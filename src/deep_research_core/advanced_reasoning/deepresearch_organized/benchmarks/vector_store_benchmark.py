"""
Performance benchmarking for vector store implementations.

This script benchmarks different vector store implementations in the codebase,
comparing their performance for various operations like adding documents,
searching, and deleting.
"""

import os
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import List, Dict, Any, Optional, Tuple
import argparse
import json
from tqdm import tqdm

from src.deep_research_core.retrieval.vector_store import get_vector_store
from src.deep_research_core.utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

class VectorStoreBenchmark:
    """
    Benchmark different vector store implementations.
    """
    
    def __init__(
        self,
        vector_store_types: List[str] = ["sqlite", "milvus"],
        embedding_dim: int = 1536,
        num_docs: List[int] = [100, 1000, 10000],
        num_queries: int = 100,
        top_k: int = 10,
        output_dir: str = "benchmark_results",
        random_seed: int = 42
    ):
        """
        Initialize the benchmark.
        
        Args:
            vector_store_types: List of vector store types to benchmark
            embedding_dim: Dimension of the embeddings
            num_docs: List of number of documents to benchmark with
            num_queries: Number of queries to run for search benchmarks
            top_k: Number of results to return in search
            output_dir: Directory to save benchmark results
            random_seed: Random seed for reproducibility
        """
        self.vector_store_types = vector_store_types
        self.embedding_dim = embedding_dim
        self.num_docs = num_docs
        self.num_queries = num_queries
        self.top_k = top_k
        self.output_dir = output_dir
        self.random_seed = random_seed
        
        # Set random seed for reproducibility
        np.random.seed(random_seed)
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Initialize results dictionary
        self.results = {
            "add": {},
            "search": {},
            "delete": {},
            "get": {},
            "count": {},
            "clear": {}
        }
        
        # Initialize vector stores
        self.vector_stores = {}
        
    def _generate_documents(self, n: int) -> Tuple[List[str], List[np.ndarray], List[Dict[str, Any]]]:
        """
        Generate random documents for benchmarking.
        
        Args:
            n: Number of documents to generate
            
        Returns:
            Tuple of (ids, embeddings, documents)
        """
        # Generate IDs
        ids = [f"doc_{i}" for i in range(n)]
        
        # Generate random embeddings
        embeddings = [np.random.rand(self.embedding_dim) for _ in range(n)]
        
        # Normalize embeddings for cosine similarity
        embeddings = [embedding / np.linalg.norm(embedding) for embedding in embeddings]
        
        # Generate documents
        documents = []
        for i in range(n):
            document = {
                "id": ids[i],
                "content": f"This is document {i} with random content for benchmarking purposes.",
                "source": f"benchmark_{i % 10}.txt",
                "metadata": {
                    "timestamp": time.time(),
                    "category": f"category_{i % 5}",
                    "priority": i % 3
                }
            }
            documents.append(document)
            
        return ids, embeddings, documents
        
    def _generate_queries(self, n: int) -> List[np.ndarray]:
        """
        Generate random query embeddings for benchmarking.
        
        Args:
            n: Number of queries to generate
            
        Returns:
            List of query embeddings
        """
        # Generate random query embeddings
        query_embeddings = [np.random.rand(self.embedding_dim) for _ in range(n)]
        
        # Normalize embeddings for cosine similarity
        query_embeddings = [embedding / np.linalg.norm(embedding) for embedding in query_embeddings]
        
        return query_embeddings
        
    def benchmark_add(self, vector_store_type: str, num_docs: int) -> float:
        """
        Benchmark adding documents to a vector store.
        
        Args:
            vector_store_type: Type of vector store to benchmark
            num_docs: Number of documents to add
            
        Returns:
            Time taken in seconds
        """
        # Initialize vector store
        vector_store = get_vector_store(
            vector_store_type=vector_store_type,
            embedding_dim=self.embedding_dim
        )
        
        # Generate documents
        ids, embeddings, documents = self._generate_documents(num_docs)
        
        # Benchmark adding documents
        start_time = time.time()
        vector_store.add(ids, embeddings, documents)
        end_time = time.time()
        
        # Calculate time taken
        time_taken = end_time - start_time
        
        # Store vector store for later benchmarks
        self.vector_stores[(vector_store_type, num_docs)] = (vector_store, ids, embeddings, documents)
        
        return time_taken
        
    def benchmark_search(self, vector_store_type: str, num_docs: int) -> float:
        """
        Benchmark searching in a vector store.
        
        Args:
            vector_store_type: Type of vector store to benchmark
            num_docs: Number of documents in the vector store
            
        Returns:
            Average time taken per query in seconds
        """
        # Get vector store
        vector_store, _, _, _ = self.vector_stores.get((vector_store_type, num_docs), (None, None, None, None))
        
        if vector_store is None:
            # If vector store doesn't exist, create it and add documents
            self.benchmark_add(vector_store_type, num_docs)
            vector_store, _, _, _ = self.vector_stores[(vector_store_type, num_docs)]
            
        # Generate query embeddings
        query_embeddings = self._generate_queries(self.num_queries)
        
        # Benchmark searching
        total_time = 0
        for query_embedding in query_embeddings:
            start_time = time.time()
            vector_store.search(query_embedding, top_k=self.top_k)
            end_time = time.time()
            total_time += (end_time - start_time)
            
        # Calculate average time taken per query
        avg_time = total_time / self.num_queries
        
        return avg_time
        
    def benchmark_hybrid_search(self, vector_store_type: str, num_docs: int) -> float:
        """
        Benchmark hybrid searching in a vector store (if supported).
        
        Args:
            vector_store_type: Type of vector store to benchmark
            num_docs: Number of documents in the vector store
            
        Returns:
            Average time taken per query in seconds, or None if not supported
        """
        # Get vector store
        vector_store, _, _, _ = self.vector_stores.get((vector_store_type, num_docs), (None, None, None, None))
        
        if vector_store is None:
            # If vector store doesn't exist, create it and add documents
            self.benchmark_add(vector_store_type, num_docs)
            vector_store, _, _, _ = self.vector_stores[(vector_store_type, num_docs)]
            
        # Check if hybrid search is supported
        if not hasattr(vector_store, "hybrid_search"):
            return None
            
        # Generate query embeddings
        query_embeddings = self._generate_queries(self.num_queries)
        
        # Generate query texts
        query_texts = [f"benchmark query {i}" for i in range(self.num_queries)]
        
        # Benchmark hybrid searching
        total_time = 0
        for i, query_embedding in enumerate(query_embeddings):
            start_time = time.time()
            vector_store.hybrid_search(
                query_embedding=query_embedding,
                query_text=query_texts[i],
                top_k=self.top_k
            )
            end_time = time.time()
            total_time += (end_time - start_time)
            
        # Calculate average time taken per query
        avg_time = total_time / self.num_queries
        
        return avg_time
        
    def benchmark_delete(self, vector_store_type: str, num_docs: int) -> float:
        """
        Benchmark deleting documents from a vector store.
        
        Args:
            vector_store_type: Type of vector store to benchmark
            num_docs: Number of documents in the vector store
            
        Returns:
            Time taken in seconds
        """
        # Get vector store
        vector_store, ids, _, _ = self.vector_stores.get((vector_store_type, num_docs), (None, None, None, None))
        
        if vector_store is None:
            # If vector store doesn't exist, create it and add documents
            self.benchmark_add(vector_store_type, num_docs)
            vector_store, ids, _, _ = self.vector_stores[(vector_store_type, num_docs)]
            
        # Select a subset of IDs to delete (10% of documents)
        num_to_delete = max(1, int(num_docs * 0.1))
        ids_to_delete = ids[:num_to_delete]
        
        # Benchmark deleting documents
        start_time = time.time()
        vector_store.delete(ids_to_delete)
        end_time = time.time()
        
        # Calculate time taken
        time_taken = end_time - start_time
        
        return time_taken
        
    def benchmark_get(self, vector_store_type: str, num_docs: int) -> float:
        """
        Benchmark getting documents from a vector store.
        
        Args:
            vector_store_type: Type of vector store to benchmark
            num_docs: Number of documents in the vector store
            
        Returns:
            Average time taken per get operation in seconds
        """
        # Get vector store
        vector_store, ids, _, _ = self.vector_stores.get((vector_store_type, num_docs), (None, None, None, None))
        
        if vector_store is None:
            # If vector store doesn't exist, create it and add documents
            self.benchmark_add(vector_store_type, num_docs)
            vector_store, ids, _, _ = self.vector_stores[(vector_store_type, num_docs)]
            
        # Select a subset of IDs to get (10% of documents)
        num_to_get = max(1, int(num_docs * 0.1))
        ids_to_get = ids[:num_to_get]
        
        # Benchmark getting documents
        start_time = time.time()
        vector_store.get_documents(ids_to_get)
        end_time = time.time()
        
        # Calculate average time taken per get operation
        avg_time = (end_time - start_time) / num_to_get
        
        return avg_time
        
    def benchmark_count(self, vector_store_type: str, num_docs: int) -> float:
        """
        Benchmark counting documents in a vector store.
        
        Args:
            vector_store_type: Type of vector store to benchmark
            num_docs: Number of documents in the vector store
            
        Returns:
            Time taken in seconds
        """
        # Get vector store
        vector_store, _, _, _ = self.vector_stores.get((vector_store_type, num_docs), (None, None, None, None))
        
        if vector_store is None:
            # If vector store doesn't exist, create it and add documents
            self.benchmark_add(vector_store_type, num_docs)
            vector_store, _, _, _ = self.vector_stores[(vector_store_type, num_docs)]
            
        # Benchmark counting documents
        start_time = time.time()
        vector_store.count()
        end_time = time.time()
        
        # Calculate time taken
        time_taken = end_time - start_time
        
        return time_taken
        
    def benchmark_clear(self, vector_store_type: str, num_docs: int) -> float:
        """
        Benchmark clearing a vector store.
        
        Args:
            vector_store_type: Type of vector store to benchmark
            num_docs: Number of documents in the vector store
            
        Returns:
            Time taken in seconds
        """
        # Get vector store
        vector_store, _, _, _ = self.vector_stores.get((vector_store_type, num_docs), (None, None, None, None))
        
        if vector_store is None:
            # If vector store doesn't exist, create it and add documents
            self.benchmark_add(vector_store_type, num_docs)
            vector_store, _, _, _ = self.vector_stores[(vector_store_type, num_docs)]
            
        # Benchmark clearing documents
        start_time = time.time()
        vector_store.clear()
        end_time = time.time()
        
        # Calculate time taken
        time_taken = end_time - start_time
        
        # Remove vector store from dictionary
        del self.vector_stores[(vector_store_type, num_docs)]
        
        return time_taken
        
    def run_benchmarks(self):
        """
        Run all benchmarks.
        """
        # Initialize results
        for operation in self.results.keys():
            for vector_store_type in self.vector_store_types:
                self.results[operation][vector_store_type] = {}
                
        # Run benchmarks for each vector store type and number of documents
        for vector_store_type in tqdm(self.vector_store_types, desc="Vector Store Types"):
            for num_docs in tqdm(self.num_docs, desc=f"Benchmarking {vector_store_type}"):
                logger.info(f"Benchmarking {vector_store_type} with {num_docs} documents")
                
                # Benchmark add
                add_time = self.benchmark_add(vector_store_type, num_docs)
                self.results["add"][vector_store_type][num_docs] = add_time
                logger.info(f"Add time: {add_time:.4f} seconds")
                
                # Benchmark search
                search_time = self.benchmark_search(vector_store_type, num_docs)
                self.results["search"][vector_store_type][num_docs] = search_time
                logger.info(f"Search time: {search_time:.4f} seconds per query")
                
                # Benchmark hybrid search (if supported)
                hybrid_search_time = self.benchmark_hybrid_search(vector_store_type, num_docs)
                if hybrid_search_time is not None:
                    if "hybrid_search" not in self.results:
                        self.results["hybrid_search"] = {}
                    if vector_store_type not in self.results["hybrid_search"]:
                        self.results["hybrid_search"][vector_store_type] = {}
                    self.results["hybrid_search"][vector_store_type][num_docs] = hybrid_search_time
                    logger.info(f"Hybrid search time: {hybrid_search_time:.4f} seconds per query")
                
                # Benchmark get
                get_time = self.benchmark_get(vector_store_type, num_docs)
                self.results["get"][vector_store_type][num_docs] = get_time
                logger.info(f"Get time: {get_time:.4f} seconds per document")
                
                # Benchmark count
                count_time = self.benchmark_count(vector_store_type, num_docs)
                self.results["count"][vector_store_type][num_docs] = count_time
                logger.info(f"Count time: {count_time:.4f} seconds")
                
                # Benchmark delete
                delete_time = self.benchmark_delete(vector_store_type, num_docs)
                self.results["delete"][vector_store_type][num_docs] = delete_time
                logger.info(f"Delete time: {delete_time:.4f} seconds")
                
                # Benchmark clear
                clear_time = self.benchmark_clear(vector_store_type, num_docs)
                self.results["clear"][vector_store_type][num_docs] = clear_time
                logger.info(f"Clear time: {clear_time:.4f} seconds")
                
        # Save results
        self.save_results()
        
    def save_results(self):
        """
        Save benchmark results to files.
        """
        # Save results as JSON
        results_file = os.path.join(self.output_dir, "benchmark_results.json")
        with open(results_file, "w") as f:
            json.dump(self.results, f, indent=2)
            
        # Create DataFrames for each operation
        dfs = {}
        for operation, results_by_type in self.results.items():
            data = []
            for vector_store_type, results_by_num in results_by_type.items():
                for num_docs, time_taken in results_by_num.items():
                    data.append({
                        "vector_store_type": vector_store_type,
                        "num_docs": num_docs,
                        "time_taken": time_taken
                    })
            dfs[operation] = pd.DataFrame(data)
            
        # Save DataFrames as CSV
        for operation, df in dfs.items():
            csv_file = os.path.join(self.output_dir, f"{operation}_benchmark.csv")
            df.to_csv(csv_file, index=False)
            
        # Create plots
        self.create_plots(dfs)
        
    def create_plots(self, dfs: Dict[str, pd.DataFrame]):
        """
        Create plots from benchmark results.
        
        Args:
            dfs: Dictionary of DataFrames with benchmark results
        """
        for operation, df in dfs.items():
            # Create a figure
            plt.figure(figsize=(10, 6))
            
            # Create a line plot
            for vector_store_type in df["vector_store_type"].unique():
                subset = df[df["vector_store_type"] == vector_store_type]
                plt.plot(subset["num_docs"], subset["time_taken"], marker="o", label=vector_store_type)
                
            # Add labels and title
            plt.xlabel("Number of Documents")
            plt.ylabel("Time (seconds)")
            plt.title(f"{operation.capitalize()} Benchmark")
            plt.legend()
            plt.grid(True)
            
            # Use logarithmic scale for x-axis if there are multiple orders of magnitude
            if len(self.num_docs) > 1 and max(self.num_docs) / min(self.num_docs) >= 10:
                plt.xscale("log")
                
            # Use logarithmic scale for y-axis if there are large differences
            if df["time_taken"].max() / df["time_taken"].min() >= 10:
                plt.yscale("log")
                
            # Save the plot
            plot_file = os.path.join(self.output_dir, f"{operation}_benchmark.png")
            plt.savefig(plot_file)
            plt.close()
            
        # Create a summary plot
        self.create_summary_plot(dfs)
        
    def create_summary_plot(self, dfs: Dict[str, pd.DataFrame]):
        """
        Create a summary plot comparing different vector store types.
        
        Args:
            dfs: Dictionary of DataFrames with benchmark results
        """
        # Get the largest number of documents
        max_num_docs = max(self.num_docs)
        
        # Create a figure
        plt.figure(figsize=(12, 8))
        
        # Create a bar plot for each operation
        operations = ["add", "search", "delete", "get", "count", "clear"]
        if "hybrid_search" in dfs:
            operations.append("hybrid_search")
            
        # Get vector store types
        vector_store_types = self.vector_store_types
        
        # Create data for the plot
        data = []
        for operation in operations:
            if operation not in dfs:
                continue
                
            df = dfs[operation]
            for vector_store_type in vector_store_types:
                subset = df[(df["vector_store_type"] == vector_store_type) & (df["num_docs"] == max_num_docs)]
                if not subset.empty:
                    data.append({
                        "operation": operation,
                        "vector_store_type": vector_store_type,
                        "time_taken": subset["time_taken"].values[0]
                    })
                    
        # Create DataFrame
        summary_df = pd.DataFrame(data)
        
        # Create a grouped bar plot
        ax = plt.subplot(111)
        
        # Get unique operations and vector store types
        unique_operations = summary_df["operation"].unique()
        unique_vector_store_types = summary_df["vector_store_type"].unique()
        
        # Set width of bars
        bar_width = 0.8 / len(unique_vector_store_types)
        
        # Set positions of bars on x-axis
        r = np.arange(len(unique_operations))
        
        # Create bars
        for i, vector_store_type in enumerate(unique_vector_store_types):
            subset = summary_df[summary_df["vector_store_type"] == vector_store_type]
            times = []
            for operation in unique_operations:
                operation_subset = subset[subset["operation"] == operation]
                if not operation_subset.empty:
                    times.append(operation_subset["time_taken"].values[0])
                else:
                    times.append(0)
            ax.bar(r + i * bar_width, times, width=bar_width, label=vector_store_type)
            
        # Add labels and title
        ax.set_xlabel("Operation")
        ax.set_ylabel("Time (seconds)")
        ax.set_title(f"Vector Store Performance Comparison ({max_num_docs} documents)")
        ax.set_xticks(r + bar_width * (len(unique_vector_store_types) - 1) / 2)
        ax.set_xticklabels(unique_operations)
        ax.legend()
        
        # Use logarithmic scale for y-axis if there are large differences
        if summary_df["time_taken"].max() / summary_df["time_taken"].min() >= 10:
            ax.set_yscale("log")
            
        # Save the plot
        plot_file = os.path.join(self.output_dir, "summary_benchmark.png")
        plt.savefig(plot_file)
        plt.close()
        
def main():
    """Run the benchmark."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Benchmark vector store implementations")
    parser.add_argument("--vector-store-types", nargs="+", default=["sqlite", "milvus"],
                        help="Vector store types to benchmark")
    parser.add_argument("--embedding-dim", type=int, default=1536,
                        help="Dimension of the embeddings")
    parser.add_argument("--num-docs", nargs="+", type=int, default=[100, 1000, 10000],
                        help="Number of documents to benchmark with")
    parser.add_argument("--num-queries", type=int, default=100,
                        help="Number of queries to run for search benchmarks")
    parser.add_argument("--top-k", type=int, default=10,
                        help="Number of results to return in search")
    parser.add_argument("--output-dir", default="benchmark_results",
                        help="Directory to save benchmark results")
    parser.add_argument("--random-seed", type=int, default=42,
                        help="Random seed for reproducibility")
    args = parser.parse_args()
    
    # Create benchmark
    benchmark = VectorStoreBenchmark(
        vector_store_types=args.vector_store_types,
        embedding_dim=args.embedding_dim,
        num_docs=args.num_docs,
        num_queries=args.num_queries,
        top_k=args.top_k,
        output_dir=args.output_dir,
        random_seed=args.random_seed
    )
    
    # Run benchmarks
    benchmark.run_benchmarks()
    
if __name__ == "__main__":
    main()
