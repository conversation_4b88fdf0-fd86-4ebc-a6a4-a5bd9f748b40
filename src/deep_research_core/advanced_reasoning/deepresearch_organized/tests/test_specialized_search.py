"""
Test for specialized search methods.
"""

import sys
import os
import requests
from typing import Dict, Any, List, Optional
import time
import json

# Import specialized search methods
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from src.deep_research_core.agents.specialized_search_methods import (
        search_google_books,
        search_open_library,
        search_openstreetmap
    )
    print("Successfully imported specialized search methods")
except ImportError as e:
    print(f"Error importing specialized search methods: {e}")
    
    # Define fallback functions
    def search_google_books(query, **kwargs):
        return {"success": False, "error": "Failed to import original function", "results": []}
    
    def search_open_library(query, **kwargs):
        return {"success": False, "error": "Failed to import original function", "results": []}
    
    def search_openstreetmap(query, **kwargs):
        return {"success": False, "error": "Failed to import original function", "results": []}

def test_book_search():
    """Test book search."""
    print("\n=== Testing Book Search ===")
    
    # Test Google Books search
    print("\n--- Google Books Search ---")
    query = "Harry Potter"
    print(f"Searching for: {query}")
    
    result = search_google_books(query, num_results=3)
    
    # Check if search was successful
    if result.get("success", False):
        print("Search successful")
        
        # Print some results
        results = result.get("results", [])
        print(f"Found {len(results)} results")
        
        for i, item in enumerate(results[:2]):
            print(f"\nResult {i+1}:")
            print(f"Title: {item.get('title', '')}")
            print(f"Authors: <AUTHORS>
            print(f"Publisher: {item.get('publisher', '')}")
            print(f"Published Date: {item.get('published_date', '')}")
    else:
        print(f"Search failed: {result.get('error', 'Unknown error')}")
    
    # Test Open Library search
    print("\n--- Open Library Search ---")
    query = "Lord of the Rings"
    print(f"Searching for: {query}")
    
    result = search_open_library(query, num_results=3)
    
    # Check if search was successful
    if result.get("success", False):
        print("Search successful")
        
        # Print some results
        results = result.get("results", [])
        print(f"Found {len(results)} results")
        
        for i, item in enumerate(results[:2]):
            print(f"\nResult {i+1}:")
            print(f"Title: {item.get('title', '')}")
            print(f"Authors: <AUTHORS>
            print(f"Publisher: {item.get('publisher', '')}")
            print(f"Published Date: {item.get('published_date', '')}")
    else:
        print(f"Search failed: {result.get('error', 'Unknown error')}")
    
    return True

def test_map_search():
    """Test map search."""
    print("\n=== Testing Map Search ===")
    
    # Test OpenStreetMap search
    print("\n--- OpenStreetMap Search ---")
    query = "Eiffel Tower Paris"
    print(f"Searching for: {query}")
    
    result = search_openstreetmap(query, num_results=3)
    
    # Check if search was successful
    if result.get("success", False):
        print("Search successful")
        
        # Print some results
        results = result.get("results", [])
        print(f"Found {len(results)} results")
        
        for i, item in enumerate(results[:2]):
            print(f"\nResult {i+1}:")
            print(f"Name: {item.get('name', '')}")
            print(f"Address: {item.get('address', '')}")
            print(f"Latitude: {item.get('latitude', '')}")
            print(f"Longitude: {item.get('longitude', '')}")
            print(f"Map URL: {item.get('map_url', '')}")
    else:
        print(f"Search failed: {result.get('error', 'Unknown error')}")
    
    return True

def run_tests():
    """Run all tests."""
    tests = [
        ("Book Search", test_book_search),
        ("Map Search", test_map_search)
    ]
    
    results = {}
    
    for name, test_func in tests:
        print(f"\n\n{'#'*100}")
        print(f"# Running Test: {name}")
        print(f"{'#'*100}")
        
        try:
            success = test_func()
            results[name] = "PASSED" if success else "FAILED"
        except Exception as e:
            print(f"Error during test: {e}")
            results[name] = "ERROR"
    
    print(f"\n\n{'#'*100}")
    print(f"# Test Results")
    print(f"{'#'*100}")
    
    for name, result in results.items():
        print(f"{name}: {result}")

if __name__ == "__main__":
    run_tests()
