#!/usr/bin/env python3
"""
Kiểm tra kết nối với SearXNG local.
"""

import requests
import sys
import time
import json
import os

# Các URL SearXNG cần kiểm tra
SEARXNG_URLS = [
    "http://localhost:8080",  # Local SearXNG
    "http://searxng:8080",    # Docker SearXNG
]

def check_searxng_connection(url):
    """Kiểm tra kết nối với SearXNG."""
    print(f"Đang kiểm tra kết nối với {url}...")
    
    try:
        # Kiểm tra trang chủ
        response = requests.get(f"{url}/", timeout=5)
        if response.status_code == 200:
            print(f"✅ Kết nối thành công với {url}")
            
            # Thử tìm kiếm đơn giản
            search_url = f"{url}/search"
            params = {
                "q": "test",
                "format": "json"
            }
            
            search_response = requests.get(search_url, params=params, timeout=10)
            
            if search_response.status_code == 200:
                try:
                    results = search_response.json()
                    result_count = len(results.get("results", []))
                    print(f"✅ Tìm kiếm thành công: Nhận được {result_count} kết quả")
                    return True
                except json.JSONDecodeError:
                    print(f"❌ Lỗi: Không thể phân tích kết quả JSON từ {url}")
            else:
                print(f"❌ Lỗi: Không thể tìm kiếm trên {url}, mã trạng thái: {search_response.status_code}")
        else:
            print(f"❌ Lỗi: Không thể kết nối với {url}, mã trạng thái: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Lỗi: Không thể kết nối với {url} - Kết nối bị từ chối")
    except requests.exceptions.Timeout:
        print(f"❌ Lỗi: Kết nối với {url} bị timeout")
    except Exception as e:
        print(f"❌ Lỗi không xác định khi kết nối với {url}: {str(e)}")
    
    return False

def main():
    """Hàm chính."""
    print("Kiểm tra kết nối với SearXNG local...\n")
    
    success = False
    for url in SEARXNG_URLS:
        if check_searxng_connection(url):
            success = True
            print(f"\nSearXNG đang hoạt động tại {url}")
            
            # Lưu URL vào biến môi trường
            os.environ["SEARXNG_URL"] = url
            print(f"Đã đặt biến môi trường SEARXNG_URL={url}")
            break
        print("\n")
    
    if not success:
        print("❌ Không thể kết nối với bất kỳ instance SearXNG nào.")
        print("Vui lòng chạy script start_searxng.sh để khởi động SearXNG local.")
        sys.exit(1)
    
    print("\nKiểm tra hoàn tất.")

if __name__ == "__main__":
    main()
