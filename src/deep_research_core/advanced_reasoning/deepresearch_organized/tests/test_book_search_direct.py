"""
Direct test for book search.
"""

import sys
import os
import requests
from typing import Dict, Any, List, Optional
import time
import json

def search_google_books(
    query: str,
    api_key: Optional[str] = None,
    num_results: int = 10,
    language: Optional[str] = None,
    filter_options: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Search for books using Google Books API (no API key required for limited usage).
    """
    try:
        # Set up API endpoint
        url = "https://www.googleapis.com/books/v1/volumes"
        
        # Prepare request parameters
        params = {
            "q": query,
            "maxResults": min(num_results, 40)  # API allows max 40 results per request
        }
        
        # Add API key if provided
        if api_key:
            params["key"] = api_key
            
        # Add language if specified
        if language:
            params["langRestrict"] = language
            
        # Add filter options if specified
        if filter_options:
            for key, value in filter_options.items():
                params[key] = value
                
        # Make the request
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        
        # Parse the response
        data = response.json()
        
        # Extract results
        results = []
        for item in data.get("items", [])[:num_results]:
            # Get volume info
            volume_info = item.get("volumeInfo", {})
            
            # Get title
            title = volume_info.get("title", "Unknown Title")
            
            # Get authors
            authors = volume_info.get("authors", ["Unknown Author"])
            
            # Get description
            description = volume_info.get("description", "")
            
            # Get publisher and publication date
            publisher = volume_info.get("publisher", "")
            published_date = volume_info.get("publishedDate", "")
            
            # Create result item
            result = {
                "title": title,
                "authors": authors,
                "description": description,
                "publisher": publisher,
                "published_date": published_date,
                "source": "google_books"
            }
            
            results.append(result)
            
        return {
            "success": True,
            "search_method": "api",
            "engine": "google_books",
            "query": query,
            "results": results,
            "search_time": 0,
            "result_count": len(results)
        }
        
    except Exception as e:
        print(f"Error searching with Google Books: {str(e)}")
        return {
            "success": False,
            "search_method": "api",
            "engine": "google_books",
            "query": query,
            "error": str(e),
            "results": []
        }

def search_open_library(
    query: str,
    num_results: int = 10,
    language: Optional[str] = None,
    filter_options: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Search for books using Open Library API (completely free, no API key required).
    """
    try:
        # Set up API endpoint
        url = "https://openlibrary.org/search.json"
        
        # Prepare request parameters
        params = {
            "q": query,
            "limit": min(num_results, 100)  # API allows max 100 results per request
        }
        
        # Add language if specified
        if language:
            params["language"] = language
            
        # Add filter options if specified
        if filter_options:
            for key, value in filter_options.items():
                params[key] = value
                
        # Make the request
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        
        # Parse the response
        data = response.json()
        
        # Extract results
        results = []
        for doc in data.get("docs", [])[:num_results]:
            # Get title
            title = doc.get("title", "Unknown Title")
            
            # Get authors
            author_names = doc.get("author_name", ["Unknown Author"])
            
            # Get publisher and publication date
            publishers = doc.get("publisher", [])
            publisher = publishers[0] if publishers else ""
            publish_year = doc.get("first_publish_year", "")
            
            # Create result item
            result = {
                "title": title,
                "authors": author_names,
                "publisher": publisher,
                "publish_year": publish_year,
                "source": "open_library"
            }
            
            results.append(result)
            
        return {
            "success": True,
            "search_method": "api",
            "engine": "open_library",
            "query": query,
            "results": results,
            "search_time": 0,
            "result_count": len(results)
        }
        
    except Exception as e:
        print(f"Error searching with Open Library: {str(e)}")
        return {
            "success": False,
            "search_method": "api",
            "engine": "open_library",
            "query": query,
            "error": str(e),
            "results": []
        }

def search_openstreetmap(
    query: str,
    num_results: int = 10,
    country_codes: Optional[List[str]] = None,
    bounding_box: Optional[List[float]] = None
) -> Dict[str, Any]:
    """
    Search for locations using OpenStreetMap Nominatim API (free, no API key required).
    """
    try:
        # Set up API endpoint
        url = "https://nominatim.openstreetmap.org/search"
        
        # Prepare request parameters
        params = {
            "q": query,
            "format": "json",
            "limit": min(num_results, 50),  # API allows max 50 results per request
            "addressdetails": 1
        }
        
        # Add country codes if specified
        if country_codes:
            params["countrycodes"] = ",".join(country_codes)
        
        # Add bounding box if specified
        if bounding_box and len(bounding_box) == 4:
            params["viewbox"] = ",".join(map(str, bounding_box))
            params["bounded"] = 1
        
        # Set up headers (required by Nominatim usage policy)
        headers = {
            "User-Agent": "DeepResearchCore/1.0",
            "Accept-Language": "en-US,en;q=0.9"
        }
        
        # Make the request
        response = requests.get(url, params=params, headers=headers, timeout=10)
        response.raise_for_status()
        
        # Parse the response
        data = response.json()
        
        # Extract results
        results = []
        for item in data:
            # Format address
            address = item.get("address", {})
            formatted_address = []
            
            # Add road and house number if available
            if address.get("road"):
                house_number = address.get("house_number", "")
                road = address.get("road", "")
                if house_number and road:
                    formatted_address.append(f"{house_number} {road}")
                elif road:
                    formatted_address.append(road)
            
            # Add city/town/village
            for key in ["city", "town", "village", "suburb", "neighbourhood"]:
                if address.get(key):
                    formatted_address.append(address.get(key))
                    break
            
            # Add state and country
            if address.get("state"):
                formatted_address.append(address.get("state"))
            if address.get("country"):
                formatted_address.append(address.get("country"))
            
            # Join address parts
            address_str = ", ".join(formatted_address)
            
            # Create result item
            result = {
                "name": item.get("display_name", ""),
                "address": address_str,
                "latitude": float(item.get("lat", 0)),
                "longitude": float(item.get("lon", 0)),
                "source": "openstreetmap"
            }
            
            results.append(result)
        
        return {
            "success": True,
            "search_method": "api",
            "engine": "openstreetmap",
            "query": query,
            "results": results,
            "search_time": 0,
            "result_count": len(results)
        }
        
    except Exception as e:
        print(f"Error searching with OpenStreetMap: {str(e)}")
        return {
            "success": False,
            "search_method": "api",
            "engine": "openstreetmap",
            "query": query,
            "error": str(e),
            "results": []
        }

def test_book_search():
    """Test book search."""
    print("\n=== Testing Book Search ===")
    
    # Test Google Books search
    print("\n--- Google Books Search ---")
    query = "Harry Potter"
    print(f"Searching for: {query}")
    
    result = search_google_books(query, num_results=3)
    
    # Check if search was successful
    if result.get("success", False):
        print("Search successful")
        
        # Print some results
        results = result.get("results", [])
        print(f"Found {len(results)} results")
        
        for i, item in enumerate(results[:2]):
            print(f"\nResult {i+1}:")
            print(f"Title: {item.get('title', '')}")
            print(f"Authors: <AUTHORS>
            print(f"Publisher: {item.get('publisher', '')}")
            print(f"Published Date: {item.get('published_date', '')}")
    else:
        print(f"Search failed: {result.get('error', 'Unknown error')}")
    
    # Test Open Library search
    print("\n--- Open Library Search ---")
    query = "Lord of the Rings"
    print(f"Searching for: {query}")
    
    result = search_open_library(query, num_results=3)
    
    # Check if search was successful
    if result.get("success", False):
        print("Search successful")
        
        # Print some results
        results = result.get("results", [])
        print(f"Found {len(results)} results")
        
        for i, item in enumerate(results[:2]):
            print(f"\nResult {i+1}:")
            print(f"Title: {item.get('title', '')}")
            print(f"Authors: <AUTHORS>
            print(f"Publisher: {item.get('publisher', '')}")
            print(f"Published Year: {item.get('publish_year', '')}")
    else:
        print(f"Search failed: {result.get('error', 'Unknown error')}")
    
    return True

def test_map_search():
    """Test map search."""
    print("\n=== Testing Map Search ===")
    
    # Test OpenStreetMap search
    print("\n--- OpenStreetMap Search ---")
    query = "Eiffel Tower Paris"
    print(f"Searching for: {query}")
    
    result = search_openstreetmap(query, num_results=3)
    
    # Check if search was successful
    if result.get("success", False):
        print("Search successful")
        
        # Print some results
        results = result.get("results", [])
        print(f"Found {len(results)} results")
        
        for i, item in enumerate(results[:2]):
            print(f"\nResult {i+1}:")
            print(f"Name: {item.get('name', '')}")
            print(f"Address: {item.get('address', '')}")
            print(f"Latitude: {item.get('latitude', '')}")
            print(f"Longitude: {item.get('longitude', '')}")
    else:
        print(f"Search failed: {result.get('error', 'Unknown error')}")
    
    return True

def run_tests():
    """Run all tests."""
    tests = [
        ("Book Search", test_book_search),
        ("Map Search", test_map_search)
    ]
    
    results = {}
    
    for name, test_func in tests:
        print(f"\n\n{'#'*100}")
        print(f"# Running Test: {name}")
        print(f"{'#'*100}")
        
        try:
            success = test_func()
            results[name] = "PASSED" if success else "FAILED"
        except Exception as e:
            print(f"Error during test: {e}")
            results[name] = "ERROR"
    
    print(f"\n\n{'#'*100}")
    print(f"# Test Results")
    print(f"{'#'*100}")
    
    for name, result in results.items():
        print(f"{name}: {result}")

if __name__ == "__main__":
    run_tests()
