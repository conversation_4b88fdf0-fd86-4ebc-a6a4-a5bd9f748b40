#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Kiểm thử ImprovedWebSearchAgent.

Module này kiểm tra xem ImprovedWebSearchAgent có sửa được các lỗi
'int' object is not subscriptable và các vấn đề khởi tạo thuộc tính hay không.
"""

import sys
import os
import time
import unittest
import logging

# Thêm thư mục gốc vào đường dẫn Python
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

# Import các module cần thiết
from src.deep_research_core.agents.web_search_agent import WebSearchAgent
from src.deep_research_core.agents.web_search_agent_improved import ImprovedWebSearchAgent

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TestImprovedWebSearchAgent(unittest.TestCase):
    """Ki<PERSON>m thử ImprovedWebSearchAgent."""

    def test_original_agent_error(self):
        """Kiểm tra lỗi 'int' object is not subscriptable trong WebSearchAgent gốc."""
        print("\n=== KIỂM TRA LỖI TRONG WEBSEARCHAGENT GỐC ===")

        # Khởi tạo WebSearchAgent
        config = {"api_search_config": {"engine": "duckduckgo"}}
        agent = WebSearchAgent(config)

        # Ghi đè thuộc tính engine_request_counts bằng một số nguyên
        agent.engine_request_counts = 0

        # Thực hiện tìm kiếm - sẽ gây ra lỗi khi cố gắng truy cập engine_request_counts như một từ điển
        try:
            with self.assertRaises(Exception) as context:
                agent.search("test query", num_results=2)

            # Kiểm tra xem có phải lỗi 'int' object is not subscriptable không
            self.assertTrue("'int' object is not subscriptable" in str(context.exception) or
                            "'int' object has no attribute" in str(context.exception))

            print(f"Lỗi đã xảy ra (dự kiến): {str(context.exception)}")
        except AssertionError:
            # Nếu không gây ra lỗi, có thể WebSearchAgent đã được cải tiến
            print("WebSearchAgent không gây ra lỗi như mong đợi. Có thể đã được cải tiến.")
            # Kiểm tra xem engine_request_counts có được khởi tạo lại không
            self.assertTrue(isinstance(agent.engine_request_counts, dict))

    def test_improved_agent_engine_request_counts(self):
        """Kiểm tra ImprovedWebSearchAgent đã sửa lỗi engine_request_counts."""
        print("\n=== KIỂM TRA IMPROVEDWEBSEARCHAGENT VỚI ENGINE_REQUEST_COUNTS ===")

        # Khởi tạo ImprovedWebSearchAgent
        config = {"api_search_config": {"engine": "duckduckgo"}}
        agent = ImprovedWebSearchAgent(config)

        # Ghi đè thuộc tính engine_request_counts bằng một số nguyên
        agent.engine_request_counts = 0

        # Thực hiện tìm kiếm - ImprovedWebSearchAgent sẽ khởi tạo lại thuộc tính
        try:
            result = agent.search("test query", _test_only=True)
            self.assertTrue(isinstance(agent.engine_request_counts, dict))
            print("Kiểm tra thành công: engine_request_counts đã được khởi tạo lại thành dict")
            print(f"Kết quả: {result}")
        except Exception as e:
            self.fail(f"ImprovedWebSearchAgent không sửa được lỗi: {str(e)}")

    def test_improved_agent_cache(self):
        """Kiểm tra ImprovedWebSearchAgent đã sửa lỗi cache."""
        print("\n=== KIỂM TRA IMPROVEDWEBSEARCHAGENT VỚI CACHE ===")

        # Khởi tạo ImprovedWebSearchAgent
        config = {"api_search_config": {"engine": "duckduckgo"}}
        agent = ImprovedWebSearchAgent(config)

        # Ghi đè thuộc tính cache bằng một số nguyên
        agent.cache = 0

        # Thực hiện tìm kiếm - ImprovedWebSearchAgent sẽ khởi tạo lại thuộc tính
        try:
            result = agent.search("test query", _test_only=True)
            self.assertTrue(hasattr(agent.cache, 'get'))
            self.assertTrue(hasattr(agent.cache, 'set'))
            print("Kiểm tra thành công: cache đã được khởi tạo lại thành đối tượng WebSearchCache")
            print(f"Kết quả: {result}")
        except Exception as e:
            self.fail(f"ImprovedWebSearchAgent không sửa được lỗi: {str(e)}")

    def test_improved_agent_user_agents(self):
        """Kiểm tra ImprovedWebSearchAgent đã sửa lỗi user_agents."""
        print("\n=== KIỂM TRA IMPROVEDWEBSEARCHAGENT VỚI USER_AGENTS ===")

        # Khởi tạo ImprovedWebSearchAgent
        config = {"api_search_config": {"engine": "duckduckgo"}}
        agent = ImprovedWebSearchAgent(config)

        # Ghi đè thuộc tính user_agents bằng một số nguyên
        agent.user_agents = 0

        # Ghi đè thuộc tính current_user_agent_index bằng một chuỗi
        agent.current_user_agent_index = "invalid"

        # Lấy user agent - ImprovedWebSearchAgent sẽ khởi tạo lại thuộc tính
        try:
            user_agent = agent._get_rotated_user_agent()
            self.assertTrue(isinstance(agent.user_agents, list))
            self.assertTrue(isinstance(agent.current_user_agent_index, int))
            print("Kiểm tra thành công: user_agents và current_user_agent_index đã được khởi tạo lại")
            print(f"User agent: {user_agent}")
        except Exception as e:
            self.fail(f"ImprovedWebSearchAgent không sửa được lỗi: {str(e)}")

    def test_improved_agent_rate_limit(self):
        """Kiểm tra ImprovedWebSearchAgent đã sửa lỗi rate limit."""
        print("\n=== KIỂM TRA IMPROVEDWEBSEARCHAGENT VỚI RATE LIMIT ===")

        # Khởi tạo ImprovedWebSearchAgent
        config = {"api_search_config": {"engine": "duckduckgo"}}
        agent = ImprovedWebSearchAgent(config)

        # Xóa các thuộc tính rate limit nếu chúng tồn tại
        if hasattr(agent, 'request_count'):
            delattr(agent, 'request_count')
        if hasattr(agent, 'request_count_reset_time'):
            delattr(agent, 'request_count_reset_time')
        if hasattr(agent, 'rate_limit'):
            delattr(agent, 'rate_limit')

        # Kiểm tra rate limit - ImprovedWebSearchAgent sẽ khởi tạo lại thuộc tính
        try:
            result = agent._check_rate_limit()
            self.assertTrue(hasattr(agent, 'request_count'))
            self.assertTrue(hasattr(agent, 'request_count_reset_time'))
            self.assertTrue(hasattr(agent, 'rate_limit'))
            print("Kiểm tra thành công: thuộc tính rate limit đã được khởi tạo lại")
            print(f"Kết quả: {result}")
        except Exception as e:
            self.fail(f"ImprovedWebSearchAgent không sửa được lỗi: {str(e)}")

def main():
    """Hàm chính để chạy kiểm thử."""
    unittest.main()

if __name__ == "__main__":
    main()
