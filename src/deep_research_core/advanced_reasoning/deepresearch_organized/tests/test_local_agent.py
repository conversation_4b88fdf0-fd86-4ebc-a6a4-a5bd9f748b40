#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for WebSearchAgentLocal.
"""

import sys
import os
import time
import logging
from typing import Dict, Any

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add parent directory to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import WebSearchAgentLocal
try:
    from deepresearch.web_search_agent_local import WebSearchAgentLocal, WebSearchAgentMerged
    
    def test_basic_search():
        """Test basic search functionality."""
        print("\n=== Testing Basic Search ===")
        agent = WebSearchAgentLocal(verbose=True)
        print(f"Created agent: {agent.name} v{agent.version}")
        
        # Test a simple search
        print("\nTrying a simple search...")
        results = agent.search("Python programming", num_results=3)
        
        # Print results
        print(f"Search success: {results.get('success', False)}")
        print(f"Results count: {len(results.get('results', []))}")
        
        # Print first result
        if results.get('results'):
            first_result = results['results'][0]
            print(f"First result:")
            print(f"  - Title: {first_result.get('title', 'N/A')}")
            print(f"  - URL: {first_result.get('url', 'N/A')}")
            print(f"  - Snippet: {first_result.get('snippet', 'N/A')[:100]}...")
        
        return results.get('success', False)
    
    def test_input_validation():
        """Test input validation."""
        print("\n=== Testing Input Validation ===")
        agent = WebSearchAgentLocal()
        
        # Test empty query
        print("\nTesting empty query...")
        try:
            results = agent.search("")
            print("❌ Empty query test failed: No exception raised")
            return False
        except ValueError as e:
            print(f"✅ Empty query test passed: {str(e)}")
        
        # Test invalid num_results
        print("\nTesting invalid num_results...")
        try:
            results = agent.search("Python", num_results=-1)
            print("❌ Invalid num_results test failed: No exception raised")
            return False
        except ValueError as e:
            print(f"✅ Invalid num_results test passed: {str(e)}")
        
        return True
    
    def test_caching():
        """Test caching functionality."""
        print("\n=== Testing Caching ===")
        agent = WebSearchAgentLocal(verbose=True)
        
        # First search (should generate results)
        print("\nFirst search (should generate results)...")
        start_time = time.time()
        results1 = agent.search("Python programming language", num_results=2)
        first_search_time = time.time() - start_time
        print(f"First search took {first_search_time:.2f} seconds")
        
        # Second search (should hit the cache)
        print("\nSecond search (should hit the cache)...")
        start_time = time.time()
        results2 = agent.search("Python programming language", num_results=2)
        second_search_time = time.time() - start_time
        print(f"Second search took {second_search_time:.2f} seconds")
        
        # Check if second search was faster
        if second_search_time < first_search_time:
            print("✅ Caching test passed: Second search was faster")
            return True
        else:
            print("❌ Caching test failed: Second search was not faster")
            return False
    
    def test_content_retrieval():
        """Test content retrieval functionality."""
        print("\n=== Testing Content Retrieval ===")
        agent = WebSearchAgentLocal(verbose=True)
        
        # Search without content
        print("\nSearching without content...")
        results_without_content = agent.search("Python programming", num_results=1)
        has_content_without_flag = "content" in results_without_content.get('results', [{}])[0]
        print(f"Has content without flag: {has_content_without_flag}")
        
        # Search with content
        print("\nSearching with content...")
        results_with_content = agent.search("Python programming", num_results=1, get_content=True)
        has_content_with_flag = "content" in results_with_content.get('results', [{}])[0]
        print(f"Has content with flag: {has_content_with_flag}")
        
        # Check content
        if has_content_with_flag:
            content = results_with_content.get('results', [{}])[0].get('content', '')
            print(f"Content length: {len(content)} characters")
            print(f"Content preview: {content[:100]}...")
        
        # Check if content was added correctly
        if not has_content_without_flag and has_content_with_flag:
            print("✅ Content retrieval test passed")
            return True
        else:
            print("❌ Content retrieval test failed")
            return False
    
    def test_different_queries():
        """Test different queries."""
        print("\n=== Testing Different Queries ===")
        agent = WebSearchAgentLocal(verbose=True)
        
        queries = ["Python", "Java", "JavaScript", "Machine Learning"]
        
        for query in queries:
            print(f"\nSearching for: {query}")
            results = agent.search(query, num_results=2)
            print(f"Results count: {len(results.get('results', []))}")
            
            # Print first result title
            if results.get('results'):
                first_result = results['results'][0]
                print(f"First result title: {first_result.get('title', 'N/A')}")
        
        return True
    
    def test_merged_alias():
        """Test WebSearchAgentMerged alias."""
        print("\n=== Testing WebSearchAgentMerged Alias ===")
        
        # Create agent using the alias
        agent = WebSearchAgentMerged(verbose=True)
        print(f"Created agent using alias: {agent.name} v{agent.version}")
        
        # Test a simple search
        print("\nTrying a simple search with the alias...")
        results = agent.search("Python programming", num_results=1)
        
        # Check if search was successful
        if results.get('success', False):
            print("✅ WebSearchAgentMerged alias test passed")
            return True
        else:
            print("❌ WebSearchAgentMerged alias test failed")
            return False
    
    def run_all_tests():
        """Run all tests."""
        print("\n=== Running All Tests ===")
        
        tests = [
            ("Basic Search", test_basic_search),
            ("Input Validation", test_input_validation),
            ("Caching", test_caching),
            ("Content Retrieval", test_content_retrieval),
            ("Different Queries", test_different_queries),
            ("WebSearchAgentMerged Alias", test_merged_alias),
        ]
        
        results = {}
        for name, test_func in tests:
            print(f"\nRunning test: {name}")
            try:
                result = test_func()
                results[name] = result
                print(f"Test {name}: {'✅ PASSED' if result else '❌ FAILED'}")
            except Exception as e:
                results[name] = False
                print(f"Test {name}: ❌ ERROR - {str(e)}")
        
        # Print summary
        print("\n=== Test Summary ===")
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        print(f"Passed: {passed}/{total} ({passed/total*100:.1f}%)")
        
        for name, result in results.items():
            print(f"{name}: {'✅ PASSED' if result else '❌ FAILED'}")
        
        return passed == total

except ImportError as e:
    print(f"Import error: {str(e)}")
except Exception as e:
    print(f"Error: {str(e)}")

if __name__ == "__main__":
    run_all_tests()
