"""
Test script for the MultiSourceValidator module.
"""

import os
import sys
import json
from typing import Dict, List, Any

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import module directly
from src.deep_research_core.reasoning.multi_source_validator import MultiSourceValidator

# Sample data
SAMPLE_SOURCES = [
    {
        "id": "source1",
        "title": "AI ở Việt Nam",
        "content": "Trí tuệ nhân tạo (AI) đang phát triển nhanh chóng ở Việt Nam. Các công ty công nghệ lớn như VinAI, FPT AI và Zalo AI đang dẫn đầu trong nghiên cứu và phát triển AI.",
        "author": "Nguyễn Văn A",
        "publication_date": "2023-01-01",
        "url": "https://example.com/ai-vietnam",
        "publisher": "Tech Magazine"
    },
    {
        "id": "source2",
        "title": "VinAI: <PERSON><PERSON>ng ty AI hàng đầu Việt Nam",
        "content": "VinAI được thành lập vào năm 2018 và là một phần của Tập đoàn Vingroup. Họ tập trung vào nghiên cứu AI cơ bản và ứng dụng.",
        "author": "Trần Thị B",
        "publication_date": "2023-02-15",
        "url": "https://example.org/vinai",
        "publisher": "Tech News"
    },
    {
        "id": "source3",
        "title": "FPT AI và các ứng dụng",
        "content": "FPT AI là bộ phận AI của Tập đoàn FPT, một trong những công ty công nghệ lớn nhất Việt Nam. Họ đã phát triển nhiều sản phẩm AI như chatbot, hệ thống nhận dạng khuôn mặt và xử lý ngôn ngữ tự nhiên.",
        "author": "Lê Văn C",
        "publication_date": "2023-03-10",
        "url": "https://example.com/fpt-ai",
        "publisher": "Tech Review"
    }
]

def test_multi_source_validator():
    """Test the MultiSourceValidator module."""
    print("\n=== Testing MultiSourceValidator ===")
    
    try:
        # Initialize MultiSourceValidator
        multi_source_validator = MultiSourceValidator(
            language="vi",
            min_sources=2
        )
        
        # Test information to validate
        information = "VinAI được thành lập vào năm 2018 và là một phần của Tập đoàn Vingroup. Họ tập trung vào nghiên cứu AI cơ bản và ứng dụng."
        
        # Validate information
        validation_result = multi_source_validator.validate(
            information=information,
            sources=SAMPLE_SOURCES,
            context={"topic": "AI ở Việt Nam"}
        )
        
        # Print validation result
        print("Validation result:")
        print(json.dumps(validation_result, indent=2, ensure_ascii=False))
        
        # Test contradicting information
        information_items = [
            "VinAI được thành lập vào năm 2018.",
            "VinAI được thành lập vào năm 2019.",
            "VinAI là một phần của Tập đoàn Vingroup."
        ]
        
        # Analyze contradictions
        contradiction_result = multi_source_validator.analyze_contradictions(
            information_items=information_items,
            sources=SAMPLE_SOURCES
        )
        
        # Print contradiction result
        print("\nContradiction analysis:")
        print(json.dumps(contradiction_result, indent=2, ensure_ascii=False))
        
        # Get most reliable information
        reliability_result = multi_source_validator.get_most_reliable_information(
            information_items=information_items,
            sources=SAMPLE_SOURCES,
            context={"topic": "AI ở Việt Nam"}
        )
        
        # Print reliability result
        print("\nMost reliable information:")
        print(json.dumps(reliability_result, indent=2, ensure_ascii=False))
    
    except Exception as e:
        print(f"Error in MultiSourceValidator test: {str(e)}")

if __name__ == "__main__":
    test_multi_source_validator()
