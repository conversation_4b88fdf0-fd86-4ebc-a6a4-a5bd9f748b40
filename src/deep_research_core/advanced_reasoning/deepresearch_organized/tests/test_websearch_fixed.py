#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for WebSearchAgent with fixes for cache-related issues.
"""

import sys
import time
from src.deep_research_core.agents.web_search_agent import WebSearchAgent
from cachetools import TTLCache
import logging

# Thiết lập logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FixedWebSearchAgent(WebSearchAgent):
    """
    Fixed version of WebSearchAgent that initializes all missing attributes.
    """
    
    def __init__(self, *args, **kwargs):
        """Initialize with proper attribute setup."""
        # Call parent init
        super().__init__(*args, **kwargs)
        
        # Ensure cache attributes are always initialized
        if not hasattr(self, 'cache'):
            self.cache = {}
        if not hasattr(self, 'cache_timestamps'):
            self.cache_timestamps = {}
            
        # Ensure rate limit attributes are always initialized
        current_time = time.time()
        if not hasattr(self, 'request_count'):
            self.request_count = 0
        if not hasattr(self, 'request_count_reset_time'):
            self.request_count_reset_time = current_time
            
        # Engine-specific request counters
        if not hasattr(self, 'engine_request_counts'):
            self.engine_request_counts = {}
        if not hasattr(self, 'engine_reset_times'):
            self.engine_reset_times = {}
            
        # Initialize engine rate limits (requests per minute)
        if not hasattr(self, 'engine_rate_limits'):
            self.engine_rate_limits = {
                'duckduckgo': 5,   # DuckDuckGo has stricter limits
                'searx': 10,       # SearX can handle more requests
                'qwant': 8,        # Qwant has moderate limits
                'default': self.rate_limit  # Default from initialization
            }
            
        # Available search engines list
        if not hasattr(self, 'available_engines'):
            self.available_engines = [
                'duckduckgo',
                'searx',
                'qwant',
                'google_scholar',
                'semantic_scholar',
                'openalex',
                'crossref',
                'arxiv',
                'pubmed',
                'google_books',
                'open_library',
                'openstreetmap'
            ]
            
        # Captcha handler related attributes
        if not hasattr(self, 'last_captcha_detection'):
            self.last_captcha_detection = {}

def print_section(title):
    """Print a section header."""
    print("\n" + "=" * 50)
    print(f" {title}")
    print("=" * 50)

def print_result(result, max_results=3):
    """Print search results in a readable format."""
    print(f"Tìm kiếm thành công: {result.get('success', False)}")
    print(f"Công cụ tìm kiếm: {result.get('engine', 'không xác định')}")
    print(f"Phương thức tìm kiếm: {result.get('search_method', 'không xác định')}")
    
    results = result.get("results", [])
    print(f"Số lượng kết quả: {len(results)}")
    
    if not results:
        print("Không có kết quả.")
        return
    
    for i, item in enumerate(results[:max_results]):
        print(f"\nKết quả {i+1}:")
        print(f"Tiêu đề: {item.get('title', '')}")
        print(f"URL: {item.get('url', '')}")
        snippet = item.get('snippet', '')
        if snippet and len(snippet) > 100:
            snippet = snippet[:100] + "..."
        print(f"Trích đoạn: {snippet}")
        print(f"Nguồn: {item.get('source', 'không xác định')}")

def main():
    """Test WebSearchAgent with fixed cache."""
    print_section("Kiểm tra WebSearchAgent (với các sửa lỗi)")
    
    # Khởi tạo FixedWebSearchAgent
    try:
        agent = FixedWebSearchAgent(
            search_method="api",
            api_search_config={"engine": "duckduckgo"},
            use_smart_cache=True,
            cache_ttl=3600,
            verbose=True
        )
        
        # Thực hiện tìm kiếm
        query = "Thủ đô của Việt Nam"
        print(f"Truy vấn: {query}")
        
        result = agent.search(query, num_results=3)
        print_result(result)
        
    except Exception as e:
        print(f"Lỗi: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 