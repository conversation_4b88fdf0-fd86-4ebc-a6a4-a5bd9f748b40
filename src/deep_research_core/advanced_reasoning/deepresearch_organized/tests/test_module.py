"""
Simple test script for testing individual modules.
"""

import sys
import os
import argparse
from typing import List, Dict, Any, Optional

# Add the src directory to the path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "src")))

# Import the modules
from deep_research_core.models.api.openrouter import OpenRouterProvider
from deep_research_core.utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

def test_openrouter():
    """Test the OpenRouter provider."""
    print("Testing OpenRouter provider...")
    
    # Initialize the provider with the API key
    api_key = "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"
    provider = OpenRouterProvider(api_key=api_key)
    
    # Test get_available_models
    try:
        models = provider.get_available_models()
        print(f"Available models: {len(models)}")
        for model_id, model_info in list(models.items())[:3]:  # Show first 3 models
            print(f"  - {model_id}")
        print("OpenRouter provider test passed!")
    except Exception as e:
        print(f"Error testing OpenRouter provider: {str(e)}")
        return False
    
    return True

def test_source_attribution():
    """Test the SourceAttribution module."""
    print("Testing SourceAttribution module...")
    
    try:
        from deep_research_core.reasoning.source_attribution import SourceAttribution
        
        # Initialize with mock data
        attribution = SourceAttribution(
            provider="openrouter",
            api_key="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"
        )
        
        print("SourceAttribution initialized successfully!")
        
        # Test the attribution
        documents = [
            {"id": "doc1", "content": "The capital of France is Paris."},
            {"id": "doc2", "content": "The Eiffel Tower is in Paris."},
            {"id": "doc3", "content": "Paris is known as the City of Light."}
        ]
        
        query = "What is the capital of France and what is it known for?"
        
        print(f"Testing attribution with query: {query}")
        print(f"Documents: {len(documents)}")
        
        # Mock the _generate method to avoid API calls
        attribution._generate = lambda system_prompt, user_prompt: '{"attributions": [{"text": "The capital of France is Paris.", "source_id": "doc1", "confidence": 0.95}, {"text": "Paris is known as the City of Light.", "source_id": "doc3", "confidence": 0.9}]}'
        
        result = attribution.attribute(query, documents)
        
        print(f"Attribution result: {result}")
        print("SourceAttribution test passed!")
        
        return True
    except Exception as e:
        print(f"Error testing SourceAttribution: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_self_reflection():
    """Test the SelfReflection module."""
    print("Testing SelfReflection module...")
    
    try:
        from deep_research_core.reasoning.self_reflection import SelfReflection
        
        # Initialize with mock data
        reflection = SelfReflection(
            provider="openrouter",
            api_key="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"
        )
        
        print("SelfReflection initialized successfully!")
        
        # Test the reflection
        reasoning = "The capital of France is Paris. Paris is located in Western Europe. France is a member of the European Union."
        
        print(f"Testing reflection with reasoning: {reasoning}")
        
        # The _generate method is already mocked in the SelfReflection class
        
        result = reflection.reflect(reasoning)
        
        print(f"Reflection result: {result}")
        print("SelfReflection test passed!")
        
        return True
    except Exception as e:
        print(f"Error testing SelfReflection: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_query_decomposition():
    """Test the MultiQueryDecomposition module."""
    print("Testing MultiQueryDecomposition module...")
    
    try:
        from deep_research_core.reasoning.multi_query_decomposition import MultiQueryDecomposition
        
        # Initialize with mock data
        decomposer = MultiQueryDecomposition(
            provider="openrouter",
            api_key="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"
        )
        
        print("MultiQueryDecomposition initialized successfully!")
        
        # Test the decomposition
        query = "What are the economic and environmental impacts of renewable energy adoption in developing countries?"
        
        print(f"Testing decomposition with query: {query}")
        
        # Mock the _generate method to avoid API calls
        decomposer._generate = lambda system_prompt, user_prompt: '{"sub_queries": ["What are the economic impacts of renewable energy adoption in developing countries?", "What are the environmental impacts of renewable energy adoption in developing countries?", "What are the challenges of renewable energy adoption in developing countries?"], "reasoning": "The original query asks about both economic and environmental impacts, so I\'ve split it into separate queries for each aspect."}'
        
        result = decomposer.decompose(query)
        
        print(f"Decomposition result: {result}")
        print("MultiQueryDecomposition test passed!")
        
        return True
    except Exception as e:
        print(f"Error testing MultiQueryDecomposition: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_source_validator():
    """Test the MultiSourceValidator module."""
    print("Testing MultiSourceValidator module...")
    
    try:
        from deep_research_core.reasoning.multi_source_validator import MultiSourceValidator
        
        # Initialize with mock data
        validator = MultiSourceValidator(
            provider="openrouter",
            api_key="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"
        )
        
        print("MultiSourceValidator initialized successfully!")
        
        # Test the validation
        sources = [
            {"id": "source1", "content": "The Earth is approximately 4.54 billion years old."},
            {"id": "source2", "content": "The Earth is about 4.5 billion years old."},
            {"id": "source3", "content": "The Earth is 6,000 years old."}
        ]
        
        claim = "The Earth is approximately 4.5 billion years old."
        
        print(f"Testing validation with claim: {claim}")
        print(f"Sources: {len(sources)}")
        
        # Mock the _generate method to avoid API calls
        validator._generate = lambda system_prompt, user_prompt: '{"validation_result": "consistent", "confidence": 0.9, "supporting_sources": ["source1", "source2"], "contradicting_sources": ["source3"], "reasoning": "Two credible sources support the claim that the Earth is approximately 4.5 billion years old, while one source contradicts it with a much younger age."}'
        
        result = validator.validate(claim, sources)
        
        print(f"Validation result: {result}")
        print("MultiSourceValidator test passed!")
        
        return True
    except Exception as e:
        print(f"Error testing MultiSourceValidator: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_tree_of_thought():
    """Test the TreeOfThought module."""
    print("Testing TreeOfThought module...")
    
    try:
        from deep_research_core.reasoning.tot import TreeOfThought
        
        # Initialize with mock data
        tot = TreeOfThought(
            provider="openrouter",
            api_key="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"
        )
        
        print("TreeOfThought initialized successfully!")
        
        # Test the reasoning
        problem = "What is the next number in the sequence: 2, 4, 8, 16, ...?"
        
        print(f"Testing reasoning with problem: {problem}")
        
        # Mock the _generate method to avoid API calls
        tot._generate_thoughts = lambda prompt, n: ["The pattern seems to be multiplying by 2 each time.", "Each number is double the previous number.", "The sequence follows the pattern 2^n where n starts at 1."]
        tot._evaluate_thoughts = lambda thoughts, prompt: [0.9, 0.8, 0.95]
        tot._generate_solution = lambda best_thought, prompt: "The next number in the sequence is 32, because each number is multiplied by 2 to get the next number."
        
        result = tot.solve(problem)
        
        print(f"Reasoning result: {result}")
        print("TreeOfThought test passed!")
        
        return True
    except Exception as e:
        print(f"Error testing TreeOfThought: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Test individual modules")
    parser.add_argument("--module", type=str, help="Module to test", 
                        choices=["openrouter", "source_attribution", "self_reflection", 
                                "multi_query_decomposition", "multi_source_validator", 
                                "tree_of_thought", "all"])
    args = parser.parse_args()
    
    if args.module == "openrouter" or args.module == "all":
        test_openrouter()
        
    if args.module == "source_attribution" or args.module == "all":
        test_source_attribution()
        
    if args.module == "self_reflection" or args.module == "all":
        test_self_reflection()
        
    if args.module == "multi_query_decomposition" or args.module == "all":
        test_multi_query_decomposition()
        
    if args.module == "multi_source_validator" or args.module == "all":
        test_multi_source_validator()
        
    if args.module == "tree_of_thought" or args.module == "all":
        test_tree_of_thought()

if __name__ == "__main__":
    main()
