#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for _deep_crawl_improved method in WebSearchAgentLocal without using <PERSON><PERSON>.
"""

import sys
import os
import logging
import time
import requests
from bs4 import BeautifulSoup
from typing import Dict, List, Any, Optional

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

def print_section(title):
    """Print a section title."""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, "="))
    print("=" * 80)

def crawl_with_requests(url, timeout=30):
    """Simple crawling function using requests and BeautifulSoup."""
    try:
        # Set user agent to avoid being blocked
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        
        # Make the request
        response = requests.get(url, headers=headers, timeout=timeout)
        
        if response.status_code != 200:
            return {
                "success": False,
                "error": f"HTTP error: {response.status_code}"
            }
        
        # Parse HTML with BeautifulSoup
        soup = BeautifulSoup(response.text, "html.parser")
        
        # Extract title
        title = soup.title.string if soup.title else url
        
        # Extract content
        content = ""
        for tag in soup.find_all(["p", "h1", "h2", "h3", "h4", "h5", "h6", "li"]):
            if tag.text:
                content += tag.text.strip() + "\n\n"
        
        # Extract links
        links = []
        for a_tag in soup.find_all("a", href=True):
            href = a_tag["href"]
            text = a_tag.text.strip()
            link_info = {
                "url": href,
                "text": text,
                "title": a_tag.get("title", ""),
                "source_url": url
            }
            links.append(link_info)
        
        # Extract metadata
        metadata = {}
        for meta in soup.find_all("meta"):
            if meta.get("name") and meta.get("content"):
                metadata[meta["name"]] = meta["content"]
            elif meta.get("property") and meta.get("content"):
                metadata[meta["property"]] = meta["content"]
        
        return {
            "success": True,
            "url": url,
            "title": title,
            "text": content,
            "links": links,
            "metadata": metadata,
            "html": response.text
        }
    except Exception as e:
        logger.error(f"Error crawling {url}: {str(e)}")
        return {
            "success": False,
            "url": url,
            "error": f"Crawling error: {str(e)}"
        }

def extract_pdf_info(url, timeout=30):
    """Extract basic information about a PDF file."""
    try:
        # Set user agent to avoid being blocked
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        
        # Make a HEAD request to get content type and size
        head_response = requests.head(url, headers=headers, timeout=timeout)
        
        if head_response.status_code != 200:
            return {
                "success": False,
                "error": f"HTTP error: {head_response.status_code}"
            }
        
        # Extract file name from URL
        file_name = url.split("/")[-1]
        
        # Get content type and length
        content_type = head_response.headers.get("Content-Type", "")
        content_length = head_response.headers.get("Content-Length", "")
        
        return {
            "success": True,
            "url": url,
            "title": file_name,
            "text": f"[PDF file: {file_name}]",
            "metadata": {
                "content_type": content_type,
                "content_length": content_length,
                "file_name": file_name
            },
            "links": []
        }
    except Exception as e:
        logger.error(f"Error extracting PDF info from {url}: {str(e)}")
        return {
            "success": False,
            "url": url,
            "error": f"PDF extraction error: {str(e)}"
        }

def test_deep_crawl_basic():
    """Test basic deep crawl functionality."""
    print_section("Testing basic deep crawl functionality")

    # Test URLs
    urls = [
        "https://www.python.org/",
        "https://thuvienphapluat.vn/van-ban/Thuong-mai/Nghi-dinh-98-2020-ND-CP-xu-phat-vi-pham-hanh-chinh-hoat-dong-thuong-mai-bao-ve-nguoi-tieu-dung-450755.pdf",
        "https://thuvienphapluat.vn/page/tim-van-ban.aspx"
    ]

    for url in urls:
        print(f"\nTesting URL: {url}")
        
        try:
            # Determine if URL is a PDF
            is_pdf = url.lower().endswith(".pdf")
            
            # Call appropriate function
            start_time = time.time()
            if is_pdf:
                result = extract_pdf_info(url)
            else:
                result = crawl_with_requests(url)
            end_time = time.time()
            
            # Print results
            print(f"Success: {result.get('success', False)}")
            print(f"Time: {end_time - start_time:.2f} seconds")
            
            if result.get('success', False):
                print(f"Title: {result.get('title', '')}")
                content = result.get('text', '')
                print(f"Content: {content[:100]}..." if content and len(content) > 100 else f"Content: {content}")
                
                # Print metadata
                metadata = result.get('metadata', {})
                print(f"Metadata: {metadata}")
                
                # Print links
                links = result.get('links', [])
                print(f"Number of links: {len(links)}")
                if links and len(links) > 0:
                    print(f"First link: {links[0]}")
            else:
                print(f"Error: {result.get('error', 'Unknown error')}")
        except Exception as e:
            print(f"Error: {str(e)}")

if __name__ == "__main__":
    test_deep_crawl_basic()
