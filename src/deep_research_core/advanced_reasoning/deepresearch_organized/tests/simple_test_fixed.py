"""
Simple test script for WebSearchAgentMerged.
"""

import sys
import os

# Thêm thư mục gốc vào đường dẫn Python
sys.path.append(os.path.abspath('.'))

# Import WebSearchAgentMerged from test_standalone
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
from test_standalone import WebSearchAgentMerged

# Kiểm tra các module cần thiết
print("Kiểm tra WebSearchAgentMerged...")
try:
    # Tạo agent
    agent = WebSearchAgentMerged()
    print("✓ WebSearchAgentMerged đã được khởi tạo thành công")

    # Thử tìm kiếm đơn giản
    print("\nThử tìm kiếm đơn giản...")
    results = agent.search("Python programming", num_results=2)

    # In kết quả
    print(f"Tìm kiếm thành công: {results.get('success', False)}")
    print(f"Số lượng kết quả: {len(results.get('results', []))}")

    # In kết quả đầu tiên
    if results.get('results'):
        first_result = results['results'][0]
        print(f"Kết quả đầu tiên:")
        print(f"  - Tiêu đề: {first_result.get('title', 'N/A')}")
        print(f"  - URL: {first_result.get('url', 'N/A')}")
        print(f"  - Đoạn trích: {first_result.get('snippet', 'N/A')[:100]}...")

except ImportError as e:
    print(f"✗ Không thể import module web_search_agent_merged: {e}")
except Exception as e:
    print(f"✗ Lỗi: {e}")

print("\nKiểm tra hoàn tất!")
