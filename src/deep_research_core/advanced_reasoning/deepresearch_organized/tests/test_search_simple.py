#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time

def search_searx(query, num_results=5, language="en"):
    """Search SearX and return results."""
    print(f"Searching for: {query}")
    
    # SearX URL
    searx_url = "http://localhost:8080"
    
    # Format API URL
    api_url = f"{searx_url}/search"
    
    # Set up parameters
    params = {
        "q": query,
        "format": "json",
        "language": language,
        "categories": "general",
        "pageno": 1,
        "engines": "google,bing,duckduckgo,wikipedia",
        "results": num_results
    }
    
    # Set up headers
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
        "Accept": "application/json",
        "X-Requested-With": "XMLHttpRequest"
    }
    
    try:
        # Make request
        print(f"Making request to {api_url}")
        response = requests.get(api_url, params=params, headers=headers, timeout=10)
        response.raise_for_status()
        
        # Parse JSON response
        data = response.json()
        
        # Print results
        print(f"Found {len(data.get('results', []))} results")
        
        for i, result in enumerate(data.get("results", [])[:num_results]):
            print(f"\n[{i+1}] {result.get('title', '')}")
            print(f"    URL: {result.get('url', '')}")
            snippet = result.get("content", "")
            if len(snippet) > 100:
                snippet = snippet[:100] + "..."
            print(f"    Snippet: {snippet}")
        
        return data
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return {"error": str(e)}

# Test with different queries
print("=== TEST 1: Python Programming ===")
search_searx("Python programming language")

time.sleep(2)  # Avoid rate limiting

print("\n=== TEST 2: Artificial Intelligence ===")
search_searx("artificial intelligence and machine learning")

time.sleep(2)  # Avoid rate limiting

print("\n=== TEST 3: Vietnam ===")
search_searx("Vietnam tourism and culture")

time.sleep(2)  # Avoid rate limiting

print("\n=== TEST 4: Vietnamese Query ===")
search_searx("lập trình python cơ bản", language="vi")

time.sleep(2)  # Avoid rate limiting

print("\n=== TEST 5: Complex Query ===")
search_searx("how does quantum computing differ from classical computing")

print("\n=== TEST COMPLETED ===")
