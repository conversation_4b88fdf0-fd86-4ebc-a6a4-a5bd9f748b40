"""
Test the TreeOfThought class with an empty query.
"""

from src.deep_research_core.reasoning.tot import TreeOfThought

def main():
    """Test the TreeOfThought class with an empty query."""
    tot = TreeOfThought(
        provider="openrouter",
        model="moonshotai/moonlight-16b-a3b-instruct:free",
        temperature=0.7,
        max_tokens=1000
    )
    
    result = tot.reason("")
    print("Result:", result)
    print("Error key exists:", "error" in result)
    print("Error value:", result.get("error"))

if __name__ == "__main__":
    main()
