#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script để kiểm tra tất cả các tính năng đã tích hợp vào WebSearchAgentLocal.
"""

import os
import sys
import time
import logging
from typing import Dict, Any, List

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

# Import WebSearchAgentLocal
from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal

def print_section(title):
    """In tiêu đề phần."""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, "="))
    print("=" * 80 + "\n")

def print_result(result):
    """In kết quả tìm kiếm."""
    if not result.get("success", False):
        print(f"Lỗi: {result.get('error', 'Unknown error')}")
        return

    print(f"Tìm thấy {len(result.get('results', []))} kết quả")
    
    for i, item in enumerate(result.get("results", []), 1):
        print(f"\n--- Kết quả {i} ---")
        print(f"Tiêu đề: {item.get('title', 'No title')}")
        print(f"URL: {item.get('url', 'No URL')}")
        
        if "snippet" in item:
            snippet = item["snippet"]
            if len(snippet) > 150:
                snippet = snippet[:150] + "..."
            print(f"Snippet: {snippet}")
        
        if "content" in item and item["content"]:
            content = item["content"]
            if len(content) > 150:
                content = content[:150] + "..."
            print(f"Nội dung: {content}")

def test_vietnamese_nlp():
    """Kiểm tra tích hợp VietnameseNLP."""
    print_section("Kiểm tra tích hợp VietnameseNLP")
    
    # Khởi tạo WebSearchAgentLocal với VietnameseNLP
    agent = WebSearchAgentLocal(
        enable_nlp=True,
        verbose=True
    )
    
    # Kiểm tra xem VietnameseNLP đã được tích hợp chưa
    vietnamese_nlp_integrated = hasattr(agent, "vietnamese_nlp_integrated") and agent.vietnamese_nlp_integrated
    logger.info(f"VietnameseNLP integrated: {vietnamese_nlp_integrated}")
    
    if vietnamese_nlp_integrated:
        # Kiểm tra các phương thức VietnameseNLP
        test_text = "Việt Nam là một quốc gia tuyệt vời với nhiều cảnh đẹp và ẩm thực phong phú."
        
        # Kiểm tra phát hiện tiếng Việt
        is_vietnamese = agent.detect_vietnamese(test_text)
        logger.info(f"Detect Vietnamese: {is_vietnamese}")
        
        # Kiểm tra chuẩn hóa tiếng Việt
        normalized = agent.normalize_vietnamese(test_text)
        logger.info(f"Normalized: {normalized}")
        
        # Kiểm tra tách từ tiếng Việt
        tokens = agent.tokenize_vietnamese(test_text)
        logger.info(f"Tokens: {tokens[:5]}...")
        
        # Kiểm tra tối ưu hóa truy vấn tiếng Việt
        query = "ẩm thực Việt Nam"
        optimized_query = agent.optimize_vietnamese_query(query)
        logger.info(f"Original query: {query}")
        logger.info(f"Optimized query: {optimized_query}")
        
        # Thực hiện tìm kiếm với truy vấn tiếng Việt
        results = agent._search_searxng(
            query=query,
            num_results=3,
            language="vi",
            get_content=True
        )
        
        # Cải thiện kết quả tiếng Việt
        enhanced_results = agent.enhance_vietnamese_results(results, query)
        
        # Hiển thị kết quả
        print_result(enhanced_results)
    else:
        logger.warning("VietnameseNLP không được tích hợp")

def test_file_processor():
    """Kiểm tra tích hợp FileProcessor."""
    print_section("Kiểm tra tích hợp FileProcessor")
    
    # Khởi tạo WebSearchAgentLocal với FileProcessor
    agent = WebSearchAgentLocal(
        enable_file_download=True,
        file_download_config={
            "download_dir": os.path.join(os.path.dirname(__file__), "downloads"),
            "max_file_size": 10 * 1024 * 1024,  # 10MB
            "allowed_extensions": [".pdf", ".docx", ".txt", ".html", ".htm"],
            "verify_ssl": True,
            "timeout": 30.0,
            "max_retries": 3,
            "retry_delay": 1.0
        },
        verbose=True
    )
    
    # Kiểm tra xem FileProcessor đã được tích hợp chưa
    file_processor_integrated = hasattr(agent, "file_processor_integrated") and agent.file_processor_integrated
    logger.info(f"FileProcessor integrated: {file_processor_integrated}")
    
    if file_processor_integrated:
        # Kiểm tra tải xuống file
        url = "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"
        
        # Tải xuống file
        download_result = agent.download_file(url)
        logger.info(f"Download result: {download_result.get('success')}")
        
        if download_result.get("success"):
            logger.info(f"Downloaded file: {download_result.get('file_path')}")
            
            # Xử lý file
            process_result = agent.process_file(
                url=url,
                extract_text=True,
                extract_metadata=True
            )
            
            if process_result.get("success"):
                logger.info(f"File processed successfully")
                logger.info(f"Text content length: {len(process_result.get('text', ''))}")
                logger.info(f"Metadata: {process_result.get('metadata', {})}")
            else:
                logger.error(f"File processing failed: {process_result.get('error')}")
        else:
            logger.error(f"File download failed: {download_result.get('error')}")
    else:
        logger.warning("FileProcessor không được tích hợp")

def test_multimedia_search():
    """Kiểm tra tích hợp MultimediaSearch."""
    print_section("Kiểm tra tích hợp MultimediaSearch")
    
    # Khởi tạo WebSearchAgentLocal với MultimediaSearch
    agent = WebSearchAgentLocal(
        enable_multimedia_search=True,
        verbose=True
    )
    
    # Kiểm tra xem MultimediaSearch đã được tích hợp chưa
    multimedia_search_integrated = hasattr(agent, "multimedia_search_integrated") and agent.multimedia_search_integrated
    logger.info(f"MultimediaSearch integrated: {multimedia_search_integrated}")
    
    if multimedia_search_integrated:
        # Kiểm tra tìm kiếm hình ảnh
        query = "Python programming"
        
        # Tìm kiếm hình ảnh
        image_results = agent.search_images(query, num_results=3)
        logger.info(f"Image search success: {image_results.get('success')}")
        
        if image_results.get("success"):
            logger.info(f"Found {len(image_results.get('results', []))} images")
            
            for i, result in enumerate(image_results.get("results", []), 1):
                logger.info(f"Image {i}: {result.get('title')} - {result.get('url')}")
        
        # Tìm kiếm video
        video_results = agent.search_videos(query, num_results=3)
        logger.info(f"Video search success: {video_results.get('success')}")
        
        if video_results.get("success"):
            logger.info(f"Found {len(video_results.get('results', []))} videos")
            
            for i, result in enumerate(video_results.get("results", []), 1):
                logger.info(f"Video {i}: {result.get('title')} - {result.get('url')}")
        
        # Tìm kiếm đa phương tiện
        multimedia_results = agent.search_multimedia(query, num_results=3, media_type="all")
        logger.info(f"Multimedia search success: {multimedia_results.get('success')}")
        
        if multimedia_results.get("success"):
            logger.info(f"Found {len(multimedia_results.get('results', []))} multimedia items")
            
            for i, result in enumerate(multimedia_results.get("results", []), 1):
                logger.info(f"Multimedia {i}: {result.get('title')} - {result.get('url')} - {result.get('media_type')}")
    else:
        logger.warning("MultimediaSearch không được tích hợp")

def test_all_integrations():
    """Kiểm tra tất cả các tích hợp."""
    print_section("Kiểm tra tất cả các tích hợp")
    
    # Khởi tạo WebSearchAgentLocal với tất cả các tính năng
    agent = WebSearchAgentLocal(
        enable_nlp=True,
        enable_multimedia_search=True,
        enable_file_download=True,
        enable_vietnamese_search=True,
        enable_performance_optimization=True,
        verbose=True
    )
    
    # Kiểm tra các tính năng đã được tích hợp
    logger.info(f"NLP enabled: {hasattr(agent, 'use_nlp') and agent.use_nlp}")
    logger.info(f"Vietnamese NLP integrated: {hasattr(agent, 'vietnamese_nlp_integrated') and agent.vietnamese_nlp_integrated}")
    logger.info(f"Multimedia Search integrated: {hasattr(agent, 'multimedia_search_integrated') and agent.multimedia_search_integrated}")
    logger.info(f"File Processor integrated: {hasattr(agent, 'file_processor_integrated') and agent.file_processor_integrated}")
    logger.info(f"Performance Optimization enabled: {hasattr(agent, 'optimize_performance') and agent.optimize_performance}")
    
    # Thực hiện tìm kiếm với tất cả các tính năng
    query = "Python programming"
    results = agent._search_searxng(
        query=query,
        num_results=3,
        get_content=True
    )
    
    # Hiển thị kết quả
    print_result(results)

if __name__ == "__main__":
    # Chạy các test
    test_vietnamese_nlp()
    test_file_processor()
    test_multimedia_search()
    test_all_integrations()
