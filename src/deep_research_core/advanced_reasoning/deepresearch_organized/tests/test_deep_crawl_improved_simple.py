#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for _deep_crawl_improved method in WebSearchAgentLocal.
"""

import sys
import os
import logging
import time
from typing import Dict, List, Any, Optional

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import WebSearchAgentLocal
try:
    from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal
except ImportError:
    try:
        from deepresearch.src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal
    except ImportError:
        try:
            from deepresearch.web_search_agent_local import WebSearchAgentLocal
        except ImportError:
            try:
                from web_search_agent_local import WebSearchAgentLocal
            except ImportError as e:
                logger.error(f"Could not import WebSearchAgentLocal: {e}")
                logger.error(f"Current sys.path: {sys.path}")
                logger.error("Ensure 'deepresearch' (parent of 'src') is in PYTHONPATH or sys.path is set correctly, and all parent directories have __init__.py if necessary.")
                sys.exit(1)

def print_section(title):
    """Print a section title."""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, "="))
    print("=" * 80)

def test_deep_crawl_improved():
    """Test _deep_crawl_improved method."""
    print_section("Testing _deep_crawl_improved method")

    # Initialize WebSearchAgentLocal
    agent = WebSearchAgentLocal(verbose=True)

    # Test URLs
    urls = [
        "https://www.python.org/",
        "https://thuvienphapluat.vn/van-ban/Thuong-mai/Nghi-dinh-98-2020-ND-CP-xu-phat-vi-pham-hanh-chinh-hoat-dong-thuong-mai-bao-ve-nguoi-tieu-dung-450755.pdf",
        "https://thuvienphapluat.vn/page/tim-van-ban.aspx"
    ]

    for url in urls:
        print(f"\nTesting URL: {url}")
        
        try:
            # Call _deep_crawl_improved method
            start_time = time.time()
            result = agent._deep_crawl_improved(
                url=url,
                max_depth=1,
                max_pages=3,
                timeout=30,
                include_html=False,
                extract_files=True,
                handle_javascript=True if ".aspx" in url.lower() else False,
                use_async=True
            )
            end_time = time.time()
            
            # Print results
            print(f"Success: {result.get('success', False)}")
            if result.get('success', False):
                print(f"Title: {result.get('title', '')}")
                content = result.get('text', '')
                print(f"Content: {content[:100]}..." if content and len(content) > 100 else f"Content: {content}")
                
                # Print metadata
                metadata = result.get('metadata', {})
                print(f"Metadata: {metadata}")
                
                # Print links
                links = result.get('links', [])
                print(f"Number of links: {len(links)}")
                if links and len(links) > 0:
                    print(f"First link: {links[0]}")
        except Exception as e:
            print(f"Error: {str(e)}")

if __name__ == "__main__":
    test_deep_crawl_improved()
