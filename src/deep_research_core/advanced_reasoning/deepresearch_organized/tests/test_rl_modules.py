"""
Test script for RL modules (PPO, SFT, GRPO).

This script tests the basic functionality of the RL modules.
"""

import os
import sys
import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

# Set API keys for testing
os.environ["OPENROUTER_API_KEY"] = "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"

def test_ppo_providers():
    """Test PPO provider initialization."""
    from src.deep_research_core.rl_tuning.providers.ppo_providers import get_ppo_provider

    # Define a simple reward function
    def simple_reward_fn(prompt, response):
        return 0.5

    # Test different providers
    providers = [
        ("openrouter", "anthropic/claude-3-opus"),
        ("openai", "gpt-3.5-turbo"),
        ("anthropic", "claude-3-opus"),
        ("deepseek", "deepseek-chat"),
        ("qwq", "qwq-32b"),
        ("gemini", "gemini-pro"),
        ("cohere", "command"),
        ("mistral", "mistral-large")
    ]

    results = {}

    for provider_type, model_name in providers:
        try:
            logger.info(f"Testing PPO provider: {provider_type} with model {model_name}")
            ppo = get_ppo_provider(
                model_name=model_name,
                provider_type=provider_type,
                reward_fn=simple_reward_fn
            )
            results[provider_type] = "SUCCESS"
        except Exception as e:
            logger.error(f"Error initializing {provider_type} PPO: {str(e)}")
            results[provider_type] = f"FAILED: {str(e)}"

    return results

def test_sft_providers():
    """Test SFT provider initialization."""
    from src.deep_research_core.rl_tuning.providers.sft_providers import get_sft_provider

    # Test different providers
    providers = [
        ("openrouter", "anthropic/claude-3-opus"),
        ("openai", "gpt-3.5-turbo"),
        ("deepseek", "deepseek-chat"),
        ("qwq", "qwq-32b"),
        ("huggingface", "mistralai/Mistral-7B-v0.1")
    ]

    results = {}

    for provider_type, model_name in providers:
        try:
            logger.info(f"Testing SFT provider: {provider_type} with model {model_name}")
            sft = get_sft_provider(
                model_name=model_name,
                provider_type=provider_type
            )
            results[provider_type] = "SUCCESS"
        except Exception as e:
            logger.error(f"Error initializing {provider_type} SFT: {str(e)}")
            results[provider_type] = f"FAILED: {str(e)}"

    return results

def test_grpo_providers():
    """Test GRPO provider initialization."""
    from src.deep_research_core.rl_tuning.providers.grpo_providers import get_grpo_provider

    # Test different providers
    providers = [
        ("openrouter", "anthropic/claude-3-opus"),
        ("deepseek", "deepseek-chat"),
        ("qwq", "qwq-32b"),
        ("huggingface", "mistralai/Mistral-7B-v0.1")
    ]

    results = {}

    for provider_type, model_name in providers:
        try:
            logger.info(f"Testing GRPO provider: {provider_type} with model {model_name}")
            grpo = get_grpo_provider(
                model_name=model_name,
                provider_type=provider_type
            )
            results[provider_type] = "SUCCESS"
        except Exception as e:
            logger.error(f"Error initializing {provider_type} GRPO: {str(e)}")
            results[provider_type] = f"FAILED: {str(e)}"

    return results

def test_reward_functions():
    """Test reward functions."""
    from src.deep_research_core.rl_tuning.grpo.base import RewardFunction, FormatRewardFunction
    from src.deep_research_core.rl_tuning.grpo.outcome_rewards import AccuracyReward, ReasoningQualityReward

    results = {}

    # Test format reward function
    try:
        logger.info("Testing FormatRewardFunction")
        format_reward = FormatRewardFunction(
            format_pattern="JSON",
            regex_patterns=[r"\{.*\}", r"^\s*\{"]
        )

        outputs = [
            '{"name": "John", "age": 30}',
            'The answer is 42',
            '{ "result": true }'
        ]

        rewards = format_reward.compute_reward(outputs)
        results["FormatRewardFunction"] = f"SUCCESS: {rewards}"
    except Exception as e:
        logger.error(f"Error testing FormatRewardFunction: {str(e)}")
        results["FormatRewardFunction"] = f"FAILED: {str(e)}"

    # Test accuracy reward function
    try:
        logger.info("Testing AccuracyReward")
        accuracy_reward = AccuracyReward()

        outputs = ["The capital of France is Paris", "The capital of Germany is Berlin"]
        references = ["Paris is the capital of France", "Berlin is the capital of Germany"]

        rewards = accuracy_reward.compute_reward(outputs, references)
        results["AccuracyReward"] = f"SUCCESS: {rewards}"
    except Exception as e:
        logger.error(f"Error testing AccuracyReward: {str(e)}")
        results["AccuracyReward"] = f"FAILED: {str(e)}"

    # Test reasoning quality reward function
    try:
        logger.info("Testing ReasoningQualityReward")
        reasoning_reward = ReasoningQualityReward()

        outputs = [
            "First, we need to understand the problem. Second, we analyze the options. Finally, we can conclude that option A is correct because it satisfies all conditions.",
            "The answer is 42."
        ]

        rewards = reasoning_reward.compute_reward(outputs)
        results["ReasoningQualityReward"] = f"SUCCESS: {rewards}"
    except Exception as e:
        logger.error(f"Error testing ReasoningQualityReward: {str(e)}")
        results["ReasoningQualityReward"] = f"FAILED: {str(e)}"

    return results

def run_all_tests():
    """Run all tests and print results."""
    logger.info("=== Testing RL Modules ===")

    # Test PPO providers
    logger.info("\n=== Testing PPO Providers ===")
    ppo_results = test_ppo_providers()

    # Test SFT providers
    logger.info("\n=== Testing SFT Providers ===")
    sft_results = test_sft_providers()

    # Test GRPO providers
    logger.info("\n=== Testing GRPO Providers ===")
    grpo_results = test_grpo_providers()

    # Test reward functions
    logger.info("\n=== Testing Reward Functions ===")
    reward_results = test_reward_functions()

    # Print summary
    logger.info("\n=== Test Summary ===")

    logger.info("PPO Providers:")
    for provider, result in ppo_results.items():
        logger.info(f"  {provider}: {result}")

    logger.info("\nSFT Providers:")
    for provider, result in sft_results.items():
        logger.info(f"  {provider}: {result}")

    logger.info("\nGRPO Providers:")
    for provider, result in grpo_results.items():
        logger.info(f"  {provider}: {result}")

    logger.info("\nReward Functions:")
    for func, result in reward_results.items():
        logger.info(f"  {func}: {result}")

if __name__ == "__main__":
    run_all_tests()
