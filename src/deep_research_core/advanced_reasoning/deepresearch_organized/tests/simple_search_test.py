#!/usr/bin/env python3
"""
Test script đơn giản để kiểm tra đầu ra của SearchResultOptimizer.
"""

import json
import time
from typing import Dict, Any, List, Optional, Union

class SearchResultOptimizer:
    """
    <PERSON><PERSON><PERSON> tối ưu hóa kết quả tìm kiếm.
    """
    
    def __init__(
        self,
        relevance_threshold: float = 0.3,
        quality_threshold: float = 0.2,
        max_content_length: int = 5000,
        min_content_length: int = 100,
        max_results: int = 5,
        min_results: int = 2,
        ideal_total_length: int = 8000,
        vietnamese_support: bool = False
    ):
        """
        Khởi tạo SearchResultOptimizer.
        """
        self.relevance_threshold = relevance_threshold
        self.quality_threshold = quality_threshold
        self.max_content_length = max_content_length
        self.min_content_length = min_content_length
        self.max_results = max_results
        self.min_results = min_results
        self.ideal_total_length = ideal_total_length
        self.vietnamese_support = vietnamese_support
        
        # <PERSON>h s<PERSON>ch từ khóa theo lĩnh vực
        self.domain_keywords = {
            "technology": ["programming", "software", "hardware", "computer", "algorithm", "code", "developer", "application", "system", "technology"],
            "health": ["health", "medical", "disease", "treatment", "symptom", "doctor", "patient", "hospital", "medicine", "diagnosis"],
            "finance": ["finance", "money", "investment", "stock", "market", "bank", "economy", "financial", "business", "trading"],
            "education": ["education", "school", "student", "teacher", "learning", "university", "college", "course", "academic", "study"],
            "science": ["science", "scientific", "research", "experiment", "theory", "physics", "chemistry", "biology", "mathematics", "scientist"]
        }
    
    def optimize(self, search_results: Dict[str, Any], query: str) -> Dict[str, Any]:
        """
        Tối ưu hóa kết quả tìm kiếm.
        """
        print(f"Optimizing search results for query: {query}")
        
        # Kiểm tra kết quả tìm kiếm
        if not search_results.get("success") or not search_results.get("results"):
            print("No search results to optimize")
            return search_results
        
        # Lấy danh sách kết quả
        results = search_results.get("results", [])
        
        # Xác định lĩnh vực của truy vấn
        domain = self._detect_domain(query)
        print(f"Detected domain for query: {domain}")
        
        # Lọc kết quả không liên quan
        filtered_results = []
        for result in results:
            # Bỏ qua các kết quả không liên quan
            if not self._is_relevant(result, query, domain):
                continue
            
            # Tính điểm liên quan
            relevance_score = self._calculate_relevance_score(result, query, domain)
            result["relevance_score"] = relevance_score
            
            # Chỉ giữ lại kết quả có điểm liên quan cao hơn ngưỡng
            if relevance_score >= self.relevance_threshold:
                filtered_results.append(result)
        
        # Sắp xếp kết quả theo điểm liên quan
        filtered_results.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)
        
        # Giới hạn độ dài nội dung
        total_content_length = 0
        for result in filtered_results:
            content = result.get("content", "")
            
            # Bỏ qua nội dung quá ngắn
            if len(content) < self.min_content_length:
                result["content_quality"] = "too_short"
                continue
                
            # Cắt nội dung quá dài
            if len(content) > self.max_content_length:
                result["content"] = content[:self.max_content_length] + "..."
                result["content_quality"] = "truncated"
            else:
                result["content_quality"] = "good"
                
            total_content_length += len(result.get("content", ""))
        
        # Đánh giá chất lượng tổng thể
        quality_metrics = self._evaluate_quality(filtered_results, query, total_content_length)
        
        # Giới hạn số lượng kết quả
        if len(filtered_results) > self.max_results:
            filtered_results = filtered_results[:self.max_results]
        
        # Tạo kết quả tối ưu
        optimized_results = {
            "success": search_results.get("success", True),
            "query": query,
            "domain": domain,
            "results": filtered_results,
            "count": len(filtered_results),
            "total_content_length": total_content_length,
            "quality_metrics": quality_metrics,
            "optimized": True,
            "timestamp": time.time()
        }
        
        print(f"Optimization completed: {len(results)} -> {len(filtered_results)} results")
        
        return optimized_results
    
    def _detect_domain(self, query: str) -> str:
        """
        Xác định lĩnh vực của truy vấn.
        """
        query_lower = query.lower()
        domain_scores = {}
        
        # Tính điểm cho mỗi lĩnh vực
        for domain, keywords in self.domain_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword in query_lower:
                    score += 1
            domain_scores[domain] = score
        
        # Chọn lĩnh vực có điểm cao nhất
        if max(domain_scores.values()) > 0:
            return max(domain_scores.items(), key=lambda x: x[1])[0]
        
        # Nếu không có lĩnh vực nào phù hợp, trả về "general"
        return "general"
    
    def _is_relevant(self, result: Dict[str, Any], query: str, domain: str) -> bool:
        """
        Kiểm tra xem kết quả có liên quan đến truy vấn không.
        """
        query_lower = query.lower()
        title = result.get("title", "").lower()
        content = result.get("content", result.get("snippet", "")).lower()
        
        # Kiểm tra các trường hợp đặc biệt
        if "programming" in query_lower and "snake" in content and "python" in query_lower:
            return False
        
        # Kiểm tra từ khóa lĩnh vực
        if domain != "general":
            domain_keywords = self.domain_keywords.get(domain, [])
            if not any(keyword in title or keyword in content for keyword in domain_keywords):
                return False
        
        # Kiểm tra từ khóa truy vấn
        query_terms = query_lower.split()
        if not any(term in title or term in content for term in query_terms):
            return False
        
        return True
    
    def _calculate_relevance_score(self, result: Dict[str, Any], query: str, domain: str) -> float:
        """
        Tính điểm liên quan của kết quả.
        """
        query_lower = query.lower()
        title = result.get("title", "").lower()
        content = result.get("content", result.get("snippet", "")).lower()
        
        # Điểm cơ bản
        score = 0.0
        
        # Điểm cho tiêu đề
        query_terms = query_lower.split()
        title_score = sum(1 for term in query_terms if term in title) / max(1, len(query_terms))
        score += title_score * 0.5
        
        # Điểm cho nội dung
        content_score = sum(1 for term in query_terms if term in content) / max(1, len(query_terms))
        score += content_score * 0.3
        
        # Điểm cho lĩnh vực
        if domain != "general":
            domain_keywords = self.domain_keywords.get(domain, [])
            domain_score = sum(1 for keyword in domain_keywords if keyword in title or keyword in content) / max(1, len(domain_keywords))
            score += domain_score * 0.2
        
        return min(1.0, score)
    
    def _evaluate_quality(self, results: List[Dict[str, Any]], query: str, total_content_length: int) -> Dict[str, Any]:
        """
        Đánh giá chất lượng tổng thể của kết quả.
        """
        # Số lượng kết quả
        count = len(results)
        count_quality = "good"
        if count < self.min_results:
            count_quality = "too_few"
        elif count > self.max_results:
            count_quality = "too_many"
        
        # Độ dài nội dung
        length_quality = "good"
        if total_content_length < self.ideal_total_length * 0.5:
            length_quality = "too_short"
        elif total_content_length > self.ideal_total_length * 1.5:
            length_quality = "too_long"
        
        # Độ đa dạng
        domains = set()
        for result in results:
            url = result.get("url", "")
            if url:
                try:
                    domain = url.split("//")[1].split("/")[0]
                    domains.add(domain)
                except:
                    pass
        
        diversity_quality = "good"
        if len(domains) < 2 and count > 2:
            diversity_quality = "low"
        
        # Điểm liên quan trung bình
        avg_relevance = sum(result.get("relevance_score", 0) for result in results) / max(1, count)
        relevance_quality = "good"
        if avg_relevance < self.relevance_threshold:
            relevance_quality = "low"
        
        # Tổng hợp đánh giá
        overall_quality = "good"
        if count_quality != "good" or length_quality != "good" or diversity_quality != "good" or relevance_quality != "good":
            overall_quality = "needs_improvement"
        
        return {
            "count": {
                "value": count,
                "quality": count_quality
            },
            "content_length": {
                "value": total_content_length,
                "quality": length_quality
            },
            "diversity": {
                "value": len(domains),
                "quality": diversity_quality
            },
            "relevance": {
                "value": avg_relevance,
                "quality": relevance_quality
            },
            "overall": overall_quality
        }

def main():
    # Tạo dữ liệu mẫu
    sample_results = {
        "success": True,
        "query": "Python programming",
        "results": [
            {
                "title": "Python Programming Language",
                "url": "https://www.python.org/",
                "snippet": "Python is a programming language that lets you work quickly.",
                "content": "Python is a programming language that lets you work quickly and integrate systems more effectively. Python is dynamically typed and garbage-collected. It supports multiple programming paradigms, including structured (particularly, procedural), object-oriented, and functional programming. It is often described as a 'batteries included' language due to its comprehensive standard library."
            },
            {
                "title": "Learn Python - Free Interactive Python Tutorial",
                "url": "https://www.learnpython.org/",
                "snippet": "Learn Python, a powerful programming language.",
                "content": "Learn Python, a powerful programming language used for many different applications. Python is a widely used high-level programming language for general-purpose programming, created by Guido van Rossum and first released in 1991. Python features a dynamic type system and automatic memory management and supports multiple programming paradigms."
            },
            {
                "title": "Python Snake Facts",
                "url": "https://www.snakefacts.com/python/",
                "snippet": "Learn about python snakes, their habitat, diet, and behavior.",
                "content": "Python snakes are some of the largest snakes in the world. They are constrictors, which means they coil around their prey and squeeze until the prey can no longer breathe. Pythons are found in Africa, Asia, and Australia."
            }
        ],
        "timestamp": time.time()
    }
    
    # Tạo optimizer
    optimizer = SearchResultOptimizer(
        relevance_threshold=0.3,
        quality_threshold=0.2,
        max_content_length=1000,
        max_results=5
    )
    
    # Tối ưu hóa kết quả
    optimized = optimizer.optimize(sample_results, "Python programming")
    
    # In kết quả
    print("\nOptimized results:")
    print(json.dumps(optimized, indent=2))
    
    # Lưu kết quả vào file
    with open("optimized_results.json", "w", encoding="utf-8") as f:
        json.dump(optimized, f, ensure_ascii=False, indent=2)
    
    print("\nResults saved to optimized_results.json")

if __name__ == "__main__":
    main()
