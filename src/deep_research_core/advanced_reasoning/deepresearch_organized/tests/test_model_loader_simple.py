"""
<PERSON><PERSON><PERSON> tra LocalModelLoader đơn giản.
"""

import sys
print(f"Python version: {sys.version}")

try:
    # Import local_model_loader
    from src.deep_research_core.models.local.model_loader import local_model_loader
    print("<PERSON><PERSON> import local_model_loader thành công!")
    
    # <PERSON><PERSON><PERSON> tra các thuộc tính
    print(f"CUDA available: {local_model_loader.cuda_available}")
    if local_model_loader.cuda_available:
        print(f"CUDA device count: {local_model_loader.cuda_device_count}")
        print(f"CUDA device names: {local_model_loader.cuda_device_names}")
    
    # L<PERSON>y danh sách các mô hình có sẵn
    available_models = local_model_loader.get_available_models()
    print(f"Số lượng mô hình có sẵn: {len(available_models)}")
    if available_models:
        print("Danh sách mô hình có sẵn:")
        for model in available_models:
            print(f"  {model}")
    
except ImportError as e:
    print(f"Lỗi khi import local_model_loader: {e}")
except Exception as e:
    print(f"Lỗi khác khi kiểm tra local_model_loader: {e}")

print("\nKiểm tra hoàn tất!")
