#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for security enhancements and error handling in WebSearchAgent.

This script tests the various security and error handling features added to
the WebSearchAgent, including user agent rotation, connection pooling,
SSL error handling, proxy error handling, and content validation.
"""

from src.deep_research_core.agents.web_search_agent import WebSearchAgent
import time
import argparse

def print_section(title):
    """Print a section title."""
    print("\n" + "=" * 50)
    print(f"  {title}")
    print("=" * 50 + "\n")

def test_user_agent_rotation():
    """Test user agent rotation functionality."""
    print_section("Testing User Agent Rotation")
    
    agent = WebSearchAgent(user_agent_rotation=True, verbose=True)
    
    print("Initial user agent:", agent._get_rotated_user_agent())
    print("After rotation:", agent._get_rotated_user_agent(rotate=True))
    print("After another rotation:", agent._get_rotated_user_agent(rotate=True))
    
    return True

def test_rate_limiting():
    """Test enhanced rate limiting with engine-specific limits."""
    print_section("Testing Enhanced Rate Limiting")
    
    agent = WebSearchAgent(rate_limit=5, verbose=True)
    
    print("Testing global rate limit:")
    for i in range(7):
        result = agent._check_rate_limit(wait_if_limited=False)
        print(f"Request {i+1}: {'Allowed' if result else 'Rate limited'}")
    
    print("\nTesting engine-specific rate limit (google_scholar):")
    for i in range(5):
        result = agent._check_rate_limit(wait_if_limited=False, engine="google_scholar")
        print(f"Request {i+1}: {'Allowed' if result else 'Rate limited'}")
    
    return True

def test_content_validation():
    """Test content validation and sanitization."""
    print_section("Testing Content Validation")
    
    agent = WebSearchAgent()
    
    # Test valid result
    valid_result = {
        "title": "Test Title",
        "url": "https://example.com/test",
        "snippet": "<p>This is a <b>test</b> snippet with HTML</p>"
    }
    
    try:
        sanitized = agent._sanitize_result(valid_result)
        print("Valid result sanitized successfully:")
        print(f"- Title: {sanitized['title']}")
        print(f"- URL: {sanitized['url']}")
        print(f"- Snippet: {sanitized['snippet']}")
        valid_test_passed = True
    except ValueError as e:
        print(f"Error with valid result: {str(e)}")
        valid_test_passed = False
    
    # Test invalid URL
    invalid_url = {
        "title": "Test Title",
        "url": "invalid-url",
        "snippet": "Test snippet"
    }
    
    try:
        sanitized = agent._sanitize_result(invalid_url)
        print("\nInvalid URL passed validation (this is a bug)!")
        invalid_url_test_passed = False
    except ValueError as e:
        print(f"\nExpected error for invalid URL: {str(e)}")
        invalid_url_test_passed = True
    
    # Test missing required field
    missing_field = {
        "url": "https://example.com/test",
        "snippet": "Test snippet"
    }
    
    try:
        sanitized = agent._sanitize_result(missing_field)
        print("\nMissing field passed validation (this is a bug)!")
        missing_field_test_passed = False
    except ValueError as e:
        print(f"\nExpected error for missing field: {str(e)}")
        missing_field_test_passed = True
    
    return valid_test_passed and invalid_url_test_passed and missing_field_test_passed

def test_error_handling():
    """Test the error handling functionality."""
    print_section("Testing Error Handling")
    
    agent = WebSearchAgent()
    
    # Test CAPTCHA error handling
    captcha_response = agent._handle_error("CAPTCHA detected on page", "test query", "duckduckgo")
    print("CAPTCHA error response:")
    print(f"- Success: {captcha_response.get('success')}")
    print(f"- Error: {captcha_response.get('error')}")
    print(f"- CAPTCHA detected: {captcha_response.get('captcha_detected', False)}")
    
    # Test connection error handling
    connection_response = agent._handle_error("Connection timed out", "test query", "google_scholar")
    print("\nConnection error response:")
    print(f"- Success: {connection_response.get('success')}")
    print(f"- Error: {connection_response.get('error')}")
    print(f"- Engine: {connection_response.get('engine')}")
    
    return True

def main():
    """Run all tests."""
    parser = argparse.ArgumentParser(description="Test WebSearchAgent security enhancements")
    parser.add_argument("--test", choices=["all", "user_agent", "rate_limit", "content_validation", "error_handling"], 
                        default="all", help="Test to run")
    args = parser.parse_args()
    
    tests = {
        "user_agent": test_user_agent_rotation,
        "rate_limit": test_rate_limiting,
        "content_validation": test_content_validation,
        "error_handling": test_error_handling
    }
    
    if args.test == "all":
        results = {}
        for name, test_func in tests.items():
            results[name] = test_func()
        
        print("\n" + "=" * 50)
        print("  SUMMARY")
        print("=" * 50)
        for name, passed in results.items():
            print(f"{name}: {'PASSED' if passed else 'FAILED'}")
    else:
        tests[args.test]()

if __name__ == "__main__":
    main() 