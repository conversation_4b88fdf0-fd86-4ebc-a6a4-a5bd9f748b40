#!/usr/bin/env python3
"""
Comprehensive test script for Deep Research Core reasoning components.

This script tests the following components:
1. TreeOfThought (ToT)
2. ChainOfThought (CoT)
3. SQLiteVectorRAG (RAG)
4. Combined reasoning (ToTRAG, CoTRAG)
"""

import os
import sys
import time
import tempfile
from unittest.mock import patch

# Set API key for testing
os.environ["OPENROUTER_API_KEY"] = "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"

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import the components to test
try:
    from src.deep_research_core.reasoning.tot import TreeOfThought
    from src.deep_research_core.reasoning.cot import ChainOfThought
    from src.deep_research_core.reasoning.sqlite_vector_rag import SQLiteVectorRAG
    from src.deep_research_core.reasoning.tot_rag import ToTRAG
    from src.deep_research_core.reasoning.cot_rag import CoTRAG
    print("Successfully imported all reasoning components")
except ImportError as e:
    print(f"Error importing components: {e}")
    sys.exit(1)

def test_tree_of_thought():
    """Test the TreeOfThought implementation."""
    print("\n=== Testing Tree of Thought (ToT) ===\n")

    try:
        # Initialize TreeOfThought with minimal parameters
        tot = TreeOfThought(
            provider="openrouter",
            model="moonshotai/moonlight-16b-a3b-instruct:free",
            max_branches=2,
            max_depth=1,
            verbose=True
        )
        print("Successfully initialized TreeOfThought")

        # Simple query
        query = "Why is the sky blue?"

        print(f"Query: {query}")
        print("Processing...\n")

        # Mock the generate method to avoid API calls
        with patch('src.deep_research_core.models.api.openrouter.provider.OpenRouterProvider.generate') as mock_generate:
            mock_generate.return_value = "The sky appears blue due to a phenomenon called Rayleigh scattering."

            # Perform reasoning
            result = tot.reason(
                query=query
            )

            # Print result
            print("\nResult:")
            print(f"Answer: {result.get('answer', 'No answer')}")
            print(f"Explored paths: {result.get('explored_paths', 0)}")

        print("\nTreeOfThought test completed successfully!")
        return True
    except Exception as e:
        print(f"Error in TreeOfThought test: {e}")
        return False

def test_chain_of_thought():
    """Test the ChainOfThought implementation."""
    print("\n=== Testing Chain of Thought (CoT) ===\n")

    try:
        # Initialize ChainOfThought
        from src.deep_research_core.models.api.openrouter.provider import OpenRouterProvider
        model = OpenRouterProvider(
            model="moonshotai/moonlight-16b-a3b-instruct:free",
            temperature=0.7,
            max_tokens=1000
        )
        cot = ChainOfThought(
            model=model
        )
        print("Successfully initialized ChainOfThought")

        # Simple query
        query = "Explain the concept of machine learning in simple terms."

        print(f"Query: {query}")
        print("Processing...\n")

        # Mock the generate method to avoid API calls
        with patch('src.deep_research_core.models.api.openrouter.provider.OpenRouterProvider.generate') as mock_generate:
            mock_generate.return_value = "Machine learning is a way for computers to learn from data without being explicitly programmed."

            # Create custom prompts for testing
            custom_system_prompt = "You are a helpful AI assistant that explains concepts clearly."
            custom_user_prompt = "Please explain this concept: {query}"

            # Perform reasoning
            result = cot.reason(
                query=query,
                custom_system_prompt=custom_system_prompt,
                custom_user_prompt=custom_user_prompt
            )

            # Print result
            print("\nResult:")
            if hasattr(result, "answer"):
                print(f"Answer: {result.answer}")
            else:
                print(f"Answer: {result.get('answer', 'No answer')}")

        print("\nChainOfThought test completed successfully!")
        return True
    except Exception as e:
        print(f"Error in ChainOfThought test: {e}")
        return False

def test_sqlite_vector_rag():
    """Test the SQLiteVectorRAG implementation."""
    print("\n=== Testing SQLiteVectorRAG ===\n")

    try:
        # Force CPU usage for embeddings
        os.environ["CUDA_VISIBLE_DEVICES"] = ""

        # Create a temporary database file
        with tempfile.NamedTemporaryFile(suffix='.db') as temp_db:
            # Initialize SQLiteVectorRAG
            rag = SQLiteVectorRAG(
                provider="openrouter",
                model="moonshotai/moonlight-16b-a3b-instruct:free",
                temperature=0.7,
                max_tokens=1000,
                db_path=temp_db.name
            )
            print(f"Successfully initialized SQLiteVectorRAG with database: {temp_db.name}")

            # Sample documents
            documents = [
                {
                    "content": "Python is a high-level, interpreted programming language known for its readability and versatility.",
                    "source": "Programming Languages",
                    "title": "Python Overview",
                    "date": "2023-01-15"
                },
                {
                    "content": "Machine learning is a subset of artificial intelligence that enables systems to learn from data and improve from experience.",
                    "source": "AI Technologies",
                    "title": "Machine Learning Basics",
                    "date": "2023-02-20"
                }
            ]

            # Add documents
            with patch.object(rag, '_get_embeddings') as mock_get_embeddings:
                # Mock the embedding function
                mock_get_embeddings.return_value = [[0.1] * 384]  # Simple mock embedding

                # Add documents
                doc_ids = rag.add_documents(documents)
                print(f"Added {len(doc_ids)} documents to the database")

                # Count documents
                count = rag.count()
                print(f"Document count: {count}")

                # Search for documents
                query = "What is Python used for?"

                # Mock the search results
                with patch.object(rag, 'search') as mock_search:
                    mock_search.return_value = [
                        {
                            "content": documents[0]["content"],
                            "source": documents[0]["source"],
                            "title": documents[0]["title"],
                            "date": documents[0]["date"],
                            "score": 0.95
                        }
                    ]

                    # Mock the generate method
                    with patch('src.deep_research_core.models.api.openrouter.provider.OpenRouterProvider.generate') as mock_generate:
                        mock_generate.return_value = "Python is used for web development, data analysis, artificial intelligence, scientific computing, and automation."

                        # Process query
                        print(f"Query: {query}")
                        print("Processing...\n")

                        result = rag.process(query)

                        # Print result
                        print("\nResult:")
                        print(f"Answer: {result.get('answer', 'No answer')}")
                        print(f"Documents: {len(result.get('documents', []))}")

        print("\nSQLiteVectorRAG test completed successfully!")
        return True
    except Exception as e:
        print(f"Error in SQLiteVectorRAG test: {e}")
        return False

def test_tot_rag():
    """Test the ToTRAG implementation."""
    print("\n=== Testing ToTRAG (Tree of Thought + RAG) ===\n")

    try:
        # Force CPU usage for embeddings
        os.environ["CUDA_VISIBLE_DEVICES"] = ""

        # Create a temporary database file
        with tempfile.NamedTemporaryFile(suffix='.db') as temp_db:
            # Initialize ToTRAG with vector store
            vector_store = SQLiteVectorRAG(
                provider="openrouter",
                model="moonshotai/moonlight-16b-a3b-instruct:free",
                temperature=0.7,
                max_tokens=1000,
                db_path=temp_db.name
            )

            # Create ToTRAG with the vector store
            tot_rag = ToTRAG(
                provider="openrouter",
                model="moonshotai/moonlight-16b-a3b-instruct:free",
                temperature=0.7,
                max_tokens=1000,
                max_branches=2,
                max_depth=1,
                vector_store=vector_store
            )
            print(f"Successfully initialized ToTRAG with database: {temp_db.name}")

            # Sample documents
            documents = [
                {
                    "content": "Climate change is causing global temperatures to rise, leading to more extreme weather events.",
                    "source": "Environmental Science",
                    "title": "Climate Change Effects",
                    "date": "2023-03-10"
                },
                {
                    "content": "Renewable energy sources like solar and wind power are becoming increasingly cost-effective alternatives to fossil fuels.",
                    "source": "Energy Research",
                    "title": "Renewable Energy Trends",
                    "date": "2023-04-05"
                }
            ]

            # Add documents
            with patch.object(vector_store, '_get_embeddings') as mock_get_embeddings:
                # Mock the embedding function
                mock_get_embeddings.return_value = [[0.1] * 384]  # Simple mock embedding

                # Add documents
                doc_ids = vector_store.add_documents(documents)
                print(f"Added {len(doc_ids)} documents to the database")

                # Count documents
                count = vector_store.count()
                print(f"Document count: {count}")

                # Process query
                query = "What are the solutions to climate change?"

                # Mock the search results
                with patch.object(vector_store, 'search') as mock_search:
                    mock_search.return_value = [
                        {
                            "content": documents[1]["content"],
                            "source": documents[1]["source"],
                            "title": documents[1]["title"],
                            "date": documents[1]["date"],
                            "score": 0.92
                        }
                    ]

                    # Mock the generate method for ToT
                    with patch('src.deep_research_core.reasoning.tot.TreeOfThought.reason') as mock_reason:
                        mock_reason.return_value = {
                            "answer": "Solutions to climate change include transitioning to renewable energy, improving energy efficiency, and implementing carbon capture technologies.",
                            "explored_paths": 2,
                            "query": query,
                            "provider": "openrouter",
                            "model": "moonshotai/moonlight-16b-a3b-instruct:free"
                        }

                        # Process query
                        print(f"Query: {query}")
                        print("Processing...\n")

                        result = tot_rag.process(query)

                        # Print result
                        print("\nResult:")
                        print(f"Answer: {result.get('answer', 'No answer')}")
                        print(f"Documents: {len(result.get('documents', []))}")
                        print(f"Explored paths: {result.get('explored_paths', 0)}")

        print("\nToTRAG test completed successfully!")
        return True
    except Exception as e:
        print(f"Error in ToTRAG test: {e}")
        return False

def test_cot_rag():
    """Test the CoTRAG implementation."""
    print("\n=== Testing CoTRAG (Chain of Thought + RAG) ===\n")

    try:
        # Force CPU usage for embeddings
        os.environ["CUDA_VISIBLE_DEVICES"] = ""

        # Create a temporary database file
        with tempfile.NamedTemporaryFile(suffix='.db') as temp_db:
            # Initialize CoTRAG with vector store
            vector_store = SQLiteVectorRAG(
                provider="openrouter",
                model="moonshotai/moonlight-16b-a3b-instruct:free",
                temperature=0.7,
                max_tokens=1000,
                db_path=temp_db.name
            )

            # Create CoTRAG with the vector store
            cot_rag = CoTRAG(
                provider="openrouter",
                model="moonshotai/moonlight-16b-a3b-instruct:free",
                temperature=0.7,
                max_tokens=1000,
                vector_store=vector_store
            )
            print(f"Successfully initialized CoTRAG with database: {temp_db.name}")

            # Sample documents
            documents = [
                {
                    "content": "Quantum computing uses quantum bits or qubits, which can exist in multiple states simultaneously due to superposition.",
                    "source": "Quantum Physics",
                    "title": "Quantum Computing Basics",
                    "date": "2023-05-12"
                },
                {
                    "content": "Quantum entanglement allows qubits to be correlated in ways that are not possible with classical bits.",
                    "source": "Quantum Physics",
                    "title": "Quantum Entanglement",
                    "date": "2023-06-20"
                }
            ]

            # Add documents
            with patch.object(vector_store, '_get_embeddings') as mock_get_embeddings:
                # Mock the embedding function
                mock_get_embeddings.return_value = [[0.1] * 384]  # Simple mock embedding

                # Add documents
                doc_ids = vector_store.add_documents(documents)
                print(f"Added {len(doc_ids)} documents to the database")

                # Count documents
                count = vector_store.count()
                print(f"Document count: {count}")

                # Process query
                query = "How does quantum computing differ from classical computing?"

                # Mock the search results
                with patch.object(vector_store, 'search') as mock_search:
                    mock_search.return_value = [
                        {
                            "content": documents[0]["content"],
                            "source": documents[0]["source"],
                            "title": documents[0]["title"],
                            "date": documents[0]["date"],
                            "score": 0.94
                        }
                    ]

                    # Mock the generate method for CoT
                    with patch('src.deep_research_core.reasoning.cot.ChainOfThought.reason') as mock_reason:
                        mock_reason.return_value = {
                            "answer": "Quantum computing differs from classical computing by using qubits instead of bits, allowing for superposition and entanglement, which enables solving certain problems much faster.",
                            "query": query,
                            "provider": "openrouter",
                            "model": "moonshotai/moonlight-16b-a3b-instruct:free"
                        }

                        # Process query
                        print(f"Query: {query}")
                        print("Processing...\n")

                        result = cot_rag.process(query)

                        # Print result
                        print("\nResult:")
                        print(f"Answer: {result.get('answer', 'No answer')}")
                        print(f"Documents: {len(result.get('documents', []))}")

        print("\nCoTRAG test completed successfully!")
        return True
    except Exception as e:
        print(f"Error in CoTRAG test: {e}")
        return False

def main():
    """Main function to run all tests."""
    print("=== Deep Research Core Reasoning Components Test ===\n")

    # Track test results
    results = {}

    # Test TreeOfThought
    results["TreeOfThought"] = test_tree_of_thought()

    # Test ChainOfThought
    results["ChainOfThought"] = test_chain_of_thought()

    # Test SQLiteVectorRAG
    results["SQLiteVectorRAG"] = test_sqlite_vector_rag()

    # Test ToTRAG
    results["ToTRAG"] = test_tot_rag()

    # Test CoTRAG
    results["CoTRAG"] = test_cot_rag()

    # Print summary
    print("\n=== Test Summary ===")
    for component, success in results.items():
        status = "PASSED" if success else "FAILED"
        print(f"{component}: {status}")

    # Return success if all tests passed
    return all(results.values())

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
