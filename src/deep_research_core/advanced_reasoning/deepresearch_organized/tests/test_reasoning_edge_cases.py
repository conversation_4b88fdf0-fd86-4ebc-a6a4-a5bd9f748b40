"""
Test edge cases and different configurations for reasoning components.
"""

import os
import sys
import tempfile
import unittest
from unittest.mock import patch, MagicMock

# Import reasoning components
from src.deep_research_core.reasoning.tot import TreeOfThought
from src.deep_research_core.reasoning.cot import ChainOfThought
from src.deep_research_core.reasoning.sqlite_vector_rag import SQLiteVectorRAG
from src.deep_research_core.reasoning.tot_rag import ToTRAG
from src.deep_research_core.reasoning.cot_rag import CoTRAG

class TestReasoningEdgeCases(unittest.TestCase):
    """Test edge cases for reasoning components."""

    def setUp(self):
        """Set up test environment."""
        # Force CPU usage for embeddings
        os.environ["CUDA_VISIBLE_DEVICES"] = ""

        # Create a temporary database file
        self.temp_db = tempfile.NamedTemporaryFile(suffix='.db')

        # Initialize vector store
        self.vector_store = SQLiteVectorRAG(
            provider="openrouter",
            model="moonshotai/moonlight-16b-a3b-instruct:free",
            temperature=0.7,
            max_tokens=1000,
            db_path=self.temp_db.name
        )

        # Sample documents
        self.documents = [
            {
                "content": "Quantum computing uses quantum bits or qubits, which can exist in multiple states simultaneously due to superposition.",
                "source": "Quantum Physics",
                "title": "Quantum Computing Basics",
                "date": "2023-05-12"
            },
            {
                "content": "Climate change is causing global temperatures to rise, leading to more extreme weather events.",
                "source": "Environmental Science",
                "title": "Climate Change Effects",
                "date": "2023-03-10"
            },
            {
                "content": "Machine learning is a subset of artificial intelligence that enables systems to learn from data and improve from experience.",
                "source": "AI Technologies",
                "title": "Machine Learning Basics",
                "date": "2023-02-20"
            }
        ]

    def tearDown(self):
        """Clean up after tests."""
        self.temp_db.close()

    def test_empty_query(self):
        """Test handling of empty query."""
        # Initialize ToT
        tot = TreeOfThought(
            provider="openrouter",
            model="moonshotai/moonlight-16b-a3b-instruct:free",
            temperature=0.7,
            max_tokens=1000
        )

        # Process empty query directly without mocking
        # The empty query handling is now built into the class
        result = tot.reason("")

        # Check result
        self.assertIn("answer", result)
        # Check that the answer indicates an empty query
        self.assertIn("not conclusive", result.get("answer", ""))

    def test_very_long_query(self):
        """Test handling of very long query."""
        # Create a very long query
        long_query = "What is " + "very " * 500 + "long query?"

        # Initialize CoT
        cot = ChainOfThought(
            provider="openrouter",
            model="moonshotai/moonlight-16b-a3b-instruct:free",
            temperature=0.7,
            max_tokens=1000
        )

        # Mock the generate method
        with patch('src.deep_research_core.models.api.openrouter.provider.OpenRouterProvider.generate') as mock_generate:
            mock_generate.return_value = "This query is too long to process effectively."

            # Process long query
            result = cot.reason(long_query)

            # Check result
            self.assertEqual(result.get("answer"), "This query is too long to process effectively.")

    def test_multilingual_query(self):
        """Test handling of multilingual query."""
        # Vietnamese query
        vietnamese_query = "Máy tính lượng tử khác với máy tính cổ điển như thế nào?"

        # Initialize ToTRAG
        tot_rag = ToTRAG(
            provider="openrouter",
            model="moonshotai/moonlight-16b-a3b-instruct:free",
            temperature=0.7,
            max_tokens=1000,
            vector_store=self.vector_store,
            language="vi"  # Vietnamese
        )

        # Add documents
        with patch.object(self.vector_store, '_get_embeddings') as mock_get_embeddings:
            # Mock the embedding function
            mock_get_embeddings.return_value = [[0.1] * 384]  # Simple mock embedding

            # Add documents
            self.vector_store.add_documents(self.documents)

            # Mock the search results
            with patch.object(self.vector_store, 'search') as mock_search:
                mock_search.return_value = [
                    {
                        "content": self.documents[0]["content"],
                        "source": self.documents[0]["source"],
                        "title": self.documents[0]["title"],
                        "date": self.documents[0]["date"],
                        "score": 0.94
                    }
                ]

                # Mock the generate method for ToT
                with patch('src.deep_research_core.reasoning.tot.TreeOfThought.reason') as mock_reason:
                    mock_reason.return_value = {
                        "answer": "Máy tính lượng tử sử dụng qubits thay vì bits, cho phép chúng tồn tại trong nhiều trạng thái đồng thời nhờ vào hiện tượng chồng chất lượng tử.",
                        "explored_paths": 2,
                        "query": vietnamese_query,
                        "provider": "openrouter",
                        "model": "moonshotai/moonlight-16b-a3b-instruct:free"
                    }

                    # Process query
                    result = tot_rag.process(vietnamese_query)

                    # Check result
                    self.assertIn("answer", result)
                    self.assertIn("documents", result)
                    self.assertEqual(len(result.get("documents", [])), 1)

    def test_error_handling(self):
        """Test error handling in reasoning components."""
        # Initialize SQLiteVectorRAG
        rag = SQLiteVectorRAG(
            provider="openrouter",
            model="moonshotai/moonlight-16b-a3b-instruct:free",
            temperature=0.7,
            max_tokens=1000,
            db_path=self.temp_db.name
        )

        # Test with invalid document
        invalid_document = {
            "invalid_field": "This document doesn't have required fields"
        }

        # Add invalid document
        with patch.object(rag, '_get_embeddings') as mock_get_embeddings:
            # Mock the embedding function
            mock_get_embeddings.return_value = [[0.1] * 384]  # Simple mock embedding

            # Add invalid document
            doc_ids = rag.add_documents([invalid_document])

            # Check result
            self.assertEqual(len(doc_ids), 0)  # Should not add any documents

    def test_adaptive_parameters(self):
        """Test adaptive parameter adjustment."""
        # Initialize ToTRAG with adaptive mode
        tot_rag = ToTRAG(
            provider="openrouter",
            model="moonshotai/moonlight-16b-a3b-instruct:free",
            temperature=0.7,
            max_tokens=1000,
            vector_store=self.vector_store,
            max_branches=2,
            max_depth=2,
            adaptive=True
        )

        # Store original parameters
        original_max_branches = tot_rag.max_branches
        original_max_depth = tot_rag.max_depth

        # Add documents
        with patch.object(self.vector_store, '_get_embeddings') as mock_get_embeddings:
            # Mock the embedding function
            mock_get_embeddings.return_value = [[0.1] * 384]  # Simple mock embedding

            # Add documents
            self.vector_store.add_documents(self.documents)

            # Mock the search results
            with patch.object(self.vector_store, 'search') as mock_search:
                mock_search.return_value = [
                    {
                        "content": self.documents[0]["content"],
                        "source": self.documents[0]["source"],
                        "title": self.documents[0]["title"],
                        "date": self.documents[0]["date"],
                        "score": 0.94
                    }
                ]

                # Mock the parameter optimizer
                with patch.object(tot_rag.tot.parameter_optimizer, 'estimate_complexity') as mock_complexity:
                    # Set a high complexity to trigger parameter adjustment
                    mock_complexity.return_value = 0.9

                    # Mock the generate method for ToT
                    with patch('src.deep_research_core.reasoning.tot.TreeOfThought.reason') as mock_reason:
                        mock_reason.return_value = {
                            "answer": "Quantum computing differs from classical computing by using qubits instead of bits.",
                            "explored_paths": 2,
                            "query": "How does quantum computing differ from classical computing?",
                            "provider": "openrouter",
                            "model": "moonshotai/moonlight-16b-a3b-instruct:free"
                        }

                        # Process query
                        result = tot_rag.process("How does quantum computing differ from classical computing?")

                        # Check if parameters were adjusted
                        self.assertNotEqual(tot_rag.tot.max_branches, original_max_branches)
                        self.assertNotEqual(tot_rag.tot.max_depth, original_max_depth)

    def test_caching(self):
        """Test caching functionality."""
        # Initialize CoTRAG with caching
        cot_rag = CoTRAG(
            provider="openrouter",
            model="moonshotai/moonlight-16b-a3b-instruct:free",
            temperature=0.7,
            max_tokens=1000,
            vector_store=self.vector_store,
            use_cache=True
        )

        # Mock the API provider to avoid OpenRouter API key error
        with patch.object(cot_rag, 'api_provider') as mock_api_provider:
            mock_api_provider.generate.return_value = "Quantum computing differs from classical computing by using qubits instead of bits."

            # Add documents
            with patch.object(self.vector_store, '_get_embeddings') as mock_get_embeddings:
                # Mock the embedding function
                mock_get_embeddings.return_value = [[0.1] * 384]  # Simple mock embedding

                # Add documents
                self.vector_store.add_documents(self.documents)

                # Mock the search results
                with patch.object(self.vector_store, 'search') as mock_search:
                    mock_search.return_value = [
                        {
                            "content": self.documents[0]["content"],
                            "source": self.documents[0]["source"],
                            "title": self.documents[0]["title"],
                            "date": self.documents[0]["date"],
                            "score": 0.94
                        }
                    ]

                    # Process query first time
                    query = "How does quantum computing differ from classical computing?"
                    result1 = cot_rag.process(query)

                    # Process same query second time
                    result2 = cot_rag.process(query)

                    # Check if second call used cache
                    self.assertEqual(result1.get("answer"), result2.get("answer"))

                    # Verify that the API provider was called only once
                    self.assertEqual(mock_api_provider.generate.call_count, 1)

if __name__ == "__main__":
    unittest.main()
