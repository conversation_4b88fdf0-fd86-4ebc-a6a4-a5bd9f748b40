"""
Test script for the reasoning modules only.
"""

import os
import sys
import json
from typing import Dict, List, Any

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import modules directly
from src.deep_research_core.reasoning.multi_query_decomposer import MultiQueryDecomposition
from src.deep_research_core.reasoning.multi_source_validator import MultiSourceValidator
from src.deep_research_core.reasoning.fact_checker import <PERSON>act<PERSON>he<PERSON>
from src.deep_research_core.reasoning.hybrid_search import HybridSearch
from src.deep_research_core.reasoning.semantic_chunking import SemanticChunking
from src.deep_research_core.reasoning.knowledge_graph_rag import KnowledgeGraphRAG
from src.deep_research_core.reasoning.knowledge_graph_reasoner import KnowledgeGraphReasoner

# Sample data
SAMPLE_SOURCES = [
    {
        "id": "source1",
        "title": "AI ở Việt Nam",
        "content": "<PERSON><PERSON><PERSON> tu<PERSON> nhân t<PERSON>o (AI) đang phát triển nhanh chóng ở Việt Nam. Các công ty công nghệ lớn như <PERSON>, FPT AI và Zalo AI đang dẫn đầu trong nghiên cứu và phát triển AI.",
        "author": "Nguyễn Văn A",
        "publication_date": "2023-01-01",
        "url": "https://example.com/ai-vietnam",
        "publisher": "Tech Magazine"
    },
    {
        "id": "source2",
        "title": "VinAI: Công ty AI hàng đầu Việt Nam",
        "content": "VinAI được thành lập vào năm 2018 và là một phần của Tập đoàn Vingroup. Họ tập trung vào nghiên cứu AI cơ bản và ứng dụng.",
        "author": "Trần Thị B",
        "publication_date": "2023-02-15",
        "url": "https://example.org/vinai",
        "publisher": "Tech News"
    },
    {
        "id": "source3",
        "title": "FPT AI và các ứng dụng",
        "content": "FPT AI là bộ phận AI của Tập đoàn FPT, một trong những công ty công nghệ lớn nhất Việt Nam. Họ đã phát triển nhiều sản phẩm AI như chatbot, hệ thống nhận dạng khuôn mặt và xử lý ngôn ngữ tự nhiên.",
        "author": "Lê Văn C",
        "publication_date": "2023-03-10",
        "url": "https://example.com/fpt-ai",
        "publisher": "Tech Review"
    }
]

def test_multi_query_decomposition():
    """Test the MultiQueryDecomposition module."""
    print("\n=== Testing MultiQueryDecomposition ===")
    
    try:
        # Initialize MultiQueryDecomposition
        multi_query_decomposition = MultiQueryDecomposition(
            language="vi",
            max_depth=2
        )
        
        # Test complex query
        complex_query = "So sánh các công ty AI ở Việt Nam, phân tích ưu điểm và nhược điểm của mỗi công ty, và dự đoán xu hướng phát triển AI ở Việt Nam trong 5 năm tới."
        
        # Decompose query
        decomposition_result = multi_query_decomposition.decompose(
            query=complex_query,
            context={"topic": "AI ở Việt Nam"},
            max_sub_queries=5,
            min_sub_queries=2
        )
        
        # Print decomposition result
        print("Decomposition result:")
        print(f"Original query: {decomposition_result['original_query']}")
        print(f"Is decomposed: {decomposition_result['is_decomposed']}")
        print(f"Number of sub-queries: {len(decomposition_result['sub_queries'])}")
        
        print("\nSub-queries:")
        for i, sub_query in enumerate(decomposition_result['sub_queries']):
            print(f"{i+1}. {sub_query['query']}")
        
        print("\nProcessing order:")
        for i, query_id in enumerate(decomposition_result['processing_order']):
            if query_id in multi_query_decomposition.query_graph:
                print(f"{i+1}. {multi_query_decomposition.query_graph[query_id]['query']}")
        
        # Test synthesis
        if decomposition_result['is_decomposed'] and decomposition_result['sub_queries']:
            # Simulate query results
            query_results = {}
            for sub_query in decomposition_result['sub_queries']:
                query_results[sub_query['id']] = f"Kết quả cho câu hỏi: {sub_query['query']}"
            
            # Synthesize results
            synthesis_result = multi_query_decomposition.synthesize_results(
                query_results=query_results,
                original_query=complex_query
            )
            
            print("\nSynthesis result:")
            print(synthesis_result)
    
    except Exception as e:
        print(f"Error in MultiQueryDecomposition test: {str(e)}")

def test_multi_source_validator():
    """Test the MultiSourceValidator module."""
    print("\n=== Testing MultiSourceValidator ===")
    
    try:
        # Initialize MultiSourceValidator
        multi_source_validator = MultiSourceValidator(
            language="vi",
            min_sources=2
        )
        
        # Test information to validate
        information = "VinAI được thành lập vào năm 2018 và là một phần của Tập đoàn Vingroup. Họ tập trung vào nghiên cứu AI cơ bản và ứng dụng."
        
        # Validate information
        validation_result = multi_source_validator.validate(
            information=information,
            sources=SAMPLE_SOURCES,
            context={"topic": "AI ở Việt Nam"}
        )
        
        # Print validation result
        print("Validation result:")
        print(json.dumps(validation_result, indent=2, ensure_ascii=False))
        
        # Test contradicting information
        information_items = [
            "VinAI được thành lập vào năm 2018.",
            "VinAI được thành lập vào năm 2019.",
            "VinAI là một phần của Tập đoàn Vingroup."
        ]
        
        # Analyze contradictions
        contradiction_result = multi_source_validator.analyze_contradictions(
            information_items=information_items,
            sources=SAMPLE_SOURCES
        )
        
        # Print contradiction result
        print("\nContradiction analysis:")
        print(json.dumps(contradiction_result, indent=2, ensure_ascii=False))
        
        # Get most reliable information
        reliability_result = multi_source_validator.get_most_reliable_information(
            information_items=information_items,
            sources=SAMPLE_SOURCES,
            context={"topic": "AI ở Việt Nam"}
        )
        
        # Print reliability result
        print("\nMost reliable information:")
        print(json.dumps(reliability_result, indent=2, ensure_ascii=False))
    
    except Exception as e:
        print(f"Error in MultiSourceValidator test: {str(e)}")

def test_fact_checker():
    """Test the FactChecker module."""
    print("\n=== Testing FactChecker ===")
    
    try:
        # Initialize FactChecker
        fact_checker = FactChecker(
            language="vi"
        )
        
        # Test claim to check
        claim = "VinAI được thành lập vào năm 2019 và có trụ sở tại Hà Nội."
        
        # Check claim
        check_result = fact_checker.check_claim(
            claim=claim,
            sources=SAMPLE_SOURCES,
            context={"topic": "AI ở Việt Nam"}
        )
        
        # Print check result
        print("Fact check result:")
        print(json.dumps(check_result, indent=2, ensure_ascii=False))
        
        # Test multiple claims
        claims = [
            "VinAI được thành lập vào năm 2018.",
            "FPT AI đã phát triển nhiều sản phẩm AI như chatbot.",
            "Zalo AI là bộ phận nghiên cứu AI của Zalo, với hơn 200 triệu người dùng."
        ]
        
        # Check multiple claims
        multi_check_result = fact_checker.check_multiple_claims(
            claims=claims,
            sources=SAMPLE_SOURCES,
            context={"topic": "AI ở Việt Nam"}
        )
        
        # Print multi-check result
        print("\nMultiple claims check result:")
        print(json.dumps(multi_check_result, indent=2, ensure_ascii=False))
    
    except Exception as e:
        print(f"Error in FactChecker test: {str(e)}")

def test_hybrid_search():
    """Test the HybridSearch module."""
    print("\n=== Testing HybridSearch ===")
    
    try:
        # Initialize HybridSearch
        hybrid_search = HybridSearch(
            language="vi"
        )
        
        # Test query
        query = "Công ty AI nào được thành lập vào năm 2018?"
        
        # Search
        search_result = hybrid_search.search(
            query=query,
            documents=SAMPLE_SOURCES,
            top_k=2,
            context={"topic": "AI ở Việt Nam"}
        )
        
        # Print search result
        print("Search result:")
        print(json.dumps(search_result, indent=2, ensure_ascii=False))
        
        # Test advanced search
        advanced_search_result = hybrid_search.advanced_search(
            query=query,
            documents=SAMPLE_SOURCES,
            top_k=2,
            semantic_weight=0.7,
            keyword_weight=0.3,
            context={"topic": "AI ở Việt Nam"}
        )
        
        # Print advanced search result
        print("\nAdvanced search result:")
        print(json.dumps(advanced_search_result, indent=2, ensure_ascii=False))
    
    except Exception as e:
        print(f"Error in HybridSearch test: {str(e)}")

def main():
    """Run all tests."""
    print("Testing reasoning modules...")
    
    # Test MultiQueryDecomposition
    test_multi_query_decomposition()
    
    # Test MultiSourceValidator
    test_multi_source_validator()
    
    # Test FactChecker
    test_fact_checker()
    
    # Test HybridSearch
    test_hybrid_search()
    
    print("\nAll tests completed!")

if __name__ == "__main__":
    main()
