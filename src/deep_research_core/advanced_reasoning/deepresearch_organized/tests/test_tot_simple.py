#!/usr/bin/env python3
"""
Simple test script for TreeOfThought.
"""

import os
import sys
from unittest.mock import patch

# Set API key for testing
os.environ["OPENROUTER_API_KEY"] = "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"

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

try:
    from src.deep_research_core.reasoning.tot import TreeOfThought
    print("Successfully imported TreeOfThought")
except ImportError as e:
    print(f"Error importing TreeOfThought: {e}")
    sys.exit(1)

def main():
    """
    Main function to test TreeOfThought.
    """
    print("=== Testing Tree of Thought (ToT) ===\n")
    
    try:
        # Initialize TreeOfThought with minimal parameters
        tot = TreeOfThought(
            provider="openrouter",
            model="moonshotai/moonlight-16b-a3b-instruct:free",
            max_branches=2,
            max_depth=1,
            verbose=True
        )
        print("Successfully initialized TreeOfThought")
        
        # Simple query
        query = "Why is the sky blue?"
        
        print(f"Query: {query}")
        print("Processing...\n")
        
        # Mock the generate method to avoid API calls
        with patch('src.deep_research_core.models.api.openrouter.provider.OpenRouterProvider.generate') as mock_generate:
            mock_generate.return_value = "The sky appears blue due to a phenomenon called Rayleigh scattering."
            
            # Perform reasoning
            result = tot.reason(
                query=query
            )
            
            # Print result
            print("\nResult:")
            print(f"Answer: {result.get('answer', 'No answer')}")
            print(f"Explored paths: {result.get('explored_paths', 0)}")
        
        print("\nTest completed successfully!")
        return 0
    except Exception as e:
        print(f"Error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
