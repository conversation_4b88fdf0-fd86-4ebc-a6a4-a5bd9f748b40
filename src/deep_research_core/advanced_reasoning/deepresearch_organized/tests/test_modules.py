"""
Test script for the newly implemented modules.
"""

import os
import sys
import json
from typing import Dict, List, Any

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.deep_research_core.reasoning import (
    KnowledgeGraphReasoner,
    KnowledgeGraphRAG,
    FactChecker,
    HybridSearch,
    SemanticChunking
)

# Sample data
SAMPLE_TEXT = """
Trí tuệ nhân tạo (AI) đang phát triển nhanh chóng ở Việt Nam. Các công ty công nghệ lớn như VinAI, FPT AI và Zalo AI đang dẫn đầu trong nghiên cứu và phát triển AI.
VinAI được thành lập vào năm 2018 và là một phần của Tập đoàn Vingroup. Họ tập trung vào nghiên cứu AI cơ bản và ứng dụng.
FPT AI là bộ phận AI của Tập đo<PERSON> FPT, một trong những công ty công nghệ lớn nhất Việt Nam. Họ đã phát triển nhiều sản phẩm AI như chatbot, hệ thống nhận dạng khuôn mặt và xử lý ngôn ngữ tự nhiên.
Zalo AI là bộ phận nghiên cứu AI của Zalo, ứng dụng nhắn tin phổ biến nhất Việt Nam với hơn 100 triệu người dùng.
Chính phủ Việt Nam cũng đã ban hành Chiến lược quốc gia về nghiên cứu, phát triển và ứng dụng Trí tuệ nhân tạo đến năm 2030, với mục tiêu đưa Việt Nam trở thành trung tâm đổi mới sáng tạo và phát triển AI trong khu vực.
"""

SAMPLE_DOCUMENTS = [
    {
        "id": "doc1",
        "content": "Trí tuệ nhân tạo (AI) đang phát triển nhanh chóng ở Việt Nam. Các công ty công nghệ lớn như VinAI, FPT AI và Zalo AI đang dẫn đầu trong nghiên cứu và phát triển AI.",
        "title": "AI ở Việt Nam",
        "source": "Báo cáo nghiên cứu",
        "embedding": [0.1] * 1536  # Dummy embedding
    },
    {
        "id": "doc2",
        "content": "VinAI được thành lập vào năm 2018 và là một phần của Tập đoàn Vingroup. Họ tập trung vào nghiên cứu AI cơ bản và ứng dụng.",
        "title": "VinAI",
        "source": "Website công ty",
        "embedding": [0.2] * 1536  # Dummy embedding
    },
    {
        "id": "doc3",
        "content": "FPT AI là bộ phận AI của Tập đoàn FPT, một trong những công ty công nghệ lớn nhất Việt Nam. Họ đã phát triển nhiều sản phẩm AI như chatbot, hệ thống nhận dạng khuôn mặt và xử lý ngôn ngữ tự nhiên.",
        "title": "FPT AI",
        "source": "Website công ty",
        "embedding": [0.3] * 1536  # Dummy embedding
    },
    {
        "id": "doc4",
        "content": "Zalo AI là bộ phận nghiên cứu AI của Zalo, ứng dụng nhắn tin phổ biến nhất Việt Nam với hơn 100 triệu người dùng.",
        "title": "Zalo AI",
        "source": "Website công ty",
        "embedding": [0.4] * 1536  # Dummy embedding
    },
    {
        "id": "doc5",
        "content": "Chính phủ Việt Nam đã ban hành Chiến lược quốc gia về nghiên cứu, phát triển và ứng dụng Trí tuệ nhân tạo đến năm 2030, với mục tiêu đưa Việt Nam trở thành trung tâm đổi mới sáng tạo và phát triển AI trong khu vực.",
        "title": "Chiến lược AI Việt Nam",
        "source": "Văn bản chính phủ",
        "embedding": [0.5] * 1536  # Dummy embedding
    }
]

def test_fact_checker():
    """Test the FactChecker module."""
    print("\n=== Testing FactChecker ===")

    # Initialize FactChecker
    fact_checker = FactChecker()

    # Test text
    test_text = "VinAI được thành lập vào năm 2019 và là một phần của Tập đoàn Vingroup. Zalo có hơn 200 triệu người dùng."

    # Check facts
    result = fact_checker.check_facts(test_text, SAMPLE_TEXT)

    # Print result
    print("Facts checked:")
    print(json.dumps(result, indent=2, ensure_ascii=False))

    # Test consistency checking
    consistency_result = fact_checker.check_consistency(test_text, SAMPLE_TEXT)

    # Print result
    print("\nConsistency check:")
    print(json.dumps(consistency_result, indent=2, ensure_ascii=False))

def test_hybrid_search():
    """Test the HybridSearch module."""
    print("\n=== Testing HybridSearch ===")

    # Initialize HybridSearch
    hybrid_search = HybridSearch()

    # Test query
    query = "Công ty AI ở Việt Nam"

    # Search
    results = hybrid_search.search(query, SAMPLE_DOCUMENTS, top_k=3)

    # Print results
    print(f"Search results for query: '{query}'")
    for i, result in enumerate(results):
        print(f"\nResult {i+1}:")
        print(f"Title: {result.get('title', 'N/A')}")
        print(f"Content: {result.get('content', 'N/A')[:100]}...")
        print(f"Semantic score: {result.get('semantic_score', 0):.4f}")
        print(f"Keyword score: {result.get('keyword_score', 0):.4f}")
        print(f"Combined score: {result.get('combined_score', 0):.4f}")

def test_semantic_chunking():
    """Test the SemanticChunking module."""
    print("\n=== Testing SemanticChunking ===")

    # Initialize SemanticChunking
    semantic_chunking = SemanticChunking(min_chunk_size=50, max_chunk_size=200)

    # Chunk text
    chunks = semantic_chunking.chunk_text(SAMPLE_TEXT)

    # Print chunks
    print(f"Generated {len(chunks)} chunks:")
    for i, chunk in enumerate(chunks):
        print(f"\nChunk {i+1}:")
        print(f"Text: {chunk['text'][:100]}...")
        print(f"Offset: {chunk['offset']}")
        print(f"Length: {chunk['length']}")

def test_knowledge_graph_rag():
    """Test the KnowledgeGraphRAG module."""
    print("\n=== Testing KnowledgeGraphRAG ===")

    # Initialize KnowledgeGraphRAG
    kg_rag = KnowledgeGraphRAG()

    try:
        # Add documents with embeddings
        documents_with_embeddings = []
        for doc in SAMPLE_DOCUMENTS:
            doc_copy = doc.copy()
            if 'embedding' in doc_copy:
                del doc_copy['embedding']  # Remove embedding from document
            documents_with_embeddings.append(doc_copy)

        embeddings = [doc.get('embedding', [0.0] * 1536) for doc in SAMPLE_DOCUMENTS]

        # Add documents
        doc_ids = kg_rag.add_documents(documents_with_embeddings, embeddings)
        print(f"Added {len(doc_ids)} documents")

        # Process a query
        query = "Các công ty AI ở Việt Nam"
        result = kg_rag.process(query)

        # Print result
        print(f"Query: {query}")
        print(f"Response: {result['response']}")
    except Exception as e:
        print(f"Error in KnowledgeGraphRAG test: {str(e)}")

def test_knowledge_graph_reasoner():
    """Test the KnowledgeGraphReasoner module."""
    print("\n=== Testing KnowledgeGraphReasoner ===")

    try:
        # Initialize KnowledgeGraphReasoner
        kg_reasoner = KnowledgeGraphReasoner()

        # Reason about a query
        query = "So sánh các công ty AI ở Việt Nam"
        result = kg_reasoner.reason(query, SAMPLE_TEXT)

        # Print result
        print(f"Query: {query}")
        print(f"Reasoning: {result['reasoning']}")
    except Exception as e:
        print(f"Error in KnowledgeGraphReasoner test: {str(e)}")

def main():
    """Run all tests."""
    print("Testing newly implemented modules...")

    # Test FactChecker
    test_fact_checker()

    # Test HybridSearch
    test_hybrid_search()

    # Test SemanticChunking
    test_semantic_chunking()

    # Test KnowledgeGraphRAG
    test_knowledge_graph_rag()

    # Test KnowledgeGraphReasoner
    test_knowledge_graph_reasoner()

    print("\nAll tests completed!")

if __name__ == "__main__":
    main()
