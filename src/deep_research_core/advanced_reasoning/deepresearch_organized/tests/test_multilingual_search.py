#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for multilingual search functionality.

This script tests the WebSearchAgent's ability to handle multilingual queries,
including translation and locale-specific ranking.
"""

from src.deep_research_core.agents.web_search_agent import WebSearchAgent
import sys
import json

def main():
    """Run multilingual search tests."""
    print("Testing Multilingual Search Functionality")
    print("----------------------------------------")
    
    # Initialize WebSearchAgent with multilingual support
    agent = WebSearchAgent(
        search_method="api", 
        translate_query=True,
        translate_results=True,
        preferred_languages=["vi", "en"],
        verbose=True
    )
    
    # Test Vietnamese query
    print("\n1. Testing Vietnamese query with translation:")
    vietnamese_query = "Tác động của trí tuệ nhân tạo đến việc làm trong tương lai"
    print(f"Query: {vietnamese_query}")
    
    results = agent.search(
        query=vietnamese_query,
        num_results=5,
        get_content=False,
        language="vi"  # Explicitly tell the agent this is Vietnamese
    )
    
    # Print summary of results
    print(f"\nSearch successful: {results['success']}")
    print(f"Translated: {results.get('translated', False)}")
    print(f"Original language: {results.get('original_language', 'unknown')}")
    print(f"Search language: {results.get('search_language', 'unknown')}")
    print(f"Number of results: {len(results.get('results', []))}")
    
    # Print first result with translation info
    if results['success'] and results['results']:
        first_result = results['results'][0]
        print("\nFirst result:")
        print(f"Title: {first_result.get('title', 'No title')}")
        print(f"Original title: {first_result.get('original_title', 'N/A')}")
        print(f"Snippet: {first_result.get('snippet', 'No snippet')[:100]}...")
        print(f"URL: {first_result.get('url', 'No URL')}")
        if 'locale_score' in first_result:
            print(f"Locale score: {first_result.get('locale_score', 0)}")
    
    # Test English query (no translation needed)
    print("\n2. Testing English query (no translation needed):")
    english_query = "Impact of artificial intelligence on future jobs"
    print(f"Query: {english_query}")
    
    results = agent.search(
        query=english_query,
        num_results=5,
        get_content=False
    )
    
    # Print summary of results
    print(f"\nSearch successful: {results['success']}")
    print(f"Translated: {results.get('translated', False)}")
    print(f"Original language: {results.get('original_language', 'unknown')}")
    print(f"Search language: {results.get('search_language', 'unknown')}")
    print(f"Number of results: {len(results.get('results', []))}")
    
    # Print first result
    if results['success'] and results['results']:
        first_result = results['results'][0]
        print("\nFirst result:")
        print(f"Title: {first_result.get('title', 'No title')}")
        print(f"Snippet: {first_result.get('snippet', 'No snippet')[:100]}...")
        print(f"URL: {first_result.get('url', 'No URL')}")
    
    print("\nTest completed.")

if __name__ == "__main__":
    main() 