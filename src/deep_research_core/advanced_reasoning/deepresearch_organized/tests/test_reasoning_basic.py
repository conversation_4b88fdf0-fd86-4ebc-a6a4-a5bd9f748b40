#!/usr/bin/env python3
"""
Basic test script for Deep Research Core reasoning components.

This script tests the following components:
1. TreeOfThought (ToT)
2. ChainOfThought (CoT)
"""

import os
import sys
from unittest.mock import patch

# Set API key for testing
os.environ["OPENROUTER_API_KEY"] = "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"

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

try:
    from src.deep_research_core.reasoning.tot import TreeOfThought
    from src.deep_research_core.reasoning.cot import ChainOfThought
    print("Successfully imported reasoning components")
except ImportError as e:
    print(f"Error importing components: {e}")
    sys.exit(1)

def test_tree_of_thought():
    """Test the TreeOfThought implementation."""
    print("\n=== Testing Tree of Thought (ToT) ===\n")

    try:
        # Initialize TreeOfThought with minimal parameters
        tot = TreeOfThought(
            provider="openrouter",
            model="moonshotai/moonlight-16b-a3b-instruct:free",
            max_branches=2,
            max_depth=1,
            verbose=True
        )
        print("Successfully initialized TreeOfThought")

        # Simple query
        query = "Why is the sky blue?"

        print(f"Query: {query}")
        print("Processing...\n")

        # Mock the generate method to avoid API calls
        with patch('src.deep_research_core.models.api.openrouter.provider.OpenRouterProvider.generate') as mock_generate:
            mock_generate.return_value = "The sky appears blue due to a phenomenon called Rayleigh scattering."

            # Perform reasoning
            result = tot.reason(
                query=query
            )

            # Print result
            print("\nResult:")
            if hasattr(result, "answer"):
                print(f"Answer: {result.answer}")
            else:
                print("Answer: No answer")

            if hasattr(result, "metadata") and "explored_paths" in result.metadata:
                print(f"Explored paths: {result.metadata['explored_paths']}")
            else:
                print("Explored paths: 1")

        print("\nTreeOfThought test completed successfully!")
        return True
    except Exception as e:
        print(f"Error in TreeOfThought test: {e}")
        return False

def test_chain_of_thought():
    """Test the ChainOfThought implementation."""
    print("\n=== Testing Chain of Thought (CoT) ===\n")

    try:
        # Initialize ChainOfThought
        from src.deep_research_core.models.api.openrouter.provider import OpenRouterProvider
        model = OpenRouterProvider(
            model="moonshotai/moonlight-16b-a3b-instruct:free",
            temperature=0.7,
            max_tokens=1000
        )
        cot = ChainOfThought(
            model=model
        )
        print("Successfully initialized ChainOfThought")

        # Simple query
        query = "Explain the concept of machine learning in simple terms."

        print(f"Query: {query}")
        print("Processing...\n")

        # Mock the generate method to avoid API calls
        with patch('src.deep_research_core.models.api.openrouter.provider.OpenRouterProvider.generate') as mock_generate:
            mock_generate.return_value = "Machine learning is a way for computers to learn from data without being explicitly programmed."

            # Create custom prompts for testing
            custom_system_prompt = "You are a helpful AI assistant that explains concepts clearly."
            custom_user_prompt = "Please explain this concept: {query}"

            # Perform reasoning
            result = cot.reason(
                query=query,
                custom_system_prompt=custom_system_prompt,
                custom_user_prompt=custom_user_prompt
            )

            # Print result
            print("\nResult:")
            if hasattr(result, "answer"):
                print(f"Answer: {result.answer}")
            else:
                print("Answer: No answer")

        print("\nChainOfThought test completed successfully!")
        return True
    except Exception as e:
        print(f"Error in ChainOfThought test: {e}")
        return False

def main():
    """Main function to run all tests."""
    print("=== Deep Research Core Basic Reasoning Test ===\n")

    # Track test results
    results = {}

    # Test TreeOfThought
    results["TreeOfThought"] = test_tree_of_thought()

    # Test ChainOfThought
    results["ChainOfThought"] = test_chain_of_thought()

    # Print summary
    print("\n=== Test Summary ===")
    for component, success in results.items():
        status = "PASSED" if success else "FAILED"
        print(f"{component}: {status}")

    # Return success if all tests passed
    return all(results.values())

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
