"""
Test script for the direct Crawlee search implementation.

This script tests the direct Crawlee search using Node.js and the Crawlee library.
"""

import os
import sys
import time
import json

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from src.deep_research_core.agents.web_search_agent import WebSearchAgent

def main():
    """Main test function for direct Crawlee search."""
    print("Testing direct Crawlee search implementation...")
    
    # Create WebSearchAgent
    agent = WebSearchAgent(verbose=True)
    
    # Test query - try a mix of simple and complex queries
    test_queries = [
        "Python programming language",
        "How to implement machine learning algorithms in TensorFlow",
        "C<PERSON>ch làm b<PERSON>h m<PERSON> t<PERSON> thố<PERSON>",
        "So sánh chi tiết gi<PERSON>a <PERSON>, Angular và Vue.js"
    ]
    
    for query in test_queries:
        print(f"\n\n=== Testing query: {query} ===")
        
        # Try direct_crawlee method
        start_time = time.time()
        print(f"Running direct_crawlee search for: {query}")
        results = agent.search(query=query, method="direct_crawlee", num_results=5)
        end_time = time.time()
        
        # Print results summary
        print(f"Search completed in {end_time - start_time:.2f} seconds")
        print(f"Success: {results.get('success', False)}")
        print(f"Engine: {results.get('engine', 'unknown')}")
        print(f"Result count: {len(results.get('results', []))}")
        
        # Print the first result details
        if results.get("success") and results.get("results"):
            first_result = results["results"][0]
            print("\nFirst result:")
            print(f"Title: {first_result.get('title', 'N/A')}")
            print(f"URL: {first_result.get('url', 'N/A')}")
            print(f"Snippet: {first_result.get('snippet', 'N/A')[:100]}...")
            
            # Check if content was extracted
            if "content" in first_result:
                print(f"Content length: {len(first_result['content'])} characters")
                print(f"Content preview: {first_result['content'][:100]}...")
        else:
            print(f"Error: {results.get('error', 'Unknown error')}")
            
        # Save results to file
        results_file = f"crawlee_results_{query.replace(' ', '_')[:20]}.json"
        with open(results_file, "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"Full results saved to {results_file}")
        
        # Add a delay between queries
        if query != test_queries[-1]:
            print("Waiting 5 seconds before next query...")
            time.sleep(5)

if __name__ == "__main__":
    main() 