"""
<PERSON><PERSON><PERSON> tra PyTorch CUDA.
"""

import torch

print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")

if torch.cuda.is_available():
    print(f"CUDA device count: {torch.cuda.device_count()}")
    print(f"CUDA device name: {torch.cuda.get_device_name(0)}")
    
    # Tạo tensor trên CPU
    x = torch.tensor([1.0, 2.0, 3.0])
    print(f"Tensor on CPU: {x}")
    
    # Chuyển tensor sang GPU
    x_cuda = x.cuda()
    print(f"Tensor on GPU: {x_cuda}")
    
    # Thực hiện phép tính trên GPU
    y_cuda = x_cuda * 2
    print(f"Result on GPU: {y_cuda}")
    
    # Chuyển kết quả về CPU
    y = y_cuda.cpu()
    print(f"Result on CPU: {y}")
else:
    print("CUDA không khả dụng")
