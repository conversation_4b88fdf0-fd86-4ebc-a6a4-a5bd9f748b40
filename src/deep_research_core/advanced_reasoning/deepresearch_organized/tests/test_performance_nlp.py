"""
Test script để kiểm tra tích hợp NLPEngine và PerformanceOptimizer vào WebSearchAgentLocal.
"""

import time
import logging
import sys
from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal

# C<PERSON><PERSON> hình logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def test_search_with_nlp_and_performance_optimization():
    """
    Kiểm tra tìm kiếm với NLPEngine và PerformanceOptimizer.
    """
    # Khởi tạo WebSearchAgentLocal với NLPEngine và PerformanceOptimizer
    agent = WebSearchAgentLocal(
        enable_nlp=True,
        enable_performance_optimization=True,
        verbose=True
    )

    # Kiểm tra xem NLPEngine và PerformanceOptimizer đã được tích hợp chưa
    logger.info(f"NLP enabled: {hasattr(agent, 'use_nlp') and agent.use_nlp}")
    logger.info(f"Performance optimization enabled: {hasattr(agent, 'optimize_performance') and agent.optimize_performance}")

    # Danh sách các truy vấn để kiểm tra
    queries = [
        "Python programming",
        "Machine learning algorithms",
        "Web development frameworks",
        "Data science tools",
        "Artificial intelligence applications"
    ]

    # Thực hiện tìm kiếm cho mỗi truy vấn
    for query in queries:
        logger.info(f"Searching for: {query}")

        # Đo thời gian thực hiện
        start_time = time.time()

        # Thực hiện tìm kiếm
        try:
            # Sử dụng phương thức _search_searxng trực tiếp để tránh đệ quy vô hạn
            results = agent._search_searxng(
                query=query,
                num_results=3,
                get_content=True
            )
        except Exception as e:
            logger.error(f"Error during search: {str(e)}")
            results = {"success": False, "error": str(e), "results": []}

        # Tính thời gian thực hiện
        elapsed_time = time.time() - start_time

        # Hiển thị kết quả
        logger.info(f"Found {len(results.get('results', []))} results in {elapsed_time:.2f} seconds")

        # Hiển thị thông tin về kết quả
        if results.get('success', False):
            for i, result in enumerate(results.get('results', []), 1):
                logger.info(f"Result {i}: {result.get('title', 'No title')} - {result.get('url', 'No URL')}")
        else:
            logger.error(f"Search failed: {results.get('error', 'Unknown error')}")

        # Hiển thị thông tin về hiệu suất
        if hasattr(agent, 'performance_optimizer'):
            # Kiểm tra xem có phương thức get_performance_stats hay không
            if hasattr(agent.performance_optimizer, 'get_performance_stats'):
                stats = agent.performance_optimizer.get_performance_stats()
                logger.info(f"Performance stats: {stats}")
            else:
                # Hiển thị các thuộc tính có sẵn
                logger.info(f"Performance optimizer available: {agent.performance_optimizer}")

        # Tạm dừng giữa các truy vấn
        time.sleep(1)

if __name__ == "__main__":
    test_search_with_nlp_and_performance_optimization()
