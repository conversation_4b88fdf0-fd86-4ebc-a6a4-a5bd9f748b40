#!/usr/bin/env python3
"""
Test for Crossref to OpenAlex fallback mechanism
"""

from src.deep_research_core.agents.web_search_agent import WebSearchAgent

def test_fallback():
    """Test the fallback mechanism from Crossref to OpenAlex."""
    print("\n=== Testing Crossref to OpenAlex Fallback ===\n")
    
    # Create a mock function that always fails
    def mock_failed_crossref(*args, **kwargs):
        print("Mock Crossref search failure triggered!")
        return {
            "success": False,
            "error": "Simulated Crossref failure for testing fallback mechanism",
            "results": []
        }
    
    # Create the agent
    agent = WebSearchAgent(
        search_method="api",
        api_search_config={"engine": "academic_combined"},
        verbose=True
    )
    
    # Save the original method
    original_search_crossref = agent._search_crossref
    
    try:
        # Replace the method with our mock
        agent._search_crossref = mock_failed_crossref
        
        print("\n--- Testing with Crossref failure ---")
        query = "machine learning transformers 2023"
        # Force refresh to avoid using cached results
        result = agent.search(query, num_results=3, force_refresh=True)
        
        # Check if OpenAlex was used as fallback
        openalex_used = result.get("success", False) and "openalex" in result.get("sources", [])
        print(f"Success: {result.get('success', False)}")
        print(f"Sources used: {result.get('sources', [])}")
        print(f"Results count: {len(result.get('results', []))}")
        print(f"Detailed errors: {result.get('detailed_errors', {})}")
        print(f"Errors: {result.get('errors', {})}")
        
        print(f"Fallback to OpenAlex used: {openalex_used}")
        
        # Print some results if available
        if result.get("success", False) and len(result.get("results", [])) > 0:
            for i, r in enumerate(result.get("results", [])[:2]):
                print(f"\nResult {i+1}:")
                print(f"Title: {r.get('title', '')}")
                print(f"Source: {r.get('source', '')}")
                print(f"URL: {r.get('url', '')}")
                print(f"Published year: {r.get('year', '')}")
        
        return {
            "success": result.get("success", False),
            "fallback_successful": openalex_used
        }
    finally:
        # Restore original method
        agent._search_crossref = original_search_crossref

if __name__ == "__main__":
    test_fallback() 