#!/usr/bin/env python3
"""
Test for WebSearchAgent's decide_search_method
"""

from src.deep_research_core.agents.web_search_agent import WebSearchAgent

def main():
    # Initialize WebSearchAgent with auto method
    agent = WebSearchAgent(search_method='auto')
    
    # Test with different queries
    test_queries = [
        ("Python programming tutorial", "en"),
        ("How to learn Python programming", "en"),
        ("Machine learning frameworks comparison", "en"),
        ("Hướng dẫn lập trình <PERSON> cơ bản", "vi"),
        ("Cách học lập trình Python", "vi"),
        ("So sánh các framework học máy", "vi")
    ]
    
    print("\n--- Testing decide_search_method with different queries ---")
    
    for query, language in test_queries:
        # Create a fake analysis with the language
        analysis = {
            "language": language,
            "complexity_score": 0.5,
            "query_type": "informational",
            "requires_detail": False,
            "requires_recency": False
        }
        
        # Test with different complexity scores
        for complexity in [0.3, 0.6, 0.8]:
            analysis["complexity_score"] = complexity
            method = agent.decide_search_method(query, analysis)
            print(f"Query: '{query}' (lang: {language}, complexity: {complexity:.1f}) -> Method: {method}")
        
        # Test with requires_detail
        analysis["complexity_score"] = 0.5
        analysis["requires_detail"] = True
        method = agent.decide_search_method(query, analysis)
        print(f"Query: '{query}' (lang: {language}, requires_detail: True) -> Method: {method}")
        
        print()

if __name__ == "__main__":
    main()
