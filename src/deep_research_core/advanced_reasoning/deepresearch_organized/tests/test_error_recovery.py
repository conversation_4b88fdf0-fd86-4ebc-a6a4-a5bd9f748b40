"""
Test script for error recovery.
"""

import sys
import os
import time

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), "src"))

from deep_research_core.utils.error_recovery import (
    ErrorRecoveryStrategy, RetryStrategy, AlternativeToolStrategy,
    InputReformulationStrategy, FallbackResultStrategy, ErrorRecoveryManager
)
from deep_research_core.utils.advanced_recovery_strategies import (
    ContextAwareReformulationStrategy, ToolParameterAdjustmentStrategy,
    AdaptiveRecoveryStrategy
)

def test_retry_strategy():
    """Test the retry strategy."""
    print("Testing retry strategy...")
    
    # Create a test error
    test_error = ValueError("Test error")
    
    # Create a context
    context = {
        "tool_name": "test_tool",
        "tool_args": {"param": "value"},
        "retry_count": 0
    }
    
    # Create a retry strategy
    strategy = RetryStrategy(max_retries=3, initial_delay=0.01)
    
    # Check that the strategy can handle the error
    can_handle = strategy.can_handle(test_error, context)
    print(f"Can handle: {can_handle}")
    
    # Recover from the error
    result = strategy.recover(test_error, context)
    
    # Check the result
    print(f"Success: {result['success']}")
    print(f"Strategy: {result['strategy']}")
    print(f"Retry count: {context['retry_count']}")
    
    # Try again with retry_count at the limit
    context["retry_count"] = 3
    can_handle = strategy.can_handle(test_error, context)
    print(f"Can handle with retry_count at limit: {can_handle}")
    
    print("Retry strategy test completed.")

def test_context_aware_reformulation_strategy():
    """Test the context-aware reformulation strategy."""
    print("\nTesting context-aware reformulation strategy...")
    
    # Create a test error
    test_error = ValueError("Invalid query format")
    
    # Create a context
    context = {
        "tool_name": "web_search",
        "tool_args": {"query": "test query"},
        "context_reformulation_attempted": False
    }
    
    # Create a context-aware reformulation strategy
    strategy = ContextAwareReformulationStrategy()
    
    # Check that the strategy can handle the error
    can_handle = strategy.can_handle(test_error, context)
    print(f"Can handle: {can_handle}")
    
    # Recover from the error
    result = strategy.recover(test_error, context)
    
    # Check the result
    print(f"Success: {result['success']}")
    print(f"Strategy: {result['strategy']}")
    print(f"Reformulated args: {result['reformulated_args']}")
    print(f"Context reformulation attempted: {context['context_reformulation_attempted']}")
    
    # Try again with reformulation already attempted
    context["context_reformulation_attempted"] = True
    can_handle = strategy.can_handle(test_error, context)
    print(f"Can handle with reformulation already attempted: {can_handle}")
    
    print("Context-aware reformulation strategy test completed.")

def test_tool_parameter_adjustment_strategy():
    """Test the tool parameter adjustment strategy."""
    print("\nTesting tool parameter adjustment strategy...")
    
    # Create a test error
    test_error = TimeoutError("Request timed out")
    
    # Create a context
    context = {
        "tool_name": "api_request",
        "tool_args": {"timeout": 10},
        "parameter_adjustment_attempted": False
    }
    
    # Create a tool parameter adjustment strategy
    strategy = ToolParameterAdjustmentStrategy()
    
    # Check that the strategy can handle the error
    can_handle = strategy.can_handle(test_error, context)
    print(f"Can handle: {can_handle}")
    
    # Recover from the error
    result = strategy.recover(test_error, context)
    
    # Check the result
    print(f"Success: {result['success']}")
    print(f"Strategy: {result['strategy']}")
    print(f"Adjusted args: {result['adjusted_args']}")
    print(f"Parameter adjustment attempted: {context['parameter_adjustment_attempted']}")
    
    # Try again with parameter adjustment already attempted
    context["parameter_adjustment_attempted"] = True
    can_handle = strategy.can_handle(test_error, context)
    print(f"Can handle with parameter adjustment already attempted: {can_handle}")
    
    print("Tool parameter adjustment strategy test completed.")

def main():
    """Run the tests."""
    test_retry_strategy()
    test_context_aware_reformulation_strategy()
    test_tool_parameter_adjustment_strategy()

if __name__ == "__main__":
    main()
