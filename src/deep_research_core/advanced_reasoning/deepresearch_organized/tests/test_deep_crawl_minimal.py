#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Minimal test script for _deep_crawl_improved method in WebSearchAgentLocal.
"""

import sys
import os
import logging
import time
from typing import Dict, List, Any, Optional

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Import WebSearchAgentLocal
try:
    from deepresearch.web_search_agent_local import WebSearchAgentLocal
except ImportError:
    try:
        from web_search_agent_local import WebSearchAgentLocal
    except ImportError:
        try:
            from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal
        except ImportError:
            logger.error("Could not import WebSearchAgentLocal")
            sys.exit(1)

def test_deep_crawl_minimal():
    """Test _deep_crawl_improved method with minimal dependencies."""
    print("\n=== Testing _deep_crawl_improved method with minimal dependencies ===\n")

    # Create a minimal WebSearchAgentLocal instance
    agent = WebSearchAgentLocal(verbose=True)

    # Test URL
    url = "https://www.python.org/"
    print(f"Testing URL: {url}")
    
    try:
        # Call _crawl_with_requests method directly
        start_time = time.time()
        result = agent._crawl_with_requests(url, timeout=30, include_html=False)
        end_time = time.time()
        
        # Print results
        print(f"Success: {result.get('success', False)}")
        print(f"Time: {end_time - start_time:.2f} seconds")
        
        if result.get('success', False):
            print(f"Title: {result.get('title', '')}")
            content = result.get('text', '')
            print(f"Content: {content[:100]}..." if content and len(content) > 100 else f"Content: {content}")
            
            # Print metadata
            metadata = result.get('metadata', {})
            print(f"Metadata: {metadata}")
            
            # Print links
            links = result.get('links', [])
            print(f"Number of links: {len(links)}")
            if links and len(links) > 0:
                print(f"First link: {links[0]}")
        else:
            print(f"Error: {result.get('error', 'Unknown error')}")
    except Exception as e:
        print(f"Error: {str(e)}")

    # Test PDF URL
    url = "https://thuvienphapluat.vn/van-ban/Thuong-mai/Nghi-dinh-98-2020-ND-CP-xu-phat-vi-pham-hanh-chinh-hoat-dong-thuong-mai-bao-ve-nguoi-tieu-dung-450755.pdf"
    print(f"\nTesting PDF URL: {url}")
    
    try:
        # Call _extract_pdf_info method directly
        start_time = time.time()
        result = agent._extract_pdf_info(url, timeout=30)
        end_time = time.time()
        
        # Print results
        print(f"Success: {result.get('success', False)}")
        print(f"Time: {end_time - start_time:.2f} seconds")
        
        if result.get('success', False):
            print(f"Title: {result.get('title', '')}")
            content = result.get('text', '')
            print(f"Content: {content[:100]}..." if content and len(content) > 100 else f"Content: {content}")
            
            # Print metadata
            metadata = result.get('metadata', {})
            print(f"Metadata: {metadata}")
        else:
            print(f"Error: {result.get('error', 'Unknown error')}")
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    test_deep_crawl_minimal()
