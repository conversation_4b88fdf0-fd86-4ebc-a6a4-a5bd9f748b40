#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for deep crawler with file download functionality.
"""

import os
import sys
import time
import logging
import shutil
import requests
from typing import Dict, Any, List, Optional
from urllib.parse import urlparse, urljoin

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import necessary modules directly to avoid circular imports
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import BeautifulSoup directly
try:
    from bs4 import BeautifulSoup
except ImportError:
    logger.error("BeautifulSoup is not installed. Please install it with 'pip install beautifulsoup4'")
    sys.exit(1)

# Import Playwright directly
try:
    from playwright.sync_api import sync_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    logger.warning("Playwright is not installed. Some features may not work.")
    PLAYWRIGHT_AVAILABLE = False

# Import SimpleFileProcessor from test_file_download_direct.py
from test_file_download_direct import SimpleFileProcessor

class SimpleDeepCrawler:
    """
    Simple deep crawler for testing.
    """

    def __init__(
        self,
        user_agent: Optional[str] = None,
        download_dir: str = "test_downloads",
        max_file_size: int = 10 * 1024 * 1024,  # 10MB
        timeout: int = 30
    ):
        """
        Initialize SimpleDeepCrawler.

        Args:
            user_agent: User-Agent to use
            download_dir: Directory to save downloaded files
            max_file_size: Maximum file size (bytes)
            timeout: Maximum timeout (seconds)
        """
        self.user_agent = user_agent or "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36"
        self.download_dir = download_dir
        self.max_file_size = max_file_size
        self.timeout = timeout

        # Create download directory if it doesn't exist
        if not os.path.exists(self.download_dir):
            os.makedirs(self.download_dir)

        # Initialize file processor
        self.file_processor = SimpleFileProcessor(
            download_dir=self.download_dir,
            max_file_size=self.max_file_size,
            timeout=self.timeout,
            user_agent=self.user_agent
        )

    def deep_crawl(
        self,
        url: str,
        max_depth: int = 2,
        max_pages: int = 5,
        download_files: bool = False
    ) -> Dict[str, Any]:
        """
        Crawl deeply into a website to extract content.

        Args:
            url: URL to crawl
            max_depth: Maximum depth to crawl
            max_pages: Maximum pages to crawl
            download_files: Whether to download files

        Returns:
            Dictionary containing extracted content
        """
        if not PLAYWRIGHT_AVAILABLE:
            logger.error("Playwright is required for deep crawling")
            return {"success": False, "error": "Playwright is not available"}

        logger.info(f"Starting deep crawl of {url} with max_depth={max_depth}, max_pages={max_pages}")

        # Initialize result
        result = {
            "success": False,
            "url": url,
            "pages": [],
            "downloaded_files": [] if download_files else None
        }

        # Track visited URLs and crawl count
        visited_urls = set()
        crawled_count = 0

        # URLs to crawl, with their depth
        urls_to_crawl = [(url, 0)]  # (url, depth)

        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            context = browser.new_context(
                user_agent=self.user_agent
            )

            while urls_to_crawl and crawled_count < max_pages:
                current_url, current_depth = urls_to_crawl.pop(0)

                # Skip if already visited or depth exceeded
                if current_url in visited_urls or current_depth > max_depth:
                    continue

                logger.info(f"Crawling {current_url} (depth: {current_depth})")

                try:
                    # Create a new page
                    page = context.new_page()

                    # Navigate to URL
                    response = page.goto(current_url, timeout=self.timeout * 1000, wait_until="networkidle")

                    # Skip if navigation failed
                    if not response or response.status >= 400:
                        logger.warning(f"Failed to load {current_url}: HTTP {response.status if response else 'unknown'}")
                        page.close()
                        continue

                    # Get page content
                    content = page.content()

                    # Parse with BeautifulSoup
                    soup = BeautifulSoup(content, "html.parser")

                    # Extract title and text
                    title = soup.title.string if soup.title else ""
                    text = " ".join([p.get_text() for p in soup.find_all("p")])

                    # Add to result
                    result["pages"].append({
                        "url": current_url,
                        "title": title,
                        "text": text[:1000] + "..." if len(text) > 1000 else text,
                        "depth": current_depth
                    })

                    # If downloading files is enabled, find and download files
                    if download_files:
                        # Find links to files
                        file_links = []
                        for a_tag in soup.find_all("a", href=True):
                            href = a_tag.get("href", "").strip()
                            if href and not href.startswith(("#", "javascript:", "mailto:", "tel:")):
                                # Convert relative URL to absolute
                                file_url = urljoin(current_url, href)
                                # Check if it's a file
                                if self.file_processor.is_file_url(file_url):
                                    file_links.append(file_url)

                        # Download files
                        for file_url in file_links[:3]:  # Limit to 3 files per page for testing
                            try:
                                file_info = self.file_processor.download_file(file_url)
                                if file_info["success"]:
                                    result["downloaded_files"].append(file_info)
                                    logger.info(f"Downloaded file: {file_info['file_name']} ({file_info['size']} bytes)")
                            except Exception as e:
                                logger.warning(f"Error downloading file from {file_url}: {str(e)}")

                    # If depth not exceeded, find links to crawl
                    if current_depth < max_depth:
                        # Find all links
                        links = []
                        for a_tag in soup.find_all("a", href=True):
                            href = a_tag.get("href", "").strip()
                            if href and not href.startswith(("#", "javascript:", "mailto:", "tel:")):
                                # Convert relative URL to absolute
                                absolute_url = urljoin(current_url, href)

                                # Only add links from the same domain
                                if urlparse(absolute_url).netloc == urlparse(current_url).netloc:
                                    links.append(absolute_url)

                        # Add links to crawl queue
                        for link in links:
                            if link not in visited_urls:
                                urls_to_crawl.append((link, current_depth + 1))

                    # Mark as visited and increment count
                    visited_urls.add(current_url)
                    crawled_count += 1

                    # Close page
                    page.close()

                    # Add delay to avoid being blocked
                    time.sleep(1)

                except Exception as e:
                    logger.warning(f"Error crawling {current_url}: {str(e)}")
                    continue

            # Close browser
            browser.close()

        # Update result
        result["success"] = True
        result["crawled_count"] = crawled_count
        result["visited_urls"] = list(visited_urls)

        return result

def main():
    """
    Main function to test deep crawler with file download.
    """
    # Create a temporary directory for downloaded files
    download_dir = "test_downloads"
    if not os.path.exists(download_dir):
        os.makedirs(download_dir)

    try:
        # Initialize SimpleDeepCrawler
        crawler = SimpleDeepCrawler(
            download_dir=download_dir
        )

        # Test URLs with files
        test_urls = [
            "https://www.w3.org/TR/2021/WD-webgpu-20210608/",  # W3C page with links to PDFs
            "https://www.adobe.com/acrobat/resources.html",    # Adobe page with PDF resources
            "https://www.pdfa.org/resource/isartor-test-suite/", # PDF/A test suite
        ]

        for url in test_urls:
            logger.info(f"Testing deep crawl with file download for URL: {url}")

            # Perform deep crawl
            result = crawler.deep_crawl(
                url=url,
                max_depth=1,
                max_pages=3,
                download_files=True
            )

            # Check result
            assert result["success"], "Deep crawl should be successful"

            # Log crawled pages
            logger.info(f"Crawled {len(result['pages'])} pages:")
            for page in result["pages"]:
                logger.info(f"  - {page['url']} (depth: {page['depth']})")

            # Check downloaded files
            if "downloaded_files" in result and result["downloaded_files"]:
                logger.info(f"Downloaded {len(result['downloaded_files'])} files:")
                for file_info in result["downloaded_files"]:
                    logger.info(f"  - {file_info['file_name']} ({file_info['size']} bytes)")

                    # Check file exists
                    assert os.path.exists(file_info["file_path"]), f"File {file_info['file_name']} should exist on disk"
            else:
                logger.info("No files downloaded")

        logger.info("All tests passed!")
        return True

    except Exception as e:
        logger.error(f"Error: {str(e)}")
        return False

    finally:
        # Clean up
        if os.path.exists(download_dir):
            shutil.rmtree(download_dir)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
