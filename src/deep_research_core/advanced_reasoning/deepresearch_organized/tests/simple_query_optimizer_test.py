#!/usr/bin/env python3
"""
Test script đơn giản để kiểm tra tính năng tối ưu hóa truy vấn.
"""

import sys
import os
import json
import numpy as np

# Mô phỏng QueryOptimizer để kiểm tra
class QueryOptimizer:
    """
    Lớp tối ưu hóa truy vấn tìm kiếm.
    
    Sử dụng kết hợp Sentence Embeddings và Domain Knowledge Graph để phân tích
    và tạo các biến thể truy vấn tối ưu.
    """
    
    def __init__(
        self,
        language="auto",
        max_variants=5
    ):
        """
        Khởi tạo QueryOptimizer.
        """
        self.language = language
        self.max_variants = max_variants
        
        # Tải stopwords
        self.stopwords = self._load_stopwords(language)
    
    def _load_stopwords(self, language):
        """
        Tải danh sách stopwords theo ngôn ngữ.
        """
        if language == "vi":
            return ["và", "hoặc", "những", "các", "là", "của", "có", "trong", "cho", "với", "về", 
                    "như", "không", "được", "đã", "sẽ", "đang", "rằng", "thì", "mà", "nên", "khi"]
        else:
            return ["and", "or", "the", "is", "of", "in", "for", "with", "about", "to", "that", 
                    "this", "these", "those", "it", "they", "he", "she", "we", "you", "as", "at"]
    
    def optimize(self, query, language=None):
        """
        Phân tích và tối ưu hóa truy vấn.
        """
        # Phát hiện ngôn ngữ nếu cần
        detected_language = self._detect_language(query, language)
        
        # Trích xuất từ khóa
        keywords = self._extract_keywords(query, detected_language)
        
        # Phát hiện lĩnh vực
        domains = self._detect_domains(keywords, query)
        
        # Tạo biến thể truy vấn
        variants = self._generate_variants(query, keywords, domains, detected_language)
        
        # Tạo kết quả
        result = {
            "original_query": query,
            "language": detected_language,
            "keywords": keywords,
            "domains": domains,
            "variants": variants,
            "processing_time": 0.1  # Giả lập thời gian xử lý
        }
        
        return result
    
    def _detect_language(self, query, language=None):
        """
        Phát hiện ngôn ngữ của truy vấn.
        """
        if language and language != "auto":
            return language
        
        if self.language != "auto":
            return self.language
        
        # Kiểm tra các ký tự tiếng Việt
        if any(c in query for c in "áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđ"):
            return "vi"
        
        # Mặc định là tiếng Anh
        return "en"
    
    def _extract_keywords(self, query, language):
        """
        Trích xuất từ khóa từ truy vấn.
        """
        # Chuyển về chữ thường
        query_lower = query.lower()
        
        # Loại bỏ dấu câu
        query_clean = query_lower.replace(".", " ").replace(",", " ").replace("?", " ").replace("!", " ")
        
        # Tách từ
        words = query_clean.split()
        
        # Loại bỏ stopwords
        stopwords = self._load_stopwords(language)
        keywords = [word for word in words if word not in stopwords]
        
        # Loại bỏ từ quá ngắn
        keywords = [keyword for keyword in keywords if len(keyword) > 2]
        
        return keywords
    
    def _detect_domains(self, keywords, query):
        """
        Phát hiện lĩnh vực của truy vấn.
        """
        domains = []
        query_lower = query.lower()
        
        # Kiểm tra từ khóa AI
        ai_keywords = ["ai", "artificial intelligence", "trí tuệ nhân tạo", "machine learning", "học máy"]
        if any(kw in query_lower for kw in ai_keywords):
            domains.append("AI")
        
        # Kiểm tra từ khóa công nghệ
        tech_keywords = ["technology", "công nghệ", "tech", "software", "phần mềm", "hardware", "phần cứng"]
        if any(kw in query_lower for kw in tech_keywords):
            domains.append("technology")
        
        # Kiểm tra từ khóa xu hướng
        trend_keywords = ["trend", "xu hướng", "latest", "mới nhất", "future", "tương lai"]
        if any(kw in query_lower for kw in trend_keywords):
            domains.append("trend")
        
        # Kiểm tra từ khóa thời gian
        time_keywords = ["2024", "2025", "năm 2024", "năm 2025"]
        if any(kw in query_lower for kw in time_keywords):
            domains.append("time")
        
        return domains
    
    def _generate_variants(self, query, keywords, domains, language):
        """
        Tạo biến thể truy vấn.
        """
        variants = []
        
        # Biến thể 1: Kết hợp các từ khóa chính
        if len(keywords) >= 3:
            variants.append(" ".join(keywords[:3]))
        
        # Biến thể 2: Thêm từ khóa thời gian
        if "time" not in domains:
            if language == "vi":
                variants.append(f"{query} 2024")
            else:
                variants.append(f"{query} 2024")
        
        # Biến thể 3: Thêm từ khóa "mới nhất"
        if "trend" not in domains:
            if language == "vi" and "mới nhất" not in query.lower():
                variants.append(f"{query} mới nhất")
            elif language == "en" and "latest" not in query.lower():
                variants.append(f"{query} latest")
        
        # Biến thể 4: Tạo truy vấn dạng câu hỏi
        if "?" not in query:
            if language == "vi":
                variants.append(f"Những {' '.join(keywords[:3])} là gì?")
            else:
                variants.append(f"What are {' '.join(keywords[:3])}?")
        
        # Biến thể 5: Kết hợp lĩnh vực
        if "AI" in domains and "trend" in domains:
            if language == "vi":
                variants.append(f"xu hướng trí tuệ nhân tạo mới nhất 2024")
            else:
                variants.append(f"latest AI trends 2024")
        
        return variants[:self.max_variants]

def test_query_optimizer():
    """
    Kiểm tra tính năng tối ưu hóa truy vấn.
    """
    print("\n=== Kiểm tra QueryOptimizer ===\n")
    
    # Danh sách các truy vấn để kiểm tra
    test_queries = [
        "Những xu hướng AI mới nhất trong năm 2025?",
        "Python programming for beginners",
        "Cách học tiếng Anh hiệu quả",
        "Tác động của biến đổi khí hậu đến nông nghiệp Việt Nam",
        "Best machine learning frameworks 2024"
    ]
    
    # Khởi tạo QueryOptimizer
    optimizer = QueryOptimizer()
    
    # Kiểm tra từng truy vấn
    for query in test_queries:
        print(f"\nTruy vấn gốc: {query}")
        
        # Phát hiện ngôn ngữ
        language = "en"
        if any(c in query for c in "áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđ"):
            language = "vi"
        
        print(f"Ngôn ngữ: {language}")
        
        # Tối ưu hóa truy vấn
        try:
            result = optimizer.optimize(query, language)
            
            # In kết quả
            print(f"Từ khóa: {', '.join(result.get('keywords', []))}")
            print(f"Lĩnh vực: {', '.join(result.get('domains', []))}")
            
            print("Biến thể truy vấn:")
            for i, variant in enumerate(result.get("variants", []), 1):
                print(f"  {i}. {variant}")
            
            print(f"Thời gian xử lý: {result.get('processing_time', 0):.4f} giây")
        except Exception as e:
            print(f"Lỗi khi tối ưu hóa truy vấn: {e}")

def main():
    """
    Hàm chính.
    """
    # Kiểm tra QueryOptimizer
    test_query_optimizer()

if __name__ == "__main__":
    main()
