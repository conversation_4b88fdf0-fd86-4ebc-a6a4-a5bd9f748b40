#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for SearXNG API.
"""

import requests
import json
import time
import sys

def search_searxng(
    query: str,
    num_results: int = 10,
    searxng_instance: str = "http://localhost:8080",
    language: str = "en",
    categories=None,
    time_range=None,
    safesearch: int = 0
):
    """
    Search the web using SearXNG API.
    """
    print(f"Searching with SearXNG instance: {searxng_instance}")
    print(f"Query: {query}")
    print(f"Language: {language}")
    print(f"Number of results: {num_results}")
    
    # Set up the headers
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
        "Accept": "application/json",
        "Accept-Language": f"{language},en-US;q=0.9,en;q=0.8",
        "Referer": "https://www.google.com/",
        "DNT": "1",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1"
    }

    # Build the query parameters
    params = {
        "q": query,
        "format": "json",
        "language": language,
        "results": num_results,
        "safesearch": safesearch
    }

    if categories:
        params["categories"] = ",".join(categories) if isinstance(categories, list) else categories

    if time_range:
        params["time_range"] = time_range

    try:
        # Make the request
        print(f"Making request to {searxng_instance}/search with params: {params}")
        response = requests.get(
            f"{searxng_instance}/search",
            params=params,
            headers=headers,
            timeout=30
        )
        
        print(f"Response status code: {response.status_code}")
        
        # Check if the request was successful
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"Response contains {len(data.get('results', []))} results")
                
                # Print the first result if available
                if data.get("results"):
                    first_result = data["results"][0]
                    print("\nFirst result:")
                    print(f"Title: {first_result.get('title', 'N/A')}")
                    print(f"URL: {first_result.get('url', 'N/A')}")
                    print(f"Content: {first_result.get('content', 'N/A')[:100]}...")
                
                return {
                    "success": True,
                    "search_method": "api",
                    "engine": "searxng",
                    "query": query,
                    "results": data.get("results", []),
                    "count": len(data.get("results", []))
                }
            except json.JSONDecodeError as e:
                print(f"Error decoding JSON: {str(e)}")
                print(f"Response text: {response.text[:500]}...")
                return {
                    "success": False,
                    "search_method": "api",
                    "engine": "searxng",
                    "query": query,
                    "error": f"Error decoding JSON: {str(e)}",
                    "results": []
                }
        else:
            print(f"Error: HTTP status code {response.status_code}")
            print(f"Response text: {response.text[:500]}...")
            return {
                "success": False,
                "search_method": "api",
                "engine": "searxng",
                "query": query,
                "error": f"HTTP error: {response.status_code}",
                "results": []
            }
    except requests.RequestException as e:
        print(f"Request error: {str(e)}")
        return {
            "success": False,
            "search_method": "api",
            "engine": "searxng",
            "query": query,
            "error": f"Request error: {str(e)}",
            "results": []
        }

def main():
    """Main function."""
    # Default query
    query = "Python programming"
    
    # Use command line argument if provided
    if len(sys.argv) > 1:
        query = sys.argv[1]
    
    # Test with local SearXNG instance
    result = search_searxng(
        query=query,
        num_results=5,
        searxng_instance="http://localhost:8080",
        language="en"
    )
    
    print("\nSearch result summary:")
    print(f"Success: {result.get('success', False)}")
    print(f"Number of results: {result.get('count', 0)}")
    print(f"Error: {result.get('error', 'None')}")

if __name__ == "__main__":
    main()
