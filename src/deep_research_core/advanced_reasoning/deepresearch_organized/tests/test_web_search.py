#!/usr/bin/env python3
"""
Script kiểm tra các tính năng mới của WebSearchAgentLocal.
"""

import os
import sys
import json
from pprint import pprint

# Thêm thư mục gốc vào sys.path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import WebSearchAgentLocal
from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal
from src.deep_research_core.agents.question_complexity_evaluator import QuestionComplexityEvaluator
from src.deep_research_core.agents.answer_quality_evaluator import AnswerQualityEvaluator

def test_question_complexity_evaluator():
    """Kiểm tra QuestionComplexityEvaluator."""
    print("\n=== Kiểm tra QuestionComplexityEvaluator ===")
    
    evaluator = QuestionComplexityEvaluator()
    
    # Kiểm tra với câu hỏi đơn giản
    simple_query = "Thời tiết H<PERSON> hôm nay"
    simple_result = evaluator.evaluate_complexity(simple_query)
    print(f"Câu hỏi đơn giản: {simple_query}")
    print(f"Độ phức tạp: {simple_result['complexity_level']} (score: {simple_result['complexity_score']:.2f})")
    print(f"Chiến lược đề xuất: {json.dumps(simple_result['recommended_strategy'], indent=2)}")
    
    # Kiểm tra với câu hỏi trung bình
    medium_query = "Cách làm bánh trung thu nhân thập cẩm"
    medium_result = evaluator.evaluate_complexity(medium_query)
    print(f"\nCâu hỏi trung bình: {medium_query}")
    print(f"Độ phức tạp: {medium_result['complexity_level']} (score: {medium_result['complexity_score']:.2f})")
    print(f"Chiến lược đề xuất: {json.dumps(medium_result['recommended_strategy'], indent=2)}")
    
    # Kiểm tra với câu hỏi phức tạp
    complex_query = "So sánh tác động của biến đổi khí hậu đến nông nghiệp ở các vùng miền khác nhau của Việt Nam và đề xuất giải pháp thích ứng"
    complex_result = evaluator.evaluate_complexity(complex_query)
    print(f"\nCâu hỏi phức tạp: {complex_query}")
    print(f"Độ phức tạp: {complex_result['complexity_level']} (score: {complex_result['complexity_score']:.2f})")
    print(f"Chiến lược đề xuất: {json.dumps(complex_result['recommended_strategy'], indent=2)}")

def test_answer_quality_evaluator():
    """Kiểm tra AnswerQualityEvaluator."""
    print("\n=== Kiểm tra AnswerQualityEvaluator ===")
    
    evaluator = AnswerQualityEvaluator()
    
    # Kiểm tra với câu trả lời tốt
    good_question = "Thủ đô của Việt Nam là gì?"
    good_answer = "Thủ đô của Việt Nam là Hà Nội. Hà Nội là trung tâm chính trị, văn hóa và kinh tế của Việt Nam. Thành phố này có lịch sử lâu đời với nhiều di tích lịch sử và văn hóa quan trọng."
    good_documents = [{"url": "https://example.com/vietnam", "title": "Việt Nam", "content": "Việt Nam có thủ đô là Hà Nội, nằm ở phía bắc đất nước."}]
    
    good_result = evaluator.evaluate_answer(good_question, good_answer, good_documents)
    print(f"Câu hỏi: {good_question}")
    print(f"Câu trả lời: {good_answer[:50]}...")
    print(f"Điểm chất lượng: {good_result['overall_score']:.2f}")
    print(f"Cần tìm kiếm thêm: {good_result['need_more_search']}")
    
    # Kiểm tra với câu trả lời kém
    bad_question = "Giải thích chi tiết về cách hoạt động của động cơ điện"
    bad_answer = "Động cơ điện là một thiết bị điện."
    bad_documents = [{"url": "https://example.com/motor", "title": "Động cơ điện", "content": "Động cơ điện là một thiết bị điện."}]
    
    bad_result = evaluator.evaluate_answer(bad_question, bad_answer, bad_documents)
    print(f"\nCâu hỏi: {bad_question}")
    print(f"Câu trả lời: {bad_answer}")
    print(f"Điểm chất lượng: {bad_result['overall_score']:.2f}")
    print(f"Cần tìm kiếm thêm: {bad_result['need_more_search']}")

def test_web_search_agent():
    """Kiểm tra WebSearchAgentLocal."""
    print("\n=== Kiểm tra WebSearchAgentLocal ===")
    
    agent = WebSearchAgentLocal()
    
    # Kiểm tra với câu hỏi đơn giản
    print("\nTìm kiếm với câu hỏi đơn giản:")
    simple_query = "Thời tiết Hà Nội hôm nay"
    simple_results = agent.search(
        query=simple_query,
        evaluate_question=True,
        evaluate_answer=True,
        auto_deep_crawl=True,
        get_content=True
    )
    
    print(f"Câu hỏi: {simple_query}")
    if "question_evaluation" in simple_results:
        qe = simple_results["question_evaluation"]
        print(f"Độ phức tạp: {qe['complexity_level']} (score: {qe['complexity_score']:.2f})")
    
    if "answer_evaluation" in simple_results:
        ae = simple_results["answer_evaluation"]
        print(f"Điểm chất lượng câu trả lời: {ae['overall_score']:.2f}")
        print(f"Cần tìm kiếm thêm: {ae['need_more_search']}")
    
    if "deep_crawl_stats" in simple_results:
        print(f"Deep crawl stats: {simple_results['deep_crawl_stats']}")
    
    # Kiểm tra với câu hỏi phức tạp
    print("\nTìm kiếm với câu hỏi phức tạp:")
    complex_query = "So sánh tác động của biến đổi khí hậu đến nông nghiệp ở các vùng miền khác nhau của Việt Nam"
    complex_results = agent.search(
        query=complex_query,
        evaluate_question=True,
        evaluate_answer=True,
        auto_deep_crawl=True,
        get_content=True
    )
    
    print(f"Câu hỏi: {complex_query}")
    if "question_evaluation" in complex_results:
        qe = complex_results["question_evaluation"]
        print(f"Độ phức tạp: {qe['complexity_level']} (score: {qe['complexity_score']:.2f})")
    
    if "answer_evaluation" in complex_results:
        ae = complex_results["answer_evaluation"]
        print(f"Điểm chất lượng câu trả lời: {ae['overall_score']:.2f}")
        print(f"Cần tìm kiếm thêm: {ae['need_more_search']}")
    
    if "deep_crawl_stats" in complex_results:
        print(f"Deep crawl stats: {complex_results['deep_crawl_stats']}")

if __name__ == "__main__":
    # Kiểm tra QuestionComplexityEvaluator
    test_question_complexity_evaluator()
    
    # Kiểm tra AnswerQualityEvaluator
    test_answer_quality_evaluator()
    
    # Kiểm tra WebSearchAgentLocal
    test_web_search_agent()
