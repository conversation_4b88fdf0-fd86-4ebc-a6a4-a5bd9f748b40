#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for WebSearchAgentLocalEnhanced with 100 random requests.
"""

import sys
import os
import time
import random
import json
import argparse
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from tqdm import tqdm

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

# Import the WebSearchAgentLocalEnhanced class
from src.deep_research_core.agents.web_search_agent_local_enhanced import WebSearchAgentLocalEnhanced

# List of random search queries
SEARCH_QUERIES = [
    "Python programming tutorial",
    "Machine learning algorithms",
    "Data science projects",
    "Web development frameworks",
    "JavaScript libraries",
    "Cloud computing services",
    "DevOps best practices",
    "Artificial intelligence applications",
    "Blockchain technology",
    "Cybersecurity best practices",
    "Mobile app development",
    "Docker containers",
    "Kubernetes orchestration",
    "Big data analytics",
    "Internet of Things devices",
    "Augmented reality applications",
    "Virtual reality headsets",
    "Natural language processing",
    "Computer vision algorithms",
    "Quantum computing",
    "5G technology",
    "Renewable energy sources",
    "Electric vehicles",
    "Space exploration",
    "Climate change solutions",
    "Sustainable agriculture",
    "Genetic engineering",
    "Biotechnology advances",
    "Nanotechnology applications",
    "Robotics in healthcare",
    "3D printing technology",
    "Smart home devices",
    "Wearable technology",
    "Digital marketing strategies",
    "E-commerce platforms",
    "Social media algorithms",
    "Content management systems",
    "Search engine optimization",
    "User experience design",
    "Responsive web design",
    "Progressive web apps",
    "Serverless architecture",
    "Microservices architecture",
    "API development",
    "GraphQL vs REST",
    "NoSQL databases",
    "SQL optimization",
    "Data visualization tools",
    "Business intelligence software",
    "Project management methodologies"
]

def generate_random_queries(base_queries, count=100):
    """Generate random queries from base queries."""
    if count <= len(base_queries):
        return random.sample(base_queries, count)
    
    # If we need more queries than we have in the base list
    result = base_queries.copy()
    while len(result) < count:
        # Take a random query and add a random modifier
        base_query = random.choice(base_queries)
        modifiers = ["best", "tutorial", "example", "guide", "vs", "how to", "what is", "why", "when to use"]
        modifier = random.choice(modifiers)
        new_query = f"{modifier} {base_query}" if random.random() > 0.5 else f"{base_query} {modifier}"
        result.append(new_query)
    
    return result[:count]

def search_with_agent(agent, query, num_results=3):
    """Perform a search with the agent and return the results."""
    try:
        start_time = time.time()
        results = agent.search(query, num_results=num_results)
        end_time = time.time()
        
        return {
            "query": query,
            "success": results.get("success", False),
            "num_results": len(results.get("results", [])),
            "time_taken": end_time - start_time,
            "error": results.get("error", None)
        }
    except Exception as e:
        return {
            "query": query,
            "success": False,
            "num_results": 0,
            "time_taken": 0,
            "error": str(e)
        }

def run_sequential_test(agent, queries, num_results=3):
    """Run tests sequentially."""
    results = []
    
    for query in tqdm(queries, desc="Testing queries"):
        result = search_with_agent(agent, query, num_results)
        results.append(result)
        # Small delay to avoid overwhelming the server
        time.sleep(0.5)
    
    return results

def run_parallel_test(agent, queries, num_results=3, max_workers=5):
    """Run tests in parallel."""
    results = []
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_query = {executor.submit(search_with_agent, agent, query, num_results): query for query in queries}
        
        for future in tqdm(as_completed(future_to_query), total=len(queries), desc="Testing queries"):
            results.append(future.result())
    
    return results

def analyze_results(results):
    """Analyze the test results."""
    total = len(results)
    successful = sum(1 for r in results if r["success"])
    failed = total - successful
    
    if total == 0:
        return {
            "total": 0,
            "successful": 0,
            "failed": 0,
            "success_rate": 0,
            "avg_time": 0,
            "avg_results": 0
        }
    
    success_rate = (successful / total) * 100
    avg_time = sum(r["time_taken"] for r in results) / total
    avg_results = sum(r["num_results"] for r in results) / total
    
    # Group errors
    errors = {}
    for r in results:
        if not r["success"] and r["error"]:
            error_type = str(r["error"])
            errors[error_type] = errors.get(error_type, 0) + 1
    
    return {
        "total": total,
        "successful": successful,
        "failed": failed,
        "success_rate": success_rate,
        "avg_time": avg_time,
        "avg_results": avg_results,
        "errors": errors
    }

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Test WebSearchAgentLocalEnhanced with 100 random requests")
    parser.add_argument("--parallel", action="store_true", help="Run tests in parallel")
    parser.add_argument("--workers", type=int, default=5, help="Number of workers for parallel testing")
    parser.add_argument("--count", type=int, default=100, help="Number of queries to test")
    parser.add_argument("--results", type=int, default=3, help="Number of results per query")
    parser.add_argument("--output", type=str, default="test_results.json", help="Output file for results")
    args = parser.parse_args()
    
    print(f"Initializing WebSearchAgentLocalEnhanced...")
    
    # Initialize the agent
    agent = WebSearchAgentLocalEnhanced(
        search_method="auto",
        api_search_config={
            "engine": "searx",
            "searx_url": "http://localhost:8080",  # Change if needed
            "language": "auto"
        },
        verbose=True
    )
    
    # Generate random queries
    print(f"Generating {args.count} random queries...")
    queries = generate_random_queries(SEARCH_QUERIES, args.count)
    
    # Run tests
    print(f"Running {'parallel' if args.parallel else 'sequential'} tests...")
    start_time = time.time()
    
    if args.parallel:
        results = run_parallel_test(agent, queries, args.results, args.workers)
    else:
        results = run_sequential_test(agent, queries, args.results)
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # Analyze results
    analysis = analyze_results(results)
    
    # Print summary
    print("\n" + "=" * 50)
    print(f"Test Summary ({args.count} queries):")
    print("=" * 50)
    print(f"Total queries: {analysis['total']}")
    print(f"Successful: {analysis['successful']} ({analysis['success_rate']:.2f}%)")
    print(f"Failed: {analysis['failed']}")
    print(f"Average time per query: {analysis['avg_time']:.2f} seconds")
    print(f"Average results per query: {analysis['avg_results']:.2f}")
    print(f"Total test time: {total_time:.2f} seconds")
    
    if analysis['errors']:
        print("\nError types:")
        for error, count in sorted(analysis['errors'].items(), key=lambda x: x[1], reverse=True):
            print(f"  - {error}: {count}")
    
    # Save results to file
    output_data = {
        "test_config": {
            "parallel": args.parallel,
            "workers": args.workers,
            "query_count": args.count,
            "results_per_query": args.results
        },
        "summary": {
            "total_queries": analysis['total'],
            "successful": analysis['successful'],
            "failed": analysis['failed'],
            "success_rate": analysis['success_rate'],
            "avg_time": analysis['avg_time'],
            "avg_results": analysis['avg_results'],
            "total_test_time": total_time,
            "errors": analysis['errors']
        },
        "detailed_results": results
    }
    
    with open(args.output, 'w') as f:
        json.dump(output_data, f, indent=2)
    
    print(f"\nDetailed results saved to {args.output}")

if __name__ == "__main__":
    main()
