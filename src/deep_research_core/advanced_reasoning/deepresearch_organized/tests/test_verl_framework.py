"""
Unit tests for the VERL framework implementation.

These tests verify that the VERL framework correctly:
1. Initializes with proper configuration
2. Trains models with appropriate optimization steps
3. Applies Vietnamese language weighting to rewards
4. <PERSON>les checkpointing and evaluation correctly
"""

import unittest
import os
import shutil
import torch
import numpy as np
from typing import Dict, List, Any

# Import the VERL framework (for testing purposes)
from verl_framework import VERLFramework

# Import the mock models from the example
from verl_example import MockModel, MockRewardModel, prepare_sample_data

class TestVERLFramework(unittest.TestCase):
    """Test suite for the VERL framework implementation."""
    
    def setUp(self):
        """Set up test environment before each test."""
        # Create mock models
        self.base_model = MockModel(vocab_size=1000, hidden_size=64)
        self.reward_model = MockRewardModel(vocab_size=1000, hidden_size=64)
        
        # Create test data
        self.train_data = prepare_sample_data(num_samples=20, seq_length=10, vocab_size=1000)
        self.eval_data = prepare_sample_data(num_samples=5, seq_length=10, vocab_size=1000)
        
        # Set up test directory for checkpoints
        self.test_dir = "test_output"
        os.makedirs(self.test_dir, exist_ok=True)
        
        # Default config for testing
        self.config = {
            "learning_rate": 1e-4,
            "batch_size": 4,
            "max_steps": 5,  # Small number for faster tests
            "vietnamese_token_weight": 1.5
        }
    
    def tearDown(self):
        """Clean up after each test."""
        # Remove test directory
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def test_initialization(self):
        """Test that the VERL framework initializes correctly."""
        # Initialize framework
        verl = VERLFramework(
            base_model=self.base_model,
            reward_model=self.reward_model,
            config=self.config,
            checkpoint_dir=self.test_dir,
            vietnamese_focus=True
        )
        
        # Check that attributes are set correctly
        self.assertEqual(verl.base_model, self.base_model)
        self.assertEqual(verl.reward_model, self.reward_model)
        self.assertEqual(verl.checkpoint_dir, self.test_dir)
        self.assertTrue(verl.vietnamese_focus)
        
        # Check that configuration is merged correctly
        self.assertEqual(verl.config["learning_rate"], 1e-4)
        self.assertEqual(verl.config["batch_size"], 4)
        self.assertEqual(verl.config["max_steps"], 5)
        self.assertEqual(verl.config["vietnamese_token_weight"], 1.5)
        
        # Check optimizer initialization
        self.assertIsInstance(verl.optimizer, torch.optim.Adam)
        
        # Check Vietnamese support initialization
        self.assertTrue(hasattr(verl, 'vietnamese_tokens'))
        self.assertIsInstance(verl.vietnamese_tokens, list)
        self.assertTrue(len(verl.vietnamese_tokens) > 0)
    
    def test_vietnamese_weighting(self):
        """Test that Vietnamese token weighting works correctly."""
        # Initialize framework
        verl = VERLFramework(
            base_model=self.base_model,
            config={"vietnamese_token_weight": 2.0},
            vietnamese_focus=True
        )
        
        # Create test input with known Vietnamese tokens
        input_ids = torch.tensor([
            [1, 2, 3, 1000, 5],  # Contains 1 Vietnamese token (index 3)
            [6, 7, 8, 9, 10]     # Contains no Vietnamese tokens
        ])
        
        # Create mock rewards
        rewards = torch.tensor([1.0, 1.0])
        
        # Apply Vietnamese weighting
        weighted_rewards = verl._apply_vietnamese_weighting(rewards, input_ids, weight=2.0)
        
        # Check that only sequences with Vietnamese tokens get higher rewards
        self.assertGreater(weighted_rewards[0].item(), rewards[0].item())
        self.assertEqual(weighted_rewards[1].item(), rewards[1].item())
    
    def test_training(self):
        """Test that training process runs without errors and improves the model."""
        # Initialize framework
        verl = VERLFramework(
            base_model=self.base_model,
            reward_model=self.reward_model,
            config=self.config,
            checkpoint_dir=self.test_dir
        )
        
        # Record metrics during training
        metrics_history = []
        
        def test_callback(step, framework, metrics):
            metrics_history.append(metrics)
        
        # Train the model
        results = verl.train(
            train_data=self.train_data,
            eval_data=self.eval_data,
            callbacks=[test_callback]
        )
        
        # Check that training completed and returned results
        self.assertIn("metrics", results)
        self.assertIn("final_loss", results)
        self.assertIn("final_reward", results)
        
        # Check that metrics were collected
        self.assertTrue(len(metrics_history) > 0)
        
        # Verify metrics format
        for metric_dict in metrics_history:
            self.assertIn("eval_loss", metric_dict)
            self.assertIn("eval_reward", metric_dict)
    
    def test_checkpointing(self):
        """Test that checkpointing and loading works correctly."""
        # Initialize framework
        verl = VERLFramework(
            base_model=self.base_model,
            reward_model=self.reward_model,
            config=self.config,
            checkpoint_dir=self.test_dir
        )
        
        # Train briefly
        verl.train(
            train_data=self.train_data,
            eval_data=self.eval_data,
            steps=1  # Just one step for testing
        )
        
        # Save checkpoint
        checkpoint_path = os.path.join(self.test_dir, "test_checkpoint")
        verl.save_checkpoint(checkpoint_path)
        
        # Check that checkpoint files were created
        self.assertTrue(os.path.exists(f"{checkpoint_path}.pt"))
        self.assertTrue(os.path.exists(f"{checkpoint_path}_config.json"))
        
        # Create a new framework instance with a fresh model
        new_model = MockModel(vocab_size=1000, hidden_size=64)
        new_verl = VERLFramework(
            base_model=new_model,
            reward_model=self.reward_model,
            config=self.config
        )
        
        # Load checkpoint
        new_verl.load_checkpoint(f"{checkpoint_path}.pt")
        
        # Evaluate both models on the same data
        eval_metrics_original = verl.evaluate(self.eval_data)
        eval_metrics_loaded = new_verl.evaluate(self.eval_data)
        
        # Check that the loaded model produces similar metrics
        self.assertAlmostEqual(
            eval_metrics_original["eval_loss"],
            eval_metrics_loaded["eval_loss"],
            places=5
        )
        self.assertAlmostEqual(
            eval_metrics_original["eval_reward"],
            eval_metrics_loaded["eval_reward"],
            places=5
        )
    
    def test_batch_sampling(self):
        """Test that batch sampling works correctly."""
        # Initialize framework with small batch size
        verl = VERLFramework(
            base_model=self.base_model,
            config={"batch_size": 3}
        )
        
        # Sample a batch
        batch = verl._sample_batch(self.train_data)
        
        # Check batch shape and contents
        self.assertEqual(batch["input_ids"].shape[0], 3)  # Batch size
        self.assertEqual(batch["input_ids"].shape[1], 10)  # Sequence length
        self.assertEqual(batch["attention_mask"].shape, batch["input_ids"].shape)
        
        # Check that batch contains tensors
        self.assertIsInstance(batch["input_ids"], torch.Tensor)
        self.assertIsInstance(batch["attention_mask"], torch.Tensor)
        self.assertIsInstance(batch["rewards"], torch.Tensor)
    
    def test_evaluation(self):
        """Test that evaluation works correctly."""
        # Initialize framework
        verl = VERLFramework(
            base_model=self.base_model,
            reward_model=self.reward_model,
            config=self.config
        )
        
        # Evaluate on test data
        metrics = verl.evaluate(self.eval_data)
        
        # Check that metrics are returned
        self.assertIn("eval_loss", metrics)
        self.assertIn("eval_reward", metrics)
        
        # Check that metrics are valid numbers
        self.assertIsInstance(metrics["eval_loss"], float)
        self.assertIsInstance(metrics["eval_reward"], float)

if __name__ == "__main__":
    unittest.main() 