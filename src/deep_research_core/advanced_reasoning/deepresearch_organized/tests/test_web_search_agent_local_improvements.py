"""
Test cho các tính năng mới của WebSearchAgentLocal.
"""

import sys
import os
import time
import unittest
from unittest.mock import patch, MagicMock

# Add the src directory to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), ".")))

try:
    from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal
except ImportError:
    # Thử import trực tiếp từ thư mục hiện tại
    try:
        from web_search_agent_local import WebSearchAgentLocal
    except ImportError:
        print(
            "Không thể import WebSearchAgentLocal. Đ<PERSON>m bảo bạn đang chạy từ thư mục gốc."
        )
        sys.exit(1)

# Thử import các module tích hợp
try:
    from src.deep_research_core.agents.vietnamese_search_integration import integrate_vietnamese_search
except ImportError:
    try:
        from vietnamese_search_integration import integrate_vietnamese_search
    except ImportError:
        integrate_vietnamese_search = None

try:
    from src.deep_research_core.agents.adaptive_crawler_integration import integrate_adaptive_crawler
except ImportError:
    try:
        from adaptive_crawler_integration import integrate_adaptive_crawler
    except ImportError:
        integrate_adaptive_crawler = None


def print_section(title):
    """Print a section title."""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, "="))
    print("=" * 80)


def print_result(result):
    """Print search result."""
    print(f"Tìm kiếm thành công: {result.get('success', False)}")
    print(f"Phương thức tìm kiếm: {result.get('search_method', 'unknown')}")
    print(f"Engine: {result.get('engine', 'unknown')}")

    if result.get("success"):
        results = result.get("results", [])
        print(f"Tìm thấy {len(results)} kết quả")

        for i, item in enumerate(results[:3]):  # Chỉ hiển thị 3 kết quả đầu tiên
            print(f"\nKết quả {i+1}:")
            print(f"  Tiêu đề: {item.get('title', '')}")
            print(f"  URL: {item.get('url', '')}")
            snippet = item.get("snippet", "")
            if snippet:
                print(
                    f"  Đoạn trích: {snippet[:100]}..."
                    if len(snippet) > 100
                    else f"  Đoạn trích: {snippet}"
                )

            content = item.get("content", "")
            if content:
                print(
                    f"  Nội dung: {content[:100]}..."
                    if len(content) > 100
                    else f"  Nội dung: {content}"
                )
    else:
        print(f"Lỗi: {result.get('error', 'Unknown error')}")


class TestWebSearchAgentLocalImprovements(unittest.TestCase):
    """Test các tính năng mới của WebSearchAgentLocal."""

    def setUp(self):
        """Khởi tạo WebSearchAgentLocal."""
        self.agent = WebSearchAgentLocal(
            search_method="auto",
            verbose=True
        )

    def test_dictionary_initialization(self):
        """Kiểm tra khởi tạo từ điển."""
        # Kiểm tra các thuộc tính từ điển
        self.assertIsInstance(self.agent.cache, dict)
        self.assertIsInstance(self.agent.cache_timestamps, dict)
        self.assertIsInstance(self.agent.engine_request_counts, dict)
        self.assertIsInstance(self.agent.engine_reset_times, dict)
        self.assertIsInstance(self.agent.last_captcha_detection, dict)

    @unittest.skipIf(integrate_vietnamese_search is None, "vietnamese_search_integration không khả dụng")
    def test_vietnamese_search_integration(self):
        """Kiểm tra tích hợp tìm kiếm tiếng Việt."""
        # Gọi hàm tích hợp
        integrate_vietnamese_search(self.agent)
        
        # Kiểm tra các thuộc tính và phương thức đã được thêm
        self.assertTrue(hasattr(self.agent, "search_vietnamese"))
        
        # Kiểm tra danh sách công cụ tìm kiếm
        if hasattr(self.agent, "available_engines"):
            self.assertIn("coccoc", self.agent.available_engines)
            self.assertIn("wikitiengviet", self.agent.available_engines)
            self.assertIn("baomoi", self.agent.available_engines)

    @unittest.skipIf(integrate_adaptive_crawler is None, "adaptive_crawler_integration không khả dụng")
    def test_adaptive_crawler_integration(self):
        """Kiểm tra tích hợp AdaptiveCrawler."""
        # Gọi hàm tích hợp
        integrate_adaptive_crawler(self.agent)
        
        # Kiểm tra các thuộc tính và phương thức đã được thêm
        self.assertTrue(hasattr(self.agent, "_adaptive_crawler"))
        self.assertTrue(hasattr(self.agent, "_deep_crawl_with_adaptive_crawler"))

    @patch("web_search_agent_local.WebSearchAgentLocal._deep_crawl")
    def test_deep_research(self, mock_deep_crawl):
        """Kiểm tra phương thức deep_research."""
        # Mock _deep_crawl để trả về kết quả giả
        mock_deep_crawl.return_value = {
            "success": True,
            "url": "https://example.com",
            "text": "This is a test content",
            "title": "Test Page"
        }
        
        # Gọi phương thức deep_research
        if hasattr(self.agent, "deep_research"):
            result = self.agent.deep_research(
                query="Python programming",
                num_results_per_query=3,
                max_sub_queries=5,
                min_sub_queries=2,
                max_content_length=10000,
                max_depth=2,
                language="auto",
                use_adaptive_crawler=True,
                optimize_query_distribution=True,
                prioritize_complex_queries=True,
                use_parallel_processing=True
            )
            
            # Kiểm tra kết quả
            self.assertIsInstance(result, dict)
            self.assertIn("success", result)
        else:
            print("Phương thức deep_research không khả dụng")

    @patch("web_search_agent_local.WebSearchAgentLocal.extract_content")
    def test_deep_crawl(self, mock_extract_content):
        """Kiểm tra phương thức _deep_crawl."""
        # Mock extract_content để trả về kết quả giả
        mock_extract_content.return_value = {
            "success": True,
            "url": "https://example.com",
            "text": "This is a test content",
            "title": "Test Page"
        }
        
        # Gọi phương thức _deep_crawl
        if hasattr(self.agent, "_deep_crawl"):
            result = self.agent._deep_crawl(
                url="https://example.com",
                max_depth=1,
                max_pages=3,
                timeout=30,
                include_html=False,
                respect_robots=True
            )
            
            # Kiểm tra kết quả
            self.assertIsInstance(result, dict)
            self.assertIn("success", result)
        else:
            print("Phương thức _deep_crawl không khả dụng")

    def test_vietnamese_detection(self):
        """Kiểm tra phát hiện tiếng Việt."""
        # Tạo phương thức detect_vietnamese_query giả
        def detect_vietnamese_query(self, query):
            vietnamese_chars = "àáảãạăằắẳẵặâầấẩẫậèéẻẽẹêềếểễệìíỉĩịòóỏõọôồốổỗộơờớởỡợùúủũụưừứửữựỳýỷỹỵđ"
            vietnamese_chars += vietnamese_chars.upper()
            return any(c in vietnamese_chars for c in query)
        
        # Gán phương thức detect_vietnamese_query cho agent
        import types
        self.agent.detect_vietnamese_query = types.MethodType(detect_vietnamese_query, self.agent)
        
        # Kiểm tra phát hiện tiếng Việt
        self.assertTrue(self.agent.detect_vietnamese_query("Xin chào thế giới"))
        self.assertFalse(self.agent.detect_vietnamese_query("Hello world"))

    def test_captcha_handling(self):
        """Kiểm tra xử lý CAPTCHA."""
        # Tạo phương thức handle_captcha giả
        def handle_captcha(self, html_content, url):
            has_captcha = "captcha" in html_content.lower() or "recaptcha" in html_content.lower()
            return {
                "success": not has_captcha,
                "message": "CAPTCHA detected" if has_captcha else "No CAPTCHA detected",
                "captcha_type": "recaptcha" if "recaptcha" in html_content.lower() else "unknown" if has_captcha else None
            }
        
        # Gán phương thức handle_captcha cho agent
        import types
        self.agent.handle_captcha = types.MethodType(handle_captcha, self.agent)
        
        # Kiểm tra xử lý CAPTCHA
        html_with_captcha = "<html><body><div class='g-recaptcha'></div></body></html>"
        html_without_captcha = "<html><body><div class='content'></div></body></html>"
        
        result_with_captcha = self.agent.handle_captcha(html_with_captcha, "https://example.com")
        result_without_captcha = self.agent.handle_captcha(html_without_captcha, "https://example.com")
        
        self.assertFalse(result_with_captcha["success"])
        self.assertTrue(result_without_captcha["success"])
        self.assertEqual(result_with_captcha["captcha_type"], "recaptcha")
        self.assertIsNone(result_without_captcha["captcha_type"])


def main():
    """Main function."""
    print_section("Kiểm tra các tính năng mới của WebSearchAgentLocal")
    
    # Chạy các bài kiểm tra
    unittest.main(argv=['first-arg-is-ignored'], exit=False)


if __name__ == "__main__":
    main()
