#!/usr/bin/env python3
"""
Test for academic search functionality
Testing the replacement of Semantic Scholar with OpenAlex and Crossref
"""

import sys
import time
from typing import Dict, Any, List

# Import the WebSearchAgent
from src.deep_research_core.agents.web_search_agent import WebSearchAgent

def print_section(title):
    """Print a section title."""
    print("\n" + "=" * 80)
    print(title)
    print("=" * 80)

def print_result(result, max_results=3):
    """Print search result in a readable format."""
    if not result.get("success", False):
        print(f"Search failed: {result.get('error', 'Unknown error')}")
        if "detailed_errors" in result:
            print("Detailed errors:")
            for engine, error in result.get("detailed_errors", {}).items():
                print(f"  {engine}: {error}")
        return False
    
    results = result.get("results", [])
    print(f"Found {len(results)} results")
    print(f"Sources used: {result.get('sources', [])}")
    
    if len(results) == 0:
        return False
    
    for i, item in enumerate(results[:max_results]):
        print(f"\nResult {i+1}:")
        print(f"Title: {item.get('title', '')}")
        print(f"URL: {item.get('url', '')}")
        print(f"Source: {item.get('source', '')}")
        snippet = item.get('snippet', '')
        if snippet and len(snippet) > 100:
            snippet = snippet[:100] + "..."
        print(f"Snippet: {snippet}")
        
        # Print additional academic fields
        if "authors" in item:
            print(f"Authors: <AUTHORS>
        if "year" in item:
            print(f"Year: {item.get('year', '')}")
        if "venue" in item:
            print(f"Venue: {item.get('venue', '')}")
        if "citation_count" in item:
            print(f"Citations: {item.get('citation_count', '')}")
    
    return True

def test_academic_combined():
    """Test the academic_combined search using OpenAlex and Crossref."""
    print_section("Testing Academic Combined Search")
    
    agent = WebSearchAgent(
        search_method="api",
        api_search_config={"engine": "academic_combined"},
        verbose=True
    )
    
    queries = [
        "machine learning in healthcare 2023",
        "transformer models natural language processing",
        "quantum computing applications 2023",
        "climate change impact agriculture",
        "neural networks deep learning"
    ]
    
    results = {}
    for query in queries:
        print(f"\n--- Query: {query} ---")
        result = agent.search(query, num_results=5)
        success = print_result(result)
        results[query] = {
            "success": success,
            "sources": result.get("sources", []),
            "result_count": len(result.get("results", []))
        }
        time.sleep(2)  # Avoid rate limiting
    
    return results

def test_openalex_specific():
    """Test OpenAlex search specifically."""
    print_section("Testing OpenAlex Search")
    
    agent = WebSearchAgent(
        search_method="api",
        api_search_config={"engine": "openalex"},
        verbose=True
    )
    
    queries = [
        "machine learning in healthcare 2023",
        "transformer models natural language processing"
    ]
    
    results = {}
    for query in queries:
        print(f"\n--- Query: {query} ---")
        result = agent.search(query, num_results=5)
        success = print_result(result)
        results[query] = {
            "success": success,
            "engine": "openalex",
            "result_count": len(result.get("results", []))
        }
        time.sleep(2)  # Avoid rate limiting
    
    return results

def test_crossref_specific():
    """Test Crossref search specifically."""
    print_section("Testing Crossref Search")
    
    agent = WebSearchAgent(
        search_method="api",
        api_search_config={"engine": "crossref"},
        verbose=True
    )
    
    queries = [
        "machine learning in healthcare 2023",
        "transformer models natural language processing"
    ]
    
    results = {}
    for query in queries:
        print(f"\n--- Query: {query} ---")
        result = agent.search(query, num_results=5)
        success = print_result(result)
        results[query] = {
            "success": success,
            "engine": "crossref",
            "result_count": len(result.get("results", []))
        }
        time.sleep(2)  # Avoid rate limiting
    
    return results

def test_priority_order():
    """Test the priority order between OpenAlex and Crossref."""
    print_section("Testing Priority Order")
    
    # Create a spy function to record calls to OpenAlex and Crossref
    call_order = []
    
    def spy_openalex(*args, **kwargs):
        call_order.append("openalex")
        # Call the original function
        result = original_search_openalex(*args, **kwargs)
        return result
    
    def spy_crossref(*args, **kwargs):
        call_order.append("crossref")
        # Call the original function
        result = original_search_crossref(*args, **kwargs)
        return result
    
    # Create the agent
    agent = WebSearchAgent(
        search_method="api",
        api_search_config={"engine": "academic_combined"},
        verbose=True
    )
    
    # Save the original methods
    original_search_openalex = agent._search_openalex
    original_search_crossref = agent._search_crossref
    
    try:
        # Replace the methods with our spy functions
        agent._search_openalex = spy_openalex
        agent._search_crossref = spy_crossref
        
        print("\n--- Testing call order with default priority ---")
        query = "deep learning vision transformers"
        # Force refresh to avoid using cached results
        result = agent.search(query, num_results=5, force_refresh=True)
        
        print(f"Call order: {call_order}")
        print(f"Success: {result.get('success', False)}")
        print(f"Sources used: {result.get('sources', [])}")
        
        # Print some results if available
        print_result(result)
        
        return {
            "success": result.get("success", False),
            "call_order": call_order,
            "sources": result.get("sources", [])
        }
    finally:
        # Restore original methods
        agent._search_openalex = original_search_openalex
        agent._search_crossref = original_search_crossref

def test_fallback_mechanism():
    """Test the fallback mechanism between OpenAlex and Crossref."""
    print_section("Testing Fallback Mechanism")
    
    # Create a mock function that always fails
    def mock_failed_openalex(*args, **kwargs):
        print("Mock OpenAlex search failure triggered!")
        return {
            "success": False,
            "error": "Simulated OpenAlex failure for testing fallback mechanism",
            "results": []
        }
    
    # Create the agent
    agent = WebSearchAgent(
        search_method="api",
        api_search_config={"engine": "academic_combined"},
        verbose=True
    )
    
    # Save the original method
    original_search_openalex = agent._search_openalex
    
    try:
        # Replace the method with our mock
        agent._search_openalex = mock_failed_openalex
        
        print("\n--- Testing with OpenAlex failure ---")
        query = "machine learning in healthcare 2023"
        # Force refresh to avoid using cached results
        result = agent.search(query, num_results=5, force_refresh=True)
        
        # Check if Crossref was used as fallback
        crossref_used = result.get("success", False) and "crossref" in result.get("sources", [])
        print(f"Success: {result.get('success', False)}")
        print(f"Sources used: {result.get('sources', [])}")
        print(f"Results count: {len(result.get('results', []))}")
        print(f"Detailed errors: {result.get('detailed_errors', {})}")
        print(f"Errors: {result.get('errors', {})}")
        
        print(f"Fallback to Crossref used: {crossref_used}")
        
        # Print some results if available
        print_result(result)
        
        return {
            "success": result.get("success", False),
            "fallback_successful": crossref_used,
            "sources": result.get("sources", []),
            "errors": result.get("errors", {}) or result.get("detailed_errors", {})
        }
    finally:
        # Restore original method
        agent._search_openalex = original_search_openalex

def test_crossref_fallback():
    """Test the fallback mechanism from Crossref to OpenAlex."""
    print_section("Testing Crossref to OpenAlex Fallback")
    
    # Create a mock function that always fails
    def mock_failed_crossref(*args, **kwargs):
        print("Mock Crossref search failure triggered!")
        return {
            "success": False,
            "error": "Simulated Crossref failure for testing fallback mechanism",
            "results": []
        }
    
    # Create the agent
    agent = WebSearchAgent(
        search_method="api",
        api_search_config={"engine": "academic_combined"},
        verbose=True
    )
    
    # Save the original method
    original_search_crossref = agent._search_crossref
    
    try:
        # Replace the method with our mock
        agent._search_crossref = mock_failed_crossref
        
        print("\n--- Testing with Crossref failure ---")
        query = "neural networks reinforcement learning"
        # Force refresh to avoid using cached results
        result = agent.search(query, num_results=5, force_refresh=True)
        
        # Check if OpenAlex was used as fallback
        openalex_used = result.get("success", False) and "openalex" in result.get("sources", [])
        print(f"Success: {result.get('success', False)}")
        print(f"Sources used: {result.get('sources', [])}")
        print(f"Results count: {len(result.get('results', []))}")
        print(f"Detailed errors: {result.get('detailed_errors', {})}")
        print(f"Errors: {result.get('errors', {})}")
        
        print(f"Fallback to OpenAlex used: {openalex_used}")
        
        # Print some results if available
        print_result(result)
        
        return {
            "success": result.get("success", False),
            "fallback_successful": openalex_used,
            "sources": result.get("sources", []),
            "errors": result.get("errors", {}) or result.get("detailed_errors", {})
        }
    finally:
        # Restore original method
        agent._search_crossref = original_search_crossref

def test_both_services_failing():
    """Test case when both OpenAlex and Crossref fail."""
    print_section("Testing Both Services Failing")
    
    # Create mock functions that always fail
    def mock_failed_openalex(*args, **kwargs):
        print("Mock OpenAlex search failure triggered!")
        return {
            "success": False,
            "error": "Simulated OpenAlex failure",
            "results": []
        }
    
    def mock_failed_crossref(*args, **kwargs):
        print("Mock Crossref search failure triggered!")
        return {
            "success": False,
            "error": "Simulated Crossref failure",
            "results": []
        }
    
    # Create the agent
    agent = WebSearchAgent(
        search_method="api",
        api_search_config={"engine": "academic_combined"},
        verbose=True
    )
    
    # Save the original methods
    original_search_openalex = agent._search_openalex
    original_search_crossref = agent._search_crossref
    
    try:
        # Replace the methods with our mocks
        agent._search_openalex = mock_failed_openalex
        agent._search_crossref = mock_failed_crossref
        
        print("\n--- Testing with both OpenAlex and Crossref failures ---")
        query = "machine learning in healthcare 2023"
        # Force refresh to avoid using cached results
        result = agent.search(query, num_results=5, force_refresh=True)
        
        # Check if arXiv or PubMed was used as fallback
        arxiv_or_pubmed_used = result.get("success", False) and any(source in result.get("sources", []) for source in ["arxiv", "pubmed"])
        print(f"Success: {result.get('success', False)}")
        print(f"Sources used: {result.get('sources', [])}")
        print(f"Results count: {len(result.get('results', []))}")
        print(f"Detailed errors: {result.get('detailed_errors', {})}")
        
        print(f"Fallback to arXiv or PubMed used: {arxiv_or_pubmed_used}")
        
        # Print some results if available
        print_result(result)
        
        return {
            "success": result.get("success", False),
            "fallback_successful": arxiv_or_pubmed_used,
            "sources": result.get("sources", []),
            "errors": result.get("errors", {}) or result.get("detailed_errors", {})
        }
    finally:
        # Restore original methods
        agent._search_openalex = original_search_openalex
        agent._search_crossref = original_search_crossref

def test_result_combination():
    """Test the ability to combine results from multiple sources."""
    print_section("Testing Result Combination")
    
    # Create the agent
    agent = WebSearchAgent(
        search_method="api",
        api_search_config={"engine": "academic_combined"},
        verbose=True
    )
    
    # Query that's likely to get results from multiple sources
    query = "quantum computing algorithms"
    
    print(f"\n--- Testing result combination for query: {query} ---")
    # Force refresh to avoid using cached results
    result = agent.search(query, num_results=10, force_refresh=True)
    
    # Check if results were successfully combined
    sources_used = result.get("sources", [])
    print(f"Success: {result.get('success', False)}")
    print(f"Sources used: {sources_used}")
    print(f"Results count: {len(result.get('results', []))}")
    
    # Count results by source
    results_by_source = {}
    for item in result.get("results", []):
        source = item.get("source", "unknown")
        if source not in results_by_source:
            results_by_source[source] = 0
        results_by_source[source] += 1
    
    print("Results by source:")
    for source, count in results_by_source.items():
        print(f"  {source}: {count}")
    
    # Print some results if available
    print_result(result, max_results=5)
    
    return {
        "success": result.get("success", False),
        "sources_used": sources_used,
        "result_count": len(result.get("results", [])),
        "results_by_source": results_by_source
    }

def test_vietnamese_queries():
    """Test the ability to handle Vietnamese language queries."""
    print_section("Testing Vietnamese Language Queries")
    
    # Create the agent
    agent = WebSearchAgent(
        search_method="api",
        api_search_config={"engine": "academic_combined"},
        verbose=True
    )
    
    # Vietnamese queries
    vietnamese_queries = [
        "học máy trong y tế",
        "trí tuệ nhân tạo và đạo đức",
        "xử lý ngôn ngữ tự nhiên tiếng Việt"
    ]
    
    results = {}
    for query in vietnamese_queries:
        print(f"\n--- Query: {query} ---")
        result = agent.search(query, num_results=5)
        success = print_result(result)
        
        results[query] = {
            "success": success,
            "sources": result.get("sources", []),
            "result_count": len(result.get("results", []))
        }
        time.sleep(2)  # Avoid rate limiting
    
    return results

def test_parallel_execution():
    """Test the parallel execution of search requests to different sources."""
    print_section("Testing Parallel Execution")
    
    # Create the agent
    agent = WebSearchAgent(
        search_method="api",
        api_search_config={"engine": "academic_combined"},
        verbose=True
    )
    
    # Set parallel requests mode (as an attribute after initialization)
    agent.parallel_requests = True
    
    # Measure time for sequential execution
    query = "quantum machine learning algorithms"
    
    print(f"\n--- Testing sequential execution timing ---")
    agent.parallel_requests = False  # Disable parallel requests
    
    # Time the sequential execution
    sequential_start = time.time()
    sequential_result = agent.search(query, num_results=10, force_refresh=True)
    sequential_time = time.time() - sequential_start
    
    print(f"Sequential execution time: {sequential_time:.2f} seconds")
    print(f"Success: {sequential_result.get('success', False)}")
    print(f"Sources used: {sequential_result.get('sources', [])}")
    print(f"Results count: {len(sequential_result.get('results', []))}")
    
    # Wait before next request to avoid rate limiting
    time.sleep(5)
    
    # Measure time for parallel execution
    print(f"\n--- Testing parallel execution timing ---")
    agent.parallel_requests = True  # Enable parallel requests
    
    # Time the parallel execution
    parallel_start = time.time()
    parallel_result = agent.search(query, num_results=10, force_refresh=True)
    parallel_time = time.time() - parallel_start
    
    print(f"Parallel execution time: {parallel_time:.2f} seconds")
    print(f"Success: {parallel_result.get('success', False)}")
    print(f"Sources used: {parallel_result.get('sources', [])}")
    print(f"Results count: {len(parallel_result.get('results', []))}")
    
    # Compute speedup
    speedup = sequential_time / parallel_time if parallel_time > 0 else 0
    print(f"Speedup: {speedup:.2f}x")
    
    return {
        "sequential_time": sequential_time,
        "parallel_time": parallel_time,
        "speedup": speedup,
        "sequential_success": sequential_result.get("success", False),
        "parallel_success": parallel_result.get("success", False)
    }

def main():
    """Run all tests."""
    # Store test results
    results = {}
    
    # Run academic combined test
    print("\nRunning test_academic_combined...")
    results["academic_combined"] = test_academic_combined()
    
    # Run openalex specific test
    print("\nRunning test_openalex_specific...")
    results["openalex_specific"] = test_openalex_specific()
    
    # Run crossref specific test
    print("\nRunning test_crossref_specific...")
    results["crossref_specific"] = test_crossref_specific()
    
    # Run priority order test
    print("\nRunning test_priority_order...")
    priority_result = test_priority_order()
    results["priority_order"] = {
        "success": priority_result.get("success", False),
        "call_order": priority_result.get("call_order", [])
    }
    
    # Run result combination test
    print("\nRunning test_result_combination...")
    combination_result = test_result_combination()
    results["result_combination"] = {
        "success": combination_result.get("success", False),
        "sources_used": combination_result.get("sources_used", []),
        "results_by_source": combination_result.get("results_by_source", {})
    }
    
    # Run Vietnamese query test
    print("\nRunning test_vietnamese_queries...")
    results["vietnamese_queries"] = test_vietnamese_queries()
    
    # Run parallel execution test
    print("\nRunning test_parallel_execution...")
    parallel_result = test_parallel_execution()
    results["parallel_execution"] = parallel_result
    
    # Run fallback mechanism test (OpenAlex -> Crossref)
    print("\nRunning test_fallback_mechanism...")
    fallback_result = test_fallback_mechanism()
    results["fallback_mechanism"] = {
        "success": fallback_result.get("success", False),
        "fallback_successful": fallback_result.get("fallback_successful", False)
    }

    # Run Crossref fallback test (Crossref -> OpenAlex)
    print("\nRunning test_crossref_fallback...")
    crossref_fallback_result = test_crossref_fallback()
    results["crossref_fallback"] = {
        "success": crossref_fallback_result.get("success", False),
        "fallback_successful": crossref_fallback_result.get("fallback_successful", False)
    }
    
    # Run both services failing test
    print("\nRunning test_both_services_failing...")
    both_failing_result = test_both_services_failing()
    results["both_services_failing"] = {
        "success": both_failing_result.get("success", False),
        "fallback_successful": both_failing_result.get("fallback_successful", False)
    }
    
    # Print test summary
    print("\n" + "=" * 80)
    print("Test Summary")
    print("=" * 80)
    
    for test_name, result in results.items():
        if test_name in ["academic_combined", "vietnamese_queries"]:
            # Check the number of successful queries
            successful_queries = 0
            for query_result in result.values():
                if query_result.get("success", False):
                    successful_queries += 1
            print(f"{test_name}: {successful_queries}/{len(result)} tests passed")
        elif test_name == "openalex_specific" or test_name == "crossref_specific":
            # Check the number of successful queries
            successful_queries = 0
            for query_result in result.values():
                if query_result.get("success", False):
                    successful_queries += 1
            print(f"{test_name}: {successful_queries}/{len(result)} tests passed")
        elif test_name == "priority_order":
            # For priority order, we print the actual call order
            print(f"{test_name}: {result.get('call_order', [])}")
        elif test_name == "result_combination":
            # For result combination, we check if multiple sources were used
            sources = result.get("sources_used", [])
            sources_count = len(sources)
            print(f"{test_name}: {sources_count} sources used - {sources}")
            for source, count in result.get("results_by_source", {}).items():
                print(f"  {source}: {count} results")
        elif test_name == "parallel_execution":
            # For parallel execution, we print timing information
            speedup = result.get("speedup", 0)
            print(f"{test_name}: Speedup = {speedup:.2f}x")
            print(f"  Sequential: {result.get('sequential_time', 0):.2f}s")
            print(f"  Parallel: {result.get('parallel_time', 0):.2f}s")
        elif test_name in ["fallback_mechanism", "crossref_fallback", "both_services_failing"]:
            # For fallback tests, we consider it passed if fallback_successful is True
            pass_count = 1 if result.get("fallback_successful", False) else 0
            print(f"{test_name}: {pass_count}/1 tests passed")
        else:
            print(f"{test_name}: results unavailable")

if __name__ == "__main__":
    main() 