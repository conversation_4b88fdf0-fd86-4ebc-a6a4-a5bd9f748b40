#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test cho adaptive_crawler_integration.py.

Module này kiểm tra các hàm trong adaptive_crawler_integration.py.
"""

import os
import sys
import unittest
from unittest.mock import MagicMock, patch
import json
import logging

# Thê<PERSON> thư mục gốc vào sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import các module cần test
from deepresearch.src.deep_research_core.agents.adaptive_crawler_integration import (
    integrate_adaptive_crawler,
    crawl_url,
    crawl_urls,
    extract_content_from_url,
    extract_links_from_url,
    extract_metadata_from_url,
    deep_crawl_with_adaptive_crawler
)

# Thiết lập logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestAdaptiveCrawlerIntegration(unittest.TestCase):
    """
    Test case cho adaptive_crawler_integration.py.
    """

    def setUp(self):
        """
        Thiết lập cho mỗi test case.
        """
        # Tạo mock cho agent
        self.agent = MagicMock()
        
        # Tạo mock cho AdaptiveCrawler
        self.adaptive_crawler = MagicMock()
        self.agent._adaptive_crawler = self.adaptive_crawler

    def test_deep_crawl_with_adaptive_crawler_success(self):
        """
        Test deep_crawl_with_adaptive_crawler với kết quả thành công.
        """
        # Thiết lập mock cho AdaptiveCrawler.crawl
        self.adaptive_crawler.crawl.return_value = {
            "success": True,
            "results": [
                {
                    "url": "https://example.com",
                    "content": "Example content 1",
                    "links": [{"url": "https://example.com/page1", "text": "Page 1"}],
                    "images": [{"url": "https://example.com/image1.jpg", "alt": "Image 1"}],
                    "files": [{"url": "https://example.com/file1.pdf", "filename": "file1.pdf"}],
                    "metadata": {"title": "Example Page 1"},
                    "structured_data": {"json_ld": [{"@type": "WebPage"}]},
                    "downloaded_files": ["/path/to/file1.pdf"]
                },
                {
                    "url": "https://example.com/page1",
                    "content": "Example content 2",
                    "links": [{"url": "https://example.com/page2", "text": "Page 2"}],
                    "images": [{"url": "https://example.com/image2.jpg", "alt": "Image 2"}],
                    "files": [{"url": "https://example.com/file2.pdf", "filename": "file2.pdf"}],
                    "metadata": {"title": "Example Page 2"},
                    "structured_data": {"json_ld": [{"@type": "Article"}]},
                    "downloaded_files": ["/path/to/file2.pdf"]
                }
            ]
        }

        # Gọi hàm cần test
        result = deep_crawl_with_adaptive_crawler(
            self.agent,
            "https://example.com",
            max_depth=2,
            max_pages=10,
            crawl_mode="full_site",
            download_media=True,
            handle_javascript=True,
            language="vi"
        )

        # Kiểm tra kết quả
        self.assertTrue(result["success"])
        self.assertEqual(result["url"], "https://example.com")
        self.assertEqual(result["content"], "Example content 1\n\nExample content 2")
        self.assertEqual(len(result["links"]), 2)
        self.assertEqual(len(result["images"]), 2)
        self.assertEqual(len(result["files"]), 2)
        self.assertEqual(result["metadata"], {"title": "Example Page 1", "title": "Example Page 2"})
        self.assertEqual(len(result["structured_data"]["json_ld"]), 2)
        self.assertEqual(len(result["downloaded_files"]), 2)
        self.assertEqual(result["crawl_stats"]["pages_crawled"], 2)
        self.assertEqual(result["crawl_stats"]["total_links"], 2)
        self.assertEqual(result["crawl_stats"]["total_images"], 2)
        self.assertEqual(result["crawl_stats"]["total_files"], 2)
        self.assertEqual(result["crawl_stats"]["total_downloaded_files"], 2)
        self.assertEqual(result["crawl_stats"]["crawl_mode"], "full_site")
        self.assertEqual(result["crawl_stats"]["language"], "vi")
        self.assertEqual(result["crawl_stats"]["max_depth"], 2)
        self.assertEqual(result["crawl_stats"]["complexity_level"], "complex")

        # Kiểm tra các tham số gọi hàm
        self.adaptive_crawler.crawl.assert_called_once()
        call_args = self.adaptive_crawler.crawl.call_args[1]
        self.assertEqual(call_args["urls"], ["https://example.com"])
        self.assertEqual(call_args["max_depth"], 2)
        self.assertEqual(call_args["max_pages"], 10)
        self.assertEqual(call_args["crawl_mode"], "full_site")
        self.assertEqual(call_args["download_media"], True)
        self.assertEqual(call_args["handle_javascript"], True)
        self.assertEqual(call_args["language"], "vi")

    def test_deep_crawl_with_adaptive_crawler_failure(self):
        """
        Test deep_crawl_with_adaptive_crawler với kết quả thất bại.
        """
        # Thiết lập mock cho AdaptiveCrawler.crawl
        self.adaptive_crawler.crawl.return_value = {
            "success": False,
            "error": "Failed to crawl URL",
            "url": "https://example.com"
        }

        # Gọi hàm cần test
        result = deep_crawl_with_adaptive_crawler(
            self.agent,
            "https://example.com",
            max_depth=1
        )

        # Kiểm tra kết quả
        self.assertFalse(result["success"])
        self.assertEqual(result["error"], "Failed to crawl URL")
        self.assertEqual(result["url"], "https://example.com")
        self.assertIn("Failed to crawl URL", result["content"])

    def test_deep_crawl_with_adaptive_crawler_exception(self):
        """
        Test deep_crawl_with_adaptive_crawler với exception.
        """
        # Thiết lập mock cho AdaptiveCrawler.crawl để ném exception
        self.adaptive_crawler.crawl.side_effect = Exception("Test exception")

        # Gọi hàm cần test
        result = deep_crawl_with_adaptive_crawler(
            self.agent,
            "https://example.com"
        )

        # Kiểm tra kết quả
        self.assertFalse(result["success"])
        self.assertEqual(result["error"], "Crawl error: Test exception")
        self.assertEqual(result["url"], "https://example.com")
        self.assertIn("Test exception", result["content"])

if __name__ == "__main__":
    unittest.main()
