#!/usr/bin/env python3
"""
Test script đơn giản để kiểm tra tính năng tối ưu hóa truy vấn.
"""

import os
import json
import time
import re
import logging
import numpy as np
from typing import Dict, Any, List, Optional, Union, Set, Tuple
from datetime import datetime

# Cấ<PERSON> hình logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QueryOptimizer:
    """
    Lớp tối ưu hóa truy vấn tìm kiếm.
    """
    
    def __init__(
        self,
        language: str = "auto",
        cache_file: Optional[str] = None,
        enable_learning: bool = True,
        max_variants: int = 5
    ):
        """
        Khởi tạo QueryOptimizer.
        """
        self.language = language
        self.cache_file = cache_file
        self.enable_learning = enable_learning
        self.max_variants = max_variants
        
        # Khởi tạo cache cho các biến thể truy vấn thành công
        self.query_variants_cache = {}
        self.successful_variants = {}
        
        # Tải cache từ file nếu có
        if cache_file and os.path.exists(cache_file):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                    self.query_variants_cache = cache_data.get('query_variants', {})
                    self.successful_variants = cache_data.get('successful_variants', {})
                logger.info(f"Loaded query optimizer cache from {cache_file}")
            except Exception as e:
                logger.warning(f"Failed to load query optimizer cache: {str(e)}")
                self.query_variants_cache = {}
                self.successful_variants = {}
    
    def optimize(self, query: str, language: Optional[str] = None) -> Dict[str, Any]:
        """
        Phân tích và tối ưu hóa truy vấn.
        """
        start_time = time.time()
        
        # Kiểm tra cache nếu có
        query_key = query.lower().strip()
        if query_key in self.query_variants_cache:
            logger.info(f"Using cached variants for query: {query}")
            cached_result = self.query_variants_cache[query_key]
            # Cập nhật thời gian xử lý
            cached_result["processing_time"] = 0.001  # Thời gian xử lý rất nhanh khi dùng cache
            return cached_result
        
        # Phát hiện ngôn ngữ nếu cần
        detected_language = self._detect_language(query, language)
        
        # Trích xuất từ khóa
        keywords = self._extract_keywords(query, detected_language)
        
        # Phát hiện lĩnh vực
        domains = self._detect_domains(keywords, query)
        
        # Tạo biến thể truy vấn
        variants = []
        
        # Ưu tiên các biến thể đã thành công trước đây
        if self.enable_learning and query_key in self.successful_variants:
            successful_variants = self.successful_variants[query_key]
            logger.info(f"Using {len(successful_variants)} successful variants for query: {query}")
            variants.extend(successful_variants)
        
        # Thêm biến thể từ quy tắc
        rule_variants = self._generate_rule_variants(query, keywords, domains, detected_language)
        variants.extend(rule_variants)
        
        # Loại bỏ trùng lặp
        variants = list(set(variants))
        
        # Giới hạn số lượng biến thể
        variants = variants[:self.max_variants]
        
        # Tạo kết quả
        result = {
            "original_query": query,
            "language": detected_language,
            "keywords": keywords,
            "domains": domains,
            "variants": variants,
            "processing_time": time.time() - start_time
        }
        
        # Lưu vào cache
        self.query_variants_cache[query_key] = result
        
        # Lưu cache vào file nếu có
        self._save_cache()
        
        return result
    
    def record_successful_variant(self, original_query: str, successful_variant: str) -> None:
        """
        Ghi nhận biến thể truy vấn thành công.
        """
        if not self.enable_learning:
            return
            
        query_key = original_query.lower().strip()
        
        # Khởi tạo danh sách nếu chưa có
        if query_key not in self.successful_variants:
            self.successful_variants[query_key] = []
        
        # Thêm biến thể thành công nếu chưa có
        if successful_variant not in self.successful_variants[query_key]:
            self.successful_variants[query_key].append(successful_variant)
            logger.info(f"Recorded successful variant for query '{original_query}': '{successful_variant}'")
            
            # Lưu cache vào file nếu có
            self._save_cache()
    
    def _save_cache(self) -> None:
        """
        Lưu cache vào file.
        """
        if not self.cache_file:
            return
            
        try:
            cache_data = {
                'query_variants': self.query_variants_cache,
                'successful_variants': self.successful_variants
            }
            
            # Tạo thư mục cha nếu chưa tồn tại
            cache_dir = os.path.dirname(self.cache_file)
            if cache_dir and not os.path.exists(cache_dir):
                os.makedirs(cache_dir)
                
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
                
            logger.info(f"Saved query optimizer cache to {self.cache_file}")
        except Exception as e:
            logger.warning(f"Failed to save query optimizer cache: {str(e)}")
    
    def _detect_language(self, query: str, language: Optional[str] = None) -> str:
        """
        Phát hiện ngôn ngữ của truy vấn.
        """
        if language and language != "auto":
            return language
        
        if self.language != "auto":
            return self.language
        
        # Kiểm tra các ký tự tiếng Việt
        if any(c in query for c in "áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđ"):
            return "vi"
        
        # Mặc định là tiếng Anh
        return "en"
    
    def _extract_keywords(self, query: str, language: str) -> List[str]:
        """
        Trích xuất từ khóa từ truy vấn.
        """
        # Chuyển về chữ thường
        query_lower = query.lower()
        
        # Loại bỏ dấu câu
        query_clean = re.sub(r'[.,?!;:()[\]{}\"\'"]', ' ', query_lower)
        
        # Xử lý đặc biệt cho tiếng Việt
        if language == "vi":
            # Giữ nguyên các từ ghép phổ biến
            common_vi_compounds = [
                "trí tuệ nhân tạo", "học máy", "học sâu", "xử lý ngôn ngữ", "thị giác máy tính",
                "tiếng anh", "tiếng việt", "biến đổi khí hậu", "năng lượng tái tạo",
                "phát triển bền vững", "nông nghiệp", "việt nam", "phương pháp", "hiệu quả"
            ]
            
            # Tìm và giữ nguyên các từ ghép
            for compound in common_vi_compounds:
                if compound in query_clean:
                    query_clean = query_clean.replace(compound, compound.replace(" ", "_"))
            
            # Tách từ
            words = query_clean.split()
            # Khôi phục các từ ghép
            words = [word.replace("_", " ") for word in words]
        else:
            # Tách từ cho các ngôn ngữ khác
            words = query_clean.split()
        
        # Loại bỏ stopwords
        stopwords = self._load_stopwords(language)
        keywords = [word for word in words if word.lower() not in stopwords]
        
        # Loại bỏ từ quá ngắn (ngoại trừ một số từ đặc biệt)
        special_short_words = ["ai", "ml", "dl", "rl", "ar", "vr", "5g", "iot", "nlp"]
        keywords = [
            keyword for keyword in keywords 
            if len(keyword) > 2 or keyword.lower() in special_short_words
        ]
        
        return keywords
    
    def _load_stopwords(self, language: str) -> List[str]:
        """
        Tải danh sách stopwords theo ngôn ngữ.
        """
        if language == "vi":
            return ["và", "hoặc", "những", "các", "là", "của", "có", "trong", "cho", "với", "về", 
                    "như", "không", "được", "đã", "sẽ", "đang", "rằng", "thì", "mà", "nên", "khi"]
        else:
            return ["and", "or", "the", "is", "of", "in", "for", "with", "about", "to", "that", 
                    "this", "these", "those", "it", "they", "he", "she", "we", "you", "as", "at"]
    
    def _detect_domains(self, keywords: List[str], query: str) -> List[str]:
        """
        Phát hiện lĩnh vực của truy vấn.
        """
        domains = set()
        query_lower = query.lower()
        
        # Kiểm tra từ khóa AI
        ai_keywords = ["ai", "artificial intelligence", "trí tuệ nhân tạo", "machine learning", "học máy"]
        if any(kw in query_lower for kw in ai_keywords):
            domains.add("AI")
        
        # Kiểm tra từ khóa công nghệ
        tech_keywords = ["technology", "công nghệ", "tech", "software", "phần mềm", "hardware", "phần cứng"]
        if any(kw in query_lower for kw in tech_keywords):
            domains.add("technology")
        
        # Kiểm tra từ khóa xu hướng
        trend_keywords = ["trend", "xu hướng", "latest", "mới nhất", "future", "tương lai"]
        if any(kw in query_lower for kw in trend_keywords):
            domains.add("trend")
        
        # Kiểm tra từ khóa thời gian
        time_keywords = ["2024", "2025", "năm 2024", "năm 2025"]
        if any(kw in query_lower for kw in time_keywords):
            domains.add("time")
        
        return list(domains)
    
    def _generate_rule_variants(self, query: str, keywords: List[str], domains: List[str], language: str) -> List[str]:
        """
        Tạo biến thể truy vấn dựa trên quy tắc.
        """
        variants = []
        current_year = datetime.now().year
        
        # Biến thể 1: Kết hợp các từ khóa chính
        if len(keywords) >= 3:
            variants.append(" ".join(keywords[:3]))
        
        # Biến thể 2: Thêm từ khóa thời gian nếu có
        if "time" not in domains and not any(str(current_year) in query or str(current_year+1) in query):
            if language == "vi":
                variants.append(f"{query} {current_year}")
            else:
                variants.append(f"{query} {current_year}")
        
        # Biến thể 3: Thêm từ khóa "mới nhất" nếu truy vấn có vẻ tìm kiếm thông tin mới
        if "trend" not in domains:
            if language == "vi" and "mới nhất" not in query.lower():
                variants.append(f"{query} mới nhất")
            elif language == "en" and "latest" not in query.lower():
                variants.append(f"{query} latest")
        
        # Biến thể 4: Tạo truy vấn dạng câu hỏi nếu truy vấn gốc không phải câu hỏi
        if "?" not in query:
            if language == "vi":
                variants.append(f"Những {' '.join(keywords[:3])} là gì?")
            else:
                variants.append(f"What are {' '.join(keywords[:3])}?")
        
        # Biến thể 5: Kết hợp lĩnh vực
        if "AI" in domains and "trend" in domains:
            if language == "vi":
                variants.append(f"xu hướng trí tuệ nhân tạo mới nhất {current_year}")
            else:
                variants.append(f"latest AI trends {current_year}")
        
        return variants

def test_query_optimizer():
    """
    Kiểm tra tính năng tối ưu hóa truy vấn.
    """
    print("\n=== Kiểm tra QueryOptimizer ===\n")
    
    # Danh sách các truy vấn để kiểm tra
    test_queries = [
        "Những xu hướng AI mới nhất trong năm 2025?",
        "Python programming for beginners",
        "Cách học tiếng Anh hiệu quả",
        "Tác động của biến đổi khí hậu đến nông nghiệp Việt Nam",
        "Best machine learning frameworks 2024"
    ]
    
    # Khởi tạo QueryOptimizer với cache
    cache_file = "query_optimizer_cache.json"
    optimizer = QueryOptimizer(
        language="auto",
        cache_file=cache_file,
        enable_learning=True
    )
    
    # Kiểm tra từng truy vấn
    for query in test_queries:
        print(f"\nTruy vấn gốc: {query}")
        
        # Tối ưu hóa truy vấn
        try:
            result = optimizer.optimize(query)
            
            # In kết quả
            print(f"Ngôn ngữ: {result.get('language', 'không xác định')}")
            print(f"Từ khóa: {', '.join(result.get('keywords', []))}")
            print(f"Lĩnh vực: {', '.join(result.get('domains', []))}")
            
            print("Biến thể truy vấn:")
            for i, variant in enumerate(result.get("variants", []), 1):
                print(f"  {i}. {variant}")
            
            print(f"Thời gian xử lý: {result.get('processing_time', 0):.4f} giây")
            
            # Ghi nhận biến thể thành công (giả lập)
            if result.get("variants"):
                # Giả lập biến thể thành công là biến thể đầu tiên
                successful_variant = result.get("variants")[0]
                optimizer.record_successful_variant(query, successful_variant)
                print(f"Đã ghi nhận biến thể thành công: {successful_variant}")
        except Exception as e:
            print(f"Lỗi khi tối ưu hóa truy vấn: {e}")
    
    # Kiểm tra cache
    if os.path.exists(cache_file):
        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
                print("\n=== Nội dung cache ===\n")
                print(f"Số lượng truy vấn trong cache: {len(cache_data.get('query_variants', {}))}")
                print(f"Số lượng biến thể thành công: {len(cache_data.get('successful_variants', {}))}")
                
                # In chi tiết các biến thể thành công
                print("\nCác biến thể thành công:")
                for query, variants in cache_data.get('successful_variants', {}).items():
                    print(f"  Truy vấn: {query}")
                    for i, variant in enumerate(variants, 1):
                        print(f"    {i}. {variant}")
        except Exception as e:
            print(f"Lỗi khi đọc cache: {e}")

def main():
    """
    Hàm chính.
    """
    # Kiểm tra QueryOptimizer
    test_query_optimizer()

if __name__ == "__main__":
    main()
