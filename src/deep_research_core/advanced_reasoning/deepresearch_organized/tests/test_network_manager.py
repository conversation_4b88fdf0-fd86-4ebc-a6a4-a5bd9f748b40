#!/usr/bin/env python3
"""
Tests for NetworkManager and network utils.

This file contains tests for the NetworkManager module and network utilities.
"""

import unittest
import time
import responses
import requests
from unittest.mock import patch, MagicMock
from urllib3.exceptions import ConnectTimeoutError

from src.deep_research_core.agents.network_manager import NetworkManager
from src.deep_research_core.agents.network_utils import (
    http_get, http_post, with_retry, with_adaptive_timeout,
    create_default_headers, RetrySession, get_network_manager,
    configure_network_manager
)

class TestNetworkManager(unittest.TestCase):
    """Test cases for NetworkManager."""
    
    def setUp(self):
        """Set up test environment."""
        self.network_manager = NetworkManager(
            base_timeout=5.0,
            max_timeout=15.0,
            min_timeout=2.0,
            max_retries=2,
            backoff_factor=0.1,
            network_conditions="normal"
        )
    
    def test_initialization(self):
        """Test NetworkManager initialization."""
        self.assertEqual(self.network_manager.base_timeout, 5.0)
        self.assertEqual(self.network_manager.max_timeout, 15.0)
        self.assertEqual(self.network_manager.min_timeout, 2.0)
        self.assertEqual(self.network_manager.max_retries, 2)
        self.assertEqual(self.network_manager.backoff_factor, 0.1)
        self.assertEqual(self.network_manager.network_conditions, "normal")
        
    def test_get_domain_from_url(self):
        """Test _get_domain_from_url method."""
        self.assertEqual(
            self.network_manager._get_domain_from_url("https://www.example.com/page?query=123"),
            "www.example.com"
        )
        self.assertEqual(
            self.network_manager._get_domain_from_url("http://example.com/path/to/resource"),
            "example.com"
        )
        
    def test_get_timeout_for_url(self):
        """Test _get_timeout_for_url method."""
        # Test default timeout
        timeout = self.network_manager._get_timeout_for_url("https://www.example.com")
        self.assertEqual(timeout, 5.0)
        
        # Test with content type
        pdf_timeout = self.network_manager._get_timeout_for_url(
            "https://www.example.com/document.pdf", 
            content_type="application/pdf"
        )
        self.assertEqual(pdf_timeout, 10.0)  # 5.0 * 2.0
        
        # Test with domain-specific timeout
        self.network_manager.domain_timeouts["www.slowsite.com"] = 8.0
        slow_timeout = self.network_manager._get_timeout_for_url("https://www.slowsite.com")
        self.assertEqual(slow_timeout, 8.0)
        
    def test_network_conditions(self):
        """Test network conditions adjustment."""
        # Test slow network
        slow_manager = NetworkManager(
            base_timeout=5.0,
            max_timeout=10.0,
            min_timeout=2.0,
            network_conditions="slow"
        )
        self.assertEqual(slow_manager.base_timeout, 10.0)  # 5.0 * 2.0
        self.assertEqual(slow_manager.max_timeout, 20.0)   # 10.0 * 2.0
        self.assertEqual(slow_manager.min_timeout, 3.0)    # 2.0 * 1.5
        
        # Test fast network
        fast_manager = NetworkManager(
            base_timeout=5.0,
            max_timeout=10.0,
            min_timeout=2.0,
            network_conditions="fast"
        )
        self.assertEqual(fast_manager.base_timeout, 3.5)   # 5.0 * 0.7
        self.assertEqual(fast_manager.max_timeout, 7.0)    # 10.0 * 0.7
        self.assertEqual(fast_manager.min_timeout, 1.4)    # 2.0 * 0.7
        
    def test_update_domain_timeout(self):
        """Test _update_domain_timeout method."""
        domain = "www.example.com"
        
        # Update with a response time
        self.network_manager._update_domain_timeout(domain, 3.5)
        
        # Check if history contains the update
        self.assertEqual(len(self.network_manager.response_time_history), 1)
        self.assertEqual(self.network_manager.response_time_history[0][0], domain)
        self.assertEqual(self.network_manager.response_time_history[0][1], 3.5)
        
        # Check if domain timeout was updated
        self.assertIn(domain, self.network_manager.domain_timeouts)
        self.assertEqual(self.network_manager.domain_timeouts[domain], 5.25)  # 3.5 * 1.5
        
    def test_user_agent_rotation(self):
        """Test user agent rotation."""
        # Get multiple user agents
        ua1 = self.network_manager._get_next_user_agent()
        ua2 = self.network_manager._get_next_user_agent()
        
        # They should be different
        self.assertNotEqual(ua1, ua2)
        
        # After cycling through all agents, it should return to the first one
        for _ in range(len(self.network_manager.user_agent_list) - 2):
            self.network_manager._get_next_user_agent()
            
        ua_cycle = self.network_manager._get_next_user_agent()
        self.assertEqual(ua_cycle, ua1)
        
    @responses.activate
    def test_request_success(self):
        """Test successful request."""
        # Set up mock response
        responses.add(
            responses.GET,
            "https://www.example.com/test",
            json={"status": "ok"},
            status=200
        )
        
        # Make request
        response = self.network_manager.get(
            "https://www.example.com/test"
        )
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {"status": "ok"})
        
    @patch('requests.Session.request')
    def test_retry_on_connection_error(self, mock_request):
        """Test retry on connection error."""
        # Set up mock to fail twice then succeed
        mock_request.side_effect = [
            requests.exceptions.ConnectionError("Connection error"),
            requests.exceptions.ConnectionError("Connection error"),
            MagicMock(status_code=200)
        ]
        
        # Make request
        response = self.network_manager.get(
            "https://www.example.com/test",
            max_retries=3
        )
        
        # Check if request was called 3 times
        self.assertEqual(mock_request.call_count, 3)
        
    @patch('requests.Session.request')
    def test_retry_on_timeout(self, mock_request):
        """Test retry on timeout."""
        # Set up mock to fail with timeout then succeed
        mock_request.side_effect = [
            requests.exceptions.Timeout("Request timed out"),
            MagicMock(status_code=200)
        ]
        
        # Make request
        response = self.network_manager.get(
            "https://www.example.com/test"
        )
        
        # Check if request was called twice
        self.assertEqual(mock_request.call_count, 2)
        
        # Check if timeout was increased for the second attempt
        # Get the timeout value from the second call
        second_call_timeout = mock_request.call_args_list[1][1]['timeout']
        first_call_timeout = mock_request.call_args_list[0][1]['timeout']
        
        # Second timeout should be larger than first
        self.assertGreater(second_call_timeout, first_call_timeout)

class TestNetworkUtils(unittest.TestCase):
    """Test cases for network utilities."""
    
    def setUp(self):
        """Set up test environment."""
        # Reset network manager singleton
        from src.deep_research_core.agents.network_utils import _network_manager
        from src.deep_research_core.agents.network_utils import get_network_manager
        
        # Reset singleton
        type(get_network_manager()).__network_manager = None
        
    def test_get_network_manager(self):
        """Test get_network_manager function."""
        # Get manager
        manager1 = get_network_manager()
        
        # Get again, should be the same instance
        manager2 = get_network_manager()
        
        # Check if they are the same instance
        self.assertIs(manager1, manager2)
        
    def test_configure_network_manager(self):
        """Test configure_network_manager function."""
        # Configure manager
        configure_network_manager(
            base_timeout=7.0,
            max_timeout=20.0,
            min_timeout=3.0,
            max_retries=4,
            backoff_factor=0.2,
            network_conditions="slow"
        )
        
        # Get manager
        manager = get_network_manager()
        
        # Check configuration
        self.assertEqual(manager.base_timeout, 14.0)  # 7.0 * 2 for slow network
        self.assertEqual(manager.max_timeout, 40.0)  # 20.0 * 2 for slow network
        self.assertEqual(manager.min_timeout, 4.5)   # 3.0 * 1.5 for slow network
        self.assertEqual(manager.max_retries, 4)
        self.assertEqual(manager.backoff_factor, 0.2)
        self.assertEqual(manager.network_conditions, "slow")
        
    def test_create_default_headers(self):
        """Test create_default_headers function."""
        # Create headers
        headers = create_default_headers(
            user_agent="Custom User Agent",
            accept_language="vi-VN,vi;q=0.9",
            referer="https://www.referrer.com"
        )
        
        # Check headers
        self.assertEqual(headers["User-Agent"], "Custom User Agent")
        self.assertEqual(headers["Accept-Language"], "vi-VN,vi;q=0.9")
        self.assertEqual(headers["Referer"], "https://www.referrer.com")
        
    @responses.activate
    def test_http_get(self):
        """Test http_get function."""
        # Set up mock response
        responses.add(
            responses.GET,
            "https://www.example.com/api",
            json={"data": "test"},
            status=200
        )
        
        # Make request
        response = http_get("https://www.example.com/api")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {"data": "test"})
        
    @responses.activate
    def test_http_post(self):
        """Test http_post function."""
        # Set up mock response
        responses.add(
            responses.POST,
            "https://www.example.com/api",
            json={"status": "created"},
            status=201
        )
        
        # Make request
        response = http_post(
            "https://www.example.com/api",
            json={"name": "Test"}
        )
        
        # Check response
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.json(), {"status": "created"})
        
    def test_with_retry_decorator(self):
        """Test with_retry decorator."""
        mock = MagicMock()
        mock.side_effect = [
            ValueError("Test error"),
            ValueError("Test error"),
            "success"
        ]
        
        # Create decorated function
        @with_retry(max_retries=3, backoff_factor=0.01)
        def test_func():
            return mock()
        
        # Call function
        result = test_func()
        
        # Check result
        self.assertEqual(result, "success")
        self.assertEqual(mock.call_count, 3)
        
    def test_with_adaptive_timeout_decorator(self):
        """Test with_adaptive_timeout decorator."""
        # Create a mock for NetworkManager.get_effective_timeout
        with patch('src.deep_research_core.agents.network_manager.NetworkManager.get_effective_timeout') as mock_get_timeout:
            mock_get_timeout.return_value = 7.5
            
            # Create decorated function
            @with_adaptive_timeout(base_timeout=5.0)
            def test_func(url, **kwargs):
                return kwargs.get('timeout')
            
            # Call function with URL
            timeout = test_func("https://www.example.com")
            
            # Check if timeout was set correctly
            self.assertEqual(timeout, 7.5)
            
            # Call function without URL
            timeout_default = test_func(None)
            
            # Check if default timeout was used
            self.assertEqual(timeout_default, 5.0)
            
    def test_retry_session(self):
        """Test RetrySession class."""
        # Create session
        session = RetrySession(
            base_timeout=8.0,
            max_retries=3,
            backoff_factor=0.3,
            status_forcelist=[500, 502]
        )
        
        # Check configuration
        self.assertEqual(session.base_timeout, 8.0)
        self.assertEqual(session.max_retries, 3)
        self.assertEqual(session.backoff_factor, 0.3)
        self.assertEqual(session.status_forcelist, [500, 502])
        
        # Test method override
        with patch('src.deep_research_core.agents.network_manager.NetworkManager.request') as mock_request:
            mock_request.return_value = MagicMock(status_code=200)
            
            # Make request
            session.get("https://www.example.com")
            
            # Check if NetworkManager.request was called
            mock_request.assert_called_once()

if __name__ == '__main__':
    unittest.main() 