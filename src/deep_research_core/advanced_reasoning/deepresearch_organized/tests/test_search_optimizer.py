#!/usr/bin/env python3

import re
import time
import json
from typing import Dict, Any, List
import logging
import sys

# Create a simple logger
logger = logging.getLogger("search_optimizer")
handler = logging.StreamHandler(sys.stdout)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)
logger.setLevel(logging.INFO)

class SearchResultOptimizer:
    """
    Lớp tối ưu hóa kết quả tìm kiếm.
    """

    def __init__(
        self,
        relevance_threshold: float = 0.3,
        quality_threshold: float = 0.2,
        max_content_length: int = 5000,
        min_content_length: int = 100,
        max_results: int = 5,
        min_results: int = 2,
        ideal_total_length: int = 8000
    ):
        """
        Khởi tạo SearchResultOptimizer.
        """
        self.relevance_threshold = relevance_threshold
        self.quality_threshold = quality_threshold
        self.max_content_length = max_content_length
        self.min_content_length = min_content_length
        self.max_results = max_results
        self.min_results = min_results
        self.ideal_total_length = ideal_total_length

        # Danh sách từ khóa theo lĩnh vực
        self.domain_keywords = {
            "technology": ["programming", "software", "hardware", "computer", "algorithm", "code", "developer", "application", "system", "technology"],
            "health": ["health", "medical", "disease", "treatment", "symptom", "doctor", "patient", "hospital", "medicine", "diagnosis"],
            "finance": ["finance", "money", "investment", "stock", "market", "bank", "economy", "financial", "business", "trading"],
            "education": ["education", "school", "student", "teacher", "learning", "university", "college", "course", "academic", "study"],
            "science": ["science", "scientific", "research", "experiment", "theory", "physics", "chemistry", "biology", "mathematics", "scientist"]
        }

    def optimize(self, search_results: Dict[str, Any], query: str) -> Dict[str, Any]:
        """
        Tối ưu hóa kết quả tìm kiếm.
        """
        logger.info(f"Optimizing search results for query: {query}")

        # Kiểm tra kết quả tìm kiếm
        if not search_results.get("success") or not search_results.get("results"):
            logger.warning("No search results to optimize")
            return search_results

        # Lấy danh sách kết quả
        results = search_results.get("results", [])

        # Xác định lĩnh vực của truy vấn
        domain = self._detect_domain(query)
        logger.info(f"Detected domain for query: {domain}")

        # Lọc kết quả không liên quan
        filtered_results = []
        for result in results:
            # Bỏ qua các kết quả không liên quan
            if not self._is_relevant(result, query, domain):
                continue

            # Tính điểm liên quan
            relevance_score = self._calculate_relevance_score(result, query, domain)
            result["relevance_score"] = relevance_score

            # Chỉ giữ lại kết quả có điểm liên quan cao hơn ngưỡng
            if relevance_score >= self.relevance_threshold:
                filtered_results.append(result)

        # Sắp xếp kết quả theo điểm liên quan
        filtered_results.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)

        # Giới hạn độ dài nội dung
        total_content_length = 0
        for result in filtered_results:
            content = result.get("content", "")

            # Bỏ qua nội dung quá ngắn
            if len(content) < self.min_content_length:
                result["content_quality"] = "too_short"
                continue

            # Cắt nội dung quá dài
            if len(content) > self.max_content_length:
                result["content"] = content[:self.max_content_length] + "..."
                result["content_quality"] = "truncated"
            else:
                result["content_quality"] = "good"

            total_content_length += len(result.get("content", ""))

        # Đánh giá chất lượng tổng thể
        quality_metrics = self._evaluate_quality(filtered_results, query, total_content_length)

        # Giới hạn số lượng kết quả
        if len(filtered_results) > self.max_results:
            filtered_results = filtered_results[:self.max_results]

        # Tạo kết quả tối ưu
        optimized_results = {
            "success": search_results.get("success", True),
            "query": query,
            "domain": domain,
            "results": filtered_results,
            "count": len(filtered_results),
            "total_content_length": total_content_length,
            "quality_metrics": quality_metrics,
            "optimized": True,
            "timestamp": time.time()
        }

        logger.info(f"Optimization completed: {len(results)} -> {len(filtered_results)} results")

        return optimized_results

    def _detect_domain(self, query: str) -> str:
        """
        Xác định lĩnh vực của truy vấn.
        """
        query_lower = query.lower()
        domain_scores = {}

        # Tính điểm cho mỗi lĩnh vực
        for domain, keywords in self.domain_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword in query_lower:
                    score += 1
            domain_scores[domain] = score

        # Chọn lĩnh vực có điểm cao nhất
        if max(domain_scores.values()) > 0:
            return max(domain_scores.items(), key=lambda x: x[1])[0]

        # Nếu không có lĩnh vực nào phù hợp, trả về "general"
        return "general"

    def _is_relevant(self, result: Dict[str, Any], query: str, domain: str) -> bool:
        """
        Kiểm tra xem kết quả có liên quan đến truy vấn không.
        """
        query_lower = query.lower()
        title = result.get("title", "").lower()
        content = result.get("content", result.get("snippet", "")).lower()

        # Kiểm tra các trường hợp đặc biệt
        if "programming" in query_lower and "snake" in content and "python" in query_lower:
            return False

        # Kiểm tra từ khóa lĩnh vực
        if domain != "general":
            domain_keywords = self.domain_keywords.get(domain, [])
            if not any(keyword in title or keyword in content for keyword in domain_keywords):
                return False

        # Kiểm tra từ khóa truy vấn
        query_terms = query_lower.split()
        if not any(term in title or term in content for term in query_terms):
            return False

        return True

    def _calculate_relevance_score(self, result: Dict[str, Any], query: str, domain: str) -> float:
        """
        Tính điểm liên quan của kết quả.
        """
        query_lower = query.lower()
        title = result.get("title", "").lower()
        content = result.get("content", result.get("snippet", "")).lower()

        # Điểm cơ bản
        score = 0.0

        # Điểm cho tiêu đề
        query_terms = query_lower.split()
        title_score = sum(1 for term in query_terms if term in title) / max(1, len(query_terms))
        score += title_score * 0.5

        # Điểm cho nội dung
        content_score = sum(1 for term in query_terms if term in content) / max(1, len(query_terms))
        score += content_score * 0.3

        # Điểm cho lĩnh vực
        if domain != "general":
            domain_keywords = self.domain_keywords.get(domain, [])
            domain_score = sum(1 for keyword in domain_keywords if keyword in title or keyword in content) / max(1, len(domain_keywords))
            score += domain_score * 0.2

        return min(1.0, score)

    def _evaluate_quality(self, results: List[Dict[str, Any]], query: str, total_content_length: int) -> Dict[str, Any]:
        """
        Đánh giá chất lượng tổng thể của kết quả.
        """
        # Số lượng kết quả
        count = len(results)
        count_quality = "good"
        if count < self.min_results:
            count_quality = "too_few"
        elif count > self.max_results:
            count_quality = "too_many"

        # Độ dài nội dung
        length_quality = "good"
        if total_content_length < self.ideal_total_length * 0.5:
            length_quality = "too_short"
        elif total_content_length > self.ideal_total_length * 1.5:
            length_quality = "too_long"

        # Độ đa dạng
        domains = set()
        for result in results:
            url = result.get("url", "")
            if url:
                try:
                    domain = url.split("//")[1].split("/")[0]
                    domains.add(domain)
                except:
                    pass

        diversity_quality = "good"
        if len(domains) < 2 and count > 2:
            diversity_quality = "low"

        # Điểm liên quan trung bình
        avg_relevance = sum(result.get("relevance_score", 0) for result in results) / max(1, count)
        relevance_quality = "good"
        if avg_relevance < self.relevance_threshold:
            relevance_quality = "low"

        # Tổng hợp đánh giá
        overall_quality = "good"
        if count_quality != "good" or length_quality != "good" or diversity_quality != "good" or relevance_quality != "good":
            overall_quality = "needs_improvement"

        return {
            "count": {
                "value": count,
                "quality": count_quality
            },
            "content_length": {
                "value": total_content_length,
                "quality": length_quality
            },
            "diversity": {
                "value": len(domains),
                "quality": diversity_quality
            },
            "relevance": {
                "value": avg_relevance,
                "quality": relevance_quality
            },
            "overall": overall_quality
        }

def create_sample_results(query, results):
    """
    Tạo kết quả tìm kiếm mẫu.
    """
    return {
        "success": True,
        "query": query,
        "results": results,
        "timestamp": time.time()
    }

def evaluate_output_quality(optimized_results):
    """
    Đánh giá chất lượng đầu ra.
    """
    quality_metrics = optimized_results.get("quality_metrics", {})
    overall_quality = quality_metrics.get("overall", "unknown")

    # Kiểm tra số lượng kết quả
    count_metrics = quality_metrics.get("count", {})
    count = count_metrics.get("value", 0)
    count_quality = count_metrics.get("quality", "unknown")

    # Kiểm tra độ dài nội dung
    length_metrics = quality_metrics.get("content_length", {})
    total_length = length_metrics.get("value", 0)
    length_quality = length_metrics.get("quality", "unknown")

    # Kiểm tra độ đa dạng
    diversity_metrics = quality_metrics.get("diversity", {})
    diversity = diversity_metrics.get("value", 0)
    diversity_quality = diversity_metrics.get("quality", "unknown")

    # Kiểm tra độ liên quan
    relevance_metrics = quality_metrics.get("relevance", {})
    relevance = relevance_metrics.get("value", 0)
    relevance_quality = relevance_metrics.get("quality", "unknown")

    # Tính điểm tổng thể (0-10)
    score = 0
    if count_quality == "good":
        score += 2.5
    elif count_quality == "too_few":
        score += 1

    if length_quality == "good":
        score += 2.5
    elif length_quality == "too_short":
        score += 1
    elif length_quality == "too_long":
        score += 1.5

    if diversity_quality == "good":
        score += 2.5
    elif diversity_quality == "low":
        score += 1

    if relevance_quality == "good":
        score += 2.5
    elif relevance_quality == "low":
        score += 1

    # Đánh giá tổng thể
    assessment = ""
    if score >= 9:
        assessment = "Xuất sắc"
    elif score >= 7:
        assessment = "Tốt"
    elif score >= 5:
        assessment = "Đạt yêu cầu"
    elif score >= 3:
        assessment = "Cần cải thiện"
    else:
        assessment = "Không đạt yêu cầu"

    return {
        "score": score,
        "assessment": assessment,
        "details": {
            "count": {
                "value": count,
                "quality": count_quality
            },
            "content_length": {
                "value": total_length,
                "quality": length_quality
            },
            "diversity": {
                "value": diversity,
                "quality": diversity_quality
            },
            "relevance": {
                "value": relevance,
                "quality": relevance_quality
            }
        }
    }

def main():
    # Create an optimizer
    optimizer = SearchResultOptimizer(
        relevance_threshold=0.3,
        quality_threshold=0.2,
        max_content_length=2000,
        min_content_length=100,
        max_results=5,
        min_results=2,
        ideal_total_length=8000
    )

    # Danh sách các câu hỏi từ nhiều lĩnh vực
    test_queries = [
        # Công nghệ
        {
            "query": "Python programming for beginners",
            "domain": "technology",
            "results": [
                {
                    "title": "Python Programming Language",
                    "url": "https://www.python.org/",
                    "snippet": "Python is a programming language that lets you work quickly.",
                    "content": "Python is a programming language that lets you work quickly and integrate systems more effectively. Python is dynamically typed and garbage-collected. It supports multiple programming paradigms, including structured (particularly, procedural), object-oriented, and functional programming. It is often described as a 'batteries included' language due to its comprehensive standard library."
                },
                {
                    "title": "Learn Python - Free Interactive Python Tutorial",
                    "url": "https://www.learnpython.org/",
                    "snippet": "Learn Python, a powerful programming language.",
                    "content": "Learn Python, a powerful programming language used for many different applications. Python is a widely used high-level programming language for general-purpose programming, created by Guido van Rossum and first released in 1991. Python features a dynamic type system and automatic memory management and supports multiple programming paradigms."
                },
                {
                    "title": "Python Snake Facts",
                    "url": "https://www.snakefacts.com/python/",
                    "snippet": "Learn about python snakes, their habitat, diet, and behavior.",
                    "content": "Python snakes are some of the largest snakes in the world. They are constrictors, which means they coil around their prey and squeeze until the prey can no longer breathe. Pythons are found in Africa, Asia, and Australia."
                },
                {
                    "title": "Python for Beginners - W3Schools",
                    "url": "https://www.w3schools.com/python/python_intro.asp",
                    "snippet": "Python is a popular programming language. It was created by Guido van Rossum, and released in 1991.",
                    "content": "Python is a popular programming language. It was created by Guido van Rossum, and released in 1991. It is used for web development (server-side), software development, mathematics, system scripting. Python can be used on a server to create web applications. Python can be used alongside software to create workflows. Python can connect to database systems. It can also read and modify files."
                }
            ]
        },
        # Y tế
        {
            "query": "Symptoms of diabetes",
            "domain": "health",
            "results": [
                {
                    "title": "Diabetes - Symptoms and causes - Mayo Clinic",
                    "url": "https://www.mayoclinic.org/diseases-conditions/diabetes/symptoms-causes/syc-20371444",
                    "snippet": "Overview. Diabetes mellitus refers to a group of diseases that affect how your body uses blood sugar (glucose).",
                    "content": "Diabetes symptoms vary depending on how much your blood sugar is elevated. Some people, especially those with prediabetes or type 2 diabetes, may sometimes not experience symptoms. In type 1 diabetes, symptoms tend to come on quickly and be more severe. Some of the signs and symptoms of type 1 diabetes and type 2 diabetes are: Increased thirst, Frequent urination, Extreme hunger, Unexplained weight loss, Presence of ketones in the urine (ketones are a byproduct of the breakdown of muscle and fat that happens when there's not enough available insulin), Fatigue, Irritability, Blurred vision, Slow-healing sores, Frequent infections, such as gums or skin infections and vaginal infections."
                },
                {
                    "title": "Diabetes: Symptoms, Causes, Treatment, Prevention, and More",
                    "url": "https://www.healthline.com/health/diabetes",
                    "snippet": "Diabetes is a metabolic disease that causes high blood sugar. The hormone insulin moves sugar from the blood into your cells to be stored or used for energy.",
                    "content": "Diabetes is a group of diseases that affect how your body uses blood sugar (glucose). Glucose is vital to your health because it's an important source of energy for the cells that make up your muscles and tissues. It's also your brain's main source of fuel. The underlying cause of diabetes varies by type. But, no matter what type of diabetes you have, it can lead to excess sugar in your blood. Too much sugar in your blood can lead to serious health problems."
                },
                {
                    "title": "10 Warning Signs of Type 2 Diabetes - WebMD",
                    "url": "https://www.webmd.com/diabetes/10-diabetes-symptoms",
                    "snippet": "Learn about the warning signs and symptoms of type 2 diabetes and what to look out for.",
                    "content": "Type 2 diabetes is a chronic condition that affects the way your body metabolizes sugar (glucose) — an important source of fuel for your body. With type 2 diabetes, your body either resists the effects of insulin — a hormone that regulates the movement of sugar into your cells — or doesn't produce enough insulin to maintain normal glucose levels. Type 2 diabetes used to be known as adult-onset diabetes, but today more children are being diagnosed with the disorder, probably due to the rise in childhood obesity. There's no cure for type 2 diabetes, but losing weight, eating well and exercising can help manage the disease."
                }
            ]
        },
        # Tài chính
        {
            "query": "How to invest in stocks",
            "domain": "finance",
            "results": [
                {
                    "title": "How to Invest in Stocks: A Beginner's Guide - NerdWallet",
                    "url": "https://www.nerdwallet.com/article/investing/how-to-invest-in-stocks",
                    "snippet": "Investing in stocks means buying shares of ownership in a public company.",
                    "content": "Investing in stocks means buying shares of ownership in a public company. Those small shares are known as the company's stock, and by investing in it, you're hoping the company grows and performs well over time. If that happens, your shares may become more valuable, and other investors may be willing to buy them from you for more than you paid for them. That means you could earn a profit if you decide to sell them."
                },
                {
                    "title": "How to Invest in Stocks - Investopedia",
                    "url": "https://www.investopedia.com/articles/basics/06/invest1000.asp",
                    "snippet": "Investing in the stock market is one of the best ways to build wealth over the long term, but it can be intimidating for beginners.",
                    "content": "Investing in the stock market is one of the best ways to build wealth over the long term, but it can be intimidating for beginners. The good news is that it's actually quite straightforward to begin investing in stocks. Here's a step-by-step guide to help you get started: 1. Decide how you want to invest in stocks, 2. Choose an investing account, 3. Learn the difference between investing in stocks and funds, 4. Set a budget for your stock investment, 5. Focus on the long-term, 6. Manage your stock portfolio."
                }
            ]
        },
        # Giáo dục
        {
            "query": "Phương pháp học tiếng Anh hiệu quả",
            "domain": "education",
            "results": [
                {
                    "title": "10 phương pháp học tiếng Anh hiệu quả nhất",
                    "url": "https://www.ef.com.vn/blog/language/10-phuong-phap-hoc-tieng-anh-hieu-qua/",
                    "snippet": "Bài viết giới thiệu 10 phương pháp học tiếng Anh hiệu quả giúp bạn cải thiện kỹ năng ngôn ngữ.",
                    "content": "Học tiếng Anh là một quá trình đòi hỏi sự kiên nhẫn và phương pháp phù hợp. Dưới đây là 10 phương pháp học tiếng Anh hiệu quả: 1. Học từ vựng theo chủ đề, 2. Luyện nghe qua phim ảnh và bài hát, 3. Nói chuyện với người bản xứ, 4. Đọc sách báo tiếng Anh hàng ngày, 5. Sử dụng ứng dụng học tiếng Anh, 6. Tham gia các khóa học trực tuyến, 7. Luyện viết thường xuyên, 8. Học ngữ pháp qua ví dụ thực tế, 9. Tạo môi trường tiếng Anh xung quanh, 10. Duy trì thói quen học tập đều đặn."
                },
                {
                    "title": "Cách học tiếng Anh hiệu quả cho người mới bắt đầu",
                    "url": "https://www.britishcouncil.vn/hoc-tieng-anh/cach-hoc-tieng-anh-hieu-qua",
                    "snippet": "Những phương pháp học tiếng Anh hiệu quả dành cho người mới bắt đầu.",
                    "content": "Đối với người mới bắt đầu học tiếng Anh, việc xây dựng nền tảng vững chắc là rất quan trọng. Bạn nên bắt đầu với những từ vựng cơ bản và cấu trúc ngữ pháp đơn giản. Hãy tập trung vào việc phát âm đúng từ đầu để tránh hình thành thói quen xấu. Ngoài ra, việc học tiếng Anh cần được thực hiện đều đặn mỗi ngày, dù chỉ 15-30 phút. Điều này hiệu quả hơn nhiều so với việc học dồn một lúc nhiều giờ vào cuối tuần."
                },
                {
                    "title": "5 bí quyết học tiếng Anh nhanh và hiệu quả",
                    "url": "https://ielts-fighter.com/tin-tuc/5-bi-quyet-hoc-tieng-anh-nhanh-va-hieu-qua-20.html",
                    "snippet": "Bài viết chia sẻ 5 bí quyết giúp bạn học tiếng Anh nhanh và hiệu quả.",
                    "content": "Để học tiếng Anh nhanh và hiệu quả, bạn cần áp dụng những bí quyết sau: 1. Xác định mục tiêu rõ ràng: Biết bạn học tiếng Anh để làm gì sẽ giúp bạn có động lực và lộ trình học tập phù hợp. 2. Tạo thói quen học mỗi ngày: Việc học đều đặn giúp bạn tiến bộ nhanh hơn. 3. Học từ vựng theo chủ đề: Giúp bạn nhớ từ lâu hơn và áp dụng vào tình huống thực tế dễ dàng. 4. Luyện tập cả 4 kỹ năng: Nghe, nói, đọc, viết cần được phát triển đồng đều. 5. Tìm người cùng học hoặc gia sư: Có người đồng hành sẽ giúp bạn duy trì động lực học tập."
                }
            ]
        },
        # Khoa học
        {
            "query": "Quantum computing explained",
            "domain": "science",
            "results": [
                {
                    "title": "Quantum computing - Wikipedia",
                    "url": "https://en.wikipedia.org/wiki/Quantum_computing",
                    "snippet": "Quantum computing is a type of computation whose operations can harness the phenomena of quantum mechanics.",
                    "content": "Quantum computing is a type of computation whose operations can harness the phenomena of quantum mechanics, such as superposition, interference, and entanglement. Devices that perform quantum computations are known as quantum computers. Though current quantum computers are too small to outperform usual (classical) computers for practical applications, larger realizations are believed to be capable of solving certain computational problems, such as integer factorization (which underlies RSA encryption), substantially faster than classical computers."
                },
                {
                    "title": "What is quantum computing? - IBM",
                    "url": "https://www.ibm.com/topics/quantum-computing",
                    "snippet": "Quantum computing harnesses the phenomena of quantum mechanics to deliver a huge leap forward in computation.",
                    "content": "Quantum computing harnesses the phenomena of quantum mechanics to deliver a huge leap forward in computation to solve certain problems. IBM Quantum is advancing quantum computing with a mission to deliver a 4,000+ qubit system by 2025. Quantum computing is a rapidly-emerging technology that harnesses the laws of quantum mechanics to solve problems too complex for classical computers."
                },
                {
                    "title": "Quantum Computing: Progress and Prospects",
                    "url": "https://www.nap.edu/read/25196/chapter/1",
                    "snippet": "A report on the current state of quantum computing and its potential impact.",
                    "content": "Quantum computing is an emerging field that uses quantum mechanics to solve problems that are too complex for classical computers. Unlike classical bits, which can be either 0 or 1, quantum bits (qubits) can exist in multiple states simultaneously due to a property called superposition. This allows quantum computers to process a vast number of possibilities simultaneously. Another key property is entanglement, which allows qubits to be correlated in ways that have no classical analog. These properties give quantum computers the potential to solve certain problems exponentially faster than classical computers."
                }
            ]
        },
        # Câu hỏi ngắn
        {
            "query": "Capital of France",
            "domain": "general",
            "results": [
                {
                    "title": "Paris - Wikipedia",
                    "url": "https://en.wikipedia.org/wiki/Paris",
                    "snippet": "Paris is the capital and most populous city of France.",
                    "content": "Paris is the capital and most populous city of France, with an estimated population of 2,165,423 residents in 2019 in an area of more than 105 square kilometres (41 square miles), making it the 34th most densely populated city in the world in 2020. Since the 17th century, Paris has been one of the world's major centres of finance, diplomacy, commerce, fashion, gastronomy, science, and arts."
                }
            ]
        },
        # Câu hỏi phức tạp
        {
            "query": "Tác động của biến đổi khí hậu đến nông nghiệp Việt Nam và các giải pháp thích ứng",
            "domain": "science",
            "results": [
                {
                    "title": "Tác động của biến đổi khí hậu đến nông nghiệp Việt Nam",
                    "url": "https://www.mard.gov.vn/Pages/tac-dong-cua-bien-doi-khi-hau-den-nong-nghiep-viet-nam.aspx",
                    "snippet": "Nghiên cứu về tác động của biến đổi khí hậu đến nông nghiệp Việt Nam và các giải pháp thích ứng.",
                    "content": "Biến đổi khí hậu đang tác động nghiêm trọng đến nông nghiệp Việt Nam thông qua nhiều hiện tượng như nước biển dâng, xâm nhập mặn, hạn hán, lũ lụt và thay đổi nhiệt độ. Đồng bằng sông Cửu Long, vựa lúa của cả nước, đang phải đối mặt với nguy cơ mất đất canh tác do xâm nhập mặn và nước biển dâng. Theo các nghiên cứu, nếu mực nước biển dâng 1m, khoảng 40% diện tích đồng bằng sông Cửu Long sẽ bị ngập, ảnh hưởng đến 17 triệu người và làm mất 40% sản lượng nông nghiệp của vùng."
                },
                {
                    "title": "Các giải pháp thích ứng với biến đổi khí hậu trong nông nghiệp",
                    "url": "https://www.fao.org.vn/giải-pháp-thích-ứng-biến-đổi-khí-hậu-nông-nghiệp",
                    "snippet": "FAO đề xuất các giải pháp thích ứng với biến đổi khí hậu trong lĩnh vực nông nghiệp tại Việt Nam.",
                    "content": "Để thích ứng với biến đổi khí hậu, ngành nông nghiệp Việt Nam cần áp dụng nhiều giải pháp đồng bộ: 1. Phát triển các giống cây trồng chịu hạn, chịu mặn, 2. Điều chỉnh lịch thời vụ phù hợp với điều kiện khí hậu mới, 3. Áp dụng kỹ thuật canh tác tiết kiệm nước, 4. Phát triển hệ thống thủy lợi thông minh, 5. Đa dạng hóa cây trồng để giảm rủi ro, 6. Phát triển bảo hiểm nông nghiệp, 7. Tăng cường nghiên cứu và chuyển giao công nghệ, 8. Xây dựng hệ thống cảnh báo sớm về thiên tai và dịch bệnh."
                },
                {
                    "title": "Nghiên cứu mô hình nông nghiệp thích ứng với biến đổi khí hậu tại Việt Nam",
                    "url": "https://www.nchmf.gov.vn/nghien-cuu-mo-hinh-nong-nghiep-thich-ung",
                    "snippet": "Tổng hợp các mô hình nông nghiệp thích ứng với biến đổi khí hậu đã được triển khai tại Việt Nam.",
                    "content": "Nhiều mô hình nông nghiệp thích ứng với biến đổi khí hậu đã được triển khai thành công tại Việt Nam. Mô hình '1 phải 5 giảm' trong canh tác lúa giúp giảm lượng nước sử dụng, giảm phát thải khí nhà kính và tăng hiệu quả kinh tế. Mô hình tôm - lúa ở Đồng bằng sông Cửu Long giúp nông dân thích ứng với xâm nhập mặn, vừa canh tác lúa vào mùa mưa, vừa nuôi tôm vào mùa khô khi đất bị nhiễm mặn. Các hệ thống canh tác tổng hợp VAC (Vườn - Ao - Chuồng) giúp đa dạng hóa sản xuất, giảm rủi ro và tăng khả năng chống chịu với biến đổi khí hậu."
                }
            ]
        }
    ]

    # Chạy test và đánh giá kết quả
    results = []

    for test_case in test_queries:
        query = test_case["query"]
        domain = test_case["domain"]
        sample_results = create_sample_results(query, test_case["results"])

        print(f"\n{'='*80}")
        print(f"Đang kiểm tra truy vấn: '{query}' (Lĩnh vực: {domain})")
        print(f"{'='*80}")

        # Tối ưu hóa kết quả
        optimized = optimizer.optimize(sample_results, query)

        # Đánh giá chất lượng đầu ra
        quality = evaluate_output_quality(optimized)

        # In kết quả
        print(f"Số kết quả gốc: {len(sample_results['results'])}")
        print(f"Số kết quả sau tối ưu: {len(optimized['results'])}")
        print(f"Tổng độ dài nội dung: {optimized.get('total_content_length', 0)} ký tự")
        print(f"Lĩnh vực được phát hiện: {optimized.get('domain', 'unknown')}")

        print("\nĐánh giá chất lượng:")
        print(f"- Điểm tổng thể: {quality['score']}/10 ({quality['assessment']})")
        print(f"- Số lượng kết quả: {quality['details']['count']['value']} ({quality['details']['count']['quality']})")
        print(f"- Độ dài nội dung: {quality['details']['content_length']['value']} ký tự ({quality['details']['content_length']['quality']})")
        print(f"- Độ đa dạng: {quality['details']['diversity']['value']} nguồn ({quality['details']['diversity']['quality']})")
        print(f"- Độ liên quan: {quality['details']['relevance']['value']:.2f} ({quality['details']['relevance']['quality']})")

        # Lưu kết quả để tổng hợp
        results.append({
            "query": query,
            "domain": domain,
            "detected_domain": optimized.get("domain", "unknown"),
            "original_count": len(sample_results["results"]),
            "optimized_count": len(optimized["results"]),
            "content_length": optimized.get("total_content_length", 0),
            "quality_score": quality["score"],
            "assessment": quality["assessment"]
        })

    # Tổng hợp kết quả
    print(f"\n{'='*80}")
    print("TỔNG HỢP KẾT QUẢ")
    print(f"{'='*80}")
    print(f"Tổng số truy vấn đã kiểm tra: {len(results)}")

    avg_score = sum(r["quality_score"] for r in results) / len(results)
    print(f"Điểm chất lượng trung bình: {avg_score:.2f}/10")

    # Phân loại kết quả theo đánh giá
    assessments = {}
    for r in results:
        assessment = r["assessment"]
        if assessment not in assessments:
            assessments[assessment] = 0
        assessments[assessment] += 1

    print("\nPhân loại kết quả:")
    for assessment, count in assessments.items():
        print(f"- {assessment}: {count} truy vấn ({count/len(results)*100:.1f}%)")

    # Kiểm tra độ chính xác của việc phát hiện lĩnh vực
    domain_accuracy = sum(1 for r in results if r["domain"] == r["detected_domain"]) / len(results)
    print(f"\nĐộ chính xác phát hiện lĩnh vực: {domain_accuracy*100:.1f}%")

    print("\nTest completed successfully!")

if __name__ == "__main__":
    main()
