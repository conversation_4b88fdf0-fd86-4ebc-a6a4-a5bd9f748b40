#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Simple test script for WebSearchFileProcessor.
"""

import os
import sys
import shutil
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import WebSearchFileProcessor directly
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))
from src.deep_research_core.agents.web_search_file_processor import WebSearchFileProcessor

def main():
    """
    Main function to test WebSearchFileProcessor.
    """
    # Create a temporary directory for downloaded files
    download_dir = "test_downloads"
    if not os.path.exists(download_dir):
        os.makedirs(download_dir)
    
    try:
        # Initialize WebSearchFileProcessor
        file_processor = WebSearchFileProcessor(
            download_dir=download_dir
        )
        
        # Test is_file_url method
        logger.info("Testing is_file_url method...")
        
        # Test URLs that should be detected as files
        file_urls = [
            "https://www.example.com/documents/sample.pdf",
            "https://www.example.com/files/data.xlsx",
            "https://www.example.com/download/presentation.pptx",
            "https://www.example.com/attachments/document.docx",
            "https://www.example.com/file.txt",
            "https://www.example.com/download?file=report.pdf",
        ]
        
        # Test URLs that should not be detected as files
        non_file_urls = [
            "https://www.example.com",
            "https://www.example.com/about",
            "https://www.example.com/contact-us",
            "https://www.example.com/blog/post-1",
        ]
        
        # Check file URLs
        for url in file_urls:
            result = file_processor.is_file_url(url)
            logger.info(f"URL: {url}, Is file: {result}")
            assert result, f"URL {url} should be detected as a file URL"
        
        # Check non-file URLs
        for url in non_file_urls:
            result = file_processor.is_file_url(url)
            logger.info(f"URL: {url}, Is file: {result}")
            assert not result, f"URL {url} should not be detected as a file URL"
        
        # Test downloading a file
        logger.info("Testing download_file method...")
        
        # URL of a PDF file
        pdf_url = "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"
        
        # Download the file
        result = file_processor.download_file(pdf_url)
        
        # Check the result
        assert result["success"], "Download should be successful"
        assert result["file_path"], "File path should be provided"
        assert os.path.exists(result["file_path"]), "File should exist on disk"
        
        # Log file information
        logger.info(f"Downloaded file: {result['file_path']}")
        logger.info(f"File size: {result['size']} bytes")
        logger.info(f"Content type: {result['content_type']}")
        
        # If text content was extracted, check it
        if "text_content" in result and result["text_content"]:
            logger.info(f"Text content length: {len(result['text_content'])}")
            assert result["text_content"], "Text content should be extracted"
        
        logger.info("All tests passed!")
        return True
    
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        return False
    
    finally:
        # Clean up
        if os.path.exists(download_dir):
            shutil.rmtree(download_dir)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
