"""
Comprehensive test script for all modules in Deep Research Core.
"""

import os
import sys
import json
from typing import Dict, List, Any, Optional

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the modules to test
from src.deep_research_core.reasoning.source_attribution import SourceAttribution
from src.deep_research_core.reasoning.self_reflection import SelfReflection
from src.deep_research_core.reasoning.multi_query_decomposition import MultiQueryDecomposition
from src.deep_research_core.reasoning.multi_source_validator import MultiSourceValidator
from src.deep_research_core.reasoning.fact_checker import <PERSON><PERSON><PERSON><PERSON><PERSON>
from src.deep_research_core.reasoning.hybrid_search import HybridSearch
from src.deep_research_core.reasoning.semantic_chunking import SemanticChunking
from src.deep_research_core.reasoning.knowledge_graph_rag import KnowledgeGraphRAG
from src.deep_research_core.reasoning.knowledge_graph_reasoner import KnowledgeGraphReasoner
from src.deep_research_core.reasoning.tot import TreeOfThought

# Set up API key for OpenRouter
OPENROUTER_API_KEY = "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"

def test_source_attribution():
    """Test the SourceAttribution module."""
    print("\n=== Testing Source Attribution ===\n")

    try:
        # Initialize with OpenRouter
        attribution = SourceAttribution(
            citation_style="apa",
            track_token_level=True,
            language="en"
        )

        # Test documents
        documents = [
            {
                "title": "Climate Change Effects",
                "content": "Climate change is causing rising sea levels and extreme weather events.",
                "author": "Climate Research Institute",
                "publication_date": "2023"
            },
            {
                "title": "Renewable Energy Solutions",
                "content": "Solar and wind power are becoming increasingly cost-effective solutions.",
                "author": "Energy Innovation Lab",
                "publication_date": "2022"
            }
        ]

        # Register sources
        source_ids = []
        for i, doc in enumerate(documents):
            source_id = f"source_{i}"
            attribution.register_source(source_id, doc)
            source_ids.append(source_id)

        # Track information usage
        attribution.track_information_usage(
            "Climate change is causing rising sea levels.",
            source_ids[0]
        )

        # Generate citation
        citation = attribution.generate_citation(source_ids[0])

        # Get bibliography
        bibliography = attribution.get_bibliography()

        print(f"Citation: {citation}")
        print(f"Bibliography: {bibliography}")

        print("Source Attribution test passed!")
        return True
    except Exception as e:
        print(f"Error testing Source Attribution: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_self_reflection():
    """Test the SelfReflection module."""
    print("\n=== Testing Self Reflection ===\n")

    try:
        # Initialize with OpenRouter
        reflection = SelfReflection(
            provider="openrouter",
            model="anthropic/claude-3-opus",  # Default model for OpenRouter
            language="en"
        )

        # Set environment variable for OpenRouter API key
        import os
        os.environ["OPENROUTER_API_KEY"] = OPENROUTER_API_KEY

        # Test reasoning
        reasoning = """
        The Earth is flat because if it were round, people on the bottom would fall off.
        Water always finds its level, and flat water proves the Earth is flat.
        The horizon looks flat, which proves the Earth is flat.
        """

        # Reflect on reasoning
        reflection_result = reflection.reflect_on_reasoning(reasoning)

        # Correct reasoning
        correction_result = reflection.correct_reasoning(reasoning, reflection_result)

        print(f"Reflection result: {reflection_result}")
        print(f"Correction result: {correction_result}")

        print("Self Reflection test passed!")
        return True
    except Exception as e:
        print(f"Error testing Self Reflection: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_query_decomposition():
    """Test the MultiQueryDecomposition module."""
    print("\n=== Testing Multi Query Decomposition ===\n")

    try:
        # Initialize with OpenRouter
        decomposer = MultiQueryDecomposition(
            provider="openrouter",
            model="anthropic/claude-3-opus",  # Default model for OpenRouter
            language="en"
        )

        # Ensure environment variable for OpenRouter API key is set
        import os
        os.environ["OPENROUTER_API_KEY"] = OPENROUTER_API_KEY

        # Test query
        query = "What are the economic and environmental impacts of renewable energy adoption in developing countries, and what policies can promote it?"

        # Decompose query
        result = decomposer.decompose(query)

        print(f"Original query: {query}")
        print(f"Decomposed queries: {result.get('sub_queries', [])}")
        print(f"Reasoning: {result.get('reasoning', '')}")

        print("Multi Query Decomposition test passed!")
        return True
    except Exception as e:
        print(f"Error testing Multi Query Decomposition: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_source_validator():
    """Test the MultiSourceValidator module."""
    print("\n=== Testing Multi Source Validator ===\n")

    try:
        # Initialize with OpenRouter
        validator = MultiSourceValidator(
            provider="openrouter",
            model="anthropic/claude-3-opus",  # Default model for OpenRouter
            language="en"
        )

        # Ensure environment variable for OpenRouter API key is set
        import os
        os.environ["OPENROUTER_API_KEY"] = OPENROUTER_API_KEY

        # Test sources
        sources = [
            {
                "id": "source1",
                "content": "The Earth is approximately 4.54 billion years old.",
                "author": "Geological Society",
                "publication_date": "2020"
            },
            {
                "id": "source2",
                "content": "The Earth is about 4.5 billion years old.",
                "author": "NASA",
                "publication_date": "2021"
            },
            {
                "id": "source3",
                "content": "The Earth is 6,000 years old.",
                "author": "Alternative Source",
                "publication_date": "2019"
            }
        ]

        # Test claim
        claim = "The Earth is approximately 4.5 billion years old."

        # Validate claim
        result = validator.validate(claim, sources)

        print(f"Claim: {claim}")
        print(f"Validation result: {result.get('validation_result', '')}")
        print(f"Confidence: {result.get('confidence', 0)}")
        print(f"Supporting sources: {result.get('supporting_sources', [])}")
        print(f"Contradicting sources: {result.get('contradicting_sources', [])}")

        print("Multi Source Validator test passed!")
        return True
    except Exception as e:
        print(f"Error testing Multi Source Validator: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_fact_checker():
    """Test the FactChecker module."""
    print("\n=== Testing Fact Checker ===\n")

    try:
        # Initialize with OpenRouter
        fact_checker = FactChecker(
            provider="openrouter",
            model="anthropic/claude-3-opus"  # Default model for OpenRouter
        )

        # Ensure environment variable for OpenRouter API key is set
        import os
        os.environ["OPENROUTER_API_KEY"] = OPENROUTER_API_KEY

        # Test text
        text = """
        The Earth is the third planet from the Sun. It is the only planet known to have life.
        The Earth's diameter is 12,742 kilometers. The Earth's atmosphere is 78% nitrogen and 21% oxygen.
        """

        # Check facts
        result = fact_checker.check_facts(text)

        print(f"Text: {text}")
        print(f"Extracted facts: {result.get('facts', [])}")
        print(f"Verified facts: {result.get('verified_facts', [])}")
        print(f"Accuracy score: {result.get('accuracy_score', 0)}")

        # Test claim checking
        sources = [
            {
                "id": "source1",
                "content": "The Earth's diameter is 12,742 kilometers."
            },
            {
                "id": "source2",
                "content": "The Earth's atmosphere is composed of 78% nitrogen, 21% oxygen, and small amounts of other gases."
            }
        ]

        claim = "The Earth's diameter is 12,742 kilometers."

        claim_result = fact_checker.check_claim(claim, sources)

        print(f"Claim: {claim}")
        print(f"Claim verification: {claim_result}")

        print("Fact Checker test passed!")
        return True
    except Exception as e:
        print(f"Error testing Fact Checker: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_hybrid_search():
    """Test the HybridSearch module."""
    print("\n=== Testing Hybrid Search ===\n")

    try:
        # Initialize with OpenRouter
        hybrid_search = HybridSearch(
            provider="openrouter",
            model="anthropic/claude-3-opus",  # Default model for OpenRouter
            semantic_weight=0.7,
            keyword_weight=0.3
        )

        # Ensure environment variable for OpenRouter API key is set
        import os
        os.environ["OPENROUTER_API_KEY"] = OPENROUTER_API_KEY

        # Test documents
        documents = [
            {
                "id": "doc1",
                "content": "Climate change is causing rising sea levels and extreme weather events.",
                "embedding": hybrid_search._get_embedding("Climate change is causing rising sea levels and extreme weather events.")
            },
            {
                "id": "doc2",
                "content": "Solar and wind power are becoming increasingly cost-effective solutions.",
                "embedding": hybrid_search._get_embedding("Solar and wind power are becoming increasingly cost-effective solutions.")
            },
            {
                "id": "doc3",
                "content": "Artificial intelligence is transforming many industries through automation.",
                "embedding": hybrid_search._get_embedding("Artificial intelligence is transforming many industries through automation.")
            }
        ]

        # Test query
        query = "climate change effects"

        # Perform search
        results = hybrid_search.search(query, documents)

        print(f"Query: {query}")
        print(f"Search results: {results}")

        # Test advanced search with custom weights
        advanced_results = hybrid_search.advanced_search(
            query=query,
            documents=documents,
            semantic_weight=0.8,
            keyword_weight=0.2
        )

        print(f"Advanced search results: {advanced_results}")

        print("Hybrid Search test passed!")
        return True
    except Exception as e:
        print(f"Error testing Hybrid Search: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_semantic_chunking():
    """Test the SemanticChunking module."""
    print("\n=== Testing Semantic Chunking ===\n")

    try:
        # Initialize with OpenRouter
        chunker = SemanticChunking(
            provider="openrouter",
            model="anthropic/claude-3-opus",  # Default model for OpenRouter
            min_chunk_size=100,
            max_chunk_size=500,
            overlap=20
        )

        # Ensure environment variable for OpenRouter API key is set
        import os
        os.environ["OPENROUTER_API_KEY"] = OPENROUTER_API_KEY

        # Test text
        text = """
        Climate change is one of the most pressing challenges facing our planet today. It refers to long-term shifts in temperatures and weather patterns, mainly caused by human activities, especially the burning of fossil fuels.

        The effects of climate change are far-reaching and include rising sea levels, more frequent and severe weather events, and disruptions to ecosystems. These changes pose significant risks to human health, food security, and water supplies.

        Addressing climate change requires a multi-faceted approach. This includes transitioning to renewable energy sources, improving energy efficiency, and implementing policies to reduce greenhouse gas emissions. International cooperation is essential, as climate change is a global issue that requires coordinated action.

        Renewable energy sources like solar, wind, and hydroelectric power are becoming increasingly important in the fight against climate change. These sources produce little to no greenhouse gas emissions and can help reduce our dependence on fossil fuels.

        Individuals can also contribute to addressing climate change through lifestyle changes. This might include reducing energy consumption, using public transportation, and adopting more sustainable consumption habits.

        Despite the challenges, there is reason for hope. Technological innovations, growing public awareness, and increasing political will are all contributing to efforts to combat climate change. However, time is of the essence, and immediate action is necessary to mitigate the worst effects of climate change.
        """

        # Chunk text
        chunks = chunker.chunk_text(text)

        print(f"Text length: {len(text)}")
        print(f"Number of chunks: {len(chunks)}")
        for i, chunk in enumerate(chunks):
            print(f"Chunk {i+1}: {len(chunk['text'])} characters")
            print(f"  Start: {chunk['offset']}")
            print(f"  Length: {chunk['length']}")

        # Test semantic similarity
        text1 = "Climate change is causing rising sea levels."
        text2 = "Global warming is leading to increased ocean levels."

        similarity = chunker.calculate_semantic_similarity(text1, text2)

        print(f"Semantic similarity: {similarity}")

        print("Semantic Chunking test passed!")
        return True
    except Exception as e:
        print(f"Error testing Semantic Chunking: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_knowledge_graph_rag():
    """Test the KnowledgeGraphRAG module."""
    print("\n=== Testing Knowledge Graph RAG ===\n")

    try:
        # Initialize with OpenRouter
        kg_rag = KnowledgeGraphRAG(
            provider="openrouter",
            model="anthropic/claude-3-opus",  # Default model for OpenRouter
            url="http://localhost:8080",  # Assuming Weaviate is running locally
            document_class_name="Document",
            entity_class_name="Entity",
            relation_class_name="Relation",
            create_schema=True
        )

        # Ensure environment variable for OpenRouter API key is set
        import os
        os.environ["OPENROUTER_API_KEY"] = OPENROUTER_API_KEY

        # Mock the document store and knowledge graph
        from unittest.mock import MagicMock
        kg_rag.document_store = MagicMock()
        kg_rag.knowledge_graph = MagicMock()

        # Mock document store methods
        kg_rag.document_store.add_documents.return_value = ["doc1", "doc2"]
        kg_rag.document_store.search.return_value = [
            {
                "id": "doc1",
                "content": "Climate change is causing rising sea levels and extreme weather events.",
                "score": 0.8
            },
            {
                "id": "doc2",
                "content": "Solar and wind power are becoming increasingly cost-effective solutions.",
                "score": 0.7
            }
        ]

        # Mock knowledge graph methods
        kg_rag.knowledge_graph.search_entities.return_value = [
            {
                "id": "entity1",
                "name": "Climate Change",
                "type": "Concept",
                "description": "Long-term shifts in temperatures and weather patterns."
            },
            {
                "id": "entity2",
                "name": "Sea Level Rise",
                "type": "Phenomenon",
                "description": "Increase in the level of the world's oceans due to global warming."
            }
        ]
        kg_rag.knowledge_graph.get_connected_entities.return_value = []
        kg_rag.knowledge_graph.get_entity_relations.return_value = []

        # Mock the _get_embedding method
        kg_rag._get_embedding = MagicMock(return_value=[0.1] * 1536)

        # Test documents
        documents = [
            {
                "content": "Climate change is causing rising sea levels and extreme weather events.",
                "metadata": {"source": "Climate Research Institute"}
            },
            {
                "content": "Solar and wind power are becoming increasingly cost-effective solutions.",
                "metadata": {"source": "Energy Innovation Lab"}
            }
        ]

        # Add documents
        doc_ids = kg_rag.add_documents(documents)

        print(f"Added document IDs: {doc_ids}")

        # Test query
        query = "What are the effects of climate change?"

        # Process query
        result = kg_rag.process(query)

        print(f"Query: {query}")
        print(f"Response: {result.get('response', '')}")

        print("Knowledge Graph RAG test passed!")
        return True
    except Exception as e:
        print(f"Error testing Knowledge Graph RAG: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_knowledge_graph_reasoner():
    """Test the KnowledgeGraphReasoner module."""
    print("\n=== Testing Knowledge Graph Reasoner ===\n")

    try:
        # Initialize with OpenRouter
        kg_reasoner = KnowledgeGraphReasoner(
            provider="openrouter",
            model="anthropic/claude-3-opus",  # Default model for OpenRouter
            url="http://localhost:8080",  # Assuming Weaviate is running locally
            entity_class_name="Entity",
            relation_class_name="Relation",
            create_schema=True
        )

        # Ensure environment variable for OpenRouter API key is set
        import os
        os.environ["OPENROUTER_API_KEY"] = OPENROUTER_API_KEY

        # Mock the knowledge graph
        from unittest.mock import MagicMock
        kg_reasoner.knowledge_graph = MagicMock()

        # Mock knowledge graph methods
        kg_reasoner.knowledge_graph.search_entities.return_value = [
            {
                "id": "entity1",
                "name": "Climate Change",
                "type": "Concept",
                "description": "Long-term shifts in temperatures and weather patterns."
            },
            {
                "id": "entity2",
                "name": "Sea Level Rise",
                "type": "Phenomenon",
                "description": "Increase in the level of the world's oceans due to global warming."
            }
        ]
        kg_reasoner.knowledge_graph.get_connected_entities.return_value = []
        kg_reasoner.knowledge_graph.get_entity_relations.return_value = []

        # Set a test knowledge graph
        graph = {
            "nodes": [
                {
                    "id": "entity1",
                    "name": "Climate Change",
                    "type": "Concept",
                    "description": "Long-term shifts in temperatures and weather patterns."
                },
                {
                    "id": "entity2",
                    "name": "Sea Level Rise",
                    "type": "Phenomenon",
                    "description": "Increase in the level of the world's oceans due to global warming."
                },
                {
                    "id": "entity3",
                    "name": "Greenhouse Gas Emissions",
                    "type": "Concept",
                    "description": "Release of gases that trap heat in the atmosphere."
                }
            ],
            "edges": [
                {
                    "source": "entity1",
                    "target": "entity2",
                    "relation": "CAUSES",
                    "weight": 0.9
                },
                {
                    "source": "entity3",
                    "target": "entity1",
                    "relation": "CAUSES",
                    "weight": 0.8
                }
            ]
        }

        # Set the knowledge graph
        kg_reasoner.set_knowledge_graph(graph)

        # Test query
        query = "How do greenhouse gas emissions affect sea levels?"

        # Reason about the query
        result = kg_reasoner.reason(query)

        print(f"Query: {query}")
        print(f"Reasoning: {result.get('reasoning', '')}")

        # Test path finding
        path_result = kg_reasoner.find_path("entity3", "entity2")

        print(f"Path from Greenhouse Gas Emissions to Sea Level Rise: {path_result}")

        print("Knowledge Graph Reasoner test passed!")
        return True
    except Exception as e:
        print(f"Error testing Knowledge Graph Reasoner: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_tree_of_thought():
    """Test the TreeOfThought module."""
    print("\n=== Testing Tree of Thought ===\n")

    try:
        # Initialize with OpenRouter
        tot = TreeOfThought(
            provider="openrouter",
            model="anthropic/claude-3-opus",  # Default model for OpenRouter
            max_branches=3,
            max_depth=2
        )

        # Ensure environment variable for OpenRouter API key is set
        import os
        os.environ["OPENROUTER_API_KEY"] = OPENROUTER_API_KEY

        # Mock the _generate_thoughts and _evaluate_paths methods
        from unittest.mock import MagicMock
        tot._generate_thoughts = MagicMock(return_value=[
            "The pattern seems to be multiplying by 2 each time.",
            "Each number is double the previous number.",
            "The sequence follows the pattern 2^n where n starts at 1."
        ])
        tot._evaluate_paths = MagicMock(return_value=[
            (0.9, "The pattern seems to be multiplying by 2 each time."),
            (0.8, "Each number is double the previous number."),
            (0.95, "The sequence follows the pattern 2^n where n starts at 1.")
        ])
        tot._expand_path = MagicMock(return_value="The sequence follows the pattern 2^n where n starts at 1. Therefore, the next number is 2^5 = 32.")

        # Test problem
        problem = "What is the next number in the sequence: 2, 4, 8, 16, ...?"

        # Solve the problem
        result = tot.solve(problem)

        print(f"Problem: {problem}")
        print(f"Solution: {result}")

        print("Tree of Thought test passed!")
        return True
    except Exception as e:
        print(f"Error testing Tree of Thought: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("=== Testing All Modules ===\n")

    # Track test results
    results = {}

    # Test Source Attribution
    results["SourceAttribution"] = test_source_attribution()

    # Test Self Reflection
    results["SelfReflection"] = test_self_reflection()

    # Test Multi Query Decomposition
    results["MultiQueryDecomposition"] = test_multi_query_decomposition()

    # Test Multi Source Validator
    results["MultiSourceValidator"] = test_multi_source_validator()

    # Test Fact Checker
    results["FactChecker"] = test_fact_checker()

    # Test Hybrid Search
    results["HybridSearch"] = test_hybrid_search()

    # Test Semantic Chunking
    results["SemanticChunking"] = test_semantic_chunking()

    # Test Knowledge Graph RAG
    results["KnowledgeGraphRAG"] = test_knowledge_graph_rag()

    # Test Knowledge Graph Reasoner
    results["KnowledgeGraphReasoner"] = test_knowledge_graph_reasoner()

    # Test Tree of Thought
    results["TreeOfThought"] = test_tree_of_thought()

    # Print summary
    print("\n=== Test Summary ===")
    for module, success in results.items():
        status = "PASSED" if success else "FAILED"
        print(f"{module}: {status}")

    # Return success if all tests passed
    return all(results.values())

if __name__ == "__main__":
    main()
