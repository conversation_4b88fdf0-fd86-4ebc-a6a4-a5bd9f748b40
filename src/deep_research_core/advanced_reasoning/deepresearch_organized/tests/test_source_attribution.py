"""
Test script for the SourceAttribution module.
"""

import os
import sys
import json
from typing import Dict, List, Any

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import module directly
from src.deep_research_core.reasoning.source_attribution import SourceAttribution

# Sample data
SAMPLE_SOURCES = [
    {
        "id": "source1",
        "title": "AI ở Việt Nam",
        "content": "Tr<PERSON> tuệ nhân tạo (AI) đang phát triển nhanh chóng ở Việt Nam. Các công ty công nghệ lớn như VinAI, FPT AI và Zalo AI đang dẫn đầu trong nghiên cứu và phát triển AI.",
        "author": "Nguyễn Văn A",
        "publication_date": "2023-01-01",
        "url": "https://example.com/ai-vietnam",
        "publisher": "Tech Magazine"
    },
    {
        "id": "source2",
        "title": "VinAI: <PERSON>ông ty AI hàng đầu Vi<PERSON>",
        "content": "VinAI được thành lập vào năm 2018 và là một phần của Tập đoàn Vingroup. Họ tập trung vào nghiên cứu AI cơ bản và ứng dụng.",
        "author": "Trần Thị B",
        "publication_date": "2023-02-15",
        "url": "https://example.org/vinai",
        "publisher": "Tech News"
    }
]

def test_source_attribution():
    """Test the SourceAttribution module."""
    print("\n=== Testing SourceAttribution ===")
    
    try:
        # Initialize SourceAttribution
        source_attribution = SourceAttribution(
            citation_style="apa",
            track_token_level=True,
            language="vi"
        )
        
        # Register sources
        for source in SAMPLE_SOURCES:
            source_attribution.register_source(source["id"], source)
        
        # Track information usage
        source_attribution.track_information_usage(
            "VinAI được thành lập vào năm 2018 và là một phần của Tập đoàn Vingroup.",
            "source2",
            (0, 71)
        )
        
        # Generate citation
        citation = source_attribution.generate_citation("source2")
        
        # Add citations to text
        text_with_citations = source_attribution.add_citations_to_text(
            "VinAI được thành lập vào năm 2018 và là một phần của Tập đoàn Vingroup."
        )
        
        # Get bibliography
        bibliography = source_attribution.get_bibliography()
        
        # Print results
        print(f"Citation: {citation}")
        print(f"Text with citations: {text_with_citations}")
        print("Bibliography:")
        for entry in bibliography:
            print(f"- {entry}")
        
        # Get citation metrics
        metrics = source_attribution.get_citation_metrics()
        print(f"Citation metrics: {json.dumps(metrics, indent=2, ensure_ascii=False)}")
    
    except Exception as e:
        print(f"Error in SourceAttribution test: {str(e)}")

if __name__ == "__main__":
    test_source_attribution()
