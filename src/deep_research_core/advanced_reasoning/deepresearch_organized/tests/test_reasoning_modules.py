"""
Test script for the reasoning modules.
"""

import os
import sys
import json
from typing import Dict, List, Any

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import modules directly to avoid circular imports
from src.deep_research_core.reasoning.source_attribution import SourceAttribution
from src.deep_research_core.reasoning.self_reflection import SelfReflection
from src.deep_research_core.reasoning.multi_query_decomposer import MultiQueryDecomposition
from src.deep_research_core.reasoning.multi_source_validator import MultiSourceValidator

# Sample data
SAMPLE_TEXT = """
Trí tuệ nhân tạo (AI) đang phát triển nhanh chóng ở Việt Nam. Các công ty công nghệ lớn như VinAI, FPT AI và Zalo AI đang dẫn đầu trong nghiên cứu và phát triển AI.
VinAI được thành lập vào năm 2018 và là một phần của Tập đoàn Vingroup. Họ tập trung vào nghiên cứu AI cơ bản và ứng dụng.
FPT AI là bộ phận AI của Tập đoàn FPT, một trong những công ty công nghệ lớn nhất Việt Nam. Họ đã phát triển nhiều sản phẩm AI như chatbot, hệ thống nhận dạng khuôn mặt và xử lý ngôn ngữ tự nhiên.
Zalo AI là bộ phận nghiên cứu AI của Zalo, ứng dụng nhắn tin phổ biến nhất Việt Nam với hơn 100 triệu người dùng.
Chính phủ Việt Nam cũng đã ban hành Chiến lược quốc gia về nghiên cứu, phát triển và ứng dụng Trí tuệ nhân tạo đến năm 2030, với mục tiêu đưa Việt Nam trở thành trung tâm đổi mới sáng tạo và phát triển AI trong khu vực.
"""

SAMPLE_SOURCES = [
    {
        "id": "source1",
        "title": "AI ở Việt Nam",
        "content": "Trí tuệ nhân tạo (AI) đang phát triển nhanh chóng ở Việt Nam. Các công ty công nghệ lớn như VinAI, FPT AI và Zalo AI đang dẫn đầu trong nghiên cứu và phát triển AI.",
        "author": "Nguyễn Văn A",
        "publication_date": "2023-01-01",
        "url": "https://example.com/ai-vietnam",
        "publisher": "Tech Magazine"
    },
    {
        "id": "source2",
        "title": "VinAI: Công ty AI hàng đầu Việt Nam",
        "content": "VinAI được thành lập vào năm 2018 và là một phần của Tập đoàn Vingroup. Họ tập trung vào nghiên cứu AI cơ bản và ứng dụng.",
        "author": "Trần Thị B",
        "publication_date": "2023-02-15",
        "url": "https://example.org/vinai",
        "publisher": "Tech News"
    },
    {
        "id": "source3",
        "title": "FPT AI và các ứng dụng",
        "content": "FPT AI là bộ phận AI của Tập đoàn FPT, một trong những công ty công nghệ lớn nhất Việt Nam. Họ đã phát triển nhiều sản phẩm AI như chatbot, hệ thống nhận dạng khuôn mặt và xử lý ngôn ngữ tự nhiên.",
        "author": "Lê Văn C",
        "publication_date": "2023-03-10",
        "url": "https://example.com/fpt-ai",
        "publisher": "Tech Review"
    }
]

def test_source_attribution():
    """Test the SourceAttribution module."""
    print("\n=== Testing SourceAttribution ===")

    try:
        # Initialize SourceAttribution
        source_attribution = SourceAttribution(
            citation_style="apa",
            track_token_level=True,
            language="vi"
        )

        # Register sources
        for source in SAMPLE_SOURCES:
            source_attribution.register_source(source["id"], source)

        # Track information usage
        source_attribution.track_information_usage(
            "VinAI được thành lập vào năm 2018 và là một phần của Tập đoàn Vingroup.",
            "source2",
            (0, 71)
        )

        # Generate citation
        citation = source_attribution.generate_citation("source2")

        # Add citations to text
        text_with_citations = source_attribution.add_citations_to_text(
            "VinAI được thành lập vào năm 2018 và là một phần của Tập đoàn Vingroup."
        )

        # Get bibliography
        bibliography = source_attribution.get_bibliography()

        # Print results
        print(f"Citation: {citation}")
        print(f"Text with citations: {text_with_citations}")
        print("Bibliography:")
        for entry in bibliography:
            print(f"- {entry}")

        # Get citation metrics
        metrics = source_attribution.get_citation_metrics()
        print(f"Citation metrics: {json.dumps(metrics, indent=2, ensure_ascii=False)}")

    except Exception as e:
        print(f"Error in SourceAttribution test: {str(e)}")

def test_self_reflection():
    """Test the SelfReflection module."""
    print("\n=== Testing SelfReflection ===")

    try:
        # Initialize SelfReflection
        self_reflection = SelfReflection(
            language="vi",
            reflection_depth=1
        )

        # Test reasoning
        reasoning = """
        Dựa trên dữ liệu, tôi kết luận rằng VinAI được thành lập vào năm 2019 và là công ty AI lớn nhất Việt Nam.
        FPT AI chỉ mới bắt đầu nghiên cứu AI gần đây và chưa có sản phẩm thương mại.
        Zalo AI có khoảng 50 triệu người dùng.
        """

        # Reflect on reasoning
        reflection_result = self_reflection.reflect_on_reasoning(
            reasoning=reasoning,
            context={"topic": "AI ở Việt Nam"},
            facts=[
                "VinAI được thành lập vào năm 2018",
                "FPT AI đã phát triển nhiều sản phẩm AI như chatbot",
                "Zalo có hơn 100 triệu người dùng"
            ]
        )

        # Print reflection result
        print("Reflection result:")
        print(json.dumps(reflection_result, indent=2, ensure_ascii=False))

        # Correct reasoning
        correction_result = self_reflection.correct_reasoning(
            reasoning=reasoning,
            reflection_result=reflection_result,
            context={"topic": "AI ở Việt Nam"},
            facts=[
                "VinAI được thành lập vào năm 2018",
                "FPT AI đã phát triển nhiều sản phẩm AI như chatbot",
                "Zalo có hơn 100 triệu người dùng"
            ]
        )

        # Print correction result
        print("\nCorrection result:")
        print(json.dumps(correction_result, indent=2, ensure_ascii=False))

    except Exception as e:
        print(f"Error in SelfReflection test: {str(e)}")

def test_multi_query_decomposition():
    """Test the MultiQueryDecomposition module."""
    print("\n=== Testing MultiQueryDecomposition ===")

    try:
        # Initialize MultiQueryDecomposition
        multi_query_decomposition = MultiQueryDecomposition(
            language="vi",
            max_depth=2
        )

        # Test complex query
        complex_query = "So sánh các công ty AI ở Việt Nam, phân tích ưu điểm và nhược điểm của mỗi công ty, và dự đoán xu hướng phát triển AI ở Việt Nam trong 5 năm tới."

        # Decompose query
        decomposition_result = multi_query_decomposition.decompose(
            query=complex_query,
            context={"topic": "AI ở Việt Nam"},
            max_sub_queries=5,
            min_sub_queries=2
        )

        # Print decomposition result
        print("Decomposition result:")
        print(f"Original query: {decomposition_result['original_query']}")
        print(f"Is decomposed: {decomposition_result['is_decomposed']}")
        print(f"Number of sub-queries: {len(decomposition_result['sub_queries'])}")

        print("\nSub-queries:")
        for i, sub_query in enumerate(decomposition_result['sub_queries']):
            print(f"{i+1}. {sub_query['query']}")

        print("\nProcessing order:")
        for i, query_id in enumerate(decomposition_result['processing_order']):
            if query_id in multi_query_decomposition.query_graph:
                print(f"{i+1}. {multi_query_decomposition.query_graph[query_id]['query']}")

        # Test synthesis
        if decomposition_result['is_decomposed'] and decomposition_result['sub_queries']:
            # Simulate query results
            query_results = {}
            for sub_query in decomposition_result['sub_queries']:
                query_results[sub_query['id']] = f"Kết quả cho câu hỏi: {sub_query['query']}"

            # Synthesize results
            synthesis_result = multi_query_decomposition.synthesize_results(
                query_results=query_results,
                original_query=complex_query
            )

            print("\nSynthesis result:")
            print(synthesis_result)

    except Exception as e:
        print(f"Error in MultiQueryDecomposition test: {str(e)}")

def test_multi_source_validator():
    """Test the MultiSourceValidator module."""
    print("\n=== Testing MultiSourceValidator ===")

    try:
        # Initialize MultiSourceValidator
        multi_source_validator = MultiSourceValidator(
            language="vi",
            min_sources=2
        )

        # Test information to validate
        information = "VinAI được thành lập vào năm 2018 và là một phần của Tập đoàn Vingroup. Họ tập trung vào nghiên cứu AI cơ bản và ứng dụng."

        # Validate information
        validation_result = multi_source_validator.validate(
            information=information,
            sources=SAMPLE_SOURCES,
            context={"topic": "AI ở Việt Nam"}
        )

        # Print validation result
        print("Validation result:")
        print(json.dumps(validation_result, indent=2, ensure_ascii=False))

        # Test contradicting information
        information_items = [
            "VinAI được thành lập vào năm 2018.",
            "VinAI được thành lập vào năm 2019.",
            "VinAI là một phần của Tập đoàn Vingroup."
        ]

        # Analyze contradictions
        contradiction_result = multi_source_validator.analyze_contradictions(
            information_items=information_items,
            sources=SAMPLE_SOURCES
        )

        # Print contradiction result
        print("\nContradiction analysis:")
        print(json.dumps(contradiction_result, indent=2, ensure_ascii=False))

        # Get most reliable information
        reliability_result = multi_source_validator.get_most_reliable_information(
            information_items=information_items,
            sources=SAMPLE_SOURCES,
            context={"topic": "AI ở Việt Nam"}
        )

        # Print reliability result
        print("\nMost reliable information:")
        print(json.dumps(reliability_result, indent=2, ensure_ascii=False))

    except Exception as e:
        print(f"Error in MultiSourceValidator test: {str(e)}")

def main():
    """Run all tests."""
    print("Testing reasoning modules...")

    # Test SourceAttribution
    test_source_attribution()

    # Test SelfReflection
    test_self_reflection()

    # Test MultiQueryDecomposition
    test_multi_query_decomposition()

    # Test MultiSourceValidator
    test_multi_source_validator()

    print("\nAll tests completed!")

if __name__ == "__main__":
    main()
