"""
Direct test for query analyzer.
"""

import sys
import os
import re
from typing import Dict, Any, List, Optional, Tuple

class SimpleQueryAnalyzer:
    """
    Simple query analyzer for testing.
    """
    
    def __init__(self):
        """
        Initialize the SimpleQueryAnalyzer.
        """
        # Book-related keywords
        self.book_keywords = [
            "book", "novel", "author", "read", "literature", "fiction", "nonfiction",
            "textbook", "publication", "publisher", "isbn", "chapter", "edition",
            "sách", "tiểu thuyết", "tác giả", "đọc", "văn học", "xuất bản"
        ]
        
        # Map-related keywords
        self.map_keywords = [
            "map", "location", "address", "directions", "navigate", "route", "distance",
            "place", "city", "country", "street", "road", "gps", "coordinates",
            "bản đồ", "địa điểm", "địa chỉ", "chỉ đường", "lộ trình", "thành phố"
        ]
    
    def analyze_query(self, query: str) -> Dict[str, Any]:
        """
        Analyze a search query.
        
        Args:
            query: The query to analyze
            
        Returns:
            Dictionary containing analysis results
        """
        # Convert query to lowercase
        query_lower = query.lower()
        
        # Detect query type
        query_type = self._detect_query_type(query_lower)
        
        # Detect language
        language = self._detect_language(query)
        
        # Detect complexity
        complexity = self._calculate_complexity(query)
        
        # Detect if query requires detailed information
        requires_detail = self._requires_detailed_info(query_lower)
        
        # Detect if query is time-sensitive
        time_sensitive = self._is_time_sensitive(query_lower)
        
        # Return analysis results
        return {
            "query_type": query_type,
            "language": language,
            "complexity": complexity,
            "requires_detail": requires_detail,
            "time_sensitive": time_sensitive
        }
    
    def _detect_query_type(self, query: str) -> str:
        """
        Detect the type of query.
        
        Args:
            query: The query to analyze
            
        Returns:
            Query type (general, book, map, etc.)
        """
        # Check for book-related query
        if any(keyword in query for keyword in self.book_keywords):
            return "book"
        
        # Check for map-related query
        if any(keyword in query for keyword in self.map_keywords):
            return "map"
        
        # Default to general query
        return "general"
    
    def _detect_language(self, query: str) -> str:
        """
        Detect the language of the query.
        
        Args:
            query: The query to analyze
            
        Returns:
            Language code (en, vi, etc.)
        """
        # Simple language detection based on characters
        vietnamese_chars = set("ăâáàảãạắằẳẵặấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđ")
        
        # Count Vietnamese characters
        vietnamese_char_count = sum(1 for char in query.lower() if char in vietnamese_chars)
        
        # If there are Vietnamese characters, assume it's Vietnamese
        if vietnamese_char_count > 0:
            return "vi"
        
        # Default to English
        return "en"
    
    def _calculate_complexity(self, query: str) -> float:
        """
        Calculate the complexity of the query.
        
        Args:
            query: The query to analyze
            
        Returns:
            Complexity score (0.0 to 1.0)
        """
        # Split query into words
        words = query.split()
        
        # Calculate complexity based on query length
        length_score = min(len(words) / 20, 1.0)  # Normalize to 0-1
        
        # Calculate complexity based on word length
        avg_word_length = sum(len(word) for word in words) / max(len(words), 1)
        word_length_score = min(avg_word_length / 10, 1.0)  # Normalize to 0-1
        
        # Check for complex question patterns
        complex_patterns = [
            r"how does", r"why does", r"what causes", r"compare", r"contrast",
            r"relationship between", r"difference between", r"similarities between",
            r"explain", r"analyze", r"evaluate", r"làm thế nào", r"tại sao", r"so sánh"
        ]
        
        pattern_score = 0.0
        for pattern in complex_patterns:
            if re.search(pattern, query.lower()):
                pattern_score = 0.7  # Boost complexity if complex pattern found
                break
        
        # Combine scores
        complexity = max(length_score * 0.4 + word_length_score * 0.3 + pattern_score * 0.3, 0.1)
        
        return min(complexity, 1.0)  # Ensure it's between 0 and 1
    
    def _requires_detailed_info(self, query: str) -> bool:
        """
        Determine if the query requires detailed information.
        
        Args:
            query: The query to analyze
            
        Returns:
            True if detailed information is required, False otherwise
        """
        detail_indicators = [
            "detailed", "comprehensive", "in-depth", "complete", "thorough",
            "chi tiết", "toàn diện", "đầy đủ", "kỹ lưỡng"
        ]
        
        return any(indicator in query for indicator in detail_indicators)
    
    def _is_time_sensitive(self, query: str) -> bool:
        """
        Determine if the query is time-sensitive.
        
        Args:
            query: The query to analyze
            
        Returns:
            True if the query is time-sensitive, False otherwise
        """
        time_indicators = [
            "latest", "recent", "current", "today", "now", "update", "news",
            "mới nhất", "gần đây", "hiện tại", "hôm nay", "bây giờ", "cập nhật", "tin tức"
        ]
        
        return any(indicator in query for indicator in time_indicators)
    
    def recommend_search_engine(self, query: str, query_type: str) -> str:
        """
        Recommend a specific search engine for a query.
        
        Args:
            query: The search query
            query_type: Type of the query
            
        Returns:
            Recommended search engine
        """
        # Book queries
        if query_type == "book":
            # Check for specific book sources
            if any(keyword in query.lower() for keyword in ["isbn", "publisher", "edition", "textbook", "academic"]):
                return "google_books"  # Google Books has better metadata
                
            if any(keyword in query.lower() for keyword in ["free", "public domain", "classic", "miễn phí"]):
                return "open_library"  # Open Library has more free books
                
            if any(keyword in query.lower() for keyword in ["review", "rating", "đánh giá", "xếp hạng", "bestseller"]):
                return "goodreads"  # Goodreads has better reviews and ratings
                
            if any(keyword in query.lower() for keyword in ["download", "pdf", "epub", "tải xuống", "ebook"]):
                return "libgen"  # Library Genesis for downloadable content
                
            # Check language preference
            query_lower = query.lower()
            vietnamese_chars = set("ăâáàảãạắằẳẵặấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđ")
            if any(char in vietnamese_chars for char in query_lower):
                return "open_library"  # Better for non-English content
                
            # Default to Google Books
            return "google_books"
        
        # Map queries
        if query_type == "map":
            # All map queries use OpenStreetMap (free, no API key required)
            return "openstreetmap"
        
        # Default to DuckDuckGo for general queries
        return "duckduckgo"

def test_query_analyzer():
    """Test query analyzer."""
    print("\n=== Testing Query Analyzer ===")
    
    # Create analyzer
    analyzer = SimpleQueryAnalyzer()
    
    # Test queries
    test_queries = [
        "Python programming language",
        "Harry Potter books by J.K. Rowling",
        "Map of Paris France",
        "Directions from New York to Boston",
        "Latest news about artificial intelligence",
        "Sách tiếng Việt hay nhất năm 2023",
        "Bản đồ thành phố Hà Nội",
        "Compare and contrast the economic policies of different countries in Southeast Asia"
    ]
    
    for query in test_queries:
        print(f"\n--- Query: {query} ---")
        
        # Analyze query
        analysis = analyzer.analyze_query(query)
        
        # Print analysis results
        print(f"Query type: {analysis.get('query_type', '')}")
        print(f"Language: {analysis.get('language', '')}")
        print(f"Complexity: {analysis.get('complexity', 0):.2f}")
        print(f"Requires detail: {analysis.get('requires_detail', False)}")
        print(f"Time sensitive: {analysis.get('time_sensitive', False)}")
        
        # Get recommended search engine
        engine = analyzer.recommend_search_engine(query, analysis.get('query_type', ''))
        print(f"Recommended search engine: {engine}")
    
    return True

if __name__ == "__main__":
    test_query_analyzer()
