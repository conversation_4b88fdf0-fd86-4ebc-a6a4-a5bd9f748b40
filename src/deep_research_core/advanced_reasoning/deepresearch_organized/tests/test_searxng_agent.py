#!/usr/bin/env python3
"""
Test for WebSearchAgent with SearXNG-Crawlee integration
"""

from src.deep_research_core.agents.web_search_agent import WebSearchAgent

def main():
    # Initialize WebSearchAgent with SearXNG-Crawlee method
    agent = WebSearchAgent(search_method='searxng_crawlee')
    
    # Test with a simple query
    print("\n--- Testing WebSearchAgent with simple query ---")
    query = "Python programming tutorial"
    
    try:
        result = agent.search(query, num_results=3)
        print_result(result)
    except Exception as e:
        print(f"Error testing WebSearchAgent with SearXNG-Crawlee: {str(e)}")
        import traceback
        traceback.print_exc()

def print_result(result, max_results=3):
    """Print search result in a readable format."""
    if not result.get("success", False):
        print(f"Search failed: {result.get('error', 'Unknown error')}")
        if "detailed_errors" in result:
            print("Detailed errors:")
            for engine, error in result.get("detailed_errors", {}).items():
                print(f"  {engine}: {error}")
        return False
    
    results = result.get("results", [])
    print(f"Found {len(results)} results")
    
    if len(results) == 0:
        return False
    
    for i, item in enumerate(results[:max_results]):
        print(f"\nResult {i+1}:")
        print(f"Title: {item.get('title', '')}")
        print(f"URL: {item.get('url', '')}")
        print(f"Source: {item.get('source', '')}")
        snippet = item.get('content', item.get('snippet', ''))
        if snippet and len(snippet) > 200:
            snippet = snippet[:200] + "..."
        print(f"Snippet: {snippet}")
    
    return True

if __name__ == "__main__":
    main()
