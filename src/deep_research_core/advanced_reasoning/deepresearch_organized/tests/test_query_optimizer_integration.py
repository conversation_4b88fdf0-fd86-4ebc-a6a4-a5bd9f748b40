#!/usr/bin/env python3
"""
Test script để kiểm tra tính năng tối ưu hóa truy vấn trong WebSearchAgentLocal.
"""

import sys
import os
import json
import time
from typing import Dict, Any, List, Optional, Union

# Thêm thư mục gốc vào sys.path để import các module
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), 'src')))

try:
    sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), 'src')))
    from deep_research_core.agents.query_optimizer import QueryOptimizer
except ImportError as e:
    print(f"Lỗi import QueryOptimizer: {e}")
    print("Hãy chắc chắn rằng bạn đang chạy script từ thư mục gốc của dự án.")
    sys.exit(1)

def test_query_optimizer():
    """
    Kiểm tra tính năng tối ưu hóa truy vấn.
    """
    print("\n=== Kiểm tra QueryOptimizer ===\n")

    # Danh sách các truy vấn để kiểm tra
    test_queries = [
        "Những xu hướng AI mới nhất trong năm 2025?",
        "Python programming for beginners",
        "Cách học tiếng Anh hiệu quả",
        "Tác động của biến đổi khí hậu đến nông nghiệp Việt Nam",
        "Best machine learning frameworks 2024"
    ]

    # Khởi tạo QueryOptimizer với cache
    cache_file = "query_optimizer_cache.json"
    optimizer = QueryOptimizer(
        language="auto",
        cache_file=cache_file,
        enable_learning=True
    )

    # Kiểm tra từng truy vấn
    for query in test_queries:
        print(f"\nTruy vấn gốc: {query}")

        # Tối ưu hóa truy vấn
        try:
            result = optimizer.optimize(query)

            # In kết quả
            print(f"Ngôn ngữ: {result.get('language', 'không xác định')}")
            print(f"Từ khóa: {', '.join(result.get('keywords', []))}")
            print(f"Lĩnh vực: {', '.join(result.get('domains', []))}")

            if 'related_concepts' in result:
                print(f"Khái niệm liên quan: {', '.join(result.get('related_concepts', []))}")

            print("Biến thể truy vấn:")
            for i, variant in enumerate(result.get("variants", []), 1):
                print(f"  {i}. {variant}")

            print(f"Thời gian xử lý: {result.get('processing_time', 0):.4f} giây")

            # Ghi nhận biến thể thành công (giả lập)
            if result.get("variants"):
                # Giả lập biến thể thành công là biến thể đầu tiên
                successful_variant = result.get("variants")[0]
                optimizer.record_successful_variant(query, successful_variant)
                print(f"Đã ghi nhận biến thể thành công: {successful_variant}")
        except Exception as e:
            print(f"Lỗi khi tối ưu hóa truy vấn: {e}")

    # Kiểm tra cache
    if os.path.exists(cache_file):
        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
                print("\n=== Nội dung cache ===\n")
                print(f"Số lượng truy vấn trong cache: {len(cache_data.get('query_variants', {}))}")
                print(f"Số lượng biến thể thành công: {len(cache_data.get('successful_variants', {}))}")

                # In chi tiết các biến thể thành công
                print("\nCác biến thể thành công:")
                for query, variants in cache_data.get('successful_variants', {}).items():
                    print(f"  Truy vấn: {query}")
                    for i, variant in enumerate(variants, 1):
                        print(f"    {i}. {variant}")
        except Exception as e:
            print(f"Lỗi khi đọc cache: {e}")

def main():
    """
    Hàm chính.
    """
    # Kiểm tra QueryOptimizer
    test_query_optimizer()

if __name__ == "__main__":
    main()
