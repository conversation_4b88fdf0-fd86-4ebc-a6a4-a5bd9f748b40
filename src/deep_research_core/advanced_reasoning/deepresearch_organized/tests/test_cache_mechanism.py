"""
Test for cache mechanism.
"""

import sys
import os
import time
import json
from typing import Dict, Any, List, Optional, Tuple
import hashlib

class SimpleCache:
    """
    Simple cache for testing.
    """
    
    def __init__(self, ttl: int = 3600, max_size: int = 1000):
        """
        Initialize the SimpleCache.
        
        Args:
            ttl: Time-to-live in seconds
            max_size: Maximum number of items in cache
        """
        self.cache = {}
        self.ttl = ttl
        self.max_size = max_size
        self.access_times = {}
    
    def get(self, key: str) -> Optional[Dict[str, Any]]:
        """
        Get an item from the cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cached item or None if not found or expired
        """
        # Check if key exists in cache
        if key not in self.cache:
            return None
        
        # Get cached item and timestamp
        item, timestamp = self.cache[key]
        
        # Check if item has expired
        if time.time() - timestamp > self.ttl:
            # Remove expired item
            del self.cache[key]
            if key in self.access_times:
                del self.access_times[key]
            return None
        
        # Update access time
        self.access_times[key] = time.time()
        
        return item
    
    def set(self, key: str, value: Dict[str, Any]) -> None:
        """
        Set an item in the cache.
        
        Args:
            key: Cache key
            value: Value to cache
        """
        # Check if cache is full
        if len(self.cache) >= self.max_size and key not in self.cache:
            # Remove least recently used item
            self._remove_lru()
        
        # Add item to cache
        self.cache[key] = (value, time.time())
        self.access_times[key] = time.time()
    
    def _remove_lru(self) -> None:
        """
        Remove the least recently used item from the cache.
        """
        if not self.access_times:
            return
        
        # Find least recently used key
        lru_key = min(self.access_times, key=self.access_times.get)
        
        # Remove item
        if lru_key in self.cache:
            del self.cache[lru_key]
        if lru_key in self.access_times:
            del self.access_times[lru_key]
    
    def clear(self) -> None:
        """
        Clear the cache.
        """
        self.cache = {}
        self.access_times = {}
    
    def size(self) -> int:
        """
        Get the number of items in the cache.
        
        Returns:
            Number of items in the cache
        """
        return len(self.cache)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics.
        
        Returns:
            Dictionary containing cache statistics
        """
        return {
            "size": len(self.cache),
            "max_size": self.max_size,
            "ttl": self.ttl,
            "memory_usage": sys.getsizeof(self.cache)
        }

class SmartCache(SimpleCache):
    """
    Smart cache with similarity search.
    """
    
    def __init__(self, ttl: int = 3600, max_size: int = 1000, similarity_threshold: float = 0.8):
        """
        Initialize the SmartCache.
        
        Args:
            ttl: Time-to-live in seconds
            max_size: Maximum number of items in cache
            similarity_threshold: Threshold for similarity search
        """
        super().__init__(ttl, max_size)
        self.similarity_threshold = similarity_threshold
        self.query_index = {}
    
    def get(self, key: str, allow_similar: bool = True) -> Optional[Dict[str, Any]]:
        """
        Get an item from the cache.
        
        Args:
            key: Cache key
            allow_similar: Whether to allow similar matches
            
        Returns:
            Cached item or None if not found or expired
        """
        # Try exact match first
        exact_match = super().get(key)
        if exact_match:
            return exact_match
        
        # If similar matches are not allowed, return None
        if not allow_similar:
            return None
        
        # Try to find similar query
        similar_key = self._find_similar_query(key)
        if similar_key:
            # Get similar item
            similar_item = super().get(similar_key)
            if similar_item:
                # Mark as similar match
                similar_item["from_similar_query"] = True
                similar_item["original_query"] = similar_key
                return similar_item
        
        return None
    
    def set(self, key: str, value: Dict[str, Any]) -> None:
        """
        Set an item in the cache.
        
        Args:
            key: Cache key
            value: Value to cache
        """
        # Add to regular cache
        super().set(key, value)
        
        # Add to query index
        self._index_query(key)
    
    def _index_query(self, query: str) -> None:
        """
        Index a query for similarity search.
        
        Args:
            query: Query to index
        """
        # Get words from query
        words = set(query.lower().split())
        
        # Add query to index for each word
        for word in words:
            if word not in self.query_index:
                self.query_index[word] = []
            if query not in self.query_index[word]:
                self.query_index[word].append(query)
    
    def _find_similar_query(self, query: str) -> Optional[str]:
        """
        Find a similar query in the cache.
        
        Args:
            query: Query to find similar match for
            
        Returns:
            Similar query or None if not found
        """
        # Get words from query
        words = set(query.lower().split())
        
        # Find candidate queries
        candidates = []
        for word in words:
            if word in self.query_index:
                candidates.extend(self.query_index[word])
        
        # If no candidates, return None
        if not candidates:
            return None
        
        # Find most similar query
        best_similarity = 0.0
        best_match = None
        
        for candidate in candidates:
            # Skip exact match
            if candidate == query:
                continue
            
            # Calculate similarity
            similarity = self._calculate_similarity(query, candidate)
            
            # Update best match
            if similarity > best_similarity and similarity >= self.similarity_threshold:
                best_similarity = similarity
                best_match = candidate
        
        return best_match
    
    def _calculate_similarity(self, query1: str, query2: str) -> float:
        """
        Calculate similarity between two queries.
        
        Args:
            query1: First query
            query2: Second query
            
        Returns:
            Similarity score (0.0 to 1.0)
        """
        # Get words from queries
        words1 = set(query1.lower().split())
        words2 = set(query2.lower().split())
        
        # Calculate Jaccard similarity
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0
    
    def clear(self) -> None:
        """
        Clear the cache.
        """
        super().clear()
        self.query_index = {}

def test_simple_cache():
    """Test simple cache."""
    print("\n=== Testing Simple Cache ===")
    
    # Create cache
    cache = SimpleCache(ttl=5, max_size=3)
    
    # Add items to cache
    print("\n--- Adding Items to Cache ---")
    cache.set("key1", {"value": "value1"})
    cache.set("key2", {"value": "value2"})
    cache.set("key3", {"value": "value3"})
    
    print(f"Cache size: {cache.size()}")
    
    # Get items from cache
    print("\n--- Getting Items from Cache ---")
    print(f"key1: {cache.get('key1')}")
    print(f"key2: {cache.get('key2')}")
    print(f"key3: {cache.get('key3')}")
    print(f"key4: {cache.get('key4')}")
    
    # Test max size
    print("\n--- Testing Max Size ---")
    cache.set("key4", {"value": "value4"})
    print(f"Cache size: {cache.size()}")
    print(f"key1: {cache.get('key1')}")  # Should be removed as LRU
    print(f"key2: {cache.get('key2')}")
    print(f"key3: {cache.get('key3')}")
    print(f"key4: {cache.get('key4')}")
    
    # Test TTL
    print("\n--- Testing TTL ---")
    print("Waiting for TTL to expire...")
    time.sleep(6)  # Wait for TTL to expire
    print(f"key2: {cache.get('key2')}")  # Should be expired
    print(f"key3: {cache.get('key3')}")  # Should be expired
    print(f"key4: {cache.get('key4')}")  # Should be expired
    print(f"Cache size: {cache.size()}")
    
    return True

def test_smart_cache():
    """Test smart cache."""
    print("\n=== Testing Smart Cache ===")
    
    # Create cache
    cache = SmartCache(ttl=60, max_size=10, similarity_threshold=0.5)
    
    # Add items to cache
    print("\n--- Adding Items to Cache ---")
    cache.set("python programming language", {"value": "Python info"})
    cache.set("java programming tutorial", {"value": "Java info"})
    cache.set("machine learning algorithms", {"value": "ML info"})
    
    print(f"Cache size: {cache.size()}")
    
    # Test exact matches
    print("\n--- Testing Exact Matches ---")
    print(f"python programming language: {cache.get('python programming language')}")
    print(f"java programming tutorial: {cache.get('java programming tutorial')}")
    print(f"machine learning algorithms: {cache.get('machine learning algorithms')}")
    
    # Test similar matches
    print("\n--- Testing Similar Matches ---")
    print(f"python programming tutorial: {cache.get('python programming tutorial')}")
    print(f"java language guide: {cache.get('java language guide')}")
    print(f"deep learning algorithms: {cache.get('deep learning algorithms')}")
    
    # Test with allow_similar=False
    print("\n--- Testing with allow_similar=False ---")
    print(f"python programming tutorial: {cache.get('python programming tutorial', allow_similar=False)}")
    
    return True

def run_tests():
    """Run all tests."""
    tests = [
        ("Simple Cache", test_simple_cache),
        ("Smart Cache", test_smart_cache)
    ]
    
    results = {}
    
    for name, test_func in tests:
        print(f"\n\n{'#'*100}")
        print(f"# Running Test: {name}")
        print(f"{'#'*100}")
        
        try:
            success = test_func()
            results[name] = "PASSED" if success else "FAILED"
        except Exception as e:
            print(f"Error during test: {e}")
            results[name] = "ERROR"
    
    print(f"\n\n{'#'*100}")
    print(f"# Test Results")
    print(f"{'#'*100}")
    
    for name, result in results.items():
        print(f"{name}: {result}")

if __name__ == "__main__":
    run_tests()
