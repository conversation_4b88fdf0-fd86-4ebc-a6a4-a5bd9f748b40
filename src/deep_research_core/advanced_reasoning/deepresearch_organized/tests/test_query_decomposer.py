#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Unit test cho QueryDecomposer.
"""

import unittest
from query_decomposer import QueryDecomposer

class TestQueryDecomposer(unittest.TestCase):
    """
    Test case cho QueryDecomposer.
    """
    
    def setUp(self):
        """
        <PERSON><PERSON><PERSON><PERSON> lập trước mỗi test case.
        """
        self.decomposer = QueryDecomposer(verbose=True)
    
    def test_decompose_comparison_query_english(self):
        """
        Test phân rã câu hỏi so sánh tiếng Anh.
        """
        query = "Compare Python and JavaScript for web development"
        result = self.decomposer.decompose_query(query)
        
        self.assertEqual(result["query_type"], "comparison")
        self.assertEqual(result["decomposition_method"], "comparison_decomposition")
        self.assertTrue(len(result["sub_queries"]) > 1)
        
        # <PERSON><PERSON><PERSON> tra các câu hỏi con
        sub_queries = result["sub_queries"]
        self.assertTrue(any("Python" in q for q in sub_queries))
        self.assertTrue(any("JavaScript" in q for q in sub_queries))
        self.assertTrue(any("differences" in q for q in sub_queries))
    
    def test_decompose_comparison_query_vietnamese(self):
        """
        Test phân rã câu hỏi so sánh tiếng Việt.
        """
        query = "So sánh Python và JavaScript cho phát triển web"
        result = self.decomposer.decompose_query(query)
        
        self.assertEqual(result["query_type"], "comparison")
        self.assertEqual(result["decomposition_method"], "comparison_decomposition")
        self.assertTrue(len(result["sub_queries"]) > 1)
        
        # Kiểm tra các câu hỏi con
        sub_queries = result["sub_queries"]
        self.assertTrue(any("Python" in q for q in sub_queries))
        self.assertTrue(any("JavaScript" in q for q in sub_queries))
        self.assertTrue(any("khác nhau" in q for q in sub_queries))
    
    def test_decompose_list_query_english(self):
        """
        Test phân rã câu hỏi liệt kê tiếng Anh.
        """
        query = "List the types of machine learning algorithms"
        result = self.decomposer.decompose_query(query)
        
        self.assertEqual(result["query_type"], "list")
        self.assertEqual(result["decomposition_method"], "list_decomposition")
        self.assertTrue(len(result["sub_queries"]) > 1)
        
        # Kiểm tra các câu hỏi con
        sub_queries = result["sub_queries"]
        self.assertTrue(any("machine learning algorithms" in q for q in sub_queries))
        self.assertTrue(any("types" in q for q in sub_queries))
    
    def test_decompose_list_query_vietnamese(self):
        """
        Test phân rã câu hỏi liệt kê tiếng Việt.
        """
        query = "Liệt kê các loại thuật toán học máy"
        result = self.decomposer.decompose_query(query)
        
        self.assertEqual(result["query_type"], "list")
        self.assertEqual(result["decomposition_method"], "list_decomposition")
        self.assertTrue(len(result["sub_queries"]) > 1)
        
        # Kiểm tra các câu hỏi con
        sub_queries = result["sub_queries"]
        self.assertTrue(any("thuật toán học máy" in q for q in sub_queries))
        self.assertTrue(any("loại" in q for q in sub_queries))
    
    def test_decompose_analysis_query_english(self):
        """
        Test phân rã câu hỏi phân tích tiếng Anh.
        """
        query = "Why is climate change happening?"
        result = self.decomposer.decompose_query(query)
        
        self.assertEqual(result["query_type"], "analysis")
        self.assertEqual(result["decomposition_method"], "analysis_decomposition")
        self.assertTrue(len(result["sub_queries"]) > 1)
        
        # Kiểm tra các câu hỏi con
        sub_queries = result["sub_queries"]
        self.assertTrue(any("climate change" in q for q in sub_queries))
        self.assertTrue(any("causes" in q for q in sub_queries))
    
    def test_decompose_analysis_query_vietnamese(self):
        """
        Test phân rã câu hỏi phân tích tiếng Việt.
        """
        query = "Tại sao biến đổi khí hậu xảy ra?"
        result = self.decomposer.decompose_query(query)
        
        self.assertEqual(result["query_type"], "analysis")
        self.assertEqual(result["decomposition_method"], "analysis_decomposition")
        self.assertTrue(len(result["sub_queries"]) > 1)
        
        # Kiểm tra các câu hỏi con
        sub_queries = result["sub_queries"]
        self.assertTrue(any("biến đổi khí hậu" in q for q in sub_queries))
        self.assertTrue(any("nguyên nhân" in q for q in sub_queries))
    
    def test_decompose_multi_part_query_english(self):
        """
        Test phân rã câu hỏi nhiều phần tiếng Anh.
        """
        query = "What is blockchain and how does it work?"
        result = self.decomposer.decompose_query(query)
        
        self.assertEqual(result["query_type"], "multi_part")
        self.assertEqual(result["decomposition_method"], "multi_part_decomposition")
        self.assertTrue(len(result["sub_queries"]) > 1)
        
        # Kiểm tra các câu hỏi con
        sub_queries = result["sub_queries"]
        self.assertTrue(any("What is blockchain" in q for q in sub_queries))
        self.assertTrue(any("how does it work" in q for q in sub_queries))
    
    def test_decompose_multi_part_query_vietnamese(self):
        """
        Test phân rã câu hỏi nhiều phần tiếng Việt.
        """
        query = "Blockchain là gì và nó hoạt động như thế nào?"
        result = self.decomposer.decompose_query(query)
        
        self.assertEqual(result["query_type"], "multi_part")
        self.assertEqual(result["decomposition_method"], "multi_part_decomposition")
        self.assertTrue(len(result["sub_queries"]) > 1)
        
        # Kiểm tra các câu hỏi con
        sub_queries = result["sub_queries"]
        self.assertTrue(any("Blockchain là gì" in q for q in sub_queries))
        self.assertTrue(any("hoạt động như thế nào" in q for q in sub_queries))
    
    def test_simple_query(self):
        """
        Test câu hỏi đơn giản.
        """
        query = "What is Python?"
        result = self.decomposer.decompose_query(query)
        
        self.assertEqual(result["query_type"], "simple")
        self.assertEqual(result["decomposition_method"], "no_decomposition")
        self.assertEqual(len(result["sub_queries"]), 1)
        self.assertEqual(result["sub_queries"][0], query)

if __name__ == "__main__":
    unittest.main()
