#!/usr/bin/env python3
"""
Simple test for academic search with OpenAlex and Crossref
"""

from src.deep_research_core.agents.web_search_agent import WebSearchAgent

def main():
    """Test academic search with a simple query."""
    agent = WebSearchAgent(
        search_method="api",
        api_search_config={"engine": "academic_combined"},
        verbose=True
    )
    
    result = agent.search("neural networks 2023", num_results=3)
    
    print(f"Success: {result['success']}, Sources: {result.get('sources', [])}")
    print(f"Results count: {len(result.get('results', []))}")
    
    for i, r in enumerate(result.get("results", [])[:2]):
        print(f"\nResult {i+1}:")
        print(f"Title: {r.get('title', '')}")
        print(f"Source: {r.get('source', '')}")
        print(f"URL: {r.get('url', '')}")
        print(f"Published year: {r.get('year', '')}")

if __name__ == "__main__":
    main() 