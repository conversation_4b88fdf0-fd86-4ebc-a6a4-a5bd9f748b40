#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for WebSearchAgentEnhanced.
"""

import sys
import os
import time
import logging
from typing import Dict, Any

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add parent directory to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import WebSearchAgentEnhanced
try:
    from deepresearch.web_search_agent_enhanced import WebSearchAgentEnhanced

    def test_basic_search():
        """Test basic search functionality."""
        print("\n=== Testing Basic Search ===")
        agent = WebSearchAgentEnhanced(verbose=True)
        print(f"Created agent: {agent.name} v{agent.version}")

        # Test a simple search
        print("\nTrying a simple search...")
        results = agent.search("Python programming", num_results=3)

        # Print results
        print(f"Search success: {results.get('success', False)}")
        print(f"Results count: {len(results.get('results', []))}")

        # Print first result
        if results.get('results'):
            first_result = results['results'][0]
            print(f"First result:")
            print(f"  - Title: {first_result.get('title', 'N/A')}")
            print(f"  - URL: {first_result.get('url', 'N/A')}")
            print(f"  - Snippet: {first_result.get('snippet', 'N/A')[:100]}...")

        return results.get('success', False)

    def test_input_validation():
        """Test input validation."""
        print("\n=== Testing Input Validation ===")
        agent = WebSearchAgentEnhanced()

        # Test empty query
        print("\nTesting empty query...")
        try:
            results = agent.search("")
            print("❌ Empty query test failed: No exception raised")
            return False
        except ValueError as e:
            print(f"✅ Empty query test passed: {str(e)}")

        # Test invalid num_results
        print("\nTesting invalid num_results...")
        try:
            results = agent.search("Python", num_results=-1)
            print("❌ Invalid num_results test failed: No exception raised")
            return False
        except ValueError as e:
            print(f"✅ Invalid num_results test passed: {str(e)}")

        # Test invalid engine
        print("\nTesting invalid engine type...")
        try:
            results = agent.search("Python", engine=123)
            print("❌ Invalid engine test failed: No exception raised")
            return False
        except ValueError as e:
            print(f"✅ Invalid engine test passed: {str(e)}")

        return True

    def test_caching():
        """Test caching functionality."""
        print("\n=== Testing Caching ===")
        agent = WebSearchAgentEnhanced(verbose=True)

        # First search (should hit the network)
        print("\nFirst search (should hit the network)...")
        start_time = time.time()
        results1 = agent.search("Python programming language", num_results=2)
        first_search_time = time.time() - start_time
        print(f"First search took {first_search_time:.2f} seconds")

        # Second search (should hit the cache)
        print("\nSecond search (should hit the cache)...")
        start_time = time.time()
        results2 = agent.search("Python programming language", num_results=2)
        second_search_time = time.time() - start_time
        print(f"Second search took {second_search_time:.2f} seconds")

        # Check if second search was faster
        if second_search_time < first_search_time:
            print("✅ Caching test passed: Second search was faster")
            return True
        else:
            print("❌ Caching test failed: Second search was not faster")
            return False

    def test_rate_limiting():
        """Test rate limiting functionality."""
        print("\n=== Testing Rate Limiting ===")

        # Create agent with very low rate limit
        agent = WebSearchAgentEnhanced(rate_limit=1, verbose=True)

        # First search (should succeed)
        print("\nFirst search (should succeed)...")
        results1 = agent.search("Python programming", num_results=1)
        print(f"First search success: {results1.get('success', False)}")

        # Manually set the rate limit counters to exceed limits
        agent.request_count = agent.rate_limit + 1

        # Second search (should hit rate limit)
        print("\nSecond search (should hit rate limit)...")
        results2 = agent.search("Java programming", num_results=1)
        print(f"Second search success: {results2.get('success', False)}")
        print(f"Second search error: {results2.get('error', 'N/A')}")

        # Check if second search hit rate limit
        if not results2.get('success', True) and "rate limit" in results2.get('error', '').lower():
            print("✅ Rate limiting test passed: Second search hit rate limit")
            return True
        else:
            print("❌ Rate limiting test failed: Second search did not hit rate limit")
            return False

    def test_adaptive_scraping():
        """Test adaptive scraping functionality."""
        print("\n=== Testing Adaptive Scraping ===")
        agent = WebSearchAgentEnhanced(verbose=True)

        # Test scraping with a reliable URL
        print("\nTesting scraping with a reliable URL...")
        content = agent._adaptive_scrape("https://www.example.com")

        # Check if content was retrieved
        if content and len(content) > 100:
            print(f"✅ Adaptive scraping test passed: Retrieved {len(content)} bytes")
            return True
        else:
            print("❌ Adaptive scraping test failed: Could not retrieve content")
            return False

    def test_captcha_detection():
        """Test CAPTCHA detection functionality."""
        print("\n=== Testing CAPTCHA Detection ===")
        agent = WebSearchAgentEnhanced(verbose=True)

        # Test with content containing CAPTCHA
        print("\nTesting with content containing CAPTCHA...")
        captcha_content = "<html><body>Please complete this CAPTCHA to continue.</body></html>"
        has_captcha = agent._detect_captcha(captcha_content)

        # Test with content not containing CAPTCHA
        print("\nTesting with content not containing CAPTCHA...")
        normal_content = "<html><body>Welcome to our website.</body></html>"
        no_captcha = agent._detect_captcha(normal_content)

        # Check results
        if has_captcha and not no_captcha:
            print("✅ CAPTCHA detection test passed")
            return True
        else:
            print("❌ CAPTCHA detection test failed")
            return False

    def run_all_tests():
        """Run all tests."""
        print("\n=== Running All Tests ===")

        tests = [
            ("Basic Search", test_basic_search),
            ("Input Validation", test_input_validation),
            ("Caching", test_caching),
            ("Rate Limiting", test_rate_limiting),
            ("Adaptive Scraping", test_adaptive_scraping),
            ("CAPTCHA Detection", test_captcha_detection),
        ]

        results = {}
        for name, test_func in tests:
            print(f"\nRunning test: {name}")
            try:
                result = test_func()
                results[name] = result
                print(f"Test {name}: {'✅ PASSED' if result else '❌ FAILED'}")
            except Exception as e:
                results[name] = False
                print(f"Test {name}: ❌ ERROR - {str(e)}")

        # Print summary
        print("\n=== Test Summary ===")
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        print(f"Passed: {passed}/{total} ({passed/total*100:.1f}%)")

        for name, result in results.items():
            print(f"{name}: {'✅ PASSED' if result else '❌ FAILED'}")

        return passed == total

except ImportError as e:
    print(f"Import error: {str(e)}")
except Exception as e:
    print(f"Error: {str(e)}")

if __name__ == "__main__":
    run_all_tests()
