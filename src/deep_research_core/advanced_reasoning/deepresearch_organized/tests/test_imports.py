import sys
print(f"Python version: {sys.version}")
print("Trying to import PPO modules...")

try:
    from deep_research_core.rl_tuning.ppo.base import BasePPO
    print("Successfully imported BasePPO")
except ImportError as e:
    print(f"Failed to import BasePPO: {e}")

try:
    from deep_research_core.rl_tuning.ppo.dataset import PPOExperience, PPODataset
    print("Successfully imported PPOExperience and PPODataset")
except ImportError as e:
    print(f"Failed to import PPOExperience and PPODataset: {e}")

try:
    from deep_research_core.rl_tuning.ppo.ppo import PPO
    print("Successfully imported PPO")
except ImportError as e:
    print(f"Failed to import PPO: {e}")

print("Import test completed")
