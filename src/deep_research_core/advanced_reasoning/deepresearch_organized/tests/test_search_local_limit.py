#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script to check if local search gets blocked after many requests.
"""

import sys
import os
import time
import json
import random
import logging
from datetime import datetime
from tqdm import tqdm
import argparse

# Thiết lập logger
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("search_test_results.log"), logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

# Import WebSearchAgent
try:
    from src.deep_research_core.agents.web_search_agent import WebSearchAgent
    from src.deep_research_core.agents.searxng_search import (
        search_searxng,
        search_with_fallback,
    )
except ImportError:
    try:
        # Thử import WebSearchAgentLocal thay thế
        from web_search_agent_local import WebSearchAgentLocal as WebSearchAgent
    except ImportError:
        logger.error("Failed to import WebSearchAgent. Make sure the path is correct.")
        sys.exit(1)

# Danh sách các chủ đề để tạo câu hỏi ngẫu nhiên
TOPICS = [
    "Python",
    "JavaScript",
    "Machine Learning",
    "Data Science",
    "Web Development",
    "Artificial Intelligence",
    "Blockchain",
    "Cybersecurity",
    "Cloud Computing",
    "DevOps",
    "Mobile Development",
    "Game Development",
    "IoT",
    "Big Data",
    "Quantum Computing",
    "Robotics",
    "Virtual Reality",
    "Augmented Reality",
    "Natural Language Processing",
    "Computer Vision",
]

# Danh sách các từ khóa để tạo câu hỏi ngẫu nhiên
QUESTION_TEMPLATES = [
    "What is {}?",
    "How to learn {}?",
    "Best resources for {}?",
    "How does {} work?",
    "Why is {} important?",
    "Compare {} with other technologies",
    "Future of {}",
    "History of {}",
    "Applications of {}",
    "Jobs in {}",
    "Salary for {} developers",
    "Companies using {}",
    "Problems with {}",
    "Advantages of {}",
    "Disadvantages of {}",
    "Tools for {}",
    "Frameworks for {}",
    "Libraries for {}",
    "Courses on {}",
    "Books about {}",
]


def generate_random_question():
    """Generate a random question."""
    topic = random.choice(TOPICS)
    template = random.choice(QUESTION_TEMPLATES)
    return template.format(topic)


def generate_questions(num_questions):
    """Generate a list of random questions."""
    questions = []
    for _ in range(num_questions):
        questions.append(generate_random_question())
    return questions


def test_search_local(num_questions=100, delay=1.0, max_retries=3, output_file=None):
    """Test if local search gets blocked after many requests."""
    # Create WebSearchAgent
    agent = WebSearchAgent(
        search_method="api",
        api_search_config={"engine": "searx", "searx_url": "http://localhost:8080"},
        verbose=True,
    )

    # Generate questions
    logger.info(f"Generating {num_questions} random questions...")
    questions = generate_questions(num_questions)

    # Statistics
    stats = {
        "total": num_questions,
        "success": 0,
        "failure": 0,
        "captcha_detected": 0,
        "empty_results": 0,
        "errors": {},
    }

    # Results
    results = []

    # Search for each question
    logger.info(f"Starting search test with {num_questions} questions...")
    start_time = time.time()

    for i, question in enumerate(tqdm(questions, desc="Searching")):
        retry_count = 0
        success = False

        while retry_count < max_retries and not success:
            try:
                # Search
                result = agent.search(question, num_results=2)

                # Check if successful
                if result.get("success") and result.get("results"):
                    stats["success"] += 1
                    success = True
                    results.append(
                        {
                            "question": question,
                            "success": True,
                            "result_count": len(result.get("results", [])),
                            "timestamp": datetime.now().isoformat(),
                        }
                    )
                elif "captcha" in str(result.get("error", "")).lower():
                    stats["captcha_detected"] += 1
                    stats["failure"] += 1
                    results.append(
                        {
                            "question": question,
                            "success": False,
                            "error": "CAPTCHA detected",
                            "timestamp": datetime.now().isoformat(),
                        }
                    )
                    logger.warning(f"CAPTCHA detected at question {i+1}: {question}")
                    break  # No need to retry if CAPTCHA is detected
                elif not result.get("results"):
                    stats["empty_results"] += 1
                    stats["failure"] += 1
                    results.append(
                        {
                            "question": question,
                            "success": False,
                            "error": "Empty results",
                            "timestamp": datetime.now().isoformat(),
                        }
                    )
                else:
                    error_msg = result.get("error", "Unknown error")
                    stats["errors"][error_msg] = stats["errors"].get(error_msg, 0) + 1
                    stats["failure"] += 1
                    results.append(
                        {
                            "question": question,
                            "success": False,
                            "error": error_msg,
                            "timestamp": datetime.now().isoformat(),
                        }
                    )

                    # Log error
                    logger.error(f"Error at question {i+1}: {error_msg}")

                    # Increment retry count
                    retry_count += 1
            except Exception as e:
                error_msg = str(e)
                stats["errors"][error_msg] = stats["errors"].get(error_msg, 0) + 1
                stats["failure"] += 1
                results.append(
                    {
                        "question": question,
                        "success": False,
                        "error": error_msg,
                        "timestamp": datetime.now().isoformat(),
                    }
                )

                # Log error
                logger.error(f"Exception at question {i+1}: {error_msg}")

                # Increment retry count
                retry_count += 1

            # Delay between requests
            if i < num_questions - 1 or retry_count < max_retries:
                time.sleep(delay)

        # Check if we need to stop due to too many CAPTCHAs
        if stats["captcha_detected"] > 5:
            logger.warning("Too many CAPTCHAs detected. Stopping test.")
            break

    # Calculate elapsed time
    elapsed_time = time.time() - start_time

    # Calculate success rate
    success_rate = (
        (stats["success"] / stats["total"]) * 100 if stats["total"] > 0 else 0
    )

    # Log statistics
    logger.info(f"Test completed in {elapsed_time:.2f} seconds")
    logger.info(f"Success rate: {success_rate:.2f}%")
    logger.info(f"Total: {stats['total']}")
    logger.info(f"Success: {stats['success']}")
    logger.info(f"Failure: {stats['failure']}")
    logger.info(f"CAPTCHA detected: {stats['captcha_detected']}")
    logger.info(f"Empty results: {stats['empty_results']}")
    logger.info(f"Errors: {stats['errors']}")

    # Save results to file
    output_path = output_file if output_file else "search_test_results.json"
    with open(output_path, "w") as f:
        json.dump({"stats": stats, "results": results}, f, indent=2)
    logger.info(f"Results saved to {output_path}")

    return stats, results


def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Test if local search gets blocked after many requests."
    )
    parser.add_argument(
        "--num-questions", type=int, default=100, help="Number of questions to generate"
    )
    parser.add_argument(
        "--delay", type=float, default=1.0, help="Delay between requests in seconds"
    )
    parser.add_argument(
        "--max-retries",
        type=int,
        default=3,
        help="Maximum number of retries per question",
    )
    parser.add_argument(
        "--output",
        type=str,
        help="Output file path for results (default: search_test_results.json)",
    )
    args = parser.parse_args()

    print(f"Testing local search with {args.num_questions} questions...")
    print(f"Delay between requests: {args.delay} seconds")
    print(f"Maximum retries per question: {args.max_retries}")
    if args.output:
        print(f"Results will be saved to: {args.output}")

    stats, _ = test_search_local(
        num_questions=args.num_questions,
        delay=args.delay,
        max_retries=args.max_retries,
        output_file=args.output,
    )

    print("\nTest completed!")
    print(f"Success rate: {(stats['success'] / stats['total']) * 100:.2f}%")
    output_path = args.output if args.output else "search_test_results.json"
    print(f"Results saved to {output_path}")


if __name__ == "__main__":
    main()
