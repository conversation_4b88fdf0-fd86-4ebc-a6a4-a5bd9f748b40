import sys
import os

# Add the project root to the Python path
sys.path.append('/home/<USER>/Desktop/automation-tool/deep_research_core')

# Import the module directly
sys.path.append('/home/<USER>/Desktop/automation-tool/deep_research_core/src/deep_research_core/agents')
import web_search_methods

# Test disabled search methods
print("Testing disabled search methods...")

print("Testing search_google...")
result = web_search_methods.search_google("test query", "fake_api_key", "fake_cse_id", num_results=2)
print(f"search_google result: {result}")

print("\nTesting search_bing...")
result = web_search_methods.search_bing("test query", "fake_api_key", num_results=2)
print(f"search_bing result: {result}")

print("\nTesting search_serper...")
result = web_search_methods.search_serper("test query", "fake_api_key", num_results=2)
print(f"search_serper result: {result}")

print("\nTesting search_serpapi...")
result = web_search_methods.search_serpapi("test query", "fake_api_key", num_results=2)
print(f"search_serpapi result: {result}")

print("\nTesting search_brave...")
result = web_search_methods.search_brave("test query", "fake_api_key", num_results=2)
print(f"search_brave result: {result}")

print("\nTesting search_yandex...")
result = web_search_methods.search_yandex("test query", "fake_api_key", num_results=2)
print(f"search_yandex result: {result}")

# Test search_qwant
print("\nTesting search_qwant...")
try:
    result = web_search_methods.search_qwant("test query", num_results=2)
    print(f"search_qwant result: {result}")
except Exception as e:
    print(f"Error with search_qwant: {str(e)}")
