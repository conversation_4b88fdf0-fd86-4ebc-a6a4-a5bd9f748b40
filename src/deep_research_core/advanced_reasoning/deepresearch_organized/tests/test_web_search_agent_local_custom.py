#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test WebSearchAgentLocal với các truy vấn tùy chỉnh và xuất kết quả dưới dạng JSON.
"""

import os
import sys
import json
import time
import argparse
from datetime import datetime

# Thêm thư mục cha vào sys.path để import các module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from web_search_agent_local import WebSearchAgentLocal
except ImportError:
    print("Không thể import WebSearchAgentLocal. <PERSON><PERSON><PERSON> bảo bạn đang chạy từ thư mục gốc.")
    sys.exit(1)


def print_section(title):
    """In tiêu đề phần."""
    print("\n" + "=" * 80)
    print(title.center(80, "="))
    print("=" * 80 + "\n")


def save_result_to_json(result, filename):
    """Lưu kết quả vào file JSON."""
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    print(f"\nĐã lưu kết quả vào file: {filename}")


def run_test_with_query(query, num_results=5, get_content=True, evaluate_question=True, evaluate_answer=True, output_file=None):
    """Chạy test với truy vấn cụ thể."""
    print_section(f"Test với truy vấn: {query}")
    
    # Khởi tạo WebSearchAgentLocal
    agent = WebSearchAgentLocal()
    
    # Thực hiện tìm kiếm
    print(f"Đang tìm kiếm với truy vấn: {query}")
    print(f"Số kết quả: {num_results}")
    print(f"Trích xuất nội dung: {get_content}")
    print(f"Đánh giá câu hỏi: {evaluate_question}")
    print(f"Đánh giá câu trả lời: {evaluate_answer}")
    
    start_time = time.time()
    result = agent.search(
        query,
        num_results=num_results,
        get_content=get_content,
        evaluate_question=evaluate_question,
        evaluate_answer=evaluate_answer
    )
    end_time = time.time()
    
    # Thêm thông tin thời gian thực hiện
    result["execution_time"] = end_time - start_time
    
    # In kết quả
    print(f"\nThời gian thực hiện: {result['execution_time']:.2f} giây")
    print(f"Thành công: {result.get('success', False)}")
    print(f"Phương thức tìm kiếm: {result.get('method', 'N/A')}")
    print(f"Số kết quả: {len(result.get('results', []))}")
    
    # In thông tin đánh giá câu hỏi nếu có
    if "question_evaluation" in result:
        qe = result["question_evaluation"]
        print("\n----- ĐÁNH GIÁ CÂU HỎI -----")
        print(f"Độ phức tạp: {qe.get('complexity_level', 'N/A')}")
        print(f"Điểm số: {qe.get('complexity_score', 'N/A')}")
        if "analysis" in qe:
            print(f"Phân tích: {qe['analysis']}")
    
    # In thông tin đánh giá câu trả lời nếu có
    if "answer_evaluation" in result:
        ae = result["answer_evaluation"]
        print("\n----- ĐÁNH GIÁ CÂU TRẢ LỜI -----")
        print(f"Điểm tổng thể: {ae.get('overall_score', 'N/A')}")
        print(f"Cần tìm kiếm thêm: {ae.get('need_more_search', 'N/A')}")
        if "detailed_evaluation" in ae:
            print(f"Đánh giá chi tiết: {ae['detailed_evaluation']}")
    
    # In chi tiết từng kết quả
    if result.get("results"):
        print("\n----- CHI TIẾT KẾT QUẢ -----")
        for i, res in enumerate(result["results"]):
            print(f"\nKết quả #{i+1}:")
            print(f"  - Tiêu đề: {res.get('title', 'N/A')}")
            print(f"  - URL: {res.get('url', 'N/A')}")
            print(f"  - Snippet: {res.get('snippet', 'N/A')}")
            if get_content and "content" in res and res["content"]:
                content_preview = res["content"][:200] + "..." if len(res["content"]) > 200 else res["content"]
                print(f"  - Nội dung: {content_preview}")
    
    # Lưu kết quả vào file JSON nếu có
    if output_file:
        save_result_to_json(result, output_file)
    
    return result


def main():
    """Hàm chính."""
    parser = argparse.ArgumentParser(description="Test WebSearchAgentLocal với các truy vấn tùy chỉnh.")
    parser.add_argument("--query", type=str, help="Truy vấn tìm kiếm")
    parser.add_argument("--queries-file", type=str, help="File chứa danh sách các truy vấn (mỗi truy vấn một dòng)")
    parser.add_argument("--num-results", type=int, default=5, help="Số kết quả trả về (mặc định: 5)")
    parser.add_argument("--no-content", action="store_true", help="Không trích xuất nội dung")
    parser.add_argument("--no-evaluate-question", action="store_true", help="Không đánh giá câu hỏi")
    parser.add_argument("--no-evaluate-answer", action="store_true", help="Không đánh giá câu trả lời")
    parser.add_argument("--output-dir", type=str, default="test_results", help="Thư mục lưu kết quả (mặc định: test_results)")
    parser.add_argument("--output-format", type=str, default="json", choices=["json", "jsonl"], help="Định dạng đầu ra (mặc định: json)")
    args = parser.parse_args()
    
    # Tạo thư mục lưu kết quả nếu chưa tồn tại
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Danh sách các truy vấn mặc định nếu không có truy vấn nào được cung cấp
    default_queries = [
        "Phân tích tác động của biến đổi khí hậu đến nông nghiệp Việt Nam trong 10 năm qua",
        "So sánh chi tiết giữa React và Vue.js về hiệu suất và quản lý state",
        "Giải thích cơ chế hoạt động của mạng neural tích chập (CNN) trong xử lý ảnh",
        "Phân tích ưu nhược điểm của các phương pháp xác thực hai yếu tố phổ biến hiện nay",
        "Tổng hợp các nghiên cứu gần đây về ứng dụng trí tuệ nhân tạo trong y tế"
    ]
    
    # Lấy danh sách các truy vấn
    queries = []
    if args.query:
        queries.append(args.query)
    elif args.queries_file:
        try:
            with open(args.queries_file, "r", encoding="utf-8") as f:
                queries = [line.strip() for line in f if line.strip()]
        except Exception as e:
            print(f"Lỗi khi đọc file truy vấn: {str(e)}")
            sys.exit(1)
    else:
        queries = default_queries
    
    print_section("Test WebSearchAgentLocal với các truy vấn tùy chỉnh")
    print(f"Số lượng truy vấn: {len(queries)}")
    
    # Tạo tên file đầu ra
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Chạy test với từng truy vấn
    all_results = []
    for i, query in enumerate(queries):
        # Tạo tên file đầu ra cho từng truy vấn
        if args.output_format == "json":
            output_file = os.path.join(args.output_dir, f"query_{i+1}_{timestamp}.json")
        else:
            output_file = None  # Sẽ lưu tất cả vào một file JSONL
        
        # Chạy test
        result = run_test_with_query(
            query,
            num_results=args.num_results,
            get_content=not args.no_content,
            evaluate_question=not args.no_evaluate_question,
            evaluate_answer=not args.no_evaluate_answer,
            output_file=output_file
        )
        
        # Thêm vào danh sách kết quả
        all_results.append(result)
    
    # Lưu tất cả kết quả vào một file JSONL nếu cần
    if args.output_format == "jsonl":
        jsonl_file = os.path.join(args.output_dir, f"all_queries_{timestamp}.jsonl")
        with open(jsonl_file, "w", encoding="utf-8") as f:
            for result in all_results:
                f.write(json.dumps(result, ensure_ascii=False) + "\n")
        print(f"\nĐã lưu tất cả kết quả vào file: {jsonl_file}")
    
    print("\nHoàn thành tất cả các test!")


if __name__ == "__main__":
    main()
