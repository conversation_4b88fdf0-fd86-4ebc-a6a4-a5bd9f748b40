#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for WebSearchAgent using mock data.
This script tests the basic functionality of WebSearchAgent without making actual API calls.
"""

import sys
import os
import json
import time
import logging
from typing import Dict, Any, List, Optional
from unittest.mock import patch, MagicMock

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add the project root to the Python path
sys.path.append('/home/<USER>/Desktop/automation-tool/deep_research_core')

# Import the WebSearchAgent
try:
    from src.deep_research_core.agents.web_search_agent import WebSearchAgent
except ImportError:
    try:
        from deep_research_core.agents.web_search_agent import WebSearchAgent
    except ImportError:
        logger.error("Could not import WebSearchAgent. Check your Python path.")
        sys.exit(1)

def print_section(title):
    """Print a section header."""
    print("\n" + "=" * 50)
    print(f" {title}")
    print("=" * 50)

def print_result(result):
    """Print search result in a readable format."""
    if not result:
        print("No result returned")
        return
    
    print(f"Success: {result.get('success', False)}")
    print(f"Engine: {result.get('engine', 'unknown')}")
    print(f"Search method: {result.get('search_method', 'unknown')}")
    print(f"Query: {result.get('query', '')}")
    print(f"Results count: {len(result.get('results', []))}")
    
    if result.get('from_cache', False):
        print("Results retrieved from cache")
    
    if result.get('fallback', False):
        print(f"Fallback used. Original method: {result.get('original_method', 'unknown')}")
    
    # Print first 3 results
    for i, item in enumerate(result.get('results', [])[:3]):
        print(f"\nResult {i+1}:")
        print(f"Title: {item.get('title', '')}")
        print(f"URL: {item.get('url', '')}")
        print(f"Snippet: {item.get('snippet', '')[:100]}...")

def save_result(query, result, filename):
    """Save search result to a JSON file."""
    result_dir = "test_results"
    os.makedirs(result_dir, exist_ok=True)
    
    filepath = os.path.join(result_dir, filename)
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump({
            "query": query,
            "result": result
        }, f, ensure_ascii=False, indent=2)
    
    logger.info(f"Result saved to {filepath}")

def get_mock_search_result(query, engine="duckduckgo"):
    """Generate mock search result for testing."""
    return {
        "success": True,
        "query": query,
        "results": [
            {
                "title": f"Mock result 1 for {query}",
                "url": "https://example.com/result1",
                "snippet": f"This is a mock snippet for {query}. It contains some information about the query.",
                "source": engine
            },
            {
                "title": f"Mock result 2 for {query}",
                "url": "https://example.com/result2",
                "snippet": f"Another mock snippet for {query}. This one has different information.",
                "source": engine
            },
            {
                "title": f"Mock result 3 for {query}",
                "url": "https://example.com/result3",
                "snippet": f"A third mock snippet for {query}. This provides yet more information.",
                "source": engine
            }
        ],
        "engine": engine,
        "search_method": "api",
        "timestamp": time.time()
    }

@patch('deep_research_core.agents.web_search_agent.WebSearchAgent._search_api')
def test_basic_search(mock_search_api):
    """Test basic search functionality with mock data."""
    print_section("Basic Search Test (Mock)")
    
    # Set up mock
    mock_search_api.return_value = get_mock_search_result("test query")
    
    try:
        # Initialize WebSearchAgent
        agent = WebSearchAgent(
            search_method="api",
            api_search_config={"engine": "duckduckgo"},
            verbose=True
        )
        
        # Perform a search
        query = "test query"
        logger.info(f"Searching for: '{query}'")
        
        result = agent.search(query, num_results=3)
        print_result(result)
        save_result(query, result, "mock_basic_search_result.json")
        
        return result.get('success', False)
    except Exception as e:
        logger.error(f"Error in basic search test: {str(e)}")
        return False

@patch('deep_research_core.agents.web_search_agent.WebSearchAgent._search_api')
@patch('deep_research_core.agents.web_search_agent.WebSearchAgent._search_crawlee')
def test_advanced_search(mock_search_crawlee, mock_search_api):
    """Test advanced search functionality with mock data."""
    print_section("Advanced Search Test (Mock)")
    
    # Set up mocks
    mock_search_api.return_value = get_mock_search_result("complex query", "duckduckgo")
    mock_search_crawlee.return_value = get_mock_search_result("complex query", "crawlee")
    
    try:
        # Initialize WebSearchAgent
        agent = WebSearchAgent(
            search_method="auto",
            api_search_config={"engine": "duckduckgo"},
            verbose=True
        )
        
        # Perform a search
        query = "complex query"
        logger.info(f"Searching for: '{query}'")
        
        result = agent.search(query, num_results=3)
        print_result(result)
        save_result(query, result, "mock_advanced_search_result.json")
        
        return result.get('success', False)
    except Exception as e:
        logger.error(f"Error in advanced search test: {str(e)}")
        return False

@patch('deep_research_core.agents.web_search_agent.WebSearchAgent._search_api')
def test_cache_functionality(mock_search_api):
    """Test cache functionality with mock data."""
    print_section("Cache Functionality Test (Mock)")
    
    # Set up mock
    mock_search_api.return_value = get_mock_search_result("cache test query")
    
    try:
        # Initialize WebSearchAgent with cache
        agent = WebSearchAgent(
            search_method="api",
            api_search_config={"engine": "duckduckgo"},
            use_smart_cache=True,
            cache_ttl=3600,
            verbose=True
        )
        
        # Perform first search
        query = "cache test query"
        logger.info(f"First search for: '{query}'")
        
        result1 = agent.search(query, num_results=3)
        print_result(result1)
        
        # Perform second search with same query (should use cache)
        logger.info(f"Second search for: '{query}' (should use cache)")
        
        # Mock should only be called once
        result2 = agent.search(query, num_results=3)
        print_result(result2)
        
        # Check if second search used cache
        cache_used = mock_search_api.call_count == 1
        logger.info(f"Cache used: {cache_used}")
        
        save_result(query, {
            "first_search": result1,
            "second_search": result2,
            "cache_used": cache_used
        }, "mock_cache_test_result.json")
        
        return cache_used
    except Exception as e:
        logger.error(f"Error in cache functionality test: {str(e)}")
        return False

def main():
    """Run all tests and report results."""
    print_section("WebSearchAgent Mock Tests")
    
    results = {
        "basic_search": test_basic_search(),
        "advanced_search": test_advanced_search(),
        "cache_functionality": test_cache_functionality()
    }
    
    print_section("Test Results Summary")
    for test_name, success in results.items():
        print(f"{test_name}: {'PASS' if success else 'FAIL'}")
    
    # Save overall results
    os.makedirs("test_results", exist_ok=True)
    with open("test_results/mock_overall_results.json", 'w') as f:
        json.dump(results, f, indent=2)
    
    # Return overall success
    return all(results.values())

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
