#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for file download functionality.
"""

import os
import sys
import unittest
import logging
import shutil
import requests
from typing import Dict, Any, List

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Thêm thư mục gốc vào sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import WebSearchFileProcessor
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))
from src.deep_research_core.agents.web_search_file_processor import WebSearchFileProcessor


class TestFileDownload(unittest.TestCase):
    """
    Test case for file download functionality.
    """

    def setUp(self):
        """
        Set up test environment.
        """
        # Create a temporary directory for downloaded files
        self.download_dir = "test_downloads"
        if not os.path.exists(self.download_dir):
            os.makedirs(self.download_dir)

        # Initialize WebSearchFileProcessor
        self.file_processor = WebSearchFileProcessor(
            download_dir=self.download_dir
        )

    def tearDown(self):
        """
        Clean up after tests.
        """
        # Xóa thư mục tạm
        if os.path.exists(self.download_dir):
            shutil.rmtree(self.download_dir)



    def test_is_file_url(self):
        """
        Test is_file_url method.
        """
        # Test URLs that should be detected as files
        file_urls = [
            "https://www.example.com/documents/sample.pdf",
            "https://www.example.com/files/data.xlsx",
            "https://www.example.com/download/presentation.pptx",
            "https://www.example.com/attachments/document.docx",
            "https://www.example.com/file.txt",
            "https://www.example.com/download?file=report.pdf",
        ]

        # Test URLs that should not be detected as files
        non_file_urls = [
            "https://www.example.com",
            "https://www.example.com/about",
            "https://www.example.com/contact-us",
            "https://www.example.com/blog/post-1",
        ]

        # Check file URLs
        for url in file_urls:
            self.assertTrue(
                self.file_processor.is_file_url(url),
                f"URL {url} should be detected as a file URL"
            )

        # Check non-file URLs
        for url in non_file_urls:
            self.assertFalse(
                self.file_processor.is_file_url(url),
                f"URL {url} should not be detected as a file URL"
            )

    def test_download_file(self):
        """
        Test downloading a file.
        """
        # URL of a PDF file
        pdf_url = "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"

        # Download the file
        result = self.file_processor.download_file(pdf_url)

        # Check the result
        self.assertTrue(result["success"], "Download should be successful")
        self.assertIsNotNone(result["file_path"], "File path should be provided")
        self.assertTrue(os.path.exists(result["file_path"]), "File should exist on disk")

        # Log file information
        logger.info(f"Downloaded file: {result['file_path']}")
        logger.info(f"File size: {result['size']} bytes")
        logger.info(f"Content type: {result['content_type']}")

        # If text content was extracted, check it
        if "text_content" in result and result["text_content"]:
            logger.info(f"Text content length: {len(result['text_content'])}")
            self.assertIsNotNone(result["text_content"], "Text content should be extracted")


if __name__ == "__main__":
    unittest.main()
