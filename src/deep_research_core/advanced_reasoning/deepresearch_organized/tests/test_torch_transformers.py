"""
Kiểm tra PyTorch và Transformers.
"""

import sys
print(f"Python version: {sys.version}")

# Kiểm tra PyTorch
try:
    import torch
    print(f"PyTorch đã được cài đặt!")
    print(f"PyTorch version: {torch.__version__}")
    
    # Kiểm tra CUDA
    if hasattr(torch, 'cuda'):
        print(f"PyTorch có hỗ trợ CUDA")
        cuda_available = torch.cuda.is_available()
        print(f"CUDA available: {cuda_available}")
        
        if cuda_available:
            device_count = torch.cuda.device_count()
            print(f"Số lượng thiết bị CUDA: {device_count}")
            
            for i in range(device_count):
                device_name = torch.cuda.get_device_name(i)
                print(f"  Thiết bị {i}: {device_name}")
    else:
        print("PyTorch không có hỗ trợ CUDA")
except ImportError as e:
    print(f"Lỗi khi import torch: {e}")

# Kiểm tra Transformers
try:
    import transformers
    print(f"\nTransformers đã được cài đặt!")
    print(f"Transformers version: {transformers.__version__}")
    
    # Kiểm tra is_torch_npu_available
    if hasattr(transformers, 'is_torch_npu_available'):
        print(f"Transformers có hàm is_torch_npu_available")
        npu_available = transformers.is_torch_npu_available()
        print(f"NPU available: {npu_available}")
    else:
        print("Transformers không có hàm is_torch_npu_available")
    
    # Kiểm tra AutoTokenizer và AutoModel
    print("\nKiểm tra AutoTokenizer và AutoModel...")
    try:
        from transformers import AutoTokenizer, AutoModel
        print("Đã import AutoTokenizer và AutoModel thành công")
    except ImportError as e:
        print(f"Lỗi khi import AutoTokenizer và AutoModel: {e}")
    except Exception as e:
        print(f"Lỗi khác khi import AutoTokenizer và AutoModel: {e}")
except ImportError as e:
    print(f"\nLỗi khi import transformers: {e}")

print("\nKiểm tra hoàn tất!")
