#!/usr/bin/env python3

import sys
import os
import json

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

# Import directly
from src.deep_research_core.agents.search_result_optimizer import SearchResultOptimizer

def main():
    # Create an optimizer
    optimizer = SearchResultOptimizer(
        relevance_threshold=0.3,
        quality_threshold=0.2,
        max_content_length=1000,
        max_results=5,
        vietnamese_support=True
    )
    
    # Sample search results
    sample_results = {
        "success": True,
        "query": "Python programming",
        "results": [
            {
                "title": "Python Programming Language",
                "url": "https://www.python.org/",
                "snippet": "Python is a programming language that lets you work quickly.",
                "content": "Python is a programming language that lets you work quickly."
            },
            {
                "title": "Learn Python - Free Interactive Python Tutorial",
                "url": "https://www.learnpython.org/",
                "snippet": "Learn Python, a powerful programming language.",
                "content": "Learn Python, a powerful programming language."
            },
            {
                "title": "Python Snake Facts",
                "url": "https://www.snakefacts.com/python/",
                "snippet": "Learn about python snakes, their habitat, diet, and behavior.",
                "content": "Python snakes are some of the largest snakes in the world."
            }
        ],
        "timestamp": 1625097600
    }
    
    # Optimize results
    optimized = optimizer.optimize(sample_results, "Python programming")
    
    # Print results
    print("Original results count:", len(sample_results["results"]))
    print("Optimized results count:", len(optimized["results"]))
    print("\nOptimized results:")
    print(json.dumps(optimized, indent=2))
    
    # Check if snake result was filtered out
    snake_found = False
    for result in optimized["results"]:
        if "snake" in result.get("title", "").lower() or "snake" in result.get("content", "").lower():
            snake_found = True
            break
    
    print("\nSnake result filtered out:", not snake_found)
    
    print("\nTest completed successfully!")

if __name__ == "__main__":
    main()
