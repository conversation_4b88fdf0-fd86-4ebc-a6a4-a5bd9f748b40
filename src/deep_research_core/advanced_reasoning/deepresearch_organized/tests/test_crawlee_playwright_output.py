#!/usr/bin/env python3
"""
Test script để kiểm tra đầu ra của WebSearchAgentLocal khi sử dụng Crawlee và Playwright.
Script này sẽ thực hiện tìm kiếm với nhiều câu hỏi khác nhau và lưu kết quả vào file JSON.
"""

import sys
import os
import json
import time
import argparse
from typing import Dict, Any, List
import logging

# Thêm thư mục gốc vào sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

# Import WebSearchAgentLocal
from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal

# Cấu hình logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def save_results_to_json(results: Dict[str, Any], filename: str) -> None:
    """
    <PERSON>ưu kết quả tìm kiếm vào file JSON.
    """
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    logger.info(f"Đã lưu kết quả vào file: {filename}")

def print_result_summary(result: Dict[str, Any]) -> None:
    """
    In tóm tắt kết quả tìm kiếm.
    """
    if not result.get("success"):
        logger.error(f"Tìm kiếm thất bại: {result.get('error', 'Unknown error')}")
        return
    
    print(f"Truy vấn: {result.get('query', 'N/A')}")
    print(f"Số kết quả: {len(result.get('results', []))}")
    print(f"Đã tối ưu hóa: {'Có' if result.get('optimized') else 'Không'}")
    
    if result.get("quality_metrics"):
        metrics = result.get("quality_metrics")
        print("\nChỉ số chất lượng:")
        print(f"- Tổng thể: {metrics.get('overall', 'N/A')}")
        
        if "count" in metrics:
            count = metrics.get("count", {})
            print(f"- Số lượng: {count.get('value', 'N/A')} ({count.get('quality', 'N/A')})")
        
        if "content_length" in metrics:
            length = metrics.get("content_length", {})
            print(f"- Độ dài nội dung: {length.get('value', 'N/A')} ký tự ({length.get('quality', 'N/A')})")
        
        if "diversity" in metrics:
            diversity = metrics.get("diversity", {})
            print(f"- Đa dạng: {diversity.get('value', 'N/A')} nguồn ({diversity.get('quality', 'N/A')})")
        
        if "relevance" in metrics:
            relevance = metrics.get("relevance", {})
            print(f"- Độ liên quan: {relevance.get('value', 'N/A'):.2f} ({relevance.get('quality', 'N/A')})")
    
    print("\nKết quả:")
    for i, res in enumerate(result.get("results", []), 1):
        print(f"{i}. {res.get('title', 'N/A')}")
        print(f"   URL: {res.get('url', 'N/A')}")
        print(f"   Độ liên quan: {res.get('relevance_score', 'N/A'):.2f}")
        content = res.get("content", "")
        print(f"   Độ dài nội dung: {len(content)} ký tự")
        print(f"   Chất lượng nội dung: {res.get('content_quality', 'N/A')}")
        print()

def test_search_with_query(agent: WebSearchAgentLocal, query: str, language: str, output_dir: str) -> Dict[str, Any]:
    """
    Thực hiện tìm kiếm với một truy vấn và lưu kết quả.
    """
    logger.info(f"Đang tìm kiếm: '{query}' (Ngôn ngữ: {language})")
    
    # Tạo tên file đầu ra
    query_slug = query.lower().replace(" ", "_")[:30]
    filename = f"{output_dir}/search_result_{query_slug}_{int(time.time())}.json"
    
    # Thực hiện tìm kiếm
    start_time = time.time()
    result = agent.search(
        query=query,
        num_results=5,
        method="crawlee",  # Sử dụng Crawlee
        language=language,
        get_content=True,  # Lấy nội dung
        force_refresh=True,  # Không sử dụng cache
        optimize_for_llm=True,  # Tối ưu hóa cho LLM
        max_content_length=5000  # Độ dài tối đa của nội dung
    )
    end_time = time.time()
    
    # Thêm thông tin thời gian
    result["search_time"] = end_time - start_time
    
    # In tóm tắt
    print("\n" + "=" * 80)
    print(f"KẾT QUẢ TÌM KIẾM: '{query}'")
    print(f"Thời gian tìm kiếm: {end_time - start_time:.2f} giây")
    print("=" * 80)
    print_result_summary(result)
    
    # Lưu kết quả
    save_results_to_json(result, filename)
    
    return result

def main():
    """
    Hàm chính.
    """
    # Parse arguments
    parser = argparse.ArgumentParser(description="Test WebSearchAgentLocal với Crawlee và Playwright")
    parser.add_argument("--output-dir", default="search_results", help="Thư mục lưu kết quả")
    args = parser.parse_args()
    
    # Tạo thư mục đầu ra nếu chưa tồn tại
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Khởi tạo WebSearchAgentLocal
    agent = WebSearchAgentLocal(
        search_method="crawlee",  # Sử dụng Crawlee
        api_search_config={
            "engine": "searx",
            "searx_url": "http://localhost:8080",  # URL của SearXNG local
            "language": "auto"
        },
        crawlee_search_config={
            "max_depth": 2,
            "max_pages_per_url": 3,
            "max_urls": 5,
            "timeout": 60  # Tăng timeout để tránh lỗi
        },
        content_extractor_config={
            "extract_links": True,
            "extract_images": False,
            "max_content_length": 10000,
            "timeout": 30
        },
        verbose=True
    )
    
    # Danh sách các câu hỏi để kiểm tra
    test_queries = [
        # Công nghệ
        {"query": "Python programming for beginners", "language": "en"},
        {"query": "Lập trình Python cho người mới bắt đầu", "language": "vi"},
        
        # Y tế
        {"query": "Symptoms of diabetes", "language": "en"},
        {"query": "Triệu chứng của bệnh tiểu đường", "language": "vi"},
        
        # Tài chính
        {"query": "How to invest in stocks", "language": "en"},
        {"query": "Cách đầu tư chứng khoán hiệu quả", "language": "vi"},
        
        # Giáo dục
        {"query": "Effective English learning methods", "language": "en"},
        {"query": "Phương pháp học tiếng Anh hiệu quả", "language": "vi"},
        
        # Khoa học
        {"query": "Quantum computing explained", "language": "en"},
        {"query": "Giải thích về máy tính lượng tử", "language": "vi"},
        
        # Câu hỏi ngắn
        {"query": "Capital of France", "language": "en"},
        {"query": "Thủ đô của Việt Nam", "language": "vi"},
        
        # Câu hỏi phức tạp
        {"query": "Impact of climate change on agriculture and adaptation solutions", "language": "en"},
        {"query": "Tác động của biến đổi khí hậu đến nông nghiệp và các giải pháp thích ứng", "language": "vi"}
    ]
    
    # Thực hiện tìm kiếm với từng câu hỏi
    results = {}
    for query_info in test_queries:
        query = query_info["query"]
        language = query_info["language"]
        
        try:
            result = test_search_with_query(agent, query, language, args.output_dir)
            results[query] = {
                "success": result.get("success", False),
                "count": len(result.get("results", [])),
                "search_time": result.get("search_time", 0),
                "language": language
            }
        except Exception as e:
            logger.error(f"Lỗi khi tìm kiếm '{query}': {str(e)}")
            results[query] = {
                "success": False,
                "error": str(e),
                "language": language
            }
    
    # Lưu tổng hợp kết quả
    summary_file = f"{args.output_dir}/search_results_summary_{int(time.time())}.json"
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    logger.info(f"Đã lưu tổng hợp kết quả vào file: {summary_file}")
    
    # In tổng hợp
    print("\n" + "=" * 80)
    print("TỔNG HỢP KẾT QUẢ TÌM KIẾM")
    print("=" * 80)
    
    success_count = sum(1 for r in results.values() if r.get("success"))
    print(f"Tổng số truy vấn: {len(results)}")
    print(f"Thành công: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
    
    # Tính thời gian trung bình
    search_times = [r.get("search_time", 0) for r in results.values() if r.get("success")]
    if search_times:
        avg_time = sum(search_times) / len(search_times)
        print(f"Thời gian tìm kiếm trung bình: {avg_time:.2f} giây")
    
    print("\nKết quả theo ngôn ngữ:")
    en_queries = [q for q, r in results.items() if r.get("language") == "en"]
    vi_queries = [q for q, r in results.items() if r.get("language") == "vi"]
    
    en_success = sum(1 for q in en_queries if results[q].get("success"))
    vi_success = sum(1 for q in vi_queries if results[q].get("success"))
    
    print(f"- Tiếng Anh: {en_success}/{len(en_queries)} thành công ({en_success/max(1,len(en_queries))*100:.1f}%)")
    print(f"- Tiếng Việt: {vi_success}/{len(vi_queries)} thành công ({vi_success/max(1,len(vi_queries))*100:.1f}%)")

if __name__ == "__main__":
    main()
