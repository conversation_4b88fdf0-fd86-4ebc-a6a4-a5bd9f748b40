"""
Test script for CaptchaSolver.

This script tests the CaptchaSolver class with various captcha types.
"""

import sys
import time
import logging
from pathlib import Path

# Thêm thư mục gốc vào Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import các lớp c<PERSON>n thiết
from src.deep_research_core.agents.captcha_solver import (
    CaptchaType, CaptchaSolver, get_captcha_solver
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_captcha_type_detection():
    """Test captcha type detection."""
    print("\n=== Testing captcha type detection ===")

    # Create a CaptchaSolver instance
    solver = CaptchaSolver(verbose=True)

    # Test HTML content with different captcha types
    test_cases = [
        {
            "html": '<div class="g-recaptcha" data-sitekey="6LdQUegUAAAAAHu3IA9TdM_WzMp5JFTQqXxKj7D0"></div>',
            "expected_type": CaptchaType.RECAPTCHA_V2
        },
        {
            "html": '<script src="https://www.google.com/recaptcha/api.js?render=6LdQUegUAAAAAHu3IA9TdM_WzMp5JFTQqXxKj7D0"></script>',
            "expected_type": CaptchaType.RECAPTCHA_V3
        },
        {
            "html": '<div class="h-captcha" data-sitekey="10000000-ffff-ffff-ffff-000000000001"></div>',
            "expected_type": CaptchaType.HCAPTCHA
        },
        {
            "html": '<form id="challenge-form" action="/cdn-cgi/challenge-platform/h/g/orchestrate/jsch/v1"></form>',
            "expected_type": CaptchaType.CLOUDFLARE
        },
        {
            "html": '<div class="ddg-challenge">Please verify you are human</div>',
            "expected_type": CaptchaType.DUCKDUCKGO
        },
        {
            "html": '<div class="cf-turnstile" data-sitekey="0x4AAAAAAAA9Wfd2eLWLVCB3"></div>',
            "expected_type": CaptchaType.TURNSTILE
        },
        {
            "html": '<div class="captcha">Please complete the captcha</div>',
            "expected_type": CaptchaType.GENERIC
        },
        {
            "html": '<div>No captcha here</div>',
            "expected_type": CaptchaType.UNKNOWN
        }
    ]

    for i, test_case in enumerate(test_cases):
        html = test_case["html"]
        expected_type = test_case["expected_type"]

        # Detect captcha type
        detected_type = solver.get_captcha_type(html)

        # Check if the detected type matches the expected type
        result = "✓" if detected_type == expected_type else "✗"
        print(f"Test case {i+1}: {result} (Detected: {detected_type.name}, Expected: {expected_type.name})")

def test_session_management():
    """Test session management."""
    print("\n=== Testing session management ===")

    # Create a CaptchaSolver instance
    solver = CaptchaSolver(verbose=True, session_ttl=5)  # Short TTL for testing

    # Test URL
    url = "https://example.com"

    # Test data
    session_data = {
        "cookies": {
            "session_id": "abc123",
            "user_id": "user123"
        },
        "headers": {
            "User-Agent": "Mozilla/5.0"
        }
    }

    # Save session
    print("Saving session...")
    save_result = solver.save_session(url, session_data)
    print(f"Save result: {save_result}")

    # Load session
    print("Loading session...")
    loaded_data = solver.load_session(url)
    print(f"Loaded data: {loaded_data}")

    # Check if loaded data matches original data
    if loaded_data and "cookies" in loaded_data and "headers" in loaded_data:
        print("Session data loaded successfully")
    else:
        print("Failed to load session data")

    # Wait for session to expire
    print("Waiting for session to expire...")
    time.sleep(6)  # Wait longer than the TTL

    # Try to load expired session
    print("Loading expired session...")
    expired_data = solver.load_session(url)
    print(f"Expired data: {expired_data}")

    # Clear expired sessions
    print("Clearing expired sessions...")
    cleared_count = solver.clear_expired_sessions()
    print(f"Cleared {cleared_count} expired sessions")

def test_playwright_captcha_solver():
    """Test PlaywrightCaptchaSolver."""
    print("\n=== Testing PlaywrightCaptchaSolver ===")

    try:
        # Create a PlaywrightCaptchaSolver instance
        solver = get_captcha_solver(
            solver_type="playwright",
            headless=False,  # Set to True for headless mode
            browser_type="chromium",
            user_agent_rotation=True,
            max_retries=1,
            verbose=True
        )

        # Test URL with captcha
        url = "https://www.google.com/recaptcha/api2/demo"

        print(f"Testing captcha solving for URL: {url}")
        print("This will open a browser window. You may need to solve the captcha manually.")
        print("Press Ctrl+C to cancel.")

        # Solve captcha
        success, html_content = solver.solve(url)

        print(f"Captcha solving result: {'Success' if success else 'Failed'}")
        if success and html_content:
            print(f"HTML content length: {len(html_content)} characters")

    except KeyboardInterrupt:
        print("Test cancelled by user")
    except Exception as e:
        print(f"Error testing PlaywrightCaptchaSolver: {str(e)}")

if __name__ == "__main__":
    # Run tests
    test_captcha_type_detection()
    test_session_management()

    # Uncomment to test PlaywrightCaptchaSolver (requires user interaction)
    # test_playwright_captcha_solver()
