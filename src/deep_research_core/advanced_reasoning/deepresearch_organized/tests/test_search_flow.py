"""
Test script for WebSearchAgentLocal.
"""

import sys
import os
import time
import json
import requests
import subprocess
import tempfile

def print_section(title):
    """Print a section title."""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, "="))
    print("=" * 80)

def test_searxng_local():
    """Test SearXNG local."""
    print_section("Kiểm tra SearXNG local")
    
    try:
        # Kiểm tra xem SearXNG local có hoạt động không
        response = requests.get("http://localhost:8080", timeout=5)
        if response.status_code == 200:
            print("SearXNG local đang hoạt động")
        else:
            print(f"SearXNG local trả về mã trạng thái: {response.status_code}")
            return False
        
        # Thử tìm kiếm với SearXNG local
        query = "Python programming"
        print(f"Tìm kiếm với SearXNG local: {query}")
        
        response = requests.get(
            "http://localhost:8080/search",
            params={
                "q": query,
                "format": "json",
                "language": "en",
                "safesearch": 0,
                "count": 5
            },
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            results = data.get("results", [])
            print(f"Tìm thấy {len(results)} kết quả")
            
            for i, result in enumerate(results[:3]):  # Chỉ hiển thị 3 kết quả đầu tiên
                print(f"\nKết quả {i+1}:")
                print(f"  Tiêu đề: {result.get('title', '')}")
                print(f"  URL: {result.get('url', '')}")
                snippet = result.get('content', '')
                if snippet:
                    print(f"  Đoạn trích: {snippet[:100]}..." if len(snippet) > 100 else f"  Đoạn trích: {snippet}")
            
            return True
        else:
            print(f"Tìm kiếm với SearXNG local thất bại: {response.status_code}")
            return False
    
    except Exception as e:
        print(f"Lỗi khi kiểm tra SearXNG local: {str(e)}")
        return False

def test_crawlee():
    """Test Crawlee."""
    print_section("Kiểm tra Crawlee")
    
    try:
        # Kiểm tra xem Node.js đã được cài đặt chưa
        try:
            result = subprocess.run(["node", "--version"], capture_output=True, text=True, check=True)
            print(f"Node.js version: {result.stdout.strip()}")
        except subprocess.CalledProcessError:
            print("Node.js chưa được cài đặt")
            return False
        
        # Kiểm tra xem Crawlee đã được cài đặt chưa
        try:
            result = subprocess.run(["npm", "list", "crawlee"], capture_output=True, text=True, check=True)
            if "crawlee@" in result.stdout:
                print("Crawlee đã được cài đặt")
            else:
                print("Crawlee chưa được cài đặt")
                return False
        except subprocess.CalledProcessError:
            print("Không thể kiểm tra Crawlee")
            return False
        
        # Tạo script Crawlee tạm thời
        with tempfile.NamedTemporaryFile(mode="w", suffix=".js", delete=False) as f:
            f.write("""
const { PlaywrightCrawler } = require('crawlee');

// Khởi tạo crawler
const crawler = new PlaywrightCrawler({
    maxRequestsPerCrawl: 1,
    
    // Hàm xử lý mỗi trang
    async requestHandler({ request, page, enqueueLinks }) {
        console.log(`Processing: ${request.url}`);
        
        // Đợi trang tải xong
        await page.waitForLoadState('networkidle');
        
        // Lấy tiêu đề
        const title = await page.title();
        
        // Lấy nội dung
        const content = await page.evaluate(() => {
            return document.body.innerText;
        });
        
        // In kết quả
        console.log(JSON.stringify({
            url: request.url,
            title: title,
            content_length: content.length
        }));
    }
});

// Thêm URL vào hàng đợi
crawler.addRequests([{ url: 'https://www.python.org' }]);

// Chạy crawler
crawler.run();
            """)
            crawlee_script = f.name
        
        try:
            # Chạy script Crawlee
            print("Chạy script Crawlee...")
            result = subprocess.run(["node", crawlee_script], capture_output=True, text=True, timeout=30)
            
            print("Kết quả từ Crawlee:")
            output_lines = result.stdout.strip().split("\n")
            for line in output_lines:
                if line.startswith("{") and line.endswith("}"):
                    try:
                        data = json.loads(line)
                        print(f"URL: {data.get('url')}")
                        print(f"Tiêu đề: {data.get('title')}")
                        print(f"Độ dài nội dung: {data.get('content_length')} ký tự")
                    except json.JSONDecodeError:
                        print(line)
                else:
                    print(line)
            
            return True
            
        finally:
            # Xóa file tạm
            try:
                os.unlink(crawlee_script)
            except Exception as e:
                print(f"Không thể xóa file tạm: {str(e)}")
    
    except Exception as e:
        print(f"Lỗi khi kiểm tra Crawlee: {str(e)}")
        return False

def test_playwright():
    """Test Playwright."""
    print_section("Kiểm tra Playwright")
    
    try:
        # Kiểm tra xem Playwright đã được cài đặt chưa
        try:
            result = subprocess.run(["python3", "-m", "playwright", "--version"], capture_output=True, text=True, check=True)
            print(f"Playwright version: {result.stdout.strip()}")
        except subprocess.CalledProcessError:
            print("Playwright chưa được cài đặt")
            return False
        
        # Tạo script Python tạm thời để kiểm tra Playwright
        with tempfile.NamedTemporaryFile(mode="w", suffix=".py", delete=False) as f:
            f.write("""
from playwright.sync_api import sync_playwright

def main():
    with sync_playwright() as p:
        browser = p.chromium.launch()
        page = browser.new_page()
        page.goto('https://www.python.org')
        title = page.title()
        content_length = len(page.content())
        browser.close()
        
        print(f"URL: https://www.python.org")
        print(f"Tiêu đề: {title}")
        print(f"Độ dài nội dung: {content_length} ký tự")

if __name__ == "__main__":
    main()
            """)
            playwright_script = f.name
        
        try:
            # Chạy script Playwright
            print("Chạy script Playwright...")
            result = subprocess.run(["python3", playwright_script], capture_output=True, text=True, timeout=30)
            
            print("Kết quả từ Playwright:")
            print(result.stdout)
            
            return True
            
        finally:
            # Xóa file tạm
            try:
                os.unlink(playwright_script)
            except Exception as e:
                print(f"Không thể xóa file tạm: {str(e)}")
    
    except Exception as e:
        print(f"Lỗi khi kiểm tra Playwright: {str(e)}")
        return False

def test_searxng_crawlee_integration():
    """Test SearXNG-Crawlee integration."""
    print_section("Kiểm tra tích hợp SearXNG-Crawlee")
    
    try:
        # Kiểm tra xem SearXNG local có hoạt động không
        if not test_searxng_local():
            print("SearXNG local không hoạt động, không thể kiểm tra tích hợp SearXNG-Crawlee")
            return False
        
        # Kiểm tra xem Crawlee có hoạt động không
        if not test_crawlee():
            print("Crawlee không hoạt động, không thể kiểm tra tích hợp SearXNG-Crawlee")
            return False
        
        # Tạo script tích hợp SearXNG-Crawlee
        with tempfile.NamedTemporaryFile(mode="w", suffix=".js", delete=False) as f:
            f.write("""
const { PlaywrightCrawler } = require('crawlee');
const fetch = require('node-fetch');

// Hàm tìm kiếm với SearXNG
async function searchWithSearXNG(query) {
    const response = await fetch(`http://localhost:8080/search?q=${encodeURIComponent(query)}&format=json&language=en&safesearch=0&count=3`);
    const data = await response.json();
    return data.results.map(result => result.url);
}

// Hàm chính
async function main() {
    // Tìm kiếm với SearXNG
    const query = "Python programming";
    console.log(`Tìm kiếm với SearXNG: ${query}`);
    
    const urls = await searchWithSearXNG(query);
    console.log(`Tìm thấy ${urls.length} URL từ SearXNG`);
    
    if (urls.length === 0) {
        console.log("Không tìm thấy URL nào từ SearXNG");
        return;
    }
    
    // Khởi tạo crawler
    const crawler = new PlaywrightCrawler({
        maxRequestsPerCrawl: urls.length,
        
        // Hàm xử lý mỗi trang
        async requestHandler({ request, page, enqueueLinks }) {
            console.log(`Processing: ${request.url}`);
            
            // Đợi trang tải xong
            await page.waitForLoadState('networkidle');
            
            // Lấy tiêu đề
            const title = await page.title();
            
            // Lấy nội dung
            const content = await page.evaluate(() => {
                return document.body.innerText;
            });
            
            // In kết quả
            console.log(JSON.stringify({
                url: request.url,
                title: title,
                content_length: content.length
            }));
        }
    });
    
    // Thêm URL vào hàng đợi
    for (const url of urls) {
        await crawler.addRequests([{ url }]);
    }
    
    // Chạy crawler
    await crawler.run();
}

// Chạy hàm chính
main().catch(console.error);
            """)
            integration_script = f.name
        
        try:
            # Cài đặt node-fetch nếu cần
            subprocess.run(["npm", "install", "node-fetch"], capture_output=True, text=True, check=True)
            
            # Chạy script tích hợp
            print("Chạy script tích hợp SearXNG-Crawlee...")
            result = subprocess.run(["node", integration_script], capture_output=True, text=True, timeout=60)
            
            print("Kết quả từ tích hợp SearXNG-Crawlee:")
            output_lines = result.stdout.strip().split("\n")
            for line in output_lines:
                if line.startswith("{") and line.endswith("}"):
                    try:
                        data = json.loads(line)
                        print(f"URL: {data.get('url')}")
                        print(f"Tiêu đề: {data.get('title')}")
                        print(f"Độ dài nội dung: {data.get('content_length')} ký tự")
                    except json.JSONDecodeError:
                        print(line)
                else:
                    print(line)
            
            return True
            
        finally:
            # Xóa file tạm
            try:
                os.unlink(integration_script)
            except Exception as e:
                print(f"Không thể xóa file tạm: {str(e)}")
    
    except Exception as e:
        print(f"Lỗi khi kiểm tra tích hợp SearXNG-Crawlee: {str(e)}")
        return False

def main():
    """Main function."""
    print_section("Kiểm tra luồng tìm kiếm")
    
    try:
        # Kiểm tra SearXNG local
        searxng_ok = test_searxng_local()
        
        # Kiểm tra Playwright
        playwright_ok = test_playwright()
        
        # Kiểm tra Crawlee
        crawlee_ok = test_crawlee()
        
        # Kiểm tra tích hợp SearXNG-Crawlee
        if searxng_ok and crawlee_ok:
            integration_ok = test_searxng_crawlee_integration()
        else:
            integration_ok = False
            print("Không thể kiểm tra tích hợp SearXNG-Crawlee vì SearXNG hoặc Crawlee không hoạt động")
        
        # Tổng kết
        print_section("Tổng kết")
        print(f"SearXNG local: {'OK' if searxng_ok else 'FAILED'}")
        print(f"Playwright: {'OK' if playwright_ok else 'FAILED'}")
        print(f"Crawlee: {'OK' if crawlee_ok else 'FAILED'}")
        print(f"Tích hợp SearXNG-Crawlee: {'OK' if integration_ok else 'FAILED'}")
        
        if searxng_ok and playwright_ok and crawlee_ok and integration_ok:
            print("\nTất cả các thành phần đều hoạt động tốt!")
            print("Luồng tìm kiếm local có thể hoạt động như mô tả trong tài liệu.")
        else:
            print("\nMột số thành phần không hoạt động!")
            print("Luồng tìm kiếm local có thể không hoạt động như mong đợi.")
    
    except Exception as e:
        import traceback
        print(f"Lỗi: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
