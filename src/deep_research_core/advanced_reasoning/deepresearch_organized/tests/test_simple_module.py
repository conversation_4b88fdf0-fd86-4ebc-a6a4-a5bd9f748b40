"""
Very simple test script for testing individual modules.
"""

import sys
import os

# Add the src directory to the path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "src")))

def test_openrouter():
    """Test the OpenRouter provider."""
    print("Testing OpenRouter provider...")
    
    try:
        from deep_research_core.models.api.openrouter import OpenRouterProvider
        
        # Initialize the provider with the API key
        api_key = "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"
        provider = OpenRouterProvider(api_key=api_key)
        
        print("OpenRouter provider initialized successfully!")
        return True
    except Exception as e:
        print(f"Error testing OpenRouter provider: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_source_attribution():
    """Test the SourceAttribution module."""
    print("Testing SourceAttribution module...")
    
    try:
        from deep_research_core.reasoning.source_attribution import SourceAttribution
        
        print("SourceAttribution imported successfully!")
        return True
    except Exception as e:
        print(f"Error importing SourceAttribution: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_self_reflection():
    """Test the SelfReflection module."""
    print("Testing SelfReflection module...")
    
    try:
        from deep_research_core.reasoning.self_reflection import SelfReflection
        
        print("SelfReflection imported successfully!")
        return True
    except Exception as e:
        print(f"Error importing SelfReflection: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_query_decomposition():
    """Test the MultiQueryDecomposition module."""
    print("Testing MultiQueryDecomposition module...")
    
    try:
        from deep_research_core.reasoning.multi_query_decomposition import MultiQueryDecomposition
        
        print("MultiQueryDecomposition imported successfully!")
        return True
    except Exception as e:
        print(f"Error importing MultiQueryDecomposition: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_source_validator():
    """Test the MultiSourceValidator module."""
    print("Testing MultiSourceValidator module...")
    
    try:
        from deep_research_core.reasoning.multi_source_validator import MultiSourceValidator
        
        print("MultiSourceValidator imported successfully!")
        return True
    except Exception as e:
        print(f"Error importing MultiSourceValidator: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_tree_of_thought():
    """Test the TreeOfThought module."""
    print("Testing TreeOfThought module...")
    
    try:
        from deep_research_core.reasoning.tot import TreeOfThought
        
        print("TreeOfThought imported successfully!")
        return True
    except Exception as e:
        print(f"Error importing TreeOfThought: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Testing modules...")
    
    # Test OpenRouter
    test_openrouter()
    
    # Test SourceAttribution
    test_source_attribution()
    
    # Test SelfReflection
    test_self_reflection()
    
    # Test MultiQueryDecomposition
    test_multi_query_decomposition()
    
    # Test MultiSourceValidator
    test_multi_source_validator()
    
    # Test TreeOfThought
    test_tree_of_thought()
    
    print("All tests completed!")
