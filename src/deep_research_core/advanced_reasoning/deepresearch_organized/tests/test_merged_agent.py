#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for WebSearchAgentMerged.
"""

import sys
import os
import importlib.util

# Add parent directory to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

try:
    # Load the module directly from file
    module_path = os.path.abspath(os.path.join(os.path.dirname(__file__),
                                              "src/deep_research_core/agents/web_search_agent_merged.py"))
    print(f"Looking for module at: {module_path}")
    if not os.path.exists(module_path):
        print(f"File not found: {module_path}")

    spec = importlib.util.spec_from_file_location(
        "web_search_agent_merged",
        module_path
    )
    if spec is None:
        raise ImportError("Could not find module web_search_agent_merged")

    web_search_agent_merged = importlib.util.module_from_spec(spec)
    sys.modules["web_search_agent_merged"] = web_search_agent_merged
    spec.loader.exec_module(web_search_agent_merged)

    # Import the class
    WebSearchAgentMerged = web_search_agent_merged.WebSearchAgentMerged

    # Create agent
    agent = WebSearchAgentMerged()

    # Print success message
    print("WebSearchAgentMerged initialized successfully!")

    # Test a simple search
    print("Testing search...")
    results = agent.search("Python programming", num_results=2)

    # Print results
    print(f"Search success: {results.get('success', False)}")
    print(f"Results count: {len(results.get('results', []))}")

    # Print first result
    if results.get('results'):
        first_result = results['results'][0]
        print(f"First result title: {first_result.get('title', 'N/A')}")
        print(f"First result URL: {first_result.get('url', 'N/A')}")

except ImportError as e:
    print(f"Import error: {str(e)}")
except Exception as e:
    print(f"Error: {str(e)}")
