"""
Check duckduckgo-search library structure.
"""

import inspect
import duckduckgo_search

# Print all modules and classes in duckduckgo_search
print("Modules and classes in duckduckgo_search:")
for name in dir(duckduckgo_search):
    if not name.startswith("__"):
        print(f"- {name}")
        obj = getattr(duckduckgo_search, name)
        if inspect.isclass(obj):
            print(f"  Class methods:")
            for method_name in dir(obj):
                if not method_name.startswith("__"):
                    print(f"  - {method_name}")
