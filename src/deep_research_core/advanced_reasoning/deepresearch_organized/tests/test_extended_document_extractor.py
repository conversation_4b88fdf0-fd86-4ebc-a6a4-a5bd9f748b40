#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for ExtendedDocumentExtractor.
"""

import os
import sys
import logging
import tempfile
import shutil
import requests
from typing import Dict, Any, List, Optional

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import necessary modules
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

try:
    # Try different import paths
    try:
        from src.deep_research_core.agents.document_extractors_extended import ExtendedDocumentExtractor
        EXTRACTOR_AVAILABLE = True
    except ImportError:
        from deepresearch.src.deep_research_core.agents.document_extractors_extended import ExtendedDocumentExtractor
        EXTRACTOR_AVAILABLE = True
except ImportError:
    logger.error("ExtendedDocumentExtractor is not available")
    EXTRACTOR_AVAILABLE = False
    # Define a placeholder class for type hints
    class ExtendedDocumentExtractor:
        def extract(self, *args, **kwargs):
            pass

def download_test_file(url: str, output_dir: str) -> Optional[str]:
    """
    Download a test file from URL.

    Args:
        url: URL of the file
        output_dir: Directory to save the file

    Returns:
        Path to the downloaded file or None if download failed
    """
    try:
        # Get file name from URL
        file_name = os.path.basename(url)
        if not file_name:
            file_name = "test_file"

        # Create output path
        output_path = os.path.join(output_dir, file_name)

        # Download file
        response = requests.get(url, stream=True, timeout=30)

        # Check status
        if response.status_code != 200:
            logger.error(f"Failed to download file from {url}: HTTP {response.status_code}")
            return None

        # Save file
        with open(output_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)

        logger.info(f"Downloaded file: {output_path}")
        return output_path

    except Exception as e:
        logger.error(f"Error downloading file from {url}: {str(e)}")
        return None

def test_extract_from_file(extractor: ExtendedDocumentExtractor, file_path: str) -> Dict[str, Any]:
    """
    Test extracting text from a file.

    Args:
        extractor: ExtendedDocumentExtractor instance
        file_path: Path to the file

    Returns:
        Dictionary with extraction results
    """
    try:
        # Open file
        with open(file_path, 'rb') as f:
            # Extract text
            result = extractor.extract(f, filename=file_path)

        # Log results
        if result.get("success"):
            text = result.get("text", "")
            logger.info(f"Successfully extracted text from {file_path}")
            logger.info(f"Text length: {len(text)} characters")
            logger.info(f"Text preview: {text[:200]}...")
        else:
            logger.warning(f"Failed to extract text from {file_path}: {result.get('error')}")

        return result

    except Exception as e:
        logger.error(f"Error extracting text from {file_path}: {str(e)}")
        return {"success": False, "error": str(e)}

def main():
    """
    Main function to test ExtendedDocumentExtractor.
    """
    if not EXTRACTOR_AVAILABLE:
        logger.error("ExtendedDocumentExtractor is not available. Exiting.")
        return False

    # Create a temporary directory for test files
    test_dir = tempfile.mkdtemp()
    logger.info(f"Created temporary directory: {test_dir}")

    try:
        # Initialize ExtendedDocumentExtractor
        extractor = ExtendedDocumentExtractor(ocr_enabled=True)

        # Test files URLs
        test_files = [
            # PDF
            "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf",
            # EPUB
            "https://www.gutenberg.org/ebooks/1342.epub.images",
            # Markdown
            "https://raw.githubusercontent.com/mdn/content/main/README.md",
            # RTF
            "https://www.irs.gov/pub/irs-pdf/fw4.rtf",
            # DOCX
            "https://file-examples.com/storage/fe5f2a0dc6578b5c39f2a8c/2017/02/file-sample_100kB.docx",
            # PPTX
            "https://file-examples.com/storage/fe5f2a0dc6578b5c39f2a8c/2017/08/file_example_PPT_250kB.ppt",
            # ODT
            "https://file-examples.com/storage/fe5f2a0dc6578b5c39f2a8c/2017/10/file-example_ODT_100kB.odt"
        ]

        # Download and test each file
        results = {}
        for url in test_files:
            logger.info(f"Testing file from URL: {url}")

            # Download file
            file_path = download_test_file(url, test_dir)
            if not file_path:
                logger.warning(f"Skipping test for {url}")
                continue

            # Extract text
            result = test_extract_from_file(extractor, file_path)

            # Store result
            file_type = os.path.splitext(file_path)[1].lower()
            results[file_type] = result.get("success", False)

        # Print summary
        logger.info("Test results summary:")
        for file_type, success in results.items():
            logger.info(f"  {file_type}: {'Success' if success else 'Failed'}")

        # Check if all tests passed
        all_passed = all(results.values())
        if all_passed:
            logger.info("All tests passed!")
        else:
            logger.warning("Some tests failed.")

        return all_passed

    except Exception as e:
        logger.error(f"Error during testing: {str(e)}")
        return False

    finally:
        # Clean up
        shutil.rmtree(test_dir)
        logger.info(f"Removed temporary directory: {test_dir}")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
