#!/usr/bin/env python3
"""
Test script to evaluate the WebSearchAgent functionality.
This script performs web searches with different methods and queries and evaluates the results.
"""

import os
import json
import time
import logging
from typing import Dict, Any, List

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import the WebSearchAgent
from src.deep_research_core.agents.web_search_agent import WebSearchAgent
from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal
from src.deep_research_core.agents.web_search_agent_enhanced import WebSearchAgentEnhanced

def evaluate_results(results: Dict[str, Any]) -> Dict[str, Any]:
    """
    Evaluate search results and return metrics.
    
    Args:
        results: Search results dictionary
        
    Returns:
        Dictionary with evaluation metrics
    """
    # Initialize metrics
    metrics = {
        "success": results.get("success", False),
        "error": results.get("error", ""),
        "result_count": 0,
        "has_content": False,
        "average_content_length": 0,
        "has_snippets": False,
        "response_time": results.get("response_time", 0),
    }
    
    # Get result list
    result_list = results.get("results", [])
    metrics["result_count"] = len(result_list)
    
    # Check for content
    content_lengths = []
    has_snippets = False
    
    for result in result_list:
        # Check if result has content
        if "content" in result and result["content"]:
            content_lengths.append(len(result["content"]))
        
        # Check if result has snippet
        if "snippet" in result and result["snippet"]:
            has_snippets = True
    
    # Calculate average content length
    if content_lengths:
        metrics["has_content"] = True
        metrics["average_content_length"] = sum(content_lengths) / len(content_lengths)
    
    metrics["has_snippets"] = has_snippets
    
    return metrics

def test_websearch_with_queries(agent, queries: List[str], method: str = None):
    """
    Test WebSearchAgent with multiple queries.
    
    Args:
        agent: WebSearchAgent instance
        queries: List of queries to test
        method: Search method to use (None for default)
    """
    results = []
    
    for query in queries:
        logger.info(f"Testing query: {query}")
        start_time = time.time()
        
        try:
            # Perform search with specified method or default
            if method:
                search_result = agent.search(query, num_results=5, method=method)
            else:
                search_result = agent.search(query, num_results=5)
                
            # Add response time
            search_result["response_time"] = time.time() - start_time
            
            # Evaluate results
            evaluation = evaluate_results(search_result)
            
            # Add to results
            results.append({
                "query": query,
                "results": search_result,
                "evaluation": evaluation
            })
            
            logger.info(f"Results: {evaluation['result_count']} items, Success: {evaluation['success']}")
            
            # Add delay between requests to avoid rate limiting
            time.sleep(2)
            
        except Exception as e:
            logger.error(f"Error searching for '{query}': {str(e)}")
            results.append({
                "query": query,
                "error": str(e),
                "evaluation": {"success": False, "error": str(e)}
            })
    
    return results

def main():
    """Main function to run the tests."""
    logger.info("Starting WebSearchAgent evaluation")
    
    # Define test queries
    basic_queries = [
        "current global climate change impacts",
        "Python programming best practices",
        "AI research trends 2025"
    ]
    
    vietnamese_queries = [
        "Tác động của biến đổi khí hậu tại Việt Nam",
        "Công nghệ trí tuệ nhân tạo ứng dụng trong y tế",
        "Phát triển bền vững ở đồng bằng sông Cửu Long"
    ]
    
    complex_queries = [
        "Compare quantum computing approaches IBM versus Google",
        "Explain effects of intermittent fasting on metabolic health",
        "Impact of social media on political polarization since 2020"
    ]
    
    # Test standard WebSearchAgent with API method
    logger.info("\n--- Testing standard WebSearchAgent with API search ---")
    agent = WebSearchAgent(search_method="api", verbose=True)
    api_results = test_websearch_with_queries(agent, basic_queries, method="api")
    
    # Test standard WebSearchAgent with crawlee method
    logger.info("\n--- Testing standard WebSearchAgent with crawlee search ---")
    crawlee_results = test_websearch_with_queries(agent, basic_queries, method="crawlee")
    
    # Test local WebSearchAgent
    logger.info("\n--- Testing WebSearchAgentLocal ---")
    local_agent = WebSearchAgentLocal(verbose=True)
    local_results = test_websearch_with_queries(local_agent, basic_queries)
    
    # Test enhanced WebSearchAgent
    logger.info("\n--- Testing WebSearchAgentEnhanced ---")
    enhanced_agent = WebSearchAgentEnhanced(verbose=True)
    enhanced_results = test_websearch_with_queries(enhanced_agent, basic_queries)
    
    # Test Vietnamese queries
    logger.info("\n--- Testing Vietnamese queries ---")
    vi_results = test_websearch_with_queries(agent, vietnamese_queries)
    
    # Test complex queries
    logger.info("\n--- Testing complex queries ---")
    complex_results = test_websearch_with_queries(agent, complex_queries)
    
    # Compile all results
    all_results = {
        "api_results": api_results,
        "crawlee_results": crawlee_results,
        "local_results": local_results,
        "enhanced_results": enhanced_results,
        "vietnamese_results": vi_results,
        "complex_results": complex_results
    }
    
    # Save results to file
    with open("web_search_evaluation_results.json", "w", encoding="utf-8") as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
    
    logger.info("Evaluation complete. Results saved to web_search_evaluation_results.json")
    
    # Print summary
    success_rates = {
        "api": sum(1 for r in api_results if r.get("evaluation", {}).get("success", False)) / len(api_results),
        "crawlee": sum(1 for r in crawlee_results if r.get("evaluation", {}).get("success", False)) / len(crawlee_results),
        "local": sum(1 for r in local_results if r.get("evaluation", {}).get("success", False)) / len(local_results),
        "enhanced": sum(1 for r in enhanced_results if r.get("evaluation", {}).get("success", False)) / len(enhanced_results),
        "vietnamese": sum(1 for r in vi_results if r.get("evaluation", {}).get("success", False)) / len(vi_results),
        "complex": sum(1 for r in complex_results if r.get("evaluation", {}).get("success", False)) / len(complex_results)
    }
    
    logger.info("\n--- Summary of Success Rates ---")
    for method, rate in success_rates.items():
        logger.info(f"{method}: {rate*100:.1f}%")

if __name__ == "__main__":
    main() 