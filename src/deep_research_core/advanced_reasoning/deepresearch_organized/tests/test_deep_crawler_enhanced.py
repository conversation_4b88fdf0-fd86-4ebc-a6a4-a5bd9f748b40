#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for enhanced deep crawler with file download functionality.
"""

import os
import sys
import time
import logging
import shutil
import requests
from typing import Dict, Any, List, Optional, Union
from urllib.parse import urlparse, urljoin

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add project root to sys.path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Try to import WebSearchAgentLocal
AGENT_AVAILABLE = False
WebSearchAgentLocal = None

try:
    from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal
    AGENT_AVAILABLE = True
except ImportError:
    try:
        from deepresearch.src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal
        AGENT_AVAILABLE = True
    except ImportError:
        logger.error("WebSearchAgentLocal is not available")

# Define a placeholder class for type hints if WebSearchAgentLocal is not available
if not AGENT_AVAILABLE:
    class WebSearchAgentLocal:
        def __init__(self, **kwargs):
            self.file_download_config = {"download_dir": "downloads"}

        def _deep_crawl(self, **kwargs):
            return {"success": False, "error": "WebSearchAgentLocal not available"}

def download_test_file(url: str, output_dir: str) -> Optional[str]:
    """
    Download a test file from URL.

    Args:
        url: URL of the file
        output_dir: Directory to save the file

    Returns:
        Path to the downloaded file or None if download failed
    """
    try:
        # Get file name from URL
        file_name = os.path.basename(url)
        if not file_name:
            file_name = "test_file"

        # Create output path
        output_path = os.path.join(output_dir, file_name)

        # Download file
        response = requests.get(url, stream=True, timeout=30)

        # Check status
        if response.status_code != 200:
            logger.error(f"Failed to download file from {url}: HTTP {response.status_code}")
            return None

        # Save file
        with open(output_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)

        logger.info(f"Downloaded file: {output_path}")
        return output_path

    except Exception as e:
        logger.error(f"Error downloading file from {url}: {str(e)}")
        return None

def test_deep_crawler(agent: WebSearchAgentLocal, url: str, max_depth: int = 1, max_pages: int = 3) -> Dict[str, Any]:
    """
    Test deep crawler functionality.

    Args:
        agent: WebSearchAgentLocal instance
        url: URL to crawl
        max_depth: Maximum depth to crawl
        max_pages: Maximum pages to crawl

    Returns:
        Dictionary with test results
    """
    try:
        logger.info(f"Testing deep crawler with URL: {url}")

        # Perform deep crawl
        result = agent._deep_crawl(
            url=url,
            max_depth=max_depth,
            max_pages=max_pages,
            download_files=True,
            download_dir=agent.file_download_config.get("download_dir", "downloads")
        )

        # Check result
        if result.get("success"):
            logger.info(f"Deep crawl successful for {url}")

            # Log crawled pages
            pages = result.get("pages", [])
            logger.info(f"Crawled {len(pages)} pages")

            # Log downloaded files
            downloaded_files = result.get("downloaded_files", [])
            logger.info(f"Downloaded {len(downloaded_files)} files")

            for file_info in downloaded_files:
                file_path = file_info.get("file_path", "")
                file_name = file_info.get("file_name", "")
                content_type = file_info.get("content_type", "")
                size = file_info.get("size", 0)

                logger.info(f"File: {file_name}, Type: {content_type}, Size: {size} bytes")

                # Check if file exists
                if file_path and os.path.exists(file_path):
                    logger.info(f"File exists at {file_path}")
                else:
                    logger.warning(f"File does not exist at {file_path}")

            return {
                "success": True,
                "url": url,
                "pages_count": len(pages),
                "files_count": len(downloaded_files),
                "files": downloaded_files
            }
        else:
            logger.error(f"Deep crawl failed for {url}: {result.get('error', 'Unknown error')}")
            return {
                "success": False,
                "url": url,
                "error": result.get("error", "Unknown error")
            }

    except Exception as e:
        logger.error(f"Error testing deep crawler: {str(e)}")
        return {
            "success": False,
            "url": url,
            "error": str(e)
        }

def main():
    """
    Main function to test enhanced deep crawler.
    """
    if not AGENT_AVAILABLE:
        logger.error("WebSearchAgentLocal is not available. Exiting.")
        return False

    # Create a temporary directory for downloaded files
    download_dir = "test_downloads"
    if not os.path.exists(download_dir):
        os.makedirs(download_dir)

    try:
        # Initialize WebSearchAgentLocal with file download enabled
        agent = WebSearchAgentLocal(
            enable_file_download=True,
            file_download_config={
                "download_dir": download_dir,
                "max_file_size": 10 * 1024 * 1024,  # 10MB
                "supported_extensions": [
                    # Tài liệu văn phòng
                    '.pdf', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx',
                    # Tài liệu văn bản
                    '.txt', '.csv', '.json', '.xml', '.html', '.htm', '.md', '.markdown',
                    # Tài liệu OpenDocument
                    '.odt', '.ods', '.odp',
                    # Sách điện tử
                    '.epub', '.mobi',
                    # Định dạng khác
                    '.rtf', '.tex'
                ]
            }
        )

        # Test URLs with files
        test_urls = [
            # W3C page with PDF files
            "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/",
            # GitHub page with Markdown files
            "https://github.com/microsoft/vscode/tree/main/docs",
            # Page with various document formats
            "https://file-examples.com/",
        ]

        # Test each URL
        results = []
        for url in test_urls:
            result = test_deep_crawler(agent, url)
            results.append(result)

        # Print summary
        logger.info("Test results summary:")
        for result in results:
            url = result.get("url", "")
            if result.get("success"):
                pages_count = result.get("pages_count", 0)
                files_count = result.get("files_count", 0)
                logger.info(f"URL: {url} - Success - Pages: {pages_count}, Files: {files_count}")
            else:
                error = result.get("error", "Unknown error")
                logger.info(f"URL: {url} - Failed - Error: {error}")

        # Check if all tests passed
        all_passed = all(result.get("success", False) for result in results)
        if all_passed:
            logger.info("All tests passed!")
        else:
            logger.warning("Some tests failed.")

        return all_passed

    except Exception as e:
        logger.error(f"Error during testing: {str(e)}")
        return False

    finally:
        # Clean up
        if os.path.exists(download_dir):
            shutil.rmtree(download_dir)
        logger.info(f"Removed temporary directory: {download_dir}")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
