#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for _deep_crawl_improved method in WebSearchAgentLocal.
"""

import sys
import os
import logging
from typing import Dict, List, Any, Optional

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import WebSearchAgentLocal
try:
    from deepresearch.web_search_agent_local import WebSearchAgentLocal
except ImportError:
    try:
        from web_search_agent_local import WebSearchAgentLocal
    except ImportError:
        try:
            from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal
        except ImportError:
            logger.error("Could not import WebSearchAgentLocal")
            sys.exit(1)

def test_deep_crawl_improved():
    """Test _deep_crawl_improved method."""
    print("=" * 80)
    print("Testing _deep_crawl_improved method")
    print("=" * 80)

    # Initialize WebSearchAgentLocal
    agent = WebSearchAgentLocal(verbose=True)

    # Test URLs
    urls = [
        "https://www.python.org/",
        "https://thuvienphapluat.vn/van-ban/Thuong-mai/Nghi-dinh-98-2020-ND-CP-xu-phat-vi-pham-hanh-chinh-hoat-dong-thuong-mai-bao-ve-nguoi-tieu-dung-450755.pdf",
        "https://thuvienphapluat.vn/page/tim-van-ban.aspx",
    ]

    for url in urls:
        print(f"\nTesting URL: {url}")
        
        try:
            # Call _deep_crawl_improved
            result = agent._deep_crawl_improved(
                url=url,
                max_depth=1,
                max_pages=3,
                timeout=30,
                include_html=False,
                extract_files=True,
                handle_javascript=True if "aspx" in url else False,
                use_async=True
            )

            # Print results
            print(f"Success: {result.get('success', False)}")
            if result.get('success', False):
                print(f"Title: {result.get('title', '')}")
                text = result.get('text', '')
                print(f"Content: {text[:200]}..." if len(text) > 200 else f"Content: {text}")
                
                # Check metadata
                metadata = result.get('metadata', {})
                if metadata:
                    print(f"Metadata: {metadata}")
                
                # Check links
                links = result.get('links', [])
                if links:
                    print(f"Number of links: {len(links)}")
                    if len(links) > 0:
                        print(f"First link: {links[0]}")
                
                # Check crawl_stats
                crawl_stats = result.get('crawl_stats', {})
                if crawl_stats:
                    print(f"Crawl stats: {crawl_stats}")
                    
                # Check results
                results = result.get('results', [])
                if results:
                    print(f"Number of results: {len(results)}")
                    if len(results) > 0:
                        print(f"First result URL: {results[0].get('url', '')}")
        except Exception as e:
            print(f"Error: {str(e)}")

if __name__ == "__main__":
    test_deep_crawl_improved()
