#!/usr/bin/env python3
"""
Test for WebSearchAgent Crawlee method
"""

from src.deep_research_core.agents.web_search_agent import WebSearchAgent
import time
import traceback

def print_section(title):
    """Print a section title."""
    print("\n" + "=" * 80)
    print(title)
    print("=" * 80)

def print_result(result, max_results=3):
    """Print search result in a readable format."""
    if not result.get("success", False):
        print(f"Search failed: {result.get('error', 'Unknown error')}")
        if "detailed_errors" in result:
            print("Detailed errors:")
            for engine, error in result.get("detailed_errors", {}).items():
                print(f"  {engine}: {error}")
        return False
    
    results = result.get("results", [])
    print(f"Found {len(results)} results")
    
    if len(results) == 0:
        return False
    
    for i, item in enumerate(results[:max_results]):
        print(f"\nResult {i+1}:")
        print(f"Title: {item.get('title', '')}")
        print(f"URL: {item.get('url', '')}")
        print(f"Source: {item.get('source', '')}")
        snippet = item.get('snippet', '')
        if snippet and len(snippet) > 100:
            snippet = snippet[:100] + "..."
        print(f"Snippet: {snippet}")
    
    return True

def test_search_crawlee():
    """Test the search_crawlee method directly."""
    print_section("Testing search_crawlee method")
    
    # Initialize WebSearchAgent
    agent = WebSearchAgent(
        search_method="crawlee",  # Force Crawlee
        verbose=True
    )
    
    # Simple query first
    print("\n--- Testing a simple query ---")
    query = "Python programming tutorial"
    
    try:
        # Check if the method exists
        if hasattr(agent, "search_crawlee"):
            print("WebSearchAgent has search_crawlee method")
            
            # Try to call the method
            result = agent.search(query, num_results=3)
            print_result(result)
        else:
            print("WebSearchAgent does NOT have search_crawlee method")
            # Check if there's any method with "crawlee" in the name
            crawlee_methods = [method for method in dir(agent) if "crawlee" in method.lower()]
            print(f"Methods containing 'crawlee': {crawlee_methods}")
    except Exception as e:
        print(f"Error testing search_crawlee: {str(e)}")
        traceback.print_exc()
    
    # Test a complex query
    print("\n--- Testing a complex query ---")
    complex_query = "Compare the architectural differences between React, Angular, and Vue for building enterprise applications"
    
    try:
        result = agent.search(complex_query, num_results=3)
        print_result(result)
    except Exception as e:
        print(f"Error testing complex query: {str(e)}")
        traceback.print_exc()
    
    # Test a Vietnamese query
    print("\n--- Testing a Vietnamese query ---")
    vietnamese_query = "Cách làm bánh mì Việt Nam truyền thống"
    
    try:
        result = agent.search(vietnamese_query, num_results=3)
        print_result(result)
    except Exception as e:
        print(f"Error testing Vietnamese query: {str(e)}")
        traceback.print_exc()

def test_evaluation_algorithm():
    """Test the result evaluation algorithm."""
    print_section("Testing result evaluation algorithm")
    
    # Initialize WebSearchAgent
    agent = WebSearchAgent(verbose=True)
    
    # Create some test results
    good_results = [
        {"title": "Python Tutorial", "snippet": "Learn Python programming with this comprehensive tutorial", "url": "https://example.com/python"},
        {"title": "Python for Beginners", "snippet": "Start learning Python programming language today", "url": "https://example.com/python-beginners"}
    ]
    
    bad_results = [
        {"title": "Unrelated Topic", "snippet": "Something completely different", "url": "https://example.com/unrelated"},
        {"title": "Another Topic", "snippet": "More unrelated content", "url": "https://example.com/another"}
    ]
    
    mixed_results = good_results + bad_results
    
    # Test with different queries and results
    queries = [
        "Python tutorial",
        "Machine learning in healthcare",
        "Completely random query with no matches"
    ]
    
    all_results = [good_results, bad_results, mixed_results]
    
    for query in queries:
        print(f"\n--- Query: {query} ---")
        for results in all_results:
            score = agent.evaluate_results_quality(results, query)
            print(f"Score: {score:.2f} for {len(results)} results")
            print(f"First result: {results[0]['title']}")

def main():
    """Run all tests."""
    test_search_crawlee()
    test_evaluation_algorithm()

if __name__ == "__main__":
    main()
