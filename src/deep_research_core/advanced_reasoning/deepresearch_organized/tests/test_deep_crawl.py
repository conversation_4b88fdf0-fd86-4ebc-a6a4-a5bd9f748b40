#!/usr/bin/env python3
"""
Script kiểm tra tính năng deep crawl tự động.
"""

import os
import sys
import json
from pprint import pprint

# Thêm thư mục gốc vào sys.path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import WebSearchAgentLocal
from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal
from src.deep_research_core.agents.adaptive_crawler import AdaptiveCrawler

def test_with_complex_question():
    """Kiểm tra với câu hỏi phức tạp để kích hoạt deep crawl."""
    print("\n=== Kiểm tra với câu hỏi phức tạp ===")
    
    # Tạo agent với AdaptiveCrawler
    agent = WebSearchAgentLocal()
    
    # Gán AdaptiveCrawler cho agent
    if not hasattr(agent, 'adaptive_crawler'):
        agent.adaptive_crawler = AdaptiveCrawler()
    
    # Câu hỏi phức tạp
    complex_query = "<PERSON>ân tích chi tiết tác động của biến đổi khí hậu đến nông nghiệp ở Đồng bằng sông Cửu Long và đề xuất các giải pháp thích ứng bền vững"
    
    # Tìm kiếm với câu hỏi phức tạp
    print(f"Câu hỏi: {complex_query}")
    results = agent.search(
        query=complex_query,
        evaluate_question=True,
        evaluate_answer=True,
        auto_deep_crawl=True,
        get_content=True,
        num_results=3  # Giới hạn số kết quả để tăng khả năng cần deep crawl
    )
    
    # In kết quả đánh giá câu hỏi
    if "question_evaluation" in results:
        qe = results["question_evaluation"]
        print(f"Độ phức tạp câu hỏi: {qe['complexity_level']} (score: {qe['complexity_score']:.2f})")
        print(f"Chiến lược đề xuất: {json.dumps(qe['recommended_strategy'], indent=2)}")
    
    # In kết quả đánh giá câu trả lời
    if "answer_evaluation" in results:
        ae = results["answer_evaluation"]
        print(f"Điểm chất lượng câu trả lời: {ae['overall_score']:.2f}")
        print(f"Cần tìm kiếm thêm: {ae['need_more_search']}")
    
    # In thông tin về deep crawl
    if "deep_crawled" in results and results["deep_crawled"]:
        print(f"Đã thực hiện deep crawl: {results['deep_crawled']}")
        print(f"Deep crawl stats: {json.dumps(results.get('deep_crawl_stats', {}), indent=2)}")
    else:
        print("Không thực hiện deep crawl")
    
    # In số lượng kết quả
    print(f"Số lượng kết quả: {len(results.get('results', []))}")
    
    return results

def test_with_poor_answer():
    """Kiểm tra với câu trả lời kém để kích hoạt deep crawl."""
    print("\n=== Kiểm tra với câu trả lời kém ===")
    
    # Tạo agent với AdaptiveCrawler
    agent = WebSearchAgentLocal()
    
    # Gán AdaptiveCrawler cho agent
    if not hasattr(agent, 'adaptive_crawler'):
        agent.adaptive_crawler = AdaptiveCrawler()
    
    # Gán một câu trả lời kém để kích hoạt deep crawl
    def mock_evaluate_answer(self, question, answer, documents):
        return {
            "overall_score": 3.5,  # Điểm thấp
            "quality_level": "poor",
            "need_more_search": True,  # Cần tìm kiếm thêm
            "criteria_scores": {
                "relevance": 4.0,
                "completeness": 3.0,
                "accuracy": 3.5
            }
        }
    
    # Lưu phương thức gốc
    original_evaluate = agent.answer_quality_evaluator.evaluate_answer
    
    # Thay thế bằng phương thức giả
    agent.answer_quality_evaluator.evaluate_answer = mock_evaluate_answer.__get__(agent.answer_quality_evaluator)
    
    # Câu hỏi
    query = "Các giải pháp thích ứng với biến đổi khí hậu trong nông nghiệp"
    
    # Tìm kiếm
    print(f"Câu hỏi: {query}")
    results = agent.search(
        query=query,
        evaluate_question=True,
        evaluate_answer=True,
        auto_deep_crawl=True,
        get_content=True
    )
    
    # Khôi phục phương thức gốc
    agent.answer_quality_evaluator.evaluate_answer = original_evaluate
    
    # In kết quả đánh giá câu hỏi
    if "question_evaluation" in results:
        qe = results["question_evaluation"]
        print(f"Độ phức tạp câu hỏi: {qe['complexity_level']} (score: {qe['complexity_score']:.2f})")
    
    # In kết quả đánh giá câu trả lời
    if "answer_evaluation" in results:
        ae = results["answer_evaluation"]
        print(f"Điểm chất lượng câu trả lời: {ae['overall_score']:.2f}")
        print(f"Cần tìm kiếm thêm: {ae['need_more_search']}")
    
    # In thông tin về deep crawl
    if "deep_crawled" in results and results["deep_crawled"]:
        print(f"Đã thực hiện deep crawl: {results['deep_crawled']}")
        print(f"Deep crawl stats: {json.dumps(results.get('deep_crawl_stats', {}), indent=2)}")
    else:
        print("Không thực hiện deep crawl")
    
    return results

def test_with_simple_question():
    """Kiểm tra với câu hỏi đơn giản."""
    print("\n=== Kiểm tra với câu hỏi đơn giản ===")
    
    # Tạo agent
    agent = WebSearchAgentLocal()
    
    # Câu hỏi đơn giản
    simple_query = "Thời tiết Hà Nội hôm nay"
    
    # Tìm kiếm với câu hỏi đơn giản
    print(f"Câu hỏi: {simple_query}")
    results = agent.search(
        query=simple_query,
        evaluate_question=True,
        evaluate_answer=True,
        auto_deep_crawl=True,
        get_content=True
    )
    
    # In kết quả đánh giá câu hỏi
    if "question_evaluation" in results:
        qe = results["question_evaluation"]
        print(f"Độ phức tạp câu hỏi: {qe['complexity_level']} (score: {qe['complexity_score']:.2f})")
        print(f"Chiến lược đề xuất: {json.dumps(qe['recommended_strategy'], indent=2)}")
    
    # In kết quả đánh giá câu trả lời
    if "answer_evaluation" in results:
        ae = results["answer_evaluation"]
        print(f"Điểm chất lượng câu trả lời: {ae['overall_score']:.2f}")
        print(f"Cần tìm kiếm thêm: {ae['need_more_search']}")
    
    # In thông tin về deep crawl
    if "deep_crawled" in results and results["deep_crawled"]:
        print(f"Đã thực hiện deep crawl: {results['deep_crawled']}")
    else:
        print("Không thực hiện deep crawl")
    
    return results

if __name__ == "__main__":
    # Kiểm tra với câu hỏi đơn giản
    test_with_simple_question()
    
    # Kiểm tra với câu hỏi phức tạp
    test_with_complex_question()
    
    # Kiểm tra với câu trả lời kém
    test_with_poor_answer()
