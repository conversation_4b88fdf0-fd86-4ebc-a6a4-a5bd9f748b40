import sys
sys.path.append('/home/<USER>/Desktop/automation-tool/deep_research_core')

# Import search methods directly without going through __init__.py
sys.modules['src.deep_research_core.agents'] = type('', (), {})()
from src.deep_research_core.agents.web_search_methods import (
    search_google, search_bing, search_serper, search_serpapi,
    search_brave, search_yandex, search_qwant
)

# Test disabled search methods
print("Testing disabled search methods...")

print("Testing search_google...")
result = search_google("test query", "fake_api_key", "fake_cse_id", num_results=2)
print(f"search_google result: {result}")

print("\nTesting search_bing...")
result = search_bing("test query", "fake_api_key", num_results=2)
print(f"search_bing result: {result}")

print("\nTesting search_serper...")
result = search_serper("test query", "fake_api_key", num_results=2)
print(f"search_serper result: {result}")

print("\nTesting search_serpapi...")
result = search_serpapi("test query", "fake_api_key", num_results=2)
print(f"search_serpapi result: {result}")

print("\nTesting search_brave...")
result = search_brave("test query", "fake_api_key", num_results=2)
print(f"search_brave result: {result}")

print("\nTesting search_yandex...")
result = search_yandex("test query", "fake_api_key", num_results=2)
print(f"search_yandex result: {result}")

# Test search_qwant
print("\nTesting search_qwant...")
try:
    result = search_qwant("test query", num_results=2)
    print(f"search_qwant result: {result}")
except Exception as e:
    print(f"Error with search_qwant: {str(e)}")
