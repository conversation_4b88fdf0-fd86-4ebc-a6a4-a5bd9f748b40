#!/usr/bin/env python3
"""
Test script đơn giản để kiểm tra tính năng tối ưu hóa truy vấn.
"""

import sys
import os
import json

# Thêm thư mục gốc vào sys.path để import các module
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), 'src')))

try:
    from deep_research_core.agents.query_optimizer import QueryOptimizer
except ImportError as e:
    print(f"Lỗi import: {e}")
    print("Hãy chắc chắn rằng bạn đang chạy script từ thư mục gốc của dự án.")
    sys.exit(1)

def test_query_optimizer():
    """
    Kiểm tra tính năng tối ưu hóa truy vấn.
    """
    print("\n=== Kiểm tra QueryOptimizer ===\n")

    # Danh sách các truy vấn để kiểm tra
    test_queries = [
        "Những xu hướng AI mới nhất trong năm 2025?",
        "Python programming for beginners",
        "Cách học tiếng Anh hiệu quả",
        "Tác động của biến đổi khí hậu đến nông nghiệp Việt Nam",
        "Best machine learning frameworks 2024"
    ]

    # Khởi tạo QueryOptimizer
    optimizer = QueryOptimizer()

    # Kiểm tra từng truy vấn
    for query in test_queries:
        print(f"\nTruy vấn gốc: {query}")

        # Phát hiện ngôn ngữ
        language = "en"
        if any(c in query for c in "áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđ"):
            language = "vi"

        print(f"Ngôn ngữ: {language}")

        # Tối ưu hóa truy vấn
        try:
            result = optimizer.optimize(query, language)

            # In kết quả
            print(f"Từ khóa: {', '.join(result.get('keywords', []))}")
            print(f"Lĩnh vực: {', '.join(result.get('domains', []))}")

            if 'related_concepts' in result:
                print(f"Khái niệm liên quan: {', '.join(result.get('related_concepts', []))}")

            print("Biến thể truy vấn:")
            for i, variant in enumerate(result.get("variants", []), 1):
                print(f"  {i}. {variant}")

            print(f"Thời gian xử lý: {result.get('processing_time', 0):.4f} giây")
        except Exception as e:
            print(f"Lỗi khi tối ưu hóa truy vấn: {e}")



def main():
    """
    Hàm chính.
    """
    # Kiểm tra QueryOptimizer
    test_query_optimizer()

    # Phần kiểm tra WebSearchAgentLocal đã được loại bỏ

if __name__ == "__main__":
    main()
