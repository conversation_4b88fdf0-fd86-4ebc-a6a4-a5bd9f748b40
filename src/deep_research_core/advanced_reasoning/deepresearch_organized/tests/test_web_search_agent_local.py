"""
Test for WebSearchAgentLocal.
"""

import sys
import os
import time

# Add the src directory to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), ".")))

try:
    from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal
except ImportError:
    # Thử import trự<PERSON> tiếp từ thư mục hiện tại
    try:
        from web_search_agent_local import WebSearchAgentLocal
    except ImportError:
        print(
            "Không thể import WebSearchAgentLocal. Đ<PERSON><PERSON> bảo bạn đang chạy từ thư mục gốc."
        )
        sys.exit(1)

# Thử import các module tích hợp
try:
    from src.deep_research_core.agents.vietnamese_search_integration import (
        integrate_vietnamese_search,
    )
except ImportError:
    try:
        from vietnamese_search_integration import integrate_vietnamese_search
    except ImportError:
        integrate_vietnamese_search = None

try:
    from src.deep_research_core.agents.adaptive_crawler_integration import (
        integrate_adaptive_crawler,
    )
except ImportError:
    try:
        from adaptive_crawler_integration import integrate_adaptive_crawler
    except ImportError:
        integrate_adaptive_crawler = None

try:
    from src.deep_research_core.agents.deep_research_integration import (
        integrate_deep_research,
    )
except ImportError:
    try:
        from deep_research_integration import integrate_deep_research
    except ImportError:
        integrate_deep_research = None

try:
    from src.deep_research_core.agents.deep_crawl_improvements import (
        integrate_deep_crawl_improvements,
    )
except ImportError:
    try:
        from deep_crawl_improvements import integrate_deep_crawl_improvements
    except ImportError:
        integrate_deep_crawl_improvements = None


def print_section(title):
    """Print a section title."""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, "="))
    print("=" * 80)


def print_result(result):
    """Print search result."""
    print(f"Tìm kiếm thành công: {result.get('success', False)}")
    print(f"Phương thức tìm kiếm: {result.get('search_method', 'unknown')}")
    print(f"Engine: {result.get('engine', 'unknown')}")

    if result.get("success"):
        results = result.get("results", [])
        print(f"Tìm thấy {len(results)} kết quả")

        for i, item in enumerate(results[:3]):  # Chỉ hiển thị 3 kết quả đầu tiên
            print(f"\nKết quả {i+1}:")
            print(f"  Tiêu đề: {item.get('title', '')}")
            print(f"  URL: {item.get('url', '')}")
            snippet = item.get("snippet", "")
            if snippet:
                print(
                    f"  Đoạn trích: {snippet[:100]}..."
                    if len(snippet) > 100
                    else f"  Đoạn trích: {snippet}"
                )

            content = item.get("content", "")
            if content:
                print(
                    f"  Nội dung: {content[:100]}..."
                    if len(content) > 100
                    else f"  Nội dung: {content}"
                )
    else:
        print(f"Lỗi: {result.get('error', 'Unknown error')}")


def test_searxng_search():
    """Test SearXNG search."""
    print_section("Tìm kiếm với SearXNG")

    # Khởi tạo WebSearchAgentLocal
    agent = WebSearchAgentLocal(
        search_method="searxng",
        api_search_config={
            "engine": "searx",
            "searx_url": "http://localhost:8080",  # Thay đổi URL nếu cần
            "language": "auto",
        },
        verbose=True,
    )

    # Thực hiện tìm kiếm
    query = "Python programming"
    print(f"Truy vấn: {query}")

    try:
        result = agent.search(query, num_results=5)
        print_result(result)
    except TypeError as e:
        if "got multiple values for keyword argument 'engines'" in str(e):
            print(
                "Lỗi: Trùng lặp tham số 'engines'. Đang thử lại với phương pháp khác..."
            )
            # Sử dụng phương thức tìm kiếm trực tiếp thay vì search_specialized_local
            result = agent.search(query, num_results=5, use_specialized_search=False)
            print_result(result)
        else:
            print(f"Lỗi: {str(e)}")

    # Thử tìm kiếm tiếng Việt
    query_vi = "Lập trình Python"
    print(f"\nTruy vấn tiếng Việt: {query_vi}")

    try:
        result_vi = agent.search(query_vi, num_results=5, language="vi")
        print_result(result_vi)
    except TypeError as e:
        if "got multiple values for keyword argument 'engines'" in str(e):
            print(
                "Lỗi: Trùng lặp tham số 'engines'. Đang thử lại với phương pháp khác..."
            )
            # Sử dụng phương thức tìm kiếm trực tiếp thay vì search_specialized_local
            result_vi = agent.search(
                query_vi, num_results=5, language="vi", use_specialized_search=False
            )
            print_result(result_vi)
        else:
            print(f"Lỗi: {str(e)}")


def test_crawlee_search():
    """Test Crawlee search."""
    print_section("Tìm kiếm với Crawlee")

    # Khởi tạo WebSearchAgentLocal
    agent = WebSearchAgentLocal(
        search_method="crawlee",
        crawlee_search_config={
            "max_depth": 2,
            "max_pages_per_url": 3,
            "max_urls": 5,
            "timeout": 30,
        },
        verbose=True,
    )

    # Thực hiện tìm kiếm
    query = "Machine learning tutorial"
    print(f"Truy vấn: {query}")

    result = agent.search(query, num_results=5)
    print_result(result)


def test_auto_search():
    """Test auto search method."""
    print_section("Tìm kiếm với phương thức tự động")

    # Khởi tạo WebSearchAgentLocal
    agent = WebSearchAgentLocal(
        search_method="auto",
        api_search_config={
            "engine": "searx",
            "searx_url": "http://localhost:8080",  # Thay đổi URL nếu cần
            "language": "auto",
        },
        crawlee_search_config={
            "max_depth": 2,
            "max_pages_per_url": 3,
            "max_urls": 5,
            "timeout": 30,
        },
        verbose=True,
    )

    # Thực hiện tìm kiếm đơn giản (sẽ sử dụng SearXNG)
    query_simple = "Capital of France"
    print(f"Truy vấn đơn giản: {query_simple}")

    result_simple = agent.search(query_simple, num_results=3)
    print_result(result_simple)

    # Thực hiện tìm kiếm phức tạp (có thể sẽ sử dụng Crawlee)
    query_complex = "Compare React and Vue.js performance and state management"
    print(f"\nTruy vấn phức tạp: {query_complex}")

    result_complex = agent.search(query_complex, num_results=3, get_content=True)
    print_result(result_complex)


def test_content_extraction():
    """Test content extraction."""
    print_section("Trích xuất nội dung")

    # Khởi tạo WebSearchAgentLocal
    agent = WebSearchAgentLocal(verbose=True)

    # URL để trích xuất nội dung
    url = "https://www.python.org/"
    print(f"Trích xuất nội dung từ: {url}")

    content = agent.extract_content(url)

    if content.get("success"):
        print(f"Tiêu đề: {content.get('title', '')}")
        text = content.get("text", "")
        print(f"Nội dung: {text[:200]}..." if len(text) > 200 else f"Nội dung: {text}")
    else:
        print(f"Lỗi: {content.get('error', 'Unknown error')}")


def test_cache():
    """Test cache functionality."""
    print_section("Kiểm tra cache")

    # Khởi tạo WebSearchAgentLocal
    agent = WebSearchAgentLocal(
        search_method="searxng", cache_ttl=60, verbose=True  # 1 phút
    )

    # Thực hiện tìm kiếm lần đầu
    query = "Python programming"
    print(f"Truy vấn lần 1: {query}")

    start_time = time.time()
    result1 = agent.search(query, num_results=3)
    end_time = time.time()

    print(f"Thời gian tìm kiếm lần 1: {end_time - start_time:.2f} giây")
    print_result(result1)

    # Thực hiện tìm kiếm lần thứ hai (sẽ sử dụng cache)
    print(f"\nTruy vấn lần 2 (sử dụng cache): {query}")

    start_time = time.time()
    result2 = agent.search(query, num_results=3)
    end_time = time.time()

    print(f"Thời gian tìm kiếm lần 2: {end_time - start_time:.2f} giây")
    print_result(result2)

    # Thực hiện tìm kiếm lần thứ ba với force_refresh=True
    print(f"\nTruy vấn lần 3 (bỏ qua cache): {query}")

    start_time = time.time()
    result3 = agent.search(query, num_results=3, force_refresh=True)
    end_time = time.time()

    print(f"Thời gian tìm kiếm lần 3: {end_time - start_time:.2f} giây")
    print_result(result3)


def test_question_complexity():
    """Test đánh giá độ phức tạp câu hỏi."""
    print_section("Đánh giá độ phức tạp câu hỏi")

    # Khởi tạo WebSearchAgentLocal với QuestionComplexityEvaluator
    agent = WebSearchAgentLocal(
        verbose=True,
        question_evaluator_config={
            "complexity_threshold_high": 0.7,
            "complexity_threshold_medium": 0.4,
            "use_domain_knowledge": True,
        },
    )

    # Câu hỏi đơn giản
    simple_query = "What is Python?"
    print(f"Câu hỏi đơn giản: {simple_query}")

    simple_result = agent.search(simple_query, evaluate_question=True)

    if "question_evaluation" in simple_result:
        print(
            f"Độ phức tạp: {simple_result['question_evaluation'].get('complexity_level', 'unknown')}"
        )
        print(
            f"Điểm phức tạp: {simple_result['question_evaluation'].get('complexity_score', 0)}"
        )

        # Hiển thị chiến lược đề xuất
        strategy = simple_result["question_evaluation"].get("recommended_strategy", {})
        print(f"Chiến lược đề xuất: {strategy.get('search_method', 'auto')}")
        print(f"Số kết quả đề xuất: {strategy.get('num_results', 5)}")
        print(f"Trích xuất nội dung: {strategy.get('get_content', False)}")
        print(f"Tìm kiếm sâu: {strategy.get('deep_crawl', False)}")
    else:
        print("Không có đánh giá độ phức tạp câu hỏi")

    # Câu hỏi phức tạp
    complex_query = "Explain the differences between Python's asyncio, threading, and multiprocessing modules, including their performance characteristics, use cases, and limitations in various scenarios."
    print(f"\nCâu hỏi phức tạp: {complex_query}")

    complex_result = agent.search(complex_query, evaluate_question=True)

    if "question_evaluation" in complex_result:
        print(
            f"Độ phức tạp: {complex_result['question_evaluation'].get('complexity_level', 'unknown')}"
        )
        print(
            f"Điểm phức tạp: {complex_result['question_evaluation'].get('complexity_score', 0)}"
        )

        # Hiển thị chiến lược đề xuất
        strategy = complex_result["question_evaluation"].get("recommended_strategy", {})
        print(f"Chiến lược đề xuất: {strategy.get('search_method', 'auto')}")
        print(f"Số kết quả đề xuất: {strategy.get('num_results', 5)}")
        print(f"Trích xuất nội dung: {strategy.get('get_content', False)}")
        print(f"Tìm kiếm sâu: {strategy.get('deep_crawl', False)}")
    else:
        print("Không có đánh giá độ phức tạp câu hỏi")


def test_answer_quality():
    """Test đánh giá chất lượng câu trả lời."""
    print_section("Đánh giá chất lượng câu trả lời")

    # Khởi tạo WebSearchAgentLocal với AnswerQualityEvaluator
    agent = WebSearchAgentLocal(
        verbose=True,
        answer_evaluator_config={
            "quality_threshold_high": 0.7,
            "quality_threshold_medium": 0.4,
            "use_model_evaluation": False,
        },
    )

    # Câu hỏi và tìm kiếm
    query = "What is Python?"
    print(f"Câu hỏi: {query}")

    result = agent.search(query, num_results=3, get_content=True, evaluate_answer=True)

    if "answer_evaluation" in result:
        print(
            f"Chất lượng: {result['answer_evaluation'].get('quality_level', 'unknown')}"
        )
        print(f"Điểm chất lượng: {result['answer_evaluation'].get('overall_score', 0)}")
        print(
            f"Cần tìm kiếm thêm: {result['answer_evaluation'].get('need_more_search', False)}"
        )

        # Hiển thị các chỉ số đánh giá
        metrics = result["answer_evaluation"].get("metrics", {})
        for metric_name, metric_data in metrics.items():
            print(f"\nChỉ số {metric_name}:")
            print(f"  Điểm: {metric_data.get('score', 0)}")
            print(f"  Giải thích: {metric_data.get('explanation', '')}")
    else:
        print("Không có đánh giá chất lượng câu trả lời")


def test_deep_crawl():
    """Test tìm kiếm sâu."""
    print_section("Tìm kiếm sâu")

    # Khởi tạo WebSearchAgentLocal
    agent = WebSearchAgentLocal(verbose=True)

    # Câu hỏi và tìm kiếm
    query = "What is Python?"
    print(f"Câu hỏi: {query}")

    result = agent.search(query, num_results=3, deep_crawl=True)
    print_result(result)


def test_captcha_handling():
    """Test xử lý CAPTCHA."""
    print_section("Xử lý CAPTCHA")

    # Khởi tạo WebSearchAgentLocal với CaptchaHandler
    agent = WebSearchAgentLocal(
        verbose=True,
        captcha_handler_config={
            "auto_solve": False,
            "use_selenium": False,
            "max_retries": 3,
            "retry_delay": 5,
        },
    )

    # URL giả có CAPTCHA
    url = "https://example.com/captcha"
    html_content = """
    <html>
    <head><title>CAPTCHA Test</title></head>
    <body>
        <div class="g-recaptcha" data-sitekey="6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"></div>
        <script src="https://www.google.com/recaptcha/api.js"></script>
    </body>
    </html>
    """

    print(f"Kiểm tra CAPTCHA trên URL: {url}")

    # Xử lý CAPTCHA
    result = agent.handle_captcha(html_content, url)

    print(f"Kết quả xử lý CAPTCHA:")
    print(f"  Thành công: {result.get('success', False)}")
    print(f"  Thông báo: {result.get('message', '')}")
    print(f"  Loại CAPTCHA: {result.get('captcha_type', 'unknown')}")


def test_vietnamese_search():
    """Test tìm kiếm tiếng Việt."""
    print_section("Tìm kiếm tiếng Việt")

    # Kiểm tra xem có integrate_vietnamese_search không
    if integrate_vietnamese_search is None:
        print("Module tìm kiếm tiếng Việt không khả dụng")
        return

    # Khởi tạo WebSearchAgentLocal
    agent = WebSearchAgentLocal(search_method="auto", verbose=True)

    # Tích hợp tìm kiếm tiếng Việt
    integrate_vietnamese_search(agent)

    # Kiểm tra các thuộc tính và phương thức đã được thêm
    print(f"Có phương thức search_vietnamese: {hasattr(agent, 'search_vietnamese')}")
    print(
        f"Có phương thức detect_vietnamese_query: {hasattr(agent, 'detect_vietnamese_query')}"
    )
    print(
        f"Có phương thức extract_vietnamese_content: {hasattr(agent, 'extract_vietnamese_content')}"
    )

    # Thực hiện tìm kiếm tiếng Việt
    query = "Lập trình Python"
    print(f"Truy vấn tiếng Việt: {query}")

    try:
        # Kiểm tra phát hiện tiếng Việt
        if hasattr(agent, "detect_vietnamese_query"):
            is_vietnamese = agent.detect_vietnamese_query(query)
            print(f"Phát hiện tiếng Việt: {is_vietnamese}")

        # Thực hiện tìm kiếm tiếng Việt
        if hasattr(agent, "search_vietnamese"):
            result = agent.search_vietnamese(query, num_results=3)
            print_result(result)

            # Kiểm tra kết quả tìm kiếm tiếng Việt
            if result.get("success"):
                results = result.get("results", [])
                if results:
                    print("\nKiểm tra kết quả tìm kiếm tiếng Việt:")
                    for i, item in enumerate(results[:2]):
                        print(f"Kết quả {i+1}:")
                        print(f"  Tiêu đề: {item.get('title', '')}")
                        print(f"  URL: {item.get('url', '')}")

                        # Trích xuất nội dung tiếng Việt
                        if hasattr(agent, "extract_vietnamese_content") and item.get(
                            "url"
                        ):
                            try:
                                content_result = agent.extract_vietnamese_content(
                                    item.get("url")
                                )
                                if content_result.get("success"):
                                    content = content_result.get("text", "")
                                    print(
                                        f"  Nội dung tiếng Việt: {content[:100]}..."
                                        if len(content) > 100
                                        else f"  Nội dung tiếng Việt: {content}"
                                    )

                                    # Kiểm tra chuẩn hóa văn bản tiếng Việt
                                    if hasattr(agent, "normalize_vietnamese_text"):
                                        normalized = agent.normalize_vietnamese_text(
                                            content
                                        )
                                        print(
                                            f"  Văn bản chuẩn hóa: {normalized[:100]}..."
                                            if len(normalized) > 100
                                            else f"  Văn bản chuẩn hóa: {normalized}"
                                        )
                            except Exception as e:
                                print(f"  Lỗi khi trích xuất nội dung: {str(e)}")

        # Kiểm tra với các loại truy vấn tiếng Việt khác nhau
        vietnamese_queries = [
            "Thủ đô của Việt Nam",
            "Cách nấu phở bò",
            "Lịch sử Việt Nam thời kỳ đổi mới",
            "Đặc sản miền Trung Việt Nam",
            "Kinh tế Việt Nam năm 2023",
        ]

        print("\nKiểm tra phát hiện tiếng Việt với nhiều loại truy vấn:")
        for vn_query in vietnamese_queries[
            :2
        ]:  # Chỉ kiểm tra 2 truy vấn để tiết kiệm thời gian
            if hasattr(agent, "detect_vietnamese_query"):
                is_vn = agent.detect_vietnamese_query(vn_query)
                print(f"  '{vn_query}': {is_vn}")
    except Exception as e:
        print(f"Lỗi khi tìm kiếm tiếng Việt: {str(e)}")


def test_adaptive_crawler():
    """Test AdaptiveCrawler."""
    print_section("Tích hợp AdaptiveCrawler")

    # Kiểm tra xem có integrate_adaptive_crawler không
    if integrate_adaptive_crawler is None:
        print("Module AdaptiveCrawler không khả dụng")
        return

    # Khởi tạo WebSearchAgentLocal
    agent = WebSearchAgentLocal(search_method="auto", verbose=True)

    # Tích hợp AdaptiveCrawler
    integrate_adaptive_crawler(agent)

    # Kiểm tra các thuộc tính và phương thức đã được thêm
    print(f"Có thuộc tính _adaptive_crawler: {hasattr(agent, '_adaptive_crawler')}")
    print(
        "Có phương thức _deep_crawl_with_adaptive_crawler: "
        f"{hasattr(agent, '_deep_crawl_with_adaptive_crawler')}"
    )
    print(f"Có phương thức crawl_url: {hasattr(agent, 'crawl_url')}")
    print(f"Có phương thức crawl_urls: {hasattr(agent, 'crawl_urls')}")

    # Thử crawl một URL
    url = "https://www.python.org/"
    print(f"Crawl URL: {url}")

    try:
        if hasattr(agent, "_deep_crawl_with_adaptive_crawler"):
            # Crawl với các cấu hình khác nhau
            configs = [
                {"max_depth": 1, "max_pages": 3, "timeout": 30, "include_html": False},
                {
                    "max_depth": 2,
                    "max_pages": 2,
                    "timeout": 20,
                    "include_html": False,
                    "extract_metadata": True,
                    "extract_links": True,
                },
            ]

            for i, config in enumerate(
                configs[:1]
            ):  # Chỉ chạy cấu hình đầu tiên để tiết kiệm thời gian
                print(f"\nCấu hình {i+1}: {config}")

                # Gọi phương thức _deep_crawl_with_adaptive_crawler
                result = agent._deep_crawl_with_adaptive_crawler(**config, url=url)

                print(f"Crawl thành công: {result.get('success', False)}")
                if result.get("success", False):
                    print(f"Tiêu đề: {result.get('title', '')}")
                    text = result.get("text", "")
                    print(
                        f"Nội dung: {text[:200]}..."
                        if len(text) > 200
                        else f"Nội dung: {text}"
                    )

                    # Kiểm tra metadata
                    if config.get("extract_metadata", False):
                        metadata = result.get("metadata", {})
                        if metadata:
                            print(f"Metadata: {metadata}")

                    # Kiểm tra links
                    if config.get("extract_links", False):
                        links = result.get("links", [])
                        if links:
                            print(f"Số lượng links: {len(links)}")
                            if links:
                                print(f"Link đầu tiên: {links[0]}")

            # Kiểm tra phương thức crawl_url nếu có
            if hasattr(agent, "crawl_url"):
                print("\nKiểm tra phương thức crawl_url:")
                crawl_result = agent.crawl_url(url, max_depth=1)
                print(f"Crawl thành công: {crawl_result.get('success', False)}")
                if crawl_result.get("success", False):
                    print(
                        f"Số lượng trang đã crawl: {len(crawl_result.get('pages', []))}"
                    )
    except Exception as e:
        print(f"Lỗi khi crawl URL: {str(e)}")


def test_deep_research_integration():
    """Test tích hợp deep_research."""
    print_section("Tích hợp deep_research")

    # Kiểm tra xem có integrate_deep_research không
    if integrate_deep_research is None:
        print("Module deep_research không khả dụng")
        return

    # Khởi tạo WebSearchAgentLocal
    agent = WebSearchAgentLocal(search_method="auto", verbose=True)

    # Tích hợp deep_research
    integrate_deep_research(agent)

    # Kiểm tra các thuộc tính và phương thức đã được thêm
    print(f"Có phương thức deep_research: {hasattr(agent, 'deep_research')}")
    print(f"Có phương thức _decompose_query: {hasattr(agent, '_decompose_query')}")
    print(f"Có phương thức _search_sub_query: {hasattr(agent, '_search_sub_query')}")
    print(f"Có phương thức _merge_results: {hasattr(agent, '_merge_results')}")

    # Kiểm tra với các loại câu hỏi khác nhau
    queries = [
        "Compare Python and JavaScript",
        "What are the advantages and disadvantages of cloud computing?",
    ]

    for query in queries[:1]:  # Chỉ kiểm tra câu hỏi đầu tiên để tiết kiệm thời gian
        print(f"\nTruy vấn deep_research: {query}")

        try:
            # Kiểm tra đánh giá độ phức tạp câu hỏi
            if hasattr(agent, "evaluate_question_complexity"):
                print("Đánh giá độ phức tạp câu hỏi:")
                complexity = agent.evaluate_question_complexity(query)
                print(f"  Độ phức tạp: {complexity.get('complexity_level', 'unknown')}")
                print(f"  Điểm phức tạp: {complexity.get('complexity_score', 0)}")

            # Thực hiện deep_research
            if hasattr(agent, "deep_research"):
                # Thử với các cấu hình khác nhau
                configs = [
                    {
                        "num_results_per_query": 2,
                        "max_sub_queries": 3,
                        "min_sub_queries": 2,
                        "max_content_length": 5000,
                        "max_depth": 1,
                        "use_parallel_processing": True,
                    }
                ]

                for config in configs:
                    print(f"\nCấu hình: {config}")

                    # Đo thời gian thực hiện
                    start_time = time.time()

                    # Thực hiện deep_research
                    result = agent.deep_research(query=query, **config)

                    # Kết thúc đo thời gian
                    end_time = time.time()

                    print(f"Thời gian thực hiện: {end_time - start_time:.2f} giây")
                    print(f"Deep research thành công: {result.get('success', False)}")

                    if result.get("success", False):
                        # Kiểm tra phân rã câu hỏi
                        sub_queries = result.get("sub_queries", [])
                        print(f"Số truy vấn con: {len(sub_queries)}")
                        if sub_queries:
                            print(f"Các truy vấn con: {', '.join(sub_queries)}")

                        # Kiểm tra kết quả tìm kiếm
                        sub_results = result.get("sub_results", [])
                        if sub_results:
                            print(f"Số kết quả con: {len(sub_results)}")

                            # Kiểm tra phân phối kết quả
                            if hasattr(agent, "_analyze_result_distribution"):
                                distribution = agent._analyze_result_distribution(
                                    sub_results
                                )
                                print(f"Phân phối kết quả: {distribution}")

                        # Kiểm tra nội dung
                        content = result.get("content", "")
                        print(
                            f"Nội dung: {content[:200]}..."
                            if len(content) > 200
                            else f"Nội dung: {content}"
                        )
        except Exception as e:
            print(f"Lỗi khi thực hiện deep_research: {str(e)}")


def test_deep_crawl_improvements():
    """Test cải tiến cho phương thức _deep_crawl."""
    print_section("Cải tiến cho phương thức _deep_crawl")

    # Kiểm tra xem có integrate_deep_crawl_improvements không
    if integrate_deep_crawl_improvements is None:
        print("Module deep_crawl_improvements không khả dụng")
        return

    # Khởi tạo WebSearchAgentLocal
    agent = WebSearchAgentLocal(search_method="auto", verbose=True)

    # Tích hợp cải tiến cho _deep_crawl
    integrate_deep_crawl_improvements(agent)

    # Kiểm tra các thuộc tính và phương thức đã được thêm
    print(
        "Có phương thức _deep_crawl_improved: "
        f"{hasattr(agent, '_deep_crawl_improved')}"
    )
    print(
        "Có phương thức _extract_file_content: "
        f"{hasattr(agent, '_extract_file_content')}"
    )
    print(
        "Có phương thức _extract_pdf_content: "
        f"{hasattr(agent, '_extract_pdf_content')}"
    )
    print(
        "Có phương thức _extract_docx_content: "
        f"{hasattr(agent, '_extract_docx_content')}"
    )
    print(
        "Có phương thức _extract_xlsx_content: "
        f"{hasattr(agent, '_extract_xlsx_content')}"
    )
    print(
        "Có phương thức _extract_pptx_content: "
        f"{hasattr(agent, '_extract_pptx_content')}"
    )

    # Thử _deep_crawl_improved với các định dạng file khác nhau
    urls = [
        "https://www.python.org/",
        "https://example.com/sample.pdf",
        "https://thuvienphapluat.vn/page/tim-van-ban.aspx",
    ]

    # Chỉ kiểm tra URL đầu tiên để tiết kiệm thời gian
    for url in urls[:1]:
        print(f"\nDeep crawl URL: {url}")

        try:
            if hasattr(agent, "_deep_crawl_improved"):
                # Xác định các tham số dựa trên URL
                handle_javascript = ".aspx" in url.lower()

                # Bắt đầu đo thời gian
                start_time = time.time()

                # Gọi phương thức _deep_crawl_improved
                result = agent._deep_crawl_improved(
                    url=url,
                    max_depth=1,
                    max_pages=3,
                    timeout=30,
                    include_html=False,
                    extract_files=True,
                    handle_javascript=handle_javascript,
                    use_async=True,
                )

                # Kết thúc đo thời gian
                end_time = time.time()

                print(f"Deep crawl thành công: {result.get('success', False)}")
                print(f"Thời gian thực hiện: {end_time - start_time:.2f} giây")

                if result.get("success", False):
                    print(f"Tiêu đề: {result.get('title', '')}")
                    text = result.get("text", "")
                    print(
                        f"Nội dung: {text[:200]}..."
                        if len(text) > 200
                        else f"Nội dung: {text}"
                    )

                    # Kiểm tra metadata
                    metadata = result.get("metadata", {})
                    if metadata:
                        print(f"Metadata: {metadata}")

                    # Kiểm tra links
                    links = result.get("links", [])
                    if links:
                        print(f"Số lượng links: {len(links)}")
                        if links:
                            print(f"Link đầu tiên: {links[0]}")

                    # Kiểm tra crawl_stats
                    crawl_stats = result.get("crawl_stats", {})
                    if crawl_stats:
                        print(f"Crawl stats: {crawl_stats}")
                else:
                    print(f"Lỗi: {result.get('error', 'Unknown error')}")
            else:
                print("Phương thức _deep_crawl_improved không khả dụng")
        except Exception as e:
            print(f"Lỗi khi thực hiện deep_crawl: {str(e)}")

    # Kiểm tra trích xuất nội dung từ các định dạng file khác nhau
    print("\nKiểm tra trích xuất nội dung từ các định dạng file khác nhau")

    # Danh sách các file để kiểm tra
    test_files = {
        "pdf": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf",
        "docx": "https://file-examples.com/storage/fe8c7eef0c6364f6c9504cc/2017/02/file-sample_100kB.docx",
        "xlsx": "https://file-examples.com/storage/fe8c7eef0c6364f6c9504cc/2017/02/file_example_XLSX_10.xlsx",
        "pptx": "https://file-examples.com/storage/fe8c7eef0c6364f6c9504cc/2017/02/file-sample_100kB.pptx",
    }

    # Chỉ kiểm tra file PDF để tiết kiệm thời gian
    for file_type, file_url in list(test_files.items())[:1]:
        print(f"\nTrích xuất nội dung từ file {file_type.upper()}: {file_url}")

        try:
            if hasattr(agent, "_extract_file_content"):
                # Bắt đầu đo thời gian
                start_time = time.time()

                # Gọi phương thức _extract_file_content
                result = agent._extract_file_content(file_url, file_type, 30)

                # Kết thúc đo thời gian
                end_time = time.time()

                print(f"Thời gian thực hiện: {end_time - start_time:.2f} giây")
                print(f"Trích xuất thành công: {result.get('success', False)}")

                if result.get("success", False):
                    print(f"Loại file: {result.get('file_type', '')}")
                    text = result.get("text", "")
                    print(
                        f"Nội dung: {text[:200]}..."
                        if len(text) > 200
                        else f"Nội dung: {text}"
                    )

                    # Kiểm tra metadata
                    metadata = result.get("metadata", {})
                    if metadata:
                        print(f"Metadata: {metadata}")
        except Exception as e:
            print(f"Lỗi khi trích xuất nội dung từ file {file_type}: {str(e)}")

    # Kiểm tra xử lý trang web động
    print("\nKiểm tra xử lý trang web động")
    dynamic_url = "https://example.com/dynamic-page"

    try:
        if hasattr(agent, "_handle_dynamic_page"):
            # Bắt đầu đo thời gian
            start_time = time.time()

            # Gọi phương thức _handle_dynamic_page
            result = agent._handle_dynamic_page(dynamic_url, 30, True)

            # Kết thúc đo thời gian
            end_time = time.time()

            print(f"Thời gian thực hiện: {end_time - start_time:.2f} giây")
            print(f"Xử lý trang động thành công: {result.get('success', False)}")

            if result.get("success", False):
                print(f"Tiêu đề: {result.get('title', '')}")
                text = result.get("text", "")
                print(
                    f"Nội dung: {text[:200]}..."
                    if len(text) > 200
                    else f"Nội dung: {text}"
                )

                # Kiểm tra links
                links = result.get("links", [])
                if links:
                    print(f"Số lượng links: {len(links)}")
                    if links:
                        print(f"Link đầu tiên: {links[0]}")

                # Kiểm tra HTML
                html = result.get("html")
                if html:
                    print(f"HTML length: {len(html)}")
    except Exception as e:
        print(f"Lỗi khi xử lý trang động: {str(e)}")


def main():
    """Main function."""
    print_section("Kiểm tra WebSearchAgentLocal")

    try:
        # Chọn các bài kiểm tra để chạy
        test_searxng_search()
        test_crawlee_search()
        test_auto_search()
        test_content_extraction()
        test_cache()

        # Các bài kiểm tra mới
        test_question_complexity()
        test_answer_quality()
        test_deep_crawl()
        test_captcha_handling()

        # Các bài kiểm tra tích hợp mới
        test_vietnamese_search()
        test_adaptive_crawler()
        test_deep_research_integration()
        test_deep_crawl_improvements()
    except Exception as e:
        import traceback

        print(f"Lỗi: {str(e)}")
        traceback.print_exc()


if __name__ == "__main__":
    main()
