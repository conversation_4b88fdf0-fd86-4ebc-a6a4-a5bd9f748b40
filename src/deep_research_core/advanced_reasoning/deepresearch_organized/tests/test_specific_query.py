#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for a specific query using WebSearchAgent.
"""

import sys
import os
import time
import json
import logging

# Thiết lập logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import WebSearchAgent
try:
    from src.deep_research_core.agents.web_search_agent import WebSearchAgent
    from src.deep_research_core.agents.searxng_search import search_searxng, search_with_fallback
except ImportError:
    logger.error("Failed to import WebSearchAgent. Make sure the path is correct.")
    sys.exit(1)

def test_specific_query(query):
    """Test WebSearchAgent with a specific query."""
    print(f"=== Testing WebSearchAgent with query: '{query}' ===")
    
    # Create WebSearchAgent
    agent = WebSearchAgent(
        search_method="api",
        api_search_config={
            "engine": "searx",
            "searx_url": "http://localhost:8080"
        },
        verbose=True
    )
    
    # Test search
    print(f"Searching for: {query}")
    
    result = agent.search(query, num_results=10)
    
    print(f"Success: {result.get('success', False)}")
    print(f"Number of results: {len(result.get('results', []))}")
    
    # Print results
    if result.get('results'):
        print("\nResults:")
        for i, res in enumerate(result.get('results', [])):
            print(f"\n--- Result {i+1} ---")
            print(f"Title: {res.get('title', 'N/A')}")
            print(f"URL: {res.get('url', 'N/A')}")
            print(f"Content: {res.get('content', 'N/A')[:200]}...")
    else:
        print("\nNo results found.")
    
    return result

def test_direct_searxng(query):
    """Test SearXNG search directly."""
    print(f"\n=== Testing SearXNG directly with query: '{query}' ===")
    
    result = search_searxng(
        query=query,
        num_results=10,
        searxng_instance="http://localhost:8080",
        language="en"
    )
    
    print(f"Success: {result.get('success', False)}")
    print(f"Number of results: {len(result.get('results', []))}")
    
    # Print results
    if result.get('results'):
        print("\nResults:")
        for i, res in enumerate(result.get('results', [])):
            print(f"\n--- Result {i+1} ---")
            print(f"Title: {res.get('title', 'N/A')}")
            print(f"URL: {res.get('url', 'N/A')}")
            print(f"Content: {res.get('content', 'N/A')[:200]}...")
    else:
        print("\nNo results found.")
    
    return result

def main():
    """Main function."""
    # Default query
    query = "Who is mr Karrman Kim samsung sds"
    
    # Use command line argument if provided
    if len(sys.argv) > 1:
        query = sys.argv[1]
    
    print(f"Testing search with query: '{query}'")
    print("=" * 50)
    
    # Test WebSearchAgent
    agent_result = test_specific_query(query)
    
    # Test SearXNG directly
    searxng_result = test_direct_searxng(query)
    
    # Save results to file
    with open("specific_query_results.json", "w") as f:
        json.dump({
            "query": query,
            "agent_result": agent_result,
            "searxng_result": searxng_result
        }, f, indent=2)
    
    print("\n=== Test Summary ===")
    print(f"Query: '{query}'")
    print(f"WebSearchAgent results: {len(agent_result.get('results', []))}")
    print(f"SearXNG direct results: {len(searxng_result.get('results', []))}")
    print(f"Results saved to specific_query_results.json")

if __name__ == "__main__":
    main()
