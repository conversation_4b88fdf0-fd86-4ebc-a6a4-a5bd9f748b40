#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Standalone test script that doesn't depend on any other modules.
Improved version with proper dictionary initialization.
"""

import time
import logging
import random
from typing import Dict, List, Any, Optional, Union

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WebSearchAgentStandalone:
    """
    A standalone implementation of WebSearchAgent with improved error handling.
    """
    
    def __init__(self, **kwargs):
        """
        Initialize WebSearchAgentStandalone with proper dictionary initialization.
        
        Args:
            **kwargs: Additional configuration options
        """
        # Initialize dictionaries first to avoid 'int' object is not subscriptable error
        self.engine_request_counts = {}
        self.engine_reset_times = {}
        self.cache = {}
        
        # Initialize other attributes
        self.name = "WebSearchAgentStandalone"
        self.version = "1.0.0"
        self.timeout = kwargs.get("timeout", 10)
        self.rate_limit = kwargs.get("rate_limit", 10)
        self.verbose = kwargs.get("verbose", False)
        
        # Initialize counters
        self.request_count = 0
        self.request_count_reset_time = time.time()
        
        # Initialize rate limits
        self.engine_rate_limits = {
            'duckduckgo': 5,
            'searx': 10,
            'qwant': 8,
            'default': self.rate_limit
        }
        
        # Initialize user agents
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.2 Safari/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:96.0) Gecko/20100101 Firefox/96.0",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36"
        ]
        self.current_user_agent_index = 0
        
        # Verify dictionaries are properly initialized
        self._verify_dictionaries()
        
        if self.verbose:
            logger.info(f"Initialized {self.name} v{self.version}")
    
    def _verify_dictionaries(self):
        """
        Verify that all dictionary attributes are properly initialized.
        If not, initialize them.
        """
        # Check engine_request_counts
        if not isinstance(self.engine_request_counts, dict):
            logger.warning("engine_request_counts is not a dictionary. Reinitializing.")
            self.engine_request_counts = {}
        
        # Check engine_reset_times
        if not isinstance(self.engine_reset_times, dict):
            logger.warning("engine_reset_times is not a dictionary. Reinitializing.")
            self.engine_reset_times = {}
        
        # Check cache
        if not isinstance(self.cache, dict):
            logger.warning("cache is not a dictionary. Reinitializing.")
            self.cache = {}
    
    def search(self, query: str, num_results: int = 10, language: str = "auto", 
               engine: Optional[str] = None, method: Optional[str] = None, 
               get_content: bool = False, force_refresh: bool = False, **kwargs) -> Dict[str, Any]:
        """
        Search the web for a query.
        
        Args:
            query: The search query
            num_results: Number of results to return
            language: Language for search results
            engine: Search engine to use
            method: Search method to use
            get_content: Whether to get full content of results
            force_refresh: Whether to force refresh cache
            **kwargs: Additional search parameters
            
        Returns:
            Dictionary with search results
        """
        # Verify dictionaries before use
        self._verify_dictionaries()
        
        if self.verbose:
            logger.info(f"Searching for: {query}")
        
        # Return mock results
        return {
            "success": True,
            "query": query,
            "results": [
                {
                    "title": "Python Programming - Wikipedia",
                    "url": "https://en.wikipedia.org/wiki/Python_(programming_language)",
                    "snippet": "Python is a high-level, general-purpose programming language. Its design philosophy emphasizes code readability with the use of significant indentation.",
                    "source": "mock",
                    "engine": "mock",
                },
                {
                    "title": "Python.org",
                    "url": "https://www.python.org/",
                    "snippet": "The official home of the Python Programming Language. Python is a programming language that lets you work quickly and integrate systems more effectively.",
                    "source": "mock",
                    "engine": "mock",
                }
            ],
            "engine": engine or "mock",
            "search_method": method or "mock",
            "timestamp": time.time(),
        }
    
    def _get_rotated_user_agent(self, rotate: bool = True) -> str:
        """
        Get a user agent, optionally rotating through a list.
        
        Args:
            rotate: Whether to rotate to the next user agent
            
        Returns:
            User agent string
        """
        # Get current user agent
        user_agent = self.user_agents[self.current_user_agent_index]
        
        # Rotate if requested
        if rotate:
            self.current_user_agent_index = (self.current_user_agent_index + 1) % len(self.user_agents)
        
        return user_agent

# Create an alias for WebSearchAgentMerged
WebSearchAgentMerged = WebSearchAgentStandalone

# Main function
def main():
    """
    Main function.
    """
    # Create agent
    agent = WebSearchAgentStandalone(verbose=True)
    print(f"Created agent: {agent.name} v{agent.version}")
    
    # Test a simple search
    print("\nTrying a simple search...")
    results = agent.search("Python programming", num_results=2)
    
    # Print results
    print(f"Search success: {results.get('success', False)}")
    print(f"Results count: {len(results.get('results', []))}")
    
    # Print first result
    if results.get('results'):
        first_result = results['results'][0]
        print(f"First result:")
        print(f"  - Title: {first_result.get('title', 'N/A')}")
        print(f"  - URL: {first_result.get('url', 'N/A')}")
        print(f"  - Snippet: {first_result.get('snippet', 'N/A')[:100]}...")
    
    # Test WebSearchAgentMerged
    print("\nTesting WebSearchAgentMerged...")
    merged_agent = WebSearchAgentMerged(verbose=True)
    print(f"Created merged agent: {merged_agent.name} v{merged_agent.version}")
    
    # Test a simple search with merged agent
    print("\nTrying a simple search with merged agent...")
    merged_results = merged_agent.search("Python programming", num_results=2)
    
    # Print results
    print(f"Search success: {merged_results.get('success', False)}")
    print(f"Results count: {len(merged_results.get('results', []))}")
    
    print("\nTest completed!")

# Run main function
if __name__ == "__main__":
    main()
