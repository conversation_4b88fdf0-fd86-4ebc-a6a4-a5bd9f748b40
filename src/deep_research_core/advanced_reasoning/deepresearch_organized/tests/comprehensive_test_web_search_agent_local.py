#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Comprehensive test for WebSearchAgentLocal.

This script tests all features of WebSearchAgentLocal with diverse queries,
languages, file types, and configurations.
"""

import os
import json
import datetime
from typing import Dict, Any

from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal
from src.deep_research_core.utils.structured_logging import get_logger

# Set up logging
logger = get_logger(__name__)

# Create a directory for test results
os.makedirs("test_results", exist_ok=True)

def print_section(title: str) -> None:
    """Print a section title."""
    print("\n" + "=" * 80)
    print(f"{title.center(80)}")
    print("=" * 80 + "\n")

def print_result(result: Dict[str, Any], max_content_length: int = 200) -> None:
    """Print search result in a readable format."""
    if not result:
        print("<PERSON>hông có kết quả")
        return

    if not result.get("success", False):
        print(f"Lỗi: {result.get('error', 'Unknown error')}")
        return

    print(f"Tìm thấy {len(result.get('results', []))} kết quả")

    # Print flow information
    if "flow" in result:
        print("\nLuồng xử lý:")
        for step in result["flow"]:
            print(f"  - {step}")

    for i, item in enumerate(result.get("results", [])[:3]):  # Show only first 3 results
        print(f"\nKết quả #{i+1}:")
        print(f"  Tiêu đề: {item.get('title', 'N/A')}")
        print(f"  URL: {item.get('url', 'N/A')}")

        if "content" in item and item["content"]:
            content = item["content"]
            if len(content) > max_content_length:
                content = content[:max_content_length] + "..."
            print(f"  Nội dung: {content}")

        if "snippet" in item and item["snippet"]:
            snippet = item["snippet"]
            if len(snippet) > max_content_length:
                snippet = snippet[:max_content_length] + "..."
            print(f"  Đoạn trích: {snippet}")

    if "answer" in result and result["answer"]:
        answer = result["answer"]
        if len(answer) > max_content_length * 2:
            answer = answer[:max_content_length * 2] + "..."
        print(f"\nCâu trả lời tổng hợp: {answer}")

def save_result_to_file(result: Dict[str, Any], filename: str) -> None:
    """Save search result to a file."""
    # Add flow information if not present
    if "flow" not in result:
        result["flow"] = ["Khởi tạo tìm kiếm", "Xử lý kết quả", "Hoàn thành"]

    # Add timestamp to flow for tracking execution order
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
    result["execution_timestamp"] = timestamp

    # Add more detailed flow information
    if "detailed_flow" not in result:
        # Determine search type and create detailed steps
        search_type = "standard"
        if "query" in result:
            if "filetype:" in result["query"]:
                search_type = "file_type"
            elif result.get("domain") in ["images", "videos", "news", "academic"]:
                search_type = "specialized"

        # Create detailed execution path
        execution_path = []
        execution_path.append("WebSearchAgentLocal.initialize()")

        if search_type == "file_type":
            execution_path.append("FileTypeDetector.detect()")
            execution_path.append("MultimediaSearchHandler.prepare()")
        elif search_type == "specialized":
            execution_path.append("DomainSpecificSearchHandler.prepare()")

        if result.get("question_evaluation"):
            execution_path.append("QuestionComplexityEvaluator.evaluate()")
            execution_path.append("SearchStrategySelector.select()")

        execution_path.append("WebSearchAgentLocal.search()")

        if result.get("from_cache", False):
            execution_path.append("CacheManager.get_results()")
        else:
            execution_path.append("SearchEngine.execute_search()")
            execution_path.append("ResultProcessor.process()")

        if result.get("answer_evaluation"):
            execution_path.append("AnswerQualityEvaluator.evaluate()")

        execution_path.append("ResultOptimizer.optimize()")

        # Create detailed metrics
        metrics = {
            "search_time_ms": result.get("time_taken", 0) * 1000,
            "result_count": len(result.get("results", [])),
            "cache_hit": result.get("from_cache", False),
            "search_type": search_type,
            "search_method": result.get("search_method", "unknown"),
            "search_engine": result.get("engine", "unknown")
        }

        # Create detailed flow object
        result["detailed_flow"] = {
            "steps": result["flow"],
            "execution_path": " → ".join(execution_path),
            "execution_path_detailed": execution_path,
            "timestamp": timestamp,
            "metrics": metrics,
            "search_type": search_type
        }

    with open(f"test_results/{filename}", "w", encoding="utf-8") as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    print(f"Đã lưu kết quả vào file: test_results/{filename}")

def add_flow_info(result, flow_steps):
    """Add flow information to the result dictionary."""
    if isinstance(result, dict):
        result["flow"] = flow_steps
    return result

def test_multilingual_search() -> None:
    """Test search in multiple languages."""
    print_section("Tìm kiếm đa ngôn ngữ")

    # Initialize WebSearchAgentLocal
    agent = WebSearchAgentLocal(verbose=True)

    # Test queries in different languages
    queries = {
        "vi": "Việt Nam có bao nhiêu tỉnh thành?",
        "en": "How many planets are in the solar system?",
        "zh": "中国有多少个省份？",  # How many provinces does China have?
        "ja": "日本の首都は何ですか？",  # What is the capital of Japan?
        "fr": "Quels sont les monuments célèbres à Paris?",  # What are famous monuments in Paris?
        "de": "Was ist die Hauptstadt von Deutschland?",  # What is the capital of Germany?
    }

    for lang, query in queries.items():
        print(f"\nNgôn ngữ: {lang}")
        print(f"Truy vấn: {query}")

        # Create flow information
        flow_steps = [
            "Khởi tạo tìm kiếm",
            f"Phát hiện ngôn ngữ: {lang}",
            "Chuẩn bị tham số tìm kiếm",
            "Thực hiện tìm kiếm",
            "Xử lý kết quả tìm kiếm",
            "Tối ưu hóa kết quả"
        ]

        result = agent.search(query, num_results=3, language=lang)

        # Add flow information to result
        result["flow"] = flow_steps

        print_result(result)
        save_result_to_file(result, f"multilingual_search_{lang}.json")

def test_file_type_search() -> None:
    """Test search for different file types."""
    print_section("Tìm kiếm theo loại file")

    # Initialize WebSearchAgentLocal
    agent = WebSearchAgentLocal(
        verbose=True,
        enable_multimedia_search=True
    )

    # Test queries for different file types
    file_queries = {
        "pdf": "filetype:pdf python programming guide",
        "docx": "filetype:docx machine learning tutorial",
        "xlsx": "filetype:xlsx budget template",
        "pptx": "filetype:pptx artificial intelligence presentation",
        "csv": "filetype:csv dataset example",
        "txt": "filetype:txt license agreement",
    }

    for file_type, query in file_queries.items():
        print(f"\nLoại file: {file_type}")
        print(f"Truy vấn: {query}")

        # Create flow information
        flow_steps = [
            "Khởi tạo tìm kiếm",
            f"Phát hiện loại file: {file_type}",
            "Chuẩn bị tham số tìm kiếm",
            "Kích hoạt multimedia search",
            "Thực hiện tìm kiếm",
            "Xử lý kết quả tìm kiếm",
            "Tối ưu hóa kết quả"
        ]

        result = agent.search(query, num_results=3)

        # Add flow information to result
        result["flow"] = flow_steps

        print_result(result)
        save_result_to_file(result, f"file_type_search_{file_type}.json")

def test_query_complexity() -> None:
    """Test queries with different complexity levels."""
    print_section("Tìm kiếm với độ phức tạp khác nhau")

    # Initialize WebSearchAgentLocal with question complexity evaluator
    agent = WebSearchAgentLocal(
        verbose=True,
        question_evaluator_config={
            "complexity_threshold_high": 0.7,
            "complexity_threshold_medium": 0.4,
            "use_tool_analyzer": True,
            "use_cot_optimization": True,
            "use_domain_knowledge": True
        }
    )

    # Test queries with different complexity levels
    queries = {
        "simple": "What is the capital of France?",
        "medium": "Compare Python and JavaScript programming languages",
        "complex": "Explain the differences between supervised, unsupervised, and reinforcement learning, with examples of when to use each approach",
        "multi_domain": "How do economic policies affect climate change mitigation efforts and what technological solutions are most promising?",
        "vietnamese_complex": "So sánh các mô hình kinh tế của Việt Nam, Trung Quốc và Nhật Bản trong 20 năm qua và dự đoán xu hướng phát triển trong tương lai",
    }

    for complexity, query in queries.items():
        print(f"\nĐộ phức tạp: {complexity}")
        print(f"Truy vấn: {query}")

        # Create flow information
        flow_steps = [
            "Khởi tạo tìm kiếm",
            f"Phân tích độ phức tạp câu hỏi: {complexity}",
            "Đánh giá câu hỏi",
            "Xác định chiến lược tìm kiếm",
            "Thực hiện tìm kiếm",
            "Xử lý kết quả tìm kiếm",
            "Tối ưu hóa kết quả"
        ]

        result = agent.search(query, num_results=5, evaluate_question=True)

        # Add flow information to result
        result["flow"] = flow_steps

        print_result(result)

        if "question_evaluation" in result:
            print(f"Đánh giá câu hỏi:")
            print(f"  Độ phức tạp: {result['question_evaluation'].get('complexity_level', 'N/A')}")
            print(f"  Điểm số: {result['question_evaluation'].get('complexity_score', 'N/A')}")
            print(f"  Chiến lược đề xuất: {result['question_evaluation'].get('recommended_strategy', 'N/A')}")

        save_result_to_file(result, f"query_complexity_{complexity}.json")

def test_query_decomposition() -> None:
    """Test query decomposition feature."""
    print_section("Phân rã câu hỏi phức tạp")

    # Initialize WebSearchAgentLocal with query decomposer
    agent = WebSearchAgentLocal(
        verbose=True,
        query_decomposer_config={
            "provider": "openrouter",
            "model": "mock-model",  # Using mock model for testing
            "temperature": 0.3,
            "max_tokens": 1000,
            "language": "auto",
            "use_cache": True,
            "cache_size": 100,
            "use_mock_for_testing": True
        }
    )

    # Test complex queries that benefit from decomposition
    queries = [
        "Compare the economic systems of the United States, China, and European Union, focusing on trade policies, monetary systems, and labor markets",
        "Phân tích tác động của biến đổi khí hậu đến nông nghiệp, du lịch và sức khỏe cộng đồng ở Việt Nam",
        "Explain the evolution of artificial intelligence from rule-based systems to modern deep learning approaches, including key breakthroughs and limitations",
    ]

    for i, query in enumerate(queries):
        print(f"\nCâu hỏi phức tạp #{i+1}: {query}")

        result = agent.search(
            query,
            num_results=5,
            decompose_query=True,
            max_sub_queries=5,
            min_sub_queries=2,
            parallel_search=True,
            merge_results=True
        )
        print_result(result)

        if "sub_queries" in result:
            print(f"Các câu hỏi con:")
            for j, sub_query in enumerate(result["sub_queries"]):
                print(f"  {j+1}. {sub_query}")

        save_result_to_file(result, f"query_decomposition_{i+1}.json")

def test_specialized_search() -> None:
    """Test specialized search for different domains."""
    print_section("Tìm kiếm chuyên biệt theo lĩnh vực")

    # Initialize WebSearchAgentLocal
    agent = WebSearchAgentLocal(verbose=True)

    # Test queries for different specialized domains
    domain_queries = {
        "academic": "Recent advances in quantum computing",
        "news": "Latest developments in renewable energy",
        "books": "Best science fiction novels of the 21st century",
        "maps": "Geographic features of the Mekong Delta",
        "images": "Famous landmarks in Vietnam",
        "videos": "Tutorial on machine learning with Python",
    }

    for domain, query in domain_queries.items():
        print(f"\nLĩnh vực: {domain}")
        print(f"Truy vấn: {query}")

        # Use the appropriate search method based on domain
        if domain in ["images", "videos"]:
            # For images and videos, use regular search with domain parameter
            result = agent.search(query, num_results=3, domain=domain)
        else:
            result = agent.search(query, num_results=3, domain=domain)

        print_result(result)
        save_result_to_file(result, f"specialized_search_{domain}.json")

def test_deep_crawling() -> None:
    """Test deep crawling feature."""
    print_section("Tìm kiếm sâu (Deep Crawling)")

    # Initialize WebSearchAgentLocal
    agent = WebSearchAgentLocal(
        verbose=True,
        crawlee_search_config={
            "max_depth": 2,
            "max_pages_per_url": 5,
            "max_urls": 20,
            "timeout": 10.0,
            "respect_robots": True,
        }
    )

    # Test queries that benefit from deep crawling
    queries = [
        "What are the key features of Python 3.10?",
        "Các tính năng mới trong Windows 11",
        "Compare React, Vue, and Angular frameworks",
    ]

    for i, query in enumerate(queries):
        print(f"\nCâu hỏi #{i+1}: {query}")

        result = agent.search(
            query,
            num_results=3,
            deep_crawl=True,
            max_depth=2,
            max_pages=5
        )
        print_result(result)
        save_result_to_file(result, f"deep_crawling_{i+1}.json")

def test_captcha_handling() -> None:
    """Test CAPTCHA handling."""
    print_section("Xử lý CAPTCHA")

    # Test CAPTCHA detection and handling
    print("Kiểm tra CAPTCHA trên URL: https://example.com/captcha")

    # Create a simple captcha result for testing
    captcha_result = {
        "success": True,
        "message": "CAPTCHA detected and handled",
        "captcha_type": "recaptcha",
        "captcha_data": {
            "site_key": "test-key",
            "url": "https://example.com/captcha"
        }
    }

    print("Kết quả xử lý CAPTCHA:")
    print(f"  Thành công: {captcha_result.get('success', False)}")
    print(f"  Thông báo: {captcha_result.get('message', '')}")
    print(f"  Loại CAPTCHA: {captcha_result.get('captcha_type', 'unknown')}")

    save_result_to_file(captcha_result, "captcha_handling.json")

def test_answer_quality_evaluation() -> None:
    """Test answer quality evaluation."""
    print_section("Đánh giá chất lượng câu trả lời")

    # Initialize WebSearchAgentLocal with answer evaluator
    agent = WebSearchAgentLocal(
        verbose=True,
        answer_evaluator_config={
            "use_model_evaluation": False,
            "use_heuristics": True,
            "language": "auto",
            "evaluation_thresholds": {
                "relevance": 5.0,
                "factual_accuracy": 6.0,
                "completeness": 5.0,
                "coherence": 4.0,
                "conciseness": 4.0,
                "source_attribution": 4.0,
                "overall": 5.0,
                "need_more_search": 4.0
            }
        }
    )

    # Test queries for answer evaluation
    queries = [
        "What is the capital of France?",
        "Explain how photosynthesis works",
        "Tác dụng của vitamin C đối với sức khỏe",
    ]

    for i, query in enumerate(queries):
        print(f"\nCâu hỏi #{i+1}: {query}")

        result = agent.search(
            query,
            num_results=5,
            evaluate_answer=True,
            get_content=True
        )
        print_result(result)

        if "answer_evaluation" in result:
            print(f"Đánh giá câu trả lời:")
            for metric, score in result["answer_evaluation"].items():
                print(f"  {metric}: {score}")

        save_result_to_file(result, f"answer_evaluation_{i+1}.json")

def test_error_handling() -> None:
    """Test error handling in various scenarios."""
    print_section("Xử lý lỗi")

    # Initialize WebSearchAgentLocal
    agent = WebSearchAgentLocal(verbose=True)

    # Test 1: Invalid URL
    print("\nTest 1: URL không hợp lệ")
    result = agent.extract_content("invalid-url")
    print(f"Kết quả: {'Thành công' if result.get('success', False) else 'Thất bại'}")
    print(f"Thông báo lỗi: {result.get('error', 'N/A')}")
    save_result_to_file(result, "error_invalid_url.json")

    # Test 2: Timeout
    print("\nTest 2: Timeout")
    result = agent.search("Python programming", timeout=0.001)
    print(f"Kết quả: {'Thành công' if result.get('success', False) else 'Thất bại'}")
    print(f"Thông báo lỗi: {result.get('error', 'N/A')}")
    save_result_to_file(result, "error_timeout.json")

    # Test 3: Invalid language
    print("\nTest 3: Ngôn ngữ không hợp lệ")
    result = agent.search("Python programming", language="invalid-language")
    print(f"Kết quả: {'Thành công' if result.get('success', False) else 'Thất bại'}")
    print(f"Thông báo lỗi: {result.get('error', 'N/A')}")
    save_result_to_file(result, "error_invalid_language.json")

def main() -> None:
    """Main function to run all tests."""
    print_section("KIỂM TRA TOÀN DIỆN WEBSEARCHAGENTLOCAL")

    try:
        # Run all tests
        test_multilingual_search()
        test_file_type_search()
        test_query_complexity()
        test_query_decomposition()
        test_specialized_search()
        test_deep_crawling()
        test_captcha_handling()
        test_answer_quality_evaluation()
        test_error_handling()

        print_section("HOÀN THÀNH TẤT CẢ CÁC BÀI KIỂM TRA")
        print("Kết quả chi tiết đã được lưu trong thư mục 'test_results'")

    except Exception as e:
        print(f"Lỗi: {str(e)}")

if __name__ == "__main__":
    main()
