"""
Simple test script for Tree of Thoughts (ToT) reasoning.

This script demonstrates the basic usage of the ToT implementation
in the Deep Research Core project.
"""

import sys
import os
import time
from typing import Dict, Any

# Add the src directory to the path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), 'src')))

from deep_research_core.reasoning.tot import TreeOfThought
from deep_research_core.utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

def test_basic_tot():
    """Test basic Tree of Thought reasoning with default parameters."""
    # Initialize the ToT reasoner
    tot = TreeOfThought(
        provider="openai",  # Change to your preferred provider
        temperature=0.7,
        max_branches=3,
        max_depth=3,
        verbose=True
    )
    
    # A complex query that would benefit from ToT reasoning
    query = "What are three innovative approaches to addressing climate change in Vietnam?"
    
    # Perform reasoning
    logger.info(f"Starting ToT reasoning for query: {query}")
    start_time = time.time()
    
    result = tot.reason(query)
    
    end_time = time.time()
    logger.info(f"ToT reasoning completed in {end_time - start_time:.2f} seconds")
    
    # Print the result
    print("\n====== TREE OF THOUGHT REASONING RESULT ======\n")
    print(f"Query: {query}")
    print(f"\nAnswer: {result['answer']}")
    print(f"\nExplored paths: {result['explored_paths']}")
    print(f"\nReasoning time: {result['reasoning_time']:.2f} seconds")
    print(f"\nProvider: {result['provider']}")
    print(f"\nModel: {result['model']}")
    
    return result

def test_tot_with_context():
    """Test Tree of Thought reasoning with context."""
    # Initialize the ToT reasoner
    tot = TreeOfThought(
        provider="openai",  # Change to your preferred provider
        temperature=0.7,
        max_branches=3,
        max_depth=3,
        verbose=True
    )
    
    # Query and relevant context
    query = "How can farmers in the Mekong Delta adapt to increasing saltwater intrusion?"
    context = """
    The Mekong Delta in Vietnam has been experiencing increasing saltwater intrusion due to:
    1. Sea level rise caused by climate change
    2. Decreased freshwater flow due to upstream dams
    3. Land subsidence from groundwater extraction
    4. Changes in seasonal rainfall patterns
    
    This affects rice production and freshwater availability for millions of people.
    """
    
    # Perform reasoning
    logger.info(f"Starting ToT reasoning with context for query: {query}")
    start_time = time.time()
    
    result = tot.reason(query, context=context)
    
    end_time = time.time()
    logger.info(f"ToT reasoning with context completed in {end_time - start_time:.2f} seconds")
    
    # Print the result
    print("\n====== TREE OF THOUGHT REASONING WITH CONTEXT RESULT ======\n")
    print(f"Query: {query}")
    print(f"\nContext: {context}")
    print(f"\nAnswer: {result['answer']}")
    print(f"\nExplored paths: {result['explored_paths']}")
    print(f"\nReasoning time: {result['reasoning_time']:.2f} seconds")
    
    return result

def test_tot_rag_integration():
    """Test Tree of Thought with RAG integration."""
    from deep_research_core.reasoning.tot_rag import ToTRAG
    
    # Initialize the ToT-RAG reasoner
    tot_rag = ToTRAG(
        provider="openai",  # Change to your preferred provider
        temperature=0.7,
        max_branches=3,
        max_depth=3,
        verbose=True,
        adaptive=True,
        use_advanced_optimization=True
    )
    
    # A query that would benefit from both retrieval and ToT reasoning
    query = "What are the most effective strategies for Vietnam to balance economic development with environmental protection?"
    
    # Perform processing
    logger.info(f"Starting ToT-RAG processing for query: {query}")
    start_time = time.time()
    
    result = tot_rag.process(query)
    
    end_time = time.time()
    logger.info(f"ToT-RAG processing completed in {end_time - start_time:.2f} seconds")
    
    # Print the result
    print("\n====== TOT-RAG INTEGRATION RESULT ======\n")
    print(f"Query: {query}")
    print(f"\nAnswer: {result['answer']}")
    print(f"\nRetrieved documents: {len(result.get('documents', []))}")
    print(f"\nReasoning time: {result['processing_time']:.2f} seconds")
    
    return result

if __name__ == "__main__":
    print("Testing basic Tree of Thought reasoning...")
    basic_result = test_basic_tot()
    
    print("\n\nTesting Tree of Thought reasoning with context...")
    context_result = test_tot_with_context()
    
    print("\n\nTesting ToT-RAG integration...")
    tot_rag_result = test_tot_rag_integration()
    
    print("\n\nAll tests completed!") 