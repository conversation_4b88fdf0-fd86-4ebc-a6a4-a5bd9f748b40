#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Simple test script for file extraction.
"""

import os
import sys
import logging
import tempfile
import shutil
import requests
from typing import Dict, Any, List, Optional, Union, BinaryIO
import mimetypes
import re
import base64

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleFileExtractor:
    """
    Simple file extractor for testing.
    """
    
    def __init__(self):
        """
        Initialize SimpleFileExtractor.
        """
        # Check for optional libraries
        self.pypdf_available = False
        self.docx_available = False
        self.pptx_available = False
        self.bs4_available = False
        self.markdown_available = False
        
        try:
            import PyPDF2
            self.pypdf_available = True
        except ImportError:
            logger.warning("PyPDF2 not available")
        
        try:
            import docx
            self.docx_available = True
        except ImportError:
            logger.warning("python-docx not available")
        
        try:
            from pptx import Presentation
            self.pptx_available = True
        except ImportError:
            logger.warning("python-pptx not available")
        
        try:
            from bs4 import BeautifulSoup
            self.bs4_available = True
        except ImportError:
            logger.warning("BeautifulSoup not available")
        
        try:
            import markdown
            self.markdown_available = True
        except ImportError:
            logger.warning("markdown not available")
    
    def extract_text_from_pdf(self, file_path: str) -> str:
        """
        Extract text from PDF file.
        
        Args:
            file_path: Path to PDF file
            
        Returns:
            Extracted text
        """
        if not self.pypdf_available:
            logger.error("PyPDF2 not available")
            return ""
        
        try:
            import PyPDF2
            
            with open(file_path, 'rb') as f:
                reader = PyPDF2.PdfReader(f)
                text = ""
                
                # Extract metadata
                if reader.metadata:
                    if reader.metadata.title:
                        text += f"Title: {reader.metadata.title}\n\n"
                    if reader.metadata.author:
                        text += f"Author: {reader.metadata.author}\n\n"
                
                # Extract text from pages
                for page_num in range(len(reader.pages)):
                    page = reader.pages[page_num]
                    text += page.extract_text() + "\n\n"
                
                return text.strip()
        except Exception as e:
            logger.error(f"Error extracting text from PDF: {str(e)}")
            return ""
    
    def extract_text_from_docx(self, file_path: str) -> str:
        """
        Extract text from DOCX file.
        
        Args:
            file_path: Path to DOCX file
            
        Returns:
            Extracted text
        """
        if not self.docx_available:
            logger.error("python-docx not available")
            return ""
        
        try:
            import docx
            
            doc = docx.Document(file_path)
            text = ""
            
            # Extract text from paragraphs
            for para in doc.paragraphs:
                text += para.text + "\n"
            
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    row_text = ""
                    for cell in row.cells:
                        if cell.text:
                            row_text += cell.text + " | "
                    if row_text:
                        text += row_text.rstrip(" | ") + "\n"
            
            return text.strip()
        except Exception as e:
            logger.error(f"Error extracting text from DOCX: {str(e)}")
            return ""
    
    def extract_text_from_markdown(self, file_path: str) -> str:
        """
        Extract text from Markdown file.
        
        Args:
            file_path: Path to Markdown file
            
        Returns:
            Extracted text
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # If markdown library is available, convert to HTML first
            if self.markdown_available:
                import markdown
                html = markdown.markdown(content)
                
                # If BeautifulSoup is available, use it to extract text
                if self.bs4_available:
                    from bs4 import BeautifulSoup
                    soup = BeautifulSoup(html, 'html.parser')
                    text = soup.get_text()
                    return text.strip()
                else:
                    # Simple HTML tag removal
                    text = re.sub(r'<[^>]+>', ' ', html)
                    return text.strip()
            else:
                # Return raw content
                return content.strip()
        except Exception as e:
            logger.error(f"Error extracting text from Markdown: {str(e)}")
            return ""
    
    def extract_text(self, file_path: str) -> Dict[str, Any]:
        """
        Extract text from file.
        
        Args:
            file_path: Path to file
            
        Returns:
            Dictionary with extraction results
        """
        result = {
            "success": False,
            "text": "",
            "mime_type": None,
            "filename": os.path.basename(file_path),
            "error": None
        }
        
        try:
            # Detect MIME type
            mime_type, _ = mimetypes.guess_type(file_path)
            result["mime_type"] = mime_type
            
            # Extract text based on MIME type
            if mime_type == 'application/pdf':
                result["text"] = self.extract_text_from_pdf(file_path)
                result["success"] = True
            elif mime_type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                result["text"] = self.extract_text_from_docx(file_path)
                result["success"] = True
            elif mime_type == 'text/markdown':
                result["text"] = self.extract_text_from_markdown(file_path)
                result["success"] = True
            elif mime_type and mime_type.startswith('text/'):
                # Plain text
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    result["text"] = f.read()
                    result["success"] = True
            else:
                result["error"] = f"Unsupported MIME type: {mime_type}"
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"Error extracting text: {str(e)}")
        
        return result

def download_test_file(url: str, output_dir: str) -> Optional[str]:
    """
    Download a test file from URL.
    
    Args:
        url: URL of the file
        output_dir: Directory to save the file
        
    Returns:
        Path to the downloaded file or None if download failed
    """
    try:
        # Get file name from URL
        file_name = os.path.basename(url)
        if not file_name:
            file_name = "test_file"
        
        # Create output path
        output_path = os.path.join(output_dir, file_name)
        
        # Download file
        response = requests.get(url, stream=True, timeout=30)
        
        # Check status
        if response.status_code != 200:
            logger.error(f"Failed to download file from {url}: HTTP {response.status_code}")
            return None
        
        # Save file
        with open(output_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
        
        logger.info(f"Downloaded file: {output_path}")
        return output_path
    
    except Exception as e:
        logger.error(f"Error downloading file from {url}: {str(e)}")
        return None

def main():
    """
    Main function to test file extraction.
    """
    # Create a temporary directory for test files
    test_dir = tempfile.mkdtemp()
    logger.info(f"Created temporary directory: {test_dir}")
    
    try:
        # Initialize SimpleFileExtractor
        extractor = SimpleFileExtractor()
        
        # Test files URLs
        test_files = [
            # PDF
            "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf",
            # Markdown
            "https://raw.githubusercontent.com/mdn/content/main/README.md",
        ]
        
        # Download and test each file
        results = {}
        for url in test_files:
            logger.info(f"Testing file from URL: {url}")
            
            # Download file
            file_path = download_test_file(url, test_dir)
            if not file_path:
                logger.warning(f"Skipping test for {url}")
                continue
            
            # Extract text
            result = extractor.extract_text(file_path)
            
            # Log results
            if result.get("success"):
                text = result.get("text", "")
                logger.info(f"Successfully extracted text from {file_path}")
                logger.info(f"Text length: {len(text)} characters")
                logger.info(f"Text preview: {text[:200]}...")
            else:
                logger.warning(f"Failed to extract text from {file_path}: {result.get('error')}")
            
            # Store result
            file_type = os.path.splitext(file_path)[1].lower()
            results[file_type] = result.get("success", False)
        
        # Print summary
        logger.info("Test results summary:")
        for file_type, success in results.items():
            logger.info(f"  {file_type}: {'Success' if success else 'Failed'}")
        
        # Check if all tests passed
        all_passed = all(results.values())
        if all_passed:
            logger.info("All tests passed!")
        else:
            logger.warning("Some tests failed.")
        
        return all_passed
    
    except Exception as e:
        logger.error(f"Error during testing: {str(e)}")
        return False
    
    finally:
        # Clean up
        shutil.rmtree(test_dir)
        logger.info(f"Removed temporary directory: {test_dir}")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
