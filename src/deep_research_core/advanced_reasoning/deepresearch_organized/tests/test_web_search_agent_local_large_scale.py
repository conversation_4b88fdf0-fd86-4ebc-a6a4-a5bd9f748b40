#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test WebSearchAgentLocal với số lượng lớn truy vấn (10,000 truy vấn).
<PERSON><PERSON><PERSON> tra khả năng chịu tải và độ ổn định của WebSearchAgentLocal.
"""

import time
import logging
import random
import os
import sys
import threading
import queue
import argparse
import json
from concurrent.futures import ThreadPoolExecutor
from typing import List, Dict, Any

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Thêm thư mục gốc vào sys.path để import các module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal
except ImportError:
    try:
        # Thử import từ thư mục hiện tại
        from web_search_agent_local import WebSearchAgentLocal
    except ImportError:
        logger.error("Không thể import WebSearchAgentLocal. Vui lòng kiểm tra đường dẫn.")
        sys.exit(1)

def print_section(title):
    """In tiêu đề phần."""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, "="))
    print("=" * 80)

def generate_random_query() -> str:
    """Tạo truy vấn ngẫu nhiên."""
    topics = [
        "Python", "Java", "JavaScript", "C++", "Ruby", "Go", "Rust", "PHP", "Swift", "Kotlin",
        "Machine Learning", "Deep Learning", "Artificial Intelligence", "Data Science", "Big Data",
        "Web Development", "Mobile Development", "Cloud Computing", "DevOps", "Cybersecurity",
        "Blockchain", "Internet of Things", "Augmented Reality", "Virtual Reality", "Quantum Computing",
        "Lập trình", "Học máy", "Trí tuệ nhân tạo", "Khoa học dữ liệu", "Phát triển web",
        "Điện toán đám mây", "An ninh mạng", "Chuỗi khối", "Internet vạn vật", "Thực tế ảo"
    ]
    
    actions = [
        "tutorial", "guide", "introduction", "examples", "best practices",
        "vs", "comparison", "difference between", "pros and cons", "advantages",
        "how to", "tips", "tricks", "advanced", "beginner",
        "hướng dẫn", "giới thiệu", "ví dụ", "thực hành", "so sánh",
        "cách", "mẹo", "nâng cao", "cơ bản", "ứng dụng"
    ]
    
    topic = random.choice(topics)
    action = random.choice(actions)
    
    return f"{topic} {action}"

def generate_questions(num_questions: int) -> List[str]:
    """Tạo danh sách truy vấn ngẫu nhiên."""
    return [generate_random_query() for _ in range(num_questions)]

def search_worker(agent, query_queue, result_queue, delay=0.0):
    """Worker thực hiện tìm kiếm."""
    while True:
        try:
            # Lấy truy vấn từ hàng đợi
            query_data = query_queue.get(block=False)
            if query_data is None:
                break
            
            query_id, query = query_data
            
            # Thực hiện tìm kiếm
            start_time = time.time()
            try:
                result = agent.search(query, num_results=3)
                success = result.get('success', False)
                error = result.get('error', '') if not success else ''
                num_results = len(result.get('results', [])) if success else 0
            except Exception as e:
                success = False
                error = str(e)
                num_results = 0
                result = {"success": False, "error": error}
            
            end_time = time.time()
            
            # Đưa kết quả vào hàng đợi kết quả
            result_queue.put({
                "query_id": query_id,
                "query": query,
                "success": success,
                "error": error,
                "num_results": num_results,
                "time": end_time - start_time
            })
            
            # Delay để tránh quá tải
            if delay > 0:
                time.sleep(delay)
            
        except queue.Empty:
            break
        except Exception as e:
            logger.error(f"Lỗi trong search_worker: {str(e)}")
            break

def test_large_scale(num_queries=10000, num_workers=10, delay=0.5, output_file=None):
    """Test với số lượng lớn truy vấn."""
    print_section(f"Test với {num_queries} truy vấn, {num_workers} worker")
    
    # Khởi tạo WebSearchAgentLocal
    agent = WebSearchAgentLocal(
        search_method="auto",
        api_search_config={
            "engine": "searx",
            "searx_url": "http://localhost:8080",
            "language": "auto"
        },
        verbose=True
    )
    
    # Tạo danh sách truy vấn
    logger.info(f"Tạo {num_queries} truy vấn ngẫu nhiên...")
    queries = generate_questions(num_queries)
    
    # Tạo hàng đợi truy vấn và kết quả
    query_queue = queue.Queue()
    result_queue = queue.Queue()
    
    # Đưa truy vấn vào hàng đợi
    for i, query in enumerate(queries):
        query_queue.put((i, query))
    
    # Thêm None vào cuối hàng đợi để báo hiệu kết thúc
    for _ in range(num_workers):
        query_queue.put(None)
    
    # Tạo và khởi động các worker
    logger.info(f"Khởi động {num_workers} worker...")
    workers = []
    for _ in range(num_workers):
        worker = threading.Thread(target=search_worker, args=(agent, query_queue, result_queue, delay))
        worker.start()
        workers.append(worker)
    
    # Theo dõi tiến trình
    start_time = time.time()
    completed = 0
    total = num_queries
    
    while completed < total:
        # Kiểm tra số lượng kết quả đã hoàn thành
        completed = result_queue.qsize()
        
        # In tiến trình
        elapsed = time.time() - start_time
        rate = completed / elapsed if elapsed > 0 else 0
        eta = (total - completed) / rate if rate > 0 else 0
        
        print(f"\rTiến trình: {completed}/{total} ({completed/total*100:.2f}%) - Tốc độ: {rate:.2f} truy vấn/giây - ETA: {eta:.2f} giây", end="")
        
        # Chờ một chút
        time.sleep(1.0)
    
    print("\n")
    
    # Chờ tất cả worker hoàn thành
    for worker in workers:
        worker.join()
    
    # Thu thập kết quả
    results = []
    while not result_queue.empty():
        results.append(result_queue.get())
    
    # Sắp xếp kết quả theo query_id
    results.sort(key=lambda x: x["query_id"])
    
    # Thống kê
    total_time = sum(r["time"] for r in results)
    avg_time = total_time / len(results) if results else 0
    success_count = sum(1 for r in results if r["success"])
    failure_count = len(results) - success_count
    empty_results_count = sum(1 for r in results if r["success"] and r["num_results"] == 0)
    
    # In thống kê
    print(f"Tổng số truy vấn: {len(results)}")
    print(f"Thành công: {success_count} ({success_count/len(results)*100:.2f}%)")
    print(f"Thất bại: {failure_count} ({failure_count/len(results)*100:.2f}%)")
    print(f"Kết quả rỗng: {empty_results_count} ({empty_results_count/len(results)*100:.2f}%)")
    print(f"Thời gian trung bình: {avg_time:.2f} giây")
    print(f"Tổng thời gian: {total_time:.2f} giây")
    print(f"Thời gian thực tế: {time.time() - start_time:.2f} giây")
    print(f"Tốc độ trung bình: {len(results)/(time.time() - start_time):.2f} truy vấn/giây")
    
    # In chi tiết lỗi
    if failure_count > 0:
        print("\nChi tiết lỗi:")
        error_counts = {}
        for r in results:
            if not r["success"]:
                error = r["error"]
                error_counts[error] = error_counts.get(error, 0) + 1
        
        for error, count in error_counts.items():
            print(f"  - {error}: {count} lần")
    
    # Lưu kết quả vào file nếu cần
    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({
                "total": len(results),
                "success": success_count,
                "failure": failure_count,
                "empty_results": empty_results_count,
                "avg_time": avg_time,
                "total_time": total_time,
                "real_time": time.time() - start_time,
                "avg_rate": len(results)/(time.time() - start_time),
                "error_counts": error_counts if failure_count > 0 else {},
                "results": results
            }, f, indent=2)
        
        print(f"\nKết quả đã được lưu vào file: {output_file}")
    
    return {
        "total": len(results),
        "success": success_count,
        "failure": failure_count,
        "empty_results": empty_results_count,
        "avg_time": avg_time,
        "total_time": total_time
    }

def main():
    """Hàm chính."""
    parser = argparse.ArgumentParser(description="Test WebSearchAgentLocal với số lượng lớn truy vấn")
    parser.add_argument("--num-queries", type=int, default=10000, help="Số lượng truy vấn (mặc định: 10000)")
    parser.add_argument("--num-workers", type=int, default=10, help="Số lượng worker (mặc định: 10)")
    parser.add_argument("--delay", type=float, default=0.5, help="Độ trễ giữa các truy vấn (giây, mặc định: 0.5)")
    parser.add_argument("--output", type=str, help="File để lưu kết quả (JSON)")
    
    args = parser.parse_args()
    
    print_section("Kiểm tra WebSearchAgentLocal với số lượng lớn truy vấn")
    
    try:
        test_large_scale(
            num_queries=args.num_queries,
            num_workers=args.num_workers,
            delay=args.delay,
            output_file=args.output
        )
    except Exception as e:
        import traceback
        print(f"Lỗi: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
