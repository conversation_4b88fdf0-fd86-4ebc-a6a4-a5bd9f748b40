"""
Simple test script for Outcome-Based Reasoning.

This script demonstrates the basic usage of the OutcomeBasedReasoner implementation
in the Deep Research Core project.
"""

import sys
import os
import time
import json
from typing import Dict, Any

# Add the src directory to the path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), 'src')))

from deep_research_core.reasoning.formats.outcome_based import OutcomeBasedReasoner
from deep_research_core.utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

def test_basic_outcome_based():
    """Test basic Outcome-Based reasoning with default parameters."""
    # Initialize the Outcome-Based reasoner
    reasoner = OutcomeBasedReasoner(
        provider="openai",  # Change to your preferred provider
        temperature=0.7,
        num_outcomes=3,
        verbose=True
    )
    
    # A query that would benefit from outcome-based reasoning
    query = "What are the potential outcomes of implementing carbon tax policies in Vietnam?"
    
    # Perform reasoning
    logger.info(f"Starting Outcome-Based reasoning for query: {query}")
    start_time = time.time()
    
    result = reasoner.reason(query)
    
    end_time = time.time()
    logger.info(f"Outcome-Based reasoning completed in {end_time - start_time:.2f} seconds")
    
    # Print the result
    print("\n====== OUTCOME-BASED REASONING RESULT ======\n")
    print(f"Query: {query}")
    print(f"\nAnswer: {result['answer']}")
    print(f"\nNumber of outcomes generated: {len(result['outcomes'])}")
    print(f"\nReasoning time: {result['reasoning_time']:.2f} seconds")
    print(f"\nProvider: {result['provider']}")
    print(f"\nModel: {result['model']}")
    
    # Print the best outcome
    print("\n----- Best Outcome -----")
    best_outcome = result['best_outcome']
    print(f"Description: {best_outcome['description']}")
    print(f"Likelihood: {best_outcome['likelihood']}%")
    print(f"Confidence score: {best_outcome.get('confidence_score', 'N/A')}")
    
    return result

def test_outcome_based_with_context():
    """Test Outcome-Based reasoning with context."""
    # Initialize the Outcome-Based reasoner
    reasoner = OutcomeBasedReasoner(
        provider="openai",  # Change to your preferred provider
        temperature=0.7,
        num_outcomes=3,
        outcome_evaluation_depth=3,
        verbose=True
    )
    
    # Query and relevant context
    query = "What would be the potential outcomes of relocating residents from flood-prone areas in the Mekong Delta?"
    context = """
    The Mekong Delta in Vietnam:
    - Is home to approximately 17 million people
    - Produces about 50% of Vietnam's rice
    - Is experiencing severe flooding and erosion
    - Has seen increasing saltwater intrusion
    - Has many communities that have lived in the same areas for generations with strong cultural ties
    - Has limited government resources for relocation programs
    """
    
    # Perform reasoning
    logger.info(f"Starting Outcome-Based reasoning with context for query: {query}")
    start_time = time.time()
    
    result = reasoner.reason(query, context=context)
    
    end_time = time.time()
    logger.info(f"Outcome-Based reasoning with context completed in {end_time - start_time:.2f} seconds")
    
    # Print the result
    print("\n====== OUTCOME-BASED REASONING WITH CONTEXT RESULT ======\n")
    print(f"Query: {query}")
    print(f"\nContext: {context}")
    print(f"\nAnswer: {result['answer']}")
    
    # Print all outcomes
    print("\n----- All Outcomes -----")
    for i, outcome in enumerate(result['outcomes']):
        print(f"\nOutcome {i+1}:")
        print(f"Description: {outcome['description']}")
        print(f"Likelihood: {outcome['likelihood']}%")
        print(f"Confidence score: {outcome.get('confidence_score', 'N/A')}")
        
        # Print benefits and drawbacks
        if 'benefits' in outcome and isinstance(outcome['benefits'], list):
            print("Benefits:")
            for benefit in outcome['benefits'][:3]:  # Print first 3 benefits
                print(f"- {benefit}")
                
        if 'drawbacks' in outcome and isinstance(outcome['drawbacks'], list):
            print("Drawbacks:")
            for drawback in outcome['drawbacks'][:3]:  # Print first 3 drawbacks
                print(f"- {drawback}")
    
    return result

def test_vietnamese_language():
    """Test Outcome-Based reasoning in Vietnamese."""
    # Initialize the Outcome-Based reasoner with Vietnamese language setting
    reasoner = OutcomeBasedReasoner(
        provider="openai",  # Change to your preferred provider
        temperature=0.7,
        num_outcomes=3,
        language="vi",  # Set language to Vietnamese
        verbose=True
    )
    
    # A query in Vietnamese
    query = "Các kết quả tiềm năng của việc phát triển năng lượng mặt trời ở Việt Nam là gì?"
    
    # Perform reasoning
    logger.info(f"Starting Vietnamese Outcome-Based reasoning for query: {query}")
    start_time = time.time()
    
    result = reasoner.reason(query)
    
    end_time = time.time()
    logger.info(f"Vietnamese Outcome-Based reasoning completed in {end_time - start_time:.2f} seconds")
    
    # Print the result
    print("\n====== VIETNAMESE OUTCOME-BASED REASONING RESULT ======\n")
    print(f"Query: {query}")
    print(f"\nAnswer: {result['answer']}")
    print(f"\nNumber of outcomes generated: {len(result['outcomes'])}")
    print(f"\nReasoning time: {result['reasoning_time']:.2f} seconds")
    
    return result

if __name__ == "__main__":
    print("Testing basic Outcome-Based reasoning...")
    basic_result = test_basic_outcome_based()
    
    print("\n\nTesting Outcome-Based reasoning with context...")
    context_result = test_outcome_based_with_context()
    
    print("\n\nTesting Outcome-Based reasoning in Vietnamese...")
    vietnamese_result = test_vietnamese_language()
    
    print("\n\nAll tests completed!") 