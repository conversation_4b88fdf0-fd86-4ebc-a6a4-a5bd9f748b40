#!/usr/bin/env python3
"""
Test script để kiểm tra đầu ra thực tế của hàm search khi sử dụng Crawlee và Playwright.
"""

import sys
import os
import json
import time
from typing import Dict, Any

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

# Import directly
from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal

def save_results_to_json(results: Dict[str, Any], filename: str) -> None:
    """
    Lưu kết quả tìm kiếm vào file JSON.
    """
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print(f"Đ<PERSON> lưu kết quả vào file: {filename}")

def print_section(title: str) -> None:
    """
    In tiêu đề phần.
    """
    print("\n" + "=" * 80)
    print(title)
    print("=" * 80)

def print_result_summary(result: Dict[str, Any]) -> None:
    """
    In tóm tắt kết quả tìm kiếm.
    """
    if result.get('success'):
        results = result.get('results', [])
        print(f"Tìm thấy {len(results)} kết quả")
        
        for i, item in enumerate(results[:3]):  # Chỉ hiển thị 3 kết quả đầu tiên
            print(f"\nKết quả {i+1}:")
            print(f"  Tiêu đề: {item.get('title', '')}")
            print(f"  URL: {item.get('url', '')}")
            
            # Hiển thị điểm liên quan nếu có
            if 'relevance_score' in item:
                print(f"  Điểm liên quan: {item.get('relevance_score', 0):.2f}")
            
            # Hiển thị đoạn trích
            snippet = item.get('snippet', '')
            if snippet:
                print(f"  Đoạn trích: {snippet[:100]}..." if len(snippet) > 100 else f"  Đoạn trích: {snippet}")
            
            # Hiển thị độ dài nội dung
            content = item.get('content', '')
            if content:
                print(f"  Độ dài nội dung: {len(content)} ký tự")
                print(f"  Nội dung: {content[:150]}..." if len(content) > 150 else f"  Nội dung: {content}")
    else:
        print(f"Lỗi: {result.get('error', 'Unknown error')}")

def test_crawlee_search(agent: WebSearchAgentLocal, query: str, language: str = "auto", optimize: bool = False) -> Dict[str, Any]:
    """
    Kiểm tra tìm kiếm với Crawlee.
    """
    print_section(f"Tìm kiếm với Crawlee: '{query}'")
    
    start_time = time.time()
    result = agent.search(
        query=query,
        num_results=5,
        method="crawlee",
        language=language,
        get_content=True,
        force_refresh=True,
        optimize_for_llm=optimize,
        max_content_length=5000
    )
    end_time = time.time()
    
    print(f"Thời gian tìm kiếm: {end_time - start_time:.2f} giây")
    print_result_summary(result)
    
    return result

def main():
    """
    Hàm chính.
    """
    # Khởi tạo WebSearchAgentLocal
    agent = WebSearchAgentLocal(
        search_method="crawlee",
        crawlee_search_config={
            "max_depth": 2,
            "max_pages_per_url": 3,
            "max_urls": 5,
            "timeout": 60
        },
        content_extractor_config={
            "extract_links": True,
            "extract_images": False,
            "max_content_length": 10000,
            "timeout": 30
        },
        verbose=True
    )
    
    # Danh sách các câu hỏi để kiểm tra
    test_queries = [
        {
            "query": "Python programming for beginners",
            "language": "en",
            "filename": "results_python_programming.json"
        },
        {
            "query": "Symptoms of diabetes",
            "language": "en",
            "filename": "results_diabetes_symptoms.json"
        },
        {
            "query": "How to invest in stocks",
            "language": "en",
            "filename": "results_invest_stocks.json"
        },
        {
            "query": "Phương pháp học tiếng Anh hiệu quả",
            "language": "vi",
            "filename": "results_hoc_tieng_anh.json"
        },
        {
            "query": "Quantum computing explained",
            "language": "en",
            "filename": "results_quantum_computing.json"
        },
        {
            "query": "Capital of France",
            "language": "en",
            "filename": "results_capital_france.json"
        },
        {
            "query": "Tác động của biến đổi khí hậu đến nông nghiệp Việt Nam",
            "language": "vi",
            "filename": "results_bien_doi_khi_hau.json"
        }
    ]
    
    # Thực hiện tìm kiếm cho mỗi câu hỏi
    for test_case in test_queries:
        query = test_case["query"]
        language = test_case["language"]
        filename = test_case["filename"]
        
        # Tìm kiếm không tối ưu hóa
        results = test_crawlee_search(agent, query, language, optimize=False)
        save_results_to_json(results, f"raw_{filename}")
        
        # Tìm kiếm có tối ưu hóa
        results_optimized = test_crawlee_search(agent, query, language, optimize=True)
        save_results_to_json(results_optimized, f"optimized_{filename}")
        
        print("\nSo sánh kết quả:")
        print(f"- Kết quả gốc: {len(results.get('results', []))} kết quả")
        print(f"- Kết quả tối ưu: {len(results_optimized.get('results', []))} kết quả")
        
        # Tính tổng độ dài nội dung
        raw_content_length = sum(len(r.get('content', '')) for r in results.get('results', []))
        opt_content_length = sum(len(r.get('content', '')) for r in results_optimized.get('results', []))
        
        print(f"- Tổng độ dài nội dung gốc: {raw_content_length} ký tự")
        print(f"- Tổng độ dài nội dung tối ưu: {opt_content_length} ký tự")
        print(f"- Tỷ lệ nén: {opt_content_length/max(1, raw_content_length)*100:.1f}%")
        
        # Kiểm tra chất lượng
        if 'quality_metrics' in results_optimized:
            quality = results_optimized.get('quality_metrics', {})
            print("\nChất lượng kết quả tối ưu:")
            print(f"- Tổng thể: {quality.get('overall', 'N/A')}")
            
            count = quality.get('count', {})
            print(f"- Số lượng: {count.get('value', 'N/A')} ({count.get('quality', 'N/A')})")
            
            length = quality.get('content_length', {})
            print(f"- Độ dài: {length.get('value', 'N/A')} ký tự ({length.get('quality', 'N/A')})")
            
            diversity = quality.get('diversity', {})
            print(f"- Đa dạng: {diversity.get('value', 'N/A')} nguồn ({diversity.get('quality', 'N/A')})")
            
            relevance = quality.get('relevance', {})
            print(f"- Độ liên quan: {relevance.get('value', 'N/A'):.2f} ({relevance.get('quality', 'N/A')})")

if __name__ == "__main__":
    main()
