"""
Simple test script for modules in Deep Research Core.
"""

import os
import sys
from typing import Dict, List, Any, Optional

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_source_attribution():
    """Test the SourceAttribution module."""
    print("\n=== Testing Source Attribution ===\n")
    
    try:
        from src.deep_research_core.reasoning.source_attribution import SourceAttribution
        
        # Initialize
        attribution = SourceAttribution(
            citation_style="apa",
            track_token_level=True,
            language="en"
        )
        
        print("SourceAttribution imported and initialized successfully!")
        return True
    except Exception as e:
        print(f"Error testing Source Attribution: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_self_reflection():
    """Test the SelfReflection module."""
    print("\n=== Testing Self Reflection ===\n")
    
    try:
        from src.deep_research_core.reasoning.self_reflection import SelfReflection
        
        # Initialize
        reflection = SelfReflection(
            provider="openrouter",
            language="en"
        )
        
        print("SelfReflection imported and initialized successfully!")
        return True
    except Exception as e:
        print(f"Error testing Self Reflection: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_query_decomposition():
    """Test the MultiQueryDecomposition module."""
    print("\n=== Testing Multi Query Decomposition ===\n")
    
    try:
        from src.deep_research_core.reasoning.multi_query_decomposition import MultiQueryDecomposition
        
        # Initialize
        decomposer = MultiQueryDecomposition(
            provider="openrouter",
            language="en"
        )
        
        print("MultiQueryDecomposition imported and initialized successfully!")
        return True
    except Exception as e:
        print(f"Error testing Multi Query Decomposition: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_source_validator():
    """Test the MultiSourceValidator module."""
    print("\n=== Testing Multi Source Validator ===\n")
    
    try:
        from src.deep_research_core.reasoning.multi_source_validator import MultiSourceValidator
        
        # Initialize
        validator = MultiSourceValidator(
            provider="openrouter",
            language="en"
        )
        
        print("MultiSourceValidator imported and initialized successfully!")
        return True
    except Exception as e:
        print(f"Error testing Multi Source Validator: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_fact_checker():
    """Test the FactChecker module."""
    print("\n=== Testing Fact Checker ===\n")
    
    try:
        from src.deep_research_core.reasoning.fact_checker import FactChecker
        
        # Initialize
        fact_checker = FactChecker(
            provider="openrouter"
        )
        
        print("FactChecker imported and initialized successfully!")
        return True
    except Exception as e:
        print(f"Error testing Fact Checker: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_hybrid_search():
    """Test the HybridSearch module."""
    print("\n=== Testing Hybrid Search ===\n")
    
    try:
        from src.deep_research_core.reasoning.hybrid_search import HybridSearch
        
        # Initialize
        hybrid_search = HybridSearch(
            provider="openrouter",
            semantic_weight=0.7,
            keyword_weight=0.3
        )
        
        print("HybridSearch imported and initialized successfully!")
        return True
    except Exception as e:
        print(f"Error testing Hybrid Search: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_semantic_chunking():
    """Test the SemanticChunking module."""
    print("\n=== Testing Semantic Chunking ===\n")
    
    try:
        from src.deep_research_core.reasoning.semantic_chunking import SemanticChunking
        
        # Initialize
        chunker = SemanticChunking(
            provider="openrouter",
            min_chunk_size=100,
            max_chunk_size=500,
            overlap=20
        )
        
        print("SemanticChunking imported and initialized successfully!")
        return True
    except Exception as e:
        print(f"Error testing Semantic Chunking: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_knowledge_graph_rag():
    """Test the KnowledgeGraphRAG module."""
    print("\n=== Testing Knowledge Graph RAG ===\n")
    
    try:
        from src.deep_research_core.reasoning.knowledge_graph_rag import KnowledgeGraphRAG
        
        # Initialize
        kg_rag = KnowledgeGraphRAG(
            provider="openrouter",
            url="http://localhost:8080",  # Assuming Weaviate is running locally
            document_class_name="Document",
            entity_class_name="Entity",
            relation_class_name="Relation",
            create_schema=True
        )
        
        print("KnowledgeGraphRAG imported and initialized successfully!")
        return True
    except Exception as e:
        print(f"Error testing Knowledge Graph RAG: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_knowledge_graph_reasoner():
    """Test the KnowledgeGraphReasoner module."""
    print("\n=== Testing Knowledge Graph Reasoner ===\n")
    
    try:
        from src.deep_research_core.reasoning.knowledge_graph_reasoner import KnowledgeGraphReasoner
        
        # Initialize
        kg_reasoner = KnowledgeGraphReasoner(
            provider="openrouter",
            url="http://localhost:8080",  # Assuming Weaviate is running locally
            entity_class_name="Entity",
            relation_class_name="Relation",
            create_schema=True
        )
        
        print("KnowledgeGraphReasoner imported and initialized successfully!")
        return True
    except Exception as e:
        print(f"Error testing Knowledge Graph Reasoner: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_tree_of_thought():
    """Test the TreeOfThought module."""
    print("\n=== Testing Tree of Thought ===\n")
    
    try:
        from src.deep_research_core.reasoning.tot import TreeOfThought
        
        # Initialize
        tot = TreeOfThought(
            provider="openrouter",
            max_branches=3,
            max_depth=2
        )
        
        print("TreeOfThought imported and initialized successfully!")
        return True
    except Exception as e:
        print(f"Error testing Tree of Thought: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("=== Testing All Modules ===\n")
    
    # Track test results
    results = {}
    
    # Test Source Attribution
    results["SourceAttribution"] = test_source_attribution()
    
    # Test Self Reflection
    results["SelfReflection"] = test_self_reflection()
    
    # Test Multi Query Decomposition
    results["MultiQueryDecomposition"] = test_multi_query_decomposition()
    
    # Test Multi Source Validator
    results["MultiSourceValidator"] = test_multi_source_validator()
    
    # Test Fact Checker
    results["FactChecker"] = test_fact_checker()
    
    # Test Hybrid Search
    results["HybridSearch"] = test_hybrid_search()
    
    # Test Semantic Chunking
    results["SemanticChunking"] = test_semantic_chunking()
    
    # Test Knowledge Graph RAG
    results["KnowledgeGraphRAG"] = test_knowledge_graph_rag()
    
    # Test Knowledge Graph Reasoner
    results["KnowledgeGraphReasoner"] = test_knowledge_graph_reasoner()
    
    # Test Tree of Thought
    results["TreeOfThought"] = test_tree_of_thought()
    
    # Print summary
    print("\n=== Test Summary ===")
    for module, success in results.items():
        status = "PASSED" if success else "FAILED"
        print(f"{module}: {status}")
    
    # Return success if all tests passed
    return all(results.values())

if __name__ == "__main__":
    main()
