"""
Test script for web search imports.
"""

import sys
print(f"Python version: {sys.version}")
print("Testing web search imports...")

try:
    from src.deep_research_core.agents.query_analyzer import QueryAnalyzer
    print("✓ Module query_analyzer đã được import thành công")
except ImportError as e:
    print(f"✗ Không thể import module query_analyzer: {e}")

try:
    from src.deep_research_core.agents.document_extractors import HTMLExtractor, CSVExtractor, XMLExtractor
    print("✓ Module document_extractors đã được import thành công")
except ImportError as e:
    print(f"✗ Không thể import module document_extractors: {e}")

try:
    from src.deep_research_core.reasoning.integrated.web_search_rag_integration import WebSearchRAGIntegrator
    print("✓ Module web_search_rag_integration đã được import thành công")
except ImportError as e:
    print(f"✗ Không thể import module web_search_rag_integration: {e}")

print("Testing complete!")
