#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Kiểm tra ImprovedWebSearchAgent đã sửa lỗi 'int' object is not subscriptable.

Script này kiểm tra xem ImprovedWebSearchAgent có giải quyết được vấn đề
'int' object is not subscriptable khi các thuộc tính như engine_request_counts,
engine_reset_times và cache bị ghi đè bởi các giá trị không phải từ điển.
"""

import sys
import os
import time
import logging
from typing import Dict, Any

# Thiết lập logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Thêm đường dẫn đến thư mục gốc
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import WebSearchAgent và ImprovedWebSearchAgent
from deepresearch.src.deep_research_core.agents.web_search_agent import WebSearchAgent
from deepresearch.src.deep_research_core.agents.web_search_agent_improved import ImprovedWebSearchAgent

def print_section(title):
    """In tiêu đề phần."""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, "="))
    print("=" * 80 + "\n")

def test_original_agent_error():
    """Tái hiện lỗi 'int' object is not subscriptable trong WebSearchAgent gốc."""
    print_section("1. Tái hiện lỗi trong WebSearchAgent gốc")
    
    try:
        # Khởi tạo WebSearchAgent
        agent = WebSearchAgent(config={"api_search_config": {"engine": "duckduckgo"}})
        
        # Ghi đè thuộc tính engine_request_counts bằng một số nguyên
        agent.engine_request_counts = 0
        
        # Gọi phương thức _check_rate_limit - sẽ gây ra lỗi
        try:
            agent._check_rate_limit(wait_if_limited=True, engine="duckduckgo")
            print("KHÔNG CÓ LỖI - Không mong đợi điều này!")
            return False
        except TypeError as e:
            if "'int' object is not subscriptable" in str(e):
                print(f"Lỗi đã xảy ra như mong đợi: {str(e)}")
                return True
            else:
                print(f"Lỗi khác đã xảy ra: {str(e)}")
                return False
    except Exception as e:
        print(f"Lỗi không mong đợi: {str(e)}")
        return False

def test_improved_agent_fix():
    """Kiểm tra ImprovedWebSearchAgent đã sửa lỗi."""
    print_section("2. Kiểm tra ImprovedWebSearchAgent đã sửa lỗi")
    
    try:
        # Khởi tạo ImprovedWebSearchAgent
        agent = ImprovedWebSearchAgent(config={"api_search_config": {"engine": "duckduckgo"}})
        
        # Ghi đè thuộc tính engine_request_counts bằng một số nguyên
        agent.engine_request_counts = 0
        
        # Gọi phương thức _check_rate_limit - không nên gây ra lỗi
        try:
            agent._check_rate_limit(wait_if_limited=True, engine="duckduckgo")
            print("Không có lỗi - ImprovedWebSearchAgent đã sửa thành công!")
            
            # Kiểm tra xem engine_request_counts đã được khởi tạo lại chưa
            if isinstance(agent.engine_request_counts, dict):
                print("engine_request_counts đã được khởi tạo lại thành công!")
                return True
            else:
                print(f"engine_request_counts vẫn không phải là dict: {type(agent.engine_request_counts)}")
                return False
        except TypeError as e:
            if "'int' object is not subscriptable" in str(e):
                print(f"Lỗi vẫn xảy ra: {str(e)}")
                return False
            else:
                print(f"Lỗi khác đã xảy ra: {str(e)}")
                return False
    except Exception as e:
        print(f"Lỗi không mong đợi: {str(e)}")
        return False

def test_cache_error():
    """Tái hiện lỗi với cache trong WebSearchAgent gốc."""
    print_section("3. Tái hiện lỗi cache trong WebSearchAgent gốc")
    
    try:
        # Khởi tạo WebSearchAgent
        agent = WebSearchAgent(config={"api_search_config": {"engine": "duckduckgo"}})
        
        # Ghi đè thuộc tính cache bằng một số nguyên
        agent.cache = 0
        
        # Thử tìm kiếm - sẽ gây ra lỗi khi truy cập cache
        try:
            agent.search("test query", num_results=2, _test_only=True)
            print("KHÔNG CÓ LỖI - Không mong đợi điều này!")
            return False
        except AttributeError as e:
            if "'int' object has no attribute" in str(e):
                print(f"Lỗi đã xảy ra như mong đợi: {str(e)}")
                return True
            else:
                print(f"Lỗi khác đã xảy ra: {str(e)}")
                return False
    except Exception as e:
        print(f"Lỗi không mong đợi: {str(e)}")
        return False

def test_improved_cache_fix():
    """Kiểm tra ImprovedWebSearchAgent đã sửa lỗi cache."""
    print_section("4. Kiểm tra ImprovedWebSearchAgent đã sửa lỗi cache")
    
    try:
        # Khởi tạo ImprovedWebSearchAgent
        agent = ImprovedWebSearchAgent(config={"api_search_config": {"engine": "duckduckgo"}})
        
        # Ghi đè thuộc tính cache bằng một số nguyên
        agent.cache = 0
        
        # Thử tìm kiếm - không nên gây ra lỗi
        try:
            result = agent.search("test query", num_results=2, _test_only=True)
            print("Không có lỗi - ImprovedWebSearchAgent đã sửa thành công!")
            print(f"Kết quả: {result}")
            return True
        except Exception as e:
            print(f"Lỗi đã xảy ra: {str(e)}")
            return False
    except Exception as e:
        print(f"Lỗi không mong đợi: {str(e)}")
        return False

def main():
    """Chạy tất cả các bài kiểm tra."""
    print_section("KIỂM TRA SỬA LỖI 'int' object is not subscriptable")
    
    results = {
        "original_agent_error": test_original_agent_error(),
        "improved_agent_fix": test_improved_agent_fix(),
        "cache_error": test_cache_error(),
        "improved_cache_fix": test_improved_cache_fix()
    }
    
    print_section("KẾT QUẢ TỔNG HỢP")
    for test_name, success in results.items():
        print(f"{test_name}: {'THÀNH CÔNG' if success else 'THẤT BẠI'}")
    
    # Trả về thành công tổng thể
    return all(results.values())

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
