#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test toàn diện cho WebSearchAgentLocal.
<PERSON><PERSON> gồm các test case cho tất cả các tính năng của WebSearchAgentLocal.
"""

import time
import logging
import unittest
import random
import os
import sys
from unittest.mock import patch, MagicMock

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Thêm thư mục gốc vào sys.path để import các module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal
    from src.deep_research_core.utils.rate_limiter import RateLimitError
except ImportError:
    try:
        # Thử import từ thư mục hiện tại
        from web_search_agent_local import WebSearchAgentLocal
    except ImportError:
        logger.error("Không thể import WebSearchAgentLocal. Vui lòng kiểm tra đường dẫn.")
        sys.exit(1)

def print_section(title):
    """In tiêu đề phần."""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, "="))
    print("=" * 80)

def print_result(result):
    """In kết quả tìm kiếm."""
    if not result:
        print("Không có kết quả")
        return

    print(f"Thành công: {result.get('success', False)}")
    
    if not result.get('success', False):
        print(f"Lỗi: {result.get('error', 'Unknown error')}")
        return
    
    print(f"Truy vấn: {result.get('query', '')}")
    print(f"Phương thức tìm kiếm: {result.get('search_method', '')}")
    print(f"Số kết quả: {len(result.get('results', []))}")
    
    # In chi tiết kết quả đầu tiên
    if result.get('results'):
        first_result = result['results'][0]
        print("\nKết quả đầu tiên:")
        print(f"  - Tiêu đề: {first_result.get('title', 'N/A')}")
        print(f"  - URL: {first_result.get('url', 'N/A')}")
        
        # In snippet hoặc nội dung
        if 'content' in first_result:
            content = first_result['content']
            print(f"  - Nội dung: {content[:100]}..." if len(content) > 100 else f"  - Nội dung: {content}")
        elif 'snippet' in first_result:
            snippet = first_result['snippet']
            print(f"  - Snippet: {snippet[:100]}..." if len(snippet) > 100 else f"  - Snippet: {snippet}")

    # In đánh giá câu hỏi nếu có
    if 'question_evaluation' in result:
        qe = result['question_evaluation']
        print("\nĐánh giá câu hỏi:")
        print(f"  - Độ phức tạp: {qe.get('complexity_level', 'N/A')}")
        print(f"  - Điểm số: {qe.get('complexity_score', 'N/A')}")
    
    # In đánh giá câu trả lời nếu có
    if 'answer_evaluation' in result:
        ae = result['answer_evaluation']
        print("\nĐánh giá câu trả lời:")
        print(f"  - Chất lượng: {ae.get('overall_score', 'N/A')}")
        print(f"  - Cần tìm kiếm thêm: {ae.get('need_more_search', 'N/A')}")

class TestWebSearchAgentLocal(unittest.TestCase):
    """Test case cho WebSearchAgentLocal."""
    
    @classmethod
    def setUpClass(cls):
        """Thiết lập trước khi chạy tất cả các test."""
        print_section("Thiết lập test WebSearchAgentLocal")
        
        # Khởi tạo WebSearchAgentLocal với cấu hình mặc định
        cls.agent = WebSearchAgentLocal(
            search_method="auto",
            api_search_config={
                "engine": "searx",
                "searx_url": "http://localhost:8080",
                "language": "auto"
            },
            crawlee_search_config={
                "max_depth": 2,
                "max_pages_per_url": 3,
                "max_urls": 5,
                "timeout": 30
            },
            verbose=True
        )
        
        # Danh sách câu hỏi đơn giản
        cls.simple_queries = [
            "Thủ đô của Việt Nam",
            "Python programming language",
            "Capital of France",
            "Who is Albert Einstein",
            "Công thức nước là gì"
        ]
        
        # Danh sách câu hỏi phức tạp
        cls.complex_queries = [
            "So sánh chi tiết giữa React và Vue.js về hiệu suất và quản lý state",
            "Explain the differences between quantum computing and classical computing with examples",
            "Phân tích tác động của biến đổi khí hậu đến nông nghiệp Việt Nam trong 10 năm qua",
            "Compare and contrast the economic policies of the last three US presidents",
            "Giải thích chi tiết cách hoạt động của thuật toán transformer trong deep learning"
        ]
    
    def test_01_basic_search(self):
        """Test tìm kiếm cơ bản."""
        print_section("Test tìm kiếm cơ bản")
        
        # Chọn một câu hỏi đơn giản
        query = random.choice(self.simple_queries)
        print(f"Truy vấn: {query}")
        
        # Thực hiện tìm kiếm
        result = self.agent.search(query, num_results=3)
        print_result(result)
        
        # Kiểm tra kết quả
        self.assertTrue(result.get('success', False), "Tìm kiếm cơ bản không thành công")
        self.assertGreater(len(result.get('results', [])), 0, "Không có kết quả tìm kiếm")
    
    def test_02_search_with_content(self):
        """Test tìm kiếm với trích xuất nội dung."""
        print_section("Test tìm kiếm với trích xuất nội dung")
        
        # Chọn một câu hỏi đơn giản
        query = random.choice(self.simple_queries)
        print(f"Truy vấn: {query}")
        
        # Thực hiện tìm kiếm với get_content=True
        result = self.agent.search(query, num_results=2, get_content=True)
        print_result(result)
        
        # Kiểm tra kết quả
        self.assertTrue(result.get('success', False), "Tìm kiếm với trích xuất nội dung không thành công")
        
        # Kiểm tra xem có nội dung không
        if result.get('results'):
            has_content = 'content' in result['results'][0]
            self.assertTrue(has_content, "Không có nội dung trong kết quả")
    
    def test_03_cache_functionality(self):
        """Test chức năng cache."""
        print_section("Test chức năng cache")
        
        # Chọn một câu hỏi đơn giản
        query = random.choice(self.simple_queries)
        print(f"Truy vấn: {query}")
        
        # Tìm kiếm lần đầu
        start_time = time.time()
        result1 = self.agent.search(query, num_results=3)
        end_time = time.time()
        time1 = end_time - start_time
        print(f"Thời gian tìm kiếm lần 1: {time1:.2f} giây")
        
        # Tìm kiếm lần thứ hai (sử dụng cache)
        start_time = time.time()
        result2 = self.agent.search(query, num_results=3)
        end_time = time.time()
        time2 = end_time - start_time
        print(f"Thời gian tìm kiếm lần 2: {time2:.2f} giây")
        
        # Kiểm tra thời gian
        self.assertLess(time2, time1, "Cache không hoạt động hiệu quả")
        
        # Tìm kiếm lần thứ ba với force_refresh=True
        start_time = time.time()
        result3 = self.agent.search(query, num_results=3, force_refresh=True)
        end_time = time.time()
        time3 = end_time - start_time
        print(f"Thời gian tìm kiếm lần 3 (force_refresh): {time3:.2f} giây")
        
        # Kiểm tra thời gian
        self.assertGreater(time3, time2, "force_refresh không hoạt động")
    
    def test_04_complex_query(self):
        """Test tìm kiếm với câu hỏi phức tạp."""
        print_section("Test tìm kiếm với câu hỏi phức tạp")
        
        # Chọn một câu hỏi phức tạp
        query = random.choice(self.complex_queries)
        print(f"Truy vấn phức tạp: {query}")
        
        # Thực hiện tìm kiếm
        result = self.agent.search(
            query, 
            num_results=5, 
            get_content=True,
            evaluate_question=True
        )
        print_result(result)
        
        # Kiểm tra kết quả
        self.assertTrue(result.get('success', False), "Tìm kiếm với câu hỏi phức tạp không thành công")
        self.assertGreater(len(result.get('results', [])), 0, "Không có kết quả tìm kiếm")
        
        # Kiểm tra đánh giá câu hỏi
        self.assertIn('question_evaluation', result, "Không có đánh giá câu hỏi")
    
    def test_05_vietnamese_search(self):
        """Test tìm kiếm tiếng Việt."""
        print_section("Test tìm kiếm tiếng Việt")
        
        # Danh sách câu hỏi tiếng Việt
        vietnamese_queries = [
            "Thủ đô của Việt Nam",
            "Công thức nước là gì",
            "Lịch sử Việt Nam thời kỳ đổi mới",
            "Cách nấu phở bò truyền thống",
            "Danh sách các tỉnh thành Việt Nam"
        ]
        
        # Chọn một câu hỏi tiếng Việt
        query = random.choice(vietnamese_queries)
        print(f"Truy vấn tiếng Việt: {query}")
        
        # Thực hiện tìm kiếm
        result = self.agent.search(query, num_results=3, language="vi")
        print_result(result)
        
        # Kiểm tra kết quả
        self.assertTrue(result.get('success', False), "Tìm kiếm tiếng Việt không thành công")
        self.assertGreater(len(result.get('results', [])), 0, "Không có kết quả tìm kiếm tiếng Việt")

def run_all_tests():
    """Chạy tất cả các test."""
    unittest.main(argv=['first-arg-is-ignored'], exit=False)

def main():
    """Hàm chính."""
    print_section("Kiểm tra toàn diện WebSearchAgentLocal")
    
    try:
        run_all_tests()
    except Exception as e:
        import traceback
        print(f"Lỗi: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
