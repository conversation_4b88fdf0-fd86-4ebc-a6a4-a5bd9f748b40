#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from src.deep_research_core.agents.web_search_agent_real import WebSearchAgent
import time
import json

def print_results(result, max_snippet_length=100):
    print(f'Success: {result.get("success")}')
    print(f'Engine: {result.get("engine")}')
    print(f'Search method: {result.get("search_method")}')
    print(f'Số kết quả: {len(result.get("results", []))}')
    
    if result.get('results'):
        print('\nKết quả:')
        for i, res in enumerate(result.get('results', [])[:5]):  # Chỉ hiển thị 5 kết quả đầu tiên
            print(f'\n[{i+1}] {res.get("title")}')
            print(f'    URL: {res.get("url")}')
            snippet = res.get("snippet", "")
            if len(snippet) > max_snippet_length:
                snippet = snippet[:max_snippet_length] + '...'
            print(f'    Snippet: {snippet}')
    else:
        print('\nKhông có kết quả hoặc có lỗi:')
        print(f'Error: {result.get("error", "Không có lỗi cụ thể")}')

print('=== TEST 1: Tìm kiếm thông tin về Python ===')
agent = WebSearchAgent(
    search_method='api',
    api_search_config={'engine': 'searx', 'searx_url': 'http://localhost:8080'},
    verbose=True
)
result = agent.search('Python programming language', num_results=5)
print_results(result)

time.sleep(2)  # Tránh rate limit

print('\n=== TEST 2: Tìm kiếm thông tin về AI ===')
result = agent.search('artificial intelligence and machine learning', num_results=5)
print_results(result)

time.sleep(2)  # Tránh rate limit

print('\n=== TEST 3: Tìm kiếm thông tin về Việt Nam ===')
result = agent.search('Vietnam tourism and culture', num_results=5)
print_results(result)

time.sleep(2)  # Tránh rate limit

print('\n=== TEST 4: Tìm kiếm bằng tiếng Việt ===')
result = agent.search('lập trình python cơ bản', num_results=5, language='vi')
print_results(result)

time.sleep(2)  # Tránh rate limit

print('\n=== TEST 5: Tìm kiếm câu hỏi phức tạp ===')
result = agent.search('how does quantum computing differ from classical computing', num_results=5)
print_results(result)

print('\n=== TEST HOÀN THÀNH ===')
