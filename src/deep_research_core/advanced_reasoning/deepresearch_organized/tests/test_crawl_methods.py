#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for _crawl_with_requests and _extract_pdf_info methods in WebSearchAgentLocal.
"""

import sys
import os
import logging
import json
import time
from typing import Dict, List, Any, Optional

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import WebSearchAgentLocal
try:
    from deepresearch.web_search_agent_local import WebSearchAgentLocal
except ImportError:
    try:
        from web_search_agent_local import WebSearchAgentLocal
    except ImportError:
        try:
            from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal
        except ImportError:
            logger.error("Could not import WebSearchAgentLocal")
            sys.exit(1)

def save_results(results: Dict[str, Any], filename: str) -> None:
    """Save results to a file."""
    os.makedirs("test_results", exist_ok=True)
    with open(f"test_results/{filename}", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    logger.info(f"Results saved to test_results/{filename}")

def print_section(title: str) -> None:
    """Print a section title."""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, "="))
    print("=" * 80)

def test_crawl_with_requests(agent: WebSearchAgentLocal) -> None:
    """Test _crawl_with_requests method."""
    print_section("Testing _crawl_with_requests method")

    # Test URLs
    urls = [
        "https://www.python.org/",
        "https://thuvienphapluat.vn/page/tim-van-ban.aspx",
        "https://www.google.com/"
    ]

    results = {}
    for url in urls:
        print(f"\nCrawling URL: {url}")
        
        try:
            # Call _crawl_with_requests method
            start_time = time.time()
            crawl_result = agent._crawl_with_requests(url, timeout=30, include_html=False)
            end_time = time.time()
            
            # Print results
            print(f"Success: {crawl_result.get('success', False)}")
            print(f"Crawl time: {end_time - start_time:.2f} seconds")
            if crawl_result.get('success', False):
                print(f"Title: {crawl_result.get('title', '')}")
                content = crawl_result.get('text', '')
                print(f"Content: {content[:100]}..." if content and len(content) > 100 else f"Content: {content}")
                print(f"Number of links: {len(crawl_result.get('links', []))}")
                
            # Save results
            results[url] = {
                "result": crawl_result,
                "time": end_time - start_time
            }
        except Exception as e:
            print(f"Error: {str(e)}")
            results[url] = {
                "error": str(e)
            }
    
    # Save results to file
    save_results(results, "crawl_with_requests_results.json")

def test_extract_pdf_info(agent: WebSearchAgentLocal) -> None:
    """Test _extract_pdf_info method."""
    print_section("Testing _extract_pdf_info method")

    # Test PDF URLs
    pdf_urls = [
        "https://thuvienphapluat.vn/van-ban/Thuong-mai/Nghi-dinh-98-2020-ND-CP-xu-phat-vi-pham-hanh-chinh-hoat-dong-thuong-mai-bao-ve-nguoi-tieu-dung-450755.pdf",
        "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf",
        "https://www.africau.edu/images/default/sample.pdf"
    ]

    results = {}
    for url in pdf_urls:
        print(f"\nExtracting PDF info from: {url}")
        
        try:
            # Call _extract_pdf_info method
            start_time = time.time()
            pdf_result = agent._extract_pdf_info(url, timeout=30)
            end_time = time.time()
            
            # Print results
            print(f"Success: {pdf_result.get('success', False)}")
            print(f"Extraction time: {end_time - start_time:.2f} seconds")
            if pdf_result.get('success', False):
                print(f"Title: {pdf_result.get('title', '')}")
                content = pdf_result.get('text', '')
                print(f"Content: {content[:100]}..." if content and len(content) > 100 else f"Content: {content}")
                print(f"Metadata: {pdf_result.get('metadata', {})}")
                
            # Save results
            results[url] = {
                "result": pdf_result,
                "time": end_time - start_time
            }
        except Exception as e:
            print(f"Error: {str(e)}")
            results[url] = {
                "error": str(e)
            }
    
    # Save results to file
    save_results(results, "extract_pdf_info_results.json")

def main():
    """Main function."""
    print_section("Testing WebSearchAgentLocal crawl methods")
    
    # Initialize WebSearchAgentLocal
    agent = WebSearchAgentLocal(verbose=True)
    
    # Test _crawl_with_requests method
    test_crawl_with_requests(agent)
    
    # Test _extract_pdf_info method
    test_extract_pdf_info(agent)

if __name__ == "__main__":
    main()
