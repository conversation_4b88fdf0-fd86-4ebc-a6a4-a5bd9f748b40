#!/usr/bin/env python3
"""
Test for SearXNG-Crawlee integration
"""

from src.deep_research_core.agents.searxng_crawlee_integration import search_with_searxng_crawlee
from src.deep_research_core.agents.web_search_agent import WebSearchAgent
import time
import traceback
import argparse

def print_section(title):
    """Print a section title."""
    print("\n" + "=" * 80)
    print(title)
    print("=" * 80)

def print_result(result, max_results=3):
    """Print search result in a readable format."""
    if not result.get("success", False):
        print(f"Search failed: {result.get('error', 'Unknown error')}")
        if "detailed_errors" in result:
            print("Detailed errors:")
            for engine, error in result.get("detailed_errors", {}).items():
                print(f"  {engine}: {error}")
        return False
    
    results = result.get("results", [])
    print(f"Found {len(results)} results")
    
    if len(results) == 0:
        return False
    
    for i, item in enumerate(results[:max_results]):
        print(f"\nResult {i+1}:")
        print(f"Title: {item.get('title', '')}")
        print(f"URL: {item.get('url', '')}")
        print(f"Source: {item.get('source', '')}")
        snippet = item.get('content', item.get('snippet', ''))
        if snippet and len(snippet) > 200:
            snippet = snippet[:200] + "..."
        print(f"Snippet: {snippet}")
    
    return True

def test_searxng_crawlee_direct():
    """Test the SearXNG-Crawlee integration directly."""
    print_section("Testing SearXNG-Crawlee integration directly")
    
    # Simple query first
    print("\n--- Testing a simple query ---")
    query = "Python programming tutorial"
    
    try:
        result = search_with_searxng_crawlee(
            query=query,
            num_results=5,
            language="en",
            max_depth=1,
            max_pages=5,
            timeout=60,
            use_local_instance=True,
            fallback_to_public=True
        )
        print_result(result)
    except Exception as e:
        print(f"Error testing SearXNG-Crawlee integration: {str(e)}")
        traceback.print_exc()
    
    # Try a more complex query
    print("\n--- Testing a complex query ---")
    query = "Compare machine learning frameworks for natural language processing"
    
    try:
        result = search_with_searxng_crawlee(
            query=query,
            num_results=5,
            language="en",
            max_depth=2,
            max_pages=10,
            timeout=90,
            use_local_instance=True,
            fallback_to_public=True
        )
        print_result(result)
    except Exception as e:
        print(f"Error testing SearXNG-Crawlee with complex query: {str(e)}")
        traceback.print_exc()
    
    # Try a Vietnamese query
    print("\n--- Testing a Vietnamese query ---")
    query = "Hướng dẫn lập trình Python cơ bản"
    
    try:
        result = search_with_searxng_crawlee(
            query=query,
            num_results=5,
            language="vi",
            max_depth=1,
            max_pages=5,
            timeout=60,
            use_local_instance=True,
            fallback_to_public=True
        )
        print_result(result)
    except Exception as e:
        print(f"Error testing SearXNG-Crawlee with Vietnamese query: {str(e)}")
        traceback.print_exc()

def test_with_web_search_agent():
    """Test integration with WebSearchAgent."""
    print_section("Testing integration with WebSearchAgent")
    
    # Initialize WebSearchAgent with custom search method
    agent = WebSearchAgent(
        search_method="auto",
        verbose=True
    )
    
    # Monkey patch the WebSearchAgent to use our integrated method
    original_search_method = agent.search
    
    def patched_search(query, method=None, num_results=10, **kwargs):
        if method == "searxng_crawlee" or (method is None and agent.search_method == "auto"):
            print(f"Using SearXNG-Crawlee integration for query: {query}")
            return search_with_searxng_crawlee(
                query=query,
                num_results=num_results,
                language=kwargs.get("language", "en"),
                max_depth=2,
                max_pages=10,
                timeout=60,
                use_local_instance=True,
                fallback_to_public=True
            )
        else:
            return original_search_method(query, method, num_results, **kwargs)
    
    # Apply the monkey patch
    agent.search = patched_search
    
    # Test with a simple query
    print("\n--- Testing WebSearchAgent with simple query ---")
    query = "Python programming tutorial"
    
    try:
        result = agent.search(query, method="searxng_crawlee", num_results=5)
        print_result(result)
    except Exception as e:
        print(f"Error testing WebSearchAgent with SearXNG-Crawlee: {str(e)}")
        traceback.print_exc()
    
    # Test with auto method selection
    print("\n--- Testing WebSearchAgent with auto method selection ---")
    query = "Compare machine learning frameworks for natural language processing"
    
    try:
        result = agent.search(query, num_results=5)
        print_result(result)
    except Exception as e:
        print(f"Error testing WebSearchAgent with auto method: {str(e)}")
        traceback.print_exc()

def main():
    """Run all tests."""
    parser = argparse.ArgumentParser(description="Test SearXNG-Crawlee integration")
    parser.add_argument("--direct", action="store_true", help="Test direct integration only")
    parser.add_argument("--agent", action="store_true", help="Test WebSearchAgent integration only")
    parser.add_argument("--query", type=str, help="Custom query to test")
    
    args = parser.parse_args()
    
    if args.query:
        print_section(f"Testing custom query: {args.query}")
        result = search_with_searxng_crawlee(
            query=args.query,
            num_results=5,
            language="auto",
            max_depth=2,
            max_pages=10,
            timeout=90,
            use_local_instance=True,
            fallback_to_public=True
        )
        print_result(result, max_results=5)
        return
    
    if args.direct or not (args.direct or args.agent):
        test_searxng_crawlee_direct()
    
    if args.agent or not (args.direct or args.agent):
        test_with_web_search_agent()

if __name__ == "__main__":
    main()
