#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Direct test script for file download functionality.
"""

import os
import sys
import time
import hashlib
import mimetypes
import tempfile
import logging
import shutil
import requests
from typing import Dict, Any, List, Optional, Union, BinaryIO
from urllib.parse import urlparse, unquote

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleFileProcessor:
    """
    Simple file processor for testing.
    """
    
    def __init__(
        self,
        download_dir: Optional[str] = None,
        max_file_size: int = 10 * 1024 * 1024,  # 10MB
        timeout: int = 30,
        user_agent: Optional[str] = None,
        supported_extensions: Optional[List[str]] = None
    ):
        """
        Initialize SimpleFileProcessor.
        
        Args:
            download_dir: Directory to save downloaded files
            max_file_size: Maximum file size (bytes)
            timeout: Maximum timeout (seconds)
            user_agent: User-Agent to use
            supported_extensions: List of supported file extensions
        """
        self.download_dir = download_dir or tempfile.gettempdir()
        self.max_file_size = max_file_size
        self.timeout = timeout
        self.user_agent = user_agent or "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36"
        
        # Create download directory if it doesn't exist
        if not os.path.exists(self.download_dir):
            os.makedirs(self.download_dir)
        
        # List of supported file extensions
        self.supported_extensions = supported_extensions or [
            '.pdf', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx',
            '.txt', '.csv', '.json', '.xml', '.html', '.htm'
        ]
    
    def is_file_url(self, url: str) -> bool:
        """
        Check if URL is a link to a file.
        
        Args:
            url: URL to check
        
        Returns:
            bool: True if it's a file, False if not
        """
        # Check URL extension
        parsed_url = urlparse(url)
        path = unquote(parsed_url.path)
        
        # Check extension
        for ext in self.supported_extensions:
            if path.lower().endswith(ext):
                return True
        
        # Check if query parameters contain file
        if 'download' in parsed_url.query.lower() or 'file' in parsed_url.query.lower():
            return True
        
        # Check special URL patterns
        special_patterns = ['/download/', '/files/', '/documents/', '/attachments/']
        for pattern in special_patterns:
            if pattern in url.lower():
                return True
        
        return False
    
    def get_file_name_from_url(self, url: str) -> str:
        """
        Get file name from URL.
        
        Args:
            url: URL to get file name from
        
        Returns:
            str: File name
        """
        parsed_url = urlparse(url)
        path = unquote(parsed_url.path)
        
        # Get file name from path
        file_name = os.path.basename(path)
        
        # If no file name, create one from URL
        if not file_name or '.' not in file_name:
            file_name = f"file_{hashlib.md5(url.encode()).hexdigest()}"
            
            # Add extension based on Content-Type if available
            try:
                response = requests.head(url, timeout=5)
                content_type = response.headers.get('Content-Type', '')
                ext = mimetypes.guess_extension(content_type)
                if ext:
                    file_name += ext
            except Exception:
                pass
        
        # Remove invalid characters
        file_name = "".join(c for c in file_name if c.isalnum() or c in "._-")
        
        return file_name
    
    def download_file(
        self,
        url: str,
        output_path: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Download file from URL.
        
        Args:
            url: URL of the file
            output_path: Path to save the file
        
        Returns:
            Dict[str, Any]: Information about the downloaded file
        """
        # Create output path if not provided
        if not output_path:
            file_name = self.get_file_name_from_url(url)
            output_path = os.path.join(self.download_dir, file_name)
        
        # Initialize result
        result = {
            "success": False,
            "url": url,
            "file_path": output_path,
            "file_name": os.path.basename(output_path),
            "content_type": None,
            "size": 0,
            "text_content": None,
            "download_time": time.time()
        }
        
        try:
            # Use requests
            headers = {
                'User-Agent': self.user_agent
            }
            
            # Download file
            response = requests.get(
                url,
                headers=headers,
                stream=True,
                timeout=self.timeout
            )
            
            # Check status
            if response.status_code != 200:
                logger.warning(f"Error downloading file from {url}: HTTP {response.status_code}")
                result["error"] = f"HTTP error: {response.status_code}"
                return result
            
            # Get Content-Type
            content_type = response.headers.get('Content-Type', '')
            result["content_type"] = content_type
            
            # Save file
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            # Update size
            result["size"] = os.path.getsize(output_path)
            
            # Extract text content if it's a text file
            if content_type.startswith('text/'):
                try:
                    with open(output_path, 'r', encoding='utf-8') as f:
                        result["text_content"] = f.read()
                except UnicodeDecodeError:
                    pass
            
            result["success"] = True
            logger.info(f"Downloaded file: {result['file_name']} ({result['size']} bytes)")
            
            return result
            
        except Exception as e:
            logger.warning(f"Error downloading file from {url}: {str(e)}")
            result["error"] = str(e)
            return result

def main():
    """
    Main function to test file download.
    """
    # Create a temporary directory for downloaded files
    download_dir = "test_downloads"
    if not os.path.exists(download_dir):
        os.makedirs(download_dir)
    
    try:
        # Initialize SimpleFileProcessor
        file_processor = SimpleFileProcessor(
            download_dir=download_dir
        )
        
        # Test is_file_url method
        logger.info("Testing is_file_url method...")
        
        # Test URLs that should be detected as files
        file_urls = [
            "https://www.example.com/documents/sample.pdf",
            "https://www.example.com/files/data.xlsx",
            "https://www.example.com/download/presentation.pptx",
            "https://www.example.com/attachments/document.docx",
            "https://www.example.com/file.txt",
            "https://www.example.com/download?file=report.pdf",
        ]
        
        # Test URLs that should not be detected as files
        non_file_urls = [
            "https://www.example.com",
            "https://www.example.com/about",
            "https://www.example.com/contact-us",
            "https://www.example.com/blog/post-1",
        ]
        
        # Check file URLs
        for url in file_urls:
            result = file_processor.is_file_url(url)
            logger.info(f"URL: {url}, Is file: {result}")
            assert result, f"URL {url} should be detected as a file URL"
        
        # Check non-file URLs
        for url in non_file_urls:
            result = file_processor.is_file_url(url)
            logger.info(f"URL: {url}, Is file: {result}")
            assert not result, f"URL {url} should not be detected as a file URL"
        
        # Test downloading a file
        logger.info("Testing download_file method...")
        
        # URL of a PDF file
        pdf_url = "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"
        
        # Download the file
        result = file_processor.download_file(pdf_url)
        
        # Check the result
        assert result["success"], "Download should be successful"
        assert result["file_path"], "File path should be provided"
        assert os.path.exists(result["file_path"]), "File should exist on disk"
        
        # Log file information
        logger.info(f"Downloaded file: {result['file_path']}")
        logger.info(f"File size: {result['size']} bytes")
        logger.info(f"Content type: {result['content_type']}")
        
        # If text content was extracted, check it
        if "text_content" in result and result["text_content"]:
            logger.info(f"Text content length: {len(result['text_content'])}")
            assert result["text_content"], "Text content should be extracted"
        
        logger.info("All tests passed!")
        return True
    
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        return False
    
    finally:
        # Clean up
        if os.path.exists(download_dir):
            shutil.rmtree(download_dir)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
