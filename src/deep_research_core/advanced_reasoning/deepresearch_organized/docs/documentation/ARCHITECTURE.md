# Kiến trúc WebSearchAgentLocal

Tài liệu này mô tả kiến trúc chi tiết của WebSearchAgentLocal, bao gồm các thành phần, lu<PERSON><PERSON> dữ liệu, và cách các module tương tác với nhau.

## <PERSON>ụ<PERSON> lục

1. [Tổng quan kiến trúc](#tổng-quan-kiến-trúc)
2. [<PERSON><PERSON><PERSON> thành phần chính](#các-thành-phần-chính)
   - [WebSearchAgentLocal](#websearchagentlocal)
   - [Search Methods](#search-methods)
   - [Query Analysis](#query-analysis)
   - [Content Extraction](#content-extraction)
   - [Caching](#caching)
   - [Rate Limiting](#rate-limiting)
   - [CAPTCHA Handling](#captcha-handling)
   - [Result Ranking](#result-ranking)
   - [Performance Optimization](#performance-optimization)
   - [Plugin System](#plugin-system)
3. [Luồng dữ liệu](#luồng-dữ-liệu)
4. [<PERSON>ô hình dữ liệu](#mô-hình-dữ-liệu)
5. [Mở rộng và tùy chỉnh](#mở-rộng-và-tùy-chỉnh)

## Tổng quan kiến trúc

WebSearchAgentLocal được thiết kế với kiến trúc module hóa, cho phép dễ dàng mở rộng và tùy chỉnh. Kiến trúc này bao gồm các thành phần chính sau:

```
+---------------------------+
|    WebSearchAgentLocal    |
+---------------------------+
            |
            v
+---------------------------+
|      Query Analysis       |<----+
+---------------------------+     |
            |                     |
            v                     |
+---------------------------+     |
|     Search Methods        |     |
+---------------------------+     |
            |                     |
            v                     |
+---------------------------+     |
|    Content Extraction     |     |
+---------------------------+     |
            |                     |
            v                     |
+---------------------------+     |
|     Result Ranking        |     |
+---------------------------+     |
            |                     |
            v                     |
+---------------------------+     |
|         Caching           |-----+
+---------------------------+
```

## Các thành phần chính

### WebSearchAgentLocal

WebSearchAgentLocal là lớp chính quản lý tìm kiếm và trích xuất nội dung. Nó kết hợp các thành phần khác để cung cấp một API đơn giản cho người dùng.

**Trách nhiệm chính:**
- Khởi tạo và quản lý các thành phần khác
- Cung cấp API tìm kiếm và trích xuất nội dung
- Xử lý lỗi và trả về kết quả theo định dạng chuẩn
- Quản lý cấu hình và tùy chọn

**Các phương thức chính:**
- `search()`: Tìm kiếm với truy vấn
- `search_vietnamese()`: Tìm kiếm tiếng Việt
- `extract_content()`: Trích xuất nội dung từ URL
- `register_plugin()`: Đăng ký plugin

### Search Methods

Module này cung cấp các phương thức tìm kiếm khác nhau, bao gồm tìm kiếm với SearXNG, Crawlee, và các phương thức chuyên biệt cho tiếng Việt.

**Các thành phần chính:**
- `search_searxng`: Tìm kiếm với SearXNG local
- `search_crawlee`: Tìm kiếm với Crawlee
- `search_vietnamese`: Tìm kiếm tiếng Việt
- `search_specialized`: Tìm kiếm chuyên biệt cho các lĩnh vực cụ thể

**Luồng dữ liệu:**
1. Nhận truy vấn từ WebSearchAgentLocal
2. Thực hiện tìm kiếm với phương thức tương ứng
3. Trả về kết quả theo định dạng chuẩn

### Query Analysis

Module này phân tích truy vấn để xác định phương thức tìm kiếm tối ưu và cải thiện kết quả tìm kiếm.

**Các thành phần chính:**
- `LocalQueryAnalyzer`: Phân tích truy vấn
- `QueryDecomposer`: Phân tách truy vấn phức tạp
- `QuestionComplexityEvaluator`: Đánh giá độ phức tạp của câu hỏi

**Luồng dữ liệu:**
1. Nhận truy vấn từ WebSearchAgentLocal
2. Phân tích truy vấn để xác định loại, độ phức tạp, lĩnh vực, và ngôn ngữ
3. Trả về kết quả phân tích để WebSearchAgentLocal chọn phương thức tìm kiếm tối ưu

### Content Extraction

Module này trích xuất nội dung từ các trang web, bao gồm tiêu đề, nội dung, liên kết, và hình ảnh.

**Các thành phần chính:**
- `AsyncContentExtractor`: Trích xuất nội dung bất đồng bộ
- `SpecializedExtractors`: Các trình trích xuất chuyên biệt cho các loại trang web khác nhau
- `ContentCleaner`: Làm sạch nội dung trích xuất

**Luồng dữ liệu:**
1. Nhận URL từ WebSearchAgentLocal
2. Tải nội dung trang web
3. Trích xuất thông tin cần thiết
4. Làm sạch và định dạng nội dung
5. Trả về nội dung đã trích xuất

### Caching

Module này quản lý cache kết quả tìm kiếm để tăng tốc độ và giảm số lượng request.

**Các thành phần chính:**
- `EnhancedWebSearchCache`: Cache kết quả tìm kiếm với nhiều cấp độ
- `CacheKey`: Tạo khóa cache từ truy vấn và tham số
- `CacheItem`: Đại diện cho một mục trong cache

**Luồng dữ liệu:**
1. Nhận truy vấn và tham số từ WebSearchAgentLocal
2. Tạo khóa cache
3. Kiểm tra xem kết quả có trong cache không
4. Nếu có, trả về kết quả từ cache
5. Nếu không, thực hiện tìm kiếm và lưu kết quả vào cache

### Rate Limiting

Module này giới hạn tốc độ tìm kiếm để tránh bị chặn và đảm bảo sử dụng tài nguyên hiệu quả.

**Các thành phần chính:**
- `AdaptiveRateLimiter`: Tự động điều chỉnh tốc độ tìm kiếm
- `ExponentialBackoffRateLimiter`: Giới hạn tốc độ với backoff theo cấp số nhân

**Luồng dữ liệu:**
1. Nhận yêu cầu tìm kiếm từ WebSearchAgentLocal
2. Kiểm tra xem có thể thực hiện tìm kiếm không
3. Nếu có, cho phép tìm kiếm
4. Nếu không, trì hoãn tìm kiếm hoặc trả về lỗi

### CAPTCHA Handling

Module này phát hiện và xử lý CAPTCHA khi tìm kiếm hoặc trích xuất nội dung.

**Các thành phần chính:**
- `CaptchaHandler`: Phát hiện và xử lý CAPTCHA
- `CaptchaDetector`: Phát hiện CAPTCHA trong trang web
- `CaptchaSolver`: Giải CAPTCHA (nếu có thể)

**Luồng dữ liệu:**
1. Nhận phản hồi từ trang web
2. Kiểm tra xem có CAPTCHA không
3. Nếu có, thử giải CAPTCHA hoặc thay đổi chiến lược tìm kiếm
4. Nếu không, tiếp tục xử lý phản hồi

### Result Ranking

Module này đánh giá và xếp hạng kết quả tìm kiếm dựa trên nhiều yếu tố.

**Các thành phần chính:**
- `EnhancedResultRanker`: Đánh giá và xếp hạng kết quả
- `RelevanceScorer`: Tính điểm liên quan
- `DomainTrustScorer`: Tính điểm tin cậy của domain
- `ContentQualityScorer`: Tính điểm chất lượng nội dung
- `FreshnessScorer`: Tính điểm độ mới của nội dung

**Luồng dữ liệu:**
1. Nhận kết quả tìm kiếm từ WebSearchAgentLocal
2. Tính điểm cho mỗi kết quả dựa trên nhiều yếu tố
3. Sắp xếp kết quả theo điểm giảm dần
4. Trả về kết quả đã xếp hạng

### Performance Optimization

Module này tối ưu hóa hiệu suất của WebSearchAgentLocal, bao gồm sử dụng bộ nhớ, CPU, và xử lý bất đồng bộ.

**Các thành phần chính:**
- `PerformanceMonitor`: Giám sát hiệu suất
- `AsyncSearchExecutor`: Thực hiện tìm kiếm bất đồng bộ
- `ResourceLimiter`: Giới hạn sử dụng tài nguyên
- `AdaptiveParameterManager`: Tự động điều chỉnh tham số

**Luồng dữ liệu:**
1. Giám sát hiệu suất của WebSearchAgentLocal
2. Tự động điều chỉnh tham số để tối ưu hóa hiệu suất
3. Giới hạn sử dụng tài nguyên để tránh quá tải

### Plugin System

Module này cho phép mở rộng chức năng của WebSearchAgentLocal với các plugin.

**Các thành phần chính:**
- `PluginManager`: Quản lý plugin
- `Plugin`: Giao diện cho plugin
- `PluginHook`: Điểm kết nối cho plugin

**Luồng dữ liệu:**
1. Đăng ký plugin với WebSearchAgentLocal
2. WebSearchAgentLocal gọi plugin tại các điểm kết nối
3. Plugin xử lý dữ liệu và trả về kết quả

## Luồng dữ liệu

Dưới đây là luồng dữ liệu chính trong WebSearchAgentLocal:

1. **Tìm kiếm**:
   ```
   User -> WebSearchAgentLocal.search() -> Check Cache -> Query Analysis -> 
   Choose Search Method -> Execute Search -> Process Results -> 
   Rank Results -> Cache Results -> Return Results
   ```

2. **Trích xuất nội dung**:
   ```
   User -> WebSearchAgentLocal.extract_content() -> Check Cache -> 
   Choose Extractor -> Execute Extraction -> Process Content -> 
   Cache Content -> Return Content
   ```

3. **Tìm kiếm tiếng Việt**:
   ```
   User -> WebSearchAgentLocal.search_vietnamese() -> Check Cache -> 
   Choose Vietnamese Search Method -> Execute Search -> Process Results -> 
   Rank Results -> Cache Results -> Return Results
   ```

## Mô hình dữ liệu

### Kết quả tìm kiếm

```json
{
  "success": true,
  "query": "Python programming",
  "results": [
    {
      "title": "Python Programming Language",
      "url": "https://www.python.org/",
      "snippet": "Python is a programming language that lets you work quickly and integrate systems more effectively.",
      "content": "...",
      "relevance_score": 0.95,
      "domain_trust_score": 0.9,
      "content_quality_score": 0.85,
      "freshness_score": 0.8,
      "final_score": 0.9
    },
    ...
  ],
  "engine": "searxng",
  "search_method": "searxng",
  "timestamp": 1625097600,
  "execution_time": 0.5
}
```

### Nội dung trích xuất

```json
{
  "success": true,
  "url": "https://www.python.org/",
  "title": "Python Programming Language",
  "content": "...",
  "html": "...",
  "language": "en",
  "links": [
    {
      "text": "Download",
      "url": "https://www.python.org/downloads/"
    },
    ...
  ],
  "images": [
    {
      "alt": "Python Logo",
      "src": "https://www.python.org/static/img/python-logo.png"
    },
    ...
  ],
  "timestamp": 1625097600,
  "execution_time": 0.5
}
```

## Mở rộng và tùy chỉnh

WebSearchAgentLocal được thiết kế để dễ dàng mở rộng và tùy chỉnh. Dưới đây là một số cách để mở rộng WebSearchAgentLocal:

### Thêm phương thức tìm kiếm mới

```python
def search_custom(query, num_results=10, **kwargs):
    """
    Phương thức tìm kiếm tùy chỉnh.
    """
    # Thực hiện tìm kiếm
    # ...
    
    # Trả về kết quả
    return {
        "success": True,
        "query": query,
        "results": results,
        "engine": "custom",
        "search_method": "custom",
        "timestamp": time.time(),
        "execution_time": execution_time
    }

# Thêm phương thức tìm kiếm vào WebSearchAgentLocal
WebSearchAgentLocal._search_custom = search_custom
```

### Thêm trình trích xuất chuyên biệt

```python
from deepresearch.src.deep_research_core.utils.specialized_extractors import BaseExtractor

class CustomExtractor(BaseExtractor):
    """
    Trình trích xuất tùy chỉnh.
    """
    
    @classmethod
    def can_handle(cls, url):
        """
        Kiểm tra xem trình trích xuất có thể xử lý URL không.
        """
        return "example.com" in url
    
    def extract(self, html, url, **kwargs):
        """
        Trích xuất nội dung từ HTML.
        """
        # Trích xuất nội dung
        # ...
        
        # Trả về nội dung
        return {
            "title": title,
            "content": content,
            "language": language,
            "links": links,
            "images": images
        }

# Đăng ký trình trích xuất
from deepresearch.src.deep_research_core.utils.specialized_extractors import register_extractor
register_extractor(CustomExtractor)
```

### Tạo plugin

```python
from deepresearch.src.deep_research_core.plugins import Plugin

class CustomPlugin(Plugin):
    """
    Plugin tùy chỉnh.
    """
    
    def __init__(self, config=None):
        """
        Khởi tạo plugin.
        """
        self.config = config or {}
    
    def on_search_start(self, query, **kwargs):
        """
        Xử lý trước khi tìm kiếm.
        """
        # Xử lý truy vấn
        # ...
        
        return query, kwargs
    
    def on_search_end(self, results, **kwargs):
        """
        Xử lý sau khi tìm kiếm.
        """
        # Xử lý kết quả
        # ...
        
        return results

# Đăng ký plugin
agent.register_plugin("custom_plugin", {
    "enabled": True,
    "config": {
        "option1": "value1",
        "option2": "value2"
    }
})
```
