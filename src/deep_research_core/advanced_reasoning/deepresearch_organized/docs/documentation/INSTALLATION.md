# Hướng dẫn cài đặt WebSearchAgentLocal

Tài liệu này cung cấp hướng dẫn chi tiết về cách cài đặt và cấu hình WebSearchAgentLocal và các thành phần phụ thuộc.

## <PERSON><PERSON><PERSON> lục

1. [<PERSON><PERSON><PERSON> cầu hệ thống](#yêu-cầu-hệ-thống)
2. [Cài đặt SearXNG](#cài-đặt-searxng)
   - [Cài đặt với Docker](#cài-đặt-với-docker)
   - [Cài đặt thủ công](#cài-đặt-thủ-công)
   - [Cấu hình SearXNG](#cấu-hình-searxng)
3. [<PERSON><PERSON><PERSON> đặt Playwright](#cài-đặt-playwright)
4. [<PERSON><PERSON>i đặt các thư viện Python](#cài-đặt-các-thư-viện-python)
5. [<PERSON>ài đặt WebSearchAgentLocal](#cài-đặt-websearchagentlocal)
6. [<PERSON><PERSON><PERSON> tra cài đặt](#kiểm-tra-cài-đặt)
7. [<PERSON><PERSON> lý sự cố](#xử-lý-sự-cố)

## Yêu cầu hệ thống

- **Hệ điều hành**: Linux, macOS, hoặc Windows
- **Python**: 3.8 hoặc cao hơn
- **Docker**: 19.03 hoặc cao hơn (nếu sử dụng Docker để cài đặt SearXNG)
- **RAM**: Tối thiểu 4GB, khuyến nghị 8GB
- **Dung lượng đĩa**: Tối thiểu 2GB cho SearXNG và các thư viện Python

## Cài đặt SearXNG

### Cài đặt với Docker

Cách đơn giản nhất để cài đặt SearXNG là sử dụng Docker:

1. **Cài đặt Docker** (nếu chưa có):
   - Ubuntu/Debian:
     ```bash
     sudo apt update
     sudo apt install docker.io docker-compose
     sudo systemctl enable --now docker
     ```
   - macOS: Tải và cài đặt [Docker Desktop](https://www.docker.com/products/docker-desktop)
   - Windows: Tải và cài đặt [Docker Desktop](https://www.docker.com/products/docker-desktop)

2. **Tải image SearXNG**:
   ```bash
   docker pull searxng/searxng
   ```

3. **Tạo thư mục cấu hình**:
   ```bash
   mkdir -p searxng
   ```

4. **Chạy container SearXNG**:
   ```bash
   docker run -d \
     --name searxng \
     -p 8080:8080 \
     -v "${PWD}/searxng:/etc/searxng" \
     -e "BASE_URL=http://localhost:8080/" \
     searxng/searxng
   ```

5. **Kiểm tra SearXNG đã chạy chưa**:
   ```bash
   docker ps | grep searxng
   ```
   
   Bạn cũng có thể truy cập http://localhost:8080 trong trình duyệt để kiểm tra.

### Cài đặt thủ công

Nếu bạn không muốn sử dụng Docker, bạn có thể cài đặt SearXNG thủ công:

1. **Cài đặt các gói phụ thuộc**:
   - Ubuntu/Debian:
     ```bash
     sudo apt update
     sudo apt install -y python3-pip python3-venv git build-essential libxslt-dev python3-dev libffi-dev libssl-dev zlib1g-dev
     ```

2. **Tải mã nguồn SearXNG**:
   ```bash
   git clone https://github.com/searxng/searxng.git
   cd searxng
   ```

3. **Tạo và kích hoạt môi trường ảo**:
   ```bash
   python3 -m venv venv
   source venv/bin/activate
   ```

4. **Cài đặt các gói Python cần thiết**:
   ```bash
   pip install -e .
   ```

5. **Tạo cấu hình**:
   ```bash
   sed -i -e "s/ultrasecretkey/$(openssl rand -hex 16)/g" searx/settings.yml
   ```

6. **Chạy SearXNG**:
   ```bash
   python -m searx.webapp
   ```

   SearXNG sẽ chạy tại http://localhost:8888

### Cấu hình SearXNG

Để cấu hình SearXNG, tạo hoặc chỉnh sửa file `settings.yml` trong thư mục cấu hình:

1. **Nếu sử dụng Docker**:
   ```bash
   nano searxng/settings.yml
   ```

2. **Nếu cài đặt thủ công**:
   ```bash
   nano searx/settings.yml
   ```

3. **Thêm cấu hình sau**:
   ```yaml
   use_default_settings: true

   server:
     secret_key: "ultrasecretkey"  # Thay đổi thành một khóa bí mật
     limiter: false
     public_instance: false
     image_proxy: true

   ui:
     static_use_hash: true
     default_theme: simple
     theme_args:
       simple_style: auto
     query_in_title: true
     infinite_scroll: false
     center_alignment: false
     results_on_new_tab: false
     advanced_search: false
     num_results: 10

   search:
     safe_search: 0
     autocomplete: "google"
     autocomplete_min: 4
     default_lang: "vi"  # Thay đổi ngôn ngữ mặc định nếu cần

   redis:
     url: redis://redis:6379/0

   enabled_plugins:
     - 'Hash plugin'
     - 'Search on category select'
     - 'Self Informations'
     - 'Tracker URL remover'
     - 'Vim-like hotkeys'
   ```

4. **Khởi động lại SearXNG**:
   - Nếu sử dụng Docker:
     ```bash
     docker restart searxng
     ```
   - Nếu cài đặt thủ công:
     ```bash
     # Dừng quá trình hiện tại (Ctrl+C) và chạy lại
     python -m searx.webapp
     ```

## Cài đặt Playwright

Playwright là một thư viện tự động hóa trình duyệt được sử dụng bởi WebSearchAgentLocal để trích xuất nội dung từ các trang web:

1. **Cài đặt Playwright**:
   ```bash
   pip install playwright
   ```

2. **Cài đặt trình duyệt**:
   ```bash
   python -m playwright install chromium
   ```

3. **Kiểm tra cài đặt**:
   ```bash
   python -m playwright --version
   ```

## Cài đặt các thư viện Python

WebSearchAgentLocal yêu cầu một số thư viện Python để hoạt động:

1. **Cài đặt các thư viện cơ bản**:
   ```bash
   pip install requests beautifulsoup4 langdetect
   ```

2. **Cài đặt các thư viện cho tính năng nâng cao**:
   ```bash
   pip install psutil asyncio aiohttp
   ```

3. **Cài đặt các thư viện tùy chọn**:
   ```bash
   pip install lxml html5lib chardet
   ```

## Cài đặt WebSearchAgentLocal

1. **Tải mã nguồn**:
   ```bash
   git clone https://github.com/yourusername/deepresearch.git
   cd deepresearch
   ```

2. **Cài đặt gói**:
   ```bash
   pip install -e .
   ```

   Hoặc nếu bạn chỉ muốn sử dụng mà không cài đặt:
   ```bash
   # Đảm bảo thư mục deepresearch nằm trong PYTHONPATH
   export PYTHONPATH=$PYTHONPATH:$(pwd)
   ```

## Kiểm tra cài đặt

Để kiểm tra xem WebSearchAgentLocal đã được cài đặt đúng cách chưa, hãy chạy script kiểm tra:

1. **Tạo script kiểm tra**:
   ```bash
   nano test_websearch.py
   ```

2. **Thêm nội dung sau**:
   ```python
   from deepresearch.src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal

   # Khởi tạo agent
   agent = WebSearchAgentLocal(verbose=True)

   # Tìm kiếm
   results = agent.search("Python programming", num_results=3)

   # In kết quả
   if results.get('success', False):
       print(f"Tìm thấy {len(results.get('results', []))} kết quả")
       for result in results.get('results', []):
           print(f"- {result.get('title')}: {result.get('url')}")
   else:
       print(f"Lỗi: {results.get('error', 'Unknown error')}")
   ```

3. **Chạy script**:
   ```bash
   python test_websearch.py
   ```

## Xử lý sự cố

### SearXNG không hoạt động

1. **Kiểm tra container**:
   ```bash
   docker ps | grep searxng
   ```

2. **Kiểm tra logs**:
   ```bash
   docker logs searxng
   ```

3. **Khởi động lại container**:
   ```bash
   docker restart searxng
   ```

4. **Kiểm tra cổng**:
   ```bash
   curl http://localhost:8080
   ```

### Playwright không hoạt động

1. **Kiểm tra cài đặt**:
   ```bash
   python -m playwright --version
   ```

2. **Cài đặt lại trình duyệt**:
   ```bash
   python -m playwright install chromium
   ```

3. **Kiểm tra trình duyệt**:
   ```python
   from playwright.sync_api import sync_playwright

   with sync_playwright() as p:
       browser = p.chromium.launch()
       page = browser.new_page()
       page.goto('https://example.com')
       print(page.title())
       browser.close()
   ```

### Lỗi 'int' object is not subscriptable

Lỗi này thường xảy ra khi các thuộc tính từ điển không được khởi tạo đúng cách. WebSearchAgentLocal đã khắc phục lỗi này bằng cách khởi tạo các thuộc tính từ điển trước khi gọi constructor của lớp cha.

Nếu bạn vẫn gặp lỗi này, hãy đảm bảo bạn đang sử dụng phiên bản mới nhất của WebSearchAgentLocal:

```python
# Kiểm tra phiên bản
from deepresearch.src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal

agent = WebSearchAgentLocal()
print(f"Phiên bản: {agent.version}")
```

### Lỗi kết nối SearXNG

Nếu bạn gặp lỗi kết nối đến SearXNG, hãy kiểm tra:

1. **URL SearXNG**:
   ```python
   agent = WebSearchAgentLocal(
       api_search_config={
           "searx_url": "http://localhost:8080"  # Đảm bảo URL này đúng
       }
   )
   ```

2. **Tường lửa**:
   ```bash
   # Kiểm tra xem cổng 8080 có đang mở không
   sudo netstat -tulpn | grep 8080
   ```

3. **Thử URL khác**:
   ```python
   agent = WebSearchAgentLocal(
       api_search_config={
           "searx_url": "http://127.0.0.1:8080"  # Thử IP thay vì localhost
       }
   )
   ```

### Lỗi thiếu thư viện

Nếu bạn gặp lỗi thiếu thư viện, hãy cài đặt thư viện đó:

```bash
pip install <tên-thư-viện>
```

Ví dụ:
```bash
pip install langdetect  # Nếu thiếu langdetect
pip install psutil      # Nếu thiếu psutil
```
