# WebSearchAgentLocal

WebSearchAgentLocal là một agent tì<PERSON> kiếm web mạnh mẽ sử dụng SearXNG local, Crawlee và Playwright để tìm kiếm và trích xuất thông tin từ web. Agent n<PERSON><PERSON> đ<PERSON> thiết kế để hoạt động hoàn toàn local, không phụ thuộc vào các dịch vụ tìm kiếm bên ngoài.

## <PERSON><PERSON><PERSON> lụ<PERSON>

1. [T<PERSON>h năng](#tính-năng)
2. [Cài đặt](#cài-đặt)
   - [Cài đặt SearXNG với Docker](#1-cài-đặt-searxng-với-docker)
   - [<PERSON><PERSON><PERSON> đặt Playwright](#2-cài-đặt-playwright)
   - [Cài đặt các thư viện Python cần thiết](#3-cài-đặt-các-thư-viện-python-cần-thiết)
3. [Sử dụng cơ bản](#sử-dụng-cơ-bản)
   - [Khởi tạo WebSearchAgentLocal](#khởi-tạo-websearchagentlocal)
   - [Tìm kiếm cơ bản](#tìm-kiếm-cơ-bản)
   - [Tìm kiếm tiếng Việt](#tìm-kiếm-tiếng-việt)
   - [Trích xuất nội dung](#trích-xuất-nội-dung)
4. [Tính năng nâng cao](#tính-năng-nâng-cao)
   - [Phân tích truy vấn nâng cao](#phân-tích-truy-vấn-nâng-cao)
   - [Tìm kiếm chuyên biệt](#tìm-kiếm-chuyên-biệt)
   - [Đánh giá và xếp hạng kết quả](#đánh-giá-và-xếp-hạng-kết-quả)
   - [Tối ưu hóa hiệu suất](#tối-ưu-hóa-hiệu-suất)
   - [Hệ thống Plugin](#hệ-thống-plugin)
5. [Cấu hình SearXNG](#cấu-hình-searxng)
6. [Ví dụ truy vấn](#ví-dụ-truy-vấn)
7. [Kiến trúc hệ thống](#kiến-trúc-hệ-thống)
8. [FAQ và xử lý sự cố](#faq-và-xử-lý-sự-cố)
9. [Tối ưu hóa hiệu suất](#tối-ưu-hóa-hiệu-suất-1)
10. [Đóng góp](#đóng-góp)

## Tính năng

- **Tìm kiếm web cục bộ**: Sử dụng SearXNG local (Docker) để tìm kiếm web mà không phụ thuộc vào API bên ngoài
- **Tìm kiếm nâng cao**: Sử dụng Crawlee và Playwright để tìm kiếm và trích xuất thông tin chi tiết
- **Phân tích truy vấn thông minh**: Phân tích truy vấn để xác định phương thức tìm kiếm tối ưu
- **Trích xuất nội dung**: Trích xuất nội dung từ các trang web với các trình trích xuất chuyên biệt
- **Cache thông minh**: Cache kết quả tìm kiếm với nhiều cấp độ (bộ nhớ, đĩa) và hỗ trợ tìm kiếm tương tự
- **Rate limiting thích ứng**: Tự động điều chỉnh tốc độ tìm kiếm để tránh bị chặn
- **Xử lý CAPTCHA**: Phát hiện và xử lý CAPTCHA tự động
- **Hỗ trợ tìm kiếm tiếng Việt**: Tối ưu hóa cho tìm kiếm tiếng Việt với các phương thức chuyên biệt
- **Đánh giá và xếp hạng kết quả**: Đánh giá và xếp hạng kết quả dựa trên nhiều yếu tố
- **Hệ thống Plugin**: Mở rộng chức năng với các plugin
- **Tối ưu hóa hiệu suất**: Tối ưu hóa sử dụng bộ nhớ, CPU và xử lý bất đồng bộ

## Cài đặt

### 1. Cài đặt SearXNG với Docker

```bash
# Tải image SearXNG
docker pull searxng/searxng

# Tạo thư mục cấu hình
mkdir -p searxng

# Chạy container SearXNG
docker run -d \
  --name searxng \
  -p 8080:8080 \
  -v "${PWD}/searxng:/etc/searxng" \
  -e "BASE_URL=http://localhost:8080/" \
  searxng/searxng
```

### 2. Cài đặt Playwright

```bash
# Cài đặt Playwright
pip install playwright

# Cài đặt trình duyệt
python -m playwright install chromium
```

### 3. Cài đặt các thư viện Python cần thiết

```bash
pip install requests beautifulsoup4 langdetect psutil asyncio
```

## Sử dụng cơ bản

### Khởi tạo WebSearchAgentLocal

```python
from deepresearch.src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal

# Khởi tạo với cấu hình mặc định
agent = WebSearchAgentLocal()

# Khởi tạo với cấu hình tùy chỉnh
agent = WebSearchAgentLocal(
    search_method="auto",  # "searxng", "crawlee", hoặc "auto"
    api_search_config={
        "engine": "searx",
        "searx_url": "http://localhost:8080",  # URL của SearXNG local
        "language": "auto",
        "time_range": "",
        "safe_search": False
    },
    crawlee_search_config={
        "max_depth": 2,
        "max_pages_per_url": 3,
        "max_urls": 5,
        "timeout": 30
    },
    content_extractor_config={
        "extract_links": True,
        "extract_images": False,
        "max_content_length": 10000,
        "timeout": 15
    },
    cache_ttl=3600,  # 1 giờ
    rate_limit=20,   # 20 yêu cầu mỗi phút
    verbose=True
)
```

### Tìm kiếm cơ bản

```python
# Tìm kiếm cơ bản
results = agent.search(query="Python programming")

# Tìm kiếm với tham số bổ sung
results = agent.search(
    query="Python programming tutorial",
    num_results=5,
    method="searxng",  # Chỉ định phương thức tìm kiếm
    get_content=True  # Trích xuất nội dung
)

# Xử lý kết quả
if results.get('success', False):
    print(f"Tìm thấy {len(results.get('results', []))} kết quả")
    for result in results.get('results', []):
        print(f"- {result.get('title')}: {result.get('url')}")
else:
    print(f"Lỗi: {results.get('error', 'Unknown error')}")
```

### Tìm kiếm tiếng Việt

```python
# Tìm kiếm tiếng Việt cơ bản
results_vi = agent.search(query="Lập trình Python", language="vi")

# Tìm kiếm tiếng Việt nâng cao
results_vi_advanced = agent.search_vietnamese(
    query="Lập trình Python",
    search_method="coccoc",  # "coccoc", "wikitiengviet", "baomoi", hoặc "auto"
    num_results=5
)
```

### Trích xuất nội dung

```python
# Trích xuất nội dung từ một URL
content = agent.extract_content("https://example.com/article")

# Trích xuất nội dung với HTML
content = agent.extract_content("https://example.com/article", include_html=True)

# In thông tin
print(f"Tiêu đề: {content.get('title', '')}")
print(f"Nội dung: {content.get('content', '')[:200]}...")
```

## Tính năng nâng cao

### Phân tích truy vấn nâng cao

WebSearchAgentLocal sử dụng `LocalQueryAnalyzer` để phân tích truy vấn và xác định phương thức tìm kiếm tối ưu:

```python
from deepresearch.src.deep_research_core.utils.local_query_analyzer import LocalQueryAnalyzer

# Khởi tạo LocalQueryAnalyzer
analyzer = LocalQueryAnalyzer()

# Phân tích truy vấn
analysis = analyzer.analyze("Cách triển khai mạng neural trong Python")

# In kết quả phân tích
print(f"Loại truy vấn: {analysis.get('query_type')}")
print(f"Độ phức tạp: {analysis.get('complexity')}")
print(f"Lĩnh vực: {analysis.get('domain')}")
print(f"Ngôn ngữ: {analysis.get('language')}")
```

### Tìm kiếm chuyên biệt

```python
# Tìm kiếm chuyên biệt cho lĩnh vực cụ thể
results = agent.search_specialized(
    query="Python neural networks",
    domain="academic",  # "academic", "books", "maps", hoặc "auto"
    num_results=5
)
```

### Đánh giá và xếp hạng kết quả

```python
from deepresearch.src.deep_research_core.agents.result_ranking_integration import integrate_enhanced_ranking

# Tích hợp EnhancedResultRanker
integrate_enhanced_ranking(agent, config={
    "trusted_domains": ["wikipedia.org", "python.org"],
    "ranking_weights": {
        "relevance": 0.5,
        "domain_trust": 0.2,
        "content_quality": 0.2,
        "freshness": 0.1
    },
    "relevance_threshold": 0.3
})

# Tìm kiếm với xếp hạng nâng cao
results = agent.search("python programming", num_results=10)
```

### Tối ưu hóa hiệu suất

```python
from deepresearch.src.deep_research_core.utils.performance_integration import integrate_performance_features

# Tích hợp các tính năng hiệu suất
integrate_performance_features(
    agent,
    enable_monitoring=True,
    enable_async_search=True,
    enable_resource_limiting=True,
    enable_adaptive_parameters=True
)
```

### Hệ thống Plugin

```python
# Đăng ký plugin
agent.register_plugin("query_optimization", {
    "enabled": True,
    "config": {
        "use_synonyms": True,
        "use_spell_check": True
    }
})

# Sử dụng plugin
results = agent.search("python programing", num_results=5)  # Lỗi chính tả sẽ được sửa
```

## Cấu hình SearXNG

Để cấu hình SearXNG, tạo file `settings.yml` trong thư mục `searxng` với nội dung sau:

```yaml
use_default_settings: true

server:
  secret_key: "ultrasecretkey"  # Thay đổi thành một khóa bí mật
  limiter: false
  public_instance: false
  image_proxy: true

ui:
  static_use_hash: true
  default_theme: simple
  theme_args:
    simple_style: auto
  query_in_title: true
  infinite_scroll: false
  center_alignment: false
  results_on_new_tab: false
  advanced_search: false
  num_results: 10

search:
  safe_search: 0
  autocomplete: "google"
  autocomplete_min: 4
  default_lang: "vi"  # Thay đổi ngôn ngữ mặc định nếu cần

redis:
  url: redis://redis:6379/0

enabled_plugins:
  - 'Hash plugin'
  - 'Search on category select'
  - 'Self Informations'
  - 'Tracker URL remover'
  - 'Vim-like hotkeys'
```

Sau khi tạo file cấu hình, khởi động lại container SearXNG:

```bash
docker restart searxng
```

## Ví dụ truy vấn

### Truy vấn đơn giản (Sử dụng SearXNG)

```python
results = agent.search("Thủ đô của Việt Nam")
```

### Truy vấn phức tạp (Sử dụng Crawlee)

```python
results = agent.search(
    "So sánh chi tiết giữa React và Vue.js về performance và state management",
    method="crawlee",
    get_content=True
)
```

### Truy vấn tiếng Việt

```python
results = agent.search_vietnamese(
    "Phân tích tác động của chính sách tiền tệ mới đối với thị trường bất động sản Việt Nam 2024",
    search_method="coccoc"
)
```

## Kiến trúc hệ thống

WebSearchAgentLocal được thiết kế với kiến trúc module hóa, cho phép dễ dàng mở rộng và tùy chỉnh:

1. **Core Agent**: `WebSearchAgentLocal` - Lớp chính quản lý tìm kiếm và trích xuất nội dung
2. **Search Methods**: `search_searxng`, `search_crawlee`, `search_vietnamese` - Các phương thức tìm kiếm khác nhau
3. **Query Analysis**: `LocalQueryAnalyzer` - Phân tích truy vấn để xác định phương thức tìm kiếm tối ưu
4. **Content Extraction**: `AsyncContentExtractor`, `SpecializedExtractors` - Trích xuất nội dung từ các trang web
5. **Caching**: `EnhancedWebSearchCache` - Cache kết quả tìm kiếm với nhiều cấp độ
6. **Rate Limiting**: `AdaptiveRateLimiter` - Giới hạn tốc độ tìm kiếm để tránh bị chặn
7. **CAPTCHA Handling**: `CaptchaHandler` - Phát hiện và xử lý CAPTCHA
8. **Result Ranking**: `EnhancedResultRanker` - Đánh giá và xếp hạng kết quả
9. **Performance Optimization**: `PerformanceMonitor`, `AsyncSearchExecutor`, `ResourceLimiter` - Tối ưu hóa hiệu suất
10. **Plugin System**: Hệ thống plugin mở rộng chức năng

## FAQ và xử lý sự cố

### SearXNG không hoạt động

1. Kiểm tra xem container SearXNG có đang chạy không:
   ```bash
   docker ps | grep searxng
   ```

2. Kiểm tra logs của container:
   ```bash
   docker logs searxng
   ```

3. Khởi động lại container:
   ```bash
   docker restart searxng
   ```

### Lỗi 'int' object is not subscriptable

Lỗi này thường xảy ra khi các thuộc tính từ điển không được khởi tạo đúng cách. WebSearchAgentLocal đã khắc phục lỗi này bằng cách khởi tạo các thuộc tính từ điển trước khi gọi constructor của lớp cha.

### Playwright không hoạt động

1. Kiểm tra xem Playwright đã được cài đặt chưa:
   ```bash
   python -m playwright --version
   ```

2. Cài đặt lại Playwright:
   ```bash
   pip install playwright
   python -m playwright install chromium
   ```

## Tối ưu hóa hiệu suất

### Giảm sử dụng bộ nhớ

1. Sử dụng `ResourceLimiter` để giới hạn sử dụng bộ nhớ:
   ```python
   from deepresearch.src.deep_research_core.utils.resource_limiter import ResourceLimiter

   limiter = ResourceLimiter(
       memory_limit=80.0,  # Giới hạn sử dụng bộ nhớ (%)
       cpu_limit=80.0,     # Giới hạn sử dụng CPU (%)
       max_concurrent_requests=10
   )
   limiter.start()
   ```

2. Điều chỉnh cấu hình cache:
   ```python
   agent = WebSearchAgentLocal(
       cache_ttl=1800,  # Giảm thời gian sống của cache (30 phút)
   )
   ```

### Tăng tốc độ tìm kiếm

1. Sử dụng `AsyncSearchExecutor` để thực hiện tìm kiếm bất đồng bộ:
   ```python
   from deepresearch.src.deep_research_core.utils.async_search_executor import AsyncSearchExecutor

   executor = AsyncSearchExecutor(
       max_concurrent_searches=5,
       max_concurrent_extractions=10
   )
   executor.start()
   ```

2. Điều chỉnh cấu hình tìm kiếm:
   ```python
   agent = WebSearchAgentLocal(
       crawlee_search_config={
           "max_depth": 1,  # Giảm độ sâu crawl
           "max_pages_per_url": 2,
           "max_urls": 3,
           "timeout": 15  # Giảm timeout
       }
   )
   ```

## Đóng góp

Chúng tôi rất hoan nghênh đóng góp của bạn! Vui lòng tạo issue hoặc pull request trên GitHub.
