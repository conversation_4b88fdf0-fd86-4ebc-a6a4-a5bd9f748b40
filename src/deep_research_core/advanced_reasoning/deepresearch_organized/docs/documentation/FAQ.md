# FAQ và xử lý sự cố

Tài liệu này cung cấp câu trả lời cho các câu hỏi thường gặp và hướng dẫn xử lý các sự cố phổ biến khi sử dụng WebSearchAgentLocal.

## Mụ<PERSON> lục

1. [<PERSON><PERSON><PERSON> hỏi chung](#câu-hỏi-chung)
2. [Cài đặt và cấu hình](#cài-đặt-và-cấu-hình)
3. [Tìm kiếm và kết quả](#tìm-kiếm-và-kết-quả)
4. [Hiệu suất và tối ưu hóa](#hiệu-suất-và-tối-ưu-hóa)
5. [Xử lý sự cố](#xử-lý-sự-cố)
6. [Tính năng nâng cao](#tính-năng-nâng-cao)

## Câ<PERSON> hỏi chung

### WebSearchAgentLocal là gì?

WebSearchAgentLocal là một agent tìm kiếm web sử dụng SearXNG local, Crawlee và Playwright để tìm kiếm và trích xuất thông tin từ web. Agent này được thiết kế để hoạt động hoàn toàn local, không phụ thuộc vào các dịch vụ tìm kiếm bên ngoài.

### WebSearchAgentLocal khác gì so với WebSearchAgent?

WebSearchAgentLocal là phiên bản cục bộ của WebSearchAgent, được thiết kế để hoạt động mà không cần API bên ngoài. Nó sử dụng SearXNG local thay vì các API tìm kiếm như DuckDuckGo, Google, hoặc Bing.

### Tôi có cần API key để sử dụng WebSearchAgentLocal không?

Không, WebSearchAgentLocal được thiết kế để hoạt động mà không cần API key. Nó sử dụng SearXNG local và các phương thức tìm kiếm cục bộ khác.

### WebSearchAgentLocal có hỗ trợ ngôn ngữ nào?

WebSearchAgentLocal hỗ trợ nhiều ngôn ngữ, bao gồm tiếng Anh, tiếng Việt, và các ngôn ngữ khác. Nó có hỗ trợ đặc biệt cho tiếng Việt với phương thức `search_vietnamese`.

## Cài đặt và cấu hình

### Làm thế nào để cài đặt SearXNG?

Bạn có thể cài đặt SearXNG bằng Docker:

```bash
# Tải image SearXNG
docker pull searxng/searxng

# Tạo thư mục cấu hình
mkdir -p searxng

# Chạy container SearXNG
docker run -d \
  --name searxng \
  -p 8080:8080 \
  -v "${PWD}/searxng:/etc/searxng" \
  -e "BASE_URL=http://localhost:8080/" \
  searxng/searxng
```

### Làm thế nào để cấu hình SearXNG?

Bạn có thể cấu hình SearXNG bằng cách tạo file `settings.yml` trong thư mục cấu hình:

```bash
nano searxng/settings.yml
```

Xem [INSTALLATION.md](INSTALLATION.md) để biết thêm chi tiết về cấu hình SearXNG.

### Làm thế nào để cài đặt Playwright?

Bạn có thể cài đặt Playwright bằng pip:

```bash
pip install playwright
python -m playwright install chromium
```

### Làm thế nào để cấu hình WebSearchAgentLocal?

Bạn có thể cấu hình WebSearchAgentLocal khi khởi tạo:

```python
agent = WebSearchAgentLocal(
    search_method="auto",
    api_search_config={
        "searx_url": "http://localhost:8080"
    },
    crawlee_search_config={
        "max_depth": 2
    },
    cache_ttl=3600,
    verbose=True
)
```

## Tìm kiếm và kết quả

### Làm thế nào để tìm kiếm với WebSearchAgentLocal?

Bạn có thể tìm kiếm với WebSearchAgentLocal bằng phương thức `search`:

```python
results = agent.search("Python programming")
```

### Làm thế nào để tìm kiếm tiếng Việt?

Bạn có thể tìm kiếm tiếng Việt bằng phương thức `search_vietnamese`:

```python
results = agent.search_vietnamese("Lập trình Python")
```

### Làm thế nào để trích xuất nội dung từ URL?

Bạn có thể trích xuất nội dung từ URL bằng phương thức `extract_content`:

```python
content = agent.extract_content("https://example.com/article")
```

### Làm thế nào để lấy nội dung đầy đủ khi tìm kiếm?

Bạn có thể lấy nội dung đầy đủ khi tìm kiếm bằng tham số `get_content`:

```python
results = agent.search("Python programming", get_content=True)
```

### Làm thế nào để bỏ qua cache khi tìm kiếm?

Bạn có thể bỏ qua cache khi tìm kiếm bằng tham số `force_refresh`:

```python
results = agent.search("Python programming", force_refresh=True)
```

## Hiệu suất và tối ưu hóa

### Làm thế nào để tăng tốc độ tìm kiếm?

Bạn có thể tăng tốc độ tìm kiếm bằng cách:
- Sử dụng cache
- Giảm số lượng kết quả
- Sử dụng phương thức tìm kiếm phù hợp
- Tích hợp các tính năng hiệu suất

```python
from deepresearch.src.deep_research_core.utils.performance_integration import integrate_performance_features

integrate_performance_features(
    agent,
    enable_async_search=True
)
```

### Làm thế nào để giảm sử dụng bộ nhớ?

Bạn có thể giảm sử dụng bộ nhớ bằng cách:
- Giảm kích thước cache
- Sử dụng ResourceLimiter
- Giảm số lượng kết quả

```python
from deepresearch.src.deep_research_core.utils.resource_limiter import ResourceLimiter

limiter = ResourceLimiter(
    memory_limit=80.0,
    cpu_limit=80.0
)
limiter.start()
```

### Làm thế nào để tối ưu hóa cache?

Bạn có thể tối ưu hóa cache bằng cách:
- Điều chỉnh thời gian sống của cache
- Điều chỉnh kích thước cache
- Sử dụng EnhancedWebSearchCache

```python
from deepresearch.src.deep_research_core.utils.enhanced_cache import EnhancedWebSearchCache

custom_cache = EnhancedWebSearchCache(
    memory_limit=1000,
    disk_limit=10000,
    memory_ttl=3600,
    disk_ttl=86400
)

agent.smart_cache = custom_cache
```

## Xử lý sự cố

### SearXNG không hoạt động

Nếu SearXNG không hoạt động, hãy thử:

1. Kiểm tra xem container SearXNG có đang chạy không:
   ```bash
   docker ps | grep searxng
   ```

2. Kiểm tra logs của container:
   ```bash
   docker logs searxng
   ```

3. Khởi động lại container:
   ```bash
   docker restart searxng
   ```

4. Kiểm tra cổng:
   ```bash
   curl http://localhost:8080
   ```

### Lỗi 'int' object is not subscriptable

Lỗi này thường xảy ra khi các thuộc tính từ điển không được khởi tạo đúng cách. WebSearchAgentLocal đã khắc phục lỗi này bằng cách khởi tạo các thuộc tính từ điển trước khi gọi constructor của lớp cha.

Nếu bạn vẫn gặp lỗi này, hãy đảm bảo bạn đang sử dụng phiên bản mới nhất của WebSearchAgentLocal.

### Playwright không hoạt động

Nếu Playwright không hoạt động, hãy thử:

1. Kiểm tra xem Playwright đã được cài đặt chưa:
   ```bash
   python -m playwright --version
   ```

2. Cài đặt lại Playwright:
   ```bash
   pip install playwright
   python -m playwright install chromium
   ```

3. Kiểm tra trình duyệt:
   ```python
   from playwright.sync_api import sync_playwright

   with sync_playwright() as p:
       browser = p.chromium.launch()
       page = browser.new_page()
       page.goto('https://example.com')
       print(page.title())
       browser.close()
   ```

### Lỗi kết nối SearXNG

Nếu bạn gặp lỗi kết nối đến SearXNG, hãy kiểm tra:

1. URL SearXNG:
   ```python
   agent = WebSearchAgentLocal(
       api_search_config={
           "searx_url": "http://localhost:8080"  # Đảm bảo URL này đúng
       }
   )
   ```

2. Tường lửa:
   ```bash
   # Kiểm tra xem cổng 8080 có đang mở không
   sudo netstat -tulpn | grep 8080
   ```

3. Thử URL khác:
   ```python
   agent = WebSearchAgentLocal(
       api_search_config={
           "searx_url": "http://127.0.0.1:8080"  # Thử IP thay vì localhost
       }
   )
   ```

### Lỗi thiếu thư viện

Nếu bạn gặp lỗi thiếu thư viện, hãy cài đặt thư viện đó:

```bash
pip install <tên-thư-viện>
```

Ví dụ:
```bash
pip install langdetect  # Nếu thiếu langdetect
pip install psutil      # Nếu thiếu psutil
```

## Tính năng nâng cao

### Làm thế nào để sử dụng EnhancedResultRanker?

Bạn có thể sử dụng EnhancedResultRanker để đánh giá và xếp hạng kết quả:

```python
from deepresearch.src.deep_research_core.agents.result_ranking_integration import integrate_enhanced_ranking

integrate_enhanced_ranking(agent, config={
    "trusted_domains": ["wikipedia.org", "python.org"],
    "ranking_weights": {
        "relevance": 0.5,
        "domain_trust": 0.2,
        "content_quality": 0.2,
        "freshness": 0.1
    }
})
```

### Làm thế nào để sử dụng plugin?

Bạn có thể sử dụng plugin để mở rộng chức năng của WebSearchAgentLocal:

```python
agent.register_plugin("query_optimization", {
    "enabled": True,
    "config": {
        "use_synonyms": True,
        "use_spell_check": True
    }
})
```

### Làm thế nào để sử dụng tìm kiếm chuyên biệt?

Bạn có thể sử dụng tìm kiếm chuyên biệt cho các lĩnh vực cụ thể:

```python
results = agent.search_specialized(
    query="Python neural networks",
    domain="academic",
    num_results=5
)
```

### Làm thế nào để sử dụng tìm kiếm bất đồng bộ?

Bạn có thể sử dụng tìm kiếm bất đồng bộ để tăng tốc độ tìm kiếm:

```python
from deepresearch.src.deep_research_core.utils.async_search_executor import AsyncSearchExecutor

executor = AsyncSearchExecutor(
    max_concurrent_searches=5,
    max_concurrent_extractions=10
)
executor.start()

# Gán executor vào agent
agent.async_search_executor = executor
```

### Làm thế nào để tùy chỉnh trình trích xuất nội dung?

Bạn có thể tùy chỉnh trình trích xuất nội dung bằng cách tạo trình trích xuất mới:

```python
from deepresearch.src.deep_research_core.utils.specialized_extractors import BaseExtractor

class CustomExtractor(BaseExtractor):
    @classmethod
    def can_handle(cls, url):
        return "example.com" in url
    
    def extract(self, html, url, **kwargs):
        # Trích xuất nội dung
        # ...
        
        return {
            "title": title,
            "content": content,
            "language": language
        }

# Đăng ký trình trích xuất
from deepresearch.src.deep_research_core.utils.specialized_extractors import register_extractor
register_extractor(CustomExtractor)
```
