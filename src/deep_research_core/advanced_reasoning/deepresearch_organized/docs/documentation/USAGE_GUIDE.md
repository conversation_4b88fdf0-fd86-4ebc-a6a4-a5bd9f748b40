# Hướng dẫn sử dụng WebSearchAgentLocal

Tài liệu này cung cấp hướng dẫn chi tiết về cách sử dụng WebSearchAgentLocal và các tính năng của nó.

## Mụ<PERSON> lục

1. [Khởi tạo WebSearchAgentLocal](#khởi-tạo-websearchagentlocal)
2. [Tì<PERSON> kiếm cơ bản](#tìm-kiếm-cơ-bản)
3. [Tìm kiếm nâng cao](#tìm-kiếm-nâng-cao)
   - [Tìm kiếm với SearXNG](#tìm-kiếm-với-searxng)
   - [Tìm kiếm với Crawlee](#tìm-kiếm-với-crawlee)
   - [Tìm kiếm tự động](#tìm-kiếm-tự-động)
4. [Trích xuất nội dung](#trích-xuất-nội-dung)
5. [Tìm kiếm tiếng Việt](#tìm-kiếm-tiếng-việt)
6. [Sử dụng cache](#sử-dụng-cache)
7. [Đánh giá và xếp hạng kết quả](#đánh-giá-và-xếp-hạng-kết-quả)
8. [Tối ưu hóa hiệu suất](#tối-ưu-hóa-hiệu-suất)
9. [Sử dụng plugin](#sử-dụng-plugin)
10. [Ví dụ thực tế](#ví-dụ-thực-tế)

## Khởi tạo WebSearchAgentLocal

### Khởi tạo với cấu hình mặc định

```python
from deepresearch.src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal

# Khởi tạo với cấu hình mặc định
agent = WebSearchAgentLocal()
```

### Khởi tạo với cấu hình tùy chỉnh

```python
# Khởi tạo với cấu hình tùy chỉnh
agent = WebSearchAgentLocal(
    search_method="auto",  # "searxng", "crawlee", hoặc "auto"
    api_search_config={
        "engine": "searx",
        "searx_url": "http://localhost:8080",  # URL của SearXNG local
        "language": "auto",
        "time_range": "",
        "safe_search": False
    },
    crawlee_search_config={
        "max_depth": 2,
        "max_pages_per_url": 3,
        "max_urls": 5,
        "timeout": 30
    },
    content_extractor_config={
        "extract_links": True,
        "extract_images": False,
        "max_content_length": 10000,
        "timeout": 15
    },
    cache_ttl=3600,  # 1 giờ
    rate_limit=20,   # 20 yêu cầu mỗi phút
    verbose=True
)
```

### Các tham số cấu hình

| Tham số | Mô tả | Giá trị mặc định |
|---------|-------|-----------------|
| `search_method` | Phương thức tìm kiếm | `"auto"` |
| `api_search_config` | Cấu hình cho tìm kiếm SearXNG | `{}` |
| `crawlee_search_config` | Cấu hình cho tìm kiếm Crawlee | `{}` |
| `content_extractor_config` | Cấu hình cho trích xuất nội dung | `{}` |
| `cache_ttl` | Thời gian sống của cache (giây) | `3600` |
| `rate_limit` | Số yêu cầu tối đa mỗi phút | `20` |
| `verbose` | Ghi log chi tiết | `False` |
| `use_query_decomposer` | Sử dụng phân tích truy vấn | `True` |
| `enable_plugins` | Bật hệ thống plugin | `True` |
| `enable_vietnamese_search` | Bật tìm kiếm tiếng Việt | `True` |

## Tìm kiếm cơ bản

### Tìm kiếm đơn giản

```python
# Tìm kiếm cơ bản
results = agent.search(query="Python programming")

# Xử lý kết quả
if results.get('success', False):
    print(f"Tìm thấy {len(results.get('results', []))} kết quả")
    for result in results.get('results', []):
        print(f"- {result.get('title')}: {result.get('url')}")
else:
    print(f"Lỗi: {results.get('error', 'Unknown error')}")
```

### Tìm kiếm với số lượng kết quả tùy chỉnh

```python
# Tìm kiếm với số lượng kết quả tùy chỉnh
results = agent.search(query="Python programming", num_results=5)
```

### Tìm kiếm và lấy nội dung đầy đủ

```python
# Tìm kiếm và lấy nội dung đầy đủ
results = agent.search(query="Python programming", get_content=True)

# Xử lý kết quả với nội dung
if results.get('success', False):
    for result in results.get('results', []):
        print(f"- {result.get('title')}: {result.get('url')}")
        print(f"  Nội dung: {result.get('content', '')[:100]}...")
```

### Tìm kiếm và bỏ qua cache

```python
# Tìm kiếm và bỏ qua cache
results = agent.search(query="Python programming", force_refresh=True)
```

## Tìm kiếm nâng cao

### Tìm kiếm với SearXNG

```python
# Tìm kiếm với SearXNG
results = agent.search(
    query="Python programming",
    method="searxng",
    num_results=10
)
```

### Tìm kiếm với Crawlee

```python
# Tìm kiếm với Crawlee
results = agent.search(
    query="Python programming",
    method="crawlee",
    num_results=5,
    max_depth=3,
    max_pages=10
)
```

### Tìm kiếm tự động

```python
# Tìm kiếm tự động (chọn phương thức tốt nhất dựa trên truy vấn)
results = agent.search(
    query="So sánh chi tiết giữa React và Vue.js về performance và state management",
    method="auto",
    get_content=True
)
```

## Trích xuất nội dung

### Trích xuất nội dung từ URL

```python
# Trích xuất nội dung từ URL
content = agent.extract_content("https://example.com/article")

# In thông tin
print(f"Tiêu đề: {content.get('title', '')}")
print(f"Nội dung: {content.get('content', '')[:200]}...")
```

### Trích xuất nội dung với HTML

```python
# Trích xuất nội dung với HTML
content = agent.extract_content("https://example.com/article", include_html=True)

# In thông tin
print(f"Tiêu đề: {content.get('title', '')}")
print(f"HTML: {content.get('html', '')[:200]}...")
```

### Trích xuất nội dung với các tùy chọn

```python
# Trích xuất nội dung với các tùy chọn
content = agent.extract_content(
    "https://example.com/article",
    include_html=True,
    extract_links=True,
    extract_images=True,
    timeout=30
)

# In thông tin về liên kết
if content.get('links'):
    print("Liên kết:")
    for link in content.get('links', [])[:5]:
        print(f"- {link.get('text', '')}: {link.get('url', '')}")

# In thông tin về hình ảnh
if content.get('images'):
    print("Hình ảnh:")
    for image in content.get('images', [])[:5]:
        print(f"- {image.get('alt', '')}: {image.get('src', '')}")
```

## Tìm kiếm tiếng Việt

### Tìm kiếm tiếng Việt cơ bản

```python
# Tìm kiếm tiếng Việt cơ bản
results_vi = agent.search(query="Lập trình Python", language="vi")
```

### Tìm kiếm tiếng Việt nâng cao

```python
# Tìm kiếm tiếng Việt nâng cao
results_vi_advanced = agent.search_vietnamese(
    query="Lập trình Python",
    search_method="coccoc",  # "coccoc", "wikitiengviet", "baomoi", hoặc "auto"
    num_results=5
)
```

### Tìm kiếm tiếng Việt với các tùy chọn

```python
# Tìm kiếm tiếng Việt với các tùy chọn
results_vi_options = agent.search_vietnamese(
    query="Phân tích tác động của chính sách tiền tệ mới đối với thị trường bất động sản Việt Nam 2024",
    search_method="auto",
    num_results=10,
    get_content=True,
    optimize_for_llm=True
)
```

## Sử dụng cache

### Bật/tắt cache

```python
# Khởi tạo với cache
agent = WebSearchAgentLocal(
    cache_ttl=3600,  # 1 giờ
    use_smart_cache=True
)

# Tìm kiếm với cache
results1 = agent.search("Python programming")  # Kết quả sẽ được cache

# Tìm kiếm lại (sẽ lấy từ cache)
results2 = agent.search("Python programming")

# Tìm kiếm và bỏ qua cache
results3 = agent.search("Python programming", force_refresh=True)
```

### Cấu hình cache nâng cao

```python
from deepresearch.src.deep_research_core.utils.enhanced_cache import EnhancedWebSearchCache

# Tạo cache tùy chỉnh
custom_cache = EnhancedWebSearchCache(
    cache_dir="/path/to/cache",
    memory_limit=1000,
    disk_limit=10000,
    memory_ttl=3600,
    disk_ttl=86400,  # 1 ngày
    similarity_threshold=0.8,
    enable_prefetching=True,
    enable_compression=True
)

# Gán cache tùy chỉnh
agent.smart_cache = custom_cache
```

## Đánh giá và xếp hạng kết quả

### Tích hợp EnhancedResultRanker

```python
from deepresearch.src.deep_research_core.agents.result_ranking_integration import integrate_enhanced_ranking

# Tích hợp EnhancedResultRanker
integrate_enhanced_ranking(agent, config={
    "trusted_domains": ["wikipedia.org", "python.org"],
    "ranking_weights": {
        "relevance": 0.5,
        "domain_trust": 0.2,
        "content_quality": 0.2,
        "freshness": 0.1
    },
    "relevance_threshold": 0.3
})

# Tìm kiếm với xếp hạng nâng cao
results = agent.search("python programming", num_results=10)

# Xem điểm xếp hạng
for result in results.get('results', []):
    print(f"- {result.get('title')}")
    print(f"  Điểm liên quan: {result.get('relevance_score', 0):.2f}")
    print(f"  Điểm tin cậy: {result.get('domain_trust_score', 0):.2f}")
    print(f"  Điểm chất lượng: {result.get('content_quality_score', 0):.2f}")
    print(f"  Điểm độ mới: {result.get('freshness_score', 0):.2f}")
    print(f"  Điểm tổng: {result.get('final_score', 0):.2f}")
```

## Tối ưu hóa hiệu suất

### Tích hợp các tính năng hiệu suất

```python
from deepresearch.src.deep_research_core.utils.performance_integration import integrate_performance_features

# Tích hợp các tính năng hiệu suất
integrate_performance_features(
    agent,
    enable_monitoring=True,
    enable_async_search=True,
    enable_resource_limiting=True,
    enable_adaptive_parameters=True,
    config={
        "monitoring": {
            "log_interval": 60,
            "memory_threshold": 80.0,
            "cpu_threshold": 80.0
        },
        "async_search": {
            "max_concurrent_searches": 5,
            "max_concurrent_extractions": 10
        },
        "resource_limiting": {
            "memory_limit": 80.0,
            "cpu_limit": 80.0,
            "max_concurrent_requests": 10
        },
        "adaptive_parameters": {
            "auto_adjust": True,
            "auto_adjust_interval": 300.0
        }
    }
)

# Sử dụng agent như bình thường
results = agent.search("python programming", num_results=10)

# Khi kết thúc, gọi cleanup để giải phóng tài nguyên
agent.cleanup()
```

## Sử dụng plugin

### Đăng ký plugin

```python
# Đăng ký plugin
agent.register_plugin("query_optimization", {
    "enabled": True,
    "config": {
        "use_synonyms": True,
        "use_spell_check": True
    }
})

# Đăng ký nhiều plugin
agent.register_plugins({
    "query_optimization": {
        "enabled": True,
        "config": {
            "use_synonyms": True,
            "use_spell_check": True
        }
    },
    "result_filtering": {
        "enabled": True,
        "config": {
            "min_content_length": 100,
            "exclude_domains": ["example.com"]
        }
    }
})
```

### Sử dụng plugin

```python
# Sử dụng plugin
results = agent.search("python programing", num_results=5)  # Lỗi chính tả sẽ được sửa
```

## Ví dụ thực tế

### Tìm kiếm thông tin cơ bản

```python
# Tìm kiếm thông tin cơ bản
results = agent.search("Thủ đô của Việt Nam")

# Xử lý kết quả
if results.get('success', False):
    print(f"Tìm thấy {len(results.get('results', []))} kết quả")
    for result in results.get('results', []):
        print(f"- {result.get('title')}: {result.get('url')}")
else:
    print(f"Lỗi: {results.get('error', 'Unknown error')}")
```

### Tìm kiếm thông tin kỹ thuật

```python
# Tìm kiếm thông tin kỹ thuật
results = agent.search(
    "Cách triển khai mạng neural trong PyTorch với attention mechanism",
    method="crawlee",
    get_content=True,
    num_results=5
)

# Xử lý kết quả
if results.get('success', False):
    print(f"Tìm thấy {len(results.get('results', []))} kết quả")
    for result in results.get('results', []):
        print(f"- {result.get('title')}: {result.get('url')}")
        print(f"  Nội dung: {result.get('content', '')[:200]}...")
else:
    print(f"Lỗi: {results.get('error', 'Unknown error')}")
```

### Tìm kiếm và phân tích thông tin

```python
# Tìm kiếm và phân tích thông tin
results = agent.search(
    "So sánh chi tiết giữa React và Vue.js về performance và state management",
    method="auto",
    get_content=True,
    num_results=10
)

# Trích xuất thông tin từ kết quả
react_info = []
vue_info = []

for result in results.get('results', []):
    content = result.get('content', '')
    
    # Phân loại thông tin
    if "react" in content.lower():
        react_info.append(content)
    
    if "vue" in content.lower():
        vue_info.append(content)

# In thông tin
print(f"Thông tin về React: {len(react_info)} kết quả")
print(f"Thông tin về Vue: {len(vue_info)} kết quả")
```

### Tìm kiếm tiếng Việt và phân tích

```python
# Tìm kiếm tiếng Việt và phân tích
results = agent.search_vietnamese(
    "Phân tích tác động của chính sách tiền tệ mới đối với thị trường bất động sản Việt Nam 2024",
    search_method="coccoc",
    num_results=10,
    get_content=True
)

# Trích xuất thông tin từ kết quả
positive_impacts = []
negative_impacts = []

for result in results.get('results', []):
    content = result.get('content', '')
    
    # Phân loại thông tin (đơn giản)
    if "tăng trưởng" in content or "phát triển" in content or "cải thiện" in content:
        positive_impacts.append(content)
    
    if "giảm" in content or "suy thoái" in content or "khó khăn" in content:
        negative_impacts.append(content)

# In thông tin
print(f"Tác động tích cực: {len(positive_impacts)} kết quả")
print(f"Tác động tiêu cực: {len(negative_impacts)} kết quả")
```
