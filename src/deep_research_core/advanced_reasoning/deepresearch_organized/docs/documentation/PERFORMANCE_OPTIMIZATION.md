# Tối ưu hóa hiệu suất WebSearchAgentLocal

Tài liệu này cung cấp hướng dẫn chi tiết về cách tối ưu hóa hiệu suất của WebSearchAgentLocal, bao gồm sử dụng bộ nhớ, CPU, và tốc độ tìm kiếm.

## <PERSON><PERSON><PERSON> l<PERSON>

1. [Tổng quan hiệu suất](#tổng-quan-hiệu-suất)
2. [Giám sát hiệu suất](#giám-sát-hiệu-suất)
3. [Tối ưu hóa bộ nhớ](#tối-ưu-hóa-bộ-nhớ)
4. [Tối ưu hóa CPU](#tối-ưu-hóa-cpu)
5. [Tối ưu hóa tốc độ tìm kiếm](#tối-ưu-hóa-tốc-độ-tìm-kiếm)
6. [Tối ưu hóa cache](#tối-ưu-hóa-cache)
7. [<PERSON><PERSON> lý bất đồng bộ](#xử-lý-bất-đồng-bộ)
8. [T<PERSON> số tự điều chỉnh](#tham-số-tự-điều-chỉnh)
9. [Cấu hình tối ưu](#cấu-hình-tối-ưu)
10. [Đo lường hiệu suất](#đo-lường-hiệu-suất)

## Tổng quan hiệu suất

WebSearchAgentLocal bao gồm nhiều thành phần có thể ảnh hưởng đến hiệu suất:

1. **Tìm kiếm**: Thực hiện tìm kiếm với SearXNG, Crawlee, hoặc các phương thức khác
2. **Trích xuất nội dung**: Trích xuất nội dung từ các trang web
3. **Cache**: Lưu trữ kết quả tìm kiếm để tăng tốc độ
4. **Rate limiting**: Giới hạn tốc độ tìm kiếm để tránh bị chặn
5. **Xử lý CAPTCHA**: Phát hiện và xử lý CAPTCHA
6. **Đánh giá và xếp hạng kết quả**: Đánh giá và xếp hạng kết quả tìm kiếm

Mỗi thành phần này có thể được tối ưu hóa để cải thiện hiệu suất tổng thể.

## Giám sát hiệu suất

Bước đầu tiên để tối ưu hóa hiệu suất là giám sát hiệu suất hiện tại. WebSearchAgentLocal cung cấp `PerformanceMonitor` để giám sát hiệu suất:

```python
from deepresearch.src.deep_research_core.utils.performance_monitor import PerformanceMonitor

# Khởi tạo PerformanceMonitor
monitor = PerformanceMonitor(
    enabled=True,
    log_interval=60,  # Ghi log mỗi 60 giây
    memory_threshold=80.0,  # Cảnh báo khi sử dụng > 80% RAM
    cpu_threshold=80.0,  # Cảnh báo khi sử dụng > 80% CPU
    log_to_file=True,
    log_file="performance_log.json",
    trace_memory=True
)

# Bắt đầu giám sát
monitor.start()

# Gán vào agent
agent.performance_monitor = monitor

# Sử dụng decorator để đo thời gian thực hiện phương thức
@monitor.time_method("search")
def search_with_monitoring(query):
    return agent.search(query)

# Lấy tóm tắt hiệu suất
summary = monitor.get_performance_summary()
print(f"Sử dụng bộ nhớ: {summary['memory']['current']:.1f}%")
print(f"Sử dụng CPU: {summary['cpu']['current']:.1f}%")
```

## Tối ưu hóa bộ nhớ

### Giới hạn sử dụng bộ nhớ

Bạn có thể giới hạn sử dụng bộ nhớ bằng `ResourceLimiter`:

```python
from deepresearch.src.deep_research_core.utils.resource_limiter import ResourceLimiter

# Khởi tạo ResourceLimiter
limiter = ResourceLimiter(
    memory_limit=80.0,  # Giới hạn sử dụng bộ nhớ (%)
    cpu_limit=80.0,  # Giới hạn sử dụng CPU (%)
    max_concurrent_requests=10,  # Số lượng request đồng thời tối đa
    check_interval=1.0,  # Khoảng thời gian kiểm tra (giây)
    adaptive=True,  # Tự động điều chỉnh giới hạn
    gc_threshold=90.0,  # Ngưỡng chạy garbage collector (%)
    emergency_actions=True  # Thực hiện các hành động khẩn cấp
)

# Bắt đầu giám sát
limiter.start()

# Gán vào agent
agent.resource_limiter = limiter

# Sử dụng decorator để giới hạn tài nguyên cho hàm
@limiter.limit_resources
def search_with_limiting(query):
    return agent.search(query)
```

### Giảm kích thước cache

Bạn có thể giảm kích thước cache để giảm sử dụng bộ nhớ:

```python
from deepresearch.src.deep_research_core.utils.enhanced_cache import EnhancedWebSearchCache

# Khởi tạo EnhancedWebSearchCache với kích thước nhỏ hơn
custom_cache = EnhancedWebSearchCache(
    memory_limit=500,  # Giảm kích thước cache trong bộ nhớ
    disk_limit=5000,  # Giảm kích thước cache trên đĩa
    memory_ttl=1800,  # Giảm thời gian sống của cache trong bộ nhớ (30 phút)
    disk_ttl=43200,  # Giảm thời gian sống của cache trên đĩa (12 giờ)
    enable_compression=True  # Bật nén để giảm kích thước
)

# Gán cache tùy chỉnh
agent.smart_cache = custom_cache
```

### Giảm số lượng kết quả

Bạn có thể giảm số lượng kết quả để giảm sử dụng bộ nhớ:

```python
# Tìm kiếm với số lượng kết quả nhỏ hơn
results = agent.search("Python programming", num_results=5)
```

## Tối ưu hóa CPU

### Giới hạn sử dụng CPU

Bạn có thể giới hạn sử dụng CPU bằng `ResourceLimiter` (xem phần Tối ưu hóa bộ nhớ).

### Giảm độ sâu crawl

Bạn có thể giảm độ sâu crawl để giảm sử dụng CPU:

```python
# Tìm kiếm với độ sâu crawl nhỏ hơn
results = agent.search(
    "Python programming",
    method="crawlee",
    max_depth=1,  # Giảm độ sâu crawl
    max_pages=5  # Giảm số lượng trang
)
```

### Tắt các tính năng không cần thiết

Bạn có thể tắt các tính năng không cần thiết để giảm sử dụng CPU:

```python
# Khởi tạo với các tính năng không cần thiết bị tắt
agent = WebSearchAgentLocal(
    use_query_decomposer=False,  # Tắt phân tích truy vấn
    enable_plugins=False  # Tắt hệ thống plugin
)
```

## Tối ưu hóa tốc độ tìm kiếm

### Sử dụng xử lý bất đồng bộ

Bạn có thể sử dụng xử lý bất đồng bộ để tăng tốc độ tìm kiếm:

```python
from deepresearch.src.deep_research_core.utils.async_search_executor import AsyncSearchExecutor

# Khởi tạo AsyncSearchExecutor
executor = AsyncSearchExecutor(
    max_concurrent_searches=5,  # Số lượng tìm kiếm đồng thời tối đa
    max_concurrent_extractions=10,  # Số lượng trích xuất đồng thời tối đa
    timeout=30,  # Thời gian chờ tối đa cho mỗi tìm kiếm (giây)
    use_thread_pool=True,  # Sử dụng thread pool
    max_workers=None  # Số lượng worker tối đa (None = tự động)
)

# Bắt đầu executor
executor.start()

# Gán vào agent
agent.async_search_executor = executor

# Tích hợp vào WebSearchAgentLocal
from deepresearch.src.deep_research_core.utils.performance_integration import integrate_performance_features

integrate_performance_features(
    agent,
    enable_async_search=True
)
```

### Sử dụng cache hiệu quả

Bạn có thể sử dụng cache hiệu quả để tăng tốc độ tìm kiếm:

```python
# Khởi tạo với cache
agent = WebSearchAgentLocal(
    cache_ttl=3600,  # 1 giờ
    use_smart_cache=True
)

# Tìm kiếm với cache
results1 = agent.search("Python programming")  # Kết quả sẽ được cache

# Tìm kiếm lại (sẽ lấy từ cache)
results2 = agent.search("Python programming")
```

### Chọn phương thức tìm kiếm phù hợp

Bạn có thể chọn phương thức tìm kiếm phù hợp để tăng tốc độ tìm kiếm:

```python
# Tìm kiếm với SearXNG (nhanh hơn Crawlee)
results = agent.search("Python programming", method="searxng")

# Tìm kiếm với Crawlee (chậm hơn nhưng chi tiết hơn)
results = agent.search("Python programming", method="crawlee")

# Tìm kiếm tự động (chọn phương thức tốt nhất dựa trên truy vấn)
results = agent.search("Python programming", method="auto")
```

## Tối ưu hóa cache

### Cấu hình cache nâng cao

Bạn có thể cấu hình cache nâng cao để tối ưu hóa hiệu suất:

```python
from deepresearch.src.deep_research_core.utils.enhanced_cache import EnhancedWebSearchCache

# Khởi tạo EnhancedWebSearchCache với cấu hình nâng cao
custom_cache = EnhancedWebSearchCache(
    cache_dir="/path/to/cache",  # Thư mục cache
    memory_limit=1000,  # Kích thước cache trong bộ nhớ
    disk_limit=10000,  # Kích thước cache trên đĩa
    memory_ttl=3600,  # Thời gian sống của cache trong bộ nhớ (1 giờ)
    disk_ttl=86400,  # Thời gian sống của cache trên đĩa (1 ngày)
    similarity_threshold=0.8,  # Ngưỡng tương đồng
    enable_prefetching=True,  # Bật prefetching
    enable_compression=True,  # Bật nén
    enable_semantic_search=False  # Tắt tìm kiếm ngữ nghĩa
)

# Gán cache tùy chỉnh
agent.smart_cache = custom_cache
```

### Sử dụng cache cho các truy vấn tương tự

Bạn có thể sử dụng cache cho các truy vấn tương tự để tăng tốc độ tìm kiếm:

```python
# Tìm kiếm với truy vấn 1
results1 = agent.search("Python programming")

# Tìm kiếm với truy vấn tương tự (có thể sử dụng cache)
results2 = agent.search("Python programming language")
```

### Tùy chỉnh thời gian sống của cache

Bạn có thể tùy chỉnh thời gian sống của cache để cân bằng giữa hiệu suất và độ mới của kết quả:

```python
# Khởi tạo với thời gian sống của cache tùy chỉnh
agent = WebSearchAgentLocal(
    cache_ttl=1800  # 30 phút
)
```

## Xử lý bất đồng bộ

### Tích hợp AsyncSearchExecutor

Bạn có thể tích hợp AsyncSearchExecutor để thực hiện tìm kiếm bất đồng bộ:

```python
from deepresearch.src.deep_research_core.utils.async_search_executor import AsyncSearchExecutor

# Khởi tạo AsyncSearchExecutor
executor = AsyncSearchExecutor(
    max_concurrent_searches=5,
    max_concurrent_extractions=10,
    timeout=30
)

# Bắt đầu executor
executor.start()

# Gán vào agent
agent.async_search_executor = executor
```

### Sử dụng batch search

Bạn có thể sử dụng batch search để thực hiện nhiều tìm kiếm đồng thời:

```python
import asyncio

async def batch_search():
    # Tạo danh sách truy vấn
    queries = ["Python programming", "Java programming", "JavaScript programming"]
    
    # Thực hiện batch search
    results = await agent.async_search_executor.execute_batch_search_async(
        agent.search,
        queries,
        num_results=5
    )
    
    return results

# Chạy batch search
loop = asyncio.get_event_loop()
results = loop.run_until_complete(batch_search())
```

### Sử dụng batch content extraction

Bạn có thể sử dụng batch content extraction để trích xuất nội dung từ nhiều URL đồng thời:

```python
import asyncio

async def batch_extract():
    # Tạo danh sách URL
    urls = ["https://example.com/1", "https://example.com/2", "https://example.com/3"]
    
    # Thực hiện batch extraction
    results = await agent.async_search_executor.extract_batch_content_async(
        agent.extract_content,
        urls
    )
    
    return results

# Chạy batch extraction
loop = asyncio.get_event_loop()
results = loop.run_until_complete(batch_extract())
```

## Tham số tự điều chỉnh

### Tích hợp AdaptiveParameterManager

Bạn có thể tích hợp AdaptiveParameterManager để tự động điều chỉnh tham số:

```python
from deepresearch.src.deep_research_core.utils.adaptive_parameters import AdaptiveParameterManager

# Khởi tạo AdaptiveParameterManager
manager = AdaptiveParameterManager(
    config_file="adaptive_params.json",
    auto_save=True,
    auto_adjust_interval=300.0  # 5 phút
)

# Thêm tham số
manager.add_parameter(
    name="max_concurrent_searches",
    initial_value=5,
    min_value=1,
    max_value=10,
    step_size=1,
    optimization_goal="maximize",
    is_integer=True
)

manager.add_parameter(
    name="timeout",
    initial_value=30.0,
    min_value=5.0,
    max_value=60.0,
    step_size=5.0,
    optimization_goal="minimize",
    is_integer=False
)

# Bắt đầu điều chỉnh tự động
manager.start_auto_adjust()

# Gán vào agent
agent.adaptive_parameter_manager = manager
```

### Cập nhật hiệu suất

Bạn có thể cập nhật hiệu suất để AdaptiveParameterManager điều chỉnh tham số:

```python
# Tìm kiếm và đo thời gian
start_time = time.time()
results = agent.search("Python programming")
end_time = time.time()
execution_time = end_time - start_time

# Tính điểm hiệu suất (điểm cao hơn = tốt hơn)
performance_score = 1.0 / max(0.1, execution_time)

# Cập nhật hiệu suất
agent.adaptive_parameter_manager.update_performance("max_concurrent_searches", performance_score)
```

## Cấu hình tối ưu

### Cấu hình cho máy có RAM thấp

```python
# Khởi tạo với cấu hình cho máy có RAM thấp
agent = WebSearchAgentLocal(
    cache_ttl=1800,  # 30 phút
    api_search_config={
        "searx_url": "http://localhost:8080"
    },
    crawlee_search_config={
        "max_depth": 1,
        "max_pages_per_url": 2,
        "max_urls": 3,
        "timeout": 15
    },
    content_extractor_config={
        "extract_links": False,
        "extract_images": False,
        "max_content_length": 5000,
        "timeout": 10
    }
)

# Tích hợp ResourceLimiter với giới hạn thấp
from deepresearch.src.deep_research_core.utils.resource_limiter import ResourceLimiter

limiter = ResourceLimiter(
    memory_limit=70.0,
    cpu_limit=70.0,
    max_concurrent_requests=5
)
limiter.start()

agent.resource_limiter = limiter
```

### Cấu hình cho máy có CPU mạnh

```python
# Khởi tạo với cấu hình cho máy có CPU mạnh
agent = WebSearchAgentLocal(
    cache_ttl=3600,  # 1 giờ
    api_search_config={
        "searx_url": "http://localhost:8080"
    },
    crawlee_search_config={
        "max_depth": 3,
        "max_pages_per_url": 5,
        "max_urls": 10,
        "timeout": 30
    },
    content_extractor_config={
        "extract_links": True,
        "extract_images": True,
        "max_content_length": 20000,
        "timeout": 20
    }
)

# Tích hợp AsyncSearchExecutor với nhiều worker
from deepresearch.src.deep_research_core.utils.async_search_executor import AsyncSearchExecutor

executor = AsyncSearchExecutor(
    max_concurrent_searches=10,
    max_concurrent_extractions=20,
    timeout=30,
    max_workers=16  # Nhiều worker hơn
)
executor.start()

agent.async_search_executor = executor
```

### Cấu hình cân bằng

```python
# Khởi tạo với cấu hình cân bằng
agent = WebSearchAgentLocal(
    cache_ttl=3600,  # 1 giờ
    api_search_config={
        "searx_url": "http://localhost:8080"
    },
    crawlee_search_config={
        "max_depth": 2,
        "max_pages_per_url": 3,
        "max_urls": 5,
        "timeout": 20
    },
    content_extractor_config={
        "extract_links": True,
        "extract_images": False,
        "max_content_length": 10000,
        "timeout": 15
    }
)

# Tích hợp tất cả các tính năng hiệu suất với cấu hình cân bằng
from deepresearch.src.deep_research_core.utils.performance_integration import integrate_performance_features

integrate_performance_features(
    agent,
    enable_monitoring=True,
    enable_async_search=True,
    enable_resource_limiting=True,
    enable_adaptive_parameters=True,
    config={
        "monitoring": {
            "log_interval": 60,
            "memory_threshold": 80.0,
            "cpu_threshold": 80.0
        },
        "async_search": {
            "max_concurrent_searches": 5,
            "max_concurrent_extractions": 10
        },
        "resource_limiting": {
            "memory_limit": 80.0,
            "cpu_limit": 80.0,
            "max_concurrent_requests": 10
        },
        "adaptive_parameters": {
            "auto_adjust": True,
            "auto_adjust_interval": 300.0
        }
    }
)
```

## Đo lường hiệu suất

### Đo thời gian thực hiện

```python
import time

# Đo thời gian thực hiện tìm kiếm
start_time = time.time()
results = agent.search("Python programming")
end_time = time.time()
execution_time = end_time - start_time

print(f"Thời gian thực hiện: {execution_time:.2f} giây")
```

### Đo sử dụng bộ nhớ

```python
import psutil
import os

# Đo sử dụng bộ nhớ
process = psutil.Process(os.getpid())
memory_before = process.memory_info().rss / 1024 / 1024  # MB

results = agent.search("Python programming")

memory_after = process.memory_info().rss / 1024 / 1024  # MB
memory_used = memory_after - memory_before

print(f"Bộ nhớ sử dụng: {memory_used:.2f} MB")
```

### Đo hiệu suất cache

```python
import time

# Đo hiệu suất cache
# Tìm kiếm lần đầu (không có cache)
start_time = time.time()
results1 = agent.search("Python programming")
end_time = time.time()
execution_time_no_cache = end_time - start_time

# Tìm kiếm lần thứ hai (có cache)
start_time = time.time()
results2 = agent.search("Python programming")
end_time = time.time()
execution_time_with_cache = end_time - start_time

# Tính tỷ lệ tăng tốc
speedup = execution_time_no_cache / execution_time_with_cache

print(f"Thời gian không cache: {execution_time_no_cache:.2f} giây")
print(f"Thời gian có cache: {execution_time_with_cache:.2f} giây")
print(f"Tỷ lệ tăng tốc: {speedup:.2f}x")
```
