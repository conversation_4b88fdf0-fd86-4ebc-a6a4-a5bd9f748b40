"""
Evaluate CoTRAG performance on real-world datasets.

This script evaluates the performance of CoTRAG on real-world datasets,
including English and Vietnamese datasets.
"""

import os
import sys
import json
import time
import argparse
import pandas as pd
from typing import Dict, Any, List, Optional
from tqdm import tqdm

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.reasoning.cotrag_enhanced import CoTRAGEnhanced
from src.deep_research_core.rag.sqlite_vector_rag import SQLiteVectorRAG
from src.deep_research_core.evaluation.cotrag_error_analyzer import CoTRAGErrorAnalyzer

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Evaluate CoTRAG performance on real-world datasets")
    
    parser.add_argument("--dataset", type=str, required=True, help="Path to the dataset CSV file")
    parser.add_argument("--vector-store", type=str, required=True, help="Path to the vector store database")
    parser.add_argument("--output", type=str, required=True, help="Path to the output JSON file")
    parser.add_argument("--language", type=str, default="en", choices=["en", "vi"], help="Language of the dataset")
    parser.add_argument("--provider", type=str, default="openrouter", help="Provider to use")
    parser.add_argument("--model", type=str, default="anthropic/claude-3-opus", help="Model to use")
    parser.add_argument("--vietnamese-embedding-model", type=str, default="phobert", help="Vietnamese embedding model to use")
    parser.add_argument("--use-adaptive-learning", action="store_true", help="Use adaptive learning")
    parser.add_argument("--use-advanced-strategies", action="store_true", help="Use advanced strategies")
    parser.add_argument("--num-samples", type=int, default=None, help="Number of samples to evaluate (default: all)")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    
    return parser.parse_args()

def load_dataset(dataset_path: str, num_samples: Optional[int] = None) -> pd.DataFrame:
    """
    Load the dataset from a CSV file.
    
    Args:
        dataset_path: Path to the dataset CSV file
        num_samples: Number of samples to load (default: all)
        
    Returns:
        DataFrame containing the dataset
    """
    df = pd.read_csv(dataset_path)
    
    if num_samples is not None:
        df = df.sample(min(num_samples, len(df)))
    
    return df

def evaluate_cotrag(
    cotrag: CoTRAGEnhanced,
    dataset: pd.DataFrame,
    verbose: bool = False
) -> List[Dict[str, Any]]:
    """
    Evaluate CoTRAG on a dataset.
    
    Args:
        cotrag: CoTRAGEnhanced instance
        dataset: DataFrame containing the dataset
        verbose: Whether to print verbose output
        
    Returns:
        List of evaluation results
    """
    results = []
    
    for i, row in tqdm(dataset.iterrows(), total=len(dataset), desc="Evaluating"):
        query = row["query"]
        expected_answer = row.get("expected_answer", None)
        
        if verbose:
            print(f"\nProcessing query: {query}")
        
        start_time = time.time()
        
        try:
            # Process the query
            result = cotrag.process(
                query=query,
                expected_answer=expected_answer,
                auto_feedback=True
            )
            
            # Calculate metrics
            metrics = calculate_metrics(result, expected_answer)
            
            # Add to results
            results.append({
                "query": query,
                "expected_answer": expected_answer,
                "answer": result["answer"],
                "metrics": metrics,
                "latency": result.get("latency", time.time() - start_time),
                "total_latency": result.get("total_latency", time.time() - start_time),
                "error_analysis": result.get("error_analysis", None),
                "weights": result.get("weights", None),
                "advanced_strategies": result.get("advanced_strategies", None)
            })
            
            if verbose:
                print(f"Answer: {result['answer'][:100]}...")
                print(f"Metrics: {metrics}")
        
        except Exception as e:
            if verbose:
                print(f"Error processing query: {str(e)}")
            
            # Add error to results
            results.append({
                "query": query,
                "expected_answer": expected_answer,
                "error": str(e),
                "latency": time.time() - start_time
            })
    
    return results

def calculate_metrics(result: Dict[str, Any], expected_answer: Optional[str]) -> Dict[str, float]:
    """
    Calculate evaluation metrics for a result.
    
    Args:
        result: Result from CoTRAG.process()
        expected_answer: Expected answer (if available)
        
    Returns:
        Dictionary of metrics
    """
    metrics = {}
    
    # Use evaluation metrics if available
    if "evaluation" in result and "metrics" in result["evaluation"]:
        metrics.update(result["evaluation"]["metrics"])
    
    # Use error analysis if available
    if "error_analysis" in result:
        error_analysis = result["error_analysis"]
        
        if "document_quality" in error_analysis:
            metrics["document_quality"] = error_analysis["document_quality"]["overall_quality"]
        
        if "reasoning_quality" in error_analysis:
            metrics["reasoning_quality"] = error_analysis["reasoning_quality"]["overall_quality"]
    
    # Calculate latency metrics
    metrics["latency"] = result.get("latency", 0)
    metrics["total_latency"] = result.get("total_latency", 0)
    
    return metrics

def calculate_aggregate_metrics(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Calculate aggregate metrics across all results.
    
    Args:
        results: List of evaluation results
        
    Returns:
        Dictionary of aggregate metrics
    """
    # Filter out results with errors
    valid_results = [r for r in results if "error" not in r]
    
    # Calculate success rate
    success_rate = len(valid_results) / len(results) if results else 0
    
    # Calculate average metrics
    avg_metrics = {}
    
    if valid_results:
        # Get all metric keys
        metric_keys = set()
        for result in valid_results:
            if "metrics" in result:
                metric_keys.update(result["metrics"].keys())
        
        # Calculate average for each metric
        for key in metric_keys:
            values = [r["metrics"].get(key, 0) for r in valid_results if "metrics" in r]
            avg_metrics[f"avg_{key}"] = sum(values) / len(values) if values else 0
    
    # Calculate weight statistics
    weight_stats = {}
    
    if valid_results:
        cot_weights = [r["weights"]["cot_weight"] for r in valid_results if "weights" in r]
        rag_weights = [r["weights"]["rag_weight"] for r in valid_results if "weights" in r]
        
        if cot_weights:
            weight_stats["avg_cot_weight"] = sum(cot_weights) / len(cot_weights)
            weight_stats["min_cot_weight"] = min(cot_weights)
            weight_stats["max_cot_weight"] = max(cot_weights)
        
        if rag_weights:
            weight_stats["avg_rag_weight"] = sum(rag_weights) / len(rag_weights)
            weight_stats["min_rag_weight"] = min(rag_weights)
            weight_stats["max_rag_weight"] = max(rag_weights)
    
    # Calculate advanced strategies statistics
    strategy_stats = {}
    
    if valid_results:
        results_with_strategies = [r for r in valid_results if "advanced_strategies" in r]
        
        if results_with_strategies:
            # Count query expansion usage
            query_expansion_count = sum(
                1 for r in results_with_strategies
                if "expanded_queries" in r["advanced_strategies"]
            )
            
            # Count iterative retrieval usage
            iterative_retrieval_count = sum(
                1 for r in results_with_strategies
                if "refined_query" in r["advanced_strategies"]
            )
            
            # Count fallback usage
            fallback_count = sum(1 for r in valid_results if "fallback" in r)
            
            strategy_stats["query_expansion_rate"] = query_expansion_count / len(results_with_strategies)
            strategy_stats["iterative_retrieval_rate"] = iterative_retrieval_count / len(results_with_strategies)
            strategy_stats["fallback_rate"] = fallback_count / len(valid_results)
    
    # Calculate error analysis statistics
    error_stats = {}
    
    if valid_results:
        results_with_error_analysis = [r for r in valid_results if "error_analysis" in r]
        
        if results_with_error_analysis:
            # Count primary error sources
            error_sources = {}
            
            for r in results_with_error_analysis:
                if "error_source" in r["error_analysis"]:
                    source = r["error_analysis"]["error_source"]["primary_source"]
                    error_sources[source] = error_sources.get(source, 0) + 1
            
            for source, count in error_sources.items():
                error_stats[f"{source}_error_rate"] = count / len(results_with_error_analysis)
    
    return {
        "total_queries": len(results),
        "successful_queries": len(valid_results),
        "success_rate": success_rate,
        "metrics": avg_metrics,
        "weight_stats": weight_stats,
        "strategy_stats": strategy_stats,
        "error_stats": error_stats
    }

def main():
    """Main function."""
    args = parse_args()
    
    # Load the dataset
    print(f"Loading dataset from {args.dataset}...")
    dataset = load_dataset(args.dataset, args.num_samples)
    print(f"Loaded {len(dataset)} samples")
    
    # Initialize vector store
    print(f"Initializing vector store from {args.vector_store}...")
    vector_store = SQLiteVectorRAG(
        db_path=args.vector_store,
        embedding_model="all-MiniLM-L6-v2"
    )
    
    # Initialize CoTRAGEnhanced
    print("Initializing CoTRAGEnhanced...")
    cotrag = CoTRAGEnhanced(
        provider=args.provider,
        model=args.model,
        vector_store=vector_store,
        language=args.language,
        vietnamese_embedding_model=args.vietnamese_embedding_model,
        use_adaptive_learning=args.use_adaptive_learning,
        use_advanced_strategies=args.use_advanced_strategies,
        verbose=args.verbose
    )
    
    # Evaluate CoTRAG
    print("Evaluating CoTRAG...")
    results = evaluate_cotrag(cotrag, dataset, args.verbose)
    
    # Calculate aggregate metrics
    print("Calculating aggregate metrics...")
    aggregate_metrics = calculate_aggregate_metrics(results)
    
    # Save results
    print(f"Saving results to {args.output}...")
    with open(args.output, "w") as f:
        json.dump({
            "results": results,
            "aggregate_metrics": aggregate_metrics,
            "config": {
                "language": args.language,
                "provider": args.provider,
                "model": args.model,
                "vietnamese_embedding_model": args.vietnamese_embedding_model if args.language == "vi" else None,
                "use_adaptive_learning": args.use_adaptive_learning,
                "use_advanced_strategies": args.use_advanced_strategies
            }
        }, f, indent=2)
    
    # Print aggregate metrics
    print("\nAggregate Metrics:")
    print(f"Total Queries: {aggregate_metrics['total_queries']}")
    print(f"Successful Queries: {aggregate_metrics['successful_queries']}")
    print(f"Success Rate: {aggregate_metrics['success_rate']:.2f}")
    
    print("\nAverage Metrics:")
    for key, value in aggregate_metrics["metrics"].items():
        print(f"{key}: {value:.4f}")
    
    if aggregate_metrics["weight_stats"]:
        print("\nWeight Statistics:")
        for key, value in aggregate_metrics["weight_stats"].items():
            print(f"{key}: {value:.4f}")
    
    if aggregate_metrics["strategy_stats"]:
        print("\nStrategy Statistics:")
        for key, value in aggregate_metrics["strategy_stats"].items():
            print(f"{key}: {value:.4f}")
    
    if aggregate_metrics["error_stats"]:
        print("\nError Statistics:")
        for key, value in aggregate_metrics["error_stats"].items():
            print(f"{key}: {value:.4f}")

if __name__ == "__main__":
    main()
