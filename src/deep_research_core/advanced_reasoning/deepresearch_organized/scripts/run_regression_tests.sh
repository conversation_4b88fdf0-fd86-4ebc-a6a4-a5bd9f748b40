#!/bin/bash
# <PERSON>ript to run regression tests for Deep Research Core
# Usage: ./scripts/run_regression_tests.sh [options]
# Options:
#   --verbose        Show verbose output
#   --parallel       Run tests in parallel
#   --report         Generate HTML report
#   --compare        Compare with previous results
#   --save           Save results for future comparison
#   --notify         Send notification on failure
#   --help           Show this help message

# Set default values
VERBOSE=false
PARALLEL=false
REPORT=false
COMPARE=false
SAVE=false
NOTIFY=false

# Parse command line arguments
for arg in "$@"
do
    case $arg in
        --verbose)
        VERBOSE=true
        shift
        ;;
        --parallel)
        PARALLEL=true
        shift
        ;;
        --report)
        REPORT=true
        shift
        ;;
        --compare)
        COMPARE=true
        shift
        ;;
        --save)
        SAVE=true
        shift
        ;;
        --notify)
        NOTIFY=true
        shift
        ;;
        --help)
        echo "Usage: ./scripts/run_regression_tests.sh [options]"
        echo "Options:"
        echo "  --verbose        Show verbose output"
        echo "  --parallel       Run tests in parallel"
        echo "  --report         Generate HTML report"
        echo "  --compare        Compare with previous results"
        echo "  --save           Save results for future comparison"
        echo "  --notify         Send notification on failure"
        echo "  --help           Show this help message"
        exit 0
        ;;
        *)
        # Unknown option
        shift
        ;;
    esac
done

# Create results directory if it doesn't exist
RESULTS_DIR="test_results/regression"
mkdir -p $RESULTS_DIR

# Set timestamp for this run
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
RESULTS_FILE="$RESULTS_DIR/regression_results_$TIMESTAMP.json"
REPORT_FILE="$RESULTS_DIR/regression_report_$TIMESTAMP.html"
LATEST_RESULTS_FILE="$RESULTS_DIR/regression_results_latest.json"

# Build the pytest command
PYTEST_CMD="python -m pytest tests/regression -v -k regression --json-report --json-report-file=$RESULTS_FILE"

# Add verbosity
if [ "$VERBOSE" = true ]; then
    PYTEST_CMD="$PYTEST_CMD -v"
fi

# Add parallel execution
if [ "$PARALLEL" = true ]; then
    # Get number of CPU cores and leave one free
    NUM_CORES=$(nproc)
    NUM_CORES=$((NUM_CORES - 1))
    if [ $NUM_CORES -lt 1 ]; then
        NUM_CORES=1
    fi
    PYTEST_CMD="$PYTEST_CMD -n $NUM_CORES"
fi

# Add HTML report
if [ "$REPORT" = true ]; then
    PYTEST_CMD="$PYTEST_CMD --html=$REPORT_FILE --self-contained-html"
fi

# Print the command being run
echo "Running: $PYTEST_CMD"

# Run the tests
START_TIME=$(date +%s)
eval $PYTEST_CMD
EXIT_CODE=$?
END_TIME=$(date +%s)

# Print summary
DURATION=$((END_TIME - START_TIME))
echo -e "\nRegression tests completed in $DURATION seconds"
echo "Exit code: $EXIT_CODE"
echo "Results saved to: $RESULTS_FILE"

# Save results for future comparison
if [ "$SAVE" = true ]; then
    cp $RESULTS_FILE $LATEST_RESULTS_FILE
    echo "Results saved for future comparison: $LATEST_RESULTS_FILE"
fi

# Compare with previous results
if [ "$COMPARE" = true ]; then
    if [ -f "$LATEST_RESULTS_FILE" ]; then
        echo -e "\nComparing with previous results..."
        python -c "
import json
import sys

try:
    with open('$LATEST_RESULTS_FILE', 'r') as f:
        previous = json.load(f)
    
    with open('$RESULTS_FILE', 'r') as f:
        current = json.load(f)
    
    prev_summary = previous.get('summary', {})
    curr_summary = current.get('summary', {})
    
    prev_total = prev_summary.get('total', 0)
    curr_total = curr_summary.get('total', 0)
    
    prev_passed = prev_summary.get('passed', 0)
    curr_passed = curr_summary.get('passed', 0)
    
    prev_failed = prev_summary.get('failed', 0)
    curr_failed = curr_summary.get('failed', 0)
    
    prev_skipped = prev_summary.get('skipped', 0)
    curr_skipped = curr_summary.get('skipped', 0)
    
    print(f'Previous run: {prev_total} tests, {prev_passed} passed, {prev_failed} failed, {prev_skipped} skipped')
    print(f'Current run:  {curr_total} tests, {curr_passed} passed, {curr_failed} failed, {curr_skipped} skipped')
    
    if prev_passed > curr_passed:
        print(f'WARNING: {prev_passed - curr_passed} tests that passed before are now failing!')
        sys.exit(1)
    elif curr_passed > prev_passed:
        print(f'IMPROVEMENT: {curr_passed - prev_passed} more tests are passing now!')
    else:
        print('No change in test results.')
    
except Exception as e:
    print(f'Error comparing results: {str(e)}')
    sys.exit(1)
"
        COMPARE_EXIT_CODE=$?
        if [ $COMPARE_EXIT_CODE -ne 0 ]; then
            echo "Comparison failed!"
            if [ "$NOTIFY" = true ]; then
                echo "Sending notification..."
                # Add notification command here (e.g., email, Slack, etc.)
            fi
        fi
    else
        echo "No previous results found for comparison."
    fi
fi

# Generate HTML report if requested
if [ "$REPORT" = true ]; then
    echo "HTML report generated: $REPORT_FILE"
fi

# Send notification on failure
if [ "$NOTIFY" = true ] && [ $EXIT_CODE -ne 0 ]; then
    echo "Sending notification..."
    # Add notification command here (e.g., email, Slack, etc.)
fi

exit $EXIT_CODE
