#!/bin/bash
# Script to run tests for Deep Research Core
# Usage: ./scripts/run_tests.sh [options]
# Options:
#   --unit           Run only unit tests
#   --integration    Run only integration tests
#   --performance    Run only performance tests
#   --coverage       Generate coverage report
#   --verbose        Show verbose output
#   --parallel       Run tests in parallel
#   --benchmark      Run benchmark tests
#   --xml            Generate XML report
#   --html           Generate HTML report
#   --all            Run all tests (default)
#   --optimize       Enable performance optimizations
#   --cache          Use test cache
#   --timeout=SEC    Set test timeout in seconds
#   --workers=N      Set number of parallel workers

# Set default values
RUN_UNIT=false
RUN_INTEGRATION=false
RUN_PERFORMANCE=false
RUN_COVERAGE=false
VERBOSE=false
PARALLEL=false
BENCHMARK=false
XML_REPORT=false
HTML_REPORT=false
RUN_ALL=false
OPTIMIZE=false
USE_CACHE=false
TIMEOUT=300
WORKERS=0

# Parse command line arguments
for arg in "$@"
do
    case $arg in
        --unit)
        RUN_UNIT=true
        shift
        ;;
        --integration)
        RUN_INTEGRATION=true
        shift
        ;;
        --performance)
        RUN_PERFORMANCE=true
        shift
        ;;
        --coverage)
        RUN_COVERAGE=true
        shift
        ;;
        --verbose)
        VERBOSE=true
        shift
        ;;
        --parallel)
        PARALLEL=true
        shift
        ;;
        --benchmark)
        BENCHMARK=true
        shift
        ;;
        --xml)
        XML_REPORT=true
        shift
        ;;
        --html)
        HTML_REPORT=true
        shift
        ;;
        --all)
        RUN_ALL=true
        shift
        ;;
        --optimize)
        OPTIMIZE=true
        shift
        ;;
        --cache)
        USE_CACHE=true
        shift
        ;;
        --timeout=*)
        TIMEOUT="${arg#*=}"
        shift
        ;;
        --workers=*)
        WORKERS="${arg#*=}"
        shift
        ;;
        *)
        # Unknown option
        shift
        ;;
    esac
done

# If no test type is specified, run all tests
if [ "$RUN_UNIT" = false ] && [ "$RUN_INTEGRATION" = false ] && [ "$RUN_PERFORMANCE" = false ] && [ "$BENCHMARK" = false ] && [ "$RUN_ALL" = false ]; then
    RUN_ALL=true
fi

# Build the pytest command
PYTEST_CMD="python -m pytest"

# Add verbosity
if [ "$VERBOSE" = true ]; then
    PYTEST_CMD="$PYTEST_CMD -v"
fi

# Add parallel execution
if [ "$PARALLEL" = true ]; then
    # Get number of CPU cores and leave one free
    if [ $WORKERS -gt 0 ]; then
        NUM_CORES=$WORKERS
    else
        NUM_CORES=$(nproc)
        NUM_CORES=$((NUM_CORES - 1))
        if [ $NUM_CORES -lt 1 ]; then
            NUM_CORES=1
        fi
    fi
    PYTEST_CMD="$PYTEST_CMD -n $NUM_CORES"
fi

# Add optimization options
if [ "$OPTIMIZE" = true ]; then
    PYTEST_CMD="$PYTEST_CMD --durations=10 --durations-min=1.0 --no-header --tb=native"

    # Use pytest-xdist load balancing
    if [ "$PARALLEL" = true ]; then
        PYTEST_CMD="$PYTEST_CMD --dist=loadscope"
    fi
fi

# Add cache options
if [ "$USE_CACHE" = true ]; then
    PYTEST_CMD="$PYTEST_CMD --lf --cache-clear=false"
fi

# Add timeout
PYTEST_CMD="$PYTEST_CMD --timeout=$TIMEOUT"

# Add coverage
if [ "$RUN_COVERAGE" = true ]; then
    PYTEST_CMD="$PYTEST_CMD --cov=src/deep_research_core"

    if [ "$XML_REPORT" = true ]; then
        PYTEST_CMD="$PYTEST_CMD --cov-report=xml"
    fi

    if [ "$HTML_REPORT" = true ]; then
        PYTEST_CMD="$PYTEST_CMD --cov-report=html"
    fi
fi

# Determine which tests to run
if [ "$RUN_UNIT" = true ]; then
    PYTEST_CMD="$PYTEST_CMD tests/unit/"
elif [ "$RUN_INTEGRATION" = true ]; then
    PYTEST_CMD="$PYTEST_CMD tests/integration/"
elif [ "$RUN_PERFORMANCE" = true ]; then
    PYTEST_CMD="$PYTEST_CMD -k performance"
elif [ "$BENCHMARK" = true ]; then
    PYTEST_CMD="$PYTEST_CMD --benchmark-only --benchmark-json=./benchmark.json"
elif [ "$RUN_ALL" = true ]; then
    PYTEST_CMD="$PYTEST_CMD tests/"
fi

# Print the command being run
echo "Running: $PYTEST_CMD"

# Run the tests
START_TIME=$(date +%s)
eval $PYTEST_CMD
EXIT_CODE=$?
END_TIME=$(date +%s)

# Print summary
DURATION=$((END_TIME - START_TIME))
echo -e "\nTests completed in $DURATION seconds"
echo "Exit code: $EXIT_CODE"

if [ "$RUN_COVERAGE" = true ]; then
    echo -e "\nCoverage report generated"
    if [ "$HTML_REPORT" = true ]; then
        echo "HTML report available in htmlcov/index.html"
    fi
    if [ "$XML_REPORT" = true ]; then
        echo "XML report available in coverage.xml"
    fi
fi

if [ "$BENCHMARK" = true ]; then
    echo -e "\nBenchmark results available in benchmark.json"
fi

exit $EXIT_CODE
