/**
 * Simple test script for SearXNG-Crawlee integration
 */

const { PlaywrightCrawler } = require('crawlee');
const fetch = require('node-fetch');
const fs = require('fs');

// Hàm tìm kiếm với SearXNG
async function searchWithSearXNG(query, numResults = 5) {
    try {
        console.log(`Tìm kiếm với SearXNG: ${query}`);
        const response = await fetch(`http://localhost:8080/search?q=${encodeURIComponent(query)}&format=json&language=auto&safesearch=0&count=${numResults}`);
        const data = await response.json();
        
        console.log(`Tìm thấy ${data.results.length} kết quả từ SearXNG`);
        
        return {
            success: true,
            results: data.results.map(result => ({
                url: result.url,
                title: result.title,
                snippet: result.content || ''
            }))
        };
    } catch (error) {
        console.error(`Lỗi khi tìm kiếm với SearXNG: ${error.message}`);
        return {
            success: false,
            error: error.message,
            results: []
        };
    }
}

// Hàm crawl URL với Playwright
async function crawlWithPlaywright(urls, maxDepth = 1, maxPages = 3, timeout = 30000) {
    console.log(`Crawl ${urls.length} URL với Playwright`);
    console.log(`Độ sâu tối đa: ${maxDepth}`);
    console.log(`Số trang tối đa: ${maxPages}`);
    console.log(`Thời gian chờ: ${timeout}ms`);
    
    // Khởi tạo mảng kết quả
    const results = [];
    
    // Khởi tạo crawler
    const crawler = new PlaywrightCrawler({
        maxRequestsPerCrawl: maxPages * urls.length,
        requestHandlerTimeoutSecs: timeout / 1000,
        maxRequestRetries: 2,
        navigationTimeoutSecs: timeout / 1000,
        
        // Hàm xử lý mỗi trang
        async requestHandler({ request, page, enqueueLinks }) {
            console.log(`Đang xử lý: ${request.url}`);
            
            try {
                // Đợi trang tải xong
                await page.waitForLoadState('networkidle');
                
                // Lấy tiêu đề
                const title = await page.title();
                
                // Lấy nội dung
                const content = await page.evaluate(() => {
                    // Tìm phần tử main hoặc article
                    const mainElement = document.querySelector('main') || 
                                        document.querySelector('article') || 
                                        document.querySelector('.content, .main-content, .post-content, .article-content');
                    
                    if (mainElement) {
                        return mainElement.innerText;
                    } else {
                        // Nếu không tìm thấy, lấy tất cả văn bản
                        return document.body.innerText;
                    }
                });
                
                // Lưu kết quả
                results.push({
                    url: request.url,
                    title: title,
                    content: content.substring(0, 1000), // Giới hạn độ dài nội dung để dễ đọc
                    depth: request.userData.depth
                });
                
                // Nếu chưa đạt độ sâu tối đa, tiếp tục crawl các link
                if (request.userData.depth < maxDepth) {
                    await enqueueLinks({
                        strategy: 'same-domain',
                        transformRequestFunction: (req) => {
                            req.userData = { 
                                ...request.userData,
                                depth: request.userData.depth + 1
                            };
                            return req;
                        },
                    });
                }
            } catch (error) {
                console.error(`Lỗi khi xử lý ${request.url}: ${error.message}`);
            }
        },
        
        // Cấu hình Playwright
        browserPoolOptions: {
            useFingerprints: true,
            fingerprintOptions: {
                fingerprintGeneratorOptions: {
                    browsers: ['chrome'],
                    operatingSystems: ['windows', 'macos', 'linux'],
                },
            },
        },
    });
    
    // Thêm các URL vào hàng đợi
    for (const url of urls) {
        await crawler.addRequests([{
            url: url,
            userData: {
                depth: 0
            }
        }]);
    }
    
    // Chạy crawler
    await crawler.run();
    
    console.log(`Đã crawl xong ${results.length} trang`);
    
    return {
        success: true,
        results: results
    };
}

// Hàm tìm kiếm với SearXNG-Crawlee
async function searchWithSearXNGCrawlee(query, numResults = 5, maxDepth = 1, maxPages = 3, timeout = 30000) {
    try {
        console.log("\n================================================================================");
        console.log(`Tìm kiếm với SearXNG-Crawlee: ${query}`);
        console.log("================================================================================\n");
        
        // Bước 1: Tìm kiếm với SearXNG để lấy danh sách URL ban đầu
        const searxngResults = await searchWithSearXNG(query, numResults * 2);
        
        if (!searxngResults.success || searxngResults.results.length === 0) {
            console.error("SearXNG tìm kiếm thất bại hoặc không trả về kết quả");
            return {
                success: false,
                error: "SearXNG tìm kiếm thất bại hoặc không trả về kết quả",
                query: query,
                results: []
            };
        }
        
        // Bước 2: Lấy danh sách URL từ kết quả SearXNG
        const urls = searxngResults.results.map(result => result.url);
        console.log(`Tìm thấy ${urls.length} URL từ SearXNG`);
        
        // Hiển thị một số URL đầu tiên
        console.log("Các URL đầu tiên:");
        urls.slice(0, 3).forEach((url, index) => {
            console.log(`  ${index + 1}. ${url}`);
        });
        
        // Bước 3: Sử dụng Crawlee để crawl các URL
        console.log(`\nCrawl ${Math.min(urls.length, numResults)} URL với Crawlee`);
        const crawleeResults = await crawlWithPlaywright(urls.slice(0, numResults), maxDepth, maxPages, timeout);
        
        if (!crawleeResults.success || crawleeResults.results.length === 0) {
            console.error("Crawlee crawl thất bại hoặc không trả về kết quả");
            return {
                success: false,
                error: "Crawlee crawl thất bại hoặc không trả về kết quả",
                query: query,
                results: searxngResults.results // Trả về kết quả từ SearXNG nếu Crawlee thất bại
            };
        }
        
        // Bước 4: Kết hợp kết quả từ SearXNG và Crawlee
        const combinedResults = [];
        
        // Thêm kết quả từ Crawlee
        for (const result of crawleeResults.results) {
            combinedResults.push({
                title: result.title,
                url: result.url,
                content: result.content,
                depth: result.depth
            });
        }
        
        // Thêm kết quả từ SearXNG nếu chưa đủ
        if (combinedResults.length < numResults) {
            for (const result of searxngResults.results) {
                // Kiểm tra xem URL đã có trong kết quả chưa
                if (!combinedResults.some(r => r.url === result.url)) {
                    combinedResults.push({
                        title: result.title,
                        url: result.url,
                        snippet: result.snippet
                    });
                    
                    // Nếu đã đủ kết quả thì dừng
                    if (combinedResults.length >= numResults) {
                        break;
                    }
                }
            }
        }
        
        // Hiển thị kết quả
        console.log("\n================================================================================");
        console.log("Kết quả tìm kiếm SearXNG-Crawlee");
        console.log("================================================================================\n");
        
        console.log(`Tìm thấy ${combinedResults.length} kết quả`);
        
        combinedResults.forEach((result, index) => {
            console.log(`\nKết quả ${index + 1}:`);
            console.log(`Tiêu đề: ${result.title}`);
            console.log(`URL: ${result.url}`);
            
            if (result.content) {
                const contentPreview = result.content.length > 200 ? 
                    result.content.substring(0, 200) + "..." : 
                    result.content;
                console.log(`Nội dung: ${contentPreview}`);
            } else if (result.snippet) {
                console.log(`Đoạn trích: ${result.snippet}`);
            }
            
            if (result.depth !== undefined) {
                console.log(`Độ sâu: ${result.depth}`);
            }
        });
        
        // Trả về kết quả
        return {
            success: true,
            query: query,
            results: combinedResults,
            engine: "searxng_crawlee",
            search_method: "searxng_crawlee",
            timestamp: Date.now()
        };
        
    } catch (error) {
        console.error(`SearXNG-Crawlee tìm kiếm thất bại: ${error.message}`);
        return {
            success: false,
            error: `SearXNG-Crawlee tìm kiếm thất bại: ${error.message}`,
            query: query,
            results: []
        };
    }
}

// Hàm chính
async function main() {
    try {
        // Lấy tham số từ command line
        const query = process.argv[2] || "Python programming tutorial";
        const numResults = parseInt(process.argv[3]) || 3;
        const maxDepth = parseInt(process.argv[4]) || 1;
        const maxPages = parseInt(process.argv[5]) || 3;
        const timeout = parseInt(process.argv[6]) || 30000;
        
        // Tìm kiếm với SearXNG-Crawlee
        console.log("================================================================================");
        console.log("Kiểm tra phương thức SearXNG-Crawlee");
        console.log("================================================================================");
        console.log(`Truy vấn: ${query}`);
        console.log(`Số kết quả: ${numResults}`);
        console.log(`Độ sâu tối đa: ${maxDepth}`);
        console.log(`Số trang tối đa: ${maxPages}`);
        console.log(`Thời gian chờ: ${timeout}ms`);
        
        const results = await searchWithSearXNGCrawlee(query, numResults, maxDepth, maxPages, timeout);
        
        // Lưu kết quả vào file
        fs.writeFileSync('searxng_crawlee_results.json', JSON.stringify(results, null, 2));
        
        console.log("\n================================================================================");
        console.log("Tổng kết");
        console.log("================================================================================");
        console.log(`Thành công: ${results.success}`);
        console.log(`Số kết quả: ${results.results.length}`);
        console.log(`Phương thức tìm kiếm: ${results.search_method}`);
        console.log(`Kết quả đã được lưu vào file: searxng_crawlee_results.json`);
        
    } catch (error) {
        console.error(`Lỗi: ${error.message}`);
    }
}

// Cài đặt node-fetch nếu chưa có
try {
    require('node-fetch');
} catch (error) {
    console.log("Đang cài đặt node-fetch...");
    require('child_process').execSync('npm install node-fetch');
}

// Chạy hàm chính
main();
