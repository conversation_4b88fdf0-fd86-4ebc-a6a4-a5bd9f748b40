#!/usr/bin/env python3
"""
Move test files from root directory to appropriate test directories.
"""

import os
import shutil
import re
from pathlib import Path

def main():
    """Move test files to appropriate directories."""
    # Define source and destination directories
    root_dir = Path(".")
    unit_tests_dir = Path("tests/unit")
    integration_tests_dir = Path("tests/integration")
    
    # Create destination directories if they don't exist
    os.makedirs(unit_tests_dir / "reasoning" / "tot", exist_ok=True)
    os.makedirs(unit_tests_dir / "reasoning" / "combined", exist_ok=True)
    
    # Define test file mappings (source -> destination)
    test_mappings = {
        # TOT tests
        "test_tot.py": unit_tests_dir / "reasoning" / "tot" / "test_tot.py",
        "test_tot_simple.py": unit_tests_dir / "reasoning" / "tot" / "test_tot_simple.py",
        "test_tot_minimal.py": unit_tests_dir / "reasoning" / "tot" / "test_tot_minimal.py",
        "test_tot_fixed.py": unit_tests_dir / "reasoning" / "tot" / "test_tot_fixed.py",
        "test_tot_advanced_evaluation.py": unit_tests_dir / "reasoning" / "tot" / "test_tot_advanced_evaluation.py",
        "test_tot_optimized.py": unit_tests_dir / "reasoning" / "tot" / "test_tot_optimized.py",
        "test_tot_optimized_advanced.py": unit_tests_dir / "reasoning" / "tot" / "test_tot_optimized_advanced.py",
        
        # Combined reasoning tests
        "test_rag_tot_cot.py": unit_tests_dir / "reasoning" / "combined" / "test_rag_tot_cot.py",
    }
    
    # Move test files
    for source, destination in test_mappings.items():
        source_path = root_dir / source
        if source_path.exists():
            print(f"Moving {source} to {destination}")
            
            # Read the file content
            with open(source_path, "r", encoding="utf-8") as f:
                content = f.read()
            
            # Update imports if needed
            content = re.sub(
                r"from src\.deep_research_core",
                "from src.deep_research_core",
                content
            )
            
            # Write the file to the destination
            with open(destination, "w", encoding="utf-8") as f:
                f.write(content)
            
            # Remove the original file
            os.remove(source_path)
            print(f"Moved {source} to {destination}")
        else:
            print(f"Source file {source} not found")
    
    print("Test files have been moved to appropriate directories.")

if __name__ == "__main__":
    main()
