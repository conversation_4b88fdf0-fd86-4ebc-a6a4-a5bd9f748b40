#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to set up Vietnamese language support for the Deep Research Core.

This script will:
1. Install required dependencies for Vietnamese NLP
2. Download necessary NLTK data
3. Set up PhoBERT model for Vietnamese sentiment analysis
4. Validate the installation

Usage:
    python setup_vietnamese_support.py [--force]
"""

import os
import sys
import argparse
import subprocess
import importlib
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("vietnamese-setup")

def check_module(module_name):
    """Check if a module is installed."""
    try:
        importlib.import_module(module_name)
        return True
    except ImportError:
        return False

def install_package(package_name):
    """Install a Python package using pip."""
    logger.info(f"Installing {package_name}...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        logger.info(f"Successfully installed {package_name}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install {package_name}: {e}")
        return False

def download_nltk_data():
    """Download necessary NLTK data."""
    logger.info("Downloading NLTK data...")
    try:
        import nltk
        nltk.download('punkt', quiet=True)
        nltk.download('wordnet', quiet=True)
        logger.info("Successfully downloaded NLTK data")
        return True
    except Exception as e:
        logger.error(f"Failed to download NLTK data: {e}")
        return False

def setup_phobert():
    """Set up PhoBERT model for Vietnamese NLP."""
    logger.info("Setting up PhoBERT model...")
    try:
        from transformers import AutoTokenizer, AutoModel
        
        # Check if model is already downloaded
        cache_dir = os.path.expanduser("~/.cache/huggingface/transformers")
        phobert_dir = Path(cache_dir) / "vinai/phobert-base-vietnamese-sentiment"
        
        if phobert_dir.exists() and not args.force:
            logger.info("PhoBERT model already downloaded")
            return True
        
        # Download model
        tokenizer = AutoTokenizer.from_pretrained("vinai/phobert-base")
        model = AutoModel.from_pretrained("vinai/phobert-base")
        
        logger.info("Successfully set up PhoBERT model")
        return True
    except Exception as e:
        logger.error(f"Failed to set up PhoBERT model: {e}")
        logger.info("You can still use Vietnamese support without the transformer model")
        return False

def validate_vietnamese_support():
    """Validate Vietnamese language support installation."""
    logger.info("Validating Vietnamese language support...")
    
    # Check underthesea installation
    underthesea_available = check_module("underthesea")
    if underthesea_available:
        logger.info("✓ Underthesea is available")
        
        # Test underthesea functionality
        try:
            from underthesea import word_tokenize, sentiment
            
            # Test word tokenization
            text = "Đây là một câu tiếng Việt."
            tokens = word_tokenize(text)
            logger.info(f"Tokenization example: {' | '.join(tokens)}")
            
            # Test sentiment analysis
            pos_text = "Sản phẩm này rất tốt và tuyệt vời."
            pos_sentiment = sentiment(pos_text)
            logger.info(f"Sentiment analysis example (positive): {pos_sentiment}")
        except Exception as e:
            logger.error(f"Error testing underthesea: {e}")
    else:
        logger.warning("✗ Underthesea is not available")
    
    # Check pyvi installation
    pyvi_available = check_module("pyvi")
    if pyvi_available:
        logger.info("✓ PyVi is available")
        
        # Test pyvi functionality
        try:
            from pyvi import ViTokenizer
            
            # Test word tokenization
            text = "Đây là một câu tiếng Việt."
            tokens = ViTokenizer.tokenize(text)
            logger.info(f"ViTokenizer example: {tokens}")
        except Exception as e:
            logger.error(f"Error testing pyvi: {e}")
    else:
        logger.warning("✗ PyVi is not available")
    
    # Check transformer installation
    transformers_available = check_module("transformers")
    if transformers_available:
        logger.info("✓ Transformers is available")
    else:
        logger.warning("✗ Transformers is not available")
    
    torch_available = check_module("torch")
    if torch_available:
        logger.info("✓ PyTorch is available")
    else:
        logger.warning("✗ PyTorch is not available")
    
    # Check Vietnamese special characters support
    try:
        text = "àáảãạăắằẳẵặâấầẩẫậèéẻẽẹêếềểễệìíỉĩịòóỏõọôốồổỗộơớờởỡợùúủũụưứừửữựỳýỷỹỵđ"
        logger.info(f"Vietnamese character test: {text}")
        logger.info("✓ Vietnamese character encoding is supported")
    except UnicodeEncodeError:
        logger.error("✗ Vietnamese character encoding is not properly supported")
    
    logger.info("Vietnamese language support validation complete")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Set up Vietnamese language support")
    parser.add_argument("--force", action="store_true", help="Force reinstallation of components")
    args = parser.parse_args()
    
    logger.info("Starting Vietnamese language support setup")
    
    # List of required packages
    packages = [
        "underthesea>=1.3.5",
        "pyvi>=0.1.1",
        "transformers>=4.8.0",
        "torch>=1.9.0",
        "nltk>=3.6.0"
    ]
    
    # Install packages
    for package in packages:
        pkg_name = package.split(">=")[0]
        if not check_module(pkg_name) or args.force:
            install_package(package)
        else:
            logger.info(f"{pkg_name} is already installed")
    
    # Set up additional components
    download_nltk_data()
    setup_phobert()
    
    # Validate the installation
    validate_vietnamese_support()
    
    logger.info("Vietnamese language support setup complete") 