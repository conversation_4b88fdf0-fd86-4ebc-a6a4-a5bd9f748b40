#!/usr/bin/env python3
"""
Analyze test performance.

This script analyzes the performance of tests and identifies bottlenecks.
"""

import os
import sys
import json
import argparse
import subprocess
import time
from collections import defaultdict
import matplotlib.pyplot as plt
import numpy as np

def parse_arguments():
    """
    Parse command line arguments.
    
    Returns:
        argparse.Namespace: Parsed arguments
    """
    parser = argparse.ArgumentParser(description='Analyze test performance')
    parser.add_argument('--output', default='test_performance.json', help='Output file')
    parser.add_argument('--threshold', type=float, default=1.0, help='Threshold for slow tests (seconds)')
    parser.add_argument('--top', type=int, default=10, help='Number of slowest tests to show')
    parser.add_argument('--plot', action='store_true', help='Generate performance plots')
    parser.add_argument('--compare', help='Compare with previous results')
    parser.add_argument('--verbose', action='store_true', help='Show verbose output')
    
    return parser.parse_args()

def run_tests_with_timing():
    """
    Run tests with timing information.
    
    Returns:
        dict: Test timing information
    """
    # Run pytest with timing information
    cmd = [
        'python', '-m', 'pytest',
        '--collect-only', '-v',
        '--no-header',
        '--durations=0'
    ]
    
    # Get list of all tests
    result = subprocess.run(cmd, capture_output=True, text=True)
    test_list = []
    
    for line in result.stdout.splitlines():
        if '<' in line and '>' in line and 'test_' in line:
            test_name = line.strip().split('<')[0].strip()
            test_list.append(test_name)
    
    # Run each test individually with timing
    test_timings = {}
    
    for test_name in test_list:
        start_time = time.time()
        cmd = [
            'python', '-m', 'pytest',
            test_name,
            '-v',
            '--no-header'
        ]
        result = subprocess.run(cmd, capture_output=True, text=True)
        end_time = time.time()
        
        duration = end_time - start_time
        test_timings[test_name] = {
            'duration': duration,
            'status': 'passed' if result.returncode == 0 else 'failed'
        }
        
        print(f"Ran {test_name} in {duration:.2f} seconds")
    
    return test_timings

def analyze_test_timings(test_timings, threshold=1.0, top=10):
    """
    Analyze test timings.
    
    Args:
        test_timings (dict): Test timing information
        threshold (float): Threshold for slow tests (seconds)
        top (int): Number of slowest tests to show
        
    Returns:
        dict: Analysis results
    """
    # Sort tests by duration
    sorted_tests = sorted(
        test_timings.items(),
        key=lambda x: x[1]['duration'],
        reverse=True
    )
    
    # Get slow tests
    slow_tests = [
        (name, info)
        for name, info in sorted_tests
        if info['duration'] >= threshold
    ]
    
    # Get top N slowest tests
    top_slowest = sorted_tests[:top]
    
    # Group tests by module
    tests_by_module = defaultdict(list)
    
    for name, info in test_timings.items():
        module = name.split('::')[0]
        tests_by_module[module].append((name, info))
    
    # Calculate module statistics
    module_stats = {}
    
    for module, tests in tests_by_module.items():
        durations = [info['duration'] for _, info in tests]
        module_stats[module] = {
            'count': len(tests),
            'total_duration': sum(durations),
            'average_duration': sum(durations) / len(durations),
            'min_duration': min(durations),
            'max_duration': max(durations)
        }
    
    # Sort modules by total duration
    sorted_modules = sorted(
        module_stats.items(),
        key=lambda x: x[1]['total_duration'],
        reverse=True
    )
    
    # Calculate overall statistics
    all_durations = [info['duration'] for _, info in test_timings.items()]
    overall_stats = {
        'count': len(test_timings),
        'total_duration': sum(all_durations),
        'average_duration': sum(all_durations) / len(all_durations),
        'min_duration': min(all_durations),
        'max_duration': max(all_durations),
        'slow_tests_count': len(slow_tests),
        'slow_tests_percentage': len(slow_tests) / len(test_timings) * 100
    }
    
    return {
        'overall_stats': overall_stats,
        'slow_tests': slow_tests,
        'top_slowest': top_slowest,
        'module_stats': module_stats,
        'sorted_modules': sorted_modules
    }

def generate_performance_plots(analysis_results, output_prefix='test_performance'):
    """
    Generate performance plots.
    
    Args:
        analysis_results (dict): Analysis results
        output_prefix (str): Output file prefix
    """
    # Plot top slowest tests
    plt.figure(figsize=(12, 8))
    
    names = [name.split('::')[-1] for name, _ in analysis_results['top_slowest']]
    durations = [info['duration'] for _, info in analysis_results['top_slowest']]
    
    plt.barh(names, durations)
    plt.xlabel('Duration (seconds)')
    plt.ylabel('Test')
    plt.title('Top Slowest Tests')
    plt.tight_layout()
    plt.savefig(f'{output_prefix}_top_slowest.png')
    
    # Plot module statistics
    plt.figure(figsize=(12, 8))
    
    module_names = [name for name, _ in analysis_results['sorted_modules'][:10]]
    module_durations = [stats['total_duration'] for _, stats in analysis_results['sorted_modules'][:10]]
    
    plt.barh(module_names, module_durations)
    plt.xlabel('Total Duration (seconds)')
    plt.ylabel('Module')
    plt.title('Top Slowest Modules')
    plt.tight_layout()
    plt.savefig(f'{output_prefix}_module_stats.png')
    
    # Plot duration distribution
    plt.figure(figsize=(12, 8))
    
    all_durations = [info['duration'] for _, info in analysis_results['test_timings'].items()]
    
    plt.hist(all_durations, bins=20)
    plt.xlabel('Duration (seconds)')
    plt.ylabel('Count')
    plt.title('Test Duration Distribution')
    plt.tight_layout()
    plt.savefig(f'{output_prefix}_duration_distribution.png')

def compare_results(current_results, previous_results):
    """
    Compare current results with previous results.
    
    Args:
        current_results (dict): Current analysis results
        previous_results (dict): Previous analysis results
        
    Returns:
        dict: Comparison results
    """
    # Compare overall statistics
    overall_diff = {}
    
    for key, value in current_results['overall_stats'].items():
        if key in previous_results['overall_stats']:
            if isinstance(value, (int, float)):
                diff = value - previous_results['overall_stats'][key]
                overall_diff[key] = {
                    'current': value,
                    'previous': previous_results['overall_stats'][key],
                    'diff': diff,
                    'diff_percentage': diff / previous_results['overall_stats'][key] * 100 if previous_results['overall_stats'][key] != 0 else 0
                }
    
    # Compare test timings
    test_diffs = {}
    
    for name, info in current_results['test_timings'].items():
        if name in previous_results['test_timings']:
            current_duration = info['duration']
            previous_duration = previous_results['test_timings'][name]['duration']
            diff = current_duration - previous_duration
            
            test_diffs[name] = {
                'current': current_duration,
                'previous': previous_duration,
                'diff': diff,
                'diff_percentage': diff / previous_duration * 100 if previous_duration != 0 else 0
            }
    
    # Sort test diffs by absolute difference
    sorted_test_diffs = sorted(
        test_diffs.items(),
        key=lambda x: abs(x[1]['diff']),
        reverse=True
    )
    
    # Get top improved and regressed tests
    improved_tests = [
        (name, diff)
        for name, diff in sorted_test_diffs
        if diff['diff'] < 0
    ]
    
    regressed_tests = [
        (name, diff)
        for name, diff in sorted_test_diffs
        if diff['diff'] > 0
    ]
    
    return {
        'overall_diff': overall_diff,
        'test_diffs': test_diffs,
        'improved_tests': improved_tests[:10],
        'regressed_tests': regressed_tests[:10]
    }

def print_analysis_results(analysis_results, args):
    """
    Print analysis results.
    
    Args:
        analysis_results (dict): Analysis results
        args (argparse.Namespace): Command line arguments
    """
    # Print overall statistics
    print("\nOverall Statistics:")
    print(f"Total tests: {analysis_results['overall_stats']['count']}")
    print(f"Total duration: {analysis_results['overall_stats']['total_duration']:.2f} seconds")
    print(f"Average duration: {analysis_results['overall_stats']['average_duration']:.2f} seconds")
    print(f"Min duration: {analysis_results['overall_stats']['min_duration']:.2f} seconds")
    print(f"Max duration: {analysis_results['overall_stats']['max_duration']:.2f} seconds")
    print(f"Slow tests (>= {args.threshold}s): {analysis_results['overall_stats']['slow_tests_count']} ({analysis_results['overall_stats']['slow_tests_percentage']:.2f}%)")
    
    # Print top slowest tests
    print(f"\nTop {args.top} Slowest Tests:")
    for i, (name, info) in enumerate(analysis_results['top_slowest'], 1):
        print(f"{i}. {name}: {info['duration']:.2f} seconds")
    
    # Print top slowest modules
    print("\nTop 5 Slowest Modules:")
    for i, (name, stats) in enumerate(analysis_results['sorted_modules'][:5], 1):
        print(f"{i}. {name}: {stats['total_duration']:.2f} seconds ({stats['count']} tests, avg: {stats['average_duration']:.2f}s)")
    
    # Print slow tests
    if args.verbose and analysis_results['slow_tests']:
        print(f"\nSlow Tests (>= {args.threshold}s):")
        for name, info in analysis_results['slow_tests']:
            print(f"{name}: {info['duration']:.2f} seconds")

def print_comparison_results(comparison_results):
    """
    Print comparison results.
    
    Args:
        comparison_results (dict): Comparison results
    """
    # Print overall diff
    print("\nOverall Comparison:")
    for key, diff in comparison_results['overall_diff'].items():
        print(f"{key}: {diff['current']:.2f} vs {diff['previous']:.2f} ({diff['diff']:.2f}, {diff['diff_percentage']:.2f}%)")
    
    # Print top improved tests
    print("\nTop 5 Most Improved Tests:")
    for i, (name, diff) in enumerate(comparison_results['improved_tests'][:5], 1):
        print(f"{i}. {name}: {diff['current']:.2f}s vs {diff['previous']:.2f}s ({diff['diff']:.2f}s, {diff['diff_percentage']:.2f}%)")
    
    # Print top regressed tests
    print("\nTop 5 Most Regressed Tests:")
    for i, (name, diff) in enumerate(comparison_results['regressed_tests'][:5], 1):
        print(f"{i}. {name}: {diff['current']:.2f}s vs {diff['previous']:.2f}s ({diff['diff']:.2f}s, {diff['diff_percentage']:.2f}%)")

def main():
    """
    Main function.
    """
    args = parse_arguments()
    
    # Run tests with timing
    print("Running tests with timing...")
    test_timings = run_tests_with_timing()
    
    # Analyze test timings
    print("Analyzing test timings...")
    analysis_results = analyze_test_timings(test_timings, args.threshold, args.top)
    analysis_results['test_timings'] = test_timings
    
    # Save results
    with open(args.output, 'w') as f:
        json.dump(analysis_results, f, indent=2)
    
    print(f"Results saved to {args.output}")
    
    # Print analysis results
    print_analysis_results(analysis_results, args)
    
    # Compare with previous results
    if args.compare:
        print(f"Comparing with {args.compare}...")
        
        with open(args.compare, 'r') as f:
            previous_results = json.load(f)
        
        comparison_results = compare_results(analysis_results, previous_results)
        print_comparison_results(comparison_results)
    
    # Generate performance plots
    if args.plot:
        print("Generating performance plots...")
        generate_performance_plots(analysis_results)
        print("Plots saved to test_performance_*.png")

if __name__ == "__main__":
    main()
