#!/usr/bin/env python
"""
Script đánh giá hiệu suất của các mô hình embedding tiếng Việt.

Script này đánh giá hiệu suất của các mô hình embedding tiếng Việt
trên các bộ dữ liệu benchmark khác nhau.
"""

import os
import sys
import json
import argparse
import logging
from typing import Dict, List, Optional, Any

# Thêm thư mục gốc vào đường dẫn Python
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.evaluation.vietnamese_benchmark import VietnameseBenchmark
from src.deep_research_core.multilingual.vietnamese_embeddings import VietnameseEmbeddings

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def parse_args():
    """
    Phân tích tham số dòng lệnh.
    
    Returns:
        <PERSON><PERSON><PERSON> tham số dòng lệnh đã phân tích
    """
    parser = argparse.ArgumentParser(
        description='Đánh giá hiệu suất của các mô hình embedding tiếng Việt'
    )
    
    parser.add_argument(
        '--models',
        type=str,
        nargs='+',
        help='Danh sách các mô hình embedding cần đánh giá'
    )
    
    parser.add_argument(
        '--domain',
        type=str,
        default='general',
        choices=VietnameseBenchmark.SUPPORTED_DOMAINS,
        help='Lĩnh vực của bộ dữ liệu benchmark'
    )
    
    parser.add_argument(
        '--task',
        type=str,
        default='retrieval',
        choices=VietnameseBenchmark.SUPPORTED_TASKS,
        help='Loại nhiệm vụ của bộ dữ liệu benchmark'
    )
    
    parser.add_argument(
        '--device',
        type=str,
        default=None,
        choices=['cpu', 'cuda', 'mps'],
        help='Thiết bị để sử dụng cho inference'
    )
    
    parser.add_argument(
        '--top-k',
        type=int,
        default=5,
        help='Số lượng kết quả hàng đầu để đánh giá'
    )
    
    parser.add_argument(
        '--output',
        type=str,
        default='vietnamese_embedding_evaluation_results.json',
        help='Đường dẫn đến file kết quả đầu ra'
    )
    
    return parser.parse_args()

def format_results(results: Dict[str, Dict[str, float]]) -> str:
    """
    Định dạng kết quả đánh giá để hiển thị.
    
    Args:
        results: Kết quả đánh giá
        
    Returns:
        Chuỗi kết quả đã định dạng
    """
    output = "\n=== KẾT QUẢ ĐÁNH GIÁ MÔ HÌNH EMBEDDING TIẾNG VIỆT ===\n\n"
    
    # Tìm mô hình tốt nhất cho mỗi chỉ số
    best_models = {
        "precision": "",
        "recall": "",
        "f1_score": "",
        "ndcg": "",
        "latency": ""
    }
    
    best_scores = {
        "precision": 0.0,
        "recall": 0.0,
        "f1_score": 0.0,
        "ndcg": 0.0,
        "latency": float('inf')
    }
    
    for model_name, metrics in results.items():
        for metric, value in metrics.items():
            if metric == "error":
                continue
                
            if metric == "latency":
                if value < best_scores[metric]:
                    best_scores[metric] = value
                    best_models[metric] = model_name
            else:
                if value > best_scores[metric]:
                    best_scores[metric] = value
                    best_models[metric] = model_name
    
    # Hiển thị kết quả cho từng mô hình
    output += f"{'Mô hình':<20} {'Precision':<10} {'Recall':<10} {'F1 Score':<10} {'NDCG':<10} {'Latency (s)':<12}\n"
    output += "-" * 75 + "\n"
    
    for model_name, metrics in results.items():
        if "error" in metrics:
            output += f"{model_name:<20} LỖI: {metrics['error']}\n"
            continue
            
        precision = metrics.get("precision", 0.0)
        recall = metrics.get("recall", 0.0)
        f1_score = metrics.get("f1_score", 0.0)
        ndcg = metrics.get("ndcg", 0.0)
        latency = metrics.get("latency", 0.0)
        
        # Đánh dấu giá trị tốt nhất
        precision_str = f"{precision:.4f}*" if model_name == best_models["precision"] else f"{precision:.4f}"
        recall_str = f"{recall:.4f}*" if model_name == best_models["recall"] else f"{recall:.4f}"
        f1_str = f"{f1_score:.4f}*" if model_name == best_models["f1_score"] else f"{f1_score:.4f}"
        ndcg_str = f"{ndcg:.4f}*" if model_name == best_models["ndcg"] else f"{ndcg:.4f}"
        latency_str = f"{latency:.4f}*" if model_name == best_models["latency"] else f"{latency:.4f}"
        
        output += f"{model_name:<20} {precision_str:<10} {recall_str:<10} {f1_str:<10} {ndcg_str:<10} {latency_str:<12}\n"
    
    output += "\n* Giá trị tốt nhất\n"
    
    # Hiển thị tóm tắt
    output += "\n=== TÓM TẮT ===\n"
    output += f"Mô hình có Precision tốt nhất: {best_models['precision']} ({best_scores['precision']:.4f})\n"
    output += f"Mô hình có Recall tốt nhất: {best_models['recall']} ({best_scores['recall']:.4f})\n"
    output += f"Mô hình có F1 Score tốt nhất: {best_models['f1_score']} ({best_scores['f1_score']:.4f})\n"
    output += f"Mô hình có NDCG tốt nhất: {best_models['ndcg']} ({best_scores['ndcg']:.4f})\n"
    output += f"Mô hình có Latency tốt nhất: {best_models['latency']} ({best_scores['latency']:.4f}s)\n"
    
    return output

def main():
    """
    Hàm chính của script.
    """
    args = parse_args()
    
    # Khởi tạo benchmark
    benchmark = VietnameseBenchmark()
    
    # Xác định các mô hình cần đánh giá
    if args.models:
        model_names = args.models
    else:
        # Sử dụng tất cả các mô hình có sẵn
        model_names = list(VietnameseEmbeddings.AVAILABLE_MODELS.keys())
    
    logger.info(f"Đánh giá {len(model_names)} mô hình embedding tiếng Việt")
    logger.info(f"Lĩnh vực: {args.domain}, Nhiệm vụ: {args.task}")
    
    # Đánh giá các mô hình
    results = benchmark.compare_embedding_models(
        model_names=model_names,
        domain=args.domain,
        task=args.task,
        device=args.device,
        top_k=args.top_k
    )
    
    # Hiển thị kết quả
    formatted_results = format_results(results)
    print(formatted_results)
    
    # Lưu kết quả vào file
    with open(args.output, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    logger.info(f"Đã lưu kết quả vào file: {args.output}")

if __name__ == "__main__":
    main()
