"""
Create evaluation datasets for CoTRAG.

This script creates evaluation datasets for CoTRAG in English and Vietnamese.
"""

import os
import sys
import argparse
import pandas as pd
from typing import List, Dict, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Create evaluation datasets for CoTRAG")
    
    parser.add_argument("--output-dir", type=str, required=True, help="Directory to save the datasets")
    parser.add_argument("--english-size", type=int, default=100, help="Number of English samples to create")
    parser.add_argument("--vietnamese-size", type=int, default=100, help="Number of Vietnamese samples to create")
    
    return parser.parse_args()

def create_english_dataset(size: int) -> pd.DataFrame:
    """
    Create an English evaluation dataset.
    
    Args:
        size: Number of samples to create
        
    Returns:
        DataFrame containing the dataset
    """
    # Define sample queries and expected answers
    samples = [
        {
            "query": "What is artificial intelligence?",
            "expected_answer": "Artificial intelligence (AI) is a branch of computer science that focuses on creating systems capable of performing tasks that typically require human intelligence.",
            "category": "factual",
            "complexity": "low"
        },
        {
            "query": "Explain the difference between supervised and unsupervised learning.",
            "expected_answer": "Supervised learning uses labeled data to train models, while unsupervised learning works with unlabeled data to find patterns or structures.",
            "category": "comparison",
            "complexity": "medium"
        },
        {
            "query": "How do neural networks work?",
            "expected_answer": "Neural networks are computational models inspired by the human brain, consisting of interconnected nodes (neurons) organized in layers that process and transform input data to produce output.",
            "category": "explanation",
            "complexity": "high"
        },
        {
            "query": "What are the ethical implications of AI in healthcare?",
            "expected_answer": "Ethical implications of AI in healthcare include privacy concerns, data security, algorithmic bias, transparency, accountability, and the potential for AI to replace human judgment in critical decisions.",
            "category": "analysis",
            "complexity": "high"
        },
        {
            "query": "Compare deep learning and traditional machine learning algorithms.",
            "expected_answer": "Deep learning uses neural networks with many layers to automatically learn features from data, while traditional machine learning algorithms often require manual feature engineering and typically have simpler architectures.",
            "category": "comparison",
            "complexity": "medium"
        },
        {
            "query": "What is reinforcement learning and how is it used?",
            "expected_answer": "Reinforcement learning is a type of machine learning where an agent learns to make decisions by taking actions in an environment to maximize rewards. It's used in robotics, game playing, autonomous vehicles, and recommendation systems.",
            "category": "explanation",
            "complexity": "medium"
        },
        {
            "query": "Explain the concept of overfitting in machine learning.",
            "expected_answer": "Overfitting occurs when a model learns the training data too well, including its noise and outliers, resulting in poor performance on new, unseen data. It happens when a model is too complex relative to the amount of training data.",
            "category": "explanation",
            "complexity": "medium"
        },
        {
            "query": "What are the main challenges in natural language processing?",
            "expected_answer": "Main challenges in natural language processing include ambiguity, context understanding, sarcasm and humor detection, handling multiple languages, dealing with evolving language, and processing unstructured text data.",
            "category": "analysis",
            "complexity": "high"
        },
        {
            "query": "How does a decision tree algorithm work?",
            "expected_answer": "A decision tree algorithm works by recursively splitting the data based on feature values to create a tree-like model of decisions. It selects the best feature to split on at each node based on metrics like information gain or Gini impurity.",
            "category": "explanation",
            "complexity": "medium"
        },
        {
            "query": "What is the difference between AI, machine learning, and deep learning?",
            "expected_answer": "AI is the broader concept of machines being able to carry out tasks intelligently. Machine learning is a subset of AI that focuses on algorithms that learn from data. Deep learning is a subset of machine learning that uses neural networks with many layers.",
            "category": "comparison",
            "complexity": "medium"
        },
        {
            "query": "Explain how backpropagation works in neural networks.",
            "expected_answer": "Backpropagation is an algorithm used to train neural networks by calculating gradients of the loss function with respect to the network weights. It works by propagating the error backward through the network, updating weights to minimize the loss.",
            "category": "explanation",
            "complexity": "high"
        },
        {
            "query": "What are convolutional neural networks and what are they used for?",
            "expected_answer": "Convolutional Neural Networks (CNNs) are a type of neural network designed for processing structured grid data like images. They use convolutional layers to automatically detect features and are primarily used for image recognition, classification, and computer vision tasks.",
            "category": "explanation",
            "complexity": "high"
        },
        {
            "query": "How does clustering work in machine learning?",
            "expected_answer": "Clustering in machine learning works by grouping similar data points together based on their features or characteristics. Algorithms like K-means, hierarchical clustering, or DBSCAN identify patterns in the data to form clusters without labeled examples.",
            "category": "explanation",
            "complexity": "medium"
        },
        {
            "query": "What is transfer learning and why is it important?",
            "expected_answer": "Transfer learning is a technique where a model developed for one task is reused as the starting point for a model on a second task. It's important because it allows leveraging knowledge from pre-trained models, reducing training time and data requirements.",
            "category": "explanation",
            "complexity": "medium"
        },
        {
            "query": "Explain the bias-variance tradeoff in machine learning.",
            "expected_answer": "The bias-variance tradeoff is a fundamental concept in machine learning where models with high bias underfit the data (too simple), while models with high variance overfit the data (too complex). Finding the right balance is crucial for good generalization.",
            "category": "explanation",
            "complexity": "high"
        },
        {
            "query": "What are the differences between classification and regression?",
            "expected_answer": "Classification predicts discrete class labels or categories, while regression predicts continuous numerical values. Classification answers 'which category?' questions, while regression answers 'how much?' questions.",
            "category": "comparison",
            "complexity": "low"
        },
        {
            "query": "How does natural language processing work?",
            "expected_answer": "Natural language processing works by applying computational techniques to analyze, understand, and generate human language. It involves steps like tokenization, part-of-speech tagging, parsing, semantic analysis, and often uses machine learning models to process text data.",
            "category": "explanation",
            "complexity": "high"
        },
        {
            "query": "What is the role of activation functions in neural networks?",
            "expected_answer": "Activation functions introduce non-linearity into neural networks, allowing them to learn complex patterns. They determine whether a neuron should be activated based on the weighted sum of inputs, enabling the network to model non-linear relationships in the data.",
            "category": "explanation",
            "complexity": "medium"
        },
        {
            "query": "Explain how recommendation systems work.",
            "expected_answer": "Recommendation systems work by analyzing user behavior, preferences, and item characteristics to suggest relevant items. They typically use collaborative filtering (based on user similarities), content-based filtering (based on item features), or hybrid approaches.",
            "category": "explanation",
            "complexity": "medium"
        },
        {
            "query": "What is the difference between supervised, unsupervised, and reinforcement learning?",
            "expected_answer": "Supervised learning uses labeled data to learn mappings from inputs to outputs. Unsupervised learning finds patterns in unlabeled data. Reinforcement learning involves an agent learning to make decisions by taking actions and receiving rewards or penalties.",
            "category": "comparison",
            "complexity": "medium"
        }
    ]
    
    # Create a DataFrame
    df = pd.DataFrame(samples)
    
    # Replicate samples if needed
    if size > len(samples):
        df = pd.concat([df] * (size // len(samples) + 1), ignore_index=True)
    
    # Trim to the requested size
    df = df.head(size)
    
    return df

def create_vietnamese_dataset(size: int) -> pd.DataFrame:
    """
    Create a Vietnamese evaluation dataset.
    
    Args:
        size: Number of samples to create
        
    Returns:
        DataFrame containing the dataset
    """
    # Define sample queries and expected answers
    samples = [
        {
            "query": "Trí tuệ nhân tạo là gì?",
            "expected_answer": "Trí tuệ nhân tạo (AI) là một nhánh của khoa học máy tính tập trung vào việc tạo ra các hệ thống có khả năng thực hiện các nhiệm vụ thường đòi hỏi trí thông minh của con người.",
            "category": "factual",
            "complexity": "low"
        },
        {
            "query": "Giải thích sự khác biệt giữa học có giám sát và học không giám sát.",
            "expected_answer": "Học có giám sát sử dụng dữ liệu đã được gán nhãn để huấn luyện mô hình, trong khi học không giám sát làm việc với dữ liệu không có nhãn để tìm ra các mẫu hoặc cấu trúc.",
            "category": "comparison",
            "complexity": "medium"
        },
        {
            "query": "Mạng nơ-ron hoạt động như thế nào?",
            "expected_answer": "Mạng nơ-ron là mô hình tính toán lấy cảm hứng từ bộ não con người, bao gồm các nút (nơ-ron) được kết nối với nhau và được tổ chức thành các lớp xử lý và biến đổi dữ liệu đầu vào để tạo ra đầu ra.",
            "category": "explanation",
            "complexity": "high"
        },
        {
            "query": "Những ý nghĩa đạo đức của AI trong chăm sóc sức khỏe là gì?",
            "expected_answer": "Những ý nghĩa đạo đức của AI trong chăm sóc sức khỏe bao gồm vấn đề quyền riêng tư, bảo mật dữ liệu, thiên kiến thuật toán, tính minh bạch, trách nhiệm giải trình và khả năng AI thay thế phán đoán của con người trong các quyết định quan trọng.",
            "category": "analysis",
            "complexity": "high"
        },
        {
            "query": "So sánh học sâu và các thuật toán học máy truyền thống.",
            "expected_answer": "Học sâu sử dụng mạng nơ-ron với nhiều lớp để tự động học các đặc trưng từ dữ liệu, trong khi các thuật toán học máy truyền thống thường đòi hỏi kỹ thuật đặc trưng thủ công và thường có kiến trúc đơn giản hơn.",
            "category": "comparison",
            "complexity": "medium"
        },
        {
            "query": "Học tăng cường là gì và nó được sử dụng như thế nào?",
            "expected_answer": "Học tăng cường là một loại học máy trong đó một tác nhân học cách đưa ra quyết định bằng cách thực hiện các hành động trong môi trường để tối đa hóa phần thưởng. Nó được sử dụng trong robot, chơi game, xe tự lái và hệ thống đề xuất.",
            "category": "explanation",
            "complexity": "medium"
        },
        {
            "query": "Giải thích khái niệm overfitting trong học máy.",
            "expected_answer": "Overfitting xảy ra khi một mô hình học dữ liệu huấn luyện quá tốt, bao gồm cả nhiễu và các điểm ngoại lai, dẫn đến hiệu suất kém trên dữ liệu mới, chưa từng thấy. Nó xảy ra khi mô hình quá phức tạp so với lượng dữ liệu huấn luyện.",
            "category": "explanation",
            "complexity": "medium"
        },
        {
            "query": "Những thách thức chính trong xử lý ngôn ngữ tự nhiên là gì?",
            "expected_answer": "Những thách thức chính trong xử lý ngôn ngữ tự nhiên bao gồm tính mơ hồ, hiểu ngữ cảnh, phát hiện sự mỉa mai và hài hước, xử lý nhiều ngôn ngữ, đối phó với ngôn ngữ đang phát triển và xử lý dữ liệu văn bản không có cấu trúc.",
            "category": "analysis",
            "complexity": "high"
        },
        {
            "query": "Thuật toán cây quyết định hoạt động như thế nào?",
            "expected_answer": "Thuật toán cây quyết định hoạt động bằng cách chia dữ liệu một cách đệ quy dựa trên giá trị đặc trưng để tạo ra mô hình dạng cây của các quyết định. Nó chọn đặc trưng tốt nhất để chia tại mỗi nút dựa trên các chỉ số như information gain hoặc Gini impurity.",
            "category": "explanation",
            "complexity": "medium"
        },
        {
            "query": "Sự khác biệt giữa AI, học máy và học sâu là gì?",
            "expected_answer": "AI là khái niệm rộng hơn về máy móc có khả năng thực hiện các nhiệm vụ một cách thông minh. Học máy là một tập con của AI tập trung vào các thuật toán học từ dữ liệu. Học sâu là một tập con của học máy sử dụng mạng nơ-ron với nhiều lớp.",
            "category": "comparison",
            "complexity": "medium"
        },
        {
            "query": "Giải thích cách backpropagation hoạt động trong mạng nơ-ron.",
            "expected_answer": "Backpropagation là một thuật toán được sử dụng để huấn luyện mạng nơ-ron bằng cách tính toán gradient của hàm mất mát đối với trọng số mạng. Nó hoạt động bằng cách lan truyền lỗi ngược qua mạng, cập nhật trọng số để giảm thiểu mất mát.",
            "category": "explanation",
            "complexity": "high"
        },
        {
            "query": "Mạng nơ-ron tích chập là gì và chúng được sử dụng để làm gì?",
            "expected_answer": "Mạng nơ-ron tích chập (CNN) là một loại mạng nơ-ron được thiết kế để xử lý dữ liệu lưới có cấu trúc như hình ảnh. Chúng sử dụng các lớp tích chập để tự động phát hiện đặc trưng và chủ yếu được sử dụng cho nhận dạng hình ảnh, phân loại và các tác vụ thị giác máy tính.",
            "category": "explanation",
            "complexity": "high"
        },
        {
            "query": "Phân cụm trong học máy hoạt động như thế nào?",
            "expected_answer": "Phân cụm trong học máy hoạt động bằng cách nhóm các điểm dữ liệu tương tự nhau dựa trên các đặc trưng hoặc đặc điểm của chúng. Các thuật toán như K-means, phân cụm phân cấp hoặc DBSCAN xác định các mẫu trong dữ liệu để hình thành các cụm mà không cần ví dụ được gán nhãn.",
            "category": "explanation",
            "complexity": "medium"
        },
        {
            "query": "Transfer learning là gì và tại sao nó quan trọng?",
            "expected_answer": "Transfer learning là một kỹ thuật trong đó một mô hình được phát triển cho một nhiệm vụ được tái sử dụng làm điểm khởi đầu cho một mô hình trên nhiệm vụ thứ hai. Nó quan trọng vì cho phép tận dụng kiến thức từ các mô hình đã được huấn luyện trước, giảm thời gian huấn luyện và yêu cầu dữ liệu.",
            "category": "explanation",
            "complexity": "medium"
        },
        {
            "query": "Giải thích sự đánh đổi giữa bias và variance trong học máy.",
            "expected_answer": "Sự đánh đổi giữa bias và variance là một khái niệm cơ bản trong học máy, trong đó các mô hình có bias cao sẽ underfit dữ liệu (quá đơn giản), trong khi các mô hình có variance cao sẽ overfit dữ liệu (quá phức tạp). Tìm được sự cân bằng phù hợp là rất quan trọng để có khả năng tổng quát hóa tốt.",
            "category": "explanation",
            "complexity": "high"
        },
        {
            "query": "Sự khác biệt giữa phân loại và hồi quy là gì?",
            "expected_answer": "Phân loại dự đoán các nhãn lớp hoặc danh mục rời rạc, trong khi hồi quy dự đoán các giá trị số liên tục. Phân loại trả lời câu hỏi 'thuộc loại nào?', trong khi hồi quy trả lời câu hỏi 'bao nhiêu?'.",
            "category": "comparison",
            "complexity": "low"
        },
        {
            "query": "Xử lý ngôn ngữ tự nhiên hoạt động như thế nào?",
            "expected_answer": "Xử lý ngôn ngữ tự nhiên hoạt động bằng cách áp dụng các kỹ thuật tính toán để phân tích, hiểu và tạo ra ngôn ngữ con người. Nó bao gồm các bước như tokenization, gắn thẻ từ loại, phân tích cú pháp, phân tích ngữ nghĩa và thường sử dụng các mô hình học máy để xử lý dữ liệu văn bản.",
            "category": "explanation",
            "complexity": "high"
        },
        {
            "query": "Vai trò của các hàm kích hoạt trong mạng nơ-ron là gì?",
            "expected_answer": "Các hàm kích hoạt đưa tính phi tuyến tính vào mạng nơ-ron, cho phép chúng học các mẫu phức tạp. Chúng xác định liệu một nơ-ron có nên được kích hoạt dựa trên tổng có trọng số của đầu vào, cho phép mạng mô hình hóa các mối quan hệ phi tuyến tính trong dữ liệu.",
            "category": "explanation",
            "complexity": "medium"
        },
        {
            "query": "Giải thích cách hệ thống đề xuất hoạt động.",
            "expected_answer": "Hệ thống đề xuất hoạt động bằng cách phân tích hành vi, sở thích của người dùng và đặc điểm của mục để đề xuất các mục liên quan. Chúng thường sử dụng lọc cộng tác (dựa trên sự tương đồng của người dùng), lọc dựa trên nội dung (dựa trên đặc điểm của mục) hoặc các phương pháp kết hợp.",
            "category": "explanation",
            "complexity": "medium"
        },
        {
            "query": "Sự khác biệt giữa học có giám sát, học không giám sát và học tăng cường là gì?",
            "expected_answer": "Học có giám sát sử dụng dữ liệu đã được gán nhãn để học ánh xạ từ đầu vào đến đầu ra. Học không giám sát tìm các mẫu trong dữ liệu không có nhãn. Học tăng cường liên quan đến một tác nhân học cách đưa ra quyết định bằng cách thực hiện các hành động và nhận phần thưởng hoặc hình phạt.",
            "category": "comparison",
            "complexity": "medium"
        }
    ]
    
    # Create a DataFrame
    df = pd.DataFrame(samples)
    
    # Replicate samples if needed
    if size > len(samples):
        df = pd.concat([df] * (size // len(samples) + 1), ignore_index=True)
    
    # Trim to the requested size
    df = df.head(size)
    
    return df

def main():
    """Main function."""
    args = parse_args()
    
    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Create English dataset
    print(f"Creating English dataset with {args.english_size} samples...")
    english_df = create_english_dataset(args.english_size)
    english_path = os.path.join(args.output_dir, "english_dataset.csv")
    english_df.to_csv(english_path, index=False)
    print(f"English dataset saved to {english_path}")
    
    # Create Vietnamese dataset
    print(f"Creating Vietnamese dataset with {args.vietnamese_size} samples...")
    vietnamese_df = create_vietnamese_dataset(args.vietnamese_size)
    vietnamese_path = os.path.join(args.output_dir, "vietnamese_dataset.csv")
    vietnamese_df.to_csv(vietnamese_path, index=False)
    print(f"Vietnamese dataset saved to {vietnamese_path}")

if __name__ == "__main__":
    main()
