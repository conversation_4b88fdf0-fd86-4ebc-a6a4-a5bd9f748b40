[tox]
envlist = py38, py39, py310, lint, type, docs
isolated_build = True

[testenv]
deps =
    pytest
    pytest-cov
commands =
    pytest {posargs:tests}

[testenv:lint]
deps =
    black
    isort
    flake8
commands =
    black --check src tests examples
    isort --check-only src tests examples
    flake8 src tests examples

[testenv:type]
deps =
    mypy
commands =
    mypy src

[testenv:docs]
deps =
    sphinx
    sphinx-rtd-theme
    myst-parser
commands =
    sphinx-build -b html docs/source docs/build/html

[flake8]
max-line-length = 88
extend-ignore = E203
exclude = .git,__pycache__,docs/source/conf.py,old,build,dist,venv
