[pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Markers for different test types
markers =
    unit: Unit tests
    integration: Integration tests
    performance: Performance tests
    benchmark: Benchmark tests
    slow: Slow tests that take a long time to run
    vietnamese: Tests for Vietnamese language features
    cot: Tests for Chain of Thought
    tot: Tests for Tree of Thought
    rag: Tests for Retrieval Augmented Generation
    cotrag: Tests for CoT-RAG
    totrag: Tests for ToT-RAG
    ragtotcot: Tests for RAG-TOT-COT

# Logging configuration
log_cli = True
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(message)s (%(filename)s:%(lineno)s)
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Coverage configuration
addopts = --strict-markers

# Parallel test execution configuration
xdist_concurrency = auto
xdist_worker_setup_timeout = 30.0
xdist_worker_teardown_timeout = 10.0
xdist_group_by_fixture = true
xdist_group_by_module = true

# Timeout configuration
timeout = 300
timeout_method = thread
timeout_func_only = false

# Benchmark configuration
benchmark_columns = min, max, mean, stddev, median, iqr, outliers, rounds, iterations
benchmark_sort = mean
benchmark_warmup = true
benchmark_warmup_iterations = 1
benchmark_min_rounds = 5
benchmark_max_time = 1.0
benchmark_disable_gc = true
benchmark_timer = time.perf_counter
