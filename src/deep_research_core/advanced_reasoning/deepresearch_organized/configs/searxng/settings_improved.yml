use_default_settings: true

server:
  secret_key: "ultrasecretkey"  # Thay đổi thành một khóa bí mật
  limiter: false
  public_instance: false
  image_proxy: true

ui:
  static_use_hash: true
  default_theme: simple
  theme_args:
    simple_style: auto
  query_in_title: true
  infinite_scroll: false
  center_alignment: false
  results_on_new_tab: false
  advanced_search: true  # Bật tìm kiếm nâng cao
  num_results: 20  # Tăng số lượng kết quả mặc định

search:
  safe_search: 0
  autocomplete: "google"
  autocomplete_min: 4
  default_lang: "vi"  # Ngôn ngữ mặc định là tiếng Việt
  formats:
    - html
    - json
    - csv
    - rss

redis:
  url: redis://redis:6379/0

enabled_plugins:
  - 'Hash plugin'
  - 'Search on category select'
  - 'Self Informations'
  - 'Tracker URL remover'
  - 'Vim-like hotkeys'
  - 'Hostname replace'  # Thêm plugin này để thay thế hostname
  - 'Open Access DOI rewrite'  # Thêm plugin này để viết lại DOI
  - 'Infinite scroll'  # Thêm plugin này để cuộn vô hạn

engines:
  # Công cụ tìm kiếm chung
  - name: google
    engine: google
    shortcut: go
    use_mobile_ui: false
    disabled: false
    
  - name: duckduckgo
    engine: duckduckgo
    shortcut: ddg
    disabled: false
    
  - name: bing
    engine: bing
    shortcut: bi
    disabled: false
    
  - name: qwant
    engine: qwant
    shortcut: qw
    disabled: false
    
  # Công cụ tìm kiếm Wikipedia
  - name: wikipedia
    engine: wikipedia
    shortcut: wp
    disabled: false
    
  - name: wikidata
    engine: wikidata
    shortcut: wd
    disabled: false
    
  # Công cụ tìm kiếm tiếng Việt
  - name: wikipedia vi
    engine: wikipedia
    shortcut: wpvi
    base_url: https://vi.wikipedia.org/
    categories: general
    disabled: false
    
  - name: wikivoyage vi
    engine: mediawiki
    shortcut: wvvi
    categories: general
    base_url: https://vi.wikivoyage.org/
    disabled: false
    
  - name: baomoi
    engine: xpath
    shortcut: bm
    categories: news
    search_url: https://baomoi.com/tim-kiem/{query}.epi
    results_xpath: //div[contains(@class, "story")]
    url_xpath: .//a/@href
    title_xpath: .//a/@title
    content_xpath: .//div[contains(@class, "story__summary")]
    suggestion_xpath: //a[contains(@class, "keyword")]/@title
    disabled: false
    
  - name: coccoc
    engine: xpath
    shortcut: cc
    categories: general
    search_url: https://coccoc.com/search#query={query}
    results_xpath: //div[contains(@class, "search-result")]
    url_xpath: .//a/@href
    title_xpath: .//a/text()
    content_xpath: .//div[contains(@class, "snippet")]
    disabled: false
    
  - name: tuoitre
    engine: xpath
    shortcut: tt
    categories: news
    search_url: https://tuoitre.vn/tim-kiem.htm?keywords={query}
    results_xpath: //div[contains(@class, "news-item")]
    url_xpath: .//a/@href
    title_xpath: .//a/@title
    content_xpath: .//div[contains(@class, "lead")]
    disabled: false
    
  - name: vnexpress
    engine: xpath
    shortcut: vne
    categories: news
    search_url: https://timkiem.vnexpress.net/?q={query}
    results_xpath: //article[contains(@class, "item-news")]
    url_xpath: .//a/@href
    title_xpath: .//a/@title
    content_xpath: .//p[contains(@class, "description")]
    disabled: false
    
  - name: tinhte
    engine: xpath
    shortcut: tinhte
    categories: general
    search_url: https://tinhte.vn/search/?q={query}
    results_xpath: //div[contains(@class, "contentRow")]
    url_xpath: .//h3[contains(@class, "contentRow-title")]/a/@href
    title_xpath: .//h3[contains(@class, "contentRow-title")]/a/text()
    content_xpath: .//div[contains(@class, "contentRow-snippet")]
    disabled: false
    
  # Công cụ tìm kiếm học thuật
  - name: semantic scholar
    engine: xpath
    shortcut: ss
    categories: science
    search_url: https://www.semanticscholar.org/search?q={query}
    results_xpath: //div[contains(@class, "result-page")]//div[contains(@class, "result-card")]
    url_xpath: .//a/@href
    title_xpath: .//a/div/text()
    content_xpath: .//div[contains(@class, "abstract")]
    disabled: false
    
  - name: arxiv
    engine: xpath
    shortcut: ax
    categories: science
    search_url: https://arxiv.org/search/?query={query}&searchtype=all
    results_xpath: //li[contains(@class, "arxiv-result")]
    url_xpath: .//p[contains(@class, "list-title")]/a/@href
    title_xpath: .//p[contains(@class, "title")]/text()
    content_xpath: .//span[contains(@class, "abstract-full")]/text()
    disabled: false
    
  - name: google scholar
    engine: google_scholar
    shortcut: gs
    categories: science
    disabled: false
