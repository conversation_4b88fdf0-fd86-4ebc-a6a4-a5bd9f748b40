use_default_settings: true

server:
  secret_key: "ultrasecretkey"  # Thay đổi thành một khóa bí mật
  limiter: false
  public_instance: false
  image_proxy: true

ui:
  static_use_hash: true
  default_theme: simple
  theme_args:
    simple_style: auto
  query_in_title: true
  infinite_scroll: false
  center_alignment: false
  results_on_new_tab: false
  advanced_search: false
  num_results: 10

search:
  safe_search: 0
  autocomplete: "google"
  autocomplete_min: 4
  default_lang: "vi"  # Thay đổi ngôn ngữ mặc định nếu cần

redis:
  url: redis://redis:6379/0

enabled_plugins:
  - 'Hash plugin'
  - 'Search on category select'
  - 'Self Informations'
  - 'Tracker URL remover'
  - 'Vim-like hotkeys'

engines:
  - name: google
    engine: google
    shortcut: go
    use_mobile_ui: false
    disabled: false
    
  - name: duckduckgo
    engine: duckduckgo
    shortcut: ddg
    disabled: false
    
  - name: bing
    engine: bing
    shortcut: bi
    disabled: false
    
  - name: qwant
    engine: qwant
    shortcut: qw
    disabled: false
    
  - name: wikipedia
    engine: wikipedia
    shortcut: wp
    disabled: false
    
  - name: wikidata
    engine: wikidata
    shortcut: wd
    disabled: false
    
  - name: wikivoyage
    engine: mediawiki
    shortcut: wv
    categories: general
    base_url: https://en.wikivoyage.org/
    disabled: false
    
  - name: wikivoyage vi
    engine: mediawiki
    shortcut: wvvi
    categories: general
    base_url: https://vi.wikivoyage.org/
    disabled: false
    
  - name: wikipedia vi
    engine: wikipedia
    shortcut: wpvi
    base_url: https://vi.wikipedia.org/
    categories: general
    disabled: false
    
  - name: baomoi
    engine: xpath
    shortcut: bm
    categories: news
    search_url: https://baomoi.com/tim-kiem/{query}.epi
    results_xpath: //div[contains(@class, "story")]
    url_xpath: .//a/@href
    title_xpath: .//a/@title
    content_xpath: .//div[contains(@class, "story__summary")]
    suggestion_xpath: //a[contains(@class, "keyword")]/@title
    disabled: false
    
  - name: coccoc
    engine: xpath
    shortcut: cc
    categories: general
    search_url: https://coccoc.com/search#query={query}
    results_xpath: //div[contains(@class, "search-result")]
    url_xpath: .//a/@href
    title_xpath: .//a/text()
    content_xpath: .//div[contains(@class, "snippet")]
    disabled: false
