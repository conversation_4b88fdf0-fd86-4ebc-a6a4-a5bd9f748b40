# Deep Research Core - Organized Structure

## 📁 Cấu trúc thư mục đã được tổ chức

```
deepresearch_organized/
├── src/                           # Source code chính (167+ files)
│   ├── agents/                    # Web search agents (5 files)
│   │   ├── adaptive_crawler.py
│   │   ├── web_search_agent_local.py
│   │   └── web_search_agent_enhanced.py
│   ├── reasoning/                 # Reasoning modules
│   ├── rag/                      # RAG implementations
│   ├── utils/                    # Utility functions (5 files)
│   │   ├── query_decomposer.py
│   │   ├── captcha_handler.py
│   │   └── answer_quality_evaluator.py
│   ├── models/                   # Model integrations (18 files)
│   │   ├── anthropic_reward_model.py
│   │   ├── openr1_integration.py
│   │   └── vietnamese_search_integration.py
│   ├── config/                   # Configuration
│   ├── scripts/                  # Additional scripts
│   ├── tests/                    # Additional test files
│   └── [150+ other Python files] # All core functionality
├── tests/                        # Test cases (100+ files)
│   ├── unit/                     # Unit tests
│   ├── integration/              # Integration tests
│   ├── performance/              # Performance tests
│   └── evaluation/               # Evaluation tests
├── docs/                         # Documentation (100+ files)
│   ├── guides/                   # User guides
│   ├── api/                      # API documentation
│   ├── tutorials/                # Tutorials
│   └── examples/                 # Code examples
├── scripts/                      # Utility scripts (15+ files)
│   ├── setup/                    # Setup scripts
│   └── deployment/               # Deployment scripts
├── configs/                      # Configuration files (30+ files)
│   ├── docker/                   # Docker configurations
│   └── environments/             # Environment configs
├── data/                         # Data files
│   ├── cache/                    # Cache data
│   ├── output/                   # Output files
│   └── samples/                  # Sample data
├── frontend/                     # Frontend code
├── notebooks/                    # Jupyter notebooks (4 files)
├── benchmarks/                   # Benchmark tests
└── temp/                         # Temporary files (7 files)
```

## 🎯 Lợi ích của cấu trúc mới

### ✅ **Tổ chức rõ ràng**
- Source code được phân loại theo chức năng
- Tests được tách biệt và categorize
- Documentation có cấu trúc logic
- Configuration files tập trung

### ✅ **Dễ navigate**
- Tìm kiếm files nhanh chóng
- Hiểu rõ chức năng từng module
- Maintain code dễ dàng hơn

### ✅ **Chuẩn hóa**
- Tuân thủ Python project structure
- Sẵn sàng cho production
- CI/CD setup đơn giản

## 📊 Thống kê

| Category | Files | Description |
|----------|-------|-------------|
| **Source Code** | 167+ | All Python source files in src/ |
| **Agents** | 5 | Web search và crawler agents |
| **Utils** | 5 | Query processing, captcha handling |
| **Models** | 18 | Model integrations và reward models |
| **Tests** | 200+ | Unit, integration, performance tests |
| **Docs** | 150+ | Guides, tutorials, examples |
| **Scripts** | 25+ | Setup, deployment, utility scripts |
| **Configs** | 40+ | Docker, environment configurations |
| **Data** | - | Cache, output, samples, volumes |
| **Frontend** | - | React frontend application |
| **Notebooks** | 4 | Jupyter tutorials |
| **Temp** | 7 | Temporary implementation files |
| **TOTAL** | **1692+** | **Complete Python files** |

## 🚀 Sử dụng

### Import modules
```python
# Agents
from deepresearch_organized.src.agents.web_search_agent_local import WebSearchAgentLocal
from deepresearch_organized.src.agents.adaptive_crawler import AdaptiveCrawler

# Utils
from deepresearch_organized.src.utils.query_decomposer import QueryDecomposer
from deepresearch_organized.src.utils.captcha_handler import CaptchaHandler

# Models
from deepresearch_organized.src.models.anthropic_reward_model import AnthropicRewardModel
```

### Run tests
```bash
# All tests
python scripts/run_all_tests.py

# Specific category
python -m pytest tests/unit/
python -m pytest tests/integration/
```

### Documentation
- **Guides**: `docs/guides/` - User guides và setup instructions
- **API**: `docs/api/` - API documentation
- **Examples**: `docs/examples/` - Code examples và tutorials

## 🔧 Development

### Setup
```bash
# Install dependencies
pip install -r configs/requirements.txt

# Setup development environment
python scripts/setup/setup.py
```

### Configuration
- **Docker**: `configs/docker/`
- **Environment**: `configs/environments/`
- **Settings**: `configs/*.yml`

---

**Cấu trúc này thay thế thư mục deepresearch cũ với tổ chức tốt hơn và dễ maintain hơn.**
