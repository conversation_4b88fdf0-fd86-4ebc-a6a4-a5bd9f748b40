{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Proximal Policy Optimization (PPO) Tutorial\n", "\n", "This notebook demonstrates how to use the PPO module for reinforcement learning from human feedback."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup\n", "\n", "First, let's import the necessary modules and set up our environment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import torch\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from tqdm.notebook import tqdm\n", "\n", "# Import PPO modules\n", "from deep_research_core.rl_tuning.ppo import (\n", "    PPO, OpenAIPPO, OpenRouterPPO, DeepSeekPPO, QwQPPO,\n", "    compute_advantages_from_rewards, compute_gae, normalize_advantages\n", ")\n", "\n", "# Set up output directory\n", "output_dir = \"./ppo_output\"\n", "os.makedirs(output_dir, exist_ok=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define a Reward Function\n", "\n", "Let's define a reward function that will be used to evaluate model responses."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def reward_fn(prompt, response):\n", "    \"\"\"\n", "    Reward function for evaluating model responses.\n", "    \n", "    Args:\n", "        prompt: Input prompt\n", "        response: Model response\n", "        \n", "    Returns:\n", "        Reward value between 0 and 1\n", "    \"\"\"\n", "    # 1. Length component - penalize very short or very long responses\n", "    words = response.split()\n", "    word_count = len(words)\n", "    \n", "    if word_count < 10:\n", "        length_reward = word_count / 10\n", "    elif word_count > 100:\n", "        length_reward = max(0, 1 - (word_count - 100) / 100)\n", "    else:\n", "        length_reward = 1.0\n", "    \n", "    # 2. Keyword component - reward presence of relevant keywords\n", "    keywords = [\"helpful\", \"informative\", \"detailed\", \"clear\", \"concise\"]\n", "    keyword_count = sum(1 for keyword in keywords if keyword.lower() in response.lower())\n", "    keyword_reward = min(1.0, keyword_count / 3)\n", "    \n", "    # Combine rewards with weights\n", "    final_reward = 0.6 * length_reward + 0.4 * keyword_reward\n", "    \n", "    return final_reward"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initialize a PPO Model\n", "\n", "Now, let's initialize a PPO model. We'll use the OpenRouter implementation for this example."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Replace with your actual API key\n", "api_key = \"your-openrouter-api-key\"\n", "\n", "# Initialize PPO model\n", "ppo_model = OpenRouterPPO(\n", "    model_name=\"anthropic/claude-2\",\n", "    api_key=api_key,\n", "    reward_fn=reward_fn,\n", "    max_length=512,\n", "    batch_size=4,\n", "    learning_rate=5e-5,\n", "    output_dir=output_dir,\n", "    verbose=True\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prepare Training Prompts\n", "\n", "Let's define some prompts for training the model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prompts = [\n", "    \"Explain the concept of reinforcement learning.\",\n", "    \"What are the key differences between supervised and unsupervised learning?\",\n", "    \"How does a neural network work?\",\n", "    \"What is transfer learning and why is it useful?\",\n", "    \"Explain the concept of overfitting in machine learning.\"\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate Initial Responses\n", "\n", "Let's generate initial responses from the model to see how it performs before training."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Initial responses:\")\n", "for prompt in prompts[:2]:  # Just use the first 2 prompts for demonstration\n", "    response = ppo_model.generate(prompt)\n", "    reward = reward_fn(prompt, response)\n", "    \n", "    print(f\"\\nPrompt: {prompt}\")\n", "    print(f\"Response: {response}\")\n", "    print(f\"Reward: {reward:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Train the Model with PPO\n", "\n", "Now, let's train the model using PPO for a few iterations."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["num_iterations = 3  # In practice, you might want to use more iterations\n", "\n", "# Store metrics for visualization\n", "all_metrics = []\n", "\n", "for iteration in range(num_iterations):\n", "    print(f\"\\nIteration {iteration+1}/{num_iterations}\")\n", "    \n", "    # Train iteration\n", "    metrics = ppo_model.train_iteration(prompts=prompts)\n", "    all_metrics.append(metrics)\n", "    \n", "    # Print metrics\n", "    print(f\"Metrics: {metrics}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualize Training Progress\n", "\n", "Let's visualize the training progress using the metrics we collected."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extract metrics\n", "iterations = list(range(1, num_iterations + 1))\n", "mean_rewards = [metrics.get('mean_reward', 0) for metrics in all_metrics]\n", "mean_kl = [metrics.get('mean_kl', 0) for metrics in all_metrics]\n", "\n", "# Create figure\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))\n", "\n", "# Plot mean reward\n", "ax1.plot(iterations, mean_rewards, 'b-', marker='o')\n", "ax1.set_xlabel('Iteration')\n", "ax1.set_ylabel('Mean Reward')\n", "ax1.set_title('Mean Reward vs. Iteration')\n", "ax1.grid(True)\n", "\n", "# Plot mean KL divergence\n", "ax2.plot(iterations, mean_kl, 'r-', marker='o')\n", "ax2.set_xlabel('Iteration')\n", "ax2.set_ylabel('Mean KL Divergence')\n", "ax2.set_title('Mean KL Divergence vs. Iteration')\n", "ax2.grid(True)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate Responses After Training\n", "\n", "Let's generate responses from the trained model to see how it has improved."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Responses after training:\")\n", "for prompt in prompts[:2]:  # Just use the first 2 prompts for demonstration\n", "    response = ppo_model.generate(prompt)\n", "    reward = reward_fn(prompt, response)\n", "    \n", "    print(f\"\\nPrompt: {prompt}\")\n", "    print(f\"Response: {response}\")\n", "    print(f\"Reward: {reward:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save the Trained Model\n", "\n", "Finally, let's save the trained model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["save_path = ppo_model.save_checkpoint()\n", "print(f\"Model saved to {save_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Exploring Advantage Calculation\n", "\n", "Let's explore the advantage calculation utilities provided by the PPO module."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create sample data\n", "rewards = torch.tensor([1.0, 0.5, 0.0, 2.0, 1.0])\n", "values = torch.tensor([0.5, 0.4, 0.3, 0.2, 0.1])\n", "dones = torch.tensor([0.0, 0.0, 0.0, 0.0, 1.0])\n", "next_values = torch.tensor([0.4, 0.3, 0.2, 0.1, 0.0])\n", "\n", "# Compute GAE\n", "advantages = compute_gae(\n", "    rewards=rewards,\n", "    values=values,\n", "    dones=dones,\n", "    next_values=next_values,\n", "    gamma=0.99,\n", "    lam=0.95\n", ")\n", "\n", "print(\"Generalized Advantage Estimation (GAE):\")\n", "print(advantages)\n", "\n", "# Normalize advantages\n", "normalized_advantages = normalize_advantages(advantages)\n", "\n", "print(\"\\nNormalized advantages:\")\n", "print(normalized_advantages)\n", "\n", "# Compute advantages from rewards\n", "rewards_list = [1.0, 0.5, 0.0, 2.0, 1.0]\n", "advantages_from_rewards = compute_advantages_from_rewards(\n", "    rewards=rewards_list,\n", "    gamma=0.99,\n", "    normalize=True\n", ")\n", "\n", "print(\"\\nAdvantages computed directly from rewards:\")\n", "print(advantages_from_rewards)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "In this notebook, we've demonstrated how to use the PPO module for reinforcement learning from human feedback. We've covered:\n", "\n", "1. Initializing a PPO model\n", "2. Defining a reward function\n", "3. Training the model with PPO\n", "4. Visualizing training progress\n", "5. Generating responses before and after training\n", "6. Exploring advantage calculation utilities\n", "\n", "This approach can be used to fine-tune language models to align with specific preferences or requirements."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}