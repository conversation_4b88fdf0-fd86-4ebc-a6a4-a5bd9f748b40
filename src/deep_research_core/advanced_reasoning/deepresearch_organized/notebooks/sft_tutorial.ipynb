{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Supervised Fine-Tuning (SFT) Tutorial\n", "\n", "This notebook demonstrates how to use the SFT module for fine-tuning language models with supervised learning."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup\n", "\n", "First, let's import the necessary modules and set up our environment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "import torch\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from tqdm.notebook import tqdm\n", "\n", "# Import SFT modules\n", "from deep_research_core.rl_tuning.sft import (\n", "    HuggingFaceSFT, OpenAISFT, OpenRouterSFT, DeepSeekSFT, QwQSFT,\n", "    SFTEvaluator, evaluate_model,\n", "    SFTDataAugmenter, augment_sft_data\n", ")\n", "\n", "# Set up output directory\n", "output_dir = \"./sft_output\"\n", "os.makedirs(output_dir, exist_ok=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create Sample Data\n", "\n", "Let's create some sample data for fine-tuning."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_sample_data(num_examples=20):\n", "    \"\"\"\n", "    Create sample data for fine-tuning.\n", "    \n", "    Args:\n", "        num_examples: Number of examples to create\n", "        \n", "    Returns:\n", "        List of examples\n", "    \"\"\"\n", "    data = []\n", "    \n", "    # Add examples for text classification\n", "    for i in range(num_examples // 4):\n", "        if i % 2 == 0:\n", "            data.append({\n", "                \"input\": f\"Classify the sentiment: This movie was great, I loved it!\",\n", "                \"output\": \"Positive\"\n", "            })\n", "        else:\n", "            data.append({\n", "                \"input\": f\"Classify the sentiment: This movie was terrible, I hated it!\",\n", "                \"output\": \"Negative\"\n", "            })\n", "    \n", "    # Add examples for text generation\n", "    for i in range(num_examples // 4):\n", "        data.append({\n", "            \"input\": f\"Write a short story about a {['cat', 'dog', 'robot', 'alien', 'wizard'][i % 5]}.\",\n", "            \"output\": f\"Once upon a time, there was a {['cat', 'dog', 'robot', 'alien', 'wizard'][i % 5]} named {['<PERSON><PERSON><PERSON>', '<PERSON>', 'R2D2', '<PERSON><PERSON><PERSON>', '<PERSON>'][i % 5]}. \"\n", "                      f\"They lived in a {['house', 'forest', 'laboratory', 'spaceship', 'tower'][i % 5]} and loved to {['play', 'run', 'compute', 'explore', 'cast spells'][i % 5]}.\"\n", "        })\n", "    \n", "    # Add examples for question answering\n", "    for i in range(num_examples // 4):\n", "        data.append({\n", "            \"input\": f\"What is the capital of {['France', 'Germany', 'Japan', 'Brazil', 'Australia'][i % 5]}?\",\n", "            \"output\": f\"{['Paris', 'Berlin', 'Tokyo', 'Brasília', 'Canberra'][i % 5]}\"\n", "        })\n", "    \n", "    # Add examples for summarization\n", "    for i in range(num_examples // 4):\n", "        data.append({\n", "            \"input\": f\"Summarize the following text: Lorem ipsum dolor sit amet, consectetur adipiscing elit. \"\n", "                    f\"Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. \"\n", "                    f\"Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\",\n", "            \"output\": f\"A brief summary of a Latin text about work and effort.\"\n", "        })\n", "    \n", "    return data\n", "\n", "# Create sample data\n", "data = create_sample_data(num_examples=20)\n", "\n", "# Split data into train and eval sets\n", "train_size = int(0.8 * len(data))\n", "train_data = data[:train_size]\n", "eval_data = data[train_size:]\n", "\n", "print(f\"Created {len(train_data)} training examples and {len(eval_data)} evaluation examples.\")\n", "\n", "# Display a few examples\n", "print(\"\\nSample training examples:\")\n", "for i, example in enumerate(train_data[:3]):\n", "    print(f\"\\nExample {i+1}:\")\n", "    print(f\"Input: {example['input']}\")\n", "    print(f\"Output: {example['output']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Augmentation\n", "\n", "Let's augment the training data to increase its size and diversity."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Augment training data\n", "augmented_data = augment_sft_data(\n", "    data=train_data,\n", "    input_key=\"input\",\n", "    output_key=\"output\",\n", "    augmentation_methods=[\"synonym_replacement\", \"random_swap\", \"random_deletion\"],\n", "    augmentation_factor=2,  # Double the dataset size\n", "    verbose=True\n", ")\n", "\n", "print(f\"Original training data size: {len(train_data)}\")\n", "print(f\"Augmented training data size: {len(augmented_data)}\")\n", "\n", "# Display a few augmented examples\n", "print(\"\\nSample augmented examples:\")\n", "for i, example in enumerate(augmented_data[len(train_data):len(train_data)+3]):\n", "    print(f\"\\nExample {i+1}:\")\n", "    print(f\"Input: {example['input']}\")\n", "    print(f\"Output: {example['output']}\")\n", "    print(f\"Augmentation method: {example.get('augmentation_method', 'N/A')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initialize an SFT Model\n", "\n", "Now, let's initialize an SFT model. We'll use the OpenRouter implementation for this example."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Replace with your actual API key\n", "api_key = \"your-openrouter-api-key\"\n", "\n", "# Initialize SFT model\n", "sft_model = OpenRouterSFT(\n", "    model_name=\"anthropic/claude-2\",\n", "    api_key=api_key,\n", "    output_dir=output_dir\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prepare Datasets\n", "\n", "Let's prepare the datasets for training."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Prepare datasets\n", "train_dataset, eval_dataset = sft_model.prepare_data(\n", "    train_data=augmented_data,\n", "    eval_data=eval_data,\n", "    input_key=\"input\",\n", "    output_key=\"output\"\n", ")\n", "\n", "print(f\"Prepared training dataset with {len(train_dataset)} examples.\")\n", "print(f\"Prepared evaluation dataset with {len(eval_dataset)} examples.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate Initial Responses\n", "\n", "Let's generate initial responses from the model to see how it performs before fine-tuning."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Initial responses:\")\n", "for example in eval_data[:2]:  # Just use the first 2 examples for demonstration\n", "    prompt = example[\"input\"]\n", "    reference = example[\"output\"]\n", "    response = sft_model.generate_text(prompt)\n", "    \n", "    print(f\"\\nPrompt: {prompt}\")\n", "    print(f\"Reference: {reference}\")\n", "    print(f\"Response: {response}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fine-<PERSON><PERSON> the <PERSON>\n", "\n", "Now, let's fine-tune the model using supervised learning."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Fine-tune the model\n", "metrics = sft_model.train_model(\n", "    train_dataset=train_dataset,\n", "    eval_dataset=eval_dataset,\n", "    num_epochs=3,\n", "    batch_size=4,\n", "    learning_rate=5e-5\n", ")\n", "\n", "print(f\"Fine-tuning metrics: {metrics}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate Responses After Fine-Tuning\n", "\n", "Let's generate responses from the fine-tuned model to see how it has improved."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Responses after fine-tuning:\")\n", "for example in eval_data[:2]:  # Just use the first 2 examples for demonstration\n", "    prompt = example[\"input\"]\n", "    reference = example[\"output\"]\n", "    response = sft_model.generate_text(prompt)\n", "    \n", "    print(f\"\\nPrompt: {prompt}\")\n", "    print(f\"Reference: {reference}\")\n", "    print(f\"Response: {response}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluate the Model\n", "\n", "Let's evaluate the fine-tuned model using various metrics."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define a model generator function for evaluation\n", "def model_generator(prompt):\n", "    return sft_model.generate_text(prompt)\n", "\n", "# Create evaluator\n", "evaluator = SFTEvaluator(\n", "    model_generator=model_generator,\n", "    output_dir=os.path.join(output_dir, \"evaluation\"),\n", "    verbose=True\n", ")\n", "\n", "# Extract prompts and references\n", "prompts = [example[\"input\"] for example in eval_data]\n", "references = [example[\"output\"] for example in eval_data]\n", "\n", "# Evaluate exact match accuracy\n", "exact_match_results = evaluator.evaluate_exact_match(\n", "    prompts=prompts,\n", "    references=references,\n", "    save_results=True\n", ")\n", "\n", "print(f\"Exact match results: {exact_match_results}\")\n", "\n", "# Evaluate text similarity\n", "similarity_results = evaluator.evaluate_similarity(\n", "    prompts=prompts,\n", "    references=references,\n", "    save_results=True\n", ")\n", "\n", "print(f\"Similarity results: {similarity_results}\")\n", "\n", "# Evaluate generation quality\n", "quality_results = evaluator.evaluate_generation_quality(\n", "    prompts=prompts,\n", "    references=references,\n", "    save_results=True\n", ")\n", "\n", "print(f\"Generation quality results: {quality_results}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualize Evaluation Results\n", "\n", "Let's visualize the evaluation results."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize evaluation results\n", "evaluator.visualize_results(save_path=os.path.join(output_dir, \"evaluation_visualization.png\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save the Fine-Tuned Model\n", "\n", "Finally, let's save the fine-tuned model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["save_path = sft_model.save_model()\n", "print(f\"Model saved to {save_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Exploring Data Augmentation\n", "\n", "Let's explore the data augmentation capabilities in more detail."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a data augmenter\n", "augmenter = SFTDataAugmenter(\n", "    input_key=\"input\",\n", "    output_key=\"output\",\n", "    random_seed=42,\n", "    verbose=True\n", ")\n", "\n", "# Sample data for augmentation\n", "sample_data = [\n", "    {\"input\": \"What is machine learning?\", \"output\": \"Machine learning is a branch of artificial intelligence...\"},\n", "    {\"input\": \"Explain neural networks.\", \"output\": \"Neural networks are computing systems inspired by the human brain...\"}\n", "]\n", "\n", "# Try different augmentation methods\n", "augmentation_methods = [\n", "    \"synonym_replacement\",\n", "    \"random_deletion\",\n", "    \"random_swap\",\n", "    \"random_insertion\",\n", "    \"back_translation\",\n", "    \"paraphrasing\",\n", "    \"eda\"\n", "]\n", "\n", "for method in augmentation_methods:\n", "    print(f\"\\nAugmentation method: {method}\")\n", "    \n", "    augmented = augmenter.augment_data(\n", "        data=sample_data,\n", "        augmentation_methods=[method],\n", "        augmentation_factor=1\n", "    )\n", "    \n", "    # Display augmented examples\n", "    for i, example in enumerate(augmented[len(sample_data):]):\n", "        print(f\"Original: {sample_data[i]['input']}\")\n", "        print(f\"Augmented: {example['input']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "In this notebook, we've demonstrated how to use the SFT module for supervised fine-tuning of language models. We've covered:\n", "\n", "1. Creating and preparing training data\n", "2. Augmenting data to increase size and diversity\n", "3. Initializing an SFT model\n", "4. Fine-tuning the model\n", "5. Evaluating the model using various metrics\n", "6. Visualizing evaluation results\n", "7. Exploring different data augmentation methods\n", "\n", "This approach can be used to adapt language models to specific tasks or domains using labeled data."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}