{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Generalized Reward-based Policy Optimization (GRPO) Tutorial\n", "\n", "This notebook demonstrates how to use the GRPO module for optimizing language model policies using various reward functions."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup\n", "\n", "First, let's import the necessary modules and set up our environment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "import torch\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from tqdm.notebook import tqdm\n", "\n", "# Import GRPO modules\n", "from deep_research_core.rl_tuning.grpo import (\n", "    OpenRouterGRPO, DeepSeekGRPO, QwQGRPO,\n", "    BLEURewardFunction, ROUGERewardFunction, LengthRewardFunction,\n", "    KeywordRewardFunction, FormatRewardFunction, DiversityRewardFunction,\n", "    CombinedRewardFunction, OutcomeRewardFunction\n", ")\n", "\n", "# Set up output directory\n", "output_dir = \"./grpo_output\"\n", "os.makedirs(output_dir, exist_ok=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create Sample Data\n", "\n", "Let's create some sample data for policy optimization."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_sample_data(num_examples=20):\n", "    \"\"\"\n", "    Create sample data for policy optimization.\n", "    \n", "    Args:\n", "        num_examples: Number of examples to create\n", "        \n", "    Returns:\n", "        List of examples\n", "    \"\"\"\n", "    data = []\n", "    \n", "    topics = [\"nature\", \"technology\", \"space\", \"history\", \"art\"]\n", "    \n", "    for i in range(num_examples):\n", "        topic = topics[i % len(topics)]\n", "        \n", "        # Generate a prompt\n", "        prompt = f\"Write a short paragraph about {topic}.\"\n", "        \n", "        # Generate a response\n", "        response = f\"Here is a paragraph about {topic}: \"\n", "        response += f\"Lorem ipsum dolor sit amet, consectetur adipiscing elit. \"\n", "        response += f\"Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. \"\n", "        response += f\"Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.\"\n", "        \n", "        # Generate a reference\n", "        reference = f\"A well-written paragraph about {topic} \"\n", "        reference += f\"should include key concepts, clear explanations, and engaging examples. \"\n", "        reference += f\"It should be informative, concise, and well-structured.\"\n", "        \n", "        # Generate a reward\n", "        reward = 0.5 + (i % 5) * 0.1\n", "        \n", "        # Add example to data\n", "        data.append({\n", "            \"prompt\": prompt,\n", "            \"response\": response,\n", "            \"reward\": reward,\n", "            \"reference\": reference\n", "        })\n", "    \n", "    return data\n", "\n", "# Create sample data\n", "data = create_sample_data(num_examples=20)\n", "\n", "# Split data into train and eval sets\n", "train_size = int(0.8 * len(data))\n", "train_data = data[:train_size]\n", "eval_data = data[train_size:]\n", "\n", "print(f\"Created {len(train_data)} training examples and {len(eval_data)} evaluation examples.\")\n", "\n", "# Display a few examples\n", "print(\"\\nSample training examples:\")\n", "for i, example in enumerate(train_data[:2]):\n", "    print(f\"\\nExample {i+1}:\")\n", "    print(f\"Prompt: {example['prompt']}\")\n", "    print(f\"Response: {example['response']}\")\n", "    print(f\"Reference: {example['reference']}\")\n", "    print(f\"Reward: {example['reward']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create Reward Functions\n", "\n", "Let's create various reward functions for policy optimization."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create keyword reward function\n", "keyword_reward = KeywordRewardFunction(\n", "    keywords=[\"clear\", \"concise\", \"helpful\", \"informative\", \"detailed\"],\n", "    case_sensitive=False,\n", "    reward_per_keyword=0.2,\n", "    max_reward=1.0\n", ")\n", "\n", "# Create length reward function\n", "length_reward = LengthRewardFunction(\n", "    target_length=50,\n", "    tolerance=0.2,\n", "    penalty_factor=0.5\n", ")\n", "\n", "# Create diversity reward function\n", "diversity_reward = DiversityRewardFunction(\n", "    ngram_size=3,\n", "    penalty_factor=0.5,\n", "    base_reward=1.0\n", ")\n", "\n", "# Create format reward function\n", "format_reward = FormatRewardFunction(\n", "    format_regex=r\"^Here is a paragraph about (.+):\",\n", "    reward_value=1.0,\n", "    default_reward=0.0\n", ")\n", "\n", "# Create combined reward function\n", "combined_reward = CombinedRewardFunction(\n", "    reward_functions=[\n", "        (keyword_reward, 0.4),\n", "        (length_reward, 0.3),\n", "        (diversity_reward, 0.2),\n", "        (format_reward, 0.1)\n", "    ],\n", "    use_cache=True\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test Reward Functions\n", "\n", "Let's test the reward functions on some sample outputs."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sample outputs\n", "outputs = [\n", "    \"Here is a paragraph about nature: Nature is beautiful and diverse. It includes plants, animals, and landscapes. Nature provides resources and inspiration for humans.\",\n", "    \"Technology has advanced rapidly in recent decades. Computers, smartphones, and the internet have transformed society. Artificial intelligence is the next frontier.\",\n", "    \"Here is a paragraph about space: Space is vast and mysterious. It contains stars, planets, galaxies, and other celestial objects. Humans have only begun to explore space.\"\n", "]\n", "\n", "# Sample references\n", "references = [\n", "    \"A well-written paragraph about nature should include key concepts, clear explanations, and engaging examples.\",\n", "    \"A well-written paragraph about technology should include key concepts, clear explanations, and engaging examples.\",\n", "    \"A well-written paragraph about space should include key concepts, clear explanations, and engaging examples.\"\n", "]\n", "\n", "# Test keyword reward function\n", "keyword_rewards = keyword_reward.compute_reward(outputs)\n", "print(\"Keyword rewards:\")\n", "for i, reward in enumerate(keyword_rewards):\n", "    print(f\"Output {i+1}: {reward:.4f}\")\n", "\n", "# Test length reward function\n", "length_rewards = length_reward.compute_reward(outputs)\n", "print(\"\\nLength rewards:\")\n", "for i, reward in enumerate(length_rewards):\n", "    print(f\"Output {i+1}: {reward:.4f}\")\n", "\n", "# Test diversity reward function\n", "diversity_rewards = diversity_reward.compute_reward(outputs)\n", "print(\"\\nDiversity rewards:\")\n", "for i, reward in enumerate(diversity_rewards):\n", "    print(f\"Output {i+1}: {reward:.4f}\")\n", "\n", "# Test format reward function\n", "format_rewards = format_reward.compute_reward(outputs)\n", "print(\"\\nFormat rewards:\")\n", "for i, reward in enumerate(format_rewards):\n", "    print(f\"Output {i+1}: {reward:.4f}\")\n", "\n", "# Test combined reward function\n", "combined_rewards = combined_reward.compute_reward(outputs)\n", "print(\"\\nCombined rewards:\")\n", "for i, reward in enumerate(combined_rewards):\n", "    print(f\"Output {i+1}: {reward:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initialize a GRPO Model\n", "\n", "Now, let's initialize a GRPO model. We'll use the OpenRouter implementation for this example."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Replace with your actual API key\n", "api_key = \"your-openrouter-api-key\"\n", "\n", "# Initialize GRPO model\n", "grpo_model = OpenRouterGRPO(\n", "    model_name=\"anthropic/claude-2\",\n", "    api_key=api_key,\n", "    output_dir=output_dir\n", ")\n", "\n", "# Add reward function to model\n", "grpo_model.add_reward_function(\"combined\", combined_reward)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prepare Datasets\n", "\n", "Let's prepare the datasets for policy optimization."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Prepare datasets\n", "train_dataset, eval_dataset = grpo_model.prepare_data(\n", "    train_data=train_data,\n", "    eval_data=eval_data,\n", "    prompt_key=\"prompt\",\n", "    response_key=\"response\",\n", "    reward_key=\"reward\",\n", "    reference_key=\"reference\"\n", ")\n", "\n", "print(f\"Prepared training dataset with {len(train_dataset)} examples.\")\n", "print(f\"Prepared evaluation dataset with {len(eval_dataset)} examples.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate Initial Responses\n", "\n", "Let's generate initial responses from the model to see how it performs before optimization."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Initial responses:\")\n", "for example in eval_data[:2]:  # Just use the first 2 examples for demonstration\n", "    prompt = example[\"prompt\"]\n", "    reference = example[\"reference\"]\n", "    response = grpo_model.generate_text(prompt)\n", "    \n", "    print(f\"\\nPrompt: {prompt}\")\n", "    print(f\"Reference: {reference}\")\n", "    print(f\"Response: {response}\")\n", "    \n", "    # Compute rewards\n", "    rewards = grpo_model.compute_rewards(\n", "        prompts=[prompt],\n", "        responses=[response],\n", "        references=[reference]\n", "    )\n", "    print(f\"Rewards: {rewards}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Optimize Policy\n", "\n", "Now, let's optimize the policy using GRPO."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Optimize policy\n", "metrics = grpo_model.optimize_policy(\n", "    train_dataset=train_dataset,\n", "    eval_dataset=eval_dataset,\n", "    num_epochs=3,\n", "    batch_size=4,\n", "    learning_rate=5e-5\n", ")\n", "\n", "print(f\"Optimization metrics: {metrics}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualize Training Progress\n", "\n", "Let's visualize the training progress."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize training progress\n", "grpo_model.visualize_training(save_path=os.path.join(output_dir, \"training_visualization.png\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate Responses After Optimization\n", "\n", "Let's generate responses from the optimized model to see how it has improved."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Responses after optimization:\")\n", "for example in eval_data[:2]:  # Just use the first 2 examples for demonstration\n", "    prompt = example[\"prompt\"]\n", "    reference = example[\"reference\"]\n", "    response = grpo_model.generate_text(prompt)\n", "    \n", "    print(f\"\\nPrompt: {prompt}\")\n", "    print(f\"Reference: {reference}\")\n", "    print(f\"Response: {response}\")\n", "    \n", "    # Compute rewards\n", "    rewards = grpo_model.compute_rewards(\n", "        prompts=[prompt],\n", "        responses=[response],\n", "        references=[reference]\n", "    )\n", "    print(f\"Rewards: {rewards}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save the Optimized Model\n", "\n", "Finally, let's save the optimized model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["save_path = grpo_model.save_model()\n", "print(f\"Model saved to {save_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating a Custom Reward Function\n", "\n", "Let's create a custom reward function by subclassing `RewardFunction`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from deep_research_core.rl_tuning.grpo import RewardFunction\n", "\n", "class CustomRewardFunction(RewardFunction):\n", "    def __init__(self, target_words=None, penalty_words=None):\n", "        super().__init__()\n", "        self.target_words = target_words or [\"clear\", \"concise\", \"helpful\"]\n", "        self.penalty_words = penalty_words or [\"vague\", \"confusing\", \"unclear\"]\n", "    \n", "    def compute_reward(self, outputs, references=None, **kwargs):\n", "        rewards = []\n", "        \n", "        for output in outputs:\n", "            # Calculate reward based on presence of target words\n", "            target_count = sum(1 for word in self.target_words if word.lower() in output.lower())\n", "            target_reward = min(1.0, target_count / len(self.target_words))\n", "            \n", "            # Calculate penalty based on presence of penalty words\n", "            penalty_count = sum(1 for word in self.penalty_words if word.lower() in output.lower())\n", "            penalty = min(1.0, penalty_count / len(self.penalty_words))\n", "            \n", "            # Combine reward and penalty\n", "            reward = target_reward * (1.0 - penalty)\n", "            \n", "            rewards.append(reward)\n", "        \n", "        return np.array(rewards)\n", "\n", "# Create custom reward function\n", "custom_reward = CustomRewardFunction(\n", "    target_words=[\"informative\", \"detailed\", \"accurate\", \"helpful\", \"clear\"],\n", "    penalty_words=[\"vague\", \"confusing\", \"unclear\", \"incorrect\", \"misleading\"]\n", ")\n", "\n", "# Test custom reward function\n", "custom_rewards = custom_reward.compute_reward(outputs)\n", "print(\"Custom rewards:\")\n", "for i, reward in enumerate(custom_rewards):\n", "    print(f\"Output {i+1}: {reward:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Exploring BLEU and ROUGE Reward Functions\n", "\n", "Let's explore the BLEU and ROUGE reward functions."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create BLEU reward function\n", "bleu_reward = BLEURewardFunction(\n", "    weights=[0.25, 0.25, 0.25, 0.25],\n", "    smoothing=True\n", ")\n", "\n", "# Create ROUGE reward function\n", "rouge_reward = ROUGERewardFunction(\n", "    rouge_type=\"rouge-l\",\n", "    metric=\"f\"\n", ")\n", "\n", "# Test BLEU reward function\n", "bleu_rewards = bleu_reward.compute_reward(outputs, references)\n", "print(\"BLEU rewards:\")\n", "for i, reward in enumerate(bleu_rewards):\n", "    print(f\"Output {i+1}: {reward:.4f}\")\n", "\n", "# Test ROUGE reward function\n", "rouge_rewards = rouge_reward.compute_reward(outputs, references)\n", "print(\"\\nROUGE rewards:\")\n", "for i, reward in enumerate(rouge_rewards):\n", "    print(f\"Output {i+1}: {reward:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Exploring Outcome Reward Function\n", "\n", "Let's explore the Outcome reward function."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define an evaluation function\n", "def evaluation_fn(output):\n", "    # Check if output contains specific keywords\n", "    required_keywords = [\"clear\", \"informative\"]\n", "    return all(keyword.lower() in output.lower() for keyword in required_keywords)\n", "\n", "# Create outcome reward function\n", "outcome_reward = OutcomeRewardFunction(\n", "    evaluation_fn=evaluation_fn,\n", "    success_reward=1.0,\n", "    failure_reward=0.0\n", ")\n", "\n", "# Test outcome reward function\n", "outcome_rewards = outcome_reward.compute_reward(outputs)\n", "print(\"Outcome rewards:\")\n", "for i, reward in enumerate(outcome_rewards):\n", "    print(f\"Output {i+1}: {reward:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "In this notebook, we've demonstrated how to use the GRPO module for optimizing language model policies using various reward functions. We've covered:\n", "\n", "1. Creating and preparing training data\n", "2. Creating and testing various reward functions\n", "3. Initializing a GRPO model\n", "4. Optimizing the policy\n", "5. Visualizing training progress\n", "6. Creating custom reward functions\n", "7. Exploring BLEU, ROUGE, and Outcome reward functions\n", "\n", "This approach can be used to optimize language model policies based on specific reward criteria."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}