import { useState, useEffect, useCallback } from 'react';
import apiService from '../api/apiService';

interface ApiHookResult<T> {
  data: T | null;
  error: any;
  isLoading: boolean;
  refetch: () => Promise<void>;
  mutateAsync: (...args: any[]) => Promise<any>;
}

/**
 * Generic hook for API calls
 */
export function useApi<T, Args extends any[]>(
  apiFunction: (...args: Args) => Promise<T>,
  initialArgs?: Args
): ApiHookResult<T> {
  const [data, setData] = useState<T | null>(null);
  const [error, setError] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const fetchData = useCallback(async (...args: Args) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await apiFunction(...args);
      setData(result);
      return result;
    } catch (err) {
      setError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [apiFunction]);

  const refetch = useCallback(async () => {
    if (initialArgs) {
      await fetchData(...initialArgs);
    }
  }, [fetchData, initialArgs]);

  const mutateAsync = useCallback(async (...args: any[]) => {
    return await fetchData(...(args as Args));
  }, [fetchData]);

  useEffect(() => {
    if (initialArgs) {
      fetchData(...initialArgs).catch(err => {
        console.error('Error in initial API call:', err);
      });
    }
  }, [fetchData, initialArgs]);

  return { data, error, isLoading, refetch, mutateAsync };
}

// Custom hooks for specific API calls

/**
 * Custom hook for health status
 */
export function useHealthStatus() {
  return useApi(apiService.getHealthStatus.bind(apiService));
}

/**
 * Custom hook for providers
 */
export function useProviders() {
  return useApi(apiService.getProviders.bind(apiService));
}

/**
 * Custom hook for query processing
 */
export function useQueryProcessing() {
  return useApi(apiService.processQuery.bind(apiService));
}

/**
 * Custom hook for visualizations
 */
export function useVisualizations() {
  return useApi(apiService.generateVisualization.bind(apiService));
}

/**
 * Custom hook for tree data
 */
export function useTreeData() {
  return useApi(apiService.getTreeData.bind(apiService));
}

/**
 * Custom hook for document uploads
 */
export function useDocumentUpload() {
  return useApi(apiService.uploadDocuments.bind(apiService));
}

/**
 * Custom hook for getting documents
 */
export function useDocuments() {
  return useApi(apiService.getDocuments.bind(apiService));
}

/**
 * Custom hook for getting collections
 */
export function useCollections() {
  return useApi(apiService.getCollections.bind(apiService));
}

export default useApi; 