import axios, { AxiosInstance, AxiosResponse } from 'axios';

// Type definitions for API responses
export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: string;
}

export interface ProviderInfo {
  providers: string[];
}

export interface HealthCheckResponse {
  status: string;
}

export interface VisualizationRequest {
  result: {
    query: string;
    best_paths: Array<any>;
    retrieved_documents?: Array<any>;
    conflicts?: Record<string, any>;
    metadata?: Record<string, any>;
  };
  filename_prefix?: string;
}

export interface VisualizationResponse {
  html_url: string;
  image_urls: string[];
  message: string;
}

export interface TreeData {
  name: string;
  children?: TreeData[];
  [key: string]: any;
}

// Create a configured Axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor for authentication
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// API Service class
class ApiService {
  // General methods
  
  /**
   * Get API health status
   */
  async getHealthStatus(): Promise<HealthCheckResponse> {
    const response: AxiosResponse<HealthCheckResponse> = await apiClient.get('/api/health');
    return response.data;
  }

  /**
   * Get available AI providers
   */
  async getProviders(): Promise<ProviderInfo> {
    const response: AxiosResponse<ProviderInfo> = await apiClient.get('/api/providers');
    return response.data;
  }

  // Reasoning methods
  
  /**
   * Process a query using the specified model and reasoning method
   */
  async processQuery(query: string, model: string, reasoningMethod: string, options?: Record<string, any>): Promise<any> {
    const response: AxiosResponse<any> = await apiClient.post('/api/process', {
      query,
      model,
      reasoning_method: reasoningMethod,
      options,
    });
    return response.data;
  }

  // Visualization methods
  
  /**
   * Generate visualizations for a reasoning result
   */
  async generateVisualization(request: VisualizationRequest): Promise<VisualizationResponse> {
    const response: AxiosResponse<VisualizationResponse> = await apiClient.post('/visualization/generate', request);
    return response.data;
  }

  /**
   * Get tree data for visualization
   */
  async getTreeData(resultId: string): Promise<TreeData> {
    const response: AxiosResponse<TreeData> = await apiClient.get(`/visualization/tree-data?result_id=${resultId}`);
    return response.data;
  }

  // Document management methods

  /**
   * Upload documents for RAG
   */
  async uploadDocuments(files: File[], collection?: string): Promise<any> {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append('files', file);
    });
    
    if (collection) {
      formData.append('collection', collection);
    }

    const response: AxiosResponse<any> = await apiClient.post('/api/documents/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  /**
   * Get document collections
   */
  async getCollections(): Promise<any> {
    const response: AxiosResponse<any> = await apiClient.get('/api/collections');
    return response.data;
  }

  /**
   * Get documents (filtered by collection if specified)
   */
  async getDocuments(collection?: string): Promise<any> {
    let url = '/api/documents';
    if (collection) {
      url += `?collection=${collection}`;
    }
    const response: AxiosResponse<any> = await apiClient.get(url);
    return response.data;
  }

  /**
   * Delete a document by ID
   */
  async deleteDocument(id: string): Promise<any> {
    const response: AxiosResponse<any> = await apiClient.delete(`/api/documents/${id}`);
    return response.data;
  }

  /**
   * Reprocess a document by ID
   */
  async reprocessDocument(id: string): Promise<any> {
    const response: AxiosResponse<any> = await apiClient.post(`/api/documents/${id}/reprocess`);
    return response.data;
  }

  /**
   * Update document tags
   */
  async updateDocumentTags(id: string, tags: string[]): Promise<any> {
    const response: AxiosResponse<any> = await apiClient.patch(`/api/documents/${id}/tags`, { tags });
    return response.data;
  }

  /**
   * Move document to collection
   */
  async moveDocumentToCollection(id: string, collectionId: string): Promise<any> {
    const response: AxiosResponse<any> = await apiClient.patch(`/api/documents/${id}/collection`, { collection_id: collectionId });
    return response.data;
  }

  // Error handling helper
  handleError(error: any): void {
    // Log error
    console.error('API Error:', error);
    
    // Throw meaningful error messages
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      throw new Error(`Server error: ${error.response.status} - ${error.response.data.message || 'Unknown error'}`);
    } else if (error.request) {
      // The request was made but no response was received
      throw new Error('No response received from server. Please check your connection.');
    } else {
      // Something happened in setting up the request that triggered an Error
      throw new Error(`Request error: ${error.message}`);
    }
  }
}

// Export a singleton instance
export const apiService = new ApiService();
export default apiService; 