import React, { useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Tab,
  Tabs
} from '@mui/material';
import { ContentCard } from '../components/common';
import TreeVisualizer, { TreeNode } from '../components/visualization/TreeVisualizer';
import GraphVisualizer, { GraphData } from '../components/visualization/GraphVisualizer';

// Sample tree data for demonstration
const sampleTreeData: TreeNode = {
  id: "root",
  text: "How can we solve climate change?",
  type: "thought",
  children: [
    {
      id: "branch1",
      text: "Explore renewable energy solutions",
      type: "thought",
      score: 0.85,
      children: [
        {
          id: "leaf1-1",
          text: "Solar power implementation",
          type: "thought",
          score: 0.9,
          children: [
            {
              id: "leaf1-1-1",
              text: "Residential solar panels",
              type: "action",
              score: 0.75
            },
            {
              id: "leaf1-1-2",
              text: "Industrial solar farms",
              type: "action",
              score: 0.88
            }
          ]
        },
        {
          id: "leaf1-2",
          text: "Wind energy expansion",
          type: "thought",
          score: 0.8,
          children: [
            {
              id: "leaf1-2-1",
              text: "Offshore wind farms",
              type: "action",
              score: 0.82
            }
          ]
        }
      ]
    },
    {
      id: "branch2",
      text: "Reduce carbon emissions",
      type: "thought",
      score: 0.9,
      children: [
        {
          id: "leaf2-1",
          text: "Transportation innovations",
          type: "thought",
          score: 0.85,
          children: [
            {
              id: "leaf2-1-1",
              text: "Electric vehicle adoption",
              type: "action",
              score: 0.92
            },
            {
              id: "leaf2-1-2",
              text: "Public transportation enhancements",
              type: "action",
              score: 0.78
            }
          ]
        },
        {
          id: "leaf2-2",
          text: "Industrial carbon capture",
          type: "thought",
          score: 0.7
        }
      ]
    },
    {
      id: "branch3",
      text: "Policy and governance approaches",
      type: "thought",
      score: 0.75,
      children: [
        {
          id: "leaf3-1",
          text: "International climate agreements",
          type: "action",
          score: 0.68
        },
        {
          id: "leaf3-2",
          text: "Carbon tax implementation",
          type: "action",
          score: 0.72
        }
      ]
    }
  ]
};

// Sample graph data for demonstration
const sampleGraphData: GraphData = {
  nodes: [
    { id: "node1", text: "How can we solve climate change?", type: "thought" },
    { id: "node2", text: "Explore renewable energy solutions", type: "thought", score: 0.85 },
    { id: "node3", text: "Reduce carbon emissions", type: "thought", score: 0.9 },
    { id: "node4", text: "Policy and governance approaches", type: "thought", score: 0.75 },
    { id: "node5", text: "Solar power implementation", type: "thought", score: 0.9 },
    { id: "node6", text: "Wind energy expansion", type: "thought", score: 0.8 },
    { id: "node7", text: "Transportation innovations", type: "thought", score: 0.85 },
    { id: "node8", text: "Industrial carbon capture", type: "thought", score: 0.7 },
    { id: "node9", text: "Residential solar panels", type: "action", score: 0.75 },
    { id: "node10", text: "Industrial solar farms", type: "action", score: 0.88 },
    { id: "node11", text: "Offshore wind farms", type: "action", score: 0.82 },
    { id: "node12", text: "Electric vehicle adoption", type: "action", score: 0.92 },
    { id: "node13", text: "Public transportation enhancements", type: "action", score: 0.78 },
    { id: "node14", text: "International climate agreements", type: "action", score: 0.68 },
    { id: "node15", text: "Carbon tax implementation", type: "action", score: 0.72 },
    { id: "node16", text: "Combined approach assessment", type: "decision", score: 0.95 }
  ],
  edges: [
    { source: "node1", target: "node2", type: "reasoning" },
    { source: "node1", target: "node3", type: "reasoning" },
    { source: "node1", target: "node4", type: "reasoning" },
    { source: "node2", target: "node5", type: "reasoning" },
    { source: "node2", target: "node6", type: "reasoning" },
    { source: "node3", target: "node7", type: "reasoning" },
    { source: "node3", target: "node8", type: "reasoning" },
    { source: "node5", target: "node9", type: "reasoning" },
    { source: "node5", target: "node10", type: "reasoning" },
    { source: "node6", target: "node11", type: "reasoning" },
    { source: "node7", target: "node12", type: "reasoning" },
    { source: "node7", target: "node13", type: "reasoning" },
    { source: "node4", target: "node14", type: "reasoning" },
    { source: "node4", target: "node15", type: "reasoning" },
    // Cross-connections for graph structure
    { source: "node2", target: "node7", type: "reasoning" },
    { source: "node3", target: "node5", type: "reasoning" },
    { source: "node5", target: "node15", type: "reasoning" },
    // Final decision node connections
    { source: "node9", target: "node16", type: "decision" },
    { source: "node10", target: "node16", type: "decision" },
    { source: "node12", target: "node16", type: "decision" },
    { source: "node15", target: "node16", type: "decision" }
  ]
};

// Interface for tab panels
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index, ...other }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`visualization-tabpanel-${index}`}
      aria-labelledby={`visualization-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

const VisualizationsPage: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [currentTree, setCurrentTree] = useState<TreeNode>(sampleTreeData);
  const [currentGraph, setCurrentGraph] = useState<GraphData>(sampleGraphData);
  const [selectedNode, setSelectedNode] = useState<TreeNode | null>(null);
  const [selectedGraphNode, setSelectedGraphNode] = useState<GraphData['nodes'][0] | null>(null);
  const [selectedLayout, setSelectedLayout] = useState<string>('force');
  
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  const handleNodeClick = (node: TreeNode) => {
    setSelectedNode(node);
  };

  const handleGraphNodeClick = (node: GraphData['nodes'][0]) => {
    setSelectedGraphNode(node);
  };
  
  const handleGenerateNewTree = () => {
    setIsLoading(true);
    
    // Simulate API call to generate a new tree
    setTimeout(() => {
      // Just use the sample data for demo purposes
      setCurrentTree({...sampleTreeData});
      setIsLoading(false);
    }, 1500);
  };

  const handleGenerateNewGraph = () => {
    setIsLoading(true);
    
    // Simulate API call to generate a new graph
    setTimeout(() => {
      // Just use the sample data for demo purposes
      setCurrentGraph({...sampleGraphData});
      setIsLoading(false);
    }, 1500);
  };
  
  const handleLayoutChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setSelectedLayout(event.target.value as string);
  };
  
  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Visualizations
      </Typography>
      
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs 
          value={tabValue} 
          onChange={handleTabChange}
          aria-label="visualization tabs"
        >
          <Tab label="Tree of Thought" />
          <Tab label="Graph of Thoughts" />
          <Tab label="RAG Visualization" />
          <Tab label="Other Visualizations" />
        </Tabs>
      </Box>
      
      <TabPanel value={tabValue} index={0}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={9}>
            {/* Main Visualization Area */}
            <TreeVisualizer 
              data={currentTree} 
              loading={isLoading}
              onNodeClick={handleNodeClick}
              height={600}
            />
          </Grid>
          
          <Grid item xs={12} md={3}>
            {/* Controls and Details */}
            <ContentCard
              title="Visualization Controls"
              elevation={2}
              sx={{ mb: 3 }}
            >
              <Button 
                variant="contained" 
                color="primary" 
                fullWidth 
                onClick={handleGenerateNewTree}
                disabled={isLoading}
              >
                Generate New Tree
              </Button>
              
              <Box sx={{ mt: 2 }}>
                <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                  <InputLabel id="reasoning-type-label">Reasoning Type</InputLabel>
                  <Select
                    labelId="reasoning-type-label"
                    value="tot"
                    label="Reasoning Type"
                  >
                    <MenuItem value="tot">Tree of Thought</MenuItem>
                    <MenuItem value="cot">Chain of Thought</MenuItem>
                    <MenuItem value="react">ReAct</MenuItem>
                  </Select>
                </FormControl>
                
                <TextField
                  fullWidth
                  label="Query"
                  defaultValue="How can we solve climate change?"
                  multiline
                  rows={4}
                  size="small"
                  sx={{ mb: 2 }}
                />
                
                <FormControl fullWidth size="small">
                  <InputLabel id="model-label">Model</InputLabel>
                  <Select
                    labelId="model-label"
                    value="gpt4"
                    label="Model"
                  >
                    <MenuItem value="gpt4">GPT-4</MenuItem>
                    <MenuItem value="gpt35">GPT-3.5</MenuItem>
                    <MenuItem value="claude">Claude</MenuItem>
                    <MenuItem value="local">Local Model</MenuItem>
                  </Select>
                </FormControl>
              </Box>
            </ContentCard>
            
            {selectedNode && (
              <ContentCard
                title="Node Details"
                elevation={2}
              >
                <Typography variant="subtitle1" gutterBottom>
                  {selectedNode.text}
                </Typography>
                {selectedNode.score !== undefined && (
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Score: {selectedNode.score.toFixed(2)}
                  </Typography>
                )}
                {selectedNode.type && (
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Type: {selectedNode.type}
                  </Typography>
                )}
                {selectedNode.children && (
                  <Typography variant="body2" color="text.secondary">
                    Children: {selectedNode.children.length}
                  </Typography>
                )}
              </ContentCard>
            )}
          </Grid>
        </Grid>
      </TabPanel>
      
      <TabPanel value={tabValue} index={1}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={9}>
            {/* Graph Visualization Area */}
            <GraphVisualizer 
              data={currentGraph} 
              loading={isLoading}
              onNodeClick={handleGraphNodeClick}
              height={600}
              layoutType={selectedLayout as 'force' | 'hierarchical' | 'radial'}
            />
          </Grid>
          
          <Grid item xs={12} md={3}>
            {/* Controls and Details */}
            <ContentCard
              title="Graph Visualization Controls"
              elevation={2}
              sx={{ mb: 3 }}
            >
              <Button 
                variant="contained" 
                color="primary" 
                fullWidth 
                onClick={handleGenerateNewGraph}
                disabled={isLoading}
              >
                Generate New Graph
              </Button>
              
              <Box sx={{ mt: 2 }}>
                <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                  <InputLabel id="layout-type-label">Layout Type</InputLabel>
                  <Select
                    labelId="layout-type-label"
                    value={selectedLayout}
                    label="Layout Type"
                    onChange={handleLayoutChange as any}
                  >
                    <MenuItem value="force">Force Directed</MenuItem>
                    <MenuItem value="hierarchical">Hierarchical</MenuItem>
                    <MenuItem value="radial">Radial</MenuItem>
                  </Select>
                </FormControl>
                
                <TextField
                  fullWidth
                  label="Query"
                  defaultValue="How can we solve climate change?"
                  multiline
                  rows={4}
                  size="small"
                  sx={{ mb: 2 }}
                />
                
                <FormControl fullWidth size="small">
                  <InputLabel id="got-model-label">Model</InputLabel>
                  <Select
                    labelId="got-model-label"
                    value="gpt4"
                    label="Model"
                  >
                    <MenuItem value="gpt4">GPT-4</MenuItem>
                    <MenuItem value="gpt35">GPT-3.5</MenuItem>
                    <MenuItem value="claude">Claude</MenuItem>
                    <MenuItem value="local">Local Model</MenuItem>
                  </Select>
                </FormControl>
              </Box>
            </ContentCard>
            
            {selectedGraphNode && (
              <ContentCard
                title="Node Details"
                elevation={2}
              >
                <Typography variant="subtitle1" gutterBottom>
                  {selectedGraphNode.text}
                </Typography>
                {selectedGraphNode.score !== undefined && (
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Score: {selectedGraphNode.score.toFixed(2)}
                  </Typography>
                )}
                {selectedGraphNode.type && (
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Type: {selectedGraphNode.type}
                  </Typography>
                )}
                <Typography variant="body2" color="text.secondary">
                  ID: {selectedGraphNode.id}
                </Typography>
              </ContentCard>
            )}
          </Grid>
        </Grid>
      </TabPanel>
      
      <TabPanel value={tabValue} index={2}>
        <ContentCard title="RAG Visualization" subtitle="Coming soon">
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body1">
              This feature is under development. RAG visualization will allow you to see how documents
              are retrieved and integrated with the reasoning process.
            </Typography>
          </Box>
        </ContentCard>
      </TabPanel>
      
      <TabPanel value={tabValue} index={3}>
        <ContentCard title="Additional Visualizations" subtitle="Coming soon">
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body1">
              More visualization types are under development, including:
            </Typography>
            <Typography component="ul" sx={{ textAlign: 'left', mt: 2 }}>
              <li>ReAct step-by-step visualization</li>
              <li>CoT reasoning flow</li>
              <li>Multi-query decomposition visualization</li>
              <li>Source attribution mapping</li>
              <li>Comparative reasoning analysis</li>
            </Typography>
          </Box>
        </ContentCard>
      </TabPanel>
    </Box>
  );
};

export default VisualizationsPage; 