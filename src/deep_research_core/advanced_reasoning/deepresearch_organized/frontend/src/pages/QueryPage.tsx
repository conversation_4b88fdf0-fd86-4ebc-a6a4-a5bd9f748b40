import React, { useState, useEffect } from 'react';
import {
  <PERSON>po<PERSON>,
  Box,
  Grid,
  Button,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Paper,
  Divider,
  TextField,
  CircularProgress,
  FormControlLabel,
  Switch,
  Alert
} from '@mui/material';
import { Send as SendIcon } from '@mui/icons-material';
import { ContentCard, CodeBlock, LoadingIndicator } from '../components/common';
import { useProviders, useProcessQuery } from '../hooks/useApi';

const reasoningMethods = [
  { value: 'react', label: 'ReAct (Reasoning + Action)' },
  { value: 'tot', label: 'Tree of Thought' },
  { value: 'cot', label: 'Chain of Thought' },
  { value: 'got', label: 'Graph of Thought' }
];

const QueryPage: React.FC = () => {
  // States
  const [query, setQuery] = useState('');
  const [model, setModel] = useState('');
  const [reasoningMethod, setReasoningMethod] = useState('react');
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [options, setOptions] = useState({
    temperature: 0.7,
    max_tokens: 1024,
    visualize: true
  });

  // API hooks
  const { 
    data: providersData, 
    loading: providersLoading, 
    error: providersError, 
    execute: fetchProviders 
  } = useProviders();
  
  const { 
    data: queryResult, 
    loading: queryLoading, 
    error: queryError, 
    execute: processQuery 
  } = useProcessQuery();

  // Fetch providers on mount
  useEffect(() => {
    fetchProviders();
  }, [fetchProviders]);

  // Set default model when providers load
  useEffect(() => {
    if (providersData?.providers?.length && !model) {
      setModel(providersData.providers[0]);
    }
  }, [providersData, model]);

  // Handle query submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!query.trim() || !model || !reasoningMethod) return;
    
    await processQuery(query, model, reasoningMethod, options);
  };

  // Handle option changes
  const handleOptionChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked, type } = e.target;
    
    setOptions(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Query Interface
      </Typography>

      <Grid container spacing={3}>
        {/* Query Form */}
        <Grid item xs={12} md={6}>
          <ContentCard
            title="New Query"
            subtitle="Enter your query to process with the selected model"
            elevation={2}
          >
            <Box component="form" onSubmit={handleSubmit} sx={{ p: 2 }}>
              {providersError && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  Error loading providers: {providersError.message}
                </Alert>
              )}
              
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    label="Query"
                    placeholder="Enter your question or request here..."
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    required
                    fullWidth
                    multiline
                    rows={4}
                    variant="outlined"
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth required>
                    <InputLabel id="model-label">Model</InputLabel>
                    <Select
                      labelId="model-label"
                      id="model-select"
                      value={model}
                      onChange={(e) => setModel(e.target.value)}
                      label="Model"
                      disabled={providersLoading || !providersData?.providers?.length}
                    >
                      {providersData?.providers?.map((provider) => (
                        <MenuItem key={provider} value={provider}>
                          {provider}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth required>
                    <InputLabel id="reasoning-method-label">Reasoning Method</InputLabel>
                    <Select
                      labelId="reasoning-method-label"
                      id="reasoning-method-select"
                      value={reasoningMethod}
                      onChange={(e) => setReasoningMethod(e.target.value)}
                      label="Reasoning Method"
                    >
                      {reasoningMethods.map((method) => (
                        <MenuItem key={method.value} value={method.value}>
                          {method.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch 
                        checked={showAdvanced} 
                        onChange={(e) => setShowAdvanced(e.target.checked)}
                      />
                    }
                    label="Show advanced options"
                  />
                </Grid>
                
                {showAdvanced && (
                  <>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        label="Temperature"
                        type="number"
                        name="temperature"
                        value={options.temperature}
                        onChange={handleOptionChange}
                        fullWidth
                        inputProps={{ min: 0, max: 1, step: 0.1 }}
                      />
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <TextField
                        label="Max Tokens"
                        type="number"
                        name="max_tokens"
                        value={options.max_tokens}
                        onChange={handleOptionChange}
                        fullWidth
                        inputProps={{ min: 100, max: 4000, step: 100 }}
                      />
                    </Grid>
                    
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch 
                            name="visualize"
                            checked={options.visualize} 
                            onChange={handleOptionChange}
                          />
                        }
                        label="Generate visualization"
                      />
                    </Grid>
                  </>
                )}
                
                <Grid item xs={12}>
                  <Divider sx={{ my: 1 }} />
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                    <Button
                      type="submit"
                      variant="contained"
                      color="primary"
                      disabled={queryLoading || !query.trim() || !model || providersLoading}
                      startIcon={queryLoading ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
                    >
                      {queryLoading ? 'Processing...' : 'Submit Query'}
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          </ContentCard>
        </Grid>
        
        {/* Results Section */}
        <Grid item xs={12} md={6}>
          <ContentCard
            title="Results"
            subtitle="Query processing results"
            elevation={2}
            loading={queryLoading}
          >
            {queryError ? (
              <Box sx={{ p: 2 }}>
                <Alert severity="error">
                  Error processing query: {queryError.message}
                </Alert>
              </Box>
            ) : queryResult ? (
              <Box sx={{ p: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Response:
                </Typography>
                
                {typeof queryResult === 'object' ? (
                  <CodeBlock
                    code={JSON.stringify(queryResult, null, 2)}
                    language="json"
                    title="JSON Response"
                  />
                ) : (
                  <Paper elevation={0} sx={{ p: 2, backgroundColor: 'background.default' }}>
                    <Typography whiteSpace="pre-wrap">
                      {queryResult}
                    </Typography>
                  </Paper>
                )}
                
                {options.visualize && queryResult && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      Visualization:
                    </Typography>
                    <Paper 
                      elevation={0} 
                      sx={{ 
                        p: 2, 
                        backgroundColor: 'background.default',
                        height: 300,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}
                    >
                      <Typography color="text.secondary">
                        Visualization would appear here
                      </Typography>
                    </Paper>
                  </Box>
                )}
              </Box>
            ) : (
              <Box sx={{ p: 3, textAlign: 'center' }}>
                <Typography color="text.secondary">
                  Submit a query to see results here
                </Typography>
              </Box>
            )}
          </ContentCard>
        </Grid>
      </Grid>
    </Box>
  );
};

export default QueryPage; 