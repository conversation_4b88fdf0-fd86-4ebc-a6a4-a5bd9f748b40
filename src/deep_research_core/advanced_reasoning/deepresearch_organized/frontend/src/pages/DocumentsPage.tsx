import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Checkbox,
  Chip,
  Container,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputAdornment,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Typography,
  useTheme,
  SelectChangeEvent,
  Popover,
  Autocomplete,
} from '@mui/material';
import {
  Add as AddIcon,
  CloudUpload as CloudUploadIcon,
  Delete as DeleteIcon,
  Download as DownloadIcon,
  Label as LabelIcon,
  Refresh as RefreshIcon,
  Search as SearchIcon,
  Create as CreateIcon,
  Folder as FolderIcon,
  FolderOpen as FolderOpenIcon,
  Visibility as VisibilityIcon,
  FilterList as FilterListIcon,
} from '@mui/icons-material';
import apiService from '../api/apiService';
import { useDocuments, useDocumentUpload, useCollections } from '../hooks/useApi';
import { Document, Collection, DocumentsResponse } from '../types';
import DocumentUploader from '../components/documents/DocumentUploader';
import CollectionManager from '../components/documents/CollectionManager';
import DocumentDetails from '../components/documents/DocumentDetails';
import { useTranslation } from 'react-i18next';
import { useToast } from '../context/ToastContext';

// Local interface extending the Document type with frontend-specific properties
interface DocumentWithUIState extends Document {
  uploadDate: string;
  lastUsed?: string;
  lastQuery?: string;
  vectorStore?: string;
}

// Document type definition
// interface Document {
//   id: string;
//   name: string;
//   type: string;
//   size: number;
//   uploadDate: string;
//   tags: string[];
//   collection: string;
//   status: 'indexed' | 'processing' | 'error';
//   chunks?: number;
//   lastUsed?: string;
//   lastQuery?: string;
//   vectorStore?: string;
// }

// Collection type definition
// interface Collection {
//   id: string;
//   name: string;
//   documentCount: number;
// }

const DocumentsPage: React.FC = () => {
  const theme = useTheme();
  const { t } = useTranslation();
  const { showToast } = useToast();
  
  // State for documents, collections, and UI
  const [documents, setDocuments] = useState<DocumentWithUIState[]>([]);
  const [collections, setCollections] = useState<Collection[]>([]);
  const [selectedCollection, setSelectedCollection] = useState<string>('all');
  const [selectedDocument, setSelectedDocument] = useState<DocumentWithUIState | null>(null);
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [sortBy, setSortBy] = useState<string>('date');
  const [documentDetailsOpen, setDocumentDetailsOpen] = useState(false);
  
  // Pagination state
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Delete confirmation dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteDialogType, setDeleteDialogType] = useState<'single' | 'multiple'>('single');
  const [documentToDelete, setDocumentToDelete] = useState<string>('');
  
  // Fetch documents using the custom hook
  const { data: documentsData, isLoading: isLoadingDocuments, refetch: refetchDocuments } = useDocuments();
  
  // Fetch collections
  const { data: collectionsData, isLoading: isLoadingCollections, refetch: refetchCollections } = useCollections();
  
  // Document upload hook
  const { mutateAsync: uploadDocumentsMutate, isLoading: isUploading } = useDocumentUpload();
  
  // File input ref
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  
  // Advanced filter state
  const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null);
  const [filterOptions, setFilterOptions] = useState({
    types: [] as string[],
    tags: [] as string[],
    status: '' as string,
    dateRange: {
      start: '',
      end: '',
    },
  });
  const [uniqueTypes, setUniqueTypes] = useState<string[]>([]);
  const [uniqueTags, setUniqueTags] = useState<string[]>([]);
  const [uniqueStatuses, setUniqueStatuses] = useState<string[]>(['indexed', 'processing', 'error']);
  
  // Update collections when data is fetched
  useEffect(() => {
    if (collectionsData && collectionsData.collections) {
      setCollections(collectionsData.collections);
    } else if (!collectionsData && !isLoadingCollections) {
      // Use mock data if API call fails or returns empty
      const mockCollections: Collection[] = [
        { id: '1', name: 'Research Papers', documentCount: 45 },
        { id: '2', name: 'Financial Reports', documentCount: 23 },
        { id: '3', name: 'Technical Documentation', documentCount: 31 },
        { id: '4', name: 'Vietnamese Literature', documentCount: 17 },
        { id: '5', name: 'Legal Documents', documentCount: 9 },
      ];
      setCollections(mockCollections);
    }
  }, [collectionsData, isLoadingCollections]);
  
  // Update documents when data is fetched
  useEffect(() => {
    if (documentsData) {
      // Transform API data to match our Document interface
      const typedData = documentsData as DocumentsResponse;
      const formattedDocuments: DocumentWithUIState[] = typedData.documents?.map((doc: Document) => ({
        ...doc,
        uploadDate: doc.upload_date,
        tags: doc.tags || [],
        collection: doc.collection || 'uncategorized',
        status: doc.status || 'indexed',
        lastUsed: doc.last_used,
        lastQuery: doc.last_query,
        vectorStore: doc.vector_store,
      })) || [];
      
      setDocuments(formattedDocuments);
    }
  }, [documentsData]);
  
  // Extract unique values for filter options
  useEffect(() => {
    if (documents && documents.length > 0) {
      // Extract unique document types
      const types = [...new Set(documents.map(doc => doc.type.toLowerCase()))];
      setUniqueTypes(types);
      
      // Extract unique tags
      const allTags = documents.reduce((tags, doc) => {
        if (doc.tags && doc.tags.length) {
          return [...tags, ...doc.tags];
        }
        return tags;
      }, [] as string[]);
      const uniqueTags = [...new Set(allTags)];
      setUniqueTags(uniqueTags);
    }
  }, [documents]);
  
  // Filter documents based on selected collection, search query, and advanced filters
  const filteredDocuments = documents.filter((doc) => {
    // Filter by collection
    const collectionMatch = selectedCollection === 'all' || doc.collection === selectedCollection;
    
    // Filter by search query
    const searchMatch = !searchQuery || 
      doc.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (doc.tags && doc.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase())));
    
    // Filter by advanced filters
    let advancedFilterMatch = true;
    
    // Filter by document types
    if (filterOptions.types.length > 0) {
      advancedFilterMatch = advancedFilterMatch && filterOptions.types.includes(doc.type.toLowerCase());
    }
    
    // Filter by tags
    if (filterOptions.tags.length > 0) {
      advancedFilterMatch = advancedFilterMatch && filterOptions.tags.some(tag => doc.tags?.includes(tag));
    }
    
    // Filter by status
    if (filterOptions.status) {
      advancedFilterMatch = advancedFilterMatch && doc.status === filterOptions.status;
    }
    
    // Filter by date range
    if (filterOptions.dateRange.start) {
      const startDate = new Date(filterOptions.dateRange.start);
      const docDate = new Date(doc.uploadDate);
      advancedFilterMatch = advancedFilterMatch && docDate >= startDate;
    }
    
    if (filterOptions.dateRange.end) {
      const endDate = new Date(filterOptions.dateRange.end);
      const docDate = new Date(doc.uploadDate);
      advancedFilterMatch = advancedFilterMatch && docDate <= endDate;
    }
    
    return collectionMatch && searchMatch && advancedFilterMatch;
  });
  
  // Sort documents
  const sortedDocuments = [...filteredDocuments].sort((a, b) => {
    switch (sortBy) {
      case 'date':
        return new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime();
      case 'name':
        return a.name.localeCompare(b.name);
      case 'size':
        return b.size - a.size;
      case 'type':
        return a.type.localeCompare(b.type);
      default:
        return 0;
    }
  });
  
  // Handle collection change
  const handleCollectionChange = (event: SelectChangeEvent) => {
    setSelectedCollection(event.target.value);
    setSelectedDocuments([]);
    setSelectedDocument(null);
  };
  
  // Handle sort change
  const handleSortChange = (event: SelectChangeEvent) => {
    setSortBy(event.target.value);
  };
  
  // Handle search query change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };
  
  // Handle document row click
  const handleDocumentClick = (doc: DocumentWithUIState) => {
    setSelectedDocument(doc);
  };

  // Handle document details view
  const handleOpenDocumentDetails = (doc: DocumentWithUIState) => {
    setSelectedDocument(doc);
    setDocumentDetailsOpen(true);
  };
  
  // Handle document details close
  const handleCloseDocumentDetails = () => {
    setDocumentDetailsOpen(false);
  };
  
  // Handle document checkbox change
  const handleSelectDocument = (event: React.ChangeEvent<HTMLInputElement>, docId: string) => {
    if (event.target.checked) {
      setSelectedDocuments([...selectedDocuments, docId]);
    } else {
      setSelectedDocuments(selectedDocuments.filter(id => id !== docId));
    }
  };
  
  // Handle select all checkbox
  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      setSelectedDocuments(sortedDocuments.map(doc => doc.id));
    } else {
      setSelectedDocuments([]);
    }
  };
  
  // Handle upload button click
  const handleUploadClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };
  
  // Handle file input change
  const handleFileInputChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      const files = Array.from(event.target.files);
      
      try {
        await uploadDocumentsMutate(files);
        // Reset the file input
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        // Refresh the documents list
        refetchDocuments();
      } catch (error) {
        console.error('Error uploading documents:', error);
        // TODO: Add toast notification for error
        showToast(t('documents.uploadError'), 'error');
      }
    }
  };
  
  // Pagination handlers
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Delete dialog handlers
  const openDeleteDialog = (type: 'single' | 'multiple', documentId?: string) => {
    setDeleteDialogType(type);
    if (documentId) {
      setDocumentToDelete(documentId);
    }
    setDeleteDialogOpen(true);
  };

  const closeDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDocumentToDelete('');
  };

  const confirmDelete = async () => {
    try {
      if (deleteDialogType === 'single') {
        await handleDeleteDocument(documentToDelete);
      } else {
        await handleDeleteDocuments();
      }
      showToast(t('documents.deleteSuccess'), 'success');
      closeDeleteDialog();
    } catch (error) {
      console.error('Error during delete:', error);
      showToast(t('documents.deleteError'), 'error');
    }
  };

  const handleDeleteDocuments = async () => {
    try {
      // Only use this function for the actual API call now, not for confirmation
      for (const id of selectedDocuments) {
        await apiService.deleteDocument(id);
      }
      
      // Refresh documents list
      refetchDocuments();
      
      // Reset selected documents
      setSelectedDocuments([]);
      
      // Close dialog
      setDeleteDialogOpen(false);
      
      showToast(t('documents.deleteSuccess'), 'success');
    } catch (error) {
      console.error('Error deleting documents:', error);
      showToast(t('documents.deleteError'), 'error');
    }
  };
  
  const handleDeleteDocument = async (id: string) => {
    try {
      // Only use this function for the actual API call now, not for confirmation
      await apiService.deleteDocument(id);
      
      // Refresh documents list
      refetchDocuments();
      
      // Close document details if open
      if (documentDetailsOpen) {
        setDocumentDetailsOpen(false);
      }
      
      // Update selected documents if needed
      setSelectedDocuments(prev => prev.filter(docId => docId !== id));
      showToast(t('documents.deleteSuccess'), 'success');
    } catch (error) {
      console.error('Error deleting document:', error);
      showToast(t('documents.deleteError'), 'error');
    }
  };
  
  // Handle reprocess document
  const handleReprocessDocument = async (id: string) => {
    try {
      await apiService.reprocessDocument(id);
      refetchDocuments();
      showToast(t('documents.reprocessSuccess'), 'success');
    } catch (error) {
      console.error('Error reprocessing document:', error);
      showToast(t('documents.reprocessError'), 'error');
    }
  };
  
  // Handle update tags
  const handleUpdateTags = async (id: string, tags: string[]) => {
    // Update the document in the local state with new tags
    const updatedDocuments = documents.map(doc => 
      doc.id === id ? { ...doc, tags } : doc
    );
    setDocuments(updatedDocuments);
    
    try {
      // Call API to update tags
      await apiService.updateDocumentTags(id, tags);
      showToast(t('documents.tagUpdateSuccess'), 'success');
    } catch (error) {
      console.error('Error updating tags:', error);
      showToast(t('documents.tagUpdateError'), 'error');
      // Revert changes in case of error
      refetchDocuments();
    }
  };
  
  // Handle move to collection
  const handleMoveToCollection = async (id: string, collectionId: string) => {
    // Update the document in the local state with new collection
    const updatedDocuments = documents.map(doc => 
      doc.id === id ? { ...doc, collection: collectionId } : doc
    );
    setDocuments(updatedDocuments);
    
    try {
      // Call API to move document to collection
      await apiService.moveDocumentToCollection(id, collectionId);
      showToast(t('documents.moveSuccess'), 'success');
    } catch (error) {
      console.error('Error moving document:', error);
      showToast(t('documents.moveError'), 'error');
      // Revert changes in case of error
      refetchDocuments();
    }
  };
  
  // Handle create collection
  const handleCreateCollection = async (name: string) => {
    // This would call an API to create a collection
    // For now, we just add it to the local state
    const newId = `temp-${Date.now()}`;
    const newCollection: Collection = {
      id: newId,
      name,
      documentCount: 0
    };
    setCollections([...collections, newCollection]);
    return Promise.resolve();
  };
  
  // Handle update collection
  const handleUpdateCollection = async (id: string, name: string) => {
    // Update the collection in the local state
    const updatedCollections = collections.map(collection => 
      collection.id === id ? { ...collection, name } : collection
    );
    setCollections(updatedCollections);
    return Promise.resolve();
  };
  
  // Handle delete collection
  const handleDeleteCollection = async (id: string) => {
    // Delete the collection from the local state
    setCollections(collections.filter(collection => collection.id !== id));
    // Move documents from this collection to 'uncategorized'
    const updatedDocuments = documents.map(doc => 
      doc.collection === id ? { ...doc, collection: 'uncategorized' } : doc
    );
    setDocuments(updatedDocuments);
    if (selectedCollection === id) {
      setSelectedCollection('all');
    }
    return Promise.resolve();
  };
  
  // Format file size for display
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
  };
  
  // Render document preview card
  const renderDocumentPreview = () => {
    if (!selectedDocument) {
      return (
        <Card sx={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <CardContent>
            <Typography variant="subtitle1" color="textSecondary" align="center">
              Select a document to view details
            </Typography>
          </CardContent>
        </Card>
      );
    }
    
    return (
      <Card sx={{ height: '100%' }}>
        <CardHeader 
          title={selectedDocument.name}
          subheader={`${selectedDocument.type.toUpperCase()} - ${formatFileSize(selectedDocument.size)}`}
        />
        <Divider />
        <CardContent>
          <Box sx={{ mb: 3, display: 'flex', justifyContent: 'center' }}>
            {/* Placeholder for document preview */}
            <Paper 
              variant="outlined" 
              sx={{ 
                width: '100%', 
                height: 200, 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                backgroundColor: 'rgba(0, 0, 0, 0.04)'
              }}
            >
              <Typography variant="body2" color="textSecondary">
                {selectedDocument.type.toUpperCase()} Preview
              </Typography>
            </Paper>
          </Box>
          
          <Typography variant="subtitle2" gutterBottom>
            Document Information
          </Typography>
          
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <Typography variant="body2" color="textSecondary">Status</Typography>
              <Typography variant="body1">
                {selectedDocument.status && selectedDocument.status.charAt(0).toUpperCase() + selectedDocument.status.slice(1)}
                {selectedDocument.chunks && ` (${selectedDocument.chunks} chunks)`}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant="body2" color="textSecondary">Upload Date</Typography>
              <Typography variant="body1">
                {new Date(selectedDocument.uploadDate).toLocaleDateString()}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant="body2" color="textSecondary">Collection</Typography>
              <Typography variant="body1">
                {collections.find(c => c.id === selectedDocument.collection)?.name || 'Uncategorized'}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant="body2" color="textSecondary">Vector Store</Typography>
              <Typography variant="body1">{selectedDocument.vectorStore || 'FAISS'}</Typography>
            </Grid>
            {selectedDocument.lastUsed && (
              <Grid item xs={12}>
                <Typography variant="body2" color="textSecondary">Last Used</Typography>
                <Typography variant="body1">
                  {new Date(selectedDocument.lastUsed).toLocaleDateString()} in query "{selectedDocument.lastQuery}"
                </Typography>
              </Grid>
            )}
          </Grid>
        </CardContent>
        <Divider />
        <Box sx={{ display: 'flex', justifyContent: 'space-between', p: 2 }}>
          <Button 
            variant="outlined" 
            startIcon={<VisibilityIcon />}
            size="small"
            onClick={() => handleOpenDocumentDetails(selectedDocument)}
          >
            View Full Document
          </Button>
          <Button 
            variant="outlined" 
            startIcon={<RefreshIcon />}
            size="small"
            onClick={() => handleReprocessDocument(selectedDocument.id)}
          >
            Re-process
          </Button>
          <Button 
            variant="outlined" 
            startIcon={<DownloadIcon />}
            size="small"
          >
            Download
          </Button>
        </Box>
      </Card>
    );
  };
  
  // Advanced filter handlers
  const handleFilterClick = (event: React.MouseEvent<HTMLElement>) => {
    setFilterAnchorEl(event.currentTarget);
  };
  
  const handleFilterClose = () => {
    setFilterAnchorEl(null);
  };
  
  const handleFilterChange = (field: string, value: any) => {
    setFilterOptions({
      ...filterOptions,
      [field]: value,
    });
  };
  
  const handleApplyFilters = () => {
    // Filters are automatically applied since they're used in filteredDocuments
    handleFilterClose();
  };
  
  const handleResetFilters = () => {
    setFilterOptions({
      types: [],
      tags: [],
      status: '',
      dateRange: {
        start: '',
        end: '',
      },
    });
  };
  
  const isFilterActive = () => {
    return filterOptions.types.length > 0 || 
           filterOptions.tags.length > 0 || 
           filterOptions.status !== '' ||
           filterOptions.dateRange.start !== '' ||
           filterOptions.dateRange.end !== '';
  };
  
  return (
    <Container maxWidth="xl">
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          {t('documents.title')}
        </Typography>
        <Typography variant="body1" color="textSecondary">
          {t('documents.subtitle')}
        </Typography>
      </Box>
      
      {/* Action Buttons */}
      <Box sx={{ mb: 3, display: 'flex', gap: 2 }}>
        <DocumentUploader 
          collections={collections}
          onUploadComplete={refetchDocuments}
        />
        <CollectionManager
          collections={collections}
          onCreateCollection={handleCreateCollection}
          onUpdateCollection={handleUpdateCollection}
          onDeleteCollection={handleDeleteCollection}
        />
        
        {/* Advanced Filter Button */}
        <Button
          variant="outlined"
          startIcon={<FilterListIcon />}
          onClick={handleFilterClick}
          color={isFilterActive() ? "primary" : "inherit"}
        >
          {isFilterActive() ? "Filters Applied" : "Filters"}
        </Button>
        
        <TextField
          placeholder="Search documents..."
          size="small"
          value={searchQuery}
          onChange={handleSearchChange}
          sx={{ ml: 'auto', width: 300 }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
        <input
          type="file"
          multiple
          ref={fileInputRef}
          style={{ display: 'none' }}
          onChange={handleFileInputChange}
        />
      </Box>
      
      {/* Advanced Filter Popover */}
      <Popover
        open={Boolean(filterAnchorEl)}
        anchorEl={filterAnchorEl}
        onClose={handleFilterClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        PaperProps={{
          sx: { width: 400, p: 3 }
        }}
      >
        <Typography variant="h6" gutterBottom>
          Advanced Filters
        </Typography>
        
        <Grid container spacing={2}>
          {/* Document Type Filter */}
          <Grid item xs={12}>
            <Autocomplete
              multiple
              id="document-type-filter"
              options={uniqueTypes}
              value={filterOptions.types}
              onChange={(e, newValue) => handleFilterChange('types', newValue)}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Document Types"
                  placeholder="Select types"
                  size="small"
                />
              )}
            />
          </Grid>
          
          {/* Tags Filter */}
          <Grid item xs={12}>
            <Autocomplete
              multiple
              id="tags-filter"
              options={uniqueTags}
              value={filterOptions.tags}
              onChange={(e, newValue) => handleFilterChange('tags', newValue)}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Tags"
                  placeholder="Select tags"
                  size="small"
                />
              )}
            />
          </Grid>
          
          {/* Status Filter */}
          <Grid item xs={12}>
            <FormControl fullWidth size="small">
              <InputLabel id="status-filter-label">Status</InputLabel>
              <Select
                labelId="status-filter-label"
                id="status-filter"
                value={filterOptions.status}
                label="Status"
                onChange={(e) => handleFilterChange('status', e.target.value)}
              >
                <MenuItem value="">
                  <em>Any</em>
                </MenuItem>
                {uniqueStatuses.map(status => (
                  <MenuItem key={status} value={status}>
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          
          {/* Date Range Filter */}
          <Grid item xs={6}>
            <TextField
              id="date-filter-start"
              label="From Date"
              type="date"
              value={filterOptions.dateRange.start}
              onChange={(e) => handleFilterChange('dateRange', { 
                ...filterOptions.dateRange, 
                start: e.target.value 
              })}
              InputLabelProps={{
                shrink: true,
              }}
              size="small"
              fullWidth
            />
          </Grid>
          <Grid item xs={6}>
            <TextField
              id="date-filter-end"
              label="To Date"
              type="date"
              value={filterOptions.dateRange.end}
              onChange={(e) => handleFilterChange('dateRange', { 
                ...filterOptions.dateRange, 
                end: e.target.value 
              })}
              InputLabelProps={{
                shrink: true,
              }}
              size="small"
              fullWidth
            />
          </Grid>
          
          {/* Filter Actions */}
          <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1, mt: 2 }}>
            <Button 
              variant="text" 
              onClick={handleResetFilters}
            >
              Reset
            </Button>
            <Button 
              variant="contained" 
              onClick={handleApplyFilters}
            >
              Apply Filters
            </Button>
          </Grid>
        </Grid>
      </Popover>
      
      <Grid container spacing={3}>
        {/* Left side - Collections and Documents */}
        <Grid item xs={12} md={8}>
          {/* Collections dropdown */}
          <Box sx={{ mb: 2 }}>
            <FormControl fullWidth size="small">
              <InputLabel id="collection-select-label">Collections</InputLabel>
              <Select
                labelId="collection-select-label"
                id="collection-select"
                value={selectedCollection}
                label="Collections"
                onChange={handleCollectionChange}
              >
                <MenuItem value="all">All Documents ({documents.length})</MenuItem>
                {collections.map((collection) => (
                  <MenuItem key={collection.id} value={collection.id}>
                    {collection.name} ({collection.documentCount})
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
          
          {/* Documents table */}
          <Card>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2 }}>
              <Typography variant="h6" component="div">
                Documents ({filteredDocuments.length})
                {isFilterActive() && (
                  <Chip
                    label="Filtered"
                    color="primary"
                    size="small"
                    onDelete={handleResetFilters}
                    sx={{ ml: 1 }}
                  />
                )}
              </Typography>
              <FormControl size="small" sx={{ width: 150 }}>
                <InputLabel id="sort-select-label">Sort By</InputLabel>
                <Select
                  labelId="sort-select-label"
                  id="sort-select"
                  value={sortBy}
                  label="Sort By"
                  onChange={handleSortChange}
                >
                  <MenuItem value="date">Date</MenuItem>
                  <MenuItem value="name">Name</MenuItem>
                  <MenuItem value="size">Size</MenuItem>
                  <MenuItem value="type">Type</MenuItem>
                </Select>
              </FormControl>
            </Box>
            <Divider />
            <TableContainer sx={{ maxHeight: 400 }}>
              <Table stickyHeader>
                <TableHead>
                  <TableRow>
                    <TableCell padding="checkbox">
                      <Checkbox
                        indeterminate={selectedDocuments.length > 0 && selectedDocuments.length < sortedDocuments.length}
                        checked={selectedDocuments.length === sortedDocuments.length && sortedDocuments.length > 0}
                        onChange={handleSelectAll}
                      />
                    </TableCell>
                    <TableCell>Name</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Size</TableCell>
                    <TableCell>Date</TableCell>
                    <TableCell>Tags</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {sortedDocuments.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} align="center">
                        <Typography variant="body2" color="textSecondary" sx={{ py: 2 }}>
                          No documents found
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ) : (
                    // Apply pagination to the sortedDocuments array
                    sortedDocuments
                      .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                      .map((doc) => (
                      <TableRow 
                        key={doc.id}
                        hover
                        selected={selectedDocument?.id === doc.id}
                        onClick={() => handleDocumentClick(doc)}
                        sx={{ cursor: 'pointer' }}
                      >
                        <TableCell padding="checkbox" onClick={(e) => e.stopPropagation()}>
                          <Checkbox
                            checked={selectedDocuments.includes(doc.id)}
                            onChange={(e) => handleSelectDocument(e, doc.id)}
                          />
                        </TableCell>
                        <TableCell>{doc.name}</TableCell>
                        <TableCell>{doc.type.toUpperCase()}</TableCell>
                        <TableCell>{formatFileSize(doc.size)}</TableCell>
                        <TableCell>{new Date(doc.uploadDate).toLocaleDateString()}</TableCell>
                        <TableCell>
                          {doc.tags && doc.tags.map(tag => (
                            <Chip 
                              key={tag} 
                              label={tag} 
                              size="small" 
                              sx={{ mr: 0.5, mb: 0.5 }} 
                            />
                          ))}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>
            
            {/* Add TablePagination component */}
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, 50]}
              component="div"
              count={sortedDocuments.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
            
            {/* Action buttons for selected documents */}
            {selectedDocuments.length > 0 && (
              <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end' }}>
                <Button 
                  variant="outlined" 
                  color="error" 
                  startIcon={<DeleteIcon />}
                  onClick={() => openDeleteDialog('multiple')}
                  sx={{ ml: 1 }}
                >
                  Delete Selected ({selectedDocuments.length})
                </Button>
              </Box>
            )}
          </Card>
        </Grid>
        
        {/* Right side - Document Preview */}
        <Grid item xs={12} md={4}>
          <Typography variant="h6" component="div" sx={{ mb: 2 }}>
            Document Preview
          </Typography>
          {renderDocumentPreview()}
        </Grid>
      </Grid>
      
      {/* Document Details Dialog */}
      {selectedDocument && documentDetailsOpen && (
        <DocumentDetails
          document={selectedDocument}
          collections={collections}
          open={documentDetailsOpen}
          onClose={handleCloseDocumentDetails}
          onDelete={handleDeleteDocument}
          onReprocess={handleReprocessDocument}
          onUpdateTags={handleUpdateTags}
          onMoveToCollection={handleMoveToCollection}
        />
      )}

      {/* Add Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={closeDeleteDialog}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <DialogTitle id="delete-dialog-title">
          {deleteDialogType === 'single' ? 'Delete Document' : 'Delete Multiple Documents'}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="delete-dialog-description">
            {deleteDialogType === 'single'
              ? 'Are you sure you want to delete this document? This action cannot be undone.'
              : `Are you sure you want to delete ${selectedDocuments.length} selected documents? This action cannot be undone.`}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeDeleteDialog} color="primary">
            Cancel
          </Button>
          <Button onClick={confirmDelete} color="error" autoFocus>
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default DocumentsPage; 