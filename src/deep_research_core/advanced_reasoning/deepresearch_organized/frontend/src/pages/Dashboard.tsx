import React, { useEffect } from 'react';
import { 
  Typography, 
  Grid, 
  Paper, 
  Box, 
  Card, 
  CardContent, 
  CardHeader,
  Button,
  Divider,
  useTheme,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Badge
} from '@mui/material';
import {
  QueryStats as QueryStatsIcon,
  Storage as StorageIcon,
  Psychology as ModelIcon,
  Timelapse as PerformanceIcon,
  CloudOutlined as CloudIcon,
  Devices as DevicesIcon,
  Sync as SyncIcon,
  Check as CheckIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import MainLayout from '../components/layout/MainLayout';
import { ContentCard, LoadingIndicator } from '../components/common';
import { useProviders } from '../hooks/useApi';

// Mock data for dashboard stats
const stats = [
  { id: 1, title: 'Total Queries', value: '1,248', icon: <QueryStatsIcon fontSize="large" color="primary" /> },
  { id: 2, title: 'Documents', value: '342', icon: <StorageIcon fontSize="large" color="primary" /> },
  { id: 3, title: 'Models Used', value: '7', icon: <ModelIcon fontSize="large" color="primary" /> },
  { id: 4, title: 'Avg Response Time', value: '1.2s', icon: <PerformanceIcon fontSize="large" color="primary" /> },
];

// Mock data for recent queries
const recentQueries = [
  { id: 1, query: 'Explain the implications of quantum computing on cryptography', timestamp: '2 hours ago', model: 'GPT-4o' },
  { id: 2, query: 'Compare and contrast different RAG strategies for long documents', timestamp: '4 hours ago', model: 'Claude-3-Opus' },
  { id: 3, query: 'What are the latest advancements in tree of thought reasoning?', timestamp: '1 day ago', model: 'QwQ-32B' },
  { id: 4, query: 'How does chain of thought reasoning compare to tree of thought?', timestamp: '2 days ago', model: 'Deepseek-R1' },
];

const Dashboard: React.FC = () => {
  const theme = useTheme();
  const { data: providersData, loading: providersLoading, error: providersError, execute: fetchProviders } = useProviders();
  
  useEffect(() => {
    fetchProviders();
  }, [fetchProviders]);
  
  return (
    <MainLayout title="Dashboard">
      <Box sx={{ pt: 2, pb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom fontWeight="medium">
          Welcome to Deep Research Core
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          Advanced AI reasoning and research at your fingertips.
        </Typography>
        
        {/* Stats cards */}
        <Grid container spacing={3} sx={{ mt: 2, mb: 4 }}>
          {stats.map((stat) => (
            <Grid item xs={12} sm={6} md={3} key={stat.id}>
              <Paper
                elevation={0}
                sx={{
                  p: 2,
                  height: '100%',
                  borderRadius: 2,
                  border: `1px solid ${theme.palette.divider}`,
                }}
              >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary">
                      {stat.title}
                    </Typography>
                    <Typography variant="h4" component="div" sx={{ mt: 1, fontWeight: 'bold' }}>
                      {stat.value}
                    </Typography>
                  </Box>
                  {stat.icon}
                </Box>
              </Paper>
            </Grid>
          ))}
        </Grid>
        
        {/* Recent queries */}
        <Card 
          elevation={0} 
          sx={{ 
            mt: 4, 
            borderRadius: 2,
            border: `1px solid ${theme.palette.divider}`,
          }}
        >
          <CardHeader 
            title="Recent Queries" 
            action={
              <Button color="primary" size="small">
                View All
              </Button>
            }
          />
          <Divider />
          <CardContent>
            {recentQueries.map((item, index) => (
              <React.Fragment key={item.id}>
                <Box sx={{ py: 2 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={7}>
                      <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>
                        {item.query}
                      </Typography>
                    </Grid>
                    <Grid item xs={6} md={3}>
                      <Typography variant="body2" color="text.secondary">
                        {item.timestamp}
                      </Typography>
                    </Grid>
                    <Grid item xs={6} md={2}>
                      <Typography variant="body2" color="primary" sx={{ fontWeight: 'medium' }}>
                        {item.model}
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>
                {index < recentQueries.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </CardContent>
        </Card>
        
        {/* Quick actions */}
        <Grid container spacing={3} sx={{ mt: 4 }}>
          <Grid item xs={12} md={6}>
            <Card 
              elevation={0}
              sx={{ 
                borderRadius: 2,
                border: `1px solid ${theme.palette.divider}`,
                height: '100%',
              }}
            >
              <CardHeader title="Get Started" />
              <Divider />
              <CardContent>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Button variant="contained" color="primary" size="large">
                    New Query
                  </Button>
                  <Button variant="outlined" color="primary" size="large">
                    Upload Documents
                  </Button>
                  <Button variant="outlined" color="primary" size="large">
                    Explore Models
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Card 
              elevation={0}
              sx={{ 
                borderRadius: 2,
                border: `1px solid ${theme.palette.divider}`,
                height: '100%',
              }}
            >
              <CardHeader title="System Status" />
              <Divider />
              <CardContent>
                <Typography variant="body1" paragraph>
                  All systems operational
                </Typography>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">API Status</Typography>
                  <Typography variant="body2" color="success.main">Online</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Model Services</Typography>
                  <Typography variant="body2" color="success.main">Online</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Vector Database</Typography>
                  <Typography variant="body2" color="success.main">Online</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2">Document Storage</Typography>
                  <Typography variant="body2" color="success.main">Online</Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
        
        {/* Available Providers */}
        <Grid item xs={12} md={6}>
          <ContentCard
            title="AI Providers"
            subtitle="Available language model providers"
            elevation={2}
            loading={providersLoading}
          >
            {providersError ? (
              <Box sx={{ p: 2, textAlign: 'center' }}>
                <ErrorIcon color="error" fontSize="large" />
                <Typography color="error" variant="body1" sx={{ mt: 1 }}>
                  Error loading providers: {providersError.message}
                </Typography>
              </Box>
            ) : providersData?.providers ? (
              <List>
                {providersData.providers.map((provider, index) => (
                  <React.Fragment key={provider}>
                    <ListItem>
                      <ListItemAvatar>
                        <Avatar sx={{ backgroundColor: 'primary.main' }}>
                          <CloudIcon />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText 
                        primary={provider} 
                        secondary="Available" 
                        primaryTypographyProps={{ fontWeight: 'medium' }}
                      />
                      <CheckIcon color="success" />
                    </ListItem>
                    {index < providersData.providers.length - 1 && <Divider variant="inset" component="li" />}
                  </React.Fragment>
                ))}
              </List>
            ) : (
              <Box sx={{ p: 2, textAlign: 'center' }}>
                <Typography color="text.secondary">
                  No providers available
                </Typography>
              </Box>
            )}
          </ContentCard>
        </Grid>
        
        {/* Recent Activity */}
        <Grid item xs={12}>
          <ContentCard
            title="Recent Activity"
            subtitle="Latest system activities"
            elevation={2}
          >
            <Box sx={{ p: 2, textAlign: 'center' }}>
              <Typography color="text.secondary">
                No recent activities
              </Typography>
            </Box>
          </ContentCard>
        </Grid>
      </Box>
    </MainLayout>
  );
};

export default Dashboard; 