import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Container, 
  Typography, 
  Paper, 
  Tabs, 
  Tab, 
  useTheme,
  useMediaQuery,
  Alert,
  Grid
} from '@mui/material';
import ChatInterface, { Message } from '../components/reasoning/ChatInterface';
import { ResponsiveGridContainer } from '../components/common';
import { useResponsivePadding, useResponsiveFontSizes } from '../utils/ResponsiveLayout';
import feedbackService from '../services/feedbackService';

const ChatDemo = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [feedbackStatus, setFeedbackStatus] = useState<{show: boolean, success: boolean, message: string}>({
    show: false,
    success: false,
    message: ''
  });
  
  const theme = useTheme();
  const isXs = useMediaQuery(theme.breakpoints.only('xs'));
  const padding = useResponsivePadding();
  const fontSize = useResponsiveFontSizes();
  
  // Sample initial messages
  const initialMessages: Message[] = [
    {
      id: '1',
      role: 'system',
      content: 'Welcome to the Deep Research Core chat interface. You can interact with different AI models and reasoning methods to solve complex problems.',
      timestamp: new Date(Date.now() - 60000 * 5) // 5 minutes ago
    },
    {
      id: '2',
      role: 'user',
      content: 'What is Tree of Thought reasoning?',
      timestamp: new Date(Date.now() - 60000 * 4) // 4 minutes ago
    },
    {
      id: '3',
      role: 'assistant',
      content: 'Tree of Thought (ToT) is an advanced reasoning technique that allows AI models to explore multiple reasoning paths in parallel, rather than following a single chain of thought.\n\nIn ToT reasoning, the model:\n\n1. Generates multiple initial thoughts (branches)\n2. Evaluates the potential of each branch\n3. Selectively explores the most promising branches\n4. Continues branching and evaluating until reaching a solution\n\nThis approach is particularly effective for complex problems requiring planning, problem-solving, or creative thinking, as it mimics human deliberative thinking processes.',
      timestamp: new Date(Date.now() - 60000 * 3), // 3 minutes ago
      metadata: {
        model: 'gpt-4',
        reasoning: 'tot',
        executionTime: 3200
      }
    }
  ];

  useEffect(() => {
    setMessages(initialMessages);
  }, []);
  
  // Handle tab change
  const handleTabChange = (_event: any, newValue: number) => {
    setActiveTab(newValue);
  };
  
  // Simulate sending a message
  const handleSendMessage = async (message: string, modelId: string, reasoningMethod: string | null): Promise<void> => {
    setIsLoading(true);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Create simulated response
    const response = {
      id: `response-${Date.now()}`,
      role: 'assistant' as const,
      content: `This is a simulated response to your query about "${message.substring(0, 30)}..." using ${modelId} with ${reasoningMethod || 'standard'} reasoning.`,
      timestamp: new Date(),
      metadata: {
        model: modelId,
        reasoning: reasoningMethod as any,
        executionTime: 1500
      }
    };
    
    // Add simulated response to messages
    setMessages(prev => [...prev, response]);
    setIsLoading(false);
  };

  // Handle feedback submission
  const handleFeedbackSubmit = async (queryId: string, feedback: any) => {
    try {
      await feedbackService.submitFeedback(queryId, feedback);
      setFeedbackStatus({
        show: true,
        success: true,
        message: 'Thank you for your feedback!'
      });
      
      setTimeout(() => {
        setFeedbackStatus(prev => ({...prev, show: false}));
      }, 3000);
    } catch (error) {
      console.error('Error submitting feedback:', error);
      setFeedbackStatus({
        show: true,
        success: false,
        message: 'Failed to submit feedback. Please try again.'
      });
      
      setTimeout(() => {
        setFeedbackStatus(prev => ({...prev, show: false}));
      }, 3000);
    }
  };
  
  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Typography 
        variant="h1" 
        sx={{ 
          fontSize: fontSize.h1.fontSize,
          mb: 3,
          textAlign: 'center'
        }}
      >
        Deep Research Core Chat Demo
      </Typography>
      
      {feedbackStatus.show && (
        <Alert 
          severity={feedbackStatus.success ? "success" : "error"}
          sx={{ mb: 2 }}
          onClose={() => setFeedbackStatus(prev => ({...prev, show: false}))}
        >
          {feedbackStatus.message}
        </Alert>
      )}
      
      <Paper 
        elevation={3} 
        sx={{ 
          p: padding.container.padding,
          mb: 4 
        }}
      >
        <Typography 
          variant="h2" 
          sx={{ 
            fontSize: fontSize.h2.fontSize,
            mb: 2 
          }}
        >
          Experiment with Different Reasoning Methods
        </Typography>
        
        <Typography 
          variant="body1" 
          sx={{ 
            fontSize: fontSize.body1.fontSize,
            mb: 3 
          }}
        >
          This demo showcases different AI reasoning techniques used in the Deep Research Core framework.
          You can select different models and reasoning methods to see how they approach problems differently.
          After receiving a response, you can provide feedback to help improve the system.
        </Typography>
        
        <Tabs 
          value={activeTab} 
          onChange={handleTabChange}
          variant={isXs ? 'fullWidth' : 'standard'}
          sx={{ mb: 3 }}
        >
          <Tab label="Interactive Chat" />
          <Tab label="Visualizations" />
          <Tab label="Comparison" />
        </Tabs>
        
        {activeTab === 0 && (
          <Grid container>
            <Grid item xs={12} md={9} lg={8} sx={{ mx: 'auto' }}>
              <ChatInterface 
                initialMessages={messages}
                onSendMessage={handleSendMessage}
                isLoading={isLoading}
              />
            </Grid>
          </Grid>
        )}
        
        {activeTab === 1 && (
          <Box sx={{ p: 4, textAlign: 'center' }}>
            <Typography variant="h3" sx={{ fontSize: fontSize.h3.fontSize }}>
              Reasoning Visualizations
            </Typography>
            <Typography variant="body1" sx={{ mt: 2 }}>
              This section will contain visualizations for different reasoning methods.
              (Coming soon)
            </Typography>
          </Box>
        )}
        
        {activeTab === 2 && (
          <Box sx={{ p: 4, textAlign: 'center' }}>
            <Typography variant="h3" sx={{ fontSize: fontSize.h3.fontSize }}>
              Model & Method Comparison
            </Typography>
            <Typography variant="body1" sx={{ mt: 2 }}>
              This section will allow side-by-side comparison of different models and reasoning methods.
              (Coming soon)
            </Typography>
          </Box>
        )}
      </Paper>
      
      <Paper 
        elevation={2} 
        sx={{ 
          p: padding.section.padding,
          backgroundColor: theme.palette.background.paper 
        }}
      >
        <Typography 
          variant="h2" 
          sx={{ 
            fontSize: fontSize.h2.fontSize,
            mb: 2 
          }}
        >
          About the Reasoning Methods
        </Typography>
        
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Paper 
              elevation={1} 
              sx={{ 
                p: padding.card.padding,
                height: '100%',
                borderTop: `4px solid ${theme.palette.primary.main}`
              }}
            >
              <Typography variant="h3" sx={{ fontSize: fontSize.h3.fontSize, mb: 1 }}>
                Tree of Thought
              </Typography>
              <Typography variant="body2">
                Explores multiple reasoning paths and evaluates them, choosing the most promising ones
                to continue exploration, mimicking deliberative thinking processes.
              </Typography>
            </Paper>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Paper 
              elevation={1} 
              sx={{ 
                p: padding.card.padding,
                height: '100%',
                borderTop: `4px solid ${theme.palette.secondary.main}`
              }}
            >
              <Typography variant="h3" sx={{ fontSize: fontSize.h3.fontSize, mb: 1 }}>
                ReAct
              </Typography>
              <Typography variant="body2">
                Combines reasoning and acting, allowing models to interact with external tools and
                use the feedback to improve responses.
              </Typography>
            </Paper>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Paper 
              elevation={1} 
              sx={{ 
                p: padding.card.padding,
                height: '100%',
                borderTop: `4px solid ${theme.palette.success.main}`
              }}
            >
              <Typography variant="h3" sx={{ fontSize: fontSize.h3.fontSize, mb: 1 }}>
                Chain of Thought
              </Typography>
              <Typography variant="body2">
                Sequential step-by-step reasoning that breaks down complex problems into manageable
                intermediate steps.
              </Typography>
            </Paper>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Paper 
              elevation={1} 
              sx={{ 
                p: padding.card.padding,
                height: '100%',
                borderTop: `4px solid ${theme.palette.info.main}`
              }}
            >
              <Typography variant="h3" sx={{ fontSize: fontSize.h3.fontSize, mb: 1 }}>
                RAG
              </Typography>
              <Typography variant="body2">
                Retrieval-Augmented Generation enhances model responses by incorporating relevant
                information from external documents and databases.
              </Typography>
            </Paper>
          </Grid>
        </Grid>
      </Paper>
    </Container>
  );
};

export default ChatDemo; 