import React, { useState } from 'react';
import { 
  Typography, 
  Paper, 
  Button, 
  TextField, 
  Switch, 
  FormControlLabel,
  IconButton,
  useTheme,
  useMediaQuery,
  Divider,
  Tabs,
  Tab,
  Box
} from '@mui/material';
import ViewListIcon from '@mui/icons-material/ViewList';
import ViewModuleIcon from '@mui/icons-material/ViewModule';
import InfoIcon from '@mui/icons-material/Info';
import { 
  ResponsiveCard, 
  ResponsiveGridContainer, 
  ResponsiveGridItem 
} from '../components/common';
import { 
  useResponsivePadding, 
  useResponsiveFontSizes,
  useResponsiveMargins 
} from '../utils/ResponsiveLayout';

// Sample data for cards
const cardData = [
  {
    id: 1,
    title: 'Introduction to Tree of Thought Reasoning',
    subtitle: 'Advanced reasoning techniques',
    content: 'Learn how ToT enables AI models to explore multiple reasoning paths and select the most effective solution strategy.',
    image: 'https://source.unsplash.com/random/800x600/?ai'
  },
  {
    id: 2,
    title: 'ReAct Pattern Implementation',
    subtitle: 'Reasoning and Acting',
    content: 'Discover how to implement the ReAct pattern for step-by-step reasoning with tool interaction capabilities.',
    image: 'https://source.unsplash.com/random/800x600/?code'
  },
  {
    id: 3,
    title: 'Chain of Thought Prompting',
    subtitle: 'Step-by-step reasoning',
    content: 'Explore techniques to guide language models through explicit reasoning steps to improve problem-solving accuracy.',
    image: 'https://source.unsplash.com/random/800x600/?brain'
  },
  {
    id: 4,
    title: 'Retrieval Augmented Generation',
    subtitle: 'Knowledge integration',
    content: 'Learn how to enhance language model outputs by incorporating external knowledge sources and databases.',
    image: 'https://source.unsplash.com/random/800x600/?library'
  },
  {
    id: 5,
    title: 'Vietnamese Language Support',
    subtitle: 'Multilingual capabilities',
    content: 'Implement specialized support for Vietnamese language processing in AI models with proper tokenization.',
    image: 'https://source.unsplash.com/random/800x600/?vietnam'
  },
  {
    id: 6,
    title: 'Fine-tuning Optimization Techniques',
    subtitle: 'Parameter-efficient methods',
    content: 'Explore PEFT methods like LoRA and QLoRA to efficiently fine-tune large language models with minimal resources.',
    image: 'https://source.unsplash.com/random/800x600/?network'
  }
];

const ResponsiveDemo = () => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [fullHeight, setFullHeight] = useState(true);
  const [selectedTab, setSelectedTab] = useState(0);
  
  const theme = useTheme();
  const isXs = useMediaQuery(theme.breakpoints.only('xs'));
  const isSm = useMediaQuery(theme.breakpoints.only('sm'));
  const isMd = useMediaQuery(theme.breakpoints.only('md'));
  
  const paddings = useResponsivePadding();
  const fontSizes = useResponsiveFontSizes();
  const margins = useResponsiveMargins();
  
  const handleViewModeChange = () => {
    setViewMode(prev => prev === 'grid' ? 'list' : 'grid');
  };
  
  const handleTabChange = (_event: any, newValue: number) => {
    setSelectedTab(newValue);
  };
  
  // Determine number of columns based on screen size and view mode
  const getCardSize = () => {
    if (viewMode === 'list') return 12;
    if (isXs) return 12;
    if (isSm) return 6;
    if (isMd) return 4;
    return 3;
  };
  
  return (
    <Paper sx={{ 
      padding: paddings.container.padding,
      minHeight: '100vh',
      backgroundColor: theme.palette.background.default
    }}>
      <Typography 
        variant="h1" 
        sx={{ 
          fontSize: fontSizes.h1.fontSize,
          mb: margins.section.marginBottom
        }}
      >
        Responsive Component Demo
      </Typography>
      
      <Paper sx={{ 
        p: paddings.section.padding, 
        mb: margins.section.marginBottom,
        backgroundColor: theme.palette.background.paper
      }}>
        <Typography variant="h2" sx={{ fontSize: fontSizes.h2.fontSize, mb: 2 }}>
          Layout Controls
        </Typography>
        
        <ResponsiveGridContainer spacing={{ xs: 1, sm: 2, md: 3 }}>
          <ResponsiveGridItem xs={12} sm={6} md={3}>
            <FormControlLabel
              control={
                <Switch 
                  checked={viewMode === 'grid'} 
                  onChange={handleViewModeChange}
                  color="primary"
                />
              }
              label={`View Mode: ${viewMode === 'grid' ? 'Grid' : 'List'}`}
            />
          </ResponsiveGridItem>
          
          <ResponsiveGridItem xs={12} sm={6} md={3}>
            <FormControlLabel
              control={
                <Switch 
                  checked={fullHeight} 
                  onChange={() => setFullHeight(!fullHeight)}
                  color="primary"
                />
              }
              label="Full Height Cards"
            />
          </ResponsiveGridItem>
          
          <ResponsiveGridItem xs={12} sm={6} md={3}>
            <IconButton onClick={handleViewModeChange} color="primary">
              {viewMode === 'grid' ? <ViewListIcon /> : <ViewModuleIcon />}
            </IconButton>
          </ResponsiveGridItem>
          
          <ResponsiveGridItem xs={12} sm={6} md={3}>
            <Button 
              variant="contained" 
              startIcon={<InfoIcon />}
              size={isXs ? 'small' : 'medium'}
              fullWidth={isXs}
            >
              View Documentation
            </Button>
          </ResponsiveGridItem>
        </ResponsiveGridContainer>
      </Paper>
      
      <Paper sx={{ mb: margins.section.marginBottom }}>
        <Tabs 
          value={selectedTab} 
          onChange={handleTabChange}
          variant={isXs ? 'fullWidth' : 'standard'}
          scrollButtons={isXs ? 'auto' : false}
          sx={{ mb: 2 }}
        >
          <Tab label="All Topics" />
          <Tab label="Reasoning" />
          <Tab label="Fine-tuning" />
          <Tab label="Languages" />
        </Tabs>
        
        <Divider />
        
        <Box sx={{ p: paddings.section.padding }}>
          <ResponsiveGridContainer spacing={{ xs: 1, sm: 2, md: 3 }}>
            {cardData.map(card => (
              <ResponsiveGridItem key={card.id} xs={12} sm={getCardSize()} md={getCardSize()} lg={getCardSize()}>
                <ResponsiveCard
                  title={card.title}
                  subtitle={card.subtitle}
                  content={
                    <Typography variant="body1" sx={{ fontSize: fontSizes.body1.fontSize }}>
                      {card.content}
                    </Typography>
                  }
                  image={card.image}
                  fullHeight={fullHeight}
                  actions={
                    <Button size={isXs ? 'small' : 'medium'} color="primary">
                      Learn More
                    </Button>
                  }
                  elevation={2}
                />
              </ResponsiveGridItem>
            ))}
          </ResponsiveGridContainer>
        </Box>
      </Paper>
      
      <Paper sx={{ p: paddings.section.padding }}>
        <Typography variant="h2" sx={{ fontSize: fontSizes.h2.fontSize, mb: 2 }}>
          Responsive Form Elements
        </Typography>
        
        <ResponsiveGridContainer spacing={{ xs: 1, sm: 2 }}>
          <ResponsiveGridItem xs={12} sm={6} md={4}>
            <TextField
              label="Full Name"
              variant="outlined"
              fullWidth
              margin="normal"
              size={isXs ? 'small' : 'medium'}
            />
          </ResponsiveGridItem>
          
          <ResponsiveGridItem xs={12} sm={6} md={4}>
            <TextField
              label="Email"
              variant="outlined"
              fullWidth
              margin="normal"
              size={isXs ? 'small' : 'medium'}
            />
          </ResponsiveGridItem>
          
          <ResponsiveGridItem xs={12} md={4}>
            <TextField
              label="Phone"
              variant="outlined"
              fullWidth
              margin="normal"
              size={isXs ? 'small' : 'medium'}
            />
          </ResponsiveGridItem>
          
          <ResponsiveGridItem xs={12}>
            <TextField
              label="Message"
              variant="outlined"
              fullWidth
              multiline
              rows={4}
              margin="normal"
            />
          </ResponsiveGridItem>
          
          <ResponsiveGridItem xs={12} sm={6}>
            <Button 
              variant="contained" 
              color="primary" 
              size={isXs ? 'small' : 'medium'}
              fullWidth={isXs}
              sx={{ mt: 2 }}
            >
              Submit
            </Button>
          </ResponsiveGridItem>
        </ResponsiveGridContainer>
      </Paper>
    </Paper>
  );
};

export default ResponsiveDemo; 