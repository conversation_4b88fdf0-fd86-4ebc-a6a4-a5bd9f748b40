import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, Paper, Button, Container, CircularProgress, Alert } from '@mui/material';
import { DebugConsole, DebugSession, DebugStep } from '../components/debugging';
import { useParams, useNavigate } from 'react-router-dom';
import { saveAs } from 'file-saver';

// In a real application, this would be fetched from an API
const mockDebugSession: DebugSession = {
  id: 'session-456',
  startTime: new Date(Date.now() - 1000 * 60 * 5).toISOString(), // 5 minutes ago
  endTime: new Date().toISOString(),
  query: 'How do graphs of thoughts compare to tree of thoughts in complex reasoning tasks?',
  steps: [
    {
      id: 'step-1',
      timestamp: new Date(Date.now() - 1000 * 60 * 4.8).toISOString(),
      type: 'model_call',
      name: 'Initial Query Analysis',
      content: 'The system analyzes the query to understand the user is asking about a comparison between graph-based and tree-based reasoning approaches.',
      duration: 320,
      modelInput: 'How do graphs of thoughts compare to tree of thoughts in complex reasoning tasks?',
      modelOutput: 'The query is asking for a comparison between Graph of Thoughts (GoT) and Tree of Thoughts (ToT) approaches specifically in the context of complex reasoning tasks.'
    },
    {
      id: 'step-2',
      timestamp: new Date(Date.now() - 1000 * 60 * 4.5).toISOString(),
      type: 'retrieval',
      name: 'Knowledge Retrieval - Graph of Thoughts',
      content: 'Retrieved information about Graph of Thoughts from the knowledge base.',
      duration: 230,
      metadata: {
        source_documents: 3,
        confidence: 0.89,
        top_document: 'graph_of_thoughts_overview.pdf'
      }
    },
    {
      id: 'step-3',
      timestamp: new Date(Date.now() - 1000 * 60 * 4.3).toISOString(),
      type: 'retrieval',
      name: 'Knowledge Retrieval - Tree of Thoughts',
      content: 'Retrieved information about Tree of Thoughts from the knowledge base.',
      duration: 210,
      metadata: {
        source_documents: 2,
        confidence: 0.92,
        top_document: 'tree_of_thoughts_original_paper.pdf'
      }
    },
    {
      id: 'step-4',
      timestamp: new Date(Date.now() - 1000 * 60 * 4.0).toISOString(),
      type: 'reasoning',
      name: 'Initial Reasoning - Structural Differences',
      content: 'The system compares the structural differences between GoT and ToT. Unlike ToT which uses a tree structure with a fixed progression path, GoT allows for a more flexible graph structure with cycles and complex connections.',
      duration: 450,
      metadata: {
        reasoning_strategy: 'comparative_analysis',
        confidence: 0.85
      }
    },
    {
      id: 'step-5',
      timestamp: new Date(Date.now() - 1000 * 60 * 3.7).toISOString(),
      type: 'reasoning',
      name: 'Performance Analysis - Complex Reasoning',
      content: 'Analysis of performance characteristics in complex reasoning tasks. GoT can represent more complex relationships and dependencies between thoughts, while ToT is more structured and easier to navigate systematically.',
      duration: 380,
      metadata: {
        reasoning_strategy: 'performance_comparison',
        confidence: 0.78
      }
    },
    {
      id: 'step-6',
      timestamp: new Date(Date.now() - 1000 * 60 * 3.4).toISOString(),
      type: 'error',
      name: 'Visualization Generation Error',
      content: 'Attempted to generate a comparison visualization but encountered an error.',
      error: 'Visualization module failed: Missing dependency "d3-force-graph"',
      duration: 120
    },
    {
      id: 'step-7',
      timestamp: new Date(Date.now() - 1000 * 60 * 3.1).toISOString(),
      type: 'action',
      name: 'Fallback to Text Comparison',
      content: 'System falls back to generating a textual comparison instead of a visualization.',
      duration: 80
    },
    {
      id: 'step-8',
      timestamp: new Date(Date.now() - 1000 * 60 * 2.8).toISOString(),
      type: 'model_call',
      name: 'Final Response Generation',
      content: 'Generating the final response comparing GoT and ToT based on the analysis.',
      duration: 520,
      modelInput: 'Generate a comprehensive comparison between Graph of Thoughts and Tree of Thoughts for complex reasoning tasks based on the analysis.',
      modelOutput: 'Graph of Thoughts (GoT) and Tree of Thoughts (ToT) are both advanced reasoning frameworks that extend beyond traditional language model generation. The key differences are:\n\n1. Structure: ToT uses a tree structure where each node can branch into multiple child thoughts but follows a hierarchical organization. GoT uses a more flexible graph structure allowing for cycles and connections between any nodes.\n\n2. Flexibility: GoT offers greater flexibility in representation, allowing thoughts to connect to any other thoughts, not just parent-child relationships.\n\n3. Complex Dependencies: GoT can better represent complex interdependencies between thoughts, making it potentially more suited for problems where thoughts have multiple connections.\n\n4. Navigation: ToT provides clearer, more systematic exploration paths, while GoT's exploration space can be more complex but also more expressive.\n\n5. Implementation: ToT is generally simpler to implement and reason about, while GoT requires more sophisticated graph handling.\n\nFor complex reasoning tasks, GoT may offer advantages when the problem involves intricate relationships between concepts that don\'t fit neatly into a tree hierarchy. However, ToT may be more efficient for problems where a clear hierarchical decomposition is possible.'
    }
  ],
  metadata: {
    model: 'OpenAI/GPT-4',
    temperature: 0.2,
    total_duration: 2310,
    success: true
  }
};

const DebugPage: React.FC = () => {
  const { sessionId } = useParams<{ sessionId: string }>();
  const navigate = useNavigate();
  const [debugSession, setDebugSession] = useState<DebugSession | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  // Fetch debug session data
  useEffect(() => {
    // In a real application, this would call an API
    setLoading(true);
    setTimeout(() => {
      if (sessionId) {
        // Simulate API call
        setDebugSession(mockDebugSession);
      } else {
        // If no session ID provided, we could load the most recent session
        // or show a list of sessions
        setDebugSession(mockDebugSession);
      }
      setLoading(false);
    }, 800); // Simulate network delay
  }, [sessionId]);
  
  // Handlers for the debug console
  const handleStepClick = useCallback((step: DebugStep) => {
    console.log('Step clicked:', step);
    // In a real app, this might open a detailed view or highlight something
  }, []);
  
  const handleSearch = useCallback((query: string) => {
    console.log('Searching for:', query);
    // In a real app, this would filter the debug steps or fetch new data
  }, []);
  
  const handleClear = useCallback(() => {
    console.log('Clearing debug console');
    // In a real app, this would clear the current session
  }, []);
  
  const handleSave = useCallback((session: DebugSession) => {
    console.log('Saving debug session:', session);
    // Save the debug session as a JSON file
    const blob = new Blob([JSON.stringify(session, null, 2)], {
      type: 'application/json'
    });
    saveAs(blob, `debug-session-${session.id}.json`);
  }, []);
  
  return (
    <Container maxWidth="xl" sx={{ my: 3 }}>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          Debugging Tools
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Interactive tools for analyzing reasoning processes, model calls, and system behavior.
        </Typography>
      </Box>
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      ) : (
        <Box>
          <DebugConsole
            session={debugSession}
            onStepClick={handleStepClick}
            onSearch={handleSearch}
            onClear={handleClear}
            onSave={handleSave}
          />
        </Box>
      )}
    </Container>
  );
};

export default DebugPage; 