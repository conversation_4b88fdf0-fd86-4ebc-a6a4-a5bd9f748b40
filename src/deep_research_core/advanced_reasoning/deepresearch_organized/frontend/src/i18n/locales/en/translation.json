{"app": {"title": "Deep Research Core", "subtitle": "Advanced AI Research and Reasoning"}, "common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "add": "Add", "remove": "Remove", "search": "Search", "upload": "Upload", "download": "Download", "refresh": "Refresh", "filter": "Filter", "apply": "Apply", "reset": "Reset", "confirm": "Confirm", "yes": "Yes", "no": "No", "loading": "Loading...", "noResults": "No results found", "actions": "Actions", "success": "Success", "error": "Error", "warning": "Warning", "info": "Information"}, "navigation": {"dashboard": "Dashboard", "documents": "Documents", "query": "Query", "analytics": "Analytics", "visualizations": "Visualizations", "comparison": "Comparison", "settings": "Settings"}, "userMenu": {"profile": "Profile", "settings": "Settings", "logout": "Logout"}, "theme": {"light": "Light Mode", "dark": "Dark Mode", "toggle": "Toggle Dark Mode"}, "language": {"en": "English", "vi": "Vietnamese", "toggle": "Switch to {{language}}"}, "documents": {"title": "Document Management", "subtitle": "Upload, organize, and manage documents for retrieval-augmented generation", "uploadDocuments": "Upload Documents", "uploadDialogTitle": "Upload New Documents", "uploadDialogSubtitle": "Select files to upload and add to the collection", "manageCollections": "Manage Collections", "manageCollectionsTitle": "Manage Document Collections", "allDocuments": "All Documents", "noDocuments": "No documents found", "documentPreview": "Document Preview", "documentDetails": "Document Details", "documentMetadata": "Document Metadata", "documentTags": "Document Tags", "documentCount": "Document Count", "status": "Status", "uploadDate": "Upload Date", "collection": "Collection", "collections": "Collections", "collectionName": "Collection Name", "createNewCollection": "Create New Collection", "editCollection": "Edit Collection", "deleteCollection": "Delete Collection", "deleteCollectionConfirm": "Are you sure you want to delete this collection? All documents will be moved to 'Uncategorized'.", "deleteDocument": "Delete Document", "deleteDocuments": "Delete Selected", "deleteDocumentConfirm": "Are you sure you want to delete this document? This action cannot be undone.", "deleteDocumentsConfirm": "Are you sure you want to delete the selected documents? This action cannot be undone.", "reprocess": "Re-process", "viewFullDocument": "View Full Document", "addTag": "Add Tag", "enterTag": "Enter a tag", "advancedFilters": "Advanced Filters", "filtersApplied": "Filters Applied", "filters": "Filters", "documentTypes": "Document Types", "selectTypes": "Select types", "tags": "Tags", "selectTags": "Select tags", "fromDate": "From Date", "toDate": "To Date", "sortBy": "Sort By", "date": "Date", "name": "Name", "size": "Size", "type": "Type", "dragAndDropFiles": "Drag and drop files here or click to browse", "dropFilesHere": "Drop files here", "browseFiles": "Browse Files", "selectedFiles": "Selected Files", "processingDocument": "Processing Document", "indexed": "Indexed", "processing": "Processing", "error": "Error", "vectorStore": "Vector Store", "lastUsed": "Last Used", "lastQuery": "Last Query", "chunks": "Chunks", "deleteSuccess": "Document(s) deleted successfully", "deleteError": "Error deleting document(s)", "reprocessSuccess": "Document re-processed successfully", "reprocessError": "Error re-processing document", "moveSuccess": "Document moved to collection successfully", "moveError": "Error moving document to collection", "tagUpdateSuccess": "Tags updated successfully", "tagUpdateError": "Error updating tags", "uploadSuccess": "Documents uploaded successfully", "uploadError": "Error uploading documents", "file": "file", "files": "files", "selected": "selected", "uncategorized": "None (Uncategorized)"}}