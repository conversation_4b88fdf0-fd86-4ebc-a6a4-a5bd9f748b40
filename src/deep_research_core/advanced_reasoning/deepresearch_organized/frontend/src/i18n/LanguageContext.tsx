import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import i18n from './i18n';

// Supported languages
export const SUPPORTED_LANGUAGES = ['en', 'vi'] as const;
export type SupportedLanguage = typeof SUPPORTED_LANGUAGES[number];

// Language context interface
interface LanguageContextType {
  currentLanguage: SupportedLanguage;
  changeLanguage: (lang: SupportedLanguage) => void;
  isRTL: boolean;
}

// Create context with default values
const LanguageContext = createContext<LanguageContextType>({
  currentLanguage: 'en',
  changeLanguage: () => {},
  isRTL: false,
});

// Hook to use the language context
export const useLanguage = () => useContext(LanguageContext);

// Provider props
interface LanguageProviderProps {
  children: ReactNode;
}

// Language provider component
export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  // Get current language from i18n
  const { i18n } = useTranslation();
  const [currentLanguage, setCurrentLanguage] = useState<SupportedLanguage>(
    (i18n.language?.substring(0, 2) as SupportedLanguage) || 'en'
  );
  
  // Check if language is RTL (for future support)
  const isRTL = ['ar', 'he', 'fa'].includes(currentLanguage);

  // Change language function
  const changeLanguage = (lang: SupportedLanguage) => {
    i18n.changeLanguage(lang);
    setCurrentLanguage(lang);
    // Store the language preference
    localStorage.setItem('i18nextLng', lang);
    // Update document language attribute
    document.documentElement.lang = lang;
    // Update direction attribute for RTL support
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
  };

  // Effect to initialize language from localStorage or browser
  useEffect(() => {
    const storedLang = localStorage.getItem('i18nextLng');
    if (storedLang && SUPPORTED_LANGUAGES.includes(storedLang as SupportedLanguage)) {
      changeLanguage(storedLang as SupportedLanguage);
    }
  }, []);

  // Context value
  const contextValue: LanguageContextType = {
    currentLanguage,
    changeLanguage,
    isRTL,
  };

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
};

export default LanguageContext; 