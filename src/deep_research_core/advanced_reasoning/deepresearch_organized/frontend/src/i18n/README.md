# Multilingual Support

This directory contains the internationalization (i18n) implementation for the Deep Research Core UI.

## Overview

The i18n system uses the following technologies:
- [i18next](https://www.i18next.com/): Core internationalization framework
- [react-i18next](https://react.i18next.com/): React bindings for i18next
- [i18next-browser-languagedetector](https://github.com/i18next/i18next-browser-languageDetector): Automatic language detection
- [i18next-http-backend](https://github.com/i18next/i18next-http-backend): Backend to load translations

## Structure

- `i18n.ts`: Main configuration and initialization of i18next
- `LanguageContext.tsx`: React context for language management
- `useLocale.ts`: Custom hook for locale-specific formatting
- `locales/`: Contains translation files for all supported languages
  - `en/`: English translations
  - `vi/`: Vietnamese translations

## Usage

### Basic Translation

To use translations in your components:

```tsx
import { useTranslation } from 'react-i18next';

function MyComponent() {
  const { t } = useTranslation();
  
  return (
    <div>
      <h1>{t('common.welcome')}</h1>
      <p>{t('myComponent.description')}</p>
    </div>
  );
}
```

### Switching Languages

To allow users to switch languages, use the `LanguageSwitcher` component:

```tsx
import LanguageSwitcher from '../components/common/LanguageSwitcher';

function Header() {
  return (
    <div>
      <LanguageSwitcher variant="icon" />
    </div>
  );
}
```

The `LanguageSwitcher` component supports several variants:
- `icon`: Display as an icon button (default)
- `text`: Display as a text button
- `menu`: Display options within an existing menu

### Using the Language Context

If you need to access or change the current language programmatically:

```tsx
import { useLanguage } from '../i18n/LanguageContext';

function LanguageAwareComponent() {
  const { currentLanguage, changeLanguage } = useLanguage();
  
  return (
    <div>
      <p>Current language: {currentLanguage}</p>
      <button onClick={() => changeLanguage('en')}>Switch to English</button>
      <button onClick={() => changeLanguage('vi')}>Switch to Vietnamese</button>
    </div>
  );
}
```

### Formatting Dates and Numbers

To format dates and numbers according to the current locale:

```tsx
import { useLocale } from '../i18n/useLocale';

function FormattedDisplay() {
  const { formatDate, formatNumber } = useLocale();
  
  return (
    <div>
      <p>Date: {formatDate(new Date())}</p>
      <p>Number: {formatNumber(1234.56)}</p>
    </div>
  );
}
```

## Adding a New Language

To add support for a new language:

1. Create a new translation file in the `locales` directory
   ```
   frontend/src/i18n/locales/[language_code]/translation.json
   ```

2. Add the new language to the `SUPPORTED_LANGUAGES` array in `LanguageContext.tsx`
   ```tsx
   export const SUPPORTED_LANGUAGES = ['en', 'vi', 'new_language'] as const;
   ```

3. Add the language to the `resources` object in `i18n.ts`
   ```tsx
   resources: {
     en: {
       translation: enTranslations,
     },
     vi: {
       translation: viTranslations,
     },
     new_language: {
       translation: newLanguageTranslations,
     }
   }
   ```

4. Add the language to the `FLAGS` and `NATIVE_NAMES` objects in `LanguageSwitcher.tsx`
   ```tsx
   const FLAGS: Record<SupportedLanguage, string> = {
     en: '🇺🇸',
     vi: '🇻🇳',
     new_language: '🏳️'  // Replace with appropriate flag emoji
   };
   
   const NATIVE_NAMES: Record<SupportedLanguage, string> = {
     en: 'English',
     vi: 'Tiếng Việt',
     new_language: 'Native Name'  // Language name in its native form
   };
   ```

5. Add the language to the locale map in `useLocale.ts`
   ```tsx
   const localeMap: Record<SupportedLanguage, string> = {
     en: 'en-US',
     vi: 'vi-VN',
     new_language: 'new_language-COUNTRY'  // e.g., fr-FR
   };
   ```

## Best Practices

1. **Keep translations organized**
   - Group related translations under namespace-like keys
   - Use dot notation for hierarchical organization

2. **Use variables for dynamic content**
   ```tsx
   // In translation file
   {
     "greeting": "Hello, {{name}}!"
   }
   
   // In component
   t('greeting', { name: 'John' })
   ```

3. **Handle pluralization**
   ```tsx
   // In translation file
   {
     "items": "{{count}} item",
     "items_plural": "{{count}} items"
   }
   
   // In component
   t('items', { count: 5 })
   ```

4. **Use formatted components for complex markup**
   ```tsx
   // In translation file
   {
     "welcome": "Welcome to <bold>{{appName}}</bold>"
   }
   
   // In component
   t('welcome', { 
     appName: 'Deep Research Core',
     bold: (chunks) => <strong>{chunks}</strong>
   })
   ```

5. **Extract translations with i18next-scanner**
   ```bash
   npm run extract-translations
   ```

## RTL Support

The implementation includes basic support for right-to-left (RTL) languages through the `isRTL` property in the Language Context. When a RTL language is selected, the `dir` attribute of the HTML document is set to "rtl".

To fully support RTL languages, you may need to adjust your CSS and component layouts. 