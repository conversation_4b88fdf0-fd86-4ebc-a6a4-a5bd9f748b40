import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';

// Import translations
import enTranslations from './locales/en/translation.json';
import viTranslations from './locales/vi/translation.json';

// Configure i18next
i18n
  // Load translations from server (for future dynamic loading)
  .use(Backend)
  // Detect user language
  .use(LanguageDetector)
  // Pass the i18n instance to react-i18next
  .use(initReactI18next)
  // Initialize i18next
  .init({
    debug: process.env.NODE_ENV === 'development',
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false, // React already safes from XSS
    },
    resources: {
      en: {
        translation: enTranslations,
      },
      vi: {
        translation: viTranslations,
      },
    },
    detection: {
      order: ['querystring', 'localStorage', 'navigator', 'htmlTag'],
      lookupQuerystring: 'lang',
      lookupLocalStorage: 'i18nextLng',
      caches: ['localStorage'],
    },
    react: {
      useSuspense: true,
    },
    backend: {
      loadPath: '/locales/{{lng}}/{{ns}}.json',
    },
    load: 'languageOnly', // Only load language code (en) not locale (en-US)
  });

// Export instance for direct use
export default i18n;

// Function to format dates according to the current locale
export const formatDate = (date: Date | string | number, options?: Intl.DateTimeFormatOptions): string => {
  const locale = i18n.language || 'en';
  const dateObj = date instanceof Date ? date : new Date(date);
  
  const formatter = new Intl.DateTimeFormat(
    locale, 
    options || { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    }
  );
  
  return formatter.format(dateObj);
};

// Function to format numbers according to the current locale
export const formatNumber = (number: number, options?: Intl.NumberFormatOptions): string => {
  const locale = i18n.language || 'en';
  
  const formatter = new Intl.NumberFormat(
    locale,
    options || {
      maximumFractionDigits: 2
    }
  );
  
  return formatter.format(number);
};

// Utility hook for changing language
export const useLanguageToggle = () => {
  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
    // Save language preference to localStorage
    localStorage.setItem('i18nextLng', lng);
  };

  const toggleLanguage = () => {
    const currentLanguage = i18n.language;
    const newLanguage = currentLanguage === 'vi' ? 'en' : 'vi';
    changeLanguage(newLanguage);
  };

  return {
    currentLanguage: i18n.language,
    changeLanguage,
    toggleLanguage,
  };
}; 