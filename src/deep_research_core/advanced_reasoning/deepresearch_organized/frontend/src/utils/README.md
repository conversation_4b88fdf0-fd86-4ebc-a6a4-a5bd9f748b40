# Frontend Performance Optimization Utilities

This directory contains utilities for improving the performance of the Deep Research Core UI.

## Overview

We've implemented several utility modules that focus on different aspects of frontend performance:

1. **performance.ts**: Core performance monitoring and optimization utilities
2. **imageOptimization.ts**: Utilities for optimizing images
3. **routeOptimization.ts**: Route-based optimizations and code splitting
4. **cacheOptimization.ts**: Data caching and storage management

## Usage

### Performance Monitoring and Optimization

```tsx
import { 
  useRenderTimer, 
  useRenderCount, 
  useDebounce, 
  useDebouncedValue,
  useThrottle,
  measureExecutionTime,
  PerformanceMonitor
} from '../utils/performance';

// Monitor component render time
const MyComponent = () => {
  // Log render time when component mounts/unmounts
  useRenderTimer('MyComponent');
  
  // Count and log re-renders
  const renderCount = useRenderCount('MyComponent');
  
  // Debounce an expensive event handler
  const handleChange = useDebounce((e) => {
    // Expensive operation
  }, 300);
  
  // Debounce a frequently changing value
  const debouncedValue = useDebouncedValue(value, 300);
  
  // Throttle a handler for frequent events like scrolling
  const handleScroll = useThrottle(() => {
    // Update something based on scroll position
  }, 100);
  
  // Measure performance of a specific operation
  useEffect(() => {
    const cleanup = PerformanceMonitor.markComponentRender('MyComponent');
    return cleanup;
  }, []);
  
  return <div>...</div>;
};

// Measure execution time of any function
const myExpensiveFunction = measureExecutionTime(
  (param1, param2) => {
    // Expensive operation
    return result;
  },
  'myExpensiveFunction'
);
```

### Image Optimization

```tsx
import {
  isImageFormatSupported,
  getBestSupportedFormat,
  generateSrcset,
  generateSizes,
  calculateAspectRatio,
  getOptimalImageSize,
  createBlurPlaceholder,
  getImageUrl
} from '../utils/imageOptimization';

// Determine the best image format based on browser support
useEffect(() => {
  const loadBestFormat = async () => {
    const format = await getBestSupportedFormat();
    console.log(`Using format: ${format}`);
  };
  loadBestFormat();
}, []);

// Generate srcset and sizes for responsive images
const srcset = generateSrcset('/images/', 'hero', 'webp');
const sizes = generateSizes({
  '(max-width: 600px)': '100vw',
  '(max-width: 1024px)': '50vw',
  'default': '33vw'
});

// Create a placeholder while image loads
const placeholder = createBlurPlaceholder(10, 10, '#f0f0f0');

// Calculate the optimal image size based on container
const optimalSize = getOptimalImageSize(containerWidth);
const imageUrl = getImageUrl('/images/', 'hero', optimalSize, 'webp');
```

### Route Optimization

```tsx
import {
  markRouteVisited,
  preloadRoute,
  preloadRelatedRoutes,
  createRouteModuleMap,
  defineRouteGraph,
  shouldPreload
} from '../utils/routeOptimization';

// Define route modules
const routeModules = createRouteModuleMap({
  '/': () => import('../pages/Dashboard'),
  '/documents': () => import('../pages/DocumentsPage'),
  '/query': () => import('../pages/QueryPage'),
  '/settings': () => import('../pages/SettingsPage')
});

// Define related routes for preloading
const routeGraph = defineRouteGraph({
  '/': ['/documents', '/query'],
  '/documents': ['/query'],
  '/query': ['/documents', '/settings']
});

// In your router component
const MyRouter = () => {
  const location = useLocation();
  
  useEffect(() => {
    // Mark the current route as visited
    markRouteVisited(location.pathname);
    
    // Preload related routes if conditions are good
    const preloadRelated = async () => {
      if (await shouldPreload()) {
        preloadRelatedRoutes(location.pathname, routeModules, routeGraph);
      }
    };
    preloadRelated();
  }, [location.pathname]);
  
  return <Routes>...</Routes>;
};
```

### Data Caching

```tsx
import {
  setCacheValue,
  getCacheValue,
  removeCacheValue,
  clearCacheByPrefix,
  clearExpiredCache,
  cacheFunction,
  cacheAsyncFunction
} from '../utils/cacheOptimization';

// Store and retrieve values with expiration
setCacheValue('user-preferences', preferences, { ttl: 86400000 }); // 1 day
const cachedPrefs = getCacheValue('user-preferences');

// Auto-cache function results
const getUser = cacheAsyncFunction(
  async (userId) => {
    const response = await fetch(`/api/users/${userId}`);
    return response.json();
  },
  (userId) => `user-${userId}`,
  { ttl: 300000 } // 5 minutes
);

// Use the cached function
const user = await getUser('123');

// Clear expired cache entries periodically
useEffect(() => {
  const interval = setInterval(() => {
    const cleared = clearExpiredCache();
    console.log(`Cleared ${cleared} expired cache entries`);
  }, 3600000); // 1 hour
  
  return () => clearInterval(interval);
}, []);
```

## Best Practices

### Component Rendering

1. **Use Memoization**: Memoize components, values, and callbacks with `React.memo`, `useMemo`, and `useCallback`.
2. **Avoid Unnecessary Re-renders**: Prevent prop changes that don't affect output.
3. **Virtual Lists**: Use virtualization for long lists with `react-window` or `react-virtualized`.
4. **Optimize Context**: Split contexts by update frequency and keep providers low in the tree.

### Data Fetching and Caching

1. **Cache API Responses**: Use cache utilities to store and reuse API responses.
2. **Fetch on Demand**: Only fetch data when needed and consider preloading.
3. **Implement Stale-While-Revalidate**: Show cached data immediately while fetching fresh data.

### Code Splitting

1. **Route-Based Splitting**: Split code by routes using `React.lazy` and `Suspense`.
2. **Component-Level Splitting**: Lazy-load heavy components that aren't immediately needed.
3. **Preload Critical Routes**: Use route prediction to preload likely destinations.

### Asset Optimization

1. **Optimize Images**: Use WebP or AVIF formats with responsive sizing.
2. **Font Loading**: Use `font-display: swap` and preload critical fonts.
3. **SVG Optimization**: Minimize SVG files and consider inline SVGs for critical icons.

## Performance Monitoring

We've implemented several utilities to monitor performance:

1. **Component Render Timing**: Track how long components take to render.
2. **Re-render Tracking**: Count and log component re-renders.
3. **Function Execution Timing**: Measure how long functions take to execute.

## Future Improvements

1. **Web Worker Offloading**: Move heavy computations to web workers.
2. **Service Worker Caching**: Implement advanced caching with service workers.
3. **Connection-Aware Loading**: Adapt loading strategy based on network conditions.
4. **Priority Hints**: Implement resource priority hints for critical assets.

## References

- [Web Vitals](https://web.dev/vitals/): Google's metrics for measuring user experience
- [React Performance Optimization](https://reactjs.org/docs/optimizing-performance.html): Official React docs
- [MDN Performance](https://developer.mozilla.org/en-US/docs/Web/API/Performance): Browser performance APIs 