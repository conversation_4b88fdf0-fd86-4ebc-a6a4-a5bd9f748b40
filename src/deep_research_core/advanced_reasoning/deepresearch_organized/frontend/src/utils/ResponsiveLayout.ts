/**
 * ResponsiveLayout.ts
 * Advanced utilities for responsive layouts in the application
 */

import { useMediaQuery, useTheme, Theme } from '@mui/material';
import { ReactNode } from 'react';

/**
 * Standard breakpoint sizes for the application
 */
export const breakpoints = {
  xs: 0,
  sm: 600,
  md: 960,
  lg: 1280,
  xl: 1920
};

// Define a simple style type for our needs
type StyleObject = Record<string, string | number>;

/**
 * Interface for responsive grid item props
 */
export interface ResponsiveGridItemProps {
  xs?: number; // 0-12 columns for extra small screens
  sm?: number; // 0-12 columns for small screens
  md?: number; // 0-12 columns for medium screens
  lg?: number; // 0-12 columns for large screens
  xl?: number; // 0-12 columns for extra large screens
  children: ReactNode;
  className?: string;
  style?: StyleObject;
}

/**
 * Interface for responsive grid container props
 */
export interface ResponsiveGridContainerProps {
  spacing?: number | {xs?: number, sm?: number, md?: number, lg?: number, xl?: number};
  children: ReactNode;
  className?: string;
  style?: StyleObject;
  direction?: 'row' | 'column' | 'row-reverse' | 'column-reverse';
  justifyContent?: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly';
  alignItems?: 'flex-start' | 'flex-end' | 'center' | 'stretch' | 'baseline';
  wrap?: 'nowrap' | 'wrap' | 'wrap-reverse';
}

/**
 * Gets the CSS grid template columns based on the current breakpoint
 * @param theme - The MUI theme
 * @returns CSS grid-template-columns property value
 */
export const getResponsiveGridTemplate = (theme: Theme): string => {
  const { breakpoints } = theme;
  const currentWidth = window.innerWidth;

  if (currentWidth < breakpoints.values.sm) return 'repeat(1, 1fr)';
  if (currentWidth < breakpoints.values.md) return 'repeat(2, 1fr)';
  if (currentWidth < breakpoints.values.lg) return 'repeat(3, 1fr)';
  if (currentWidth < breakpoints.values.xl) return 'repeat(4, 1fr)';
  return 'repeat(4, 1fr)';
};

/**
 * Custom hook that returns responsive padding values based on the current breakpoint
 * @returns Object with padding values for different screen sizes
 */
export function useResponsivePadding() {
  const theme = useTheme();
  const isXs = useMediaQuery(theme.breakpoints.only('xs'));
  const isSm = useMediaQuery(theme.breakpoints.only('sm'));
  const isMd = useMediaQuery(theme.breakpoints.only('md'));
  
  return {
    container: {
      padding: isXs ? theme.spacing(1) : 
               isSm ? theme.spacing(2) : 
               isMd ? theme.spacing(3) : theme.spacing(4)
    },
    section: {
      padding: isXs ? theme.spacing(1) : 
               isSm ? theme.spacing(1.5) : 
               isMd ? theme.spacing(2) : theme.spacing(3)
    },
    card: {
      padding: isXs ? theme.spacing(1) : 
               isSm ? theme.spacing(1.5) : theme.spacing(2)
    }
  };
}

/**
 * Custom hook that returns responsive font sizes based on the current breakpoint
 * @returns Object with font sizes for different elements and screen sizes
 */
export function useResponsiveFontSizes() {
  const theme = useTheme();
  const isXs = useMediaQuery(theme.breakpoints.only('xs'));
  const isSm = useMediaQuery(theme.breakpoints.only('sm'));
  
  return {
    h1: {
      fontSize: isXs ? '1.75rem' : 
               isSm ? '2rem' : '2.5rem'
    },
    h2: {
      fontSize: isXs ? '1.5rem' : 
               isSm ? '1.75rem' : '2rem'
    },
    h3: {
      fontSize: isXs ? '1.25rem' : 
               isSm ? '1.5rem' : '1.75rem'
    },
    body1: {
      fontSize: isXs ? '0.875rem' : 
               isSm ? '0.9375rem' : '1rem'
    },
    body2: {
      fontSize: isXs ? '0.8125rem' : 
               isSm ? '0.875rem' : '0.9375rem'
    }
  };
}

/**
 * Custom hook that returns responsive margin values based on the current breakpoint
 * @returns Object with margin values for different screen sizes
 */
export function useResponsiveMargins() {
  const theme = useTheme();
  const isXs = useMediaQuery(theme.breakpoints.only('xs'));
  const isSm = useMediaQuery(theme.breakpoints.only('sm'));
  const isMd = useMediaQuery(theme.breakpoints.only('md'));
  
  return {
    section: {
      marginBottom: isXs ? theme.spacing(2) : 
                   isSm ? theme.spacing(3) : 
                   isMd ? theme.spacing(4) : theme.spacing(5)
    },
    component: {
      marginBottom: isXs ? theme.spacing(1.5) : 
                   isSm ? theme.spacing(2) : 
                   isMd ? theme.spacing(2.5) : theme.spacing(3)
    },
    element: {
      marginBottom: isXs ? theme.spacing(1) : 
                   isSm ? theme.spacing(1.5) : theme.spacing(2)
    }
  };
}

/**
 * Helper function to calculate grid column width based on breakpoint
 * @param props - The responsive grid item props
 * @param breakpoint - Current breakpoint name
 * @returns CSS grid column width
 */
export const calculateGridColumnWidth = (
  props: ResponsiveGridItemProps,
  breakpoint: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
): string => {
  const value = props[breakpoint] || 12;
  return `span ${value} / span ${value}`;
};

/**
 * Utility to generate responsive styles for an element
 * @param theme - The MUI theme
 * @param stylesByBreakpoint - Object containing styles for each breakpoint
 * @returns A complete style object with common styles
 */
export const generateResponsiveStyles = (
  theme: Theme,
  stylesByBreakpoint: {
    xs?: StyleObject;
    sm?: StyleObject;
    md?: StyleObject;
    lg?: StyleObject;
    xl?: StyleObject;
    default: StyleObject;
  }
): StyleObject => {
  // Start with default styles
  return { ...stylesByBreakpoint.default };
  // Media query styles will be applied by MUI's useMediaQuery in components
};

export default {
  useResponsivePadding,
  useResponsiveFontSizes,
  useResponsiveMargins,
  getResponsiveGridTemplate,
  calculateGridColumnWidth,
  generateResponsiveStyles,
  breakpoints
}; 