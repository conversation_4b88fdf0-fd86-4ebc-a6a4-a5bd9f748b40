/**
 * Performance optimization utilities for the Deep Research Core UI
 */
import { useEffect, useRef, useCallback, useState } from 'react';

/**
 * Custom hook to measure component render time
 * @param componentName - Name of the component to measure
 * @param enabled - Whether to enable the measurement
 * @returns void
 */
export const useRenderTimer = (componentName: string, enabled: boolean = true): void => {
  const startTimeRef = useRef<number>(0);
  
  useEffect(() => {
    if (!enabled) return;
    
    // Record start time on mount
    startTimeRef.current = performance.now();
    
    return () => {
      // Log render time on unmount
      const endTime = performance.now();
      const renderTime = endTime - startTimeRef.current;
      console.log(`[Performance] ${componentName} rendered in ${renderTime.toFixed(2)}ms`);
    };
  }, [componentName, enabled]);
};

/**
 * Custom hook to track and report component re-renders
 * @param componentName - Name of the component to track
 * @param enabled - Whether to enable tracking
 * @returns Render count
 */
export const useRenderCount = (componentName: string, enabled: boolean = true): number => {
  const renderCount = useRef<number>(0);
  
  useEffect(() => {
    if (!enabled) return;
    
    renderCount.current += 1;
    console.log(`[Performance] ${componentName} re-rendered ${renderCount.current} times`);
  });
  
  return renderCount.current;
};

/**
 * Custom hook to debounce a function
 * @param callback - Function to debounce
 * @param delay - Delay in milliseconds
 * @returns Debounced function
 */
export const useDebounce = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  const debouncedCallback = useCallback(
    (...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        callback(...args);
      }, delay);
    },
    [callback, delay]
  );
  
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
  
  return debouncedCallback;
};

/**
 * Custom hook to debounce a value
 * @param value - Value to debounce
 * @param delay - Delay in milliseconds
 * @returns Debounced value
 */
export const useDebouncedValue = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    
    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);
  
  return debouncedValue;
};

/**
 * Custom hook to throttle a function
 * @param callback - Function to throttle
 * @param delay - Delay in milliseconds
 * @returns Throttled function
 */
export const useThrottle = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  const lastCalledRef = useRef<number>(0);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  const throttledCallback = useCallback(
    (...args: Parameters<T>) => {
      const now = Date.now();
      const timeSinceLastCall = now - lastCalledRef.current;
      
      if (timeSinceLastCall >= delay) {
        // If enough time has passed, call the function immediately
        lastCalledRef.current = now;
        callback(...args);
      } else {
        // Otherwise, schedule it to run at the end of the delay period
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
        
        timeoutRef.current = setTimeout(() => {
          lastCalledRef.current = Date.now();
          callback(...args);
        }, delay - timeSinceLastCall);
      }
    },
    [callback, delay]
  );
  
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
  
  return throttledCallback;
};

/**
 * Measure execution time of a function
 * @param fn - Function to measure
 * @param fnName - Name of the function (for logging)
 * @returns Wrapped function that logs execution time
 */
export function measureExecutionTime<T extends (...args: any[]) => any>(
  fn: T,
  fnName: string
): (...args: Parameters<T>) => ReturnType<T> {
  return (...args: Parameters<T>): ReturnType<T> => {
    const start = performance.now();
    const result = fn(...args);
    
    if (result instanceof Promise) {
      return result.finally(() => {
        const end = performance.now();
        console.log(`[Performance] ${fnName} took ${(end - start).toFixed(2)}ms to execute (async)`);
      }) as ReturnType<T>;
    } else {
      const end = performance.now();
      console.log(`[Performance] ${fnName} took ${(end - start).toFixed(2)}ms to execute`);
      return result;
    }
  };
}

/**
 * Performance monitoring for React components
 */
export const PerformanceMonitor = {
  markComponentRender: (componentName: string) => {
    performance.mark(`${componentName}-render-start`);
    return () => {
      performance.mark(`${componentName}-render-end`);
      performance.measure(
        `${componentName}-render-time`,
        `${componentName}-render-start`,
        `${componentName}-render-end`
      );
      
      const entries = performance.getEntriesByName(`${componentName}-render-time`);
      if (entries.length > 0) {
        console.log(`[Performance] ${componentName} rendered in ${entries[0].duration.toFixed(2)}ms`);
      }
      
      // Clean up
      performance.clearMarks(`${componentName}-render-start`);
      performance.clearMarks(`${componentName}-render-end`);
      performance.clearMeasures(`${componentName}-render-time`);
    };
  },
  
  logSlowRendering: (componentName: string, threshold: number = 16) => {
    return (duration: number) => {
      if (duration > threshold) {
        console.warn(`[Performance] Slow rendering detected: ${componentName} took ${duration.toFixed(2)}ms (> ${threshold}ms)`);
      }
    };
  }
};

/**
 * Utility to check if a component is being rendered on the server
 */
export const isServer = () => typeof window === 'undefined';

/**
 * Utility to check if intersection observer is supported
 */
export const isIntersectionObserverSupported = () => 
  typeof window !== 'undefined' && 'IntersectionObserver' in window;

/**
 * Custom hook for implementing lazy loading of images or other components
 * @param options - Intersection observer options
 * @returns [ref, isInView] - Reference to attach to element and whether element is in view
 */
export const useInView = (options: IntersectionObserverInit = { threshold: 0.1 }) => {
  const [isInView, setIsInView] = useState(false);
  const ref = useRef<Element | null>(null);
  
  useEffect(() => {
    if (!isIntersectionObserverSupported() || !ref.current) {
      // If IntersectionObserver is not supported, assume element is in view
      setIsInView(true);
      return;
    }
    
    const observer = new IntersectionObserver(([entry]) => {
      setIsInView(entry.isIntersecting);
    }, options);
    
    observer.observe(ref.current);
    
    return () => {
      if (ref.current) {
        observer.unobserve(ref.current);
      }
    };
  }, [options]);
  
  return [ref, isInView] as const;
};

export default {
  useRenderTimer,
  useRenderCount,
  useDebounce,
  useDebouncedValue,
  useThrottle,
  measureExecutionTime,
  PerformanceMonitor,
  isServer,
  isIntersectionObserverSupported,
  useInView
}; 