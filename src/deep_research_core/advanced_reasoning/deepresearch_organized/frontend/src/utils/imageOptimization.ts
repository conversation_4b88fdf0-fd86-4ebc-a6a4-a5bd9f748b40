/**
 * Image optimization utilities for Deep Research Core UI
 */

/**
 * Check if an image format is supported in the current browser
 * @param format - Image format to check (webp, avif, etc.)
 * @returns Promise that resolves to true if supported, false otherwise
 */
export const isImageFormatSupported = (format: string): Promise<boolean> => {
  return new Promise((resolve) => {
    const image = new Image();
    image.onload = () => resolve(true);
    image.onerror = () => resolve(false);
    image.src = `data:image/${format};base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=`;
  });
};

/**
 * Get the most optimized image format supported by the browser
 * @returns Promise that resolves to the best supported format
 */
export const getBestSupportedFormat = async (): Promise<string> => {
  const formats = ['avif', 'webp', 'jpg'];
  
  for (const format of formats) {
    if (await isImageFormatSupported(format)) {
      return format;
    }
  }
  
  return 'jpg'; // Fallback format
};

/**
 * Generate srcset for responsive images
 * @param basePath - Base path of the image
 * @param filename - Filename without extension
 * @param format - Image format
 * @param widths - Array of widths to include in srcset
 * @returns srcset attribute string
 */
export const generateSrcset = (
  basePath: string,
  filename: string,
  format: string,
  widths: number[] = [320, 640, 960, 1280, 1920]
): string => {
  return widths
    .map((width) => `${basePath}${filename}-${width}.${format} ${width}w`)
    .join(', ');
};

/**
 * Generate sizes attribute for responsive images
 * @param breakpoints - Map of breakpoints to sizes
 * @returns sizes attribute string
 */
export const generateSizes = (
  breakpoints: Record<string, string> = {
    '(max-width: 600px)': '100vw',
    '(max-width: 1024px)': '50vw',
    'default': '33vw'
  }
): string => {
  return Object.entries(breakpoints)
    .map(([breakpoint, size]) => 
      breakpoint === 'default' ? size : `${breakpoint} ${size}`
    )
    .join(', ');
};

/**
 * Calculate the aspect ratio based on width and height
 * @param width - Width of the image
 * @param height - Height of the image
 * @returns CSS aspect-ratio value
 */
export const calculateAspectRatio = (width: number, height: number): string => {
  return `${width} / ${height}`;
};

/**
 * Calculate the appropriate image size to load based on device pixel ratio and container size
 * @param containerWidth - Width of the container in pixels
 * @param pixelRatio - Device pixel ratio (defaults to window.devicePixelRatio or 1)
 * @param availableSizes - Array of available image sizes
 * @returns The optimal image size to load
 */
export const getOptimalImageSize = (
  containerWidth: number, 
  pixelRatio: number = typeof window !== 'undefined' ? window.devicePixelRatio || 1 : 1,
  availableSizes: number[] = [320, 640, 960, 1280, 1920]
): number => {
  const targetWidth = containerWidth * pixelRatio;
  
  // Find the smallest size that is larger than the target
  const optimalSize = availableSizes.find(size => size >= targetWidth);
  
  // If no size is large enough, use the largest available
  return optimalSize || availableSizes[availableSizes.length - 1];
};

/**
 * Create a blurred data URL placeholder for images
 * @param width - Width of the placeholder
 * @param height - Height of the placeholder
 * @param color - Base color (hex or rgb)
 * @returns Data URL for the placeholder
 */
export const createBlurPlaceholder = (
  width: number = 10,
  height: number = 10,
  color: string = '#e2e8f0'
): string => {
  // This would normally be implemented with Canvas, but for simplicity,
  // we'll return a data URL for a solid color
  return `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 ${width} ${height}'%3E%3Crect width='${width}' height='${height}' fill='${encodeURIComponent(color)}'/%3E%3C/svg%3E`;
};

/**
 * Generate URL for an image with the specified parameters
 * @param basePath - Base path of the image
 * @param filename - Filename without extension
 * @param width - Width of the image
 * @param format - Image format
 * @param quality - Quality of the image (0-100)
 * @returns Full image URL
 */
export const getImageUrl = (
  basePath: string,
  filename: string,
  width: number,
  format: string = 'jpg',
  quality: number = 90
): string => {
  return `${basePath}${filename}-${width}.${format}?q=${quality}`;
};

/**
 * Check if the image is already in the viewport
 * @param imageElement - Image element to check
 * @returns True if the image is in the viewport
 */
export const isImageInViewport = (imageElement: HTMLImageElement): boolean => {
  if (typeof window === 'undefined' || !imageElement) return false;

  const rect = imageElement.getBoundingClientRect();
  
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  );
};

export default {
  isImageFormatSupported,
  getBestSupportedFormat,
  generateSrcset,
  generateSizes,
  calculateAspectRatio,
  getOptimalImageSize,
  createBlurPlaceholder,
  getImageUrl,
  isImageInViewport
}; 