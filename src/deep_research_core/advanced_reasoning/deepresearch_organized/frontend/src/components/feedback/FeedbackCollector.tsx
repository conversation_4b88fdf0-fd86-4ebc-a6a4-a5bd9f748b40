import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typo<PERSON>,
  <PERSON>ing,
  TextField,
  Button,
  Chip,
  Di<PERSON>r,
  <PERSON>nack<PERSON>,
  Alert,
  FormControlLabel,
  Switch,
  <PERSON>lider,
  IconButton,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Lightbulb as LightbulbIcon,
  Send as SendIcon,
  ThumbUp as ThumbUpIcon,
  ThumbDown as ThumbDownIcon,
  Check as CheckIcon,
  Close as CloseIcon
} from '@mui/icons-material';

// Define the feedback data structure
export interface FeedbackData {
  overallRating: number;
  detailedRatings: {
    accuracy: number;
    clarity: number;
    relevance: number;
    reasoning: number;
    speed: number;
  };
  feedbackText: string;
  factualErrors: boolean;
  keyInsights: string[];
  suggestionType: 'improvement' | 'error' | 'praise' | 'other';
}

// Initial state for feedback form
const initialFeedbackState: FeedbackData = {
  overallRating: 0,
  detailedRatings: {
    accuracy: 0,
    clarity: 0,
    relevance: 0,
    reasoning: 0,
    speed: 0
  },
  feedbackText: '',
  factualErrors: false,
  keyInsights: [],
  suggestionType: 'other'
};

interface FeedbackCollectorProps {
  queryId: string;
  query: string;
  model: string;
  reasoningMethod: string;
  onSubmitFeedback: (queryId: string, feedback: FeedbackData) => Promise<void>;
  compact?: boolean;
}

const FeedbackCollector: React.FC<FeedbackCollectorProps> = ({
  queryId,
  query,
  model,
  reasoningMethod,
  onSubmitFeedback,
  compact = false
}) => {
  // State
  const [feedback, setFeedback] = useState<FeedbackData>(initialFeedbackState);
  const [expandedForm, setExpandedForm] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentInsight, setCurrentInsight] = useState('');

  // Handle rating change
  const handleRatingChange = (category: keyof FeedbackData | keyof FeedbackData['detailedRatings'], value: number) => {
    if (category === 'overallRating') {
      setFeedback(prev => ({ ...prev, overallRating: value }));
    } else {
      setFeedback(prev => ({
        ...prev,
        detailedRatings: {
          ...prev.detailedRatings,
          [category]: value
        }
      }));
    }
  };

  // Handle text feedback change
  const handleTextChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFeedback(prev => ({ ...prev, feedbackText: e.target.value }));
  };

  // Handle factual errors toggle
  const handleFactualErrorsToggle = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFeedback(prev => ({ ...prev, factualErrors: e.target.checked }));
  };

  // Handle adding insights
  const handleAddInsight = () => {
    if (currentInsight.trim()) {
      setFeedback(prev => ({
        ...prev,
        keyInsights: [...prev.keyInsights, currentInsight.trim()]
      }));
      setCurrentInsight('');
    }
  };

  // Handle removing an insight
  const handleRemoveInsight = (index: number) => {
    setFeedback(prev => ({
      ...prev,
      keyInsights: prev.keyInsights.filter((_, i) => i !== index)
    }));
  };

  // Handle suggestion type selection
  const handleSuggestionTypeChange = (type: FeedbackData['suggestionType']) => {
    setFeedback(prev => ({ ...prev, suggestionType: type }));
  };

  // Submit feedback
  const handleSubmit = async () => {
    try {
      setSubmitting(true);
      setError(null);
      
      await onSubmitFeedback(queryId, feedback);
      
      setSuccess(true);
      // Reset form after successful submission
      setFeedback(initialFeedbackState);
      setExpandedForm(false);
    } catch (err) {
      setError((err as Error).message || 'Failed to submit feedback');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle quick thumbs up/down
  const handleQuickFeedback = async (isPositive: boolean) => {
    try {
      setSubmitting(true);
      setError(null);
      
      const quickFeedback: FeedbackData = {
        ...initialFeedbackState,
        overallRating: isPositive ? 4 : 2,
        feedbackText: isPositive ? 'Good response' : 'Need improvement'
      };
      
      await onSubmitFeedback(queryId, quickFeedback);
      
      setSuccess(true);
    } catch (err) {
      setError((err as Error).message || 'Failed to submit feedback');
    } finally {
      setSubmitting(false);
    }
  };

  // Close success alert
  const handleCloseSuccess = () => {
    setSuccess(false);
  };

  // Close error alert
  const handleCloseError = () => {
    setError(null);
  };

  // Compact version (just thumbs up/down)
  if (compact && !expandedForm) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 2 }}>
        <Typography variant="body2">Was this response helpful?</Typography>
        
        <Tooltip title="Yes, this was helpful">
          <IconButton 
            color="primary" 
            onClick={() => handleQuickFeedback(true)}
            disabled={submitting}
          >
            <ThumbUpIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="No, needs improvement">
          <IconButton 
            color="error" 
            onClick={() => handleQuickFeedback(false)}
            disabled={submitting}
          >
            <ThumbDownIcon />
          </IconButton>
        </Tooltip>
        
        <Button 
          size="small" 
          onClick={() => setExpandedForm(true)}
          disabled={submitting}
        >
          Detailed Feedback
        </Button>
        
        <Snackbar open={success} autoHideDuration={3000} onClose={handleCloseSuccess}>
          <Alert severity="success" onClose={handleCloseSuccess}>
            Thank you for your feedback!
          </Alert>
        </Snackbar>
        
        <Snackbar open={!!error} autoHideDuration={5000} onClose={handleCloseError}>
          <Alert severity="error" onClose={handleCloseError}>
            {error}
          </Alert>
        </Snackbar>
      </Box>
    );
  }

  // Full feedback form
  return (
    <Paper elevation={3} sx={{ p: 3, mt: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <LightbulbIcon color="primary" sx={{ mr: 1 }} />
        <Typography variant="h6">
          Provide Feedback
        </Typography>
      </Box>
      
      <Divider sx={{ mb: 2 }} />
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle1" gutterBottom>
          How would you rate this response?
        </Typography>
        <Rating
          name="overall-rating"
          value={feedback.overallRating}
          onChange={(_, value) => handleRatingChange('overallRating', value || 0)}
          precision={0.5}
          size="large"
          disabled={submitting}
        />
      </Box>
      
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography>Detailed Ratings</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Box>
              <Typography gutterBottom>Accuracy</Typography>
              <Rating
                name="accuracy-rating"
                value={feedback.detailedRatings.accuracy}
                onChange={(_, value) => handleRatingChange('accuracy', value || 0)}
                disabled={submitting}
              />
            </Box>
            
            <Box>
              <Typography gutterBottom>Clarity</Typography>
              <Rating
                name="clarity-rating"
                value={feedback.detailedRatings.clarity}
                onChange={(_, value) => handleRatingChange('clarity', value || 0)}
                disabled={submitting}
              />
            </Box>
            
            <Box>
              <Typography gutterBottom>Relevance</Typography>
              <Rating
                name="relevance-rating"
                value={feedback.detailedRatings.relevance}
                onChange={(_, value) => handleRatingChange('relevance', value || 0)}
                disabled={submitting}
              />
            </Box>
            
            <Box>
              <Typography gutterBottom>Reasoning</Typography>
              <Rating
                name="reasoning-rating"
                value={feedback.detailedRatings.reasoning}
                onChange={(_, value) => handleRatingChange('reasoning', value || 0)}
                disabled={submitting}
              />
            </Box>
            
            <Box>
              <Typography gutterBottom>Response Speed</Typography>
              <Rating
                name="speed-rating"
                value={feedback.detailedRatings.speed}
                onChange={(_, value) => handleRatingChange('speed', value || 0)}
                disabled={submitting}
              />
            </Box>
          </Box>
        </AccordionDetails>
      </Accordion>
      
      <Box sx={{ mt: 3 }}>
        <FormControlLabel
          control={
            <Switch
              checked={feedback.factualErrors}
              onChange={handleFactualErrorsToggle}
              disabled={submitting}
            />
          }
          label="Did you notice any factual errors?"
        />
      </Box>
      
      <Box sx={{ mt: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          What type of feedback are you providing?
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <Chip
            label="Suggestion for improvement"
            onClick={() => handleSuggestionTypeChange('improvement')}
            color={feedback.suggestionType === 'improvement' ? 'primary' : 'default'}
            disabled={submitting}
          />
          <Chip
            label="Error report"
            onClick={() => handleSuggestionTypeChange('error')}
            color={feedback.suggestionType === 'error' ? 'primary' : 'default'}
            disabled={submitting}
          />
          <Chip
            label="Praise"
            onClick={() => handleSuggestionTypeChange('praise')}
            color={feedback.suggestionType === 'praise' ? 'primary' : 'default'}
            disabled={submitting}
          />
          <Chip
            label="Other"
            onClick={() => handleSuggestionTypeChange('other')}
            color={feedback.suggestionType === 'other' ? 'primary' : 'default'}
            disabled={submitting}
          />
        </Box>
      </Box>
      
      <Box sx={{ mt: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Additional Comments
        </Typography>
        <TextField
          multiline
          rows={4}
          fullWidth
          placeholder="Please share your thoughts about the response..."
          value={feedback.feedbackText}
          onChange={handleTextChange}
          disabled={submitting}
        />
      </Box>
      
      <Box sx={{ mt: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Key Insights (Optional)
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          Add any key insights or points you found particularly valuable
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
          <TextField
            fullWidth
            placeholder="Add an insight..."
            value={currentInsight}
            onChange={(e) => setCurrentInsight(e.target.value)}
            disabled={submitting}
          />
          <Button
            variant="outlined"
            onClick={handleAddInsight}
            disabled={!currentInsight.trim() || submitting}
          >
            Add
          </Button>
        </Box>
        
        {feedback.keyInsights.length > 0 && (
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {feedback.keyInsights.map((insight, index) => (
              <Chip
                key={index}
                label={insight}
                onDelete={() => handleRemoveInsight(index)}
                disabled={submitting}
              />
            ))}
          </Box>
        )}
      </Box>
      
      <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
        {compact && (
          <Button 
            variant="outlined" 
            onClick={() => setExpandedForm(false)}
            disabled={submitting}
          >
            Simple View
          </Button>
        )}
        
        <Box sx={{ flex: 1 }} />
        
        <Button
          variant="contained"
          color="primary"
          endIcon={<SendIcon />}
          onClick={handleSubmit}
          disabled={feedback.overallRating === 0 || submitting}
        >
          {submitting ? 'Submitting...' : 'Submit Feedback'}
        </Button>
      </Box>
      
      <Snackbar open={success} autoHideDuration={3000} onClose={handleCloseSuccess}>
        <Alert severity="success" onClose={handleCloseSuccess}>
          Thank you for your feedback!
        </Alert>
      </Snackbar>
      
      <Snackbar open={!!error} autoHideDuration={5000} onClose={handleCloseError}>
        <Alert severity="error" onClose={handleCloseError}>
          {error}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

export default FeedbackCollector; 