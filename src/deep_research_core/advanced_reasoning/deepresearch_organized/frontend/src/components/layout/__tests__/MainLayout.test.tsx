import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import MainLayout from '../MainLayout';

// Mock the child components
jest.mock('../AppHeader', () => () => <div data-testid="mock-app-header">App Header</div>);
jest.mock('../Sidebar', () => () => <div data-testid="mock-sidebar">Sidebar</div>);
jest.mock('../Footer', () => () => <div data-testid="mock-footer">Footer</div>);

describe('MainLayout Component', () => {
  const renderWithTheme = (ui: React.ReactElement) => {
    const theme = createTheme();
    return render(
      <ThemeProvider theme={theme}>
        {ui}
      </ThemeProvider>
    );
  };

  test('renders children content', () => {
    renderWithTheme(
      <MainLayout title="Test Layout">
        <div data-testid="test-content">Test Content</div>
      </MainLayout>
    );
    
    expect(screen.getByTestId('test-content')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  test('passes title to AppHeader', () => {
    renderWithTheme(
      <MainLayout title="Test Layout">
        <div>Content</div>
      </MainLayout>
    );
    
    expect(screen.getByTestId('mock-app-header')).toBeInTheDocument();
  });

  test('renders Sidebar by default', () => {
    renderWithTheme(
      <MainLayout title="Test Layout">
        <div>Content</div>
      </MainLayout>
    );
    
    expect(screen.getByTestId('mock-sidebar')).toBeInTheDocument();
  });

  test('does not render Sidebar when showSidebar is false', () => {
    renderWithTheme(
      <MainLayout title="Test Layout" showSidebar={false}>
        <div>Content</div>
      </MainLayout>
    );
    
    expect(screen.queryByTestId('mock-sidebar')).not.toBeInTheDocument();
  });

  test('renders Footer component', () => {
    renderWithTheme(
      <MainLayout title="Test Layout">
        <div>Content</div>
      </MainLayout>
    );
    
    expect(screen.getByTestId('mock-footer')).toBeInTheDocument();
  });

  test('has proper content structure', () => {
    renderWithTheme(
      <MainLayout title="Test Layout">
        <div>Content</div>
      </MainLayout>
    );
    
    // Check for main content container
    const mainContent = screen.getByRole('main');
    expect(mainContent).toBeInTheDocument();
  });
}); 