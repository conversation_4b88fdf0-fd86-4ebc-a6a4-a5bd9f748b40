import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import Footer from '../Footer';

describe('Footer Component', () => {
  const renderWithTheme = (ui: React.ReactElement) => {
    const theme = createTheme();
    return render(
      <ThemeProvider theme={theme}>
        {ui}
      </ThemeProvider>
    );
  };

  test('renders the footer container', () => {
    renderWithTheme(<Footer />);
    
    const footer = screen.getByRole('contentinfo');
    expect(footer).toBeInTheDocument();
  });

  test('displays copyright information', () => {
    renderWithTheme(<Footer />);
    
    // Look for copyright symbol and year
    expect(screen.getByText(/©/)).toBeInTheDocument();
    expect(screen.getByText(new RegExp(new Date().getFullYear().toString()))).toBeInTheDocument();
  });

  test('includes Deep Research Core branding', () => {
    renderWithTheme(<Footer />);
    
    expect(screen.getByText(/Deep Research Core/i)).toBeInTheDocument();
  });

  test('links have correct attributes', () => {
    renderWithTheme(<Footer />);
    
    // Get all links in the footer
    const links = screen.getAllByRole('link');
    
    // Verify links have proper attributes
    links.forEach(link => {
      expect(link).toHaveAttribute('href');
      expect(link).toHaveAttribute('target', '_blank');
      expect(link).toHaveAttribute('rel', 'noopener noreferrer');
    });
  });

  test('has proper spacing and styling', () => {
    renderWithTheme(<Footer />);
    
    const footer = screen.getByRole('contentinfo');
    expect(footer).toHaveStyle({ padding: expect.stringContaining('px') });
  });

  test('renders copyright text', () => {
    renderWithTheme(<Footer />);
    expect(screen.getByText(/copyright/i)).toBeInTheDocument();
  });

  test('displays the current year in copyright text', () => {
    const currentYear = new Date().getFullYear();
    renderWithTheme(<Footer />);
    expect(screen.getByText(new RegExp(currentYear.toString()))).toBeInTheDocument();
  });

  test('shows links section', () => {
    renderWithTheme(<Footer />);
    expect(screen.getByText(/links/i)).toBeInTheDocument();
  });

  test('contains link to documentation', () => {
    renderWithTheme(<Footer />);
    const documentationLink = screen.getByText(/documentation/i);
    expect(documentationLink).toBeInTheDocument();
    expect(documentationLink.closest('a')).toHaveAttribute('href');
  });

  test('contains link to github repository', () => {
    renderWithTheme(<Footer />);
    const githubLink = screen.getByText(/github/i);
    expect(githubLink).toBeInTheDocument();
    expect(githubLink.closest('a')).toHaveAttribute('href');
  });

  test('shows version information', () => {
    renderWithTheme(<Footer />);
    expect(screen.getByText(/version/i)).toBeInTheDocument();
  });
}); 