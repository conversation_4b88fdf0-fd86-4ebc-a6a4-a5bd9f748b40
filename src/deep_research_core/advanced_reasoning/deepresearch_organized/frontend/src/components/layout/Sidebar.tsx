import React from 'react';
import {
  Box,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  Typography,
  useTheme
} from '@mui/material';
import {
  Home as HomeIcon,
  Search as SearchIcon,
  Description as DocumentIcon,
  Timeline as VisualizationIcon,
  Settings as SettingsIcon,
  BarChart as AnalyticsIcon,
  DeviceHub as ResponsiveIcon
} from '@mui/icons-material';
import { useLocation, Link } from 'react-router-dom';

// Navigation items configuration
const navItems = [
  { text: 'Dashboard', icon: <HomeIcon />, path: '/' },
  { text: 'Query Interface', icon: <SearchIcon />, path: '/query' },
  { text: 'Documents', icon: <DocumentIcon />, path: '/documents' },
  { text: 'Visualizations', icon: <VisualizationIcon />, path: '/visualizations' },
  { text: 'Analytics', icon: <AnalyticsIcon />, path: '/analytics' },
  { text: 'Responsive Test', icon: <ResponsiveIcon />, path: '/responsive-test' },
  { text: 'Settings', icon: <SettingsIcon />, path: '/settings' },
];

interface SidebarProps {
  width?: number;
  variant?: 'permanent' | 'persistent' | 'temporary';
  open?: boolean;
  onClose?: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  width = 240,
  variant = 'permanent',
  open = true,
  onClose
}) => {
  const theme = useTheme();
  const location = useLocation();
  
  const content = (
    <>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          padding: theme.spacing(2),
          height: 64,
        }}
      >
        <Typography variant="h6" component="div">
          Navigation
        </Typography>
      </Box>
      
      <Divider />
      
      <List sx={{ pt: 1 }}>
        {navItems.map((item) => (
          <ListItem key={item.text} disablePadding>
            <ListItemButton
              component={Link}
              to={item.path}
              selected={location.pathname === item.path}
              sx={{
                '&.Mui-selected': {
                  backgroundColor: 'rgba(74, 85, 189, 0.1)',
                  borderRight: `3px solid ${theme.palette.primary.main}`,
                  '&:hover': {
                    backgroundColor: 'rgba(74, 85, 189, 0.15)',
                  },
                },
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.04)',
                },
              }}
            >
              <ListItemIcon
                sx={{
                  minWidth: 40,
                  color: location.pathname === item.path ? theme.palette.primary.main : 'inherit',
                }}
              >
                {item.icon}
              </ListItemIcon>
              <ListItemText primary={item.text} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </>
  );

  return (
    <Box
      component="nav"
      sx={{ width: { md: width }, flexShrink: { md: 0 } }}
    >
      {/* Mobile version (drawer) */}
      <Drawer
        variant={variant}
        open={open}
        onClose={onClose}
        ModalProps={{ keepMounted: true }}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': { 
            boxSizing: 'border-box', 
            width: width,
            backgroundColor: theme.palette.background.default,
          },
        }}
      >
        {content}
      </Drawer>
      
      {/* Desktop version (permanent drawer) */}
      <Drawer
        variant="permanent"
        sx={{
          display: { xs: 'none', md: 'block' },
          '& .MuiDrawer-paper': { 
            boxSizing: 'border-box', 
            width: width,
            backgroundColor: theme.palette.background.default,
            borderRight: `1px solid ${theme.palette.divider}`,
          },
        }}
        open
      >
        {content}
      </Drawer>
    </Box>
  );
};

export default Sidebar; 