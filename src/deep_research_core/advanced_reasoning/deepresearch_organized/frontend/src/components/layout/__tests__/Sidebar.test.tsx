import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { MemoryRouter } from 'react-router-dom';
import Sidebar from '../Sidebar';

describe('Sidebar Component', () => {
  const renderWithTheme = (ui: React.ReactElement) => {
    const theme = createTheme();
    return render(
      <ThemeProvider theme={theme}>
        <MemoryRouter>
          {ui}
        </MemoryRouter>
      </ThemeProvider>
    );
  };

  test('renders sidebar when open', () => {
    renderWithTheme(<Sidebar open={true} onClose={() => {}} />);
    const drawer = screen.getByRole('presentation');
    expect(drawer).toBeInTheDocument();
  });

  test('displays application title', () => {
    renderWithTheme(<Sidebar open={true} onClose={() => {}} />);
    expect(screen.getByText(/deep research/i)).toBeInTheDocument();
  });

  test('contains navigation links', () => {
    renderWithTheme(<Sidebar open={true} onClose={() => {}} />);
    expect(screen.getByText(/dashboard/i)).toBeInTheDocument();
    expect(screen.getByText(/documents/i)).toBeInTheDocument();
    expect(screen.getByText(/reasoning/i)).toBeInTheDocument();
  });

  test('calls onClose when close button is clicked', () => {
    const handleClose = jest.fn();
    renderWithTheme(<Sidebar open={true} onClose={handleClose} />);
    
    const closeButton = screen.getByRole('button', { name: /close drawer/i });
    fireEvent.click(closeButton);
    
    expect(handleClose).toHaveBeenCalledTimes(1);
  });

  test('renders icons with menu items', () => {
    renderWithTheme(<Sidebar open={true} onClose={() => {}} />);
    
    // Check if icons are rendered
    const menuItems = screen.getAllByRole('listitem');
    expect(menuItems.length).toBeGreaterThan(0);
    
    // Each menu item should have an icon
    menuItems.forEach(item => {
      const icon = item.querySelector('svg');
      expect(icon).toBeInTheDocument();
    });
  });

  test('highlights active link based on current path', () => {
    renderWithTheme(
      <MemoryRouter initialEntries={['/documents']}>
        <ThemeProvider theme={createTheme()}>
          <Sidebar open={true} onClose={() => {}} />
        </ThemeProvider>
      </MemoryRouter>
    );
    
    // The Documents link should be highlighted
    const documentsLink = screen.getByText(/documents/i).closest('a');
    expect(documentsLink).toHaveClass('Mui-selected');
    
    // Other links should not be highlighted
    const dashboardLink = screen.getByText(/dashboard/i).closest('a');
    expect(dashboardLink).not.toHaveClass('Mui-selected');
  });
}); 