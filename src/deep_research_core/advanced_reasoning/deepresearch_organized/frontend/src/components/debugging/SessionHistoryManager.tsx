import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Divider,
  TextField,
  InputAdornment,
  Chip,
  Tooltip,
  Button,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions
} from '@mui/material';
import {
  Search as SearchIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  CloudDownload as DownloadIcon,
  InfoOutlined as InfoIcon
} from '@mui/icons-material';
import { DebugSession } from './index';
import { formatTimestamp } from '../../utils/formatters';

// Mock API for demonstration purposes
// In a real app, this would be replaced with actual API calls
const mockApi = {
  fetchSessions: async (query: string = ''): Promise<DebugSession[]> => {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // Generate some mock sessions
    const sessions: DebugSession[] = [];
    const now = Date.now();
    
    for (let i = 0; i < 10; i++) {
      const startTime = new Date(now - (i * 1000 * 60 * 30)).toISOString(); // 30 minutes apart
      const endTime = new Date(new Date(startTime).getTime() + 1000 * 60 * 5).toISOString(); // 5 minutes duration
      
      sessions.push({
        id: `session-${i + 100}`,
        startTime,
        endTime,
        query: [
          'How do graphs of thoughts compare to tree of thoughts?',
          'What are the advantages of ReAct reasoning?',
          'How does RAG improve question answering?',
          'What are the best strategies for self-reflection in LLMs?',
          'How to implement multi-source validation in a reasoning system?'
        ][i % 5],
        steps: [],
        metadata: {
          model: ['OpenAI/GPT-4', 'Claude/3', 'DeepSeek/large', 'Cohere/Command'][i % 4],
          success: Math.random() > 0.2,
          total_duration: Math.floor(1000 + Math.random() * 5000)
        }
      });
    }
    
    // Filter by query if provided
    if (query) {
      const lowercaseQuery = query.toLowerCase();
      return sessions.filter(session => 
        session.query.toLowerCase().includes(lowercaseQuery) || 
        session.id.toLowerCase().includes(lowercaseQuery) ||
        (session.metadata?.model as string)?.toLowerCase().includes(lowercaseQuery)
      );
    }
    
    return sessions;
  },
  
  deleteSession: async (sessionId: string): Promise<boolean> => {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));
    return true;
  },
  
  downloadSession: async (sessionId: string): Promise<Blob> => {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // Create a dummy JSON blob
    const sessionData = {
      id: sessionId,
      timestamp: new Date().toISOString(),
      steps: [],
      metadata: {}
    };
    
    return new Blob([JSON.stringify(sessionData, null, 2)], {
      type: 'application/json'
    });
  }
};

interface SessionHistoryManagerProps {
  onSessionSelect?: (session: DebugSession) => void;
  width?: string | number;
  height?: string | number;
}

const SessionHistoryManager: React.FC<SessionHistoryManagerProps> = ({
  onSessionSelect,
  width = '100%',
  height = '600px'
}) => {
  const [sessions, setSessions] = useState<DebugSession[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedSessionId, setSelectedSessionId] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false);
  const [sessionToDelete, setSessionToDelete] = useState<string | null>(null);
  const [refreshKey, setRefreshKey] = useState<number>(0);

  // Fetch sessions
  useEffect(() => {
    const fetchSessionHistory = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const data = await mockApi.fetchSessions(searchTerm);
        setSessions(data);
      } catch (err) {
        console.error('Error fetching session history:', err);
        setError('Failed to load session history. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchSessionHistory();
  }, [searchTerm, refreshKey]);

  // Handle search input change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  // Handle refresh button click
  const handleRefresh = () => {
    setRefreshKey(prevKey => prevKey + 1);
  };

  // Handle session selection
  const handleSessionSelect = (session: DebugSession) => {
    setSelectedSessionId(session.id);
    if (onSessionSelect) {
      onSessionSelect(session);
    }
  };

  // Handle delete dialog
  const openDeleteDialog = (sessionId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    setSessionToDelete(sessionId);
    setDeleteDialogOpen(true);
  };

  const closeDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setSessionToDelete(null);
  };

  // Handle session deletion
  const handleDeleteSession = async () => {
    if (!sessionToDelete) return;
    
    try {
      await mockApi.deleteSession(sessionToDelete);
      setSessions(prevSessions => prevSessions.filter(s => s.id !== sessionToDelete));
      if (selectedSessionId === sessionToDelete) {
        setSelectedSessionId(null);
      }
    } catch (err) {
      console.error('Error deleting session:', err);
      setError('Failed to delete session. Please try again.');
    } finally {
      closeDeleteDialog();
    }
  };

  // Handle session download
  const handleDownloadSession = async (sessionId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    
    try {
      const blob = await mockApi.downloadSession(sessionId);
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `debug-session-${sessionId}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Error downloading session:', err);
      setError('Failed to download session. Please try again.');
    }
  };

  return (
    <Paper elevation={2} sx={{ width, height, overflow: 'hidden', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ p: 2, borderBottom: '1px solid #e0e0e0' }}>
        <Typography variant="h6" component="h2" gutterBottom>
          Debug Session History
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <TextField
            size="small"
            placeholder="Search sessions..."
            value={searchTerm}
            onChange={handleSearchChange}
            fullWidth
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon fontSize="small" />
                </InputAdornment>
              ),
            }}
          />
          <Tooltip title="Refresh sessions">
            <IconButton onClick={handleRefresh} disabled={loading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mx: 2, mt: 2 }}>
          {error}
        </Alert>
      )}

      <Box sx={{ flex: 1, overflow: 'auto', p: 0 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
            <CircularProgress />
          </Box>
        ) : sessions.length === 0 ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
            <Typography color="text.secondary">
              {searchTerm ? 'No sessions matching your search' : 'No debug sessions found'}
            </Typography>
          </Box>
        ) : (
          <List disablePadding>
            {sessions.map((session, index) => (
              <React.Fragment key={session.id}>
                {index > 0 && <Divider />}
                <ListItem 
                  button 
                  selected={selectedSessionId === session.id}
                  onClick={() => handleSessionSelect(session)}
                  sx={{ 
                    py: 1.5,
                    '&.Mui-selected': {
                      bgcolor: 'rgba(25, 118, 210, 0.08)',
                    },
                    '&:hover': {
                      bgcolor: 'rgba(0, 0, 0, 0.04)',
                    }
                  }}
                >
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body1" component="span" noWrap sx={{ maxWidth: '300px' }}>
                          {session.query}
                        </Typography>
                        {session.metadata?.success === false && (
                          <Chip size="small" label="Error" color="error" />
                        )}
                      </Box>
                    }
                    secondary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mt: 0.5 }}>
                        <Typography variant="caption" component="span">
                          {formatTimestamp(new Date(session.startTime).getTime())}
                        </Typography>
                        {session.metadata?.model && (
                          <Chip 
                            size="small" 
                            label={session.metadata.model as string} 
                            sx={{ height: 20, fontSize: '0.7rem' }}
                          />
                        )}
                        <Typography variant="caption" component="span">
                          {`ID: ${session.id}`}
                        </Typography>
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <Tooltip title="Download session">
                      <IconButton
                        edge="end"
                        size="small"
                        onClick={(e) => handleDownloadSession(session.id, e)}
                        sx={{ mr: 1 }}
                      >
                        <DownloadIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete session">
                      <IconButton
                        edge="end"
                        size="small"
                        onClick={(e) => openDeleteDialog(session.id, e)}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </ListItemSecondaryAction>
                </ListItem>
              </React.Fragment>
            ))}
          </List>
        )}
      </Box>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={closeDeleteDialog}
      >
        <DialogTitle>Confirm Deletion</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this debug session? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeDeleteDialog}>Cancel</Button>
          <Button onClick={handleDeleteSession} color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default SessionHistoryManager; 