import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Divider,
  Chip,
  IconButton,
  Tooltip
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import FileCopyIcon from '@mui/icons-material/FileCopy';
import SyntaxHighlighter from 'react-syntax-highlighter';
import { docco } from 'react-syntax-highlighter/dist/esm/styles/hljs';
import { formatDuration } from '../../utils/formatters';
import { DebugSession } from './index';

interface DebugSessionInfoProps {
  session: DebugSession;
  onSave?: (session: DebugSession) => void;
  showRawData?: boolean;
}

const DebugSessionInfo: React.FC<DebugSessionInfoProps> = ({
  session,
  onSave,
  showRawData = false
}) => {
  // Copy content to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  // Calculate session duration
  const calculateDuration = (): number | null => {
    if (!session.endTime) return null;
    
    const startDate = new Date(session.startTime);
    const endDate = new Date(session.endTime);
    return endDate.getTime() - startDate.getTime();
  };

  const duration = calculateDuration();

  // Count steps by type
  const getStepCounts = () => {
    const counts = {
      model_call: 0,
      retrieval: 0,
      reasoning: 0,
      action: 0,
      error: 0,
      other: 0,
      info: 0
    };
    
    session.steps.forEach(step => {
      if (step.type in counts) {
        counts[step.type as keyof typeof counts]++;
      } else {
        counts.other++;
      }
    });
    
    return counts;
  };

  const stepCounts = getStepCounts();

  return (
    <Paper variant="outlined" sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Session Details</Typography>
        {onSave && (
          <Tooltip title="Save session">
            <IconButton size="small" onClick={() => onSave(session)}>
              <SaveIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        )}
      </Box>
      <Divider sx={{ mb: 2 }} />
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle2">Session ID</Typography>
        <Typography variant="body2">{session.id}</Typography>
      </Box>
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle2">Query</Typography>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body2" sx={{ flex: 1 }}>{session.query}</Typography>
          <Tooltip title="Copy query">
            <IconButton size="small" onClick={() => copyToClipboard(session.query)}>
              <FileCopyIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle2">Started</Typography>
        <Typography variant="body2">{new Date(session.startTime).toLocaleString()}</Typography>
      </Box>
      
      {session.endTime && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2">Ended</Typography>
          <Typography variant="body2">{new Date(session.endTime).toLocaleString()}</Typography>
        </Box>
      )}
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle2">Duration</Typography>
        <Typography variant="body2">
          {duration ? formatDuration(duration) : 'In progress...'}
        </Typography>
      </Box>
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle2">Steps</Typography>
        <Typography variant="body2">{session.steps.length} total steps</Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
          {stepCounts.model_call > 0 && (
            <Chip 
              size="small" 
              label={`${stepCounts.model_call} Model Calls`}
              sx={{ bgcolor: '#E6F7FF' }}
            />
          )}
          {stepCounts.retrieval > 0 && (
            <Chip 
              size="small" 
              label={`${stepCounts.retrieval} Retrievals`}
              sx={{ bgcolor: '#F6FFED' }}
            />
          )}
          {stepCounts.reasoning > 0 && (
            <Chip 
              size="small" 
              label={`${stepCounts.reasoning} Reasoning`}
              sx={{ bgcolor: '#FFFBE6' }}
            />
          )}
          {stepCounts.action > 0 && (
            <Chip 
              size="small" 
              label={`${stepCounts.action} Actions`}
              sx={{ bgcolor: '#E6FFFB' }}
            />
          )}
          {stepCounts.error > 0 && (
            <Chip 
              size="small" 
              label={`${stepCounts.error} Errors`}
              sx={{ bgcolor: '#FFF1F0' }}
            />
          )}
        </Box>
      </Box>
      
      {session.metadata && Object.keys(session.metadata).length > 0 && (
        <Box>
          <Typography variant="subtitle2" gutterBottom>Session Metadata</Typography>
          <Paper variant="outlined" sx={{ p: 1 }}>
            {showRawData ? (
              <SyntaxHighlighter
                language="json"
                style={docco}
                customStyle={{ fontSize: '0.85rem', padding: '0.5rem', margin: 0 }}
              >
                {JSON.stringify(session.metadata, null, 2)}
              </SyntaxHighlighter>
            ) : (
              <Box sx={{ pl: 1 }}>
                {Object.entries(session.metadata).map(([key, value]) => (
                  <Typography key={key} variant="body2" sx={{ fontSize: '0.85rem', mb: 0.5 }}>
                    <b>{key}:</b> {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                  </Typography>
                ))}
              </Box>
            )}
          </Paper>
        </Box>
      )}
    </Paper>
  );
};

export default DebugSessionInfo; 