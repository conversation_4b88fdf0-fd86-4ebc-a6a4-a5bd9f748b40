import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import DebugConsole, { DebugSession } from '../DebugConsole';

// Mock data for testing
const mockDebugSession: DebugSession = {
  id: 'session-123',
  startTime: '2024-04-27T12:00:00.000Z',
  endTime: '2024-04-27T12:05:30.000Z',
  query: 'What is the capital of France?',
  steps: [
    {
      id: 'step-1',
      timestamp: '2024-04-27T12:00:05.000Z',
      type: 'model_call',
      name: 'Initial Query Processing',
      content: 'Processing the query about the capital of France',
      duration: 250,
      modelInput: 'What is the capital of France?',
      modelOutput: 'The capital of France is Paris.'
    },
    {
      id: 'step-2',
      timestamp: '2024-04-27T12:00:15.000Z',
      type: 'retrieval',
      name: 'Knowledge Retrieval',
      content: 'Retrieved information about Paris from the database',
      duration: 150,
      metadata: {
        source: 'geography_db',
        confidence: 0.95
      }
    },
    {
      id: 'step-3',
      timestamp: '2024-04-27T12:00:25.000Z',
      type: 'error',
      name: 'Image Loading Error',
      content: 'Failed to load image of Paris',
      error: 'File not found: paris.jpg'
    }
  ],
  metadata: {
    model: 'GPT-4',
    version: '2.0',
    user_id: 'user-456'
  }
};

// Mock functions
const mockOnStepClick = jest.fn();
const mockOnSearch = jest.fn();
const mockOnClear = jest.fn();
const mockOnSave = jest.fn();

describe('DebugConsole Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders without crashing with null session', () => {
    render(<DebugConsole session={null} />);
    expect(screen.getByText('Debug Console')).toBeInTheDocument();
    expect(screen.getByText('No active debugging session')).toBeInTheDocument();
  });

  test('renders debug session information correctly', () => {
    render(<DebugConsole session={mockDebugSession} />);
    
    // Header should be visible
    expect(screen.getByText('Debug Console')).toBeInTheDocument();
    
    // Query should be visible in the first tab
    expect(screen.getByText('Initial Query Processing')).toBeInTheDocument();
    expect(screen.getByText('Knowledge Retrieval')).toBeInTheDocument();
    expect(screen.getByText('Image Loading Error')).toBeInTheDocument();
  });

  test('filters steps based on type selection', () => {
    render(<DebugConsole session={mockDebugSession} />);
    
    // All steps should be visible initially
    expect(screen.getByText('Initial Query Processing')).toBeInTheDocument();
    expect(screen.getByText('Knowledge Retrieval')).toBeInTheDocument();
    expect(screen.getByText('Image Loading Error')).toBeInTheDocument();
    
    // Select only error type
    const filterSelect = screen.getByLabelText('Filter');
    fireEvent.change(filterSelect, { target: { value: 'error' } });
    
    // Only error step should now be visible
    expect(screen.queryByText('Initial Query Processing')).not.toBeInTheDocument();
    expect(screen.queryByText('Knowledge Retrieval')).not.toBeInTheDocument();
    expect(screen.getByText('Image Loading Error')).toBeInTheDocument();
  });

  test('calls onSearch when search form is submitted', () => {
    render(<DebugConsole session={mockDebugSession} onSearch={mockOnSearch} />);
    
    const searchInput = screen.getByPlaceholderText('Search debug logs...');
    const searchButton = screen.getByRole('button', { name: 'Search' });
    
    fireEvent.change(searchInput, { target: { value: 'Paris' } });
    fireEvent.click(searchButton);
    
    expect(mockOnSearch).toHaveBeenCalledWith('Paris');
  });

  test('expands step details when accordion is clicked', () => {
    render(<DebugConsole session={mockDebugSession} />);
    
    // Initially metadata should not be visible
    expect(screen.queryByText('source: geography_db')).not.toBeInTheDocument();
    
    // Click on the Knowledge Retrieval step
    fireEvent.click(screen.getByText('Knowledge Retrieval'));
    
    // Now metadata should be visible
    expect(screen.getByText('Metadata:')).toBeInTheDocument();
  });

  test('calls onSave when save button is clicked', () => {
    render(<DebugConsole session={mockDebugSession} onSave={mockOnSave} />);
    
    const saveButton = screen.getByRole('button', { name: 'Save debug session' });
    fireEvent.click(saveButton);
    
    expect(mockOnSave).toHaveBeenCalledWith(mockDebugSession);
  });
}); 