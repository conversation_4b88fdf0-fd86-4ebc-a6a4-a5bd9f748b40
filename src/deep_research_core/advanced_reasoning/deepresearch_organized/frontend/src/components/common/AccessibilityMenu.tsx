import React, { useState } from 'react';
import {
  IconButton,
  <PERSON>u,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Tooltip,
  Divider,
  Slider,
  Typography,
  Switch,
  FormControlLabel,
  Box,
  useTheme
} from '@mui/material';
import {
  Accessibility as AccessibilityIcon,
  TextFields as TextFieldsIcon,
  FormatLineSpacing as LineSpacingIcon,
  Contrast as ContrastIcon,
  HighlightAlt as HighlightIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

interface AccessibilityMenuProps {
  /**
   * Size of the button
   * @default 'medium'
   */
  size?: 'small' | 'medium' | 'large';
  
  /**
   * Whether the component is being used in dark areas like the header
   * @default false
   */
  onDarkBackground?: boolean;
}

/**
 * AccessibilityMenu component provides accessibility settings for the application
 */
const AccessibilityMenu: React.FC<AccessibilityMenuProps> = ({
  size = 'medium',
  onDarkBackground = false
}) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  
  // Accessibility settings state
  const [fontSize, setFontSize] = useState<number>(100); // Percentage
  const [lineSpacing, setLineSpacing] = useState<number>(1.5);
  const [highContrast, setHighContrast] = useState<boolean>(false);
  const [focusHighlight, setFocusHighlight] = useState<boolean>(false);
  
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  
  const handleClose = () => {
    setAnchorEl(null);
  };
  
  const handleFontSizeChange = (_event: Event, newValue: number | number[]) => {
    setFontSize(newValue as number);
    // Apply font size changes to document
    document.documentElement.style.fontSize = `${newValue}%`;
  };
  
  const handleLineSpacingChange = (_event: Event, newValue: number | number[]) => {
    setLineSpacing(newValue as number);
    // Apply line spacing changes to document
    document.body.style.lineHeight = `${newValue}`;
  };
  
  const handleHighContrastChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setHighContrast(event.target.checked);
    // Apply high contrast class to body
    if (event.target.checked) {
      document.body.classList.add('high-contrast');
    } else {
      document.body.classList.remove('high-contrast');
    }
  };
  
  const handleFocusHighlightChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFocusHighlight(event.target.checked);
    // Apply focus highlight class to body
    if (event.target.checked) {
      document.body.classList.add('focus-highlight');
    } else {
      document.body.classList.remove('focus-highlight');
    }
  };
  
  return (
    <>
      <Tooltip title={t('accessibility.settings')}>
        <IconButton
          onClick={handleClick}
          size={size}
          color={onDarkBackground ? 'inherit' : 'default'}
          aria-controls={open ? 'accessibility-menu' : undefined}
          aria-haspopup="true"
          aria-expanded={open ? 'true' : undefined}
          aria-label={t('accessibility.settings')}
        >
          <AccessibilityIcon />
        </IconButton>
      </Tooltip>
      
      <Menu
        id="accessibility-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'accessibility-button',
          dense: false,
          sx: { width: 300, maxWidth: '100%' }
        }}
      >
        <Typography
          variant="subtitle1"
          sx={{ 
            fontWeight: 'bold', 
            px: 2, 
            pt: 1 
          }}
        >
          {t('accessibility.settings')}
        </Typography>
        
        <Divider sx={{ my: 1 }} />
        
        <Box sx={{ px: 2, py: 1 }}>
          <Box 
            sx={{ 
              display: 'flex', 
              alignItems: 'center',
              mb: 1
            }}
          >
            <TextFieldsIcon 
              fontSize="small" 
              sx={{ mr: 1, color: theme.palette.text.secondary }} 
            />
            <Typography variant="body2">
              {t('accessibility.fontSize')}
            </Typography>
          </Box>
          <Slider
            value={fontSize}
            onChange={handleFontSizeChange}
            aria-label={t('accessibility.fontSize')}
            valueLabelDisplay="auto"
            min={80}
            max={200}
            marks={[
              { value: 80, label: '80%' },
              { value: 100, label: '100%' },
              { value: 150, label: '150%' },
              { value: 200, label: '200%' },
            ]}
          />
        </Box>
        
        <Box sx={{ px: 2, py: 1 }}>
          <Box 
            sx={{ 
              display: 'flex', 
              alignItems: 'center',
              mb: 1
            }}
          >
            <LineSpacingIcon 
              fontSize="small" 
              sx={{ mr: 1, color: theme.palette.text.secondary }} 
            />
            <Typography variant="body2">
              {t('accessibility.lineSpacing')}
            </Typography>
          </Box>
          <Slider
            value={lineSpacing}
            onChange={handleLineSpacingChange}
            aria-label={t('accessibility.lineSpacing')}
            valueLabelDisplay="auto"
            min={1}
            max={3}
            step={0.1}
            marks={[
              { value: 1, label: '1' },
              { value: 1.5, label: '1.5' },
              { value: 2, label: '2' },
              { value: 3, label: '3' },
            ]}
          />
        </Box>
        
        <Divider sx={{ my: 1 }} />
        
        <MenuItem>
          <ListItemIcon>
            <ContrastIcon fontSize="small" />
          </ListItemIcon>
          <FormControlLabel
            control={
              <Switch 
                checked={highContrast}
                onChange={handleHighContrastChange}
                inputProps={{ 'aria-label': t('accessibility.highContrast') }}
              />
            }
            label={
              <Typography variant="body2">
                {t('accessibility.highContrast')}
              </Typography>
            }
            sx={{ ml: 0, width: '100%' }}
          />
        </MenuItem>
        
        <MenuItem>
          <ListItemIcon>
            <HighlightIcon fontSize="small" />
          </ListItemIcon>
          <FormControlLabel
            control={
              <Switch 
                checked={focusHighlight}
                onChange={handleFocusHighlightChange}
                inputProps={{ 'aria-label': t('accessibility.focusHighlight') }}
              />
            }
            label={
              <Typography variant="body2">
                {t('accessibility.focusHighlight')}
              </Typography>
            }
            sx={{ ml: 0, width: '100%' }}
          />
        </MenuItem>
      </Menu>
    </>
  );
};

export default AccessibilityMenu; 