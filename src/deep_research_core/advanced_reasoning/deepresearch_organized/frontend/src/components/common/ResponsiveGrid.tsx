import React from 'react';
import { Box, useTheme, useMediaQuery } from '@mui/material';
import { 
  ResponsiveGridContainerProps, 
  ResponsiveGridItemProps,
  calculateGridColumnWidth
} from '../../utils/ResponsiveLayout';

/**
 * A responsive grid container component that adapts to different screen sizes
 */
export const ResponsiveGridContainer = ({
  children,
  spacing = 2,
  direction = 'row',
  justifyContent = 'flex-start',
  alignItems = 'stretch',
  wrap = 'wrap',
  className,
  style
}: ResponsiveGridContainerProps) => {
  const theme = useTheme();
  const isXs = useMediaQuery(theme.breakpoints.only('xs'));
  const isSm = useMediaQuery(theme.breakpoints.only('sm'));
  const isMd = useMediaQuery(theme.breakpoints.only('md'));
  
  // Calculate spacing based on the current breakpoint
  const getSpacing = (): number => {
    if (typeof spacing === 'number') {
      return spacing;
    }
    
    if (isXs && spacing.xs !== undefined) return spacing.xs;
    if (isSm && spacing.sm !== undefined) return spacing.sm;
    if (isMd && spacing.md !== undefined) return spacing.md;
    
    // Default fallbacks based on screen size
    return isXs ? 1 : isSm ? 1.5 : isMd ? 2 : 3;
  };
  
  const spacingValue = getSpacing();
  
  return (
    <Box
      className={className}
      sx={{
        display: 'grid',
        gap: theme.spacing(spacingValue),
        gridTemplateColumns: 'repeat(12, 1fr)',
        flexDirection: direction,
        justifyContent,
        alignItems,
        flexWrap: wrap,
        width: '100%',
        ...style
      }}
    >
      {children}
    </Box>
  );
};

/**
 * A responsive grid item component that adapts to different screen sizes
 */
export const ResponsiveGridItem = ({
  children,
  xs = 12,
  sm,
  md,
  lg,
  xl,
  className,
  style
}: ResponsiveGridItemProps) => {
  const theme = useTheme();
  const isXs = useMediaQuery(theme.breakpoints.only('xs'));
  const isSm = useMediaQuery(theme.breakpoints.only('sm'));
  const isMd = useMediaQuery(theme.breakpoints.only('md'));
  const isLg = useMediaQuery(theme.breakpoints.only('lg'));
  const isXl = useMediaQuery(theme.breakpoints.only('xl'));
  
  // Determine grid column span based on current breakpoint
  const getGridColumn = (): string => {
    if (isXs) return calculateGridColumnWidth({ xs, sm, md, lg, xl, children }, 'xs');
    if (isSm && sm !== undefined) return calculateGridColumnWidth({ xs, sm, md, lg, xl, children }, 'sm');
    if (isMd && md !== undefined) return calculateGridColumnWidth({ xs, sm, md, lg, xl, children }, 'md');
    if (isLg && lg !== undefined) return calculateGridColumnWidth({ xs, sm, md, lg, xl, children }, 'lg');
    if (isXl && xl !== undefined) return calculateGridColumnWidth({ xs, sm, md, lg, xl, children }, 'xl');
    
    // Fallback logic with progressive enhancement
    if (isSm) return calculateGridColumnWidth({ xs: sm || xs, sm, md, lg, xl, children }, 'xs');
    if (isMd) return calculateGridColumnWidth({ xs: md || sm || xs, sm, md, lg, xl, children }, 'xs');
    if (isLg) return calculateGridColumnWidth({ xs: lg || md || sm || xs, sm, md, lg, xl, children }, 'xs');
    if (isXl) return calculateGridColumnWidth({ xs: xl || lg || md || sm || xs, sm, md, lg, xl, children }, 'xs');
    
    return calculateGridColumnWidth({ xs, sm, md, lg, xl, children }, 'xs');
  };
  
  return (
    <Box
      className={className}
      sx={{
        gridColumn: getGridColumn(),
        ...style
      }}
    >
      {children}
    </Box>
  );
};

const ResponsiveGrid = { ResponsiveGridContainer, ResponsiveGridItem };
export default ResponsiveGrid; 