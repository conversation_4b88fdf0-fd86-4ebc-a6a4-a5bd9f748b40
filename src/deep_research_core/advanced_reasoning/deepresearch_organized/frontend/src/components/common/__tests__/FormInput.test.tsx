import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import FormInput, { SelectOption } from '../FormInput';
import userEvent from '@testing-library/user-event';

// Create a wrapper component that provides the necessary context
const Wrapper = ({ children }: { children: React.ReactNode }) => {
  const theme = createTheme();
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('FormInput Component', () => {
  // Text Input Tests
  describe('Text Input', () => {
    test('renders text input with label', () => {
      render(
        <Wrapper>
          <FormInput
            type="text"
            label="Name"
            name="name"
            value=""
            onChange={() => {}}
          />
        </Wrapper>
      );
      
      expect(screen.getByLabelText('Name')).toBeInTheDocument();
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    test('handles text input change correctly', () => {
      const handleChange = jest.fn();
      render(
        <Wrapper>
          <FormInput
            type="text"
            label="Name"
            name="name"
            value=""
            onChange={handleChange}
          />
        </Wrapper>
      );
      
      const input = screen.getByLabelText('Name');
      fireEvent.change(input, { target: { value: 'John Doe' } });
      
      expect(handleChange).toHaveBeenCalled();
    });

    test('displays error state and helper text', () => {
      render(
        <Wrapper>
          <FormInput
            type="text"
            label="Name"
            name="name"
            value=""
            onChange={() => {}}
            error={true}
            helperText="This field is required"
          />
        </Wrapper>
      );
      
      expect(screen.getByText('This field is required')).toBeInTheDocument();
      expect(screen.getByLabelText('Name')).toHaveAttribute('aria-invalid', 'true');
    });
  });

  // Password Input Tests
  describe('Password Input', () => {
    test('renders password input with masked value', () => {
      render(
        <Wrapper>
          <FormInput
            type="password"
            label="Password"
            name="password"
            value="secret123"
            onChange={() => {}}
          />
        </Wrapper>
      );
      
      const input = screen.getByLabelText('Password');
      expect(input).toBeInTheDocument();
      expect(input).toHaveAttribute('type', 'password');
    });
  });

  // Textarea Tests
  describe('Textarea Input', () => {
    test('renders textarea with specified rows', () => {
      render(
        <Wrapper>
          <FormInput
            type="textarea"
            label="Description"
            name="description"
            value="Test description"
            onChange={() => {}}
            rows={6}
          />
        </Wrapper>
      );
      
      const textarea = screen.getByLabelText('Description');
      expect(textarea).toBeInTheDocument();
      expect(textarea.closest('textarea')).toHaveAttribute('rows', '6');
    });
  });

  // Select Input Tests
  describe('Select Input', () => {
    const options: SelectOption[] = [
      { value: 'option1', label: 'Option 1' },
      { value: 'option2', label: 'Option 2' },
      { value: 'option3', label: 'Option 3' },
    ];

    test('renders select with options', () => {
      render(
        <Wrapper>
          <FormInput
            type="select"
            label="Select Option"
            name="select"
            value="option1"
            onChange={() => {}}
            options={options}
          />
        </Wrapper>
      );
      
      expect(screen.getByLabelText('Select Option')).toBeInTheDocument();
    });

    test('handles select change correctly', async () => {
      const handleChange = jest.fn();
      render(
        <Wrapper>
          <FormInput
            type="select"
            label="Select Option"
            name="select"
            value="option1"
            onChange={handleChange}
            options={options}
          />
        </Wrapper>
      );
      
      // Open the select dropdown
      const selectElement = screen.getByLabelText('Select Option');
      fireEvent.mouseDown(selectElement);
      
      // Select an option
      const option = await screen.findByText('Option 2');
      userEvent.click(option);
      
      expect(handleChange).toHaveBeenCalled();
    });
  });

  // Checkbox Tests
  describe('Checkbox Input', () => {
    test('renders checkbox with label', () => {
      render(
        <Wrapper>
          <FormInput
            type="checkbox"
            label="Accept Terms"
            name="terms"
            checked={false}
            onChange={() => {}}
          />
        </Wrapper>
      );
      
      expect(screen.getByLabelText('Accept Terms')).toBeInTheDocument();
      expect(screen.getByRole('checkbox')).not.toBeChecked();
    });

    test('handles checkbox change correctly', () => {
      const handleChange = jest.fn();
      render(
        <Wrapper>
          <FormInput
            type="checkbox"
            label="Accept Terms"
            name="terms"
            checked={false}
            onChange={handleChange}
          />
        </Wrapper>
      );
      
      const checkbox = screen.getByLabelText('Accept Terms');
      fireEvent.click(checkbox);
      
      expect(handleChange).toHaveBeenCalled();
    });
  });

  // Switch Tests
  describe('Switch Input', () => {
    test('renders switch with label', () => {
      render(
        <Wrapper>
          <FormInput
            type="switch"
            label="Enable Notifications"
            name="notifications"
            checked={true}
            onChange={() => {}}
          />
        </Wrapper>
      );
      
      expect(screen.getByLabelText('Enable Notifications')).toBeInTheDocument();
      expect(screen.getByRole('checkbox')).toBeChecked();
    });

    test('handles switch change correctly', () => {
      const handleChange = jest.fn();
      render(
        <Wrapper>
          <FormInput
            type="switch"
            label="Enable Notifications"
            name="notifications"
            checked={true}
            onChange={handleChange}
          />
        </Wrapper>
      );
      
      const switchEl = screen.getByLabelText('Enable Notifications');
      fireEvent.click(switchEl);
      
      expect(handleChange).toHaveBeenCalled();
    });
  });
}); 