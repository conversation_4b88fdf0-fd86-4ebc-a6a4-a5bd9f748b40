import React, { useState } from 'react';
import { 
  Button, 
  IconButton, 
  Menu, 
  MenuItem, 
  ListItemIcon, 
  ListItemText,
  Tooltip,
  Badge
} from '@mui/material';
import {
  Translate as TranslateIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useLanguage, SUPPORTED_LANGUAGES, SupportedLanguage } from '../../i18n/LanguageContext';

// Flag icons for each language
const FLAGS: Record<SupportedLanguage, string> = {
  en: '🇺🇸',
  vi: '🇻🇳'
};

// Names of languages in their native language
const NATIVE_NAMES: Record<SupportedLanguage, string> = {
  en: 'English',
  vi: 'Tiếng Việt'
};

interface LanguageSwitcherProps {
  variant?: 'icon' | 'text' | 'menu';
  size?: 'small' | 'medium' | 'large';
  showLabel?: boolean;
  position?: 'navbar' | 'sidebar' | 'footer';
}

/**
 * LanguageSwitcher component allows users to change the application language.
 * It can be displayed as an icon button, text button, or in a menu.
 */
const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  variant = 'icon',
  size = 'medium',
  showLabel = false,
  position = 'navbar'
}) => {
  const { t } = useTranslation();
  const { currentLanguage, changeLanguage } = useLanguage();
  
  // Menu state
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  
  // Open menu
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  
  // Close menu
  const handleClose = () => {
    setAnchorEl(null);
  };
  
  // Change language
  const handleLanguageChange = (language: SupportedLanguage) => {
    changeLanguage(language);
    handleClose();
  };
  
  // Flag with circle badge for current language
  const FlagWithBadge = (lang: SupportedLanguage) => (
    <Badge
      badgeContent={lang === currentLanguage ? <CheckCircleIcon fontSize="small" color="primary" /> : null}
      overlap="circular"
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'right',
      }}
    >
      <span style={{ fontSize: size === 'small' ? '1rem' : size === 'large' ? '1.5rem' : '1.25rem' }}>
        {FLAGS[lang]}
      </span>
    </Badge>
  );
  
  // Icon button variant
  if (variant === 'icon') {
    return (
      <>
        <Tooltip title={t('language.toggle', { language: t(`language.${currentLanguage === 'en' ? 'vi' : 'en'}`) })}>
          <IconButton
            onClick={handleClick}
            size={size}
            aria-controls={open ? 'language-menu' : undefined}
            aria-haspopup="true"
            aria-expanded={open ? 'true' : undefined}
            color="inherit"
          >
            <TranslateIcon />
          </IconButton>
        </Tooltip>
        <Menu
          id="language-menu"
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          MenuListProps={{
            'aria-labelledby': 'language-button',
          }}
        >
          {SUPPORTED_LANGUAGES.map((lang) => (
            <MenuItem 
              key={lang} 
              onClick={() => handleLanguageChange(lang)}
              selected={lang === currentLanguage}
            >
              <ListItemIcon>
                {FlagWithBadge(lang)}
              </ListItemIcon>
              <ListItemText>
                {NATIVE_NAMES[lang]}
              </ListItemText>
            </MenuItem>
          ))}
        </Menu>
      </>
    );
  }
  
  // Text button variant
  if (variant === 'text') {
    return (
      <>
        <Button
          onClick={handleClick}
          size={size}
          startIcon={<TranslateIcon />}
          endIcon={currentLanguage === 'vi' ? '🇻🇳' : '🇺🇸'}
          aria-controls={open ? 'language-menu' : undefined}
          aria-haspopup="true"
          aria-expanded={open ? 'true' : undefined}
          color="inherit"
        >
          {showLabel ? t(`language.${currentLanguage}`) : null}
        </Button>
        <Menu
          id="language-menu"
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          MenuListProps={{
            'aria-labelledby': 'language-button',
          }}
        >
          {SUPPORTED_LANGUAGES.map((lang) => (
            <MenuItem 
              key={lang} 
              onClick={() => handleLanguageChange(lang)}
              selected={lang === currentLanguage}
            >
              <ListItemIcon>
                {FlagWithBadge(lang)}
              </ListItemIcon>
              <ListItemText>
                {NATIVE_NAMES[lang]}
              </ListItemText>
            </MenuItem>
          ))}
        </Menu>
      </>
    );
  }
  
  // Menu variant (already in a menu)
  return (
    <>
      {SUPPORTED_LANGUAGES.map((lang) => (
        <MenuItem 
          key={lang} 
          onClick={() => handleLanguageChange(lang)}
          selected={lang === currentLanguage}
        >
          <ListItemIcon>
            {FlagWithBadge(lang)}
          </ListItemIcon>
          <ListItemText>
            {NATIVE_NAMES[lang]}
          </ListItemText>
        </MenuItem>
      ))}
    </>
  );
};

export default LanguageSwitcher; 