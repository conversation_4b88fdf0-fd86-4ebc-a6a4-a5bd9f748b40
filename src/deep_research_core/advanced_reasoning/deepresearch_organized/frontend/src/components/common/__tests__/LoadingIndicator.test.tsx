import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import LoadingIndicator from '../LoadingIndicator';

// Create a wrapper component that provides theme context
const Wrapper = ({ children }: { children: React.ReactNode }) => {
  const theme = createTheme();
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('LoadingIndicator Component', () => {
  // Helper function to advance timers for delay testing
  const advanceTimers = async () => {
    // Fast-forward time
    jest.advanceTimersByTime(500);
    
    // Wait for any state updates
    await waitFor(() => {});
  };

  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  test('renders nothing when not loading', () => {
    const { container } = render(
      <Wrapper>
        <LoadingIndicator loading={false} />
      </Wrapper>
    );
    
    expect(container.firstChild).toBeNull();
  });

  test('renders circular variant by default', async () => {
    render(
      <Wrapper>
        <LoadingIndicator loading={true} delay={0} />
      </Wrapper>
    );
    
    const circularProgress = screen.getByRole('progressbar');
    expect(circularProgress).toBeInTheDocument();
    expect(circularProgress).toHaveClass('MuiCircularProgress-root');
  });

  test('renders with custom message', async () => {
    render(
      <Wrapper>
        <LoadingIndicator loading={true} message="Loading data..." delay={0} />
      </Wrapper>
    );
    
    expect(screen.getByText('Loading data...')).toBeInTheDocument();
  });

  test('respects delay timing', async () => {
    jest.useFakeTimers();
    
    const { container } = render(
      <Wrapper>
        <LoadingIndicator loading={true} delay={300} />
      </Wrapper>
    );
    
    // Initially nothing should be rendered due to delay
    expect(container.firstChild).toBeNull();
    
    // Advance time by 300ms
    await advanceTimers();
    
    // Now it should be visible
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('renders linear variant correctly', async () => {
    render(
      <Wrapper>
        <LoadingIndicator loading={true} variant="linear" delay={0} />
      </Wrapper>
    );
    
    const linearProgress = screen.getByRole('progressbar');
    expect(linearProgress).toBeInTheDocument();
    expect(linearProgress).toHaveClass('MuiLinearProgress-root');
  });

  test('renders overlay variant correctly', async () => {
    render(
      <Wrapper>
        <LoadingIndicator loading={true} variant="overlay" delay={0} />
      </Wrapper>
    );
    
    const circularProgress = screen.getByRole('progressbar');
    expect(circularProgress).toBeInTheDocument();
    
    // Should be wrapped in a Paper component
    const paper = circularProgress.closest('.MuiPaper-root');
    expect(paper).toBeInTheDocument();
  });

  test('renders fullpage variant correctly', async () => {
    render(
      <Wrapper>
        <LoadingIndicator loading={true} variant="fullpage" delay={0} />
      </Wrapper>
    );
    
    const circularProgress = screen.getByRole('progressbar');
    expect(circularProgress).toBeInTheDocument();
    
    // Should have a fixed position container
    const container = circularProgress.closest('div[style*="position: fixed"]');
    expect(container).toBeInTheDocument();
  });

  test('applies different sizes correctly', async () => {
    const { rerender } = render(
      <Wrapper>
        <LoadingIndicator loading={true} size="small" delay={0} />
      </Wrapper>
    );
    
    // Small size
    let circularProgress = screen.getByRole('progressbar');
    expect(circularProgress).toHaveAttribute('aria-valuenow', '');
    
    // Medium size
    rerender(
      <Wrapper>
        <LoadingIndicator loading={true} size="medium" delay={0} />
      </Wrapper>
    );
    
    circularProgress = screen.getByRole('progressbar');
    expect(circularProgress).toHaveAttribute('aria-valuenow', '');
    
    // Large size
    rerender(
      <Wrapper>
        <LoadingIndicator loading={true} size="large" delay={0} />
      </Wrapper>
    );
    
    circularProgress = screen.getByRole('progressbar');
    expect(circularProgress).toHaveAttribute('aria-valuenow', '');
  });

  test('shows determinate progress when value is provided', async () => {
    render(
      <Wrapper>
        <LoadingIndicator loading={true} value={75} delay={0} />
      </Wrapper>
    );
    
    const progressbar = screen.getByRole('progressbar');
    expect(progressbar).toHaveAttribute('aria-valuenow', '75');
  });

  test('applies different colors correctly', async () => {
    const { rerender } = render(
      <Wrapper>
        <LoadingIndicator loading={true} color="primary" delay={0} />
      </Wrapper>
    );
    
    // Primary color
    let circularProgress = screen.getByRole('progressbar');
    expect(circularProgress).toHaveClass('MuiCircularProgress-colorPrimary');
    
    // Secondary color
    rerender(
      <Wrapper>
        <LoadingIndicator loading={true} color="secondary" delay={0} />
      </Wrapper>
    );
    
    circularProgress = screen.getByRole('progressbar');
    expect(circularProgress).toHaveClass('MuiCircularProgress-colorSecondary');
    
    // Error color
    rerender(
      <Wrapper>
        <LoadingIndicator loading={true} color="error" delay={0} />
      </Wrapper>
    );
    
    circularProgress = screen.getByRole('progressbar');
    expect(circularProgress).toHaveClass('MuiCircularProgress-colorError');
  });
}); 