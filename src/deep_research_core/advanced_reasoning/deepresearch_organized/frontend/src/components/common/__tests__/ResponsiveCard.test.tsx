import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import ResponsiveCard from '../ResponsiveCard';
import { Button } from '@mui/material';

const theme = createTheme();

describe('ResponsiveCard Component', () => {
  const renderWithTheme = (ui: React.ReactNode) => {
    return render(<ThemeProvider theme={theme}>{ui}</ThemeProvider>);
  };

  test('renders title and content correctly', () => {
    renderWithTheme(
      <ResponsiveCard 
        title="Test Card Title" 
        content={<div>Test Card Content</div>} 
      />
    );
    
    expect(screen.getByText('Test Card Title')).toBeInTheDocument();
    expect(screen.getByText('Test Card Content')).toBeInTheDocument();
  });

  test('renders subtitle when provided', () => {
    renderWithTheme(
      <ResponsiveCard 
        title="Test Card Title" 
        subtitle="Test Subtitle"
        content={<div>Test Card Content</div>} 
      />
    );
    
    expect(screen.getByText('Test Subtitle')).toBeInTheDocument();
  });

  test('renders actions when provided', () => {
    renderWithTheme(
      <ResponsiveCard 
        title="Test Card Title" 
        content={<div>Test Card Content</div>}
        actions={<Button>Test Action</Button>}
      />
    );
    
    expect(screen.getByText('Test Action')).toBeInTheDocument();
  });

  test('applies full height styling when specified', () => {
    const { container } = renderWithTheme(
      <ResponsiveCard 
        title="Test Card Title" 
        content={<div>Test Card Content</div>}
        fullHeight
      />
    );
    
    // Find the main Card element
    const cardElement = container.firstChild;
    expect(cardElement).toHaveStyle('height: 100%');
  });

  test('applies no padding when specified', () => {
    const { container } = renderWithTheme(
      <ResponsiveCard 
        title="Test Card Title" 
        content={<div>Test Card Content</div>}
        noPadding
      />
    );
    
    // The CardContent should have padding: 0
    const cardContent = container.querySelector('.MuiCardContent-root');
    expect(cardContent).toHaveStyle('padding: 0px');
  });

  test('renders image when provided', () => {
    renderWithTheme(
      <ResponsiveCard 
        title="Test Card Title" 
        content={<div>Test Card Content</div>}
        image="test-image.jpg"
      />
    );
    
    const image = screen.getByAltText('Test Card Title');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', 'test-image.jpg');
  });
}); 