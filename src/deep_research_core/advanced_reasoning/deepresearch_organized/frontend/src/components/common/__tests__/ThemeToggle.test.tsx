import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { createTheme } from '@mui/material';
import ThemeToggle from '../ThemeToggle';

describe('ThemeToggle Component', () => {
  const mockToggle = jest.fn();
  const theme = createTheme();
  
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  test('renders in light mode correctly', () => {
    render(
      <ThemeProvider theme={theme}>
        <ThemeToggle onToggle={mockToggle} isDarkMode={false} data-testid="theme-toggle" />
      </ThemeProvider>
    );
    
    // Should show dark mode icon when in light mode (for switching to dark)
    const darkModeIcon = screen.getByTestId('Brightness4Icon');
    expect(darkModeIcon).toBeInTheDocument();
  });
  
  test('renders in dark mode correctly', () => {
    render(
      <ThemeProvider theme={theme}>
        <ThemeToggle onToggle={mockToggle} isDarkMode={true} data-testid="theme-toggle" />
      </ThemeProvider>
    );
    
    // Should show light mode icon when in dark mode (for switching to light)
    const lightModeIcon = screen.getByTestId('Brightness7Icon');
    expect(lightModeIcon).toBeInTheDocument();
  });
  
  test('calls onToggle when clicked', () => {
    render(
      <ThemeProvider theme={theme}>
        <ThemeToggle onToggle={mockToggle} isDarkMode={false} />
      </ThemeProvider>
    );
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    expect(mockToggle).toHaveBeenCalledTimes(1);
  });
  
  test('applies different styles on dark background', () => {
    render(
      <ThemeProvider theme={theme}>
        <ThemeToggle onToggle={mockToggle} isDarkMode={false} onDarkBackground={true} />
      </ThemeProvider>
    );
    
    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('color', 'inherit');
  });
  
  test('applies different sizes', () => {
    render(
      <ThemeProvider theme={theme}>
        <ThemeToggle onToggle={mockToggle} isDarkMode={false} size="small" />
      </ThemeProvider>
    );
    
    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('size', 'small');
  });
}); 