import React, { ChangeEvent } from 'react';
import {
  TextField,
  TextFieldProps,
  MenuItem,
  Checkbox,
  FormControlLabel,
  FormControl,
  FormHelperText,
  InputLabel,
  Select,
  SelectChangeEvent,
  CheckboxProps,
} from '@mui/material';

export interface FormInputProps extends Omit<TextFieldProps, 'type'> {
  /**
   * Input type
   * @default 'text'
   */
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'date' | 'time' | 'select' | 'textarea' | 'checkbox' | 'radio';
  
  /**
   * Options for select type
   */
  options?: Array<{
    value: string;
    label: string;
  }>;
  
  /**
   * Number of rows for textarea
   * @default 4
   */
  rows?: number;
  
  /**
   * Checkbox specific props
   */
  checkboxProps?: Omit<CheckboxProps, 'name' | 'checked' | 'onChange' | 'disabled' | 'required'>;
}

/**
 * A flexible form input component that supports various input types
 */
const FormInput: React.FC<FormInputProps> = ({
  type = 'text',
  label,
  name,
  value,
  onChange,
  onBlur,
  error,
  helperText,
  required,
  fullWidth = true,
  placeholder,
  disabled,
  options = [],
  rows = 4,
  checkboxProps = {},
  ...rest
}) => {
  // Checkbox input
  if (type === 'checkbox') {
    return (
      <FormControl error={error} fullWidth={fullWidth}>
        <FormControlLabel
          control={
            <Checkbox
              name={name}
              checked={!!value}
              onChange={onChange as React.ChangeEventHandler<HTMLInputElement>}
              disabled={disabled}
              required={required}
              {...checkboxProps}
            />
          }
          label={label}
        />
        {error && helperText && <FormHelperText>{helperText}</FormHelperText>}
      </FormControl>
    );
  }

  // Radio input
  if (type === 'radio') {
    return (
      <FormControl error={error} fullWidth={fullWidth}>
        <FormControlLabel
          control={
            <input
              type="radio"
              name={name}
              value={value as string}
              onChange={onChange}
              disabled={disabled}
              required={required}
            />
          }
          label={label}
        />
        {error && helperText && <FormHelperText>{helperText}</FormHelperText>}
      </FormControl>
    );
  }

  // Select input
  if (type === 'select') {
    const handleSelectChange = (e: SelectChangeEvent<unknown>) => {
      if (onChange) {
        // Create a synthetic event that matches the expected ChangeEvent interface
        const syntheticEvent = {
          ...e,
          target: {
            ...e.target,
            value: e.target.value
          }
        } as unknown as ChangeEvent<HTMLInputElement>;
        
        onChange(syntheticEvent);
      }
    };
    
    return (
      <FormControl error={error} fullWidth={fullWidth} required={required}>
        <InputLabel id={`${name}-label`}>{label}</InputLabel>
        <Select
          labelId={`${name}-label`}
          id={name}
          name={name}
          value={value || ''}
          onChange={handleSelectChange}
          onBlur={onBlur}
          label={label}
          disabled={disabled}
        >
          {options.map((option) => (
            <MenuItem key={option.value} value={option.value}>
              {option.label}
            </MenuItem>
          ))}
        </Select>
        {helperText && <FormHelperText>{helperText}</FormHelperText>}
      </FormControl>
    );
  }

  // Textarea input
  if (type === 'textarea') {
    return (
      <TextField
        id={name}
        name={name}
        label={label}
        value={value || ''}
        onChange={onChange}
        onBlur={onBlur}
        error={error}
        helperText={helperText}
        required={required}
        fullWidth={fullWidth}
        placeholder={placeholder}
        disabled={disabled}
        multiline
        rows={rows}
        {...rest}
      />
    );
  }

  // Default text input (text, email, password, number, tel, url, date, time)
  return (
    <TextField
      id={name}
      name={name}
      label={label}
      value={value || ''}
      onChange={onChange}
      onBlur={onBlur}
      error={error}
      helperText={helperText}
      required={required}
      fullWidth={fullWidth}
      placeholder={placeholder}
      disabled={disabled}
      type={type}
      InputProps={{
        ...rest.InputProps,
      }}
      {...rest}
    />
  );
};

export default FormInput; 