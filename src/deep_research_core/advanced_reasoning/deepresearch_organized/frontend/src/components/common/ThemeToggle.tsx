import React from 'react';
import { IconButton, Tooltip, useTheme } from '@mui/material';
import { Brightness4 as DarkModeIcon, Brightness7 as LightModeIcon } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

interface ThemeToggleProps {
  /**
   * Whether the component is being used in dark areas like the header
   * @default false
   */
  onDarkBackground?: boolean;
  
  /**
   * Size of the icon button
   * @default 'medium'
   */
  size?: 'small' | 'medium' | 'large';
  
  /**
   * Callback function when theme is toggled
   */
  onToggle: () => void;
  
  /**
   * Current theme mode
   */
  isDarkMode: boolean;
}

/**
 * Theme toggle component for switching between light and dark themes
 */
const ThemeToggle: React.FC<ThemeToggleProps> = ({
  onDarkBackground = false,
  size = 'medium',
  onToggle,
  isDarkMode
}) => {
  const theme = useTheme();
  const { t } = useTranslation();
  
  return (
    <Tooltip title={isDarkMode ? t('theme.switchToLight') : t('theme.switchToDark')}>
      <IconButton
        onClick={onToggle}
        size={size}
        color={onDarkBackground ? 'inherit' : 'default'}
        aria-label={isDarkMode ? t('theme.switchToLight') : t('theme.switchToDark')}
        sx={{
          transition: theme.transitions.create(['color'], {
            duration: theme.transitions.duration.shortest,
          }),
        }}
      >
        {isDarkMode ? <LightModeIcon /> : <DarkModeIcon />}
      </IconButton>
    </Tooltip>
  );
};

export default ThemeToggle; 