import React, { useState, useRef, useEffect } from 'react';
import { 
  Box, 
  TextField, 
  Button, 
  Paper, 
  Typography, 
  Avatar, 
  IconButton,
  Menu,
  MenuItem,
  Tooltip,
  CircularProgress,
  useTheme,
  useMediaQuery,
  Divider,
  Select,
  FormControl,
  InputLabel,
  SelectChangeEvent
} from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import DeleteIcon from '@mui/icons-material/Delete';
import SettingsIcon from '@mui/icons-material/Settings';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { ResponsiveGridContainer, ResponsiveGridItem } from '../common';
import { useResponsivePadding, useResponsiveFontSizes } from '../../utils/ResponsiveLayout';
import FeedbackCollector from '../feedback/FeedbackCollector';
import feedbackService from '../../services/feedbackService';

// Message Types
export type MessageRole = 'user' | 'assistant' | 'system';

export interface Message {
  id: string;
  role: MessageRole;
  content: string;
  timestamp: Date;
  metadata?: {
    reasoning?: 'tot' | 'react' | 'cot' | 'rag' | null;
    model?: string;
    executionTime?: number;
    documents?: string[];
  };
}

// Model Types
interface ModelOption {
  id: string;
  name: string;
  description: string;
  provider: string;
  supportsReasoning: boolean;
}

// Reasoning Method Types
interface ReasoningOption {
  id: 'tot' | 'react' | 'cot' | 'rag';
  name: string;
  description: string;
}

// Sample Data (In a real app, these would come from an API)
const models: ModelOption[] = [
  { 
    id: 'gpt-4', 
    name: 'GPT-4', 
    description: 'Advanced model with strong reasoning capabilities',
    provider: 'OpenAI',
    supportsReasoning: true
  },
  { 
    id: 'gpt-3.5-turbo', 
    name: 'GPT-3.5', 
    description: 'Fast and efficient model for general tasks',
    provider: 'OpenAI',
    supportsReasoning: true
  },
  { 
    id: 'claude-3-opus', 
    name: 'Claude 3 Opus', 
    description: 'Advanced reasoning with detailed outputs',
    provider: 'Anthropic',
    supportsReasoning: true
  },
  { 
    id: 'llama-3-70b', 
    name: 'Llama 3 (70B)', 
    description: 'Open model with strong reasoning',
    provider: 'Meta',
    supportsReasoning: true
  },
  { 
    id: 'mistral-7b', 
    name: 'Mistral (7B)', 
    description: 'Efficient open model for local deployment',
    provider: 'Mistral AI',
    supportsReasoning: true
  }
];

const reasoningMethods: ReasoningOption[] = [
  {
    id: 'tot',
    name: 'Tree of Thought',
    description: 'Explores multiple reasoning paths and selects the best one'
  },
  {
    id: 'react',
    name: 'ReAct',
    description: 'Reasoning and acting with tool use capability'
  },
  {
    id: 'cot',
    name: 'Chain of Thought',
    description: 'Step-by-step reasoning process'
  },
  {
    id: 'rag',
    name: 'Retrieval Augmented Generation',
    description: 'Enhances reasoning with document retrieval'
  }
];

// Props Interface
interface ChatInterfaceProps {
  initialMessages?: Message[];
  onSendMessage?: (message: string, modelId: string, reasoningMethod: string | null) => Promise<void>;
  onClearConversation?: () => void;
  isLoading?: boolean;
}

/**
 * A responsive chat interface component for interacting with AI models
 */
const ChatInterface: React.FC<ChatInterfaceProps> = ({
  initialMessages = [],
  onSendMessage,
  onClearConversation,
  isLoading = false
}) => {
  // State
  const [messages, setMessages] = useState<Message[]>(initialMessages);
  const [inputValue, setInputValue] = useState('');
  const [selectedModel, setSelectedModel] = useState<string>(models[0].id);
  const [selectedReasoning, setSelectedReasoning] = useState<string | null>(null);
  const [settingsAnchorEl, setSettingsAnchorEl] = useState<null | HTMLElement>(null);
  const [modelMenuAnchorEl, setModelMenuAnchorEl] = useState<null | HTMLElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Theme and responsive hooks
  const theme = useTheme();
  const isXs = useMediaQuery(theme.breakpoints.only('xs'));
  const isSm = useMediaQuery(theme.breakpoints.only('sm'));
  const padding = useResponsivePadding();
  const fontSize = useResponsiveFontSizes();
  
  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);
  
  // Event Handlers
  const handleSendMessage = async () => {
    if (inputValue.trim() === '') return;
    
    // Create new message
    const newMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: inputValue,
      timestamp: new Date()
    };
    
    // Add message to state
    setMessages(prev => [...prev, newMessage]);
    setInputValue('');
    
    // Call the onSendMessage callback if provided
    if (onSendMessage) {
      await onSendMessage(inputValue, selectedModel, selectedReasoning);
    } else {
      // Demo behavior - simulate response
      simulateResponse(inputValue, selectedModel, selectedReasoning as 'tot' | 'react' | 'cot' | 'rag' | null);
    }
  };
  
  const handleModelChange = (event: SelectChangeEvent<string>) => {
    setSelectedModel(event.target.value);
  };
  
  const handleReasoningChange = (event: SelectChangeEvent<string>) => {
    setSelectedReasoning(event.target.value === 'none' ? null : event.target.value);
  };
  
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };
  
  const handleClearConversation = () => {
    setMessages([]);
    setSettingsAnchorEl(null);
    
    if (onClearConversation) {
      onClearConversation();
    }
  };
  
  const handleSettingsClick = (event: React.MouseEvent<HTMLElement>) => {
    setSettingsAnchorEl(event.currentTarget);
  };
  
  const handleSettingsClose = () => {
    setSettingsAnchorEl(null);
  };
  
  const handleModelMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setModelMenuAnchorEl(event.currentTarget);
  };
  
  const handleModelMenuClose = () => {
    setModelMenuAnchorEl(null);
  };
  
  // Simulate response for demo purposes
  const simulateResponse = (
    input: string, 
    modelId: string,
    reasoning: 'tot' | 'react' | 'cot' | 'rag' | null
  ) => {
    // Demo: Add loading indicator
    const loadingMessage: Message = {
      id: 'loading',
      role: 'assistant',
      content: '...',
      timestamp: new Date()
    };
    
    setMessages(prev => [...prev, loadingMessage]);
    
    // Simulate API delay
    setTimeout(() => {
      // Remove loading message
      setMessages(prev => prev.filter(msg => msg.id !== 'loading'));
      
      // Get model info
      const modelInfo = models.find(m => m.id === modelId);
      
      // Create response based on reasoning method
      let responseContent = '';
      
      if (reasoning === 'tot') {
        responseContent = `[Tree of Thought Reasoning]\n\nLet me explore multiple approaches:\n\nPath 1: ${input} -> This leads to consideration A\nPath 2: ${input} -> This suggests alternative B\nPath 3: ${input} -> This points to possibility C\n\nAfter evaluating all paths, I conclude that Path 2 is most promising because...\n\nFinal answer: Based on this reasoning tree, ${modelInfo?.name} suggests...`;
      } else if (reasoning === 'react') {
        responseContent = `[ReAct Reasoning]\n\nThought: I need to understand "${input}" and determine what actions to take.\nAction: Search for information about ${input.split(' ').slice(0, 3).join(' ')}\nObservation: Found relevant information about...\nThought: Based on this information, I should...\nAction: Calculate the relationship between...\nObservation: The result shows...\n\nFinal answer: After performing these steps, ${modelInfo?.name} concludes that...`;
      } else if (reasoning === 'cot') {
        responseContent = `[Chain of Thought]\n\nStep 1: First, I need to understand what "${input}" is asking.\nStep 2: This requires me to consider several factors...\nStep 3: Taking into account these considerations...\nStep 4: This leads me to conclude...\n\nTherefore, based on this chain of reasoning, ${modelInfo?.name} suggests...`;
      } else if (reasoning === 'rag') {
        responseContent = `[Retrieval Augmented Generation]\n\nQuery: "${input}"\n\nRetrieved Documents:\n1. "Document A" - Contains information about...\n2. "Document B" - Provides context on...\n3. "Document C" - Mentions related concepts...\n\nSynthesizing information from these sources, ${modelInfo?.name} determines that...`;
      } else {
        responseContent = `Based on your question about "${input}", ${modelInfo?.name} would suggest considering the following factors... [Response continues with relevant details to the question]`;
      }
      
      // Add response message
      const responseMessage: Message = {
        id: Date.now().toString(),
        role: 'assistant',
        content: responseContent,
        timestamp: new Date(),
        metadata: {
          reasoning: reasoning,
          model: modelId,
          executionTime: Math.floor(Math.random() * 5000) + 500, // Random time for demo
          documents: reasoning === 'rag' ? ['Document A', 'Document B', 'Document C'] : undefined
        }
      };
      
      setMessages(prev => [...prev, responseMessage]);
    }, 2000); // Simulate 2-second delay
  };
  
  // Helper function to format messages
  const formatMessageContent = (content: string) => {
    // Simple formatting for code blocks, bullet points, etc.
    return content.split('\n').map((line, i) => (
      <React.Fragment key={i}>
        {line}
        <br />
      </React.Fragment>
    ));
  };
  
  // Helper to get avatar letter
  const getAvatarLetter = (role: MessageRole) => {
    if (role === 'user') return 'U';
    if (role === 'assistant') return 'A';
    return 'S';
  };
  
  // Helper to get avatar color
  const getAvatarColor = (role: MessageRole) => {
    if (role === 'user') return theme.palette.primary.main;
    if (role === 'assistant') return theme.palette.secondary.main;
    return theme.palette.warning.main;
  };
  
  // Handle feedback submission
  const handleSubmitFeedback = async (queryId: string, feedback: any) => {
    try {
      await feedbackService.submitFeedback(queryId, feedback);
    } catch (error) {
      console.error('Error submitting feedback:', error);
    }
  };
  
  // Message rendering helper
  const renderMessage = (message: Message, index: number) => {
    const isUser = message.role === 'user';
    const isSystem = message.role === 'system';
    
    return (
      <Box key={message.id}>
        <Box
          sx={{
            display: 'flex',
            flexDirection: isUser ? 'row-reverse' : 'row',
            alignItems: 'flex-start',
            mb: 2,
          }}
        >
          {!isSystem && (
            <Avatar
              sx={{
                bgcolor: getAvatarColor(message.role),
                mr: isUser ? 0 : 2,
                ml: isUser ? 2 : 0,
              }}
            >
              {getAvatarLetter(message.role)}
            </Avatar>
          )}
          
          <Box
            sx={{
              maxWidth: '75%',
              minWidth: isSystem ? '100%' : 'auto',
            }}
          >
            <Paper
              elevation={1}
              sx={{
                p: 2,
                borderRadius: 2,
                backgroundColor: isSystem 
                  ? theme.palette.grey[100] 
                  : isUser 
                    ? theme.palette.primary.light
                    : theme.palette.background.paper,
                color: isUser ? theme.palette.primary.contrastText : 'inherit',
                textAlign: isSystem ? 'center' : 'left',
                width: isSystem ? '100%' : 'auto',
              }}
            >
              <Typography
                variant="body1"
                sx={{
                  fontSize: fontSize.body1.fontSize,
                  whiteSpace: 'pre-wrap',
                }}
              >
                {formatMessageContent(message.content)}
              </Typography>
              
              {message.metadata && (
                <Box sx={{ mt: 1, pt: 1, borderTop: `1px solid ${theme.palette.divider}` }}>
                  {message.metadata.model && (
                    <Typography variant="caption" component="span" sx={{ mr: 1 }}>
                      Model: {message.metadata.model}
                    </Typography>
                  )}
                  
                  {message.metadata.reasoning && (
                    <Typography variant="caption" component="span" sx={{ mr: 1 }}>
                      Method: {reasoningMethods.find(m => m.id === message.metadata?.reasoning)?.name || message.metadata.reasoning}
                    </Typography>
                  )}
                  
                  {message.metadata.executionTime && (
                    <Typography variant="caption" component="span">
                      Time: {(message.metadata.executionTime / 1000).toFixed(1)}s
                    </Typography>
                  )}
                </Box>
              )}
            </Paper>
            
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{
                display: 'block',
                mt: 0.5,
                textAlign: isUser ? 'right' : 'left',
              }}
            >
              {message.timestamp.toLocaleTimeString()}
            </Typography>
          </Box>
        </Box>
        
        {/* Add feedback collector after assistant messages */}
        {message.role === 'assistant' && index === messages.length - 1 && (
          <Box sx={{ ml: 7, mr: 2, mb: 3 }}>
            <FeedbackCollector
              queryId={message.id}
              query={messages.find(m => m.role === 'user' && messages.indexOf(m) < index)?.content || ''}
              model={message.metadata?.model || selectedModel}
              reasoningMethod={message.metadata?.reasoning || selectedReasoning || 'none'}
              onSubmitFeedback={handleSubmitFeedback}
              compact={true}
            />
          </Box>
        )}
      </Box>
    );
  };
  
  return (
    <Paper elevation={3} sx={{ height: '70vh', display: 'flex', flexDirection: 'column' }}>
      {/* Message Header */}
      <Box sx={{ 
        p: 2, 
        borderBottom: `1px solid ${theme.palette.divider}`,
        display: 'flex',
        justifyContent: 'space-between'
      }}>
        <Typography variant="h6">
          Conversation
        </Typography>
        
        <IconButton onClick={handleSettingsClick} size="small">
          <SettingsIcon />
        </IconButton>
        
        <Menu
          anchorEl={settingsAnchorEl}
          open={Boolean(settingsAnchorEl)}
          onClose={handleSettingsClose}
        >
          <MenuItem onClick={handleClearConversation}>
            <DeleteIcon sx={{ mr: 1 }} fontSize="small" />
            Clear conversation
          </MenuItem>
        </Menu>
      </Box>
      
      {/* Messages Container */}
      <Box sx={{ 
        flexGrow: 1, 
        overflow: 'auto',
        p: 2,
        backgroundColor: theme.palette.grey[50]
      }}>
        {messages.length === 0 ? (
          <Box sx={{ 
            display: 'flex', 
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: theme.palette.text.secondary
          }}>
            <Typography variant="body1" sx={{ mb: 1 }}>
              No messages yet
            </Typography>
            <Typography variant="body2">
              Start a conversation by typing a message below
            </Typography>
          </Box>
        ) : (
          messages.map((message, index) => renderMessage(message, index))
        )}
        <div ref={messagesEndRef} />
      </Box>
      
      {/* Input Area */}
      <Box sx={{ 
        p: padding.section.padding, 
        borderTop: `1px solid ${theme.palette.divider}`,
        backgroundColor: theme.palette.background.paper
      }}>
        {/* Model and Reasoning Selection */}
        <Box sx={{ 
          display: 'flex', 
          flexWrap: 'wrap', 
          gap: 1,
          mb: 1.5
        }}>
          <FormControl 
            size="small" 
            sx={{ minWidth: 120, flexGrow: isXs ? 1 : 0 }}
          >
            <InputLabel id="model-select-label">Model</InputLabel>
            <Select
              labelId="model-select-label"
              value={selectedModel}
              label="Model"
              onChange={handleModelChange}
            >
              {models.map((model) => (
                <MenuItem key={model.id} value={model.id}>
                  {model.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          <FormControl 
            size="small" 
            sx={{ minWidth: 150, flexGrow: isXs ? 1 : 0 }}
          >
            <InputLabel id="reasoning-select-label">Reasoning Method</InputLabel>
            <Select
              labelId="reasoning-select-label"
              value={selectedReasoning || 'none'}
              label="Reasoning Method"
              onChange={handleReasoningChange}
            >
              <MenuItem value="none">None</MenuItem>
              {reasoningMethods.map((method) => (
                <MenuItem key={method.id} value={method.id}>
                  {method.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
        
        {/* Message Input */}
        <Box sx={{ 
          display: 'flex',
          alignItems: 'flex-end'
        }}>
          <TextField
            fullWidth
            multiline
            maxRows={4}
            placeholder="Type your message..."
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleKeyPress}
            disabled={isLoading}
            variant="outlined"
            sx={{
              "& .MuiOutlinedInput-root": {
                borderRadius: '16px'
              }
            }}
          />
          
          <Button
            variant="contained"
            color="primary"
            endIcon={isLoading ? <CircularProgress size={24} color="inherit" /> : <SendIcon />}
            onClick={handleSendMessage}
            disabled={inputValue.trim() === '' || isLoading}
            sx={{ ml: 1, minWidth: 100, height: 56 }}
          >
            {isLoading ? 'Sending...' : 'Send'}
          </Button>
        </Box>
      </Box>
    </Paper>
  );
};

export default ChatInterface; 