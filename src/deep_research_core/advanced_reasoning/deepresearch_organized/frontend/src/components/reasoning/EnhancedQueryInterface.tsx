import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  <PERSON>po<PERSON>, 
  TextField, 
  Button, 
  Select, 
  MenuItem, 
  FormControl, 
  InputLabel, 
  Paper, 
  Grid, 
  CircularProgress, 
  Divider,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  FormControlLabel,
  Slider,
  Switch,
  Tabs,
  Tab,
  Tooltip,
  Alert,
  Autocomplete
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Settings as SettingsIcon,
  BarChart as BarChartIcon,
  Save as SaveIcon,
  History as HistoryIcon,
  Bookmark as BookmarkIcon,
  BookmarkBorder as BookmarkBorderIcon,
  Translate as TranslateIcon,
  FormatSize as FormatSizeIcon,
  Help as HelpIcon,
  Mic as MicIcon,
  Code as CodeIcon,
  Send as SendIcon
} from '@mui/icons-material';
import { apiService } from '../../api/apiService';

// Define types
interface QueryOptions {
  temperature: number;
  maxTokens: number;
  responseFormat: string;
  language: string;
  includeReferences: boolean;
  enableGraphVisualization: boolean;
  useCache: boolean;
  detailedReasoning: boolean;
}

interface ReasoningMethod {
  id: string;
  name: string;
  description: string;
  bestFor: string[];
}

interface QueryHistory {
  id: string;
  text: string;
  timestamp: Date;
  model: string;
  reasoningMethod: string;
}

interface QuerySuggestion {
  text: string;
  category: string;
}

interface QueryTemplateCategory {
  name: string;
  templates: string[];
}

const defaultOptions: QueryOptions = {
  temperature: 0.7,
  maxTokens: 1500,
  responseFormat: 'text',
  language: 'en',
  includeReferences: true,
  enableGraphVisualization: true,
  useCache: true,
  detailedReasoning: true
};

const reasoningMethods: ReasoningMethod[] = [
  {
    id: 'tot-rag',
    name: 'Tree of Thought (ToT-RAG)',
    description: 'Explores multiple reasoning paths retrieving information as needed',
    bestFor: ['Complex reasoning', 'Multi-step problem solving', 'Research questions']
  },
  {
    id: 'react',
    name: 'ReAct',
    description: 'Reasoning and acting in an alternating manner',
    bestFor: ['Tool usage', 'Information gathering', 'Dynamic exploration']
  },
  {
    id: 'cot',
    name: 'Chain of Thought',
    description: 'Sequential reasoning with explicit intermediate steps',
    bestFor: ['Step-by-step problems', 'Mathematical reasoning', 'Logical deduction']
  },
  {
    id: 'got',
    name: 'Graph of Thought',
    description: 'Non-linear reasoning with interconnected thoughts',
    bestFor: ['Complex relationships', 'Brainstorming', 'Creative tasks']
  },
  {
    id: 'hyde-rag',
    name: 'HyDE-RAG',
    description: 'Hypothetical document generation for better retrieval',
    bestFor: ['Document-heavy tasks', 'Precise information retrieval', 'Factual questions']
  }
];

const queryTemplateCategories: QueryTemplateCategory[] = [
  {
    name: 'Research',
    templates: [
      'Explain the relationship between [topic1] and [topic2] in the context of [field]',
      'Summarize the current state of research on [topic]',
      'What are the main arguments for and against [theory/position]?',
      'Compare and contrast [approach1] and [approach2] for solving [problem]'
    ]
  },
  {
    name: 'Analysis',
    templates: [
      'Analyze [text/data] and identify key patterns or insights',
      'What are the potential implications of [event/finding] for [domain]?',
      'Evaluate the strengths and weaknesses of [method/approach]',
      'How might [factor] influence or affect [outcome]?'
    ]
  },
  {
    name: 'Problem Solving',
    templates: [
      'What solutions exist for [problem] and what are their tradeoffs?',
      'Design an approach to solve [problem] considering [constraints]',
      'How could [system/process] be optimized for better [metric]?',
      'What are potential ways to mitigate [risk/issue]?'
    ]
  }
];

const commonQuerySuggestions: QuerySuggestion[] = [
  { text: 'Compare tree of thoughts and graph of thoughts for complex reasoning', category: 'Research' },
  { text: 'Explain the implementation of ReAct reasoning in a step-by-step manner', category: 'Technical' },
  { text: 'What are the best practices for implementing RAG systems with Vietnamese text?', category: 'Technical' },
  { text: 'Summarize the latest advances in retrieval-augmented generation', category: 'Research' },
  { text: 'How can I design a multi-stage reasoning system for medical diagnosis?', category: 'Problem Solving' }
];

const EnhancedQueryInterface: React.FC = () => {
  // State variables
  const [query, setQuery] = useState('');
  const [model, setModel] = useState('');
  const [reasoningMethod, setReasoningMethod] = useState('tot-rag');
  const [options, setOptions] = useState<QueryOptions>(defaultOptions);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [optionsDialogOpen, setOptionsDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [templates, setTemplates] = useState<string[]>([]);
  const [queryHistory, setQueryHistory] = useState<QueryHistory[]>([]);
  const [savedQueries, setSavedQueries] = useState<QueryHistory[]>([]);
  const [historyDialogOpen, setHistoryDialogOpen] = useState(false);
  const [templateDialogOpen, setTemplateDialogOpen] = useState(false);
  const [suggestionDialogOpen, setSuggestionDialogOpen] = useState(false);
  const [speechRecognitionActive, setSpeechRecognitionActive] = useState(false);
  const [providers, setProviders] = useState<string[]>([]);
  
  // Fetch available providers on component mount
  useEffect(() => {
    const fetchProviders = async () => {
      try {
        const data = await apiService.getProviders();
        setProviders(data.providers);
        // Set default model to first provider if available
        if (data.providers.length > 0) {
          setModel(data.providers[0]);
        }
      } catch (error) {
        console.error('Failed to fetch providers:', error);
        setError('Failed to load available AI models');
      }
    };
    
    const loadQueryHistory = () => {
      // In a real app, this would load from localStorage or an API
      const mockHistory: QueryHistory[] = [
        {
          id: 'query1',
          text: 'How can I implement a ToT-RAG system with Vietnamese language support?',
          timestamp: new Date(Date.now() - 86400000), // 1 day ago
          model: 'GPT-O1',
          reasoningMethod: 'tot-rag'
        },
        {
          id: 'query2',
          text: 'Compare different reasoning methods for complex knowledge tasks',
          timestamp: new Date(Date.now() - 172800000), // 2 days ago
          model: 'Claude/3',
          reasoningMethod: 'cot'
        },
        {
          id: 'query3',
          text: 'What are the best practices for source attribution in RAG systems?',
          timestamp: new Date(Date.now() - 259200000), // 3 days ago
          model: 'DeepSeek/large',
          reasoningMethod: 'hyde-rag'
        }
      ];
      
      setQueryHistory(mockHistory);
      setSavedQueries([mockHistory[1]]); // Example saved query
    };
    
    fetchProviders();
    loadQueryHistory();
  }, []);

  // Handle query submission
  const handleSubmit = async (event?: React.FormEvent) => {
    if (event) {
      event.preventDefault();
    }
    
    if (!query.trim() || !model || !reasoningMethod) {
      setError('Please fill in all required fields');
      return;
    }
    
    setError(null);
    setIsLoading(true);
    
    try {
      // In a real app, this would call the API
      console.log('Processing query:', {
        query,
        model,
        reasoningMethod,
        options
      });
      
      // Add to history
      const newQuery: QueryHistory = {
        id: `query-${Date.now()}`,
        text: query,
        timestamp: new Date(),
        model,
        reasoningMethod
      };
      
      setQueryHistory(prev => [newQuery, ...prev]);
      
      // Mock processing delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock success
      setIsLoading(false);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      setIsLoading(false);
    }
  };

  // Handle option changes
  const handleOptionChange = (key: keyof QueryOptions, value: any) => {
    setOptions(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Toggle speech recognition
  const toggleSpeechRecognition = () => {
    if (speechRecognitionActive) {
      // Stop recognition
      setSpeechRecognitionActive(false);
    } else {
      // Start recognition
      setSpeechRecognitionActive(true);
      
      // Mock speech recognition
      setTimeout(() => {
        setQuery(prev => prev + " This text was added via speech recognition.");
        setSpeechRecognitionActive(false);
      }, 3000);
    }
  };

  // Handle selecting a template
  const handleSelectTemplate = (template: string) => {
    setQuery(template);
    setTemplateDialogOpen(false);
  };

  // Handle selecting a suggestion
  const handleSelectSuggestion = (suggestion: QuerySuggestion) => {
    setQuery(suggestion.text);
    setSuggestionDialogOpen(false);
  };

  // Handle selecting a history item
  const handleSelectHistoryItem = (item: QueryHistory) => {
    setQuery(item.text);
    setModel(item.model);
    setReasoningMethod(item.reasoningMethod);
    setHistoryDialogOpen(false);
  };

  // Handle saving a query
  const handleSaveQuery = (query: QueryHistory) => {
    setSavedQueries(prev => {
      // Check if already saved
      if (prev.some(item => item.id === query.id)) {
        return prev;
      }
      return [query, ...prev];
    });
  };

  // Handle removing a saved query
  const handleRemoveSavedQuery = (queryId: string) => {
    setSavedQueries(prev => prev.filter(item => item.id !== queryId));
  };

  // Get the selected reasoning method object
  const selectedMethod = reasoningMethods.find(method => method.id === reasoningMethod) || reasoningMethods[0];

  return (
    <Box sx={{ width: '100%', maxWidth: 1200, mx: 'auto' }}>
      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h5" component="h2" gutterBottom>
          Enhanced Query Interface
        </Typography>
        
        <Box component="form" onSubmit={handleSubmit}>
          <Grid container spacing={2}>
            {/* Model Selection */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth variant="outlined" required>
                <InputLabel>Model</InputLabel>
                <Select
                  value={model}
                  onChange={(e) => setModel(e.target.value)}
                  label="Model"
                >
                  {providers.map((provider) => (
                    <MenuItem key={provider} value={provider}>
                      {provider}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            {/* Reasoning Method Selection */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth variant="outlined" required>
                <InputLabel>Reasoning Method</InputLabel>
                <Select
                  value={reasoningMethod}
                  onChange={(e) => setReasoningMethod(e.target.value)}
                  label="Reasoning Method"
                >
                  {reasoningMethods.map((method) => (
                    <MenuItem key={method.id} value={method.id}>
                      {method.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              
              {/* Reasoning Method Info */}
              <Box sx={{ mt: 1 }}>
                <Typography variant="caption" color="text.secondary">
                  {selectedMethod.description}
                </Typography>
                <Box sx={{ mt: 0.5, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selectedMethod.bestFor.map((strength, idx) => (
                    <Chip 
                      key={idx} 
                      label={strength} 
                      size="small" 
                      variant="outlined" 
                      sx={{ fontSize: '0.7rem' }}
                    />
                  ))}
                </Box>
              </Box>
            </Grid>
            
            {/* Query Input Area */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <Button 
                  size="small" 
                  startIcon={<HistoryIcon />} 
                  onClick={() => setHistoryDialogOpen(true)}
                >
                  History
                </Button>
                <Button 
                  size="small" 
                  startIcon={<FormatSizeIcon />} 
                  onClick={() => setTemplateDialogOpen(true)}
                >
                  Templates
                </Button>
                <Tooltip title="Show query suggestions">
                  <Button 
                    size="small" 
                    startIcon={<HelpIcon />} 
                    onClick={() => setSuggestionDialogOpen(true)}
                  >
                    Suggestions
                  </Button>
                </Tooltip>
                <Box sx={{ flex: 1 }} />
                <FormControlLabel
                  control={
                    <Switch
                      size="small"
                      checked={options.language === 'vi'}
                      onChange={() => handleOptionChange('language', options.language === 'en' ? 'vi' : 'en')}
                    />
                  }
                  label={
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <TranslateIcon fontSize="small" sx={{ mr: 0.5 }} />
                      <Typography variant="body2">
                        {options.language === 'en' ? 'English' : 'Vietnamese'}
                      </Typography>
                    </Box>
                  }
                />
              </Box>
              
              <TextField
                fullWidth
                multiline
                rows={6}
                variant="outlined"
                placeholder="Enter your query here..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                InputProps={{
                  endAdornment: (
                    <Box sx={{ display: 'flex', alignItems: 'center', p: 1 }}>
                      <Tooltip title={speechRecognitionActive ? 'Stop dictation' : 'Start dictation'}>
                        <IconButton 
                          onClick={toggleSpeechRecognition}
                          color={speechRecognitionActive ? 'primary' : 'default'}
                        >
                          <MicIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  )
                }}
                required
              />
              
              {speechRecognitionActive && (
                <Alert severity="info" sx={{ mt: 1 }}>
                  Listening... Speak now.
                </Alert>
              )}
            </Grid>
            
            {/* Action Buttons */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 1 }}>
                <Button
                  variant="outlined"
                  startIcon={<SettingsIcon />}
                  onClick={() => setOptionsDialogOpen(true)}
                >
                  Advanced Options
                </Button>
                
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  disabled={isLoading || !query.trim() || !model || !reasoningMethod}
                  startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
                  onClick={() => handleSubmit()}
                >
                  {isLoading ? 'Processing...' : 'Submit Query'}
                </Button>
              </Box>
              
              {error && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  {error}
                </Alert>
              )}
            </Grid>
          </Grid>
        </Box>
      </Paper>
      
      {/* Options Dialog */}
      <Dialog
        open={optionsDialogOpen}
        onClose={() => setOptionsDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Advanced Query Options</DialogTitle>
        <DialogContent>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <Typography gutterBottom>
                Temperature: {options.temperature}
              </Typography>
              <Slider
                value={options.temperature}
                onChange={(_, value) => handleOptionChange('temperature', value)}
                min={0}
                max={1}
                step={0.1}
                marks={[
                  { value: 0, label: 'Precise' },
                  { value: 0.5, label: 'Balanced' },
                  { value: 1, label: 'Creative' }
                ]}
                valueLabelDisplay="auto"
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Typography gutterBottom>
                Max Tokens: {options.maxTokens}
              </Typography>
              <Slider
                value={options.maxTokens}
                onChange={(_, value) => handleOptionChange('maxTokens', value)}
                min={500}
                max={4000}
                step={500}
                marks={[
                  { value: 500, label: '500' },
                  { value: 2000, label: '2000' },
                  { value: 4000, label: '4000' }
                ]}
                valueLabelDisplay="auto"
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Response Format</InputLabel>
                <Select
                  value={options.responseFormat}
                  onChange={(e) => handleOptionChange('responseFormat', e.target.value)}
                  label="Response Format"
                >
                  <MenuItem value="text">Text</MenuItem>
                  <MenuItem value="json">JSON</MenuItem>
                  <MenuItem value="markdown">Markdown</MenuItem>
                  <MenuItem value="structured">Structured</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <Divider sx={{ my: 1 }} />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={options.includeReferences}
                    onChange={(e) => handleOptionChange('includeReferences', e.target.checked)}
                  />
                }
                label="Include references"
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={options.enableGraphVisualization}
                    onChange={(e) => handleOptionChange('enableGraphVisualization', e.target.checked)}
                  />
                }
                label="Enable graph visualization"
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={options.useCache}
                    onChange={(e) => handleOptionChange('useCache', e.target.checked)}
                  />
                }
                label="Use cached results when available"
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={options.detailedReasoning}
                    onChange={(e) => handleOptionChange('detailedReasoning', e.target.checked)}
                  />
                }
                label="Show detailed reasoning steps"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOptionsDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
      
      {/* History Dialog */}
      <Dialog
        open={historyDialogOpen}
        onClose={() => setHistoryDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Query History</DialogTitle>
        <DialogContent>
          <Tabs
            value={activeTab}
            onChange={(_, newValue) => setActiveTab(newValue)}
            sx={{ mb: 2 }}
          >
            <Tab label="Recent Queries" />
            <Tab label="Saved Queries" />
          </Tabs>
          
          {activeTab === 0 && (
            <Box>
              {queryHistory.length === 0 ? (
                <Typography color="text.secondary" align="center" sx={{ py: 4 }}>
                  No query history available
                </Typography>
              ) : (
                <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
                  {queryHistory.map((item) => (
                    <Paper key={item.id} variant="outlined" sx={{ p: 2, mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                        <Box>
                          <Typography variant="subtitle1" sx={{ cursor: 'pointer' }} onClick={() => handleSelectHistoryItem(item)}>
                            {item.text.length > 100 ? `${item.text.substring(0, 100)}...` : item.text}
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                            <Chip size="small" label={item.model} />
                            <Chip size="small" label={item.reasoningMethod} />
                            <Typography variant="caption" color="text.secondary">
                              {item.timestamp.toLocaleString()}
                            </Typography>
                          </Box>
                        </Box>
                        <IconButton 
                          onClick={() => handleSaveQuery(item)}
                          color={savedQueries.some(q => q.id === item.id) ? 'primary' : 'default'}
                        >
                          {savedQueries.some(q => q.id === item.id) ? <BookmarkIcon /> : <BookmarkBorderIcon />}
                        </IconButton>
                      </Box>
                    </Paper>
                  ))}
                </Box>
              )}
            </Box>
          )}
          
          {activeTab === 1 && (
            <Box>
              {savedQueries.length === 0 ? (
                <Typography color="text.secondary" align="center" sx={{ py: 4 }}>
                  No saved queries available
                </Typography>
              ) : (
                <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
                  {savedQueries.map((item) => (
                    <Paper key={item.id} variant="outlined" sx={{ p: 2, mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                        <Box>
                          <Typography variant="subtitle1" sx={{ cursor: 'pointer' }} onClick={() => handleSelectHistoryItem(item)}>
                            {item.text.length > 100 ? `${item.text.substring(0, 100)}...` : item.text}
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                            <Chip size="small" label={item.model} />
                            <Chip size="small" label={item.reasoningMethod} />
                            <Typography variant="caption" color="text.secondary">
                              {item.timestamp.toLocaleString()}
                            </Typography>
                          </Box>
                        </Box>
                        <IconButton onClick={() => handleRemoveSavedQuery(item.id)}>
                          <BookmarkIcon color="primary" />
                        </IconButton>
                      </Box>
                    </Paper>
                  ))}
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setHistoryDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
      
      {/* Templates Dialog */}
      <Dialog
        open={templateDialogOpen}
        onClose={() => setTemplateDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Query Templates</DialogTitle>
        <DialogContent>
          <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
            {queryTemplateCategories.map((category) => (
              <Box key={category.name} sx={{ mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  {category.name}
                </Typography>
                <Divider sx={{ mb: 2 }} />
                
                {category.templates.map((template, idx) => (
                  <Paper key={idx} variant="outlined" sx={{ p: 2, mb: 2, cursor: 'pointer' }} onClick={() => handleSelectTemplate(template)}>
                    <Typography>{template}</Typography>
                  </Paper>
                ))}
              </Box>
            ))}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTemplateDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
      
      {/* Suggestions Dialog */}
      <Dialog
        open={suggestionDialogOpen}
        onClose={() => setSuggestionDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Query Suggestions</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" paragraph>
            Here are some example queries you can try:
          </Typography>
          
          <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
            {commonQuerySuggestions.map((suggestion, idx) => (
              <Paper key={idx} variant="outlined" sx={{ p: 2, mb: 2, cursor: 'pointer' }} onClick={() => handleSelectSuggestion(suggestion)}>
                <Typography>{suggestion.text}</Typography>
                <Chip size="small" label={suggestion.category} sx={{ mt: 1 }} />
              </Paper>
            ))}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSuggestionDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default EnhancedQueryInterface; 