import React, { useRef, useEffect, useState } from 'react';
import { 
  Box, 
  Typography, 
  IconButton, 
  Paper, 
  Slider, 
  FormControlLabel, 
  Switch,
  Tooltip,
  Chip,
  Divider
} from '@mui/material';
import {
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  Refresh as RefreshIcon,
  Save as SaveIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon
} from '@mui/icons-material';
import { ContentCard } from '../common';

// Define types for ReAct data
export interface ReActStep {
  id: string;
  type: 'thought' | 'action' | 'observation';
  content: string;
  timestamp?: string;
  metadata?: Record<string, any>;
}

export interface ReActSequence {
  steps: ReActStep[];
  query: string;
  final_answer?: string;
  metadata?: Record<string, any>;
}

export interface ReActVisualizerProps {
  data: ReActSequence | null;
  title?: string;
  width?: number | string;
  height?: number | string;
  showControls?: boolean;
  onStepClick?: (step: ReActStep) => void;
  loading?: boolean;
}

const ReActVisualizer: React.FC<ReActVisualizerProps> = ({
  data,
  title = 'ReAct Reasoning Visualization',
  width = '100%',
  height = 600,
  showControls = true,
  onStepClick,
  loading = false
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const svgRef = useRef<SVGSVGElement>(null);
  const [zoom, setZoom] = useState<number>(1);
  const [showDetails, setShowDetails] = useState<boolean>(true);
  const [expandedSteps, setExpandedSteps] = useState<Set<string>>(new Set());
  
  // D3 visualization would be implemented here in a full implementation
  useEffect(() => {
    if (!data || !svgRef.current) return;
    
    // Clear previous content
    const svg = svgRef.current;
    while (svg.firstChild) {
      svg.removeChild(svg.firstChild);
    }
    
    // In a real implementation, this would use D3.js to render the sequence flow
    // For now, add a placeholder illustration
    
    // Create a text element to indicate D3 would be used
    const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    text.setAttribute('x', '50%');
    text.setAttribute('y', '50%');
    text.setAttribute('text-anchor', 'middle');
    text.setAttribute('dominant-baseline', 'middle');
    text.setAttribute('fill', '#666');
    text.textContent = 'ReAct flow would be visualized here with D3.js';
    svg.appendChild(text);
    
    // SVG Elements for illustration
    const svgWidth = svg.clientWidth || 800;
    const svgHeight = svg.clientHeight || 400;
    
    // Draw a simple illustration of the ReAct flow
    if (data.steps && data.steps.length > 0) {
      // Calculate positions
      const stepWidth = 120;
      const stepHeight = 60;
      const horizontalGap = 50;
      const startX = Math.max(50, (svgWidth - (data.steps.length * (stepWidth + horizontalGap))) / 2);
      const centerY = svgHeight / 2;
      
      // Draw the steps and connections
      data.steps.forEach((step, index) => {
        const x = startX + index * (stepWidth + horizontalGap);
        const y = centerY;
        
        // Create rectangle for each step
        const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        rect.setAttribute('x', x.toString());
        rect.setAttribute('y', (y - stepHeight/2).toString());
        rect.setAttribute('width', stepWidth.toString());
        rect.setAttribute('height', stepHeight.toString());
        rect.setAttribute('rx', '5');
        
        // Set color based on step type
        let fill = '#4A55BD'; // Default blue
        if (step.type === 'action') {
          fill = '#22C55E'; // Green
        } else if (step.type === 'observation') {
          fill = '#FAAD14'; // Yellow/Orange
        }
        
        rect.setAttribute('fill', fill);
        rect.setAttribute('stroke', '#fff');
        rect.setAttribute('stroke-width', '2');
        svg.appendChild(rect);
        
        // Add type label
        const typeLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        typeLabel.setAttribute('x', (x + stepWidth/2).toString());
        typeLabel.setAttribute('y', (y - stepHeight/2 + 15).toString());
        typeLabel.setAttribute('text-anchor', 'middle');
        typeLabel.setAttribute('fill', '#fff');
        typeLabel.setAttribute('font-weight', 'bold');
        typeLabel.textContent = step.type.charAt(0).toUpperCase() + step.type.slice(1);
        svg.appendChild(typeLabel);
        
        // Add short content preview (first 10 chars)
        const contentPreview = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        contentPreview.setAttribute('x', (x + stepWidth/2).toString());
        contentPreview.setAttribute('y', (y + 5).toString());
        contentPreview.setAttribute('text-anchor', 'middle');
        contentPreview.setAttribute('fill', '#fff');
        contentPreview.textContent = step.content.length > 10 
          ? step.content.substring(0, 10) + '...' 
          : step.content;
        svg.appendChild(contentPreview);
        
        // Add connector line to next step
        if (index < data.steps.length - 1) {
          const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
          line.setAttribute('x1', (x + stepWidth).toString());
          line.setAttribute('y1', y.toString());
          line.setAttribute('x2', (x + stepWidth + horizontalGap).toString());
          line.setAttribute('y2', y.toString());
          line.setAttribute('stroke', '#999');
          line.setAttribute('stroke-width', '2');
          line.setAttribute('marker-end', 'url(#arrowhead)');
          svg.appendChild(line);
        }
      });
      
      // Add arrowhead marker definition
      const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
      const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
      marker.setAttribute('id', 'arrowhead');
      marker.setAttribute('viewBox', '0 0 10 10');
      marker.setAttribute('refX', '5');
      marker.setAttribute('refY', '5');
      marker.setAttribute('markerWidth', '6');
      marker.setAttribute('markerHeight', '6');
      marker.setAttribute('orient', 'auto');
      
      const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
      path.setAttribute('d', 'M 0 0 L 10 5 L 0 10 z');
      path.setAttribute('fill', '#999');
      
      marker.appendChild(path);
      defs.appendChild(marker);
      svg.appendChild(defs);
    }
    
  }, [data, zoom, showDetails]);
  
  const handleZoomIn = () => {
    setZoom(prevZoom => Math.min(prevZoom + 0.2, 3));
  };
  
  const handleZoomOut = () => {
    setZoom(prevZoom => Math.max(prevZoom - 0.2, 0.5));
  };
  
  const handleZoomChange = (event: Event, newValue: number | number[]) => {
    setZoom(newValue as number);
  };
  
  const handleResetView = () => {
    setZoom(1);
  };
  
  const handleToggleDetails = () => {
    setShowDetails(!showDetails);
  };
  
  const toggleStepExpand = (stepId: string) => {
    setExpandedSteps(prev => {
      const newSet = new Set(prev);
      if (newSet.has(stepId)) {
        newSet.delete(stepId);
      } else {
        newSet.add(stepId);
      }
      return newSet;
    });
  };
  
  const handleSaveImage = () => {
    if (!svgRef.current) return;
    
    const svg = svgRef.current;
    const serializer = new XMLSerializer();
    const svgString = serializer.serializeToString(svg);
    const svgBlob = new Blob([svgString], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(svgBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = 'react_visualization.svg';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };
  
  // Color mapping for different step types
  const getStepColor = (type: string) => {
    switch(type) {
      case 'thought': return '#4A55BD';
      case 'action': return '#22C55E';
      case 'observation': return '#FAAD14';
      default: return '#999';
    }
  };
  
  const getStepIcon = (type: string) => {
    switch(type) {
      case 'thought': return '💭';
      case 'action': return '⚡';
      case 'observation': return '👁️';
      default: return '•';
    }
  };
  
  return (
    <ContentCard
      title={title}
      subtitle="Interactive visualization of the ReAct reasoning flow"
      loading={loading}
    >
      <Box sx={{ width, height: 'auto', display: 'flex', flexDirection: 'column' }}>
        {/* Controls */}
        {showControls && (
          <Box sx={{ mb: 2, display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: 1 }}>
            <Tooltip title="Zoom In">
              <IconButton onClick={handleZoomIn} size="small">
                <ZoomInIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Zoom Out">
              <IconButton onClick={handleZoomOut} size="small">
                <ZoomOutIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Reset View">
              <IconButton onClick={handleResetView} size="small">
                <RefreshIcon />
              </IconButton>
            </Tooltip>
            <Box sx={{ width: 150, mx: 2 }}>
              <Slider
                value={zoom}
                min={0.5}
                max={3}
                step={0.1}
                onChange={handleZoomChange}
                aria-labelledby="zoom-slider"
              />
            </Box>
            <FormControlLabel
              control={
                <Switch
                  checked={showDetails}
                  onChange={handleToggleDetails}
                  size="small"
                />
              }
              label="Show Details"
            />
            <Box sx={{ flexGrow: 1 }} />
            <Tooltip title="Save as SVG">
              <IconButton onClick={handleSaveImage} size="small">
                <SaveIcon />
              </IconButton>
            </Tooltip>
          </Box>
        )}
        
        {/* D3 Visualization Area */}
        <Box
          ref={containerRef}
          sx={{
            width: '100%',
            height: '250px',
            border: '1px solid #ddd',
            borderRadius: 1,
            overflow: 'hidden',
            position: 'relative',
            mb: 2
          }}
        >
          <svg
            ref={svgRef}
            width="100%"
            height="100%"
            style={{
              transform: `scale(${zoom})`,
              transformOrigin: 'center',
              transition: 'transform 0.3s ease'
            }}
          />
        </Box>
        
        {/* Sequence Steps List View */}
        {data && showDetails && (
          <Box sx={{ maxHeight: 350, overflow: 'auto', pr: 1 }}>
            {data.query && (
              <Paper sx={{ p: 2, mb: 2, bgcolor: '#f5f5f5' }}>
                <Typography variant="subtitle2" color="textSecondary">Query:</Typography>
                <Typography variant="body1">{data.query}</Typography>
              </Paper>
            )}
            
            {data.steps.map((step, index) => (
              <Paper 
                key={step.id} 
                sx={{ 
                  p: 2, 
                  mb: 2, 
                  borderLeft: `4px solid ${getStepColor(step.type)}`,
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'  
                  }
                }}
                onClick={() => onStepClick && onStepClick(step)}
              >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Chip 
                      label={`${getStepIcon(step.type)} ${step.type.charAt(0).toUpperCase() + step.type.slice(1)}`}
                      size="small"
                      sx={{ 
                        bgcolor: getStepColor(step.type), 
                        color: 'white',
                        fontWeight: 'bold' 
                      }}
                    />
                    <Typography variant="caption" color="textSecondary">
                      Step {index + 1}
                    </Typography>
                  </Box>
                  <IconButton 
                    size="small" 
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleStepExpand(step.id);
                    }}
                  >
                    {expandedSteps.has(step.id) ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                  </IconButton>
                </Box>
                
                <Typography 
                  variant="body2" 
                  sx={{ 
                    whiteSpace: expandedSteps.has(step.id) ? 'normal' : 'nowrap',
                    overflow: expandedSteps.has(step.id) ? 'visible' : 'hidden',
                    textOverflow: 'ellipsis',
                    maxHeight: expandedSteps.has(step.id) ? 'none' : '2.5em'
                  }}
                >
                  {step.content}
                </Typography>
                
                {step.metadata && expandedSteps.has(step.id) && (
                  <Box sx={{ mt: 1, pt: 1, borderTop: '1px dashed #ddd' }}>
                    <Typography variant="caption" color="textSecondary">Metadata:</Typography>
                    <Box sx={{ pl: 1, fontSize: '0.85rem' }}>
                      {Object.entries(step.metadata).map(([key, value]) => (
                        <Typography key={key} variant="body2" sx={{ mt: 0.5 }}>
                          <b>{key}:</b> {JSON.stringify(value)}
                        </Typography>
                      ))}
                    </Box>
                  </Box>
                )}
              </Paper>
            ))}
            
            {data.final_answer && (
              <Paper sx={{ p: 2, mt: 3, mb: 2, bgcolor: '#f0f7ff', borderLeft: '4px solid #1976d2' }}>
                <Typography variant="subtitle2" color="primary">Final Answer:</Typography>
                <Typography variant="body1">{data.final_answer}</Typography>
              </Paper>
            )}
          </Box>
        )}
      </Box>
    </ContentCard>
  );
};

export default ReActVisualizer; 