import React, { useRef, useEffect, useState } from 'react';
import { Box, Typography, IconButton, Paper, Slider, FormControlLabel, Switch } from '@mui/material';
import {
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Save as SaveIcon
} from '@mui/icons-material';
import { ContentCard } from '../common';

// Define types for tree data
export interface TreeNode {
  id: string;
  text: string;
  children?: TreeNode[];
  score?: number;
  metadata?: Record<string, any>;
  type?: 'thought' | 'action' | 'observation' | 'result';
}

export interface TreeVisualizerProps {
  data: TreeNode | null;
  title?: string;
  width?: number | string;
  height?: number | string;
  showControls?: boolean;
  onNodeClick?: (node: TreeNode) => void;
  loading?: boolean;
}

const TreeVisualizer: React.FC<TreeVisualizerProps> = ({
  data,
  title = 'Tree of Thought Visualization',
  width = '100%',
  height = 600,
  showControls = true,
  onNodeClick,
  loading = false
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [zoom, setZoom] = useState<number>(1);
  const [showLabels, setShowLabels] = useState<boolean>(true);
  const [expandAll, setExpandAll] = useState<boolean>(true);
  
  // This state variable is for future implementation of node hover details
  // Will be used when D3 integration is complete
  const [hoveredNode, setHoveredNode] = useState<TreeNode | null>(null);
  
  // Mock rendering of a tree (in a real implementation, use D3.js or a similar library)
  useEffect(() => {
    if (!data || !svgRef.current) return;
    
    // In a real implementation, this would use D3.js to render the tree
    // For now, we'll just add a placeholder
    const svg = svgRef.current;
    
    // Clear previous content
    while (svg.firstChild) {
      svg.removeChild(svg.firstChild);
    }
    
    // Create a text element to show we would render the tree here
    const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    text.setAttribute('x', '50%');
    text.setAttribute('y', '50%');
    text.setAttribute('text-anchor', 'middle');
    text.setAttribute('dominant-baseline', 'middle');
    text.setAttribute('fill', '#666');
    text.textContent = 'Tree visualization would be rendered here using D3.js';
    
    svg.appendChild(text);
    
    // Draw a sample node and connections to illustrate the concept
    const createCircle = (cx: number, cy: number, r: number, fill: string) => {
      const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
      circle.setAttribute('cx', cx.toString());
      circle.setAttribute('cy', cy.toString());
      circle.setAttribute('r', r.toString());
      circle.setAttribute('fill', fill);
      return circle;
    };
    
    const createLine = (x1: number, y1: number, x2: number, y2: number, stroke: string) => {
      const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
      line.setAttribute('x1', x1.toString());
      line.setAttribute('y1', y1.toString());
      line.setAttribute('x2', x2.toString());
      line.setAttribute('y2', y2.toString());
      line.setAttribute('stroke', stroke);
      line.setAttribute('stroke-width', '2');
      return line;
    };
    
    // Root node
    svg.appendChild(createCircle(400, 100, 20, '#4A55BD'));
    
    // First level nodes
    svg.appendChild(createLine(400, 120, 300, 200, '#999'));
    svg.appendChild(createLine(400, 120, 500, 200, '#999'));
    
    svg.appendChild(createCircle(300, 200, 15, '#22C55E'));
    svg.appendChild(createCircle(500, 200, 15, '#FF4D4F'));
    
    // Second level nodes
    svg.appendChild(createLine(300, 215, 250, 300, '#999'));
    svg.appendChild(createLine(300, 215, 350, 300, '#999'));
    
    svg.appendChild(createCircle(250, 300, 12, '#FAAD14'));
    svg.appendChild(createCircle(350, 300, 12, '#13C2C2'));
    
  }, [data, zoom, expandAll, showLabels]);
  
  const handleZoomIn = () => {
    setZoom(prevZoom => Math.min(prevZoom + 0.2, 3));
  };
  
  const handleZoomOut = () => {
    setZoom(prevZoom => Math.max(prevZoom - 0.2, 0.5));
  };
  
  const handleZoomChange = (event: Event, newValue: number | number[]) => {
    setZoom(newValue as number);
  };
  
  const handleResetView = () => {
    setZoom(1);
  };
  
  const handleToggleLabels = () => {
    setShowLabels(!showLabels);
  };
  
  const handleToggleExpand = () => {
    setExpandAll(!expandAll);
  };
  
  const handleSaveImage = () => {
    if (!svgRef.current) return;
    
    const svg = svgRef.current;
    const serializer = new XMLSerializer();
    const svgString = serializer.serializeToString(svg);
    const svgBlob = new Blob([svgString], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(svgBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = 'tree_visualization.svg';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };
  
  // Placeholder for a node details panel that would show when a node is clicked
  const renderNodeDetails = () => {
    if (!hoveredNode) return null;
    
    return (
      <Paper
        elevation={3}
        sx={{
          position: 'absolute',
          bottom: 16,
          right: 16,
          padding: 2,
          maxWidth: 300,
          zIndex: 10
        }}
      >
        <Typography variant="subtitle1" gutterBottom>
          Node Details
        </Typography>
        <Typography variant="body2" gutterBottom>
          <strong>ID:</strong> {hoveredNode.id}
        </Typography>
        <Typography variant="body2" gutterBottom>
          <strong>Text:</strong> {hoveredNode.text}
        </Typography>
        {hoveredNode.score !== undefined && (
          <Typography variant="body2" gutterBottom>
            <strong>Score:</strong> {hoveredNode.score.toFixed(2)}
          </Typography>
        )}
        {hoveredNode.type && (
          <Typography variant="body2" gutterBottom>
            <strong>Type:</strong> {hoveredNode.type}
          </Typography>
        )}
      </Paper>
    );
  };
  
  return (
    <ContentCard
      title={title}
      subtitle="Interactive visualization of the reasoning tree"
      elevation={2}
      loading={loading}
      fullHeight
    >
      <Box
        ref={containerRef}
        sx={{
          position: 'relative',
          width: '100%',
          height: typeof height === 'number' ? `${height}px` : height,
          overflow: 'hidden'
        }}
      >
        {/* Controls */}
        {showControls && (
          <Box
            sx={{
              position: 'absolute',
              top: 16,
              right: 16,
              zIndex: 10,
              display: 'flex',
              flexDirection: 'column',
              gap: 1,
              backgroundColor: 'background.paper',
              borderRadius: 1,
              padding: 1,
              boxShadow: 1
            }}
          >
            <IconButton onClick={handleZoomIn} size="small" title="Zoom In">
              <ZoomInIcon />
            </IconButton>
            <IconButton onClick={handleZoomOut} size="small" title="Zoom Out">
              <ZoomOutIcon />
            </IconButton>
            <IconButton onClick={handleResetView} size="small" title="Reset View">
              <RefreshIcon />
            </IconButton>
            <IconButton onClick={handleToggleExpand} size="small" title={expandAll ? 'Collapse Tree' : 'Expand Tree'}>
              {expandAll ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
            <IconButton onClick={handleSaveImage} size="small" title="Save as SVG">
              <SaveIcon />
            </IconButton>
          </Box>
        )}
        
        {/* Bottom controls */}
        {showControls && (
          <Box
            sx={{
              position: 'absolute',
              bottom: 16,
              left: 16,
              right: 16,
              zIndex: 10,
              display: 'flex',
              alignItems: 'center',
              gap: 2,
              backgroundColor: 'background.paper',
              borderRadius: 1,
              padding: 1,
              boxShadow: 1
            }}
          >
            <Typography variant="body2" sx={{ minWidth: 70 }}>
              Zoom: {zoom.toFixed(1)}x
            </Typography>
            <Slider
              value={zoom}
              min={0.5}
              max={3}
              step={0.1}
              onChange={handleZoomChange}
              sx={{ flex: 1 }}
            />
            <FormControlLabel
              control={
                <Switch
                  checked={showLabels}
                  onChange={handleToggleLabels}
                  size="small"
                />
              }
              label="Labels"
              sx={{ ml: 2 }}
            />
          </Box>
        )}
        
        {/* Tree Visualization SVG */}
        <svg
          ref={svgRef}
          width="100%"
          height="100%"
          viewBox="0 0 800 600"
          preserveAspectRatio="xMidYMid meet"
          style={{
            transform: `scale(${zoom})`,
            transformOrigin: 'center',
            transition: 'transform 0.3s ease'
          }}
        />
        
        {/* Node details panel */}
        {renderNodeDetails()}
        
        {/* No data message */}
        {!data && !loading && (
          <Box
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              textAlign: 'center'
            }}
          >
            <Typography variant="h6" color="text.secondary">
              No data available
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Run a Tree of Thought query to generate visualization data
            </Typography>
          </Box>
        )}
      </Box>
    </ContentCard>
  );
};

export default TreeVisualizer; 