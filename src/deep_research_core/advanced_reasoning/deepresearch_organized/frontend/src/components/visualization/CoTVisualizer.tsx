import React, { useRef, useEffect, useState } from 'react';
import {
  Box,
  Typography,
  IconButton,
  Paper,
  Slider,
  FormControlLabel,
  Switch,
  <PERSON><PERSON><PERSON>,
  Chip,
  Stepper,
  Step,
  Step<PERSON><PERSON>l,
  Step<PERSON>ontent,
  Card,
  CardContent,
  Divider
} from '@mui/material';
import {
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  Refresh as RefreshIcon,
  Save as SaveIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon
} from '@mui/icons-material';
import { ContentCard } from '../common';

// Define types for CoT data
export interface CoTStep {
  id: string;
  content: string;
  confidence?: number;
  timestamp?: string;
  metadata?: Record<string, any>;
}

export interface CoTReasoning {
  steps: CoTStep[];
  query: string;
  answer: string;
  confidence?: number;
  metadata?: Record<string, any>;
}

export interface CoTVisualizerProps {
  data: CoTReasoning | null;
  title?: string;
  width?: number | string;
  height?: number | string;
  showControls?: boolean;
  onStepClick?: (step: CoTStep) => void;
  loading?: boolean;
}

const CoTVisualizer: React.FC<CoTVisualizerProps> = ({
  data,
  title = 'Chain of Thought Visualization',
  width = '100%',
  height = 600,
  showControls = true,
  onStepClick,
  loading = false
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [zoom, setZoom] = useState<number>(1);
  const [showConfidence, setShowConfidence] = useState<boolean>(true);
  const [activeStep, setActiveStep] = useState<number>(0);
  const [expandedSteps, setExpandedSteps] = useState<Set<string>>(new Set());
  
  // D3 visualization would be implemented here in a full implementation
  useEffect(() => {
    if (!data || !svgRef.current) return;
    
    // Clear previous content
    const svg = svgRef.current;
    while (svg.firstChild) {
      svg.removeChild(svg.firstChild);
    }
    
    // In a real implementation, this would use D3.js to render the chain
    // For now, add a placeholder illustration
    
    // Create a text element to indicate D3 would be used
    const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    text.setAttribute('x', '50%');
    text.setAttribute('y', '50%');
    text.setAttribute('text-anchor', 'middle');
    text.setAttribute('dominant-baseline', 'middle');
    text.setAttribute('fill', '#666');
    text.textContent = 'Chain of Thought would be visualized here with D3.js';
    svg.appendChild(text);
    
    // SVG Elements for illustration
    const svgWidth = svg.clientWidth || 800;
    const svgHeight = svg.clientHeight || 400;
    
    // Draw a simple illustration of the CoT flow
    if (data.steps && data.steps.length > 0) {
      // Calculate positions
      const boxWidth = 100;
      const boxHeight = 50;
      const horizontalGap = 40;
      const startX = Math.max(50, (svgWidth - (data.steps.length * (boxWidth + horizontalGap))) / 2);
      const centerY = svgHeight / 2;
      
      // Draw the boxes and connections
      data.steps.forEach((step, index) => {
        const x = startX + index * (boxWidth + horizontalGap);
        const y = centerY;
        
        // Create rectangle for each step
        const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        rect.setAttribute('x', x.toString());
        rect.setAttribute('y', (y - boxHeight/2).toString());
        rect.setAttribute('width', boxWidth.toString());
        rect.setAttribute('height', boxHeight.toString());
        rect.setAttribute('rx', '5');
        rect.setAttribute('fill', index === activeStep ? '#4A55BD' : '#ddd');
        rect.setAttribute('stroke', '#fff');
        rect.setAttribute('stroke-width', '2');
        svg.appendChild(rect);
        
        // Add step number
        const stepNumber = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        stepNumber.setAttribute('x', (x + boxWidth/2).toString());
        stepNumber.setAttribute('y', (y + 5).toString());
        stepNumber.setAttribute('text-anchor', 'middle');
        stepNumber.setAttribute('fill', index === activeStep ? '#fff' : '#333');
        stepNumber.setAttribute('font-weight', 'bold');
        stepNumber.textContent = (index + 1).toString();
        svg.appendChild(stepNumber);
        
        // Add connector line to next step
        if (index < data.steps.length - 1) {
          const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
          line.setAttribute('x1', (x + boxWidth).toString());
          line.setAttribute('y1', y.toString());
          line.setAttribute('x2', (x + boxWidth + horizontalGap).toString());
          line.setAttribute('y2', y.toString());
          line.setAttribute('stroke', '#999');
          line.setAttribute('stroke-width', '2');
          line.setAttribute('marker-end', 'url(#arrowhead)');
          svg.appendChild(line);
        }
        
        // Add confidence indicator if available and enabled
        if (showConfidence && step.confidence !== undefined) {
          const confidenceHeight = boxHeight * 0.6 * step.confidence;
          const confidenceBar = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
          confidenceBar.setAttribute('x', (x + boxWidth + 5).toString());
          confidenceBar.setAttribute('y', (y + boxHeight/2 - confidenceHeight).toString());
          confidenceBar.setAttribute('width', '5');
          confidenceBar.setAttribute('height', confidenceHeight.toString());
          confidenceBar.setAttribute('fill', getConfidenceColor(step.confidence));
          svg.appendChild(confidenceBar);
        }
      });
      
      // Add arrowhead marker definition
      const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
      const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
      marker.setAttribute('id', 'arrowhead');
      marker.setAttribute('viewBox', '0 0 10 10');
      marker.setAttribute('refX', '5');
      marker.setAttribute('refY', '5');
      marker.setAttribute('markerWidth', '6');
      marker.setAttribute('markerHeight', '6');
      marker.setAttribute('orient', 'auto');
      
      const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
      path.setAttribute('d', 'M 0 0 L 10 5 L 0 10 z');
      path.setAttribute('fill', '#999');
      
      marker.appendChild(path);
      defs.appendChild(marker);
      svg.appendChild(defs);
    }
    
  }, [data, zoom, showConfidence, activeStep]);
  
  const handleZoomIn = () => {
    setZoom(prevZoom => Math.min(prevZoom + 0.2, 3));
  };
  
  const handleZoomOut = () => {
    setZoom(prevZoom => Math.max(prevZoom - 0.2, 0.5));
  };
  
  const handleZoomChange = (event: Event, newValue: number | number[]) => {
    setZoom(newValue as number);
  };
  
  const handleResetView = () => {
    setZoom(1);
  };
  
  const handleToggleConfidence = () => {
    setShowConfidence(!showConfidence);
  };
  
  const handleStepChange = (index: number) => {
    setActiveStep(index);
  };
  
  const toggleStepExpand = (stepId: string) => {
    setExpandedSteps(prev => {
      const newSet = new Set(prev);
      if (newSet.has(stepId)) {
        newSet.delete(stepId);
      } else {
        newSet.add(stepId);
      }
      return newSet;
    });
  };
  
  const handleSaveImage = () => {
    if (!svgRef.current) return;
    
    const svg = svgRef.current;
    const serializer = new XMLSerializer();
    const svgString = serializer.serializeToString(svg);
    const svgBlob = new Blob([svgString], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(svgBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = 'cot_visualization.svg';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };
  
  // Helper function to get color based on confidence
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return '#22C55E'; // Green for high confidence
    if (confidence >= 0.5) return '#FAAD14'; // Yellow for medium confidence
    return '#FF4D4F'; // Red for low confidence
  };
  
  return (
    <ContentCard
      title={title}
      subtitle="Step-by-step visualization of the reasoning chain"
      loading={loading}
    >
      <Box sx={{ width, height: 'auto', display: 'flex', flexDirection: 'column' }}>
        {/* Controls */}
        {showControls && (
          <Box sx={{ mb: 2, display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: 1 }}>
            <Tooltip title="Zoom In">
              <IconButton onClick={handleZoomIn} size="small">
                <ZoomInIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Zoom Out">
              <IconButton onClick={handleZoomOut} size="small">
                <ZoomOutIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Reset View">
              <IconButton onClick={handleResetView} size="small">
                <RefreshIcon />
              </IconButton>
            </Tooltip>
            <Box sx={{ width: 150, mx: 2 }}>
              <Slider
                value={zoom}
                min={0.5}
                max={3}
                step={0.1}
                onChange={handleZoomChange}
                aria-labelledby="zoom-slider"
              />
            </Box>
            {data?.steps.some(step => step.confidence !== undefined) && (
              <FormControlLabel
                control={
                  <Switch
                    checked={showConfidence}
                    onChange={handleToggleConfidence}
                    size="small"
                  />
                }
                label="Show Confidence"
              />
            )}
            <Box sx={{ flexGrow: 1 }} />
            <Tooltip title="Save as SVG">
              <IconButton onClick={handleSaveImage} size="small">
                <SaveIcon />
              </IconButton>
            </Tooltip>
          </Box>
        )}
        
        {/* D3 Visualization Area */}
        <Box
          ref={containerRef}
          sx={{
            width: '100%',
            height: '200px',
            border: '1px solid #ddd',
            borderRadius: 1,
            overflow: 'hidden',
            position: 'relative',
            mb: 2
          }}
        >
          <svg
            ref={svgRef}
            width="100%"
            height="100%"
            style={{
              transform: `scale(${zoom})`,
              transformOrigin: 'center',
              transition: 'transform 0.3s ease'
            }}
          />
        </Box>
        
        {/* Query Card */}
        {data?.query && (
          <Paper 
            elevation={0} 
            sx={{ 
              p: 2, 
              mb: 3, 
              bgcolor: '#f5f5f5', 
              border: '1px solid #e0e0e0',
              borderRadius: 2
            }}
          >
            <Typography variant="subtitle2" color="textSecondary" gutterBottom>
              Query:
            </Typography>
            <Typography variant="body1">
              {data.query}
            </Typography>
          </Paper>
        )}
        
        {/* Stepper View */}
        {data?.steps && (
          <Stepper 
            activeStep={activeStep} 
            orientation="vertical" 
            sx={{ mb: 3 }}
            nonLinear
          >
            {data.steps.map((step, index) => (
              <Step key={step.id} completed={false}>
                <StepLabel 
                  onClick={() => handleStepChange(index)}
                  sx={{ cursor: 'pointer' }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="subtitle2">
                      Step {index + 1}
                    </Typography>
                    {step.confidence !== undefined && showConfidence && (
                      <Chip 
                        label={`${Math.round(step.confidence * 100)}%`}
                        size="small"
                        sx={{ 
                          bgcolor: getConfidenceColor(step.confidence),
                          color: 'white',
                          fontSize: '0.7rem',
                          height: 20
                        }}
                      />
                    )}
                    <IconButton 
                      size="small" 
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleStepExpand(step.id);
                      }}
                      sx={{ ml: 'auto' }}
                    >
                      {expandedSteps.has(step.id) ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    </IconButton>
                  </Box>
                </StepLabel>
                <StepContent>
                  <Typography 
                    variant="body2"
                    sx={{ 
                      whiteSpace: expandedSteps.has(step.id) ? 'normal' : 'nowrap',
                      overflow: expandedSteps.has(step.id) ? 'visible' : 'hidden',
                      textOverflow: 'ellipsis',
                      maxHeight: expandedSteps.has(step.id) ? 'none' : '2.5em',
                      mb: 1
                    }}
                  >
                    {step.content}
                  </Typography>
                  
                  {step.metadata && expandedSteps.has(step.id) && (
                    <Box sx={{ mt: 1, pt: 1, borderTop: '1px dashed #ddd' }}>
                      <Typography variant="caption" color="textSecondary">Metadata:</Typography>
                      <Box sx={{ pl: 1, fontSize: '0.85rem' }}>
                        {Object.entries(step.metadata).map(([key, value]) => (
                          <Typography key={key} variant="body2" sx={{ mt: 0.5 }}>
                            <b>{key}:</b> {JSON.stringify(value)}
                          </Typography>
                        ))}
                      </Box>
                    </Box>
                  )}
                </StepContent>
              </Step>
            ))}
          </Stepper>
        )}
        
        {/* Final Answer */}
        {data?.answer && (
          <Card sx={{ mb: 2, borderLeft: '4px solid #4A55BD' }}>
            <CardContent>
              <Typography variant="subtitle1" color="primary" gutterBottom>
                Final Answer
                {data.confidence !== undefined && (
                  <Chip 
                    label={`${Math.round(data.confidence * 100)}% Confidence`}
                    size="small"
                    sx={{ 
                      ml: 1,
                      bgcolor: getConfidenceColor(data.confidence),
                      color: 'white'
                    }}
                  />
                )}
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <Typography variant="body1">
                {data.answer}
              </Typography>
              
              {data.metadata && (
                <Box sx={{ mt: 2, pt: 1, borderTop: '1px dashed #ddd' }}>
                  <Typography variant="caption" color="textSecondary">
                    Answer Metadata:
                  </Typography>
                  <Box sx={{ pl: 1, fontSize: '0.85rem' }}>
                    {Object.entries(data.metadata).map(([key, value]) => (
                      <Typography key={key} variant="body2" sx={{ mt: 0.5 }}>
                        <b>{key}:</b> {JSON.stringify(value)}
                      </Typography>
                    ))}
                  </Box>
                </Box>
              )}
            </CardContent>
          </Card>
        )}
      </Box>
    </ContentCard>
  );
};

export default CoTVisualizer; 