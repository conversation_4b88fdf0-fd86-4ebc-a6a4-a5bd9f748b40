import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import TreeVisualizer from '../TreeVisualizer';

// Create a wrapper component that provides theme context
const Wrapper = ({ children }: { children: React.ReactNode }) => {
  const theme = createTheme();
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn()
}));

describe('TreeVisualizer Component', () => {
  // Sample data for testing
  const sampleData = {
    id: 'root',
    label: 'Root Node',
    children: [
      {
        id: 'child1',
        label: 'Child 1',
        children: [
          { 
            id: 'grandchild1',
            label: 'Grandchild 1',
            children: []
          }
        ]
      },
      {
        id: 'child2',
        label: 'Child 2',
        children: []
      }
    ]
  };

  test('renders with loading state', () => {
    render(
      <Wrapper>
        <TreeVisualizer data={null} loading={true} />
      </Wrapper>
    );
    
    // Should show loading indicator
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(screen.getByText(/loading/i)).toBeInTheDocument();
  });

  test('renders with no data state', () => {
    render(
      <Wrapper>
        <TreeVisualizer data={null} loading={false} />
      </Wrapper>
    );
    
    expect(screen.getByText(/no data available/i)).toBeInTheDocument();
  });

  test('renders tree visualization when data is provided', () => {
    render(
      <Wrapper>
        <TreeVisualizer data={sampleData} loading={false} />
      </Wrapper>
    );
    
    // Should display the visualization container
    expect(screen.getByTestId('tree-visualization-container')).toBeInTheDocument();
  });

  test('renders control panel with buttons', () => {
    render(
      <Wrapper>
        <TreeVisualizer data={sampleData} loading={false} />
      </Wrapper>
    );
    
    // Control panel buttons should be present
    expect(screen.getByLabelText(/zoom in/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/zoom out/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/reset view/i)).toBeInTheDocument();
  });

  test('handles zoom in button click', () => {
    render(
      <Wrapper>
        <TreeVisualizer data={sampleData} loading={false} />
      </Wrapper>
    );
    
    const zoomInButton = screen.getByLabelText(/zoom in/i);
    fireEvent.click(zoomInButton);
    
    // We can't easily test the visual zoom effect, but we can ensure the button is clickable
    expect(zoomInButton).not.toBeDisabled();
  });

  test('handles zoom out button click', () => {
    render(
      <Wrapper>
        <TreeVisualizer data={sampleData} loading={false} />
      </Wrapper>
    );
    
    const zoomOutButton = screen.getByLabelText(/zoom out/i);
    fireEvent.click(zoomOutButton);
    
    // We can't easily test the visual zoom effect, but we can ensure the button is clickable
    expect(zoomOutButton).not.toBeDisabled();
  });

  test('handles reset view button click', () => {
    render(
      <Wrapper>
        <TreeVisualizer data={sampleData} loading={false} />
      </Wrapper>
    );
    
    const resetButton = screen.getByLabelText(/reset view/i);
    fireEvent.click(resetButton);
    
    // We can't easily test the visual reset effect, but we can ensure the button is clickable
    expect(resetButton).not.toBeDisabled();
  });

  test('applies custom height when provided', () => {
    const { container } = render(
      <Wrapper>
        <TreeVisualizer data={sampleData} loading={false} height={600} />
      </Wrapper>
    );
    
    const visualizationContainer = screen.getByTestId('tree-visualization-container');
    expect(visualizationContainer).toHaveStyle('height: 600px');
  });

  test('calls onNodeClick when node is clicked', () => {
    const handleNodeClick = jest.fn();
    render(
      <Wrapper>
        <TreeVisualizer 
          data={sampleData} 
          loading={false}
          onNodeClick={handleNodeClick}
        />
      </Wrapper>
    );
    
    // Note: Due to how D3 renders, we can't easily test the node click directly
    // This is a limitation of the testing environment
    // In a real test, we would need to mock D3 or use a more complex setup
    
    // Instead, we'll just verify the prop is passed
    expect(handleNodeClick).toBeDefined();
  });
}); 