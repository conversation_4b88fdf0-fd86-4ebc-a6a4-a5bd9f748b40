import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CollectionManager from '../CollectionManager';

// Create a wrapper component that provides theme context
const Wrapper = ({ children }: { children: React.ReactNode }) => {
  const theme = createTheme();
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

// Mock data
const mockCollections = [
  { id: '1', name: 'Research Papers', documentCount: 5 },
  { id: '2', name: 'Financial Documents', documentCount: 3 },
];

// Mock functions
const mockOnCreateCollection = jest.fn();
const mockOnUpdateCollection = jest.fn();
const mockOnDeleteCollection = jest.fn();

describe('CollectionManager Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders manage collections button correctly', () => {
    render(
      <Wrapper>
        <CollectionManager 
          collections={mockCollections} 
          onCreateCollection={mockOnCreateCollection}
          onUpdateCollection={mockOnUpdateCollection}
          onDeleteCollection={mockOnDeleteCollection}
        />
      </Wrapper>
    );
    
    const manageButton = screen.getByText(/manage collections/i);
    expect(manageButton).toBeInTheDocument();
  });

  test('opens dialog when manage button is clicked', () => {
    render(
      <Wrapper>
        <CollectionManager 
          collections={mockCollections} 
          onCreateCollection={mockOnCreateCollection}
          onUpdateCollection={mockOnUpdateCollection}
          onDeleteCollection={mockOnDeleteCollection}
        />
      </Wrapper>
    );
    
    // Dialog should not be visible initially
    expect(screen.queryByText(/manage document collections/i)).not.toBeInTheDocument();
    
    // Click manage button
    const manageButton = screen.getByText(/manage collections/i);
    fireEvent.click(manageButton);
    
    // Dialog should be visible
    expect(screen.getByText(/manage document collections/i)).toBeInTheDocument();
  });

  test('displays all collections in the dialog', () => {
    render(
      <Wrapper>
        <CollectionManager 
          collections={mockCollections} 
          onCreateCollection={mockOnCreateCollection}
          onUpdateCollection={mockOnUpdateCollection}
          onDeleteCollection={mockOnDeleteCollection}
        />
      </Wrapper>
    );
    
    // Open dialog
    const manageButton = screen.getByText(/manage collections/i);
    fireEvent.click(manageButton);
    
    // Check if collections are displayed
    expect(screen.getByText('Research Papers')).toBeInTheDocument();
    expect(screen.getByText('Financial Documents')).toBeInTheDocument();
    expect(screen.getByText('(5 documents)')).toBeInTheDocument();
    expect(screen.getByText('(3 documents)')).toBeInTheDocument();
  });

  test('shows add collection form when add button is clicked', () => {
    render(
      <Wrapper>
        <CollectionManager 
          collections={mockCollections} 
          onCreateCollection={mockOnCreateCollection}
          onUpdateCollection={mockOnUpdateCollection}
          onDeleteCollection={mockOnDeleteCollection}
        />
      </Wrapper>
    );
    
    // Open dialog
    const manageButton = screen.getByText(/manage collections/i);
    fireEvent.click(manageButton);
    
    // Click add collection button
    const addButton = screen.getByText(/add collection/i);
    fireEvent.click(addButton);
    
    // Check if form is displayed
    expect(screen.getByText(/create new collection/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/collection name/i)).toBeInTheDocument();
  });

  test('calls onCreateCollection when adding a new collection', async () => {
    render(
      <Wrapper>
        <CollectionManager 
          collections={mockCollections} 
          onCreateCollection={mockOnCreateCollection}
          onUpdateCollection={mockOnUpdateCollection}
          onDeleteCollection={mockOnDeleteCollection}
        />
      </Wrapper>
    );
    
    // Open dialog
    const manageButton = screen.getByText(/manage collections/i);
    fireEvent.click(manageButton);
    
    // Click add collection button
    const addButton = screen.getByText(/add collection/i);
    fireEvent.click(addButton);
    
    // Fill out form
    const nameInput = screen.getByLabelText(/collection name/i);
    fireEvent.change(nameInput, { target: { value: 'New Test Collection' } });
    
    // Submit form
    const createButton = screen.getByText(/create/i);
    fireEvent.click(createButton);
    
    // Check if onCreateCollection was called
    await waitFor(() => {
      expect(mockOnCreateCollection).toHaveBeenCalledWith('New Test Collection');
    });
  });

  test('shows edit collection form when edit button is clicked', () => {
    render(
      <Wrapper>
        <CollectionManager 
          collections={mockCollections} 
          onCreateCollection={mockOnCreateCollection}
          onUpdateCollection={mockOnUpdateCollection}
          onDeleteCollection={mockOnDeleteCollection}
        />
      </Wrapper>
    );
    
    // Open dialog
    const manageButton = screen.getByText(/manage collections/i);
    fireEvent.click(manageButton);
    
    // Find and click edit button for first collection
    const editButtons = screen.getAllByLabelText(/edit collection/i);
    fireEvent.click(editButtons[0]);
    
    // Check if edit form is displayed
    expect(screen.getByText(/edit collection/i)).toBeInTheDocument();
    const nameInput = screen.getByLabelText(/collection name/i);
    expect(nameInput).toHaveValue('Research Papers');
  });

  test('calls onUpdateCollection when saving edited collection', async () => {
    render(
      <Wrapper>
        <CollectionManager 
          collections={mockCollections} 
          onCreateCollection={mockOnCreateCollection}
          onUpdateCollection={mockOnUpdateCollection}
          onDeleteCollection={mockOnDeleteCollection}
        />
      </Wrapper>
    );
    
    // Open dialog
    const manageButton = screen.getByText(/manage collections/i);
    fireEvent.click(manageButton);
    
    // Find and click edit button for first collection
    const editButtons = screen.getAllByLabelText(/edit collection/i);
    fireEvent.click(editButtons[0]);
    
    // Edit name
    const nameInput = screen.getByLabelText(/collection name/i);
    fireEvent.change(nameInput, { target: { value: 'Updated Research Papers' } });
    
    // Save changes
    const saveButton = screen.getByText(/save/i);
    fireEvent.click(saveButton);
    
    // Check if onUpdateCollection was called
    await waitFor(() => {
      expect(mockOnUpdateCollection).toHaveBeenCalledWith('1', 'Updated Research Papers');
    });
  });

  test('shows delete confirmation when delete button is clicked', () => {
    render(
      <Wrapper>
        <CollectionManager 
          collections={mockCollections} 
          onCreateCollection={mockOnCreateCollection}
          onUpdateCollection={mockOnUpdateCollection}
          onDeleteCollection={mockOnDeleteCollection}
        />
      </Wrapper>
    );
    
    // Open dialog
    const manageButton = screen.getByText(/manage collections/i);
    fireEvent.click(manageButton);
    
    // Find and click delete button for first collection
    const deleteButtons = screen.getAllByLabelText(/delete collection/i);
    fireEvent.click(deleteButtons[0]);
    
    // Check if confirmation dialog is displayed
    expect(screen.getByText(/are you sure you want to delete this collection/i)).toBeInTheDocument();
  });

  test('calls onDeleteCollection when confirming deletion', async () => {
    render(
      <Wrapper>
        <CollectionManager 
          collections={mockCollections} 
          onCreateCollection={mockOnCreateCollection}
          onUpdateCollection={mockOnUpdateCollection}
          onDeleteCollection={mockOnDeleteCollection}
        />
      </Wrapper>
    );
    
    // Open dialog
    const manageButton = screen.getByText(/manage collections/i);
    fireEvent.click(manageButton);
    
    // Find and click delete button for first collection
    const deleteButtons = screen.getAllByLabelText(/delete collection/i);
    fireEvent.click(deleteButtons[0]);
    
    // Confirm deletion
    const confirmButton = screen.getByText(/delete/i);
    fireEvent.click(confirmButton);
    
    // Check if onDeleteCollection was called
    await waitFor(() => {
      expect(mockOnDeleteCollection).toHaveBeenCalledWith('1');
    });
  });
}); 