import React, { useState } from 'react';
import {
  Box,
  <PERSON>ton,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  TextField,
  Typography,
  Divider,
  Tooltip,
} from '@mui/material';
import {
  Create as CreateIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Folder as FolderIcon,
} from '@mui/icons-material';
import { Collection } from '../../types';

interface CollectionManagerProps {
  collections: Collection[];
  onCreateCollection: (name: string) => Promise<void>;
  onUpdateCollection: (id: string, name: string) => Promise<void>;
  onDeleteCollection: (id: string) => Promise<void>;
}

const CollectionManager: React.FC<CollectionManagerProps> = ({
  collections,
  onCreateCollection,
  onUpdateCollection,
  onDeleteCollection,
}) => {
  const [open, setOpen] = useState(false);
  const [newCollectionName, setNewCollectionName] = useState('');
  const [editingCollection, setEditingCollection] = useState<Collection | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const handleClickOpen = () => {
    setOpen(true);
    setNewCollectionName('');
    setEditingCollection(null);
    setError(null);
  };
  
  const handleClose = () => {
    if (!isSubmitting) {
      setOpen(false);
    }
  };
  
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setNewCollectionName(event.target.value);
    setError(null);
  };
  
  const handleCreateCollection = async () => {
    if (!newCollectionName.trim()) {
      setError('Collection name cannot be empty');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      await onCreateCollection(newCollectionName.trim());
      setNewCollectionName('');
    } catch (error) {
      console.error('Error creating collection:', error);
      setError('Failed to create collection. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const handleEditClick = (collection: Collection) => {
    setEditingCollection(collection);
    setNewCollectionName(collection.name);
    setError(null);
  };
  
  const handleUpdateCollection = async () => {
    if (!editingCollection) return;
    
    if (!newCollectionName.trim()) {
      setError('Collection name cannot be empty');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      await onUpdateCollection(editingCollection.id, newCollectionName.trim());
      setEditingCollection(null);
      setNewCollectionName('');
    } catch (error) {
      console.error('Error updating collection:', error);
      setError('Failed to update collection. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const handleDeleteClick = async (collectionId: string) => {
    if (window.confirm('Are you sure you want to delete this collection? Documents in this collection will be moved to Uncategorized.')) {
      setIsSubmitting(true);
      
      try {
        await onDeleteCollection(collectionId);
      } catch (error) {
        console.error('Error deleting collection:', error);
      } finally {
        setIsSubmitting(false);
      }
    }
  };
  
  const handleCancelEdit = () => {
    setEditingCollection(null);
    setNewCollectionName('');
    setError(null);
  };
  
  return (
    <>
      <Button 
        variant="outlined" 
        startIcon={<CreateIcon />}
        onClick={handleClickOpen}
      >
        Manage Collections
      </Button>
      
      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Manage Document Collections</DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ mb: 3 }}>
            Create and manage collections to organize your documents.
          </DialogContentText>
          
          <Box sx={{ mb: 3 }}>
            <TextField
              label={editingCollection ? 'Edit Collection Name' : 'New Collection Name'}
              fullWidth
              value={newCollectionName}
              onChange={handleInputChange}
              error={!!error}
              helperText={error}
              disabled={isSubmitting}
              autoFocus
            />
            <Box sx={{ mt: 1, display: 'flex', justifyContent: 'flex-end' }}>
              {editingCollection ? (
                <>
                  <Button onClick={handleCancelEdit} disabled={isSubmitting} sx={{ mr: 1 }}>
                    Cancel
                  </Button>
                  <Button
                    variant="contained"
                    onClick={handleUpdateCollection}
                    disabled={isSubmitting || !newCollectionName.trim()}
                  >
                    Update
                  </Button>
                </>
              ) : (
                <Button
                  variant="contained"
                  onClick={handleCreateCollection}
                  disabled={isSubmitting || !newCollectionName.trim()}
                >
                  Create
                </Button>
              )}
            </Box>
          </Box>
          
          <Divider sx={{ my: 2 }} />
          
          <Typography variant="subtitle1" gutterBottom>
            Existing Collections
          </Typography>
          
          {collections.length === 0 ? (
            <Typography variant="body2" color="textSecondary">
              No collections yet. Create your first collection above.
            </Typography>
          ) : (
            <List>
              {collections.map((collection) => (
                <ListItem key={collection.id}>
                  <FolderIcon sx={{ mr: 2, color: 'primary.main' }} />
                  <ListItemText
                    primary={collection.name}
                    secondary={`${collection.documentCount} document${collection.documentCount !== 1 ? 's' : ''}`}
                  />
                  <ListItemSecondaryAction>
                    <Tooltip title="Edit">
                      <IconButton
                        edge="end"
                        onClick={() => handleEditClick(collection)}
                        disabled={isSubmitting}
                        size="small"
                        sx={{ mr: 1 }}
                      >
                        <EditIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete">
                      <IconButton
                        edge="end"
                        onClick={() => handleDeleteClick(collection.id)}
                        disabled={isSubmitting}
                        size="small"
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} disabled={isSubmitting}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default CollectionManager; 