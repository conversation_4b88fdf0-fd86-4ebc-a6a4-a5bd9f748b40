import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { CssBaseline } from '@mui/material';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import ThemeProvider from './theme/ThemeContext';
import ToastProvider from './context/ToastContext';
import MainLayout from './layouts/MainLayout';
import { LanguageProvider } from './i18n/LanguageContext';

// Import pages
import Dashboard from './pages/Dashboard';
import QueryPage from './pages/QueryPage';
import AnalyticsPage from './pages/AnalyticsPage';
import VisualizationsPage from './pages/VisualizationsPage';
import DocumentsPage from './pages/DocumentsPage';
import ResponsiveTestPage from './pages/ResponsiveTestPage';
import ChatDemo from './pages/ChatDemo';
import AccessibilityPage from './pages/AccessibilityPage';

// Placeholder components for demonstration
const SettingsPage = () => <div>Settings Page</div>;

// Create a query client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
    },
  },
});

const App: React.FC = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <LanguageProvider>
          <ToastProvider>
            <CssBaseline />
            <BrowserRouter>
              <Routes>
                <Route path="/" element={
                  <MainLayout>
                    <Dashboard />
                  </MainLayout>
                } />
                <Route path="/query" element={
                  <MainLayout>
                    <QueryPage />
                  </MainLayout>
                } />
                <Route path="/documents" element={
                  <MainLayout>
                    <DocumentsPage />
                  </MainLayout>
                } />
                <Route path="/visualizations" element={
                  <MainLayout>
                    <VisualizationsPage />
                  </MainLayout>
                } />
                <Route path="/analytics" element={
                  <MainLayout>
                    <AnalyticsPage />
                  </MainLayout>
                } />
                <Route path="/settings" element={
                  <MainLayout title="Settings">
                    <SettingsPage />
                  </MainLayout>
                } />
                <Route path="/responsive-test" element={
                  <MainLayout title="Responsive Design Testing">
                    <ResponsiveTestPage />
                  </MainLayout>
                } />
                <Route path="/chat-demo" element={
                  <MainLayout title="Chat Interface Demo">
                    <ChatDemo />
                  </MainLayout>
                } />
                <Route path="/accessibility" element={
                  <MainLayout title="Accessibility Settings">
                    <AccessibilityPage />
                  </MainLayout>
                } />
              </Routes>
            </BrowserRouter>
          </ToastProvider>
        </LanguageProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
};

export default App; 