import { createTheme, ThemeOptions } from '@mui/material/styles';
import { tokens } from './tokens';

// Create a theme instance.
const themeOptions: ThemeOptions = {
  palette: {
    mode: 'light',
    primary: tokens.colors.primary,
    secondary: tokens.colors.secondary,
    error: tokens.colors.error,
    warning: tokens.colors.warning,
    info: tokens.colors.info,
    success: tokens.colors.success,
    background: {
      default: tokens.colors.background.default,
      paper: tokens.colors.background.paper,
    },
    text: {
      primary: tokens.colors.text.primary,
      secondary: tokens.colors.text.secondary,
      disabled: tokens.colors.text.disabled,
    },
    action: tokens.colors.action,
  },
  typography: {
    fontFamily: tokens.typography.fontFamily,
    fontWeightLight: tokens.typography.fontWeightLight,
    fontWeightRegular: tokens.typography.fontWeightRegular,
    fontWeightMedium: tokens.typography.fontWeightMedium,
    fontWeightBold: tokens.typography.fontWeightBold,
    h1: tokens.typography.h1,
    h2: tokens.typography.h2,
    h3: tokens.typography.h3,
    h4: tokens.typography.h4,
    h5: tokens.typography.h5,
    h6: tokens.typography.h6,
    subtitle1: tokens.typography.subtitle1,
    subtitle2: tokens.typography.subtitle2,
    body1: tokens.typography.body1,
    body2: tokens.typography.body2,
    button: tokens.typography.button,
    caption: tokens.typography.caption,
    overline: tokens.typography.overline,
  },
  spacing: tokens.spacing.unit,
  shape: {
    borderRadius: 8, // Default border radius from our tokens
  },
  shadows: [
    'none',
    tokens.shadows.xs,
    tokens.shadows.sm,
    tokens.shadows.sm,
    tokens.shadows.md,
    tokens.shadows.md,
    tokens.shadows.md,
    tokens.shadows.md,
    tokens.shadows.lg,
    tokens.shadows.lg,
    tokens.shadows.lg,
    tokens.shadows.lg,
    tokens.shadows.lg,
    tokens.shadows.xl,
    tokens.shadows.xl,
    tokens.shadows.xl,
    tokens.shadows.xl,
    tokens.shadows.xl,
    tokens.shadows.xxl,
    tokens.shadows.xxl,
    tokens.shadows.xxl,
    tokens.shadows.xxl,
    tokens.shadows.xxl,
    tokens.shadows.xxl,
    tokens.shadows.xxl,
  ],
  transitions: {
    easing: tokens.transitions.easing,
    duration: tokens.transitions.duration,
  },
  zIndex: tokens.zIndex,
  components: {
    // Override default component styles
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none' as 'none',
          borderRadius: tokens.shape.borderRadius.medium,
          fontWeight: tokens.typography.fontWeightSemiBold,
          padding: `${tokens.spacing.xsmall} ${tokens.spacing.small}`,
        },
        containedPrimary: {
          '&:hover': {
            backgroundColor: tokens.colors.primary.dark,
          },
        },
        containedSecondary: {
          '&:hover': {
            backgroundColor: tokens.colors.secondary.dark,
          },
        },
        outlinedPrimary: {
          borderColor: tokens.colors.primary.main,
          '&:hover': {
            backgroundColor: 'rgba(37, 99, 235, 0.04)',
          },
        },
        textPrimary: {
          '&:hover': {
            backgroundColor: 'rgba(37, 99, 235, 0.04)',
          },
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: tokens.shape.borderRadius.medium,
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderWidth: '2px',
            },
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: tokens.shape.borderRadius.medium,
          boxShadow: tokens.shadows.sm,
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        rounded: {
          borderRadius: tokens.shape.borderRadius.medium,
        },
        elevation1: {
          boxShadow: tokens.shadows.sm,
        },
        elevation2: {
          boxShadow: tokens.shadows.md,
        },
        elevation3: {
          boxShadow: tokens.shadows.lg,
        },
        elevation4: {
          boxShadow: tokens.shadows.xl,
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          boxShadow: tokens.shadows.sm,
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          padding: tokens.spacing.small,
        },
        head: {
          fontWeight: tokens.typography.fontWeightSemiBold,
          backgroundColor: tokens.colors.neutral[50],
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: tokens.shape.borderRadius.full,
        },
      },
    },
    MuiAlert: {
      styleOverrides: {
        root: {
          borderRadius: tokens.shape.borderRadius.medium,
        },
      },
    },
    MuiTooltip: {
      styleOverrides: {
        tooltip: {
          backgroundColor: tokens.colors.neutral[800],
          fontSize: tokens.typography.caption.fontSize,
          padding: `${tokens.spacing.xxsmall} ${tokens.spacing.xsmall}`,
          borderRadius: tokens.shape.borderRadius.small,
        },
      },
    },
  },
};

// Create light and dark themes
export const lightTheme = createTheme(themeOptions);

export const darkTheme = createTheme({
  ...themeOptions,
  palette: {
    ...themeOptions.palette,
    mode: 'dark',
    background: {
      default: tokens.colors.background.dark,
      paper: tokens.colors.neutral[800],
    },
    text: {
      primary: tokens.colors.neutral[100],
      secondary: tokens.colors.neutral[300],
      disabled: tokens.colors.neutral[500],
    },
  },
});

export default lightTheme; 