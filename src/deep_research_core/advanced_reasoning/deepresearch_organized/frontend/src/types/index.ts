// Document Management Types
export interface Document {
  id: string;
  name: string;
  type: string;
  size: number;
  upload_date: string;
  tags?: string[];
  collection?: string;
  status?: 'indexed' | 'processing' | 'error';
  chunks?: number;
  last_used?: string;
  last_query?: string;
  vector_store?: string;
}

export interface Collection {
  id: string;
  name: string;
  documentCount: number;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: string;
}

export interface ProviderInfo {
  providers: string[];
}

export interface HealthCheckResponse {
  status: string;
}

export interface VisualizationRequest {
  result: {
    query: string;
    best_paths: Array<any>;
    retrieved_documents?: Array<any>;
    conflicts?: Record<string, any>;
    metadata?: Record<string, any>;
  };
  filename_prefix?: string;
}

export interface VisualizationResponse {
  html_url: string;
  image_urls: string[];
  message: string;
}

export interface TreeData {
  name: string;
  children?: TreeData[];
  [key: string]: any;
}

export interface DocumentsResponse {
  documents: Document[];
  count: number;
}

export interface CollectionsResponse {
  collections: Collection[];
  count: number;
}

export interface DocumentUploadResponse {
  success: boolean;
  documents: Document[];
  message: string;
} 