import apiService from '../api/apiService';
import { FeedbackData } from '../components/feedback/FeedbackCollector';
import axios from 'axios';

// Use the same baseURL configuration as in apiService
const apiClient = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add authorization interceptor
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

class FeedbackService {
  /**
   * Submit user feedback for a specific query
   * @param queryId - The ID of the query that received feedback
   * @param feedback - The feedback data provided by the user
   * @returns Promise that resolves when feedback is submitted successfully
   */
  async submitFeedback(queryId: string, feedback: FeedbackData): Promise<void> {
    try {
      const response = await apiClient.post('/api/feedback', {
        queryId,
        feedback,
        timestamp: new Date().toISOString()
      });
      return response.data;
    } catch (error) {
      console.error('Error submitting feedback:', error);
      apiService.handleError(error);
      throw new Error('Failed to submit feedback. Please try again later.');
    }
  }
  
  /**
   * Get all feedback for a specific query
   * @param queryId - The ID of the query
   * @returns Promise that resolves with the feedback data
   */
  async getFeedbackForQuery(queryId: string): Promise<FeedbackData[]> {
    try {
      const response = await apiClient.get(`/api/feedback/${queryId}`);
      return response.data;
    } catch (error) {
      console.error('Error getting feedback:', error);
      apiService.handleError(error);
      throw new Error('Failed to fetch feedback data.');
    }
  }
  
  /**
   * Get feedback statistics for all queries
   * @returns Promise that resolves with the feedback statistics
   */
  async getFeedbackStats(): Promise<{
    totalFeedback: number;
    averageRating: number;
    ratingDistribution: Record<number, number>;
    feedbackByType: Record<string, number>;
  }> {
    try {
      const response = await apiClient.get('/api/feedback/stats');
      return response.data;
    } catch (error) {
      console.error('Error getting feedback stats:', error);
      apiService.handleError(error);
      throw new Error('Failed to fetch feedback statistics.');
    }
  }
}

export default new FeedbackService(); 