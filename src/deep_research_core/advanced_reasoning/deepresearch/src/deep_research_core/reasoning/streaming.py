"""
Streaming module for handling incremental responses from AI models.

This module provides classes and utilities for processing streaming responses
from large language models within the Deep Research Core framework. It includes
functionality for managing token-by-token responses, aggregating partial
responses, and formatting streamed content for different reasoning formats.

The streaming capabilities are designed to work seamlessly with both local models
and cloud provider APIs that support streaming, including OpenAI, Anthropic Claude,
and DeepSeek models through direct APIs or via OpenRouter.
"""

import time
import json
import asyncio
import logging
from functools import lru_cache
from typing import List, Dict, Any, Optional, Callable, AsyncGenerator, Union, Tuple
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

class StreamBuffer:
    """
    A buffer for collecting and processing streamed tokens.
    
    This class handles the aggregation of incoming tokens, formatting partial 
    responses, and providing access to the complete response when streaming is complete.
    """
    
    def __init__(self, use_cache: bool = True, cache_size: int = 100, max_buffer_size: int = 10000):
        """
        Initialize a new stream buffer.
        
        Args:
            max_buffer_size: Maximum number of tokens the buffer can hold
        """
        self.buffer: List[str] = []
        self.max_buffer_size = max_buffer_size
        self.is_complete = False
        self.start_time = time.time()
    
    def add_token(self, token: str) -> None:
        """
        Add a new token to the buffer.
        
        Args:
            token: The token to add
        """
        if len(self.buffer) >= self.max_buffer_size:
            # Remove oldest token if buffer is full
            self.buffer.pop(0)
        
        self.buffer.append(token)
    
    def get_content(self) -> str:
        """
        Get the current content of the buffer.
        
        Returns:
            The concatenated content of all tokens in the buffer
        """
        return "".join(self.buffer)
    
    def clear(self) -> None:
        """Clear the buffer contents."""
        self.buffer = []
        self.is_complete = False
        self.start_time = time.time()
    
    def mark_complete(self) -> None:
        """Mark the stream as complete."""
        self.is_complete = True
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the stream.
        
        Returns:
            Dictionary containing stream statistics
        """
        elapsed_time = time.time() - self.start_time
        return {
            "token_count": len(self.buffer),
            "elapsed_time": elapsed_time,
            "tokens_per_second": len(self.buffer) / elapsed_time if elapsed_time > 0 else 0,
            "is_complete": self.is_complete
        }


class BaseStreamHandler(ABC):
    """
    Abstract base class for stream handlers.
    
    Stream handlers process incoming tokens and determine how to integrate
    them with different reasoning formats and output styles.
    """
    
    def __init__(self):
        """Initialize the stream handler."""
        self.buffer = StreamBuffer()
    
    @abstractmethod
    async def process_token(self, token: str) -> str:
        """
        Process a single token from the stream.
        
        Args:
            token: The token to process
            
        Returns:
            Processed output that's ready for display or further processing
        """
        pass
    
    @abstractmethod
    async def finalize(self) -> str:
        """
        Finalize the stream and return the complete output.
        
        Returns:
            The complete processed output
        """
        pass
    
    def reset(self) -> None:
        """Reset the stream handler state."""
        self.buffer.clear()


class ReActStreamHandler(BaseStreamHandler):
    """
    Stream handler for the ReAct reasoning format.
    
    This handler processes tokens specifically for the ReAct format,
    identifying and formatting thought, action, and observation segments.
    """
    
    def __init__(self):
        """Initialize the ReAct stream handler."""
        super().__init__()
        self.current_section = None
        self.sections = {
            "thought": [],
            "action": [],
            "observation": []
        }
    
    async def process_token(self, token: str) -> str:
        """
        Process a token for the ReAct format.
        
        Identifies section transitions (Thought, Action, Observation) and
        formats the output accordingly.
        
        Args:
            token: The token to process
            
        Returns:
            Formatted output for the current state
        """
        self.buffer.add_token(token)
        content = self.buffer.get_content()
        
        # Check for section transitions
        if "Thought:" in content and (self.current_section != "thought" or self.current_section is None):
            self.current_section = "thought"
            thought_idx = content.rfind("Thought:")
            self.sections["thought"] = [content[thought_idx:]]
        elif "Action:" in content and self.current_section != "action":
            self.current_section = "action"
            action_idx = content.rfind("Action:")
            self.sections["action"] = [content[action_idx:]]
        elif "Observation:" in content and self.current_section != "observation":
            self.current_section = "observation"
            obs_idx = content.rfind("Observation:")
            self.sections["observation"] = [content[obs_idx:]]
        elif self.current_section:
            # Append to current section
            self.sections[self.current_section][-1] = content
        
        formatted_output = self._format_current_state()
        return formatted_output
    
    def _format_current_state(self) -> str:
        """
        Format the current state for display.
        
        Returns:
            Formatted string representation of the current state
        """
        output = ""
        for section, contents in self.sections.items():
            if contents:
                if section == "thought":
                    output += f"\nThinking... {contents[-1].replace('Thought:', '').strip()}"
                elif section == "action":
                    output += f"\nTaking action: {contents[-1].replace('Action:', '').strip()}"
                elif section == "observation":
                    output += f"\nObserving: {contents[-1].replace('Observation:', '').strip()}"
        
        return output
    
    async def finalize(self) -> str:
        """
        Finalize the ReAct stream.
        
        Returns:
            The complete formatted output
        """
        self.buffer.mark_complete()
        return self._format_current_state()


class StreamingReasoner:
    """
    Manager for streaming reasoning operations.
    
    This class coordinates the streaming process, connecting to model providers,
    processing tokens through the appropriate handler, and yielding formatted
    results to consumers.
    """
    
    def __init__(self, use_cache: bool = True, cache_size: int = 100, model_provider: Any,
        stream_handler: Optional[BaseStreamHandler] = None,
        vietnamese_support: bool = True
    ):
        """
        Initialize the streaming reasoner.
        
        Args:
            model_provider: The model provider to use for generating responses
            stream_handler: Handler for processing the stream (defaults to ReActStreamHandler)
            vietnamese_support: Whether to enable Vietnamese language optimizations
        """
        self.model_provider = model_provider
        self.stream_handler = stream_handler or ReActStreamHandler()
        self.vietnamese_support = vietnamese_support
        self.streaming_active = False
    
    async def stream_response(
        self, 
        prompt: str, 
        callback: Optional[Callable[[str], None]] = None
    ) -> AsyncGenerator[str, None]:
        """
        Stream a response for the given prompt.
        
        Args:
            prompt: The input prompt
            callback: Optional callback function for each token
            
        Yields:
            Formatted chunks of the response as they become available
        """
        self.streaming_active = True
        self.stream_handler.reset()
        
        try:
            # Initialize the stream with the model provider
            async for token in self._get_token_stream(prompt):
                processed = await self.stream_handler.process_token(token)
                
                if callback:
                    callback(processed)
                
                yield processed
                
                # Small delay to prevent overwhelming the client
                await asyncio.sleep(0.01)
                
        except Exception as e:
            logger.error(f"Error during streaming: {str(e)}")
            raise
        finally:
            self.streaming_active = False
            final_output = await self.stream_handler.finalize()
            yield final_output
    
    async def _get_token_stream(self, prompt: str) -> AsyncGenerator[str, None]:
        """
        Get a stream of tokens from the model provider.
        
        Args:
            prompt: The input prompt
            
        Yields:
            Individual tokens from the model
        """
        # This is a placeholder - actual implementation would connect to the model provider's
        # streaming API (OpenAI, Claude, etc.)
        
        # For Vietnamese support, we might need special handling
        if self.vietnamese_support and any(char in prompt for char in "àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ"):
            logger.info("Vietnamese content detected, applying optimizations")
            # Apply any special handling for Vietnamese
        
        try:
            # In a real implementation, this would be replaced with actual API calls
            # to the model provider's streaming endpoint
            stream = await self.model_provider.generate_stream(prompt)
            
            async for chunk in stream:
                # Extract token based on the provider's format
                # This is just a placeholder - real implementation would parse the provider's response
                token = self._extract_token_from_chunk(chunk)
                yield token
                
        except Exception as e:
            logger.error(f"Error getting token stream: {str(e)}")
            raise
    
    def _extract_token_from_chunk(self, chunk: Any) -> str:
        """
        Extract a token from a provider-specific chunk.
        
        Args:
            chunk: The chunk from the provider
            
        Returns:
            The extracted token
        """
        # This is a placeholder - actual implementation would depend on the
        # format used by each provider
        
        # Example for OpenAI-like format:
        if hasattr(chunk, 'choices') and len(chunk.choices) > 0:
            if hasattr(chunk.choices[0], 'delta') and hasattr(chunk.choices[0].delta, 'content'):
                return chunk.choices[0].delta.content or ""
        
        # Example for a simple string format:
        if isinstance(chunk, str):
            return chunk
            
        # Default fallback
        return ""
    
    def cancel_stream(self) -> None:
        """Cancel the current streaming operation."""
        self.streaming_active = False


class BatchedStreamingReasoner:
    """
    Handles batched streaming for multiple queries.
    
    This class manages multiple simultaneous streaming operations,
    allowing for parallel processing of multiple queries.
    """
    
    def __init__(self, use_cache: bool = True, cache_size: int = 100, model_provider: Any,
        max_concurrent: int = 5,
        stream_handler_factory: Callable[[], BaseStreamHandler] = ReActStreamHandler
    ):
        """
        Initialize the batched streaming reasoner.
        
        Args:
            model_provider: The model provider to use
            max_concurrent: Maximum number of concurrent streams
            stream_handler_factory: Factory function for creating stream handlers
        """
        self.model_provider = model_provider
        self.max_concurrent = max_concurrent
        self.stream_handler_factory = stream_handler_factory
        self.active_streams: Dict[str, StreamingReasoner] = {}
    
    async def process_batch(
        self, 
        queries: List[Tuple[str, str]]
    ) -> AsyncGenerator[Dict[str, str], None]:
        """
        Process a batch of queries with streaming.
        
        Args:
            queries: List of (query_id, prompt) tuples
            
        Yields:
            Dictionary mapping query_ids to their current outputs
        """
        # Create a semaphore to limit concurrent processing
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        # Initialize tasks for each query
        tasks = []
        for query_id, prompt in queries:
            task = asyncio.create_task(
                self._process_single_query(semaphore, query_id, prompt)
            )
            tasks.append(task)
        
        # Process results as they become available
        all_results: Dict[str, str] = {query_id: "" for query_id, _ in queries}
        completed = 0
        
        while completed < len(queries):
            # Wait for any task to complete
            done, pending = await asyncio.wait(
                tasks, 
                return_when=asyncio.FIRST_COMPLETED,
                timeout=0.1
            )
            
            # Process completed tasks
            for task in done:
                query_id, result = await task
                all_results[query_id] = result
                completed += 1
                tasks.remove(task)
            
            # Yield current state
            yield all_results.copy()
            
            # Small delay to prevent overwhelming the client
            await asyncio.sleep(0.05)
    
    async def _process_single_query(
        self, 
        semaphore: asyncio.Semaphore,
        query_id: str, 
        prompt: str
    ) -> Tuple[str, str]:
        """
        Process a single query with streaming, respecting concurrency limits.
        
        Args:
            semaphore: Semaphore for limiting concurrency
            query_id: ID of the query
            prompt: The input prompt
            
        Returns:
            Tuple of (query_id, final_result)
        """
        async with semaphore:
            # Create a new streaming reasoner for this query
            reasoner = StreamingReasoner(
                self.model_provider,
                stream_handler=self.stream_handler_factory()
            )
            
            self.active_streams[query_id] = reasoner
            final_output = ""
            
            try:
                # Process the stream
                async for chunk in reasoner.stream_response(prompt):
                    final_output = chunk
            finally:
                # Clean up
                if query_id in self.active_streams:
                    del self.active_streams[query_id]
            
            return query_id, final_output
    
    def cancel_query(self, query_id: str) -> bool:
        """
        Cancel a specific query.
        
        Args:
            query_id: ID of the query to cancel
            
        Returns:
            True if the query was found and cancelled, False otherwise
        """
        if query_id in self.active_streams:
            self.active_streams[query_id].cancel_stream()
            del self.active_streams[query_id]
            return True
        return False
    
    def cancel_all(self) -> None:
        """Cancel all active streams."""
        for query_id in list(self.active_streams.keys()):
            self.cancel_query(query_id)


class StreamingTool:
    """
    A tool that supports streaming responses.
    
    This class extends the base tool concept to support streaming responses,
    allowing tools to generate results incrementally instead of all at once.
    """
    
    def __init__(self, use_cache: bool = True, cache_size: int = 100, name: str,
        description: str,
        stream_handler: Optional[BaseStreamHandler] = None
    ):
        """
        Initialize a streaming tool.
        
        Args:
            name: The name of the tool
            description: A description of the tool
            stream_handler: Handler for processing the stream
        """
        self.name = name
        self.description = description
        self.stream_handler = stream_handler or ReActStreamHandler()
    
    @abstractmethod
    async def stream_execute(self, **kwargs) -> AsyncGenerator[str, None]:
        """
        Execute the tool with streaming output.
        
        Args:
            **kwargs: Tool-specific arguments
            
        Yields:
            Incremental results as they become available
        """
        pass
    
    async def execute(self, **kwargs) -> str:
        """
        Execute the tool and return the complete result.
        
        This is a convenience method that collects all streamed output
        and returns it as a single string.
        
        Args:
            **kwargs: Tool-specific arguments
            
        Returns:
            The complete tool output
        """
        complete_output = ""
        async for chunk in self.stream_execute(**kwargs):
            complete_output = chunk
        return complete_output 