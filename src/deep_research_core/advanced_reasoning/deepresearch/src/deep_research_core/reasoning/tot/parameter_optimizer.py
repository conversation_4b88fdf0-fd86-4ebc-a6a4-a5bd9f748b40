"""
Parameter optimizer for Tree of Thought (ToT) reasoning.

This module provides a class for optimizing parameters for Tree of Thought reasoning
based on query complexity and other factors.
"""

from typing import Dict, Any, Optional, List, Tuple, Union
from functools import lru_cache
import re
import math
import json
import hashlib
import os
from pathlib import Path

from ...utils.structured_logging import get_logger
from ...utils.performance_metrics import measure_latency
from ...config.app_config import MAX_REASONING_DEPTH, MAX_BRANCHES, CACHE_DIR

# Create a logger
logger = get_logger(__name__)

class TOTParameterOptimizer:
    """
    Optimizer for Tree of Thought parameters.

    This class analyzes query complexity and other factors to optimize
    parameters for Tree of Thought reasoning, such as max_depth, max_branches,
    and temperature.

    Features:
    - Automatic parameter adjustment based on query complexity
    - Query type detection (analytical, creative, factual, etc.)
    - Performance analysis and optimization
    - Language-specific optimizations (English and Vietnamese)
    - Adaptive exploration strategies
    """

    def __init__(
        self,
        base_max_depth: int = 3,
        base_max_branches: int = 3,
        base_temperature: float = 0.7,
        adaptive: bool = True,
        min_branches: int = 2,
        max_branches: int = 6,
        min_depth: int = 1,
        max_depth: int = 5,
        min_temperature: float = 0.5,
        max_temperature: float = 0.9,
        verbose: bool = False,
        language: Optional[str] = None,
        cache_enabled: bool = True
    ):
        """
        Initialize the TOTParameterOptimizer.

        Args:
            base_max_depth: Base maximum depth for the reasoning tree
            base_max_branches: Base maximum branches for each node
            base_temperature: Base temperature for generation
            adaptive: Whether to adapt parameters based on query complexity
            min_branches: Minimum number of branches
            max_branches: Maximum number of branches
            min_depth: Minimum depth
            max_depth: Maximum depth
            min_temperature: Minimum temperature
            max_temperature: Maximum temperature
            verbose: Whether to print verbose output
        """
        self.base_max_depth = base_max_depth
        self.base_max_branches = base_max_branches
        self.base_temperature = base_temperature
        self.adaptive = adaptive
        self.min_branches = min_branches
        self.max_branches = max_branches
        self.min_depth = min_depth
        self.max_depth = max_depth
        self.min_temperature = min_temperature
        self.max_temperature = max_temperature
        self.verbose = verbose
        self.language = language
        self.cache_enabled = cache_enabled

        # Create cache directory if it doesn't exist
        if self.cache_enabled and CACHE_DIR:
            os.makedirs(os.path.join(CACHE_DIR, "tot_parameters"), exist_ok=True)

        # Complexity indicators for different types of queries
        self.complexity_indicators = {
            "analytical": [
                # English indicators
                "analyze", "compare", "evaluate", "pros and cons", "benefits", "drawbacks",
                "advantages", "disadvantages", "contrast", "differentiate", "examine",
                # Vietnamese indicators
                "phân tích", "so sánh", "đánh giá", "ưu nhược điểm", "lợi ích", "nhược điểm",
                "ưu điểm", "khác biệt", "phân biệt", "xem xét", "đối chiếu", "nhận định"
            ],
            "creative": [
                # English indicators
                "creative", "design", "imagine", "invent", "novel", "innovative", "unique",
                "original", "artistic", "brainstorm", "generate", "create",
                # Vietnamese indicators
                "sáng tạo", "thiết kế", "tưởng tượng", "phát minh", "mới lạ", "độc đáo",
                "nguyên bản", "nghệ thuật", "ý tưởng", "tạo ra", "hình dung", "sáng kiến"
            ],
            "factual": [
                # English indicators
                "what is", "define", "explain", "describe", "who", "when", "where", "why",
                "how many", "list", "enumerate", "tell me about", "information on",
                # Vietnamese indicators
                "là gì", "định nghĩa", "giải thích", "mô tả", "ai", "khi nào", "ở đâu", "tại sao",
                "bao nhiêu", "liệt kê", "kể tên", "cho biết về", "thông tin về", "nêu"
            ],
            "problem_solving": [
                # English indicators
                "solve", "solution", "fix", "improve", "optimize", "resolve", "address",
                "overcome", "handle", "deal with", "approach", "strategy",
                # Vietnamese indicators
                "giải quyết", "giải pháp", "sửa chữa", "cải thiện", "tối ưu hóa", "khắc phục",
                "vượt qua", "xử lý", "đối phó", "cách tiếp cận", "chiến lược", "phương án"
            ],
            "multi_step": [
                # English indicators
                "steps to", "process", "procedure", "method", "how to", "guide", "tutorial",
                "walkthrough", "instructions", "sequence", "stages", "phases",
                # Vietnamese indicators
                "các bước để", "quy trình", "thủ tục", "phương pháp", "làm thế nào để", "hướng dẫn",
                "bài hướng dẫn", "chỉ dẫn", "trình tự", "giai đoạn", "các bước", "tiến trình"
            ]
        }

        # Parameter adjustments for different query types
        self.query_type_adjustments = {
            "analytical": {"depth": 0.3, "branches": 0.2, "temperature": -0.1},
            "creative": {"depth": 0.1, "branches": 0.3, "temperature": 0.2},
            "factual": {"depth": -0.2, "branches": -0.2, "temperature": -0.2},
            "problem_solving": {"depth": 0.3, "branches": 0.2, "temperature": 0.0},
            "multi_step": {"depth": 0.4, "branches": 0.1, "temperature": -0.1}
        }

    @measure_latency("tot_parameter_optimizer_analyze_complexity")
    def analyze_query_complexity(self, query: str) -> Dict[str, Any]:
        """
        Analyze the complexity of a query.

        Args:
            query: The query to analyze

        Returns:
            Dictionary with complexity analysis results
        """
        # Normalize query for analysis
        query_lower = query.lower()

        # Basic complexity metrics
        length_complexity = min(1.0, len(query) / 200)  # Normalize by typical query length

        # Count question marks as an indicator of complexity
        question_complexity = min(1.0, query.count("?") / 3)

        # Count commas as an indicator of complexity (more clauses)
        comma_complexity = min(1.0, query.count(",") / 5)

        # Count semicolons as an indicator of complexity (more complex structure)
        semicolon_complexity = min(1.0, query.count(";") / 2) * 0.8

        # Detect logical connectors (and, or, but, however, etc.)
        logical_connectors = [
            "and", "or", "but", "however", "therefore", "thus", "nevertheless", "although", "despite", "whereas",
            "và", "hoặc", "nhưng", "tuy nhiên", "do đó", "vì vậy", "mặc dù", "dù", "trong khi", "ngược lại"
        ]
        connector_count = sum(1 for connector in logical_connectors if connector in query_lower)
        connector_complexity = min(1.0, connector_count / 5) * 0.7

        # Detect nested questions or multi-part questions
        nested_question_indicators = [
            "first", "second", "third", "lastly", "finally", "additionally", "moreover",
            "thứ nhất", "thứ hai", "thứ ba", "cuối cùng", "ngoài ra", "hơn nữa"
        ]
        nested_count = sum(1 for indicator in nested_question_indicators if indicator in query_lower)
        nested_complexity = min(1.0, nested_count / 3) * 0.8

        # Detect domain-specific complexity
        domain_indicators = {
            "technical": ["code", "algorithm", "programming", "technical", "lập trình", "thuật toán", "kỹ thuật"],
            "scientific": ["scientific", "physics", "chemistry", "biology", "khoa học", "vật lý", "hóa học", "sinh học"],
            "philosophical": ["philosophy", "ethics", "moral", "triết học", "đạo đức", "luân lý"],
            "mathematical": ["math", "equation", "calculation", "toán học", "phương trình", "tính toán"]
        }

        domain_scores = {}
        for domain, indicators in domain_indicators.items():
            domain_count = sum(1 for indicator in indicators if indicator in query_lower)
            domain_scores[domain] = min(1.0, domain_count / 2) * 0.9

        domain_complexity = max(domain_scores.values()) if domain_scores else 0.0

        # Detect query types more efficiently
        query_types = self._detect_query_types(query_lower)

        # Calculate type-based complexity
        type_complexity = 0.0
        for query_type, score in query_types.items():
            if query_type in ["analytical", "creative", "problem_solving", "multi_step"]:
                type_complexity += score * 0.25

        # Calculate overall complexity (weighted average)
        overall_complexity = (
            length_complexity * 0.15 +
            question_complexity * 0.15 +
            comma_complexity * 0.1 +
            semicolon_complexity * 0.05 +
            connector_complexity * 0.1 +
            nested_complexity * 0.15 +
            domain_complexity * 0.1 +
            type_complexity * 0.2
        )

        # Ensure complexity is between 0 and 1
        overall_complexity = max(0.0, min(1.0, overall_complexity))

        if self.verbose:
            logger.info(f"Query complexity analysis: {overall_complexity:.2f}")
            logger.info(f"Query types: {query_types}")
            logger.info(f"Domain complexity: {domain_complexity:.2f}")
            logger.info(f"Nested complexity: {nested_complexity:.2f}")

        return {
            "overall_complexity": overall_complexity,
            "length_complexity": length_complexity,
            "question_complexity": question_complexity,
            "comma_complexity": comma_complexity,
            "semicolon_complexity": semicolon_complexity,
            "connector_complexity": connector_complexity,
            "nested_complexity": nested_complexity,
            "domain_complexity": domain_complexity,
            "domain_scores": domain_scores,
            "type_complexity": type_complexity,
            "query_types": query_types
        }

    @lru_cache(maxsize=100)
    def _detect_query_types(self, query: str) -> Dict[str, float]:
        """
        Detect the types of a query.

        Args:
            query: The query to analyze

        Returns:
            Dictionary mapping query types to confidence scores
        """
        query_lower = query.lower()

        # Initialize scores for each query type
        scores = {query_type: 0.0 for query_type in self.complexity_indicators}

        # Check for indicators of each query type more efficiently
        for query_type, indicators in self.complexity_indicators.items():
            # Use a more efficient approach for matching
            matches = 0
            for indicator in indicators:
                if indicator in query_lower:
                    matches += 1
                    # Early exit if we've found enough indicators
                    if matches >= 3:
                        break

            # Calculate score based on indicator presence
            if matches > 0:
                scores[query_type] = min(1.0, matches / 3)

        # Ensure at least one type has a non-zero score
        if all(score == 0.0 for score in scores.values()):
            scores["factual"] = 0.5  # Default to factual if no clear type

        # Normalize scores to sum to 1.0 (use a faster approach)
        total_score = sum(scores.values())
        if total_score > 0:
            for k in scores:
                scores[k] /= total_score

        return scores

    def adjust_parameters_for_complexity(
        self,
        query: str
    ) -> Dict[str, Any]:
        """
        Adjust parameters based on query complexity.

        Args:
            query: The query to adjust parameters for

        Returns:
            Dictionary with adjusted parameters
        """
        if not self.adaptive:
            # Return base parameters if not in adaptive mode
            return {
                "max_depth": self.base_max_depth,
                "max_branches": self.base_max_branches,
                "temperature": self.base_temperature
            }

        # Analyze query complexity
        complexity_analysis = self.analyze_query_complexity(query)
        overall_complexity = complexity_analysis["overall_complexity"]
        query_types = complexity_analysis["query_types"]
        domain_scores = complexity_analysis.get("domain_scores", {})
        nested_complexity = complexity_analysis.get("nested_complexity", 0.0)
        domain_complexity = complexity_analysis.get("domain_complexity", 0.0)

        # Base adjustments based on overall complexity
        depth_adjustment = overall_complexity * 0.5  # Up to 0.5 increase for complex queries
        branches_adjustment = overall_complexity * 0.6  # Up to 0.6 increase for complex queries
        temperature_adjustment = overall_complexity * 0.2 - 0.1  # -0.1 to 0.1 range

        # Additional adjustments based on query types
        for query_type, score in query_types.items():
            if query_type in self.query_type_adjustments:
                type_adjustments = self.query_type_adjustments[query_type]
                depth_adjustment += type_adjustments["depth"] * score
                branches_adjustment += type_adjustments["branches"] * score
                temperature_adjustment += type_adjustments["temperature"] * score

        # Adjust for nested questions (multi-part questions need more branches)
        if nested_complexity > 0.3:
            branches_adjustment += nested_complexity * 0.5
            depth_adjustment += nested_complexity * 0.3

        # Adjust for domain-specific complexity
        if domain_complexity > 0.5:
            # Technical and scientific domains benefit from deeper reasoning
            if domain_scores.get("technical", 0) > 0.5 or domain_scores.get("scientific", 0) > 0.5:
                depth_adjustment += 0.5
                temperature_adjustment -= 0.1  # Lower temperature for more precise reasoning

            # Philosophical domains benefit from more branches
            if domain_scores.get("philosophical", 0) > 0.5:
                branches_adjustment += 0.5
                temperature_adjustment += 0.1  # Higher temperature for more diverse reasoning

            # Mathematical domains benefit from deeper reasoning
            if domain_scores.get("mathematical", 0) > 0.5:
                depth_adjustment += 0.7
                temperature_adjustment -= 0.15  # Lower temperature for more precise reasoning

        # Calculate adjusted parameters
        adjusted_depth = round(self.base_max_depth + depth_adjustment)
        adjusted_branches = round(self.base_max_branches + branches_adjustment)
        adjusted_temperature = self.base_temperature + temperature_adjustment

        # Ensure parameters are within bounds
        adjusted_depth = max(self.min_depth, min(self.max_depth, adjusted_depth))
        adjusted_branches = max(self.min_branches, min(self.max_branches, adjusted_branches))
        adjusted_temperature = max(self.min_temperature, min(self.max_temperature, adjusted_temperature))

        # Special case: For very complex queries, ensure minimum values
        if overall_complexity > 0.8:
            adjusted_depth = max(adjusted_depth, 3)  # Ensure at least depth 3 for very complex queries
            adjusted_branches = max(adjusted_branches, 3)  # Ensure at least 3 branches for very complex queries

        if self.verbose:
            logger.info(f"Adjusted parameters for query complexity {overall_complexity:.2f}:")
            logger.info(f"  max_depth: {self.base_max_depth} -> {adjusted_depth}")
            logger.info(f"  max_branches: {self.base_max_branches} -> {adjusted_branches}")
            logger.info(f"  temperature: {self.base_temperature:.2f} -> {adjusted_temperature:.2f}")
            logger.info(f"  domain complexity: {domain_complexity:.2f}")
            logger.info(f"  nested complexity: {nested_complexity:.2f}")
            logger.info(f"  query types: {', '.join([f'{k}: {v:.2f}' for k, v in query_types.items() if v > 0.2])}")

        return {
            "max_depth": adjusted_depth,
            "max_branches": adjusted_branches,
            "temperature": adjusted_temperature,
            "complexity_analysis": complexity_analysis,
            "adjustments": {
                "depth_adjustment": depth_adjustment,
                "branches_adjustment": branches_adjustment,
                "temperature_adjustment": temperature_adjustment,
                "domain_complexity": domain_complexity,
                "nested_complexity": nested_complexity
            }
        }

    def estimate_complexity(self, query: str) -> float:
        """
        Estimate the complexity of a query.
        This is a simplified wrapper around analyze_query_complexity that returns just the overall complexity score.

        Args:
            query: The query to estimate complexity for

        Returns:
            A complexity score between 0 and 1
        """
        analysis = self.analyze_query_complexity(query)
        return analysis["overall_complexity"]

    def analyze_performance(
        self,
        current_parameters: Dict[str, Any],
        performance_metrics: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Analyze performance and suggest parameter improvements.

        Args:
            current_parameters: Current parameters being used
            performance_metrics: Optional performance metrics from previous runs

        Returns:
            Dictionary with parameter suggestions and reasoning
        """
        suggestions = {
            "parameter_suggestions": {},
            "reasoning": []
        }

        # If no performance metrics provided, we can only make general suggestions
        if not performance_metrics:
            # Suggest balanced parameters if none provided
            if "max_depth" not in current_parameters:
                suggestions["parameter_suggestions"]["max_depth"] = self.base_max_depth
                suggestions["reasoning"].append(f"Using default max_depth of {self.base_max_depth}")

            if "max_branches" not in current_parameters:
                suggestions["parameter_suggestions"]["max_branches"] = self.base_max_branches
                suggestions["reasoning"].append(f"Using default max_branches of {self.base_max_branches}")

            if "temperature" not in current_parameters:
                suggestions["parameter_suggestions"]["temperature"] = self.base_temperature
                suggestions["reasoning"].append(f"Using default temperature of {self.base_temperature}")

            return suggestions

        # With performance metrics, we can make more informed suggestions
        latency = performance_metrics.get("latency", 0)
        explored_paths = performance_metrics.get("explored_paths", 0)
        success_rate = performance_metrics.get("success_rate", 0.5)

        # Analyze latency
        if latency > 10:
            # If latency is high, suggest reducing branches or depth
            current_branches = current_parameters.get("max_branches", self.base_max_branches)
            current_depth = current_parameters.get("max_depth", self.base_max_depth)

            if current_branches > self.min_branches:
                suggestions["parameter_suggestions"]["max_branches"] = max(self.min_branches, current_branches - 1)
                suggestions["reasoning"].append(
                    f"Reduced max_branches from {current_branches} to {suggestions['parameter_suggestions']['max_branches']} "
                    f"to improve latency (current: {latency:.2f}s)"
                )
            elif current_depth > self.min_depth:
                suggestions["parameter_suggestions"]["max_depth"] = max(self.min_depth, current_depth - 1)
                suggestions["reasoning"].append(
                    f"Reduced max_depth from {current_depth} to {suggestions['parameter_suggestions']['max_depth']} "
                    f"to improve latency (current: {latency:.2f}s)"
                )

        # Analyze exploration
        theoretical_max = current_parameters.get("max_branches", 3) ** current_parameters.get("max_depth", 3)
        if explored_paths < theoretical_max * 0.3 and success_rate < 0.7:
            # If we're exploring too few paths and success rate is low, suggest increasing temperature
            current_temp = current_parameters.get("temperature", self.base_temperature)
            if current_temp < self.max_temperature:
                new_temp = min(self.max_temperature, current_temp + 0.1)
                suggestions["parameter_suggestions"]["temperature"] = new_temp
                suggestions["reasoning"].append(
                    f"Increased temperature from {current_temp:.2f} to {new_temp:.2f} to explore more diverse paths"
                )

        return suggestions

    def optimize_parameters_for_query(self, query: str) -> Dict[str, Any]:
        """Optimize parameters for a given query.

        Args:
            query: The query to optimize parameters for

        Returns:
            A dictionary of optimized parameters
        """
        if not self.adaptive:
            return {
                "max_depth": self.base_max_depth,
                "max_branches": self.base_max_branches,
                "temperature": self.base_temperature
            }

        # First, adjust parameters based on query complexity
        parameters = self.adjust_parameters_for_complexity(query)

        # Then, adjust parameters based on query type
        parameters = self.adjust_parameters_for_query_type(query, parameters)

        # Finally, adjust advanced parameters
        parameters = self.adjust_advanced_parameters(query, parameters)

        return parameters

    def adjust_parameters_for_query_type(self, query: str, current_parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Adjust parameters based on query type.

        Args:
            query: The query to adjust parameters for
            current_parameters: The current parameters

        Returns:
            A dictionary of adjusted parameters
        """
        # Get query type scores
        query_analysis = self.analyze_query_complexity(query)
        query_types = query_analysis.get("query_types", {})

        # Make a copy of current parameters
        adjusted_parameters = current_parameters.copy()

        # Adjust parameters based on query type
        if query_types.get("analytical", 0) > 0.6:
            # For analytical queries, increase depth and reduce branches
            adjusted_parameters["max_depth"] = min(self.max_depth, adjusted_parameters.get("max_depth", self.base_max_depth) + 1)
            adjusted_parameters["temperature"] = max(self.min_temperature, adjusted_parameters.get("temperature", self.base_temperature) - 0.1)

        if query_types.get("creative", 0) > 0.6:
            # For creative queries, increase temperature and branches
            adjusted_parameters["temperature"] = min(self.max_temperature, adjusted_parameters.get("temperature", self.base_temperature) + 0.1)
            adjusted_parameters["max_branches"] = min(self.max_branches, adjusted_parameters.get("max_branches", self.base_max_branches) + 1)

        if query_types.get("factual", 0) > 0.6:
            # For factual queries, reduce depth and temperature
            adjusted_parameters["max_depth"] = max(self.min_depth, adjusted_parameters.get("max_depth", self.base_max_depth) - 1)
            adjusted_parameters["temperature"] = max(self.min_temperature, adjusted_parameters.get("temperature", self.base_temperature) - 0.1)

        if query_types.get("problem_solving", 0) > 0.6:
            # For problem-solving queries, increase depth and branches
            adjusted_parameters["max_depth"] = min(self.max_depth, adjusted_parameters.get("max_depth", self.base_max_depth) + 1)
            adjusted_parameters["max_branches"] = min(self.max_branches, adjusted_parameters.get("max_branches", self.base_max_branches) + 1)

        if query_types.get("multi_step", 0) > 0.6:
            # For multi-step queries, increase depth
            adjusted_parameters["max_depth"] = min(self.max_depth, adjusted_parameters.get("max_depth", self.base_max_depth) + 1)

        # Ensure parameters are within bounds
        adjusted_parameters["max_depth"] = max(self.min_depth, min(self.max_depth, adjusted_parameters.get("max_depth", self.base_max_depth)))
        adjusted_parameters["max_branches"] = max(self.min_branches, min(self.max_branches, adjusted_parameters.get("max_branches", self.base_max_branches)))
        adjusted_parameters["temperature"] = max(self.min_temperature, min(self.max_temperature, adjusted_parameters.get("temperature", self.base_temperature)))

        if self.verbose:
            logger.info(f"Adjusted parameters for query type:")
            for query_type, score in query_types.items():
                if score > 0.2:
                    logger.info(f"  {query_type}: {score:.2f}")
            logger.info(f"  max_depth: {current_parameters.get('max_depth', self.base_max_depth)} -> {adjusted_parameters['max_depth']}")
            logger.info(f"  max_branches: {current_parameters.get('max_branches', self.base_max_branches)} -> {adjusted_parameters['max_branches']}")
            logger.info(f"  temperature: {current_parameters.get('temperature', self.base_temperature):.2f} -> {adjusted_parameters['temperature']:.2f}")

        return adjusted_parameters

    def adjust_advanced_parameters(self, query: str, current_parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Adjust advanced parameters based on query complexity and type.

        Args:
            query: The query to adjust parameters for
            current_parameters: The current parameters

        Returns:
            A dictionary of adjusted parameters with advanced settings
        """
        # Get query complexity and type
        query_analysis = self.analyze_query_complexity(query)
        overall_complexity = query_analysis.get("overall_complexity", 0.5)
        query_types = query_analysis.get("query_types", {})

        # Make a copy of current parameters
        adjusted_parameters = current_parameters.copy()

        # Add early pruning for complex queries
        if overall_complexity > 0.7:
            adjusted_parameters["early_pruning"] = True
            adjusted_parameters["pruning_threshold"] = 0.3
        else:
            adjusted_parameters["early_pruning"] = False

        # Add multi-criteria evaluation for analytical and problem-solving queries
        if query_types.get("analytical", 0) > 0.5 or query_types.get("problem_solving", 0) > 0.5:
            adjusted_parameters["multi_criteria_evaluation"] = True
            adjusted_parameters["evaluation_criteria"] = [
                "relevance", "coherence", "logical_consistency", "completeness"
            ]
        else:
            adjusted_parameters["multi_criteria_evaluation"] = False

        # Add selective caching for all queries to improve performance
        adjusted_parameters["selective_cache"] = True

        # Add information sharing between branches for complex queries
        if overall_complexity > 0.6:
            adjusted_parameters["information_sharing"] = True
        else:
            adjusted_parameters["information_sharing"] = False

        # Add parallel exploration for very complex queries if available
        if overall_complexity > 0.8:
            adjusted_parameters["parallel_exploration"] = True
        else:
            adjusted_parameters["parallel_exploration"] = False

        # Set token budget based on complexity
        if overall_complexity > 0.8:
            adjusted_parameters["token_budget"] = 4000
        elif overall_complexity > 0.5:
            adjusted_parameters["token_budget"] = 3000
        else:
            adjusted_parameters["token_budget"] = 2000

        # Set minimum tokens per step based on complexity
        if overall_complexity > 0.7:
            adjusted_parameters["min_tokens_per_step"] = 100
        else:
            adjusted_parameters["min_tokens_per_step"] = 50

        if self.verbose:
            logger.info(f"Adjusted advanced parameters for query complexity {overall_complexity:.2f}:")
            logger.info(f"  early_pruning: {adjusted_parameters.get('early_pruning', False)}")
            logger.info(f"  multi_criteria_evaluation: {adjusted_parameters.get('multi_criteria_evaluation', False)}")
            logger.info(f"  selective_cache: {adjusted_parameters.get('selective_cache', False)}")
            logger.info(f"  information_sharing: {adjusted_parameters.get('information_sharing', False)}")
            logger.info(f"  parallel_exploration: {adjusted_parameters.get('parallel_exploration', False)}")
            logger.info(f"  token_budget: {adjusted_parameters.get('token_budget', 2000)}")
            logger.info(f"  min_tokens_per_step: {adjusted_parameters.get('min_tokens_per_step', 50)}")

        return adjusted_parameters

    def suggest_tot_improvements(
        self,
        query: str,
        current_parameters: Dict[str, Any],
        performance_metrics: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Suggest improvements for ToT parameters based on query and performance.

        Args:
            query: The query being processed
            current_parameters: Current parameters being used
            performance_metrics: Optional performance metrics from previous runs

        Returns:
            Dictionary with suggested improvements
        """
        suggestions = {
            "parameter_suggestions": {},
            "reasoning": [],
            "expected_impact": "medium"
        }

        # Get optimal parameters based on query complexity
        optimal_params = self.adjust_parameters_for_complexity(query)

        # Compare current parameters with optimal parameters
        for param in ["max_depth", "max_branches", "temperature"]:
            current_value = current_parameters.get(param, getattr(self, f"base_{param}"))
            optimal_value = optimal_params.get(param)

            if param == "temperature":
                # For temperature, consider small differences significant
                if abs(current_value - optimal_value) > 0.1:
                    suggestions["parameter_suggestions"][param] = optimal_value
                    suggestions["reasoning"].append(
                        f"Adjusted {param} from {current_value:.2f} to {optimal_value:.2f} "
                        f"based on query complexity and type."
                    )
            else:
                # For integer parameters, check if they differ
                if current_value != optimal_value:
                    suggestions["parameter_suggestions"][param] = optimal_value
                    suggestions["reasoning"].append(
                        f"Adjusted {param} from {current_value} to {optimal_value} "
                        f"based on query complexity and type."
                    )

        # If we have performance metrics, make additional suggestions
        if performance_metrics:
            latency = performance_metrics.get("latency", 0)
            explored_paths = performance_metrics.get("explored_paths", 0)

            # If latency is high but we're suggesting more branches/depth, add a warning
            if (latency > 10 and
                (suggestions["parameter_suggestions"].get("max_branches", 0) > current_parameters.get("max_branches", 0) or
                 suggestions["parameter_suggestions"].get("max_depth", 0) > current_parameters.get("max_depth", 0))):
                suggestions["reasoning"].append(
                    "Warning: Increasing branches/depth may further increase latency, "
                    "which is already high. Consider using parallel exploration if available."
                )

            # If we explored very few paths relative to theoretical maximum, suggest adjustments
            theoretical_max = current_parameters.get("max_branches", 3) ** current_parameters.get("max_depth", 3)
            if explored_paths < theoretical_max * 0.3:
                suggestions["reasoning"].append(
                    f"Only explored {explored_paths} paths out of a theoretical maximum of {theoretical_max}. "
                    f"Consider adjusting early pruning settings or evaluation criteria to explore more paths."
                )

        # Determine expected impact
        if len(suggestions["parameter_suggestions"]) > 1:
            suggestions["expected_impact"] = "high"
        elif len(suggestions["parameter_suggestions"]) == 0:
            suggestions["expected_impact"] = "low"

        return suggestions
