"""
CoTRAG with Recency Handling.

This module extends the CoTRAG implementation with recency handling,
including the ability to detect and handle queries requiring recent information.
"""

import re
import logging
import time
import datetime
from functools import lru_cache
from typing import Dict, Any, List, Optional, Union, Callable, Tuple

from .cot_rag import CoTRAG
from ..utils.structured_logging import get_logger
from ..utils.performance_metrics import measure_latency
from ..utils.distributed_tracing import trace_function

# Create a logger
logger = get_logger(__name__)

class CoTRAGRecencyHandler(CoTRAG):
    """
    Extends CoTRAG with recency handling.
    
    This class adds the ability to detect and handle queries requiring
    recent information, with special handling for time-sensitive queries.
    """
    
    def __init__(self, use_cache: bool = True, cache_size: int = 100, provider: str = "openai",
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        vector_store=None,
        adaptive: bool = True,
        use_cache: bool = True,
        evaluate_results: bool = False,
        use_dynamic_weighting: bool = True,
        min_cot_weight: float = 0.3,
        max_cot_weight: float = 0.8,
        default_cot_weight: float = 0.5,
        weighting_strategy: str = "auto",
        handle_irrelevant_docs: bool = True,
        relevance_threshold: float = 0.3,
        analyze_errors: bool = False,
        verbose: bool = False,
        detect_recency: bool = True,
        recency_threshold_days: int = 30,
        web_search_enabled: bool = False,
        web_search_provider: Optional[str] = None,
        max_web_results: int = 3,
        knowledge_cutoff_date: Optional[str] = None
    ):
        """
        Initialize CoTRAGRecencyHandler.
        
        Args:
            provider: The provider to use ("openai", "anthropic", etc.)
            model: The model to use (if None, will use provider's default)
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            vector_store: Vector store to use for retrieval
            adaptive: Whether to use adaptive parameter adjustment
            use_cache: Whether to use caching for repeated queries
            evaluate_results: Whether to evaluate reasoning quality
            use_dynamic_weighting: Whether to dynamically adjust weights between CoT and RAG
            min_cot_weight: Minimum weight for CoT (0.0 to 1.0)
            max_cot_weight: Maximum weight for CoT (0.0 to 1.0)
            default_cot_weight: Default weight for CoT (0.0 to 1.0)
            weighting_strategy: Weight adjustment strategy ("auto", "query_type", 
                              "query_complexity", "document_relevance")
            handle_irrelevant_docs: Whether to handle cases when RAG returns irrelevant documents
            relevance_threshold: Threshold for document relevance (0.0 to 1.0)
            analyze_errors: Whether to analyze errors in results
            verbose: Whether to log detailed information
            detect_recency: Whether to detect queries requiring recent information
            recency_threshold_days: Number of days to consider information as recent
            web_search_enabled: Whether to use web search for recent information
            web_search_provider: Web search provider to use
            max_web_results: Maximum number of web search results to use
            knowledge_cutoff_date: Knowledge cutoff date of the model (YYYY-MM-DD)
        """
        # Initialize the base CoTRAG class
        super().__init__(
            provider=provider,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            vector_store=vector_store,
            adaptive=adaptive,
            use_cache=use_cache,
            evaluate_results=evaluate_results,
            use_dynamic_weighting=use_dynamic_weighting,
            min_cot_weight=min_cot_weight,
            max_cot_weight=max_cot_weight,
            default_cot_weight=default_cot_weight,
            weighting_strategy=weighting_strategy,
            handle_irrelevant_docs=handle_irrelevant_docs,
            relevance_threshold=relevance_threshold,
            analyze_errors=analyze_errors,
            verbose=verbose
        )
        
        # Store recency handling parameters
        self.detect_recency = detect_recency
        self.recency_threshold_days = recency_threshold_days
        self.web_search_enabled = web_search_enabled
        self.web_search_provider = web_search_provider
        self.max_web_results = max_web_results
        
        # Set knowledge cutoff date
        if knowledge_cutoff_date:
            self.knowledge_cutoff_date = knowledge_cutoff_date
        else:
            # Default knowledge cutoff dates for different models
            cutoff_dates = {
                "gpt-4": "2023-04-01",
                "gpt-4-turbo": "2023-12-01",
                "gpt-4o": "2023-12-01",
                "gpt-3.5-turbo": "2023-04-01",
                "claude-3-opus": "2023-08-01",
                "claude-3-sonnet": "2023-08-01",
                "claude-3-haiku": "2023-08-01"
            }
            
            # Try to find a matching model
            for model_prefix, date in cutoff_dates.items():
                if model and model_prefix in model:
                    self.knowledge_cutoff_date = date
                    break
            else:
                # Default to a conservative date if no match
                self.knowledge_cutoff_date = "2023-04-01"
        
        # Set up caching
        if use_cache:
            self._generate_cached = lru_cache(maxsize=cache_size)(self._generate)
        else:
            self._generate_cached = self._generate
            
        logger.info(f"Initialized CoTRAGRecencyHandler with detect_recency={detect_recency}, knowledge_cutoff_date={self.knowledge_cutoff_date}")
    
    def _detect_recency_requirement(self, query: str) -> Dict[str, Any]:
        """
        Detect if a query requires recent information.
        
        Args:
            query: The query to analyze
            
        Returns:
            Dictionary with recency analysis
        """
        # Check for explicit time-related terms
        recency_terms = [
            "recent", "latest", "newest", "current", "today", "now", "update",
            "last week", "last month", "this year", "2023", "2024", "2025",
            "happening now", "trending", "news", "development"
        ]
        
        # Check for Vietnamese recency terms
        vietnamese_recency_terms = [
            "gần đây", "mới nhất", "hiện tại", "hôm nay", "bây giờ", "cập nhật",
            "tuần trước", "tháng trước", "năm nay", "2023", "2024", "2025",
            "đang diễn ra", "xu hướng", "tin tức", "phát triển"
        ]
        
        # Combine all terms
        all_recency_terms = recency_terms + vietnamese_recency_terms
        
        # Check for matches
        matches = []
        for term in all_recency_terms:
            if term.lower() in query.lower():
                matches.append(term)
        
        # Get current date
        current_date = datetime.datetime.now().strftime("%Y-%m-%d")
        
        # Calculate days since knowledge cutoff
        try:
            cutoff_date = datetime.datetime.strptime(self.knowledge_cutoff_date, "%Y-%m-%d")
            current_datetime = datetime.datetime.now()
            days_since_cutoff = (current_datetime - cutoff_date).days
        except:
            days_since_cutoff = 365  # Default to a year if calculation fails
        
        # Determine if query requires recent information
        requires_recent = len(matches) > 0
        
        # Try to determine the specific time period required
        time_period = None
        if requires_recent:
            # Check for specific time periods
            if any(term in query.lower() for term in ["today", "now", "hôm nay", "bây giờ"]):
                time_period = "today"
            elif any(term in query.lower() for term in ["this week", "tuần này"]):
                time_period = "this_week"
            elif any(term in query.lower() for term in ["this month", "tháng này"]):
                time_period = "this_month"
            elif any(term in query.lower() for term in ["this year", "năm nay"]):
                time_period = "this_year"
            else:
                time_period = "recent"
        
        # Create analysis result
        analysis = {
            "requires_recent_information": requires_recent,
            "recency_terms_found": matches,
            "current_date": current_date,
            "knowledge_cutoff_date": self.knowledge_cutoff_date,
            "days_since_cutoff": days_since_cutoff,
            "time_period": time_period,
            "beyond_knowledge_cutoff": requires_recent and days_since_cutoff > 0
        }
        
        return analysis
    
    def _perform_web_search(self, query: str, max_results: int = 3) -> List[Dict[str, Any]]:
        """
        Perform a web search to get recent information.
        
        Args:
            query: The query to search for
            max_results: Maximum number of results to return
            
        Returns:
            List of search results
        """
        if not self.web_search_enabled:
            logger.warning("Web search is not enabled")
            return []
        
        try:
            # Import web search module
            from ..tools.web_search import web_search
            
            # Modify query to emphasize recency
            search_query = f"recent {query} site:.com OR site:.org OR site:.gov"
            
            # Perform web search
            search_results = web_search(search_query, num_results=max_results)
            
            # Format results
            formatted_results = []
            for result in search_results:
                formatted_results.append({
                    "title": result.get("title", ""),
                    "url": result.get("link", ""),
                    "snippet": result.get("snippet", ""),
                    "source": "web_search"
                })
            
            return formatted_results
        
        except Exception as e:
            logger.error(f"Error performing web search: {str(e)}")
            return []
    
    def _extract_web_content(self, url: str) -> str:
        """
        Extract content from a web page.
        
        Args:
            url: URL of the web page
            
        Returns:
            Extracted content
        """
        try:
            # Import web fetch module
            from ..tools.web_fetch import web_fetch
            
            # Fetch web page content
            content = web_fetch(url)
            
            return content
        
        except Exception as e:
            logger.error(f"Error extracting web content: {str(e)}")
            return f"Error extracting content from {url}: {str(e)}"
    
    def _format_web_results(self, web_results: List[Dict[str, Any]]) -> str:
        """
        Format web search results for inclusion in the prompt.
        
        Args:
            web_results: List of web search results
            
        Returns:
            Formatted web results
        """
        if not web_results:
            return ""
        
        formatted_text = "RECENT WEB SEARCH RESULTS:\n\n"
        
        for i, result in enumerate(web_results):
            formatted_text += f"[{i+1}] {result['title']}\n"
            formatted_text += f"URL: {result['url']}\n"
            formatted_text += f"Snippet: {result['snippet']}\n\n"
        
        return formatted_text
    
    def _get_recency_system_prompt(self, recency_analysis: Dict[str, Any]) -> str:
        """
        Get a system prompt optimized for handling queries requiring recent information.
        
        Args:
            recency_analysis: Recency analysis from _detect_recency_requirement
            
        Returns:
            System prompt for handling recency
        """
        current_date = recency_analysis["current_date"]
        cutoff_date = recency_analysis["knowledge_cutoff_date"]
        days_since_cutoff = recency_analysis["days_since_cutoff"]
        
        prompt = f"""You are a helpful AI assistant that combines retrieval and reasoning to provide accurate answers.
        Today's date is {current_date}.
        Your knowledge cutoff date is {cutoff_date}, which is {days_since_cutoff} days ago.
        
        The user has asked a question that may require recent information beyond your knowledge cutoff.
        
        Please follow these guidelines:
        1. Clearly acknowledge the time-sensitive nature of the query.
        2. Be transparent about your knowledge cutoff date and its limitations.
        3. Use the retrieved information, including any recent web search results, to provide the most up-to-date answer possible.
        4. Clearly distinguish between information from your training data (before the cutoff) and information from the retrieved documents.
        5. If the retrieved information is not recent enough, acknowledge this limitation.
        6. Use a step-by-step reasoning approach to develop your answer.
        7. If you cannot provide a complete or up-to-date answer, clearly state what information is missing or potentially outdated.
        
        Your goal is to provide the most helpful and accurate response possible while being transparent about the recency of the information."""
        
        return prompt
    
    @trace_function(name="cotrag_recency_handler_process")
    @measure_latency("cotrag_recency_handler_process")
    def process(
        self,
        query: str,
        top_k: int = 5,
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        force_refresh: bool = False,
        expected_answer: Optional[str] = None,
        check_recency: Optional[bool] = None,
        use_web_search: Optional[bool] = None
    ) -> Dict[str, Any]:
        """
        Process a query that may require recent information.
        
        Args:
            query: The query to process
            top_k: Number of documents to retrieve
            custom_system_prompt: Custom system prompt to use
            custom_user_prompt: Custom user prompt template to use
            callback: Optional callback function for streaming
            force_refresh: Whether to force a refresh (ignore cache)
            expected_answer: Optional expected answer for error analysis
            check_recency: Whether to check for recency requirement (overrides detect_recency)
            use_web_search: Whether to use web search for recent information (overrides web_search_enabled)
            
        Returns:
            Dict containing the query, retrieved documents, reasoning, and answer
        """
        start_time = time.time()
        
        # Check cache if enabled and not forcing refresh
        if self.use_cache and not force_refresh:
            cached_result = self.cache.get(query)
            if cached_result:
                logger.info(f"Retrieved result from cache for query: {query[:50]}...")
                return cached_result
        
        # Determine whether to check for recency requirement
        should_check_recency = check_recency if check_recency is not None else self.detect_recency
        
        # Check for recency requirement if enabled
        recency_analysis = None
        if should_check_recency:
            recency_analysis = self._detect_recency_requirement(query)
            requires_recent = recency_analysis.get("requires_recent_information", False)
            
            if self.verbose:
                logger.info(f"Recency analysis: {recency_analysis}")
            
            # Handle recency requirement if detected
            if requires_recent:
                # Determine whether to use web search
                should_use_web_search = use_web_search if use_web_search is not None else self.web_search_enabled
                
                # Perform web search if enabled
                web_results = []
                if should_use_web_search:
                    web_results = self._perform_web_search(query, max_results=self.max_web_results)
                    
                    if self.verbose:
                        logger.info(f"Web search results: {len(web_results)} results")
                
                # Retrieve documents from vector store
                documents = self.retrieve(query, top_k=top_k)
                
                # Get recency system prompt
                if custom_system_prompt:
                    system_prompt = custom_system_prompt
                else:
                    system_prompt = self._get_recency_system_prompt(recency_analysis)
                
                # Format context from documents
                context = self._format_context(documents)
                
                # Add web search results if available
                if web_results:
                    web_context = self._format_web_results(web_results)
                    context = web_context + "\n\n" + context
                
                # Create user prompt
                if custom_user_prompt:
                    user_prompt = custom_user_prompt.format(query=query, context=context)
                else:
                    user_prompt = self.user_prompt_template.format(query=query, context=context)
                
                # Get optimal weights
                weights = self._get_optimal_weights(query, documents)
                
                # Create weighted prompt if using dynamic weighting
                if self.use_dynamic_weighting:
                    user_prompt = self._create_weighted_prompt(query, context, weights)
                
                # Generate reasoning
                reasoning = self.api_provider.generate(
                    prompt=user_prompt,
                    model=self.model,
                    system_prompt=system_prompt,
                    temperature=self.temperature,
                    max_tokens=self.max_tokens
                )
                
                # Extract final answer
                final_answer = self._extract_final_answer(reasoning)
                
                # Create result
                result = {
                    "query": query,
                    "documents": documents,
                    "reasoning": reasoning,
                    "answer": final_answer,
                    "weights": weights,
                    "model": self.model,
                    "provider": self.provider,
                    "latency": time.time() - start_time,
                    "recency": {
                        "analysis": recency_analysis,
                        "web_results": web_results if web_results else None
                    }
                }
                
                # Evaluate results if enabled
                if self.evaluate_results:
                    result["evaluation"] = self._evaluate_results(
                        query=query,
                        documents=documents,
                        reasoning=reasoning,
                        answer=final_answer,
                        expected_answer=expected_answer
                    )
                
                # Analyze errors if enabled
                if self.analyze_errors:
                    result["error_analysis"] = self._analyze_errors(
                        query=query,
                        documents=documents,
                        reasoning=reasoning,
                        answer=final_answer,
                        expected_answer=expected_answer
                    )
                
                # Cache result if caching is enabled
                if self.use_cache:
                    self.cache.set(query, result)
                
                return result
        
        # If not requiring recent information or recency handling is disabled, process normally
        result = super().process(
            query=query,
            top_k=top_k,
            custom_system_prompt=custom_system_prompt,
            custom_user_prompt=custom_user_prompt,
            callback=callback,
            force_refresh=force_refresh,
            expected_answer=expected_answer
        )
        
        # Add recency information if checked
        if should_check_recency and recency_analysis:
            result["recency"] = {
                "analysis": recency_analysis
            }
        
        return result
