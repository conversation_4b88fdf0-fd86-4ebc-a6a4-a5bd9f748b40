"""
<PERSON><PERSON><PERSON> tối ưu hóa nâng cao cho Tree of Thought (TOT).

Module này cung cấp các tối ưu hóa nâng cao để cải thiện hiệu suất,
đ<PERSON> chính xác và sử dụng tài nguyên của Tree of Thought (TOT).
"""

import time
import heapq
import threading
import concurrent.futures
from typing import List, Tuple, Dict, Any, Optional, Callable, Set
from functools import lru_cache

from ..utils.performance_metrics import measure_latency
from ..utils.distributed_tracing import trace_function

class PathPruner:
    """
    Lớp cắt tỉa đường dẫn để loại bỏ sớm các nhánh kém triển vọng.
    """

    # Biến lớp để lưu trữ ngưỡng cắt tỉa toàn cục
    _pruning_threshold = 0.3
    _min_acceptable_score = 5.0
    _depth_penalty_factor = 0.1

    @classmethod
    def set_pruning_threshold(cls, threshold: float):
        """
        Đặt ngưỡng cắt tỉa toàn cục.

        Args:
            threshold: Ngưỡng cắt tỉa mới (0.0-1.0)
        """
        cls._pruning_threshold = max(0.1, min(0.9, threshold))

    @classmethod
    def set_min_acceptable_score(cls, score: float):
        """
        Đặt điểm số tối thiểu có thể chấp nhận.

        Args:
            score: Điểm số tối thiểu (1.0-10.0)
        """
        cls._min_acceptable_score = max(1.0, min(8.0, score))

    @classmethod
    def set_depth_penalty_factor(cls, factor: float):
        """
        Đặt hệ số phạt theo độ sâu.

        Args:
            factor: Hệ số phạt (0.0-0.5)
        """
        cls._depth_penalty_factor = max(0.0, min(0.5, factor))

    @classmethod
    def should_prune(cls, score: float, depth: int, best_score_so_far: float,
                     pruning_threshold: Optional[float] = None) -> bool:
        """
        Quyết định xem có nên cắt tỉa một đường dẫn hay không.

        Args:
            score: Điểm số của đường dẫn
            depth: Độ sâu hiện tại
            best_score_so_far: Điểm số tốt nhất đã tìm thấy
            pruning_threshold: Ngưỡng cắt tỉa tùy chọn (ghi đè ngưỡng toàn cục)

        Returns:
            True nếu nên cắt tỉa, False nếu không
        """
        # Sử dụng ngưỡng được cung cấp hoặc ngưỡng toàn cục
        threshold = pruning_threshold if pruning_threshold is not None else cls._pruning_threshold

        # Điều chỉnh ngưỡng dựa trên độ sâu
        adjusted_threshold = cls.dynamic_pruning_threshold(depth, 4, threshold)

        # Điều chỉnh điểm số tối thiểu dựa trên độ sâu
        min_score = max(1.0, cls._min_acceptable_score - (depth * cls._depth_penalty_factor))

        # Nếu điểm số quá thấp so với điểm tốt nhất, cắt tỉa
        if best_score_so_far > 0 and score < best_score_so_far * (1 - adjusted_threshold):
            return True

        # Nếu ở độ sâu cao mà điểm số vẫn thấp, cắt tỉa
        if depth > 1 and score < min_score:
            return True

        # Nếu điểm số quá thấp, cắt tỉa bất kể độ sâu
        if score < 3.0:
            return True

        return False

    @staticmethod
    def dynamic_pruning_threshold(depth: int, max_depth: int,
                                  base_threshold: float = 0.3) -> float:
        """
        Tính toán ngưỡng cắt tỉa động dựa trên độ sâu.

        Args:
            depth: Độ sâu hiện tại
            max_depth: Độ sâu tối đa
            base_threshold: Ngưỡng cơ bản (mặc định: 0.3)

        Returns:
            Ngưỡng cắt tỉa động
        """
        # Ngưỡng cắt tỉa giảm dần theo độ sâu
        # Ở độ sâu thấp, chúng ta cắt tỉa mạnh hơn
        # Ở độ sâu cao, chúng ta cắt tỉa ít hơn để khám phá nhiều hơn
        depth_ratio = min(1.0, depth / max_depth)
        return base_threshold * (1 - depth_ratio * 0.7)


class BatchEvaluator:
    """
    Lớp đánh giá theo nhóm để cải thiện hiệu suất.
    """

    @staticmethod
    def split_into_batches(paths: List[str], batch_size: int = 3) -> List[List[str]]:
        """
        Chia danh sách đường dẫn thành các nhóm nhỏ.

        Args:
            paths: Danh sách đường dẫn
            batch_size: Kích thước nhóm (mặc định: 3)

        Returns:
            Danh sách các nhóm đường dẫn
        """
        return [paths[i:i+batch_size] for i in range(0, len(paths), batch_size)]

    @staticmethod
    def merge_evaluations(batch_evaluations: List[List[Tuple[float, str]]]) -> List[Tuple[float, str]]:
        """
        Kết hợp các đánh giá từ nhiều nhóm.

        Args:
            batch_evaluations: Danh sách các đánh giá theo nhóm

        Returns:
            Danh sách đánh giá đã kết hợp
        """
        merged = []
        for batch in batch_evaluations:
            merged.extend(batch)

        # Sắp xếp theo điểm giảm dần
        return sorted(merged, key=lambda x: x[0], reverse=True)


class ParallelExplorer:
    """
    Lớp khám phá song song để cải thiện hiệu suất.
    """

    def __init__(self, max_workers: int = 3):
        """
        Khởi tạo khám phá song song.

        Args:
            max_workers: Số lượng worker tối đa (mặc định: 3)
        """
        self.max_workers = max_workers
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)

    def explore_paths_parallel(self,
                              paths: List[Tuple[float, str]],
                              evaluate_func: Callable[[str], List[Tuple[float, str]]],
                              max_paths: int = 5) -> List[Tuple[float, str]]:
        """
        Khám phá nhiều đường dẫn song song.

        Args:
            paths: Danh sách đường dẫn ban đầu (điểm, đường dẫn)
            evaluate_func: Hàm đánh giá đường dẫn
            max_paths: Số lượng đường dẫn tối đa để khám phá

        Returns:
            Danh sách đường dẫn đã đánh giá
        """
        # Chọn các đường dẫn tốt nhất để khám phá
        top_paths = sorted(paths, key=lambda x: x[0], reverse=True)[:max_paths]

        # Khám phá song song
        futures = []
        for _, path in top_paths:
            future = self.executor.submit(evaluate_func, path)
            futures.append(future)

        # Thu thập kết quả
        results = []
        for future in concurrent.futures.as_completed(futures):
            try:
                result = future.result()
                results.extend(result)
            except Exception as e:
                print(f"Lỗi khi khám phá song song: {str(e)}")

        # Sắp xếp kết quả theo điểm giảm dần
        return sorted(results, key=lambda x: x[0], reverse=True)

    def shutdown(self):
        """
        Đóng executor.
        """
        self.executor.shutdown()


class MemoryManager:
    """
    Lớp quản lý bộ nhớ để tối ưu hóa sử dụng tài nguyên.
    """

    def __init__(self, max_paths_to_keep: int = 100):
        """
        Khởi tạo quản lý bộ nhớ.

        Args:
            max_paths_to_keep: Số lượng đường dẫn tối đa để giữ lại (mặc định: 100)
        """
        self.max_paths_to_keep = max_paths_to_keep
        self.path_cache = {}
        self.path_scores = {}
        self.path_access_times = {}

    @measure_latency("add_path")
    @trace_function(name="add_path")
    def add_path(self, path_id: str, path: str, score: float):
        """
        Thêm một đường dẫn vào bộ nhớ.

        Args:
            path_id: ID của đường dẫn
            path: Nội dung đường dẫn
            score: Điểm số của đường dẫn
        """
        # Nếu bộ nhớ đầy, xóa các đường dẫn ít được truy cập nhất
        if len(self.path_cache) >= self.max_paths_to_keep:
            self._cleanup()

        # Thêm đường dẫn mới
        self.path_cache[path_id] = path
        self.path_scores[path_id] = score
        self.path_access_times[path_id] = time.time()

    def get_path(self, path_id: str) -> Tuple[str, float]:
        """
        Lấy một đường dẫn từ bộ nhớ.

        Args:
            path_id: ID của đường dẫn

        Returns:
            Tuple (đường dẫn, điểm số)
        """
        if path_id in self.path_cache:
            # Cập nhật thời gian truy cập
            self.path_access_times[path_id] = time.time()
            return self.path_cache[path_id], self.path_scores[path_id]

        return None, 0.0

    def _cleanup(self):
        """
        Xóa các đường dẫn ít được truy cập nhất.
        """
        # Sắp xếp theo thời gian truy cập
        sorted_paths = sorted(self.path_access_times.items(), key=lambda x: x[1])

        # Xóa 25% đường dẫn ít được truy cập nhất
        paths_to_remove = sorted_paths[:len(sorted_paths) // 4]

        for path_id, _ in paths_to_remove:
            del self.path_cache[path_id]
            del self.path_scores[path_id]
            del self.path_access_times[path_id]


class TokenBudgetManager:
    """
    Lớp quản lý ngân sách token để kiểm soát chi phí.
    """

    def __init__(self, total_budget: int = 100000):
        """
        Khởi tạo quản lý ngân sách token.

        Args:
            total_budget: Tổng ngân sách token (mặc định: 100000)
        """
        self.total_budget = total_budget
        self.used_budget = 0
        self.budget_multiplier = 1.0
        self.lock = threading.Lock()

    def can_use_tokens(self, num_tokens: int) -> bool:
        """
        Kiểm tra xem có thể sử dụng số token yêu cầu hay không.

        Args:
            num_tokens: Số token yêu cầu

        Returns:
            True nếu có thể sử dụng, False nếu không
        """
        with self.lock:
            return self.used_budget + num_tokens <= self.total_budget

    def use_tokens(self, num_tokens: int) -> bool:
        """
        Sử dụng số token yêu cầu.

        Args:
            num_tokens: Số token yêu cầu

        Returns:
            True nếu thành công, False nếu không
        """
        with self.lock:
            if self.used_budget + num_tokens <= self.total_budget:
                self.used_budget += num_tokens
                return True
            return False

    def get_remaining_budget(self) -> int:
        """
        Lấy ngân sách còn lại.

        Returns:
            Số token còn lại
        """
        with self.lock:
            return self.total_budget - self.used_budget

    def get_budget(self) -> int:
        """
        Lấy tổng ngân sách token.

        Returns:
            Tổng ngân sách token
        """
        with self.lock:
            return self.total_budget

    def set_budget(self, budget: int) -> None:
        """
        Đặt tổng ngân sách token.

        Args:
            budget: Tổng ngân sách token mới
        """
        with self.lock:
            self.total_budget = max(1000, budget)

    def set_budget_multiplier(self, multiplier: float) -> None:
        """
        Đặt hệ số nhân ngân sách.

        Args:
            multiplier: Hệ số nhân (0.1-10.0)
        """
        with self.lock:
            self.budget_multiplier = max(0.1, min(10.0, multiplier))
            self.total_budget = int(self.total_budget * self.budget_multiplier)

    def reset(self):
        """
        Đặt lại ngân sách đã sử dụng.
        """
        with self.lock:
            self.used_budget = 0


class MultiCriteriaEvaluator:
    """
    Lớp đánh giá đa tiêu chí để cải thiện độ chính xác.
    """

    @staticmethod
    def evaluate_text_features(text: str) -> Dict[str, float]:
        """
        Đánh giá các đặc điểm văn bản.

        Args:
            text: Văn bản cần đánh giá

        Returns:
            Dict các đặc điểm và điểm số
        """
        features = {}

        # Độ dài
        features["length"] = min(10.0, len(text) / 500)

        # Độ phức tạp (dựa trên số câu và độ dài trung bình của câu)
        sentences = [s.strip() for s in text.split(".") if s.strip()]
        if sentences:
            avg_sentence_length = sum(len(s.split()) for s in sentences) / len(sentences)
            features["complexity"] = min(10.0, avg_sentence_length / 15)
        else:
            features["complexity"] = 0.0

        # Tính mạch lạc (dựa trên số từ nối)
        connectives = ["vì", "do đó", "tuy nhiên", "mặc dù", "nếu", "vì vậy", "bởi vì",
                      "therefore", "however", "although", "if", "thus", "because"]
        connective_count = sum(1 for c in connectives if c in text.lower())
        features["coherence"] = min(10.0, connective_count / 5)

        # Tính đa dạng từ vựng
        words = [w.lower() for w in text.split() if w.strip()]
        unique_words = set(words)
        if words:
            features["vocabulary_diversity"] = min(10.0, len(unique_words) / len(words) * 10)
        else:
            features["vocabulary_diversity"] = 0.0

        return features

    @staticmethod
    def calculate_weighted_score(features: Dict[str, float],
                                weights: Dict[str, float] = None) -> float:
        """
        Tính điểm số có trọng số từ các đặc điểm.

        Args:
            features: Dict các đặc điểm và điểm số
            weights: Dict các trọng số (mặc định: None)

        Returns:
            Điểm số có trọng số
        """
        if weights is None:
            weights = {
                "length": 0.2,
                "complexity": 0.3,
                "coherence": 0.3,
                "vocabulary_diversity": 0.2
            }

        # Tính điểm số có trọng số
        weighted_score = 0.0
        for feature, score in features.items():
            if feature in weights:
                weighted_score += score * weights[feature]

        return weighted_score

    @staticmethod
    def adjust_score_based_on_features(base_score: float, text: str,
                                      weight: float = 0.3) -> float:
        """
        Điều chỉnh điểm số dựa trên các đặc điểm văn bản.

        Args:
            base_score: Điểm số cơ bản
            text: Văn bản cần đánh giá
            weight: Trọng số cho điểm số đặc điểm (mặc định: 0.3)

        Returns:
            Điểm số đã điều chỉnh
        """
        # Đánh giá đặc điểm văn bản
        features = MultiCriteriaEvaluator.evaluate_text_features(text)

        # Tính điểm số đặc điểm
        feature_score = MultiCriteriaEvaluator.calculate_weighted_score(features)

        # Điều chỉnh điểm số
        adjusted_score = base_score * (1 - weight) + feature_score * weight

        return adjusted_score


class SelectiveCache:
    """
    Lớp cache có chọn lọc để tối ưu hóa sử dụng tài nguyên.
    """

    def __init__(self, max_size: int = 100):
        """
        Khởi tạo cache có chọn lọc.

        Args:
            max_size: Kích thước tối đa của cache (mặc định: 100)
        """
        self.max_size = max_size
        self.cache = {}
        self.priorities = {}

    @measure_latency("add")
    @trace_function(name="add")
    def add(self, key: str, value: Any, priority: float = 1.0):
        """
        Thêm một mục vào cache.

        Args:
            key: Khóa
            value: Giá trị
            priority: Độ ưu tiên (mặc định: 1.0)
        """
        # Nếu cache đầy, xóa các mục có độ ưu tiên thấp nhất
        if len(self.cache) >= self.max_size:
            self._cleanup()

        # Thêm mục mới
        self.cache[key] = value
        self.priorities[key] = priority

    def get(self, key: str) -> Any:
        """
        Lấy một mục từ cache.

        Args:
            key: Khóa

        Returns:
            Giá trị nếu tồn tại, None nếu không
        """
        return self.cache.get(key)

    def _cleanup(self):
        """
        Xóa các mục có độ ưu tiên thấp nhất.
        """
        # Sắp xếp theo độ ưu tiên
        sorted_items = sorted(self.priorities.items(), key=lambda x: x[1])

        # Xóa 25% mục có độ ưu tiên thấp nhất
        items_to_remove = sorted_items[:len(sorted_items) // 4]

        for key, _ in items_to_remove:
            del self.cache[key]
            del self.priorities[key]


class AdaptiveExploration:
    """
    Lớp khám phá thích ứng để cải thiện hiệu suất.
    """

    # Biến lớp để lưu trữ các tham số toàn cục
    _depth_weight = 0.5
    _score_weight = 0.5
    _novelty_weight = 0.2
    _uncertainty_weight = 0.3
    _exploration_bias = 0.1

    @classmethod
    def set_weights(cls, depth_weight: float = 0.5, score_weight: float = 0.5,
                   novelty_weight: float = 0.2, uncertainty_weight: float = 0.3):
        """
        Đặt trọng số cho các yếu tố khám phá.

        Args:
            depth_weight: Trọng số cho yếu tố độ sâu (0.0-1.0)
            score_weight: Trọng số cho yếu tố điểm số (0.0-1.0)
            novelty_weight: Trọng số cho yếu tố mới lạ (0.0-1.0)
            uncertainty_weight: Trọng số cho yếu tố không chắc chắn (0.0-1.0)
        """
        # Đảm bảo tổng trọng số bằng 1.0
        total = depth_weight + score_weight + novelty_weight + uncertainty_weight
        if total > 0:
            cls._depth_weight = depth_weight / total
            cls._score_weight = score_weight / total
            cls._novelty_weight = novelty_weight / total
            cls._uncertainty_weight = uncertainty_weight / total

    @classmethod
    def set_exploration_bias(cls, bias: float):
        """
        Đặt độ chéch khám phá.

        Args:
            bias: Độ chéch khám phá (-0.5 đến 0.5)
        """
        cls._exploration_bias = max(-0.5, min(0.5, bias))

    @classmethod
    def calculate_exploration_factor(cls, depth: int, max_depth: int,
                                    score: float, best_score: float,
                                    novelty: float = 0.5,
                                    uncertainty: float = 0.5) -> float:
        """
        Tính toán hệ số khám phá thích ứng.

        Args:
            depth: Độ sâu hiện tại
            max_depth: Độ sâu tối đa
            score: Điểm số hiện tại
            best_score: Điểm số tốt nhất
            novelty: Độ mới lạ của đường dẫn (0.0-1.0)
            uncertainty: Độ không chắc chắn của đường dẫn (0.0-1.0)

        Returns:
            Hệ số khám phá
        """
        # Yếu tố độ sâu: giảm dần theo độ sâu
        depth_factor = 1.0 - (depth / max(max_depth, 1))

        # Yếu tố điểm số: tăng dần theo điểm số tương đối
        score_factor = score / max(best_score, 1.0)

        # Tính toán hệ số khám phá có trọng số
        exploration_factor = (
            cls._depth_weight * depth_factor +
            cls._score_weight * score_factor +
            cls._novelty_weight * novelty +
            cls._uncertainty_weight * uncertainty
        )

        # Áp dụng độ chéch khám phá
        exploration_factor = max(0.1, min(1.0, exploration_factor + cls._exploration_bias))

        return exploration_factor

    @classmethod
    def adjust_num_branches(cls, base_branches: int, exploration_factor: float,
                           query_complexity: float = 0.5) -> int:
        """
        Điều chỉnh số nhánh dựa trên hệ số khám phá và độ phức tạp của truy vấn.

        Args:
            base_branches: Số nhánh cơ bản
            exploration_factor: Hệ số khám phá
            query_complexity: Độ phức tạp của truy vấn (0.0-1.0)

        Returns:
            Số nhánh đã điều chỉnh
        """
        # Điều chỉnh số nhánh dựa trên hệ số khám phá
        adjusted_branches = max(1, int(base_branches * exploration_factor))

        # Điều chỉnh thêm dựa trên độ phức tạp của truy vấn
        complexity_adjustment = max(0, int(query_complexity * 2))
        adjusted_branches = max(1, adjusted_branches + complexity_adjustment)

        return adjusted_branches

    @classmethod
    def calculate_path_novelty(cls, path: str, explored_paths: List[str]) -> float:
        """
        Tính toán độ mới lạ của một đường dẫn so với các đường dẫn đã khám phá.

        Args:
            path: Đường dẫn cần đánh giá
            explored_paths: Danh sách các đường dẫn đã khám phá

        Returns:
            Độ mới lạ (0.0-1.0)
        """
        if not explored_paths:
            return 1.0  # Đường dẫn đầu tiên luôn có độ mới lạ cao nhất

        # Tính độ tương đồng với từng đường dẫn đã khám phá
        similarities = []
        path_words = set(path.lower().split())

        for explored_path in explored_paths:
            explored_words = set(explored_path.lower().split())

            # Tính hệ số Jaccard (tỷ lệ giao trên từ)
            if not path_words or not explored_words:
                similarities.append(0.0)
                continue

            intersection = len(path_words.intersection(explored_words))
            union = len(path_words.union(explored_words))

            if union > 0:
                similarity = intersection / union
            else:
                similarity = 0.0

            similarities.append(similarity)

        # Độ mới lạ là 1 - độ tương đồng cao nhất
        max_similarity = max(similarities) if similarities else 0.0
        novelty = 1.0 - max_similarity

        return novelty
