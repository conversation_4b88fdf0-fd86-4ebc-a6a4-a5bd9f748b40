"""
Action Space Awareness module for Deep Research Core.

This module provides functionality for improving the reasoning agent's awareness
of available actions and their constraints, enabling more effective reasoning.
"""

from typing import Dict, Any, List, Optional, Callable, Union, Tuple, Set
import inspect
import json

from deep_research_core.utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

class ActionSpaceAwareness:
    """
    Class for enhancing reasoning agent's awareness of available actions.
    
    This class helps reasoning agents understand what actions are available, 
    their constraints, and how they can be effectively used in the reasoning process.
    It improves efficiency by reducing exploration of invalid or ineffective actions.
    """
    
    def __init__(
        self,
        tools: List[Any] = None,
        action_history_limit: int = 100,
        enable_analytics: bool = True,
        **kwargs
    ):
        """
        Initialize the ActionSpaceAwareness.
        
        Args:
            tools: List of tool objects that define available actions
            action_history_limit: Maximum number of actions to track in history
            enable_analytics: Whether to enable analytics on action usage
            **kwargs: Additional implementation-specific parameters
        """
        self.tools = tools or []
        self.action_history_limit = action_history_limit
        self.enable_analytics = enable_analytics
        
        # Action space tracking
        self.available_actions = {}
        self.action_history = []
        self.action_statistics = {}
        
        # Initialize the action space if tools are provided
        if self.tools:
            self._initialize_action_space()
    
    def _initialize_action_space(self) -> None:
        """
        Initialize the action space based on provided tools.
        """
        for tool in self.tools:
            tool_name = tool.__class__.__name__
            methods = self._extract_tool_methods(tool)
            
            self.available_actions[tool_name] = {
                "methods": methods,
                "description": self._get_tool_description(tool),
                "constraints": self._extract_tool_constraints(tool)
            }
            
            # Initialize statistics for each action
            for method_name in methods:
                action_id = f"{tool_name}.{method_name}"
                self.action_statistics[action_id] = {
                    "usage_count": 0,
                    "success_rate": 0.0,
                    "avg_execution_time": 0.0,
                    "last_used": None,
                    "common_patterns": []
                }
    
    def _extract_tool_methods(self, tool: Any) -> Dict[str, Dict[str, Any]]:
        """
        Extract available methods from a tool.
        
        Args:
            tool: The tool object
            
        Returns:
            Dictionary of method names and their metadata
        """
        methods = {}
        for name, method in inspect.getmembers(tool, predicate=inspect.ismethod):
            # Skip private methods
            if name.startswith('_'):
                continue
                
            # Get method signature
            signature = inspect.signature(method)
            
            methods[name] = {
                "parameters": {
                    param_name: {
                        "type": str(param.annotation) if param.annotation != inspect.Parameter.empty else "Any",
                        "default": None if param.default == inspect.Parameter.empty else param.default
                    }
                    for param_name, param in signature.parameters.items()
                    if param_name != 'self'
                },
                "return_type": str(signature.return_annotation) if signature.return_annotation != inspect.Parameter.empty else "Any",
                "docstring": inspect.getdoc(method) or "No documentation available"
            }
        
        return methods
    
    def _get_tool_description(self, tool: Any) -> str:
        """
        Get the description of a tool.
        
        Args:
            tool: The tool object
            
        Returns:
            Description of the tool
        """
        return inspect.getdoc(tool) or f"No description available for {tool.__class__.__name__}"
    
    def _extract_tool_constraints(self, tool: Any) -> Dict[str, Any]:
        """
        Extract constraints for a tool.
        
        Args:
            tool: The tool object
            
        Returns:
            Dictionary of constraints
        """
        constraints = {}
        
        # Look for constraints in tool attributes
        if hasattr(tool, 'constraints'):
            constraints = tool.constraints
        elif hasattr(tool, 'limits'):
            constraints = tool.limits
        else:
            # Default constraints
            constraints = {
                "rate_limit": None,
                "max_tokens": None,
                "requires_auth": False
            }
            
        return constraints
    
    def register_action(
        self,
        tool_name: str,
        method_name: str,
        params: Dict[str, Any],
        result: Any,
        execution_time: float,
        success: bool
    ) -> None:
        """
        Register an action execution in the history.
        
        Args:
            tool_name: Name of the tool used
            method_name: Name of the method called
            params: Parameters used for the call
            result: Result of the action
            execution_time: Time taken to execute the action
            success: Whether the action was successful
        """
        action_id = f"{tool_name}.{method_name}"
        
        # Add to history
        action_record = {
            "tool": tool_name,
            "method": method_name,
            "params": params,
            "success": success,
            "execution_time": execution_time,
            "timestamp": self._get_current_timestamp()
        }
        
        self.action_history.append(action_record)
        
        # Trim history if needed
        if len(self.action_history) > self.action_history_limit:
            self.action_history = self.action_history[-self.action_history_limit:]
        
        # Update statistics if enabled
        if self.enable_analytics and action_id in self.action_statistics:
            stats = self.action_statistics[action_id]
            stats["usage_count"] += 1
            stats["last_used"] = action_record["timestamp"]
            
            # Update success rate
            prev_count = stats["usage_count"] - 1
            prev_success_rate = stats["success_rate"]
            stats["success_rate"] = (prev_success_rate * prev_count + (1 if success else 0)) / stats["usage_count"]
            
            # Update average execution time
            prev_avg_time = stats["avg_execution_time"]
            stats["avg_execution_time"] = (prev_avg_time * prev_count + execution_time) / stats["usage_count"]
    
    def _get_current_timestamp(self) -> str:
        """
        Get the current timestamp as a string.
        
        Returns:
            Current timestamp string
        """
        import datetime
        return datetime.datetime.now().isoformat()
    
    def get_action_recommendations(
        self,
        context: str,
        max_recommendations: int = 3
    ) -> List[Dict[str, Any]]:
        """
        Get recommended actions based on current context.
        
        Args:
            context: The current reasoning context
            max_recommendations: Maximum number of recommendations to return
            
        Returns:
            List of recommended actions with metadata
        """
        # This would involve more sophisticated logic in a real implementation
        # Here we're just returning the most successful actions as a simplification
        
        if not self.action_statistics:
            return []
        
        # Sort actions by success rate and usage count
        sorted_actions = sorted(
            self.action_statistics.items(),
            key=lambda x: (x[1]["success_rate"], x[1]["usage_count"]),
            reverse=True
        )
        
        recommendations = []
        for action_id, stats in sorted_actions[:max_recommendations]:
            tool_name, method_name = action_id.split(".")
            
            if tool_name in self.available_actions and method_name in self.available_actions[tool_name]["methods"]:
                method_info = self.available_actions[tool_name]["methods"][method_name]
                
                recommendations.append({
                    "tool": tool_name,
                    "method": method_name,
                    "description": method_info["docstring"],
                    "parameters": method_info["parameters"],
                    "success_rate": stats["success_rate"],
                    "usage_count": stats["usage_count"]
                })
        
        return recommendations
    
    def get_action_space_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the available action space.
        
        Returns:
            Dictionary summarizing the available action space
        """
        tool_count = len(self.available_actions)
        method_count = sum(len(tool_data["methods"]) for tool_data in self.available_actions.values())
        
        return {
            "tool_count": tool_count,
            "method_count": method_count,
            "tools": [
                {
                    "name": tool_name,
                    "description": tool_data["description"],
                    "method_count": len(tool_data["methods"]),
                    "methods": list(tool_data["methods"].keys())
                }
                for tool_name, tool_data in self.available_actions.items()
            ]
        }
    
    def get_usage_analytics(self) -> Dict[str, Any]:
        """
        Get analytics on action usage.
        
        Returns:
            Dictionary with action usage analytics
        """
        if not self.enable_analytics:
            return {"status": "Analytics not enabled"}
        
        # Most used actions
        most_used = sorted(
            self.action_statistics.items(),
            key=lambda x: x[1]["usage_count"],
            reverse=True
        )[:5]
        
        # Most successful actions
        most_successful = sorted(
            [(k, v) for k, v in self.action_statistics.items() if v["usage_count"] > 0],
            key=lambda x: x[1]["success_rate"],
            reverse=True
        )[:5]
        
        return {
            "total_actions_executed": len(self.action_history),
            "unique_actions_used": sum(1 for stats in self.action_statistics.values() if stats["usage_count"] > 0),
            "most_used_actions": [
                {"action": action_id, "count": stats["usage_count"]}
                for action_id, stats in most_used
            ],
            "most_successful_actions": [
                {"action": action_id, "success_rate": stats["success_rate"]}
                for action_id, stats in most_successful
            ]
        }
    
    def add_tool(self, tool: Any) -> None:
        """
        Add a new tool to the action space.
        
        Args:
            tool: The tool object to add
        """
        self.tools.append(tool)
        tool_name = tool.__class__.__name__
        
        methods = self._extract_tool_methods(tool)
        
        self.available_actions[tool_name] = {
            "methods": methods,
            "description": self._get_tool_description(tool),
            "constraints": self._extract_tool_constraints(tool)
        }
        
        # Initialize statistics for each action
        for method_name in methods:
            action_id = f"{tool_name}.{method_name}"
            self.action_statistics[action_id] = {
                "usage_count": 0,
                "success_rate": 0.0,
                "avg_execution_time": 0.0,
                "last_used": None,
                "common_patterns": []
            }
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the action space awareness to a dictionary representation.
        
        Returns:
            Dictionary representation of the action space
        """
        return {
            "available_actions": self.available_actions,
            "action_statistics": self.action_statistics,
            "action_history_limit": self.action_history_limit,
            "enable_analytics": self.enable_analytics
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ActionSpaceAwareness':
        """
        Create an ActionSpaceAwareness instance from a dictionary.
        
        Args:
            data: Dictionary representation of an ActionSpaceAwareness
            
        Returns:
            New ActionSpaceAwareness instance
        """
        instance = cls(
            tools=[],  # Tools need to be added separately
            action_history_limit=data.get("action_history_limit", 100),
            enable_analytics=data.get("enable_analytics", True)
        )
        
        instance.available_actions = data.get("available_actions", {})
        instance.action_statistics = data.get("action_statistics", {})
        
        return instance 