"""
Performance optimizations for SQLiteVectorRAG.

This module contains functions and utilities to optimize the performance
of SQLiteVectorRAG for different use cases.
"""

import sqlite3
import time
from typing import Dict, Any, List, Optional, Tuple

import numpy as np

from ..sqlite_vector_rag import SQLiteVectorRAG
from ...utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)


def optimize_database(db_path: str, use_wal: bool = True) -> bool:
    """
    Optimize a SQLite database for better performance.

    Args:
        db_path: Path to the SQLite database
        use_wal: Whether to use WAL mode (default: True)

    Returns:
        True if optimization was successful, False otherwise
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Enable WAL mode for better concurrency and performance if requested
        if use_wal:
            cursor.execute("PRAGMA journal_mode=WAL")
        else:
            cursor.execute("PRAGMA journal_mode=DELETE")

        # Set synchronous mode to NORMAL for better performance
        cursor.execute("PRAGMA synchronous=NORMAL")

        # Set cache size to 10000 pages (about 40MB)
        cursor.execute("PRAGMA cache_size=10000")

        # Set temp_store to MEMORY for better performance
        cursor.execute("PRAGMA temp_store=MEMORY")

        # Set mmap_size to 30MB for better performance
        cursor.execute("PRAGMA mmap_size=30000000")

        # Run ANALYZE to update statistics
        cursor.execute("ANALYZE")

        # Run VACUUM to defragment the database
        cursor.execute("VACUUM")

        conn.commit()
        conn.close()
        logger.info(f"Successfully optimized database: {db_path}")
        return True
    except Exception as e:
        logger.error(f"Failed to optimize database {db_path}: {str(e)}")
        return False



def create_optimized_indexes(rag: SQLiteVectorRAG) -> None:
    """
    Create optimized indexes for a SQLiteVectorRAG instance.

    Args:
        rag: SQLiteVectorRAG instance
    """
    conn = sqlite3.connect(rag.db_path)
    cursor = conn.cursor()

    # Create index on content for full-text search
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_content ON documents(content)")

    # Create index on source for filtering
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_source ON documents(source)")

    # Create index on date for filtering
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_date ON documents(date)")

    # Create index on parent_id for hierarchical documents
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_parent_id ON documents(parent_id)")

    # Create index on hash for deduplication
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_hash ON documents(hash)")

    conn.commit()
    conn.close()

    logger.info(f"Created optimized indexes for SQLiteVectorRAG at {rag.db_path}")


def batch_add_documents(rag: SQLiteVectorRAG, documents: List[Dict[str, Any]], batch_size: int = 100) -> List[int]:
    """
    Add documents in batches for better performance.

    Args:
        rag: SQLiteVectorRAG instance
        documents: List of documents to add
        batch_size: Number of documents per batch

    Returns:
        List of document IDs
    """
    doc_ids = []

    # Process in batches
    for i in range(0, len(documents), batch_size):
        batch = documents[i:i+batch_size]
        batch_ids = rag.add_documents(batch)
        doc_ids.extend(batch_ids)

        # Log progress
        logger.info(f"Added batch {i//batch_size + 1}/{(len(documents)-1)//batch_size + 1} ({len(batch_ids)} documents)")

    return doc_ids


def create_search_cache(rag: SQLiteVectorRAG) -> None:
    """
    Create a search cache table for frequently used queries.

    Args:
        rag: SQLiteVectorRAG instance
    """
    conn = sqlite3.connect(rag.db_path)
    cursor = conn.cursor()

    # Create cache table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS search_cache (
        query_hash TEXT PRIMARY KEY,
        query TEXT NOT NULL,
        results TEXT NOT NULL,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Create index on timestamp for cache expiration
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_cache_timestamp ON search_cache(timestamp)')

    conn.commit()
    conn.close()

    logger.info(f"Created search cache for SQLiteVectorRAG at {rag.db_path}")


def optimize_vector_search(embeddings: List[np.ndarray], query_embedding: np.ndarray, top_k: int) -> List[Tuple[int, float]]:
    """
    Optimize vector search using NumPy operations.

    Args:
        embeddings: List of document embeddings
        query_embedding: Query embedding
        top_k: Number of results to return

    Returns:
        List of (index, score) tuples
    """
    # Convert list of embeddings to a 2D array
    embeddings_array = np.vstack(embeddings)

    # Normalize embeddings for cosine similarity
    embeddings_norm = np.linalg.norm(embeddings_array, axis=1, keepdims=True)
    embeddings_array = embeddings_array / embeddings_norm

    # Normalize query embedding
    query_norm = np.linalg.norm(query_embedding)
    query_embedding = query_embedding / query_norm

    # Calculate cosine similarity
    similarities = np.dot(embeddings_array, query_embedding)

    # Get top-k indices and scores
    if len(similarities) <= top_k:
        top_indices = np.argsort(similarities)[::-1]
        top_scores = similarities[top_indices]
    else:
        top_indices = np.argpartition(similarities, -top_k)[-top_k:]
        top_indices = top_indices[np.argsort(similarities[top_indices])[::-1]]
        top_scores = similarities[top_indices]

    # Return as list of tuples
    return [(int(idx), float(score)) for idx, score in zip(top_indices, top_scores)]


class OptimizedSQLiteVectorRAG(SQLiteVectorRAG):
    """
    Optimized version of SQLiteVectorRAG with performance improvements.
    """

    def __init__(self, *args, **kwargs):
        """Initialize OptimizedSQLiteVectorRAG."""
        # Extract optimization parameters
        self.use_cache = kwargs.pop("use_cache", True)
        self.cache_ttl = kwargs.pop("cache_ttl", 3600)  # 1 hour
        self.batch_size = kwargs.pop("batch_size", 100)

        # Initialize parent class
        super().__init__(*args, **kwargs)

        # Optimize database
        optimize_database(self.db_path)

        # Create optimized indexes
        create_optimized_indexes(self)

        # Create search cache if enabled
        if self.use_cache:
            create_search_cache(self)

        # Initialize cache
        self.query_cache = {}

    def add_documents(self, documents: List[Dict[str, Any]], **kwargs) -> List[int]:
        """
        Add documents in batches for better performance.

        Args:
            documents: List of documents to add
            **kwargs: Additional arguments

        Returns:
            List of document IDs
        """
        if len(documents) > self.batch_size:
            return batch_add_documents(self, documents, self.batch_size)
        else:
            return super().add_documents(documents, **kwargs)

    def search(self, query: str, top_k: Optional[int] = None, hybrid_weight: float = 0.7, **kwargs) -> List[Dict[str, Any]]:
        """
        Search for documents with caching.

        Args:
            query: Query string
            top_k: Number of results to return
            hybrid_weight: Weight for vector search vs keyword search
            **kwargs: Additional arguments

        Returns:
            List of search results
        """
        # Check cache if enabled
        if self.use_cache:
            cache_key = f"{query}:{top_k or self.top_k}:{hybrid_weight}"
            if cache_key in self.query_cache:
                cache_entry = self.query_cache[cache_key]
                if time.time() - cache_entry["timestamp"] < self.cache_ttl:
                    logger.info(f"Cache hit for query: {query[:50]}...")
                    return cache_entry["results"]

        # Perform search
        results = super().search(query, top_k, hybrid_weight, **kwargs)

        # Update cache if enabled
        if self.use_cache:
            self.query_cache[cache_key] = {
                "results": results,
                "timestamp": time.time()
            }

            # Prune cache if it gets too large
            if len(self.query_cache) > 1000:
                oldest_key = min(self.query_cache.keys(), key=lambda k: self.query_cache[k]["timestamp"])
                del self.query_cache[oldest_key]

        return results

    def clear_cache(self) -> None:
        """Clear the search cache."""
        self.query_cache = {}

        if self.use_cache:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("DELETE FROM search_cache")
            conn.commit()
            conn.close()

        logger.info("Cleared search cache")

    def close(self) -> None:
        """Close the connection and clear the cache."""
        self.clear_cache()
        super().close()
