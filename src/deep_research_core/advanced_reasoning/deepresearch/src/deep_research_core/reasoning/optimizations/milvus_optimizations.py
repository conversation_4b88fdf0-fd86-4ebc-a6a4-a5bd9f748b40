"""
Performance optimizations for MilvusRAG.

This module contains functions and utilities to optimize the performance
of MilvusRAG for different use cases.
"""

import time
from typing import Dict, Any, List, Optional, Tuple, Union

import numpy as np

from ..milvus_rag import MilvusRAG
from ...utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)


def optimize_milvus_collection(milvus_rag: MilvusRAG, index_type: str = "HNSW", metric_type: str = "IP") -> None:
    """
    Optimize a Milvus collection for better performance.
    
    Args:
        milvus_rag: MilvusRAG instance
        index_type: Type of index to create (HNSW, IVF_FLAT, etc.)
        metric_type: Metric type (IP for inner product, L2 for Euclidean)
    """
    # Get the collection name
    collection_name = milvus_rag.vector_store.collection_name
    
    # Create index parameters based on index type
    if index_type == "HNSW":
        index_params = {
            "index_type": "HNSW",
            "metric_type": metric_type,
            "params": {"M": 16, "efConstruction": 500}
        }
    elif index_type == "IVF_FLAT":
        index_params = {
            "index_type": "IVF_FLAT",
            "metric_type": metric_type,
            "params": {"nlist": 1024}
        }
    elif index_type == "IVF_SQ8":
        index_params = {
            "index_type": "IVF_SQ8",
            "metric_type": metric_type,
            "params": {"nlist": 1024}
        }
    else:
        raise ValueError(f"Unsupported index type: {index_type}")
    
    # Create index on the embedding field
    milvus_rag.vector_store.client.create_index(
        collection_name=collection_name,
        field_name="embedding",
        index_params=index_params
    )
    
    # Set search parameters
    if index_type == "HNSW":
        search_params = {"ef": 100}
    elif index_type in ["IVF_FLAT", "IVF_SQ8"]:
        search_params = {"nprobe": 16}
    else:
        search_params = {}
    
    # Store search parameters in the vector store
    milvus_rag.vector_store.search_params = search_params
    
    logger.info(f"Optimized Milvus collection {collection_name} with {index_type} index")


def batch_add_documents(milvus_rag: MilvusRAG, documents: List[Dict[str, Any]], batch_size: int = 100) -> List[int]:
    """
    Add documents in batches for better performance.
    
    Args:
        milvus_rag: MilvusRAG instance
        documents: List of documents to add
        batch_size: Number of documents per batch
        
    Returns:
        List of document IDs
    """
    doc_ids = []
    
    # Process in batches
    for i in range(0, len(documents), batch_size):
        batch = documents[i:i+batch_size]
        batch_ids = milvus_rag.add_documents(batch)
        doc_ids.extend(batch_ids)
        
        # Log progress
        logger.info(f"Added batch {i//batch_size + 1}/{(len(documents)-1)//batch_size + 1} ({len(batch_ids)} documents)")
    
    return doc_ids


def create_partitions(milvus_rag: MilvusRAG, partition_key: str = "source") -> None:
    """
    Create partitions in Milvus for better performance with large datasets.
    
    Args:
        milvus_rag: MilvusRAG instance
        partition_key: Field to use for partitioning
    """
    # Get the collection name
    collection_name = milvus_rag.vector_store.collection_name
    
    # Get unique values for the partition key
    conn = milvus_rag.vector_store.client
    results = conn.query(
        collection_name=collection_name,
        expr=f"{partition_key} != ''",
        output_fields=[partition_key]
    )
    
    # Extract unique values
    unique_values = set()
    for result in results:
        if partition_key in result and result[partition_key]:
            unique_values.add(result[partition_key])
    
    # Create partitions for each unique value
    for value in unique_values:
        partition_name = f"{partition_key}_{value}".replace(" ", "_").lower()
        try:
            conn.create_partition(
                collection_name=collection_name,
                partition_name=partition_name
            )
            logger.info(f"Created partition {partition_name} in collection {collection_name}")
        except Exception as e:
            logger.warning(f"Failed to create partition {partition_name}: {str(e)}")
    
    logger.info(f"Created {len(unique_values)} partitions in collection {collection_name}")


class OptimizedMilvusRAG(MilvusRAG):
    """
    Optimized version of MilvusRAG with performance improvements.
    """
    
    def __init__(self, *args, **kwargs):
        """Initialize OptimizedMilvusRAG."""
        # Extract optimization parameters
        self.use_cache = kwargs.pop("use_cache", True)
        self.cache_ttl = kwargs.pop("cache_ttl", 3600)  # 1 hour
        self.batch_size = kwargs.pop("batch_size", 100)
        self.index_type = kwargs.pop("index_type", "HNSW")
        self.metric_type = kwargs.pop("metric_type", "IP")
        self.use_partitions = kwargs.pop("use_partitions", False)
        self.partition_key = kwargs.pop("partition_key", "source")
        
        # Initialize parent class
        super().__init__(*args, **kwargs)
        
        # Optimize Milvus collection
        optimize_milvus_collection(self, self.index_type, self.metric_type)
        
        # Create partitions if enabled
        if self.use_partitions:
            create_partitions(self, self.partition_key)
        
        # Initialize cache
        self.query_cache = {}
    
    def add_documents(self, documents: List[Dict[str, Any]], **kwargs) -> List[int]:
        """
        Add documents in batches for better performance.
        
        Args:
            documents: List of documents to add
            **kwargs: Additional arguments
            
        Returns:
            List of document IDs
        """
        if len(documents) > self.batch_size:
            return batch_add_documents(self, documents, self.batch_size)
        else:
            return super().add_documents(documents, **kwargs)
    
    def search(
        self, 
        query: str,
        top_k: Optional[int] = None,
        filter_expr: Optional[str] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Search for documents with caching.
        
        Args:
            query: Query string
            top_k: Number of results to return
            filter_expr: Filter expression
            **kwargs: Additional arguments
            
        Returns:
            List of search results
        """
        # Check cache if enabled
        if self.use_cache:
            cache_key = f"{query}:{top_k or self.top_k}:{filter_expr}"
            if cache_key in self.query_cache:
                cache_entry = self.query_cache[cache_key]
                if time.time() - cache_entry["timestamp"] < self.cache_ttl:
                    logger.info(f"Cache hit for query: {query[:50]}...")
                    return cache_entry["results"]
        
        # Perform search
        results = super().search(query, top_k, filter_expr, **kwargs)
        
        # Update cache if enabled
        if self.use_cache:
            self.query_cache[cache_key] = {
                "results": results,
                "timestamp": time.time()
            }
            
            # Prune cache if it gets too large
            if len(self.query_cache) > 1000:
                oldest_key = min(self.query_cache.keys(), key=lambda k: self.query_cache[k]["timestamp"])
                del self.query_cache[oldest_key]
        
        return results
    
    def clear_cache(self) -> None:
        """Clear the search cache."""
        self.query_cache = {}
        logger.info("Cleared search cache")
    
    def close(self) -> None:
        """Close the connection and clear the cache."""
        self.clear_cache()
        super().close()
    
    def process(
        self, 
        query: str,
        filter_expr: Optional[str] = None,
        callback: Optional[callable] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process a query with optimized performance.
        
        Args:
            query: The query to process
            filter_expr: Optional filter expression
            callback: Optional callback function
            **kwargs: Additional arguments
            
        Returns:
            Dictionary with results
        """
        # Use partitions if enabled
        if self.use_partitions and "partition" in kwargs:
            partition = kwargs.pop("partition")
            if filter_expr:
                filter_expr = f"({filter_expr}) && ({self.partition_key} == '{partition}')"
            else:
                filter_expr = f"{self.partition_key} == '{partition}'"
        
        # Call parent method
        return super().process(query, filter_expr, callback, **kwargs)
