"""
Optimizations for RAG implementations.

This package contains optimized versions of RAG implementations
with performance improvements.
"""

from .sqlite_optimizations import OptimizedSQLiteVectorRAG, optimize_database, create_optimized_indexes, batch_add_documents
from .milvus_optimizations import OptimizedMilvusRAG, optimize_milvus_collection, create_partitions

__all__ = [
    'OptimizedSQLiteVectorRAG',
    'OptimizedMilvusRAG',
    'optimize_database',
    'create_optimized_indexes',
    'batch_add_documents',
    'optimize_milvus_collection',
    'create_partitions'
]
