"""
Fact Checker for Deep Research Core.

This module provides a fact checker that verifies facts in text.
"""

import json
from functools import lru_cache
from typing import Dict, List, Any, Optional, Union, Tuple

from ..models.api.openai import openai_provider
from ..models.api.anthropic import anthropic_provider
from ..models.api.openrouter import openrouter_provider

from ..utils.structured_logging import get_logger
from ..utils.performance_metrics import measure_latency
from ..utils.distributed_tracing import trace_function, span

# Create a logger
logger = get_logger(__name__)


class FactChecker:
    """
    Fact Checker.

    This class provides a fact checker that verifies facts in text.
    """

    def __init__(self, use_cache: bool = True, cache_size: int = 100, provider: str = "openai",
        model: Optional[str] = None,
        temperature: float = 0.3,
        max_tokens: int = 1000,
        **kwargs
    ):
        """
        Initialize the Fact Checker.

        Args:
            provider: LLM provider (openai, anthropic, openrouter)
            model: LLM model name
            temperature: LLM temperature
            max_tokens: Maximum tokens for LLM response
        """
        self.provider = provider
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens

        # Initialize LLM provider
        if provider == "openai":
            self.llm_provider = openai_provider
        elif provider == "anthropic":
            self.llm_provider = anthropic_provider
        elif provider == "openrouter":
            self.llm_provider = openrouter_provider
        else:
            raise ValueError(f"Unsupported provider: {provider}")

        # Set up caching
        if use_cache:
            self._generate_cached = lru_cache(maxsize=cache_size)(self._generate)
        else:
            self._generate_cached = self._generate

        logger.info(f"Initialized FactChecker with provider {provider} and model {model}")

    def _generate(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        json_format: bool = False
    ) -> str:
        """
        Generate text using the model.

        Args:
            prompt: The prompt to generate from
            system_prompt: Optional system prompt
            temperature: Optional temperature override
            max_tokens: Optional max tokens override
            json_format: Whether to return JSON format

        Returns:
            Generated text
        """
        return self.llm_provider.generate(
            prompt=prompt,
            model=self.model,
            system_prompt=system_prompt,
            temperature=temperature if temperature is not None else self.temperature,
            max_tokens=max_tokens if max_tokens is not None else self.max_tokens,
            json_format=json_format
        )

    @trace_function(name="fact_checker_check_facts")
    @measure_latency("fact_checker_check_facts")
    def check_facts(
        self,
        text: str,
        reference_text: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Check facts in text.

        Args:
            text: Text to check facts in
            reference_text: Optional reference text to check facts against

        Returns:
            Dictionary with fact checking results
        """
        # Extract facts from text
        facts = self._extract_facts(text)

        # Verify facts
        if reference_text:
            # Verify against reference text
            verified_facts = self._verify_facts_against_reference(facts, reference_text)
        else:
            # Verify using LLM
            verified_facts = self._verify_facts_using_llm(facts)

        # Prepare result
        result = {
            "text": text,
            "facts": facts,
            "verified_facts": verified_facts,
            "accuracy_score": self._calculate_accuracy_score(verified_facts)
        }

        return result

    @trace_function(name="fact_checker_extract_facts")
    @measure_latency("fact_checker_extract_facts")
    def _extract_facts(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract facts from text.

        Args:
            text: Text to extract facts from

        Returns:
            List of fact dictionaries
        """
        prompt = f"""
        Extract factual claims from the following text. A factual claim is a statement that can be verified as true or false.
        Return a JSON array of objects, where each object has the following properties:
        - "claim": The factual claim
        - "context": The surrounding context of the claim
        - "confidence": Your confidence that this is a factual claim (0-1)

        Text:
        {text}
        """

        try:
            response = self.llm_provider.generate(
                prompt=prompt,
                model=self.model,
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )

            # Parse the response
            try:
                facts = json.loads(response)
                if isinstance(facts, list):
                    return facts
                else:
                    logger.warning(f"Invalid facts format: {response}")
                    return []
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse facts: {response}")
                # Try to extract facts from the response using a simple heuristic
                if "[" in response and "]" in response:
                    facts_str = response[response.find("["):response.rfind("]")+1]
                    try:
                        facts = json.loads(facts_str)
                        if isinstance(facts, list):
                            return facts
                    except:
                        pass
                return []

        except Exception as e:
            logger.error(f"Error extracting facts: {str(e)}")
            return []

    @trace_function(name="fact_checker_verify_facts_against_reference")
    @measure_latency("fact_checker_verify_facts_against_reference")
    def _verify_facts_against_reference(
        self,
        facts: List[Dict[str, Any]],
        reference_text: str
    ) -> List[Dict[str, Any]]:
        """
        Verify facts against reference text.

        Args:
            facts: List of fact dictionaries
            reference_text: Reference text to verify facts against

        Returns:
            List of verified fact dictionaries
        """
        verified_facts = []
        for fact in facts:
            prompt = f"""
            Verify if the following factual claim is supported by the reference text.

            Claim: {fact['claim']}

            Reference Text:
            {reference_text}

            Return a JSON object with the following properties:
            - "claim": The factual claim
            - "is_supported": Whether the claim is supported by the reference text (true/false)
            - "evidence": The evidence from the reference text that supports or contradicts the claim
            - "confidence": Your confidence in the verification (0-1)
            """

            try:
                response = self.llm_provider.generate(
                    prompt=prompt,
                    model=self.model,
                    temperature=self.temperature,
                    max_tokens=self.max_tokens
                )

                # Parse the response
                try:
                    verified_fact = json.loads(response)
                    verified_fact["original_claim"] = fact
                    verified_facts.append(verified_fact)
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse verified fact: {response}")
                    # Try to extract the verified fact from the response using a simple heuristic
                    if "{" in response and "}" in response:
                        verified_fact_str = response[response.find("{"):response.rfind("}")+1]
                        try:
                            verified_fact = json.loads(verified_fact_str)
                            verified_fact["original_claim"] = fact
                            verified_facts.append(verified_fact)
                        except:
                            pass

            except Exception as e:
                logger.error(f"Error verifying fact: {str(e)}")

        return verified_facts

    @trace_function(name="fact_checker_verify_facts_using_llm")
    @measure_latency("fact_checker_verify_facts_using_llm")
    def _verify_facts_using_llm(self, facts: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Verify facts using LLM.

        Args:
            facts: List of fact dictionaries

        Returns:
            List of verified fact dictionaries
        """
        verified_facts = []
        for fact in facts:
            prompt = f"""
            Verify the following factual claim based on your knowledge.

            Claim: {fact['claim']}
            Context: {fact.get('context', 'No context provided')}

            Return a JSON object with the following properties:
            - "claim": The factual claim
            - "is_accurate": Whether the claim is accurate based on your knowledge (true/false)
            - "explanation": Your explanation for why the claim is accurate or inaccurate
            - "confidence": Your confidence in the verification (0-1)
            - "needs_citation": Whether the claim needs a citation (true/false)
            """

            try:
                response = self.llm_provider.generate(
                    prompt=prompt,
                    model=self.model,
                    temperature=self.temperature,
                    max_tokens=self.max_tokens
                )

                # Parse the response
                try:
                    verified_fact = json.loads(response)
                    verified_fact["original_claim"] = fact
                    verified_facts.append(verified_fact)
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse verified fact: {response}")
                    # Try to extract the verified fact from the response using a simple heuristic
                    if "{" in response and "}" in response:
                        verified_fact_str = response[response.find("{"):response.rfind("}")+1]
                        try:
                            verified_fact = json.loads(verified_fact_str)
                            verified_fact["original_claim"] = fact
                            verified_facts.append(verified_fact)
                        except:
                            pass

            except Exception as e:
                logger.error(f"Error verifying fact: {str(e)}")

        return verified_facts

    @trace_function(name="fact_checker_calculate_accuracy_score")
    @measure_latency("fact_checker_calculate_accuracy_score")
    def _calculate_accuracy_score(self, verified_facts: List[Dict[str, Any]]) -> float:
        """
        Calculate accuracy score.

        Args:
            verified_facts: List of verified fact dictionaries

        Returns:
            Accuracy score (0-1)
        """
        if not verified_facts:
            return 0.0

        # Calculate accuracy score
        total_score = 0.0
        for fact in verified_facts:
            if "is_supported" in fact:
                # Reference-based verification
                is_accurate = fact["is_supported"]
            elif "is_accurate" in fact:
                # LLM-based verification
                is_accurate = fact["is_accurate"]
            else:
                continue

            confidence = fact.get("confidence", 0.5)
            if isinstance(is_accurate, bool):
                score = confidence if is_accurate else 0.0
            elif isinstance(is_accurate, str):
                score = confidence if is_accurate.lower() == "true" else 0.0
            else:
                score = 0.0

            total_score += score

        return total_score / len(verified_facts)

    @trace_function(name="fact_checker_check_claim")
    @measure_latency("fact_checker_check_claim")
    def check_claim(
        self,
        claim: str,
        sources: List[Dict[str, Any]],
        context: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Check a single claim against sources.

        Args:
            claim: The claim to check
            sources: List of source dictionaries
            context: Optional context information

        Returns:
            Dictionary with fact checking results
        """
        if not sources:
            logger.warning("No sources provided for fact checking")
            return {
                "claim": claim,
                "is_verified": False,
                "confidence_score": 0.0,
                "supporting_sources": [],
                "contradicting_sources": [],
                "explanation": "No sources provided for verification"
            }

        # Combine sources into a single reference text
        reference_text = "\n\n".join([
            f"Source {i+1} ({source.get('id', f'source-{i}')}): {source.get('content', '')}"
            for i, source in enumerate(sources)
        ])

        prompt = f"""
        Verify the following claim against the provided sources.

        Claim: {claim}

        Sources:
        {reference_text}

        Return a JSON object with the following properties:
        - "claim": The claim being verified
        - "is_verified": Whether the claim is verified by the sources (true/false)
        - "confidence_score": Your confidence in the verification (0-1)
        - "supporting_sources": Array of source IDs that support the claim
        - "contradicting_sources": Array of source IDs that contradict the claim
        - "explanation": Your explanation of the verification result
        """

        try:
            response = self.llm_provider.generate(
                prompt=prompt,
                model=self.model,
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )

            # Parse the response
            try:
                result = json.loads(response)
                return result
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse fact check result: {response}")
                # Try to extract the result from the response using a simple heuristic
                if "{" in response and "}" in response:
                    result_str = response[response.find("{"):response.rfind("}")+1]
                    try:
                        result = json.loads(result_str)
                        return result
                    except:
                        pass
                return {
                    "claim": claim,
                    "is_verified": False,
                    "confidence_score": 0.0,
                    "supporting_sources": [],
                    "contradicting_sources": [],
                    "explanation": "Failed to parse result",
                    "raw_response": response
                }

        except Exception as e:
            logger.error(f"Error checking claim: {str(e)}")
            return {
                "claim": claim,
                "is_verified": False,
                "confidence_score": 0.0,
                "supporting_sources": [],
                "contradicting_sources": [],
                "explanation": f"Error: {str(e)}",
                "error": str(e)
            }

    @trace_function(name="fact_checker_check_multiple_claims")
    @measure_latency("fact_checker_check_multiple_claims")
    def check_multiple_claims(
        self,
        claims: List[str],
        sources: List[Dict[str, Any]],
        context: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Check multiple claims against sources.

        Args:
            claims: List of claims to check
            sources: List of source dictionaries
            context: Optional context information

        Returns:
            Dictionary with fact checking results for all claims
        """
        results = []
        for claim in claims:
            result = self.check_claim(claim, sources, context, **kwargs)
            results.append(result)

        # Calculate overall verification metrics
        verified_claims = [r for r in results if r.get("is_verified", False)]
        verification_rate = len(verified_claims) / len(claims) if claims else 0.0
        avg_confidence = sum(r.get("confidence_score", 0.0) for r in results) / len(results) if results else 0.0

        return {
            "claims_count": len(claims),
            "verified_claims_count": len(verified_claims),
            "verification_rate": verification_rate,
            "average_confidence": avg_confidence,
            "results": results
        }

    @trace_function(name="fact_checker_check_consistency")
    @measure_latency("fact_checker_check_consistency")
    def check_consistency(
        self,
        text1: str,
        text2: str,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Check consistency between two texts.

        Args:
            text1: First text
            text2: Second text

        Returns:
            Dictionary with consistency checking results
        """
        prompt = f"""
        Check the consistency between the following two texts. Identify any contradictions or inconsistencies.

        Text 1:
        {text1}

        Text 2:
        {text2}

        Return a JSON object with the following properties:
        - "is_consistent": Whether the texts are consistent with each other (true/false)
        - "contradictions": An array of objects, where each object describes a contradiction:
          - "statement1": The statement from Text 1
          - "statement2": The statement from Text 2
          - "explanation": Explanation of the contradiction
        - "consistency_score": A score from 0 to 1 indicating the overall consistency
        - "explanation": Overall explanation of the consistency analysis
        """

        try:
            response = self.llm_provider.generate(
                prompt=prompt,
                model=self.model,
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )

            # Parse the response
            try:
                result = json.loads(response)
                return result
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse consistency result: {response}")
                # Try to extract the result from the response using a simple heuristic
                if "{" in response and "}" in response:
                    result_str = response[response.find("{"):response.rfind("}")+1]
                    try:
                        result = json.loads(result_str)
                        return result
                    except:
                        pass
                return {
                    "is_consistent": None,
                    "contradictions": [],
                    "consistency_score": 0.0,
                    "explanation": "Failed to parse result",
                    "raw_response": response
                }

        except Exception as e:
            logger.error(f"Error checking consistency: {str(e)}")
            return {
                "is_consistent": None,
                "contradictions": [],
                "consistency_score": 0.0,
                "explanation": f"Error: {str(e)}",
                "error": str(e)
            }
