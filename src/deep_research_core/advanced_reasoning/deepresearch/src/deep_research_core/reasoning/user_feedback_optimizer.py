"""
User feedback-based parameter optimization for ToTRAG.

This module provides utilities for optimizing ToTRAG parameters
based on user feedback using reinforcement learning techniques.
"""

import os
import json
import time
import numpy as np
import pickle
from functools import lru_cache
from typing import Dict, List, Any, Optional, Tuple, Set, Union
from dataclasses import dataclass, field
from collections import defaultdict

from ..utils.structured_logging import get_logger
from ..utils.distributed_tracing import trace_function
from ..utils.performance_metrics import measure_latency

# Create a logger
logger = get_logger(__name__)

@dataclass
class FeedbackRecord:
    """Record of user feedback on a ToTRAG result."""
    query: str
    parameters: Dict[str, Any]
    rating: float  # User rating (1-10)
    feedback_text: Optional[str] = None
    timestamp: float = field(default_factory=time.time)
    execution_time: float = 0.0
    result_quality: Optional[Dict[str, float]] = None

@dataclass
class ParameterConfig:
    """Configuration for a ToTRAG parameter."""
    name: str
    min_value: float
    max_value: float
    step_size: float
    current_value: float
    description: str
    is_discrete: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "name": self.name,
            "min_value": self.min_value,
            "max_value": self.max_value,
            "step_size": self.step_size,
            "current_value": self.current_value,
            "description": self.description,
            "is_discrete": self.is_discrete
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ParameterConfig':
        """Create from dictionary."""
        return cls(
            name=data["name"],
            min_value=data["min_value"],
            max_value=data["max_value"],
            step_size=data["step_size"],
            current_value=data["current_value"],
            description=data["description"],
            is_discrete=data.get("is_discrete", False)
        )

class UserFeedbackOptimizer:
    """
    User feedback-based parameter optimization for ToTRAG.
    
    This class implements parameter optimization based on user feedback
    using reinforcement learning techniques.
    """
    
    def __init__(self, use_cache: bool = True, cache_size: int = 100, learning_rate: float = 0.1,
        exploration_rate: float = 0.2,
        decay_rate: float = 0.99,
        min_exploration_rate: float = 0.05,
        max_feedback_records: int = 1000,
        data_path: Optional[str] = None
    ):
        """
        Initialize the user feedback optimizer.
        
        Args:
            learning_rate: Learning rate for parameter updates
            exploration_rate: Initial exploration rate for trying new parameters
            decay_rate: Decay rate for exploration
            min_exploration_rate: Minimum exploration rate
            max_feedback_records: Maximum number of feedback records to keep
            data_path: Path to save/load data
        """
        self.learning_rate = learning_rate
        self.exploration_rate = exploration_rate
        self.decay_rate = decay_rate
        self.min_exploration_rate = min_exploration_rate
        self.max_feedback_records = max_feedback_records
        self.data_path = data_path or os.path.join(os.path.expanduser("~"), ".deep_research_core", "user_feedback")
        
        # Create data directory if it doesn't exist
        os.makedirs(self.data_path, exist_ok=True)
        
        # Initialize parameter configurations
        self.parameter_configs: Dict[str, ParameterConfig] = {}
        self._initialize_default_parameters()
        
        # Initialize feedback records
        self.feedback_records: List[FeedbackRecord] = []
        
        # Initialize parameter value history
        self.parameter_history: Dict[str, List[Tuple[float, float]]] = defaultdict(list)
        
        # Load existing data if available
        self._load_data()
        
        logger.info(
            f"Initialized UserFeedbackOptimizer with learning_rate={learning_rate}, "
            f"exploration_rate={exploration_rate}, data_path={self.data_path}"
        )
    
    def _initialize_default_parameters(self) -> None:
        """
        Initialize default parameter configurations.
        """
        self.parameter_configs = {
            "temperature": ParameterConfig(
                name="temperature",
                min_value=0.1,
                max_value=1.0,
                step_size=0.1,
                current_value=0.7,
                description="Sampling temperature for the language model"
            ),
            "max_branches": ParameterConfig(
                name="max_branches",
                min_value=2,
                max_value=5,
                step_size=1,
                current_value=3,
                description="Maximum number of branches to explore at each step",
                is_discrete=True
            ),
            "max_depth": ParameterConfig(
                name="max_depth",
                min_value=2,
                max_value=5,
                step_size=1,
                current_value=3,
                description="Maximum depth of the reasoning tree",
                is_discrete=True
            ),
            "top_k": ParameterConfig(
                name="top_k",
                min_value=3,
                max_value=10,
                step_size=1,
                current_value=5,
                description="Number of documents to retrieve",
                is_discrete=True
            )
        }
    
    def _load_data(self) -> None:
        """
        Load data from disk.
        """
        # Load parameter configurations
        config_path = os.path.join(self.data_path, "parameter_configs.json")
        if os.path.exists(config_path):
            try:
                with open(config_path, "r", encoding="utf-8") as f:
                    config_data = json.load(f)
                
                for name, data in config_data.items():
                    self.parameter_configs[name] = ParameterConfig.from_dict(data)
                
                logger.info(f"Loaded parameter configurations from {config_path}")
            except Exception as e:
                logger.error(f"Error loading parameter configurations: {str(e)}")
        
        # Load feedback records
        records_path = os.path.join(self.data_path, "feedback_records.pkl")
        if os.path.exists(records_path):
            try:
                with open(records_path, "rb") as f:
                    self.feedback_records = pickle.load(f)
                logger.info(f"Loaded {len(self.feedback_records)} feedback records from {records_path}")
            except Exception as e:
                logger.error(f"Error loading feedback records: {str(e)}")
        
        # Load parameter history
        history_path = os.path.join(self.data_path, "parameter_history.pkl")
        if os.path.exists(history_path):
            try:
                with open(history_path, "rb") as f:
                    self.parameter_history = pickle.load(f)
                logger.info(f"Loaded parameter history from {history_path}")
            except Exception as e:
                logger.error(f"Error loading parameter history: {str(e)}")
    
    def _save_data(self) -> None:
        """
        Save data to disk.
        """
        # Save parameter configurations
        config_path = os.path.join(self.data_path, "parameter_configs.json")
        try:
            config_data = {name: config.to_dict() for name, config in self.parameter_configs.items()}
            with open(config_path, "w", encoding="utf-8") as f:
                json.dump(config_data, f, indent=2)
            logger.info(f"Saved parameter configurations to {config_path}")
        except Exception as e:
            logger.error(f"Error saving parameter configurations: {str(e)}")
        
        # Save feedback records
        records_path = os.path.join(self.data_path, "feedback_records.pkl")
        try:
            with open(records_path, "wb") as f:
                pickle.dump(self.feedback_records, f)
            logger.info(f"Saved {len(self.feedback_records)} feedback records to {records_path}")
        except Exception as e:
            logger.error(f"Error saving feedback records: {str(e)}")
        
        # Save parameter history
        history_path = os.path.join(self.data_path, "parameter_history.pkl")
        try:
            with open(history_path, "wb") as f:
                pickle.dump(self.parameter_history, f)
            logger.info(f"Saved parameter history to {history_path}")
        except Exception as e:
            logger.error(f"Error saving parameter history: {str(e)}")
    
    def add_parameter_config(self, config: ParameterConfig) -> None:
        """
        Add a new parameter configuration.
        
        Args:
            config: Parameter configuration
        """
        self.parameter_configs[config.name] = config
        logger.info(f"Added parameter configuration for {config.name}")
        self._save_data()
    
    def get_parameter_config(self, name: str) -> Optional[ParameterConfig]:
        """
        Get a parameter configuration.
        
        Args:
            name: Parameter name
            
        Returns:
            Parameter configuration or None if not found
        """
        return self.parameter_configs.get(name)
    
    def get_optimized_parameters(
        self,
        query_type: Optional[str] = None,
        explore: bool = True
    ) -> Dict[str, Any]:
        """
        Get optimized parameters based on user feedback.
        
        Args:
            query_type: Type of query (optional)
            explore: Whether to explore new parameter values
            
        Returns:
            Dictionary of optimized parameters
        """
        # Start with current parameter values
        params = {name: config.current_value for name, config in self.parameter_configs.items()}
        
        # Apply exploration if enabled
        if explore and np.random.random() < self.exploration_rate:
            # Choose a random parameter to explore
            param_name = np.random.choice(list(self.parameter_configs.keys()))
            config = self.parameter_configs[param_name]
            
            # Generate a new value within the allowed range
            if config.is_discrete:
                # For discrete parameters, choose a random step
                steps = int((config.max_value - config.min_value) / config.step_size) + 1
                step = np.random.randint(0, steps)
                new_value = config.min_value + step * config.step_size
            else:
                # For continuous parameters, choose a random value
                new_value = np.random.uniform(config.min_value, config.max_value)
                # Round to step size
                new_value = round(new_value / config.step_size) * config.step_size
                # Ensure within bounds
                new_value = max(config.min_value, min(config.max_value, new_value))
            
            # Update the parameter
            params[param_name] = new_value
            
            logger.info(f"Exploring new value for {param_name}: {new_value}")
        
        # If query type is provided, try to optimize for that type
        if query_type and len(self.feedback_records) > 0:
            # Filter feedback records by query type
            relevant_records = [
                record for record in self.feedback_records
                if self._get_query_type(record.query) == query_type
            ]
            
            if relevant_records:
                # Find the best performing parameters for this query type
                best_record = max(relevant_records, key=lambda r: r.rating)
                
                # Blend with current parameters (70% best, 30% current)
                for name, value in best_record.parameters.items():
                    if name in params:
                        params[name] = 0.7 * value + 0.3 * params[name]
                        
                        # Round discrete parameters
                        if name in self.parameter_configs and self.parameter_configs[name].is_discrete:
                            params[name] = round(params[name])
                
                logger.info(f"Optimized parameters for query type: {query_type}")
        
        return params
    
    def _get_query_type(self, query: str) -> str:
        """
        Determine the type of a query.
        
        Args:
            query: Query string
            
        Returns:
            Query type
        """
        # Simple heuristic-based query type detection
        query = query.lower()
        
        if "?" in query:
            if any(word in query for word in ["what", "who", "where", "when", "which"]):
                return "factual"
            elif any(word in query for word in ["how", "why"]):
                return "explanatory"
            elif any(word in query for word in ["can", "could", "would", "will"]):
                return "yes_no"
            else:
                return "question"
        elif any(word in query for word in ["compare", "contrast", "difference", "similarities"]):
            return "comparative"
        elif any(word in query for word in ["list", "enumerate", "examples"]):
            return "listing"
        elif any(word in query for word in ["analyze", "analysis", "evaluate", "assessment"]):
            return "analytical"
        elif any(word in query for word in ["summarize", "summary", "overview"]):
            return "summary"
        else:
            return "general"
    
    def add_feedback(
        self,
        query: str,
        parameters: Dict[str, Any],
        rating: float,
        feedback_text: Optional[str] = None,
        execution_time: float = 0.0,
        result_quality: Optional[Dict[str, float]] = None
    ) -> None:
        """
        Add user feedback for a query.
        
        Args:
            query: Query string
            parameters: Parameters used for the query
            rating: User rating (1-10)
            feedback_text: Optional feedback text
            execution_time: Execution time in seconds
            result_quality: Optional quality metrics
        """
        # Create feedback record
        record = FeedbackRecord(
            query=query,
            parameters=parameters,
            rating=rating,
            feedback_text=feedback_text,
            execution_time=execution_time,
            result_quality=result_quality
        )
        
        # Add to feedback records
        self.feedback_records.append(record)
        
        # Trim if needed
        if len(self.feedback_records) > self.max_feedback_records:
            # Sort by timestamp (oldest first)
            self.feedback_records.sort(key=lambda x: x.timestamp)
            
            # Keep only the most recent records
            self.feedback_records = self.feedback_records[-self.max_feedback_records:]
        
        # Update parameter history
        for name, value in parameters.items():
            if name in self.parameter_configs:
                self.parameter_history[name].append((value, rating))
                
                # Trim history if needed
                if len(self.parameter_history[name]) > 100:
                    self.parameter_history[name] = self.parameter_history[name][-100:]
        
        # Update parameters based on feedback
        self._update_parameters(record)
        
        # Decay exploration rate
        self.exploration_rate = max(
            self.min_exploration_rate,
            self.exploration_rate * self.decay_rate
        )
        
        # Save data
        self._save_data()
        
        logger.info(f"Added feedback for query: {query[:50]}..., rating: {rating}")
    
    def _update_parameters(self, record: FeedbackRecord) -> None:
        """
        Update parameters based on feedback.
        
        Args:
            record: Feedback record
        """
        # Only update if rating is good (above 7)
        if record.rating < 7.0:
            return
        
        # Update each parameter
        for name, value in record.parameters.items():
            if name in self.parameter_configs:
                config = self.parameter_configs[name]
                
                # Calculate update (move toward the value that got good feedback)
                update = self.learning_rate * (value - config.current_value)
                
                # Apply update
                new_value = config.current_value + update
                
                # Ensure within bounds
                new_value = max(config.min_value, min(config.max_value, new_value))
                
                # Round discrete parameters
                if config.is_discrete:
                    new_value = round(new_value)
                
                # Update current value
                config.current_value = new_value
                
                logger.info(f"Updated parameter {name}: {config.current_value:.4f}")
    
    def get_parameter_recommendations(self) -> Dict[str, Dict[str, Any]]:
        """
        Get parameter recommendations based on feedback history.
        
        Returns:
            Dictionary of parameter recommendations
        """
        recommendations = {}
        
        for name, history in self.parameter_history.items():
            if not history:
                continue
            
            # Extract values and ratings
            values = [value for value, _ in history]
            ratings = [rating for _, rating in history]
            
            # Calculate average rating for each unique value
            unique_values = set(values)
            value_ratings = defaultdict(list)
            
            for value, rating in history:
                value_ratings[value].append(rating)
            
            avg_ratings = {value: sum(ratings) / len(ratings) for value, ratings in value_ratings.items()}
            
            # Find the best value
            best_value = max(avg_ratings.items(), key=lambda x: x[1])[0]
            
            # Calculate correlation between value and rating
            if len(unique_values) > 1:
                correlation = np.corrcoef(values, ratings)[0, 1]
            else:
                correlation = 0.0
            
            # Create recommendation
            recommendations[name] = {
                "best_value": best_value,
                "current_value": self.parameter_configs[name].current_value,
                "correlation": correlation,
                "confidence": min(1.0, len(history) / 20.0),  # Higher confidence with more data
                "trend": "increase" if correlation > 0.2 else "decrease" if correlation < -0.2 else "stable"
            }
        
        return recommendations
    
    def get_query_type_recommendations(self) -> Dict[str, Dict[str, Any]]:
        """
        Get parameter recommendations for different query types.
        
        Returns:
            Dictionary of query type recommendations
        """
        if not self.feedback_records:
            return {}
        
        # Group feedback records by query type
        query_type_records = defaultdict(list)
        
        for record in self.feedback_records:
            query_type = self._get_query_type(record.query)
            query_type_records[query_type].append(record)
        
        # Generate recommendations for each query type
        recommendations = {}
        
        for query_type, records in query_type_records.items():
            if len(records) < 3:
                continue
            
            # Find the best performing parameters for this query type
            best_record = max(records, key=lambda r: r.rating)
            
            # Calculate average parameters for top 3 records
            top_records = sorted(records, key=lambda r: r.rating, reverse=True)[:3]
            avg_params = {}
            
            for param_name in self.parameter_configs:
                values = [record.parameters.get(param_name, self.parameter_configs[param_name].current_value) 
                         for record in top_records]
                avg_params[param_name] = sum(values) / len(values)
                
                # Round discrete parameters
                if param_name in self.parameter_configs and self.parameter_configs[param_name].is_discrete:
                    avg_params[param_name] = round(avg_params[param_name])
            
            # Create recommendation
            recommendations[query_type] = {
                "best_parameters": best_record.parameters,
                "average_parameters": avg_params,
                "confidence": min(1.0, len(records) / 10.0),  # Higher confidence with more data
                "sample_size": len(records)
            }
        
        return recommendations
