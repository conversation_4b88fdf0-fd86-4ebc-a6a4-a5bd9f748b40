"""
SQLite-based Vector Retrieval-Augmented Generation (RAG) with Vietnamese support.

This module extends the SQLiteVectorRAG implementation with specialized
Vietnamese embedding models and language-specific processing.
"""

import os
import time
import sqlite3
import numpy as np
from functools import lru_cache
from typing import Dict, Any, List, Optional, Tuple, Union, Callable

from .sqlite_vector_rag import SQLiteVectorRAG
from ..multilingual.vietnamese_embeddings import VietnameseEmbeddingFactory
from ..utils.structured_logging import get_logger
from ..utils.performance_metrics import measure_latency, measure_block_latency
from ..utils.distributed_tracing import trace_function, span

# Create a logger
logger = get_logger(__name__)

class SQLiteVectorRAGVietnamese(SQLiteVectorRAG):
    """
    Extends SQLiteVectorRAG with specialized Vietnamese support.
    
    This class improves the Vietnamese language capabilities of SQLiteVectorRAG
    by using specialized Vietnamese embedding models and language-specific processing.
    """
    
    def __init__(self, use_cache: bool = True, cache_size: int = 100, db_path: str,
        embedding_model: str = "phobert",
        provider: str = "openrouter",
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        top_k: int = 5,
        chunk_size: int = 1000,
        chunk_overlap: int = 200,
        api_key: Optional[str] = None,
        device: Optional[str] = None,
        cache_dir: Optional[str] = None
    ):
        """
        Initialize SQLiteVectorRAGVietnamese.
        
        Args:
            db_path: Path to the SQLite database
            embedding_model: Name of the Vietnamese embedding model to use
            provider: Model provider (openai, anthropic, openrouter)
            model: Model name
            temperature: Temperature for text generation
            max_tokens: Maximum number of tokens to generate
            top_k: Number of documents to retrieve
            chunk_size: Size of document chunks
            chunk_overlap: Overlap between document chunks
            api_key: API key for the provider
            device: Device to use for inference ("cpu", "cuda", "mps")
            cache_dir: Directory to cache models
        """
        # Initialize with a placeholder embedding model
        # We'll override the embedding functionality with Vietnamese-specific models
        super().__init__(
            db_path=db_path,
            embedding_model="all-MiniLM-L6-v2",  # Placeholder, will be overridden
            provider=provider,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            top_k=top_k,
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            api_key=api_key
        )
        
        # Store Vietnamese-specific parameters
        self.vietnamese_model_name = embedding_model
        self.device = device
        self.cache_dir = cache_dir
        
        # Initialize Vietnamese embedding model
        self.vietnamese_embeddings = VietnameseEmbeddingFactory.get_embedding_model(
            model_name=self.vietnamese_model_name,
            device=self.device,
            cache_dir=self.cache_dir
        )
        
        # Update embedding dimension
        self.embedding_dim = self.vietnamese_embeddings.get_embedding_dimension()
        
        # Set up caching
        if use_cache:
            self._generate_cached = lru_cache(maxsize=cache_size)(self._generate)
        else:
            self._generate_cached = self._generate
            
        logger.info(f"Initialized SQLiteVectorRAGVietnamese with model: {self.vietnamese_model_name}")
    
    def _get_embeddings(self, texts: List[str]) -> np.ndarray:
        """
        Get embeddings for texts using Vietnamese-specific models.
        
        Args:
            texts: List of texts to embed
            
        Returns:
            Numpy array of embeddings
        """
        return self.vietnamese_embeddings.get_embeddings(texts)
    
    @trace_function(name="sqlite_vector_rag_vietnamese_search")
    @measure_latency("sqlite_vector_rag_vietnamese_search")
    def search(
        self,
        query: str,
        top_k: Optional[int] = None,
        hybrid_weight: float = 0.7,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Search for documents similar to the query using Vietnamese-specific embeddings.
        
        Args:
            query: The query string
            top_k: Number of results to return (defaults to self.top_k)
            hybrid_weight: Weight for vector search vs keyword search (1.0 = vector only, 0.0 = keyword only)
            **kwargs: Additional arguments
            
        Returns:
            List of dictionaries containing the retrieved documents and their similarity scores
        """
        if top_k is None:
            top_k = self.top_k
        
        # Generate embedding for the query using Vietnamese-specific model
        query_embedding = self.vietnamese_embeddings.get_embedding(query)
        
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Get all documents
        cursor.execute("SELECT id, content, source, title, date, embedding FROM documents")
        rows = cursor.fetchall()
        
        # Calculate vector similarities
        similarities = []
        for row in rows:
            doc_embedding = self._deserialize_embedding(row["embedding"])
            
            # Calculate cosine similarity
            similarity = np.dot(query_embedding, doc_embedding) / (
                np.linalg.norm(query_embedding) * np.linalg.norm(doc_embedding)
            )
            
            # For hybrid search, we'll combine with keyword matching
            if hybrid_weight < 1.0:
                # Simple keyword matching (count matching terms)
                query_terms = set(query.lower().split())
                content_terms = set(row["content"].lower().split())
                matching_terms = query_terms.intersection(content_terms)
                keyword_score = len(matching_terms) / max(len(query_terms), 1)
                
                # Combine scores with the specified weight
                similarity = (hybrid_weight * similarity) + ((1 - hybrid_weight) * keyword_score)
            
            similarities.append((row, similarity))
        
        # Sort by similarity (descending)
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        # Take top_k results
        top_results = similarities[:top_k]
        
        # Format results
        results = []
        for row, score in top_results:
            result = {
                "id": row["id"],
                "content": row["content"],
                "source": row["source"],
                "title": row["title"],
                "date": row["date"],
                "score": float(score),
                "metadata": {
                    "source": row["source"],
                    "title": row["title"],
                    "date": row["date"]
                }
            }
            results.append(result)
        
        conn.close()
        
        return results
    
    @trace_function(name="sqlite_vector_rag_vietnamese_process")
    @measure_latency("sqlite_vector_rag_vietnamese_process")
    def process(
        self,
        query: str,
        top_k: Optional[int] = None,
        hybrid_weight: float = 0.7,
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        save_evaluation: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process a query using Vietnamese-specific embeddings and generate a response.
        
        Args:
            query: The query to process
            top_k: Number of results to return (defaults to self.top_k)
            hybrid_weight: Weight for vector search vs keyword search (1.0 = vector only, 0.0 = keyword only)
            custom_system_prompt: Custom system prompt to use
            custom_user_prompt: Custom user prompt template to use
            callback: Optional callback function for streaming
            save_evaluation: Whether to save evaluation data
            **kwargs: Additional arguments
            
        Returns:
            Dictionary containing the answer, retrieved documents, and other information
        """
        # Start timing
        start_time = time.time()
        
        # Retrieve relevant documents
        documents = self.search(query, top_k, hybrid_weight, **kwargs)
        
        # Format the context
        context = self.format_context(documents)
        
        # Generate the response
        response = self.generate(
            query=query,
            context=context,
            custom_system_prompt=custom_system_prompt,
            custom_user_prompt=custom_user_prompt,
            callback=callback
        )
        
        # Calculate latency
        latency = time.time() - start_time
        
        # Save evaluation data if requested
        evaluation_id = None
        if save_evaluation:
            evaluation_id = self.save_evaluation(query, response, documents, latency)
        
        # Create result
        result = {
            "query": query,
            "answer": response,
            "documents": documents,
            "latency": latency,
            "model": self.model,
            "provider": self.provider,
            "evaluation_id": evaluation_id
        }
        
        return result
    
    def get_system_prompt(self) -> str:
        """
        Get the system prompt for Vietnamese RAG.
        
        Returns:
            System prompt string
        """
        return """Bạn là một trợ lý AI hữu ích, chính xác và trung thực. Khi được hỏi một câu hỏi, hãy sử dụng thông tin từ các tài liệu được cung cấp để trả lời. Nếu thông tin không có trong tài liệu, hãy nói rằng bạn không biết. Nếu tài liệu chỉ cung cấp thông tin một phần, hãy trả lời dựa trên những gì có sẵn và chỉ ra rằng thông tin có thể không đầy đủ. Luôn trích dẫn nguồn tài liệu trong câu trả lời của bạn."""
    
    def get_user_prompt_template(self) -> str:
        """
        Get the user prompt template for Vietnamese RAG.
        
        Returns:
            User prompt template string
        """
        return """Tài liệu:
{context}

Câu hỏi: {query}

Trả lời chi tiết dựa trên tài liệu được cung cấp. Nếu tài liệu không chứa thông tin liên quan, hãy nói rằng bạn không tìm thấy thông tin."""
