"""
Memory Management Module for ReActReasoner

<PERSON><PERSON><PERSON> này cung cấp khả năng quản lý bộ nhớ cho ReActReasoner, gi<PERSON><PERSON> xử lý các
cuộc trò chuyện dài và phức tạp thông qua việc lưu trữ và truy xuất thông tin.
"""

import time
import json
import logging
from functools import lru_cache
from typing import Dict, Any, List, Optional, Tuple, Union, Callable

# Setup logging
logger = logging.getLogger("memory_management")

class MemoryItem:
    """Một mục trong bộ nhớ của ReActReasoner."""
    
    def __init__(self, use_cache: bool = True, cache_size: int = 100, content: Any,
        memory_type: str,
        created_at: float = None,
        metadata: Dict[str, Any] = None,
        importance: float = 0.5,
        ttl: Optional[float] = None  # Time-to-live in seconds
    ):
        """
        Khởi tạo một mục bộ nhớ.
        
        Args:
            content: Nội dung củ<PERSON> mục bộ nhớ
            memory_type: <PERSON><PERSON><PERSON> bộ nhớ (thought, action, observation, result, etc.)
            created_at: Thờ<PERSON> điể<PERSON> tạo (timestamp)
            metadata: Metadata bổ sung
            importance: Mức độ quan trọng (0.0 đến 1.0)
            ttl: Thời gian sống (giây), None = không hết hạn
        """
        self.content = content
        self.memory_type = memory_type
        self.created_at = created_at or time.time()
        self.metadata = metadata or {}
        self.importance = max(0.0, min(1.0, importance))  # Clamp to [0, 1]
        self.ttl = ttl
        self.last_accessed = self.created_at
        self.access_count = 0
        self.id = f"{memory_type}_{int(self.created_at * 1000)}"
    
    def is_expired(self) -> bool:
        """Kiểm tra xem mục bộ nhớ có hết hạn không."""
        if self.ttl is None:
            return False
        return (time.time() - self.created_at) > self.ttl
    
    def access(self) -> None:
        """Đánh dấu mục bộ nhớ đã được truy cập."""
        self.last_accessed = time.time()
        self.access_count += 1
    
    def to_dict(self) -> Dict[str, Any]:
        """Chuyển đổi thành dictionary."""
        return {
            "id": self.id,
            "content": self.content,
            "memory_type": self.memory_type,
            "created_at": self.created_at,
            "last_accessed": self.last_accessed,
            "access_count": self.access_count,
            "importance": self.importance,
            "ttl": self.ttl,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MemoryItem':
        """Tạo mục bộ nhớ từ dictionary."""
        item = cls(
            content=data["content"],
            memory_type=data["memory_type"],
            created_at=data["created_at"],
            metadata=data["metadata"],
            importance=data["importance"],
            ttl=data["ttl"]
        )
        item.last_accessed = data["last_accessed"]
        item.access_count = data["access_count"]
        item.id = data["id"]
        return item
    
    def __str__(self) -> str:
        """String representation."""
        return f"MemoryItem({self.memory_type}, importance={self.importance:.2f})"


class MemoryManager:
    """
    Quản lý bộ nhớ cho ReActReasoner.
    
    Class này cung cấp khả năng lưu trữ, truy xuất, và quản lý thông tin trong
    các cuộc trò chuyện dài, với các chiến lược quản lý bộ nhớ khác nhau.
    """
    
    def __init__(self, use_cache: bool = True, cache_size: int = 100, max_items: int = 1000,
        memory_strategy: str = "importance_recency",
        persistence_path: Optional[str] = None,
        ttl_defaults: Optional[Dict[str, Optional[float]]] = None,
        importance_defaults: Optional[Dict[str, float]] = None,
        importance_calculators: Optional[Dict[str, Callable[[Any, Dict[str, Any]], float]]] = None
    ):
        """
        Khởi tạo MemoryManager.
        
        Args:
            max_items: Số lượng mục tối đa trong bộ nhớ
            memory_strategy: Chiến lược quản lý bộ nhớ 
                (importance_recency, importance, recency, fifo)
            persistence_path: Đường dẫn để lưu/tải bộ nhớ
            ttl_defaults: Giá trị TTL mặc định cho từng loại bộ nhớ
            importance_defaults: Giá trị importance mặc định cho từng loại
            importance_calculators: Các hàm tính importance cho từng loại
        """
        self.max_items = max_items
        self.memory_strategy = memory_strategy
        self.persistence_path = persistence_path
        
        # Default TTL values
        self.ttl_defaults = {
            "thought": None,  # No expiration
            "action": None,
            "observation": None,
            "result": None,
            "context": None,
            "temporary": 600  # 10 minutes
        }
        if ttl_defaults:
            self.ttl_defaults.update(ttl_defaults)
        
        # Default importance values
        self.importance_defaults = {
            "thought": 0.5,
            "action": 0.7,
            "observation": 0.6,
            "result": 0.8,
            "context": 0.9,
            "temporary": 0.3
        }
        if importance_defaults:
            self.importance_defaults.update(importance_defaults)
        
        # Functions to calculate importance
        self.importance_calculators = importance_calculators or {}
        
        # Memory storage
        self.memories: Dict[str, MemoryItem] = {}
        
        # Memory statistics
        self.stats = {
            "total_adds": 0,
            "total_retrievals": 0,
            "total_pruned": 0,
            "memory_hits": 0,
            "memory_misses": 0
        }
        
        # Load from persistence if available
        if self.persistence_path:
            self.load()
    
    def add(
        self,
        content: Any,
        memory_type: str,
        metadata: Optional[Dict[str, Any]] = None,
        importance: Optional[float] = None,
        ttl: Optional[float] = None
    ) -> str:
        """
        Thêm mục vào bộ nhớ.
        
        Args:
            content: Nội dung cần lưu
            memory_type: Loại bộ nhớ
            metadata: Metadata bổ sung
            importance: Mức độ quan trọng (nếu None, sẽ sử dụng giá trị mặc định 
                hoặc tính toán dựa trên calculator)
            ttl: Thời gian sống (nếu None, sẽ sử dụng giá trị mặc định)
            
        Returns:
            ID của mục bộ nhớ
        """
        # Clean expired memories
        self._clean_expired()
        
        # Calculate importance if not provided
        if importance is None:
            # Try to use calculator
            if memory_type in self.importance_calculators:
                try:
                    importance = self.importance_calculators[memory_type](content, metadata or {})
                except Exception as e:
                    logger.warning(f"Error calculating importance: {e}")
                    importance = self.importance_defaults.get(memory_type, 0.5)
            else:
                importance = self.importance_defaults.get(memory_type, 0.5)
        
        # Get TTL if not provided
        if ttl is None:
            ttl = self.ttl_defaults.get(memory_type)
        
        # Create memory item
        memory_item = MemoryItem(
            content=content,
            memory_type=memory_type,
            metadata=metadata,
            importance=importance,
            ttl=ttl
        )
        
        # Add to memories
        self.memories[memory_item.id] = memory_item
        self.stats["total_adds"] += 1
        
        # Prune if needed
        if len(self.memories) > self.max_items:
            self._prune()
        
        # Save if persistence is enabled
        if self.persistence_path:
            self.save()
        
        return memory_item.id
    
    def get(self, memory_id: str) -> Optional[MemoryItem]:
        """
        Lấy mục bộ nhớ theo ID.
        
        Args:
            memory_id: ID của mục bộ nhớ
            
        Returns:
            MemoryItem hoặc None nếu không tìm thấy
        """
        memory_item = self.memories.get(memory_id)
        
        if memory_item:
            if memory_item.is_expired():
                # Remove expired item
                del self.memories[memory_id]
                self.stats["memory_misses"] += 1
                return None
            
            # Update access stats
            memory_item.access()
            self.stats["memory_hits"] += 1
            self.stats["total_retrievals"] += 1
            return memory_item
        
        self.stats["memory_misses"] += 1
        return None
    
    def get_by_type(
        self,
        memory_type: str,
        limit: Optional[int] = None,
        sort_by: str = "recency",
        filter_fn: Optional[Callable[[MemoryItem], bool]] = None
    ) -> List[MemoryItem]:
        """
        Lấy danh sách các mục bộ nhớ theo loại.
        
        Args:
            memory_type: Loại bộ nhớ cần lấy
            limit: Số lượng tối đa
            sort_by: Cách sắp xếp (recency, importance, access_count)
            filter_fn: Hàm lọc bổ sung
            
        Returns:
            Danh sách các mục bộ nhớ
        """
        # Clean expired memories
        self._clean_expired()
        
        # Filter by type
        items = [item for item in self.memories.values() if item.memory_type == memory_type]
        
        # Apply additional filter if provided
        if filter_fn:
            items = [item for item in items if filter_fn(item)]
        
        # Sort items
        if sort_by == "recency":
            items.sort(key=lambda x: x.last_accessed, reverse=True)
        elif sort_by == "importance":
            items.sort(key=lambda x: x.importance, reverse=True)
        elif sort_by == "access_count":
            items.sort(key=lambda x: x.access_count, reverse=True)
        elif sort_by == "created_at":
            items.sort(key=lambda x: x.created_at, reverse=True)
        
        # Apply limit
        if limit is not None:
            items = items[:limit]
        
        # Update stats
        for item in items:
            item.access()
        
        self.stats["total_retrievals"] += len(items)
        return items
    
    def search(
        self,
        query: str,
        memory_types: Optional[List[str]] = None,
        limit: int = 10,
        min_score: float = 0.0,
        use_embeddings: bool = False
    ) -> List[Tuple[MemoryItem, float]]:
        """
        Tìm kiếm trong bộ nhớ.
        
        Args:
            query: Truy vấn tìm kiếm
            memory_types: Các loại bộ nhớ cần tìm (None = tất cả)
            limit: Số lượng kết quả tối đa
            min_score: Điểm số tối thiểu (0-1)
            use_embeddings: Có sử dụng embeddings hay không
            
        Returns:
            Danh sách các cặp (mục bộ nhớ, điểm số)
        """
        # Clean expired memories
        self._clean_expired()
        
        # Filter by type if needed
        if memory_types:
            items = [item for item in self.memories.values() if item.memory_type in memory_types]
        else:
            items = list(self.memories.values())
        
        # We can implement different search algorithms based on use_embeddings
        if use_embeddings:
            # In a real implementation, we would use embeddings for semantic search
            # For now, just do simple substring matching
            results = []
            for item in items:
                if isinstance(item.content, str):
                    # Simple score based on substring match
                    if query.lower() in item.content.lower():
                        score = 0.8  # Arbitrary high score for demo
                    else:
                        continue
                else:
                    # For non-string content, check string representation
                    if query.lower() in str(item.content).lower():
                        score = 0.6  # Lower score for non-exact matches
                    else:
                        continue
                
                if score >= min_score:
                    results.append((item, score))
        else:
            # Simple substring search
            results = []
            for item in items:
                content_str = str(item.content) if not isinstance(item.content, str) else item.content
                if query.lower() in content_str.lower():
                    # Calculate a simple score based on substring position
                    pos = content_str.lower().find(query.lower())
                    score = 1.0 - (pos / (len(content_str) + 1))
                    
                    if score >= min_score:
                        results.append((item, score))
        
        # Sort by score
        results.sort(key=lambda x: x[1], reverse=True)
        
        # Apply limit
        results = results[:limit]
        
        # Update stats
        for item, _ in results:
            item.access()
        
        self.stats["total_retrievals"] += len(results)
        return results
    
    def remove(self, memory_id: str) -> bool:
        """
        Xóa mục bộ nhớ theo ID.
        
        Args:
            memory_id: ID của mục bộ nhớ
            
        Returns:
            True nếu xóa thành công, False nếu không tìm thấy
        """
        if memory_id in self.memories:
            del self.memories[memory_id]
            
            # Save if persistence is enabled
            if self.persistence_path:
                self.save()
                
            return True
        return False
    
    def clear(self, memory_type: Optional[str] = None) -> int:
        """
        Xóa tất cả mục bộ nhớ hoặc chỉ các mục của một loại.
        
        Args:
            memory_type: Loại bộ nhớ cần xóa (None = tất cả)
            
        Returns:
            Số lượng mục đã xóa
        """
        if memory_type is None:
            count = len(self.memories)
            self.memories.clear()
        else:
            ids_to_remove = [
                memory_id for memory_id, item in self.memories.items()
                if item.memory_type == memory_type
            ]
            count = len(ids_to_remove)
            for memory_id in ids_to_remove:
                del self.memories[memory_id]
        
        # Save if persistence is enabled
        if self.persistence_path:
            self.save()
            
        return count
    
    def summarize(self, memory_type: Optional[str] = None) -> Dict[str, Any]:
        """
        Tạo tóm tắt về bộ nhớ.
        
        Args:
            memory_type: Loại bộ nhớ cần tóm tắt (None = tất cả)
            
        Returns:
            Dict chứa thông tin tóm tắt
        """
        # Clean expired memories
        self._clean_expired()
        
        if memory_type:
            items = [item for item in self.memories.values() if item.memory_type == memory_type]
        else:
            items = list(self.memories.values())
        
        # Group by type
        type_counts = {}
        for item in items:
            if item.memory_type not in type_counts:
                type_counts[item.memory_type] = 0
            type_counts[item.memory_type] += 1
        
        # Calculate stats
        avg_importance = sum(item.importance for item in items) / len(items) if items else 0
        
        return {
            "total_items": len(items),
            "type_counts": type_counts,
            "avg_importance": avg_importance,
            "oldest_item": min(item.created_at for item in items) if items else None,
            "newest_item": max(item.created_at for item in items) if items else None,
            "stats": self.stats
        }
    
    def save(self, path: Optional[str] = None) -> bool:
        """
        Lưu bộ nhớ vào file.
        
        Args:
            path: Đường dẫn file (None = sử dụng persistence_path)
            
        Returns:
            True nếu lưu thành công, False nếu có lỗi
        """
        save_path = path or self.persistence_path
        if not save_path:
            logger.warning("No persistence path specified")
            return False
        
        try:
            # Convert memories to dict
            data = {
                "memories": {
                    memory_id: item.to_dict() for memory_id, item in self.memories.items()
                },
                "stats": self.stats,
                "max_items": self.max_items,
                "memory_strategy": self.memory_strategy,
                "saved_at": time.time()
            }
            
            # Write to file
            with open(save_path, 'w') as f:
                json.dump(data, f, indent=2)
                
            logger.info(f"Saved {len(self.memories)} memories to {save_path}")
            return True
        except Exception as e:
            logger.error(f"Error saving memories: {e}")
            return False
    
    def load(self, path: Optional[str] = None) -> bool:
        """
        Tải bộ nhớ từ file.
        
        Args:
            path: Đường dẫn file (None = sử dụng persistence_path)
            
        Returns:
            True nếu tải thành công, False nếu có lỗi
        """
        load_path = path or self.persistence_path
        if not load_path:
            logger.warning("No persistence path specified")
            return False
        
        try:
            # Read from file
            with open(load_path, 'r') as f:
                data = json.load(f)
            
            # Load memories
            self.memories = {
                memory_id: MemoryItem.from_dict(item_data)
                for memory_id, item_data in data["memories"].items()
            }
            
            # Load stats
            self.stats = data["stats"]
            
            # Optionally load settings
            if "max_items" in data:
                self.max_items = data["max_items"]
            if "memory_strategy" in data:
                self.memory_strategy = data["memory_strategy"]
                
            logger.info(f"Loaded {len(self.memories)} memories from {load_path}")
            
            # Clean expired memories
            self._clean_expired()
            
            return True
        except FileNotFoundError:
            logger.warning(f"Memory file not found: {load_path}")
            return False
        except Exception as e:
            logger.error(f"Error loading memories: {e}")
            return False
    
    def _clean_expired(self) -> int:
        """
        Xóa các mục bộ nhớ đã hết hạn.
        
        Returns:
            Số lượng mục đã xóa
        """
        expired_ids = [
            memory_id for memory_id, item in self.memories.items()
            if item.is_expired()
        ]
        
        for memory_id in expired_ids:
            del self.memories[memory_id]
        
        if expired_ids:
            self.stats["total_pruned"] += len(expired_ids)
            
            # Save if persistence is enabled
            if self.persistence_path:
                self.save()
        
        return len(expired_ids)
    
    def _prune(self) -> int:
        """
        Loại bỏ bớt mục bộ nhớ khi vượt quá giới hạn.
        
        Returns:
            Số lượng mục đã xóa
        """
        items_to_remove = len(self.memories) - self.max_items
        if items_to_remove <= 0:
            return 0
        
        # Sort items based on strategy
        if self.memory_strategy == "importance_recency":
            # Weighted combination of importance and recency
            items = [
                (memory_id, item, 
                 item.importance * 0.7 + (time.time() - item.last_accessed) * -0.3)
                for memory_id, item in self.memories.items()
            ]
            # Sort by score (higher is better)
            items.sort(key=lambda x: x[2], reverse=True)
        elif self.memory_strategy == "importance":
            # Sort by importance only
            items = [
                (memory_id, item, item.importance)
                for memory_id, item in self.memories.items()
            ]
            # Sort by importance (higher is better)
            items.sort(key=lambda x: x[2], reverse=True)
        elif self.memory_strategy == "recency":
            # Sort by recency only
            items = [
                (memory_id, item, item.last_accessed)
                for memory_id, item in self.memories.items()
            ]
            # Sort by last accessed (more recent is better)
            items.sort(key=lambda x: x[2], reverse=True)
        else:  # Default to FIFO
            # Sort by creation time
            items = [
                (memory_id, item, item.created_at)
                for memory_id, item in self.memories.items()
            ]
            # Sort by creation time (newer is better)
            items.sort(key=lambda x: x[2], reverse=True)
        
        # Keep only the best items
        keep_items = items[:self.max_items]
        remove_items = items[self.max_items:]
        
        # Update memories
        self.memories = {
            memory_id: item for memory_id, item, _ in keep_items
        }
        
        pruned_count = len(remove_items)
        self.stats["total_pruned"] += pruned_count
        
        # Save if persistence is enabled
        if self.persistence_path:
            self.save()
        
        return pruned_count


class MemoryEnabledReActReasoner:
    """
    Đối tượng bọc ReActReasoner để hỗ trợ quản lý bộ nhớ.
    
    Class này bao bọc ReActReasoner tiêu chuẩn và thêm khả năng quản lý bộ nhớ.
    """
    
    def __init__(self, use_cache: bool = True, cache_size: int = 100, reasoner,
        memory_config: Optional[Dict[str, Any]] = None,
        auto_store_types: Optional[List[str]] = None
    ):
        """
        Khởi tạo MemoryEnabledReActReasoner.
        
        Args:
            reasoner: Đối tượng ReActReasoner
            memory_config: Cấu hình cho MemoryManager
            auto_store_types: Các loại thông tin tự động lưu trữ
        """
        self.reasoner = reasoner
        
        # Default memory configuration
        default_config = {
            "max_items": 1000,
            "memory_strategy": "importance_recency",
            "persistence_path": None
        }
        
        # Merge with provided config
        config = default_config.copy()
        if memory_config:
            config.update(memory_config)
        
        # Create memory manager
        self.memory_manager = MemoryManager(**config)
        
        # Auto store settings
        self.auto_store_types = auto_store_types or ["thought", "action", "observation", "result"]
        
        # Patch reasoner methods
        self._patch_reasoner_methods()
        
        # Conversation history
        self.conversation_id = f"conversation_{int(time.time())}"
        self.conversation_summary = ""
        self.conversation_turn = 0
    
    def _patch_reasoner_methods(self) -> None:
        """Patch reasoner methods to enable memory management."""
        # Store original methods
        self._original_methods = {
            "_step": self.reasoner._step,
            "_parse_action": self.reasoner._parse_action,
            "_execute_action": self.reasoner._execute_action,
            "run": self.reasoner.run
        }
        
        # Patch methods
        self.reasoner._step = self._patched_step
        self.reasoner._parse_action = self._patched_parse_action
        self.reasoner._execute_action = self._patched_execute_action
        self.reasoner.run = self._patched_run
    
    def _patched_step(self, *args, **kwargs):
        """Patched version of _step that stores thoughts."""
        result = self._original_methods["_step"](*args, **kwargs)
        
        # Store thought in memory if enabled
        if "thought" in self.auto_store_types and "thought" in result:
            self.memory_manager.add(
                content=result["thought"],
                memory_type="thought",
                metadata={
                    "conversation_id": self.conversation_id,
                    "turn": self.conversation_turn
                }
            )
        
        return result
    
    def _patched_parse_action(self, *args, **kwargs):
        """Patched version of _parse_action that stores actions."""
        action = self._original_methods["_parse_action"](*args, **kwargs)
        
        # Store action in memory if enabled
        if "action" in self.auto_store_types and action:
            self.memory_manager.add(
                content=action,
                memory_type="action",
                metadata={
                    "conversation_id": self.conversation_id,
                    "turn": self.conversation_turn
                }
            )
        
        return action
    
    def _patched_execute_action(self, *args, **kwargs):
        """Patched version of _execute_action that stores observations."""
        observation = self._original_methods["_execute_action"](*args, **kwargs)
        
        # Store observation in memory if enabled
        if "observation" in self.auto_store_types and observation:
            self.memory_manager.add(
                content=observation,
                memory_type="observation",
                metadata={
                    "conversation_id": self.conversation_id,
                    "turn": self.conversation_turn
                }
            )
        
        return observation
    
    def _patched_run(self, *args, **kwargs):
        """Patched version of run that stores the result."""
        # Increment conversation turn
        self.conversation_turn += 1
        
        # Store context if provided
        input_query = args[0] if args else kwargs.get("input", "")
        if input_query and "context" in self.auto_store_types:
            self.memory_manager.add(
                content=input_query,
                memory_type="context",
                metadata={
                    "conversation_id": self.conversation_id,
                    "turn": self.conversation_turn,
                    "type": "input"
                },
                importance=0.8  # Context is usually important
            )
        
        # Enhance input with relevant memories if appropriate
        if kwargs.get("use_memory", True) and input_query:
            enhanced_input = self._enhance_input_with_memories(input_query)
            if enhanced_input != input_query:
                if args:
                    args = (enhanced_input,) + args[1:]
                else:
                    kwargs["input"] = enhanced_input
        
        # Run the reasoner
        result = self._original_methods["run"](*args, **kwargs)
        
        # Store result in memory if enabled
        if "result" in self.auto_store_types and result:
            result_content = result
            if isinstance(result, dict) and "output" in result:
                result_content = result["output"]
            
            self.memory_manager.add(
                content=result_content,
                memory_type="result",
                metadata={
                    "conversation_id": self.conversation_id,
                    "turn": self.conversation_turn,
                    "type": "output"
                }
            )
        
        # Update conversation summary
        self._update_conversation_summary()
        
        return result
    
    def _enhance_input_with_memories(self, input_query: str) -> str:
        """
        Tăng cường input với các bộ nhớ liên quan.
        
        Args:
            input_query: Câu truy vấn đầu vào
            
        Returns:
            Câu truy vấn đã được tăng cường
        """
        # Tìm kiếm các bộ nhớ liên quan
        memories = self.memory_manager.search(
            query=input_query,
            memory_types=["context", "result", "observation"],
            limit=3,
            min_score=0.5
        )
        
        if not memories:
            return input_query
        
        # Add relevant memories to input
        memory_context = "\n\nRelevant information from memory:\n"
        for memory_item, score in memories:
            memory_type = memory_item.memory_type.capitalize()
            memory_content = str(memory_item.content)
            memory_context += f"- {memory_type} ({score:.2f}): {memory_content}\n"
        
        return f"{input_query}\n{memory_context}"
    
    def _update_conversation_summary(self) -> None:
        """Cập nhật tóm tắt cuộc trò chuyện."""
        # In a real implementation, this might call an LLM to generate a summary
        # For now, just count the turns
        self.conversation_summary = f"Conversation with {self.conversation_turn} turns"
    
    def run(self, 
            input_query: str,
            use_memory: bool = True,
            store_types: Optional[List[str]] = None,
            **kwargs) -> Any:
        """
        Chạy reasoner với quản lý bộ nhớ.
        
        Args:
            input_query: Câu truy vấn đầu vào
            use_memory: Có sử dụng bộ nhớ để tăng cường input hay không
            store_types: Ghi đè các loại thông tin tự động lưu trữ
            **kwargs: Các tham số khác cho reasoner.run
            
        Returns:
            Kết quả từ reasoner
        """
        # Override auto store types if provided
        original_store_types = self.auto_store_types
        if store_types is not None:
            self.auto_store_types = store_types
        
        try:
            # Add use_memory to kwargs
            kwargs["use_memory"] = use_memory
            
            # Run the reasoner
            return self.reasoner.run(input_query, **kwargs)
        finally:
            # Restore original store types
            if store_types is not None:
                self.auto_store_types = original_store_types
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Lấy thống kê về bộ nhớ."""
        return self.memory_manager.summarize()
    
    def save_memories(self, path: Optional[str] = None) -> bool:
        """Lưu bộ nhớ vào file."""
        return self.memory_manager.save(path)
    
    def load_memories(self, path: Optional[str] = None) -> bool:
        """Tải bộ nhớ từ file."""
        return self.memory_manager.load(path)
    
    def clear_memories(self, memory_type: Optional[str] = None) -> int:
        """Xóa bộ nhớ."""
        return self.memory_manager.clear(memory_type)
    
    def start_new_conversation(self) -> None:
        """Bắt đầu cuộc trò chuyện mới."""
        self.conversation_id = f"conversation_{int(time.time())}"
        self.conversation_turn = 0
        self.conversation_summary = ""
    
    def get_conversation_memories(self, limit: int = 10) -> Dict[str, List[MemoryItem]]:
        """
        Lấy bộ nhớ của cuộc trò chuyện hiện tại.
        
        Args:
            limit: Số lượng mục tối đa cho mỗi loại
            
        Returns:
            Dict chứa các mục bộ nhớ, phân loại theo loại
        """
        result = {}
        
        for memory_type in ["context", "thought", "action", "observation", "result"]:
            items = self.memory_manager.get_by_type(
                memory_type=memory_type,
                limit=limit,
                sort_by="created_at",
                filter_fn=lambda x: x.metadata.get("conversation_id") == self.conversation_id
            )
            result[memory_type] = items
        
        return result 