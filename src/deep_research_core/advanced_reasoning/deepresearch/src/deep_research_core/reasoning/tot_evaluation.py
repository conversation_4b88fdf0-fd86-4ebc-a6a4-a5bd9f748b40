"""
<PERSON><PERSON><PERSON> phương thức đánh giá nâng cao cho Tree of Thought (TOT).

<PERSON><PERSON><PERSON> này cung cấp các phương thức đánh giá nâng cao để cải thiện hiệu suất
và độ chính xác của Tree of Thought (TOT).
"""

import re
import json
import time
import logging
import difflib
from typing import List, Tuple, Dict, Any, Optional, Union, Set, Callable
from functools import lru_cache

from ..utils.structured_logging import get_logger
from ..utils.performance_metrics import measure_latency
from ..utils.distributed_tracing import trace_function

# Create a logger
logger = get_logger(__name__)

class AdvancedEvaluator:
    """
    Lớp đánh giá nâng cao cho Tree of Thought (TOT).

    Cung cấp các phương thức đánh giá nâng cao để cải thiện hiệu suất
    và độ chính xác của Tree of Thought (TOT).

    Tính năng:
    - <PERSON><PERSON><PERSON> giá đa tiêu chí dựa trên loại truy vấn
    - <PERSON><PERSON> tích cấu trúc và không cấu trúc của đánh giá
    - Hỗ trợ đa ngôn ngữ (tiếng Anh và tiếng Việt)
    - Đánh giá dựa trên lĩnh vực cụ thể
    - Phân tích lỗi và tự động khắc phục
    - Tối ưu hóa bộ nhớ cache
    """

    @staticmethod
    def create_evaluation_prompt(query: str, paths: List[str], criteria: Optional[List[str]] = None) -> str:
        """
        Tạo prompt đánh giá với tiêu chí cụ thể.

        Args:
            query: Truy vấn gốc
            paths: Danh sách các đường dẫn suy luận cần đánh giá
            criteria: Danh sách các tiêu chí đánh giá (tùy chọn)

        Returns:
            Prompt đánh giá
        """
        # Tiêu chí đánh giá mặc định
        default_criteria = [
            "Tính logic và nhất quán",
            "Độ sâu và chi tiết của phân tích",
            "Tính sáng tạo và đa dạng của cách tiếp cận",
            "Tính khả thi và thực tế của kết luận",
            "Mức độ liên quan đến truy vấn gốc"
        ]

        # Sử dụng tiêu chí được cung cấp hoặc tiêu chí mặc định
        eval_criteria = criteria or default_criteria

        # Tạo phần tiêu chí
        criteria_text = "\n".join([f"{i+1}. {criterion}" for i, criterion in enumerate(eval_criteria)])

        # Tạo phần đường dẫn
        paths_text = "\n\n".join([f"Path {i+1}:\n{path}" for i, path in enumerate(paths)])

        # Tạo prompt đánh giá
        prompt = f"""Đánh giá các đường dẫn suy luận sau cho truy vấn: "{query}"

Tiêu chí đánh giá (thang điểm 1-10 cho mỗi tiêu chí):
{criteria_text}

Đường dẫn suy luận:
{paths_text}

Đánh giá chi tiết:
Đối với mỗi đường dẫn, hãy đánh giá theo từng tiêu chí và đưa ra điểm tổng hợp (thang điểm 1-10).
Sau đó, xếp hạng các đường dẫn từ tốt nhất đến kém nhất.

Định dạng phản hồi:
```json
{{
  "evaluations": [
    {{
      "path_id": 1,
      "scores": {{
        "Tiêu chí 1": điểm,
        "Tiêu chí 2": điểm,
        ...
      }},
      "total_score": điểm_tổng,
      "explanation": "Giải thích ngắn gọn"
    }},
    ...
  ],
  "ranking": [id_đường_dẫn_tốt_nhất, id_đường_dẫn_thứ_hai, ...]
}}
```"""

        return prompt

    @staticmethod
    @lru_cache(maxsize=200)
    def parse_structured_evaluation(evaluation: str) -> List[Tuple[float, int]]:
        """
        Phân tích đánh giá có cấu trúc (JSON).

        Args:
            evaluation: Đánh giá từ mô hình

        Returns:
            Danh sách các tuple (điểm, id_đường_dẫn)
        """
        try:
            # Tìm và trích xuất phần JSON
            json_match = re.search(r'```json\s*(.*?)\s*```', evaluation, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
                # Xử lý các trường hợp JSON không hợp lệ
                json_str = json_str.replace("''", '""').replace("'", '"')

                try:
                    data = json.loads(json_str)
                except json.JSONDecodeError:
                    # Thử sửa lỗi JSON phổ biến
                    json_str = re.sub(r'([{,])\s*(\w+)\s*:', r'\1"\2":', json_str)
                    json_str = re.sub(r',\s*}', '}', json_str)
                    try:
                        data = json.loads(json_str)
                    except json.JSONDecodeError as e:
                        logger.warning(f"Không thể phân tích JSON sau khi sửa lỗi: {str(e)}")
                        return []

                # Trích xuất điểm và id đường dẫn
                scores = []
                for eval_item in data.get("evaluations", []):
                    path_id = eval_item.get("path_id", 0) - 1  # Chuyển từ 1-based sang 0-based
                    total_score = eval_item.get("total_score", 5.0)
                    # Đảm bảo điểm số nằm trong khoảng hợp lệ
                    total_score = max(1.0, min(10.0, float(total_score)))
                    scores.append((total_score, path_id))

                # Sắp xếp theo điểm giảm dần
                return sorted(scores, key=lambda x: x[0], reverse=True)

            # Nếu không tìm thấy JSON, thử sử dụng ranking
            ranking_match = re.search(r'"ranking":\s*\[(.*?)\]', evaluation, re.DOTALL)
            if ranking_match:
                ranking_str = ranking_match.group(1)
                ranking = [int(x.strip()) - 1 for x in ranking_str.split(",") if x.strip().isdigit()]

                # Gán điểm dựa trên thứ hạng (điểm cao nhất cho thứ hạng đầu tiên)
                scores = [(10.0 - i * (5.0 / len(ranking)) if i < len(ranking) else 5.0, rank) for i, rank in enumerate(ranking)]
                return scores
        except Exception as e:
            logger.error(f"Lỗi khi phân tích đánh giá có cấu trúc: {str(e)}")

        # Trả về danh sách trống nếu không thể phân tích
        return []

    @staticmethod
    @lru_cache(maxsize=200)
    def parse_unstructured_evaluation(evaluation: str, num_paths: int) -> List[Tuple[float, int]]:
        """
        Phân tích đánh giá không có cấu trúc.

        Args:
            evaluation: Đánh giá từ mô hình
            num_paths: Số lượng đường dẫn

        Returns:
            Danh sách các tuple (điểm, id_đường_dẫn)
        """
        try:
            scores = []

            # Tìm điểm số cho từng đường dẫn
            for i in range(num_paths):
                path_num = i + 1
                score = None

                # Tìm kiếm các mẫu điểm số phổ biến
                patterns = [
                    f"Path {path_num}:? ([0-9]+\.?[0-9]*)[/]?10",
                    f"Path {path_num}.*?score:? ([0-9]+\.?[0-9]*)[/]?10",
                    f"Path {path_num}.*?rating:? ([0-9]+\.?[0-9]*)[/]?10",
                    f"Path {path_num}.*?([0-9]+\.?[0-9]*)[/]?10",
                    f"Đường dẫn {path_num}:? ([0-9]+\.?[0-9]*)[/]?10",
                    f"Đường dẫn {path_num}.*?điểm:? ([0-9]+\.?[0-9]*)[/]?10",
                    f"Đường dẫn {path_num}.*?đánh giá:? ([0-9]+\.?[0-9]*)[/]?10",
                    f"Path {path_num}.*?score:? ([0-9]+\.?[0-9]*)",
                    f"Path {path_num}.*?rating:? ([0-9]+\.?[0-9]*)",
                    f"Đường dẫn {path_num}.*?điểm:? ([0-9]+\.?[0-9]*)",
                    f"Đường dẫn {path_num}.*?đánh giá:? ([0-9]+\.?[0-9]*)",
                    f"Đánh giá {path_num}:? ([0-9]+\.?[0-9]*)[/]?10",
                    f"Evaluation {path_num}:? ([0-9]+\.?[0-9]*)[/]?10"
                ]

                for pattern in patterns:
                    match = re.search(pattern, evaluation, re.IGNORECASE)
                    if match:
                        try:
                            score = float(match.group(1))
                            # Đảm bảo điểm số nằm trong khoảng hợp lệ
                            score = max(1.0, min(10.0, score))
                            break
                        except (ValueError, IndexError):
                            continue

                # Nếu không tìm thấy điểm số, thử phân tích ngữ nghĩa
                if score is None:
                    # Tìm đoạn văn bản đánh giá cho đường dẫn này
                    path_section = ""
                    path_sections = []

                    # Tìm tất cả các dòng liên quan đến đường dẫn này
                    for line in evaluation.split("\n"):
                        if f"Path {path_num}" in line or f"Đường dẫn {path_num}" in line:
                            path_section = line
                            path_sections.append(line)

                    # Nếu không tìm thấy, thử tìm các phần đánh giá khác
                    if not path_section:
                        for line in evaluation.split("\n"):
                            if f"#{path_num}" in line or f"({path_num})" in line or f"[{path_num}]" in line:
                                path_section = line
                                path_sections.append(line)

                    # Phân tích ngữ nghĩa
                    positive_terms = [
                        # Tiếng Anh
                        "best", "strongest", "most promising", "highest", "excellent", "good", "better", "great", "superior",
                        "outstanding", "exceptional", "remarkable", "impressive", "solid", "thorough", "comprehensive", "coherent",
                        "logical", "well-reasoned", "insightful", "thoughtful", "detailed", "accurate", "precise", "clear",
                        # Tiếng Việt
                        "tốt", "tốt nhất", "mạnh", "mạnh nhất", "triển vọng", "triển vọng nhất", "xuất sắc", "hợp lý",
                        "vượt trội", "nổi bật", "đặc biệt", "ấn tượng", "chắc chắn", "toàn diện", "mạch lạc", "logic",
                        "sâu sắc", "chi tiết", "chính xác", "rõ ràng", "thuyết phục", "đáng tin cậy", "hiệu quả"
                    ]
                    negative_terms = [
                        # Tiếng Anh
                        "worst", "weakest", "least promising", "lowest", "poor", "bad", "worse", "inadequate", "insufficient",
                        "flawed", "problematic", "inconsistent", "incoherent", "illogical", "confusing", "vague", "superficial",
                        "shallow", "incomplete", "inaccurate", "imprecise", "unclear", "unconvincing", "unreliable",
                        # Tiếng Việt
                        "tệ", "tệ nhất", "yếu", "yếu nhất", "ít triển vọng", "ít triển vọng nhất", "kém", "không hợp lý",
                        "không đầy đủ", "có vấn đề", "không nhất quán", "không mạch lạc", "không logic", "gây nhầm lẫn",
                        "mơ hồ", "hời hợt", "nông cạn", "không đầy đủ", "không chính xác", "không rõ ràng", "không thuyết phục"
                    ]

                    # Kiểm tra tất cả các phần đã tìm thấy
                    all_text = " ".join(path_sections).lower()

                    # Đếm số từ tích cực và tiêu cực
                    positive_count = sum(1 for term in positive_terms if term in all_text)
                    negative_count = sum(1 for term in negative_terms if term in all_text)

                    # Tính điểm dựa trên số lượng từ tích cực và tiêu cực
                    if positive_count > negative_count:
                        score = 7.0 + min(3.0, positive_count * 0.5)  # Tối đa 10.0
                    elif negative_count > positive_count:
                        score = 4.0 - min(3.0, negative_count * 0.5)  # Tối thiểu 1.0
                    else:
                        score = 5.0

                    # Đảm bảo điểm số nằm trong khoảng hợp lệ
                    score = max(1.0, min(10.0, score))

                scores.append((score, i))

            # Sắp xếp theo điểm giảm dần
            return sorted(scores, key=lambda x: x[0], reverse=True)
        except Exception as e:
            logger.error(f"Lỗi khi phân tích đánh giá không có cấu trúc: {str(e)}")
            # Trả về điểm mặc định nếu gặp lỗi
            return [(5.0, i) for i in range(num_paths)]

    @staticmethod
    def analyze_evaluation_errors(evaluation: str, paths: List[str]) -> Dict[str, List[str]]:
        """
        Analyze the evaluation output for errors, both in format and content.

        Args:
            evaluation (str): The evaluation output from the LLM.
            paths (list): Optional. The list of reasoning paths that were evaluated.

        Returns:
            dict: A dictionary containing identified errors and suggestions for improvement.
        """
        errors = {
            "format_errors": [],
            "content_errors": [],
            "tot_specific_errors": [],
            "vietnamese_specific_errors": [],
            "reasoning_quality_issues": [],
            "suggestions": []
        }

        if not evaluation or not evaluation.strip():
            errors["format_errors"].append("Không có đánh giá được cung cấp")
            errors["suggestions"].append("Kiểm tra và thiết lập lại prompt đánh giá")
            return errors

        # Check for expected sections
        if "Path" not in evaluation:
            errors["format_errors"].append("Không tìm thấy phần đánh giá cho các đường dẫn")
            errors["suggestions"].append("Đảm bảo rằng prompt đánh giá yêu cầu phân tích từng đường dẫn")

        if "Ranking" not in evaluation:
            errors["format_errors"].append("Không tìm thấy phần xếp hạng đường dẫn")
            errors["suggestions"].append("Đảm bảo rằng prompt đánh giá yêu cầu xếp hạng các đường dẫn")

        # Check for scores
        score_pattern = r"Score: (\d+(?:\.\d+)?)/10"
        scores = re.findall(score_pattern, evaluation)

        if not scores:
            errors["format_errors"].append("Không tìm thấy điểm số cho các đường dẫn")
            errors["suggestions"].append("Đảm bảo rằng prompt đánh giá yêu cầu cho điểm mỗi đường dẫn theo thang 10")
        else:
            try:
                scores = [float(score) for score in scores]
                if any(score < 0 or score > 10 for score in scores):
                    errors["content_errors"].append("Có một hoặc nhiều điểm số nằm ngoài thang điểm 0-10")
                    errors["suggestions"].append("Đảm bảo rằng prompt đánh giá chỉ định rõ việc sử dụng thang điểm 0-10")
            except ValueError:
                errors["content_errors"].append("Không thể chuyển đổi một số điểm sang số")
                errors["suggestions"].append("Kiểm tra và làm rõ định dạng điểm số trong prompt đánh giá")

        # Check for path numbering consistency
        path_numbers = re.findall(r"Path (\d+):", evaluation)
        if path_numbers:
            try:
                path_numbers = [int(num) for num in path_numbers]
                expected_numbers = list(range(1, len(path_numbers) + 1))
                if sorted(path_numbers) != expected_numbers:
                    errors["format_errors"].append(f"Số đường dẫn không nhất quán: {path_numbers} vs. {expected_numbers}")
                    errors["suggestions"].append("Đảm bảo rằng tất cả các đường dẫn được đánh số tuần tự")
            except ValueError:
                pass

        # Check for ranking consistency with number of paths
        ranking_match = re.search(r"Ranking: (.+)", evaluation)
        if ranking_match:
            ranking_str = ranking_match.group(1)
            try:
                ranking = [int(r.strip()) for r in ranking_str.split(",")]
                if len(ranking) != len(path_numbers):
                    errors["content_errors"].append(
                        f"Số lượng đường dẫn trong xếp hạng ({len(ranking)}) không khớp với số đường dẫn được đánh giá ({len(path_numbers)})"
                    )
                    errors["suggestions"].append("Đảm bảo rằng tất cả các đường dẫn đều được xếp hạng")

                expected_ranking_set = set(range(1, len(path_numbers) + 1))
                if set(ranking) != expected_ranking_set:
                    errors["content_errors"].append(
                        f"Xếp hạng không chứa tất cả các số từ 1 đến {len(path_numbers)}"
                    )
                    errors["suggestions"].append(
                        "Đảm bảo rằng xếp hạng bao gồm mỗi số từ 1 đến N chính xác một lần"
                    )
            except ValueError:
                errors["content_errors"].append("Không thể phân tích xếp hạng dưới dạng danh sách các số nguyên")
                errors["suggestions"].append("Đảm bảo rằng xếp hạng được định dạng là danh sách được phân tách bằng dấu phẩy các số nguyên")

        # Additional specific ToT error detection when paths are provided
        if paths:
            # Check path lengths
            short_paths = [i+1 for i, path in enumerate(paths) if len(path.split()) < 50]
            if short_paths:
                errors["tot_specific_errors"].append(
                    f"Path {', '.join(map(str, short_paths))} quá ngắn (dưới 50 từ)"
                )
                # Add general information completeness issue
                errors["tot_specific_errors"].append("thiếu thông tin")
                errors["tot_specific_errors"].append("lack of information completeness")
                errors["suggestions"].append(
                    "Điều chỉnh tham số max_depth hoặc temperature cao hơn để khuyến khích khám phá sâu hơn"
                )

            # Check for similar paths (potential exploration issues)
            if len(paths) > 1:
                similar_paths = []
                for i in range(len(paths)):
                    for j in range(i+1, len(paths)):
                        similarity = difflib.SequenceMatcher(None, paths[i], paths[j]).ratio()
                        if similarity > 0.8:  # Arbitrary threshold
                            similar_paths.append((i+1, j+1))

                if similar_paths:
                    path_pairs = [f"{i} và {j}" for i, j in similar_paths]
                    errors["tot_specific_errors"].append(
                        f"Các đường dẫn {', '.join(path_pairs)} quá giống nhau"
                    )
                    # Add general repetition error messages
                    errors["tot_specific_errors"].append("lặp lại")
                    errors["tot_specific_errors"].append("repetition in reasoning")
                    errors["suggestions"].append(
                        "Tăng temperature hoặc điều chỉnh prompt để khuyến khích đa dạng trong quá trình khám phá"
                    )

            # Check for logical fallacies
            logical_fallacies = AdvancedEvaluator._detect_logical_fallacies(paths)
            if logical_fallacies:
                for path_idx, fallacies in logical_fallacies.items():
                    errors["tot_specific_errors"].append(
                        f"Path {path_idx} chứa logical fallacy: {', '.join(fallacies)}"
                    )
                    # Add general contradiction error messages
                    errors["tot_specific_errors"].append("mâu thuẫn")
                    errors["tot_specific_errors"].append("contradiction in reasoning")
                    # Add specific fallacy error messages
                    errors["tot_specific_errors"].append("lập luận sai lầm")
                    errors["tot_specific_errors"].append("logical fallacy detected")
                    errors["tot_specific_errors"].append("suy luận không hợp lý")
                    errors["tot_specific_errors"].append("invalid inference")
                errors["suggestions"].append(
                    "Điều chỉnh prompt để nhấn mạnh lập luận hợp lý và tránh các logical fallacy"
                )
                errors["suggestions"].append(
                    "Thêm hướng dẫn về cách xây dựng lập luận chặt chẽ và kiểm tra tính hợp lý của mỗi bước suy luận"
                )

            # Check for bias in reasoning
            biases = AdvancedEvaluator._detect_bias(paths)
            if biases:
                for path_idx, bias_types in biases.items():
                    errors["tot_specific_errors"].append(
                        f"Path {path_idx} thể hiện thiên kiến: {', '.join(bias_types)}"
                    )
                    # Add general irrelevance error messages
                    errors["tot_specific_errors"].append("không liên quan")
                    errors["tot_specific_errors"].append("irrelevant content")
                errors["suggestions"].append(
                    "Điều chỉnh prompt để khuyến khích đa dạng quan điểm và giảm thiểu thiên kiến"
                )

            # Check for incomplete information or evidence
            incomplete_info = AdvancedEvaluator._analyze_information_completeness(paths)
            if incomplete_info:
                for path_idx, issues in incomplete_info.items():
                    errors["tot_specific_errors"].append(
                        f"Path {path_idx} thiếu thông tin: {issues}"
                    )
                    # Add a general error about information completeness
                    errors["tot_specific_errors"].append("thiếu thông tin")
                    errors["tot_specific_errors"].append("lack of information completeness")
                errors["suggestions"].append(
                    "Điều chỉnh prompt để yêu cầu bằng chứng và thông tin đầy đủ hơn"
                )

            # Vietnamese-specific checks
            vietnamese_errors = AdvancedEvaluator._check_vietnamese_specific_issues(paths)
            if vietnamese_errors:
                errors["vietnamese_specific_errors"] = vietnamese_errors
                errors["suggestions"].append(
                    "Tối ưu hóa prompt để cải thiện chất lượng nội dung tiếng Việt và phân tích ngôn ngữ"
                )

            # Advanced reasoning quality assessment
            reasoning_issues = AdvancedEvaluator._assess_reasoning_quality(paths)
            if reasoning_issues:
                errors["reasoning_quality_issues"] = reasoning_issues
                errors["suggestions"].append(
                    "Điều chỉnh prompt để cải thiện chất lượng suy luận và tư duy phản biện"
                )

            # Check for convergence issues in Tree of Thought
            convergence_issues = AdvancedEvaluator._detect_convergence_issues(paths)
            if convergence_issues:
                for issue in convergence_issues:
                    errors["tot_specific_errors"].append(issue)
                # Add general non-convergence error messages
                errors["tot_specific_errors"].append("không hội tụ")
                errors["tot_specific_errors"].append("non-convergence in reasoning")
                errors["suggestions"].append(
                    "Điều chỉnh các tham số max_depth và branching_factor để cải thiện quá trình hội tụ của ToT"
                )

            # Check for prompt leakage issues
            prompt_leakage = AdvancedEvaluator._detect_prompt_leakage(paths)
            if prompt_leakage:
                errors["tot_specific_errors"].append(
                    f"Phát hiện dấu hiệu prompt leakage trong paths: {', '.join(map(str, prompt_leakage))}"
                )
                # Add general prompt leakage error messages
                errors["tot_specific_errors"].append("lặp lại chỉ dẫn")
                errors["tot_specific_errors"].append("prompt regurgitation")
                errors["tot_specific_errors"].append("sao chép prompt")
                errors["tot_specific_errors"].append("instruction copying")
                errors["suggestions"].append(
                    "Điều chỉnh prompt để tránh việc mô hình lặp lại chỉ dẫn thay vì tạo nội dung mới"
                )
                errors["suggestions"].append(
                    "Sử dụng các kỹ thuật như few-shot examples hoặc role prompting để giảm thiểu prompt leakage"
                )

        return errors

    @staticmethod
    def _check_vietnamese_specific_issues(paths):
        """
        Kiểm tra các vấn đề đặc thù của tiếng Việt trong các đường dẫn suy luận.

        Args:
            paths (list): Danh sách các đường dẫn suy luận để phân tích

        Returns:
            list: Danh sách các vấn đề phát hiện được
        """
        issues = []

        # Các mẫu regex cho vấn đề tiếng Việt phổ biến
        patterns = {
            "dấu_thanh_thiếu": r'\b[a-zA-Z]+[aeiouAEIOUyY]\b',  # Từ có nguyên âm nhưng không có dấu
            "từ_nước_ngoài_không_được_dịch": r'\b(implement|support|function|query|result|output|input)\b',
            "lỗi_chính_tả_phổ_biến": r'\b(ko|k|đc|dc|bth|bt|vv|vân vân)\b',
            "lỗi_dấu_câu": r'[a-zA-Z0-9àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]\s+[,.;:]'
        }

        for path in paths:
            # Tỷ lệ tiếng Việt trong văn bản
            vietnamese_chars = sum(1 for c in path if 'àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđĐ'.find(c) >= 0)
            total_chars = len([c for c in path if c.isalpha()])

            if total_chars > 0 and vietnamese_chars / total_chars < 0.3:
                issues.append("Tỷ lệ ký tự tiếng Việt thấp, có thể sử dụng nhiều từ không dấu hoặc tiếng Anh")

            # Kiểm tra các mẫu lỗi cụ thể
            for error_type, pattern in patterns.items():
                if re.search(pattern, path):
                    if error_type == "dấu_thanh_thiếu":
                        issues.append("Có thể có từ tiếng Việt thiếu dấu thanh hoặc dấu phụ")
                    elif error_type == "từ_nước_ngoài_không_được_dịch":
                        issues.append("Sử dụng nhiều thuật ngữ tiếng Anh không được dịch sang tiếng Việt")
                    elif error_type == "lỗi_chính_tả_phổ_biến":
                        issues.append("Sử dụng từ viết tắt hoặc thông tục không phù hợp trong ngữ cảnh học thuật")
                    elif error_type == "lỗi_dấu_câu":
                        issues.append("Có lỗi về quy tắc đặt dấu câu trong tiếng Việt")
                    break  # Chỉ báo cáo một lần cho mỗi loại lỗi

            # Kiểm tra cấu trúc câu tiếng Việt
            sentences = re.split(r'[.!?]+', path)
            for sentence in sentences:
                if len(sentence.split()) > 5:  # Câu đủ dài để phân tích
                    # Kiểm tra cấu trúc chủ-vị thiếu trong tiếng Việt
                    words = sentence.split()
                    if len(words) > 10 and not any(w in sentence.lower() for w in ["là", "đã", "sẽ", "có thể", "cần", "nên"]):
                        issues.append("Có thể có câu thiếu động từ hoặc cấu trúc chủ-vị không rõ ràng")
                        break

        return list(set(issues))  # Loại bỏ các vấn đề trùng lặp

    @staticmethod
    def _assess_reasoning_quality(paths):
        """
        Đánh giá chất lượng suy luận trong các đường dẫn.

        Args:
            paths (list): Danh sách các đường dẫn suy luận để phân tích

        Returns:
            list: Danh sách các vấn đề về chất lượng suy luận
        """
        issues = []

        # Các mẫu cho các dạng suy luận mạnh và yếu
        strong_reasoning_patterns = [
            r'(vì|bởi vì|do đó|vì vậy|do|kết quả là|dẫn đến|dẫn tới|từ đó)',
            r'(nếu|giả sử|giả định|cho rằng|với điều kiện).*(thì|sẽ)',
            r'(đầu tiên|thứ hai|thứ ba|tiếp theo|sau đó|cuối cùng)',
            r'(ví dụ|chẳng hạn|minh họa|thể hiện qua)',
            r'(so sánh|đối chiếu|tương phản|khác biệt)',
            r'(mặc dù|tuy nhiên|trái lại|ngược lại|mặt khác)',
            r'(kết luận|tóm lại|tổng kết|tổng quát)'
        ]

        weak_reasoning_patterns = [
            r'(luôn luôn|không bao giờ|tất cả|hoàn toàn|không có gì)',  # Tuyệt đối hóa
            r'(hiển nhiên|rõ ràng|chắc chắn|ai cũng biết)',  # Giả định không chứng minh
            r'(có thể|có lẽ|dường như|hình như)',  # Suy luận thiếu chắc chắn quá nhiều
            r'(tôi nghĩ|tôi cho rằng|theo quan điểm cá nhân)',  # Dựa quá nhiều vào ý kiến cá nhân
            r'(đơn giản là|chỉ là|đơn thuần là)'  # Đơn giản hóa vấn đề phức tạp
        ]

        for i, path in enumerate(paths):
            path_lower = path.lower()

            # Đếm số mẫu suy luận mạnh và yếu
            strong_patterns_count = sum(1 for pattern in strong_reasoning_patterns if re.search(pattern, path_lower))
            weak_patterns_count = sum(1 for pattern in weak_reasoning_patterns if re.search(pattern, path_lower))

            # Phân tích cấu trúc suy luận
            if strong_patterns_count < 3 and len(path.split()) > 100:
                issues.append(f"Path {i+1} thiếu các kết nối logic rõ ràng giữa các ý tưởng")

            if weak_patterns_count > 3:
                issues.append(f"Path {i+1} sử dụng nhiều biểu thức suy luận yếu hoặc thiếu chính xác")

            # Kiểm tra độ phức tạp của suy luận
            sentences = re.split(r'[.!?]+', path)
            avg_words_per_sentence = sum(len(s.split()) for s in sentences if s.strip()) / max(1, len([s for s in sentences if s.strip()]))

            if avg_words_per_sentence < 8 and len(sentences) > 5:
                issues.append(f"Path {i+1} sử dụng câu quá ngắn và đơn giản, thiếu sự phân tích phức tạp")

            # Kiểm tra mức độ phát triển ý tưởng
            if not re.search(r'(ví dụ|chẳng hạn|minh họa|thể hiện qua)', path_lower) and len(path.split()) > 100:
                issues.append(f"Path {i+1} thiếu ví dụ cụ thể hoặc minh họa để hỗ trợ lập luận")

            # Kiểm tra tư duy phản biện
            if not re.search(r'(mặc dù|tuy nhiên|mặt khác|trái lại|ngược lại)', path_lower) and len(path.split()) > 150:
                issues.append(f"Path {i+1} thiếu tư duy phản biện, không xem xét các quan điểm đối lập")

        return list(set(issues))  # Loại bỏ các vấn đề trùng lặp

    @staticmethod
    def _detect_convergence_issues(paths):
        """
        Phát hiện các vấn đề về hội tụ trong Tree of Thought.

        Args:
            paths (list): Danh sách các đường dẫn suy luận để phân tích

        Returns:
            list: Danh sách các vấn đề về hội tụ
        """
        issues = []

        # Kiểm tra đường dẫn không đi đến kết luận
        for i, path in enumerate(paths):
            path_lower = path.lower()

            # Kiểm tra xem path có kết luận hay không
            conclusion_patterns = [
                r'(kết luận|tóm lại|tổng kết|vậy nên|do đó|vì vậy|cuối cùng)[^.!?]*[.!?]',
                r'(therefore|thus|hence|in conclusion|to summarize|in summary|finally)[^.!?]*[.!?]'
            ]

            has_conclusion = any(re.search(pattern, path_lower) for pattern in conclusion_patterns)

            if not has_conclusion and len(path.split()) > 100:
                issues.append(f"Path {i+1} không hội tụ đến kết luận rõ ràng")

            # Kiểm tra sự phân nhánh quá mức hoặc không hiệu quả
            branches = path.count("Nhánh") + path.count("Branch")
            if branches > 3 and not has_conclusion:
                issues.append(f"Path {i+1} có quá nhiều nhánh suy luận nhưng không dẫn đến kết luận hiệu quả")

            # Kiểm tra các vòng lặp logic hoặc lặp lại ý tưởng
            sentences = re.split(r'[.!?]+', path)
            unique_sentences = set()
            repeated_ideas = 0

            for s in sentences:
                s_simplified = re.sub(r'[^\w\s]', '', s.lower())
                for existing in unique_sentences:
                    similarity = difflib.SequenceMatcher(None, s_simplified, existing).ratio()
                    if similarity > 0.7:  # Ngưỡng tương tự
                        repeated_ideas += 1
                        break
                unique_sentences.add(s_simplified)

            if repeated_ideas > 3:
                issues.append(f"Path {i+1} có dấu hiệu lặp lại ý tưởng hoặc vòng lặp logic")

        # Kiểm tra sự hội tụ giữa các đường dẫn
        if len(paths) > 1:
            conclusions = []
            for path in paths:
                last_sentences = re.split(r'[.!?]+', path)[-3:]  # 3 câu cuối cùng
                conclusions.append(" ".join(last_sentences))

            # Đánh giá độ tương đồng giữa các kết luận
            similar_conclusions = 0
            for i in range(len(conclusions)):
                for j in range(i+1, len(conclusions)):
                    similarity = difflib.SequenceMatcher(None, conclusions[i], conclusions[j]).ratio()
                    if similarity < 0.3:  # Ngưỡng khác biệt
                        similar_conclusions += 1

            if similar_conclusions > len(paths) / 2:
                issues.append("Các đường dẫn không hội tụ đến kết luận tương tự, thiếu sự đồng thuận")

        return issues

    @staticmethod
    def _detect_prompt_leakage(paths):
        """
        Phát hiện dấu hiệu prompt leakage trong các đường dẫn suy luận.

        Args:
            paths (list): Danh sách các đường dẫn suy luận để phân tích

        Returns:
            list: Chỉ số của các đường dẫn có dấu hiệu prompt leakage
        """
        prompt_leakage_paths = []

        prompt_leakage_patterns = [
            r'(as an ai|as a language model|as an assistant|i\'m an ai)',
            r'(với tư cách là|với vai trò là|là một mô hình ngôn ngữ|là một trợ lý)',
            r'(không thể|không có khả năng|không được phép|không được quyền)',
            r'(i apologize|i cannot|i\'m unable to|i don\'t have the ability)',
            r'(xin lỗi|tôi không thể|tôi không có khả năng|tôi không được phép)',
            r'(based on the prompt|according to your instructions|as requested)',
            r'(dựa trên hướng dẫn|theo chỉ dẫn của bạn|như bạn yêu cầu)'
        ]

        for i, path in enumerate(paths):
            path_lower = path.lower()

            for pattern in prompt_leakage_patterns:
                if re.search(pattern, path_lower):
                    prompt_leakage_paths.append(i+1)
                    break

        return prompt_leakage_paths

    @staticmethod
    def _detect_logical_fallacies(paths):
        """
        Detect common logical fallacies in reasoning paths.

        Args:
            paths (list): List of reasoning paths to analyze

        Returns:
            dict: Dictionary mapping path indices to detected fallacies
        """
        fallacy_patterns = {
            "appeal to authority": [
                r"according to.*authority", r"expert.*says", r"professor.*claims",
                r"scientist.*stated", r"research.*proves", r"studies.*show",
                r"authority.*claims", r"expert.*opinion"
            ],
            "ad hominem": [
                r"(can't|cannot).*because.*(who|background|education|person)",
                r"wrong.*because.*(who|background|education|person)",
                r"(isn't|is not).*credible.*because.*(who|background|education|person)"
            ],
            "false causality": [
                r"(after|following).*therefore", r"happened.*then.*caused",
                r"correlation.*causation", r"because.*followed"
            ],
            "hasty generalization": [
                r"(all|every|always|never).*because.*some", r"(all|every|always|never).*example",
                r"(entire|whole).*based on.*few", r"(everyone|everybody).*because.*I"
            ],
            "straw man": [
                r"simplifying.*argument", r"exaggerate.*position",
                r"misrepresent.*argument", r"weaker version"
            ],
            "false dichotomy": [
                r"(either|or).*only two", r"only two options",
                r"(must|have to|has to).*choose.*between", r"no middle ground"
            ]
        }

        results = {}

        for i, path in enumerate(paths):
            path_lower = path.lower()
            detected_fallacies = []

            for fallacy, patterns in fallacy_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, path_lower):
                        detected_fallacies.append(fallacy)
                        break  # Break after finding one instance of this fallacy type

            if detected_fallacies:
                results[i+1] = detected_fallacies

        return results

    @staticmethod
    def _detect_bias(paths):
        """
        Detect common cognitive biases in reasoning paths.

        Args:
            paths (list): List of reasoning paths to analyze

        Returns:
            dict: Dictionary mapping path indices to detected biases
        """
        bias_patterns = {
            "confirmation bias": [
                r"confirms.*belief", r"supports.*already.*know",
                r"evidence.*confirms", r"proves.*right",
                r"supports.*existing", r"reinforces.*view"
            ],
            "recency bias": [
                r"recent.*more important", r"latest.*most significant",
                r"current.*overrides", r"newest.*best"
            ],
            "optimism bias": [
                r"always.*better", r"certainly.*improve",
                r"definitely.*succeed", r"absolutely.*work",
                r"guaranteed.*positive", r"sure to.*benefit"
            ],
            "pessimism bias": [
                r"always.*worse", r"certainly.*fail",
                r"definitely.*problem", r"absolutely.*negative",
                r"guaranteed.*negative", r"sure to.*fail"
            ],
            "status quo bias": [
                r"traditional.*better", r"change.*risky",
                r"existing.*proven", r"new.*untested",
                r"current.*preferable", r"established.*reliable"
            ],
            "anchoring bias": [
                r"initial.*determines", r"first.*impression",
                r"starting.*point", r"baseline.*comparison"
            ]
        }

        results = {}

        for i, path in enumerate(paths):
            path_lower = path.lower()
            detected_biases = []

            for bias, patterns in bias_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, path_lower):
                        detected_biases.append(bias)
                        break  # Break after finding one instance of this bias type

            if detected_biases:
                results[i+1] = detected_biases

        return results

    @staticmethod
    def _analyze_information_completeness(paths):
        """
        Analyze the completeness of information in reasoning paths.

        Args:
            paths (list): List of reasoning paths to analyze

        Returns:
            dict: Dictionary mapping path indices to information completeness issues
        """
        completeness_issues = {}

        # Check for factual errors or inaccuracies
        factual_error_patterns = [
            r"(incorrect|wrong|false|inaccurate|mistaken|erroneous)",
            r"(misconception|misunderstanding|misinterpretation)",
            r"(not true|untrue|misleading|deceptive)"
        ]

        # Check for factual errors or inaccuracies
        for i, path in enumerate(paths):
            path_lower = path.lower()
            factual_errors = []

            # Check for factual error indicators
            if re.search(r"(incorrect|wrong|false|inaccurate|mistaken|erroneous)", path_lower):
                factual_errors.append("potential factual error detected")

            if re.search(r"(misconception|misunderstanding|misinterpretation)", path_lower):
                factual_errors.append("potential misconception detected")

            if re.search(r"(not true|untrue|misleading|deceptive)", path_lower):
                factual_errors.append("potentially misleading information")

            if factual_errors:
                completeness_issues[i+1] = ", ".join(factual_errors)

        # Check for evidence and supporting information
        evidence_patterns = [
            r"(research|study|evidence|data|statistics|experiment|survey|analysis)",
            r"(according to|based on|demonstrated by|shown by|confirmed by)",
            r"(percentage|number|figure|rate|ratio|proportion)"
        ]

        # Check for specific details
        detail_patterns = [
            r"\d+%", r"\d+\.\d+", r"\bin \d{4}\b", r"\b\d{4}-\d{2}-\d{2}\b"
        ]

        for i, path in enumerate(paths):
            issues = []
            path_lower = path.lower()

            # Check for claims without evidence
            if re.search(r"(is|are|was|were|will be|should be).*but", path_lower) and \
               not any(re.search(pattern, path_lower) for pattern in evidence_patterns):
                issues.append("thiếu bằng chứng cho các tuyên bố")
                issues.append("lack of evidence for claims")

            # Check for assertion-heavy content without support
            assertion_count = len(re.findall(r"(is|are|must|should|will|cannot|always|never)", path_lower))
            evidence_count = sum(1 for pattern in evidence_patterns if re.search(pattern, path_lower))
            detail_count = sum(1 for pattern in detail_patterns if re.search(pattern, path_lower))

            if assertion_count > 3 and evidence_count == 0 and detail_count == 0:
                issues.append("nhiều tuyên bố không có sự hỗ trợ")
                issues.append("multiple assertions without support")
                issues.append("thiếu thông tin")
                issues.append("lack of information completeness")

            # Check for one-sided arguments without counterpoints
            if re.search(r"(better|best|optimal|ideal|superior|preferable)", path_lower) and \
               not re.search(r"(however|although|despite|nevertheless|on the other hand|conversely)", path_lower):
                issues.append("thiếu xem xét các quan điểm đối lập")
                issues.append("lack of consideration for opposing viewpoints")

            if issues:
                # Add a general issue about information completeness
                if any("thiếu bằng chứng" in issue or "multiple assertions without support" in issue for issue in issues):
                    issues.append("thiếu thông tin")
                    issues.append("lack of information completeness")

                completeness_issues[i+1] = ", ".join(issues)

        return completeness_issues

    @staticmethod
    def evaluate_text_features(text: str) -> Dict[str, float]:
        """
        Đánh giá các đặc điểm văn bản.

        Args:
            text: Văn bản cần đánh giá

        Returns:
            Từ điển chứa các đặc điểm và điểm số tương ứng
        """
        features = {}

        # Độ dài văn bản
        text_length = len(text.split())
        features["length_score"] = min(8.0, text_length / 100)

        # Từ khóa chất lượng
        quality_terms = [
            # Tiếng Anh
            "therefore", "because", "consequently", "thus", "hence", "furthermore", "moreover", "in addition",
            "in conclusion", "to summarize", "specifically", "for example", "for instance", "in particular",
            # Tiếng Việt
            "vì vậy", "do đó", "bởi vì", "kết luận", "phân tích", "tóm lại", "cụ thể", "ví dụ",
            "ngoài ra", "hơn nữa", "thêm vào đó", "đặc biệt", "chính xác", "rõ ràng"
        ]
        quality_count = sum(1 for term in quality_terms if term.lower() in text.lower())
        features["quality_term_score"] = min(7.0, quality_count * 0.5)

        # Độ phức tạp của câu
        sentences = re.split(r'[.!?]+', text)
        avg_sentence_length = sum(len(s.split()) for s in sentences if s.strip()) / max(1, len([s for s in sentences if s.strip()]))
        features["complexity_score"] = min(7.0, avg_sentence_length / 5)

        # Tính mạch lạc
        coherence_terms = ["first", "second", "third", "next", "then", "finally", "lastly",
                          "thứ nhất", "thứ hai", "tiếp theo", "cuối cùng"]
        coherence_count = sum(1 for term in coherence_terms if term.lower() in text.lower())
        features["coherence_score"] = min(6.0, coherence_count * 0.8)

        return features

    @staticmethod
    @trace_function(name="advanced_evaluator_calculate_weighted_score")
    def calculate_weighted_score(features: Dict[str, float]) -> float:
        """
        Tính điểm tổng hợp từ các đặc điểm văn bản.

        Args:
            features: Từ điển chứa các đặc điểm và điểm số

        Returns:
            Điểm tổng hợp
        """
        weights = {
            "length_score": 0.2,
            "quality_term_score": 0.4,
            "complexity_score": 0.2,
            "coherence_score": 0.2
        }

        total_score = 0.0
        for feature, score in features.items():
            if feature in weights:
                total_score += score * weights[feature]

        # Thêm điểm cơ sở để đảm bảo điểm tối thiểu
        total_score += 3.0

        return total_score

    @staticmethod
    @trace_function(name="advanced_evaluator_evaluate_paths")
    @measure_latency("advanced_evaluator_evaluate_paths")
    def evaluate_paths(evaluation: str, paths: List[str]) -> Dict[str, Any]:
        """
        Đánh giá các đường dẫn suy luận.

        Args:
            evaluation: Đánh giá từ mô hình
            paths: Danh sách các đường dẫn suy luận

        Returns:
            Dictionary chứa kết quả đánh giá, bao gồm best_paths và reasoning
        """
        try:
            # Thử phân tích đánh giá có cấu trúc trước
            scored_paths_indices = AdvancedEvaluator.parse_structured_evaluation(evaluation)

            # Nếu không thành công, thử phân tích đánh giá không có cấu trúc
            if not scored_paths_indices:
                scored_paths_indices = AdvancedEvaluator.parse_unstructured_evaluation(evaluation, len(paths))

            # Chuyển đổi từ (điểm, id_đường_dẫn) sang (điểm, đường_dẫn)
            scored_paths = [(score, paths[path_id]) for score, path_id in scored_paths_indices if 0 <= path_id < len(paths)]

            # Nếu vẫn không có kết quả, gán điểm dựa trên độ dài và chất lượng của đường dẫn
            if not scored_paths:
                # Đánh giá dựa trên đặc điểm văn bản
                scored_paths = []
                for path in paths:
                    # Sử dụng phương thức đánh giá đặc điểm văn bản nâng cao
                    features = AdvancedEvaluator.evaluate_text_features(path)

                    # Tính điểm tổng hợp từ các đặc điểm
                    total_score = AdvancedEvaluator.calculate_weighted_score(features)

                    # Đảm bảo điểm nằm trong khoảng hợp lệ
                    total_score = max(3.0, min(9.0, total_score))

                    scored_paths.append((total_score, path))

            # Sắp xếp theo điểm giảm dần
            sorted_paths = sorted(scored_paths, key=lambda x: x[0], reverse=True)

            # Phân tích lỗi trong đánh giá
            errors = AdvancedEvaluator.analyze_evaluation_errors(evaluation, paths)

            # Trả về kết quả dưới dạng dictionary
            return {
                "best_paths": sorted_paths,
                "reasoning": evaluation,
                "explored_paths": len(paths),
                "errors": errors
            }
        except Exception as e:
            logger.error(f"Lỗi khi đánh giá đường dẫn: {str(e)}")
            # Phân tích lỗi trong đánh giá
            errors = AdvancedEvaluator.analyze_evaluation_errors("Không thể phân tích đánh giá", paths)

            # Trả về điểm mặc định nếu gặp lỗi
            default_paths = [(5.0, path) for path in paths]
            return {
                "best_paths": default_paths,
                "reasoning": "Không thể phân tích đánh giá. Sử dụng điểm mặc định.",
                "explored_paths": len(paths),
                "errors": errors
            }

    @staticmethod
    def suggest_tot_improvements(
        query: str,
        paths: List[str],
        evaluation_result: Dict[str, Any],
        tot_parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Đề xuất cải tiến cho Tree of Thought dựa trên kết quả đánh giá.

        Args:
            query: Truy vấn gốc
            paths: Danh sách các đường dẫn suy luận
            evaluation_result: Kết quả đánh giá từ ToT
            tot_parameters: Tham số hiện tại của ToT

        Returns:
            Từ điển chứa các đề xuất cải tiến
        """
        suggestions = {
            "parameter_suggestions": {},
            "prompt_suggestions": [],
            "exploration_suggestions": [],
            "reasoning": [],
            "optimization_suggestions": [],
            "priority": "low"  # Mặc định là ưu tiên thấp
        }

        # Phân tích lỗi trong đánh giá
        errors = AdvancedEvaluator.analyze_evaluation_errors(evaluation_result.get("reasoning", ""), paths)

        # Thêm các gợi ý dựa trên lỗi đặc thù của ToT
        if errors.get("tot_specific_errors", []):
            suggestions["reasoning"].append("Phát hiện lỗi đặc thù của Tree of Thought:")

            # Phân loại các lỗi để đưa ra gợi ý cụ thể
            repetition_errors = []
            non_convergence_errors = []
            contradiction_errors = []
            irrelevance_errors = []
            no_conclusion_errors = []
            short_path_errors = []
            inefficient_branching_errors = []
            inaccurate_evaluation_errors = []
            shallow_reasoning_errors = []
            unused_information_errors = []
            other_errors = []

            for error in errors.get("tot_specific_errors", []):
                if "giống nhau" in error.lower():
                    repetition_errors.append(error)
                elif "không hội tụ" in error.lower():
                    non_convergence_errors.append(error)
                elif "mâu thuẫn" in error.lower():
                    contradiction_errors.append(error)
                elif "không liên quan" in error.lower():
                    irrelevance_errors.append(error)
                elif "thiếu kết luận" in error.lower():
                    no_conclusion_errors.append(error)
                elif "quá ngắn" in error.lower():
                    short_path_errors.append(error)
                elif "lặp lại" in error.lower():
                    repetition_errors.append(error)
                elif "phân nhánh không hiệu quả" in error.lower():
                    inefficient_branching_errors.append(error)
                elif "đánh giá không chính xác" in error.lower() or "không đủ sâu" in error.lower():
                    inaccurate_evaluation_errors.append(error)
                elif "thiếu thông tin" in error.lower():
                    unused_information_errors.append(error)
                else:
                    other_errors.append(error)

            # Hiển thị tất cả các lỗi
            for error in errors.get("tot_specific_errors", []):
                suggestions["reasoning"].append(f"- {error}")

            # Đưa ra gợi ý cụ thể dựa trên loại lỗi
            if repetition_errors:
                suggestions["parameter_suggestions"]["temperature"] = min(0.95, tot_parameters.get("temperature", 0.7) + 0.15)
                suggestions["prompt_suggestions"].append(
                    "Thêm hướng dẫn vào prompt để khám phá các hướng tiếp cận khác nhau"
                )
                suggestions["optimization_suggestions"].append(
                    "Sử dụng TOTParameterOptimizer để tự động điều chỉnh temperature dựa trên độ đa dạng của đường dẫn"
                )

            if non_convergence_errors or no_conclusion_errors:
                suggestions["parameter_suggestions"]["max_depth"] = min(5, tot_parameters.get("max_depth", 3) + 1)
                suggestions["prompt_suggestions"].append(
                    "Thêm hướng dẫn vào prompt yêu cầu kết luận rõ ràng cho mỗi đường dẫn"
                )
                suggestions["optimization_suggestions"].append(
                    "Triển khai cơ chế phát hiện và xử lý đường dẫn không hội tụ tự động"
                )

            if contradiction_errors:
                suggestions["prompt_suggestions"].append(
                    "Thêm hướng dẫn vào prompt để duy trì tính nhất quán trong quá trình suy luận"
                )
                suggestions["optimization_suggestions"].append(
                    "Triển khai cơ chế kiểm tra tính nhất quán giữa các bước suy luận"
                )

            if irrelevance_errors:
                suggestions["prompt_suggestions"].append(
                    "Làm rõ truy vấn và thêm hướng dẫn để tập trung vào vấn đề chính"
                )
                suggestions["optimization_suggestions"].append(
                    "Triển khai cơ chế đánh giá độ liên quan của từng bước suy luận với truy vấn gốc"
                )

            if short_path_errors:
                suggestions["prompt_suggestions"].append(
                    "Yêu cầu suy luận chi tiết hơn với ít nhất 3-4 bước suy luận cho mỗi đường dẫn"
                )
                suggestions["parameter_suggestions"]["min_tokens_per_step"] = 100  # Đề xuất tham số mới
                suggestions["optimization_suggestions"].append(
                    "Triển khai cơ chế kiểm tra độ chi tiết của từng bước suy luận"
                )

            if inefficient_branching_errors:
                suggestions["parameter_suggestions"]["max_branches"] = min(6, tot_parameters.get("max_branches", 3) + 1)
                suggestions["parameter_suggestions"]["early_pruning"] = True
                suggestions["prompt_suggestions"].append(
                    "Thêm hướng dẫn vào prompt để khám phá các hướng tiếp cận đa dạng và độc lập"
                )
                suggestions["optimization_suggestions"].append(
                    "Triển khai PathPruner để loại bỏ sớm các đường dẫn không hiệu quả"
                )

            if inaccurate_evaluation_errors:
                suggestions["parameter_suggestions"]["multi_criteria_evaluation"] = True
                suggestions["prompt_suggestions"].append(
                    "Sử dụng các tiêu chí đánh giá cụ thể và rõ ràng hơn"
                )
                suggestions["optimization_suggestions"].append(
                    "Triển khai MultiCriteriaEvaluator để đánh giá đường dẫn dựa trên nhiều tiêu chí khác nhau"
                )

            if shallow_reasoning_errors:
                suggestions["parameter_suggestions"]["max_depth"] = min(5, tot_parameters.get("max_depth", 3) + 1)
                suggestions["prompt_suggestions"].append(
                    "Yêu cầu phân tích sâu hơn với các lập luận chi tiết và ví dụ cụ thể"
                )
                suggestions["optimization_suggestions"].append(
                    "Triển khai cơ chế đánh giá độ sâu của suy luận và tự động điều chỉnh tham số"
                )

            if unused_information_errors:
                suggestions["parameter_suggestions"]["information_sharing"] = True  # Đề xuất tham số mới
                suggestions["prompt_suggestions"].append(
                    "Yêu cầu tập hợp và tổng hợp thông tin từ các đường dẫn khác nhau"
                )
                suggestions["optimization_suggestions"].append(
                    "Triển khai cơ chế chia sẻ thông tin giữa các đường dẫn để tận dụng kết quả từ các nhánh khác"
                )

        # Kiểm tra số lượng đường dẫn đã khám phá
        explored_paths = evaluation_result.get("explored_paths", 0)
        max_branches = tot_parameters.get("max_branches", 3)
        max_depth = tot_parameters.get("max_depth", 3)
        temperature = tot_parameters.get("temperature", 0.7)

        # Tính toán số lượng đường dẫn tối đa có thể
        theoretical_max = 0
        for d in range(1, max_depth + 1):
            theoretical_max += max_branches ** d

        # Kiểm tra tỷ lệ khám phá
        exploration_ratio = explored_paths / max(1, theoretical_max)

        # Đề xuất điều chỉnh tham số dựa trên tỷ lệ khám phá
        if exploration_ratio < 0.3:
            suggestions["reasoning"].append(
                f"Tỷ lệ khám phá thấp: {exploration_ratio:.2f} ({explored_paths}/{theoretical_max})"
            )

            # Kiểm tra xem có lỗi lặp lại không
            has_repetition = any("giống nhau" in error for error in errors.get("tot_specific_errors", []))
            if has_repetition:
                suggestions["parameter_suggestions"]["temperature"] = min(1.0, temperature + 0.1)
                suggestions["reasoning"].append(
                    "Tăng temperature để tạo ra các đường dẫn đa dạng hơn"
                )
            else:
                # Nếu không có lỗi lặp lại, có thể cần tăng số nhánh
                suggestions["parameter_suggestions"]["max_branches"] = min(6, max_branches + 1)
                suggestions["reasoning"].append(
                    "Tăng số nhánh để khám phá nhiều đường dẫn hơn"
                )

            # Đề xuất về chiến lược khám phá
            suggestions["exploration_suggestions"].append(
                "Triển khai chiến lược khám phá thích ứng để tập trung vào các đường dẫn hứa hẹn"
            )
            suggestions["optimization_suggestions"].append(
                "Sử dụng TOTParameterOptimizer để tự động điều chỉnh tham số dựa trên độ phức tạp của truy vấn"
            )

        # Kiểm tra độ sâu của cây
        if max_depth < 3 and any("không hội tụ" in error for error in errors.get("tot_specific_errors", [])):
            suggestions["parameter_suggestions"]["max_depth"] = max_depth + 1
            suggestions["reasoning"].append(
                "Tăng độ sâu để cho phép suy luận phát triển đầy đủ hơn"
            )
            suggestions["optimization_suggestions"].append(
                "Triển khai cơ chế tự động điều chỉnh độ sâu dựa trên độ phức tạp của truy vấn"
            )

        # Kiểm tra chất lượng đường dẫn
        best_paths = evaluation_result.get("best_paths", [])
        if best_paths and len(best_paths) > 0:
            best_score = best_paths[0][0] if isinstance(best_paths[0], tuple) else 0

            # Nếu điểm số tốt nhất thấp, đề xuất cải thiện prompt
            if best_score < 7.0:
                suggestions["prompt_suggestions"].append(
                    "Điều chỉnh prompt để cung cấp hướng dẫn rõ ràng hơn về cách tiếp cận vấn đề"
                )
                suggestions["prompt_suggestions"].append(
                    "Thêm ví dụ về cách suy luận hiệu quả vào prompt"
                )
                suggestions["reasoning"].append(
                    f"Điểm số tốt nhất ({best_score:.2f}) thấp hơn mức mong đợi (7.0)"
                )
                suggestions["optimization_suggestions"].append(
                    "Triển khai cơ chế tự động điều chỉnh prompt dựa trên phản hồi và kết quả đánh giá"
                )

        # Đề xuất về chiến lược khám phá
        if "adaptive" in tot_parameters and not tot_parameters.get("adaptive", False):
            suggestions["parameter_suggestions"]["adaptive"] = True
            suggestions["reasoning"].append(
                "Bật chế độ adaptive để tự động điều chỉnh tham số dựa trên độ phức tạp của truy vấn"
            )

        # Đề xuất về tối ưu hóa nâng cao
        if "use_advanced_optimization" in tot_parameters and not tot_parameters.get("use_advanced_optimization", False):
            suggestions["parameter_suggestions"]["use_advanced_optimization"] = True
            suggestions["reasoning"].append(
                "Bật tối ưu hóa nâng cao để cải thiện hiệu suất và chất lượng kết quả"
            )

        # Đề xuất về khám phá song song
        if explored_paths > 20 and "parallel_exploration" in tot_parameters and not tot_parameters.get("parallel_exploration", False):
            suggestions["parameter_suggestions"]["parallel_exploration"] = True
            suggestions["parameter_suggestions"]["max_workers"] = 3
            suggestions["reasoning"].append(
                "Bật khám phá song song để tăng tốc quá trình suy luận với số lượng đường dẫn lớn"
            )
            suggestions["optimization_suggestions"].append(
                "Triển khai ParallelExplorationManager để quản lý tài nguyên hiệu quả khi khám phá song song"
            )

        # Đề xuất về cải thiện đánh giá
        if "multi_criteria_evaluation" not in tot_parameters or not tot_parameters.get("multi_criteria_evaluation", False):
            suggestions["parameter_suggestions"]["multi_criteria_evaluation"] = True
            suggestions["reasoning"].append(
                "Bật đánh giá đa tiêu chí để cải thiện chất lượng lựa chọn đường dẫn tốt nhất"
            )
            suggestions["optimization_suggestions"].append(
                "Triển khai MultiCriteriaEvaluator để đánh giá đường dẫn dựa trên nhiều tiêu chí khác nhau"
            )

        # Đề xuất về cải thiện hiệu suất
        if "selective_cache" not in tot_parameters or not tot_parameters.get("selective_cache", False):
            suggestions["parameter_suggestions"]["selective_cache"] = True
            suggestions["reasoning"].append(
                "Bật bộ nhớ đệm có chọn lọc để cải thiện hiệu suất khi xử lý các truy vấn tương tự"
            )
            suggestions["optimization_suggestions"].append(
                "Triển khai SelectiveCache để lưu trữ và tái sử dụng kết quả của các truy vấn tương tự"
            )

        # Đánh giá mức độ ưu tiên của các đề xuất
        tot_specific_errors = errors.get("tot_specific_errors", [])

        # Đánh giá mức độ ưu tiên dựa trên số lượng lỗi và mức độ nghiêm trọng
        if len(tot_specific_errors) > 3 or len(suggestions["parameter_suggestions"]) > 2 or len(suggestions["prompt_suggestions"]) > 1:
            suggestions["priority"] = "high"
        elif len(tot_specific_errors) > 1 or len(suggestions["parameter_suggestions"]) > 0 or len(suggestions["prompt_suggestions"]) > 0:
            suggestions["priority"] = "medium"
        else:
            suggestions["priority"] = "low"

        # Thêm đề xuất tổng hợp
        if suggestions["priority"] != "low":
            suggestions["summary"] = "Cần cải thiện Tree of Thought với các điều chỉnh tham số và tối ưu hóa nâng cao"

            # Đề xuất triển khai TOTParameterOptimizer nếu có nhiều đề xuất điều chỉnh tham số
            if len(suggestions["parameter_suggestions"]) >= 2:
                if "TOTParameterOptimizer" not in str(suggestions["optimization_suggestions"]):
                    suggestions["optimization_suggestions"].append(
                        "Triển khai TOTParameterOptimizer để tự động điều chỉnh tất cả các tham số dựa trên đặc điểm truy vấn"
                    )

        return suggestions

    @staticmethod
    def _get_domain_specific_criteria(query: str) -> List[str]:
        """
        Tạo tiêu chí đánh giá dựa trên lĩnh vực cụ thể.

        Args:
            query: Truy vấn gốc

        Returns:
            Danh sách các tiêu chí đánh giá dựa trên lĩnh vực
        """
        domain_criteria = []

        # Lĩnh vực y tế
        if any(term in query.lower() for term in ["y tế", "sức khỏe", "bệnh", "medical", "health", "disease", "treatment"]):
            domain_criteria.extend([
                "Tính chính xác và cập nhật của thông tin y tế",
                "Khả năng phân tích các nguyên nhân và triệu chứng",
                "Tính toàn diện của các phương pháp điều trị"
            ])

        # Lĩnh vực luật pháp
        if any(term in query.lower() for term in ["luật", "pháp luật", "legal", "law", "regulation", "quy định"]):
            domain_criteria.extend([
                "Tính chính xác và cập nhật của thông tin pháp lý",
                "Khả năng phân tích các điều luật và quy định",
                "Tính logic và nhất quán trong lập luận pháp lý"
            ])

        # Lĩnh vực giáo dục
        if any(term in query.lower() for term in ["giáo dục", "học tập", "education", "learning", "teaching", "dạy học"]):
            domain_criteria.extend([
                "Tính sư phạm và phương pháp giảng dạy",
                "Khả năng truyền đạt kiến thức rõ ràng",
                "Tính phù hợp với đối tượng học tập"
            ])

        # Lĩnh vực kỹ thuật
        if any(term in query.lower() for term in ["kỹ thuật", "công nghệ", "technical", "technology", "engineering", "phần mềm"]):
            domain_criteria.extend([
                "Tính chính xác và cập nhật của thông tin kỹ thuật",
                "Khả năng giải thích các khái niệm kỹ thuật phức tạp",
                "Tính thực tiễn và khả thi của giải pháp"
            ])

        return domain_criteria

    @staticmethod
    @trace_function(name="advanced_evaluator_create_criteria")
    @lru_cache(maxsize=100)
    def create_criteria_for_query(query: str) -> List[str]:
        """
        Tạo tiêu chí đánh giá phù hợp với truy vấn.

        Args:
            query: Truy vấn gốc

        Returns:
            Danh sách các tiêu chí đánh giá
        """
        # Tiêu chí cơ bản
        basic_criteria = [
            "Tính logic và nhất quán",
            "Độ sâu và chi tiết của phân tích",
            "Mức độ liên quan đến truy vấn gốc"
        ]

        # Tiêu chí bổ sung dựa trên loại truy vấn
        additional_criteria = []

        # Truy vấn phân tích
        if any(term in query.lower() for term in ["phân tích", "analyze", "compare", "so sánh", "đánh giá", "evaluate"]):
            additional_criteria.extend([
                "Tính toàn diện của phân tích",
                "Khả năng xem xét nhiều khía cạnh"
            ])

        # Truy vấn giải quyết vấn đề
        if any(term in query.lower() for term in ["giải quyết", "solve", "solution", "giải pháp", "khắc phục", "fix"]):
            additional_criteria.extend([
                "Tính khả thi của giải pháp",
                "Hiệu quả của giải pháp đề xuất",
                "Khả năng giải quyết vấn đề gốc"
            ])

        # Truy vấn sáng tạo
        if any(term in query.lower() for term in ["sáng tạo", "creative", "innovative", "đổi mới", "thiết kế", "design"]):
            additional_criteria.extend([
                "Tính sáng tạo và độc đáo",
                "Tính đột phá của ý tưởng",
                "Khả năng tạo ra giá trị mới"
            ])

        # Truy vấn dự đoán
        if any(term in query.lower() for term in ["dự đoán", "predict", "forecast", "tương lai", "future"]):
            additional_criteria.extend([
                "Tính hợp lý của dự đoán",
                "Khả năng dự đoán dựa trên dữ liệu và xu hướng",
                "Xem xét các kịch bản khác nhau"
            ])

        # Kiểm tra các lĩnh vực cụ thể
        domain_criteria = AdvancedEvaluator._get_domain_specific_criteria(query)
        if domain_criteria:
            additional_criteria.extend(domain_criteria)

        # Kết hợp tiêu chí cơ bản và bổ sung, giới hạn tối đa 5 tiêu chí
        all_criteria = basic_criteria + additional_criteria
        return all_criteria[:5]

    @staticmethod
    def analyze_query_alignment(query, paths):
        """
        Analyze how well paths align with the original query.

        Args:
            query (str): The original query
            paths (list): List of reasoning paths

        Returns:
            dict: Results of alignment analysis including scores and divergence detection
        """
        # Extract meaningful keywords from the query
        query_keywords = set(re.findall(r'\b\w{3,}\b', query.lower()))
        path_scores = []
        path_analysis = []

        for i, path in enumerate(paths):
            path_lower = path.lower()
            path_keywords = set(re.findall(r'\b\w{3,}\b', path_lower))

            # Calculate Jaccard similarity
            if not query_keywords or not path_keywords:
                score = 0.0
            else:
                overlap = len(query_keywords.intersection(path_keywords))
                union = len(query_keywords.union(path_keywords))
                score = overlap / union

            # Analyze what's missing or different
            missing_keywords = query_keywords - path_keywords
            extra_keywords = path_keywords - query_keywords

            path_scores.append(score)
            path_analysis.append({
                "path_index": i+1,
                "alignment_score": score,
                "missing_query_keywords": list(missing_keywords)[:5],  # List top 5 missing keywords
                "extra_path_keywords": list(extra_keywords)[:5],       # List top 5 extra keywords
                "is_divergent": score < 0.3                           # Consider divergent if below threshold
            })

        # Determine if paths have diverged from query
        divergence_detected = any(score < 0.3 for score in path_scores)

        return {
            'query_alignment_scores': path_scores,
            'divergence_detected': divergence_detected,
            'path_analysis': path_analysis,
            'average_alignment': sum(path_scores) / len(path_scores) if path_scores else 0.0
        }
