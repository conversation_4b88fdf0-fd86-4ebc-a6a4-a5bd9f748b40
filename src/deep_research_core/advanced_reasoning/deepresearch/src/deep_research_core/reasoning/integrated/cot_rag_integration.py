#!/usr/bin/env python3
"""
CoT-RAG Integration

This module provides an integrator for combining Chain of Thought reasoning
with Retrieval-Augmented Generation.
"""

import re
from typing import Any, Dict, List, Optional, Tuple, Union

from deep_research_core.utils.types import Document, ReasoningResult
from deep_research_core.models.base import BaseModel
from deep_research_core.reasoning.base import BaseReasoner
from deep_research_core.reasoning.cot import ChainOfThought
from deep_research_core.reasoning.rag.base import RAGReasoner


class CoTRAGIntegrator(BaseReasoner):
    """
    An integrator that combines Chain of Thought (CoT) reasoning with
    Retrieval-Augmented Generation (RAG).
    
    This integrator:
    1. Uses CoT to break down complex queries into reasoning steps
    2. For each reasoning step, retrieves relevant documents using RAG
    3. Enhances each reasoning step with relevant information from documents
    4. Generates a final answer based on all reasoning steps and documents
    """
    
    def __init__(
        self,
        model: BaseModel,
        cot_reasoner: ChainOfThought,
        rag_reasoner: RAGReasoner,
        max_documents_per_step: int = 3,
        retrieval_strategy: str = "step_based",
        language: str = "en",
        verbose: bool = False,
    ):
        """
        Initialize the CoT-RAG integrator.
        
        Args:
            model: The base model to use for reasoning
            cot_reasoner: A Chain of Thought reasoner
            rag_reasoner: A RAG reasoner for document retrieval and enhancement
            max_documents_per_step: Maximum number of documents to retrieve per reasoning step
            retrieval_strategy: Strategy for document retrieval, either "step_based" (retrieve 
                                for each step) or "initial" (retrieve only for the original query)
            language: The language to use for reasoning (default: "en")
            verbose: Whether to output verbose logging
        """
        super().__init__()
        self.model = model
        self.cot_reasoner = cot_reasoner
        
        if not rag_reasoner:
            raise ValueError("RAG reasoner is required for CoT-RAG integration")
        self.rag_reasoner = rag_reasoner
        
        self.max_documents_per_step = max_documents_per_step
        
        if retrieval_strategy not in ["step_based", "initial"]:
            raise ValueError("Retrieval strategy must be either 'step_based' or 'initial'")
        self.retrieval_strategy = retrieval_strategy
        
        self.language = language
        self.verbose = verbose
        
        # Set up language-specific templates
        self._setup_language_templates()
    
    def _setup_language_templates(self):
        """Set up language-specific templates for prompts and instructions."""
        if self.language == "vi":
            self.step_prompt = "Bước {step}: {description}"
            self.final_answer_prompt = (
                "Dựa trên quá trình suy luận và tài liệu tham khảo, "
                "hãy cung cấp câu trả lời cuối cùng và đầy đủ cho câu hỏi: {query}\n\n"
                "Tổng hợp thông tin từ tất cả các bước suy luận và các tài liệu đã sử dụng."
            )
            self.step_format_regex = r"Bước\s+(\d+):\s*(.*?)(?:\n|$)"
            self.reasoning_enhanced_prompt = (
                "Dựa vào tài liệu sau:\n\n{documents}\n\n"
                "Hãy cải thiện bước suy luận này:\n{reasoning}\n\n"
                "Cung cấp suy luận chi tiết và đầy đủ hơn, sử dụng thông tin từ tài liệu."
            )
        else:  # Default to English
            self.step_prompt = "Step {step}: {description}"
            self.final_answer_prompt = (
                "Based on the reasoning process and referenced documents, "
                "provide a comprehensive final answer to the question: {query}\n\n"
                "Synthesize information from all reasoning steps and documents used."
            )
            self.step_format_regex = r"Step\s+(\d+):\s*(.*?)(?:\n|$)"
            self.reasoning_enhanced_prompt = (
                "Based on the following documents:\n\n{documents}\n\n"
                "Enhance this reasoning step:\n{reasoning}\n\n"
                "Provide more detailed and comprehensive reasoning, incorporating information from the documents."
            )
    
    def extract_reasoning_steps(self, cot_result: ReasoningResult) -> List[Dict[str, Any]]:
        """
        Extract structured reasoning steps from a Chain of Thought reasoning result.
        
        Args:
            cot_result: The result from the Chain of Thought reasoner
            
        Returns:
            A list of reasoning steps, each containing a step number, description, and reasoning
        """
        reasoning_text = cot_result.answer
        steps = []
        
        # Extract steps using regex based on the language
        step_matches = re.finditer(self.step_format_regex, reasoning_text, re.MULTILINE)
        
        current_step = None
        current_description = None
        current_reasoning = []
        
        # Process all lines in the reasoning text
        for line in reasoning_text.split('\n'):
            # Check if line starts a new step
            match = re.match(self.step_format_regex, line)
            if match:
                # Save the previous step if there was one
                if current_step is not None:
                    steps.append({
                        "step": current_step,
                        "description": current_description,
                        "reasoning": '\n'.join(current_reasoning).strip(),
                        "documents": []  # Will be filled later
                    })
                
                # Start a new step
                current_step = int(match.group(1))
                current_description = match.group(2).strip()
                current_reasoning = []
            elif current_step is not None:
                # Add line to current reasoning
                current_reasoning.append(line)
        
        # Add the last step if there is one
        if current_step is not None:
            steps.append({
                "step": current_step,
                "description": current_description,
                "reasoning": '\n'.join(current_reasoning).strip(),
                "documents": []  # Will be filled later
            })
        
        # If no steps were found but there is reasoning text, treat it as a single step
        if not steps and reasoning_text.strip():
            steps.append({
                "step": 1,
                "description": "Single step reasoning",
                "reasoning": reasoning_text.strip(),
                "documents": []
            })
        
        return steps
    
    def process_reasoning_step(
        self, 
        step: Dict[str, Any], 
        query: str, 
        all_documents: List[Document]
    ) -> Dict[str, Any]:
        """
        Process a single reasoning step by retrieving relevant documents and enhancing the reasoning.
        
        Args:
            step: The reasoning step to process
            query: The original query
            all_documents: List of all documents retrieved so far
            
        Returns:
            The enhanced reasoning step with documents
        """
        # Retrieve relevant documents based on the retrieval strategy
        if self.retrieval_strategy == "step_based":
            retrieval_query = f"{query}\n{step['description']}\n{step['reasoning']}"
            rag_result = self.rag_reasoner.retrieve(
                retrieval_query,
                k=self.max_documents_per_step
            )
            step_documents = rag_result.documents
        else:  # "initial" strategy - use the same documents for all steps
            step_documents = all_documents[:self.max_documents_per_step]
        
        # Store the documents with this step
        step["documents"] = step_documents
        
        # Format documents for the prompt
        documents_text = "\n\n".join(
            [f"Document {i+1} ({doc.source}):\n{doc.content}" for i, doc in enumerate(step_documents)]
        )
        
        # Enhance the reasoning with document information
        if step_documents:
            enhanced_prompt = self.reasoning_enhanced_prompt.format(
                documents=documents_text,
                reasoning=step["reasoning"]
            )
            
            enhanced_result = self.model.generate(enhanced_prompt)
            enhanced_reasoning = enhanced_result.strip()
            
            # Update the reasoning with the enhanced version
            step["reasoning"] = enhanced_reasoning
        
        return step
    
    def generate_final_answer(
        self, 
        query: str, 
        reasoning_steps: List[Dict[str, Any]]
    ) -> str:
        """
        Generate the final answer based on all reasoning steps and documents.
        
        Args:
            query: The original query
            reasoning_steps: List of all reasoning steps with documents
            
        Returns:
            The final answer
        """
        # Format the reasoning steps
        formatted_steps = []
        for step in reasoning_steps:
            step_text = f"{self.step_prompt.format(step=step['step'], description=step['description'])}\n"
            step_text += f"{step['reasoning']}\n"
            
            # Add document references
            if step["documents"]:
                if self.language == "vi":
                    step_text += "\nTài liệu tham khảo:\n"
                else:
                    step_text += "\nReferenced Documents:\n"
                    
                for i, doc in enumerate(step["documents"]):
                    step_text += f"- {doc.source}: {doc.content[:100]}...\n"
            
            formatted_steps.append(step_text)
        
        # Create the prompt for the final answer
        final_prompt = (
            f"Query: {query}\n\n"
            f"Reasoning Process:\n\n"
            f"{chr(10).join(formatted_steps)}\n\n"
            f"{self.final_answer_prompt.format(query=query)}"
        )
        
        # Generate the final answer
        final_answer = self.model.generate(final_prompt)
        
        return final_answer.strip()
    
    def reason(self, query: str) -> ReasoningResult:
        """
        Perform reasoning using the integrated CoT-RAG approach.
        
        Args:
            query: The query to reason about
            
        Returns:
            A ReasoningResult with the answer and metadata
        """
        # Step 1: Break down the query into reasoning steps using Chain of Thought
        if self.verbose:
            print(f"Breaking down query into reasoning steps: {query}")
        
        cot_result = self.cot_reasoner.reason(query)
        reasoning_steps = self.extract_reasoning_steps(cot_result)
        
        if self.verbose:
            print(f"Extracted {len(reasoning_steps)} reasoning steps")
        
        # Step 2: Initialize for document retrieval
        all_documents = []
        
        # For "initial" retrieval strategy, retrieve documents once for the query
        if self.retrieval_strategy == "initial":
            rag_result = self.rag_reasoner.retrieve(
                query,
                k=self.max_documents_per_step * len(reasoning_steps)
            )
            all_documents = rag_result.documents
            
            if self.verbose:
                print(f"Retrieved {len(all_documents)} documents for the initial query")
        
        # Step 3: Process each reasoning step
        processed_steps = []
        for step in reasoning_steps:
            if self.verbose:
                print(f"Processing step {step['step']}: {step['description']}")
            
            processed_step = self.process_reasoning_step(step, query, all_documents)
            processed_steps.append(processed_step)
            
            # Update all_documents with any new documents
            for doc in processed_step["documents"]:
                if doc not in all_documents:
                    all_documents.append(doc)
        
        # Step 4: Generate the final answer
        if self.verbose:
            print("Generating final answer")
        
        final_answer = self.generate_final_answer(query, processed_steps)
        
        # Step 5: Create and return the result
        result = ReasoningResult(
            query=query,
            answer=final_answer,
            metadata={
                "reasoning_steps": processed_steps,
                "total_documents": len(all_documents),
                "unique_documents": len({doc.source for doc in all_documents}),
                "retrieval_strategy": self.retrieval_strategy,
                "language": self.language
            }
        )
        
        return result 