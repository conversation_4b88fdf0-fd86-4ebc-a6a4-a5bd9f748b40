"""
Integration of WebSearchAgent with Chain of Thought (CoT) reasoning.

This module provides a combined approach where:
- Web search is used to retrieve up-to-date information
- Chain of Thought reasoning is used to break down complex queries
- Each reasoning step can be enhanced with relevant search results
"""

import time
import logging
from typing import Dict, Any, List, Optional, Union, Callable, Tuple

from ...agents.web_search_agent import WebSearchAgent
from ...reasoning.cot import ChainOfThought
from ...utils.structured_logging import get_logger
from ...utils.performance_metrics import measure_latency
from ...utils.distributed_tracing import trace_function

# Create a logger
logger = get_logger(__name__)

class WebSearchCoTIntegrator:
    """
    Integration of WebSearchAgent with Chain of Thought (CoT) reasoning.
    
    This class provides a combined approach where:
    - Web search is used to retrieve up-to-date information
    - Chain of Thought reasoning is used to break down complex queries
    - Each reasoning step can be enhanced with relevant search results
    """
    
    def __init__(
        self,
        cot_instance: ChainOfThought,
        web_search_agent: Optional[WebSearchAgent] = None,
        web_search_config: Optional[Dict[str, Any]] = None,
        max_search_results: int = 5,
        search_after_steps: bool = True,
        search_before_final: bool = True,
        verbose: bool = False
    ):
        """
        Initialize the WebSearchCoTIntegrator.
        
        Args:
            cot_instance: An instance of ChainOfThought
            web_search_agent: An instance of WebSearchAgent (optional)
            web_search_config: Configuration for WebSearchAgent if not provided
            max_search_results: Maximum number of search results to retrieve
            search_after_steps: Whether to perform searches after each reasoning step
            search_before_final: Whether to perform a search before generating the final answer
            verbose: Whether to log detailed information
        """
        self.cot = cot_instance
        
        # Initialize WebSearchAgent if not provided
        if web_search_agent is None:
            if web_search_config is None:
                web_search_config = {
                    "search_method": "api",
                    "api_search_config": {
                        "engine": "google",
                        "language": "vi"
                    }
                }
            self.web_search = WebSearchAgent(**web_search_config)
        else:
            self.web_search = web_search_agent
        
        self.max_search_results = max_search_results
        self.search_after_steps = search_after_steps
        self.search_before_final = search_before_final
        self.verbose = verbose
        
        # Override the generate_step method of CoT to include web search
        self._original_generate_step = self.cot._generate_step
        self.cot._generate_step = self._generate_step_with_search
        
        # Override the generate_answer method of CoT to include web search
        self._original_generate_answer = self.cot._generate_answer
        self.cot._generate_answer = self._generate_answer_with_search
        
        logger.info(
            "Initialized WebSearchCoTIntegrator",
            max_search_results=max_search_results,
            search_after_steps=search_after_steps,
            search_before_final=search_before_final
        )
    
    def _generate_step_with_search(self, query: str, steps: List[str]) -> str:
        """
        Generate a reasoning step with web search integration.
        
        Args:
            query: The original query
            steps: The reasoning steps so far
            
        Returns:
            The next reasoning step
        """
        # Call the original generate_step method
        next_step = self._original_generate_step(query, steps)
        
        # Perform search after the step if enabled
        if self.search_after_steps:
            # Extract search query from the step
            search_query = self._extract_search_query(next_step)
            
            if search_query:
                logger.info("Performing web search after CoT step", query=search_query)
                search_response = self.web_search.search(search_query, max_results=self.max_search_results)
                
                if search_response.get("success", False):
                    results = search_response.get("results", [])
                    
                    if results:
                        # Add search results to the step
                        search_context = "\nInformation from web search:\n"
                        for i, result in enumerate(results):
                            search_context += f"{i+1}. {result.get('title', '')}: {result.get('snippet', '')}\n"
                            search_context += f"   Source: {result.get('url', '')}\n"
                        
                        next_step += search_context
        
        return next_step
    
    def _generate_answer_with_search(self, query: str, steps: List[str]) -> str:
        """
        Generate the final answer with web search integration.
        
        Args:
            query: The original query
            steps: The reasoning steps
            
        Returns:
            The final answer
        """
        # Perform search before generating the final answer if enabled
        if self.search_before_final:
            logger.info("Performing web search before final answer", query=query)
            search_response = self.web_search.search(query, max_results=self.max_search_results)
            
            if search_response.get("success", False):
                results = search_response.get("results", [])
                
                if results:
                    # Add search results to the steps
                    search_context = "Information from web search:\n"
                    for i, result in enumerate(results):
                        search_context += f"{i+1}. {result.get('title', '')}: {result.get('snippet', '')}\n"
                        search_context += f"   Source: {result.get('url', '')}\n"
                    
                    steps.append(search_context)
        
        # Call the original generate_answer method
        return self._original_generate_answer(query, steps)
    
    def _extract_search_query(self, step: str) -> Optional[str]:
        """
        Extract a search query from a reasoning step.
        
        Args:
            step: The reasoning step
            
        Returns:
            The search query or None
        """
        # Look for patterns like "I need to search for X" or "Let me search for X"
        search_patterns = [
            "search for",
            "look up",
            "find information about",
            "need to know about",
            "tìm kiếm",
            "tra cứu",
            "tìm thông tin về"
        ]
        
        for pattern in search_patterns:
            if pattern in step.lower():
                # Extract the query after the pattern
                parts = step.lower().split(pattern)
                if len(parts) > 1:
                    query = parts[1].strip()
                    # Remove punctuation at the end
                    if query and query[-1] in ".,:;?!":
                        query = query[:-1]
                    return query
        
        return None
    
    @trace_function(name="web_search_cot_process")
    @measure_latency("web_search_cot_process")
    def process(
        self,
        query: str,
        callback: Optional[Callable[[str], None]] = None
    ) -> Dict[str, Any]:
        """
        Process a query using both web search and CoT.
        
        Args:
            query: The query to process
            callback: Callback function for streaming output
            
        Returns:
            Dictionary containing the answer and metadata
        """
        start_time = time.time()
        
        # Perform initial search
        initial_search_response = self.web_search.search(query, max_results=self.max_search_results)
        initial_search_results = []
        
        if initial_search_response.get("success", False):
            initial_search_results = initial_search_response.get("results", [])
        
        # Prepare initial context with search results
        initial_context = ""
        if initial_search_results:
            initial_context += "Information from web search:\n"
            for i, result in enumerate(initial_search_results):
                initial_context += f"{i+1}. {result.get('title', '')}: {result.get('snippet', '')}\n"
                initial_context += f"   Source: {result.get('url', '')}\n\n"
        
        # Process with CoT
        cot_response = self.cot.reason(
            query=query,
            initial_context=initial_context,
            callback=callback
        )
        
        # Calculate latency
        latency = time.time() - start_time
        
        # Prepare response
        response = {
            "query": query,
            "answer": cot_response.get("answer", ""),
            "steps": cot_response.get("steps", []),
            "initial_search_results": initial_search_results,
            "latency": latency
        }
        
        return response
