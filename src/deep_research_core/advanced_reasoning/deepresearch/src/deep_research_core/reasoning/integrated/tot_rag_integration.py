"""
ToT-RAG Integration Module

This module implements an integrated reasoning approach that combines Tree of Thought (ToT)
reasoning with Retrieval-Augmented Generation (RAG) for enhanced reasoning capabilities.

The integration allows for:
1. Document retrieval at each node of the reasoning tree
2. Context-aware exploration of the reasoning space
3. Evidence-backed branching and evaluation
"""

import logging
from typing import Dict, List, Optional, Any, Tuple, Callable, Union
import time

from deep_research_core.models.base import BaseModel
from deep_research_core.reasoning.tot.implementation import TreeOfThought
from deep_research_core.reasoning.rag.implementation import RAGReasoner
from deep_research_core.utils.types import Document, ThoughtNode, ReasoningResult
from deep_research_core.utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

class ToTRAGIntegrator:
    """
    Integration of Tree of Thought reasoning with RAG capabilities.
    
    This class provides a combined reasoning approach where:
    - The tree exploration is guided by retrieved documents
    - Each branch of thinking has access to relevant context
    - Document retrieval is performed at key decision points
    - Evaluation takes into account both reasoning quality and evidence support
    """
    
    def __init__(
        self,
        model: BaseModel,
        tot_reasoner: Optional[TreeOfThought] = None,
        rag_reasoner: Optional[RAGReasoner] = None,
        retrieval_depth: int = 2,  # How deep in the tree to perform retrievals
        max_documents_per_node: int = 3,
        evaluation_weights: Dict[str, float] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        verbose: bool = False
    ):
        """
        Initialize the ToT-RAG integrator.
        
        Args:
            model: The language model to use for reasoning
            tot_reasoner: Optional pre-configured ToT reasoner
            rag_reasoner: Optional pre-configured RAG reasoner
            retrieval_depth: Maximum depth in the tree to perform retrievals
            max_documents_per_node: Maximum documents to retrieve per node
            evaluation_weights: Weights for different evaluation criteria
            temperature: Temperature for generation
            max_tokens: Maximum tokens for generation
            verbose: Whether to log detailed information
        """
        self.model = model
        self.tot_reasoner = tot_reasoner or TreeOfThought(
            provider=model.provider,
            model=model.model_name,
            temperature=temperature,
            max_tokens=max_tokens
        )
        
        # We must have a concrete RAG reasoner provided
        if rag_reasoner is None:
            raise ValueError("A concrete RAG reasoner implementation must be provided")
        
        self.rag_reasoner = rag_reasoner
        self.retrieval_depth = retrieval_depth
        self.max_documents_per_node = max_documents_per_node
        self.evaluation_weights = evaluation_weights or {
            "reasoning_quality": 0.4,
            "evidence_support": 0.4,
            "coherence": 0.2
        }
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.verbose = verbose
    
    def reason(
        self,
        query: str,
        relevant_documents: Optional[List[Document]] = None,
        max_depth: int = 3,
        max_branches: int = 3,
        num_samples_per_branch: int = 2,
        custom_evaluation_fn: Optional[Callable] = None,
        stop_tokens: List[str] = None,
        custom_prompt_template: Optional[str] = None,
        **kwargs
    ) -> ReasoningResult:
        """
        Perform integrated ToT-RAG reasoning.
        
        Args:
            query: The user query to reason about
            relevant_documents: Optional pre-retrieved relevant documents
            max_depth: Maximum depth of the reasoning tree
            max_branches: Maximum branches to explore at each node
            num_samples_per_branch: Number of samples to generate per branch
            custom_evaluation_fn: Optional custom evaluation function
            stop_tokens: Optional tokens to stop generation
            custom_prompt_template: Optional custom prompt template
            **kwargs: Additional arguments
            
        Returns:
            ReasoningResult: The result of the reasoning process
        """
        start_time = time.time()
        logger.info(f"Starting ToT-RAG reasoning for query: {query}")
        
        # Initialize the reasoning tree
        root_node = self._create_root_node(query, relevant_documents)
        
        # Perform tree exploration with integrated RAG
        final_tree = self._explore_tree(
            root_node=root_node,
            query=query,
            max_depth=max_depth,
            max_branches=max_branches,
            num_samples_per_branch=num_samples_per_branch,
            custom_evaluation_fn=custom_evaluation_fn,
            stop_tokens=stop_tokens,
            custom_prompt_template=custom_prompt_template,
            **kwargs
        )
        
        # Select the best path
        best_path = self._select_best_path(final_tree)
        
        # Generate the final answer
        final_answer = self._generate_final_answer(query, best_path, root_node.get('documents', []))
        
        reasoning_time = time.time() - start_time
        logger.info(f"ToT-RAG reasoning completed in {reasoning_time:.2f}s")
        
        # Prepare the reasoning result
        result = ReasoningResult(
            query=query,
            answer=final_answer,
            reasoning_tree=final_tree,
            best_path=best_path,
            metadata={
                "reasoning_time": reasoning_time,
                "method": "tot_rag",
                "max_depth": max_depth,
                "max_branches": max_branches,
                "retrieval_depth": self.retrieval_depth,
                "documents_used": self._count_documents_used(final_tree)
            }
        )
        
        return result
    
    def _create_root_node(self, query: str, relevant_documents: Optional[List[Document]] = None) -> ThoughtNode:
        """
        Create the root node of the reasoning tree.
        
        Args:
            query: The user query
            relevant_documents: Optional pre-retrieved documents
            
        Returns:
            ThoughtNode: The root node with initial documents
        """
        # Retrieve documents if not provided
        if relevant_documents is None:
            documents = self.rag_reasoner.retrieve_documents(query, limit=self.max_documents_per_node)
        else:
            documents = relevant_documents[:self.max_documents_per_node]
        
        # Create the root node
        root_node = {
            "id": "root",
            "thought": f"I need to answer the question: {query}",
            "score": 1.0,
            "depth": 0,
            "documents": documents,
            "children": []
        }
        
        return root_node
    
    def _explore_tree(
        self,
        root_node: ThoughtNode,
        query: str,
        max_depth: int,
        max_branches: int,
        num_samples_per_branch: int,
        custom_evaluation_fn: Optional[Callable],
        stop_tokens: List[str],
        custom_prompt_template: Optional[str],
        **kwargs
    ) -> ThoughtNode:
        """
        Explore the reasoning tree using both ToT and RAG capabilities.
        
        Args:
            root_node: The root node of the tree
            query: The user query
            max_depth: Maximum depth to explore
            max_branches: Maximum branches per node
            num_samples_per_branch: Samples per branch
            custom_evaluation_fn: Optional custom evaluation
            stop_tokens: Stop tokens for generation
            custom_prompt_template: Custom prompt template
            **kwargs: Additional arguments
            
        Returns:
            ThoughtNode: The fully explored tree
        """
        # Use the ToT reasoner to explore the tree
        # We'll override the branch generation and evaluation to incorporate RAG
        
        def rag_enhanced_branch_generator(node, depth):
            """Generate branches with document-enhanced context"""
            if self.verbose:
                logger.info(f"Generating branches at depth {depth} with RAG enhancement")
            
            # Construct context from documents
            docs_context = self._create_document_context(node.get('documents', []))
            
            # Get current thought path
            thought_path = self._get_thought_path(node)
            
            # Generate branches using both the thought path and document context
            branches = self.tot_reasoner._generate_branches(
                query=query,
                thought_path=thought_path,
                additional_context=docs_context,
                num_branches=max_branches,
                num_samples_per_branch=num_samples_per_branch,
                stop_tokens=stop_tokens,
                custom_prompt_template=custom_prompt_template
            )
            
            # For each branch, retrieve relevant documents if we're not too deep
            if depth < self.retrieval_depth:
                for branch in branches:
                    # Create a focused query for this specific branch
                    branch_query = f"{query} {branch['thought']}"
                    
                    # Retrieve documents specific to this branch
                    branch_docs = self.rag_reasoner.retrieve_documents(
                        branch_query,
                        limit=self.max_documents_per_node
                    )
                    
                    # Store the documents with the branch
                    branch['documents'] = branch_docs
            else:
                # Beyond retrieval depth, inherit documents from parent
                for branch in branches:
                    branch['documents'] = node.get('documents', [])
            
            return branches
        
        def rag_enhanced_evaluator(node):
            """Evaluate branches considering both reasoning quality and evidence support"""
            if self.verbose:
                logger.info(f"Evaluating branch at depth {node['depth']} with RAG enhancement")
            
            # Get basic reasoning quality from ToT evaluator
            reasoning_quality = self.tot_reasoner._evaluate_branch(node)
            
            # Calculate evidence support
            evidence_support = self._calculate_evidence_support(node, query)
            
            # Calculate coherence with overall reasoning
            coherence = self._calculate_coherence(node, root_node)
            
            # Weighted average of scores
            final_score = (
                self.evaluation_weights["reasoning_quality"] * reasoning_quality +
                self.evaluation_weights["evidence_support"] * evidence_support +
                self.evaluation_weights["coherence"] * coherence
            )
            
            if self.verbose:
                logger.info(f"Branch score: {final_score:.4f} (reasoning: {reasoning_quality:.4f}, "
                            f"evidence: {evidence_support:.4f}, coherence: {coherence:.4f})")
            
            return final_score
        
        # Explore the tree with our custom functions
        return self.tot_reasoner._explore_tree(
            root_node=root_node,
            max_depth=max_depth,
            branch_generator=rag_enhanced_branch_generator,
            branch_evaluator=rag_enhanced_evaluator if custom_evaluation_fn is None else custom_evaluation_fn,
            **kwargs
        )
    
    def _create_document_context(self, documents: List[Document]) -> str:
        """
        Create a textual context from documents.
        
        Args:
            documents: List of documents
            
        Returns:
            str: Formatted document context
        """
        if not documents:
            return ""
        
        context_parts = ["Relevant information:"]
        
        for i, doc in enumerate(documents):
            context_parts.append(f"[{i+1}] {doc.content} (Source: {doc.source})")
        
        return "\n\n".join(context_parts)
    
    def _get_thought_path(self, node: ThoughtNode) -> List[str]:
        """
        Extract the thought path from the root to the current node.
        
        Args:
            node: The current node
            
        Returns:
            List[str]: The thought path
        """
        thoughts = []
        current = node
        
        # Traverse up to collect thoughts in reverse order
        while current:
            thoughts.append(current.get("thought", ""))
            current = current.get("parent")
        
        # Reverse to get correct order (root to node)
        return list(reversed(thoughts))
    
    def _calculate_evidence_support(self, node: ThoughtNode, query: str) -> float:
        """
        Calculate how well the node is supported by evidence.
        
        Args:
            node: The node to evaluate
            query: The original query
            
        Returns:
            float: Evidence support score (0-1)
        """
        documents = node.get('documents', [])
        if not documents:
            return 0.5  # Neutral score if no documents
        
        # For each document, check relevance to both query and thought
        relevance_scores = []
        thought = node.get('thought', '')
        
        for doc in documents:
            # We can use the RAG reasoner's relevance function if available
            if hasattr(self.rag_reasoner, 'calculate_relevance'):
                query_relevance = self.rag_reasoner.calculate_relevance(query, doc.content)
                thought_relevance = self.rag_reasoner.calculate_relevance(thought, doc.content)
                combined_relevance = (query_relevance + thought_relevance) / 2
                relevance_scores.append(combined_relevance)
            else:
                # Simple fallback - would be better with actual relevance calculation
                relevance_scores.append(0.7)  # Default reasonable score
        
        # Return average relevance
        return sum(relevance_scores) / len(relevance_scores) if relevance_scores else 0.5
    
    def _calculate_coherence(self, node: ThoughtNode, root_node: ThoughtNode) -> float:
        """
        Calculate coherence of this branch with the overall reasoning.
        
        Args:
            node: The current node
            root_node: The root node of the tree
            
        Returns:
            float: Coherence score (0-1)
        """
        # This would ideally use a more sophisticated coherence measure
        # For now, we'll use a simple proxy based on depth
        depth = node.get('depth', 0)
        if depth == 0:
            return 1.0
        
        # Decay slightly with depth to encourage more concise reasoning
        return max(0.7, 1.0 - (depth * 0.1))
    
    def _select_best_path(self, tree: ThoughtNode) -> List[ThoughtNode]:
        """
        Select the best path through the reasoning tree.
        
        Args:
            tree: The full reasoning tree
            
        Returns:
            List[ThoughtNode]: The best path from root to leaf
        """
        # Get all leaf nodes
        leaf_nodes = self._get_leaf_nodes(tree)
        
        if not leaf_nodes:
            return [tree]  # Only the root node
        
        # Find the leaf with the highest score
        best_leaf = max(leaf_nodes, key=lambda node: node.get('score', 0))
        
        # Trace path from the best leaf to the root
        path = []
        current = best_leaf
        
        while current:
            path.append(current)
            current = current.get('parent')
        
        # Reverse to get root-to-leaf order
        return list(reversed(path))
    
    def _get_leaf_nodes(self, node: ThoughtNode) -> List[ThoughtNode]:
        """
        Get all leaf nodes in the tree.
        
        Args:
            node: The root node
            
        Returns:
            List[ThoughtNode]: All leaf nodes
        """
        if not node.get('children', []):
            return [node]
        
        leaves = []
        for child in node.get('children', []):
            leaves.extend(self._get_leaf_nodes(child))
            
        return leaves
    
    def _generate_final_answer(self, query: str, best_path: List[ThoughtNode], initial_documents: List[Document]) -> str:
        """
        Generate the final answer based on the best reasoning path.
        
        Args:
            query: The original query
            best_path: The best reasoning path
            initial_documents: Initial documents from the root
            
        Returns:
            str: The final answer
        """
        # Collect all thoughts and documents along the path
        thoughts = [node.get('thought', '') for node in best_path if node.get('thought')]
        
        all_documents = set()
        for node in best_path:
            for doc in node.get('documents', []):
                all_documents.add((doc.content, doc.source))
        
        # Create a context from documents with citations
        document_context = []
        for i, (content, source) in enumerate(all_documents):
            document_context.append(f"[{i+1}] {content} (Source: {source})")
        
        # Create the synthesis prompt
        synthesis_prompt = f"""
        Question: {query}
        
        Reasoning process:
        {' -> '.join(thoughts)}
        
        Relevant information:
        {chr(10).join(document_context)}
        
        Based on the reasoning process and the relevant information above, 
        provide a comprehensive and well-supported answer to the question.
        Include citations to the relevant information where appropriate using the format [X].
        """
        
        # Generate the final answer
        final_answer = self.model.generate(
            prompt=synthesis_prompt,
            temperature=self.temperature,
            max_tokens=self.max_tokens,
        )
        
        return final_answer
    
    def _count_documents_used(self, tree: ThoughtNode) -> int:
        """
        Count the total number of unique documents used in the tree.
        
        Args:
            tree: The reasoning tree
            
        Returns:
            int: Number of unique documents
        """
        unique_docs = set()
        
        def collect_docs(node):
            for doc in node.get('documents', []):
                unique_docs.add((doc.content, doc.source))
                
            for child in node.get('children', []):
                collect_docs(child)
        
        collect_docs(tree)
        return len(unique_docs) 