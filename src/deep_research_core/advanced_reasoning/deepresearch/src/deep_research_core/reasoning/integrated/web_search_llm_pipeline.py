"""
WebSearchLLMPipeline for Deep Research Core.

This module provides a pipeline that integrates WebSearchAgent with LLM processing.
The pipeline follows these steps: search -> fetch -> extract -> filter -> rank -> process -> feed to LLM.
"""

import time
import re
import json
from typing import Dict, List, Any, Optional, Callable, Union, Tuple
from concurrent.futures import ThreadPoolExecutor
import numpy as np
from bs4 import BeautifulSoup

from ...agents.web_search_agent import WebSearchAgent
from ...models.base import BaseModel
from ...models.api.openrouter.provider import OpenRouterProvider
from ...models.api.openai.provider import OpenAIProvider
from ...models.api.anthropic.provider import AnthropicProvider
from ...utils.structured_logging import get_logger
from ...utils.text_processing import TextProcessor
from ...utils.smart_cache import SmartCache
from ...utils.distributed_tracing import trace_function

# Create a logger
logger = get_logger(__name__)

class WebSearchLLMPipeline:
    """
    A pipeline that integrates WebSearchAgent with LLM processing.

    This class provides a complete pipeline with the following steps:
    1. Search: Use WebSearchAgent to search for information
    2. Fetch: Fetch the full content of search results
    3. Extract: Extract relevant information from the content
    4. Filter: Filter out irrelevant or low-quality information
    5. Rank: Rank the information by relevance and quality
    6. Process: Process the information into a format suitable for LLM
    7. Feed to LLM: Send the processed information to an LLM for final answer generation
    """

    def __init__(
        self,
        web_search_agent: Optional[WebSearchAgent] = None,
        web_search_config: Optional[Dict[str, Any]] = None,
        llm_provider: str = "openrouter",
        llm_model: str = "anthropic/claude-3-opus-20240229",
        llm_api_key: Optional[str] = None,
        max_search_results: int = 10,
        max_content_length: int = 100000,
        max_context_length: int = 100000,
        relevance_threshold: float = 0.5,
        quality_threshold: float = 0.5,
        use_cache: bool = True,
        cache_ttl: int = 3600,
        verbose: bool = False
    ):
        """
        Initialize the WebSearchLLMPipeline.

        Args:
            web_search_agent: An instance of WebSearchAgent (optional)
            web_search_config: Configuration for WebSearchAgent if not provided
            llm_provider: The LLM provider to use ("openrouter", "openai", "anthropic")
            llm_model: The LLM model to use
            llm_api_key: The API key for the LLM provider
            max_search_results: Maximum number of search results to retrieve
            max_content_length: Maximum length of content to extract from each result
            max_context_length: Maximum length of context to send to LLM
            relevance_threshold: Threshold for relevance filtering
            quality_threshold: Threshold for quality filtering
            use_cache: Whether to use caching
            cache_ttl: Time-to-live for cache entries in seconds
            verbose: Whether to log detailed information
        """
        # Initialize WebSearchAgent
        if web_search_agent is None:
            if web_search_config is None:
                web_search_config = {
                    "search_method": "auto",
                    "api_search_config": {
                        "engine": "duckduckgo",
                        "language": "auto"
                    },
                    "crawlee_search_config": {
                        "max_depth": 2,
                        "max_pages": 5,
                        "timeout": 60
                    }
                }
            self.web_search = WebSearchAgent(**web_search_config)
        else:
            self.web_search = web_search_agent

        # Initialize LLM
        self.llm_provider = llm_provider
        self.llm_model = llm_model
        self.llm_api_key = llm_api_key

        if llm_provider == "openrouter":
            self.llm = OpenRouterProvider(api_key=llm_api_key, model=llm_model)
        elif llm_provider == "openai":
            self.llm = OpenAIProvider(api_key=llm_api_key, model=llm_model)
        elif llm_provider == "anthropic":
            self.llm = AnthropicProvider(api_key=llm_api_key, model=llm_model)
        else:
            raise ValueError(f"Unsupported LLM provider: {llm_provider}")

        # Initialize other parameters
        self.max_search_results = max_search_results
        self.max_content_length = max_content_length
        self.max_context_length = max_context_length
        self.relevance_threshold = relevance_threshold
        self.quality_threshold = quality_threshold
        self.verbose = verbose

        # Initialize text processor
        self.text_processor = TextProcessor()

        # Initialize cache
        self.use_cache = use_cache
        if use_cache:
            self.cache = SmartCache(
                embedding_model="all-MiniLM-L6-v2",
                max_size=1000,
                similarity_threshold=0.85,
                ttl_seconds=cache_ttl,
                use_embeddings=True
            )

        logger.info(
            "Initialized WebSearchLLMPipeline",
            llm_provider=llm_provider,
            llm_model=llm_model,
            max_search_results=max_search_results,
            max_content_length=max_content_length,
            max_context_length=max_context_length
        )

    @trace_function(name="web_search_llm_pipeline_process")
    def process(
        self,
        query: str,
        system_prompt: Optional[str] = None,
        custom_prompt_template: Optional[str] = None,
        force_refresh: bool = False,
        callback: Optional[Callable[[str], None]] = None
    ) -> Dict[str, Any]:
        """
        Process a query through the complete pipeline.

        Args:
            query: The query to process
            system_prompt: Custom system prompt for the LLM
            custom_prompt_template: Custom prompt template for the LLM
            force_refresh: Whether to bypass cache
            callback: Callback function for streaming output

        Returns:
            Dictionary containing the answer and metadata
        """
        start_time = time.time()

        # Check cache first
        if self.use_cache and not force_refresh:
            cached_result = self.cache.get(query)
            if cached_result is not None:
                logger.info("Returning cached result", query=query)
                return cached_result

        # Step 1: Search
        search_results = self._search(query)

        # Step 2: Fetch
        fetched_results = self._fetch(search_results)

        # Step 3: Extract
        extracted_info = self._extract(fetched_results, query)

        # Step 4: Filter
        filtered_info = self._filter(extracted_info, query)

        # Step 5: Rank
        ranked_info = self._rank(filtered_info, query)

        # Step 6: Process
        processed_info = self._process(ranked_info, query)

        # Step 7: Feed to LLM
        llm_response = self._feed_to_llm(processed_info, query, system_prompt, custom_prompt_template, callback)

        # Calculate latency
        latency = time.time() - start_time

        # Prepare response
        response = {
            "query": query,
            "answer": llm_response.get("content", ""),
            "sources": [item.get("url", "") for item in ranked_info[:5]],
            "search_results_count": len(search_results),
            "filtered_results_count": len(filtered_info),
            "latency": latency
        }

        # Cache the response
        if self.use_cache:
            self.cache.set(query, response)

        return response

    def _search(self, query: str) -> List[Dict[str, Any]]:
        """
        Step 1: Search for information using WebSearchAgent.

        Args:
            query: The query to search for

        Returns:
            List of search results
        """
        logger.info("Performing web search", query=query)

        search_response = self.web_search.search(
            query=query,
            num_results=self.max_search_results,
            auto_method=True
        )

        if not search_response.get("success", False):
            logger.warning("Search failed", error=search_response.get("error", "Unknown error"))
            return []

        results = search_response.get("results", [])
        logger.info("Search completed", results_count=len(results))

        return results

    def _fetch(self, search_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Step 2: Fetch the full content of search results.

        Args:
            search_results: List of search results

        Returns:
            List of search results with fetched content
        """
        logger.info("Fetching content", results_count=len(search_results))

        # Skip if no results
        if not search_results:
            return []

        # Use ThreadPoolExecutor for parallel fetching
        fetched_results = []
        with ThreadPoolExecutor(max_workers=5) as executor:
            future_to_result = {
                executor.submit(self.web_search.extract_content, result["url"]): result
                for result in search_results
                if "url" in result
            }

            for future in future_to_result:
                result = future_to_result[future]
                try:
                    content = future.result()
                    if content.get("success", False):
                        result["full_content"] = content.get("text", "")
                        result["html_content"] = content.get("html", "")
                        result["title"] = content.get("title", result.get("title", ""))
                        fetched_results.append(result)
                except Exception as e:
                    logger.warning("Error fetching content", url=result.get("url", ""), error=str(e))

        logger.info("Fetching completed", fetched_count=len(fetched_results))

        return fetched_results

    def _extract(self, fetched_results: List[Dict[str, Any]], query: str) -> List[Dict[str, Any]]:
        """
        Step 3: Extract relevant information from the content.

        Args:
            fetched_results: List of search results with fetched content
            query: The original query

        Returns:
            List of search results with extracted information
        """
        logger.info("Extracting information", results_count=len(fetched_results))

        # Skip if no results
        if not fetched_results:
            return []

        extracted_results = []
        for result in fetched_results:
            try:
                # Get content
                full_content = result.get("full_content", "")
                html_content = result.get("html_content", "")
                snippet = result.get("snippet", "")

                # Skip if no content
                if not full_content and not html_content and not snippet:
                    continue

                # Extract main content
                extracted_content = ""
                if html_content:
                    # Use BeautifulSoup to extract main content
                    soup = BeautifulSoup(html_content, "html.parser")

                    # Remove script, style, and other non-content elements
                    for tag in soup.find_all(["script", "style", "header", "footer", "nav"]):
                        tag.decompose()

                    # Try to find main content
                    main_content = soup.find("main") or soup.find("article") or soup.find("div", class_=["content", "main", "article"])

                    if main_content:
                        extracted_content = main_content.get_text(separator=" ", strip=True)
                    else:
                        # Fallback to all paragraphs
                        paragraphs = soup.find_all("p")
                        extracted_content = " ".join([p.get_text(strip=True) for p in paragraphs])

                # Use full_content if extracted_content is empty
                if not extracted_content:
                    extracted_content = full_content

                # Use snippet if extracted_content is still empty
                if not extracted_content:
                    extracted_content = snippet

                # Limit content length
                if len(extracted_content) > self.max_content_length:
                    extracted_content = extracted_content[:self.max_content_length]

                # Add to result
                result["extracted_content"] = extracted_content
                extracted_results.append(result)

            except Exception as e:
                logger.warning("Error extracting content", url=result.get("url", ""), error=str(e))

        logger.info("Extraction completed", extracted_count=len(extracted_results))

        return extracted_results

    def _filter(self, extracted_results: List[Dict[str, Any]], query: str) -> List[Dict[str, Any]]:
        """
        Step 4: Filter out irrelevant or low-quality information.

        Args:
            extracted_results: List of search results with extracted information
            query: The original query

        Returns:
            List of filtered search results
        """
        logger.info("Filtering information", results_count=len(extracted_results))

        # Skip if no results
        if not extracted_results:
            return []

        # Chuẩn bị truy vấn cho tính toán độ liên quan
        query_lower = query.lower()
        query_terms = set(self.text_processor.tokenize(query_lower))

        # Xác định ngôn ngữ của truy vấn
        is_vietnamese = any(c in 'àáảãạăắằẳẵặâấầẩẫậèéẻẽẹêếềểễệìíỉĩịòóỏõọôốồổỗộơớờởỡợùúủũụưứừửữựỳýỷỹỵđ' for c in query_lower)

        # Tạo danh sách các từ khóa quan trọng từ truy vấn
        important_keywords = []
        for term in query_terms:
            # Bỏ qua các từ dừng (stop words)
            if len(term) <= 2:
                continue

            # Từ khóa tiếng Việt thường dài hơn
            if is_vietnamese and len(term) >= 3:
                important_keywords.append(term)
            # Từ khóa tiếng Anh
            elif not is_vietnamese and len(term) >= 4:
                important_keywords.append(term)

        # Nếu không có từ khóa quan trọng, sử dụng tất cả các từ
        if not important_keywords:
            important_keywords = list(query_terms)

        filtered_results = []
        for result in extracted_results:
            try:
                # Lấy nội dung
                content = result.get("extracted_content", "")
                title = result.get("title", "")
                url = result.get("url", "")

                # Bỏ qua nếu không có nội dung
                if not content:
                    continue

                # Tính điểm liên quan dựa trên nhiều yếu tố
                content_lower = content.lower()
                title_lower = title.lower()

                # 1. Kiểm tra từ khóa quan trọng trong nội dung
                keyword_matches = 0
                for keyword in important_keywords:
                    if keyword in content_lower:
                        keyword_matches += 1

                # Tính điểm dựa trên tỷ lệ từ khóa khớp
                keyword_score = keyword_matches / len(important_keywords) if important_keywords else 0.0

                # 2. Kiểm tra cụm từ chính xác
                exact_phrase_score = 0.0
                if query_lower in content_lower:
                    exact_phrase_score = 0.5  # Điểm cao nếu cụm từ chính xác xuất hiện
                elif query_lower in title_lower:
                    exact_phrase_score = 0.3  # Điểm nếu cụm từ xuất hiện trong tiêu đề

                # 3. Kiểm tra mật độ từ khóa
                content_terms = set(self.text_processor.tokenize(content_lower))
                if content_terms and query_terms:
                    # Tính độ trùng lặp của các từ
                    overlap = len(query_terms.intersection(content_terms))
                    overlap_score = overlap / len(query_terms) if query_terms else 0.0
                else:
                    overlap_score = 0.0

                # 4. Kiểm tra vị trí xuất hiện từ khóa (ưu tiên xuất hiện sớm)
                position_score = 0.0
                first_paragraph = content_lower[:500]  # Lấy 500 ký tự đầu tiên
                for keyword in important_keywords:
                    if keyword in first_paragraph:
                        position_score += 0.1  # Cộng điểm cho mỗi từ khóa xuất hiện sớm
                position_score = min(0.3, position_score)  # Giới hạn tối đa 0.3

                # Tính điểm liên quan tổng hợp (tổng trọng số)
                relevance_score = (
                    keyword_score * 0.4 +      # 40% từ điểm từ khóa
                    exact_phrase_score * 0.3 +  # 30% từ điểm cụm từ chính xác
                    overlap_score * 0.2 +       # 20% từ điểm trùng lặp
                    position_score * 0.1        # 10% từ điểm vị trí
                )

                # Tính điểm chất lượng
                quality_score = self._calculate_quality_score(result)

                # Thêm điểm vào kết quả
                result["relevance_score"] = relevance_score
                result["quality_score"] = quality_score
                result["keyword_score"] = keyword_score
                result["exact_phrase_score"] = exact_phrase_score
                result["overlap_score"] = overlap_score
                result["position_score"] = position_score

                # Lọc theo ngưỡng liên quan và chất lượng
                # Giảm ngưỡng nếu có ít kết quả
                if len(extracted_results) < 5:
                    adjusted_relevance_threshold = self.relevance_threshold * 0.7
                    adjusted_quality_threshold = self.quality_threshold * 0.7
                else:
                    adjusted_relevance_threshold = self.relevance_threshold
                    adjusted_quality_threshold = self.quality_threshold

                if relevance_score >= adjusted_relevance_threshold or quality_score >= adjusted_quality_threshold:
                    filtered_results.append(result)

            except Exception as e:
                logger.warning("Error filtering content", url=result.get("url", ""), error=str(e))

        # Nếu không có kết quả nào vượt qua ngưỡng, lấy top 3 kết quả có điểm cao nhất
        if not filtered_results and extracted_results:
            logger.info("No results passed threshold, selecting top results based on scores")

            # Tính điểm tổng hợp cho tất cả kết quả
            for result in extracted_results:
                try:
                    content = result.get("extracted_content", "")
                    if not content:
                        result["combined_score"] = 0.0
                        continue

                    # Tính nhanh điểm liên quan đơn giản
                    content_lower = content.lower()
                    matches = sum(1 for term in query_terms if term in content_lower)
                    simple_relevance = matches / len(query_terms) if query_terms else 0.0

                    # Tính điểm chất lượng
                    simple_quality = min(1.0, len(content) / 2000)

                    # Điểm tổng hợp
                    result["combined_score"] = simple_relevance * 0.7 + simple_quality * 0.3
                except:
                    result["combined_score"] = 0.0

            # Sắp xếp theo điểm tổng hợp và lấy top 3
            sorted_results = sorted(extracted_results, key=lambda x: x.get("combined_score", 0.0), reverse=True)
            filtered_results = sorted_results[:3]

        logger.info("Filtering completed", filtered_count=len(filtered_results))

        return filtered_results

    def _calculate_quality_score(self, result: Dict[str, Any]) -> float:
        """
        Calculate quality score for a result.

        Args:
            result: Search result

        Returns:
            Quality score between 0.0 and 1.0
        """
        score = 0.0

        # Content length (0.3)
        content = result.get("extracted_content", "")
        if content:
            content_length = len(content)
            # Score based on content length, max at 2000 chars
            score += min(0.3, content_length / 6000)

        # URL quality (0.2)
        url = result.get("url", "")
        if url:
            # Prefer https
            if url.startswith("https"):
                score += 0.05

            # Prefer reliable domains
            reliable_domains = [
                ".edu", ".gov", ".org", "wikipedia.org", "github.com",
                "scholar.google.com", "arxiv.org", "researchgate.net"
            ]
            for domain in reliable_domains:
                if domain in url:
                    score += 0.15
                    break

        # Title quality (0.2)
        title = result.get("title", "")
        if title:
            # Score based on title length, max at 100 chars
            score += min(0.2, len(title) / 100)

        # Freshness (0.1)
        # Try to extract date from URL or content
        date_patterns = [
            r"/(20\d{2}/\d{1,2}/\d{1,2})/",
            r"/(20\d{2}-\d{1,2}-\d{1,2})/",
            r"published.*?(20\d{2}[-/]\d{1,2}[-/]\d{1,2})",
            r"date.*?(20\d{2}[-/]\d{1,2}[-/]\d{1,2})"
        ]

        for pattern in date_patterns:
            match = re.search(pattern, url + " " + content, re.IGNORECASE)
            if match:
                # Simple heuristic - newer content is better
                year_match = re.search(r"20(\d{2})", match.group(1))
                if year_match:
                    year = int(year_match.group(1))
                    current_year = int(time.strftime("%y"))
                    # Score based on recency, max for current year
                    score += min(0.1, max(0, 0.1 - 0.02 * (current_year - year)))
                break

        # Snippet quality (0.2)
        snippet = result.get("snippet", "")
        if snippet:
            # Score based on snippet length, max at 200 chars
            score += min(0.2, len(snippet) / 200)

        return min(1.0, score)

    def _rank(self, filtered_results: List[Dict[str, Any]], query: str) -> List[Dict[str, Any]]:
        """
        Step 5: Rank the information by relevance and quality.

        Args:
            filtered_results: List of filtered search results
            query: The original query

        Returns:
            List of ranked search results
        """
        logger.info("Ranking information", results_count=len(filtered_results))

        # Skip if no results
        if not filtered_results:
            return []

        # Xác định ngôn ngữ của truy vấn
        is_vietnamese = any(c in 'àáảãạăắằẳẵặâấầẩẫậèéẻẽẹêếềểễệìíỉĩịòóỏõọôốồổỗộơớờởỡợùúủũụưứừửữựỳýỷỹỵđ' for c in query.lower())

        # Tính điểm tổng hợp cho mỗi kết quả
        for result in filtered_results:
            # Lấy các điểm thành phần
            relevance_score = result.get("relevance_score", 0.0)
            quality_score = result.get("quality_score", 0.0)
            keyword_score = result.get("keyword_score", 0.0)
            exact_phrase_score = result.get("exact_phrase_score", 0.0)
            overlap_score = result.get("overlap_score", 0.0)
            position_score = result.get("position_score", 0.0)

            # Điều chỉnh trọng số dựa trên ngôn ngữ
            if is_vietnamese:
                # Cho tiếng Việt, ưu tiên cao hơn cho độ chính xác của cụm từ và từ khóa
                relevance_weight = 0.7  # Tăng trọng số cho độ liên quan
                quality_weight = 0.3    # Giảm trọng số cho chất lượng
            else:
                # Cho tiếng Anh, cân bằng hơn giữa độ liên quan và chất lượng
                relevance_weight = 0.6
                quality_weight = 0.4

            # Tính điểm tổng hợp
            combined_score = (relevance_score * relevance_weight) + (quality_score * quality_weight)

            # Thêm điểm thưởng cho các yếu tố quan trọng
            # 1. Điểm thưởng cho cụm từ chính xác
            if exact_phrase_score > 0:
                combined_score += 0.1

            # 2. Điểm thưởng cho độ mới của nội dung (nếu có)
            if "timestamp" in result:
                try:
                    # Ưu tiên nội dung mới hơn
                    current_time = time.time()
                    content_time = float(result.get("timestamp", 0))
                    age_in_days = (current_time - content_time) / (24 * 3600)

                    # Nội dung trong vòng 30 ngày được cộng điểm
                    if age_in_days < 30:
                        freshness_bonus = max(0, 0.1 * (1 - age_in_days / 30))
                        combined_score += freshness_bonus
                except:
                    pass

            # 3. Điểm thưởng cho nguồn đáng tin cậy
            url = result.get("url", "").lower()
            trusted_domains = [
                ".gov", ".edu", ".org", "wikipedia.org",
                "vnexpress.net", "tuoitre.vn", "thanhnien.vn", "cafef.vn",
                "worldbank.org", "imf.org", "adb.org", "gso.gov.vn"
            ]

            for domain in trusted_domains:
                if domain in url:
                    combined_score += 0.15
                    break

            # Lưu điểm tổng hợp vào kết quả
            result["combined_score"] = min(1.0, combined_score)  # Giới hạn tối đa là 1.0

            # Thêm giải thích về điểm số để dễ debug
            result["score_explanation"] = {
                "relevance_score": relevance_score,
                "quality_score": quality_score,
                "relevance_weight": relevance_weight,
                "quality_weight": quality_weight,
                "final_score": result["combined_score"]
            }

        # Sắp xếp kết quả theo điểm tổng hợp
        ranked_results = sorted(filtered_results, key=lambda x: x.get("combined_score", 0.0), reverse=True)

        # Giới hạn số lượng kết quả trả về (tối đa 10 kết quả)
        max_results = min(10, len(ranked_results))
        ranked_results = ranked_results[:max_results]

        logger.info("Ranking completed", ranked_count=len(ranked_results))

        # Log top 3 kết quả để debug
        for i, result in enumerate(ranked_results[:3]):
            logger.info(
                f"Top result {i+1}",
                title=result.get("title", "No title")[:50],
                url=result.get("url", "No URL"),
                combined_score=result.get("combined_score", 0.0),
                relevance_score=result.get("relevance_score", 0.0),
                quality_score=result.get("quality_score", 0.0)
            )

        return ranked_results

    def _process(self, ranked_results: List[Dict[str, Any]], query: str) -> List[Dict[str, Any]]:
        """
        Step 6: Process the information into a format suitable for LLM.

        Args:
            ranked_results: List of ranked search results
            query: The original query

        Returns:
            List of processed search results
        """
        logger.info("Processing information", results_count=len(ranked_results))

        # Skip if no results
        if not ranked_results:
            return []

        processed_results = []
        for result in ranked_results:
            try:
                # Get content
                content = result.get("extracted_content", "")

                # Skip if no content
                if not content:
                    continue

                # Clean and format content
                cleaned_content = self.text_processor.clean_text(content)

                # Summarize if too long
                if len(cleaned_content) > 1000:
                    # Simple extractive summarization
                    sentences = self.text_processor.split_into_sentences(cleaned_content)
                    if len(sentences) > 10:
                        # Keep first 3 and last 2 sentences, plus 5 most relevant sentences
                        first_sentences = sentences[:3]
                        last_sentences = sentences[-2:]

                        # Find most relevant middle sentences
                        middle_sentences = sentences[3:-2]
                        if middle_sentences:
                            # Score sentences by term overlap with query
                            query_terms = set(self.text_processor.tokenize(query.lower()))
                            sentence_scores = []

                            for sentence in middle_sentences:
                                sentence_terms = set(self.text_processor.tokenize(sentence.lower()))
                                if not sentence_terms or not query_terms:
                                    score = 0.0
                                else:
                                    # Calculate term overlap
                                    overlap = len(query_terms.intersection(sentence_terms))
                                    score = overlap / len(query_terms) if query_terms else 0.0
                                sentence_scores.append((sentence, score))

                            # Sort by score and take top 5
                            sorted_sentences = sorted(sentence_scores, key=lambda x: x[1], reverse=True)
                            middle_sentences = [s[0] for s in sorted_sentences[:5]]

                        # Combine sentences
                        cleaned_content = " ".join(first_sentences + middle_sentences + last_sentences)

                # Add to result
                result["processed_content"] = cleaned_content
                processed_results.append(result)

            except Exception as e:
                logger.warning("Error processing content", url=result.get("url", ""), error=str(e))

        logger.info("Processing completed", processed_count=len(processed_results))

        return processed_results

    def _feed_to_llm(
        self,
        processed_results: List[Dict[str, Any]],
        query: str,
        system_prompt: Optional[str] = None,
        custom_prompt_template: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None
    ) -> Dict[str, Any]:
        """
        Step 7: Feed the processed information to an LLM for final answer generation.

        Args:
            processed_results: List of processed search results
            query: The original query
            system_prompt: Custom system prompt for the LLM
            custom_prompt_template: Custom prompt template for the LLM
            callback: Callback function for streaming output

        Returns:
            LLM response
        """
        logger.info("Feeding to LLM", results_count=len(processed_results))

        # Prepare context
        context = ""
        total_length = 0
        max_length = self.max_context_length

        for i, result in enumerate(processed_results):
            content = result.get("processed_content", "")
            url = result.get("url", "")
            title = result.get("title", "")

            # Skip if no content
            if not content:
                continue

            # Format context entry
            entry = f"[{i+1}] {title}\nSource: {url}\n\n{content}\n\n"

            # Check if adding this entry would exceed max length
            if total_length + len(entry) > max_length:
                # If we already have some context, stop adding more
                if total_length > 0:
                    break

                # If this is the first entry and it's too long, truncate it
                entry = f"[{i+1}] {title}\nSource: {url}\n\n{content[:max_length-100]}...\n\n"

            # Add to context
            context += entry
            total_length += len(entry)

        # Prepare system prompt
        if system_prompt is None:
            system_prompt = (
                "You are a helpful assistant that provides accurate and informative answers based on the search results provided. "
                "Use the information from the search results to answer the user's question. "
                "If the search results don't contain relevant information, acknowledge that and provide a general response. "
                "Always cite your sources by referring to the source numbers [1], [2], etc."
            )

        # Prepare user prompt
        if custom_prompt_template is None:
            user_prompt = (
                f"I need information about: {query}\n\n"
                f"Here are the search results:\n\n{context}\n\n"
                f"Based on these search results, please provide a comprehensive answer to my question. "
                f"Include relevant information from the sources and cite them using the source numbers [1], [2], etc. "
                f"If the search results don't fully answer my question, acknowledge that and provide the best answer you can."
            )
        else:
            user_prompt = custom_prompt_template.format(query=query, context=context)

        # Call LLM
        try:
            response = self.llm.generate(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                stream=callback is not None
            )

            if callback is not None:
                # Handle streaming
                for chunk in response:
                    if "content" in chunk:
                        callback(chunk["content"])

                # Return final response
                return {"content": "".join([chunk.get("content", "") for chunk in response])}
            else:
                # Return non-streaming response
                return response

        except Exception as e:
            logger.error("Error calling LLM", error=str(e))
            return {"content": f"Error generating response: {str(e)}"}
