"""
Integration of WebSearchAgent with Tree of Thought (ToT) reasoning.

This module provides a combined approach where:
- Web search is used to retrieve up-to-date information
- Tree of Thought reasoning is used to explore different reasoning paths
- Each reasoning path can trigger additional searches for specific information
"""

import time
import logging
from typing import Dict, Any, List, Optional, Union, Callable, Tuple

from ...agents.web_search_agent import WebSearchAgent
from ...reasoning.tot import TreeOfThought
from ...utils.structured_logging import get_logger
from ...utils.performance_metrics import measure_latency
from ...utils.distributed_tracing import trace_function

# Create a logger
logger = get_logger(__name__)

class WebSearchToTIntegrator:
    """
    Integration of WebSearchAgent with Tree of Thought (ToT) reasoning.
    
    This class provides a combined approach where:
    - Web search is used to retrieve up-to-date information
    - Tree of Thought reasoning is used to explore different reasoning paths
    - Each reasoning path can trigger additional searches for specific information
    """
    
    def __init__(
        self,
        tot_instance: TreeOfThought,
        web_search_agent: Optional[WebSearchAgent] = None,
        web_search_config: Optional[Dict[str, Any]] = None,
        max_search_results: int = 5,
        max_searches_per_path: int = 2,
        search_at_depth: List[int] = [0, 2],
        verbose: bool = False
    ):
        """
        Initialize the WebSearchToTIntegrator.
        
        Args:
            tot_instance: An instance of TreeOfThought
            web_search_agent: An instance of WebSearchAgent (optional)
            web_search_config: Configuration for WebSearchAgent if not provided
            max_search_results: Maximum number of search results to retrieve
            max_searches_per_path: Maximum number of searches per reasoning path
            search_at_depth: List of depths at which to perform searches
            verbose: Whether to log detailed information
        """
        self.tot = tot_instance
        
        # Initialize WebSearchAgent if not provided
        if web_search_agent is None:
            if web_search_config is None:
                web_search_config = {
                    "search_method": "api",
                    "api_search_config": {
                        "engine": "google",
                        "language": "vi"
                    }
                }
            self.web_search = WebSearchAgent(**web_search_config)
        else:
            self.web_search = web_search_agent
        
        self.max_search_results = max_search_results
        self.max_searches_per_path = max_searches_per_path
        self.search_at_depth = search_at_depth
        self.verbose = verbose
        
        # Override the evaluate_node method of ToT to include web search
        self._original_evaluate_node = self.tot._evaluate_node
        self.tot._evaluate_node = self._evaluate_node_with_search
        
        logger.info(
            "Initialized WebSearchToTIntegrator",
            max_search_results=max_search_results,
            max_searches_per_path=max_searches_per_path,
            search_at_depth=search_at_depth
        )
    
    def _evaluate_node_with_search(self, node: Dict[str, Any], depth: int, path_id: str) -> Dict[str, Any]:
        """
        Evaluate a node with web search integration.
        
        Args:
            node: The node to evaluate
            depth: The current depth
            path_id: The path ID
            
        Returns:
            The evaluated node
        """
        # Check if search should be performed at this depth
        if depth in self.search_at_depth:
            # Extract search queries from the node
            search_queries = self._extract_search_queries(node)
            
            # Limit the number of searches
            search_queries = search_queries[:self.max_searches_per_path]
            
            # Perform searches
            search_results = []
            for query in search_queries:
                logger.info("Performing web search in ToT", query=query, depth=depth, path_id=path_id)
                search_response = self.web_search.search(query, max_results=self.max_search_results)
                
                if search_response.get("success", False):
                    results = search_response.get("results", [])
                    search_results.append({
                        "query": query,
                        "results": results
                    })
            
            # Add search results to the node
            if search_results:
                node["search_results"] = search_results
                
                # Add search results to the node content
                search_context = "Information from web search:\n"
                for search in search_results:
                    search_context += f"Query: {search['query']}\n"
                    for i, result in enumerate(search['results']):
                        search_context += f"{i+1}. {result.get('title', '')}: {result.get('snippet', '')}\n"
                        search_context += f"   Source: {result.get('url', '')}\n\n"
                
                node["content"] += f"\n\n{search_context}"
        
        # Call the original evaluate_node method
        return self._original_evaluate_node(node, depth, path_id)
    
    def _extract_search_queries(self, node: Dict[str, Any]) -> List[str]:
        """
        Extract search queries from a node.
        
        Args:
            node: The node to extract queries from
            
        Returns:
            List of search queries
        """
        # Extract search queries from the node content
        content = node.get("content", "")
        
        # Look for patterns like "I need to search for X" or "Let me search for X"
        search_queries = []
        
        # Simple pattern matching for search queries
        search_patterns = [
            "search for",
            "look up",
            "find information about",
            "need to know about",
            "tìm kiếm",
            "tra cứu",
            "tìm thông tin về"
        ]
        
        lines = content.split("\n")
        for line in lines:
            for pattern in search_patterns:
                if pattern in line.lower():
                    # Extract the query after the pattern
                    query = line.lower().split(pattern)[1].strip()
                    # Remove punctuation at the end
                    if query and query[-1] in ".,:;?!":
                        query = query[:-1]
                    if query:
                        search_queries.append(query)
        
        return search_queries
    
    @trace_function(name="web_search_tot_process")
    @measure_latency("web_search_tot_process")
    def process(
        self,
        query: str,
        callback: Optional[Callable[[str], None]] = None
    ) -> Dict[str, Any]:
        """
        Process a query using both web search and ToT.
        
        Args:
            query: The query to process
            callback: Callback function for streaming output
            
        Returns:
            Dictionary containing the answer and metadata
        """
        start_time = time.time()
        
        # Perform initial search
        initial_search_response = self.web_search.search(query, max_results=self.max_search_results)
        initial_search_results = []
        
        if initial_search_response.get("success", False):
            initial_search_results = initial_search_response.get("results", [])
        
        # Prepare initial context with search results
        initial_context = ""
        if initial_search_results:
            initial_context += "Information from web search:\n"
            for i, result in enumerate(initial_search_results):
                initial_context += f"{i+1}. {result.get('title', '')}: {result.get('snippet', '')}\n"
                initial_context += f"   Source: {result.get('url', '')}\n\n"
        
        # Process with ToT
        tot_response = self.tot.reason(
            query=query,
            initial_context=initial_context,
            callback=callback
        )
        
        # Calculate latency
        latency = time.time() - start_time
        
        # Prepare response
        response = {
            "query": query,
            "answer": tot_response.get("answer", ""),
            "initial_search_results": initial_search_results,
            "explored_paths": tot_response.get("explored_paths", 0),
            "max_depth": tot_response.get("max_depth", 0),
            "latency": latency
        }
        
        return response
