"""
Integration of WebSearchAgent with RAG capabilities.

This module provides a combined approach where:
- Web search is used to retrieve up-to-date information
- Retrieved information is stored in a vector database
- RAG is used to generate answers based on both stored and retrieved information
"""

import time
import logging
from typing import Dict, Any, List, Optional, Union, Callable, Tuple

from ...agents.web_search_agent import WebSearchAgent
from ...reasoning.sqlite_vector_rag import SQLiteVectorRAG
from ...utils.structured_logging import get_logger
from ...utils.performance_metrics import measure_latency
from ...utils.distributed_tracing import trace_function

# Create a logger
logger = get_logger(__name__)

class WebSearchRAGIntegrator:
    """
    Integration of WebSearchAgent with RAG capabilities.
    
    This class provides a combined approach where:
    - Web search is used to retrieve up-to-date information
    - Retrieved information is stored in a vector database
    - RAG is used to generate answers based on both stored and retrieved information
    """
    
    def __init__(
        self,
        rag_instance: SQLiteVectorRAG,
        web_search_agent: Optional[WebSearchAgent] = None,
        web_search_config: Optional[Dict[str, Any]] = None,
        auto_store_results: bool = True,
        max_search_results: int = 5,
        max_documents_per_query: int = 10,
        search_weight: float = 0.5,
        rag_weight: float = 0.5,
        use_hybrid_search: bool = True,
        hybrid_weight: float = 0.7,
        verbose: bool = False
    ):
        """
        Initialize the WebSearchRAGIntegrator.
        
        Args:
            rag_instance: An instance of SQLiteVectorRAG
            web_search_agent: An instance of WebSearchAgent (optional)
            web_search_config: Configuration for WebSearchAgent if not provided
            auto_store_results: Whether to automatically store search results in RAG
            max_search_results: Maximum number of search results to retrieve
            max_documents_per_query: Maximum number of documents to retrieve from RAG
            search_weight: Weight for search results in the final answer
            rag_weight: Weight for RAG results in the final answer
            use_hybrid_search: Whether to use hybrid search in RAG
            hybrid_weight: Weight for hybrid search in RAG
            verbose: Whether to log detailed information
        """
        self.rag = rag_instance
        
        # Initialize WebSearchAgent if not provided
        if web_search_agent is None:
            if web_search_config is None:
                web_search_config = {
                    "search_method": "api",
                    "api_search_config": {
                        "engine": "google",
                        "language": "vi"
                    }
                }
            self.web_search = WebSearchAgent(**web_search_config)
        else:
            self.web_search = web_search_agent
        
        self.auto_store_results = auto_store_results
        self.max_search_results = max_search_results
        self.max_documents_per_query = max_documents_per_query
        self.search_weight = search_weight
        self.rag_weight = rag_weight
        self.use_hybrid_search = use_hybrid_search
        self.hybrid_weight = hybrid_weight
        self.verbose = verbose
        
        logger.info(
            "Initialized WebSearchRAGIntegrator",
            auto_store_results=auto_store_results,
            max_search_results=max_search_results,
            max_documents_per_query=max_documents_per_query,
            search_weight=search_weight,
            rag_weight=rag_weight,
            use_hybrid_search=use_hybrid_search,
            hybrid_weight=hybrid_weight
        )
    
    @trace_function(name="web_search_rag_process")
    @measure_latency("web_search_rag_process")
    def process(
        self,
        query: str,
        force_search: bool = False,
        filter_expr: Optional[str] = None,
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None
    ) -> Dict[str, Any]:
        """
        Process a query using both web search and RAG.
        
        Args:
            query: The query to process
            force_search: Whether to force web search even if RAG has results
            filter_expr: Filter expression for RAG search
            custom_system_prompt: Custom system prompt for RAG
            custom_user_prompt: Custom user prompt for RAG
            callback: Callback function for streaming output
            
        Returns:
            Dictionary containing the answer and metadata
        """
        start_time = time.time()
        
        # Step 1: Search RAG for existing information
        rag_results = self.rag.search(
            query=query,
            top_k=self.max_documents_per_query,
            filter_expr=filter_expr,
            hybrid_weight=self.hybrid_weight if self.use_hybrid_search else None
        )
        
        # Step 2: Determine if web search is needed
        need_search = force_search or len(rag_results) < 2
        
        # Step 3: Perform web search if needed
        search_results = []
        if need_search:
            logger.info("Performing web search", query=query, force_search=force_search)
            search_response = self.web_search.search(query, max_results=self.max_search_results)
            
            if search_response.get("success", False):
                search_results = search_response.get("results", [])
                
                # Store search results in RAG if auto_store_results is enabled
                if self.auto_store_results and search_results:
                    documents = []
                    for result in search_results:
                        documents.append({
                            "content": result.get("snippet", ""),
                            "source": result.get("url", ""),
                            "title": result.get("title", ""),
                            "date": time.strftime("%Y-%m-%d")
                        })
                    
                    self.rag.add_documents(documents)
                    logger.info("Stored search results in RAG", count=len(documents))
        
        # Step 4: Combine RAG and search results
        combined_context = ""
        
        # Add RAG results
        if rag_results:
            combined_context += "Information from database:\n"
            for i, result in enumerate(rag_results):
                combined_context += f"{i+1}. {result.get('content', '')}\n"
                combined_context += f"   Source: {result.get('source', '')}\n\n"
        
        # Add search results
        if search_results:
            combined_context += "Information from web search:\n"
            for i, result in enumerate(search_results):
                combined_context += f"{i+1}. {result.get('snippet', '')}\n"
                combined_context += f"   Source: {result.get('url', '')}\n\n"
        
        # Step 5: Generate answer using RAG with combined context
        if not custom_system_prompt:
            custom_system_prompt = """You are a helpful assistant that provides accurate and comprehensive answers based on the provided information.
Use the information from both the database and web search results to provide the most up-to-date and accurate answer.
Always cite your sources when providing information."""
        
        if not custom_user_prompt:
            custom_user_prompt = """Please answer the following question based on the provided information:
Question: {query}

Context:
{context}

Provide a comprehensive answer that combines information from all sources. Cite sources where appropriate."""
        
        # Process with RAG
        rag_response = self.rag.process(
            query=query,
            custom_system_prompt=custom_system_prompt,
            custom_user_prompt=custom_user_prompt.format(query=query, context=combined_context),
            callback=callback
        )
        
        # Calculate latency
        latency = time.time() - start_time
        
        # Prepare response
        response = {
            "query": query,
            "answer": rag_response.get("answer", ""),
            "rag_results": rag_results,
            "search_results": search_results,
            "latency": latency,
            "search_performed": need_search,
            "combined_context": combined_context
        }
        
        return response
