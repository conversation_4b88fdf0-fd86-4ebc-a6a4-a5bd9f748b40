# Integrated Reasoning Components

This directory contains implementations of integrated reasoning approaches that combine multiple reasoning techniques to enhance the capabilities and performance of the Deep Research Core.

## ToT-RAG Integration (`tot_rag_integration.py`)

The ToT-RAG Integration combines Tree of Thought (ToT) reasoning with Retrieval-Augmented Generation (RAG) for enhanced reasoning capabilities. This integration allows:

1. **Document retrieval at each node** of the reasoning tree
2. **Context-aware exploration** of the reasoning space
3. **Evidence-backed branching** and evaluation

### Key Features

- **Document-enhanced reasoning**: Each branch of thinking has access to relevant documents
- **Contextualized reasoning paths**: Document retrieval is performed at strategic points in the reasoning tree
- **Evidence-weighted evaluation**: Branches are evaluated based on reasoning quality and evidence support
- **Multi-criteria scoring**: Uses weighted evaluation of reasoning quality, evidence support, and coherence
- **Vietnamese language support**: Compatible with Vietnamese text retrieval and reasoning

### Example Usage

```python
from deep_research_core.models.base import BaseModel
from deep_research_core.reasoning.tot.implementation import TreeOfThought
from deep_research_core.reasoning.rag.implementation import RAGReasoner
from deep_research_core.reasoning.integrated.tot_rag_integration import ToTRAGIntegrator
from deep_research_core.utils.types import Document

# Initialize model and reasoners
model = setup_model()  # Your implementation of BaseModel
rag_reasoner = RAGReasoner(model=model)
tot_reasoner = TreeOfThought(provider="openai", model="gpt-4")

# Create the integrator
tot_rag_integrator = ToTRAGIntegrator(
    model=model,
    tot_reasoner=tot_reasoner,
    rag_reasoner=rag_reasoner,
    retrieval_depth=2,
    max_documents_per_node=3,
    verbose=True
)

# Perform reasoning
result = tot_rag_integrator.reason(
    query="What are the environmental impacts of renewable energy?",
    max_depth=3,
    max_branches=3
)

# Access the results
print(f"Answer: {result.answer}")
print(f"Reasoning time: {result.metadata.get('reasoning_time'):.2f} seconds")
print(f"Documents used: {result.metadata.get('documents_used')}")
```

For a full working example, see `examples/tot_rag_example.py`.

### Customization

The ToT-RAG integrator supports customization through various parameters:

- **Evaluation weights**: Adjust the importance of reasoning quality, evidence support, and coherence
- **Retrieval depth**: Control how deep in the tree document retrieval occurs
- **Document limits**: Set the maximum number of documents per node
- **Custom evaluation**: Provide custom evaluation functions

### Benefits

- **Enhanced reasoning quality** through evidence-backed thoughts
- **Better contextualized exploration** of the reasoning space
- **More reliable answers** with supporting evidence
- **Improved reasoning under uncertainty** by incorporating evidence at each step
- **Reduced hallucination** through evidence checking 