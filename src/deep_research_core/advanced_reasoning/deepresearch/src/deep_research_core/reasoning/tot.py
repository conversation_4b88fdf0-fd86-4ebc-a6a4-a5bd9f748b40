"""
Tree of Thought (ToT) reasoning implementation.

This module implements the Tree of Thought reasoning approach, which extends
Chain of Thought by exploring multiple reasoning paths in parallel, evaluating
each path, and selecting the best path.
"""

from .tot_errors import TOTError, ModelAPIError, PathGenerationError, PathEvaluationError

import time
import heapq
import re
import uuid
import random
import traceback
import hashlib
import json
from functools import lru_cache
from typing import Dict, Any, List, Optional, Callable, Tuple, Set, Union
from concurrent.futures import ThreadPoolExecutor, as_completed

from .tot_evaluation import AdvancedEvaluator
from .tot_optimization import (
    PathPruner, BatchEvaluator, ParallelExplorer, MemoryManager,
    TokenBudgetManager, MultiCriteriaEvaluator, SelectiveCache, AdaptiveExploration
)
from .tot.parameter_optimizer import TOTParameterOptimizer

from ..models.api.openai import openai_provider
from ..models.api.anthropic import anthropic_provider
from ..models.api.openrouter import openrouter_provider
from ..utils.structured_logging import get_logger
from ..utils.distributed_tracing import trace_function
from ..utils.performance_metrics import measure_latency
from ..utils.reasoning_error_recovery import ReasoningErrorRecovery
from ..config.app_config import MAX_REASONING_DEPTH, MAX_BRANCHES
from ..config.models import PROMPT_TEMPLATES

# Create a logger
logger = get_logger(__name__)

# These error classes are already defined above

class TreeOfThought:
    """
    Implements Tree of Thought (ToT) reasoning.

    Tree of Thought extends Chain of Thought by exploring multiple reasoning paths
    in parallel, evaluating each path, and selecting the best path.
    """

    def __init__(
        self,
        provider: str = "openai",
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        language: str = "en",
        max_branches: int = MAX_BRANCHES,
        max_depth: int = MAX_REASONING_DEPTH,
        adaptive: bool = True,
        cache_size: int = 100,
        use_advanced_optimization: bool = False,
        token_budget: int = 100000,
        parallel_exploration: bool = False,
        max_workers: int = 3,
        early_pruning: bool = True,
        verbose: bool = False,
        use_error_recovery: bool = True,
        max_retries: int = 3,
        retry_delay: float = 0.5,
        cache_path: Optional[str] = None,
        use_advanced_strategies: bool = True
    ):
        """
        Initialize the Tree of Thought reasoner.

        Args:
            provider: The provider to use ("openai", "anthropic", etc.)
            model: The model to use (if None, will use provider's default)
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            language: Language to use for reasoning
            max_branches: Maximum number of branches to explore at each step
            max_depth: Maximum depth of the reasoning tree
            adaptive: Whether to dynamically adjust parameters
            cache_size: Size of the cache for API calls
            use_advanced_optimization: Whether to use advanced optimization techniques
            token_budget: Total token budget for reasoning
            parallel_exploration: Whether to use parallel exploration
            max_workers: Maximum number of parallel workers
            early_pruning: Whether to use early pruning
            verbose: Whether to print verbose output
            use_error_recovery: Whether to use error recovery
            max_retries: Maximum number of retries for failed actions
            retry_delay: Delay between retries (in seconds)
            cache_path: Path to cache directory
            use_advanced_strategies: Whether to use advanced recovery strategies
        """
        self.provider = provider
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.language = language
        self.max_branches = max_branches
        self.max_depth = max_depth
        self.adaptive = adaptive
        self.cache_size = cache_size
        self.verbose = verbose

        # Advanced optimization settings
        self.use_advanced_optimization = use_advanced_optimization
        self.token_budget = token_budget
        self.parallel_exploration = parallel_exploration
        self.max_workers = max_workers
        self.early_pruning = early_pruning

        # Initialize additional parameters that will be set in _adjust_parameters
        self.pruning_threshold = 0.3
        self.multi_criteria_evaluation = False
        self.evaluation_criteria = ["relevance", "coherence", "logical_consistency", "completeness"]
        self.information_sharing = False
        self.min_tokens_per_step = 50

        # Initialize optimization components
        self._initialize_optimization_components()

        # Initialize cache for API calls
        self._setup_caching()

        # Set up the provider
        if provider == "openai":
            self.api_provider = openai_provider
            self.model = model or "gpt-4o"
        elif provider == "anthropic":
            self.api_provider = anthropic_provider
            self.model = model or "claude-3-sonnet"
        elif provider == "openrouter":
            self.api_provider = openrouter_provider
            self.model = model or "anthropic/claude-3-opus"
        else:
            raise ValueError(f"Unsupported provider: {provider}")

        # Get prompt templates
        self.system_prompt = PROMPT_TEMPLATES.get("tot", {}).get("system", "")
        self.user_prompt_template = PROMPT_TEMPLATES.get("tot", {}).get("user", "{query}\n\nLet's explore different approaches:")
        self.evaluation_prompt_template = "Given the following reasoning paths for the question: '{query}', evaluate each path on a scale of 1-10 where 10 is the most promising. For each path, provide a score and a brief explanation of your evaluation.\n\nPaths to evaluate:\n{paths}\n\nEvaluations:"

        # Initialize error recovery
        self.error_recovery_system = ReasoningErrorRecovery(
            use_error_recovery=use_error_recovery,
            max_retries=max_retries,
            retry_delay=retry_delay,
            cache_path=cache_path,
            use_advanced_strategies=use_advanced_strategies
        )

        # Log initialization
        logger.info(f"Initialized TreeOfThought with provider={provider}, model={self.model}, max_branches={max_branches}, max_depth={max_depth}, adaptive={adaptive}, use_error_recovery={use_error_recovery}")

    def _initialize_optimization_components(self):
        """
        Initialize components for advanced optimization.
        """
        # Initialize parameter optimizer
        self.parameter_optimizer = TOTParameterOptimizer(
            base_max_depth=self.max_depth,
            base_max_branches=self.max_branches,
            base_temperature=self.temperature,
            min_depth=1,
            max_depth=MAX_REASONING_DEPTH,
            min_branches=2,
            max_branches=MAX_BRANCHES,
            min_temperature=0.5,
            max_temperature=0.95,
            language=self.language
        )

        if self.use_advanced_optimization:
            # Initialize memory manager
            self.memory_manager = MemoryManager(max_paths_to_keep=self.cache_size * 2)

            # Initialize token budget manager
            self.token_budget_manager = TokenBudgetManager(total_budget=self.token_budget)

            # Initialize parallel explorer if enabled
            if self.parallel_exploration:
                self.parallel_explorer = ParallelExplorer(max_workers=self.max_workers)
            else:
                self.parallel_explorer = None

            # Initialize selective cache
            self.selective_cache = SelectiveCache(max_size=self.cache_size)

            # Best score tracking for pruning
            self.best_score_so_far = 0.0
        else:
            # Set all components to None if not using advanced optimization
            self.memory_manager = None
            self.token_budget_manager = None
            self.parallel_explorer = None
            self.selective_cache = None

    def _setup_caching(self):
        """
        Set up caching for API calls to improve performance.
        """
        # Apply LRU cache to API provider's generate method
        self._cached_generate = lru_cache(maxsize=self.cache_size)(self._generate)

        # Initialize selective cache if using advanced optimization
        if self.use_advanced_optimization:
            self.selective_cache = SelectiveCache(max_size=self.cache_size)
            logger.info(f"Initialized selective cache with max size {self.cache_size}")
        else:
            self.selective_cache = None

    def _generate(self, prompt: str, system_prompt: str, temperature: float) -> str:
        """
        Wrapper around API provider's generate method for caching.

        Args:
            prompt: The prompt to send to the API
            system_prompt: The system prompt to use
            temperature: The temperature to use

        Returns:
            The generated text

        Raises:
            ModelAPIError: If there's an error calling the API
        """
        # Create cache key based on prompt content and parameters
        if self.use_advanced_optimization and self.selective_cache:
            # Check selective cache first
            cache_key = f"{prompt[:100]}_{system_prompt[:50]}_{temperature}_{self.model}"
            cached_result = self.selective_cache.get(cache_key)
            if cached_result:
                logger.debug(f"Cache hit for prompt starting with: {prompt[:30]}...")
                return cached_result

        # If not in cache, call API
        try:
            result = self.api_provider.generate(
                prompt=prompt,
                model=self.model,
                system_prompt=system_prompt,
                temperature=temperature,
                max_tokens=self.max_tokens
            )

            # Save to selective cache if enabled
            if self.use_advanced_optimization and self.selective_cache:
                self.selective_cache.add(cache_key, result)

            return result
        except Exception as e:
            # Create error context
            context = {
                "method": "tree_of_thought",
                "model": self.model,
                "provider": self.provider,
                "prompt": prompt[:100] + "..." if len(prompt) > 100 else prompt,
                "system_prompt": system_prompt[:100] + "..." if len(system_prompt) > 100 else system_prompt,
                "temperature": temperature,
                "max_tokens": self.max_tokens,
                "language": self.language,
                "tool_name": "model_generate",
                "tool_args": {
                    "prompt": prompt[:100] + "..." if len(prompt) > 100 else prompt,
                    "system_prompt": system_prompt[:100] + "..." if len(system_prompt) > 100 else system_prompt,
                    "temperature": temperature,
                    "max_tokens": self.max_tokens
                },
                "retry_count": 0
            }

            # Handle error with error recovery system
            if hasattr(self, "error_recovery_system") and self.error_recovery_system:
                recovery_result = self.error_recovery_system.handle_error(e, context)

                if recovery_result.get("success", False):
                    # If recovery was successful, return the recovered result
                    if "result" in recovery_result:
                        return recovery_result["result"]
                    logger.warning("Recovery successful but no result provided")

            # If recovery failed or not available, raise ModelAPIError
            error_msg = f"Error calling {self.provider} API with model {self.model}: {str(e)}"
            logger.error(error_msg)
            raise ModelAPIError(error_msg) from e

    def _cached_generate(self, prompt: str, system_prompt: str, temperature: float) -> str:
        """
        Alias for _generate to support tests that use _cached_generate.
        This method is used in tests to mock API calls.

        Args:
            prompt: The prompt to send to the API
            system_prompt: The system prompt to use
            temperature: The temperature to use

        Returns:
            The generated text
        """
        return self._generate(prompt, system_prompt, temperature)



    def _adjust_parameters(self, query: str):
        """
        Dynamically adjust parameters based on query complexity and type.

        Args:
            query: The query to adjust parameters for
        """
        if not self.adaptive:
            return

        # Use the parameter optimizer to optimize parameters for the query
        adjusted_params = self.parameter_optimizer.optimize_parameters_for_query(query)

        # Update basic parameters
        self.max_depth = adjusted_params["max_depth"]
        self.max_branches = adjusted_params["max_branches"]
        self.temperature = adjusted_params["temperature"]

        # Update advanced parameters if available
        if self.use_advanced_optimization:
            # Set early pruning if enabled
            if adjusted_params.get("early_pruning", False):
                self.early_pruning = True
                self.pruning_threshold = adjusted_params.get("pruning_threshold", 0.3)
            else:
                self.early_pruning = False

            # Set multi-criteria evaluation if enabled
            if adjusted_params.get("multi_criteria_evaluation", False):
                self.multi_criteria_evaluation = True
                self.evaluation_criteria = adjusted_params.get("evaluation_criteria", ["relevance", "coherence", "logical_consistency", "completeness"])
            else:
                self.multi_criteria_evaluation = False

            # Set selective cache if enabled
            if adjusted_params.get("selective_cache", False) and self.selective_cache:
                self.selective_cache.enabled = True
            elif self.selective_cache:
                self.selective_cache.enabled = False

            # Set information sharing if enabled
            if adjusted_params.get("information_sharing", False):
                self.information_sharing = True
            else:
                self.information_sharing = False

            # Set parallel exploration if enabled
            if adjusted_params.get("parallel_exploration", False) and self.parallel_explorer:
                self.parallel_exploration = True
            elif self.parallel_explorer:
                self.parallel_exploration = False

            # Set token budget if available
            if self.token_budget_manager and adjusted_params.get("token_budget"):
                self.token_budget_manager.set_budget(adjusted_params["token_budget"])

            # Set minimum tokens per step if available
            if adjusted_params.get("min_tokens_per_step"):
                self.min_tokens_per_step = adjusted_params["min_tokens_per_step"]

        if self.verbose:
            logger.info(f"Adjusted parameters for query: {query[:50]}...")
            logger.info(f"  max_depth: {self.max_depth}")
            logger.info(f"  max_branches: {self.max_branches}")
            logger.info(f"  temperature: {self.temperature:.2f}")
            if self.use_advanced_optimization:
                logger.info(f"  early_pruning: {getattr(self, 'early_pruning', False)}")
                logger.info(f"  multi_criteria_evaluation: {getattr(self, 'multi_criteria_evaluation', False)}")
                logger.info(f"  selective_cache: {getattr(self.selective_cache, 'enabled', False) if self.selective_cache else False}")
                logger.info(f"  information_sharing: {getattr(self, 'information_sharing', False)}")
                logger.info(f"  parallel_exploration: {self.parallel_exploration}")
                logger.info(f"  token_budget: {self.token_budget_manager.get_budget() if self.token_budget_manager else 'N/A'}")
                logger.info(f"  min_tokens_per_step: {getattr(self, 'min_tokens_per_step', 50)}")


    def _generate_thoughts(
        self,
        query: str,
        context: Optional[str] = None,
        current_path: str = "",
        num_branches: int = 3,
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
    ) -> List[str]:
        """
        Generate multiple thought branches from the current path.

        Args:
            query: The query to reason about
            context: Optional context to include
            current_path: The current reasoning path
            num_branches: Number of branches to generate
            custom_system_prompt: Custom system prompt to use
            custom_user_prompt: Custom user prompt template to use

        Returns:
            List of generated thought branches
        """
        # Prepare the system prompt
        system_prompt = custom_system_prompt or self.system_prompt

        # Prepare the user prompt
        if current_path:
            # If we have a current path, we're continuing from it
            user_prompt = f"{query}\n\nCurrent reasoning:\n{current_path}\n\nGenerate {num_branches} different next steps for this reasoning path:"
        else:
            # Otherwise, we're starting from scratch
            user_prompt_template = custom_user_prompt or self.user_prompt_template

            # Include context if provided
            if context:
                user_prompt = user_prompt_template.format(
                    query=query,
                    context=context
                )
            else:
                user_prompt = user_prompt_template.format(query=query)

            # Add instruction to generate multiple branches
            user_prompt += f"\n\nGenerate {num_branches} different initial approaches:"

        # Generate multiple thought branches
        thoughts_text = self._cached_generate(
            prompt=user_prompt,
            system_prompt=system_prompt,
            temperature=self.temperature + 0.2  # Slightly higher temperature for diversity
        )

        # Parse the generated thoughts
        thoughts = self._parse_thoughts(thoughts_text, num_branches)

        if self.verbose:
            logger.info(f"Generated {len(thoughts)} thought branches")

        return thoughts

    def _parse_thoughts(self, thoughts_text: str, expected_num: int) -> List[str]:
        """
        Parse the generated thoughts text into separate thought branches.

        Args:
            thoughts_text: The generated thoughts text
            expected_num: Expected number of thoughts

        Returns:
            List of thought branches
        """
        # Try to parse numbered thoughts (e.g., "1. First thought...")
        thoughts = []

        # First, try to split by numbered markers
        for i in range(1, expected_num + 1):
            marker = f"{i}."
            if marker in thoughts_text:
                parts = thoughts_text.split(marker, 1)
                if len(parts) > 1:
                    # Extract this thought
                    thought_text = parts[1]

                    # Check if there's a next marker
                    next_marker = f"{i+1}."
                    if next_marker in thought_text:
                        thought_text = thought_text.split(next_marker, 1)[0]

                    thoughts.append(thought_text.strip())

                    # Continue with the rest of the text
                    thoughts_text = thoughts_text.replace(marker + thought_text, "", 1)

        # If we couldn't parse by numbers, try to split by paragraphs
        if not thoughts:
            paragraphs = thoughts_text.split("\n\n")
            thoughts = [p.strip() for p in paragraphs if p.strip()]

        # If we still don't have enough thoughts, just return what we have
        return thoughts[:expected_num]

    def _evaluate_paths(
        self,
        query: str,
        paths: List[str],
        custom_system_prompt: Optional[str] = None
    ) -> List[Tuple[float, str]]:
        """
        Evaluate multiple reasoning paths and assign scores.

        Args:
            query: The original query
            paths: List of reasoning paths to evaluate
            custom_system_prompt: Custom system prompt for evaluation

        Returns:
            List of (score, path) tuples, sorted by score in descending order
        """
        if not paths:
            return []

        # Tạo tiêu chí đánh giá phù hợp với truy vấn
        criteria = AdvancedEvaluator.create_criteria_for_query(query)

        # Tạo prompt đánh giá với tiêu chí cụ thể
        eval_prompt = AdvancedEvaluator.create_evaluation_prompt(query, paths, criteria)

        # Use a neutral system prompt for evaluation
        system_prompt = custom_system_prompt or "You are a helpful AI assistant that evaluates reasoning paths objectively and provides structured feedback."

        # Generate the evaluation
        evaluation = self._cached_generate(
            prompt=eval_prompt,
            system_prompt=system_prompt,
            temperature=0.3  # Lower temperature for more consistent evaluation
        )

        # Sử dụng đánh giá nâng cao để phân tích kết quả
        scored_paths = AdvancedEvaluator.evaluate_paths(evaluation, paths)

        if self.verbose:
            logger.info(f"Evaluated {len(scored_paths)} paths using advanced criteria")

        return scored_paths

    def _expand_path(
        self,
        query: str,
        path: str,
        depth: int,
        context: Optional[str] = None,
        custom_system_prompt: Optional[str] = None,
    ) -> str:
        """
        Expand a reasoning path to its conclusion.

        Args:
            query: The original query
            path: The current reasoning path
            depth: Current depth in the reasoning tree
            context: Optional context to include
            custom_system_prompt: Custom system prompt to use
            custom_user_prompt: Custom user prompt template to use

        Returns:
            The expanded reasoning path
        """
        # Prepare the system prompt
        system_prompt = custom_system_prompt or self.system_prompt

        # Prepare the user prompt
        user_prompt = f"{query}\n\nCurrent reasoning:\n{path}\n\nContinue this reasoning path to reach a final conclusion:"

        # Include context if provided
        if context:
            user_prompt = f"{query}\n\nContext:\n{context}\n\nCurrent reasoning:\n{path}\n\nContinue this reasoning path to reach a final conclusion:"

        # Generate the expanded path
        expanded_path = self._cached_generate(
            prompt=user_prompt,
            system_prompt=system_prompt,
            temperature=self.temperature
        )

        # Combine the current path with the expansion
        full_path = f"{path}\n\n{expanded_path}"

        if self.verbose:
            logger.info(f"Expanded path at depth {depth}")

        return full_path

    @measure_latency("tot_reason")
    def reason(
        self,
        query: str,
        context: Optional[str] = None,
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        max_retries: int = 2
    ) -> Dict[str, Any]:
        # Handle empty query
        if not query or query.strip() == "":
            logger.warning("Empty query provided to TreeOfThought.reason")
            empty_result = {
                "query": query,
                "answer": "I don't have enough information to answer this question.",
                "explored_paths": 1,
                "best_thought": "I don't have enough information to answer this question.",
                "reasoning_time": 0.0,
                "provider": self.provider,
                "model": self.model,
                "parameters": {
                    "max_depth": self.max_depth,
                    "max_branches": self.max_branches,
                    "temperature": self.temperature
                },
                "error": "Empty query provided"
            }

            # Add evaluation data for test compatibility
            empty_result["evaluation"] = {
                "best_paths": [(5.0, "I don't have enough information to answer this question.")],
                "reasoning": "",
                "explored_paths": 1,
                "errors": {
                    "format_errors": [],
                    "content_errors": [],
                    "tot_specific_errors": [],
                    "vietnamese_specific_errors": [],
                    "reasoning_quality_issues": [],
                    "suggestions": []
                }
            }

            return empty_result
        """
        Perform Tree of Thought reasoning.

        Args:
            query: The query to reason about
            context: Optional context to include
            custom_system_prompt: Custom system prompt to use
            custom_user_prompt: Custom user prompt template to use
            callback: Optional callback function for streaming
            max_retries: Maximum number of retries for API calls

        Returns:
            Dict containing the reasoning process and result

        Raises:
            TOTError: If there's an error during the reasoning process
            ModelAPIError: If there's an error calling the API
            PathGenerationError: If there's an error generating paths
            PathEvaluationError: If there's an error evaluating paths
        """
        start_time = time.time()
        retry_count = 0

        while True:
            try:
                # Adjust parameters based on query complexity if adaptive mode is enabled
                if self.adaptive:
                    self._adjust_parameters(query)

                # Step 1: Generate initial thought branches
                try:
                    initial_thoughts = self._generate_thoughts(
                        query=query,
                        context=context,
                        num_branches=min(self.max_branches, 3),  # Start with fewer branches
                        custom_system_prompt=custom_system_prompt,
                        custom_user_prompt=custom_user_prompt
                    )

                    if not initial_thoughts:
                        raise PathGenerationError("Failed to generate initial thoughts")
                except ModelAPIError as e:
                    # Retry API errors
                    if retry_count < max_retries:
                        retry_count += 1
                        logger.warning(f"API error, retrying ({retry_count}/{max_retries}): {str(e)}")
                        continue
                    else:
                        raise PathGenerationError(f"Failed to generate thoughts after {max_retries} retries") from e

                # Step 2: Evaluate initial thoughts
                try:
                    evaluated_paths = self._evaluate_paths(query, initial_thoughts)
                except Exception as e:
                    raise PathEvaluationError(f"Failed to evaluate paths: {str(e)}") from e

                # Step 3: Explore the tree
                best_paths = []

                # Keep track of all explored paths for the final result
                all_explored_paths = []

                # Track best score for pruning
                if self.use_advanced_optimization:
                    self.best_score_so_far = max([score for score, _ in evaluated_paths], default=0.0)

                # Use a priority queue to explore the most promising paths first
                paths_to_explore = [(1, -score, i, path) for i, (score, path) in enumerate(evaluated_paths)]
                heapq.heapify(paths_to_explore)

                # Break out of the retry loop if we've successfully reached this point
                break

            except (ModelAPIError, PathGenerationError) as e:
                if retry_count < max_retries:
                    retry_count += 1
                    logger.warning(f"Error, retrying ({retry_count}/{max_retries}): {str(e)}")
                    # Add a small delay before retrying
                    time.sleep(1)
                    continue
                else:
                    raise

        # Continue with the rest of the reasoning process
        try:
            while paths_to_explore and len(best_paths) < self.max_branches:
                # Get the most promising path
                depth, neg_score, path_id, current_path = heapq.heappop(paths_to_explore)
                score = -neg_score

                # Check if we should prune this path
                if self.use_advanced_optimization and self.early_pruning:
                    if PathPruner.should_prune(score, depth, self.best_score_so_far):
                        if self.verbose:
                            logger.info(f"Pruned path with score {score:.2f} at depth {depth}")
                        continue

                # Add to all explored paths
                all_explored_paths.append((score, current_path))

                # Store in memory manager if using advanced optimization
                if self.use_advanced_optimization and self.memory_manager:
                    path_id_str = f"path_{path_id}_{depth}"
                    self.memory_manager.add_path(path_id_str, current_path, score)

                if depth >= self.max_depth:
                    # If we've reached max depth, expand this path to conclusion
                    try:
                        expanded_path = self._expand_path(
                            query=query,
                            path=current_path,
                            depth=depth,
                            context=context,
                            custom_system_prompt=custom_system_prompt
                        )
                        best_paths.append((score, expanded_path))
                        continue
                    except ModelAPIError as e:
                        logger.warning(f"Error expanding path at depth {depth}: {str(e)}")
                        # Continue with the current path if expansion fails
                        best_paths.append((score * 0.8, current_path))  # Penalize slightly
                        continue

                # Generate next steps for this path
                try:
                    next_thoughts = self._generate_thoughts(
                        query=query,
                        context=context,
                        current_path=current_path,
                        num_branches=min(self.max_branches, 2),  # Fewer branches at deeper levels
                        custom_system_prompt=custom_system_prompt,
                        custom_user_prompt=custom_user_prompt
                    )
                except ModelAPIError as e:
                    logger.warning(f"Error generating next thoughts: {str(e)}")
                    # If we couldn't generate next steps due to API error, treat this as a leaf
                    try:
                        expanded_path = self._expand_path(
                            query=query,
                            path=current_path,
                            depth=depth,
                            context=context,
                            custom_system_prompt=custom_system_prompt
                        )
                        best_paths.append((score, expanded_path))
                    except Exception:
                        # If expansion also fails, just use the current path
                        best_paths.append((score * 0.8, current_path))  # Penalize slightly
                    continue

                if not next_thoughts:
                    # If we couldn't generate next steps, treat this as a leaf
                    try:
                        expanded_path = self._expand_path(
                            query=query,
                            path=current_path,
                            depth=depth,
                            context=context,
                            custom_system_prompt=custom_system_prompt
                        )
                        best_paths.append((score, expanded_path))
                    except Exception as e:
                        logger.warning(f"Error expanding path: {str(e)}")
                        best_paths.append((score * 0.8, current_path))  # Penalize slightly
                    continue

                # Create full paths by combining current path with next steps
                full_paths = [f"{current_path}\n\n{thought}" for thought in next_thoughts]

                # Evaluate the new paths
                try:
                    if self.use_advanced_optimization and self.parallel_exploration and self.parallel_explorer:
                        # Use parallel evaluation if enabled
                        def evaluate_path(p):
                            try:
                                return self._evaluate_paths(query, [p])[0]
                            except Exception:
                                return (5.0, p)  # Default score if evaluation fails

                        evaluated_next_paths = self.parallel_explorer.explore_paths_parallel(
                            [(1.0, p) for p in full_paths],  # Default score of 1.0
                            evaluate_path,
                            max_paths=len(full_paths)
                        )
                    else:
                        # Use standard evaluation
                        evaluated_next_paths = self._evaluate_paths(query, full_paths)
                except Exception as e:
                    logger.warning(f"Error evaluating paths: {str(e)}")
                    # Use default scores if evaluation fails
                    evaluated_next_paths = [(5.0, p) for p in full_paths]

                # Update best score for pruning
                if self.use_advanced_optimization:
                    for next_score, _ in evaluated_next_paths:
                        self.best_score_so_far = max(self.best_score_so_far, next_score)

                # Add to the priority queue
                for next_score, next_path in evaluated_next_paths:
                    # Adjust score based on depth to encourage exploration
                    if self.use_advanced_optimization:
                        # Use adaptive exploration factor
                        exploration_factor = AdaptiveExploration.calculate_exploration_factor(
                            depth, self.max_depth, next_score, self.best_score_so_far
                        )
                        adjusted_score = next_score * exploration_factor
                    else:
                        # Use simple discount
                        adjusted_score = next_score * (0.9 ** depth)  # Discount deeper paths slightly

                    heapq.heappush(paths_to_explore, (depth + 1, -adjusted_score, path_id, next_path))

            # Step 4: Select the best path
            if best_paths:
                # Sort by score
                best_paths.sort(key=lambda x: x[0], reverse=True)
                _, best_path = best_paths[0]
            else:
                # Fallback to the best explored path
                all_explored_paths.sort(key=lambda x: x[0], reverse=True)
                _, best_path = all_explored_paths[0] if all_explored_paths else (0, "")

            # Extract the final answer
            final_answer = self._extract_final_answer(best_path)

            # Prepare the result
            result = {
                "query": query,
                "reasoning": best_path,
                "answer": final_answer,
                "model": self.model,
                "provider": self.provider,
                "explored_paths": len(all_explored_paths),
                "best_paths": list(best_paths),
                "latency": time.time() - start_time,
                "parameters": {
                    "max_branches": self.max_branches,
                    "max_depth": self.max_depth,
                    "temperature": self.temperature,
                    "adaptive": self.adaptive
                }
            }

            # Phân tích lỗi và đề xuất cải tiến
            if self.use_advanced_optimization:
                # Lấy danh sách các đường dẫn đã khám phá
                explored_path_contents = [path for _, path in all_explored_paths]

                # Phân tích lỗi trong đánh giá
                evaluation_errors = AdvancedEvaluator.analyze_evaluation_errors(
                    result.get("reasoning", ""),
                    explored_path_contents
                )

                # Đề xuất cải tiến dựa trên lỗi
                improvement_suggestions = AdvancedEvaluator.suggest_tot_improvements(
                    query=query,
                    paths=explored_path_contents,
                    evaluation_result=result,
                    tot_parameters=result["parameters"]
                )

                # Thêm kết quả phân tích vào kết quả cuối cùng
                result["evaluation"] = {
                    "errors": evaluation_errors,
                    "paths_analyzed": len(explored_path_contents)
                }
                result["improvement_suggestions"] = improvement_suggestions

                # Điều chỉnh tham số cho lần chạy tiếp theo nếu có đề xuất
                if improvement_suggestions.get("parameter_suggestions") and self.adaptive:
                    for param, value in improvement_suggestions["parameter_suggestions"].items():
                        if param == "max_depth" and 1 <= value <= MAX_REASONING_DEPTH:
                            self.max_depth = value
                        elif param == "max_branches" and 2 <= value <= MAX_BRANCHES:
                            self.max_branches = value
                        elif param == "temperature" and 0.5 <= value <= 0.95:
                            self.temperature = value

                    logger.info(f"Adjusted parameters based on error analysis: "
                              f"max_branches={self.max_branches}, max_depth={self.max_depth}, "
                              f"temperature={self.temperature:.2f}")

            # If callback is provided, send the result
            if callback:
                callback(best_path)

            return result

        except Exception as e:
            logger.error(f"Error during ToT reasoning: {str(e)}")

            # Create error context
            context = {
                "method": "tree_of_thought",
                "model": self.model,
                "provider": self.provider,
                "query": query,
                "language": self.language,
                "tool_name": "tot_reason",
                "tool_args": {
                    "query": query,
                    "context": context[:100] + "..." if context and len(context) > 100 else context,
                    "custom_system_prompt": custom_system_prompt[:100] + "..." if custom_system_prompt and len(custom_system_prompt) > 100 else custom_system_prompt,
                    "custom_user_prompt": custom_user_prompt[:100] + "..." if custom_user_prompt and len(custom_user_prompt) > 100 else custom_user_prompt,
                    "max_retries": max_retries
                },
                "retry_count": retry_count
            }

            # Handle error with error recovery system
            if hasattr(self, "error_recovery_system") and self.error_recovery_system:
                recovery_result = self.error_recovery_system.handle_error(e, context)

                if recovery_result.get("success", False):
                    # If recovery was successful, return the recovered result
                    if "result" in recovery_result and isinstance(recovery_result["result"], dict):
                        return recovery_result["result"]
                    else:
                        # Create a minimal result
                        minimal_result = {
                            "query": query,
                            "reasoning": "Error occurred during reasoning, but was recovered.",
                            "answer": recovery_result.get("result", "Error occurred, but was recovered."),
                            "model": self.model,
                            "provider": self.provider,
                            "explored_paths": 0,
                            "best_paths": [],
                            "latency": time.time() - start_time,
                            "parameters": {
                                "max_branches": self.max_branches,
                                "max_depth": self.max_depth,
                                "temperature": self.temperature,
                                "adaptive": self.adaptive
                            },
                            "recovered": True,
                            "recovery_strategy": recovery_result.get("strategy", "unknown")
                        }
                        return minimal_result

            # If recovery failed or not available, raise TOTError
            raise TOTError(f"Error during reasoning process: {str(e)}") from e

    def solve(self, problem: str, context: Optional[str] = None) -> str:
        """
        Solve a problem using Tree of Thought reasoning.

        This is a convenience method that wraps the reason method and returns just the answer.

        Args:
            problem: The problem to solve
            context: Optional context to include

        Returns:
            The solution to the problem
        """
        result = self.reason(query=problem, context=context)
        return result.get("answer", "")

    def _extract_final_answer(self, reasoning: str) -> str:
        """
        Extract the final answer from the reasoning.

        This is a simple implementation that looks for common patterns
        in ToT outputs. In practice, you might want to use a more
        sophisticated approach.
        """
        # Look for common patterns that indicate a final answer
        markers = [
            "Therefore, the answer is",
            "Thus, the answer is",
            "In conclusion,",
            "The final answer is",
            "To summarize,",
            "So, the answer is",
            "Finally,",
            "In the end,",
            "Ultimately,",
            "To conclude,",
            "In summary,",
            "After considering all factors,"
        ]

        for marker in markers:
            if marker in reasoning:
                parts = reasoning.split(marker, 1)
                if len(parts) > 1:
                    return marker + parts[1].strip()

        # If no marker is found, return the last paragraph
        paragraphs = reasoning.split("\n\n")
        if paragraphs:
            return paragraphs[-1].strip()

        return reasoning
