"""
Knowledge Graph Integration module.

This module provides functionality to integrate knowledge graphs with the reasoning system.
It uses Weaviate as the backend for storing and querying the knowledge graph.
"""

from functools import lru_cache
from typing import Dict, Any, List, Optional, Union, Tuple, Set
import uuid
import json

from ..retrieval.weaviate_vector_store import WeaviateVectorStore
from ..utils.structured_logging import get_logger
from ..utils.performance_metrics import measure_latency
from ..utils.distributed_tracing import trace_function, span

# Create a logger
logger = get_logger(__name__)


class KnowledgeGraphIntegration:
    """
    Knowledge Graph Integration using Weaviate.

    This class provides functionality to integrate knowledge graphs with the reasoning system.
    It supports:
    - Creating and managing entities and relationships
    - Querying the knowledge graph
    - Integrating with RAG for enhanced reasoning
    - Visualizing the knowledge graph
    """

    def __init__(self, use_cache: bool = True, cache_size: int = 100, url: str = "http://localhost:8080",
        api_key: Optional[str] = None,
        entity_class_name: str = "Entity",
        relation_class_name: str = "Relation",
        create_schema: bool = True,
        embedding_dimension: int = 768
    ):
        """
        Initialize the Knowledge Graph Integration.

        Args:
            url: Weaviate server URL
            api_key: Weaviate API key
            entity_class_name: Name of the entity class
            relation_class_name: Name of the relation class
            create_schema: Whether to create the schema if it doesn't exist
            embedding_dimension: Dimension of the embedding vectors
        """
        self.url = url
        self.api_key = api_key
        self.entity_class_name = entity_class_name
        self.relation_class_name = relation_class_name
        self.embedding_dimension = embedding_dimension

        # Initialize Weaviate vector store for entities
        self.entity_store = WeaviateVectorStore(
            url=url,
            api_key=api_key,
            class_name=entity_class_name,
            create_class=create_schema,
            vector_index_type="hnsw"
        )

        # Initialize Weaviate vector store for relations
        self.relation_store = WeaviateVectorStore(
            url=url,
            api_key=api_key,
            class_name=relation_class_name,
            create_class=create_schema,
            vector_index_type="hnsw"
        )

        # Create schema if needed
        if create_schema:
            self._create_schema()

        # Set up caching
        if use_cache:
            self._generate_cached = lru_cache(maxsize=cache_size)(self._generate)
        else:
            self._generate_cached = self._generate
            
        logger.info(f"Initialized KnowledgeGraphIntegration with entity class {entity_class_name} and relation class {relation_class_name}")

    def _create_schema(self):
        """
        Create the knowledge graph schema in Weaviate.
        """
        # Get Weaviate client from entity store
        client = self.entity_store.client

        # Check if entity class exists
        if not hasattr(client, 'schema') or not client.schema.exists(self.entity_class_name):
            # Create entity class
            entity_class = {
                "class": self.entity_class_name,
                "description": "Entity class for knowledge graph",
                "vectorizer": "none",  # We'll provide our own vectors
                "vectorIndexType": "hnsw",
                "vectorIndexConfig": {
                    "type": "hnsw",
                    "maxConnections": 64,
                    "efConstruction": 128,
                    "ef": 256,
                    "distance": "cosine"
                },
                "properties": [
                    {
                        "name": "name",
                        "dataType": ["text"],
                        "description": "The name of the entity"
                    },
                    {
                        "name": "type",
                        "dataType": ["text"],
                        "description": "The type of the entity"
                    },
                    {
                        "name": "description",
                        "dataType": ["text"],
                        "description": "The description of the entity"
                    },
                    {
                        "name": "properties",
                        "dataType": ["object"],
                        "description": "Additional properties of the entity"
                    }
                ]
            }
            if hasattr(client, 'schema'):
                client.schema.create_class(entity_class)
                logger.info(f"Created entity class {self.entity_class_name}")

        # Check if relation class exists
        if not hasattr(client, 'schema') or not client.schema.exists(self.relation_class_name):
            # Create relation class
            relation_class = {
                "class": self.relation_class_name,
                "description": "Relation class for knowledge graph",
                "vectorizer": "none",  # We'll provide our own vectors
                "vectorIndexType": "hnsw",
                "vectorIndexConfig": {
                    "type": "hnsw",
                    "maxConnections": 64,
                    "efConstruction": 128,
                    "ef": 256,
                    "distance": "cosine"
                },
                "properties": [
                    {
                        "name": "type",
                        "dataType": ["text"],
                        "description": "The type of the relation"
                    },
                    {
                        "name": "source",
                        "dataType": ["text"],
                        "description": "The source entity ID"
                    },
                    {
                        "name": "target",
                        "dataType": ["text"],
                        "description": "The target entity ID"
                    },
                    {
                        "name": "weight",
                        "dataType": ["number"],
                        "description": "The weight of the relation"
                    },
                    {
                        "name": "properties",
                        "dataType": ["object"],
                        "description": "Additional properties of the relation"
                    }
                ]
            }
            if hasattr(client, 'schema'):
                client.schema.create_class(relation_class)
                logger.info(f"Created relation class {self.relation_class_name}")

    @trace_function(name="knowledge_graph_add_entity")
    @measure_latency("knowledge_graph_add_entity")
    def add_entity(
        self,
        name: str,
        entity_type: str,
        description: str = "",
        properties: Optional[Dict[str, Any]] = None,
        embedding: Optional[List[float]] = None
    ) -> str:
        """
        Add an entity to the knowledge graph.

        Args:
            name: Name of the entity
            entity_type: Type of the entity
            description: Description of the entity
            properties: Additional properties of the entity
            embedding: Embedding vector for the entity

        Returns:
            Entity ID
        """
        # Create entity document
        entity = {
            "name": name,
            "type": entity_type,
            "description": description,
            "properties": properties or {}
        }

        # Generate a random embedding if not provided
        if embedding is None:
            import numpy as np
            embedding = list(np.random.rand(self.embedding_dimension).astype(float))

        # Add entity to the store
        entity_ids = self.entity_store.add_documents([entity], [embedding])
        entity_id = entity_ids[0]

        logger.info(f"Added entity {name} with ID {entity_id}")
        return entity_id

    @trace_function(name="knowledge_graph_add_relation")
    @measure_latency("knowledge_graph_add_relation")
    def add_relation(
        self,
        source_id: str,
        target_id: str,
        relation_type: str,
        weight: float = 1.0,
        properties: Optional[Dict[str, Any]] = None,
        embedding: Optional[List[float]] = None
    ) -> str:
        """
        Add a relation between two entities.

        Args:
            source_id: ID of the source entity
            target_id: ID of the target entity
            relation_type: Type of the relation
            weight: Weight of the relation
            properties: Additional properties of the relation
            embedding: Embedding vector for the relation

        Returns:
            Relation ID
        """
        # Create relation document
        relation = {
            "source": source_id,
            "target": target_id,
            "type": relation_type,
            "weight": weight,
            "properties": properties or {}
        }

        # Generate a random embedding if not provided
        if embedding is None:
            import numpy as np
            embedding = list(np.random.rand(self.embedding_dimension).astype(float))

        # Add relation to the store
        relation_ids = self.relation_store.add_documents([relation], [embedding])
        relation_id = relation_ids[0]

        logger.info(f"Added relation {relation_type} from {source_id} to {target_id} with ID {relation_id}")
        return relation_id

    @trace_function(name="knowledge_graph_get_entity")
    @measure_latency("knowledge_graph_get_entity")
    def get_entity(self, entity_id: str) -> Optional[Dict[str, Any]]:
        """
        Get an entity by ID.

        Args:
            entity_id: ID of the entity

        Returns:
            Entity dictionary or None if not found
        """
        return self.entity_store.get_document(entity_id)

    @trace_function(name="knowledge_graph_get_relation")
    @measure_latency("knowledge_graph_get_relation")
    def get_relation(self, relation_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a relation by ID.

        Args:
            relation_id: ID of the relation

        Returns:
            Relation dictionary or None if not found
        """
        return self.relation_store.get_document(relation_id)

    @trace_function(name="knowledge_graph_search_entities")
    @measure_latency("knowledge_graph_search_entities")
    def search_entities(
        self,
        query_embedding: List[float],
        entity_type: Optional[str] = None,
        top_k: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Search for entities by embedding similarity.

        Args:
            query_embedding: Query embedding vector
            entity_type: Optional entity type filter
            top_k: Number of results to return

        Returns:
            List of entity dictionaries
        """
        # Create filter expression if entity type is provided
        filter_expr = f"type == '{entity_type}'" if entity_type else None

        # Search for entities
        return self.entity_store.search(query_embedding, top_k, filter_expr)

    @trace_function(name="knowledge_graph_search_relations")
    @measure_latency("knowledge_graph_search_relations")
    def search_relations(
        self,
        query_embedding: List[float],
        relation_type: Optional[str] = None,
        source_id: Optional[str] = None,
        target_id: Optional[str] = None,
        top_k: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Search for relations by embedding similarity.

        Args:
            query_embedding: Query embedding vector
            relation_type: Optional relation type filter
            source_id: Optional source entity ID filter
            target_id: Optional target entity ID filter
            top_k: Number of results to return

        Returns:
            List of relation dictionaries
        """
        # Create filter expression
        filter_parts = []
        if relation_type:
            filter_parts.append(f"type == '{relation_type}'")
        if source_id:
            filter_parts.append(f"source == '{source_id}'")
        if target_id:
            filter_parts.append(f"target == '{target_id}'")

        filter_expr = " and ".join(filter_parts) if filter_parts else None

        # Search for relations
        return self.relation_store.search(query_embedding, top_k, filter_expr)

    @trace_function(name="knowledge_graph_get_entity_relations")
    @measure_latency("knowledge_graph_get_entity_relations")
    def get_entity_relations(
        self,
        entity_id: str,
        relation_type: Optional[str] = None,
        direction: str = "outgoing"
    ) -> List[Dict[str, Any]]:
        """
        Get relations for an entity.

        Args:
            entity_id: ID of the entity
            relation_type: Optional relation type filter
            direction: Direction of relations ("outgoing", "incoming", or "both")

        Returns:
            List of relation dictionaries
        """
        # Create filter expression
        direction_parts = []

        if direction == "outgoing" or direction == "both":
            direction_parts.append(f"source == '{entity_id}'")

        if direction == "incoming" or direction == "both":
            direction_parts.append(f"target == '{entity_id}'")

        direction_expr = " or ".join(direction_parts)

        if relation_type:
            filter_expr = f"({direction_expr}) and type == '{relation_type}'"
        else:
            filter_expr = direction_expr

        # Get relations
        # Adapt to expected signature of get_documents
        # We'll use the get_documents method according to its actual interface
        try:
            # Try with expected filter_expr parameter
            return self.relation_store.get_documents(filter_expr=filter_expr)
        except TypeError:
            # Fallback implementation for different interface
            all_documents = self.relation_store.get_documents()
            
            # Apply filter manually if necessary
            if not filter_expr:
                return all_documents
                
            filtered_documents = []
            for doc in all_documents:
                if direction == "outgoing" or direction == "both":
                    if doc.get("source") == entity_id:
                        if relation_type is None or doc.get("type") == relation_type:
                            filtered_documents.append(doc)
                
                if direction == "incoming" or direction == "both":
                    if doc.get("target") == entity_id:
                        if relation_type is None or doc.get("type") == relation_type:
                            filtered_documents.append(doc)
                            
            return filtered_documents

    @trace_function(name="knowledge_graph_get_connected_entities")
    @measure_latency("knowledge_graph_get_connected_entities")
    def get_connected_entities(
        self,
        entity_id: str,
        relation_type: Optional[str] = None,
        direction: str = "outgoing",
        max_depth: int = 1
    ) -> List[Dict[str, Any]]:
        """
        Get entities connected to the given entity.

        Args:
            entity_id: ID of the entity
            relation_type: Optional relation type filter
            direction: Direction of relations ("outgoing", "incoming", or "both")
            max_depth: Maximum depth of traversal

        Returns:
            List of entity dictionaries
        """
        # Get relations for the entity
        relations = self.get_entity_relations(entity_id, relation_type, direction)

        # Extract connected entity IDs
        connected_entity_ids = set()
        for relation in relations:
            if direction == "outgoing" or direction == "both":
                connected_entity_ids.add(relation["target"])
            if direction == "incoming" or direction == "both":
                connected_entity_ids.add(relation["source"])

        # Remove the original entity ID
        if entity_id in connected_entity_ids:
            connected_entity_ids.remove(entity_id)

        # Get connected entities
        connected_entities = []
        for connected_id in connected_entity_ids:
            entity = self.get_entity(connected_id)
            if entity:
                connected_entities.append(entity)

        # Recursively get connected entities if max_depth > 1
        if max_depth > 1:
            for connected_id in connected_entity_ids:
                deeper_entities = self.get_connected_entities(
                    connected_id,
                    relation_type,
                    direction,
                    max_depth - 1
                )
                connected_entities.extend(deeper_entities)

        return connected_entities

    @trace_function(name="knowledge_graph_visualize")
    @measure_latency("knowledge_graph_visualize")
    def visualize(
        self,
        entity_ids: Optional[List[str]] = None,
        relation_types: Optional[List[str]] = None,
        max_entities: int = 100
    ) -> Dict[str, Any]:
        """
        Generate visualization data for the knowledge graph.

        Args:
            entity_ids: Optional list of entity IDs to include
            relation_types: Optional list of relation types to include
            max_entities: Maximum number of entities to include

        Returns:
            Dictionary with nodes and edges for visualization
        """
        # Get entities
        if entity_ids:
            entities = []
            for entity_id in entity_ids:
                entity = self.get_entity(entity_id)
                if entity:
                    entities.append(entity)
        else:
            # Get all entities up to max_entities
            try:
                # Try with expected limit parameter
                entities = self.entity_store.get_documents(limit=max_entities)
            except TypeError:
                # Fallback implementation
                all_entities = self.entity_store.get_documents()
                entities = all_entities[:max_entities]

        # Create nodes
        nodes = []
        entity_id_set = set()
        for entity in entities:
            entity_id = entity["id"]
            entity_id_set.add(entity_id)
            nodes.append({
                "id": entity_id,
                "label": entity["name"],
                "type": entity["type"],
                "properties": entity["properties"]
            })

        # Get relations between these entities
        relations = []
        for entity_id in entity_id_set:
            entity_relations = self.get_entity_relations(entity_id, direction="both")
            for relation in entity_relations:
                source_id = relation["source"]
                target_id = relation["target"]

                # Only include relations where both source and target are in our entity set
                if source_id in entity_id_set and target_id in entity_id_set:
                    # Filter by relation type if specified
                    if relation_types and relation["type"] not in relation_types:
                        continue

                    relations.append(relation)

        # Create edges
        edges = []
        for relation in relations:
            edges.append({
                "id": relation["id"],
                "source": relation["source"],
                "target": relation["target"],
                "label": relation["type"],
                "weight": relation["weight"],
                "properties": relation["properties"]
            })

        # Return visualization data
        return {
            "nodes": nodes,
            "edges": edges
        }

    @trace_function(name="knowledge_graph_query")
    @measure_latency("knowledge_graph_query")
    def query(self, query: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Execute a GraphQL query against the knowledge graph.

        Args:
            query: GraphQL query string
            params: Optional query parameters

        Returns:
            Query result
        """
        try:
            # Execute the query safely checking for attribute
            client = self.entity_store.client
            if hasattr(client, 'query') and hasattr(client.query, 'raw'):
                result = client.query.raw(query, params)
                return result
            else:
                logger.error("Client does not support raw GraphQL queries")
                return {"error": "Client does not support raw GraphQL queries"}
        except Exception as e:
            logger.error(f"Failed to execute GraphQL query: {str(e)}")
            return {"error": str(e)}

    @trace_function(name="knowledge_graph_clear")
    @measure_latency("knowledge_graph_clear")
    def clear(self) -> bool:
        """
        Clear the knowledge graph.

        Returns:
            True if successful, False otherwise
        """
        try:
            # Clear entity and relation stores
            self.entity_store.clear()
            self.relation_store.clear()
            return True
        except Exception as e:
            logger.error(f"Failed to clear knowledge graph: {str(e)}")
            return False

    @trace_function(name="knowledge_graph_export")
    @measure_latency("knowledge_graph_export")
    def export(self, file_path: str) -> bool:
        """
        Export the knowledge graph to a file.

        Args:
            file_path: Path to the output file

        Returns:
            True if successful, False otherwise
        """
        try:
            # Get all entities and relations safely
            try:
                entities = self.entity_store.get_documents()
            except TypeError:
                # If get_documents requires document_ids
                entities = []
                # Implementation would depend on how to list all documents

            try:
                relations = self.relation_store.get_documents()
            except TypeError:
                # If get_documents requires document_ids
                relations = []
                # Implementation would depend on how to list all documents

            # Create export data
            export_data = {
                "entities": entities,
                "relations": relations
            }

            # Write to file
            with open(file_path, "w") as f:
                json.dump(export_data, f, indent=2)

            logger.info(f"Exported knowledge graph to {file_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to export knowledge graph: {str(e)}")
            return False

    @trace_function(name="knowledge_graph_import")
    @measure_latency("knowledge_graph_import")
    def import_from_file(self, file_path: str) -> bool:
        """
        Import a knowledge graph from a file.

        Args:
            file_path: Path to the input file

        Returns:
            True if successful, False otherwise
        """
        try:
            # Read from file
            with open(file_path, "r") as f:
                import_data = json.load(f)

            # Clear existing data
            self.clear()

            # Import entities
            for entity in import_data.get("entities", []):
                # Extract entity data
                entity_data = {
                    "name": entity.get("name", ""),
                    "type": entity.get("type", ""),
                    "description": entity.get("description", ""),
                    "properties": entity.get("properties", {})
                }

                # Add entity with its embedding
                self.entity_store.add_documents(
                    [entity_data],
                    [entity.get("vector", [])]
                )

            # Import relations
            for relation in import_data.get("relations", []):
                # Extract relation data
                relation_data = {
                    "source": relation.get("source", ""),
                    "target": relation.get("target", ""),
                    "type": relation.get("type", ""),
                    "weight": relation.get("weight", 1.0),
                    "properties": relation.get("properties", {})
                }

                # Add relation with its embedding
                self.relation_store.add_documents(
                    [relation_data],
                    [relation.get("vector", [])]
                )

            logger.info(f"Imported knowledge graph from {file_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to import knowledge graph: {str(e)}")
            return False

    @trace_function(name="knowledge_graph_build_from_documents")
    @measure_latency("knowledge_graph_build_from_documents")
    def build_from_documents(
        self,
        documents: List[Dict[str, Any]],
        llm_provider: Any,
        model: str,
        batch_size: int = 5,
        extract_relations: bool = True,
        merge_entities: bool = True,
        similarity_threshold: float = 0.85,
        max_documents: Optional[int] = None,
        include_doc_metadata: bool = True
    ) -> Tuple[int, int]:
        """
        Automatically build a knowledge graph from a collection of documents.
        
        This method extracts entities and relationships from documents using LLMs
        and adds them to the knowledge graph.
        
        Args:
            documents: List of documents to process
            llm_provider: The LLM provider to use for extraction (e.g., openai_provider)
            model: The model to use for extraction
            batch_size: Number of documents to process at once
            extract_relations: Whether to extract relations between entities
            merge_entities: Whether to merge similar entities
            similarity_threshold: Threshold for merging similar entities
            max_documents: Maximum number of documents to process (None for all)
            include_doc_metadata: Whether to include document metadata in entity properties
            
        Returns:
            Tuple of (number of entities created, number of relations created)
        """
        import numpy as np
        from sentence_transformers import SentenceTransformer
        from ..utils.text_processing import split_text
        
        if max_documents:
            documents = documents[:max_documents]
            
        # Initialize metrics
        total_entities = 0
        total_relations = 0
        entity_map = {}  # Maps entity name to entity ID
        entity_embeddings = {}  # Maps entity ID to its embedding
        
        # Initialize SentenceTransformer for entity merging
        embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
        
        # Process documents in batches
        for i in range(0, len(documents), batch_size):
            batch = documents[i:i+batch_size]
            logger.info(f"Processing document batch {i//batch_size + 1}/{(len(documents) + batch_size - 1)//batch_size}")
            
            for doc_index, document in enumerate(batch):
                doc_content = document.get("content", "")
                if not doc_content:
                    continue
                    
                doc_id = document.get("id", str(uuid.uuid4()))
                doc_metadata = document.get("metadata", {}) if include_doc_metadata else {}
                
                # Split long documents
                text_chunks = split_text(doc_content, max_length=8000, overlap=200)
                
                for chunk_index, chunk in enumerate(text_chunks):
                    # Create the prompt for entity extraction
                    prompt = f"""
                    Extract entities from the following text. For each entity, provide:
                    1. Name: The canonical name of the entity
                    2. Type: One of [Person, Organization, Location, Event, Concept, Product, Technology]
                    3. Description: A brief description of the entity
                    4. Properties: Key-value pairs of attributes
                    
                    Format your response as a JSON array of entity objects with the fields: name, type, description, properties.
                    Do not include the word "entity" in the names.
                    
                    Text to analyze:
                    {chunk}
                    """
                    
                    try:
                        # Extract entities using LLM
                        extraction_result = llm_provider.generate(
                            prompt=prompt,
                            model=model,
                            temperature=0.2,
                            max_tokens=2000
                        )
                        
                        # Parse the result
                        try:
                            entities = json.loads(extraction_result)
                            
                            # Process each entity
                            batch_entity_ids = []
                            for entity_data in entities:
                                entity_name = entity_data.get("name", "").strip()
                                if not entity_name:
                                    continue
                                    
                                # Update properties with document metadata
                                properties = entity_data.get("properties", {})
                                if include_doc_metadata:
                                    for key, value in doc_metadata.items():
                                        properties[f"doc_{key}"] = value
                                
                                # Check if we need to merge with existing entity
                                entity_id = None
                                if merge_entities and entity_name in entity_map:
                                    entity_id = entity_map[entity_name]
                                    # Update existing entity with new properties
                                    existing_entity = self.get_entity(entity_id)
                                    if existing_entity:
                                        merged_properties = existing_entity.get("properties", {})
                                        merged_properties.update(properties)
                                        properties = merged_properties
                                        
                                        # Update entity
                                        self.update_entity(
                                            entity_id=entity_id,
                                            name=entity_name,
                                            entity_type=entity_data.get("type", "Unknown"),
                                            description=entity_data.get("description", ""),
                                            properties=properties
                                        )
                                else:
                                    # Create new entity
                                    entity_embedding = embedding_model.encode(entity_name)
                                    
                                    # Check for similar entities if merging
                                    if merge_entities:
                                        # Check if we have a similar entity already
                                        similar_entity_id = None
                                        max_similarity = 0
                                        
                                        for eid, emb in entity_embeddings.items():
                                            similarity = np.dot(entity_embedding, emb) / (np.linalg.norm(entity_embedding) * np.linalg.norm(emb))
                                            if similarity > similarity_threshold and similarity > max_similarity:
                                                similar_entity_id = eid
                                                max_similarity = similarity
                                        
                                        if similar_entity_id:
                                            entity_id = similar_entity_id
                                            # Update existing entity
                                            existing_entity = self.get_entity(entity_id)
                                            if existing_entity:
                                                merged_properties = existing_entity.get("properties", {})
                                                merged_properties.update(properties)
                                                
                                                # Update entity
                                                self.update_entity(
                                                    entity_id=entity_id,
                                                    name=entity_name,
                                                    entity_type=entity_data.get("type", "Unknown"),
                                                    description=entity_data.get("description", ""),
                                                    properties=merged_properties
                                                )
                                    
                                    if not entity_id:
                                        # Add new entity
                                        entity_id = self.add_entity(
                                            name=entity_name,
                                            entity_type=entity_data.get("type", "Unknown"),
                                            description=entity_data.get("description", ""),
                                            properties=properties,
                                            embedding=list(entity_embedding)
                                        )
                                        entity_embeddings[entity_id] = entity_embedding
                                        total_entities += 1
                                    
                                    # Map entity name to ID
                                    entity_map[entity_name] = entity_id
                                
                                batch_entity_ids.append(entity_id)
                            
                            # Extract relations if requested
                            if extract_relations and len(batch_entity_ids) > 1:
                                # Create the prompt for relation extraction
                                entity_list = [f"{i+1}. {entities[i].get('name', '')} ({entities[i].get('type', 'Unknown')})" for i in range(len(batch_entity_ids))]
                                entity_text = "\n".join(entity_list)
                                
                                relation_prompt = f"""
                                Given the following entities extracted from text:
                                {entity_text}
                                
                                Identify all meaningful relationships between these entities. For each relationship, provide:
                                1. Source: The index number of the source entity
                                2. Target: The index number of the target entity
                                3. Type: The type of relationship (e.g., WORKS_FOR, LOCATED_IN, FOUNDED, PART_OF, etc.)
                                4. Properties: Any additional properties of the relationship
                                
                                Format your response as a JSON array of relationship objects with the fields: source, target, type, properties.
                                Only include relations that are explicitly mentioned or strongly implied in the text.
                                
                                Text:
                                {chunk}
                                """
                                
                                try:
                                    relation_result = llm_provider.generate(
                                        prompt=relation_prompt,
                                        model=model,
                                        temperature=0.2,
                                        max_tokens=2000
                                    )
                                    
                                    # Parse the relations
                                    relations = json.loads(relation_result)
                                    
                                    # Add relations to the knowledge graph
                                    for relation_data in relations:
                                        source_idx = int(relation_data.get("source", 0)) - 1
                                        target_idx = int(relation_data.get("target", 0)) - 1
                                        
                                        if 0 <= source_idx < len(batch_entity_ids) and 0 <= target_idx < len(batch_entity_ids):
                                            source_id = batch_entity_ids[source_idx]
                                            target_id = batch_entity_ids[target_idx]
                                            
                                            # Add relation
                                            self.add_relation(
                                                source_id=source_id,
                                                target_id=target_id,
                                                relation_type=relation_data.get("type", "RELATED_TO"),
                                                weight=1.0,
                                                properties=relation_data.get("properties", {})
                                            )
                                            total_relations += 1
                                
                                except Exception as e:
                                    logger.error(f"Error extracting relations: {str(e)}")
                            
                            # Add relations to document
                            for entity_id in batch_entity_ids:
                                # Add relation between entity and document
                                self.add_relation(
                                    source_id=entity_id,
                                    target_id=doc_id,
                                    relation_type="MENTIONED_IN",
                                    weight=1.0,
                                    properties={
                                        "document_id": doc_id,
                                        "chunk_index": chunk_index
                                    }
                                )
                                total_relations += 1
                                
                        except json.JSONDecodeError as e:
                            logger.error(f"Error parsing entity extraction result: {str(e)}")
                            
                    except Exception as e:
                        logger.error(f"Error in entity extraction: {str(e)}")
                        
        logger.info(f"Knowledge graph build completed: {total_entities} entities and {total_relations} relations created")
        return total_entities, total_relations

    @trace_function(name="knowledge_graph_update_entity")
    @measure_latency("knowledge_graph_update_entity")
    def update_entity(
        self,
        entity_id: str,
        name: Optional[str] = None,
        entity_type: Optional[str] = None,
        description: Optional[str] = None,
        properties: Optional[Dict[str, Any]] = None,
        embedding: Optional[List[float]] = None
    ) -> bool:
        """
        Update an entity in the knowledge graph.
        
        Args:
            entity_id: ID of the entity to update
            name: New name of the entity
            entity_type: New type of the entity
            description: New description of the entity
            properties: New properties of the entity
            embedding: New embedding vector for the entity
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get current entity data
            current_entity = self.get_entity(entity_id)
            if not current_entity:
                logger.error(f"Entity {entity_id} not found for update")
                return False
                
            # Update entity data
            entity_data = {
                "name": name if name is not None else current_entity.get("name", ""),
                "type": entity_type if entity_type is not None else current_entity.get("type", ""),
                "description": description if description is not None else current_entity.get("description", ""),
                "properties": properties if properties is not None else current_entity.get("properties", {})
            }
            
            # Update entity - use update_documents method if update_document doesn't exist
            if hasattr(self.entity_store, 'update_document'):
                success = self.entity_store.update_document(entity_id, entity_data, embedding)
            elif hasattr(self.entity_store, 'update_documents'):
                # Adapt to update_documents interface
                documents = [entity_data]
                embeddings = [embedding] if embedding is not None else None
                document_ids = [entity_id]
                results = self.entity_store.update_documents(documents, embeddings, document_ids)
                success = len(results) > 0 and results[0] == entity_id
            else:
                # Fallback implementation
                logger.error("Entity store does not support updating documents")
                return False
            
            if success:
                logger.info(f"Updated entity {entity_id}")
            else:
                logger.error(f"Failed to update entity {entity_id}")
                
            return success
            
        except Exception as e:
            logger.error(f"Error updating entity {entity_id}: {str(e)}")
            return False
