"""
Machine learning-based conflict resolution for ToTRAG.

This module provides machine learning-based conflict resolution strategies
for handling contradictory information in retrieved documents.
"""

import os
import json
import time
import numpy as np
import pickle
from functools import lru_cache
from typing import Dict, List, Any, Optional, Tuple, Set, Union
from collections import defaultdict
from dataclasses import dataclass, field
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.cluster import AgglomerativeClustering
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression

from ..utils.structured_logging import get_logger
from ..utils.distributed_tracing import trace_function
from ..utils.performance_metrics import measure_latency

# Create a logger
logger = get_logger(__name__)

@dataclass
class ConflictExample:
    """Example of a document conflict with user feedback."""
    documents: List[Dict[str, Any]]
    query: str
    selected_document_indices: List[int]
    timestamp: float = field(default_factory=time.time)
    features: Dict[str, Any] = field(default_factory=dict)

class MLConflictResolver:
    """
    Machine learning-based conflict resolution for ToTRAG.
    
    This class implements advanced conflict resolution strategies
    using machine learning techniques to identify and resolve
    contradictions in retrieved documents.
    """
    
    def __init__(self, use_cache: bool = True, cache_size: int = 100, model_type: str = "random_forest",
        similarity_threshold: float = 0.7,
        min_examples_for_training: int = 10,
        max_examples_to_keep: int = 1000,
        data_path: Optional[str] = None
    ):
        """
        Initialize the ML conflict resolver.
        
        Args:
            model_type: Type of ML model to use ("random_forest", "logistic_regression")
            similarity_threshold: Threshold for document similarity
            min_examples_for_training: Minimum number of examples needed for training
            max_examples_to_keep: Maximum number of examples to keep in memory
            data_path: Path to save/load training data and models
        """
        self.model_type = model_type
        self.similarity_threshold = similarity_threshold
        self.min_examples_for_training = min_examples_for_training
        self.max_examples_to_keep = max_examples_to_keep
        self.data_path = data_path or os.path.join(os.path.expanduser("~"), ".deep_research_core", "ml_conflict_resolver")
        
        # Create data directory if it doesn't exist
        os.makedirs(self.data_path, exist_ok=True)
        
        # Initialize vectorizer for text features
        self.vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words="english",
            ngram_range=(1, 2)
        )
        
        # Initialize model
        self.model = self._create_model()
        
        # Initialize training examples
        self.training_examples: List[ConflictExample] = []
        
        # Load existing data and model if available
        self._load_data()
        
        logger.info(
            f"Initialized MLConflictResolver with model_type={model_type}, "
            f"similarity_threshold={similarity_threshold}, data_path={self.data_path}"
        )
    
    def _create_model(self):
        """
        Create a new ML model based on the specified type.
        
        Returns:
            New ML model instance
        """
        if self.model_type == "random_forest":
            return RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                random_state=42
            )
        elif self.model_type == "logistic_regression":
            return LogisticRegression(
                C=1.0,
                max_iter=1000,
                random_state=42
            )
        else:
            logger.warning(f"Unknown model type: {self.model_type}, using random_forest")
            return RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                random_state=42
            )
    
    def _load_data(self) -> None:
        """
        Load training examples and model from disk.
        """
        # Load training examples
        examples_path = os.path.join(self.data_path, "training_examples.pkl")
        if os.path.exists(examples_path):
            try:
                with open(examples_path, "rb") as f:
                    self.training_examples = pickle.load(f)
                logger.info(f"Loaded {len(self.training_examples)} training examples from {examples_path}")
            except Exception as e:
                logger.error(f"Error loading training examples: {str(e)}")
        
        # Load model
        model_path = os.path.join(self.data_path, f"{self.model_type}_model.pkl")
        if os.path.exists(model_path):
            try:
                with open(model_path, "rb") as f:
                    self.model = pickle.load(f)
                logger.info(f"Loaded model from {model_path}")
            except Exception as e:
                logger.error(f"Error loading model: {str(e)}")
        
        # Load vectorizer
        vectorizer_path = os.path.join(self.data_path, "vectorizer.pkl")
        if os.path.exists(vectorizer_path):
            try:
                with open(vectorizer_path, "rb") as f:
                    self.vectorizer = pickle.load(f)
                logger.info(f"Loaded vectorizer from {vectorizer_path}")
            except Exception as e:
                logger.error(f"Error loading vectorizer: {str(e)}")
    
    def _save_data(self) -> None:
        """
        Save training examples and model to disk.
        """
        # Save training examples
        examples_path = os.path.join(self.data_path, "training_examples.pkl")
        try:
            with open(examples_path, "wb") as f:
                pickle.dump(self.training_examples, f)
            logger.info(f"Saved {len(self.training_examples)} training examples to {examples_path}")
        except Exception as e:
            logger.error(f"Error saving training examples: {str(e)}")
        
        # Save model
        model_path = os.path.join(self.data_path, f"{self.model_type}_model.pkl")
        try:
            with open(model_path, "wb") as f:
                pickle.dump(self.model, f)
            logger.info(f"Saved model to {model_path}")
        except Exception as e:
            logger.error(f"Error saving model: {str(e)}")
        
        # Save vectorizer
        vectorizer_path = os.path.join(self.data_path, "vectorizer.pkl")
        try:
            with open(vectorizer_path, "wb") as f:
                pickle.dump(self.vectorizer, f)
            logger.info(f"Saved vectorizer to {vectorizer_path}")
        except Exception as e:
            logger.error(f"Error saving vectorizer: {str(e)}")
    
    def _extract_features(self, documents: List[Dict[str, Any]], query: str) -> Dict[str, Any]:
        """
        Extract features from documents and query for ML model.
        
        Args:
            documents: List of documents
            query: Query string
            
        Returns:
            Dictionary of features
        """
        features = {}
        
        # Extract document contents
        contents = [doc.get("content", "") for doc in documents]
        
        # Calculate document similarity matrix
        try:
            # Fit vectorizer if not already fit
            if not hasattr(self.vectorizer, "vocabulary_"):
                self.vectorizer.fit(contents + [query])
            
            # Transform documents and query
            doc_vectors = self.vectorizer.transform(contents)
            query_vector = self.vectorizer.transform([query])
            
            # Calculate document-document similarity
            doc_sim_matrix = cosine_similarity(doc_vectors)
            
            # Calculate document-query similarity
            doc_query_sim = cosine_similarity(doc_vectors, query_vector).flatten()
            
            # Store similarity features
            features["doc_sim_matrix"] = doc_sim_matrix
            features["doc_query_sim"] = doc_query_sim
            
            # Calculate average similarity
            features["avg_doc_sim"] = np.mean(doc_sim_matrix)
            
            # Calculate variance in similarity
            features["var_doc_sim"] = np.var(doc_sim_matrix)
            
            # Calculate max and min similarity
            features["max_doc_sim"] = np.max(doc_sim_matrix - np.eye(len(documents)))
            features["min_doc_sim"] = np.min(doc_sim_matrix + np.eye(len(documents)) * 2)
            
            # Calculate average query similarity
            features["avg_query_sim"] = np.mean(doc_query_sim)
            
            # Calculate variance in query similarity
            features["var_query_sim"] = np.var(doc_query_sim)
            
            # Calculate max and min query similarity
            features["max_query_sim"] = np.max(doc_query_sim)
            features["min_query_sim"] = np.min(doc_query_sim)
        
        except Exception as e:
            logger.error(f"Error extracting similarity features: {str(e)}")
            
            # Set default values
            features["doc_sim_matrix"] = np.eye(len(documents))
            features["doc_query_sim"] = np.ones(len(documents))
            features["avg_doc_sim"] = 1.0
            features["var_doc_sim"] = 0.0
            features["max_doc_sim"] = 1.0
            features["min_doc_sim"] = 1.0
            features["avg_query_sim"] = 1.0
            features["var_query_sim"] = 0.0
            features["max_query_sim"] = 1.0
            features["min_query_sim"] = 1.0
        
        # Extract document metadata features
        sources = [doc.get("metadata", {}).get("source", "unknown") for doc in documents]
        dates = [doc.get("metadata", {}).get("date", "") for doc in documents]
        scores = [doc.get("score", 0.0) for doc in documents]
        
        # Store metadata features
        features["sources"] = sources
        features["dates"] = dates
        features["scores"] = scores
        
        # Calculate score statistics
        features["avg_score"] = np.mean(scores)
        features["var_score"] = np.var(scores)
        features["max_score"] = np.max(scores)
        features["min_score"] = np.min(scores)
        
        # Extract document length features
        doc_lengths = [len(content.split()) for content in contents]
        
        # Store length features
        features["doc_lengths"] = doc_lengths
        features["avg_length"] = np.mean(doc_lengths)
        features["var_length"] = np.var(doc_lengths)
        features["max_length"] = np.max(doc_lengths)
        features["min_length"] = np.min(doc_lengths)
        
        return features
    
    def _prepare_training_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        Prepare training data from examples.
        
        Returns:
            Tuple of (X, y) for training
        """
        if len(self.training_examples) < self.min_examples_for_training:
            logger.warning(
                f"Not enough training examples: {len(self.training_examples)} < {self.min_examples_for_training}"
            )
            return np.array([]), np.array([])
        
        # Prepare feature vectors and labels
        X = []
        y = []
        
        for example in self.training_examples:
            features = example.features
            
            # For each document in the example
            for i in range(len(example.documents)):
                # Create feature vector for this document
                feature_vector = [
                    features["doc_query_sim"][i],
                    features["scores"][i],
                    features["doc_lengths"][i],
                    features["avg_doc_sim"],
                    features["var_doc_sim"],
                    features["avg_query_sim"],
                    features["var_query_sim"],
                    features["avg_score"],
                    features["var_score"],
                    features["avg_length"],
                    features["var_length"]
                ]
                
                # Add to training data
                X.append(feature_vector)
                
                # Label is 1 if document was selected, 0 otherwise
                label = 1 if i in example.selected_document_indices else 0
                y.append(label)
        
        return np.array(X), np.array(y)
    
    def _train_model(self) -> bool:
        """
        Train the ML model on collected examples.
        
        Returns:
            True if training was successful, False otherwise
        """
        # Prepare training data
        X, y = self._prepare_training_data()
        
        if len(X) == 0 or len(y) == 0:
            return False
        
        try:
            # Train the model
            self.model.fit(X, y)
            logger.info(f"Trained {self.model_type} model on {len(X)} examples")
            
            # Save the model
            self._save_data()
            
            return True
        
        except Exception as e:
            logger.error(f"Error training model: {str(e)}")
            return False
    
    def add_training_example(
        self,
        documents: List[Dict[str, Any]],
        query: str,
        selected_document_indices: List[int]
    ) -> None:
        """
        Add a training example with user feedback.
        
        Args:
            documents: List of documents
            query: Query string
            selected_document_indices: Indices of documents selected by the user
        """
        # Extract features
        features = self._extract_features(documents, query)
        
        # Create example
        example = ConflictExample(
            documents=documents,
            query=query,
            selected_document_indices=selected_document_indices,
            features=features
        )
        
        # Add to training examples
        self.training_examples.append(example)
        
        # Trim if needed
        if len(self.training_examples) > self.max_examples_to_keep:
            # Sort by timestamp (oldest first)
            self.training_examples.sort(key=lambda x: x.timestamp)
            
            # Keep only the most recent examples
            self.training_examples = self.training_examples[-self.max_examples_to_keep:]
        
        # Train model if we have enough examples
        if len(self.training_examples) >= self.min_examples_for_training:
            self._train_model()
        
        # Save examples
        self._save_data()
        
        logger.info(f"Added training example, now have {len(self.training_examples)} examples")
    
    def resolve_conflicts(
        self,
        documents: List[Dict[str, Any]],
        query: str,
        strategy: str = "ml_weighted"
    ) -> List[Dict[str, Any]]:
        """
        Resolve conflicts in documents using ML techniques.
        
        Args:
            documents: List of documents
            query: Query string
            strategy: Resolution strategy ("ml_weighted", "ml_filter", "ml_cluster")
            
        Returns:
            List of documents with conflicts resolved
        """
        if not documents or len(documents) <= 1:
            return documents
        
        # Extract features
        features = self._extract_features(documents, query)
        
        # Check if we have enough training examples and a trained model
        has_trained_model = (
            len(self.training_examples) >= self.min_examples_for_training and
            hasattr(self.model, "predict_proba")
        )
        
        # Apply the selected strategy
        if strategy == "ml_weighted" and has_trained_model:
            return self._resolve_ml_weighted(documents, features)
        elif strategy == "ml_filter" and has_trained_model:
            return self._resolve_ml_filter(documents, features)
        elif strategy == "ml_cluster":
            return self._resolve_ml_cluster(documents, features)
        else:
            # Fall back to similarity-based clustering if no trained model
            logger.warning(f"Using fallback strategy (ml_cluster) instead of {strategy}")
            return self._resolve_ml_cluster(documents, features)
    
    def _resolve_ml_weighted(
        self,
        documents: List[Dict[str, Any]],
        features: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Resolve conflicts by weighting documents using ML predictions.
        
        Args:
            documents: List of documents
            features: Extracted features
            
        Returns:
            List of weighted documents
        """
        # Prepare feature vectors for prediction
        X = []
        for i in range(len(documents)):
            feature_vector = [
                features["doc_query_sim"][i],
                features["scores"][i],
                features["doc_lengths"][i],
                features["avg_doc_sim"],
                features["var_doc_sim"],
                features["avg_query_sim"],
                features["var_query_sim"],
                features["avg_score"],
                features["var_score"],
                features["avg_length"],
                features["var_length"]
            ]
            X.append(feature_vector)
        
        # Get model predictions
        try:
            # Get probability of class 1 (document should be selected)
            probs = self.model.predict_proba(np.array(X))[:, 1]
        except Exception as e:
            logger.error(f"Error making predictions: {str(e)}")
            # Fall back to document scores
            probs = np.array(features["scores"])
            # Normalize to [0, 1]
            if np.max(probs) > np.min(probs):
                probs = (probs - np.min(probs)) / (np.max(probs) - np.min(probs))
        
        # Create weighted documents
        weighted_docs = []
        for i, doc in enumerate(documents):
            # Create a copy of the document
            weighted_doc = doc.copy()
            
            # Update score with ML prediction
            original_score = doc.get("score", 0.0)
            ml_score = probs[i]
            
            # Combine original score and ML score (70% ML, 30% original)
            combined_score = 0.7 * ml_score + 0.3 * original_score
            
            weighted_doc["score"] = combined_score
            weighted_doc["ml_score"] = ml_score
            weighted_doc["original_score"] = original_score
            
            weighted_docs.append(weighted_doc)
        
        # Sort by combined score
        weighted_docs.sort(key=lambda x: x["score"], reverse=True)
        
        return weighted_docs
    
    def _resolve_ml_filter(
        self,
        documents: List[Dict[str, Any]],
        features: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Resolve conflicts by filtering documents using ML predictions.
        
        Args:
            documents: List of documents
            features: Extracted features
            
        Returns:
            List of filtered documents
        """
        # Prepare feature vectors for prediction
        X = []
        for i in range(len(documents)):
            feature_vector = [
                features["doc_query_sim"][i],
                features["scores"][i],
                features["doc_lengths"][i],
                features["avg_doc_sim"],
                features["var_doc_sim"],
                features["avg_query_sim"],
                features["var_query_sim"],
                features["avg_score"],
                features["var_score"],
                features["avg_length"],
                features["var_length"]
            ]
            X.append(feature_vector)
        
        # Get model predictions
        try:
            # Predict class (1 = keep, 0 = filter out)
            predictions = self.model.predict(np.array(X))
            
            # Get probability of class 1 (document should be selected)
            probs = self.model.predict_proba(np.array(X))[:, 1]
        except Exception as e:
            logger.error(f"Error making predictions: {str(e)}")
            # Fall back to keeping all documents
            predictions = np.ones(len(documents))
            probs = np.array(features["scores"])
        
        # Filter documents
        filtered_docs = []
        for i, doc in enumerate(documents):
            if predictions[i] == 1:
                # Create a copy of the document
                filtered_doc = doc.copy()
                
                # Add ML score
                filtered_doc["ml_score"] = float(probs[i])
                
                filtered_docs.append(filtered_doc)
        
        # If no documents were kept, keep the highest scoring one
        if not filtered_docs and documents:
            best_idx = np.argmax(probs)
            best_doc = documents[best_idx].copy()
            best_doc["ml_score"] = float(probs[best_idx])
            filtered_docs.append(best_doc)
        
        # Sort by ML score
        filtered_docs.sort(key=lambda x: x["ml_score"], reverse=True)
        
        return filtered_docs
    
    def _resolve_ml_cluster(
        self,
        documents: List[Dict[str, Any]],
        features: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Resolve conflicts by clustering documents and selecting representatives.
        
        Args:
            documents: List of documents
            features: Extracted features
            
        Returns:
            List of representative documents
        """
        if len(documents) <= 2:
            return documents
        
        # Get similarity matrix
        sim_matrix = features["doc_sim_matrix"]
        
        # Convert to distance matrix
        dist_matrix = 1 - sim_matrix
        
        # Determine number of clusters
        # More clusters for more diverse documents
        avg_sim = features["avg_doc_sim"]
        if avg_sim > 0.8:
            n_clusters = 2  # Very similar documents
        elif avg_sim > 0.6:
            n_clusters = min(3, len(documents))  # Moderately similar
        else:
            n_clusters = min(4, len(documents))  # Diverse documents
        
        try:
            # Perform clustering
            clustering = AgglomerativeClustering(
                n_clusters=n_clusters,
                affinity="precomputed",
                linkage="average"
            )
            
            cluster_labels = clustering.fit_predict(dist_matrix)
            
            # Group documents by cluster
            clusters = defaultdict(list)
            for i, label in enumerate(cluster_labels):
                clusters[label].append(i)
            
            # Select representative from each cluster
            representatives = []
            for cluster_indices in clusters.values():
                if not cluster_indices:
                    continue
                
                # Get query similarity for documents in this cluster
                cluster_query_sim = [features["doc_query_sim"][i] for i in cluster_indices]
                
                # Get scores for documents in this cluster
                cluster_scores = [documents[i].get("score", 0.0) for i in cluster_indices]
                
                # Combine query similarity and score (50/50 weight)
                combined_scores = [0.5 * sim + 0.5 * score for sim, score in zip(cluster_query_sim, cluster_scores)]
                
                # Select document with highest combined score
                best_idx = cluster_indices[np.argmax(combined_scores)]
                representatives.append(best_idx)
            
            # Get representative documents
            result_docs = [documents[i] for i in representatives]
            
            # Sort by original score
            result_docs.sort(key=lambda x: x.get("score", 0.0), reverse=True)
            
            return result_docs
        
        except Exception as e:
            logger.error(f"Error in clustering: {str(e)}")
            # Fall back to original documents
            return documents
