"""
SQLite-based Vector Retrieval-Augmented Generation (RAG) with hierarchical documents.

This module extends the SQLiteVectorRAG implementation with hierarchical document
representation, allowing documents to be represented at multiple levels of granularity.
"""

import os
import sqlite3
from functools import lru_cache
from typing import Dict, Any, List, Optional, Tuple, Union, Callable

from .sqlite_vector_rag import SQLiteVectorRAG
from ..retrieval.hierarchical_document import HierarchicalDocument
from ..utils.structured_logging import get_logger
from ..utils.performance_metrics import measure_latency, measure_block_latency
from ..utils.distributed_tracing import trace_function, span

# Create a logger
logger = get_logger(__name__)

class SQLiteVectorRAGWithHierarchicalDocuments(SQLiteVectorRAG):
    """
    Extends SQLiteVectorRAG with hierarchical document representation.
    
    This class improves the document processing of SQLiteVectorRAG by using
    hierarchical document representation, allowing documents to be represented
    at multiple levels of granularity (document, section, paragraph, sentence).
    """
    
    def __init__(self, use_cache: bool = True, cache_size: int = 100, db_path: str,
        embedding_model: str = "all-MiniLM-L6-v2",
        provider: str = "openrouter",
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        top_k: int = 5,
        chunk_size: int = 1000,
        chunk_overlap: int = 200,
        api_key: Optional[str] = None,
        hierarchical_levels: List[str] = ["document", "section", "paragraph"],
        use_hierarchical_documents: bool = True
    ):
        """
        Initialize SQLiteVectorRAGWithHierarchicalDocuments.
        
        Args:
            db_path: Path to the SQLite database
            embedding_model: Name of the sentence-transformers model to use
            provider: Model provider (openai, anthropic, openrouter)
            model: Model name
            temperature: Temperature for text generation
            max_tokens: Maximum number of tokens to generate
            top_k: Number of documents to retrieve
            chunk_size: Size of document chunks (for fallback chunking)
            chunk_overlap: Overlap between document chunks (for fallback chunking)
            api_key: API key for the provider
            hierarchical_levels: Levels of granularity to include ("document", "section", "paragraph", "sentence")
            use_hierarchical_documents: Whether to use hierarchical documents (if False, falls back to character-based chunking)
        """
        # Initialize the parent class
        super().__init__(
            db_path=db_path,
            embedding_model=embedding_model,
            provider=provider,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            top_k=top_k,
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            api_key=api_key
        )
        
        # Store hierarchical document parameters
        self.hierarchical_levels = hierarchical_levels
        self.use_hierarchical_documents = use_hierarchical_documents
        
        # Extend database schema for hierarchical documents
        if self.use_hierarchical_documents:
            self._extend_db_schema()
    
    def _extend_db_schema(self) -> None:
        """
        Extend the database schema for hierarchical documents.
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Add chunk_type column if it doesn't exist
        cursor.execute("PRAGMA table_info(documents)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        if "chunk_type" not in column_names:
            cursor.execute("ALTER TABLE documents ADD COLUMN chunk_type TEXT")
        
        # Add parent_chunk_id column if it doesn't exist
        if "parent_chunk_id" not in column_names:
            cursor.execute("ALTER TABLE documents ADD COLUMN parent_chunk_id TEXT")
        
        # Create index on chunk_type
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_chunk_type ON documents(chunk_type)")
        
        # Create index on parent_chunk_id
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_parent_chunk_id ON documents(parent_chunk_id)")
        
        conn.commit()
        conn.close()
        
        logger.info("Extended database schema for hierarchical documents")
    
    @trace_function(name="sqlite_vector_rag_hierarchical_add_documents")
    @measure_latency("sqlite_vector_rag_hierarchical_add_documents")
    def add_documents(
        self,
        documents: List[Dict[str, Any]],
        update_existing: bool = True,
        auto_chunk: bool = True
    ) -> List[int]:
        """
        Add documents to the database with hierarchical representation.
        
        Args:
            documents: List of document dictionaries with 'content', 'source', etc.
            update_existing: Whether to update existing documents with the same hash
            auto_chunk: Whether to automatically chunk long documents
            
        Returns:
            List of IDs of the inserted or updated documents
        """
        if not self.use_hierarchical_documents or not auto_chunk:
            # Fall back to parent class method
            return super().add_documents(documents, update_existing, auto_chunk)
        
        # Process documents hierarchically
        all_doc_ids = []
        
        for doc in documents:
            # Create hierarchical document
            hierarchical_doc = HierarchicalDocument(
                content=doc.get("content", ""),
                source=doc.get("source", ""),
                title=doc.get("title"),
                date=doc.get("date"),
                metadata=doc.get("metadata")
            )
            
            # Get chunks at specified levels
            all_chunks = []
            for level in self.hierarchical_levels:
                chunks = hierarchical_doc.get_chunks_by_level(level)
                all_chunks.extend(chunks)
            
            # Add chunks to database
            chunk_ids = self._add_chunks(all_chunks, update_existing)
            all_doc_ids.extend(chunk_ids)
        
        return all_doc_ids
    
    def _add_chunks(
        self,
        chunks: List[Dict[str, Any]],
        update_existing: bool = True
    ) -> List[int]:
        """
        Add chunks to the database.
        
        Args:
            chunks: List of chunk dictionaries
            update_existing: Whether to update existing chunks with the same hash
            
        Returns:
            List of IDs of the inserted or updated chunks
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Extract content for embedding
        contents = [chunk.get("content", "") for chunk in chunks]
        
        # Generate embeddings
        embeddings = self._get_embeddings(contents)
        
        chunk_ids = []
        updated_count = 0
        inserted_count = 0
        
        for i, chunk in enumerate(chunks):
            try:
                # Compute document hash
                doc_hash = self._compute_document_hash(chunk)
                
                # Serialize the embedding
                embedding_blob = self._serialize_embedding(embeddings[i])
                
                # Get chunk type and parent ID
                chunk_type = chunk.get("type", "document")
                parent_chunk_id = chunk.get("metadata", {}).get("document_id")
                
                # Check if document with same hash exists
                if update_existing:
                    cursor.execute("SELECT id FROM documents WHERE hash = ?", (doc_hash,))
                    existing_doc = cursor.fetchone()
                    
                    if existing_doc:
                        # Update existing document
                        cursor.execute(
                            """UPDATE documents SET
                            content = ?, source = ?, title = ?, date = ?,
                            embedding = ?, chunk_type = ?, parent_chunk_id = ?
                            WHERE id = ?""",
                            (
                                chunk.get("content", ""),
                                chunk.get("source", ""),
                                chunk.get("title"),
                                chunk.get("date"),
                                embedding_blob,
                                chunk_type,
                                parent_chunk_id,
                                existing_doc["id"]
                            )
                        )
                        
                        chunk_ids.append(existing_doc["id"])
                        updated_count += 1
                        continue
                
                # Insert new document
                cursor.execute(
                    """INSERT INTO documents
                    (content, source, title, date, embedding, hash, chunk_type, parent_chunk_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)""",
                    (
                        chunk.get("content", ""),
                        chunk.get("source", ""),
                        chunk.get("title"),
                        chunk.get("date"),
                        embedding_blob,
                        doc_hash,
                        chunk_type,
                        parent_chunk_id
                    )
                )
                
                chunk_ids.append(cursor.lastrowid)
                inserted_count += 1
                
            except Exception as e:
                logger.error(f"Error adding chunk: {str(e)}")
        
        conn.commit()
        conn.close()
        
        logger.info(f"Added {inserted_count} new chunks, updated {updated_count} existing chunks")
        
        return chunk_ids
    
    @trace_function(name="sqlite_vector_rag_hierarchical_search")
    @measure_latency("sqlite_vector_rag_hierarchical_search")
    def search(
        self,
        query: str,
        top_k: Optional[int] = None,
        hybrid_weight: float = 0.7,
        chunk_types: Optional[List[str]] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Search for documents similar to the query with support for hierarchical filtering.
        
        Args:
            query: The query string
            top_k: Number of results to return (defaults to self.top_k)
            hybrid_weight: Weight for vector search vs keyword search (1.0 = vector only, 0.0 = keyword only)
            chunk_types: Types of chunks to include in search ("document", "section", "paragraph", "sentence")
            **kwargs: Additional arguments
            
        Returns:
            List of dictionaries containing the retrieved documents and their similarity scores
        """
        if top_k is None:
            top_k = self.top_k
        
        if not self.use_hierarchical_documents or not chunk_types:
            # Fall back to parent class method
            return super().search(query, top_k, hybrid_weight, **kwargs)
        
        # Generate embedding for the query
        query_embedding = self._get_embeddings([query])[0]
        
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Get documents with specified chunk types
        placeholders = ",".join(["?"] * len(chunk_types))
        cursor.execute(
            f"SELECT id, content, source, title, date, embedding, chunk_type, parent_chunk_id FROM documents WHERE chunk_type IN ({placeholders})",
            chunk_types
        )
        rows = cursor.fetchall()
        
        # Calculate vector similarities
        similarities = []
        for row in rows:
            doc_embedding = self._deserialize_embedding(row["embedding"])
            
            # Reshape embeddings for cosine_similarity
            query_embedding_reshaped = query_embedding.reshape(1, -1)
            doc_embedding_reshaped = doc_embedding.reshape(1, -1)
            
            # Calculate cosine similarity
            similarity = self._calculate_cosine_similarity(query_embedding_reshaped, doc_embedding_reshaped)[0][0]
            
            # For hybrid search, we'll combine with keyword matching
            if hybrid_weight < 1.0:
                # Simple keyword matching (count matching terms)
                query_terms = set(query.lower().split())
                content_terms = set(row["content"].lower().split())
                matching_terms = query_terms.intersection(content_terms)
                keyword_score = len(matching_terms) / max(len(query_terms), 1)
                
                # Combine scores with the specified weight
                similarity = (hybrid_weight * similarity) + ((1 - hybrid_weight) * keyword_score)
            
            similarities.append((row, similarity))
        
        # Sort by similarity (descending)
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        # Take top_k results
        top_results = similarities[:top_k]
        
        # Format results
        results = []
        for row, score in top_results:
            result = {
                "id": row["id"],
                "content": row["content"],
                "source": row["source"],
                "title": row["title"],
                "date": row["date"],
                "score": float(score),
                "chunk_type": row["chunk_type"],
                "parent_chunk_id": row["parent_chunk_id"],
                "metadata": {
                    "source": row["source"],
                    "title": row["title"],
                    "date": row["date"],
                    "chunk_type": row["chunk_type"],
                    "parent_chunk_id": row["parent_chunk_id"]
                }
            }
            results.append(result)
        
        conn.close()
        
        return results
    
    def get_related_chunks(
        self,
        chunk_id: int,
        relation_type: str = "children"
    ) -> List[Dict[str, Any]]:
        """
        Get related chunks for a given chunk.
        
        Args:
            chunk_id: ID of the chunk to get related chunks for
            relation_type: Type of relation ("children", "parent", "siblings")
            
        Returns:
            List of related chunks
        """
        if not self.use_hierarchical_documents:
            return []
        
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Get the chunk
        cursor.execute(
            "SELECT id, chunk_type, parent_chunk_id FROM documents WHERE id = ?",
            (chunk_id,)
        )
        chunk = cursor.fetchone()
        
        if not chunk:
            conn.close()
            return []
        
        related_chunks = []
        
        if relation_type == "children":
            # Get child chunks
            cursor.execute(
                "SELECT id, content, source, title, date, chunk_type, parent_chunk_id FROM documents WHERE parent_chunk_id = ?",
                (chunk_id,)
            )
            related_chunks = cursor.fetchall()
        
        elif relation_type == "parent":
            # Get parent chunk
            if chunk["parent_chunk_id"]:
                cursor.execute(
                    "SELECT id, content, source, title, date, chunk_type, parent_chunk_id FROM documents WHERE id = ?",
                    (chunk["parent_chunk_id"],)
                )
                parent = cursor.fetchone()
                if parent:
                    related_chunks = [parent]
        
        elif relation_type == "siblings":
            # Get sibling chunks (chunks with the same parent)
            if chunk["parent_chunk_id"]:
                cursor.execute(
                    "SELECT id, content, source, title, date, chunk_type, parent_chunk_id FROM documents WHERE parent_chunk_id = ? AND id != ?",
                    (chunk["parent_chunk_id"], chunk_id)
                )
                related_chunks = cursor.fetchall()
        
        # Format results
        results = []
        for row in related_chunks:
            result = {
                "id": row["id"],
                "content": row["content"],
                "source": row["source"],
                "title": row["title"],
                "date": row["date"],
                "chunk_type": row["chunk_type"],
                "parent_chunk_id": row["parent_chunk_id"],
                "metadata": {
                    "source": row["source"],
                    "title": row["title"],
                    "date": row["date"],
                    "chunk_type": row["chunk_type"],
                    "parent_chunk_id": row["parent_chunk_id"]
                }
            }
            results.append(result)
        
        conn.close()
        
        return results
    
    def format_context_hierarchical(
        self,
        documents: List[Dict[str, Any]],
        include_related: bool = True,
        relation_type: str = "parent"
    ) -> str:
        """
        Format retrieved documents into context with hierarchical information.
        
        Args:
            documents: List of retrieved documents
            include_related: Whether to include related chunks
            relation_type: Type of relation for related chunks ("parent", "children", "siblings")
            
        Returns:
            Formatted context string
        """
        if not self.use_hierarchical_documents:
            return self.format_context(documents)
        
        context_parts = []
        
        for i, doc in enumerate(documents):
            # Add document information
            context_part = f"Document {i+1} [{doc['chunk_type']}]: {doc['title'] or 'Untitled'} (Source: {doc['source']})\n\n"
            context_part += doc['content']
            
            # Add related chunks if requested
            if include_related:
                related_chunks = self.get_related_chunks(doc['id'], relation_type)
                
                if related_chunks:
                    context_part += f"\n\n{relation_type.capitalize()} Information:\n"
                    
                    for j, related in enumerate(related_chunks[:2]):  # Limit to 2 related chunks
                        context_part += f"- {related['chunk_type'].capitalize()}: {related['content'][:100]}..."
            
            context_parts.append(context_part)
        
        return "\n\n---\n\n".join(context_parts)
    
    @trace_function(name="sqlite_vector_rag_hierarchical_process")
    @measure_latency("sqlite_vector_rag_hierarchical_process")
    def process(
        self,
        query: str,
        top_k: Optional[int] = None,
        hybrid_weight: float = 0.7,
        chunk_types: Optional[List[str]] = None,
        include_related: bool = True,
        relation_type: str = "parent",
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        save_evaluation: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process a query using hierarchical document retrieval and generate a response.
        
        Args:
            query: The query to process
            top_k: Number of results to return (defaults to self.top_k)
            hybrid_weight: Weight for vector search vs keyword search (1.0 = vector only, 0.0 = keyword only)
            chunk_types: Types of chunks to include in search ("document", "section", "paragraph", "sentence")
            include_related: Whether to include related chunks in context
            relation_type: Type of relation for related chunks ("parent", "children", "siblings")
            custom_system_prompt: Custom system prompt to use
            custom_user_prompt: Custom user prompt template to use
            callback: Optional callback function for streaming
            save_evaluation: Whether to save evaluation data
            **kwargs: Additional arguments
            
        Returns:
            Dictionary containing the answer, retrieved documents, and other information
        """
        if not self.use_hierarchical_documents or not chunk_types:
            # Use default chunk types if not specified
            if not chunk_types:
                chunk_types = self.hierarchical_levels
            
            # Search for documents
            documents = self.search(
                query=query,
                top_k=top_k,
                hybrid_weight=hybrid_weight,
                chunk_types=chunk_types,
                **kwargs
            )
            
            # Format context with hierarchical information
            context = self.format_context_hierarchical(
                documents=documents,
                include_related=include_related,
                relation_type=relation_type
            )
            
            # Generate response
            response = self.generate(
                query=query,
                context=context,
                custom_system_prompt=custom_system_prompt,
                custom_user_prompt=custom_user_prompt,
                callback=callback
            )
            
            # Create result
            result = {
                "query": query,
                "answer": response,
                "documents": documents,
                "context": context,
                "model": self.model,
                "provider": self.provider
            }
            
            return result
        else:
            # Fall back to parent class method
            return super().process(
                query=query,
                top_k=top_k,
                hybrid_weight=hybrid_weight,
                custom_system_prompt=custom_system_prompt,
                custom_user_prompt=custom_user_prompt,
                callback=callback,
                save_evaluation=save_evaluation,
                **kwargs
            )
