"""
CoTRAG with Adaptive Learning.

This module extends the CoTRAG implementation with adaptive learning
capabilities to automatically adjust parameters based on real-world data.
"""

import os
import json
from collections import defaultdict
from datetime import datetime
from functools import lru_cache
from typing import Dict, Any, List, Optional, Callable

from .cot_rag import CoTRAG
from ..utils.structured_logging import get_logger
from ..utils.performance_metrics import measure_latency
from ..utils.distributed_tracing import trace_function

# Create a logger
logger = get_logger(__name__)

class CoTRAGAdaptiveLearning(CoTRAG):
    """
    Extends CoTRAG with adaptive learning capabilities.

    This class adds the ability to learn from real-world data and
    automatically adjust parameters for optimal performance.
    """

    def __init__(self, use_cache: bool = True, cache_size: int = 100, provider: str = "openai",
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        vector_store=None,
        adaptive: bool = True,
        use_cache: bool = True,
        evaluate_results: bool = False,
        use_dynamic_weighting: bool = True,
        min_cot_weight: float = 0.3,
        max_cot_weight: float = 0.8,
        default_cot_weight: float = 0.5,
        weighting_strategy: str = "auto",
        handle_irrelevant_docs: bool = True,
        relevance_threshold: float = 0.3,
        analyze_errors: bool = False,
        verbose: bool = False,
        learning_rate: float = 0.01,
        feedback_history_path: Optional[str] = None,
        max_history_size: int = 1000,
        auto_adjust_interval: int = 50,
        min_samples_for_adjustment: int = 10
    ):
        """
        Initialize CoTRAGAdaptiveLearning.

        Args:
            provider: The provider to use ("openai", "anthropic", etc.)
            model: The model to use (if None, will use provider's default)
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            vector_store: Vector store to use for retrieval
            adaptive: Whether to use adaptive parameter adjustment
            use_cache: Whether to use caching for repeated queries
            evaluate_results: Whether to evaluate reasoning quality
            use_dynamic_weighting: Whether to dynamically adjust weights between CoT and RAG
            min_cot_weight: Minimum weight for CoT (0.0 to 1.0)
            max_cot_weight: Maximum weight for CoT (0.0 to 1.0)
            default_cot_weight: Default weight for CoT (0.0 to 1.0)
            weighting_strategy: Weight adjustment strategy ("auto", "query_type",
                              "query_complexity", "document_relevance")
            handle_irrelevant_docs: Whether to handle cases when RAG returns irrelevant documents
            relevance_threshold: Threshold for document relevance (0.0 to 1.0)
            analyze_errors: Whether to analyze errors in results
            verbose: Whether to log detailed information
            learning_rate: Rate at which parameters are adjusted based on feedback
            feedback_history_path: Path to store feedback history
            max_history_size: Maximum number of feedback entries to store
            auto_adjust_interval: Number of feedback entries before auto-adjustment
            min_samples_for_adjustment: Minimum number of samples required for adjustment
        """
        # Initialize the base CoTRAG class
        super().__init__(
            provider=provider,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            vector_store=vector_store,
            adaptive=adaptive,
            use_cache=use_cache,
            evaluate_results=evaluate_results,
            use_dynamic_weighting=use_dynamic_weighting,
            min_cot_weight=min_cot_weight,
            max_cot_weight=max_cot_weight,
            default_cot_weight=default_cot_weight,
            weighting_strategy=weighting_strategy,
            handle_irrelevant_docs=handle_irrelevant_docs,
            relevance_threshold=relevance_threshold,
            analyze_errors=analyze_errors,
            verbose=verbose
        )

        # Store adaptive learning parameters
        self.learning_rate = learning_rate
        self.feedback_history_path = feedback_history_path or "cotrag_feedback_history.json"
        self.max_history_size = max_history_size
        self.auto_adjust_interval = auto_adjust_interval
        self.min_samples_for_adjustment = min_samples_for_adjustment

        # Store weight parameters for reference in feedback
        self.min_cot_weight = min_cot_weight
        self.max_cot_weight = max_cot_weight
        self.default_cot_weight = default_cot_weight
        self.weighting_strategy = weighting_strategy

        # Initialize feedback history
        self.feedback_history = self._load_feedback_history()

        # Initialize parameter adjustment tracking
        self.feedback_count_since_adjustment = 0

        # Initialize parameter statistics
        self.parameter_stats = {
            "min_cot_weight": {"sum": 0, "count": 0, "best_value": min_cot_weight},
            "max_cot_weight": {"sum": 0, "count": 0, "best_value": max_cot_weight},
            "default_cot_weight": {"sum": 0, "count": 0, "best_value": default_cot_weight},
            "relevance_threshold": {"sum": 0, "count": 0, "best_value": relevance_threshold},
            "temperature": {"sum": 0, "count": 0, "best_value": temperature}
        }

        # Initialize query type statistics
        self.query_type_stats = defaultdict(lambda: {"cot_weight": {"sum": 0, "count": 0}})

        # Set up caching
        if use_cache:
            self._generate_cached = lru_cache(maxsize=cache_size)(self._generate)
        else:
            self._generate_cached = self._generate
            
        logger.info(f"Initialized CoTRAGAdaptiveLearning with learning rate: {self.learning_rate}")

    def _load_feedback_history(self) -> List[Dict[str, Any]]:
        """
        Load feedback history from file.

        Returns:
            List of feedback entries
        """
        if os.path.exists(self.feedback_history_path):
            try:
                with open(self.feedback_history_path, "r", encoding="utf-8") as f:
                    history = json.load(f)
                count = len(history)
                logger.info(f"Loaded {count} feedback entries from {self.feedback_history_path}")
                return history
            except Exception as e:
                logger.error(f"Error loading feedback history: {e}")
                return []
        else:
            logger.info(f"No feedback history found at {self.feedback_history_path}")
            return []

    def _save_feedback_history(self) -> None:
        """Save feedback history to file."""
        try:
            with open(self.feedback_history_path, "w", encoding="utf-8") as f:
                json.dump(self.feedback_history, f, indent=2)
            count = len(self.feedback_history)
            logger.info(f"Saved {count} feedback entries to {self.feedback_history_path}")
        except Exception as e:
            logger.error(f"Error saving feedback history: {e}")

    def add_feedback(
        self,
        query: str,
        result: Dict[str, Any],
        feedback_score: float,
        feedback_type: str = "user",
        feedback_notes: Optional[str] = None
    ) -> None:
        """
        Add feedback for a query result.

        Args:
            query: The original query
            result: The result from processing the query
            feedback_score: Score from 0.0 (poor) to 1.0 (excellent)
            feedback_type: Type of feedback ("user", "automatic", "expert")
            feedback_notes: Optional notes about the feedback
        """
        # Create feedback entry
        feedback_entry = {
            "query": query,
            "timestamp": datetime.now().isoformat(),
            "feedback_score": feedback_score,
            "feedback_type": feedback_type,
            "feedback_notes": feedback_notes,
            "parameters": {
                "provider": self.provider,
                "model": self.model,
                "temperature": self.temperature,
                "max_tokens": self.max_tokens,
                "min_cot_weight": self.min_cot_weight,
                "max_cot_weight": self.max_cot_weight,
                "default_cot_weight": self.default_cot_weight,
                "weighting_strategy": self.weighting_strategy,
                "relevance_threshold": self.relevance_threshold
            }
        }

        # Add weights if available
        if "weights" in result:
            feedback_entry["weights"] = result["weights"]

        # Add query type if available
        if self.weight_optimizer:
            query_analysis = self.weight_optimizer.analyze_query(query)
            if ("query_type_analysis" in query_analysis and
                    "query_type" in query_analysis["query_type_analysis"]):
                feedback_entry["query_type"] = query_analysis["query_type_analysis"]["query_type"]

        # Add error analysis if available
        if "error_analysis" in result and "error_source" in result["error_analysis"]:
            error_source = result["error_analysis"]["error_source"]
            suggestions = result["error_analysis"].get("suggestions", {})
            feedback_entry["error_analysis"] = {
                "primary_source": error_source.get("primary_source", "unknown"),
                "confidence": error_source.get("confidence", 0.0),
                "primary_suggestion": suggestions.get("primary_suggestion", "")
            }

        # Add to feedback history
        self.feedback_history.append(feedback_entry)

        # Trim history if needed
        if len(self.feedback_history) > self.max_history_size:
            self.feedback_history = self.feedback_history[-self.max_history_size:]

        # Save feedback history
        self._save_feedback_history()

        # Update parameter statistics
        self._update_parameter_stats(feedback_entry, feedback_score)

        # Increment feedback count
        self.feedback_count_since_adjustment += 1

        # Check if auto-adjustment is needed
        if self.feedback_count_since_adjustment >= self.auto_adjust_interval:
            self.adjust_parameters()
            self.feedback_count_since_adjustment = 0

    def _update_parameter_stats(self, entry: Dict[str, Any], score: float) -> None:
        """
        Update parameter statistics based on feedback.

        Args:
            entry: The feedback entry dictionary
            score: The feedback score (0.0 to 1.0)
        """
        # Update general parameter statistics
        for param, stats in self.parameter_stats.items():
            if param in entry["parameters"]:
                value = entry["parameters"][param]
                stats["sum"] += value * score
                stats["count"] += 1

        # Update query type statistics
        if "query_type" in entry and "weights" in entry:
            query_type = entry["query_type"]
            cot_weight = entry["weights"]["cot_weight"]

            self.query_type_stats[query_type]["cot_weight"]["sum"] += cot_weight * score
            self.query_type_stats[query_type]["cot_weight"]["count"] += 1

    def adjust_parameters(self) -> Dict[str, Any]:
        """
        Adjust parameters based on feedback history.

        Returns:
            Dictionary with adjusted parameters
        """
        min_samples = self.min_samples_for_adjustment
        if not self.feedback_history or len(self.feedback_history) < min_samples:
            logger.info(f"Not enough feedback samples for adjustment (minimum: {min_samples})")
            return {}

        entries_count = len(self.feedback_history)
        logger.info(f"Adjusting parameters based on {entries_count} feedback entries")

        # Calculate optimal parameters
        adjusted_params = {}

        # Adjust general parameters
        for param, stats in self.parameter_stats.items():
            if stats["count"] >= self.min_samples_for_adjustment:
                optimal_value = stats["sum"] / stats["count"]
                current_value = getattr(self, param)

                # Apply learning rate to adjustment
                new_value = current_value + self.learning_rate * (optimal_value - current_value)

                # Apply constraints
                if param == "min_cot_weight":
                    new_value = max(0.1, min(0.5, new_value))
                elif param == "max_cot_weight":
                    new_value = max(0.5, min(0.9, new_value))
                elif param == "default_cot_weight":
                    new_value = max(0.3, min(0.7, new_value))
                elif param == "relevance_threshold":
                    new_value = max(0.1, min(0.5, new_value))
                elif param == "temperature":
                    new_value = max(0.1, min(1.0, new_value))

                # Update parameter
                setattr(self, param, new_value)
                adjusted_params[param] = new_value

                # Update best value if this parameter has better feedback
                avg_feedback = stats["sum"] / stats["count"]
                if avg_feedback > 0.7:  # Only update if feedback is good
                    stats["best_value"] = new_value

                logger.info(f"Adjusted {param}: {current_value:.4f} -> {new_value:.4f}")

        # Update weight optimizer if it exists
        if self.weight_optimizer and "min_cot_weight" in adjusted_params:
            self.weight_optimizer.min_cot_weight = adjusted_params["min_cot_weight"]

        if self.weight_optimizer and "max_cot_weight" in adjusted_params:
            self.weight_optimizer.max_cot_weight = adjusted_params["max_cot_weight"]

        if self.weight_optimizer and "default_cot_weight" in adjusted_params:
            self.weight_optimizer.default_cot_weight = adjusted_params["default_cot_weight"]

        # Adjust query type weights in the optimizer
        if self.weight_optimizer:
            self._adjust_query_type_weights()

        return adjusted_params

    def _adjust_query_type_weights(self) -> None:
        """Adjust weights for different query types based on feedback."""
        # Ensure the weight optimizer is ready for query type weights
        if hasattr(self.weight_optimizer, "initialize_query_weights"):
            # Use proper method if available
            self.weight_optimizer.initialize_query_weights()
        # No fallback needed - we'll check before accessing attributes

        for query_type, stats in self.query_type_stats.items():
            if stats["cot_weight"]["count"] >= self.min_samples_for_adjustment:
                optimal_weight = stats["cot_weight"]["sum"] / stats["cot_weight"]["count"]

                # Store optimal weight for this query type
                if hasattr(self.weight_optimizer, "set_query_weight"):
                    # Use proper method if available
                    self.weight_optimizer.set_query_weight(query_type, optimal_weight)
                # No fallback - if the method doesn't exist, we skip this step

                logger.info(f"Set optimal weight for {query_type}: {optimal_weight:.4f}")

    def reset_to_best_parameters(self) -> Dict[str, Any]:
        """
        Reset parameters to the best values based on feedback history.

        Returns:
            Dictionary with reset parameters
        """
        reset_params = {}

        for param, stats in self.parameter_stats.items():
            if "best_value" in stats:
                current_value = getattr(self, param)
                best_value = stats["best_value"]

                # Update parameter
                setattr(self, param, best_value)
                reset_params[param] = best_value

                logger.info(f"Reset {param}: {current_value:.4f} -> {best_value:.4f}")

        # Update weight optimizer if it exists
        if self.weight_optimizer and "min_cot_weight" in reset_params:
            self.weight_optimizer.min_cot_weight = reset_params["min_cot_weight"]

        if self.weight_optimizer and "max_cot_weight" in reset_params:
            self.weight_optimizer.max_cot_weight = reset_params["max_cot_weight"]

        if self.weight_optimizer and "default_cot_weight" in reset_params:
            self.weight_optimizer.default_cot_weight = reset_params["default_cot_weight"]

        return reset_params

    def get_parameter_stats(self) -> Dict[str, Any]:
        """
        Get statistics about parameters and their performance.

        Returns:
            Dictionary with parameter statistics
        """
        stats = {}

        # Calculate average values for each parameter
        for param, param_stats in self.parameter_stats.items():
            if param_stats["count"] > 0:
                avg_value = param_stats["sum"] / param_stats["count"]
                stats[param] = {
                    "current_value": getattr(self, param),
                    "average_value": avg_value,
                    "best_value": param_stats.get("best_value", getattr(self, param)),
                    "sample_count": param_stats["count"]
                }

        # Calculate average weights for each query type
        query_type_weights = {}
        for query_type, type_stats in self.query_type_stats.items():
            if type_stats["cot_weight"]["count"] > 0:
                avg_weight = type_stats["cot_weight"]["sum"] / type_stats["cot_weight"]["count"]
                query_type_weights[query_type] = {
                    "average_cot_weight": avg_weight,
                    "sample_count": type_stats["cot_weight"]["count"]
                }

        stats["query_type_weights"] = query_type_weights

        return stats

    def analyze_feedback_trends(self) -> Dict[str, Any]:
        """
        Analyze trends in feedback history.

        Returns:
            Dictionary with feedback trends
        """
        if not self.feedback_history:
            return {"message": "No feedback history available"}

        # Group feedback by time periods
        time_periods = {}
        for entry in self.feedback_history:
            timestamp = datetime.fromisoformat(entry["timestamp"])
            period = timestamp.strftime("%Y-%m")

            if period not in time_periods:
                time_periods[period] = {
                    "count": 0,
                    "score_sum": 0,
                    "query_types": defaultdict(int)
                }

            time_periods[period]["count"] += 1
            time_periods[period]["score_sum"] += entry["feedback_score"]

            if "query_type" in entry:
                time_periods[period]["query_types"][entry["query_type"]] += 1

        # Calculate average scores by period
        period_scores = {}
        for period, stats in time_periods.items():
            period_scores[period] = {
                "average_score": stats["score_sum"] / stats["count"],
                "count": stats["count"],
                "query_types": dict(stats["query_types"])
            }

        # Analyze error sources
        error_sources = defaultdict(int)
        for entry in self.feedback_history:
            if "error_analysis" in entry and "primary_source" in entry["error_analysis"]:
                error_sources[entry["error_analysis"]["primary_source"]] += 1

        # Calculate overall statistics
        total_count = len(self.feedback_history)
        total_score = sum(entry["feedback_score"] for entry in self.feedback_history)
        average_score = total_score / total_count if total_count > 0 else 0

        return {
            "total_entries": total_count,
            "average_score": average_score,
            "period_scores": period_scores,
            "error_sources": dict(error_sources)
        }

    @trace_function(name="cotrag_adaptive_process")
    @measure_latency("cotrag_adaptive_process")
    def process(
        self,
        query: str,
        top_k: int = 5,
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        force_refresh: bool = False,
        expected_answer: Optional[str] = None,
        auto_feedback: bool = False
    ) -> Dict[str, Any]:
        """
        Process a query using CoTRAG with adaptive learning.

        Args:
            query: The query to process
            top_k: Number of documents to retrieve
            custom_system_prompt: Custom system prompt to use
            custom_user_prompt: Custom user prompt template to use
            callback: Optional callback function for streaming
            force_refresh: Whether to force a refresh (ignore cache)
            expected_answer: Optional expected answer for error analysis
            auto_feedback: Whether to automatically generate feedback

        Returns:
            Dict containing the query, retrieved documents, reasoning, and answer
        """
        # Use the base CoTRAG process method
        result = super().process(
            query=query,
            top_k=top_k,
            custom_system_prompt=custom_system_prompt,
            custom_user_prompt=custom_user_prompt,
            callback=callback,
            force_refresh=force_refresh,
            expected_answer=expected_answer
        )

        # Add adaptive learning metadata
        result["adaptive_learning"] = True

        # Generate automatic feedback if enabled
        if auto_feedback and "evaluation" in result:
            # Use evaluation metrics to generate feedback
            overall_quality = result["evaluation"]["metrics"].get("overall_quality", 0) / 10.0
            self.add_feedback(
                query=query,
                result=result,
                feedback_score=overall_quality,
                feedback_type="automatic",
                feedback_notes="Auto-generated from evaluation metrics"
            )

            result["auto_feedback"] = {
                "score": overall_quality,
                "type": "automatic"
            }

        return result
