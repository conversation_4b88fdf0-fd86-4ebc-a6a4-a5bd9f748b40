"""
Weaviate-based Retrieval-Augmented Generation (RAG) implementation.

This module provides a RAG implementation using Weaviate for efficient vector search.
"""

from functools import lru_cache
from typing import List, Dict, Any, Optional, Callable

from .base_rag import BaseRAG
from ..retrieval.weaviate_vector_store import WeaviateVectorStore
from ..models.api.openai import openai_provider
from ..models.api.anthropic import anthropic_provider
from ..models.api.openrouter import openrouter_provider

from ..utils.structured_logging import get_logger
from ..utils.performance_metrics import measure_latency
from ..utils.distributed_tracing import trace_function, span

# Create a logger
logger = get_logger(__name__)


class WeaviateRAG(BaseRAG):
    """
    Retrieval-Augmented Generation using Weaviate as the vector store.

    This class provides a RAG implementation using Weaviate for efficient vector search.
    It supports:
    - Adding, updating, and deleting documents
    - Processing queries with RAG
    - Filtering based on metadata
    - Advanced index configuration
    - Document and embedding retrieval by ID
    """

    def __init__(self, use_cache: bool = True, cache_size: int = 100, provider: str = "openai",
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        embedding_model: str = "text-embedding-ada-002",
        url: str = "http://localhost:8080",
        api_key: Optional[str] = None,
        class_name: str = "Document",
        batch_size: int = 100,
        create_class: bool = False,
        vector_index_type: str = "hnsw",
        vector_index_config: Optional[Dict[str, Any]] = None,
        top_k: int = 5
    ):
        """
        Initialize the WeaviateRAG.

        Args:
            provider: The provider to use ("openai", "anthropic", "openrouter")
            model: The model to use (if None, will use provider's default)
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            embedding_model: Model to use for embeddings
            url: Weaviate server URL
            api_key: Weaviate API key
            class_name: Name of the Weaviate class
            batch_size: Batch size for adding documents
            create_class: Whether to create the class if it doesn't exist
            vector_index_type: Type of vector index (hnsw, flat)
            vector_index_config: Configuration for the vector index
            top_k: Number of documents to retrieve
        """
        # Initialize the base class
        super().__init__(
            provider=provider,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            embedding_model=embedding_model,
            top_k=top_k
        )

        # Set up the provider
        if provider == "openai":
            self.api_provider = openai_provider
            self.model = model or "gpt-4o"
        elif provider == "anthropic":
            self.api_provider = anthropic_provider
            self.model = model or "claude-3-opus-20240229"
        elif provider == "openrouter":
            self.api_provider = openrouter_provider
            self.model = model or "openai/gpt-4o"
        else:
            raise ValueError(f"Unsupported provider: {provider}")

        # Initialize the vector store
        try:
            self.vector_store = WeaviateVectorStore(
                url=url,
                api_key=api_key,
                class_name=class_name,
                batch_size=batch_size,
                create_class=create_class,
                vector_index_type=vector_index_type,
                vector_index_config=vector_index_config
            )
            # Set up caching
        if use_cache:
            self._generate_cached = lru_cache(maxsize=cache_size)(self._generate)
        else:
            self._generate_cached = self._generate
            
        logger.info(f"Initialized WeaviateVectorStore with class {class_name}")
        except Exception as e:
            logger.error(f"Failed to initialize WeaviateVectorStore: {e}")
            raise

    @trace_function(name="weaviate_rag_add_documents")
    @measure_latency("weaviate_rag_add_documents")
    def add_documents(self, documents: List[Dict[str, Any]], **kwargs) -> List[str]:
        """
        Add documents to the vector store.

        Args:
            documents: List of document dictionaries with 'content', 'source', and optional metadata
            **kwargs: Additional implementation-specific arguments

        Returns:
            List of IDs of the inserted documents
        """
        try:
            # Extract content for embedding
            contents = [doc.get("content", "") for doc in documents]

            # Generate embeddings
            with span("generate_embeddings"):
                embeddings = self._get_embeddings(contents)

            # Add documents to vector store
            with span("add_to_vector_store"):
                result = self.vector_store.add_documents(documents, embeddings)

            logger.info(f"Added {len(documents)} documents to Weaviate")
            return result
        except Exception as e:
            logger.error(f"Failed to add documents to Weaviate: {str(e)}")
            raise

    @trace_function(name="weaviate_rag_search")
    @measure_latency("weaviate_rag_search")
    def search(
        self,
        query: str,
        top_k: Optional[int] = None,
        filter_expr: Optional[str] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Search for documents relevant to the query.

        Args:
            query: The query to search for
            top_k: Number of results to return (if None, uses the default)
            filter_expr: Optional filter expression in Weaviate GraphQL format
            **kwargs: Additional implementation-specific arguments

        Returns:
            List of dictionaries containing the retrieved documents and their similarity scores
        """
        try:
            # Generate embedding for the query
            with span("generate_query_embedding"):
                query_embedding = self._get_embeddings([query])[0]

            # Retrieve relevant documents
            with span("search_vector_store"):
                retrieved_docs = self.vector_store.search(
                    query_embedding=query_embedding,
                    top_k=top_k or self.top_k,
                    filter_expr=filter_expr
                )

            logger.info(f"Retrieved {len(retrieved_docs)} documents from Weaviate for query: {query[:50]}...")
            return retrieved_docs
        except Exception as e:
            logger.error(f"Failed to search Weaviate: {str(e)}")
            raise

    @trace_function(name="weaviate_rag_process")
    @measure_latency("weaviate_rag_process")
    def process(
        self,
        query: str,
        filter_expr: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process a query using RAG.

        Args:
            query: The query to process
            filter_expr: Optional filter expression in Weaviate GraphQL format
            callback: Optional callback function for streaming responses
            **kwargs: Additional implementation-specific arguments

        Returns:
            Dictionary containing the answer, retrieved documents, and other information
        """
        try:
            # Search for relevant documents
            retrieved_docs = self.search(query, filter_expr=filter_expr)

            # Format documents for the prompt
            with span("format_documents"):
                formatted_docs = self._format_documents(retrieved_docs)

            # Create the prompt
            with span("create_prompts"):
                system_prompt = self._create_system_prompt()
                user_prompt = self._create_user_prompt(query, formatted_docs)

            # Generate the answer
            with span("generate_answer"):
                response = self.api_provider.complete(
                    system_prompt=system_prompt,
                    user_prompt=user_prompt,
                    model=self.model,
                    temperature=self.temperature,
                    max_tokens=self.max_tokens,
                    stream=callback is not None,
                    callback=callback
                )

            # Return the result
            return {
                "query": query,
                "answer": response,
                "documents": retrieved_docs,
                "system_prompt": system_prompt,
                "user_prompt": user_prompt,
                "model": self.model,
                "provider": self.provider
            }
        except Exception as e:
            logger.error(f"Failed to process query with WeaviateRAG: {str(e)}")
            raise

    @trace_function(name="weaviate_rag_update_documents")
    @measure_latency("weaviate_rag_update_documents")
    def update_documents(self, document_ids: List[str], documents: List[Dict[str, Any]], **kwargs) -> bool:
        """
        Update existing documents in the vector store.

        Args:
            document_ids: List of document IDs to update
            documents: List of updated document dictionaries
            **kwargs: Additional implementation-specific arguments

        Returns:
            True if successful, False otherwise
        """
        try:
            # Extract content for embedding
            contents = [doc.get("content", "") for doc in documents]

            # Generate embeddings
            with span("generate_embeddings"):
                embeddings = self._get_embeddings(contents)

            # Update documents in vector store
            # Note: Weaviate doesn't have a separate update method, it uses add_documents which is an upsert operation
            with span("update_vector_store"):
                # First delete the existing documents
                self.vector_store.delete_documents(document_ids)

                # Then add the updated documents
                # We need to maintain the same document IDs
                for i, doc_id in enumerate(document_ids):
                    if i < len(documents):
                        documents[i]["id"] = doc_id

                result = self.vector_store.add_documents(documents, embeddings)

            logger.info(f"Updated {len(documents)} documents in Weaviate")
            return True
        except Exception as e:
            logger.error(f"Failed to update documents in Weaviate: {str(e)}")
            raise

    @trace_function(name="weaviate_rag_delete_documents")
    @measure_latency("weaviate_rag_delete_documents")
    def delete_documents(self, document_ids: List[str], **kwargs) -> bool:
        """
        Delete documents from the vector store.

        Args:
            document_ids: List of document IDs to delete
            **kwargs: Additional implementation-specific arguments

        Returns:
            True if successful, False otherwise
        """
        try:
            # Delete documents from vector store
            with span("delete_from_vector_store"):
                result = self.vector_store.delete_documents(document_ids)

            logger.info(f"Deleted {len(document_ids)} documents from Weaviate")
            return result
        except Exception as e:
            logger.error(f"Failed to delete documents from Weaviate: {str(e)}")
            raise

    @trace_function(name="weaviate_rag_get_document")
    @measure_latency("weaviate_rag_get_document")
    def get_document(self, document_id: str, **kwargs) -> Optional[Dict[str, Any]]:
        """
        Get a document by ID.

        Args:
            document_id: Document ID
            **kwargs: Additional implementation-specific arguments

        Returns:
            Document dictionary or None if not found
        """
        try:
            # Get document from vector store
            with span("get_document_from_vector_store"):
                document = self.vector_store.get_document(document_id)

            if document:
                logger.info(f"Retrieved document {document_id} from Weaviate")
            else:
                logger.warning(f"Document {document_id} not found in Weaviate")

            return document
        except Exception as e:
            logger.error(f"Failed to get document from Weaviate: {str(e)}")
            raise

    @trace_function(name="weaviate_rag_get_documents")
    @measure_latency("weaviate_rag_get_documents")
    def get_documents(self, document_ids: List[str], **kwargs) -> List[Dict[str, Any]]:
        """
        Get multiple documents by ID.

        Args:
            document_ids: List of document IDs
            **kwargs: Additional implementation-specific arguments

        Returns:
            List of document dictionaries
        """
        try:
            # Get documents from vector store
            with span("get_documents_from_vector_store"):
                documents = self.vector_store.get_documents(document_ids)

            logger.info(f"Retrieved {len(documents)} documents from Weaviate")
            return documents
        except Exception as e:
            logger.error(f"Failed to get documents from Weaviate: {str(e)}")
            raise

    @trace_function(name="weaviate_rag_get_embedding")
    @measure_latency("weaviate_rag_get_embedding")
    def get_embedding(self, document_id: str, **kwargs) -> Optional[List[float]]:
        """
        Get the embedding for a document.

        Args:
            document_id: Document ID
            **kwargs: Additional implementation-specific arguments

        Returns:
            Embedding vector or None if not found
        """
        try:
            # Get embedding from vector store
            with span("get_embedding_from_vector_store"):
                embedding = self.vector_store.get_embedding(document_id)

            if embedding:
                logger.info(f"Retrieved embedding for document {document_id} from Weaviate")
            else:
                logger.warning(f"Embedding for document {document_id} not found in Weaviate")

            return embedding
        except Exception as e:
            logger.error(f"Failed to get embedding from Weaviate: {str(e)}")
            raise

    @trace_function(name="weaviate_rag_get_embeddings")
    @measure_latency("weaviate_rag_get_embeddings")
    def get_embeddings(self, document_ids: List[str], **kwargs) -> List[Optional[List[float]]]:
        """
        Get embeddings for multiple documents.

        Args:
            document_ids: List of document IDs
            **kwargs: Additional implementation-specific arguments

        Returns:
            List of embedding vectors (None for documents not found)
        """
        try:
            # Get embeddings from vector store
            with span("get_embeddings_from_vector_store"):
                embeddings = self.vector_store.get_embeddings(document_ids)

            logger.info(f"Retrieved embeddings for {len(document_ids)} documents from Weaviate")
            return embeddings
        except Exception as e:
            logger.error(f"Failed to get embeddings from Weaviate: {str(e)}")
            raise

    @trace_function(name="weaviate_rag_create_index")
    @measure_latency("weaviate_rag_create_index")
    def create_index(self, index_params: Optional[Dict[str, Any]] = None, **kwargs) -> bool:
        """
        Create or recreate the vector index with custom parameters.

        Args:
            index_params: Optional parameters for the index
            **kwargs: Additional implementation-specific arguments

        Returns:
            True if successful, False otherwise
        """
        try:
            # Create index in vector store
            with span("create_index_in_vector_store"):
                result = self.vector_store.create_index(index_params)

            logger.info(f"Created new Weaviate index with parameters: {index_params}")
            return result
        except Exception as e:
            logger.error(f"Failed to create Weaviate index: {str(e)}")
            raise

    @trace_function(name="weaviate_rag_optimize")
    @measure_latency("weaviate_rag_optimize")
    def optimize(self, **kwargs) -> bool:
        """
        Optimize the vector store for better performance.

        Note: Weaviate is automatically optimized, so this is a no-op.

        Args:
            **kwargs: Additional implementation-specific arguments

        Returns:
            True if successful, False otherwise
        """
        try:
            # Optimize vector store (no-op for Weaviate)
            with span("optimize_vector_store"):
                result = self.vector_store.optimize()

            logger.info("Weaviate is automatically optimized")
            return result
        except Exception as e:
            logger.error(f"Failed to optimize Weaviate: {str(e)}")
            raise

    @trace_function(name="weaviate_rag_backup")
    @measure_latency("weaviate_rag_backup")
    def backup(self, backup_path: str, **kwargs) -> bool:
        """
        Backup the vector store to a file.

        Note: Weaviate doesn't support direct backups through the client. This method provides a warning.

        Args:
            backup_path: Path to save the backup
            **kwargs: Additional implementation-specific arguments

        Returns:
            False as Weaviate doesn't support direct backups through the client
        """
        try:
            # Weaviate doesn't support direct backups through the client
            logger.warning("Weaviate doesn't support direct backups through the client. Use Weaviate's built-in backup features instead.")
            return False
        except Exception as e:
            logger.error(f"Failed to backup Weaviate: {str(e)}")
            raise

    @trace_function(name="weaviate_rag_restore")
    @measure_latency("weaviate_rag_restore")
    def restore(self, backup_path: str, **kwargs) -> bool:
        """
        Restore the vector store from a backup.

        Note: Weaviate doesn't support direct restores through the client. This method provides a warning.

        Args:
            backup_path: Path to the backup file
            **kwargs: Additional implementation-specific arguments

        Returns:
            False as Weaviate doesn't support direct restores through the client
        """
        try:
            # Weaviate doesn't support direct restores through the client
            logger.warning("Weaviate doesn't support direct restores through the client. Use Weaviate's built-in restore features instead.")
            return False
        except Exception as e:
            logger.error(f"Failed to restore Weaviate: {str(e)}")
            raise

    @trace_function(name="weaviate_rag_clear")
    @measure_latency("weaviate_rag_clear")
    def clear(self) -> bool:
        """Clear all documents from the vector store."""
        try:
            result = self.vector_store.clear()
            logger.info("Cleared all documents from Weaviate")
            return result
        except Exception as e:
            logger.error(f"Failed to clear Weaviate: {str(e)}")
            raise

    @trace_function(name="weaviate_rag_count")
    @measure_latency("weaviate_rag_count")
    def count(self) -> int:
        """Return the number of documents in the vector store."""
        try:
            count = self.vector_store.count()
            logger.info(f"Weaviate contains {count} documents")
            return count
        except Exception as e:
            logger.error(f"Failed to count documents in Weaviate: {str(e)}")
            raise

    @trace_function(name="weaviate_rag_close")
    @measure_latency("weaviate_rag_close")
    def close(self) -> None:
        """Close the vector store connection."""
        try:
            self.vector_store.close()
            logger.info("Closed connection to Weaviate")
        except Exception as e:
            logger.error(f"Failed to close Weaviate connection: {str(e)}")
            raise
