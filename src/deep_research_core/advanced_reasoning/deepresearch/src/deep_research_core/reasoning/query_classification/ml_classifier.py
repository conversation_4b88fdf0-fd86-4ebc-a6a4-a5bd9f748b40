"""
ML-Based Query Classifier.

This module implements a machine learning approach to classify queries into categories
based on trained models and feature extraction.
"""

import os
import numpy as np
import pickle
from typing import Dict, List, Tuple, Optional, Any, Union
import logging
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.pipeline import Pipeline
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix

from deep_research_core.reasoning.query_classification.base_classifier import BaseQueryClassifier
from deep_research_core.reasoning.query_classification.categories import QueryCategory, CATEGORY_EXAMPLES

logger = logging.getLogger(__name__)


class MLQueryClassifier(BaseQueryClassifier):
    """
    Machine learning-based query classifier.
    
    This classifier uses ML techniques to determine the category of a query
    based on training data from example queries.
    """
    
    def __init__(
        self,
        model_type: str = "random_forest",
        vectorizer_kwargs: Optional[Dict[str, Any]] = None,
        model_kwargs: Optional[Dict[str, Any]] = None,
        load_pretrained: bool = True,
        model_path: Optional[str] = None
    ):
        """
        Initialize the ML-based classifier.
        
        Args:
            model_type: Type of ML model to use ("random_forest" or "logistic_regression")
            vectorizer_kwargs: Keyword arguments for the TfidfVectorizer
            model_kwargs: Keyword arguments for the ML model
            load_pretrained: Whether to load a pretrained model if available
            model_path: Path to load/save the model (if None, uses default path)
        """
        # Initialize default parameters
        self.model_type = model_type
        self.vectorizer_kwargs = vectorizer_kwargs or {
            "ngram_range": (1, 2),
            "max_features": 1000,
            "min_df": 2
        }
        
        if model_type == "random_forest":
            self.model_kwargs = model_kwargs or {
                "n_estimators": 100,
                "max_depth": 10,
                "random_state": 42
            }
        else:  # logistic_regression
            self.model_kwargs = model_kwargs or {
                "C": 1.0,
                "max_iter": 1000,
                "random_state": 42,
                "multi_class": "auto"
            }
            
        # Define default model path
        self.model_path = model_path or os.path.join(
            os.path.dirname(__file__), 
            "models", 
            f"query_classifier_{model_type}.pkl"
        )
        
        # Create models directory if it doesn't exist
        os.makedirs(os.path.dirname(self.model_path), exist_ok=True)
        
        # Initialize vectorizer and model
        self.vectorizer = TfidfVectorizer(**self.vectorizer_kwargs)
        
        if model_type == "random_forest":
            self.model = RandomForestClassifier(**self.model_kwargs)
        else:  # logistic_regression
            self.model = LogisticRegression(**self.model_kwargs)
        
        # Category to index mapping
        self.category_to_idx = {category: i for i, category in enumerate(QueryCategory)}
        self.idx_to_category = {i: category for category, i in self.category_to_idx.items()}
        
        # Load pretrained model if requested
        self.is_trained = False
        if load_pretrained:
            try:
                self.load_model(self.model_path)
                self.is_trained = True
                logger.info(f"Loaded pretrained ML query classifier from {self.model_path}")
            except (FileNotFoundError, pickle.UnpicklingError):
                logger.warning(f"No pretrained model found at {self.model_path}, will train from scratch")
                self._train_with_examples()
        else:
            self._train_with_examples()
    
    def _train_with_examples(self):
        """Train the model with example queries from CATEGORY_EXAMPLES."""
        X = []
        y = []
        
        # Prepare training data
        for category, examples in CATEGORY_EXAMPLES.items():
            X.extend(examples)
            y.extend([category] * len(examples))
        
        # Convert categories to indices
        y_indices = [self.category_to_idx[category] for category in y]
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y_indices, test_size=0.2, random_state=42
        )
        
        # Fit vectorizer and transform data
        X_train_vec = self.vectorizer.fit_transform(X_train)
        
        # Train model
        self.model.fit(X_train_vec, y_train)
        
        # Evaluate model
        X_test_vec = self.vectorizer.transform(X_test)
        y_pred = self.model.predict(X_test_vec)
        accuracy = accuracy_score(y_test, y_pred)
        
        logger.info(f"Trained ML query classifier with accuracy: {accuracy:.4f}")
        
        # Save model
        self.save_model(self.model_path)
        self.is_trained = True
    
    def classify(self, query: str) -> QueryCategory:
        """
        Classify a query into a specific category using ML.
        
        Args:
            query: The input query string to classify
            
        Returns:
            The determined QueryCategory
        """
        if not self.is_trained:
            self._train_with_examples()
        
        # Handle Vietnamese queries
        is_viet = self.is_vietnamese(query)
        
        # Transform query
        query_vec = self.vectorizer.transform([query])
        
        # Predict category index
        category_idx = self.model.predict(query_vec)[0]
        category = self.idx_to_category[category_idx]
        
        # Map to Vietnamese category if needed
        if is_viet:
            if category == QueryCategory.FACTUAL:
                return QueryCategory.VIETNAMESE_FACTUAL
            elif category == QueryCategory.CREATIVE:
                return QueryCategory.VIETNAMESE_CREATIVE
            elif category == QueryCategory.ANALYTICAL:
                return QueryCategory.VIETNAMESE_ANALYTICAL
        
        return category
    
    def get_top_categories(self, query: str, n: int = 3) -> List[Tuple[QueryCategory, float]]:
        """
        Get the top N most likely categories for a query with confidence scores.
        
        Args:
            query: The input query string to classify
            n: Number of top categories to return
            
        Returns:
            List of tuples containing (category, confidence_score)
        """
        if not self.is_trained:
            self._train_with_examples()
        
        # Transform query
        query_vec = self.vectorizer.transform([query])
        
        # Get prediction probabilities
        if hasattr(self.model, "predict_proba"):
            proba = self.model.predict_proba(query_vec)[0]
            
            # Sort categories by probability
            top_indices = np.argsort(proba)[::-1][:n]
            result = [(self.idx_to_category[idx], float(proba[idx])) for idx in top_indices]
            
            return result
        else:
            # Fallback if model doesn't support probabilities
            category = self.classify(query)
            return [(category, 1.0)]
    
    def get_weight_distribution(self, query: str) -> Dict[str, float]:
        """
        Get the recommended weight distribution for RAG, TOT, and COT based on the query.
        
        Args:
            query: The input query string
            
        Returns:
            Dictionary with keys 'rag', 'tot', 'cot' and float values representing weights
        """
        category = self.classify(query)
        return category.weight_distribution
    
    def batch_classify(self, queries: List[str]) -> List[QueryCategory]:
        """
        Classify multiple queries at once.
        
        Args:
            queries: List of query strings to classify
            
        Returns:
            List of QueryCategory corresponding to each input query
        """
        if not self.is_trained:
            self._train_with_examples()
        
        # Transform queries
        queries_vec = self.vectorizer.transform(queries)
        
        # Predict category indices
        category_indices = self.model.predict(queries_vec)
        
        # Convert indices to categories
        categories = [self.idx_to_category[idx] for idx in category_indices]
        
        # Handle Vietnamese queries
        for i, query in enumerate(queries):
            if self.is_vietnamese(query):
                if categories[i] == QueryCategory.FACTUAL:
                    categories[i] = QueryCategory.VIETNAMESE_FACTUAL
                elif categories[i] == QueryCategory.CREATIVE:
                    categories[i] = QueryCategory.VIETNAMESE_CREATIVE
                elif categories[i] == QueryCategory.ANALYTICAL:
                    categories[i] = QueryCategory.VIETNAMESE_ANALYTICAL
        
        return categories
    
    def explain_classification(self, query: str) -> str:
        """
        Provide an explanation for why the query was classified in a certain category.
        
        Args:
            query: The input query string
            
        Returns:
            String explanation of the classification decision
        """
        if not self.is_trained:
            self._train_with_examples()
        
        category = self.classify(query)
        top_categories = self.get_top_categories(query, 3)
        
        # Extract feature importance
        query_vec = self.vectorizer.transform([query])
        
        # Get feature names
        feature_names = self.vectorizer.get_feature_names_out()
        
        # For Random Forest, we can extract feature importance
        if self.model_type == "random_forest":
            importances = self.model.feature_importances_
            query_features = query_vec.toarray()[0]
            
            # Get non-zero features in the query
            present_features = [(feature_names[i], query_features[i], importances[i]) 
                              for i in range(len(feature_names)) 
                              if query_features[i] > 0]
            
            # Sort by importance * value
            present_features.sort(key=lambda x: x[1] * x[2], reverse=True)
            
            # Build explanation
            explanation = [f"Query classified as {category.name} with confidence {top_categories[0][1]:.2f}"]
            
            if present_features:
                explanation.append("Top influential features:")
                for feature, value, importance in present_features[:5]:
                    explanation.append(f"- '{feature}' (value: {value:.2f}, importance: {importance:.2f})")
            
            explanation.append("Other possible categories:")
            for cat, conf in top_categories[1:]:
                explanation.append(f"- {cat.name}: {conf:.2f}")
            
            return "\n".join(explanation)
        else:
            # For logistic regression, we could extract coefficients but it's more complex
            # Simplified explanation for now
            explanation = [f"Query classified as {category.name} with confidence {top_categories[0][1]:.2f}"]
            explanation.append("Other possible categories:")
            for cat, conf in top_categories[1:]:
                explanation.append(f"- {cat.name}: {conf:.2f}")
            
            return "\n".join(explanation)
    
    def save_model(self, filepath: Optional[str] = None) -> None:
        """
        Save the classifier model to a file.
        
        Args:
            filepath: Path to save the model. If None, uses the default path.
        """
        filepath = filepath or self.model_path
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        # Create dictionary with model components
        model_data = {
            'vectorizer': self.vectorizer,
            'model': self.model,
            'category_to_idx': self.category_to_idx,
            'idx_to_category': self.idx_to_category,
            'is_trained': self.is_trained,
            'model_type': self.model_type
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)
    
    def load_model(self, filepath: Optional[str] = None) -> None:
        """
        Load a classifier model from a file.
        
        Args:
            filepath: Path to the saved model. If None, uses the default path.
        """
        filepath = filepath or self.model_path
        
        with open(filepath, 'rb') as f:
            model_data = pickle.load(f)
        
        self.vectorizer = model_data['vectorizer']
        self.model = model_data['model']
        self.category_to_idx = model_data['category_to_idx']
        self.idx_to_category = model_data['idx_to_category']
        self.is_trained = model_data['is_trained']
        self.model_type = model_data['model_type']
    
    def update_model(self, new_data: List[Tuple[str, QueryCategory]], 
                     epochs: int = 1) -> None:
        """
        Update the classifier with new training data.
        
        Args:
            new_data: List of (query, category) pairs for training
            epochs: Number of training epochs (ignored for sklearn models)
        """
        if not self.is_trained:
            self._train_with_examples()
        
        # Extract queries and categories
        X = [query for query, _ in new_data]
        y = [self.category_to_idx[category] for _, category in new_data]
        
        # Transform queries
        X_vec = self.vectorizer.transform(X)
        
        # Update model (partial_fit for incremental learning if available)
        if hasattr(self.model, "partial_fit"):
            classes = list(range(len(self.category_to_idx)))
            for _ in range(epochs):
                self.model.partial_fit(X_vec, y, classes=classes)
        else:
            # For models without partial_fit, we need to retrain with all data
            # This could be improved by storing previous data or using a model that supports incremental learning
            self.model.fit(X_vec, y)
        
        # Save updated model
        self.save_model(self.model_path) 