"""
Query Categories for RAG-TOT-COT system.

This module defines the different categories of queries that can be processed by the
RAG-TOT-COT system, along with example queries and descriptions for each category.
These categories are used for classification and strategy selection to optimize
the weight distribution between RAG, TOT, and COT techniques.
"""

from typing import Dict, List, Any, Tuple
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class QueryCategory(Enum):
    """
    Enumeration of query categories with their recommended weight distributions.
    
    Each category represents a different type of query and includes a recommended
    weight distribution for RAG, TOT, and COT techniques.
    
    Attributes:
        value (str): The name of the category
        weights (Tuple[float, float, float]): The recommended weights for (RAG, TOT, COT)
    """
    
    # Factual queries rely heavily on RAG for retrieval of information
    FACTUAL = ("factual", (0.7, 0.2, 0.1))
    
    # Creative queries benefit from Tree of Thought reasoning
    CREATIVE = ("creative", (0.2, 0.6, 0.2))
    
    # Analytical queries use a blend of all techniques with emphasis on Chain of Thought
    ANALYTICAL = ("analytical", (0.3, 0.3, 0.4))
    
    # Problem-solving queries benefit from structured Tree of Thought
    PROBLEM_SOLVING = ("problem_solving", (0.2, 0.5, 0.3))
    
    # Open-ended queries benefit from Tree of Thought exploration
    OPEN_ENDED = ("open_ended", (0.1, 0.6, 0.3))
    
    # Decision-making queries need structured reasoning from TOT and COT
    DECISION_MAKING = ("decision_making", (0.2, 0.4, 0.4))
    
    # Comparison queries benefit from structured analysis
    COMPARISON = ("comparison", (0.4, 0.3, 0.3))
    
    # Opinion queries benefit from Chain of Thought reasoning
    OPINION = ("opinion", (0.1, 0.3, 0.6))
    
    # Procedural queries benefit from step-by-step Chain of Thought
    PROCEDURAL = ("procedural", (0.3, 0.2, 0.5))
    
    # Definition queries rely heavily on RAG
    DEFINITION = ("definition", (0.8, 0.1, 0.1))
    
    # Clarification queries use Chain of Thought to explore alternatives
    CLARIFICATION = ("clarification", (0.3, 0.2, 0.5))
    
    # Vietnamese-specific categories with adjusted weights for better performance
    
    # Vietnamese factual queries may need more reasoning due to language nuances
    VIETNAMESE_FACTUAL = ("vietnamese_factual", (0.6, 0.2, 0.2))
    
    # Vietnamese creative queries
    VIETNAMESE_CREATIVE = ("vietnamese_creative", (0.2, 0.5, 0.3))
    
    # Vietnamese analytical queries
    VIETNAMESE_ANALYTICAL = ("vietnamese_analytical", (0.3, 0.3, 0.4))
    
    # Fallback category when no clear classification is possible
    UNKNOWN = ("unknown", (0.33, 0.33, 0.34))
    
    def __init__(self, value: str, weights: Tuple[float, float, float]):
        self._value_ = value
        self.weights = weights
    
    @property
    def weight_distribution(self) -> Dict[str, float]:
        """
        Get the weight distribution as a dictionary.
        
        Returns:
            Dictionary with keys 'rag', 'tot', 'cot' and their respective weight values
        """
        return {
            'rag': self.weights[0],
            'tot': self.weights[1],
            'cot': self.weights[2]
        }
    
    @classmethod
    def from_name(cls, name: str) -> 'QueryCategory':
        """
        Get a QueryCategory enum by its name.
        
        Args:
            name: The name of the category (case-insensitive)
            
        Returns:
            The corresponding QueryCategory enum
            
        Raises:
            ValueError: If no matching category is found
        """
        for category in cls:
            if category.value.lower() == name.lower():
                return category
        raise ValueError(f"No category found with name: {name}")
    
    @classmethod
    def for_vietnamese_query(cls, category: 'QueryCategory') -> 'QueryCategory':
        """
        Convert a regular category to its Vietnamese-specific equivalent if available.
        
        Args:
            category: The original category
            
        Returns:
            The Vietnamese-specific category if available, otherwise the original category
        """
        mapping = {
            cls.FACTUAL: cls.VIETNAMESE_FACTUAL,
            cls.CREATIVE: cls.VIETNAMESE_CREATIVE,
            cls.ANALYTICAL: cls.VIETNAMESE_ANALYTICAL
        }
        return mapping.get(category, category)


# Category descriptions and features
CATEGORY_DESCRIPTIONS = {
    QueryCategory.FACTUAL: "Straightforward information retrieval queries that seek concrete facts",
    QueryCategory.ANALYTICAL: "Queries that require analysis, comparison, or evaluation of information",
    QueryCategory.CREATIVE: "Queries that ask for creative or imaginative content generation",
    QueryCategory.PROCEDURAL: "Queries that ask for step-by-step instructions or processes",
    QueryCategory.OPINION: "Queries that ask for subjective judgments or opinions",
    QueryCategory.COMPARISON: "Queries that compare two or more things",
    QueryCategory.VIETNAMESE_FACTUAL: "Queries written in Vietnamese language about factual topics",
    QueryCategory.VIETNAMESE_CREATIVE: "Queries written in Vietnamese language about creative topics",
    QueryCategory.VIETNAMESE_ANALYTICAL: "Queries written in Vietnamese language about analytical topics",
    QueryCategory.PROBLEM_SOLVING: "Queries that require problem-solving and structured reasoning",
    QueryCategory.OPEN_ENDED: "Queries that require open-ended exploration and creativity",
    QueryCategory.DECISION_MAKING: "Queries that require decision-making and structured reasoning",
    QueryCategory.DEFINITION: "Queries that require definition or explanation of a term or concept",
    QueryCategory.CLARIFICATION: "Queries that require clarification or explanation of a concept or idea",
    QueryCategory.UNKNOWN: "Queries that do not fit into any other category"
}

# Example queries for each category
CATEGORY_EXAMPLES = {
    QueryCategory.FACTUAL: [
        "What is the capital of France?",
        "When was the Declaration of Independence signed?",
        "How many planets are in our solar system?",
        "What is the population of Tokyo?",
        "Who wrote the novel 'Pride and Prejudice'?"
    ],
    QueryCategory.ANALYTICAL: [
        "Compare and contrast renewable and non-renewable energy sources.",
        "What are the pros and cons of universal basic income?",
        "Analyze the impact of social media on modern communication.",
        "What factors contributed to the fall of the Roman Empire?",
        "How does inflation affect economic growth?"
    ],
    QueryCategory.CREATIVE: [
        "Write a short story about a time traveler.",
        "Create a poem about nature.",
        "Design a fictional animal that could exist in the Amazon rainforest.",
        "Imagine how transportation might evolve in the next 100 years.",
        "Generate an alternate history where the industrial revolution never happened."
    ],
    QueryCategory.PROCEDURAL: [
        "How do I reset my router?",
        "What are the steps to make sourdough bread?",
        "Explain how to change a flat tire.",
        "What is the process for obtaining a passport?",
        "How do I troubleshoot a slow computer?"
    ],
    QueryCategory.OPINION: [
        "What's the best way to learn a new language?",
        "Should artificial intelligence development be regulated?",
        "Is remote work better than office work?",
        "What's the most important skill for future success?",
        "Are electric vehicles truly better for the environment?"
    ],
    QueryCategory.COMPARISON: [
        "Compare quantum computing with classical computing and explain how each might impact cybersecurity in the future.",
        "What were the economic and social factors that led to the French Revolution, and how do they compare to modern revolutionary movements?",
        "Analyze the health benefits of Mediterranean and ketogenic diets, and suggest which might be better for someone with diabetes.",
        "How has climate change affected migration patterns globally, and what policies might best address climate refugees?",
        "Evaluate the different approaches to urban planning in European and Asian cities, and discuss their sustainability."
    ],
    QueryCategory.VIETNAMESE_FACTUAL: [
        "Việt Nam có bao nhiêu tỉnh thành?",
        "Nguyên nhân của chiến tranh Việt Nam là gì?",
        "Làm thế nào để nấu món phở bò truyền thống?",
        "Trường đại học nào tốt nhất ở Hà Nội?",
        "Tại sao người Việt Nam có thói quen ăn cơm ba bữa một ngày?"
    ],
    QueryCategory.VIETNAMESE_CREATIVE: [
        "Tạo ra một câu chuyện về một người du hành thời gian.",
        "Tạo ra một bài thơ về thiên nhiên.",
        "Thiết kế một con vật thực tế có thể tồn tại trong rừng nhiệt đới Amazon.",
        "Tưởng tượng cách thức vận tải có thể phát triển trong 100 năm tới.",
        "Tạo ra một lịch sử thay đổi mà không có cuộc cách mạng công nghiệp."
    ],
    QueryCategory.VIETNAMESE_ANALYTICAL: [
        "Phân tích các nguyên nhân của chiến tranh Việt Nam.",
        "So sánh các nguyên nhân của chiến tranh Việt Nam.",
        "Phân tích các yếu tố ảnh hưởng đến cách nấu món phở bò truyền thống.",
        "Phân tích các yếu tố ảnh hưởng đến cách đánh giá trường đại học tốt nhất ở Hà Nội.",
        "Phân tích các yếu tố ảnh hưởng đến thói quen ăn cơm ba bữa một ngày của người Việt Nam."
    ],
    QueryCategory.PROBLEM_SOLVING: [
        "Cách giải quyết vấn đề khi bạn không có mạng.",
        "Cách giải quyết vấn đề khi bạn không có bột sữa.",
        "Cách giải quyết vấn đề khi bạn không có đủ bánh xe.",
        "Cách giải quyết vấn đề khi bạn không có hộ chiếu.",
        "Cách giải quyết vấn đề khi bạn không có đủ bộ RAM."
    ],
    QueryCategory.OPEN_ENDED: [
        "Tạo ra một câu chuyện về một người du hành thời gian.",
        "Tạo ra một bài thơ về thiên nhiên.",
        "Thiết kế một con vật thực tế có thể tồn tại trong rừng nhiệt đới Amazon.",
        "Tưởng tượng cách thức vận tải có thể phát triển trong 100 năm tới.",
        "Tạo ra một lịch sử thay đổi mà không có cuộc cách mạng công nghiệp."
    ],
    QueryCategory.DECISION_MAKING: [
        "Cách đưa ra quyết định khi bạn không có đủ thông tin.",
        "Cách đưa ra quyết định khi bạn không có đủ thông tin.",
        "Cách đưa ra quyết định khi bạn không có đủ thông tin.",
        "Cách đưa ra quyết định khi bạn không có đủ thông tin.",
        "Cách đưa ra quyết định khi bạn không có đủ thông tin."
    ],
    QueryCategory.DEFINITION: [
        "Cách định nghĩa một thuật ngữ hoặc khái niệm.",
        "Cách định nghĩa một thuật ngữ hoặc khái niệm.",
        "Cách định nghĩa một thuật ngữ hoặc khái niệm.",
        "Cách định nghĩa một thuật ngữ hoặc khái niệm.",
        "Cách định nghĩa một thuật ngữ hoặc khái niệm."
    ],
    QueryCategory.CLARIFICATION: [
        "Cách giải thích hoặc làm rõ một khái niệm hoặc ý tưởng.",
        "Cách giải thích hoặc làm rõ một khái niệm hoặc ý tưởng.",
        "Cách giải thích hoặc làm rõ một khái niệm hoặc ý tưởng.",
        "Cách giải thích hoặc làm rõ một khái niệm hoặc ý tưởng.",
        "Cách giải thích hoặc làm rõ một khái niệm hoặc ý tưởng."
    ],
    QueryCategory.UNKNOWN: [
        "Câu hỏi không thể phân loại vào bất kỳ loại câu hỏi nào khác."
    ]
}

# Category features for rule-based classification
CATEGORY_FEATURES = {
    QueryCategory.FACTUAL: {
        "question_words": ["what", "when", "where", "who", "which", "how many", "how much"],
        "information_seeking": True,
        "analysis_required": False,
        "creativity_required": False,
        "step_by_step": False,
        "opinion_based": False,
        "average_length": "short",
        "keyword_indicators": ["list", "name", "define", "identify", "state"]
    },
    QueryCategory.ANALYTICAL: {
        "question_words": ["why", "how", "what"],
        "information_seeking": True,
        "analysis_required": True,
        "creativity_required": False,
        "step_by_step": False,
        "opinion_based": False,
        "average_length": "medium",
        "keyword_indicators": ["analyze", "compare", "contrast", "evaluate", "examine", 
                              "explain", "interpret", "investigate"]
    },
    QueryCategory.CREATIVE: {
        "question_words": ["can you", "could you", "would", "imagine"],
        "information_seeking": False,
        "analysis_required": False,
        "creativity_required": True,
        "step_by_step": False,
        "opinion_based": False,
        "average_length": "medium",
        "keyword_indicators": ["create", "design", "develop", "generate", "imagine", 
                              "invent", "write", "compose", "story", "poem"]
    },
    QueryCategory.PROCEDURAL: {
        "question_words": ["how do", "how to", "what steps", "what is the process"],
        "information_seeking": True,
        "analysis_required": False,
        "creativity_required": False,
        "step_by_step": True,
        "opinion_based": False,
        "average_length": "medium",
        "keyword_indicators": ["steps", "process", "procedure", "instructions", "guide", 
                              "how to", "method", "tutorial"]
    },
    QueryCategory.OPINION: {
        "question_words": ["should", "could", "would", "is it better", "what do you think"],
        "information_seeking": True,
        "analysis_required": True,
        "creativity_required": False,
        "step_by_step": False,
        "opinion_based": True,
        "average_length": "medium",
        "keyword_indicators": ["opinion", "perspective", "view", "thoughts", "believe", 
                              "consider", "recommend", "suggest", "best"]
    },
    QueryCategory.COMPARISON: {
        "question_words": ["compare", "contrast", "evaluate", "examine", "explain", "interpret"],
        "information_seeking": True,
        "analysis_required": True,
        "creativity_required": False,
        "step_by_step": False,
        "opinion_based": False,
        "average_length": "medium",
        "keyword_indicators": ["and", "also", "additionally", "furthermore", "multiple", 
                              "several parts", "comprehensive"]
    },
    QueryCategory.VIETNAMESE_FACTUAL: {
        "language": "vietnamese",
        "information_seeking": True,
        "analysis_required": False,
        "creativity_required": False,
        "step_by_step": False,
        "opinion_based": False,
        "average_length": "variable",
        "keyword_indicators": ["việt nam", "tiếng việt", "người việt"]
    },
    QueryCategory.VIETNAMESE_CREATIVE: {
        "language": "vietnamese",
        "information_seeking": False,
        "analysis_required": False,
        "creativity_required": True,
        "step_by_step": False,
        "opinion_based": False,
        "average_length": "variable",
        "keyword_indicators": ["việt nam", "tiếng việt", "người việt"]
    },
    QueryCategory.VIETNAMESE_ANALYTICAL: {
        "language": "vietnamese",
        "information_seeking": True,
        "analysis_required": False,
        "creativity_required": False,
        "step_by_step": False,
        "opinion_based": False,
        "average_length": "variable",
        "keyword_indicators": ["việt nam", "tiếng việt", "người việt"]
    },
    QueryCategory.PROBLEM_SOLVING: {
        "question_words": ["how do", "how to", "what steps", "what is the process"],
        "information_seeking": True,
        "analysis_required": False,
        "creativity_required": False,
        "step_by_step": True,
        "opinion_based": False,
        "average_length": "medium",
        "keyword_indicators": ["steps", "process", "procedure", "instructions", "guide", 
                              "how to", "method", "tutorial"]
    },
    QueryCategory.OPEN_ENDED: {
        "question_words": ["can you", "could you", "would", "imagine"],
        "information_seeking": False,
        "analysis_required": False,
        "creativity_required": True,
        "step_by_step": False,
        "opinion_based": False,
        "average_length": "medium",
        "keyword_indicators": ["create", "design", "develop", "generate", "imagine", 
                              "invent", "write", "compose", "story", "poem"]
    },
    QueryCategory.DECISION_MAKING: {
        "question_words": ["should", "could", "would", "is it better", "what do you think"],
        "information_seeking": True,
        "analysis_required": True,
        "creativity_required": False,
        "step_by_step": False,
        "opinion_based": True,
        "average_length": "medium",
        "keyword_indicators": ["opinion", "perspective", "view", "thoughts", "believe", 
                              "consider", "recommend", "suggest", "best"]
    },
    QueryCategory.DEFINITION: {
        "question_words": ["what is", "what are", "how is", "how are", "who is", "who are", "when is", "when are", "where is", "where are", "why is", "why are", "which is", "which are"],
        "information_seeking": True,
        "analysis_required": False,
        "creativity_required": False,
        "step_by_step": False,
        "opinion_based": False,
        "average_length": "medium",
        "keyword_indicators": ["define", "explain", "describe", "identify", "state"]
    },
    QueryCategory.CLARIFICATION: {
        "question_words": ["why", "how", "what"],
        "information_seeking": True,
        "analysis_required": False,
        "creativity_required": False,
        "step_by_step": False,
        "opinion_based": False,
        "average_length": "medium",
        "keyword_indicators": ["clarify", "explain", "elaborate", "provide", "clarification"]
    },
    QueryCategory.UNKNOWN: {
        "question_words": ["what", "when", "where", "who", "which", "how many", "how much"],
        "information_seeking": True,
        "analysis_required": False,
        "creativity_required": False,
        "step_by_step": False,
        "opinion_based": False,
        "average_length": "medium",
        "keyword_indicators": ["list", "name", "define", "identify", "state"]
    }
}

# Suggested weight distributions for each category
CATEGORY_WEIGHTS = {
    QueryCategory.FACTUAL: {"rag": 0.7, "tot": 0.2, "cot": 0.1},
    QueryCategory.ANALYTICAL: {"rag": 0.3, "tot": 0.3, "cot": 0.4},
    QueryCategory.CREATIVE: {"rag": 0.2, "tot": 0.6, "cot": 0.2},
    QueryCategory.PROCEDURAL: {"rag": 0.3, "tot": 0.2, "cot": 0.5},
    QueryCategory.OPINION: {"rag": 0.1, "tot": 0.3, "cot": 0.6},
    QueryCategory.COMPARISON: {"rag": 0.4, "tot": 0.3, "cot": 0.3},
    QueryCategory.VIETNAMESE_FACTUAL: {"rag": 0.6, "tot": 0.2, "cot": 0.2},
    QueryCategory.VIETNAMESE_CREATIVE: {"rag": 0.2, "tot": 0.5, "cot": 0.3},
    QueryCategory.VIETNAMESE_ANALYTICAL: {"rag": 0.3, "tot": 0.3, "cot": 0.4},
    QueryCategory.PROBLEM_SOLVING: {"rag": 0.2, "tot": 0.5, "cot": 0.3},
    QueryCategory.OPEN_ENDED: {"rag": 0.1, "tot": 0.6, "cot": 0.3},
    QueryCategory.DECISION_MAKING: {"rag": 0.2, "tot": 0.4, "cot": 0.4},
    QueryCategory.DEFINITION: {"rag": 0.8, "tot": 0.1, "cot": 0.1},
    QueryCategory.CLARIFICATION: {"rag": 0.3, "tot": 0.2, "cot": 0.5},
    QueryCategory.UNKNOWN: {"rag": 0.33, "tot": 0.33, "cot": 0.34}
}


def get_category_info(category: QueryCategory) -> Dict[str, Any]:
    """
    Get comprehensive information about a query category.
    
    Args:
        category: The query category to get information about
        
    Returns:
        Dictionary containing description, examples, features and weight distribution
    """
    return {
        "description": CATEGORY_DESCRIPTIONS.get(category, "No description available"),
        "examples": CATEGORY_EXAMPLES.get(category, []),
        "features": CATEGORY_FEATURES.get(category, {}),
        "weights": CATEGORY_WEIGHTS.get(category, {"rag": 0.33, "tot": 0.33, "cot": 0.34})
    }


def list_all_categories() -> List[str]:
    """
    Get a list of all available query categories.
    
    Returns:
        List of category names
    """
    return [category.value for category in QueryCategory]


def get_all_category_weights() -> Dict[str, Dict[str, float]]:
    """
    Get all category weight distributions.
    
    Returns:
        Dictionary mapping category names to weight distributions
    """
    return {category.value: weights for category, weights in CATEGORY_WEIGHTS.items()} 