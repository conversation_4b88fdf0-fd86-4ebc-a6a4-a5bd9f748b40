"""
Base Classifier for Query Classification.

This module defines the base abstract interface for all query classifiers
used in the RAG-TOT-COT system. Classifiers determine the category of a query
to optimize weight distribution between RAG, TOT, and COT techniques.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Tuple, Union
import logging
import pickle
import re

from deep_research_core.reasoning.query_classification.categories import QueryCategory

logger = logging.getLogger(__name__)


class BaseQueryClassifier(ABC):
    """
    Abstract base class for query classifiers.
    
    All query classifier implementations must inherit from this class and
    implement the required abstract methods.
    """
    
    @abstractmethod
    def classify(self, query: str) -> QueryCategory:
        """
        Classify a query into a specific category.
        
        Args:
            query: The input query string to classify
            
        Returns:
            The determined QueryCategory
        """
        pass
    
    @abstractmethod
    def get_top_categories(self, query: str, n: int = 3) -> List[Tuple[QueryCategory, float]]:
        """
        Get the top N most likely categories for a query with confidence scores.
        
        Args:
            query: The input query string to classify
            n: Number of top categories to return
            
        Returns:
            List of tuples containing (category, confidence_score)
        """
        pass
    
    @abstractmethod
    def get_weight_distribution(self, query: str) -> Dict[str, float]:
        """
        Get the recommended weight distribution for RAG, TOT, and COT based on the query.
        
        Args:
            query: The input query string
            
        Returns:
            Dictionary with keys 'rag', 'tot', 'cot' and float values representing weights
        """
        pass
    
    @abstractmethod
    def batch_classify(self, queries: List[str]) -> List[QueryCategory]:
        """
        Classify multiple queries at once.
        
        Args:
            queries: List of query strings to classify
            
        Returns:
            List of QueryCategory corresponding to each input query
        """
        pass
    
    def explain_classification(self, query: str) -> str:
        """
        Provide an explanation for why the query was classified in a certain category.
        
        Args:
            query: The input query string
            
        Returns:
            String explanation of the classification decision
        """
        return f"Classification explanation not implemented for {self.__class__.__name__}"
    
    def is_vietnamese(self, text: str) -> bool:
        """
        Detect if a text is in Vietnamese language.
        
        Args:
            text: Input text to analyze
            
        Returns:
            True if text is likely Vietnamese, False otherwise
        """
        # Check for Vietnamese-specific characters
        vietnamese_chars = re.compile(r'[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]', 
                                      re.IGNORECASE)
        
        # Count Vietnamese characters
        viet_char_count = len(vietnamese_chars.findall(text))
        
        # If significant Vietnamese characters are present, consider it Vietnamese
        return viet_char_count > 0 and viet_char_count / len(text) > 0.05
    
    def save_model(self, filepath: str) -> None:
        """
        Save the classifier model to a file.
        
        Args:
            filepath: Path to save the model
        """
        with open(filepath, 'wb') as f:
            pickle.dump(self, f)
    
    @classmethod
    def load_model(cls, filepath: str) -> 'BaseQueryClassifier':
        """
        Load a classifier model from a file.
        
        Args:
            filepath: Path to the saved model
            
        Returns:
            Loaded classifier instance
        """
        with open(filepath, 'rb') as f:
            return pickle.load(f)
    
    def update_model(self, new_data: List[Tuple[str, QueryCategory]], 
                     epochs: int = 1) -> None:
        """
        Update the classifier with new training data.
        
        Args:
            new_data: List of (query, category) pairs for training
            epochs: Number of training epochs
        """
        raise NotImplementedError(
            f"Model updating not implemented for {self.__class__.__name__}"
        ) 