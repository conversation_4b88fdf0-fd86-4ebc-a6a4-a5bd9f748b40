"""
Rule-Based Query Classifier.

This module implements a rule-based approach to classify queries into categories
based on keyword matching, patterns, and linguistic features.
"""

import re
from typing import Dict, List, Tuple, Set, Optional
import logging
from collections import Counter

from deep_research_core.reasoning.query_classification.base_classifier import BaseQueryClassifier
from deep_research_core.reasoning.query_classification.categories import QueryCategory

logger = logging.getLogger(__name__)

class RuleBasedQueryClassifier(BaseQueryClassifier):
    """
    Rule-based query classifier that uses keyword matching and patterns.
    
    This classifier uses predefined rules, keywords, and patterns to determine
    the category of a query. It's a simple but effective approach for many
    common query types.
    """
    
    def __init__(self):
        """Initialize the rule-based classifier with keyword dictionaries and patterns."""
        self._initialize_category_keywords()
        self._initialize_patterns()
        self._initialize_vietnamese_specific_rules()
    
    def is_vietnamese(self, text: str) -> bool:
        """
        Determine if the given text is likely in Vietnamese.
        
        Args:
            text: The text to check
            
        Returns:
            True if the text is likely Vietnamese, False otherwise
        """
        # Vietnamese specific characters
        viet_chars = {'ă', 'â', 'đ', 'ê', 'ô', 'ơ', 'ư', 'á', 'à', 'ả', 'ã', 'ạ', 
                     'ắ', 'ằ', 'ẳ', 'ẵ', 'ặ', 'ấ', 'ầ', 'ẩ', 'ẫ', 'ậ', 'é', 'è', 
                     'ẻ', 'ẽ', 'ẹ', 'ế', 'ề', 'ể', 'ễ', 'ệ', 'í', 'ì', 'ỉ', 'ĩ', 
                     'ị', 'ó', 'ò', 'ỏ', 'õ', 'ọ', 'ố', 'ồ', 'ổ', 'ỗ', 'ộ', 'ớ', 
                     'ờ', 'ở', 'ỡ', 'ợ', 'ú', 'ù', 'ủ', 'ũ', 'ụ', 'ứ', 'ừ', 'ử', 
                     'ữ', 'ự', 'ý', 'ỳ', 'ỷ', 'ỹ', 'ỵ'}
        
        # Common Vietnamese words
        viet_words = {'là', 'và', 'của', 'có', 'không', 'được', 'các', 'trong', 
                     'cho', 'này', 'đã', 'về', 'như', 'những', 'để', 'với', 
                     'tôi', 'bạn', 'anh', 'chị'}
        
        # Check for Vietnamese characters
        for char in text.lower():
            if char in viet_chars:
                return True
        
        # Check for Vietnamese words
        text_words = set(text.lower().split())
        if text_words.intersection(viet_words):
            return True
        
        return False
    
    def _initialize_category_keywords(self) -> None:
        """Initialize dictionaries of keywords for each category."""
        # Dictionary mapping categories to sets of associated keywords
        self.category_keywords: Dict[QueryCategory, Set[str]] = {
            QueryCategory.FACTUAL: {
                'what', 'who', 'when', 'where', 'which', 'how many', 'define', 'explain',
                'identify', 'list', 'tell me about', 'information on', 'facts about', 
                'history of', 'details on', 'tell me', 'describe', 'what is', 'who is'
            },
            
            QueryCategory.CREATIVE: {
                'create', 'generate', 'imagine', 'story', 'design', 'invent', 'write', 
                'compose', 'develop', 'suggest', 'fiction', 'creative', 'novel', 'poem',
                'song', 'narrative', 'script', 'scenario', 'alternative'
            },
            
            QueryCategory.ANALYTICAL: {
                'analyze', 'compare', 'contrast', 'evaluate', 'examine', 'assess', 
                'review', 'critique', 'investigate', 'study', 'research', 'explore',
                'pros and cons', 'benefits', 'drawbacks', 'advantages', 'disadvantages',
                'implications', 'considerations'
            },
            
            QueryCategory.PROCEDURAL: {
                'how to', 'steps', 'process', 'procedure', 'method', 'technique', 
                'instructions', 'guide', 'tutorial', 'walkthrough', 'implement',
                'make', 'build', 'create a', 'develop a', 'setup', 'install',
                'configure', 'assemble', 'operate'
            },
            
            QueryCategory.OPINION: {
                'opinion', 'think', 'feel', 'belief', 'view', 'stance', 'position',
                'perspective', 'judgment', 'recommendation', 'suggest', 'advise',
                'should', 'would', 'could', 'better', 'best', 'worst', 'appropriate',
                'recommend', 'advice'
            },
            
            QueryCategory.PROBLEM_SOLVING: {
                'solve', 'solution', 'problem', 'issue', 'fix', 'resolve', 'address',
                'overcome', 'tackle', 'approach', 'strategy', 'debug', 'troubleshoot',
                'investigate', 'diagnose', 'identify issue', 'root cause', 'workaround'
            },
            
            QueryCategory.OPEN_ENDED: {
                'brainstorm', 'explore', 'possibilities', 'ideas', 'options', 'alternatives',
                'scenarios', 'what if', 'imagine if', 'consider', 'contemplate', 'hypothetical',
                'conceptualize', 'envision', 'think about', 'ponder', 'reflect on'
            },
            
            QueryCategory.DECISION_MAKING: {
                'decide', 'choose', 'select', 'option', 'alternative', 'best choice',
                'recommendation', 'should I', 'better to', 'decision', 'trade-off',
                'pros and cons', 'benefits', 'drawbacks', 'compare options'
            },
            
            QueryCategory.COMPARISON: {
                'compare', 'contrast', 'difference', 'similarity', 'versus', 'vs',
                'better than', 'worse than', 'advantages over', 'disadvantages of',
                'preferred', 'superior', 'inferior', 'alternative to', 'comparison'
            },
            
            QueryCategory.DEFINITION: {
                'define', 'definition', 'meaning', 'what is', 'what does', 'explain term',
                'describe concept', 'terminology', 'stands for', 'referred to as', 'called',
                'known as', 'understand term', 'describe term'
            },
            
            QueryCategory.CLARIFICATION: {
                'clarify', 'explain', 'elaborate', 'detail', 'specifics', 'more information',
                'understand', 'confused about', 'unclear', 'not sure about', 'could you explain',
                'what do you mean', 'expand on', 'provide context'
            },
            
            QueryCategory.VIETNAMESE_FACTUAL: {
                'là gì', 'ai là', 'khi nào', 'ở đâu', 'cái nào', 'bao nhiêu', 
                'định nghĩa', 'giải thích', 'liệt kê', 'cho biết về', 'thông tin về',
                'sự kiện về', 'lịch sử của', 'chi tiết về', 'mô tả', 'miêu tả'
            },
            
            QueryCategory.VIETNAMESE_CREATIVE: {
                'tạo ra', 'viết', 'sáng tác', 'thiết kế', 'hình dung', 'tưởng tượng',
                'câu chuyện', 'phát triển', 'đề xuất', 'hư cấu', 'sáng tạo', 'tiểu thuyết',
                'bài thơ', 'bài hát', 'kịch bản', 'tình huống'
            },
            
            QueryCategory.VIETNAMESE_ANALYTICAL: {
                'phân tích', 'so sánh', 'đối chiếu', 'đánh giá', 'xem xét', 'thẩm định',
                'nhận xét', 'phê bình', 'điều tra', 'nghiên cứu', 'ưu điểm', 'nhược điểm',
                'lợi ích', 'hạn chế', 'tác động', 'cân nhắc'
            }
        }
    
    def _initialize_patterns(self) -> None:
        """Initialize regex patterns for query classification."""
        self.patterns = {
            QueryCategory.FACTUAL: [
                re.compile(r'^(what|who|when|where|which|why|how) (is|are|was|were|do|does|did)', re.IGNORECASE),
                re.compile(r'^(define|explain|describe|tell me about)\s', re.IGNORECASE)
            ],
            
            QueryCategory.CREATIVE: [
                re.compile(r'^(create|generate|write|design|compose|develop)\s(a|an|the)?\s', re.IGNORECASE),
                re.compile(r'(story|poem|song|fictional|creative|imaginary)', re.IGNORECASE)
            ],
            
            QueryCategory.ANALYTICAL: [
                re.compile(r'^(analyze|compare|contrast|evaluate|examine|assess)\s', re.IGNORECASE),
                re.compile(r'(pros\s+and\s+cons|advantages\s+and\s+disadvantages)', re.IGNORECASE)
            ],
            
            QueryCategory.PROCEDURAL: [
                re.compile(r'^(how\s+to|steps\s+to|instructions\s+for)\s', re.IGNORECASE),
                re.compile(r'(procedure|tutorial|guide|walkthrough)', re.IGNORECASE)
            ],
            
            QueryCategory.OPINION: [
                re.compile(r'(your\s+opinion|do\s+you\s+think|what\s+do\s+you\s+think|should\s+I)', re.IGNORECASE),
                re.compile(r'(\badvise\b|\brecommend\b)', re.IGNORECASE)
            ],
            
            QueryCategory.PROBLEM_SOLVING: [
                re.compile(r'(solve|fix|debug|troubleshoot|resolve)\s(a|an|the)?\s(problem|issue|error|bug)', re.IGNORECASE),
                re.compile(r'(not\s+working|doesn\'t\s+work|failed|error|exception|issue)', re.IGNORECASE)
            ],
            
            QueryCategory.OPEN_ENDED: [
                re.compile(r'(brainstorm|explore|consider|possibilities|ideas|options)', re.IGNORECASE),
                re.compile(r'(what\s+if|imagine\s+if|hypothetical)', re.IGNORECASE)
            ],
            
            QueryCategory.DECISION_MAKING: [
                re.compile(r'(decide|choose|select|recommend|should\s+I|best\s+option)', re.IGNORECASE),
                re.compile(r'(decision|trade\s+off|pros\s+and\s+cons)', re.IGNORECASE)
            ],
            
            # Vietnamese patterns
            QueryCategory.VIETNAMESE_FACTUAL: [
                re.compile(r'(là\s+gì|ai\s+là|ở\s+đâu|khi\s+nào|tại\s+sao)'),
                re.compile(r'(giải\s+thích|định\s+nghĩa|mô\s+tả|cho\s+biết\s+về)')
            ],
            
            QueryCategory.VIETNAMESE_CREATIVE: [
                re.compile(r'(viết|sáng\s+tác|thiết\s+kế|tạo\s+ra)'),
                re.compile(r'(câu\s+chuyện|hư\s+cấu|sáng\s+tạo|tưởng\s+tượng)')
            ],
            
            QueryCategory.VIETNAMESE_ANALYTICAL: [
                re.compile(r'(phân\s+tích|so\s+sánh|đánh\s+giá|xem\s+xét)'),
                re.compile(r'(ưu\s+điểm|nhược\s+điểm|lợi\s+ích|hạn\s+chế)')
            ]
        }
    
    def _initialize_vietnamese_specific_rules(self) -> None:
        """Initialize Vietnamese-specific rules and mapping."""
        # Mapping from general categories to Vietnamese-specific categories
        self.viet_category_mapping = {
            QueryCategory.FACTUAL: QueryCategory.VIETNAMESE_FACTUAL,
            QueryCategory.CREATIVE: QueryCategory.VIETNAMESE_CREATIVE,
            QueryCategory.ANALYTICAL: QueryCategory.VIETNAMESE_ANALYTICAL,
            # For other categories, we'll use the general ones
        }
    
    def classify(self, query: str) -> QueryCategory:
        """
        Classify a query into a specific category using rule-based approach.
        
        Args:
            query: The input query string to classify
            
        Returns:
            The determined QueryCategory
        """
        # Get scores for each category
        category_scores = self._score_categories(query)
        
        # Return the category with the highest score
        if category_scores:
            return max(category_scores.items(), key=lambda x: x[1])[0]
        
        # Default to FACTUAL if no strong match found
        if self.is_vietnamese(query):
            return QueryCategory.VIETNAMESE_FACTUAL
        return QueryCategory.FACTUAL
    
    def _score_categories(self, query: str) -> Dict[QueryCategory, float]:
        """
        Score each category based on keyword matches and patterns.
        
        Args:
            query: The input query string
            
        Returns:
            Dictionary mapping categories to their confidence scores
        """
        scores: Dict[QueryCategory, float] = {category: 0.0 for category in QueryCategory}
        
        # Check if query is in Vietnamese
        is_viet = self.is_vietnamese(query)
        query_lower = query.lower()
        
        # Score based on keywords
        for category, keywords in self.category_keywords.items():
            for keyword in keywords:
                if keyword.lower() in query_lower:
                    scores[category] += 1.0
        
        # Score based on patterns
        for category, pattern_list in self.patterns.items():
            for pattern in pattern_list:
                if pattern.search(query):
                    scores[category] += 2.0  # Patterns given higher weight than keywords
        
        # Apply Vietnamese mapping if needed
        if is_viet:
            for general_cat, viet_cat in self.viet_category_mapping.items():
                # Transfer some score from general category to Vietnamese-specific category
                if scores[general_cat] > 0:
                    transfer = scores[general_cat] * 0.8
                    scores[general_cat] -= transfer
                    scores[viet_cat] += transfer
        
        # Normalize scores
        total_score = sum(scores.values())
        if total_score > 0:
            scores = {cat: score / total_score for cat, score in scores.items()}
        
        # Filter out categories with very low scores
        return {cat: score for cat, score in scores.items() if score > 0.05}
    
    def get_top_categories(self, query: str, n: int = 3) -> List[Tuple[QueryCategory, float]]:
        """
        Get the top N most likely categories for a query with confidence scores.
        
        Args:
            query: The input query string to classify
            n: Number of top categories to return
            
        Returns:
            List of tuples containing (category, confidence_score)
        """
        scores = self._score_categories(query)
        sorted_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        return sorted_scores[:n]
    
    def get_weight_distribution(self, query: str) -> Dict[str, float]:
        """
        Get the recommended weight distribution for RAG, TOT, and COT based on the query.
        
        Args:
            query: The input query string
            
        Returns:
            Dictionary with keys 'rag', 'tot', 'cot' and float values representing weights
        """
        category = self.classify(query)
        return {
            'rag': category.weights[0],
            'tot': category.weights[1],
            'cot': category.weights[2]
        }
    
    def batch_classify(self, queries: List[str]) -> List[QueryCategory]:
        """
        Classify multiple queries at once.
        
        Args:
            queries: List of query strings to classify
            
        Returns:
            List of QueryCategory corresponding to each input query
        """
        return [self.classify(query) for query in queries]
    
    def explain_classification(self, query: str) -> str:
        """
        Provide an explanation for why the query was classified in a certain category.
        
        Args:
            query: The input query string
            
        Returns:
            String explanation of the classification decision
        """
        category = self.classify(query)
        scores = self._score_categories(query)
        sorted_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:3]
        
        # Find matching keywords
        matched_keywords = []
        for keyword in self.category_keywords.get(category, []):
            if keyword.lower() in query.lower():
                matched_keywords.append(keyword)
        
        # Check for pattern matches
        pattern_matches = []
        for pattern in self.patterns.get(category, []):
            if pattern.search(query):
                pattern_matches.append(pattern.pattern)
        
        # Build explanation
        explanation = [f"Query classified as {category.name} with confidence {scores.get(category, 0):.2f}"]
        
        if matched_keywords:
            explanation.append(f"Matched keywords: {', '.join(matched_keywords[:5])}")
        
        if pattern_matches:
            explanation.append(f"Matched patterns: {len(pattern_matches)} patterns")
        
        if len(sorted_scores) > 1:
            explanation.append("Other possible categories:")
            for cat, score in sorted_scores[1:]:
                explanation.append(f"- {cat.name}: {score:.2f}")
        
        if self.is_vietnamese(query):
            explanation.append("Query detected as Vietnamese")
        
        return "\n".join(explanation) 