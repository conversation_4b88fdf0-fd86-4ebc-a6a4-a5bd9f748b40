"""
Enhanced CoTRAG Implementation.

This module provides an enhanced implementation of CoTRAG that combines
all the advanced features and improvements.
"""

import logging
import time
from functools import lru_cache
from typing import Dict, Any, List, Optional, Union, Callable, Tuple

from .cot_rag import CoTRAG
from .cotrag_vietnamese import CoTRAGVietnamese
from .cotrag_adaptive_learning import CoTRAGAdaptiveLearning
from .cotrag_advanced_strategies import CoTRAGAdvancedStrategies
from ..utils.structured_logging import get_logger
from ..utils.performance_metrics import measure_latency
from ..utils.distributed_tracing import trace_function

# Create a logger
logger = get_logger(__name__)

class CoTRAGEnhanced:
    """
    Enhanced implementation of CoTRAG that combines all advanced features.
    
    This class integrates Vietnamese language support, adaptive learning,
    and advanced strategies into a single comprehensive implementation.
    """
    
    def __init__(self, use_cache: bool = True, cache_size: int = 100, provider: str = "openai",
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        vector_store=None,
        language: str = "en",
        vietnamese_embedding_model: str = "phobert",
        use_adaptive_learning: bool = True,
        use_advanced_strategies: bool = True,
        learning_rate: float = 0.01,
        feedback_history_path: Optional[str] = None,
        use_query_expansion: bool = True,
        use_iterative_retrieval: bool = True,
        use_fallback_strategies: bool = True,
        adaptive: bool = True,
        use_cache: bool = True,
        evaluate_results: bool = False,
        use_dynamic_weighting: bool = True,
        min_cot_weight: float = 0.3,
        max_cot_weight: float = 0.8,
        default_cot_weight: float = 0.5,
        weighting_strategy: str = "auto",
        handle_irrelevant_docs: bool = True,
        relevance_threshold: float = 0.3,
        analyze_errors: bool = False,
        verbose: bool = False,
        device: Optional[str] = None,
        cache_dir: Optional[str] = None
    ):
        """
        Initialize CoTRAGEnhanced.
        
        Args:
            provider: The provider to use ("openai", "anthropic", etc.)
            model: The model to use (if None, will use provider's default)
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            vector_store: Vector store to use for retrieval
            language: Language to use ("en", "vi")
            vietnamese_embedding_model: Name of the Vietnamese embedding model to use
            use_adaptive_learning: Whether to use adaptive learning
            use_advanced_strategies: Whether to use advanced strategies
            learning_rate: Rate at which parameters are adjusted based on feedback
            feedback_history_path: Path to store feedback history
            use_query_expansion: Whether to use query expansion for retrieval
            use_iterative_retrieval: Whether to use iterative retrieval
            use_fallback_strategies: Whether to use fallback strategies
            adaptive: Whether to use adaptive parameter adjustment
            use_cache: Whether to use caching for repeated queries
            evaluate_results: Whether to evaluate reasoning quality
            use_dynamic_weighting: Whether to dynamically adjust weights between CoT and RAG
            min_cot_weight: Minimum weight for CoT (0.0 to 1.0)
            max_cot_weight: Maximum weight for CoT (0.0 to 1.0)
            default_cot_weight: Default weight for CoT (0.0 to 1.0)
            weighting_strategy: Weight adjustment strategy ("auto", "query_type", 
                              "query_complexity", "document_relevance")
            handle_irrelevant_docs: Whether to handle cases when RAG returns irrelevant documents
            relevance_threshold: Threshold for document relevance (0.0 to 1.0)
            analyze_errors: Whether to analyze errors in results
            verbose: Whether to log detailed information
            device: Device to use for inference ("cpu", "cuda", "mps")
            cache_dir: Directory to cache models
        """
        self.language = language
        self.use_adaptive_learning = use_adaptive_learning
        self.use_advanced_strategies = use_advanced_strategies
        self.verbose = verbose
        
        # Create the appropriate CoTRAG implementation based on configuration
        if language == "vi":
            # Vietnamese implementation
            self.cotrag = CoTRAGVietnamese(
                provider=provider,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                vector_store=vector_store,
                adaptive=adaptive,
                use_cache=use_cache,
                evaluate_results=evaluate_results,
                use_dynamic_weighting=use_dynamic_weighting,
                min_cot_weight=min_cot_weight,
                max_cot_weight=max_cot_weight,
                default_cot_weight=default_cot_weight,
                weighting_strategy=weighting_strategy,
                handle_irrelevant_docs=handle_irrelevant_docs,
                relevance_threshold=relevance_threshold,
                analyze_errors=analyze_errors,
                verbose=verbose,
                vietnamese_embedding_model=vietnamese_embedding_model,
                device=device,
                cache_dir=cache_dir
            )
            # Set up caching
        if use_cache:
            self._generate_cached = lru_cache(maxsize=cache_size)(self._generate)
        else:
            self._generate_cached = self._generate
            
        logger.info(f"Initialized Vietnamese CoTRAG with model: {vietnamese_embedding_model}")
        
        elif use_advanced_strategies:
            # Advanced strategies implementation
            self.cotrag = CoTRAGAdvancedStrategies(
                provider=provider,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                vector_store=vector_store,
                adaptive=adaptive,
                use_cache=use_cache,
                evaluate_results=evaluate_results,
                use_dynamic_weighting=use_dynamic_weighting,
                min_cot_weight=min_cot_weight,
                max_cot_weight=max_cot_weight,
                default_cot_weight=default_cot_weight,
                weighting_strategy=weighting_strategy,
                handle_irrelevant_docs=handle_irrelevant_docs,
                relevance_threshold=relevance_threshold,
                analyze_errors=analyze_errors,
                verbose=verbose,
                use_query_expansion=use_query_expansion,
                use_iterative_retrieval=use_iterative_retrieval,
                use_fallback_strategies=use_fallback_strategies
            )
            logger.info("Initialized CoTRAG with advanced strategies")
        
        else:
            # Base implementation
            self.cotrag = CoTRAG(
                provider=provider,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                vector_store=vector_store,
                adaptive=adaptive,
                use_cache=use_cache,
                evaluate_results=evaluate_results,
                use_dynamic_weighting=use_dynamic_weighting,
                min_cot_weight=min_cot_weight,
                max_cot_weight=max_cot_weight,
                default_cot_weight=default_cot_weight,
                weighting_strategy=weighting_strategy,
                handle_irrelevant_docs=handle_irrelevant_docs,
                relevance_threshold=relevance_threshold,
                analyze_errors=analyze_errors,
                verbose=verbose
            )
            logger.info("Initialized base CoTRAG")
        
        # Wrap with adaptive learning if enabled
        if use_adaptive_learning:
            self.adaptive_learning = CoTRAGAdaptiveLearning(
                provider=provider,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                vector_store=vector_store,
                adaptive=adaptive,
                use_cache=use_cache,
                evaluate_results=evaluate_results,
                use_dynamic_weighting=use_dynamic_weighting,
                min_cot_weight=min_cot_weight,
                max_cot_weight=max_cot_weight,
                default_cot_weight=default_cot_weight,
                weighting_strategy=weighting_strategy,
                handle_irrelevant_docs=handle_irrelevant_docs,
                relevance_threshold=relevance_threshold,
                analyze_errors=analyze_errors,
                verbose=verbose,
                learning_rate=learning_rate,
                feedback_history_path=feedback_history_path
            )
            logger.info(f"Added adaptive learning with learning rate: {learning_rate}")
        else:
            self.adaptive_learning = None
    
    @trace_function(name="cotrag_enhanced_process")
    @measure_latency("cotrag_enhanced_process")
    def process(
        self,
        query: str,
        top_k: int = 5,
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        force_refresh: bool = False,
        expected_answer: Optional[str] = None,
        auto_feedback: bool = False
    ) -> Dict[str, Any]:
        """
        Process a query using the enhanced CoTRAG implementation.
        
        Args:
            query: The query to process
            top_k: Number of documents to retrieve
            custom_system_prompt: Custom system prompt to use
            custom_user_prompt: Custom user prompt template to use
            callback: Optional callback function for streaming
            force_refresh: Whether to force a refresh (ignore cache)
            expected_answer: Optional expected answer for error analysis
            auto_feedback: Whether to automatically generate feedback
            
        Returns:
            Dict containing the query, retrieved documents, reasoning, and answer
        """
        start_time = time.time()
        
        # Process with the appropriate implementation
        if self.use_adaptive_learning and self.adaptive_learning:
            result = self.adaptive_learning.process(
                query=query,
                top_k=top_k,
                custom_system_prompt=custom_system_prompt,
                custom_user_prompt=custom_user_prompt,
                callback=callback,
                force_refresh=force_refresh,
                expected_answer=expected_answer,
                auto_feedback=auto_feedback
            )
        else:
            result = self.cotrag.process(
                query=query,
                top_k=top_k,
                custom_system_prompt=custom_system_prompt,
                custom_user_prompt=custom_user_prompt,
                callback=callback,
                force_refresh=force_refresh,
                expected_answer=expected_answer
            )
        
        # Add enhanced metadata
        result["enhanced"] = True
        result["language"] = self.language
        result["use_adaptive_learning"] = self.use_adaptive_learning
        result["use_advanced_strategies"] = self.use_advanced_strategies
        
        # Calculate total latency
        result["total_latency"] = time.time() - start_time
        
        return result
    
    def add_feedback(
        self,
        query: str,
        result: Dict[str, Any],
        feedback_score: float,
        feedback_type: str = "user",
        feedback_notes: Optional[str] = None
    ) -> None:
        """
        Add feedback for a query result.
        
        Args:
            query: The original query
            result: The result from processing the query
            feedback_score: Score from 0.0 (poor) to 1.0 (excellent)
            feedback_type: Type of feedback ("user", "automatic", "expert")
            feedback_notes: Optional notes about the feedback
        """
        if self.use_adaptive_learning and self.adaptive_learning:
            self.adaptive_learning.add_feedback(
                query=query,
                result=result,
                feedback_score=feedback_score,
                feedback_type=feedback_type,
                feedback_notes=feedback_notes
            )
        else:
            logger.warning("Adaptive learning is not enabled, feedback will not be stored")
    
    def adjust_parameters(self) -> Dict[str, Any]:
        """
        Adjust parameters based on feedback history.
        
        Returns:
            Dictionary with adjusted parameters
        """
        if self.use_adaptive_learning and self.adaptive_learning:
            return self.adaptive_learning.adjust_parameters()
        else:
            logger.warning("Adaptive learning is not enabled, parameters cannot be adjusted")
            return {}
    
    def get_parameter_stats(self) -> Dict[str, Any]:
        """
        Get statistics about parameters and their performance.
        
        Returns:
            Dictionary with parameter statistics
        """
        if self.use_adaptive_learning and self.adaptive_learning:
            return self.adaptive_learning.get_parameter_stats()
        else:
            logger.warning("Adaptive learning is not enabled, parameter statistics not available")
            return {}
    
    def analyze_feedback_trends(self) -> Dict[str, Any]:
        """
        Analyze trends in feedback history.
        
        Returns:
            Dictionary with feedback trends
        """
        if self.use_adaptive_learning and self.adaptive_learning:
            return self.adaptive_learning.analyze_feedback_trends()
        else:
            logger.warning("Adaptive learning is not enabled, feedback trends not available")
            return {"message": "Adaptive learning is not enabled"}
    
    def analyze_query(self, query: str) -> Dict[str, Any]:
        """
        Analyze a query to determine optimal processing parameters.
        
        Args:
            query: The query to analyze
            
        Returns:
            Dictionary with detailed analysis
        """
        return self.cotrag.analyze_query(query)
    
    def retrieve(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        Retrieve relevant documents for a query.
        
        Args:
            query: The query to retrieve documents for
            top_k: Number of documents to retrieve
            
        Returns:
            List of retrieved documents
        """
        return self.cotrag.retrieve(query, top_k=top_k)
