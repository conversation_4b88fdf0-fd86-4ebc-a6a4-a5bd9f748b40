"""
SQLite-based Vector Retrieval-Augmented Generation (RAG) with R-tree index.

This module extends the SQLiteVectorRAG implementation with R-tree indexing for efficient
spatial search of vector embeddings.
"""

import os
import time
import sqlite3
import numpy as np
from functools import lru_cache
from typing import Dict, Any, List, Optional, Tuple, Union, Callable

from .sqlite_vector_rag import SQLiteVectorRAG
from ..utils.structured_logging import get_logger
from ..utils.performance_metrics import measure_latency, measure_block_latency
from ..utils.distributed_tracing import trace_function, span

# Create a logger
logger = get_logger(__name__)

class SQLiteVectorRAGWithRTree(SQLiteVectorRAG):
    """
    Extends SQLiteVectorRAG with R-tree indexing for efficient spatial search.
    
    This class improves the vector search performance of SQLiteVectorRAG by using
    SQLite's R-tree extension for efficient spatial indexing of vector embeddings.
    While not as fast as dedicated ANN libraries like FAISS, it provides a good
    balance between performance and simplicity by keeping everything in SQLite.
    """
    
    def __init__(self, use_cache: bool = True, cache_size: int = 100, db_path: str,
        embedding_model: str = "all-MiniLM-L6-v2",
        provider: str = "openrouter",
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        top_k: int = 5,
        chunk_size: int = 1000,
        chunk_overlap: int = 200,
        api_key: Optional[str] = None,
        pca_dimensions: int = 20,
        use_rtree: bool = True
    ):
        """
        Initialize SQLiteVectorRAGWithRTree.
        
        Args:
            db_path: Path to the SQLite database
            embedding_model: Name of the sentence-transformers model to use
            provider: Model provider (openai, anthropic, openrouter)
            model: Model name
            temperature: Temperature for text generation
            max_tokens: Maximum number of tokens to generate
            top_k: Number of documents to retrieve
            chunk_size: Size of document chunks
            chunk_overlap: Overlap between document chunks
            api_key: API key for the provider
            pca_dimensions: Number of dimensions to use for PCA reduction (R-tree works best with fewer dimensions)
            use_rtree: Whether to use R-tree indexing (if False, falls back to brute-force)
        """
        # Initialize the parent class
        super().__init__(
            db_path=db_path,
            embedding_model=embedding_model,
            provider=provider,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            top_k=top_k,
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            api_key=api_key
        )
        
        # Store R-tree parameters
        self.pca_dimensions = min(pca_dimensions, self.embedding_dim)
        self.use_rtree = use_rtree
        
        # Initialize PCA for dimension reduction
        if self.use_rtree:
            from sklearn.decomposition import PCA
            self.pca = PCA(n_components=self.pca_dimensions)
            self.pca_trained = False
            
            # Initialize R-tree index
            self._init_rtree()
    
    def _init_rtree(self) -> None:
        """
        Initialize the R-tree index in the SQLite database.
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Enable R-tree extension
        cursor.execute("PRAGMA module_list")
        modules = cursor.fetchall()
        rtree_available = any(module[0] == 'rtree' for module in modules)
        
        if not rtree_available:
            logger.warning("SQLite R-tree extension not available, falling back to brute-force search")
            self.use_rtree = False
            conn.close()
            return
        
        # Create R-tree virtual table if it doesn't exist
        cursor.execute("""
        CREATE VIRTUAL TABLE IF NOT EXISTS embedding_rtree USING rtree(
            id,                  -- Integer primary key
            min_d1, max_d1,      -- Dimension 1
            min_d2, max_d2,      -- Dimension 2
            min_d3, max_d3,      -- Dimension 3
            min_d4, max_d4,      -- Dimension 4
            min_d5, max_d5,      -- Dimension 5
            min_d6, max_d6,      -- Dimension 6
            min_d7, max_d7,      -- Dimension 7
            min_d8, max_d8,      -- Dimension 8
            min_d9, max_d9,      -- Dimension 9
            min_d10, max_d10,    -- Dimension 10
            min_d11, max_d11,    -- Dimension 11
            min_d12, max_d12,    -- Dimension 12
            min_d13, max_d13,    -- Dimension 13
            min_d14, max_d14,    -- Dimension 14
            min_d15, max_d15,    -- Dimension 15
            min_d16, max_d16,    -- Dimension 16
            min_d17, max_d17,    -- Dimension 17
            min_d18, max_d18,    -- Dimension 18
            min_d19, max_d19,    -- Dimension 19
            min_d20, max_d20     -- Dimension 20
        )
        """)
        
        # Create a table to store the full embeddings
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS embedding_vectors (
            id INTEGER PRIMARY KEY,
            doc_id INTEGER NOT NULL,
            embedding BLOB NOT NULL,
            FOREIGN KEY (doc_id) REFERENCES documents (id) ON DELETE CASCADE
        )
        """)
        
        # Create index on doc_id
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_embedding_vectors_doc_id ON embedding_vectors (doc_id)")
        
        conn.commit()
        conn.close()
        
        logger.info("Initialized R-tree index")
    
    def _train_pca(self) -> None:
        """
        Train the PCA model on existing document embeddings.
        """
        if not self.use_rtree:
            return
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Get all document embeddings
        cursor.execute("SELECT embedding FROM documents")
        rows = cursor.fetchall()
        
        if not rows:
            logger.warning("No documents found, PCA not trained")
            conn.close()
            return
        
        # Extract embeddings
        embeddings = []
        for row in rows:
            embeddings.append(self._deserialize_embedding(row[0]))
        
        # Convert to numpy array
        embeddings_array = np.array(embeddings)
        
        # Train PCA
        self.pca.fit(embeddings_array)
        self.pca_trained = True
        
        conn.close()
        
        logger.info(f"Trained PCA with {len(embeddings)} embeddings")
    
    def _reduce_dimensions(self, embedding: np.ndarray) -> np.ndarray:
        """
        Reduce the dimensionality of an embedding using PCA.
        
        Args:
            embedding: The embedding to reduce
            
        Returns:
            Reduced embedding
        """
        if not self.use_rtree:
            return embedding
        
        # Train PCA if not already trained
        if not self.pca_trained:
            self._train_pca()
            
            # If still not trained, return original embedding
            if not self.pca_trained:
                return embedding[:self.pca_dimensions]
        
        # Reshape for single sample
        if len(embedding.shape) == 1:
            embedding = embedding.reshape(1, -1)
        
        # Reduce dimensions
        reduced = self.pca.transform(embedding)
        
        # Return as 1D array
        return reduced[0]
    
    def _add_to_rtree(self, doc_id: int, embedding: np.ndarray) -> None:
        """
        Add a document embedding to the R-tree index.
        
        Args:
            doc_id: Document ID
            embedding: Document embedding
        """
        if not self.use_rtree:
            return
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Reduce dimensions
        reduced_embedding = self._reduce_dimensions(embedding)
        
        # Ensure we have the right number of dimensions
        if len(reduced_embedding) < self.pca_dimensions:
            # Pad with zeros
            reduced_embedding = np.pad(reduced_embedding, (0, self.pca_dimensions - len(reduced_embedding)))
        elif len(reduced_embedding) > self.pca_dimensions:
            # Truncate
            reduced_embedding = reduced_embedding[:self.pca_dimensions]
        
        # Store the full embedding
        cursor.execute(
            "INSERT INTO embedding_vectors (doc_id, embedding) VALUES (?, ?)",
            (doc_id, self._serialize_embedding(embedding))
        )
        
        # Get the ID of the inserted embedding
        embedding_id = cursor.lastrowid
        
        # Create parameters for R-tree insertion
        rtree_params = [embedding_id]
        for i in range(self.pca_dimensions):
            # For each dimension, add min and max (same value for point data)
            rtree_params.extend([float(reduced_embedding[i]), float(reduced_embedding[i])])
        
        # Create placeholders for the SQL query
        placeholders = ", ".join(["?"] * (1 + self.pca_dimensions * 2))
        
        # Insert into R-tree
        cursor.execute(f"INSERT INTO embedding_rtree VALUES ({placeholders})", rtree_params)
        
        conn.commit()
        conn.close()
    
    def _search_rtree(self, query_embedding: np.ndarray, top_k: int) -> List[Tuple[int, float]]:
        """
        Search for similar documents using the R-tree index.
        
        Args:
            query_embedding: Query embedding
            top_k: Number of results to return
            
        Returns:
            List of tuples (doc_id, similarity_score)
        """
        if not self.use_rtree:
            return []
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Reduce dimensions
        reduced_query = self._reduce_dimensions(query_embedding)
        
        # Ensure we have the right number of dimensions
        if len(reduced_query) < self.pca_dimensions:
            # Pad with zeros
            reduced_query = np.pad(reduced_query, (0, self.pca_dimensions - len(reduced_query)))
        elif len(reduced_query) > self.pca_dimensions:
            # Truncate
            reduced_query = reduced_query[:self.pca_dimensions]
        
        # Calculate the search radius (adjust as needed)
        search_radius = 0.5
        
        # Create the WHERE clause for the R-tree query
        where_clauses = []
        for i in range(self.pca_dimensions):
            dim = i + 1
            min_val = reduced_query[i] - search_radius
            max_val = reduced_query[i] + search_radius
            where_clauses.append(f"min_d{dim} <= {max_val} AND max_d{dim} >= {min_val}")
        
        where_clause = " AND ".join(where_clauses)
        
        # Query the R-tree
        cursor.execute(f"""
        SELECT e.id, e.doc_id, v.embedding
        FROM embedding_rtree e
        JOIN embedding_vectors v ON e.id = v.id
        WHERE {where_clause}
        """)
        
        rows = cursor.fetchall()
        
        # Calculate actual similarities using the full embeddings
        similarities = []
        for row in rows:
            doc_id = row[1]
            doc_embedding = self._deserialize_embedding(row[2])
            
            # Calculate cosine similarity
            similarity = np.dot(query_embedding, doc_embedding) / (np.linalg.norm(query_embedding) * np.linalg.norm(doc_embedding))
            
            similarities.append((doc_id, float(similarity)))
        
        # Sort by similarity (descending)
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        # Take top_k results
        top_results = similarities[:top_k]
        
        conn.close()
        
        return top_results
    
    @trace_function(name="sqlite_vector_rag_rtree_add_documents")
    @measure_latency("sqlite_vector_rag_rtree_add_documents")
    def add_documents(
        self,
        documents: List[Dict[str, Any]],
        update_existing: bool = True,
        auto_chunk: bool = True
    ) -> List[int]:
        """
        Add documents to the database and update the R-tree index.
        
        Args:
            documents: List of document dictionaries with 'content', 'source', etc.
            update_existing: Whether to update existing documents with the same hash
            auto_chunk: Whether to automatically chunk long documents
            
        Returns:
            List of IDs of the inserted or updated documents
        """
        # Add documents using the parent class method
        doc_ids = super().add_documents(documents, update_existing, auto_chunk)
        
        # If using R-tree and documents were added, update the R-tree index
        if self.use_rtree and doc_ids:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get the embeddings for the added documents
            placeholders = ",".join(["?"] * len(doc_ids))
            cursor.execute(f"SELECT id, embedding FROM documents WHERE id IN ({placeholders})", doc_ids)
            rows = cursor.fetchall()
            
            # Add to R-tree
            for row in rows:
                doc_id = row[0]
                embedding = self._deserialize_embedding(row[1])
                self._add_to_rtree(doc_id, embedding)
            
            conn.close()
            
            # Retrain PCA if needed
            if not self.pca_trained:
                self._train_pca()
        
        return doc_ids
    
    @trace_function(name="sqlite_vector_rag_rtree_search")
    @measure_latency("sqlite_vector_rag_rtree_search")
    def search(
        self,
        query: str,
        top_k: Optional[int] = None,
        hybrid_weight: float = 0.7,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Search for documents similar to the query using R-tree and keyword search.
        
        Args:
            query: The query string
            top_k: Number of results to return (defaults to self.top_k)
            hybrid_weight: Weight for vector search vs keyword search (1.0 = vector only, 0.0 = keyword only)
            **kwargs: Additional arguments
            
        Returns:
            List of dictionaries containing the retrieved documents and their similarity scores
        """
        if top_k is None:
            top_k = self.top_k
        
        # If not using R-tree or hybrid_weight is 0, fall back to parent class method
        if not self.use_rtree or hybrid_weight == 0.0:
            return super().search(query, top_k, hybrid_weight, **kwargs)
        
        # Generate embedding for the query
        query_embedding = self._get_embeddings([query])[0]
        
        # Perform R-tree search
        rtree_results = self._search_rtree(query_embedding, top_k=top_k * 2)  # Get more results for hybrid reranking
        
        # If no results from R-tree or not enough results, fall back to brute-force
        if len(rtree_results) < top_k:
            logger.info(f"R-tree search returned only {len(rtree_results)} results, falling back to brute-force")
            return super().search(query, top_k, hybrid_weight, **kwargs)
        
        # If hybrid search is enabled, combine with keyword search
        if hybrid_weight < 1.0:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Get document IDs from R-tree search
            rtree_doc_ids = [doc_id for doc_id, _ in rtree_results]
            
            # Get documents from database
            placeholders = ",".join(["?"] * len(rtree_doc_ids))
            cursor.execute(f"SELECT id, content, source, title, date FROM documents WHERE id IN ({placeholders})", rtree_doc_ids)
            rows = cursor.fetchall()
            
            # Create a mapping from document ID to row
            doc_rows = {row["id"]: row for row in rows}
            
            # Calculate keyword scores
            keyword_scores = {}
            query_terms = set(query.lower().split())
            
            for doc_id, row in doc_rows.items():
                content_terms = set(row["content"].lower().split())
                matching_terms = query_terms.intersection(content_terms)
                keyword_score = len(matching_terms) / max(len(query_terms), 1)
                keyword_scores[doc_id] = keyword_score
            
            # Combine scores
            combined_scores = []
            for doc_id, rtree_score in rtree_results:
                if doc_id in keyword_scores:
                    keyword_score = keyword_scores[doc_id]
                    combined_score = (hybrid_weight * rtree_score) + ((1 - hybrid_weight) * keyword_score)
                    combined_scores.append((doc_id, combined_score))
            
            # Sort by combined score
            combined_scores.sort(key=lambda x: x[1], reverse=True)
            
            # Take top_k results
            top_results = combined_scores[:top_k]
            
            # Format results
            results = []
            for doc_id, score in top_results:
                # Get document details
                row = doc_rows.get(doc_id)
                if row:
                    result = {
                        "id": row["id"],
                        "content": row["content"],
                        "source": row["source"],
                        "title": row["title"],
                        "date": row["date"],
                        "score": float(score),
                        "metadata": {
                            "source": row["source"],
                            "title": row["title"],
                            "date": row["date"]
                        }
                    }
                    results.append(result)
            
            conn.close()
            
            return results
        else:
            # Use R-tree results directly
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Get document details
            results = []
            for doc_id, score in rtree_results[:top_k]:
                cursor.execute("SELECT id, content, source, title, date FROM documents WHERE id = ?", (doc_id,))
                row = cursor.fetchone()
                
                if row:
                    result = {
                        "id": row["id"],
                        "content": row["content"],
                        "source": row["source"],
                        "title": row["title"],
                        "date": row["date"],
                        "score": float(score),
                        "metadata": {
                            "source": row["source"],
                            "title": row["title"],
                            "date": row["date"]
                        }
                    }
                    results.append(result)
            
            conn.close()
            
            return results
    
    def clear(self) -> None:
        """
        Clear the database and R-tree index.
        """
        # Clear the database using the parent class method
        super().clear()
        
        # Clear the R-tree index
        if self.use_rtree:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("DELETE FROM embedding_rtree")
            cursor.execute("DELETE FROM embedding_vectors")
            
            conn.commit()
            conn.close()
            
            # Reset PCA
            self.pca_trained = False
