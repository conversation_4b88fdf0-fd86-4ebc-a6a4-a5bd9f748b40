"""
CoTRAG with Multi-Query Support.

This module extends the CoTRAG implementation with multi-query support,
including the ability to handle queries with multiple sub-questions.
"""

import re
import logging
import time
from functools import lru_cache
from typing import Dict, Any, List, Optional, Union, Callable, Tuple

from .cot_rag import CoTRAG
from ..utils.structured_logging import get_logger
from ..utils.performance_metrics import measure_latency
from ..utils.distributed_tracing import trace_function

# Create a logger
logger = get_logger(__name__)

class CoTRAGMultiQuery(CoTRAG):
    """
    Extends CoTRAG with multi-query support.
    
    This class adds the ability to handle queries with multiple sub-questions,
    breaking them down and processing each part separately before combining
    the results.
    """
    
    def __init__(self, use_cache: bool = True, cache_size: int = 100, provider: str = "openai",
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        vector_store=None,
        adaptive: bool = True,
        use_cache: bool = True,
        evaluate_results: bool = False,
        use_dynamic_weighting: bool = True,
        min_cot_weight: float = 0.3,
        max_cot_weight: float = 0.8,
        default_cot_weight: float = 0.5,
        weighting_strategy: str = "auto",
        handle_irrelevant_docs: bool = True,
        relevance_threshold: float = 0.3,
        analyze_errors: bool = False,
        verbose: bool = False,
        auto_detect_multi_query: bool = True,
        max_sub_queries: int = 5,
        combine_results: bool = True
    ):
        """
        Initialize CoTRAGMultiQuery.
        
        Args:
            provider: The provider to use ("openai", "anthropic", etc.)
            model: The model to use (if None, will use provider's default)
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            vector_store: Vector store to use for retrieval
            adaptive: Whether to use adaptive parameter adjustment
            use_cache: Whether to use caching for repeated queries
            evaluate_results: Whether to evaluate reasoning quality
            use_dynamic_weighting: Whether to dynamically adjust weights between CoT and RAG
            min_cot_weight: Minimum weight for CoT (0.0 to 1.0)
            max_cot_weight: Maximum weight for CoT (0.0 to 1.0)
            default_cot_weight: Default weight for CoT (0.0 to 1.0)
            weighting_strategy: Weight adjustment strategy ("auto", "query_type", 
                              "query_complexity", "document_relevance")
            handle_irrelevant_docs: Whether to handle cases when RAG returns irrelevant documents
            relevance_threshold: Threshold for document relevance (0.0 to 1.0)
            analyze_errors: Whether to analyze errors in results
            verbose: Whether to log detailed information
            auto_detect_multi_query: Whether to automatically detect multi-part queries
            max_sub_queries: Maximum number of sub-queries to process
            combine_results: Whether to combine results from sub-queries
        """
        # Initialize the base CoTRAG class
        super().__init__(
            provider=provider,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            vector_store=vector_store,
            adaptive=adaptive,
            use_cache=use_cache,
            evaluate_results=evaluate_results,
            use_dynamic_weighting=use_dynamic_weighting,
            min_cot_weight=min_cot_weight,
            max_cot_weight=max_cot_weight,
            default_cot_weight=default_cot_weight,
            weighting_strategy=weighting_strategy,
            handle_irrelevant_docs=handle_irrelevant_docs,
            relevance_threshold=relevance_threshold,
            analyze_errors=analyze_errors,
            verbose=verbose
        )
        
        # Store multi-query parameters
        self.auto_detect_multi_query = auto_detect_multi_query
        self.max_sub_queries = max_sub_queries
        self.combine_results = combine_results
        
        # Set up caching
        if use_cache:
            self._generate_cached = lru_cache(maxsize=cache_size)(self._generate)
        else:
            self._generate_cached = self._generate
            
        logger.info(f"Initialized CoTRAGMultiQuery with auto_detect_multi_query={auto_detect_multi_query}")
    
    def _is_multi_query(self, query: str) -> bool:
        """
        Detect if a query contains multiple sub-questions.
        
        Args:
            query: The query to analyze
            
        Returns:
            True if the query contains multiple sub-questions, False otherwise
        """
        # Check for numbered questions (e.g., "1. What is...? 2. How does...?")
        numbered_pattern = r'\b\d+\s*[.)][\s\n]+\w+'
        if re.search(numbered_pattern, query) and query.count('?') > 1:
            return True
        
        # Check for multiple question marks with different contexts
        if query.count('?') > 1:
            # Split by question marks
            parts = query.split('?')
            
            # Check if parts have substantial content (not just follow-up phrases)
            content_parts = [p.strip() for p in parts[:-1] if len(p.strip()) > 10]
            
            if len(content_parts) > 1:
                return True
        
        # Check for explicit markers like "questions:", "multiple questions", etc.
        markers = ["questions:", "multiple questions", "several questions", "answer these questions"]
        for marker in markers:
            if marker.lower() in query.lower():
                return True
        
        return False
    
    def _extract_sub_queries(self, query: str) -> List[str]:
        """
        Extract sub-queries from a multi-part query.
        
        Args:
            query: The multi-part query
            
        Returns:
            List of sub-queries
        """
        # Try to extract using AI
        try:
            system_prompt = """You are a helpful assistant that extracts individual questions from a multi-part query.
            Extract each distinct question as a separate, complete question.
            Format your response as a numbered list with ONLY the questions, one per line.
            Do not include any explanations or additional text."""
            
            user_prompt = f"Extract the individual questions from this multi-part query:\n\n{query}"
            
            response = self.api_provider.generate(
                prompt=user_prompt,
                model=self.model,
                system_prompt=system_prompt,
                temperature=0.3,
                max_tokens=1000
            )
            
            # Extract questions from the response
            sub_queries = []
            for line in response.strip().split("\n"):
                # Remove numbering and leading/trailing whitespace
                clean_line = re.sub(r"^\d+[\.\)]\s*", "", line).strip()
                if clean_line and "?" in clean_line:
                    sub_queries.append(clean_line)
            
            if sub_queries:
                return sub_queries[:self.max_sub_queries]
        
        except Exception as e:
            logger.error(f"Error extracting sub-queries using AI: {str(e)}")
        
        # Fallback: Extract using regex patterns
        sub_queries = []
        
        # Try to extract numbered questions
        numbered_pattern = r'\b(\d+\s*[.)][\s\n]+[^?]+\?)'
        numbered_matches = re.findall(numbered_pattern, query)
        
        if numbered_matches:
            sub_queries = [m.strip() for m in numbered_matches]
        else:
            # Try to split by question marks
            parts = query.split('?')
            
            for i, part in enumerate(parts[:-1]):
                if len(part.strip()) > 10:
                    sub_query = part.strip() + '?'
                    sub_queries.append(sub_query)
        
        # Limit to max_sub_queries
        return sub_queries[:self.max_sub_queries]
    
    def _combine_sub_results(
        self,
        query: str,
        sub_queries: List[str],
        sub_results: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Combine results from multiple sub-queries.
        
        Args:
            query: The original multi-part query
            sub_queries: List of sub-queries
            sub_results: List of results for each sub-query
            
        Returns:
            Combined result
        """
        # Extract answers from sub-results
        sub_answers = []
        all_documents = []
        
        for i, (sub_query, sub_result) in enumerate(zip(sub_queries, sub_results)):
            sub_answers.append({
                "question": sub_query,
                "answer": sub_result["answer"],
                "reasoning": sub_result.get("reasoning", "")
            })
            
            # Collect unique documents
            for doc in sub_result.get("documents", []):
                if doc not in all_documents:
                    all_documents.append(doc)
        
        # Create a prompt to combine the answers
        system_prompt = """You are a helpful assistant that combines answers to multiple sub-questions into a comprehensive response.
        Create a well-structured, cohesive answer that addresses all parts of the original query.
        Use the provided answers to sub-questions as your source material.
        Make sure to maintain the accuracy and completeness of each individual answer.
        Organize your response logically, with clear transitions between different parts of the answer."""
        
        user_prompt = f"""Original multi-part query: {query}

Sub-questions and answers:
"""
        
        for i, sub_answer in enumerate(sub_answers):
            user_prompt += f"\nQuestion {i+1}: {sub_answer['question']}\nAnswer {i+1}: {sub_answer['answer']}\n"
        
        user_prompt += "\nPlease combine these answers into a comprehensive response to the original query."
        
        # Generate combined answer
        combined_reasoning = self.api_provider.generate(
            prompt=user_prompt,
            model=self.model,
            system_prompt=system_prompt,
            temperature=self.temperature,
            max_tokens=self.max_tokens
        )
        
        # Extract final answer
        combined_answer = self._extract_final_answer(combined_reasoning)
        
        # Calculate average weights
        avg_weights = {}
        if all("weights" in sub_result for sub_result in sub_results):
            for key in sub_results[0]["weights"].keys():
                avg_weights[key] = sum(sub_result["weights"].get(key, 0) for sub_result in sub_results) / len(sub_results)
        
        # Create combined result
        combined_result = {
            "query": query,
            "documents": all_documents,
            "reasoning": combined_reasoning,
            "answer": combined_answer,
            "weights": avg_weights,
            "model": self.model,
            "provider": self.provider,
            "multi_query": {
                "sub_queries": sub_queries,
                "sub_answers": [sub_result["answer"] for sub_result in sub_results],
                "sub_results": sub_results
            }
        }
        
        return combined_result
    
    @trace_function(name="cotrag_multi_query_process")
    @measure_latency("cotrag_multi_query_process")
    def process(
        self,
        query: str,
        top_k: int = 5,
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        force_refresh: bool = False,
        expected_answer: Optional[str] = None,
        is_multi_query: Optional[bool] = None,
        sub_queries: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Process a query that may contain multiple sub-questions.
        
        Args:
            query: The query to process
            top_k: Number of documents to retrieve
            custom_system_prompt: Custom system prompt to use
            custom_user_prompt: Custom user prompt template to use
            callback: Optional callback function for streaming
            force_refresh: Whether to force a refresh (ignore cache)
            expected_answer: Optional expected answer for error analysis
            is_multi_query: Whether the query contains multiple sub-questions
            sub_queries: Optional list of sub-queries to process
            
        Returns:
            Dict containing the query, retrieved documents, reasoning, and answer
        """
        start_time = time.time()
        
        # Check cache if enabled and not forcing refresh
        if self.use_cache and not force_refresh:
            cached_result = self.cache.get(query)
            if cached_result:
                logger.info(f"Retrieved result from cache for query: {query[:50]}...")
                return cached_result
        
        # Determine if this is a multi-query
        if is_multi_query is None and self.auto_detect_multi_query:
            is_multi_query = self._is_multi_query(query)
        
        # If not a multi-query, process normally
        if not is_multi_query:
            return super().process(
                query=query,
                top_k=top_k,
                custom_system_prompt=custom_system_prompt,
                custom_user_prompt=custom_user_prompt,
                callback=callback,
                force_refresh=force_refresh,
                expected_answer=expected_answer
            )
        
        # Extract sub-queries if not provided
        if sub_queries is None:
            sub_queries = self._extract_sub_queries(query)
        
        if not sub_queries:
            logger.warning(f"Failed to extract sub-queries from multi-part query: {query[:50]}...")
            return super().process(
                query=query,
                top_k=top_k,
                custom_system_prompt=custom_system_prompt,
                custom_user_prompt=custom_user_prompt,
                callback=callback,
                force_refresh=force_refresh,
                expected_answer=expected_answer
            )
        
        if self.verbose:
            logger.info(f"Processing multi-part query with {len(sub_queries)} sub-queries")
            for i, sub_query in enumerate(sub_queries):
                logger.info(f"Sub-query {i+1}: {sub_query}")
        
        # Process each sub-query
        sub_results = []
        for i, sub_query in enumerate(sub_queries):
            if self.verbose:
                logger.info(f"Processing sub-query {i+1}/{len(sub_queries)}: {sub_query}")
            
            # Process sub-query
            sub_result = super().process(
                query=sub_query,
                top_k=top_k,
                custom_system_prompt=custom_system_prompt,
                custom_user_prompt=custom_user_prompt,
                callback=callback,
                force_refresh=force_refresh,
                expected_answer=None  # No expected answer for sub-queries
            )
            
            sub_results.append(sub_result)
        
        # Combine results if enabled
        if self.combine_results:
            result = self._combine_sub_results(query, sub_queries, sub_results)
        else:
            # Just return the sub-results without combining
            result = {
                "query": query,
                "multi_query": {
                    "sub_queries": sub_queries,
                    "sub_results": sub_results
                },
                "model": self.model,
                "provider": self.provider
            }
        
        # Add latency
        result["latency"] = time.time() - start_time
        
        # Cache result if caching is enabled
        if self.use_cache:
            self.cache.set(query, result)
        
        return result
