"""
Mock implementation of Tree of Thought for testing.
"""

from functools import lru_cache
from typing import Dict, List, Any, Optional, Callable, Union

from .base import BaseReasoner

class TreeOfThought(BaseReasoner):
    """
    Mock implementation of Tree of Thought for testing.
    """
    
    def __init__(self, use_cache: bool = True, cache_size: int = 100, provider: str = "openrouter",
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        language: str = "en",
        max_branches: int = 3,
        max_depth: int = 2,
        adaptive: bool = True,
        verbose: bool = False
    ):
        """
        Initialize the TreeOfThought reasoner.
        
        Args:
            provider: The provider to use ("openai", "anthropic", "openrouter", etc.)
            model: The model to use (if None, will use provider's default)
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            language: Language for prompts ("en", "vi", etc.)
            max_branches: Maximum branches to explore
            max_depth: Maximum depth of the reasoning tree
            adaptive: Whether to adapt parameters based on query complexity
            verbose: Whether to print verbose output
        """
        super().__init__()
        
        self.provider = provider
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.language = language
        self.max_branches = max_branches
        self.max_depth = max_depth
        self.adaptive = adaptive
        self.verbose = verbose
    
    def reason(
        self,
        query: str,
        context: Optional[str] = None,
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Reason about a query using Tree of Thought.
        
        Args:
            query: The query to reason about
            context: Optional context for the query
            custom_system_prompt: Custom system prompt to use
            custom_user_prompt: Custom user prompt template to use
            callback: Optional callback function for streaming
            **kwargs: Additional arguments to pass to the provider
            
        Returns:
            Dictionary containing the reasoning results
        """
        # Mock implementation for testing
        if callback:
            callback(f"Reasoning about: {query}\n")
            callback("Exploring multiple reasoning paths...\n")
            callback("Selecting the best reasoning path...\n")
        
        # Create a mock response
        if "AI" in query and "Vietnam" in query:
            answer = "The major AI companies in Vietnam include VinAI, FPT AI, and Zalo AI."
            best_thought = "Let me think about AI companies in Vietnam. Based on the documents, I can identify several major players: VinAI, FPT AI, and Zalo AI. VinAI seems to be focused on research in computer vision and NLP."
        elif "công ty AI" in query and "Việt Nam" in query:
            answer = "Các công ty AI lớn ở Việt Nam bao gồm VinAI, FPT AI và Zalo AI."
            best_thought = "Hãy suy nghĩ về các công ty AI ở Việt Nam. Dựa trên tài liệu, tôi có thể xác định một số công ty lớn: VinAI, FPT AI và Zalo AI. VinAI dường như tập trung vào nghiên cứu thị giác máy tính và xử lý ngôn ngữ tự nhiên."
        else:
            answer = f"Answer for: {query}"
            best_thought = f"Thinking about {query}... Based on the context, I can conclude that {answer}"
        
        if callback:
            callback(f"Final answer: {answer}\n")
        
        return {
            "query": query,
            "answer": answer,
            "best_thought": best_thought,
            "explored_paths": 3,
            "model": self.model,
            "provider": self.provider
        }
