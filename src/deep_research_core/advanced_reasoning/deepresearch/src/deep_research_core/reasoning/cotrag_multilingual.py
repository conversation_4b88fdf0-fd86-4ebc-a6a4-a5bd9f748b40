"""
CoTRAG with Multilingual Support.

This module extends the CoTRAG implementation with multilingual support,
including the ability to handle queries in multiple languages and
mixed-language queries.
"""

import re
import logging
import time
import langdetect
from functools import lru_cache
from typing import Dict, Any, List, Optional, Union, Callable, Tuple

from .cot_rag import CoTRAG
from .cotrag_vietnamese import CoTRAGVietnamese
from ..utils.structured_logging import get_logger
from ..utils.performance_metrics import measure_latency
from ..utils.distributed_tracing import trace_function

# Create a logger
logger = get_logger(__name__)

class CoTRAGMultilingual(CoTRAG):
    """
    Extends CoTRAG with multilingual support.
    
    This class adds the ability to handle queries in multiple languages
    and mixed-language queries.
    """
    
    SUPPORTED_LANGUAGES = {
        "en": "English",
        "vi": "Vietnamese",
        "zh": "Chinese",
        "fr": "French",
        "de": "German",
        "es": "Spanish",
        "ja": "Japanese",
        "ko": "Korean",
        "ru": "Russian"
    }
    
    def __init__(self, use_cache: bool = True, cache_size: int = 100, provider: str = "openai",
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        vector_store=None,
        adaptive: bool = True,
        use_cache: bool = True,
        evaluate_results: bool = False,
        use_dynamic_weighting: bool = True,
        min_cot_weight: float = 0.3,
        max_cot_weight: float = 0.8,
        default_cot_weight: float = 0.5,
        weighting_strategy: str = "auto",
        handle_irrelevant_docs: bool = True,
        relevance_threshold: float = 0.3,
        analyze_errors: bool = False,
        verbose: bool = False,
        default_language: str = "en",
        auto_detect_language: bool = True,
        translate_queries: bool = True,
        translate_documents: bool = True,
        translate_responses: bool = True,
        vietnamese_embedding_model: str = "phobert",
        device: Optional[str] = None,
        cache_dir: Optional[str] = None
    ):
        """
        Initialize CoTRAGMultilingual.
        
        Args:
            provider: The provider to use ("openai", "anthropic", etc.)
            model: The model to use (if None, will use provider's default)
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            vector_store: Vector store to use for retrieval
            adaptive: Whether to use adaptive parameter adjustment
            use_cache: Whether to use caching for repeated queries
            evaluate_results: Whether to evaluate reasoning quality
            use_dynamic_weighting: Whether to dynamically adjust weights between CoT and RAG
            min_cot_weight: Minimum weight for CoT (0.0 to 1.0)
            max_cot_weight: Maximum weight for CoT (0.0 to 1.0)
            default_cot_weight: Default weight for CoT (0.0 to 1.0)
            weighting_strategy: Weight adjustment strategy ("auto", "query_type", 
                              "query_complexity", "document_relevance")
            handle_irrelevant_docs: Whether to handle cases when RAG returns irrelevant documents
            relevance_threshold: Threshold for document relevance (0.0 to 1.0)
            analyze_errors: Whether to analyze errors in results
            verbose: Whether to log detailed information
            default_language: Default language to use ("en", "vi", etc.)
            auto_detect_language: Whether to automatically detect query language
            translate_queries: Whether to translate queries to English for retrieval
            translate_documents: Whether to translate retrieved documents to query language
            translate_responses: Whether to translate responses to query language
            vietnamese_embedding_model: Name of the Vietnamese embedding model to use
            device: Device to use for inference ("cpu", "cuda", "mps")
            cache_dir: Directory to cache models
        """
        # Initialize the base CoTRAG class
        super().__init__(
            provider=provider,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            vector_store=vector_store,
            adaptive=adaptive,
            use_cache=use_cache,
            evaluate_results=evaluate_results,
            use_dynamic_weighting=use_dynamic_weighting,
            min_cot_weight=min_cot_weight,
            max_cot_weight=max_cot_weight,
            default_cot_weight=default_cot_weight,
            weighting_strategy=weighting_strategy,
            handle_irrelevant_docs=handle_irrelevant_docs,
            relevance_threshold=relevance_threshold,
            analyze_errors=analyze_errors,
            verbose=verbose
        )
        
        # Store multilingual parameters
        self.default_language = default_language
        self.auto_detect_language = auto_detect_language
        self.translate_queries = translate_queries
        self.translate_documents = translate_documents
        self.translate_responses = translate_responses
        
        # Initialize language-specific implementations
        self.vietnamese_implementation = None
        if "vi" in self.SUPPORTED_LANGUAGES:
            self.vietnamese_implementation = CoTRAGVietnamese(
                provider=provider,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                vector_store=vector_store,
                adaptive=adaptive,
                use_cache=use_cache,
                evaluate_results=evaluate_results,
                use_dynamic_weighting=use_dynamic_weighting,
                min_cot_weight=min_cot_weight,
                max_cot_weight=max_cot_weight,
                default_cot_weight=default_cot_weight,
                weighting_strategy=weighting_strategy,
                handle_irrelevant_docs=handle_irrelevant_docs,
                relevance_threshold=relevance_threshold,
                analyze_errors=analyze_errors,
                verbose=verbose,
                vietnamese_embedding_model=vietnamese_embedding_model,
                device=device,
                cache_dir=cache_dir
            )
        
        # Set up caching
        if use_cache:
            self._generate_cached = lru_cache(maxsize=cache_size)(self._generate)
        else:
            self._generate_cached = self._generate
            
        logger.info(f"Initialized CoTRAGMultilingual with default language: {self.default_language}")
    
    def _detect_language(self, text: str) -> str:
        """
        Detect the language of a text.
        
        Args:
            text: The text to detect language for
            
        Returns:
            Language code ("en", "vi", etc.)
        """
        try:
            # Use langdetect to detect language
            lang = langdetect.detect(text)
            
            # Map to supported languages
            if lang in self.SUPPORTED_LANGUAGES:
                return lang
            else:
                logger.warning(f"Detected unsupported language: {lang}, using default: {self.default_language}")
                return self.default_language
        
        except Exception as e:
            logger.error(f"Error detecting language: {str(e)}")
            return self.default_language
    
    def _detect_mixed_language(self, text: str) -> Dict[str, float]:
        """
        Detect if a text contains multiple languages and their proportions.
        
        Args:
            text: The text to analyze
            
        Returns:
            Dictionary mapping language codes to their proportions
        """
        # Split text into sentences
        sentences = re.split(r'[.!?]', text)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        if not sentences:
            return {self.default_language: 1.0}
        
        # Detect language for each sentence
        lang_counts = {}
        
        for sentence in sentences:
            try:
                lang = langdetect.detect(sentence)
                if lang in self.SUPPORTED_LANGUAGES:
                    lang_counts[lang] = lang_counts.get(lang, 0) + 1
                else:
                    lang_counts[self.default_language] = lang_counts.get(self.default_language, 0) + 1
            except:
                lang_counts[self.default_language] = lang_counts.get(self.default_language, 0) + 1
        
        # Calculate proportions
        total = sum(lang_counts.values())
        lang_proportions = {lang: count / total for lang, count in lang_counts.items()}
        
        return lang_proportions
    
    def _translate_text(self, text: str, source_lang: str, target_lang: str) -> str:
        """
        Translate text from source language to target language.
        
        Args:
            text: The text to translate
            source_lang: Source language code
            target_lang: Target language code
            
        Returns:
            Translated text
        """
        if source_lang == target_lang:
            return text
        
        try:
            # Create a prompt for translation
            system_prompt = f"""You are a professional translator. Translate the following text from {self.SUPPORTED_LANGUAGES.get(source_lang, source_lang)} to {self.SUPPORTED_LANGUAGES.get(target_lang, target_lang)}.
            Provide ONLY the translated text without any explanations, notes, or original text."""
            
            user_prompt = f"Translate this text:\n\n{text}"
            
            # Generate translation
            translation = self.api_provider.generate(
                prompt=user_prompt,
                model=self.model,
                system_prompt=system_prompt,
                temperature=0.3,
                max_tokens=max(1000, len(text) // 2)
            )
            
            return translation.strip()
        
        except Exception as e:
            logger.error(f"Error translating text: {str(e)}")
            return text
    
    def _get_system_prompt_for_language(self, language: str) -> str:
        """
        Get a system prompt optimized for a specific language.
        
        Args:
            language: Language code
            
        Returns:
            System prompt in the specified language
        """
        if language == "vi" and self.vietnamese_implementation:
            return self.vietnamese_implementation.system_prompt
        
        # For other languages, translate the default system prompt
        if language != "en":
            translated_prompt = self._translate_text(self.system_prompt, "en", language)
            return translated_prompt
        
        return self.system_prompt
    
    def _get_user_prompt_template_for_language(self, language: str) -> str:
        """
        Get a user prompt template optimized for a specific language.
        
        Args:
            language: Language code
            
        Returns:
            User prompt template in the specified language
        """
        if language == "vi" and self.vietnamese_implementation:
            return self.vietnamese_implementation.user_prompt_template
        
        # For other languages, translate the default user prompt template
        if language != "en":
            translated_template = self._translate_text(self.user_prompt_template, "en", language)
            return translated_template
        
        return self.user_prompt_template
    
    def _handle_mixed_language_query(self, query: str, lang_proportions: Dict[str, float]) -> Tuple[str, str, str]:
        """
        Handle a mixed-language query.
        
        Args:
            query: The mixed-language query
            lang_proportions: Dictionary mapping language codes to their proportions
            
        Returns:
            Tuple of (primary_language, query_for_retrieval, query_for_reasoning)
        """
        # Determine primary language (highest proportion)
        primary_language = max(lang_proportions.items(), key=lambda x: x[1])[0]
        
        # For retrieval, translate to English if needed
        if self.translate_queries and primary_language != "en":
            query_for_retrieval = self._translate_text(query, primary_language, "en")
        else:
            query_for_retrieval = query
        
        # For reasoning, keep the original query
        query_for_reasoning = query
        
        if self.verbose:
            logger.info(f"Mixed-language query detected: {lang_proportions}")
            logger.info(f"Primary language: {primary_language}")
            logger.info(f"Query for retrieval: {query_for_retrieval}")
        
        return primary_language, query_for_retrieval, query_for_reasoning
    
    @trace_function(name="cotrag_multilingual_process")
    @measure_latency("cotrag_multilingual_process")
    def process(
        self,
        query: str,
        top_k: int = 5,
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        force_refresh: bool = False,
        expected_answer: Optional[str] = None,
        language: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process a multilingual query using CoTRAG.
        
        Args:
            query: The query to process
            top_k: Number of documents to retrieve
            custom_system_prompt: Custom system prompt to use
            custom_user_prompt: Custom user prompt template to use
            callback: Optional callback function for streaming
            force_refresh: Whether to force a refresh (ignore cache)
            expected_answer: Optional expected answer for error analysis
            language: Optional language code to override auto-detection
            
        Returns:
            Dict containing the query, retrieved documents, reasoning, and answer
        """
        start_time = time.time()
        
        # Check cache if enabled and not forcing refresh
        if self.use_cache and not force_refresh:
            cached_result = self.cache.get(query)
            if cached_result:
                logger.info(f"Retrieved result from cache for query: {query[:50]}...")
                return cached_result
        
        # Detect language if not specified
        query_language = language
        if query_language is None:
            if self.auto_detect_language:
                query_language = self._detect_language(query)
            else:
                query_language = self.default_language
        
        # Check for mixed-language query
        lang_proportions = self._detect_mixed_language(query)
        is_mixed_language = len(lang_proportions) > 1 and max(lang_proportions.values()) < 0.8
        
        if is_mixed_language:
            primary_language, query_for_retrieval, query_for_reasoning = self._handle_mixed_language_query(
                query, lang_proportions
            )
        else:
            primary_language = query_language
            
            # Translate query for retrieval if needed
            if self.translate_queries and primary_language != "en":
                query_for_retrieval = self._translate_text(query, primary_language, "en")
            else:
                query_for_retrieval = query
            
            query_for_reasoning = query
        
        # Use language-specific implementation if available
        if primary_language == "vi" and self.vietnamese_implementation:
            result = self.vietnamese_implementation.process(
                query=query_for_reasoning,
                top_k=top_k,
                custom_system_prompt=custom_system_prompt,
                custom_user_prompt=custom_user_prompt,
                callback=callback,
                force_refresh=force_refresh,
                expected_answer=expected_answer
            )
            
            # Add multilingual metadata
            result["multilingual"] = {
                "detected_language": primary_language,
                "is_mixed_language": is_mixed_language,
                "language_proportions": lang_proportions if is_mixed_language else {primary_language: 1.0}
            }
            
            return result
        
        # Retrieve documents using the retrieval query
        documents = self.retrieve(query_for_retrieval, top_k=top_k)
        
        # Translate documents if needed
        if self.translate_documents and primary_language != "en":
            for doc in documents:
                doc["original_content"] = doc["content"]
                doc["content"] = self._translate_text(doc["content"], "en", primary_language)
        
        # Get system prompt for the primary language
        if custom_system_prompt:
            system_prompt = custom_system_prompt
        else:
            system_prompt = self._get_system_prompt_for_language(primary_language)
        
        # Get user prompt template for the primary language
        if custom_user_prompt:
            user_prompt_template = custom_user_prompt
        else:
            user_prompt_template = self._get_user_prompt_template_for_language(primary_language)
        
        # Format context from documents
        context = self._format_context(documents)
        
        # Create user prompt
        user_prompt = user_prompt_template.format(query=query_for_reasoning, context=context)
        
        # Get optimal weights
        weights = self._get_optimal_weights(query_for_reasoning, documents)
        
        # Create weighted prompt if using dynamic weighting
        if self.use_dynamic_weighting:
            user_prompt = self._create_weighted_prompt(query_for_reasoning, context, weights)
        
        # Generate reasoning
        reasoning = self.api_provider.generate(
            prompt=user_prompt,
            model=self.model,
            system_prompt=system_prompt,
            temperature=self.temperature,
            max_tokens=self.max_tokens
        )
        
        # Extract final answer
        final_answer = self._extract_final_answer(reasoning)
        
        # Translate response if needed
        if self.translate_responses and primary_language != self.default_language and self.default_language != "en":
            original_reasoning = reasoning
            original_answer = final_answer
            
            reasoning = self._translate_text(reasoning, primary_language, self.default_language)
            final_answer = self._translate_text(final_answer, primary_language, self.default_language)
            
            # Store original responses
            result_translations = {
                "original_reasoning": original_reasoning,
                "original_answer": original_answer,
                "translated_reasoning": reasoning,
                "translated_answer": final_answer
            }
        else:
            result_translations = None
        
        # Create result
        result = {
            "query": query,
            "documents": documents,
            "reasoning": reasoning,
            "answer": final_answer,
            "weights": weights,
            "model": self.model,
            "provider": self.provider,
            "latency": time.time() - start_time
        }
        
        # Add multilingual metadata
        result["multilingual"] = {
            "detected_language": primary_language,
            "is_mixed_language": is_mixed_language,
            "language_proportions": lang_proportions if is_mixed_language else {primary_language: 1.0},
            "translations": result_translations
        }
        
        # Evaluate results if enabled
        if self.evaluate_results:
            result["evaluation"] = self._evaluate_results(
                query=query,
                documents=documents,
                reasoning=reasoning,
                answer=final_answer,
                expected_answer=expected_answer
            )
        
        # Analyze errors if enabled
        if self.analyze_errors:
            result["error_analysis"] = self._analyze_errors(
                query=query,
                documents=documents,
                reasoning=reasoning,
                answer=final_answer,
                expected_answer=expected_answer
            )
        
        # Cache result if caching is enabled
        if self.use_cache:
            self.cache.set(query, result)
        
        return result
