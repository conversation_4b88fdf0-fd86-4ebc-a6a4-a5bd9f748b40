"""
CoTRAG with Ambiguity Handling.

This module extends the CoTRAG implementation with ambiguity handling,
including the ability to detect and handle ambiguous or unclear queries.
"""

import logging
import time
from functools import lru_cache
from typing import Dict, Any, List, Optional, Union, Callable, Tuple

from .cot_rag import CoTRAG
from ..utils.structured_logging import get_logger
from ..utils.performance_metrics import measure_latency
from ..utils.distributed_tracing import trace_function

# Create a logger
logger = get_logger(__name__)

class CoTRAGAmbiguityHandler(CoTRAG):
    """
    Extends CoTRAG with ambiguity handling.
    
    This class adds the ability to detect and handle ambiguous or unclear
    queries, providing multiple interpretations and clarifying questions.
    """
    
    def __init__(self, use_cache: bool = True, cache_size: int = 100, provider: str = "openai",
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        vector_store=None,
        adaptive: bool = True,
        use_cache: bool = True,
        evaluate_results: bool = False,
        use_dynamic_weighting: bool = True,
        min_cot_weight: float = 0.3,
        max_cot_weight: float = 0.8,
        default_cot_weight: float = 0.5,
        weighting_strategy: str = "auto",
        handle_irrelevant_docs: bool = True,
        relevance_threshold: float = 0.3,
        analyze_errors: bool = False,
        verbose: bool = False,
        detect_ambiguity: bool = True,
        max_interpretations: int = 3,
        ambiguity_threshold: float = 0.7,
        provide_clarifying_questions: bool = True
    ):
        """
        Initialize CoTRAGAmbiguityHandler.
        
        Args:
            provider: The provider to use ("openai", "anthropic", etc.)
            model: The model to use (if None, will use provider's default)
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            vector_store: Vector store to use for retrieval
            adaptive: Whether to use adaptive parameter adjustment
            use_cache: Whether to use caching for repeated queries
            evaluate_results: Whether to evaluate reasoning quality
            use_dynamic_weighting: Whether to dynamically adjust weights between CoT and RAG
            min_cot_weight: Minimum weight for CoT (0.0 to 1.0)
            max_cot_weight: Maximum weight for CoT (0.0 to 1.0)
            default_cot_weight: Default weight for CoT (0.0 to 1.0)
            weighting_strategy: Weight adjustment strategy ("auto", "query_type", 
                              "query_complexity", "document_relevance")
            handle_irrelevant_docs: Whether to handle cases when RAG returns irrelevant documents
            relevance_threshold: Threshold for document relevance (0.0 to 1.0)
            analyze_errors: Whether to analyze errors in results
            verbose: Whether to log detailed information
            detect_ambiguity: Whether to detect ambiguous queries
            max_interpretations: Maximum number of interpretations for ambiguous queries
            ambiguity_threshold: Threshold for ambiguity detection (0.0 to 1.0)
            provide_clarifying_questions: Whether to provide clarifying questions
        """
        # Initialize the base CoTRAG class
        super().__init__(
            provider=provider,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            vector_store=vector_store,
            adaptive=adaptive,
            use_cache=use_cache,
            evaluate_results=evaluate_results,
            use_dynamic_weighting=use_dynamic_weighting,
            min_cot_weight=min_cot_weight,
            max_cot_weight=max_cot_weight,
            default_cot_weight=default_cot_weight,
            weighting_strategy=weighting_strategy,
            handle_irrelevant_docs=handle_irrelevant_docs,
            relevance_threshold=relevance_threshold,
            analyze_errors=analyze_errors,
            verbose=verbose
        )
        
        # Store ambiguity handling parameters
        self.detect_ambiguity = detect_ambiguity
        self.max_interpretations = max_interpretations
        self.ambiguity_threshold = ambiguity_threshold
        self.provide_clarifying_questions = provide_clarifying_questions
        
        # Set up caching
        if use_cache:
            self._generate_cached = lru_cache(maxsize=cache_size)(self._generate)
        else:
            self._generate_cached = self._generate
            
        logger.info(f"Initialized CoTRAGAmbiguityHandler with detect_ambiguity={detect_ambiguity}")
    
    def _detect_query_ambiguity(self, query: str) -> Dict[str, Any]:
        """
        Detect if a query is ambiguous or unclear.
        
        Args:
            query: The query to analyze
            
        Returns:
            Dictionary with ambiguity analysis
        """
        try:
            # Create a prompt for ambiguity detection
            system_prompt = """You are an expert at analyzing queries for ambiguity and clarity.
            Analyze the given query and determine if it is ambiguous, vague, or unclear.
            Consider the following aspects:
            1. Multiple possible interpretations
            2. Missing context or information
            3. Vague or imprecise terms
            4. Unclear references or pronouns
            5. Overly broad or general questions
            
            Provide a structured analysis with:
            - An ambiguity score from 0.0 (completely clear) to 1.0 (highly ambiguous)
            - Whether the query is ambiguous (true/false)
            - The specific type of ambiguity (lexical, syntactic, semantic, etc.)
            - A brief explanation of why the query is ambiguous
            
            Format your response as a JSON object with these fields."""
            
            user_prompt = f"Analyze this query for ambiguity: {query}"
            
            # Generate ambiguity analysis
            response = self.api_provider.generate(
                prompt=user_prompt,
                model=self.model,
                system_prompt=system_prompt,
                temperature=0.3,
                max_tokens=500
            )
            
            # Extract JSON from response
            import json
            import re
            
            # Try to extract JSON from the response
            json_match = re.search(r'(\{.*\})', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
                analysis = json.loads(json_str)
            else:
                # Fallback: create a simple analysis
                analysis = {
                    "ambiguity_score": 0.5,
                    "is_ambiguous": False,
                    "ambiguity_type": "unknown",
                    "explanation": "Could not parse ambiguity analysis"
                }
            
            # Ensure required fields are present
            if "ambiguity_score" not in analysis:
                analysis["ambiguity_score"] = 0.5
            
            if "is_ambiguous" not in analysis:
                analysis["is_ambiguous"] = analysis["ambiguity_score"] >= self.ambiguity_threshold
            
            return analysis
        
        except Exception as e:
            logger.error(f"Error detecting query ambiguity: {str(e)}")
            
            # Return a default analysis
            return {
                "ambiguity_score": 0.0,
                "is_ambiguous": False,
                "ambiguity_type": "unknown",
                "explanation": f"Error during ambiguity detection: {str(e)}"
            }
    
    def _generate_interpretations(self, query: str, ambiguity_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate multiple interpretations for an ambiguous query.
        
        Args:
            query: The ambiguous query
            ambiguity_analysis: Ambiguity analysis from _detect_query_ambiguity
            
        Returns:
            List of interpretations
        """
        try:
            # Create a prompt for generating interpretations
            system_prompt = f"""You are an expert at clarifying ambiguous queries.
            The following query has been identified as ambiguous:
            "{query}"
            
            Ambiguity type: {ambiguity_analysis.get('ambiguity_type', 'unknown')}
            Explanation: {ambiguity_analysis.get('explanation', 'No explanation provided')}
            
            Generate {self.max_interpretations} different possible interpretations of this query.
            For each interpretation:
            1. Provide a clear, specific interpretation of what the query might mean
            2. Explain why this is a valid interpretation
            3. Provide a reformulated version of the query that removes the ambiguity
            
            Format your response as a JSON array of objects, each with fields:
            - "interpretation": The specific interpretation
            - "explanation": Why this is a valid interpretation
            - "reformulated_query": The clear, unambiguous version of the query"""
            
            user_prompt = f"Generate interpretations for this ambiguous query: {query}"
            
            # Generate interpretations
            response = self.api_provider.generate(
                prompt=user_prompt,
                model=self.model,
                system_prompt=system_prompt,
                temperature=0.7,  # Higher temperature for diverse interpretations
                max_tokens=1000
            )
            
            # Extract JSON from response
            import json
            import re
            
            # Try to extract JSON from the response
            json_match = re.search(r'(\[.*\])', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
                interpretations = json.loads(json_str)
            else:
                # Fallback: create a simple interpretation
                interpretations = [{
                    "interpretation": "Default interpretation",
                    "explanation": "Could not parse multiple interpretations",
                    "reformulated_query": query
                }]
            
            # Ensure we have at least one interpretation
            if not interpretations:
                interpretations = [{
                    "interpretation": "Default interpretation",
                    "explanation": "No interpretations generated",
                    "reformulated_query": query
                }]
            
            # Limit to max_interpretations
            return interpretations[:self.max_interpretations]
        
        except Exception as e:
            logger.error(f"Error generating interpretations: {str(e)}")
            
            # Return a default interpretation
            return [{
                "interpretation": "Default interpretation",
                "explanation": f"Error during interpretation generation: {str(e)}",
                "reformulated_query": query
            }]
    
    def _generate_clarifying_questions(self, query: str, ambiguity_analysis: Dict[str, Any]) -> List[str]:
        """
        Generate clarifying questions for an ambiguous query.
        
        Args:
            query: The ambiguous query
            ambiguity_analysis: Ambiguity analysis from _detect_query_ambiguity
            
        Returns:
            List of clarifying questions
        """
        try:
            # Create a prompt for generating clarifying questions
            system_prompt = f"""You are an expert at clarifying ambiguous queries.
            The following query has been identified as ambiguous:
            "{query}"
            
            Ambiguity type: {ambiguity_analysis.get('ambiguity_type', 'unknown')}
            Explanation: {ambiguity_analysis.get('explanation', 'No explanation provided')}
            
            Generate 3-5 specific clarifying questions that would help resolve the ambiguity.
            These questions should:
            1. Be focused on the specific points of ambiguity
            2. Be answerable with specific information
            3. Help narrow down the possible interpretations
            4. Be phrased in a clear, concise way
            
            Format your response as a JSON array of strings, with each string being a clarifying question."""
            
            user_prompt = f"Generate clarifying questions for this ambiguous query: {query}"
            
            # Generate clarifying questions
            response = self.api_provider.generate(
                prompt=user_prompt,
                model=self.model,
                system_prompt=system_prompt,
                temperature=0.7,  # Higher temperature for diverse questions
                max_tokens=500
            )
            
            # Extract JSON from response
            import json
            import re
            
            # Try to extract JSON from the response
            json_match = re.search(r'(\[.*\])', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
                questions = json.loads(json_str)
            else:
                # Try to extract questions line by line
                lines = response.strip().split("\n")
                questions = []
                for line in lines:
                    # Look for numbered questions or questions with question marks
                    if ("?" in line) and (re.match(r'^\d+[\.\)]', line) or line.strip().endswith("?")):
                        # Clean up the question
                        question = re.sub(r'^\d+[\.\)]\s*', '', line).strip()
                        questions.append(question)
            
            # Ensure we have at least one question
            if not questions:
                questions = ["Could you please clarify what you mean by your query?"]
            
            return questions
        
        except Exception as e:
            logger.error(f"Error generating clarifying questions: {str(e)}")
            
            # Return default questions
            return [
                "Could you please clarify what you mean by your query?",
                "Can you provide more context or specific details about what you're asking?"
            ]
    
    def _process_with_multiple_interpretations(
        self,
        query: str,
        interpretations: List[Dict[str, Any]],
        top_k: int = 5,
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        force_refresh: bool = False
    ) -> Dict[str, Any]:
        """
        Process an ambiguous query with multiple interpretations.
        
        Args:
            query: The original ambiguous query
            interpretations: List of interpretations from _generate_interpretations
            top_k: Number of documents to retrieve
            custom_system_prompt: Custom system prompt to use
            custom_user_prompt: Custom user prompt template to use
            callback: Optional callback function for streaming
            force_refresh: Whether to force a refresh (ignore cache)
            
        Returns:
            Dict containing the query, interpretations, and results for each interpretation
        """
        # Process each interpretation
        interpretation_results = []
        
        for i, interp in enumerate(interpretations):
            if self.verbose:
                logger.info(f"Processing interpretation {i+1}/{len(interpretations)}: {interp['reformulated_query']}")
            
            # Process the reformulated query
            result = super().process(
                query=interp["reformulated_query"],
                top_k=top_k,
                custom_system_prompt=custom_system_prompt,
                custom_user_prompt=custom_user_prompt,
                callback=callback,
                force_refresh=force_refresh
            )
            
            # Add interpretation information
            result["interpretation"] = interp["interpretation"]
            result["explanation"] = interp["explanation"]
            
            interpretation_results.append(result)
        
        # Create a combined system prompt for synthesizing results
        system_prompt = """You are a helpful AI assistant that can handle ambiguous queries.
        The user has asked an ambiguous question that has multiple possible interpretations.
        You have processed each interpretation separately and now need to synthesize the results.
        
        Present a comprehensive response that:
        1. Acknowledges the ambiguity in the original query
        2. Briefly explains each possible interpretation
        3. Provides the answer for each interpretation
        4. If appropriate, suggests which interpretation might be most likely
        
        Be clear, concise, and helpful in your response."""
        
        # Create a user prompt with the original query and all interpretations
        user_prompt = f"""Original ambiguous query: {query}

Multiple interpretations were identified:
"""
        
        for i, (interp, result) in enumerate(zip(interpretations, interpretation_results)):
            user_prompt += f"\nInterpretation {i+1}: {interp['interpretation']}\n"
            user_prompt += f"Answer for interpretation {i+1}: {result['answer']}\n"
        
        user_prompt += "\nPlease synthesize these interpretations and answers into a comprehensive response."
        
        # Generate synthesized reasoning
        synthesized_reasoning = self.api_provider.generate(
            prompt=user_prompt,
            model=self.model,
            system_prompt=system_prompt,
            temperature=self.temperature,
            max_tokens=self.max_tokens
        )
        
        # Extract final answer
        synthesized_answer = self._extract_final_answer(synthesized_reasoning)
        
        # Collect all unique documents
        all_documents = []
        for result in interpretation_results:
            for doc in result.get("documents", []):
                if doc not in all_documents:
                    all_documents.append(doc)
        
        # Create combined result
        combined_result = {
            "query": query,
            "documents": all_documents,
            "reasoning": synthesized_reasoning,
            "answer": synthesized_answer,
            "model": self.model,
            "provider": self.provider,
            "ambiguity": {
                "is_ambiguous": True,
                "interpretations": interpretations,
                "interpretation_results": interpretation_results
            }
        }
        
        return combined_result
    
    @trace_function(name="cotrag_ambiguity_handler_process")
    @measure_latency("cotrag_ambiguity_handler_process")
    def process(
        self,
        query: str,
        top_k: int = 5,
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        force_refresh: bool = False,
        expected_answer: Optional[str] = None,
        check_ambiguity: Optional[bool] = None,
        handle_ambiguity: bool = True
    ) -> Dict[str, Any]:
        """
        Process a query that may be ambiguous or unclear.
        
        Args:
            query: The query to process
            top_k: Number of documents to retrieve
            custom_system_prompt: Custom system prompt to use
            custom_user_prompt: Custom user prompt template to use
            callback: Optional callback function for streaming
            force_refresh: Whether to force a refresh (ignore cache)
            expected_answer: Optional expected answer for error analysis
            check_ambiguity: Whether to check for ambiguity (overrides detect_ambiguity)
            handle_ambiguity: Whether to handle ambiguity if detected
            
        Returns:
            Dict containing the query, retrieved documents, reasoning, and answer
        """
        start_time = time.time()
        
        # Check cache if enabled and not forcing refresh
        if self.use_cache and not force_refresh:
            cached_result = self.cache.get(query)
            if cached_result:
                logger.info(f"Retrieved result from cache for query: {query[:50]}...")
                return cached_result
        
        # Determine whether to check for ambiguity
        should_check_ambiguity = check_ambiguity if check_ambiguity is not None else self.detect_ambiguity
        
        # Check for ambiguity if enabled
        if should_check_ambiguity:
            ambiguity_analysis = self._detect_query_ambiguity(query)
            is_ambiguous = ambiguity_analysis.get("is_ambiguous", False)
            
            if self.verbose:
                logger.info(f"Ambiguity analysis: {ambiguity_analysis}")
            
            # Handle ambiguity if detected and handling is enabled
            if is_ambiguous and handle_ambiguity:
                # Generate multiple interpretations
                interpretations = self._generate_interpretations(query, ambiguity_analysis)
                
                # Generate clarifying questions if enabled
                clarifying_questions = None
                if self.provide_clarifying_questions:
                    clarifying_questions = self._generate_clarifying_questions(query, ambiguity_analysis)
                
                # Process with multiple interpretations
                result = self._process_with_multiple_interpretations(
                    query=query,
                    interpretations=interpretations,
                    top_k=top_k,
                    custom_system_prompt=custom_system_prompt,
                    custom_user_prompt=custom_user_prompt,
                    callback=callback,
                    force_refresh=force_refresh
                )
                
                # Add ambiguity analysis and clarifying questions
                result["ambiguity"]["analysis"] = ambiguity_analysis
                
                if clarifying_questions:
                    result["ambiguity"]["clarifying_questions"] = clarifying_questions
                
                # Add latency
                result["latency"] = time.time() - start_time
                
                # Cache result if caching is enabled
                if self.use_cache:
                    self.cache.set(query, result)
                
                return result
        
        # If not ambiguous or ambiguity handling is disabled, process normally
        result = super().process(
            query=query,
            top_k=top_k,
            custom_system_prompt=custom_system_prompt,
            custom_user_prompt=custom_user_prompt,
            callback=callback,
            force_refresh=force_refresh,
            expected_answer=expected_answer
        )
        
        # Add ambiguity information if checked
        if should_check_ambiguity:
            result["ambiguity"] = {
                "is_ambiguous": False,
                "analysis": ambiguity_analysis
            }
        
        return result
