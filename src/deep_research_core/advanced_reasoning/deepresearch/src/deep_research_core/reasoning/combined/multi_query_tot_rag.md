# Multi-Query Tree of Thought RAG (MultiQueryToTRAG)

## Overview

MultiQueryToTRAG is a powerful reasoning module that combines three advanced AI techniques:

1. **Multi-Query Decomposition (MQD)**: Breaks down complex queries into simpler sub-queries
2. **Tree of Thought (ToT)**: Explores multiple reasoning paths for each sub-query
3. **Retrieval-Augmented Generation (RAG)**: Provides factual grounding through document retrieval

This combined approach leverages the strengths of each technique to handle complex reasoning tasks with improved accuracy and explainability.

## How It Works

The MultiQueryToTRAG process follows these steps:

1. **Query Decomposition**: A complex query is broken down into simpler sub-queries using the MultiQueryDecomposition module. Dependencies between sub-queries are tracked to ensure proper processing order.

2. **Document Retrieval**: For each sub-query, relevant documents are retrieved from a knowledge base using the provided RAG instance.

3. **Tree of Thought Reasoning**: Each sub-query is processed using Tree of Thought reasoning, which explores multiple reasoning paths using the retrieved documents as context.

4. **Result Synthesis**: The answers from all sub-queries are synthesized into a coherent response that addresses the original query.

## Key Features

- **Hierarchical Decomposition**: Handles complex queries by breaking them down into manageable parts
- **Dependency Tracking**: Processes sub-queries in the correct order based on their dependencies
- **Multiple Reasoning Paths**: Explores different approaches to solving each sub-query
- **Document-Based Verification**: Grounds reasoning in factual information from retrieved documents
- **Fallback Mechanisms**: Includes robust error handling with fallback to simpler processing when needed

## Usage Example

```python
from deep_research_core.reasoning.combined.multi_query_tot_rag import MultiQueryToTRAG
from deep_research_core.rag.sqlite_vector_rag import SQLiteVectorRAG

# Initialize a RAG instance
rag_instance = SQLiteVectorRAG(
    db_path="path/to/vector_db.sqlite",
    embedding_model="multilingual-e5-large"
)

# Initialize MultiQueryToTRAG
reasoner = MultiQueryToTRAG(
    rag_instance=rag_instance,
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free",
    language="vi",  # Supports both English and Vietnamese
    mq_max_depth=2,  # Maximum depth for hierarchical decomposition
    tot_max_branches=3,  # Maximum branches to explore in ToT
    tot_max_depth=2,  # Maximum depth of the reasoning tree in ToT
    verbose=True
)

# Process a complex query
result = reasoner.process(
    query="So sánh các công ty AI ở Việt Nam, phân tích ưu điểm và nhược điểm của mỗi công ty, và dự đoán xu hướng phát triển AI ở Việt Nam trong 5 năm tới.",
    top_k=5  # Number of documents to retrieve for each sub-query
)

# Access the results
print(f"Original query: {result['query']}")
print(f"Is decomposed: {result['is_decomposed']}")
print(f"Number of sub-queries: {len(result.get('sub_queries', []))}")
print(f"Final answer: {result['answer']}")
```

## Configuration Options

The MultiQueryToTRAG module offers extensive configuration options:

### Basic Parameters
- `rag_instance`: Instance of a RAG system
- `provider`: The provider to use ("openai", "anthropic", "openrouter", etc.)
- `model`: The model to use (if None, will use provider's default)
- `temperature`: Sampling temperature
- `max_tokens`: Maximum number of tokens to generate
- `language`: Language for prompts ("en", "vi", etc.)

### MultiQueryDecomposition Parameters
- `mq_max_depth`: Maximum depth for hierarchical decomposition
- `track_dependencies`: Whether to track dependencies between sub-queries
- `enable_synthesis`: Whether to enable result synthesis

### Tree of Thought Parameters
- `tot_max_branches`: Maximum branches to explore in ToT
- `tot_max_depth`: Maximum depth of the reasoning tree in ToT
- `tot_adaptive`: Whether to adapt ToT parameters based on query complexity

### General Parameters
- `use_cache`: Whether to use caching
- `cache_size`: Size of the cache
- `verbose`: Whether to print verbose output

## Integration with Other Modules

MultiQueryToTRAG can be integrated with other reasoning modules in the deep_research_core library:

- **Chain of Thought (CoT)**: For step-by-step reasoning
- **ReAct**: For reasoning with tool use
- **Self-Reflection**: For improved accuracy through self-correction
- **Source Attribution**: For citing sources in the generated response

## Performance Considerations

- Processing complex queries with MultiQueryToTRAG can be computationally intensive due to the multiple reasoning paths explored.
- Consider using a smaller value for `tot_max_branches` and `tot_max_depth` for faster processing.
- The `use_cache` parameter can significantly improve performance for repeated queries.

## Limitations

- Requires a high-quality RAG implementation for optimal performance
- May struggle with extremely complex queries that cannot be easily decomposed
- Performance depends on the quality of the underlying language model

## Future Improvements

- Parallel processing of independent sub-queries
- Improved dependency detection between sub-queries
- Integration with more specialized reasoning modules
- Enhanced result synthesis with better handling of contradictions
