"""
Multi-Query Tree of Thought RAG Reasoner implementation.

This module provides a combined reasoning approach that integrates:
1. Multi-Query Decomposition (MQD)
2. Tree of Thought (ToT) reasoning
3. Retrieval-Augmented Generation (RAG)

The approach works by:
1. Decomposing a complex query into simpler sub-queries
2. For each sub-query:
   a. Retrieving relevant documents
   b. Exploring multiple reasoning paths using Tree of Thought
   c. Selecting the best reasoning path
3. Synthesizing the answers into a coherent response

This combined approach leverages the strengths of each technique:
- MQD breaks down complex queries into manageable parts
- ToT explores multiple reasoning paths for each sub-query
- RAG provides factual grounding through document retrieval
"""

import json
import time
import traceback
from typing import Dict, List, Any, Optional, Callable, Union, Tuple

from ...utils.structured_logging import get_logger
from ...utils.performance_metrics import measure_latency, measure_block_latency
from ...utils.distributed_tracing import trace_function, span
from ..base import BaseReasoner
from ..multi_query_decomposer import MultiQueryDecomposition
try:
    from ..tot import TreeOfThought
except ImportError:
    from ..tot_mock import TreeOfThought
from ..base_rag import BaseRAG

# Create a logger
logger = get_logger(__name__)

class MultiQueryToTRAG(BaseReasoner):
    """
    Implements a combination of Multi-Query Decomposition (MQD),
    Tree of Thought (ToT) reasoning, and Retrieval-Augmented Generation (RAG).

    This approach works by:
    1. Decomposing a complex query into simpler sub-queries
    2. For each sub-query:
       a. Retrieving relevant documents
       b. Exploring multiple reasoning paths using Tree of Thought
       c. Selecting the best reasoning path
    3. Synthesizing the answers into a coherent response
    """

    def __init__(
        self,
        rag_instance: BaseRAG,
        provider: str = "openrouter",
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        language: str = "en",
        # MultiQueryDecomposition parameters
        mq_max_depth: int = 2,
        track_dependencies: bool = True,
        enable_synthesis: bool = True,
        # Tree of Thought parameters
        tot_max_branches: int = 3,
        tot_max_depth: int = 2,
        tot_adaptive: bool = True,
        # General parameters
        use_cache: bool = True,
        cache_size: int = 100,
        verbose: bool = False,
        use_mock_for_testing: bool = False
    ):
        """
        Initialize the MultiQueryToTRAG reasoner.

        Args:
            rag_instance: Instance of a RAG system
            provider: The provider to use ("openai", "anthropic", "openrouter", etc.)
            model: The model to use (if None, will use provider's default)
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            language: Language for prompts ("en", "vi", etc.)
            mq_max_depth: Maximum depth for hierarchical decomposition in MQD
            track_dependencies: Whether to track dependencies between sub-queries
            enable_synthesis: Whether to enable result synthesis
            tot_max_branches: Maximum branches to explore in ToT
            tot_max_depth: Maximum depth of the reasoning tree in ToT
            tot_adaptive: Whether to adapt ToT parameters based on query complexity
            use_cache: Whether to use caching
            cache_size: Size of the cache
            verbose: Whether to print verbose output
            use_mock_for_testing: Whether to use mock providers for testing
        """
        super().__init__()

        self.rag_instance = rag_instance
        self.provider = provider
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.language = language
        self.verbose = verbose
        self.use_mock_for_testing = use_mock_for_testing

        # Initialize MultiQueryDecomposition
        self.decomposer = MultiQueryDecomposition(
            provider=provider,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            language=language,
            max_depth=mq_max_depth,
            track_dependencies=track_dependencies,
            enable_synthesis=enable_synthesis,
            use_cache=use_cache,
            cache_size=cache_size,
            use_mock_for_testing=use_mock_for_testing
        )

        # Initialize Tree of Thought
        self.tot = TreeOfThought(
            provider=provider,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            language=language,
            max_branches=tot_max_branches,
            max_depth=tot_max_depth,
            adaptive=tot_adaptive,
            verbose=verbose
        )

        # Initialize document cache
        self.document_cache = {}

        logger.info(f"Initialized MultiQueryToTRAG with provider={provider}, model={model or 'default'}, "
                   f"tot_max_branches={tot_max_branches}, tot_max_depth={tot_max_depth}")

    @trace_function(name="multi_query_tot_rag_process")
    @measure_latency("multi_query_tot_rag_process")
    def process(
        self,
        query: str,
        top_k: int = 5,
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        max_sub_queries: int = 5,
        min_sub_queries: int = 2,
        context: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process a query using MultiQueryToTRAG.

        Args:
            query: The query to process
            top_k: Number of documents to retrieve for each sub-query
            custom_system_prompt: Custom system prompt to use
            custom_user_prompt: Custom user prompt template to use
            callback: Optional callback function for streaming
            max_sub_queries: Maximum number of sub-queries to generate
            min_sub_queries: Minimum number of sub-queries to generate
            context: Optional context for the query
            **kwargs: Additional arguments to pass to the RAG process method

        Returns:
            Dictionary containing the query, sub-queries, answers, and synthesized response
        """
        start_time = time.time()

        if self.verbose:
            logger.info(f"Processing query: {query}")
            if callback:
                callback(f"Processing query: {query}\n\n")

        try:
            # Step 1: Decompose the query into sub-queries
            decomposition_result = self.decomposer.decompose(
                query=query,
                context=context,
                max_sub_queries=max_sub_queries,
                min_sub_queries=min_sub_queries
            )

            is_decomposed = decomposition_result["is_decomposed"]
            sub_queries = decomposition_result["sub_queries"]
            processing_order = decomposition_result["processing_order"]

            if self.verbose:
                logger.info(f"Query decomposed into {len(sub_queries)} sub-queries")
                if callback:
                    callback(f"Query decomposed into {len(sub_queries)} sub-queries:\n")
                    for i, sub_query in enumerate(sub_queries):
                        callback(f"{i+1}. {sub_query['query']}\n")
                    callback("\n")

            # If the query wasn't decomposed, just process it directly
            if not is_decomposed:
                if self.verbose:
                    logger.info("Query is not complex, processing directly")
                    if callback:
                        callback("Query is not complex, processing directly\n\n")

                return self._process_single_query(
                    query=query,
                    top_k=top_k,
                    custom_system_prompt=custom_system_prompt,
                    custom_user_prompt=custom_user_prompt,
                    callback=callback,
                    **kwargs
                )

            # Step 2: Process each sub-query using RAG + ToT
            sub_query_results = {}
            all_documents = []
            tot_reasoning_paths = {}

            # Process sub-queries sequentially (following dependencies)
            for query_id in processing_order:
                # Skip the original query
                if query_id == self._generate_query_id(query):
                    continue

                sub_query_info = decomposition_result["dependency_graph"][query_id]
                sub_query = sub_query_info["query"]

                if self.verbose:
                    logger.info(f"Processing sub-query: {sub_query}")
                    if callback:
                        callback(f"Processing sub-query: {sub_query}\n")

                try:
                    # Step 2a: Retrieve documents for the sub-query
                    documents = self._retrieve_documents(
                        query=sub_query,
                        top_k=top_k,
                        **kwargs
                    )

                    # Step 2b: Process the sub-query using ToT with document context
                    tot_result = self._process_with_tot(
                        query=sub_query,
                        documents=documents,
                        custom_system_prompt=custom_system_prompt,
                        custom_user_prompt=custom_user_prompt,
                        callback=callback,
                        **kwargs
                    )

                    # Store the result
                    sub_query_results[query_id] = tot_result["answer"]
                    tot_reasoning_paths[query_id] = tot_result.get("best_thought", "")

                    # Collect documents for later use
                    all_documents.extend(documents)

                    # Cache documents for this query
                    self.document_cache[query_id] = documents

                    if self.verbose and callback:
                        callback(f"Answer: {tot_result['answer']}\n\n")

                except Exception as e:
                    error_msg = f"Error processing sub-query '{sub_query}': {str(e)}"
                    logger.error(error_msg)

                    # Store error as result
                    sub_query_results[query_id] = f"Error: {str(e)}"

                    if self.verbose and callback:
                        callback(f"Error: {str(e)}\n\n")

            # Step 3: Synthesize the results
            if self.verbose:
                logger.info("Synthesizing results")
                if callback:
                    callback("Synthesizing results...\n\n")

            synthesis = self.decomposer.synthesize_results(
                query_results=sub_query_results,
                original_query=query
            )

            # Calculate latency
            latency = time.time() - start_time

            # Prepare the result
            result = {
                "query": query,
                "is_decomposed": is_decomposed,
                "sub_queries": sub_queries,
                "sub_query_results": sub_query_results,
                "tot_reasoning_paths": tot_reasoning_paths,
                "documents": all_documents,
                "answer": synthesis,
                "processing_order": processing_order,
                "model": self.model,
                "provider": self.provider,
                "latency": latency
            }

            if self.verbose:
                logger.info(f"MultiQueryToTRAG completed in {latency:.2f} seconds")
                if callback:
                    callback(f"\nFinal answer: {synthesis}\n")

            return result

        except Exception as e:
            error_msg = f"Error in MultiQueryToTRAG process: {str(e)}"
            logger.error(error_msg)
            logger.error(f"Traceback: {traceback.format_exc()}")

            # Fallback to direct processing
            if self.verbose:
                logger.info("Falling back to direct processing")
                if callback:
                    callback(f"Error in decomposition: {str(e)}\nFalling back to direct processing\n\n")

            try:
                return self._process_single_query(
                    query=query,
                    top_k=top_k,
                    custom_system_prompt=custom_system_prompt,
                    custom_user_prompt=custom_user_prompt,
                    callback=callback,
                    **kwargs
                )
            except Exception as fallback_error:
                error_msg = f"Error in fallback processing: {str(fallback_error)}"
                logger.error(error_msg)

                # Return error result
                return {
                    "query": query,
                    "is_decomposed": False,
                    "error": error_msg,
                    "answer": f"Error processing query: {str(e)}. Fallback also failed: {str(fallback_error)}",
                    "model": self.model,
                    "provider": self.provider,
                    "latency": time.time() - start_time
                }

    def _process_single_query(
        self,
        query: str,
        top_k: int = 5,
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process a single query using RAG + ToT.

        Args:
            query: The query to process
            top_k: Number of documents to retrieve
            custom_system_prompt: Custom system prompt to use
            custom_user_prompt: Custom user prompt template to use
            callback: Optional callback function for streaming
            **kwargs: Additional arguments to pass to the RAG process method

        Returns:
            Dictionary containing the query, documents, and answer
        """
        try:
            # Step 1: Retrieve documents
            documents = self._retrieve_documents(
                query=query,
                top_k=top_k,
                **kwargs
            )

            # Step 2: Process with ToT
            tot_result = self._process_with_tot(
                query=query,
                documents=documents,
                custom_system_prompt=custom_system_prompt,
                custom_user_prompt=custom_user_prompt,
                callback=callback,
                **kwargs
            )

            # Add documents to the result
            tot_result["documents"] = documents

            return tot_result

        except Exception as e:
            error_msg = f"Error in single query processing: {str(e)}"
            logger.error(error_msg)

            # Return error result
            return {
                "query": query,
                "error": error_msg,
                "answer": f"Error processing query: {str(e)}",
                "model": self.model,
                "provider": self.provider
            }

    def _retrieve_documents(
        self,
        query: str,
        top_k: int = 5,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Retrieve documents for a query.

        Args:
            query: The query to retrieve documents for
            top_k: Number of documents to retrieve
            **kwargs: Additional arguments to pass to the RAG search method

        Returns:
            List of retrieved documents
        """
        try:
            # Use the RAG instance to search for documents
            documents = self.rag_instance.search(
                query=query,
                top_k=top_k,
                **kwargs
            )

            if self.verbose:
                logger.info(f"Retrieved {len(documents)} documents for query: {query}")

            return documents

        except Exception as e:
            logger.error(f"Error retrieving documents: {str(e)}")
            return []

    def _process_with_tot(
        self,
        query: str,
        documents: List[Dict[str, Any]],
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process a query using Tree of Thought with document context.

        Args:
            query: The query to process
            documents: Retrieved documents to use as context
            custom_system_prompt: Custom system prompt to use
            custom_user_prompt: Custom user prompt template to use
            callback: Optional callback function for streaming
            **kwargs: Additional arguments to pass to the ToT reason method

        Returns:
            Dictionary containing the reasoning results
        """
        try:
            # Create document context
            doc_context = self._format_documents(documents)

            # Process with ToT
            tot_result = self.tot.reason(
                query=query,
                context=doc_context,
                custom_system_prompt=custom_system_prompt,
                custom_user_prompt=custom_user_prompt,
                callback=callback,
                **kwargs
            )

            return tot_result

        except Exception as e:
            logger.error(f"Error in ToT processing: {str(e)}")

            # Fallback to direct RAG
            try:
                logger.info("Falling back to direct RAG processing")

                rag_result = self.rag_instance.process(
                    query=query,
                    top_k=len(documents),
                    custom_system_prompt=custom_system_prompt,
                    custom_user_prompt=custom_user_prompt,
                    **kwargs
                )

                return {
                    "query": query,
                    "answer": rag_result["answer"],
                    "model": self.model,
                    "provider": self.provider,
                    "fallback": "direct_rag"
                }

            except Exception as fallback_error:
                logger.error(f"Error in fallback RAG processing: {str(fallback_error)}")

                # Return error result
                return {
                    "query": query,
                    "answer": f"Error processing query: {str(e)}. Fallback also failed: {str(fallback_error)}",
                    "model": self.model,
                    "provider": self.provider,
                    "error": str(e)
                }

    def _format_documents(self, documents: List[Dict[str, Any]]) -> str:
        """
        Format retrieved documents for inclusion in the prompt.

        Args:
            documents: List of retrieved documents

        Returns:
            Formatted string of documents
        """
        if not documents:
            return ""

        formatted_docs = []

        for i, doc in enumerate(documents):
            formatted_doc = f"Document {i+1}:\n"

            # Add title if available
            if "title" in doc:
                formatted_doc += f"Title: {doc['title']}\n"

            # Add content
            if "content" in doc:
                formatted_doc += f"Content: {doc['content']}\n"
            elif "text" in doc:
                formatted_doc += f"Content: {doc['text']}\n"

            # Add source if available
            if "source" in doc:
                formatted_doc += f"Source: {doc['source']}\n"

            # Add score if available
            if "score" in doc:
                formatted_doc += f"Relevance: {doc['score']:.4f}\n"

            formatted_docs.append(formatted_doc)

        return "\n\n".join(formatted_docs)

    def _generate_query_id(self, query: str) -> str:
        """
        Generate a unique ID for a query.

        Args:
            query: The query to generate an ID for

        Returns:
            A unique ID for the query
        """
        import hashlib
        return hashlib.md5(query.encode()).hexdigest()

    @trace_function(name="multi_query_tot_rag_reason")
    def reason(
        self,
        query: str,
        context: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Reason about a query using MultiQueryToTRAG.

        This method is an alias for process() to conform to the BaseReasoner interface.

        Args:
            query: The query to reason about
            context: Optional context for the query
            callback: Optional callback function for streaming
            **kwargs: Additional arguments to pass to the process method

        Returns:
            Dictionary containing the reasoning results
        """
        context_dict = {"context": context} if context else None

        return self.process(
            query=query,
            context=context_dict,
            callback=callback,
            **kwargs
        )
