"""Combined reasoning methods."""

# Import RAG-TOT-COT
try:
    from .rag_tot_cot import RAGTOTCOTReasoner
except ImportError:
    # RAG-TOT-COT is optional
    pass

# Import MultiQueryRAG
try:
    from .multi_query_rag import MultiQueryRAG
except ImportError:
    # MultiQueryRAG is optional
    pass

# Import MultiQueryToTRAG
try:
    from .multi_query_tot_rag import MultiQueryToTRAG
except ImportError:
    # MultiQueryToTRAG is optional
    pass

__all__ = [
    'RAGTOTCOTReasoner',
    'MultiQueryRAG',
    'MultiQueryToTRAG'
]