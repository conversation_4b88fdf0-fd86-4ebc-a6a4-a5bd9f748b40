"""
Multi-Query RAG Reasoner implementation.

This module provides a combined reasoning approach that integrates:
1. Multi-Query Decomposition (MQD)
2. Retrieval-Augmented Generation (RAG)

The approach works by:
1. Decomposing a complex query into simpler sub-queries
2. Retrieving relevant documents for each sub-query
3. Generating answers for each sub-query using RAG
4. Synthesizing the answers into a coherent response

Enhanced features:
- Advanced error handling with fallback mechanisms
- Document relevance scoring and filtering
- Document-based verification during synthesis
- Parallel processing of sub-queries (optional)
- Adaptive query refinement based on document retrieval
"""

import json
import time
import asyncio
import concurrent.futures
import traceback
from typing import Dict, List, Any, Optional, Callable, Union, Tuple, Set

from ...utils.structured_logging import get_logger
from ...utils.performance_metrics import measure_latency, measure_block_latency
from ...utils.distributed_tracing import trace_function, span
from ..base import BaseReasoner
from ..multi_query_decomposer import MultiQueryDecomposition
from ..base_rag import BaseRAG

# Create a logger
logger = get_logger(__name__)

class MultiQueryRAG(BaseReasoner):
    """
    Implements a combination of Multi-Query Decomposition (MQD) and
    Retrieval-Augmented Generation (RAG).

    This approach works by:
    1. Decomposing a complex query into simpler sub-queries
    2. Retrieving relevant documents for each sub-query
    3. Generating answers for each sub-query using RAG
    4. Synthesizing the answers into a coherent response

    Enhanced features:
    - Advanced error handling with fallback mechanisms
    - Document relevance scoring and filtering
    - Document-based verification during synthesis
    - Parallel processing of sub-queries (optional)
    - Adaptive query refinement based on document retrieval
    """

    def __init__(
        self,
        rag_instance: BaseRAG,
        provider: str = "openrouter",
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        language: str = "en",
        max_depth: int = 2,
        track_dependencies: bool = True,
        enable_synthesis: bool = True,
        use_cache: bool = True,
        cache_size: int = 100,
        verbose: bool = False,
        enable_parallel: bool = False,
        max_workers: int = 4,
        min_relevance_score: float = 0.6,
        enable_document_verification: bool = True,
        enable_adaptive_refinement: bool = True,
        max_refinement_attempts: int = 2,
        use_mock_for_testing: bool = False
    ):
        """
        Initialize the MultiQueryRAG reasoner.

        Args:
            rag_instance: Instance of a RAG system
            provider: The provider to use ("openai", "anthropic", "openrouter", etc.)
            model: The model to use (if None, will use provider's default)
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            language: Language for prompts ("en", "vi", etc.)
            max_depth: Maximum depth for hierarchical decomposition
            track_dependencies: Whether to track dependencies between sub-queries
            enable_synthesis: Whether to enable result synthesis
            use_cache: Whether to use caching
            cache_size: Size of the cache
            verbose: Whether to print verbose output
            enable_parallel: Whether to process sub-queries in parallel
            max_workers: Maximum number of parallel workers
            min_relevance_score: Minimum relevance score for documents
            enable_document_verification: Whether to verify synthesis with documents
            enable_adaptive_refinement: Whether to adaptively refine queries
            max_refinement_attempts: Maximum number of refinement attempts
            use_mock_for_testing: Whether to use mock providers for testing
        """
        super().__init__()

        self.rag_instance = rag_instance
        self.provider = provider
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.language = language
        self.verbose = verbose
        self.enable_parallel = enable_parallel
        self.max_workers = max_workers
        self.min_relevance_score = min_relevance_score
        self.enable_document_verification = enable_document_verification
        self.enable_adaptive_refinement = enable_adaptive_refinement
        self.max_refinement_attempts = max_refinement_attempts
        self.use_mock_for_testing = use_mock_for_testing

        # Initialize MultiQueryDecomposition
        self.decomposer = MultiQueryDecomposition(
            provider=provider,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            language=language,
            max_depth=max_depth,
            track_dependencies=track_dependencies,
            enable_synthesis=enable_synthesis,
            use_cache=use_cache,
            cache_size=cache_size,
            use_mock_for_testing=use_mock_for_testing
        )

        # Initialize document cache
        self.document_cache = {}

        logger.info(f"Initialized MultiQueryRAG with provider={provider}, model={model or 'default'}, "
                   f"enable_parallel={enable_parallel}, enable_document_verification={enable_document_verification}")

    @trace_function(name="multi_query_rag_process")
    @measure_latency("multi_query_rag_process")
    def process(
        self,
        query: str,
        top_k: int = 5,
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        max_sub_queries: int = 5,
        min_sub_queries: int = 2,
        context: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process a query using MultiQueryRAG.

        Args:
            query: The query to process
            top_k: Number of documents to retrieve for each sub-query
            custom_system_prompt: Custom system prompt to use
            custom_user_prompt: Custom user prompt template to use
            callback: Optional callback function for streaming
            max_sub_queries: Maximum number of sub-queries to generate
            min_sub_queries: Minimum number of sub-queries to generate
            context: Optional context for the query
            **kwargs: Additional arguments to pass to the RAG process method

        Returns:
            Dictionary containing the query, sub-queries, answers, and synthesized response
        """
        start_time = time.time()

        if self.verbose:
            logger.info(f"Processing query: {query}")
            if callback:
                callback(f"Decomposing query: {query}\n\n")

        try:
            # Step 1: Decompose the query into sub-queries
            decomposition_result = self.decomposer.decompose(
                query=query,
                context=context,
                max_sub_queries=max_sub_queries,
                min_sub_queries=min_sub_queries
            )

            is_decomposed = decomposition_result["is_decomposed"]
            sub_queries = decomposition_result["sub_queries"]
            processing_order = decomposition_result["processing_order"]

            if self.verbose:
                logger.info(f"Query decomposed into {len(sub_queries)} sub-queries")
                if callback:
                    callback(f"Query decomposed into {len(sub_queries)} sub-queries:\n")
                    for i, sub_query in enumerate(sub_queries):
                        callback(f"{i+1}. {sub_query['query']}\n")
                    callback("\n")

            # If the query wasn't decomposed, just process it directly
            if not is_decomposed:
                if self.verbose:
                    logger.info("Query is not complex, processing directly")
                    if callback:
                        callback("Query is not complex, processing directly\n\n")

                return self._process_single_query(
                    query=query,
                    top_k=top_k,
                    custom_system_prompt=custom_system_prompt,
                    custom_user_prompt=custom_user_prompt,
                    callback=callback,
                    **kwargs
                )

            # Step 2: Process each sub-query using RAG
            sub_query_results = {}
            all_documents = []

            # Determine which processing method to use
            if self.enable_parallel and len(processing_order) > 1:
                # Process sub-queries in parallel
                sub_query_results, all_documents = self._process_sub_queries_parallel(
                    decomposition_result=decomposition_result,
                    processing_order=processing_order,
                    original_query=query,
                    top_k=top_k,
                    custom_system_prompt=custom_system_prompt,
                    custom_user_prompt=custom_user_prompt,
                    callback=callback,
                    **kwargs
                )
            else:
                # Process sub-queries sequentially
                sub_query_results, all_documents = self._process_sub_queries_sequential(
                    decomposition_result=decomposition_result,
                    processing_order=processing_order,
                    original_query=query,
                    top_k=top_k,
                    custom_system_prompt=custom_system_prompt,
                    custom_user_prompt=custom_user_prompt,
                    callback=callback,
                    **kwargs
                )

            # Step 3: Synthesize the results
            if self.verbose:
                logger.info("Synthesizing results")
                if callback:
                    callback("Synthesizing results...\n\n")

            # If document verification is enabled, use it for synthesis
            if self.enable_document_verification and all_documents:
                synthesis = self._synthesize_with_document_verification(
                    query_results=sub_query_results,
                    original_query=query,
                    documents=all_documents
                )
            else:
                synthesis = self.decomposer.synthesize_results(
                    query_results=sub_query_results,
                    original_query=query
                )

            # Calculate latency
            latency = time.time() - start_time

            # Prepare the result
            result = {
                "query": query,
                "is_decomposed": is_decomposed,
                "sub_queries": sub_queries,
                "sub_query_results": sub_query_results,
                "documents": all_documents,
                "answer": synthesis,
                "processing_order": processing_order,
                "model": self.model,
                "provider": self.provider,
                "latency": latency
            }

            if self.verbose:
                logger.info(f"MultiQueryRAG completed in {latency:.2f} seconds")
                if callback:
                    callback(f"\nFinal answer: {synthesis}\n")

            return result

        except Exception as e:
            error_msg = f"Error in MultiQueryRAG process: {str(e)}"
            logger.error(error_msg)
            logger.error(f"Traceback: {traceback.format_exc()}")

            # Fallback to direct processing
            if self.verbose:
                logger.info("Falling back to direct processing")
                if callback:
                    callback(f"Error in decomposition: {str(e)}\nFalling back to direct processing\n\n")

            try:
                return self._process_single_query(
                    query=query,
                    top_k=top_k,
                    custom_system_prompt=custom_system_prompt,
                    custom_user_prompt=custom_user_prompt,
                    callback=callback,
                    **kwargs
                )
            except Exception as fallback_error:
                error_msg = f"Error in fallback processing: {str(fallback_error)}"
                logger.error(error_msg)

                # Return error result
                return {
                    "query": query,
                    "is_decomposed": False,
                    "error": error_msg,
                    "answer": f"Error processing query: {str(e)}. Fallback also failed: {str(fallback_error)}",
                    "model": self.model,
                    "provider": self.provider,
                    "latency": time.time() - start_time
                }

    def _process_single_query(
        self,
        query: str,
        top_k: int = 5,
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process a single query using RAG.

        Args:
            query: The query to process
            top_k: Number of documents to retrieve
            custom_system_prompt: Custom system prompt to use
            custom_user_prompt: Custom user prompt template to use
            callback: Optional callback function for streaming
            **kwargs: Additional arguments to pass to the RAG process method

        Returns:
            Dictionary containing the query, documents, and answer
        """
        try:
            # Process the query using RAG
            result = self.rag_instance.process(
                query=query,
                top_k=top_k,
                custom_system_prompt=custom_system_prompt,
                custom_user_prompt=custom_user_prompt,
                callback=callback,
                **kwargs
            )

            # If adaptive refinement is enabled, check document relevance
            if self.enable_adaptive_refinement and result.get("documents"):
                avg_score = self._calculate_average_relevance(result["documents"])

                if avg_score < self.min_relevance_score:
                    # Try to refine the query
                    refined_result = self._refine_query_and_process(
                        original_query=query,
                        documents=result["documents"],
                        top_k=top_k,
                        custom_system_prompt=custom_system_prompt,
                        custom_user_prompt=custom_user_prompt,
                        callback=callback,
                        **kwargs
                    )

                    if refined_result:
                        return refined_result

            return result

        except Exception as e:
            error_msg = f"Error in single query processing: {str(e)}"
            logger.error(error_msg)

            # Return error result
            return {
                "query": query,
                "error": error_msg,
                "answer": f"Error processing query: {str(e)}",
                "model": self.model,
                "provider": self.provider
            }

    def _process_sub_queries_sequential(
        self,
        decomposition_result: Dict[str, Any],
        processing_order: List[str],
        original_query: str,
        top_k: int = 5,
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        **kwargs
    ) -> Tuple[Dict[str, str], List[Dict[str, Any]]]:
        """
        Process sub-queries sequentially.

        Args:
            decomposition_result: Result of query decomposition
            processing_order: Order to process queries in
            original_query: The original query
            top_k: Number of documents to retrieve for each sub-query
            custom_system_prompt: Custom system prompt to use
            custom_user_prompt: Custom user prompt template to use
            callback: Optional callback function for streaming
            **kwargs: Additional arguments to pass to the RAG process method

        Returns:
            Tuple of (sub_query_results, all_documents)
        """
        sub_query_results = {}
        all_documents = []

        for query_id in processing_order:
            # Skip the original query
            if query_id == self._generate_query_id(original_query):
                continue

            sub_query_info = decomposition_result["dependency_graph"][query_id]
            sub_query = sub_query_info["query"]

            if self.verbose:
                logger.info(f"Processing sub-query: {sub_query}")
                if callback:
                    callback(f"Processing sub-query: {sub_query}\n")

            try:
                # Process the sub-query using RAG
                sub_result = self.rag_instance.process(
                    query=sub_query,
                    top_k=top_k,
                    custom_system_prompt=custom_system_prompt,
                    custom_user_prompt=custom_user_prompt,
                    **kwargs
                )

                # Filter documents by relevance score
                documents = self._filter_documents_by_relevance(sub_result.get("documents", []))

                # Store the result
                sub_query_results[query_id] = sub_result["answer"]

                # Collect documents for later use
                all_documents.extend(documents)

                # Cache documents for this query
                self.document_cache[query_id] = documents

                if self.verbose and callback:
                    callback(f"Answer: {sub_result['answer']}\n\n")

            except Exception as e:
                error_msg = f"Error processing sub-query '{sub_query}': {str(e)}"
                logger.error(error_msg)

                # Store error as result
                sub_query_results[query_id] = f"Error: {str(e)}"

                if self.verbose and callback:
                    callback(f"Error: {str(e)}\n\n")

        return sub_query_results, all_documents

    def _process_sub_queries_parallel(
        self,
        decomposition_result: Dict[str, Any],
        processing_order: List[str],
        original_query: str,
        top_k: int = 5,
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        **kwargs
    ) -> Tuple[Dict[str, str], List[Dict[str, Any]]]:
        """
        Process sub-queries in parallel.

        Args:
            decomposition_result: Result of query decomposition
            processing_order: Order to process queries in
            original_query: The original query
            top_k: Number of documents to retrieve for each sub-query
            custom_system_prompt: Custom system prompt to use
            custom_user_prompt: Custom user prompt template to use
            callback: Optional callback function for streaming
            **kwargs: Additional arguments to pass to the RAG process method

        Returns:
            Tuple of (sub_query_results, all_documents)
        """
        sub_query_results = {}
        all_documents = []

        # Filter out the original query
        original_query_id = self._generate_query_id(original_query)
        filtered_order = [qid for qid in processing_order if qid != original_query_id]

        # Group queries by dependency level
        dependency_levels = self._group_by_dependency_level(
            decomposition_result["dependency_graph"],
            filtered_order
        )

        if self.verbose:
            logger.info(f"Processing {len(filtered_order)} sub-queries in {len(dependency_levels)} dependency levels")

        # Process each dependency level in order
        for level, level_query_ids in enumerate(dependency_levels):
            if self.verbose:
                logger.info(f"Processing dependency level {level} with {len(level_query_ids)} queries")
                if callback:
                    callback(f"Processing dependency level {level} with {len(level_query_ids)} queries\n")

            # Process this level in parallel
            level_results = {}
            level_documents = []

            with concurrent.futures.ThreadPoolExecutor(max_workers=min(self.max_workers, len(level_query_ids))) as executor:
                # Create a dictionary to store futures
                future_to_query_id = {}

                # Submit tasks
                for query_id in level_query_ids:
                    sub_query_info = decomposition_result["dependency_graph"][query_id]
                    sub_query = sub_query_info["query"]

                    future = executor.submit(
                        self._process_single_sub_query,
                        sub_query=sub_query,
                        query_id=query_id,
                        top_k=top_k,
                        custom_system_prompt=custom_system_prompt,
                        custom_user_prompt=custom_user_prompt,
                        **kwargs
                    )

                    future_to_query_id[future] = query_id

                # Process results as they complete
                for future in concurrent.futures.as_completed(future_to_query_id):
                    query_id = future_to_query_id[future]
                    sub_query_info = decomposition_result["dependency_graph"][query_id]
                    sub_query = sub_query_info["query"]

                    try:
                        result, documents = future.result()

                        # Store results
                        level_results[query_id] = result
                        level_documents.extend(documents)

                        if self.verbose:
                            logger.info(f"Completed sub-query: {sub_query}")
                            if callback:
                                callback(f"Completed sub-query: {sub_query}\nAnswer: {result}\n\n")

                    except Exception as e:
                        error_msg = f"Error processing sub-query '{sub_query}': {str(e)}"
                        logger.error(error_msg)

                        # Store error as result
                        level_results[query_id] = f"Error: {str(e)}"

                        if self.verbose and callback:
                            callback(f"Error processing sub-query: {sub_query}\nError: {str(e)}\n\n")

            # Update overall results
            sub_query_results.update(level_results)
            all_documents.extend(level_documents)

        return sub_query_results, all_documents

    def _process_single_sub_query(
        self,
        sub_query: str,
        query_id: str,
        top_k: int = 5,
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        **kwargs
    ) -> Tuple[str, List[Dict[str, Any]]]:
        """
        Process a single sub-query.

        Args:
            sub_query: The sub-query to process
            query_id: ID of the sub-query
            top_k: Number of documents to retrieve
            custom_system_prompt: Custom system prompt to use
            custom_user_prompt: Custom user prompt template to use
            **kwargs: Additional arguments to pass to the RAG process method

        Returns:
            Tuple of (answer, documents)
        """
        try:
            # Process the sub-query using RAG
            sub_result = self.rag_instance.process(
                query=sub_query,
                top_k=top_k,
                custom_system_prompt=custom_system_prompt,
                custom_user_prompt=custom_user_prompt,
                **kwargs
            )

            # Filter documents by relevance score
            documents = self._filter_documents_by_relevance(sub_result.get("documents", []))

            # Cache documents for this query
            self.document_cache[query_id] = documents

            return sub_result["answer"], documents

        except Exception as e:
            logger.error(f"Error in _process_single_sub_query: {str(e)}")
            return f"Error: {str(e)}", []

    def _filter_documents_by_relevance(self, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Filter documents by relevance score.

        Args:
            documents: List of documents to filter

        Returns:
            Filtered list of documents
        """
        if not documents:
            return []

        # Filter documents by relevance score
        filtered_docs = []

        for doc in documents:
            # Check if document has a score
            score = doc.get("score", 0.0)

            # Include document if score is above threshold
            if score >= self.min_relevance_score:
                filtered_docs.append(doc)

        # If no documents passed the filter, include the top document
        if not filtered_docs and documents:
            # Sort by score and take the top one
            sorted_docs = sorted(documents, key=lambda d: d.get("score", 0.0), reverse=True)
            filtered_docs.append(sorted_docs[0])

        return filtered_docs

    def _calculate_average_relevance(self, documents: List[Dict[str, Any]]) -> float:
        """
        Calculate the average relevance score of documents.

        Args:
            documents: List of documents

        Returns:
            Average relevance score
        """
        if not documents:
            return 0.0

        total_score = sum(doc.get("score", 0.0) for doc in documents)
        return total_score / len(documents)

    def _refine_query_and_process(
        self,
        original_query: str,
        documents: List[Dict[str, Any]],
        top_k: int = 5,
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        **kwargs
    ) -> Optional[Dict[str, Any]]:
        """
        Refine a query based on retrieved documents and process it.

        Args:
            original_query: The original query
            documents: Retrieved documents
            top_k: Number of documents to retrieve
            custom_system_prompt: Custom system prompt to use
            custom_user_prompt: Custom user prompt template to use
            callback: Optional callback function for streaming
            **kwargs: Additional arguments to pass to the RAG process method

        Returns:
            Processed result or None if refinement failed
        """
        if self.verbose:
            logger.info(f"Refining query: {original_query}")
            if callback:
                callback(f"Refining query: {original_query}\n")

        try:
            # Generate a refined query
            refined_query = self._generate_refined_query(original_query, documents)

            if not refined_query or refined_query == original_query:
                if self.verbose:
                    logger.info("Query refinement failed or produced the same query")
                return None

            if self.verbose:
                logger.info(f"Refined query: {refined_query}")
                if callback:
                    callback(f"Refined query: {refined_query}\n")

            # Process the refined query
            for attempt in range(self.max_refinement_attempts):
                try:
                    result = self.rag_instance.process(
                        query=refined_query,
                        top_k=top_k,
                        custom_system_prompt=custom_system_prompt,
                        custom_user_prompt=custom_user_prompt,
                        **kwargs
                    )

                    # Check if the result is better
                    if result.get("documents"):
                        new_avg_score = self._calculate_average_relevance(result["documents"])

                        if new_avg_score >= self.min_relevance_score:
                            # Add refinement info to result
                            result["original_query"] = original_query
                            result["refined_query"] = refined_query
                            result["refinement_attempt"] = attempt + 1

                            return result

                    # If we're here, the refinement didn't improve results
                    # Try to refine again if we have attempts left
                    if attempt < self.max_refinement_attempts - 1:
                        refined_query = self._generate_refined_query(refined_query, result.get("documents", []))

                        if self.verbose:
                            logger.info(f"Re-refined query (attempt {attempt+2}): {refined_query}")
                            if callback:
                                callback(f"Re-refined query (attempt {attempt+2}): {refined_query}\n")

                except Exception as e:
                    logger.error(f"Error processing refined query (attempt {attempt+1}): {str(e)}")

                    # Try again with a different refinement if we have attempts left
                    if attempt < self.max_refinement_attempts - 1:
                        refined_query = self._generate_refined_query(original_query, documents, attempt=attempt+1)

            # If we're here, refinement didn't help
            return None

        except Exception as e:
            logger.error(f"Error in query refinement: {str(e)}")
            return None

    def _generate_refined_query(
        self,
        query: str,
        documents: List[Dict[str, Any]],
        attempt: int = 0
    ) -> str:
        """
        Generate a refined query based on retrieved documents.

        Args:
            query: The original query
            documents: Retrieved documents
            attempt: Refinement attempt number

        Returns:
            Refined query
        """
        if not documents:
            return query

        try:
            # Extract relevant content from documents
            doc_content = "\n\n".join([
                f"Document {i+1}: {doc.get('content', '')[:500]}..."
                for i, doc in enumerate(documents[:3])
            ])

            # Create system prompt
            if self.language == "vi":
                system_prompt = """Bạn là một hệ thống tinh chỉnh câu hỏi để cải thiện kết quả tìm kiếm.
                Nhiệm vụ của bạn là phân tích câu hỏi gốc và các tài liệu đã truy xuất, sau đó tạo ra một câu hỏi được tinh chỉnh
                sẽ giúp tìm kiếm các tài liệu liên quan hơn. Câu hỏi được tinh chỉnh nên:
                1. Giữ nguyên ý định của câu hỏi gốc
                2. Thêm các từ khóa cụ thể từ các tài liệu liên quan
                3. Loại bỏ các thuật ngữ mơ hồ hoặc quá rộng
                4. Tập trung vào các khía cạnh cụ thể của câu hỏi
                5. Sử dụng ngôn ngữ chính xác và rõ ràng"""
            else:
                system_prompt = """You are a query refinement system that improves search results.
                Your task is to analyze the original query and retrieved documents, then generate a refined query
                that will help retrieve more relevant documents. The refined query should:
                1. Maintain the original intent of the query
                2. Add specific keywords from relevant documents
                3. Remove ambiguous or overly broad terms
                4. Focus on specific aspects of the query
                5. Use precise and clear language"""

            # Create user prompt
            if self.language == "vi":
                user_prompt = f"""Câu hỏi gốc: {query}

Các tài liệu đã truy xuất:
{doc_content}

Dựa trên câu hỏi gốc và các tài liệu đã truy xuất, hãy tạo ra một câu hỏi được tinh chỉnh sẽ giúp tìm kiếm các tài liệu liên quan hơn.
Câu hỏi được tinh chỉnh nên dài hơn và cụ thể hơn, nhưng vẫn giữ nguyên ý định của câu hỏi gốc.

Lần thử tinh chỉnh: {attempt + 1}"""
            else:
                user_prompt = f"""Original query: {query}

Retrieved documents:
{doc_content}

Based on the original query and the retrieved documents, generate a refined query that will help retrieve more relevant documents.
The refined query should be longer and more specific, but still maintain the original intent of the query.

Refinement attempt: {attempt + 1}"""

            # Generate refined query
            provider = self.decomposer.base_decomposer.api_provider
            refined_query = provider.generate(
                prompt=user_prompt,
                model=self.model,
                system_prompt=system_prompt,
                temperature=0.7,
                max_tokens=200
            )

            # Clean up the refined query
            refined_query = refined_query.strip()

            # If the refined query is too short, return the original
            if len(refined_query) < len(query) / 2:
                return query

            return refined_query

        except Exception as e:
            logger.error(f"Error generating refined query: {str(e)}")
            return query

    def _synthesize_with_document_verification(
        self,
        query_results: Dict[str, str],
        original_query: str,
        documents: List[Dict[str, Any]]
    ) -> str:
        """
        Synthesize results with document verification.

        Args:
            query_results: Dictionary mapping query IDs to their results
            original_query: The original query
            documents: Retrieved documents

        Returns:
            Synthesized response
        """
        try:
            # First, get the basic synthesis
            synthesis = self.decomposer.synthesize_results(
                query_results=query_results,
                original_query=original_query
            )

            # If no documents, return the basic synthesis
            if not documents:
                return synthesis

            # Extract relevant content from documents
            doc_content = "\n\n".join([
                f"Document {i+1}: {doc.get('content', '')[:500]}..."
                for i, doc in enumerate(documents[:5])
            ])

            # Create system prompt for verification
            if self.language == "vi":
                system_prompt = """Bạn là một hệ thống xác minh thông tin dựa trên tài liệu.
                Nhiệm vụ của bạn là xác minh câu trả lời tổng hợp dựa trên các tài liệu đã truy xuất.
                Hãy kiểm tra xem câu trả lời có:
                1. Chính xác và phù hợp với thông tin trong tài liệu
                2. Không chứa thông tin sai lệch hoặc không được hỗ trợ
                3. Đầy đủ và toàn diện

                Nếu câu trả lời có vấn đề, hãy sửa chữa nó để đảm bảo tính chính xác và đầy đủ."""
            else:
                system_prompt = """You are a document-based verification system.
                Your task is to verify the synthesized answer based on the retrieved documents.
                Check if the answer is:
                1. Accurate and consistent with the information in the documents
                2. Free from incorrect or unsupported information
                3. Complete and comprehensive

                If the answer has issues, correct it to ensure accuracy and completeness."""

            # Create user prompt for verification
            if self.language == "vi":
                user_prompt = f"""Câu hỏi gốc: {original_query}

Câu trả lời tổng hợp:
{synthesis}

Tài liệu đã truy xuất:
{doc_content}

Hãy xác minh câu trả lời tổng hợp dựa trên các tài liệu đã truy xuất. Nếu câu trả lời có vấn đề, hãy sửa chữa nó.
Trả về câu trả lời đã được xác minh và sửa chữa (nếu cần)."""
            else:
                user_prompt = f"""Original query: {original_query}

Synthesized answer:
{synthesis}

Retrieved documents:
{doc_content}

Please verify the synthesized answer based on the retrieved documents. If the answer has issues, correct it.
Return the verified and corrected answer (if needed)."""

            # Generate verified synthesis
            provider = self.decomposer.base_decomposer.api_provider
            verified_synthesis = provider.generate(
                prompt=user_prompt,
                model=self.model,
                system_prompt=system_prompt,
                temperature=0.3,
                max_tokens=1500
            )

            return verified_synthesis.strip()

        except Exception as e:
            logger.error(f"Error in document verification: {str(e)}")
            return synthesis

    def _group_by_dependency_level(
        self,
        dependency_graph: Dict[str, Dict[str, Any]],
        query_ids: List[str]
    ) -> List[List[str]]:
        """
        Group queries by dependency level for parallel processing.

        Args:
            dependency_graph: Dependency graph from decomposition
            query_ids: List of query IDs to group

        Returns:
            List of lists of query IDs grouped by dependency level
        """
        # Create a dictionary to track dependency counts
        dependency_counts = {}

        # Initialize dependency counts
        for query_id in query_ids:
            dependencies = dependency_graph[query_id].get("dependencies", [])
            # Filter out dependencies that aren't in our query_ids list
            filtered_deps = [dep for dep in dependencies if dep in query_ids]
            dependency_counts[query_id] = len(filtered_deps)

        # Group queries by dependency level
        levels = []
        remaining = set(query_ids)

        while remaining:
            # Find all queries with no remaining dependencies
            current_level = [qid for qid in remaining if dependency_counts[qid] == 0]

            if not current_level:
                # Circular dependency detected, break it
                logger.warning("Circular dependency detected, breaking cycle")
                current_level = [next(iter(remaining))]

            # Add current level to levels
            levels.append(current_level)

            # Remove current level from remaining
            remaining -= set(current_level)

            # Update dependency counts
            for qid in remaining:
                for completed_qid in current_level:
                    if completed_qid in dependency_graph[qid].get("dependencies", []):
                        dependency_counts[qid] -= 1

        return levels

    def _generate_query_id(self, query: str) -> str:
        """
        Generate a unique ID for a query.

        Args:
            query: The query to generate an ID for

        Returns:
            A unique ID for the query
        """
        import hashlib
        return hashlib.md5(query.encode()).hexdigest()

    @trace_function(name="multi_query_rag_reason")
    def reason(
        self,
        query: str,
        context: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Reason about a query using MultiQueryRAG.

        This method is an alias for process() to conform to the BaseReasoner interface.

        Args:
            query: The query to reason about
            context: Optional context for the query
            callback: Optional callback function for streaming
            **kwargs: Additional arguments to pass to the process method

        Returns:
            Dictionary containing the reasoning results
        """
        context_dict = {"context": context} if context else None

        return self.process(
            query=query,
            context=context_dict,
            callback=callback,
            **kwargs
        )
