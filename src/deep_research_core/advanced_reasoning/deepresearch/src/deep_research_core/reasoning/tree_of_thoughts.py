"""
Tree of Thoughts (ToT) Reasoning.

Module này triển khai kỹ thuật suy luận Tree of Thoughts (ToT),
cho phép mô hình khám phá không gian tìm kiếm của các suy nghĩ với nhiều bước.
"""

import time
import logging
import uuid
from typing import List, Dict, Any, Optional, Tuple, Callable, Set, Union
from dataclasses import dataclass
import numpy as np
from collections import defaultdict

from ..utils.structured_logging import get_logger

# Thiết lập logger
logger = get_logger(__name__)

@dataclass
class ThoughtNode:
    """
    Một nút suy nghĩ trong cây Tree of Thoughts.
    """
    
    id: str  # Đ<PERSON>nh danh duy nhất cho nút
    content: str  # Nội dung suy nghĩ
    parent_id: Optional[str] = None  # ID của nút cha
    depth: int = 0  # Độ sâu trong cây
    score: float = 0.0  # Điể<PERSON> đánh giá
    metadata: Dict[str, Any] = None  # Siêu dữ liệu bổ sung
    children: List["ThoughtNode"] = None  # Các nút con
    
    def __post_init__(self):
        """Khởi tạo sau khi dataclass được tạo."""
        if self.metadata is None:
            self.metadata = {}
        if self.children is None:
            self.children = []
    
    def add_child(self, thought: "ThoughtNode") -> None:
        """Thêm một nút con."""
        self.children.append(thought)
    
    def get_path_to_root(self) -> List["ThoughtNode"]:
        """Lấy đường dẫn từ nút hiện tại đến gốc."""
        path = [self]
        current = self
        
        while current.parent_id:
            parent = None
            # Tìm nút cha trong cây
            # Trong triển khai thực tế, cần một cơ chế hiệu quả hơn
            # để truy cập nút cha trực tiếp
            # ...
            
            if parent:
                path.append(parent)
                current = parent
            else:
                break
        
        return path[::-1]  # Đảo ngược để bắt đầu từ gốc
    
    def to_dict(self) -> Dict[str, Any]:
        """Chuyển đổi nút thành dictionary."""
        return {
            "id": self.id,
            "content": self.content,
            "parent_id": self.parent_id,
            "depth": self.depth, 
            "score": self.score,
            "metadata": self.metadata,
            "children": [child.to_dict() for child in self.children]
        }


class TreeOfThoughts:
    """
    Triển khai kỹ thuật suy luận Tree of Thoughts (ToT).
    
    ToT cho phép mô hình khám phá không gian tìm kiếm của các suy nghĩ
    với nhiều bước và đánh giá các đường dẫn khác nhau.
    """
    
    def __init__(
        self,
        model: Any,
        thought_generator: Callable[[str, int], List[str]],
        thought_evaluator: Callable[[List[str], str], List[float]],
        max_depth: int = 5,
        beam_width: int = 3,
        max_steps: int = 100,
        debug: bool = False
    ):
        """
        Khởi tạo TreeOfThoughts.
        
        Args:
            model: Mô hình ngôn ngữ để tạo suy nghĩ
            thought_generator: Hàm tạo các suy nghĩ mới
            thought_evaluator: Hàm đánh giá các suy nghĩ
            max_depth: Độ sâu tối đa của cây
            beam_width: Số nhánh tối đa để khám phá ở mỗi bước
            max_steps: Số bước tối đa
            debug: Chế độ debug
        """
        self.model = model
        self.thought_generator = thought_generator
        self.thought_evaluator = thought_evaluator
        self.max_depth = max_depth
        self.beam_width = beam_width
        self.max_steps = max_steps
        self.debug = debug
        
        self.root = None
        self.all_nodes = {}  # Dictionary để truy cập nhanh các nút theo ID
        self.best_paths = []  # Các đường dẫn tốt nhất được tìm thấy
        self.steps_taken = 0  # Đếm số bước đã thực hiện
        
        logger.info(f"Đã khởi tạo TreeOfThoughts với max_depth={max_depth}, beam_width={beam_width}")
    
    def _create_root(self, problem: str) -> ThoughtNode:
        """
        Tạo nút gốc cho cây.
        
        Args:
            problem: Mô tả vấn đề
            
        Returns:
            Nút gốc
        """
        root_id = str(uuid.uuid4())
        
        # Khởi tạo nút gốc
        root = ThoughtNode(
            id=root_id,
            content=f"Vấn đề: {problem}",
            depth=0,
            score=1.0
        )
        
        self.all_nodes[root_id] = root
        return root
    
    def solve(self, problem: str) -> Tuple[bool, List[ThoughtNode], Dict[str, Any]]:
        """
        Giải quyết một vấn đề bằng phương pháp ToT.
        
        Args:
            problem: Mô tả vấn đề
            
        Returns:
            success: Thành công hay không
            best_path: Đường dẫn tốt nhất
            stats: Thống kê về quá trình giải quyết
        """
        start_time = time.time()
        
        # Khởi tạo cây với vấn đề
        self.root = self._create_root(problem)
        self.steps_taken = 0
        
        # Thông tin ban đầu
        logger.info(f"Bắt đầu giải quyết vấn đề: {problem[:100]}...")
        
        # Chạy beam search
        final_thoughts = self._beam_search(self.root, problem)
        
        # Đánh giá kết quả cuối cùng nếu có
        success = False
        best_thought = None
        
        if final_thoughts:
            # Đánh giá các suy nghĩ cuối cùng
            thoughts_content = [t.content for t in final_thoughts]
            scores = self.thought_evaluator(thoughts_content, problem)
            
            # Tìm suy nghĩ tốt nhất
            best_idx = np.argmax(scores)
            best_thought = final_thoughts[best_idx]
            best_score = scores[best_idx]
            
            # Cập nhật điểm cho suy nghĩ tốt nhất
            best_thought.score = best_score
            
            # Xác định thành công dựa trên điểm số
            success = best_score > 0.8  # Ngưỡng coi là thành công
            
            # Xây dựng đường dẫn tốt nhất
            best_path = best_thought.get_path_to_root()
            self.best_paths.append(best_path)
        else:
            best_path = []
        
        # Tạo thống kê
        stats = {
            "success": success,
            "steps_taken": self.steps_taken,
            "total_nodes": len(self.all_nodes),
            "execution_time": time.time() - start_time,
            "best_score": best_thought.score if best_thought else 0.0
        }
        
        logger.info(f"Hoàn thành giải quyết vấn đề. Thành công: {success}, "
                   f"Số bước: {self.steps_taken}, Thời gian: {stats['execution_time']:.2f}s")
        
        return success, best_path, stats
    
    def _beam_search(self, node: ThoughtNode, problem: str) -> List[ThoughtNode]:
        """
        Thực hiện beam search từ một nút.
        
        Args:
            node: Nút bắt đầu
            problem: Mô tả vấn đề
            
        Returns:
            Danh sách các nút lá tốt nhất
        """
        # Kiểm tra điều kiện dừng
        if self.steps_taken >= self.max_steps:
            logger.debug(f"Đã đạt giới hạn số bước ({self.max_steps})")
            return []
        
        # Khởi tạo danh sách các nút hiện tại
        current_nodes = [node]
        
        # Lặp qua các độ sâu
        for depth in range(node.depth + 1, self.max_depth + 1):
            next_nodes = []
            
            # Xử lý mỗi nút ở độ sâu hiện tại
            for current_node in current_nodes:
                # Tạo suy nghĩ mới từ nút hiện tại
                current_path = current_node.get_path_to_root()
                context = "\n".join([n.content for n in current_path])
                
                # Tạo các suy nghĩ mới
                new_thoughts_content = self.thought_generator(context, self.beam_width * 2)
                
                # Đánh giá các suy nghĩ mới
                scores = self.thought_evaluator(new_thoughts_content, problem)
                
                # Tìm top-k suy nghĩ tốt nhất (beam search)
                top_indices = np.argsort(scores)[-self.beam_width:]
                
                # Tạo các nút mới cho top-k suy nghĩ
                for idx in top_indices:
                    thought_id = str(uuid.uuid4())
                    new_node = ThoughtNode(
                        id=thought_id,
                        content=new_thoughts_content[idx],
                        parent_id=current_node.id,
                        depth=depth,
                        score=scores[idx]
                    )
                    
                    # Thêm vào cây và danh sách
                    current_node.add_child(new_node)
                    self.all_nodes[thought_id] = new_node
                    next_nodes.append(new_node)
                    
                    # Cập nhật số bước
                    self.steps_taken += 1
            
            # Kiểm tra nếu không có nút tiếp theo
            if not next_nodes:
                return current_nodes
            
            # Sắp xếp các nút tiếp theo theo điểm số
            next_nodes.sort(key=lambda x: x.score, reverse=True)
            
            # Giữ lại beam_width nút tốt nhất
            current_nodes = next_nodes[:self.beam_width]
            
            # Kiểm tra xem có nút đạt ngưỡng giải pháp không
            solution_nodes = [n for n in current_nodes if self._is_solution(n, problem)]
            if solution_nodes:
                return solution_nodes
        
        # Trả về các nút lá tốt nhất
        return current_nodes
    
    def _is_solution(self, node: ThoughtNode, problem: str) -> bool:
        """
        Kiểm tra xem một nút có phải là giải pháp không.
        
        Args:
            node: Nút cần kiểm tra
            problem: Mô tả vấn đề gốc
            
        Returns:
            True nếu nút là giải pháp, False nếu không
        """
        # Trong triển khai thực tế, cần cách đánh giá phức tạp hơn
        # Có thể sử dụng mô hình để xác định
        # Tạm thời sử dụng điểm số làm tiêu chí
        return node.score > 0.9
    
    def visualize(self, output_file: str = "tot_tree.png") -> None:
        """
        Trực quan hóa cây suy luận.
        
        Args:
            output_file: Đường dẫn file đầu ra
        """
        # Trong triển khai thực tế, sử dụng thư viện như graphviz
        # Tạm thời chỉ in thông tin
        logger.info(f"Trực quan hóa cây ToT với {len(self.all_nodes)} nút")
        logger.info(f"Số đường dẫn tốt nhất: {len(self.best_paths)}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Lấy thống kê về cây suy luận.
        
        Returns:
            Thống kê
        """
        # Tính số nút ở mỗi độ sâu
        nodes_by_depth = defaultdict(int)
        
        for node in self.all_nodes.values():
            nodes_by_depth[node.depth] += 1
        
        # Tính điểm trung bình
        avg_score = np.mean([node.score for node in self.all_nodes.values()])
        
        return {
            "total_nodes": len(self.all_nodes),
            "max_depth": max(nodes_by_depth.keys()) if nodes_by_depth else 0,
            "nodes_by_depth": dict(nodes_by_depth),
            "avg_score": avg_score,
            "best_paths": len(self.best_paths)
        } 