"""
Reasoning Format Evaluator and Comparator.

This module provides tools for evaluating and comparing different reasoning formats
based on various metrics such as accuracy, latency, memory usage, and token count.
"""

import json
import time
import statistics
from typing import Dict, List, Any, Tuple, Optional, Union, Callable
import numpy as np

from deep_research_core.reasoning.base import BaseReasoner
from deep_research_core.utils.performance_metrics import measure_memory_usage
from deep_research_core.utils.text_similarity import semantic_similarity
from deep_research_core.utils.structured_logging import get_logger

logger = get_logger(__name__)


class ReasoningFormatEvaluator:
    """
    Evaluator for comparing different reasoning formats.

    This class provides methods for evaluating and comparing different reasoning formats
    based on various metrics such as accuracy, latency, memory usage, and token count.
    """

    def __init__(
        self,
        reasoners: Dict[str, BaseReasoner],
        metrics: List[str] = None,
        verbose: bool = False
    ):
        """
        Initialize the ReasoningFormatEvaluator.

        Args:
            reasoners: Dictionary mapping format names to reasoner instances
            metrics: List of metrics to evaluate (default: accuracy, latency, tokens, memory)
            verbose: Whether to print verbose output

        Raises:
            TypeError: If any reasoner is not an instance of BaseReasoner
        """
        # Validate reasoners
        for name, reasoner in reasoners.items():
            if not isinstance(reasoner, BaseReasoner):
                raise TypeError(f"Reasoner '{name}' must be an instance of BaseReasoner")

        self.reasoners = reasoners
        self.metrics = metrics or ["accuracy", "latency", "tokens", "memory"]
        self.verbose = verbose

    def evaluate_single_query(
        self,
        query: str,
        ground_truth: str = None,
        **kwargs
    ) -> Dict[str, Dict[str, Any]]:
        """
        Evaluate all reasoners on a single query.

        Args:
            query: The query to evaluate
            ground_truth: The ground truth answer (optional)
            **kwargs: Additional arguments to pass to the reasoners

        Returns:
            Dictionary mapping format names to evaluation results
        """
        results = {}

        for name, reasoner in self.reasoners.items():
            if self.verbose:
                logger.info(f"Evaluating {name} on query: {query[:50]}...")

            # Measure memory usage before reasoning
            mem_before = measure_memory_usage()

            # Measure reasoning time
            start_time = time.time()
            result = reasoner.reason(query, **kwargs)
            end_time = time.time()

            # Measure memory usage after reasoning
            mem_after = measure_memory_usage()

            # Calculate metrics
            latency = end_time - start_time
            memory_usage = mem_after - mem_before

            # Calculate accuracy if ground truth is provided
            accuracy = None
            if ground_truth and "answer" in result:
                accuracy = self._calculate_accuracy(result["answer"], ground_truth)

            # Calculate token count if available
            tokens = result.get("token_count", None)

            # Store metrics
            metrics = {
                "latency": latency,
                "memory": memory_usage
            }

            if accuracy is not None:
                metrics["accuracy"] = accuracy

            if tokens is not None:
                metrics["tokens"] = tokens

            # Store results
            results[name] = {
                "result": result,
                "metrics": metrics
            }

            if self.verbose:
                logger.info(f"  {name} metrics: {metrics}")

        return results

    def evaluate_multiple_queries(
        self,
        queries: List[str],
        ground_truths: List[str] = None,
        **kwargs
    ) -> Dict[str, Dict[str, Any]]:
        """
        Evaluate all reasoners on multiple queries.

        Args:
            queries: List of queries to evaluate
            ground_truths: List of ground truth answers (optional)
            **kwargs: Additional arguments to pass to the reasoners

        Returns:
            Dictionary mapping format names to aggregated evaluation results
        """
        if ground_truths and len(queries) != len(ground_truths):
            raise ValueError("Number of queries must match number of ground truths")

        all_results = []

        for i, query in enumerate(queries):
            ground_truth = ground_truths[i] if ground_truths else None
            result = self.evaluate_single_query(query, ground_truth, **kwargs)
            all_results.append(result)

        # Aggregate results
        aggregated_results = self._aggregate_results(all_results)

        # Add sample count
        for format_data in aggregated_results.values():
            format_data["sample_count"] = len(queries)

        return aggregated_results

    def _calculate_accuracy(self, answer: str, ground_truth: str) -> float:
        """
        Calculate accuracy of an answer compared to ground truth.

        Args:
            answer: The generated answer
            ground_truth: The ground truth answer

        Returns:
            Accuracy score between 0 and 1
        """
        # Use semantic similarity for accuracy calculation
        return semantic_similarity(answer, ground_truth)

    def _aggregate_results(
        self,
        results_list: List[Dict[str, Dict[str, Any]]]
    ) -> Dict[str, Dict[str, Any]]:
        """
        Aggregate results from multiple evaluations.

        Args:
            results_list: List of evaluation results

        Returns:
            Dictionary mapping format names to aggregated results
        """
        aggregated = {}

        # Initialize aggregated results
        for result_dict in results_list:
            for format_name, format_data in result_dict.items():
                if format_name not in aggregated:
                    aggregated[format_name] = {
                        "metrics": {metric: [] for metric in self.metrics},
                        "results": []
                    }

                # Collect metrics
                for metric, value in format_data["metrics"].items():
                    if metric in self.metrics:
                        aggregated[format_name]["metrics"][metric].append(value)

                # Collect results
                if "result" in format_data:
                    aggregated[format_name]["results"].append(format_data["result"])

        # Calculate average metrics
        for format_name, format_data in aggregated.items():
            avg_metrics = {}
            for metric, values in format_data["metrics"].items():
                if values:  # Only calculate if we have values
                    avg_metrics[metric] = statistics.mean(values)

            format_data["metrics"] = avg_metrics

        return aggregated

    def generate_comparison_report(
        self,
        results: Dict[str, Dict[str, Any]],
        format: str = "text"
    ) -> str:
        """
        Generate a comparison report of evaluation results.

        Args:
            results: Evaluation results
            format: Output format (text, markdown, json)

        Returns:
            Formatted comparison report
        """
        if format == "json":
            return json.dumps(results, indent=2)

        # Extract metrics for each format
        format_metrics = {}
        for format_name, format_data in results.items():
            format_metrics[format_name] = format_data["metrics"]

        # Generate report
        if format == "markdown":
            return self._generate_markdown_report(format_metrics)
        else:  # Default to text
            return self._generate_text_report(format_metrics)

    def _generate_text_report(self, format_metrics: Dict[str, Dict[str, float]]) -> str:
        """Generate a text report."""
        lines = ["Reasoning Format Comparison Report", "=" * 30, ""]

        # Add metrics table
        lines.append("Metrics by Format:")
        lines.append("-" * 20)

        for format_name, metrics in format_metrics.items():
            lines.append(f"\n{format_name}:")
            for metric, value in metrics.items():
                lines.append(f"  {metric}: {value:.4f}")

        # Add best format for each metric
        lines.append("\nBest Format by Metric:")
        lines.append("-" * 20)

        for metric in self.metrics:
            if all(metric in metrics for metrics in format_metrics.values()):
                best_format = self.recommend_best_format(
                    {"format": {"metrics": metrics} for format, metrics in format_metrics.items()},
                    priority_metric=metric
                )[0]
                lines.append(f"{metric}: {best_format}")

        return "\n".join(lines)

    def _generate_markdown_report(self, format_metrics: Dict[str, Dict[str, float]]) -> str:
        """Generate a markdown report."""
        lines = ["# Reasoning Format Comparison Report", ""]

        # Add metrics table
        lines.append("## Metrics by Format")
        lines.append("")

        # Create table header
        metrics = set()
        for metrics_dict in format_metrics.values():
            metrics.update(metrics_dict.keys())

        header = ["Format"]
        for metric in sorted(metrics):
            header.append(metric.capitalize())

        lines.append("| " + " | ".join(header) + " |")
        lines.append("| " + " | ".join(["---"] * len(header)) + " |")

        # Add rows for each format
        for format_name, metrics_dict in sorted(format_metrics.items()):
            row = [format_name]
            for metric in sorted(metrics):
                value = metrics_dict.get(metric, "N/A")
                if isinstance(value, (int, float)):
                    row.append(f"{value:.4f}")
                else:
                    row.append(str(value))
            lines.append("| " + " | ".join(row) + " |")

        # Add best format for each metric
        lines.append("")
        lines.append("## Best Format by Metric")
        lines.append("")

        lines.append("| Metric | Best Format |")
        lines.append("| --- | --- |")

        for metric in sorted(metrics):
            if all(metric in metrics_dict for metrics_dict in format_metrics.values()):
                # Find best format for this metric
                best_format = None
                best_value = None

                for format_name, metrics_dict in format_metrics.items():
                    value = metrics_dict[metric]

                    # For latency and memory, lower is better
                    if metric in ["latency", "memory", "tokens"]:
                        value = -value

                    if best_value is None or value > best_value:
                        best_format = format_name
                        best_value = value

                if best_format:
                    lines.append(f"| {metric.capitalize()} | {best_format} |")

        return "\n".join(lines)

    def recommend_best_format(
        self,
        results: Dict[str, Dict[str, Any]],
        priority_metric: str = "accuracy"
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Recommend the best reasoning format based on a priority metric.

        Args:
            results: Evaluation results
            priority_metric: Metric to prioritize

        Returns:
            Tuple of (best format name, format data)
        """
        best_format = None
        best_value = None
        best_data = None

        for format_name, format_data in results.items():
            if "metrics" not in format_data or priority_metric not in format_data["metrics"]:
                continue

            value = format_data["metrics"][priority_metric]

            # For latency and memory, lower is better
            if priority_metric in ["latency", "memory", "tokens"]:
                value = -value

            if best_value is None or value > best_value:
                best_format = format_name
                best_value = value
                best_data = format_data

        return best_format, best_data


class ReasoningFormatComparator:
    """
    Comparator for multi-criteria comparison of reasoning formats.

    This class provides methods for comparing different reasoning formats
    based on multiple criteria with customizable weights.
    """

    def __init__(
        self,
        criteria: List[str],
        weights: Dict[str, float] = None,
        verbose: bool = False
    ):
        """
        Initialize the ReasoningFormatComparator.

        Args:
            criteria: List of criteria to compare
            weights: Dictionary mapping criteria to weights (will be normalized)
            verbose: Whether to print verbose output
        """
        self.criteria = criteria
        self.verbose = verbose

        # Initialize weights
        if weights is None:
            # Equal weights if not provided
            weights = {criterion: 1.0 for criterion in criteria}

        # Normalize weights
        total_weight = sum(weights.values())
        self.weights = {k: v / total_weight for k, v in weights.items()}

    def compare(
        self,
        results: Dict[str, Dict[str, Any]],
        custom_weights: Dict[str, float] = None
    ) -> Dict[str, Any]:
        """
        Compare reasoning formats based on multiple criteria.

        Args:
            results: Evaluation results
            custom_weights: Custom weights to use for this comparison

        Returns:
            Comparison results
        """
        # Extract metrics for each format
        format_metrics = {}
        for format_name, format_data in results.items():
            if "metrics" in format_data:
                format_metrics[format_name] = format_data["metrics"]

        # Use custom weights if provided, otherwise use default weights
        weights = custom_weights or self.weights

        # Normalize weights if custom weights are provided
        if custom_weights:
            total_weight = sum(custom_weights.values())
            weights = {k: v / total_weight for k, v in custom_weights.items()}

        # Normalize metrics
        normalized_metrics = self._normalize_metrics(format_metrics)

        # Calculate scores
        scores = {}
        for format_name, metrics in normalized_metrics.items():
            # Calculate weighted score for each criterion
            criterion_scores = {}
            for criterion, value in metrics.items():
                if criterion in weights:
                    # For latency and memory, lower is better, so invert the normalized value
                    if criterion in ["latency", "memory", "tokens"]:
                        value = 1.0 - value
                    criterion_scores[criterion] = value

            # Calculate overall score
            overall_score = 0.0
            for criterion, score in criterion_scores.items():
                overall_score += score * weights.get(criterion, 0.0)

            scores[format_name] = {
                "overall_score": overall_score,
                "criterion_scores": criterion_scores
            }

        # Rank formats by overall score
        ranked_formats = sorted(
            scores.keys(),
            key=lambda x: scores[x]["overall_score"],
            reverse=True
        )

        # Determine best format
        best_format = ranked_formats[0] if ranked_formats else None

        return {
            "scores": scores,
            "ranked_formats": ranked_formats,
            "best_format": best_format,
            "weights_used": weights
        }

    def _normalize_metrics(
        self,
        format_metrics: Dict[str, Dict[str, float]]
    ) -> Dict[str, Dict[str, float]]:
        """
        Normalize metrics across formats to a 0-1 scale.

        Args:
            format_metrics: Dictionary mapping format names to metrics

        Returns:
            Dictionary mapping format names to normalized metrics
        """
        normalized = {format_name: {} for format_name in format_metrics}

        # Find min and max values for each metric
        metric_ranges = {}
        for metrics in format_metrics.values():
            for metric, value in metrics.items():
                if metric not in metric_ranges:
                    metric_ranges[metric] = {"min": float("inf"), "max": float("-inf")}

                metric_ranges[metric]["min"] = min(metric_ranges[metric]["min"], value)
                metric_ranges[metric]["max"] = max(metric_ranges[metric]["max"], value)

        # Normalize each metric
        for format_name, metrics in format_metrics.items():
            for metric, value in metrics.items():
                min_val = metric_ranges[metric]["min"]
                max_val = metric_ranges[metric]["max"]

                # Skip normalization if min and max are the same
                if min_val == max_val:
                    normalized[format_name][metric] = 0.5
                    continue

                # Normalize to 0-1 range
                normalized_value = (value - min_val) / (max_val - min_val)
                normalized[format_name][metric] = normalized_value

        return normalized

    def generate_comparison_report(
        self,
        comparison_results: Dict[str, Any],
        format: str = "text"
    ) -> str:
        """
        Generate a comparison report.

        Args:
            comparison_results: Results from the compare method
            format: Output format (text, markdown, json)

        Returns:
            Formatted comparison report
        """
        if format == "json":
            return json.dumps(comparison_results, indent=2)

        if format == "markdown":
            return self._generate_markdown_report(comparison_results)
        else:  # Default to text
            return self._generate_text_report(comparison_results)

    def _generate_text_report(self, comparison_results: Dict[str, Any]) -> str:
        """Generate a text report."""
        lines = ["Reasoning Format Comparison Report", "=" * 30, ""]

        # Add ranked formats
        lines.append("Ranked Formats:")
        lines.append("-" * 15)

        for i, format_name in enumerate(comparison_results["ranked_formats"]):
            score = comparison_results["scores"][format_name]["overall_score"]
            lines.append(f"{i+1}. {format_name} (Score: {score:.4f})")

        # Add best format
        lines.append("\nBest Format:")
        lines.append("-" * 11)
        lines.append(f"{comparison_results['best_format']}")

        # Add weights used
        lines.append("\nWeights Used:")
        lines.append("-" * 12)
        for criterion, weight in comparison_results["weights_used"].items():
            lines.append(f"{criterion}: {weight:.4f}")

        # Add detailed scores
        lines.append("\nDetailed Scores:")
        lines.append("-" * 15)

        for format_name in comparison_results["ranked_formats"]:
            format_scores = comparison_results["scores"][format_name]
            lines.append(f"\n{format_name}:")
            lines.append(f"  Overall Score: {format_scores['overall_score']:.4f}")
            lines.append("  Criterion Scores:")
            for criterion, score in format_scores["criterion_scores"].items():
                lines.append(f"    {criterion}: {score:.4f}")

        return "\n".join(lines)

    def _generate_markdown_report(self, comparison_results: Dict[str, Any]) -> str:
        """Generate a markdown report."""
        lines = ["# Reasoning Format Comparison Report", ""]

        # Add ranked formats
        lines.append("## Ranked Formats")
        lines.append("")
        lines.append("| Rank | Format | Overall Score |")
        lines.append("| --- | --- | --- |")

        for i, format_name in enumerate(comparison_results["ranked_formats"]):
            score = comparison_results["scores"][format_name]["overall_score"]
            lines.append(f"| {i+1} | {format_name} | {score:.4f} |")

        # Add best format
        lines.append("")
        lines.append("## Best Format")
        lines.append("")
        lines.append(f"**{comparison_results['best_format']}**")

        # Add weights used
        lines.append("")
        lines.append("## Weights Used")
        lines.append("")
        lines.append("| Criterion | Weight |")
        lines.append("| --- | --- |")
        for criterion, weight in comparison_results["weights_used"].items():
            lines.append(f"| {criterion} | {weight:.4f} |")

        # Add detailed scores
        lines.append("")
        lines.append("## Detailed Scores")
        lines.append("")

        for format_name in comparison_results["ranked_formats"]:
            format_scores = comparison_results["scores"][format_name]
            lines.append(f"### {format_name}")
            lines.append("")
            lines.append(f"Overall Score: **{format_scores['overall_score']:.4f}**")
            lines.append("")
            lines.append("| Criterion | Score |")
            lines.append("| --- | --- |")
            for criterion, score in format_scores["criterion_scores"].items():
                lines.append(f"| {criterion} | {score:.4f} |")
            lines.append("")

        return "\n".join(lines)
