"""
ReAct (Reasoning + Action) format implementation.

This module implements the ReAct reasoning format, which combines reasoning with actions
to solve complex tasks through iterative reasoning and interaction with tools.
"""

import re
import time
import json
from typing import Dict, Any, List, Optional, Union, Callable, Tuple
import traceback

from ..base import BaseReasoner
from ...utils.structured_logging import get_logger
from ...models.api.openai import openai_provider
from ...models.api.anthropic import anthropic_provider
from ...models.api.openrouter import openrouter_provider
from ...tools.base import BaseTool, ToolRegistry

# Create a logger
logger = get_logger(__name__)

class ReActReasoner(BaseReasoner):
    """
    Implements the ReAct (Reasoning + Action) reasoning format.

    ReAct combines reasoning with actions to solve complex tasks through
    iterative reasoning and interaction with tools.
    """

    def __init__(
        self,
        provider: str = "openai",
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        language: str = "en",
        max_iterations: int = 10,
        tools: Optional[List[BaseTool]] = None,
        verbose: bool = False,
        **kwargs
    ):
        """
        Initialize the ReActReasoner.

        Args:
            provider: The provider to use ("openai", "anthropic", "openrouter")
            model: The model to use (if None, will use provider's default)
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            language: Language for the reasoning (en, vi, etc.)
            max_iterations: Maximum number of iterations for reasoning
            tools: List of tools available for the reasoner to use
            verbose: Whether to print verbose output during reasoning
            **kwargs: Additional implementation-specific arguments
        """
        super().__init__(
            provider=provider,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            **kwargs
        )
        self.language = language
        self.max_iterations = max_iterations
        self.tools = tools or []
        self.verbose = verbose

        # Set up the provider
        if provider == "openai":
            self.api_provider = openai_provider
            self.model = model or "gpt-4o"
        elif provider == "anthropic":
            self.api_provider = anthropic_provider
            self.model = model or "claude-3-sonnet"
        elif provider == "openrouter":
            self.api_provider = openrouter_provider
            self.model = model or "anthropic/claude-3-opus"
        else:
            raise ValueError(f"Unsupported provider: {provider}")

        # Create tool registry
        self.tool_registry = {}
        for tool in self.tools:
            self.tool_registry[tool.name] = tool

    def _format_tool_descriptions(self) -> str:
        """
        Format tool descriptions for the system prompt.

        Returns:
            Formatted tool descriptions
        """
        if not self.tools:
            return "No tools available."

        descriptions = []
        for tool in self.tools:
            schema = tool.get_schema()
            props = schema.get("properties", {})
            required = schema.get("required", [])

            # Format parameters
            parameters = []
            for name, prop in props.items():
                param_type = prop.get("type", "string")
                param_desc = prop.get("description", "")
                is_required = name in required
                parameters.append(f"- {name} ({param_type}){' [REQUIRED]' if is_required else ''}: {param_desc}")

            # Add tool description
            tool_desc = [
                f"## {tool.name}",
                f"{tool.description}",
                "Parameters:",
            ]
            tool_desc.extend(parameters)
            descriptions.append("\n".join(tool_desc))

        return "\n\n".join(descriptions)

    def _create_system_prompt(self) -> str:
        """
        Create the system prompt for the ReAct reasoner.

        Returns:
            System prompt
        """
        system_prompt = """You are an assistant that answers questions by carefully going through a reasoning process, thinking step-by-step, and using tools when helpful.

When you have a question that might be better answered using tools:
1. Think about what information you need
2. Use the appropriate tool to find that information
3. Analyze the results and decide if you need to use more tools
4. Once you have all the information you need, answer the question directly

You must follow this format:

Thought: Your reasoning about what to do next and why
Action: tool_name
Action Input: {{"param1": "value1", "param2": "value2"}}
Observation: [Tool output will appear here]
... (repeat Thought/Action/Observation as needed)
Thought: I now know the answer
Answer: [Your final answer to the user's question]

Available Tools:
{tool_descriptions}

Begin!
"""
        return system_prompt.format(tool_descriptions=self._format_tool_descriptions())

    def _parse_action(self, text: str) -> Tuple[Optional[str], Optional[Dict[str, Any]]]:
        """
        Parse an action from the model's response.

        Args:
            text: The model's response text

        Returns:
            Tuple of (action_name, action_args) or (None, None) if no action found
        """
        # Look for action pattern
        action_match = re.search(r"Action: (\w+)", text)
        if not action_match:
            return None, None

        action_name = action_match.group(1)

        # Look for action input pattern
        input_match = re.search(r"Action Input: ({.*?})", text, re.DOTALL)
        if not input_match:
            return action_name, {}

        # Parse the JSON input
        try:
            action_args = json.loads(input_match.group(1))
        except json.JSONDecodeError:
            logger.error(f"Failed to parse action input: {input_match.group(1)}")
            action_args = {}

        return action_name, action_args

    def _execute_action(self, action_name: str, action_args: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute an action using the appropriate tool.

        Args:
            action_name: Name of the action/tool to execute
            action_args: Arguments for the action

        Returns:
            Dictionary containing the action result
        """
        # Check if the tool exists
        if action_name not in self.tool_registry:
            return {
                "success": False,
                "error": f"Tool '{action_name}' not found. Available tools: {', '.join(self.tool_registry.keys())}"
            }

        # Get the tool and execute it
        tool = self.tool_registry[action_name]
        try:
            result = tool.run(**action_args)
            return {
                "success": True,
                "result": result
            }
        except Exception as e:
            logger.error(f"Error executing tool '{action_name}': {str(e)}")
            logger.error(traceback.format_exc())
            return {
                "success": False,
                "error": f"Error executing tool '{action_name}': {str(e)}"
            }

    def _format_conversation(self, history: List[Dict[str, Any]]) -> str:
        """
        Format the conversation history for the model.

        Args:
            history: List of conversation history items

        Returns:
            Formatted conversation as a string
        """
        formatted = []
        for item in history:
            if item["role"] == "observation":
                formatted.append(f"Observation: {item['content']}")
            elif item["role"] == "user":
                formatted.append(f"Question: {item['content']}")
            elif item["role"] == "assistant":
                formatted.append(item["content"])
        return "\n".join(formatted)

    def reason(
        self,
        query: str,
        context: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        max_retries: int = 2,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Perform ReAct reasoning on the given query.

        Args:
            query: The query to reason about
            context: Optional context to include
            callback: Optional callback function for streaming responses
            max_retries: Maximum number of retries for failed API calls
            **kwargs: Additional implementation-specific arguments

        Returns:
            Dictionary containing the reasoning result and additional information
        """
        # Start timing
        start_time = time.time()

        # Initialize result
        result = {
            "query": query,
            "answer": None,
            "actions": [],
            "iterations": 0,
            "success": False,
            "error": None,
            "latency": 0,
            "conversation": []
        }

        # Create system prompt
        system_prompt = kwargs.get("custom_system_prompt", self._create_system_prompt())

        # Format query with context if provided
        user_query = query
        if context:
            user_query = f"{query}\n\nContext: {context}"

        # Initialize conversation history
        conversation = [{"role": "user", "content": user_query}]
        result["conversation"].append({"role": "user", "content": user_query})

        # ReAct loop
        for iteration in range(self.max_iterations):
            # Update iterations count
            result["iterations"] = iteration + 1

            # Format the conversation for the model
            formatted_conversation = self._format_conversation(conversation)

            # Generate assistant response
            retry_count = 0
            while retry_count <= max_retries:
                try:
                    model_response = self.api_provider.generate(
                        model=self.model,
                        prompt=formatted_conversation,
                        system_prompt=system_prompt,
                        temperature=self.temperature,
                        max_tokens=self.max_tokens
                    )
                    break
                except Exception as e:
                    retry_count += 1
                    if retry_count > max_retries:
                        error_msg = f"Failed to generate response after {max_retries} retries: {str(e)}"
                        logger.error(error_msg)
                        result["error"] = error_msg
                        result["success"] = False
                        result["latency"] = time.time() - start_time
                        return result
                    logger.warning(f"Error generating response (retry {retry_count}/{max_retries}): {str(e)}")
                    time.sleep(1)  # Wait before retrying

            # Add assistant response to conversation
            conversation.append({"role": "assistant", "content": model_response})
            result["conversation"].append({"role": "assistant", "content": model_response})

            # Call callback if provided
            if callback:
                callback(model_response)

            # Check if the response contains an action
            action_name, action_args = self._parse_action(model_response)

            # If no action is found, check if we have an answer
            if not action_name:
                answer_match = re.search(r"Answer: (.*?)($|Thought:)", model_response, re.DOTALL)
                if answer_match:
                    result["answer"] = answer_match.group(1).strip()
                    result["success"] = True
                    break
                continue

            # Execute the action
            action_result = self._execute_action(action_name, action_args)

            # Record the action
            action_record = {
                "name": action_name,
                "args": action_args,
                "result": action_result
            }
            result["actions"].append(action_record)

            # Add observation to conversation
            observation_content = json.dumps(action_result.get("result", {})) if action_result.get("success", False) else action_result.get("error", "Unknown error")
            conversation.append({"role": "observation", "content": observation_content})
            result["conversation"].append({"role": "observation", "content": observation_content})

            # Check if we've reached an answer in the last response
            answer_match = re.search(r"Answer: (.*?)($|Thought:)", model_response, re.DOTALL)
            if answer_match:
                result["answer"] = answer_match.group(1).strip()
                result["success"] = True
                break

        # If we've exhausted iterations without an answer, extract a best effort answer
        if not result["answer"]:
            # Look for the most recent "Thought: I now know the answer" or similar pattern
            for item in reversed(result["conversation"]):
                if item["role"] == "assistant":
                    answer_match = re.search(r"Answer: (.*?)($|Thought:)", item["content"], re.DOTALL)
                    if answer_match:
                        result["answer"] = answer_match.group(1).strip()
                        result["success"] = True
                        break

                    # If no Answer pattern, try to extract a conclusion
                    final_thought_match = re.search(r"Thought: I now know(.*?)$", item["content"], re.DOTALL)
                    if final_thought_match:
                        result["answer"] = final_thought_match.group(1).strip()
                        result["success"] = True
                        break

            # If still no answer, just use the most recent response
            if not result["answer"]:
                for item in reversed(result["conversation"]):
                    if item["role"] == "assistant":
                        result["answer"] = "Based on the reasoning process, I couldn't reach a definitive answer."
                        result["success"] = False
                        break

        # Calculate latency
        result["latency"] = time.time() - start_time

        return result
