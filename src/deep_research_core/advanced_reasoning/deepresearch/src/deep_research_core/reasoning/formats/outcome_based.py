"""
Outcome-Based Reasoning format implementation.

This module implements the Outcome-Based reasoning format, which focuses on
generating and evaluating potential outcomes to optimize decision-making.
"""

import json
import time
import re
from typing import Dict, Any, List, Optional, Callable, Tuple

from ..base import BaseReasoner
from ...models.api.openai import openai_provider
from ...models.api.anthropic import anthropic_provider
from ...models.api.openrouter import openrouter_provider
from ...utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

class OutcomeBasedReasoner(BaseReasoner):
    """
    Implements the Outcome-Based reasoning format.
    
    This reasoner focuses on generating multiple potential outcomes,
    evaluating them based on likelihood and confidence, and selecting
    the best outcome to generate a final answer.
    """
    
    def __init__(
        self,
        provider: str = "openai",
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        language: str = "en",
        num_outcomes: int = 3,
        outcome_evaluation_depth: int = 2,
        use_confidence_scoring: bool = True,
        use_outcome_optimization: bool = True,
        use_outcome_validation: bool = True,
        verbose: bool = False,
        **kwargs
    ):
        """
        Initialize the OutcomeBasedReasoner.
        
        Args:
            provider: The provider to use ("openai", "anthropic", "openrouter")
            model: The model to use (if None, will use provider's default)
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            language: Language to use (en, vi, etc.)
            num_outcomes: Number of potential outcomes to generate
            outcome_evaluation_depth: Depth of outcome evaluation
            use_confidence_scoring: Whether to use confidence scoring
            use_outcome_optimization: Whether to optimize outcomes
            use_outcome_validation: Whether to validate outcomes
            verbose: Whether to print verbose output
            **kwargs: Additional implementation-specific arguments
        """
        super().__init__(
            provider=provider,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            **kwargs
        )
        
        self.language = language
        self.num_outcomes = num_outcomes
        self.outcome_evaluation_depth = outcome_evaluation_depth
        self.use_confidence_scoring = use_confidence_scoring
        self.use_outcome_optimization = use_outcome_optimization
        self.use_outcome_validation = use_outcome_validation
        self.verbose = verbose
        
        # Set up the provider
        if provider == "openai":
            self.api_provider = openai_provider
            self.model = model or "gpt-4o"
        elif provider == "anthropic":
            self.api_provider = anthropic_provider
            self.model = model or "claude-3-sonnet"
        elif provider == "openrouter":
            self.api_provider = openrouter_provider
            self.model = model or "anthropic/claude-3-opus"
        else:
            raise ValueError(f"Unsupported provider: {provider}")
    
    def reason(
        self,
        query: str,
        context: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Perform Outcome-Based reasoning on the given query.
        
        Args:
            query: The query to reason about
            context: Optional context to include
            callback: Optional callback function for streaming responses
            **kwargs: Additional implementation-specific arguments
            
        Returns:
            Dictionary containing the reasoning result and additional information
        """
        start_time = time.time()
        
        # Generate potential outcomes
        outcomes = self._generate_outcomes(query, context, **kwargs)
        
        # Evaluate outcomes
        evaluated_outcomes = self._evaluate_outcomes(query, outcomes, context, **kwargs)
        
        # Select best outcome
        best_outcome = self._select_best_outcome(evaluated_outcomes, **kwargs)
        
        # Generate answer from best outcome
        answer = self._generate_answer_from_outcome(query, best_outcome, context, **kwargs)
        
        end_time = time.time()
        
        return {
            "query": query,
            "answer": answer,
            "outcomes": evaluated_outcomes,
            "best_outcome": best_outcome,
            "reasoning_time": end_time - start_time,
            "provider": self.provider,
            "model": self.model
        }
    
    def _generate_outcomes(
        self,
        query: str,
        context: Optional[str] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Generate potential outcomes for the given query.
        
        Args:
            query: The query to reason about
            context: Optional context to include
            **kwargs: Additional implementation-specific arguments
            
        Returns:
            List of potential outcomes, each as a dictionary
        """
        try:
            # Create the prompt for generating outcomes
            if self.language == "vi":
                system_prompt = f"""Bạn là một trợ lý AI chuyên về phân tích và dự đoán kết quả. 
                Hãy tạo ra {self.num_outcomes} kết quả tiềm năng khác nhau cho câu hỏi được đưa ra.
                Mỗi kết quả nên bao gồm:
                - Mô tả: Mô tả chi tiết về kết quả
                - Khả năng xảy ra: Đánh giá phần trăm khả năng xảy ra (0-100)
                - Bằng chứng: Các bằng chứng hoặc lý do ủng hộ kết quả này
                - Lợi ích: Các lợi ích của kết quả này
                - Hạn chế: Các hạn chế hoặc rủi ro của kết quả này
                - Các bước: Các bước cần thực hiện để đạt được kết quả này
                
                Trả lời dưới dạng mảng JSON với mỗi kết quả là một đối tượng có các trường trên."""
                
                user_prompt = f"Câu hỏi: {query}"
                if context:
                    user_prompt += f"\n\nBối cảnh: {context}"
            else:
                system_prompt = f"""You are an AI assistant specialized in outcome analysis and prediction.
                Generate {self.num_outcomes} different potential outcomes for the given query.
                Each outcome should include:
                - description: Detailed description of the outcome
                - likelihood: Percentage likelihood of this outcome (0-100)
                - evidence: Evidence or reasons supporting this outcome
                - benefits: Benefits of this outcome
                - drawbacks: Drawbacks or risks of this outcome
                - steps: Steps needed to achieve this outcome
                
                Respond with a JSON array where each outcome is an object with the fields above."""
                
                user_prompt = f"Query: {query}"
                if context:
                    user_prompt += f"\n\nContext: {context}"
            
            # Generate outcomes using the API provider
            response = self.api_provider.generate(
                prompt=user_prompt,
                system_prompt=system_prompt,
                model=self.model,
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            
            # Parse the response to extract outcomes
            outcomes = self._parse_outcomes_response(response)
            
            # If no outcomes were generated, create a default one
            if not outcomes:
                outcomes = [{
                    "description": "Default outcome due to parsing error",
                    "likelihood": 50,
                    "evidence": "No evidence available",
                    "benefits": ["Unknown"],
                    "drawbacks": ["Unknown"],
                    "steps": ["Unknown"]
                }]
            
            return outcomes
            
        except Exception as e:
            logger.error(f"Error generating outcomes: {str(e)}")
            # Return a default outcome in case of error
            return [{
                "description": "Default outcome due to error",
                "likelihood": 50,
                "evidence": f"Error occurred: {str(e)}",
                "benefits": ["Unknown"],
                "drawbacks": ["Unknown"],
                "steps": ["Unknown"]
            }]
    
    def _parse_outcomes_response(self, response: str) -> List[Dict[str, Any]]:
        """
        Parse the API response to extract outcomes.
        
        Args:
            response: The API response string
            
        Returns:
            List of outcomes extracted from the response
        """
        try:
            # Try to find a JSON array in the response
            json_match = re.search(r'\[\s*{.*}\s*\]', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                outcomes = json.loads(json_str)
                return outcomes
            
            # If no JSON array was found, try to parse the entire response as JSON
            outcomes = json.loads(response)
            if isinstance(outcomes, list):
                return outcomes
            
            # If the response is a JSON object, wrap it in a list
            if isinstance(outcomes, dict):
                return [outcomes]
            
            # If all else fails, create a default outcome
            return [{
                "description": "Default outcome due to parsing error",
                "likelihood": 50,
                "evidence": "No evidence available",
                "benefits": ["Unknown"],
                "drawbacks": ["Unknown"],
                "steps": ["Unknown"]
            }]
            
        except json.JSONDecodeError:
            logger.warning(f"Failed to parse outcomes response as JSON: {response}")
            # Try to extract structured information from the text
            outcomes = []
            
            # Look for sections that might be outcomes
            outcome_sections = re.split(r'\n\s*Outcome\s+\d+\s*:\s*|\n\s*\d+\.\s+', response)
            if len(outcome_sections) > 1:  # First section is usually intro text
                for section in outcome_sections[1:]:  # Skip the intro
                    if not section.strip():
                        continue
                    
                    # Extract description
                    description_match = re.search(r'(?:Description\s*:|^)\s*(.+?)(?:\n|$)', section, re.IGNORECASE)
                    description = description_match.group(1).strip() if description_match else "Unknown outcome"
                    
                    # Extract likelihood
                    likelihood_match = re.search(r'(?:Likelihood\s*:|Probability\s*:)\s*(\d+)\s*%?', section, re.IGNORECASE)
                    likelihood = int(likelihood_match.group(1)) if likelihood_match else 50
                    
                    # Extract evidence
                    evidence_match = re.search(r'(?:Evidence\s*:|Reasons\s*:)\s*(.+?)(?:\n\s*(?:Benefits|Drawbacks|Steps)\s*:|$)', section, re.DOTALL | re.IGNORECASE)
                    evidence = evidence_match.group(1).strip() if evidence_match else "No evidence provided"
                    
                    # Extract benefits
                    benefits_match = re.search(r'(?:Benefits\s*:|Pros\s*:)\s*(.+?)(?:\n\s*(?:Drawbacks|Steps)\s*:|$)', section, re.DOTALL | re.IGNORECASE)
                    benefits_text = benefits_match.group(1).strip() if benefits_match else ""
                    benefits = [b.strip() for b in re.split(r'\n\s*-\s*|\n\s*\d+\.\s*', benefits_text) if b.strip()]
                    if not benefits:
                        benefits = ["No benefits specified"]
                    
                    # Extract drawbacks
                    drawbacks_match = re.search(r'(?:Drawbacks\s*:|Cons\s*:|Risks\s*:)\s*(.+?)(?:\n\s*(?:Steps|Benefits)\s*:|$)', section, re.DOTALL | re.IGNORECASE)
                    drawbacks_text = drawbacks_match.group(1).strip() if drawbacks_match else ""
                    drawbacks = [d.strip() for d in re.split(r'\n\s*-\s*|\n\s*\d+\.\s*', drawbacks_text) if d.strip()]
                    if not drawbacks:
                        drawbacks = ["No drawbacks specified"]
                    
                    # Extract steps
                    steps_match = re.search(r'(?:Steps\s*:|Process\s*:|Implementation\s*:)\s*(.+?)(?:\n\s*(?:Outcome|\d+\.)\s*:|$)', section, re.DOTALL | re.IGNORECASE)
                    steps_text = steps_match.group(1).strip() if steps_match else ""
                    steps = [s.strip() for s in re.split(r'\n\s*-\s*|\n\s*\d+\.\s*', steps_text) if s.strip()]
                    if not steps:
                        steps = ["No steps specified"]
                    
                    outcomes.append({
                        "description": description,
                        "likelihood": likelihood,
                        "evidence": evidence,
                        "benefits": benefits,
                        "drawbacks": drawbacks,
                        "steps": steps
                    })
            
            # If we couldn't extract any outcomes, create a default one
            if not outcomes:
                outcomes = [{
                    "description": "Default outcome extracted from text",
                    "likelihood": 50,
                    "evidence": "Extracted from non-JSON response",
                    "benefits": ["Unknown"],
                    "drawbacks": ["Unknown"],
                    "steps": ["Unknown"]
                }]
            
            return outcomes
        except Exception as e:
            logger.error(f"Error parsing outcomes response: {str(e)}")
            return [{
                "description": "Default outcome due to parsing error",
                "likelihood": 50,
                "evidence": "Error parsing response",
                "benefits": ["Unknown"],
                "drawbacks": ["Unknown"],
                "steps": ["Unknown"]
            }]
    
    def _evaluate_outcomes(
        self,
        query: str,
        outcomes: List[Dict[str, Any]],
        context: Optional[str] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Evaluate the generated outcomes.
        
        Args:
            query: The query to reason about
            outcomes: List of outcomes to evaluate
            context: Optional context to include
            **kwargs: Additional implementation-specific arguments
            
        Returns:
            List of evaluated outcomes with confidence scores
        """
        if not self.use_confidence_scoring:
            # If confidence scoring is disabled, just return the outcomes with default confidence scores
            for outcome in outcomes:
                outcome["confidence_score"] = outcome.get("likelihood", 50)
            return outcomes
        
        try:
            # Create a copy of the outcomes to avoid modifying the originals
            evaluated_outcomes = [outcome.copy() for outcome in outcomes]
            
            # Create the prompt for evaluating outcomes
            if self.language == "vi":
                system_prompt = """Bạn là một trợ lý AI chuyên về đánh giá kết quả.
                Hãy đánh giá mức độ tin cậy của mỗi kết quả tiềm năng dựa trên các tiêu chí sau:
                1. Tính nhất quán nội bộ
                2. Tính hợp lý dựa trên bằng chứng
                3. Tính đầy đủ của phân tích
                4. Tính khả thi của các bước thực hiện
                5. Sự cân bằng giữa lợi ích và hạn chế
                
                Cho mỗi kết quả, hãy cung cấp:
                - Điểm tin cậy (0-100)
                - Lý do ngắn gọn cho điểm số
                - Các điểm mạnh của kết quả
                - Các điểm yếu của kết quả
                
                Trả lời dưới dạng mảng JSON với mỗi đánh giá là một đối tượng có các trường trên."""
                
                outcomes_str = json.dumps(outcomes, ensure_ascii=False, indent=2)
                user_prompt = f"Câu hỏi: {query}\n\nCác kết quả cần đánh giá:\n{outcomes_str}"
                if context:
                    user_prompt += f"\n\nBối cảnh: {context}"
            else:
                system_prompt = """You are an AI assistant specialized in outcome evaluation.
                Evaluate the confidence level of each potential outcome based on the following criteria:
                1. Internal consistency
                2. Reasonableness based on evidence
                3. Comprehensiveness of analysis
                4. Feasibility of implementation steps
                5. Balance of benefits and drawbacks
                
                For each outcome, provide:
                - confidence_score (0-100)
                - reasoning (brief justification for the score)
                - strengths (key strengths of the outcome)
                - weaknesses (key weaknesses of the outcome)
                
                Respond with a JSON array where each evaluation is an object with the fields above."""
                
                outcomes_str = json.dumps(outcomes, ensure_ascii=False, indent=2)
                user_prompt = f"Query: {query}\n\nOutcomes to evaluate:\n{outcomes_str}"
                if context:
                    user_prompt += f"\n\nContext: {context}"
            
            # Generate evaluations using the API provider
            response = self.api_provider.generate(
                prompt=user_prompt,
                system_prompt=system_prompt,
                model=self.model,
                temperature=max(0.1, self.temperature - 0.2),  # Lower temperature for evaluation
                max_tokens=self.max_tokens
            )
            
            # Parse the response to extract evaluations
            try:
                # Try to find a JSON array in the response
                json_match = re.search(r'\[\s*{.*}\s*\]', response, re.DOTALL)
                if json_match:
                    json_str = json_match.group(0)
                    evaluations = json.loads(json_str)
                else:
                    # If no JSON array was found, try to parse the entire response as JSON
                    evaluations = json.loads(response)
                
                # Ensure evaluations is a list
                if not isinstance(evaluations, list):
                    if isinstance(evaluations, dict):
                        evaluations = [evaluations]
                    else:
                        evaluations = []
                
                # Match evaluations with outcomes
                if len(evaluations) == len(evaluated_outcomes):
                    for i, evaluation in enumerate(evaluations):
                        # Add evaluation data to the corresponding outcome
                        evaluated_outcomes[i]["confidence_score"] = evaluation.get("confidence_score", evaluated_outcomes[i].get("likelihood", 50))
                        evaluated_outcomes[i]["evaluation_reasoning"] = evaluation.get("reasoning", "No reasoning provided")
                        evaluated_outcomes[i]["strengths"] = evaluation.get("strengths", ["No strengths identified"])
                        evaluated_outcomes[i]["weaknesses"] = evaluation.get("weaknesses", ["No weaknesses identified"])
                else:
                    # If the number of evaluations doesn't match the number of outcomes,
                    # use a simple approach to assign confidence scores
                    for i, outcome in enumerate(evaluated_outcomes):
                        outcome["confidence_score"] = outcome.get("likelihood", 50)
                        outcome["evaluation_reasoning"] = "Confidence score based on likelihood due to evaluation mismatch"
                        outcome["strengths"] = ["Not evaluated"]
                        outcome["weaknesses"] = ["Not evaluated"]
            except json.JSONDecodeError:
                # If JSON parsing fails, use a simple approach
                for i, outcome in enumerate(evaluated_outcomes):
                    outcome["confidence_score"] = outcome.get("likelihood", 50)
                    outcome["evaluation_reasoning"] = "Confidence score based on likelihood due to evaluation parsing error"
                    outcome["strengths"] = ["Not evaluated"]
                    outcome["weaknesses"] = ["Not evaluated"]
            
            return evaluated_outcomes
            
        except Exception as e:
            logger.error(f"Error evaluating outcomes: {str(e)}")
            # In case of error, return the original outcomes with default confidence scores
            for outcome in outcomes:
                outcome["confidence_score"] = outcome.get("likelihood", 50)
                outcome["evaluation_reasoning"] = f"Error during evaluation: {str(e)}"
                outcome["strengths"] = ["Not evaluated"]
                outcome["weaknesses"] = ["Not evaluated"]
            return outcomes
    
    def _select_best_outcome(
        self,
        evaluated_outcomes: List[Dict[str, Any]],
        **kwargs
    ) -> Dict[str, Any]:
        """
        Select the best outcome based on evaluation.
        
        Args:
            evaluated_outcomes: List of evaluated outcomes
            **kwargs: Additional implementation-specific arguments
            
        Returns:
            The best outcome
        """
        if not evaluated_outcomes:
            # If no outcomes are provided, return a default outcome
            return {
                "description": "No outcomes available",
                "likelihood": 0,
                "confidence_score": 0,
                "evidence": "No outcomes were provided for selection",
                "benefits": ["None"],
                "drawbacks": ["None"],
                "steps": ["None"]
            }
        
        # Sort outcomes by confidence score (or likelihood if confidence score is not available)
        sorted_outcomes = sorted(
            evaluated_outcomes,
            key=lambda x: x.get("confidence_score", x.get("likelihood", 0)),
            reverse=True
        )
        
        # Return the outcome with the highest score
        return sorted_outcomes[0]
    
    def _generate_answer_from_outcome(
        self,
        query: str,
        outcome: Dict[str, Any],
        context: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        Generate a final answer based on the best outcome.
        
        Args:
            query: The query to reason about
            outcome: The best outcome
            context: Optional context to include
            **kwargs: Additional implementation-specific arguments
            
        Returns:
            Generated answer
        """
        try:
            # Create the prompt for generating the answer
            if self.language == "vi":
                system_prompt = """Bạn là một trợ lý AI chuyên về đưa ra câu trả lời dựa trên phân tích kết quả.
                Hãy tạo ra một câu trả lời chi tiết và toàn diện dựa trên kết quả tốt nhất đã được chọn.
                Câu trả lời nên:
                1. Trực tiếp giải quyết câu hỏi ban đầu
                2. Giải thích tại sao kết quả này là tốt nhất
                3. Đưa ra các bước thực hiện cụ thể (nếu phù hợp)
                4. Cân nhắc cả lợi ích và hạn chế
                5. Cung cấp bất kỳ cảnh báo hoặc điều kiện nào (nếu cần)
                
                Câu trả lời nên rõ ràng, súc tích và hữu ích."""
                
                outcome_str = json.dumps(outcome, ensure_ascii=False, indent=2)
                user_prompt = f"Câu hỏi: {query}\n\nKết quả tốt nhất:\n{outcome_str}"
                if context:
                    user_prompt += f"\n\nBối cảnh: {context}"
            else:
                system_prompt = """You are an AI assistant specialized in providing answers based on outcome analysis.
                Create a detailed and comprehensive answer based on the selected best outcome.
                Your answer should:
                1. Directly address the original query
                2. Explain why this outcome is the best
                3. Provide specific implementation steps (if applicable)
                4. Consider both benefits and drawbacks
                5. Provide any necessary caveats or conditions
                
                The answer should be clear, concise, and helpful."""
                
                outcome_str = json.dumps(outcome, ensure_ascii=False, indent=2)
                user_prompt = f"Query: {query}\n\nBest outcome:\n{outcome_str}"
                if context:
                    user_prompt += f"\n\nContext: {context}"
            
            # Generate the answer using the API provider
            response = self.api_provider.generate(
                prompt=user_prompt,
                system_prompt=system_prompt,
                model=self.model,
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            
            return response.strip()
            
        except Exception as e:
            logger.error(f"Error generating answer from outcome: {str(e)}")
            # In case of error, return a default answer
            description = outcome.get("description", "Unknown outcome")
            steps = outcome.get("steps", ["No steps available"])
            steps_text = "\n".join([f"- {step}" for step in steps]) if isinstance(steps, list) else steps

            if self.language == "vi":
                return f"Dựa trên phân tích, kết quả tốt nhất là: {description}\n\nCác bước thực hiện:\n{steps_text}\n\n(Lưu ý: Đã xảy ra lỗi khi tạo câu trả lời chi tiết: {str(e)})"
            else:
                return f"Based on the analysis, the best outcome is: {description}\n\nImplementation steps:\n{steps_text}\n\n(Note: An error occurred while generating the detailed answer: {str(e)})"
