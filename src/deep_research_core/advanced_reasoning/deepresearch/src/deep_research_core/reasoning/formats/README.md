# Reasoning Formats

This directory contains various reasoning formats for the Deep Research Core system, providing different approaches to solving complex tasks.

## Available Formats

- [ReAct](#react-reasoning--action) - Reasoning + Action format for interactive problem-solving
- [OutcomeBased](#outcome-based-reasoning) - Reasoning based on predicted outcomes

## ReAct (Reasoning + Action)

ReAct is a reasoning format that combines chain-of-thought reasoning with the ability to interact with tools. This allows the model to break down complex problems, gather information using tools, and arrive at more accurate solutions.

### Key Features

- Step-by-step reasoning process
- Integration with tools for information gathering and processing
- Iterative approach to problem-solving
- Support for multimodal inputs (text, structured data)

### Usage Example

```python
from deep_research_core.reasoning.formats.react import ReActReasoner
from deep_research_core.tools.calculator import CalculatorTool
from deep_research_core.tools.search import SearchTool

# Initialize tools
calculator = CalculatorTool()
search = SearchTool()

# Create the reasoner
reasoner = ReActReasoner(
    provider="openai",  # Can be "openai", "anthropic", or "openrouter"
    model="gpt-4o",  # Model to use (provider-specific)
    temperature=0.7,  # Control randomness in responses
    max_tokens=2000,  # Maximum response length
    language="en",  # Language for reasoning (en, vi, etc.)
    max_iterations=10,  # Maximum number of reasoning steps
    tools=[calculator, search],  # Tools available to the reasoner
    verbose=True  # Enable verbose output
)

# Use the reasoner to solve a problem
result = reasoner.reason(
    query="What is the population of Vietnam's capital city multiplied by 2?",
    context="Please use tools to find accurate information before answering."
)

# Print the answer
print(f"Answer: {result['answer']}")

# View the tools used
for action in result["actions"]:
    print(f"Used {action['name']} with args: {action['args']}")
    print(f"Result: {action['result'].get('result', None)}")

# View conversation history
for item in result["conversation"]:
    print(f"{item['role']}: {item['content'][:50]}...")
```

### Parameters

- **provider** (str): The provider to use ("openai", "anthropic", "openrouter")
- **model** (str, optional): The model to use (provider-specific)
- **temperature** (float): Controls randomness (0.0-1.0)
- **max_tokens** (int): Maximum number of tokens in the response
- **language** (str): Language for reasoning (en, vi, etc.)
- **max_iterations** (int): Maximum number of reasoning iterations
- **tools** (List[BaseTool]): List of tools available to the reasoner
- **verbose** (bool): Whether to print verbose output

### Creating Custom Tools

You can create custom tools by extending the `BaseTool` class:

```python
from deep_research_core.tools.base import BaseTool, register_tool

@register_tool
class MyCustomTool(BaseTool):
    """
    A custom tool for specific functionality.
    """
    
    name = "my_custom_tool"
    description = "Performs a custom operation."
    
    def run(self, param1: str, param2: int = 0) -> dict:
        """
        Run the custom tool.
        
        Args:
            param1: First parameter
            param2: Second parameter with default value
            
        Returns:
            Dictionary containing the result
        """
        result = f"Processed {param1} with value {param2}"
        return {
            "result": result,
            "additional_info": f"Completed at step {param2}",
            "success": True
        }
```

## Outcome-Based Reasoning

*Documentation coming soon*

## Comparing Reasoning Formats

For evaluating and comparing different reasoning formats, you can use the `ReasoningFormatEvaluator` and `ReasoningFormatComparator` classes:

```python
from deep_research_core.reasoning.formats.evaluator import ReasoningFormatEvaluator, ReasoningFormatComparator
from deep_research_core.reasoning.formats.react import ReActReasoner
from deep_research_core.reasoning.formats.outcome_based import OutcomeBasedReasoner

# Initialize reasoners
react = ReActReasoner(provider="openai", model="gpt-4o")
outcome = OutcomeBasedReasoner(provider="openai", model="gpt-4o")

# Compare reasoners on a set of queries
comparator = ReasoningFormatComparator(
    reasoners=[react, outcome],
    evaluation_metrics=["accuracy", "reasoning_steps", "execution_time"]
)

results = comparator.compare(
    queries=[
        "What is the capital of France?",
        "Calculate the compound interest on $1000 at 5% for 3 years.",
        "Explain the difference between RNA and DNA."
    ]
)

# View comparison results
print(results.summary())
print(results.get_best_reasoner())
``` 