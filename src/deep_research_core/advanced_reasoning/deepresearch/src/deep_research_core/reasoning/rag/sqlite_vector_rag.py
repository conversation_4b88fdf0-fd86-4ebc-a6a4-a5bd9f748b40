"""
SQLite Vector RAG implementation.

This module provides a RAG implementation using SQLite for vector storage.
"""

import os
import json
import sqlite3
import uuid
from typing import Dict, List, Any, Optional, Callable, Union, Tuple

import numpy as np

from ...utils.structured_logging import get_logger
from ...utils.performance_metrics import measure_latency, measure_block_latency
from ...utils.distributed_tracing import trace_function, span
from ..base_rag import BaseRAG

# Create a logger
logger = get_logger(__name__)


class SQLiteVectorRAG(BaseRAG):
    """
    RAG implementation using SQLite for vector storage.

    This class provides a RAG implementation using SQLite for storing and retrieving
    document vectors. It supports:
    - Adding and removing documents
    - Searching for similar documents
    - Processing queries with RAG
    """

    def __init__(
        self,
        db_path: str,
        provider: str = "openai",
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        embedding_model: str = "text-embedding-ada-002",
        top_k: int = 5,
        create_tables: bool = True
    ):
        """
        Initialize the SQLiteVectorRAG.

        Args:
            db_path: Path to the SQLite database
            provider: The provider to use ("openai", "anthropic", "openrouter")
            model: The model to use (if None, will use provider's default)
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            embedding_model: Model to use for embeddings
            top_k: Number of documents to retrieve
            create_tables: Whether to create tables if they don't exist
        """
        super().__init__(
            provider=provider,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            embedding_model=embedding_model,
            top_k=top_k
        )

        self.db_path = db_path

        # Initialize the API provider
        if provider == "openai":
            from ...models.api.openai import openai_provider
            self.api_provider = openai_provider
        elif provider == "anthropic":
            from ...models.api.anthropic import anthropic_provider
            self.api_provider = anthropic_provider
        elif provider == "openrouter":
            from ...models.api.openrouter import openrouter_provider
            self.api_provider = openrouter_provider
        else:
            raise ValueError(f"Unsupported provider: {provider}")

        # Initialize the database
        self._initialize_db(create_tables)

        logger.info(f"Initialized SQLiteVectorRAG with database at {db_path}")

    def _initialize_db(self, create_tables: bool = True) -> None:
        """
        Initialize the SQLite database.

        Args:
            create_tables: Whether to create tables if they don't exist
        """
        # Create the directory if it doesn't exist (only for file-based databases)
        if self.db_path != ':memory:':
            db_dir = os.path.dirname(self.db_path)
            if db_dir:  # Only create directory if path has a directory component
                os.makedirs(db_dir, exist_ok=True)

        # Connect to the database
        self.conn = sqlite3.connect(self.db_path)
        self.conn.row_factory = sqlite3.Row

        # Create tables if needed
        if create_tables:
            self._create_tables()

    def _create_tables(self) -> None:
        """Create the necessary tables in the database."""
        cursor = self.conn.cursor()

        # Create documents table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS documents (
            id TEXT PRIMARY KEY,
            content TEXT NOT NULL,
            source TEXT,
            title TEXT,
            metadata TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)

        # Create embeddings table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS embeddings (
            id TEXT PRIMARY KEY,
            document_id TEXT NOT NULL,
            embedding BLOB NOT NULL,
            dimension INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE
        )
        """)

        # Create index on document_id
        cursor.execute("""
        CREATE INDEX IF NOT EXISTS idx_embeddings_document_id ON embeddings(document_id)
        """)

        self.conn.commit()

    def add_documents(self, documents: List[Dict[str, Any]], **kwargs) -> List[str]:
        """
        Add documents to the vector store.

        Args:
            documents: List of document dictionaries with 'content', 'source', and optional metadata
            **kwargs: Additional implementation-specific arguments

        Returns:
            List of IDs of the inserted documents
        """
        with span("add_documents"):
            with measure_block_latency("add_documents"):
                # Generate embeddings for the documents
                texts = [doc.get("content", "") for doc in documents]
                embeddings = self._get_embeddings(texts)

                # Insert documents and embeddings
                document_ids = []
                cursor = self.conn.cursor()

                for i, doc in enumerate(documents):
                    # Generate a document ID if not provided
                    doc_id = doc.get("id", str(uuid.uuid4()))
                    document_ids.append(doc_id)

                    # Insert document
                    metadata = {k: v for k, v in doc.items() if k not in ["content", "source", "title", "id"]}
                    cursor.execute(
                        """
                        INSERT INTO documents (id, content, source, title, metadata)
                        VALUES (?, ?, ?, ?, ?)
                        """,
                        (
                            doc_id,
                            doc.get("content", ""),
                            doc.get("source", ""),
                            doc.get("title", ""),
                            json.dumps(metadata)
                        )
                    )

                    # Insert embedding
                    embedding_id = str(uuid.uuid4())
                    cursor.execute(
                        """
                        INSERT INTO embeddings (id, document_id, embedding, dimension)
                        VALUES (?, ?, ?, ?)
                        """,
                        (
                            embedding_id,
                            doc_id,
                            self._serialize_embedding(embeddings[i]),
                            len(embeddings[i])
                        )
                    )

                self.conn.commit()

                return document_ids

    def search(
        self,
        query: str,
        top_k: Optional[int] = None,
        filter_expr: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Search for documents relevant to the query.

        Args:
            query: The query to search for
            top_k: Number of results to return (if None, uses the default)
            filter_expr: Filter expression for metadata filtering
            **kwargs: Additional implementation-specific arguments

        Returns:
            List of dictionaries containing the retrieved documents and their similarity scores
        """
        with span("search"):
            with measure_block_latency("search"):
                # Use the provided top_k or the default
                if top_k is None:
                    top_k = self.top_k

                # Generate embedding for the query
                query_embedding = self._get_embeddings([query])[0]

                # Search for similar documents
                results = self._search_similar_documents(query_embedding, top_k, filter_expr)

                return results

    def process(
        self,
        query: str,
        filter_expr: Optional[Dict[str, Any]] = None,
        callback: Optional[Callable[[str], None]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process a query using RAG.

        Args:
            query: The query to process
            filter_expr: Filter expression for metadata filtering
            callback: Optional callback function for streaming responses
            **kwargs: Additional implementation-specific arguments

        Returns:
            Dictionary containing the answer, retrieved documents, and other information
        """
        with span("process"):
            with measure_block_latency("process"):
                # Search for relevant documents
                documents = self.search(query, filter_expr=filter_expr)

                # Format the documents for the prompt
                formatted_docs = self._format_documents(documents)

                # Create the system prompt
                system_prompt = self._create_system_prompt()

                # Create the user prompt
                user_prompt = self._create_user_prompt(query, formatted_docs)

                # Generate the answer
                stream = callback is not None

                if stream:
                    answer = self.api_provider.complete(
                        system_prompt=system_prompt,
                        user_prompt=user_prompt,
                        model=self.model,
                        temperature=self.temperature,
                        max_tokens=self.max_tokens,
                        stream=True,
                        callback=callback
                    )
                else:
                    answer = self.api_provider.complete(
                        system_prompt=system_prompt,
                        user_prompt=user_prompt,
                        model=self.model,
                        temperature=self.temperature,
                        max_tokens=self.max_tokens
                    )

                # Return the result
                return {
                    "query": query,
                    "answer": answer,
                    "documents": documents,
                    "model": self.model,
                    "provider": self.provider
                }

    def clear(self) -> None:
        """Clear all documents from the vector store."""
        with span("clear"):
            cursor = self.conn.cursor()
            cursor.execute("DELETE FROM embeddings")
            cursor.execute("DELETE FROM documents")
            self.conn.commit()

    def count(self) -> int:
        """Return the number of documents in the vector store."""
        with span("count"):
            cursor = self.conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM documents")
            return cursor.fetchone()[0]

    def close(self) -> None:
        """Close the vector store connection."""
        with span("close"):
            if hasattr(self, "conn") and self.conn:
                self.conn.close()

    def _search_similar_documents(
        self,
        query_embedding: List[float],
        top_k: int,
        filter_expr: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for similar documents using cosine similarity.

        Args:
            query_embedding: The query embedding
            top_k: Number of results to return
            filter_expr: Filter expression for metadata filtering

        Returns:
            List of dictionaries containing the retrieved documents and their similarity scores
        """
        cursor = self.conn.cursor()

        # Get all embeddings
        cursor.execute("SELECT document_id, embedding FROM embeddings")
        rows = cursor.fetchall()

        # Calculate cosine similarity for each embedding
        similarities = []
        for row in rows:
            document_id = row["document_id"]
            embedding = self._deserialize_embedding(row["embedding"])

            # Calculate cosine similarity
            similarity = self._cosine_similarity(query_embedding, embedding)
            similarities.append((document_id, similarity))

        # Sort by similarity (descending)
        similarities.sort(key=lambda x: x[1], reverse=True)

        # Get the top_k document IDs
        top_document_ids = [doc_id for doc_id, _ in similarities[:top_k]]

        # Get the documents
        documents = []
        for doc_id, similarity in similarities[:top_k]:
            cursor.execute(
                "SELECT id, content, source, title, metadata FROM documents WHERE id = ?",
                (doc_id,)
            )
            row = cursor.fetchone()

            if row:
                # Parse metadata
                metadata = json.loads(row["metadata"]) if row["metadata"] else {}

                # Apply filter if provided
                if filter_expr and not self._matches_filter(metadata, filter_expr):
                    continue

                # Create document dictionary
                document = {
                    "id": row["id"],
                    "content": row["content"],
                    "source": row["source"],
                    "title": row["title"],
                    "score": similarity
                }

                # Add metadata
                document.update(metadata)

                documents.append(document)

        return documents

    def _matches_filter(self, metadata: Dict[str, Any], filter_expr: Dict[str, Any]) -> bool:
        """
        Check if metadata matches the filter expression.

        Args:
            metadata: Document metadata
            filter_expr: Filter expression

        Returns:
            True if the metadata matches the filter, False otherwise
        """
        for key, value in filter_expr.items():
            if key not in metadata or metadata[key] != value:
                return False
        return True

    def _cosine_similarity(self, a: List[float], b: List[float]) -> float:
        """
        Calculate cosine similarity between two vectors.

        Args:
            a: First vector
            b: Second vector

        Returns:
            Cosine similarity
        """
        a_np = np.array(a)
        b_np = np.array(b)

        # Calculate cosine similarity
        dot_product = np.dot(a_np, b_np)
        norm_a = np.linalg.norm(a_np)
        norm_b = np.linalg.norm(b_np)

        # Avoid division by zero
        if norm_a == 0 or norm_b == 0:
            return 0

        return dot_product / (norm_a * norm_b)

    def _serialize_embedding(self, embedding: List[float]) -> bytes:
        """
        Serialize an embedding to bytes.

        Args:
            embedding: The embedding to serialize

        Returns:
            Serialized embedding
        """
        return np.array(embedding, dtype=np.float32).tobytes()

    def _deserialize_embedding(self, data: bytes) -> List[float]:
        """
        Deserialize an embedding from bytes.

        Args:
            data: The serialized embedding

        Returns:
            Deserialized embedding
        """
        return np.frombuffer(data, dtype=np.float32).tolist()
