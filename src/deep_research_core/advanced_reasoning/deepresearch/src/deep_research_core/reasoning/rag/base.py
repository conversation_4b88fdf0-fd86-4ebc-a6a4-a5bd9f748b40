"""
Base RAG (Retrieval-Augmented Generation) reasoner.

This module provides the abstract base class for RAG reasoners.
"""

from abc import ABC, abstractmethod
from typing import List, Optional

from deep_research_core.models.base import BaseModel
from deep_research_core.reasoning.base import BaseReasoner
from deep_research_core.utils.types import Document, ReasoningResult


class RAGReasoner(BaseReasoner, ABC):
    """
    Abstract base class for RAG (Retrieval-Augmented Generation) reasoners.
    
    This class defines the interface for RAG reasoners that combine document
    retrieval with language model generation.
    """
    
    def __init__(
        self,
        model: BaseModel = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        **kwargs
    ):
        """
        Initialize the RAG reasoner.
        
        Args:
            model: The language model to use for generation
            temperature: Temperature for generation
            max_tokens: Maximum tokens for generation
            **kwargs: Additional parameters
        """
        super().__init__()
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
    
    @abstractmethod
    def retrieve(self, query: str, k: int = 3, **kwargs) -> ReasoningResult:
        """
        Retrieve documents relevant to the query.
        
        Args:
            query: The query to search for
            k: Maximum number of documents to retrieve
            **kwargs: Additional parameters
            
        Returns:
            ReasoningResult with retrieved documents
        """
        pass
    
    @abstractmethod
    def reason(self, query: str, documents: Optional[List[Document]] = None, **kwargs) -> ReasoningResult:
        """
        Perform reasoning with the query and documents.
        
        Args:
            query: The query to reason about
            documents: Optional documents to use (will retrieve if not provided)
            **kwargs: Additional parameters
            
        Returns:
            ReasoningResult with the reasoned answer
        """
        pass
