"""
Feedback loop for RAG systems.

This module provides functionality for implementing feedback loops in RAG systems,
allowing the system to learn from user feedback and improve over time.
"""

import os
import json
import time
import uuid
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Callable, Union
from datetime import datetime

from deep_research_core.utils.structured_logging import get_logger
from deep_research_core.evaluation.qa_evaluator import QAEvaluator

# Get logger
logger = get_logger(__name__)

class FeedbackLoop:
    """
    Feedback loop for RAG systems.
    
    This class provides functionality for implementing feedback loops in RAG systems,
    allowing the system to learn from user feedback and improve over time.
    """
    
    def __init__(
        self,
        storage_path: Optional[str] = None,
        evaluator: Optional[QAEvaluator] = None,
        auto_apply_improvements: bool = False,
        min_feedback_count: int = 10,
        language: str = "en",
        verbose: bool = False
    ):
        """
        Initialize the feedback loop.
        
        Args:
            storage_path: Path to store feedback data
            evaluator: QA evaluator to use for automatic evaluation
            auto_apply_improvements: Whether to automatically apply improvements
            min_feedback_count: Minimum number of feedback items needed for analysis
            language: Language of the content
            verbose: Whether to print verbose logs
        """
        self.storage_path = storage_path
        self.evaluator = evaluator or QAEvaluator(language=language)
        self.auto_apply_improvements = auto_apply_improvements
        self.min_feedback_count = min_feedback_count
        self.language = language
        self.verbose = verbose
        
        # Create storage directory if needed
        if storage_path:
            os.makedirs(storage_path, exist_ok=True)
            
        # Initialize feedback storage
        self.feedback_data = {}
        self.improvement_history = []
        
        # Load existing feedback data if available
        self._load_feedback_data()
        
    def add_feedback(
        self,
        query_id: str,
        query: str,
        answer: str,
        documents: List[Dict[str, Any]],
        feedback: Dict[str, Any],
        user_id: Optional[str] = None
    ) -> str:
        """
        Add user feedback for a query.
        
        Args:
            query_id: ID of the query
            query: The query text
            answer: The generated answer
            documents: The retrieved documents
            feedback: User feedback (rating, comments, etc.)
            user_id: ID of the user providing feedback
            
        Returns:
            ID of the feedback entry
        """
        # Generate feedback ID
        feedback_id = str(uuid.uuid4())
        
        # Create feedback entry
        feedback_entry = {
            "feedback_id": feedback_id,
            "query_id": query_id,
            "query": query,
            "answer": answer,
            "documents": documents,
            "feedback": feedback,
            "user_id": user_id,
            "timestamp": datetime.now().isoformat(),
            "applied_improvements": []
        }
        
        # Add automatic evaluation if evaluator is available
        if self.evaluator:
            try:
                evaluation = self.evaluator.evaluate(query, answer, documents)
                feedback_entry["evaluation"] = evaluation
            except Exception as e:
                logger.error(f"Error evaluating answer: {str(e)}")
        
        # Store feedback
        self.feedback_data[feedback_id] = feedback_entry
        
        # Save feedback data
        self._save_feedback_data()
        
        # Apply improvements if auto-apply is enabled
        if self.auto_apply_improvements and len(self.feedback_data) >= self.min_feedback_count:
            self.analyze_feedback()
        
        return feedback_id
    
    def get_feedback(self, feedback_id: str) -> Optional[Dict[str, Any]]:
        """
        Get feedback by ID.
        
        Args:
            feedback_id: ID of the feedback entry
            
        Returns:
            Feedback entry, or None if not found
        """
        return self.feedback_data.get(feedback_id)
    
    def get_feedback_by_query(self, query_id: str) -> List[Dict[str, Any]]:
        """
        Get all feedback for a query.
        
        Args:
            query_id: ID of the query
            
        Returns:
            List of feedback entries for the query
        """
        return [
            feedback for feedback in self.feedback_data.values()
            if feedback["query_id"] == query_id
        ]
    
    def analyze_feedback(self) -> Dict[str, Any]:
        """
        Analyze feedback data and identify potential improvements.
        
        Returns:
            Dictionary containing analysis results and improvement suggestions
        """
        if len(self.feedback_data) < self.min_feedback_count:
            logger.info(f"Not enough feedback data for analysis (minimum {self.min_feedback_count} required)")
            return {
                "status": "insufficient_data",
                "message": f"Need at least {self.min_feedback_count} feedback items for analysis",
                "improvements": []
            }
        
        # Initialize improvements
        improvements = []
        
        # Analyze by rating
        rating_improvements = self._analyze_by_rating()
        improvements.extend(rating_improvements)
        
        # Analyze by document relevance
        document_improvements = self._analyze_by_document_relevance()
        improvements.extend(document_improvements)
        
        # Analyze by query type
        query_improvements = self._analyze_by_query_type()
        improvements.extend(query_improvements)
        
        # Sort improvements by confidence
        improvements.sort(key=lambda x: x.get("confidence", 0), reverse=True)
        
        # Create analysis results
        analysis_results = {
            "status": "success",
            "message": f"Analyzed {len(self.feedback_data)} feedback items",
            "timestamp": datetime.now().isoformat(),
            "improvements": improvements
        }
        
        return analysis_results
    
    def apply_improvements(
        self,
        improvements: List[Dict[str, Any]],
        rag_system: Any
    ) -> Dict[str, Any]:
        """
        Apply selected improvements to the RAG system.
        
        Args:
            improvements: List of improvements to apply
            rag_system: RAG system to apply improvements to
            
        Returns:
            Dictionary containing results of applying improvements
        """
        results = {
            "applied": [],
            "failed": [],
            "skipped": []
        }
        
        for improvement in improvements:
            improvement_type = improvement.get("type")
            confidence = improvement.get("confidence", 0)
            
            # Skip if confidence is too low
            if confidence < 0.7:
                results["skipped"].append({
                    "improvement": improvement,
                    "reason": f"Confidence too low ({confidence:.2f} < 0.7)"
                })
                continue
            
            try:
                # Apply improvement based on type
                if improvement_type == "retrieval_count":
                    # Update top_k parameter
                    new_top_k = improvement.get("suggested_value")
                    if hasattr(rag_system, "top_k") and new_top_k:
                        old_value = rag_system.top_k
                        rag_system.top_k = new_top_k
                        
                        # Record the applied improvement
                        applied_improvement = {
                            "improvement": improvement,
                            "old_value": old_value,
                            "new_value": new_top_k,
                            "timestamp": datetime.now().isoformat()
                        }
                        
                        results["applied"].append(applied_improvement)
                        self.improvement_history.append(applied_improvement)
                
                elif improvement_type == "reranking_weight":
                    # Update reranking weight
                    new_weight = improvement.get("suggested_value")
                    if hasattr(rag_system, "reranking_weight") and new_weight:
                        old_value = rag_system.reranking_weight
                        rag_system.reranking_weight = new_weight
                        
                        # Record the applied improvement
                        applied_improvement = {
                            "improvement": improvement,
                            "old_value": old_value,
                            "new_value": new_weight,
                            "timestamp": datetime.now().isoformat()
                        }
                        
                        results["applied"].append(applied_improvement)
                        self.improvement_history.append(applied_improvement)
                
                elif improvement_type == "prompt_template":
                    # Update prompt template
                    new_template = improvement.get("suggested_value")
                    if hasattr(rag_system, "prompt_template") and new_template:
                        old_value = rag_system.prompt_template
                        rag_system.prompt_template = new_template
                        
                        # Record the applied improvement
                        applied_improvement = {
                            "improvement": improvement,
                            "old_value": old_value,
                            "new_value": new_template,
                            "timestamp": datetime.now().isoformat()
                        }
                        
                        results["applied"].append(applied_improvement)
                        self.improvement_history.append(applied_improvement)
                
                elif improvement_type == "query_expansion":
                    # Update query expansion setting
                    new_value = improvement.get("suggested_value")
                    if hasattr(rag_system, "use_query_expansion") and new_value is not None:
                        old_value = rag_system.use_query_expansion
                        rag_system.use_query_expansion = new_value
                        
                        # Record the applied improvement
                        applied_improvement = {
                            "improvement": improvement,
                            "old_value": old_value,
                            "new_value": new_value,
                            "timestamp": datetime.now().isoformat()
                        }
                        
                        results["applied"].append(applied_improvement)
                        self.improvement_history.append(applied_improvement)
                
                else:
                    # Unknown improvement type
                    results["skipped"].append({
                        "improvement": improvement,
                        "reason": f"Unknown improvement type: {improvement_type}"
                    })
            
            except Exception as e:
                # Record the failure
                results["failed"].append({
                    "improvement": improvement,
                    "error": str(e)
                })
                logger.error(f"Error applying improvement: {str(e)}")
        
        # Save improvement history
        self._save_improvement_history()
        
        return results
    
    def get_improvement_history(self) -> List[Dict[str, Any]]:
        """
        Get the history of applied improvements.
        
        Returns:
            List of applied improvements
        """
        return self.improvement_history
    
    def _analyze_by_rating(self) -> List[Dict[str, Any]]:
        """
        Analyze feedback by rating.
        
        Returns:
            List of improvement suggestions
        """
        improvements = []
        
        # Get all ratings
        ratings = []
        for feedback in self.feedback_data.values():
            if "feedback" in feedback and "rating" in feedback["feedback"]:
                ratings.append(feedback["feedback"]["rating"])
        
        if not ratings:
            return []
        
        # Calculate average rating
        avg_rating = sum(ratings) / len(ratings)
        
        # If average rating is low, suggest improvements
        if avg_rating < 3.5:
            # Check if evaluations are available
            evaluations = []
            for feedback in self.feedback_data.values():
                if "evaluation" in feedback and "metrics" in feedback["evaluation"]:
                    evaluations.append(feedback["evaluation"])
            
            if evaluations:
                # Analyze metrics
                metric_scores = {}
                for evaluation in evaluations:
                    for metric, data in evaluation["metrics"].items():
                        if metric not in metric_scores:
                            metric_scores[metric] = []
                        metric_scores[metric].append(data["score"])
                
                # Calculate average scores
                avg_scores = {
                    metric: sum(scores) / len(scores)
                    for metric, scores in metric_scores.items()
                }
                
                # Find lowest scoring metrics
                sorted_metrics = sorted(avg_scores.items(), key=lambda x: x[1])
                
                # Suggest improvements based on lowest scoring metrics
                if sorted_metrics:
                    lowest_metric, lowest_score = sorted_metrics[0]
                    
                    if lowest_metric == "relevance" and lowest_score < 7:
                        improvements.append({
                            "type": "retrieval_count",
                            "metric": "relevance",
                            "current_score": lowest_score,
                            "suggested_value": 10,  # Increase top_k
                            "suggestion": "Increase the number of retrieved documents to improve relevance",
                            "confidence": 0.8,
                            "auto_applicable": True
                        })
                    
                    elif lowest_metric == "factual_accuracy" and lowest_score < 7:
                        improvements.append({
                            "type": "reranking_weight",
                            "metric": "factual_accuracy",
                            "current_score": lowest_score,
                            "suggested_value": 0.7,  # Increase reranking weight
                            "suggestion": "Increase the weight of reranking to prioritize more relevant documents",
                            "confidence": 0.75,
                            "auto_applicable": True
                        })
                    
                    elif lowest_metric == "completeness" and lowest_score < 7:
                        improvements.append({
                            "type": "prompt_template",
                            "metric": "completeness",
                            "current_score": lowest_score,
                            "suggested_value": "Please provide a comprehensive answer to the following question based on the provided documents:\n\nQuestion: {query}\n\nDocuments:\n{documents}\n\nAnswer:",
                            "suggestion": "Update prompt template to emphasize comprehensive answers",
                            "confidence": 0.7,
                            "auto_applicable": True
                        })
                    
                    elif lowest_metric == "source_attribution" and lowest_score < 7:
                        improvements.append({
                            "type": "prompt_template",
                            "metric": "source_attribution",
                            "current_score": lowest_score,
                            "suggested_value": "Please provide a detailed answer to the following question based on the provided documents. Include citations to the relevant documents in your answer:\n\nQuestion: {query}\n\nDocuments:\n{documents}\n\nAnswer:",
                            "suggestion": "Update prompt template to emphasize source attribution",
                            "confidence": 0.7,
                            "auto_applicable": True
                        })
        
        return improvements
    
    def _analyze_by_document_relevance(self) -> List[Dict[str, Any]]:
        """
        Analyze feedback by document relevance.
        
        Returns:
            List of improvement suggestions
        """
        improvements = []
        
        # Check if document relevance feedback is available
        doc_relevance_feedback = []
        for feedback in self.feedback_data.values():
            if "feedback" in feedback and "document_relevance" in feedback["feedback"]:
                doc_relevance_feedback.append(feedback)
        
        if not doc_relevance_feedback:
            return []
        
        # Calculate average document relevance
        avg_relevance = sum(
            feedback["feedback"]["document_relevance"]
            for feedback in doc_relevance_feedback
        ) / len(doc_relevance_feedback)
        
        # If average relevance is low, suggest improvements
        if avg_relevance < 3.5:
            improvements.append({
                "type": "query_expansion",
                "metric": "document_relevance",
                "current_score": avg_relevance,
                "suggested_value": True,
                "suggestion": "Enable query expansion to improve document retrieval",
                "confidence": 0.8,
                "auto_applicable": True
            })
        
        return improvements
    
    def _analyze_by_query_type(self) -> List[Dict[str, Any]]:
        """
        Analyze feedback by query type.
        
        Returns:
            List of improvement suggestions
        """
        improvements = []
        
        # Group feedback by query type
        query_types = {}
        for feedback in self.feedback_data.values():
            query = feedback.get("query", "").lower()
            
            # Simple query type classification
            query_type = "general"
            if query.startswith("what") or query.startswith("who") or query.startswith("where"):
                query_type = "factual"
            elif query.startswith("how") or query.startswith("why"):
                query_type = "explanatory"
            elif query.startswith("compare") or "difference between" in query:
                query_type = "comparative"
            
            # Vietnamese query classification
            if self.language == "vi":
                if query.startswith("gì") or query.startswith("ai") or query.startswith("ở đâu"):
                    query_type = "factual"
                elif query.startswith("làm thế nào") or query.startswith("tại sao"):
                    query_type = "explanatory"
                elif query.startswith("so sánh") or "sự khác nhau giữa" in query:
                    query_type = "comparative"
            
            if query_type not in query_types:
                query_types[query_type] = []
            
            query_types[query_type].append(feedback)
        
        # Analyze each query type
        for query_type, feedbacks in query_types.items():
            # Calculate average rating for this query type
            if not all("feedback" in f and "rating" in f["feedback"] for f in feedbacks):
                continue
                
            avg_rating = sum(f["feedback"]["rating"] for f in feedbacks) / len(feedbacks)
            
            # If average rating is low, suggest improvements
            if avg_rating < 3.5:
                if query_type == "explanatory":
                    improvements.append({
                        "type": "prompt_template",
                        "query_type": query_type,
                        "current_score": avg_rating,
                        "suggested_value": "Please provide a detailed explanation to the following question based on the provided documents:\n\nQuestion: {query}\n\nDocuments:\n{documents}\n\nExplanation:",
                        "suggestion": f"Update prompt template for {query_type} queries to emphasize detailed explanations",
                        "confidence": 0.75,
                        "auto_applicable": True
                    })
                
                elif query_type == "comparative":
                    improvements.append({
                        "type": "prompt_template",
                        "query_type": query_type,
                        "current_score": avg_rating,
                        "suggested_value": "Please compare the following items based on the provided documents. Include similarities, differences, and a structured comparison:\n\nQuestion: {query}\n\nDocuments:\n{documents}\n\nComparison:",
                        "suggestion": f"Update prompt template for {query_type} queries to emphasize structured comparisons",
                        "confidence": 0.75,
                        "auto_applicable": True
                    })
        
        return improvements
    
    def _save_feedback_data(self) -> None:
        """Save feedback data to storage."""
        if not self.storage_path:
            return
        
        try:
            # Save to file
            with open(os.path.join(self.storage_path, "feedback_data.json"), "w") as f:
                json.dump(self.feedback_data, f, indent=2)
        
        except Exception as e:
            logger.error(f"Error saving feedback data: {str(e)}")
    
    def _load_feedback_data(self) -> None:
        """Load feedback data from storage."""
        if not self.storage_path:
            return
        
        try:
            # Check if file exists
            file_path = os.path.join(self.storage_path, "feedback_data.json")
            
            if not os.path.exists(file_path):
                return
            
            # Load from file
            with open(file_path, "r") as f:
                self.feedback_data = json.load(f)
        
        except Exception as e:
            logger.error(f"Error loading feedback data: {str(e)}")
    
    def _save_improvement_history(self) -> None:
        """Save improvement history to storage."""
        if not self.storage_path:
            return
        
        try:
            # Save to file
            with open(os.path.join(self.storage_path, "improvement_history.json"), "w") as f:
                json.dump(self.improvement_history, f, indent=2)
        
        except Exception as e:
            logger.error(f"Error saving improvement history: {str(e)}")
    
    def _load_improvement_history(self) -> None:
        """Load improvement history from storage."""
        if not self.storage_path:
            return
        
        try:
            # Check if file exists
            file_path = os.path.join(self.storage_path, "improvement_history.json")
            
            if not os.path.exists(file_path):
                return
            
            # Load from file
            with open(file_path, "r") as f:
                self.improvement_history = json.load(f)
        
        except Exception as e:
            logger.error(f"Error loading improvement history: {str(e)}")
