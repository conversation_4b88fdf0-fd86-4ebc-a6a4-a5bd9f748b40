"""
SQLite-based Vector Retrieval-Augmented Generation (RAG) with semantic chunking.

This module extends the SQLiteVectorRAG implementation with semantic chunking,
which splits documents based on semantic structure rather than fixed character counts.
"""

import os
from functools import lru_cache
from typing import Dict, Any, List, Optional, Tuple, Union, Callable

from .sqlite_vector_rag import SQLiteVectorRAG
from ..retrieval.semantic_chunker import SemanticChunker
from ..utils.structured_logging import get_logger
from ..utils.performance_metrics import measure_latency, measure_block_latency
from ..utils.distributed_tracing import trace_function, span

# Create a logger
logger = get_logger(__name__)

class SQLiteVectorRAGWithSemanticChunking(SQLiteVectorRAG):
    """
    Extends SQLiteVectorRAG with semantic chunking capabilities.
    
    This class improves the document processing of SQLiteVectorRAG by using
    semantic chunking, which splits documents based on semantic structure
    rather than fixed character counts, resulting in more coherent chunks.
    """
    
    def __init__(self, use_cache: bool = True, cache_size: int = 100, db_path: str,
        embedding_model: str = "all-MiniLM-L6-v2",
        provider: str = "openrouter",
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        top_k: int = 5,
        chunk_size: int = 1000,
        chunk_overlap: int = 200,
        api_key: Optional[str] = None,
        chunking_strategy: str = "hybrid",
        respect_sections: bool = True,
        respect_sentences: bool = True,
        use_semantic_chunking: bool = True
    ):
        """
        Initialize SQLiteVectorRAGWithSemanticChunking.
        
        Args:
            db_path: Path to the SQLite database
            embedding_model: Name of the sentence-transformers model to use
            provider: Model provider (openai, anthropic, openrouter)
            model: Model name
            temperature: Temperature for text generation
            max_tokens: Maximum number of tokens to generate
            top_k: Number of documents to retrieve
            chunk_size: Maximum size of a chunk in characters
            chunk_overlap: Size of overlap between chunks in characters
            api_key: API key for the provider
            chunking_strategy: Chunking strategy ("paragraph", "section", "hybrid", "sentence")
            respect_sections: Whether to respect section boundaries
            respect_sentences: Whether to respect sentence boundaries
            use_semantic_chunking: Whether to use semantic chunking (if False, falls back to character-based chunking)
        """
        # Initialize the parent class
        super().__init__(
            db_path=db_path,
            embedding_model=embedding_model,
            provider=provider,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            top_k=top_k,
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            api_key=api_key
        )
        
        # Store semantic chunking parameters
        self.chunking_strategy = chunking_strategy
        self.respect_sections = respect_sections
        self.respect_sentences = respect_sentences
        self.use_semantic_chunking = use_semantic_chunking
        
        # Initialize semantic chunker
        if self.use_semantic_chunking:
            self.semantic_chunker = SemanticChunker(
                strategy=self.chunking_strategy,
                max_chunk_size=self.chunk_size,
                min_chunk_size=self.chunk_size // 2,  # Minimum chunk size is half the maximum
                overlap_size=self.chunk_overlap,
                respect_sections=self.respect_sections,
                respect_sentences=self.respect_sentences
            )
    
    def _chunk_document(self, document: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Chunk a document based on semantic structure or character count.
        
        Args:
            document: Document dictionary with 'content', 'source', etc.
            
        Returns:
            List of document chunks
        """
        if self.use_semantic_chunking:
            # Use semantic chunking
            return self.semantic_chunker.chunk_document(document)
        else:
            # Fall back to character-based chunking
            return super()._chunk_document(document)
    
    @trace_function(name="sqlite_vector_rag_semantic_add_documents")
    @measure_latency("sqlite_vector_rag_semantic_add_documents")
    def add_documents(
        self,
        documents: List[Dict[str, Any]],
        update_existing: bool = True,
        auto_chunk: bool = True
    ) -> List[int]:
        """
        Add documents to the database with semantic chunking.
        
        Args:
            documents: List of document dictionaries with 'content', 'source', etc.
            update_existing: Whether to update existing documents with the same hash
            auto_chunk: Whether to automatically chunk long documents
            
        Returns:
            List of IDs of the inserted or updated documents
        """
        # Use the parent class method, which will call our overridden _chunk_document method
        return super().add_documents(documents, update_existing, auto_chunk)
    
    def analyze_document(self, content: str) -> Dict[str, Any]:
        """
        Analyze a document and return information about its structure.
        
        Args:
            content: Document content
            
        Returns:
            Dictionary with document analysis
        """
        if self.use_semantic_chunking:
            return self.semantic_chunker.analyze_document(content)
        else:
            # Simple analysis for character-based chunking
            return {
                "total_length": len(content),
                "estimated_chunks": (len(content) + self.chunk_size - 1) // self.chunk_size
            }
