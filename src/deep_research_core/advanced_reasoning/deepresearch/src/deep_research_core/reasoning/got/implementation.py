"""
Graph of Thoughts (GoT) Reasoner Implementation.

This module provides the implementation of Graph of Thoughts (GoT) reasoning,
which extends Tree of Thought by supporting more complex graph structures with
non-hierarchical connections between thoughts.
"""

import time
import json
import re
import random
import networkx as nx
from typing import Dict, Any, List, Optional, Callable, Tuple, Set, Union
from collections import defaultdict

from ..base import BaseReasoner
from .base import GoTBase
from ...models.api.openai import openai_provider
from ...models.api.anthropic import anthropic_provider
from ...models.api.openrouter import openrouter_provider
from ...utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

class GraphOfThoughtsReasoner(BaseReasoner, GoTBase):
    """
    Graph of Thoughts (GoT) Reasoner.
    
    This reasoner extends the Tree of Thought approach by allowing more complex graph
    structures with:
    - Bidirectional connections between thoughts
    - Cross-connections between branches
    - Cyclic reasoning paths
    - Merging of thought branches
    """
    
    def __init__(
        self,
        provider: str = "openai",
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        max_depth: int = 3,
        max_branches: int = 3,
        max_nodes: int = 100,
        thought_fusion: bool = True,
        edge_weighting: bool = True,
        cycle_detection: bool = True,
        adaptive_exploration: bool = True,
        language: str = "en",
        verbose: bool = False,
        **kwargs
    ):
        """
        Initialize the Graph of Thoughts reasoner.
        
        Args:
            provider: The provider to use ("openai", "anthropic", "openrouter")
            model: The model to use (if None, will use provider's default)
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            max_depth: Maximum depth of the graph
            max_branches: Maximum number of branches per node
            max_nodes: Maximum total nodes in the graph
            thought_fusion: Whether to allow merging of similar thoughts
            edge_weighting: Whether to use weighted edges
            cycle_detection: Whether to detect and handle cycles
            adaptive_exploration: Whether to adapt exploration strategy based on progress
            language: Language to use (en, vi, etc.)
            verbose: Whether to print verbose output
            **kwargs: Additional implementation-specific arguments
        """
        super().__init__(
            provider=provider,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            **kwargs
        )
        
        self.max_depth = max_depth
        self.max_branches = max_branches
        self.max_nodes = max_nodes
        self.thought_fusion = thought_fusion
        self.edge_weighting = edge_weighting
        self.cycle_detection = cycle_detection
        self.adaptive_exploration = adaptive_exploration
        self.language = language
        self.verbose = verbose
        
        # Set up the provider
        if provider == "openai":
            self.api_provider = openai_provider
            self.model = model or "gpt-4o"
        elif provider == "anthropic":
            self.api_provider = anthropic_provider
            self.model = model or "claude-3-opus"
        elif provider == "openrouter":
            self.api_provider = openrouter_provider
            self.model = model or "anthropic/claude-3-opus"
        else:
            raise ValueError(f"Unsupported provider: {provider}")
            
        # Initialize thought graph
        self.graph = nx.DiGraph()
        
        logger.info(f"Initialized GraphOfThoughtsReasoner with model {self.model}")
    
    def reason(
        self,
        query: str,
        context: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Perform Graph of Thoughts reasoning on the given query.
        
        Args:
            query: The query to reason about
            context: Optional context to include
            callback: Optional callback function for streaming responses
            **kwargs: Additional implementation-specific arguments
            
        Returns:
            Dictionary containing the reasoning result and additional information
        """
        start_time = time.time()
        
        # Reset the graph for this query
        self.graph = nx.DiGraph()
        
        # Add the query as the root node
        root_id = "root"
        self.graph.add_node(root_id, content=query, type="query", depth=0, score=0)
        
        # Generate initial thoughts
        initial_thoughts = self._generate_initial_thoughts(query, context, **kwargs)
        
        # Add initial thoughts to the graph
        for i, thought in enumerate(initial_thoughts):
            thought_id = f"thought_0_{i}"
            self.graph.add_node(
                thought_id,
                content=thought["content"],
                type="thought",
                depth=1,
                score=thought.get("score", 0)
            )
            self.graph.add_edge(root_id, thought_id, weight=1.0)
        
        # Explore the graph iteratively
        current_depth = 1
        while current_depth < self.max_depth and len(self.graph) < self.max_nodes:
            # Select nodes to expand
            nodes_to_expand = self._select_nodes_to_expand(current_depth)
            
            if not nodes_to_expand:
                break
                
            # Expand selected nodes
            for node_id in nodes_to_expand:
                self._expand_node(node_id, query, current_depth, context, **kwargs)
                
            # Update edge weights if enabled
            if self.edge_weighting:
                self._update_edge_weights()
                
            # Merge similar thoughts if enabled
            if self.thought_fusion:
                self._merge_similar_thoughts()
                
            # Check for cycles if enabled
            if self.cycle_detection:
                self._handle_cycles()
                
            # Adapt exploration strategy if enabled
            if self.adaptive_exploration:
                self._adapt_exploration_strategy(current_depth)
                
            current_depth += 1
        
        # Find best path through the graph
        best_paths = self._find_best_paths()
        
        # Generate answer from best path
        answer = self._generate_answer_from_paths(query, best_paths, context)
        
        end_time = time.time()
        
        # Prepare result and visualization data
        visualization_data = self._prepare_visualization_data()
        
        return {
            "query": query,
            "answer": answer,
            "graph": self.graph,
            "visualization_data": visualization_data,
            "best_paths": best_paths,
            "reasoning_time": end_time - start_time,
            "node_count": len(self.graph),
            "provider": self.provider,
            "model": self.model
        }
    
    def _generate_initial_thoughts(
        self,
        query: str,
        context: Optional[str] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Generate initial thoughts for the given query.
        
        Args:
            query: The query to reason about
            context: Optional context to include
            **kwargs: Additional implementation-specific arguments
            
        Returns:
            List of initial thoughts
        """
        try:
            # Create the prompt for generating initial thoughts
            if self.language == "vi":
                system_prompt = """Bạn là một trợ lý AI thông minh giúp tạo ra các hướng suy luận khác nhau.
                Hãy tạo ra 3-5 hướng suy luận khác nhau để tiếp cận vấn đề được đưa ra.
                Mỗi hướng nên độc đáo và khám phá một góc nhìn hoặc chiến lược khác nhau.
                
                Định dạng:
                1. Hướng suy luận 1: [Mô tả ngắn]
                   Nội dung chi tiết...
                2. Hướng suy luận 2: [Mô tả ngắn]
                   Nội dung chi tiết...
                
                Hãy đảm bảo mỗi hướng suy luận đủ chi tiết để có thể phát triển thêm."""
            else:
                system_prompt = """You are an intelligent AI assistant helping to generate different reasoning directions.
                Create 3-5 different reasoning approaches to tackle the given problem.
                Each approach should be unique and explore a different perspective or strategy.
                
                Format:
                1. Approach 1: [Brief description]
                   Detailed content...
                2. Approach 2: [Brief description]
                   Detailed content...
                
                Ensure each approach is detailed enough to be developed further."""
            
            # Generate initial thoughts
            thoughts_response = self.api_provider.generate(
                prompt=query,
                system_prompt=system_prompt,
                model=self.model,
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            
            # Parse the response to extract initial thoughts
            thoughts = self._parse_thoughts(thoughts_response)
            
            # If no thoughts were generated, create a default one
            if not thoughts:
                logger.warning("No initial thoughts were generated, creating default thought")
                thoughts = [{
                    "content": "Let's analyze this problem step by step.",
                    "score": 0.5
                }]
            
            # Generate scores for initial thoughts
            thoughts = self._evaluate_thoughts(thoughts, query, context)
            
            return thoughts
            
        except Exception as e:
            logger.error(f"Error generating initial thoughts: {str(e)}")
            # Return a default thought in case of error
            return [{
                "content": "Let's analyze this problem step by step.",
                "score": 0.5
            }]
    
    def _parse_thoughts(self, response: str) -> List[Dict[str, Any]]:
        """
        Parse thoughts from the model's response.
        
        Args:
            response: The model's response
            
        Returns:
            List of parsed thoughts
        """
        thoughts = []
        
        # Try to parse numbered thoughts (e.g., "1. Thought 1", "2. Thought 2")
        numbered_pattern = r'\d+\.\s*(?:Approach|Hướng suy luận|[^:]+):\s*([^\n]+)(?:\n|$)([^0-9]+)?'
        matches = re.finditer(numbered_pattern, response, re.DOTALL)
        
        for match in matches:
            title = match.group(1).strip()
            content = match.group(2).strip() if match.group(2) else ""
            full_content = f"{title}\n\n{content}" if content else title
            thoughts.append({"content": full_content, "score": 0})
        
        # If no numbered thoughts were found, try to split by paragraphs
        if not thoughts:
            paragraphs = [p.strip() for p in response.split("\n\n") if p.strip()]
            for paragraph in paragraphs:
                if len(paragraph) > 20:  # Minimum length to be considered a thought
                    thoughts.append({"content": paragraph, "score": 0})
        
        return thoughts
    
    def _evaluate_thoughts(
        self,
        thoughts: List[Dict[str, Any]],
        query: str,
        context: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Evaluate and score the thoughts.
        
        Args:
            thoughts: List of thoughts to evaluate
            query: The original query
            context: Optional context
            
        Returns:
            List of evaluated thoughts with scores
        """
        try:
            # Create the prompt for evaluating thoughts
            if self.language == "vi":
                system_prompt = """Bạn là một trợ lý AI đánh giá chất lượng của các hướng suy luận.
                Đánh giá mỗi hướng suy luận dựa trên các tiêu chí sau:
                - Liên quan đến câu hỏi
                - Độ sâu và chi tiết
                - Tính logic và nhất quán
                - Tính sáng tạo và khả năng giải quyết vấn đề
                
                Cho mỗi hướng, đưa ra điểm số từ 0 đến 10 và giải thích ngắn gọn.
                Trả lời dưới định dạng JSON với mỗi hướng suy luận và điểm số tương ứng."""
            else:
                system_prompt = """You are an AI assistant evaluating the quality of reasoning approaches.
                Evaluate each approach based on these criteria:
                - Relevance to the question
                - Depth and detail
                - Logical coherence
                - Creativity and problem-solving potential
                
                For each approach, provide a score from 0 to 10 and a brief explanation.
                Respond in JSON format with each approach and its corresponding score."""
            
            # Prepare thoughts for evaluation
            thoughts_for_eval = "\n\n".join([
                f"Approach {i+1}:\n{thought['content']}" 
                for i, thought in enumerate(thoughts)
            ])
            
            eval_prompt = f"Question: {query}\n\n{thoughts_for_eval}"
            if context:
                eval_prompt += f"\n\nContext: {context}"
            
            # Generate evaluation
            eval_response = self.api_provider.generate(
                prompt=eval_prompt,
                system_prompt=system_prompt,
                model=self.model,
                temperature=0.3,  # Lower temperature for more consistent evaluations
                max_tokens=self.max_tokens
            )
            
            # Try to extract JSON from the response
            json_match = re.search(r'```json\s*(\{.*?\})\s*```', eval_response, re.DOTALL)
            if json_match:
                eval_json = json.loads(json_match.group(1))
            else:
                json_match = re.search(r'\{[^{]*"[^"]*"[^{]*:.*\}', eval_response, re.DOTALL)
                if json_match:
                    eval_json = json.loads(json_match.group(0))
                else:
                    # If JSON parsing fails, assign random scores
                    logger.warning("Failed to parse evaluation JSON, assigning default scores")
                    for thought in thoughts:
                        thought["score"] = random.uniform(5, 8) / 10.0
                    return thoughts
            
            # Apply scores to thoughts
            for i, thought in enumerate(thoughts):
                approach_key = f"Approach {i+1}"
                alt_approach_key = f"approach_{i+1}"
                
                if approach_key in eval_json:
                    if isinstance(eval_json[approach_key], dict) and "score" in eval_json[approach_key]:
                        thought["score"] = float(eval_json[approach_key]["score"]) / 10.0
                    else:
                        thought["score"] = float(eval_json[approach_key]) / 10.0
                elif alt_approach_key in eval_json:
                    if isinstance(eval_json[alt_approach_key], dict) and "score" in eval_json[alt_approach_key]:
                        thought["score"] = float(eval_json[alt_approach_key]["score"]) / 10.0
                    else:
                        thought["score"] = float(eval_json[alt_approach_key]) / 10.0
                elif str(i+1) in eval_json:
                    if isinstance(eval_json[str(i+1)], dict) and "score" in eval_json[str(i+1)]:
                        thought["score"] = float(eval_json[str(i+1)]["score"]) / 10.0
                    else:
                        thought["score"] = float(eval_json[str(i+1)]) / 10.0
                else:
                    # If score not found, use a default
                    thought["score"] = 0.5
            
            return thoughts
            
        except Exception as e:
            logger.error(f"Error evaluating thoughts: {str(e)}")
            # Assign default scores in case of error
            for thought in thoughts:
                thought["score"] = 0.5
            return thoughts
    
    def _select_nodes_to_expand(self, current_depth: int) -> List[str]:
        """
        Select nodes to expand at the current depth.
        
        Args:
            current_depth: Current depth in the graph
            
        Returns:
            List of node IDs to expand
        """
        # Get all nodes at the current depth
        nodes_at_depth = [
            node for node, data in self.graph.nodes(data=True)
            if data.get("depth") == current_depth and data.get("type") == "thought"
        ]
        
        # If we have too many nodes, select the best ones
        if len(nodes_at_depth) > self.max_branches:
            # Sort by score
            nodes_at_depth = sorted(
                nodes_at_depth,
                key=lambda node_id: self.graph.nodes[node_id].get("score", 0),
                reverse=True
            )
            nodes_at_depth = nodes_at_depth[:self.max_branches]
        
        return nodes_at_depth
    
    def _expand_node(
        self,
        node_id: str,
        query: str,
        current_depth: int,
        context: Optional[str] = None,
        **kwargs
    ) -> None:
        """
        Expand a node by generating new thoughts.
        
        Args:
            node_id: ID of the node to expand
            query: Original query
            current_depth: Current depth in the graph
            context: Optional context
            **kwargs: Additional implementation-specific arguments
        """
        try:
            # Get the content of the node
            node_content = self.graph.nodes[node_id]["content"]
            
            # Create the prompt for expanding the node
            if self.language == "vi":
                system_prompt = f"""Bạn là một trợ lý AI thông minh đang mở rộng hướng suy luận.
                Dựa trên hướng suy luận sau, hãy phát triển 2-3 hướng mới chi tiết hơn.
                Mỗi hướng mới nên đi sâu vào một khía cạnh cụ thể hoặc mở rộng ý tưởng từ hướng ban đầu.
                
                Định dạng:
                1. Hướng mở rộng 1: [Mô tả ngắn]
                   Nội dung chi tiết...
                2. Hướng mở rộng 2: [Mô tả ngắn]
                   Nội dung chi tiết...
                
                Hãy đảm bảo mỗi hướng mở rộng là một bước tiến từ hướng suy luận ban đầu."""
            else:
                system_prompt = f"""You are an intelligent AI assistant expanding a reasoning approach.
                Based on the following approach, develop 2-3 more detailed approaches.
                Each new approach should either delve deeper into a specific aspect or extend ideas from the original approach.
                
                Format:
                1. Extended approach 1: [Brief description]
                   Detailed content...
                2. Extended approach 2: [Brief description]
                   Detailed content...
                
                Ensure each extended approach is a progression from the original reasoning."""
            
            expand_prompt = f"Original question: {query}\n\nCurrent reasoning approach:\n{node_content}"
            if context:
                expand_prompt += f"\n\nContext: {context}"
            
            # Generate expanded thoughts
            expanded_response = self.api_provider.generate(
                prompt=expand_prompt,
                system_prompt=system_prompt,
                model=self.model,
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            
            # Parse the response to extract expanded thoughts
            expanded_thoughts = self._parse_thoughts(expanded_response)
            
            # If no thoughts were expanded, create a default one
            if not expanded_thoughts:
                logger.warning(f"No expanded thoughts were generated for node {node_id}, creating default thought")
                expanded_thoughts = [{
                    "content": f"Continuing from the previous thought: {node_content[:100]}...",
                    "score": self.graph.nodes[node_id].get("score", 0) * 0.9  # Slightly lower score
                }]
            
            # Evaluate expanded thoughts
            expanded_thoughts = self._evaluate_thoughts(expanded_thoughts, query, context)
            
            # Add expanded thoughts to the graph
            for i, thought in enumerate(expanded_thoughts):
                thought_id = f"thought_{current_depth+1}_{node_id}_{i}"
                self.graph.add_node(
                    thought_id,
                    content=thought["content"],
                    type="thought",
                    depth=current_depth + 1,
                    score=thought.get("score", 0)
                )
                # Add edge from parent to new thought
                edge_weight = 1.0
                self.graph.add_edge(node_id, thought_id, weight=edge_weight)
                
        except Exception as e:
            logger.error(f"Error expanding node {node_id}: {str(e)}")
    
    def _update_edge_weights(self) -> None:
        """
        Update the weights of edges in the graph based on node scores.
        """
        for u, v in self.graph.edges():
            source_score = self.graph.nodes[u].get("score", 0.5)
            target_score = self.graph.nodes[v].get("score", 0.5)
            # Edge weight is the average of source and target scores
            edge_weight = (source_score + target_score) / 2
            self.graph.edges[u, v]["weight"] = edge_weight
    
    def _merge_similar_thoughts(self) -> None:
        """
        Merge similar thoughts in the graph.
        """
        # Find pairs of similar nodes
        node_pairs = []
        nodes = list(self.graph.nodes())
        
        # Only compare nodes of the same depth to avoid merging across depths
        for i, node1 in enumerate(nodes):
            for node2 in nodes[i+1:]:
                # Skip if nodes are not both thoughts
                if self.graph.nodes[node1].get("type") != "thought" or self.graph.nodes[node2].get("type") != "thought":
                    continue
                    
                # Skip if nodes are at different depths
                if self.graph.nodes[node1].get("depth") != self.graph.nodes[node2].get("depth"):
                    continue
                
                # Check if contents are similar
                similarity = self._calculate_similarity(
                    self.graph.nodes[node1]["content"],
                    self.graph.nodes[node2]["content"]
                )
                
                if similarity > 0.8:  # Similarity threshold
                    node_pairs.append((node1, node2, similarity))
        
        # Sort pairs by similarity (highest first)
        node_pairs.sort(key=lambda x: x[2], reverse=True)
        
        # Merge similar nodes
        merged_nodes = set()
        for node1, node2, similarity in node_pairs:
            # Skip if either node has already been merged
            if node1 in merged_nodes or node2 in merged_nodes:
                continue
                
            # Merge node2 into node1 (keep node1)
            # Update node1 content and score
            content1 = self.graph.nodes[node1]["content"]
            content2 = self.graph.nodes[node2]["content"]
            score1 = self.graph.nodes[node1].get("score", 0.5)
            score2 = self.graph.nodes[node2].get("score", 0.5)
            
            # Combined content (choose the higher scored one)
            if score1 >= score2:
                new_content = content1
            else:
                new_content = content2
                
            # Combined score (max of the two)
            new_score = max(score1, score2)
            
            # Update node1
            self.graph.nodes[node1]["content"] = new_content
            self.graph.nodes[node1]["score"] = new_score
            
            # Redirect node2's incoming edges to node1
            for pred in list(self.graph.predecessors(node2)):
                if pred != node1:  # Avoid self-loops
                    edge_weight = self.graph.edges[pred, node2].get("weight", 1.0)
                    self.graph.add_edge(pred, node1, weight=edge_weight)
            
            # Redirect node2's outgoing edges to node1
            for succ in list(self.graph.successors(node2)):
                if succ != node1:  # Avoid self-loops
                    edge_weight = self.graph.edges[node2, succ].get("weight", 1.0)
                    self.graph.add_edge(node1, succ, weight=edge_weight)
            
            # Remove node2
            self.graph.remove_node(node2)
            
            # Mark node2 as merged
            merged_nodes.add(node2)
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """
        Calculate similarity between two text strings.
        
        Args:
            text1: First text
            text2: Second text
            
        Returns:
            Similarity score between 0 and 1
        """
        # Simple Jaccard similarity on word sets
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        if union == 0:
            return 0
            
        return intersection / union
    
    def _handle_cycles(self) -> None:
        """
        Detect and handle cycles in the graph.
        """
        # Find cycles
        try:
            cycles = list(nx.simple_cycles(self.graph))
            for cycle in cycles:
                if len(cycle) > 1:
                    # Find edge with lowest weight and remove it
                    min_weight = float('inf')
                    min_edge = None
                    
                    for i in range(len(cycle)):
                        u = cycle[i]
                        v = cycle[(i+1) % len(cycle)]
                        if self.graph.has_edge(u, v):
                            weight = self.graph.edges[u, v].get("weight", 1.0)
                            if weight < min_weight:
                                min_weight = weight
                                min_edge = (u, v)
                    
                    if min_edge:
                        self.graph.remove_edge(*min_edge)
                        logger.info(f"Removed edge {min_edge} to break cycle: {cycle}")
        except Exception as e:
            logger.error(f"Error handling cycles: {str(e)}")
    
    def _adapt_exploration_strategy(self, current_depth: int) -> None:
        """
        Adapt exploration strategy based on current progress.
        
        Args:
            current_depth: Current depth in the graph
        """
        # Adjust max_branches based on performance at current depth
        nodes_at_depth = [
            node for node, data in self.graph.nodes(data=True)
            if data.get("depth") == current_depth and data.get("type") == "thought"
        ]
        
        # Calculate average score at current depth
        scores = [self.graph.nodes[node].get("score", 0) for node in nodes_at_depth]
        avg_score = sum(scores) / len(scores) if scores else 0
        
        # Adjust max_branches based on average score
        if avg_score > 0.8:
            # If we're doing well, reduce branching (focus on promising paths)
            self.max_branches = max(2, self.max_branches - 1)
        elif avg_score < 0.4:
            # If we're doing poorly, increase branching (explore more)
            self.max_branches = min(6, self.max_branches + 1)
    
    def _find_best_paths(self) -> List[List[str]]:
        """
        Find the best paths through the graph.
        
        Returns:
            List of best paths (each path is a list of node IDs)
        """
        # Get leaf nodes (nodes with no successors)
        leaf_nodes = [
            node for node in self.graph.nodes()
            if self.graph.out_degree(node) == 0 and self.graph.nodes[node].get("type") == "thought"
        ]
        
        if not leaf_nodes:
            # If no leaf nodes, use nodes at the maximum depth
            max_depth = max(
                (data.get("depth", 0) for _, data in self.graph.nodes(data=True)),
                default=0
            )
            leaf_nodes = [
                node for node, data in self.graph.nodes(data=True)
                if data.get("depth") == max_depth and data.get("type") == "thought"
            ]
        
        # Sort leaf nodes by score
        leaf_nodes = sorted(
            leaf_nodes,
            key=lambda node: self.graph.nodes[node].get("score", 0),
            reverse=True
        )
        
        # Get top K leaf nodes
        top_k = min(3, len(leaf_nodes))
        top_leafs = leaf_nodes[:top_k]
        
        # Find paths from root to each top leaf
        paths = []
        for leaf in top_leafs:
            # Find all simple paths from root to this leaf
            root_id = "root"
            try:
                leaf_paths = list(nx.all_simple_paths(self.graph, root_id, leaf))
                if leaf_paths:
                    # Calculate path score (average of node scores)
                    path_scores = []
                    for path in leaf_paths:
                        path_score = sum(self.graph.nodes[node].get("score", 0) for node in path) / len(path)
                        path_scores.append((path, path_score))
                    
                    # Add best path to results
                    best_path = max(path_scores, key=lambda x: x[1])[0]
                    paths.append(best_path)
            except Exception as e:
                logger.error(f"Error finding paths to leaf {leaf}: {str(e)}")
        
        return paths
    
    def _generate_answer_from_paths(
        self,
        query: str,
        paths: List[List[str]],
        context: Optional[str] = None
    ) -> str:
        """
        Generate a final answer from the best paths.
        
        Args:
            query: Original query
            paths: List of best paths
            context: Optional context
            
        Returns:
            Generated answer
        """
        try:
            # Extract reasoning from paths
            reasoning_chains = []
            for path in paths:
                chain = []
                for node_id in path:
                    node_content = self.graph.nodes[node_id]["content"]
                    node_type = self.graph.nodes[node_id].get("type", "")
                    if node_type == "thought":
                        chain.append(node_content)
                reasoning_chains.append(chain)
            
            # Create the prompt for generating the final answer
            if self.language == "vi":
                system_prompt = """Bạn là một trợ lý AI thông minh tổng hợp câu trả lời từ các chuỗi suy luận.
                Dựa trên các chuỗi suy luận được cung cấp, hãy tạo ra một câu trả lời toàn diện và mạch lạc.
                Câu trả lời của bạn nên tích hợp những điểm mạnh nhất từ mỗi chuỗi suy luận.
                Hãy tập trung vào độ chính xác, rõ ràng và hữu ích của câu trả lời.
                
                Đừng nhắc lại quá trình suy luận, chỉ cung cấp câu trả lời cuối cùng."""
            else:
                system_prompt = """You are an intelligent AI assistant synthesizing an answer from reasoning chains.
                Based on the provided reasoning chains, create a comprehensive and coherent answer.
                Your answer should integrate the strongest points from each reasoning chain.
                Focus on accuracy, clarity, and helpfulness in your response.
                
                Do not recap the reasoning process, just provide the final answer."""
            
            # Format reasoning chains
            reasoning_text = ""
            for i, chain in enumerate(reasoning_chains):
                reasoning_text += f"\nReasoning Chain {i+1}:\n"
                for j, thought in enumerate(chain):
                    if j == 0 and thought == query:
                        continue  # Skip the query node
                    reasoning_text += f"- {thought}\n"
            
            answer_prompt = f"Question: {query}\n{reasoning_text}"
            if context:
                answer_prompt += f"\n\nContext: {context}"
            
            # Generate final answer
            answer = self.api_provider.generate(
                prompt=answer_prompt,
                system_prompt=system_prompt,
                model=self.model,
                temperature=0.5,  # Moderate temperature for balanced output
                max_tokens=self.max_tokens
            )
            
            return answer
            
        except Exception as e:
            logger.error(f"Error generating answer from paths: {str(e)}")
            return "I couldn't generate a complete answer due to an error in the reasoning process."
    
    def _prepare_visualization_data(self) -> Dict[str, Any]:
        """
        Prepare visualization data for the thought graph.
        
        Returns:
            Dictionary with visualization data
        """
        # Create nodes list
        nodes = []
        for node_id, data in self.graph.nodes(data=True):
            node_type = data.get("type", "unknown")
            node_depth = data.get("depth", 0)
            node_score = data.get("score", 0)
            
            # Truncate content for visualization
            content = data.get("content", "")
            if len(content) > 100:
                content = content[:97] + "..."
            
            nodes.append({
                "id": node_id,
                "label": content,
                "type": node_type,
                "depth": node_depth,
                "score": node_score
            })
        
        # Create edges list
        edges = []
        for u, v, data in self.graph.edges(data=True):
            edges.append({
                "from": u,
                "to": v,
                "weight": data.get("weight", 1.0)
            })
        
        return {
            "nodes": nodes,
            "edges": edges
        } 