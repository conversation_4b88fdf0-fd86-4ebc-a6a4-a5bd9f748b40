"""
Base class for Graph of Thoughts (GoT) reasoning.

This module provides the base class for implementing Graph of Thoughts,
which extends Tree of Thought by supporting more complex graph structures.
"""

from typing import Dict, Any, List, Optional, Callable, Tuple, Set, Union
from abc import ABC, abstractmethod

class GoTBase(ABC):
    """
    Base class for Graph of Thoughts implementations.
    
    This class defines the common interface for all Graph of Thoughts
    implementations, ensuring compatibility across different variations.
    """
    
    @abstractmethod
    def reason(
        self,
        query: str,
        context: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Perform Graph of Thoughts reasoning on the given query.
        
        Args:
            query: The query to reason about
            context: Optional context to include
            callback: Optional callback function for streaming responses
            **kwargs: Additional implementation-specific arguments
            
        Returns:
            Dictionary containing the reasoning result and additional information
        """
        pass
    
    @abstractmethod
    def _generate_initial_thoughts(
        self,
        query: str,
        context: Optional[str] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Generate initial thoughts for the given query.
        
        Args:
            query: The query to reason about
            context: Optional context to include
            **kwargs: Additional implementation-specific arguments
            
        Returns:
            List of initial thoughts
        """
        pass
    
    @abstractmethod
    def _evaluate_thoughts(
        self,
        thoughts: List[Dict[str, Any]],
        query: str,
        context: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Evaluate and score the thoughts.
        
        Args:
            thoughts: List of thoughts to evaluate
            query: The original query
            context: Optional context
            
        Returns:
            List of evaluated thoughts with scores
        """
        pass
    
    @abstractmethod
    def _find_best_paths(self) -> List[List[str]]:
        """
        Find the best paths through the graph.
        
        Returns:
            List of best paths (each path is a list of node IDs)
        """
        pass
    
    @abstractmethod
    def _generate_answer_from_paths(
        self,
        query: str,
        paths: List[List[str]],
        context: Optional[str] = None
    ) -> str:
        """
        Generate a final answer from the best paths.
        
        Args:
            query: Original query
            paths: List of best paths
            context: Optional context
            
        Returns:
            Generated answer
        """
        pass 