# Graph of Thoughts (GoT) Reasoning Module

## Overview

The Graph of Thoughts (GoT) reasoning module is an extension of the Tree of Thought (ToT) approach that allows for more complex and flexible thought structures. While ToT is limited to tree-like structures where each node has a single parent, GoT enables:

- **Bidirectional connections** between thoughts
- **Cross-connections** between branches
- **Cyclic reasoning paths**
- **Merging of thought branches**

This approach enables more sophisticated reasoning patterns that better mimic human thinking processes.

## Key Features

- **Graph-based reasoning:** Represents thoughts as nodes in a directed graph with weighted edges
- **Thought fusion:** Merges similar thoughts to eliminate redundancy
- **Cycle detection:** Identifies and handles cyclic reasoning paths
- **Adaptive exploration:** Dynamically adjusts the exploration strategy based on current performance
- **Vietnamese language support:** Includes built-in support for both English and Vietnamese

## Usage Example

```python
from deep_research_core.reasoning import GraphOfThoughtsReasoner

# Initialize the reasoner
reasoner = GraphOfThoughtsReasoner(
    provider="openai",  # Can be "openai", "anthropic", or "openrouter"
    model="gpt-4o",     # Optional: will use provider's default if not specified
    max_depth=3,        # Maximum depth of the graph
    max_branches=3,     # Maximum branches per node
    thought_fusion=True,# Whether to merge similar thoughts
    language="en"       # Language to use (en, vi)
)

# Perform reasoning
result = reasoner.reason(
    query="What are the potential impacts of quantum computing on cryptography?",
    context="Consider both short-term and long-term implications."
)

# Get the final answer
answer = result["answer"]

# Access the reasoning graph
graph = result["graph"]

# Visualize the reasoning process
visualization_data = result["visualization_data"]
```

## Integration with Other Reasoning Approaches

The Graph of Thoughts reasoner can be combined with other reasoning techniques:

- **RAG:** Augment GoT with retrieved information from various sources
- **Agent capabilities:** Integrate with tools and external APIs
- **Multi-stage reasoning:** Use GoT as one stage in a multi-stage reasoning pipeline

## Performance Considerations

- Graph exploration can be computationally intensive for complex queries
- The module includes adaptive parameters to balance exploration with efficiency
- For very complex problems, consider adjusting `max_depth`, `max_branches`, and `max_nodes` parameters

## Extending the Module

To extend the Graph of Thoughts module with custom functionality:

1. Subclass `GraphOfThoughtsReasoner` or `GoTBase`
2. Override the specific methods you want to customize
3. Maintain the same interface for compatibility with the rest of the system 