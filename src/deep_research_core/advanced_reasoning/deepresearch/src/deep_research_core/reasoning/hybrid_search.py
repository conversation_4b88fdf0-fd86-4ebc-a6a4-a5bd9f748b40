"""
Hybrid Search for Deep Research Core.

This module provides a hybrid search implementation that combines semantic and keyword search.
"""

import re
from functools import lru_cache
from typing import Dict, List, Any, Optional, Union, Tuple

import numpy as np

from ..models.api.openai import openai_provider
from ..models.api.anthropic import anthropic_provider
from ..models.api.openrouter import openrouter_provider

from ..utils.structured_logging import get_logger
from ..utils.performance_metrics import measure_latency
from ..utils.distributed_tracing import trace_function, span

# Create a logger
logger = get_logger(__name__)


class HybridSearch:
    """
    Hybrid Search.

    This class provides a hybrid search implementation that combines semantic and keyword search.
    """

    def __init__(self, use_cache: bool = True, cache_size: int = 100, provider: str = "openai",
        model: Optional[str] = None,
        temperature: float = 0.3,
        max_tokens: int = 1000,
        semantic_weight: float = 0.7,
        keyword_weight: float = 0.3,
        **kwargs
    ):
        """
        Initialize the Hybrid Search.

        Args:
            provider: LLM provider (openai, anthropic, openrouter)
            model: LLM model name
            temperature: LLM temperature
            max_tokens: Maximum tokens for LLM response
            semantic_weight: Weight for semantic search (0-1)
            keyword_weight: Weight for keyword search (0-1)
        """
        self.provider = provider
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens

        # Normalize weights
        total_weight = semantic_weight + keyword_weight
        self.semantic_weight = semantic_weight / total_weight
        self.keyword_weight = keyword_weight / total_weight

        # Initialize LLM provider
        if provider == "openai":
            self.llm_provider = openai_provider
        elif provider == "anthropic":
            self.llm_provider = anthropic_provider
        elif provider == "openrouter":
            self.llm_provider = openrouter_provider
        else:
            raise ValueError(f"Unsupported provider: {provider}")

        # Set up caching
        if use_cache:
            self._generate_cached = lru_cache(maxsize=cache_size)(self._generate)
        else:
            self._generate_cached = self._generate

        logger.info(f"Initialized HybridSearch with provider {provider} and model {model}")

    def _generate(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        json_format: bool = False
    ) -> str:
        """
        Generate text using the model.

        Args:
            prompt: The prompt to generate from
            system_prompt: Optional system prompt
            temperature: Optional temperature override
            max_tokens: Optional max tokens override
            json_format: Whether to return JSON format

        Returns:
            Generated text
        """
        return self.llm_provider.generate(
            prompt=prompt,
            model=self.model,
            system_prompt=system_prompt,
            temperature=temperature if temperature is not None else self.temperature,
            max_tokens=max_tokens if max_tokens is not None else self.max_tokens,
            json_format=json_format
        )

    @trace_function(name="hybrid_search_search")
    @measure_latency("hybrid_search_search")
    def search(
        self,
        query: str,
        documents: List[Dict[str, Any]],
        top_k: int = 5,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Perform hybrid search with default weights.

        Args:
            query: Search query
            documents: List of document dictionaries
            top_k: Number of results to return

        Returns:
            List of document dictionaries with relevance scores
        """
        return self.advanced_search(
            query=query,
            documents=documents,
            top_k=top_k,
            semantic_weight=self.semantic_weight,
            keyword_weight=self.keyword_weight,
            **kwargs
        )

    @trace_function(name="hybrid_search_advanced_search")
    @measure_latency("hybrid_search_advanced_search")
    def advanced_search(
        self,
        query: str,
        documents: List[Dict[str, Any]],
        top_k: int = 5,
        semantic_weight: float = 0.7,
        keyword_weight: float = 0.3,
        context: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Perform hybrid search with custom weights.

        Args:
            query: Search query
            documents: List of document dictionaries
            top_k: Number of results to return
            semantic_weight: Weight for semantic search (0-1)
            keyword_weight: Weight for keyword search (0-1)
            context: Optional context information

        Returns:
            List of document dictionaries with relevance scores
        """
        # Normalize weights
        total_weight = semantic_weight + keyword_weight
        if total_weight == 0:
            # Default to equal weights if both are zero
            semantic_weight = 0.5
            keyword_weight = 0.5
            total_weight = 1.0

        normalized_semantic_weight = semantic_weight / total_weight
        normalized_keyword_weight = keyword_weight / total_weight

        # Get query embedding
        query_embedding = self._get_embedding(query)

        # Perform semantic search
        semantic_results = self._semantic_search(query_embedding, documents, top_k * 2)

        # Perform keyword search
        keyword_results = self._keyword_search(query, documents, top_k * 2)

        # Combine results with custom weights
        combined_results = self._combine_results_with_weights(
            semantic_results,
            keyword_results,
            top_k,
            normalized_semantic_weight,
            normalized_keyword_weight
        )

        return combined_results

    @trace_function(name="hybrid_search_combine_results_with_weights")
    @measure_latency("hybrid_search_combine_results_with_weights")
    def _combine_results_with_weights(
        self,
        semantic_results: List[Dict[str, Any]],
        keyword_results: List[Dict[str, Any]],
        top_k: int,
        semantic_weight: float,
        keyword_weight: float
    ) -> List[Dict[str, Any]]:
        """
        Combine semantic and keyword search results with custom weights.

        Args:
            semantic_results: List of semantic search results
            keyword_results: List of keyword search results
            top_k: Number of results to return
            semantic_weight: Weight for semantic search (0-1)
            keyword_weight: Weight for keyword search (0-1)

        Returns:
            List of combined search results
        """
        # Create a map of document IDs to results
        combined_map = {}

        # Add semantic results
        for result in semantic_results:
            doc_id = result.get("id")
            if not doc_id:
                continue

            combined_map[doc_id] = {
                "document": result,
                "semantic_score": result.get("semantic_score", 0.0),
                "keyword_score": 0.0
            }

        # Add keyword results
        for result in keyword_results:
            doc_id = result.get("id")
            if not doc_id:
                continue

            if doc_id in combined_map:
                # Update existing entry
                combined_map[doc_id]["keyword_score"] = result.get("keyword_score", 0.0)
            else:
                # Add new entry
                combined_map[doc_id] = {
                    "document": result,
                    "semantic_score": 0.0,
                    "keyword_score": result.get("keyword_score", 0.0)
                }

        # Calculate combined scores with custom weights
        combined_results = []
        for doc_id, entry in combined_map.items():
            document = entry["document"]
            semantic_score = entry["semantic_score"]
            keyword_score = entry["keyword_score"]

            # Calculate combined score
            combined_score = (
                semantic_weight * semantic_score +
                keyword_weight * keyword_score
            )

            # Add to results
            result = document.copy()
            result["semantic_score"] = semantic_score
            result["keyword_score"] = keyword_score
            result["combined_score"] = combined_score
            combined_results.append(result)

        # Sort by combined score
        combined_results.sort(key=lambda x: x["combined_score"], reverse=True)

        # Return top_k results
        return combined_results[:top_k]

    @trace_function(name="hybrid_search_get_embedding")
    @measure_latency("hybrid_search_get_embedding")
    def _get_embedding(self, text: str) -> List[float]:
        """
        Get embedding for text.

        Args:
            text: Text to get embedding for

        Returns:
            Embedding vector
        """
        try:
            if self.provider == "openai":
                embedding = openai_provider.get_embedding(text)
            elif self.provider == "anthropic":
                embedding = anthropic_provider.get_embedding(text)
            elif self.provider == "openrouter":
                embedding = openrouter_provider.get_embedding(text)
            else:
                # Default to OpenAI
                embedding = openai_provider.get_embedding(text)
            return embedding
        except Exception as e:
            logger.error(f"Error getting embedding: {str(e)}")
            # Return a random embedding as fallback
            return list(np.random.rand(1536))

    @trace_function(name="hybrid_search_semantic_search")
    @measure_latency("hybrid_search_semantic_search")
    def _semantic_search(
        self,
        query_embedding: List[float],
        documents: List[Dict[str, Any]],
        top_k: int
    ) -> List[Dict[str, Any]]:
        """
        Perform semantic search.

        Args:
            query_embedding: Query embedding vector
            documents: List of document dictionaries
            top_k: Number of results to return

        Returns:
            List of document dictionaries with semantic scores
        """
        # Calculate semantic similarity for each document
        results = []
        for doc in documents:
            # Get document embedding
            doc_embedding = doc.get("embedding")
            if not doc_embedding:
                # Skip documents without embeddings
                continue

            # Calculate cosine similarity
            similarity = self._cosine_similarity(query_embedding, doc_embedding)

            # Add to results
            result = doc.copy()
            result["semantic_score"] = similarity
            results.append(result)

        # Sort by semantic score
        results.sort(key=lambda x: x["semantic_score"], reverse=True)

        # Return top_k results
        return results[:top_k]

    @trace_function(name="hybrid_search_keyword_search")
    @measure_latency("hybrid_search_keyword_search")
    def _keyword_search(
        self,
        query: str,
        documents: List[Dict[str, Any]],
        top_k: int
    ) -> List[Dict[str, Any]]:
        """
        Perform keyword search.

        Args:
            query: Search query
            documents: List of document dictionaries
            top_k: Number of results to return

        Returns:
            List of document dictionaries with keyword scores
        """
        # Extract keywords from query
        keywords = self._extract_keywords(query)

        # Calculate keyword match score for each document
        results = []
        for doc in documents:
            # Get document content
            content = doc.get("content", "")
            if not content:
                # Skip documents without content
                continue

            # Calculate keyword match score
            score = self._calculate_keyword_score(keywords, content)

            # Add to results
            result = doc.copy()
            result["keyword_score"] = score
            results.append(result)

        # Sort by keyword score
        results.sort(key=lambda x: x["keyword_score"], reverse=True)

        # Return top_k results
        return results[:top_k]

    @trace_function(name="hybrid_search_extract_keywords")
    @measure_latency("hybrid_search_extract_keywords")
    def _extract_keywords(self, query: str) -> List[str]:
        """
        Extract keywords from query.

        Args:
            query: Search query

        Returns:
            List of keywords
        """
        # Simple keyword extraction
        # Remove punctuation and convert to lowercase
        query = re.sub(r'[^\w\s]', '', query.lower())

        # Split into words
        words = query.split()

        # Remove stopwords
        stopwords = ["a", "an", "the", "and", "or", "but", "in", "on", "at", "to", "for", "with", "by", "about", "as", "of", "is", "are", "was", "were", "be", "been", "being", "have", "has", "had", "do", "does", "did", "will", "would", "shall", "should", "can", "could", "may", "might", "must", "this", "that", "these", "those", "i", "you", "he", "she", "it", "we", "they", "me", "him", "her", "us", "them", "my", "your", "his", "its", "our", "their", "mine", "yours", "hers", "ours", "theirs"]
        keywords = [word for word in words if word not in stopwords]

        return keywords

    @trace_function(name="hybrid_search_calculate_keyword_score")
    @measure_latency("hybrid_search_calculate_keyword_score")
    def _calculate_keyword_score(self, keywords: List[str], content: str) -> float:
        """
        Calculate keyword match score.

        Args:
            keywords: List of keywords
            content: Document content

        Returns:
            Keyword match score (0-1)
        """
        if not keywords:
            return 0.0

        # Convert content to lowercase
        content = content.lower()

        # Count keyword matches
        match_count = 0
        for keyword in keywords:
            if keyword in content:
                match_count += 1

        # Calculate score
        score = match_count / len(keywords)

        return score

    @trace_function(name="hybrid_search_combine_results")
    @measure_latency("hybrid_search_combine_results")
    def _combine_results(
        self,
        semantic_results: List[Dict[str, Any]],
        keyword_results: List[Dict[str, Any]],
        top_k: int
    ) -> List[Dict[str, Any]]:
        """
        Combine semantic and keyword search results.

        Args:
            semantic_results: List of semantic search results
            keyword_results: List of keyword search results
            top_k: Number of results to return

        Returns:
            List of combined search results
        """
        # Create a map of document IDs to results
        combined_map = {}

        # Add semantic results
        for result in semantic_results:
            doc_id = result.get("id")
            if not doc_id:
                continue

            combined_map[doc_id] = {
                "document": result,
                "semantic_score": result.get("semantic_score", 0.0),
                "keyword_score": 0.0
            }

        # Add keyword results
        for result in keyword_results:
            doc_id = result.get("id")
            if not doc_id:
                continue

            if doc_id in combined_map:
                # Update existing entry
                combined_map[doc_id]["keyword_score"] = result.get("keyword_score", 0.0)
            else:
                # Add new entry
                combined_map[doc_id] = {
                    "document": result,
                    "semantic_score": 0.0,
                    "keyword_score": result.get("keyword_score", 0.0)
                }

        # Calculate combined scores
        combined_results = []
        for doc_id, entry in combined_map.items():
            document = entry["document"]
            semantic_score = entry["semantic_score"]
            keyword_score = entry["keyword_score"]

            # Calculate combined score
            combined_score = (
                self.semantic_weight * semantic_score +
                self.keyword_weight * keyword_score
            )

            # Add to results
            result = document.copy()
            result["semantic_score"] = semantic_score
            result["keyword_score"] = keyword_score
            result["combined_score"] = combined_score
            combined_results.append(result)

        # Sort by combined score
        combined_results.sort(key=lambda x: x["combined_score"], reverse=True)

        # Return top_k results
        return combined_results[:top_k]

    @trace_function(name="hybrid_search_cosine_similarity")
    @measure_latency("hybrid_search_cosine_similarity")
    def _cosine_similarity(self, a: List[float], b: List[float]) -> float:
        """
        Calculate cosine similarity between two vectors.

        Args:
            a: First vector
            b: Second vector

        Returns:
            Cosine similarity
        """
        a = np.array(a)
        b = np.array(b)
        return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))
