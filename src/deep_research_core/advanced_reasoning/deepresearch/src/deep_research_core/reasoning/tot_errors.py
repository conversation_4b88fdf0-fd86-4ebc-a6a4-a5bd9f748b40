"""
Error classes for Tree of Thought (ToT) reasoning.
"""

class TOTError(Exception):
    """Base exception for Tree of Thought errors."""
    pass

class ModelAPIError(TOTError):
    """Exception raised when there's an error calling the model API."""
    pass

class PathGenerationError(TOTError):
    """Exception raised when there's an error generating paths."""
    pass

class PathEvaluationError(TOTError):
    """Exception raised when there's an error evaluating paths."""
    pass
