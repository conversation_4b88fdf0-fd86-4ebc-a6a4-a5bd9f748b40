"""
Knowledge Graph Reasoner for Deep Research Core.

This module provides a reasoner that uses knowledge graphs for reasoning.
"""

import json
from functools import lru_cache
from typing import Dict, List, Any, Optional, Union

from .base import BaseReasoner
from .knowledge_graph_integration import KnowledgeGraphIntegration
from ..models.api.openai import openai_provider
from ..models.api.anthropic import anthropic_provider
from ..models.api.openrouter import openrouter_provider

from ..utils.structured_logging import get_logger
from ..utils.performance_metrics import measure_latency
from ..utils.distributed_tracing import trace_function, span

# Create a logger
logger = get_logger(__name__)


class KnowledgeGraphReasoner(BaseReasoner):
    """
    Knowledge Graph Reasoner.

    This class provides a reasoner that uses knowledge graphs for reasoning.
    """

    def __init__(self, use_cache: bool = True, cache_size: int = 100, provider: str = "openai",
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        url: str = "http://localhost:8080",
        api_key: Optional[str] = None,
        entity_class_name: str = "Entity",
        relation_class_name: str = "Relation",
        create_schema: bool = True,
        max_depth: int = 2,
        max_entities: int = 10,
        language: str = "en",
        **kwargs
    ):
        """
        Initialize the Knowledge Graph Reasoner.

        Args:
            provider: LLM provider (openai, anthropic, openrouter)
            model: LLM model name
            temperature: LLM temperature
            max_tokens: Maximum tokens for LLM response
            url: Weaviate server URL
            api_key: Weaviate API key
            entity_class_name: Name of the entity class
            relation_class_name: Name of the relation class
            create_schema: Whether to create the schema if it doesn't exist
            max_depth: Maximum depth for graph traversal
            max_entities: Maximum number of entities to include in reasoning
        """
        super().__init__()
        self.provider = provider
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.max_depth = max_depth
        self.max_entities = max_entities
        self.language = language

        # Initialize knowledge graph
        try:
            self.knowledge_graph = KnowledgeGraphIntegration(
                url=url,
                api_key=api_key,
                entity_class_name=entity_class_name,
                relation_class_name=relation_class_name,
                create_schema=create_schema
            )
        except Exception as e:
            logger.warning(f"Failed to initialize Knowledge Graph: {str(e)}")
            logger.warning("Using dummy Knowledge Graph as fallback")
            from ..retrieval.dummy_knowledge_graph import DummyKnowledgeGraph
            self.knowledge_graph = DummyKnowledgeGraph()

        # Initialize LLM provider
        if provider == "openai":
            self.llm_provider = openai_provider
        elif provider == "anthropic":
            self.llm_provider = anthropic_provider
        elif provider == "openrouter":
            self.llm_provider = openrouter_provider
        else:
            raise ValueError(f"Unsupported provider: {provider}")

        # Set up caching
        if use_cache:
            self._generate_cached = lru_cache(maxsize=cache_size)(self._generate)
        else:
            self._generate_cached = self._generate

        logger.info(f"Initialized KnowledgeGraphReasoner with provider {provider} and model {model}")

    def _generate(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        json_format: bool = False
    ) -> str:
        """
        Generate text using the model.

        Args:
            prompt: The prompt to generate from
            system_prompt: Optional system prompt
            temperature: Optional temperature override
            max_tokens: Optional max tokens override
            json_format: Whether to return JSON format

        Returns:
            Generated text
        """
        return self.llm_provider.generate(
            prompt=prompt,
            model=self.model,
            system_prompt=system_prompt,
            temperature=temperature if temperature is not None else self.temperature,
            max_tokens=max_tokens if max_tokens is not None else self.max_tokens,
            json_format=json_format
        )

    @trace_function(name="kg_reasoner_set_knowledge_graph")
    @measure_latency("kg_reasoner_set_knowledge_graph")
    def set_knowledge_graph(
        self,
        graph: Dict[str, Any]
    ) -> None:
        """
        Set the knowledge graph for reasoning.

        Args:
            graph: Knowledge graph dictionary with 'nodes' and 'edges' keys
        """
        if not isinstance(graph, dict):
            raise ValueError("Graph must be a dictionary")

        if "nodes" not in graph or "edges" not in graph:
            raise ValueError("Graph must contain 'nodes' and 'edges' keys")

        # Clear existing knowledge graph
        self.knowledge_graph.clear()

        # Add nodes as entities
        for node in graph.get("nodes", []):
            if not isinstance(node, dict):
                logger.warning(f"Skipping invalid node: {node}")
                continue

            node_id = node.get("id")
            if not node_id:
                logger.warning(f"Skipping node without ID: {node}")
                continue

            # Extract node properties
            name = node.get("name", node_id)
            entity_type = node.get("type", "Unknown")
            description = node.get("description", "")

            # Get remaining properties
            properties = {k: v for k, v in node.items()
                         if k not in ["id", "name", "type", "description"]}

            # Add entity to knowledge graph
            self.knowledge_graph.add_entity(
                name=name,
                entity_type=entity_type,
                description=description,
                properties=properties,
                entity_id=node_id
            )

        # Add edges as relations
        for edge in graph.get("edges", []):
            if not isinstance(edge, dict):
                logger.warning(f"Skipping invalid edge: {edge}")
                continue

            source_id = edge.get("source")
            target_id = edge.get("target")
            relation_type = edge.get("relation", "RELATED_TO")

            if not source_id or not target_id:
                logger.warning(f"Skipping edge without source or target: {edge}")
                continue

            # Extract edge properties
            weight = edge.get("weight", 1.0)
            properties = {k: v for k, v in edge.items()
                         if k not in ["source", "target", "relation", "weight"]}

            # Add relation to knowledge graph
            self.knowledge_graph.add_relation(
                source_id=source_id,
                target_id=target_id,
                relation_type=relation_type,
                weight=weight,
                properties=properties
            )

        logger.info(f"Set knowledge graph with {len(graph.get('nodes', []))} nodes and {len(graph.get('edges', []))} edges")

    @trace_function(name="kg_reasoner_find_path")
    @measure_latency("kg_reasoner_find_path")
    def find_path(
        self,
        start_node: str,
        end_node: str,
        max_depth: int = 3
    ) -> Dict[str, Any]:
        """
        Find a path between two nodes in the knowledge graph.

        Args:
            start_node: ID of the start node
            end_node: ID of the end node
            max_depth: Maximum path depth

        Returns:
            Dictionary with path information
        """
        # Initialize path
        path = {
            "start_node": start_node,
            "end_node": end_node,
            "path_found": False,
            "path": [],
            "path_length": 0
        }

        # Check if nodes exist
        start_entity = self.knowledge_graph.get_entity(start_node)
        end_entity = self.knowledge_graph.get_entity(end_node)

        if not start_entity:
            path["error"] = f"Start node {start_node} not found"
            return path

        if not end_entity:
            path["error"] = f"End node {end_node} not found"
            return path

        # Perform breadth-first search
        visited = set()
        queue = [(start_node, [])]

        while queue:
            current_node, current_path = queue.pop(0)

            # Skip if already visited
            if current_node in visited:
                continue

            # Mark as visited
            visited.add(current_node)

            # Check if we reached the end node
            if current_node == end_node:
                path["path_found"] = True
                path["path"] = current_path + [current_node]
                path["path_length"] = len(path["path"]) - 1
                break

            # Check if we reached max depth
            if len(current_path) >= max_depth:
                continue

            # Get connected entities
            relations = self.knowledge_graph.get_entity_relations(
                current_node,
                direction="outgoing"
            )

            # Add connected entities to queue
            for relation in relations:
                target_id = relation["target"]
                if target_id not in visited:
                    queue.append((target_id, current_path + [current_node]))

        return path

    @trace_function(name="kg_reasoner_extract_entities")
    @measure_latency("kg_reasoner_extract_entities")
    def extract_entities(
        self,
        text: str
    ) -> List[Dict[str, Any]]:
        """
        Extract entities from text.

        Args:
            text: Text to extract entities from

        Returns:
            List of extracted entities
        """
        prompt = f"""
        Extract entities from the following text. For each entity, provide:
        1. Name
        2. Type (Person, Organization, Location, Concept, etc.)
        3. Description
        4. Properties (key-value pairs)
        5. Relations to other entities (if any)

        Format the output as a JSON array of entity objects.

        Text:
        {text}
        """

        try:
            # Generate entity extraction
            extraction_result = self.llm_provider.generate(
                prompt=prompt,
                model=self.model,
                temperature=0.2,
                max_tokens=2000
            )

            # Parse the result
            try:
                entities = json.loads(extraction_result)
                return entities
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse entity extraction result: {extraction_result}")
                # Try to extract the result from the response using a simple heuristic
                if "[" in extraction_result and "]" in extraction_result:
                    result_str = extraction_result[extraction_result.find("["):extraction_result.rfind("]")+1]
                    try:
                        entities = json.loads(result_str)
                        return entities
                    except:
                        pass
                return []

        except Exception as e:
            logger.error(f"Error extracting entities: {str(e)}")
            return []

    @trace_function(name="kg_reasoner_reason")
    @measure_latency("kg_reasoner_reason")
    def reason(
        self,
        query: str,
        context: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Reason about a query using the knowledge graph.

        Args:
            query: Query to reason about
            context: Optional additional context

        Returns:
            Dictionary with reasoning results
        """
        # Step 1: Extract key concepts from the query
        concepts = self._extract_concepts(query)

        # Step 2: Retrieve relevant entities from the knowledge graph
        entities = self._retrieve_entities(concepts)

        # Step 3: Build a subgraph for reasoning
        subgraph = self._build_subgraph(entities)

        # Step 4: Generate reasoning based on the subgraph
        reasoning = self._generate_reasoning(query, subgraph, context)

        # Step 5: Prepare result
        result = {
            "query": query,
            "concepts": concepts,
            "entities": entities,
            "subgraph": subgraph,
            "reasoning": reasoning
        }

        return result

    @trace_function(name="kg_reasoner_extract_concepts")
    @measure_latency("kg_reasoner_extract_concepts")
    def _extract_concepts(self, query: str) -> List[str]:
        """
        Extract key concepts from the query.

        Args:
            query: Query to extract concepts from

        Returns:
            List of key concepts
        """
        prompt = f"""
        Extract the key concepts from the following query. These concepts will be used to search a knowledge graph.
        Return a JSON array of strings representing the key concepts.

        Query: {query}
        """

        try:
            response = self.llm_provider.generate(
                prompt=prompt,
                model=self.model,
                temperature=0.3,
                max_tokens=200
            )

            # Parse the response
            try:
                concepts = json.loads(response)
                if isinstance(concepts, list):
                    return concepts
                else:
                    logger.warning(f"Invalid concepts format: {response}")
                    return [query]  # Fallback to using the whole query
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse concepts: {response}")
                # Try to extract concepts from the response using a simple heuristic
                if "[" in response and "]" in response:
                    concepts_str = response[response.find("["):response.rfind("]")+1]
                    try:
                        concepts = json.loads(concepts_str)
                        if isinstance(concepts, list):
                            return concepts
                    except:
                        pass
                return [query]  # Fallback to using the whole query

        except Exception as e:
            logger.error(f"Error extracting concepts: {str(e)}")
            return [query]  # Fallback to using the whole query

    @trace_function(name="kg_reasoner_retrieve_entities")
    @measure_latency("kg_reasoner_retrieve_entities")
    def _retrieve_entities(self, concepts: List[str]) -> List[Dict[str, Any]]:
        """
        Retrieve relevant entities from the knowledge graph.

        Args:
            concepts: List of key concepts

        Returns:
            List of entity dictionaries
        """
        all_entities = []
        for concept in concepts:
            # Get embedding for the concept
            try:
                if self.provider == "openai":
                    embedding = openai_provider.get_embedding(concept)
                elif self.provider == "anthropic":
                    embedding = anthropic_provider.get_embedding(concept)
                elif self.provider == "openrouter":
                    embedding = openrouter_provider.get_embedding(concept)
                else:
                    # Default to OpenAI
                    embedding = openai_provider.get_embedding(concept)
            except Exception as e:
                logger.error(f"Error getting embedding for concept '{concept}': {str(e)}")
                continue

            # Search for entities
            entities = self.knowledge_graph.search_entities(embedding, top_k=5)
            all_entities.extend(entities)

        # Remove duplicates
        unique_entities = {}
        for entity in all_entities:
            if entity["id"] not in unique_entities:
                unique_entities[entity["id"]] = entity

        # Limit the number of entities
        entities_list = list(unique_entities.values())
        if len(entities_list) > self.max_entities:
            entities_list = entities_list[:self.max_entities]

        return entities_list

    @trace_function(name="kg_reasoner_build_subgraph")
    @measure_latency("kg_reasoner_build_subgraph")
    def _build_subgraph(self, entities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Build a subgraph for reasoning.

        Args:
            entities: List of entity dictionaries

        Returns:
            Subgraph dictionary
        """
        # Initialize subgraph
        subgraph = {
            "entities": entities,
            "relations": []
        }

        # Get relations between entities
        entity_ids = [entity["id"] for entity in entities]
        for entity_id in entity_ids:
            # Get connected entities
            connected_entities = self.knowledge_graph.get_connected_entities(
                entity_id,
                max_depth=self.max_depth
            )

            # Add new entities to the subgraph
            for entity in connected_entities:
                if entity["id"] not in entity_ids:
                    entity_ids.append(entity["id"])
                    subgraph["entities"].append(entity)

            # Get relations
            relations = self.knowledge_graph.get_entity_relations(
                entity_id,
                direction="both"
            )
            subgraph["relations"].extend(relations)

        # Remove duplicate relations
        unique_relations = {}
        for relation in subgraph["relations"]:
            relation_key = f"{relation['source']}-{relation['type']}-{relation['target']}"
            if relation_key not in unique_relations:
                unique_relations[relation_key] = relation

        subgraph["relations"] = list(unique_relations.values())

        return subgraph

    @trace_function(name="kg_reasoner_generate_reasoning")
    @measure_latency("kg_reasoner_generate_reasoning")
    def _generate_reasoning(
        self,
        query: str,
        subgraph: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Generate reasoning based on the subgraph.

        Args:
            query: Query to reason about
            subgraph: Subgraph for reasoning
            context: Optional additional context

        Returns:
            Reasoning text
        """
        # Format entities
        entities_text = ""
        for entity in subgraph["entities"]:
            properties_str = ", ".join([
                f"{k}: {v}" for k, v in entity.get("properties", {}).items()
            ])
            description = entity.get('description', '')
            entities_text += f"- {entity['name']} ({entity['type']}): {description} {properties_str}\n"

        # Format relations
        relations_text = ""
        entity_map = {entity["id"]: entity for entity in subgraph["entities"]}
        for relation in subgraph["relations"]:
            source = entity_map.get(relation["source"], {}).get("name", relation["source"])
            target = entity_map.get(relation["target"], {}).get("name", relation["target"])
            properties_str = ", ".join([
                f"{k}: {v}" for k, v in relation.get("properties", {}).items()
            ])
            relation_str = f"- {source} {relation['type']} {target}"
            if 'weight' in relation:
                relation_str += f" (Weight: {relation['weight']})"
            if properties_str:
                relation_str += f" {properties_str}"
            relations_text += relation_str + "\n"

        # Prepare prompt based on language
        if self.language == "vi":
            prompt = f"""
            Tôi cần lý luận về truy vấn sau đây bằng cách sử dụng đồ thị tri thức.

            Truy vấn: {query}

            Thông tin đồ thị tri thức:

            Thực thể:
            {entities_text}

            Quan hệ:
            {relations_text}
            """
        else:
            prompt = f"""
            I need to reason about the following query using a knowledge graph.

            Query: {query}

            Knowledge Graph Information:

            Entities:
            {entities_text}

            Relations:
            {relations_text}
            """

        if context:
            # Format context dictionary
            context_str = "\n".join([f"{k}: {v}" for k, v in context.items()]) if isinstance(context, dict) else str(context)
            prompt += f"\nAdditional Context:\n{context_str}\n"

        if self.language == "vi":
            prompt += """
            Vui lòng cung cấp lý luận chi tiết dựa trên thông tin đồ thị tri thức.
            Xem xét mối quan hệ giữa các thực thể và cách chúng liên quan đến truy vấn.
            Nếu đồ thị tri thức không chứa đủ thông tin để trả lời truy vấn, hãy giải thích những gì còn thiếu.
            """
        else:
            prompt += """
            Please provide a detailed reasoning based on the knowledge graph information.
            Consider the relationships between entities and how they relate to the query.
            If the knowledge graph doesn't contain enough information to answer the query, explain what's missing.
            """

        try:
            reasoning = self.llm_provider.generate(
                prompt=prompt,
                model=self.model,
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            return reasoning.strip()
        except Exception as e:
            logger.error(f"Error generating reasoning: {str(e)}")
            return f"Error generating reasoning: {str(e)}"
