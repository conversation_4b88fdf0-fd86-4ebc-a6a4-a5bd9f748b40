"""
RAG implementation using <PERSON><PERSON><PERSON><PERSON> as the vector store.
"""

import time
import numpy as np
from functools import lru_cache
from typing import List, Dict, Any, Optional, Callable

from .base_rag import BaseRAG
from ..retrieval.vector_store.milvus_vector_store import MilvusVectorStore
from ..models.api.openai import openai_provider
from ..models.api.anthropic import anthropic_provider
from ..models.api.openrouter import openrouter_provider

from ..utils.structured_logging import get_logger
from ..utils.performance_metrics import measure_latency
from ..utils.distributed_tracing import trace_function, span

# Create a structured logger
logger = get_logger(__name__)

class MilvusRAG(BaseRAG):
    """
    Retrieval-Augmented Generation using Milvus as the vector store.
    """

    def __init__(self, use_cache: bool = True, cache_size: int = 100, provider: str = "openai",
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        embedding_model: str = "text-embedding-ada-002",
        collection_name: str = "rag_documents",
        connection_args: Dict[str, Any] = None,
        embedding_dim: int = 1536,
        top_k: int = 5
    ):
        """
        Initialize the MilvusRAG.

        Args:
            provider: The provider to use ("openai", "anthropic", "openrouter")
            model: The model to use (if None, will use provider's default)
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            embedding_model: Model to use for embeddings
            collection_name: Name of the Milvus collection
            connection_args: Arguments for connecting to Milvus
            embedding_dim: Dimension of the embeddings
            top_k: Number of documents to retrieve
        """
        # Initialize the base class
        super().__init__(
            provider=provider,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            embedding_model=embedding_model,
            top_k=top_k
        )

        # Set up the provider
        if provider == "openai":
            self.api_provider = openai_provider
            self.model = model or "gpt-4o"
        elif provider == "anthropic":
            self.api_provider = anthropic_provider
            self.model = model or "claude-3-opus-20240229"
        elif provider == "openrouter":
            self.api_provider = openrouter_provider
            self.model = model or "openai/gpt-4o"
        else:
            raise ValueError(f"Unsupported provider: {provider}")

        # Initialize the vector store
        try:
            self.vector_store = MilvusVectorStore(
                collection_name=collection_name,
                connection_args=connection_args,
                embedding_dim=embedding_dim
            )

            # Set up caching
            if use_cache:
                self._generate_cached = lru_cache(maxsize=cache_size)(self._generate)
            else:
                self._generate_cached = self._generate

            logger.info(f"Initialized MilvusVectorStore with collection {collection_name}")
        except Exception as e:
            logger.error(f"Failed to initialize MilvusVectorStore: {e}")
            raise

    @trace_function(name="milvus_rag_add_documents")
    @measure_latency("milvus_rag_add_documents")
    def add_documents(self, documents: List[Dict[str, Any]], **kwargs) -> List[int]:
        """
        Add documents to the vector store.

        Args:
            documents: List of document dictionaries with 'content', 'source', and optional metadata
            **kwargs: Additional implementation-specific arguments

        Returns:
            List of IDs of the inserted documents
        """
        try:
            # Extract content for embedding
            contents = [doc.get("content", "") for doc in documents]

            # Generate embeddings
            with span("generate_embeddings"):
                embeddings = self._get_embeddings(contents)

            # Generate IDs if not provided
            ids = []
            for i, doc in enumerate(documents):
                if "id" not in doc:
                    doc["id"] = f"doc_{int(time.time())}_{i}"
                ids.append(doc["id"])

            # Add documents to vector store
            with span("add_to_vector_store"):
                result = self.vector_store.add(ids, embeddings, documents)

            logger.info(f"Added {len(documents)} documents to Milvus collection")
            return result
        except Exception as e:
            logger.error(f"Failed to add documents to Milvus: {str(e)}")
            raise

    # Use the implementation from BaseRAG
    def _get_embeddings(self, texts: List[str]) -> List[List[float]]:
        return super()._get_embeddings(texts)

    @trace_function(name="milvus_rag_search")
    @measure_latency("milvus_rag_search")
    def search(
        self,
        query: str,
        top_k: Optional[int] = None,
        filter_expr: str = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Search for documents relevant to the query.

        Args:
            query: The query to search for
            top_k: Number of results to return (if None, uses the default)
            filter_expr: Optional filter expression for document retrieval
            **kwargs: Additional implementation-specific arguments

        Returns:
            List of dictionaries containing the retrieved documents and their similarity scores
        """
        try:
            # Generate embedding for the query
            with span("generate_query_embedding"):
                query_embedding = self._get_embeddings([query])[0]

            # Retrieve relevant documents
            with span("search_vector_store"):
                # Convert list to numpy array if needed
                if not isinstance(query_embedding, np.ndarray):
                    query_embedding = np.array(query_embedding)

                retrieved_docs = self.vector_store.search(
                    query_embedding=query_embedding,
                    top_k=top_k or self.top_k,
                    filter_expr=filter_expr
                )

            logger.info(f"Retrieved {len(retrieved_docs)} documents from Milvus for query: {query[:50]}...")
            return retrieved_docs
        except Exception as e:
            logger.error(f"Failed to search Milvus: {str(e)}")
            raise

    @trace_function(name="milvus_rag_process")
    @measure_latency("milvus_rag_process")
    def process(
        self,
        query: str,
        filter_expr: str = None,
        callback: Callable[[str], None] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process a query using RAG.

        Args:
            query: The query to process
            filter_expr: Optional filter expression for document retrieval
            callback: Optional callback function for streaming responses
            **kwargs: Additional implementation-specific arguments

        Returns:
            Dictionary containing the answer, retrieved documents, and other information
        """
        try:
            # Search for relevant documents
            retrieved_docs = self.search(query, filter_expr=filter_expr)

            # Format documents for the prompt
            with span("format_documents"):
                formatted_docs = self._format_documents(retrieved_docs)

            # Create the prompt
            with span("create_prompts"):
                system_prompt = self._create_system_prompt()
                user_prompt = self._create_user_prompt(query, formatted_docs)

            # Generate the answer
            with span("generate_answer"):
                response = self.api_provider.complete(
                    system_prompt=system_prompt,
                    user_prompt=user_prompt,
                    model=self.model,
                    temperature=self.temperature,
                    max_tokens=self.max_tokens,
                    stream=callback is not None,
                    callback=callback
                )

            # Return the result
            return {
                "query": query,
                "answer": response,
                "documents": retrieved_docs,
                "system_prompt": system_prompt,
                "user_prompt": user_prompt,
                "model": self.model,
                "provider": self.provider
            }
        except Exception as e:
            logger.error(f"Failed to process query with MilvusRAG: {str(e)}")
            raise

    # Use the implementation from BaseRAG
    def _format_documents(self, documents: List[Dict[str, Any]]) -> str:
        return super()._format_documents(documents)

    # Use the implementation from BaseRAG
    def _create_system_prompt(self) -> str:
        return super()._create_system_prompt()

    # Use the implementation from BaseRAG
    def _create_user_prompt(self, query: str, formatted_docs: str) -> str:
        return super()._create_user_prompt(query, formatted_docs)

    @trace_function(name="milvus_rag_clear")
    @measure_latency("milvus_rag_clear")
    def clear(self) -> None:
        """Clear all documents from the vector store."""
        try:
            self.vector_store.clear()
            logger.info("Cleared all documents from Milvus collection")
        except Exception as e:
            logger.error(f"Failed to clear Milvus collection: {str(e)}")
            raise

    @trace_function(name="milvus_rag_count")
    @measure_latency("milvus_rag_count")
    def count(self) -> int:
        """Return the number of documents in the vector store."""
        try:
            count = self.vector_store.count()
            logger.info(f"Milvus collection contains {count} documents")
            return count
        except Exception as e:
            logger.error(f"Failed to count documents in Milvus collection: {str(e)}")
            raise

    @trace_function(name="milvus_rag_close")
    @measure_latency("milvus_rag_close")
    def close(self) -> None:
        """Close the vector store connection."""
        try:
            self.vector_store.close()
            logger.info("Closed connection to Milvus")
        except Exception as e:
            logger.error(f"Failed to close Milvus connection: {str(e)}")
            raise
