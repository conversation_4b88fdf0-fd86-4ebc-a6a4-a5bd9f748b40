"""
Knowledge Graph-enhanced Retrieval-Augmented Generation (KG-RAG).

This module provides a RAG implementation enhanced with knowledge graph capabilities.
"""

from functools import lru_cache
from typing import List, Dict, Any, Optional, Callable, Union, Tuple
import json

from .base_rag import BaseRAG
from .knowledge_graph_integration import KnowledgeGraphIntegration
from ..retrieval.weaviate_vector_store import WeaviateVectorStore
from ..models.api.openai import openai_provider
from ..models.api.anthropic import anthropic_provider
from ..models.api.openrouter import openrouter_provider

from ..utils.structured_logging import get_logger
from ..utils.performance_metrics import measure_latency
from ..utils.distributed_tracing import trace_function, span

# Create a logger
logger = get_logger(__name__)


class KnowledgeGraphRAG(BaseRAG):
    """
    Knowledge Graph-enhanced Retrieval-Augmented Generation (KG-RAG).

    This class provides a RAG implementation enhanced with knowledge graph capabilities.
    It supports:
    - Adding, updating, and deleting documents
    - Processing queries with RAG
    - Filtering based on metadata
    - Advanced index configuration
    - Document and embedding retrieval by ID
    - Knowledge graph integration for enhanced reasoning
    """

    def __init__(self, use_cache: bool = True, cache_size: int = 100, provider: str = "openai",
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        embedding_model: str = "text-embedding-ada-002",
        url: str = "http://localhost:8080",
        api_key: Optional[str] = None,
        document_class_name: str = "Document",
        entity_class_name: str = "Entity",
        relation_class_name: str = "Relation",
        batch_size: int = 100,
        create_schema: bool = True,
        vector_index_type: str = "hnsw",
        vector_index_config: Optional[Dict[str, Any]] = None,
        top_k: int = 5,
        kg_weight: float = 0.3,
        use_kg_for_query_expansion: bool = True,
        use_kg_for_context_enrichment: bool = True
    ):
        """
        Initialize the Knowledge Graph-enhanced RAG.

        Args:
            provider: LLM provider (openai, anthropic, openrouter)
            model: LLM model name
            temperature: LLM temperature
            max_tokens: Maximum tokens for LLM response
            embedding_model: Embedding model name
            url: Weaviate server URL
            api_key: Weaviate API key
            document_class_name: Name of the document class
            entity_class_name: Name of the entity class
            relation_class_name: Name of the relation class
            batch_size: Batch size for document processing
            create_schema: Whether to create the schema if it doesn't exist
            vector_index_type: Vector index type
            vector_index_config: Vector index configuration
            top_k: Number of documents to retrieve
            kg_weight: Weight of knowledge graph results in final ranking
            use_kg_for_query_expansion: Whether to use knowledge graph for query expansion
            use_kg_for_context_enrichment: Whether to use knowledge graph for context enrichment
        """
        super().__init__()
        self.provider = provider
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.embedding_model = embedding_model
        self.url = url
        self.api_key = api_key
        self.document_class_name = document_class_name
        self.entity_class_name = entity_class_name
        self.relation_class_name = relation_class_name
        self.batch_size = batch_size
        self.create_schema = create_schema
        self.vector_index_type = vector_index_type
        self.vector_index_config = vector_index_config
        self.top_k = top_k
        self.kg_weight = kg_weight
        self.use_kg_for_query_expansion = use_kg_for_query_expansion
        self.use_kg_for_context_enrichment = use_kg_for_context_enrichment

        # Initialize document store
        try:
            self.document_store = WeaviateVectorStore(
                url=url,
                api_key=api_key,
                class_name=document_class_name,
                create_class=create_schema,
                vector_index_type=vector_index_type,
                vector_index_config=vector_index_config
            )
        except Exception as e:
            logger.warning(f"Failed to initialize Weaviate document store: {str(e)}")
            logger.warning("Using in-memory vector store as fallback")
            from ..retrieval.memory_vector_store import MemoryVectorStore
            self.document_store = MemoryVectorStore()

        # Initialize knowledge graph
        try:
            self.knowledge_graph = KnowledgeGraphIntegration(
                url=url,
                api_key=api_key,
                entity_class_name=entity_class_name,
                relation_class_name=relation_class_name,
                create_schema=create_schema
            )
        except Exception as e:
            logger.warning(f"Failed to initialize Knowledge Graph: {str(e)}")
            logger.warning("Using dummy Knowledge Graph as fallback")
            from ..retrieval.dummy_knowledge_graph import DummyKnowledgeGraph
            self.knowledge_graph = DummyKnowledgeGraph()

        # Initialize LLM provider
        if provider == "openai":
            self.llm_provider = openai_provider
        elif provider == "anthropic":
            self.llm_provider = anthropic_provider
        elif provider == "openrouter":
            self.llm_provider = openrouter_provider
        else:
            raise ValueError(f"Unsupported provider: {provider}")

        # Set up caching
        if use_cache:
            self._generate_cached = lru_cache(maxsize=cache_size)(self._generate)
        else:
            self._generate_cached = self._generate

        logger.info(f"Initialized KnowledgeGraphRAG with provider {provider} and model {model}")

    def _generate(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        json_format: bool = False
    ) -> str:
        """
        Generate text using the model.

        Args:
            prompt: The prompt to generate from
            system_prompt: Optional system prompt
            temperature: Optional temperature override
            max_tokens: Optional max tokens override
            json_format: Whether to return JSON format

        Returns:
            Generated text
        """
        return self.llm_provider.generate(
            prompt=prompt,
            model=self.model,
            system_prompt=system_prompt,
            temperature=temperature if temperature is not None else self.temperature,
            max_tokens=max_tokens if max_tokens is not None else self.max_tokens,
            json_format=json_format
        )

    @trace_function(name="kg_rag_add_documents")
    @measure_latency("kg_rag_add_documents")
    def add_documents(
        self,
        documents: List[Dict[str, Any]],
        embeddings: Optional[List[List[float]]] = None,
        extract_entities: bool = True
    ) -> List[str]:
        """
        Add documents to the RAG system.

        Args:
            documents: List of documents to add
            embeddings: Optional list of embeddings for the documents
            extract_entities: Whether to extract entities from the documents

        Returns:
            List of document IDs
        """
        # Add documents to the document store
        document_ids = self.document_store.add_documents(documents, embeddings)

        # Extract entities if requested
        if extract_entities:
            self._extract_entities_from_documents(documents, document_ids)

        return document_ids

    @trace_function(name="kg_rag_extract_entities")
    @measure_latency("kg_rag_extract_entities")
    def _extract_entities_from_documents(
        self,
        documents: List[Dict[str, Any]],
        document_ids: List[str]
    ) -> None:
        """
        Extract entities from documents and add them to the knowledge graph.

        Args:
            documents: List of documents
            document_ids: List of document IDs
        """
        for i, document in enumerate(documents):
            document_id = document_ids[i]

            # Extract content from document
            content = document.get("content", "")
            if not content:
                continue

            # Use LLM to extract entities
            prompt = f"""
            Extract entities from the following text. For each entity, provide:
            1. Name
            2. Type (Person, Organization, Location, Concept, etc.)
            3. Description
            4. Properties (key-value pairs)
            5. Relations to other entities (if any)

            Format the output as a JSON array of entity objects.

            Text:
            {content}
            """

            try:
                # Generate entity extraction
                extraction_result = self.llm_provider.generate(
                    prompt=prompt,
                    model=self.model,
                    temperature=0.2,
                    max_tokens=2000
                )

                # Parse the result
                try:
                    entities = json.loads(extraction_result)

                    # Add entities to the knowledge graph
                    entity_ids = []
                    for entity in entities:
                        entity_id = self.knowledge_graph.add_entity(
                            name=entity.get("name", ""),
                            entity_type=entity.get("type", "Unknown"),
                            description=entity.get("description", ""),
                            properties=entity.get("properties", {})
                        )
                        entity_ids.append(entity_id)

                        # Add relation to document
                        self.knowledge_graph.add_relation(
                            source_id=entity_id,
                            target_id=document_id,
                            relation_type="MENTIONED_IN",
                            weight=1.0,
                            properties={"document_id": document_id}
                        )

                    # Add relations between entities
                    for i, entity in enumerate(entities):
                        relations = entity.get("relations", [])
                        for relation in relations:
                            target_name = relation.get("target", "")
                            relation_type = relation.get("type", "RELATED_TO")

                            # Find target entity
                            target_index = next(
                                (j for j, e in enumerate(entities) if e.get("name") == target_name),
                                None
                            )

                            if target_index is not None:
                                # Add relation
                                self.knowledge_graph.add_relation(
                                    source_id=entity_ids[i],
                                    target_id=entity_ids[target_index],
                                    relation_type=relation_type,
                                    weight=relation.get("weight", 1.0),
                                    properties=relation.get("properties", {})
                                )

                except json.JSONDecodeError:
                    logger.error(f"Failed to parse entity extraction result: {extraction_result}")

            except Exception as e:
                logger.error(f"Failed to extract entities from document {document_id}: {str(e)}")

    @trace_function(name="kg_rag_process")
    @measure_latency("kg_rag_process")
    def process(
        self,
        query: str,
        top_k: Optional[int] = None,
        filter_expr: Optional[str] = None,
        use_kg: bool = True,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process a query using the RAG system.

        Args:
            query: Query string
            top_k: Number of documents to retrieve
            filter_expr: Filter expression for document retrieval
            use_kg: Whether to use knowledge graph enhancement

        Returns:
            Dictionary with query results
        """
        top_k = top_k or self.top_k

        # Step 1: Expand query using knowledge graph if enabled
        expanded_query = query
        if use_kg and self.use_kg_for_query_expansion:
            expanded_query = self._expand_query_with_kg(query)

        # Step 2: Get query embedding
        query_embedding = self._get_embedding(expanded_query)

        # Step 3: Retrieve documents
        documents = self.search(query, top_k, filter_expr)

        # Step 4: Retrieve entities from knowledge graph if enabled
        kg_context = ""
        if use_kg and self.use_kg_for_context_enrichment:
            kg_context = self._get_kg_context(query, query_embedding)

        # Step 5: Prepare context
        context = self._prepare_context(documents, kg_context)

        # Step 6: Generate response
        response = self._generate_response(query, context)

        # Step 7: Prepare result
        result = {
            "query": query,
            "expanded_query": expanded_query,
            "documents": documents,
            "kg_context": kg_context,
            "response": response
        }

        return result

    @trace_function(name="kg_rag_expand_query")
    @measure_latency("kg_rag_expand_query")
    def _expand_query_with_kg(self, query: str) -> str:
        """
        Expand the query using knowledge graph.

        Args:
            query: Original query

        Returns:
            Expanded query
        """
        # Get query embedding
        query_embedding = self._get_embedding(query)

        # Search for relevant entities
        entities = self.knowledge_graph.search_entities(query_embedding, top_k=3)

        # If no entities found, return original query
        if not entities:
            return query

        # Use LLM to expand the query
        entity_context = "\n".join([
            f"- {entity['name']} ({entity['type']}): {entity['description']}"
            for entity in entities
        ])

        prompt = f"""
        I want to expand a search query using related entities from a knowledge graph.

        Original query: {query}

        Related entities from knowledge graph:
        {entity_context}

        Please rewrite the query to include relevant information from these entities to make the search more effective.
        Only output the expanded query, nothing else.
        """

        try:
            expanded_query = self.llm_provider.generate(
                prompt=prompt,
                model=self.model,
                temperature=0.3,
                max_tokens=200
            )

            logger.info(f"Expanded query: {expanded_query}")
            return expanded_query.strip()

        except Exception as e:
            logger.error(f"Failed to expand query: {str(e)}")
            return query

    @trace_function(name="kg_rag_get_kg_context")
    @measure_latency("kg_rag_get_kg_context")
    def _get_kg_context(self, query: str, query_embedding: List[float]) -> str:
        """
        Get context from knowledge graph.

        Args:
            query: Query string (used for logging and context generation)
            query_embedding: Query embedding

        Returns:
            Knowledge graph context
        """
        try:
            # Log the query for debugging
            logger.debug(f"Getting knowledge graph context for query: {query}")

            # Search for relevant entities
            entities = self.knowledge_graph.search_entities(query_embedding, top_k=5)

            # If no entities found, return empty context
            if not entities:
                logger.info(f"No entities found for query: {query}")
                return ""

            # Get connected entities
            all_entities = []
            for entity in entities:
                all_entities.append(entity)
                connected = self.knowledge_graph.get_connected_entities(
                    entity["id"],
                    max_depth=1
                )
                all_entities.extend(connected)

            # Remove duplicates
            unique_entities = {}
            for entity in all_entities:
                if entity["id"] not in unique_entities:
                    unique_entities[entity["id"]] = entity

            # Get relations between entities
            relations = []
            for entity_id in unique_entities:
                entity_relations = self.knowledge_graph.get_entity_relations(
                    entity_id,
                    direction="both"
                )
                relations.extend(entity_relations)

            # Format context
            context_parts = ["Knowledge Graph Context:"]

            # Add entities
            context_parts.append("\nEntities:")
            for entity in unique_entities.values():
                properties_str = ", ".join([
                    f"{k}: {v}" for k, v in entity.get("properties", {}).items()
                ])
                description = entity.get('description', '')
                context_parts.append(
                    f"- {entity['name']} ({entity['type']}): {description} {properties_str}"
                )

            # Add relations
            if relations:
                context_parts.append("\nRelations:")
                for relation in relations:
                    source = unique_entities.get(relation["source"], {}).get("name", relation["source"])
                    target = unique_entities.get(relation["target"], {}).get("name", relation["target"])
                    properties_str = ", ".join([
                        f"{k}: {v}" for k, v in relation.get("properties", {}).items()
                    ])
                    relation_str = f"- {source} {relation['type']} {target}"
                    if 'weight' in relation:
                        relation_str += f" (Weight: {relation['weight']})"
                    if properties_str:
                        relation_str += f" {properties_str}"
                    context_parts.append(relation_str)

            return "\n".join(context_parts)

        except Exception as e:
            logger.error(f"Error getting knowledge graph context for query '{query}': {str(e)}")
            return f"Error retrieving knowledge graph context: {str(e)}"

    @trace_function(name="kg_rag_prepare_context")
    @measure_latency("kg_rag_prepare_context")
    def _prepare_context(self, documents: List[Dict[str, Any]], kg_context: str) -> str:
        """
        Prepare context for response generation.

        Args:
            documents: Retrieved documents
            kg_context: Knowledge graph context

        Returns:
            Prepared context
        """
        context_parts = []

        # Add document context
        context_parts.append("Document Context:")
        for i, doc in enumerate(documents):
            context_parts.append(f"[Document {i+1}]")
            context_parts.append(doc.get("content", ""))
            context_parts.append("")

        # Add knowledge graph context if available
        if kg_context:
            context_parts.append("Knowledge Graph Context:")
            context_parts.append(kg_context)

        return "\n".join(context_parts)

    @trace_function(name="kg_rag_generate_response")
    @measure_latency("kg_rag_generate_response")
    def _generate_response(self, query: str, context: str) -> str:
        """
        Generate response using LLM.

        Args:
            query: Query string
            context: Context for response generation

        Returns:
            Generated response
        """
        prompt = f"""
        Answer the following question based on the provided context. If the context doesn't contain the information needed to answer the question, say so.

        Context:
        {context}

        Question: {query}

        Answer:
        """

        try:
            response = self.llm_provider.generate(
                prompt=prompt,
                model=self.model,
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )

            return response.strip()

        except Exception as e:
            logger.error(f"Failed to generate response: {str(e)}")
            return f"Error generating response: {str(e)}"

    @trace_function(name="kg_rag_search")
    @measure_latency("kg_rag_search")
    def search(
        self,
        query: str,
        top_k: Optional[int] = None,
        filter_expr: Optional[str] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Search for documents relevant to the query.

        Args:
            query: The query to search for
            top_k: Number of results to return (if None, uses the default)
            filter_expr: Filter expression for document retrieval
            **kwargs: Additional implementation-specific arguments

        Returns:
            List of dictionaries containing the retrieved documents and their similarity scores
        """
        top_k = top_k or self.top_k

        # Get query embedding
        query_embedding = self._get_embedding(query)

        # Search for documents
        return self.document_store.search(query_embedding, top_k, filter_expr)

    @trace_function(name="kg_rag_get_embedding")
    @measure_latency("kg_rag_get_embedding")
    def _get_embedding(self, text: str) -> List[float]:
        """
        Get embedding for text.

        Args:
            text: Text to embed

        Returns:
            Embedding vector
        """
        if not text or not isinstance(text, str):
            logger.warning(f"Invalid text for embedding: {text}")
            text = str(text) if text else ""

        try:
            # Use the appropriate provider for embeddings based on the configured provider
            if self.provider == "openai":
                embedding = openai_provider.get_embedding(
                    text=text,
                    model=self.embedding_model
                )
            elif self.provider == "anthropic":
                embedding = anthropic_provider.get_embedding(
                    text=text,
                    model=self.embedding_model
                )
            elif self.provider == "openrouter":
                embedding = openrouter_provider.get_embedding(
                    text=text,
                    model=self.embedding_model
                )
            else:
                # Default to OpenAI
                embedding = openai_provider.get_embedding(
                    text=text,
                    model=self.embedding_model
                )

            return embedding

        except Exception as e:
            logger.error(f"Failed to get embedding for text '{text[:50]}...': {str(e)}")

            # Return random embedding as fallback
            # Import numpy here to avoid dependency issues
            import numpy as np
            # Standard embedding size for most models
            return list(np.random.rand(1536).astype(float))

    @trace_function(name="kg_rag_update_document")
    @measure_latency("kg_rag_update_document")
    def update_document(
        self,
        document_id: str,
        document: Dict[str, Any],
        embedding: Optional[List[float]] = None,
        extract_entities: bool = True
    ) -> bool:
        """
        Update a document in the RAG system.

        Args:
            document_id: Document ID
            document: Updated document
            embedding: Optional updated embedding
            extract_entities: Whether to extract entities from the document

        Returns:
            True if successful, False otherwise
        """
        # Update document in the document store
        success = self.document_store.update_document(document_id, document, embedding)

        # Extract entities if requested and update was successful
        if success and extract_entities:
            self._extract_entities_from_documents([document], [document_id])

        return success

    @trace_function(name="kg_rag_delete_document")
    @measure_latency("kg_rag_delete_document")
    def delete_document(self, document_id: str) -> bool:
        """
        Delete a document from the RAG system.

        Args:
            document_id: Document ID

        Returns:
            True if successful, False otherwise
        """
        # Delete document from the document store
        success = self.document_store.delete_document(document_id)

        # Delete relations to the document
        if success:
            # Find relations where the document is the target
            relations = self.knowledge_graph.get_entity_relations(
                document_id,
                relation_type="MENTIONED_IN",
                direction="incoming"
            )

            # Delete these relations
            for relation in relations:
                self.knowledge_graph.relation_store.delete_document(relation["id"])

        return success

    @trace_function(name="kg_rag_get_document")
    @measure_latency("kg_rag_get_document")
    def get_document(self, document_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a document by ID.

        Args:
            document_id: Document ID

        Returns:
            Document dictionary or None if not found
        """
        return self.document_store.get_document(document_id)

    @trace_function(name="kg_rag_get_documents")
    @measure_latency("kg_rag_get_documents")
    def get_documents(
        self,
        filter_expr: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Get documents matching a filter expression.

        Args:
            filter_expr: Filter expression
            limit: Maximum number of documents to return

        Returns:
            List of document dictionaries
        """
        return self.document_store.get_documents(filter_expr, limit)

    @trace_function(name="kg_rag_clear")
    @measure_latency("kg_rag_clear")
    def clear(self) -> None:
        """
        Clear all data from the RAG system.
        """
        # Clear document store
        self.document_store.clear()

        # Clear knowledge graph
        self.knowledge_graph.clear()

    @trace_function(name="kg_rag_count")
    @measure_latency("kg_rag_count")
    def count(self) -> int:
        """
        Return the number of documents in the vector store.

        Returns:
            Number of documents
        """
        return self.document_store.count_documents()

    @trace_function(name="kg_rag_count_documents")
    @measure_latency("kg_rag_count_documents")
    def count_documents(self) -> int:
        """
        Return the number of documents in the vector store.
        This is an alias for count() to maintain compatibility with BaseRAG.

        Returns:
            Number of documents
        """
        return self.count()

    @trace_function(name="kg_rag_close")
    @measure_latency("kg_rag_close")
    def close(self) -> None:
        """
        Close the vector store connection.
        """
        self.document_store.close()
        self.knowledge_graph.close()

    @trace_function(name="kg_rag_get_knowledge_graph")
    @measure_latency("kg_rag_get_knowledge_graph")
    def get_knowledge_graph(self) -> Dict[str, Any]:
        """
        Get the knowledge graph.

        Returns:
            Dictionary with nodes and edges
        """
        try:
            # Get all entities
            entities = self.knowledge_graph.get_all_entities()

            # Get all relations
            relations = self.knowledge_graph.get_all_relations()

            # Format as graph
            graph = {
                "nodes": [],
                "edges": []
            }

            # Add entities as nodes
            for entity in entities:
                node = {
                    "id": entity["id"],
                    "name": entity["name"],
                    "type": entity["type"],
                    "description": entity.get("description", "")
                }

                # Add properties
                for key, value in entity.get("properties", {}).items():
                    node[key] = value

                graph["nodes"].append(node)

            # Add relations as edges
            for relation in relations:
                edge = {
                    "source": relation["source"],
                    "target": relation["target"],
                    "relation": relation["type"],
                    "weight": relation.get("weight", 1.0)
                }

                # Add properties
                for key, value in relation.get("properties", {}).items():
                    edge[key] = value

                graph["edges"].append(edge)

            return graph

        except Exception as e:
            logger.error(f"Error getting knowledge graph: {str(e)}")
            # Return empty graph as fallback
            return {
                "nodes": [],
                "edges": []
            }

    @trace_function(name="kg_rag_query")
    @measure_latency("kg_rag_query")
    def query(
        self,
        query: str,
        num_results: int = 5,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Query the RAG system.
        This is an alias for search() to maintain compatibility with BaseRAG.

        Args:
            query: The query to search for
            num_results: Number of results to return
            **kwargs: Additional implementation-specific arguments

        Returns:
            List of dictionaries containing the retrieved documents
        """
        return self.search(query, top_k=num_results, **kwargs)

    @trace_function(name="kg_rag_add_document")
    @measure_latency("kg_rag_add_document")
    def add_document(
        self,
        content: str,
        metadata: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> str:
        """
        Add a document to the RAG system.
        This is a convenience method that wraps add_documents() to maintain compatibility with BaseRAG.

        Args:
            content: The content of the document
            metadata: Optional metadata for the document
            **kwargs: Additional implementation-specific arguments

        Returns:
            ID of the added document
        """
        # Create document dictionary
        document = {
            "content": content,
            "metadata": metadata or {}
        }

        # Add document
        document_ids = self.add_documents([document], **kwargs)

        # Return the first document ID
        return document_ids[0] if document_ids else ""
