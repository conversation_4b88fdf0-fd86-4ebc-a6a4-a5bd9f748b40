"""
Module xử lý ngôn ngữ tự nhiên tiếng Việt phiên bản nhẹ.

Mo<PERSON><PERSON> này cung cấp các công cụ xử lý ngôn ngữ tự nhiên tiếng Việt
nhẹ hơn và hiệu quả hơn để sử dụng trong các môi trường giới hạn tài nguyên.
"""

import logging
import re
import string
import os
import json
from typing import Dict, Any, List, Optional, Set, Tuple
from datetime import datetime
import time

# Thiết lập logging
logger = logging.getLogger(__name__)

class VietnameseNLPLite:
    """Lớp xử lý ngôn ngữ tự nhiên tiếng Việt phiên bản nhẹ."""
    
    def __init__(self, resources_dir: Optional[str] = None):
        """
        Khởi tạo bộ xử lý NLP tiếng Việt phiên bản nhẹ.
        
        Args:
            resources_dir: <PERSON>h<PERSON> mục chứa tài nguyên (mặc định: thư mục hiện tại)
        """
        self.initialized = False
        
        # X<PERSON><PERSON> định thư mục tài nguyên
        self.resources_dir = resources_dir or os.path.join(os.path.dirname(__file__), "resources")
        os.makedirs(self.resources_dir, exist_ok=True)
        
        # Từ điển stopwords tiếng Việt
        self.vietnamese_stopwords = self._load_vietnamese_stopwords()
        
        # Từ điển từ đồng nghĩa
        self.synonym_dict = self._load_vietnamese_synonyms()
        
        # Từ điển từ vựng cảm xúc
        self.sentiment_words = self._load_sentiment_words()
        
        # Mẫu biểu thức chính quy để tách câu
        self.sentence_pattern = re.compile(r'(?<!\w\.\w.)(?<![A-Z][a-z]\.)(?<=\.|\?|\!)\s')
        
        # Mảng chứa các ký tự dấu câu
        self.punctuation = string.punctuation + '""''…'
        
        # Khởi tạo cache cho TextRank
        self.textrank_cache = {}
        self.cache_expiry = 3600  # 1 giờ
        
        # Số lượt gọi và thời gian xử lý cho theo dõi hiệu suất
        self._call_count = {}
        self._processing_time = {}
        
        self.initialized = True
        logger.info("VietnameseNLPLite đã được khởi tạo thành công")
    
    def word_tokenize(self, text: str) -> List[str]:
        """
        Tách từ đơn giản cho văn bản tiếng Việt.
        
        Args:
            text: Văn bản cần tách từ
            
        Returns:
            Danh sách các từ đã tách
        """
        start_time = time.time()
        
        # Tiền xử lý văn bản
        text = self._preprocess_text(text)
        
        # Tách từ đơn giản bằng khoảng trắng
        tokens = text.split()
        
        # Thử áp dụng một số quy tắc đơn giản để kết hợp các từ ghép tiếng Việt phổ biến
        # Ví dụ: "Hà Nội", "xe máy", "trí tuệ nhân tạo"
        result = []
        i = 0
        while i < len(tokens):
            if i < len(tokens) - 1:
                bigram = tokens[i] + " " + tokens[i+1]
                if bigram.lower() in self._common_bigrams():
                    result.append(bigram)
                    i += 2
                    continue
            
            result.append(tokens[i])
            i += 1
        
        # Cập nhật thống kê
        self._update_stats("word_tokenize", time.time() - start_time)
        
        return result
    
    def _preprocess_text(self, text: str) -> str:
        """
        Tiền xử lý văn bản trước khi phân tích.
        
        Args:
            text: Văn bản cần xử lý
            
        Returns:
            Văn bản đã được xử lý
        """
        # Chuyển văn bản về dạng chuẩn hóa
        text = text.strip()
        
        # Loại bỏ khoảng trắng thừa
        text = re.sub(r'\s+', ' ', text)
        
        # Loại bỏ các ký tự đặc biệt nếu cần
        # Chú ý: Không loại bỏ dấu câu để giữ nguyên ngữ nghĩa
        
        return text
    
    def _common_bigrams(self) -> Set[str]:
        """
        Danh sách các bigram phổ biến trong tiếng Việt.
        
        Returns:
            Set các bigram phổ biến
        """
        return {
            "hà nội", "thành phố", "hồ chí minh", "đà nẵng", 
            "xe máy", "ô tô", "nhà cửa", "trường học",
            "công ty", "nhân viên", "giám đốc", "quản lý",
            "trí tuệ", "nhân tạo", "học máy", "dữ liệu",
            "môi trường", "tài nguyên", "năng lượng", "phát triển",
            "kinh tế", "chính trị", "xã hội", "văn hóa"
        }
    
    def pos_tag_simple(self, text: str) -> List[Tuple[str, str]]:
        """
        Gán nhãn từ loại đơn giản cho văn bản tiếng Việt.
        
        Args:
            text: Văn bản cần gán nhãn
            
        Returns:
            Danh sách các cặp (từ, nhãn từ loại)
        """
        start_time = time.time()
        
        tokens = self.word_tokenize(text)
        result = []
        
        # Danh sách từ điển đơn giản cho các loại từ phổ biến
        noun_dict = self._load_pos_dictionary("nouns")
        verb_dict = self._load_pos_dictionary("verbs")
        adj_dict = self._load_pos_dictionary("adjectives")
        
        for token in tokens:
            token_lower = token.lower()
            
            # Kiểm tra từng loại từ
            if token_lower in noun_dict:
                pos = "N"  # Danh từ
            elif token_lower in verb_dict:
                pos = "V"  # Động từ
            elif token_lower in adj_dict:
                pos = "A"  # Tính từ
            else:
                pos = "O"  # Khác
            
            result.append((token, pos))
        
        # Cập nhật thống kê
        self._update_stats("pos_tag_simple", time.time() - start_time)
        
        return result
    
    def _load_pos_dictionary(self, pos_type: str) -> Set[str]:
        """
        Tải từ điển từ loại.
        
        Args:
            pos_type: Loại từ loại ("nouns", "verbs", "adjectives")
            
        Returns:
            Set các từ thuộc loại đó
        """
        # Dữ liệu mẫu cho từng loại từ
        if pos_type == "nouns":
            return {
                "người", "nhà", "thành phố", "công ty", "trường học", 
                "sinh viên", "giáo viên", "máy tính", "điện thoại", "xe", 
                "quần áo", "tiền", "nước", "thức ăn", "sách", "bàn", "ghế"
            }
        elif pos_type == "verbs":
            return {
                "là", "có", "đi", "làm", "học", "nói", "viết", "đọc", "nghe", 
                "ăn", "uống", "ngủ", "mua", "bán", "sử dụng", "phát triển", "tạo"
            }
        elif pos_type == "adjectives":
            return {
                "đẹp", "xấu", "tốt", "xấu", "lớn", "nhỏ", "nhanh", "chậm", 
                "mạnh", "yếu", "thông minh", "quan trọng", "đắt", "rẻ", "mới", "cũ"
            }
        else:
            return set()
    
    def sentiment(self, text: str) -> Dict[str, Any]:
        """
        Phân tích cảm xúc văn bản tiếng Việt sử dụng phương pháp từ điển.
        
        Args:
            text: Văn bản cần phân tích
            
        Returns:
            Dictionary chứa kết quả phân tích cảm xúc
        """
        start_time = time.time()
        
        # Tiền xử lý văn bản
        text_lower = text.lower()
        
        # Tính điểm cho từng từ
        positive_score = 0
        negative_score = 0
        
        # Đếm số từ tích cực và tiêu cực
        for word in self.sentiment_words["positive"]:
            if word in text_lower:
                positive_score += 1
        
        for word in self.sentiment_words["negative"]:
            if word in text_lower:
                negative_score += 1
        
        # Tính điểm tổng hợp
        total_score = 0.5  # Giá trị mặc định trung tính
        label = "neutral"
        
        if positive_score > negative_score:
            # Công thức: 0.5 (trung tính) + độ chênh lệch được chuẩn hóa (tối đa 0.4)
            score_diff = positive_score - negative_score
            normalized_diff = min(0.4, score_diff * 0.1)
            total_score = 0.5 + normalized_diff
            label = "positive"
        elif negative_score > positive_score:
            # Công thức: 0.5 (trung tính) - độ chênh lệch được chuẩn hóa (tối đa 0.4)
            score_diff = negative_score - positive_score
            normalized_diff = min(0.4, score_diff * 0.1)
            total_score = 0.5 - normalized_diff
            label = "negative"
        
        # Cập nhật thống kê
        self._update_stats("sentiment", time.time() - start_time)
        
        return {
            "label": label,
            "score": total_score,
            "text": text,
            "positive_words": positive_score,
            "negative_words": negative_score
        }
    
    def extract_keywords(self, text: str, max_keywords: int = 10) -> List[Dict[str, Any]]:
        """
        Trích xuất từ khóa quan trọng từ văn bản tiếng Việt.
        
        Args:
            text: Văn bản cần trích xuất từ khóa
            max_keywords: Số lượng từ khóa tối đa
            
        Returns:
            Danh sách các từ khóa và trọng số
        """
        start_time = time.time()
        
        # Tách từ
        tokens = self.word_tokenize(text)
        
        # Loại bỏ stopwords
        filtered_tokens = [token for token in tokens if token.lower() not in self.vietnamese_stopwords]
        
        # Tính tần số xuất hiện
        word_freq = {}
        for token in filtered_tokens:
            token_lower = token.lower()
            if token_lower not in word_freq:
                word_freq[token_lower] = {
                    "word": token,
                    "count": 1,
                    "score": 0
                }
            else:
                word_freq[token_lower]["count"] += 1
        
        # Gán điểm dựa trên tần suất
        for word, data in word_freq.items():
            # Công thức đơn giản: score = count / max_count
            data["score"] = data["count"] / max(1, max([d["count"] for d in word_freq.values()]))
        
        # Sắp xếp theo điểm
        sorted_keywords = sorted(
            word_freq.values(),
            key=lambda x: x["score"],
            reverse=True
        )
        
        # Giới hạn số lượng từ khóa
        result = sorted_keywords[:max_keywords]
        
        # Cập nhật thống kê
        self._update_stats("extract_keywords", time.time() - start_time)
        
        return result
    
    def summarize(self, text: str, max_sentences: int = 3, method: str = "frequency") -> str:
        """
        Tóm tắt văn bản tiếng Việt.
        
        Args:
            text: Văn bản cần tóm tắt
            max_sentences: Số câu tối đa trong bản tóm tắt
            method: Phương pháp tóm tắt ("frequency" hoặc "position")
            
        Returns:
            Văn bản đã được tóm tắt
        """
        start_time = time.time()
        
        # Tách câu
        sentences = self._split_sentences(text)
        
        # Nếu văn bản ít câu, không cần tóm tắt
        if len(sentences) <= max_sentences:
            return text
        
        # Chọn phương pháp tóm tắt
        if method == "position":
            summary_sentences = self._position_based_summarize(sentences, max_sentences)
        else:  # frequency
            summary_sentences = self._frequency_based_summarize(sentences, max_sentences)
        
        # Ghép các câu lại thành bản tóm tắt
        summary = " ".join(summary_sentences)
        
        # Cập nhật thống kê
        self._update_stats("summarize", time.time() - start_time)
        
        return summary
    
    def _split_sentences(self, text: str) -> List[str]:
        """
        Tách văn bản thành các câu.
        
        Args:
            text: Văn bản cần tách
            
        Returns:
            Danh sách các câu
        """
        # Tách câu bằng biểu thức chính quy
        sentences = self.sentence_pattern.split(text)
        
        # Lọc các câu trống
        return [s.strip() for s in sentences if s.strip()]
    
    def _position_based_summarize(self, sentences: List[str], max_sentences: int) -> List[str]:
        """
        Tóm tắt dựa trên vị trí câu.
        
        Args:
            sentences: Danh sách các câu
            max_sentences: Số câu tối đa trong bản tóm tắt
            
        Returns:
            Danh sách các câu trong bản tóm tắt
        """
        # Ý tưởng: Câu đầu tiên và cuối cùng thường quan trọng
        result = []
        
        # Thêm câu đầu tiên
        if len(sentences) > 0:
            result.append(sentences[0])
        
        # Thêm câu cuối cùng nếu có nhiều hơn 1 câu
        if len(sentences) > 1 and len(result) < max_sentences:
            result.append(sentences[-1])
        
        # Thêm các câu ở giữa nếu cần
        if len(result) < max_sentences:
            middle_count = max_sentences - len(result)
            step = max(1, len(sentences) // (middle_count + 1))
            
            for i in range(step, len(sentences) - 1, step):
                if len(result) >= max_sentences:
                    break
                if sentences[i] not in result:
                    result.append(sentences[i])
        
        # Sắp xếp các câu theo thứ tự xuất hiện ban đầu
        result.sort(key=lambda s: sentences.index(s))
        
        return result
    
    def _frequency_based_summarize(self, sentences: List[str], max_sentences: int) -> List[str]:
        """
        Tóm tắt dựa trên tần suất từ.
        
        Args:
            sentences: Danh sách các câu
            max_sentences: Số câu tối đa trong bản tóm tắt
            
        Returns:
            Danh sách các câu trong bản tóm tắt
        """
        # Tính điểm cho mỗi câu dựa trên tần suất từ
        word_freq = {}
        sentence_scores = {}
        
        # Tính tần suất từ trong văn bản
        for sentence in sentences:
            # Tách từ và loại bỏ stopwords
            words = [w.lower() for w in self.word_tokenize(sentence) 
                     if w.lower() not in self.vietnamese_stopwords]
            
            for word in words:
                if word not in word_freq:
                    word_freq[word] = 1
                else:
                    word_freq[word] += 1
        
        # Tính điểm cho mỗi câu
        for i, sentence in enumerate(sentences):
            words = [w.lower() for w in self.word_tokenize(sentence)]
            score = sum(word_freq.get(word, 0) for word in words) / max(1, len(words))
            
            # Điểm thêm cho vị trí
            position_bonus = 0
            if i == 0 or i == len(sentences) - 1:
                position_bonus = 0.2
            
            sentence_scores[i] = score + position_bonus
        
        # Chọn các câu có điểm cao nhất
        top_indices = sorted(sentence_scores.keys(), 
                            key=lambda i: sentence_scores[i], 
                            reverse=True)[:max_sentences]
        
        # Sắp xếp theo thứ tự xuất hiện trong văn bản gốc
        top_indices.sort()
        
        return [sentences[i] for i in top_indices]
    
    def text_similarity(self, text1: str, text2: str) -> float:
        """
        Tính độ tương đồng văn bản đơn giản.
        
        Args:
            text1: Văn bản thứ nhất
            text2: Văn bản thứ hai
            
        Returns:
            Điểm tương đồng từ 0.0 đến 1.0
        """
        start_time = time.time()
        
        # Tách từ
        words1 = set(w.lower() for w in self.word_tokenize(text1))
        words2 = set(w.lower() for w in self.word_tokenize(text2))
        
        # Loại bỏ stopwords
        words1 = words1 - self.vietnamese_stopwords
        words2 = words2 - self.vietnamese_stopwords
        
        # Nếu một trong hai tập rỗng
        if not words1 or not words2:
            self._update_stats("text_similarity", time.time() - start_time)
            return 0.0
        
        # Tính toán tương đồng Jaccard
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        jaccard = intersection / union if union > 0 else 0.0
        
        # Cập nhật thống kê
        self._update_stats("text_similarity", time.time() - start_time)
        
        return jaccard
    
    def enhance_search_query(self, query: str) -> Dict[str, Any]:
        """
        Cải thiện truy vấn tìm kiếm tiếng Việt.
        
        Args:
            query: Truy vấn gốc
            
        Returns:
            Dictionary chứa truy vấn đã được cải thiện và thông tin bổ sung
        """
        start_time = time.time()
        
        # Tách từ
        tokens = self.word_tokenize(query)
        
        # Loại bỏ stopwords
        filtered_tokens = [token for token in tokens 
                          if token.lower() not in self.vietnamese_stopwords]
        
        # Thêm từ đồng nghĩa
        synonyms = []
        for token in filtered_tokens:
            token_synonyms = self.get_synonyms(token)
            if token_synonyms:
                # Chỉ thêm tối đa 2 từ đồng nghĩa cho mỗi từ
                synonyms.extend(token_synonyms[:2])
        
        # Tạo truy vấn mở rộng
        enhanced_parts = []
        
        # Thêm truy vấn gốc
        enhanced_parts.append(query)
        
        # Thêm các từ đồng nghĩa
        if synonyms:
            enhanced_parts.append(' '.join(synonyms))
        
        # Kết hợp thành truy vấn mở rộng
        enhanced_query = ' '.join(enhanced_parts)
        
        # Cập nhật thống kê
        self._update_stats("enhance_search_query", time.time() - start_time)
        
        return {
            "enhanced_query": enhanced_query,
            "original": query,
            "synonyms_added": synonyms,
            "tokens": tokens,
            "filtered_tokens": filtered_tokens
        }
    
    def get_synonyms(self, word: str, max_results: int = 3) -> List[str]:
        """
        Lấy danh sách từ đồng nghĩa cho một từ tiếng Việt.
        
        Args:
            word: Từ cần tìm đồng nghĩa
            max_results: Số lượng từ đồng nghĩa tối đa
            
        Returns:
            Danh sách các từ đồng nghĩa
        """
        word_lower = word.lower()
        
        # Tìm trong từ điển đồng nghĩa
        if word_lower in self.synonym_dict:
            return self.synonym_dict[word_lower][:max_results]
            
        return []
    
    def _load_vietnamese_stopwords(self) -> Set[str]:
        """
        Tải danh sách stopwords tiếng Việt.
        
        Returns:
            Set các stopwords
        """
        # Một số stopwords tiếng Việt phổ biến
        return {
            'là', 'của', 'và', 'các', 'có', 'được', 'trong', 'đã', 'cho', 'không', 
            'những', 'để', 'về', 'cần', 'người', 'khi', 'này', 'vào', 'từ', 'tôi', 
            'sẽ', 'với', 'bị', 'đến', 'cũng', 'nhiều', 'hơn', 'như', 'đang', 'nên',
            'nhưng', 'thì', 'đều', 'lên', 'trên', 'rất', 'theo', 'hoặc', 'lại', 'mà',
            'nếu', 'làm', 'vẫn', 'còn', 'đó', 'do', 'sau', 'phải', 'biết', 'rồi', 
            'một', 'hai', 'ba', 'bốn', 'năm', 'ai', 'gì', 'bao giờ', 'à', 'thôi'
        }
    
    def _load_vietnamese_synonyms(self) -> Dict[str, List[str]]:
        """
        Tải từ điển từ đồng nghĩa tiếng Việt.
        
        Returns:
            Dictionary chứa từ và danh sách đồng nghĩa
        """
        # Từ điển đồng nghĩa đơn giản
        return {
            'tốt': ['hay', 'giỏi', 'xuất sắc', 'tuyệt vời'],
            'xấu': ['tồi', 'kém', 'dở', 'tệ'],
            'lớn': ['to', 'rộng', 'khổng lồ', 'đồ sộ'],
            'nhỏ': ['bé', 'nhỏ bé', 'tí', 'li ti'],
            'nhanh': ['mau', 'vội', 'gấp', 'cấp tốc'],
            'chậm': ['từ từ', 'chầm chậm', 'thong thả'],
            'buồn': ['đau khổ', 'sầu não', 'phiền muộn'],
            'vui': ['hạnh phúc', 'phấn khởi', 'sung sướng'],
            'quan trọng': ['trọng yếu', 'cốt lõi', 'then chốt'],
            'thông minh': ['sáng dạ', 'thông thái', 'uyên bác'],
            'tìm': ['kiếm', 'tìm kiếm', 'truy tìm', 'dò tìm'],
            'mua': ['sắm', 'mua sắm', 'tậu', 'sở hữu'],
            'bán': ['rao', 'bán hàng', 'kinh doanh'],
            'xem': ['nhìn', 'quan sát', 'theo dõi'],
            'nghe': ['lắng nghe', 'nghe thấy', 'thính giác'],
            'nói': ['phát biểu', 'diễn đạt', 'trò chuyện'],
            'hiểu': ['thấu hiểu', 'nắm bắt', 'lĩnh hội'],
            'công việc': ['việc làm', 'nghề nghiệp', 'chức vụ'],
            'tiền': ['tiền bạc', 'tiền tệ', 'tài chính'],
            'xe': ['ô tô', 'xe hơi', 'phương tiện'],
            'nhà': ['căn hộ', 'chung cư', 'biệt thự'],
            'thông tin': ['dữ liệu', 'tin tức', 'số liệu'],
            'giáo dục': ['đào tạo', 'học tập', 'giảng dạy'],
            'du lịch': ['tham quan', 'nghỉ dưỡng', 'đi chơi'],
            'cần': ['yêu cầu', 'đòi hỏi', 'mong muốn']
        }
    
    def _load_sentiment_words(self) -> Dict[str, List[str]]:
        """
        Tải danh sách từ cảm xúc.
        
        Returns:
            Dictionary chứa từ tích cực và tiêu cực
        """
        return {
            "positive": [
                "tốt", "hay", "tuyệt", "xuất sắc", "thích", "ưa", "thú vị", "hài lòng", 
                "vui", "tích cực", "hạnh phúc", "yêu", "đáng giá", "tuyệt vời", "hiệu quả",
                "giỏi", "đẹp", "hiện đại", "chất lượng", "hấp dẫn", "phù hợp", "tiện lợi",
                "nhanh", "sạch", "chu đáo", "nhiệt tình", "công bằng", "lịch sự", "vui vẻ",
                "thoải mái", "mới", "chân thành", "trung thực", "đúng giờ", "rộng rãi"
            ],
            "negative": [
                "kém", "tệ", "chán", "buồn", "thất vọng", "không thích", "ghét", "tiêu cực",
                "tồi", "kém chất lượng", "đáng thất vọng", "không hài lòng", "khó chịu",
                "tức giận", "thiếu", "lỗi", "hỏng", "không được", "không tốt", "lãng phí",
                "chậm", "bẩn", "thô lỗ", "lạnh nhạt", "thiếu công bằng", "bất lịch sự", 
                "buồn bã", "khó khăn", "cũ", "giả dối", "không trung thực", "trễ", "chật hẹp"
            ]
        }
    
    def _update_stats(self, method_name: str, execution_time: float) -> None:
        """
        Cập nhật thống kê cho phương thức.
        
        Args:
            method_name: Tên phương thức
            execution_time: Thời gian thực thi
        """
        if method_name not in self._call_count:
            self._call_count[method_name] = 0
            self._processing_time[method_name] = []
        
        self._call_count[method_name] += 1
        self._processing_time[method_name].append(execution_time)
        
        # Giới hạn số lượng mẫu lưu trữ
        if len(self._processing_time[method_name]) > 100:
            self._processing_time[method_name] = self._processing_time[method_name][-100:]
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Lấy thống kê hiệu suất.
        
        Returns:
            Dictionary chứa thống kê hiệu suất
        """
        stats = {}
        
        for method in self._call_count.keys():
            call_count = self._call_count[method]
            times = self._processing_time[method]
            
            if times:
                avg_time = sum(times) / len(times)
                max_time = max(times)
                min_time = min(times)
            else:
                avg_time = max_time = min_time = 0
            
            stats[method] = {
                "calls": call_count,
                "avg_time": avg_time,
                "max_time": max_time,
                "min_time": min_time,
                "total_time": sum(times)
            }
        
        return stats


# Tạo thể hiện global cho module
vietnamese_nlp_lite = VietnameseNLPLite() 