#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module triển khai các tính năng nâng cao cho WebSearchAgent.

Module này cung cấp các lớp và hàm để triển khai các tính năng nâng cao, bao gồm:
1. Xử lý CAPTCHA
2. Xoay vòng User-Agent
3. Quản lý proxy
4. <PERSON><PERSON>i thiện trích xuất nội dung
"""

import os
import json
import time
import logging
import threading
import random
import re
from typing import Dict, Any, List, Optional, Union, Tuple
from datetime import datetime, timedelta
import requests
import urllib.parse
from bs4 import BeautifulSoup
import base64
import io
from PIL import Image
import numpy as np

# Thiết lập logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UserAgentManager:
    """
    Lớp quản lý User-Agent.
    
    Tính năng:
    - Xoay vòng User-Agent
    - Tạo User-Agent ngẫu nhiên
    - Tùy chỉnh User-Agent theo trình duyệt, hệ điều hành
    """
    
    def __init__(
        self,
        user_agents: Optional[List[str]] = None,
        rotation_strategy: str = "sequential",
        rotation_interval: int = 10,
        browser_weights: Optional[Dict[str, float]] = None,
        os_weights: Optional[Dict[str, float]] = None
    ):
        """
        Khởi tạo UserAgentManager.
        
        Args:
            user_agents: Danh sách User-Agent
            rotation_strategy: Chiến lược xoay vòng ("sequential", "random", "weighted")
            rotation_interval: Khoảng thời gian xoay vòng (số lần sử dụng)
            browser_weights: Trọng số cho các trình duyệt
            os_weights: Trọng số cho các hệ điều hành
        """
        # Khởi tạo danh sách User-Agent
        self.user_agents = user_agents or self._get_default_user_agents()
        self.rotation_strategy = rotation_strategy
        self.rotation_interval = rotation_interval
        
        # Khởi tạo trọng số
        self.browser_weights = browser_weights or {
            "chrome": 0.5,
            "firefox": 0.3,
            "safari": 0.1,
            "edge": 0.05,
            "opera": 0.05
        }
        
        self.os_weights = os_weights or {
            "windows": 0.6,
            "macos": 0.2,
            "linux": 0.1,
            "android": 0.05,
            "ios": 0.05
        }
        
        # Khởi tạo các biến theo dõi
        self.current_index = 0
        self.use_count = 0
        self.last_rotation_time = time.time()
        
        # Khởi tạo lock cho thread safety
        self.lock = threading.RLock()
    
    def _get_default_user_agents(self) -> List[str]:
        """
        Lấy danh sách User-Agent mặc định.
        
        Returns:
            List[str]: Danh sách User-Agent
        """
        return [
            # Chrome
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
            
            # Firefox
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:90.0) Gecko/20100101 Firefox/90.0",
            "Mozilla/5.0 (X11; Linux x86_64; rv:90.0) Gecko/20100101 Firefox/90.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101 Firefox/91.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:91.0) Gecko/20100101 Firefox/91.0",
            
            # Safari
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15",
            
            # Edge
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59",
            
            # Opera
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 OPR/77.0.4054.277",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 OPR/77.0.4054.277",
            
            # Mobile
            "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36",
            "Mozilla/5.0 (Linux; Android 11; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36"
        ]
    
    def _generate_random_user_agent(self) -> str:
        """
        Tạo User-Agent ngẫu nhiên.
        
        Returns:
            str: User-Agent ngẫu nhiên
        """
        # Chọn trình duyệt
        browser = random.choices(
            list(self.browser_weights.keys()),
            weights=list(self.browser_weights.values())
        )[0]
        
        # Chọn hệ điều hành
        os_name = random.choices(
            list(self.os_weights.keys()),
            weights=list(self.os_weights.values())
        )[0]
        
        # Tạo User-Agent
        if browser == "chrome":
            chrome_version = f"{random.randint(70, 96)}.0.{random.randint(3000, 5000)}.{random.randint(1, 200)}"
            
            if os_name == "windows":
                return f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{chrome_version} Safari/537.36"
            elif os_name == "macos":
                return f"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_{random.randint(13, 15)}_{random.randint(1, 7)}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{chrome_version} Safari/537.36"
            elif os_name == "linux":
                return f"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{chrome_version} Safari/537.36"
            elif os_name == "android":
                return f"Mozilla/5.0 (Linux; Android {random.randint(8, 12)}; SM-G{random.randint(900, 999)}B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{chrome_version} Mobile Safari/537.36"
        
        elif browser == "firefox":
            firefox_version = f"{random.randint(70, 96)}.0"
            
            if os_name == "windows":
                return f"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:{firefox_version}) Gecko/20100101 Firefox/{firefox_version}"
            elif os_name == "macos":
                return f"Mozilla/5.0 (Macintosh; Intel Mac OS X 10.{random.randint(13, 15)}; rv:{firefox_version}) Gecko/20100101 Firefox/{firefox_version}"
            elif os_name == "linux":
                return f"Mozilla/5.0 (X11; Linux x86_64; rv:{firefox_version}) Gecko/20100101 Firefox/{firefox_version}"
            elif os_name == "android":
                return f"Mozilla/5.0 (Android {random.randint(8, 12)}; Mobile; rv:{firefox_version}) Gecko/{firefox_version} Firefox/{firefox_version}"
        
        elif browser == "safari":
            safari_version = f"{random.randint(12, 15)}.{random.randint(0, 1)}.{random.randint(1, 5)}"
            
            if os_name == "macos":
                return f"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_{random.randint(13, 15)}_{random.randint(1, 7)}) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/{safari_version} Safari/605.1.15"
            elif os_name == "ios":
                return f"Mozilla/5.0 (iPhone; CPU iPhone OS {random.randint(13, 15)}_{random.randint(1, 7)} like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/{safari_version} Mobile/15E148 Safari/604.1"
        
        # Nếu không khớp với bất kỳ trường hợp nào, trả về User-Agent Chrome mặc định
        return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    
    def get_user_agent(self) -> str:
        """
        Lấy User-Agent theo chiến lược xoay vòng.
        
        Returns:
            str: User-Agent
        """
        with self.lock:
            # Kiểm tra xem có cần xoay vòng không
            if self.use_count >= self.rotation_interval:
                self.use_count = 0
                
                if self.rotation_strategy == "sequential":
                    # Xoay vòng tuần tự
                    self.current_index = (self.current_index + 1) % len(self.user_agents)
                elif self.rotation_strategy == "random":
                    # Xoay vòng ngẫu nhiên
                    self.current_index = random.randint(0, len(self.user_agents) - 1)
                elif self.rotation_strategy == "weighted":
                    # Tạo User-Agent ngẫu nhiên
                    return self._generate_random_user_agent()
            
            # Tăng số lần sử dụng
            self.use_count += 1
            
            # Trả về User-Agent
            return self.user_agents[self.current_index]
    
    def add_user_agent(self, user_agent: str) -> None:
        """
        Thêm User-Agent vào danh sách.
        
        Args:
            user_agent: User-Agent cần thêm
        """
        with self.lock:
            if user_agent not in self.user_agents:
                self.user_agents.append(user_agent)
    
    def remove_user_agent(self, user_agent: str) -> bool:
        """
        Xóa User-Agent khỏi danh sách.
        
        Args:
            user_agent: User-Agent cần xóa
            
        Returns:
            bool: True nếu xóa thành công, False nếu không
        """
        with self.lock:
            if user_agent in self.user_agents and len(self.user_agents) > 1:
                self.user_agents.remove(user_agent)
                
                # Điều chỉnh current_index nếu cần
                if self.current_index >= len(self.user_agents):
                    self.current_index = 0
                
                return True
            
            return False
    
    def get_all_user_agents(self) -> List[str]:
        """
        Lấy tất cả User-Agent.
        
        Returns:
            List[str]: Danh sách User-Agent
        """
        with self.lock:
            return self.user_agents.copy()

class ProxyManager:
    """
    Lớp quản lý proxy.
    
    Tính năng:
    - Xoay vòng proxy
    - Kiểm tra proxy
    - Tự động loại bỏ proxy không hoạt động
    """
    
    def __init__(
        self,
        proxies: Optional[List[Dict[str, Any]]] = None,
        rotation_strategy: str = "sequential",
        rotation_interval: int = 10,
        check_interval: int = 3600,  # 1 giờ
        timeout: int = 10,
        auto_remove_failed: bool = True,
        max_consecutive_failures: int = 3
    ):
        """
        Khởi tạo ProxyManager.
        
        Args:
            proxies: Danh sách proxy
            rotation_strategy: Chiến lược xoay vòng ("sequential", "random", "weighted")
            rotation_interval: Khoảng thời gian xoay vòng (số lần sử dụng)
            check_interval: Khoảng thời gian kiểm tra proxy (giây)
            timeout: Thời gian timeout khi kiểm tra proxy (giây)
            auto_remove_failed: Tự động loại bỏ proxy không hoạt động
            max_consecutive_failures: Số lần thất bại liên tiếp tối đa trước khi loại bỏ proxy
        """
        # Khởi tạo danh sách proxy
        self.proxies = proxies or []
        self.rotation_strategy = rotation_strategy
        self.rotation_interval = rotation_interval
        self.check_interval = check_interval
        self.timeout = timeout
        self.auto_remove_failed = auto_remove_failed
        self.max_consecutive_failures = max_consecutive_failures
        
        # Khởi tạo các biến theo dõi
        self.current_index = 0
        self.use_count = 0
        self.last_check_time = time.time()
        self.proxy_stats = {}
        
        # Khởi tạo lock cho thread safety
        self.lock = threading.RLock()
        
        # Khởi tạo thống kê cho mỗi proxy
        for proxy in self.proxies:
            proxy_id = self._get_proxy_id(proxy)
            self.proxy_stats[proxy_id] = {
                "success_count": 0,
                "failure_count": 0,
                "consecutive_failures": 0,
                "last_success_time": None,
                "last_failure_time": None,
                "average_response_time": 0,
                "is_active": True
            }
        
        # Kiểm tra proxy nếu có
        if self.proxies:
            self._check_proxies()
    
    def _get_proxy_id(self, proxy: Dict[str, Any]) -> str:
        """
        Lấy ID của proxy.
        
        Args:
            proxy: Proxy cần lấy ID
            
        Returns:
            str: ID của proxy
        """
        host = proxy.get("host", "")
        port = proxy.get("port", "")
        username = proxy.get("username", "")
        
        return f"{host}:{port}:{username}"
    
    def _format_proxy_url(self, proxy: Dict[str, Any]) -> str:
        """
        Định dạng URL proxy.
        
        Args:
            proxy: Proxy cần định dạng
            
        Returns:
            str: URL proxy
        """
        protocol = proxy.get("protocol", "http")
        host = proxy.get("host", "")
        port = proxy.get("port", "")
        username = proxy.get("username", "")
        password = proxy.get("password", "")
        
        if username and password:
            return f"{protocol}://{username}:{password}@{host}:{port}"
        else:
            return f"{protocol}://{host}:{port}"
    
    def _check_proxy(self, proxy: Dict[str, Any]) -> bool:
        """
        Kiểm tra proxy.
        
        Args:
            proxy: Proxy cần kiểm tra
            
        Returns:
            bool: True nếu proxy hoạt động, False nếu không
        """
        proxy_url = self._format_proxy_url(proxy)
        proxies = {
            "http": proxy_url,
            "https": proxy_url
        }
        
        try:
            # Ghi lại thời gian bắt đầu
            start_time = time.time()
            
            # Thực hiện kiểm tra
            response = requests.get(
                "https://www.google.com",
                proxies=proxies,
                timeout=self.timeout
            )
            
            # Ghi lại thời gian kết thúc
            end_time = time.time()
            response_time = end_time - start_time
            
            # Kiểm tra kết quả
            if response.status_code == 200:
                # Cập nhật thống kê
                proxy_id = self._get_proxy_id(proxy)
                with self.lock:
                    if proxy_id in self.proxy_stats:
                        self.proxy_stats[proxy_id]["success_count"] += 1
                        self.proxy_stats[proxy_id]["consecutive_failures"] = 0
                        self.proxy_stats[proxy_id]["last_success_time"] = datetime.now()
                        self.proxy_stats[proxy_id]["is_active"] = True
                        
                        # Cập nhật thời gian phản hồi trung bình
                        stats = self.proxy_stats[proxy_id]
                        stats["average_response_time"] = (
                            (stats["average_response_time"] * (stats["success_count"] - 1) + response_time) /
                            stats["success_count"]
                        )
                
                return True
            else:
                # Cập nhật thống kê
                proxy_id = self._get_proxy_id(proxy)
                with self.lock:
                    if proxy_id in self.proxy_stats:
                        self.proxy_stats[proxy_id]["failure_count"] += 1
                        self.proxy_stats[proxy_id]["consecutive_failures"] += 1
                        self.proxy_stats[proxy_id]["last_failure_time"] = datetime.now()
                        
                        # Kiểm tra số lần thất bại liên tiếp
                        if (self.auto_remove_failed and
                            self.proxy_stats[proxy_id]["consecutive_failures"] >= self.max_consecutive_failures):
                            self.proxy_stats[proxy_id]["is_active"] = False
                
                return False
        
        except Exception as e:
            # Cập nhật thống kê
            proxy_id = self._get_proxy_id(proxy)
            with self.lock:
                if proxy_id in self.proxy_stats:
                    self.proxy_stats[proxy_id]["failure_count"] += 1
                    self.proxy_stats[proxy_id]["consecutive_failures"] += 1
                    self.proxy_stats[proxy_id]["last_failure_time"] = datetime.now()
                    
                    # Kiểm tra số lần thất bại liên tiếp
                    if (self.auto_remove_failed and
                        self.proxy_stats[proxy_id]["consecutive_failures"] >= self.max_consecutive_failures):
                        self.proxy_stats[proxy_id]["is_active"] = False
            
            logger.error(f"Lỗi khi kiểm tra proxy {proxy_url}: {str(e)}")
            return False
