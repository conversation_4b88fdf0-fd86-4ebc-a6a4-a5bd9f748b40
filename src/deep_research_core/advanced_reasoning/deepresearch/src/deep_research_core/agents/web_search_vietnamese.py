"""
Web Search Vietnamese Support Module.

Module này cung cấp các chức năng đặc biệt cho tiếng Việt trong WebSearchAgent.
"""

import re
import logging
from typing import Dict, Any, List, Optional, Union, Set, Tuple

from ..utils.structured_logging import get_logger

# Thiết lập logger
logger = get_logger(__name__)

# Kiểm tra underthesea có sẵn không
try:
    import underthesea
    UNDERTHESEA_AVAILABLE = True
    logger.info("Underthesea đã được cài đặt. Hỗ trợ tiếng Việt nâng cao được kích hoạt.")
except ImportError:
    UNDERTHESEA_AVAILABLE = False
    logger.warning("Underthesea không được cài đặt. Hỗ trợ tiếng Việt nâng cao bị tắt.")

# <PERSON><PERSON><PERSON> từ dừng tiếng Việt phổ biến
VIETNAMESE_STOPWORDS = {
    "và", "hoặc", "là", "của", "có", "được", "không", "một", "trong", 
    "đã", "với", "về", "theo", "từ", "trên", "cho", "để", "sẽ", "bị", 
    "còn", "vì", "bởi", "như", "như là", "rằng", "tại", "nếu", "thì", 
    "nên", "khi", "lúc", "sau", "trước", "vậy", "mà", "thì", "các", "những",
    "cũng", "đang", "nay", "này", "đó", "đây", "mới", "thế", "vẫn", "đến",
    "lại", "nữa", "nhất", "chỉ", "thôi", "ai", "gì", "đâu", "nào"
}

# Từ điển từ đồng nghĩa tiếng Việt cơ bản
VIETNAMESE_SYNONYMS = {
    "mua": ["sắm", "tậu", "thu mua", "đặt hàng"],
    "bán": ["buôn bán", "mua bán", "kinh doanh", "giao dịch"],
    "sử dụng": ["dùng", "áp dụng", "vận dụng", "xài", "ứng dụng"],
    "tạo": ["làm", "hình thành", "sinh ra", "sản xuất"],
    "phát triển": ["tiến triển", "nâng cao", "mở mang", "tiến bộ"],
    "tìm kiếm": ["tìm", "tra cứu", "tìm hiểu", "khám phá", "tra tìm"],
    "hướng dẫn": ["chỉ dẫn", "chỉ bảo", "dẫn dắt", "chỉ đường"],
    "mới": ["hiện đại", "tân tiến", "mới mẻ", "mới lạ"],
    "tốt": ["hay", "tuyệt", "đẹp", "xuất sắc", "giỏi", "chất lượng"],
    "xấu": ["kém", "tệ", "không tốt", "dở", "không đẹp"],
    "nhanh": ["mau", "vội", "chóng", "lẹ", "nhanh chóng"],
    "chậm": ["từ từ", "chậm chạp", "thư thả", "chậm rãi"],
    "học": ["học tập", "nghiên cứu", "học hỏi", "tìm hiểu"],
    "dạy": ["giảng dạy", "hướng dẫn", "đào tạo", "truyền đạt"],
    "giá": ["giá cả", "giá tiền", "chi phí", "phí", "tiền"],
    "công nghệ": ["kỹ thuật", "phương pháp", "kỹ thuật số", "công nghệ thông tin"],
    "vấn đề": ["khó khăn", "trở ngại", "thách thức", "rắc rối"],
    "giải pháp": ["cách giải quyết", "phương án", "biện pháp", "cách thức"],
    "công việc": ["việc làm", "nghề nghiệp", "nghề", "chức vụ", "nhiệm vụ"],
    "thông tin": ["tin tức", "dữ liệu", "tin", "tư liệu", "tài liệu"]
}

def enhance_vietnamese_results(search_result: Dict[str, Any]) -> None:
    """
    Nâng cao chất lượng kết quả tìm kiếm tiếng Việt.
    
    Args:
        search_result: Kết quả tìm kiếm cần được tăng cường
    """
    if not UNDERTHESEA_AVAILABLE or not search_result.get("success", False):
        return
    
    try:
        # Lấy truy vấn gốc
        query = search_result.get("query", "")
        if not query:
            return
        
        # Phân tích truy vấn
        query_analysis = enhance_search_query(query)
        
        # Cập nhật metadata của kết quả
        if "metadata" not in search_result:
            search_result["metadata"] = {}
        
        search_result["metadata"]["vietnamese_query_analysis"] = {
            "important_words": query_analysis.get("important_words", []),
            "entities": query_analysis.get("entities", []),
            "synonyms_added": query_analysis.get("synonyms_added", [])
        }
        
        # Xử lý và cải thiện xếp hạng kết quả
        results = search_result.get("results", [])
        if not results:
            return
        
        # Trích xuất từ khóa quan trọng từ query
        query_keywords = extract_keywords(query, max_keywords=5)
        query_keyword_set = {kw["word"].lower() for kw in query_keywords}
        
        # Đánh giá mức độ liên quan cho các kết quả
        for result in results:
            relevance_score = 0.0
            
            # Sử dụng tiêu đề, snippet và URL để đánh giá
            title = result.get("title", "")
            snippet = result.get("snippet", "")
            url = result.get("url", "")
            
            # Phân tích ngôn ngữ trên tiêu đề và snippet
            if title:
                title_words = set(word.lower() for word in underthesea.word_tokenize(title))
                # Tính điểm dựa trên từ khóa trong tiêu đề
                relevance_score += sum(2.0 for word in title_words if word in query_keyword_set)
            
            if snippet:
                # Phân tách từ tiếng Việt
                snippet_words = set(word.lower() for word in underthesea.word_tokenize(snippet))
                # Tính điểm dựa trên từ khóa trong snippet
                relevance_score += sum(1.0 for word in snippet_words if word in query_keyword_set)
            
            # Phân tích URL cho các trang Việt
            if url:
                if ".vn" in url or "vietnam" in url.lower() or "tieng-viet" in url.lower():
                    relevance_score += 1.0
            
            # Cập nhật điểm liên quan cho kết quả
            result["vietnamese_relevance_score"] = relevance_score
        
        # Sắp xếp lại kết quả theo điểm liên quan nếu có nhiều hơn 1 kết quả
        if len(results) > 1:
            sorted_results = sorted(results, key=lambda x: x.get("vietnamese_relevance_score", 0), reverse=True)
            search_result["results"] = sorted_results
            search_result["metadata"]["vietnamese_reranking_applied"] = True
    
    except Exception as e:
        logger.error(f"Lỗi khi nâng cao kết quả tiếng Việt: {str(e)}")
        search_result["metadata"]["vietnamese_enhancement_error"] = str(e)

def enhance_search_query(query: str) -> Dict[str, Any]:
    """
    Nâng cao chất lượng truy vấn tìm kiếm tiếng Việt.
    
    Args:
        query: Truy vấn cần được tăng cường
        
    Returns:
        Dictionary chứa kết quả phân tích và tăng cường
    """
    if not UNDERTHESEA_AVAILABLE:
        return {"error": "Underthesea không khả dụng"}
    
    result = {
        "original_query": query,
        "important_words": [],
        "entities": [],
        "synonyms_added": [],
        "enhanced_query": query
    }
    
    try:
        # Phân tích POS (part-of-speech)
        pos_tags = underthesea.pos_tag(query)
        
        # Trích xuất từ quan trọng (danh từ, động từ, tính từ)
        important_words = []
        for word, tag in pos_tags:
            if tag.startswith('N') or tag.startswith('V') or tag.startswith('A'):
                if word.lower() not in VIETNAMESE_STOPWORDS and len(word) > 1:
                    important_words.append({"word": word, "tag": tag})
        
        result["important_words"] = important_words
        
        # Phân tích thực thể có tên (NER)
        try:
            ner_tags = underthesea.ner(query)
            entities = []
            current_entity = {"text": "", "tag": ""}
            
            for word, tag in ner_tags:
                if tag != "O":  # O có nghĩa là không phải thực thể
                    if tag.startswith("B-"):  # Bắt đầu một thực thể mới
                        if current_entity["text"]:
                            entities.append(current_entity.copy())
                        current_entity = {"text": word, "tag": tag[2:]}  # Loại bỏ tiền tố B-
                    elif tag.startswith("I-"):  # Tiếp tục một thực thể
                        current_entity["text"] += " " + word
            
            # Thêm thực thể cuối cùng
            if current_entity["text"]:
                entities.append(current_entity)
                
            result["entities"] = entities
        except:
            # NER có thể không hoạt động trên một số phiên bản underthesea
            pass
        
        # Tìm từ đồng nghĩa để mở rộng truy vấn
        synonyms_added = []
        enhanced_query_parts = [query]
        
        for word_info in important_words:
            word = word_info["word"].lower()
            if word in VIETNAMESE_SYNONYMS:
                # Thêm tối đa 2 từ đồng nghĩa
                for synonym in VIETNAMESE_SYNONYMS[word][:2]:
                    if synonym.lower() not in query.lower():
                        synonyms_added.append({"original": word, "synonym": synonym})
                        enhanced_query_parts.append(synonym)
        
        # Tạo truy vấn nâng cao nếu có từ đồng nghĩa
        if synonyms_added:
            result["synonyms_added"] = synonyms_added
            result["enhanced_query"] = " ".join(enhanced_query_parts)
        
        return result
        
    except Exception as e:
        logger.error(f"Lỗi khi nâng cao truy vấn tiếng Việt: {str(e)}")
        result["error"] = str(e)
        return result

def extract_keywords(text: str, max_keywords: int = 10) -> List[Dict[str, Any]]:
    """
    Trích xuất từ khóa quan trọng từ văn bản tiếng Việt.
    
    Args:
        text: Văn bản cần trích xuất từ khóa
        max_keywords: Số lượng từ khóa tối đa cần trích xuất
        
    Returns:
        Danh sách từ khóa với trọng số
    """
    if not UNDERTHESEA_AVAILABLE:
        return []
    
    try:
        # Phân tích POS
        pos_tags = underthesea.pos_tag(text)
        
        # Loại bỏ các từ dừng và giữ lại các từ quan trọng
        keywords = []
        for word, tag in pos_tags:
            lowercase_word = word.lower()
            if (tag.startswith('N') or tag.startswith('V') or tag.startswith('A')) and \
               lowercase_word not in VIETNAMESE_STOPWORDS and len(word) > 1:
                # Tính trọng số đơn giản
                weight = 1.0
                if tag.startswith('N'):  # Danh từ có trọng số cao hơn
                    weight = 1.5
                if tag == 'Np':  # Danh từ riêng còn cao hơn nữa
                    weight = 2.0
                
                # Từ dài hơn thường quan trọng hơn
                length_bonus = min(len(word) / 10, 0.5)
                weight += length_bonus
                
                keywords.append({
                    "word": word,
                    "tag": tag,
                    "weight": weight
                })
        
        # Sắp xếp theo trọng số và giới hạn số lượng
        sorted_keywords = sorted(keywords, key=lambda x: x["weight"], reverse=True)
        return sorted_keywords[:max_keywords]
        
    except Exception as e:
        logger.error(f"Lỗi khi trích xuất từ khóa tiếng Việt: {str(e)}")
        return []

def calculate_text_similarity(text1: str, text2: str) -> float:
    """
    Tính độ tương tự giữa hai văn bản tiếng Việt.
    
    Args:
        text1: Văn bản thứ nhất
        text2: Văn bản thứ hai
        
    Returns:
        Độ tương tự từ 0.0 đến 1.0
    """
    if not UNDERTHESEA_AVAILABLE:
        # Fallback to simple matching if underthesea is not available
        return _simple_similarity(text1, text2)
    
    try:
        # Tokenize texts
        tokens1 = underthesea.word_tokenize(text1.lower())
        tokens2 = underthesea.word_tokenize(text2.lower())
        
        # Remove stopwords
        tokens1 = [t for t in tokens1 if t not in VIETNAMESE_STOPWORDS]
        tokens2 = [t for t in tokens2 if t not in VIETNAMESE_STOPWORDS]
        
        # Check if either set is empty
        if not tokens1 or not tokens2:
            return 0.0
        
        # Calculate Jaccard similarity
        set1 = set(tokens1)
        set2 = set(tokens2)
        
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        jaccard = intersection / union if union > 0 else 0.0
        
        # Additionally check for synonym overlap
        synonym_bonus = _calculate_synonym_overlap(set1, set2)
        
        # Combine scores (70% Jaccard, 30% synonym bonus)
        combined_score = 0.7 * jaccard + 0.3 * synonym_bonus
        
        return min(combined_score, 1.0)  # Cap at 1.0
        
    except Exception as e:
        logger.error(f"Lỗi khi tính độ tương tự văn bản tiếng Việt: {str(e)}")
        # Fallback to simple method
        return _simple_similarity(text1, text2)

def _calculate_synonym_overlap(set1: Set[str], set2: Set[str]) -> float:
    """
    Tính độ phủ từ đồng nghĩa giữa hai tập từ.
    
    Args:
        set1: Tập từ thứ nhất
        set2: Tập từ thứ hai
        
    Returns:
        Điểm phủ từ đồng nghĩa từ 0.0 đến 1.0
    """
    if not set1 or not set2:
        return 0.0
    
    synonym_matches = 0
    
    # Kiểm tra mỗi từ trong set1
    for word1 in set1:
        # Nếu từ đã có trong set2, bỏ qua
        if word1 in set2:
            continue
            
        # Kiểm tra các từ đồng nghĩa
        if word1 in VIETNAMESE_SYNONYMS:
            for synonym in VIETNAMESE_SYNONYMS[word1]:
                if synonym in set2:
                    synonym_matches += 1
                    break
    
    # Kiểm tra mỗi từ trong set2
    for word2 in set2:
        # Nếu từ đã có trong set1, bỏ qua
        if word2 in set1:
            continue
            
        # Kiểm tra các từ đồng nghĩa
        if word2 in VIETNAMESE_SYNONYMS:
            for synonym in VIETNAMESE_SYNONYMS[word2]:
                if synonym in set1:
                    synonym_matches += 1
                    break
    
    # Tính điểm đồng nghĩa
    max_possible_matches = min(len(set1), len(set2))
    return synonym_matches / max_possible_matches if max_possible_matches > 0 else 0.0

def _simple_similarity(text1: str, text2: str) -> float:
    """
    Phương pháp đơn giản để tính độ tương tự giữa hai văn bản.
    
    Args:
        text1: Văn bản thứ nhất
        text2: Văn bản thứ hai
        
    Returns:
        Độ tương tự từ 0.0 đến 1.0
    """
    # Chuyển thành chữ thường
    t1 = text1.lower()
    t2 = text2.lower()
    
    # Nếu giống hệt nhau
    if t1 == t2:
        return 1.0
    
    # Tách từ đơn giản
    words1 = set(re.findall(r'\w+', t1))
    words2 = set(re.findall(r'\w+', t2))
    
    # Loại bỏ từ dừng
    words1 = words1.difference(VIETNAMESE_STOPWORDS)
    words2 = words2.difference(VIETNAMESE_STOPWORDS)
    
    # Nếu một trong hai tập rỗng
    if not words1 or not words2:
        return 0.0
    
    # Tính Jaccard similarity
    intersection = len(words1.intersection(words2))
    union = len(words1.union(words2))
    
    return intersection / union 