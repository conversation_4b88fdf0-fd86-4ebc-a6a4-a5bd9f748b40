"""
<PERSON><PERSON><PERSON><PERSON> thức phân rã câu hỏi phức tạp thành các câu hỏi đơn giản hơn.

Module này cung cấp phương thức để phân rã câu hỏi phức tạp thành các câu hỏi đơn giản hơn
và tổng hợp kết quả từ các câu hỏi con.
"""

import logging
import time
from typing import Dict, Any, List, Optional

from ..utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

def decompose_query(query: str, query_decomposer=None) -> List[Dict[str, Any]]:
    """
    Phân rã câu hỏi phức tạp thành các câu hỏi đơn giản hơn.

    Args:
        query: Câu hỏi cần phân rã
        query_decomposer: Đ<PERSON>i tượng QueryDecomposer

    Returns:
        List[Dict[str, Any]]: <PERSON>h sách các câu hỏi con
    """
    if not query_decomposer:
        logger.warning("Query decomposer is not initialized, using mock decomposition")
        # Sử dụng mock decomposition
        return _mock_decompose_query(query)

    try:
        # Sử dụng QueryDecomposer để phân rã câu hỏi
        sub_queries = query_decomposer.decompose(query)
        
        # Chuyển đổi kết quả thành định dạng chuẩn
        result = []
        for i, sub_query in enumerate(sub_queries):
            result.append({
                "id": i + 1,
                "query": sub_query.get("query", ""),
                "type": sub_query.get("type", "factual"),
                "importance": sub_query.get("importance", "high"),
                "original_query": query
            })
        
        return result
    except Exception as e:
        logger.error(f"Error decomposing query: {str(e)}")
        # Fallback to mock decomposition
        return _mock_decompose_query(query)

def _mock_decompose_query(query: str) -> List[Dict[str, Any]]:
    """
    Phân rã câu hỏi giả lập cho mục đích kiểm thử.
    
    Args:
        query: Câu hỏi cần phân rã
        
    Returns:
        List[Dict[str, Any]]: Danh sách các câu hỏi con
    """
    # Phân tích câu hỏi đơn giản
    words = query.split()
    
    # Nếu câu hỏi quá ngắn, không phân rã
    if len(words) < 5:
        return [{
            "id": 1,
            "query": query,
            "type": "factual",
            "importance": "high",
            "original_query": query
        }]
    
    # Phân rã câu hỏi dựa trên độ dài
    if "và" in query.lower() or "and" in query.lower():
        parts = query.replace(" and ", " và ").split(" và ")
        result = []
        
        for i, part in enumerate(parts):
            result.append({
                "id": i + 1,
                "query": part.strip(),
                "type": "factual",
                "importance": "high",
                "original_query": query
            })
        
        return result
    
    # Phân rã câu hỏi dựa trên cấu trúc
    if "?" in query:
        parts = query.split("?")
        result = []
        
        for i, part in enumerate(parts):
            if part.strip():
                result.append({
                    "id": i + 1,
                    "query": part.strip() + "?",
                    "type": "factual",
                    "importance": "high",
                    "original_query": query
                })
        
        return result
    
    # Phân rã câu hỏi thành 2 phần
    mid = len(words) // 2
    part1 = " ".join(words[:mid])
    part2 = " ".join(words[mid:])
    
    return [
        {
            "id": 1,
            "query": part1,
            "type": "factual",
            "importance": "high",
            "original_query": query
        },
        {
            "id": 2,
            "query": part2,
            "type": "factual",
            "importance": "medium",
            "original_query": query
        }
    ]
