"""
Crawlee search implementation for deep research core.

This module provides a bridge to use C<PERSON>lee (JS-based crawler) from Python
to perform deep crawling and data extraction from web pages.
"""

import json
import os
import subprocess
import tempfile
import time
from typing import Dict, Any, List, Optional, Union
import logging

from ..utils.structured_logging import get_logger
from ..utils.playwright_installer import ensure_playwright_ready

# Create a logger
logger = get_logger(__name__)

def search_with_crawlee(
    query: str,
    start_urls: List[str] = None,
    max_pages: int = 10,
    max_depth: int = 2,
    same_domain_only: bool = True,
    timeout: int = 60000,
    wait_until: str = "networkidle",
    language: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Perform a deep web search using Crawlee to crawl and extract data from web pages.

    Args:
        query: The search query to perform
        start_urls: List of URLs to start crawling from (if None, will be generated from query)
        max_pages: Maximum number of pages to crawl
        max_depth: Maximum depth of links to follow
        same_domain_only: Whether to stay on the same domain while crawling
        timeout: Timeout in milliseconds for each page load
        wait_until: Wait until condition for page loading ('load', 'domcontentloaded', 'networkidle')
        language: Language code for search results

    Returns:
        Dictionary containing search results with extracted data
    """
    try:
        # Log the search parameters
        logger.info(f"Crawlee search for query: '{query}', language: {language}, max_pages: {max_pages}, max_depth: {max_depth}")

        # If start_urls is provided but empty, treat it as None
        if start_urls is not None and len(start_urls) == 0:
            start_urls = None

        # If no start URLs provided, generate them from a basic search
        if not start_urls:
            from .web_search_methods import search_duckduckgo, search_qwant

            # Try DuckDuckGo first
            try:
                logger.info(f"Trying to get initial URLs from DuckDuckGo for query: '{query}'")
                search_results = search_duckduckgo(query, num_results=5, language=language)
                if search_results.get("success") and search_results.get("results"):
                    start_urls = [result["url"] for result in search_results["results"]]
                    logger.info(f"Got {len(start_urls)} initial URLs from DuckDuckGo")
            except Exception as e:
                logger.warning(f"Failed to get initial URLs from DuckDuckGo: {str(e)}")

            # If DuckDuckGo fails, try Qwant
            if not start_urls:
                try:
                    logger.info(f"Trying to get initial URLs from Qwant for query: '{query}'")
                    search_results = search_qwant(query, num_results=5, language=language)
                    if search_results.get("success") and search_results.get("results"):
                        start_urls = [result["url"] for result in search_results["results"]]
                        logger.info(f"Got {len(start_urls)} initial URLs from Qwant")
                except Exception as e:
                    logger.warning(f"Failed to get initial URLs from Qwant: {str(e)}")

            # If all search methods fail, try with a fallback URL for common domains
            if not start_urls:
                logger.warning("Failed to get initial URLs from search engines, trying fallback URLs")

                # Detect language to determine appropriate fallback URLs
                try:
                    from langdetect import detect
                    query_language = detect(query)
                except:
                    query_language = language if language else "en"

                # Generate fallback URLs based on language
                if query_language == "vi":
                    # Vietnamese fallback URLs
                    fallback_urls = [
                        f"https://vi.wikipedia.org/wiki/Đặc_biệt:Tìm_kiếm?search={query}",
                        f"https://www.google.com.vn/search?q={query}",
                        f"https://coccoc.com/search?query={query}"
                    ]
                else:
                    # English/default fallback URLs
                    fallback_urls = [
                        f"https://en.wikipedia.org/wiki/Special:Search?search={query}",
                        f"https://www.google.com/search?q={query}",
                        f"https://www.bing.com/search?q={query}"
                    ]

                start_urls = fallback_urls
                logger.info(f"Using {len(start_urls)} fallback URLs for language: {query_language}")

            # If still no URLs, return error
            if not start_urls:
                return {
                    "success": False,
                    "search_method": "crawlee",
                    "engine": "crawlee",
                    "query": query,
                    "error": "Failed to generate initial URLs for crawling",
                    "results": []
                }

        # Create temporary directory to store crawlee script and results
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create crawlee script
            script_path = os.path.join(temp_dir, "crawlee_script.js")

            # Convert parameters to JSON for passing to script
            params = {
                "query": query,
                "startUrls": start_urls,
                "maxPages": max_pages,
                "maxDepth": max_depth,
                "sameDomainOnly": same_domain_only,
                "timeout": timeout,
                "waitUntil": wait_until,
                "outputPath": os.path.join(temp_dir, "results.json"),
                "language": language or "en"
            }

            # Write parameters to a JSON file
            params_path = os.path.join(temp_dir, "params.json")
            with open(params_path, "w") as f:
                json.dump(params, f)

            # Create the Crawlee script
            with open(script_path, "w") as f:
                f.write(_get_crawlee_script())

            # Check if Playwright is installed and install browsers if needed
            playwright_ready, playwright_message = ensure_playwright_ready(browsers=["chromium"])
            if not playwright_ready:
                logger.error(f"Failed to ensure Playwright is ready: {playwright_message}")
                return {
                    "success": False,
                    "search_method": "crawlee",
                    "engine": "crawlee",
                    "query": query,
                    "error": f"Failed to ensure Playwright is ready: {playwright_message}",
                    "results": []
                }

            # Check if Node.js is installed
            try:
                # First try to install Crawlee if necessary
                logger.info("Installing Crawlee and Playwright dependencies...")
                install_process = subprocess.run(
                    ["npm", "install", "crawlee", "playwright"],
                    cwd=temp_dir,
                    capture_output=True,
                    text=True,
                    timeout=300  # 5 minute timeout for installation
                )

                if install_process.returncode != 0:
                    logger.error(f"Failed to install Crawlee: {install_process.stderr}")
                    return {
                        "success": False,
                        "search_method": "crawlee",
                        "engine": "crawlee",
                        "query": query,
                        "error": f"Failed to install Crawlee: {install_process.stderr}",
                        "results": []
                    }

                # Install Playwright browsers in the Node.js environment
                logger.info("Installing Playwright browsers for Node.js...")
                playwright_install_process = subprocess.run(
                    ["npx", "playwright", "install", "chromium"],
                    cwd=temp_dir,
                    capture_output=True,
                    text=True,
                    timeout=300  # 5 minute timeout for installation
                )

                if playwright_install_process.returncode != 0:
                    logger.error(f"Failed to install Playwright browsers: {playwright_install_process.stderr}")
                    # Continue anyway, as the Python Playwright installation might be sufficient
                    logger.info("Continuing with Python Playwright installation...")

                # Run the Crawlee script
                logger.info("Running Crawlee script...")
                process = subprocess.run(
                    ["node", script_path, params_path],
                    cwd=temp_dir,
                    capture_output=True,
                    text=True,
                    timeout=timeout / 1000 * max_pages  # Convert to seconds and multiply by max pages
                )

                if process.returncode != 0:
                    logger.error(f"Failed to run Crawlee script: {process.stderr}")
                    return {
                        "success": False,
                        "search_method": "crawlee",
                        "engine": "crawlee",
                        "query": query,
                        "error": f"Failed to run Crawlee script: {process.stderr}",
                        "results": []
                    }

                # Read the results
                results_path = os.path.join(temp_dir, "results.json")
                if not os.path.exists(results_path):
                    logger.error("Crawlee script did not produce results file")
                    return {
                        "success": False,
                        "search_method": "crawlee",
                        "engine": "crawlee",
                        "query": query,
                        "error": "Crawlee script did not produce results file",
                        "results": []
                    }

                with open(results_path, "r", encoding="utf-8") as f:
                    crawlee_results = json.load(f)

                # Format results in the standard format
                formatted_results = []
                for item in crawlee_results:
                    # Calculate relevance score to query
                    relevance_score = _calculate_relevance_score(query, item.get("content", ""), item.get("title", ""))

                    formatted_result = {
                        "title": item.get("title", "Unknown Title"),
                        "url": item.get("url", ""),
                        "snippet": item.get("metaDescription", "")[:200] if item.get("metaDescription") else item.get("content", "")[:200],
                        "content": item.get("content", ""),
                        "metaDescription": item.get("metaDescription", ""),
                        "source": "crawlee",
                        "relevance_score": relevance_score
                    }
                    formatted_results.append(formatted_result)

                # Sort by relevance score
                formatted_results.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)

                return {
                    "success": True,
                    "search_method": "crawlee",
                    "engine": "crawlee",
                    "query": query,
                    "results": formatted_results,
                    "start_urls": start_urls
                }

            except FileNotFoundError:
                logger.error("Node.js is not installed. Please install Node.js to use Crawlee.")
                return {
                    "success": False,
                    "search_method": "crawlee",
                    "engine": "crawlee",
                    "query": query,
                    "error": "Node.js is not installed. Please install Node.js to use Crawlee.",
                    "results": []
                }
            except subprocess.TimeoutExpired:
                logger.error("Crawlee script timed out")
                return {
                    "success": False,
                    "search_method": "crawlee",
                    "engine": "crawlee",
                    "query": query,
                    "error": "Crawlee script timed out",
                    "results": []
                }

    except Exception as e:
        logger.error(f"Error in search_with_crawlee: {str(e)}")
        return {
            "success": False,
            "search_method": "crawlee",
            "engine": "crawlee",
            "query": query,
            "error": f"Error in search_with_crawlee: {str(e)}",
            "results": []
        }

def _calculate_relevance_score(query: str, content: str, title: str) -> float:
    """
    Calculate relevance score between query and content.

    Args:
        query: Search query
        content: Extracted content
        title: Title of the page

    Returns:
        Relevance score between 0.0 and 1.0
    """
    # Simple relevance calculation based on term frequency
    query_terms = set(query.lower().split())

    # Remove stopwords
    stopwords = {"a", "an", "the", "and", "or", "but", "in", "on", "at", "to", "for", "with", "by", "of", "from"}
    query_terms = query_terms - stopwords

    if not query_terms:
        return 0.5  # Default score if no meaningful terms

    # Calculate term frequency in content
    content_lower = content.lower()
    title_lower = title.lower()

    term_scores = []
    for term in query_terms:
        # Count occurrences
        content_count = content_lower.count(term)
        title_count = title_lower.count(term) * 3  # Title matches are weighted higher

        # Calculate term score
        term_score = min((content_count + title_count) / 10, 1.0)
        term_scores.append(term_score)

    # Average the term scores
    avg_score = sum(term_scores) / len(term_scores) if term_scores else 0.0

    # Adjust for content length (penalize very short content)
    length_factor = min(len(content) / 1000, 1.0)

    # Final score
    final_score = avg_score * 0.7 + length_factor * 0.3

    return final_score

def _get_crawlee_script() -> str:
    """
    Return the Crawlee script as a string.

    Returns:
        String containing the Crawlee script
    """
    return '''
// Crawlee script for deep web search
const fs = require('fs');
const { PlaywrightCrawler, Dataset, createPlaywrightRouter } = require('crawlee');

// Get parameters from command line
const paramsPath = process.argv[2];
if (!paramsPath) {
    console.error('No parameters file specified');
    process.exit(1);
}

// Read parameters
const params = JSON.parse(fs.readFileSync(paramsPath, 'utf8'));
const {
    query,
    startUrls,
    maxPages,
    maxDepth,
    sameDomainOnly,
    timeout,
    waitUntil,
    outputPath,
    language
} = params;

// Initialize a Map to store depth of each URL
const urlDepthMap = new Map();
startUrls.forEach(url => urlDepthMap.set(url, 0));

// Create router
const router = createPlaywrightRouter();

// Default route handler
router.addDefaultHandler(async ({ request, page, enqueueLinks, log }) => {
    const { url } = request;
    const currentDepth = urlDepthMap.get(url) || 0;

    // Log current progress
    log.info(`Processing ${url} (depth: ${currentDepth})`);

    try {
        // Wait for the page to load
        await page.waitForLoadState(waitUntil);

        // Extract title
        const title = await page.title();

        // Try to get meta description
        let metaDescription = '';
        try {
            metaDescription = await page.$eval('meta[name="description"]', el => el.getAttribute('content'));
        } catch (error) {
            // If no meta description, leave it empty
        }

        // Try to get main content from article tag
        let content = '';
        try {
            // Try different selectors for main content
            const selectors = [
                'article', 'main', '.content', '#content',
                '.post-content', '.entry-content', '.article-content'
            ];

            for (const selector of selectors) {
                if (await page.$(selector)) {
                    content = await page.$eval(selector, el => el.innerText);
                    break;
                }
            }

            // If no specific content container found, fall back to body
            if (!content) {
                content = await page.$eval('body', el => el.innerText);

                // Clean up content - remove navigation, footer, etc.
                const elementsToRemove = [
                    'nav', 'header', 'footer', '.navigation', '.menu', '.sidebar',
                    '.comments', '.related', '.advertisement', '.ads'
                ];

                for (const selector of elementsToRemove) {
                    const elements = await page.$$(selector);
                    for (const element of elements) {
                        await element.evaluate(node => node.innerText = '');
                    }
                }

                // Get cleaned body content
                content = await page.$eval('body', el => el.innerText);
            }

            // Clean up content - remove extra whitespace
            content = content.replace(/\\s+/g, ' ').trim();

        } catch (error) {
            log.warning(`Failed to extract content from ${url}: ${error.message}`);
            content = '';
        }

        // Try to extract structured data if available
        let structuredData = null;
        try {
            const jsonLdElements = await page.$$('script[type="application/ld+json"]');
            if (jsonLdElements.length > 0) {
                const jsonStrings = await Promise.all(
                    jsonLdElements.map(el => el.evaluate(node => node.textContent))
                );

                structuredData = jsonStrings
                    .map(str => {
                        try {
                            return JSON.parse(str);
                        } catch (e) {
                            return null;
                        }
                    })
                    .filter(Boolean);
            }
        } catch (error) {
            log.warning(`Failed to extract structured data from ${url}: ${error.message}`);
        }

        // Save the extracted data
        await Dataset.pushData({
            url,
            title,
            metaDescription,
            content,
            structuredData,
            depth: currentDepth,
            crawledAt: new Date().toISOString()
        });

        // If we're not at max depth, enqueue more links
        if (currentDepth < maxDepth) {
            // Configure which links to follow
            const enqueueOptions = {
                strategy: sameDomainOnly ? 'same-domain' : 'all',
                transformRequestFunction: (req) => {
                    // Store the depth of the new URL
                    urlDepthMap.set(req.url, currentDepth + 1);
                    return req;
                },
            };

            await enqueueLinks({ page, enqueueOptions });
        }
    } catch (error) {
        log.error(`Failed to process ${url}: ${error.message}`);
    }
});

// Create the PlaywrightCrawler
const crawler = new PlaywrightCrawler({
    // Use the router for all requests
    requestHandler: router,

    // Set maximum number of pages
    maxRequestsPerCrawl: maxPages,

    // Configure browser behavior
    navigationTimeoutSecs: timeout / 1000,

    // Configure headless mode and other options
    launchContext: {
        launchOptions: {
            headless: true,
        },
    },

    // Configure default headers
    preNavigationHooks: [
        async ({ page, request }) => {
            // Set language header if specified
            await page.setExtraHTTPHeaders({
                'Accept-Language': language,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            });
        }
    ],
});

// Run the crawler and save results
(async () => {
    // Run the crawler
    await crawler.run(startUrls);

    // Get dataset
    const dataset = await Dataset.open();
    const results = await dataset.getData();

    // Write results to the specified output file
    fs.writeFileSync(outputPath, JSON.stringify(results.items, null, 2));

    console.log(`Crawling complete. Results saved to ${outputPath}`);
})();
'''