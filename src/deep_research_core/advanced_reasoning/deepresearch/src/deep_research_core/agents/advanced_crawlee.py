"""
Advanced Crawlee module for Deep Research Core.

Module này cung cấp các chức năng nâng cao để crawl và scrape dữ liệu từ web
sử dụng Crawlee và Playwright, với các tối ưu hóa cho hiệu suất cao khi xử lý
số lượng lớn trang web.

Tính năng:
- Quản lý bộ nhớ thông minh để tránh rò rỉ bộ nhớ
- Xử lý đồng thời nhiều trang web với giới hạn tài nguyên
- Tự động điều chỉnh tham số dựa trên loại trang web
- Xử lý lỗi và khôi phục nâng cao
- Hỗ trợ đa ngôn ngữ và đa nền tảng
"""

import json
import os
import subprocess
import tempfile
import time
import gc
import signal
import resource
import psutil
import asyncio
import concurrent.futures
from typing import Dict, Any, List, Optional, Union, Tuple, Set, Callable
import logging
import urllib.parse
from pathlib import Path
import threading
import queue
import hashlib
from functools import lru_cache

from ..utils.structured_logging import get_logger
from ..utils.playwright_installer import ensure_playwright_ready

# Create a logger
logger = get_logger(__name__)

# Thiết lập giới hạn tài nguyên mặc định
DEFAULT_MEMORY_LIMIT_MB = 1024  # 1GB
DEFAULT_MAX_CONCURRENT_CRAWLS = 5
DEFAULT_TIMEOUT = 60  # 60 giây
DEFAULT_BATCH_SIZE = 10  # Số URL tối đa trong một batch

class ResourceManager:
    """
    Quản lý tài nguyên hệ thống cho các tiến trình crawl.

    Tính năng:
    - Giám sát và giới hạn sử dụng bộ nhớ
    - Giám sát và giới hạn sử dụng CPU
    - Tự động điều chỉnh số lượng tiến trình đồng thời
    - Phát hiện và xử lý rò rỉ tài nguyên
    """

    def __init__(
        self,
        memory_limit_mb: int = DEFAULT_MEMORY_LIMIT_MB,
        max_concurrent_processes: int = DEFAULT_MAX_CONCURRENT_CRAWLS,
        cpu_threshold: float = 0.8,  # 80% CPU
        check_interval: float = 1.0  # 1 giây
    ):
        """
        Khởi tạo ResourceManager.

        Args:
            memory_limit_mb: Giới hạn bộ nhớ tối đa (MB)
            max_concurrent_processes: Số tiến trình đồng thời tối đa
            cpu_threshold: Ngưỡng sử dụng CPU để giảm số tiến trình
            check_interval: Khoảng thời gian kiểm tra tài nguyên (giây)
        """
        self.memory_limit_mb = memory_limit_mb
        self.max_concurrent_processes = max_concurrent_processes
        self.cpu_threshold = cpu_threshold
        self.check_interval = check_interval

        # Khởi tạo thông tin trạng thái
        self.active_processes = {}
        self.resource_usage = {
            "memory_mb": 0,
            "cpu_percent": 0,
            "process_count": 0
        }

        # Khởi tạo lock cho thread safety
        self.lock = threading.RLock()

        # Khởi tạo thread giám sát
        self.monitor_thread = None
        self.stop_monitoring = threading.Event()

        # Bắt đầu giám sát
        self._start_monitoring()

    def _start_monitoring(self):
        """Bắt đầu thread giám sát tài nguyên."""
        if self.monitor_thread is None:
            self.stop_monitoring.clear()
            self.monitor_thread = threading.Thread(
                target=self._monitor_resources,
                daemon=True
            )
            self.monitor_thread.start()

    def _stop_monitoring(self):
        """Dừng thread giám sát tài nguyên."""
        if self.monitor_thread is not None:
            self.stop_monitoring.set()
            self.monitor_thread.join(timeout=2.0)
            self.monitor_thread = None

    def _monitor_resources(self):
        """Giám sát tài nguyên hệ thống."""
        while not self.stop_monitoring.is_set():
            try:
                # Lấy thông tin sử dụng tài nguyên
                memory_info = psutil.virtual_memory()
                cpu_percent = psutil.cpu_percent(interval=0.1)

                with self.lock:
                    # Cập nhật thông tin sử dụng
                    self.resource_usage["memory_mb"] = memory_info.used / (1024 * 1024)
                    self.resource_usage["cpu_percent"] = cpu_percent
                    self.resource_usage["process_count"] = len(self.active_processes)

                    # Kiểm tra và xử lý các tiến trình đã kết thúc
                    self._cleanup_finished_processes()

                    # Kiểm tra sử dụng tài nguyên
                    if memory_info.percent > 90:  # Sử dụng >90% bộ nhớ
                        self._handle_high_memory_usage()

                    if cpu_percent > self.cpu_threshold * 100:  # Sử dụng CPU cao
                        self._handle_high_cpu_usage()

            except Exception as e:
                logger.error(f"Lỗi khi giám sát tài nguyên: {str(e)}")

            # Đợi đến lần kiểm tra tiếp theo
            self.stop_monitoring.wait(self.check_interval)

    def _cleanup_finished_processes(self):
        """Dọn dẹp các tiến trình đã kết thúc."""
        to_remove = []

        for pid, process_info in self.active_processes.items():
            process = process_info["process"]

            # Kiểm tra xem tiến trình còn chạy không
            if process.poll() is not None:
                to_remove.append(pid)

        # Xóa các tiến trình đã kết thúc
        for pid in to_remove:
            del self.active_processes[pid]

    def _handle_high_memory_usage(self):
        """Xử lý khi sử dụng bộ nhớ cao."""
        # Tìm tiến trình sử dụng nhiều bộ nhớ nhất
        if not self.active_processes:
            return

        # Sắp xếp theo thời gian bắt đầu (kill tiến trình mới nhất)
        sorted_processes = sorted(
            self.active_processes.items(),
            key=lambda x: x[1]["start_time"]
        )

        # Kill tiến trình mới nhất
        newest_pid, process_info = sorted_processes[-1]
        process = process_info["process"]

        logger.warning(f"Sử dụng bộ nhớ cao, kết thúc tiến trình {newest_pid}")

        try:
            process.terminate()
            # Đợi tối đa 3 giây
            for _ in range(30):
                if process.poll() is not None:
                    break
                time.sleep(0.1)

            # Nếu vẫn chưa kết thúc, kill
            if process.poll() is None:
                process.kill()

        except Exception as e:
            logger.error(f"Lỗi khi kết thúc tiến trình {newest_pid}: {str(e)}")

    def _handle_high_cpu_usage(self):
        """Xử lý khi sử dụng CPU cao."""
        # Giảm số lượng tiến trình tối đa tạm thời
        current_max = self.max_concurrent_processes
        self.max_concurrent_processes = max(1, current_max - 1)

        logger.warning(f"Sử dụng CPU cao, giảm số tiến trình tối đa từ {current_max} xuống {self.max_concurrent_processes}")

        # Khôi phục sau 30 giây
        def restore_max_processes():
            time.sleep(30)
            with self.lock:
                self.max_concurrent_processes = current_max
                logger.info(f"Khôi phục số tiến trình tối đa về {current_max}")

        # Chạy trong thread riêng
        threading.Thread(target=restore_max_processes, daemon=True).start()

    def register_process(self, process: subprocess.Popen, metadata: Dict[str, Any] = None) -> int:
        """
        Đăng ký tiến trình để giám sát.

        Args:
            process: Tiến trình cần giám sát
            metadata: Thông tin bổ sung về tiến trình

        Returns:
            int: ID của tiến trình
        """
        with self.lock:
            # Kiểm tra số lượng tiến trình
            if len(self.active_processes) >= self.max_concurrent_processes:
                raise RuntimeError(f"Đã đạt giới hạn số tiến trình đồng thời ({self.max_concurrent_processes})")

            # Đăng ký tiến trình
            pid = process.pid
            self.active_processes[pid] = {
                "process": process,
                "start_time": time.time(),
                "metadata": metadata or {}
            }

            return pid

    def unregister_process(self, pid: int) -> bool:
        """
        Hủy đăng ký tiến trình.

        Args:
            pid: ID của tiến trình

        Returns:
            bool: True nếu thành công, False nếu không
        """
        with self.lock:
            if pid in self.active_processes:
                del self.active_processes[pid]
                return True

            return False

    def wait_for_available_slot(self, timeout: Optional[float] = None) -> bool:
        """
        Đợi cho đến khi có slot trống để chạy tiến trình mới.

        Args:
            timeout: Thời gian tối đa để đợi (giây), None để đợi vô hạn

        Returns:
            bool: True nếu có slot trống, False nếu timeout
        """
        start_time = time.time()

        while True:
            with self.lock:
                if len(self.active_processes) < self.max_concurrent_processes:
                    return True

            # Kiểm tra timeout
            if timeout is not None and time.time() - start_time > timeout:
                return False

            # Đợi một chút
            time.sleep(0.5)

    def get_resource_usage(self) -> Dict[str, Any]:
        """
        Lấy thông tin sử dụng tài nguyên hiện tại.

        Returns:
            Dict[str, Any]: Thông tin sử dụng tài nguyên
        """
        with self.lock:
            return self.resource_usage.copy()

    def get_active_processes(self) -> Dict[int, Dict[str, Any]]:
        """
        Lấy danh sách các tiến trình đang hoạt động.

        Returns:
            Dict[int, Dict[str, Any]]: Danh sách tiến trình
        """
        with self.lock:
            return {
                pid: {
                    "start_time": info["start_time"],
                    "run_time": time.time() - info["start_time"],
                    "metadata": info["metadata"]
                }
                for pid, info in self.active_processes.items()
            }

    def __del__(self):
        """Dọn dẹp khi đối tượng bị hủy."""
        self._stop_monitoring()

        # Kết thúc tất cả các tiến trình
        with self.lock:
            for pid, process_info in list(self.active_processes.items()):
                process = process_info["process"]
                try:
                    process.terminate()
                except:
                    pass

class MemoryOptimizedCrawler:
    """
    Crawler tối ưu hóa bộ nhớ cho Crawlee.

    Tính năng:
    - Xử lý batch để giảm sử dụng bộ nhớ
    - Tự động dọn dẹp bộ nhớ sau mỗi batch
    - Tự động điều chỉnh kích thước batch dựa trên tài nguyên
    - Xử lý lỗi và tự động thử lại
    """

    def __init__(
        self,
        resource_manager: Optional[ResourceManager] = None,
        batch_size: int = DEFAULT_BATCH_SIZE,
        max_retries: int = 3,
        retry_delay: float = 2.0,
        timeout: int = DEFAULT_TIMEOUT,
        memory_cleanup_interval: int = 5,  # Số batch trước khi dọn dẹp bộ nhớ
        adaptive_batch_size: bool = True
    ):
        """
        Khởi tạo MemoryOptimizedCrawler.

        Args:
            resource_manager: Đối tượng quản lý tài nguyên
            batch_size: Số URL tối đa trong một batch
            max_retries: Số lần thử lại tối đa khi gặp lỗi
            retry_delay: Thời gian chờ giữa các lần thử lại (giây)
            timeout: Thời gian chờ tối đa cho mỗi batch (giây)
            memory_cleanup_interval: Số batch trước khi dọn dẹp bộ nhớ
            adaptive_batch_size: Tự động điều chỉnh kích thước batch
        """
        # Khởi tạo resource manager nếu chưa có
        self.resource_manager = resource_manager or ResourceManager()

        # Thiết lập các tham số
        self.batch_size = batch_size
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.timeout = timeout
        self.memory_cleanup_interval = memory_cleanup_interval
        self.adaptive_batch_size = adaptive_batch_size

        # Khởi tạo thông tin trạng thái
        self.stats = {
            "total_urls": 0,
            "successful_urls": 0,
            "failed_urls": 0,
            "total_batches": 0,
            "retry_count": 0,
            "total_time": 0
        }

    def crawl_urls(
        self,
        urls: List[str],
        max_depth: int = 1,
        detailed_scraping: bool = False,
        proxy: Optional[str] = None,
        script_path: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Crawl danh sách URL với xử lý batch.

        Args:
            urls: Danh sách URL cần crawl
            max_depth: Độ sâu tối đa
            detailed_scraping: Có trích xuất thông tin chi tiết hay không
            proxy: URL proxy để sử dụng
            script_path: Đường dẫn đến script Crawlee

        Returns:
            Dict[str, Any]: Kết quả crawl
        """
        if not urls:
            return {
                "success": False,
                "error": "Không có URL để crawl",
                "results": []
            }

        # Khởi tạo kết quả
        all_results = []
        failed_urls = []
        start_time = time.time()

        # Chia thành các batch
        url_batches = [urls[i:i+self.batch_size] for i in range(0, len(urls), self.batch_size)]

        logger.info(f"Chia {len(urls)} URL thành {len(url_batches)} batch, mỗi batch tối đa {self.batch_size} URL")

        # Xử lý từng batch
        for batch_index, batch_urls in enumerate(url_batches):
            logger.info(f"Xử lý batch {batch_index + 1}/{len(url_batches)} với {len(batch_urls)} URL")

            # Đợi cho đến khi có slot trống
            if not self.resource_manager.wait_for_available_slot(timeout=self.timeout):
                logger.warning(f"Timeout khi đợi slot trống cho batch {batch_index + 1}")
                failed_urls.extend(batch_urls)
                continue

            # Crawl batch
            batch_results = self._crawl_batch(
                batch_urls,
                max_depth=max_depth,
                detailed_scraping=detailed_scraping,
                proxy=proxy,
                script_path=script_path,
                batch_index=batch_index
            )

            # Xử lý kết quả batch
            if batch_results.get("success", False):
                all_results.extend(batch_results.get("results", []))
            else:
                failed_urls.extend(batch_urls)

            # Cập nhật thống kê
            self.stats["total_batches"] += 1
            self.stats["successful_urls"] += len(batch_results.get("results", []))
            self.stats["failed_urls"] += len(batch_urls) - len(batch_results.get("results", []))

            # Dọn dẹp bộ nhớ định kỳ
            if batch_index % self.memory_cleanup_interval == 0:
                self._cleanup_memory()

            # Điều chỉnh kích thước batch nếu cần
            if self.adaptive_batch_size:
                self._adjust_batch_size()

        # Cập nhật thống kê tổng thể
        self.stats["total_urls"] += len(urls)
        self.stats["total_time"] += time.time() - start_time

        # Trả về kết quả
        return {
            "success": len(all_results) > 0,
            "results": all_results,
            "failed_urls": failed_urls,
            "count": len(all_results),
            "total_urls": len(urls),
            "stats": self.stats
        }

    def _crawl_batch(
        self,
        urls: List[str],
        max_depth: int,
        detailed_scraping: bool,
        proxy: Optional[str],
        script_path: Optional[str],
        batch_index: int
    ) -> Dict[str, Any]:
        """
        Crawl một batch URL.

        Args:
            urls: Danh sách URL trong batch
            max_depth: Độ sâu tối đa
            detailed_scraping: Có trích xuất thông tin chi tiết hay không
            proxy: URL proxy để sử dụng
            script_path: Đường dẫn đến script Crawlee
            batch_index: Chỉ số của batch

        Returns:
            Dict[str, Any]: Kết quả crawl batch
        """
        # Tạo thư mục tạm thời cho batch này
        with tempfile.TemporaryDirectory() as temp_dir:
            # Tạo file kết quả
            results_path = os.path.join(temp_dir, "batch_results.json")

            # Sử dụng script mặc định nếu không được cung cấp
            if script_path is None:
                script_dir = Path(__file__).parent
                script_path = script_dir / "crawlee_scraper.js"

                if not script_path.exists():
                    return {
                        "success": False,
                        "error": f"Script Crawlee không tồn tại: {script_path}",
                        "results": []
                    }

            # Chuyển đổi tham số thành chuỗi JSON
            urls_json = json.dumps(urls)

            # Chuẩn bị tham số cho script
            command = [
                'node',
                '--max-old-space-size=512',  # Giới hạn bộ nhớ cho Node.js
                str(script_path),
                urls_json,
                str(max_depth),
                str(len(urls) * 2),  # max_pages
                str(len(urls)),  # max_results
                str(self.timeout),
                str(detailed_scraping).lower(),
                results_path  # Thêm đường dẫn file kết quả
            ]

            # Thêm proxy nếu được cung cấp
            if proxy:
                command.append('--proxy=' + proxy)

            # Thực hiện với retry
            for attempt in range(self.max_retries):
                try:
                    logger.info(f"Batch {batch_index}: Attempt {attempt + 1}/{self.max_retries} with {len(urls)} URLs")

                    # Chạy script Crawlee
                    process = subprocess.Popen(
                        command,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True
                    )

                    # Đăng ký với resource manager
                    try:
                        self.resource_manager.register_process(
                            process,
                            metadata={
                                "batch_index": batch_index,
                                "urls": urls,
                                "attempt": attempt + 1
                            }
                        )
                    except RuntimeError as e:
                        logger.warning(f"Không thể đăng ký tiến trình: {str(e)}")
                        process.terminate()
                        time.sleep(self.retry_delay)
                        continue

                    # Đợi tiến trình hoàn thành hoặc timeout
                    try:
                        stdout, stderr = process.communicate(timeout=self.timeout)
                    except subprocess.TimeoutExpired:
                        # Nếu timeout, kill tiến trình
                        process.kill()
                        stdout, stderr = process.communicate()

                        logger.warning(f"Batch {batch_index}: Timeout sau {self.timeout} giây")

                        # Hủy đăng ký tiến trình
                        self.resource_manager.unregister_process(process.pid)

                        # Thử lại nếu còn lần thử
                        if attempt < self.max_retries - 1:
                            time.sleep(self.retry_delay)
                            continue

                        return {
                            "success": False,
                            "error": f"Timeout sau {self.timeout} giây",
                            "results": []
                        }

                    # Hủy đăng ký tiến trình
                    self.resource_manager.unregister_process(process.pid)

                    # Ghi log output nếu cần
                    if stdout:
                        logger.debug(f"Batch {batch_index}: stdout: {stdout[:500]}...")
                    if stderr:
                        logger.warning(f"Batch {batch_index}: stderr: {stderr[:500]}...")

                    # Kiểm tra xem file kết quả có tồn tại không
                    if not os.path.exists(results_path):
                        logger.warning(f"Batch {batch_index}: File kết quả không tồn tại")

                        # Thử lại nếu còn lần thử
                        if attempt < self.max_retries - 1:
                            time.sleep(self.retry_delay)
                            continue

                        return {
                            "success": False,
                            "error": "File kết quả không tồn tại",
                            "results": []
                        }

                    # Đọc kết quả từ file
                    try:
                        with open(results_path, 'r', encoding='utf-8') as f:
                            results = json.load(f)

                        return {
                            "success": True,
                            "results": results,
                            "count": len(results)
                        }
                    except json.JSONDecodeError as e:
                        logger.error(f"Batch {batch_index}: Lỗi khi đọc file JSON: {str(e)}")

                        # Thử lại nếu còn lần thử
                        if attempt < self.max_retries - 1:
                            time.sleep(self.retry_delay)
                            continue

                        return {
                            "success": False,
                            "error": f"Lỗi khi đọc file JSON: {str(e)}",
                            "results": []
                        }

                except Exception as e:
                    logger.error(f"Batch {batch_index}: Lỗi không mong đợi: {str(e)}")

                    # Cập nhật thống kê
                    self.stats["retry_count"] += 1

                    # Thử lại nếu còn lần thử
                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay * (attempt + 1))  # Tăng thời gian chờ
                        continue

                    return {
                        "success": False,
                        "error": str(e),
                        "results": []
                    }

            # Nếu tất cả các lần thử đều thất bại
            return {
                "success": False,
                "error": "Tất cả các lần thử đều thất bại",
                "results": []
            }

    def _cleanup_memory(self):
        """Dọn dẹp bộ nhớ."""
        # Gọi garbage collector
        gc.collect()

        # Ghi log
        memory_usage = psutil.Process(os.getpid()).memory_info().rss / (1024 * 1024)
        logger.info(f"Dọn dẹp bộ nhớ: {memory_usage:.2f} MB đang được sử dụng")

    def _adjust_batch_size(self):
        """Điều chỉnh kích thước batch dựa trên tài nguyên."""
        # Lấy thông tin sử dụng tài nguyên
        resource_usage = self.resource_manager.get_resource_usage()

        # Điều chỉnh kích thước batch dựa trên sử dụng bộ nhớ
        memory_percent = resource_usage["memory_mb"] / (self.resource_manager.memory_limit_mb or 1)

        if memory_percent > 0.8:  # Sử dụng >80% bộ nhớ
            # Giảm kích thước batch
            new_batch_size = max(1, int(self.batch_size * 0.8))
            if new_batch_size != self.batch_size:
                logger.info(f"Giảm kích thước batch từ {self.batch_size} xuống {new_batch_size} do sử dụng bộ nhớ cao")
                self.batch_size = new_batch_size
        elif memory_percent < 0.5 and self.stats["retry_count"] == 0:  # Sử dụng <50% bộ nhớ và không có retry
            # Tăng kích thước batch
            new_batch_size = min(DEFAULT_BATCH_SIZE * 2, int(self.batch_size * 1.2))
            if new_batch_size != self.batch_size:
                logger.info(f"Tăng kích thước batch từ {self.batch_size} lên {new_batch_size} do sử dụng bộ nhớ thấp")
                self.batch_size = new_batch_size

# Khởi tạo resource manager toàn cục
_resource_manager = ResourceManager()

def crawl_and_scrape(
    start_urls: List[str],
    max_depth: int = 2,
    max_pages: int = 20,
    max_results: int = 10,
    timeout: int = 60,
    detailed_scraping: bool = False,
    proxy: Optional[str] = None,
    max_retries: int = 3,
    use_memory_optimization: bool = True,
    batch_size: Optional[int] = None
) -> Dict[str, Any]:
    """
    Sử dụng Crawlee và Playwright để crawl và scrape dữ liệu từ các trang web.

    Phiên bản cải tiến với tối ưu hóa bộ nhớ và xử lý batch.

    Args:
        start_urls: Danh sách URL khởi đầu
        max_depth: Độ sâu tối đa để crawl
        max_pages: Số trang tối đa để crawl
        max_results: Số kết quả tối đa để trả về
        timeout: Thời gian chờ tối đa (giây)
        detailed_scraping: Có trích xuất thông tin chi tiết hay không
        proxy: URL proxy để sử dụng (ví dụ: "http://user:<EMAIL>:8080")
        max_retries: Số lần thử lại tối đa khi gặp lỗi
        use_memory_optimization: Có sử dụng tối ưu hóa bộ nhớ hay không
        batch_size: Kích thước batch (số URL trong một lần xử lý)

    Returns:
        Dictionary chứa kết quả crawl và scrape
    """
    logger.info(f"Bắt đầu crawl và scrape {len(start_urls)} URLs (độ sâu: {max_depth}, số trang tối đa: {max_pages})")

    # Kiểm tra Playwright đã được cài đặt chưa
    playwright_ready, playwright_message = ensure_playwright_ready(browsers=["chromium"])
    if not playwright_ready:
        logger.error(f"Playwright không sẵn sàng: {playwright_message}")
        return {
            "success": False,
            "error": f"Playwright không sẵn sàng: {playwright_message}",
            "results": []
        }

    # Lấy đường dẫn đến script Crawlee
    script_dir = Path(__file__).parent
    script_path = script_dir / "crawlee_scraper.js"

    # Kiểm tra xem script có tồn tại không
    if not script_path.exists():
        logger.error(f"Script Crawlee không tồn tại: {script_path}")
        return {
            "success": False,
            "error": f"Script Crawlee không tồn tại: {script_path}",
            "results": []
        }

    # Sử dụng MemoryOptimizedCrawler nếu được yêu cầu
    if use_memory_optimization and len(start_urls) > 3:  # Chỉ sử dụng cho nhiều URL
        logger.info(f"Sử dụng MemoryOptimizedCrawler với {len(start_urls)} URLs")

        # Khởi tạo crawler với batch size tùy chỉnh nếu được cung cấp
        crawler = MemoryOptimizedCrawler(
            resource_manager=_resource_manager,
            batch_size=batch_size or DEFAULT_BATCH_SIZE,
            max_retries=max_retries,
            timeout=timeout,
            adaptive_batch_size=True
        )

        # Crawl URLs
        result = crawler.crawl_urls(
            urls=start_urls,
            max_depth=max_depth,
            detailed_scraping=detailed_scraping,
            proxy=proxy,
            script_path=script_path
        )

        # Thêm thông tin bổ sung vào kết quả
        if result.get("success", False):
            result["search_method"] = "advanced_crawlee_optimized"
            result["engine"] = "crawlee_playwright_memory_optimized"
            result["timestamp"] = time.time()

            # Giới hạn số kết quả trả về
            if len(result.get("results", [])) > max_results:
                result["results"] = result["results"][:max_results]
                result["count"] = len(result["results"])
                result["note"] = f"Kết quả đã được giới hạn xuống {max_results} (tổng số: {result.get('total_urls', 0)})"

        return result

    # Sử dụng phương pháp cũ nếu không sử dụng tối ưu hóa bộ nhớ
    logger.info(f"Sử dụng phương pháp crawl tiêu chuẩn với {len(start_urls)} URLs")

    # Tạo thư mục tạm thời để lưu kết quả
    with tempfile.TemporaryDirectory() as temp_dir:
        results_path = os.path.join(temp_dir, "crawlee_results.json")

        # Chuyển đổi tham số thành chuỗi JSON
        start_urls_json = json.dumps(start_urls)

        # Chuẩn bị tham số cho script
        command = [
            'node',
            '--max-old-space-size=512',  # Giới hạn bộ nhớ cho Node.js
            str(script_path),
            start_urls_json,
            str(max_depth),
            str(max_pages),
            str(max_results),
            str(timeout),
            str(detailed_scraping).lower(),
            results_path  # Thêm đường dẫn file kết quả
        ]

        # Thêm proxy nếu được cung cấp
        if proxy:
            command.append('--proxy=' + proxy)

        # Thực hiện với retry
        for attempt in range(max_retries):
            try:
                logger.info(f"Crawlee attempt {attempt + 1}/{max_retries} with {len(start_urls)} URLs")

                # Chạy script Crawlee
                process = subprocess.Popen(
                    command,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )

                # Đăng ký với resource manager
                try:
                    _resource_manager.register_process(
                        process,
                        metadata={
                            "urls": start_urls,
                            "attempt": attempt + 1
                        }
                    )
                except RuntimeError as e:
                    logger.warning(f"Không thể đăng ký tiến trình: {str(e)}")

                # Đợi tiến trình hoàn thành hoặc timeout
                try:
                    stdout, stderr = process.communicate(timeout=timeout * 2)
                except subprocess.TimeoutExpired:
                    # Nếu timeout, kill tiến trình
                    process.kill()
                    stdout, stderr = process.communicate()

                    logger.error(f"Crawlee script timed out after {timeout * 2} seconds (attempt {attempt + 1})")

                    # Hủy đăng ký tiến trình
                    _resource_manager.unregister_process(process.pid)

                    # Thử lại nếu còn lần thử
                    if attempt < max_retries - 1:
                        time.sleep(2 * (attempt + 1))
                        continue

                    return {
                        "success": False,
                        "error": f"Crawlee script timed out after {timeout * 2} seconds",
                        "results": []
                    }

                # Hủy đăng ký tiến trình
                _resource_manager.unregister_process(process.pid)

                # Ghi log output nếu cần
                if stdout:
                    logger.debug(f"Crawlee stdout: {stdout[:500]}...")
                if stderr:
                    logger.warning(f"Crawlee stderr: {stderr[:500]}...")

                # Kiểm tra xem file kết quả có tồn tại không
                if not os.path.exists(results_path):
                    logger.warning(f"Results file not found after successful run. Retrying...")

                    # Thử lại nếu còn lần thử
                    if attempt < max_retries - 1:
                        time.sleep(2 * (attempt + 1))
                        continue

                    return {
                        "success": False,
                        "error": "Results file not found after successful run",
                        "results": []
                    }

                # Đọc kết quả từ file
                try:
                    with open(results_path, 'r', encoding='utf-8') as f:
                        results = json.load(f)

                    return {
                        "success": True,
                        "search_method": "advanced_crawlee",
                        "engine": "crawlee_playwright",
                        "results": results,
                        "count": len(results),
                        "timestamp": time.time()
                    }
                except json.JSONDecodeError as e:
                    logger.error(f"Error decoding JSON results: {str(e)}")
                    if attempt < max_retries - 1:
                        time.sleep(2 * (attempt + 1))
                        continue
                    return {
                        "success": False,
                        "error": f"Error decoding JSON results: {str(e)}",
                        "results": []
                    }
                except Exception as e:
                    logger.error(f"Error reading results file: {str(e)}")
                    if attempt < max_retries - 1:
                        time.sleep(2 * (attempt + 1))
                        continue
                    return {
                        "success": False,
                        "error": f"Error reading results file: {str(e)}",
                        "results": []
                    }

            except FileNotFoundError:
                logger.error("Node.js or Crawlee not installed. Please install Node.js and run 'npm install crawlee'")
                return {
                    "success": False,
                    "error": "Node.js or Crawlee not installed. Please install Node.js and run 'npm install crawlee'",
                    "results": []
                }
            except Exception as e:
                logger.error(f"Unexpected error in crawl_and_scrape (attempt {attempt + 1}): {str(e)}")
                if attempt < max_retries - 1:
                    # Thêm delay trước khi thử lại
                    time.sleep(2 * (attempt + 1))
                    continue
                return {
                    "success": False,
                    "error": str(e),
                    "results": []
                }

        # Nếu tất cả các lần thử đều thất bại
        return {
            "success": False,
            "error": "All retry attempts failed",
            "results": []
        }

def search_with_advanced_crawlee(
    query: str,
    num_results: int = 10,
    max_depth: int = 2,
    max_pages: int = 20,
    timeout: int = 60,
    detailed_scraping: bool = False,
    language: str = "en",
    use_memory_optimization: bool = True,
    batch_size: Optional[int] = None
) -> Dict[str, Any]:
    """
    Tìm kiếm web với Crawlee và Playwright.

    Phiên bản cải tiến với tối ưu hóa bộ nhớ và xử lý batch.

    Phương pháp này sẽ:
    1. Sử dụng SearXNG để lấy kết quả ban đầu
    2. Sử dụng Crawlee để crawl và scrape các trang web từ kết quả SearXNG
    3. Tối ưu hóa bộ nhớ và tài nguyên khi xử lý nhiều URL

    Args:
        query: Truy vấn tìm kiếm
        num_results: Số kết quả trả về
        max_depth: Độ sâu tối đa để crawl
        max_pages: Số trang tối đa để crawl
        timeout: Thời gian chờ tối đa (giây)
        detailed_scraping: Có trích xuất thông tin chi tiết hay không
        language: Mã ngôn ngữ cho kết quả tìm kiếm
        use_memory_optimization: Có sử dụng tối ưu hóa bộ nhớ hay không
        batch_size: Kích thước batch (số URL trong một lần xử lý)

    Returns:
        Dictionary chứa kết quả tìm kiếm
    """
    logger.info(f"Tìm kiếm với Advanced Crawlee: '{query}' (ngôn ngữ: {language}, tối ưu bộ nhớ: {use_memory_optimization})")

    # Bước 1: Sử dụng SearXNG để lấy kết quả ban đầu
    from .searxng_search import search_with_fallback

    searxng_results = search_with_fallback(
        query=query,
        num_results=max(num_results, 10),  # Lấy ít nhất 10 kết quả để có đủ URL cho crawling
        language=language,
        max_retries=3
    )

    # Nếu không có kết quả từ SearXNG, thử sử dụng Crawlee trực tiếp
    if not searxng_results.get("success") or not searxng_results.get("results"):
        logger.warning("Không có kết quả từ SearXNG, thử sử dụng Crawlee trực tiếp")

        # Tạo URL tìm kiếm trực tiếp dựa trên ngôn ngữ
        if language == "vi":
            # URL tìm kiếm cho tiếng Việt
            search_urls = [
                f"https://www.google.com.vn/search?q={urllib.parse.quote(query)}&hl=vi",
                f"https://duckduckgo.com/?q={urllib.parse.quote(query)}&kl=vn-vi",
                f"https://coccoc.com/search?query={urllib.parse.quote(query)}",
                f"https://vi.wikipedia.org/wiki/Đặc_biệt:Tìm_kiếm?search={urllib.parse.quote(query)}",
                f"https://www.bing.com/search?q={urllib.parse.quote(query)}&setlang=vi"
            ]
        else:
            # URL tìm kiếm cho các ngôn ngữ khác
            search_urls = [
                f"https://www.google.com/search?q={urllib.parse.quote(query)}",
                f"https://duckduckgo.com/?q={urllib.parse.quote(query)}",
                f"https://www.bing.com/search?q={urllib.parse.quote(query)}",
                f"https://en.wikipedia.org/wiki/Special:Search?search={urllib.parse.quote(query)}"
            ]

        logger.info(f"Crawling trực tiếp với các URL: {search_urls[:3]}")

        # Sử dụng Crawlee để crawl các URL tìm kiếm trực tiếp
        crawlee_results = crawl_and_scrape(
            start_urls=search_urls[:3],  # Giới hạn số URL để tránh bị chặn
            max_depth=max_depth,
            max_pages=max_pages,
            max_results=num_results,
            timeout=timeout,
            detailed_scraping=detailed_scraping,
            use_memory_optimization=use_memory_optimization,
            batch_size=batch_size
        )

        # Nếu crawling thất bại, thử với các nguồn đáng tin cậy cụ thể
        if not crawlee_results.get("success") or not crawlee_results.get("results"):
            logger.warning("Crawling trực tiếp thất bại, thử với các nguồn đáng tin cậy")

            # Tạo danh sách các nguồn đáng tin cậy dựa trên ngôn ngữ và chủ đề
            trusted_sources = []

            # Phân tích truy vấn để xác định chủ đề
            query_lower = query.lower()

            # Xác định chủ đề dựa trên từ khóa trong truy vấn
            if any(keyword in query_lower for keyword in ["kinh tế", "tài chính", "economy", "finance", "business"]):
                if language == "vi":
                    trusted_sources = [
                        f"https://vnexpress.net/kinh-doanh",
                        f"https://tuoitre.vn/kinh-te.htm",
                        f"https://cafef.vn/",
                        f"https://www.gso.gov.vn/",  # Tổng cục Thống kê
                        f"https://www.mof.gov.vn/webcenter/portal/mof/r/m/trangchu"  # Bộ Tài chính
                    ]
                else:
                    trusted_sources = [
                        f"https://www.worldbank.org/en/country/vietnam/overview",
                        f"https://www.imf.org/en/Countries/VNM",
                        f"https://www.adb.org/countries/viet-nam/economy",
                        f"https://tradingeconomics.com/vietnam/indicators",
                        f"https://www.vietnam-briefing.com/category/news/"
                    ]
            elif any(keyword in query_lower for keyword in ["công nghệ", "technology", "tech", "it", "software"]):
                if language == "vi":
                    trusted_sources = [
                        f"https://vnexpress.net/so-hoa",
                        f"https://genk.vn/",
                        f"https://tinhte.vn/",
                        f"https://vietnamnet.vn/cong-nghe"
                    ]
                else:
                    trusted_sources = [
                        f"https://techcrunch.com/",
                        f"https://www.wired.com/",
                        f"https://www.theverge.com/",
                        f"https://www.cnet.com/"
                    ]
            elif any(keyword in query_lower for keyword in ["y tế", "sức khỏe", "health", "medical", "medicine"]):
                if language == "vi":
                    trusted_sources = [
                        f"https://suckhoedoisong.vn/",
                        f"https://www.vinmec.com/vi/tin-tuc/",
                        f"https://www.moh.gov.vn/"  # Bộ Y tế
                    ]
                else:
                    trusted_sources = [
                        f"https://www.who.int/",
                        f"https://www.mayoclinic.org/",
                        f"https://www.nih.gov/",
                        f"https://www.webmd.com/"
                    ]
            else:
                # Nguồn đáng tin cậy chung
                if language == "vi":
                    trusted_sources = [
                        f"https://vnexpress.net/",
                        f"https://tuoitre.vn/",
                        f"https://dantri.com.vn/",
                        f"https://vietnamnet.vn/",
                        f"https://vi.wikipedia.org/"
                    ]
                else:
                    trusted_sources = [
                        f"https://www.bbc.com/news",
                        f"https://www.reuters.com/",
                        f"https://www.theguardian.com/",
                        f"https://en.wikipedia.org/",
                        f"https://www.britannica.com/"
                    ]

            logger.info(f"Thử crawl với {len(trusted_sources)} nguồn đáng tin cậy")

            # Thử crawl các nguồn đáng tin cậy
            trusted_crawl_results = crawl_and_scrape(
                start_urls=trusted_sources,
                max_depth=1,  # Giảm độ sâu để tập trung vào trang chính
                max_pages=max_pages,
                max_results=num_results,
                timeout=timeout,
                detailed_scraping=detailed_scraping,
                use_memory_optimization=use_memory_optimization,
                batch_size=batch_size
            )

            # Nếu crawl nguồn đáng tin cậy thành công
            if trusted_crawl_results.get("success") and trusted_crawl_results.get("results"):
                logger.info(f"Crawl nguồn đáng tin cậy thành công, tìm thấy {len(trusted_crawl_results['results'])} kết quả")
                trusted_crawl_results["query"] = query
                return trusted_crawl_results

            # Nếu vẫn thất bại, tạo kết quả giả
            logger.warning("Tất cả các phương pháp crawl đều thất bại, tạo kết quả giả")

            # Tạo kết quả giả với thông tin cơ bản
            dummy_results = []
            for i, url in enumerate(search_urls[:3]):
                dummy_results.append({
                    "title": f"Search result for: {query} ({i+1})",
                    "url": url,
                    "content": f"This is a placeholder result for the query: {query}. The actual content could not be retrieved due to technical limitations.",
                    "source": "direct_search",
                    "timestamp": time.time()
                })

            return {
                "success": True,
                "search_method": "advanced_crawlee",
                "engine": "direct_search",
                "query": query,
                "results": dummy_results,
                "count": len(dummy_results),
                "timestamp": time.time(),
                "note": "These are placeholder results. The actual content could not be retrieved."
            }

        # Thêm truy vấn vào kết quả
        crawlee_results["query"] = query

        return crawlee_results

    # Bước 2: Lấy URL từ kết quả SearXNG để crawl
    start_urls = [result["url"] for result in searxng_results.get("results", []) if "url" in result]
    if not start_urls:
        logger.warning("Không có URL từ kết quả SearXNG để crawl")
        return searxng_results

    logger.info(f"Đã lấy {len(start_urls)} URL từ SearXNG để crawl")

    # Bước 3: Sử dụng Crawlee để crawl và scrape các URL từ SearXNG
    crawlee_results = crawl_and_scrape(
        start_urls=start_urls[:5],  # Giới hạn số URL để tránh crawl quá nhiều
        max_depth=max_depth,
        max_pages=max_pages,
        max_results=num_results,
        timeout=timeout,
        detailed_scraping=detailed_scraping,
        use_memory_optimization=use_memory_optimization,
        batch_size=batch_size
    )

    # Nếu crawling thất bại, trả về kết quả SearXNG
    if not crawlee_results.get("success") or not crawlee_results.get("results"):
        logger.warning("Crawling thất bại, trả về kết quả SearXNG")
        return searxng_results

    # Thêm truy vấn vào kết quả
    crawlee_results["query"] = query

    return crawlee_results
