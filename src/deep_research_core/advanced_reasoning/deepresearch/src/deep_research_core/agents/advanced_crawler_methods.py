"""
Additional methods for AdvancedCrawler.

This module provides additional methods for the AdvancedCrawler class.
"""

import os
import time
import json
import logging
import hashlib
import random
from typing import Dict, Any, List, Optional, Tuple, Set, Union, Callable
from urllib.parse import urlparse, urljoin, urldefrag

# Create logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def _handle_pagination(self, url: str, html_content: str, links: List[str]) -> List[Dict[str, Any]]:
    """
    Handle pagination for a page.
    
    Args:
        url: URL of the page
        html_content: HTML content of the page
        links: Links found on the page
        
    Returns:
        List of dictionaries with page information from pagination
    """
    if not self.pagination_handler:
        return []
    
    try:
        # Detect pagination
        pagination_info = self.pagination_handler.detect_pagination(url, html_content, links)
        
        if not pagination_info.get("has_pagination"):
            return []
        
        # Define page handler function
        def page_handler(page_url: str) -> Tuple[str, List[str]]:
            """Handle a single page for pagination."""
            result = self._crawl_url(page_url, 0, {"source": "pagination"})
            if result.get("success"):
                return result.get("html_content", ""), result.get("links", [])
            return "", []
        
        # Handle pagination based on type
        if self.use_playwright and self.playwright_context:
            # Create a new page for pagination
            page = self.playwright_context.new_page()
            
            try:
                # Navigate to URL
                page.goto(url, wait_until="networkidle", timeout=self.timeout * 1000)
                
                # Handle pagination with Playwright
                pagination_results = self.pagination_handler.handle_pagination(
                    url,
                    pagination_info,
                    page_handler,
                    page
                )
                
                # Process results
                processed_results = []
                for result in pagination_results:
                    # Extract content if needed
                    if self.extract_content and "html_content" in result:
                        content = self._extract_content(
                            result["html_content"],
                            result["url"],
                            result.get("title", "")
                        )
                        result["content"] = content
                    
                    processed_results.append(result)
                
                return processed_results
            
            finally:
                # Close the page
                page.close()
        else:
            # Handle pagination without Playwright
            pagination_results = self.pagination_handler.handle_pagination(
                url,
                pagination_info,
                page_handler
            )
            
            # Process results
            processed_results = []
            for result in pagination_results:
                # Extract content if needed
                if self.extract_content and "html_content" in result:
                    content = self._extract_content(
                        result["html_content"],
                        result["url"],
                        result.get("title", "")
                    )
                    result["content"] = content
                
                processed_results.append(result)
            
            return processed_results
    
    except Exception as e:
        logger.error(f"Error handling pagination for {url}: {str(e)}")
        return []

def _extract_content(self, html_content: str, url: str, title: str) -> str:
    """
    Extract main content from HTML.
    
    Args:
        html_content: HTML content to extract from
        url: URL of the page
        title: Title of the page
        
    Returns:
        Extracted content
    """
    try:
        from bs4 import BeautifulSoup
        from readability import Document
        
        # Use readability to extract main content
        doc = Document(html_content)
        content_html = doc.summary()
        
        # Parse with BeautifulSoup
        soup = BeautifulSoup(content_html, "html.parser")
        
        # Remove unwanted elements
        for element in soup.select("script, style, meta, link, noscript, iframe, form, button, input"):
            element.decompose()
        
        # Get text content
        content = soup.get_text(separator="\n", strip=True)
        
        # Limit content length if needed
        if self.max_content_length > 0 and len(content) > self.max_content_length:
            content = content[:self.max_content_length] + "..."
        
        # Optimize for LLM if needed
        if self.optimize_for_llm:
            content = self._optimize_for_llm(content, title, url)
        
        return content
    
    except Exception as e:
        logger.error(f"Error extracting content from {url}: {str(e)}")
        return ""

def _optimize_for_llm(self, content: str, title: str, url: str) -> str:
    """
    Optimize content for LLM input.
    
    Args:
        content: Content to optimize
        title: Title of the page
        url: URL of the page
        
    Returns:
        Optimized content
    """
    # Add title and URL as context
    optimized = f"Title: {title}\nURL: {url}\n\n{content}"
    
    # Remove excessive whitespace
    optimized = "\n".join(line.strip() for line in optimized.split("\n") if line.strip())
    
    # Remove duplicate newlines
    while "\n\n\n" in optimized:
        optimized = optimized.replace("\n\n\n", "\n\n")
    
    return optimized

def _get_user_agent(self) -> str:
    """
    Get a user agent string.
    
    Returns:
        User agent string
    """
    if self.user_agent_rotation and self.user_agent_list:
        return random.choice(self.user_agent_list)
    
    # Default user agents
    default_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    ]
    
    return random.choice(default_agents)

def _get_proxy(self) -> Optional[str]:
    """
    Get a proxy server.
    
    Returns:
        Proxy server URL or None
    """
    if self.use_proxy and self.proxy_list:
        return random.choice(self.proxy_list)
    return None

def _check_rate_limit(self) -> bool:
    """
    Check if rate limit is exceeded.
    
    Returns:
        True if within rate limit, False otherwise
    """
    current_time = time.time()
    
    # Reset counter if window has passed
    if current_time - self.rate_limit_start > self.rate_limit_window:
        self.rate_limit_start = current_time
        self.request_count = 0
    
    # Check if limit exceeded
    if self.request_count >= self.rate_limit:
        return False
    
    return True

def _record_request(self) -> None:
    """Record a request for rate limiting."""
    current_time = time.time()
    
    # Reset counter if window has passed
    if current_time - self.rate_limit_start > self.rate_limit_window:
        self.rate_limit_start = current_time
        self.request_count = 0
    
    # Increment counter
    self.request_count += 1

def _create_result(self, success: bool, results: Optional[List[Dict[str, Any]]] = None, error: Optional[str] = None) -> Dict[str, Any]:
    """
    Create a result dictionary.
    
    Args:
        success: Whether the crawl was successful
        results: List of page results
        error: Error message if any
        
    Returns:
        Result dictionary
    """
    result = {
        "success": success,
        "query": self.query,
        "timestamp": time.time(),
        "stats": self.stats,
        "results": results or []
    }
    
    if error:
        result["error"] = error
    
    return result

def _cleanup(self) -> None:
    """Clean up resources."""
    # Close Playwright if used
    if self.use_playwright and hasattr(self, "playwright"):
        try:
            if self.playwright_context:
                self.playwright_context.close()
            
            if self.playwright_browser:
                self.playwright_browser.close()
            
            self.playwright.stop()
            
            logger.info("Playwright resources cleaned up")
        
        except Exception as e:
            logger.error(f"Error cleaning up Playwright resources: {str(e)}")
    
    # Save final state
    if self.enable_state_saving and hasattr(self, "link_queue"):
        try:
            # Force save state
            if hasattr(self.link_queue, "_save_state"):
                self.link_queue._save_state()
            
            logger.info("Crawler state saved")
        
        except Exception as e:
            logger.error(f"Error saving crawler state: {str(e)}")

# Add methods to AdvancedCrawler class
def add_methods_to_advanced_crawler():
    """Add methods to AdvancedCrawler class."""
    from .advanced_crawler import AdvancedCrawler
    
    AdvancedCrawler._handle_pagination = _handle_pagination
    AdvancedCrawler._extract_content = _extract_content
    AdvancedCrawler._optimize_for_llm = _optimize_for_llm
    AdvancedCrawler._get_user_agent = _get_user_agent
    AdvancedCrawler._get_proxy = _get_proxy
    AdvancedCrawler._check_rate_limit = _check_rate_limit
    AdvancedCrawler._record_request = _record_request
    AdvancedCrawler._create_result = _create_result
    AdvancedCrawler._cleanup = _cleanup
