"""
ContentExtractor - <PERSON><PERSON><PERSON> trích xuất nội dung chính cho AdaptiveCrawler.

Module này cung cấp các chức năng trích xuất nội dung chính từ các trang web,
với khả năng xử lý nhiều loại trang web và ngôn ngữ khác nhau.
"""

import re
import logging
import html
import unicodedata
from typing import Dict, Any, List, Optional, Union, Tuple, Set
from urllib.parse import urlparse
from bs4 import BeautifulSoup, Comment
import trafilatura
from readability import Document
import langdetect

from ..utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

class ContentExtractor:
    """
    ContentExtractor class.

    Cung cấp các chức năng trích xuất nội dung chính từ các trang web.
    """

    def __init__(
        self,
        min_content_length: int = 500,
        max_content_length: int = 100000,
        detect_language: bool = True,
        supported_languages: Optional[List[str]] = None,
        extract_metadata: bool = True,
        extract_images: bool = True,
        extract_links: bool = True,
        extract_tables: bool = True,
        extract_title: bool = True,
        extract_author: bool = True,
        extract_date: bool = True,
        extract_description: bool = True,
        extract_keywords: bool = True,
        extract_canonical: bool = True,
        extract_sitename: bool = True,
        extract_categories: bool = True,
        extract_tags: bool = True,
        extract_feed: bool = True,
        extract_comments: bool = False,
        extract_breadcrumbs: bool = True,
        extract_next_page: bool = True,
        extract_prev_page: bool = True,
        extract_pagination: bool = True,
        verbose: bool = False
    ):
        """
        Khởi tạo ContentExtractor.

        Args:
            min_content_length: Độ dài tối thiểu của nội dung
            max_content_length: Độ dài tối đa của nội dung
            detect_language: Có phát hiện ngôn ngữ hay không
            supported_languages: Danh sách các ngôn ngữ được hỗ trợ
            extract_metadata: Có trích xuất metadata hay không
            extract_images: Có trích xuất hình ảnh hay không
            extract_links: Có trích xuất links hay không
            extract_tables: Có trích xuất bảng hay không
            extract_title: Có trích xuất tiêu đề hay không
            extract_author: Có trích xuất tác giả hay không
            extract_date: Có trích xuất ngày hay không
            extract_description: Có trích xuất mô tả hay không
            extract_keywords: Có trích xuất từ khóa hay không
            extract_canonical: Có trích xuất URL chính thức hay không
            extract_sitename: Có trích xuất tên trang web hay không
            extract_categories: Có trích xuất danh mục hay không
            extract_tags: Có trích xuất thẻ hay không
            extract_feed: Có trích xuất feed hay không
            extract_comments: Có trích xuất bình luận hay không
            extract_breadcrumbs: Có trích xuất breadcrumbs hay không
            extract_next_page: Có trích xuất trang tiếp theo hay không
            extract_prev_page: Có trích xuất trang trước hay không
            extract_pagination: Có trích xuất phân trang hay không
            verbose: Ghi log chi tiết
        """
        self.min_content_length = min_content_length
        self.max_content_length = max_content_length
        self.detect_language = detect_language
        self.supported_languages = supported_languages or [
            'vi', 'en', 'zh', 'ja', 'ko', 'fr', 'de', 'es', 'it', 'ru', 'pt', 'ar'
        ]
        self.extract_metadata = extract_metadata
        self.extract_images = extract_images
        self.extract_links = extract_links
        self.extract_tables = extract_tables
        self.extract_title = extract_title
        self.extract_author = extract_author
        self.extract_date = extract_date
        self.extract_description = extract_description
        self.extract_keywords = extract_keywords
        self.extract_canonical = extract_canonical
        self.extract_sitename = extract_sitename
        self.extract_categories = extract_categories
        self.extract_tags = extract_tags
        self.extract_feed = extract_feed
        self.extract_comments = extract_comments
        self.extract_breadcrumbs = extract_breadcrumbs
        self.extract_next_page = extract_next_page
        self.extract_prev_page = extract_prev_page
        self.extract_pagination = extract_pagination
        self.verbose = verbose

    def extract_content(self, url: str, html_content: str, page=None) -> Dict[str, Any]:
        """
        Trích xuất nội dung chính từ nội dung HTML.

        Args:
            url: URL của trang web
            html_content: Nội dung HTML của trang web
            page: Đối tượng Playwright Page (nếu có)

        Returns:
            Dict[str, Any]: Nội dung đã trích xuất
        """
        # Kết quả trích xuất
        result = {
            'url': url,
            'content': '',
            'content_length': 0,
            'content_type': 'text',
            'language': '',
            'title': '',
            'metadata': {},
            'images': [],
            'links': [],
            'tables': [],
            'success': False,
            'error': ''
        }

        try:
            # Phát hiện ngôn ngữ
            if self.detect_language:
                try:
                    language = langdetect.detect(html_content)
                    result['language'] = language

                    # Kiểm tra ngôn ngữ được hỗ trợ
                    if language not in self.supported_languages:
                        if self.verbose:
                            logger.warning(f"Ngôn ngữ không được hỗ trợ: {language}")
                except Exception as e:
                    if self.verbose:
                        logger.warning(f"Lỗi khi phát hiện ngôn ngữ: {str(e)}")

            # Sử dụng nhiều phương pháp trích xuất và chọn kết quả tốt nhất
            content_trafilatura = self._extract_with_trafilatura(url, html_content)
            content_readability = self._extract_with_readability(url, html_content)
            content_custom = self._extract_with_custom_algorithm(url, html_content)

            # Chọn kết quả tốt nhất
            content = self._select_best_content(content_trafilatura, content_readability, content_custom)

            # Cập nhật kết quả
            result['content'] = content['content']
            result['content_length'] = len(content['content'])
            result['title'] = content['title']
            result['metadata'] = content['metadata']

            # Trích xuất hình ảnh, links, bảng nếu cần
            if self.extract_images:
                result['images'] = content['images']

            if self.extract_links:
                result['links'] = content['links']

            if self.extract_tables:
                result['tables'] = content['tables']

            # Kiểm tra độ dài nội dung
            if result['content_length'] < self.min_content_length:
                if self.verbose:
                    logger.warning(f"Nội dung quá ngắn: {result['content_length']} < {self.min_content_length}")
                result['error'] = f"Nội dung quá ngắn: {result['content_length']} < {self.min_content_length}"
            elif result['content_length'] > self.max_content_length:
                if self.verbose:
                    logger.warning(f"Nội dung quá dài: {result['content_length']} > {self.max_content_length}")
                # Cắt nội dung
                result['content'] = result['content'][:self.max_content_length]
                result['content_length'] = self.max_content_length

            # Đánh dấu thành công
            result['success'] = True

        except Exception as e:
            if self.verbose:
                logger.error(f"Lỗi khi trích xuất nội dung: {str(e)}")
            result['error'] = str(e)

        return result

    def _extract_with_trafilatura(self, url: str, html_content: str) -> Dict[str, Any]:
        """
        Trích xuất nội dung sử dụng thư viện trafilatura.

        Args:
            url: URL của trang web
            html_content: Nội dung HTML của trang web

        Returns:
            Dict[str, Any]: Nội dung đã trích xuất
        """
        result = {
            'content': '',
            'title': '',
            'metadata': {},
            'images': [],
            'links': [],
            'tables': []
        }

        try:
            # Trích xuất nội dung với trafilatura
            extracted = trafilatura.extract(
                html_content,
                url=url,
                include_links=self.extract_links,
                include_images=self.extract_images,
                include_tables=self.extract_tables,
                include_comments=self.extract_comments,
                output_format='json',
                with_metadata=self.extract_metadata
            )

            if extracted:
                # Parse JSON
                import json
                data = json.loads(extracted)

                # Lấy nội dung
                result['content'] = data.get('text', '')

                # Lấy tiêu đề
                result['title'] = data.get('title', '')

                # Lấy metadata
                result['metadata'] = {
                    'author': data.get('author', ''),
                    'date': data.get('date', ''),
                    'description': data.get('description', ''),
                    'sitename': data.get('sitename', ''),
                    'categories': data.get('categories', []),
                    'tags': data.get('tags', []),
                    'language': data.get('language', '')
                }

                # Lấy hình ảnh
                if self.extract_images and 'images' in data:
                    result['images'] = data.get('images', [])

                # Lấy links
                if self.extract_links and 'links' in data:
                    result['links'] = data.get('links', [])

                # Lấy bảng
                if self.extract_tables and 'tables' in data:
                    result['tables'] = data.get('tables', [])

        except Exception as e:
            if self.verbose:
                logger.warning(f"Lỗi khi trích xuất với trafilatura: {str(e)}")

        return result

    def _extract_with_readability(self, url: str, html_content: str) -> Dict[str, Any]:
        """
        Trích xuất nội dung sử dụng thư viện readability.

        Args:
            url: URL của trang web
            html_content: Nội dung HTML của trang web

        Returns:
            Dict[str, Any]: Nội dung đã trích xuất
        """
        result = {
            'content': '',
            'title': '',
            'metadata': {},
            'images': [],
            'links': [],
            'tables': []
        }

        try:
            # Trích xuất nội dung với readability
            doc = Document(html_content)

            # Lấy tiêu đề
            result['title'] = doc.title()

            # Lấy nội dung
            content_html = doc.summary()

            # Parse HTML để lấy text và các thành phần khác
            soup = BeautifulSoup(content_html, 'html.parser')

            # Lấy nội dung text
            result['content'] = soup.get_text(separator=' ', strip=True)

            # Lấy hình ảnh
            if self.extract_images:
                images = []
                for img in soup.find_all('img'):
                    src = img.get('src', '')
                    if src:
                        images.append({
                            'url': src,
                            'alt': img.get('alt', ''),
                            'title': img.get('title', '')
                        })
                result['images'] = images

            # Lấy links
            if self.extract_links:
                links = []
                for a in soup.find_all('a'):
                    href = a.get('href', '')
                    if href:
                        links.append({
                            'url': href,
                            'text': a.get_text(strip=True),
                            'title': a.get('title', '')
                        })
                result['links'] = links

            # Lấy bảng
            if self.extract_tables:
                tables = []
                for table in soup.find_all('table'):
                    rows = []
                    for tr in table.find_all('tr'):
                        cells = []
                        for td in tr.find_all(['td', 'th']):
                            cells.append(td.get_text(strip=True))
                        if cells:
                            rows.append(cells)
                    if rows:
                        tables.append(rows)
                result['tables'] = tables

            # Lấy metadata
            result['metadata'] = {
                'author': '',
                'date': '',
                'description': '',
                'sitename': '',
                'categories': [],
                'tags': [],
                'language': ''
            }

            # Trích xuất metadata từ HTML gốc nếu cần
            if self.extract_metadata:
                soup_full = BeautifulSoup(html_content, 'html.parser')

                # Lấy author
                author_meta = soup_full.find('meta', {'name': ['author', 'Author', 'AUTHOR']})
                if author_meta:
                    result['metadata']['author'] = author_meta.get('content', '')

                # Lấy description
                desc_meta = soup_full.find('meta', {'name': ['description', 'Description', 'DESCRIPTION']})
                if desc_meta:
                    result['metadata']['description'] = desc_meta.get('content', '')

                # Lấy keywords
                keywords_meta = soup_full.find('meta', {'name': ['keywords', 'Keywords', 'KEYWORDS']})
                if keywords_meta:
                    keywords = keywords_meta.get('content', '')
                    if keywords:
                        result['metadata']['tags'] = [k.strip() for k in keywords.split(',')]

        except Exception as e:
            if self.verbose:
                logger.warning(f"Lỗi khi trích xuất với readability: {str(e)}")

        return result

    def _extract_with_custom_algorithm(self, url: str, html_content: str) -> Dict[str, Any]:
        """
        Trích xuất nội dung sử dụng thuật toán tùy chỉnh.

        Args:
            url: URL của trang web
            html_content: Nội dung HTML của trang web

        Returns:
            Dict[str, Any]: Nội dung đã trích xuất
        """
        result = {
            'content': '',
            'title': '',
            'metadata': {},
            'images': [],
            'links': [],
            'tables': []
        }

        try:
            # Parse HTML
            soup = BeautifulSoup(html_content, 'html.parser')

            # Loại bỏ các phần tử không cần thiết
            for element in soup.find_all(['script', 'style', 'iframe', 'noscript', 'header', 'footer', 'nav']):
                element.decompose()

            # Loại bỏ comments
            for comment in soup.find_all(text=lambda text: isinstance(text, Comment)):
                comment.extract()

            # Lấy tiêu đề
            title_tag = soup.find('title')
            if title_tag:
                result['title'] = title_tag.get_text(strip=True)

            # Tìm các thẻ có thể chứa nội dung chính
            content_tags = []

            # Tìm thẻ article
            articles = soup.find_all('article')
            if articles:
                content_tags.extend(articles)

            # Tìm thẻ main
            main = soup.find('main')
            if main:
                content_tags.append(main)

            # Tìm thẻ div có class hoặc id chứa từ khóa liên quan đến nội dung
            content_keywords = ['content', 'article', 'post', 'entry', 'main', 'body', 'text']
            for keyword in content_keywords:
                for div in soup.find_all('div', class_=lambda c: c and keyword in c.lower()):
                    content_tags.append(div)
                for div in soup.find_all('div', id=lambda i: i and keyword in i.lower()):
                    content_tags.append(div)

            # Nếu không tìm thấy thẻ nào, sử dụng body
            if not content_tags:
                body = soup.find('body')
                if body:
                    content_tags.append(body)

            # Trích xuất nội dung từ các thẻ đã tìm thấy
            content_text = []
            for tag in content_tags:
                # Lấy tất cả các đoạn văn bản
                paragraphs = tag.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'li'])
                for p in paragraphs:
                    text = p.get_text(strip=True)
                    if text and len(text) > 20:  # Chỉ lấy đoạn văn bản có ý nghĩa
                        content_text.append(text)

            # Kết hợp các đoạn văn bản
            result['content'] = '\n\n'.join(content_text)

            # Trích xuất hình ảnh
            if self.extract_images:
                images = []
                for tag in content_tags:
                    for img in tag.find_all('img'):
                        src = img.get('src', '')
                        if src:
                            images.append({
                                'url': src,
                                'alt': img.get('alt', ''),
                                'title': img.get('title', '')
                            })
                result['images'] = images

            # Trích xuất links
            if self.extract_links:
                links = []
                for tag in content_tags:
                    for a in tag.find_all('a'):
                        href = a.get('href', '')
                        if href:
                            links.append({
                                'url': href,
                                'text': a.get_text(strip=True),
                                'title': a.get('title', '')
                            })
                result['links'] = links

            # Trích xuất bảng
            if self.extract_tables:
                tables = []
                for tag in content_tags:
                    for table in tag.find_all('table'):
                        rows = []
                        for tr in table.find_all('tr'):
                            cells = []
                            for td in tr.find_all(['td', 'th']):
                                cells.append(td.get_text(strip=True))
                            if cells:
                                rows.append(cells)
                        if rows:
                            tables.append(rows)
                result['tables'] = tables

            # Trích xuất metadata
            result['metadata'] = {
                'author': '',
                'date': '',
                'description': '',
                'sitename': '',
                'categories': [],
                'tags': [],
                'language': ''
            }

            # Trích xuất metadata
            if self.extract_metadata:
                # Lấy author
                author_meta = soup.find('meta', {'name': ['author', 'Author', 'AUTHOR']})
                if author_meta and author_meta.get('content'):
                    result['metadata']['author'] = author_meta.get('content', '')

                # Lấy description
                desc_meta = soup.find('meta', {'name': ['description', 'Description', 'DESCRIPTION']})
                if desc_meta and desc_meta.get('content'):
                    result['metadata']['description'] = desc_meta.get('content', '')

                # Lấy keywords
                keywords_meta = soup.find('meta', {'name': ['keywords', 'Keywords', 'KEYWORDS']})
                if keywords_meta and keywords_meta.get('content'):
                    keywords = keywords_meta.get('content', '')
                    if keywords and isinstance(keywords, str):
                        result['metadata']['tags'] = [k.strip() for k in keywords.split(',')]

                # Lấy sitename
                og_site_name = soup.find('meta', {'property': 'og:site_name'})
                if og_site_name and og_site_name.get('content'):
                    result['metadata']['sitename'] = og_site_name.get('content', '')

                # Lấy date
                date_meta = soup.find('meta', {'property': ['article:published_time', 'og:published_time']})
                if date_meta and date_meta.get('content'):
                    result['metadata']['date'] = date_meta.get('content', '')

        except Exception as e:
            if self.verbose:
                logger.warning(f"Lỗi khi trích xuất với thuật toán tùy chỉnh: {str(e)}")

        return result

    def _select_best_content(self, content1: Dict[str, Any], content2: Dict[str, Any], content3: Dict[str, Any]) -> Dict[str, Any]:
        """
        Chọn nội dung tốt nhất từ ba kết quả trích xuất.

        Args:
            content1: Kết quả trích xuất từ trafilatura
            content2: Kết quả trích xuất từ readability
            content3: Kết quả trích xuất từ thuật toán tùy chỉnh

        Returns:
            Dict[str, Any]: Nội dung tốt nhất
        """
        # Tính điểm cho mỗi kết quả
        score1 = self._calculate_content_score(content1)
        score2 = self._calculate_content_score(content2)
        score3 = self._calculate_content_score(content3)

        # Chọn kết quả có điểm cao nhất
        if score1 >= score2 and score1 >= score3:
            return content1
        elif score2 >= score1 and score2 >= score3:
            return content2
        else:
            return content3

    def _calculate_content_score(self, content: Dict[str, Any]) -> float:
        """
        Tính điểm cho nội dung đã trích xuất.

        Args:
            content: Nội dung đã trích xuất

        Returns:
            float: Điểm của nội dung
        """
        score = 0.0

        # Điểm cho độ dài nội dung
        content_length = len(content.get('content', ''))
        if content_length > 0:
            # Nội dung dài hơn được ưu tiên, nhưng không quá dài
            if content_length < self.min_content_length:
                score += content_length / self.min_content_length
            elif content_length > self.max_content_length:
                score += 0.5
            else:
                score += 1.0

        # Điểm cho tiêu đề
        if content.get('title', ''):
            score += 0.5

        # Điểm cho metadata
        metadata = content.get('metadata', {})
        if metadata.get('author', ''):
            score += 0.1
        if metadata.get('date', ''):
            score += 0.1
        if metadata.get('description', ''):
            score += 0.1
        if metadata.get('sitename', ''):
            score += 0.1

        # Điểm cho hình ảnh
        images = content.get('images', [])
        if images:
            score += min(0.3, len(images) * 0.05)

        # Điểm cho links
        links = content.get('links', [])
        if links:
            score += min(0.2, len(links) * 0.02)

        # Điểm cho bảng
        tables = content.get('tables', [])
        if tables:
            score += min(0.2, len(tables) * 0.05)

        return score