// crawlee_scraper.js
// <PERSON>ript để thực hiện cả crawling và scraping với <PERSON>lee và Playwright

const { PlaywrightCrawler, Dataset, createPlaywrightRouter, KeyValueStore } = require('crawlee');
const { writeFileSync } = require('fs');

// <PERSON>h sách User-Agent để xoay vòng
const USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
    'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
    'Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59'
];

// Lấy User-Agent ngẫu nhiên
function getRandomUserAgent() {
    return USER_AGENTS[Math.floor(Math.random() * USER_AGENTS.length)];
}

// Hàm delay ngẫu nhiên
function getRandomDelay(min = 2000, max = 5000) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Hàm trích xuất thông tin cơ bản từ trang web
const extractBasicInfo = (page) => {
    return page.evaluate(() => {
        // Trích xuất tiêu đề, nội dung, v.v.
        const title = document.title;
        const content = document.body.innerText;

        // Trích xuất các đoạn văn bản có liên quan
        const paragraphs = Array.from(document.querySelectorAll('p, h1, h2, h3, h4, h5, h6'))
            .map(el => el.innerText)
            .filter(text => text.length > 50);

        // Trích xuất meta description
        const metaDescription = document.querySelector('meta[name="description"]')?.content || '';

        return {
            title,
            content: paragraphs.join('\n\n').substring(0, 5000),
            url: window.location.href,
            meta_description: metaDescription
        };
    });
};

// Hàm trích xuất thông tin chi tiết từ trang web
const extractDetailedInfo = (page) => {
    return page.evaluate(() => {
        // Trích xuất tất cả thông tin có thể
        const title = document.title;
        const h1 = document.querySelector('h1')?.innerText || '';
        const metaDescription = document.querySelector('meta[name="description"]')?.content || '';
        const metaKeywords = document.querySelector('meta[name="keywords"]')?.content || '';
        
        // Trích xuất tất cả văn bản
        const allText = document.body.innerText;
        
        // Trích xuất các đoạn văn bản có cấu trúc
        const paragraphs = Array.from(document.querySelectorAll('p'))
            .map(el => el.innerText)
            .filter(text => text.length > 20);
            
        // Trích xuất các tiêu đề
        const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'))
            .map(el => ({ level: el.tagName.toLowerCase(), text: el.innerText }))
            .filter(heading => heading.text.trim().length > 0);
            
        // Trích xuất các liên kết
        const links = Array.from(document.querySelectorAll('a[href]'))
            .map(el => ({ 
                text: el.innerText.trim(), 
                href: el.href,
                isExternal: el.href.startsWith('http') && !el.href.includes(window.location.hostname)
            }))
            .filter(link => link.text.length > 0);
            
        // Trích xuất hình ảnh
        const images = Array.from(document.querySelectorAll('img[src]'))
            .map(el => ({
                src: el.src,
                alt: el.alt || '',
                width: el.width,
                height: el.height
            }))
            .filter(img => img.src && !img.src.startsWith('data:'));
            
        // Trích xuất bảng
        const tables = Array.from(document.querySelectorAll('table'))
            .map(table => {
                const rows = Array.from(table.querySelectorAll('tr'))
                    .map(row => Array.from(row.querySelectorAll('td, th'))
                        .map(cell => cell.innerText.trim())
                    );
                return rows;
            });
            
        // Trích xuất JSON-LD
        const jsonLdScripts = Array.from(document.querySelectorAll('script[type="application/ld+json"]'))
            .map(script => {
                try {
                    return JSON.parse(script.innerText);
                } catch (e) {
                    return null;
                }
            })
            .filter(data => data !== null);
            
        // Trích xuất Open Graph
        const openGraph = {};
        document.querySelectorAll('meta[property^="og:"]').forEach(meta => {
            const property = meta.getAttribute('property').replace('og:', '');
            openGraph[property] = meta.getAttribute('content');
        });
        
        return {
            title,
            h1,
            meta_description: metaDescription,
            meta_keywords: metaKeywords,
            content: paragraphs.join('\n\n'),
            headings,
            links,
            images,
            tables,
            structured_data: jsonLdScripts,
            open_graph: openGraph,
            url: window.location.href,
            full_text: allText.substring(0, 10000) // Giới hạn độ dài
        };
    });
};

// Hàm chính để crawl và scrape
async function crawlAndScrape(startUrls, maxDepth, maxPages, maxResults, timeout, detailedScraping = false) {
    const results = [];
    const visitedUrls = new Set();
    const outputFile = 'crawlee_results.json';

    // Tạo router để xử lý các request
    const router = createPlaywrightRouter();

    // Xử lý trang web
    router.addDefaultHandler(async ({ request, page, enqueueLinks }) => {
        const url = request.url;
        const currentDepth = request.userData.depth || 0;
        
        console.log(`Processing ${url} (depth: ${currentDepth})`);

        try {
            // Thiết lập User-Agent ngẫu nhiên
            await page.setExtraHTTPHeaders({
                'User-Agent': getRandomUserAgent(),
                'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            });

            // Thêm delay ngẫu nhiên trước khi truy cập trang
            await new Promise(resolve => setTimeout(resolve, getRandomDelay()));

            // Trích xuất thông tin từ trang
            const pageInfo = detailedScraping 
                ? await extractDetailedInfo(page)
                : await extractBasicInfo(page);

            // Thêm vào kết quả nếu chưa đủ
            if (results.length < maxResults && !visitedUrls.has(pageInfo.url)) {
                results.push(pageInfo);
                visitedUrls.add(pageInfo.url);
                console.log(`Scraped: ${pageInfo.url}`);
                
                // Lưu kết quả vào Dataset
                await Dataset.pushData(pageInfo);
            }

            // Thêm delay trước khi tiếp tục
            await new Promise(resolve => setTimeout(resolve, getRandomDelay(1000, 3000)));

            // Thêm các liên kết vào hàng đợi nếu chưa đạt độ sâu tối đa
            if (currentDepth < maxDepth) {
                await enqueueLinks({
                    strategy: 'same-domain', // Chỉ crawl trong cùng domain
                    transformRequestFunction: (req) => {
                        req.userData.depth = currentDepth + 1;
                        return req;
                    }
                });
            }
        } catch (error) {
            console.error(`Error processing ${url}: ${error.message}`);
        }
    });

    // Tạo crawler
    const crawler = new PlaywrightCrawler({
        // Sử dụng router đã tạo
        requestHandler: router,
        
        // Giới hạn số lượng trang
        maxRequestsPerCrawl: maxPages,
        
        // Cấu hình timeout
        navigationTimeoutSecs: timeout,
        requestHandlerTimeoutSecs: timeout,
        
        // Giới hạn số lượng request đồng thời
        maxConcurrency: 2,
        
        // Cấu hình browser
        launchContext: {
            launchOptions: {
                headless: true,
                args: [
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--disable-setuid-sandbox',
                    '--no-sandbox',
                    '--disable-web-security',
                    '--disable-features=IsolateOrigins,site-per-process'
                ]
            }
        },
        
        // Xử lý lỗi
        failedRequestHandler: async ({ request, error }) => {
            console.error(`Request ${request.url} failed: ${error.message}`);
        }
    });

    // Bắt đầu crawl
    await crawler.run(
        startUrls.map(url => ({
            url,
            userData: { depth: 0 }
        }))
    );

    // Lưu kết quả vào file
    writeFileSync(outputFile, JSON.stringify(results, null, 2));

    return {
        success: true,
        results: results,
        count: results.length
    };
}

// Xử lý tham số dòng lệnh
const args = process.argv.slice(2);
const startUrls = JSON.parse(args[0]);
const maxDepth = parseInt(args[1]);
const maxPages = parseInt(args[2]);
const maxResults = parseInt(args[3]);
const timeout = parseInt(args[4]);
const detailedScraping = args[5] === 'true';

// Chạy crawler
crawlAndScrape(startUrls, maxDepth, maxPages, maxResults, timeout, detailedScraping)
    .then(result => {
        console.log(`Crawling and scraping completed. Processed ${result.count} pages.`);
        process.exit(0);
    })
    .catch(error => {
        console.error(`Error during crawling and scraping: ${error.message}`);
        process.exit(1);
    });
