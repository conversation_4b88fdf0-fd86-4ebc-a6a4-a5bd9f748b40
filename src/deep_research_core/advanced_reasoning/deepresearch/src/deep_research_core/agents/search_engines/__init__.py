"""
<PERSON><PERSON><PERSON> công cụ tìm kiếm cho WebSearchAgent.

Module này cung cấp các công cụ tìm kiếm khác nhau để sử dụng với WebSearchAgent,
bao gồm các công cụ tìm kiếm chung và chuyên biệt.
"""

# Import các công cụ tìm kiếm chuyên biệt
try:
    from deep_research_core.agents.search_engines.specialized_engines import ScholarSearchEngine
    SCHOLAR_SEARCH_AVAILABLE = True
except ImportError:
    SCHOLAR_SEARCH_AVAILABLE = False

try:
    from deep_research_core.agents.search_engines.specialized_engines_part2 import NewsSearchEngine
    NEWS_SEARCH_AVAILABLE = True
except ImportError:
    NEWS_SEARCH_AVAILABLE = False

try:
    from deep_research_core.agents.search_engines.specialized_engines_part3 import BookSearchEngine
    BOOK_SEARCH_AVAILABLE = True
except ImportError:
    BOOK_SEARCH_AVAILABLE = False

# Danh sách các công cụ tìm kiếm chuyên biệt
SPECIALIZED_ENGINES = []

if SCHOLAR_SEARCH_AVAILABLE:
    SPECIALIZED_ENGINES.append(ScholarSearchEngine)

if NEWS_SEARCH_AVAILABLE:
    SPECIALIZED_ENGINES.append(NewsSearchEngine)

if BOOK_SEARCH_AVAILABLE:
    SPECIALIZED_ENGINES.append(BookSearchEngine)

__all__ = ['SPECIALIZED_ENGINES']

if SCHOLAR_SEARCH_AVAILABLE:
    __all__.append('ScholarSearchEngine')

if NEWS_SEARCH_AVAILABLE:
    __all__.append('NewsSearchEngine')

if BOOK_SEARCH_AVAILABLE:
    __all__.append('BookSearchEngine')
