"""
<PERSON><PERSON><PERSON> công cụ tìm kiếm chuyên bi<PERSON> (Phần 3).

<PERSON><PERSON>le này cung cấp các công cụ tìm kiếm chuyên biệt bổ sung cho các lĩnh vực cụ thể
như sách, v.v.
"""

import re
import json
import time
import logging
import random
import requests
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
from urllib.parse import quote_plus, urlencode

from deep_research_core.agents.web_search_agent import SearchResult

# Lấy logger
logger = logging.getLogger(__name__)

class BookSearchEngine:
    """Công cụ tìm kiếm sách."""
    
    name = "book"
    display_name = "Book Search"
    
    def __init__(self, **kwargs):
        """
        Khởi tạo BookSearchEngine.
        
        Args:
            **kwargs: Tham số bổ sung
        """
        self.timeout = kwargs.get("timeout", 10)
        self.user_agent = kwargs.get("user_agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
        self.proxy = kwargs.get("proxy", None)
        self.use_google_books = kwargs.get("use_google_books", True)
        self.google_books_api_key = kwargs.get("google_books_api_key", None)
    
    def search(self, query: str, num_results: int = 10, **kwargs) -> Dict[str, Any]:
        """
        Thực hiện tìm kiếm sách.
        
        Args:
            query: Truy vấn tìm kiếm
            num_results: Số lượng kết quả
            **kwargs: Tham số bổ sung
            
        Returns:
            Từ điển chứa kết quả tìm kiếm
        """
        if self.use_google_books:
            return self._search_with_google_books(query, num_results, **kwargs)
        else:
            return self._search_with_open_library(query, num_results, **kwargs)
    
    def _search_with_google_books(self, query: str, num_results: int = 10, **kwargs) -> Dict[str, Any]:
        """
        Thực hiện tìm kiếm sách sử dụng Google Books API.
        
        Args:
            query: Truy vấn tìm kiếm
            num_results: Số lượng kết quả
            **kwargs: Tham số bổ sung
            
        Returns:
            Từ điển chứa kết quả tìm kiếm
        """
        try:
            # Chuẩn bị tham số
            params = {
                "q": query,
                "maxResults": min(num_results, 40)  # Google Books API giới hạn 40 kết quả mỗi trang
            }
            
            # Thêm API key nếu có
            if self.google_books_api_key:
                params["key"] = self.google_books_api_key
            
            # Thêm tham số bổ sung
            if "language" in kwargs:
                params["langRestrict"] = kwargs["language"]
            if "filter" in kwargs:
                params["filter"] = kwargs["filter"]
            if "print_type" in kwargs:
                params["printType"] = kwargs["print_type"]
            if "order_by" in kwargs:
                params["orderBy"] = kwargs["order_by"]
            
            # Gửi yêu cầu
            response = requests.get(
                "https://www.googleapis.com/books/v1/volumes",
                params=params,
                timeout=self.timeout
            )
            
            # Kiểm tra phản hồi
            if response.status_code != 200:
                return {
                    "success": False,
                    "error": f"Google Books API returned status code {response.status_code}",
                    "results": []
                }
            
            # Phân tích phản hồi
            data = response.json()
            
            # Chuyển đổi kết quả
            results = []
            for item in data.get("items", []):
                volume_info = item.get("volumeInfo", {})
                
                # Trích xuất thông tin
                title = volume_info.get("title", "")
                subtitle = volume_info.get("subtitle", "")
                authors = volume_info.get("authors", [])
                publisher = volume_info.get("publisher", "")
                published_date = volume_info.get("publishedDate", "")
                description = volume_info.get("description", "")
                page_count = volume_info.get("pageCount", 0)
                categories = volume_info.get("categories", [])
                language = volume_info.get("language", "")
                
                # Trích xuất URL
                preview_link = volume_info.get("previewLink", "")
                info_link = volume_info.get("infoLink", "")
                canonical_link = volume_info.get("canonicalVolumeLink", "")
                url = canonical_link or info_link or preview_link
                
                # Trích xuất hình ảnh
                image_links = volume_info.get("imageLinks", {})
                thumbnail = image_links.get("thumbnail", "")
                
                # Tạo đoạn trích
                snippet = subtitle or description
                if snippet and len(snippet) > 200:
                    snippet = snippet[:197] + "..."
                
                # Tạo kết quả
                result = SearchResult(
                    title=title,
                    url=url,
                    snippet=snippet,
                    source=self.name,
                    metadata={
                        "authors": authors,
                        "publisher": publisher,
                        "published_date": published_date,
                        "page_count": page_count,
                        "categories": categories,
                        "language": language,
                        "thumbnail": thumbnail,
                        "type": "book"
                    }
                )
                results.append(result)
            
            return {
                "success": True,
                "results": results,
                "engine": self.name
            }
            
        except Exception as e:
            logger.error(f"Lỗi khi tìm kiếm với Google Books API: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "results": []
            }
    
    def _search_with_open_library(self, query: str, num_results: int = 10, **kwargs) -> Dict[str, Any]:
        """
        Thực hiện tìm kiếm sách sử dụng Open Library API.
        
        Args:
            query: Truy vấn tìm kiếm
            num_results: Số lượng kết quả
            **kwargs: Tham số bổ sung
            
        Returns:
            Từ điển chứa kết quả tìm kiếm
        """
        try:
            # Chuẩn bị tham số
            params = {
                "q": query,
                "limit": min(num_results, 100)
            }
            
            # Thêm tham số bổ sung
            if "language" in kwargs:
                params["language"] = kwargs["language"]
            
            # Gửi yêu cầu
            response = requests.get(
                "https://openlibrary.org/search.json",
                params=params,
                timeout=self.timeout
            )
            
            # Kiểm tra phản hồi
            if response.status_code != 200:
                return {
                    "success": False,
                    "error": f"Open Library API returned status code {response.status_code}",
                    "results": []
                }
            
            # Phân tích phản hồi
            data = response.json()
            
            # Chuyển đổi kết quả
            results = []
            for doc in data.get("docs", [])[:num_results]:
                # Trích xuất thông tin
                title = doc.get("title", "")
                authors = doc.get("author_name", [])
                publisher = doc.get("publisher", [""])[0] if doc.get("publisher") else ""
                published_year = doc.get("first_publish_year", "")
                languages = doc.get("language", [])
                subjects = doc.get("subject", [])
                
                # Tạo URL
                key = doc.get("key", "")
                url = f"https://openlibrary.org{key}" if key else ""
                
                # Tạo đoạn trích
                snippet = f"Published by {publisher} in {published_year}" if publisher and published_year else ""
                if subjects:
                    snippet += f". Subjects: {', '.join(subjects[:3])}"
                
                # Tạo URL hình ảnh
                cover_id = doc.get("cover_i")
                thumbnail = f"https://covers.openlibrary.org/b/id/{cover_id}-M.jpg" if cover_id else ""
                
                # Tạo kết quả
                result = SearchResult(
                    title=title,
                    url=url,
                    snippet=snippet,
                    source=self.name,
                    metadata={
                        "authors": authors,
                        "publisher": publisher,
                        "published_year": published_year,
                        "languages": languages,
                        "subjects": subjects,
                        "thumbnail": thumbnail,
                        "type": "book"
                    }
                )
                results.append(result)
            
            return {
                "success": True,
                "results": results,
                "engine": self.name
            }
            
        except Exception as e:
            logger.error(f"Lỗi khi tìm kiếm với Open Library API: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "results": []
            }
