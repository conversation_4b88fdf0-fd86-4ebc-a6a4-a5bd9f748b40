"""
<PERSON><PERSON><PERSON> công cụ tìm kiếm chuyên biệt.

<PERSON><PERSON><PERSON> này cung cấp các công cụ tìm kiếm chuyên biệt cho các lĩnh vực cụ thể
như học thuật, tin tức, s<PERSON><PERSON>, v.v.
"""

import re
import json
import time
import logging
import random
import requests
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
from urllib.parse import quote_plus, urlencode

from deep_research_core.agents.web_search_agent import SearchResult

# Lấy logger
logger = logging.getLogger(__name__)

import re
import json
import time
import logging
import random
import requests
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
from urllib.parse import quote_plus, urlencode

from deep_research_core.agents.web_search_agent import SearchResult

# Lấy logger
logger = logging.getLogger(__name__)

class ScholarSearchEngine:
    """<PERSON><PERSON><PERSON> cụ tìm kiếm học thuật."""

    name = "scholar"
    display_name = "Scholar Search"

    def __init__(self, **kwargs):
        """
        Khởi tạo ScholarSearchEngine.

        Args:
            **kwargs: Tham số bổ sung
        """
        self.timeout = kwargs.get("timeout", 10)
        self.user_agent = kwargs.get("user_agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
        self.proxy = kwargs.get("proxy", None)
        self.use_serpapi = kwargs.get("use_serpapi", False)
        self.serpapi_key = kwargs.get("serpapi_key", None)

    def search(self, query: str, num_results: int = 10, **kwargs) -> Dict[str, Any]:
        """
        Thực hiện tìm kiếm học thuật.

        Args:
            query: Truy vấn tìm kiếm
            num_results: Số lượng kết quả
            **kwargs: Tham số bổ sung

        Returns:
            Từ điển chứa kết quả tìm kiếm
        """
        if self.use_serpapi and self.serpapi_key:
            return self._search_with_serpapi(query, num_results, **kwargs)
        else:
            return self._search_with_scraping(query, num_results, **kwargs)

    def _search_with_serpapi(self, query: str, num_results: int = 10, **kwargs) -> Dict[str, Any]:
        """
        Thực hiện tìm kiếm học thuật sử dụng SerpAPI.

        Args:
            query: Truy vấn tìm kiếm
            num_results: Số lượng kết quả
            **kwargs: Tham số bổ sung

        Returns:
            Từ điển chứa kết quả tìm kiếm
        """
        try:
            # Chuẩn bị tham số
            params = {
                "engine": "google_scholar",
                "q": query,
                "api_key": self.serpapi_key,
                "num": num_results
            }

            # Thêm tham số bổ sung
            if "year_low" in kwargs:
                params["as_ylo"] = kwargs["year_low"]
            if "year_high" in kwargs:
                params["as_yhi"] = kwargs["year_high"]

            # Gửi yêu cầu
            response = requests.get(
                "https://serpapi.com/search",
                params=params,
                timeout=self.timeout
            )

            # Kiểm tra phản hồi
            if response.status_code != 200:
                return {
                    "success": False,
                    "error": f"SerpAPI returned status code {response.status_code}",
                    "results": []
                }

            # Phân tích phản hồi
            data = response.json()

            # Chuyển đổi kết quả
            results = []
            for item in data.get("organic_results", []):
                result = SearchResult(
                    title=item.get("title", ""),
                    url=item.get("link", ""),
                    snippet=item.get("snippet", ""),
                    source=self.name,
                    metadata={
                        "authors": item.get("publication_info", {}).get("authors", []),
                        "year": item.get("publication_info", {}).get("year"),
                        "citations": item.get("inline_links", {}).get("cited_by", {}).get("total", 0),
                        "type": "academic"
                    }
                )
                results.append(result)

            return {
                "success": True,
                "results": results,
                "engine": self.name
            }

        except Exception as e:
            logger.error(f"Lỗi khi tìm kiếm với SerpAPI: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "results": []
            }

    def _search_with_scraping(self, query: str, num_results: int = 10, **kwargs) -> Dict[str, Any]:
        """
        Thực hiện tìm kiếm học thuật sử dụng scraping.

        Args:
            query: Truy vấn tìm kiếm
            num_results: Số lượng kết quả
            **kwargs: Tham số bổ sung

        Returns:
            Từ điển chứa kết quả tìm kiếm
        """
        try:
            # Chuẩn bị URL
            base_url = "https://scholar.google.com/scholar"
            params = {
                "q": query,
                "hl": kwargs.get("language", "en"),
                "num": min(num_results, 20)  # Google Scholar giới hạn 20 kết quả mỗi trang
            }

            # Thêm tham số bổ sung
            if "year_low" in kwargs:
                params["as_ylo"] = kwargs["year_low"]
            if "year_high" in kwargs:
                params["as_yhi"] = kwargs["year_high"]

            # Chuẩn bị headers
            headers = {
                "User-Agent": self.user_agent,
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Language": "en-US,en;q=0.5",
                "Referer": "https://scholar.google.com/",
                "DNT": "1",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1"
            }

            # Chuẩn bị proxies
            proxies = None
            if self.proxy:
                proxies = {
                    "http": self.proxy,
                    "https": self.proxy
                }

            # Gửi yêu cầu
            response = requests.get(
                base_url,
                params=params,
                headers=headers,
                proxies=proxies,
                timeout=self.timeout
            )

            # Kiểm tra phản hồi
            if response.status_code != 200:
                return {
                    "success": False,
                    "error": f"Google Scholar returned status code {response.status_code}",
                    "results": []
                }

            # Phân tích HTML
            soup = BeautifulSoup(response.text, "html.parser")

            # Trích xuất kết quả
            results = []
            for item in soup.select(".gs_r.gs_or.gs_scl"):
                # Trích xuất tiêu đề và URL
                title_elem = item.select_one(".gs_rt a")
                title = title_elem.text if title_elem else "No title"
                url = title_elem["href"] if title_elem and "href" in title_elem.attrs else ""

                # Trích xuất đoạn trích
                snippet_elem = item.select_one(".gs_rs")
                snippet = snippet_elem.text if snippet_elem else ""

                # Trích xuất thông tin bổ sung
                footer_elem = item.select_one(".gs_a")
                footer_text = footer_elem.text if footer_elem else ""

                # Phân tích thông tin tác giả và năm
                authors = []
                year = None

                if footer_text:
                    # Tách phần tác giả
                    parts = footer_text.split(" - ")
                    if len(parts) > 0:
                        # Tách danh sách tác giả
                        author_text = parts[0]
                        authors = [a.strip() for a in author_text.split(",")]

                    # Tìm năm xuất bản
                    year_match = re.search(r"\b(19|20)\d{2}\b", footer_text)
                    if year_match:
                        year = year_match.group(0)

                # Trích xuất số lượng trích dẫn
                citations = 0
                cite_elem = item.select_one(".gs_fl a:nth-child(3)")
                if cite_elem:
                    cite_text = cite_elem.text
                    cite_match = re.search(r"Cited by (\d+)", cite_text)
                    if cite_match:
                        citations = int(cite_match.group(1))

                # Tạo kết quả
                result = SearchResult(
                    title=title,
                    url=url,
                    snippet=snippet,
                    source=self.name,
                    metadata={
                        "authors": authors,
                        "year": year,
                        "citations": citations,
                        "type": "academic"
                    }
                )
                results.append(result)

                # Kiểm tra số lượng kết quả
                if len(results) >= num_results:
                    break

            return {
                "success": True,
                "results": results,
                "engine": self.name
            }

        except Exception as e:
            logger.error(f"Lỗi khi tìm kiếm với Google Scholar: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "results": []
            }
