"""
<PERSON><PERSON><PERSON> giá chất lượng câu trả lời.

<PERSON><PERSON><PERSON> này cung cấp các phương thức để đánh giá chất lượng câu trả lời
và quyết định có cần tìm kiếm thêm hay không.
"""

import re
from typing import Dict, Any, List, Optional, Union, Tuple
import time

from ..utils.structured_logging import get_logger
from ..evaluation.qa_evaluator import QAEvaluator

# Create a logger
logger = get_logger(__name__)

class AnswerQualityEvaluator:
    """
    Đánh giá chất lượng câu trả lời.

    Tính năng:
    - <PERSON><PERSON><PERSON> giá độ liên quan, đ<PERSON> ch<PERSON>h xác, tính đầy đủ
    - Quyết định có cần tìm kiếm thêm hay không
    - Hỗ trợ tiếng Việt
    """

    def __init__(
        self,
        config: Optional[Dict[str, Any]] = None,
        use_model_evaluation: bool = False,
        use_heuristics: bool = True,
        language: str = "auto"
    ):
        """
        Khởi tạo AnswerQualityEvaluator.

        Args:
            config: Cấu hình
            use_model_evaluation: Sử dụng model để đánh giá
            use_heuristics: Sử dụng heuristics để đánh giá
            language: Ngôn ngữ (auto, en, vi)
        """
        self.config = config or {}
        self.use_model_evaluation = use_model_evaluation
        self.use_heuristics = use_heuristics
        self.language = language

        # Khởi tạo các thành phần
        self._initialize_components()

        # Tải các ngưỡng đánh giá
        self._load_evaluation_thresholds()

    def _initialize_components(self):
        """Khởi tạo các thành phần."""
        # Khởi tạo QAEvaluator nếu cần
        if self.use_model_evaluation:
            try:
                provider = self.config.get("model_provider", "openai")
                model = self.config.get("model_name", "gpt-4o")

                self.qa_evaluator = QAEvaluator(
                    provider=provider,
                    model=model,
                    use_model_evaluation=True,
                    use_heuristics=self.use_heuristics,
                    language=self.language if self.language != "auto" else "en"
                )
            except Exception as e:
                logger.warning(f"Không thể khởi tạo QAEvaluator: {str(e)}")
                self.use_model_evaluation = False

    def _load_evaluation_thresholds(self):
        """Tải các ngưỡng đánh giá."""
        # Các ngưỡng đánh giá mặc định (điều chỉnh thấp hơn)
        self.evaluation_thresholds = {
            "relevance": 5.0,        # Trước đây là 6.0
            "factual_accuracy": 6.0,  # Trước đây là 7.0
            "completeness": 5.0,      # Trước đây là 6.0
            "coherence": 4.0,         # Trước đây là 5.0
            "conciseness": 4.0,       # Trước đây là 5.0
            "source_attribution": 4.0, # Trước đây là 5.0
            "overall": 5.0,           # Trước đây là 6.0
            "need_more_search": 4.0    # Trước đây là 5.0
        }

        # Cập nhật từ config nếu có
        if "evaluation_thresholds" in self.config:
            self.evaluation_thresholds.update(self.config["evaluation_thresholds"])

    def evaluate_answer(
        self,
        question: str,
        answer: str,
        documents: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Đánh giá chất lượng câu trả lời.

        Args:
            question: Câu hỏi
            answer: Câu trả lời
            documents: Danh sách tài liệu được sử dụng

        Returns:
            Dict[str, Any]: Kết quả đánh giá
        """
        # Kết quả đánh giá
        evaluation = {
            "question": question,
            "answer_length": len(answer),
            "document_count": len(documents),
            "metrics": {},
            "overall_score": 0.0,
            "need_more_search": False,
            "evaluation_time": 0.0
        }

        start_time = time.time()

        # Sử dụng QAEvaluator nếu có
        if self.use_model_evaluation:
            try:
                qa_results = self.qa_evaluator.evaluate(
                    question=question,
                    answer=answer,
                    documents=documents
                )

                # Cập nhật kết quả đánh giá
                evaluation["metrics"] = qa_results.get("metrics", {})
                evaluation["overall_score"] = qa_results.get("overall_score", 0.0)
            except Exception as e:
                logger.warning(f"Lỗi khi sử dụng QAEvaluator: {str(e)}")

        # Sử dụng heuristics nếu không có kết quả từ model hoặc không sử dụng model
        if not evaluation["metrics"] or not self.use_model_evaluation:
            self._evaluate_with_heuristics(question, answer, documents, evaluation)

        # Quyết định có cần tìm kiếm thêm hay không
        self._decide_need_more_search(evaluation)

        # Cập nhật thời gian đánh giá
        evaluation["evaluation_time"] = time.time() - start_time

        return evaluation

    def _evaluate_with_heuristics(
        self,
        question: str,
        answer: str,
        documents: List[Dict[str, Any]],
        evaluation: Dict[str, Any]
    ):
        """
        Đánh giá câu trả lời bằng heuristics.

        Args:
            question: Câu hỏi
            answer: Câu trả lời
            documents: Danh sách tài liệu được sử dụng
            evaluation: Kết quả đánh giá hiện tại
        """
        # Đánh giá độ liên quan
        relevance_score = self._evaluate_relevance(question, answer)
        evaluation["metrics"]["relevance"] = {
            "score": relevance_score,
            "explanation": "Đánh giá độ liên quan dựa trên sự trùng lặp từ khóa và cấu trúc câu trả lời."
        }

        # Đánh giá tính đầy đủ
        completeness_score = self._evaluate_completeness(question, answer, documents)
        evaluation["metrics"]["completeness"] = {
            "score": completeness_score,
            "explanation": "Đánh giá tính đầy đủ dựa trên độ dài câu trả lời và mức độ bao phủ các khía cạnh của câu hỏi."
        }

        # Đánh giá trích dẫn nguồn
        attribution_score = self._evaluate_source_attribution(answer, documents)
        evaluation["metrics"]["source_attribution"] = {
            "score": attribution_score,
            "explanation": "Đánh giá trích dẫn nguồn dựa trên việc sử dụng thông tin từ tài liệu."
        }

        # Tính điểm tổng hợp
        overall_score = (relevance_score + completeness_score + attribution_score) / 3
        evaluation["overall_score"] = overall_score

    def _evaluate_relevance(self, question: str, answer: str) -> float:
        """
        Đánh giá độ liên quan của câu trả lời.

        Args:
            question: Câu hỏi
            answer: Câu trả lời

        Returns:
            float: Điểm độ liên quan (0-10)
        """
        # Chuyển về chữ thường
        question_lower = question.lower()
        answer_lower = answer.lower()

        # Tách từ
        question_words = set(re.findall(r'\b\w+\b', question_lower))
        answer_words = set(re.findall(r'\b\w+\b', answer_lower))

        # Tính độ trùng lặp từ
        if not question_words:
            return 5.0

        overlap = len(question_words.intersection(answer_words)) / len(question_words)

        # Tính điểm dựa trên độ trùng lặp
        relevance_score = min(10.0, overlap * 15.0)

        # Kiểm tra cấu trúc câu trả lời
        if question_lower.startswith("what is") and "is" in answer_lower[:50]:
            relevance_score += 1.0
        elif question_lower.startswith("how to") and ("by" in answer_lower[:50] or "step" in answer_lower[:50]):
            relevance_score += 1.0
        elif question_lower.startswith("why") and ("because" in answer_lower[:50] or "reason" in answer_lower[:50]):
            relevance_score += 1.0

        # Giới hạn điểm trong khoảng 0-10
        return max(0.0, min(10.0, relevance_score))

    def _evaluate_completeness(
        self,
        question: str,
        answer: str,
        documents: List[Dict[str, Any]]
    ) -> float:
        """
        Đánh giá tính đầy đủ của câu trả lời.

        Args:
            question: Câu hỏi
            answer: Câu trả lời
            documents: Danh sách tài liệu được sử dụng

        Returns:
            float: Điểm tính đầy đủ (0-10)
        """
        # Đánh giá dựa trên độ dài
        answer_length = len(answer)

        if answer_length < 50:
            length_score = 2.0
        elif answer_length < 100:
            length_score = 4.0
        elif answer_length < 200:
            length_score = 6.0
        elif answer_length < 500:
            length_score = 8.0
        else:
            length_score = 10.0

        # Đánh giá dựa trên số lượng đoạn văn
        paragraphs = [p for p in answer.split('\n') if p.strip()]

        if len(paragraphs) < 2:
            paragraph_score = 3.0
        elif len(paragraphs) < 3:
            paragraph_score = 5.0
        elif len(paragraphs) < 5:
            paragraph_score = 7.0
        else:
            paragraph_score = 9.0

        # Đánh giá dựa trên mức độ sử dụng tài liệu
        doc_usage = 0.0
        if documents:
            doc_content = ' '.join([doc.get('content', doc.get('text', '')) for doc in documents])
            doc_words = set(re.findall(r'\b\w+\b', doc_content.lower()))
            answer_words = set(re.findall(r'\b\w+\b', answer.lower()))

            if doc_words:
                doc_usage = min(10.0, len(answer_words.intersection(doc_words)) / len(doc_words) * 20.0)

        # Tính điểm tổng hợp
        completeness_score = (length_score * 0.4) + (paragraph_score * 0.3) + (doc_usage * 0.3)

        return max(0.0, min(10.0, completeness_score))

    def _evaluate_source_attribution(
        self,
        answer: str,
        documents: List[Dict[str, Any]]
    ) -> float:
        """
        Đánh giá trích dẫn nguồn trong câu trả lời.

        Args:
            answer: Câu trả lời
            documents: Danh sách tài liệu được sử dụng

        Returns:
            float: Điểm trích dẫn nguồn (0-10)
        """
        # Các mẫu trích dẫn
        citation_patterns = [
            "according to", "as stated in", "as mentioned in",
            "based on", "from", "cited in", "reference", "source",
            "theo", "như đã nêu trong", "như đã đề cập trong",
            "dựa trên", "từ", "trích dẫn trong", "tài liệu", "nguồn"
        ]

        # Đếm số lượng trích dẫn
        citation_count = sum(1 for pattern in citation_patterns if pattern.lower() in answer.lower())

        # Kiểm tra nội dung tài liệu trong câu trả lời
        content_match = 0
        for doc in documents:
            doc_content = doc.get("content", doc.get("text", ""))
            doc_sentences = [s.strip() for s in doc_content.split(".") if s.strip()]

            for sentence in doc_sentences:
                if len(sentence) > 20 and sentence.lower() in answer.lower():
                    content_match += 1

        # Tính điểm dựa trên số lượng trích dẫn và nội dung trùng khớp
        attribution_score = min(10.0, (citation_count * 2.0) + (content_match * 1.0))

        return attribution_score

    def _decide_need_more_search(self, evaluation: Dict[str, Any]):
        """
        Quyết định có cần tìm kiếm thêm hay không.

        Args:
            evaluation: Kết quả đánh giá
        """
        # Lấy các điểm số
        overall_score = evaluation["overall_score"]
        metrics = evaluation["metrics"]

        # Kiểm tra các điểm số cụ thể
        relevance_score = metrics.get("relevance", {}).get("score", 0.0)
        completeness_score = metrics.get("completeness", {}).get("score", 0.0)

        # Quyết định dựa trên ngưỡng
        need_more_search = False

        if overall_score < self.evaluation_thresholds["overall"]:
            need_more_search = True

        if relevance_score < self.evaluation_thresholds["relevance"]:
            need_more_search = True

        if completeness_score < self.evaluation_thresholds["completeness"]:
            need_more_search = True

        # Cập nhật kết quả
        evaluation["need_more_search"] = need_more_search
