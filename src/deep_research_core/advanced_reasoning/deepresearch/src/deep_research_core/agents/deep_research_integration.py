#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module cung cấp các hàm tích hợp AdaptiveCrawler với phương thức deep_research.

Module này cung cấp các hàm để tích hợp AdaptiveCrawler với phương thức deep_research của WebSearchAgentLocal,
cho phép deep_research sử dụng AdaptiveCrawler để crawl nội dung web một cách thích ứng.
"""

import logging
import time
import asyncio
import concurrent.futures
from typing import Dict, List, Any, Optional, Tuple, Union

from ..utils.structured_logging import get_logger
from ..utils.url_utils import is_valid_url, clean_url

# Thiết lập logging
logger = get_logger(__name__)

def integrate_deep_research(agent, config: Dict[str, Any] = None) -> None:
    """
    Tích hợp AdaptiveCrawler vào phương thức deep_research của WebSearchAgentLocal.
    
    Args:
        agent: WebSearchAgentLocal instance
        config: <PERSON><PERSON><PERSON> hình cho AdaptiveCrawler
    """
    if config is None:
        config = {}
    
    try:
        # Import AdaptiveCrawler
        try:
            from ..crawlers.adaptive_crawler import AdaptiveCrawler
            ADAPTIVE_CRAWLER_AVAILABLE = True
        except ImportError:
            try:
                from .adaptive_crawler import AdaptiveCrawler
                ADAPTIVE_CRAWLER_AVAILABLE = True
            except ImportError:
                ADAPTIVE_CRAWLER_AVAILABLE = False
        
        if not ADAPTIVE_CRAWLER_AVAILABLE:
            logger.warning("AdaptiveCrawler not available. Integration with deep_research will be limited.")
            return
        
        # Khởi tạo AdaptiveCrawler nếu chưa có
        if not hasattr(agent, "_adaptive_crawler") or agent._adaptive_crawler is None:
            agent._adaptive_crawler = AdaptiveCrawler(
                config=config,
                use_memory_optimization=config.get("use_memory_optimization", True),
                use_playwright=config.get("use_playwright", True),
                verbose=agent.verbose if hasattr(agent, "verbose") else False
            )
        
        # Tích hợp với phương thức deep_research
        from .improved_deep_research import deep_research as original_deep_research
        
        # Định nghĩa phương thức deep_research mới
        def enhanced_deep_research(
            self,
            query: str,
            num_results_per_query: int = 3,
            max_sub_queries: int = 5,
            min_sub_queries: int = 2,
            max_content_length: int = 10000,
            max_depth: int = 2,
            language: str = "auto",
            use_adaptive_crawler: bool = True,
            optimize_query_distribution: bool = True,
            prioritize_complex_queries: bool = True,
            use_parallel_processing: bool = True,
            **kwargs,
        ) -> Dict[str, Any]:
            """
            Phương thức deep_research cải tiến sử dụng AdaptiveCrawler.
            
            Args:
                query: Câu hỏi cần nghiên cứu
                num_results_per_query: Số kết quả cho mỗi câu hỏi con
                max_sub_queries: Số câu hỏi con tối đa
                min_sub_queries: Số câu hỏi con tối thiểu
                max_content_length: Độ dài tối đa của nội dung
                max_depth: Độ sâu tối đa khi crawl
                language: Ngôn ngữ tìm kiếm
                use_adaptive_crawler: Có sử dụng AdaptiveCrawler không
                optimize_query_distribution: Tối ưu hóa phân bổ số lượng kết quả cho mỗi câu hỏi con
                prioritize_complex_queries: Ưu tiên các câu hỏi phức tạp hơn
                use_parallel_processing: Sử dụng xử lý song song cho các câu hỏi con
                **kwargs: Các tham số bổ sung
                
            Returns:
                Dict[str, Any]: Kết quả nghiên cứu
            """
            # Kiểm tra xem có sử dụng AdaptiveCrawler không
            if not use_adaptive_crawler or not hasattr(self, "_adaptive_crawler") or self._adaptive_crawler is None:
                # Nếu không sử dụng AdaptiveCrawler, gọi phương thức gốc
                return original_deep_research(
                    self,
                    query=query,
                    num_results_per_query=num_results_per_query,
                    max_sub_queries=max_sub_queries,
                    min_sub_queries=min_sub_queries,
                    max_content_length=max_content_length,
                    max_depth=max_depth,
                    language=language,
                    use_adaptive_crawler=False,
                    optimize_query_distribution=optimize_query_distribution,
                    prioritize_complex_queries=prioritize_complex_queries,
                    use_parallel_processing=use_parallel_processing,
                    **kwargs,
                )
            
            # Gọi phương thức gốc với tham số use_adaptive_crawler=True
            # Phương thức gốc sẽ sử dụng self._adaptive_crawler
            return original_deep_research(
                self,
                query=query,
                num_results_per_query=num_results_per_query,
                max_sub_queries=max_sub_queries,
                min_sub_queries=min_sub_queries,
                max_content_length=max_content_length,
                max_depth=max_depth,
                language=language,
                use_adaptive_crawler=True,
                optimize_query_distribution=optimize_query_distribution,
                prioritize_complex_queries=prioritize_complex_queries,
                use_parallel_processing=use_parallel_processing,
                **kwargs,
            )
        
        # Gán phương thức deep_research mới
        agent.deep_research = lambda *args, **kwargs: enhanced_deep_research(agent, *args, **kwargs)
        
        # Thêm phương thức _deep_crawl_with_adaptive_crawler
        from .adaptive_crawler_integration import deep_crawl_with_adaptive_crawler
        agent._deep_crawl_with_adaptive_crawler = lambda url, **kwargs: deep_crawl_with_adaptive_crawler(agent, url, **kwargs)
        
        logger.info("AdaptiveCrawler integrated with deep_research successfully")
    except Exception as e:
        logger.error(f"Failed to integrate AdaptiveCrawler with deep_research: {str(e)}")

def process_sub_query_with_adaptive_crawler(
    agent,
    sub_query: Dict[str, Any],
    max_depth: int = 2,
    max_pages: int = 5,
    timeout: int = 30,
    **kwargs
) -> Dict[str, Any]:
    """
    Xử lý một câu hỏi con với AdaptiveCrawler.
    
    Args:
        agent: WebSearchAgentLocal instance
        sub_query: Câu hỏi con cần xử lý
        max_depth: Độ sâu tối đa khi crawl
        max_pages: Số trang tối đa khi crawl
        timeout: Thời gian chờ tối đa (giây)
        **kwargs: Các tham số bổ sung
        
    Returns:
        Dict[str, Any]: Kết quả xử lý
    """
    if not hasattr(agent, "_adaptive_crawler") or agent._adaptive_crawler is None:
        logger.warning("AdaptiveCrawler not available")
        return {
            "success": False,
            "error": "AdaptiveCrawler not available",
            "query": sub_query.get("query", ""),
            "results": []
        }
    
    # Tìm kiếm để lấy danh sách URL
    search_results = agent.search(
        query=sub_query["query"],
        num_results=sub_query.get("allocated_results", 5),
        language=kwargs.get("language", "auto"),
        search_method=kwargs.get("search_method", "auto"),
        optimize_for_llm=True
    )
    
    if not search_results.get("success", False) or not search_results.get("results"):
        return {
            "success": False,
            "error": "Search failed",
            "query": sub_query["query"],
            "results": []
        }
    
    # Lấy danh sách URL từ kết quả tìm kiếm
    urls = [result["url"] for result in search_results.get("results", []) if "url" in result]
    
    if not urls:
        return {
            "success": False,
            "error": "No URLs found",
            "query": sub_query["query"],
            "results": []
        }
    
    # Xác định mức độ phức tạp
    complexity_level = "medium"
    if sub_query.get("complexity") == "high":
        complexity_level = "complex"
    elif sub_query.get("complexity") == "low":
        complexity_level = "simple"
    
    # Crawl các URL với AdaptiveCrawler
    crawler_results = agent._adaptive_crawler.crawl(
        urls=urls[:min(5, len(urls))],  # Giới hạn số URL để tránh quá tải
        complexity_level=complexity_level,
        max_depth=max_depth,
        max_pages=max_pages,
        timeout=timeout,
        detailed_scraping=True,
        extract_images=kwargs.get("extract_images", False),
        extract_files=kwargs.get("extract_files", False),
        extract_html=kwargs.get("extract_html", True),
        extract_file_content=kwargs.get("extract_file_content", True)
    )
    
    # Chuyển đổi kết quả sang định dạng phù hợp
    results = {
        "success": crawler_results.get("success", False),
        "query": sub_query["query"],
        "results": []
    }
    
    # Thêm kết quả crawl vào kết quả
    for item in crawler_results.get("results", []):
        results["results"].append({
            "url": item.get("url", ""),
            "title": item.get("title", ""),
            "content": item.get("content", ""),
            "metadata": item.get("metadata", {}),
            "links": item.get("links", [])
        })
    
    return results
