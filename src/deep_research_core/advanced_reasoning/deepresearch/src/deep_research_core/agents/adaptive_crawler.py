"""
Adaptive Crawler - Crawler thích ứng cho WebSearchAgent.

Module này cung cấp các chức năng crawl thích ứng dựa trên chất lượng kết quả tìm kiếm,
với khả năng tự động điều chỉnh chiến lược crawl dựa trên độ phức tạp của câu hỏi
và chất lượng câu trả lời.
"""

import time
import os
import json
import re
import hashlib
import tempfile
from typing import Dict, Any, List, Optional, Union, Tuple, Set
from urllib.parse import urlparse, urljoin

from ..utils.structured_logging import get_logger
from ..utils.playwright_installer import ensure_playwright_ready
from .advanced_crawlee import MemoryOptimizedCrawler, ResourceManager
from .document_extractors import DocumentExtractor

# Thử import ExtendedDocumentExtractor nếu có
try:
    from .document_extractors_extended import ExtendedDocumentExtractor
    EXTENDED_DOCUMENT_EXTRACTOR_AVAILABLE = True
except ImportError:
    EXTENDED_DOCUMENT_EXTRACTOR_AVAILABLE = False

# Create a logger
logger = get_logger(__name__)

class AdaptiveCrawler:
    """
    Crawler thích ứng cho WebSearchAgent.

    Tính năng:
    - Tự động điều chỉnh chiến lược crawl dựa trên độ phức tạp của câu hỏi
    - Tự động điều chỉnh chiến lược crawl dựa trên chất lượng câu trả lời
    - Hỗ trợ crawl sâu khi cần thiết
    - Tối ưu hóa sử dụng tài nguyên
    """

    def __init__(
        self,
        config: Optional[Dict[str, Any]] = None,
        resource_manager: Optional[ResourceManager] = None,
        use_memory_optimization: bool = True,
        use_playwright: bool = True,
        crawl_mode: str = "basic",
        download_media: bool = False,
        download_path: Optional[str] = None,
        site_map_enabled: bool = False,
        max_media_size_mb: int = 10,
        extract_file_content: bool = False,
        enable_javascript: bool = True,
        wait_for_selector: Optional[str] = None,
        wait_for_timeout: int = 1000,
        handle_spa: bool = False,
        handle_infinite_scroll: bool = False,
        infinite_scroll_max_scrolls: int = 5,
        infinite_scroll_timeout: int = 1000,
        handle_ajax: bool = False,
        ajax_wait_time: int = 2000,
        ajax_request_patterns: Optional[List[str]] = None,
        handle_pagination: bool = False,
        pagination_selectors: Optional[List[str]] = None,
        max_pagination_pages: int = 5,
        max_pages: int = 10
    ):
        """
        Khởi tạo AdaptiveCrawler.

        Args:
            config: Cấu hình
            resource_manager: Đối tượng quản lý tài nguyên
            use_memory_optimization: Sử dụng tối ưu hóa bộ nhớ
            use_playwright: Sử dụng Playwright
            crawl_mode: Chế độ crawl ("basic", "full_site", "content_only", "media_only")
            download_media: Có tải xuống media (hình ảnh, video, audio) hay không
            download_path: Đường dẫn để lưu các file đã tải xuống
            site_map_enabled: Có tạo sitemap cho trang web đã crawl hay không
            max_media_size_mb: Kích thước tối đa (MB) cho file media được tải xuống
            extract_file_content: Có trích xuất nội dung từ file đã tải xuống hay không
            enable_javascript: Có bật JavaScript khi crawl hay không
            wait_for_selector: Chờ một selector cụ thể xuất hiện trước khi trích xuất nội dung
            wait_for_timeout: Thời gian chờ thêm (ms) sau khi trang đã tải
            handle_spa: Xử lý các trang SPA (Single Page Application)
            handle_infinite_scroll: Xử lý các trang có infinite scroll
            infinite_scroll_max_scrolls: Số lần cuộn tối đa cho infinite scroll
            infinite_scroll_timeout: Thời gian chờ (ms) giữa các lần cuộn cho infinite scroll
            handle_ajax: Xử lý các request AJAX
            ajax_wait_time: Thời gian chờ (ms) cho các request AJAX hoàn thành
            ajax_request_patterns: Danh sách các pattern URL để xác định request AJAX (ví dụ: ["/api/", ".json"])
        """
        self.config = config or {}
        self.use_memory_optimization = use_memory_optimization
        self.use_playwright = use_playwright
        self.crawl_mode = crawl_mode
        self.download_media = download_media
        self.download_path = download_path
        self.site_map_enabled = site_map_enabled
        self.max_media_size_mb = max_media_size_mb
        self.extract_file_content = extract_file_content
        self.enable_javascript = enable_javascript
        self.wait_for_selector = wait_for_selector
        self.wait_for_timeout = wait_for_timeout
        self.handle_spa = handle_spa
        self.handle_infinite_scroll = handle_infinite_scroll
        self.infinite_scroll_max_scrolls = infinite_scroll_max_scrolls
        self.infinite_scroll_timeout = infinite_scroll_timeout
        self.handle_ajax = handle_ajax
        self.ajax_wait_time = ajax_wait_time
        self.ajax_request_patterns = ajax_request_patterns or ["/api/", ".json", "ajax", "xhr"]
        self.handle_pagination = handle_pagination
        self.max_pages = max_pages
        self.max_pagination_pages = max_pagination_pages
        self.pagination_selectors = pagination_selectors

        # Khởi tạo document extractor nếu cần
        self.document_extractor = None
        if self.extract_file_content:
            if EXTENDED_DOCUMENT_EXTRACTOR_AVAILABLE:
                self.document_extractor = ExtendedDocumentExtractor()
            else:
                self.document_extractor = DocumentExtractor()

        # Tạo thư mục download_path nếu cần
        if self.download_media and self.download_path:
            os.makedirs(self.download_path, exist_ok=True)
            os.makedirs(os.path.join(self.download_path, "images"), exist_ok=True)
            os.makedirs(os.path.join(self.download_path, "videos"), exist_ok=True)
            os.makedirs(os.path.join(self.download_path, "files"), exist_ok=True)

        # Khởi tạo resource manager nếu chưa có
        self.resource_manager = resource_manager or ResourceManager()

        # Khởi tạo các thành phần
        self._initialize_components()

        # Tải cấu hình crawl
        self._load_crawl_config()

    def _initialize_components(self):
        """Khởi tạo các thành phần."""
        # Khởi tạo MemoryOptimizedCrawler nếu cần
        if self.use_memory_optimization:
            try:
                batch_size = self.config.get("batch_size", 5)
                memory_threshold = self.config.get("memory_threshold", 80.0)
                memory_cleanup_interval = self.config.get("memory_cleanup_interval", 5)

                self.memory_crawler = MemoryOptimizedCrawler(
                    resource_manager=self.resource_manager,
                    batch_size=batch_size,
                    adaptive_batch_size=True,
                    memory_cleanup_interval=memory_cleanup_interval
                )

                # Khởi tạo thông tin theo dõi bộ nhớ
                self.memory_stats = {
                    "initial_memory": self._get_current_memory_usage(),
                    "peak_memory": 0,
                    "memory_cleanups": 0,
                    "memory_saved": 0,
                    "last_cleanup_time": time.time(),
                    "memory_history": [],  # Lịch sử sử dụng bộ nhớ
                    "leak_detection": {
                        "consecutive_increases": 0,
                        "last_values": [],
                        "leak_detected": False,
                        "leak_detection_time": None
                    }
                }

                # Thiết lập ngưỡng bộ nhớ
                self.memory_threshold = memory_threshold
                self.critical_memory_threshold = self.config.get("critical_memory_threshold", 90.0)
                self.memory_check_interval = self.config.get("memory_check_interval", 60)  # Giây

                # Thiết lập các tham số phát hiện rò rỉ bộ nhớ
                self.memory_leak_threshold = self.config.get("memory_leak_threshold", 5)  # Số lần tăng liên tiếp
                self.memory_history_size = self.config.get("memory_history_size", 10)  # Số lượng giá trị lưu trữ
                self.memory_leak_percent_threshold = self.config.get("memory_leak_percent_threshold", 3.0)  # % tăng tối thiểu

                logger.info(f"Đã khởi tạo tối ưu hóa bộ nhớ với ngưỡng {memory_threshold}%")
            except Exception as e:
                logger.warning(f"Không thể khởi tạo MemoryOptimizedCrawler: {str(e)}")
                self.use_memory_optimization = False

    def _get_current_memory_usage(self) -> float:
        """
        Lấy thông tin sử dụng bộ nhớ hiện tại.

        Returns:
            float: Phần trăm bộ nhớ đang sử dụng
        """
        try:
            import psutil
            memory_info = psutil.virtual_memory()

            # Cập nhật thống kê bộ nhớ nếu có
            if hasattr(self, "memory_stats"):
                # Thêm vào lịch sử bộ nhớ
                if "memory_history" in self.memory_stats:
                    self.memory_stats["memory_history"].append({
                        "timestamp": time.time(),
                        "percent": memory_info.percent,
                        "available": memory_info.available,
                        "used": memory_info.used,
                        "total": memory_info.total
                    })

                    # Giữ lịch sử trong giới hạn kích thước
                    if hasattr(self, "memory_history_size"):
                        max_size = self.memory_history_size
                    else:
                        max_size = 20  # Mặc định

                    if len(self.memory_stats["memory_history"]) > max_size:
                        self.memory_stats["memory_history"] = self.memory_stats["memory_history"][-max_size:]

                # Cập nhật peak memory
                if memory_info.percent > self.memory_stats.get("peak_memory", 0):
                    self.memory_stats["peak_memory"] = memory_info.percent

                # Phân tích xu hướng sử dụng bộ nhớ
                self._analyze_memory_trend()

            return memory_info.percent
        except ImportError:
            logger.warning("Không thể import psutil, sử dụng giá trị mặc định cho bộ nhớ")
            return 50.0
        except Exception as e:
            logger.warning(f"Lỗi khi lấy thông tin bộ nhớ: {str(e)}")
            return 50.0

    def _analyze_memory_trend(self) -> None:
        """
        Phân tích xu hướng sử dụng bộ nhớ và đưa ra cảnh báo nếu cần.
        """
        if not hasattr(self, "memory_stats") or "memory_history" not in self.memory_stats:
            return

        history = self.memory_stats["memory_history"]
        if len(history) < 5:  # Cần ít nhất 5 điểm dữ liệu
            return

        # Lấy 5 điểm dữ liệu gần nhất
        recent = history[-5:]

        # Tính tốc độ tăng trưởng bộ nhớ
        first_percent = recent[0]["percent"]
        last_percent = recent[-1]["percent"]
        time_diff = recent[-1]["timestamp"] - recent[0]["timestamp"]

        if time_diff <= 0:
            return

        # Tính tốc độ tăng trưởng (%/giây)
        growth_rate = (last_percent - first_percent) / time_diff

        # Lưu tốc độ tăng trưởng
        self.memory_stats["growth_rate"] = growth_rate

        # Lưu lịch sử tốc độ tăng trưởng
        if "growth_rate_history" not in self.memory_stats:
            self.memory_stats["growth_rate_history"] = []

        self.memory_stats["growth_rate_history"].append({
            "timestamp": time.time(),
            "rate": growth_rate,
            "memory_start": first_percent,
            "memory_end": last_percent,
            "time_span": time_diff
        })

        # Giữ lịch sử tốc độ tăng trưởng trong giới hạn
        max_history = getattr(self, "memory_history_size", 20)
        if len(self.memory_stats["growth_rate_history"]) > max_history:
            self.memory_stats["growth_rate_history"] = self.memory_stats["growth_rate_history"][-max_history:]

        # Phân tích mẫu tăng trưởng
        memory_pattern = self._detect_memory_pattern(recent)
        if memory_pattern:
            self.memory_stats["memory_pattern"] = memory_pattern
            logger.info(f"Phát hiện mẫu sử dụng bộ nhớ: {memory_pattern}")

            # Lưu lịch sử mẫu bộ nhớ
            if "pattern_history" not in self.memory_stats:
                self.memory_stats["pattern_history"] = []

            self.memory_stats["pattern_history"].append({
                "timestamp": time.time(),
                "pattern": memory_pattern,
                "memory": last_percent,
                "growth_rate": growth_rate
            })

            # Giữ lịch sử mẫu trong giới hạn
            if len(self.memory_stats["pattern_history"]) > max_history:
                self.memory_stats["pattern_history"] = self.memory_stats["pattern_history"][-max_history:]

            # Phân tích xu hướng mẫu
            self._analyze_pattern_trend()

        # Cảnh báo nếu tốc độ tăng trưởng quá cao
        if growth_rate > 1.0:  # Tăng hơn 1% mỗi giây
            logger.warning(f"Phát hiện tốc độ tăng bộ nhớ cao: {growth_rate:.2f}%/giây")

            # Giảm ngưỡng bộ nhớ để tối ưu hóa sớm hơn
            if hasattr(self, "memory_threshold"):
                old_threshold = self.memory_threshold
                self.memory_threshold = max(60, self.memory_threshold - 10)
                logger.warning(f"Giảm ngưỡng bộ nhớ từ {old_threshold:.1f}% xuống {self.memory_threshold:.1f}%")

            # Giảm khoảng thời gian kiểm tra bộ nhớ
            if hasattr(self, "memory_check_interval"):
                old_interval = self.memory_check_interval
                self.memory_check_interval = max(15, self.memory_check_interval // 2)
                logger.warning(f"Giảm khoảng thời gian kiểm tra bộ nhớ từ {old_interval}s xuống {self.memory_check_interval}s")

            # Kiểm tra xem có phải rò rỉ bộ nhớ không
            if growth_rate > 2.0 and all(recent[i]["percent"] > recent[i-1]["percent"] for i in range(1, len(recent))):
                # Nếu tăng liên tục và nhanh, có thể là rò rỉ
                logger.warning("Phát hiện khả năng rò rỉ bộ nhớ từ phân tích xu hướng")

                # Cập nhật thông tin rò rỉ
                if "leak_detection" in self.memory_stats:
                    self.memory_stats["leak_detection"]["potential_leak"] = True
                    self.memory_stats["leak_detection"]["potential_leak_time"] = time.time()
                    self.memory_stats["leak_detection"]["potential_leak_growth_rate"] = growth_rate

                    # Tăng số lần phát hiện rò rỉ tiềm năng
                    self.memory_stats["leak_detection"]["potential_leak_count"] = self.memory_stats["leak_detection"].get("potential_leak_count", 0) + 1

                    # Lưu mẫu rò rỉ
                    if "potential_leak_patterns" not in self.memory_stats["leak_detection"]:
                        self.memory_stats["leak_detection"]["potential_leak_patterns"] = []

                    self.memory_stats["leak_detection"]["potential_leak_patterns"].append({
                        "timestamp": time.time(),
                        "growth_rate": growth_rate,
                        "memory_values": [entry["percent"] for entry in recent],
                        "pattern": memory_pattern
                    })

                    # Giới hạn số lượng mẫu
                    if len(self.memory_stats["leak_detection"]["potential_leak_patterns"]) > 10:
                        self.memory_stats["leak_detection"]["potential_leak_patterns"].pop(0)

        # Dự đoán khi nào bộ nhớ sẽ đạt ngưỡng nguy hiểm
        if growth_rate > 0 and hasattr(self, "critical_memory_threshold"):
            current = last_percent
            critical = self.critical_memory_threshold

            if current < critical:
                time_to_critical = (critical - current) / growth_rate

                # Lưu dự đoán
                self.memory_stats["time_to_critical"] = time_to_critical
                self.memory_stats["time_to_critical_prediction_time"] = time.time()

                # Lưu lịch sử dự đoán
                if "critical_predictions" not in self.memory_stats:
                    self.memory_stats["critical_predictions"] = []

                self.memory_stats["critical_predictions"].append({
                    "timestamp": time.time(),
                    "current_memory": current,
                    "critical_threshold": critical,
                    "growth_rate": growth_rate,
                    "time_to_critical": time_to_critical
                })

                # Giới hạn số lượng dự đoán
                if len(self.memory_stats["critical_predictions"]) > 10:
                    self.memory_stats["critical_predictions"].pop(0)

                if time_to_critical < 60:  # Dưới 1 phút
                    logger.warning(f"Cảnh báo: Bộ nhớ có thể đạt ngưỡng nguy hiểm trong {time_to_critical:.1f} giây")

                    # Thực hiện tối ưu hóa ngay lập tức
                    self._optimize_memory(force=True)
                elif time_to_critical < 300:  # Dưới 5 phút
                    logger.warning(f"Cảnh báo: Bộ nhớ có thể đạt ngưỡng nguy hiểm trong {time_to_critical/60:.1f} phút")

                    # Giảm ngưỡng bộ nhớ để tối ưu hóa sớm hơn
                    if hasattr(self, "memory_threshold"):
                        self.memory_threshold = max(70, self.memory_threshold - 5)

        # Phân tích biến động bộ nhớ
        if len(history) >= 10:
            # Tính độ biến động (độ lệch chuẩn)
            memory_values = [entry["percent"] for entry in history[-10:]]
            mean = sum(memory_values) / len(memory_values)
            variance = sum((x - mean) ** 2 for x in memory_values) / len(memory_values)
            std_dev = variance ** 0.5

            # Lưu độ biến động
            self.memory_stats["memory_volatility"] = std_dev

            # Lưu lịch sử độ biến động
            if "volatility_history" not in self.memory_stats:
                self.memory_stats["volatility_history"] = []

            current_time = time.time()
            self.memory_stats["volatility_history"].append({
                "timestamp": current_time,
                "std_dev": std_dev,
                "mean": mean,
                "min": min(memory_values),
                "max": max(memory_values),
                "range": max(memory_values) - min(memory_values),
                "coefficient_of_variation": (std_dev / mean) * 100 if mean > 0 else 0
            })

            # Giới hạn lịch sử độ biến động
            if len(self.memory_stats["volatility_history"]) > max_history:
                self.memory_stats["volatility_history"] = self.memory_stats["volatility_history"][-max_history:]

            # Phân tích xu hướng độ biến động
            if len(self.memory_stats["volatility_history"]) >= 3:
                recent_volatility = self.memory_stats["volatility_history"][-3:]

                # Tính độ biến động trung bình
                avg_std_dev = sum(item["std_dev"] for item in recent_volatility) / len(recent_volatility)
                self.memory_stats["average_volatility"] = avg_std_dev

                # Kiểm tra xu hướng độ biến động
                is_increasing_volatility = all(recent_volatility[i]["std_dev"] >= recent_volatility[i-1]["std_dev"] for i in range(1, len(recent_volatility)))
                is_decreasing_volatility = all(recent_volatility[i]["std_dev"] <= recent_volatility[i-1]["std_dev"] for i in range(1, len(recent_volatility)))

                if is_increasing_volatility:
                    self.memory_stats["volatility_trend"] = "increasing"
                    logger.warning(f"Phát hiện độ biến động bộ nhớ đang tăng: {recent_volatility[0]['std_dev']:.2f}% -> {recent_volatility[-1]['std_dev']:.2f}%")
                elif is_decreasing_volatility:
                    self.memory_stats["volatility_trend"] = "decreasing"
                    logger.info(f"Phát hiện độ biến động bộ nhớ đang giảm: {recent_volatility[0]['std_dev']:.2f}% -> {recent_volatility[-1]['std_dev']:.2f}%")
                else:
                    self.memory_stats["volatility_trend"] = "fluctuating"

                # Phân tích chi tiết độ biến động
                volatility_analysis = self._analyze_memory_volatility(recent_volatility)
                self.memory_stats["volatility_analysis"] = volatility_analysis

                # Xử lý các khuyến nghị từ phân tích độ biến động
                if volatility_analysis["risk_level"] == "high":
                    logger.warning(f"Phát hiện độ biến động bộ nhớ nguy hiểm: {volatility_analysis['description']}")
                    logger.warning(f"Khuyến nghị: {', '.join(volatility_analysis['recommendations'])}")

                    # Điều chỉnh ngưỡng bộ nhớ nếu cần
                    if hasattr(self, "memory_threshold") and volatility_analysis.get("adjust_threshold", False):
                        old_threshold = self.memory_threshold
                        self.memory_threshold = max(65, self.memory_threshold - 10)
                        logger.warning(f"Giảm ngưỡng bộ nhớ từ {old_threshold:.1f}% xuống {self.memory_threshold:.1f}% do độ biến động cao")

                    # Điều chỉnh khoảng thời gian kiểm tra bộ nhớ
                    if hasattr(self, "memory_check_interval") and volatility_analysis.get("adjust_interval", False):
                        old_interval = self.memory_check_interval
                        self.memory_check_interval = max(15, self.memory_check_interval // 2)
                        logger.warning(f"Giảm khoảng thời gian kiểm tra bộ nhớ từ {old_interval}s xuống {self.memory_check_interval}s do độ biến động cao")

                elif volatility_analysis["risk_level"] == "medium":
                    logger.info(f"Phát hiện độ biến động bộ nhớ cần chú ý: {volatility_analysis['description']}")
                    logger.info(f"Khuyến nghị: {', '.join(volatility_analysis['recommendations'])}")

                    # Điều chỉnh nhẹ ngưỡng bộ nhớ nếu cần
                    if hasattr(self, "memory_threshold") and volatility_analysis.get("adjust_threshold", False):
                        old_threshold = self.memory_threshold
                        self.memory_threshold = max(70, self.memory_threshold - 5)
                        logger.info(f"Điều chỉnh ngưỡng bộ nhớ từ {old_threshold:.1f}% xuống {self.memory_threshold:.1f}% do độ biến động")

                    # Điều chỉnh khoảng thời gian kiểm tra bộ nhớ
                    if hasattr(self, "memory_check_interval") and volatility_analysis.get("adjust_interval", False):
                        old_interval = self.memory_check_interval
                        self.memory_check_interval = max(20, self.memory_check_interval // 1.5)
                        logger.info(f"Điều chỉnh khoảng thời gian kiểm tra bộ nhớ từ {old_interval}s xuống {self.memory_check_interval}s do độ biến động")

            # Phân tích xu hướng độ biến động
            if len(self.memory_stats["volatility_history"]) >= 3:
                recent_volatility = self.memory_stats["volatility_history"][-3:]

                # Tính độ biến động trung bình
                avg_std_dev = sum(item["std_dev"] for item in recent_volatility) / len(recent_volatility)
                self.memory_stats["average_volatility"] = avg_std_dev

                # Kiểm tra xu hướng độ biến động
                is_increasing_volatility = all(recent_volatility[i]["std_dev"] >= recent_volatility[i-1]["std_dev"] for i in range(1, len(recent_volatility)))
                is_decreasing_volatility = all(recent_volatility[i]["std_dev"] <= recent_volatility[i-1]["std_dev"] for i in range(1, len(recent_volatility)))

                if is_increasing_volatility:
                    self.memory_stats["volatility_trend"] = "increasing"
                    logger.warning(f"Phát hiện độ biến động bộ nhớ đang tăng: {recent_volatility[0]['std_dev']:.2f}% -> {recent_volatility[-1]['std_dev']:.2f}%")
                elif is_decreasing_volatility:
                    self.memory_stats["volatility_trend"] = "decreasing"
                    logger.info(f"Phát hiện độ biến động bộ nhớ đang giảm: {recent_volatility[0]['std_dev']:.2f}% -> {recent_volatility[-1]['std_dev']:.2f}%")
                else:
                    self.memory_stats["volatility_trend"] = "fluctuating"

            # Nếu độ biến động cao, có thể có vấn đề
            if std_dev > 10:  # Độ biến động lớn
                logger.warning(f"Phát hiện độ biến động bộ nhớ cao: {std_dev:.2f}%")

                # Điều chỉnh tần suất kiểm tra bộ nhớ
                if hasattr(self, "memory_check_interval"):
                    self.memory_check_interval = max(10, self.memory_check_interval // 2)
                    logger.info(f"Điều chỉnh khoảng thời gian kiểm tra bộ nhớ xuống {self.memory_check_interval}s do độ biến động cao")

    def _analyze_optimization_efficiency(self) -> Dict[str, Any]:
        """
        Phân tích hiệu quả của các lần tối ưu hóa bộ nhớ trước đó.

        Returns:
            Dict[str, Any]: Kết quả phân tích hiệu quả tối ưu hóa
        """
        result = {
            "average_efficiency": 0,
            "efficiency_trend": "stable",
            "recommendations": [],
            "timestamp": time.time()
        }

        if not hasattr(self, "memory_stats") or "optimization_efficiency_history" not in self.memory_stats:
            return result

        history = self.memory_stats["optimization_efficiency_history"]
        if len(history) < 3:  # Cần ít nhất 3 điểm dữ liệu
            return result

        # Tính hiệu quả trung bình
        efficiencies = [entry["efficiency"] for entry in history]
        avg_efficiency = sum(efficiencies) / len(efficiencies)
        result["average_efficiency"] = avg_efficiency

        # Phân tích xu hướng hiệu quả
        recent = history[-3:]
        is_increasing = all(recent[i]["efficiency"] >= recent[i-1]["efficiency"] for i in range(1, len(recent)))
        is_decreasing = all(recent[i]["efficiency"] <= recent[i-1]["efficiency"] for i in range(1, len(recent)))

        if is_increasing:
            result["efficiency_trend"] = "increasing"
        elif is_decreasing:
            result["efficiency_trend"] = "decreasing"
        else:
            result["efficiency_trend"] = "fluctuating"

        # Phân tích hiệu quả theo loại tối ưu hóa
        optimization_types = {}
        for entry in history:
            opt_type = entry["optimization_type"]
            if opt_type not in optimization_types:
                optimization_types[opt_type] = {"count": 0, "total_efficiency": 0}

            optimization_types[opt_type]["count"] += 1
            optimization_types[opt_type]["total_efficiency"] += entry["efficiency"]

        # Tính hiệu quả trung bình cho mỗi loại
        for opt_type, data in optimization_types.items():
            if data["count"] > 0:
                data["average_efficiency"] = data["total_efficiency"] / data["count"]

        result["optimization_types"] = optimization_types

        # Đưa ra khuyến nghị
        if avg_efficiency < 5:
            result["recommendations"].append("Điều chỉnh chiến lược tối ưu hóa do hiệu quả thấp")

            # Tìm loại tối ưu hóa hiệu quả nhất
            best_type = max(optimization_types.items(), key=lambda x: x[1]["average_efficiency"], default=(None, None))
            if best_type[0]:
                result["recommendations"].append(f"Ưu tiên sử dụng tối ưu hóa loại '{best_type[0]}' (hiệu quả: {best_type[1]['average_efficiency']:.2f}%)")

        if result["efficiency_trend"] == "decreasing":
            result["recommendations"].append("Xem xét thay đổi chiến lược tối ưu hóa do hiệu quả đang giảm dần")

        return result

    def _analyze_memory_usage_patterns(self) -> Dict[str, Any]:
        """
        Phân tích các mẫu sử dụng bộ nhớ để phát hiện các vấn đề tiềm ẩn.

        Returns:
            Dict[str, Any]: Kết quả phân tích mẫu sử dụng bộ nhớ
        """
        result = {
            "patterns_detected": [],
            "recommendations": [],
            "risk_level": "low",
            "timestamp": time.time()
        }

        if not hasattr(self, "memory_stats") or "memory_history" not in self.memory_stats:
            return result

        history = self.memory_stats["memory_history"]
        if len(history) < 10:  # Cần ít nhất 10 điểm dữ liệu
            return result

        # Lấy 10 điểm dữ liệu gần nhất
        recent = history[-10:]

        # Tính các thông số thống kê
        memory_values = [entry["percent"] for entry in recent]
        mean = sum(memory_values) / len(memory_values)
        variance = sum((x - mean) ** 2 for x in memory_values) / len(memory_values)
        std_dev = variance ** 0.5
        min_val = min(memory_values)
        max_val = max(memory_values)
        range_val = max_val - min_val

        # Lưu các thông số thống kê
        result["statistics"] = {
            "mean": mean,
            "std_dev": std_dev,
            "min": min_val,
            "max": max_val,
            "range": range_val,
            "coefficient_of_variation": (std_dev / mean) * 100 if mean > 0 else 0
        }

        # Phát hiện các mẫu sử dụng bộ nhớ

        # 1. Kiểm tra mẫu tăng liên tục
        is_increasing = all(memory_values[i] >= memory_values[i-1] for i in range(1, len(memory_values)))
        if is_increasing:
            pattern = {
                "type": "continuous_increase",
                "severity": "high",
                "description": "Bộ nhớ tăng liên tục qua 10 lần đo gần nhất"
            }
            result["patterns_detected"].append(pattern)
            result["recommendations"].append("Kiểm tra rò rỉ bộ nhớ và thực hiện tối ưu hóa mạnh")
            result["risk_level"] = "high"

        # 2. Kiểm tra mẫu dao động mạnh
        if std_dev > 10:
            pattern = {
                "type": "high_volatility",
                "severity": "medium",
                "description": f"Bộ nhớ dao động mạnh (độ lệch chuẩn: {std_dev:.2f}%)"
            }
            result["patterns_detected"].append(pattern)
            result["recommendations"].append("Điều chỉnh tần suất tối ưu hóa bộ nhớ")
            if result["risk_level"] != "high":
                result["risk_level"] = "medium"

        # 3. Kiểm tra mẫu tăng đột biến
        has_spike = False
        for i in range(1, len(memory_values)):
            if memory_values[i] - memory_values[i-1] > 15:  # Tăng hơn 15% trong một lần đo
                has_spike = True
                break

        if has_spike:
            pattern = {
                "type": "memory_spike",
                "severity": "high",
                "description": "Phát hiện đột biến bộ nhớ (tăng hơn 15% trong một lần đo)"
            }
            result["patterns_detected"].append(pattern)
            result["recommendations"].append("Kiểm tra hoạt động gây đột biến bộ nhớ")
            result["risk_level"] = "high"

        # 4. Kiểm tra mẫu bộ nhớ cao liên tục
        high_memory_count = sum(1 for x in memory_values if x > 85)
        if high_memory_count >= 5:  # Bộ nhớ cao trong ít nhất 5 lần đo
            pattern = {
                "type": "sustained_high_memory",
                "severity": "high",
                "description": f"Bộ nhớ duy trì ở mức cao (>85%) trong {high_memory_count}/10 lần đo gần nhất"
            }
            result["patterns_detected"].append(pattern)
            result["recommendations"].append("Thực hiện tối ưu hóa bộ nhớ mạnh và giảm ngưỡng tối ưu hóa")
            result["risk_level"] = "high"

        # 5. Kiểm tra mẫu tối ưu hóa không hiệu quả
        optimization_count = sum(1 for entry in recent if entry.get("saved", 0) > 0)
        if optimization_count >= 3:
            avg_saved = sum(entry.get("saved", 0) for entry in recent) / optimization_count
            if avg_saved < 5:  # Trung bình tiết kiệm ít hơn 5%
                pattern = {
                    "type": "ineffective_optimization",
                    "severity": "medium",
                    "description": f"Tối ưu hóa bộ nhớ không hiệu quả (trung bình tiết kiệm {avg_saved:.2f}%)"
                }
                result["patterns_detected"].append(pattern)
                result["recommendations"].append("Điều chỉnh chiến lược tối ưu hóa bộ nhớ")
                if result["risk_level"] != "high":
                    result["risk_level"] = "medium"

        return result

    def _analyze_memory_usage_patterns(self) -> Dict[str, Any]:
        """
        Phân tích các mẫu sử dụng bộ nhớ để phát hiện các vấn đề tiềm ẩn.

        Returns:
            Dict[str, Any]: Kết quả phân tích mẫu sử dụng bộ nhớ
        """
        result = {
            "patterns_detected": [],
            "recommendations": [],
            "risk_level": "low",
            "timestamp": time.time()
        }

        if not hasattr(self, "memory_stats") or "memory_history" not in self.memory_stats:
            return result

        history = self.memory_stats["memory_history"]
        if len(history) < 10:  # Cần ít nhất 10 điểm dữ liệu
            return result

        # Lấy 10 điểm dữ liệu gần nhất
        recent = history[-10:]

        # Tính các thông số thống kê
        memory_values = [entry["percent"] for entry in recent]
        mean = sum(memory_values) / len(memory_values)
        variance = sum((x - mean) ** 2 for x in memory_values) / len(memory_values)
        std_dev = variance ** 0.5
        min_val = min(memory_values)
        max_val = max(memory_values)
        range_val = max_val - min_val

        # Lưu các thông số thống kê
        result["statistics"] = {
            "mean": mean,
            "std_dev": std_dev,
            "min": min_val,
            "max": max_val,
            "range": range_val,
            "coefficient_of_variation": (std_dev / mean) * 100 if mean > 0 else 0
        }

        # Phát hiện các mẫu sử dụng bộ nhớ

        # 1. Kiểm tra mẫu tăng liên tục
        is_increasing = all(memory_values[i] >= memory_values[i-1] for i in range(1, len(memory_values)))
        if is_increasing:
            pattern = {
                "type": "continuous_increase",
                "severity": "high",
                "description": "Bộ nhớ tăng liên tục qua 10 lần đo gần nhất"
            }
            result["patterns_detected"].append(pattern)
            result["recommendations"].append("Kiểm tra rò rỉ bộ nhớ và thực hiện tối ưu hóa mạnh")
            result["risk_level"] = "high"

        # 2. Kiểm tra mẫu dao động mạnh
        if std_dev > 10:
            pattern = {
                "type": "high_volatility",
                "severity": "medium",
                "description": f"Bộ nhớ dao động mạnh (độ lệch chuẩn: {std_dev:.2f}%)"
            }
            result["patterns_detected"].append(pattern)
            result["recommendations"].append("Điều chỉnh tần suất tối ưu hóa bộ nhớ")
            if result["risk_level"] != "high":
                result["risk_level"] = "medium"

        # 3. Kiểm tra mẫu tăng đột biến
        has_spike = False
        for i in range(1, len(memory_values)):
            if memory_values[i] - memory_values[i-1] > 15:  # Tăng hơn 15% trong một lần đo
                has_spike = True
                break

        if has_spike:
            pattern = {
                "type": "memory_spike",
                "severity": "high",
                "description": "Phát hiện đột biến bộ nhớ (tăng hơn 15% trong một lần đo)"
            }
            result["patterns_detected"].append(pattern)
            result["recommendations"].append("Kiểm tra hoạt động gây đột biến bộ nhớ")
            result["risk_level"] = "high"

        # 4. Kiểm tra mẫu bộ nhớ cao liên tục
        high_memory_count = sum(1 for x in memory_values if x > 85)
        if high_memory_count >= 5:  # Bộ nhớ cao trong ít nhất 5 lần đo
            pattern = {
                "type": "sustained_high_memory",
                "severity": "high",
                "description": f"Bộ nhớ duy trì ở mức cao (>85%) trong {high_memory_count}/10 lần đo gần nhất"
            }
            result["patterns_detected"].append(pattern)
            result["recommendations"].append("Thực hiện tối ưu hóa bộ nhớ mạnh và giảm ngưỡng tối ưu hóa")
            result["risk_level"] = "high"

        # 5. Kiểm tra mẫu tối ưu hóa không hiệu quả
        optimization_count = sum(1 for entry in recent if entry.get("saved", 0) > 0)
        if optimization_count >= 3:
            avg_saved = sum(entry.get("saved", 0) for entry in recent) / optimization_count
            if avg_saved < 5:  # Trung bình tiết kiệm ít hơn 5%
                pattern = {
                    "type": "ineffective_optimization",
                    "severity": "medium",
                    "description": f"Tối ưu hóa bộ nhớ không hiệu quả (trung bình tiết kiệm {avg_saved:.2f}%)"
                }
                result["patterns_detected"].append(pattern)
                result["recommendations"].append("Điều chỉnh chiến lược tối ưu hóa bộ nhớ")
                if result["risk_level"] != "high":
                    result["risk_level"] = "medium"

        return result

    def _analyze_optimization_efficiency(self) -> Dict[str, Any]:
        """
        Phân tích hiệu quả của các lần tối ưu hóa bộ nhớ trước đó.

        Returns:
            Dict[str, Any]: Kết quả phân tích hiệu quả tối ưu hóa
        """
        result = {
            "average_efficiency": 0,
            "efficiency_trend": "stable",
            "recommendations": [],
            "timestamp": time.time()
        }

        if not hasattr(self, "memory_stats") or "optimization_efficiency_history" not in self.memory_stats:
            return result

        history = self.memory_stats["optimization_efficiency_history"]
        if len(history) < 3:  # Cần ít nhất 3 điểm dữ liệu
            return result

        # Tính hiệu quả trung bình
        efficiencies = [entry["efficiency"] for entry in history]
        avg_efficiency = sum(efficiencies) / len(efficiencies)
        result["average_efficiency"] = avg_efficiency

        # Phân tích xu hướng hiệu quả
        recent = history[-3:]
        is_increasing = all(recent[i]["efficiency"] >= recent[i-1]["efficiency"] for i in range(1, len(recent)))
        is_decreasing = all(recent[i]["efficiency"] <= recent[i-1]["efficiency"] for i in range(1, len(recent)))

        if is_increasing:
            result["efficiency_trend"] = "increasing"
        elif is_decreasing:
            result["efficiency_trend"] = "decreasing"
        else:
            result["efficiency_trend"] = "fluctuating"

        # Phân tích hiệu quả theo loại tối ưu hóa
        optimization_types = {}
        for entry in history:
            opt_type = entry["optimization_type"]
            if opt_type not in optimization_types:
                optimization_types[opt_type] = {"count": 0, "total_efficiency": 0}

            optimization_types[opt_type]["count"] += 1
            optimization_types[opt_type]["total_efficiency"] += entry["efficiency"]

        # Tính hiệu quả trung bình cho mỗi loại
        for opt_type, data in optimization_types.items():
            if data["count"] > 0:
                data["average_efficiency"] = data["total_efficiency"] / data["count"]

        result["optimization_types"] = optimization_types

        # Đưa ra khuyến nghị
        if avg_efficiency < 5:
            result["recommendations"].append("Điều chỉnh chiến lược tối ưu hóa do hiệu quả thấp")

            # Tìm loại tối ưu hóa hiệu quả nhất
            best_type = max(optimization_types.items(), key=lambda x: x[1]["average_efficiency"], default=(None, None))
            if best_type[0]:
                result["recommendations"].append(f"Ưu tiên sử dụng tối ưu hóa loại '{best_type[0]}' (hiệu quả: {best_type[1]['average_efficiency']:.2f}%)")

        if result["efficiency_trend"] == "decreasing":
            result["recommendations"].append("Xem xét thay đổi chiến lược tối ưu hóa do hiệu quả đang giảm dần")

        return result

    def _analyze_memory_volatility(self, volatility_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Phân tích chi tiết độ biến động bộ nhớ để phát hiện các vấn đề tiềm ẩn.

        Args:
            volatility_data: Dữ liệu độ biến động bộ nhớ

        Returns:
            Dict[str, Any]: Kết quả phân tích độ biến động bộ nhớ
        """
        result = {
            "risk_level": "low",
            "description": "",
            "recommendations": [],
            "adjust_threshold": False,
            "adjust_interval": False,
            "timestamp": time.time()
        }

        if not volatility_data or len(volatility_data) < 3:
            return result

        # Tính các thông số thống kê
        std_devs = [item["std_dev"] for item in volatility_data]
        means = [item["mean"] for item in volatility_data]
        ranges = [item["range"] for item in volatility_data]
        cvs = [item["coefficient_of_variation"] for item in volatility_data]

        avg_std_dev = sum(std_devs) / len(std_devs)
        avg_cv = sum(cvs) / len(cvs)
        max_std_dev = max(std_devs)
        max_range = max(ranges)

        # Lưu các thông số thống kê
        result["statistics"] = {
            "avg_std_dev": avg_std_dev,
            "avg_cv": avg_cv,
            "max_std_dev": max_std_dev,
            "max_range": max_range,
            "std_dev_trend": "increasing" if std_devs[-1] > std_devs[0] else "decreasing" if std_devs[-1] < std_devs[0] else "stable"
        }

        # Phân tích mức độ rủi ro

        # 1. Kiểm tra độ biến động cao
        if max_std_dev > 15:
            result["risk_level"] = "high"
            result["description"] = f"Độ biến động bộ nhớ rất cao (độ lệch chuẩn: {max_std_dev:.2f}%)"
            result["recommendations"].append("Giảm ngưỡng bộ nhớ để tối ưu hóa thường xuyên hơn")
            result["recommendations"].append("Kiểm tra các hoạt động gây biến động mạnh")
            result["adjust_threshold"] = True
            result["adjust_interval"] = True
        elif max_std_dev > 10:
            result["risk_level"] = "medium"
            result["description"] = f"Độ biến động bộ nhớ cao (độ lệch chuẩn: {max_std_dev:.2f}%)"
            result["recommendations"].append("Điều chỉnh ngưỡng bộ nhớ")
            result["adjust_threshold"] = True

        # 2. Kiểm tra hệ số biến thiên cao
        if avg_cv > 20:
            if result["risk_level"] != "high":
                result["risk_level"] = "high"
            result["description"] += f", Hệ số biến thiên cao ({avg_cv:.2f}%)"
            result["recommendations"].append("Điều chỉnh khoảng thời gian kiểm tra bộ nhớ")
            result["adjust_interval"] = True
        elif avg_cv > 15:
            if result["risk_level"] != "high":
                result["risk_level"] = "medium"
            result["description"] += f", Hệ số biến thiên khá cao ({avg_cv:.2f}%)"

        # 3. Kiểm tra xu hướng tăng độ biến động
        is_increasing = all(std_devs[i] >= std_devs[i-1] for i in range(1, len(std_devs)))
        if is_increasing and std_devs[-1] > 1.5 * std_devs[0]:
            if result["risk_level"] != "high":
                result["risk_level"] = "high"
            result["description"] += f", Độ biến động đang tăng nhanh ({std_devs[0]:.2f}% -> {std_devs[-1]:.2f}%)"
            result["recommendations"].append("Theo dõi sát sao việc sử dụng bộ nhớ")
            result["adjust_threshold"] = True
            result["adjust_interval"] = True

        # 4. Kiểm tra biên độ dao động lớn
        if max_range > 30:
            if result["risk_level"] != "high":
                result["risk_level"] = "high"
            result["description"] += f", Biên độ dao động rất lớn ({max_range:.2f}%)"
            result["recommendations"].append("Kiểm tra các hoạt động gây đột biến bộ nhớ")
            result["adjust_threshold"] = True
        elif max_range > 20:
            if result["risk_level"] != "high":
                result["risk_level"] = "medium"
            result["description"] += f", Biên độ dao động lớn ({max_range:.2f}%)"

        # Nếu không có vấn đề gì, đưa ra mô tả mặc định
        if result["risk_level"] == "low":
            result["description"] = f"Độ biến động bộ nhớ ổn định (độ lệch chuẩn: {avg_std_dev:.2f}%, hệ số biến thiên: {avg_cv:.2f}%)"

        # Đảm bảo không có khuyến nghị trùng lặp
        result["recommendations"] = list(set(result["recommendations"]))

        return result

    def _analyze_pattern_trend(self) -> None:
        """
        Phân tích xu hướng mẫu bộ nhớ để phát hiện các vấn đề tiềm ẩn.
        """
        if not hasattr(self, "memory_stats") or "pattern_history" not in self.memory_stats:
            return

        pattern_history = self.memory_stats["pattern_history"]
        if len(pattern_history) < 3:  # Cần ít nhất 3 mẫu để phân tích
            return

        # Lấy 5 mẫu gần nhất hoặc tất cả nếu ít hơn 5
        recent_patterns = pattern_history[-min(5, len(pattern_history)):]

        # Đếm số lần xuất hiện của mỗi mẫu
        pattern_counts = {}
        for entry in recent_patterns:
            pattern = entry["pattern"]
            pattern_counts[pattern] = pattern_counts.get(pattern, 0) + 1

        # Tìm mẫu phổ biến nhất
        most_common_pattern = max(pattern_counts.items(), key=lambda x: x[1])
        pattern_name, count = most_common_pattern

        # Lưu thông tin xu hướng mẫu
        self.memory_stats["pattern_trend"] = {
            "most_common_pattern": pattern_name,
            "count": count,
            "total_patterns": len(recent_patterns),
            "timestamp": time.time()
        }

        # Phân tích dựa trên loại mẫu phổ biến
        if pattern_name in ["rapid_increase", "steady_increase"] and count >= 3:
            # Nếu mẫu tăng xuất hiện nhiều lần liên tiếp, có thể có rò rỉ bộ nhớ
            logger.warning(f"Phát hiện xu hướng tăng bộ nhớ liên tục: {pattern_name} ({count}/{len(recent_patterns)} mẫu)")

            # Cập nhật thông tin rò rỉ
            if "leak_detection" in self.memory_stats:
                self.memory_stats["leak_detection"]["pattern_based_leak"] = True
                self.memory_stats["leak_detection"]["pattern_based_leak_time"] = time.time()
                self.memory_stats["leak_detection"]["pattern_based_leak_pattern"] = pattern_name

                # Tăng số lần phát hiện rò rỉ tiềm năng
                self.memory_stats["leak_detection"]["potential_leak_count"] = self.memory_stats["leak_detection"].get("potential_leak_count", 0) + 1

        elif pattern_name == "spike" and count >= 2:
            # Nếu có nhiều đột biến, có thể có vấn đề với quản lý bộ nhớ
            logger.warning(f"Phát hiện nhiều đột biến bộ nhớ: {count}/{len(recent_patterns)} mẫu")

            # Giảm ngưỡng bộ nhớ để tối ưu hóa sớm hơn
            if hasattr(self, "memory_threshold"):
                old_threshold = self.memory_threshold
                self.memory_threshold = max(65, self.memory_threshold - 5)
                logger.warning(f"Giảm ngưỡng bộ nhớ từ {old_threshold:.1f}% xuống {self.memory_threshold:.1f}% do phát hiện nhiều đột biến")

        elif pattern_name in ["high_volatility", "moderate_volatility"] and count >= 3:
            # Nếu bộ nhớ dao động mạnh, có thể có vấn đề với quản lý bộ nhớ
            logger.warning(f"Phát hiện bộ nhớ dao động mạnh liên tục: {pattern_name} ({count}/{len(recent_patterns)} mẫu)")

            # Điều chỉnh tần suất kiểm tra bộ nhớ
            if hasattr(self, "memory_check_interval"):
                old_interval = self.memory_check_interval
                self.memory_check_interval = max(20, self.memory_check_interval // 1.5)
                logger.info(f"Điều chỉnh khoảng thời gian kiểm tra bộ nhớ xuống {self.memory_check_interval}s do dao động mạnh")

        elif pattern_name == "stable" and count >= 4:
            # Nếu bộ nhớ ổn định, có thể tăng ngưỡng để giảm tần suất tối ưu hóa
            logger.info(f"Phát hiện bộ nhớ ổn định: {count}/{len(recent_patterns)} mẫu")

            # Tăng ngưỡng bộ nhớ nếu hiện tại thấp
            if hasattr(self, "memory_threshold") and self.memory_threshold < 80:
                old_threshold = self.memory_threshold
                self.memory_threshold = min(85, self.memory_threshold + 5)
                logger.info(f"Tăng ngưỡng bộ nhớ từ {old_threshold:.1f}% lên {self.memory_threshold:.1f}% do bộ nhớ ổn định")

    def _detect_memory_pattern(self, memory_samples: List[Dict]) -> str:
        """
        Phát hiện mẫu sử dụng bộ nhớ từ các mẫu.

        Args:
            memory_samples: Danh sách các mẫu bộ nhớ

        Returns:
            str: Tên mẫu phát hiện được hoặc None nếu không phát hiện
        """
        if not memory_samples or len(memory_samples) < 3:
            return None

        # Lấy giá trị phần trăm bộ nhớ
        values = [sample["percent"] for sample in memory_samples]

        # Kiểm tra mẫu tăng đều
        is_increasing = all(values[i] >= values[i-1] for i in range(1, len(values)))
        if is_increasing:
            # Tính tốc độ tăng
            growth_rate = (values[-1] - values[0]) / (len(values) - 1)

            if growth_rate > 5:
                return "rapid_increase"  # Tăng nhanh
            elif growth_rate > 1:
                return "steady_increase"  # Tăng đều
            else:
                return "slow_increase"  # Tăng chậm

        # Kiểm tra mẫu giảm đều
        is_decreasing = all(values[i] <= values[i-1] for i in range(1, len(values)))
        if is_decreasing:
            return "decreasing"  # Giảm

        # Kiểm tra mẫu dao động
        min_val = min(values)
        max_val = max(values)
        range_val = max_val - min_val

        if range_val > 20:
            return "high_volatility"  # Dao động mạnh
        elif range_val > 10:
            return "moderate_volatility"  # Dao động vừa
        elif range_val > 5:
            return "low_volatility"  # Dao động nhẹ

        # Kiểm tra mẫu ổn định
        if range_val < 3:
            return "stable"  # Ổn định

        # Kiểm tra mẫu tăng đột biến
        for i in range(1, len(values)):
            if values[i] - values[i-1] > 10:
                return "spike"  # Đột biến

        # Mẫu không xác định
        return "irregular"

    def _analyze_leak_pattern(self, pattern: Dict[str, Any]) -> None:
        """
        Phân tích mẫu rò rỉ bộ nhớ để xác định loại rò rỉ.

        Args:
            pattern: Mẫu rò rỉ bộ nhớ
        """
        if not hasattr(self, "memory_stats") or "leak_detection" not in self.memory_stats:
            return

        leak_detection = self.memory_stats["leak_detection"]

        # Khởi tạo từ điển pattern_matches nếu chưa có
        if "pattern_matches" not in leak_detection:
            leak_detection["pattern_matches"] = {}

        pattern_type = pattern.get("pattern_type", "unknown")
        growth_rate = pattern.get("growth_rate", 0)
        current_memory = pattern.get("current_memory", 0)

        # Phân tích mẫu dựa trên loại
        if pattern_type == "spike":
            # Đột biến bộ nhớ
            leak_detection["pattern_matches"]["memory_spike"] = {
                "timestamp": time.time(),
                "growth_rate": growth_rate,
                "current_memory": current_memory,
                "confidence": 0.8 if growth_rate > 10 else 0.6
            }
            logger.warning(f"Phát hiện đột biến bộ nhớ với tốc độ tăng {growth_rate:.2f}%")

        elif pattern_type == "steady_increase":
            # Tăng đều đặn
            leak_detection["pattern_matches"]["steady_leak"] = {
                "timestamp": time.time(),
                "growth_rate": growth_rate,
                "current_memory": current_memory,
                "confidence": 0.7 if growth_rate > 2 else 0.5
            }
            logger.warning(f"Phát hiện rò rỉ bộ nhớ đều đặn với tốc độ tăng {growth_rate:.2f}%")

        elif pattern_type == "consecutive_increase":
            # Tăng liên tiếp
            consecutive_increases = pattern.get("consecutive_increases", 0)
            leak_detection["pattern_matches"]["consecutive_leak"] = {
                "timestamp": time.time(),
                "growth_rate": growth_rate,
                "current_memory": current_memory,
                "consecutive_increases": consecutive_increases,
                "confidence": min(0.9, 0.5 + consecutive_increases * 0.1)
            }
            logger.warning(f"Phát hiện rò rỉ bộ nhớ sau {consecutive_increases} lần tăng liên tiếp")

        # Giới hạn số lượng mẫu
        if len(leak_detection["pattern_matches"]) > 5:
            # Xóa mẫu cũ nhất
            oldest_key = min(leak_detection["pattern_matches"].keys(),
                            key=lambda k: leak_detection["pattern_matches"][k].get("timestamp", 0))
            leak_detection["pattern_matches"].pop(oldest_key, None)

    def _analyze_long_term_memory_trend(self, leak_detection: Dict[str, Any], current_memory: float) -> None:
        """
        Phân tích xu hướng sử dụng bộ nhớ dài hạn.

        Args:
            leak_detection: Thông tin phát hiện rò rỉ
            current_memory: Mức sử dụng bộ nhớ hiện tại
        """
        if "long_term_trend" not in leak_detection:
            # Khởi tạo thông tin xu hướng dài hạn
            leak_detection["long_term_trend"] = {
                "start_time": time.time(),
                "start_memory": current_memory,
                "samples": []
            }
            return

        trend = leak_detection["long_term_trend"]
        samples = trend.get("samples", [])

        # Cần ít nhất 10 mẫu để phân tích
        if len(samples) < 10:
            return

        # Tính thời gian trôi qua từ khi bắt đầu theo dõi (giây)
        elapsed_time = time.time() - trend["start_time"]

        # Tính tốc độ tăng trưởng dài hạn (%/giờ)
        if elapsed_time > 0:
            long_term_growth_rate = (current_memory - trend["start_memory"]) / (elapsed_time / 3600)
            trend["growth_rate_per_hour"] = long_term_growth_rate

            # Nếu tốc độ tăng trưởng dài hạn cao, có thể có rò rỉ bộ nhớ
            if long_term_growth_rate > 5:  # Tăng hơn 5% mỗi giờ
                logger.warning(f"Phát hiện rò rỉ bộ nhớ dài hạn: {long_term_growth_rate:.2f}%/giờ")

                # Cập nhật thông tin rò rỉ
                leak_detection["long_term_leak"] = {
                    "detected": True,
                    "timestamp": time.time(),
                    "growth_rate_per_hour": long_term_growth_rate,
                    "elapsed_hours": elapsed_time / 3600
                }

                # Tăng số lần phát hiện rò rỉ
                leak_detection["leak_detected_count"] = leak_detection.get("leak_detected_count", 0) + 1

                # Đánh dấu rò rỉ
                leak_detection["leak_detected"] = True

        # Phân tích mẫu sử dụng bộ nhớ
        if len(samples) >= 20:
            # Lấy 20 mẫu gần nhất
            recent_samples = samples[-20:]

            # Tính độ biến động
            memory_values = [sample["memory"] for sample in recent_samples]
            mean = sum(memory_values) / len(memory_values)
            variance = sum((x - mean) ** 2 for x in memory_values) / len(memory_values)
            std_dev = variance ** 0.5

            # Lưu độ biến động
            trend["volatility"] = std_dev

            # Phát hiện mẫu sử dụng bộ nhớ
            # Kiểm tra mẫu tăng đều
            is_increasing = all(memory_values[i] >= memory_values[i-1] for i in range(1, len(memory_values)))
            if is_increasing:
                trend["pattern"] = "increasing"

                # Tính tốc độ tăng
                growth_rate = (memory_values[-1] - memory_values[0]) / len(memory_values)
                trend["growth_rate"] = growth_rate

                if growth_rate > 1:
                    logger.warning(f"Phát hiện xu hướng tăng bộ nhớ dài hạn: {growth_rate:.2f}%/mẫu")

            # Kiểm tra mẫu dao động
            elif std_dev > 5:
                trend["pattern"] = "volatile"
                logger.warning(f"Phát hiện bộ nhớ dao động mạnh: độ lệch chuẩn {std_dev:.2f}%")

    def _detect_memory_leak(self, current_memory: float) -> bool:
        """
        Phát hiện rò rỉ bộ nhớ dựa trên xu hướng sử dụng bộ nhớ.

        Args:
            current_memory: Mức sử dụng bộ nhớ hiện tại (%)

        Returns:
            bool: True nếu phát hiện rò rỉ bộ nhớ, False nếu không
        """
        if not hasattr(self, "memory_stats") or not self.use_memory_optimization:
            return False

        # Kiểm tra xem có thông tin theo dõi rò rỉ bộ nhớ không
        if "leak_detection" not in self.memory_stats:
            # Khởi tạo thông tin theo dõi rò rỉ bộ nhớ nếu chưa có
            self.memory_stats["leak_detection"] = {
                "consecutive_increases": 0,
                "last_values": [],
                "leak_detected": False,
                "leak_detected_count": 0,
                "last_leak_time": 0,
                "leak_patterns": [],
                "growth_rates": [],
                "pattern_matches": {},
                "long_term_trend": {
                    "start_time": time.time(),
                    "start_memory": current_memory,
                    "samples": []
                }
            }

        # Cập nhật lịch sử sử dụng bộ nhớ
        leak_detection = self.memory_stats["leak_detection"]
        last_values = leak_detection["last_values"]

        # Thêm giá trị hiện tại vào lịch sử
        last_values.append(current_memory)

        # Cập nhật xu hướng dài hạn
        if "long_term_trend" in leak_detection:
            leak_detection["long_term_trend"]["samples"].append({
                "timestamp": time.time(),
                "memory": current_memory
            })

            # Giữ lịch sử xu hướng dài hạn trong giới hạn
            max_samples = 100
            if len(leak_detection["long_term_trend"]["samples"]) > max_samples:
                leak_detection["long_term_trend"]["samples"] = leak_detection["long_term_trend"]["samples"][-max_samples:]

        # Giữ lịch sử trong giới hạn kích thước
        history_size = getattr(self, "memory_history_size", 10)
        if len(last_values) > history_size:
            last_values.pop(0)

        # Cần ít nhất 3 giá trị để phát hiện xu hướng
        if len(last_values) < 3:
            return leak_detection.get("leak_detected", False)

        # Tính tốc độ tăng trưởng bộ nhớ
        if len(last_values) >= 2:
            # Tính tốc độ tăng trưởng trung bình
            growth_rate = (last_values[-1] - last_values[0]) / (len(last_values) - 1)
            leak_detection["current_growth_rate"] = growth_rate

            # Lưu lịch sử tốc độ tăng trưởng
            if "growth_rates" not in leak_detection:
                leak_detection["growth_rates"] = []

            leak_detection["growth_rates"].append({
                "timestamp": time.time(),
                "rate": growth_rate,
                "memory": current_memory
            })

            # Giữ lịch sử tốc độ tăng trưởng trong giới hạn
            if len(leak_detection["growth_rates"]) > 10:
                leak_detection["growth_rates"].pop(0)

            # Phát hiện tăng đột biến
            growth_threshold = 5.0
            if current_memory > 80:  # Nếu bộ nhớ đã cao, giảm ngưỡng phát hiện
                growth_threshold = 3.0

            if growth_rate > growth_threshold:
                logger.warning(f"Phát hiện tăng bộ nhớ đột biến: {growth_rate:.2f}%/lần đo (ngưỡng: {growth_threshold:.1f}%)")
                leak_detection["consecutive_increases"] += 2  # Tăng nhanh hơn

                # Lưu mẫu rò rỉ
                if "leak_patterns" not in leak_detection:
                    leak_detection["leak_patterns"] = []

                pattern = {
                    "timestamp": time.time(),
                    "values": last_values.copy(),
                    "growth_rate": growth_rate,
                    "current_memory": current_memory,
                    "pattern_type": "spike"  # Đánh dấu loại mẫu là đột biến
                }

                leak_detection["leak_patterns"].append(pattern)

                # Phân tích mẫu rò rỉ để tìm kiểu rò rỉ
                self._analyze_leak_pattern(pattern)

                # Giữ số lượng mẫu trong giới hạn
                if len(leak_detection["leak_patterns"]) > 10:
                    leak_detection["leak_patterns"].pop(0)

            # Phát hiện tăng đều đặn
            elif growth_rate > 0 and all(last_values[i] >= last_values[i-1] for i in range(1, len(last_values))):
                # Nếu tất cả các giá trị đều tăng đều đặn
                pattern = {
                    "timestamp": time.time(),
                    "values": last_values.copy(),
                    "growth_rate": growth_rate,
                    "current_memory": current_memory,
                    "pattern_type": "steady_increase"  # Đánh dấu loại mẫu là tăng đều
                }

                leak_detection["leak_patterns"].append(pattern)
                self._analyze_leak_pattern(pattern)

                # Giữ số lượng mẫu trong giới hạn
                if len(leak_detection["leak_patterns"]) > 10:
                    leak_detection["leak_patterns"].pop(0)

        # Phân tích xu hướng dài hạn
        self._analyze_long_term_memory_trend(leak_detection, current_memory)

        # Kiểm tra xem bộ nhớ có tăng liên tục không
        is_increasing = True
        threshold = getattr(self, "memory_leak_percent_threshold", 2.0)

        # Điều chỉnh ngưỡng dựa trên mức sử dụng bộ nhớ hiện tại
        if current_memory > 85:
            threshold = max(0.5, threshold / 2)  # Giảm ngưỡng khi bộ nhớ cao
        elif current_memory < 50:
            threshold = min(4.0, threshold * 1.5)  # Tăng ngưỡng khi bộ nhớ thấp

        for i in range(1, len(last_values)):
            # Nếu giá trị hiện tại không lớn hơn giá trị trước đó + ngưỡng
            if last_values[i] <= last_values[i-1] + threshold:
                is_increasing = False
                break

        # Cập nhật số lần tăng liên tiếp
        if is_increasing:
            leak_detection["consecutive_increases"] += 1

            # Nếu tăng liên tục nhiều lần, lưu mẫu
            if leak_detection["consecutive_increases"] >= 3:
                pattern = {
                    "timestamp": time.time(),
                    "values": last_values.copy(),
                    "growth_rate": growth_rate if 'growth_rate' in locals() else 0,
                    "current_memory": current_memory,
                    "consecutive_increases": leak_detection["consecutive_increases"],
                    "pattern_type": "consecutive_increase"
                }

                leak_detection["leak_patterns"].append(pattern)
                self._analyze_leak_pattern(pattern)

                # Giữ số lượng mẫu trong giới hạn
                if len(leak_detection["leak_patterns"]) > 10:
                    leak_detection["leak_patterns"].pop(0)
        else:
            # Giảm dần số lần phát hiện thay vì đặt về 0
            leak_detection["consecutive_increases"] = max(0, leak_detection["consecutive_increases"] - 1)

        # Kiểm tra xem có vượt quá ngưỡng không
        leak_threshold = getattr(self, "memory_leak_threshold", 3)

        # Nếu bộ nhớ đã cao, giảm ngưỡng phát hiện
        if current_memory > 80:
            leak_threshold = max(2, leak_threshold - 1)
        elif current_memory > 90:
            leak_threshold = 1  # Ngưỡng thấp nhất khi bộ nhớ rất cao

        if leak_detection["consecutive_increases"] >= leak_threshold:
            # Kiểm tra thời gian từ lần phát hiện cuối cùng
            current_time = time.time()
            last_leak_time = leak_detection.get("last_leak_time", 0)

            # Chỉ báo rò rỉ nếu đã qua một khoảng thời gian từ lần phát hiện trước
            cooldown_time = 60  # 60 giây
            if current_time - last_leak_time > cooldown_time:
                leak_detection["leak_detected"] = True
                leak_detection["leak_detected_count"] = leak_detection.get("leak_detected_count", 0) + 1
                leak_detection["last_leak_time"] = current_time

                # Lưu thông tin chi tiết về rò rỉ
                leak_info = {
                    "timestamp": current_time,
                    "memory_values": last_values.copy(),
                    "current_memory": current_memory,
                    "consecutive_increases": leak_detection["consecutive_increases"],
                    "detection_method": "consecutive_increases"
                }

                # Thêm thông tin về tốc độ tăng trưởng nếu có
                if "current_growth_rate" in leak_detection:
                    leak_info["growth_rate"] = leak_detection["current_growth_rate"]

                # Thêm thông tin về mẫu rò rỉ nếu có
                if "pattern_matches" in leak_detection and leak_detection["pattern_matches"]:
                    leak_info["pattern_matches"] = leak_detection["pattern_matches"]

                leak_detection["last_leak_info"] = leak_info

                logger.warning(f"Phát hiện rò rỉ bộ nhớ! Bộ nhớ tăng liên tục {leak_detection['consecutive_increases']} lần")
                logger.warning(f"Lịch sử bộ nhớ: {last_values}")

                # Ghi log thêm thông tin chi tiết
                if "current_growth_rate" in leak_detection:
                    logger.warning(f"Tốc độ tăng trưởng bộ nhớ: {leak_detection['current_growth_rate']:.2f}%/lần đo")

                if "pattern_matches" in leak_detection and leak_detection["pattern_matches"]:
                    logger.warning(f"Mẫu rò rỉ phát hiện: {list(leak_detection['pattern_matches'].keys())}")

                # Nếu phát hiện nhiều lần, giảm ngưỡng bộ nhớ
                if leak_detection["leak_detected_count"] > 2 and hasattr(self, "memory_threshold"):
                    old_threshold = self.memory_threshold
                    self.memory_threshold = max(60, self.memory_threshold - 5)
                    logger.warning(f"Giảm ngưỡng bộ nhớ từ {old_threshold:.1f}% xuống {self.memory_threshold:.1f}% do phát hiện rò rỉ nhiều lần")

                return True

        return leak_detection.get("leak_detected", False)

    def _handle_memory_leak(self) -> Dict[str, Any]:
        """
        Xử lý rò rỉ bộ nhớ khi phát hiện.

        Returns:
            Dict[str, Any]: Kết quả xử lý rò rỉ bộ nhớ
        """
        result = {
            "success": True,
            "actions": [],
            "memory_before": self._get_current_memory_usage(),
            "memory_after": None,
            "memory_saved": 0,
            "leak_info": {}
        }

        logger.warning("Bắt đầu xử lý rò rỉ bộ nhớ...")

        try:
            # 0. Lưu thông tin về rò rỉ bộ nhớ
            if hasattr(self, "memory_stats") and "leak_detection" in self.memory_stats:
                leak_detection = self.memory_stats["leak_detection"]
                result["leak_info"] = {
                    "consecutive_increases": leak_detection.get("consecutive_increases", 0),
                    "leak_detected_count": leak_detection.get("leak_detected_count", 0),
                    "current_growth_rate": leak_detection.get("current_growth_rate", 0),
                    "last_values": leak_detection.get("last_values", [])[-5:] if "last_values" in leak_detection else []
                }

                # Lưu thông tin chi tiết về rò rỉ
                if "last_leak_info" in leak_detection:
                    result["leak_info"]["last_leak_info"] = leak_detection["last_leak_info"]

                # Lưu mẫu rò rỉ
                if "leak_patterns" in leak_detection:
                    result["leak_info"]["patterns"] = leak_detection["leak_patterns"]

            # 1. Thực hiện tối ưu hóa mạnh
            self._perform_critical_optimization(result)

            # 2. Xóa tất cả các cache
            cache_attrs = []
            for attr_name in dir(self):
                if "_cache" in attr_name or attr_name.endswith("_cache"):
                    cache_attrs.append(attr_name)

            for attr_name in cache_attrs:
                if hasattr(self, attr_name):
                    try:
                        cache_obj = getattr(self, attr_name)
                        if hasattr(cache_obj, "clear"):
                            cache_obj.clear()
                            result["actions"].append(f"clear_{attr_name}")
                        elif isinstance(cache_obj, dict):
                            setattr(self, attr_name, {})
                            result["actions"].append(f"clear_dict_{attr_name}")
                        elif isinstance(cache_obj, list):
                            setattr(self, attr_name, [])
                            result["actions"].append(f"clear_list_{attr_name}")
                    except Exception as e:
                        logger.warning(f"Lỗi khi xóa cache {attr_name}: {str(e)}")

            # 3. Giải phóng bộ nhớ không sử dụng
            import gc
            gc.collect(0)  # Thu gom thế hệ trẻ
            gc.collect(1)  # Thu gom thế hệ trung gian
            gc.collect(2)  # Thu gom tất cả các thế hệ
            result["actions"].append("multiple_gc_collect")

            # 4. Thử sử dụng malloc_trim nếu có sẵn (Linux)
            try:
                import ctypes
                libc = ctypes.CDLL("libc.so.6")
                if hasattr(libc, "malloc_trim"):
                    libc.malloc_trim(0)
                    result["actions"].append("malloc_trim")
            except Exception:
                pass

            # 5. Giảm ngưỡng bộ nhớ để tối ưu hóa thường xuyên hơn
            if hasattr(self, "memory_threshold"):
                old_threshold = self.memory_threshold
                # Giảm ngưỡng nhiều hơn nếu phát hiện rò rỉ nhiều lần
                leak_count = 0
                if hasattr(self, "memory_stats") and "leak_detection" in self.memory_stats:
                    leak_count = self.memory_stats["leak_detection"].get("leak_detected_count", 0)

                reduction = 15
                if leak_count > 2:
                    reduction = 20
                if leak_count > 5:
                    reduction = 25

                self.memory_threshold = max(50, self.memory_threshold - reduction)
                result["actions"].append(f"reduce_memory_threshold_{old_threshold}_to_{self.memory_threshold}")

            # 6. Giảm khoảng thời gian kiểm tra bộ nhớ
            if hasattr(self, "memory_check_interval"):
                old_interval = self.memory_check_interval
                self.memory_check_interval = max(10, self.memory_check_interval // 3)
                result["actions"].append(f"reduce_check_interval_{old_interval}_to_{self.memory_check_interval}")

            # 7. Đặt lại trạng thái phát hiện rò rỉ
            if hasattr(self, "memory_stats") and "leak_detection" in self.memory_stats:
                self.memory_stats["leak_detection"]["leak_detected"] = False
                # Giảm số lần tăng liên tiếp nhưng không đặt về 0 hoàn toàn
                self.memory_stats["leak_detection"]["consecutive_increases"] = max(0, self.memory_stats["leak_detection"]["consecutive_increases"] - 2)
                result["actions"].append("reset_leak_detection")

                # Lưu thời gian xử lý rò rỉ
                self.memory_stats["leak_detection"]["last_handled_time"] = time.time()

                # Lưu thông tin về lần xử lý rò rỉ này
                if "leak_handling_history" not in self.memory_stats["leak_detection"]:
                    self.memory_stats["leak_detection"]["leak_handling_history"] = []

                # Giới hạn kích thước lịch sử
                if len(self.memory_stats["leak_detection"]["leak_handling_history"]) > 5:
                    self.memory_stats["leak_detection"]["leak_handling_history"].pop(0)

            # 8. Thử giải phóng bộ nhớ Python
            try:
                # Xóa các đối tượng không sử dụng trong sys.modules
                import sys
                for module_name in list(sys.modules.keys()):
                    if module_name.startswith('_') and not module_name.startswith('__'):
                        if module_name in sys.modules:
                            try:
                                del sys.modules[module_name]
                                result["actions"].append(f"unload_module_{module_name}")
                            except Exception:
                                pass
            except Exception as e:
                logger.warning(f"Lỗi khi giải phóng module: {str(e)}")

            # Đo bộ nhớ sau khi xử lý
            memory_after = self._get_current_memory_usage()
            result["memory_after"] = memory_after
            result["memory_saved"] = max(0, result["memory_before"] - memory_after)

            # Lưu thông tin xử lý rò rỉ vào lịch sử
            if hasattr(self, "memory_stats") and "leak_detection" in self.memory_stats:
                if "leak_handling_history" in self.memory_stats["leak_detection"]:
                    self.memory_stats["leak_detection"]["leak_handling_history"].append({
                        "timestamp": time.time(),
                        "memory_before": result["memory_before"],
                        "memory_after": memory_after,
                        "memory_saved": result["memory_saved"],
                        "actions": result["actions"]
                    })

            logger.info(f"Xử lý rò rỉ bộ nhớ hoàn tất: {result['memory_before']:.1f}% -> {memory_after:.1f}% (tiết kiệm: {result['memory_saved']:.1f}%)")

        except Exception as e:
            logger.error(f"Lỗi khi xử lý rò rỉ bộ nhớ: {str(e)}")
            result["success"] = False
            result["error"] = str(e)

        return result

    def _optimize_memory(self, force: bool = False) -> Dict[str, Any]:
        """
        Tối ưu hóa bộ nhớ.

        Args:
            force: Có bắt buộc tối ưu hóa không, bất kể ngưỡng bộ nhớ

        Returns:
            Dict[str, Any]: Kết quả tối ưu hóa
        """
        # Lấy thông tin bộ nhớ hiện tại
        current_memory = self._get_current_memory_usage()

        # Ghi lại bộ nhớ trước khi tối ưu hóa
        memory_before = current_memory

        # Khởi tạo kết quả
        result = {
            "success": True,
            "optimized": True,
            "memory_before": memory_before,
            "memory_after": None,
            "memory_saved": 0,
            "actions": []
        }

        try:
            # 0. Kiểm tra rò rỉ bộ nhớ
            leak_detected = self._detect_memory_leak(current_memory)
            if leak_detected:
                logger.warning("Phát hiện rò rỉ bộ nhớ, thực hiện xử lý đặc biệt")
                leak_result = self._handle_memory_leak()

                # Cập nhật kết quả từ xử lý rò rỉ
                result["leak_detected"] = True
                result["leak_handled"] = leak_result["success"]
                result["actions"].extend(leak_result["actions"])
                result["memory_after"] = leak_result["memory_after"]
                result["memory_saved"] = leak_result["memory_saved"]

                # Nếu xử lý rò rỉ thành công và tiết kiệm được nhiều bộ nhớ, trả về kết quả
                if leak_result["success"] and leak_result["memory_saved"] > 10:
                    logger.info(f"Xử lý rò rỉ bộ nhớ thành công, tiết kiệm {leak_result['memory_saved']:.1f}% bộ nhớ")
                    return result

                # Nếu không, tiếp tục với tối ưu hóa thông thường
                logger.info("Tiếp tục với tối ưu hóa bộ nhớ thông thường sau khi xử lý rò rỉ")

            # 1. Gọi garbage collector
            import gc
            gc.collect()
            result["actions"].append("gc_collect")

            # 2. Xác định mức độ tối ưu hóa dựa trên mức sử dụng bộ nhớ
            aggressive = current_memory > 90 or force or leak_detected
            light = current_memory < 70 and not force and not leak_detected

            # 3. Tối ưu hóa các đối tượng lớn
            self._optimize_large_objects(result, aggressive=aggressive, light=light)

            # 4. Nếu bộ nhớ ở mức nguy hiểm, thực hiện tối ưu hóa mạnh
            if current_memory >= self.critical_memory_threshold or force or leak_detected:
                self._perform_critical_optimization(result)

            # 5. Xóa cache nếu có
            if hasattr(self, "_cache"):
                self._cache.clear()
                result["actions"].append("clear_cache")

            # 6. Xóa các cache khác nếu có
            for attr_name in dir(self):
                if attr_name.endswith("_cache") and hasattr(self, attr_name) and attr_name != "_cache":
                    cache_obj = getattr(self, attr_name)
                    if hasattr(cache_obj, "clear"):
                        cache_obj.clear()
                        result["actions"].append(f"clear_{attr_name}")
                    elif isinstance(cache_obj, dict):
                        cache_obj.clear()
                        result["actions"].append(f"clear_dict_{attr_name}")

            # Đo bộ nhớ sau khi tối ưu hóa
            memory_after = self._get_current_memory_usage()
            result["memory_after"] = memory_after
            result["memory_saved"] = max(0, memory_before - memory_after)

            # Cập nhật thống kê bộ nhớ
            if hasattr(self, "memory_stats"):
                # Thêm vào lịch sử bộ nhớ
                self.memory_stats["memory_history"].append({
                    "timestamp": time.time(),
                    "before": memory_before,
                    "after": memory_after,
                    "saved": result["memory_saved"],
                    "aggressive": aggressive,
                    "light": light,
                    "percent": memory_after  # Thêm phần trăm bộ nhớ hiện tại để phân tích xu hướng
                })

                # Giữ lịch sử trong giới hạn kích thước
                if len(self.memory_stats["memory_history"]) > self.memory_history_size:
                    self.memory_stats["memory_history"] = self.memory_stats["memory_history"][-self.memory_history_size:]

                # Cập nhật số lần tối ưu hóa
                self.memory_stats["optimizations"] = self.memory_stats.get("optimizations", 0) + 1

                # Cập nhật tổng bộ nhớ đã tiết kiệm
                self.memory_stats["total_memory_saved"] = self.memory_stats.get("total_memory_saved", 0) + result["memory_saved"]

            # Ghi log thông tin về bộ nhớ
            logger.info(f"Tối ưu hóa bộ nhớ hoàn tất: {memory_before:.1f}% -> {memory_after:.1f}% (tiết kiệm: {result['memory_saved']:.1f}%)")

            # Nếu tối ưu hóa không hiệu quả và bộ nhớ vẫn cao, thực hiện tối ưu hóa mạnh hơn
            if memory_after > self.critical_memory_threshold and result["memory_saved"] < 5:
                logger.warning(f"Tối ưu hóa không hiệu quả, bộ nhớ vẫn cao ({memory_after:.1f}%), thực hiện tối ưu hóa mạnh hơn")
                self._perform_critical_optimization(result)

                # Đo lại bộ nhớ
                memory_after = self._get_current_memory_usage()
                result["memory_after"] = memory_after
                result["memory_saved"] = max(0, memory_before - memory_after)
                result["actions"].append("additional_critical_optimization")

                logger.info(f"Tối ưu hóa bổ sung hoàn tất: {memory_before:.1f}% -> {memory_after:.1f}% (tiết kiệm: {result['memory_saved']:.1f}%)")

                # Nếu vẫn không hiệu quả, giảm ngưỡng bộ nhớ để tối ưu hóa thường xuyên hơn
                if memory_after > 85 and result["memory_saved"] < 10:
                    old_threshold = self.memory_threshold
                    self.memory_threshold = max(60, self.memory_threshold - 10)
                    logger.warning(f"Giảm ngưỡng bộ nhớ từ {old_threshold:.1f}% xuống {self.memory_threshold:.1f}%")
                    result["actions"].append(f"reduce_memory_threshold_{old_threshold}_to_{self.memory_threshold}")

                    # Giảm khoảng thời gian kiểm tra bộ nhớ
                    if hasattr(self, "memory_check_interval"):
                        old_interval = self.memory_check_interval
                        self.memory_check_interval = max(30, self.memory_check_interval // 2)
                        logger.warning(f"Giảm khoảng thời gian kiểm tra bộ nhớ từ {old_interval}s xuống {self.memory_check_interval}s")
                        result["actions"].append(f"reduce_check_interval_{old_interval}_to_{self.memory_check_interval}")

            # Phân tích xu hướng sử dụng bộ nhớ
            if hasattr(self, "memory_stats") and len(self.memory_stats.get("memory_history", [])) >= 5:
                self._analyze_memory_trend()

            # Phân tích các mẫu sử dụng bộ nhớ
            if hasattr(self, "memory_stats") and len(self.memory_stats.get("memory_history", [])) >= 10:
                memory_patterns = self._analyze_memory_usage_patterns()

                # Lưu kết quả phân tích
                self.memory_stats["memory_patterns_analysis"] = memory_patterns

                # Xử lý các mẫu phát hiện được
                if memory_patterns["patterns_detected"]:
                    logger.info(f"Phát hiện {len(memory_patterns['patterns_detected'])} mẫu sử dụng bộ nhớ, mức độ rủi ro: {memory_patterns['risk_level']}")

                    # Thực hiện các khuyến nghị
                    if memory_patterns["risk_level"] == "high":
                        logger.warning(f"Phát hiện mẫu sử dụng bộ nhớ nguy hiểm: {[p['type'] for p in memory_patterns['patterns_detected']]}")
                        logger.warning(f"Khuyến nghị: {memory_patterns['recommendations']}")

                        # Điều chỉnh ngưỡng bộ nhớ nếu có mẫu nguy hiểm
                        if hasattr(self, "memory_threshold"):
                            old_threshold = self.memory_threshold
                            self.memory_threshold = max(60, self.memory_threshold - 10)
                            logger.warning(f"Giảm ngưỡng bộ nhớ từ {old_threshold:.1f}% xuống {self.memory_threshold:.1f}% do phát hiện mẫu nguy hiểm")
                            result["actions"].append(f"reduce_memory_threshold_{old_threshold}_to_{self.memory_threshold}")

                        # Giảm khoảng thời gian kiểm tra bộ nhớ
                        if hasattr(self, "memory_check_interval"):
                            old_interval = self.memory_check_interval
                            self.memory_check_interval = max(15, self.memory_check_interval // 2)
                            logger.warning(f"Giảm khoảng thời gian kiểm tra bộ nhớ từ {old_interval}s xuống {self.memory_check_interval}s")
                            result["actions"].append(f"reduce_check_interval_{old_interval}_to_{self.memory_check_interval}")

                    elif memory_patterns["risk_level"] == "medium":
                        logger.info(f"Phát hiện mẫu sử dụng bộ nhớ cần chú ý: {[p['type'] for p in memory_patterns['patterns_detected']]}")
                        logger.info(f"Khuyến nghị: {memory_patterns['recommendations']}")

                        # Điều chỉnh ngưỡng bộ nhớ nếu có mẫu cần chú ý
                        if hasattr(self, "memory_threshold"):
                            old_threshold = self.memory_threshold
                            self.memory_threshold = max(70, self.memory_threshold - 5)
                            logger.info(f"Điều chỉnh ngưỡng bộ nhớ từ {old_threshold:.1f}% xuống {self.memory_threshold:.1f}%")
                            result["actions"].append(f"adjust_memory_threshold_{old_threshold}_to_{self.memory_threshold}")

            # Phân tích hiệu quả tối ưu hóa
            if hasattr(self, "memory_stats") and "optimization_efficiency_history" in self.memory_stats and len(self.memory_stats["optimization_efficiency_history"]) >= 3:
                efficiency_analysis = self._analyze_optimization_efficiency()

                # Lưu kết quả phân tích
                self.memory_stats["optimization_efficiency_analysis"] = efficiency_analysis

                # Xử lý các khuyến nghị
                if efficiency_analysis["recommendations"]:
                    logger.info(f"Phân tích hiệu quả tối ưu hóa: {efficiency_analysis['average_efficiency']:.2f}%, xu hướng: {efficiency_analysis['efficiency_trend']}")
                    logger.info(f"Khuyến nghị: {efficiency_analysis['recommendations']}")

                    # Điều chỉnh chiến lược tối ưu hóa nếu hiệu quả thấp
                    if efficiency_analysis["average_efficiency"] < 5:
                        logger.warning(f"Hiệu quả tối ưu hóa thấp: {efficiency_analysis['average_efficiency']:.2f}%")

                        # Tìm loại tối ưu hóa hiệu quả nhất
                        if "optimization_types" in efficiency_analysis:
                            best_type = None
                            best_efficiency = 0

                            for opt_type, data in efficiency_analysis["optimization_types"].items():
                                if "average_efficiency" in data and data["average_efficiency"] > best_efficiency:
                                    best_efficiency = data["average_efficiency"]
                                    best_type = opt_type

                            if best_type and best_efficiency > 0:
                                logger.info(f"Loại tối ưu hóa hiệu quả nhất: {best_type} ({best_efficiency:.2f}%)")
                                result["actions"].append(f"recommend_optimization_type_{best_type}")

            # Phân tích các mẫu sử dụng bộ nhớ
            if hasattr(self, "memory_stats") and len(self.memory_stats.get("memory_history", [])) >= 10:
                memory_patterns = self._analyze_memory_usage_patterns()

                # Lưu kết quả phân tích
                self.memory_stats["memory_patterns_analysis"] = memory_patterns

                # Xử lý các mẫu phát hiện được
                if memory_patterns["patterns_detected"]:
                    logger.info(f"Phát hiện {len(memory_patterns['patterns_detected'])} mẫu sử dụng bộ nhớ, mức độ rủi ro: {memory_patterns['risk_level']}")

                    # Thực hiện các khuyến nghị
                    if memory_patterns["risk_level"] == "high":
                        logger.warning(f"Phát hiện mẫu sử dụng bộ nhớ nguy hiểm: {[p['type'] for p in memory_patterns['patterns_detected']]}")
                        logger.warning(f"Khuyến nghị: {memory_patterns['recommendations']}")

                        # Điều chỉnh ngưỡng bộ nhớ nếu có mẫu nguy hiểm
                        if hasattr(self, "memory_threshold"):
                            old_threshold = self.memory_threshold
                            self.memory_threshold = max(60, self.memory_threshold - 10)
                            logger.warning(f"Giảm ngưỡng bộ nhớ từ {old_threshold:.1f}% xuống {self.memory_threshold:.1f}% do phát hiện mẫu nguy hiểm")
                            result["actions"].append(f"reduce_memory_threshold_{old_threshold}_to_{self.memory_threshold}")

                        # Giảm khoảng thời gian kiểm tra bộ nhớ
                        if hasattr(self, "memory_check_interval"):
                            old_interval = self.memory_check_interval
                            self.memory_check_interval = max(15, self.memory_check_interval // 2)
                            logger.warning(f"Giảm khoảng thời gian kiểm tra bộ nhớ từ {old_interval}s xuống {self.memory_check_interval}s")
                            result["actions"].append(f"reduce_check_interval_{old_interval}_to_{self.memory_check_interval}")

                    elif memory_patterns["risk_level"] == "medium":
                        logger.info(f"Phát hiện mẫu sử dụng bộ nhớ cần chú ý: {[p['type'] for p in memory_patterns['patterns_detected']]}")
                        logger.info(f"Khuyến nghị: {memory_patterns['recommendations']}")

                        # Điều chỉnh ngưỡng bộ nhớ nếu có mẫu cần chú ý
                        if hasattr(self, "memory_threshold"):
                            old_threshold = self.memory_threshold
                            self.memory_threshold = max(70, self.memory_threshold - 5)
                            logger.info(f"Điều chỉnh ngưỡng bộ nhớ từ {old_threshold:.1f}% xuống {self.memory_threshold:.1f}%")
                            result["actions"].append(f"adjust_memory_threshold_{old_threshold}_to_{self.memory_threshold}")

            # Phân tích hiệu quả tối ưu hóa
            if hasattr(self, "memory_stats") and "optimization_efficiency_history" in self.memory_stats and len(self.memory_stats["optimization_efficiency_history"]) >= 3:
                efficiency_analysis = self._analyze_optimization_efficiency()

                # Lưu kết quả phân tích
                self.memory_stats["optimization_efficiency_analysis"] = efficiency_analysis

                # Xử lý các khuyến nghị
                if efficiency_analysis["recommendations"]:
                    logger.info(f"Phân tích hiệu quả tối ưu hóa: {efficiency_analysis['average_efficiency']:.2f}%, xu hướng: {efficiency_analysis['efficiency_trend']}")
                    logger.info(f"Khuyến nghị: {efficiency_analysis['recommendations']}")

                    # Điều chỉnh chiến lược tối ưu hóa nếu hiệu quả thấp
                    if efficiency_analysis["average_efficiency"] < 5:
                        logger.warning(f"Hiệu quả tối ưu hóa thấp: {efficiency_analysis['average_efficiency']:.2f}%")

                        # Tìm loại tối ưu hóa hiệu quả nhất
                        if "optimization_types" in efficiency_analysis:
                            best_type = None
                            best_efficiency = 0

                            for opt_type, data in efficiency_analysis["optimization_types"].items():
                                if "average_efficiency" in data and data["average_efficiency"] > best_efficiency:
                                    best_efficiency = data["average_efficiency"]
                                    best_type = opt_type

                            if best_type and best_efficiency > 0:
                                logger.info(f"Loại tối ưu hóa hiệu quả nhất: {best_type} ({best_efficiency:.2f}%)")
                                result["actions"].append(f"recommend_optimization_type_{best_type}")

        except Exception as e:
            logger.error(f"Lỗi khi tối ưu hóa bộ nhớ: {str(e)}")
            result["success"] = False
            result["error"] = str(e)

        return result

    def _optimize_large_objects(self, result: Dict[str, Any] = None, aggressive: bool = False, light: bool = False) -> Dict[str, Any]:
        """
        Tối ưu hóa các đối tượng lớn trong bộ nhớ.

        Args:
            result: Kết quả tối ưu hóa để cập nhật
            aggressive: Có thực hiện tối ưu hóa mạnh hay không
            light: Có thực hiện tối ưu hóa nhẹ hay không

        Returns:
            Dict[str, Any]: Kết quả tối ưu hóa
        """
        # Khởi tạo kết quả nếu chưa có
        if result is None:
            result = {
                "success": True,
                "optimized": True,
                "actions": [],
                "aggressive": aggressive,
                "light": light
            }

        # Tối ưu hóa kết quả crawl nếu có
        if hasattr(self, "_results") and isinstance(self._results, list):
            # Giới hạn kích thước của các trường lớn trong kết quả
            for item in self._results:
                if isinstance(item, dict):
                    # Tối ưu hóa các trường lớn
                    content_limit = 50000 if aggressive else (150000 if not light else 100000)
                    html_limit = 10000 if aggressive else (50000 if not light else 100000)

                    # Xử lý trường content
                    if "content" in item and isinstance(item["content"], str):
                        if len(item["content"]) > content_limit:
                            # Lưu kích thước ban đầu
                            original_size = len(item["content"])
                            # Cắt bớt nội dung
                            item["content"] = item["content"][:content_limit] + f"... [TRUNCATED - Original size: {original_size} bytes]"
                            result["actions"].append(f"truncate_large_content_{content_limit}")

                    # Xử lý trường HTML
                    for field in ["html", "raw_html"]:
                        if field in item and isinstance(item[field], str):
                            if aggressive and len(item[field]) > 0:
                                # Nếu tối ưu hóa mạnh, xóa hoàn toàn HTML
                                item[field] = f"[REMOVED DUE TO MEMORY CONSTRAINTS - FIELD: {field}]"
                                result["actions"].append(f"remove_{field}")
                            elif len(item[field]) > html_limit:
                                # Lưu kích thước ban đầu
                                original_size = len(item[field])
                                # Cắt bớt HTML
                                item[field] = item[field][:html_limit] + f"... [TRUNCATED - Original size: {original_size} bytes]"
                                result["actions"].append(f"truncate_large_{field}_{html_limit}")

                    # Xử lý trường text
                    if "text" in item and isinstance(item["text"], str) and len(item["text"]) > content_limit:
                        # Lưu kích thước ban đầu
                        original_size = len(item["text"])
                        # Cắt bớt nội dung
                        item["text"] = item["text"][:content_limit] + f"... [TRUNCATED - Original size: {original_size} bytes]"
                        result["actions"].append(f"truncate_large_text_{content_limit}")

                    # Xử lý các trường metadata nếu tối ưu hóa mạnh
                    if aggressive and "metadata" in item and isinstance(item["metadata"], dict):
                        # Giữ lại chỉ các trường metadata quan trọng
                        important_fields = ["title", "description", "keywords", "published_time", "author"]
                        item["metadata"] = {k: v for k, v in item["metadata"].items() if k in important_fields}
                        result["actions"].append("optimize_metadata")

                    # Xử lý các trường links nếu tối ưu hóa mạnh
                    if "links" in item and isinstance(item["links"], list):
                        max_links = 5 if aggressive else (10 if not light else 20)
                        if len(item["links"]) > max_links:
                            item["links"] = item["links"][:max_links]
                            result["actions"].append(f"truncate_links_{max_links}")

            result["actions"].append("optimize_results")

        # Tối ưu hóa cache nếu có
        if hasattr(self, "_cache") and hasattr(self._cache, "items"):
            cache_size = len(self._cache)
            max_cache_size = 50 if aggressive else (75 if not light else 100)

            if cache_size > max_cache_size:  # Nếu cache quá lớn
                # Giữ lại các mục được truy cập gần đây nhất
                if hasattr(self._cache, "get_most_recent"):
                    self._cache = self._cache.get_most_recent(max_cache_size)
                else:
                    # Nếu không có phương thức get_most_recent, giữ lại các mục ngẫu nhiên
                    import random
                    keys = list(self._cache.keys())
                    keys_to_keep = random.sample(keys, min(max_cache_size, len(keys)))
                    new_cache = {k: self._cache[k] for k in keys_to_keep}
                    self._cache = new_cache

                result["actions"].append(f"optimize_cache_{max_cache_size}")

            # Nếu tối ưu hóa mạnh, xóa hoàn toàn cache
            if aggressive and hasattr(self, "_cache"):
                self._cache.clear()
                result["actions"].append("clear_cache")

        return result

    def _perform_critical_optimization(self, result: Dict[str, Any]) -> None:
        """
        Thực hiện tối ưu hóa mạnh khi bộ nhớ ở mức nguy hiểm.

        Args:
            result: Kết quả tối ưu hóa để cập nhật
        """
        import gc
        import sys

        # Xóa cache nếu có
        if hasattr(self, "_cache"):
            self._cache.clear()
            result["actions"].append("clear_cache")

        # Giải phóng bộ nhớ cho các đối tượng lớn
        if hasattr(self, "_results"):
            # Giữ lại chỉ 5 kết quả gần nhất (giảm từ 10 xuống 5)
            if len(self._results) > 5:
                self._results = self._results[-5:]
                result["actions"].append("truncate_results_to_5")

            # Xóa hoàn toàn các trường lớn không cần thiết
            for item in self._results:
                if isinstance(item, dict):
                    # Xóa hoàn toàn HTML và các trường lớn khác
                    for field in ["html", "raw_html", "full_content", "original_html", "full_html", "parsed_html"]:
                        if field in item:
                            item.pop(field, None)
                            result["actions"].append(f"remove_field_{field}")

                    # Giữ lại chỉ 300 ký tự đầu tiên của nội dung (giảm từ 500 xuống 300)
                    if "content" in item and isinstance(item["content"], str):
                        if len(item["content"]) > 300:
                            item["content"] = item["content"][:300] + "... [TRUNCATED DUE TO CRITICAL MEMORY CONSTRAINTS]"
                            result["actions"].append("aggressive_content_truncation_300")

                    # Xóa hoặc giảm kích thước các trường metadata
                    if "metadata" in item and isinstance(item["metadata"], dict):
                        # Chỉ giữ lại title và url
                        essential_fields = ["title", "url"]
                        item["metadata"] = {k: v for k, v in item["metadata"].items() if k in essential_fields}
                        result["actions"].append("minimize_metadata_extreme")

                    # Xóa hoàn toàn các trường không cần thiết
                    for field in ["links", "images", "videos", "audios", "files", "embedded", "scripts", "styles"]:
                        if field in item:
                            item.pop(field, None)
                            result["actions"].append(f"remove_{field}")

            result["actions"].append("extreme_content_truncation")

        # Xóa tất cả các cache
        cache_attrs = []
        for attr_name in dir(self):
            if "_cache" in attr_name or attr_name.endswith("_cache"):
                cache_attrs.append(attr_name)

        for attr_name in cache_attrs:
            if hasattr(self, attr_name):
                try:
                    cache_obj = getattr(self, attr_name)
                    if isinstance(cache_obj, dict):
                        setattr(self, attr_name, {})
                    elif isinstance(cache_obj, list):
                        setattr(self, attr_name, [])
                    elif hasattr(cache_obj, "clear"):
                        cache_obj.clear()
                    result["actions"].append(f"clear_{attr_name}")
                except Exception as e:
                    logger.warning(f"Lỗi khi xóa cache {attr_name}: {str(e)}")

        # Xóa các thuộc tính lớn không cần thiết
        large_attrs = [
            "_html_cache", "_content_cache", "_raw_data", "_last_batch_results",
            "_temp_results", "_temp_data", "_temp_content", "_temp_html",
            "_image_cache", "_video_cache", "_audio_cache", "_file_cache",
            "_last_response", "_response_cache", "_request_cache", "_extracted_data",
            "_parsed_data", "_raw_html", "_processed_html", "_processed_content",
            "_last_crawl_results", "_last_crawl_stats", "_last_crawl_time"
        ]
        for attr in large_attrs:
            if hasattr(self, attr):
                try:
                    delattr(self, attr)
                    result["actions"].append(f"delete_{attr}")
                except Exception as e:
                    logger.warning(f"Lỗi khi xóa thuộc tính {attr}: {str(e)}")

        # Xóa các thuộc tính lớn trong các đối tượng con
        if hasattr(self, "memory_crawler"):
            # Thử gọi phương thức dọn dẹp mạnh nếu có
            if hasattr(self.memory_crawler, "clear_all_caches"):
                self.memory_crawler.clear_all_caches()
                result["actions"].append("memory_crawler_clear_all_caches")
            elif hasattr(self.memory_crawler, "emergency_cleanup"):
                self.memory_crawler.emergency_cleanup()
                result["actions"].append("memory_crawler_emergency_cleanup")
            elif hasattr(self.memory_crawler, "clear_memory"):
                self.memory_crawler.clear_memory()
                result["actions"].append("memory_crawler_clear_memory")

        # Thử giải phóng bộ nhớ Python
        try:
            # Xóa các đối tượng không sử dụng trong sys.modules
            for module_name in list(sys.modules.keys()):
                if module_name.startswith('_') and not module_name.startswith('__'):
                    if module_name in sys.modules:
                        try:
                            del sys.modules[module_name]
                            result["actions"].append(f"unload_module_{module_name}")
                        except Exception:
                            pass
        except Exception as e:
            logger.warning(f"Lỗi khi giải phóng module: {str(e)}")

        # Gọi lại garbage collector nhiều lần với các tham số khác nhau
        gc.collect(0)  # Thu gom thế hệ trẻ
        gc.collect(1)  # Thu gom thế hệ trung gian
        gc.collect(2)  # Thu gom tất cả các thế hệ

        # Thử giải phóng bộ nhớ không sử dụng cho hệ điều hành
        try:
            # Thử sử dụng malloc_trim nếu có sẵn (Linux)
            import ctypes
            try:
                libc = ctypes.CDLL("libc.so.6")
                if hasattr(libc, "malloc_trim"):
                    libc.malloc_trim(0)
                    result["actions"].append("malloc_trim")
            except Exception:
                pass
        except ImportError:
            pass

        # Gọi lại garbage collector một lần nữa
        gc.collect(2)
        result["actions"].append("multiple_gc_collect")

        # Ghi log thông báo
        logger.warning("Đã thực hiện tối ưu hóa bộ nhớ ở mức nguy hiểm")

    def _check_memory(self, force: bool = False) -> Dict[str, Any]:
        """
        Kiểm tra và tối ưu hóa bộ nhớ nếu cần.

        Args:
            force: Có bắt buộc tối ưu hóa không, bất kể ngưỡng bộ nhớ

        Returns:
            Dict[str, Any]: Kết quả tối ưu hóa bộ nhớ
        """
        if not self.use_memory_optimization:
            return {"success": False, "reason": "Memory optimization is disabled"}

        current_time = time.time()
        last_check_time = getattr(self, "_last_memory_check_time", 0)

        # Lấy mức sử dụng bộ nhớ hiện tại
        current_memory = self._get_current_memory_usage()

        # Cập nhật peak memory nếu cần
        if hasattr(self, "memory_stats"):
            if current_memory > self.memory_stats.get("peak_memory", 0):
                self.memory_stats["peak_memory"] = current_memory

        # Điều chỉnh ngưỡng bộ nhớ dựa trên mức sử dụng hiện tại
        if hasattr(self, "memory_threshold"):
            # Nếu bộ nhớ tăng nhanh, giảm ngưỡng để tối ưu hóa sớm hơn
            if hasattr(self, "_last_memory_usage") and current_memory > self._last_memory_usage + 10:
                self.memory_threshold = max(70, self.memory_threshold - 5)
                logger.info(f"Giảm ngưỡng bộ nhớ xuống {self.memory_threshold:.1f}% do bộ nhớ tăng nhanh")
            # Nếu bộ nhớ ổn định ở mức thấp, tăng ngưỡng để giảm tần suất tối ưu hóa
            elif hasattr(self, "_last_memory_usage") and current_memory < 50 and self._last_memory_usage < 50:
                self.memory_threshold = min(85, self.memory_threshold + 2)
                logger.info(f"Tăng ngưỡng bộ nhớ lên {self.memory_threshold:.1f}% do bộ nhớ ổn định")

        # Lưu mức sử dụng bộ nhớ hiện tại để so sánh lần sau
        self._last_memory_usage = current_memory

        # Kiểm tra xem đã đến lúc kiểm tra bộ nhớ chưa
        time_since_last_cleanup = current_time - last_check_time
        need_optimization = (
            force or
            current_memory >= self.memory_threshold or
            (time_since_last_cleanup >= self.memory_check_interval and current_memory >= self.memory_threshold * 0.8)
        )

        if not need_optimization:
            return {"success": True, "optimized": False, "current_memory": current_memory}

        # Thực hiện tối ưu hóa
        logger.info(f"Bắt đầu tối ưu hóa bộ nhớ (sử dụng: {current_memory:.1f}%, ngưỡng: {self.memory_threshold:.1f}%)")

        # Ghi lại bộ nhớ trước khi tối ưu hóa
        memory_before = current_memory

        try:
            # Tối ưu hóa mức độ tùy thuộc vào mức sử dụng bộ nhớ
            if current_memory > 90:  # Mức rất cao
                # Tối ưu hóa mạnh
                logger.info("Thực hiện tối ưu hóa bộ nhớ mức cao")
                result = self._optimize_large_objects(aggressive=True)
                # Thực hiện tối ưu hóa mạnh
                self._perform_critical_optimization(result)
            elif current_memory > 80:  # Mức cao
                # Tối ưu hóa thông thường
                logger.info("Thực hiện tối ưu hóa bộ nhớ mức trung bình")
                result = self._optimize_large_objects(aggressive=False)
            else:  # Mức thấp
                # Tối ưu hóa nhẹ
                logger.info("Thực hiện tối ưu hóa bộ nhớ mức nhẹ")
                result = self._optimize_large_objects(aggressive=False, light=True)

            # Kiểm tra lại bộ nhớ sau khi tối ưu hóa
            memory_after = self._get_current_memory_usage()
            result["memory_before"] = memory_before
            result["memory_after"] = memory_after
            result["memory_saved"] = max(0, memory_before - memory_after)

            # Cập nhật thống kê
            if hasattr(self, "memory_stats"):
                self.memory_stats["optimizations"] = self.memory_stats.get("optimizations", 0) + 1
                self.memory_stats["memory_saved"] = self.memory_stats.get("memory_saved", 0) + result["memory_saved"]
                self.memory_stats["last_cleanup_time"] = current_time

                # Lưu lịch sử hiệu quả tối ưu hóa
                if "optimization_efficiency_history" not in self.memory_stats:
                    self.memory_stats["optimization_efficiency_history"] = []

                # Tính hiệu quả tối ưu hóa (% bộ nhớ tiết kiệm được)
                efficiency = result["memory_saved"]

                # Xác định loại tối ưu hóa
                optimization_type = "standard"
                if "leak_detected" in result and result["leak_detected"]:
                    optimization_type = "leak_handling"
                elif "actions" in result and result["actions"]:
                    if any("aggressive" in action for action in result["actions"]):
                        optimization_type = "aggressive"
                    elif any("light" in action for action in result["actions"]):
                        optimization_type = "light"

                # Lưu thông tin hiệu quả tối ưu hóa
                self.memory_stats["optimization_efficiency_history"].append({
                    "timestamp": current_time,
                    "efficiency": efficiency,
                    "memory_before": memory_before,
                    "memory_after": memory_after,
                    "optimization_type": optimization_type,
                    "actions": result["actions"] if "actions" in result else []
                })

                # Giới hạn kích thước lịch sử
                max_history = 20
                if len(self.memory_stats["optimization_efficiency_history"]) > max_history:
                    self.memory_stats["optimization_efficiency_history"] = self.memory_stats["optimization_efficiency_history"][-max_history:]

            logger.info(f"Tối ưu hóa bộ nhớ hoàn tất: {memory_before:.1f}% -> {memory_after:.1f}% (tiết kiệm: {result['memory_saved']:.1f}%)")

            # Nếu vẫn còn cao, thực hiện tối ưu hóa mạnh hơn
            if memory_after > 85 and not result.get("aggressive", False):
                logger.warning(f"Bộ nhớ vẫn còn cao ({memory_after:.1f}%), thực hiện tối ưu hóa mạnh hơn")
                # Thực hiện tối ưu hóa mạnh
                self._perform_critical_optimization(result)
                # Kiểm tra lại bộ nhớ
                memory_final = self._get_current_memory_usage()
                result["memory_after"] = memory_final
                result["memory_saved"] = max(0, memory_before - memory_final)
                result["additional_optimization"] = True
                logger.info(f"Tối ưu hóa bộ nhớ bổ sung: {memory_after:.1f}% -> {memory_final:.1f}% (tổng tiết kiệm: {result['memory_saved']:.1f}%)")

                # Điều chỉnh ngưỡng bộ nhớ nếu tối ưu hóa không hiệu quả
                if memory_final > 80 and result["memory_saved"] < 10:
                    # Giảm ngưỡng bộ nhớ để tối ưu hóa sớm hơn
                    self.memory_threshold = max(60, self.memory_threshold - 10)
                    logger.warning(f"Giảm ngưỡng bộ nhớ xuống {self.memory_threshold:.1f}% do tối ưu hóa không hiệu quả")
                    # Giảm khoảng thời gian kiểm tra bộ nhớ
                    self.memory_check_interval = max(30, self.memory_check_interval // 2)
                    logger.warning(f"Giảm khoảng thời gian kiểm tra bộ nhớ xuống {self.memory_check_interval}s")
                    result["threshold_adjusted"] = True

            # Cập nhật thời gian kiểm tra cuối cùng
            self._last_memory_check_time = current_time

            return result

        except Exception as e:
            logger.error(f"Lỗi khi tối ưu hóa bộ nhớ: {str(e)}")
            return {"success": False, "error": str(e), "current_memory": current_memory}



    def _load_crawl_config(self):
        """Tải cấu hình crawl."""
        # Cấu hình crawl mặc định
        self.crawl_config = {
            "default": {
                "max_depth": 1,
                "max_pages": 5,
                "timeout": 30,
                "detailed_scraping": False,
                "follow_links": True,
                "same_domain_only": True,
                "respect_robots_txt": True,
                "max_retries": 3,
                "retry_delay": 2.0,
                "blacklisted_domains": [
                    "facebook.com", "twitter.com", "instagram.com", "youtube.com",
                    "tiktok.com", "pinterest.com", "linkedin.com", "reddit.com"
                ],
                "blacklisted_extensions": [
                    ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
                    ".zip", ".rar", ".tar", ".gz", ".mp3", ".mp4", ".avi", ".mov"
                ]
            },
            "simple": {
                "max_depth": 0,
                "max_pages": 3,
                "timeout": 20,
                "detailed_scraping": False,
                "follow_links": False
            },
            "medium": {
                "max_depth": 1,
                "max_pages": 5,
                "timeout": 30,
                "detailed_scraping": True,
                "follow_links": True
            },
            "complex": {
                "max_depth": 2,
                "max_pages": 10,
                "timeout": 60,
                "detailed_scraping": True,
                "follow_links": True
            }
        }

        # Cập nhật từ config nếu có
        if "crawl_config" in self.config:
            for level, config in self.config["crawl_config"].items():
                if level in self.crawl_config:
                    self.crawl_config[level].update(config)
                else:
                    self.crawl_config[level] = config

    def _filter_urls(self, urls: List[str]) -> List[str]:
        """
        Lọc danh sách URL trước khi crawl.

        Args:
            urls: Danh sách URL cần lọc

        Returns:
            List[str]: Danh sách URL đã lọc
        """
        if not urls:
            return []

        # Lấy cấu hình mặc định
        default_config = self.crawl_config.get("default", {})
        blacklisted_domains = default_config.get("blacklisted_domains", [])
        blacklisted_extensions = default_config.get("blacklisted_extensions", [])

        filtered_urls = []

        for url in urls:
            # Kiểm tra URL hợp lệ
            if not url or not isinstance(url, str) or not url.startswith(("http://", "https://")):
                logger.warning(f"Bỏ qua URL không hợp lệ: {url}")
                continue

            # Phân tích URL
            try:
                parsed_url = urlparse(url)
                domain = parsed_url.netloc
                path = parsed_url.path.lower()

                # Kiểm tra domain bị chặn
                if any(blocked_domain in domain for blocked_domain in blacklisted_domains):
                    logger.warning(f"Bỏ qua URL từ domain bị chặn: {url}")
                    continue

                # Kiểm tra phần mở rộng bị chặn
                if any(path.endswith(ext) for ext in blacklisted_extensions):
                    logger.warning(f"Bỏ qua URL có phần mở rộng bị chặn: {url}")
                    continue

                # Thêm vào danh sách đã lọc
                filtered_urls.append(url)

            except Exception as e:
                logger.warning(f"Lỗi khi phân tích URL {url}: {str(e)}")
                continue

        # Loại bỏ các URL trùng lặp
        filtered_urls = list(dict.fromkeys(filtered_urls))

        logger.info(f"Đã lọc {len(urls)} URL thành {len(filtered_urls)} URL hợp lệ")
        return filtered_urls

    def crawl(
        self,
        urls: List[str],
        complexity_level: str = "medium",
        max_depth: Optional[int] = None,
        max_pages: Optional[int] = None,
        timeout: Optional[int] = None,
        detailed_scraping: Optional[bool] = None,
        crawl_mode: Optional[str] = None,
        extract_images: Optional[bool] = None,
        extract_files: Optional[bool] = None,
        extract_html: Optional[bool] = None,
        extract_file_content: Optional[bool] = None,
        handle_pagination: Optional[bool] = None,
        max_pagination_pages: Optional[int] = None,
        pagination_selectors: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Crawl danh sách URL với chiến lược thích ứng.

        Args:
            urls: Danh sách URL cần crawl
            complexity_level: Mức độ phức tạp (simple, medium, complex)
            max_depth: Độ sâu tối đa (ghi đè cấu hình)
            max_pages: Số trang tối đa (ghi đè cấu hình)
            timeout: Thời gian chờ tối đa (ghi đè cấu hình)
            detailed_scraping: Có trích xuất thông tin chi tiết hay không (ghi đè cấu hình)
            crawl_mode: Chế độ crawl ("basic", "full_site", "content_only", "media_only")
            extract_images: Có trích xuất hình ảnh hay không
            extract_files: Có trích xuất file hay không
            extract_html: Có lưu HTML hay không
            extract_file_content: Có trích xuất nội dung từ file đã tải xuống hay không
            handle_pagination: Có xử lý phân trang hay không
            max_pagination_pages: Số trang phân trang tối đa
            pagination_selectors: Danh sách các CSS selector để xác định phân trang

        Returns:
            Dict[str, Any]: Kết quả crawl
        """
        if not urls:
            return {
                "success": False,
                "error": "Không có URL để crawl",
                "results": []
            }

        # Sử dụng crawl_mode từ tham số hoặc từ thuộc tính của đối tượng
        mode = crawl_mode or self.crawl_mode

        # Lấy cấu hình crawl dựa trên mức độ phức tạp
        config = self.crawl_config.get(complexity_level, self.crawl_config["default"])

        # Cấu hình dựa trên crawl_mode
        if mode == "full_site":
            # Chế độ crawl toàn bộ trang web
            config["max_depth"] = config.get("max_depth", 3) if max_depth is None else max_depth
            config["max_pages"] = config.get("max_pages", 100) if max_pages is None else max_pages
            config["detailed_scraping"] = True if detailed_scraping is None else detailed_scraping
            config["extract_images"] = True if extract_images is None else extract_images
            config["extract_files"] = True if extract_files is None else extract_files
            config["extract_html"] = True if extract_html is None else extract_html
            config["follow_links"] = True
            config["same_domain_only"] = True
            config["handle_pagination"] = True if handle_pagination is None else handle_pagination
            config["max_pagination_pages"] = max_pagination_pages or 10
        elif mode == "content_only":
            # Chế độ chỉ lấy nội dung
            config["max_depth"] = 0 if max_depth is None else max_depth
            config["max_pages"] = config.get("max_pages", 10) if max_pages is None else max_pages
            config["detailed_scraping"] = True if detailed_scraping is None else detailed_scraping
            config["extract_images"] = False if extract_images is None else extract_images
            config["extract_files"] = False if extract_files is None else extract_files
            config["extract_html"] = False if extract_html is None else extract_html
            config["follow_links"] = False
            config["same_domain_only"] = True
            config["handle_pagination"] = False if handle_pagination is None else handle_pagination
        elif mode == "media_only":
            # Chế độ chỉ lấy media
            config["max_depth"] = 1 if max_depth is None else max_depth
            config["max_pages"] = config.get("max_pages", 20) if max_pages is None else max_pages
            config["detailed_scraping"] = False if detailed_scraping is None else detailed_scraping
            config["extract_images"] = True if extract_images is None else extract_images
            config["extract_files"] = True if extract_files is None else extract_files
            config["extract_html"] = False if extract_html is None else extract_html
            config["follow_links"] = True
            config["same_domain_only"] = True
            config["handle_pagination"] = True if handle_pagination is None else handle_pagination
            config["max_pagination_pages"] = max_pagination_pages or 5
        else:  # "basic" hoặc mặc định
            # Ghi đè các tham số nếu được cung cấp
            if max_depth is not None:
                config["max_depth"] = max_depth
            if max_pages is not None:
                config["max_pages"] = max_pages
            if timeout is not None:
                config["timeout"] = timeout
            if detailed_scraping is not None:
                config["detailed_scraping"] = detailed_scraping
            if extract_images is not None:
                config["extract_images"] = extract_images
            if extract_files is not None:
                config["extract_files"] = extract_files
            if extract_html is not None:
                config["extract_html"] = extract_html
            if handle_pagination is not None:
                config["handle_pagination"] = handle_pagination
            if max_pagination_pages is not None:
                config["max_pagination_pages"] = max_pagination_pages

        # Thêm thông tin về chế độ crawl vào config
        config["crawl_mode"] = mode

        # Thêm thông tin về download_media và download_path
        config["download_media"] = self.download_media
        config["download_path"] = self.download_path
        config["max_media_size_mb"] = self.max_media_size_mb

        # Cập nhật extract_file_content nếu được cung cấp
        if extract_file_content is not None:
            self.extract_file_content = extract_file_content
        config["extract_file_content"] = self.extract_file_content

        # Cập nhật thông tin về pagination
        if pagination_selectors is not None:
            config["pagination_selectors"] = pagination_selectors
        else:
            config["pagination_selectors"] = self.pagination_selectors

        # Sử dụng MemoryOptimizedCrawler nếu có
        if self.use_memory_optimization:
            return self._crawl_with_memory_optimization(urls, config)
        else:
            return self._crawl_standard(urls, config)

    def _crawl_with_memory_optimization(
        self,
        urls: List[str],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Crawl với tối ưu hóa bộ nhớ.

        Args:
            urls: Danh sách URL cần crawl
            config: Cấu hình crawl

        Returns:
            Dict[str, Any]: Kết quả crawl
        """
        try:
            # Kiểm tra xem memory_crawler có phương thức crawl_urls không
            if hasattr(self.memory_crawler, 'crawl_urls'):
                # Sử dụng MemoryOptimizedCrawler từ advanced_crawlee.py
                return self.memory_crawler.crawl_urls(
                    urls=urls,
                    max_depth=config.get("max_depth", 2),
                    detailed_scraping=config.get("detailed_scraping", False),
                    proxy=config.get("proxy", None),
                    script_path=config.get("script_path", None)
                )
            elif hasattr(self.memory_crawler, 'crawl'):
                # Sử dụng MemoryOptimizedCrawler từ memory_optimized_crawler.py
                results = {
                    "success": True,
                    "results": [],
                    "count": 0
                }

                # Crawl từng URL
                for url in urls:
                    try:
                        result = asyncio.run(self.memory_crawler.crawl(
                            start_url=url,
                            extract_content=True,
                            follow_links=config.get("follow_links", True),
                            content_selectors=config.get("content_selectors", None),
                            link_selectors=config.get("link_selectors", None),
                            url_patterns=config.get("url_patterns", None),
                            exclude_patterns=config.get("exclude_patterns", None)
                        ))

                        if result.get("success", False):
                            results["results"].extend(result.get("pages", []))
                    except Exception as e:
                        logger.error(f"Lỗi khi crawl URL {url}: {str(e)}")

                # Cập nhật số lượng kết quả
                results["count"] = len(results["results"])

                return results
            else:
                # Không tìm thấy phương thức phù hợp
                raise AttributeError("MemoryOptimizedCrawler không có phương thức crawl_urls hoặc crawl")
        except Exception as e:
            logger.error(f"Lỗi khi crawl với tối ưu hóa bộ nhớ: {str(e)}")

            # Thử phương pháp tiêu chuẩn nếu thất bại
            return self._crawl_standard(urls, config)

    def _crawl_standard(
        self,
        urls: List[str],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Crawl với phương pháp tiêu chuẩn.

        Args:
            urls: Danh sách URL cần crawl
            config: Cấu hình crawl

        Returns:
            Dict[str, Any]: Kết quả crawl
        """
        # Kiểm tra và tối ưu hóa bộ nhớ trước khi bắt đầu crawl
        if self.use_memory_optimization:
            self._optimize_memory(force=True)

        # Kiểm tra Playwright nếu cần
        if self.use_playwright:
            result = self._crawl_with_playwright(urls, config)
        else:
            result = self._crawl_with_requests(urls, config)

        # Tối ưu hóa bộ nhớ sau khi crawl
        if self.use_memory_optimization:
            self._optimize_memory()

        return result

    def _check_memory(self, force: bool = False) -> Dict[str, Any]:
        """
        Kiểm tra và tối ưu hóa bộ nhớ nếu cần.

        Args:
            force: Có bắt buộc tối ưu hóa không, bất kể ngưỡng bộ nhớ

        Returns:
            Dict[str, Any]: Kết quả kiểm tra và tối ưu hóa bộ nhớ
        """
        if not self.use_memory_optimization:
            return {"success": False, "reason": "Memory optimization is disabled"}

        current_time = time.time()
        last_check_time = getattr(self, "_last_memory_check_time", 0)

        # Lấy mức sử dụng bộ nhớ hiện tại
        current_memory = self._get_current_memory_usage()

        # Cập nhật peak memory nếu cần
        if hasattr(self, "memory_stats"):
            if current_memory > self.memory_stats.get("peak_memory", 0):
                self.memory_stats["peak_memory"] = current_memory

        # Điều chỉnh ngưỡng bộ nhớ dựa trên mức sử dụng hiện tại
        if hasattr(self, "memory_threshold"):
            # Nếu bộ nhớ tăng nhanh, giảm ngưỡng để tối ưu hóa sớm hơn
            if hasattr(self, "_last_memory_usage") and current_memory > self._last_memory_usage + 10:
                self.memory_threshold = max(70, self.memory_threshold - 5)
                logger.info(f"Giảm ngưỡng bộ nhớ xuống {self.memory_threshold:.1f}% do bộ nhớ tăng nhanh")
            # Nếu bộ nhớ ổn định ở mức thấp, tăng ngưỡng để giảm tần suất tối ưu hóa
            elif hasattr(self, "_last_memory_usage") and current_memory < 50 and self._last_memory_usage < 50:
                self.memory_threshold = min(85, self.memory_threshold + 2)

        # Lưu mức sử dụng bộ nhớ hiện tại để so sánh lần sau
        self._last_memory_usage = current_memory

        # Kiểm tra xem đã đến lúc kiểm tra bộ nhớ chưa
        time_since_last_cleanup = current_time - last_check_time
        need_optimization = (
            force or
            current_memory >= self.memory_threshold or
            (time_since_last_cleanup >= self.memory_check_interval and current_memory >= self.memory_threshold * 0.8)
        )

        if not need_optimization:
            return {"success": True, "optimized": False, "current_memory": current_memory}

        # Thực hiện tối ưu hóa
        result = self._optimize_memory(force=force)
        self._last_memory_check_time = current_time

        return result

    def _crawl_with_playwright(
        self,
        urls: List[str],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Crawl với Playwright.

        Args:
            urls: Danh sách URL cần crawl
            config: Cấu hình crawl

        Returns:
            Dict[str, Any]: Kết quả crawl
        """
        # Tối ưu hóa bộ nhớ trước khi bắt đầu crawl
        self._check_memory(force=True)

        from playwright.sync_api import sync_playwright, TimeoutError as PlaywrightTimeoutError

        results = []
        failed_urls = []
        visited_urls = set()

        # Theo dõi sử dụng bộ nhớ
        memory_stats = {
            "initial": self._get_current_memory_usage(),
            "peak": 0,
            "checks": 0,
            "optimizations": 0
        }

        # Lọc URL trước khi crawl
        filtered_urls = self._filter_urls(urls)
        if not filtered_urls:
            return {
                "success": False,
                "error": "Không có URL hợp lệ sau khi lọc",
                "results": [],
                "failed_urls": urls
            }

        # Thiết lập batch size để tối ưu hóa bộ nhớ
        batch_size = config.get("batch_size", 3)
        if self.use_memory_optimization:
            # Điều chỉnh batch size dựa trên mức sử dụng bộ nhớ hiện tại
            current_memory = self._get_current_memory_usage()
            if current_memory > 80:
                batch_size = 2
            elif current_memory > 70:
                batch_size = 3
            elif current_memory < 50:
                batch_size = 5
            logger.info(f"Điều chỉnh batch size thành {batch_size} dựa trên mức sử dụng bộ nhớ {current_memory:.1f}%")

        # Chia URLs thành các batch để xử lý
        url_batches = [filtered_urls[i:i + batch_size] for i in range(0, len(filtered_urls), batch_size)]
        logger.info(f"Chia {len(filtered_urls)} URL thành {len(url_batches)} batch, mỗi batch {batch_size} URL")

        try:
            with sync_playwright() as p:
                browser = p.chromium.launch(headless=True)
                context = browser.new_context(
                    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                    viewport={"width": 1280, "height": 720}
                )

                # Thiết lập timeout và số lần thử lại
                max_retries = config.get("max_retries", 3)
                retry_delay = config.get("retry_delay", 2.0)
                timeout = config.get("timeout", 30)

                # Thiết lập batch size để tối ưu hóa bộ nhớ
                batch_size = config.get("batch_size", 3)
                if self.use_memory_optimization:
                    # Điều chỉnh batch size dựa trên mức sử dụng bộ nhớ hiện tại
                    current_memory = self._get_current_memory_usage()
                    if current_memory > 80:
                        batch_size = 2
                    elif current_memory > 70:
                        batch_size = 3
                    elif current_memory < 50:
                        batch_size = 5
                    logger.info(f"Điều chỉnh batch size thành {batch_size} dựa trên mức sử dụng bộ nhớ {current_memory:.1f}%")

                # Chia URLs thành các batch để xử lý
                url_batches = [filtered_urls[i:i + batch_size] for i in range(0, len(filtered_urls), batch_size)]
                logger.info(f"Chia {len(filtered_urls)} URL thành {len(url_batches)} batch, mỗi batch {batch_size} URL")

                batch_count = 0

                # Lấy các tham số JavaScript và SPA
                enable_javascript = config.get("enable_javascript", self.enable_javascript)
                wait_for_selector = config.get("wait_for_selector", self.wait_for_selector)
                wait_for_timeout = config.get("wait_for_timeout", self.wait_for_timeout)
                handle_spa = config.get("handle_spa", self.handle_spa)

                # Xử lý từng batch URL
                for batch in url_batches:
                    batch_count += 1
                    logger.info(f"Xử lý batch {batch_count}/{len(url_batches)} ({len(batch)} URLs)")

                    # Tối ưu hóa bộ nhớ trước khi xử lý batch mới
                    if self.use_memory_optimization and batch_count > 1:
                        self._check_memory()

                        # Cập nhật thống kê bộ nhớ
                        current_memory = self._get_current_memory_usage()
                        memory_stats["peak"] = max(memory_stats["peak"], current_memory)
                        memory_stats["checks"] += 1

                        # Điều chỉnh batch size dựa trên mức sử dụng bộ nhớ hiện tại
                        if current_memory > 85:
                            # Nếu bộ nhớ quá cao, giảm batch size cho các batch tiếp theo
                            remaining_batches = url_batches[batch_count-1:]
                            new_batch_size = max(1, batch_size // 2)
                            url_batches = url_batches[:batch_count-1]

                            # Tạo lại các batch còn lại với batch size nhỏ hơn
                            remaining_urls = [url for batch in remaining_batches for url in batch]
                            new_batches = [remaining_urls[i:i + new_batch_size] for i in range(0, len(remaining_urls), new_batch_size)]
                            url_batches.extend(new_batches)

                            logger.info(f"Điều chỉnh batch size từ {batch_size} xuống {new_batch_size} do bộ nhớ cao ({current_memory:.1f}%)")
                            batch_size = new_batch_size

                        # Ghi log thông tin bộ nhớ
                        logger.info(f"Sử dụng bộ nhớ: {current_memory:.1f}% (đỉnh: {memory_stats['peak']:.1f}%)")

                        # Điều chỉnh batch size dựa trên mức sử dụng bộ nhớ hiện tại
                        if current_memory > 85:
                            # Nếu bộ nhớ quá cao, giảm batch size cho các batch tiếp theo
                            remaining_batches = url_batches[batch_count-1:]
                            new_batch_size = max(1, batch_size // 2)
                            url_batches = url_batches[:batch_count-1]

                            # Tạo lại các batch còn lại với batch size nhỏ hơn
                            remaining_urls = [url for batch in remaining_batches for url in batch]
                            new_batches = [remaining_urls[i:i + new_batch_size] for i in range(0, len(remaining_urls), new_batch_size)]
                            url_batches.extend(new_batches)

                            logger.info(f"Điều chỉnh batch size từ {batch_size} xuống {new_batch_size} do bộ nhớ cao ({current_memory:.1f}%)")
                            batch_size = new_batch_size

                        # Ghi log thông tin bộ nhớ
                        logger.info(f"Sử dụng bộ nhớ: {current_memory:.1f}% (đỉnh: {memory_stats['peak']:.1f}%)")

                    # Xử lý từng URL trong batch
                    for url in batch:
                        if url in visited_urls:
                            continue

                        visited_urls.add(url)
                        success = False
                        retry_count = 0

                        while not success and retry_count < max_retries:
                            try:
                                # Tăng số lần thử
                                retry_count += 1

                                # Tạo trang mới
                                page = context.new_page()

                                # Thiết lập timeout cho trang
                                page.set_default_timeout(timeout * 1000)

                                # Thiết lập xử lý lỗi JavaScript
                                page.on("pageerror", lambda err: logger.warning(f"Lỗi JavaScript: {err}"))

                                # Thiết lập JavaScript
                                if not enable_javascript:
                                    # Tắt JavaScript nếu không cần
                                    page.context.add_init_script("Object.defineProperty(navigator, 'webdriver', { get: () => false })")
                                    page.route("**/*.js", lambda route: route.abort())

                                # Thiết lập chặn quảng cáo và theo dõi
                                page.route("**/*.{png,jpg,jpeg,gif,webp,svg,ico}", lambda route: route.abort())
                                page.route("**/{analytics,gtm,tracking,pixel}.js", lambda route: route.abort())

                                # Điều hướng đến URL với timeout
                                logger.info(f"Đang crawl {url} (lần thử {retry_count}/{max_retries})")

                                # Sử dụng domcontentloaded thay vì networkidle để tránh timeout
                                wait_until = "domcontentloaded" if retry_count > 1 else "networkidle"
                                page.goto(url, timeout=timeout * 1000, wait_until=wait_until)

                                # Đợi thêm thời gian nếu cần
                                if wait_for_timeout > 0:
                                    page.wait_for_timeout(wait_for_timeout)

                                # Đợi selector cụ thể nếu được chỉ định
                                if wait_for_selector:
                                    try:
                                        page.wait_for_selector(wait_for_selector, timeout=timeout * 1000)
                                    except Exception as e:
                                        logger.warning(f"Không thể tìm thấy selector '{wait_for_selector}': {str(e)}")

                                # Xử lý SPA nếu cần
                                if handle_spa:
                                    self._handle_spa(page, url)
                                # Xử lý infinite scroll nếu cần (và không phải SPA)
                                elif self.handle_infinite_scroll:
                                    self._handle_infinite_scroll(page, url)

                                # Xử lý AJAX nếu cần
                                if self.handle_ajax:
                                    self._handle_ajax(page, url)

                                # Trích xuất tiêu đề
                                title = page.title()

                                # Trích xuất nội dung
                                content = page.content()

                                # Trích xuất nội dung chính với xử lý lỗi
                                try:
                                    main_content = page.evaluate("""() => {
                                        try {
                                            // Tìm phần tử chính
                                            const main = document.querySelector('main, article, [role="main"]');
                                            if (main) return main.innerText;

                                            // Tìm phần tử nội dung
                                            const content = document.querySelector('.content, .main-content, .post-content, .article-content');
                                            if (content) return content.innerText;

                                            // Loại bỏ các phần không cần thiết
                                            const elementsToRemove = document.querySelectorAll('nav, header, footer, aside, .sidebar, .ads, .advertisement, .menu');
                                            elementsToRemove.forEach(el => el.remove());

                                            // Trả về nội dung còn lại
                                            return document.body.innerText;
                                        } catch (e) {
                                            return document.body ? document.body.innerText : document.documentElement.innerText;
                                        }
                                    }""")
                                except Exception as e:
                                    logger.warning(f"Lỗi khi trích xuất nội dung chính: {str(e)}")
                                    main_content = "Không thể trích xuất nội dung"

                                # Trích xuất metadata nếu cần
                                metadata = {}
                                if config.get("detailed_scraping", False):
                                    try:
                                        metadata = page.evaluate("""() => {
                                            const result = {};

                                            // Lấy meta description
                                            const description = document.querySelector('meta[name="description"]');
                                            if (description) result.description = description.getAttribute('content');

                                            // Lấy meta keywords
                                            const keywords = document.querySelector('meta[name="keywords"]');
                                            if (keywords) result.keywords = keywords.getAttribute('content');

                                            // Lấy thời gian xuất bản
                                            const published = document.querySelector('meta[property="article:published_time"]');
                                            if (published) result.published_time = published.getAttribute('content');

                                            return result;
                                        }""")
                                    except Exception as e:
                                        logger.warning(f"Lỗi khi trích xuất metadata: {str(e)}")

                                # Tối ưu hóa kết quả trước khi thêm vào danh sách
                                result_item = {
                                    "url": url,
                                    "title": title,
                                    "content": main_content,
                                    "crawl_time": time.strftime("%Y-%m-%d %H:%M:%S")
                                }

                                # Chỉ thêm HTML nếu cần thiết và không quá lớn
                                if config.get("detailed_scraping", False):
                                    if content and len(content) > 100000:
                                        # Giới hạn kích thước HTML
                                        result_item["html"] = content[:10000] + "... [TRUNCATED]"
                                        result_item["html_truncated"] = True
                                    else:
                                        result_item["html"] = content
                                else:
                                    result_item["html"] = None

                                # Chỉ thêm metadata nếu cần thiết
                                if config.get("detailed_scraping", False):
                                    result_item["metadata"] = metadata
                                else:
                                    result_item["metadata"] = None

                                # Thêm vào kết quả
                                results.append(result_item)

                                # Kiểm tra và tối ưu hóa bộ nhớ sau mỗi 5 kết quả
                                if len(results) % 5 == 0:
                                    self._check_memory()

                                # Crawl các liên kết nếu cần
                                if config.get("follow_links", False) and config.get("max_depth", 0) > 0:
                                    try:
                                        links = page.evaluate("""() => {
                                            return Array.from(document.querySelectorAll('a[href]'))
                                                .map(a => a.href)
                                                .filter(href => href.startsWith('http'));
                                        }""")

                                        # Lọc và xử lý các liên kết
                                        if links and len(links) > 0:
                                            # Lọc các liên kết cùng domain nếu cần
                                            if config.get("same_domain_only", True):
                                                current_domain = urlparse(url).netloc
                                                links = [link for link in links if urlparse(link).netloc == current_domain]

                                            # Giới hạn số lượng liên kết
                                            max_links = min(5, len(links))
                                            selected_links = links[:max_links]

                                            # Lưu các liên kết vào kết quả
                                            results[-1]["links"] = selected_links
                                    except Exception as e:
                                        logger.warning(f"Lỗi khi trích xuất liên kết: {str(e)}")

                                # Đánh dấu thành công
                                success = True

                                # Đóng trang
                                page.close()

                            except TimeoutError as e:
                                logger.warning(f"Timeout khi crawl {url} (lần thử {retry_count}/{max_retries}): {str(e)}")

                                # Đóng trang nếu còn mở
                                try:
                                    page.close()
                                except:
                                    pass

                                # Đợi trước khi thử lại
                                if retry_count < max_retries:
                                    time.sleep(retry_delay)
                                    # Tăng timeout cho lần thử tiếp theo
                                    timeout = min(timeout * 1.5, 60)

                            except Exception as e:
                                logger.error(f"Lỗi khi crawl {url} (lần thử {retry_count}/{max_retries}): {str(e)}")

                                # Đóng trang nếu còn mở
                                try:
                                    page.close()
                                except:
                                    pass

                                # Đợi trước khi thử lại
                                if retry_count < max_retries:
                                    time.sleep(retry_delay)

                    # Thêm vào danh sách thất bại nếu không thành công
                    if not success:
                        failed_urls.append(url)

                # Đóng trình duyệt
                browser.close()

                # Tối ưu hóa bộ nhớ sau khi crawl
                self._check_memory()

                # Ghi log thông tin về bộ nhớ
                current_memory = self._get_current_memory_usage()
                memory_stats["peak"] = max(memory_stats["peak"], current_memory)
                memory_stats["optimizations"] += 1
                logger.info(f"Hoàn tất crawl với Playwright: {len(results)} URL thành công, {len(failed_urls)} URL thất bại")
                logger.info(f"Thống kê bộ nhớ: Hiện tại {current_memory:.1f}%, Đỉnh {memory_stats['peak']:.1f}%, Kiểm tra {memory_stats['checks']} lần, Tối ưu {memory_stats['optimizations']} lần")

        except Exception as e:
            logger.error(f"Lỗi khi sử dụng Playwright: {str(e)}")

            # Tối ưu hóa bộ nhớ trước khi thử phương pháp khác
            self._check_memory(force=True)

            # Thử phương pháp với requests nếu thất bại
            return self._crawl_with_requests(filtered_urls, config)

        return {
            "success": len(results) > 0,
            "results": results,
            "failed_urls": failed_urls,
            "count": len(results),
            "retry_stats": {
                "max_retries": config.get("max_retries", 3),
                "retry_delay": config.get("retry_delay", 2.0)
            }
        }

    def _crawl_with_requests(
        self,
        urls: List[str],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Crawl với requests.

        Args:
            urls: Danh sách URL cần crawl
            config: Cấu hình crawl

        Returns:
            Dict[str, Any]: Kết quả crawl
        """
        # Tối ưu hóa bộ nhớ trước khi bắt đầu crawl
        self._check_memory(force=True)
        import requests
        from bs4 import BeautifulSoup
        from requests.exceptions import RequestException, Timeout, ConnectionError

        results = []
        failed_urls = []
        visited_urls = set()

        # Theo dõi sử dụng bộ nhớ
        memory_stats = {
            "initial": self._get_current_memory_usage(),
            "peak": 0,
            "checks": 0,
            "optimizations": 0
        }

        # Lọc URL trước khi crawl
        filtered_urls = self._filter_urls(urls)
        if not filtered_urls:
            return {
                "success": False,
                "error": "Không có URL hợp lệ sau khi lọc",
                "results": [],
                "failed_urls": urls
            }

        # Thiết lập timeout và số lần thử lại
        max_retries = config.get("max_retries", 3)
        retry_delay = config.get("retry_delay", 2.0)
        timeout = config.get("timeout", 30)

        # Thiết lập batch size để tối ưu hóa bộ nhớ
        batch_size = config.get("batch_size", 5)
        if self.use_memory_optimization:
            # Điều chỉnh batch size dựa trên mức sử dụng bộ nhớ hiện tại
            current_memory = self._get_current_memory_usage()
            if current_memory > 80:
                batch_size = 3
            elif current_memory > 70:
                batch_size = 4
            elif current_memory < 50:
                batch_size = 8
            logger.info(f"Điều chỉnh batch size thành {batch_size} dựa trên mức sử dụng bộ nhớ {current_memory:.1f}%")

        # Thiết lập headers
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Cache-Control": "max-age=0"
        }

        # Chia URLs thành các batch để xử lý
        url_batches = [filtered_urls[i:i + batch_size] for i in range(0, len(filtered_urls), batch_size)]
        logger.info(f"Chia {len(filtered_urls)} URL thành {len(url_batches)} batch, mỗi batch {batch_size} URL")

        batch_count = 0
        # Xử lý từng batch URL
        for batch in url_batches:
            batch_count += 1
            logger.info(f"Xử lý batch {batch_count}/{len(url_batches)} ({len(batch)} URLs)")

            # Tối ưu hóa bộ nhớ trước khi xử lý batch mới
            if self.use_memory_optimization and batch_count > 1:
                self._check_memory()

                # Cập nhật thống kê bộ nhớ
                current_memory = self._get_current_memory_usage()
                memory_stats["peak"] = max(memory_stats["peak"], current_memory)
                memory_stats["checks"] += 1

                # Điều chỉnh batch size dựa trên mức sử dụng bộ nhớ hiện tại
                if current_memory > 85:
                    # Nếu bộ nhớ quá cao, giảm batch size cho các batch tiếp theo
                    remaining_batches = url_batches[batch_count-1:]
                    new_batch_size = max(1, batch_size // 2)
                    url_batches = url_batches[:batch_count-1]

                    # Tạo lại các batch còn lại với batch size nhỏ hơn
                    remaining_urls = [url for batch in remaining_batches for url in batch]
                    new_batches = [remaining_urls[i:i + new_batch_size] for i in range(0, len(remaining_urls), new_batch_size)]
                    url_batches.extend(new_batches)

                    logger.info(f"Điều chỉnh batch size từ {batch_size} xuống {new_batch_size} do bộ nhớ cao ({current_memory:.1f}%)")
                    batch_size = new_batch_size

                    # Thực hiện tối ưu hóa bộ nhớ mạnh hơn
                    self._check_memory(force=True)
                    memory_stats["optimizations"] += 1
                elif current_memory > 75:
                    # Nếu bộ nhớ cao nhưng chưa quá cao, giảm nhẹ batch size
                    if batch_size > 2:
                        new_batch_size = max(2, batch_size - 1)
                        if new_batch_size != batch_size:
                            # Chỉ điều chỉnh nếu batch size thực sự thay đổi
                            remaining_batches = url_batches[batch_count-1:]
                            url_batches = url_batches[:batch_count-1]

                            # Tạo lại các batch còn lại với batch size nhỏ hơn
                            remaining_urls = [url for batch in remaining_batches for url in batch]
                            new_batches = [remaining_urls[i:i + new_batch_size] for i in range(0, len(remaining_urls), new_batch_size)]
                            url_batches.extend(new_batches)

                            logger.info(f"Điều chỉnh nhẹ batch size từ {batch_size} xuống {new_batch_size} do bộ nhớ cao ({current_memory:.1f}%)")
                            batch_size = new_batch_size

                # Ghi log thông tin bộ nhớ
                logger.info(f"Sử dụng bộ nhớ: {current_memory:.1f}% (đỉnh: {memory_stats['peak']:.1f}%)")

            # Crawl từng URL trong batch
            for url in batch:
                if url in visited_urls:
                    continue

                visited_urls.add(url)
                success = False
                retry_count = 0

                while not success and retry_count < max_retries:
                    try:
                        # Tăng số lần thử
                        retry_count += 1

                        # Ghi log
                        logger.info(f"Đang crawl {url} (lần thử {retry_count}/{max_retries})")

                        # Gửi request với timeout
                        response = requests.get(
                            url,
                            headers=headers,
                            timeout=timeout,
                            allow_redirects=True,
                            verify=True
                        )
                        response.raise_for_status()

                        # Phân tích HTML
                        soup = BeautifulSoup(response.text, "html.parser")

                        # Lấy tiêu đề
                        title = soup.title.string if soup.title else ""

                        # Xóa các phần không cần thiết
                        for tag in soup(["script", "style", "nav", "header", "footer", "aside", "iframe", "noscript"]):
                            tag.decompose()

                        # Lấy nội dung chính
                        main_content = ""

                        # Thử tìm phần tử main hoặc article
                        main_element = (
                            soup.find("main")
                            or soup.find("article")
                            or soup.find("div", class_=["content", "main-content", "post-content", "article-content"])
                            or soup.find("div", id=["content", "main-content", "post-content", "article-content"])
                        )

                        if main_element:
                            main_content = main_element.get_text(separator="\n", strip=True)
                        else:
                            # Nếu không tìm thấy phần tử main, lấy tất cả văn bản
                            main_content = soup.get_text(separator="\n", strip=True)

                        # Trích xuất metadata nếu cần
                        metadata = {}
                        if config.get("detailed_scraping", False):
                            # Lấy meta description
                            meta_desc = soup.find("meta", attrs={"name": "description"})
                            if meta_desc:
                                metadata["description"] = meta_desc.get("content", "")

                            # Lấy meta keywords
                            meta_keywords = soup.find("meta", attrs={"name": "keywords"})
                            if meta_keywords:
                                metadata["keywords"] = meta_keywords.get("content", "")

                            # Lấy thời gian xuất bản
                            meta_published = soup.find("meta", attrs={"property": "article:published_time"})
                            if meta_published:
                                metadata["published_time"] = meta_published.get("content", "")

                        # Trích xuất liên kết nếu cần
                        links = []
                        if config.get("follow_links", False) and config.get("max_depth", 0) > 0:
                            # Lấy tất cả liên kết
                            all_links = [a.get("href") for a in soup.find_all("a", href=True)]

                            # Lọc các liên kết hợp lệ
                            valid_links = []
                            for link in all_links:
                                # Chuyển đổi liên kết tương đối thành tuyệt đối
                                if link.startswith("/"):
                                    parsed_base = urlparse(url)
                                    base_url = f"{parsed_base.scheme}://{parsed_base.netloc}"
                                    link = base_url + link

                                # Chỉ giữ lại các liên kết HTTP/HTTPS
                                if link.startswith(("http://", "https://")):
                                    valid_links.append(link)

                            # Lọc các liên kết cùng domain nếu cần
                            if config.get("same_domain_only", True) and valid_links:
                                current_domain = urlparse(url).netloc
                                valid_links = [link for link in valid_links if urlparse(link).netloc == current_domain]

                            # Giới hạn số lượng liên kết
                            max_links = min(5, len(valid_links))
                            links = valid_links[:max_links]

                        # Tối ưu hóa nội dung trước khi thêm vào kết quả
                        # Giới hạn kích thước nội dung để tiết kiệm bộ nhớ
                        max_content_length = config.get("max_content_length", 100000)
                        if len(main_content) > max_content_length:
                            main_content = main_content[:max_content_length] + "... [TRUNCATED]"
                            logger.info(f"Đã cắt ngắn nội dung của {url} từ {len(main_content)} xuống {max_content_length} ký tự")

                        # Chỉ lưu HTML nếu cần thiết và giới hạn kích thước
                        html_content = None
                        if config.get("detailed_scraping", False):
                            html_str = str(soup)
                            max_html_length = config.get("max_html_length", 200000)
                            if len(html_str) > max_html_length:
                                html_content = html_str[:max_html_length] + "... [TRUNCATED]"
                                logger.info(f"Đã cắt ngắn HTML của {url} từ {len(html_str)} xuống {max_html_length} ký tự")
                            else:
                                html_content = html_str

                        # Tối ưu hóa kết quả trước khi thêm vào danh sách
                        result_item = {
                            "url": url,
                            "title": title,
                            "content": main_content,
                            "crawl_time": time.strftime("%Y-%m-%d %H:%M:%S")
                        }

                        # Chỉ thêm HTML nếu cần thiết và không quá lớn
                        if config.get("detailed_scraping", False):
                            if html_content and len(html_content) > 100000:
                                # Giới hạn kích thước HTML
                                result_item["html"] = html_content[:10000] + "... [TRUNCATED]"
                                result_item["html_truncated"] = True
                            else:
                                result_item["html"] = html_content
                        else:
                            result_item["html"] = None

                        # Chỉ thêm metadata nếu cần thiết
                        if config.get("detailed_scraping", False):
                            result_item["metadata"] = metadata
                        else:
                            result_item["metadata"] = None

                        # Giới hạn số lượng liên kết
                        if links and len(links) > 0:
                            result_item["links"] = links[:10] if len(links) > 10 else links
                        else:
                            result_item["links"] = None

                        # Thêm vào kết quả
                        results.append(result_item)

                        # Kiểm tra và tối ưu hóa bộ nhớ sau mỗi 5 kết quả
                        if len(results) % 5 == 0:
                            self._check_memory()

                        # Đánh dấu thành công
                        success = True

                    except requests.exceptions.Timeout as e:
                        logger.warning(f"Timeout khi crawl {url} (lần thử {retry_count}/{max_retries}): {str(e)}")

                        # Đợi trước khi thử lại
                        if retry_count < max_retries:
                            time.sleep(retry_delay)
                            # Tăng timeout cho lần thử tiếp theo
                            timeout = min(timeout * 1.5, 60)

                    except requests.exceptions.ConnectionError as e:
                        logger.warning(f"Lỗi kết nối khi crawl {url} (lần thử {retry_count}/{max_retries}): {str(e)}")

                        # Đợi lâu hơn trước khi thử lại
                        if retry_count < max_retries:
                            time.sleep(retry_delay * 2)

                    except requests.exceptions.RequestException as e:
                        logger.warning(f"Lỗi request khi crawl {url} (lần thử {retry_count}/{max_retries}): {str(e)}")

                        # Đợi trước khi thử lại
                        if retry_count < max_retries:
                            time.sleep(retry_delay)

                    except Exception as e:
                        logger.error(f"Lỗi không xác định khi crawl {url} (lần thử {retry_count}/{max_retries}): {str(e)}")

                        # Đợi trước khi thử lại
                        if retry_count < max_retries:
                            time.sleep(retry_delay)

                        # Kiểm tra và tối ưu hóa bộ nhớ nếu cần
                        if retry_count % 3 == 0:  # Kiểm tra sau mỗi 3 lần thử
                            self._check_memory()

            # Thêm vào danh sách thất bại nếu không thành công
            if not success:
                failed_urls.append(url)

        # Tối ưu hóa bộ nhớ sau khi crawl
        self._check_memory(force=True)

        # Tối ưu hóa kết quả để giảm bộ nhớ
        if len(results) > 0:
            logger.info(f"Tối ưu hóa {len(results)} kết quả để giảm bộ nhớ")
            for result in results:
                # Giới hạn kích thước nội dung
                if "content" in result and isinstance(result["content"], str) and len(result["content"]) > 50000:
                    result["content"] = result["content"][:50000] + "... [TRUNCATED]"

                # Xóa HTML nếu quá lớn
                if "html" in result and result["html"] and len(result["html"]) > 100000:
                    result["html"] = result["html"][:5000] + "... [TRUNCATED]"

                # Giới hạn số lượng liên kết
                if "links" in result and result["links"] and len(result["links"]) > 10:
                    result["links"] = result["links"][:10]

        # Ghi log thông tin về bộ nhớ
        current_memory = self._get_current_memory_usage()
        memory_stats["peak"] = max(memory_stats["peak"], current_memory)
        memory_stats["optimizations"] += 1
        logger.info(f"Hoàn tất crawl với Requests: {len(results)} URL thành công, {len(failed_urls)} URL thất bại")
        logger.info(f"Thống kê bộ nhớ: Hiện tại {current_memory:.1f}%, Đỉnh {memory_stats['peak']:.1f}%, Kiểm tra {memory_stats['checks']} lần, Tối ưu {memory_stats['optimizations']} lần")

        return {
            "success": len(results) > 0,
            "results": results,
            "failed_urls": failed_urls,
            "count": len(results),
            "retry_stats": {
                "max_retries": max_retries,
                "retry_delay": retry_delay
            }
        }

    def _handle_ajax(self, page, url: str) -> None:
        """
        Xử lý các request AJAX.

        Phương thức này theo dõi và đợi các request AJAX hoàn thành trước khi trích xuất nội dung.

        Args:
            page: Đối tượng Playwright Page
            url: URL của trang web
        """
        try:
            # Đợi thêm thời gian để JavaScript được thực thi
            page.wait_for_timeout(1000)

            # Theo dõi các request AJAX
            ajax_requests = []
            ajax_responses = []

            # Thiết lập theo dõi request
            page.on("request", lambda request: self._track_ajax_request(request, ajax_requests))
            page.on("response", lambda response: self._track_ajax_response(response, ajax_responses))

            # Kích hoạt các sự kiện trên trang để kích hoạt AJAX
            self._trigger_ajax_events(page)

            # Đợi các request AJAX hoàn thành
            logger.info(f"Đợi {self.ajax_wait_time}ms cho các request AJAX hoàn thành")
            page.wait_for_timeout(self.ajax_wait_time)

            # Kiểm tra xem có request AJAX nào đang chờ không
            pending_requests = [req for req in ajax_requests if req not in ajax_responses]
            if pending_requests:
                logger.info(f"Còn {len(pending_requests)} request AJAX đang chờ, đợi thêm")
                page.wait_for_timeout(self.ajax_wait_time / 2)

            # Log thông tin về các request AJAX
            logger.debug(f"Đã theo dõi {len(ajax_requests)} request AJAX, {len(ajax_responses)} đã hoàn thành")

        except Exception as e:
            logger.warning(f"Lỗi khi xử lý AJAX {url}: {str(e)}")

    def _track_ajax_request(self, request, ajax_requests: List) -> None:
        """
        Theo dõi request AJAX.

        Args:
            request: Đối tượng Request từ Playwright
            ajax_requests: Danh sách các request AJAX đang theo dõi
        """
        try:
            # Kiểm tra xem request có phải là AJAX không
            url = request.url
            resource_type = request.resource_type

            # Kiểm tra resource type
            is_ajax = resource_type in ["xhr", "fetch"]

            # Kiểm tra URL pattern
            if not is_ajax and self.ajax_request_patterns:
                is_ajax = any(pattern in url for pattern in self.ajax_request_patterns)

            if is_ajax:
                ajax_requests.append(request)
                logger.debug(f"Phát hiện request AJAX: {url} ({resource_type})")
        except Exception as e:
            logger.warning(f"Lỗi khi theo dõi request AJAX: {str(e)}")

    def _track_ajax_response(self, response, ajax_responses: List) -> None:
        """
        Theo dõi response AJAX.

        Args:
            response: Đối tượng Response từ Playwright
            ajax_responses: Danh sách các response AJAX đã nhận
        """
        try:
            # Lấy request tương ứng
            request = response.request

            # Thêm vào danh sách response
            ajax_responses.append(request)

            # Log thông tin
            logger.debug(f"Nhận response AJAX: {response.status} - {request.url}")
        except Exception as e:
            logger.warning(f"Lỗi khi theo dõi response AJAX: {str(e)}")

    def _trigger_ajax_events(self, page) -> None:
        """
        Kích hoạt các sự kiện trên trang để kích hoạt AJAX.

        Args:
            page: Đối tượng Playwright Page
        """
        try:
            # Cuộn trang để kích hoạt lazy loading
            page.evaluate("""() => {
                window.scrollTo(0, 100);
                setTimeout(() => window.scrollTo(0, 0), 100);
            }""")

            # Nhấp vào các nút có thể kích hoạt AJAX
            page.evaluate("""() => {
                // Tìm các nút có thể kích hoạt AJAX
                const ajaxTriggers = Array.from(document.querySelectorAll(
                    'button.load-more, button.ajax, [data-ajax="true"], .ajax-load, .load-ajax'
                ));

                // Nhấp vào mỗi nút
                ajaxTriggers.slice(0, 3).forEach(el => {
                    try {
                        el.click();
                    } catch (e) {
                        // Bỏ qua lỗi
                    }
                });
            }""")

            # Đợi một chút sau khi kích hoạt
            page.wait_for_timeout(500)

        except Exception as e:
            logger.warning(f"Lỗi khi kích hoạt sự kiện AJAX: {str(e)}")

    def _handle_infinite_scroll(self, page, url: str) -> None:
        """
        Xử lý các trang có infinite scroll.

        Phương thức này thực hiện cuộn trang nhiều lần để tải thêm nội dung từ các trang có infinite scroll.

        Args:
            page: Đối tượng Playwright Page
            url: URL của trang web
        """
        try:
            # Đợi thêm thời gian để JavaScript được thực thi
            page.wait_for_timeout(1000)

            # Kiểm tra xem trang có infinite scroll không
            has_infinite_scroll = page.evaluate("""() => {
                // Kiểm tra các dấu hiệu của infinite scroll
                return !!(
                    // Kiểm tra các thuộc tính phổ biến
                    document.querySelector('.infinite-scroll') ||
                    document.querySelector('[data-infinite-scroll]') ||
                    document.querySelector('.load-more') ||

                    // Kiểm tra các thư viện phổ biến
                    (window.jQuery && window.jQuery.fn.infiniteScroll) ||
                    window.InfiniteScroll ||

                    // Kiểm tra các thuộc tính của React, Vue, Angular
                    document.querySelector('[data-scroll="infinite"]') ||
                    document.querySelector('[v-infinite-scroll]') ||
                    document.querySelector('[ng-infinite-scroll]')
                );
            }""")

            # Nếu không phát hiện được infinite scroll, thử cuộn và kiểm tra sự thay đổi chiều cao
            if not has_infinite_scroll:
                # Lưu chiều cao ban đầu
                initial_height = page.evaluate("() => document.body.scrollHeight")

                # Cuộn xuống dưới
                page.evaluate("() => window.scrollTo(0, document.body.scrollHeight)")

                # Đợi một chút
                page.wait_for_timeout(self.infinite_scroll_timeout)

                # Kiểm tra chiều cao mới
                new_height = page.evaluate("() => document.body.scrollHeight")

                # Nếu chiều cao thay đổi, có thể là infinite scroll
                has_infinite_scroll = new_height > initial_height

            if has_infinite_scroll:
                logger.info(f"Phát hiện trang có infinite scroll: {url}")

                # Thực hiện cuộn nhiều lần để tải thêm nội dung
                for i in range(self.infinite_scroll_max_scrolls):
                    # Lưu chiều cao trước khi cuộn
                    before_height = page.evaluate("() => document.body.scrollHeight")

                    # Cuộn xuống dưới
                    page.evaluate("""() => {
                        return new Promise((resolve) => {
                            // Cuộn xuống dưới cùng
                            window.scrollTo(0, document.body.scrollHeight);
                            resolve();
                        });
                    }""")

                    # Đợi để trang tải thêm nội dung
                    page.wait_for_timeout(self.infinite_scroll_timeout)

                    # Kiểm tra chiều cao sau khi cuộn
                    after_height = page.evaluate("() => document.body.scrollHeight")

                    logger.debug(f"Cuộn lần {i+1}/{self.infinite_scroll_max_scrolls}: {before_height} -> {after_height}")

                    # Nếu chiều cao không thay đổi, có thể đã hết nội dung
                    if after_height == before_height:
                        logger.info(f"Đã đạt đến cuối trang sau {i+1} lần cuộn")
                        break

                # Cuộn lên đầu trang để có thể trích xuất nội dung từ đầu
                page.evaluate("() => window.scrollTo(0, 0)")

                # Đợi thêm thời gian
                page.wait_for_timeout(1000)

        except Exception as e:
            logger.warning(f"Lỗi khi xử lý infinite scroll {url}: {str(e)}")

    def _handle_spa(self, page, url: str) -> None:
        """
        Xử lý các trang Single Page Application (SPA).

        Phương thức này thực hiện các tác vụ đặc biệt để xử lý các trang SPA như React, Angular, Vue.js.

        Args:
            page: Đối tượng Playwright Page
            url: URL của trang web
        """
        try:
            # Đợi thêm thời gian để JavaScript được thực thi
            page.wait_for_timeout(2000)

            # Kiểm tra xem trang có phải là SPA không
            is_spa = page.evaluate("""() => {
                // Kiểm tra các framework phổ biến
                return !!(
                    window.React ||
                    window.angular ||
                    window.Vue ||
                    document.querySelector('[data-reactroot]') ||
                    document.querySelector('[ng-app]') ||
                    document.querySelector('[ng-controller]') ||
                    document.querySelector('[v-app]')
                );
            }""")

            if is_spa:
                logger.info(f"Phát hiện trang SPA: {url}")

                # Cuộn trang để tải thêm nội dung (lazy loading)
                page.evaluate("""() => {
                    return new Promise((resolve) => {
                        let totalHeight = 0;
                        const distance = 100;
                        const timer = setInterval(() => {
                            const scrollHeight = document.body.scrollHeight;
                            window.scrollBy(0, distance);
                            totalHeight += distance;

                            if(totalHeight >= scrollHeight){
                                clearInterval(timer);
                                resolve();
                            }
                        }, 100);
                    });
                }""")

                # Đợi thêm thời gian sau khi cuộn
                page.wait_for_timeout(1000)

                # Thử nhấp vào các tab hoặc nút để hiển thị thêm nội dung
                page.evaluate("""() => {
                    // Tìm các tab và nút
                    const clickables = Array.from(document.querySelectorAll('button, .tab, [role="tab"], .nav-item, .accordion-header'));

                    // Nhấp vào mỗi phần tử
                    clickables.slice(0, 5).forEach(el => {
                        try {
                            el.click();
                        } catch (e) {
                            // Bỏ qua lỗi
                        }
                    });
                }""")

                # Đợi thêm thời gian sau khi nhấp
                page.wait_for_timeout(1000)

                # Xử lý infinite scroll nếu cần
                if self.handle_infinite_scroll:
                    self._handle_infinite_scroll(page, url)

        except Exception as e:
            logger.warning(f"Lỗi khi xử lý SPA {url}: {str(e)}")

    def _extract_images(self, url: str, content: str, page=None) -> List[Dict[str, Any]]:
        """
        Trích xuất hình ảnh từ nội dung.

        Args:
            url: URL của trang web
            content: Nội dung HTML của trang web
            page: Đối tượng Playwright Page (nếu có)

        Returns:
            List[Dict[str, Any]]: Danh sách hình ảnh
        """
        images = []
        base_url = url

        # Trích xuất base tag
        base_match = re.search(r'<base\s+href=["\'](.*?)["\']', content, re.IGNORECASE)
        if base_match:
            base_url = base_match.group(1).strip()

        # Nếu có Playwright Page, sử dụng JavaScript để trích xuất tất cả hình ảnh
        if page and self.use_playwright:
            try:
                js_images = page.evaluate("""() => {
                    const images = [];

                    // Lấy tất cả thẻ img
                    document.querySelectorAll('img').forEach(img => {
                        if (img.src) {
                            images.push({
                                src: img.src,
                                alt: img.alt || '',
                                width: img.width || '',
                                height: img.height || '',
                                title: img.title || '',
                                type: 'img'
                            });
                        }
                    });

                    // Lấy tất cả background image từ CSS
                    Array.from(document.querySelectorAll('*')).forEach(el => {
                        const style = window.getComputedStyle(el);
                        const bgImage = style.backgroundImage;
                        if (bgImage && bgImage !== 'none') {
                            const match = bgImage.match(/url\\(['"]?([^'"\\)]+)['"]?\\)/);
                            if (match && match[1]) {
                                images.push({
                                    src: match[1],
                                    alt: 'CSS Background Image',
                                    width: el.offsetWidth || '',
                                    height: el.offsetHeight || '',
                                    title: '',
                                    type: 'css'
                                });
                            }
                        }
                    });

                    // Lấy tất cả thẻ picture và source
                    document.querySelectorAll('picture').forEach(picture => {
                        const sources = picture.querySelectorAll('source');
                        sources.forEach(source => {
                            if (source.srcset) {
                                // Lấy URL từ srcset (lấy URL đầu tiên nếu có nhiều)
                                const srcset = source.srcset.split(',')[0].trim().split(' ')[0];
                                if (srcset) {
                                    images.push({
                                        src: srcset,
                                        alt: picture.querySelector('img') ? picture.querySelector('img').alt || '' : '',
                                        width: '',
                                        height: '',
                                        title: '',
                                        type: 'picture'
                                    });
                                }
                            }
                        });
                    });

                    return images;
                }""")

                # Chuyển đổi relative URL thành absolute URL và thêm vào danh sách
                for img in js_images:
                    src = img['src']
                    if not src.startswith(('http://', 'https://')):
                        src = urljoin(base_url, src)

                    # Lọc hình ảnh dựa trên kích thước nếu có
                    if img['width'] and img['height']:
                        try:
                            width = int(img['width'])
                            height = int(img['height'])
                            # Bỏ qua hình ảnh quá nhỏ (có thể là icon)
                            if width < 50 and height < 50:
                                continue
                        except (ValueError, TypeError):
                            pass

                    images.append({
                        'url': src,
                        'alt': img['alt'],
                        'title': img['title'],
                        'width': img['width'],
                        'height': img['height'],
                        'source_url': url,
                        'source_type': img['type']
                    })
            except Exception as e:
                logger.warning(f"Lỗi khi trích xuất hình ảnh với JavaScript: {str(e)}")

        # Trích xuất img tags từ HTML
        img_tags = re.findall(r"<img\s+([^>]*)>", content, re.IGNORECASE)
        for img_tag in img_tags:
            src_match = re.search(r'src=["\'](.*?)["\']', img_tag)
            if src_match:
                src = src_match.group(1).strip()

                # Bỏ qua các src không hợp lệ
                if not src or src.startswith("data:"):
                    continue

                # Chuyển đổi relative URL thành absolute URL
                if not src.startswith(('http://', 'https://')):
                    src = urljoin(base_url, src)

                # Trích xuất alt
                alt_match = re.search(r'alt=["\'](.*?)["\']', img_tag)
                alt = alt_match.group(1).strip() if alt_match else ""

                # Trích xuất title
                title_match = re.search(r'title=["\'](.*?)["\']', img_tag)
                title = title_match.group(1).strip() if title_match else ""

                # Trích xuất width và height
                width_match = re.search(r'width=["\'](.*?)["\']', img_tag)
                width = width_match.group(1).strip() if width_match else ""

                height_match = re.search(r'height=["\'](.*?)["\']', img_tag)
                height = height_match.group(1).strip() if height_match else ""

                # Lọc hình ảnh dựa trên kích thước nếu có
                if width and height:
                    try:
                        width_val = int(width)
                        height_val = int(height)
                        # Bỏ qua hình ảnh quá nhỏ (có thể là icon)
                        if width_val < 50 and height_val < 50:
                            continue
                    except (ValueError, TypeError):
                        pass

                # Thêm vào danh sách hình ảnh nếu chưa tồn tại
                image_url = src
                if not any(img['url'] == image_url for img in images):
                    images.append({
                        'url': image_url,
                        'alt': alt,
                        'title': title,
                        'width': width,
                        'height': height,
                        'source_url': url,
                        'source_type': 'html'
                    })

        # Trích xuất background images từ style tags
        style_tags = re.findall(r"<style[^>]*>(.*?)</style>", content, re.IGNORECASE | re.DOTALL)
        for style_tag in style_tags:
            bg_images = re.findall(r"background(?:-image)?:\s*url\(['\"](.*?)['\"]", style_tag)
            for bg_image in bg_images:
                # Chuyển đổi relative URL thành absolute URL
                if not bg_image.startswith(('http://', 'https://')):
                    bg_image = urljoin(base_url, bg_image)

                # Thêm vào danh sách hình ảnh nếu chưa tồn tại
                if not any(img['url'] == bg_image for img in images):
                    images.append({
                        'url': bg_image,
                        'alt': 'CSS Background Image',
                        'title': '',
                        'width': '',
                        'height': '',
                        'source_url': url,
                        'source_type': 'css'
                    })

        # Trích xuất picture và source tags
        picture_tags = re.findall(r"<picture\b[^>]*>(.*?)</picture>", content, re.IGNORECASE | re.DOTALL)
        for picture_tag in picture_tags:
            # Trích xuất source tags
            source_tags = re.findall(r"<source\b[^>]*>", picture_tag, re.IGNORECASE)
            for source_tag in source_tags:
                srcset_match = re.search(r'srcset=["\'](.*?)["\']', source_tag)
                if srcset_match:
                    srcset = srcset_match.group(1).strip()
                    # Lấy URL đầu tiên từ srcset
                    first_src = srcset.split(',')[0].strip().split(' ')[0]

                    if first_src:
                        # Chuyển đổi relative URL thành absolute URL
                        if not first_src.startswith(('http://', 'https://')):
                            first_src = urljoin(base_url, first_src)

                        # Thêm vào danh sách hình ảnh nếu chưa tồn tại
                        if not any(img['url'] == first_src for img in images):
                            images.append({
                                'url': first_src,
                                'alt': 'Picture Source',
                                'title': '',
                                'width': '',
                                'height': '',
                                'source_url': url,
                                'source_type': 'picture'
                            })

        # Tải xuống hình ảnh nếu cần
        if self.download_media and self.download_path:
            self._download_images(images)

        return images

    def _download_images(self, images: List[Dict[str, Any]]) -> None:
        """
        Tải xuống hình ảnh từ danh sách.

        Args:
            images: Danh sách hình ảnh cần tải xuống
        """
        import requests
        from PIL import Image
        import io

        # Tạo thư mục lưu hình ảnh nếu chưa tồn tại
        images_dir = os.path.join(self.download_path, "images")
        os.makedirs(images_dir, exist_ok=True)

        for i, image in enumerate(images):
            image_url = image['url']
            try:
                # Tạo tên file
                parsed_url = urlparse(image_url)
                filename = os.path.basename(parsed_url.path)
                if not filename or "." not in filename:
                    # Tạo tên file từ hash của URL
                    filename = f"image_{hashlib.md5(image_url.encode()).hexdigest()}.jpg"

                # Tạo đường dẫn lưu file
                save_path = os.path.join(images_dir, filename)

                # Kiểm tra nếu file đã tồn tại
                if os.path.exists(save_path):
                    # Cập nhật thông tin tải xuống
                    image['downloaded'] = True
                    image['local_path'] = save_path
                    image['file_size'] = os.path.getsize(save_path)
                    continue

                # Tải hình ảnh
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
                }
                response = requests.get(image_url, headers=headers, timeout=30, stream=True)
                response.raise_for_status()

                # Kiểm tra kích thước file
                content_length = int(response.headers.get('content-length', 0))
                if content_length > self.max_media_size_mb * 1024 * 1024:
                    logger.warning(f"Bỏ qua hình ảnh quá lớn: {image_url} ({content_length / (1024 * 1024):.2f} MB > {self.max_media_size_mb} MB)")
                    image['downloaded'] = False
                    image['download_error'] = f"File quá lớn: {content_length / (1024 * 1024):.2f} MB > {self.max_media_size_mb} MB"
                    continue

                # Lưu hình ảnh
                with open(save_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)

                # Cập nhật thông tin tải xuống
                image['downloaded'] = True
                image['local_path'] = save_path
                image['file_size'] = os.path.getsize(save_path)

                # Trích xuất thông tin hình ảnh
                try:
                    img = Image.open(save_path)
                    image['width'] = img.width
                    image['height'] = img.height
                    image['format'] = img.format
                    image['mode'] = img.mode
                except Exception as e:
                    logger.warning(f"Lỗi khi trích xuất thông tin hình ảnh {image_url}: {str(e)}")

            except Exception as e:
                logger.warning(f"Lỗi khi tải xuống hình ảnh {image_url}: {str(e)}")
                image['downloaded'] = False
                image['download_error'] = str(e)

    def _extract_videos(self, url: str, content: str, page=None) -> List[Dict[str, Any]]:
        """
        Trích xuất video từ nội dung.

        Args:
            url: URL của trang web
            content: Nội dung HTML của trang web
            page: Đối tượng Playwright Page (nếu có)

        Returns:
            List[Dict[str, Any]]: Danh sách video
        """
        videos = []
        base_url = url

        # Trích xuất base tag
        base_match = re.search(r'<base\s+href=["\'](.*?)["\']', content, re.IGNORECASE)
        if base_match:
            base_url = base_match.group(1).strip()

        # Nếu có Playwright Page, sử dụng JavaScript để trích xuất tất cả video
        if page and self.use_playwright:
            try:
                js_videos = page.evaluate("""() => {
                    const videos = [];

                    // Lấy tất cả thẻ video
                    document.querySelectorAll('video').forEach(video => {
                        const sources = [];

                        // Lấy tất cả thẻ source trong video
                        video.querySelectorAll('source').forEach(source => {
                            if (source.src) {
                                sources.push({
                                    src: source.src,
                                    type: source.type || ''
                                });
                            }
                        });

                        // Nếu video có src trực tiếp
                        if (video.src) {
                            sources.push({
                                src: video.src,
                                type: video.getAttribute('type') || ''
                            });
                        }

                        if (sources.length > 0) {
                            videos.push({
                                sources: sources,
                                width: video.videoWidth || video.width || '',
                                height: video.videoHeight || video.height || '',
                                poster: video.poster || '',
                                duration: video.duration || '',
                                type: 'html5'
                            });
                        }
                    });

                    // Lấy tất cả iframe có thể chứa video nhúng
                    document.querySelectorAll('iframe').forEach(iframe => {
                        const src = iframe.src || '';
                        if (src) {
                            // YouTube embeds
                            if (src.includes('youtube.com/embed/') || src.includes('youtube-nocookie.com/embed/')) {
                                const match = src.match(/embed\\/([-\\w]+)/);
                                if (match && match[1]) {
                                    videos.push({
                                        videoId: match[1],
                                        src: src,
                                        width: iframe.width || '',
                                        height: iframe.height || '',
                                        type: 'youtube'
                                    });
                                }
                            }
                            // Vimeo embeds
                            else if (src.includes('player.vimeo.com/video/')) {
                                const match = src.match(/video\\/(\\d+)/);
                                if (match && match[1]) {
                                    videos.push({
                                        videoId: match[1],
                                        src: src,
                                        width: iframe.width || '',
                                        height: iframe.height || '',
                                        type: 'vimeo'
                                    });
                                }
                            }
                            // Dailymotion embeds
                            else if (src.includes('dailymotion.com/embed/')) {
                                const match = src.match(/embed\\/video\\/([-\\w]+)/);
                                if (match && match[1]) {
                                    videos.push({
                                        videoId: match[1],
                                        src: src,
                                        width: iframe.width || '',
                                        height: iframe.height || '',
                                        type: 'dailymotion'
                                    });
                                }
                            }
                            // Facebook embeds
                            else if (src.includes('facebook.com/plugins/video.php')) {
                                videos.push({
                                    src: src,
                                    width: iframe.width || '',
                                    height: iframe.height || '',
                                    type: 'facebook'
                                });
                            }
                            // Các iframe khác có thể chứa video
                            else if (src.includes('video') || src.includes('player') || src.includes('embed')) {
                                videos.push({
                                    src: src,
                                    width: iframe.width || '',
                                    height: iframe.height || '',
                                    type: 'iframe'
                                });
                            }
                        }
                    });

                    return videos;
                }""")

                # Chuyển đổi relative URL thành absolute URL và thêm vào danh sách
                for video in js_videos:
                    # Xử lý video HTML5
                    if video.get('type') == 'html5':
                        sources = video.get('sources', [])
                        for source in sources:
                            src = source.get('src', '')
                            if not src.startswith(('http://', 'https://')):
                                src = urljoin(base_url, src)
                            source['src'] = src

                        # Xử lý poster nếu có
                        poster = video.get('poster', '')
                        if poster and not poster.startswith(('http://', 'https://')):
                            video['poster'] = urljoin(base_url, poster)

                        videos.append({
                            'url': sources[0]['src'] if sources else '',
                            'sources': sources,
                            'width': video.get('width', ''),
                            'height': video.get('height', ''),
                            'poster': video.get('poster', ''),
                            'duration': video.get('duration', ''),
                            'source_url': url,
                            'source_type': 'html5'
                        })
                    # Xử lý video nhúng
                    else:
                        src = video.get('src', '')
                        if not src.startswith(('http://', 'https://')):
                            src = urljoin(base_url, src)

                        embed_type = video.get('type', 'unknown')
                        video_id = video.get('videoId', '')

                        # Tạo URL trực tiếp đến video nếu có thể
                        direct_url = ''
                        if embed_type == 'youtube' and video_id:
                            direct_url = f"https://www.youtube.com/watch?v={video_id}"
                        elif embed_type == 'vimeo' and video_id:
                            direct_url = f"https://vimeo.com/{video_id}"
                        elif embed_type == 'dailymotion' and video_id:
                            direct_url = f"https://www.dailymotion.com/video/{video_id}"

                        videos.append({
                            'url': direct_url or src,
                            'embed_url': src,
                            'video_id': video_id,
                            'width': video.get('width', ''),
                            'height': video.get('height', ''),
                            'source_url': url,
                            'source_type': embed_type
                        })
            except Exception as e:
                logger.warning(f"Lỗi khi trích xuất video với JavaScript: {str(e)}")

        # Trích xuất video tags từ HTML
        video_tags = re.findall(r"<video\b[^>]*>(.*?)</video>", content, re.IGNORECASE | re.DOTALL)
        for video_tag in video_tags:
            # Trích xuất thuộc tính của thẻ video
            video_attrs = re.search(r"<video\b([^>]*)>", video_tag, re.IGNORECASE)
            if video_attrs:
                attrs = video_attrs.group(1)

                # Trích xuất src
                src_match = re.search(r'src=["\'](.*?)["\']', attrs)
                src = src_match.group(1).strip() if src_match else ""

                # Trích xuất width và height
                width_match = re.search(r'width=["\'](.*?)["\']', attrs)
                width = width_match.group(1).strip() if width_match else ""

                height_match = re.search(r'height=["\'](.*?)["\']', attrs)
                height = height_match.group(1).strip() if height_match else ""

                # Trích xuất poster
                poster_match = re.search(r'poster=["\'](.*?)["\']', attrs)
                poster = poster_match.group(1).strip() if poster_match else ""

                # Trích xuất các thẻ source
                sources = []
                source_tags = re.findall(r"<source\b([^>]*)>", video_tag, re.IGNORECASE)
                for source_tag in source_tags:
                    source_src_match = re.search(r'src=["\'](.*?)["\']', source_tag)
                    if source_src_match:
                        source_src = source_src_match.group(1).strip()

                        # Chuyển đổi relative URL thành absolute URL
                        if not source_src.startswith(('http://', 'https://')):
                            source_src = urljoin(base_url, source_src)

                        # Trích xuất type
                        source_type_match = re.search(r'type=["\'](.*?)["\']', source_tag)
                        source_type = source_type_match.group(1).strip() if source_type_match else ""

                        sources.append({
                            'src': source_src,
                            'type': source_type
                        })

                # Nếu có src trực tiếp, thêm vào sources
                if src:
                    # Chuyển đổi relative URL thành absolute URL
                    if not src.startswith(('http://', 'https://')):
                        src = urljoin(base_url, src)

                    sources.append({
                        'src': src,
                        'type': ''
                    })

                # Nếu có sources, thêm vào danh sách video
                if sources:
                    # Chuyển đổi poster URL nếu có
                    if poster and not poster.startswith(('http://', 'https://')):
                        poster = urljoin(base_url, poster)

                    # Thêm vào danh sách video nếu chưa tồn tại
                    video_url = sources[0]['src']
                    if not any(v['url'] == video_url for v in videos):
                        videos.append({
                            'url': video_url,
                            'sources': sources,
                            'width': width,
                            'height': height,
                            'poster': poster,
                            'source_url': url,
                            'source_type': 'html5'
                        })

        # Trích xuất iframe có thể chứa video nhúng
        iframe_tags = re.findall(r"<iframe\b([^>]*)>", content, re.IGNORECASE)
        for iframe_tag in iframe_tags:
            # Trích xuất src
            src_match = re.search(r'src=["\'](.*?)["\']', iframe_tag)
            if src_match:
                src = src_match.group(1).strip()

                # Bỏ qua các src không hợp lệ
                if not src:
                    continue

                # Chuyển đổi relative URL thành absolute URL
                if not src.startswith(('http://', 'https://')):
                    src = urljoin(base_url, src)

                # Trích xuất width và height
                width_match = re.search(r'width=["\'](.*?)["\']', iframe_tag)
                width = width_match.group(1).strip() if width_match else ""

                height_match = re.search(r'height=["\'](.*?)["\']', iframe_tag)
                height = height_match.group(1).strip() if height_match else ""

                # Xác định loại video nhúng
                video_id = ""
                embed_type = "iframe"
                direct_url = ""

                # YouTube embeds
                if "youtube.com/embed/" in src or "youtube-nocookie.com/embed/" in src:
                    embed_type = "youtube"
                    match = re.search(r"embed/([-\w]+)", src)
                    if match:
                        video_id = match.group(1)
                        direct_url = f"https://www.youtube.com/watch?v={video_id}"

                # Vimeo embeds
                elif "player.vimeo.com/video/" in src:
                    embed_type = "vimeo"
                    match = re.search(r"video/(\d+)", src)
                    if match:
                        video_id = match.group(1)
                        direct_url = f"https://vimeo.com/{video_id}"

                # Dailymotion embeds
                elif "dailymotion.com/embed/" in src:
                    embed_type = "dailymotion"
                    match = re.search(r"embed/video/([-\w]+)", src)
                    if match:
                        video_id = match.group(1)
                        direct_url = f"https://www.dailymotion.com/video/{video_id}"

                # Facebook embeds
                elif "facebook.com/plugins/video.php" in src:
                    embed_type = "facebook"

                # Các iframe khác có thể chứa video
                elif "video" in src or "player" in src or "embed" in src:
                    embed_type = "iframe"
                else:
                    # Bỏ qua iframe không liên quan đến video
                    continue

                # Thêm vào danh sách video nếu chưa tồn tại
                video_url = direct_url or src
                if not any(v['url'] == video_url for v in videos):
                    videos.append({
                        'url': video_url,
                        'embed_url': src,
                        'video_id': video_id,
                        'width': width,
                        'height': height,
                        'source_url': url,
                        'source_type': embed_type
                    })

        # Tải xuống video nếu cần
        if self.download_media and self.download_path:
            self._download_videos(videos)

        return videos

    def _extract_audio(self, url: str, content: str, page=None) -> List[Dict[str, Any]]:
        """
        Trích xuất audio từ nội dung.

        Args:
            url: URL của trang web
            content: Nội dung HTML của trang web
            page: Đối tượng Playwright Page (nếu có)

        Returns:
            List[Dict[str, Any]]: Danh sách audio
        """
        audios = []
        base_url = url

        # Trích xuất base tag
        base_match = re.search(r'<base\s+href=["\'](.*?)["\']', content, re.IGNORECASE)
        if base_match:
            base_url = base_match.group(1).strip()

        # Nếu có Playwright Page, sử dụng JavaScript để trích xuất tất cả audio
        if page and self.use_playwright:
            try:
                js_audios = page.evaluate("""() => {
                    const audios = [];

                    // Lấy tất cả thẻ audio
                    document.querySelectorAll('audio').forEach(audio => {
                        const sources = [];

                        // Lấy tất cả thẻ source trong audio
                        audio.querySelectorAll('source').forEach(source => {
                            if (source.src) {
                                sources.push({
                                    src: source.src,
                                    type: source.type || ''
                                });
                            }
                        });

                        // Nếu audio có src trực tiếp
                        if (audio.src) {
                            sources.push({
                                src: audio.src,
                                type: audio.getAttribute('type') || ''
                            });
                        }

                        if (sources.length > 0) {
                            audios.push({
                                sources: sources,
                                duration: audio.duration || '',
                                type: 'html5'
                            });
                        }
                    });

                    // Lấy tất cả iframe có thể chứa audio nhúng
                    document.querySelectorAll('iframe').forEach(iframe => {
                        const src = iframe.src || '';
                        if (src) {
                            // SoundCloud embeds
                            if (src.includes('soundcloud.com/player')) {
                                audios.push({
                                    src: src,
                                    width: iframe.width || '',
                                    height: iframe.height || '',
                                    type: 'soundcloud'
                                });
                            }
                            // Spotify embeds
                            else if (src.includes('open.spotify.com/embed')) {
                                audios.push({
                                    src: src,
                                    width: iframe.width || '',
                                    height: iframe.height || '',
                                    type: 'spotify'
                                });
                            }
                            // Các iframe khác có thể chứa audio
                            else if (src.includes('audio') || src.includes('player') || src.includes('embed')) {
                                audios.push({
                                    src: src,
                                    width: iframe.width || '',
                                    height: iframe.height || '',
                                    type: 'iframe'
                                });
                            }
                        }
                    });

                    return audios;
                }""")

                # Chuyển đổi relative URL thành absolute URL và thêm vào danh sách
                for audio in js_audios:
                    # Xử lý audio HTML5
                    if audio.get('type') == 'html5':
                        sources = audio.get('sources', [])
                        for source in sources:
                            src = source.get('src', '')
                            if not src.startswith(('http://', 'https://')):
                                src = urljoin(base_url, src)
                            source['src'] = src

                        audios.append({
                            'url': sources[0]['src'] if sources else '',
                            'sources': sources,
                            'duration': audio.get('duration', ''),
                            'source_url': url,
                            'source_type': 'html5'
                        })
                    # Xử lý audio nhúng
                    else:
                        src = audio.get('src', '')
                        if not src.startswith(('http://', 'https://')):
                            src = urljoin(base_url, src)

                        embed_type = audio.get('type', 'unknown')

                        audios.append({
                            'url': src,
                            'embed_url': src,
                            'width': audio.get('width', ''),
                            'height': audio.get('height', ''),
                            'source_url': url,
                            'source_type': embed_type
                        })
            except Exception as e:
                logger.warning(f"Lỗi khi trích xuất audio với JavaScript: {str(e)}")

        # Trích xuất audio tags từ HTML
        audio_tags = re.findall(r"<audio\b[^>]*>(.*?)</audio>", content, re.IGNORECASE | re.DOTALL)
        for audio_tag in audio_tags:
            # Trích xuất thuộc tính của thẻ audio
            audio_attrs = re.search(r"<audio\b([^>]*)>", audio_tag, re.IGNORECASE)
            if audio_attrs:
                attrs = audio_attrs.group(1)

                # Trích xuất src
                src_match = re.search(r'src=["\'](.*?)["\']', attrs)
                src = src_match.group(1).strip() if src_match else ""

                # Trích xuất các thẻ source
                sources = []
                source_tags = re.findall(r"<source\b([^>]*)>", audio_tag, re.IGNORECASE)
                for source_tag in source_tags:
                    source_src_match = re.search(r'src=["\'](.*?)["\']', source_tag)
                    if source_src_match:
                        source_src = source_src_match.group(1).strip()

                        # Chuyển đổi relative URL thành absolute URL
                        if not source_src.startswith(('http://', 'https://')):
                            source_src = urljoin(base_url, source_src)

                        # Trích xuất type
                        source_type_match = re.search(r'type=["\'](.*?)["\']', source_tag)
                        source_type = source_type_match.group(1).strip() if source_type_match else ""

                        sources.append({
                            'src': source_src,
                            'type': source_type
                        })

                # Nếu có src trực tiếp, thêm vào sources
                if src:
                    # Chuyển đổi relative URL thành absolute URL
                    if not src.startswith(('http://', 'https://')):
                        src = urljoin(base_url, src)

                    sources.append({
                        'src': src,
                        'type': ''
                    })

                # Nếu có sources, thêm vào danh sách audio
                if sources:
                    # Thêm vào danh sách audio nếu chưa tồn tại
                    audio_url = sources[0]['src']
                    if not any(a['url'] == audio_url for a in audios):
                        audios.append({
                            'url': audio_url,
                            'sources': sources,
                            'source_url': url,
                            'source_type': 'html5'
                        })

        # Trích xuất iframe có thể chứa audio nhúng
        iframe_tags = re.findall(r"<iframe\b([^>]*)>", content, re.IGNORECASE)
        for iframe_tag in iframe_tags:
            # Trích xuất src
            src_match = re.search(r'src=["\'](.*?)["\']', iframe_tag)
            if src_match:
                src = src_match.group(1).strip()

                # Bỏ qua các src không hợp lệ
                if not src:
                    continue

                # Chuyển đổi relative URL thành absolute URL
                if not src.startswith(('http://', 'https://')):
                    src = urljoin(base_url, src)

                # Trích xuất width và height
                width_match = re.search(r'width=["\'](.*?)["\']', iframe_tag)
                width = width_match.group(1).strip() if width_match else ""

                height_match = re.search(r'height=["\'](.*?)["\']', iframe_tag)
                height = height_match.group(1).strip() if height_match else ""

                # Xác định loại audio nhúng
                embed_type = "iframe"

                # SoundCloud embeds
                if "soundcloud.com/player" in src:
                    embed_type = "soundcloud"

                # Spotify embeds
                elif "open.spotify.com/embed" in src:
                    embed_type = "spotify"

                # Các iframe khác có thể chứa audio
                elif "audio" in src or "player" in src or "embed" in src:
                    embed_type = "iframe"
                else:
                    # Bỏ qua iframe không liên quan đến audio
                    continue

                # Thêm vào danh sách audio nếu chưa tồn tại
                if not any(a['url'] == src for a in audios):
                    audios.append({
                        'url': src,
                        'embed_url': src,
                        'width': width,
                        'height': height,
                        'source_url': url,
                        'source_type': embed_type
                    })

        # Tải xuống audio nếu cần
        if self.download_media and self.download_path:
            self._download_audio(audios)

        return audios

    def _download_videos(self, videos: List[Dict[str, Any]]) -> None:
        """
        Tải xuống video từ danh sách.

        Args:
            videos: Danh sách video cần tải xuống
        """
        import requests

        # Tạo thư mục lưu video nếu chưa tồn tại
        videos_dir = os.path.join(self.download_path, "videos")
        os.makedirs(videos_dir, exist_ok=True)

        for i, video in enumerate(videos):
            video_url = video['url']

            # Bỏ qua các URL không hỗ trợ tải xuống trực tiếp
            if any(domain in video_url for domain in ['youtube.com', 'vimeo.com', 'dailymotion.com', 'facebook.com']):
                video['downloaded'] = False
                video['download_error'] = "Không hỗ trợ tải xuống trực tiếp từ nền tảng này"
                continue

            try:
                # Tạo tên file
                parsed_url = urlparse(video_url)
                filename = os.path.basename(parsed_url.path)
                if not filename or "." not in filename:
                    # Tạo tên file từ hash của URL
                    filename = f"video_{hashlib.md5(video_url.encode()).hexdigest()}.mp4"

                # Tạo đường dẫn lưu file
                save_path = os.path.join(videos_dir, filename)

                # Kiểm tra nếu file đã tồn tại
                if os.path.exists(save_path):
                    # Cập nhật thông tin tải xuống
                    video['downloaded'] = True
                    video['local_path'] = save_path
                    video['file_size'] = os.path.getsize(save_path)
                    continue

                # Kiểm tra xem có thể sử dụng yt-dlp không
                try:
                    import yt_dlp

                    # Cấu hình yt-dlp
                    ydl_opts = {
                        "format": "best",
                        "outtmpl": save_path,
                        "quiet": True,
                        "no_warnings": True,
                        "ignoreerrors": True,
                        "noplaylist": True,
                    }

                    # Thực hiện tải video
                    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                        info = ydl.extract_info(video_url, download=True)

                        if info:
                            # Cập nhật thông tin tải xuống
                            video['downloaded'] = True
                            video['local_path'] = save_path
                            video['file_size'] = os.path.getsize(save_path)
                            video['duration'] = info.get('duration')
                            video['format'] = info.get('format')
                            video['width'] = info.get('width')
                            video['height'] = info.get('height')
                            continue
                except ImportError:
                    logger.warning("Thư viện yt-dlp không được cài đặt. Sử dụng phương thức tải xuống thông thường.")
                except Exception as e:
                    logger.warning(f"Lỗi khi tải xuống video với yt-dlp: {str(e)}")

                # Nếu không thể sử dụng yt-dlp, thử tải xuống trực tiếp
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
                }

                # Kiểm tra kích thước file trước khi tải
                try:
                    response = requests.head(video_url, headers=headers, timeout=30)
                    content_length = int(response.headers.get('content-length', 0))

                    if content_length > self.max_media_size_mb * 1024 * 1024:
                        logger.warning(f"Bỏ qua video quá lớn: {video_url} ({content_length / (1024 * 1024):.2f} MB > {self.max_media_size_mb} MB)")
                        video['downloaded'] = False
                        video['download_error'] = f"File quá lớn: {content_length / (1024 * 1024):.2f} MB > {self.max_media_size_mb} MB"
                        continue
                except Exception:
                    # Nếu không thể kiểm tra kích thước, tiếp tục tải
                    pass

                # Tải video
                try:
                    response = requests.get(video_url, headers=headers, timeout=60, stream=True)
                    response.raise_for_status()

                    # Kiểm tra kích thước file
                    content_length = int(response.headers.get('content-length', 0))
                    if content_length > self.max_media_size_mb * 1024 * 1024:
                        logger.warning(f"Bỏ qua video quá lớn: {video_url} ({content_length / (1024 * 1024):.2f} MB > {self.max_media_size_mb} MB)")
                        video['downloaded'] = False
                        video['download_error'] = f"File quá lớn: {content_length / (1024 * 1024):.2f} MB > {self.max_media_size_mb} MB"
                        continue

                    # Lưu video
                    with open(save_path, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            f.write(chunk)

                    # Cập nhật thông tin tải xuống
                    video['downloaded'] = True
                    video['local_path'] = save_path
                    video['file_size'] = os.path.getsize(save_path)
                except Exception as e:
                    logger.warning(f"Lỗi khi tải xuống video {video_url}: {str(e)}")
                    video['downloaded'] = False
                    video['download_error'] = str(e)

            except Exception as e:
                logger.warning(f"Lỗi khi tải xuống video {video_url}: {str(e)}")
                video['downloaded'] = False
                video['download_error'] = str(e)
        """
        Tải xuống audio từ danh sách.

        Args:
            audios: Danh sách audio cần tải xuống
        """
        import requests

        # Tạo thư mục lưu audio nếu chưa tồn tại
        audios_dir = os.path.join(self.download_path, "audios")
        os.makedirs(audios_dir, exist_ok=True)

        for i, audio in enumerate(audios):
            audio_url = audio['url']

            # Bỏ qua các URL không hỗ trợ tải xuống trực tiếp
            if any(domain in audio_url for domain in ['soundcloud.com', 'spotify.com', 'apple.com/music']):
                audio['downloaded'] = False
                audio['download_error'] = "Không hỗ trợ tải xuống trực tiếp từ nền tảng này"
                continue

            try:
                # Tạo tên file
                parsed_url = urlparse(audio_url)
                filename = os.path.basename(parsed_url.path)
                if not filename or "." not in filename:
                    # Tạo tên file từ hash của URL
                    filename = f"audio_{hashlib.md5(audio_url.encode()).hexdigest()}.mp3"

                # Tạo đường dẫn lưu file
                save_path = os.path.join(audios_dir, filename)

                # Kiểm tra nếu file đã tồn tại
                if os.path.exists(save_path):
                    # Cập nhật thông tin tải xuống
                    audio['downloaded'] = True
                    audio['local_path'] = save_path
                    audio['file_size'] = os.path.getsize(save_path)
                    continue

                # Kiểm tra xem có thể sử dụng yt-dlp không
                try:
                    import yt_dlp

                    # Cấu hình yt-dlp
                    ydl_opts = {
                        "format": "bestaudio/best",
                        "outtmpl": save_path,
                        "quiet": True,
                        "no_warnings": True,
                        "ignoreerrors": True,
                        "noplaylist": True,
                        "postprocessors": [{
                            "key": "FFmpegExtractAudio",
                            "preferredcodec": "mp3",
                            "preferredquality": "192",
                        }]
                    }

                    # Thực hiện tải audio
                    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                        info = ydl.extract_info(audio_url, download=True)

                        if info:
                            # Cập nhật thông tin tải xuống
                            audio['downloaded'] = True
                            audio['local_path'] = save_path
                            audio['file_size'] = os.path.getsize(save_path)
                            audio['duration'] = info.get('duration')
                            audio['format'] = info.get('format')
                            continue
                except ImportError:
                    logger.warning("Thư viện yt-dlp không được cài đặt. Sử dụng phương thức tải xuống thông thường.")
                except Exception as e:
                    logger.warning(f"Lỗi khi tải xuống audio với yt-dlp: {str(e)}")

                # Nếu không thể sử dụng yt-dlp, thử tải xuống trực tiếp
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
                }

                # Kiểm tra kích thước file trước khi tải
                try:
                    response = requests.head(audio_url, headers=headers, timeout=30)
                    content_length = int(response.headers.get('content-length', 0))

                    if content_length > self.max_media_size_mb * 1024 * 1024:
                        logger.warning(f"Bỏ qua audio quá lớn: {audio_url} ({content_length / (1024 * 1024):.2f} MB > {self.max_media_size_mb} MB)")
                        audio['downloaded'] = False
                        audio['download_error'] = f"File quá lớn: {content_length / (1024 * 1024):.2f} MB > {self.max_media_size_mb} MB"
                        continue
                except Exception:
                    # Nếu không thể kiểm tra kích thước, tiếp tục tải
                    pass

                # Tải audio
                try:
                    response = requests.get(audio_url, headers=headers, timeout=60, stream=True)
                    response.raise_for_status()

                    # Kiểm tra kích thước file
                    content_length = int(response.headers.get('content-length', 0))
                    if content_length > self.max_media_size_mb * 1024 * 1024:
                        logger.warning(f"Bỏ qua audio quá lớn: {audio_url} ({content_length / (1024 * 1024):.2f} MB > {self.max_media_size_mb} MB)")
                        audio['downloaded'] = False
                        audio['download_error'] = f"File quá lớn: {content_length / (1024 * 1024):.2f} MB > {self.max_media_size_mb} MB"
                        continue

                    # Lưu audio
                    with open(save_path, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            f.write(chunk)

                    # Cập nhật thông tin tải xuống
                    audio['downloaded'] = True
                    audio['local_path'] = save_path
                    audio['file_size'] = os.path.getsize(save_path)
                except Exception as e:
                    logger.warning(f"Lỗi khi tải xuống audio {audio_url}: {str(e)}")
                    audio['downloaded'] = False
                    audio['download_error'] = str(e)

            except Exception as e:
                logger.warning(f"Lỗi khi tải xuống audio {audio_url}: {str(e)}")
                audio['downloaded'] = False
                audio['download_error'] = str(e)

                # Kiểm tra xem có thể sử dụng yt-dlp không
                try:
                    import yt_dlp

                    # Cấu hình yt-dlp
                    ydl_opts = {
                        "format": "best",
                        "outtmpl": save_path,
                        "quiet": True,
                        "no_warnings": True,
                        "ignoreerrors": True,
                        "noplaylist": True,
                    }

                    # Thực hiện tải video
                    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                        info = ydl.extract_info(video_url, download=True)

                        if info:
                            # Cập nhật thông tin tải xuống
                            video['downloaded'] = True
                            video['local_path'] = save_path
                            video['file_size'] = os.path.getsize(save_path)
                            video['duration'] = info.get('duration')
                            video['format'] = info.get('format')
                            video['width'] = info.get('width')
                            video['height'] = info.get('height')
                            continue
                except ImportError:
                    logger.warning("Thư viện yt-dlp không được cài đặt. Sử dụng phương thức tải xuống thông thường.")
                except Exception as e:
                    logger.warning(f"Lỗi khi tải xuống video với yt-dlp: {str(e)}")

                # Nếu không thể sử dụng yt-dlp, thử tải xuống trực tiếp
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
                }

                # Kiểm tra kích thước file trước khi tải
                try:
                    response = requests.head(video_url, headers=headers, timeout=30)
                    content_length = int(response.headers.get('content-length', 0))

                    if content_length > self.max_media_size_mb * 1024 * 1024:
                        logger.warning(f"Bỏ qua video quá lớn: {video_url} ({content_length / (1024 * 1024):.2f} MB > {self.max_media_size_mb} MB)")
                        video['downloaded'] = False
                        video['download_error'] = f"File quá lớn: {content_length / (1024 * 1024):.2f} MB > {self.max_media_size_mb} MB"
                        continue
                except Exception:
                    # Nếu không thể kiểm tra kích thước, tiếp tục tải
                    pass

                # Tải video
                try:
                    response = requests.get(video_url, headers=headers, timeout=60, stream=True)
                    response.raise_for_status()

                    # Kiểm tra kích thước file
                    content_length = int(response.headers.get('content-length', 0))
                    if content_length > self.max_media_size_mb * 1024 * 1024:
                        logger.warning(f"Bỏ qua video quá lớn: {video_url} ({content_length / (1024 * 1024):.2f} MB > {self.max_media_size_mb} MB)")
                        video['downloaded'] = False
                        video['download_error'] = f"File quá lớn: {content_length / (1024 * 1024):.2f} MB > {self.max_media_size_mb} MB"
                        continue

                    # Lưu video
                    with open(save_path, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            f.write(chunk)

                    # Cập nhật thông tin tải xuống
                    video['downloaded'] = True
                    video['local_path'] = save_path
                    video['file_size'] = os.path.getsize(save_path)
                except Exception as e:
                    logger.warning(f"Lỗi khi tải xuống video {video_url}: {str(e)}")
                    video['downloaded'] = False
                    video['download_error'] = str(e)

            except Exception as e:
                logger.warning(f"Lỗi khi tải xuống video {video_url}: {str(e)}")
                video['downloaded'] = False
                video['download_error'] = str(e)

    def _download_audio(self, audios: List[Dict[str, Any]]) -> None:
        """
        Tải xuống audio từ danh sách.

        Args:
            audios: Danh sách audio cần tải xuống
        """
        import requests

        # Tạo thư mục lưu audio nếu chưa tồn tại
        audios_dir = os.path.join(self.download_path, "audios")
        os.makedirs(audios_dir, exist_ok=True)

        for i, audio in enumerate(audios):
            audio_url = audio['url']

            # Bỏ qua các URL không hỗ trợ tải xuống trực tiếp
            if any(domain in audio_url for domain in ['soundcloud.com', 'spotify.com', 'apple.com/music']):
                audio['downloaded'] = False
                audio['download_error'] = "Không hỗ trợ tải xuống trực tiếp từ nền tảng này"
                continue

            try:
                # Tạo tên file
                parsed_url = urlparse(audio_url)
                filename = os.path.basename(parsed_url.path)
                if not filename or "." not in filename:
                    # Tạo tên file từ hash của URL
                    filename = f"audio_{hashlib.md5(audio_url.encode()).hexdigest()}.mp3"

                # Tạo đường dẫn lưu file
                save_path = os.path.join(audios_dir, filename)

                # Kiểm tra nếu file đã tồn tại
                if os.path.exists(save_path):
                    # Cập nhật thông tin tải xuống
                    audio['downloaded'] = True
                    audio['local_path'] = save_path
                    audio['file_size'] = os.path.getsize(save_path)
                    continue

                # Kiểm tra xem có thể sử dụng yt-dlp không
                try:
                    import yt_dlp

                    # Cấu hình yt-dlp
                    ydl_opts = {
                        "format": "bestaudio/best",
                        "outtmpl": save_path,
                        "quiet": True,
                        "no_warnings": True,
                        "ignoreerrors": True,
                        "noplaylist": True,
                        "postprocessors": [{
                            "key": "FFmpegExtractAudio",
                            "preferredcodec": "mp3",
                            "preferredquality": "192",
                        }]
                    }

                    # Thực hiện tải audio
                    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                        info = ydl.extract_info(audio_url, download=True)

                        if info:
                            # Cập nhật thông tin tải xuống
                            audio['downloaded'] = True
                            audio['local_path'] = save_path
                            audio['file_size'] = os.path.getsize(save_path)
                            audio['duration'] = info.get('duration')
                            audio['format'] = info.get('format')
                            continue
                except ImportError:
                    logger.warning("Thư viện yt-dlp không được cài đặt. Sử dụng phương thức tải xuống thông thường.")
                except Exception as e:
                    logger.warning(f"Lỗi khi tải xuống audio với yt-dlp: {str(e)}")

                # Nếu không thể sử dụng yt-dlp, thử tải xuống trực tiếp
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
                }

                # Kiểm tra kích thước file trước khi tải
                try:
                    response = requests.head(audio_url, headers=headers, timeout=30)
                    content_length = int(response.headers.get('content-length', 0))

                    if content_length > self.max_media_size_mb * 1024 * 1024:
                        logger.warning(f"Bỏ qua audio quá lớn: {audio_url} ({content_length / (1024 * 1024):.2f} MB > {self.max_media_size_mb} MB)")
                        audio['downloaded'] = False
                        audio['download_error'] = f"File quá lớn: {content_length / (1024 * 1024):.2f} MB > {self.max_media_size_mb} MB"
                        continue
                except Exception:
                    # Nếu không thể kiểm tra kích thước, tiếp tục tải
                    pass

                # Tải audio
                try:
                    response = requests.get(audio_url, headers=headers, timeout=60, stream=True)
                    response.raise_for_status()

                    # Kiểm tra kích thước file
                    content_length = int(response.headers.get('content-length', 0))
                    if content_length > self.max_media_size_mb * 1024 * 1024:
                        logger.warning(f"Bỏ qua audio quá lớn: {audio_url} ({content_length / (1024 * 1024):.2f} MB > {self.max_media_size_mb} MB)")
                        audio['downloaded'] = False
                        audio['download_error'] = f"File quá lớn: {content_length / (1024 * 1024):.2f} MB > {self.max_media_size_mb} MB"
                        continue

                    # Lưu audio
                    with open(save_path, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            f.write(chunk)

                    # Cập nhật thông tin tải xuống
                    audio['downloaded'] = True
                    audio['local_path'] = save_path
                    audio['file_size'] = os.path.getsize(save_path)
                except Exception as e:
                    logger.warning(f"Lỗi khi tải xuống audio {audio_url}: {str(e)}")
                    audio['downloaded'] = False
                    audio['download_error'] = str(e)

            except Exception as e:
                logger.warning(f"Lỗi khi tải xuống audio {audio_url}: {str(e)}")
                audio['downloaded'] = False
                audio['download_error'] = str(e)

    def _extract_files(self, url: str, content: str, page=None) -> List[Dict[str, Any]]:
        """
        Trích xuất files từ nội dung.

        Args:
            url: URL của trang web
            content: Nội dung HTML của trang web
            page: Đối tượng Playwright Page (nếu có)

        Returns:
            List[Dict[str, Any]]: Danh sách files
        """
        files = []
        base_url = url

        # Trích xuất base tag
        base_match = re.search(r'<base\s+href=["\'](.*?)["\']', content, re.IGNORECASE)
        if base_match:
            base_url = base_match.group(1).strip()

        # Các phần mở rộng file phổ biến (mở rộng)
        file_extensions = [
            # Văn bản và tài liệu
            ".pdf", ".doc", ".docx", ".rtf", ".odt", ".txt", ".md", ".markdown",

            # Bảng tính
            ".xls", ".xlsx", ".ods", ".csv", ".tsv",

            # Trình chiếu
            ".ppt", ".pptx", ".odp", ".key",

            # Sách điện tử
            ".epub", ".mobi", ".azw", ".azw3",

            # Hình ảnh vector và thiết kế
            ".ai", ".eps", ".svg", ".sketch", ".xd", ".psd", ".indd",

            # Mã nguồn và lập trình
            ".java", ".py", ".js", ".html", ".css", ".php", ".c", ".cpp", ".h",
            ".cs", ".rb", ".go", ".rs", ".swift", ".kt", ".ts", ".jsx", ".tsx",

            # Dữ liệu và cấu hình
            ".json", ".xml", ".yaml", ".yml", ".toml", ".ini", ".conf", ".cfg",

            # Nén và lưu trữ
            ".zip", ".rar", ".7z", ".tar", ".gz", ".bz2", ".xz", ".tgz",

            # Cơ sở dữ liệu
            ".sql", ".db", ".sqlite", ".mdb", ".accdb",

            # CAD và 3D
            ".dwg", ".dxf", ".obj", ".stl", ".blend",

            # Âm thanh
            ".mp3", ".wav", ".ogg", ".flac", ".aac", ".m4a", ".wma",

            # Video
            ".mp4", ".avi", ".mov", ".wmv", ".mkv", ".webm", ".flv", ".m4v",

            # Khác
            ".tex", ".bib", ".log", ".dat", ".bin", ".iso", ".dmg", ".apk", ".ipa"
        ]

        # Nếu có Playwright Page, sử dụng JavaScript để trích xuất tất cả links
        if page and self.use_playwright:
            try:
                js_files = page.evaluate("""(fileExtensions) => {
                    const files = [];
                    const links = Array.from(document.querySelectorAll('a[href]'));

                    for (const link of links) {
                        const href = link.href;
                        if (!href || href.startsWith('#') || href.startsWith('javascript:')) {
                            continue;
                        }

                        // Kiểm tra phần mở rộng
                        const hasValidExtension = fileExtensions.some(ext =>
                            href.toLowerCase().endsWith(ext)
                        );

                        if (hasValidExtension) {
                            files.push({
                                url: href,
                                text: link.textContent.trim(),
                                title: link.title || '',
                                download: link.hasAttribute('download'),
                                download_filename: link.getAttribute('download') || ''
                            });
                        }
                    }

                    return files;
                }""", file_extensions)

                # Xử lý kết quả
                for file_info in js_files:
                    file_url = file_info['url']

                    # Phân tích URL để lấy tên file
                    parsed_url = urlparse(file_url)
                    path = parsed_url.path
                    filename = path.split("/")[-1]

                    # Xác định loại file
                    file_type = "unknown"
                    for ext in file_extensions:
                        if filename.lower().endswith(ext):
                            file_type = ext[1:]  # Bỏ dấu chấm
                            break

                    # Thêm vào danh sách files nếu chưa tồn tại
                    if not any(f['url'] == file_url for f in files):
                        files.append({
                            'url': file_url,
                            'filename': filename,
                            'text': file_info['text'],
                            'title': file_info['title'],
                            'type': file_type,
                            'source_url': url,
                            'download_attr': file_info['download'],
                            'download_filename': file_info['download_filename']
                        })
            except Exception as e:
                logger.warning(f"Lỗi khi trích xuất files với JavaScript: {str(e)}")

        # Trích xuất a tags từ HTML
        a_tags = re.findall(r"<a\s+([^>]*)>(.*?)</a>", content, re.IGNORECASE | re.DOTALL)
        for a_tag, a_text in a_tags:
            href_match = re.search(r'href=["\'](.*?)["\']', a_tag)
            if href_match:
                href = href_match.group(1).strip()

                # Bỏ qua các href không hợp lệ
                if not href or href.startswith("#") or href.startswith("javascript:"):
                    continue

                # Kiểm tra phần mở rộng
                if not any(href.lower().endswith(ext) for ext in file_extensions):
                    continue

                # Chuyển đổi relative URL thành absolute URL
                if not href.startswith(("http://", "https://")):
                    href = urljoin(base_url, href)

                # Trích xuất text
                text = re.sub(r"<[^>]*>", "", a_text).strip()

                # Trích xuất title
                title_match = re.search(r'title=["\'](.*?)["\']', a_tag)
                title = title_match.group(1).strip() if title_match else ""

                # Trích xuất download attribute
                download_match = re.search(r'download(?:=["\'](.*?)["\'])?', a_tag)
                download_attr = bool(download_match)
                download_filename = download_match.group(1).strip() if download_match and download_match.group(1) else ""

                # Phân tích URL để lấy tên file
                parsed_url = urlparse(href)
                path = parsed_url.path
                filename = path.split("/")[-1]

                # Xác định loại file
                file_type = "unknown"
                for ext in file_extensions:
                    if filename.lower().endswith(ext):
                        file_type = ext[1:]  # Bỏ dấu chấm
                        break

                # Thêm vào danh sách files nếu chưa tồn tại
                if not any(f['url'] == href for f in files):
                    files.append({
                        'url': href,
                        'filename': filename,
                        'text': text,
                        'title': title,
                        'type': file_type,
                        'source_url': url,
                        'download_attr': download_attr,
                        'download_filename': download_filename
                    })

        # Tải xuống files nếu cần
        if self.download_media and self.download_path:
            self._download_files(files)

        return files

    def _extract_file_content(self, file_path: str, mime_type: Optional[str] = None) -> Dict[str, Any]:
        """
        Trích xuất nội dung từ file.

        Args:
            file_path: Đường dẫn đến file
            mime_type: Kiểu MIME của file (nếu biết)

        Returns:
            Dict[str, Any]: Kết quả trích xuất
        """
        result = {
            "success": False,
            "text": "",
            "metadata": {},
            "error": None
        }

        if not self.document_extractor:
            result["error"] = "Document extractor không được khởi tạo"
            return result

        if not os.path.exists(file_path):
            result["error"] = f"File không tồn tại: {file_path}"
            return result

        try:
            # Xác định kiểu MIME nếu không được cung cấp
            if not mime_type:
                import mimetypes
                mime_type, _ = mimetypes.guess_type(file_path)

            # Mở file và trích xuất nội dung
            with open(file_path, 'rb') as f:
                extraction_result = self.document_extractor.extract_text(
                    f,
                    mime_type=mime_type,
                    filename=os.path.basename(file_path)
                )

                # Cập nhật kết quả
                result["success"] = extraction_result.get("success", False)
                result["text"] = extraction_result.get("text", "")
                result["metadata"] = {
                    "mime_type": mime_type,
                    "file_size": os.path.getsize(file_path),
                    "file_name": os.path.basename(file_path)
                }

                if not result["success"]:
                    result["error"] = extraction_result.get("error", "Không thể trích xuất nội dung")

        except Exception as e:
            result["error"] = str(e)
            logger.warning(f"Lỗi khi trích xuất nội dung từ file {file_path}: {str(e)}")

        return result

    def _download_files(self, files: List[Dict[str, Any]]) -> None:
        """
        Tải xuống files từ danh sách.

        Args:
            files: Danh sách files cần tải xuống
        """
        import requests
        import mimetypes

        # Tạo thư mục lưu files nếu chưa tồn tại
        files_dir = os.path.join(self.download_path, "files")
        os.makedirs(files_dir, exist_ok=True)

        for i, file_info in enumerate(files):
            file_url = file_info['url']

            # Bỏ qua các URL không hỗ trợ tải xuống trực tiếp
            if any(domain in file_url for domain in ['drive.google.com', 'docs.google.com']):
                file_info['downloaded'] = False
                file_info['download_error'] = "Không hỗ trợ tải xuống trực tiếp từ Google Drive/Docs"
                continue

            try:
                # Tạo tên file
                filename = file_info.get('filename', '')
                if not filename or "." not in filename:
                    # Tạo tên file từ hash của URL
                    parsed_url = urlparse(file_url)
                    path = parsed_url.path
                    filename = os.path.basename(path)
                    if not filename or "." not in filename:
                        # Sử dụng download_filename nếu có
                        if file_info.get('download_filename'):
                            filename = file_info['download_filename']
                        else:
                            # Tạo tên file từ hash của URL và loại file
                            file_type = file_info.get('type', 'unknown')
                            filename = f"file_{hashlib.md5(file_url.encode()).hexdigest()}.{file_type}"

                # Tạo đường dẫn lưu file
                save_path = os.path.join(files_dir, filename)

                # Kiểm tra nếu file đã tồn tại
                if os.path.exists(save_path):
                    # Cập nhật thông tin tải xuống
                    file_info['downloaded'] = True
                    file_info['local_path'] = save_path
                    file_info['file_size'] = os.path.getsize(save_path)
                    continue

                # Tải file
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
                }

                # Kiểm tra kích thước file trước khi tải
                try:
                    response = requests.head(file_url, headers=headers, timeout=30)
                    content_length = int(response.headers.get('content-length', 0))

                    if content_length > self.max_media_size_mb * 1024 * 1024:
                        logger.warning(f"Bỏ qua file quá lớn: {file_url} ({content_length / (1024 * 1024):.2f} MB > {self.max_media_size_mb} MB)")
                        file_info['downloaded'] = False
                        file_info['download_error'] = f"File quá lớn: {content_length / (1024 * 1024):.2f} MB > {self.max_media_size_mb} MB"
                        continue

                    # Lấy content-type để xác định loại file
                    content_type = response.headers.get('content-type', '')
                    if content_type and 'html' not in content_type and '.' not in filename:
                        # Thêm phần mở rộng dựa trên content-type
                        ext = mimetypes.guess_extension(content_type)
                        if ext:
                            filename = f"{os.path.splitext(filename)[0]}{ext}"
                            save_path = os.path.join(files_dir, filename)
                except Exception:
                    # Nếu không thể kiểm tra kích thước, tiếp tục tải
                    pass

                # Tải file
                try:
                    response = requests.get(file_url, headers=headers, timeout=60, stream=True)
                    response.raise_for_status()

                    # Kiểm tra kích thước file
                    content_length = int(response.headers.get('content-length', 0))
                    if content_length > self.max_media_size_mb * 1024 * 1024:
                        logger.warning(f"Bỏ qua file quá lớn: {file_url} ({content_length / (1024 * 1024):.2f} MB > {self.max_media_size_mb} MB)")
                        file_info['downloaded'] = False
                        file_info['download_error'] = f"File quá lớn: {content_length / (1024 * 1024):.2f} MB > {self.max_media_size_mb} MB"
                        continue

                    # Lưu file
                    with open(save_path, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            f.write(chunk)

                    # Cập nhật thông tin tải xuống
                    file_info['downloaded'] = True
                    file_info['local_path'] = save_path
                    file_info['file_size'] = os.path.getsize(save_path)

                    # Cập nhật loại file nếu cần
                    if file_info['type'] == 'unknown':
                        content_type = response.headers.get('content-type', '')
                        if content_type:
                            file_info['content_type'] = content_type

                    # Trích xuất nội dung file nếu cần
                    if self.extract_file_content and self.document_extractor:
                        content_type = file_info.get('content_type', '')
                        extraction_result = self._extract_file_content(save_path, content_type)
                        if extraction_result['success']:
                            file_info['extracted_content'] = extraction_result['text']
                            file_info['content_extraction_success'] = True
                            if extraction_result.get('metadata'):
                                file_info['content_metadata'] = extraction_result['metadata']
                        else:
                            file_info['content_extraction_success'] = False
                            file_info['content_extraction_error'] = extraction_result.get('error', 'Không thể trích xuất nội dung')

                except Exception as e:
                    logger.warning(f"Lỗi khi tải xuống file {file_url}: {str(e)}")
                    file_info['downloaded'] = False
                    file_info['download_error'] = str(e)

            except Exception as e:
                logger.warning(f"Lỗi khi tải xuống file {file_url}: {str(e)}")
                file_info['downloaded'] = False
                file_info['download_error'] = str(e)