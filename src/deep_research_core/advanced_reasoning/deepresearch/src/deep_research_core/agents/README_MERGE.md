# Hướng dẫn merge WebSearchAgent

## Tổng quan

Tài liệu này mô tả quá trình merge code giữa các phiên bản khác nhau của WebSearchAgent:
1. WebSearchAgent (web_search_agent.py) - Phiên bản gốc
2. ImprovedWebSearchAgent (web_search_agent_improved.py) - Phiên bản cải tiến
3. WebSearchAgentMerged (web_search_agent_merged.py) - Phiên bản đã merge

## Vấn đề cần giải quyết

Phiên bản gốc của WebSearchAgent có một số vấn đề:
- Lỗi 'int' object is not subscriptable khi khởi tạo các thuộc tính từ điển
- Thiếu các cơ chế xử lý lỗi và fallback
- Thiếu các tính năng nâng cao như adaptive scraping, CAPTCHA handling, v.v.

## G<PERSON><PERSON><PERSON> pháp

Chúng tôi đã tạo một phiên bản mới (WebSearchAgentMerged) kết hợp các tính năng tốt nhất từ cả hai phiên bản:

1. **Khởi tạo đúng các thuộc tính từ điển**:
   - Khởi tạo các thuộc tính như `engine_request_counts`, `engine_reset_times`, và `cache` trước khi gọi `super().__init__()`
   - Kiểm tra kiểu dữ liệu trước khi sử dụng
   - Khởi tạo lại các thuộc tính nếu chúng không tồn tại hoặc có kiểu dữ liệu không đúng

2. **Cải thiện xử lý lỗi và cơ chế fallback**:
   - Thêm các cơ chế xử lý lỗi chi tiết
   - Thêm cơ chế fallback để chuyển đổi giữa các công cụ tìm kiếm khi một công cụ thất bại
   - Thêm cơ chế retry với backoff

3. **Tích hợp các tính năng từ cả hai phiên bản**:
   - Adaptive scraping từ ImprovedWebSearchAgent
   - Xử lý CAPTCHA từ ImprovedWebSearchAgent
   - Rate limiting nâng cao từ cả hai phiên bản
   - Caching thông minh từ WebSearchAgent

## Cấu trúc file

- **web_search_agent_merged.py**: Phiên bản đã merge
- **test_web_search_agent_merged.py**: File test cho phiên bản đã merge
- **caching.py**: Module caching được sử dụng bởi WebSearchAgentMerged

## Cách sử dụng

```python
from deep_research_core.agents.web_search_agent_merged import WebSearchAgentMerged

# Khởi tạo agent
agent = WebSearchAgentMerged(
    config={
        "use_cache": True,
        "verbose": True,
        "api_search_config": {
            "engine": "duckduckgo",
            "language": "en"
        },
        "crawlee_search_config": {
            "max_depth": 2,
            "max_pages": 10
        }
    }
)

# Tìm kiếm
results = agent.search(
    query="python programming",
    num_results=5,
    language="auto",
    method="api"  # Có thể là "api", "crawlee", "direct_crawlee", hoặc "auto"
)

# Trích xuất nội dung
content_results = agent.search(
    query="python programming",
    num_results=5,
    get_content=True  # Tự động trích xuất nội dung từ các kết quả
)

# Phân tích truy vấn
analysis = agent.analyze_query("what is python programming?")
```

## Các cải tiến chính

1. **Khởi tạo an toàn**:
   ```python
   def _initialize_core_attributes(self):
       """Khởi tạo các thuộc tính cốt lõi trước khi gọi super().__init__"""
       # Engine config
       self.api_search_config = {...}
       self.crawlee_search_config = {...}

       # Rate limiting - đảm bảo dữ liệu là dict
       self.engine_request_counts = {...}
       self.engine_reset_times = {...}
       self.engine_rate_limits = {...}
   ```

2. **Xử lý lỗi và fallback**:
   ```python
   def search(self, query, engine=None, num_results=10, language="auto", method=None, get_content=False, force_refresh=False, **kwargs):
       # Determine which search method to use
       if method == "api" or (method is None and not get_content):
           result = self._search_api(query, num_results=num_results, language=language, engine=engine, **kwargs)
       elif method == "crawlee" or (method is None and get_content):
           result = self._search_crawlee(query, num_results=num_results, language=language, **kwargs)
       elif method == "direct_crawlee":
           result = self._search_direct_crawlee(query, num_results=num_results, **kwargs)
       else:
           # Default to API search
           result = self._search_api(query, num_results=num_results, language=language, engine=engine, **kwargs)
   ```

3. **Trích xuất nội dung thông minh**:
   ```python
   def _extract_content_from_results(self, search_results):
       # Extract content for each result
       for i, result in enumerate(results["results"]):
           if "url" in result:
               try:
                   # Extract content
                   content = self.extract_content(result["url"])
                   
                   # Add content to result
                   results["results"][i]["content"] = content
               except Exception as e:
                   logger.error(f"Content extraction error: {str(e)}")
   ```

## Kết luận

WebSearchAgentMerged là phiên bản cải tiến kết hợp các tính năng tốt nhất từ cả hai phiên bản trước đó. Nó giải quyết các vấn đề trong phiên bản gốc và thêm nhiều tính năng mới. Chúng tôi khuyến nghị sử dụng WebSearchAgentMerged cho tất cả các dự án mới.
