"""
Academic search methods for Deep Research Core.

This module provides functions to search academic sources such as
Google Scholar, Semantic Scholar, arXiv, PubMed, etc.
"""

import re
import time
import json
import urllib.parse
import requests
from typing import Dict, Any, List, Optional
from bs4 import BeautifulSoup
from ..utils.structured_logging import get_logger

# Set up logger
logger = get_logger(__name__)

def search_google_scholar(query: str, num_results: int = 10) -> Dict[str, Any]:
    """
    Search Google Scholar for academic papers.
    
    Args:
        query: The query to search for
        num_results: Number of results to return
        
    Returns:
        Dictionary containing search results
    """
    logger.info(f"Searching Google Scholar for: {query}")
    
    try:
        # URL encode the query
        encoded_query = urllib.parse.quote_plus(query)
        
        # Build URL
        url = f"https://scholar.google.com/scholar?q={encoded_query}&hl=en&as_sdt=0,5&num={num_results}"
        
        # Setup headers to mimic a browser
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1"
        }
        
        # Make the request
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        # Parse the HTML response
        soup = BeautifulSoup(response.text, "html.parser")
        
        # Extract search results
        results = []
        
        # Check if we're blocked or faced with a CAPTCHA
        if "Please show you're not a robot" in response.text:
            logger.warning("Google Scholar returned a CAPTCHA")
            return {
                "success": False,
                "error": "Google Scholar returned a CAPTCHA",
                "results": []
            }
            
        # Google Scholar specific selectors
        result_elements = soup.select(".gs_r.gs_or.gs_scl")
        
        for element in result_elements[:num_results]:
            # Get title and URL
            title_element = element.select_one(".gs_rt a")
            if not title_element:
                continue
            
            title = title_element.get_text().strip()
            url = title_element.get("href", "")
            
            # Get snippet/abstract
            snippet_element = element.select_one(".gs_rs")
            snippet = snippet_element.get_text().strip() if snippet_element else ""
            
            # Get authors, publication, year
            pub_element = element.select_one(".gs_a")
            publication_info = pub_element.get_text().strip() if pub_element else ""
            
            # Extract year using regex
            year_match = re.search(r'\b(19|20)\d{2}\b', publication_info)
            year = year_match.group(0) if year_match else ""
            
            # Extract authors
            authors = ""
            if pub_element:
                authors_text = publication_info.split("-")[0] if "-" in publication_info else ""
                authors = authors_text.strip()
            
            # Add to results
            result = {
                "title": title,
                "url": url,
                "snippet": snippet,
                "publication_info": publication_info,
                "authors": authors,
                "year": year,
                "source": "google_scholar"
            }
            results.append(result)
        
        # If no results were found, return an error
        if not results:
            logger.warning(f"No Google Scholar results found for query: {query}")
            return {
                "success": False,
                "error": "No results found",
                "results": []
            }
        
        return {
            "success": True,
            "results": results,
            "engine": "google_scholar",
            "source": "google_scholar"
        }
        
    except Exception as e:
        logger.error(f"Error searching Google Scholar: {str(e)}")
        return {
            "success": False,
            "error": f"Google Scholar search failed: {str(e)}",
            "results": []
        }

def search_semantic_scholar(query: str, num_results: int = 10) -> Dict[str, Any]:
    """
    Search Semantic Scholar for academic papers using their API.
    
    Args:
        query: The query to search for
        num_results: Number of results to return
        
    Returns:
        Dictionary containing search results
    """
    logger.info(f"Searching Semantic Scholar for: {query}")
    
    try:
        # Build API URL
        api_url = "https://api.semanticscholar.org/graph/v1/paper/search"
        
        # Set up parameters
        params = {
            "query": query,
            "limit": num_results,
            "fields": "title,url,abstract,authors,venue,year,citationCount,openAccessPdf"
        }
        
        # Setup headers
        headers = {
            "Accept": "application/json"
        }
        
        # Make the request
        response = requests.get(api_url, params=params, headers=headers, timeout=15)
        response.raise_for_status()
        
        # Parse the JSON response
        data = response.json()
        
        # Extract search results
        results = []
        papers = data.get("data", [])
        
        for paper in papers:
            # Extract authors
            authors_list = paper.get("authors", [])
            authors = ", ".join([author.get("name", "") for author in authors_list if "name" in author])
            
            # Create result object
            result = {
                "title": paper.get("title", ""),
                "url": f"https://www.semanticscholar.org/paper/{paper.get('paperId', '')}" if paper.get("paperId") else "",
                "snippet": paper.get("abstract", ""),
                "authors": authors,
                "year": paper.get("year"),
                "venue": paper.get("venue", ""),
                "citation_count": paper.get("citationCount"),
                "open_access_pdf": paper.get("openAccessPdf", {}).get("url", ""),
                "source": "semantic_scholar"
            }
            results.append(result)
        
        # If no results were found, return an error
        if not results:
            logger.warning(f"No Semantic Scholar results found for query: {query}")
            return {
                "success": False,
                "error": "No results found on Semantic Scholar",
                "results": []
            }
        
        return {
            "success": True,
            "results": results,
            "engine": "semantic_scholar",
            "source": "semantic_scholar"
        }
        
    except Exception as e:
        logger.error(f"Error searching Semantic Scholar: {str(e)}")
        return {
            "success": False,
            "error": f"Semantic Scholar search failed: {str(e)}",
            "results": []
        }

def search_arxiv(query: str, num_results: int = 10) -> Dict[str, Any]:
    """
    Search arXiv for academic papers using their API.
    
    Args:
        query: The query to search for
        num_results: Number of results to return
        
    Returns:
        Dictionary containing search results
    """
    logger.info(f"Searching arXiv for: {query}")
    
    try:
        # URL encode the query
        encoded_query = urllib.parse.quote_plus(query)
        
        # Build API URL
        api_url = f"http://export.arxiv.org/api/query?search_query=all:{encoded_query}&start=0&max_results={num_results}"
        
        # Make the request
        response = requests.get(api_url, timeout=15)
        response.raise_for_status()
        
        # Parse the XML response
        soup = BeautifulSoup(response.text, "xml")
        if soup.find("entry") is None:
            # If XML parser fails, try using HTML parser as fallback
            soup = BeautifulSoup(response.text, "html.parser")
        
        # Extract search results
        results = []
        entries = soup.find_all("entry")
        
        for entry in entries:
            # Extract authors
            authors_elements = entry.find_all("author")
            authors = ", ".join([author.find("name").text for author in authors_elements if author.find("name")])
            
            # Extract title, removing newlines and extra spaces
            title_element = entry.find("title")
            title = " ".join(title_element.text.split()) if title_element else ""
            
            # Extract summary/abstract, removing newlines and extra spaces
            summary_element = entry.find("summary")
            summary = " ".join(summary_element.text.split()) if summary_element else ""
            
            # Extract URL
            url_element = entry.find("id")
            url = url_element.text if url_element else ""
            
            # Extract publication date
            published_element = entry.find("published")
            published = published_element.text if published_element else ""
            
            # Extract year using regex
            year_match = re.search(r'\b(19|20)\d{2}\b', published)
            year = year_match.group(0) if year_match else ""
            
            # Create result object
            result = {
                "title": title,
                "url": url,
                "snippet": summary,
                "authors": authors,
                "published": published,
                "year": year,
                "source": "arxiv"
            }
            results.append(result)
        
        # If no results were found, return an error
        if not results:
            # Try alternative method: scrape the arXiv website
            return _search_arxiv_scrape(query, num_results)
        
        return {
            "success": True,
            "results": results,
            "engine": "arxiv",
            "source": "arxiv"
        }
        
    except Exception as e:
        logger.error(f"Error searching arXiv API: {str(e)}")
        # Try alternative method: scrape the arXiv website
        return _search_arxiv_scrape(query, num_results)

def _search_arxiv_scrape(query: str, num_results: int = 10) -> Dict[str, Any]:
    """
    Fallback method to search arXiv by scraping the website.
    
    Args:
        query: The query to search for
        num_results: Number of results to return
        
    Returns:
        Dictionary containing search results
    """
    logger.info(f"Searching arXiv (web scrape fallback) for: {query}")
    
    try:
        # URL encode the query
        encoded_query = urllib.parse.quote_plus(query)
        
        # Build URL
        url = f"https://arxiv.org/search/?query={encoded_query}&searchtype=all"
        
        # Setup headers to mimic a browser
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1"
        }
        
        # Make the request
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        # Parse the HTML response
        soup = BeautifulSoup(response.text, "html.parser")
        
        # Extract search results
        results = []
        
        # arXiv specific selectors
        result_elements = soup.select("li.arxiv-result")
        
        for element in result_elements[:num_results]:
            # Get title
            title_element = element.select_one("p.title")
            title = title_element.get_text(strip=True) if title_element else ""
            
            # Get URL
            link_element = element.select_one("p.list-title a")
            paper_id = link_element.get("href", "").split("/")[-1] if link_element else ""
            url = f"https://arxiv.org/abs/{paper_id}" if paper_id else ""
            
            # Get authors
            authors_element = element.select_one("p.authors")
            authors = authors_element.get_text(strip=True).replace("Authors: <AUTHORS>
            
            # Get abstract
            abstract_element = element.select_one("span.abstract-full")
            abstract = abstract_element.get_text(strip=True) if abstract_element else ""
            
            # Get published date
            date_element = element.select_one("p.is-size-7")
            date_text = date_element.get_text(strip=True) if date_element else ""
            
            # Extract year using regex
            year_match = re.search(r'\b(19|20)\d{2}\b', date_text)
            year = year_match.group(0) if year_match else ""
            
            # Create result object
            result = {
                "title": title,
                "url": url,
                "snippet": abstract,
                "authors": authors,
                "year": year,
                "source": "arxiv"
            }
            results.append(result)
        
        # If no results were found, return an error
        if not results:
            logger.warning(f"No arXiv results found for query: {query}")
            return {
                "success": False,
                "error": "No results found on arXiv",
                "results": []
            }
        
        return {
            "success": True,
            "results": results,
            "engine": "arxiv",
            "source": "arxiv"
        }
        
    except Exception as e:
        logger.error(f"Error scraping arXiv: {str(e)}")
        return {
            "success": False,
            "error": f"arXiv search failed (API and scraping): {str(e)}",
            "results": []
        }

def search_pubmed(query: str, num_results: int = 10) -> Dict[str, Any]:
    """
    Search PubMed for medical and biological research papers using their API.
    
    Args:
        query: The query to search for
        num_results: Number of results to return
        
    Returns:
        Dictionary containing search results
    """
    logger.info(f"Searching PubMed for: {query}")
    
    try:
        # URL encode the query
        encoded_query = urllib.parse.quote_plus(query)
        
        # Build API URLs (PubMed requires two API calls: one to get IDs, one to get details)
        search_url = f"https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi?db=pubmed&term={encoded_query}&retmode=json&retmax={num_results}"
        
        # Make the search request to get IDs
        search_response = requests.get(search_url, timeout=10)
        search_response.raise_for_status()
        
        # Parse the JSON response
        search_data = search_response.json()
        
        # Extract paper IDs
        paper_ids = search_data.get("esearchresult", {}).get("idlist", [])
        
        if not paper_ids:
            logger.warning(f"No PubMed IDs found for query: {query}")
            # Try fallback method: scrape the PubMed website
            return _search_pubmed_scrape(query, num_results)
        
        # Join IDs for the second API call
        id_string = ",".join(paper_ids)
        
        # Build URL for second API call to get details
        details_url = f"https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esummary.fcgi?db=pubmed&id={id_string}&retmode=json"
        
        # Make the details request
        details_response = requests.get(details_url, timeout=15)
        details_response.raise_for_status()
        
        # Parse the JSON response
        details_data = details_response.json()
        
        # Extract search results
        results = []
        
        # Get the results container
        result_container = details_data.get("result", {})
        
        # Process each paper
        for paper_id in paper_ids:
            if paper_id not in result_container:
                continue
                
            paper = result_container[paper_id]
            
            # Extract authors
            authors_list = paper.get("authors", [])
            authors = ", ".join([author.get("name", "") for author in authors_list if "name" in author])
            
            # Extract title
            title = paper.get("title", "")
            
            # Extract source/journal
            source = paper.get("source", "")
            
            # Extract publication date
            pub_date = paper.get("pubdate", "")
            
            # Extract year using regex
            year_match = re.search(r'\b(19|20)\d{2}\b', pub_date)
            year = year_match.group(0) if year_match else ""
            
            # Create URL
            url = f"https://pubmed.ncbi.nlm.nih.gov/{paper_id}"
            
            # Abstract isn't provided in the summary, only in the full record
            # We'll include it as empty and potentially fetch it later if needed
            
            # Create result object
            result = {
                "title": title,
                "url": url,
                "snippet": f"Published in {source}, {pub_date}",
                "authors": authors,
                "journal": source,
                "year": year,
                "pubmed_id": paper_id,
                "source": "pubmed"
            }
            results.append(result)
        
        # If no results were found, return an error
        if not results:
            logger.warning(f"No PubMed results found for query: {query}")
            # Try fallback method: scrape the PubMed website
            return _search_pubmed_scrape(query, num_results)
        
        return {
            "success": True,
            "results": results,
            "engine": "pubmed",
            "source": "pubmed"
        }
        
    except Exception as e:
        logger.error(f"Error searching PubMed API: {str(e)}")
        # Try fallback method: scrape the PubMed website
        return _search_pubmed_scrape(query, num_results)

def _search_pubmed_scrape(query: str, num_results: int = 10) -> Dict[str, Any]:
    """
    Fallback method to search PubMed by scraping the website.
    
    Args:
        query: The query to search for
        num_results: Number of results to return
        
    Returns:
        Dictionary containing search results
    """
    logger.info(f"Searching PubMed (web scrape fallback) for: {query}")
    
    try:
        # URL encode the query
        encoded_query = urllib.parse.quote_plus(query)
        
        # Build URL
        url = f"https://pubmed.ncbi.nlm.nih.gov/?term={encoded_query}"
        
        # Setup headers to mimic a browser
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1"
        }
        
        # Make the request
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        # Parse the HTML response
        soup = BeautifulSoup(response.text, "html.parser")
        
        # Extract search results
        results = []
        
        # PubMed specific selectors
        result_elements = soup.select("article.full-docsum")
        
        for element in result_elements[:num_results]:
            # Get title
            title_element = element.select_one(".docsum-title")
            title = title_element.get_text(strip=True) if title_element else ""
            
            # Get URL
            link_element = element.select_one(".docsum-title")
            url = ""
            if link_element and link_element.has_attr("href"):
                url = "https://pubmed.ncbi.nlm.nih.gov" + link_element["href"]
            
            # Get snippet
            snippet_element = element.select_one(".full-view-snippet")
            snippet = snippet_element.get_text(strip=True) if snippet_element else ""
            
            # Get authors
            authors_element = element.select_one(".docsum-authors")
            authors = authors_element.get_text(strip=True) if authors_element else ""
            
            # Get publication info
            citation_element = element.select_one(".docsum-journal-citation")
            citation = citation_element.get_text(strip=True) if citation_element else ""
            
            # Extract year using regex
            year_match = re.search(r'\b(19|20)\d{2}\b', citation)
            year = year_match.group(0) if year_match else ""
            
            # Create result object
            result = {
                "title": title,
                "url": url,
                "snippet": snippet,
                "authors": authors,
                "citation": citation,
                "year": year,
                "source": "pubmed"
            }
            results.append(result)
        
        # If no results were found, return an error
        if not results:
            logger.warning(f"No PubMed results found for query: {query}")
            return {
                "success": False,
                "error": "No results found on PubMed",
                "results": []
            }
        
        return {
            "success": True,
            "results": results,
            "engine": "pubmed",
            "source": "pubmed"
        }
        
    except Exception as e:
        logger.error(f"Error scraping PubMed: {str(e)}")
        return {
            "success": False,
            "error": f"PubMed search failed (API and scraping): {str(e)}",
            "results": []
        }

def search_openalex(query: str, num_results: int = 10) -> Dict[str, Any]:
    """
    Search OpenAlex for academic papers.
    
    Args:
        query: The query to search for
        num_results: Number of results to return
        
    Returns:
        Dictionary containing search results
    """
    logger.info(f"Searching OpenAlex for: {query}")
    
    try:
        # URL encode the query
        encoded_query = urllib.parse.quote_plus(query)
        
        # Build API URL
        url = f"https://api.openalex.org/works?search={encoded_query}&per-page={num_results}"
        
        # Setup headers to include email (recommended by OpenAlex)
        headers = {
            "Accept": "application/json",
            "User-Agent": "deep_research_core/1.0 (https://github.com/yourusername/deep_research_core; <EMAIL>)"
        }
        
        # Make the request
        response = requests.get(url, headers=headers, timeout=15)
        response.raise_for_status()
        
        # Parse the JSON response
        data = response.json()
        
        # Extract search results
        results = []
        papers = data.get("results", [])
        
        for paper in papers:
            # Extract title
            title = paper.get("title", "")
            
            # Extract URL
            doi = paper.get("doi", "")
            url = doi if doi else paper.get("url", "")
            
            # Extract abstract
            abstract = paper.get("abstract", "")
            
            # Extract authors
            authors_list = paper.get("authorships", [])
            authors = ", ".join([author.get("author", {}).get("display_name", "") 
                                for author in authors_list if "author" in author])
            
            # Extract publication date and year
            publication_date = paper.get("publication_date", "")
            year = publication_date[:4] if publication_date else ""
            
            # Extract journal/venue
            source = paper.get("primary_location", {}).get("source", {}).get("display_name", "")
            
            # Create result object
            result = {
                "title": title,
                "url": url,
                "snippet": abstract[:200] + "..." if abstract and len(abstract) > 200 else abstract,
                "authors": authors,
                "year": year,
                "journal": source,
                "citation_count": paper.get("cited_by_count", 0),
                "source": "openalex"
            }
            results.append(result)
        
        # If no results were found, return an error
        if not results:
            logger.warning(f"No OpenAlex results found for query: {query}")
            return {
                "success": False,
                "error": "No results found on OpenAlex",
                "results": []
            }
        
        return {
            "success": True,
            "results": results,
            "engine": "openalex",
            "source": "openalex"
        }
        
    except Exception as e:
        logger.error(f"Error searching OpenAlex: {str(e)}")
        return {
            "success": False,
            "error": f"OpenAlex search failed: {str(e)}",
            "results": []
        }

def search_crossref(query: str, num_results: int = 10) -> Dict[str, Any]:
    """
    Search Crossref for academic papers.
    
    Args:
        query: The query to search for
        num_results: Number of results to return
        
    Returns:
        Dictionary containing search results
    """
    logger.info(f"Searching Crossref for: {query}")
    
    try:
        # URL encode the query
        encoded_query = urllib.parse.quote_plus(query)
        
        # Build API URL
        url = f"https://api.crossref.org/works?query={encoded_query}&rows={num_results}"
        
        # Setup headers to include email (recommended by Crossref)
        headers = {
            "Accept": "application/json",
            "User-Agent": "deep_research_core/1.0 (https://github.com/yourusername/deep_research_core; <EMAIL>)"
        }
        
        # Make the request
        response = requests.get(url, headers=headers, timeout=15)
        response.raise_for_status()
        
        # Parse the JSON response
        data = response.json()
        
        # Extract search results
        results = []
        papers = data.get("message", {}).get("items", [])
        
        for paper in papers:
            # Extract title
            title_list = paper.get("title", [])
            title = title_list[0] if title_list else ""
            
            # Extract URL/DOI
            doi = paper.get("DOI", "")
            url = f"https://doi.org/{doi}" if doi else ""
            
            # Extract abstract (not typically available in Crossref)
            abstract = paper.get("abstract", "")
            
            # Extract authors
            authors_list = paper.get("author", [])
            authors = ", ".join([
                f"{author.get('given', '')} {author.get('family', '')}"
                for author in authors_list
            ])
            
            # Extract publication date and year
            published_date = paper.get("created", {})
            date_parts = published_date.get("date-parts", [[""]])[0]
            year = str(date_parts[0]) if date_parts else ""
            
            # Extract journal/venue
            container_title_list = paper.get("container-title", [])
            journal = container_title_list[0] if container_title_list else ""
            
            # Create result object
            result = {
                "title": title,
                "url": url,
                "snippet": abstract[:200] + "..." if abstract and len(abstract) > 200 else abstract,
                "authors": authors,
                "year": year,
                "journal": journal,
                "type": paper.get("type", ""),
                "source": "crossref"
            }
            results.append(result)
        
        # If no results were found, return an error
        if not results:
            logger.warning(f"No Crossref results found for query: {query}")
            return {
                "success": False,
                "error": "No results found on Crossref",
                "results": []
            }
        
        return {
            "success": True,
            "results": results,
            "engine": "crossref",
            "source": "crossref"
        }
        
    except Exception as e:
        logger.error(f"Error searching Crossref: {str(e)}")
        return {
            "success": False,
            "error": f"Crossref search failed: {str(e)}",
            "results": []
        }

def search_academic_combined(query: str, num_results: int = 10) -> Dict[str, Any]:
    """
    Search multiple academic sources and combine the results.
    
    Args:
        query: The query to search for
        num_results: Number of results to return per source
        
    Returns:
        Dictionary containing combined search results
    """
    logger.info(f"Performing combined academic search for: {query}")
    
    # Calculate how many results to request from each source
    # to ensure we get enough combined results
    results_per_source = min(num_results * 2, 20)
    
    # Search functions to try
    search_functions = [
        search_semantic_scholar,
        search_openalex,
        search_arxiv,
        search_pubmed,
        search_crossref,
        search_google_scholar  # Try Google Scholar last due to CAPTCHA issues
    ]
    
    # Collect all results
    all_results = []
    errors = {}
    
    # Try each search function
    for search_func in search_functions:
        try:
            source_name = search_func.__name__.replace("search_", "")
            logger.info(f"Trying {source_name} search...")
            
            results = search_func(query, results_per_source)
            
            if results.get("success") and results.get("results"):
                all_results.extend(results["results"])
                logger.info(f"Added {len(results['results'])} results from {source_name}")
            else:
                error_msg = results.get("error", "Unknown error")
                logger.warning(f"{source_name} search failed: {error_msg}")
                errors[source_name] = error_msg
                
        except Exception as e:
            source_name = search_func.__name__.replace("search_", "")
            logger.error(f"Exception in {source_name} search: {str(e)}")
            errors[source_name] = str(e)
    
    # Remove duplicates by URL
    unique_results = {}
    for result in all_results:
        url = result.get("url", "")
        if url and url not in unique_results:
            unique_results[url] = result
    
    # Convert back to list
    final_results = list(unique_results.values())
    
    # Sort by year (most recent first)
    final_results.sort(key=lambda x: x.get("year", "0"), reverse=True)
    
    # Limit to requested number
    final_results = final_results[:num_results]
    
    # If no results were found, return an error
    if not final_results:
        logger.warning(f"No results found from any academic source for query: {query}")
        return {
            "success": False,
            "error": "No results found from any academic source",
            "detailed_errors": errors,
            "results": []
        }
    
    return {
        "success": True,
        "results": final_results,
        "engine": "academic_combined",
        "source": "academic_combined",
        "errors": errors if errors else None
    }
