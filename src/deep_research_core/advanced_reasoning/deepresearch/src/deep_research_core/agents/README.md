# Deep Research Core - Agents

## WebSearchAgentMerged

### Tổng quan

WebSearchAgentMerged là phiên bản cải tiến kết hợp các tính năng tốt nhất từ WebSearchAgent và ImprovedWebSearchAgent. Phiên bản này giải quyết các vấn đề trong phiên bản gốc và thêm nhiều tính năng mới.

### Vấn đề đã giải quyết

1. **Lỗi 'int' object is not subscriptable**:
   - Khởi tạo đúng các thuộc tính từ điển trước khi gọi `super().__init__()`
   - Kiểm tra kiểu dữ liệu trước khi sử dụng
   - Khởi tạo lại các thuộc tính nếu chúng không tồn tại hoặc có kiểu dữ liệu không đúng

2. **<PERSON><PERSON><PERSON> thiện xử lý lỗi và cơ chế fallback**:
   - <PERSON><PERSON><PERSON><PERSON> các cơ chế xử lý lỗi chi tiết
   - Th<PERSON><PERSON> cơ chế fallback để chuyển đổi giữa các công cụ tìm kiếm khi một công cụ thất bại
   - Thêm cơ chế retry với backoff

3. **Tích hợp các tính năng từ cả hai phiên bản**:
   - Adaptive scraping từ ImprovedWebSearchAgent
   - Xử lý CAPTCHA từ ImprovedWebSearchAgent
   - Rate limiting nâng cao từ cả hai phiên bản
   - Caching thông minh từ WebSearchAgent

### Cách sử dụng

```python
from deep_research_core.agents.web_search_agent_merged import WebSearchAgentMerged

# Khởi tạo agent
agent = WebSearchAgentMerged(
    config={
        "use_cache": True,
        "verbose": True,
        "api_search_config": {
            "engine": "duckduckgo",
            "language": "en"
        },
        "crawlee_search_config": {
            "max_depth": 2,
            "max_pages": 10
        }
    }
)

# Tìm kiếm cơ bản
results = agent.search(
    query="python programming",
    num_results=5
)

# Tìm kiếm với engine cụ thể
results = agent.search(
    query="python programming",
    engine="duckduckgo",
    num_results=5
)

# Tìm kiếm và trích xuất nội dung
results = agent.search(
    query="python programming",
    get_content=True,
    num_results=3
)

# Phân tích truy vấn
analysis = agent.analyze_query("what is python programming?")
```

### Các phương thức chính

#### `search(query, num_results=10, language="auto", engine=None, method=None, get_content=False, force_refresh=False, **kwargs)`

Thực hiện tìm kiếm web với các tùy chọn:
- `query`: Truy vấn tìm kiếm
- `num_results`: Số kết quả trả về
- `language`: Ngôn ngữ ("auto", "en", "vi", v.v.)
- `engine`: Công cụ tìm kiếm (duckduckgo, searx, qwant, v.v.)
- `method`: Phương thức tìm kiếm ("api", "crawlee", "direct_crawlee", "auto")
- `get_content`: Có trích xuất nội dung từ kết quả tìm kiếm hay không
- `force_refresh`: Có bỏ qua cache hay không

#### `analyze_query(query)`

Phân tích truy vấn tìm kiếm để xác định chiến lược tìm kiếm tốt nhất:
- Phát hiện ngôn ngữ
- Xác định xem truy vấn có phải là câu hỏi hay không
- Kiểm tra các toán tử tìm kiếm
- Phát hiện các thuật ngữ kỹ thuật hoặc chuyên ngành

#### `extract_content(url, **kwargs)`

Trích xuất nội dung từ một URL:
- Tải nội dung
- Phân tích HTML
- Trích xuất văn bản, tiêu đề, liên kết
- Làm sạch HTML

### Cấu trúc file

- **web_search_agent_merged.py**: Phiên bản đã merge
- **test_web_search_agent_merged.py**: File test cho phiên bản đã merge
- **caching.py**: Module caching được sử dụng bởi WebSearchAgentMerged

### Lưu ý

- Các phương thức tìm kiếm như `search_duckduckgo`, `search_searx`, v.v. đã được mock để dễ dàng kiểm thử. Trong môi trường thực tế, bạn cần thay thế chúng bằng các phương thức thực tế.
- Phiên bản này đã được tối ưu hóa để xử lý cả tiếng Anh và tiếng Việt.
- Cơ chế cache giúp giảm số lượng yêu cầu đến các công cụ tìm kiếm và cải thiện hiệu suất.
