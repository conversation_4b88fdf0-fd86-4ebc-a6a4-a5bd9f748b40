// crawlee_comparison.js
const { PlaywrightCrawler } = require('crawlee');
const { writeFileSync } = require('fs');

// Danh sách User-Agent để xoay vòng
const USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
    'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
    'Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59'
];

// Lấy User-Agent ngẫu nhiên
function getRandomUserAgent() {
    return USER_AGENTS[Math.floor(Math.random() * USER_AGENTS.length)];
}

// Hàm trích xuất thông tin từ trang web
const extractPageInfo = (page) => {
    return page.evaluate(() => {
        // Trích xuất tiêu đề, nội dung, v.v.
        const title = document.title;
        const content = document.body.innerText;

        // Trích xuất các đoạn văn bản có liên quan
        const paragraphs = Array.from(document.querySelectorAll('p, h1, h2, h3, h4, h5, h6'))
            .map(el => el.innerText)
            .filter(text => text.length > 50);

        // Trích xuất meta description
        const metaDescription = document.querySelector('meta[name="description"]')?.content || '';

        return {
            title,
            content: paragraphs.join('\n\n').substring(0, 5000),
            url: window.location.href,
            meta_description: metaDescription
        };
    });
};

// Hàm delay ngẫu nhiên
function getRandomDelay(min = 2000, max = 5000) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Hàm chính để crawl
async function crawlWithPlaywright(startUrls, maxDepth, maxPages, maxResults, timeout) {
    const results = [];
    const visitedUrls = new Set();

    const crawler = new PlaywrightCrawler({
        maxRequestsPerCrawl: maxPages,
        maxConcurrency: 2, // Giới hạn số lượng request đồng thời
        navigationTimeoutSecs: timeout,

        // Cấu hình browser
        launchContext: {
            launchOptions: {
                headless: true,
                args: [
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--disable-setuid-sandbox',
                    '--no-sandbox',
                    '--disable-web-security',
                    '--disable-features=IsolateOrigins,site-per-process'
                ]
            }
        },

        // Cấu hình request
        requestHandlerTimeoutSecs: timeout,

        // Hàm xử lý mỗi trang
        async requestHandler({ page, request, enqueueLinks }) {
            // Kiểm tra độ sâu
            const depth = request.userData.depth || 0;
            if (depth > maxDepth) return;

            // Thiết lập User-Agent ngẫu nhiên
            await page.setExtraHTTPHeaders({
                'User-Agent': getRandomUserAgent(),
                'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            });

            // Thêm delay ngẫu nhiên trước khi truy cập trang
            await new Promise(resolve => setTimeout(resolve, getRandomDelay()));

            try {
                // Trích xuất thông tin
                const pageInfo = await extractPageInfo(page);

                // Thêm vào kết quả nếu chưa đủ
                if (results.length < maxResults && !visitedUrls.has(pageInfo.url)) {
                    results.push(pageInfo);
                    visitedUrls.add(pageInfo.url);
                    console.log(`Crawled: ${pageInfo.url}`);
                }

                // Thêm delay trước khi tiếp tục
                await new Promise(resolve => setTimeout(resolve, getRandomDelay(1000, 3000)));

                // Thêm các liên kết vào hàng đợi
                if (depth < maxDepth) {
                    await enqueueLinks({
                        userData: { depth: depth + 1 },
                        transformRequestFunction: (req) => {
                            // Thêm delay ngẫu nhiên cho mỗi request
                            req.userData.delay = getRandomDelay();
                            return req;
                        }
                    });
                }
            } catch (error) {
                console.error(`Error processing ${request.url}: ${error.message}`);
            }
        }
    });

    // Bắt đầu crawl
    await crawler.run(startUrls);

    // Lưu kết quả vào file tạm thời
    writeFileSync('crawlee_results.json', JSON.stringify(results));

    return {
        success: true,
        results: results
    };
}

// Xử lý tham số dòng lệnh
const args = process.argv.slice(2);
const startUrls = JSON.parse(args[0]);
const maxDepth = parseInt(args[1]);
const maxPages = parseInt(args[2]);
const maxResults = parseInt(args[3]);
const timeout = parseInt(args[4]);

// Chạy crawler
crawlWithPlaywright(startUrls, maxDepth, maxPages, maxResults, timeout)
    .catch(error => {
        console.error(error);
        process.exit(1);
    });
