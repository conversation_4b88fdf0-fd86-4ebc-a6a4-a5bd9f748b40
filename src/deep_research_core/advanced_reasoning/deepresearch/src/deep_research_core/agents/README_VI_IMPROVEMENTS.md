# Cải tiến WebSearchAgent

Tài liệu này mô tả các cải tiến mới cho WebSearchAgent tập trung vào xử lý tiếng Việt và tối ưu hóa bộ nhớ.

## 1. <PERSON><PERSON> lý tiếng Việt

### 1.1. <PERSON><PERSON> tích cảm xúc (Sentiment Analysis)

- **<PERSON><PERSON><PERSON> tiến**: Sửa lỗi phương thức `sentiment()` để kiểm tra tồn tại của hàm trong underthesea
- **Tính năng mới**: Ph<PERSON>ơng thức dự phòng `_fallback_sentiment()` để phân tích cảm xúc khi underthesea không có sentiment
- **Lợi ích**: Hệ thống luôn trả về kết quả phân tích cảm xúc, ngay cả khi underthesea không hỗ trợ

### 1.2. Tóm tắt văn bản (Text Summarization)

- **C<PERSON>i tiến**: <PERSON><PERSON><PERSON><PERSON> thuật toán TextRank cho tóm tắt chất lượng cao
- **T<PERSON>h năng mới**: 
  - Thêm tham số `method` cho phép chọn phương pháp tóm tắt
  - Thêm phương thức `_textrank_summarize()` sử dụng ma trận tương đồng
  - Thêm phương thức `_build_similarity_matrix()` và `_textrank_scores()`
- **Lợi ích**: Tóm tắt văn bản tiếng Việt chính xác hơn, đặc biệt với văn bản dài

### 1.3. Mô hình đặc thù cho domain

- **Tính năng mới**: Lớp `VietnameseDomainModel` hỗ trợ các domain cụ thể:
  - legal (pháp lý)
  - medical (y tế)
  - tech (công nghệ)
- **Thành phần chính**:
  - Từ vựng domain (`vocabulary`)
  - Từ đồng nghĩa domain (`synonyms`) 
  - Thực thể domain (`entities`)
  - PhoBERT đặc thù cho domain
- **Lợi ích**: Tìm kiếm chính xác hơn trong các lĩnh vực chuyên môn

### 1.4. Xử lý tương đồng ngữ nghĩa

- **Cải tiến**: Sửa lỗi `_phobert_similarity()` để sử dụng cosine similarity đúng cách
- **Lợi ích**: Xác định độ tương đồng văn bản tiếng Việt chính xác hơn

## 2. Tối ưu hóa bộ nhớ

### 2.1. Garbage Collection chủ động

- **Tính năng mới**:
  - Phương thức `_check_proactive_gc()` tự động kiểm tra mức sử dụng bộ nhớ
  - Phương thức `_perform_garbage_collection()` dọn dẹp bộ nhớ chủ động
  - Phương thức `_get_memory_usage()` theo dõi mức sử dụng bộ nhớ
- **Lợi ích**: Giảm thiểu rò rỉ bộ nhớ khi xử lý nhiều truy vấn

### 2.2. Tối ưu hóa kết quả trước khi lưu cache

- **Tính năng mới**: Phương thức `_optimize_result_for_cache()` để:
  - Xóa nội dung HTML lớn
  - Cắt bớt snippet quá dài
  - Loại bỏ metadata không cần thiết
  - Xóa trường dữ liệu lớn
- **Lợi ích**: Giảm kích thước cache, tăng hiệu suất

### 2.3. Cache phân tán (Redis)

- **Tính năng mới**: Lớp `RedisWebSearchCache` mở rộng từ `SmartWebSearchCache` để:
  - Lưu trữ kết quả tìm kiếm vào Redis
  - Truy vấn từ Redis với TTL
  - Hỗ trợ truy vấn tương tự trong môi trường phân tán
  - Theo dõi hiệu suất Redis
- **Lợi ích**: Hỗ trợ triển khai phân tán quy mô lớn

## 3. Cải thiện kiểm thử

### 3.1. Kiểm thử đơn vị mở rộng

- **Tính năng mới**: Kiểm thử chi tiết cho các phương thức xử lý tiếng Việt:
  - Phân tích cảm xúc
  - Tóm tắt văn bản
  - Tương đồng văn bản
  - Mô hình domain cụ thể
- **Lợi ích**: Phát hiện lỗi sớm, đảm bảo chất lượng mã

### 3.2. Kiểm thử tích hợp

- **Tính năng mới**: Kiểm thử tích hợp với dữ liệu thực:
  - Tìm kiếm web tiếng Việt
  - Trích xuất nội dung tài liệu
  - Tìm kiếm tương tự từ cache
  - Kiểm thử domain model
- **Lợi ích**: Xác minh hệ thống hoạt động đúng trong môi trường thực tế

### 3.3. Benchmark hiệu suất

- **Tính năng mới**: Benchmark đo lường hiệu suất:
  - Tìm kiếm cơ bản và tìm kiếm hàng loạt
  - Hiệu suất cache
  - Tối ưu hóa bộ nhớ
  - Xử lý tiếng Việt
- **Lợi ích**: Theo dõi hiệu suất hệ thống và phát hiện sớm sự xuống cấp

## Cách sử dụng Redis Cache

```python
from deep_research_core.agents.caching import RedisWebSearchCache
from deep_research_core.agents.web_search_agent import WebSearchAgent

# Khởi tạo cache Redis
redis_cache = RedisWebSearchCache(
    redis_url="redis://localhost:6379/0",
    prefix="websearch:",
    max_size=10000,
    default_ttl=86400,  # 1 ngày
    vietnamese_support=True
)

# Khởi tạo WebSearchAgent với Redis cache
agent = WebSearchAgent(
    config={
        "cache": "redis",
        "redis_config": {
            "url": "redis://localhost:6379/0",
            "prefix": "websearch:"
        },
        "vietnamese_support": True,
        "memory_management": {
            "enable_optimization": True,
            "result_limit_size": 1024 * 100,  # 100KB
            "cleanup_interval": 300  # 5 phút
        }
    }
)

# Sử dụng
results = agent.search("trí tuệ nhân tạo", language="vi")
```

## Sử dụng mô hình đặc thù cho domain

```python
from deep_research_core.agents.vietnamese_nlp import VietnameseDomainModel
from deep_research_core.agents.web_search_agent import WebSearchAgent

# Khởi tạo mô hình pháp lý
legal_model = VietnameseDomainModel(domain="legal")

# Tăng cường truy vấn với thuật ngữ pháp lý
query = "quy định về hợp đồng mua bán"
enhanced_query = legal_model.enhance_query_for_domain(query)
print(f"Truy vấn gốc: {query}")
print(f"Truy vấn tăng cường: {enhanced_query}")

# Sử dụng với WebSearchAgent
agent = WebSearchAgent(vietnamese_support=True)
results = agent.search(enhanced_query, language="vi")
```

## Tóm tắt văn bản với TextRank

```python
from deep_research_core.agents.vietnamese_nlp import VietnameseNLP

nlp = VietnameseNLP()

text = """
Trí tuệ nhân tạo (AI) là một lĩnh vực của khoa học máy tính tập trung vào việc tạo ra các hệ thống 
thông minh có khả năng thực hiện các nhiệm vụ thường đòi hỏi trí thông minh của con người. 
Học máy là một nhánh của AI cho phép máy tính học từ dữ liệu và cải thiện hiệu suất mà không cần 
được lập trình rõ ràng. Xử lý ngôn ngữ tự nhiên là lĩnh vực AI tập trung vào tương tác giữa máy tính 
và ngôn ngữ tự nhiên của con người. Thị giác máy tính là lĩnh vực nghiên cứu liên quan đến cách 
máy tính có thể trích xuất thông tin từ hình ảnh và video.
"""

# Tóm tắt cơ bản
basic_summary = nlp.summarize(text, max_sentences=2, method="basic")
print(f"Tóm tắt cơ bản: {basic_summary}")

# Tóm tắt TextRank
textrank_summary = nlp.summarize(text, max_sentences=2, method="textrank")
print(f"Tóm tắt TextRank: {textrank_summary}")
``` 