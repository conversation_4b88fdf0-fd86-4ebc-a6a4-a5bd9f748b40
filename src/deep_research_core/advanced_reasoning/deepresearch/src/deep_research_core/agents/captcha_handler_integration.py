#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module tích hợp CaptchaHandler vào WebSearchAgentLocal.

Module này cung cấp các hàm để tích hợp CaptchaHandler vào WebSearchAgentLocal,
gi<PERSON><PERSON> xử lý CAPTCHA hiệu quả hơn khi tìm kiếm web.
"""

import logging
import time
import traceback
import os
import json
from typing import Dict, Any, Optional, List, Tuple, Union, Callable

# Thiết lập logging
from ..utils.structured_logging import get_logger
logger = get_logger(__name__)

# Import các module xử lý lỗi
from ..utils.error_handling import (
    SearchError,
    RateLimitError,
    ConnectionError,
    ContentExtractionError,
    BotDetectionError,
    TimeoutError,
    format_error_response
)

# Import CaptchaHandler nếu có thể
try:
    from ..utils.captcha_handler import CaptchaHandler
    CAPTCHA_HANDLER_AVAILABLE = True
except ImportError:
    CAPTCHA_HANDLER_AVAILABLE = False
    logger.warning("CaptchaHandler không khả dụng. Xử lý CAPTCHA sẽ bị hạn chế.")

# Định nghĩa các lỗi cụ thể cho CaptchaHandler
class CaptchaHandlerError(Exception):
    """Lỗi cơ bản cho CaptchaHandler."""
    def __init__(self, message: str, url: str = None, details: Dict[str, Any] = None):
        self.message = message
        self.url = url
        self.details = details or {}
        super().__init__(self.message)

class CaptchaHandlerNotAvailableError(CaptchaHandlerError):
    """Lỗi khi CaptchaHandler không khả dụng."""
    pass

class CaptchaHandlerConfigError(CaptchaHandlerError):
    """Lỗi cấu hình CaptchaHandler."""
    pass

class CaptchaHandlerDetectionError(CaptchaHandlerError):
    """Lỗi phát hiện CAPTCHA."""
    pass

class CaptchaHandlerSolvingError(CaptchaHandlerError):
    """Lỗi giải CAPTCHA."""
    pass

# Hàm xử lý lỗi
def handle_captcha_handler_error(e: Exception, url: str = None, context: str = None) -> Dict[str, Any]:
    """
    Xử lý lỗi từ CaptchaHandler và trả về kết quả lỗi có cấu trúc.

    Args:
        e: Exception đã xảy ra
        url: URL đang được xử lý
        context: Ngữ cảnh của lỗi

    Returns:
        Dict[str, Any]: Kết quả lỗi có cấu trúc
    """
    error_type = type(e).__name__
    error_message = str(e)
    error_traceback = traceback.format_exc()

    # Ghi log lỗi
    logger.error(f"CaptchaHandler error in {context or 'unknown context'}: {error_type}: {error_message}")
    if logger.isEnabledFor(logging.DEBUG):
        logger.debug(f"Traceback: {error_traceback}")

    # Phân loại lỗi
    if isinstance(e, CaptchaHandlerError):
        error_category = "captcha_handler_error"
        error_details = e.details if hasattr(e, "details") else {}
        error_url = e.url if hasattr(e, "url") else url
    elif isinstance(e, ConnectionError) or "ConnectionError" in error_type:
        error_category = "connection_error"
        error_details = {"traceback": error_traceback}
        error_url = url
    elif isinstance(e, TimeoutError) or "TimeoutError" in error_type or "timeout" in error_message.lower():
        error_category = "timeout_error"
        error_details = {"traceback": error_traceback}
        error_url = url
    else:
        error_category = "unknown_error"
        error_details = {"traceback": error_traceback}
        error_url = url

    # Tạo kết quả lỗi
    error_result = {
        "success": False,
        "url": error_url,
        "error": error_message,
        "error_type": error_type,
        "error_category": error_category,
        "error_details": error_details,
        "content": f"[Error: {error_message} for URL: {error_url}]"
    }

    return error_result

def integrate_captcha_handler(agent, config: Optional[Dict[str, Any]] = None) -> None:
    """
    Tích hợp CaptchaHandler vào WebSearchAgentLocal.

    Args:
        agent: WebSearchAgentLocal instance
        config: Cấu hình cho CaptchaHandler
    """
    if not CAPTCHA_HANDLER_AVAILABLE:
        logger.warning("CaptchaHandler không khả dụng. Xử lý CAPTCHA sẽ bị hạn chế.")
        agent.has_captcha_handler = False
        return

    # Cấu hình mặc định
    default_config = {
        "auto_solve": False,
        "use_selenium": False,
        "use_playwright": True,
        "anticaptcha_key": None,
        "max_retries": 5,
        "retry_delay": 3.0,
        "user_agents": None,
        "user_agent_manager": None,
        "timeout": 30,
        "use_proxy": False,
        "proxies": None,
        "proxy_rotation": False,
        "browser_emulation": True,
        "persistent_browser": False,
        "stealth_mode": True,
        "cache_captcha_domains": True,
        "cache_ttl": 3600 * 24,
        "vietnamese_support": True,
        "verbose": False
    }

    # Kết hợp cấu hình mặc định và cấu hình người dùng
    captcha_config = default_config.copy()
    if config:
        captcha_config.update(config)

    # Khởi tạo CaptchaHandler
    try:
        agent._captcha_handler = CaptchaHandler(**captcha_config)
        agent.has_captcha_handler = True
        logger.info("CaptchaHandler initialized successfully")

        # Gán các phương thức tích hợp
        agent.detect_captcha = lambda html_content, url=None: detect_captcha(agent, html_content, url)
        agent.handle_captcha = lambda url, html_content=None: handle_captcha(agent, url, html_content)
        agent.is_domain_known_captcha = lambda url: is_domain_known_captcha(agent, url)
        agent.clear_captcha_cache = lambda: clear_captcha_cache(agent)

        # Lưu cấu hình đã sử dụng
        agent._captcha_handler_config = captcha_config

        # Khởi tạo các thuộc tính cần thiết
        agent.captcha_domains = set()
        agent.last_captcha_detection = {}

        logger.info("CaptchaHandler đã được tích hợp thành công vào WebSearchAgentLocal")
    except Exception as e:
        logger.error(f"Failed to integrate CaptchaHandler: {str(e)}")
        agent.has_captcha_handler = False

def detect_captcha(agent, html_content: str, url: str = None) -> Tuple[bool, str, Dict[str, Any]]:
    """
    Phát hiện CAPTCHA trong nội dung HTML.

    Args:
        agent: WebSearchAgentLocal instance
        html_content: Nội dung HTML cần kiểm tra
        url: URL của trang web (tùy chọn)

    Returns:
        Tuple[bool, str, Dict[str, Any]]: (có_captcha, loại_captcha, thông_tin_thêm)
    """
    if not hasattr(agent, "_captcha_handler") or agent._captcha_handler is None:
        error = CaptchaHandlerNotAvailableError("CaptchaHandler not available", url=url)
        logger.warning("CaptchaHandler not available for CAPTCHA detection")
        return False, "unknown", {}

    try:
        # Gọi phương thức detect_captcha của CaptchaHandler
        result = agent._captcha_handler.detect_captcha(html_content)

        # Kiểm tra kết quả
        if isinstance(result, tuple):
            if len(result) == 2:
                has_captcha, captcha_type = result
                captcha_data = {}
            elif len(result) >= 3:
                has_captcha, captcha_type, captcha_data = result[:3]
            else:
                has_captcha, captcha_type, captcha_data = False, "unknown", {}
        else:
            has_captcha, captcha_type, captcha_data = False, "unknown", {}

        # Ghi nhận domain có CAPTCHA nếu phát hiện
        if has_captcha and url:
            from urllib.parse import urlparse
            domain = urlparse(url).netloc
            if not hasattr(agent, 'captcha_domains'):
                agent.captcha_domains = set()
            agent.captcha_domains.add(domain)

            # Ghi nhận thời gian phát hiện CAPTCHA
            if not hasattr(agent, 'last_captcha_detection'):
                agent.last_captcha_detection = {}
            agent.last_captcha_detection[url] = time.time()

            logger.info(f"CAPTCHA detected on {url}: {captcha_type}")

        return has_captcha, captcha_type, captcha_data
    except Exception as e:
        error = CaptchaHandlerDetectionError(f"Error detecting CAPTCHA: {str(e)}", url=url)
        logger.error(f"Error detecting CAPTCHA: {str(e)}")
        return False, "unknown", {}

def handle_captcha(agent, url: str, html_content: str = None) -> Dict[str, Any]:
    """
    Xử lý CAPTCHA.

    Args:
        agent: WebSearchAgentLocal instance
        url: URL gặp CAPTCHA
        html_content: Nội dung HTML (tùy chọn)

    Returns:
        Dict[str, Any]: Kết quả xử lý CAPTCHA
    """
    if not hasattr(agent, "_captcha_handler") or agent._captcha_handler is None:
        error = CaptchaHandlerNotAvailableError("CaptchaHandler not available", url=url)
        return handle_captcha_handler_error(error, url, "handle_captcha")

    try:
        # Nếu không có html_content, thử tải trang
        if html_content is None:
            import requests
            try:
                response = requests.get(url, timeout=agent._captcha_handler_config.get("timeout", 30))
                html_content = response.text
            except Exception as e:
                error = ConnectionError(f"Failed to fetch URL: {str(e)}", url=url)
                return handle_captcha_handler_error(error, url, "handle_captcha")

        # Phát hiện CAPTCHA
        has_captcha, captcha_type, captcha_data = detect_captcha(agent, html_content, url)

        if not has_captcha:
            return {
                "success": True,
                "handled": False,
                "message": "No CAPTCHA detected",
                "url": url
            }

        # Thử giải quyết CAPTCHA với CaptchaHandler
        try:
            # Sử dụng phương thức solve_captcha
            solved_result = agent._captcha_handler.solve_captcha(html_content, url, captcha_type)

            # Kiểm tra kết quả
            if isinstance(solved_result, dict):
                if solved_result.get("success", False):
                    return {
                        "success": True,
                        "handled": True,
                        "strategy": "captcha_handler",
                        "content": solved_result.get("content", ""),
                        "url": url,
                        "captcha_type": captcha_type,
                        "captcha_data": captcha_data
                    }
            elif isinstance(solved_result, str) and solved_result:
                return {
                    "success": True,
                    "handled": True,
                    "strategy": "captcha_handler",
                    "content": solved_result,
                    "url": url,
                    "captcha_type": captcha_type,
                    "captcha_data": captcha_data
                }
        except Exception as e:
            logger.warning(f"CaptchaHandler failed to solve CAPTCHA: {str(e)}")

        # Nếu không thể giải quyết CAPTCHA
        return {
            "success": False,
            "handled": False,
            "error": "Failed to solve CAPTCHA",
            "url": url,
            "captcha_detected": True,
            "captcha_type": captcha_type,
            "captcha_data": captcha_data
        }
    except Exception as e:
        error = CaptchaHandlerError(f"Unexpected error: {str(e)}", url=url)
        return handle_captcha_handler_error(error, url, "handle_captcha")

def is_domain_known_captcha(agent, url: str) -> bool:
    """
    Kiểm tra xem domain có phải là domain đã biết có CAPTCHA không.

    Args:
        agent: WebSearchAgentLocal instance
        url: URL cần kiểm tra

    Returns:
        bool: True nếu domain đã biết có CAPTCHA, False nếu không
    """
    if not hasattr(agent, "_captcha_handler") or agent._captcha_handler is None:
        return False

    try:
        from urllib.parse import urlparse
        domain = urlparse(url).netloc

        # Kiểm tra trong danh sách domain có CAPTCHA
        if hasattr(agent, 'captcha_domains') and domain in agent.captcha_domains:
            return True

        # Kiểm tra trong CaptchaHandler
        if hasattr(agent._captcha_handler, 'is_domain_known_captcha'):
            return agent._captcha_handler.is_domain_known_captcha(url)

        return False
    except Exception as e:
        logger.error(f"Error checking if domain is known CAPTCHA: {str(e)}")
        return False

def clear_captcha_cache(agent) -> None:
    """
    Xóa cache CAPTCHA.

    Args:
        agent: WebSearchAgentLocal instance
    """
    if not hasattr(agent, "_captcha_handler") or agent._captcha_handler is None:
        return

    try:
        # Xóa cache trong CaptchaHandler
        if hasattr(agent._captcha_handler, 'clear_captcha_cache'):
            agent._captcha_handler.clear_captcha_cache()

        # Xóa cache trong agent
        if hasattr(agent, 'captcha_domains'):
            agent.captcha_domains.clear()

        if hasattr(agent, 'last_captcha_detection'):
            agent.last_captcha_detection.clear()

        logger.info("CAPTCHA cache cleared")
    except Exception as e:
        logger.error(f"Error clearing CAPTCHA cache: {str(e)}")

def try_different_user_agent(self, url: str) -> Dict[str, Any]:
    """
    Thử sử dụng User-Agent khác.
    """
    # Triển khai trong phiên bản tiếp theo
    return {'success': False}

def try_delay_and_retry(self, url: str) -> Dict[str, Any]:
    """
    Thử chờ một thời gian và thử lại.
    """
    # Triển khai trong phiên bản tiếp theo
    return {'success': False}

def try_alternative_search_method(self, url: str) -> Dict[str, Any]:
    """
    Thử phương thức tìm kiếm khác (Playwright).
    """
    try:
        # Sử dụng Playwright để trích xuất nội dung
        content_result = self._deep_crawl(
            url=url,
            max_depth=0,  # Chỉ crawl trang hiện tại
            max_pages=1,   # Chỉ lấy 1 trang
            timeout=30,
            include_html=True,
            respect_robots=True
        )

        if content_result.get('success'):
            # Kiểm tra xem còn CAPTCHA không
            content = content_result.get('text', '')
            has_captcha, _ = self._captcha_handler.detect_captcha(content)
            if not has_captcha:
                return {
                    'success': True,
                    'url': url,
                    'content': content,
                    'strategy': 'alternative_search_method'
                }

    except Exception as e:
        logger.warning(f"Failed to use alternative search method: {str(e)}")

    return {'success': False}

def try_browser_emulation(self, url: str) -> Dict[str, Any]:
    """
    Thử sử dụng trình duyệt để giải quyết CAPTCHA.
    """
    # Triển khai trong phiên bản tiếp theo
    return {'success': False}

def simulate_human_behavior(self, page) -> None:
    """
    Mô phỏng hành vi người dùng trên trang web.
    """
    # Triển khai trong phiên bản tiếp theo
    pass
