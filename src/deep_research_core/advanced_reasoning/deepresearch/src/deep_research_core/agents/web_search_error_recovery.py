"""
Web Search Error Recovery Module.

Module này cung cấp các chức năng xử lý lỗi và phục hồi cho WebSearchAgent.
"""

import time
import logging
import random
import traceback
import functools
from typing import Dict, Any, List, Optional, Union, Callable, TypeVar

from ..utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

# Định nghĩa T là kiểu trả về bất kỳ cho decorator
T = TypeVar('T')

# Khởi tạo cấu hình mặc định
_error_recovery_config = {
    "max_retries": 3,
    "backoff_factor": 0.5,
    "captcha_solver_available": False,
    "proxy_rotation_enabled": False,
    "user_agent_rotation_enabled": True,
    "fallback_sources": ["duckduckgo", "qwant", "searxng"],
    "wait_between_retries": True,
    "verbose": False
}

# <PERSON><PERSON><PERSON> lo<PERSON>i lỗi
ERROR_TYPES = {
    "network": ["ConnectionError", "Timeout", "TooManyRedirects", "RequestException"],
    "rate_limit": ["429", "RateLimitExceeded", "TooManyRequests"],
    "captcha": ["Captcha", "CaptchaDetected", "403", "Forbidden"],
    "server": ["500", "502", "503", "504", "ServerError"],
    "content": ["ContentError", "ParseError", "EmptyResponse"],
    "authentication": ["401", "403", "Unauthorized", "Forbidden"]
}

class WebSearchErrorRecovery:
    """
    Hệ thống phục hồi lỗi cho WebSearchAgent.
    
    Cung cấp các cơ chế xử lý lỗi và phục hồi tự động.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Khởi tạo WebSearchErrorRecovery.
        
        Args:
            config: Cấu hình tùy chọn
        """
        self.config = config or _error_recovery_config.copy()
        self.error_stats = {
            "total": 0,
            "recovered": 0,
            "failed": 0,
            "by_type": {
                "network": {"count": 0, "recovered": 0},
                "rate_limit": {"count": 0, "recovered": 0},
                "captcha": {"count": 0, "recovered": 0},
                "server": {"count": 0, "recovered": 0},
                "content": {"count": 0, "recovered": 0},
                "authentication": {"count": 0, "recovered": 0},
                "unknown": {"count": 0, "recovered": 0}
            }
        }
        
        logger.info(f"Khởi tạo WebSearchErrorRecovery với max_retries={self.config['max_retries']}")
    
    def handle_error(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Xử lý lỗi và đề xuất giải pháp.
        
        Args:
            error: Lỗi cần xử lý
            context: Ngữ cảnh lỗi
            
        Returns:
            Dictionary chứa thông tin xử lý và đề xuất phục hồi
        """
        context = context or {}
        error_str = str(error)
        error_type = self._classify_error(error, error_str)
        
        # Tăng số lượt lỗi
        self.error_stats["total"] += 1
        self.error_stats["by_type"][error_type]["count"] += 1
        
        # Tạo kết quả xử lý
        result = {
            "error": error_str,
            "error_type": error_type,
            "context": context,
            "traceback": traceback.format_exc(),
            "recovery_options": [],
            "should_retry": False,
            "retry_delay": 0,
            "retry_with_different_params": False,
            "new_params": {}
        }
        
        # Xử lý theo loại lỗi
        if error_type == "network":
            result.update(self._handle_network_error(error_str))
        elif error_type == "rate_limit":
            result.update(self._handle_rate_limit_error(error_str, context))
        elif error_type == "captcha":
            result.update(self._handle_captcha_error(error_str, context))
        elif error_type == "server":
            result.update(self._handle_server_error(error_str))
        elif error_type == "content":
            result.update(self._handle_content_error(error_str, context))
        elif error_type == "authentication":
            result.update(self._handle_authentication_error(error_str, context))
        else:
            result.update(self._handle_unknown_error(error_str))
        
        # Chi tiết về việc phục hồi
        if self.config["verbose"]:
            if result["should_retry"]:
                logger.info(f"Phục hồi lỗi {error_type}: {error_str} (thử lại sau {result['retry_delay']}s)")
            else:
                logger.info(f"Không thể phục hồi lỗi {error_type}: {error_str}")
        
        return result
    
    def _classify_error(self, error: Exception, error_str: str) -> str:
        """
        Phân loại lỗi.
        
        Args:
            error: Lỗi cần phân loại
            error_str: Chuỗi lỗi
            
        Returns:
            Loại lỗi
        """
        error_name = error.__class__.__name__
        
        for error_type, patterns in ERROR_TYPES.items():
            for pattern in patterns:
                if pattern in error_str or pattern in error_name:
                    return error_type
        
        return "unknown"
    
    def _handle_network_error(self, error_str: str) -> Dict[str, Any]:
        """
        Xử lý lỗi mạng.
        
        Args:
            error_str: Chuỗi lỗi
            
        Returns:
            Dictionary chứa thông tin xử lý
        """
        result = {
            "should_retry": True,
            "retry_delay": self._calculate_retry_delay(1),
            "recovery_options": ["retry", "timeout_increase"]
        }
        
        if "timeout" in error_str.lower():
            result["retry_with_different_params"] = True
            result["new_params"] = {"timeout": 60}  # Tăng timeout
            result["recovery_options"].append("proxy_rotation")
        elif "connection" in error_str.lower():
            result["retry_delay"] = self._calculate_retry_delay(2)  # Chờ lâu hơn cho lỗi kết nối
            result["recovery_options"].append("proxy_rotation")
        
        return result
    
    def _handle_rate_limit_error(self, error_str: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Xử lý lỗi giới hạn tỷ lệ.
        
        Args:
            error_str: Chuỗi lỗi
            context: Ngữ cảnh lỗi
            
        Returns:
            Dictionary chứa thông tin xử lý
        """
        engine = context.get("engine", "default")
        
        result = {
            "should_retry": True,
            "retry_delay": self._calculate_retry_delay(3), # Chờ lâu hơn cho rate limit
            "recovery_options": ["retry_with_backoff", "engine_switch"],
            "retry_with_different_params": True,
            "new_params": {}
        }
        
        # Chuyển sang công cụ khác
        fallback_sources = self.config["fallback_sources"]
        if fallback_sources and engine in fallback_sources:
            # Tìm nguồn dự phòng khác
            available_fallbacks = [e for e in fallback_sources if e != engine]
            if available_fallbacks:
                new_engine = random.choice(available_fallbacks)
                result["new_params"]["engine"] = new_engine
                result["recovery_options"].append(f"switch_to_{new_engine}")
        
        # Thêm luân chuyển proxy nếu được bật
        if self.config["proxy_rotation_enabled"]:
            result["new_params"]["use_proxy"] = True
            result["recovery_options"].append("proxy_rotation")
        
        # Thêm luân chuyển user-agent
        if self.config["user_agent_rotation_enabled"]:
            result["new_params"]["rotate_user_agent"] = True
        
        return result
    
    def _handle_captcha_error(self, error_str: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Xử lý lỗi captcha.
        
        Args:
            error_str: Chuỗi lỗi
            context: Ngữ cảnh lỗi
            
        Returns:
            Dictionary chứa thông tin xử lý
        """
        result = {
            "should_retry": self.config["captcha_solver_available"],
            "retry_delay": self._calculate_retry_delay(5),  # Chờ lâu hơn cho captcha
            "recovery_options": ["engine_switch", "proxy_rotation"],
            "retry_with_different_params": True,
            "new_params": {}
        }
        
        # Nếu có công cụ giải captcha, sử dụng nó
        if self.config["captcha_solver_available"]:
            result["recovery_options"].append("solve_captcha")
            result["new_params"]["solve_captcha"] = True
        
        # Nếu không, chuyển sang công cụ khác
        engine = context.get("engine", "default")
        fallback_sources = self.config["fallback_sources"]
        if fallback_sources and engine in fallback_sources:
            available_fallbacks = [e for e in fallback_sources if e != engine]
            if available_fallbacks:
                new_engine = random.choice(available_fallbacks)
                result["new_params"]["engine"] = new_engine
                result["recovery_options"].append(f"switch_to_{new_engine}")
                result["should_retry"] = True
        
        # Thêm luân chuyển proxy
        if self.config["proxy_rotation_enabled"]:
            result["new_params"]["use_proxy"] = True
            result["new_params"]["proxy_rotation"] = "random"
            result["recovery_options"].append("proxy_rotation")
        
        # Thêm luân chuyển user-agent
        if self.config["user_agent_rotation_enabled"]:
            result["new_params"]["rotate_user_agent"] = True
        
        return result
    
    def _handle_server_error(self, error_str: str) -> Dict[str, Any]:
        """
        Xử lý lỗi máy chủ.
        
        Args:
            error_str: Chuỗi lỗi
            
        Returns:
            Dictionary chứa thông tin xử lý
        """
        result = {
            "should_retry": True,
            "retry_delay": self._calculate_retry_delay(2),
            "recovery_options": ["retry_with_backoff"]
        }
        
        # Xác định loại lỗi máy chủ cụ thể
        if "503" in error_str or "Service Unavailable" in error_str:
            result["retry_delay"] = self._calculate_retry_delay(4)  # Chờ lâu hơn cho dịch vụ không khả dụng
        elif "502" in error_str or "Bad Gateway" in error_str:
            result["retry_delay"] = self._calculate_retry_delay(3)
        elif "504" in error_str or "Gateway Timeout" in error_str:
            result["retry_delay"] = self._calculate_retry_delay(3)
        
        return result
    
    def _handle_content_error(self, error_str: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Xử lý lỗi nội dung.
        
        Args:
            error_str: Chuỗi lỗi
            context: Ngữ cảnh lỗi
            
        Returns:
            Dictionary chứa thông tin xử lý
        """
        result = {
            "should_retry": True,
            "retry_delay": self._calculate_retry_delay(1),
            "recovery_options": ["retry", "parser_switch"],
            "retry_with_different_params": True,
            "new_params": {}
        }
        
        # Thử phương pháp phân tích khác
        if "parse" in error_str.lower() or "parser" in error_str.lower():
            result["new_params"]["alternative_parser"] = True
        
        # Xử lý nội dung trống
        if "empty" in error_str.lower() or "no content" in error_str.lower():
            engine = context.get("engine", "default")
            fallback_sources = self.config["fallback_sources"]
            if fallback_sources and engine in fallback_sources:
                available_fallbacks = [e for e in fallback_sources if e != engine]
                if available_fallbacks:
                    new_engine = random.choice(available_fallbacks)
                    result["new_params"]["engine"] = new_engine
                    result["recovery_options"].append(f"switch_to_{new_engine}")
        
        return result
    
    def _handle_authentication_error(self, error_str: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Xử lý lỗi xác thực.
        
        Args:
            error_str: Chuỗi lỗi
            context: Ngữ cảnh lỗi
            
        Returns:
            Dictionary chứa thông tin xử lý
        """
        result = {
            "should_retry": False,  # Mặc định không thử lại lỗi xác thực
            "retry_delay": 0,
            "recovery_options": ["api_key_refresh", "engine_switch"],
            "retry_with_different_params": True,
            "new_params": {}
        }
        
        # Chuyển sang công cụ khác nếu có
        engine = context.get("engine", "default")
        fallback_sources = self.config["fallback_sources"]
        if fallback_sources and engine in fallback_sources:
            available_fallbacks = [e for e in fallback_sources if e != engine]
            if available_fallbacks:
                new_engine = random.choice(available_fallbacks)
                result["new_params"]["engine"] = new_engine
                result["recovery_options"].append(f"switch_to_{new_engine}")
                result["should_retry"] = True
        
        return result
    
    def _handle_unknown_error(self, error_str: str) -> Dict[str, Any]:
        """
        Xử lý lỗi không xác định.
        
        Args:
            error_str: Chuỗi lỗi
            
        Returns:
            Dictionary chứa thông tin xử lý
        """
        result = {
            "should_retry": False,
            "retry_delay": 0,
            "recovery_options": []
        }
        
        # Thử phát hiện một số lỗi phổ biến
        lower_error = error_str.lower()
        
        if "connection" in lower_error or "timeout" in lower_error or "network" in lower_error:
            result.update(self._handle_network_error(error_str))
        elif "empty" in lower_error or "parse" in lower_error or "content" in lower_error:
            context = {}
            result.update(self._handle_content_error(error_str, context))
        elif any(code in error_str for code in ["401", "403", "429", "500", "502", "503", "504"]):
            if "429" in error_str:
                context = {}
                result.update(self._handle_rate_limit_error(error_str, context))
            elif "401" in error_str or "403" in error_str:
                context = {}
                result.update(self._handle_authentication_error(error_str, context))
            else:
                result.update(self._handle_server_error(error_str))
        else:
            # Nếu không thể phân loại, thử lại một lần
            result["should_retry"] = True
            result["retry_delay"] = self._calculate_retry_delay(1)
            result["recovery_options"] = ["retry_once"]
        
        return result
    
    def _calculate_retry_delay(self, attempt: int) -> float:
        """
        Tính toán thời gian chờ giữa các lần thử lại.
        
        Args:
            attempt: Số lần thử
            
        Returns:
            Thời gian chờ (giây)
        """
        backoff_factor = self.config["backoff_factor"]
        base_delay = 1.0
        
        if attempt <= 0:
            return base_delay
        
        # Exponential backoff with jitter
        delay = base_delay * (backoff_factor ** (attempt - 1))
        jitter = random.uniform(0.8, 1.2)  # Thêm 20% jitter
        
        return delay * jitter
    
    def update_stats(self, success: bool, error_type: str) -> None:
        """
        Cập nhật thống kê lỗi.
        
        Args:
            success: Có thành công hay không
            error_type: Loại lỗi
        """
        if success:
            self.error_stats["recovered"] += 1
            self.error_stats["by_type"][error_type]["recovered"] += 1
        else:
            self.error_stats["failed"] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Lấy thống kê về lỗi và phục hồi.
        
        Returns:
            Dictionary chứa thống kê
        """
        stats = self.error_stats.copy()
        
        # Tính tỷ lệ phục hồi
        if stats["total"] > 0:
            stats["recovery_rate"] = stats["recovered"] / stats["total"]
        else:
            stats["recovery_rate"] = 0.0
        
        # Tính tỷ lệ phục hồi theo loại
        for error_type, data in stats["by_type"].items():
            if data["count"] > 0:
                data["recovery_rate"] = data["recovered"] / data["count"]
            else:
                data["recovery_rate"] = 0.0
        
        return stats
    
    def reset_stats(self) -> None:
        """Đặt lại thống kê về lỗi và phục hồi."""
        self.error_stats = {
            "total": 0,
            "recovered": 0,
            "failed": 0,
            "by_type": {
                "network": {"count": 0, "recovered": 0},
                "rate_limit": {"count": 0, "recovered": 0},
                "captcha": {"count": 0, "recovered": 0},
                "server": {"count": 0, "recovered": 0},
                "content": {"count": 0, "recovered": 0},
                "authentication": {"count": 0, "recovered": 0},
                "unknown": {"count": 0, "recovered": 0}
            }
        }


# Instance toàn cục của hệ thống phục hồi lỗi
_error_recovery_system = WebSearchErrorRecovery()

def get_error_recovery_system() -> WebSearchErrorRecovery:
    """
    Lấy instance toàn cục của hệ thống phục hồi lỗi.
    
    Returns:
        Instance của WebSearchErrorRecovery
    """
    return _error_recovery_system

def configure_error_recovery(**kwargs) -> None:
    """
    Cấu hình hệ thống phục hồi lỗi.
    
    Args:
        **kwargs: Các tham số cấu hình
    """
    global _error_recovery_config
    global _error_recovery_system
    
    # Cập nhật cấu hình
    for key, value in kwargs.items():
        if key in _error_recovery_config:
            _error_recovery_config[key] = value
    
    # Tạo mới instance với cấu hình đã cập nhật
    _error_recovery_system = WebSearchErrorRecovery(_error_recovery_config)
    
    logger.info(f"Đã cấu hình hệ thống phục hồi lỗi với max_retries={_error_recovery_config['max_retries']}")

def with_error_recovery(func: Callable[..., T]) -> Callable[..., T]:
    """
    Decorator để thêm phục hồi lỗi cho hàm.
    
    Args:
        func: Hàm cần thêm phục hồi lỗi
        
    Returns:
        Hàm đã được bọc với phục hồi lỗi
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        recovery_system = get_error_recovery_system()
        max_retries = _error_recovery_config["max_retries"]
        attempt = 0
        last_error = None
        error_type = "unknown"
        
        while attempt <= max_retries:
            try:
                # Thực thi hàm
                result = func(*args, **kwargs)
                
                # Nếu đây là lần thử lại thành công, cập nhật thống kê
                if attempt > 0:
                    recovery_system.update_stats(True, error_type)
                    if _error_recovery_config["verbose"]:
                        logger.info(f"Phục hồi thành công sau {attempt} lần thử lại")
                
                return result
            
            except Exception as e:
                attempt += 1
                last_error = e
                
                # Tạo ngữ cảnh từ tham số
                context = {
                    "function": func.__name__,
                    "attempt": attempt,
                    "max_retries": max_retries
                }
                
                # Thêm tham số vào ngữ cảnh
                if len(args) > 0 and hasattr(args[0], '__class__'):
                    # Đây có thể là phương thức của lớp
                    context["class"] = args[0].__class__.__name__
                
                # Thêm tham số vào ngữ cảnh
                for key, value in kwargs.items():
                    if isinstance(value, (str, int, float, bool)) or value is None:
                        context[key] = value
                
                # Xử lý lỗi
                error_result = recovery_system.handle_error(e, context)
                error_type = error_result["error_type"]
                
                if not error_result["should_retry"] or attempt > max_retries:
                    # Không thể phục hồi hoặc đã hết số lần thử, cập nhật thống kê
                    recovery_system.update_stats(False, error_type)
                    
                    if _error_recovery_config["verbose"]:
                        logger.error(f"Không thể phục hồi lỗi sau {attempt} lần thử: {str(e)}")
                    
                    # Gửi lại lỗi
                    raise
                
                # Cập nhật tham số cho lần thử tiếp theo
                if error_result["retry_with_different_params"]:
                    for key, value in error_result["new_params"].items():
                        kwargs[key] = value
                
                # Chờ trước khi thử lại
                if _error_recovery_config["wait_between_retries"] and error_result["retry_delay"] > 0:
                    time.sleep(error_result["retry_delay"])
                
                if _error_recovery_config["verbose"]:
                    options = ", ".join(error_result["recovery_options"])
                    logger.info(f"Thử lại {attempt}/{max_retries} sau lỗi {error_type} với các tùy chọn: {options}")
        
        # Không nên đến đây, nhưng nếu có, gửi lại lỗi cuối cùng
        raise last_error
    
    return wrapper 