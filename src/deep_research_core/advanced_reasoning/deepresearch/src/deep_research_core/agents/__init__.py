"""
Agents module for Deep Research Core.

This module provides various agent implementations for different tasks.
"""

# Import configuration variables
from .config import TRANSLATOR_AVAILABLE

# Import caching classes
from .caching import WebSearchCache, SmartWebSearchCache

# Define empty variables to avoid circular imports
# These will be properly imported later
SEARCH_DUCKDUCKGO_AVAILABLE = False
SEARCH_QWANT_AVAILABLE = False
SEARCH_SEARX_AVAILABLE = False

# Temporarily commented out due to import error
# from .researcher import ResearcherAgent

__all__ = [
    # 'ResearcherAgent',
    'WebSearchAgent',
    'WebSearchAgentLocal',
    'WebSearchCache',
    'SmartWebSearchCache',
    'TRANSLATOR_AVAILABLE'
]

# Import WebSearchAgent after defining TRANSLATOR_AVAILABLE to avoid circular import
from .web_search_agent_local import WebSearchAgentLocal
from .web_search_agent import WebSearchAgent