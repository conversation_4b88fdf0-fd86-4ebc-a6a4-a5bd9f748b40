"""
<PERSON><PERSON><PERSON><PERSON> thức _deep_crawl cải tiến cho WebSearchAgentLocal.

Mo<PERSON><PERSON> này cung cấp phương thức _deep_crawl cải tiến để tối ưu hóa hiệu suấ<PERSON>,
thê<PERSON> lọc URL thông minh, và cải thiện chiến lược crawl dựa trên độ phức tạp.
Hỗ trợ tải xuống và xử lý file từ các URL.
"""

import logging
import time
import random
import os
import asyncio
from typing import Dict, Any, List, Optional, Tuple, Set
from urllib.parse import urlparse, urljoin

from bs4 import BeautifulSoup
import requests

from ..utils.structured_logging import get_logger
from ..utils.robots_parser import robots_parser
from ..utils.url_utils import clean_url, is_valid_url, get_domain
from .dynamic_content_handler import DynamicContentHandler

# Thử import WebSearchFileProcessor
try:
    from .web_search_file_processor import WebSearchFileProcessor
    FILE_PROCESSOR_AVAILABLE = True
except ImportError:
    FILE_PROCESSOR_AVAILABLE = False

# Create a logger
logger = get_logger(__name__)

def deep_crawl(
    self,
    url: str,
    max_depth: int = 2,
    max_pages: int = 5,
    timeout: int = 30,
    include_html: bool = False,
    respect_robots: bool = True,
    download_files: bool = False,
    download_dir: str = "downloads",
    process_file_content: bool = True,
    supported_file_types: Optional[List[str]] = None,
    extract_images: bool = False,
    extract_links: bool = True,
    extract_tables: bool = True,
    extract_metadata: bool = True,
    optimize_performance: bool = True,
    **kwargs
) -> Dict[str, Any]:
    """
    Crawl sâu vào trang web để trích xuất nội dung.

    Args:
        url: URL cần crawl
        max_depth: Độ sâu tối đa khi crawl
        max_pages: Số trang tối đa khi crawl
        timeout: Thời gian chờ tối đa (giây)
        include_html: Có bao gồm HTML không
        respect_robots: Có tuân thủ robots.txt không
        download_files: Có tải xuống các file hay không
        download_dir: Thư mục để lưu các file tải xuống
        process_file_content: Có xử lý nội dung file sau khi tải xuống không
        supported_file_types: Danh sách các loại file được hỗ trợ (None = tất cả)
        extract_images: Có trích xuất hình ảnh không
        extract_links: Có trích xuất liên kết không
        extract_tables: Có trích xuất bảng không
        extract_metadata: Có trích xuất metadata không
        optimize_performance: Có tối ưu hóa hiệu suất không
        **kwargs: Các tham số bổ sung

    Returns:
        Dictionary chứa nội dung trích xuất
    """
    from playwright.sync_api import sync_playwright

    # Khởi tạo file processor nếu cần
    file_processor = None
    if download_files and FILE_PROCESSOR_AVAILABLE:
        # Xác định các loại file được hỗ trợ
        if supported_file_types is None:
            # Mặc định hỗ trợ các loại file phổ biến
            supported_file_types = [
                # Văn bản
                "pdf", "doc", "docx", "txt", "rtf", "odt",
                # Bảng tính
                "xls", "xlsx", "csv", "ods",
                # Trình bày
                "ppt", "pptx", "odp",
                # Dữ liệu
                "json", "xml", "yaml", "yml",
                # Hình ảnh (nếu extract_images=True)
                "jpg", "jpeg", "png", "gif", "svg", "webp",
                # Tài liệu khác
                "epub", "mobi"
            ]

            # Thêm các định dạng hình ảnh nếu được yêu cầu
            if extract_images:
                supported_file_types.extend(["bmp", "tiff", "ico", "raw"])

        file_processor = WebSearchFileProcessor(
            download_dir=download_dir,
            timeout=timeout
        )

    logger.info(f"Deep crawling {url} with max depth {max_depth}, max pages {max_pages}")

    # Lấy các tham số bổ sung
    url_filter = kwargs.get("url_filter", None)
    content_filter = kwargs.get("content_filter", None)
    priority_keywords = kwargs.get("priority_keywords", [])
    complexity_level = kwargs.get("complexity_level", "medium")

    # Điều chỉnh tham số dựa trên độ phức tạp
    if complexity_level == "high" or complexity_level == "complex":
        max_depth = max(max_depth, 3)
        max_pages = max(max_pages, 7)
    elif complexity_level == "low" or complexity_level == "simple":
        max_depth = min(max_depth, 1)
        max_pages = min(max_pages, 3)

    # Kiểm tra xem URL có phải là trang web động không
    is_dynamic_page = kwargs.get("is_dynamic_page", False)
    handle_javascript = kwargs.get("handle_javascript", False)
    handle_spa = kwargs.get("handle_spa", False)

    # Nếu là trang web động, sử dụng DynamicContentHandler
    if is_dynamic_page or handle_javascript or handle_spa:
        logger.info(f"Using DynamicContentHandler for dynamic page: {url}")

        # Khởi tạo DynamicContentHandler
        dynamic_handler = DynamicContentHandler(
            timeout=timeout,
            respect_robots=respect_robots,
            config={
                "wait_for_idle_network": True,
                "wait_for_selectors": True,
                "handle_lazy_loading": True,
                "handle_infinite_scroll": handle_spa,
                "handle_pagination": handle_spa,
                "handle_popups": True,
                "handle_iframes": handle_javascript,
                "extract_shadow_dom": handle_javascript,
                "extract_dynamic_data": True,
                "block_ads": True,
                "block_trackers": True,
                "block_media": kwargs.get("block_media", False),
            }
        )

        # Trích xuất nội dung
        dynamic_result = asyncio.run(dynamic_handler.extract_content(
            url=url,
            selectors=kwargs.get("content_selectors"),
            wait_for_selectors=kwargs.get("wait_for_selectors"),
            scroll_count=kwargs.get("scroll_count", 3),
            wait_time=kwargs.get("wait_time", 1000),
            extract_metadata=True,
            extract_links=True,
            extract_images=include_html,
            extract_json_ld=True
        ))

        # Chuyển đổi kết quả sang định dạng chuẩn
        result = {
            "success": dynamic_result.get("success", False),
            "url": url,
            "title": dynamic_result.get("title", ""),
            "text": dynamic_result.get("content", ""),
            "crawled_pages": [{
                "url": url,
                "title": dynamic_result.get("title", ""),
                "text": dynamic_result.get("content", ""),
                "html": dynamic_result.get("html", "") if include_html else ""
            }],
            "links": [link.get("url") for link in dynamic_result.get("links", [])],
            "crawl_stats": {
                "max_depth": max_depth,
                "max_pages": max_pages,
                "start_time": time.time(),
                "execution_time": dynamic_result.get("elapsed_time", 0),
                "is_dynamic_page": True
            }
        }

        # Thêm metadata nếu có
        if dynamic_result.get("metadata"):
            result["metadata"] = dynamic_result.get("metadata")

        # Thêm JSON-LD nếu có
        if dynamic_result.get("json_ld"):
            result["json_ld"] = dynamic_result.get("json_ld")

        # Thêm thông tin về hình ảnh nếu có
        if include_html and dynamic_result.get("images"):
            result["images"] = dynamic_result.get("images")

        # Thêm dữ liệu động nếu có
        if dynamic_result.get("dynamic_data"):
            result["dynamic_data"] = dynamic_result.get("dynamic_data")

        return result

    # Nếu không phải trang web động, sử dụng phương pháp thông thường
    # Khởi tạo kết quả
    result = {
        "success": True,
        "url": url,
        "title": "",
        "text": "",
        "crawled_pages": [],
        "links": [],
        "crawl_stats": {
            "max_depth": max_depth,
            "max_pages": max_pages,
            "start_time": time.time()
        }
    }

    # Tập hợp các URL đã thăm
    visited_urls = set()
    # Hàng đợi các URL cần thăm: (url, depth, priority)
    url_queue = [(url, 0, 10)]  # URL gốc có độ ưu tiên cao nhất
    # Số trang đã crawl
    crawled_count = 0

    try:
        with sync_playwright() as p:
            browser = p.chromium.launch(
                headless=True,
                args=['--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage']
            )

            context = browser.new_context(
                viewport={"width": 1280, "height": 800},
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36"
            )

            # Xử lý các URL trong hàng đợi
            while url_queue and crawled_count < max_pages:
                # Sắp xếp hàng đợi theo độ ưu tiên (cao đến thấp)
                url_queue.sort(key=lambda x: (-x[2], x[1]))  # Ưu tiên theo: độ ưu tiên cao, độ sâu thấp

                current_url, current_depth, priority = url_queue.pop(0)

                if current_url in visited_urls:
                    continue

                if current_depth > max_depth:
                    continue

                try:
                    # Kiểm tra robots.txt
                    if respect_robots and not robots_parser.can_fetch(current_url):
                        logger.warning(f"URL {current_url} is disallowed by robots.txt")
                        visited_urls.add(current_url)
                        continue

                    logger.info(f"Crawling {current_url} (depth: {current_depth}, priority: {priority})")

                    # Tạo trang mới
                    page = context.new_page()

                    # Thiết lập timeout
                    page.set_default_timeout(timeout * 1000)

                    # Điều hướng đến URL
                    response = page.goto(current_url, wait_until="domcontentloaded")

                    if not response:
                        logger.warning(f"Failed to load {current_url}")
                        visited_urls.add(current_url)
                        page.close()
                        continue

                    # Đợi trang tải
                    page.wait_for_load_state("networkidle", timeout=timeout * 1000)

                    # Lấy nội dung
                    content = page.content()
                    title = page.title()

                    # Phân tích nội dung
                    soup = BeautifulSoup(content, "html.parser")

                    # Loại bỏ các phần tử không cần thiết
                    for element in soup.select("script, style, nav, footer, header, aside, iframe, noscript"):
                        element.decompose()

                    # Trích xuất nội dung văn bản
                    page_text = soup.get_text(separator="\n", strip=True)

                    # Kiểm tra bộ lọc nội dung nếu có
                    if content_filter and not content_filter(page_text):
                        logger.info(f"Content filter rejected {current_url}")
                        visited_urls.add(current_url)
                        page.close()
                        continue

                    # Thêm vào kết quả
                    page_result = {
                        "url": current_url,
                        "title": title,
                        "text": page_text,
                        "depth": current_depth,
                        "priority": priority
                    }

                    if include_html:
                        page_result["html"] = content

                    result["crawled_pages"].append(page_result)

                    # Nếu đây là trang đầu tiên, lấy tiêu đề và nội dung cho kết quả chính
                    if current_url == url:
                        result["title"] = title
                        result["text"] = page_text

                    # Nếu chưa đạt đến độ sâu tối đa, tìm các liên kết
                    if current_depth < max_depth:
                        # Lấy tất cả các liên kết
                        links = []
                        for a_tag in soup.find_all("a", href=True):
                            href = a_tag.get("href", "").strip()
                            if href and not href.startswith(("#", "javascript:", "mailto:", "tel:")):
                                # Chuyển đổi URL tương đối thành tuyệt đối
                                full_url = urljoin(current_url, href)
                                # Làm sạch URL
                                clean_link = clean_url(full_url)
                                if is_valid_url(clean_link):
                                    links.append((clean_link, a_tag.get_text().strip()))

                        # Lọc và sắp xếp các liên kết theo độ ưu tiên
                        filtered_links = []
                        for link_url, link_text in links:
                            # Kiểm tra bộ lọc URL nếu có
                            if url_filter and not url_filter(link_url):
                                continue

                            # Tính toán độ ưu tiên cho URL
                            link_priority = calculate_url_priority(
                                link_url,
                                link_text,
                                current_url,
                                priority_keywords,
                                page_title=title,
                                page_content=page_text,
                                complexity_level=complexity_level
                            )

                            # Thêm vào danh sách liên kết đã lọc
                            filtered_links.append((link_url, link_priority))

                        # Sắp xếp liên kết theo độ ưu tiên
                        filtered_links.sort(key=lambda x: -x[1])

                        # Giới hạn số lượng liên kết để tránh quá tải
                        max_links = min(10, len(filtered_links))

                        # Thêm các liên kết vào hàng đợi
                        for link_url, link_priority in filtered_links[:max_links]:
                            # Thêm vào hàng đợi nếu chưa thăm
                            if link_url not in visited_urls:
                                url_queue.append((link_url, current_depth + 1, link_priority))
                                result["links"].append(link_url)

                    # Kiểm tra và tải xuống các file nếu cần
                    if download_files and file_processor:
                        # Tìm các liên kết đến file
                        file_links = []
                        for a_tag in soup.find_all("a", href=True):
                            href = a_tag.get("href", "").strip()
                            if href and not href.startswith(("#", "javascript:", "mailto:", "tel:")):
                                # Chuyển đổi URL tương đối thành tuyệt đối
                                file_url = urljoin(current_url, href)
                                # Kiểm tra xem có phải là file không
                                if file_processor.is_file_url(file_url):
                                    file_links.append(file_url)

                        # Tải xuống các file
                        downloaded_files = []
                        for file_url in file_links:
                            try:
                                file_info = file_processor.download_file(file_url)
                                if file_info.get("success"):
                                    downloaded_files.append(file_info)
                                    logger.info(f"Đã tải xuống file: {file_info.get('file_name')} ({file_info.get('size')} bytes)")
                            except Exception as e:
                                logger.warning(f"Lỗi khi tải xuống file từ {file_url}: {str(e)}")

                        # Thêm thông tin về các file đã tải xuống vào kết quả
                        if downloaded_files:
                            if "downloaded_files" not in result:
                                result["downloaded_files"] = []
                            result["downloaded_files"].extend(downloaded_files)

                    # Đánh dấu đã thăm và tăng số lượng
                    visited_urls.add(current_url)
                    crawled_count += 1

                    # Đóng trang
                    page.close()

                    # Thêm delay ngẫu nhiên để tránh bị chặn
                    time.sleep(random.uniform(1.0, 3.0))

                except Exception as e:
                    logger.error(f"Error crawling {current_url}: {str(e)}")
                    visited_urls.add(current_url)
                    continue

            # Đóng browser
            browser.close()

    except Exception as e:
        logger.error(f"Error in deep crawl: {str(e)}")
        return {
            "success": False,
            "url": url,
            "error": str(e)
        }

    # Cập nhật thống kê
    result["crawl_stats"].update({
        "visited_urls": len(visited_urls),
        "crawled_pages": crawled_count,
        "execution_time": time.time() - result["crawl_stats"]["start_time"]
    })

    # Thêm thông tin tổng hợp
    result["total_pages_crawled"] = crawled_count
    result["max_depth_reached"] = max(depth for _, depth, _ in url_queue) if url_queue else current_depth

    logger.info(f"Deep crawl completed: {crawled_count} pages crawled in {result['crawl_stats']['execution_time']:.2f}s")

    return result

def calculate_url_priority(url: str, link_text: str, current_url: str, priority_keywords: List[str],
                     page_title: str = "", page_content: str = "", complexity_level: str = "medium") -> int:
    """
    Tính toán độ ưu tiên cho URL dựa trên nhiều yếu tố.

    Args:
        url: URL cần tính toán độ ưu tiên
        link_text: Văn bản của liên kết
        current_url: URL hiện tại
        priority_keywords: Danh sách từ khóa ưu tiên
        page_title: Tiêu đề của trang chứa liên kết
        page_content: Nội dung của trang chứa liên kết
        complexity_level: Độ phức tạp của truy vấn ("low", "medium", "high")

    Returns:
        Điểm ưu tiên (cao hơn = ưu tiên hơn)
    """
    priority = 5  # Điểm ưu tiên mặc định

    # Ưu tiên URL cùng domain
    if get_domain(url) == get_domain(current_url):
        priority += 3

    # Phân tích URL
    url_parts = urlparse(url)
    url_path = url_parts.path.lower()
    url_params = url_parts.query.lower()

    # Kiểm tra định dạng file
    file_extensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt', '.csv', '.json', '.xml']
    for ext in file_extensions:
        if url_path.endswith(ext):
            # Ưu tiên cao cho các file có thể chứa thông tin quan trọng
            priority += 4
            break

    # Ưu tiên URL có chứa từ khóa trong đường dẫn
    for keyword in priority_keywords:
        keyword_lower = keyword.lower()
        if keyword_lower in url_path:
            priority += 2
        if keyword_lower in url_params:
            priority += 1

    # Ưu tiên URL có chứa từ khóa trong văn bản liên kết
    link_text_lower = link_text.lower()
    keyword_count = 0
    for keyword in priority_keywords:
        keyword_lower = keyword.lower()
        if keyword_lower in link_text_lower:
            priority += 2
            keyword_count += 1

    # Thưởng thêm nếu nhiều từ khóa xuất hiện trong văn bản liên kết
    if keyword_count > 1:
        priority += keyword_count

    # Phân tích ngữ cảnh của liên kết
    if page_title and page_content:
        # Kiểm tra nếu liên kết nằm gần các từ khóa trong nội dung
        content_snippet = ""
        try:
            # Tìm vị trí của link_text trong nội dung
            link_pos = page_content.lower().find(link_text.lower())
            if link_pos >= 0:
                # Lấy đoạn văn bản xung quanh liên kết (100 ký tự trước và sau)
                start = max(0, link_pos - 100)
                end = min(len(page_content), link_pos + len(link_text) + 100)
                content_snippet = page_content[start:end]

                # Kiểm tra từ khóa trong đoạn văn bản này
                for keyword in priority_keywords:
                    if keyword.lower() in content_snippet.lower():
                        priority += 1
        except:
            pass

    # Điều chỉnh ưu tiên dựa trên độ phức tạp của truy vấn
    if complexity_level == "high":
        # Với truy vấn phức tạp, ưu tiên các trang có nhiều thông tin chi tiết
        if any(indicator in url_path for indicator in ['/research/', '/paper/', '/study/', '/article/', '/detail/', '/full/']):
            priority += 3
        # Ưu tiên các trang có thể chứa thông tin chuyên sâu
        if any(domain in url for domain in ['.edu', '.gov', '.org', 'wikipedia.org', 'scholar.google']):
            priority += 2
    elif complexity_level == "low":
        # Với truy vấn đơn giản, ưu tiên các trang tổng quan
        if any(indicator in url_path for indicator in ['/overview/', '/summary/', '/introduction/', '/basic/']):
            priority += 2
        # Ưu tiên các trang có thông tin ngắn gọn, dễ hiểu
        if 'wiki' in url or 'faq' in url_path:
            priority += 2

    # Giảm ưu tiên cho các URL có khả năng là trang không liên quan
    irrelevant_patterns = [
        '/tag/', '/category/', '/author/', '/search/', '/page/', '/comment/', '/archive/',
        '/login', '/register', '/signup', '/cart', '/checkout', '/privacy', '/terms',
        '/advertisement', '/ads/', '/404', '/error', '/captcha', '/sitemap', '/rss',
        '/feed', '/wp-content', '/wp-includes', '/cdn-cgi', '/static/', '/media/',
        '/share', '/print', '/email', '/subscribe', '/newsletter'
    ]

    for pattern in irrelevant_patterns:
        if pattern in url_path:
            priority -= 2

    # Giảm ưu tiên cho URL có quá nhiều tham số (có thể là trang động hoặc tracking)
    if len(url_params) > 50:
        priority -= 2

    # Giảm ưu tiên cho URL có chứa các tham số tracking phổ biến
    tracking_params = ['utm_', 'fbclid', 'gclid', 'msclkid', 'ref=', 'source=', 'campaign=']
    for param in tracking_params:
        if param in url_params:
            priority -= 1

    # Giới hạn điểm ưu tiên trong khoảng 1-10
    return max(1, min(10, priority))
