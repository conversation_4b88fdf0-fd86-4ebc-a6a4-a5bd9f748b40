#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module tích hợp AnswerQualityEvaluator vào WebSearchAgentLocal.

Module này cung cấp các phương thức để tích hợp AnswerQualityEvaluator vào WebSearchAgentLocal
để đánh giá chất lượng câu trả lời và quyết định có cần tìm kiếm thêm hay không.
"""

import time
import logging
from typing import Dict, List, Any, Optional, Tuple, Union

from ..utils.structured_logging import get_logger
from ..agents.answer_quality_evaluator import AnswerQualityEvaluator

# Thiết lập logging
logger = get_logger(__name__)

def integrate_answer_quality_evaluator(agent, config: Optional[Dict[str, Any]] = None) -> None:
    """
    Tích hợp AnswerQualityEvaluator vào WebSearchAgentLocal.
    
    Args:
        agent: WebSearchAgentLocal instance
        config: <PERSON><PERSON><PERSON> hình cho AnswerQualityEvaluator
    """
    # C<PERSON>u hình mặc định
    default_config = {
        "use_model_evaluation": False,
        "use_heuristics": True,
        "language": "auto",
        "evaluation_thresholds": {
            "relevance": 5.0,
            "factual_accuracy": 6.0,
            "completeness": 5.0,
            "coherence": 4.0,
            "conciseness": 4.0,
            "source_attribution": 4.0,
            "overall": 5.0,
            "need_more_search": 4.0
        },
        "model_provider": "openai",
        "model_name": "gpt-4o"
    }
    
    # Kết hợp cấu hình mặc định và cấu hình được cung cấp
    config = {**default_config, **(config or {})}
    
    # Xác định ngôn ngữ
    if config["language"] == "auto":
        # Sử dụng ngôn ngữ mặc định của agent
        if hasattr(agent, "language") and agent.language:
            config["language"] = agent.language
        else:
            config["language"] = "en"
    
    # Khởi tạo AnswerQualityEvaluator
    try:
        answer_evaluator = AnswerQualityEvaluator(
            config=config,
            use_model_evaluation=config["use_model_evaluation"],
            use_heuristics=config["use_heuristics"],
            language=config["language"]
        )
        
        # Gán AnswerQualityEvaluator cho agent
        agent.answer_evaluator = answer_evaluator
        agent.use_answer_evaluator = True
        agent.answer_evaluator_config = config
        
        # Patch phương thức search của agent
        original_search = agent.search
        
        def patched_search(
            query: str,
            num_results: int = 10,
            language: str = "auto",
            search_method: str = "auto",
            get_content: bool = False,
            force_refresh: bool = False,
            evaluate_answer: bool = False,
            **kwargs
        ) -> Dict[str, Any]:
            """
            Phương thức search đã được patch để hỗ trợ đánh giá câu trả lời.
            """
            # Nếu không sử dụng đánh giá câu trả lời, gọi phương thức search gốc
            if not evaluate_answer:
                return original_search(
                    query=query,
                    num_results=num_results,
                    language=language,
                    search_method=search_method,
                    get_content=get_content,
                    force_refresh=force_refresh,
                    evaluate_answer=False,
                    **kwargs
                )
            
            # Đảm bảo get_content=True khi evaluate_answer=True
            if evaluate_answer and not get_content:
                get_content = True
                logger.info("Automatically enabling get_content for answer evaluation")
            
            # Gọi phương thức search gốc
            results = original_search(
                query=query,
                num_results=num_results,
                language=language,
                search_method=search_method,
                get_content=get_content,
                force_refresh=force_refresh,
                evaluate_answer=False,  # Tránh đệ quy
                **kwargs
            )
            
            # Nếu tìm kiếm thất bại, trả về kết quả
            if not results.get("success", False):
                return results
            
            # Đánh giá câu trả lời
            try:
                # Tạo câu trả lời đơn giản từ kết quả
                answer = create_simple_answer(results.get("results", []), query)
                
                # Đánh giá câu trả lời
                answer_evaluation = evaluate_answer_quality(
                    agent=agent,
                    query=query,
                    answer=answer,
                    results=results.get("results", [])
                )
                
                # Thêm đánh giá câu trả lời vào kết quả
                results["answer_evaluation"] = answer_evaluation
                results["answer"] = answer
                
                # Thực hiện tìm kiếm bổ sung nếu cần
                if answer_evaluation.get("need_more_search", False):
                    logger.info("Answer quality is low, performing additional search")
                    
                    # Thực hiện tìm kiếm bổ sung
                    additional_results = perform_additional_search(
                        agent=agent,
                        query=query,
                        original_results=results,
                        **kwargs
                    )
                    
                    # Cập nhật kết quả
                    if additional_results:
                        results = additional_results
            except Exception as e:
                logger.error(f"Error evaluating answer quality: {str(e)}")
                # Thêm thông tin lỗi vào kết quả
                results["answer_evaluation_error"] = str(e)
            
            return results
        
        # Thay thế phương thức search
        agent.search = patched_search
        
        # Thêm phương thức evaluate_answer_quality vào agent
        agent.evaluate_answer_quality = lambda query, answer, results: evaluate_answer_quality(
            agent=agent,
            query=query,
            answer=answer,
            results=results
        )
        
        logger.info("AnswerQualityEvaluator integrated successfully")
    except Exception as e:
        logger.error(f"Error integrating AnswerQualityEvaluator: {str(e)}")
        agent.use_answer_evaluator = False

def evaluate_answer_quality(
    agent,
    query: str,
    answer: str,
    results: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """
    Đánh giá chất lượng câu trả lời.
    
    Args:
        agent: WebSearchAgentLocal instance
        query: Câu hỏi
        answer: Câu trả lời
        results: Danh sách kết quả tìm kiếm
        
    Returns:
        Dict[str, Any]: Kết quả đánh giá
    """
    # Kiểm tra AnswerQualityEvaluator
    if not hasattr(agent, "answer_evaluator") or not agent.answer_evaluator:
        logger.warning("AnswerQualityEvaluator not available, using default evaluation")
        return {
            "quality_level": "unknown",
            "quality_score": 5.0,
            "need_more_search": False,
            "evaluation_method": "default"
        }
    
    # Chuẩn bị tài liệu
    documents = []
    for result in results:
        document = {
            "title": result.get("title", ""),
            "url": result.get("url", ""),
            "content": result.get("content", result.get("snippet", ""))
        }
        documents.append(document)
    
    # Đánh giá câu trả lời
    try:
        evaluation = agent.answer_evaluator.evaluate_answer(
            question=query,
            answer=answer,
            documents=documents
        )
        
        # Xác định mức độ chất lượng
        quality_score = evaluation.get("overall_score", 0.0)
        if quality_score >= 7.0:
            quality_level = "high"
        elif quality_score >= 5.0:
            quality_level = "medium"
        else:
            quality_level = "low"
        
        # Tạo kết quả đánh giá
        result = {
            "quality_level": quality_level,
            "quality_score": quality_score,
            "need_more_search": evaluation.get("need_more_search", False),
            "evaluation_method": "answer_evaluator",
            "metrics": evaluation.get("metrics", {}),
            "evaluation_time": evaluation.get("evaluation_time", 0.0)
        }
        
        return result
    except Exception as e:
        logger.error(f"Error in evaluate_answer_quality: {str(e)}")
        return {
            "quality_level": "unknown",
            "quality_score": 5.0,
            "need_more_search": False,
            "evaluation_method": "error",
            "error": str(e)
        }

def create_simple_answer(results: List[Dict[str, Any]], query: str) -> str:
    """
    Tạo câu trả lời đơn giản từ kết quả tìm kiếm.
    
    Args:
        results: Danh sách kết quả tìm kiếm
        query: Câu hỏi
        
    Returns:
        str: Câu trả lời đơn giản
    """
    if not results:
        return f"Không tìm thấy kết quả cho câu hỏi: {query}"
    
    # Tạo câu trả lời từ các kết quả
    answer_parts = []
    
    # Thêm phần giới thiệu
    answer_parts.append(f"Dựa trên kết quả tìm kiếm cho câu hỏi '{query}', tôi tìm thấy các thông tin sau:")
    
    # Thêm thông tin từ các kết quả
    for i, result in enumerate(results[:5]):  # Chỉ sử dụng 5 kết quả đầu tiên
        title = result.get("title", "")
        content = result.get("content", result.get("snippet", ""))
        url = result.get("url", "")
        
        if not content:
            continue
        
        # Giới hạn độ dài nội dung
        if len(content) > 500:
            content = content[:500] + "..."
        
        answer_parts.append(f"\n{i+1}. {title}")
        answer_parts.append(f"{content}")
        answer_parts.append(f"Nguồn: {url}")
    
    # Thêm phần kết luận
    answer_parts.append("\nTrên đây là thông tin tôi tìm thấy. Nếu bạn cần thông tin chi tiết hơn, vui lòng cho tôi biết.")
    
    return "\n".join(answer_parts)

def perform_additional_search(
    agent,
    query: str,
    original_results: Dict[str, Any],
    **kwargs
) -> Dict[str, Any]:
    """
    Thực hiện tìm kiếm bổ sung khi chất lượng câu trả lời thấp.
    
    Args:
        agent: WebSearchAgentLocal instance
        query: Câu hỏi
        original_results: Kết quả tìm kiếm ban đầu
        **kwargs: Các tham số khác
        
    Returns:
        Dict[str, Any]: Kết quả tìm kiếm bổ sung
    """
    # Tạo truy vấn mở rộng
    expanded_query = expand_query(query, original_results)
    
    # Thực hiện tìm kiếm với truy vấn mở rộng
    try:
        # Tăng số lượng kết quả
        num_results = kwargs.get("num_results", 10)
        num_results = min(20, num_results * 2)  # Tăng gấp đôi nhưng không quá 20
        
        # Thực hiện tìm kiếm
        additional_results = agent.search(
            query=expanded_query,
            num_results=num_results,
            get_content=True,
            force_refresh=True,
            evaluate_answer=False,  # Tránh đệ quy
            deep_crawl=True,  # Kích hoạt deep crawl
            **kwargs
        )
        
        # Gộp kết quả
        merged_results = merge_search_results(
            original_results=original_results,
            additional_results=additional_results
        )
        
        # Đánh giá lại câu trả lời
        answer = create_simple_answer(merged_results.get("results", []), query)
        answer_evaluation = evaluate_answer_quality(
            agent=agent,
            query=query,
            answer=answer,
            results=merged_results.get("results", [])
        )
        
        # Cập nhật kết quả
        merged_results["answer_evaluation"] = answer_evaluation
        merged_results["answer"] = answer
        merged_results["additional_search"] = {
            "expanded_query": expanded_query,
            "original_quality": original_results.get("answer_evaluation", {}).get("quality_level", "unknown"),
            "new_quality": answer_evaluation.get("quality_level", "unknown")
        }
        
        return merged_results
    except Exception as e:
        logger.error(f"Error in perform_additional_search: {str(e)}")
        return original_results

def expand_query(query: str, original_results: Dict[str, Any]) -> str:
    """
    Mở rộng truy vấn dựa trên kết quả tìm kiếm ban đầu.
    
    Args:
        query: Câu hỏi gốc
        original_results: Kết quả tìm kiếm ban đầu
        
    Returns:
        str: Truy vấn mở rộng
    """
    # Trích xuất từ khóa từ kết quả
    keywords = extract_keywords_from_results(original_results.get("results", []))
    
    # Mở rộng truy vấn
    if keywords:
        expanded_query = f"{query} {' '.join(keywords[:3])}"
    else:
        expanded_query = f"{query} detailed information"
    
    return expanded_query

def extract_keywords_from_results(results: List[Dict[str, Any]]) -> List[str]:
    """
    Trích xuất từ khóa từ kết quả tìm kiếm.
    
    Args:
        results: Danh sách kết quả tìm kiếm
        
    Returns:
        List[str]: Danh sách từ khóa
    """
    # Tạo danh sách từ khóa
    keywords = []
    
    # Trích xuất từ khóa từ tiêu đề
    for result in results:
        title = result.get("title", "")
        if title:
            # Tách từ
            words = title.split()
            # Thêm từ có độ dài > 3 vào danh sách từ khóa
            keywords.extend([word for word in words if len(word) > 3])
    
    # Loại bỏ trùng lặp
    unique_keywords = list(set(keywords))
    
    return unique_keywords

def merge_search_results(
    original_results: Dict[str, Any],
    additional_results: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Gộp kết quả tìm kiếm ban đầu và kết quả tìm kiếm bổ sung.
    
    Args:
        original_results: Kết quả tìm kiếm ban đầu
        additional_results: Kết quả tìm kiếm bổ sung
        
    Returns:
        Dict[str, Any]: Kết quả tìm kiếm đã gộp
    """
    # Tạo kết quả gộp
    merged_results = original_results.copy()
    
    # Gộp danh sách kết quả
    original_urls = set(result.get("url", "") for result in original_results.get("results", []))
    merged_results_list = original_results.get("results", []).copy()
    
    # Thêm kết quả bổ sung không trùng lặp
    for result in additional_results.get("results", []):
        url = result.get("url", "")
        if url and url not in original_urls:
            merged_results_list.append(result)
            original_urls.add(url)
    
    # Cập nhật danh sách kết quả
    merged_results["results"] = merged_results_list
    
    return merged_results
