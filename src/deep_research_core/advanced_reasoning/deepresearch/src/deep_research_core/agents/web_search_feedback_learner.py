"""
Tự động học và cải thiện dựa trên phản hồi người dùng.

Module này giúp WebSearchAgent tự động học và cải thiện 
dựa trên phản hồi của người dùng.
"""

import os
import time
import json
import logging
import sqlite3
import pickle
from typing import Dict, Any, List, Optional, Tuple, Union
from datetime import datetime
import numpy as np
from collections import defaultdict

from ..utils.structured_logging import get_logger

# Khởi tạo logger
logger = get_logger(__name__)

class WebSearchFeedbackLearner:
    """
    WebSearchFeedbackLearner giúp WebSearchAgent tự học 
    và cải thiện dựa trên phản hồi của người dùng.
    """
    
    def __init__(
        self,
        feedback_db_path: Optional[str] = None,
        learning_rate: float = 0.05,
        min_confidence: float = 0.4,
        min_feedback_samples: int = 5,
        max_features: int = 100,
        auto_apply: bool = True,
        verbose: bool = False
    ):
        """
        Khởi tạo WebSearchFeedbackLearner.
        
        Args:
            feedback_db_path: Đường dẫn đến file SQLite lưu feedback
            learning_rate: Tốc độ học
            min_confidence: Độ tin cậy tối thiểu
            min_feedback_samples: Số lượng mẫu tối thiểu để áp dụng học
            max_features: Số lượng đặc trưng tối đa
            auto_apply: Tự động áp dụng cải tiến
            verbose: Bật chế độ verbose
        """
        self.learning_rate = learning_rate
        self.min_confidence = min_confidence
        self.min_feedback_samples = min_feedback_samples
        self.max_features = max_features
        self.auto_apply = auto_apply
        self.verbose = verbose
        
        # Khởi tạo cơ sở dữ liệu feedback
        self.feedback_db_path = feedback_db_path
        if feedback_db_path:
            self._init_feedback_db()
        else:
            self.conn = None
        
        # Khởi tạo các mô hình
        self.engine_weights = defaultdict(lambda: 1.0)
        self.query_patterns = {}
        self.failed_patterns = set()
        self.successful_patterns = set()
        self.user_preferences = defaultdict(dict)
        
        # Các đặc trưng đã học được
        self.learned_features = {
            "engine_preferences": {},
            "query_transformations": {},
            "result_quality_factors": {},
            "language_preferences": {},
            "time_preferences": {}
        }
        
        # Khởi tạo trọng số cho thuật toán ranking
        self.ranking_weights = {
            "relevance": 0.4,
            "recency": 0.2,
            "quality": 0.3,
            "user_preference": 0.1
        }
        
        # Thống kê
        self.stats = {
            "total_feedback": 0,
            "positive_feedback": 0,
            "negative_feedback": 0,
            "applied_improvements": 0,
            "feedback_per_engine": {},
            "feedback_per_query_type": {}
        }
        
        logger.info(f"Đã khởi tạo WebSearchFeedbackLearner")
        
    def _init_feedback_db(self):
        """
        Khởi tạo cơ sở dữ liệu feedback.
        """
        try:
            # Đảm bảo thư mục tồn tại
            os.makedirs(os.path.dirname(self.feedback_db_path), exist_ok=True)
            
            # Kết nối đến SQLite
            self.conn = sqlite3.connect(self.feedback_db_path)
            cursor = self.conn.cursor()
            
            # Tạo bảng feedback
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS search_feedback (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                query TEXT,
                engine TEXT,
                language TEXT,
                timestamp REAL,
                results_count INTEGER,
                feedback_score INTEGER,
                feedback_type TEXT,
                user_comment TEXT,
                search_params TEXT,
                selected_result INTEGER,
                session_id TEXT
            )
            ''')
            
            # Tạo bảng cho learned_features
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS learned_features (
                feature_type TEXT,
                feature_key TEXT,
                feature_value REAL,
                confidence REAL,
                samples INTEGER,
                last_updated REAL,
                PRIMARY KEY (feature_type, feature_key)
            )
            ''')
            
            # Tạo bảng cho engine weights
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS engine_weights (
                engine TEXT PRIMARY KEY,
                weight REAL,
                confidence REAL,
                samples INTEGER,
                last_updated REAL
            )
            ''')
            
            # Tạo chỉ mục
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_query ON search_feedback(query)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_engine ON search_feedback(engine)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_feedback_score ON search_feedback(feedback_score)")
            
            self.conn.commit()
            logger.info(f"Đã khởi tạo cơ sở dữ liệu feedback tại {self.feedback_db_path}")
            
            # Tải các đặc trưng đã học được
            self._load_learned_features()
            
        except Exception as e:
            logger.error(f"Lỗi khởi tạo cơ sở dữ liệu feedback: {str(e)}")
            self.conn = None
    
    def _load_learned_features(self):
        """
        Tải các đặc trưng đã học được từ cơ sở dữ liệu.
        """
        if not self.conn:
            return
            
        try:
            cursor = self.conn.cursor()
            
            # Tải engine weights
            cursor.execute("SELECT engine, weight FROM engine_weights")
            for engine, weight in cursor.fetchall():
                self.engine_weights[engine] = weight
            
            # Tải learned features
            cursor.execute("SELECT feature_type, feature_key, feature_value FROM learned_features")
            for feature_type, feature_key, feature_value in cursor.fetchall():
                if feature_type in self.learned_features:
                    self.learned_features[feature_type][feature_key] = feature_value
            
            logger.info(f"Đã tải {len(self.engine_weights)} engine weights và {sum(len(f) for f in self.learned_features.values())} learned features")
            
        except Exception as e:
            logger.error(f"Lỗi tải learned features: {str(e)}")
    
    def add_feedback(
        self,
        query: str,
        search_result: Dict[str, Any],
        feedback_score: int,
        feedback_type: str = "result_quality",
        user_comment: Optional[str] = None,
        selected_result: Optional[int] = None,
        session_id: Optional[str] = None
    ) -> bool:
        """
        Thêm phản hồi người dùng.
        
        Args:
            query: Truy vấn tìm kiếm
            search_result: Kết quả tìm kiếm
            feedback_score: Điểm đánh giá (-2 đến 2)
            feedback_type: Loại phản hồi
            user_comment: Bình luận của người dùng
            selected_result: Chỉ số kết quả được chọn
            session_id: ID phiên làm việc
            
        Returns:
            True nếu thành công, False nếu thất bại
        """
        if not query or not search_result:
            return False
            
        try:
            # Trích xuất thông tin từ kết quả tìm kiếm
            engine = search_result.get("engine", "unknown")
            language = search_result.get("language", "en")
            results_count = len(search_result.get("results", []))
            search_params = json.dumps({
                k: v for k, v in search_result.items() 
                if k not in ["results", "success", "error"]
            })
            
            timestamp = time.time()
            
            # Thêm vào cơ sở dữ liệu
            if self.conn:
                cursor = self.conn.cursor()
                cursor.execute(
                    """
                    INSERT INTO search_feedback
                    (query, engine, language, timestamp, results_count, feedback_score,
                    feedback_type, user_comment, search_params, selected_result, session_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """,
                    (
                        query, engine, language, timestamp, results_count, feedback_score,
                        feedback_type, user_comment, search_params, selected_result, session_id
                    )
                )
                self.conn.commit()
            
            # Cập nhật thống kê
            self.stats["total_feedback"] += 1
            if feedback_score > 0:
                self.stats["positive_feedback"] += 1
            elif feedback_score < 0:
                self.stats["negative_feedback"] += 1
                
            # Cập nhật theo engine
            if engine not in self.stats["feedback_per_engine"]:
                self.stats["feedback_per_engine"][engine] = 0
            self.stats["feedback_per_engine"][engine] += 1
            
            # Cập nhật học tức thời nếu được chỉ định
            if self.auto_apply:
                self._update_models_from_feedback(
                    query, engine, language, feedback_score, 
                    feedback_type, search_result, selected_result
                )
            
            logger.info(f"Đã thêm phản hồi cho truy vấn '{query}' với điểm {feedback_score}")
            return True
            
        except Exception as e:
            logger.error(f"Lỗi thêm phản hồi: {str(e)}")
            return False
    
    def _update_models_from_feedback(
        self,
        query: str,
        engine: str,
        language: str,
        feedback_score: int,
        feedback_type: str,
        search_result: Dict[str, Any],
        selected_result: Optional[int]
    ):
        """
        Cập nhật các mô hình từ phản hồi.
        
        Args:
            Thông tin phản hồi
        """
        try:
            # Cập nhật trọng số engine
            self._update_engine_weight(engine, feedback_score)
            
            # Cập nhật đặc trưng ngôn ngữ
            self._update_language_preference(language, feedback_score)
            
            # Xử lý các truy vấn thất bại hoặc thành công
            if feedback_score <= -1:
                self._analyze_failed_query(query, engine, search_result)
            elif feedback_score >= 1:
                self._analyze_successful_query(query, engine, search_result)
            
            # Cập nhật đặc trưng kết quả nếu có kết quả được chọn
            if selected_result is not None and 0 <= selected_result < len(search_result.get("results", [])):
                self._update_result_quality_factors(
                    search_result["results"][selected_result], 
                    feedback_score
                )
                
            # Lưu các đặc trưng đã học
            if self.conn:
                self._save_learned_features()
                
        except Exception as e:
            logger.error(f"Lỗi cập nhật mô hình từ phản hồi: {str(e)}")
    
    def _update_engine_weight(self, engine: str, feedback_score: int):
        """
        Cập nhật trọng số cho công cụ tìm kiếm.
        
        Args:
            engine: Công cụ tìm kiếm
            feedback_score: Điểm phản hồi
        """
        # Chuyển đổi feedback_score từ -2..2 sang -1..1
        normalized_score = feedback_score / 2.0
        
        # Cập nhật trọng số với học dần dần
        current_weight = self.engine_weights[engine]
        new_weight = current_weight + self.learning_rate * normalized_score
        
        # Đảm bảo trọng số không âm và có giới hạn trên
        new_weight = max(0.1, min(2.0, new_weight))
        
        self.engine_weights[engine] = new_weight
        
        if self.verbose:
            logger.info(f"Cập nhật trọng số cho engine {engine}: {current_weight:.2f} -> {new_weight:.2f}")
    
    def _update_language_preference(self, language: str, feedback_score: int):
        """
        Cập nhật preference cho ngôn ngữ.
        
        Args:
            language: Mã ngôn ngữ
            feedback_score: Điểm phản hồi
        """
        if not language:
            return
            
        normalized_score = feedback_score / 2.0
        
        if language not in self.learned_features["language_preferences"]:
            self.learned_features["language_preferences"][language] = 1.0
            
        current_value = self.learned_features["language_preferences"][language]
        new_value = current_value + self.learning_rate * normalized_score
        new_value = max(0.1, min(2.0, new_value))
        
        self.learned_features["language_preferences"][language] = new_value
    
    def _analyze_failed_query(self, query: str, engine: str, search_result: Dict[str, Any]):
        """
        Phân tích truy vấn thất bại.
        
        Args:
            query: Truy vấn
            engine: Công cụ tìm kiếm
            search_result: Kết quả tìm kiếm
        """
        # Lưu pattern thất bại
        self.failed_patterns.add(query.lower())
        
        # Tạo biến đổi truy vấn tiềm năng
        # Ví dụ: thêm dấu ngoặc kép, thêm từ khóa bổ sung, etc.
        potential_transformations = {
            f'"{query}"': 0.0,
            f'{query} guide': 0.0,
            f'{query} tutorial': 0.0,
            f'{query} example': 0.0
        }
        
        for transform, _ in potential_transformations.items():
            if transform not in self.learned_features["query_transformations"]:
                self.learned_features["query_transformations"][transform] = 0.0
    
    def _analyze_successful_query(self, query: str, engine: str, search_result: Dict[str, Any]):
        """
        Phân tích truy vấn thành công.
        
        Args:
            query: Truy vấn
            engine: Công cụ tìm kiếm
            search_result: Kết quả tìm kiếm
        """
        # Lưu pattern thành công
        self.successful_patterns.add(query.lower())
        
        # Tìm kiếm các đặc trưng giúp truy vấn thành công
        results = search_result.get("results", [])
        if results:
            result_count = len(results)
            if result_count > 0:
                # Cập nhật đặc trưng số lượng kết quả
                key = f"min_results_{engine}"
                if key not in self.learned_features["result_quality_factors"]:
                    self.learned_features["result_quality_factors"][key] = result_count
                else:
                    # Lấy giá trị nhỏ hơn
                    current = self.learned_features["result_quality_factors"][key]
                    self.learned_features["result_quality_factors"][key] = min(current, result_count)
    
    def _update_result_quality_factors(self, result: Dict[str, Any], feedback_score: int):
        """
        Cập nhật các yếu tố chất lượng kết quả.
        
        Args:
            result: Kết quả tìm kiếm
            feedback_score: Điểm phản hồi
        """
        normalized_score = feedback_score / 2.0
        
        # Trích xuất domain
        url = result.get("url", "")
        if url:
            import re
            from urllib.parse import urlparse
            
            try:
                domain = urlparse(url).netloc
                
                # Cập nhật độ tin cậy của domain
                key = f"domain_trust_{domain}"
                if key not in self.learned_features["result_quality_factors"]:
                    self.learned_features["result_quality_factors"][key] = 1.0 + normalized_score
                else:
                    current = self.learned_features["result_quality_factors"][key]
                    new_value = current + self.learning_rate * normalized_score
                    self.learned_features["result_quality_factors"][key] = max(0.1, min(2.0, new_value))
            except:
                pass
    
    def _save_learned_features(self):
        """
        Lưu các đặc trưng đã học vào cơ sở dữ liệu.
        """
        if not self.conn:
            return
            
        try:
            cursor = self.conn.cursor()
            
            # Lưu engine weights
            for engine, weight in self.engine_weights.items():
                cursor.execute(
                    """
                    INSERT OR REPLACE INTO engine_weights
                    (engine, weight, confidence, samples, last_updated)
                    VALUES (?, ?, ?, ?, ?)
                    """,
                    (engine, weight, 1.0, 1, time.time())
                )
            
            # Lưu learned features
            for feature_type, features in self.learned_features.items():
                for feature_key, feature_value in features.items():
                    cursor.execute(
                        """
                        INSERT OR REPLACE INTO learned_features
                        (feature_type, feature_key, feature_value, confidence, samples, last_updated)
                        VALUES (?, ?, ?, ?, ?, ?)
                        """,
                        (feature_type, feature_key, feature_value, 1.0, 1, time.time())
                    )
            
            self.conn.commit()
            
        except Exception as e:
            logger.error(f"Lỗi lưu learned features: {str(e)}")
    
    def apply_learning_to_query(
        self, 
        query: str, 
        engine: Optional[str] = None,
        language: Optional[str] = None
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Áp dụng các đặc trưng đã học để cải thiện truy vấn.
        
        Args:
            query: Truy vấn gốc
            engine: Công cụ tìm kiếm
            language: Ngôn ngữ
            
        Returns:
            Tuple (truy vấn đã cải thiện, params bổ sung)
        """
        improved_query = query
        additional_params = {}
        
        try:
            # Kiểm tra xem truy vấn có trong các pattern thất bại
            query_lower = query.lower()
            if query_lower in self.failed_patterns:
                # Tìm kiếm biến đổi tốt nhất
                best_transform = None
                best_score = -1
                
                for transform, score in self.learned_features["query_transformations"].items():
                    if transform.startswith(query_lower) and score > best_score:
                        best_transform = transform
                        best_score = score
                
                if best_transform and best_score > self.min_confidence:
                    improved_query = best_transform
            
            # Chọn engine tốt nhất nếu không chỉ định
            if not engine:
                best_engine = None
                best_weight = -1
                
                for eng, weight in self.engine_weights.items():
                    if weight > best_weight:
                        best_engine = eng
                        best_weight = weight
                
                if best_engine:
                    additional_params["engine"] = best_engine
            
            # Áp dụng ngôn ngữ ưa thích nếu không chỉ định
            if not language:
                best_language = None
                best_score = -1
                
                for lang, score in self.learned_features["language_preferences"].items():
                    if score > best_score:
                        best_language = lang
                        best_score = score
                
                if best_language and best_score > self.min_confidence:
                    additional_params["language"] = best_language
            
            if improved_query != query or additional_params:
                logger.info(f"Đã cải thiện truy vấn: '{query}' -> '{improved_query}' với {additional_params}")
                self.stats["applied_improvements"] += 1
            
            return improved_query, additional_params
            
        except Exception as e:
            logger.error(f"Lỗi áp dụng learning: {str(e)}")
            return query, {}
    
    def rerank_results(
        self, 
        query: str, 
        results: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Sắp xếp lại kết quả dựa trên learning.
        
        Args:
            query: Truy vấn
            results: Danh sách kết quả
            
        Returns:
            Danh sách kết quả đã sắp xếp lại
        """
        if not results:
            return results
            
        try:
            # Tính điểm cho mỗi kết quả
            scored_results = []
            
            for result in results:
                # Điểm cơ bản từ kết quả
                base_score = result.get("relevance_score", 0.5)
                
                # Điểm tin cậy domain
                domain_score = 0.5
                url = result.get("url", "")
                if url:
                    from urllib.parse import urlparse
                    domain = urlparse(url).netloc
                    domain_key = f"domain_trust_{domain}"
                    domain_score = self.learned_features["result_quality_factors"].get(domain_key, 0.5)
                
                # Tính điểm tổng hợp
                final_score = (
                    base_score * self.ranking_weights["relevance"] +
                    domain_score * self.ranking_weights["quality"]
                )
                
                scored_results.append((result, final_score))
            
            # Sắp xếp theo điểm giảm dần
            scored_results.sort(key=lambda x: x[1], reverse=True)
            
            # Trả về kết quả đã sắp xếp
            return [item[0] for item in scored_results]
            
        except Exception as e:
            logger.error(f"Lỗi rerank kết quả: {str(e)}")
            return results
    
    def get_learning_stats(self) -> Dict[str, Any]:
        """
        Lấy thống kê về quá trình học.
        
        Returns:
            Dictionary chứa thống kê
        """
        return {
            **self.stats,
            "learned_engines": len(self.engine_weights),
            "failed_patterns": len(self.failed_patterns),
            "successful_patterns": len(self.successful_patterns),
            "query_transformations": len(self.learned_features["query_transformations"]),
            "result_quality_factors": len(self.learned_features["result_quality_factors"]),
            "language_preferences": len(self.learned_features["language_preferences"])
        }
    
    def export_learned_model(self, path: str) -> bool:
        """
        Xuất mô hình đã học ra file.
        
        Args:
            path: Đường dẫn file
            
        Returns:
            True nếu thành công, False nếu thất bại
        """
        try:
            model_data = {
                "engine_weights": dict(self.engine_weights),
                "learned_features": self.learned_features,
                "failed_patterns": list(self.failed_patterns),
                "successful_patterns": list(self.successful_patterns),
                "ranking_weights": self.ranking_weights,
                "stats": self.stats,
                "timestamp": time.time()
            }
            
            with open(path, 'wb') as f:
                pickle.dump(model_data, f)
                
            logger.info(f"Đã xuất mô hình học ra {path}")
            return True
            
        except Exception as e:
            logger.error(f"Lỗi xuất mô hình: {str(e)}")
            return False
    
    def import_learned_model(self, path: str) -> bool:
        """
        Nhập mô hình đã học từ file.
        
        Args:
            path: Đường dẫn file
            
        Returns:
            True nếu thành công, False nếu thất bại
        """
        try:
            with open(path, 'rb') as f:
                model_data = pickle.load(f)
                
            # Cập nhật mô hình
            self.engine_weights = defaultdict(lambda: 1.0, model_data["engine_weights"])
            self.learned_features = model_data["learned_features"]
            self.failed_patterns = set(model_data["failed_patterns"])
            self.successful_patterns = set(model_data["successful_patterns"])
            self.ranking_weights = model_data["ranking_weights"]
            self.stats = model_data["stats"]
            
            # Lưu vào cơ sở dữ liệu nếu có
            if self.conn:
                self._save_learned_features()
                
            logger.info(f"Đã nhập mô hình học từ {path}")
            return True
            
        except Exception as e:
            logger.error(f"Lỗi nhập mô hình: {str(e)}")
            return False
    
    def close(self):
        """
        Đóng WebSearchFeedbackLearner và giải phóng tài nguyên.
        """
        if self.conn:
            self.conn.close()
            self.conn = None
            
        logger.info("Đã đóng WebSearchFeedbackLearner")
    
    def __del__(self):
        """
        Destructor.
        """
        self.close() 