"""
Module tích hợp <PERSON>lee cho WebSearchAgent nâng cao.
"""

import os
import logging
import subprocess
import json
import time
import tempfile
from typing import Dict, Any, List, Optional, Union
import requests
from pathlib import Path

logger = logging.getLogger(__name__)

class CrawleeIntegration:
    """Tích hợp với <PERSON>lee cho các chức năng crawling nâng cao."""
    
    def __init__(self, base_dir: Optional[str] = None, use_docker: bool = True):
        """
        Khởi tạo tích hợp Crawlee.
        
        Args:
            base_dir: <PERSON><PERSON><PERSON> mục cơ sở để lưu trữ kết quả crawling
            use_docker: Sử dụng Docker để chạy Crawlee
        """
        self.base_dir = base_dir or os.path.join(os.path.expanduser("~"), ".deep_research_core", "crawlee")
        os.makedirs(self.base_dir, exist_ok=True)
        
        self.use_docker = use_docker
        self.crawler_path = os.path.join(os.path.dirname(__file__), "crawlers")
        self.results_dir = os.path.join(self.base_dir, "results")
        os.makedirs(self.results_dir, exist_ok=True)
        
        # Kiểm tra cài đặt Node.js
        self.nodejs_available = self._check_nodejs()
        
        # Kiểm tra cài đặt Docker
        self.docker_available = self._check_docker() if use_docker else False
        
        if not self.nodejs_available and not self.docker_available:
            logger.warning("Không tìm thấy Node.js hoặc Docker. Chức năng Crawlee sẽ bị hạn chế.")
    
    def _check_nodejs(self) -> bool:
        """Kiểm tra cài đặt Node.js."""
        try:
            result = subprocess.run(["node", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"Node.js phiên bản {result.stdout.strip()} đã được cài đặt")
                return True
        except Exception:
            pass
        
        return False
    
    def _check_docker(self) -> bool:
        """Kiểm tra cài đặt Docker."""
        try:
            result = subprocess.run(["docker", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"Docker phiên bản {result.stdout.strip()} đã được cài đặt")
                return True
        except Exception:
            pass
        
        return False
    
    def _check_crawlee_installation(self) -> bool:
        """Kiểm tra cài đặt Crawlee."""
        if not self.nodejs_available:
            return False
        
        try:
            # Tạo thư mục tạm
            with tempfile.TemporaryDirectory() as temp_dir:
                # Tạo package.json đơn giản
                with open(os.path.join(temp_dir, "package.json"), "w") as f:
                    json.dump({
                        "name": "crawlee-check",
                        "version": "1.0.0",
                        "dependencies": {
                            "crawlee": "^3.0.0"
                        }
                    }, f)
                
                # Cài đặt Crawlee
                result = subprocess.run(
                    ["npm", "list", "crawlee"], 
                    cwd=temp_dir, 
                    capture_output=True, 
                    text=True
                )
                
                return "crawlee" in result.stdout
        except Exception as e:
            logger.error(f"Lỗi khi kiểm tra cài đặt Crawlee: {str(e)}")
            return False
    
    def _ensure_crawler_exists(self, crawler_type: str) -> bool:
        """
        Đảm bảo crawler tồn tại.
        
        Args:
            crawler_type: Loại crawler (basic, playwright, cheerio, ...)
            
        Returns:
            True nếu crawler tồn tại hoặc đã tạo thành công
        """
        crawler_dir = os.path.join(self.crawler_path, crawler_type)
        
        # Kiểm tra nếu đã tồn tại
        if os.path.exists(crawler_dir) and os.path.isdir(crawler_dir):
            return True
        
        # Tạo thư mục crawler
        os.makedirs(crawler_dir, exist_ok=True)
        
        # Tạo các file cần thiết
        self._create_crawler_files(crawler_dir, crawler_type)
        
        return os.path.exists(os.path.join(crawler_dir, "package.json"))
    
    def _create_crawler_files(self, crawler_dir: str, crawler_type: str):
        """
        Tạo các file cần thiết cho crawler.
        
        Args:
            crawler_dir: Thư mục crawler
            crawler_type: Loại crawler
        """
        # Tạo package.json
        package_json = {
            "name": f"deep-research-{crawler_type}-crawler",
            "version": "1.0.0",
            "type": "module",
            "dependencies": {
                "crawlee": "^3.0.0",
                "playwright": "^1.32.1" if crawler_type == "playwright" else None
            },
            "scripts": {
                "start": "node src/main.js"
            }
        }
        
        # Loại bỏ các giá trị None
        package_json["dependencies"] = {k: v for k, v in package_json["dependencies"].items() if v is not None}
        
        with open(os.path.join(crawler_dir, "package.json"), "w") as f:
            json.dump(package_json, f, indent=2)
        
        # Tạo thư mục src
        src_dir = os.path.join(crawler_dir, "src")
        os.makedirs(src_dir, exist_ok=True)
        
        # Tạo file main.js
        main_js_content = self._get_main_js_content(crawler_type)
        
        with open(os.path.join(src_dir, "main.js"), "w") as f:
            f.write(main_js_content)
    
    def _get_main_js_content(self, crawler_type: str) -> str:
        """
        Lấy nội dung cho file main.js dựa trên loại crawler.
        
        Args:
            crawler_type: Loại crawler
            
        Returns:
            Nội dung file main.js
        """
        if crawler_type == "basic":
            return """
import { HttpCrawler, Dataset } from 'crawlee';

// Lấy thông số từ môi trường hoặc sử dụng giá trị mặc định
const startUrls = process.env.START_URLS ? JSON.parse(process.env.START_URLS) : [];
const maxDepth = process.env.MAX_DEPTH ? parseInt(process.env.MAX_DEPTH) : 1;
const maxConcurrency = process.env.MAX_CONCURRENCY ? parseInt(process.env.MAX_CONCURRENCY) : 10;
const maxPagesPerCrawl = process.env.MAX_PAGES ? parseInt(process.env.MAX_PAGES) : 50;

// Khởi tạo crawler
const crawler = new HttpCrawler({
    maxRequestsPerCrawl: maxPagesPerCrawl,
    maxConcurrency: maxConcurrency,
    async requestHandler({ request, body, enqueueLinks, log }) {
        const title = body.match(/<title[^>]*>([^<]+)<\/title>/i)?.[1] || '';
        log.info(`Processing: ${request.url}, title: ${title}`);
        
        // Lấy tất cả văn bản từ body
        let plainText = '';
        try {
            // Loại bỏ thẻ script, style
            const bodyText = body
                .replace(/<script[^>]*>[\\s\\S]*?<\\/script>/gi, '')
                .replace(/<style[^>]*>[\\s\\S]*?<\\/style>/gi, '');
            
            // Trích xuất văn bản
            plainText = bodyText
                .replace(/<[^>]*>/g, ' ')
                .replace(/\\s+/g, ' ')
                .trim();
        } catch (e) {
            log.error(`Error extracting text from ${request.url}: ${e.message}`);
        }
        
        // Lưu trữ dữ liệu
        await Dataset.pushData({
            url: request.url,
            title,
            depth: request.userData.depth || 0,
            text: plainText.substring(0, 10000), // Giới hạn độ dài
            crawledAt: new Date().toISOString()
        });
        
        // Theo dõi liên kết nếu chưa đến độ sâu tối đa
        const currentDepth = request.userData.depth || 0;
        if (currentDepth < maxDepth) {
            await enqueueLinks({
                userData: { depth: currentDepth + 1 },
                transformRequestFunction: (req) => {
                    req.userData.depth = currentDepth + 1;
                    return req;
                },
            });
        }
    },
    failedRequestHandler({ request, log }) {
        log.error(`Request failed: ${request.url}`);
    },
});

// Khởi chạy crawler
await crawler.run(startUrls);
console.log('Crawler finished');
"""
        elif crawler_type == "playwright":
            return """
import { PlaywrightCrawler, Dataset } from 'crawlee';

// Lấy thông số từ môi trường hoặc sử dụng giá trị mặc định
const startUrls = process.env.START_URLS ? JSON.parse(process.env.START_URLS) : [];
const maxDepth = process.env.MAX_DEPTH ? parseInt(process.env.MAX_DEPTH) : 1;
const maxConcurrency = process.env.MAX_CONCURRENCY ? parseInt(process.env.MAX_CONCURRENCY) : 5;
const maxPagesPerCrawl = process.env.MAX_PAGES ? parseInt(process.env.MAX_PAGES) : 30;
const waitTime = process.env.WAIT_TIME ? parseInt(process.env.WAIT_TIME) : 2000;

// Khởi tạo crawler
const crawler = new PlaywrightCrawler({
    maxRequestsPerCrawl: maxPagesPerCrawl,
    maxConcurrency: maxConcurrency,
    launchContext: {
        launchOptions: {
            headless: true,
        },
    },
    async requestHandler({ request, page, enqueueLinks, log }) {
        log.info(`Processing: ${request.url}`);
        
        // Đợi để trang tải hoàn chỉnh
        await page.waitForLoadState('networkidle');
        await new Promise(r => setTimeout(r, waitTime));
        
        // Lấy tiêu đề
        const title = await page.title();
        
        // Lấy nội dung văn bản
        const bodyText = await page.evaluate(() => {
            // Loại bỏ các phần không mong muốn
            const elementsToRemove = document.querySelectorAll('script, style, noscript, iframe, nav, footer');
            elementsToRemove.forEach(el => el.remove());
            
            // Lấy văn bản từ phần thân trang
            return document.body.innerText;
        });
        
        // Lấy ảnh
        const images = await page.evaluate(() => {
            return Array.from(document.querySelectorAll('img'))
                .slice(0, 5) // Giới hạn 5 ảnh
                .map(img => ({
                    src: img.src,
                    alt: img.alt || ''
                }))
                .filter(img => img.src);
        });
        
        // Lấy meta tags
        const metaTags = await page.evaluate(() => {
            const meta = {};
            document.querySelectorAll('meta').forEach(m => {
                const name = m.getAttribute('name') || m.getAttribute('property');
                const content = m.getAttribute('content');
                if (name && content) meta[name] = content;
            });
            return meta;
        });
        
        // Lưu trữ dữ liệu
        await Dataset.pushData({
            url: request.url,
            title,
            depth: request.userData.depth || 0,
            text: bodyText.substring(0, 15000), // Giới hạn độ dài
            images,
            metaTags,
            crawledAt: new Date().toISOString()
        });
        
        // Theo dõi liên kết nếu chưa đến độ sâu tối đa
        const currentDepth = request.userData.depth || 0;
        if (currentDepth < maxDepth) {
            await enqueueLinks({
                userData: { depth: currentDepth + 1 },
                transformRequestFunction: (req) => {
                    req.userData.depth = currentDepth + 1;
                    return req;
                },
            });
        }
    },
    failedRequestHandler({ request, log }) {
        log.error(`Request failed: ${request.url}`);
    },
});

// Khởi chạy crawler
await crawler.run(startUrls);
console.log('Crawler finished');
"""
        else:
            # Mặc định dùng cheerio crawler
            return """
import { CheerioCrawler, Dataset } from 'crawlee';

// Lấy thông số từ môi trường hoặc sử dụng giá trị mặc định
const startUrls = process.env.START_URLS ? JSON.parse(process.env.START_URLS) : [];
const maxDepth = process.env.MAX_DEPTH ? parseInt(process.env.MAX_DEPTH) : 1;
const maxConcurrency = process.env.MAX_CONCURRENCY ? parseInt(process.env.MAX_CONCURRENCY) : 10;
const maxPagesPerCrawl = process.env.MAX_PAGES ? parseInt(process.env.MAX_PAGES) : 50;

// Khởi tạo crawler
const crawler = new CheerioCrawler({
    maxRequestsPerCrawl: maxPagesPerCrawl,
    maxConcurrency: maxConcurrency,
    async requestHandler({ request, $ /* cheerio object */, enqueueLinks, log }) {
        log.info(`Processing: ${request.url}`);
        
        // Lấy tiêu đề
        const title = $('title').text().trim();
        
        // Loại bỏ các phần không mong muốn
        $('script, style, noscript, iframe').remove();
        
        // Lấy nội dung văn bản
        const body = $('body');
        let text = body.text() || '';
        text = text.replace(/\\s+/g, ' ').trim();
        
        // Lấy meta descriptions
        const description = $('meta[name="description"]').attr('content') || '';
        
        // Lấy heading tags
        const h1 = $('h1').first().text().trim();
        
        // Lấy liên kết
        const links = [];
        $('a[href]').each((index, el) => {
            if (index < 20) { // Giới hạn 20 liên kết
                links.push({
                    href: $(el).attr('href'),
                    text: $(el).text().trim()
                });
            }
        });
        
        // Lưu trữ dữ liệu
        await Dataset.pushData({
            url: request.url,
            title,
            h1,
            description,
            depth: request.userData.depth || 0,
            text: text.substring(0, 15000), // Giới hạn độ dài
            links: links.filter(link => link.href),
            crawledAt: new Date().toISOString()
        });
        
        // Theo dõi liên kết nếu chưa đến độ sâu tối đa
        const currentDepth = request.userData.depth || 0;
        if (currentDepth < maxDepth) {
            await enqueueLinks({
                userData: { depth: currentDepth + 1 },
                transformRequestFunction: (req) => {
                    req.userData.depth = currentDepth + 1;
                    return req;
                },
            });
        }
    },
    failedRequestHandler({ request, log }) {
        log.error(`Request failed: ${request.url}`);
    },
});

// Khởi chạy crawler
await crawler.run(startUrls);
console.log('Crawler finished');
"""
    
    def run_crawler(
        self, 
        url: Union[str, List[str]], 
        crawler_type: str = "basic", 
        max_depth: int = 1,
        max_pages: int = 50,
        max_concurrency: int = 10,
        wait_time: int = 2000,
        timeout: int = 300
    ) -> Dict[str, Any]:
        """
        Chạy crawler Crawlee.
        
        Args:
            url: URL hoặc danh sách URL để crawl
            crawler_type: Loại crawler (basic, playwright, cheerio)
            max_depth: Độ sâu tối đa
            max_pages: Số trang tối đa
            max_concurrency: Số kết nối đồng thời tối đa
            wait_time: Thời gian chờ (ms) cho crawler Playwright
            timeout: Thời gian chờ tối đa (giây)
            
        Returns:
            Dict với kết quả và metadata
        """
        # Chuẩn bị URL
        urls = [url] if isinstance(url, str) else url
        
        # Kiểm tra URLs hợp lệ
        valid_urls = []
        for u in urls:
            if u.startswith(('http://', 'https://')):
                valid_urls.append(u)
            else:
                logger.warning(f"Bỏ qua URL không hợp lệ: {u}")
        
        if not valid_urls:
            return {"success": False, "error": "Không có URL hợp lệ để crawl", "results": []}
        
        # Tạo ID thực thi duy nhất
        run_id = f"{int(time.time())}_{crawler_type}"
        run_dir = os.path.join(self.results_dir, run_id)
        os.makedirs(run_dir, exist_ok=True)
        
        # Đảm bảo crawler tồn tại
        if not self._ensure_crawler_exists(crawler_type):
            return {"success": False, "error": f"Không thể tạo crawler {crawler_type}", "results": []}
        
        # Chuẩn bị biến môi trường
        env = os.environ.copy()
        env["START_URLS"] = json.dumps(valid_urls)
        env["MAX_DEPTH"] = str(max_depth)
        env["MAX_PAGES"] = str(max_pages)
        env["MAX_CONCURRENCY"] = str(max_concurrency)
        env["WAIT_TIME"] = str(wait_time)
        
        crawler_dir = os.path.join(self.crawler_path, crawler_type)
        
        try:
            if self.use_docker and self.docker_available:
                return self._run_crawler_with_docker(
                    crawler_type=crawler_type,
                    run_dir=run_dir,
                    env=env,
                    timeout=timeout
                )
            elif self.nodejs_available:
                return self._run_crawler_with_nodejs(
                    crawler_dir=crawler_dir,
                    run_dir=run_dir,
                    env=env,
                    timeout=timeout
                )
            else:
                return {
                    "success": False, 
                    "error": "Không thể chạy crawler - cần Node.js hoặc Docker",
                    "results": []
                }
        except Exception as e:
            logger.error(f"Lỗi khi chạy crawler: {str(e)}")
            return {"success": False, "error": str(e), "results": []}
    
    def _run_crawler_with_nodejs(
        self, 
        crawler_dir: str, 
        run_dir: str, 
        env: Dict[str, str],
        timeout: int
    ) -> Dict[str, Any]:
        """
        Chạy crawler với Node.js.
        
        Args:
            crawler_dir: Thư mục crawler
            run_dir: Thư mục kết quả
            env: Biến môi trường
            timeout: Thời gian chờ tối đa
            
        Returns:
            Dict với kết quả và metadata
        """
        # Cài đặt dependencies
        logger.info(f"Cài đặt dependencies trong {crawler_dir}")
        subprocess.run(["npm", "install"], cwd=crawler_dir, check=True)
        
        # Chạy crawler
        logger.info(f"Chạy crawler từ {crawler_dir}")
        process = subprocess.Popen(
            ["npm", "start"],
            cwd=crawler_dir,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        try:
            stdout, stderr = process.communicate(timeout=timeout)
            
            # Lưu logs
            with open(os.path.join(run_dir, "stdout.log"), "w") as f:
                f.write(stdout)
            
            with open(os.path.join(run_dir, "stderr.log"), "w") as f:
                f.write(stderr)
            
            # Kiểm tra kết quả
            if process.returncode != 0:
                return {
                    "success": False,
                    "error": f"Crawler trả về mã lỗi {process.returncode}",
                    "logs": stderr,
                    "results": []
                }
            
            # Tìm và đọc file dataset
            storage_dir = os.path.join(crawler_dir, "storage")
            datasets_dir = os.path.join(storage_dir, "datasets", "default")
            
            if not os.path.exists(datasets_dir):
                return {
                    "success": False,
                    "error": "Không tìm thấy thư mục dataset trong kết quả",
                    "results": []
                }
            
            # Đọc tất cả file .json trong thư mục dataset
            results = []
            for filename in os.listdir(datasets_dir):
                if filename.endswith(".json"):
                    file_path = os.path.join(datasets_dir, filename)
                    with open(file_path, "r") as f:
                        try:
                            data = json.load(f)
                            results.append(data)
                        except json.JSONDecodeError:
                            logger.error(f"Không thể phân tích file JSON: {file_path}")
            
            # Sao chép kết quả sang thư mục run
            for filename in os.listdir(datasets_dir):
                if filename.endswith(".json"):
                    src_path = os.path.join(datasets_dir, filename)
                    dst_path = os.path.join(run_dir, filename)
                    with open(src_path, "r") as src, open(dst_path, "w") as dst:
                        dst.write(src.read())
            
            return {
                "success": True,
                "run_id": os.path.basename(run_dir),
                "results_dir": run_dir,
                "result_count": len(results),
                "results": results
            }
            
        except subprocess.TimeoutExpired:
            process.kill()
            return {
                "success": False,
                "error": f"Crawler vượt quá thời gian {timeout} giây",
                "results": []
            }
    
    def _run_crawler_with_docker(
        self, 
        crawler_type: str, 
        run_dir: str, 
        env: Dict[str, str],
        timeout: int
    ) -> Dict[str, Any]:
        """
        Chạy crawler với Docker.
        
        Args:
            crawler_type: Loại crawler
            run_dir: Thư mục kết quả
            env: Biến môi trường
            timeout: Thời gian chờ tối đa
            
        Returns:
            Dict với kết quả và metadata
        """
        # Chuẩn bị Docker command
        crawler_dir = os.path.join(self.crawler_path, crawler_type)
        
        # Tạo Dockerfile
        dockerfile_path = os.path.join(crawler_dir, "Dockerfile")
        with open(dockerfile_path, "w") as f:
            f.write(f"""
FROM node:16

WORKDIR /app

COPY package.json .
RUN npm install

COPY . .

CMD ["npm", "start"]
""")
        
        # Tạo Docker image
        image_name = f"deep-research-crawlee-{crawler_type}"
        logger.info(f"Tạo Docker image {image_name}")
        
        build_process = subprocess.run(
            ["docker", "build", "-t", image_name, "."],
            cwd=crawler_dir,
            capture_output=True,
            text=True
        )
        
        if build_process.returncode != 0:
            return {
                "success": False,
                "error": f"Không thể tạo Docker image: {build_process.stderr}",
                "results": []
            }
        
        # Chuẩn bị biến môi trường cho Docker
        env_args = []
        for key, value in env.items():
            env_args.extend(["-e", f"{key}={value}"])
        
        # Chạy container
        logger.info(f"Chạy Docker container với image {image_name}")
        
        container_name = f"deep-research-crawlee-{int(time.time())}"
        
        run_command = [
            "docker", "run",
            "--name", container_name,
            "-v", f"{run_dir}:/app/results",
            *env_args,
            image_name
        ]
        
        try:
            process = subprocess.run(
                run_command,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            
            # Lưu logs
            with open(os.path.join(run_dir, "stdout.log"), "w") as f:
                f.write(process.stdout)
            
            with open(os.path.join(run_dir, "stderr.log"), "w") as f:
                f.write(process.stderr)
            
            # Sao chép kết quả từ container
            copy_command = [
                "docker", "cp",
                f"{container_name}:/app/storage/datasets/default/.",
                run_dir
            ]
            
            copy_process = subprocess.run(
                copy_command,
                capture_output=True,
                text=True
            )
            
            # Dọn dẹp container
            subprocess.run(["docker", "rm", container_name], capture_output=True)
            
            # Đọc kết quả
            results = []
            for filename in os.listdir(run_dir):
                if filename.endswith(".json") and not filename.startswith("stderr") and not filename.startswith("stdout"):
                    file_path = os.path.join(run_dir, filename)
                    with open(file_path, "r") as f:
                        try:
                            data = json.load(f)
                            results.append(data)
                        except json.JSONDecodeError:
                            logger.error(f"Không thể phân tích file JSON: {file_path}")
            
            return {
                "success": True,
                "run_id": os.path.basename(run_dir),
                "results_dir": run_dir,
                "result_count": len(results),
                "results": results
            }
            
        except subprocess.TimeoutExpired:
            # Dừng và xóa container nếu timeout
            subprocess.run(["docker", "stop", container_name], capture_output=True)
            subprocess.run(["docker", "rm", container_name], capture_output=True)
            
            return {
                "success": False,
                "error": f"Crawler vượt quá thời gian {timeout} giây",
                "results": []
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Lỗi khi chạy Docker container: {str(e)}",
                "results": []
            }
    
    def get_crawler_status(self, run_id: str) -> Dict[str, Any]:
        """
        Lấy trạng thái của crawler.
        
        Args:
            run_id: ID của lần chạy
            
        Returns:
            Dict với trạng thái và metadata
        """
        run_dir = os.path.join(self.results_dir, run_id)
        
        if not os.path.exists(run_dir):
            return {"success": False, "error": f"Không tìm thấy ID chạy: {run_id}"}
        
        # Đếm số lượng file kết quả
        result_files = [f for f in os.listdir(run_dir) if f.endswith(".json") and not f.startswith("stderr") and not f.startswith("stdout")]
        
        # Đọc logs
        stdout_path = os.path.join(run_dir, "stdout.log")
        stderr_path = os.path.join(run_dir, "stderr.log")
        
        stdout = ""
        stderr = ""
        
        if os.path.exists(stdout_path):
            with open(stdout_path, "r") as f:
                stdout = f.read()
        
        if os.path.exists(stderr_path):
            with open(stderr_path, "r") as f:
                stderr = f.read()
        
        return {
            "success": True,
            "run_id": run_id,
            "results_dir": run_dir,
            "result_count": len(result_files),
            "results_files": result_files,
            "stdout": stdout[-1000:] if len(stdout) > 1000 else stdout,  # Giới hạn độ dài
            "stderr": stderr[-1000:] if len(stderr) > 1000 else stderr   # Giới hạn độ dài
        }
    
    def get_results(self, run_id: str, max_results: int = 100) -> Dict[str, Any]:
        """
        Lấy kết quả từ crawler.
        
        Args:
            run_id: ID của lần chạy
            max_results: Số lượng kết quả tối đa
            
        Returns:
            Dict với kết quả và metadata
        """
        run_dir = os.path.join(self.results_dir, run_id)
        
        if not os.path.exists(run_dir):
            return {"success": False, "error": f"Không tìm thấy ID chạy: {run_id}"}
        
        # Lọc các file kết quả
        result_files = [
            f for f in os.listdir(run_dir) 
            if f.endswith(".json") and not f.startswith("stderr") and not f.startswith("stdout")
        ]
        
        # Đọc kết quả
        results = []
        for filename in result_files[:max_results]:
            file_path = os.path.join(run_dir, filename)
            with open(file_path, "r") as f:
                try:
                    data = json.load(f)
                    results.append(data)
                except json.JSONDecodeError:
                    logger.error(f"Không thể phân tích file JSON: {file_path}")
        
        return {
            "success": True,
            "run_id": run_id,
            "results_dir": run_dir,
            "result_count": len(results),
            "total_results": len(result_files),
            "results": results
        }

    def crawl_and_extract(
        self, 
        url: Union[str, List[str]], 
        crawler_type: str = "cheerio",
        max_depth: int = 1, 
        max_pages: int = 20
    ) -> Dict[str, Any]:
        """
        Crawl trang web và trích xuất nội dung.
        
        Args:
            url: URL hoặc danh sách URL để crawl
            crawler_type: Loại crawler (cheerio, playwright, basic)
            max_depth: Độ sâu tối đa
            max_pages: Số trang tối đa
            
        Returns:
            Dict với kết quả và metadata
        """
        # Sử dụng Playwright crawler cho cả trường hợp JavaScript
        if crawler_type not in ["playwright", "cheerio", "basic"]:
            crawler_type = "cheerio"  # Sử dụng mặc định
        
        # Chạy crawler
        crawler_result = self.run_crawler(
            url=url,
            crawler_type=crawler_type,
            max_depth=max_depth,
            max_pages=max_pages,
            max_concurrency=10 if crawler_type == "playwright" else 20,
            wait_time=3000 if crawler_type == "playwright" else 0,
            timeout=600  # 10 phút
        )
        
        if not crawler_result.get("success", False):
            return crawler_result
        
        # Trích xuất và tổng hợp thông tin
        results = crawler_result.get("results", [])
        
        if not results:
            return {
                "success": True,
                "warning": "Không có kết quả thu được",
                "run_id": crawler_result.get("run_id", ""),
                "content": [],
                "urls": []
            }
        
        # Xử lý kết quả
        processed_results = []
        crawled_urls = set()
        
        for result in results:
            url = result.get("url", "")
            crawled_urls.add(url)
            
            processed_result = {
                "url": url,
                "title": result.get("title", ""),
                "text": result.get("text", ""),
                "depth": result.get("depth", 0),
                "crawledAt": result.get("crawledAt", "")
            }
            
            # Thêm trường bổ sung nếu có
            if "metaTags" in result:
                processed_result["meta"] = result["metaTags"]
            
            if "images" in result:
                processed_result["images"] = result["images"]
            
            if "links" in result:
                processed_result["links"] = result["links"]
            
            if "h1" in result:
                processed_result["h1"] = result["h1"]
            
            # Thêm vào kết quả
            processed_results.append(processed_result)
        
        return {
            "success": True,
            "run_id": crawler_result.get("run_id", ""),
            "content": processed_results,
            "urls": list(crawled_urls)
        }

# Singleton instance
crawlee_integration = CrawleeIntegration() 