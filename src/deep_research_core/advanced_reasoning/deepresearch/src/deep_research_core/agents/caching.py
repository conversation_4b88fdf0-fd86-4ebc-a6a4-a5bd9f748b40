"""
Caching module for web search results.

This module provides caching functionality for web search agents.
"""

import os
import json
import time
import hashlib
import logging
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
import threading

# Create logger
logger = logging.getLogger(__name__)

class WebSearchCache:
    """
    Cache for web search results.
    
    Provides persistent caching for search queries and results.
    """
    
    def __init__(self, 
                 cache_dir: Optional[str] = None, 
                 max_size: int = 10000, 
                 default_ttl: int = 86400):
        """
        Initialize the WebSearchCache.
        
        Args:
            cache_dir: Directory to store cache files (default: ./.cache)
            max_size: Maximum number of items in memory cache
            default_ttl: Default time-to-live in seconds (default: 24 hours)
        """
        # Set cache directory
        self.cache_dir = cache_dir or os.path.join(os.path.dirname(__file__), "..", ".cache")
        
        # Create cache directory if it doesn't exist
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # Initialize memory cache
        self.memory_cache = {}
        self.access_times = {}
        self.max_size = max_size
        self.default_ttl = default_ttl
        
        # Cache stats
        self.hits = 0
        self.misses = 0
        self.sets = 0
        
        # Lock for thread safety
        self.lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Dict[str, Any]]:
        """
        Get a value from cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found or expired
        """
        with self.lock:
            # Try memory cache first
            if key in self.memory_cache:
                value, timestamp, ttl = self.memory_cache[key]
                
                # Check if expired
                if time.time() - timestamp > ttl:
                    # Remove expired item
                    del self.memory_cache[key]
                    if key in self.access_times:
                        del self.access_times[key]
                    self.misses += 1
                    return None
                
                # Update access time
                self.access_times[key] = time.time()
                self.hits += 1
                
                return value
            
            # Try disk cache
            cache_file = self._get_cache_file(key)
            if os.path.exists(cache_file):
                try:
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        cache_data = json.load(f)
                    
                    # Get cached value and timestamp
                    value = cache_data.get("value")
                    timestamp = cache_data.get("timestamp", 0)
                    ttl = cache_data.get("ttl", self.default_ttl)
                    
                    # Check if expired
                    if time.time() - timestamp > ttl:
                        # Remove expired item
                        os.remove(cache_file)
                        self.misses += 1
                        return None
                    
                    # Add to memory cache
                    self.memory_cache[key] = (value, timestamp, ttl)
                    self.access_times[key] = time.time()
                    self.hits += 1
                    
                    return value
                except Exception as e:
                    logger.error(f"Error reading cache file: {str(e)}")
            
            # Not found in cache
            self.misses += 1
            return None
    
    def set(self, key: str, value: Dict[str, Any], ttl: Optional[int] = None) -> None:
        """
        Set a value in cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time-to-live in seconds (default: self.default_ttl)
        """
        with self.lock:
            # Set TTL if not provided
            if ttl is None:
                ttl = self.default_ttl
            
            timestamp = time.time()
            
            # Check if memory cache is full
            if len(self.memory_cache) >= self.max_size and key not in self.memory_cache:
                # Remove least recently used item
                self._remove_lru()
            
            # Add to memory cache
            self.memory_cache[key] = (value, timestamp, ttl)
            self.access_times[key] = timestamp
            
            # Add to disk cache
            cache_file = self._get_cache_file(key)
            
            try:
                # Create cache data
                cache_data = {
                    "value": value,
                    "timestamp": timestamp,
                    "ttl": ttl,
                    "created": datetime.now().isoformat()
                }
                
                # Write to file
                with open(cache_file, 'w', encoding='utf-8') as f:
                    json.dump(cache_data, f, ensure_ascii=False, indent=2)
                
                self.sets += 1
            except Exception as e:
                logger.error(f"Error writing cache file: {str(e)}")
    
    def delete(self, key: str) -> bool:
        """
        Delete an item from cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if the item was deleted, False otherwise
        """
        with self.lock:
            deleted = False
            
            # Remove from memory cache
            if key in self.memory_cache:
                del self.memory_cache[key]
                if key in self.access_times:
                    del self.access_times[key]
                deleted = True
            
            # Remove from disk cache
            cache_file = self._get_cache_file(key)
            if os.path.exists(cache_file):
                try:
                    os.remove(cache_file)
                    deleted = True
                except Exception as e:
                    logger.error(f"Error deleting cache file: {str(e)}")
            
            return deleted
    
    def clear(self, pattern: Optional[str] = None) -> int:
        """
        Clear all cache items or items matching a pattern.
        
        Args:
            pattern: Optional pattern to match cache keys
            
        Returns:
            Number of items cleared
        """
        with self.lock:
            count = 0
            
            # Clear memory cache
            if pattern:
                # Clear items matching pattern
                keys_to_delete = [key for key in self.memory_cache.keys() if pattern in key]
                for key in keys_to_delete:
                    if self.delete(key):
                        count += 1
            else:
                # Clear all items
                count = len(self.memory_cache)
                self.memory_cache = {}
                self.access_times = {}
                
                # Clear all disk cache files
                try:
                    for filename in os.listdir(self.cache_dir):
                        if filename.endswith('.cache'):
                            os.remove(os.path.join(self.cache_dir, filename))
                except Exception as e:
                    logger.error(f"Error clearing cache directory: {str(e)}")
            
            return count
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics.
        
        Returns:
            Dictionary with cache statistics
        """
        with self.lock:
            # Count disk cache files
            disk_count = 0
            try:
                disk_count = len([f for f in os.listdir(self.cache_dir) if f.endswith('.cache')])
            except Exception:
                pass
            
            return {
                "memory_count": len(self.memory_cache),
                "disk_count": disk_count,
                "max_size": self.max_size,
                "default_ttl": self.default_ttl,
                "hits": self.hits,
                "misses": self.misses,
                "sets": self.sets,
                "hit_ratio": self.hits / max(self.hits + self.misses, 1)
            }
    
    def _remove_lru(self) -> None:
        """
        Remove the least recently used item from the memory cache.
        """
        if not self.access_times:
            return
        
        # Find least recently used key
        lru_key = min(self.access_times, key=self.access_times.get)
        
        # Remove from memory cache
        if lru_key in self.memory_cache:
            del self.memory_cache[lru_key]
        if lru_key in self.access_times:
            del self.access_times[lru_key]
    
    def _get_cache_file(self, key: str) -> str:
        """
        Get the cache file path for a key.
        
        Args:
            key: Cache key
            
        Returns:
            Path to cache file
        """
        # Create safe filename from key
        filename = hashlib.md5(key.encode('utf-8')).hexdigest() + '.cache'
        
        return os.path.join(self.cache_dir, filename)


class SmartWebSearchCache(WebSearchCache):
    """
    Smart cache for web search results.
    
    Extends WebSearchCache with semantic search capabilities.
    Can find similar queries based on vector similarity.
    """
    
    def __init__(self, 
                 cache_dir: Optional[str] = None, 
                 max_size: int = 10000, 
                 default_ttl: int = 86400,
                 similarity_threshold: float = 0.8,
                 vietnamese_support: bool = False,
                 error_adaptive: bool = True):
        """
        Initialize the SmartWebSearchCache.
        
        Args:
            cache_dir: Directory to store cache files
            max_size: Maximum number of items in memory cache
            default_ttl: Default time-to-live in seconds (default: 24 hours)
            similarity_threshold: Threshold for considering queries similar
            vietnamese_support: Enable specialized handling for Vietnamese
            error_adaptive: Automatically adjust cache behavior based on error patterns
        """
        super().__init__(cache_dir=cache_dir, max_size=max_size, default_ttl=default_ttl)
        
        self.similarity_threshold = similarity_threshold
        self.query_index = {}  # Maps query to key
        self.vietnamese_support = vietnamese_support
        self.error_adaptive = error_adaptive
        
        # Error tracking
        self.error_count = 0
        self.error_tracking = {}
        self.last_error_check = time.time()
        
        # Thống kê cắt giảm cache
        self.trim_count = 0
        self.last_trim_time = time.time()
        
        # Cấu hình GC chủ động
        self.enable_proactive_gc = True
        self.gc_threshold = 0.8  # Ngưỡng sử dụng bộ nhớ để kích hoạt GC
        self.gc_check_interval = 300  # Kiểm tra mỗi 5 phút
        self.last_gc_time = time.time()
        
        # Try to initialize embeddings
        try:
            import numpy as np
            from sentence_transformers import SentenceTransformer
            
            # Choose model based on language support
            if self.vietnamese_support:
                # Use a multilingual model with Vietnamese support
                self.model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
            else:
                # Use a faster English-focused model
                self.model = SentenceTransformer('all-MiniLM-L6-v2')
                
            self.embeddings_available = True
            logger.info("Sentence embeddings model loaded successfully")
        except ImportError:
            self.embeddings_available = False
            logger.warning("Sentence Transformers not available. Install with 'pip install sentence-transformers'")
            
            # Fallback to keyword-based similarity
            try:
                import nltk
                from nltk.tokenize import word_tokenize
                from nltk.corpus import stopwords
                
                # Download necessary NLTK data
                nltk.download('punkt', quiet=True)
                nltk.download('stopwords', quiet=True)
                
                self.nltk_available = True
            except ImportError:
                self.nltk_available = False
                logger.warning("NLTK not available for fallback similarity. Install with 'pip install nltk'")
        
        # Check for Vietnamese NLP support if needed
        if self.vietnamese_support:
            try:
                from underthesea import word_tokenize as vi_tokenize
                self.underthesea_available = True
            except ImportError:
                self.underthesea_available = False
                logger.warning("Underthesea not available for Vietnamese. Install with 'pip install underthesea'")

    def get(self, key: str, allow_similar: bool = True, context: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """
        Get a value from cache, optionally looking for similar queries.
        
        Args:
            key: Cache key
            allow_similar: Whether to allow similar queries
            context: Additional context for the query
            
        Returns:
            Cached value or None if not found or expired
        """
        # Kiểm tra xem có cần thực hiện GC chủ động không
        self._check_proactive_gc()
        
        # Extract query from key if it's a query hash
        query = self._extract_query_from_key(key)
        
        # First try exact match
        result = super().get(key)
        
        # Check for similar queries if not found and allowed
        if result is None and allow_similar and query:
            similar_key = self._find_similar_query(query)
            
            if similar_key:
                similar_result = super().get(similar_key)
                
                if similar_result:
                    # If found a similar result, add a flag to indicate it's from a similar query
                    similar_result["from_similar_query"] = True
                    similar_result["original_query"] = query
                    similar_result["matched_query"] = self._extract_query_from_key(similar_key)
                    
                    if context and self.error_adaptive:
                        # Check if this query was previously associated with errors
                        if key in self.error_tracking:
                            # Reduce similarity threshold for queries with errors
                            self._adjust_ttl_for_error_prone_query(similar_key)
                    
                    return similar_result
        
        # Update error tracking if needed
        if context and self.error_adaptive and result is not None:
            self._track_result_status(key, result, context)
        
        return result

    def set(self, key: str, value: Dict[str, Any], ttl: Optional[int] = None) -> None:
        """
        Set a value in cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time-to-live in seconds
        """
        super().set(key, value, ttl)
        
        # Extract and index query
        query = self._extract_query_from_key(key)
        if query:
            self._index_query(query, key)
        
        # Check result status for error tracking
        if self.error_adaptive:
            # If the result has an error, track it and reduce TTL
            if not value.get("success", True) or "error" in value:
                self._track_error(key, value.get("error", "unknown_error"))
                
                # Reduce TTL for error results
                self._adjust_ttl_for_error_prone_query(key)

    def _track_error(self, key: str, error: str) -> None:
        """
        Track errors for adaptive caching.
        
        Args:
            key: Cache key
            error: Error message
        """
        self.error_count += 1
        
        if key not in self.error_tracking:
            self.error_tracking[key] = {"count": 0, "errors": {}}
        
        self.error_tracking[key]["count"] += 1
        
        if error not in self.error_tracking[key]["errors"]:
            self.error_tracking[key]["errors"][error] = 0
        
        self.error_tracking[key]["errors"][error] += 1
        
        # Periodically clean up error tracking
        if time.time() - self.last_error_check > 3600:  # 1 hour
            self._cleanup_error_tracking()
            self.last_error_check = time.time()

    def _track_result_status(self, key: str, result: Dict[str, Any], context: Dict[str, Any]) -> None:
        """
        Track result status for adaptive caching.
        
        Args:
            key: Cache key
            result: Result from cache
            context: Context information
        """
        # If the context contains error recovery information
        if "error_recovery" in context and context["error_recovery"]:
            # This query has been through error recovery
            self._track_error(key, context.get("error_type", "recovered_error"))
            
            # Adjust TTL based on error recovery
            if "recovery_strategy" in context:
                # Different strategies might warrant different TTL adjustments
                if context["recovery_strategy"] in ["FALLBACK_SOURCE", "CHANGE_METHOD"]:
                    # Results from fallback sources might need to be re-checked sooner
                    self._adjust_ttl_for_error_prone_query(key, factor=0.5)

    def _adjust_ttl_for_error_prone_query(self, key: str, factor: float = 0.25) -> None:
        """
        Adjust TTL for error-prone queries.
        
        Args:
            key: Cache key
            factor: Factor to multiply TTL by
        """
        cache_file = self._get_cache_file(key)
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                
                # Reduce TTL
                current_ttl = cache_data.get("ttl", self.default_ttl)
                new_ttl = max(60, int(current_ttl * factor))  # Minimum 60 seconds
                
                cache_data["ttl"] = new_ttl
                
                # Write back
                with open(cache_file, 'w', encoding='utf-8') as f:
                    json.dump(cache_data, f, ensure_ascii=False, indent=2)
                
                # Update memory cache
                if key in self.memory_cache:
                    value, timestamp, _ = self.memory_cache[key]
                    self.memory_cache[key] = (value, timestamp, new_ttl)
                
                logger.debug(f"Adjusted TTL for error-prone query: {key}, new TTL: {new_ttl}s")
            except Exception as e:
                logger.error(f"Error adjusting TTL: {str(e)}")

    def _cleanup_error_tracking(self) -> None:
        """Clean up old error tracking data."""
        # Remove tracking data for keys not in cache
        keys_to_remove = []
        
        for key in self.error_tracking:
            if not self._key_exists(key):
                keys_to_remove.append(key)
        
        for key in keys_to_remove:
            del self.error_tracking[key]
        
        # Reset error count
        self.error_count = 0

    def _key_exists(self, key: str) -> bool:
        """
        Check if a key exists in cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if the key exists in cache
        """
        # Check memory cache
        if key in self.memory_cache:
            return True
        
        # Check disk cache
        cache_file = self._get_cache_file(key)
        return os.path.exists(cache_file)

    def _extract_query_from_key(self, key: str) -> Optional[str]:
        """
        Attempt to extract original query from a key.
        
        Args:
            key: Cache key
            
        Returns:
            Original query or None if not possible
        """
        # If the key is already in our index, return it
        if key in self.query_index:
            return key
        
        # Try to parse from a standard format: "q=query|..."
        try:
            if '|' in key:
                parts = key.split('|')
                for part in parts:
                    if part.startswith('q='):
                        return part[2:]
        except:
            pass
        
        return None

    def _index_query(self, query: str, key: str) -> None:
        """
        Index a query for later similarity search.
        
        Args:
            query: Search query
            key: Cache key
        """
        self.query_index[query] = key
        
        # Clean up index if it's getting too large
        if len(self.query_index) > self.max_size * 2:
            # Keep only queries that are still in cache
            valid_queries = {}
            for q, k in self.query_index.items():
                if self._key_exists(k):
                    valid_queries[q] = k
            
            self.query_index = valid_queries

    def _find_similar_query(self, query: str) -> Optional[str]:
        """
        Find a similar query in the cache.
        
        Args:
            query: Query to find similar matches for
            
        Returns:
            Cache key of similar query or None
        """
        if not self.query_index:
            return None
        
        # Skip if query is too short
        if len(query.strip()) < 3:
            return None
        
        # Detect language to optimize similarity calculation
        lang = self._detect_language(query)
        is_vietnamese = lang == 'vi'
        
        # Use specialized method for Vietnamese if needed
        if is_vietnamese and self.vietnamese_support:
            return self._find_similar_vietnamese_query(query)
        
        # Use embeddings if available
        if self.embeddings_available:
            try:
                # Calculate query embedding
                query_embedding = self.model.encode(query, convert_to_tensor=True)
                
                best_similarity = 0.0
                best_match = None
                
                # Compare with cached queries
                for cached_query, cached_key in self.query_index.items():
                    # Skip if the cached query is not in memory or disk cache
                    if not self._key_exists(cached_key):
                        continue
                    
                    # Calculate similarity
                    similarity = self._calculate_embedding_similarity(query, cached_query, query_embedding)
                    
                    if similarity > self.similarity_threshold and similarity > best_similarity:
                        best_similarity = similarity
                        best_match = cached_key
                
                return best_match
            except Exception as e:
                logger.warning(f"Error calculating embedding similarity: {str(e)}")
                # Fall back to keyword similarity
                pass
        
        # Fall back to keyword-based similarity
        return self._find_similar_by_keywords(query)

    def _find_similar_vietnamese_query(self, query: str) -> Optional[str]:
        """
        Find a similar Vietnamese query using specialized methods.
        
        Args:
            query: Vietnamese query
            
        Returns:
            Cache key of similar query or None
        """
        if not self.underthesea_available:
            # Fall back to normal similarity
            return self._find_similar_by_keywords(query)
        
        try:
            from underthesea import word_tokenize as vi_tokenize
            
            # Tokenize Vietnamese query
            query_tokens = set(vi_tokenize(query.lower()))
            
            best_similarity = 0.0
            best_match = None
            
            # Compare with cached queries
            for cached_query, cached_key in self.query_index.items():
                # Skip if the cached query is not in memory or disk cache
                if not self._key_exists(cached_key):
                    continue
                
                # Skip if language is not Vietnamese
                if self._detect_language(cached_query) != 'vi':
                    continue
                
                # Tokenize cached query
                cached_tokens = set(vi_tokenize(cached_query.lower()))
                
                # Calculate Jaccard similarity
                if not query_tokens or not cached_tokens:
                    continue
                    
                intersection = len(query_tokens.intersection(cached_tokens))
                union = len(query_tokens.union(cached_tokens))
                
                similarity = intersection / union if union > 0 else 0
                
                if similarity > self.similarity_threshold and similarity > best_similarity:
                    best_similarity = similarity
                    best_match = cached_key
            
            return best_match
        except Exception as e:
            logger.warning(f"Error with Vietnamese similarity: {str(e)}")
            return self._find_similar_by_keywords(query)

    def _find_similar_by_keywords(self, query: str) -> Optional[str]:
        """
        Find a similar query using keyword-based methods.
        
        Args:
            query: Query to find similar matches for
            
        Returns:
            Cache key of similar query or None
        """
        # Simple keyword-based similarity
        query_words = set(self._tokenize(query.lower()))
        
        if not query_words:
            return None
        
        best_similarity = 0.0
        best_match = None
        
        for cached_query, cached_key in self.query_index.items():
            # Skip if the cached query is not in memory or disk cache
            if not self._key_exists(cached_key):
                continue
                
            cached_words = set(self._tokenize(cached_query.lower()))
            
            if not cached_words:
                continue
                
            # Calculate Jaccard similarity
            intersection = len(query_words.intersection(cached_words))
            union = len(query_words.union(cached_words))
            
            similarity = intersection / union if union > 0 else 0
            
            if similarity > self.similarity_threshold and similarity > best_similarity:
                best_similarity = similarity
                best_match = cached_key
        
        return best_match

    def _tokenize(self, text: str) -> List[str]:
        """
        Tokenize text for similarity comparison.
        
        Args:
            text: Text to tokenize
            
        Returns:
            List of tokens
        """
        if self.nltk_available:
            try:
                from nltk.tokenize import word_tokenize
                from nltk.corpus import stopwords
                
                # Get stopwords
                try:
                    stop_words = set(stopwords.words('english'))
                except:
                    stop_words = set()
                
                # Tokenize and remove stopwords
                tokens = word_tokenize(text)
                return [w for w in tokens if w.isalnum() and w not in stop_words]
            except Exception:
                pass
        
        # Simple fallback tokenization
        return [w for w in text.split() if w.strip()]

    def _calculate_embedding_similarity(self, query1: str, query2: str, query1_embedding=None) -> float:
        """
        Calculate similarity between two queries using embeddings.
        
        Args:
            query1: First query
            query2: Second query
            query1_embedding: Pre-calculated embedding for query1
            
        Returns:
            Similarity score (0-1)
        """
        if not self.embeddings_available:
            return 0.0
        
        try:
            # Use pre-calculated embedding if provided
            if query1_embedding is None:
                query1_embedding = self.model.encode(query1, convert_to_tensor=True)
            
            query2_embedding = self.model.encode(query2, convert_to_tensor=True)
            
            # Calculate cosine similarity
            from torch import nn
            cosine_similarity = nn.CosineSimilarity(dim=0)
            similarity = cosine_similarity(query1_embedding, query2_embedding).item()
            
            return max(0.0, min(1.0, similarity))
        except Exception as e:
            logger.warning(f"Error calculating embedding similarity: {str(e)}")
            return 0.0

    def _detect_language(self, text: str) -> str:
        """
        Detect language of text.
        
        Args:
            text: Text to detect language
            
        Returns:
            Language code
        """
        # Simple detection for Vietnamese characters
        vietnamese_chars = set("àáảãạăắằẳẵặâấầẩẫậèéẻẽẹêếềểễệìíỉĩịòóỏõọôốồổỗộơớờởỡợùúủũụưứừửữựỳýỷỹỵđ")
        if any(c in vietnamese_chars for c in text.lower()):
            return 'vi'
        
        # Try using langdetect
        try:
            from langdetect import detect
            return detect(text)
        except:
            # Default to English
            return 'en'

    def get_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics.
        
        Returns:
            Dictionary with cache statistics
        """
        stats = super().get_stats()
        
        # Add smart cache specific stats
        stats.update({
            "indexed_queries": len(self.query_index),
            "embeddings_available": self.embeddings_available,
            "vietnamese_support": self.vietnamese_support,
            "error_count": self.error_count,
            "error_tracked_keys": len(self.error_tracking)
        })
        
        return stats

    def get_size(self) -> int:
        """
        Trả về kích thước hiện tại của cache.
        
        Returns:
            Số lượng mục trong cache
        """
        return len(self.memory_cache)
    
    def trim(self, keep_ratio: float = 0.75) -> Dict[str, Any]:
        """
        Cắt giảm kích thước cache để bảo tồn bộ nhớ.
        
        Args:
            keep_ratio: Tỷ lệ mục cần giữ lại (0.0-1.0)
            
        Returns:
            Thông tin về quá trình cắt giảm
        """
        if keep_ratio <= 0 or keep_ratio >= 1:
            keep_ratio = 0.75  # Giá trị mặc định nếu không hợp lệ
            
        cache_size = len(self.memory_cache)
        
        # Nếu cache quá nhỏ, không cần cắt giảm
        if cache_size < 10:
            return {"trimmed": False, "reason": "Cache too small", "size": cache_size}
            
        # Số lượng mục cần giữ lại
        keep_count = max(1, int(cache_size * keep_ratio))
        
        # Sắp xếp cache theo thời gian truy cập
        sorted_items = sorted(
            [(k, v, self.access_times.get(k, 0)) for k, v in self.memory_cache.items()],
            key=lambda x: x[2],
            reverse=True  # Ưu tiên giữ lại các mục truy cập gần đây
        )
        
        # Chỉ giữ lại keep_count mục đầu tiên
        keep_items = sorted_items[:keep_count]
        
        # Tạo cache mới
        new_cache = {}
        for key, value, _ in keep_items:
            new_cache[key] = value
            
        # Lưu lại các chỉ số trước khi cắt giảm
        old_size = len(self.memory_cache)
        
        # Cập nhật cache
        self.memory_cache = new_cache
        
        # Ghi lại thông tin
        self.last_trim_time = time.time()
        self.trim_count += 1
        
        return {
            "trimmed": True,
            "old_size": old_size,
            "new_size": len(self.memory_cache),
            "removed": old_size - len(self.memory_cache),
            "keep_ratio": keep_ratio,
            "trim_count": self.trim_count
        }

    def _check_proactive_gc(self) -> None:
        """Kiểm tra và thực hiện Garbage Collection chủ động nếu cần."""
        if not self.enable_proactive_gc:
            return
            
        current_time = time.time()
        
        # Kiểm tra theo chu kỳ
        if current_time - self.last_gc_time < self.gc_check_interval:
            return
            
        try:
            # Kiểm tra mức sử dụng bộ nhớ
            memory_usage = self._get_memory_usage()
            
            # Nếu vượt ngưỡng, thực hiện GC
            if memory_usage > self.gc_threshold:
                self._perform_garbage_collection()
                
            self.last_gc_time = current_time
        except Exception as e:
            logger.error(f"Lỗi khi kiểm tra GC: {str(e)}")
    
    def _perform_garbage_collection(self) -> Dict[str, Any]:
        """
        Thực hiện dọn dẹp bộ nhớ chủ động.
        
        Returns:
            Thông tin về quá trình dọn dẹp
        """
        start_time = time.time()
        
        try:
            # 1. Cắt giảm bộ nhớ cache theo thời gian truy cập
            trim_result = self.trim(0.7)  # Giữ lại 70% mục truy cập gần đây nhất
            
            # 2. Dọn dẹp error tracking
            old_error_count = len(self.error_tracking)
            self._cleanup_error_tracking()
            new_error_count = len(self.error_tracking)
            
            # 3. Giải phóng các đối tượng không sử dụng
            import gc
            collected = gc.collect()
            
            # 4. Dọn dẹp query_index
            old_index_size = len(self.query_index)
            valid_queries = {}
            for q, k in self.query_index.items():
                if self._key_exists(k):
                    valid_queries[q] = k
            self.query_index = valid_queries
            
            return {
                "success": True,
                "elapsed_time": time.time() - start_time,
                "trim_result": trim_result,
                "error_tracking_cleaned": old_error_count - new_error_count,
                "gc_objects_collected": collected,
                "query_index_cleaned": old_index_size - len(self.query_index)
            }
        except Exception as e:
            logger.error(f"Lỗi khi thực hiện GC: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "elapsed_time": time.time() - start_time
            }
    
    def _get_memory_usage(self) -> float:
        """
        Lấy mức sử dụng bộ nhớ hiện tại.
        
        Returns:
            Phần trăm bộ nhớ đang sử dụng
        """
        try:
            import psutil
            process = psutil.Process()
            return process.memory_percent()
        except ImportError:
            # Nếu không có psutil, dùng kích thước cache làm thước đo
            return len(self.memory_cache) / self.max_size
        except Exception as e:
            logger.error(f"Lỗi khi lấy thông tin bộ nhớ: {str(e)}")
            return 0.0


class RedisWebSearchCache(SmartWebSearchCache):
    """
    Redis-based cache for web search results.
    
    Extends SmartWebSearchCache with Redis backend for distributed caching.
    """
    
    def __init__(
        self,
        redis_url: str = "redis://localhost:6379/0",
        prefix: str = "websearch:",
        max_size: int = 10000,
        default_ttl: int = 86400,
        similarity_threshold: float = 0.8,
        vietnamese_support: bool = False,
        error_adaptive: bool = True
    ):
        """
        Khởi tạo cache Redis cho tìm kiếm web.
        
        Args:
            redis_url: URL kết nối Redis
            prefix: Tiền tố khóa Redis
            max_size: Kích thước tối đa của cache bộ nhớ
            default_ttl: Thời gian sống mặc định (giây)
            similarity_threshold: Ngưỡng tương đồng truy vấn
            vietnamese_support: Hỗ trợ tiếng Việt
            error_adaptive: Tự động điều chỉnh theo lỗi
        """
        # Khởi tạo lớp cha nhưng bỏ qua cache_dir
        super().__init__(
            cache_dir=None,
            max_size=max_size,
            default_ttl=default_ttl,
            similarity_threshold=similarity_threshold,
            vietnamese_support=vietnamese_support,
            error_adaptive=error_adaptive
        )
        
        self.redis_url = redis_url
        self.prefix = prefix
        self.redis = None
        
        # Thử kết nối Redis
        self._connect_redis()
        
        # Thống kê Redis
        self.redis_hits = 0
        self.redis_misses = 0
        self.redis_errors = 0
    
    def _connect_redis(self) -> bool:
        """
        Kết nối đến Redis server.
        
        Returns:
            True nếu kết nối thành công
        """
        try:
            import redis
            self.redis = redis.from_url(self.redis_url)
            # Kiểm tra kết nối
            self.redis.ping()
            logger.info(f"Kết nối Redis thành công: {self.redis_url}")
            return True
        except ImportError:
            logger.error("Không thể import module redis. Cài đặt với: pip install redis")
            self.redis = None
            return False
        except Exception as e:
            logger.error(f"Lỗi kết nối Redis: {str(e)}")
            self.redis = None
            return False
    
    def get(self, key: str, allow_similar: bool = True, context: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """
        Lấy giá trị từ cache, với hỗ trợ tìm kiếm truy vấn tương tự.
        
        Args:
            key: Khóa cache
            allow_similar: Cho phép truy vấn tương tự
            context: Ngữ cảnh bổ sung
            
        Returns:
            Giá trị cache hoặc None nếu không tìm thấy
        """
        # Kiểm tra cache bộ nhớ trước
        result = super().get(key, allow_similar=False, context=context)
        if result:
            return result
        
        # Nếu không tìm thấy trong cache bộ nhớ, kiểm tra Redis
        if not self.redis:
            if not self._connect_redis():
                return None
        
        try:
            redis_key = f"{self.prefix}{key}"
            data = self.redis.get(redis_key)
            
            if data:
                # Giải nén dữ liệu JSON
                import json
                cache_data = json.loads(data)
                
                # Kiểm tra hết hạn
                timestamp = cache_data.get("timestamp", 0)
                ttl = cache_data.get("ttl", self.default_ttl)
                
                if time.time() - timestamp > ttl:
                    # Xóa giá trị hết hạn
                    self.redis.delete(redis_key)
                    self.redis_misses += 1
                    
                    # Tiếp tục với tìm kiếm tương tự nếu được yêu cầu
                    if allow_similar:
                        return self._find_similar_from_redis(key, context)
                    
                    return None
                
                # Lấy giá trị
                value = cache_data.get("value")
                
                # Thêm vào cache bộ nhớ
                with self.lock:
                    self.memory_cache[key] = (value, timestamp, ttl)
                    self.access_times[key] = time.time()
                
                self.redis_hits += 1
                self.hits += 1
                
                return value
            else:
                self.redis_misses += 1
                
                # Tìm kiếm truy vấn tương tự nếu được yêu cầu
                if allow_similar:
                    return self._find_similar_from_redis(key, context)
                
                return None
                
        except Exception as e:
            logger.error(f"Lỗi khi lấy từ Redis: {str(e)}")
            self.redis_errors += 1
            
            # Tìm kiếm truy vấn tương tự từ cache bộ nhớ nếu Redis thất bại
            if allow_similar:
                query = self._extract_query_from_key(key)
                if query:
                    similar_key = self._find_similar_query(query)
                    if similar_key:
                        return super().get(similar_key)
            
            return None
    
    def set(self, key: str, value: Dict[str, Any], ttl: Optional[int] = None) -> None:
        """
        Đặt giá trị vào cache.
        
        Args:
            key: Khóa cache
            value: Giá trị cần lưu
            ttl: Thời gian sống (giây)
        """
        # Lưu vào cache bộ nhớ
        super().set(key, value, ttl)
        
        # Lưu vào Redis
        if not self.redis:
            if not self._connect_redis():
                return
        
        try:
            # Thiết lập TTL nếu không cung cấp
            if ttl is None:
                ttl = self.default_ttl
            
            timestamp = time.time()
            redis_key = f"{self.prefix}{key}"
            
            # Tạo dữ liệu cache
            cache_data = {
                "value": value,
                "timestamp": timestamp,
                "ttl": ttl,
                "created": timestamp
            }
            
            # Chuyển thành chuỗi JSON
            import json
            data = json.dumps(cache_data)
            
            # Lưu vào Redis với TTL
            self.redis.setex(redis_key, ttl, data)
            
            # Lưu truy vấn vào index nếu có
            query = self._extract_query_from_key(key)
            if query:
                # Lưu ánh xạ truy vấn trong Redis
                query_index_key = f"{self.prefix}query_index:{query}"
                self.redis.set(query_index_key, key)
                # Thiết lập thời gian hết hạn cho index
                self.redis.expire(query_index_key, ttl)
                
        except Exception as e:
            logger.error(f"Lỗi khi lưu vào Redis: {str(e)}")
            self.redis_errors += 1
    
    def delete(self, key: str) -> bool:
        """
        Xóa một mục từ cache.
        
        Args:
            key: Khóa cache
            
        Returns:
            True nếu xóa thành công
        """
        # Xóa từ cache bộ nhớ
        deleted = super().delete(key)
        
        # Xóa từ Redis
        if not self.redis:
            if not self._connect_redis():
                return deleted
        
        try:
            redis_key = f"{self.prefix}{key}"
            self.redis.delete(redis_key)
            
            # Tìm và xóa khỏi query index
            query = self._extract_query_from_key(key)
            if query:
                query_index_key = f"{self.prefix}query_index:{query}"
                self.redis.delete(query_index_key)
            
            return True
        except Exception as e:
            logger.error(f"Lỗi khi xóa từ Redis: {str(e)}")
            self.redis_errors += 1
            return deleted
    
    def clear(self, pattern: Optional[str] = None) -> int:
        """
        Xóa tất cả các mục cache hoặc các mục khớp với mẫu.
        
        Args:
            pattern: Mẫu tùy chọn để khớp với khóa cache
            
        Returns:
            Số lượng mục đã xóa
        """
        # Xóa từ cache bộ nhớ
        count = super().clear(pattern)
        
        # Xóa từ Redis
        if not self.redis:
            if not self._connect_redis():
                return count
        
        try:
            if pattern:
                # Xóa các khóa khớp với mẫu
                redis_pattern = f"{self.prefix}*{pattern}*"
                keys = self.redis.keys(redis_pattern)
                if keys:
                    self.redis.delete(*keys)
                    count += len(keys)
            else:
                # Xóa tất cả các khóa với tiền tố
                redis_keys = self.redis.keys(f"{self.prefix}*")
                if redis_keys:
                    self.redis.delete(*redis_keys)
                    count += len(redis_keys)
            
            return count
        except Exception as e:
            logger.error(f"Lỗi khi xóa từ Redis: {str(e)}")
            self.redis_errors += 1
            return count
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Lấy thống kê cache.
        
        Returns:
            Dictionary chứa thống kê cache
        """
        # Lấy thống kê từ lớp cha
        stats = super().get_stats()
        
        # Thêm thống kê Redis
        redis_stats = {
            "redis_connected": self.redis is not None,
            "redis_url": self.redis_url,
            "redis_hits": self.redis_hits,
            "redis_misses": self.redis_misses,
            "redis_errors": self.redis_errors
        }
        
        # Thêm thống kê từ Redis server nếu có thể
        if self.redis:
            try:
                info = self.redis.info()
                redis_stats.update({
                    "redis_used_memory": info.get("used_memory_human", "N/A"),
                    "redis_keys": info.get("db0", {}).get("keys", 0),
                    "redis_uptime": info.get("uptime_in_seconds", 0)
                })
                
                # Đếm số khóa trong cache Redis
                prefix_keys = len(self.redis.keys(f"{self.prefix}*"))
                redis_stats["redis_cache_keys"] = prefix_keys
            except Exception as e:
                logger.error(f"Lỗi khi lấy thông tin Redis: {str(e)}")
        
        stats.update(redis_stats)
        return stats
    
    def _find_similar_from_redis(self, key: str, context: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """
        Tìm truy vấn tương tự từ Redis.
        
        Args:
            key: Khóa cache
            context: Ngữ cảnh bổ sung
            
        Returns:
            Giá trị cache từ truy vấn tương tự hoặc None
        """
        if not self.redis:
            return None
            
        query = self._extract_query_from_key(key)
        if not query:
            return None
            
        try:
            # Lấy tất cả các truy vấn từ Redis
            query_keys = self.redis.keys(f"{self.prefix}query_index:*")
            queries = [k.decode('utf-8').split(':', 2)[-1] for k in query_keys]
            
            # Tìm truy vấn tương tự
            best_match = None
            best_similarity = 0.0
            
            for q in queries:
                if q == query:
                    continue
                    
                # Tính độ tương tự
                similarity = self._simple_similarity(query, q)
                
                if similarity > self.similarity_threshold and similarity > best_similarity:
                    best_similarity = similarity
                    best_match = q
            
            if best_match:
                # Lấy khóa cho truy vấn tương tự
                query_index_key = f"{self.prefix}query_index:{best_match}"
                similar_key = self.redis.get(query_index_key)
                
                if similar_key:
                    similar_key = similar_key.decode('utf-8')
                    
                    # Lấy kết quả
                    redis_key = f"{self.prefix}{similar_key}"
                    data = self.redis.get(redis_key)
                    
                    if data:
                        import json
                        cache_data = json.loads(data)
                        
                        # Kiểm tra hết hạn
                        timestamp = cache_data.get("timestamp", 0)
                        ttl = cache_data.get("ttl", self.default_ttl)
                        
                        if time.time() - timestamp > ttl:
                            return None
                            
                        value = cache_data.get("value")
                        if value:
                            # Đánh dấu là từ truy vấn tương tự
                            value["from_similar_query"] = True
                            value["original_query"] = query
                            value["matched_query"] = best_match
                            return value
            
            return None
        except Exception as e:
            logger.error(f"Lỗi khi tìm truy vấn tương tự từ Redis: {str(e)}")
            return None
    
    def _simple_similarity(self, text1: str, text2: str) -> float:
        """
        Tính độ tương đồng dựa trên phương pháp đơn giản giữa hai chuỗi văn bản.
        
        Args:
            text1: Văn bản thứ nhất
            text2: Văn bản thứ hai
            
        Returns:
            Điểm tương đồng từ 0.0 đến 1.0
        """
        # Tách từ
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        # Kiểm tra tập hợp trống
        if not words1 or not words2:
            return 0.0
        
        # Tính toán tương đồng Jaccard
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0 