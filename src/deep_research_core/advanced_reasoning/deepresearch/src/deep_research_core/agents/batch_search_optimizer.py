"""
Module tối ưu hóa tìm kiếm hàng loạt.

Module này cung cấp các công cụ để tối ưu hóa hiệu suất 
khi thực hiện nhiều truy vấn tìm kiếm.
"""

import time
import threading
import queue
import logging
import gc
from typing import Dict, Any, List, Optional, Callable, Tuple, Set, Union
import concurrent.futures
from dataclasses import dataclass, field
from weakref import WeakValueDictionary

from deep_research_core.agents.web_search_agent import WebSearchAgent

# Thiết lập logging
logger = logging.getLogger(__name__)

@dataclass
class SearchTask:
    """Đại diện cho một tác vụ tìm kiếm."""
    
    query: str
    engine: Optional[str] = None
    num_results: int = 10
    language: str = "auto"
    params: Dict[str, Any] = field(default_factory=dict)
    priority: int = 0
    id: Optional[str] = None
    timestamp: float = field(default_factory=time.time)
    
    def to_dict(self) -> Dict[str, Any]:
        """Chuyển đổi tác vụ thành dictionary."""
        return {
            "id": self.id,
            "query": self.query,
            "engine": self.engine,
            "num_results": self.num_results,
            "language": self.language,
            "params": self.params,
            "priority": self.priority,
            "timestamp": self.timestamp
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SearchTask':
        """Tạo tác vụ từ dictionary."""
        return cls(
            id=data.get("id"),
            query=data.get("query", ""),
            engine=data.get("engine"),
            num_results=data.get("num_results", 10),
            language=data.get("language", "auto"),
            params=data.get("params", {}),
            priority=data.get("priority", 0),
            timestamp=data.get("timestamp", time.time())
        )

@dataclass
class SearchResult:
    """Đại diện cho kết quả một tác vụ tìm kiếm."""
    
    task_id: str
    success: bool
    data: Dict[str, Any]
    error: Optional[str] = None
    timestamp: float = field(default_factory=time.time)
    
    def to_dict(self) -> Dict[str, Any]:
        """Chuyển đổi kết quả thành dictionary."""
        result = {
            "task_id": self.task_id,
            "success": self.success,
            "data": self.data,
            "timestamp": self.timestamp
        }
        if self.error:
            result["error"] = self.error
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SearchResult':
        """Tạo kết quả từ dictionary."""
        return cls(
            task_id=data.get("task_id", ""),
            success=data.get("success", False),
            data=data.get("data", {}),
            error=data.get("error"),
            timestamp=data.get("timestamp", time.time())
        )

class BatchSearchOptimizer:
    """
    Tối ưu hóa tìm kiếm hàng loạt.
    
    Lớp này quản lý việc thực hiện nhiều truy vấn tìm kiếm một cách hiệu quả,
    bao gồm gom nhóm truy vấn, điều chỉnh tỷ lệ và thực hiện song song.
    """
    
    def __init__(
        self, 
        search_agent: WebSearchAgent,
        num_workers: int = 3,
        max_queue_size: int = 100,
        throttle_delay: float = 0.5,
        engine_rotation: bool = True,
        config: Optional[Dict[str, Any]] = None
    ):
        """
        Khởi tạo trình tối ưu hóa tìm kiếm hàng loạt.
        
        Args:
            search_agent: Đối tượng WebSearchAgent
            num_workers: Số lượng worker song song
            max_queue_size: Kích thước tối đa của hàng đợi
            throttle_delay: Thời gian trễ giữa các truy vấn
            engine_rotation: Xoay vòng công cụ tìm kiếm
            config: Cấu hình bổ sung
        """
        self.search_agent = search_agent
        self.num_workers = num_workers
        self.max_queue_size = max_queue_size
        self.throttle_delay = throttle_delay
        self.engine_rotation = engine_rotation
        self.config = config or {}
        
        # Đánh giá hiệu suất
        self.task_queue = queue.PriorityQueue(maxsize=max_queue_size)
        self.results = {}
        self.running = False
        self.workers = []
        self.executor = None
        
        # Xoay vòng công cụ
        self.search_engines = self.config.get("search_engines", [
            "google", "bing", "duckduckgo", "searx", "qwant"
        ])
        self.engine_index = 0
        
        # Phân tích truy vấn tương tự
        self.similar_query_threshold = self.config.get("similar_query_threshold", 0.85)
        self.completed_tasks = set()
        
        # Sử dụng WeakValueDictionary để lưu trữ lịch sử tác vụ, giúp tự động xóa khi không cần thiết nữa
        self._task_history_dict = WeakValueDictionary()
        self.task_history = []
        
        # Cấu hình quản lý bộ nhớ
        self.max_history_size = self.config.get("max_history_size", 1000)
        self.memory_cleanup_threshold = self.config.get("memory_cleanup_threshold", 0.85)  # Ngưỡng sử dụng bộ nhớ để kích hoạt dọn dẹp
        self.last_cleanup_time = time.time()
        self.cleanup_interval = self.config.get("cleanup_interval", 300)  # 5 phút
        
        logger.info(f"BatchSearchOptimizer khởi tạo với {num_workers} worker và {max_queue_size} kích thước hàng đợi")
    
    def add_task(self, task: Union[SearchTask, Dict[str, Any]]) -> str:
        """
        Thêm một tác vụ tìm kiếm vào hàng đợi.
        
        Args:
            task: Tác vụ tìm kiếm hoặc dictionary mô tả tác vụ
            
        Returns:
            ID của tác vụ
        """
        if isinstance(task, dict):
            task = SearchTask.from_dict(task)
        
        # Tạo ID nếu chưa có
        if not task.id:
            task.id = f"task_{int(time.time())}_{len(self.task_history)}"
        
        # Kiểm tra truy vấn tương tự trong lịch sử
        similar_task_id = self._find_similar_task(task.query)
        if similar_task_id and similar_task_id in self.results:
            logger.info(f"Truy vấn tương tự đã tìm thấy: {task.query} ~ {similar_task_id}")
            # Tạo kết quả dựa trên tác vụ tương tự
            similar_result = self.results[similar_task_id]
            self.results[task.id] = SearchResult(
                task_id=task.id,
                success=similar_result.success,
                data={
                    **similar_result.data,
                    "from_similar_query": True,
                    "similar_task_id": similar_task_id
                },
                error=similar_result.error
            )
            return task.id
        
        # Đưa vào hàng đợi với mức ưu tiên
        try:
            # Ưu tiên cao hơn = số nhỏ hơn
            self.task_queue.put((-task.priority, task))
            
            # Lưu vào lịch sử và từ điển tham chiếu yếu
            self._task_history_dict[task.id] = task
            self.task_history.append(task)
            
            # Kiểm tra kích thước lịch sử và thực hiện dọn dẹp nếu cần
            if len(self.task_history) > self.max_history_size:
                self._trim_task_history()
            
            # Kiểm tra và thực hiện dọn dẹp bộ nhớ định kỳ
            current_time = time.time()
            if current_time - self.last_cleanup_time > self.cleanup_interval:
                self._perform_memory_cleanup()
                self.last_cleanup_time = current_time
            
            logger.debug(f"Đã thêm tác vụ: {task.id} - {task.query}")
            return task.id
            
        except queue.Full:
            logger.warning(f"Hàng đợi đầy, không thể thêm tác vụ: {task.query}")
            return ""
    
    def add_tasks(self, tasks: List[Union[SearchTask, Dict[str, Any]]]) -> List[str]:
        """
        Thêm nhiều tác vụ tìm kiếm.
        
        Args:
            tasks: Danh sách tác vụ tìm kiếm
            
        Returns:
            Danh sách ID của các tác vụ đã thêm
        """
        task_ids = []
        for task in tasks:
            task_id = self.add_task(task)
            if task_id:
                task_ids.append(task_id)
        return task_ids
    
    def start(self) -> None:
        """Bắt đầu xử lý tác vụ."""
        if self.running:
            logger.warning("BatchSearchOptimizer đã đang chạy")
            return
        
        self.running = True
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=self.num_workers)
        
        # Tạo các worker
        self.workers = []
        for i in range(self.num_workers):
            future = self.executor.submit(self._worker_process, i)
            self.workers.append(future)
        
        logger.info(f"Đã khởi động BatchSearchOptimizer với {self.num_workers} worker")
    
    def stop(self, wait_complete: bool = True) -> None:
        """
        Dừng xử lý tác vụ.
        
        Args:
            wait_complete: Chờ cho đến khi hoàn thành tất cả các tác vụ
        """
        if not self.running:
            return
        
        if wait_complete:
            # Chờ cho đến khi hàng đợi trống
            self.task_queue.join()
        
        self.running = False
        self.executor.shutdown(wait=wait_complete)
        logger.info("Đã dừng BatchSearchOptimizer")
    
    def get_result(self, task_id: str) -> Optional[SearchResult]:
        """
        Lấy kết quả cho một tác vụ.
        
        Args:
            task_id: ID của tác vụ
            
        Returns:
            Kết quả tìm kiếm hoặc None nếu không tìm thấy
        """
        return self.results.get(task_id)
    
    def get_all_results(self) -> Dict[str, SearchResult]:
        """
        Lấy tất cả kết quả.
        
        Returns:
            Dictionary chứa tất cả kết quả, được lập chỉ mục theo ID tác vụ
        """
        return self.results.copy()
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Lấy thống kê về trình tối ưu hóa.
        
        Returns:
            Dictionary chứa thống kê
        """
        completed = len(self.results)
        pending = self.task_queue.qsize()
        total = completed + pending
        
        # Tính toán thống kê nâng cao
        success_count = sum(1 for r in self.results.values() if r.success)
        error_count = completed - success_count
        by_engine = {}
        
        for result in self.results.values():
            if not result.success:
                continue
                
            engine = result.data.get("engine", "unknown")
            if engine not in by_engine:
                by_engine[engine] = 0
            by_engine[engine] += 1
        
        return {
            "total_tasks": total,
            "completed_tasks": completed,
            "pending_tasks": pending,
            "success_rate": success_count / completed if completed else 0,
            "error_count": error_count,
            "by_engine": by_engine,
            "workers": self.num_workers,
            "running": self.running
        }
    
    def clear_results(self, keep_last_n: int = 0) -> int:
        """
        Xóa kết quả cũ.
        
        Args:
            keep_last_n: Số lượng kết quả mới nhất cần giữ lại
            
        Returns:
            Số lượng kết quả đã xóa
        """
        if keep_last_n <= 0:
            count = len(self.results)
            self.results.clear()
            self.completed_tasks.clear()
            return count
        
        # Sắp xếp kết quả theo thời gian
        sorted_results = sorted(
            self.results.items(),
            key=lambda x: x[1].timestamp,
            reverse=True
        )
        
        # Giữ lại n kết quả mới nhất
        to_keep = {task_id for task_id, _ in sorted_results[:keep_last_n]}
        to_remove = {task_id for task_id in self.results if task_id not in to_keep}
        
        # Xóa kết quả cũ
        for task_id in to_remove:
            del self.results[task_id]
            if task_id in self.completed_tasks:
                self.completed_tasks.remove(task_id)
        
        return len(to_remove)
    
    def _worker_process(self, worker_id: int) -> None:
        """
        Hàm xử lý của worker.
        
        Args:
            worker_id: ID của worker
        """
        logger.info(f"Worker {worker_id} bắt đầu")
        
        while self.running:
            try:
                # Lấy tác vụ từ hàng đợi
                _, task = self.task_queue.get(timeout=1.0)
                
                # Kiểm tra xem tác vụ đã hoàn thành chưa
                if task.id in self.completed_tasks:
                    self.task_queue.task_done()
                    continue
                
                # Xử lý tác vụ
                result = self._process_task(task, worker_id)
                
                # Lưu kết quả
                self.results[task.id] = result
                self.completed_tasks.add(task.id)
                
                # Đánh dấu tác vụ hoàn thành
                self.task_queue.task_done()
                
                # Thêm thời gian trễ để tránh quá tải
                time.sleep(self.throttle_delay)
                
            except queue.Empty:
                # Không có tác vụ trong hàng đợi
                pass
            except Exception as e:
                logger.error(f"Worker {worker_id} gặp lỗi: {str(e)}")
        
        logger.info(f"Worker {worker_id} dừng")
    
    def _process_task(self, task: SearchTask, worker_id: int) -> SearchResult:
        """
        Xử lý một tác vụ tìm kiếm.
        
        Args:
            task: Tác vụ cần xử lý
            worker_id: ID của worker
            
        Returns:
            Kết quả tìm kiếm
        """
        try:
            # Đặt công cụ tìm kiếm nếu cần thiết
            engine = task.engine
            if not engine and self.engine_rotation:
                engine = self._get_next_engine()
            
            logger.info(f"Worker {worker_id} đang xử lý tác vụ {task.id} với công cụ {engine or 'mặc định'}")
            
            # Thực hiện tìm kiếm
            result = self.search_agent.search(
                query=task.query,
                engine=engine,
                num_results=task.num_results,
                language=task.language,
                **task.params
            )
            
            # Tạo kết quả
            return SearchResult(
                task_id=task.id,
                success=result.get("success", False),
                data=result,
                error=result.get("error")
            )
            
        except Exception as e:
            logger.error(f"Lỗi khi xử lý tác vụ {task.id}: {str(e)}")
            return SearchResult(
                task_id=task.id,
                success=False,
                data={},
                error=str(e)
            )
    
    def _get_next_engine(self) -> str:
        """
        Lấy công cụ tìm kiếm kế tiếp trong danh sách xoay vòng.
        
        Returns:
            Tên của công cụ tìm kiếm
        """
        if not self.search_engines:
            return ""
        
        engine = self.search_engines[self.engine_index]
        self.engine_index = (self.engine_index + 1) % len(self.search_engines)
        return engine
    
    def _find_similar_task(self, query: str) -> Optional[str]:
        """
        Tìm một tác vụ tương tự trong lịch sử.
        
        Args:
            query: Truy vấn cần tìm
            
        Returns:
            ID của tác vụ tương tự hoặc None nếu không tìm thấy
        """
        # Trong trường hợp thực tế, cần sử dụng một thuật toán so sánh văn bản tốt hơn
        # Ví dụ: sử dụng embeddings và tính tương đồng cosine
        
        # Giải pháp đơn giản: so sánh từ khóa
        query_words = set(query.lower().split())
        best_match = None
        best_score = 0.0
        
        for task in self.task_history:
            if task.id not in self.completed_tasks:
                continue
                
            task_words = set(task.query.lower().split())
            
            # Đo lường sự tương đồng dựa trên từ chung
            union_size = len(query_words.union(task_words))
            if union_size == 0:
                continue
                
            intersection_size = len(query_words.intersection(task_words))
            similarity = intersection_size / union_size
            
            if similarity > self.similar_query_threshold and similarity > best_score:
                best_score = similarity
                best_match = task.id
        
        return best_match
    
    def _trim_task_history(self) -> None:
        """Cắt giảm lịch sử tác vụ để giữ kích thước trong giới hạn."""
        if len(self.task_history) <= self.max_history_size:
            return
        
        # Giữ lại 80% nhiệm vụ mới nhất
        keep_count = int(self.max_history_size * 0.8)
        
        # Sắp xếp theo thời gian và loại bỏ các nhiệm vụ cũ
        self.task_history = sorted(self.task_history, key=lambda t: t.timestamp, reverse=True)[:keep_count]
        
        logger.info(f"Đã cắt giảm lịch sử tác vụ xuống {keep_count} mục")
    
    def _perform_memory_cleanup(self) -> None:
        """Thực hiện dọn dẹp bộ nhớ định kỳ."""
        try:
            # Xóa các kết quả cũ hơn 1 giờ nếu không còn trong completed_tasks
            current_time = time.time()
            old_results = [
                task_id for task_id, result in self.results.items()
                if current_time - result.timestamp > 3600 and task_id not in self.completed_tasks
            ]
            
            for task_id in old_results:
                del self.results[task_id]
                
            # Xóa completed_tasks cũ (hơn 2 giờ)
            self.completed_tasks = {
                task_id for task_id in self.completed_tasks
                if task_id in self.results and current_time - self.results[task_id].timestamp <= 7200
            }
            
            # Gọi garbage collector để đảm bảo giải phóng bộ nhớ
            gc.collect()
            
            logger.info(f"Dọn dẹp bộ nhớ: đã xóa {len(old_results)} kết quả cũ")
        except Exception as e:
            logger.error(f"Lỗi khi dọn dẹp bộ nhớ: {str(e)}")
    
    def force_memory_cleanup(self) -> Dict[str, int]:
        """
        Buộc dọn dẹp bộ nhớ ngay lập tức.
        
        Returns:
            Dictionary chứa thông tin về số lượng đối tượng đã xóa
        """
        results_before = len(self.results)
        completed_before = len(self.completed_tasks)
        history_before = len(self.task_history)
        
        # Thực hiện dọn dẹp
        self._trim_task_history()
        self._perform_memory_cleanup()
        
        # Đảm bảo giải phóng bộ nhớ
        gc.collect()
        
        return {
            "results_removed": results_before - len(self.results),
            "completed_tasks_removed": completed_before - len(self.completed_tasks),
            "history_trimmed": history_before - len(self.task_history)
        } 