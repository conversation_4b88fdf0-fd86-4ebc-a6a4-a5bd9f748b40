"""
Web search methods for WebSearchAgent.

This module provides implementations for various search methods used by WebSearchAgent.
"""

import json
import logging
import time
import urllib.parse
from typing import Dict, Any, List, Optional
from bs4 import BeautifulSoup

from ..utils.structured_logging import get_logger
from .network_utils import http_get, create_default_headers, with_retry, with_adaptive_timeout

# Create a logger
logger = get_logger(__name__)

def search_google(query: str, api_key: str, cse_id: str, num_results: int = 10,
                 language: Optional[str] = None, safe_search: Optional[bool] = None) -> Dict[str, Any]:
    """
    Search using Google Custom Search API.
    This is a paid API and has been disabled.

    Args:
        query: The query to search for
        api_key: Google API key
        cse_id: Google Custom Search Engine ID
        num_results: Number of results to return
        language: Language code for search results
        safe_search: Whether to enable safe search

    Returns:
        Dictionary containing error message
    """
    logger.warning("Google Search API is a paid service and has been disabled")
    return {
        "success": False,
        "search_method": "api",
        "engine": "google",
        "query": query,
        "error": "Google Search API is a paid service and has been disabled",
        "results": []
    }

def search_bing(query: str, api_key: str, num_results: int = 10,
               language: Optional[str] = None, safe_search: Optional[bool] = None,
               region: Optional[str] = None) -> Dict[str, Any]:
    """
    Search using Bing Search API.
    This is a paid API and has been disabled.

    Args:
        query: The query to search for
        api_key: Bing API key
        num_results: Number of results to return
        language: Language code for search results
        safe_search: Whether to enable safe search
        region: Region code for search results

    Returns:
        Dictionary containing error message
    """
    logger.warning("Bing Search API is a paid service and has been disabled")
    return {
        "success": False,
        "search_method": "api",
        "engine": "bing",
        "query": query,
        "error": "Bing Search API is a paid service and has been disabled",
        "results": []
    }

def search_serper(query: str, api_key: str, num_results: int = 10,
                 language: Optional[str] = None, region: Optional[str] = None) -> Dict[str, Any]:
    """
    Search using Serper.dev API.
    This is a paid API and has been disabled.

    Args:
        query: The query to search for
        api_key: Serper API key
        num_results: Number of results to return
        language: Language code for search results
        region: Region code for search results

    Returns:
        Dictionary containing error message
    """
    logger.warning("Serper.dev API is a paid service and has been disabled")
    return {
        "success": False,
        "search_method": "api",
        "engine": "serper",
        "query": query,
        "error": "Serper.dev API is a paid service and has been disabled",
        "results": []
    }

def search_serpapi(query: str, api_key: str, num_results: int = 10,
                  language: Optional[str] = None, region: Optional[str] = None,
                  safe_search: Optional[bool] = None) -> Dict[str, Any]:
    """
    Search using SerpAPI.
    This is a paid API and has been disabled.

    Args:
        query: The query to search for
        api_key: SerpAPI key
        num_results: Number of results to return
        language: Language code for search results
        region: Region code for search results
        safe_search: Whether to enable safe search

    Returns:
        Dictionary containing error message
    """
    logger.warning("SerpAPI is a paid service and has been disabled")
    return {
        "success": False,
        "search_method": "api",
        "engine": "serpapi",
        "query": query,
        "error": "SerpAPI is a paid service and has been disabled",
        "results": []
    }

@with_retry(max_retries=3, backoff_factor=0.5)
@with_adaptive_timeout(base_timeout=20.0, content_type="html")
def search_qwant(query: str, num_results: int = 10, language: Optional[str] = None) -> Dict[str, Any]:
    """
    Search using Qwant.

    Args:
        query: The query to search for
        num_results: Number of results to return
        language: Language code for search results

    Returns:
        Dictionary containing search results
    """
    try:
        # Create headers with realistic values using utility function
        headers = create_default_headers(
            accept_language=f"{language if language else 'en'}-US,{language if language else 'en'};q=0.9",
            referer="https://www.google.com/"
        )

        # Prepare search parameters with more options to look like a real user
        params = {
            "q": query,
            "t": "web",
            "client": "opensearch",
            "s": "0",  # Start position
            "locale": language if language else "en_US"
        }

        # Add language if specified
        if language:
            params["l"] = language

        # Thêm cookie để giả lập người dùng đã đăng nhập
        cookies = {
            "qwant-session": "session-" + str(int(time.time())),
            "qwant-locale": language if language else "en_US",
            "qwant-consent": "true"
        }

        # Add a small delay before making the request to avoid rate limiting
        time.sleep(1.0)

        # Make the request with advanced error handling
        response = http_get(
            "https://www.qwant.com/",
            params=params,
            headers=headers,
            cookies=cookies,
            content_type="html"  # Explicitly specify content type for timeout calculation
        )
        response.raise_for_status()

        # Parse the HTML response
        soup = BeautifulSoup(response.text, "html.parser")

        # Extract results
        results = []
        result_elements = soup.select(".result")

        for element in result_elements[:num_results]:
            # Extract title and URL
            title_element = element.select_one(".result__title")
            url_element = element.select_one(".result__url")
            snippet_element = element.select_one(".result__desc")
            
            if title_element and url_element:
                title = title_element.get_text(strip=True)
                url = url_element.get("href") or url_element.get_text(strip=True)
                snippet = snippet_element.get_text(strip=True) if snippet_element else ""
                
                results.append({
                    "title": title,
                    "url": url,
                    "snippet": snippet,
                    "engine": "qwant"
                })
        
        # If no results found with selectors, try alternative selectors
        if not results:
            links = soup.select("a.external")
            for link in links[:num_results]:
                title = link.get_text(strip=True)
                url = link.get("href", "")
                if title and url:
                    results.append({
                        "title": title,
                        "url": url,
                        "snippet": "",
                        "engine": "qwant"
                    })
        
        return {
            "success": True,
            "search_method": "html",
            "engine": "qwant",
            "query": query,
            "results": results
        }
        
    except Exception as e:
        logger.error(f"Error in Qwant search: {str(e)}")
        return {
            "success": False,
            "search_method": "html",
            "engine": "qwant", 
            "query": query,
            "error": f"Error in Qwant search: {str(e)}",
            "results": []
        }

def search_brave(query: str, api_key: Optional[str] = None, num_results: int = 10,
                language: Optional[str] = None, safe_search: Optional[bool] = None) -> Dict[str, Any]:
    """
    Search using Brave Search API.
    This is a paid API and has been disabled.

    Args:
        query: The query to search for
        api_key: Brave Search API key (optional for free tier)
        num_results: Number of results to return
        language: Language code for search results
        safe_search: Whether to enable safe search

    Returns:
        Dictionary containing error message
    """
    logger.warning("Brave Search API is a paid service and has been disabled")
    return {
        "success": False,
        "search_method": "api",
        "engine": "brave",
        "query": query,
        "error": "Brave Search API is a paid service and has been disabled",
        "results": []
    }

def search_yandex(query: str, api_key: str, num_results: int = 10,
                 language: Optional[str] = None, region: Optional[str] = None) -> Dict[str, Any]:
    """
    Search using Yandex Search API.
    This is a paid API and has been disabled.

    Args:
        query: The query to search for
        api_key: Yandex API key
        num_results: Number of results to return
        language: Language code for search results
        region: Region code for search results

    Returns:
        Dictionary containing error message
    """
    logger.warning("Yandex Search API is a paid service and has been disabled")
    return {
        "success": False,
        "search_method": "api",
        "engine": "yandex",
        "query": query,
        "error": "Yandex Search API is a paid service and has been disabled",
        "results": []
    }

def search_duckduckgo(query: str, num_results: int = 10, language: Optional[str] = None,
                  time_range: Optional[str] = None) -> Dict[str, Any]:
    """
    Search using DuckDuckGo.

    Args:
        query: The query to search for
        num_results: Number of results to return
        language: Language code for search results
        time_range: Time range for search results

    Returns:
        Dictionary containing search results
    """
    try:
        # First try the API approach
        api_results = _search_duckduckgo_api(query, num_results, language, time_range)
        if api_results.get("success") and api_results.get("results"):
            return api_results

        # If API fails, fall back to HTML scraping
        logger.info("DuckDuckGo API approach failed, falling back to HTML scraping")
        return _search_duckduckgo_html(query, num_results, language, time_range)

    except Exception as e:
        logger.error(f"Both DuckDuckGo search methods failed: {str(e)}")
        return {
            "success": False,
            "search_method": "web",
            "engine": "duckduckgo",
            "query": query,
            "error": f"All DuckDuckGo search methods failed: {str(e)}",
            "results": []
        }

def _search_duckduckgo_api(query: str, num_results: int = 10, language: Optional[str] = None,
                          time_range: Optional[str] = None) -> Dict[str, Any]:
    """
    Search using DuckDuckGo API.

    Args:
        query: The query to search for
        num_results: Number of results to return
        language: Language code for search results
        time_range: Time range for search results

    Returns:
        Dictionary containing search results
    """
    try:
        # Set up headers to mimic a browser
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "application/json",
            "Accept-Language": "en-US,en;q=0.5",
            "Referer": "https://duckduckgo.com/",
            "DNT": "1",
            "Connection": "keep-alive"
        }

        # Prepare search parameters
        params = {
            "q": query,
            "format": "json",
            "pretty": "0",
            "no_redirect": "1",
            "no_html": "1",
            "skip_disambig": "1"
        }

        # Add language if specified
        if language:
            params["kl"] = language

        # Add time range if specified
        if time_range:
            time_map = {
                "day": "d",
                "week": "w",
                "month": "m",
                "year": "y"
            }
            if time_range in time_map:
                params["df"] = time_map[time_range]

        # Make the request
        response = requests.get(
            "https://api.duckduckgo.com/",
            params=params,
            headers=headers,
            timeout=10
        )
        response.raise_for_status()

        # Parse the JSON response
        data = response.json()

        # Extract results
        results = []

        # Extract abstract (if any)
        if data.get("Abstract"):
            results.append({
                "title": data.get("Heading", "Abstract"),
                "url": data.get("AbstractURL", ""),
                "snippet": data.get("Abstract", ""),
                "source": "duckduckgo_abstract"
            })

        # Extract related topics
        for topic in data.get("RelatedTopics", []):
            if "Topics" in topic:
                # This is a category
                for subtopic in topic.get("Topics", []):
                    if subtopic.get("Text") and subtopic.get("FirstURL"):
                        results.append({
                            "title": subtopic.get("Text", "").split(" - ")[0],
                            "url": subtopic.get("FirstURL", ""),
                            "snippet": subtopic.get("Text", ""),
                            "source": "duckduckgo_related"
                        })
            else:
                # This is a direct topic
                if topic.get("Text") and topic.get("FirstURL"):
                    results.append({
                        "title": topic.get("Text", "").split(" - ")[0],
                        "url": topic.get("FirstURL", ""),
                        "snippet": topic.get("Text", ""),
                        "source": "duckduckgo_related"
                    })

        # If we have enough results, return them
        if len(results) >= num_results:
            return {
                "success": True,
                "search_method": "api",
                "engine": "duckduckgo",
                "query": query,
                "results": results[:num_results],
                "timestamp": time.time()
            }

        # If not enough results, return what we have (even if empty)
        if results:
            return {
                "success": True,
                "search_method": "api",
                "engine": "duckduckgo",
                "query": query,
                "results": results,
                "timestamp": time.time()
            }

        # If no results, return failure
        return {
            "success": False,
            "search_method": "api",
            "engine": "duckduckgo",
            "query": query,
            "error": "No results found with DuckDuckGo API",
            "results": []
        }

    except Exception as e:
        logger.error(f"Error searching DuckDuckGo API: {str(e)}")
        return {
            "success": False,
            "search_method": "api",
            "engine": "duckduckgo",
            "query": query,
            "error": f"DuckDuckGo API search failed: {str(e)}",
            "results": []
        }

def _search_duckduckgo_html(query: str, num_results: int = 10, language: Optional[str] = None,
                           time_range: Optional[str] = None) -> Dict[str, Any]:
    """
    Search using DuckDuckGo by scraping HTML results.

    Args:
        query: The query to search for
        num_results: Number of results to return
        language: Language code for search results
        time_range: Time range for search results

    Returns:
        Dictionary containing search results
    """
    try:
        # Set up headers to mimic a browser
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Referer": "https://duckduckgo.com/",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1"
        }

        # Prepare search parameters
        params = {
            "q": query,
            "ia": "web"
        }

        # Add language if specified
        if language:
            params["kl"] = language

        # Add time range if specified
        if time_range:
            time_map = {
                "day": "d",
                "week": "w",
                "month": "m",
                "year": "y"
            }
            if time_range in time_map:
                params["df"] = time_map[time_range]

        # Make the request
        response = requests.get(
            "https://html.duckduckgo.com/html/",
            params=params,
            headers=headers,
            timeout=10
        )
        response.raise_for_status()

        # Parse the HTML response
        soup = BeautifulSoup(response.text, "html.parser")

        # Extract results
        results = []
        result_elements = soup.select(".result")

        for element in result_elements[:num_results]:
            # Extract title and URL
            title_element = element.select_one(".result__title")
            url_element = element.select_one(".result__url")
            snippet_element = element.select_one(".result__snippet")

            if title_element:
                # Get title
                title = title_element.get_text().strip()

                # Get URL
                url = ""
                link_element = title_element.select_one("a")
                if link_element and link_element.has_attr("href"):
                    # Clean up URL (it often has /l/?kh=-1&uddg= prefix)
                    raw_url = link_element["href"]
                    if "uddg=" in raw_url:
                        url = urllib.parse.unquote(raw_url.split("uddg=")[1])
                    else:
                        url = raw_url
                elif url_element:
                    url = url_element.get_text().strip()

                # Get snippet
                snippet = ""
                if snippet_element:
                    snippet = snippet_element.get_text().strip()

                # Add result
                if url:
                    results.append({
                        "title": title,
                        "url": url,
                        "snippet": snippet,
                        "source": "duckduckgo_html"
                    })

        # If we couldn't extract results using the expected structure,
        # try an alternative approach
        if not results:
            # Try alternative selectors
            links = soup.select("a.result__a")
            for link in links[:num_results]:
                title = link.get_text().strip()
                url = link.get("href", "")

                # Clean up URL
                if "uddg=" in url:
                    url = urllib.parse.unquote(url.split("uddg=")[1])

                # Find snippet
                snippet = ""
                snippet_element = link.find_next("div", class_="result__snippet")
                if snippet_element:
                    snippet = snippet_element.get_text().strip()

                if url:
                    results.append({
                        "title": title,
                        "url": url,
                        "snippet": snippet,
                        "source": "duckduckgo_html"
                    })

        return {
            "success": True if results else False,
            "search_method": "html",
            "engine": "duckduckgo",
            "query": query,
            "results": results,
            "error": "No results found" if not results else None,
            "timestamp": time.time()
        }

    except Exception as e:
        logger.error(f"Error searching DuckDuckGo HTML: {str(e)}")
        return {
            "success": False,
            "search_method": "html",
            "engine": "duckduckgo",
            "query": query,
            "error": f"DuckDuckGo HTML search failed: {str(e)}",
            "results": []
        }
