#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module cung cấp các hàm tích hợp AdaptiveScraper với WebSearchAgentLocal.

Module này cung cấp các hàm để tích hợp AdaptiveScraper với WebSearchAgentLocal,
cho phép WebSearchAgentLocal sử dụng AdaptiveScraper để scrape nội dung web một cách thích ứng.
"""

import logging
import time
from typing import Dict, List, Any, Optional

from ..scrapers.adaptive_scraper import AdaptiveScraper
from ..utils.structured_logging import get_logger

# Thiết lập logging
logger = get_logger(__name__)

def integrate_adaptive_scraper(agent, config: Dict[str, Any] = None) -> None:
    """
    Tích hợp AdaptiveScraper vào WebSearchAgentLocal.
    
    Args:
        agent: WebSearchAgentLocal instance
        config: <PERSON><PERSON><PERSON> hình cho AdaptiveScraper
    """
    if config is None:
        config = {}
    
    # Khởi tạo AdaptiveScraper
    try:
        agent._adaptive_scraper = AdaptiveScraper(
            timeout=config.get("timeout", 10.0),
            user_agent=config.get("user_agent", agent.user_agent if hasattr(agent, "user_agent") else None),
            headers=config.get("headers"),
            proxies=config.get("proxies"),
            verify_ssl=config.get("verify_ssl", True),
            max_retries=config.get("max_retries", 3),
            retry_delay=config.get("retry_delay", 2.0),
            cache_enabled=config.get("cache_enabled", True),
            cache_ttl=config.get("cache_ttl", 3600),
            cache_size=config.get("cache_size", 1000),
            extract_metadata=config.get("extract_metadata", True),
            extract_main_content=config.get("extract_main_content", True),
            extract_images=config.get("extract_images", False),
            extract_links=config.get("extract_links", True),
            extract_tables=config.get("extract_tables", False),
            extract_lists=config.get("extract_lists", True),
            extract_headings=config.get("extract_headings", True),
            extract_structured_data=config.get("extract_structured_data", False),
            detect_language=config.get("detect_language", True),
            detect_encoding=config.get("detect_encoding", True),
            detect_content_type=config.get("detect_content_type", True),
            detect_site_type=config.get("detect_site_type", True),
            clean_content=config.get("clean_content", True),
            remove_ads=config.get("remove_ads", True),
            remove_navigation=config.get("remove_navigation", True),
            remove_sidebars=config.get("remove_sidebars", True),
            remove_footers=config.get("remove_footers", True),
            remove_comments=config.get("remove_comments", True),
            verbose=config.get("verbose", agent.verbose if hasattr(agent, "verbose") else False)
        )
        
        # Gán các phương thức tích hợp
        agent._scrape_url = scrape_url
        agent._scrape_urls = scrape_urls
        agent._extract_content_from_url = extract_content_from_url
        agent._extract_metadata_from_url = extract_metadata_from_url
        agent._extract_main_content_from_url = extract_main_content_from_url
        agent._extract_structured_data_from_url = extract_structured_data_from_url
        
        logger.info("AdaptiveScraper integrated successfully")
    except Exception as e:
        logger.error(f"Failed to integrate AdaptiveScraper: {str(e)}")

def scrape_url(agent, url: str, **kwargs) -> Dict[str, Any]:
    """
    Scrape một URL sử dụng AdaptiveScraper.
    
    Args:
        agent: WebSearchAgentLocal instance
        url: URL cần scrape
        **kwargs: Các tham số bổ sung
        
    Returns:
        Dict[str, Any]: Kết quả scrape
    """
    if not hasattr(agent, "_adaptive_scraper") or agent._adaptive_scraper is None:
        logger.warning("AdaptiveScraper not available")
        return {"success": False, "url": url, "error": "AdaptiveScraper not available"}
    
    # Scrape URL
    return agent._adaptive_scraper.scrape(url, **kwargs)

def scrape_urls(agent, urls: List[str], **kwargs) -> List[Dict[str, Any]]:
    """
    Scrape nhiều URL sử dụng AdaptiveScraper.
    
    Args:
        agent: WebSearchAgentLocal instance
        urls: Danh sách URL cần scrape
        **kwargs: Các tham số bổ sung
        
    Returns:
        List[Dict[str, Any]]: Danh sách kết quả scrape
    """
    if not hasattr(agent, "_adaptive_scraper") or agent._adaptive_scraper is None:
        logger.warning("AdaptiveScraper not available")
        return [{"success": False, "url": url, "error": "AdaptiveScraper not available"} for url in urls]
    
    # Scrape URLs
    results = []
    for url in urls:
        result = agent._adaptive_scraper.scrape(url, **kwargs)
        results.append(result)
    
    return results

def extract_content_from_url(agent, url: str, **kwargs) -> str:
    """
    Trích xuất nội dung từ URL sử dụng AdaptiveScraper.
    
    Args:
        agent: WebSearchAgentLocal instance
        url: URL cần trích xuất nội dung
        **kwargs: Các tham số bổ sung
        
    Returns:
        str: Nội dung trích xuất
    """
    if not hasattr(agent, "_adaptive_scraper") or agent._adaptive_scraper is None:
        logger.warning("AdaptiveScraper not available")
        return f"[Error: AdaptiveScraper not available for URL: {url}]"
    
    # Scrape URL
    result = agent._adaptive_scraper.scrape(url, **kwargs)
    
    # Kiểm tra kết quả
    if not result.get("success", False):
        error = result.get("error", "Unknown error")
        return f"[Error: {error} for URL: {url}]"
    
    # Trả về nội dung
    return result.get("content", "")

def extract_metadata_from_url(agent, url: str, **kwargs) -> Dict[str, Any]:
    """
    Trích xuất metadata từ URL sử dụng AdaptiveScraper.
    
    Args:
        agent: WebSearchAgentLocal instance
        url: URL cần trích xuất metadata
        **kwargs: Các tham số bổ sung
        
    Returns:
        Dict[str, Any]: Metadata
    """
    if not hasattr(agent, "_adaptive_scraper") or agent._adaptive_scraper is None:
        logger.warning("AdaptiveScraper not available")
        return {}
    
    # Scrape URL
    result = agent._adaptive_scraper.scrape(url, **kwargs)
    
    # Kiểm tra kết quả
    if not result.get("success", False):
        return {}
    
    # Trả về metadata
    return result.get("metadata", {})

def extract_main_content_from_url(agent, url: str, **kwargs) -> str:
    """
    Trích xuất nội dung chính từ URL sử dụng AdaptiveScraper.
    
    Args:
        agent: WebSearchAgentLocal instance
        url: URL cần trích xuất nội dung chính
        **kwargs: Các tham số bổ sung
        
    Returns:
        str: Nội dung chính
    """
    if not hasattr(agent, "_adaptive_scraper") or agent._adaptive_scraper is None:
        logger.warning("AdaptiveScraper not available")
        return f"[Error: AdaptiveScraper not available for URL: {url}]"
    
    # Scrape URL
    result = agent._adaptive_scraper.scrape(url, **kwargs)
    
    # Kiểm tra kết quả
    if not result.get("success", False):
        error = result.get("error", "Unknown error")
        return f"[Error: {error} for URL: {url}]"
    
    # Trả về nội dung chính
    return result.get("content", "")

def extract_structured_data_from_url(agent, url: str, **kwargs) -> Dict[str, Any]:
    """
    Trích xuất dữ liệu có cấu trúc từ URL sử dụng AdaptiveScraper.
    
    Args:
        agent: WebSearchAgentLocal instance
        url: URL cần trích xuất dữ liệu có cấu trúc
        **kwargs: Các tham số bổ sung
        
    Returns:
        Dict[str, Any]: Dữ liệu có cấu trúc
    """
    if not hasattr(agent, "_adaptive_scraper") or agent._adaptive_scraper is None:
        logger.warning("AdaptiveScraper not available")
        return {}
    
    # Scrape URL
    result = agent._adaptive_scraper.scrape(url, **kwargs)
    
    # Kiểm tra kết quả
    if not result.get("success", False):
        return {}
    
    # Trả về dữ liệu có cấu trúc
    return result.get("structured_data", {})
