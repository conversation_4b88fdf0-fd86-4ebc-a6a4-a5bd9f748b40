"""
<PERSON><PERSON><PERSON> chiến lược xử lý lỗi đặc thù cho từng công cụ tìm kiếm.

Module này cung cấp các chiến lược xử lý lỗi được tùy chỉnh cho từng công cụ tìm kiếm cụ thể,
gi<PERSON><PERSON> cải thiện khả năng phục hồi và độ tin cậy của WebSearchAgent.
"""

import re
import time
import logging
import random
from typing import Dict, Any, List, Optional, Union, Tuple
from datetime import datetime, timedelta

from deep_research_core.utils.error_recovery import (
    ErrorRecoveryStrategy,
    RetryStrategy
)
from deep_research_core.agents.web_search_agent_enhanced_error_handling import WebSearchError, WebSearchErrorType

# Lấy logger
logger = logging.getLogger(__name__)

class GoogleSearchErrorStrategy(ErrorRecoveryStrategy):
    """Chiến lược xử lý lỗi đặc thù cho Google Search."""
    
    name = "google_search_error"
    description = "Xử lý các lỗi đặc thù của Google Search"
    
    def __init__(self, **kwargs):
        """Khởi tạo chiến lược xử lý lỗi Google Search."""
        super().__init__(**kwargs)
        self.captcha_wait_time = kwargs.get("captcha_wait_time", 60.0)
        self.max_captcha_retries = kwargs.get("max_captcha_retries", 2)
        self.current_captcha_retries = 0
        self.last_captcha_time = None
    
    def can_handle(self, error: Exception, context: Dict[str, Any]) -> bool:
        """
        Kiểm tra xem chiến lược này có thể xử lý lỗi không.
        
        Args:
            error: Lỗi cần xử lý
            context: Thông tin ngữ cảnh về lỗi
            
        Returns:
            True nếu có thể xử lý, False nếu không
        """
        # Kiểm tra xem có phải lỗi Google Search không
        if not isinstance(error, WebSearchError):
            return False
            
        # Kiểm tra xem có phải đang sử dụng Google Search không
        tool_args = context.get("tool_args", {})
        engine = tool_args.get("engine", "").lower()
        
        if engine != "google":
            return False
            
        # Kiểm tra loại lỗi
        error_type = error.error_type
        error_message = str(error).lower()
        
        # Các lỗi đặc thù của Google
        google_specific_errors = [
            "captcha", "unusual traffic", "automated queries",
            "429", "too many requests", "rate limit",
            "your client has issued a malformed or illegal request"
        ]
        
        return (error_type in [WebSearchErrorType.CAPTCHA, WebSearchErrorType.RATE_LIMIT] or
                any(err in error_message for err in google_specific_errors))
    
    def recover(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Cố gắng khôi phục từ lỗi Google Search.
        
        Args:
            error: Lỗi cần khôi phục
            context: Thông tin ngữ cảnh về lỗi
            
        Returns:
            Từ điển chứa kết quả khôi phục và thông tin bổ sung
        """
        error_message = str(error).lower()
        tool_args = context.get("tool_args", {}).copy()
        
        # Xử lý lỗi CAPTCHA
        if "captcha" in error_message or "unusual traffic" in error_message:
            # Kiểm tra số lần thử lại CAPTCHA
            if self.last_captcha_time and (datetime.now() - self.last_captcha_time).total_seconds() < 3600:
                self.current_captcha_retries += 1
            else:
                self.current_captcha_retries = 1
                
            self.last_captcha_time = datetime.now()
            
            if self.current_captcha_retries > self.max_captcha_retries:
                # Chuyển sang công cụ tìm kiếm khác
                alternative_engine = "duckduckgo"
                logger.info(f"Đã vượt quá số lần thử lại CAPTCHA, chuyển sang {alternative_engine}")
                
                tool_args["engine"] = alternative_engine
                
                return {
                    "success": True,
                    "message": f"Chuyển từ Google sang {alternative_engine} do lỗi CAPTCHA",
                    "reformulated_args": tool_args
                }
            else:
                # Chờ một thời gian trước khi thử lại
                wait_time = self.captcha_wait_time * self.current_captcha_retries
                logger.info(f"Chờ {wait_time}s trước khi thử lại Google Search do lỗi CAPTCHA")
                time.sleep(wait_time)
                
                # Thay đổi User-Agent
                return {
                    "success": True,
                    "message": "Thử lại Google Search với User-Agent mới",
                    "reformulated_args": tool_args,
                    "metadata": {
                        "change_user_agent": True,
                        "captcha_retry_count": self.current_captcha_retries
                    }
                }
        
        # Xử lý lỗi rate limit
        elif "429" in error_message or "too many requests" in error_message or "rate limit" in error_message:
            # Chờ một thời gian ngẫu nhiên trước khi thử lại
            wait_time = random.uniform(30, 60)
            logger.info(f"Chờ {wait_time:.1f}s trước khi thử lại Google Search do lỗi rate limit")
            time.sleep(wait_time)
            
            return {
                "success": True,
                "message": "Thử lại Google Search sau khi chờ đợi",
                "reformulated_args": tool_args
            }
        
        # Xử lý lỗi malformed request
        elif "malformed" in error_message or "illegal request" in error_message:
            # Đơn giản hóa truy vấn
            query = tool_args.get("query", "")
            simplified_query = re.sub(r'[^\w\s]', ' ', query)
            simplified_query = re.sub(r'\s+', ' ', simplified_query).strip()
            
            tool_args["query"] = simplified_query
            
            return {
                "success": True,
                "message": f"Đơn giản hóa truy vấn: '{query}' -> '{simplified_query}'",
                "reformulated_args": tool_args
            }
        
        # Xử lý các lỗi khác
        else:
            # Chuyển sang công cụ tìm kiếm khác
            alternative_engine = "bing"
            logger.info(f"Chuyển từ Google sang {alternative_engine} do lỗi không xác định")
            
            tool_args["engine"] = alternative_engine
            
            return {
                "success": True,
                "message": f"Chuyển từ Google sang {alternative_engine} do lỗi không xác định",
                "reformulated_args": tool_args
            }


class BingSearchErrorStrategy(ErrorRecoveryStrategy):
    """Chiến lược xử lý lỗi đặc thù cho Bing Search."""
    
    name = "bing_search_error"
    description = "Xử lý các lỗi đặc thù của Bing Search"
    
    def __init__(self, **kwargs):
        """Khởi tạo chiến lược xử lý lỗi Bing Search."""
        super().__init__(**kwargs)
        self.max_retries = kwargs.get("max_retries", 3)
        self.retry_delay = kwargs.get("retry_delay", 5.0)
        self.current_retries = 0
    
    def can_handle(self, error: Exception, context: Dict[str, Any]) -> bool:
        """
        Kiểm tra xem chiến lược này có thể xử lý lỗi không.
        
        Args:
            error: Lỗi cần xử lý
            context: Thông tin ngữ cảnh về lỗi
            
        Returns:
            True nếu có thể xử lý, False nếu không
        """
        # Kiểm tra xem có phải đang sử dụng Bing Search không
        tool_args = context.get("tool_args", {})
        engine = tool_args.get("engine", "").lower()
        
        if engine != "bing":
            return False
            
        # Kiểm tra loại lỗi
        error_message = str(error).lower()
        
        # Các lỗi đặc thù của Bing
        bing_specific_errors = [
            "invalid subscription key", "subscription key", 
            "api key", "authentication", "unauthorized",
            "request limit", "quota exceeded"
        ]
        
        return any(err in error_message for err in bing_specific_errors)
    
    def recover(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Cố gắng khôi phục từ lỗi Bing Search.
        
        Args:
            error: Lỗi cần khôi phục
            context: Thông tin ngữ cảnh về lỗi
            
        Returns:
            Từ điển chứa kết quả khôi phục và thông tin bổ sung
        """
        error_message = str(error).lower()
        tool_args = context.get("tool_args", {}).copy()
        
        # Xử lý lỗi API key
        if "invalid subscription key" in error_message or "subscription key" in error_message or "api key" in error_message:
            # Chuyển sang công cụ tìm kiếm không yêu cầu API key
            alternative_engine = "duckduckgo"
            logger.info(f"Chuyển từ Bing sang {alternative_engine} do lỗi API key")
            
            tool_args["engine"] = alternative_engine
            
            return {
                "success": True,
                "message": f"Chuyển từ Bing sang {alternative_engine} do lỗi API key",
                "reformulated_args": tool_args
            }
        
        # Xử lý lỗi quota
        elif "request limit" in error_message or "quota exceeded" in error_message:
            # Chuyển sang công cụ tìm kiếm khác
            alternative_engine = "searx"
            logger.info(f"Chuyển từ Bing sang {alternative_engine} do lỗi quota")
            
            tool_args["engine"] = alternative_engine
            
            return {
                "success": True,
                "message": f"Chuyển từ Bing sang {alternative_engine} do lỗi quota",
                "reformulated_args": tool_args
            }
        
        # Xử lý các lỗi khác
        else:
            self.current_retries += 1
            
            if self.current_retries > self.max_retries:
                # Chuyển sang công cụ tìm kiếm khác
                alternative_engine = "duckduckgo"
                logger.info(f"Đã vượt quá số lần thử lại, chuyển từ Bing sang {alternative_engine}")
                
                tool_args["engine"] = alternative_engine
                
                return {
                    "success": True,
                    "message": f"Chuyển từ Bing sang {alternative_engine} sau {self.max_retries} lần thử lại",
                    "reformulated_args": tool_args
                }
            else:
                # Chờ một thời gian trước khi thử lại
                wait_time = self.retry_delay * self.current_retries
                logger.info(f"Chờ {wait_time}s trước khi thử lại Bing Search (lần thử {self.current_retries}/{self.max_retries})")
                time.sleep(wait_time)
                
                return {
                    "success": True,
                    "message": f"Thử lại Bing Search (lần thử {self.current_retries}/{self.max_retries})",
                    "reformulated_args": tool_args
                }
