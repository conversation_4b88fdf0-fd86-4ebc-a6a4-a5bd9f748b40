"""
<PERSON><PERSON><PERSON> chiến lược xử lý lỗi đặc thù cho từng công cụ tìm kiếm (Phần 2).

<PERSON><PERSON><PERSON> này cung cấp các chiến lược xử lý lỗi được tùy chỉnh cho các công cụ tìm kiếm kh<PERSON>c,
bổ sung cho các chiến lược trong engine_specific_strategies.py.
"""

import re
import time
import logging
import random
from typing import Dict, Any, List, Optional, Union, Tuple
from datetime import datetime, timedelta

from deep_research_core.utils.error_recovery import (
    ErrorRecoveryStrategy,
    RetryStrategy
)
from deep_research_core.agents.web_search_agent_enhanced_error_handling import WebSearchError, WebSearchErrorType

# Lấy logger
logger = logging.getLogger(__name__)

class DuckDuckGoErrorStrategy(ErrorRecoveryStrategy):
    """Chiến lược xử lý lỗi đặc thù cho DuckDuckGo."""
    
    name = "duckduckgo_search_error"
    description = "Xử lý các lỗi đặc thù của DuckDuckGo"
    
    def __init__(self, **kwargs):
        """Khởi tạo chiến lược xử lý lỗi DuckDuckGo."""
        super().__init__(**kwargs)
        self.html_fallback = kwargs.get("html_fallback", True)
        self.max_retries = kwargs.get("max_retries", 3)
        self.current_retries = 0
    
    def can_handle(self, error: Exception, context: Dict[str, Any]) -> bool:
        """
        Kiểm tra xem chiến lược này có thể xử lý lỗi không.
        
        Args:
            error: Lỗi cần xử lý
            context: Thông tin ngữ cảnh về lỗi
            
        Returns:
            True nếu có thể xử lý, False nếu không
        """
        # Kiểm tra xem có phải đang sử dụng DuckDuckGo không
        tool_args = context.get("tool_args", {})
        engine = tool_args.get("engine", "").lower()
        
        if engine != "duckduckgo":
            return False
            
        # Kiểm tra loại lỗi
        error_message = str(error).lower()
        
        # Các lỗi đặc thù của DuckDuckGo
        ddg_specific_errors = [
            "api error", "connection error", "timeout", 
            "no results", "blocked", "403", "forbidden",
            "cloudflare", "ddg api"
        ]
        
        return any(err in error_message for err in ddg_specific_errors)
    
    def recover(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Cố gắng khôi phục từ lỗi DuckDuckGo.
        
        Args:
            error: Lỗi cần khôi phục
            context: Thông tin ngữ cảnh về lỗi
            
        Returns:
            Từ điển chứa kết quả khôi phục và thông tin bổ sung
        """
        error_message = str(error).lower()
        tool_args = context.get("tool_args", {}).copy()
        
        # Xử lý lỗi API
        if "api error" in error_message and self.html_fallback:
            logger.info("Chuyển từ DuckDuckGo API sang phương thức HTML scraping")
            
            return {
                "success": True,
                "message": "Chuyển từ DuckDuckGo API sang phương thức HTML scraping",
                "reformulated_args": tool_args,
                "metadata": {
                    "use_html_method": True
                }
            }
        
        # Xử lý lỗi bị chặn
        elif "blocked" in error_message or "403" in error_message or "forbidden" in error_message or "cloudflare" in error_message:
            # Thử thay đổi User-Agent và sử dụng proxy
            self.current_retries += 1
            
            if self.current_retries > self.max_retries:
                # Chuyển sang công cụ tìm kiếm khác
                alternative_engine = "searx"
                logger.info(f"Đã vượt quá số lần thử lại, chuyển từ DuckDuckGo sang {alternative_engine}")
                
                tool_args["engine"] = alternative_engine
                
                return {
                    "success": True,
                    "message": f"Chuyển từ DuckDuckGo sang {alternative_engine} sau {self.max_retries} lần thử lại",
                    "reformulated_args": tool_args
                }
            else:
                # Chờ một thời gian ngẫu nhiên trước khi thử lại
                wait_time = random.uniform(5, 15) * self.current_retries
                logger.info(f"Chờ {wait_time:.1f}s trước khi thử lại DuckDuckGo với User-Agent mới")
                time.sleep(wait_time)
                
                return {
                    "success": True,
                    "message": f"Thử lại DuckDuckGo với User-Agent mới (lần thử {self.current_retries}/{self.max_retries})",
                    "reformulated_args": tool_args,
                    "metadata": {
                        "change_user_agent": True,
                        "use_proxy": True
                    }
                }
        
        # Xử lý lỗi không có kết quả
        elif "no results" in error_message:
            # Đơn giản hóa truy vấn
            query = tool_args.get("query", "")
            words = query.split()
            
            if len(words) > 3:
                simplified_query = " ".join(words[:3])
                logger.info(f"Đơn giản hóa truy vấn DuckDuckGo: '{query}' -> '{simplified_query}'")
                
                tool_args["query"] = simplified_query
                
                return {
                    "success": True,
                    "message": f"Đơn giản hóa truy vấn: '{query}' -> '{simplified_query}'",
                    "reformulated_args": tool_args
                }
            else:
                # Chuyển sang công cụ tìm kiếm khác
                alternative_engine = "qwant"
                logger.info(f"Chuyển từ DuckDuckGo sang {alternative_engine} do không có kết quả")
                
                tool_args["engine"] = alternative_engine
                
                return {
                    "success": True,
                    "message": f"Chuyển từ DuckDuckGo sang {alternative_engine} do không có kết quả",
                    "reformulated_args": tool_args
                }
        
        # Xử lý lỗi timeout
        elif "timeout" in error_message:
            # Thử lại với timeout dài hơn
            logger.info("Thử lại DuckDuckGo với timeout dài hơn")
            
            tool_args["timeout"] = tool_args.get("timeout", 10) * 1.5
            
            return {
                "success": True,
                "message": f"Thử lại DuckDuckGo với timeout {tool_args['timeout']}s",
                "reformulated_args": tool_args
            }
        
        # Xử lý các lỗi khác
        else:
            # Chuyển sang công cụ tìm kiếm khác
            alternative_engine = "searx"
            logger.info(f"Chuyển từ DuckDuckGo sang {alternative_engine} do lỗi không xác định")
            
            tool_args["engine"] = alternative_engine
            
            return {
                "success": True,
                "message": f"Chuyển từ DuckDuckGo sang {alternative_engine} do lỗi không xác định",
                "reformulated_args": tool_args
            }
