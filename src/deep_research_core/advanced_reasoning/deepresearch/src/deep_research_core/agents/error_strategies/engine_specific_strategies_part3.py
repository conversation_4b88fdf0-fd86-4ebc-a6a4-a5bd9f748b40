"""
<PERSON><PERSON><PERSON> chiến lược xử lý lỗi đặc thù cho từng công cụ tìm kiếm (Phần 3).

<PERSON><PERSON>le này cung cấp các chiến lược xử lý lỗi được tùy chỉnh cho các công cụ tìm kiếm kh<PERSON>,
bổ sung cho các chiến lược trong engine_specific_strategies.py và engine_specific_strategies_part2.py.
"""

import re
import time
import logging
import random
from typing import Dict, Any, List, Optional, Union, Tuple
from datetime import datetime, timedelta

from deep_research_core.utils.error_recovery import (
    ErrorRecoveryStrategy,
    RetryStrategy
)
from deep_research_core.agents.web_search_agent_enhanced_error_handling import WebSearchError, WebSearchErrorType

# Lấy logger
logger = logging.getLogger(__name__)

class SearxErrorStrategy(ErrorRecoveryStrategy):
    """Chiến lược xử lý lỗi đặc thù cho Searx/SearxNG."""
    
    name = "searx_search_error"
    description = "Xử lý các lỗi đặc thù của Searx/SearxNG"
    
    def __init__(self, **kwargs):
        """Khởi tạo chiến lược xử lý lỗi Searx."""
        super().__init__(**kwargs)
        self.instance_rotation = kwargs.get("instance_rotation", True)
        self.searx_instances = kwargs.get("searx_instances", [
            "https://searx.be",
            "https://search.disroot.org",
            "https://searx.tiekoetter.com",
            "https://search.privacyguides.net"
        ])
        self.current_instance_index = 0
    
    def can_handle(self, error: Exception, context: Dict[str, Any]) -> bool:
        """
        Kiểm tra xem chiến lược này có thể xử lý lỗi không.
        
        Args:
            error: Lỗi cần xử lý
            context: Thông tin ngữ cảnh về lỗi
            
        Returns:
            True nếu có thể xử lý, False nếu không
        """
        # Kiểm tra xem có phải đang sử dụng Searx không
        tool_args = context.get("tool_args", {})
        engine = tool_args.get("engine", "").lower()
        
        if engine not in ["searx", "searxng"]:
            return False
            
        # Kiểm tra loại lỗi
        error_message = str(error).lower()
        
        # Các lỗi đặc thù của Searx
        searx_specific_errors = [
            "connection error", "timeout", "searx instance",
            "unreachable", "404", "not found", "500", "internal server error",
            "503", "service unavailable"
        ]
        
        return any(err in error_message for err in searx_specific_errors)
    
    def recover(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Cố gắng khôi phục từ lỗi Searx.
        
        Args:
            error: Lỗi cần khôi phục
            context: Thông tin ngữ cảnh về lỗi
            
        Returns:
            Từ điển chứa kết quả khôi phục và thông tin bổ sung
        """
        error_message = str(error).lower()
        tool_args = context.get("tool_args", {}).copy()
        
        # Xử lý lỗi instance không khả dụng
        if self.instance_rotation and ("connection error" in error_message or 
                                      "timeout" in error_message or 
                                      "unreachable" in error_message or 
                                      "404" in error_message or 
                                      "500" in error_message or 
                                      "503" in error_message):
            # Chuyển sang instance khác
            self.current_instance_index = (self.current_instance_index + 1) % len(self.searx_instances)
            new_instance = self.searx_instances[self.current_instance_index]
            
            logger.info(f"Chuyển sang instance Searx khác: {new_instance}")
            
            tool_args["searx_url"] = new_instance
            
            return {
                "success": True,
                "message": f"Chuyển sang instance Searx khác: {new_instance}",
                "reformulated_args": tool_args
            }
        
        # Xử lý các lỗi khác
        else:
            # Chuyển sang công cụ tìm kiếm khác
            alternative_engine = "duckduckgo"
            logger.info(f"Chuyển từ Searx sang {alternative_engine}")
            
            tool_args["engine"] = alternative_engine
            
            return {
                "success": True,
                "message": f"Chuyển từ Searx sang {alternative_engine}",
                "reformulated_args": tool_args
            }


class QwantErrorStrategy(ErrorRecoveryStrategy):
    """Chiến lược xử lý lỗi đặc thù cho Qwant."""
    
    name = "qwant_search_error"
    description = "Xử lý các lỗi đặc thù của Qwant"
    
    def __init__(self, **kwargs):
        """Khởi tạo chiến lược xử lý lỗi Qwant."""
        super().__init__(**kwargs)
        self.max_retries = kwargs.get("max_retries", 2)
        self.current_retries = 0
    
    def can_handle(self, error: Exception, context: Dict[str, Any]) -> bool:
        """
        Kiểm tra xem chiến lược này có thể xử lý lỗi không.
        
        Args:
            error: Lỗi cần xử lý
            context: Thông tin ngữ cảnh về lỗi
            
        Returns:
            True nếu có thể xử lý, False nếu không
        """
        # Kiểm tra xem có phải đang sử dụng Qwant không
        tool_args = context.get("tool_args", {})
        engine = tool_args.get("engine", "").lower()
        
        if engine != "qwant":
            return False
            
        # Kiểm tra loại lỗi
        error_message = str(error).lower()
        
        # Các lỗi đặc thù của Qwant
        qwant_specific_errors = [
            "connection error", "timeout", "qwant api",
            "no results", "blocked", "403", "forbidden",
            "429", "too many requests"
        ]
        
        return any(err in error_message for err in qwant_specific_errors)
    
    def recover(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Cố gắng khôi phục từ lỗi Qwant.
        
        Args:
            error: Lỗi cần khôi phục
            context: Thông tin ngữ cảnh về lỗi
            
        Returns:
            Từ điển chứa kết quả khôi phục và thông tin bổ sung
        """
        error_message = str(error).lower()
        tool_args = context.get("tool_args", {}).copy()
        
        # Xử lý lỗi rate limit
        if "429" in error_message or "too many requests" in error_message:
            # Chờ một thời gian ngẫu nhiên trước khi thử lại
            wait_time = random.uniform(10, 30)
            logger.info(f"Chờ {wait_time:.1f}s trước khi thử lại Qwant do lỗi rate limit")
            time.sleep(wait_time)
            
            return {
                "success": True,
                "message": "Thử lại Qwant sau khi chờ đợi",
                "reformulated_args": tool_args
            }
        
        # Xử lý lỗi bị chặn
        elif "blocked" in error_message or "403" in error_message or "forbidden" in error_message:
            # Thử thay đổi User-Agent và sử dụng proxy
            self.current_retries += 1
            
            if self.current_retries > self.max_retries:
                # Chuyển sang công cụ tìm kiếm khác
                alternative_engine = "searx"
                logger.info(f"Đã vượt quá số lần thử lại, chuyển từ Qwant sang {alternative_engine}")
                
                tool_args["engine"] = alternative_engine
                
                return {
                    "success": True,
                    "message": f"Chuyển từ Qwant sang {alternative_engine} sau {self.max_retries} lần thử lại",
                    "reformulated_args": tool_args
                }
            else:
                # Chờ một thời gian ngẫu nhiên trước khi thử lại
                wait_time = random.uniform(5, 15) * self.current_retries
                logger.info(f"Chờ {wait_time:.1f}s trước khi thử lại Qwant với User-Agent mới")
                time.sleep(wait_time)
                
                return {
                    "success": True,
                    "message": f"Thử lại Qwant với User-Agent mới (lần thử {self.current_retries}/{self.max_retries})",
                    "reformulated_args": tool_args,
                    "metadata": {
                        "change_user_agent": True,
                        "use_proxy": True
                    }
                }
        
        # Xử lý các lỗi khác
        else:
            # Chuyển sang công cụ tìm kiếm khác
            alternative_engine = "duckduckgo"
            logger.info(f"Chuyển từ Qwant sang {alternative_engine} do lỗi không xác định")
            
            tool_args["engine"] = alternative_engine
            
            return {
                "success": True,
                "message": f"Chuyển từ Qwant sang {alternative_engine} do lỗi không xác định",
                "reformulated_args": tool_args
            }
