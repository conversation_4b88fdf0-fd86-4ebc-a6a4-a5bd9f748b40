    def _handle_pagination(self, soup, url: str) -> List[str]:
        """
        Xử lý phân trang và trả về danh sách các URL trang tiếp theo.

        Args:
            soup: BeautifulSoup object của trang hiện tại
            url: URL của trang hiện tại

        Returns:
            List[str]: <PERSON><PERSON><PERSON>ch các URL trang tiếp theo
        """
        pagination_urls = []
        base_url = urlparse(url)
        base_domain = f"{base_url.scheme}://{base_url.netloc}"

        # Sử dụng các selector được cấu hình hoặc mặc định
        pagination_selectors = self.pagination_selectors or [
            ".pagination a", "ul.pagination a", ".pager a",
            "nav.pagination a", ".paginate a", ".page-numbers",
            ".pages a", ".page-link", "[class*='pag'] a"
        ]

        # Tì<PERSON> các liên kết phân trang
        for selector in pagination_selectors:
            try:
                pagination_links = soup.select(selector)
                if pagination_links:
                    for link in pagination_links:
                        href = link.get('href')
                        if href and not href.startswith('#') and 'javascript:' not in href:
                            # Xử lý URL tương đối
                            if href.startswith('/'):
                                href = f"{base_domain}{href}"
                            elif not href.startswith(('http://', 'https://')):
                                href = urljoin(url, href)

                            # Chỉ thêm URL mới
                            if href not in pagination_urls and href != url:
                                pagination_urls.append(href)
            except Exception as e:
                logger.warning(f"Lỗi khi xử lý phân trang với selector {selector}: {str(e)}")

        # Tìm các nút "Trang tiếp" hoặc "Trang sau"
        next_page_texts = ['next', 'next page', 'trang tiếp', 'trang sau', 'tiếp theo', '›', '»', 'trang kế']
        for link in soup.find_all('a'):
            try:
                link_text = link.get_text().lower().strip()
                if any(text in link_text for text in next_page_texts):
                    href = link.get('href')
                    if href and not href.startswith('#') and 'javascript:' not in href:
                        # Xử lý URL tương đối
                        if href.startswith('/'):
                            href = f"{base_domain}{href}"
                        elif not href.startswith(('http://', 'https://')):
                            href = urljoin(url, href)

                        # Chỉ thêm URL mới
                        if href not in pagination_urls and href != url:
                            pagination_urls.append(href)
            except Exception as e:
                continue

        # Tìm các nút có thuộc tính rel="next"
        for link in soup.find_all('a', attrs={'rel': 'next'}):
            try:
                href = link.get('href')
                if href and not href.startswith('#') and 'javascript:' not in href:
                    # Xử lý URL tương đối
                    if href.startswith('/'):
                        href = f"{base_domain}{href}"
                    elif not href.startswith(('http://', 'https://')):
                        href = urljoin(url, href)

                    # Chỉ thêm URL mới
                    if href not in pagination_urls and href != url:
                        pagination_urls.append(href)
            except Exception as e:
                continue

        # Giới hạn số lượng URL phân trang theo cấu hình
        if hasattr(self, 'max_pagination_pages') and self.max_pagination_pages > 0:
            if len(pagination_urls) > self.max_pagination_pages:
                pagination_urls = pagination_urls[:self.max_pagination_pages]
                logger.info(f"Giới hạn số lượng URL phân trang từ {len(pagination_urls)} xuống {self.max_pagination_pages}")

        return pagination_urls
