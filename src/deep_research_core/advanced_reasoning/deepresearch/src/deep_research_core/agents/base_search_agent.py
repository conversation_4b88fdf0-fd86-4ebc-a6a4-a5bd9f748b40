"""
Base search agent module.

This module provides a base class for web search agents with common functionality.
"""

import time
import logging
from typing import Dict, Any, List, Optional, Union
from langdetect import detect, LangDetectException

from ..utils.structured_logging import get_logger
from ..utils.rate_limiter import RateLimiter

# Create a logger
logger = get_logger(__name__)


class BaseSearchAgent:
    """
    Base class for web search agents.

    This class provides common functionality for web search agents, including:
    - Rate limiting
    - Caching
    - Error handling
    - Result formatting
    """

    def __init__(
        self,
        cache_ttl: int = 600,
        rate_limit: int = 10,
        verbose: bool = False,
    ):
        """
        Initialize the base search agent.

        Args:
            cache_ttl: Cache time-to-live in seconds
            rate_limit: Maximum number of requests per minute
            verbose: Whether to log verbose information
        """
        self.cache_ttl = cache_ttl
        self.rate_limit = rate_limit
        self.verbose = verbose

        # Initialize cache
        self.cache = {}
        self.cache_timestamps = {}

        # Initialize rate limiter with default limits
        engine_limits = {
            'duckduckgo': (100, 60),  # 100 requests per minute
            'searx': (60, 60),        # 60 requests per minute
            'qwant': (60, 60),        # 60 requests per minute
            'google': (10, 60),       # 10 requests per minute
            'bing': (20, 60),         # 20 requests per minute
            'crawlee': (100, 60),     # 100 requests per minute
            'default': (rate_limit, 60)  # Default from constructor parameter
        }
        self.rate_limiter = RateLimiter(
            global_limit=rate_limit,
            global_window=60,
            engine_limits=engine_limits
        )

    def _check_rate_limit(self, wait_if_limited: bool = True, engine: str = "default") -> bool:
        """
        Check rate limit for the search engine.

        Args:
            wait_if_limited: Whether to wait if rate limit is exceeded
            engine: Search engine to check rate limit for

        Returns:
            True if rate limit is not exceeded, False otherwise
        """
        # Kiểm tra xem phương thức check_limit có chấp nhận tham số wait_if_limited không
        if hasattr(self.rate_limiter, 'check_limit'):
            # Kiểm tra signature của phương thức check_limit
            import inspect
            sig = inspect.signature(self.rate_limiter.check_limit)

            if 'wait_if_limited' in sig.parameters:
                # Phương thức cũ với wait_if_limited
                return self.rate_limiter.check_limit(
                    engine=engine,
                    wait_if_limited=wait_if_limited,
                    max_wait_time=60
                )
            else:
                # Phương thức mới không có wait_if_limited
                if wait_if_limited:
                    # Nếu cần chờ, sử dụng wait_if_limited
                    self.rate_limiter.wait_if_limited(engine=engine)

                # Kiểm tra giới hạn
                return self.rate_limiter.check_limit(engine=engine)

        # Fallback nếu không có rate_limiter
        return True

    def _check_cache(self, query: str) -> Optional[Dict[str, Any]]:
        """
        Check cache for query results.

        Args:
            query: Query to check in cache

        Returns:
            Cached results if available and not expired, None otherwise
        """
        if query in self.cache:
            # Check if cache has expired
            timestamp = self.cache_timestamps.get(query, 0)
            if time.time() - timestamp < self.cache_ttl:
                logger.info(f"Found cached results for query: {query}")
                return self.cache[query]
            else:
                # Remove expired cache
                del self.cache[query]
                del self.cache_timestamps[query]

        return None

    def _update_cache(self, query: str, results: Dict[str, Any]):
        """
        Update cache with query results.

        Args:
            query: Query to cache
            results: Results to cache
        """
        self.cache[query] = results
        self.cache_timestamps[query] = time.time()

    def _detect_language(self, query: str, default_language: str = "en") -> str:
        """
        Detect language of query.

        Args:
            query: Query to detect language for
            default_language: Default language to use if detection fails

        Returns:
            Detected language code or default language
        """
        try:
            return detect(query)
        except LangDetectException:
            return default_language

    def _is_vietnamese(self, query: str) -> bool:
        """
        Check if query is in Vietnamese.

        Args:
            query: Query to check

        Returns:
            True if query is in Vietnamese, False otherwise
        """
        try:
            # Try to detect the query language
            query_language = detect(query)
            return query_language == "vi"
        except Exception:
            # If language detection fails, check for Vietnamese characters
            return any(
                c in query
                for c in "áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđ"
            )

    def _format_error_response(
        self,
        query: str,
        error: str,
        engine: str = "unknown",
        search_method: str = "unknown",
        detailed_errors: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """
        Format error response.

        Args:
            query: Original query
            error: Error message
            engine: Search engine used
            search_method: Search method used
            detailed_errors: Detailed error information

        Returns:
            Formatted error response
        """
        response = {
            "success": False,
            "error": error,
            "query": query,
            "engine": engine,
            "search_method": search_method,
            "timestamp": time.time(),
            "results": []
        }

        if detailed_errors:
            response["detailed_errors"] = detailed_errors

        return response

    def _format_success_response(
        self,
        query: str,
        results: List[Dict[str, Any]],
        engine: str,
        search_method: str,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Format success response.

        Args:
            query: Original query
            results: Search results
            engine: Search engine used
            search_method: Search method used
            additional_data: Additional data to include in response

        Returns:
            Formatted success response
        """
        response = {
            "success": True,
            "query": query,
            "engine": engine,
            "search_method": search_method,
            "timestamp": time.time(),
            "results": results
        }

        if additional_data:
            response.update(additional_data)

        return response

    def search(self, query: str, num_results: int = 10, **kwargs) -> Dict[str, Any]:
        """
        Perform a web search.

        This method should be implemented by subclasses.

        Args:
            query: Query to search for
            num_results: Number of results to return
            **kwargs: Additional parameters for search

        Returns:
            Dictionary containing search results
        """
        raise NotImplementedError("Subclasses must implement search method")
