# Cải tiến cho WebSearchAgent

Tài liệu này mô tả các cải tiến đã thực hiện cho WebSearchAgent trong dự án Deep Research Core, bao gồm các sửa lỗi và tính năng mới.

## 1. Sửa lỗi kiểm tra sentiment trong underthesea

### Vấn đề
Phương thức `sentiment` trong module `VietnameseNLP` kiểm tra tính khả dụng của hàm `sentiment` trong thư viện `underthesea` không chính xác.

### Giải pháp
- Sử dụng `hasattr()` để kiểm tra đúng cách sự tồn tại của phương thức.
- Thêm nhật ký (log) khi phương thức không tồn tại.
- Sử dụng phương pháp dự phòng khi chức năng không khả dụng.

```python
def sentiment(self, text: str) -> Dict[str, Any]:
    """Phân tích cảm xúc văn bản tiếng Việt."""
    if not self.underthesea_available:
        return {"error": "Underthesea không khả dụng"}
        
    try:
        # Kiểm tra đúng cách phương thức sentiment có tồn tại không
        if not hasattr(underthesea, 'sentiment'):
            logger.warning("Underthesea không có phương thức sentiment, sử dụng phương pháp dự phòng")
            return self._fallback_sentiment(text)
        
        # Sử dụng sentiment từ underthesea nếu có
        result = underthesea.sentiment(text)
        # ...
```

## 2. Thêm phương thức _simple_similarity cho RedisWebSearchCache

### Vấn đề
Lớp `RedisWebSearchCache` sử dụng phương thức `_simple_similarity` để tìm kiếm các truy vấn tương tự trong cache, nhưng phương thức này chưa được triển khai.

### Giải pháp
Thêm phương thức `_simple_similarity` để tính toán độ tương đồng Jaccard giữa hai chuỗi văn bản:

```python
def _simple_similarity(self, text1: str, text2: str) -> float:
    """Tính độ tương đồng dựa trên phương pháp đơn giản."""
    # Tách từ
    words1 = set(text1.lower().split())
    words2 = set(text2.lower().split())
    
    # Kiểm tra tập hợp trống
    if not words1 or not words2:
        return 0.0
    
    # Tính toán tương đồng Jaccard
    intersection = len(words1.intersection(words2))
    union = len(words1.union(words2))
    
    return intersection / union if union > 0 else 0.0
```

## 3. Sửa lỗi trong ParallelWebSearchAgent

### Vấn đề
Lớp `ParallelWebSearchAgent` gọi phương thức `extract_content` mà không kiểm tra sự tồn tại của phương thức này trong `WebSearchAgent`.

### Giải pháp
Cập nhật phương thức `_safe_extract` để kiểm tra và sử dụng các phương thức thay thế nếu cần:

```python
def _safe_extract(self, url: str, extractor: Optional[str] = None) -> Dict[str, Any]:
    """Thực hiện trích xuất an toàn với xử lý lỗi."""
    try:
        # Tạo agent mới để tránh xung đột
        agent = WebSearchAgent()
        # Sao chép cấu hình từ agent gốc
        agent.config = self.base_agent.config.copy()
        
        # Kiểm tra phương thức trích xuất có tồn tại
        if hasattr(agent, 'extract_content'):
            # Sử dụng extract_content nếu có
            result = agent.extract_content(url, extractor=extractor)
        elif hasattr(agent, 'extract_url_content'):
            # Thử phương thức thay thế
            result = agent.extract_url_content(url, extractor_type=extractor)
        elif hasattr(agent, 'get_content'):
            # Thử phương thức khác
            result = agent.get_content(url, extractor_type=extractor)
        else:
            # Nếu không có phương thức trích xuất
            logger.error(f"Không tìm thấy phương thức trích xuất nội dung trong WebSearchAgent")
            raise AttributeError("WebSearchAgent không có phương thức trích xuất nội dung")
        
        # ... xử lý kết quả ...
```

## 4. Sửa lỗi tham số trong RedisWebSearchCache

### Vấn đề
Có sự không nhất quán trong việc sử dụng tham số `expiration_time` và `default_ttl` trong constructor của `RedisWebSearchCache`.

### Giải pháp
Cập nhật các test case để sử dụng đúng tham số `default_ttl`:

```python
self.cache = RedisWebSearchCache(
    redis_url="redis://localhost:6379/0",
    default_ttl=3600  # Sửa từ expiration_time thành default_ttl
)
```

## 5. Thêm Resource Monitor

### Mô tả
Hệ thống giám sát tài nguyên để theo dõi việc sử dụng CPU, bộ nhớ và đĩa, đảm bảo hiệu suất ổn định của WebSearchAgent.

### Tính năng chính
- Giám sát real-time CPU, memory, và disk usage
- Thiết lập các ngưỡng cảnh báo tùy chỉnh
- Hỗ trợ gửi thông báo khi vượt ngưỡng
- Tương thích với cả psutil và fallback method
- Lưu trữ lịch sử sử dụng tài nguyên

### Sử dụng
```python
from src.deep_research_core.agents.resource_monitor import resource_monitor

# Bắt đầu giám sát
resource_monitor.start_monitoring()

# Đăng ký đối tượng để nhận thông báo
class MyAgent:
    def handle_resource_alert(self, resource_type, current, threshold):
        print(f"Cảnh báo: {resource_type} đã vượt ngưỡng ({current:.1f}%)")

agent = MyAgent()
resource_monitor.register_monitor(agent)

# Lấy thông tin sử dụng hiện tại
usage = resource_monitor.get_resource_usage()
print(f"CPU: {usage['cpu']['percent']}%, Memory: {usage['memory']['percent']}%")

# Dừng giám sát
resource_monitor.stop_monitoring()
```

## 6. Thêm Retry và Circuit Breaker

### Mô tả
Các công cụ xử lý lỗi để tăng độ bền vững cho ứng dụng, bao gồm retry với backoff và circuit breaker để ngăn cascade failure.

### Tính năng chính
- RetryWithBackoff: Tự động thử lại các tác vụ khi gặp lỗi với thời gian chờ tăng dần
- CircuitBreaker: Ngăn hệ thống gọi liên tục các dịch vụ bị lỗi
- Theo dõi và ghi nhận lỗi chi tiết
- Hỗ trợ sử dụng như decorator

### Sử dụng
```python
from src.deep_research_core.agents.utils.retry_util import RetryWithBackoff, CircuitBreaker

# Sử dụng retry
@RetryWithBackoff(max_retries=3, base_delay=1.0)
def fetch_data(url):
    # Logic fetch data
    pass

# Sử dụng circuit breaker
google_breaker = CircuitBreaker(failure_threshold=5, reset_timeout=120)

@google_breaker
def call_google_api(query):
    # Logic call API
    pass

# Hoặc sử dụng các circuit breaker có sẵn
from src.deep_research_core.agents.utils.retry_util import google_api_breaker, openai_api_breaker

@openai_api_breaker
def generate_text(prompt):
    # Logic call OpenAI
    pass
```

## 7. Thêm Safe Parallel Executor

### Mô tả
Công cụ xử lý tác vụ song song an toàn, tránh memory leak và quản lý tài nguyên hiệu quả.

### Tính năng chính
- Hỗ trợ xử lý song song với multi-threading và asyncio
- Xử lý lô (batch) cho số lượng tác vụ lớn
- Tự động dọn dẹp tài nguyên để tránh memory leak
- Theo dõi hiệu suất và xử lý lỗi chi tiết
- Timeout tự động cho các tác vụ kéo dài

### Sử dụng
```python
from src.deep_research_core.agents.utils.parallel_util import SafeParallelExecutor

# Tạo executor
executor = SafeParallelExecutor(max_workers=5, timeout=30)

# Định nghĩa hàm cần thực thi
def process_url(url, extractor=None):
    # Logic xử lý URL
    return {"url": url, "content": "..."}

# Chuẩn bị tham số
urls = ["https://example.com", "https://example.org", "https://example.net"]
params = [(url, None) for url in urls]

# Thực thi song song
results = executor.execute_many(process_url, params)

# Xử lý kết quả
for result in results:
    if result.success:
        print(f"Success: {result.result['url']}")
    else:
        print(f"Error: {str(result.error)}")

# Xem thống kê
stats = executor.get_stats()
print(f"Success rate: {stats['success_rate']}%, Avg time: {stats['avg_execution_time']:.3f}s")
```

## Các cải tiến khác

### Test Coverage
Đã thêm các test case toàn diện cho tất cả các cải tiến:
- Test cho việc kiểm tra sentiment trong underthesea
- Test cho phương thức _simple_similarity
- Test cho ResourceMonitor
- Test cho RetryWithBackoff và CircuitBreaker
- Test cho SafeParallelExecutor

### Tài liệu
- Cập nhật docstring cho tất cả các phương thức và lớp mới
- Thêm hướng dẫn sử dụng chi tiết cho các tính năng mới
- Thêm ví dụ mã nguồn cho từng tính năng 