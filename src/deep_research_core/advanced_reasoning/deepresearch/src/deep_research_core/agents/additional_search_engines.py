"""
<PERSON><PERSON><PERSON> công cụ tìm kiếm bổ sung cho WebSearchAgent.

Module này cung cấp các công cụ tìm kiếm bổ sung cho WebSearchAgent,
bao gồ<PERSON>, Brave Search, và Yandex.
"""

import json
import time
import random
import logging
import requests
from typing import Dict, Any, List, Optional, Union
from bs4 import BeautifulSoup
from urllib.parse import quote_plus

# Thiết lập logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QwantSearchEngine:
    """
    Công cụ tìm kiếm <PERSON>want.
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        timeout: float = 10.0,
        user_agent: Optional[str] = None,
        **kwargs
    ):
        """
        Khởi tạo QwantSearchEngine.
        
        Args:
            api_key: API key (không bắt buộc)
            timeout: Thờ<PERSON> gian chờ (gi<PERSON>y)
            user_agent: User-Agent
            **kwargs: <PERSON><PERSON> số bổ sung
        """
        self.api_key = api_key
        self.timeout = timeout
        self.user_agent = user_agent or "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    
    def search(
        self,
        query: str,
        num_results: int = 10,
        language: str = "en",
        **kwargs
    ) -> Dict[str, Any]:
        """
        Thực hiện tìm kiếm với Qwant.
        
        Args:
            query: Truy vấn tìm kiếm
            num_results: Số lượng kết quả
            language: Ngôn ngữ
            **kwargs: Tham số bổ sung
            
        Returns:
            Dict với kết quả tìm kiếm
        """
        logger.info(f"Tìm kiếm Qwant với query: {query}")
        
        try:
            # Chuẩn bị URL
            encoded_query = quote_plus(query)
            url = f"https://api.qwant.com/v3/search/web?q={encoded_query}&count={num_results}&locale={language}"
            
            # Chuẩn bị headers
            headers = {
                "User-Agent": self.user_agent,
                "Accept": "application/json"
            }
            
            # Thêm API key nếu có
            if self.api_key:
                headers["X-Qwant-API-Key"] = self.api_key
            
            # Thực hiện yêu cầu
            response = requests.get(
                url,
                headers=headers,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            # Phân tích phản hồi
            data = response.json()
            
            # Kiểm tra lỗi
            if "data" not in data or "result" not in data["data"]:
                return {
                    "success": False,
                    "error": "Không thể phân tích phản hồi từ Qwant",
                    "query": query,
                    "engine": "qwant",
                    "results": []
                }
            
            # Trích xuất kết quả
            items = data["data"]["result"]["items"]
            results = []
            
            for item in items:
                if "title" in item and "url" in item:
                    results.append({
                        "title": item.get("title", ""),
                        "url": item.get("url", ""),
                        "snippet": item.get("description", ""),
                        "source": "qwant"
                    })
            
            return {
                "success": True,
                "query": query,
                "engine": "qwant",
                "results": results[:num_results]
            }
            
        except Exception as e:
            logger.error(f"Lỗi khi tìm kiếm Qwant: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "query": query,
                "engine": "qwant",
                "results": []
            }

class BraveSearchEngine:
    """
    Công cụ tìm kiếm Brave Search.
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        timeout: float = 10.0,
        user_agent: Optional[str] = None,
        **kwargs
    ):
        """
        Khởi tạo BraveSearchEngine.
        
        Args:
            api_key: API key (bắt buộc cho API, không bắt buộc cho HTML)
            timeout: Thời gian chờ (giây)
            user_agent: User-Agent
            **kwargs: Tham số bổ sung
        """
        self.api_key = api_key
        self.timeout = timeout
        self.user_agent = user_agent or "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    
    def search(
        self,
        query: str,
        num_results: int = 10,
        language: str = "en",
        **kwargs
    ) -> Dict[str, Any]:
        """
        Thực hiện tìm kiếm với Brave Search.
        
        Args:
            query: Truy vấn tìm kiếm
            num_results: Số lượng kết quả
            language: Ngôn ngữ
            **kwargs: Tham số bổ sung
            
        Returns:
            Dict với kết quả tìm kiếm
        """
        logger.info(f"Tìm kiếm Brave Search với query: {query}")
        
        # Nếu có API key, sử dụng API
        if self.api_key:
            return self._search_api(query, num_results, language, **kwargs)
        else:
            # Nếu không có API key, sử dụng HTML scraping
            return self._search_html(query, num_results, language, **kwargs)
    
    def _search_api(
        self,
        query: str,
        num_results: int = 10,
        language: str = "en",
        **kwargs
    ) -> Dict[str, Any]:
        """
        Thực hiện tìm kiếm với Brave Search API.
        
        Args:
            query: Truy vấn tìm kiếm
            num_results: Số lượng kết quả
            language: Ngôn ngữ
            **kwargs: Tham số bổ sung
            
        Returns:
            Dict với kết quả tìm kiếm
        """
        try:
            # Chuẩn bị URL
            url = "https://api.search.brave.com/res/v1/web/search"
            
            # Chuẩn bị params
            params = {
                "q": query,
                "count": num_results,
                "language": language
            }
            
            # Chuẩn bị headers
            headers = {
                "Accept": "application/json",
                "X-Subscription-Token": self.api_key
            }
            
            # Thực hiện yêu cầu
            response = requests.get(
                url,
                params=params,
                headers=headers,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            # Phân tích phản hồi
            data = response.json()
            
            # Kiểm tra lỗi
            if "web" not in data or "results" not in data["web"]:
                return {
                    "success": False,
                    "error": "Không thể phân tích phản hồi từ Brave Search API",
                    "query": query,
                    "engine": "brave",
                    "results": []
                }
            
            # Trích xuất kết quả
            items = data["web"]["results"]
            results = []
            
            for item in items:
                results.append({
                    "title": item.get("title", ""),
                    "url": item.get("url", ""),
                    "snippet": item.get("description", ""),
                    "source": "brave"
                })
            
            return {
                "success": True,
                "query": query,
                "engine": "brave",
                "results": results
            }
            
        except Exception as e:
            logger.error(f"Lỗi khi tìm kiếm Brave Search API: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "query": query,
                "engine": "brave",
                "results": []
            }
    
    def _search_html(
        self,
        query: str,
        num_results: int = 10,
        language: str = "en",
        **kwargs
    ) -> Dict[str, Any]:
        """
        Thực hiện tìm kiếm với Brave Search HTML.
        
        Args:
            query: Truy vấn tìm kiếm
            num_results: Số lượng kết quả
            language: Ngôn ngữ
            **kwargs: Tham số bổ sung
            
        Returns:
            Dict với kết quả tìm kiếm
        """
        try:
            # Chuẩn bị URL
            encoded_query = quote_plus(query)
            url = f"https://search.brave.com/search?q={encoded_query}&language={language}"
            
            # Chuẩn bị headers
            headers = {
                "User-Agent": self.user_agent,
                "Accept": "text/html"
            }
            
            # Thực hiện yêu cầu
            response = requests.get(
                url,
                headers=headers,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            # Phân tích HTML
            soup = BeautifulSoup(response.text, "html.parser")
            
            # Trích xuất kết quả
            results = []
            result_elements = soup.select(".snippet")
            
            for element in result_elements:
                title_element = element.select_one(".snippet-title")
                url_element = element.select_one(".result-header a")
                snippet_element = element.select_one(".snippet-description")
                
                if title_element and url_element:
                    title = title_element.get_text(strip=True)
                    url = url_element.get("href", "")
                    snippet = snippet_element.get_text(strip=True) if snippet_element else ""
                    
                    results.append({
                        "title": title,
                        "url": url,
                        "snippet": snippet,
                        "source": "brave"
                    })
                
                if len(results) >= num_results:
                    break
            
            return {
                "success": True,
                "query": query,
                "engine": "brave",
                "results": results
            }
            
        except Exception as e:
            logger.error(f"Lỗi khi tìm kiếm Brave Search HTML: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "query": query,
                "engine": "brave",
                "results": []
            }

class YandexSearchEngine:
    """
    Công cụ tìm kiếm Yandex.
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        timeout: float = 10.0,
        user_agent: Optional[str] = None,
        **kwargs
    ):
        """
        Khởi tạo YandexSearchEngine.
        
        Args:
            api_key: API key (không bắt buộc)
            timeout: Thời gian chờ (giây)
            user_agent: User-Agent
            **kwargs: Tham số bổ sung
        """
        self.api_key = api_key
        self.timeout = timeout
        self.user_agent = user_agent or "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    
    def search(
        self,
        query: str,
        num_results: int = 10,
        language: str = "en",
        **kwargs
    ) -> Dict[str, Any]:
        """
        Thực hiện tìm kiếm với Yandex.
        
        Args:
            query: Truy vấn tìm kiếm
            num_results: Số lượng kết quả
            language: Ngôn ngữ
            **kwargs: Tham số bổ sung
            
        Returns:
            Dict với kết quả tìm kiếm
        """
        logger.info(f"Tìm kiếm Yandex với query: {query}")
        
        try:
            # Chuẩn bị URL
            encoded_query = quote_plus(query)
            url = f"https://yandex.com/search/?text={encoded_query}&lang={language}"
            
            # Chuẩn bị headers
            headers = {
                "User-Agent": self.user_agent,
                "Accept": "text/html"
            }
            
            # Thực hiện yêu cầu
            response = requests.get(
                url,
                headers=headers,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            # Phân tích HTML
            soup = BeautifulSoup(response.text, "html.parser")
            
            # Trích xuất kết quả
            results = []
            result_elements = soup.select(".serp-item")
            
            for element in result_elements:
                title_element = element.select_one(".OrganicTitle-Link")
                url_element = element.select_one(".Path-Item")
                snippet_element = element.select_one(".OrganicText")
                
                if title_element:
                    title = title_element.get_text(strip=True)
                    url = title_element.get("href", "")
                    
                    # Lấy URL đầy đủ nếu là URL tương đối
                    if url.startswith("/"):
                        url = f"https://yandex.com{url}"
                    
                    snippet = snippet_element.get_text(strip=True) if snippet_element else ""
                    
                    results.append({
                        "title": title,
                        "url": url,
                        "snippet": snippet,
                        "source": "yandex"
                    })
                
                if len(results) >= num_results:
                    break
            
            return {
                "success": True,
                "query": query,
                "engine": "yandex",
                "results": results
            }
            
        except Exception as e:
            logger.error(f"Lỗi khi tìm kiếm Yandex: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "query": query,
                "engine": "yandex",
                "results": []
            }
