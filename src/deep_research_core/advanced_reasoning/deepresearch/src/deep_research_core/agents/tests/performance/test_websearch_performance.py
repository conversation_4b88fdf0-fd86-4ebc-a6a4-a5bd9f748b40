"""
Benchmark hiệu suất cho WebSearchAgent.

Mo<PERSON>le này chứa các benchmark hiệu suất cho các thành phần WebSearchAgent,
bao gồm tìm kiếm, cache, tối ưu bộ nhớ, và xử lý tiếng Việt.
"""

import unittest
import time
import sys
import os
import gc
import tempfile
import statistics
from typing import Dict, Any, List, Tuple

from deep_research_core.agents.web_search_agent import WebSearchAgent
from deep_research_core.agents.batch_search_optimizer import BatchSearchOptimizer
from deep_research_core.agents.caching import SmartWebSearchCache, RedisWebSearchCache
from deep_research_core.agents.vietnamese_nlp import VietnameseNLP


class PerformanceBenchmark(unittest.TestCase):
    """
    Benchmark hiệu suất cho WebSearchAgent.
    
    Lưu ý: Các benchmark này có thể mất thời gian. Đặt biến môi trường:
    RUN_PERFORMANCE_TESTS=1 để chạy benchmark.
    """
    
    @classmethod
    def setUpClass(cls):
        """Cài đặt cho tất cả các benchmark."""
        # Kiểm tra biến môi trường để xác định có chạy benchmark không
        cls.run_performance_tests = os.environ.get("RUN_PERFORMANCE_TESTS", "0") == "1"
        
        if not cls.run_performance_tests:
            return
            
        # Tạo thư mục cache tạm thời
        cls.temp_dir = tempfile.mkdtemp(prefix="websearch_benchmark_")
        
        # Cấu hình cho benchmark
        cls.config = {
            "cache_dir": cls.temp_dir,
            "cache": {
                "max_size": 1000,
                "default_ttl": 3600,
                "similarity_threshold": 0.8
            },
            "memory_management": {
                "enable_optimization": True,
                "max_cache_memory_percent": 75,
                "result_limit_size": 1024 * 100,
                "cleanup_interval": 60
            }
        }
        
        # Khởi tạo agent
        cls.agent = WebSearchAgent(config=cls.config)
        
        # Khởi tạo batch optimizer
        cls.batch_optimizer = BatchSearchOptimizer(
            search_agent=cls.agent,
            num_workers=3
        )
        
        # Khởi tạo NLP tiếng Việt
        cls.vi_nlp = VietnameseNLP()
        
        # Khởi tạo cache độc lập
        cls.cache = SmartWebSearchCache(
            cache_dir=os.path.join(cls.temp_dir, "smart_cache"),
            max_size=1000
        )
        
        # Chuẩn bị dữ liệu mẫu
        cls.search_queries = [
            "artificial intelligence",
            "machine learning algorithms",
            "neural networks",
            "deep learning",
            "computer vision",
            "natural language processing",
            "big data analytics",
            "cloud computing",
            "internet of things",
            "blockchain technology"
        ]
        
        cls.vietnamese_queries = [
            "trí tuệ nhân tạo",
            "học máy",
            "mạng nơ-ron",
            "học sâu",
            "thị giác máy tính",
            "xử lý ngôn ngữ tự nhiên",
            "phân tích dữ liệu lớn",
            "điện toán đám mây",
            "internet vạn vật",
            "công nghệ blockchain"
        ]
    
    def setUp(self):
        """Cài đặt trước mỗi benchmark."""
        if not self.run_performance_tests:
            self.skipTest("Bỏ qua benchmark hiệu suất, đặt RUN_PERFORMANCE_TESTS=1 để chạy")
        
        # Gọi garbage collector để giảm nhiễu
        gc.collect()
    
    def measure_execution_time(self, func, *args, **kwargs) -> Tuple[float, Any]:
        """
        Đo thời gian thực thi hàm.
        
        Args:
            func: Hàm cần đo
            *args, **kwargs: Tham số cho hàm
            
        Returns:
            Tuple (thời gian thực thi, kết quả)
        """
        start_time = time.time()
        result = func(*args, **kwargs)
        elapsed = time.time() - start_time
        return elapsed, result
    
    def benchmark_operation(self, operation_name: str, func, iterations: int = 3, *args, **kwargs) -> Dict[str, Any]:
        """
        Thực hiện benchmark một hàm với nhiều lần lặp lại.
        
        Args:
            operation_name: Tên của hoạt động
            func: Hàm cần benchmark
            iterations: Số lần lặp lại
            *args, **kwargs: Tham số cho hàm
            
        Returns:
            Dictionary chứa thống kê benchmark
        """
        times = []
        results = []
        
        print(f"\nBenchmarking {operation_name}...")
        for i in range(iterations):
            print(f"  Iteration {i+1}/{iterations}...")
            # Gọi GC trước mỗi lần đo
            gc.collect()
            time.sleep(0.5)  # Đợi hệ thống ổn định
            
            try:
                elapsed, result = self.measure_execution_time(func, *args, **kwargs)
                times.append(elapsed)
                results.append(result)
                print(f"  - Completed in {elapsed:.3f}s")
            except Exception as e:
                print(f"  - Failed: {str(e)}")
        
        # Tính toán thống kê
        if times:
            avg_time = statistics.mean(times)
            min_time = min(times)
            max_time = max(times)
            median_time = statistics.median(times)
            try:
                std_dev = statistics.stdev(times) if len(times) > 1 else 0
            except:
                std_dev = 0
                
            print(f"Results for {operation_name}:")
            print(f"  Average: {avg_time:.3f}s")
            print(f"  Min/Max: {min_time:.3f}s / {max_time:.3f}s")
            print(f"  Median: {median_time:.3f}s")
            print(f"  Std Dev: {std_dev:.3f}s")
            
            return {
                "operation": operation_name,
                "iterations": iterations,
                "avg_time": avg_time,
                "min_time": min_time,
                "max_time": max_time,
                "median_time": median_time,
                "std_dev": std_dev,
                "success": True
            }
        else:
            return {
                "operation": operation_name,
                "iterations": iterations,
                "success": False,
                "error": "No successful measurements"
            }
    
    def test_search_performance(self):
        """Benchmark hiệu suất tìm kiếm."""
        result = self.benchmark_operation(
            "Basic Search",
            self.agent.search,
            iterations=2,
            query="python programming",
            num_results=5
        )
        
        self.assertTrue(result["success"])
        print(f"Average search time: {result['avg_time']:.3f}s")
    
    def test_cache_performance(self):
        """Benchmark hiệu suất cache."""
        # 1. Điền cache với một số dữ liệu
        for i in range(50):
            key = f"test_key_{i}"
            value = {
                "data": f"test_value_{i}",
                "timestamp": time.time(),
                "query": f"test query {i}"
            }
            self.cache.set(key, value)
        
        # 2. Benchmark lấy từ cache
        result = self.benchmark_operation(
            "Cache Retrieval",
            lambda: [self.cache.get(f"test_key_{i}") for i in range(20)],
            iterations=5
        )
        
        self.assertTrue(result["success"])
        print(f"Average cache retrieval time: {result['avg_time']:.3f}s")
        
        # 3. Benchmark truy vấn tương tự
        result = self.benchmark_operation(
            "Similar Query Cache",
            lambda: [self.cache.get(f"test_key_{i}_variant", allow_similar=True) for i in range(20)],
            iterations=3
        )
        
        self.assertTrue(result["success"])
        print(f"Average similar query retrieval time: {result['avg_time']:.3f}s")
    
    def test_batch_search_performance(self):
        """Benchmark hiệu suất tìm kiếm hàng loạt."""
        # Chuẩn bị các tác vụ
        tasks = []
        for query in self.search_queries[:5]:  # Sử dụng 5 truy vấn để giảm thời gian chạy
            tasks.append({
                "query": query,
                "num_results": 3,
                "priority": 1
            })
        
        # Benchmark thực hiện tìm kiếm hàng loạt
        self.batch_optimizer.start()
        
        result = self.benchmark_operation(
            "Batch Search",
            lambda: self._process_batch_search(tasks),
            iterations=1  # Chỉ chạy một lần vì mất nhiều thời gian
        )
        
        self.batch_optimizer.stop()
        
        self.assertTrue(result["success"])
        print(f"Batch search time: {result['avg_time']:.3f}s")
    
    def _process_batch_search(self, tasks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Thực hiện tìm kiếm hàng loạt và chờ kết quả.
        
        Args:
            tasks: Danh sách các tác vụ tìm kiếm
            
        Returns:
            Kết quả tìm kiếm
        """
        # Thêm tác vụ
        task_ids = self.batch_optimizer.add_tasks(tasks)
        
        # Chờ kết quả (tối đa 60 giây)
        max_wait = 60
        start_time = time.time()
        results = {}
        
        while time.time() - start_time < max_wait:
            # Kiểm tra kết quả
            all_complete = True
            for task_id in task_ids:
                result = self.batch_optimizer.get_result(task_id)
                if result:
                    results[task_id] = result
                else:
                    all_complete = False
            
            if all_complete:
                break
                
            time.sleep(0.5)
        
        # Trả về thống kê
        stats = self.batch_optimizer.get_stats()
        stats["results_count"] = len(results)
        
        return stats
    
    def test_memory_optimization_performance(self):
        """Benchmark hiệu suất tối ưu hóa bộ nhớ."""
        # Chuẩn bị dữ liệu lớn để buộc tối ưu hóa bộ nhớ
        large_data = {}
        for i in range(100):
            large_data[f"key_{i}"] = "x" * 10000  # 10KB mỗi mục
            
        # Benchmark thời gian dọn dẹp bộ nhớ
        result = self.benchmark_operation(
            "Memory Cleanup",
            self.agent._perform_memory_cleanup,
            iterations=3
        )
        
        self.assertTrue(result["success"])
        print(f"Average memory cleanup time: {result['avg_time']:.3f}s")
        
        # Benchmark tối ưu hóa kết quả trước khi lưu cache
        mock_result = {
            "query": "test query",
            "results": [{"title": "Test", "snippet": "x" * 5000, "html": "y" * 10000} for _ in range(10)],
            "metadata": {"large_data": large_data},
            "html_content": "z" * 50000
        }
        
        result = self.benchmark_operation(
            "Result Optimization",
            self.agent._optimize_result_for_cache,
            iterations=5,
            result=mock_result
        )
        
        self.assertTrue(result["success"])
        print(f"Average result optimization time: {result['avg_time']:.3f}s")
    
    def test_vietnamese_nlp_performance(self):
        """Benchmark hiệu suất xử lý tiếng Việt."""
        if not self.agent.vietnamese_support:
            self.skipTest("Bỏ qua benchmark tiếng Việt, underthesea không khả dụng")
            
        # Mẫu văn bản tiếng Việt
        text = """
        Trí tuệ nhân tạo (AI) là một lĩnh vực của khoa học máy tính tập trung vào việc tạo ra các hệ thống 
        thông minh có khả năng thực hiện các nhiệm vụ thường đòi hỏi trí thông minh của con người. 
        Học máy là một nhánh của AI cho phép máy tính học từ dữ liệu và cải thiện hiệu suất mà không cần 
        được lập trình rõ ràng. Xử lý ngôn ngữ tự nhiên là lĩnh vực AI tập trung vào tương tác giữa máy tính 
        và ngôn ngữ tự nhiên của con người.
        """
        
        # 1. Benchmark tách từ
        result = self.benchmark_operation(
            "Word Tokenization",
            self.vi_nlp.word_tokenize,
            iterations=5,
            text=text
        )
        
        self.assertTrue(result["success"])
        print(f"Average tokenization time: {result['avg_time']:.3f}s")
        
        # 2. Benchmark trích xuất từ khóa
        result = self.benchmark_operation(
            "Keyword Extraction",
            self.vi_nlp.extract_keywords,
            iterations=5,
            text=text,
            max_keywords=10
        )
        
        self.assertTrue(result["success"])
        print(f"Average keyword extraction time: {result['avg_time']:.3f}s")
        
        # 3. Benchmark tóm tắt văn bản
        result = self.benchmark_operation(
            "Text Summarization",
            self.vi_nlp.summarize,
            iterations=3,
            text=text,
            max_sentences=2
        )
        
        self.assertTrue(result["success"])
        print(f"Average summarization time: {result['avg_time']:.3f}s")
    
    @classmethod
    def tearDownClass(cls):
        """Dọn dẹp sau tất cả các benchmark."""
        if not cls.run_performance_tests:
            return
            
        # Dừng BatchSearchOptimizer nếu đang chạy
        if hasattr(cls, 'batch_optimizer'):
            cls.batch_optimizer.stop()
            
        # Xóa thư mục tạm
        import shutil
        if hasattr(cls, 'temp_dir') and os.path.exists(cls.temp_dir):
            shutil.rmtree(cls.temp_dir)


if __name__ == "__main__":
    unittest.main() 