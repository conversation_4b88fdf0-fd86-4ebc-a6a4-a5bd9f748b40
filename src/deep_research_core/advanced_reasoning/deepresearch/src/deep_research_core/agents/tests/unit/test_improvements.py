"""
Unit tests cho các cải tiến mới trong WebSearchAgent.

Mo<PERSON>le này kiểm tra các cải tiến của WebSearchAgent bao gồm:
- VietnameseNLPLite
- RedisWebSearchCache với _simple_similarity
- ParallelWebSearchAgent
- PerformanceTracker
"""

import unittest
import time
import os
import tempfile
from unittest.mock import patch, MagicMock

from deep_research_core.agents.lightweight.vietnamese_nlp_lite import vietnamese_nlp_lite
from deep_research_core.agents.caching import RedisWebSearchCache
from deep_research_core.agents.parallel_search import parallel_search_agent, ParallelWebSearchAgent
from deep_research_core.agents.performance_tracker import performance_tracker, PerformanceTracker


class TestVietnameseNLPLite(unittest.TestCase):
    """Test cho VietnameseNLPLite."""
    
    def test_word_tokenize(self):
        """Test tách từ đơn giản."""
        text = "<PERSON>à Nội là thủ đô của Việt Nam"
        tokens = vietnamese_nlp_lite.word_tokenize(text)
        
        self.assertIsInstance(tokens, list)
        self.assertTrue(len(tokens) > 0)
        self.assertIn("<PERSON>à Nội", tokens)  # Kiểm tra token ghép
    
    def test_sentiment(self):
        """Test phân tích cảm xúc."""
        # Test văn bản tích cực
        positive_text = "Sản phẩm này rất tốt và đáng giá"
        pos_result = vietnamese_nlp_lite.sentiment(positive_text)
        
        self.assertIsInstance(pos_result, dict)
        self.assertEqual(pos_result["label"], "positive")
        self.assertGreater(pos_result["score"], 0.5)
        
        # Test văn bản tiêu cực
        negative_text = "Dịch vụ quá tệ, nhân viên thô lỗ"
        neg_result = vietnamese_nlp_lite.sentiment(negative_text)
        
        self.assertIsInstance(neg_result, dict)
        self.assertEqual(neg_result["label"], "negative")
        self.assertLess(neg_result["score"], 0.5)
    
    def test_summarize(self):
        """Test tóm tắt văn bản."""
        long_text = """
        Hà Nội là thủ đô của Việt Nam, một trong những thành phố lâu đời nhất châu Á với lịch sử hơn 1000 năm.
        Thành phố nằm bên bờ sông Hồng với nhiều hồ đẹp và không gian xanh.
        Hà Nội có 30 quận, huyện với dân số khoảng 8 triệu người.
        Hà Nội là trung tâm chính trị, văn hóa và giáo dục của cả nước với nhiều di tích lịch sử quan trọng.
        Phố cổ Hà Nội là điểm du lịch nổi tiếng với 36 phố phường buôn bán các mặt hàng truyền thống.
        Hà Nội có nền ẩm thực phong phú với những món ăn nổi tiếng như phở, bún chả, chả cá Lã Vọng.
        """
        
        summary = vietnamese_nlp_lite.summarize(long_text, max_sentences=2)
        
        self.assertIsInstance(summary, str)
        self.assertTrue(len(summary) < len(long_text))
        # Kiểm tra số câu (đơn giản)
        self.assertLessEqual(len(summary.split('.')), 3)  # 2 câu + có thể có 1 phần tử rỗng
    
    def test_extract_keywords(self):
        """Test trích xuất từ khóa."""
        text = "Hà Nội là thủ đô của Việt Nam với nhiều di tích lịch sử và văn hóa đặc sắc."
        keywords = vietnamese_nlp_lite.extract_keywords(text, max_keywords=3)
        
        self.assertIsInstance(keywords, list)
        self.assertLessEqual(len(keywords), 3)
        # Kiểm tra cấu trúc keyword
        for keyword in keywords:
            self.assertIn("word", keyword)
            self.assertIn("score", keyword)
    
    def test_text_similarity(self):
        """Test tính độ tương đồng văn bản."""
        text1 = "Hà Nội là thủ đô của Việt Nam"
        text2 = "Thủ đô Việt Nam là Hà Nội"
        text3 = "Thành phố Hồ Chí Minh là thành phố lớn nhất Việt Nam"
        
        # Kiểm tra độ tương đồng giữa text1 và text2 (cao)
        sim1 = vietnamese_nlp_lite.text_similarity(text1, text2)
        # Kiểm tra độ tương đồng giữa text1 và text3 (thấp)
        sim2 = vietnamese_nlp_lite.text_similarity(text1, text3)
        
        self.assertGreater(sim1, 0.5)  # Độ tương đồng cao
        self.assertLess(sim2, sim1)    # Độ tương đồng thấp hơn


class TestRedisWebSearchCache(unittest.TestCase):
    """Test cho RedisWebSearchCache với phương thức _simple_similarity."""

    @patch('redis.Redis')
    def setUp(self, mock_redis):
        """Thiết lập test."""
        # Tạo mock redis client
        self.mock_redis = mock_redis.return_value
        self.mock_redis.get.return_value = None
        self.mock_redis.set.return_value = True
        
        self.cache = RedisWebSearchCache(
            redis_url="redis://localhost:6379/0",
            default_ttl=3600
        )
    
    def test_simple_similarity(self):
        """Test phương thức _simple_similarity."""
        # Test với hai chuỗi tương tự
        text1 = "Hà Nội là thủ đô của Việt Nam"
        text2 = "Thủ đô Việt Nam là Hà Nội"
        similarity1 = self.cache._simple_similarity(text1, text2)
        
        # Test với hai chuỗi khác nhau
        text3 = "Sài Gòn là thành phố lớn nhất Việt Nam"
        similarity2 = self.cache._simple_similarity(text1, text3)
        
        # Test với chuỗi rỗng
        similarity3 = self.cache._simple_similarity(text1, "")
        similarity4 = self.cache._simple_similarity("", "")
        
        # Kiểm tra kết quả
        self.assertGreater(similarity1, 0)
        self.assertLessEqual(similarity1, 1.0)
        self.assertLess(similarity2, similarity1)  # Độ tương đồng thấp hơn
        self.assertEqual(similarity3, 0.0)  # Chuỗi rỗng trả về 0
        self.assertEqual(similarity4, 0.0)  # Hai chuỗi rỗng trả về 0


class TestParallelWebSearchAgent(unittest.TestCase):
    """Test cho ParallelWebSearchAgent."""
    
    def setUp(self):
        """Thiết lập test."""
        # Tạo mock WebSearchAgent
        self.mock_agent = MagicMock()
        self.mock_agent.search.return_value = [{"title": "Test Result", "url": "https://example.com"}]
        self.mock_agent.extract_content.return_value = "Test content"
        self.mock_agent.config = {}
        
        # Tạo ParallelWebSearchAgent với mock agent
        self.parallel_agent = ParallelWebSearchAgent(
            base_agent=self.mock_agent,
            max_workers=2,
            timeout=5
        )
    
    @patch('deep_research_core.agents.parallel_search.WebSearchAgent')
    def test_search_many(self, mock_websearch_class):
        """Test tìm kiếm song song."""
        # Thiết lập mock
        mock_instance = MagicMock()
        mock_instance.search.return_value = [{"title": "Test Result", "url": "https://example.com"}]
        mock_instance.config = {}
        mock_websearch_class.return_value = mock_instance
        
        # Thực hiện tìm kiếm song song
        queries = ["test query 1", "test query 2"]
        results = self.parallel_agent.search_many(queries)
        
        # Kiểm tra kết quả
        self.assertEqual(len(results), 2)
        for result in results:
            self.assertIn("query", result)
            self.assertIn("results", result)
            self.assertTrue(result["success"])
    
    @patch('deep_research_core.agents.parallel_search.WebSearchAgent')
    def test_extract_many(self, mock_websearch_class):
        """Test trích xuất nội dung song song."""
        # Thiết lập mock
        mock_instance = MagicMock()
        mock_instance.extract_content.return_value = "Test content"
        mock_instance.config = {}
        mock_websearch_class.return_value = mock_instance
        
        # Thực hiện trích xuất song song
        urls = ["https://example.com/1", "https://example.com/2"]
        results = self.parallel_agent.extract_many(urls)
        
        # Kiểm tra kết quả
        self.assertEqual(len(results), 2)
        for result in results:
            self.assertIn("url", result)
            self.assertIn("content", result)
            self.assertTrue(result["success"])
    
    def test_analyze_results(self):
        """Test phân tích kết quả."""
        # Tạo dữ liệu test
        results = [
            {"query": "q1", "results": [1, 2, 3], "success": True},
            {"query": "q2", "results": [4, 5], "success": True},
            {"query": "q3", "results": [], "success": False, "error": "Test error"}
        ]
        
        # Phân tích kết quả
        analysis = self.parallel_agent.analyze_results(results)
        
        # Kiểm tra kết quả
        self.assertIn("success_rate", analysis)
        self.assertIn("total_results", analysis)
        self.assertEqual(analysis["queries"], 3)
        self.assertEqual(analysis["successful_queries"], 2)
        self.assertEqual(analysis["failed_queries"], 1)
        self.assertEqual(analysis["total_results"], 5)


class TestPerformanceTracker(unittest.TestCase):
    """Test cho PerformanceTracker."""
    
    def setUp(self):
        """Thiết lập test."""
        # Tạo thư mục tạm cho test
        self.test_dir = tempfile.mkdtemp()
        self.csv_dir = os.path.join(self.test_dir, "perf_csv")
        self.db_file = os.path.join(self.test_dir, "perf_test.db")
        
        # Tạo tracker cho test
        self.memory_tracker = PerformanceTracker(
            storage_type="memory",
            auto_save=False
        )
        
        # Tạo tracker với CSV
        os.makedirs(self.csv_dir, exist_ok=True)
        self.csv_tracker = PerformanceTracker(
            storage_type="csv",
            storage_path=self.csv_dir,
            auto_save=False
        )
    
    def tearDown(self):
        """Dọn dẹp sau test."""
        # Đóng trackers
        self.memory_tracker.close()
        self.csv_tracker.close()
    
    @patch('sqlite3.connect')
    def test_sqlite_init(self, mock_connect):
        """Test khởi tạo sqlite."""
        # Thiết lập mock
        mock_conn = MagicMock()
        mock_cursor = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mock_connect.return_value = mock_conn
        
        # Tạo tracker với SQLite
        tracker = PerformanceTracker(
            storage_type="sqlite",
            storage_path=self.db_file,
            auto_save=False
        )
        
        # Kiểm tra kết quả
        mock_connect.assert_called_once_with(self.db_file, check_same_thread=False)
        self.assertEqual(mock_cursor.execute.call_count, 4)  # 4 bảng được tạo
        
        # Đóng tracker
        tracker.close()
    
    def test_track_search(self):
        """Test theo dõi tìm kiếm."""
        # Thực hiện track
        self.memory_tracker.track_search(
            query="test query",
            engine="google",
            execution_time=0.5,
            result_count=10,
            cache_hit=False,
            memory_usage=100.0,
            language="vi"
        )
        
        # Kiểm tra kết quả
        self.assertEqual(len(self.memory_tracker.metrics["search"]), 1)
        self.assertEqual(self.memory_tracker.session_metrics["search_count"], 1)
        self.assertEqual(self.memory_tracker.session_metrics["cache_miss_count"], 1)
        
        # Track thêm lần nữa với cache hit
        self.memory_tracker.track_search(
            query="test query 2",
            engine="bing",
            execution_time=0.3,
            result_count=5,
            cache_hit=True,
            memory_usage=110.0,
            language="en"
        )
        
        # Kiểm tra kết quả
        self.assertEqual(len(self.memory_tracker.metrics["search"]), 2)
        self.assertEqual(self.memory_tracker.session_metrics["search_count"], 2)
        self.assertEqual(self.memory_tracker.session_metrics["cache_hit_count"], 1)
    
    def test_track_cache_metrics(self):
        """Test theo dõi cache metrics."""
        # Thực hiện track
        self.memory_tracker.track_cache_metrics(
            cache_size=100,
            hit_count=80,
            miss_count=20,
            memory_used=200.0
        )
        
        # Kiểm tra kết quả
        self.assertEqual(len(self.memory_tracker.metrics["cache_hit"]), 1)
        cache_entry = self.memory_tracker.metrics["cache_hit"][0]
        self.assertEqual(cache_entry["cache_size"], 100)
        self.assertEqual(cache_entry["hit_count"], 80)
        self.assertEqual(cache_entry["miss_count"], 20)
        self.assertEqual(cache_entry["hit_ratio"], 0.8)
    
    def test_get_performance_summary(self):
        """Test lấy tóm tắt hiệu suất."""
        # Thêm dữ liệu test
        for i in range(5):
            self.memory_tracker.track_search(
                query=f"test query {i}",
                engine="google",
                execution_time=0.5 + i*0.1,
                result_count=10 - i,
                cache_hit=i % 2 == 0,
                memory_usage=100.0 + i*10,
                language="vi"
            )
        
        # Lấy tóm tắt
        summary = self.memory_tracker.get_performance_summary()
        
        # Kiểm tra kết quả
        self.assertEqual(summary["search_count"], 5)
        self.assertEqual(summary["cache_hits"], 3)  # i=0,2,4
        self.assertEqual(summary["cache_misses"], 2)  # i=1,3
        self.assertGreater(summary["avg_search_time"], 0)


if __name__ == "__main__":
    unittest.main() 