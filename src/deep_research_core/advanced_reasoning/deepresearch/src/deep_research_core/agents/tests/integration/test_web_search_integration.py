"""
<PERSON><PERSON><PERSON> thử tích hợp cho WebSearchAgent.

<PERSON><PERSON><PERSON> này chứa các bài kiểm tra tích hợp với dữ liệu thực cho WebSearchAgent,
kiểm tra khả năng tìm kiếm, crawl và trích xuất nội dung thực tế.
"""

import unittest
import time
import os
from typing import Dict, Any, List

from deep_research_core.agents.web_search_agent import WebSearchAgent
from deep_research_core.agents.vietnamese_nlp import VietnameseNLP, VietnameseDomainModel


class TestWebSearchIntegration(unittest.TestCase):
    """<PERSON><PERSON><PERSON> thử tích hợp cho WebSearchAgent với dữ liệu thực."""
    
    @classmethod
    def setUpClass(cls):
        """Cài đặt cho tất cả các bài kiểm tra."""
        # Kiểm tra biến môi trường để xác định có chạy kiể<PERSON> thử tích hợp không
        cls.run_integration_tests = os.environ.get("RUN_INTEGRATION_TESTS", "0") == "1"
        
        if not cls.run_integration_tests:
            return
            
        # Cấu hình cho các kiểm thử tích hợp
        cls.config = {
            "cache_dir": "./.test_cache",
            "cache": {
                "max_size": 100,
                "default_ttl": 3600,  # 1 giờ
                "similarity_threshold": 0.8
            },
            "memory_management": {
                "enable_optimization": True,
                "max_cache_memory_percent": 75,
                "result_limit_size": 1024 * 50,  # 50KB
                "cleanup_interval": 30
            },
            "vietnamese_rerank": True
        }
        
        # Khởi tạo agent
        cls.agent = WebSearchAgent(config=cls.config)
        
        # Khởi tạo NLP tiếng Việt
        cls.vi_nlp = VietnameseNLP()
        
        # Ngủ để đợi khởi tạo hoàn tất
        time.sleep(1)
    
    def setUp(self):
        """Cài đặt trước mỗi bài kiểm tra."""
        if not self.run_integration_tests:
            self.skipTest("Bỏ qua kiểm thử tích hợp, đặt RUN_INTEGRATION_TESTS=1 để chạy")
    
    def test_basic_search(self):
        """Kiểm tra tìm kiếm cơ bản với dữ liệu thực."""
        result = self.agent.search("python programming language", num_results=3)
        
        # Kiểm tra kết quả
        self.assertTrue(result.get("success", False))
        self.assertIn("results", result)
        self.assertGreaterEqual(len(result["results"]), 1)
        
        # Kiểm tra nội dung kết quả
        first_result = result["results"][0]
        self.assertIn("title", first_result)
        self.assertIn("url", first_result)
        self.assertIn("snippet", first_result)
        
        # Kiểm tra thông tin thống kê
        self.assertIn("elapsed_time", result)
        self.assertLess(result["elapsed_time"], 10)  # Dưới 10 giây
    
    def test_vietnamese_search(self):
        """Kiểm tra tìm kiếm tiếng Việt với dữ liệu thực."""
        # Bỏ qua nếu không hỗ trợ tiếng Việt
        if not self.agent.vietnamese_support:
            self.skipTest("Bỏ qua kiểm thử tiếng Việt, underthesea không khả dụng")
            
        result = self.agent.search("ngôn ngữ lập trình Python", language="vi", num_results=3)
        
        # Kiểm tra kết quả
        self.assertTrue(result.get("success", False))
        self.assertIn("results", result)
        self.assertGreaterEqual(len(result["results"]), 1)
        
        # Kiểm tra tăng cường tiếng Việt
        self.assertIn("metadata", result)
        self.assertIn("vietnamese_query_analysis", result["metadata"])
        
        # Kiểm tra điểm liên quan
        self.assertIn("relevance_score", result["results"][0])
    
    def test_document_extraction(self):
        """Kiểm tra trích xuất nội dung tài liệu với dữ liệu thực."""
        # Sử dụng một URL PDF công khai
        pdf_url = "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"
        
        result = self.agent.extract_document_content(pdf_url)
        
        # Kiểm tra kết quả
        self.assertTrue(result.get("success", False))
        self.assertIn("text", result)
        self.assertGreater(len(result["text"]), 0)
        self.assertIn("content_type", result)
        self.assertIn("pdf", result["content_type"].lower())
    
    def test_text_similarity(self):
        """Kiểm tra độ tương đồng văn bản tiếng Việt."""
        if not self.agent.vietnamese_support:
            self.skipTest("Bỏ qua kiểm thử tiếng Việt, underthesea không khả dụng")
            
        text1 = "Python là ngôn ngữ lập trình bậc cao, dễ đọc và dễ học."
        text2 = "Python đơn giản, dễ học và là một ngôn ngữ lập trình cấp cao."
        text3 = "Java là một ngôn ngữ lập trình hướng đối tượng."
        
        # Tính độ tương đồng
        sim1_2 = self.vi_nlp.text_similarity(text1, text2)
        sim1_3 = self.vi_nlp.text_similarity(text1, text3)
        
        # Kiểm tra kết quả
        self.assertGreater(sim1_2, 0.5)  # Tương đồng cao
        self.assertLess(sim1_3, 0.5)     # Tương đồng thấp
        self.assertGreater(sim1_2, sim1_3)  # text1 và text2 tương đồng hơn text1 và text3
    
    def test_cache_with_similar_queries(self):
        """Kiểm tra cache với các truy vấn tương tự."""
        # Thực hiện truy vấn đầu tiên
        query1 = "artificial intelligence applications"
        result1 = self.agent.search(query1, num_results=2)
        
        # Thực hiện truy vấn tương tự
        query2 = "applications of artificial intelligence"
        result2 = self.agent.search(query2, num_results=2)
        
        # Kiểm tra kết quả từ cache
        self.assertTrue(result2.get("success", False))
        self.assertTrue(result2.get("from_similar_query", False), 
                      "Kết quả phải lấy từ truy vấn tương tự trong cache")
        self.assertEqual(result2.get("original_query"), query2)
    
    def test_domain_specific_model(self):
        """Kiểm tra mô hình đặc thù cho domain."""
        # Bỏ qua nếu không thể tải mô hình
        try:
            legal_model = VietnameseDomainModel(domain="legal")
            if not legal_model.initialized:
                self.skipTest("Bỏ qua kiểm thử domain model, không thể khởi tạo")
                
            # Kiểm tra từ vựng đặc thù domain
            legal_def = legal_model.get_domain_definition("luật")
            tech_model = VietnameseDomainModel(domain="tech")
            tech_def = tech_model.get_domain_definition("thuật toán")
            
            self.assertIsNotNone(legal_def)
            self.assertIsNotNone(tech_def)
            
            # Kiểm tra tăng cường truy vấn
            legal_query = "khởi kiện dân sự"
            enhanced_query = legal_model.enhance_query_for_domain(legal_query)
            
            self.assertNotEqual(legal_query, enhanced_query)
            self.assertGreater(len(enhanced_query), len(legal_query))
        except Exception as e:
            self.skipTest(f"Bỏ qua kiểm thử domain model: {str(e)}")
    
    @classmethod
    def tearDownClass(cls):
        """Dọn dẹp sau tất cả các bài kiểm tra."""
        if not cls.run_integration_tests:
            return
            
        # Xóa cache
        if hasattr(cls, 'agent') and hasattr(cls.agent, 'cache'):
            cls.agent.clear_cache()
            
        # Xóa thư mục cache nếu tồn tại
        import shutil
        if os.path.exists(cls.config["cache_dir"]):
            shutil.rmtree(cls.config["cache_dir"])


if __name__ == "__main__":
    unittest.main() 