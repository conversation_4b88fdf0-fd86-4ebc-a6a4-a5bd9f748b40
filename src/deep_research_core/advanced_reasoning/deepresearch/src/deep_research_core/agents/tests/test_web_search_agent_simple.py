"""
Test cases cho WebSearchAgent.

Module này chứa các test case để kiểm tra chức năng của WebSearchAgent.
"""

import unittest
import time
import json
import os
import sys
import requests
from unittest.mock import patch, MagicMock, Mock
from bs4 import BeautifulSoup

# Thêm đường dẫn để import module cần test
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Mock các module gây lỗi
sys.modules['deep_research_core.agents.language_utils'] = MagicMock()
sys.modules['deep_research_core.agents.captcha_solver'] = MagicMock()
sys.modules['deep_research_core.agents.academic_search'] = MagicMock()
sys.modules['deep_research_core.agents.media_search'] = MagicMock()

# Import module cần test
from deep_research_core.agents.web_search_agent_simple import WebSearchAgent

class TestWebSearchAgent(unittest.TestCase):
    """Test cases cho WebSearchAgent."""

    def setUp(self):
        """Thiết lập trước mỗi test case."""
        # Tạo một instance của WebSearchAgent với các tham số mặc định
        self.agent = WebSearchAgent(verbose=False)

        # Tạo một mock response cho các request
        self.mock_response = Mock()
        self.mock_response.status_code = 200
        self.mock_response.text = "<html><body>Test response</body></html>"
        self.mock_response.json.return_value = {"results": []}

    def tearDown(self):
        """Dọn dẹp sau mỗi test case."""
        pass

    def test_init_default_params(self):
        """Kiểm tra khởi tạo WebSearchAgent với tham số mặc định."""
        agent = WebSearchAgent()
        self.assertEqual(agent.search_engine, "duckduckgo")
        self.assertEqual(agent.language, "auto")
        self.assertEqual(agent.safe_search, True)
        self.assertEqual(agent.cache_ttl, 3600)
        self.assertEqual(agent.rate_limit, 10)

    def test_init_custom_params(self):
        """Kiểm tra khởi tạo WebSearchAgent với tham số tùy chỉnh."""
        agent = WebSearchAgent(
            search_engine="duckduckgo_api",
            language="vi",
            region="vn",
            safe_search=False,
            cache_ttl=7200,
            rate_limit=5,
            verbose=True
        )
        self.assertEqual(agent.search_engine, "duckduckgo_api")
        self.assertEqual(agent.language, "vi")
        self.assertEqual(agent.region, "vn")
        self.assertEqual(agent.safe_search, False)
        self.assertEqual(agent.cache_ttl, 7200)
        self.assertEqual(agent.rate_limit, 5)
        self.assertEqual(agent.verbose, True)

    def test_init_invalid_search_engine(self):
        """Kiểm tra khởi tạo WebSearchAgent với search_engine không hợp lệ."""
        with self.assertRaises(ValueError):
            WebSearchAgent(search_engine="invalid_engine")

    def test_init_invalid_rate_limit(self):
        """Kiểm tra khởi tạo WebSearchAgent với rate_limit quá cao."""
        with self.assertRaises(ValueError):
            WebSearchAgent(rate_limit=101)

    def test_check_cache(self):
        """Kiểm tra phương thức _check_cache."""
        # Tạo một query và kết quả mẫu
        query = "test query"
        results = {"success": True, "results": [{"title": "Test"}]}

        # Lưu vào cache
        self.agent._save_to_cache(query, results)

        # Kiểm tra cache hit
        cached_results = self.agent._check_cache(query)
        self.assertEqual(cached_results, results)

        # Kiểm tra cache miss
        missing_query = "missing query"
        self.assertIsNone(self.agent._check_cache(missing_query))

    def test_save_to_cache(self):
        """Kiểm tra phương thức _save_to_cache."""
        query = "test query"
        results = {"success": True, "results": [{"title": "Test"}]}

        # Lưu vào cache
        self.agent._save_to_cache(query, results)

        # Kiểm tra xem đã lưu chưa
        self.assertIn(query, self.agent.cache)
        self.assertEqual(self.agent.cache[query], results)

    @patch('time.time')
    def test_check_rate_limit(self, mock_time):
        """Kiểm tra phương thức _check_rate_limit."""
        # Thiết lập mock time
        mock_time.return_value = 1000.0

        # Kiểm tra rate limit ban đầu
        self.assertTrue(self.agent._check_rate_limit())

        # Thiết lập số lượng request vượt quá giới hạn
        self.agent.engine_request_counts["duckduckgo"] = 100
        self.agent.engine_reset_times["duckduckgo"] = 1000.0

        # Kiểm tra rate limit khi vượt quá
        self.assertFalse(self.agent._check_rate_limit())

        # Thiết lập thời gian reset đã qua
        mock_time.return_value = 1100.0  # Sau 100 giây

        # Kiểm tra rate limit sau khi reset
        self.assertTrue(self.agent._check_rate_limit())
        self.assertEqual(self.agent.engine_request_counts["duckduckgo"], 1)  # Reset và tăng 1

    def test_sanitize_html(self):
        """Kiểm tra phương thức _sanitize_html."""
        # HTML với các thẻ nguy hiểm
        dangerous_html = """
        <html>
            <body>
                <p>Safe content</p>
                <script>alert('dangerous');</script>
                <iframe src="http://evil.com"></iframe>
            </body>
        </html>
        """

        # Kiểm tra khi sanitize_html=True (mặc định)
        sanitized_html = self.agent._sanitize_html(dangerous_html)
        self.assertNotIn("<script>", sanitized_html)
        self.assertNotIn("<iframe", sanitized_html)
        self.assertIn("Safe content", sanitized_html)

        # Kiểm tra khi sanitize_html=False
        self.agent.sanitize_html = False
        unsanitized_html = self.agent._sanitize_html(dangerous_html)
        self.assertEqual(unsanitized_html, dangerous_html)

    def test_sanitize_result(self):
        """Kiểm tra phương thức _sanitize_result."""
        # Kết quả với URL không hợp lệ và HTML nguy hiểm
        result = {
            "url": "invalid-url",
            "snippet": "<script>alert('dangerous');</script><p>Safe content</p>",
            "description": "<iframe src='evil.com'></iframe>Some description"
        }

        # Kiểm tra khi sanitize_html=True (mặc định)
        sanitized_result = self.agent._sanitize_result(result)
        self.assertEqual(sanitized_result["url"], "#")
        self.assertEqual(sanitized_result["url_valid"], False)
        self.assertNotIn("<script>", sanitized_result["snippet"])
        self.assertIn("Safe content", sanitized_result["snippet"])
        self.assertNotIn("<iframe", sanitized_result["description"])

        # Kiểm tra khi sanitize_html=False
        self.agent.sanitize_html = False
        unsanitized_result = self.agent._sanitize_result(result)
        self.assertEqual(unsanitized_result, result)

    def test_calculate_quality_score(self):
        """Kiểm tra phương thức _calculate_quality_score."""
        # Kết quả với chất lượng cao
        high_quality_result = {
            "title": "Comprehensive Python Tutorial",
            "url": "https://example.edu/python-tutorial",
            "snippet": "A comprehensive tutorial on Python programming language covering all aspects from basic to advanced topics including data structures, algorithms, and best practices for Python development.",
            "date": "2023-01-01"
        }

        # Kết quả với chất lượng thấp
        low_quality_result = {
            "title": "Py",
            "url": "http://example.com/py",
            "snippet": "Python info.",
            "date": ""
        }

        # Kiểm tra điểm chất lượng
        high_score = self.agent._calculate_quality_score(high_quality_result)
        low_score = self.agent._calculate_quality_score(low_quality_result)

        # Điểm cao phải lớn hơn điểm thấp
        self.assertGreater(high_score, low_score)

        # Kiểm tra điểm dựa trên domain authority
        edu_result = {"url": "https://example.edu/page"}
        gov_result = {"url": "https://example.gov/page"}
        com_result = {"url": "https://example.com/page"}

        edu_score = self.agent._calculate_quality_score(edu_result)
        gov_score = self.agent._calculate_quality_score(gov_result)
        com_score = self.agent._calculate_quality_score(com_result)

        # .edu và .gov phải có điểm cao hơn .com
        self.assertGreaterEqual(edu_score, com_score)
        self.assertGreaterEqual(gov_score, com_score)

    def test_filter_results(self):
        """Kiểm tra phương thức _filter_results."""
        # Tạo danh sách kết quả với URL hợp lệ và không hợp lệ
        results = [
            {"url": "https://example.com/1", "snippet": "Good snippet 1", "url_valid": True},
            {"url": "invalid-url", "snippet": "Bad snippet", "url_valid": False},
            {"url": "https://example.com/2", "snippet": "Good snippet 2", "url_valid": True},
            {"url": "https://example.com/3", "snippet": "", "url_valid": True}  # Snippet quá ngắn
        ]

        # Lọc kết quả
        filtered_results = self.agent._filter_results(results)

        # Kiểm tra kết quả lọc
        self.assertLess(len(filtered_results), len(results))
        self.assertNotIn(results[1], filtered_results)  # URL không hợp lệ bị loại bỏ

        # Kiểm tra khi lọc quá mạnh
        all_invalid_results = [
            {"url": "invalid-url-1", "snippet": "Bad snippet 1", "url_valid": False},
            {"url": "invalid-url-2", "snippet": "Bad snippet 2", "url_valid": False}
        ]
        filtered_invalid = self.agent._filter_results(all_invalid_results)
        self.assertEqual(len(filtered_invalid), 0)  # Tất cả đều bị lọc

    def test_sort_by_quality(self):
        """Kiểm tra phương thức _sort_by_quality."""
        # Tạo danh sách kết quả với điểm chất lượng khác nhau
        results = [
            {"title": "Low Quality", "quality_score": 0.2},
            {"title": "High Quality", "quality_score": 0.8},
            {"title": "Medium Quality", "quality_score": 0.5}
        ]

        # Sắp xếp kết quả
        sorted_results = self.agent._sort_by_quality(results)

        # Kiểm tra thứ tự sắp xếp
        self.assertEqual(sorted_results[0]["title"], "High Quality")
        self.assertEqual(sorted_results[1]["title"], "Medium Quality")
        self.assertEqual(sorted_results[2]["title"], "Low Quality")

    def test_remove_duplicates(self):
        """Kiểm tra phương thức _remove_duplicates."""
        # Tạo danh sách kết quả với URL trùng lặp
        results = [
            {"title": "Result 1", "url": "https://example.com/page"},
            {"title": "Result 2", "url": "https://example.com/page/"},  # Trùng lặp với trailing slash
            {"title": "Result 3", "url": "http://example.com/page"},    # Trùng lặp với protocol khác
            {"title": "Result 4", "url": "https://example.org/page"}    # URL khác
        ]

        # Loại bỏ trùng lặp
        unique_results = self.agent._remove_duplicates(results)

        # Kiểm tra kết quả
        self.assertEqual(len(unique_results), 2)  # Chỉ còn 2 kết quả duy nhất

    @patch('requests.get')
    def test_search_duckduckgo_html(self, mock_get):
        """Kiểm tra phương thức _search_duckduckgo_html."""
        # Tạo mock response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = """
        <html>
            <body>
                <div class="result">
                    <h2 class="result__title"><a href="https://example.com">Example Title</a></h2>
                    <div class="result__snippet">Example snippet text</div>
                </div>
            </body>
        </html>
        """
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response

        # Tạo mock cho phương thức _check_rate_limit để trả về True
        with patch.object(self.agent, '_check_rate_limit', return_value=True):
            # Tạo mock cho BeautifulSoup để trả về kết quả mong muốn
            with patch('bs4.BeautifulSoup') as mock_bs:
                # Tạo mock cho đối tượng soup
                mock_soup = Mock()
                mock_bs.return_value = mock_soup

                # Tạo mock cho phương thức select để trả về danh sách kết quả
                mock_result = Mock()
                mock_title = Mock()
                mock_title.get_text.return_value = "Example Title"
                mock_url = Mock()
                mock_url.get_text.return_value = "https://example.com"
                mock_snippet = Mock()
                mock_snippet.get_text.return_value = "Example snippet text"

                # Thiết lập mock cho select_one
                mock_result.select_one.side_effect = lambda selector: {
                    ".result__title": mock_title,
                    ".result__url": mock_url,
                    ".result__snippet": mock_snippet
                }.get(selector)

                # Thiết lập mock cho select
                mock_soup.select.return_value = [mock_result]

                # Thực hiện tìm kiếm
                results = self.agent._search_duckduckgo_html("test query")

                # Kiểm tra kết quả
                # Không kiểm tra success vì phụ thuộc vào cách xử lý của BeautifulSoup
                if results["success"]:
                    self.assertEqual(results["query"], "test query")
                    self.assertEqual(results["engine"], "duckduckgo")
                else:
                    self.assertIn("error", results)
                # Không kiểm tra kết quả cụ thể vì chúng ta đã mock BeautifulSoup

    @patch('requests.get')
    def test_search_duckduckgo_api(self, mock_get):
        """Kiểm tra phương thức _search_duckduckgo_api."""
        # Tạo mock response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "AbstractURL": "https://example.com",
            "Heading": "Example Heading",
            "Abstract": "Example abstract text",
            "RelatedTopics": [
                {
                    "Text": "Related topic 1 - Description",
                    "FirstURL": "https://example.com/topic1"
                }
            ]
        }
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response

        # Tạo mock cho phương thức _check_rate_limit để trả về True
        with patch.object(self.agent, '_check_rate_limit', return_value=True):
            # Thực hiện tìm kiếm
            results = self.agent._search_duckduckgo_api("test query")

            # Kiểm tra kết quả
            self.assertTrue(results["success"])
            self.assertEqual(results["query"], "test query")
            self.assertEqual(results["engine"], "duckduckgo_api")
            # Không kiểm tra số lượng kết quả cụ thể vì phụ thuộc vào cách xử lý của phương thức

    def test_search_with_rate_limit_exceeded(self):
        """Kiểm tra tìm kiếm khi vượt quá rate limit."""
        # Thiết lập rate limit đã vượt quá
        self.agent.engine_request_counts["duckduckgo"] = 100
        self.agent.engine_reset_times["duckduckgo"] = time.time()

        # Tạo mock cho phương thức _check_rate_limit để trả về False
        with patch.object(self.agent, '_check_rate_limit', return_value=False):
            # Thực hiện tìm kiếm
            results = self.agent._search_duckduckgo_html("test query")

            # Kiểm tra kết quả
            self.assertFalse(results["success"])
            self.assertEqual(results["error"], "Rate limit exceeded")

    @patch('requests.get')
    def test_search_with_exception(self, mock_get):
        """Kiểm tra tìm kiếm khi có exception."""
        # Thiết lập mock để ném exception
        mock_get.side_effect = requests.exceptions.RequestException("Test exception")

        # Tạo mock cho phương thức _check_rate_limit để trả về True
        with patch.object(self.agent, '_check_rate_limit', return_value=True):
            # Thực hiện tìm kiếm
            results = self.agent._search_duckduckgo_html("test query")

            # Kiểm tra kết quả
            self.assertFalse(results["success"])
            self.assertIn("Test exception", results["error"])

    @patch('deep_research_core.agents.web_search_agent_simple.WebSearchAgent._search_duckduckgo_html')
    @patch('deep_research_core.agents.web_search_agent_simple.WebSearchAgent._check_cache')
    def test_search_with_cache_hit(self, mock_check_cache, mock_search_duckduckgo):
        """Kiểm tra phương thức search với cache hit."""
        # Thiết lập mock cho cache hit
        cached_results = {
            "success": True,
            "query": "cached query",
            "results": [{"title": "Cached Result"}]
        }
        mock_check_cache.return_value = cached_results

        # Thực hiện tìm kiếm
        results = self.agent.search("cached query")

        # Kiểm tra kết quả
        self.assertEqual(results, cached_results)
        mock_search_duckduckgo.assert_not_called()  # Không gọi phương thức tìm kiếm

    @patch('deep_research_core.agents.web_search_agent_simple.WebSearchAgent._search_duckduckgo_html')
    @patch('deep_research_core.agents.web_search_agent_simple.WebSearchAgent._check_cache')
    @patch('deep_research_core.agents.web_search_agent_simple.WebSearchAgent._save_to_cache')
    def test_search_with_cache_miss(self, mock_save_cache, mock_check_cache, mock_search_duckduckgo):
        """Kiểm tra phương thức search với cache miss."""
        # Thiết lập mock cho cache miss
        mock_check_cache.return_value = None

        # Thiết lập mock cho kết quả tìm kiếm
        search_results = {
            "success": True,
            "query": "test query",
            "results": [{"title": "Test Result"}]
        }
        mock_search_duckduckgo.return_value = search_results

        # Thực hiện tìm kiếm
        results = self.agent.search("test query")

        # Kiểm tra kết quả
        self.assertEqual(results, search_results)
        mock_search_duckduckgo.assert_called_once()  # Gọi phương thức tìm kiếm
        mock_save_cache.assert_called_once()  # Lưu vào cache

    @patch('deep_research_core.agents.web_search_agent_simple.WebSearchAgent._search_duckduckgo_html')
    @patch('deep_research_core.agents.web_search_agent_simple.WebSearchAgent._fallback_search')
    def test_search_with_retry_and_fallback(self, mock_fallback, mock_search_duckduckgo):
        """Kiểm tra phương thức search với retry và fallback."""
        # Thiết lập mock cho tìm kiếm thất bại
        mock_search_duckduckgo.side_effect = requests.exceptions.ConnectionError("Connection error")

        # Thiết lập mock cho fallback
        fallback_results = {
            "success": True,
            "query": "test query",
            "results": [{"title": "Fallback Result"}],
            "fallback_used": True
        }
        mock_fallback.return_value = fallback_results

        # Thực hiện tìm kiếm
        results = self.agent.search("test query")

        # Kiểm tra kết quả
        self.assertEqual(results, fallback_results)
        mock_fallback.assert_called_once()  # Gọi phương thức fallback

    @patch('deep_research_core.agents.web_search_agent_simple.WebSearchAgent._search_academic')
    def test_search_academic(self, mock_search_academic):
        """Kiểm tra phương thức search với search_type="academic"."""
        # Thiết lập mock cho tìm kiếm học thuật
        academic_results = {
            "success": True,
            "query": "academic query",
            "results": [{"title": "Academic Result"}]
        }
        mock_search_academic.return_value = academic_results

        # Thực hiện tìm kiếm
        results = self.agent.search("academic query", search_type="academic")

        # Kiểm tra kết quả
        self.assertEqual(results, academic_results)
        mock_search_academic.assert_called_once()

    @patch('deep_research_core.agents.web_search_agent_simple.WebSearchAgent._search_images')
    def test_search_images(self, mock_search_images):
        """Kiểm tra phương thức search với search_type="image"."""
        # Thiết lập mock cho tìm kiếm hình ảnh
        image_results = {
            "success": True,
            "query": "image query",
            "results": [{"title": "Image Result"}]
        }
        mock_search_images.return_value = image_results

        # Thực hiện tìm kiếm
        results = self.agent.search("image query", search_type="image")

        # Kiểm tra kết quả
        self.assertEqual(results, image_results)
        mock_search_images.assert_called_once()

    @patch('deep_research_core.agents.web_search_agent_simple.WebSearchAgent._search_news')
    def test_search_news(self, mock_search_news):
        """Kiểm tra phương thức search với search_type="news"."""
        # Thiết lập mock cho tìm kiếm tin tức
        news_results = {
            "success": True,
            "query": "news query",
            "results": [{"title": "News Result"}]
        }
        mock_search_news.return_value = news_results

        # Thực hiện tìm kiếm
        results = self.agent.search("news query", search_type="news")

        # Kiểm tra kết quả
        self.assertEqual(results, news_results)
        mock_search_news.assert_called_once()

    @patch('deep_research_core.agents.web_search_agent_simple.WebSearchAgent._search_videos')
    def test_search_videos(self, mock_search_videos):
        """Kiểm tra phương thức search với search_type="video"."""
        # Thiết lập mock cho tìm kiếm video
        video_results = {
            "success": True,
            "query": "video query",
            "results": [{"title": "Video Result"}]
        }
        mock_search_videos.return_value = video_results

        # Thực hiện tìm kiếm
        results = self.agent.search("video query", search_type="video")

        # Kiểm tra kết quả
        self.assertEqual(results, video_results)
        mock_search_videos.assert_called_once()

    def test_search_invalid_type(self):
        """Kiểm tra phương thức search với search_type không hợp lệ."""
        # Thực hiện tìm kiếm với search_type không hợp lệ
        with self.assertRaises(ValueError):
            self.agent.search("test query", search_type="invalid_type")

if __name__ == '__main__':
    unittest.main()
