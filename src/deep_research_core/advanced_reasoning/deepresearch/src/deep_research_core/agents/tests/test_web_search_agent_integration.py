#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Integration tests for WebSearchAgent.
This tests the full functionality of the WebSearchAgent with real network requests.

NOTE: These tests make actual network requests and may be affected by
rate limiting, network connectivity, or changes to external services.
"""

import unittest
import time
import os
import logging

from deep_research_core.agents.web_search_agent import WebSearchAgent

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestWebSearchAgentIntegration(unittest.TestCase):
    """Integration tests for WebSearchAgent."""

    @classmethod
    def setUpClass(cls):
        """Set up test environment once before all tests."""
        cls.agent = WebSearchAgent(
            search_method="auto",
            rate_limit=5,  # Lower rate limit for testing
            cache_ttl=300,  # Short cache time for testing
            verbose=True
        )

    def setUp(self):
        """Set up before each test."""
        # Sleep to respect rate limits (important for integration tests)
        time.sleep(2)

    def test_search_with_duckduckgo(self):
        """Test search with Duck<PERSON>uckGo."""
        # Skip if no network connection
        if os.environ.get('SKIP_NETWORK_TESTS'):
            self.skipTest("Network tests disabled")

        query = "Python programming language"
        results = self.agent.search(
            query=query,
            method="api",
            num_results=3
        )

        self.assertEqual(results["query"], query)
        if results["success"]:
            self.assertGreaterEqual(len(results.get("results", [])), 1)
            self.assertEqual(results["engine"], "duckduckgo")
        else:
            logger.warning(f"DuckDuckGo search failed: {results.get('error', 'Unknown error')}")
            # If API failed, it should try HTML fallback
            self.assertIn("search_method", results)

    def test_search_with_fallback(self):
        """Test search with fallback mechanism when primary method fails."""
        # Skip if no network connection
        if os.environ.get('SKIP_NETWORK_TESTS'):
            self.skipTest("Network tests disabled")

        # Force API method with an invalid engine to trigger fallback
        self.agent.api_search_config = {"engine": "invalid_engine"}
        query = "Machine learning basics"
        results = self.agent.search(
            query=query,
            num_results=3,
            auto_method=True  # Enable auto fallback
        )

        self.assertEqual(results["query"], query)
        self.assertTrue(results["success"])
        self.assertGreaterEqual(len(results.get("results", [])), 1)
        self.assertTrue(results.get("fallback", False))
        self.assertIsNotNone(results.get("original_method"))

    def test_content_extraction(self):
        """Test content extraction from URLs."""
        # Skip if no network connection
        if os.environ.get('SKIP_NETWORK_TESTS'):
            self.skipTest("Network tests disabled")

        # Test with Wikipedia (structured content)
        url = "https://en.wikipedia.org/wiki/Python_(programming_language)"
        content = self.agent.extract_content(url)

        self.assertTrue(content["success"])
        self.assertEqual(content["url"], url)
        self.assertIn("Python", content["title"])
        self.assertGreater(len(content["text"]), 1000)  # Should extract substantial content
        self.assertGreater(len(content["links"]), 5)  # Should extract multiple links

        # Test with example.com (simple content)
        url = "https://example.com"
        content = self.agent.extract_content(url)

        self.assertTrue(content["success"])
        self.assertEqual(content["url"], url)
        self.assertIn("Example", content["title"])

    def test_search_with_html_fallback(self):
        """Test direct use of HTML fallback methods."""
        # Skip if no network connection
        if os.environ.get('SKIP_NETWORK_TESTS'):
            self.skipTest("Network tests disabled")

        # Test DuckDuckGo HTML fallback
        query = "Web scraping with Python"
        results = self.agent._search_duckduckgo_html(query, num_results=3)

        if results["success"]:
            self.assertEqual(results["query"], query)
            self.assertEqual(results["engine"], "duckduckgo")
            self.assertEqual(results["search_method"], "html")
            self.assertGreaterEqual(len(results["results"]), 1)
        else:
            logger.warning(f"DuckDuckGo HTML fallback failed: {results.get('error', 'Unknown error')}")

        # Test SearX HTML fallback (if previous test passed)
        if results["success"]:
            # Wait to respect rate limits
            time.sleep(2)
            query = "Natural language processing"
            results = self.agent._search_searx_html(query, num_results=3)

            if results["success"]:
                self.assertEqual(results["query"], query)
                self.assertEqual(results["engine"], "searx")
                self.assertEqual(results["search_method"], "html")
                self.assertGreaterEqual(len(results["results"]), 1)
            else:
                logger.warning(f"SearX HTML fallback failed: {results.get('error', 'Unknown error')}")

    def test_search_vietnamese_query(self):
        """Test search with Vietnamese query."""
        # Skip if no network connection
        if os.environ.get('SKIP_NETWORK_TESTS'):
            self.skipTest("Network tests disabled")

        query = "Ngôn ngữ lập trình Python"
        results = self.agent.search(
            query=query,
            method="auto",
            num_results=3,
            language="vi"
        )

        self.assertEqual(results["query"], query)
        if results["success"]:
            self.assertGreaterEqual(len(results.get("results", [])), 1)
        else:
            logger.warning(f"Vietnamese search failed: {results.get('error', 'Unknown error')}")


if __name__ == '__main__':
    unittest.main() 