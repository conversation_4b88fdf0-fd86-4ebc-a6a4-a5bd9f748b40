"""
Test cases cho WebSearchAgent với trọng tâm vào phân tích độ khó của câu hỏi và lựa chọn API.

Module này chứa các test case để kiểm tra:
1. <PERSON> độ khó thì đổi nơi gọi API
2. Tùy theo câu hỏi để đánh giá nên gọi API ở đâu
3. <PERSON><PERSON>h giá đầu ra xem có đúng không
"""

import unittest
import time
import json
import os
import sys
from unittest.mock import patch, MagicMock, Mock

# Thêm đường dẫn để import module cần test
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Mock các module gây lỗi
sys.modules['deep_research_core.agents.language_utils'] = MagicMock()
sys.modules['deep_research_core.agents.captcha_solver'] = MagicMock()
sys.modules['deep_research_core.agents.academic_search'] = MagicMock()
sys.modules['deep_research_core.agents.media_search'] = MagicMock()

# Import module cần test
from deep_research_core.agents.web_search_agent_simple import WebSearchAgent
from deep_research_core.agents.query_analyzer import QueryAnalyzer

class TestWebSearchQueryAnalysis(unittest.TestCase):
    """Test cases cho WebSearchAgent với trọng tâm vào phân tích độ khó của câu hỏi và lựa chọn API."""

    def setUp(self):
        """Thiết lập trước mỗi test case."""
        # Tạo một instance của WebSearchAgent với các tham số mặc định
        self.agent = WebSearchAgent(verbose=False)
        
        # Tạo một instance của QueryAnalyzer
        self.query_analyzer = QueryAnalyzer()

    def tearDown(self):
        """Dọn dẹp sau mỗi test case."""
        pass
        
    def test_query_analysis(self):
        """Kiểm tra phân tích độ khó của câu hỏi."""
        # Danh sách các câu hỏi với độ khó khác nhau
        queries = [
            # Câu hỏi đơn giản
            "Thủ đô của Việt Nam",
            "Ai là chủ tịch nước Việt Nam",
            "Thời tiết Hà Nội hôm nay",
            
            # Câu hỏi trung bình
            "Cách làm bánh mì Việt Nam truyền thống",
            "Các địa điểm du lịch nổi tiếng ở Đà Nẵng",
            "Lịch sử hình thành của Hà Nội",
            
            # Câu hỏi phức tạp
            "So sánh chi tiết các mô hình học máy trong xử lý ngôn ngữ tự nhiên",
            "Phân tích tác động của biến đổi khí hậu đến nền kinh tế Việt Nam trong 10 năm qua",
            "Đánh giá hiệu quả của các phương pháp điều trị ung thư mới nhất năm 2023"
        ]
        
        # Kiểm tra phân tích độ khó của từng câu hỏi
        for query in queries:
            # Phân tích câu hỏi
            with patch.object(self.agent, 'analyze_query') as mock_analyze:
                # Gọi phương thức analyze_query
                self.agent.analyze_query(query)
                
                # Kiểm tra xem phương thức analyze_query đã được gọi với đúng tham số chưa
                mock_analyze.assert_called_once_with(query)
    
    @patch('deep_research_core.agents.web_search_agent_simple.WebSearchAgent.analyze_query')
    def test_search_method_selection_by_complexity(self, mock_analyze):
        """Kiểm tra lựa chọn phương thức tìm kiếm dựa trên độ khó của câu hỏi."""
        # Thiết lập mock cho phương thức analyze_query
        
        # Trường hợp 1: Câu hỏi đơn giản
        mock_analyze.return_value = {
            "complexity_score": 0.2,
            "query_type": "informational",
            "requires_detail": False,
            "requires_recency": False,
            "recommended_search_method": "api"
        }
        
        # Kiểm tra phương thức tìm kiếm cho câu hỏi đơn giản
        method = self.agent.decide_search_method("Thủ đô của Việt Nam")
        self.assertEqual(method, "api")
        
        # Trường hợp 2: Câu hỏi phức tạp
        mock_analyze.return_value = {
            "complexity_score": 0.8,
            "query_type": "informational",
            "requires_detail": True,
            "requires_recency": False,
            "recommended_search_method": "crawlee"
        }
        
        # Kiểm tra phương thức tìm kiếm cho câu hỏi phức tạp
        method = self.agent.decide_search_method("So sánh chi tiết các mô hình học máy trong xử lý ngôn ngữ tự nhiên")
        self.assertEqual(method, "crawlee")
        
        # Trường hợp 3: Câu hỏi yêu cầu thông tin mới
        mock_analyze.return_value = {
            "complexity_score": 0.5,
            "query_type": "informational",
            "requires_detail": False,
            "requires_recency": True,
            "recommended_search_method": "api"
        }
        
        # Kiểm tra phương thức tìm kiếm cho câu hỏi yêu cầu thông tin mới
        method = self.agent.decide_search_method("Tin tức mới nhất về COVID-19")
        self.assertEqual(method, "api")
    
    @patch('deep_research_core.agents.web_search_agent_simple.WebSearchAgent.analyze_query')
    def test_search_method_selection_by_query_type(self, mock_analyze):
        """Kiểm tra lựa chọn phương thức tìm kiếm dựa trên loại câu hỏi."""
        # Thiết lập mock cho phương thức analyze_query
        
        # Trường hợp 1: Câu hỏi học thuật
        mock_analyze.return_value = {
            "complexity_score": 0.6,
            "query_type": "academic",
            "requires_detail": True,
            "requires_recency": False,
            "recommended_search_method": "academic"
        }
        
        # Kiểm tra phương thức tìm kiếm cho câu hỏi học thuật
        with patch.object(self.agent, '_search_academic') as mock_search_academic:
            mock_search_academic.return_value = {"success": True, "results": []}
            self.agent.search("Machine learning applications in healthcare 2023", search_type="academic")
            mock_search_academic.assert_called_once()
        
        # Trường hợp 2: Câu hỏi về địa điểm
        mock_analyze.return_value = {
            "complexity_score": 0.3,
            "query_type": "map",
            "requires_detail": False,
            "requires_recency": False,
            "recommended_search_method": "map"
        }
        
        # Kiểm tra phương thức tìm kiếm cho câu hỏi về địa điểm
        with patch.object(self.agent, 'search') as mock_search:
            mock_search.return_value = {"success": True, "results": []}
            self.agent.search("Hồ Gươm Hà Nội", search_type="map")
            mock_search.assert_called_once()
    
    @patch('requests.get')
    def test_output_validation(self, mock_get):
        """Kiểm tra đánh giá đầu ra của kết quả tìm kiếm."""
        # Tạo mock response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = """
        <html>
            <body>
                <div class="result">
                    <h2 class="result__title"><a href="https://example.com">Example Title</a></h2>
                    <div class="result__snippet">Example snippet text</div>
                </div>
            </body>
        </html>
        """
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # Tạo mock cho phương thức _check_rate_limit để trả về True
        with patch.object(self.agent, '_check_rate_limit', return_value=True):
            # Tạo mock cho BeautifulSoup để trả về kết quả mong muốn
            with patch('bs4.BeautifulSoup') as mock_bs:
                # Tạo mock cho đối tượng soup
                mock_soup = Mock()
                mock_bs.return_value = mock_soup
                
                # Tạo mock cho phương thức select để trả về danh sách kết quả
                mock_result = Mock()
                mock_title = Mock()
                mock_title.get_text.return_value = "Example Title"
                mock_url = Mock()
                mock_url.get_text.return_value = "https://example.com"
                mock_snippet = Mock()
                mock_snippet.get_text.return_value = "Example snippet text"
                
                # Thiết lập mock cho select_one
                mock_result.select_one.side_effect = lambda selector: {
                    ".result__title": mock_title,
                    ".result__url": mock_url,
                    ".result__snippet": mock_snippet
                }.get(selector)
                
                # Thiết lập mock cho select
                mock_soup.select.return_value = [mock_result]
                
                # Tạo mock cho phương thức _filter_results để trả về kết quả đã lọc
                with patch.object(self.agent, '_filter_results', return_value=[
                    {"title": "Example Title", "url": "https://example.com", "snippet": "Example snippet text"}
                ]):
                    # Tạo mock cho phương thức _sort_by_quality để giữ nguyên kết quả
                    with patch.object(self.agent, '_sort_by_quality', return_value=[
                        {"title": "Example Title", "url": "https://example.com", "snippet": "Example snippet text"}
                    ]):
                        # Tạo mock cho phương thức _remove_duplicates để giữ nguyên kết quả
                        with patch.object(self.agent, '_remove_duplicates', return_value=[
                            {"title": "Example Title", "url": "https://example.com", "snippet": "Example snippet text"}
                        ]):
                            # Thực hiện tìm kiếm
                            results = self.agent._search_duckduckgo_html("test query")
                            
                            # Kiểm tra kết quả
                            self.assertTrue(results["success"])
                            self.assertEqual(results["query"], "test query")
                            self.assertEqual(results["engine"], "duckduckgo")
                            
                            # Kiểm tra chất lượng kết quả
                            for result in results["results"]:
                                # Kiểm tra các trường bắt buộc
                                self.assertIn("title", result)
                                self.assertIn("url", result)
                                self.assertIn("snippet", result)
                                
                                # Kiểm tra URL hợp lệ
                                self.assertTrue(result["url"].startswith("http"))
                                
                                # Kiểm tra title và snippet không rỗng
                                self.assertTrue(len(result["title"]) > 0)
                                self.assertTrue(len(result["snippet"]) > 0)
    
    @patch('deep_research_core.agents.web_search_agent_simple.WebSearchAgent.analyze_query')
    @patch('deep_research_core.agents.web_search_agent_simple.WebSearchAgent._search_duckduckgo_html')
    @patch('deep_research_core.agents.web_search_agent_simple.WebSearchAgent._search_duckduckgo_api')
    def test_vietnamese_query_handling(self, mock_api, mock_html, mock_analyze):
        """Kiểm tra xử lý câu hỏi tiếng Việt."""
        # Thiết lập mock cho phương thức analyze_query
        mock_analyze.return_value = {
            "complexity_score": 0.4,
            "query_type": "informational",
            "requires_detail": False,
            "requires_recency": False,
            "recommended_search_method": "api"
        }
        
        # Thiết lập mock cho phương thức _search_duckduckgo_html
        mock_html.return_value = {
            "success": True,
            "query": "Thủ đô của Việt Nam",
            "engine": "duckduckgo",
            "results": [
                {"title": "Hà Nội", "url": "https://example.com/hanoi", "snippet": "Hà Nội là thủ đô của Việt Nam"}
            ]
        }
        
        # Thiết lập mock cho phương thức _search_duckduckgo_api
        mock_api.return_value = {
            "success": True,
            "query": "Thủ đô của Việt Nam",
            "engine": "duckduckgo_api",
            "results": [
                {"title": "Hà Nội", "url": "https://example.com/hanoi", "snippet": "Hà Nội là thủ đô của Việt Nam"}
            ]
        }
        
        # Kiểm tra xử lý câu hỏi tiếng Việt
        with patch.object(self.agent, '_check_cache', return_value=None):
            with patch.object(self.agent, '_save_to_cache'):
                # Thực hiện tìm kiếm
                results = self.agent.search("Thủ đô của Việt Nam")
                
                # Kiểm tra kết quả
                self.assertTrue(results["success"])
                self.assertEqual(results["query"], "Thủ đô của Việt Nam")
                self.assertGreaterEqual(len(results["results"]), 1)
                
                # Kiểm tra nội dung kết quả
                self.assertEqual(results["results"][0]["title"], "Hà Nội")
                self.assertEqual(results["results"][0]["url"], "https://example.com/hanoi")
                self.assertEqual(results["results"][0]["snippet"], "Hà Nội là thủ đô của Việt Nam")

if __name__ == '__main__':
    unittest.main()
