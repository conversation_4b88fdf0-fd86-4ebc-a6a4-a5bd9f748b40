#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Unit tests for rate limiter.
"""

import unittest
import time
import os
import sys
import json
from unittest.mock import patch, MagicMock

# Add the src directory to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../../..')))

from deepresearch.src.deep_research_core.agents.rate_limiter import (
    TokenBucketRateLimiter,
    AdaptiveRateLimiter,
    ExponentialBackoffRateLimiter
)

class TestTokenBucketRateLimiter(unittest.TestCase):
    """Test TokenBucketRateLimiter."""

    def setUp(self):
        """Set up test fixtures."""
        self.rate_limit = 10
        self.time_window = 60.0
        self.limiter = TokenBucketRateLimiter(
            rate_limit=self.rate_limit,
            time_window=self.time_window,
            burst_limit=self.rate_limit,
            verbose=False
        )

    def test_initialization(self):
        """Test initialization."""
        self.assertEqual(self.limiter.rate_limit, self.rate_limit)
        self.assertEqual(self.limiter.time_window, self.time_window)
        self.assertEqual(self.limiter.token_rate, self.rate_limit / self.time_window)
        self.assertEqual(self.limiter.burst_limit, self.rate_limit)

    def test_check_limit(self):
        """Test check_limit."""
        # Should be within limit initially
        self.assertTrue(self.limiter.check_limit())

        # Consume all tokens
        for _ in range(self.rate_limit):
            self.limiter.increment()

        # Should be at limit now
        self.assertFalse(self.limiter.check_limit())

    def test_wait_if_limited(self):
        """Test wait_if_limited."""
        # Consume all tokens
        for _ in range(self.rate_limit):
            self.limiter.increment()

        # Should need to wait now
        with patch('time.sleep') as mock_sleep:
            wait_time = self.limiter.wait_if_limited()
            mock_sleep.assert_called_once()
            self.assertGreater(wait_time, 0)

    def test_engine_specific_limits(self):
        """Test engine-specific rate limits."""
        # Check different engines
        self.assertTrue(self.limiter.check_limit(engine="google"))
        self.assertTrue(self.limiter.check_limit(engine="bing"))

        # Consume all tokens for google
        for _ in range(self.rate_limit):
            self.limiter.increment(engine="google")

        # Google should be at limit, but bing should not
        self.assertFalse(self.limiter.check_limit(engine="google"))
        self.assertTrue(self.limiter.check_limit(engine="bing"))

    def test_get_stats(self):
        """Test get_stats."""
        # Make some requests
        self.limiter.increment(engine="google")
        self.limiter.increment(engine="google")
        self.limiter.increment(engine="bing")

        # Get stats for google
        stats = self.limiter.get_stats(engine="google")
        self.assertEqual(stats["requests"], 2)

        # Get stats for all engines
        all_stats = self.limiter.get_stats()
        self.assertEqual(all_stats["total_requests"], 3)
        self.assertEqual(all_stats["engines"]["google"]["requests"], 2)
        self.assertEqual(all_stats["engines"]["bing"]["requests"], 1)

class TestAdaptiveRateLimiter(unittest.TestCase):
    """Test AdaptiveRateLimiter."""

    def setUp(self):
        """Set up test fixtures."""
        self.rate_limit = 10
        self.time_window = 60.0
        self.limiter = AdaptiveRateLimiter(
            rate_limit=self.rate_limit,
            time_window=self.time_window,
            burst_limit=self.rate_limit,
            min_rate_limit=1,
            max_rate_limit=20,
            backoff_factor=0.5,
            recovery_factor=0.1,
            adaptation_interval=5,
            verbose=False,
            persistent=False
        )

    def test_initialization(self):
        """Test initialization."""
        self.assertEqual(self.limiter.rate_limit, self.rate_limit)
        self.assertEqual(self.limiter.time_window, self.time_window)
        self.assertEqual(self.limiter.min_rate_limit, 1)
        self.assertEqual(self.limiter.max_rate_limit, 20)
        self.assertEqual(self.limiter.backoff_factor, 0.5)
        self.assertEqual(self.limiter.recovery_factor, 0.1)
        self.assertEqual(self.limiter.adaptation_interval, 5)

    def test_record_response(self):
        """Test record_response."""
        # Record some responses
        self.limiter.record_response(engine="google", success=True)
        self.limiter.record_response(engine="google", success=False)
        self.limiter.record_response(engine="google", success=True, status_code=200)

        # Check history
        self.assertEqual(len(self.limiter.request_history["google"]), 3)
        self.assertEqual(self.limiter.requests_since_adaptation["google"], 3)

    def test_adapt_rate_limit(self):
        """Test _adapt_rate_limit."""
        # Record successful responses
        for _ in range(10):
            self.limiter.record_response(engine="google", success=True)

        # Get initial token_rate
        initial_token_rate = self.limiter.token_rate

        # Manually trigger adaptation
        self.limiter._adapt_rate_limit("google")

        # Token rate should increase or stay the same due to high success ratio
        self.assertGreaterEqual(self.limiter.token_rate, initial_token_rate)

        # Record failed responses
        for _ in range(10):
            self.limiter.record_response(engine="bing", success=False)

        # Get initial token_rate for bing
        if "bing" in self.limiter.buckets:
            initial_token_rate = self.limiter.buckets["bing"].get("token_rate", self.limiter.token_rate)
        else:
            initial_token_rate = self.limiter.token_rate

        # Manually trigger adaptation
        self.limiter._adapt_rate_limit("bing")

        # Token rate should decrease or stay the same due to low success ratio
        if "bing" in self.limiter.buckets:
            new_token_rate = self.limiter.buckets["bing"].get("token_rate", self.limiter.token_rate)
        else:
            new_token_rate = self.limiter.token_rate

        self.assertLessEqual(new_token_rate, initial_token_rate)

    def test_persistent_config(self):
        """Test persistent configuration."""
        # Create a temporary file
        temp_file = "test_adaptive_rate_limiter.json"

        # Create a limiter with persistence
        persistent_limiter = AdaptiveRateLimiter(
            rate_limit=self.rate_limit,
            time_window=self.time_window,
            persistent=True,
            persistent_file=temp_file
        )

        # Change token_rate
        persistent_limiter.token_rate = 0.5

        # Save config
        persistent_limiter._save_config()

        # Create a new limiter with the same file
        new_limiter = AdaptiveRateLimiter(
            rate_limit=self.rate_limit,
            time_window=self.time_window,
            persistent=True,
            persistent_file=temp_file
        )

        # Token rate should be loaded from file
        self.assertEqual(new_limiter.token_rate, 0.5)

        # Clean up
        if os.path.exists(temp_file):
            os.remove(temp_file)

class TestExponentialBackoffRateLimiter(unittest.TestCase):
    """Test ExponentialBackoffRateLimiter."""

    def setUp(self):
        """Set up test fixtures."""
        self.rate_limit = 10
        self.time_window = 60.0
        self.limiter = ExponentialBackoffRateLimiter(
            rate_limit=self.rate_limit,
            time_window=self.time_window,
            burst_limit=self.rate_limit,
            initial_backoff_time=0.1,
            backoff_multiplier=2.0,
            max_backoff_time=10.0,
            jitter=0.0,
            verbose=False
        )

    def test_initialization(self):
        """Test initialization."""
        self.assertEqual(self.limiter.rate_limit, self.rate_limit)
        self.assertEqual(self.limiter.time_window, self.time_window)
        self.assertEqual(self.limiter.initial_backoff_time, 0.1)
        self.assertEqual(self.limiter.backoff_multiplier, 2.0)
        self.assertEqual(self.limiter.max_backoff_time, 10.0)
        self.assertEqual(self.limiter.jitter, 0.0)

    def test_calculate_backoff_time(self):
        """Test _calculate_backoff_time."""
        # Test with different retry counts
        self.assertEqual(self.limiter._calculate_backoff_time(0), 0.1)
        self.assertEqual(self.limiter._calculate_backoff_time(1), 0.2)
        self.assertEqual(self.limiter._calculate_backoff_time(2), 0.4)
        self.assertEqual(self.limiter._calculate_backoff_time(3), 0.8)

        # Test with max_backoff_time
        self.assertEqual(self.limiter._calculate_backoff_time(10), 10.0)  # Should be capped at max_backoff_time

    def test_record_response_with_429(self):
        """Test record_response with 429 status code."""
        # Record a 429 response
        with patch('time.time', return_value=100.0):
            self.limiter.record_response(engine="google", success=False, status_code=429)

        # Check backoff info
        self.assertEqual(self.limiter.backoff_info["google"]["retry_count"], 1)
        # Backoff time is calculated as initial_backoff_time * (backoff_multiplier ** retry_count)
        # For retry_count=1, it's 0.1 * (2.0 ** 1) = 0.2
        self.assertEqual(self.limiter.backoff_info["google"]["next_retry_time"], 100.2)  # 100.0 + 0.2

        # Record another 429 response
        with patch('time.time', return_value=101.0):
            self.limiter.record_response(engine="google", success=False, status_code=429)

        # Check backoff info (should increase)
        self.assertEqual(self.limiter.backoff_info["google"]["retry_count"], 2)
        # For retry_count=2, it's 0.1 * (2.0 ** 2) = 0.4
        self.assertEqual(self.limiter.backoff_info["google"]["next_retry_time"], 101.4)  # 101.0 + 0.4

    def test_check_limit_with_backoff(self):
        """Test check_limit with backoff."""
        # Set up backoff
        self.limiter.backoff_info["google"] = {
            "retry_count": 1,
            "next_retry_time": time.time() + 1.0
        }

        # Should be limited due to backoff
        self.assertFalse(self.limiter.check_limit(engine="google"))

        # Set next_retry_time in the past
        self.limiter.backoff_info["google"]["next_retry_time"] = time.time() - 1.0

        # Should not be limited anymore
        self.assertTrue(self.limiter.check_limit(engine="google"))

    def test_wait_if_limited_with_backoff(self):
        """Test wait_if_limited with backoff."""
        # Set up backoff
        with patch('time.time', return_value=100.0):
            self.limiter.backoff_info["google"] = {
                "retry_count": 1,
                "next_retry_time": 101.0
            }

        # Should wait due to backoff
        with patch('time.time', return_value=100.0), patch('time.sleep') as mock_sleep:
            wait_time = self.limiter.wait_if_limited(engine="google")
            mock_sleep.assert_called_once_with(1.0)
            self.assertEqual(wait_time, 1.0)

if __name__ == '__main__':
    unittest.main()
