"""
Test cases cho WebSearchAgent với trọng tâm vào:
1. <PERSON>ộ khó thì đổi nơi gọi API
2. Tùy theo câu hỏi để đánh giá nên gọi API ở đâu
3. <PERSON><PERSON><PERSON> giá đầu ra xem có đúng không
"""

import unittest
import time
import json
import os
import sys
import requests
from unittest.mock import patch, MagicMock, Mock

# Thêm đường dẫn để import module cần test
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Mock các module gây lỗi
sys.modules['deep_research_core.agents.language_utils'] = MagicMock()
sys.modules['deep_research_core.agents.captcha_solver'] = MagicMock()
sys.modules['deep_research_core.agents.academic_search'] = MagicMock()
sys.modules['deep_research_core.agents.media_search'] = MagicMock()

# Import module cần test
from deep_research_core.agents.web_search_agent_simple import WebSearchAgent

class TestWebSearchAgentFeatures(unittest.TestCase):
    """Test cases cho WebSearchAgent với trọng tâm vào các tính năng chính."""

    def setUp(self):
        """Thiết lập trước mỗi test case."""
        # Tạo một instance của WebSearchAgent với các tham số mặc định
        self.agent = WebSearchAgent(verbose=False)

    def tearDown(self):
        """Dọn dẹp sau mỗi test case."""
        pass

    @patch('requests.get')
    def test_search_type_selection(self, mock_get):
        """Kiểm tra lựa chọn loại tìm kiếm dựa trên tham số search_type."""
        # Tạo mock response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = "<html><body>Test response</body></html>"
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response

        # Tạo mock cho các phương thức tìm kiếm
        with patch.object(self.agent, '_search_duckduckgo_html') as mock_web:
            with patch.object(self.agent, '_search_academic') as mock_academic:
                with patch.object(self.agent, '_search_images') as mock_images:
                    with patch.object(self.agent, '_search_news') as mock_news:
                        with patch.object(self.agent, '_search_videos') as mock_videos:
                            # Thiết lập mock cho các phương thức tìm kiếm
                            mock_web.return_value = {"success": True, "results": []}
                            mock_academic.return_value = {"success": True, "results": []}
                            mock_images.return_value = {"success": True, "results": []}
                            mock_news.return_value = {"success": True, "results": []}
                            mock_videos.return_value = {"success": True, "results": []}

                            # Kiểm tra tìm kiếm web
                            self.agent.search("test query", search_type="web")
                            mock_web.assert_called_once()
                            mock_academic.assert_not_called()
                            mock_images.assert_not_called()
                            mock_news.assert_not_called()
                            mock_videos.assert_not_called()

                            # Reset mocks
                            mock_web.reset_mock()
                            mock_academic.reset_mock()
                            mock_images.reset_mock()
                            mock_news.reset_mock()
                            mock_videos.reset_mock()

                            # Kiểm tra tìm kiếm học thuật
                            self.agent.search("academic query", search_type="academic")
                            mock_web.assert_not_called()
                            mock_academic.assert_called_once()
                            mock_images.assert_not_called()
                            mock_news.assert_not_called()
                            mock_videos.assert_not_called()

                            # Reset mocks
                            mock_web.reset_mock()
                            mock_academic.reset_mock()
                            mock_images.reset_mock()
                            mock_news.reset_mock()
                            mock_videos.reset_mock()

                            # Kiểm tra tìm kiếm hình ảnh
                            self.agent.search("image query", search_type="image")
                            mock_web.assert_not_called()
                            mock_academic.assert_not_called()
                            mock_images.assert_called_once()
                            mock_news.assert_not_called()
                            mock_videos.assert_not_called()

                            # Reset mocks
                            mock_web.reset_mock()
                            mock_academic.reset_mock()
                            mock_images.reset_mock()
                            mock_news.reset_mock()
                            mock_videos.reset_mock()

                            # Kiểm tra tìm kiếm tin tức
                            self.agent.search("news query", search_type="news")
                            mock_web.assert_not_called()
                            mock_academic.assert_not_called()
                            mock_images.assert_not_called()
                            mock_news.assert_called_once()
                            mock_videos.assert_not_called()

                            # Reset mocks
                            mock_web.reset_mock()
                            mock_academic.reset_mock()
                            mock_images.reset_mock()
                            mock_news.reset_mock()
                            mock_videos.reset_mock()

                            # Kiểm tra tìm kiếm video
                            self.agent.search("video query", search_type="video")
                            mock_web.assert_not_called()
                            mock_academic.assert_not_called()
                            mock_images.assert_not_called()
                            mock_news.assert_not_called()
                            mock_videos.assert_called_once()

    def test_search_type_selection_by_parameter(self):
        """Kiểm tra lựa chọn loại tìm kiếm dựa trên tham số search_type."""
        # Tạo một instance mới của WebSearchAgent
        agent = WebSearchAgent(verbose=False)

        # Kiểm tra tìm kiếm với các loại khác nhau
        with patch.object(agent, '_search_duckduckgo_html') as mock_duckduckgo:
            with patch.object(agent, '_search_academic') as mock_academic:
                with patch.object(agent, '_search_images') as mock_images:
                    with patch.object(agent, '_search_news') as mock_news:
                        with patch.object(agent, '_search_videos') as mock_videos:
                            # Thiết lập mock cho các phương thức tìm kiếm
                            mock_duckduckgo.return_value = {"success": True, "results": []}
                            mock_academic.return_value = {"success": True, "results": []}
                            mock_images.return_value = {"success": True, "results": []}
                            mock_news.return_value = {"success": True, "results": []}
                            mock_videos.return_value = {"success": True, "results": []}

                            # Kiểm tra tìm kiếm web
                            agent.search_engine = "duckduckgo"  # Đặt search_engine thành duckduckgo
                            agent.search("test query", search_type="web")
                            mock_duckduckgo.assert_called_once()
                            mock_academic.assert_not_called()
                            mock_images.assert_not_called()
                            mock_news.assert_not_called()
                            mock_videos.assert_not_called()

                            # Reset mocks
                            mock_duckduckgo.reset_mock()
                            mock_academic.reset_mock()
                            mock_images.reset_mock()
                            mock_news.reset_mock()
                            mock_videos.reset_mock()

                            # Kiểm tra tìm kiếm học thuật
                            agent.search("academic query", search_type="academic")
                            mock_duckduckgo.assert_not_called()
                            mock_academic.assert_called_once()
                            mock_images.assert_not_called()
                            mock_news.assert_not_called()
                            mock_videos.assert_not_called()

                            # Reset mocks
                            mock_duckduckgo.reset_mock()
                            mock_academic.reset_mock()
                            mock_images.reset_mock()
                            mock_news.reset_mock()
                            mock_videos.reset_mock()

                            # Kiểm tra tìm kiếm hình ảnh
                            agent.search("image query", search_type="image")
                            mock_duckduckgo.assert_not_called()
                            mock_academic.assert_not_called()
                            mock_images.assert_called_once()
                            mock_news.assert_not_called()
                            mock_videos.assert_not_called()

                            # Reset mocks
                            mock_duckduckgo.reset_mock()
                            mock_academic.reset_mock()
                            mock_images.reset_mock()
                            mock_news.reset_mock()
                            mock_videos.reset_mock()

                            # Kiểm tra tìm kiếm tin tức
                            agent.search("news query", search_type="news")
                            mock_duckduckgo.assert_not_called()
                            mock_academic.assert_not_called()
                            mock_images.assert_not_called()
                            mock_news.assert_called_once()
                            mock_videos.assert_not_called()

                            # Reset mocks
                            mock_duckduckgo.reset_mock()
                            mock_academic.reset_mock()
                            mock_images.reset_mock()
                            mock_news.reset_mock()
                            mock_videos.reset_mock()

                            # Kiểm tra tìm kiếm video
                            agent.search("video query", search_type="video")
                            mock_duckduckgo.assert_not_called()
                            mock_academic.assert_not_called()
                            mock_images.assert_not_called()
                            mock_news.assert_not_called()
                            mock_videos.assert_called_once()

    def test_fallback_search(self):
        """Kiểm tra tìm kiếm fallback khi tìm kiếm chính thất bại."""
        # Tạo một instance mới của WebSearchAgent
        agent = WebSearchAgent(verbose=False)

        # Tạo mock cho phương thức _fallback_search
        with patch.object(agent, '_fallback_search') as mock_fallback:
            # Thiết lập mock cho phương thức _fallback_search
            mock_fallback.return_value = {
                "success": True,
                "query": "test query",
                "results": [{"title": "Fallback Result"}],
                "fallback_used": True
            }

            # Tạo mock cho phương thức _search_duckduckgo_html để ném exception
            with patch.object(agent, '_search_duckduckgo_html', side_effect=requests.exceptions.ConnectionError("Test exception")):
                # Đặt search_engine thành duckduckgo
                agent.search_engine = "duckduckgo"

                # Thực hiện tìm kiếm
                results = agent.search("test query")

                # Kiểm tra kết quả
                mock_fallback.assert_called_once()
                self.assertTrue(results["success"])
                self.assertTrue(results["fallback_used"])
                self.assertEqual(results["results"][0]["title"], "Fallback Result")

    def test_quality_score_calculation(self):
        """Kiểm tra tính điểm chất lượng cho kết quả tìm kiếm."""
        # Tạo các kết quả tìm kiếm với chất lượng khác nhau
        high_quality_result = {
            "title": "Comprehensive Python Tutorial",
            "url": "https://example.edu/python-tutorial",
            "snippet": "A comprehensive tutorial on Python programming language covering all aspects from basic to advanced topics including data structures, algorithms, and best practices for Python development.",
            "date": "2023-01-01"
        }

        medium_quality_result = {
            "title": "Python Tutorial",
            "url": "https://example.com/python-tutorial",
            "snippet": "Learn Python programming language with this tutorial.",
            "date": "2022-01-01"
        }

        low_quality_result = {
            "title": "Py",
            "url": "http://example.net/py",
            "snippet": "Python info.",
            "date": ""
        }

        # Tính điểm chất lượng
        high_score = self.agent._calculate_quality_score(high_quality_result)
        medium_score = self.agent._calculate_quality_score(medium_quality_result)
        low_score = self.agent._calculate_quality_score(low_quality_result)

        # Kiểm tra điểm chất lượng
        self.assertGreater(high_score, medium_score)
        self.assertGreater(medium_score, low_score)

        # Kiểm tra điểm dựa trên domain authority
        edu_result = {"url": "https://example.edu/page", "title": "EDU Page", "snippet": "EDU content"}
        gov_result = {"url": "https://example.gov/page", "title": "GOV Page", "snippet": "GOV content"}
        org_result = {"url": "https://example.org/page", "title": "ORG Page", "snippet": "ORG content"}
        com_result = {"url": "https://example.com/page", "title": "COM Page", "snippet": "COM content"}

        edu_score = self.agent._calculate_quality_score(edu_result)
        gov_score = self.agent._calculate_quality_score(gov_result)
        org_score = self.agent._calculate_quality_score(org_result)
        com_score = self.agent._calculate_quality_score(com_result)

        # .edu và .gov phải có điểm cao hơn .org và .com
        self.assertGreater(edu_score, com_score)
        self.assertGreater(gov_score, com_score)
        self.assertGreater(org_score, com_score)

    @patch('requests.get')
    def test_output_validation(self, mock_get):
        """Kiểm tra đánh giá đầu ra của kết quả tìm kiếm."""
        # Tạo mock response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = """
        <html>
            <body>
                <div class="result">
                    <h2 class="result__title"><a href="https://example.com">Example Title</a></h2>
                    <div class="result__snippet">Example snippet text</div>
                </div>
            </body>
        </html>
        """
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response

        # Tạo mock cho phương thức _check_rate_limit để trả về True
        with patch.object(self.agent, '_check_rate_limit', return_value=True):
            # Tạo mock cho BeautifulSoup để trả về kết quả mong muốn
            with patch('bs4.BeautifulSoup') as mock_bs:
                # Tạo mock cho đối tượng soup
                mock_soup = Mock()
                mock_bs.return_value = mock_soup

                # Tạo mock cho phương thức select để trả về danh sách kết quả
                mock_result = Mock()

                # Mock cho title
                mock_title = Mock()
                mock_title.get_text.return_value = "Example Title"

                # Mock cho link trong title
                mock_link = Mock()
                mock_link.get_text.return_value = "Example Title"
                mock_link.has_attr.return_value = True
                mock_link.__getitem__ = lambda key: "https://example.com" if key == "href" else None

                # Mock cho snippet
                mock_snippet = Mock()
                mock_snippet.get_text.return_value = "Example snippet text"

                # Thiết lập mock cho select_one
                mock_result.select_one.side_effect = lambda selector: {
                    ".result__title": mock_title,
                    ".result__title a": mock_link,
                    ".result__snippet": mock_snippet
                }.get(selector)

                # Thiết lập mock cho select
                mock_soup.select.return_value = [mock_result]

                # Thực hiện tìm kiếm
                with patch.object(self.agent, '_filter_results', return_value=[
                    {"title": "Example Title", "url": "https://example.com", "snippet": "Example snippet text"}
                ]):
                    with patch.object(self.agent, '_sort_by_quality', return_value=[
                        {"title": "Example Title", "url": "https://example.com", "snippet": "Example snippet text"}
                    ]):
                        with patch.object(self.agent, '_remove_duplicates', return_value=[
                            {"title": "Example Title", "url": "https://example.com", "snippet": "Example snippet text"}
                        ]):
                            # Thực hiện tìm kiếm
                            results = self.agent._search_duckduckgo_html("test query")

                            # Kiểm tra kết quả
                            if results["success"]:
                                # Kiểm tra các trường bắt buộc
                                for result in results["results"]:
                                    self.assertIn("title", result)
                                    self.assertIn("url", result)
                                    self.assertIn("snippet", result)

                                    # Kiểm tra title và snippet không rỗng
                                    self.assertTrue(len(result["title"]) > 0)
                                    self.assertTrue(len(result["snippet"]) > 0)

                                    # Kiểm tra URL hợp lệ
                                    self.assertTrue(result["url"].startswith("http"))

if __name__ == '__main__':
    unittest.main()
