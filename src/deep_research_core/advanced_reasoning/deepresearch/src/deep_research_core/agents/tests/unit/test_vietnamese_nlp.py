"""
<PERSON><PERSON><PERSON> thử đơn vị cho lớp VietnameseNLP và VietnameseDomainModel.

Mo<PERSON><PERSON> này chứa các bài kiểm tra đơn vị chi tiết cho các chức năng xử lý
ngôn ngữ tiếng <PERSON>, bao gồ<PERSON> tách từ, t<PERSON><PERSON> tắ<PERSON>, phân tích cảm xúc, và các
chức năng kh<PERSON>c.
"""

import unittest
import tempfile
import os
from unittest.mock import patch, MagicMock, PropertyMock

from deep_research_core.agents.vietnamese_nlp import VietnameseNLP, VietnameseDomainModel


class TestVietnameseNLP(unittest.TestCase):
    """Bài kiểm thử đơn vị cho lớp VietnameseNLP."""

    @classmethod
    def setUpClass(cls):
        """Cài đặt cho tất cả các bài kiểm tra."""
        # Cố gắng khởi tạo VietnameseNLP
        cls.nlp = VietnameseNLP()
        cls.has_underthesea = cls.nlp.underthesea_available
    
    def test_initialization(self):
        """<PERSON><PERSON><PERSON> tra khởi tạo VietnameseNLP."""
        self.assertIsInstance(self.nlp, VietnameseNLP)
        
        # Kiểm tra các thuộc tính quan trọng
        self.assertIsNotNone(self.nlp.underthesea_available)
        self.assertIsNotNone(self.nlp.phobert_available)
    
    def test_word_tokenize(self):
        """Kiểm tra tách từ tiếng Việt."""
        text = "Học máy là một nhánh của trí tuệ nhân tạo."
        
        if not self.has_underthesea:
            # Kiểm tra phương pháp dự phòng nếu không có underthesea
            tokens = self.nlp.word_tokenize(text)
            self.assertIsInstance(tokens, list)
            self.assertTrue(len(tokens) > 0)
        else:
            # Kiểm tra với underthesea
            tokens = self.nlp.word_tokenize(text)
            self.assertIsInstance(tokens, list)
            self.assertTrue(len(tokens) > 0)
            
            # Đảm bảo kết quả có các từ quan trọng
            important_words = ["Học", "máy", "trí tuệ", "nhân tạo"]
            for word in important_words:
                self.assertTrue(any(word in token for token in tokens), 
                               f"Từ '{word}' không xuất hiện trong kết quả tách từ")
    
    def test_pos_tag(self):
        """Kiểm tra gán nhãn từ loại tiếng Việt."""
        text = "Máy tính hiện đại rất mạnh."
        
        if not self.has_underthesea:
            # Kiểm tra hành vi khi không có underthesea
            pos_tags = self.nlp.pos_tag(text)
            self.assertEqual(pos_tags, [])
        else:
            # Kiểm tra với underthesea
            pos_tags = self.nlp.pos_tag(text)
            self.assertIsInstance(pos_tags, list)
            self.assertTrue(len(pos_tags) > 0)
            
            # Kiểm tra cấu trúc kết quả
            for item in pos_tags:
                self.assertIsInstance(item, tuple)
                self.assertEqual(len(item), 2)
                self.assertIsInstance(item[0], str)  # Từ
                self.assertIsInstance(item[1], str)  # Nhãn từ loại
    
    def test_extract_keywords(self):
        """Kiểm tra trích xuất từ khóa tiếng Việt."""
        text = """
        Trí tuệ nhân tạo (AI) là một lĩnh vực của khoa học máy tính tập trung vào việc 
        tạo ra các hệ thống thông minh có khả năng thực hiện các nhiệm vụ thường đòi hỏi 
        trí thông minh của con người.
        """
        
        keywords = self.nlp.extract_keywords(text, max_keywords=5)
        
        # Kiểm tra cấu trúc kết quả
        self.assertIsInstance(keywords, list)
        self.assertLessEqual(len(keywords), 5)
        
        if keywords:
            for kw in keywords:
                self.assertIsInstance(kw, dict)
                self.assertIn("text", kw)
                self.assertIn("score", kw)
                self.assertIsInstance(kw["text"], str)
                self.assertIsInstance(kw["score"], float)
                self.assertGreaterEqual(kw["score"], 0.0)
                self.assertLessEqual(kw["score"], 1.0)
    
    def test_sentiment_with_underthesea(self):
        """Kiểm tra phân tích cảm xúc với underthesea."""
        if not self.has_underthesea:
            self.skipTest("Bỏ qua kiểm thử, underthesea không khả dụng")
        
        # Test với văn bản tích cực
        positive_text = "Sản phẩm này rất tuyệt vời và đáng tiền."
        positive_result = self.nlp.sentiment(positive_text)
        
        self.assertIsInstance(positive_result, dict)
        self.assertIn("label", positive_result)
        self.assertIn("score", positive_result)
        self.assertIn("text", positive_result)
        
        # Kiểm tra dựa trên phương thức sentiment có sẵn hay không
        import underthesea
        if hasattr(underthesea, 'sentiment'):
            # Underthesea có sentiment, nên kết quả sẽ đến từ đó
            pass
        else:
            # Kiểm tra phương pháp dự phòng
            self.assertIn("note", positive_result)
            self.assertIn("Kết quả từ phương pháp dự phòng", positive_result["note"])
    
    def test_sentiment_fallback(self):
        """Kiểm tra phương pháp dự phòng cho phân tích cảm xúc."""
        # Test với văn bản tích cực
        positive_text = "Sản phẩm này rất tuyệt vời và đáng tiền."
        
        # Tạo mock cho underthesea_available để buộc dùng phương pháp dự phòng
        with patch.object(self.nlp, 'underthesea_available', False):
            positive_result = self.nlp.sentiment(positive_text)
            
            self.assertIsInstance(positive_result, dict)
            self.assertEqual(positive_result.get("error"), "Underthesea không khả dụng")
        
        # Test với _fallback_sentiment trực tiếp
        positive_result = self.nlp._fallback_sentiment(positive_text)
        negative_text = "Sản phẩm này thật tệ và không đáng tiền."
        negative_result = self.nlp._fallback_sentiment(negative_text)
        neutral_text = "Đây là một sản phẩm bình thường."
        neutral_result = self.nlp._fallback_sentiment(neutral_text)
        
        # Kiểm tra kết quả
        self.assertEqual(positive_result["label"], "positive")
        self.assertEqual(negative_result["label"], "negative")
        self.assertEqual(neutral_result["label"], "neutral")
        
        # Kiểm tra điểm
        self.assertGreater(positive_result["score"], 0.5)
        self.assertLess(negative_result["score"], 0.5)
        self.assertAlmostEqual(neutral_result["score"], 0.5)
    
    def test_summarize_basic(self):
        """Kiểm tra tóm tắt văn bản tiếng Việt với phương pháp cơ bản."""
        text = """
        Trí tuệ nhân tạo (AI) là một lĩnh vực của khoa học máy tính tập trung vào việc tạo ra các hệ thống 
        thông minh có khả năng thực hiện các nhiệm vụ thường đòi hỏi trí thông minh của con người. 
        Học máy là một nhánh của AI cho phép máy tính học từ dữ liệu và cải thiện hiệu suất mà không cần 
        được lập trình rõ ràng. Xử lý ngôn ngữ tự nhiên là lĩnh vực AI tập trung vào tương tác giữa máy tính 
        và ngôn ngữ tự nhiên của con người. Thị giác máy tính là lĩnh vực nghiên cứu liên quan đến cách 
        máy tính có thể trích xuất thông tin từ hình ảnh và video. Robotics là lĩnh vực kết hợp AI và kỹ thuật
        để tạo ra máy móc có thể tương tác với thế giới vật lý.
        """
        
        # Tóm tắt với 2 câu
        summary = self.nlp.summarize(text, max_sentences=2, method="basic")
        
        # Kiểm tra kết quả
        self.assertIsInstance(summary, str)
        self.assertTrue(len(summary) > 0)
        self.assertLess(len(summary), len(text))
        
        # Phải ít hơn 3 câu
        sentences_in_summary = len(summary.split("."))
        self.assertLessEqual(sentences_in_summary, 3)  # +1 cho câu có thể không kết thúc bằng dấu chấm
    
    def test_summarize_textrank(self):
        """Kiểm tra tóm tắt văn bản tiếng Việt với phương pháp TextRank."""
        text = """
        Trí tuệ nhân tạo (AI) là một lĩnh vực của khoa học máy tính tập trung vào việc tạo ra các hệ thống 
        thông minh có khả năng thực hiện các nhiệm vụ thường đòi hỏi trí thông minh của con người. 
        Học máy là một nhánh của AI cho phép máy tính học từ dữ liệu và cải thiện hiệu suất mà không cần 
        được lập trình rõ ràng. Xử lý ngôn ngữ tự nhiên là lĩnh vực AI tập trung vào tương tác giữa máy tính 
        và ngôn ngữ tự nhiên của con người. Thị giác máy tính là lĩnh vực nghiên cứu liên quan đến cách 
        máy tính có thể trích xuất thông tin từ hình ảnh và video. Robotics là lĩnh vực kết hợp AI và kỹ thuật
        để tạo ra máy móc có thể tương tác với thế giới vật lý.
        """
        
        # Tóm tắt với TextRank
        summary = self.nlp.summarize(text, max_sentences=2, method="textrank")
        
        # Kiểm tra kết quả
        self.assertIsInstance(summary, str)
        self.assertTrue(len(summary) > 0)
        self.assertLess(len(summary), len(text))
    
    def test_text_similarity(self):
        """Kiểm tra độ tương đồng văn bản tiếng Việt."""
        text1 = "Python là ngôn ngữ lập trình bậc cao, dễ đọc và dễ học."
        text2 = "Python đơn giản, dễ học và là một ngôn ngữ lập trình cấp cao."
        text3 = "Java là một ngôn ngữ lập trình hướng đối tượng."
        
        # Tính độ tương đồng
        sim1_2 = self.nlp.text_similarity(text1, text2)
        sim1_3 = self.nlp.text_similarity(text1, text3)
        sim1_1 = self.nlp.text_similarity(text1, text1)
        
        # Kiểm tra các kết quả
        self.assertIsInstance(sim1_2, float)
        self.assertIsInstance(sim1_3, float)
        self.assertIsInstance(sim1_1, float)
        
        # Kiểm tra phạm vi giá trị
        self.assertGreaterEqual(sim1_2, 0.0)
        self.assertLessEqual(sim1_2, 1.0)
        self.assertGreaterEqual(sim1_3, 0.0)
        self.assertLessEqual(sim1_3, 1.0)
        
        # Text1 nên giống text2 hơn text3
        self.assertGreater(sim1_2, sim1_3)
        
        # Text1 nên giống chính nó nhất
        self.assertAlmostEqual(sim1_1, 1.0, delta=0.001)
    
    def test_recognize_entities(self):
        """Kiểm tra nhận dạng thực thể tiếng Việt."""
        text = "Ông Nguyễn Văn A là giám đốc công ty XYZ tại Hà Nội."
        
        if not self.has_underthesea:
            # Sử dụng NER nếu có underthesea
            entities = []
        else:
            # Thử sử dụng NER từ underthesea nếu có
            try:
                import underthesea
                if hasattr(underthesea, 'ner'):
                    entities = underthesea.ner(text)
                    
                    # Kiểm tra cấu trúc kết quả
                    self.assertIsInstance(entities, list)
                    
                    for entity in entities:
                        if isinstance(entity, tuple) and len(entity) >= 2:
                            word, entity_type = entity[:2]
                            self.assertIsInstance(word, str)
                            self.assertIsInstance(entity_type, str)
                else:
                    self.skipTest("underthesea.ner không khả dụng")
            except ImportError:
                self.skipTest("underthesea không khả dụng")


class TestVietnameseDomainModel(unittest.TestCase):
    """Bài kiểm thử đơn vị cho lớp VietnameseDomainModel."""
    
    def test_initialization(self):
        """Kiểm tra khởi tạo VietnameseDomainModel."""
        model = VietnameseDomainModel(domain="legal")
        
        self.assertIsInstance(model, VietnameseDomainModel)
        self.assertEqual(model.domain, "legal")
        self.assertTrue(model.initialized)
        
        # Kiểm tra các từ điển
        self.assertIsInstance(model.vocabulary, dict)
        self.assertIsInstance(model.synonyms, dict)
        self.assertIsInstance(model.entities, dict)
    
    def test_domain_vocabulary(self):
        """Kiểm tra từ vựng domain."""
        legal_model = VietnameseDomainModel(domain="legal")
        tech_model = VietnameseDomainModel(domain="tech")
        medical_model = VietnameseDomainModel(domain="medical")
        
        # Kiểm tra có từ vựng khác nhau
        self.assertNotEqual(legal_model.vocabulary, tech_model.vocabulary)
        self.assertNotEqual(legal_model.vocabulary, medical_model.vocabulary)
        self.assertNotEqual(tech_model.vocabulary, medical_model.vocabulary)
        
        # Kiểm tra định nghĩa cụ thể
        legal_term = "luật"
        tech_term = "thuật toán"
        medical_term = "bệnh án"
        
        self.assertIsNotNone(legal_model.get_domain_definition(legal_term))
        self.assertIsNotNone(tech_model.get_domain_definition(tech_term))
        self.assertIsNotNone(medical_model.get_domain_definition(medical_term))
    
    def test_domain_synonyms(self):
        """Kiểm tra từ đồng nghĩa domain."""
        legal_model = VietnameseDomainModel(domain="legal")
        
        # Kiểm tra từ đồng nghĩa
        synonyms = legal_model.get_domain_synonyms("luật")
        
        self.assertIsInstance(synonyms, list)
        self.assertGreater(len(synonyms), 0)
        
        # Từ không tồn tại
        missing_synonyms = legal_model.get_domain_synonyms("không_tồn_tại")
        self.assertEqual(missing_synonyms, [])
    
    def test_domain_entities(self):
        """Kiểm tra thực thể domain."""
        legal_model = VietnameseDomainModel(domain="legal")
        
        # Kiểm tra thực thể
        self.assertTrue(legal_model.is_domain_entity("Tòa án nhân dân"))
        self.assertFalse(legal_model.is_domain_entity("không_tồn_tại"))
        
        # Kiểm tra thực thể với loại
        self.assertTrue(legal_model.is_domain_entity("Tòa án nhân dân", entity_type="ORG"))
        self.assertFalse(legal_model.is_domain_entity("Tòa án nhân dân", entity_type="PER"))
    
    def test_query_enhancement(self):
        """Kiểm tra tăng cường truy vấn với thuật ngữ domain."""
        legal_model = VietnameseDomainModel(domain="legal")
        
        query = "luật dân sự"
        enhanced_query = legal_model.enhance_query_for_domain(query)
        
        self.assertIsInstance(enhanced_query, str)
        self.assertGreater(len(enhanced_query), len(query))
        
        # Kiểm tra truy vấn không có từ đồng nghĩa
        fake_query = "xyz абв 123"
        fake_enhanced = legal_model.enhance_query_for_domain(fake_query)
        self.assertEqual(fake_query, fake_enhanced)
    
    def test_word_embedding(self):
        """Kiểm tra vector từ trong domain."""
        model = VietnameseDomainModel(domain="tech")
        
        # Với mock cho model
        test_embedding = [0.1, 0.2, 0.3]
        with patch.object(model, 'model', create=True) as mock_model, \
             patch.object(model, 'tokenizer', create=True) as mock_tokenizer:
                
            mock_outputs = MagicMock()
            mock_hidden_state = MagicMock()
            mock_tensor = MagicMock()
            mock_tensor.tolist.return_value = test_embedding
            
            mock_hidden_state.mean.return_value = mock_tensor
            mock_outputs.last_hidden_state = mock_hidden_state
            mock_model.return_value = mock_outputs
            
            embedding = model.get_word_embedding("test")
            
            self.assertEqual(embedding, test_embedding)
        
        # Kiểm tra khi model không khả dụng
        with patch.object(model, 'model', None):
            embedding = model.get_word_embedding("test")
            self.assertIsNone(embedding)


if __name__ == "__main__":
    unittest.main() 