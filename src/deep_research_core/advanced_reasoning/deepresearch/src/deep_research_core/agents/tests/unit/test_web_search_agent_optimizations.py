"""
<PERSON><PERSON><PERSON> thử các tính năng tối ưu của WebSearchAgent.

Mo<PERSON>le này chứa các bài kiểm tra cho các tính năng tối ưu hóa mới bổ sung
vào WebSearchAgent, bao gồm tối ưu bộ nhớ, xử lý tiếng Vi<PERSON> nâng cao,
và tối ưu tải trang.
"""

import unittest
import time
import sys
import gc
import re
from unittest.mock import patch, MagicMock, PropertyMock
from typing import Dict, Any, List

from deep_research_core.agents.web_search_agent import WebSearchAgent
from deep_research_core.agents.batch_search_optimizer import BatchSearchOptimizer
from deep_research_core.agents.vietnamese_nlp import VietnameseNLP
from deep_research_core.agents.pagination_handler import PaginationHandler, LazyLoadHandler


class TestMemoryOptimizations(unittest.TestCase):
    """Kiểm tra các tính năng tối ưu hóa bộ nhớ."""
    
    def setUp(self):
        """Cài đặt trước mỗi bài kiểm tra."""
        # C<PERSON>u hình tối ưu bộ nhớ được bật
        self.config = {
            "memory_management": {
                "enable_optimization": True,
                "max_cache_memory_percent": 75,
                "result_limit_size": 1024 * 100,  # 100KB
                "cleanup_interval": 1  # 1 giây để kiểm tra dễ dàng hơn
            }
        }
        
        self.agent = WebSearchAgent(config=self.config)
    
    @patch('deep_research_core.agents.web_search_agent.gc')
    def test_memory_cleanup_triggers(self, mock_gc):
        """Kiểm tra kích hoạt dọn dẹp bộ nhớ."""
        # Đặt thời gian dọn dẹp về quá khứ
        self.agent.last_memory_cleanup = time.time() - 10
        
        # Gọi phương thức sẽ kích hoạt kiểm tra bộ nhớ
        with patch.object(self.agent, '_perform_memory_cleanup') as mock_cleanup:
            self.agent.search("test query")
            mock_cleanup.assert_called_once()
        
        # Kiểm tra rằng gc.collect() được gọi
        self.agent._perform_memory_cleanup()
        mock_gc.collect.assert_called_once()
    
    @patch('deep_research_core.agents.web_search_agent.WebSearchAgent._get_memory_usage')
    def test_memory_cleanup_when_memory_high(self, mock_get_memory):
        """Kiểm tra dọn dẹp bộ nhớ khi sử dụng nhiều bộ nhớ."""
        # Giả lập sử dụng bộ nhớ cao
        mock_get_memory.return_value = {
            "rss_mb": 1000,
            "percent": 80.0,  # Trên ngưỡng 75%
            "system_percent": 90.0
        }
        
        # Tạo dữ liệu giả để dọn dẹp
        self.agent.last_captcha_detection = {
            "query1": {"timestamp": time.time() - 7200},  # 2 giờ trước
            "query2": {"timestamp": time.time()}  # Hiện tại
        }
        
        # Gọi hàm dọn dẹp và kiểm tra kết quả
        result = self.agent._perform_memory_cleanup()
        
        # Kiểm tra dữ liệu cũ đã bị xóa
        self.assertEqual(len(self.agent.last_captcha_detection), 1)
        self.assertIn("query2", self.agent.last_captcha_detection)
        self.assertTrue(result["cleaned"])
    
    @patch('deep_research_core.agents.web_search_agent.WebSearchAgent._get_memory_usage')
    def test_no_cleanup_when_memory_low(self, mock_get_memory):
        """Kiểm tra không dọn dẹp khi bộ nhớ thấp."""
        # Giả lập sử dụng bộ nhớ thấp
        mock_get_memory.return_value = {
            "rss_mb": 500,
            "percent": 30.0,  # Dưới ngưỡng 75%
            "system_percent": 40.0
        }
        
        # Gọi hàm dọn dẹp và kiểm tra kết quả
        result = self.agent._perform_memory_cleanup()
        
        # Kiểm tra không có hành động dọn dẹp
        self.assertFalse(result["cleaned"])
    
    def test_batch_optimizer_memory_management(self):
        """Kiểm tra quản lý bộ nhớ trong BatchSearchOptimizer."""
        optimizer = BatchSearchOptimizer(
            search_agent=self.agent,
            config={
                "max_history_size": 10,
                "cleanup_interval": 1
            }
        )
        
        # Thêm nhiều tác vụ để vượt quá giới hạn
        for i in range(20):
            optimizer.add_task({
                "query": f"test query {i}",
                "priority": 1
            })
        
        # Kiểm tra cắt giảm lịch sử
        self.assertLessEqual(len(optimizer.task_history), 10)
        
        # Kiểm tra dọn dẹp bộ nhớ
        cleanup_stats = optimizer.force_memory_cleanup()
        self.assertIsInstance(cleanup_stats, dict)
        self.assertIn("history_trimmed", cleanup_stats)


class TestVietnameseLanguageImprovements(unittest.TestCase):
    """Kiểm tra các cải tiến xử lý tiếng Việt."""
    
    def setUp(self):
        """Cài đặt trước mỗi bài kiểm tra."""
        self.agent = WebSearchAgent(config={"vietnamese_rerank": True})
        self.nlp = VietnameseNLP()
        
        # Dữ liệu mẫu tiếng Việt để kiểm tra
        self.vietnamese_text = "Việt Nam là một quốc gia tuyệt vời với nhiều cảnh đẹp và ẩm thực phong phú."
        self.vietnamese_query = "ẩm thực Việt Nam"
    
    @patch('deep_research_core.agents.vietnamese_nlp.underthesea')
    def test_enhance_search_query(self, mock_underthesea):
        """Kiểm tra tính năng cải thiện truy vấn tìm kiếm."""
        # Giả lập kết quả từ underthesea
        mock_underthesea.word_tokenize.return_value = ["ẩm", "thực", "Việt", "Nam"]
        mock_underthesea.pos_tag.return_value = [
            ("ẩm", "N"), ("thực", "N"), ("Việt", "Np"), ("Nam", "Np")
        ]
        mock_underthesea.ner.return_value = [
            ("Việt", "B-LOC"), ("Nam", "I-LOC")
        ]
        
        # Gọi hàm cải thiện truy vấn
        result = self.nlp.enhance_search_query(self.vietnamese_query)
        
        # Kiểm tra kết quả
        self.assertIn("enhanced_query", result)
        self.assertIn(self.vietnamese_query, result["enhanced_query"])
        self.assertIn("important_words", result)
        self.assertIn("entities", result)
    
    @patch('deep_research_core.agents.web_search_agent.vietnamese_nlp')
    def test_vietnamese_results_enhancement(self, mock_nlp):
        """Kiểm tra tính năng cải thiện kết quả tìm kiếm tiếng Việt."""
        # Tạo dữ liệu tìm kiếm mẫu
        search_result = {
            "success": True,
            "query": self.vietnamese_query,
            "results": [
                {
                    "title": "Ẩm thực Việt Nam - Wikipedia",
                    "snippet": "Ẩm thực Việt Nam sử dụng rất nhiều rau thơm và gia vị.",
                    "url": "https://vi.wikipedia.org/wiki/Am_thuc_Viet_Nam"
                },
                {
                    "title": "10 món ăn ngon nhất của Việt Nam",
                    "snippet": "Việt Nam có nhiều món ăn ngon và nổi tiếng trên thế giới.",
                    "url": "https://example.com/food"
                }
            ]
        }
        
        # Giả lập kết quả từ VietnameseNLP
        mock_nlp.enhance_search_query.return_value = {
            "important_words": ["ẩm", "thực"],
            "entities": [{"text": "Việt Nam", "type": "LOC"}],
            "synonyms_added": ["món ăn", "ẩm thực"]
        }
        mock_nlp.text_similarity.return_value = 0.8
        mock_nlp.extract_keywords.return_value = [
            {"word": "ẩm", "score": 0.9}, 
            {"word": "thực", "score": 0.8}
        ]
        
        # Gọi hàm cải thiện kết quả
        self.agent._enhance_vietnamese_results(search_result)
        
        # Kiểm tra metadata đã được thêm
        self.assertIn("metadata", search_result)
        self.assertIn("vietnamese_query_analysis", search_result["metadata"])
        
        # Kiểm tra xếp hạng kết quả
        for result in search_result["results"]:
            self.assertIn("relevance_score", result)
            self.assertIn("semantic_similarity", result)


class TestPaginationHandling(unittest.TestCase):
    """Kiểm tra các tính năng xử lý phân trang."""
    
    def setUp(self):
        """Cài đặt trước mỗi bài kiểm tra."""
        self.pagination_handler = PaginationHandler(
            max_pages=3,
            max_concurrent=2,
            use_async=False  # Sử dụng chế độ đồng bộ để dễ kiểm tra
        )
    
    @patch('deep_research_core.agents.pagination_handler.requests.get')
    @patch('deep_research_core.agents.pagination_handler.BeautifulSoup')
    def test_extract_paginated_content_with_next_selector(self, mock_bs, mock_get):
        """Kiểm tra trích xuất nội dung từ trang có nút chuyển trang."""
        # Giả lập phản hồi HTTP
        mock_response = MagicMock()
        mock_response.text = "<html><body><div class='content'>Test content</div><a class='next' href='page2.html'>Next</a></body></html>"
        mock_response.raise_for_status = MagicMock()
        mock_get.return_value = mock_response
        
        # Giả lập phân tích HTML
        mock_soup = MagicMock()
        mock_content = MagicMock()
        mock_content.text.strip.return_value = "Test content"
        mock_soup.select.return_value = [mock_content]
        
        mock_next = MagicMock()
        mock_next.has_attr.return_value = True
        mock_next.__getitem__.return_value = "page2.html"
        mock_soup.select_one.return_value = mock_next
        
        mock_bs.return_value = mock_soup
        
        # Gọi hàm trích xuất nội dung
        result = self.pagination_handler.extract_paginated_content(
            start_url="http://example.com",
            content_selector=".content",
            next_page_selector=".next"
        )
        
        # Kiểm tra kết quả
        self.assertTrue(result["success"])
        self.assertEqual(len(result["content"]), 1)
        self.assertEqual(result["pages_loaded"], 1)
    
    @patch('deep_research_core.agents.pagination_handler.requests.get')
    @patch('deep_research_core.agents.pagination_handler.BeautifulSoup')
    def test_extract_paginated_content_with_page_param(self, mock_bs, mock_get):
        """Kiểm tra trích xuất nội dung từ trang có tham số phân trang."""
        # Giả lập phản hồi HTTP
        mock_response = MagicMock()
        mock_response.text = "<html><body><div class='content'>Test content</div></body></html>"
        mock_response.raise_for_status = MagicMock()
        mock_get.return_value = mock_response
        
        # Giả lập phân tích HTML
        mock_soup = MagicMock()
        mock_content = MagicMock()
        mock_content.text.strip.return_value = "Test content"
        mock_soup.select.return_value = [mock_content]
        mock_bs.return_value = mock_soup
        
        # Gọi hàm trích xuất nội dung
        result = self.pagination_handler.extract_paginated_content(
            start_url="http://example.com",
            content_selector=".content",
            page_param="page"
        )
        
        # Kiểm tra kết quả
        self.assertTrue(result["success"])
        # Số lượng request phải phù hợp với max_pages
        self.assertEqual(mock_get.call_count, self.pagination_handler.max_pages)


class TestLazyLoadHandling(unittest.TestCase):
    """Kiểm tra các tính năng xử lý lazy loading."""
    
    def setUp(self):
        """Cài đặt trước mỗi bài kiểm tra."""
        self.lazy_load_handler = LazyLoadHandler(
            scroll_attempts=2,
            wait_time=0.1,
            timeout=5
        )
    
    @patch('deep_research_core.agents.pagination_handler.webdriver')
    def test_extract_dynamic_content_with_selenium(self, mock_webdriver):
        """Kiểm tra trích xuất nội dung từ trang có lazy loading bằng Selenium."""
        # Bỏ qua nếu Selenium không khả dụng
        if not self.lazy_load_handler.selenium_available:
            self.skipTest("Selenium không khả dụng")
        
        # Giả lập trình duyệt
        mock_driver = MagicMock()
        mock_webdriver.Chrome.return_value = mock_driver
        
        # Giả lập phần tử
        mock_element = MagicMock()
        mock_element.text = "Dynamic content"
        mock_element.get_attribute.return_value = "<p>Dynamic content</p>"
        mock_driver.find_elements.return_value = [mock_element]
        
        # Gọi hàm trích xuất nội dung
        result = self.lazy_load_handler._extract_with_selenium(
            url="http://example.com",
            content_selector=".content",
            scroll_selector=None,
            button_selector=".load-more",
            max_items=10,
            wait_for_selector=None
        )
        
        # Kiểm tra kết quả
        self.assertTrue(result["success"])
        self.assertEqual(len(result["content"]), 1)
        self.assertEqual(result["content"][0]["text"], "Dynamic content")


if __name__ == "__main__":
    unittest.main() 