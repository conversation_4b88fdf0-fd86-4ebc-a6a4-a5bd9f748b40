"""
Tests cho các module tiện ích.
"""

import unittest
from unittest.mock import patch, MagicMock
import time
import threading

from src.deep_research_core.agents.utils.retry_util import (
    RetryWithBackoff, CircuitBreaker, RetryError, CircuitBreakerOpenError
)
from src.deep_research_core.agents.utils.parallel_util import (
    SafeParallelExecutor, TaskResult
)
from src.deep_research_core.agents.resource_monitor import ResourceMonitor


class TestRetryWithBackoff(unittest.TestCase):
    """Test RetryWithBackoff."""
    
    def test_successful_execution(self):
        """Test thực thi thành công."""
        # Hàm mock luôn thành công
        mock_func = MagicMock(return_value="success")
        
        # Tạo decorator
        retry = RetryWithBackoff(max_retries=3)
        
        # Thực thi
        decorated_func = retry(mock_func)
        result = decorated_func("arg1", "arg2", kwarg1="value1")
        
        # Ki<PERSON><PERSON> tra
        self.assertEqual(result, "success")
        mock_func.assert_called_once_with("arg1", "arg2", kwarg1="value1")
    
    def test_retry_on_error(self):
        """Test retry khi có lỗi."""
        # Hàm mock lỗi 2 lần rồi thành công
        mock_func = MagicMock(side_effect=[ValueError("Error 1"), ValueError("Error 2"), "success"])
        
        # Tạo decorator
        retry = RetryWithBackoff(max_retries=3, base_delay=0.01)
        
        # Thực thi
        decorated_func = retry(mock_func)
        result = decorated_func()
        
        # Kiểm tra
        self.assertEqual(result, "success")
        self.assertEqual(mock_func.call_count, 3)
    
    def test_exceed_max_retries(self):
        """Test vượt quá số lần retry tối đa."""
        # Hàm mock luôn lỗi
        mock_func = MagicMock(side_effect=ValueError("Always fail"))
        
        # Tạo decorator
        retry = RetryWithBackoff(max_retries=2, base_delay=0.01)
        
        # Thực thi
        decorated_func = retry(mock_func)
        
        # Kiểm tra
        with self.assertRaises(RetryError):
            decorated_func()
        
        self.assertEqual(mock_func.call_count, 3)  # 1 lần đầu + 2 lần retry
    
    def test_specific_exceptions(self):
        """Test chỉ retry cho loại exception cụ thể."""
        # Hàm mock lỗi với loại khác nhau
        mock_func = MagicMock(side_effect=[ValueError("Value error"), TypeError("Type error")])
        
        # Tạo decorator chỉ retry cho ValueError
        retry = RetryWithBackoff(max_retries=2, base_delay=0.01, retry_exceptions=[ValueError])
        
        # Thực thi
        decorated_func = retry(mock_func)
        
        # TypeError không nằm trong danh sách retry nên sẽ được đẩy lên
        with self.assertRaises(TypeError):
            decorated_func()
        
        self.assertEqual(mock_func.call_count, 2)  # 1 lần đầu + 1 lần retry


class TestCircuitBreaker(unittest.TestCase):
    """Test CircuitBreaker."""
    
    def test_successful_execution(self):
        """Test thực thi thành công."""
        # Hàm mock luôn thành công
        mock_func = MagicMock(return_value="success")
        
        # Tạo circuit breaker
        breaker = CircuitBreaker(failure_threshold=3)
        
        # Thực thi
        result = breaker.execute(mock_func, "arg1", kwarg1="value1")
        
        # Kiểm tra
        self.assertEqual(result, "success")
        mock_func.assert_called_once_with("arg1", kwarg1="value1")
        
        # Kiểm tra trạng thái
        state = breaker.get_state()
        self.assertEqual(state["state"], "CLOSED")
        self.assertEqual(state["success_count"], 1)
        self.assertEqual(state["failure_count"], 0)
    
    def test_open_circuit_on_failures(self):
        """Test mở circuit khi có nhiều lỗi."""
        # Hàm mock luôn lỗi
        mock_func = MagicMock(side_effect=ValueError("Always fail"))
        
        # Tạo circuit breaker
        breaker = CircuitBreaker(failure_threshold=2)
        
        # Thực thi và gặp lỗi đến khi mở circuit
        for _ in range(2):
            with self.assertRaises(ValueError):
                breaker.execute(mock_func)
        
        # Lần thứ 3 sẽ mở circuit
        with self.assertRaises(ValueError):
            breaker.execute(mock_func)
        
        # Kiểm tra trạng thái
        state = breaker.get_state()
        self.assertEqual(state["state"], "OPEN")
        self.assertEqual(state["failure_count"], 3)
        
        # Lần tiếp theo sẽ lỗi CircuitBreakerOpenError
        with self.assertRaises(CircuitBreakerOpenError):
            breaker.execute(mock_func)
    
    def test_half_open_after_timeout(self):
        """Test thử lại sau khi timeout."""
        # Hàm mock lỗi rồi thành công
        mock_func = MagicMock(side_effect=[ValueError("Fail 1"), ValueError("Fail 2"), 
                                          ValueError("Fail 3"), "success"])
        
        # Tạo circuit breaker với timeout ngắn
        breaker = CircuitBreaker(failure_threshold=2, reset_timeout=0.1)
        
        # Thực thi và gặp lỗi đến khi mở circuit
        for _ in range(3):
            with self.assertRaises(ValueError):
                breaker.execute(mock_func)
        
        # Kiểm tra trạng thái
        state = breaker.get_state()
        self.assertEqual(state["state"], "OPEN")
        
        # Chờ timeout
        time.sleep(0.2)
        
        # Lần tiếp theo sẽ vào trạng thái HALF_OPEN
        result = breaker.execute(mock_func)
        self.assertEqual(result, "success")
        
        # Kiểm tra trạng thái đã đóng lại
        state = breaker.get_state()
        self.assertEqual(state["state"], "CLOSED")
    
    def test_decorator_usage(self):
        """Test sử dụng như decorator."""
        # Tạo circuit breaker
        breaker = CircuitBreaker(failure_threshold=2)
        
        # Tạo hàm với decorator
        @breaker
        def test_func(arg):
            if arg == "fail":
                raise ValueError("Failed")
            return f"Success: {arg}"
        
        # Thực thi thành công
        result = test_func("test")
        self.assertEqual(result, "Success: test")
        
        # Thực thi lỗi
        for _ in range(2):
            with self.assertRaises(ValueError):
                test_func("fail")
        
        # Lần thứ 3 sẽ mở circuit
        with self.assertRaises(ValueError):
            test_func("fail")
        
        # Lần tiếp theo sẽ lỗi CircuitBreakerOpenError
        with self.assertRaises(CircuitBreakerOpenError):
            test_func("test")


class TestSafeParallelExecutor(unittest.TestCase):
    """Test SafeParallelExecutor."""
    
    def test_execute_many_success(self):
        """Test thực thi nhiều tác vụ thành công."""
        # Hàm mock
        def mock_func(x, y):
            return x * y
        
        # Tạo executor
        executor = SafeParallelExecutor(max_workers=2)
        
        # Tham số
        params = [(2, 3), (4, 5), (6, 7)]
        
        # Thực thi
        results = executor.execute_many(mock_func, params)
        
        # Kiểm tra
        self.assertEqual(len(results), 3)
        self.assertTrue(all(r.success for r in results))
        self.assertEqual([r.result for r in results], [6, 20, 42])
    
    def test_execute_many_with_errors(self):
        """Test thực thi nhiều tác vụ có lỗi."""
        # Hàm mock
        def mock_func(x, y):
            if x == 4:
                raise ValueError("Error for x=4")
            return x * y
        
        # Tạo executor
        executor = SafeParallelExecutor(max_workers=2)
        
        # Tham số
        params = [(2, 3), (4, 5), (6, 7)]
        
        # Thực thi
        results = executor.execute_many(mock_func, params)
        
        # Kiểm tra
        self.assertEqual(len(results), 3)
        self.assertTrue(results[0].success)
        self.assertFalse(results[1].success)
        self.assertTrue(results[2].success)
        self.assertEqual(results[0].result, 6)
        self.assertEqual(results[2].result, 42)
        self.assertIsInstance(results[1].error, ValueError)
    
    def test_execute_batch(self):
        """Test thực thi theo lô."""
        # Hàm mock
        mock_func = MagicMock(side_effect=lambda x, y: x + y)
        
        # Tạo executor
        executor = SafeParallelExecutor(max_workers=2)
        
        # Tham số
        params = [(i, i * 10) for i in range(10)]
        
        # Thực thi theo lô
        results = executor.execute_batch(mock_func, params, batch_size=3, delay_between_batches=0.01)
        
        # Kiểm tra
        self.assertEqual(len(results), 10)
        self.assertTrue(all(r.success for r in results))
        self.assertEqual([r.result for r in results], [0 + 0, 1 + 10, 2 + 20, 3 + 30, 4 + 40, 
                                                     5 + 50, 6 + 60, 7 + 70, 8 + 80, 9 + 90])
    
    def test_timeout(self):
        """Test timeout."""
        # Hàm mock với sleep
        def slow_func(sleep_time):
            time.sleep(sleep_time)
            return "done"
        
        # Tạo executor với timeout ngắn
        executor = SafeParallelExecutor(max_workers=2, timeout=0.1)
        
        # Tham số - một nhanh, một chậm
        params = [(0.01,), (0.5,)]
        
        # Thực thi
        results = executor.execute_many(slow_func, params)
        
        # Kiểm tra - task nhanh thành công, task chậm timeout
        self.assertEqual(len(results), 2)
        self.assertTrue(results[0].success)
        self.assertFalse(results[1].success)
        self.assertEqual(results[0].result, "done")
        self.assertIsInstance(results[1].error, TimeoutError)


class TestResourceMonitor(unittest.TestCase):
    """Test ResourceMonitor."""
    
    def setUp(self):
        """Thiết lập test."""
        # Mặc định không có psutil
        self.psutil_mock = MagicMock()
        self.psutil_patch = patch('src.deep_research_core.agents.resource_monitor.psutil', self.psutil_mock)
        self.psutil_patch.start()
    
    def tearDown(self):
        """Dọn dẹp sau test."""
        self.psutil_patch.stop()
    
    def test_initialization(self):
        """Test khởi tạo monitor."""
        monitor = ResourceMonitor(
            memory_threshold=70.0,
            cpu_threshold=80.0,
            disk_threshold=90.0,
            check_interval=30
        )
        
        self.assertEqual(monitor.memory_threshold, 70.0)
        self.assertEqual(monitor.cpu_threshold, 80.0)
        self.assertEqual(monitor.disk_threshold, 90.0)
        self.assertEqual(monitor.check_interval, 30)
        self.assertFalse(monitor.running)
    
    @patch('os.path.exists', return_value=True)
    @patch('builtins.open', return_value=MagicMock())
    def test_monitor_lifecycle(self, mock_open, mock_exists):
        """Test vòng đời của monitor."""
        # Chuẩn bị mocks cho kiểm tra tài nguyên dự phòng
        mock_file = mock_open.return_value.__enter__.return_value
        mock_file.read.return_value = (
            "MemTotal:        8000000 kB\n"
            "MemFree:         2000000 kB\n"
            "MemAvailable:    4000000 kB\n"
        )
        
        # Tạo monitor
        monitor = ResourceMonitor(check_interval=0.1)
        
        # Bắt đầu giám sát
        self.assertTrue(monitor.start_monitoring())
        self.assertTrue(monitor.running)
        
        # Thêm monitor object 
        class TestMonitor:
            def handle_resource_alert(self, resource_type, current, threshold):
                pass
        
        test_monitor = TestMonitor()
        self.assertTrue(monitor.register_monitor(test_monitor))
        
        # Chờ một chút để giám sát chạy
        time.sleep(0.2)
        
        # Dừng giám sát
        self.assertTrue(monitor.stop_monitoring())
        self.assertFalse(monitor.running)
        
        # Hủy đăng ký monitor
        self.assertTrue(monitor.unregister_monitor(test_monitor))
    
    def test_resource_usage(self):
        """Test lấy thông tin sử dụng tài nguyên."""
        # Chuẩn bị mocks cho psutil
        memory_mock = MagicMock()
        memory_mock.total = 8000000000
        memory_mock.available = 4000000000
        memory_mock.percent = 50.0
        memory_mock.used = 4000000000
        memory_mock.free = 4000000000
        
        disk_mock = MagicMock()
        disk_mock.total = 100000000000
        disk_mock.used = 70000000000
        disk_mock.free = 30000000000
        disk_mock.percent = 70.0
        
        self.psutil_mock.virtual_memory.return_value = memory_mock
        self.psutil_mock.disk_usage.return_value = disk_mock
        self.psutil_mock.cpu_percent.return_value = 30.0
        self.psutil_mock.cpu_count.return_value = 4
        
        # Tạo monitor
        monitor = ResourceMonitor()
        
        # Lấy thông tin sử dụng
        usage = monitor.get_resource_usage()
        
        # Kiểm tra kết quả
        self.assertIn("cpu", usage)
        self.assertIn("memory", usage)
        self.assertIn("disk", usage)
        self.assertEqual(usage["cpu"]["percent"], 30.0)
        self.assertEqual(usage["memory"]["percent"], 50.0)
        self.assertEqual(usage["disk"]["percent"], 70.0)
    
    def test_reset_history(self):
        """Test xóa lịch sử."""
        # Tạo monitor
        monitor = ResourceMonitor()
        
        # Thêm dữ liệu vào lịch sử
        monitor._add_to_history("cpu", time.time(), 50.0)
        monitor._add_to_history("memory", time.time(), 60.0)
        
        # Kiểm tra có dữ liệu
        self.assertTrue(len(monitor.history["cpu"]) > 0)
        self.assertTrue(len(monitor.history["memory"]) > 0)
        
        # Reset lịch sử
        monitor.reset_history()
        
        # Kiểm tra đã xóa
        self.assertEqual(len(monitor.history["cpu"]), 0)
        self.assertEqual(len(monitor.history["memory"]), 0)


if __name__ == '__main__':
    unittest.main() 