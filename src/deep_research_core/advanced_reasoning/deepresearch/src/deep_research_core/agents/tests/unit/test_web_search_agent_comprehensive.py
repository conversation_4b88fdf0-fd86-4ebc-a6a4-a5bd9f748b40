import unittest
import time
import os
import tempfile
from unittest.mock import patch, MagicMock
from typing import Dict, Any

from deep_research_core.agents.web_search_agent import WebSearchAgent
from deep_research_core.agents.caching import SmartWebSearchCache

class TestWebSearchAgentComprehensive(unittest.TestCase):
    """Kiểm thử toàn diện cho WebSearchAgent."""

    def setUp(self):
        """Thiết lập môi trường kiểm thử."""
        # Tạo thư mục cache tạm thời
        self.temp_dir = tempfile.TemporaryDirectory()
        self.cache_dir = self.temp_dir.name
        
        # Cấu hình WebSearchAgent cho kiểm thử
        self.config = {
            "cache_dir": self.cache_dir,
            "default_engine": "duckduckgo",
            "cache": {
                "max_size": 100,
                "default_ttl": 3600,
                "similarity_threshold": 0.8
            },
            "rate_limit": 10,
            "max_results": 5,
            "error_recovery": {
                "max_retries": 2,
                "backoff_factor": 0.5
            }
        }
        
        # Khởi tạo agent
        self.agent = WebSearchAgent(config=self.config, verbose=True)
    
    def tearDown(self):
        """Dọn dẹp sau kiểm thử."""
        self.temp_dir.cleanup()
    
    def test_initialization_comprehensive(self):
        """Kiểm tra khởi tạo WebSearchAgent toàn diện."""
        # Kiểm tra các thuộc tính cơ bản
        self.assertEqual(self.agent.default_engine, "duckduckgo")
        self.assertEqual(self.agent.max_results, 5)
        self.assertEqual(self.agent.rate_limit, 10)
        
        # Kiểm tra khởi tạo các dictionary
        self.assertIsInstance(self.agent.engine_request_counts, dict)
        self.assertIsInstance(self.agent.engine_reset_times, dict)
        self.assertIsInstance(self.agent.engine_rate_limits, dict)
        
        # Kiểm tra giá trị mặc định của rate limits
        self.assertEqual(self.agent.engine_rate_limits["duckduckgo"], 100)
        self.assertEqual(self.agent.engine_rate_limits["google"], 60)
        
        # Kiểm tra khởi tạo cache
        self.assertIsInstance(self.agent.cache, SmartWebSearchCache)
    
    def test_generate_cache_key(self):
        """Kiểm tra phương thức tạo khóa cache."""
        # Trường hợp bình thường
        key1 = self.agent._generate_cache_key("python programming", "google", 10, "en", {})
        self.assertIsInstance(key1, str)
        self.assertEqual(len(key1), 32)  # MD5 hash
        
        # Kiểm tra tính nhất quán
        key2 = self.agent._generate_cache_key("python programming", "google", 10, "en", {})
        self.assertEqual(key1, key2)
        
        # Kiểm tra tham số khác nhau
        key3 = self.agent._generate_cache_key("python programming", "bing", 10, "en", {})
        self.assertNotEqual(key1, key3)
        
        # Kiểm tra với tham số bổ sung
        key4 = self.agent._generate_cache_key("python programming", "google", 10, "en", {"site": "stackoverflow.com"})
        self.assertNotEqual(key1, key4)
        
        # Kiểm tra với kiểu dữ liệu không hợp lệ
        key5 = self.agent._generate_cache_key(123, "google", "abc", None, None)
        self.assertIsInstance(key5, str)
        self.assertEqual(len(key5), 32)
    
    def test_detect_language(self):
        """Kiểm tra phương thức phát hiện ngôn ngữ."""
        # Tiếng Anh
        self.assertEqual(self.agent._detect_language("This is English text"), "en")
        
        # Tiếng Việt
        self.assertEqual(self.agent._detect_language("Đây là văn bản tiếng Việt"), "vi")
        
        # Chuỗi trống
        self.assertEqual(self.agent._detect_language(""), "en")
        
        # Không phải chuỗi
        self.assertEqual(self.agent._detect_language(123), "en")
        
        # Với None
        self.assertEqual(self.agent._detect_language(None), "en")
    
    @patch('requests.get')
    def test_search_duckduckgo_html(self, mock_get):
        """Kiểm tra phương thức tìm kiếm DuckDuckGo HTML."""
        # Tạo phản hồi giả
        mock_response = MagicMock()
        mock_response.text = """
        <div class="result">
            <h2 class="result__title"><a href="https://example.com">Example Title</a></h2>
            <a class="result__url">https://example.com</a>
            <div class="result__snippet">Example snippet text</div>
        </div>
        """
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # Thực hiện tìm kiếm
        result = self.agent._search_duckduckgo_html("test query", 5)
        
        # Kiểm tra kết quả
        self.assertTrue(result["success"])
        self.assertEqual(result["engine"], "duckduckgo")
        self.assertGreaterEqual(len(result["results"]), 1)
        self.assertEqual(result["results"][0]["title"], "Example Title")
        self.assertEqual(result["results"][0]["url"], "https://example.com")
    
    @patch('requests.get')
    def test_search_duckduckgo_html_error(self, mock_get):
        """Kiểm tra xử lý lỗi trong phương thức tìm kiếm DuckDuckGo HTML."""
        # Tạo lỗi giả
        mock_get.side_effect = Exception("Connection error")
        
        # Thực hiện tìm kiếm
        result = self.agent._search_duckduckgo_html("test query", 5)
        
        # Kiểm tra kết quả lỗi
        self.assertFalse(result["success"])
        self.assertEqual(result["engine"], "duckduckgo")
        self.assertIn("error", result)
    
    @patch('deep_research_core.agents.web_search_agent.get_search_engine')
    def test_search_method(self, mock_get_search_engine):
        """Kiểm tra phương thức search chính."""
        # Tạo đối tượng engine giả
        mock_engine = MagicMock()
        mock_engine.search.return_value = {
            "success": True,
            "results": [
                {"title": "Result 1", "url": "https://example.com/1", "snippet": "Example 1"},
                {"title": "Result 2", "url": "https://example.com/2", "snippet": "Example 2"}
            ]
        }
        mock_get_search_engine.return_value = mock_engine
        
        # Thực hiện tìm kiếm
        result = self.agent.search("test query", "google", 5, "en")
        
        # Kiểm tra kết quả
        self.assertTrue(result["success"])
        self.assertIn("results", result)
        
        # Kiểm tra gọi method
        mock_get_search_engine.assert_called_once_with("google")
        mock_engine.search.assert_called_once()
    
    @patch('deep_research_core.agents.web_search_agent.get_search_engine')
    def test_search_invalid_input(self, mock_get_search_engine):
        """Kiểm tra xử lý đầu vào không hợp lệ."""
        # Tìm kiếm với query trống
        result = self.agent.search("", "google", 5, "en")
        self.assertFalse(result["success"])
        self.assertIn("error", result)
        
        # Tìm kiếm với None
        result = self.agent.search(None, "google", 5, "en")
        self.assertFalse(result["success"])
        self.assertIn("error", result)
        
        # Tìm kiếm với num_results không hợp lệ
        mock_engine = MagicMock()
        mock_engine.search.return_value = {"success": True, "results": []}
        mock_get_search_engine.return_value = mock_engine
        
        result = self.agent.search("test", "google", "invalid", "en")
        self.assertIn("search_params", mock_engine.search.call_args[1])
        self.assertEqual(mock_engine.search.call_args[1]["search_params"]["num_results"], 5)
    
    def test_check_rate_limit(self):
        """Kiểm tra phương thức kiểm tra giới hạn tỷ lệ."""
        # Đặt lại bộ đếm
        self.agent.request_count = 0
        self.agent.request_count_reset_time = time.time()
        self.agent.engine_request_counts = {}
        self.agent.engine_reset_times = {}
        
        # Kiểm tra giới hạn toàn cục
        for i in range(self.agent.rate_limit):
            self.assertTrue(self.agent._check_rate_limit())
        
        # Giới hạn đã đạt, nên trả về False
        self.assertFalse(self.agent._check_rate_limit())
        
        # Kiểm tra với engine cụ thể
        self.agent.request_count = 0  # Đặt lại bộ đếm toàn cục
        engine = "test_engine"
        self.agent.engine_rate_limits[engine] = 5
        
        for i in range(5):
            self.assertTrue(self.agent._check_rate_limit(engine=engine))
        
        # Giới hạn đã đạt, nên trả về False
        self.assertFalse(self.agent._check_rate_limit(engine=engine))
    
    def test_get_rotated_user_agent(self):
        """Kiểm tra phương thức xoay vòng User-Agent."""
        # Lấy User-Agent đầu tiên
        ua1 = self.agent._get_rotated_user_agent()
        self.assertIsInstance(ua1, str)
        
        # Lấy User-Agent tiếp theo (xoay vòng)
        ua2 = self.agent._get_rotated_user_agent()
        self.assertIsInstance(ua2, str)
        self.assertNotEqual(ua1, ua2)
        
        # Kiểm tra không xoay vòng
        ua3 = self.agent._get_rotated_user_agent(rotate=False)
        ua4 = self.agent._get_rotated_user_agent(rotate=False)
        self.assertEqual(ua3, ua4)
        
        # Kiểm tra xoay vòng đủ một chu kỳ
        original_index = self.agent.current_user_agent_index
        for _ in range(len(self.agent.user_agents)):
            self.agent._get_rotated_user_agent()
        
        self.assertEqual(original_index, self.agent.current_user_agent_index)

    @patch('deep_research_core.agents.crawlee_integration.crawl_and_extract')
    def test_crawl_and_extract(self, mock_crawl):
        """Kiểm tra phương thức crawl_and_extract."""
        # Tạo kết quả giả
        mock_crawl.return_value = {
            "success": True,
            "content": "Example content",
            "title": "Example Page"
        }
        
        # Thực hiện crawl
        result = self.agent.crawl_and_extract("https://example.com")
        
        # Kiểm tra kết quả
        self.assertTrue(result["success"])
        self.assertEqual(result["content"], "Example content")
        
        # Kiểm tra gọi method
        mock_crawl.assert_called_once()
    
    @patch('deep_research_core.agents.document_extractors.document_extractor.extract_text')
    @patch('deep_research_core.agents.web_search_agent.http_get')
    def test_extract_document_content(self, mock_http_get, mock_extract):
        """Kiểm tra phương thức extract_document_content."""
        # Tạo phản hồi giả
        mock_response = MagicMock()
        mock_response.headers = {"Content-Type": "application/pdf"}
        mock_response.raw = b"fake document content"
        mock_response.raise_for_status.return_value = None
        mock_http_get.return_value = mock_response
        
        # Tạo kết quả trích xuất giả
        mock_extract.return_value = {
            "success": True,
            "text": "Extracted text content",
            "metadata": {"pages": 5}
        }
        
        # Thực hiện trích xuất
        result = self.agent.extract_document_content("https://example.com/doc.pdf")
        
        # Kiểm tra kết quả
        self.assertTrue(result["success"])
        self.assertEqual(result["text"], "Extracted text content")
        self.assertIn("language", result)
        
        # Kiểm tra gọi method
        mock_http_get.assert_called_once()
        mock_extract.assert_called_once()

if __name__ == '__main__':
    unittest.main() 