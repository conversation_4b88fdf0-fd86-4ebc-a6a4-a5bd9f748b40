"""
Web Search Cache Memory Optimizer.

Module này cung cấp lớp WebSearchCacheMemoryOptimizer để tối ưu hóa
bộ nhớ cho WebSearchCacheEnhanced, đặc biệt khi xử lý nhiều truy vấn.
"""

import gc
import time
import psutil
import logging
import threading
from typing import Dict, Any, List, Optional, Union
import sys
import os

from ..utils.structured_logging import get_logger
from ..utils.cache_memory_manager import CacheMemoryManager

# Tạo logger
logger = get_logger(__name__)

class WebSearchCacheMemoryOptimizer:
    """
    Lớp tối ưu hóa bộ nhớ cho WebSearchCacheEnhanced.
    
    Lớp này kết nối WebSearchCacheEnhanced với CacheMemoryManager để 
    giám sát và tối ưu hóa việc sử dụng bộ nhớ cho cache tìm kiếm web.
    """
    
    def __init__(
        self,
        cache_instance,
        config: Optional[Dict[str, Any]] = None,
        auto_register: bool = True
    ):
        """
        Khởi tạo WebSearchCacheMemoryOptimizer.
        
        Args:
            cache_instance: Đối tượng WebSearchCacheEnhanced cần tối ưu hóa
            config: Cấu hình tùy chọn
            auto_register: Tự động đăng ký cache với CacheMemoryManager
        """
        self.cache_instance = cache_instance
        self.config = config or {}
        
        # Lấy instance của CacheMemoryManager
        memory_config = self.config.get("memory_management", {})
        memory_threshold = memory_config.get("memory_threshold", 80.0)
        critical_threshold = memory_config.get("critical_threshold", 90.0)
        check_interval = memory_config.get("check_interval", 60)
        disk_threshold = memory_config.get("disk_threshold", 85.0)
        
        self.memory_manager = CacheMemoryManager.get_instance(
            memory_threshold=memory_threshold,
            critical_threshold=critical_threshold,
            check_interval=check_interval,
            disk_threshold=disk_threshold
        )
        
        # Cấu hình quản lý bộ nhớ
        self.result_size_threshold = memory_config.get("result_size_threshold", 100 * 1024)  # 100KB
        self.max_memory_cache_entries = memory_config.get("max_memory_cache_entries", 100)
        self.max_memory_cache_size_mb = memory_config.get("max_memory_cache_size_mb", 100)  # 100MB
        self.cleanup_aggressive = memory_config.get("cleanup_aggressive", True)
        self.enable_auto_pruning = memory_config.get("enable_auto_pruning", True)
        
        # Theo dõi và kiểm soát bộ nhớ
        self.last_memory_check = time.time()
        self.memory_usage_history = []
        self.memory_check_interval = memory_config.get("memory_check_interval", 60)  # 60 giây
        
        # Cờ kiểm soát
        self.optimization_enabled = memory_config.get("optimization_enabled", True)
        self.low_memory_mode = False
        self.emergency_mode = False
        
        # Trạng thái và thống kê
        self.stats = {
            "optimizations_performed": 0,
            "items_pruned": 0,
            "memory_saved": 0,
            "emergency_cleanups": 0,
            "large_items_compressed": 0
        }
        
        # Patching WebSearchCacheEnhanced để thêm các methods
        self._patch_cache_methods()
        
        # Đăng ký với memory manager nếu cần
        if auto_register:
            self.memory_manager.register_cache(self.cache_instance)
            logger.info("Đã đăng ký cache với CacheMemoryManager")
    
    def _patch_cache_methods(self) -> None:
        """Patch các phương thức của WebSearchCacheEnhanced để thêm tối ưu hóa bộ nhớ."""
        cache = self.cache_instance
        
        # Lưu các phương thức gốc
        original_get = cache.get
        original_set = cache.set
        original_cleanup = getattr(cache, "perform_memory_cleanup", None)
        original_check_memory = getattr(cache, "check_memory_cleanup", None)
        
        # Patch phương thức get
        def optimized_get(key, allow_similar=False, context=None):
            # Ghi lại hoạt động truy cập cache
            if self.optimization_enabled:
                self.memory_manager.record_cache_access(cache, is_write=False)
                self._maybe_check_memory()
            
            # Gọi phương thức gốc
            result = original_get(key, allow_similar, context)
            
            # Tối ưu hóa kết quả nếu cần
            if result and self.optimization_enabled and self.low_memory_mode:
                result = self._optimize_result_if_needed(result)
            
            return result
        
        # Patch phương thức set
        def optimized_set(key, value, ttl=None):
            # Ghi lại hoạt động truy cập cache
            if self.optimization_enabled:
                self.memory_manager.record_cache_access(cache, is_write=True)
                self._maybe_check_memory()
            
            # Tối ưu hóa giá trị nếu cần
            if value and self.optimization_enabled:
                value = self._optimize_result_if_needed(value)
            
            # Nếu trong chế độ khẩn cấp, kiểm tra và giải phóng không gian nếu cần
            if self.emergency_mode and self.enable_auto_pruning:
                self._prune_cache_if_needed()
            
            # Gọi phương thức gốc
            return original_set(key, value, ttl)
        
        # Patch phương thức perform_memory_cleanup
        def optimized_memory_cleanup():
            start_time = time.time()
            
            # Nếu có phương thức gốc, gọi nó trước
            result = {}
            if original_cleanup:
                result = original_cleanup()
            
            # Thực hiện tối ưu hóa bộ nhớ bổ sung
            if self.optimization_enabled:
                optimization_result = self._perform_deep_optimization()
                result["optimization_result"] = optimization_result
            
            # Thu gom rác
            gc.collect()
            
            # Cập nhật trạng thái
            self.stats["optimizations_performed"] += 1
            self.last_memory_check = time.time()
            
            duration = time.time() - start_time
            logger.info(f"Tối ưu hóa bộ nhớ cache hoàn tất trong {duration:.2f}s")
            
            return result
        
        # Patch phương thức check_memory_cleanup
        def optimized_check_memory(force=False):
            if original_check_memory:
                original_check_memory(force)
            
            if self.optimization_enabled:
                now = time.time()
                if force or (now - self.last_memory_check > self.memory_check_interval):
                    check_result = self.memory_manager.check_memory_usage(force_cleanup=force)
                    
                    # Cập nhật chế độ
                    memory_percent = check_result.get("memory_percent", 0)
                    self.low_memory_mode = memory_percent > 70
                    self.emergency_mode = memory_percent > 85
                    
                    # Thực hiện pruning nếu cần
                    if self.emergency_mode and self.enable_auto_pruning:
                        self._prune_cache_if_needed()
                    
                    # Cập nhật thời gian kiểm tra
                    self.last_memory_check = now
                    
                    # Theo dõi lịch sử sử dụng bộ nhớ
                    self.memory_usage_history.append({
                        "timestamp": now,
                        "memory_percent": memory_percent,
                        "low_memory_mode": self.low_memory_mode,
                        "emergency_mode": self.emergency_mode
                    })
                    
                    # Giới hạn kích thước lịch sử
                    if len(self.memory_usage_history) > 100:
                        self.memory_usage_history = self.memory_usage_history[-100:]
        
        # Áp dụng các patches
        cache.get = optimized_get
        cache.set = optimized_set
        cache.perform_memory_cleanup = optimized_memory_cleanup
        cache.check_memory_cleanup = optimized_check_memory
        
        # Thêm phương thức mới
        cache.get_memory_optimizer = lambda: self
        cache.get_optimization_stats = lambda: self.stats
        
        logger.info("Đã patch các phương thức WebSearchCacheEnhanced với tối ưu hóa bộ nhớ")
    
    def _maybe_check_memory(self) -> None:
        """Kiểm tra bộ nhớ nếu đến lúc."""
        if not self.optimization_enabled:
            return
        
        now = time.time()
        if now - self.last_memory_check > self.memory_check_interval:
            self.memory_manager.check_memory_usage()
            self.last_memory_check = now
    
    def _optimize_result_if_needed(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Tối ưu hóa kết quả tìm kiếm nếu cần.
        
        Args:
            result: Kết quả tìm kiếm cần tối ưu hóa
            
        Returns:
            Kết quả đã tối ưu hóa
        """
        if not result or not isinstance(result, dict):
            return result
        
        # Nếu không có tối ưu hóa hoặc không có kết quả tìm kiếm
        if not self.optimization_enabled or "results" not in result:
            return result
        
        try:
            # Sao chép để không ảnh hưởng đến kết quả gốc
            optimized = result.copy()
            
            # Lấy danh sách kết quả
            results = optimized.get("results", [])
            
            # Tối ưu hóa từng kết quả tìm kiếm
            for item in results:
                if not isinstance(item, dict):
                    continue
                
                # Cắt ngắn snippet quá dài
                if "snippet" in item and isinstance(item["snippet"], str):
                    snippet = item["snippet"]
                    if len(snippet) > 1000:  # Giới hạn 1000 ký tự
                        item["snippet"] = snippet[:997] + "..."
                        item["snippet_truncated"] = True
                
                # Đánh dấu các trường lớn để nén
                for field in ["content", "html", "raw_html", "full_text"]:
                    if field in item and isinstance(item[field], str):
                        content = item[field]
                        if len(content) > self.result_size_threshold:
                            # Thêm metadata để WebSearchCacheEnhanced biết cần nén
                            item[f"{field}_needs_compression"] = True
                            
                            # Trong chế độ khẩn cấp, xóa luôn nội dung lớn
                            if self.emergency_mode:
                                item[field] = f"[REMOVED DUE TO MEMORY CONSTRAINTS - ORIGINAL SIZE: {len(content)} bytes]"
                                item[f"{field}_removed"] = True
                                self.stats["items_pruned"] += 1
            
            return optimized
        except Exception as e:
            logger.error(f"Lỗi khi tối ưu hóa kết quả: {str(e)}")
            return result
    
    def _prune_cache_if_needed(self) -> None:
        """Cắt giảm cache nếu cần thiết."""
        try:
            # Kiểm tra số lượng mục trong bộ nhớ cache
            if hasattr(self.cache_instance, "memory_cache"):
                memory_cache = getattr(self.cache_instance, "memory_cache", {})
                if len(memory_cache) > self.max_memory_cache_entries:
                    # Giữ lại 80% số mục (loại bỏ 20%)
                    entries_to_keep = int(self.max_memory_cache_entries * 0.8)
                    if entries_to_keep < len(memory_cache):
                        # Sắp xếp theo thời gian hết hạn
                        memory_cache_ttl = getattr(self.cache_instance, "memory_cache_ttl", {})
                        sorted_entries = sorted(
                            memory_cache_ttl.items(),
                            key=lambda x: x[1]  # Sắp xếp theo TTL
                        )
                        
                        # Xóa các mục cũ nhất
                        entries_to_remove = sorted_entries[:len(memory_cache) - entries_to_keep]
                        for key, _ in entries_to_remove:
                            if key in memory_cache:
                                del memory_cache[key]
                            if key in memory_cache_ttl:
                                del memory_cache_ttl[key]
                        
                        self.stats["items_pruned"] += len(entries_to_remove)
                        
                        logger.info(f"Pruned {len(entries_to_remove)} entries from memory cache")
        except Exception as e:
            logger.error(f"Lỗi khi thực hiện pruning: {str(e)}")
    
    def _perform_deep_optimization(self) -> Dict[str, Any]:
        """
        Thực hiện tối ưu hóa sâu cho cache.
        
        Returns:
            Dictionary chứa kết quả tối ưu hóa
        """
        result = {
            "timestamp": time.time(),
            "items_optimized": 0,
            "memory_before": None,
            "memory_after": None,
            "memory_saved_mb": 0
        }
        
        try:
            # Lấy thông tin bộ nhớ trước khi tối ưu hóa
            memory_before = self.memory_manager.get_memory_usage()
            result["memory_before"] = memory_before
            
            # Thực hiện tối ưu hóa meta-data của cache
            if hasattr(self.cache_instance, "metadata"):
                metadata = getattr(self.cache_instance, "metadata", {})
                
                # Loại bỏ các chỉ số thống kê cũ không cần thiết
                if "stats" in metadata:
                    stats = metadata["stats"]
                    # Giữ lại các thống kê tổng hợp, loại bỏ chi tiết
                    if "detailed_metrics" in stats:
                        del stats["detailed_metrics"]
                    
                    # Giới hạn kích thước lịch sử
                    if "history" in stats and isinstance(stats["history"], list):
                        if len(stats["history"]) > 100:
                            stats["history"] = stats["history"][-100:]
                
                # Tối ưu hóa danh sách entries nếu quá lớn
                if "entries" in metadata and isinstance(metadata["entries"], dict):
                    entries = metadata["entries"]
                    if len(entries) > 10000:
                        # Tìm và xóa các mục cũ nhất
                        sorted_entries = sorted(
                            [(k, v.get("expires_at", 0)) for k, v in entries.items()],
                            key=lambda x: x[1]
                        )
                        
                        # Xóa 20% các mục cũ nhất
                        entries_to_remove = sorted_entries[:int(len(entries) * 0.2)]
                        for key, _ in entries_to_remove:
                            if key in entries:
                                del entries[key]
                                # Xóa file tương ứng nếu có
                                try:
                                    if hasattr(self.cache_instance, "_get_cache_file_path"):
                                        file_path = self.cache_instance._get_cache_file_path(key)
                                        if os.path.exists(file_path):
                                            os.remove(file_path)
                                except Exception:
                                    pass
                        
                        result["items_optimized"] += len(entries_to_remove)
                
                # Lưu metadata đã tối ưu hóa
                if hasattr(self.cache_instance, "_save_metadata"):
                    self.cache_instance._save_metadata()
            
            # Thu gom rác để lấy lại bộ nhớ
            gc.collect()
            
            # Lấy thông tin bộ nhớ sau khi tối ưu hóa
            memory_after = self.memory_manager.get_memory_usage()
            result["memory_after"] = memory_after
            
            # Tính toán bộ nhớ đã tiết kiệm
            memory_saved = memory_before["process"]["rss"] - memory_after["process"]["rss"]
            result["memory_saved_mb"] = memory_saved / (1024 * 1024)
            
            # Cập nhật thống kê
            self.stats["memory_saved"] += memory_saved
            
            logger.info(
                f"Tối ưu hóa sâu hoàn tất: {result['items_optimized']} mục được tối ưu, "
                f"tiết kiệm {result['memory_saved_mb']:.2f}MB bộ nhớ"
            )
            
        except Exception as e:
            logger.error(f"Lỗi khi thực hiện tối ưu hóa sâu: {str(e)}")
            result["error"] = str(e)
        
        return result
    
    def get_optimization_status(self) -> Dict[str, Any]:
        """
        Lấy trạng thái tối ưu hóa hiện tại.
        
        Returns:
            Dictionary chứa trạng thái tối ưu hóa
        """
        memory_info = self.memory_manager.get_memory_usage()
        
        return {
            "timestamp": time.time(),
            "optimization_enabled": self.optimization_enabled,
            "low_memory_mode": self.low_memory_mode,
            "emergency_mode": self.emergency_mode,
            "current_memory_usage": {
                "percent": memory_info["system"]["percent"],
                "process_mb": memory_info["process"]["rss_mb"]
            },
            "stats": self.stats.copy(),
            "last_memory_check": self.last_memory_check,
            "memory_check_interval": self.memory_check_interval
        }
    
    def enable_optimization(self, enabled: bool = True) -> None:
        """
        Bật/tắt tối ưu hóa bộ nhớ.
        
        Args:
            enabled: True để bật, False để tắt
        """
        self.optimization_enabled = enabled
        logger.info(f"Tối ưu hóa bộ nhớ đã được {'bật' if enabled else 'tắt'}")
    
    def reset_stats(self) -> None:
        """Reset thống kê tối ưu hóa."""
        self.stats = {
            "optimizations_performed": 0,
            "items_pruned": 0,
            "memory_saved": 0,
            "emergency_cleanups": 0,
            "large_items_compressed": 0
        }
        logger.info("Đã reset thống kê tối ưu hóa bộ nhớ")
    
    def unregister(self) -> None:
        """Hủy đăng ký cache với CacheMemoryManager."""
        self.memory_manager.unregister_cache(self.cache_instance)
        logger.info("Đã hủy đăng ký cache với CacheMemoryManager") 