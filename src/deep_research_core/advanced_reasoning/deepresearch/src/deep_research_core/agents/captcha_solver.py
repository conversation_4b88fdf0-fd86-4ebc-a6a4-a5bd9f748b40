"""
Module xử lý CAPTCHA.

Module này cung cấp các công cụ phát hiện và giải quyết CAPTCHA 
từ các công cụ tìm kiếm và trang web.
"""

import logging
import base64
import time
import re
from typing import Dict, Any, Optional, Tuple, List, Union
from enum import Enum
import requests
from bs4 import BeautifulSoup

# Thiết lập logging
logger = logging.getLogger(__name__)

class CaptchaType(Enum):
    """Các loại CAPTCHA được hỗ trợ."""
    RECAPTCHA_V2 = "recaptcha_v2"
    RECAPTCHA_V3 = "recaptcha_v3"
    HCAPTCHA = "hcaptcha"
    TEXTCAPTCHA = "text_captcha"
    IMAGECAPTCHA = "image_captcha"
    CLOUDFLARE = "cloudflare"
    UNKNOWN = "unknown"

class CaptchaSolver:
    """Lớ<PERSON> cơ sở cho các giải pháp CAPTCHA."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Khởi tạo trình giải quyết CAPTCHA.
        
        Args:
            config: Cấu hình cho trình giải quyết
        """
        self.config = config or {}
        self._api_key = self.config.get("api_key", "")
        self._service_url = self.config.get("service_url", "")
        self._timeout = self.config.get("timeout", 30)
        
    def detect_captcha(self, html_content: str) -> Tuple[bool, CaptchaType, Dict[str, Any]]:
        """
        Phát hiện CAPTCHA trong nội dung HTML.
        
        Args:
            html_content: Nội dung HTML cần kiểm tra
            
        Returns:
            Tuple chứa (có_captcha, loại_captcha, thông_tin_thêm)
        """
        if not html_content:
            return False, CaptchaType.UNKNOWN, {}
        
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Kiểm tra reCAPTCHA
        recaptcha_divs = soup.find_all('div', class_=re.compile(r'g-recaptcha|recaptcha'))
        if recaptcha_divs:
            site_key = None
            for div in recaptcha_divs:
                site_key = div.get('data-sitekey')
                if site_key:
                    break
            
            return True, CaptchaType.RECAPTCHA_V2, {"site_key": site_key}
        
        # Kiểm tra hCAPTCHA
        hcaptcha_divs = soup.find_all('div', class_=re.compile(r'h-captcha|hcaptcha'))
        if hcaptcha_divs:
            site_key = None
            for div in hcaptcha_divs:
                site_key = div.get('data-sitekey')
                if site_key:
                    break
            
            return True, CaptchaType.HCAPTCHA, {"site_key": site_key}
        
        # Kiểm tra Cloudflare
        if 'cf-browser-verification' in html_content:
            return True, CaptchaType.CLOUDFLARE, {}
        
        # Kiểm tra CAPTCHA văn bản đơn giản
        text_inputs = soup.find_all('input', {'name': re.compile(r'captcha|securitycode', re.I)})
        captcha_images = soup.find_all('img', {'src': re.compile(r'captcha|security', re.I)})
        
        if text_inputs and captcha_images:
            image_url = None
            for img in captcha_images:
                if img.get('src'):
                    image_url = img.get('src')
                    break
            
            return True, CaptchaType.TEXTCAPTCHA, {"image_url": image_url}
        
        # Không tìm thấy CAPTCHA
        return False, CaptchaType.UNKNOWN, {}
    
    def solve(self, captcha_type: CaptchaType, captcha_data: Dict[str, Any], page_url: str) -> Dict[str, Any]:
        """
        Giải quyết CAPTCHA.
        
        Args:
            captcha_type: Loại CAPTCHA
            captcha_data: Dữ liệu CAPTCHA (site_key, image_url, v.v.)
            page_url: URL của trang chứa CAPTCHA
            
        Returns:
            Dictionary chứa kết quả giải quyết CAPTCHA
        """
        if not self._api_key:
            logger.warning("Không có API key cho dịch vụ giải quyết CAPTCHA")
            return {
                "success": False,
                "error": "No API key provided",
                "solution": None
            }
        
        logger.info(f"Đang giải quyết CAPTCHA loại {captcha_type.value} cho URL {page_url}")
        
        if captcha_type == CaptchaType.RECAPTCHA_V2:
            return self._solve_recaptcha_v2(captcha_data, page_url)
        elif captcha_type == CaptchaType.HCAPTCHA:
            return self._solve_hcaptcha(captcha_data, page_url)
        elif captcha_type == CaptchaType.TEXTCAPTCHA:
            return self._solve_text_captcha(captcha_data)
        elif captcha_type == CaptchaType.CLOUDFLARE:
            return self._solve_cloudflare(page_url)
        else:
            return {
                "success": False,
                "error": f"Unsupported CAPTCHA type: {captcha_type.value}",
                "solution": None
            }
    
    def _solve_recaptcha_v2(self, captcha_data: Dict[str, Any], page_url: str) -> Dict[str, Any]:
        """
        Giải quyết reCAPTCHA v2.
        
        Args:
            captcha_data: Dữ liệu CAPTCHA
            page_url: URL của trang
            
        Returns:
            Dictionary chứa kết quả
        """
        site_key = captcha_data.get("site_key")
        if not site_key:
            return {
                "success": False,
                "error": "No site key found for reCAPTCHA",
                "solution": None
            }
        
        # Giải mã giả (trong thực tế sẽ gọi API)
        logger.info("Đang kết nối với dịch vụ giải quyết CAPTCHA...")
        time.sleep(2)  # Mô phỏng thời gian giải quyết
        
        return {
            "success": True,
            "solution": {
                "type": "recaptcha_v2",
                "token": f"03AFY_a8{site_key[:10]}abcdefghijklmnopqrstuvwxyz",
                "challenge_ts": time.time()
            }
        }
    
    def _solve_hcaptcha(self, captcha_data: Dict[str, Any], page_url: str) -> Dict[str, Any]:
        """
        Giải quyết hCAPTCHA.
        
        Args:
            captcha_data: Dữ liệu CAPTCHA
            page_url: URL của trang
            
        Returns:
            Dictionary chứa kết quả
        """
        site_key = captcha_data.get("site_key")
        if not site_key:
            return {
                "success": False,
                "error": "No site key found for hCAPTCHA",
                "solution": None
            }
        
        # Giải mã giả (trong thực tế sẽ gọi API)
        logger.info("Đang kết nối với dịch vụ giải quyết CAPTCHA...")
        time.sleep(2)  # Mô phỏng thời gian giải quyết
        
        return {
            "success": True,
            "solution": {
                "type": "hcaptcha",
                "token": f"P0_{site_key[:8]}abcdefghijklmnopqrstuvwxyz",
                "challenge_ts": time.time()
            }
        }
    
    def _solve_text_captcha(self, captcha_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Giải quyết CAPTCHA văn bản/hình ảnh.
        
        Args:
            captcha_data: Dữ liệu CAPTCHA
            
        Returns:
            Dictionary chứa kết quả
        """
        image_url = captcha_data.get("image_url")
        if not image_url:
            return {
                "success": False,
                "error": "No image URL found for text CAPTCHA",
                "solution": None
            }
        
        # Giải mã giả (trong thực tế sẽ tải ảnh và gọi API OCR)
        logger.info(f"Đang xử lý hình ảnh CAPTCHA từ {image_url}...")
        time.sleep(1)  # Mô phỏng thời gian giải quyết
        
        # Tạo một kết quả giả định cho mục đích demo
        return {
            "success": True,
            "solution": {
                "type": "text",
                "text": "AB123X",
                "confidence": 0.95
            }
        }
    
    def _solve_cloudflare(self, page_url: str) -> Dict[str, Any]:
        """
        Xử lý thách thức Cloudflare.
        
        Args:
            page_url: URL của trang
            
        Returns:
            Dictionary chứa kết quả
        """
        logger.info(f"Đang xử lý Cloudflare challenge cho {page_url}...")
        time.sleep(3)  # Mô phỏng thời gian xử lý
        
        return {
            "success": False,
            "error": "Cloudflare challenges are not directly supported. Consider using a specialized browser/proxy.",
            "solution": None
        }
    
class CaptchaHandler:
    """
    Trình xử lý CAPTCHA tích hợp với WebSearchAgent.
    
    Lớp này phát hiện và xử lý CAPTCHA trong quá trình tìm kiếm web.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, solver: Optional[CaptchaSolver] = None):
        """
        Khởi tạo trình xử lý CAPTCHA.
        
        Args:
            config: Cấu hình cho trình xử lý
            solver: Trình giải quyết CAPTCHA tùy chỉnh
        """
        self.config = config or {}
        self.solver = solver or CaptchaSolver(self.config.get("solver_config"))
        
        self.captcha_detectons = {}  # Lưu trữ thông tin về các CAPTCHA đã phát hiện
        self.captcha_solves = {}  # Lưu trữ thông tin về các CAPTCHA đã giải quyết
        
        # Cấu hình
        self.auto_solve = self.config.get("auto_solve", False)
        self.detection_threshold = self.config.get("detection_threshold", 0.7)
        self.max_retries = self.config.get("max_retries", 3)
        self.retry_delay = self.config.get("retry_delay", 5)
        
        logger.info(f"CaptchaHandler được khởi tạo với auto_solve={self.auto_solve}")
    
    def check_and_handle_captcha(self, response: requests.Response, search_engine: str) -> Tuple[bool, Dict[str, Any]]:
        """
        Kiểm tra và xử lý CAPTCHA từ phản hồi HTTP.
        
        Args:
            response: Phản hồi HTTP
            search_engine: Tên công cụ tìm kiếm
            
        Returns:
            Tuple chứa (captcha_detected, solution_info)
        """
        if not response or not hasattr(response, 'text'):
            return False, {}
        
        html_content = response.text
        has_captcha, captcha_type, captcha_data = self.solver.detect_captcha(html_content)
        
        if not has_captcha:
            return False, {}
        
        page_url = response.url
        logger.warning(f"Phát hiện CAPTCHA loại {captcha_type.value} tại {page_url} từ {search_engine}")
        
        # Lưu thông tin phát hiện
        detection_key = f"{search_engine}:{page_url}"
        self.captcha_detectons[detection_key] = {
            "timestamp": time.time(),
            "captcha_type": captcha_type.value,
            "search_engine": search_engine,
            "url": page_url,
            "data": captcha_data
        }
        
        # Giải quyết tự động nếu được cấu hình
        if self.auto_solve:
            logger.info(f"Đang tự động giải quyết CAPTCHA cho {search_engine}")
            solution = self.solver.solve(captcha_type, captcha_data, page_url)
            
            if solution.get("success", False):
                logger.info(f"Giải quyết CAPTCHA thành công cho {search_engine}")
                
                # Lưu thông tin giải quyết
                self.captcha_solves[detection_key] = {
                    "timestamp": time.time(),
                    "solution": solution.get("solution"),
                    "search_engine": search_engine
                }
                
                return True, solution
            else:
                logger.warning(f"Không thể giải quyết CAPTCHA: {solution.get('error', 'Unknown error')}")
        
        return True, {"captcha_type": captcha_type.value, "data": captcha_data}
    
    def get_captcha_statistics(self) -> Dict[str, Any]:
        """
        Lấy thống kê về CAPTCHA.
        
        Returns:
            Dictionary chứa thống kê
        """
        stats = {
            "total_detections": len(self.captcha_detectons),
            "total_solutions": len(self.captcha_solves),
            "by_engine": {},
            "by_type": {}
        }
        
        # Thống kê theo công cụ tìm kiếm
        for detection in self.captcha_detectons.values():
            engine = detection.get("search_engine", "unknown")
            if engine not in stats["by_engine"]:
                stats["by_engine"][engine] = {"count": 0, "solved": 0}
            
            stats["by_engine"][engine]["count"] += 1
            
            # Kiểm tra xem đã giải quyết chưa
            detection_key = f"{engine}:{detection.get('url', '')}"
            if detection_key in self.captcha_solves:
                stats["by_engine"][engine]["solved"] += 1
        
        # Thống kê theo loại CAPTCHA
        for detection in self.captcha_detectons.values():
            captcha_type = detection.get("captcha_type", "unknown")
            if captcha_type not in stats["by_type"]:
                stats["by_type"][captcha_type] = {"count": 0, "solved": 0}
            
            stats["by_type"][captcha_type]["count"] += 1
            
            # Kiểm tra xem đã giải quyết chưa
            engine = detection.get("search_engine", "unknown")
            detection_key = f"{engine}:{detection.get('url', '')}"
            if detection_key in self.captcha_solves:
                stats["by_type"][captcha_type]["solved"] += 1
        
        return stats
    
    def clear_history(self, older_than_seconds: Optional[int] = None) -> int:
        """
        Xóa lịch sử CAPTCHA.
        
        Args:
            older_than_seconds: Chỉ xóa các mục cũ hơn số giây này
            
        Returns:
            Số mục đã xóa
        """
        if older_than_seconds is None:
            # Xóa tất cả
            total = len(self.captcha_detectons)
            self.captcha_detectons.clear()
            self.captcha_solves.clear()
            return total
        
        # Xóa dựa trên tuổi
        current_time = time.time()
        to_remove_detections = []
        to_remove_solutions = []
        
        # Kiểm tra các mục phát hiện
        for key, detection in self.captcha_detectons.items():
            timestamp = detection.get("timestamp", 0)
            if current_time - timestamp > older_than_seconds:
                to_remove_detections.append(key)
        
        # Kiểm tra các mục giải quyết
        for key, solution in self.captcha_solves.items():
            timestamp = solution.get("timestamp", 0)
            if current_time - timestamp > older_than_seconds:
                to_remove_solutions.append(key)
        
        # Xóa các mục
        for key in to_remove_detections:
            self.captcha_detectons.pop(key, None)
        
        for key in to_remove_solutions:
            self.captcha_solves.pop(key, None)
        
        return len(to_remove_detections) + len(to_remove_solutions)
