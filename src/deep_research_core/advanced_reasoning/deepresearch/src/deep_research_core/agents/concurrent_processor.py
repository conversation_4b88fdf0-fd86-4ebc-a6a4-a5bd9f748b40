#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Concurrent Processor - Xử lý đồng thời cho WebSearchAgent.

Module này cung cấp các công cụ xử lý đồng thời để tối ưu hóa hiệu suất:
1. Batch Processor: <PERSON><PERSON> lý hàng loạt các yêu cầu
2. Parallel Executor: <PERSON><PERSON><PERSON><PERSON> thi song song các tác vụ
3. Rate Limiter: Giới hạn tốc độ thực thi
"""

import time
import logging
import threading
import queue
from typing import Dict, Any, List, Optional, Union, Tuple, Callable, TypeVar, Generic
import concurrent.futures
from datetime import datetime, timedelta

# Tạo logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# <PERSON><PERSON><PERSON> nghĩa kiểu dữ liệu
T = TypeVar('T')  # Input type
R = TypeVar('R')  # Result type

class BatchProcessor(Generic[T, R]):
    """
    Batch Processor - Xử lý hàng loạt các yêu cầu.
    
    Tính năng:
    - Gom nhóm các yêu cầu để xử lý hàng loạt
    - Tự động xử lý khi đạt kích thước batch hoặc hết thời gian chờ
    - Hỗ trợ callback để xử lý kết quả
    """
    
    def __init__(
        self,
        batch_function: Callable[[List[T]], List[R]],
        max_batch_size: int = 10,
        max_wait_time: float = 0.5,
        on_result_callback: Optional[Callable[[T, R], None]] = None
    ):
        """
        Khởi tạo BatchProcessor.
        
        Args:
            batch_function: Hàm xử lý hàng loạt
            max_batch_size: Kích thước tối đa của batch
            max_wait_time: Thời gian chờ tối đa (giây)
            on_result_callback: Callback khi có kết quả
        """
        self.batch_function = batch_function
        self.max_batch_size = max_batch_size
        self.max_wait_time = max_wait_time
        self.on_result_callback = on_result_callback
        
        self.queue = queue.Queue()
        self.results = {}
        self.batch_lock = threading.RLock()
        self.next_id = 0
        self.id_lock = threading.RLock()
        
        self.stop_event = threading.Event()
        self.worker_thread = threading.Thread(target=self._worker, daemon=True)
        self.worker_thread.start()
        
        self.stats = {
            "total_requests": 0,
            "total_batches": 0,
            "avg_batch_size": 0,
            "max_batch_size_reached": 0,
            "timeout_triggered": 0
        }
    
    def _get_next_id(self) -> int:
        """
        Lấy ID tiếp theo.
        
        Returns:
            int: ID tiếp theo
        """
        with self.id_lock:
            id = self.next_id
            self.next_id += 1
            return id
    
    def process(self, item: T, timeout: Optional[float] = None) -> R:
        """
        Xử lý một mục.
        
        Args:
            item: Mục cần xử lý
            timeout: Thời gian chờ tối đa (giây)
            
        Returns:
            R: Kết quả xử lý
            
        Raises:
            TimeoutError: Nếu quá thời gian chờ
            RuntimeError: Nếu worker đã dừng
        """
        if self.stop_event.is_set():
            raise RuntimeError("BatchProcessor đã dừng")
        
        # Tạo ID cho mục
        item_id = self._get_next_id()
        
        # Tạo event để thông báo khi có kết quả
        result_event = threading.Event()
        
        # Đưa vào queue
        self.queue.put((item_id, item, result_event))
        
        with self.batch_lock:
            self.stats["total_requests"] += 1
        
        # Chờ kết quả
        if not result_event.wait(timeout):
            raise TimeoutError("Quá thời gian chờ kết quả")
        
        # Lấy kết quả
        with self.batch_lock:
            result = self.results.pop(item_id, None)
        
        if result is None:
            raise RuntimeError("Không tìm thấy kết quả")
        
        return result
    
    def process_async(self, item: T, callback: Callable[[R], None]) -> None:
        """
        Xử lý một mục bất đồng bộ.
        
        Args:
            item: Mục cần xử lý
            callback: Hàm callback khi có kết quả
            
        Raises:
            RuntimeError: Nếu worker đã dừng
        """
        if self.stop_event.is_set():
            raise RuntimeError("BatchProcessor đã dừng")
        
        # Tạo ID cho mục
        item_id = self._get_next_id()
        
        # Tạo callback wrapper
        def callback_wrapper(event):
            # Chờ kết quả
            event.wait()
            
            # Lấy kết quả
            with self.batch_lock:
                result = self.results.pop(item_id, None)
            
            if result is not None:
                callback(result)
        
        # Tạo event để thông báo khi có kết quả
        result_event = threading.Event()
        
        # Tạo thread để chờ kết quả
        callback_thread = threading.Thread(
            target=callback_wrapper,
            args=(result_event,),
            daemon=True
        )
        callback_thread.start()
        
        # Đưa vào queue
        self.queue.put((item_id, item, result_event))
        
        with self.batch_lock:
            self.stats["total_requests"] += 1
    
    def _worker(self) -> None:
        """Worker thread để xử lý các batch."""
        batch = []  # [(id, item, event)]
        last_process_time = time.time()
        
        while not self.stop_event.is_set():
            try:
                # Chờ mục tiếp theo với timeout
                try:
                    item_data = self.queue.get(timeout=self.max_wait_time)
                    batch.append(item_data)
                    self.queue.task_done()
                except queue.Empty:
                    # Timeout, xử lý batch hiện tại nếu có
                    if batch and time.time() - last_process_time >= self.max_wait_time:
                        with self.batch_lock:
                            self.stats["timeout_triggered"] += 1
                        self._process_batch(batch)
                        batch = []
                        last_process_time = time.time()
                    continue
                
                # Xử lý batch nếu đạt kích thước tối đa
                if len(batch) >= self.max_batch_size:
                    with self.batch_lock:
                        self.stats["max_batch_size_reached"] += 1
                    self._process_batch(batch)
                    batch = []
                    last_process_time = time.time()
            except Exception as e:
                logger.error(f"Lỗi trong worker thread: {str(e)}")
        
        # Xử lý batch còn lại khi dừng
        if batch:
            try:
                self._process_batch(batch)
            except Exception as e:
                logger.error(f"Lỗi khi xử lý batch cuối cùng: {str(e)}")
    
    def _process_batch(self, batch: List[Tuple[int, T, threading.Event]]) -> None:
        """
        Xử lý một batch.
        
        Args:
            batch: Danh sách các mục cần xử lý [(id, item, event)]
        """
        if not batch:
            return
        
        # Tách dữ liệu
        ids, items, events = zip(*batch)
        
        try:
            # Gọi hàm xử lý batch
            results = self.batch_function(list(items))
            
            # Lưu kết quả
            with self.batch_lock:
                for i, result in enumerate(results):
                    item_id = ids[i]
                    item = items[i]
                    self.results[item_id] = result
                    
                    # Gọi callback nếu có
                    if self.on_result_callback:
                        try:
                            self.on_result_callback(item, result)
                        except Exception as e:
                            logger.error(f"Lỗi trong callback: {str(e)}")
                
                # Cập nhật thống kê
                self.stats["total_batches"] += 1
                self.stats["avg_batch_size"] = (
                    (self.stats["avg_batch_size"] * (self.stats["total_batches"] - 1) + len(batch))
                    / self.stats["total_batches"]
                )
            
            # Thông báo cho tất cả các event
            for event in events:
                event.set()
        except Exception as e:
            logger.error(f"Lỗi khi xử lý batch: {str(e)}")
            
            # Thông báo lỗi cho tất cả các event
            for item_id, event in zip(ids, events):
                with self.batch_lock:
                    self.results[item_id] = None
                event.set()
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Lấy thống kê.
        
        Returns:
            Dict[str, Any]: Thống kê
        """
        with self.batch_lock:
            return self.stats.copy()
    
    def shutdown(self) -> None:
        """Dừng worker thread."""
        self.stop_event.set()
        if hasattr(self, "worker_thread") and self.worker_thread.is_alive():
            self.worker_thread.join(timeout=5)


class ParallelExecutor:
    """
    Parallel Executor - Thực thi song song các tác vụ.
    
    Tính năng:
    - Thực thi song song các tác vụ với ThreadPoolExecutor
    - Tự động điều chỉnh số lượng worker dựa trên tải
    - Hỗ trợ timeout và retry
    """
    
    def __init__(
        self,
        max_workers: Optional[int] = None,
        thread_name_prefix: str = "parallel",
        auto_adjust: bool = True
    ):
        """
        Khởi tạo ParallelExecutor.
        
        Args:
            max_workers: Số lượng worker tối đa
            thread_name_prefix: Tiền tố tên thread
            auto_adjust: Tự động điều chỉnh số lượng worker
        """
        self.max_workers = max_workers or min(32, (os.cpu_count() or 1) * 5)
        self.thread_name_prefix = thread_name_prefix
        self.auto_adjust = auto_adjust
        
        self.executor = concurrent.futures.ThreadPoolExecutor(
            max_workers=self.max_workers,
            thread_name_prefix=self.thread_name_prefix
        )
        
        self.stats = {
            "total_tasks": 0,
            "successful_tasks": 0,
            "failed_tasks": 0,
            "retried_tasks": 0,
            "timed_out_tasks": 0,
            "avg_execution_time": 0
        }
        self.stats_lock = threading.RLock()
        
        # Theo dõi tải
        self.active_tasks = 0
        self.active_tasks_lock = threading.RLock()
    
    def execute(
        self,
        func: Callable[..., R],
        *args,
        timeout: Optional[float] = None,
        retry_count: int = 0,
        retry_delay: float = 1.0,
        **kwargs
    ) -> R:
        """
        Thực thi một tác vụ.
        
        Args:
            func: Hàm cần thực thi
            *args: Tham số vị trí
            timeout: Thời gian chờ tối đa (giây)
            retry_count: Số lần thử lại
            retry_delay: Thời gian chờ giữa các lần thử lại (giây)
            **kwargs: Tham số từ khóa
            
        Returns:
            R: Kết quả thực thi
            
        Raises:
            TimeoutError: Nếu quá thời gian chờ
            Exception: Nếu có lỗi trong quá trình thực thi
        """
        with self.active_tasks_lock:
            self.active_tasks += 1
        
        with self.stats_lock:
            self.stats["total_tasks"] += 1
        
        start_time = time.time()
        
        try:
            # Thực thi với retry
            retry = 0
            last_exception = None
            
            while retry <= retry_count:
                try:
                    # Thực thi tác vụ
                    future = self.executor.submit(func, *args, **kwargs)
                    
                    # Chờ kết quả với timeout
                    if timeout is not None:
                        result = future.result(timeout=timeout)
                    else:
                        result = future.result()
                    
                    # Cập nhật thống kê
                    with self.stats_lock:
                        self.stats["successful_tasks"] += 1
                        execution_time = time.time() - start_time
                        self.stats["avg_execution_time"] = (
                            (self.stats["avg_execution_time"] * (self.stats["successful_tasks"] - 1) + execution_time)
                            / self.stats["successful_tasks"]
                        )
                    
                    return result
                except concurrent.futures.TimeoutError:
                    # Hủy tác vụ nếu timeout
                    future.cancel()
                    
                    with self.stats_lock:
                        self.stats["timed_out_tasks"] += 1
                    
                    raise TimeoutError(f"Quá thời gian chờ ({timeout}s)")
                except Exception as e:
                    last_exception = e
                    
                    # Thử lại nếu chưa đạt số lần tối đa
                    if retry < retry_count:
                        retry += 1
                        
                        with self.stats_lock:
                            self.stats["retried_tasks"] += 1
                        
                        # Đợi trước khi thử lại
                        time.sleep(retry_delay)
                    else:
                        with self.stats_lock:
                            self.stats["failed_tasks"] += 1
                        
                        raise
            
            # Không bao giờ đến đây
            raise last_exception or RuntimeError("Lỗi không xác định")
        finally:
            with self.active_tasks_lock:
                self.active_tasks -= 1
    
    def map(
        self,
        func: Callable[[T], R],
        items: List[T],
        timeout: Optional[float] = None,
        chunksize: int = 1
    ) -> List[R]:
        """
        Áp dụng hàm cho mỗi mục trong danh sách.
        
        Args:
            func: Hàm cần áp dụng
            items: Danh sách các mục
            timeout: Thời gian chờ tối đa (giây)
            chunksize: Kích thước chunk
            
        Returns:
            List[R]: Danh sách kết quả
            
        Raises:
            TimeoutError: Nếu quá thời gian chờ
            Exception: Nếu có lỗi trong quá trình thực thi
        """
        with self.active_tasks_lock:
            self.active_tasks += len(items)
        
        with self.stats_lock:
            self.stats["total_tasks"] += len(items)
        
        start_time = time.time()
        
        try:
            # Thực thi map
            if timeout is not None:
                results = list(self.executor.map(func, items, timeout=timeout, chunksize=chunksize))
            else:
                results = list(self.executor.map(func, items, chunksize=chunksize))
            
            # Cập nhật thống kê
            with self.stats_lock:
                self.stats["successful_tasks"] += len(items)
                execution_time = time.time() - start_time
                self.stats["avg_execution_time"] = (
                    (self.stats["avg_execution_time"] * (self.stats["successful_tasks"] - len(items)) + execution_time)
                    / self.stats["successful_tasks"]
                )
            
            return results
        except concurrent.futures.TimeoutError:
            with self.stats_lock:
                self.stats["timed_out_tasks"] += len(items)
            
            raise TimeoutError(f"Quá thời gian chờ ({timeout}s)")
        except Exception as e:
            with self.stats_lock:
                self.stats["failed_tasks"] += len(items)
            
            raise
        finally:
            with self.active_tasks_lock:
                self.active_tasks -= len(items)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Lấy thống kê.
        
        Returns:
            Dict[str, Any]: Thống kê
        """
        with self.stats_lock:
            stats = self.stats.copy()
        
        with self.active_tasks_lock:
            stats["active_tasks"] = self.active_tasks
        
        return stats
    
    def shutdown(self, wait: bool = True) -> None:
        """
        Dừng executor.
        
        Args:
            wait: Chờ các tác vụ hoàn thành
        """
        self.executor.shutdown(wait=wait)


import os

class RateLimiter:
    """
    Rate Limiter - Giới hạn tốc độ thực thi.
    
    Tính năng:
    - Giới hạn số lượng yêu cầu trong một khoảng thời gian
    - Hỗ trợ nhiều loại giới hạn (giây, phút, giờ)
    - Tự động chờ nếu vượt quá giới hạn
    """
    
    def __init__(
        self,
        requests_per_second: Optional[int] = None,
        requests_per_minute: Optional[int] = None,
        requests_per_hour: Optional[int] = None
    ):
        """
        Khởi tạo RateLimiter.
        
        Args:
            requests_per_second: Số yêu cầu tối đa mỗi giây
            requests_per_minute: Số yêu cầu tối đa mỗi phút
            requests_per_hour: Số yêu cầu tối đa mỗi giờ
        """
        self.requests_per_second = requests_per_second
        self.requests_per_minute = requests_per_minute
        self.requests_per_hour = requests_per_hour
        
        self.request_times = []
        self.lock = threading.RLock()
    
    def acquire(self, block: bool = True) -> bool:
        """
        Yêu cầu một token.
        
        Args:
            block: Chờ nếu không có token
            
        Returns:
            bool: True nếu lấy được token, False nếu không
        """
        with self.lock:
            now = datetime.now()
            
            # Xóa các yêu cầu cũ
            self._clean_old_requests(now)
            
            # Kiểm tra giới hạn
            if self._check_limits(now):
                # Thêm yêu cầu mới
                self.request_times.append(now)
                return True
            elif not block:
                return False
            
            # Tính thời gian chờ
            wait_time = self._calculate_wait_time(now)
            
            # Chờ
            time.sleep(wait_time.total_seconds())
            
            # Thử lại
            return self.acquire(block=False)
    
    def _clean_old_requests(self, now: datetime) -> None:
        """
        Xóa các yêu cầu cũ.
        
        Args:
            now: Thời gian hiện tại
        """
        # Tính thời gian cũ nhất cần giữ lại
        oldest_time = now
        
        if self.requests_per_hour is not None:
            oldest_time = min(oldest_time, now - timedelta(hours=1))
        
        if self.requests_per_minute is not None:
            oldest_time = min(oldest_time, now - timedelta(minutes=1))
        
        if self.requests_per_second is not None:
            oldest_time = min(oldest_time, now - timedelta(seconds=1))
        
        # Xóa các yêu cầu cũ hơn
        self.request_times = [t for t in self.request_times if t >= oldest_time]
    
    def _check_limits(self, now: datetime) -> bool:
        """
        Kiểm tra giới hạn.
        
        Args:
            now: Thời gian hiện tại
            
        Returns:
            bool: True nếu chưa vượt quá giới hạn, False nếu đã vượt quá
        """
        # Kiểm tra giới hạn theo giây
        if self.requests_per_second is not None:
            second_ago = now - timedelta(seconds=1)
            requests_in_second = sum(1 for t in self.request_times if t >= second_ago)
            
            if requests_in_second >= self.requests_per_second:
                return False
        
        # Kiểm tra giới hạn theo phút
        if self.requests_per_minute is not None:
            minute_ago = now - timedelta(minutes=1)
            requests_in_minute = sum(1 for t in self.request_times if t >= minute_ago)
            
            if requests_in_minute >= self.requests_per_minute:
                return False
        
        # Kiểm tra giới hạn theo giờ
        if self.requests_per_hour is not None:
            hour_ago = now - timedelta(hours=1)
            requests_in_hour = sum(1 for t in self.request_times if t >= hour_ago)
            
            if requests_in_hour >= self.requests_per_hour:
                return False
        
        return True
    
    def _calculate_wait_time(self, now: datetime) -> timedelta:
        """
        Tính thời gian chờ.
        
        Args:
            now: Thời gian hiện tại
            
        Returns:
            timedelta: Thời gian chờ
        """
        wait_times = []
        
        # Tính thời gian chờ theo giây
        if self.requests_per_second is not None:
            second_ago = now - timedelta(seconds=1)
            requests_in_second = [t for t in self.request_times if t >= second_ago]
            
            if len(requests_in_second) >= self.requests_per_second:
                # Chờ đến khi yêu cầu cũ nhất hết hạn
                oldest = min(requests_in_second)
                wait_times.append(oldest + timedelta(seconds=1) - now)
        
        # Tính thời gian chờ theo phút
        if self.requests_per_minute is not None:
            minute_ago = now - timedelta(minutes=1)
            requests_in_minute = [t for t in self.request_times if t >= minute_ago]
            
            if len(requests_in_minute) >= self.requests_per_minute:
                # Chờ đến khi yêu cầu cũ nhất hết hạn
                oldest = min(requests_in_minute)
                wait_times.append(oldest + timedelta(minutes=1) - now)
        
        # Tính thời gian chờ theo giờ
        if self.requests_per_hour is not None:
            hour_ago = now - timedelta(hours=1)
            requests_in_hour = [t for t in self.request_times if t >= hour_ago]
            
            if len(requests_in_hour) >= self.requests_per_hour:
                # Chờ đến khi yêu cầu cũ nhất hết hạn
                oldest = min(requests_in_hour)
                wait_times.append(oldest + timedelta(hours=1) - now)
        
        if not wait_times:
            return timedelta(seconds=0)
        
        return max(wait_times)
