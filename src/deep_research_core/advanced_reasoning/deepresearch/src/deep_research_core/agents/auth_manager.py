"""
Module for managing authentication in web crawling.

This module provides functionality for detecting login forms, storing credentials,
and handling authentication for web crawling.
"""

import os
import json
import time
import re
import base64
import hashlib
import logging
from typing import Dict, Any, Optional, List, Tuple, Union
from urllib.parse import urlparse, urljoin
import keyring

# Create logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AuthManager:
    """
    Authentication manager for web crawling.

    Features:
    - Detect login forms on web pages
    - Store and retrieve credentials securely
    - Handle authentication for web crawling
    - Manage cookies for authenticated sessions
    """

    def __init__(
        self,
        credentials_file: Optional[str] = None,
        use_keyring: bool = True,
        keyring_service_name: str = "deep_research_core",
        cookie_file: Optional[str] = None,
        auto_detect_login: bool = True,
        auto_login: bool = False,
        require_confirmation: bool = True
    ):
        """
        Initialize the AuthManager.

        Args:
            credentials_file: Path to file for storing credentials (encrypted)
            use_keyring: Whether to use system keyring for storing passwords
            keyring_service_name: Service name for keyring
            cookie_file: Path to file for storing cookies
            auto_detect_login: Whether to automatically detect login forms
            auto_login: Whether to automatically attempt login
            require_confirmation: Whether to require confirmation before login
        """
        self.credentials_file = credentials_file
        self.use_keyring = use_keyring
        self.keyring_service_name = keyring_service_name
        self.cookie_file = cookie_file
        self.auto_detect_login = auto_detect_login
        self.auto_login = auto_login
        self.require_confirmation = require_confirmation

        # Initialize storage
        self.credentials = {}  # {domain: {username, password_hash, last_used}}
        self.cookies = {}  # {domain: {cookies_dict, expiry}}
        self.detected_forms = {}  # {url: {form_info}}
        self.login_attempts = {}  # {domain: {success_count, fail_count, last_attempt}}

        # Load stored data
        self._load_credentials()
        self._load_cookies()

    def detect_login_form(self, url: str, html_content: str) -> Optional[Dict[str, Any]]:
        """
        Detect login form on a web page.

        Args:
            url: URL of the page
            html_content: HTML content of the page

        Returns:
            Dictionary with login form information or None
        """
        if not self.auto_detect_login:
            return None

        # Simple login form detection
        login_patterns = [
            (r'<form[^>]*login[^>]*>', r'login form'),
            (r'<form[^>]*signin[^>]*>', r'signin form'),
            (r'<form[^>]*auth[^>]*>', r'auth form'),
            (r'<input[^>]*password[^>]*>', r'password field')
        ]

        for pattern, form_type in login_patterns:
            if re.search(pattern, html_content, re.IGNORECASE):
                # Extract form action if available
                action_match = re.search(r'<form[^>]*action=["\']([^"\']+)["\']', html_content)
                action = action_match.group(1) if action_match else None

                # Convert relative URL to absolute
                if action and not action.startswith(('http://', 'https://')):
                    action = urljoin(url, action)

                # Extract form method if available
                method_match = re.search(r'<form[^>]*method=["\']([^"\']+)["\']', html_content)
                method = method_match.group(1) if method_match else "post"

                # Extract input fields
                username_fields = []
                password_fields = []

                # Common username field patterns
                username_patterns = [
                    r'<input[^>]*name=["\']([^"\']+)["\'][^>]*user',
                    r'<input[^>]*name=["\']([^"\']+)["\'][^>]*email',
                    r'<input[^>]*name=["\']([^"\']+)["\'][^>]*login',
                    r'<input[^>]*id=["\']([^"\']+)["\'][^>]*user',
                    r'<input[^>]*id=["\']([^"\']+)["\'][^>]*email',
                    r'<input[^>]*id=["\']([^"\']+)["\'][^>]*login'
                ]

                for pattern in username_patterns:
                    matches = re.finditer(pattern, html_content, re.IGNORECASE)
                    for match in matches:
                        username_fields.append(match.group(1))

                # Common password field patterns
                password_patterns = [
                    r'<input[^>]*type=["\']password["\'][^>]*name=["\']([^"\']+)["\']',
                    r'<input[^>]*name=["\']([^"\']+)["\'][^>]*type=["\']password["\']'
                ]

                for pattern in password_patterns:
                    matches = re.finditer(pattern, html_content, re.IGNORECASE)
                    for match in matches:
                        password_fields.append(match.group(1))

                # Extract CSRF token if present
                csrf_token = None
                csrf_patterns = [
                    r'<input[^>]*name=["\']csrf[^"\']*["\'][^>]*value=["\']([^"\']+)["\']',
                    r'<input[^>]*name=["\']token[^"\']*["\'][^>]*value=["\']([^"\']+)["\']',
                    r'<meta[^>]*name=["\']csrf-token["\'][^>]*content=["\']([^"\']+)["\']'
                ]

                for pattern in csrf_patterns:
                    match = re.search(pattern, html_content, re.IGNORECASE)
                    if match:
                        csrf_token = match.group(1)
                        break

                # Create form info
                form_info = {
                    "type": form_type,
                    "url": url,
                    "action": action or url,
                    "method": method.lower(),
                    "username_fields": username_fields,
                    "password_fields": password_fields,
                    "csrf_token": csrf_token,
                    "detected_at": time.time()
                }

                # Store detected form
                domain = self._get_domain(url)
                self.detected_forms[url] = form_info

                logger.info(f"Detected login form on {url} ({form_type})")
                return form_info

        return None

    def store_credentials(
        self,
        domain: str,
        username: str,
        password: str,
        save: bool = True
    ) -> bool:
        """
        Store credentials for a domain.

        Args:
            domain: Domain to store credentials for
            username: Username
            password: Password
            save: Whether to save to disk/keyring

        Returns:
            True if successful, False otherwise
        """
        try:
            # Normalize domain
            domain = self._normalize_domain(domain)

            # Store password in keyring if enabled
            if self.use_keyring:
                keyring_key = f"{self.keyring_service_name}:{domain}"
                keyring.set_password(keyring_key, username, password)

                # Store only username and hash in memory
                password_hash = self._hash_password(password)
                self.credentials[domain] = {
                    "username": username,
                    "password_hash": password_hash,
                    "last_used": time.time(),
                    "in_keyring": True
                }
            else:
                # Store encrypted password in memory
                encrypted_password = self._encrypt_password(password)
                self.credentials[domain] = {
                    "username": username,
                    "encrypted_password": encrypted_password,
                    "last_used": time.time(),
                    "in_keyring": False
                }

            # Save to file if requested
            if save and self.credentials_file:
                self._save_credentials()

            logger.info(f"Stored credentials for {domain}")
            return True

        except Exception as e:
            logger.error(f"Failed to store credentials for {domain}: {str(e)}")
            return False

    def get_credentials(self, domain: str) -> Optional[Dict[str, str]]:
        """
        Get credentials for a domain.

        Args:
            domain: Domain to get credentials for

        Returns:
            Dictionary with username and password or None
        """
        try:
            # Normalize domain
            domain = self._normalize_domain(domain)

            # Check if we have credentials
            if domain not in self.credentials:
                return None

            cred_info = self.credentials[domain]
            username = cred_info["username"]

            # Get password from keyring if stored there
            if cred_info.get("in_keyring", False) and self.use_keyring:
                keyring_key = f"{self.keyring_service_name}:{domain}"
                password = keyring.get_password(keyring_key, username)

                if not password:
                    logger.warning(f"Password not found in keyring for {domain}")
                    return None

            # Otherwise decrypt from memory
            elif "encrypted_password" in cred_info:
                password = self._decrypt_password(cred_info["encrypted_password"])
            else:
                logger.warning(f"No password available for {domain}")
                return None

            # Update last used time
            self.credentials[domain]["last_used"] = time.time()

            return {
                "username": username,
                "password": password
            }

        except Exception as e:
            logger.error(f"Failed to get credentials for {domain}: {str(e)}")
            return None

    def store_cookies(self, domain: str, cookies: Dict[str, str], expiry: Optional[int] = None) -> bool:
        """
        Store cookies for a domain.

        Args:
            domain: Domain to store cookies for
            cookies: Dictionary of cookies
            expiry: Expiry time in seconds from now (None for session cookies)

        Returns:
            True if successful, False otherwise
        """
        try:
            # Normalize domain
            domain = self._normalize_domain(domain)

            # Calculate expiry time
            expiry_time = None
            if expiry:
                expiry_time = time.time() + expiry

            # Store cookies
            self.cookies[domain] = {
                "cookies": cookies,
                "expiry": expiry_time,
                "created_at": time.time(),
                "last_used": time.time()
            }

            # Save to file if available
            if self.cookie_file:
                self._save_cookies()

            logger.info(f"Stored cookies for {domain}")
            return True

        except Exception as e:
            logger.error(f"Failed to store cookies for {domain}: {str(e)}")
            return False

    def get_cookies(self, domain: str) -> Optional[Dict[str, str]]:
        """
        Get cookies for a domain.

        Args:
            domain: Domain to get cookies for

        Returns:
            Dictionary of cookies or None
        """
        try:
            # Normalize domain
            domain = self._normalize_domain(domain)

            # Check if we have cookies
            if domain not in self.cookies:
                return None

            cookie_info = self.cookies[domain]

            # Check if cookies have expired
            if cookie_info.get("expiry") and cookie_info["expiry"] < time.time():
                logger.info(f"Cookies for {domain} have expired")
                del self.cookies[domain]
                return None

            # Update last used time
            self.cookies[domain]["last_used"] = time.time()

            return cookie_info["cookies"]

        except Exception as e:
            logger.error(f"Failed to get cookies for {domain}: {str(e)}")
            return None

    def perform_login(
        self,
        url: str,
        form_info: Dict[str, Any],
        credentials: Optional[Dict[str, str]] = None,
        browser_context: Any = None
    ) -> Tuple[bool, Optional[Dict[str, str]]]:
        """
        Perform login using detected form and credentials.

        Args:
            url: URL of the login page
            form_info: Login form information
            credentials: Credentials to use (if None, will try to retrieve)
            browser_context: Browser context for Playwright (if available)

        Returns:
            Tuple of (success, cookies)
        """
        if not self.auto_login:
            logger.info("Auto-login is disabled")
            return False, None

        try:
            domain = self._get_domain(url)

            # Get credentials if not provided
            if not credentials:
                credentials = self.get_credentials(domain)

            if not credentials:
                logger.warning(f"No credentials available for {domain}")
                return False, None

            # Check if we have required form fields
            if not form_info.get("username_fields") or not form_info.get("password_fields"):
                logger.warning(f"Missing required form fields for {url}")
                return False, None

            # Require confirmation if enabled
            if self.require_confirmation:
                logger.info(f"Login attempt requires confirmation for {domain}")
                # In a real implementation, this would prompt the user
                # For now, we'll just log and continue

            # Record login attempt
            if domain not in self.login_attempts:
                self.login_attempts[domain] = {
                    "success_count": 0,
                    "fail_count": 0,
                    "last_attempt": time.time()
                }

            # If browser_context is provided, use Playwright for login
            if browser_context:
                success, cookies = self._login_with_playwright(
                    url, form_info, credentials, browser_context
                )
            else:
                # Otherwise use requests
                success, cookies = self._login_with_requests(
                    url, form_info, credentials
                )

            # Update login attempts
            if success:
                self.login_attempts[domain]["success_count"] += 1

                # Store cookies if available
                if cookies:
                    self.store_cookies(domain, cookies)

                logger.info(f"Login successful for {domain}")
            else:
                self.login_attempts[domain]["fail_count"] += 1
                logger.warning(f"Login failed for {domain}")

            self.login_attempts[domain]["last_attempt"] = time.time()

            return success, cookies

        except Exception as e:
            logger.error(f"Error during login attempt: {str(e)}")
            return False, None

    def _login_with_playwright(
        self,
        url: str,
        form_info: Dict[str, Any],
        credentials: Dict[str, str],
        browser_context: Any
    ) -> Tuple[bool, Optional[Dict[str, str]]]:
        """
        Perform login using Playwright.

        Args:
            url: URL of the login page
            form_info: Login form information
            credentials: Credentials to use
            browser_context: Browser context for Playwright

        Returns:
            Tuple of (success, cookies)
        """
        try:
            # This is a placeholder for the actual implementation
            # In a real implementation, this would use Playwright to fill the form and submit
            logger.info(f"Playwright login not fully implemented for {url}")

            # Simulate success for now
            return True, {"session": "dummy_session_id"}

        except Exception as e:
            logger.error(f"Error during Playwright login: {str(e)}")
            return False, None

    def _login_with_requests(
        self,
        url: str,
        form_info: Dict[str, Any],
        credentials: Dict[str, str]
    ) -> Tuple[bool, Optional[Dict[str, str]]]:
        """
        Perform login using requests.

        Args:
            url: URL of the login page
            form_info: Login form information
            credentials: Credentials to use

        Returns:
            Tuple of (success, cookies)
        """
        try:
            # This is a placeholder for the actual implementation
            # In a real implementation, this would use requests to submit the form
            logger.info(f"Requests login not fully implemented for {url}")

            # Simulate success for now
            return True, {"session": "dummy_session_id"}

        except Exception as e:
            logger.error(f"Error during requests login: {str(e)}")
            return False, None

    def _normalize_domain(self, domain: str) -> str:
        """
        Normalize domain for storage.

        Args:
            domain: Domain to normalize

        Returns:
            Normalized domain
        """
        # Remove protocol and www
        if "://" in domain:
            domain = domain.split("://")[1]

        if domain.startswith("www."):
            domain = domain[4:]

        return domain.lower()

    def _get_domain(self, url: str) -> str:
        """
        Extract domain from URL.

        Args:
            url: URL to extract domain from

        Returns:
            Domain
        """
        try:
            parsed = urlparse(url)
            domain = parsed.netloc

            # Remove port if present
            if ":" in domain:
                domain = domain.split(":")[0]

            return self._normalize_domain(domain)

        except Exception:
            return ""

    def _hash_password(self, password: str) -> str:
        """
        Create a hash of a password for storage comparison.

        Args:
            password: Password to hash

        Returns:
            Hashed password
        """
        return hashlib.sha256(password.encode()).hexdigest()

    def _encrypt_password(self, password: str) -> str:
        """
        Encrypt a password for storage.

        Args:
            password: Password to encrypt

        Returns:
            Encrypted password
        """
        # This is a simple encryption for demonstration
        # In a real implementation, use proper encryption
        return base64.b64encode(password.encode()).decode()

    def _decrypt_password(self, encrypted: str) -> str:
        """
        Decrypt a password from storage.

        Args:
            encrypted: Encrypted password

        Returns:
            Decrypted password
        """
        # This is a simple decryption for demonstration
        # In a real implementation, use proper decryption
        return base64.b64decode(encrypted.encode()).decode()

    def _load_credentials(self) -> None:
        """Load credentials from file."""
        if not self.credentials_file or not os.path.exists(self.credentials_file):
            return

        try:
            with open(self.credentials_file, 'r') as f:
                data = json.load(f)

            self.credentials = data
            logger.info(f"Loaded credentials for {len(self.credentials)} domains")

        except Exception as e:
            logger.error(f"Failed to load credentials: {str(e)}")

    def _save_credentials(self) -> None:
        """Save credentials to file."""
        if not self.credentials_file:
            return

        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.credentials_file), exist_ok=True)

            # Save to temporary file first
            temp_file = f"{self.credentials_file}.tmp"
            with open(temp_file, 'w') as f:
                json.dump(self.credentials, f)

            # Rename to actual file
            os.replace(temp_file, self.credentials_file)

            logger.info(f"Saved credentials for {len(self.credentials)} domains")

        except Exception as e:
            logger.error(f"Failed to save credentials: {str(e)}")

    def _load_cookies(self) -> None:
        """Load cookies from file."""
        if not self.cookie_file or not os.path.exists(self.cookie_file):
            return

        try:
            with open(self.cookie_file, 'r') as f:
                data = json.load(f)

            # Filter out expired cookies
            current_time = time.time()
            valid_cookies = {}

            for domain, cookie_info in data.items():
                if not cookie_info.get("expiry") or cookie_info["expiry"] > current_time:
                    valid_cookies[domain] = cookie_info

            self.cookies = valid_cookies
            logger.info(f"Loaded cookies for {len(self.cookies)} domains")

        except Exception as e:
            logger.error(f"Failed to load cookies: {str(e)}")

    def _save_cookies(self) -> None:
        """Save cookies to file."""
        if not self.cookie_file:
            return

        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.cookie_file), exist_ok=True)

            # Save to temporary file first
            temp_file = f"{self.cookie_file}.tmp"
            with open(temp_file, 'w') as f:
                json.dump(self.cookies, f)

            # Rename to actual file
            os.replace(temp_file, self.cookie_file)

            logger.info(f"Saved cookies for {len(self.cookies)} domains")

        except Exception as e:
            logger.error(f"Failed to save cookies: {str(e)}")

    def clear(self) -> None:
        """Clear all stored data."""
        self.credentials = {}
        self.cookies = {}
        self.detected_forms = {}
        self.login_attempts = {}
