"""
Module cung cấp các lớp và hàm để tìm kiếm học thuật.

Module này cung cấp các lớp và hàm để tìm kiếm học thuật từ nhiều nguồn khá<PERSON> n<PERSON><PERSON> Google Scholar, Semantic Scholar, arXiv, và các nguồn học thuật khác.
"""

import time
import logging
import requests
import re
from typing import Dict, Any, List, Optional, Union, Tuple
from bs4 import BeautifulSoup
from urllib.parse import quote, urlencode

# Tạo logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AcademicSearchProvider:
    """
    Lớp cơ sở cho các nhà cung cấp tìm kiếm học thuật.
    """
    
    def __init__(self, verbose: bool = False):
        """
        Khởi tạo AcademicSearchProvider.
        
        Args:
            verbose: Ghi log chi tiết
        """
        self.verbose = verbose
    
    def search(self, query: str, num_results: int = 10) -> Dict[str, Any]:
        """
        Tìm kiếm học thuật.
        
        Args:
            query: Truy vấn cần tìm kiếm
            num_results: Số kết quả cần trả về
            
        Returns:
            Dictionary chứa kết quả tìm kiếm
        """
        raise NotImplementedError("Phương thức này cần được triển khai bởi lớp con")

class GoogleScholarProvider(AcademicSearchProvider):
    """
    Nhà cung cấp tìm kiếm học thuật Google Scholar.
    """
    
    def __init__(self, verbose: bool = False, captcha_solver=None):
        """
        Khởi tạo GoogleScholarProvider.
        
        Args:
            verbose: Ghi log chi tiết
            captcha_solver: Đối tượng CaptchaSolver để xử lý captcha
        """
        super().__init__(verbose)
        self.captcha_solver = captcha_solver
    
    def search(self, query: str, num_results: int = 10) -> Dict[str, Any]:
        """
        Tìm kiếm học thuật trên Google Scholar.
        
        Args:
            query: Truy vấn cần tìm kiếm
            num_results: Số kết quả cần trả về
            
        Returns:
            Dictionary chứa kết quả tìm kiếm
        """
        try:
            # Sử dụng Google Scholar (web scraping)
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Language": "en-US,en;q=0.5",
                "Referer": "https://scholar.google.com/",
                "DNT": "1",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1"
            }
            
            # Mã hóa truy vấn cho URL
            encoded_query = quote(query)
            url = f"https://scholar.google.com/scholar?q={encoded_query}&hl=en&as_sdt=0,5"
            
            # Thực hiện yêu cầu
            html_content = None
            
            # Sử dụng captcha solver nếu có
            if self.captcha_solver:
                if self.verbose:
                    logger.info("Using captcha solver for URL: %s", url)
                
                success, html_content = self.captcha_solver.solve(url)
                
                if not success or not html_content:
                    if self.verbose:
                        logger.warning("Captcha solver failed, falling back to direct request")
                    html_content = None
            
            # Nếu không có captcha solver hoặc captcha solver thất bại, sử dụng yêu cầu trực tiếp
            if html_content is None:
                response = requests.get(url, headers=headers, timeout=10)
                response.raise_for_status()
                html_content = response.text
            
            # Phân tích kết quả sử dụng BeautifulSoup
            soup = BeautifulSoup(html_content, "html.parser")
            
            # Kiểm tra xem có captcha không
            if "captcha" in html_content.lower() or "challenge" in html_content.lower():
                if self.verbose:
                    logger.warning("Captcha detected in response")
                
                # Nếu đã sử dụng captcha solver nhưng vẫn có captcha, trả về lỗi
                if self.captcha_solver:
                    return {
                        "success": False,
                        "error": "Captcha detected even after using captcha solver",
                        "results": []
                    }
            
            # Trích xuất kết quả tìm kiếm
            results = []
            result_elements = soup.select(".gs_r.gs_or.gs_scl")
            
            for element in result_elements[:num_results]:
                # Trích xuất tiêu đề và URL
                title_element = element.select_one(".gs_rt")
                
                if title_element:
                    # Lấy tiêu đề
                    title = title_element.get_text().strip()
                    
                    # Lấy URL
                    url = ""
                    link_element = title_element.select_one("a")
                    if link_element and link_element.has_attr("href"):
                        url = link_element["href"]
                    
                    # Lấy tác giả và tạp chí
                    authors_element = element.select_one(".gs_a")
                    authors = authors_element.get_text().strip() if authors_element else ""
                    
                    # Lấy đoạn trích
                    snippet_element = element.select_one(".gs_rs")
                    snippet = snippet_element.get_text().strip() if snippet_element else ""
                    
                    # Lấy số trích dẫn
                    citation_element = element.select_one(".gs_fl a")
                    citations = ""
                    if citation_element and "Cited by" in citation_element.get_text():
                        citations = citation_element.get_text().strip()
                    
                    # Thêm kết quả
                    results.append({
                        "title": title,
                        "url": url,
                        "authors": authors,
                        "snippet": snippet,
                        "citations": citations,
                        "source": "google_scholar"
                    })
            
            if self.verbose:
                logger.info("Found %d results from Google Scholar for query: %s", 
                           len(results), query)
            
            return {
                "success": True,
                "query": query,
                "results": results,
                "engine": "google_scholar",
                "timestamp": time.time()
            }
        
        except Exception as e:
            logger.error("Error searching Google Scholar: %s", str(e))
            return {
                "success": False,
                "error": str(e),
                "results": []
            }

class SemanticScholarProvider(AcademicSearchProvider):
    """
    Nhà cung cấp tìm kiếm học thuật Semantic Scholar.
    """
    
    def __init__(self, verbose: bool = False, api_key: Optional[str] = None):
        """
        Khởi tạo SemanticScholarProvider.
        
        Args:
            verbose: Ghi log chi tiết
            api_key: API key cho Semantic Scholar (nếu có)
        """
        super().__init__(verbose)
        self.api_key = api_key
    
    def search(self, query: str, num_results: int = 10) -> Dict[str, Any]:
        """
        Tìm kiếm học thuật trên Semantic Scholar.
        
        Args:
            query: Truy vấn cần tìm kiếm
            num_results: Số kết quả cần trả về
            
        Returns:
            Dictionary chứa kết quả tìm kiếm
        """
        try:
            # Sử dụng Semantic Scholar API
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Accept": "application/json"
            }
            
            # Thêm API key nếu có
            if self.api_key:
                headers["x-api-key"] = self.api_key
            
            # Tạo URL
            params = {
                "query": query,
                "limit": num_results,
                "fields": "title,url,abstract,authors,venue,year,citationCount,influentialCitationCount,externalIds"
            }
            
            url = f"https://api.semanticscholar.org/graph/v1/paper/search?{urlencode(params)}"
            
            # Thực hiện yêu cầu
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            # Trích xuất kết quả tìm kiếm
            results = []
            
            for paper in data.get("data", []):
                # Lấy thông tin tác giả
                authors = []
                for author in paper.get("authors", []):
                    authors.append(author.get("name", ""))
                
                # Lấy thông tin trích dẫn
                citation_count = paper.get("citationCount", 0)
                influential_citation_count = paper.get("influentialCitationCount", 0)
                
                # Lấy thông tin DOI
                external_ids = paper.get("externalIds", {})
                doi = external_ids.get("DOI", "")
                
                # Thêm kết quả
                results.append({
                    "title": paper.get("title", ""),
                    "url": paper.get("url", ""),
                    "authors": ", ".join(authors),
                    "snippet": paper.get("abstract", ""),
                    "venue": paper.get("venue", ""),
                    "year": paper.get("year", ""),
                    "citations": f"Cited by {citation_count}",
                    "influential_citations": influential_citation_count,
                    "doi": doi,
                    "source": "semantic_scholar"
                })
            
            if self.verbose:
                logger.info("Found %d results from Semantic Scholar for query: %s", 
                           len(results), query)
            
            return {
                "success": True,
                "query": query,
                "results": results,
                "engine": "semantic_scholar",
                "timestamp": time.time()
            }
        
        except Exception as e:
            logger.error("Error searching Semantic Scholar: %s", str(e))
            return {
                "success": False,
                "error": str(e),
                "results": []
            }

class ArxivProvider(AcademicSearchProvider):
    """
    Nhà cung cấp tìm kiếm học thuật arXiv.
    """
    
    def __init__(self, verbose: bool = False):
        """
        Khởi tạo ArxivProvider.
        
        Args:
            verbose: Ghi log chi tiết
        """
        super().__init__(verbose)
    
    def search(self, query: str, num_results: int = 10) -> Dict[str, Any]:
        """
        Tìm kiếm học thuật trên arXiv.
        
        Args:
            query: Truy vấn cần tìm kiếm
            num_results: Số kết quả cần trả về
            
        Returns:
            Dictionary chứa kết quả tìm kiếm
        """
        try:
            # Sử dụng arXiv API
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            }
            
            # Tạo URL
            params = {
                "search_query": f"all:{query}",
                "start": 0,
                "max_results": num_results
            }
            
            url = f"http://export.arxiv.org/api/query?{urlencode(params)}"
            
            # Thực hiện yêu cầu
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            
            # Phân tích kết quả sử dụng BeautifulSoup
            soup = BeautifulSoup(response.text, "xml")
            
            # Trích xuất kết quả tìm kiếm
            results = []
            entries = soup.find_all("entry")
            
            for entry in entries:
                # Lấy tiêu đề
                title = entry.title.text.strip() if entry.title else ""
                
                # Lấy URL
                url = entry.id.text.strip() if entry.id else ""
                
                # Lấy tác giả
                authors = []
                for author in entry.find_all("author"):
                    if author.name:
                        authors.append(author.name.text.strip())
                
                # Lấy tóm tắt
                summary = entry.summary.text.strip() if entry.summary else ""
                
                # Lấy ngày xuất bản
                published = entry.published.text.strip() if entry.published else ""
                
                # Lấy danh mục
                categories = []
                for category in entry.find_all("category"):
                    if category.get("term"):
                        categories.append(category.get("term"))
                
                # Thêm kết quả
                results.append({
                    "title": title,
                    "url": url,
                    "authors": ", ".join(authors),
                    "snippet": summary,
                    "published": published,
                    "categories": categories,
                    "source": "arxiv"
                })
            
            if self.verbose:
                logger.info("Found %d results from arXiv for query: %s", 
                           len(results), query)
            
            return {
                "success": True,
                "query": query,
                "results": results,
                "engine": "arxiv",
                "timestamp": time.time()
            }
        
        except Exception as e:
            logger.error("Error searching arXiv: %s", str(e))
            return {
                "success": False,
                "error": str(e),
                "results": []
            }

class AcademicSearchAggregator:
    """
    Lớp tổng hợp kết quả từ nhiều nhà cung cấp tìm kiếm học thuật.
    """
    
    def __init__(self, providers: Optional[List[AcademicSearchProvider]] = None, verbose: bool = False):
        """
        Khởi tạo AcademicSearchAggregator.
        
        Args:
            providers: Danh sách các nhà cung cấp tìm kiếm học thuật
            verbose: Ghi log chi tiết
        """
        self.providers = providers or []
        self.verbose = verbose
    
    def add_provider(self, provider: AcademicSearchProvider) -> None:
        """
        Thêm nhà cung cấp tìm kiếm học thuật.
        
        Args:
            provider: Nhà cung cấp tìm kiếm học thuật
        """
        self.providers.append(provider)
    
    def search(self, query: str, num_results: int = 10) -> Dict[str, Any]:
        """
        Tìm kiếm học thuật từ nhiều nhà cung cấp.
        
        Args:
            query: Truy vấn cần tìm kiếm
            num_results: Số kết quả cần trả về từ mỗi nhà cung cấp
            
        Returns:
            Dictionary chứa kết quả tìm kiếm từ tất cả các nhà cung cấp
        """
        all_results = []
        successful_providers = []
        failed_providers = []
        
        # Tìm kiếm từ tất cả các nhà cung cấp
        for provider in self.providers:
            provider_name = provider.__class__.__name__
            
            if self.verbose:
                logger.info("Searching with provider: %s", provider_name)
            
            result = provider.search(query, num_results)
            
            if result["success"]:
                all_results.extend(result["results"])
                successful_providers.append(provider_name)
            else:
                failed_providers.append({
                    "provider": provider_name,
                    "error": result.get("error", "Unknown error")
                })
        
        # Sắp xếp kết quả theo số trích dẫn (nếu có)
        def get_citation_count(result):
            citations = result.get("citations", "")
            if isinstance(citations, str) and "cited by" in citations.lower():
                try:
                    return int(re.search(r'\d+', citations).group())
                except (AttributeError, ValueError):
                    pass
            return 0
        
        all_results.sort(key=get_citation_count, reverse=True)
        
        # Giới hạn số lượng kết quả
        all_results = all_results[:num_results]
        
        if self.verbose:
            logger.info("Found %d total results from %d providers", 
                       len(all_results), len(successful_providers))
        
        return {
            "success": len(successful_providers) > 0,
            "query": query,
            "results": all_results,
            "successful_providers": successful_providers,
            "failed_providers": failed_providers,
            "timestamp": time.time()
        }

def create_academic_search_aggregator(captcha_solver=None, semantic_scholar_api_key=None, verbose=False) -> AcademicSearchAggregator:
    """
    Tạo một AcademicSearchAggregator với các nhà cung cấp phổ biến.
    
    Args:
        captcha_solver: Đối tượng CaptchaSolver để xử lý captcha
        semantic_scholar_api_key: API key cho Semantic Scholar (nếu có)
        verbose: Ghi log chi tiết
        
    Returns:
        AcademicSearchAggregator đã được cấu hình
    """
    aggregator = AcademicSearchAggregator(verbose=verbose)
    
    # Thêm các nhà cung cấp
    aggregator.add_provider(GoogleScholarProvider(verbose=verbose, captcha_solver=captcha_solver))
    aggregator.add_provider(SemanticScholarProvider(verbose=verbose, api_key=semantic_scholar_api_key))
    aggregator.add_provider(ArxivProvider(verbose=verbose))
    
    return aggregator
