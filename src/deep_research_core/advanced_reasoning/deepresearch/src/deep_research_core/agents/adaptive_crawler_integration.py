#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module cung cấp các hàm tích hợp AdaptiveCrawler với WebSearchAgentLocal.

Module này cung cấp các hàm để tích hợp AdaptiveCrawler với WebSearchAgentLocal,
cho phép WebSearchAgentLocal sử dụng AdaptiveCrawler để crawl nội dung web một cách thích ứng.
"""

import logging
import time
import asyncio
import os
import json
import traceback
import concurrent.futures
from typing import Dict, List, Any, Optional, Tuple, Union, Callable

from ..utils.structured_logging import get_logger
from ..utils.url_utils import is_valid_url, clean_url, normalize_url
from ..utils.performance_optimizer import PerformanceOptimizer
from ..utils.memory_manager import MemoryManager
from ..utils.error_handling import (
    SearchError,
    RateLimitError,
    ConnectionError,
    ContentExtractionError,
    BotDetectionError,
    TimeoutError,
    format_error_response
)

# Import AdaptiveCrawler
try:
    from ..crawlers.adaptive_crawler import AdaptiveCrawler
    ADAPTIVE_CRAWLER_AVAILABLE = True
except ImportError:
    try:
        from .adaptive_crawler import AdaptiveCrawler
        ADAPTIVE_CRAWLER_AVAILABLE = True
    except ImportError:
        ADAPTIVE_CRAWLER_AVAILABLE = False

# Import MemoryOptimizedCrawler
try:
    from ..crawlers.memory_optimized_crawler import MemoryOptimizedCrawler
    MEMORY_OPTIMIZED_CRAWLER_AVAILABLE = True
except ImportError:
    try:
        from .memory_optimized_crawler import MemoryOptimizedCrawler
        MEMORY_OPTIMIZED_CRAWLER_AVAILABLE = True
    except ImportError:
        MEMORY_OPTIMIZED_CRAWLER_AVAILABLE = False

# Import PlaywrightHandler
try:
    from ..utils.playwright_handler import PlaywrightHandler
    PLAYWRIGHT_HANDLER_AVAILABLE = True
except ImportError:
    try:
        from .playwright_handler import PlaywrightHandler
        PLAYWRIGHT_HANDLER_AVAILABLE = True
    except ImportError:
        PLAYWRIGHT_HANDLER_AVAILABLE = False

# Thiết lập logging
logger = get_logger(__name__)

# Định nghĩa các lỗi cụ thể cho AdaptiveCrawler
class AdaptiveCrawlerError(Exception):
    """Lỗi cơ bản cho AdaptiveCrawler."""
    def __init__(self, message: str, url: str = None, details: Dict[str, Any] = None):
        self.message = message
        self.url = url
        self.details = details or {}
        super().__init__(self.message)

class AdaptiveCrawlerNotAvailableError(AdaptiveCrawlerError):
    """Lỗi khi AdaptiveCrawler không khả dụng."""
    pass

class AdaptiveCrawlerConfigError(AdaptiveCrawlerError):
    """Lỗi cấu hình AdaptiveCrawler."""
    pass

class AdaptiveCrawlerConnectionError(AdaptiveCrawlerError):
    """Lỗi kết nối khi crawl."""
    pass

class AdaptiveCrawlerTimeoutError(AdaptiveCrawlerError):
    """Lỗi timeout khi crawl."""
    pass

class AdaptiveCrawlerCaptchaError(AdaptiveCrawlerError):
    """Lỗi CAPTCHA khi crawl."""
    pass

class AdaptiveCrawlerContentExtractionError(AdaptiveCrawlerError):
    """Lỗi trích xuất nội dung khi crawl."""
    pass

class AdaptiveCrawlerPlaywrightError(AdaptiveCrawlerError):
    """Lỗi Playwright khi crawl."""
    pass

class AdaptiveCrawlerMemoryError(AdaptiveCrawlerError):
    """Lỗi bộ nhớ khi crawl."""
    pass

# Các hằng số
DEFAULT_CONFIG = {
    "max_depth": 2,
    "max_urls_per_domain": 5,
    "max_total_urls": 100,
    "delay": 1.0,
    "timeout": 10.0,
    "respect_robots_txt": True,
    "follow_redirects": True,
    "max_redirects": 5,
    "max_retries": 3,
    "retry_delay": 2.0,
    "max_threads": 5,
    "verify_ssl": True,
    "cache_enabled": True,
    "cache_ttl": 3600,
    "cache_size": 1000,
    "extract_metadata": True,
    "extract_links": True,
    "extract_images": False,
    "extract_files": False,
    "extract_structured_data": False,
    "detect_language": True,
    "detect_encoding": True,
    "detect_content_type": True,
    "detect_site_type": True,
    "handle_javascript": False,
    "handle_captcha": False,
    "use_memory_optimization": True,
    "memory_threshold": 80.0,
    "memory_cleanup_interval": 5,
    "use_playwright": False,
    "headless": True,
    "browser_type": "chromium",
    "viewport_size": {"width": 1280, "height": 800},
    "handle_spa": False,
    "handle_infinite_scroll": False,
    "handle_pagination": True,
    "download_media": False,
    "download_path": "downloads",
    "max_media_size_mb": 10,
    "site_map_enabled": False,
    "crawl_mode": "full_site",
    "language": "auto",
    "detailed_scraping": True,
    "verbose": False
}

# Hàm xử lý lỗi
def handle_adaptive_crawler_error(e: Exception, url: str = None, context: str = None) -> Dict[str, Any]:
    """
    Xử lý lỗi từ AdaptiveCrawler và trả về kết quả lỗi có cấu trúc.

    Args:
        e: Exception đã xảy ra
        url: URL đang được xử lý
        context: Ngữ cảnh của lỗi

    Returns:
        Dict[str, Any]: Kết quả lỗi có cấu trúc
    """
    error_type = type(e).__name__
    error_message = str(e)
    error_traceback = traceback.format_exc()

    # Ghi log lỗi
    logger.error(f"AdaptiveCrawler error in {context or 'unknown context'}: {error_type}: {error_message}")
    if logger.isEnabledFor(logging.DEBUG):
        logger.debug(f"Traceback: {error_traceback}")

    # Phân loại lỗi
    if isinstance(e, AdaptiveCrawlerError):
        error_category = "adaptive_crawler_error"
        error_details = e.details if hasattr(e, "details") else {}
        error_url = e.url if hasattr(e, "url") else url
    elif isinstance(e, ConnectionError) or "ConnectionError" in error_type:
        error_category = "connection_error"
        error_details = {"traceback": error_traceback}
        error_url = url
    elif isinstance(e, TimeoutError) or "TimeoutError" in error_type or "timeout" in error_message.lower():
        error_category = "timeout_error"
        error_details = {"traceback": error_traceback}
        error_url = url
    elif "captcha" in error_message.lower():
        error_category = "captcha_error"
        error_details = {"traceback": error_traceback}
        error_url = url
    elif "memory" in error_message.lower():
        error_category = "memory_error"
        error_details = {"traceback": error_traceback}
        error_url = url
    elif "playwright" in error_message.lower():
        error_category = "playwright_error"
        error_details = {"traceback": error_traceback}
        error_url = url
    else:
        error_category = "unknown_error"
        error_details = {"traceback": error_traceback}
        error_url = url

    # Tạo kết quả lỗi
    error_result = {
        "success": False,
        "url": error_url,
        "error": error_message,
        "error_type": error_type,
        "error_category": error_category,
        "error_details": error_details,
        "content": f"[Error: {error_message} for URL: {error_url}]"
    }

    return error_result

def integrate_adaptive_crawler(agent, config: Dict[str, Any] = None) -> None:
    """
    Tích hợp AdaptiveCrawler vào WebSearchAgentLocal.

    Args:
        agent: WebSearchAgentLocal instance
        config: Cấu hình cho AdaptiveCrawler
    """
    if config is None:
        config = {}

    # Kết hợp cấu hình mặc định với cấu hình được cung cấp
    merged_config = DEFAULT_CONFIG.copy()
    merged_config.update(config)

    # Khởi tạo MemoryManager nếu cần
    if merged_config.get("use_memory_optimization", True):
        try:
            memory_manager = MemoryManager(
                threshold=merged_config.get("memory_threshold", 80.0),
                cleanup_interval=merged_config.get("memory_cleanup_interval", 5),
                verbose=merged_config.get("verbose", False)
            )
            logger.info("MemoryManager initialized successfully")
        except Exception as e:
            logger.warning(f"Failed to initialize MemoryManager: {str(e)}")
            memory_manager = None
    else:
        memory_manager = None

    # Khởi tạo PlaywrightHandler nếu cần
    if merged_config.get("use_playwright", False) and PLAYWRIGHT_HANDLER_AVAILABLE:
        try:
            playwright_handler = PlaywrightHandler(
                headless=merged_config.get("headless", True),
                browser_type=merged_config.get("browser_type", "chromium"),
                timeout=merged_config.get("timeout", 30),
                viewport_size=merged_config.get("viewport_size", {"width": 1280, "height": 800}),
                user_agent=merged_config.get("user_agent", agent.user_agent if hasattr(agent, "user_agent") else None),
                handle_javascript=merged_config.get("handle_javascript", True),
                handle_spa=merged_config.get("handle_spa", False),
                handle_infinite_scroll=merged_config.get("handle_infinite_scroll", False),
                handle_captcha=merged_config.get("handle_captcha", False),
                verbose=merged_config.get("verbose", False)
            )
            logger.info("PlaywrightHandler initialized successfully")
        except Exception as e:
            logger.warning(f"Failed to initialize PlaywrightHandler: {str(e)}")
            playwright_handler = None
    else:
        playwright_handler = None

    # Khởi tạo MediaHandler nếu cần
    if merged_config.get("extract_images", False) or merged_config.get("download_media", False):
        try:
            from .media_handler import MediaHandler
            media_handler = MediaHandler(
                download_path=merged_config.get("download_path", "downloads"),
                max_media_size_mb=merged_config.get("max_media_size_mb", 10),
                timeout=merged_config.get("timeout", 30),
                max_retries=merged_config.get("max_retries", 3),
                verbose=merged_config.get("verbose", False)
            )
            logger.info("MediaHandler initialized successfully")
        except ImportError:
            logger.warning("MediaHandler not available, media extraction will be limited")
            media_handler = None
    else:
        media_handler = None

    # Khởi tạo FileHandler nếu cần
    if merged_config.get("extract_files", False):
        try:
            from .file_handler import FileHandler
            file_handler = FileHandler(
                download_path=merged_config.get("download_path", "downloads"),
                max_file_size_mb=merged_config.get("max_file_size_mb", 50),
                timeout=merged_config.get("timeout", 30),
                max_retries=merged_config.get("max_retries", 3),
                verbose=merged_config.get("verbose", False)
            )
            logger.info("FileHandler initialized successfully")
        except ImportError:
            logger.warning("FileHandler not available, file extraction will be limited")
            file_handler = None
    else:
        file_handler = None

    # Khởi tạo ErrorHandler nếu cần
    try:
        from .error_handler import ErrorHandler
        error_handler = ErrorHandler(
            max_retries=merged_config.get("max_retries", 3),
            verbose=merged_config.get("verbose", False)
        )
        logger.info("ErrorHandler initialized successfully")
    except ImportError:
        logger.warning("ErrorHandler not available, error handling will be limited")
        error_handler = None

    # Khởi tạo AdaptiveCrawler
    try:
        # Sử dụng MemoryOptimizedCrawler nếu có và được yêu cầu
        if merged_config.get("use_memory_optimization", True) and MEMORY_OPTIMIZED_CRAWLER_AVAILABLE:
            agent._adaptive_crawler = MemoryOptimizedCrawler(
                max_depth=merged_config.get("max_depth", 2),
                max_urls_per_domain=merged_config.get("max_urls_per_domain", 5),
                max_total_urls=merged_config.get("max_total_urls", 100),
                delay=merged_config.get("delay", 1.0),
                timeout=merged_config.get("timeout", 10.0),
                user_agent=merged_config.get("user_agent", agent.user_agent if hasattr(agent, "user_agent") else None),
                respect_robots_txt=merged_config.get("respect_robots_txt", True),
                follow_redirects=merged_config.get("follow_redirects", True),
                max_redirects=merged_config.get("max_redirects", 5),
                max_retries=merged_config.get("max_retries", 3),
                retry_delay=merged_config.get("retry_delay", 2.0),
                max_threads=merged_config.get("max_threads", 5),
                verify_ssl=merged_config.get("verify_ssl", True),
                proxies=merged_config.get("proxies"),
                cookies=merged_config.get("cookies"),
                headers=merged_config.get("headers"),
                cache_enabled=merged_config.get("cache_enabled", True),
                cache_ttl=merged_config.get("cache_ttl", 3600),
                cache_size=merged_config.get("cache_size", 1000),
                content_types=merged_config.get("content_types"),
                excluded_extensions=merged_config.get("excluded_extensions"),
                excluded_domains=merged_config.get("excluded_domains"),
                included_domains=merged_config.get("included_domains"),
                url_patterns=merged_config.get("url_patterns"),
                content_patterns=merged_config.get("content_patterns"),
                min_content_length=merged_config.get("min_content_length", 500),
                max_content_length=merged_config.get("max_content_length", 5000000),
                extract_metadata=merged_config.get("extract_metadata", True),
                extract_links=merged_config.get("extract_links", True),
                extract_images=merged_config.get("extract_images", False),
                extract_files=merged_config.get("extract_files", False),
                extract_structured_data=merged_config.get("extract_structured_data", False),
                detect_language=merged_config.get("detect_language", True),
                detect_encoding=merged_config.get("detect_encoding", True),
                detect_content_type=merged_config.get("detect_content_type", True),
                detect_site_type=merged_config.get("detect_site_type", True),
                handle_javascript=merged_config.get("handle_javascript", False),
                handle_captcha=merged_config.get("handle_captcha", False),
                memory_manager=memory_manager,
                playwright_handler=playwright_handler,
                verbose=merged_config.get("verbose", agent.verbose if hasattr(agent, "verbose") else False)
            )
            logger.info("MemoryOptimizedCrawler initialized successfully")
        else:
            # Sử dụng AdaptiveCrawler thông thường
            agent._adaptive_crawler = AdaptiveCrawler(
                max_depth=merged_config.get("max_depth", 2),
                max_urls_per_domain=merged_config.get("max_urls_per_domain", 5),
                max_total_urls=merged_config.get("max_total_urls", 100),
                delay=merged_config.get("delay", 1.0),
                timeout=merged_config.get("timeout", 10.0),
                user_agent=merged_config.get("user_agent", agent.user_agent if hasattr(agent, "user_agent") else None),
                respect_robots_txt=merged_config.get("respect_robots_txt", True),
                follow_redirects=merged_config.get("follow_redirects", True),
                max_redirects=merged_config.get("max_redirects", 5),
                max_retries=merged_config.get("max_retries", 3),
                retry_delay=merged_config.get("retry_delay", 2.0),
                max_threads=merged_config.get("max_threads", 5),
                verify_ssl=merged_config.get("verify_ssl", True),
                proxies=merged_config.get("proxies"),
                cookies=merged_config.get("cookies"),
                headers=merged_config.get("headers"),
                cache_enabled=merged_config.get("cache_enabled", True),
                cache_ttl=merged_config.get("cache_ttl", 3600),
                cache_size=merged_config.get("cache_size", 1000),
                content_types=merged_config.get("content_types"),
                excluded_extensions=merged_config.get("excluded_extensions"),
                excluded_domains=merged_config.get("excluded_domains"),
                included_domains=merged_config.get("included_domains"),
                url_patterns=merged_config.get("url_patterns"),
                content_patterns=merged_config.get("content_patterns"),
                min_content_length=merged_config.get("min_content_length", 500),
                max_content_length=merged_config.get("max_content_length", 5000000),
                extract_metadata=merged_config.get("extract_metadata", True),
                extract_links=merged_config.get("extract_links", True),
                extract_images=merged_config.get("extract_images", False),
                extract_files=merged_config.get("extract_files", False),
                extract_structured_data=merged_config.get("extract_structured_data", False),
                detect_language=merged_config.get("detect_language", True),
                detect_encoding=merged_config.get("detect_encoding", True),
                detect_content_type=merged_config.get("detect_content_type", True),
                detect_site_type=merged_config.get("detect_site_type", True),
                handle_javascript=merged_config.get("handle_javascript", False),
                handle_captcha=merged_config.get("handle_captcha", False),
                verbose=merged_config.get("verbose", agent.verbose if hasattr(agent, "verbose") else False)
            )
            logger.info("AdaptiveCrawler initialized successfully")

        # Lưu cấu hình đã sử dụng
        agent._adaptive_crawler_config = merged_config

        # Gán các phương thức tích hợp
        agent._crawl_url = lambda url, **kwargs: crawl_url(agent, url, **kwargs)
        agent._crawl_urls = lambda urls, **kwargs: crawl_urls(agent, urls, **kwargs)
        agent._extract_content_from_url = lambda url, **kwargs: extract_content_from_url(agent, url, **kwargs)
        agent._extract_links_from_url = lambda url, **kwargs: extract_links_from_url(agent, url, **kwargs)
        agent._extract_metadata_from_url = lambda url, **kwargs: extract_metadata_from_url(agent, url, **kwargs)
        agent._deep_crawl_with_adaptive_crawler = lambda url, **kwargs: deep_crawl_with_adaptive_crawler(agent, url, **kwargs)
        agent._crawl_with_memory_optimization = lambda url, **kwargs: crawl_with_memory_optimization(agent, url, **kwargs) if memory_manager else None
        agent._crawl_with_playwright = lambda url, **kwargs: crawl_with_playwright(agent, url, **kwargs) if playwright_handler else None

        logger.info("AdaptiveCrawler integrated successfully")
    except Exception as e:
        logger.error(f"Failed to integrate AdaptiveCrawler: {str(e)}")

def crawl_url(agent, url: str, **kwargs) -> Dict[str, Any]:
    """
    Crawl một URL sử dụng AdaptiveCrawler.

    Args:
        agent: WebSearchAgentLocal instance
        url: URL cần crawl
        **kwargs: Các tham số bổ sung

    Returns:
        Dict[str, Any]: Kết quả crawl
    """
    # Kiểm tra URL
    if not url:
        error = AdaptiveCrawlerError("URL is empty or None", url=url)
        return handle_adaptive_crawler_error(error, url, "crawl_url")

    # Kiểm tra URL hợp lệ
    try:
        url = clean_url(url)
    except Exception as e:
        error = AdaptiveCrawlerError(f"Invalid URL: {str(e)}", url=url)
        return handle_adaptive_crawler_error(error, url, "crawl_url")

    # Kiểm tra AdaptiveCrawler có sẵn không
    if not hasattr(agent, "_adaptive_crawler") or agent._adaptive_crawler is None:
        error = AdaptiveCrawlerNotAvailableError("AdaptiveCrawler not available", url=url)
        logger.warning(f"AdaptiveCrawler not available for URL: {url}")
        return handle_adaptive_crawler_error(error, url, "crawl_url")

    # Crawl URL
    try:
        result = agent._adaptive_crawler.crawl(url, **kwargs)

        # Kiểm tra kết quả
        if not result.get("success", False):
            error_message = result.get("error", "Unknown error")
            error = AdaptiveCrawlerError(error_message, url=url, details=result)
            return handle_adaptive_crawler_error(error, url, "crawl_url")

        return result
    except ConnectionError as e:
        error = AdaptiveCrawlerConnectionError(f"Connection error: {str(e)}", url=url)
        return handle_adaptive_crawler_error(error, url, "crawl_url")
    except TimeoutError as e:
        error = AdaptiveCrawlerTimeoutError(f"Timeout error: {str(e)}", url=url)
        return handle_adaptive_crawler_error(error, url, "crawl_url")
    except Exception as e:
        error = AdaptiveCrawlerError(f"Unexpected error: {str(e)}", url=url)
        return handle_adaptive_crawler_error(error, url, "crawl_url")

def crawl_urls(agent, urls: List[str], **kwargs) -> List[Dict[str, Any]]:
    """
    Crawl nhiều URL sử dụng AdaptiveCrawler.

    Args:
        agent: WebSearchAgentLocal instance
        urls: Danh sách URL cần crawl
        **kwargs: Các tham số bổ sung

    Returns:
        List[Dict[str, Any]]: Danh sách kết quả crawl
    """
    if not hasattr(agent, "_adaptive_crawler") or agent._adaptive_crawler is None:
        logger.warning("AdaptiveCrawler not available")
        return [{"success": False, "url": url, "error": "AdaptiveCrawler not available"} for url in urls]

    # Crawl URLs
    results = []
    for url in urls:
        result = agent._adaptive_crawler.crawl(url, **kwargs)
        results.append(result)

    return results

def extract_content_from_url(agent, url: str, **kwargs) -> str:
    """
    Trích xuất nội dung từ URL sử dụng AdaptiveCrawler.

    Args:
        agent: WebSearchAgentLocal instance
        url: URL cần trích xuất nội dung
        **kwargs: Các tham số bổ sung

    Returns:
        str: Nội dung trích xuất
    """
    if not hasattr(agent, "_adaptive_crawler") or agent._adaptive_crawler is None:
        logger.warning("AdaptiveCrawler not available")
        return f"[Error: AdaptiveCrawler not available for URL: {url}]"

    # Crawl URL
    result = agent._adaptive_crawler.crawl(url, **kwargs)

    # Kiểm tra kết quả
    if not result.get("success", False):
        error = result.get("error", "Unknown error")
        return f"[Error: {error} for URL: {url}]"

    # Trả về nội dung
    return result.get("content", "")

def extract_links_from_url(agent, url: str, **kwargs) -> List[Dict[str, Any]]:
    """
    Trích xuất links từ URL sử dụng AdaptiveCrawler.

    Args:
        agent: WebSearchAgentLocal instance
        url: URL cần trích xuất links
        **kwargs: Các tham số bổ sung

    Returns:
        List[Dict[str, Any]]: Danh sách links
    """
    if not hasattr(agent, "_adaptive_crawler") or agent._adaptive_crawler is None:
        logger.warning("AdaptiveCrawler not available")
        return []

    # Crawl URL
    result = agent._adaptive_crawler.crawl(url, **kwargs)

    # Kiểm tra kết quả
    if not result.get("success", False):
        return []

    # Trả về links
    return result.get("links", [])

def extract_metadata_from_url(agent, url: str, **kwargs) -> Dict[str, Any]:
    """
    Trích xuất metadata từ URL sử dụng AdaptiveCrawler.

    Args:
        agent: WebSearchAgentLocal instance
        url: URL cần trích xuất metadata
        **kwargs: Các tham số bổ sung

    Returns:
        Dict[str, Any]: Metadata
    """
    if not hasattr(agent, "_adaptive_crawler") or agent._adaptive_crawler is None:
        logger.warning("AdaptiveCrawler not available")
        return {}

    # Crawl URL
    result = agent._adaptive_crawler.crawl(url, **kwargs)

    # Kiểm tra kết quả
    if not result.get("success", False):
        return {}

    # Trả về metadata
    return result.get("metadata", {})

def crawl_with_memory_optimization(agent, url: str, **kwargs) -> Dict[str, Any]:
    """
    Crawl một URL với tối ưu hóa bộ nhớ.

    Args:
        agent: WebSearchAgentLocal instance
        url: URL cần crawl
        **kwargs: Các tham số bổ sung

    Returns:
        Dict[str, Any]: Kết quả crawl
    """
    if not hasattr(agent, "_adaptive_crawler") or agent._adaptive_crawler is None:
        logger.warning("AdaptiveCrawler not available")
        return {"success": False, "url": url, "error": "AdaptiveCrawler not available"}

    # Kiểm tra xem AdaptiveCrawler có hỗ trợ tối ưu hóa bộ nhớ không
    if not hasattr(agent._adaptive_crawler, "crawl_with_memory_optimization"):
        logger.warning("Memory optimization not supported by AdaptiveCrawler")
        return agent._adaptive_crawler.crawl(url, **kwargs)

    # Crawl URL với tối ưu hóa bộ nhớ
    return agent._adaptive_crawler.crawl_with_memory_optimization(url, **kwargs)

def crawl_with_playwright(agent, url: str, **kwargs) -> Dict[str, Any]:
    """
    Crawl một URL sử dụng Playwright.

    Args:
        agent: WebSearchAgentLocal instance
        url: URL cần crawl
        **kwargs: Các tham số bổ sung
            - headless: Chạy trình duyệt ở chế độ headless hay không (mặc định: True)
            - browser_type: Loại trình duyệt ("chromium", "firefox", "webkit") (mặc định: "chromium")
            - timeout: Thời gian timeout cho mỗi request (mặc định: 30)
            - viewport_size: Kích thước viewport (mặc định: {"width": 1280, "height": 800})
            - user_agent: User-Agent để sử dụng (mặc định: None)
            - handle_javascript: Xử lý JavaScript hay không (mặc định: True)
            - handle_spa: Xử lý Single Page Application hay không (mặc định: False)
            - handle_infinite_scroll: Xử lý infinite scroll hay không (mặc định: False)
            - handle_captcha: Xử lý CAPTCHA hay không (mặc định: False)
            - wait_for_selector: Selector để đợi trước khi trích xuất nội dung (mặc định: None)
            - wait_for_load_state: Trạng thái tải để đợi ("load", "domcontentloaded", "networkidle") (mặc định: "networkidle")
            - extract_images: Trích xuất hình ảnh hay không (mặc định: True)
            - extract_files: Trích xuất files hay không (mặc định: True)
            - extract_html: Trích xuất HTML hay không (mặc định: True)
            - extract_structured_data: Trích xuất dữ liệu có cấu trúc hay không (mặc định: False)
            - download_media: Tải xuống media hay không (mặc định: False)
            - download_path: Đường dẫn để lưu các file đã tải xuống (mặc định: "downloads")
            - max_media_size_mb: Giới hạn kích thước file media có thể tải xuống (mặc định: 10)

    Returns:
        Dict[str, Any]: Kết quả crawl
    """
    if not hasattr(agent, "_adaptive_crawler") or agent._adaptive_crawler is None:
        logger.warning("AdaptiveCrawler not available")
        return {"success": False, "url": url, "error": "AdaptiveCrawler not available"}

    # Kiểm tra xem AdaptiveCrawler có hỗ trợ Playwright không
    if not hasattr(agent._adaptive_crawler, "crawl_with_playwright"):
        logger.warning("Playwright not supported by AdaptiveCrawler")
        return agent._adaptive_crawler.crawl(url, **kwargs)

    # Crawl URL với Playwright
    return agent._adaptive_crawler.crawl_with_playwright(url, **kwargs)

def deep_crawl_with_adaptive_crawler(agent, url: str, **kwargs) -> Dict[str, Any]:
    """
    Thực hiện deep crawl với AdaptiveCrawler.

    Args:
        agent: WebSearchAgentLocal instance
        url: URL cần crawl
        **kwargs: Các tham số bổ sung
            - max_depth: Độ sâu tối đa khi crawl (mặc định: 2)
            - max_pages: Số trang tối đa khi crawl (mặc định: 10)
            - timeout: Thời gian timeout cho mỗi request (mặc định: 60)
            - crawl_mode: Chế độ crawl ("basic", "full_site", "content_only", "media_only") (mặc định: "full_site")
            - extract_images: Trích xuất hình ảnh hay không (mặc định: True)
            - extract_files: Trích xuất files hay không (mặc định: True)
            - extract_html: Trích xuất HTML hay không (mặc định: True)
            - extract_file_content: Trích xuất nội dung file hay không (mặc định: True)
            - download_media: Tải xuống media hay không (mặc định: False)
            - download_path: Đường dẫn để lưu các file đã tải xuống (mặc định: "downloads")
            - max_media_size_mb: Giới hạn kích thước file media có thể tải xuống (mặc định: 10)
            - site_map_enabled: Tạo sitemap cho trang web đã crawl (mặc định: False)
            - respect_robots: Tuân thủ robots.txt hay không (mặc định: True)
            - handle_javascript: Xử lý JavaScript hay không (mặc định: False)
            - handle_captcha: Xử lý CAPTCHA hay không (mặc định: False)
            - handle_pagination: Xử lý phân trang hay không (mặc định: True)
            - handle_infinite_scroll: Xử lý infinite scroll hay không (mặc định: False)
            - handle_spa: Xử lý Single Page Application hay không (mặc định: False)
            - language: Ngôn ngữ ưu tiên ("auto", "en", "vi", ...) (mặc định: "auto")
            - detailed_scraping: Trích xuất chi tiết hay không (mặc định: True)
            - extract_structured_data: Trích xuất dữ liệu có cấu trúc hay không (mặc định: False)

    Returns:
        Dict[str, Any]: Kết quả crawl
    """
    # Kiểm tra URL
    if not url:
        error = AdaptiveCrawlerError("URL is empty or None", url=url)
        return handle_adaptive_crawler_error(error, url, "deep_crawl_with_adaptive_crawler")

    # Kiểm tra URL hợp lệ
    try:
        url = clean_url(url)
    except Exception as e:
        error = AdaptiveCrawlerError(f"Invalid URL: {str(e)}", url=url)
        return handle_adaptive_crawler_error(error, url, "deep_crawl_with_adaptive_crawler")

    # Kiểm tra AdaptiveCrawler có sẵn không
    if not hasattr(agent, "_adaptive_crawler") or agent._adaptive_crawler is None:
        error = AdaptiveCrawlerNotAvailableError("AdaptiveCrawler not available", url=url)
        logger.warning(f"AdaptiveCrawler not available for URL: {url}")
        return handle_adaptive_crawler_error(error, url, "deep_crawl_with_adaptive_crawler")

    # Xác định mức độ phức tạp
    max_depth = kwargs.get("max_depth", 2)
    complexity_level = "medium"
    if max_depth == 0:
        complexity_level = "simple"
    elif max_depth >= 2:
        complexity_level = "complex"

    # Xác định chế độ crawl
    crawl_mode = kwargs.get("crawl_mode", "full_site")
    if crawl_mode not in ["basic", "full_site", "content_only", "media_only"]:
        logger.warning(f"Invalid crawl_mode: {crawl_mode}, using 'full_site' instead")
        crawl_mode = "full_site"

    # Xác định ngôn ngữ
    language = kwargs.get("language", "auto")

    # Xác định đường dẫn tải xuống
    download_path = kwargs.get("download_path", "downloads")

    # Xác định kích thước tối đa của file media
    max_media_size_mb = kwargs.get("max_media_size_mb", 10)

    # Crawl URL
    try:
        result = agent._adaptive_crawler.crawl(
            urls=[url],
            complexity_level=complexity_level,
            max_depth=max_depth,
            max_pages=kwargs.get("max_pages", 10),
            timeout=kwargs.get("timeout", 60),
            detailed_scraping=kwargs.get("detailed_scraping", True),
            crawl_mode=crawl_mode,
            extract_images=kwargs.get("extract_images", True),
            extract_files=kwargs.get("extract_files", True),
            extract_html=kwargs.get("extract_html", True),
            extract_file_content=kwargs.get("extract_file_content", True),
            download_media=kwargs.get("download_media", False),
            download_path=download_path,
            max_media_size_mb=max_media_size_mb,
            site_map_enabled=kwargs.get("site_map_enabled", False),
            respect_robots=kwargs.get("respect_robots", True),
            handle_javascript=kwargs.get("handle_javascript", False),
            handle_captcha=kwargs.get("handle_captcha", False),
            handle_pagination=kwargs.get("handle_pagination", True),
            handle_infinite_scroll=kwargs.get("handle_infinite_scroll", False),
            handle_spa=kwargs.get("handle_spa", False),
            language=language,
            extract_structured_data=kwargs.get("extract_structured_data", False)
        )
    except ConnectionError as e:
        error = AdaptiveCrawlerConnectionError(f"Connection error: {str(e)}", url=url)
        return handle_adaptive_crawler_error(error, url, "deep_crawl_with_adaptive_crawler")
    except TimeoutError as e:
        error = AdaptiveCrawlerTimeoutError(f"Timeout error: {str(e)}", url=url)
        return handle_adaptive_crawler_error(error, url, "deep_crawl_with_adaptive_crawler")
    except Exception as e:
        error = AdaptiveCrawlerError(f"Error during crawl: {str(e)}", url=url)
        return handle_adaptive_crawler_error(error, url, "deep_crawl_with_adaptive_crawler")

    # Chuyển đổi kết quả sang định dạng tương thích với deep_crawl
    if not result.get("success", False):
        error_message = result.get("error", "Unknown error")
        error = AdaptiveCrawlerError(error_message, url=url, details=result)
        return handle_adaptive_crawler_error(error, url, "deep_crawl_with_adaptive_crawler")

    # Lấy kết quả
    results = result.get("results", [])
    if not results:
        error = AdaptiveCrawlerContentExtractionError("No results", url=url)
        return handle_adaptive_crawler_error(error, url, "deep_crawl_with_adaptive_crawler")

    # Tổng hợp nội dung từ tất cả các kết quả
    all_content = []
    all_links = []
    all_images = []
    all_files = []
    all_metadata = {}
    all_structured_data = {}
    downloaded_files = []

    for item in results:
        if "content" in item and item["content"]:
            all_content.append(item["content"])

        if "links" in item and item["links"]:
            all_links.extend(item["links"])

        if "images" in item and item["images"]:
            all_images.extend(item["images"])

        if "files" in item and item["files"]:
            all_files.extend(item["files"])

        if "metadata" in item and item["metadata"]:
            all_metadata.update(item["metadata"])

        if "structured_data" in item and item["structured_data"]:
            for key, value in item["structured_data"].items():
                if key not in all_structured_data:
                    all_structured_data[key] = []
                all_structured_data[key].extend(value)

        if "downloaded_files" in item and item["downloaded_files"]:
            downloaded_files.extend(item["downloaded_files"])

    # Trả về kết quả
    return {
        "success": True,
        "url": url,
        "content": "\n\n".join(all_content),
        "links": all_links,
        "images": all_images,
        "files": all_files,
        "metadata": all_metadata,
        "structured_data": all_structured_data,
        "downloaded_files": downloaded_files,
        "crawl_stats": {
            "pages_crawled": len(results),
            "total_content_length": sum(len(item.get("content", "")) for item in results),
            "total_links": len(all_links),
            "total_images": len(all_images),
            "total_files": len(all_files),
            "total_downloaded_files": len(downloaded_files),
            "crawl_mode": crawl_mode,
            "language": language,
            "max_depth": max_depth,
            "complexity_level": complexity_level
        }
    }
