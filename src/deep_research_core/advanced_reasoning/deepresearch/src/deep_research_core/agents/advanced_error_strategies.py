"""
<PERSON><PERSON><PERSON> chiến lược xử lý lỗi nâng cao cho WebSearchAgent.

Module này cung cấp các chiến lược xử lý lỗi nâng cao cho WebSearchAgent,
bao gồm xử lý CAPTCHA, lỗi mạng, và cải tiến truy vấn ngữ nghĩa.
"""

import re
import time
import random
import logging
from typing import Dict, Any, List, Optional, Union, Tuple
from datetime import datetime
from collections import defaultdict

from src.deep_research_core.utils.error_recovery import ErrorRecoveryStrategy
from src.deep_research_core.utils.error_recovery import InputReformulationStrategy

# Thiết lập logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CaptchaHandlingStrategy(ErrorRecoveryStrategy):
    """
    Chiến lược xử lý CAPTCHA.
    
    <PERSON>ến lược này cố gắng xử lý CAPTCHA bằng cách:
    1. Thay đổi User-Agent
    2. Thay đổi proxy
    3. <PERSON><PERSON> một khoảng thời gian
    4. Thử lại với công cụ tìm kiếm khác
    """
    
    name = "captcha_handling"
    description = "Xử lý CAPTCHA bằng cách thay đổi User-Agent, proxy, và chờ đợi"
    
    def __init__(
        self,
        user_agents: Optional[List[str]] = None,
        proxies: Optional[List[str]] = None,
        wait_time: float = 60.0,
        max_retries: int = 3,
        **kwargs
    ):
        """
        Khởi tạo chiến lược xử lý CAPTCHA.
        
        Args:
            user_agents: Danh sách User-Agent để luân chuyển
            proxies: Danh sách proxy để luân chuyển
            wait_time: Thời gian chờ (giây) trước khi thử lại
            max_retries: Số lần thử lại tối đa
            **kwargs: Tham số bổ sung
        """
        super().__init__(**kwargs)
        self.user_agents = user_agents or [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36"
        ]
        self.proxies = proxies or []
        self.wait_time = wait_time
        self.max_retries = max_retries
        self.current_retry = 0
    
    def recover(
        self,
        error: Exception,
        context: Dict[str, Any],
        **kwargs
    ) -> Dict[str, Any]:
        """
        Cố gắng khôi phục từ lỗi CAPTCHA.
        
        Args:
            error: Lỗi gặp phải
            context: Ngữ cảnh lỗi
            **kwargs: Tham số bổ sung
            
        Returns:
            Kết quả khôi phục
        """
        # Kiểm tra xem đã vượt quá số lần thử lại chưa
        if context.get("captcha_retries", 0) >= self.max_retries:
            return {
                "success": False,
                "strategy": self.name,
                "error": str(error),
                "message": f"Đã vượt quá số lần thử lại xử lý CAPTCHA ({self.max_retries})"
            }
        
        # Tăng số lần thử lại
        context["captcha_retries"] = context.get("captcha_retries", 0) + 1
        
        # Lấy thông tin công cụ tìm kiếm và truy vấn
        tool_args = context.get("tool_args", {})
        engine = tool_args.get("engine", "")
        
        # Thay đổi User-Agent
        if self.user_agents:
            new_user_agent = random.choice(self.user_agents)
            tool_args["headers"] = tool_args.get("headers", {})
            tool_args["headers"]["User-Agent"] = new_user_agent
            logger.info(f"Thay đổi User-Agent: {new_user_agent}")
        
        # Thay đổi proxy
        if self.proxies:
            new_proxy = random.choice(self.proxies)
            tool_args["proxy"] = new_proxy
            logger.info(f"Thay đổi proxy: {new_proxy}")
        
        # Chờ một khoảng thời gian
        wait_time = self.wait_time * (1 + random.random())
        logger.info(f"Chờ {wait_time:.2f}s trước khi thử lại sau lỗi CAPTCHA")
        time.sleep(wait_time)
        
        return {
            "success": True,
            "strategy": self.name,
            "message": f"Đã thử lại sau lỗi CAPTCHA (lần {context['captcha_retries']}/{self.max_retries})",
            "original_args": context.get("tool_args", {}),
            "reformulated_args": tool_args
        }

class NetworkErrorStrategy(ErrorRecoveryStrategy):
    """
    Chiến lược xử lý lỗi mạng.
    
    Chiến lược này cố gắng xử lý lỗi mạng bằng cách:
    1. Thay đổi timeout
    2. Thay đổi proxy
    3. Thử lại với backoff
    """
    
    name = "network_error"
    description = "Xử lý lỗi mạng bằng cách thay đổi timeout, proxy, và thử lại với backoff"
    
    def __init__(
        self,
        initial_timeout: float = 10.0,
        max_timeout: float = 60.0,
        backoff_factor: float = 2.0,
        max_retries: int = 3,
        proxies: Optional[List[str]] = None,
        **kwargs
    ):
        """
        Khởi tạo chiến lược xử lý lỗi mạng.
        
        Args:
            initial_timeout: Timeout ban đầu (giây)
            max_timeout: Timeout tối đa (giây)
            backoff_factor: Hệ số backoff
            max_retries: Số lần thử lại tối đa
            proxies: Danh sách proxy để luân chuyển
            **kwargs: Tham số bổ sung
        """
        super().__init__(**kwargs)
        self.initial_timeout = initial_timeout
        self.max_timeout = max_timeout
        self.backoff_factor = backoff_factor
        self.max_retries = max_retries
        self.proxies = proxies or []
    
    def recover(
        self,
        error: Exception,
        context: Dict[str, Any],
        **kwargs
    ) -> Dict[str, Any]:
        """
        Cố gắng khôi phục từ lỗi mạng.
        
        Args:
            error: Lỗi gặp phải
            context: Ngữ cảnh lỗi
            **kwargs: Tham số bổ sung
            
        Returns:
            Kết quả khôi phục
        """
        # Kiểm tra xem đã vượt quá số lần thử lại chưa
        if context.get("network_retries", 0) >= self.max_retries:
            return {
                "success": False,
                "strategy": self.name,
                "error": str(error),
                "message": f"Đã vượt quá số lần thử lại xử lý lỗi mạng ({self.max_retries})"
            }
        
        # Tăng số lần thử lại
        retry_count = context.get("network_retries", 0) + 1
        context["network_retries"] = retry_count
        
        # Lấy thông tin công cụ tìm kiếm và truy vấn
        tool_args = context.get("tool_args", {}).copy()
        
        # Tính timeout mới với backoff
        current_timeout = tool_args.get("timeout", self.initial_timeout)
        new_timeout = min(current_timeout * self.backoff_factor, self.max_timeout)
        tool_args["timeout"] = new_timeout
        
        # Thay đổi proxy nếu có
        if self.proxies:
            new_proxy = random.choice(self.proxies)
            tool_args["proxy"] = new_proxy
            logger.info(f"Thay đổi proxy: {new_proxy}")
        
        # Chờ với backoff
        wait_time = (2 ** retry_count) * (0.5 + random.random())
        logger.info(f"Chờ {wait_time:.2f}s trước khi thử lại sau lỗi mạng (timeout: {new_timeout}s)")
        time.sleep(wait_time)
        
        return {
            "success": True,
            "strategy": self.name,
            "message": f"Đã thử lại sau lỗi mạng (lần {retry_count}/{self.max_retries}, timeout: {new_timeout}s)",
            "original_args": context.get("tool_args", {}),
            "reformulated_args": tool_args
        }

class SemanticQueryReformulationStrategy(InputReformulationStrategy):
    """
    Chiến lược cải tiến truy vấn dựa trên ngữ nghĩa.
    
    Chiến lược này cải tiến truy vấn dựa trên phân tích ngữ nghĩa và loại lỗi.
    """
    
    name = "semantic_query_reformulation"
    description = "Cải tiến truy vấn dựa trên phân tích ngữ nghĩa và loại lỗi"
    
    def __init__(
        self,
        max_reformulations: int = 3,
        **kwargs
    ):
        """
        Khởi tạo chiến lược cải tiến truy vấn ngữ nghĩa.
        
        Args:
            max_reformulations: Số lần cải tiến tối đa
            **kwargs: Tham số bổ sung
        """
        super().__init__(**kwargs)
        self.max_reformulations = max_reformulations
        
        # Các mẫu cải tiến truy vấn
        self.reformulation_patterns = {
            "no_results": [
                # Loại bỏ các từ khóa quá cụ thể
                (r"(\w+\s+){5,}", r"\1\2\3"),
                # Loại bỏ các ký tự đặc biệt
                (r"[^\w\s]", r" "),
                # Loại bỏ các từ khóa hiếm
                (r"\b(specific|rare|unique|obscure)\b", r""),
                # Thêm từ khóa phổ biến
                (r"^(.+)$", r"\1 guide tutorial")
            ],
            "too_many_results": [
                # Thêm dấu ngoặc kép
                (r"^(.+)$", r'"\1"'),
                # Thêm từ khóa cụ thể
                (r"^(.+)$", r"\1 detailed specific"),
                # Thêm giới hạn thời gian
                (r"^(.+)$", r"\1 recent")
            ],
            "irrelevant_results": [
                # Thêm từ khóa liên quan
                (r"^(.+)$", r"\1 relevant"),
                # Loại bỏ từ khóa gây nhầm lẫn
                (r"\b(vs|versus|compared to|alternative)\b", r""),
                # Thêm từ khóa định hướng
                (r"^(.+)$", r"\1 exact")
            ]
        }
    
    def recover(
        self,
        error: Exception,
        context: Dict[str, Any],
        **kwargs
    ) -> Dict[str, Any]:
        """
        Cố gắng khôi phục bằng cách cải tiến truy vấn.
        
        Args:
            error: Lỗi gặp phải
            context: Ngữ cảnh lỗi
            **kwargs: Tham số bổ sung
            
        Returns:
            Kết quả khôi phục
        """
        # Kiểm tra xem đã vượt quá số lần cải tiến chưa
        if context.get("semantic_reformulations", 0) >= self.max_reformulations:
            return {
                "success": False,
                "strategy": self.name,
                "error": str(error),
                "message": f"Đã vượt quá số lần cải tiến truy vấn ({self.max_reformulations})"
            }
        
        # Tăng số lần cải tiến
        context["semantic_reformulations"] = context.get("semantic_reformulations", 0) + 1
        
        # Lấy thông tin truy vấn
        tool_args = context.get("tool_args", {}).copy()
        query = tool_args.get("query", "")
        if not query:
            return {
                "success": False,
                "strategy": self.name,
                "error": str(error),
                "message": "Không tìm thấy truy vấn trong ngữ cảnh"
            }
        
        # Xác định loại lỗi
        error_str = str(error).lower()
        error_type = "no_results"  # Mặc định
        
        if "no results" in error_str or "not found" in error_str or "zero results" in error_str:
            error_type = "no_results"
        elif "too many" in error_str or "too broad" in error_str:
            error_type = "too_many_results"
        elif "irrelevant" in error_str or "not relevant" in error_str:
            error_type = "irrelevant_results"
        
        # Cải tiến truy vấn dựa trên loại lỗi
        reformulated_query = self._reformulate_query(query, error_type)
        
        # Cập nhật tool_args
        tool_args["query"] = reformulated_query
        
        return {
            "success": True,
            "strategy": self.name,
            "message": f"Đã cải tiến truy vấn: '{query}' -> '{reformulated_query}'",
            "original_args": context.get("tool_args", {}),
            "reformulated_args": tool_args,
            "original_query": query,
            "reformulated_query": reformulated_query
        }
    
    def _reformulate_query(self, query: str, error_type: str) -> str:
        """
        Cải tiến truy vấn dựa trên loại lỗi.
        
        Args:
            query: Truy vấn gốc
            error_type: Loại lỗi
            
        Returns:
            Truy vấn đã cải tiến
        """
        # Lấy các mẫu cải tiến cho loại lỗi
        patterns = self.reformulation_patterns.get(error_type, [])
        
        # Áp dụng các mẫu cải tiến
        reformulated_query = query
        for pattern, replacement in patterns:
            try:
                new_query = re.sub(pattern, replacement, reformulated_query)
                if new_query != reformulated_query:
                    reformulated_query = new_query
                    break
            except Exception as e:
                logger.error(f"Lỗi khi áp dụng mẫu cải tiến: {str(e)}")
        
        # Nếu không có mẫu nào được áp dụng, thử cải tiến đơn giản
        if reformulated_query == query:
            if error_type == "no_results":
                # Đơn giản hóa truy vấn
                words = query.split()
                if len(words) > 3:
                    reformulated_query = " ".join(words[:3])
            elif error_type == "too_many_results":
                # Thêm dấu ngoặc kép
                reformulated_query = f'"{query}"'
            elif error_type == "irrelevant_results":
                # Thêm từ khóa "exact"
                reformulated_query = f"{query} exact"
        
        return reformulated_query
