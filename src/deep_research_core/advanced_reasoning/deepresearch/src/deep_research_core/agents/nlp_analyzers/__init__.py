"""
NLP analyzers package for natural language processing in deep research core.

This package contains various NLP analyzers for text processing, sentiment analysis,
factuality detection, and other NLP-related tasks.
"""

from typing import Dict, Any, Optional

# Try to import transformer sentiment analyzer if available
try:
    from .transformer_sentiment_analyzer import (
        TransformerSentimentAnalyzer,
        VietnameseSentimentEnhanced,
        get_sentiment_analyzer,
        TRANSFORMERS_AVAILABLE
    )
except ImportError:
    # Define placeholder for when imports fail
    TRANSFORMERS_AVAILABLE = False

    def get_sentiment_analyzer(**kwargs) -> Dict[str, Any]:
        """Placeholder for when transformer models are not available."""
        return {
            "error": "Transformer sentiment analyzer not available. Install required dependencies."
        } 