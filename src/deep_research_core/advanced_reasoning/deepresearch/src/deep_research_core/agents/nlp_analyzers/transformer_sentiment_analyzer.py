"""
Transformer-based sentiment analyzer for enhanced text sentiment analysis.

This module provides sentiment analysis using state-of-the-art transformer models,
optimized for both English and Vietnamese text.
"""

import os
import logging
from typing import Dict, Any, List, Optional, Union, Tuple
import json
import re

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Check for transformer and torch availability
try:
    import torch
    import transformers
    from transformers import (
        AutoModelForSequenceClassification, 
        AutoTokenizer,
        pipeline
    )
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    logger.warning("Transformers or PyTorch not available. Install with: pip install transformers torch")


class TransformerSentimentAnalyzer:
    """
    Sentiment analyzer using transformer models.
    
    This class provides high-quality sentiment analysis for text content
    using pre-trained transformer models. It supports both English and
    Vietnamese text.
    """
    
    # Default models for different languages
    DEFAULT_MODELS = {
        "en": "distilbert-base-uncased-finetuned-sst-2-english",
        "vi": "vinai/phobert-base-vietnamese-sentiment"
    }
    
    def __init__(
        self,
        model_name: Optional[str] = None,
        language: str = "auto",
        device: str = "auto",
        cache_dir: Optional[str] = None,
        batch_size: int = 8,
        max_length: int = 512
    ):
        """
        Initialize the TransformerSentimentAnalyzer.
        
        Args:
            model_name: Name of the transformer model to use (if None, chooses based on language)
            language: Language code ("en", "vi", or "auto" for auto-detection)
            device: Device to run inference on ("cpu", "cuda", "auto")
            cache_dir: Directory to cache downloaded models
            batch_size: Batch size for processing multiple texts
            max_length: Maximum sequence length for tokenization
        """
        self.language = language
        self.model_name = model_name
        self.cache_dir = cache_dir
        self.batch_size = batch_size
        self.max_length = max_length
        
        # Check for transformers availability
        if not TRANSFORMERS_AVAILABLE:
            logger.error("Cannot initialize TransformerSentimentAnalyzer: transformers library not available")
            self.model = None
            self.tokenizer = None
            self.pipeline = None
            self.is_ready = False
            return
        
        # Set device
        if device == "auto":
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            self.device = device
            
        # Initialize model
        self.is_ready = self._initialize_model()
    
    def _initialize_model(self) -> bool:
        """
        Initialize the transformer model for sentiment analysis.
        
        Returns:
            True if initialization was successful, False otherwise
        """
        if not TRANSFORMERS_AVAILABLE:
            return False
        
        try:
            # Select model based on language if not specified
            if self.model_name is None:
                if self.language == "vi":
                    self.model_name = self.DEFAULT_MODELS["vi"]
                else:
                    self.model_name = self.DEFAULT_MODELS["en"]
                    
            logger.info(f"Loading model: {self.model_name} on {self.device}")
            
            # Load tokenizer and model
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_name,
                cache_dir=self.cache_dir
            )
            
            self.model = AutoModelForSequenceClassification.from_pretrained(
                self.model_name,
                cache_dir=self.cache_dir
            )
            
            # Move model to appropriate device
            self.model = self.model.to(self.device)
            
            # Create sentiment pipeline
            self.pipeline = pipeline(
                "sentiment-analysis",
                model=self.model,
                tokenizer=self.tokenizer,
                device=0 if self.device == "cuda" else -1,
                batch_size=self.batch_size
            )
            
            # Get label mapping
            self.id2label = self.model.config.id2label if hasattr(self.model.config, "id2label") else None
            
            logger.info(f"Model loaded successfully: {self.model_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing transformer model: {str(e)}")
            self.model = None
            self.tokenizer = None
            self.pipeline = None
            return False
    
    def _detect_language(self, text: str) -> str:
        """
        Detect the language of the text.
        
        Args:
            text: Text to analyze
            
        Returns:
            Language code ("en" or "vi")
        """
        # Simple rule-based detection for Vietnamese
        vietnamese_chars = set("àáảãạăắằẳẵặâấầẩẫậèéẻẽẹêếềểễệìíỉĩịòóỏõọôốồổỗộơớờởỡợùúủũụưứừửữựỳýỷỹỵđ")
        text_lower = text.lower()
        
        # Count Vietnamese characters
        vn_char_count = sum(1 for c in text_lower if c in vietnamese_chars)
        
        # If there are Vietnamese characters, it's likely Vietnamese
        if vn_char_count > 0:
            return "vi"
            
        # Otherwise, try using langdetect if available
        try:
            from langdetect import detect
            lang = detect(text)
            if lang == "vi":
                return "vi"
        except:
            pass
            
        # Default to English
        return "en"
    
    def _preprocess_text(self, text: str, language: str) -> str:
        """
        Preprocess text for sentiment analysis.
        
        Args:
            text: Text to preprocess
            language: Language of the text
            
        Returns:
            Preprocessed text
        """
        # Basic cleanup
        text = text.strip()
        
        # Remove URLs
        text = re.sub(r'https?://\S+', '', text)
        
        # Remove excess whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Language-specific preprocessing
        if language == "vi":
            # Vietnamese-specific preprocessing if needed
            pass
            
        return text
    
    def _normalize_sentiment_score(self, raw_result: Dict[str, Any], language: str) -> Tuple[str, float]:
        """
        Normalize sentiment scores to a consistent format.
        
        Args:
            raw_result: Raw output from the transformer model
            language: Language of the analyzed text
            
        Returns:
            Tuple of (sentiment_label, normalized_score)
        """
        label = raw_result.get("label", "NEUTRAL")
        score = raw_result.get("score", 0.5)
        
        # For English models (typically POSITIVE/NEGATIVE)
        if language == "en":
            if label.upper() == "POSITIVE":
                return "positive", score
            elif label.upper() == "NEGATIVE":
                return "negative", -score
            else:
                return "neutral", 0.0
        
        # For Vietnamese models (may have different label schemes)
        elif language == "vi":
            if label.upper() in ["POSITIVE", "POS", "TÍCH CỰC"]:
                return "positive", score
            elif label.upper() in ["NEGATIVE", "NEG", "TIÊU CỰC"]:
                return "negative", -score
            else:
                return "neutral", 0.0
        
        # Default case
        return "neutral", 0.0
    
    def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """
        Analyze the sentiment of a text using transformer models.
        
        Args:
            text: Text to analyze
            
        Returns:
            Dictionary with sentiment analysis results
        """
        if not self.is_ready or not text:
            return {
                "sentiment": "neutral",
                "score": 0.0,
                "method": "none",
                "error": "Model not available or text empty"
            }
        
        try:
            # Detect language if set to auto
            lang = self._detect_language(text) if self.language == "auto" else self.language
            
            # Preprocess text
            processed_text = self._preprocess_text(text, lang)
            
            # Check if we need to reinitialize with a different model
            if self.language == "auto" and lang != "en" and self.model_name == self.DEFAULT_MODELS["en"]:
                # Save current model name
                current_model = self.model_name
                
                # Set language-specific model
                self.model_name = self.DEFAULT_MODELS.get(lang, self.DEFAULT_MODELS["en"])
                
                # Only reinitialize if different model needed
                if current_model != self.model_name:
                    logger.info(f"Switching to {lang} model: {self.model_name}")
                    self._initialize_model()
            
            # Truncate if necessary
            if len(processed_text) > self.max_length * 4:  # Rough character estimate
                processed_text = processed_text[:self.max_length * 4]
            
            # Run inference
            raw_result = self.pipeline(processed_text)[0]
            
            # Normalize results
            sentiment, score = self._normalize_sentiment_score(raw_result, lang)
            
            # Return standardized result
            return {
                "sentiment": sentiment,
                "score": score,
                "confidence": raw_result.get("score", 0.0),
                "raw_label": raw_result.get("label", ""),
                "language": lang,
                "method": "transformer",
                "model": self.model_name
            }
            
        except Exception as e:
            logger.error(f"Error in transformer sentiment analysis: {str(e)}")
            return {
                "sentiment": "neutral",
                "score": 0.0,
                "method": "error",
                "error": str(e)
            }
    
    def analyze_sentiment_batch(self, texts: List[str]) -> List[Dict[str, Any]]:
        """
        Analyze sentiment for a batch of texts.
        
        Args:
            texts: List of texts to analyze
            
        Returns:
            List of sentiment analysis results
        """
        if not self.is_ready:
            return [{"sentiment": "neutral", "score": 0.0, "method": "none", "error": "Model not available"}] * len(texts)
        
        results = []
        
        try:
            # Process in batches
            for i in range(0, len(texts), self.batch_size):
                batch = texts[i:i + self.batch_size]
                batch_results = [self.analyze_sentiment(text) for text in batch]
                results.extend(batch_results)
                
            return results
            
        except Exception as e:
            logger.error(f"Error in batch sentiment analysis: {str(e)}")
            return [{"sentiment": "neutral", "score": 0.0, "method": "error", "error": str(e)}] * len(texts)
    
    def is_available(self) -> bool:
        """
        Check if the transformer model is available.
        
        Returns:
            True if available, False otherwise
        """
        return self.is_ready


class VietnameseSentimentEnhanced:
    """
    Enhanced sentiment analyzer specifically for Vietnamese text.
    
    This class combines transformer-based analysis with rule-based approaches
    to provide high-quality sentiment analysis for Vietnamese text.
    """
    
    def __init__(self, use_transformers: bool = True):
        """
        Initialize the VietnameseSentimentEnhanced analyzer.
        
        Args:
            use_transformers: Whether to use transformer models if available
        """
        self.use_transformers = use_transformers
        
        # Initialize transformer analyzer if requested
        self.transformer_analyzer = None
        if use_transformers and TRANSFORMERS_AVAILABLE:
            self.transformer_analyzer = TransformerSentimentAnalyzer(
                language="vi",
                model_name="vinai/phobert-base-vietnamese-sentiment"
            )
        
        # Load Vietnamese sentiment lexicon
        self.positive_words = [
            "tốt", "hay", "tuyệt vời", "xuất sắc", "tích cực", "hữu ích",
            "hiệu quả", "thành công", "đẹp", "đáng khen", "tài năng", "giỏi",
            "mạnh mẽ", "phù hợp", "thông minh", "nhanh chóng", "tiện lợi",
            "đáng tin cậy", "chính xác", "hấp dẫn", "thú vị", "vui vẻ",
            "hạnh phúc", "phấn khởi", "bình an", "thoải mái", "hài lòng",
            "giá trị", "giàu có", "tiến bộ", "phát triển", "sáng tạo",
            "cải tiến", "cập nhật", "dễ dàng", "thuận tiện", "ổn định",
            "an toàn", "tiết kiệm", "yêu thích", "ủng hộ", "dễ chịu",
            "thoải mái", "đơn giản", "tự nhiên", "trung thành", "rõ ràng"
        ]
        
        self.negative_words = [
            "tệ", "kém", "tồi", "xấu", "thất bại", "sai lầm", "yếu",
            "lỗi thời", "bất lợi", "phức tạp", "khó khăn", "nguy hiểm",
            "thiếu", "lỗi", "chậm", "đắt", "buồn", "tức giận", "thất vọng",
            "lo lắng", "sợ hãi", "đau khổ", "cáu kỉnh", "phiền muộn",
            "khó chịu", "tồi tệ", "tồi tệ nhất", "kinh khủng", "dễ vỡ",
            "dễ hỏng", "lừa đảo", "kém chất lượng", "không hiệu quả",
            "không đáng tin cậy", "không chính xác", "lãng phí", "giả",
            "thô sơ", "căng thẳng", "phức tạp", "hỗn loạn", "mơ hồ", 
            "vô lý", "chán nản", "màu mè", "cẩu thả", "hời hợt", "cũ kỹ"
        ]
        
        # Vietnamese intensifiers (boost sentiment scores)
        self.intensifiers = [
            "rất", "quá", "cực kỳ", "vô cùng", "hết sức", "cực", "siêu", 
            "đặc biệt", "hoàn toàn", "tuyệt đối", "vô cùng", "cực kỳ",
            "phi thường", "lắm", "lạ thường", "không thể tin"
        ]
        
        # Vietnamese negations (flip sentiment polarity)
        self.negations = [
            "không", "chẳng", "đâu", "chưa", "không phải", "không được",
            "không còn", "không hề", "không thể", "không nên", "không bao giờ",
            "chẳng thể", "chẳng bao giờ", "chẳng còn", "chẳng hề", "chẳng được",
            "chả", "đừng", "đéo", "không đúng", "thiếu"
        ]
        
        # Try to load underthesea for Vietnamese NLP if available
        try:
            from underthesea import sentiment
            self.underthesea_available = True
        except ImportError:
            self.underthesea_available = False
    
    def _rule_based_analysis(self, text: str) -> Dict[str, Any]:
        """
        Perform rule-based sentiment analysis for Vietnamese text.
        
        Args:
            text: Vietnamese text to analyze
            
        Returns:
            Dictionary with sentiment analysis results
        """
        text_lower = text.lower()
        words = re.findall(r'\b\w+\b', text_lower)
        
        # Count sentiment words
        pos_count = 0
        neg_count = 0
        
        # Track positions of negations and intensifiers
        negation_positions = [i for i, word in enumerate(words) if word in self.negations]
        intensifier_positions = [i for i, word in enumerate(words) if word in self.intensifiers]
        
        # Analyze each word
        for i, word in enumerate(words):
            # Check if word is preceded by negation (within 2 positions)
            negated = any(neg_pos < i and i - neg_pos <= 2 for neg_pos in negation_positions)
            
            # Check if word is preceded by intensifier (within 2 positions)
            intensified = any(int_pos < i and i - int_pos <= 2 for int_pos in intensifier_positions)
            intensity_factor = 2.0 if intensified else 1.0
            
            # Check sentiment
            if word in self.positive_words:
                if negated:
                    neg_count += intensity_factor
                else:
                    pos_count += intensity_factor
            elif word in self.negative_words:
                if negated:
                    pos_count += intensity_factor * 0.5  # Negated negative is less positive than actual positive
                else:
                    neg_count += intensity_factor
        
        # Calculate sentiment score
        total_sentiment_words = pos_count + neg_count
        if total_sentiment_words == 0:
            return {"sentiment": "neutral", "score": 0.0, "method": "rule-based"}
        
        # Normalize to [-1, 1] range
        sentiment_score = (pos_count - neg_count) / total_sentiment_words
        
        # Determine sentiment
        if sentiment_score > 0.1:
            sentiment = "positive"
        elif sentiment_score < -0.1:
            sentiment = "negative"
        else:
            sentiment = "neutral"
        
        # Return result
        return {
            "sentiment": sentiment,
            "score": sentiment_score,
            "positive_count": pos_count,
            "negative_count": neg_count,
            "method": "rule-based"
        }
    
    def _underthesea_analysis(self, text: str) -> Dict[str, Any]:
        """
        Perform sentiment analysis using Underthesea library.
        
        Args:
            text: Vietnamese text to analyze
            
        Returns:
            Dictionary with sentiment analysis results
        """
        if not self.underthesea_available:
            # Cố gắng import động underthesea
            try:
                import importlib.util
                spec = importlib.util.find_spec("underthesea")
                if spec is not None:
                    # Module có sẵn nhưng chưa được import
                    underthesea = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(underthesea)
                    
                    # Đánh dấu là có sẵn
                    self.underthesea_available = True
                    
                    # Thực hiện phân tích
                    try:
                        result = underthesea.sentiment(text)
                        
                        # Map underthesea results to our format
                        if result == "positive":
                            return {"sentiment": "positive", "score": 0.7, "method": "underthesea"}
                        elif result == "negative":
                            return {"sentiment": "negative", "score": -0.7, "method": "underthesea"}
                        else:
                            return {"sentiment": "neutral", "score": 0.0, "method": "underthesea"}
                    except Exception as e:
                        logger.error(f"Error in underthesea sentiment analysis: {str(e)}")
                else:
                    return {"sentiment": "neutral", "score": 0.0, "method": "none", "error": "Underthesea not available"}
            except Exception as e:
                return {"sentiment": "neutral", "score": 0.0, "method": "none", "error": f"Underthesea import error: {str(e)}"}
        
        try:
            from underthesea import sentiment
            
            # Get sentiment from underthesea
            result = sentiment(text)
            
            # Map underthesea results to our format
            if result == "positive":
                return {"sentiment": "positive", "score": 0.7, "method": "underthesea"}
            elif result == "negative":
                return {"sentiment": "negative", "score": -0.7, "method": "underthesea"}
            else:
                return {"sentiment": "neutral", "score": 0.0, "method": "underthesea"}
                
        except Exception as e:
            logger.error(f"Error using Underthesea: {str(e)}")
            return {"sentiment": "neutral", "score": 0.0, "method": "error", "error": str(e)}
    
    def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """
        Analyze sentiment of Vietnamese text using multiple methods.
        
        Args:
            text: Vietnamese text to analyze
            
        Returns:
            Dictionary with sentiment analysis results
        """
        results = {}
        methods_used = []
        
        # Try transformer-based analysis first if available
        if self.transformer_analyzer and self.transformer_analyzer.is_available():
            transformer_result = self.transformer_analyzer.analyze_sentiment(text)
            results["transformer"] = transformer_result
            methods_used.append(("transformer", abs(transformer_result["score"]), transformer_result["score"]))
        
        # Try underthesea if available
        if self.underthesea_available:
            underthesea_result = self._underthesea_analysis(text)
            results["underthesea"] = underthesea_result
            methods_used.append(("underthesea", abs(underthesea_result["score"]), underthesea_result["score"]))
        
        # Always use rule-based analysis as fallback
        rule_based_result = self._rule_based_analysis(text)
        results["rule_based"] = rule_based_result
        methods_used.append(("rule_based", abs(rule_based_result["score"]), rule_based_result["score"]))
        
        # Choose the method with the strongest sentiment signal
        if methods_used:
            # Sort by confidence (absolute score value)
            methods_used.sort(key=lambda x: x[1], reverse=True)
            best_method, _, best_score = methods_used[0]
            
            # Get the best result
            best_result = results[best_method]
            
            # Return combined result
            return {
                "sentiment": best_result["sentiment"],
                "score": best_score,
                "details": results,
                "method": best_method,
                "language": "vi"
            }
        
        # Fallback if no methods succeeded
        return {
            "sentiment": "neutral",
            "score": 0.0,
            "method": "none",
            "error": "No analysis methods available",
            "language": "vi"
        }


# Factory function to get an appropriate sentiment analyzer
def get_sentiment_analyzer(language: str = "auto", use_transformers: bool = True) -> Union[TransformerSentimentAnalyzer, VietnameseSentimentEnhanced]:
    """
    Get an appropriate sentiment analyzer for the specified language.
    
    Args:
        language: Language code ("en", "vi", or "auto")
        use_transformers: Whether to use transformer models if available
        
    Returns:
        Sentiment analyzer instance
    """
    if language == "vi":
        return VietnameseSentimentEnhanced(use_transformers=use_transformers)
    else:
        return TransformerSentimentAnalyzer(language=language)


# Simple test function
def test_sentiment_analyzer():
    """Test the sentiment analyzers."""
    # Test texts
    english_texts = [
        "This product is amazing! I love it so much.",
        "I'm very disappointed with the service. Terrible experience.",
        "The weather is nice today."
    ]
    
    vietnamese_texts = [
        "Sản phẩm này tuyệt vời quá! Tôi rất thích nó.",
        "Tôi rất thất vọng với dịch vụ này. Trải nghiệm tồi tệ.",
        "Thời tiết hôm nay khá đẹp."
    ]
    
    # Test English analyzer
    print("Testing English sentiment analysis:")
    en_analyzer = get_sentiment_analyzer("en")
    for text in english_texts:
        result = en_analyzer.analyze_sentiment(text)
        print(f"Text: {text}")
        print(f"Sentiment: {result['sentiment']}, Score: {result['score']:.2f}, Method: {result['method']}")
        print()
    
    # Test Vietnamese analyzer
    print("\nTesting Vietnamese sentiment analysis:")
    vi_analyzer = get_sentiment_analyzer("vi")
    for text in vietnamese_texts:
        result = vi_analyzer.analyze_sentiment(text)
        print(f"Text: {text}")
        print(f"Sentiment: {result['sentiment']}, Score: {result['score']:.2f}, Method: {result['method']}")
        print()

if __name__ == "__main__":
    test_sentiment_analyzer() 