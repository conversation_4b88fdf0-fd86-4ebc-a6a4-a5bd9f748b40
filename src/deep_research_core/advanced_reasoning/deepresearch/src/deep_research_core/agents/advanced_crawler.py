"""
Advanced crawler module for WebSearchAgent.

This module provides advanced crawling capabilities for WebSearchAgent,
including smart link queue management, authentication handling, and pagination handling.
"""

import os
import time
import json
import logging
import hashlib
from typing import Dict, Any, List, Optional, Tuple, Set, Union, Callable
from urllib.parse import urlparse, urljoin, urldefrag

# Import internal modules
from .link_queue_manager import LinkQueueManager
from .auth_manager import AuthManager
from .pagination_handler import PaginationHandler

# Create logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdvancedCrawler:
    """
    Advanced crawler for WebSearchAgent.
    
    Features:
    - Smart link queue management
    - Authentication handling
    - Pagination handling
    - Content extraction
    - Rate limiting
    - Proxy rotation
    """
    
    def __init__(
        self,
        query: str,
        start_urls: Optional[List[str]] = None,
        max_pages: int = 10,
        max_depth: int = 2,
        max_links_per_domain: int = 5,
        timeout: int = 30,
        follow_links: bool = True,
        respect_robots_txt: bool = True,
        enable_state_saving: bool = True,
        state_dir: Optional[str] = None,
        enable_auth: bool = False,
        credentials_file: Optional[str] = None,
        cookie_file: Optional[str] = None,
        enable_pagination: bool = True,
        max_pagination_pages: int = 5,
        enable_load_more: bool = True,
        enable_infinite_scroll: bool = True,
        wait_time: float = 1.0,
        max_wait_time: float = 30.0,
        rate_limit: int = 10,  # requests per minute
        use_playwright: bool = False,
        use_proxy: bool = False,
        proxy_list: Optional[List[str]] = None,
        user_agent_rotation: bool = False,
        user_agent_list: Optional[List[str]] = None,
        extract_content: bool = True,
        extract_links: bool = True,
        extract_images: bool = False,
        max_content_length: int = 100000,
        optimize_for_llm: bool = True,
        language: str = "auto"
    ):
        """
        Initialize the AdvancedCrawler.
        
        Args:
            query: Search query
            start_urls: List of URLs to start crawling from
            max_pages: Maximum number of pages to crawl
            max_depth: Maximum depth to crawl
            max_links_per_domain: Maximum links to crawl per domain
            timeout: Timeout for requests (seconds)
            follow_links: Whether to follow links
            respect_robots_txt: Whether to respect robots.txt
            enable_state_saving: Whether to save crawl state
            state_dir: Directory for saving state
            enable_auth: Whether to enable authentication
            credentials_file: Path to credentials file
            cookie_file: Path to cookie file
            enable_pagination: Whether to enable pagination handling
            max_pagination_pages: Maximum number of pagination pages to process
            enable_load_more: Whether to enable "Load More" button handling
            enable_infinite_scroll: Whether to enable infinite scroll handling
            wait_time: Time to wait between requests (seconds)
            max_wait_time: Maximum time to wait for page load (seconds)
            rate_limit: Maximum requests per minute
            use_playwright: Whether to use Playwright for rendering
            use_proxy: Whether to use proxies
            proxy_list: List of proxies to use
            user_agent_rotation: Whether to rotate user agents
            user_agent_list: List of user agents to use
            extract_content: Whether to extract content
            extract_links: Whether to extract links
            extract_images: Whether to extract images
            max_content_length: Maximum content length to extract
            optimize_for_llm: Whether to optimize content for LLM
            language: Language code
        """
        self.query = query
        self.start_urls = start_urls or []
        self.max_pages = max_pages
        self.max_depth = max_depth
        self.max_links_per_domain = max_links_per_domain
        self.timeout = timeout
        self.follow_links = follow_links
        self.respect_robots_txt = respect_robots_txt
        self.enable_state_saving = enable_state_saving
        self.state_dir = state_dir or os.path.join(os.path.expanduser("~"), ".deep_research_core", "crawler_state")
        self.enable_auth = enable_auth
        self.credentials_file = credentials_file
        self.cookie_file = cookie_file
        self.enable_pagination = enable_pagination
        self.max_pagination_pages = max_pagination_pages
        self.enable_load_more = enable_load_more
        self.enable_infinite_scroll = enable_infinite_scroll
        self.wait_time = wait_time
        self.max_wait_time = max_wait_time
        self.rate_limit = rate_limit
        self.use_playwright = use_playwright
        self.use_proxy = use_proxy
        self.proxy_list = proxy_list or []
        self.user_agent_rotation = user_agent_rotation
        self.user_agent_list = user_agent_list or []
        self.extract_content = extract_content
        self.extract_links = extract_links
        self.extract_images = extract_images
        self.max_content_length = max_content_length
        self.optimize_for_llm = optimize_for_llm
        self.language = language
        
        # Create state directory if it doesn't exist
        if self.enable_state_saving and self.state_dir:
            os.makedirs(self.state_dir, exist_ok=True)
        
        # Initialize components
        self._initialize_components()
        
        # Initialize stats
        self.stats = {
            "pages_crawled": 0,
            "pages_with_content": 0,
            "links_discovered": 0,
            "pagination_pages": 0,
            "auth_attempts": 0,
            "auth_success": 0,
            "start_time": time.time(),
            "end_time": None,
            "total_content_size": 0,
            "errors": []
        }
        
        # Initialize rate limiting
        self.last_request_time = 0
        self.request_count = 0
        self.rate_limit_window = 60  # 1 minute
        self.rate_limit_start = time.time()
    
    def _initialize_components(self):
        """Initialize crawler components."""
        # Initialize link queue manager
        state_file = None
        if self.enable_state_saving and self.state_dir:
            query_hash = hashlib.md5(self.query.encode()).hexdigest()
            state_file = os.path.join(self.state_dir, f"queue_state_{query_hash}.json")
        
        self.link_queue = LinkQueueManager(
            max_queue_size=self.max_pages * 10,  # 10x max pages for queue size
            state_file=state_file,
            relevance_threshold=0.3,
            max_links_per_domain=self.max_links_per_domain,
            enable_state_saving=self.enable_state_saving,
            save_interval=60,  # Save every minute
            prioritize_same_domain=True,
            domain_authority_boost=True,
            pagination_detection=self.enable_pagination,
            login_form_detection=self.enable_auth
        )
        
        # Initialize auth manager
        self.auth_manager = None
        if self.enable_auth:
            self.auth_manager = AuthManager(
                credentials_file=self.credentials_file,
                use_keyring=True,
                cookie_file=self.cookie_file,
                auto_detect_login=True,
                auto_login=False,  # Require confirmation before login
                require_confirmation=True
            )
        
        # Initialize pagination handler
        self.pagination_handler = None
        if self.enable_pagination:
            self.pagination_handler = PaginationHandler(
                max_pages=self.max_pagination_pages,
                pagination_detection_threshold=0.7,
                enable_load_more_detection=self.enable_load_more,
                enable_infinite_scroll_detection=self.enable_infinite_scroll,
                enable_ajax_pagination_detection=True,
                wait_time_between_pages=self.wait_time,
                max_wait_time=self.max_wait_time,
                scroll_step=500,
                max_scroll_attempts=20
            )
        
        # Initialize Playwright if needed
        self.playwright_browser = None
        self.playwright_context = None
        if self.use_playwright:
            self._initialize_playwright()
    
    def _initialize_playwright(self):
        """Initialize Playwright for browser automation."""
        try:
            from playwright.sync_api import sync_playwright
            
            self.playwright = sync_playwright().start()
            browser_type = self.playwright.chromium
            
            # Launch browser
            self.playwright_browser = browser_type.launch(headless=True)
            
            # Create context with options
            context_options = {
                "viewport": {"width": 1280, "height": 800},
                "user_agent": self._get_user_agent() if self.user_agent_rotation else None,
                "proxy": self._get_proxy() if self.use_proxy else None,
                "java_script_enabled": True,
                "bypass_csp": True,
                "ignore_https_errors": True
            }
            
            # Remove None values
            context_options = {k: v for k, v in context_options.items() if v is not None}
            
            self.playwright_context = self.playwright_browser.new_context(**context_options)
            
            logger.info("Playwright initialized successfully")
        
        except ImportError:
            logger.error("Playwright not installed. Please install with: pip install playwright")
            logger.error("Then run: playwright install")
            self.use_playwright = False
        
        except Exception as e:
            logger.error(f"Failed to initialize Playwright: {str(e)}")
            self.use_playwright = False
