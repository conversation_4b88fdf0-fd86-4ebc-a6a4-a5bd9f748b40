"""
Module trích xuất nội dung từ nhiều định dạng tài liệu.
"""

import os
import io
import logging
from typing import Dict, Any, Optional, BinaryIO, Union
import mimetypes
import re
import base64

logger = logging.getLogger(__name__)

# Thử import các thư viện không bắt buộc
try:
    import PyPDF2
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False

try:
    import docx
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

try:
    from pptx import Presentation
    PPTX_AVAILABLE = True
except ImportError:
    PPTX_AVAILABLE = False

try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False

class DocumentExtractor:
    """Trích xuất nội dung từ nhiều định dạng tài liệu."""
    
    def __init__(self):
        """Khởi tạo DocumentExtractor."""
        # <PERSON><PERSON><PERSON> tra các thư viện khả dụng
        missing_libraries = []
        
        if not PYPDF2_AVAILABLE:
            missing_libraries.append("PyPDF2")
            
        if not DOCX_AVAILABLE:
            missing_libraries.append("python-docx")
            
        if not PPTX_AVAILABLE:
            missing_libraries.append("python-pptx")
            
        if not BS4_AVAILABLE:
            missing_libraries.append("beautifulsoup4")
        
        if missing_libraries:
            logger.warning(f"Các thư viện sau đang thiếu và ảnh hưởng đến khả năng trích xuất: {', '.join(missing_libraries)}")
    
    def _detect_mime_type(self, file_obj: BinaryIO, filename: Optional[str] = None) -> str:
        """
        Phát hiện kiểu MIME của tệp.
        
        Args:
            file_obj: Đối tượng tệp
            filename: Tên tệp (tùy chọn)
            
        Returns:
            Kiểu MIME
        """
        if filename:
            # Thử phát hiện từ phần mở rộng
            mime_type, _ = mimetypes.guess_type(filename)
            if mime_type:
                return mime_type
        
        # Đọc một phần nhỏ để phát hiện
        pos = file_obj.tell()
        header = file_obj.read(4096)
        file_obj.seek(pos)  # Đặt lại vị trí
        
        # Phát hiện các loại tệp phổ biến từ signature
        if header.startswith(b'%PDF'):
            return 'application/pdf'
        elif header.startswith(b'PK\x03\x04'):
            # Có thể là DOCX, PPTX (tệp ZIP)
            if filename:
                if filename.endswith('.docx'):
                    return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                elif filename.endswith('.pptx'):
                    return 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
            return 'application/zip'
        elif header.startswith(b'<!DOCTYPE html') or header.startswith(b'<html'):
            return 'text/html'
        
        # Mặc định
        return 'application/octet-stream'
    
    def extract_text_from_pdf(self, file_obj: BinaryIO) -> str:
        """
        Trích xuất văn bản từ tệp PDF.
        
        Args:
            file_obj: Đối tượng tệp PDF
            
        Returns:
            Văn bản trích xuất
        """
        if not PYPDF2_AVAILABLE:
            logger.error("Không thể trích xuất PDF: PyPDF2 không khả dụng")
            return ""
        
        try:
            pdf_reader = PyPDF2.PdfReader(file_obj)
            text = ""
            
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text += page.extract_text() + "\n"
            
            # Xử lý văn bản
            text = re.sub(r'\s+', ' ', text)  # Loại bỏ khoảng trắng dư thừa
            
            return text.strip()
        except Exception as e:
            logger.error(f"Lỗi khi trích xuất PDF: {str(e)}")
            return ""
    
    def extract_text_from_docx(self, file_obj: BinaryIO) -> str:
        """
        Trích xuất văn bản từ tệp DOCX.
        
        Args:
            file_obj: Đối tượng tệp DOCX
            
        Returns:
            Văn bản trích xuất
        """
        if not DOCX_AVAILABLE:
            logger.error("Không thể trích xuất DOCX: python-docx không khả dụng")
            return ""
        
        try:
            doc = docx.Document(file_obj)
            text = ""
            
            for para in doc.paragraphs:
                text += para.text + "\n"
            
            # Trích xuất từ bảng
            for table in doc.tables:
                for row in table.rows:
                    row_text = ""
                    for cell in row.cells:
                        if cell.text:
                            row_text += cell.text + " | "
                    if row_text:
                        text += row_text.rstrip(" | ") + "\n"
            
            return text.strip()
        except Exception as e:
            logger.error(f"Lỗi khi trích xuất DOCX: {str(e)}")
            return ""
    
    def extract_text_from_pptx(self, file_obj: BinaryIO) -> str:
        """
        Trích xuất văn bản từ tệp PPTX.
        
        Args:
            file_obj: Đối tượng tệp PPTX
            
        Returns:
            Văn bản trích xuất
        """
        if not PPTX_AVAILABLE:
            logger.error("Không thể trích xuất PPTX: python-pptx không khả dụng")
            return ""
        
        try:
            presentation = Presentation(file_obj)
            text = ""
            
            for slide in presentation.slides:
                slide_text = ""
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text:
                        slide_text += shape.text + " "
                
                if slide_text:
                    text += slide_text + "\n\n"
            
            return text.strip()
        except Exception as e:
            logger.error(f"Lỗi khi trích xuất PPTX: {str(e)}")
            return ""
    
    def extract_text_from_html(self, content: str) -> str:
        """
        Trích xuất văn bản từ HTML.
        
        Args:
            content: Nội dung HTML
            
        Returns:
            Văn bản trích xuất
        """
        if not BS4_AVAILABLE:
            logger.error("Không thể trích xuất HTML: BeautifulSoup không khả dụng")
            return content
        
        try:
            soup = BeautifulSoup(content, 'html.parser')
            
            # Loại bỏ script, style tags
            for script in soup(["script", "style", "noscript", "iframe", "head"]):
                script.extract()
            
            # Lấy văn bản
            text = soup.get_text(separator=' ', strip=True)
            
            # Xử lý văn bản
            lines = [line.strip() for line in text.splitlines() if line.strip()]
            text = ' '.join(lines)
            
            # Loại bỏ khoảng trắng dư thừa
            text = re.sub(r'\s+', ' ', text)
            
            return text.strip()
        except Exception as e:
            logger.error(f"Lỗi khi trích xuất HTML: {str(e)}")
            return content
    
    def extract_text(self, file_or_content: Union[BinaryIO, str], mime_type: Optional[str] = None, filename: Optional[str] = None) -> Dict[str, Any]:
        """
        Trích xuất văn bản từ tệp hoặc nội dung.
        
        Args:
            file_or_content: Đối tượng tệp hoặc nội dung chuỗi
            mime_type: Kiểu MIME (tùy chọn)
            filename: Tên tệp (tùy chọn)
            
        Returns:
            Dict với văn bản trích xuất và metadata
        """
        result = {
            "success": False,
            "text": "",
            "mime_type": mime_type,
            "filename": filename,
            "error": None
        }
        
        try:
            # Nếu là chuỗi
            if isinstance(file_or_content, str):
                # Nếu là HTML
                if mime_type == 'text/html' or (not mime_type and file_or_content.strip().startswith(('<html', '<!DOCTYPE'))):
                    result["text"] = self.extract_text_from_html(file_or_content)
                    result["mime_type"] = 'text/html'
                    result["success"] = True
                    return result
                else:
                    # Chuỗi văn bản thông thường
                    result["text"] = file_or_content
                    result["mime_type"] = 'text/plain'
                    result["success"] = True
                    return result
            
            # Nếu là đối tượng tệp
            # Phát hiện kiểu MIME nếu không được cung cấp
            if not mime_type:
                mime_type = self._detect_mime_type(file_or_content, filename)
                result["mime_type"] = mime_type
            
            # Trích xuất dựa trên kiểu MIME
            if mime_type == 'application/pdf':
                result["text"] = self.extract_text_from_pdf(file_or_content)
                result["success"] = True
            
            elif mime_type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                result["text"] = self.extract_text_from_docx(file_or_content)
                result["success"] = True
            
            elif mime_type == 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
                result["text"] = self.extract_text_from_pptx(file_or_content)
                result["success"] = True
            
            elif mime_type.startswith('text/html'):
                content = file_or_content.read().decode('utf-8', errors='ignore')
                result["text"] = self.extract_text_from_html(content)
                result["success"] = True
            
            elif mime_type.startswith('text/'):
                content = file_or_content.read().decode('utf-8', errors='ignore')
                result["text"] = content
                result["success"] = True
            
            else:
                result["error"] = f"Không hỗ trợ kiểu MIME: {mime_type}"
        
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"Lỗi khi trích xuất: {str(e)}")
        
        return result

# Singleton instance
document_extractor = DocumentExtractor()
