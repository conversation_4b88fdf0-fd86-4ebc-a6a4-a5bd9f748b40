#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Content Filter Plugin - Plugin lọc nội dung cho WebSearchAgent.

Plugin này lọc nội dung không phù hợp hoặc spam từ kết quả tìm kiếm.
"""

import re
import logging
from typing import Dict, Any, List, Optional
import os
import json

# Import PluginInterface
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from plugin_system import PluginInterface

# Tạo logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ContentFilterPlugin(PluginInterface):
    """
    Content Filter Plugin - Plugin lọc nội dung cho WebSearchAgent.
    
    Tính năng:
    - <PERSON><PERSON><PERSON> nội dung không phù hợp
    - L<PERSON><PERSON> nội dung spam
    - <PERSON><PERSON><PERSON> nội dung quảng cáo
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Khởi tạo ContentFilterPlugin.
        
        Args:
            config: Cấu hình plugin
        """
        super().__init__(config)
        self.name = "ContentFilterPlugin"
        self.version = "1.0.0"
        self.description = "Plugin lọc nội dung không phù hợp hoặc spam từ kết quả tìm kiếm"
        self.author = "Augment Code"
        
        # Tải danh sách từ khóa
        self.inappropriate_keywords = self._load_keywords("inappropriate_keywords.txt")
        self.spam_keywords = self._load_keywords("spam_keywords.txt")
        self.ad_keywords = self._load_keywords("ad_keywords.txt")
        
        # Tải cấu hình
        self.min_content_length = self.config.get("min_content_length", 50)
        self.max_spam_score = self.config.get("max_spam_score", 0.7)
        self.max_ad_score = self.config.get("max_ad_score", 0.8)
        self.enable_inappropriate_filter = self.config.get("enable_inappropriate_filter", True)
        self.enable_spam_filter = self.config.get("enable_spam_filter", True)
        self.enable_ad_filter = self.config.get("enable_ad_filter", True)
    
    def _load_keywords(self, filename: str) -> List[str]:
        """
        Tải danh sách từ khóa từ file.
        
        Args:
            filename: Tên file
            
        Returns:
            List[str]: Danh sách từ khóa
        """
        # Đường dẫn file
        file_path = os.path.join(os.path.dirname(__file__), "data", filename)
        
        # Danh sách mặc định
        default_keywords = []
        
        # Nếu file không tồn tại, trả về danh sách mặc định
        if not os.path.exists(file_path):
            logger.warning(f"File {filename} không tồn tại. Sử dụng danh sách mặc định.")
            return default_keywords
        
        # Đọc file
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                keywords = [line.strip() for line in f if line.strip()]
            
            return keywords
        except Exception as e:
            logger.error(f"Lỗi khi đọc file {filename}: {str(e)}")
            return default_keywords
    
    def post_search(self, query: str, results: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Hook sau khi tìm kiếm.
        
        Args:
            query: Truy vấn tìm kiếm
            results: Kết quả tìm kiếm
            **kwargs: Tham số khác
            
        Returns:
            Dict[str, Any]: Kết quả xử lý
        """
        # Nếu không có kết quả, trả về nguyên bản
        if not results.get("success", False) or not results.get("results", []):
            return results
        
        # Lọc kết quả
        filtered_results = []
        filtered_count = 0
        
        for result in results.get("results", []):
            # Kiểm tra nội dung
            if self._should_filter(result):
                filtered_count += 1
                continue
            
            # Thêm vào kết quả đã lọc
            filtered_results.append(result)
        
        # Cập nhật kết quả
        results["results"] = filtered_results
        results["filtered_count"] = filtered_count
        
        # Thêm thông tin về plugin
        if "plugin_info" not in results:
            results["plugin_info"] = {}
        
        results["plugin_info"]["content_filter"] = {
            "name": self.name,
            "version": self.version,
            "filtered_count": filtered_count
        }
        
        return results
    
    def post_extract_content(self, url: str, content: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Hook sau khi trích xuất nội dung.
        
        Args:
            url: URL đã trích xuất
            content: Nội dung đã trích xuất
            **kwargs: Tham số khác
            
        Returns:
            Dict[str, Any]: Kết quả xử lý
        """
        # Nếu không có nội dung, trả về nguyên bản
        if not content.get("success", False) or not content.get("content", ""):
            return content
        
        # Kiểm tra nội dung
        if self._should_filter_content(content.get("content", ""), content.get("title", "")):
            # Đánh dấu là đã lọc
            content["filtered"] = True
            content["filtered_reason"] = "Nội dung không phù hợp hoặc spam"
            
            # Xóa nội dung
            content["content"] = ""
            
            # Thêm thông tin về plugin
            if "plugin_info" not in content:
                content["plugin_info"] = {}
            
            content["plugin_info"]["content_filter"] = {
                "name": self.name,
                "version": self.version,
                "filtered": True
            }
        
        return content
    
    def _should_filter(self, result: Dict[str, Any]) -> bool:
        """
        Kiểm tra xem kết quả có nên bị lọc không.
        
        Args:
            result: Kết quả tìm kiếm
            
        Returns:
            bool: True nếu nên lọc, False nếu không
        """
        # Lấy nội dung
        title = result.get("title", "")
        content = result.get("content", "") or result.get("snippet", "")
        
        # Kiểm tra độ dài nội dung
        if len(content) < self.min_content_length:
            return True
        
        # Kiểm tra nội dung
        return self._should_filter_content(content, title)
    
    def _should_filter_content(self, content: str, title: str) -> bool:
        """
        Kiểm tra xem nội dung có nên bị lọc không.
        
        Args:
            content: Nội dung
            title: Tiêu đề
            
        Returns:
            bool: True nếu nên lọc, False nếu không
        """
        # Kết hợp tiêu đề và nội dung
        text = f"{title} {content}".lower()
        
        # Kiểm tra nội dung không phù hợp
        if self.enable_inappropriate_filter and self._contains_inappropriate_content(text):
            return True
        
        # Kiểm tra spam
        if self.enable_spam_filter and self._calculate_spam_score(text) > self.max_spam_score:
            return True
        
        # Kiểm tra quảng cáo
        if self.enable_ad_filter and self._calculate_ad_score(text) > self.max_ad_score:
            return True
        
        return False
    
    def _contains_inappropriate_content(self, text: str) -> bool:
        """
        Kiểm tra xem văn bản có chứa nội dung không phù hợp không.
        
        Args:
            text: Văn bản cần kiểm tra
            
        Returns:
            bool: True nếu có, False nếu không
        """
        for keyword in self.inappropriate_keywords:
            if re.search(r'\b' + re.escape(keyword) + r'\b', text, re.IGNORECASE):
                return True
        
        return False
    
    def _calculate_spam_score(self, text: str) -> float:
        """
        Tính điểm spam của văn bản.
        
        Args:
            text: Văn bản cần kiểm tra
            
        Returns:
            float: Điểm spam (0-1)
        """
        # Đếm số từ khóa spam
        spam_count = 0
        
        for keyword in self.spam_keywords:
            if re.search(r'\b' + re.escape(keyword) + r'\b', text, re.IGNORECASE):
                spam_count += 1
        
        # Tính điểm
        if not self.spam_keywords:
            return 0.0
        
        return min(1.0, spam_count / (len(self.spam_keywords) * 0.2))
    
    def _calculate_ad_score(self, text: str) -> float:
        """
        Tính điểm quảng cáo của văn bản.
        
        Args:
            text: Văn bản cần kiểm tra
            
        Returns:
            float: Điểm quảng cáo (0-1)
        """
        # Đếm số từ khóa quảng cáo
        ad_count = 0
        
        for keyword in self.ad_keywords:
            if re.search(r'\b' + re.escape(keyword) + r'\b', text, re.IGNORECASE):
                ad_count += 1
        
        # Tính điểm
        if not self.ad_keywords:
            return 0.0
        
        return min(1.0, ad_count / (len(self.ad_keywords) * 0.2))
