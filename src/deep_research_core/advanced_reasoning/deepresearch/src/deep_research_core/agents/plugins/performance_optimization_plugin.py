#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Performance Optimization Plugin - Plugin tối ưu hóa hiệu suất cho WebSearchAgent.

Plugin này tối ưu hóa hiệu suất của WebSearchAgent bằng cách:
1. Tối ưu hóa cache
2. Tối ưu hóa rate limiting
3. Tối ưu hóa trích xuất nội dung
"""

import logging
import time
import os
import json
import threading
from typing import Dict, Any, List, Optional, Union
import psutil

# Import PluginInterface
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from plugin_system import PluginInterface

# Tạo logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PerformanceOptimizationPlugin(PluginInterface):
    """
    Performance Optimization Plugin - Plugin tối ưu hóa hiệu suất cho WebSearchAgent.
    
    Tính năng:
    - Tối ưu hóa cache
    - Tối ưu hóa rate limiting
    - Tối ưu hóa trích xuất nội dung
    - Giám sát hiệu suất
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Khởi tạo PerformanceOptimizationPlugin.
        
        Args:
            config: Cấu hình plugin
        """
        super().__init__(config)
        self.name = "PerformanceOptimizationPlugin"
        self.version = "1.0.0"
        self.description = "Plugin tối ưu hóa hiệu suất cho WebSearchAgent"
        self.author = "Augment Code"
        
        # Tải cấu hình
        self.enable_cache_optimization = self.config.get("enable_cache_optimization", True)
        self.enable_rate_limit_optimization = self.config.get("enable_rate_limit_optimization", True)
        self.enable_content_extraction_optimization = self.config.get("enable_content_extraction_optimization", True)
        self.enable_performance_monitoring = self.config.get("enable_performance_monitoring", True)
        
        # Cấu hình cache
        self.cache_ttl = self.config.get("cache_ttl", 3600)  # 1 giờ
        self.cache_size_limit = self.config.get("cache_size_limit", 1000)
        
        # Cấu hình rate limiting
        self.rate_limit_window = self.config.get("rate_limit_window", 60)  # 60 giây
        self.rate_limit_max_requests = self.config.get("rate_limit_max_requests", 20)
        
        # Cấu hình trích xuất nội dung
        self.content_extraction_timeout = self.config.get("content_extraction_timeout", 15)
        self.content_extraction_max_length = self.config.get("content_extraction_max_length", 10000)
        
        # Khởi tạo biến theo dõi hiệu suất
        self.performance_metrics = {
            "search_time": [],
            "extract_content_time": [],
            "cache_hits": 0,
            "cache_misses": 0,
            "rate_limit_hits": 0,
            "memory_usage": []
        }
        
        # Khởi tạo thread giám sát hiệu suất
        if self.enable_performance_monitoring:
            self.monitoring_thread = threading.Thread(target=self._monitor_performance, daemon=True)
            self.monitoring_thread.start()
    
    def initialize(self, agent: Any) -> bool:
        """
        Khởi tạo plugin với agent.
        
        Args:
            agent: WebSearchAgent
            
        Returns:
            bool: True nếu thành công, False nếu thất bại
        """
        # Lưu tham chiếu đến agent
        self.agent = agent
        
        # Tối ưu hóa cache nếu được kích hoạt
        if self.enable_cache_optimization:
            self._optimize_cache()
        
        # Tối ưu hóa rate limiting nếu được kích hoạt
        if self.enable_rate_limit_optimization:
            self._optimize_rate_limiting()
        
        # Tối ưu hóa trích xuất nội dung nếu được kích hoạt
        if self.enable_content_extraction_optimization:
            self._optimize_content_extraction()
        
        return True
    
    def pre_search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Hook trước khi tìm kiếm.
        
        Args:
            query: Truy vấn tìm kiếm
            **kwargs: Tham số khác
            
        Returns:
            Dict[str, Any]: Kết quả xử lý
        """
        # Bắt đầu đo thời gian
        self._start_time = time.time()
        
        # Kiểm tra cache
        if self.enable_cache_optimization and hasattr(self.agent, "cache"):
            cache_key = query
            if cache_key in self.agent.cache:
                self.performance_metrics["cache_hits"] += 1
            else:
                self.performance_metrics["cache_misses"] += 1
        
        # Kiểm tra rate limit
        if self.enable_rate_limit_optimization and hasattr(self.agent, "rate_limiter"):
            if not self.agent.rate_limiter.can_request():
                self.performance_metrics["rate_limit_hits"] += 1
        
        return {"query": query, "modified": False}
    
    def post_search(self, query: str, results: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Hook sau khi tìm kiếm.
        
        Args:
            query: Truy vấn tìm kiếm
            results: Kết quả tìm kiếm
            **kwargs: Tham số khác
            
        Returns:
            Dict[str, Any]: Kết quả xử lý
        """
        # Kết thúc đo thời gian
        if hasattr(self, "_start_time"):
            search_time = time.time() - self._start_time
            self.performance_metrics["search_time"].append(search_time)
            
            # Giới hạn số lượng mẫu
            if len(self.performance_metrics["search_time"]) > 100:
                self.performance_metrics["search_time"] = self.performance_metrics["search_time"][-100:]
        
        # Thêm thông tin về plugin
        if "plugin_info" not in results:
            results["plugin_info"] = {}
        
        results["plugin_info"]["performance_optimization"] = {
            "name": self.name,
            "version": self.version,
            "search_time": search_time if hasattr(self, "_start_time") else None,
            "cache_hits": self.performance_metrics["cache_hits"],
            "cache_misses": self.performance_metrics["cache_misses"]
        }
        
        return results
    
    def pre_extract_content(self, url: str, **kwargs) -> Dict[str, Any]:
        """
        Hook trước khi trích xuất nội dung.
        
        Args:
            url: URL cần trích xuất
            **kwargs: Tham số khác
            
        Returns:
            Dict[str, Any]: Kết quả xử lý
        """
        # Bắt đầu đo thời gian
        self._extract_start_time = time.time()
        
        # Tối ưu hóa tham số trích xuất nội dung
        if self.enable_content_extraction_optimization:
            kwargs["timeout"] = min(kwargs.get("timeout", 30), self.content_extraction_timeout)
            kwargs["max_content_length"] = min(kwargs.get("max_content_length", 0), self.content_extraction_max_length)
        
        return {"url": url, "modified": False}
    
    def post_extract_content(self, url: str, content: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Hook sau khi trích xuất nội dung.
        
        Args:
            url: URL đã trích xuất
            content: Nội dung đã trích xuất
            **kwargs: Tham số khác
            
        Returns:
            Dict[str, Any]: Kết quả xử lý
        """
        # Kết thúc đo thời gian
        if hasattr(self, "_extract_start_time"):
            extract_time = time.time() - self._extract_start_time
            self.performance_metrics["extract_content_time"].append(extract_time)
            
            # Giới hạn số lượng mẫu
            if len(self.performance_metrics["extract_content_time"]) > 100:
                self.performance_metrics["extract_content_time"] = self.performance_metrics["extract_content_time"][-100:]
        
        # Thêm thông tin về plugin
        if "plugin_info" not in content:
            content["plugin_info"] = {}
        
        content["plugin_info"]["performance_optimization"] = {
            "name": self.name,
            "version": self.version,
            "extract_time": extract_time if hasattr(self, "_extract_start_time") else None
        }
        
        return content
    
    def shutdown(self) -> None:
        """
        Dọn dẹp khi plugin bị tắt.
        """
        # Lưu thống kê hiệu suất
        self._save_performance_metrics()
    
    def _optimize_cache(self) -> None:
        """
        Tối ưu hóa cache.
        """
        if hasattr(self.agent, "cache_ttl"):
            self.agent.cache_ttl = self.cache_ttl
        
        # Xóa các mục cache cũ
        if hasattr(self.agent, "cache") and hasattr(self.agent, "cache_timestamps"):
            current_time = time.time()
            keys_to_remove = []
            
            for key, timestamp in self.agent.cache_timestamps.items():
                if current_time - timestamp > self.cache_ttl:
                    keys_to_remove.append(key)
            
            for key in keys_to_remove:
                if key in self.agent.cache:
                    del self.agent.cache[key]
                if key in self.agent.cache_timestamps:
                    del self.agent.cache_timestamps[key]
            
            # Giới hạn kích thước cache
            if len(self.agent.cache) > self.cache_size_limit:
                # Sắp xếp theo thời gian truy cập
                sorted_keys = sorted(self.agent.cache_timestamps.items(), key=lambda x: x[1])
                
                # Xóa các mục cũ nhất
                keys_to_remove = [key for key, _ in sorted_keys[:len(self.agent.cache) - self.cache_size_limit]]
                
                for key in keys_to_remove:
                    if key in self.agent.cache:
                        del self.agent.cache[key]
                    if key in self.agent.cache_timestamps:
                        del self.agent.cache_timestamps[key]
    
    def _optimize_rate_limiting(self) -> None:
        """
        Tối ưu hóa rate limiting.
        """
        if hasattr(self.agent, "rate_limiter"):
            # Cập nhật cấu hình rate limiter
            if hasattr(self.agent.rate_limiter, "time_window"):
                self.agent.rate_limiter.time_window = self.rate_limit_window
            
            if hasattr(self.agent.rate_limiter, "rate_limit"):
                self.agent.rate_limiter.rate_limit = self.rate_limit_max_requests
    
    def _optimize_content_extraction(self) -> None:
        """
        Tối ưu hóa trích xuất nội dung.
        """
        if hasattr(self.agent, "content_extractor_config"):
            # Cập nhật cấu hình trích xuất nội dung
            self.agent.content_extractor_config["timeout"] = self.content_extraction_timeout
            self.agent.content_extractor_config["max_content_length"] = self.content_extraction_max_length
    
    def _monitor_performance(self) -> None:
        """
        Giám sát hiệu suất.
        """
        while True:
            try:
                # Đo lượng bộ nhớ sử dụng
                process = psutil.Process(os.getpid())
                memory_info = process.memory_info()
                memory_usage = memory_info.rss / (1024 * 1024)  # MB
                
                self.performance_metrics["memory_usage"].append(memory_usage)
                
                # Giới hạn số lượng mẫu
                if len(self.performance_metrics["memory_usage"]) > 100:
                    self.performance_metrics["memory_usage"] = self.performance_metrics["memory_usage"][-100:]
                
                # Lưu thống kê hiệu suất định kỳ
                self._save_performance_metrics()
                
                # Đợi 60 giây
                time.sleep(60)
            except Exception as e:
                logger.error(f"Lỗi khi giám sát hiệu suất: {str(e)}")
                time.sleep(60)
    
    def _save_performance_metrics(self) -> None:
        """
        Lưu thống kê hiệu suất.
        """
        try:
            # Tạo thư mục nếu chưa tồn tại
            metrics_dir = os.path.join(os.path.dirname(__file__), "data", "metrics")
            os.makedirs(metrics_dir, exist_ok=True)
            
            # Tạo tên file
            metrics_file = os.path.join(metrics_dir, "performance_metrics.json")
            
            # Tính toán thống kê
            avg_search_time = sum(self.performance_metrics["search_time"]) / max(1, len(self.performance_metrics["search_time"]))
            avg_extract_time = sum(self.performance_metrics["extract_content_time"]) / max(1, len(self.performance_metrics["extract_content_time"]))
            avg_memory_usage = sum(self.performance_metrics["memory_usage"]) / max(1, len(self.performance_metrics["memory_usage"]))
            
            # Tạo dữ liệu
            metrics_data = {
                "timestamp": time.time(),
                "avg_search_time": avg_search_time,
                "avg_extract_time": avg_extract_time,
                "avg_memory_usage": avg_memory_usage,
                "cache_hits": self.performance_metrics["cache_hits"],
                "cache_misses": self.performance_metrics["cache_misses"],
                "rate_limit_hits": self.performance_metrics["rate_limit_hits"]
            }
            
            # Lưu dữ liệu
            with open(metrics_file, "w", encoding="utf-8") as f:
                json.dump(metrics_data, f, indent=2)
        except Exception as e:
            logger.error(f"Lỗi khi lưu thống kê hiệu suất: {str(e)}")
