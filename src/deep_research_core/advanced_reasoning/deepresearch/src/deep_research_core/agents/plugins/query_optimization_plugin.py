#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Query Optimization Plugin - Plugin tối ưu hóa truy vấn cho WebSearchAgent.

Plugin này tối ưu hóa truy vấn tìm kiếm để cải thiện kết quả tìm kiếm.
"""

import re
import logging
from typing import Dict, Any, List, Optional
import os
import json

# Import PluginInterface
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from plugin_system import PluginInterface

# Tạo logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QueryOptimizationPlugin(PluginInterface):
    """
    Query Optimization Plugin - Plugin tối ưu hóa truy vấn cho WebSearchAgent.
    
    Tính năng:
    - T<PERSON>i ưu hóa truy vấn tìm kiếm
    - Thêm toán tử tìm kiếm
    - Loại bỏ từ không cần thiết
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Khởi tạo QueryOptimizationPlugin.
        
        Args:
            config: Cấu hình plugin
        """
        super().__init__(config)
        self.name = "QueryOptimizationPlugin"
        self.version = "1.0.0"
        self.description = "Plugin tối ưu hóa truy vấn tìm kiếm"
        self.author = "Augment Code"
        
        # Tải cấu hình
        self.enable_operator_optimization = self.config.get("enable_operator_optimization", True)
        self.enable_stopword_removal = self.config.get("enable_stopword_removal", True)
        self.enable_query_expansion = self.config.get("enable_query_expansion", True)
        self.enable_site_specific_search = self.config.get("enable_site_specific_search", True)
        self.site_specific_domains = self.config.get("site_specific_domains", [])
        
        # Danh sách từ dừng (stopwords)
        self.stopwords = {
            "en": ["a", "an", "the", "and", "or", "but", "is", "are", "was", "were", "be", "been", "being", "in", "on", "at", "to", "for", "with", "by", "about", "against", "between", "into", "through", "during", "before", "after", "above", "below", "from", "up", "down", "of", "off", "over", "under", "again", "further", "then", "once", "here", "there", "when", "where", "why", "how", "all", "any", "both", "each", "few", "more", "most", "other", "some", "such", "no", "nor", "not", "only", "own", "same", "so", "than", "too", "very", "s", "t", "can", "will", "just", "don", "should", "now"],
            "vi": ["và", "hoặc", "nhưng", "là", "của", "có", "không", "được", "trong", "đã", "với", "này", "đó", "những", "các", "để", "cho", "nên", "vì", "tại", "từ", "khi", "nếu", "mà", "về", "như", "bởi", "theo", "đến", "thì", "vào", "ra", "tôi", "bạn", "anh", "chị", "họ", "nó", "một", "hai", "ba", "bốn", "năm", "sáu", "bảy", "tám", "chín", "mười"]
        }
        
        # Toán tử tìm kiếm
        self.search_operators = {
            "exact_match": '"{}',  # Tìm kiếm chính xác
            "exclude": "-{}",      # Loại trừ
            "site": "site:{}",     # Tìm kiếm trong trang web
            "filetype": "filetype:{}", # Tìm kiếm theo loại file
            "intitle": "intitle:{}", # Tìm kiếm trong tiêu đề
            "inurl": "inurl:{}"    # Tìm kiếm trong URL
        }
    
    def pre_search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Hook trước khi tìm kiếm.
        
        Args:
            query: Truy vấn tìm kiếm
            **kwargs: Tham số khác
            
        Returns:
            Dict[str, Any]: Kết quả xử lý
        """
        # Lấy ngôn ngữ
        language = kwargs.get("language", "en")
        
        # Tối ưu hóa truy vấn
        optimized_query = self._optimize_query(query, language)
        
        # Nếu truy vấn không thay đổi, trả về nguyên bản
        if optimized_query == query:
            return {"query": query, "modified": False}
        
        # Trả về truy vấn đã tối ưu hóa
        logger.info(f"Đã tối ưu hóa truy vấn: '{query}' -> '{optimized_query}'")
        return {"query": optimized_query, "modified": True, "original_query": query}
    
    def _optimize_query(self, query: str, language: str) -> str:
        """
        Tối ưu hóa truy vấn tìm kiếm.
        
        Args:
            query: Truy vấn tìm kiếm
            language: Ngôn ngữ
            
        Returns:
            str: Truy vấn đã tối ưu hóa
        """
        # Truy vấn gốc
        original_query = query
        
        # Loại bỏ từ dừng nếu được kích hoạt
        if self.enable_stopword_removal:
            query = self._remove_stopwords(query, language)
        
        # Thêm toán tử tìm kiếm nếu được kích hoạt
        if self.enable_operator_optimization:
            query = self._add_search_operators(query)
        
        # Thêm tìm kiếm theo trang web cụ thể nếu được kích hoạt
        if self.enable_site_specific_search and self.site_specific_domains:
            query = self._add_site_specific_search(query)
        
        # Mở rộng truy vấn nếu được kích hoạt
        if self.enable_query_expansion:
            query = self._expand_query(query, language)
        
        return query
    
    def _remove_stopwords(self, query: str, language: str) -> str:
        """
        Loại bỏ từ dừng khỏi truy vấn.
        
        Args:
            query: Truy vấn tìm kiếm
            language: Ngôn ngữ
            
        Returns:
            str: Truy vấn đã loại bỏ từ dừng
        """
        # Nếu ngôn ngữ không được hỗ trợ, trả về nguyên bản
        if language not in self.stopwords:
            return query
        
        # Tách truy vấn thành các từ
        words = query.split()
        
        # Loại bỏ từ dừng
        filtered_words = [word for word in words if word.lower() not in self.stopwords[language]]
        
        # Nếu không còn từ nào, trả về nguyên bản
        if not filtered_words:
            return query
        
        # Ghép lại thành truy vấn
        return " ".join(filtered_words)
    
    def _add_search_operators(self, query: str) -> str:
        """
        Thêm toán tử tìm kiếm vào truy vấn.
        
        Args:
            query: Truy vấn tìm kiếm
            
        Returns:
            str: Truy vấn đã thêm toán tử
        """
        # Kiểm tra xem truy vấn có phải là câu hỏi không
        is_question = any(query.startswith(q) for q in ["what", "who", "when", "where", "why", "how", "which", "whose", "whom"])
        
        # Nếu là câu hỏi, thêm dấu ngoặc kép
        if is_question and '"' not in query:
            return f'"{query}"'
        
        return query
    
    def _add_site_specific_search(self, query: str) -> str:
        """
        Thêm tìm kiếm theo trang web cụ thể.
        
        Args:
            query: Truy vấn tìm kiếm
            
        Returns:
            str: Truy vấn đã thêm tìm kiếm theo trang web
        """
        # Nếu đã có toán tử site:, không thêm nữa
        if "site:" in query:
            return query
        
        # Chọn ngẫu nhiên một trang web từ danh sách
        import random
        if self.site_specific_domains:
            domain = random.choice(self.site_specific_domains)
            return f"{query} site:{domain}"
        
        return query
    
    def _expand_query(self, query: str, language: str) -> str:
        """
        Mở rộng truy vấn.
        
        Args:
            query: Truy vấn tìm kiếm
            language: Ngôn ngữ
            
        Returns:
            str: Truy vấn đã mở rộng
        """
        # Mở rộng truy vấn dựa trên từ đồng nghĩa, từ liên quan, v.v.
        # Đây chỉ là một ví dụ đơn giản, trong thực tế cần sử dụng các kỹ thuật NLP phức tạp hơn
        
        # Trả về nguyên bản vì chức năng này cần triển khai phức tạp hơn
        return query
