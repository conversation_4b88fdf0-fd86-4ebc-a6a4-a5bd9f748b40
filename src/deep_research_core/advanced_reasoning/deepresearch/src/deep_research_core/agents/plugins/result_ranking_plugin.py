#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Result Ranking Plugin - Plugin xếp hạng kết quả cho WebSearchAgent.

Plugin này xếp hạng kết quả tìm kiếm dựa trên độ phù hợp, đ<PERSON> tin cậy, và các yếu tố khác.
"""

import re
import logging
from typing import Dict, Any, List, Optional
import os
import json
import time
from urllib.parse import urlparse

# Import PluginInterface
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from plugin_system import PluginInterface

# Tạo logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ResultRankingPlugin(PluginInterface):
    """
    Result Ranking Plugin - Plugin xếp hạng kết quả cho WebSearchAgent.
    
    Tính năng:
    - Xế<PERSON> hạng kết quả dựa trên độ phù hợp
    - Xếp hạng kết quả dựa trên độ tin cậy của domain
    - Xếp hạng kết quả dựa trên độ mới
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Khởi tạo ResultRankingPlugin.
        
        Args:
            config: Cấu hình plugin
        """
        super().__init__(config)
        self.name = "ResultRankingPlugin"
        self.version = "1.0.0"
        self.description = "Plugin xếp hạng kết quả tìm kiếm"
        self.author = "Augment Code"
        
        # Tải cấu hình
        self.enable_relevance_ranking = self.config.get("enable_relevance_ranking", True)
        self.enable_domain_ranking = self.config.get("enable_domain_ranking", True)
        self.enable_recency_ranking = self.config.get("enable_recency_ranking", True)
        self.enable_content_length_ranking = self.config.get("enable_content_length_ranking", True)
        
        # Trọng số cho các yếu tố xếp hạng
        self.ranking_weights = self.config.get("ranking_weights", {
            "relevance": 0.4,
            "domain_trust": 0.3,
            "recency": 0.2,
            "content_length": 0.1
        })
        
        # Danh sách domain tin cậy
        self.trusted_domains = self.config.get("trusted_domains", [
            "wikipedia.org",
            "github.com",
            "stackoverflow.com",
            "medium.com",
            "dev.to",
            "mozilla.org",
            "w3.org",
            "python.org",
            "microsoft.com",
            "google.com",
            "apple.com",
            "amazon.com",
            "ibm.com",
            "oracle.com",
            "mit.edu",
            "harvard.edu",
            "stanford.edu",
            "berkeley.edu",
            "cmu.edu",
            "nytimes.com",
            "bbc.com",
            "reuters.com",
            "bloomberg.com",
            "who.int",
            "un.org",
            "europa.eu",
            "nasa.gov",
            "nih.gov",
            "cdc.gov",
            "fda.gov"
        ])
        
        # Danh sách domain tiếng Việt tin cậy
        self.trusted_vietnamese_domains = self.config.get("trusted_vietnamese_domains", [
            "vnexpress.net",
            "tuoitre.vn",
            "thanhnien.vn",
            "dantri.com.vn",
            "vietnamnet.vn",
            "baomoi.com",
            "vtv.vn",
            "vov.vn",
            "nhandan.com.vn",
            "chinhphu.vn",
            "moh.gov.vn",
            "moet.gov.vn",
            "vnu.edu.vn",
            "hust.edu.vn",
            "coccoc.com",
            "tiki.vn",
            "viettimes.vn",
            "cafef.vn",
            "cafebiz.vn",
            "kenh14.vn",
            "genk.vn",
            "suckhoedoisong.vn",
            "bachhoaxanh.com",
            "thegioididong.com",
            "fpt.com.vn"
        ])
        
        # Kết hợp danh sách domain tin cậy
        self.all_trusted_domains = self.trusted_domains + self.trusted_vietnamese_domains
    
    def post_search(self, query: str, results: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Hook sau khi tìm kiếm.
        
        Args:
            query: Truy vấn tìm kiếm
            results: Kết quả tìm kiếm
            **kwargs: Tham số khác
            
        Returns:
            Dict[str, Any]: Kết quả xử lý
        """
        # Nếu không có kết quả, trả về nguyên bản
        if not results.get("success", False) or not results.get("results", []):
            return results
        
        # Xếp hạng kết quả
        ranked_results = self._rank_results(query, results.get("results", []))
        
        # Cập nhật kết quả
        results["results"] = ranked_results
        
        # Thêm thông tin về plugin
        if "plugin_info" not in results:
            results["plugin_info"] = {}
        
        results["plugin_info"]["result_ranking"] = {
            "name": self.name,
            "version": self.version,
            "ranking_enabled": True,
            "ranking_factors": {
                "relevance": self.enable_relevance_ranking,
                "domain_trust": self.enable_domain_ranking,
                "recency": self.enable_recency_ranking,
                "content_length": self.enable_content_length_ranking
            }
        }
        
        return results
    
    def _rank_results(self, query: str, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Xếp hạng kết quả tìm kiếm.
        
        Args:
            query: Truy vấn tìm kiếm
            results: Danh sách kết quả
            
        Returns:
            List[Dict[str, Any]]: Danh sách kết quả đã xếp hạng
        """
        # Tính điểm cho mỗi kết quả
        scored_results = []
        
        for result in results:
            # Tính điểm các yếu tố
            relevance_score = self._calculate_relevance_score(query, result) if self.enable_relevance_ranking else 0.5
            domain_score = self._calculate_domain_score(result) if self.enable_domain_ranking else 0.5
            recency_score = self._calculate_recency_score(result) if self.enable_recency_ranking else 0.5
            content_length_score = self._calculate_content_length_score(result) if self.enable_content_length_ranking else 0.5
            
            # Tính điểm tổng hợp
            total_score = (
                relevance_score * self.ranking_weights.get("relevance", 0.4) +
                domain_score * self.ranking_weights.get("domain_trust", 0.3) +
                recency_score * self.ranking_weights.get("recency", 0.2) +
                content_length_score * self.ranking_weights.get("content_length", 0.1)
            )
            
            # Thêm điểm vào kết quả
            result_with_score = result.copy()
            result_with_score["_ranking_score"] = total_score
            result_with_score["_ranking_details"] = {
                "relevance": relevance_score,
                "domain_trust": domain_score,
                "recency": recency_score,
                "content_length": content_length_score
            }
            
            scored_results.append(result_with_score)
        
        # Sắp xếp kết quả theo điểm giảm dần
        scored_results.sort(key=lambda x: x.get("_ranking_score", 0), reverse=True)
        
        return scored_results
    
    def _calculate_relevance_score(self, query: str, result: Dict[str, Any]) -> float:
        """
        Tính điểm độ phù hợp.
        
        Args:
            query: Truy vấn tìm kiếm
            result: Kết quả tìm kiếm
            
        Returns:
            float: Điểm độ phù hợp (0-1)
        """
        # Lấy nội dung
        title = result.get("title", "")
        snippet = result.get("snippet", "") or result.get("content", "")
        
        # Tách truy vấn thành các từ
        query_words = set(query.lower().split())
        
        # Đếm số từ truy vấn xuất hiện trong tiêu đề và snippet
        title_words = set(title.lower().split())
        snippet_words = set(snippet.lower().split())
        
        title_matches = len(query_words.intersection(title_words))
        snippet_matches = len(query_words.intersection(snippet_words))
        
        # Tính điểm
        title_score = title_matches / max(1, len(query_words))
        snippet_score = snippet_matches / max(1, len(query_words))
        
        # Trọng số: tiêu đề quan trọng hơn snippet
        return 0.7 * title_score + 0.3 * snippet_score
    
    def _calculate_domain_score(self, result: Dict[str, Any]) -> float:
        """
        Tính điểm độ tin cậy của domain.
        
        Args:
            result: Kết quả tìm kiếm
            
        Returns:
            float: Điểm độ tin cậy (0-1)
        """
        # Lấy domain từ URL
        url = result.get("url", "")
        if not url:
            return 0.5
        
        domain = urlparse(url).netloc
        
        # Kiểm tra domain có trong danh sách tin cậy không
        if domain in self.all_trusted_domains:
            return 1.0
        
        # Kiểm tra domain có phải là subdomain của domain tin cậy không
        for trusted_domain in self.all_trusted_domains:
            if domain.endswith(f".{trusted_domain}"):
                return 0.9
        
        # Kiểm tra TLD
        tld = domain.split(".")[-1]
        if tld in ["edu", "gov", "org"]:
            return 0.8
        elif tld in ["com", "net", "io", "co"]:
            return 0.6
        
        return 0.5
    
    def _calculate_recency_score(self, result: Dict[str, Any]) -> float:
        """
        Tính điểm độ mới.
        
        Args:
            result: Kết quả tìm kiếm
            
        Returns:
            float: Điểm độ mới (0-1)
        """
        # Lấy thời gian từ kết quả
        timestamp = result.get("timestamp")
        if not timestamp:
            return 0.5
        
        # Tính thời gian trôi qua (ngày)
        current_time = time.time()
        days_ago = (current_time - timestamp) / (24 * 60 * 60)
        
        # Tính điểm
        if days_ago < 1:  # Trong vòng 1 ngày
            return 1.0
        elif days_ago < 7:  # Trong vòng 1 tuần
            return 0.9
        elif days_ago < 30:  # Trong vòng 1 tháng
            return 0.8
        elif days_ago < 90:  # Trong vòng 3 tháng
            return 0.7
        elif days_ago < 365:  # Trong vòng 1 năm
            return 0.6
        
        return 0.5
    
    def _calculate_content_length_score(self, result: Dict[str, Any]) -> float:
        """
        Tính điểm độ dài nội dung.
        
        Args:
            result: Kết quả tìm kiếm
            
        Returns:
            float: Điểm độ dài nội dung (0-1)
        """
        # Lấy nội dung
        content = result.get("content", "")
        if not content:
            return 0.5
        
        # Tính độ dài
        length = len(content)
        
        # Tính điểm
        if length > 5000:  # Nội dung dài
            return 1.0
        elif length > 2000:
            return 0.9
        elif length > 1000:
            return 0.8
        elif length > 500:
            return 0.7
        elif length > 200:
            return 0.6
        
        return 0.5
