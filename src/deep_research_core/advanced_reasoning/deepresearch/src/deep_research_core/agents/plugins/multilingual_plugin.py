#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Multilingual Plugin - Plugin hỗ trợ đa ngôn ngữ cho WebSearchAgent.

Plugin này cung cấp các tính năng hỗ trợ đa ngôn ngữ:
1. <PERSON><PERSON><PERSON> hiện ngôn ngữ tự động
2. <PERSON><PERSON>ch truy vấn tìm kiếm
3. <PERSON><PERSON><PERSON> kết quả tìm kiếm
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
import os
import json
import re

# Import PluginInterface
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from plugin_system import PluginInterface

# Tạo logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Kiểm tra các thư viện tùy chọn
try:
    from langdetect import detect, LangDetectException
    LANGDETECT_AVAILABLE = True
except ImportError:
    LANGDETECT_AVAILABLE = False
    logger.warning("Thư viện langdetect không khả dụng. Cài đặt với 'pip install langdetect'")

try:
    from deep_translator import GoogleTranslator
    TRANSLATOR_AVAILABLE = True
except ImportError:
    TRANSLATOR_AVAILABLE = False
    logger.warning("Thư viện deep_translator không khả dụng. Cài đặt với 'pip install deep-translator'")

class MultilingualPlugin(PluginInterface):
    """
    Multilingual Plugin - Plugin hỗ trợ đa ngôn ngữ cho WebSearchAgent.

    Tính năng:
    - Phát hiện ngôn ngữ tự động
    - Dịch truy vấn tìm kiếm
    - Dịch kết quả tìm kiếm
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Khởi tạo MultilingualPlugin.

        Args:
            config: Cấu hình plugin
        """
        super().__init__(config)
        self.name = "MultilingualPlugin"
        self.version = "1.0.0"
        self.description = "Plugin hỗ trợ đa ngôn ngữ cho WebSearchAgent"
        self.author = "Augment Code"

        # Tải cấu hình
        self.default_language = self.config.get("default_language", "en")
        self.supported_languages = self.config.get("supported_languages", ["en", "vi", "fr", "de", "es", "zh-CN", "ja", "ko", "ru"])
        self.auto_detect = self.config.get("auto_detect", True)
        self.translate_query = self.config.get("translate_query", True)
        self.translate_results = self.config.get("translate_results", True)
        self.translate_content = self.config.get("translate_content", False)

        # Kiểm tra các thư viện
        self.can_detect = LANGDETECT_AVAILABLE
        self.can_translate = TRANSLATOR_AVAILABLE

        # Khởi tạo bộ dịch
        self.translators = {}
        if self.can_translate:
            for lang in self.supported_languages:
                if lang != "en":
                    try:
                        self.translators[f"en-{lang}"] = GoogleTranslator(source="en", target=lang)
                        self.translators[f"{lang}-en"] = GoogleTranslator(source=lang, target="en")
                    except Exception as e:
                        logger.error(f"Lỗi khi khởi tạo bộ dịch {lang}: {str(e)}")

    def pre_search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Hook trước khi tìm kiếm.

        Args:
            query: Truy vấn tìm kiếm
            **kwargs: Tham số khác

        Returns:
            Dict[str, Any]: Kết quả xử lý
        """
        # Lấy ngôn ngữ
        language = kwargs.get("language", "auto")

        # Nếu ngôn ngữ là auto, phát hiện ngôn ngữ
        if language == "auto" and self.auto_detect and self.can_detect:
            detected_language = self._detect_language(query)
            language = detected_language or self.default_language

            logger.info(f"Đã phát hiện ngôn ngữ: {language}")
        elif language == "auto":
            language = self.default_language

        # Nếu ngôn ngữ không được hỗ trợ, sử dụng ngôn ngữ mặc định
        if language not in self.supported_languages:
            language = self.default_language

        # Dịch truy vấn nếu cần
        if self.translate_query and self.can_translate and language != "en":
            # Dịch sang tiếng Anh
            translated_query = self._translate(query, language, "en")

            if translated_query:
                logger.info(f"Đã dịch truy vấn: '{query}' -> '{translated_query}'")

                # Trả về truy vấn đã dịch
                return {
                    "query": translated_query,
                    "modified": True,
                    "original_query": query,
                    "original_language": language
                }

        # Không dịch
        return {
            "query": query,
            "modified": False,
            "language": language
        }

    def post_search(self, query: str, results: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Hook sau khi tìm kiếm.

        Args:
            query: Truy vấn tìm kiếm
            results: Kết quả tìm kiếm
            **kwargs: Tham số khác

        Returns:
            Dict[str, Any]: Kết quả xử lý
        """
        # Nếu không có kết quả, trả về nguyên bản
        if not results.get("success", False) or not results.get("results", []):
            return results

        # Lấy ngôn ngữ
        language = kwargs.get("language", "auto")
        original_language = kwargs.get("original_language")

        # Nếu không cần dịch kết quả, trả về nguyên bản
        if not self.translate_results or not self.can_translate:
            return results

        # Nếu ngôn ngữ là auto, sử dụng ngôn ngữ đã phát hiện
        if language == "auto" and original_language:
            language = original_language
        elif language == "auto":
            language = self.default_language

        # Nếu ngôn ngữ là tiếng Anh, không cần dịch
        if language == "en":
            return results

        # Dịch kết quả
        for result in results.get("results", []):
            # Dịch tiêu đề
            title = result.get("title", "")
            if title:
                translated_title = self._translate(title, "en", language)
                if translated_title:
                    result["title"] = translated_title
                    result["original_title"] = title

            # Dịch snippet
            snippet = result.get("snippet", "")
            if snippet:
                translated_snippet = self._translate(snippet, "en", language)
                if translated_snippet:
                    result["snippet"] = translated_snippet
                    result["original_snippet"] = snippet

            # Dịch nội dung nếu cần
            if self.translate_content:
                content = result.get("content", "")
                if content:
                    # Giới hạn độ dài để tránh lỗi
                    if len(content) > 5000:
                        content = content[:5000]

                    translated_content = self._translate(content, "en", language)
                    if translated_content:
                        result["content"] = translated_content
                        result["original_content"] = content

        # Thêm thông tin về plugin
        if "plugin_info" not in results:
            results["plugin_info"] = {}

        results["plugin_info"]["multilingual"] = {
            "name": self.name,
            "version": self.version,
            "language": language,
            "translated": True
        }

        return results

    def post_extract_content(self, url: str, content: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Hook sau khi trích xuất nội dung.

        Args:
            url: URL đã trích xuất
            content: Nội dung đã trích xuất
            **kwargs: Tham số khác

        Returns:
            Dict[str, Any]: Kết quả xử lý
        """
        # Nếu không có nội dung, trả về nguyên bản
        if not content.get("success", False) or not content.get("content", ""):
            return content

        # Lấy ngôn ngữ
        language = kwargs.get("language", "auto")

        # Nếu không cần dịch nội dung, trả về nguyên bản
        if not self.translate_content or not self.can_translate:
            return content

        # Nếu ngôn ngữ là auto, phát hiện ngôn ngữ
        if language == "auto" and self.auto_detect and self.can_detect:
            detected_language = self._detect_language(content.get("content", ""))
            language = detected_language or self.default_language
        elif language == "auto":
            language = self.default_language

        # Nếu ngôn ngữ không được hỗ trợ, sử dụng ngôn ngữ mặc định
        if language not in self.supported_languages:
            language = self.default_language

        # Nếu ngôn ngữ là tiếng Anh, không cần dịch
        if language == "en":
            return content

        # Phát hiện ngôn ngữ của nội dung
        content_language = self._detect_language(content.get("content", ""))

        # Nếu nội dung đã là ngôn ngữ đích, không cần dịch
        if content_language == language:
            return content

        # Dịch nội dung
        title = content.get("title", "")
        if title:
            translated_title = self._translate(title, content_language or "en", language)
            if translated_title:
                content["title"] = translated_title
                content["original_title"] = title

        # Dịch nội dung
        text = content.get("content", "")
        if text:
            # Giới hạn độ dài để tránh lỗi
            if len(text) > 5000:
                text = text[:5000]

            translated_text = self._translate(text, content_language or "en", language)
            if translated_text:
                content["content"] = translated_text
                content["original_content"] = text

        # Thêm thông tin về plugin
        if "plugin_info" not in content:
            content["plugin_info"] = {}

        content["plugin_info"]["multilingual"] = {
            "name": self.name,
            "version": self.version,
            "language": language,
            "original_language": content_language,
            "translated": True
        }

        return content

    def _detect_language(self, text: str) -> Optional[str]:
        """
        Phát hiện ngôn ngữ của văn bản.

        Args:
            text: Văn bản cần phát hiện

        Returns:
            Optional[str]: Mã ngôn ngữ hoặc None nếu không phát hiện được
        """
        if not self.can_detect or not text:
            return None

        try:
            # Giới hạn độ dài để tránh lỗi
            if len(text) > 1000:
                text = text[:1000]

            # Phát hiện ngôn ngữ
            language = detect(text)

            # Ánh xạ ngôn ngữ
            language_map = {
                "zh-cn": "zh-CN",
                "zh-tw": "zh-TW",
                "zh": "zh-CN"
            }

            return language_map.get(language, language)
        except LangDetectException as e:
            logger.error(f"Lỗi khi phát hiện ngôn ngữ: {str(e)}")
            return None

    def _translate(self, text: str, source_lang: str, target_lang: str) -> Optional[str]:
        """
        Dịch văn bản.

        Args:
            text: Văn bản cần dịch
            source_lang: Ngôn ngữ nguồn
            target_lang: Ngôn ngữ đích

        Returns:
            Optional[str]: Văn bản đã dịch hoặc None nếu không dịch được
        """
        if not self.can_translate or not text:
            return None

        # Nếu ngôn ngữ nguồn và đích giống nhau, không cần dịch
        if source_lang == target_lang:
            return text

        try:
            # Lấy bộ dịch
            translator_key = f"{source_lang}-{target_lang}"

            if translator_key not in self.translators:
                # Tạo bộ dịch mới
                try:
                    self.translators[translator_key] = GoogleTranslator(source=source_lang, target=target_lang)
                except Exception as e:
                    logger.error(f"Lỗi khi tạo bộ dịch {translator_key}: {str(e)}")
                    return None

            # Dịch văn bản
            translator = self.translators[translator_key]

            # Giới hạn độ dài để tránh lỗi
            if len(text) > 5000:
                text = text[:5000]

            translated_text = translator.translate(text)

            return translated_text
        except Exception as e:
            logger.error(f"Lỗi khi dịch văn bản: {str(e)}")
            return None
