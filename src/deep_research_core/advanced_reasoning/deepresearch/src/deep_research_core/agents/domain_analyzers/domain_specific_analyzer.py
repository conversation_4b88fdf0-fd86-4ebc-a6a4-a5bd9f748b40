"""
Domain-specific analysis for search results.

This module provides specialized analysis for search results based on specific domains
such as health, finance, technology, etc.
"""

import re
import json
import logging
from typing import Dict, List, Any, Optional, Set, Tuple
from collections import Counter
import os
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Define paths
DATA_DIR = Path(__file__).parent.parent.parent / "data"

class DomainSpecificAnalyzer:
    """
    Analyzer for domain-specific content in search results.
    
    This class provides methods to analyze search results based on specific domains
    such as health, finance, technology, etc. It can detect domain-specific entities,
    terminology, and evaluate domain-specific credibility.
    """
    
    # Domain types
    DOMAIN_TYPES = [
        "health",
        "finance",
        "technology",
        "science",
        "education",
        "news",
        "entertainment",
        "sports",
        "politics",
        "legal",
        "environment",
        "travel"
    ]
    
    def __init__(self, domain_type: Optional[str] = None):
        """
        Initialize the DomainSpecificAnalyzer.
        
        Args:
            domain_type: Specific domain to focus on (if None, will auto-detect)
        """
        self.domain_type = domain_type
        self.domain_data = {}
        
        # Load domain-specific data
        self._load_domain_data()
        
        # Try to load NLP libraries if available
        try:
            import nltk
            self.nltk_available = True
            # Download necessary NLTK data
            try:
                nltk.data.find('tokenizers/punkt')
            except LookupError:
                nltk.download('punkt', quiet=True)
        except ImportError:
            self.nltk_available = False
            logger.warning("NLTK not available. Some features will be limited.")
        
        # Vietnamese NLP
        try:
            import underthesea
            self.underthesea_available = True
        except ImportError:
            self.underthesea_available = False
            logger.warning("Underthesea not available. Vietnamese analysis will be limited.")
    
    def _load_domain_data(self):
        """Load domain-specific data from files."""
        # Create data directory if it doesn't exist
        if not os.path.exists(DATA_DIR):
            os.makedirs(DATA_DIR)
        
        # Try to load domain-specific terminology and patterns
        for domain in self.DOMAIN_TYPES:
            domain_file = DATA_DIR / f"{domain}_domain_data.json"
            
            if os.path.exists(domain_file):
                try:
                    with open(domain_file, 'r', encoding='utf-8') as f:
                        self.domain_data[domain] = json.load(f)
                    logger.info(f"Loaded domain data for {domain}")
                except Exception as e:
                    logger.error(f"Error loading domain data for {domain}: {str(e)}")
                    # Initialize with defaults if loading fails
                    self.domain_data[domain] = self._get_default_domain_data(domain)
            else:
                # If file doesn't exist, use defaults
                self.domain_data[domain] = self._get_default_domain_data(domain)
                
                # Save default data
                try:
                    with open(domain_file, 'w', encoding='utf-8') as f:
                        json.dump(self.domain_data[domain], f, ensure_ascii=False, indent=2)
                except Exception as e:
                    logger.error(f"Error saving default domain data for {domain}: {str(e)}")
    
    def _get_default_domain_data(self, domain: str) -> Dict[str, Any]:
        """
        Get default domain data for a specific domain.
        
        Args:
            domain: Domain type
            
        Returns:
            Dictionary with default domain data
        """
        # Common default structure
        data = {
            "terminology": {
                "en": [],
                "vi": []
            },
            "entities": {
                "en": [],
                "vi": []
            },
            "patterns": {
                "en": [],
                "vi": []
            },
            "credible_sources": [],
            "keywords": {
                "en": [],
                "vi": []
            }
        }
        
        # Domain-specific defaults
        if domain == "health":
            data["terminology"]["en"] = ["symptoms", "diagnosis", "treatment", "medication", "disease", "condition", "healthcare", "patient", "physician", "doctor", "nurse", "hospital", "clinic"]
            data["terminology"]["vi"] = ["triệu chứng", "chẩn đoán", "điều trị", "thuốc", "bệnh", "tình trạng", "chăm sóc sức khỏe", "bệnh nhân", "bác sĩ", "y tá", "bệnh viện", "phòng khám"]
            data["credible_sources"] = ["who.int", "cdc.gov", "nih.gov", "mayoclinic.org", "webmd.com", "medlineplus.gov", "health.harvard.edu", "moh.gov.vn", "pasteur.vn"]
            
        elif domain == "finance":
            data["terminology"]["en"] = ["investment", "stock", "bond", "dividend", "interest", "loan", "mortgage", "credit", "debit", "bankruptcy", "portfolio", "equity", "asset", "liability"]
            data["terminology"]["vi"] = ["đầu tư", "cổ phiếu", "trái phiếu", "cổ tức", "lãi suất", "khoản vay", "thế chấp", "tín dụng", "nợ", "phá sản", "danh mục đầu tư", "vốn", "tài sản", "nợ phải trả"]
            data["credible_sources"] = ["bloomberg.com", "wsj.com", "cnbc.com", "investor.gov", "forbes.com", "finance.yahoo.com", "ft.com", "sbv.gov.vn", "mof.gov.vn"]
            
        elif domain == "technology":
            data["terminology"]["en"] = ["software", "hardware", "algorithm", "programming", "database", "network", "server", "cloud", "security", "encryption", "interface", "IoT", "AI", "machine learning"]
            data["terminology"]["vi"] = ["phần mềm", "phần cứng", "thuật toán", "lập trình", "cơ sở dữ liệu", "mạng", "máy chủ", "đám mây", "bảo mật", "mã hóa", "giao diện", "IoT", "AI", "học máy"]
            data["credible_sources"] = ["techcrunch.com", "wired.com", "theverge.com", "cnet.com", "arstechnica.com", "zdnet.com", "techradar.com", "mit.edu", "ictnews.vn", "vnexpress.net/so-hoa"]
        
        # Default Vietnamese words for other domains
        if domain == "science":
            data["terminology"]["vi"] = ["khoa học", "thí nghiệm", "nghiên cứu", "phát hiện", "giả thuyết", "lý thuyết", "nguyên tắc", "hiện tượng"]
        elif domain == "education":
            data["terminology"]["vi"] = ["giáo dục", "học tập", "dạy học", "trường học", "sinh viên", "học sinh", "giáo viên", "đại học", "bằng cấp", "chứng chỉ"]
        elif domain == "news":
            data["terminology"]["vi"] = ["tin tức", "báo cáo", "sự kiện", "biên tập", "phóng viên", "bản tin", "tuyên bố"]
        
        return data
    
    def detect_domain_type(self, text: str, language: str = "auto") -> Dict[str, Any]:
        """
        Detect the domain type of a text.
        
        Args:
            text: Text to analyze
            language: Language of the text ("en", "vi", or "auto")
            
        Returns:
            Dictionary with domain detection results
        """
        if not text:
            return {"domain": "unknown", "confidence": 0.0, "domain_scores": {}}
        
        # Auto-detect language if needed
        if language == "auto":
            language = self._detect_language(text)
        
        # Initialize domain scores
        domain_scores = {domain: 0.0 for domain in self.DOMAIN_TYPES}
        
        # Lowercase text for matching
        text_lower = text.lower()
        
        # Tokenize text based on language
        tokens = self._tokenize_text(text_lower, language)
        
        # Calculate domain scores based on terminology matches
        for domain in self.DOMAIN_TYPES:
            domain_data = self.domain_data.get(domain, {})
            
            # Get language-specific terminology
            terminology = domain_data.get("terminology", {}).get(language, [])
            keywords = domain_data.get("keywords", {}).get(language, [])
            
            # Count term matches
            term_count = sum(1 for term in terminology if term in text_lower)
            
            # Count keyword matches (exact)
            keyword_count = sum(1 for keyword in keywords if keyword in tokens)
            
            # Check patterns
            patterns = domain_data.get("patterns", {}).get(language, [])
            pattern_matches = sum(1 for pattern in patterns if re.search(pattern, text_lower))
            
            # Calculate score
            base_score = (term_count * 1.0 + keyword_count * 2.0 + pattern_matches * 3.0) / max(1, len(terminology) + len(keywords) + len(patterns))
            domain_scores[domain] = min(1.0, base_score)
        
        # Find best domain
        best_domain = max(domain_scores.items(), key=lambda x: x[1])
        
        # Return results
        return {
            "domain": best_domain[0] if best_domain[1] > 0.1 else "unknown",
            "confidence": best_domain[1],
            "domain_scores": domain_scores,
            "language": language
        }
    
    def _detect_language(self, text: str) -> str:
        """
        Detect the language of the text.
        
        Args:
            text: Text to analyze
            
        Returns:
            Language code ("en" or "vi")
        """
        # Simple rule-based detection for Vietnamese
        vietnamese_chars = set("àáảãạăắằẳẵặâấầẩẫậèéẻẽẹêếềểễệìíỉĩịòóỏõọôốồổỗộơớờởỡợùúủũụưứừửữựỳýỷỹỵđ")
        text_lower = text.lower()
        
        # Count Vietnamese characters
        vn_char_count = sum(1 for c in text_lower if c in vietnamese_chars)
        
        # If there are Vietnamese characters, it's likely Vietnamese
        if vn_char_count > 0:
            return "vi"
            
        # Otherwise, try using langdetect if available
        try:
            from langdetect import detect
            lang = detect(text)
            if lang == "vi":
                return "vi"
        except:
            pass
            
        # Default to English
        return "en"
    
    def _tokenize_text(self, text: str, language: str) -> List[str]:
        """
        Tokenize text based on language.
        
        Args:
            text: Text to tokenize
            language: Language of the text
            
        Returns:
            List of tokens
        """
        # Thử dùng underthesea cho tiếng Việt
        if language == "vi":
            if self.underthesea_available:
                try:
                    from underthesea import word_tokenize
                    return word_tokenize(text)
                except Exception as e:
                    logger.warning(f"Error using underthesea for tokenization: {str(e)}")
            else:
                # Fallback cho tiếng Việt khi không có underthesea
                # Áp dụng nguyên tắc tokenize đơn giản cho tiếng Việt
                try:
                    # Thử sử dụng pyvi nếu có
                    try:
                        from pyvi import ViTokenizer
                        return ViTokenizer.tokenize(text).split()
                    except ImportError:
                        pass
                        
                    # Tokenize thủ công với regex nếu không có thư viện hỗ trợ
                    import re
                    # Xử lý dấu câu
                    text = re.sub(r'([.,!?()])', r' \1 ', text)
                    # Tách từ
                    words = text.split()
                    # Ghép lại các từ có gạch ngang
                    i = 0
                    while i < len(words) - 1:
                        if words[i].endswith('-'):
                            words[i] = words[i][:-1] + words[i+1]
                            words.pop(i+1)
                        else:
                            i += 1
                    return words
                except Exception as e:
                    logger.warning(f"Error in Vietnamese fallback tokenization: {str(e)}")
        
        # Thử dùng nltk cho các ngôn ngữ
        if self.nltk_available:
            try:
                from nltk.tokenize import word_tokenize
                return word_tokenize(text)
            except Exception as e:
                logger.warning(f"Error using NLTK for tokenization: {str(e)}")
        
        # Fallback cuối cùng: tách theo khoảng trắng
        return text.split()
    
    def analyze_domain_specific_content(self, search_results: Dict[str, Any], domain_type: Optional[str] = None) -> Dict[str, Any]:
        """
        Analyze search results for domain-specific content.
        
        Args:
            search_results: Search results to analyze
            domain_type: Domain type to focus on (if None, uses instance domain_type or auto-detects)
            
        Returns:
            Dictionary with domain-specific analysis results
        """
        if not search_results.get("success", False) or not search_results.get("results", []):
            return {
                "success": False,
                "error": "No valid search results to analyze",
                "query": search_results.get("query", ""),
                "domain_analysis": {}
            }
        
        # Get domain type to use
        target_domain = domain_type or self.domain_type
        
        # Get results
        results = search_results.get("results", [])
        
        # Initialize analysis results
        domain_analysis = {
            "query": search_results.get("query", ""),
            "domain_type": target_domain,
            "domain_detected": target_domain is not None,
            "result_analysis": [],
            "domain_specific_entities": [],
            "terminology_frequency": {},
            "domain_specific_sources": [],
            "domain_relevance_score": 0.0
        }
        
        # Analyze each result
        domain_scores = []
        terminology_counter = Counter()
        entity_counter = Counter()
        
        for result in results:
            # Extract text content
            title = result.get("title", "")
            snippet = result.get("snippet", "")
            content = result.get("content", "")
            url = result.get("url", "")
            
            # Combine text for analysis
            text = f"{title} {snippet} {content}"
            
            # If no domain is specified, detect it
            if target_domain is None:
                domain_detection = self.detect_domain_type(text)
                detected_domain = domain_detection["domain"]
                confidence = domain_detection["confidence"]
                
                # If this is the first result with a high-confidence domain, set it as target
                if detected_domain != "unknown" and confidence > 0.5 and target_domain is None:
                    target_domain = detected_domain
                    domain_analysis["domain_type"] = target_domain
                    domain_analysis["domain_detected"] = True
            
            # Skip detailed analysis if we still don't have a domain type
            if target_domain is None or target_domain == "unknown":
                continue
            
            # Get domain data
            domain_data = self.domain_data.get(target_domain, {})
            
            # Analyze text for domain-specific content
            result_analysis = self._analyze_text_for_domain(text, url, target_domain)
            domain_analysis["result_analysis"].append(result_analysis)
            
            # Update counters
            for term, count in result_analysis.get("terminology", {}).items():
                terminology_counter[term] += count
                
            for entity in result_analysis.get("entities", []):
                entity_counter[entity] += 1
                
            # Track domain score
            domain_scores.append(result_analysis.get("domain_relevance_score", 0.0))
            
            # Check if source is domain-specific
            if result_analysis.get("is_domain_specific_source", False):
                domain_analysis["domain_specific_sources"].append({
                    "url": url,
                    "title": title,
                    "domain": result_analysis.get("domain", "")
                })
        
        # Update overall analysis
        domain_analysis["terminology_frequency"] = dict(terminology_counter.most_common(20))
        domain_analysis["domain_specific_entities"] = [{"entity": e, "count": c} for e, c in entity_counter.most_common(20)]
        domain_analysis["domain_relevance_score"] = sum(domain_scores) / max(1, len(domain_scores))
        
        # Return combined results
        return {
            "success": True,
            "query": search_results.get("query", ""),
            "domain_analysis": domain_analysis
        }
    
    def _analyze_text_for_domain(self, text: str, url: str, domain_type: str) -> Dict[str, Any]:
        """
        Analyze text for domain-specific content.
        
        Args:
            text: Text to analyze
            url: URL of the content
            domain_type: Domain type to analyze for
            
        Returns:
            Dictionary with domain-specific analysis for this text
        """
        # Get domain data
        domain_data = self.domain_data.get(domain_type, {})
        
        # Detect language
        language = self._detect_language(text)
        
        # Get terminology and entities for this domain and language
        terminology = domain_data.get("terminology", {}).get(language, [])
        entities = domain_data.get("entities", {}).get(language, [])
        
        # Find terminology matches
        terminology_counts = {}
        for term in terminology:
            count = len(re.findall(r'\b' + re.escape(term) + r'\b', text.lower()))
            if count > 0:
                terminology_counts[term] = count
        
        # Find entity matches
        entity_matches = []
        for entity in entities:
            if re.search(r'\b' + re.escape(entity) + r'\b', text, re.IGNORECASE):
                entity_matches.append(entity)
        
        # Check if the source is domain-specific
        credible_sources = domain_data.get("credible_sources", [])
        is_domain_specific = any(source in url for source in credible_sources)
        
        # Extract domain from URL
        from urllib.parse import urlparse
        parsed_url = urlparse(url)
        domain = parsed_url.netloc
        
        # Calculate domain relevance score
        term_factor = min(1.0, len(terminology_counts) / max(1, len(terminology) * 0.3))
        entity_factor = min(1.0, len(entity_matches) / max(1, len(entities) * 0.3))
        source_factor = 1.0 if is_domain_specific else 0.0
        
        domain_relevance_score = (term_factor * 0.5 + entity_factor * 0.3 + source_factor * 0.2)
        
        # Return analysis
        return {
            "url": url,
            "domain": domain,
            "language": language,
            "domain_type": domain_type,
            "terminology": terminology_counts,
            "entities": entity_matches,
            "is_domain_specific_source": is_domain_specific,
            "domain_relevance_score": domain_relevance_score
        }
    
    def filter_results_by_domain(
        self, 
        search_results: Dict[str, Any], 
        domain_type: Optional[str] = None,
        min_relevance_score: float = 0.3
    ) -> Dict[str, Any]:
        """
        Filter search results based on domain-specific relevance.
        
        Args:
            search_results: Search results to filter
            domain_type: Domain type to filter by
            min_relevance_score: Minimum domain relevance score threshold
            
        Returns:
            Filtered search results
        """
        # Analyze domain-specific content
        analysis = self.analyze_domain_specific_content(search_results, domain_type)
        
        if not analysis.get("success", False):
            return search_results
        
        # Get domain analysis
        domain_analysis = analysis.get("domain_analysis", {})
        
        # Get domain type (detected or specified)
        effective_domain = domain_analysis.get("domain_type", domain_type)
        
        if effective_domain is None or effective_domain == "unknown":
            return search_results
        
        # Get original results
        results = search_results.get("results", [])
        
        # Get result analyses
        result_analyses = domain_analysis.get("result_analysis", [])
        
        # Create mapping of URL to relevance score
        url_to_score = {
            ra.get("url", ""): ra.get("domain_relevance_score", 0.0) 
            for ra in result_analyses
        }
        
        # Filter results
        filtered_results = []
        for result in results:
            url = result.get("url", "")
            score = url_to_score.get(url, 0.0)
            
            if score >= min_relevance_score:
                # Add domain relevance info to result
                result_with_domain = result.copy()
                result_with_domain["domain_relevance_score"] = score
                result_with_domain["domain_type"] = effective_domain
                filtered_results.append(result_with_domain)
        
        # Create filtered results response
        filtered_response = search_results.copy()
        filtered_response["results"] = filtered_results
        filtered_response["original_result_count"] = len(results)
        filtered_response["filtered_result_count"] = len(filtered_results)
        filtered_response["domain_type"] = effective_domain
        filtered_response["min_relevance_score"] = min_relevance_score
        
        return filtered_response
    
    def get_domain_terminology(self, domain_type: str, language: str = "en") -> List[str]:
        """
        Get terminology for a specific domain and language.
        
        Args:
            domain_type: Domain type
            language: Language code
            
        Returns:
            List of terminology
        """
        domain_data = self.domain_data.get(domain_type, {})
        return domain_data.get("terminology", {}).get(language, [])
    
    def add_domain_terminology(self, domain_type: str, terms: List[str], language: str = "en") -> bool:
        """
        Add terminology to a domain.
        
        Args:
            domain_type: Domain type
            terms: List of terms to add
            language: Language code
            
        Returns:
            True if successful, False otherwise
        """
        # Validate domain type
        if domain_type not in self.DOMAIN_TYPES:
            logger.error(f"Invalid domain type: {domain_type}")
            return False
        
        # Get domain data
        if domain_type not in self.domain_data:
            self.domain_data[domain_type] = self._get_default_domain_data(domain_type)
        
        # Ensure terminology section exists
        if "terminology" not in self.domain_data[domain_type]:
            self.domain_data[domain_type]["terminology"] = {"en": [], "vi": []}
        
        # Ensure language section exists
        if language not in self.domain_data[domain_type]["terminology"]:
            self.domain_data[domain_type]["terminology"][language] = []
        
        # Add terms (avoid duplicates)
        current_terms = set(self.domain_data[domain_type]["terminology"][language])
        for term in terms:
            if term and term not in current_terms:
                self.domain_data[domain_type]["terminology"][language].append(term)
                current_terms.add(term)
        
        # Save updated data
        domain_file = DATA_DIR / f"{domain_type}_domain_data.json"
        try:
            with open(domain_file, 'w', encoding='utf-8') as f:
                json.dump(self.domain_data[domain_type], f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"Error saving domain data for {domain_type}: {str(e)}")
            return False
    
    def get_credible_sources(self, domain_type: str) -> List[str]:
        """
        Get credible sources for a specific domain.
        
        Args:
            domain_type: Domain type
            
        Returns:
            List of credible source domains
        """
        domain_data = self.domain_data.get(domain_type, {})
        return domain_data.get("credible_sources", [])
    
    def add_credible_sources(self, domain_type: str, sources: List[str]) -> bool:
        """
        Add credible sources to a domain.
        
        Args:
            domain_type: Domain type
            sources: List of source domains to add
            
        Returns:
            True if successful, False otherwise
        """
        # Validate domain type
        if domain_type not in self.DOMAIN_TYPES:
            logger.error(f"Invalid domain type: {domain_type}")
            return False
        
        # Get domain data
        if domain_type not in self.domain_data:
            self.domain_data[domain_type] = self._get_default_domain_data(domain_type)
        
        # Ensure credible_sources section exists
        if "credible_sources" not in self.domain_data[domain_type]:
            self.domain_data[domain_type]["credible_sources"] = []
        
        # Add sources (avoid duplicates)
        current_sources = set(self.domain_data[domain_type]["credible_sources"])
        for source in sources:
            if source and source not in current_sources:
                self.domain_data[domain_type]["credible_sources"].append(source)
                current_sources.add(source)
        
        # Save updated data
        domain_file = DATA_DIR / f"{domain_type}_domain_data.json"
        try:
            with open(domain_file, 'w', encoding='utf-8') as f:
                json.dump(self.domain_data[domain_type], f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"Error saving domain data for {domain_type}: {str(e)}")
            return False


# Example usage function
def test_domain_analyzer():
    """Test the domain specific analyzer."""
    # Sample health-related text
    health_text_en = "The patient presented with symptoms of fever, cough, and shortness of breath. The doctor recommended a COVID-19 test and prescribed antibiotics."
    health_text_vi = "Bệnh nhân có các triệu chứng sốt, ho và khó thở. Bác sĩ đề nghị xét nghiệm COVID-19 và kê đơn thuốc kháng sinh."
    
    # Sample finance-related text
    finance_text_en = "The stock market declined by 2% today as investors worried about rising interest rates. Treasury bonds saw increased demand."
    finance_text_vi = "Thị trường chứng khoán giảm 2% hôm nay khi nhà đầu tư lo ngại về lãi suất tăng. Trái phiếu kho bạc thấy nhu cầu tăng."
    
    # Create analyzer
    analyzer = DomainSpecificAnalyzer()
    
    # Test domain detection
    print("Testing domain detection:")
    print(f"Health (EN): {analyzer.detect_domain_type(health_text_en)}")
    print(f"Health (VI): {analyzer.detect_domain_type(health_text_vi)}")
    print(f"Finance (EN): {analyzer.detect_domain_type(finance_text_en)}")
    print(f"Finance (VI): {analyzer.detect_domain_type(finance_text_vi)}")
    
    # Test with a sample search result
    sample_results = {
        "success": True,
        "query": "COVID-19 symptoms treatment",
        "results": [
            {
                "url": "https://www.who.int/health-topics/coronavirus",
                "title": "Coronavirus disease (COVID-19)",
                "snippet": "Learn about COVID-19 symptoms and treatments",
                "content": health_text_en
            },
            {
                "url": "https://example.com/covid19",
                "title": "COVID-19 Information",
                "snippet": "Information about the coronavirus pandemic",
                "content": "COVID-19 has affected millions worldwide. " + health_text_en
            }
        ]
    }
    
    # Test domain-specific analysis
    analysis = analyzer.analyze_domain_specific_content(sample_results, "health")
    print("\nDomain-specific analysis:")
    print(f"Domain type: {analysis['domain_analysis']['domain_type']}")
    print(f"Domain relevance score: {analysis['domain_analysis']['domain_relevance_score']}")
    print(f"Terminology frequency: {analysis['domain_analysis']['terminology_frequency']}")
    
    # Test filtering
    filtered_results = analyzer.filter_results_by_domain(sample_results, "health", 0.5)
    print("\nFiltered results:")
    print(f"Original count: {filtered_results['original_result_count']}")
    print(f"Filtered count: {filtered_results['filtered_result_count']}")

if __name__ == "__main__":
    test_domain_analyzer() 