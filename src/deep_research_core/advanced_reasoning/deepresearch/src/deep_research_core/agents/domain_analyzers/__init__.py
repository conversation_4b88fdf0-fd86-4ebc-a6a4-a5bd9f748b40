"""
Domain analyzers package for domain-specific analysis in deep research core.

This package contains analyzers for detecting and analyzing content in specific domains
such as health, finance, technology, etc.
"""

from typing import Dict, Any, Optional

# Try to import domain specific analyzer if available
try:
    from .domain_specific_analyzer import DomainSpecificAnalyzer
    DOMAIN_ANALYZER_AVAILABLE = True
except ImportError:
    # Define placeholder for when imports fail
    DOMAIN_ANALYZER_AVAILABLE = False 