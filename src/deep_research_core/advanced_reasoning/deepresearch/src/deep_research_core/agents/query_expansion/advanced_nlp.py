"""
<PERSON><PERSON><PERSON> kỹ thuật NLP nâng cao cho mở rộng truy vấn.

Module này cung cấp các kỹ thuật xử lý ngôn ngữ tự nhiên nâng cao để cải thiện
chất lượng và độ phủ của các truy vấn tìm kiếm.
"""

import re
import logging
import random
from typing import Dict, Any, List, Optional, Union, Set, Tuple
from collections import defaultdict

# Thử import các thư viện NLP nâng cao
try:
    import nltk
    from nltk.corpus import wordnet, stopwords
    from nltk.tokenize import word_tokenize
    from nltk.stem import WordNetLemmatizer
    NLTK_AVAILABLE = True
except ImportError:
    NLTK_AVAILABLE = False

try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False

try:
    from transformers import pipeline
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False

# Lấy logger
logger = logging.getLogger(__name__)

class ContextualQueryExpander:
    """Mở rộng truy vấn dựa trên ngữ cảnh và ngữ nghĩa."""
    
    def __init__(self, **kwargs):
        """
        Khởi tạo ContextualQueryExpander.
        
        Args:
            **kwargs: Tham số bổ sung
        """
        self.use_wordnet = kwargs.get("use_wordnet", True)
        self.use_spacy = kwargs.get("use_spacy", SPACY_AVAILABLE)
        self.use_transformers = kwargs.get("use_transformers", TRANSFORMERS_AVAILABLE)
        self.max_expansions = kwargs.get("max_expansions", 5)
        self.language = kwargs.get("language", "en")
        
        # Khởi tạo các thành phần NLP
        self._initialize_nlp_components()
    
    def _initialize_nlp_components(self):
        """Khởi tạo các thành phần NLP cần thiết."""
        # Khởi tạo WordNet
        if self.use_wordnet and NLTK_AVAILABLE:
            try:
                nltk.data.find('corpora/wordnet')
                nltk.data.find('corpora/stopwords')
                nltk.data.find('tokenizers/punkt')
                self.lemmatizer = WordNetLemmatizer()
                self.stopwords = set(stopwords.words('english'))
                logger.info("Đã khởi tạo WordNet thành công")
            except LookupError:
                logger.warning("Không tìm thấy dữ liệu WordNet, đang tải...")
                nltk.download('wordnet', quiet=True)
                nltk.download('stopwords', quiet=True)
                nltk.download('punkt', quiet=True)
                self.lemmatizer = WordNetLemmatizer()
                self.stopwords = set(stopwords.words('english'))
        else:
            self.lemmatizer = None
            self.stopwords = set()
        
        # Khởi tạo SpaCy
        if self.use_spacy and SPACY_AVAILABLE:
            try:
                if self.language == "en":
                    self.nlp = spacy.load("en_core_web_sm")
                elif self.language == "vi":
                    self.nlp = spacy.load("xx_ent_wiki_sm")  # Mô hình đa ngôn ngữ
                else:
                    self.nlp = spacy.load("xx_ent_wiki_sm")
                logger.info(f"Đã khởi tạo SpaCy với mô hình cho ngôn ngữ {self.language}")
            except OSError:
                logger.warning(f"Không tìm thấy mô hình SpaCy cho ngôn ngữ {self.language}, đang tải mô hình đa ngôn ngữ...")
                spacy.cli.download("xx_ent_wiki_sm")
                self.nlp = spacy.load("xx_ent_wiki_sm")
        else:
            self.nlp = None
        
        # Khởi tạo Transformers
        if self.use_transformers and TRANSFORMERS_AVAILABLE:
            try:
                self.fill_mask = pipeline("fill-mask", model="bert-base-uncased")
                logger.info("Đã khởi tạo Transformers fill-mask pipeline")
            except Exception as e:
                logger.error(f"Lỗi khi khởi tạo Transformers: {str(e)}")
                self.fill_mask = None
        else:
            self.fill_mask = None
    
    def expand_query(self, query: str) -> List[str]:
        """
        Mở rộng truy vấn sử dụng các kỹ thuật NLP nâng cao.
        
        Args:
            query: Truy vấn cần mở rộng
            
        Returns:
            Danh sách các truy vấn đã mở rộng
        """
        expanded_queries = [query]  # Luôn giữ truy vấn gốc
        
        # Tiền xử lý truy vấn
        processed_query = self._preprocess_query(query)
        
        # Mở rộng với WordNet
        if self.lemmatizer is not None:
            wordnet_expansions = self._expand_with_wordnet(processed_query)
            expanded_queries.extend(wordnet_expansions)
        
        # Mở rộng với SpaCy
        if self.nlp is not None:
            spacy_expansions = self._expand_with_spacy(processed_query)
            expanded_queries.extend(spacy_expansions)
        
        # Mở rộng với Transformers
        if self.fill_mask is not None:
            transformer_expansions = self._expand_with_transformers(processed_query)
            expanded_queries.extend(transformer_expansions)
        
        # Loại bỏ trùng lặp và giới hạn số lượng
        unique_expansions = list(dict.fromkeys(expanded_queries))
        return unique_expansions[:self.max_expansions + 1]  # +1 cho truy vấn gốc
    
    def _preprocess_query(self, query: str) -> str:
        """
        Tiền xử lý truy vấn.
        
        Args:
            query: Truy vấn cần xử lý
            
        Returns:
            Truy vấn đã xử lý
        """
        # Chuyển về chữ thường
        query = query.lower()
        
        # Loại bỏ ký tự đặc biệt
        query = re.sub(r'[^\w\s]', ' ', query)
        
        # Loại bỏ khoảng trắng thừa
        query = re.sub(r'\s+', ' ', query).strip()
        
        return query
    
    def _expand_with_wordnet(self, query: str) -> List[str]:
        """
        Mở rộng truy vấn sử dụng WordNet.
        
        Args:
            query: Truy vấn cần mở rộng
            
        Returns:
            Danh sách các truy vấn đã mở rộng
        """
        expansions = []
        
        # Tách từ
        tokens = word_tokenize(query)
        
        # Loại bỏ stopwords
        content_tokens = [token for token in tokens if token.lower() not in self.stopwords]
        
        # Tìm từ đồng nghĩa cho mỗi từ
        for token in content_tokens:
            # Lemmatize từ
            lemma = self.lemmatizer.lemmatize(token)
            
            # Tìm các synsets
            synsets = wordnet.synsets(lemma)
            
            # Lấy từ đồng nghĩa
            synonyms = set()
            for synset in synsets[:3]:  # Giới hạn số lượng synsets
                for lemma_obj in synset.lemmas():
                    synonym = lemma_obj.name().replace('_', ' ')
                    if synonym != token and synonym not in synonyms:
                        synonyms.add(synonym)
            
            # Tạo truy vấn mới bằng cách thay thế từ
            for synonym in list(synonyms)[:2]:  # Giới hạn số lượng từ đồng nghĩa
                new_tokens = tokens.copy()
                for i, t in enumerate(new_tokens):
                    if t == token:
                        new_tokens[i] = synonym
                        break
                
                new_query = ' '.join(new_tokens)
                expansions.append(new_query)
        
        return expansions
    
    def _expand_with_spacy(self, query: str) -> List[str]:
        """
        Mở rộng truy vấn sử dụng SpaCy.
        
        Args:
            query: Truy vấn cần mở rộng
            
        Returns:
            Danh sách các truy vấn đã mở rộng
        """
        expansions = []
        
        # Phân tích truy vấn với SpaCy
        doc = self.nlp(query)
        
        # Tìm các thực thể
        entities = [(ent.text, ent.label_) for ent in doc.ents]
        
        # Mở rộng dựa trên thực thể
        if entities:
            # Thêm truy vấn với các thực thể được nhấn mạnh
            for entity, label in entities:
                # Thêm dấu ngoặc kép cho thực thể
                quoted_entity = f'"{entity}"'
                new_query = query.replace(entity, quoted_entity)
                expansions.append(new_query)
        
        # Mở rộng dựa trên cấu trúc ngữ pháp
        noun_chunks = list(doc.noun_chunks)
        if noun_chunks:
            # Tạo truy vấn mới bằng cách nhấn mạnh các cụm danh từ
            for chunk in noun_chunks:
                # Thêm dấu ngoặc kép cho cụm danh từ
                quoted_chunk = f'"{chunk.text}"'
                new_query = query.replace(chunk.text, quoted_chunk)
                expansions.append(new_query)
        
        return expansions
    
    def _expand_with_transformers(self, query: str) -> List[str]:
        """
        Mở rộng truy vấn sử dụng Transformers.
        
        Args:
            query: Truy vấn cần mở rộng
            
        Returns:
            Danh sách các truy vấn đã mở rộng
        """
        expansions = []
        
        # Tách từ
        tokens = query.split()
        
        # Chỉ áp dụng cho truy vấn có ít nhất 3 từ
        if len(tokens) >= 3:
            # Chọn ngẫu nhiên một từ để thay thế
            for _ in range(min(2, len(tokens))):
                # Chọn vị trí ngẫu nhiên (không phải từ đầu tiên)
                pos = random.randint(1, len(tokens) - 1)
                
                # Tạo mẫu mask
                masked_query = ' '.join(tokens[:pos] + ['[MASK]'] + tokens[pos+1:])
                
                try:
                    # Dự đoán từ thay thế
                    predictions = self.fill_mask(masked_query)
                    
                    # Lấy 2 dự đoán đầu tiên
                    for pred in predictions[:2]:
                        # Tạo truy vấn mới
                        new_tokens = tokens.copy()
                        new_tokens[pos] = pred['token_str'].strip()
                        new_query = ' '.join(new_tokens)
                        expansions.append(new_query)
                except Exception as e:
                    logger.error(f"Lỗi khi sử dụng Transformers fill-mask: {str(e)}")
        
        return expansions
