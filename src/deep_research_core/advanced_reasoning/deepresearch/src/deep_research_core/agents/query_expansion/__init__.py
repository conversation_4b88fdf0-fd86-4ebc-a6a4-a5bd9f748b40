"""
Mở rộng truy vấn cho WebSearchAgent.

Module này cung cấp các phương pháp mở rộng truy vấn để cải thiện chất lượng
và độ phủ của các truy vấn tìm kiếm.
"""

from typing import List, Optional, Dict, Any

# Import các module mở rộng truy vấn
try:
    from deep_research_core.agents.query_expansion.advanced_nlp import ContextualQueryExpander
    CONTEXTUAL_EXPANDER_AVAILABLE = True
except ImportError:
    CONTEXTUAL_EXPANDER_AVAILABLE = False

try:
    from deep_research_core.agents.query_expansion.semantic_expansion import SemanticQueryExpander
    SEMANTIC_EXPANDER_AVAILABLE = True
except ImportError:
    SEMANTIC_EXPANDER_AVAILABLE = False

class QueryExpansionPipeline:
    """Pipeline mở rộng truy vấn kết hợp nhiều phương pháp."""
    
    def __init__(self, **kwargs):
        """
        Khởi tạo QueryExpansionPipeline.
        
        Args:
            **kwargs: Tham số bổ sung
        """
        self.max_expansions = kwargs.get("max_expansions", 5)
        self.expanders = []
        
        # Thêm các expander
        if kwargs.get("use_contextual", True) and CONTEXTUAL_EXPANDER_AVAILABLE:
            self.expanders.append(ContextualQueryExpander(**kwargs))
        
        if kwargs.get("use_semantic", True) and SEMANTIC_EXPANDER_AVAILABLE:
            self.expanders.append(SemanticQueryExpander(**kwargs))
    
    def expand(self, query: str) -> List[str]:
        """
        Mở rộng truy vấn sử dụng tất cả các phương pháp.
        
        Args:
            query: Truy vấn cần mở rộng
            
        Returns:
            Danh sách các truy vấn đã mở rộng
        """
        # Luôn giữ truy vấn gốc
        expanded_queries = [query]
        
        # Áp dụng từng expander
        for expander in self.expanders:
            expanded_queries.extend(expander.expand_query(query))
        
        # Loại bỏ trùng lặp và giới hạn số lượng
        unique_expansions = list(dict.fromkeys(expanded_queries))
        return unique_expansions[:self.max_expansions + 1]  # +1 cho truy vấn gốc

def create_default_expansion_pipeline(**kwargs) -> QueryExpansionPipeline:
    """
    Tạo pipeline mở rộng truy vấn mặc định.
    
    Args:
        **kwargs: Tham số bổ sung
        
    Returns:
        QueryExpansionPipeline đã cấu hình
    """
    return QueryExpansionPipeline(
        max_expansions=kwargs.get("max_expansions", 5),
        use_contextual=kwargs.get("use_contextual", True),
        use_semantic=kwargs.get("use_semantic", True),
        language=kwargs.get("language", "en"),
        similarity_threshold=kwargs.get("similarity_threshold", 0.7)
    )

__all__ = [
    'QueryExpansionPipeline',
    'create_default_expansion_pipeline'
]

if CONTEXTUAL_EXPANDER_AVAILABLE:
    __all__.append('ContextualQueryExpander')

if SEMANTIC_EXPANDER_AVAILABLE:
    __all__.append('SemanticQueryExpander')
