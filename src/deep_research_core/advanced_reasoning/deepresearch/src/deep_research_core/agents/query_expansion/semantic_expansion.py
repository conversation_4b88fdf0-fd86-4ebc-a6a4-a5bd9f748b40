"""
Mở rộng truy vấn dựa trên ngữ nghĩa.

Module này cung cấp các phương pháp mở rộng truy vấn dựa trên ngữ nghĩa,
sử dụng các mô hình nhúng từ và kỹ thuật phân tích ngữ nghĩa.
"""

import logging
import numpy as np
from typing import Dict, Any, List, Optional, Union, Set, Tuple

# Thử import các thư viện nhúng từ
try:
    import gensim
    from gensim.models import Word2Vec, FastText, KeyedVectors
    GENSIM_AVAILABLE = True
except ImportError:
    GENSIM_AVAILABLE = False

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

# Lấy logger
logger = logging.getLogger(__name__)

class SemanticQueryExpander:
    """Mở rộng truy vấn dựa trên ngữ nghĩa."""
    
    def __init__(self, **kwargs):
        """
        Khởi tạo SemanticQueryExpander.
        
        Args:
            **kwargs: Tham số bổ sung
        """
        self.use_word2vec = kwargs.get("use_word2vec", GENSIM_AVAILABLE)
        self.use_sentence_transformers = kwargs.get("use_sentence_transformers", SENTENCE_TRANSFORMERS_AVAILABLE)
        self.max_expansions = kwargs.get("max_expansions", 5)
        self.language = kwargs.get("language", "en")
        self.similarity_threshold = kwargs.get("similarity_threshold", 0.7)
        
        # Khởi tạo các mô hình nhúng từ
        self._initialize_embedding_models()
    
    def _initialize_embedding_models(self):
        """Khởi tạo các mô hình nhúng từ."""
        # Khởi tạo Word2Vec
        if self.use_word2vec and GENSIM_AVAILABLE:
            try:
                # Thử tải mô hình Word2Vec được huấn luyện trước
                if self.language == "en":
                    self.word2vec_model = KeyedVectors.load_word2vec_format(
                        "models/GoogleNews-vectors-negative300.bin", binary=True
                    )
                elif self.language == "vi":
                    self.word2vec_model = KeyedVectors.load_word2vec_format(
                        "models/vi_word2vec.bin", binary=True
                    )
                else:
                    self.word2vec_model = None
                
                logger.info(f"Đã tải mô hình Word2Vec cho ngôn ngữ {self.language}")
            except Exception as e:
                logger.warning(f"Không thể tải mô hình Word2Vec: {str(e)}")
                self.word2vec_model = None
        else:
            self.word2vec_model = None
        
        # Khởi tạo Sentence Transformers
        if self.use_sentence_transformers and SENTENCE_TRANSFORMERS_AVAILABLE:
            try:
                if self.language == "en":
                    self.sentence_model = SentenceTransformer("all-MiniLM-L6-v2")
                elif self.language == "vi":
                    self.sentence_model = SentenceTransformer("paraphrase-multilingual-MiniLM-L12-v2")
                else:
                    self.sentence_model = SentenceTransformer("paraphrase-multilingual-MiniLM-L12-v2")
                
                logger.info(f"Đã tải mô hình Sentence Transformers cho ngôn ngữ {self.language}")
            except Exception as e:
                logger.error(f"Lỗi khi tải mô hình Sentence Transformers: {str(e)}")
                self.sentence_model = None
        else:
            self.sentence_model = None
    
    def expand_query(self, query: str) -> List[str]:
        """
        Mở rộng truy vấn sử dụng các kỹ thuật ngữ nghĩa.
        
        Args:
            query: Truy vấn cần mở rộng
            
        Returns:
            Danh sách các truy vấn đã mở rộng
        """
        expanded_queries = [query]  # Luôn giữ truy vấn gốc
        
        # Mở rộng với Word2Vec
        if self.word2vec_model is not None:
            word2vec_expansions = self._expand_with_word2vec(query)
            expanded_queries.extend(word2vec_expansions)
        
        # Mở rộng với Sentence Transformers
        if self.sentence_model is not None:
            sentence_expansions = self._expand_with_sentence_transformers(query)
            expanded_queries.extend(sentence_expansions)
        
        # Loại bỏ trùng lặp và giới hạn số lượng
        unique_expansions = list(dict.fromkeys(expanded_queries))
        return unique_expansions[:self.max_expansions + 1]  # +1 cho truy vấn gốc
    
    def _expand_with_word2vec(self, query: str) -> List[str]:
        """
        Mở rộng truy vấn sử dụng Word2Vec.
        
        Args:
            query: Truy vấn cần mở rộng
            
        Returns:
            Danh sách các truy vấn đã mở rộng
        """
        expansions = []
        
        # Tách từ
        tokens = query.lower().split()
        
        # Mở rộng từng từ
        for i, token in enumerate(tokens):
            try:
                # Tìm các từ tương tự
                similar_words = self.word2vec_model.most_similar(token, topn=3)
                
                # Tạo truy vấn mới bằng cách thay thế từ
                for word, similarity in similar_words:
                    if similarity >= self.similarity_threshold:
                        new_tokens = tokens.copy()
                        new_tokens[i] = word
                        new_query = ' '.join(new_tokens)
                        expansions.append(new_query)
            except KeyError:
                # Từ không có trong từ điển
                continue
        
        return expansions
    
    def _expand_with_sentence_transformers(self, query: str) -> List[str]:
        """
        Mở rộng truy vấn sử dụng Sentence Transformers.
        
        Args:
            query: Truy vấn cần mở rộng
            
        Returns:
            Danh sách các truy vấn đã mở rộng
        """
        # Danh sách các biến thể truy vấn tiềm năng
        potential_variations = [
            f"information about {query}",
            f"what is {query}",
            f"{query} explanation",
            f"{query} tutorial",
            f"{query} guide",
            f"{query} examples",
            f"how to {query}",
            f"{query} best practices"
        ]
        
        # Tính embedding cho truy vấn gốc
        query_embedding = self.sentence_model.encode(query)
        
        # Tính embedding cho các biến thể
        variation_embeddings = self.sentence_model.encode(potential_variations)
        
        # Tính độ tương tự
        similarities = np.dot(variation_embeddings, query_embedding) / (
            np.linalg.norm(variation_embeddings, axis=1) * np.linalg.norm(query_embedding)
        )
        
        # Chọn các biến thể có độ tương tự cao
        expansions = []
        for i, similarity in enumerate(similarities):
            if similarity >= self.similarity_threshold:
                expansions.append(potential_variations[i])
        
        # Giới hạn số lượng
        return expansions[:3]
