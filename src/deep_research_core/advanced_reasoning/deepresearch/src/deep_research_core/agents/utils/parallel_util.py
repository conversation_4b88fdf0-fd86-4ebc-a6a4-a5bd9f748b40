"""
Module tiện ích xử lý song song.

<PERSON><PERSON><PERSON> này cung cấp các công cụ để xử lý tác vụ song song an toàn,
tránh memory leak và quản lý tài nguyên hiệu quả.
"""

import logging
import time
import threading
import concurrent.futures
import asyncio
import functools
import gc
import traceback
from typing import List, Dict, Any, Callable, Optional, Tuple, TypeVar, Generic, Union

# Thiết lập logging
logger = logging.getLogger(__name__)

# Định nghĩa kiểu generic
T = TypeVar('T')  # Kiểu kết quả
P = TypeVar('P')  # Ki<PERSON><PERSON> tham số

class TaskResult(Generic[T]):
    """Lớp đóng gói kết quả của tác vụ."""
    
    def __init__(self, 
                result: Optional[T] = None, 
                error: Optional[Exception] = None,
                execution_time: float = 0.0,
                task_id: Optional[str] = None):
        """
        Khởi t<PERSON><PERSON> kết quả tác vụ.
        
        Args:
            result: <PERSON><PERSON><PERSON> quả của tác vụ
            error: Lỗi nếu có
            execution_time: <PERSON>h<PERSON><PERSON> gian thực thi
            task_id: ID của tác vụ
        """
        self.result = result
        self.error = error
        self.execution_time = execution_time
        self.task_id = task_id
        self.success = error is None
        self.timestamp = time.time()
    
    def __str__(self) -> str:
        if self.success:
            return f"TaskResult(success={self.success}, time={self.execution_time:.3f}s)"
        else:
            return f"TaskResult(success={self.success}, error={str(self.error)})"
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Chuyển đổi kết quả thành dictionary.
        
        Returns:
            Dictionary chứa thông tin kết quả
        """
        return {
            "result": self.result,
            "error": str(self.error) if self.error else None,
            "error_type": self.error.__class__.__name__ if self.error else None,
            "execution_time": self.execution_time,
            "task_id": self.task_id,
            "success": self.success,
            "timestamp": self.timestamp
        }


class SafeParallelExecutor:
    """Lớp thực thi an toàn cho xử lý song song."""
    
    def __init__(self, 
                max_workers: int = 5, 
                timeout: int = 60,
                use_asyncio: bool = False,
                cleanup_interval: int = 3):
        """
        Khởi tạo executor.
        
        Args:
            max_workers: Số lượng worker tối đa
            timeout: Thời gian chờ tối đa (giây)
            use_asyncio: Sử dụng asyncio thay vì threading
            cleanup_interval: Khoảng thời gian dọn dẹp (giây)
        """
        self.max_workers = max_workers
        self.timeout = timeout
        self.use_asyncio = use_asyncio
        self.cleanup_interval = cleanup_interval
        
        self.active_tasks = set()
        self.lock = threading.RLock()
        
        # Thống kê hiệu suất
        self.stats = {
            "total_tasks": 0,
            "successful_tasks": 0,
            "failed_tasks": 0,
            "total_execution_time": 0.0,
            "avg_execution_time": 0.0,
            "max_concurrent": 0,
            "timeouts": 0,
            "last_gc_time": 0.0,
            "memory_usage": {}
        }
        
        logger.info(f"SafeParallelExecutor khởi tạo với {max_workers} workers")
    
    def execute_many(self, 
                   func: Callable[..., T], 
                   param_list: List[Tuple],
                   task_ids: Optional[List[str]] = None) -> List[TaskResult[T]]:
        """
        Thực thi hàm với nhiều bộ tham số.
        
        Args:
            func: Hàm cần thực thi
            param_list: Danh sách các bộ tham số (dạng tuple)
            task_ids: Danh sách ID tác vụ tương ứng
            
        Returns:
            Danh sách kết quả
        """
        # Chuẩn bị danh sách ID tác vụ
        if not task_ids:
            task_ids = [f"task_{i}" for i in range(len(param_list))]
        elif len(task_ids) < len(param_list):
            # Bổ sung ID nếu thiếu
            for i in range(len(task_ids), len(param_list)):
                task_ids.append(f"task_{i}")
        
        # Chọn phương pháp thực thi
        if self.use_asyncio:
            return self._execute_async(func, param_list, task_ids)
        else:
            return self._execute_threading(func, param_list, task_ids)
    
    def _execute_threading(self, 
                        func: Callable[..., T], 
                        param_list: List[Tuple],
                        task_ids: List[str]) -> List[TaskResult[T]]:
        """
        Thực thi song song sử dụng multi-threading.
        
        Args:
            func: Hàm cần thực thi
            param_list: Danh sách các bộ tham số
            task_ids: Danh sách ID tác vụ
            
        Returns:
            Danh sách kết quả
        """
        results = [None] * len(param_list)
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = []
            
            # Cập nhật số lượng tác vụ đồng thời tối đa
            concurrent_count = min(len(param_list), self.max_workers)
            self.stats["max_concurrent"] = max(self.stats["max_concurrent"], concurrent_count)
            
            # Cập nhật tổng số tác vụ
            self.stats["total_tasks"] += len(param_list)
            
            # Tạo future cho mỗi tác vụ
            for i, (params, task_id) in enumerate(zip(param_list, task_ids)):
                future = executor.submit(self._safe_execute, func, params, task_id)
                future._index = i  # Lưu index để xử lý kết quả đúng thứ tự
                future._task_id = task_id  # Lưu task_id
                futures.append(future)
                
                with self.lock:
                    self.active_tasks.add(future)
            
            # Xử lý kết quả
            for future in concurrent.futures.as_completed(futures):
                with self.lock:
                    if future in self.active_tasks:
                        self.active_tasks.remove(future)
                
                try:
                    result = future.result(timeout=self.timeout)
                    results[future._index] = result
                    
                    # Cập nhật thống kê
                    if result.success:
                        self.stats["successful_tasks"] += 1
                    else:
                        self.stats["failed_tasks"] += 1
                    
                    self.stats["total_execution_time"] += result.execution_time
                    self.stats["avg_execution_time"] = (
                        self.stats["total_execution_time"] / 
                        (self.stats["successful_tasks"] + self.stats["failed_tasks"])
                    )
                    
                except concurrent.futures.TimeoutError:
                    logger.error(f"Timeout khi thực thi tác vụ {future._task_id}")
                    results[future._index] = TaskResult(
                        error=TimeoutError(f"Task timed out after {self.timeout}s"),
                        task_id=future._task_id
                    )
                    self.stats["timeouts"] += 1
                    self.stats["failed_tasks"] += 1
                    
                except Exception as e:
                    logger.error(f"Lỗi khi xử lý kết quả tác vụ {future._task_id}: {str(e)}")
                    results[future._index] = TaskResult(
                        error=e,
                        task_id=future._task_id
                    )
                    self.stats["failed_tasks"] += 1
            
            # Dọn dẹp tài nguyên nếu cần
            self._cleanup_resources()
        
        return results
    
    def _execute_async(self, 
                     func: Callable[..., T], 
                     param_list: List[Tuple],
                     task_ids: List[str]) -> List[TaskResult[T]]:
        """
        Thực thi song song sử dụng asyncio.
        
        Args:
            func: Hàm cần thực thi
            param_list: Danh sách các bộ tham số
            task_ids: Danh sách ID tác vụ
            
        Returns:
            Danh sách kết quả
        """
        async def _main():
            # Cập nhật số lượng tác vụ đồng thời tối đa
            concurrent_count = min(len(param_list), self.max_workers)
            self.stats["max_concurrent"] = max(self.stats["max_concurrent"], concurrent_count)
            
            # Cập nhật tổng số tác vụ
            self.stats["total_tasks"] += len(param_list)
            
            # Tạo ngăn xếp semaphore để giới hạn số lượng tác vụ đồng thời
            semaphore = asyncio.Semaphore(self.max_workers)
            
            async def _execute_with_semaphore(params, task_id, index):
                async with semaphore:
                    # Thực hiện tác vụ trong thread pool để tránh chặn event loop
                    loop = asyncio.get_event_loop()
                    try:
                        result = await loop.run_in_executor(
                            None,
                            functools.partial(
                                self._safe_execute,
                                func, params, task_id
                            )
                        )
                        
                        # Cập nhật thống kê
                        if result.success:
                            self.stats["successful_tasks"] += 1
                        else:
                            self.stats["failed_tasks"] += 1
                        
                        self.stats["total_execution_time"] += result.execution_time
                        self.stats["avg_execution_time"] = (
                            self.stats["total_execution_time"] / 
                            (self.stats["successful_tasks"] + self.stats["failed_tasks"])
                        )
                        
                        return result, index
                    except asyncio.TimeoutError:
                        logger.error(f"Timeout khi thực thi tác vụ {task_id}")
                        self.stats["timeouts"] += 1
                        self.stats["failed_tasks"] += 1
                        return TaskResult(
                            error=TimeoutError(f"Task timed out after {self.timeout}s"),
                            task_id=task_id
                        ), index
                    except Exception as e:
                        logger.error(f"Lỗi khi thực thi tác vụ {task_id}: {str(e)}")
                        self.stats["failed_tasks"] += 1
                        return TaskResult(
                            error=e,
                            task_id=task_id
                        ), index
            
            # Tạo task cho mỗi bộ tham số
            tasks = []
            for i, (params, task_id) in enumerate(zip(param_list, task_ids)):
                task = asyncio.create_task(_execute_with_semaphore(params, task_id, i))
                tasks.append(task)
            
            # Chờ tất cả task hoàn thành
            results = [None] * len(param_list)
            for task in asyncio.as_completed(tasks, timeout=self.timeout):
                try:
                    result, index = await task
                    results[index] = result
                except asyncio.TimeoutError:
                    logger.error(f"Timeout tổng thể khi thực thi lô tác vụ")
                    # Không biết task nào timeout, nên không thể gán kết quả
                except Exception as e:
                    logger.error(f"Lỗi khi chờ task hoàn thành: {str(e)}")
            
            # Dọn dẹp tài nguyên nếu cần
            self._cleanup_resources()
            
            return results
        
        # Chạy event loop
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            # Tạo loop mới nếu không có loop đang chạy
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        if loop.is_running():
            # Đang trong event loop, tạo Future và chờ kết quả
            task = asyncio.create_task(_main())
            try:
                return loop.run_until_complete(
                    asyncio.wait_for(task, timeout=self.timeout * 2)
                )
            except asyncio.TimeoutError:
                logger.error(f"Timeout tổng thể khi thực thi lô tác vụ")
                return [
                    TaskResult(
                        error=TimeoutError(f"Batch processing timed out after {self.timeout*2}s"),
                        task_id=task_id
                    ) for task_id in task_ids
                ]
        else:
            # Chạy trong loop mới
            try:
                return loop.run_until_complete(_main())
            finally:
                loop.close()
    
    def _safe_execute(self, 
                    func: Callable[..., T], 
                    params: Tuple,
                    task_id: str) -> TaskResult[T]:
        """
        Thực thi an toàn hàm với tham số.
        
        Args:
            func: Hàm cần thực thi
            params: Bộ tham số
            task_id: ID tác vụ
            
        Returns:
            Kết quả đóng gói
        """
        start_time = time.time()
        
        try:
            # Thực thi hàm với tham số
            result = func(*params)
            
            execution_time = time.time() - start_time
            return TaskResult(
                result=result,
                execution_time=execution_time,
                task_id=task_id
            )
        except Exception as e:
            logger.error(f"Lỗi khi thực thi tác vụ {task_id}: {str(e)}")
            logger.debug(traceback.format_exc())
            
            execution_time = time.time() - start_time
            return TaskResult(
                error=e,
                execution_time=execution_time,
                task_id=task_id
            )
        finally:
            # Gợi ý garbage collector nếu cần
            if time.time() - self.stats["last_gc_time"] > self.cleanup_interval:
                self._cleanup_resources()
    
    def execute_batch(self, 
                    func: Callable[..., T], 
                    param_list: List[Tuple],
                    batch_size: int = 3,
                    delay_between_batches: float = 1.0,
                    task_ids: Optional[List[str]] = None) -> List[TaskResult[T]]:
        """
        Thực thi theo lô với số lượng tham số lớn.
        
        Args:
            func: Hàm cần thực thi
            param_list: Danh sách các bộ tham số
            batch_size: Kích thước lô
            delay_between_batches: Thời gian chờ giữa các lô
            task_ids: Danh sách ID tác vụ tương ứng
            
        Returns:
            Danh sách kết quả
        """
        results = []
        total_batches = (len(param_list) + batch_size - 1) // batch_size
        
        # Chuẩn bị danh sách ID tác vụ
        if not task_ids:
            task_ids = [f"task_{i}" for i in range(len(param_list))]
        elif len(task_ids) < len(param_list):
            # Bổ sung ID nếu thiếu
            for i in range(len(task_ids), len(param_list)):
                task_ids.append(f"task_{i}")
        
        logger.info(f"Thực thi theo lô: {len(param_list)} tác vụ, {total_batches} lô, kích thước lô {batch_size}")
        
        for i in range(0, len(param_list), batch_size):
            # Lấy lô tham số hiện tại
            batch_params = param_list[i:i+batch_size]
            batch_ids = task_ids[i:i+batch_size]
            
            # Thực hiện tác vụ lô
            logger.info(f"Xử lý lô {i//batch_size + 1}/{total_batches}: {len(batch_params)} tác vụ")
            batch_results = self.execute_many(func, batch_params, batch_ids)
            
            # Thêm kết quả vào danh sách
            results.extend(batch_results)
            
            # Dọn dẹp tài nguyên sau mỗi lô
            self._cleanup_resources()
            
            # Tạm dừng một chút để tránh quá tải
            if i + batch_size < len(param_list) and delay_between_batches > 0:
                time.sleep(delay_between_batches)
        
        return results
    
    def _cleanup_resources(self) -> None:
        """Dọn dẹp tài nguyên sau khi thực thi."""
        # Ghi nhận thời điểm dọn dẹp
        self.stats["last_gc_time"] = time.time()
        
        # Gợi ý cho garbage collector
        gc.collect(0)  # Chỉ thu thập thế hệ 0, ít tốn thời gian hơn
        
        # Ghi nhận thông tin sử dụng bộ nhớ nếu có
        try:
            import psutil
            process = psutil.Process()
            self.stats["memory_usage"] = {
                "timestamp": time.time(),
                "rss_mb": process.memory_info().rss / (1024 * 1024),
                "vms_mb": process.memory_info().vms / (1024 * 1024),
                "percent": process.memory_percent()
            }
        except ImportError:
            pass
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Lấy thống kê hiệu suất.
        
        Returns:
            Dictionary chứa thông tin thống kê
        """
        return {
            "timestamp": time.time(),
            "total_tasks": self.stats["total_tasks"],
            "successful_tasks": self.stats["successful_tasks"],
            "failed_tasks": self.stats["failed_tasks"],
            "success_rate": (
                self.stats["successful_tasks"] / max(1, self.stats["total_tasks"]) * 100
            ),
            "avg_execution_time": self.stats["avg_execution_time"],
            "total_execution_time": self.stats["total_execution_time"],
            "max_concurrent": self.stats["max_concurrent"],
            "timeouts": self.stats["timeouts"],
            "memory_usage": self.stats["memory_usage"],
            "active_tasks": len(self.active_tasks)
        }
    
    def reset_stats(self) -> None:
        """Reset thống kê hiệu suất."""
        self.stats = {
            "total_tasks": 0,
            "successful_tasks": 0,
            "failed_tasks": 0,
            "total_execution_time": 0.0,
            "avg_execution_time": 0.0,
            "max_concurrent": 0,
            "timeouts": 0,
            "last_gc_time": time.time(),
            "memory_usage": {}
        }


# Tạo thể hiện global cho module
parallel_executor = SafeParallelExecutor() 