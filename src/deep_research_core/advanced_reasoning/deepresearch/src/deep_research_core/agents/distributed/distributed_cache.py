"""
Cache phân tán cho WebSearchAgent.

Module này cung cấp các lớp và hàm để triển khai cache phân tán,
gi<PERSON><PERSON> cải thiện hiệu suất và khả năng mở rộng của WebSearchAgent.
"""

import os
import json
import time
import pickle
import logging
import hashlib
from typing import Dict, Any, List, Optional, Union, Tuple, Set
from datetime import datetime, timedelta

# Thử import các thư viện phân tán
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

try:
    import memcached
    MEMCACHED_AVAILABLE = True
except ImportError:
    MEMCACHED_AVAILABLE = False

# Lấy logger
logger = logging.getLogger(__name__)

class DistributedCache:
    """Cache phân tán cơ bản."""
    
    def __init__(self, **kwargs):
        """
        Khởi tạo DistributedCache.
        
        Args:
            **kwargs: Tham số bổ sung
        """
        self.ttl = kwargs.get("ttl", 86400)  # 1 ngày
        self.namespace = kwargs.get("namespace", "websearch")
    
    def get(self, key: str) -> Optional[Any]:
        """
        Lấy giá trị từ cache.
        
        Args:
            key: Khóa cache
            
        Returns:
            Giá trị cache hoặc None nếu không tìm thấy
        """
        raise NotImplementedError("Phương thức get() cần được triển khai bởi lớp con")
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        Đặt giá trị vào cache.
        
        Args:
            key: Khóa cache
            value: Giá trị cần lưu
            ttl: Thời gian sống (giây), None để sử dụng giá trị mặc định
            
        Returns:
            True nếu thành công, False nếu thất bại
        """
        raise NotImplementedError("Phương thức set() cần được triển khai bởi lớp con")
    
    def delete(self, key: str) -> bool:
        """
        Xóa giá trị khỏi cache.
        
        Args:
            key: Khóa cache
            
        Returns:
            True nếu thành công, False nếu thất bại
        """
        raise NotImplementedError("Phương thức delete() cần được triển khai bởi lớp con")
    
    def clear(self) -> bool:
        """
        Xóa tất cả các giá trị trong cache.
        
        Returns:
            True nếu thành công, False nếu thất bại
        """
        raise NotImplementedError("Phương thức clear() cần được triển khai bởi lớp con")
    
    def _format_key(self, key: str) -> str:
        """
        Định dạng khóa cache.
        
        Args:
            key: Khóa gốc
            
        Returns:
            Khóa đã định dạng
        """
        return f"{self.namespace}:{key}"


class RedisCache(DistributedCache):
    """Cache phân tán sử dụng Redis."""
    
    def __init__(self, **kwargs):
        """
        Khởi tạo RedisCache.
        
        Args:
            **kwargs: Tham số bổ sung
        """
        super().__init__(**kwargs)
        
        if not REDIS_AVAILABLE:
            raise ImportError("Redis không khả dụng. Hãy cài đặt gói redis: pip install redis")
        
        # Khởi tạo kết nối Redis
        self.host = kwargs.get("host", "localhost")
        self.port = kwargs.get("port", 6379)
        self.db = kwargs.get("db", 0)
        self.password = kwargs.get("password", None)
        
        self.client = redis.Redis(
            host=self.host,
            port=self.port,
            db=self.db,
            password=self.password,
            decode_responses=False
        )
    
    def get(self, key: str) -> Optional[Any]:
        """
        Lấy giá trị từ cache Redis.
        
        Args:
            key: Khóa cache
            
        Returns:
            Giá trị cache hoặc None nếu không tìm thấy
        """
        try:
            formatted_key = self._format_key(key)
            data = self.client.get(formatted_key)
            
            if data is None:
                return None
            
            return pickle.loads(data)
        except Exception as e:
            logger.error(f"Lỗi khi lấy giá trị từ Redis cache: {str(e)}")
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        Đặt giá trị vào cache Redis.
        
        Args:
            key: Khóa cache
            value: Giá trị cần lưu
            ttl: Thời gian sống (giây), None để sử dụng giá trị mặc định
            
        Returns:
            True nếu thành công, False nếu thất bại
        """
        try:
            formatted_key = self._format_key(key)
            data = pickle.dumps(value)
            
            expiration = ttl if ttl is not None else self.ttl
            
            self.client.set(formatted_key, data, ex=expiration)
            return True
        except Exception as e:
            logger.error(f"Lỗi khi đặt giá trị vào Redis cache: {str(e)}")
            return False
    
    def delete(self, key: str) -> bool:
        """
        Xóa giá trị khỏi cache Redis.
        
        Args:
            key: Khóa cache
            
        Returns:
            True nếu thành công, False nếu thất bại
        """
        try:
            formatted_key = self._format_key(key)
            self.client.delete(formatted_key)
            return True
        except Exception as e:
            logger.error(f"Lỗi khi xóa giá trị khỏi Redis cache: {str(e)}")
            return False
    
    def clear(self) -> bool:
        """
        Xóa tất cả các giá trị trong cache Redis thuộc namespace.
        
        Returns:
            True nếu thành công, False nếu thất bại
        """
        try:
            pattern = f"{self.namespace}:*"
            cursor = 0
            while True:
                cursor, keys = self.client.scan(cursor, pattern, 100)
                if keys:
                    self.client.delete(*keys)
                if cursor == 0:
                    break
            return True
        except Exception as e:
            logger.error(f"Lỗi khi xóa tất cả giá trị khỏi Redis cache: {str(e)}")
            return False
