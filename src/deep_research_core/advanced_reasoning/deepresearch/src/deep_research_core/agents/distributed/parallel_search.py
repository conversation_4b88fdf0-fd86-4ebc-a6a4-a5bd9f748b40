"""
Tì<PERSON> kiếm song song cho WebSearchAgent.

<PERSON><PERSON><PERSON> này cung cấp các lớp và hàm để thực hiện tìm kiếm song song,
giú<PERSON> cải thiện hiệu suất và khả năng mở rộng của WebSearchAgent.
"""

import time
import logging
import threading
import concurrent.futures
from typing import Dict, Any, List, Optional, Union, Tuple, Set, Callable

# Lấy logger
logger = logging.getLogger(__name__)

class ParallelSearchExecutor:
    """Thực thi tìm kiếm song song."""
    
    def __init__(self, **kwargs):
        """
        Khởi tạo ParallelSearchExecutor.
        
        Args:
            **kwargs: <PERSON>ham số bổ sung
        """
        self.max_workers = kwargs.get("max_workers", 5)
        self.timeout = kwargs.get("timeout", 30)
        self.use_threads = kwargs.get("use_threads", True)
    
    def execute(self, search_tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Thự<PERSON> thi các tác vụ tìm kiếm song song.
        
        Args:
            search_tasks: <PERSON>h sách các tác vụ tìm kiếm
                Mỗi tác vụ là một từ điển với các khóa:
                - "engine": Tên công cụ tìm kiếm
                - "query": Truy vấn tìm kiếm
                - "search_func": Hàm tìm kiếm
                - "kwargs": Tham số bổ sung cho hàm tìm kiếm
            
        Returns:
            Danh sách kết quả tìm kiếm
        """
        if self.use_threads:
            return self._execute_with_threads(search_tasks)
        else:
            return self._execute_with_processes(search_tasks)
    
    def _execute_with_threads(self, search_tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Thực thi các tác vụ tìm kiếm song song sử dụng threads.
        
        Args:
            search_tasks: Danh sách các tác vụ tìm kiếm
            
        Returns:
            Danh sách kết quả tìm kiếm
        """
        results = []
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Tạo future cho mỗi tác vụ
            future_to_task = {
                executor.submit(self._execute_search_task, task): task
                for task in search_tasks
            }
            
            # Xử lý kết quả khi hoàn thành
            for future in concurrent.futures.as_completed(future_to_task, timeout=self.timeout):
                task = future_to_task[future]
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    logger.error(f"Lỗi khi thực thi tìm kiếm với {task['engine']}: {str(e)}")
                    # Thêm kết quả lỗi
                    results.append({
                        "success": False,
                        "error": str(e),
                        "engine": task["engine"],
                        "query": task["query"],
                        "results": []
                    })
        
        return results
    
    def _execute_with_processes(self, search_tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Thực thi các tác vụ tìm kiếm song song sử dụng processes.
        
        Args:
            search_tasks: Danh sách các tác vụ tìm kiếm
            
        Returns:
            Danh sách kết quả tìm kiếm
        """
        results = []
        
        with concurrent.futures.ProcessPoolExecutor(max_workers=self.max_workers) as executor:
            # Tạo future cho mỗi tác vụ
            future_to_task = {}
            for task in search_tasks:
                # Lưu ý: Không thể truyền trực tiếp đối tượng không thể pickle
                # Vì vậy, chúng ta cần đảm bảo rằng search_func là một hàm có thể pickle
                if "search_func" in task and callable(task["search_func"]):
                    future = executor.submit(task["search_func"], task["query"], **task.get("kwargs", {}))
                    future_to_task[future] = task
            
            # Xử lý kết quả khi hoàn thành
            for future in concurrent.futures.as_completed(future_to_task, timeout=self.timeout):
                task = future_to_task[future]
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    logger.error(f"Lỗi khi thực thi tìm kiếm với {task['engine']}: {str(e)}")
                    # Thêm kết quả lỗi
                    results.append({
                        "success": False,
                        "error": str(e),
                        "engine": task["engine"],
                        "query": task["query"],
                        "results": []
                    })
        
        return results
    
    def _execute_search_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        Thực thi một tác vụ tìm kiếm.
        
        Args:
            task: Tác vụ tìm kiếm
            
        Returns:
            Kết quả tìm kiếm
        """
        try:
            search_func = task["search_func"]
            query = task["query"]
            kwargs = task.get("kwargs", {})
            
            # Thực thi tìm kiếm
            result = search_func(query, **kwargs)
            
            # Đảm bảo kết quả có thông tin về công cụ tìm kiếm
            if isinstance(result, dict) and "engine" not in result:
                result["engine"] = task["engine"]
            
            return result
        except Exception as e:
            logger.error(f"Lỗi khi thực thi tìm kiếm: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "engine": task["engine"],
                "query": query,
                "results": []
            }


class MultiEngineSearchExecutor:
    """Thực thi tìm kiếm trên nhiều công cụ song song."""
    
    def __init__(self, engines: List[Any], **kwargs):
        """
        Khởi tạo MultiEngineSearchExecutor.
        
        Args:
            engines: Danh sách các công cụ tìm kiếm
            **kwargs: Tham số bổ sung
        """
        self.engines = engines
        self.executor = ParallelSearchExecutor(**kwargs)
    
    def search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Thực hiện tìm kiếm trên nhiều công cụ song song.
        
        Args:
            query: Truy vấn tìm kiếm
            **kwargs: Tham số bổ sung
            
        Returns:
            Kết quả tìm kiếm tổng hợp
        """
        # Tạo danh sách tác vụ tìm kiếm
        search_tasks = []
        for engine in self.engines:
            task = {
                "engine": engine.name,
                "query": query,
                "search_func": engine.search,
                "kwargs": kwargs
            }
            search_tasks.append(task)
        
        # Thực thi tìm kiếm song song
        results = self.executor.execute(search_tasks)
        
        # Tổng hợp kết quả
        combined_results = self._combine_results(results)
        
        return combined_results
    
    def _combine_results(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Tổng hợp kết quả từ nhiều công cụ tìm kiếm.
        
        Args:
            results: Danh sách kết quả từ các công cụ tìm kiếm
            
        Returns:
            Kết quả tìm kiếm tổng hợp
        """
        # Khởi tạo kết quả tổng hợp
        combined_results = {
            "success": True,
            "results": [],
            "engines": [],
            "errors": []
        }
        
        # Tổng hợp kết quả từ các công cụ
        for result in results:
            # Thêm tên công cụ
            if "engine" in result:
                combined_results["engines"].append(result["engine"])
            
            # Kiểm tra thành công
            if result.get("success", False):
                # Thêm kết quả
                if "results" in result and isinstance(result["results"], list):
                    combined_results["results"].extend(result["results"])
            else:
                # Thêm lỗi
                error_info = {
                    "engine": result.get("engine", "unknown"),
                    "error": result.get("error", "Unknown error")
                }
                combined_results["errors"].append(error_info)
        
        # Kiểm tra xem có kết quả nào không
        if not combined_results["results"]:
            combined_results["success"] = False
        
        return combined_results
