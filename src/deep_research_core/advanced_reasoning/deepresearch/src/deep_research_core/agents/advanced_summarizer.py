"""
Advanced summarizer module.

This module provides functions to generate high-quality summaries of content:
- Extract key information from text
- Generate abstractive summaries
- Support multiple languages
"""

import re
import string
import logging
import unicodedata
from typing import Dict, List, Any, Optional, Tuple, Set
from collections import Counter
import re

# Get logger
from ..utils.structured_logging import get_logger
logger = get_logger(__name__)

# Try to import multilingual utils
try:
    from ..utils.multilingual_utils import detect_language
    MULTILINGUAL_UTILS_AVAILABLE = True
except ImportError:
    MULTILINGUAL_UTILS_AVAILABLE = False
    logger.warning("Multilingual utils not available. Language detection will be limited.")

# Try to import reasoning models
try:
    from ..models.base_reasoning_model import BaseReasoningModel
    from ..models.gpt_o1 import GPTO1Model
    from ..models.deepseek_r1 import DeepseekR1Model
    from ..models.qwq_32b import QwQ32BModel
    REASONING_MODELS_AVAILABLE = True
except ImportError:
    REASONING_MODELS_AVAILABLE = False
    logger.warning("Reasoning models not available. Advanced summarization will be limited.")

class AdvancedSummarizer:
    """
    Advanced summarizer for content.

    This class provides methods for generating high-quality summaries of content
    using both extractive and abstractive techniques.
    """

    # Stopwords for different languages
    STOPWORDS = {
        "en": set(["a", "an", "the", "and", "or", "but", "if", "because", "as", "what",
                  "when", "where", "how", "who", "which", "this", "that", "these", "those",
                  "is", "are", "was", "were", "be", "been", "being", "have", "has", "had",
                  "do", "does", "did", "can", "could", "will", "would", "shall", "should",
                  "may", "might", "must", "of", "for", "with", "about", "against", "between",
                  "into", "through", "during", "before", "after", "above", "below", "to",
                  "from", "up", "down", "in", "out", "on", "off", "over", "under", "again",
                  "further", "then", "once", "here", "there", "all", "any", "both", "each",
                  "few", "more", "most", "other", "some", "such", "no", "nor", "not", "only",
                  "own", "same", "so", "than", "too", "very", "just", "now"]),
        "vi": {
            "và", "của", "các", "có", "được", "là", "trong", "đã", "cho", "không",
            "những", "để", "này", "với", "tại", "từ", "theo", "trên", "về", "như",
            "nhưng", "một", "còn", "đến", "khi", "làm", "ra", "thì", "sẽ", "nên"
        }
    }

    # Summarization prompts for different languages
    SUMMARIZATION_PROMPTS = {
        "en": "Please provide a concise summary of the following text in {max_length} words or less:\n\n{text}",
        "vi": "Vui lòng cung cấp bản tóm tắt ngắn gọn của văn bản sau trong tối đa {max_length} từ:\n\n{text}"
    }

    def __init__(
        self,
        model_name: Optional[str] = None,
        use_extractive: bool = True,
        use_abstractive: bool = True,
        **kwargs
    ):
        """
        Initialize the advanced summarizer.

        Args:
            model_name: Name of the reasoning model to use for abstractive summarization
            use_extractive: Whether to use extractive summarization
            use_abstractive: Whether to use abstractive summarization
            **kwargs: Additional arguments for the reasoning model
        """
        self.use_extractive = use_extractive
        self.use_abstractive = use_abstractive and REASONING_MODELS_AVAILABLE

        # Initialize reasoning model for abstractive summarization
        self.reasoning_model = None

        if self.use_abstractive and REASONING_MODELS_AVAILABLE:
            try:
                if model_name == "gpt-o1":
                    self.reasoning_model = GPTO1Model(**kwargs)
                elif model_name == "deepseek-r1":
                    self.reasoning_model = DeepseekR1Model(**kwargs)
                elif model_name == "qwq-32b":
                    self.reasoning_model = QwQ32BModel(**kwargs)
                else:
                    # Default to GPT-O1
                    self.reasoning_model = GPTO1Model(**kwargs)

                logger.info(f"Initialized advanced summarizer with model: {self.reasoning_model.__class__.__name__}")
            except Exception as e:
                logger.error(f"Error initializing reasoning model: {str(e)}")
                self.reasoning_model = None

    def _extract_key_sentences(
        self,
        text: str,
        language: str,
        max_sentences: int = 3
    ) -> List[str]:
        """
        Extract key sentences from text.

        Args:
            text: Text to extract sentences from
            language: Language of the text
            max_sentences: Maximum number of sentences to extract

        Returns:
            List of key sentences
        """
        if not text:
            return []

        # Split into sentences using simple regex to avoid NLTK issues
        sentences = re.split(r'[.!?]+', text)
        sentences = [s.strip() for s in sentences if s.strip()]

        if not sentences:
            return []

        # Always include the first sentence
        key_sentences = [sentences[0]]

        if len(sentences) <= max_sentences:
            return sentences

        # Score remaining sentences
        sentence_scores = []

        # Get stopwords for language
        lang_stopwords = self.STOPWORDS.get(language, self.STOPWORDS.get("en", set()))

        # Calculate word frequencies
        words = text.lower().split()
        word_freq = Counter([w for w in words if w not in lang_stopwords and w not in string.punctuation])

        # Score sentences based on word frequencies
        for i, sentence in enumerate(sentences[1:], 1):  # Skip first sentence
            words = sentence.lower().split()
            score = sum(word_freq[word] for word in words if word in word_freq)

            # Normalize by sentence length
            if len(words) > 0:
                score = score / len(words)

            # Penalize very short sentences
            if len(words) < 3:
                score *= 0.5

            # Bonus for sentences near the beginning
            position_bonus = 1.0 - (i / len(sentences))
            score += position_bonus

            sentence_scores.append((i, score))

        # Select top scoring sentences
        top_sentences = sorted(sentence_scores, key=lambda x: x[1], reverse=True)[:max_sentences-1]

        # Add top sentences in original order
        for i, _ in sorted(top_sentences, key=lambda x: x[0]):
            key_sentences.append(sentences[i])

        return key_sentences

    def _generate_abstractive_summary(
        self,
        text: str,
        language: str,
        max_length: int = 100
    ) -> str:
        """
        Generate abstractive summary using a reasoning model.

        Args:
            text: Text to summarize
            language: Language of the text
            max_length: Maximum length of summary in words

        Returns:
            Abstractive summary
        """
        if not self.reasoning_model:
            return ""

        # Get summarization prompt for language
        prompt_template = self.SUMMARIZATION_PROMPTS.get(language, self.SUMMARIZATION_PROMPTS["en"])

        # Create prompt
        prompt = prompt_template.format(text=text, max_length=max_length)

        # Generate summary
        try:
            response = self.reasoning_model.generate(prompt)
            summary = response.get("text", "").strip()
            return summary
        except Exception as e:
            logger.error(f"Error generating abstractive summary: {str(e)}")
            return ""

    def summarize(
        self,
        text: str,
        language: Optional[str] = None,
        max_length: int = 100,
        strategy: str = "hybrid"
    ) -> str:
        """
        Generate a summary of the text.

        Args:
            text: Text to summarize
            language: Language of the text
            max_length: Maximum length of summary in words
            strategy: Summarization strategy ("extractive", "abstractive", or "hybrid")

        Returns:
            Summary of the text
        """
        if not text:
            return ""

        # Detect language if not provided
        if language is None and MULTILINGUAL_UTILS_AVAILABLE:
            language = detect_language(text)
        else:
            language = "en"

        # Truncate very long texts
        if len(text) > 10000:
            text = text[:10000]

        # Choose summarization strategy
        if strategy == "extractive" or (strategy == "hybrid" and not self.reasoning_model):
            # Extractive summarization
            key_sentences = self._extract_key_sentences(text, language, max_sentences=max(1, max_length // 20))
            summary = " ".join(key_sentences)

            # Truncate if still too long
            words = summary.split()
            if len(words) > max_length:
                summary = " ".join(words[:max_length]) + "..."

            return summary

        elif strategy == "abstractive" and self.reasoning_model:
            # Abstractive summarization
            return self._generate_abstractive_summary(text, language, max_length)

        elif strategy == "hybrid" and self.reasoning_model:
            # Hybrid summarization: first extractive, then abstractive
            key_sentences = self._extract_key_sentences(text, language, max_sentences=5)
            extracted_summary = " ".join(key_sentences)

            # Use abstractive summarization on the extracted summary
            return self._generate_abstractive_summary(extracted_summary, language, max_length)

        else:
            # Fallback to simple extractive
            sentences = re.split(r'[.!?]+', text)
            sentences = [s.strip() for s in sentences if s.strip()]

            if not sentences:
                return ""

            # Take first few sentences
            summary = " ".join(sentences[:3])

            # Truncate if still too long
            words = summary.split()
            if len(words) > max_length:
                summary = " ".join(words[:max_length]) + "..."

            return summary

    def summarize_search_results(
        self,
        results: List[Dict[str, Any]],
        language: Optional[str] = None,
        max_length: int = 100,
        strategy: str = "hybrid"
    ) -> List[Dict[str, Any]]:
        """
        Generate summaries for search results.

        Args:
            results: List of search results
            language: Language of the results
            max_length: Maximum length of each summary in words
            strategy: Summarization strategy

        Returns:
            List of search results with summaries
        """
        if not results:
            return []

        # Process each result
        for result in results:
            # Get content
            content = result.get('content', '') or result.get('snippet', '')

            if content:
                # Generate summary
                summary = self.summarize(content, language, max_length, strategy)

                # Add summary to result
                result['summary'] = summary

        return results
