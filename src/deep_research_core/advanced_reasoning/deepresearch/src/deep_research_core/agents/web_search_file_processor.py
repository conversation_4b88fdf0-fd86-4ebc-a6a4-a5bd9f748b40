"""
Module xử lý file từ các URL cho WebSearchAgent.

Module này cung cấp các chức năng để tải xuống và xử lý file từ các URL,
hỗ trợ nhiều định dạng file như PDF, DOCX, PPTX, v.v.
"""

import os
import io
import time
import logging
import tempfile
import hashlib
import mimetypes
import requests
from typing import Dict, Any, List, Optional, Union, BinaryIO
from urllib.parse import urlparse, unquote

from ..utils.structured_logging import get_logger

# Thử import DocumentExtractor
try:
    from .document_extractors import DocumentExtractor
    DOCUMENT_EXTRACTOR_AVAILABLE = True
except ImportError:
    DOCUMENT_EXTRACTOR_AVAILABLE = False

# Thử import ExtendedDocumentExtractor
try:
    from .document_extractors_extended import ExtendedDocumentExtractor
    EXTENDED_DOCUMENT_EXTRACTOR_AVAILABLE = True
except ImportError:
    EXTENDED_DOCUMENT_EXTRACTOR_AVAILABLE = False

# Thử import Playwright
try:
    from playwright.sync_api import sync_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False

# Create a logger
logger = get_logger(__name__)

class WebSearchFileProcessor:
    """
    Xử lý file từ các URL cho WebSearchAgent.
    """

    def __init__(
        self,
        download_dir: Optional[str] = None,
        max_file_size: int = 10 * 1024 * 1024,  # 10MB
        timeout: int = 30,
        user_agent: Optional[str] = None,
        supported_extensions: Optional[List[str]] = None
    ):
        """
        Khởi tạo WebSearchFileProcessor.

        Args:
            download_dir: Thư mục để lưu các file tải xuống
            max_file_size: Kích thước file tối đa (bytes)
            timeout: Thời gian chờ tối đa (giây)
            user_agent: User-Agent để sử dụng
            supported_extensions: Danh sách các phần mở rộng file được hỗ trợ
        """
        self.download_dir = download_dir or tempfile.gettempdir()
        self.max_file_size = max_file_size
        self.timeout = timeout
        self.user_agent = user_agent or "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36"

        # Tạo thư mục tải xuống nếu chưa tồn tại
        if not os.path.exists(self.download_dir):
            os.makedirs(self.download_dir)

        # Khởi tạo DocumentExtractor nếu có
        if EXTENDED_DOCUMENT_EXTRACTOR_AVAILABLE:
            from .document_extractors_extended import extended_document_extractor
            self.document_extractor = extended_document_extractor
        elif DOCUMENT_EXTRACTOR_AVAILABLE:
            self.document_extractor = DocumentExtractor()
        else:
            self.document_extractor = None

        # Danh sách các phần mở rộng file được hỗ trợ
        self.supported_extensions = supported_extensions or [
            # Tài liệu văn phòng
            '.pdf', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx',
            # Tài liệu văn bản
            '.txt', '.csv', '.json', '.xml', '.html', '.htm', '.md', '.markdown',
            # Tài liệu OpenDocument
            '.odt', '.ods', '.odp',
            # Sách điện tử
            '.epub', '.mobi',
            # Định dạng khác
            '.rtf', '.tex'
        ]

    def is_file_url(self, url: str) -> bool:
        """
        Kiểm tra xem URL có phải là liên kết đến file hay không.

        Args:
            url: URL cần kiểm tra

        Returns:
            bool: True nếu là file, False nếu không
        """
        # Kiểm tra phần mở rộng của URL
        parsed_url = urlparse(url)
        path = unquote(parsed_url.path)

        # Kiểm tra phần mở rộng
        for ext in self.supported_extensions:
            if path.lower().endswith(ext):
                return True

        # Kiểm tra tham số query có chứa file hay không
        if 'download' in parsed_url.query.lower() or 'file' in parsed_url.query.lower():
            return True

        # Kiểm tra các mẫu URL đặc biệt
        special_patterns = ['/download/', '/files/', '/documents/', '/attachments/']
        for pattern in special_patterns:
            if pattern in url.lower():
                return True

        return False

    def get_file_name_from_url(self, url: str) -> str:
        """
        Lấy tên file từ URL.

        Args:
            url: URL cần lấy tên file

        Returns:
            str: Tên file
        """
        parsed_url = urlparse(url)
        path = unquote(parsed_url.path)

        # Lấy tên file từ path
        file_name = os.path.basename(path)

        # Nếu không có tên file, tạo tên file từ URL
        if not file_name or '.' not in file_name:
            file_name = f"file_{hashlib.md5(url.encode()).hexdigest()}"

            # Thêm phần mở rộng dựa trên Content-Type nếu có
            try:
                response = requests.head(url, timeout=5)
                content_type = response.headers.get('Content-Type', '')
                ext = mimetypes.guess_extension(content_type)
                if ext:
                    file_name += ext
            except Exception:
                pass

        # Loại bỏ các ký tự không hợp lệ
        file_name = "".join(c for c in file_name if c.isalnum() or c in "._-")

        return file_name

    def download_file(
        self,
        url: str,
        output_path: Optional[str] = None,
        use_playwright: bool = False
    ) -> Dict[str, Any]:
        """
        Tải xuống file từ URL.

        Args:
            url: URL của file
            output_path: Đường dẫn để lưu file
            use_playwright: Có sử dụng Playwright hay không

        Returns:
            Dict[str, Any]: Thông tin về file đã tải xuống
        """
        # Tạo đường dẫn output nếu không được cung cấp
        if not output_path:
            file_name = self.get_file_name_from_url(url)
            output_path = os.path.join(self.download_dir, file_name)

        # Khởi tạo kết quả
        result = {
            "success": False,
            "url": url,
            "file_path": output_path,
            "file_name": os.path.basename(output_path),
            "content_type": None,
            "size": 0,
            "text_content": None,
            "download_time": time.time()
        }

        try:
            # Sử dụng Playwright nếu được yêu cầu và có sẵn
            if use_playwright and PLAYWRIGHT_AVAILABLE:
                return self._download_with_playwright(url, output_path)

            # Sử dụng requests
            headers = {
                'User-Agent': self.user_agent
            }

            # Tải xuống file
            response = requests.get(
                url,
                headers=headers,
                stream=True,
                timeout=self.timeout
            )

            # Kiểm tra trạng thái
            if response.status_code != 200:
                logger.warning(f"Lỗi khi tải xuống file từ {url}: HTTP {response.status_code}")
                result["error"] = f"HTTP error: {response.status_code}"
                return result

            # Lấy Content-Type
            content_type = response.headers.get('Content-Type', '')
            result["content_type"] = content_type

            # Lưu file
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            # Cập nhật kích thước
            result["size"] = os.path.getsize(output_path)

            # Trích xuất nội dung văn bản nếu có DocumentExtractor
            if self.document_extractor:
                with open(output_path, 'rb') as f:
                    extraction_result = self.document_extractor.extract(
                        f,
                        mime_type=content_type,
                        filename=result["file_name"]
                    )

                    if extraction_result.get("success"):
                        result["text_content"] = extraction_result.get("text", "")

            result["success"] = True
            logger.info(f"Đã tải xuống file: {result['file_name']} ({result['size']} bytes)")

            return result

        except Exception as e:
            logger.warning(f"Lỗi khi tải xuống file từ {url}: {str(e)}")
            result["error"] = str(e)
            return result

    def _download_with_playwright(
        self,
        url: str,
        output_path: str
    ) -> Dict[str, Any]:
        """
        Tải xuống file bằng Playwright.

        Args:
            url: URL của file
            output_path: Đường dẫn để lưu file

        Returns:
            Dict[str, Any]: Thông tin về file đã tải xuống
        """
        # Khởi tạo kết quả
        result = {
            "success": False,
            "url": url,
            "file_path": output_path,
            "file_name": os.path.basename(output_path),
            "content_type": None,
            "size": 0,
            "text_content": None,
            "download_time": time.time()
        }

        try:
            with sync_playwright() as p:
                browser = p.chromium.launch(headless=True)
                context = browser.new_context(
                    accept_downloads=True,
                    user_agent=self.user_agent
                )

                page = context.new_page()

                # Thiết lập xử lý tải xuống
                download_promise = page.wait_for_download()

                # Điều hướng đến URL
                page.goto(url, timeout=self.timeout * 1000)

                # Chờ tải xuống
                download = download_promise

                # Lưu file
                download.save_as(output_path)

                # Lấy thông tin
                result["content_type"] = download.suggested_filename.split('.')[-1]
                result["size"] = os.path.getsize(output_path)

                # Đóng trình duyệt
                browser.close()

                # Trích xuất nội dung văn bản nếu có DocumentExtractor
                if self.document_extractor:
                    with open(output_path, 'rb') as f:
                        extraction_result = self.document_extractor.extract(
                            f,
                            filename=result["file_name"]
                        )

                        if extraction_result.get("success"):
                            result["text_content"] = extraction_result.get("text", "")

                result["success"] = True
                logger.info(f"Đã tải xuống file với Playwright: {result['file_name']} ({result['size']} bytes)")

                return result

        except Exception as e:
            logger.warning(f"Lỗi khi tải xuống file với Playwright từ {url}: {str(e)}")
            result["error"] = str(e)
            return result
