#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module cung cấp các lớp và phương thức để phân tích ngữ nghĩa nâng cao.

Module này cung cấp các lớp và phương thức để phân tích ngữ nghĩa của nội dung web,
trích xuất thông tin, phân loại nội dung, phân tích mối quan hệ giữa các thực thể,
phân tích tình cảm và quan điểm, phân tích chủ đề và phân loại nội dung.
"""

import re
import time
import logging
import json
from typing import Dict, List, Any, Optional, Set, Tuple, Union
from collections import Counter, defaultdict

from ..utils.structured_logging import get_logger

# Thiết lập logging
logger = get_logger(__name__)


class SemanticAnalyzer:
    """
    Lớp cung cấp các phư<PERSON><PERSON> thức để phân tích ngữ nghĩa nâng cao.

    Tính năng:
    - Phân tích ngữ nghĩa của nội dung web
    - Trích xuất thông tin từ nội dung web
    - Phân loại nội dung web
    - Phân tích mối quan hệ giữa các thực thể
    - Phân tích tình cảm và quan điểm
    - Phân tích chủ đề và phân loại nội dung
    - Hỗ trợ nhiều ngôn ngữ (tiếng Anh, tiếng Việt)
    """

    def __init__(
        self,
        language: str = "auto",
        use_nlp: bool = True,
        use_entity_recognition: bool = True,
        use_sentiment_analysis: bool = True,
        use_topic_analysis: bool = True,
        use_relation_extraction: bool = True,
        use_classification: bool = True,
        use_summarization: bool = True,
        use_keyword_extraction: bool = True,
        use_question_answering: bool = True,
        use_vietnamese_nlp: bool = True,
        use_english_nlp: bool = True,
        cache_enabled: bool = True,
        cache_ttl: int = 3600,
        cache_size: int = 1000,
        verbose: bool = False,
    ):
        """
        Khởi tạo SemanticAnalyzer.

        Args:
            language: Ngôn ngữ mặc định ("auto", "en", "vi")
            use_nlp: Sử dụng NLP hay không
            use_entity_recognition: Sử dụng nhận dạng thực thể hay không
            use_sentiment_analysis: Sử dụng phân tích tình cảm hay không
            use_topic_analysis: Sử dụng phân tích chủ đề hay không
            use_relation_extraction: Sử dụng trích xuất mối quan hệ hay không
            use_classification: Sử dụng phân loại hay không
            use_summarization: Sử dụng tóm tắt hay không
            use_keyword_extraction: Sử dụng trích xuất từ khóa hay không
            use_question_answering: Sử dụng trả lời câu hỏi hay không
            use_vietnamese_nlp: Sử dụng NLP tiếng Việt hay không
            use_english_nlp: Sử dụng NLP tiếng Anh hay không
            cache_enabled: Bật cache hay không
            cache_ttl: Thời gian sống của cache (giây)
            cache_size: Kích thước tối đa của cache
            verbose: Ghi log chi tiết hay không
        """
        # Cấu hình analyzer
        self.language = language
        self.use_nlp = use_nlp
        self.use_entity_recognition = use_entity_recognition
        self.use_sentiment_analysis = use_sentiment_analysis
        self.use_topic_analysis = use_topic_analysis
        self.use_relation_extraction = use_relation_extraction
        self.use_classification = use_classification
        self.use_summarization = use_summarization
        self.use_keyword_extraction = use_keyword_extraction
        self.use_question_answering = use_question_answering
        self.use_vietnamese_nlp = use_vietnamese_nlp
        self.use_english_nlp = use_english_nlp
        self.cache_enabled = cache_enabled
        self.cache_ttl = cache_ttl
        self.cache_size = cache_size
        self.verbose = verbose

        # Khởi tạo các thuộc tính khác
        self.content_cache = {}
        self.language_cache = {}
        self.entity_cache = {}
        self.sentiment_cache = {}
        self.topic_cache = {}
        self.relation_cache = {}
        self.classification_cache = {}
        self.summarization_cache = {}
        self.keyword_cache = {}
        self.qa_cache = {}
        self.stats = {
            "total_analyzed": 0,
            "successful_analyses": 0,
            "failed_analyses": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "errors": {},
        }

        # Khởi tạo các bộ phân tích
        self._initialize_analyzers()

    def _initialize_analyzers(self):
        """
        Khởi tạo các bộ phân tích.
        """
        # Khởi tạo bộ phát hiện ngôn ngữ
        self.language_detector = None
        if self.language == "auto":
            try:
                from ..multilingual import LanguageDetector

                self.language_detector = LanguageDetector()
                logger.info("Language detector initialized")
            except ImportError:
                logger.warning("Language detector not available")

        # Khởi tạo bộ phân tích NLP tiếng Việt
        self.vietnamese_nlp = None
        if self.use_vietnamese_nlp:
            try:
                from ..multilingual import VietnameseNLP

                self.vietnamese_nlp = VietnameseNLP()
                logger.info("Vietnamese NLP initialized")
            except ImportError:
                logger.warning("Vietnamese NLP not available")

        # Khởi tạo bộ phân tích NLP tiếng Anh
        self.english_nlp = None
        if self.use_english_nlp:
            try:
                # Thử sử dụng spaCy
                try:
                    import spacy

                    self.english_nlp = spacy.load("en_core_web_sm")
                    logger.info("English NLP (spaCy) initialized")
                except (ImportError, OSError):
                    # Thử sử dụng NLTK
                    try:
                        import nltk

                        nltk.download("punkt", quiet=True)
                        nltk.download("stopwords", quiet=True)
                        nltk.download("wordnet", quiet=True)
                        nltk.download("averaged_perceptron_tagger", quiet=True)
                        nltk.download("maxent_ne_chunker", quiet=True)
                        nltk.download("words", quiet=True)
                        from nltk.tokenize import word_tokenize, sent_tokenize
                        from nltk.corpus import stopwords
                        from nltk.stem import WordNetLemmatizer

                        self.english_nlp = {
                            "tokenize": word_tokenize,
                            "sent_tokenize": sent_tokenize,
                            "stopwords": set(stopwords.words("english")),
                            "lemmatizer": WordNetLemmatizer(),
                        }
                        logger.info("English NLP (NLTK) initialized")
                    except ImportError:
                        logger.warning("English NLP not available")
            except Exception as e:
                logger.warning(f"Error initializing English NLP: {str(e)}")

        # Khởi tạo bộ phân tích thực thể
        self.entity_recognizer = None
        if self.use_entity_recognition:
            try:
                from ..analyzers.entity_recognizer import EntityRecognizer

                self.entity_recognizer = EntityRecognizer()
                logger.info("Entity recognizer initialized")
            except ImportError:
                logger.warning("Entity recognizer not available")

        # Khởi tạo bộ phân tích tình cảm
        self.sentiment_analyzer = None
        if self.use_sentiment_analysis:
            try:
                from ..analyzers.sentiment_analyzer import SentimentAnalyzer

                self.sentiment_analyzer = SentimentAnalyzer()
                logger.info("Sentiment analyzer initialized")
            except ImportError:
                logger.warning("Sentiment analyzer not available")

        # Khởi tạo bộ phân tích chủ đề
        self.topic_analyzer = None
        if self.use_topic_analysis:
            try:
                from ..analyzers.topic_analyzer import TopicAnalyzer

                self.topic_analyzer = TopicAnalyzer()
                logger.info("Topic analyzer initialized")
            except ImportError:
                logger.warning("Topic analyzer not available")

        # Khởi tạo bộ trích xuất mối quan hệ
        self.relation_extractor = None
        if self.use_relation_extraction:
            try:
                from ..analyzers.relation_extractor import RelationExtractor

                self.relation_extractor = RelationExtractor()
                logger.info("Relation extractor initialized")
            except ImportError:
                logger.warning("Relation extractor not available")

        # Khởi tạo bộ phân loại
        self.classifier = None
        if self.use_classification:
            try:
                from ..analyzers.content_classifier import ContentClassifier

                self.classifier = ContentClassifier()
                logger.info("Classifier initialized")
            except ImportError:
                logger.warning("Classifier not available")

        # Khởi tạo bộ tóm tắt
        self.summarizer = None
        if self.use_summarization:
            try:
                from ..analyzers.content_summarizer import ContentSummarizer

                self.summarizer = ContentSummarizer()
                logger.info("Summarizer initialized")
            except ImportError:
                logger.warning("Summarizer not available")

        # Khởi tạo bộ trích xuất từ khóa
        self.keyword_extractor = None
        if self.use_keyword_extraction:
            try:
                from ..analyzers.keyword_extractor import KeywordExtractor

                self.keyword_extractor = KeywordExtractor()
                logger.info("Keyword extractor initialized")
            except ImportError:
                logger.warning("Keyword extractor not available")

        # Khởi tạo bộ trả lời câu hỏi
        self.question_answerer = None
        if self.use_question_answering:
            try:
                from ..analyzers.question_answerer import QuestionAnswerer

                self.question_answerer = QuestionAnswerer()
                logger.info("Question answerer initialized")
            except ImportError:
                logger.warning("Question answerer not available")

    def analyze_content(self, content: str, **kwargs) -> Dict[str, Any]:
        """
        Phân tích nội dung.

        Args:
            content: Nội dung cần phân tích
            **kwargs: Các tham số bổ sung

        Returns:
            Dict[str, Any]: Kết quả phân tích
        """
        # Kiểm tra cache
        if self.cache_enabled:
            cache_key = self._get_cache_key(content)
            cached_result = self._get_from_cache(cache_key, "content")
            if cached_result:
                self.stats["cache_hits"] += 1
                return cached_result
            self.stats["cache_misses"] += 1

        # Thực hiện phân tích
        start_time = time.time()
        result = self._analyze_content(content, **kwargs)
        end_time = time.time()

        # Cập nhật thống kê
        self.stats["total_analyzed"] += 1

        if result.get("success", False):
            self.stats["successful_analyses"] += 1
        else:
            self.stats["failed_analyses"] += 1
            error_type = result.get("error_type", "unknown")
            self.stats["errors"][error_type] = (
                self.stats["errors"].get(error_type, 0) + 1
            )

        result["analysis_time"] = end_time - start_time

        # Lưu vào cache
        if self.cache_enabled and result.get("success", False):
            cache_key = self._get_cache_key(content)
            self._add_to_cache(cache_key, result, "content")

        return result

    def _analyze_content(self, content: str, **kwargs) -> Dict[str, Any]:
        """
        Thực hiện phân tích nội dung.

        Args:
            content: Nội dung cần phân tích
            **kwargs: Các tham số bổ sung

        Returns:
            Dict[str, Any]: Kết quả phân tích
        """
        # Khởi tạo kết quả
        result = {"success": False, "content": content, "timestamp": time.time()}

        try:
            # Phát hiện ngôn ngữ
            language = kwargs.get("language", self.language)
            if language == "auto" and self.language_detector:
                language = self.language_detector.detect_language(content)

            # Phân tích nội dung dựa trên ngôn ngữ
            if language == "vi" and self.vietnamese_nlp:
                analysis = self._analyze_vietnamese_content(content, **kwargs)
            elif language == "en" and self.english_nlp:
                analysis = self._analyze_english_content(content, **kwargs)
            else:
                # Sử dụng phân tích mặc định
                analysis = self._analyze_default_content(content, **kwargs)

            # Trích xuất thực thể
            entities = {}
            if self.use_entity_recognition and self.entity_recognizer:
                entities = self.entity_recognizer.extract_entities(content, language)

            # Phân tích tình cảm
            sentiment = {}
            if self.use_sentiment_analysis and self.sentiment_analyzer:
                sentiment = self.sentiment_analyzer.analyze_sentiment(content, language)

            # Phân tích chủ đề
            topics = {}
            if self.use_topic_analysis and self.topic_analyzer:
                topics = self.topic_analyzer.analyze_topics(content, language)

            # Trích xuất mối quan hệ
            relations = {}
            if self.use_relation_extraction and self.relation_extractor:
                relations = self.relation_extractor.extract_relations(content, language)

            # Phân loại nội dung
            classification = {}
            if self.use_classification and self.classifier:
                classification = self.classifier.classify_content(content, language)

            # Tóm tắt nội dung
            summary = ""
            if self.use_summarization and self.summarizer:
                summary = self.summarizer.summarize_content(content, language)

            # Trích xuất từ khóa
            keywords = []
            if self.use_keyword_extraction and self.keyword_extractor:
                keywords = self.keyword_extractor.extract_keywords(content, language)

            # Cập nhật kết quả
            result.update(
                {
                    "success": True,
                    "language": language,
                    "analysis": analysis,
                    "entities": entities,
                    "sentiment": sentiment,
                    "topics": topics,
                    "relations": relations,
                    "classification": classification,
                    "summary": summary,
                    "keywords": keywords,
                }
            )

            return result
        except Exception as e:
            # Xử lý lỗi
            result["error"] = str(e)
            result["error_type"] = type(e).__name__
            return result

    def _analyze_vietnamese_content(self, content: str, **kwargs) -> Dict[str, Any]:
        """
        Phân tích nội dung tiếng Việt.

        Args:
            content: Nội dung cần phân tích
            **kwargs: Các tham số bổ sung

        Returns:
            Dict[str, Any]: Kết quả phân tích
        """
        if not self.vietnamese_nlp:
            return self._analyze_default_content(content, **kwargs)

        # Phân tích nội dung tiếng Việt
        try:
            # Tokenize
            tokens = self.vietnamese_nlp.tokenize(content)

            # POS tagging
            pos_tags = self.vietnamese_nlp.pos_tag(content)

            # Named entity recognition
            entities = self.vietnamese_nlp.ner(content)

            # Dependency parsing
            dependencies = self.vietnamese_nlp.dependency_parse(content)

            # Sentence segmentation
            sentences = self.vietnamese_nlp.segment_sentences(content)

            # Word segmentation
            words = self.vietnamese_nlp.segment_words(content)

            # Stopwords removal
            filtered_words = self.vietnamese_nlp.remove_stopwords(words)

            # Stemming
            stemmed_words = self.vietnamese_nlp.stem(filtered_words)

            # Lemmatization
            lemmatized_words = self.vietnamese_nlp.lemmatize(filtered_words)

            # Cập nhật kết quả
            return {
                "tokens": tokens,
                "pos_tags": pos_tags,
                "entities": entities,
                "dependencies": dependencies,
                "sentences": sentences,
                "words": words,
                "filtered_words": filtered_words,
                "stemmed_words": stemmed_words,
                "lemmatized_words": lemmatized_words,
            }
        except Exception as e:
            logger.warning(f"Error analyzing Vietnamese content: {str(e)}")
            return self._analyze_default_content(content, **kwargs)

    def _analyze_english_content(self, content: str, **kwargs) -> Dict[str, Any]:
        """
        Phân tích nội dung tiếng Anh.

        Args:
            content: Nội dung cần phân tích
            **kwargs: Các tham số bổ sung

        Returns:
            Dict[str, Any]: Kết quả phân tích
        """
        if not self.english_nlp:
            return self._analyze_default_content(content, **kwargs)

        # Phân tích nội dung tiếng Anh
        try:
            # Kiểm tra loại bộ phân tích
            if isinstance(self.english_nlp, dict):
                # Sử dụng NLTK

                # Tokenize
                tokens = self.english_nlp["tokenize"](content)

                # Sentence segmentation
                sentences = self.english_nlp["sent_tokenize"](content)

                # Stopwords removal
                filtered_words = [
                    word
                    for word in tokens
                    if word.lower() not in self.english_nlp["stopwords"]
                ]

                # Lemmatization
                lemmatized_words = [
                    self.english_nlp["lemmatizer"].lemmatize(word)
                    for word in filtered_words
                ]

                # POS tagging
                pos_tags = []
                try:
                    import nltk

                    pos_tags = nltk.pos_tag(tokens)
                except (ImportError, AttributeError):
                    pass

                # Named entity recognition
                entities = []
                try:
                    import nltk

                    chunks = nltk.ne_chunk(pos_tags)
                    for chunk in chunks:
                        if hasattr(chunk, "label"):
                            entity = " ".join(c[0] for c in chunk)
                            entity_type = chunk.label()
                            entities.append((entity, entity_type))
                except (ImportError, AttributeError, Exception):
                    pass

                # Cập nhật kết quả
                return {
                    "tokens": tokens,
                    "sentences": sentences,
                    "filtered_words": filtered_words,
                    "lemmatized_words": lemmatized_words,
                    "pos_tags": pos_tags,
                    "entities": entities,
                }
            else:
                # Sử dụng spaCy
                doc = self.english_nlp(content)

                # Tokenize
                tokens = [token.text for token in doc]

                # Sentence segmentation
                sentences = [sent.text for sent in doc.sents]

                # POS tagging
                pos_tags = [(token.text, token.pos_) for token in doc]

                # Named entity recognition
                entities = [(ent.text, ent.label_) for ent in doc.ents]

                # Dependency parsing
                dependencies = [
                    (token.text, token.dep_, token.head.text) for token in doc
                ]

                # Stopwords removal
                filtered_words = [token.text for token in doc if not token.is_stop]

                # Lemmatization
                lemmatized_words = [token.lemma_ for token in doc]

                # Cập nhật kết quả
                return {
                    "tokens": tokens,
                    "sentences": sentences,
                    "pos_tags": pos_tags,
                    "entities": entities,
                    "dependencies": dependencies,
                    "filtered_words": filtered_words,
                    "lemmatized_words": lemmatized_words,
                }
        except Exception as e:
            logger.warning(f"Error analyzing English content: {str(e)}")
            return self._analyze_default_content(content, **kwargs)

    def _analyze_default_content(self, content: str, **_) -> Dict[str, Any]:
        """
        Phân tích nội dung mặc định.

        Args:
            content: Nội dung cần phân tích
            **kwargs: Các tham số bổ sung

        Returns:
            Dict[str, Any]: Kết quả phân tích
        """
        # Phân tích nội dung mặc định

        # Tokenize
        tokens = content.split()

        # Sentence segmentation
        sentences = re.split(r"(?<=[.!?])\s+", content)

        # Word count
        word_count = len(tokens)

        # Sentence count
        sentence_count = len(sentences)

        # Character count
        char_count = len(content)

        # Average word length
        avg_word_length = (
            sum(len(word) for word in tokens) / word_count if word_count > 0 else 0
        )

        # Average sentence length
        avg_sentence_length = word_count / sentence_count if sentence_count > 0 else 0

        # Frequency distribution
        freq_dist = Counter(tokens)

        # Top words
        top_words = freq_dist.most_common(10)

        # Cập nhật kết quả
        return {
            "tokens": tokens,
            "sentences": sentences,
            "word_count": word_count,
            "sentence_count": sentence_count,
            "char_count": char_count,
            "avg_word_length": avg_word_length,
            "avg_sentence_length": avg_sentence_length,
            "freq_dist": dict(freq_dist),
            "top_words": top_words,
        }

    def _get_cache_key(self, content: str) -> str:
        """
        Tạo khóa cache cho nội dung.

        Args:
            content: Nội dung cần tạo khóa cache

        Returns:
            str: Khóa cache
        """
        # Sử dụng MD5 để tạo khóa cache
        import hashlib

        return hashlib.md5(content.encode()).hexdigest()

    def _get_from_cache(self, key: str, cache_type: str) -> Optional[Dict[str, Any]]:
        """
        Lấy kết quả từ cache.

        Args:
            key: Khóa cache
            cache_type: Loại cache

        Returns:
            Optional[Dict[str, Any]]: Kết quả từ cache hoặc None nếu không tìm thấy
        """
        if not self.cache_enabled:
            return None

        # Xác định cache cần sử dụng
        cache = None
        if cache_type == "content":
            cache = self.content_cache
        elif cache_type == "entity":
            cache = self.entity_cache
        elif cache_type == "sentiment":
            cache = self.sentiment_cache
        elif cache_type == "topic":
            cache = self.topic_cache
        elif cache_type == "relation":
            cache = self.relation_cache
        elif cache_type == "classification":
            cache = self.classification_cache
        elif cache_type == "summarization":
            cache = self.summarization_cache
        elif cache_type == "keyword":
            cache = self.keyword_cache
        elif cache_type == "qa":
            cache = self.qa_cache
        else:
            return None

        # Kiểm tra cache
        if key in cache:
            cache_entry = cache[key]

            # Kiểm tra thời gian sống
            if time.time() - cache_entry["timestamp"] < self.cache_ttl:
                return cache_entry["data"]

            # Xóa cache hết hạn
            del cache[key]

        return None

    def _add_to_cache(self, key: str, data: Dict[str, Any], cache_type: str) -> None:
        """
        Thêm kết quả vào cache.

        Args:
            key: Khóa cache
            data: Dữ liệu cần lưu vào cache
            cache_type: Loại cache
        """
        if not self.cache_enabled:
            return

        # Xác định cache cần sử dụng
        cache = None
        if cache_type == "content":
            cache = self.content_cache
        elif cache_type == "entity":
            cache = self.entity_cache
        elif cache_type == "sentiment":
            cache = self.sentiment_cache
        elif cache_type == "topic":
            cache = self.topic_cache
        elif cache_type == "relation":
            cache = self.relation_cache
        elif cache_type == "classification":
            cache = self.classification_cache
        elif cache_type == "summarization":
            cache = self.summarization_cache
        elif cache_type == "keyword":
            cache = self.keyword_cache
        elif cache_type == "qa":
            cache = self.qa_cache
        else:
            return

        # Thêm vào cache
        cache[key] = {"timestamp": time.time(), "data": data}

        # Kiểm tra kích thước cache
        if len(cache) > self.cache_size:
            # Xóa mục cũ nhất
            oldest_key = min(cache.keys(), key=lambda k: cache[k]["timestamp"])
            del cache[oldest_key]
