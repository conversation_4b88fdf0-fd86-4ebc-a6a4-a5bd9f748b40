"""
Mistral AI provider for Deep Research Core.

This module provides integration with Mistral AI's API.
"""

import os
import json
import time
import logging
from typing import Dict, List, Any, Optional, Union

import mistralai
from mistralai.client import MistralClient
from mistralai.models.chat_completion import ChatMessage

from deep_research_core.utils.structured_logging import get_logger
from deep_research_core.providers.base_provider import BaseProvider

logger = get_logger(__name__)


class MistralProvider(BaseProvider):
    """
    Provider for Mistral AI's API.
    
    This class provides methods for interacting with Mistral AI's models.
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        api_base: Optional[str] = None,
        model: str = "mistral-medium",
        **kwargs
    ):
        """
        Initialize the Mistral AI provider.
        
        Args:
            api_key: Mistral AI API key
            api_base: Mistral AI API base URL (optional)
            model: Model name (default: "mistral-medium")
            **kwargs: Additional arguments
        """
        super().__init__()
        
        # Set API key
        self.api_key = api_key or os.environ.get("MISTRAL_API_KEY")
        if not self.api_key:
            raise ValueError("Mistral AI API key is required")
        
        # Set model
        self.model = model
        
        # Set API base URL
        self.api_base = api_base
        
        # Initialize the Mistral AI client
        self.client = MistralClient(api_key=self.api_key, endpoint=self.api_base)
        
        # Store additional arguments
        self.kwargs = kwargs
    
    def generate(
        self,
        prompt: str,
        max_tokens: int = 1024,
        temperature: float = 0.7,
        top_p: float = 0.95,
        stop: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate text using the Mistral AI API.
        
        Args:
            prompt: Input prompt
            max_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature
            top_p: Top-p sampling parameter
            stop: List of stop sequences
            **kwargs: Additional arguments
            
        Returns:
            Dictionary containing the generated text and metadata
        """
        try:
            # Create messages
            messages = [
                ChatMessage(role="user", content=prompt)
            ]
            
            # Generate text
            response = self.client.chat(
                model=self.model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=top_p,
                stop=stop
            )
            
            # Extract text from response
            text = response.choices[0].message.content
            
            # Return formatted response
            return {
                "choices": [
                    {
                        "text": text,
                        "finish_reason": response.choices[0].finish_reason
                    }
                ],
                "usage": {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                },
                "model": self.model,
                "provider": "mistral"
            }
            
        except Exception as e:
            logger.error(f"Error generating text with Mistral AI: {str(e)}")
            raise
    
    def generate_chat(
        self,
        messages: List[Dict[str, str]],
        max_tokens: int = 1024,
        temperature: float = 0.7,
        top_p: float = 0.95,
        stop: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate chat response using the Mistral AI API.
        
        Args:
            messages: List of message dictionaries with "role" and "content" keys
            max_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature
            top_p: Top-p sampling parameter
            stop: List of stop sequences
            **kwargs: Additional arguments
            
        Returns:
            Dictionary containing the generated text and metadata
        """
        try:
            # Convert messages to Mistral AI format
            mistral_messages = []
            
            for message in messages:
                role = message["role"]
                content = message["content"]
                
                if role == "system":
                    mistral_messages.append(ChatMessage(role="system", content=content))
                elif role == "user":
                    mistral_messages.append(ChatMessage(role="user", content=content))
                elif role == "assistant":
                    mistral_messages.append(ChatMessage(role="assistant", content=content))
            
            # Generate chat response
            response = self.client.chat(
                model=self.model,
                messages=mistral_messages,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=top_p,
                stop=stop
            )
            
            # Extract text from response
            text = response.choices[0].message.content
            
            # Return formatted response
            return {
                "choices": [
                    {
                        "text": text,
                        "finish_reason": response.choices[0].finish_reason
                    }
                ],
                "usage": {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                },
                "model": self.model,
                "provider": "mistral"
            }
            
        except Exception as e:
            logger.error(f"Error generating chat with Mistral AI: {str(e)}")
            raise
    
    def embed(
        self,
        text: Union[str, List[str]],
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate embeddings using the Mistral AI API.
        
        Args:
            text: Input text or list of texts
            **kwargs: Additional arguments
            
        Returns:
            Dictionary containing the embeddings and metadata
        """
        try:
            # Convert single text to list
            if isinstance(text, str):
                text = [text]
            
            # Generate embeddings
            response = self.client.embeddings(
                model="mistral-embed",  # Use the appropriate embedding model
                input=text
            )
            
            # Extract embeddings from response
            embeddings = [data.embedding for data in response.data]
            
            # Return formatted response
            return {
                "embeddings": embeddings,
                "model": "mistral-embed",
                "provider": "mistral"
            }
            
        except Exception as e:
            logger.error(f"Error generating embeddings with Mistral AI: {str(e)}")
            raise
    
    def get_token_count(self, text: str) -> int:
        """
        Get the number of tokens in the text.
        
        Args:
            text: Input text
            
        Returns:
            Number of tokens
        """
        try:
            # Use the tokenize endpoint to get token count
            response = self.client.count_tokens(
                model=self.model,
                content=text
            )
            return response.usage.total_tokens
        except Exception as e:
            logger.error(f"Error counting tokens with Mistral AI: {str(e)}")
            # Fallback to a simple approximation: 1 token ≈ 4 characters
            return len(text) // 4
