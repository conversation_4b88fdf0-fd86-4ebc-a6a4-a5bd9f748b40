"""
OpenRouter API provider.

This module provides a client for the OpenRouter API.
"""

import os
import json
import time
import requests
from typing import Dict, List, Any, Optional, Union, Tuple, Callable

from deep_research_core.utils.structured_logging import get_logger
from deep_research_core.providers.base_provider import BaseProvider

logger = get_logger(__name__)


class OpenRouterProvider(BaseProvider):
    """
    Client for the OpenRouter API.

    This class provides methods for interacting with the OpenRouter API,
    which provides access to various LLM providers through a unified interface.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        api_base: Optional[str] = None,
        model: str = "moonshotai/moonlight-16b-a3b-instruct:free",
        timeout: int = 60,
        max_retries: int = 3,
        retry_delay: int = 2,
        **kwargs
    ):
        """
        Initialize the OpenRouter API client.

        Args:
            api_key: OpenRouter API key (defaults to OPENROUTER_API_KEY environment variable)
            api_base: OpenRouter API base URL
            model: Default model to use
            timeout: Request timeout in seconds
            max_retries: Maximum number of retries for failed requests
            retry_delay: Delay between retries in seconds
            **kwargs: Additional parameters
        """
        self.api_key = api_key or os.environ.get("OPENROUTER_API_KEY")
        if not self.api_key:
            logger.warning("OpenRouter API key not found. Set OPENROUTER_API_KEY environment variable.")

        self.api_base = api_base or "https://openrouter.ai/api/v1"
        self.model = model
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay

        # Set up session
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://deep-research-core.ai",  # Replace with your actual domain
            "X-Title": "Deep Research Core"  # Replace with your actual app name
        })

    def generate(
        self,
        prompt: str,
        system_message: Optional[str] = None,
        max_tokens: int = 1024,
        temperature: float = 0.7,
        top_p: float = 1.0,
        n: int = 1,
        stop: Optional[Union[str, List[str]]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate text using the OpenRouter API.

        Args:
            prompt: Input prompt text
            system_message: System message for chat models
            max_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature
            top_p: Nucleus sampling parameter
            n: Number of completions to generate
            stop: Stop sequences
            **kwargs: Additional parameters

        Returns:
            API response
        """
        if not self.api_key:
            raise ValueError("OpenRouter API key is required")

        # Prepare request data
        messages = []

        if system_message:
            messages.append({"role": "system", "content": system_message})

        messages.append({"role": "user", "content": prompt})

        data = {
            "model": self.model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "top_p": top_p,
            "n": n
        }

        if stop:
            data["stop"] = stop

        # Add any additional parameters
        for key, value in kwargs.items():
            if key not in data:
                data[key] = value

        # Make API request with retries
        for attempt in range(self.max_retries):
            try:
                response = self.session.post(
                    f"{self.api_base}/chat/completions",
                    json=data,
                    timeout=self.timeout
                )

                if response.status_code == 200:
                    return response.json()
                else:
                    error_msg = f"OpenRouter API error: {response.status_code} - {response.text}"
                    logger.error(error_msg)

                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                    else:
                        raise Exception(error_msg)

            except Exception as e:
                if attempt < self.max_retries - 1:
                    logger.warning(f"OpenRouter API request failed, retrying: {str(e)}")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"OpenRouter API request failed after {self.max_retries} attempts: {str(e)}")
                    raise

        # This should not be reached, but just in case
        raise Exception("OpenRouter API request failed")

    def get_models(self) -> Dict[str, Any]:
        """
        Get available models from OpenRouter.

        Returns:
            API response with available models
        """
        if not self.api_key:
            raise ValueError("OpenRouter API key is required")

        # Make API request with retries
        for attempt in range(self.max_retries):
            try:
                response = self.session.get(
                    f"{self.api_base}/models",
                    timeout=self.timeout
                )

                if response.status_code == 200:
                    return response.json()
                else:
                    error_msg = f"OpenRouter API error: {response.status_code} - {response.text}"
                    logger.error(error_msg)

                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                    else:
                        raise Exception(error_msg)

            except Exception as e:
                if attempt < self.max_retries - 1:
                    logger.warning(f"OpenRouter API request failed, retrying: {str(e)}")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"OpenRouter API request failed after {self.max_retries} attempts: {str(e)}")
                    raise

        # This should not be reached, but just in case
        raise Exception("OpenRouter API request failed")
