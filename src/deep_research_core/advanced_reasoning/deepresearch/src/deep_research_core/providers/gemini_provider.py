"""
Google AI (Gemini) provider for Deep Research Core.

This module provides integration with Google's Gemini API.
"""

import os
import json
import time
import logging
from typing import Dict, List, Any, Optional, Union

import google.generativeai as genai
from google.generativeai.types import GenerationConfig

from deep_research_core.utils.structured_logging import get_logger
from deep_research_core.providers.base_provider import BaseProvider

logger = get_logger(__name__)


class GeminiProvider(BaseProvider):
    """
    Provider for Google's Gemini API.
    
    This class provides methods for interacting with Google's Gemini models.
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        api_base: Optional[str] = None,
        model: str = "gemini-pro",
        **kwargs
    ):
        """
        Initialize the Gemini provider.
        
        Args:
            api_key: Google AI API key
            api_base: Google AI API base URL (optional)
            model: Model name (default: "gemini-pro")
            **kwargs: Additional arguments
        """
        super().__init__()
        
        # Set API key
        self.api_key = api_key or os.environ.get("GOOGLE_API_KEY")
        if not self.api_key:
            raise ValueError("Google AI API key is required")
        
        # Set model
        self.model = model
        
        # Initialize the Gemini API
        genai.configure(api_key=self.api_key)
        
        # Store additional arguments
        self.kwargs = kwargs
    
    def generate(
        self,
        prompt: str,
        max_tokens: int = 1024,
        temperature: float = 0.7,
        top_p: float = 0.95,
        top_k: int = 40,
        stop: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate text using the Gemini API.
        
        Args:
            prompt: Input prompt
            max_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature
            top_p: Top-p sampling parameter
            top_k: Top-k sampling parameter
            stop: List of stop sequences
            **kwargs: Additional arguments
            
        Returns:
            Dictionary containing the generated text and metadata
        """
        try:
            # Create generation config
            generation_config = GenerationConfig(
                max_output_tokens=max_tokens,
                temperature=temperature,
                top_p=top_p,
                top_k=top_k,
                stop_sequences=stop or []
            )
            
            # Get the model
            model = genai.GenerativeModel(self.model)
            
            # Generate text
            response = model.generate_content(
                prompt,
                generation_config=generation_config
            )
            
            # Extract text from response
            text = response.text
            
            # Return formatted response
            return {
                "choices": [
                    {
                        "text": text,
                        "finish_reason": "stop"
                    }
                ],
                "usage": {
                    "prompt_tokens": 0,  # Not provided by the API
                    "completion_tokens": 0,  # Not provided by the API
                    "total_tokens": 0  # Not provided by the API
                },
                "model": self.model,
                "provider": "gemini"
            }
            
        except Exception as e:
            logger.error(f"Error generating text with Gemini: {str(e)}")
            raise
    
    def generate_chat(
        self,
        messages: List[Dict[str, str]],
        max_tokens: int = 1024,
        temperature: float = 0.7,
        top_p: float = 0.95,
        top_k: int = 40,
        stop: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate chat response using the Gemini API.
        
        Args:
            messages: List of message dictionaries with "role" and "content" keys
            max_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature
            top_p: Top-p sampling parameter
            top_k: Top-k sampling parameter
            stop: List of stop sequences
            **kwargs: Additional arguments
            
        Returns:
            Dictionary containing the generated text and metadata
        """
        try:
            # Create generation config
            generation_config = GenerationConfig(
                max_output_tokens=max_tokens,
                temperature=temperature,
                top_p=top_p,
                top_k=top_k,
                stop_sequences=stop or []
            )
            
            # Get the model
            model = genai.GenerativeModel(self.model)
            
            # Convert messages to Gemini format
            chat = model.start_chat()
            
            # Add messages to chat
            for message in messages:
                role = message["role"]
                content = message["content"]
                
                if role == "user":
                    response = chat.send_message(content)
                elif role == "assistant":
                    # Skip assistant messages as they are responses
                    continue
                elif role == "system":
                    # Add system message as user message with special prefix
                    response = chat.send_message(f"System: {content}")
            
            # Generate response for the last message
            last_message = messages[-1]
            if last_message["role"] == "user":
                # Already sent in the loop above
                text = response.text
            else:
                # Send a dummy message to get a response
                response = chat.send_message("Please respond to the above messages.")
                text = response.text
            
            # Return formatted response
            return {
                "choices": [
                    {
                        "text": text,
                        "finish_reason": "stop"
                    }
                ],
                "usage": {
                    "prompt_tokens": 0,  # Not provided by the API
                    "completion_tokens": 0,  # Not provided by the API
                    "total_tokens": 0  # Not provided by the API
                },
                "model": self.model,
                "provider": "gemini"
            }
            
        except Exception as e:
            logger.error(f"Error generating chat with Gemini: {str(e)}")
            raise
    
    def embed(
        self,
        text: Union[str, List[str]],
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate embeddings using the Gemini API.
        
        Args:
            text: Input text or list of texts
            **kwargs: Additional arguments
            
        Returns:
            Dictionary containing the embeddings and metadata
        """
        try:
            # Convert single text to list
            if isinstance(text, str):
                text = [text]
            
            # Get embedding model
            embedding_model = "embedding-001"  # Use the appropriate embedding model
            model = genai.get_embedding_model(embedding_model)
            
            # Generate embeddings
            embeddings = []
            for t in text:
                result = model.embed_content(t)
                embeddings.append(result.embedding)
            
            # Return formatted response
            return {
                "embeddings": embeddings,
                "model": embedding_model,
                "provider": "gemini"
            }
            
        except Exception as e:
            logger.error(f"Error generating embeddings with Gemini: {str(e)}")
            raise
    
    def get_token_count(self, text: str) -> int:
        """
        Get the number of tokens in the text.
        
        Args:
            text: Input text
            
        Returns:
            Number of tokens
        """
        # Gemini doesn't provide a token counting API
        # Use a simple approximation: 1 token ≈ 4 characters
        return len(text) // 4
