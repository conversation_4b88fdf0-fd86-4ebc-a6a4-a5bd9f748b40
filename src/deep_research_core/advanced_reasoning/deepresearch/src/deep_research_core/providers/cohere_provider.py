"""
Cohere provider for Deep Research Core.

This module provides integration with Cohere's API.
"""

import os
import json
import time
import logging
from typing import Dict, List, Any, Optional, Union

import cohere
from cohere.responses.classify import Example

from deep_research_core.utils.structured_logging import get_logger
from deep_research_core.providers.base_provider import BaseProvider

logger = get_logger(__name__)


class CohereProvider(BaseProvider):
    """
    Provider for Cohere's API.
    
    This class provides methods for interacting with Cohere's models.
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        api_base: Optional[str] = None,
        model: str = "command",
        **kwargs
    ):
        """
        Initialize the Cohere provider.
        
        Args:
            api_key: Cohere API key
            api_base: Cohere API base URL (optional)
            model: Model name (default: "command")
            **kwargs: Additional arguments
        """
        super().__init__()
        
        # Set API key
        self.api_key = api_key or os.environ.get("COHERE_API_KEY")
        if not self.api_key:
            raise ValueError("Cohere API key is required")
        
        # Set model
        self.model = model
        
        # Initialize the Cohere client
        self.client = cohere.Client(api_key=self.api_key)
        
        # Store additional arguments
        self.kwargs = kwargs
    
    def generate(
        self,
        prompt: str,
        max_tokens: int = 1024,
        temperature: float = 0.7,
        top_p: float = 0.95,
        top_k: int = 0,
        stop: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate text using the Cohere API.
        
        Args:
            prompt: Input prompt
            max_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature
            top_p: Top-p sampling parameter
            top_k: Top-k sampling parameter
            stop: List of stop sequences
            **kwargs: Additional arguments
            
        Returns:
            Dictionary containing the generated text and metadata
        """
        try:
            # Generate text
            response = self.client.generate(
                prompt=prompt,
                model=self.model,
                max_tokens=max_tokens,
                temperature=temperature,
                p=top_p,
                k=top_k,
                stop_sequences=stop or [],
                return_likelihoods="NONE"
            )
            
            # Extract text from response
            text = response.generations[0].text
            
            # Return formatted response
            return {
                "choices": [
                    {
                        "text": text,
                        "finish_reason": "stop"
                    }
                ],
                "usage": {
                    "prompt_tokens": response.meta.billed_units.input_tokens,
                    "completion_tokens": response.meta.billed_units.output_tokens,
                    "total_tokens": response.meta.billed_units.input_tokens + response.meta.billed_units.output_tokens
                },
                "model": self.model,
                "provider": "cohere"
            }
            
        except Exception as e:
            logger.error(f"Error generating text with Cohere: {str(e)}")
            raise
    
    def generate_chat(
        self,
        messages: List[Dict[str, str]],
        max_tokens: int = 1024,
        temperature: float = 0.7,
        top_p: float = 0.95,
        top_k: int = 0,
        stop: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate chat response using the Cohere API.
        
        Args:
            messages: List of message dictionaries with "role" and "content" keys
            max_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature
            top_p: Top-p sampling parameter
            top_k: Top-k sampling parameter
            stop: List of stop sequences
            **kwargs: Additional arguments
            
        Returns:
            Dictionary containing the generated text and metadata
        """
        try:
            # Convert messages to Cohere chat format
            chat_history = []
            system_message = None
            
            for message in messages[:-1]:  # Exclude the last message
                role = message["role"]
                content = message["content"]
                
                if role == "system":
                    system_message = content
                elif role == "user":
                    chat_history.append({"role": "USER", "message": content})
                elif role == "assistant":
                    chat_history.append({"role": "CHATBOT", "message": content})
            
            # Get the last message (should be from the user)
            last_message = messages[-1]
            if last_message["role"] != "user":
                raise ValueError("The last message must be from the user")
            
            # Generate chat response
            response = self.client.chat(
                message=last_message["content"],
                chat_history=chat_history,
                model=self.model,
                max_tokens=max_tokens,
                temperature=temperature,
                p=top_p,
                k=top_k,
                preamble=system_message,
                stop_sequences=stop or []
            )
            
            # Extract text from response
            text = response.text
            
            # Return formatted response
            return {
                "choices": [
                    {
                        "text": text,
                        "finish_reason": "stop"
                    }
                ],
                "usage": {
                    "prompt_tokens": response.meta.billed_units.input_tokens,
                    "completion_tokens": response.meta.billed_units.output_tokens,
                    "total_tokens": response.meta.billed_units.input_tokens + response.meta.billed_units.output_tokens
                },
                "model": self.model,
                "provider": "cohere"
            }
            
        except Exception as e:
            logger.error(f"Error generating chat with Cohere: {str(e)}")
            raise
    
    def embed(
        self,
        text: Union[str, List[str]],
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate embeddings using the Cohere API.
        
        Args:
            text: Input text or list of texts
            **kwargs: Additional arguments
            
        Returns:
            Dictionary containing the embeddings and metadata
        """
        try:
            # Convert single text to list
            if isinstance(text, str):
                text = [text]
            
            # Generate embeddings
            response = self.client.embed(
                texts=text,
                model="embed-english-v3.0",  # Use the appropriate embedding model
                input_type="search_document"
            )
            
            # Extract embeddings from response
            embeddings = response.embeddings
            
            # Return formatted response
            return {
                "embeddings": embeddings,
                "model": "embed-english-v3.0",
                "provider": "cohere"
            }
            
        except Exception as e:
            logger.error(f"Error generating embeddings with Cohere: {str(e)}")
            raise
    
    def classify(
        self,
        text: Union[str, List[str]],
        examples: List[Dict[str, str]],
        **kwargs
    ) -> Dict[str, Any]:
        """
        Classify text using the Cohere API.
        
        Args:
            text: Input text or list of texts
            examples: List of example dictionaries with "text" and "label" keys
            **kwargs: Additional arguments
            
        Returns:
            Dictionary containing the classifications and metadata
        """
        try:
            # Convert single text to list
            if isinstance(text, str):
                text = [text]
            
            # Convert examples to Cohere format
            cohere_examples = [
                Example(text=example["text"], label=example["label"])
                for example in examples
            ]
            
            # Classify text
            response = self.client.classify(
                inputs=text,
                examples=cohere_examples,
                model="embed-english-v3.0"  # Use the appropriate classification model
            )
            
            # Extract classifications from response
            classifications = []
            for classification in response.classifications:
                classifications.append({
                    "prediction": classification.prediction,
                    "confidence": classification.confidence,
                    "labels": {
                        label: confidence
                        for label, confidence in classification.labels.items()
                    }
                })
            
            # Return formatted response
            return {
                "classifications": classifications,
                "model": "embed-english-v3.0",
                "provider": "cohere"
            }
            
        except Exception as e:
            logger.error(f"Error classifying text with Cohere: {str(e)}")
            raise
    
    def get_token_count(self, text: str) -> int:
        """
        Get the number of tokens in the text.
        
        Args:
            text: Input text
            
        Returns:
            Number of tokens
        """
        try:
            # Use the tokenize endpoint to get token count
            response = self.client.tokenize(text=text)
            return len(response.tokens)
        except Exception as e:
            logger.error(f"Error counting tokens with Cohere: {str(e)}")
            # Fallback to a simple approximation: 1 token ≈ 4 characters
            return len(text) // 4
