"""
Cohere API provider.

This module provides a client for the Cohere API that implements the BaseProvider interface.
"""

import os
import json
import time
import requests
from typing import Dict, List, Any, Optional, Union, Tuple, Callable

try:
    import cohere
    from cohere.responses.classify import Example
    COHERE_AVAILABLE = True
except ImportError:
    COHERE_AVAILABLE = False

from deep_research_core.utils.structured_logging import get_logger
from deep_research_core.providers.base_provider import BaseProvider

logger = get_logger(__name__)


class CohereProvider(BaseProvider):
    """
    Client for the Cohere API.

    This class provides methods for interacting with the Cohere API,
    including text generation with Command models and embeddings.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        api_base: Optional[str] = None,
        model: str = "command",
        timeout: int = 60,
        max_retries: int = 3,
        retry_delay: int = 2,
        **kwargs
    ):
        """
        Initialize the Cohere API client.

        Args:
            api_key: Cohere API key (defaults to COHERE_API_KEY environment variable)
            api_base: Cohere API base URL
            model: Default model to use
            timeout: Request timeout in seconds
            max_retries: Maximum number of retries for failed requests
            retry_delay: Delay between retries in seconds
            **kwargs: Additional parameters
        """
        # Initialize base class
        super().__init__(api_key, api_base, model, timeout, max_retries, retry_delay, **kwargs)

        self.api_key = api_key or os.environ.get("COHERE_API_KEY")

        # Check if we should show warning
        from deep_research_core.config.api_warnings import should_show_warning
        if not self.api_key and should_show_warning("cohere"):
            logger.warning("cohere API key not found. Set COHERE_API_KEY environment variable.")

        self.api_base = api_base or "https://api.cohere.ai/v1"
        self.model = model
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay

        # Set up session for REST API
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        })

        # Set up Cohere client if available
        self.client = None
        if COHERE_AVAILABLE:
            self.client = cohere.Client(api_key=self.api_key)

    def generate(
        self,
        prompt: str,
        system_message: Optional[str] = None,
        max_tokens: int = 1024,
        temperature: float = 0.7,
        top_p: float = 1.0,
        n: int = 1,
        stop: Optional[Union[str, List[str]]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate text using the Cohere API.

        Args:
            prompt: Input prompt text
            system_message: System message for Cohere models
            max_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature
            top_p: Nucleus sampling parameter
            n: Number of completions to generate (Cohere only supports n=1)
            stop: Stop sequences
            **kwargs: Additional parameters

        Returns:
            API response in a standardized format
        """
        if not self.api_key:
            raise ValueError("Cohere API key is required")

        # Use the Python client if available
        if COHERE_AVAILABLE and self.client:
            try:
                # Generate text using the Cohere client
                response = self.client.generate(
                    prompt=prompt,
                    model=self.model,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    p=top_p,
                    stop_sequences=stop if isinstance(stop, list) else ([stop] if stop else None),
                    return_likelihoods="NONE"
                )

                # Extract text from response
                text = response.generations[0].text

                # Return formatted response
                return {
                    "choices": [
                        {
                            "text": text,
                            "finish_reason": "stop"
                        }
                    ],
                    "usage": {
                        "prompt_tokens": response.meta.billed_units.input_tokens,
                        "completion_tokens": response.meta.billed_units.output_tokens,
                        "total_tokens": response.meta.billed_units.input_tokens + response.meta.billed_units.output_tokens
                    },
                    "model": self.model,
                    "provider": "cohere"
                }
            except Exception as e:
                logger.warning(f"Error using Cohere client, falling back to REST API: {str(e)}")
                # Fall back to REST API

        # Prepare request data for REST API
        data = {
            "model": self.model,
            "prompt": prompt,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "p": top_p
        }

        if system_message:
            data["preamble"] = system_message

        if stop:
            data["stop_sequences"] = [stop] if isinstance(stop, str) else stop

        # Add any additional parameters
        for key, value in kwargs.items():
            if key not in data:
                data[key] = value

        # Make API request with retries
        for attempt in range(self.max_retries):
            try:
                response = self.session.post(
                    f"{self.api_base}/generate",
                    json=data,
                    timeout=self.timeout
                )

                if response.status_code == 200:
                    resp_json = response.json()

                    # Convert to standardized format
                    return {
                        "choices": [
                            {
                                "text": resp_json["generations"][0]["text"],
                                "finish_reason": "stop"
                            }
                        ],
                        "usage": {
                            "prompt_tokens": resp_json.get("meta", {}).get("billed_units", {}).get("input_tokens", 0),
                            "completion_tokens": resp_json.get("meta", {}).get("billed_units", {}).get("output_tokens", 0),
                            "total_tokens": (
                                resp_json.get("meta", {}).get("billed_units", {}).get("input_tokens", 0) +
                                resp_json.get("meta", {}).get("billed_units", {}).get("output_tokens", 0)
                            )
                        },
                        "model": self.model,
                        "provider": "cohere"
                    }

                error_msg = f"Cohere API error: {response.status_code} - {response.text}"
                logger.error(error_msg)

                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
                else:
                    raise ValueError(error_msg)

            except Exception as e:
                if attempt < self.max_retries - 1:
                    logger.warning(f"Cohere API request failed, retrying: {str(e)}")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"Cohere API request failed after {self.max_retries} attempts: {str(e)}")
                    raise

        # This should not be reached, but just in case
        raise ValueError("Cohere API request failed")

    def chat(
        self,
        messages: List[Dict[str, str]],
        system_message: Optional[str] = None,
        max_tokens: int = 1024,
        temperature: float = 0.7,
        top_p: float = 1.0,
        n: int = 1,
        stop: Optional[Union[str, List[str]]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Chat with the Cohere API.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            system_message: System message for Cohere models
            max_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature
            top_p: Nucleus sampling parameter
            n: Number of completions to generate (Cohere only supports n=1)
            stop: Stop sequences
            **kwargs: Additional parameters

        Returns:
            API response in a standardized format
        """
        if not self.api_key:
            raise ValueError("Cohere API key is required")

        # Use the Python client if available
        if COHERE_AVAILABLE and self.client:
            try:
                # Convert messages to Cohere chat format
                chat_history = []
                last_message = None

                for i, msg in enumerate(messages):
                    role = msg.get("role", "user")
                    content = msg.get("content", "")

                    if i == len(messages) - 1:
                        # Last message is the current query
                        last_message = content
                    else:
                        # Previous messages go into chat history
                        cohere_role = "USER" if role == "user" else "CHATBOT"
                        chat_history.append({"role": cohere_role, "message": content})

                if not last_message:
                    raise ValueError("No user message found in the messages list")

                # Generate chat response
                response = self.client.chat(
                    message=last_message,
                    chat_history=chat_history,
                    model=self.model,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    p=top_p,
                    preamble=system_message,
                    stop_sequences=stop if isinstance(stop, list) else ([stop] if stop else None)
                )

                # Return formatted response
                return {
                    "choices": [
                        {
                            "text": response.text,
                            "finish_reason": "stop"
                        }
                    ],
                    "usage": {
                        "prompt_tokens": response.meta.billed_units.input_tokens,
                        "completion_tokens": response.meta.billed_units.output_tokens,
                        "total_tokens": (
                            response.meta.billed_units.input_tokens +
                            response.meta.billed_units.output_tokens
                        )
                    },
                    "model": self.model,
                    "provider": "cohere"
                }
            except Exception as e:
                logger.warning(f"Error using Cohere client, falling back to REST API: {str(e)}")
                # Fall back to REST API

        # Prepare request data for REST API
        data = {
            "model": self.model,
            "chat_history": [],
            "message": "",
            "max_tokens": max_tokens,
            "temperature": temperature,
            "p": top_p
        }

        # Process messages
        for i, msg in enumerate(messages):
            role = msg.get("role", "user")
            content = msg.get("content", "")

            if i == len(messages) - 1:
                # Last message is the current query
                data["message"] = content
            else:
                # Previous messages go into chat history
                cohere_role = "USER" if role == "user" else "CHATBOT"
                data["chat_history"].append({
                    "role": cohere_role,
                    "message": content
                })

        if system_message:
            data["preamble"] = system_message

        if stop:
            data["stop_sequences"] = [stop] if isinstance(stop, str) else stop

        # Add any additional parameters
        for key, value in kwargs.items():
            if key not in data:
                data[key] = value

        # Make API request with retries
        for attempt in range(self.max_retries):
            try:
                response = self.session.post(
                    f"{self.api_base}/chat",
                    json=data,
                    timeout=self.timeout
                )

                if response.status_code == 200:
                    resp_json = response.json()

                    # Convert to standardized format
                    return {
                        "choices": [
                            {
                                "text": resp_json.get("text", ""),
                                "finish_reason": "stop"
                            }
                        ],
                        "usage": {
                            "prompt_tokens": resp_json.get("meta", {}).get("billed_units", {}).get("input_tokens", 0),
                            "completion_tokens": resp_json.get("meta", {}).get("billed_units", {}).get("output_tokens", 0),
                            "total_tokens": (
                                resp_json.get("meta", {}).get("billed_units", {}).get("input_tokens", 0) +
                                resp_json.get("meta", {}).get("billed_units", {}).get("output_tokens", 0)
                            )
                        },
                        "model": self.model,
                        "provider": "cohere"
                    }

                error_msg = f"Cohere API error: {response.status_code} - {response.text}"
                logger.error(error_msg)

                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
                else:
                    raise ValueError(error_msg)

            except Exception as e:
                if attempt < self.max_retries - 1:
                    logger.warning(f"Cohere API request failed, retrying: {str(e)}")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"Cohere API request failed after {self.max_retries} attempts: {str(e)}")
                    raise

        # This should not be reached, but just in case
        raise ValueError("Cohere API request failed")

    def embed(
        self,
        text: Union[str, List[str]],
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate embeddings using the Cohere API.

        Args:
            text: Input text or list of texts
            **kwargs: Additional arguments

        Returns:
            Dictionary containing the embeddings and metadata
        """
        if not self.api_key:
            raise ValueError("Cohere API key is required")

        # Use the Python client if available
        if COHERE_AVAILABLE and self.client:
            try:
                # Convert single text to list
                texts = text if isinstance(text, list) else [text]

                # Generate embeddings
                response = self.client.embed(
                    texts=texts,
                    model=kwargs.get("model", "embed-english-v3.0"),
                    input_type=kwargs.get("input_type", "search_document")
                )

                # Extract embeddings from response
                embeddings = response.embeddings

                # Return formatted response
                return {
                    "embeddings": embeddings,
                    "model": kwargs.get("model", "embed-english-v3.0"),
                    "provider": "cohere"
                }

            except Exception as e:
                logger.warning(f"Error using Cohere client, falling back to REST API: {str(e)}")
                # Fall back to REST API

        # Ensure text is a list
        texts = text if isinstance(text, list) else [text]

        # Prepare request data
        data = {
            "model": kwargs.get("model", "embed-english-v3.0"),
            "texts": texts,
            "input_type": kwargs.get("input_type", "search_document")
        }

        # Add any additional parameters
        for key, value in kwargs.items():
            if key not in data and key not in ["model", "input_type"]:
                data[key] = value

        # Make API request with retries
        for attempt in range(self.max_retries):
            try:
                response = self.session.post(
                    f"{self.api_base}/embed",
                    json=data,
                    timeout=self.timeout
                )

                if response.status_code == 200:
                    resp_json = response.json()

                    # Convert to standardized format
                    return {
                        "embeddings": resp_json.get("embeddings", []),
                        "model": data["model"],
                        "provider": "cohere"
                    }

                error_msg = f"Cohere API error: {response.status_code} - {response.text}"
                logger.error(error_msg)

                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
                else:
                    raise ValueError(error_msg)

            except Exception as e:
                if attempt < self.max_retries - 1:
                    logger.warning(f"Cohere API request failed, retrying: {str(e)}")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"Cohere API request failed after {self.max_retries} attempts: {str(e)}")
                    raise

        # This should not be reached, but just in case
        raise ValueError("Cohere API request failed")

    def get_token_count(self, text: str) -> int:
        """
        Get the number of tokens in the text.

        Args:
            text: Input text

        Returns:
            Number of tokens
        """
        if not self.api_key:
            raise ValueError("Cohere API key is required")

        # Use the Python client if available
        if COHERE_AVAILABLE and self.client:
            try:
                # Use the tokenize endpoint to get token count
                response = self.client.tokenize(text=text)
                return len(response.tokens)
            except Exception as e:
                logger.warning(f"Error using Cohere client for token counting, using fallback: {str(e)}")
                # Fall back to approximation

        # Fallback to a simple approximation: 1 token ≈ 4 characters
        return len(text) // 4