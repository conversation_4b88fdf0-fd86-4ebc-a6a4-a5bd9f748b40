"""
Google Gemini API provider.

This module provides a client for the Google Gemini API.
"""

import os
import json
import time
import requests
from typing import Dict, List, Any, Optional, Union, Tuple, Callable

from deep_research_core.utils.structured_logging import get_logger

logger = get_logger(__name__)


class GeminiProvider:
    """
    Client for the Google Gemini API.
    
    This class provides methods for interacting with the Google Gemini API,
    including text generation with Gemini models.
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        api_base: Optional[str] = None,
        model: str = "gemini-1.5-pro",
        timeout: int = 60,
        max_retries: int = 3,
        retry_delay: int = 2,
        **kwargs
    ):
        """
        Initialize the Google Gemini API client.
        
        Args:
            api_key: Google API key (defaults to GOOGLE_API_KEY environment variable)
            api_base: Google API base URL
            model: Default model to use
            timeout: Request timeout in seconds
            max_retries: Maximum number of retries for failed requests
            retry_delay: Delay between retries in seconds
            **kwargs: Additional parameters
        """
        self.api_key = api_key or os.environ.get("GOOGLE_API_KEY")
        if not self.api_key:
            logger.warning("Google API key not found. Set GOOGLE_API_KEY environment variable.")
        
        self.api_base = api_base or "https://generativelanguage.googleapis.com/v1beta"
        self.model = model
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
    
    def generate(
        self,
        prompt: str,
        system_message: Optional[str] = None,
        max_tokens: int = 1024,
        temperature: float = 0.7,
        top_p: float = 1.0,
        stop: Optional[Union[str, List[str]]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate text using the Google Gemini API.
        
        Args:
            prompt: Input prompt text
            system_message: System message for Gemini models
            max_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature
            top_p: Nucleus sampling parameter
            stop: Stop sequences
            **kwargs: Additional parameters
            
        Returns:
            API response
        """
        if not self.api_key:
            raise ValueError("Google API key is required")
        
        # Prepare request data
        contents = []
        
        if system_message:
            contents.append({
                "role": "user",
                "parts": [{"text": f"System: {system_message}"}]
            })
        
        contents.append({
            "role": "user",
            "parts": [{"text": prompt}]
        })
        
        data = {
            "contents": contents,
            "generationConfig": {
                "maxOutputTokens": max_tokens,
                "temperature": temperature,
                "topP": top_p
            }
        }
        
        if stop:
            data["generationConfig"]["stopSequences"] = [stop] if isinstance(stop, str) else stop
        
        # Add any additional parameters
        for key, value in kwargs.items():
            if key not in data:
                data[key] = value
        
        # Make API request with retries
        url = f"{self.api_base}/models/{self.model}:generateContent?key={self.api_key}"
        
        for attempt in range(self.max_retries):
            try:
                response = requests.post(
                    url,
                    json=data,
                    timeout=self.timeout,
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    error_msg = f"Google Gemini API error: {response.status_code} - {response.text}"
                    logger.error(error_msg)
                    
                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                    else:
                        raise Exception(error_msg)
            
            except Exception as e:
                if attempt < self.max_retries - 1:
                    logger.warning(f"Google Gemini API request failed, retrying: {str(e)}")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"Google Gemini API request failed after {self.max_retries} attempts: {str(e)}")
                    raise
        
        # This should not be reached, but just in case
        raise Exception("Google Gemini API request failed")
    
    def get_embeddings(
        self,
        texts: Union[str, List[str]],
        model: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Get embeddings for text using the Google Gemini API.
        
        Args:
            texts: Text or list of texts to embed
            model: Embedding model to use
            **kwargs: Additional parameters
            
        Returns:
            API response with embeddings
        """
        if not self.api_key:
            raise ValueError("Google API key is required")
        
        # Ensure texts is a list
        if isinstance(texts, str):
            texts = [texts]
        
        embedding_model = model or "embedding-001"
        
        results = {"data": []}
        
        # Process each text separately
        for text in texts:
            data = {
                "model": f"models/{embedding_model}",
                "content": {
                    "parts": [{"text": text}]
                }
            }
            
            # Add any additional parameters
            for key, value in kwargs.items():
                if key not in data:
                    data[key] = value
            
            url = f"{self.api_base}/models/{embedding_model}:embedContent?key={self.api_key}"
            
            # Make API request with retries
            for attempt in range(self.max_retries):
                try:
                    response = requests.post(
                        url,
                        json=data,
                        timeout=self.timeout,
                        headers={"Content-Type": "application/json"}
                    )
                    
                    if response.status_code == 200:
                        response_data = response.json()
                        results["data"].append({
                            "embedding": response_data.get("embedding", {}).get("values", []),
                            "index": len(results["data"]),
                            "object": "embedding"
                        })
                        break
                    else:
                        error_msg = f"Google Gemini API error: {response.status_code} - {response.text}"
                        logger.error(error_msg)
                        
                        if attempt < self.max_retries - 1:
                            time.sleep(self.retry_delay)
                        else:
                            raise Exception(error_msg)
                
                except Exception as e:
                    if attempt < self.max_retries - 1:
                        logger.warning(f"Google Gemini API request failed, retrying: {str(e)}")
                        time.sleep(self.retry_delay)
                    else:
                        logger.error(f"Google Gemini API request failed after {self.max_retries} attempts: {str(e)}")
                        raise
        
        return results
