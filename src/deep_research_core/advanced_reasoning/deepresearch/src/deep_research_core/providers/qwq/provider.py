"""
QwQ API provider.

This module provides a client for the QwQ API.
"""

import os
import json
import time
import requests
from typing import Dict, List, Any, Optional, Union, Tuple, Callable

from deep_research_core.utils.structured_logging import get_logger

logger = get_logger(__name__)


class QwQProvider:
    """
    Client for the QwQ API.

    This class provides methods for interacting with the QwQ API,
    including text generation with QwQ models.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        api_base: Optional[str] = None,
        model: str = "qwq-32b",
        timeout: int = 60,
        max_retries: int = 3,
        retry_delay: int = 2,
        **kwargs
    ):
        """
        Initialize the QwQ API client.

        Args:
            api_key: QwQ API key (defaults to QWQ_API_KEY environment variable)
            api_base: QwQ API base URL
            model: Default model to use
            timeout: Request timeout in seconds
            max_retries: Maximum number of retries for failed requests
            retry_delay: Delay between retries in seconds
            **kwargs: Additional parameters
        """
        self.api_key = api_key or os.environ.get("QWQ_API_KEY")

        # Check if we should show warning
        from deep_research_core.config.api_warnings import should_show_warning
        if not self.api_key and should_show_warning("qwq"):
            logger.warning("qwq API key not found. Set QWQ_API_KEY environment variable.")

        self.api_base = api_base or "https://api.qwq.ai/v1"
        self.model = model
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay

        # Set up session
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        })

    def generate(
        self,
        prompt: str,
        system_message: Optional[str] = None,
        max_tokens: int = 1024,
        temperature: float = 0.7,
        top_p: float = 1.0,
        stop: Optional[Union[str, List[str]]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate text using the QwQ API.

        Args:
            prompt: Input prompt text
            system_message: System message for QwQ models
            max_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature
            top_p: Nucleus sampling parameter
            stop: Stop sequences
            **kwargs: Additional parameters

        Returns:
            API response
        """
        if not self.api_key:
            raise ValueError("QwQ API key is required")

        # Prepare request data
        messages = []

        if system_message:
            messages.append({"role": "system", "content": system_message})

        messages.append({"role": "user", "content": prompt})

        data = {
            "model": self.model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "top_p": top_p
        }

        if stop:
            data["stop"] = stop if isinstance(stop, list) else [stop]

        # Add any additional parameters
        for key, value in kwargs.items():
            if key not in data:
                data[key] = value

        # Make API request with retries
        for attempt in range(self.max_retries):
            try:
                response = self.session.post(
                    f"{self.api_base}/chat/completions",
                    json=data,
                    timeout=self.timeout
                )

                if response.status_code == 200:
                    return response.json()
                else:
                    error_msg = f"QwQ API error: {response.status_code} - {response.text}"
                    logger.error(error_msg)

                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                    else:
                        raise Exception(error_msg)

            except Exception as e:
                if attempt < self.max_retries - 1:
                    logger.warning(f"QwQ API request failed, retrying: {str(e)}")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"QwQ API request failed after {self.max_retries} attempts: {str(e)}")
                    raise

        # This should not be reached, but just in case
        raise Exception("QwQ API request failed")

    def get_embeddings(
        self,
        texts: Union[str, List[str]],
        model: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Get embeddings for text using the QwQ API.

        Args:
            texts: Text or list of texts to embed
            model: Embedding model to use
            **kwargs: Additional parameters

        Returns:
            API response with embeddings
        """
        if not self.api_key:
            raise ValueError("QwQ API key is required")

        # Ensure texts is a list
        if isinstance(texts, str):
            texts = [texts]

        # Prepare request data
        data = {
            "model": model or "qwq-embedding",
            "input": texts
        }

        # Add any additional parameters
        for key, value in kwargs.items():
            if key not in data:
                data[key] = value

        # Make API request with retries
        for attempt in range(self.max_retries):
            try:
                response = self.session.post(
                    f"{self.api_base}/embeddings",
                    json=data,
                    timeout=self.timeout
                )

                if response.status_code == 200:
                    return response.json()
                else:
                    error_msg = f"QwQ API error: {response.status_code} - {response.text}"
                    logger.error(error_msg)

                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                    else:
                        raise Exception(error_msg)

            except Exception as e:
                if attempt < self.max_retries - 1:
                    logger.warning(f"QwQ API request failed, retrying: {str(e)}")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"QwQ API request failed after {self.max_retries} attempts: {str(e)}")
                    raise

        # This should not be reached, but just in case
        raise Exception("QwQ API request failed")
