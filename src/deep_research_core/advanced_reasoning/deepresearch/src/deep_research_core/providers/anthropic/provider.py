"""
Anthropic API provider.

This module provides a client for the Anthropic API.
"""

import os
import json
import time
import requests
from typing import Dict, List, Any, Optional, Union, Tuple, Callable

from deep_research_core.utils.structured_logging import get_logger
from deep_research_core.providers.base_provider import BaseProvider

logger = get_logger(__name__)


class AnthropicProvider(BaseProvider):
    """
    Client for the Anthropic API.

    This class provides methods for interacting with the Anthropic API,
    including text generation with Claude models.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        api_base: Optional[str] = None,
        model: str = "claude-3-opus-20240229",
        timeout: int = 60,
        max_retries: int = 3,
        retry_delay: int = 2,
        **kwargs
    ):
        """
        Initialize the Anthropic API client.

        Args:
            api_key: Anthropic API key (defaults to ANTHROPIC_API_KEY environment variable)
            api_base: Anthropic API base URL
            model: Default model to use
            timeout: Request timeout in seconds
            max_retries: Maximum number of retries for failed requests
            retry_delay: Delay between retries in seconds
            **kwargs: Additional parameters
        """
        self.api_key = api_key or os.environ.get("ANTHROPIC_API_KEY")

        # Check if we should show warning
        from deep_research_core.config.api_warnings import should_show_warning
        if not self.api_key and should_show_warning("anthropic"):
            logger.warning("anthropic API key not found. Set ANTHROPIC_API_KEY environment variable.")

        self.api_base = api_base or "https://api.anthropic.com/v1"
        self.model = model
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay

        # Set up session
        self.session = requests.Session()
        self.session.headers.update({
            "x-api-key": self.api_key,
            "anthropic-version": "2023-06-01",
            "Content-Type": "application/json"
        })

    def generate(
        self,
        prompt: str,
        system_message: Optional[str] = None,
        max_tokens: int = 1024,
        temperature: float = 0.7,
        top_p: float = 1.0,
        stop: Optional[Union[str, List[str]]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate text using the Anthropic API.

        Args:
            prompt: Input prompt text
            system_message: System message for Claude models
            max_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature
            top_p: Nucleus sampling parameter
            stop: Stop sequences
            **kwargs: Additional parameters

        Returns:
            API response
        """
        if not self.api_key:
            raise ValueError("Anthropic API key is required")

        # Prepare request data
        data = {
            "model": self.model,
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": max_tokens,
            "temperature": temperature,
            "top_p": top_p
        }

        if system_message:
            data["system"] = system_message

        if stop:
            data["stop_sequences"] = [stop] if isinstance(stop, str) else stop

        # Add any additional parameters
        for key, value in kwargs.items():
            if key not in data:
                data[key] = value

        # Make API request with retries
        for attempt in range(self.max_retries):
            try:
                response = self.session.post(
                    f"{self.api_base}/messages",
                    json=data,
                    timeout=self.timeout
                )

                if response.status_code == 200:
                    return response.json()
                else:
                    error_msg = f"Anthropic API error: {response.status_code} - {response.text}"
                    logger.error(error_msg)

                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                    else:
                        raise Exception(error_msg)

            except Exception as e:
                if attempt < self.max_retries - 1:
                    logger.warning(f"Anthropic API request failed, retrying: {str(e)}")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"Anthropic API request failed after {self.max_retries} attempts: {str(e)}")
                    raise

        # This should not be reached, but just in case
        raise Exception("Anthropic API request failed")
