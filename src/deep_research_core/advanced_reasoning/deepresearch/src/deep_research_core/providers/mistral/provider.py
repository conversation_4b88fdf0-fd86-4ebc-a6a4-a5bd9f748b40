"""
Mistral AI API provider.

This module provides a client for the Mistral AI API that implements the BaseProvider interface.
"""

import os
import json
import time
import requests
from typing import Dict, List, Any, Optional, Union, Tuple, Callable

try:
    import mistralai
    from mistralai.client import MistralClient
    from mistralai.models.chat_completion import ChatMessage
    MISTRAL_AVAILABLE = True
except ImportError:
    MISTRAL_AVAILABLE = False

from deep_research_core.utils.structured_logging import get_logger
from deep_research_core.providers.base_provider import BaseProvider

logger = get_logger(__name__)


class MistralProvider(BaseProvider):
    """
    Client for the Mistral AI API.

    This class provides methods for interacting with the Mistral AI API,
    including text generation with Mistral models.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        api_base: Optional[str] = None,
        model: str = "mistral-large-latest",
        timeout: int = 60,
        max_retries: int = 3,
        retry_delay: int = 2,
        **kwargs
    ):
        """
        Initialize the Mistral AI API client.

        Args:
            api_key: Mistral AI API key (defaults to MISTRAL_API_KEY environment variable)
            api_base: Mistral AI API base URL
            model: Default model to use
            timeout: Request timeout in seconds
            max_retries: Maximum number of retries for failed requests
            retry_delay: Delay between retries in seconds
            **kwargs: Additional parameters
        """
        # Initialize base class
        super().__init__(api_key, api_base, model, timeout, max_retries, retry_delay, **kwargs)

        self.api_key = api_key or os.environ.get("MISTRAL_API_KEY")

        # Check if we should show warning
        from deep_research_core.config.api_warnings import should_show_warning
        if not self.api_key and should_show_warning("mistral"):
            logger.warning("Mistral AI API key not found. Set MISTRAL_API_KEY environment variable.")

        self.api_base = api_base or "https://api.mistral.ai/v1"
        self.model = model
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay

        # Set up session for REST API
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        })

        # Set up Mistral client if available
        self.client = None
        if MISTRAL_AVAILABLE:
            self.client = MistralClient(api_key=self.api_key, endpoint=self.api_base)

    def generate(
        self,
        prompt: str,
        system_message: Optional[str] = None,
        max_tokens: int = 1024,
        temperature: float = 0.7,
        top_p: float = 1.0,
        n: int = 1,
        stop: Optional[Union[str, List[str]]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate text using the Mistral AI API.

        Args:
            prompt: Input prompt text
            system_message: System message for chat models
            max_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature
            top_p: Nucleus sampling parameter
            n: Number of completions to generate
            stop: Stop sequences
            **kwargs: Additional parameters

        Returns:
            API response in a standardized format
        """
        if not self.api_key:
            raise ValueError("Mistral AI API key is required")

        # Use the Python client if available
        if MISTRAL_AVAILABLE and self.client:
            try:
                # Create messages
                messages = []

                if system_message:
                    messages.append(ChatMessage(role="system", content=system_message))

                messages.append(ChatMessage(role="user", content=prompt))

                # Generate text
                response = self.client.chat(
                    model=self.model,
                    messages=messages,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    top_p=top_p,
                    random_seed=kwargs.get("random_seed"),
                    safe_prompt=kwargs.get("safe_prompt", False),
                    stop=stop if isinstance(stop, list) else ([stop] if stop else None)
                )

                # Extract text from response
                text = response.choices[0].message.content

                # Return formatted response
                return {
                    "choices": [
                        {
                            "text": text,
                            "finish_reason": response.choices[0].finish_reason
                        }
                    ],
                    "usage": {
                        "prompt_tokens": response.usage.prompt_tokens,
                        "completion_tokens": response.usage.completion_tokens,
                        "total_tokens": response.usage.total_tokens
                    },
                    "model": self.model,
                    "provider": "mistral"
                }
            except Exception as e:
                logger.warning(f"Error using Mistral client, falling back to REST API: {str(e)}")
                # Fall back to REST API

        # Prepare request data for REST API
        messages = []

        if system_message:
            messages.append({"role": "system", "content": system_message})

        messages.append({"role": "user", "content": prompt})

        data = {
            "model": self.model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "top_p": top_p,
            "n": n
        }

        if stop:
            data["stop"] = stop if isinstance(stop, list) else [stop]

        # Add any additional parameters
        for key, value in kwargs.items():
            if key not in data:
                data[key] = value

        # Make API request with retries
        for attempt in range(self.max_retries):
            try:
                response = self.session.post(
                    f"{self.api_base}/chat/completions",
                    json=data,
                    timeout=self.timeout
                )

                if response.status_code == 200:
                    resp_json = response.json()

                    # Convert to standardized format
                    return {
                        "choices": [
                            {
                                "text": resp_json["choices"][0]["message"]["content"],
                                "finish_reason": resp_json["choices"][0].get("finish_reason", "stop")
                            }
                        ],
                        "usage": {
                            "prompt_tokens": resp_json.get("usage", {}).get("prompt_tokens", 0),
                            "completion_tokens": resp_json.get("usage", {}).get("completion_tokens", 0),
                            "total_tokens": resp_json.get("usage", {}).get("total_tokens", 0)
                        },
                        "model": self.model,
                        "provider": "mistral"
                    }

                error_msg = f"Mistral AI API error: {response.status_code} - {response.text}"
                logger.error(error_msg)

                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
                else:
                    raise ValueError(error_msg)

            except Exception as e:
                if attempt < self.max_retries - 1:
                    logger.warning(f"Mistral AI API request failed, retrying: {str(e)}")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"Mistral AI API request failed after {self.max_retries} attempts: {str(e)}")
                    raise

        # This should not be reached, but just in case
        raise ValueError("Mistral AI API request failed")

    def embed(
        self,
        text: Union[str, List[str]],
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate embeddings using the Mistral AI API.

        Args:
            text: Input text or list of texts
            **kwargs: Additional arguments

        Returns:
            Dictionary containing the embeddings and metadata
        """
        if not self.api_key:
            raise ValueError("Mistral AI API key is required")

        # Use the Python client if available
        if MISTRAL_AVAILABLE and self.client:
            try:
                # Convert single text to list
                texts = text if isinstance(text, list) else [text]

                # Generate embeddings
                response = self.client.embeddings(
                    model=kwargs.get("model", "mistral-embed"),
                    input=texts
                )

                # Extract embeddings from response
                embeddings = [data.embedding for data in response.data]

                # Return formatted response
                return {
                    "embeddings": embeddings,
                    "model": kwargs.get("model", "mistral-embed"),
                    "provider": "mistral"
                }

            except Exception as e:
                logger.warning(f"Error using Mistral client, falling back to REST API: {str(e)}")
                # Fall back to REST API

        # Ensure text is a list
        texts = text if isinstance(text, list) else [text]

        # Prepare request data
        data = {
            "model": kwargs.get("model", "mistral-embed"),
            "input": texts
        }

        # Add any additional parameters
        for key, value in kwargs.items():
            if key not in data and key != "model":
                data[key] = value

        # Make API request with retries
        for attempt in range(self.max_retries):
            try:
                response = self.session.post(
                    f"{self.api_base}/embeddings",
                    json=data,
                    timeout=self.timeout
                )

                if response.status_code == 200:
                    resp_json = response.json()

                    # Extract embeddings from response
                    embeddings = [item["embedding"] for item in resp_json.get("data", [])]

                    # Return formatted response
                    return {
                        "embeddings": embeddings,
                        "model": data["model"],
                        "provider": "mistral"
                    }

                error_msg = f"Mistral AI API error: {response.status_code} - {response.text}"
                logger.error(error_msg)

                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
                else:
                    raise ValueError(error_msg)

            except Exception as e:
                if attempt < self.max_retries - 1:
                    logger.warning(f"Mistral AI API request failed, retrying: {str(e)}")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"Mistral AI API request failed after {self.max_retries} attempts: {str(e)}")
                    raise

        # This should not be reached, but just in case
        raise ValueError("Mistral AI API request failed")

    def get_token_count(self, text: str) -> int:
        """
        Get the number of tokens in the text.

        Args:
            text: Input text

        Returns:
            Number of tokens
        """
        if not self.api_key:
            raise ValueError("Mistral AI API key is required")

        # Use the Python client if available
        if MISTRAL_AVAILABLE and self.client:
            try:
                # Use the count_tokens method
                response = self.client.count_tokens(
                    model=self.model,
                    content=text
                )
                return response.usage.total_tokens
            except Exception as e:
                logger.warning(f"Error using Mistral client for token counting, using fallback: {str(e)}")
                # Fall back to approximation

        # Fallback to a simple approximation: 1 token ≈ 4 characters
        return len(text) // 4