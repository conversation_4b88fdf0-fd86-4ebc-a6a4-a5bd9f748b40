"""
DeepSeek API provider.

This module provides a client for the DeepSeek API.
"""

import os
import json
import time
import requests
from typing import Dict, List, Any, Optional, Union, Tuple, Callable

from deep_research_core.utils.structured_logging import get_logger
from deep_research_core.providers.base_provider import BaseProvider

logger = get_logger(__name__)


class DeepSeekProvider(BaseProvider):
    """
    Client for the DeepSeek API.

    This class provides methods for interacting with the DeepSeek API,
    including text generation, embeddings, and fine-tuning.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        api_base: Optional[str] = None,
        model: str = "deepseek-ai/deepseek-coder-33b-instruct",
        timeout: int = 60,
        max_retries: int = 3,
        retry_delay: int = 2,
        **kwargs
    ):
        """
        Initialize the DeepSeek API client.

        Args:
            api_key: DeepSeek API key (defaults to DEEPSEEK_API_KEY environment variable)
            api_base: DeepSeek API base URL
            model: Default model to use
            timeout: Request timeout in seconds
            max_retries: Maximum number of retries for failed requests
            retry_delay: Delay between retries in seconds
            **kwargs: Additional parameters
        """
        self.api_key = api_key or os.environ.get("DEEPSEEK_API_KEY")

        # Check if we should show warning
        from deep_research_core.config.api_warnings import should_show_warning
        if not self.api_key and should_show_warning("deepseek"):
            logger.warning("deepseek API key not found. Set DEEPSEEK_API_KEY environment variable.")

        self.api_base = api_base or "https://api.deepseek.com/v1"
        self.model = model
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay

        # Set up session
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        })

    def generate(
        self,
        prompt: str,
        system_message: Optional[str] = None,
        max_tokens: int = 1024,
        temperature: float = 0.7,
        top_p: float = 1.0,
        n: int = 1,
        stop: Optional[Union[str, List[str]]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate text using the DeepSeek API.

        Args:
            prompt: Input prompt text
            system_message: System message for chat models
            max_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature
            top_p: Nucleus sampling parameter
            n: Number of completions to generate
            stop: Stop sequences
            **kwargs: Additional parameters

        Returns:
            API response
        """
        if not self.api_key:
            raise ValueError("DeepSeek API key is required")

        # Prepare request data
        messages = []

        if system_message:
            messages.append({"role": "system", "content": system_message})

        messages.append({"role": "user", "content": prompt})

        data = {
            "model": self.model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "top_p": top_p,
            "n": n
        }

        if stop:
            data["stop"] = stop

        # Add any additional parameters
        for key, value in kwargs.items():
            if key not in data:
                data[key] = value

        # Make API request with retries
        for attempt in range(self.max_retries):
            try:
                response = self.session.post(
                    f"{self.api_base}/chat/completions",
                    json=data,
                    timeout=self.timeout
                )

                if response.status_code == 200:
                    return response.json()
                else:
                    error_msg = f"DeepSeek API error: {response.status_code} - {response.text}"
                    logger.error(error_msg)

                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                    else:
                        raise Exception(error_msg)

            except Exception as e:
                if attempt < self.max_retries - 1:
                    logger.warning(f"DeepSeek API request failed, retrying: {str(e)}")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"DeepSeek API request failed after {self.max_retries} attempts: {str(e)}")
                    raise

        # This should not be reached, but just in case
        raise Exception("DeepSeek API request failed")

    def get_embeddings(
        self,
        texts: Union[str, List[str]],
        model: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Get embeddings for text using the DeepSeek API.

        Args:
            texts: Text or list of texts to embed
            model: Embedding model to use
            **kwargs: Additional parameters

        Returns:
            API response with embeddings
        """
        if not self.api_key:
            raise ValueError("DeepSeek API key is required")

        # Ensure texts is a list
        if isinstance(texts, str):
            texts = [texts]

        # Prepare request data
        data = {
            "model": model or "deepseek-ai/deepseek-embedding",
            "input": texts
        }

        # Add any additional parameters
        for key, value in kwargs.items():
            if key not in data:
                data[key] = value

        # Make API request with retries
        for attempt in range(self.max_retries):
            try:
                response = self.session.post(
                    f"{self.api_base}/embeddings",
                    json=data,
                    timeout=self.timeout
                )

                if response.status_code == 200:
                    return response.json()
                else:
                    error_msg = f"DeepSeek API error: {response.status_code} - {response.text}"
                    logger.error(error_msg)

                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                    else:
                        raise Exception(error_msg)

            except Exception as e:
                if attempt < self.max_retries - 1:
                    logger.warning(f"DeepSeek API request failed, retrying: {str(e)}")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"DeepSeek API request failed after {self.max_retries} attempts: {str(e)}")
                    raise

        # This should not be reached, but just in case
        raise Exception("DeepSeek API request failed")

    def create_fine_tuning_job(
        self,
        training_file: str,
        validation_file: Optional[str] = None,
        model: Optional[str] = None,
        hyperparameters: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a fine-tuning job using the DeepSeek API.

        Args:
            training_file: Path to training data file
            validation_file: Path to validation data file
            model: Base model to fine-tune
            hyperparameters: Fine-tuning hyperparameters
            **kwargs: Additional parameters

        Returns:
            API response with fine-tuning job details
        """
        if not self.api_key:
            raise ValueError("DeepSeek API key is required")

        # Prepare request data
        data = {
            "training_file": training_file,
            "model": model or self.model
        }

        if validation_file:
            data["validation_file"] = validation_file

        if hyperparameters:
            data["hyperparameters"] = hyperparameters

        # Add any additional parameters
        for key, value in kwargs.items():
            if key not in data:
                data[key] = value

        # Make API request with retries
        for attempt in range(self.max_retries):
            try:
                response = self.session.post(
                    f"{self.api_base}/fine-tuning/jobs",
                    json=data,
                    timeout=self.timeout
                )

                if response.status_code == 200:
                    return response.json()
                else:
                    error_msg = f"DeepSeek API error: {response.status_code} - {response.text}"
                    logger.error(error_msg)

                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                    else:
                        raise Exception(error_msg)

            except Exception as e:
                if attempt < self.max_retries - 1:
                    logger.warning(f"DeepSeek API request failed, retrying: {str(e)}")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"DeepSeek API request failed after {self.max_retries} attempts: {str(e)}")
                    raise

        # This should not be reached, but just in case
        raise Exception("DeepSeek API request failed")

    def get_fine_tuning_job(
        self,
        job_id: str
    ) -> Dict[str, Any]:
        """
        Get details of a fine-tuning job.

        Args:
            job_id: ID of the fine-tuning job

        Returns:
            API response with fine-tuning job details
        """
        if not self.api_key:
            raise ValueError("DeepSeek API key is required")

        # Make API request with retries
        for attempt in range(self.max_retries):
            try:
                response = self.session.get(
                    f"{self.api_base}/fine-tuning/jobs/{job_id}",
                    timeout=self.timeout
                )

                if response.status_code == 200:
                    return response.json()
                else:
                    error_msg = f"DeepSeek API error: {response.status_code} - {response.text}"
                    logger.error(error_msg)

                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                    else:
                        raise Exception(error_msg)

            except Exception as e:
                if attempt < self.max_retries - 1:
                    logger.warning(f"DeepSeek API request failed, retrying: {str(e)}")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"DeepSeek API request failed after {self.max_retries} attempts: {str(e)}")
                    raise

        # This should not be reached, but just in case
        raise Exception("DeepSeek API request failed")

    def list_fine_tuning_jobs(
        self,
        limit: int = 10,
        **kwargs
    ) -> Dict[str, Any]:
        """
        List fine-tuning jobs.

        Args:
            limit: Maximum number of jobs to return
            **kwargs: Additional parameters

        Returns:
            API response with fine-tuning jobs
        """
        if not self.api_key:
            raise ValueError("DeepSeek API key is required")

        # Prepare query parameters
        params = {"limit": limit}

        # Add any additional parameters
        for key, value in kwargs.items():
            if key not in params:
                params[key] = value

        # Make API request with retries
        for attempt in range(self.max_retries):
            try:
                response = self.session.get(
                    f"{self.api_base}/fine-tuning/jobs",
                    params=params,
                    timeout=self.timeout
                )

                if response.status_code == 200:
                    return response.json()
                else:
                    error_msg = f"DeepSeek API error: {response.status_code} - {response.text}"
                    logger.error(error_msg)

                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                    else:
                        raise Exception(error_msg)

            except Exception as e:
                if attempt < self.max_retries - 1:
                    logger.warning(f"DeepSeek API request failed, retrying: {str(e)}")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"DeepSeek API request failed after {self.max_retries} attempts: {str(e)}")
                    raise

        # This should not be reached, but just in case
        raise Exception("DeepSeek API request failed")

    def upload_file(
        self,
        file_path: str,
        purpose: str = "fine-tune"
    ) -> Dict[str, Any]:
        """
        Upload a file to the DeepSeek API.

        Args:
            file_path: Path to the file to upload
            purpose: Purpose of the file

        Returns:
            API response with file details
        """
        if not self.api_key:
            raise ValueError("DeepSeek API key is required")

        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")

        # Make API request with retries
        for attempt in range(self.max_retries):
            try:
                with open(file_path, "rb") as f:
                    response = self.session.post(
                        f"{self.api_base}/files",
                        files={"file": f},
                        data={"purpose": purpose},
                        timeout=self.timeout
                    )

                if response.status_code == 200:
                    return response.json()
                else:
                    error_msg = f"DeepSeek API error: {response.status_code} - {response.text}"
                    logger.error(error_msg)

                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                    else:
                        raise Exception(error_msg)

            except Exception as e:
                if attempt < self.max_retries - 1:
                    logger.warning(f"DeepSeek API request failed, retrying: {str(e)}")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"DeepSeek API request failed after {self.max_retries} attempts: {str(e)}")
                    raise

        # This should not be reached, but just in case
        raise Exception("DeepSeek API request failed")
