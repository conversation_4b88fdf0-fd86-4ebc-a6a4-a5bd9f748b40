"""
Base provider interface.

This module defines the base interface for all API providers.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union, Tuple, Callable


class BaseProvider(ABC):
    """
    Base class for all API providers.
    
    This abstract class defines the interface that all provider implementations
    must follow to ensure consistent behavior across different APIs.
    """
    
    @abstractmethod
    def __init__(
        self,
        api_key: Optional[str] = None,
        api_base: Optional[str] = None,
        model: str = "default",
        timeout: int = 60,
        max_retries: int = 3,
        retry_delay: int = 2,
        **kwargs
    ):
        """
        Initialize the API provider.
        
        Args:
            api_key: API key for authentication
            api_base: API base URL
            model: Default model to use
            timeout: Request timeout in seconds
            max_retries: Maximum number of retries for failed requests
            retry_delay: Delay between retries in seconds
            **kwargs: Additional parameters
        """
        pass
    
    @abstractmethod
    def generate(
        self,
        prompt: str,
        system_message: Optional[str] = None,
        max_tokens: int = 1024,
        temperature: float = 0.7,
        top_p: float = 1.0,
        n: int = 1,
        stop: Optional[Union[str, List[str]]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate text using the API.
        
        Args:
            prompt: Input prompt text
            system_message: System message for chat models
            max_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature
            top_p: Nucleus sampling parameter
            n: Number of completions to generate
            stop: Stop sequences
            **kwargs: Additional parameters
            
        Returns:
            API response
        """
        pass
