"""
Context processor implementation for Deep Research Core.

This module provides functionality for processing context,
including dynamic context window and context compression.
"""

import re
import nltk
import logging
from typing import Dict, List, Any, Optional, Union, Tuple, Set, Callable
from dataclasses import dataclass

import numpy as np

from ...utils.structured_logging import get_logger
from ...utils.performance import trace_function, measure_latency
from ...models.embeddings.base import get_embedding_model

# Get logger
logger = get_logger(__name__)


@dataclass
class ContextProcessorConfig:
    """Configuration for context processor."""
    
    max_tokens: int = 3000
    min_tokens: int = 500
    token_buffer: int = 200  # Buffer to account for prompt tokens
    compression_ratio: float = 0.7  # Target compression ratio
    use_dynamic_window: bool = True
    use_compression: bool = True
    relevance_threshold: float = 0.7
    embedding_model: Optional[str] = None
    device: str = "cpu"


class DynamicContextWindow:
    """
    Dynamic context window implementation.
    
    This class provides functionality for dynamically adjusting the
    context window size based on query complexity and document relevance.
    """
    
    def __init__(
        self,
        max_tokens: int = 3000,
        min_tokens: int = 500,
        token_buffer: int = 200,
        relevance_threshold: float = 0.7,
        embedding_model: Optional[str] = None,
        device: str = "cpu"
    ):
        """
        Initialize dynamic context window.
        
        Args:
            max_tokens: Maximum number of tokens in context window
            min_tokens: Minimum number of tokens in context window
            token_buffer: Buffer to account for prompt tokens
            relevance_threshold: Threshold for document relevance
            embedding_model: Name of embedding model to use
            device: Device to use for embedding model
        """
        self.max_tokens = max_tokens
        self.min_tokens = min_tokens
        self.token_buffer = token_buffer
        self.relevance_threshold = relevance_threshold
        
        # Initialize embedding model if specified
        self.model = None
        if embedding_model:
            try:
                self.model = get_embedding_model(embedding_model, device=device)
                logger.info(f"Initialized embedding model: {embedding_model}")
            except Exception as e:
                logger.warning(f"Failed to initialize embedding model: {e}")
    
    @trace_function(name="dynamic_context_window_adjust")
    @measure_latency("dynamic_context_window_adjust")
    def adjust(
        self,
        query: str,
        documents: List[Dict[str, Any]],
        token_counter: Optional[Callable[[str], int]] = None,
        content_field: str = "content"
    ) -> List[Dict[str, Any]]:
        """
        Adjust context window based on query complexity and document relevance.
        
        Args:
            query: Query string
            documents: List of documents
            token_counter: Function to count tokens in text
            content_field: Field containing document content
            
        Returns:
            Adjusted list of documents
        """
        if not documents:
            return []
        
        # If no token counter provided, use simple approximation
        if token_counter is None:
            token_counter = lambda text: len(text.split())
        
        # Calculate query complexity
        query_complexity = self._calculate_query_complexity(query)
        
        # Adjust max tokens based on query complexity
        adjusted_max_tokens = int(self.max_tokens * query_complexity)
        adjusted_max_tokens = max(self.min_tokens, min(adjusted_max_tokens, self.max_tokens))
        
        # Calculate available tokens (accounting for buffer)
        available_tokens = adjusted_max_tokens - self.token_buffer
        
        # If embedding model is available, calculate document relevance
        if self.model:
            # Get query embedding
            query_embedding = self.model.get_embedding(query)
            
            # Calculate document relevance
            documents_with_relevance = []
            for doc in documents:
                content = doc.get(content_field, "")
                if content:
                    # Get document embedding
                    doc_embedding = self.model.get_embedding(content)
                    
                    # Calculate cosine similarity
                    relevance = self._calculate_similarity(query_embedding, doc_embedding)
                    
                    documents_with_relevance.append((doc, relevance))
            
            # Sort by relevance (descending)
            documents_with_relevance.sort(key=lambda x: x[1], reverse=True)
            
            # Filter by relevance threshold
            filtered_docs = [doc for doc, relevance in documents_with_relevance if relevance >= self.relevance_threshold]
            
            # If no documents pass threshold, use top documents
            if not filtered_docs and documents_with_relevance:
                filtered_docs = [documents_with_relevance[0][0]]
                
                # Add more documents if they're close to the top document's relevance
                top_relevance = documents_with_relevance[0][1]
                for doc, relevance in documents_with_relevance[1:]:
                    if relevance >= top_relevance * 0.9:  # Within 90% of top relevance
                        filtered_docs.append(doc)
            
            documents = filtered_docs
        
        # Count tokens in each document
        documents_with_tokens = [(doc, token_counter(doc.get(content_field, ""))) for doc in documents]
        
        # If total tokens is within limit, return all documents
        total_tokens = sum(tokens for _, tokens in documents_with_tokens)
        if total_tokens <= available_tokens:
            return documents
        
        # Otherwise, include documents until we reach the token limit
        included_docs = []
        current_tokens = 0
        
        for doc, tokens in documents_with_tokens:
            if current_tokens + tokens <= available_tokens:
                included_docs.append(doc)
                current_tokens += tokens
            else:
                # If we can't include the full document, try to include a truncated version
                if tokens > 0:
                    content = doc.get(content_field, "")
                    remaining_tokens = available_tokens - current_tokens
                    
                    if remaining_tokens >= self.min_tokens:
                        # Truncate content to fit remaining tokens
                        truncated_content = self._truncate_text(content, remaining_tokens, token_counter)
                        
                        # Create truncated document
                        truncated_doc = doc.copy()
                        truncated_doc[content_field] = truncated_content
                        truncated_doc["truncated"] = True
                        
                        included_docs.append(truncated_doc)
                
                # Stop including documents
                break
        
        return included_docs
    
    def _calculate_query_complexity(self, query: str) -> float:
        """
        Calculate query complexity.
        
        Args:
            query: Query string
            
        Returns:
            Complexity score (0.5-1.0)
        """
        # Simple heuristics for query complexity
        
        # Length-based complexity
        length = len(query.split())
        length_score = min(1.0, length / 20)  # Normalize to 0-1
        
        # Structure-based complexity
        has_quotes = '"' in query
        has_operators = any(op in query.lower() for op in ["and", "or", "not"])
        has_special_chars = any(c in query for c in [":", "-", "+", "*"])
        
        structure_score = 0.5
        if has_quotes:
            structure_score += 0.1
        if has_operators:
            structure_score += 0.2
        if has_special_chars:
            structure_score += 0.2
        
        structure_score = min(1.0, structure_score)
        
        # Combine scores
        complexity = 0.5 + 0.5 * ((length_score + structure_score) / 2)
        
        return complexity
    
    def _calculate_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """
        Calculate cosine similarity between embeddings.
        
        Args:
            embedding1: First embedding
            embedding2: Second embedding
            
        Returns:
            Cosine similarity
        """
        # Convert to numpy arrays
        vec1 = np.array(embedding1)
        vec2 = np.array(embedding2)
        
        # Calculate cosine similarity
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)
    
    def _truncate_text(
        self,
        text: str,
        max_tokens: int,
        token_counter: Callable[[str], int]
    ) -> str:
        """
        Truncate text to fit within token limit.
        
        Args:
            text: Text to truncate
            max_tokens: Maximum number of tokens
            token_counter: Function to count tokens
            
        Returns:
            Truncated text
        """
        # If text is already within limit, return as is
        if token_counter(text) <= max_tokens:
            return text
        
        # Try to truncate at sentence boundaries
        try:
            sentences = nltk.sent_tokenize(text)
            
            truncated_text = ""
            for sentence in sentences:
                if token_counter(truncated_text + sentence) <= max_tokens:
                    truncated_text += sentence + " "
                else:
                    break
            
            return truncated_text.strip()
        
        except Exception:
            # Fallback to simple truncation
            words = text.split()
            truncated_words = []
            
            for word in words:
                if token_counter(" ".join(truncated_words + [word])) <= max_tokens:
                    truncated_words.append(word)
                else:
                    break
            
            return " ".join(truncated_words)


class ContextCompressor:
    """
    Context compressor implementation.
    
    This class provides functionality for compressing context
    to reduce token usage while preserving important information.
    """
    
    def __init__(
        self,
        compression_ratio: float = 0.7,
        embedding_model: Optional[str] = None,
        device: str = "cpu"
    ):
        """
        Initialize context compressor.
        
        Args:
            compression_ratio: Target compression ratio
            embedding_model: Name of embedding model to use
            device: Device to use for embedding model
        """
        self.compression_ratio = compression_ratio
        
        # Initialize embedding model if specified
        self.model = None
        if embedding_model:
            try:
                self.model = get_embedding_model(embedding_model, device=device)
                logger.info(f"Initialized embedding model: {embedding_model}")
            except Exception as e:
                logger.warning(f"Failed to initialize embedding model: {e}")
    
    @trace_function(name="context_compressor_compress")
    @measure_latency("context_compressor_compress")
    def compress(
        self,
        query: str,
        documents: List[Dict[str, Any]],
        content_field: str = "content"
    ) -> List[Dict[str, Any]]:
        """
        Compress context to reduce token usage.
        
        Args:
            query: Query string
            documents: List of documents
            content_field: Field containing document content
            
        Returns:
            Compressed list of documents
        """
        if not documents:
            return []
        
        # If embedding model is not available, use simple compression
        if self.model is None:
            return self._simple_compress(query, documents, content_field)
        
        # Get query embedding
        query_embedding = self.model.get_embedding(query)
        
        # Process each document
        compressed_docs = []
        
        for doc in documents:
            content = doc.get(content_field, "")
            
            if not content:
                compressed_docs.append(doc)
                continue
            
            # Split content into sentences
            try:
                sentences = nltk.sent_tokenize(content)
            except Exception:
                # Fallback to simple splitting
                sentences = re.split(r'(?<=[.!?])\s+', content)
            
            # If only one sentence, keep document as is
            if len(sentences) <= 1:
                compressed_docs.append(doc)
                continue
            
            # Get embeddings for each sentence
            sentence_embeddings = [self.model.get_embedding(sentence) for sentence in sentences]
            
            # Calculate relevance of each sentence to query
            relevance_scores = [
                self._calculate_similarity(query_embedding, embedding)
                for embedding in sentence_embeddings
            ]
            
            # Calculate information density of each sentence
            density_scores = self._calculate_information_density(sentences, sentence_embeddings)
            
            # Combine relevance and density scores
            combined_scores = [
                0.7 * relevance + 0.3 * density
                for relevance, density in zip(relevance_scores, density_scores)
            ]
            
            # Determine how many sentences to keep
            target_sentences = max(1, int(len(sentences) * self.compression_ratio))
            
            # Get indices of top sentences by score
            top_indices = sorted(
                range(len(sentences)),
                key=lambda i: combined_scores[i],
                reverse=True
            )[:target_sentences]
            
            # Sort indices to preserve original order
            top_indices.sort()
            
            # Create compressed content
            compressed_content = " ".join(sentences[i] for i in top_indices)
            
            # Create compressed document
            compressed_doc = doc.copy()
            compressed_doc[content_field] = compressed_content
            compressed_doc["compressed"] = True
            compressed_doc["compression_ratio"] = len(compressed_content) / len(content)
            
            compressed_docs.append(compressed_doc)
        
        return compressed_docs
    
    def _simple_compress(
        self,
        query: str,
        documents: List[Dict[str, Any]],
        content_field: str = "content"
    ) -> List[Dict[str, Any]]:
        """
        Simple compression without embeddings.
        
        Args:
            query: Query string
            documents: List of documents
            content_field: Field containing document content
            
        Returns:
            Compressed list of documents
        """
        # Extract query terms
        query_terms = set(query.lower().split())
        
        # Process each document
        compressed_docs = []
        
        for doc in documents:
            content = doc.get(content_field, "")
            
            if not content:
                compressed_docs.append(doc)
                continue
            
            # Split content into sentences
            try:
                sentences = nltk.sent_tokenize(content)
            except Exception:
                # Fallback to simple splitting
                sentences = re.split(r'(?<=[.!?])\s+', content)
            
            # If only one sentence, keep document as is
            if len(sentences) <= 1:
                compressed_docs.append(doc)
                continue
            
            # Calculate relevance of each sentence to query
            relevance_scores = []
            
            for sentence in sentences:
                # Count query terms in sentence
                sentence_terms = set(sentence.lower().split())
                term_overlap = len(query_terms.intersection(sentence_terms))
                
                # Calculate relevance score
                relevance = term_overlap / len(query_terms) if query_terms else 0
                relevance_scores.append(relevance)
            
            # Determine how many sentences to keep
            target_sentences = max(1, int(len(sentences) * self.compression_ratio))
            
            # Get indices of top sentences by relevance
            top_indices = sorted(
                range(len(sentences)),
                key=lambda i: relevance_scores[i],
                reverse=True
            )[:target_sentences]
            
            # Sort indices to preserve original order
            top_indices.sort()
            
            # Create compressed content
            compressed_content = " ".join(sentences[i] for i in top_indices)
            
            # Create compressed document
            compressed_doc = doc.copy()
            compressed_doc[content_field] = compressed_content
            compressed_doc["compressed"] = True
            compressed_doc["compression_ratio"] = len(compressed_content) / len(content)
            
            compressed_docs.append(compressed_doc)
        
        return compressed_docs
    
    def _calculate_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """
        Calculate cosine similarity between embeddings.
        
        Args:
            embedding1: First embedding
            embedding2: Second embedding
            
        Returns:
            Cosine similarity
        """
        # Convert to numpy arrays
        vec1 = np.array(embedding1)
        vec2 = np.array(embedding2)
        
        # Calculate cosine similarity
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)
    
    def _calculate_information_density(
        self,
        sentences: List[str],
        embeddings: List[List[float]]
    ) -> List[float]:
        """
        Calculate information density of sentences.
        
        Args:
            sentences: List of sentences
            embeddings: List of sentence embeddings
            
        Returns:
            List of information density scores
        """
        # Calculate average embedding
        avg_embedding = np.mean(embeddings, axis=0)
        
        # Calculate distance from average for each sentence
        distances = [
            1.0 - self._calculate_similarity(embedding, avg_embedding)
            for embedding in embeddings
        ]
        
        # Normalize distances
        max_distance = max(distances) if distances else 1.0
        normalized_distances = [d / max_distance if max_distance > 0 else 0.5 for d in distances]
        
        return normalized_distances


class ContextProcessor:
    """
    Context processor implementation.
    
    This class provides functionality for processing context,
    including dynamic context window and context compression.
    """
    
    def __init__(self, config: Optional[ContextProcessorConfig] = None):
        """
        Initialize context processor.
        
        Args:
            config: Configuration for context processor
        """
        self.config = config or ContextProcessorConfig()
        
        # Initialize dynamic context window
        self.dynamic_window = DynamicContextWindow(
            max_tokens=self.config.max_tokens,
            min_tokens=self.config.min_tokens,
            token_buffer=self.config.token_buffer,
            relevance_threshold=self.config.relevance_threshold,
            embedding_model=self.config.embedding_model,
            device=self.config.device
        )
        
        # Initialize context compressor
        self.compressor = ContextCompressor(
            compression_ratio=self.config.compression_ratio,
            embedding_model=self.config.embedding_model,
            device=self.config.device
        )
    
    @trace_function(name="context_processor_process")
    @measure_latency("context_processor_process")
    def process(
        self,
        query: str,
        documents: List[Dict[str, Any]],
        token_counter: Optional[Callable[[str], int]] = None,
        content_field: str = "content"
    ) -> List[Dict[str, Any]]:
        """
        Process context for a query.
        
        Args:
            query: Query string
            documents: List of documents
            token_counter: Function to count tokens in text
            content_field: Field containing document content
            
        Returns:
            Processed list of documents
        """
        if not documents:
            return []
        
        # Apply dynamic context window if enabled
        if self.config.use_dynamic_window:
            documents = self.dynamic_window.adjust(
                query=query,
                documents=documents,
                token_counter=token_counter,
                content_field=content_field
            )
        
        # Apply context compression if enabled
        if self.config.use_compression:
            documents = self.compressor.compress(
                query=query,
                documents=documents,
                content_field=content_field
            )
        
        return documents
