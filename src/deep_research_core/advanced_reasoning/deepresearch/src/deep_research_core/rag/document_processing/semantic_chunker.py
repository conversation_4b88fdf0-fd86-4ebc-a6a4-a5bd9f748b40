"""
Semantic chunker implementation for Deep Research Core.

This module provides functionality for chunking documents based on
semantic structure rather than fixed character counts.
"""

import re
import nltk
import logging
from typing import Dict, List, Any, Optional, Union, Tuple, Set
from dataclasses import dataclass

import numpy as np

from ...utils.structured_logging import get_logger
from ...utils.performance import trace_function, measure_latency
from ...models.embeddings.base import get_embedding_model

# Get logger
logger = get_logger(__name__)

# Try to download NLTK resources if not already available
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    try:
        nltk.download('punkt', quiet=True)
    except Exception as e:
        logger.warning(f"Failed to download NLTK punkt tokenizer: {e}")


@dataclass
class SemanticChunkerConfig:
    """Configuration for semantic chunker."""
    
    strategy: str = "hybrid"  # "paragraph", "section", "sentence", "hybrid"
    max_chunk_size: int = 1000
    min_chunk_size: int = 100
    chunk_overlap: int = 200
    respect_sections: bool = True
    respect_sentences: bool = True
    embedding_model: Optional[str] = None
    similarity_threshold: float = 0.7
    content_field: str = "content"
    preserve_metadata: bool = True
    language: str = "en"
    device: str = "cpu"


class SemanticChunker:
    """
    Semantic chunker implementation.
    
    This class provides functionality for chunking documents based on
    semantic structure rather than fixed character counts.
    """
    
    def __init__(self, config: Optional[SemanticChunkerConfig] = None):
        """
        Initialize semantic chunker.
        
        Args:
            config: Configuration for semantic chunker
        """
        self.config = config or SemanticChunkerConfig()
        
        # Initialize embedding model if specified
        self.model = None
        if self.config.embedding_model:
            try:
                self.model = get_embedding_model(
                    self.config.embedding_model,
                    device=self.config.device
                )
                logger.info(f"Initialized embedding model: {self.config.embedding_model}")
            except Exception as e:
                logger.warning(f"Failed to initialize embedding model: {e}")
        
        # Compile section header regex
        self.section_header_pattern = re.compile(
            r'^(?:#{1,6}|\*{1,3}|\={1,3}|\-{1,3}|\+{1,3})\s+(.+?)(?:\s+#{1,6}|\s+\*{1,3}|\s+\={1,3}|\s+\-{1,3}|\s+\+{1,3})?$',
            re.MULTILINE
        )
    
    @trace_function(name="semantic_chunker_chunk")
    @measure_latency("semantic_chunker_chunk")
    def chunk(
        self,
        document: Union[str, Dict[str, Any]],
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Chunk a document based on semantic structure.
        
        Args:
            document: Document to chunk (string or dictionary with content field)
            **kwargs: Additional arguments
            
        Returns:
            List of document chunks
        """
        # Convert string document to dictionary if needed
        if isinstance(document, str):
            document = {self.config.content_field: document}
        
        # Get document content
        content = document.get(self.config.content_field, "")
        
        # If content is empty, return document as is
        if not content:
            return [document]
        
        # If content is smaller than max_chunk_size, return as is
        if len(content) <= self.config.max_chunk_size:
            return [document]
        
        # Choose chunking method based on strategy
        if self.config.strategy == "paragraph":
            chunks = self._chunk_by_paragraph(content)
        elif self.config.strategy == "section":
            chunks = self._chunk_by_section(content)
        elif self.config.strategy == "sentence":
            chunks = self._chunk_by_sentence(content)
        elif self.config.strategy == "hybrid":
            chunks = self._chunk_hybrid(content)
        else:
            # Default to paragraph chunking
            chunks = self._chunk_by_paragraph(content)
        
        # Create chunk documents
        chunk_docs = []
        for i, chunk_text in enumerate(chunks):
            chunk_doc = {
                self.config.content_field: chunk_text,
                "chunk_id": i,
                "chunk_type": self.config.strategy,
                "chunk_size": len(chunk_text),
                "document_id": document.get("id", str(id(document)))
            }
            
            # Add metadata if needed
            if self.config.preserve_metadata:
                for key, value in document.items():
                    if key != self.config.content_field and key not in chunk_doc:
                        chunk_doc[key] = value
            
            chunk_docs.append(chunk_doc)
        
        return chunk_docs
    
    def _chunk_by_paragraph(self, content: str) -> List[str]:
        """
        Chunk content by paragraphs.
        
        Args:
            content: Content to chunk
            
        Returns:
            List of chunks
        """
        # Split into paragraphs
        paragraphs = self._split_into_paragraphs(content)
        
        # Group paragraphs into chunks
        chunks = []
        current_chunk = []
        current_size = 0
        
        for paragraph in paragraphs:
            paragraph_size = len(paragraph)
            
            # If paragraph is too large, split it further
            if paragraph_size > self.config.max_chunk_size:
                # If we have content in current_chunk, add it as a chunk
                if current_chunk:
                    chunks.append("\n\n".join(current_chunk))
                    current_chunk = []
                    current_size = 0
                
                # Split large paragraph into smaller chunks
                if self.config.respect_sentences:
                    # Split by sentences
                    sentences = self._split_into_sentences(paragraph)
                    sentence_chunks = self._group_by_size(
                        sentences,
                        self.config.max_chunk_size,
                        self.config.min_chunk_size,
                        separator=" "
                    )
                    chunks.extend(sentence_chunks)
                else:
                    # Split by fixed size
                    paragraph_chunks = self._split_fixed_size(
                        paragraph,
                        self.config.max_chunk_size,
                        self.config.chunk_overlap
                    )
                    chunks.extend(paragraph_chunks)
                
                continue
            
            # If adding this paragraph would exceed max_chunk_size, start a new chunk
            if current_size + paragraph_size > self.config.max_chunk_size and current_size >= self.config.min_chunk_size:
                chunks.append("\n\n".join(current_chunk))
                current_chunk = []
                current_size = 0
            
            # Add paragraph to current chunk
            current_chunk.append(paragraph)
            current_size += paragraph_size
        
        # Add final chunk if not empty
        if current_chunk:
            chunks.append("\n\n".join(current_chunk))
        
        return chunks
    
    def _chunk_by_section(self, content: str) -> List[str]:
        """
        Chunk content by sections.
        
        Args:
            content: Content to chunk
            
        Returns:
            List of chunks
        """
        # Split into sections
        sections = self._split_into_sections(content)
        
        # If no sections found, fall back to paragraph chunking
        if len(sections) <= 1:
            return self._chunk_by_paragraph(content)
        
        # Group sections into chunks
        chunks = []
        current_chunk = []
        current_size = 0
        
        for section in sections:
            section_size = len(section)
            
            # If section is too large, chunk it further
            if section_size > self.config.max_chunk_size:
                # If we have content in current_chunk, add it as a chunk
                if current_chunk:
                    chunks.append("\n\n".join(current_chunk))
                    current_chunk = []
                    current_size = 0
                
                # Chunk section by paragraphs
                section_chunks = self._chunk_by_paragraph(section)
                chunks.extend(section_chunks)
                
                continue
            
            # If adding this section would exceed max_chunk_size, start a new chunk
            if current_size + section_size > self.config.max_chunk_size and current_size >= self.config.min_chunk_size:
                chunks.append("\n\n".join(current_chunk))
                current_chunk = []
                current_size = 0
            
            # Add section to current chunk
            current_chunk.append(section)
            current_size += section_size
        
        # Add final chunk if not empty
        if current_chunk:
            chunks.append("\n\n".join(current_chunk))
        
        return chunks
    
    def _chunk_by_sentence(self, content: str) -> List[str]:
        """
        Chunk content by sentences.
        
        Args:
            content: Content to chunk
            
        Returns:
            List of chunks
        """
        # Split into sentences
        sentences = self._split_into_sentences(content)
        
        # Group sentences into chunks
        return self._group_by_size(
            sentences,
            self.config.max_chunk_size,
            self.config.min_chunk_size,
            separator=" "
        )
    
    def _chunk_hybrid(self, content: str) -> List[str]:
        """
        Chunk content using a hybrid approach.
        
        This method tries to respect document structure while ensuring
        chunks are of appropriate size.
        
        Args:
            content: Content to chunk
            
        Returns:
            List of chunks
        """
        # First try to split by sections
        if self.config.respect_sections:
            sections = self._split_into_sections(content)
            if len(sections) > 1:
                # Process each section
                all_chunks = []
                for section in sections:
                    # If section is small enough, keep it as is
                    if len(section) <= self.config.max_chunk_size:
                        all_chunks.append(section)
                    else:
                        # Otherwise, chunk it by paragraphs
                        section_chunks = self._chunk_by_paragraph(section)
                        all_chunks.extend(section_chunks)
                
                return all_chunks
        
        # If no sections or not respecting sections, try paragraphs
        return self._chunk_by_paragraph(content)
    
    def _split_into_paragraphs(self, text: str) -> List[str]:
        """
        Split text into paragraphs.
        
        Args:
            text: Text to split
            
        Returns:
            List of paragraphs
        """
        # Split by double newlines
        paragraphs = re.split(r'\n\s*\n', text)
        
        # Filter out empty paragraphs
        return [p.strip() for p in paragraphs if p.strip()]
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """
        Split text into sentences.
        
        Args:
            text: Text to split
            
        Returns:
            List of sentences
        """
        try:
            # Use NLTK for sentence tokenization
            return nltk.sent_tokenize(text, language=self.config.language)
        except Exception as e:
            logger.warning(f"Failed to tokenize sentences with NLTK: {e}")
            
            # Fallback to simple regex-based sentence splitting
            sentences = re.split(r'(?<=[.!?])\s+', text)
            return [s.strip() for s in sentences if s.strip()]
    
    def _split_into_sections(self, text: str) -> List[str]:
        """
        Split text into sections based on headers.
        
        Args:
            text: Text to split
            
        Returns:
            List of sections
        """
        # Find all section headers
        headers = list(self.section_header_pattern.finditer(text))
        
        # If no headers found, return the whole text as one section
        if not headers:
            return [text]
        
        # Split text into sections
        sections = []
        
        # Add content before first header if any
        if headers[0].start() > 0:
            sections.append(text[:headers[0].start()].strip())
        
        # Add each section
        for i in range(len(headers)):
            start = headers[i].start()
            end = headers[i+1].start() if i < len(headers) - 1 else len(text)
            sections.append(text[start:end].strip())
        
        return sections
    
    def _group_by_size(
        self,
        items: List[str],
        max_size: int,
        min_size: int,
        separator: str = "\n\n"
    ) -> List[str]:
        """
        Group items into chunks based on size constraints.
        
        Args:
            items: Items to group
            max_size: Maximum size of a chunk
            min_size: Minimum size of a chunk
            separator: Separator to use when joining items
            
        Returns:
            List of chunks
        """
        chunks = []
        current_chunk = []
        current_size = 0
        
        for item in items:
            item_size = len(item)
            separator_size = len(separator) if current_chunk else 0
            
            # If item is too large, split it further
            if item_size > max_size:
                # If we have content in current_chunk, add it as a chunk
                if current_chunk:
                    chunks.append(separator.join(current_chunk))
                    current_chunk = []
                    current_size = 0
                
                # Split large item into smaller chunks
                item_chunks = self._split_fixed_size(item, max_size, self.config.chunk_overlap)
                chunks.extend(item_chunks)
                
                continue
            
            # If adding this item would exceed max_size, start a new chunk
            if current_size + item_size + separator_size > max_size and current_size >= min_size:
                chunks.append(separator.join(current_chunk))
                current_chunk = []
                current_size = 0
                separator_size = 0
            
            # Add item to current chunk
            current_chunk.append(item)
            current_size += item_size + separator_size
        
        # Add final chunk if not empty
        if current_chunk:
            chunks.append(separator.join(current_chunk))
        
        return chunks
    
    def _split_fixed_size(
        self,
        text: str,
        chunk_size: int,
        chunk_overlap: int
    ) -> List[str]:
        """
        Split text into fixed-size chunks with overlap.
        
        Args:
            text: Text to split
            chunk_size: Size of each chunk
            chunk_overlap: Overlap between chunks
            
        Returns:
            List of chunks
        """
        # If text is smaller than chunk_size, return as is
        if len(text) <= chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            # Calculate end position
            end = start + chunk_size
            
            # If we're at the end of the text, just add the remaining text
            if end >= len(text):
                chunks.append(text[start:])
                break
            
            # Try to find a good split point (whitespace)
            split_point = self._find_split_point(text, end)
            
            # Add chunk
            chunks.append(text[start:split_point])
            
            # Update start position for next chunk, accounting for overlap
            start = split_point - chunk_overlap if split_point > chunk_overlap else split_point
        
        return chunks
    
    def _find_split_point(self, text: str, end: int) -> int:
        """
        Find a good split point near the end position.
        
        Args:
            text: Text to split
            end: End position
            
        Returns:
            Split point
        """
        # Look for whitespace within 10% of the end position
        search_range = int(self.config.max_chunk_size * 0.1)
        
        # Search for sentence boundaries first
        if self.config.respect_sentences:
            # Look for sentence boundaries (., !, ?)
            for i in range(end, max(end - search_range, 0), -1):
                if i < len(text) and i > 0:
                    if text[i-1] in ".!?" and (i == len(text) or text[i].isspace()):
                        return i
        
        # If no sentence boundary found, look for any whitespace
        for i in range(end, max(end - search_range, 0), -1):
            if i < len(text) and text[i].isspace():
                return i
        
        # If no good split point found, just split at the end position
        return min(end, len(text))
    
    def analyze_document(self, content: str) -> Dict[str, Any]:
        """
        Analyze document structure.
        
        Args:
            content: Document content
            
        Returns:
            Analysis results
        """
        # Count paragraphs
        paragraphs = self._split_into_paragraphs(content)
        num_paragraphs = len(paragraphs)
        
        # Count sentences
        sentences = self._split_into_sentences(content)
        num_sentences = len(sentences)
        
        # Count sections
        sections = self._split_into_sections(content)
        num_sections = len(sections)
        
        # Calculate average paragraph length
        avg_paragraph_length = sum(len(p) for p in paragraphs) / num_paragraphs if num_paragraphs > 0 else 0
        
        # Calculate average sentence length
        avg_sentence_length = sum(len(s) for s in sentences) / num_sentences if num_sentences > 0 else 0
        
        # Calculate average section length
        avg_section_length = sum(len(s) for s in sections) / num_sections if num_sections > 0 else 0
        
        # Determine recommended chunking strategy
        if num_sections > 1 and avg_section_length < self.config.max_chunk_size:
            recommended_strategy = "section"
        elif num_paragraphs > 1 and avg_paragraph_length < self.config.max_chunk_size:
            recommended_strategy = "paragraph"
        elif num_sentences > 1:
            recommended_strategy = "sentence"
        else:
            recommended_strategy = "fixed_size"
        
        return {
            "document_length": len(content),
            "num_paragraphs": num_paragraphs,
            "num_sentences": num_sentences,
            "num_sections": num_sections,
            "avg_paragraph_length": avg_paragraph_length,
            "avg_sentence_length": avg_sentence_length,
            "avg_section_length": avg_section_length,
            "recommended_strategy": recommended_strategy
        }
