"""
Contextual Compression for RAG (Retrieval-Augmented Generation).

This module provides functionality to compress retrieved context 
for more efficient utilization of LLM context windows.
"""

import re
from typing import Dict, Any, List, Optional, Union, Callable

import numpy as np

from ..utils.structured_logging import get_logger
from ..utils.text_processing import split_text, normalize_text
from ..utils.text_similarity import semantic_similarity, jaccard_similarity, text_overlap_ratio
from ..utils.performance_metrics import measure_latency
from ..utils.distributed_tracing import trace_function, span

# Create a logger
logger = get_logger(__name__)

class ContextualCompression:
    """
    Contextual Compression for RAG systems.
    
    This class provides methods to compress retrieved documents to optimize
    context window usage, including:
    - Redundancy removal
    - Semantic filtering
    - Query-focused compression
    - Length-based truncation
    """
    
    def __init__(
        self,
        max_tokens_per_doc: int = 500,
        overlap_threshold: float = 0.7,
        relevance_threshold: float = 0.6,
        compression_ratio: float = 0.8,
        use_semantic_similarity: bool = True,
        prioritize_query_relevant: bool = True,
        default_compressor: str = "auto",
        **kwargs
    ):
        """
        Initialize the ContextualCompression.
        
        Args:
            max_tokens_per_doc: Maximum tokens per document after compression
            overlap_threshold: Threshold for considering text as redundant
            relevance_threshold: Threshold for filtering by relevance
            compression_ratio: Target compression ratio (0.0-1.0)
            use_semantic_similarity: Whether to use semantic similarity
            prioritize_query_relevant: Whether to prioritize query-relevant segments
            default_compressor: Default compression method ('auto', 'semantic', 'redundancy', 'truncation')
            **kwargs: Additional implementation-specific arguments
        """
        self.max_tokens_per_doc = max_tokens_per_doc
        self.overlap_threshold = overlap_threshold
        self.relevance_threshold = relevance_threshold
        self.compression_ratio = compression_ratio
        self.use_semantic_similarity = use_semantic_similarity
        self.prioritize_query_relevant = prioritize_query_relevant
        self.default_compressor = default_compressor
        
        # Additional configuration
        self.keep_first_paragraph = kwargs.get("keep_first_paragraph", True)
        self.keep_conclusion = kwargs.get("keep_conclusion", True)
        self.token_estimator = kwargs.get("token_estimator", self._estimate_tokens)
        
        logger.info(f"Initialized ContextualCompression with compression_ratio={compression_ratio}")
    
    @trace_function(name="compress_documents")
    @measure_latency("compress_documents")
    def compress_documents(
        self,
        documents: List[Dict[str, Any]],
        query: str,
        method: Optional[str] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Compress a list of documents based on the query.
        
        Args:
            documents: List of documents to compress
            query: The query used for retrieval
            method: Compression method ('auto', 'semantic', 'redundancy', 'truncation')
            **kwargs: Additional compression parameters
            
        Returns:
            List of compressed documents
        """
        if not documents:
            return []
        
        method = method or self.default_compressor
        
        with span("compress_documents"):
            # Copy documents to avoid modifying the originals
            compressed_docs = []
            
            # Process documents based on method
            if method == "auto":
                # Automatically select the best method based on documents and query
                compressed_docs = self._auto_compress(documents, query, **kwargs)
            elif method == "semantic":
                # Compress based on semantic relevance to query
                compressed_docs = self._semantic_compress(documents, query, **kwargs)
            elif method == "redundancy":
                # Remove redundant information across documents
                compressed_docs = self._redundancy_compress(documents, query, **kwargs)
            elif method == "truncation":
                # Simple length-based truncation
                compressed_docs = self._truncation_compress(documents, query, **kwargs)
            else:
                logger.warning(f"Unknown compression method: {method}, using auto")
                compressed_docs = self._auto_compress(documents, query, **kwargs)
            
            # Add compression stats to each document
            for orig_doc, comp_doc in zip(documents, compressed_docs):
                orig_tokens = self.token_estimator(orig_doc.get("content", ""))
                comp_tokens = self.token_estimator(comp_doc.get("content", ""))
                
                compression_rate = 1.0 - (comp_tokens / orig_tokens) if orig_tokens > 0 else 0.0
                comp_doc["compression_stats"] = {
                    "original_tokens": orig_tokens,
                    "compressed_tokens": comp_tokens,
                    "compression_rate": compression_rate,
                    "method": method
                }
            
            return compressed_docs
    
    def _auto_compress(
        self, 
        documents: List[Dict[str, Any]], 
        query: str,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Automatically select and apply the best compression method.
        
        Args:
            documents: List of documents to compress
            query: The query used for retrieval
            **kwargs: Additional compression parameters
            
        Returns:
            List of compressed documents
        """
        total_tokens = sum(self.token_estimator(doc.get("content", "")) for doc in documents)
        avg_similarity = self._calculate_avg_similarity(documents)
        
        # Choose strategy based on document characteristics
        if avg_similarity > self.overlap_threshold:
            # High redundancy - prioritize redundancy removal
            logger.info(f"Auto-selecting redundancy compression (avg similarity: {avg_similarity:.2f})")
            return self._redundancy_compress(documents, query, **kwargs)
        elif total_tokens > (len(documents) * self.max_tokens_per_doc * 1.5):
            # Very long documents - use semantic compression
            logger.info(f"Auto-selecting semantic compression (total tokens: {total_tokens})")
            return self._semantic_compress(documents, query, **kwargs)
        else:
            # Default to truncation for shorter documents
            logger.info(f"Auto-selecting truncation compression (total tokens: {total_tokens})")
            return self._truncation_compress(documents, query, **kwargs)
    
    def _semantic_compress(
        self, 
        documents: List[Dict[str, Any]], 
        query: str,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Compress documents by keeping semantically relevant parts.
        
        Args:
            documents: List of documents to compress
            query: The query used for retrieval
            **kwargs: Additional compression parameters
            
        Returns:
            List of compressed documents
        """
        compressed_docs = []
        
        for doc in documents:
            content = doc.get("content", "")
            if not content.strip():
                compressed_docs.append(doc.copy())
                continue
            
            # Split document into paragraphs/segments
            segments = re.split(r'\n\s*\n', content)
            
            # Calculate similarity of each segment with the query
            segment_scores = []
            for segment in segments:
                if not segment.strip():
                    continue
                
                score = semantic_similarity(query, segment)
                segment_scores.append((segment, score))
            
            # Sort segments by relevance score (descending)
            segment_scores.sort(key=lambda x: x[1], reverse=True)
            
            # Keep segments above relevance threshold or up to max tokens
            kept_segments = []
            total_tokens = 0
            
            # Always keep first paragraph if configured
            if self.keep_first_paragraph and segments:
                first_segment = segments[0]
                kept_segments.append(first_segment)
                total_tokens += self.token_estimator(first_segment)
                # Remove from segments to avoid duplication
                segment_scores = [(s, score) for s, score in segment_scores if s != first_segment]
            
            # Add relevant segments until max tokens is reached
            for segment, score in segment_scores:
                if score < self.relevance_threshold:
                    # Skip segments below threshold
                    continue
                    
                segment_tokens = self.token_estimator(segment)
                if total_tokens + segment_tokens <= self.max_tokens_per_doc:
                    kept_segments.append(segment)
                    total_tokens += segment_tokens
                else:
                    # Try to fit a portion of the segment if it's valuable
                    if score > self.relevance_threshold + 0.2 and segment_tokens > 50:
                        # Extract a portion that would fit
                        remaining_tokens = self.max_tokens_per_doc - total_tokens
                        if remaining_tokens > 30:  # Only if we can add something meaningful
                            partial_segment = segment[:int(len(segment) * (remaining_tokens / segment_tokens))]
                            # Ensure it ends at sentence boundary
                            last_period = partial_segment.rfind('.')
                            if last_period > 0:
                                partial_segment = partial_segment[:last_period+1]
                                kept_segments.append(partial_segment)
                                total_tokens += self.token_estimator(partial_segment)
                    break
            
            # Create compressed document
            compressed_content = "\n\n".join(kept_segments)
            compressed_doc = doc.copy()
            compressed_doc["content"] = compressed_content
            compressed_docs.append(compressed_doc)
        
        return compressed_docs
    
    def _redundancy_compress(
        self, 
        documents: List[Dict[str, Any]], 
        query: str,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Compress documents by removing redundant information across documents.
        
        Args:
            documents: List of documents to compress
            query: The query used for retrieval
            **kwargs: Additional compression parameters
            
        Returns:
            List of compressed documents
        """
        if not documents:
            return []
        
        # First, rank documents by relevance to query
        doc_rankings = []
        for i, doc in enumerate(documents):
            content = doc.get("content", "")
            score = doc.get("score", 0)
            # Adjust score by similarity if available, otherwise use the retrieved score
            if self.use_semantic_similarity:
                query_similarity = semantic_similarity(query, content)
                adjusted_score = (score + query_similarity) / 2
            else:
                adjusted_score = score
            doc_rankings.append((i, adjusted_score))
        
        # Sort documents by adjusted score (descending)
        doc_rankings.sort(key=lambda x: x[1], reverse=True)
        
        # Process documents in order of relevance
        seen_segments = set()
        compressed_docs = []
        
        for i in range(len(documents)):
            idx = doc_rankings[i][0]
            doc = documents[idx]
            content = doc.get("content", "")
            
            # Split into segments (paragraphs)
            segments = re.split(r'\n\s*\n', content)
            
            # Filter out redundant segments
            unique_segments = []
            for segment in segments:
                if not segment.strip():
                    continue
                
                # Check if segment is redundant with any previously seen segment
                is_redundant = False
                for seen in seen_segments:
                    if (jaccard_similarity(segment, seen) > self.overlap_threshold or 
                        text_overlap_ratio(segment, seen) > self.overlap_threshold):
                        is_redundant = True
                        break
                
                if not is_redundant:
                    unique_segments.append(segment)
                    seen_segments.add(segment)
            
            # Limit by token count
            kept_segments = []
            total_tokens = 0
            
            for segment in unique_segments:
                segment_tokens = self.token_estimator(segment)
                if total_tokens + segment_tokens <= self.max_tokens_per_doc:
                    kept_segments.append(segment)
                    total_tokens += segment_tokens
                else:
                    break
            
            # Create compressed document
            compressed_content = "\n\n".join(kept_segments)
            compressed_doc = doc.copy()
            compressed_doc["content"] = compressed_content
            compressed_docs.append(compressed_doc)
        
        # Restore original document order
        orig_order_docs = [None] * len(documents)
        for i, (idx, _) in enumerate(doc_rankings):
            if i < len(compressed_docs):
                orig_order_docs[idx] = compressed_docs[i]
        
        # Fill any gaps (shouldn't happen, but just in case)
        for i in range(len(orig_order_docs)):
            if orig_order_docs[i] is None:
                orig_order_docs[i] = documents[i].copy()
        
        return orig_order_docs
    
    def _truncation_compress(
        self, 
        documents: List[Dict[str, Any]], 
        query: str,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Compress documents by simple truncation.
        
        Args:
            documents: List of documents to compress
            query: The query used for retrieval
            **kwargs: Additional compression parameters
            
        Returns:
            List of compressed documents
        """
        compressed_docs = []
        
        for doc in documents:
            content = doc.get("content", "")
            
            # Calculate token count
            token_count = self.token_estimator(content)
            
            if token_count <= self.max_tokens_per_doc:
                # No compression needed
                compressed_docs.append(doc.copy())
                continue
            
            # Truncate content to fit max tokens
            # Start with simple truncation at character level
            approx_chars_per_token = len(content) / token_count
            max_chars = int(self.max_tokens_per_doc * approx_chars_per_token)
            
            truncated_content = content[:max_chars]
            
            # Ensure it ends at a sentence boundary
            last_period = truncated_content.rfind('.')
            if last_period > max_chars * 0.7:  # Only if the period is not too far back
                truncated_content = truncated_content[:last_period+1]
            
            # Create compressed document
            compressed_doc = doc.copy()
            compressed_doc["content"] = truncated_content
            compressed_docs.append(compressed_doc)
        
        return compressed_docs
    
    def _calculate_avg_similarity(self, documents: List[Dict[str, Any]]) -> float:
        """
        Calculate average similarity between documents.
        
        Args:
            documents: List of documents
            
        Returns:
            Average similarity score
        """
        if len(documents) <= 1:
            return 0.0
        
        total_similarity = 0.0
        comparison_count = 0
        
        for i in range(len(documents)):
            for j in range(i+1, len(documents)):
                content_i = documents[i].get("content", "")
                content_j = documents[j].get("content", "")
                
                similarity = jaccard_similarity(content_i, content_j)
                total_similarity += similarity
                comparison_count += 1
        
        return total_similarity / comparison_count if comparison_count > 0 else 0.0
    
    def _estimate_tokens(self, text: str) -> int:
        """
        Estimate the number of tokens in a text.
        
        Args:
            text: Input text
            
        Returns:
            Estimated token count
        """
        # Simple estimation: 1 token ≈ 4 characters for English
        return len(text) // 4 + 1 