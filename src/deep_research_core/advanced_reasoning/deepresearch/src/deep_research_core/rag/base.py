"""
Base class for RAG (Retrieval-Augmented Generation) systems.

This module defines the abstract base class for all RAG systems,
providing a common interface for different implementations.
"""

import abc
from typing import Dict, Any, List, Optional, Union

from ..utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

class BaseRAG(abc.ABC):
    """
    Abstract base class for all RAG systems.
    
    This class defines the common interface that all RAG systems should follow,
    making it easier to switch between different implementations or add new ones.
    """
    
    def __init__(self, **kwargs):
        """
        Initialize the BaseRAG.
        
        Args:
            **kwargs: Additional implementation-specific arguments
        """
        pass
    
    @abc.abstractmethod
    def query(
        self,
        query: str,
        num_results: int = 5,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Query the RAG system.
        
        Args:
            query: The query to search for
            num_results: Number of results to return
            **kwargs: Additional implementation-specific arguments
            
        Returns:
            List of dictionaries containing the retrieved documents
        """
        pass
    
    @abc.abstractmethod
    def add_document(
        self,
        content: str,
        metadata: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> str:
        """
        Add a document to the RAG system.
        
        Args:
            content: The content of the document
            metadata: Optional metadata for the document
            **kwargs: Additional implementation-specific arguments
            
        Returns:
            ID of the added document
        """
        pass
    
    @abc.abstractmethod
    def delete_document(
        self,
        document_id: str,
        **kwargs
    ) -> bool:
        """
        Delete a document from the RAG system.
        
        Args:
            document_id: ID of the document to delete
            **kwargs: Additional implementation-specific arguments
            
        Returns:
            True if successful, False otherwise
        """
        pass
    
    @abc.abstractmethod
    def clear(self) -> bool:
        """
        Clear all documents from the RAG system.
        
        Returns:
            True if successful, False otherwise
        """
        pass
