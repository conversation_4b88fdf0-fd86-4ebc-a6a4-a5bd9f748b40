"""
RAG (Retrieval-Augmented Generation) module.

This module provides implementations of RAG systems,
which combine retrieval and generation to improve the quality of generated text.
"""

from .base import BaseRAG
from .contextual_compression import ContextualCompression
from .error_recovery_rag import ErrorRecoveryRAG

# Import search implementations
from .search import (
    KeywordSearchEngine,
    BM25SearchEngine,
    TFIDFSearchEngine,
    SimpleSearchEngine,
    create_search_engine,
    HybridSearch
)

# Import document processing
from .document_processing import (
    SemanticChunker,
    SemanticChunkerConfig,
    ContextProcessor,
    ContextProcessorConfig,
    DynamicContextWindow,
    ContextCompressor
)

__all__ = [
    # Base classes
    "BaseRAG",
    "ContextualCompression",
    "ErrorRecoveryRAG",

    # Search implementations
    "KeywordSearchEngine",
    "BM25SearchEngine",
    "TFIDFSearchEngine",
    "SimpleSearchEngine",
    "create_search_engine",
    "HybridSearch",

    # Document processing
    "SemanticChunker",
    "SemanticChunkerConfig",
    "ContextProcessor",
    "ContextProcessorConfig",
    "DynamicContextWindow",
    "ContextCompressor"
]
