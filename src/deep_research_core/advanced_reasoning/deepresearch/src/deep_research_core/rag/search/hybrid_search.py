"""
Hybrid search implementation for Deep Research Core.

This module provides hybrid search functionality that combines
semantic search and keyword search for improved retrieval.
"""

import time
from typing import Dict, List, Any, Optional, Set, Tuple, Union, Callable

import numpy as np

from ...utils.structured_logging import get_logger
from ...utils.performance import trace_function, measure_latency
from ...models.embeddings.base import get_embedding_model
from .keyword_search import KeywordSearchEngine, create_search_engine

# Get logger
logger = get_logger(__name__)


class HybridSearch:
    """
    Hybrid search implementation.
    
    This class combines semantic search and keyword search for improved retrieval.
    """
    
    def __init__(
        self,
        embedding_model: Optional[str] = None,
        keyword_method: str = "bm25",
        language: str = "en",
        semantic_weight: float = 0.7,
        keyword_weight: float = 0.3,
        use_cache: bool = True,
        cache_size: int = 1000
    ):
        """
        Initialize hybrid search.
        
        Args:
            embedding_model: Name of the embedding model to use
            keyword_method: Keyword search method ("bm25", "tfidf", "simple")
            language: Language for text processing
            semantic_weight: Weight for semantic search (0-1)
            keyword_weight: Weight for keyword search (0-1)
            use_cache: Whether to use embedding cache
            cache_size: Size of embedding cache
        """
        self.embedding_model = embedding_model
        self.keyword_method = keyword_method
        self.language = language
        self.semantic_weight = semantic_weight
        self.keyword_weight = keyword_weight
        self.use_cache = use_cache
        self.cache_size = cache_size
        
        # Initialize embedding model
        self._init_embedding_model()
        
        # Initialize keyword search engine
        self.keyword_search = create_search_engine(
            method=keyword_method,
            language=language
        )
        
        # Initialize document store
        self.documents = []
        self.document_embeddings = []
        
        # Initialize embedding cache
        self.embedding_cache = {}
    
    def _init_embedding_model(self):
        """Initialize embedding model."""
        try:
            self.embedding_model_instance = get_embedding_model(self.embedding_model)
            logger.info(f"Initialized embedding model: {self.embedding_model}")
        except Exception as e:
            logger.error(f"Failed to initialize embedding model: {e}")
            self.embedding_model_instance = None
    
    @trace_function(name="hybrid_search_add_document")
    @measure_latency("hybrid_search_add_document")
    def add_document(self, document: Dict[str, Any]) -> int:
        """
        Add a document to the search index.
        
        Args:
            document: Document to add
            
        Returns:
            Document ID
        """
        # Get document content
        content = document.get("content", "")
        if not content:
            logger.warning("Document has no content, skipping")
            return -1
        
        # Add document to list
        doc_id = len(self.documents)
        self.documents.append(document)
        
        # Get document embedding
        embedding = self._get_embedding(content)
        self.document_embeddings.append(embedding)
        
        # Add document to keyword search engine
        self.keyword_search.add_document(document, doc_id)
        
        return doc_id
    
    @trace_function(name="hybrid_search_add_documents")
    @measure_latency("hybrid_search_add_documents")
    def add_documents(self, documents: List[Dict[str, Any]]) -> List[int]:
        """
        Add multiple documents to the search index.
        
        Args:
            documents: List of documents to add
            
        Returns:
            List of document IDs
        """
        doc_ids = []
        
        for document in documents:
            doc_id = self.add_document(document)
            doc_ids.append(doc_id)
        
        return doc_ids
    
    @trace_function(name="hybrid_search_search")
    @measure_latency("hybrid_search_search")
    def search(
        self,
        query: str,
        top_k: int = 5,
        semantic_weight: Optional[float] = None,
        keyword_weight: Optional[float] = None,
        filter_func: Optional[Callable[[Dict[str, Any]], bool]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for documents matching the query.
        
        Args:
            query: Query string
            top_k: Number of results to return
            semantic_weight: Weight for semantic search (0-1)
            keyword_weight: Weight for keyword search (0-1)
            filter_func: Optional function to filter documents
            
        Returns:
            List of documents with scores
        """
        # Use default weights if not provided
        semantic_weight = semantic_weight if semantic_weight is not None else self.semantic_weight
        keyword_weight = keyword_weight if keyword_weight is not None else self.keyword_weight
        
        # Normalize weights
        total_weight = semantic_weight + keyword_weight
        if total_weight == 0:
            logger.warning("Both semantic_weight and keyword_weight are 0, using default weights")
            semantic_weight = 0.7
            keyword_weight = 0.3
            total_weight = 1.0
        
        normalized_semantic_weight = semantic_weight / total_weight
        normalized_keyword_weight = keyword_weight / total_weight
        
        # Get query embedding
        query_embedding = self._get_embedding(query)
        
        # Perform semantic search
        semantic_results = self._semantic_search(query_embedding, top_k * 2, filter_func)
        
        # Perform keyword search
        keyword_results = self._keyword_search(query, top_k * 2, filter_func)
        
        # Combine results
        combined_results = self._combine_results(
            semantic_results,
            keyword_results,
            top_k,
            normalized_semantic_weight,
            normalized_keyword_weight
        )
        
        return combined_results
    
    @trace_function(name="hybrid_search_get_embedding")
    @measure_latency("hybrid_search_get_embedding")
    def _get_embedding(self, text: str) -> List[float]:
        """
        Get embedding for text.
        
        Args:
            text: Text to get embedding for
            
        Returns:
            Embedding vector
        """
        # Check cache first
        if self.use_cache and text in self.embedding_cache:
            return self.embedding_cache[text]
        
        # Get embedding from model
        if self.embedding_model_instance is not None:
            try:
                embedding = self.embedding_model_instance.get_embedding(text)
                
                # Add to cache
                if self.use_cache:
                    # Limit cache size
                    if len(self.embedding_cache) >= self.cache_size:
                        # Remove random item
                        self.embedding_cache.pop(next(iter(self.embedding_cache)))
                    
                    self.embedding_cache[text] = embedding
                
                return embedding
            except Exception as e:
                logger.error(f"Failed to get embedding: {e}")
        
        # Fallback to random embedding
        logger.warning("Using random embedding as fallback")
        return list(np.random.rand(768))
    
    @trace_function(name="hybrid_search_semantic_search")
    @measure_latency("hybrid_search_semantic_search")
    def _semantic_search(
        self,
        query_embedding: List[float],
        top_k: int,
        filter_func: Optional[Callable[[Dict[str, Any]], bool]] = None
    ) -> List[Dict[str, Any]]:
        """
        Perform semantic search.
        
        Args:
            query_embedding: Query embedding
            top_k: Number of results to return
            filter_func: Optional function to filter documents
            
        Returns:
            List of documents with scores
        """
        if not self.documents:
            return []
        
        # Convert embeddings to numpy arrays
        query_embedding_np = np.array(query_embedding)
        document_embeddings_np = np.array(self.document_embeddings)
        
        # Calculate cosine similarity
        similarities = np.dot(document_embeddings_np, query_embedding_np) / (
            np.linalg.norm(document_embeddings_np, axis=1) * np.linalg.norm(query_embedding_np)
        )
        
        # Get top-k indices
        if filter_func is not None:
            # Apply filter
            filtered_indices = [i for i, doc in enumerate(self.documents) if filter_func(doc)]
            filtered_similarities = [(i, similarities[i]) for i in filtered_indices]
            top_indices = sorted(filtered_similarities, key=lambda x: x[1], reverse=True)[:top_k]
        else:
            # Get top-k without filtering
            top_indices = sorted(enumerate(similarities), key=lambda x: x[1], reverse=True)[:top_k]
        
        # Create results
        results = []
        for i, score in top_indices:
            document = self.documents[i].copy()
            document["score"] = float(score)
            results.append(document)
        
        return results
    
    @trace_function(name="hybrid_search_keyword_search")
    @measure_latency("hybrid_search_keyword_search")
    def _keyword_search(
        self,
        query: str,
        top_k: int,
        filter_func: Optional[Callable[[Dict[str, Any]], bool]] = None
    ) -> List[Dict[str, Any]]:
        """
        Perform keyword search.
        
        Args:
            query: Query string
            top_k: Number of results to return
            filter_func: Optional function to filter documents
            
        Returns:
            List of documents with scores
        """
        return self.keyword_search.search(query, top_k, filter_func)
    
    @trace_function(name="hybrid_search_combine_results")
    @measure_latency("hybrid_search_combine_results")
    def _combine_results(
        self,
        semantic_results: List[Dict[str, Any]],
        keyword_results: List[Dict[str, Any]],
        top_k: int,
        semantic_weight: float,
        keyword_weight: float
    ) -> List[Dict[str, Any]]:
        """
        Combine semantic and keyword search results.
        
        Args:
            semantic_results: Semantic search results
            keyword_results: Keyword search results
            top_k: Number of results to return
            semantic_weight: Weight for semantic search
            keyword_weight: Weight for keyword search
            
        Returns:
            Combined results
        """
        # Create a dictionary to store combined scores
        combined_scores = {}
        
        # Add semantic search scores
        for result in semantic_results:
            doc_id = result.get("id", result.get("_id", None))
            if doc_id is None:
                # If no ID, use content as key
                doc_id = result.get("content", "")[:100]
            
            combined_scores[doc_id] = {
                "document": result,
                "semantic_score": result.get("score", 0.0) * semantic_weight,
                "keyword_score": 0.0
            }
        
        # Add keyword search scores
        for result in keyword_results:
            doc_id = result.get("id", result.get("_id", None))
            if doc_id is None:
                # If no ID, use content as key
                doc_id = result.get("content", "")[:100]
            
            if doc_id in combined_scores:
                # Update existing entry
                combined_scores[doc_id]["keyword_score"] = result.get("score", 0.0) * keyword_weight
            else:
                # Add new entry
                combined_scores[doc_id] = {
                    "document": result,
                    "semantic_score": 0.0,
                    "keyword_score": result.get("score", 0.0) * keyword_weight
                }
        
        # Calculate total scores
        for doc_id, data in combined_scores.items():
            data["total_score"] = data["semantic_score"] + data["keyword_score"]
        
        # Sort by total score
        sorted_results = sorted(
            combined_scores.values(),
            key=lambda x: x["total_score"],
            reverse=True
        )
        
        # Create final results
        results = []
        for data in sorted_results[:top_k]:
            document = data["document"].copy()
            document["score"] = data["total_score"]
            document["semantic_score"] = data["semantic_score"]
            document["keyword_score"] = data["keyword_score"]
            results.append(document)
        
        return results
    
    def clear(self):
        """Clear the search index."""
        self.documents = []
        self.document_embeddings = []
        self.keyword_search.clear()
        self.embedding_cache = {}
