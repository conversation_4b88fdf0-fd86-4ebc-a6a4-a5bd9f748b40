"""
Keyword search implementation for Deep Research Core.

This module provides keyword search functionality using various algorithms
such as BM25, TF-IDF, and simple keyword matching.
"""

import re
import math
import string
from typing import Dict, List, Any, Optional, Set, Tuple, Union, Callable
from collections import Counter, defaultdict

import numpy as np

from ...utils.structured_logging import get_logger
from ...utils.text_processing import preprocess_text, tokenize_text, remove_stopwords

# Get logger
logger = get_logger(__name__)


class KeywordSearchEngine:
    """
    Keyword search engine implementation.
    
    This class provides keyword search functionality using various algorithms
    such as BM25, TF-IDF, and simple keyword matching.
    """
    
    def __init__(
        self,
        method: str = "bm25",
        language: str = "en",
        case_sensitive: bool = False,
        remove_punctuation: bool = True,
        remove_stop_words: bool = True,
        stemming: bool = True,
        k1: float = 1.5,
        b: float = 0.75
    ):
        """
        Initialize keyword search engine.
        
        Args:
            method: Search method to use ("bm25", "tfidf", "simple")
            language: Language for text processing
            case_sensitive: Whether search should be case sensitive
            remove_punctuation: Whether to remove punctuation
            remove_stop_words: Whether to remove stop words
            stemming: Whether to apply stemming
            k1: BM25 parameter for term frequency scaling
            b: BM25 parameter for document length normalization
        """
        self.method = method.lower()
        self.language = language
        self.case_sensitive = case_sensitive
        self.remove_punctuation = remove_punctuation
        self.remove_stop_words = remove_stop_words
        self.stemming = stemming
        self.k1 = k1
        self.b = b
        
        # Initialize index
        self.documents = []
        self.document_tokens = []
        self.document_lengths = []
        self.avg_document_length = 0
        self.term_document_freq = defaultdict(list)  # term -> [(doc_id, freq), ...]
        self.document_freq = defaultdict(int)  # term -> number of documents containing term
        self.idf = {}  # term -> inverse document frequency
        
        # Validate method
        if self.method not in ["bm25", "tfidf", "simple"]:
            logger.warning(f"Unknown search method: {self.method}. Using BM25 instead.")
            self.method = "bm25"
    
    def add_document(self, document: Dict[str, Any], doc_id: Optional[int] = None) -> int:
        """
        Add a document to the search index.
        
        Args:
            document: Document to add
            doc_id: Optional document ID (if None, will be assigned automatically)
            
        Returns:
            Document ID
        """
        # Get document content
        content = document.get("content", "")
        if not content:
            logger.warning("Document has no content, skipping")
            return -1
        
        # Assign document ID if not provided
        if doc_id is None:
            doc_id = len(self.documents)
        
        # Add document to index
        self.documents.append(document)
        
        # Tokenize document
        tokens = self._tokenize_text(content)
        self.document_tokens.append(tokens)
        
        # Update document length
        doc_length = len(tokens)
        self.document_lengths.append(doc_length)
        
        # Update average document length
        total_length = sum(self.document_lengths)
        self.avg_document_length = total_length / len(self.documents) if self.documents else 0
        
        # Update term frequencies
        term_freq = Counter(tokens)
        
        for term, freq in term_freq.items():
            # Update term-document frequency
            self.term_document_freq[term].append((doc_id, freq))
            
            # Update document frequency
            self.document_freq[term] += 1
        
        # Update inverse document frequencies
        self._update_idf()
        
        return doc_id
    
    def add_documents(self, documents: List[Dict[str, Any]]) -> List[int]:
        """
        Add multiple documents to the search index.
        
        Args:
            documents: List of documents to add
            
        Returns:
            List of document IDs
        """
        doc_ids = []
        
        for document in documents:
            doc_id = self.add_document(document)
            doc_ids.append(doc_id)
        
        return doc_ids
    
    def search(
        self,
        query: str,
        top_k: int = 5,
        filter_func: Optional[Callable[[Dict[str, Any]], bool]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for documents matching the query.
        
        Args:
            query: Query string
            top_k: Number of results to return
            filter_func: Optional function to filter documents
            
        Returns:
            List of documents with scores
        """
        # Tokenize query
        query_tokens = self._tokenize_text(query)
        
        if not query_tokens:
            logger.warning("Query has no tokens after processing")
            return []
        
        # Calculate scores based on method
        if self.method == "bm25":
            scores = self._bm25_search(query_tokens)
        elif self.method == "tfidf":
            scores = self._tfidf_search(query_tokens)
        else:  # simple
            scores = self._simple_search(query_tokens)
        
        # Sort documents by score
        doc_scores = [(i, score) for i, score in enumerate(scores) if score > 0]
        doc_scores.sort(key=lambda x: x[1], reverse=True)
        
        # Filter documents if filter function is provided
        if filter_func is not None:
            doc_scores = [(i, score) for i, score in doc_scores if filter_func(self.documents[i])]
        
        # Get top-k results
        results = []
        for i, score in doc_scores[:top_k]:
            document = self.documents[i].copy()
            document["score"] = score
            results.append(document)
        
        return results
    
    def _tokenize_text(self, text: str) -> List[str]:
        """
        Tokenize text based on configuration.
        
        Args:
            text: Text to tokenize
            
        Returns:
            List of tokens
        """
        # Preprocess text
        processed_text = preprocess_text(
            text,
            language=self.language,
            case_sensitive=self.case_sensitive,
            remove_punctuation=self.remove_punctuation
        )
        
        # Tokenize text
        tokens = tokenize_text(processed_text, language=self.language)
        
        # Remove stop words if enabled
        if self.remove_stop_words:
            tokens = remove_stopwords(tokens, language=self.language)
        
        # Apply stemming if enabled
        if self.stemming:
            try:
                from nltk.stem import SnowballStemmer
                stemmer = SnowballStemmer(self.language)
                tokens = [stemmer.stem(token) for token in tokens]
            except (ImportError, ValueError):
                # If NLTK is not available or language is not supported, skip stemming
                pass
        
        return tokens
    
    def _update_idf(self):
        """Update inverse document frequencies."""
        num_docs = len(self.documents)
        
        for term, doc_freq in self.document_freq.items():
            # Calculate IDF
            if self.method == "bm25":
                # BM25 IDF formula
                self.idf[term] = math.log((num_docs - doc_freq + 0.5) / (doc_freq + 0.5) + 1.0)
            else:
                # Standard IDF formula
                self.idf[term] = math.log(num_docs / (doc_freq + 1.0)) + 1.0
    
    def _bm25_search(self, query_tokens: List[str]) -> List[float]:
        """
        Perform BM25 search.
        
        Args:
            query_tokens: Tokenized query
            
        Returns:
            List of scores for each document
        """
        num_docs = len(self.documents)
        scores = [0.0] * num_docs
        
        # Calculate query term frequencies
        query_term_freq = Counter(query_tokens)
        
        # Calculate BM25 scores
        for term, query_freq in query_term_freq.items():
            if term not in self.term_document_freq:
                continue
            
            # Get IDF for term
            idf = self.idf.get(term, 0.0)
            
            # Calculate score contribution for each document containing the term
            for doc_id, term_freq in self.term_document_freq[term]:
                # Get document length
                doc_length = self.document_lengths[doc_id]
                
                # Calculate BM25 score for this term
                numerator = term_freq * (self.k1 + 1)
                denominator = term_freq + self.k1 * (1 - self.b + self.b * doc_length / self.avg_document_length)
                scores[doc_id] += idf * numerator / denominator
        
        return scores
    
    def _tfidf_search(self, query_tokens: List[str]) -> List[float]:
        """
        Perform TF-IDF search.
        
        Args:
            query_tokens: Tokenized query
            
        Returns:
            List of scores for each document
        """
        num_docs = len(self.documents)
        scores = [0.0] * num_docs
        
        # Calculate query term frequencies
        query_term_freq = Counter(query_tokens)
        
        # Calculate TF-IDF scores
        for term, query_freq in query_term_freq.items():
            if term not in self.term_document_freq:
                continue
            
            # Get IDF for term
            idf = self.idf.get(term, 0.0)
            
            # Calculate score contribution for each document containing the term
            for doc_id, term_freq in self.term_document_freq[term]:
                # Calculate TF-IDF score for this term
                tf = term_freq / self.document_lengths[doc_id]
                scores[doc_id] += tf * idf
        
        return scores
    
    def _simple_search(self, query_tokens: List[str]) -> List[float]:
        """
        Perform simple keyword search.
        
        Args:
            query_tokens: Tokenized query
            
        Returns:
            List of scores for each document
        """
        num_docs = len(self.documents)
        scores = [0.0] * num_docs
        
        # Calculate scores based on term presence
        for term in query_tokens:
            if term not in self.term_document_freq:
                continue
            
            # Add 1.0 to score for each document containing the term
            for doc_id, _ in self.term_document_freq[term]:
                scores[doc_id] += 1.0
        
        # Normalize scores by query length
        if query_tokens:
            scores = [score / len(query_tokens) for score in scores]
        
        return scores
    
    def clear(self):
        """Clear the search index."""
        self.documents = []
        self.document_tokens = []
        self.document_lengths = []
        self.avg_document_length = 0
        self.term_document_freq = defaultdict(list)
        self.document_freq = defaultdict(int)
        self.idf = {}


class BM25SearchEngine(KeywordSearchEngine):
    """BM25 search engine implementation."""
    
    def __init__(
        self,
        language: str = "en",
        case_sensitive: bool = False,
        remove_punctuation: bool = True,
        remove_stop_words: bool = True,
        stemming: bool = True,
        k1: float = 1.5,
        b: float = 0.75
    ):
        """
        Initialize BM25 search engine.
        
        Args:
            language: Language for text processing
            case_sensitive: Whether search should be case sensitive
            remove_punctuation: Whether to remove punctuation
            remove_stop_words: Whether to remove stop words
            stemming: Whether to apply stemming
            k1: BM25 parameter for term frequency scaling
            b: BM25 parameter for document length normalization
        """
        super().__init__(
            method="bm25",
            language=language,
            case_sensitive=case_sensitive,
            remove_punctuation=remove_punctuation,
            remove_stop_words=remove_stop_words,
            stemming=stemming,
            k1=k1,
            b=b
        )


class TFIDFSearchEngine(KeywordSearchEngine):
    """TF-IDF search engine implementation."""
    
    def __init__(
        self,
        language: str = "en",
        case_sensitive: bool = False,
        remove_punctuation: bool = True,
        remove_stop_words: bool = True,
        stemming: bool = True
    ):
        """
        Initialize TF-IDF search engine.
        
        Args:
            language: Language for text processing
            case_sensitive: Whether search should be case sensitive
            remove_punctuation: Whether to remove punctuation
            remove_stop_words: Whether to remove stop words
            stemming: Whether to apply stemming
        """
        super().__init__(
            method="tfidf",
            language=language,
            case_sensitive=case_sensitive,
            remove_punctuation=remove_punctuation,
            remove_stop_words=remove_stop_words,
            stemming=stemming
        )


class SimpleSearchEngine(KeywordSearchEngine):
    """Simple keyword search engine implementation."""
    
    def __init__(
        self,
        language: str = "en",
        case_sensitive: bool = False,
        remove_punctuation: bool = True,
        remove_stop_words: bool = True,
        stemming: bool = True
    ):
        """
        Initialize simple search engine.
        
        Args:
            language: Language for text processing
            case_sensitive: Whether search should be case sensitive
            remove_punctuation: Whether to remove punctuation
            remove_stop_words: Whether to remove stop words
            stemming: Whether to apply stemming
        """
        super().__init__(
            method="simple",
            language=language,
            case_sensitive=case_sensitive,
            remove_punctuation=remove_punctuation,
            remove_stop_words=remove_stop_words,
            stemming=stemming
        )


def create_search_engine(
    method: str = "bm25",
    language: str = "en",
    **kwargs
) -> KeywordSearchEngine:
    """
    Create a keyword search engine.
    
    Args:
        method: Search method to use ("bm25", "tfidf", "simple")
        language: Language for text processing
        **kwargs: Additional arguments for the search engine
        
    Returns:
        Keyword search engine
    """
    method = method.lower()
    
    if method == "bm25":
        return BM25SearchEngine(language=language, **kwargs)
    elif method == "tfidf":
        return TFIDFSearchEngine(language=language, **kwargs)
    elif method == "simple":
        return SimpleSearchEngine(language=language, **kwargs)
    else:
        logger.warning(f"Unknown search method: {method}. Using BM25 instead.")
        return BM25SearchEngine(language=language, **kwargs)
