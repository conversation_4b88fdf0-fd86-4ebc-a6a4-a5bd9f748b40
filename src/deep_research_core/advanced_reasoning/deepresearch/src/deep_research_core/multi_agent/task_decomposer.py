"""
Task decomposition engine for breaking complex tasks into subtasks.

This module provides the TaskDecomposer class for decomposing complex tasks
into smaller, more manageable subtasks that can be assigned to different agents.
"""

import time
import uuid
import re
import json
from typing import Dict, List, Any, Optional
import networkx as nx
from ..utils.structured_logging import get_logger
from ..reasoning.cot import ChainOfThought

logger = get_logger(__name__)


class TaskDecomposer:
    """
    Task decomposition engine for breaking complex tasks into subtasks.

    This class provides methods for decomposing tasks into smaller, more
    manageable subtasks that can be assigned to different agents.

    Features:
    - Hierarchical decomposition: Break tasks into subtasks and sub-subtasks
    - Graph-based decomposition: Create a dependency graph of subtasks
    - Role-based assignment suggestions: Match subtasks to agent roles
    - Visualization: Generate visualizations of the task decomposition
    """

    def __init__(
        self,
        language_model,
        decomposition_strategy: str = "hierarchical",
        max_depth: int = 3,
        max_subtasks_per_level: int = 5,
        min_subtasks_per_level: int = 2,
        use_cache: bool = True,
        cache_size: int = 100,
        adaptive_mode: bool = False,
        feedback_threshold: float = 0.7,
        skill_based_assignment: bool = False,
        dependency_aware_scheduling: bool = False
    ):
        """
        Initialize the task decomposer.

        Args:
            language_model: Language model to use for decomposition
            decomposition_strategy: Strategy for decomposing tasks
                Options: "hierarchical", "graph", "sequential", "parallel", "adaptive"
            max_depth: Maximum depth of task hierarchy
            max_subtasks_per_level: Maximum number of subtasks per level
            min_subtasks_per_level: Minimum number of subtasks per level
            use_cache: Whether to cache decomposition results
            cache_size: Size of the decomposition cache
            adaptive_mode: Whether to use adaptive decomposition
            feedback_threshold: Threshold for feedback-based adaptation (0.0-1.0)
            skill_based_assignment: Whether to use skill-based assignment
            dependency_aware_scheduling: Whether to use dependency-aware scheduling
        """
        self.language_model = language_model
        self.decomposition_strategy = decomposition_strategy
        self.max_depth = max_depth
        self.max_subtasks_per_level = max_subtasks_per_level
        self.min_subtasks_per_level = min_subtasks_per_level
        self.use_cache = use_cache
        self.cache_size = cache_size
        self.adaptive_mode = adaptive_mode
        self.feedback_threshold = feedback_threshold
        self.skill_based_assignment = skill_based_assignment
        self.dependency_aware_scheduling = dependency_aware_scheduling

        # Initialize the CoT reasoner for task decomposition
        self.cot_reasoner = ChainOfThought(model=language_model)

        # Initialize task graph for dependency tracking
        self.task_graph = nx.DiGraph()

        # Cache for decomposition results
        self.decomposition_cache = {}

        # Feedback history for adaptive decomposition
        self.feedback_history = {}

        # Strategy performance metrics
        self.strategy_performance = {
            "hierarchical": {"success_rate": 0.8, "samples": 10},
            "graph": {"success_rate": 0.7, "samples": 5},
            "sequential": {"success_rate": 0.6, "samples": 5},
            "parallel": {"success_rate": 0.5, "samples": 5},
            "adaptive": {"success_rate": 0.9, "samples": 0}
        }

        # Agent skill registry
        self.agent_skills = {}

        # Task skill requirements registry
        self.task_skill_requirements = {}

        # Execution timeline for dependency-aware scheduling
        self.execution_timeline = {}

        logger.info(f"Initialized TaskDecomposer with strategy={decomposition_strategy}, max_depth={max_depth}")

    def decompose_task(
        self,
        task_description: str,
        available_roles: List[str] = None,
        context: Optional[Dict[str, Any]] = None,
        feedback: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Decompose a complex task into subtasks.

        Args:
            task_description: Description of the main task
            available_roles: List of agent roles available for assignment
            context: Additional context for task decomposition
            feedback: Optional feedback on previous decompositions for adaptive mode

        Returns:
            Dictionary containing subtasks and their dependencies
        """
        # Process feedback if provided (for adaptive decomposition)
        if feedback and self.adaptive_mode:
            self._process_decomposition_feedback(feedback)

        # Check cache if enabled
        if self.use_cache and task_description in self.decomposition_cache:
            logger.info(f"Using cached decomposition for task: {task_description[:50]}...")
            return self.decomposition_cache[task_description]

        logger.info(f"Decomposing task: {task_description}")
        logger.info(f"Available roles: {available_roles}")

        # Reset the task graph
        self.task_graph = nx.DiGraph()

        # Create the main task node
        main_task_id = f"task_{str(uuid.uuid4())[:8]}"
        self.task_graph.add_node(
            main_task_id,
            description=task_description,
            type="main_task",
            depth=0,
            available_roles=available_roles
        )

        # Choose decomposition strategy
        if self.adaptive_mode:
            # Use adaptive strategy selection
            strategy = self._select_best_strategy(task_description, context)
            logger.info(f"Adaptive mode selected strategy: {strategy}")
        else:
            strategy = self.decomposition_strategy

        # Apply the selected strategy
        if strategy == "hierarchical":
            result = self._hierarchical_decomposition(
                main_task_id,
                task_description,
                available_roles,
                context
            )
        elif strategy == "graph":
            result = self._graph_based_decomposition(
                main_task_id,
                task_description,
                available_roles,
                context
            )
        elif strategy == "sequential":
            result = self._sequential_decomposition(
                main_task_id,
                task_description,
                available_roles,
                context
            )
        elif strategy == "parallel":
            result = self._parallel_decomposition(
                main_task_id,
                task_description,
                available_roles,
                context
            )
        else:
            logger.warning(f"Unknown decomposition strategy: {strategy}, falling back to hierarchical")
            result = self._hierarchical_decomposition(
                main_task_id,
                task_description,
                available_roles,
                context
            )

        # Store the strategy used for this decomposition
        if "metadata" not in result:
            result["metadata"] = {}
        result["metadata"]["strategy_used"] = strategy

        # If in adaptive mode, evaluate the decomposition quality
        if self.adaptive_mode:
            quality_score = self._evaluate_decomposition_quality(result)
            result["metadata"]["quality_score"] = quality_score

            # Check if re-decomposition is needed
            if quality_score < self.feedback_threshold:
                logger.info(f"Low quality decomposition detected ({quality_score:.2f}), attempting re-decomposition")
                return self._redecompose_task(
                    main_task_id,
                    task_description,
                    available_roles,
                    context,
                    previous_strategy=strategy
                )

        # Cache the result if caching is enabled
        if self.use_cache:
            self.decomposition_cache[task_description] = result

            # Trim cache if it exceeds the size limit
            if len(self.decomposition_cache) > self.cache_size:
                # Remove the oldest entry
                oldest_key = next(iter(self.decomposition_cache))
                del self.decomposition_cache[oldest_key]

        return result

    def _hierarchical_decomposition(
        self,
        main_task_id: str,
        task_description: str,
        available_roles: List[str],
        context: Optional[Dict[str, Any]] = None,
        current_depth: int = 0
    ) -> Dict[str, Any]:
        """
        Perform hierarchical decomposition of a task.

        Args:
            main_task_id: ID of the main task
            task_description: Description of the task
            available_roles: List of available agent roles
            context: Additional context for decomposition
            current_depth: Current depth in the decomposition hierarchy

        Returns:
            Dictionary containing the decomposed task structure
        """
        if current_depth >= self.max_depth:
            logger.info(f"Reached maximum depth {self.max_depth}, stopping decomposition")
            return self._create_result_from_graph(main_task_id)

        # Generate subtasks using the language model
        subtasks = self._generate_subtasks(
            task_description,
            available_roles,
            context,
            is_hierarchical=True
        )

        # Add subtasks to the graph
        for subtask in subtasks:
            subtask_id = subtask["id"]

            # Add metadata for better tracking
            subtask_metadata = {
                "depth": current_depth + 1,
                "parent_id": main_task_id,
                "complexity": self._calculate_task_complexity(subtask["description"]),
                "estimated_effort": self._estimate_task_effort(subtask["description"]),
                "created_at": time.time()
            }

            # Merge with existing metadata
            subtask.update(subtask_metadata)

            # Add to graph
            self.task_graph.add_node(
                subtask_id,
                **subtask
            )

            # Connect to parent task with relationship metadata
            self.task_graph.add_edge(
                main_task_id,
                subtask_id,
                type="parent-child",
                relationship_strength=1.0
            )

            # Add dependencies between subtasks with relationship metadata
            for dep_id in subtask.get("dependencies", []):
                if dep_id in self.task_graph:
                    dependency_type = self._determine_dependency_type(
                        dep_id,
                        subtask_id,
                        subtask.get("description", ""),
                        self.task_graph.nodes[dep_id].get("description", "")
                    )

                    self.task_graph.add_edge(
                        dep_id,
                        subtask_id,
                        type="dependency",
                        dependency_type=dependency_type,
                        relationship_strength=0.8
                    )

        # Recursively decompose complex subtasks if not at max depth
        if current_depth + 1 < self.max_depth:
            for subtask in subtasks:
                subtask_id = subtask["id"]
                subtask_description = subtask["description"]

                # Enhanced complexity check with adaptive threshold
                complexity_score = self._calculate_task_complexity(subtask_description)
                depth_adjusted_threshold = self._get_complexity_threshold(current_depth + 1)

                # Check if this subtask is complex enough to decompose further
                if complexity_score > depth_adjusted_threshold:
                    logger.info(f"Further decomposing complex subtask: {subtask_description[:50]}... (complexity: {complexity_score:.2f})")
                    self._hierarchical_decomposition(
                        subtask_id,
                        subtask_description,
                        available_roles,
                        context,
                        current_depth + 1
                    )
                else:
                    logger.debug(f"Subtask not complex enough for further decomposition: {subtask_description[:50]}... (complexity: {complexity_score:.2f})")

        # Verify and optimize the hierarchical structure
        self._optimize_hierarchical_structure(main_task_id, current_depth)

        return self._create_result_from_graph(main_task_id)

    def _calculate_task_complexity(self, task_description: str) -> float:
        """
        Calculate the complexity of a task based on its description.

        Args:
            task_description: Description of the task

        Returns:
            Complexity score between 0.0 and 1.0
        """
        # Base complexity on length
        length_score = min(1.0, len(task_description.split()) / 50.0)

        # Complexity keywords
        complex_keywords = [
            "analyze", "research", "design", "develop", "implement",
            "evaluate", "compare", "synthesize", "create", "plan",
            "multiple", "complex", "comprehensive", "detailed", "integrate",
            "optimize", "coordinate", "manage", "investigate", "experiment"
        ]

        # Count complexity keywords
        keyword_count = sum(1 for keyword in complex_keywords if keyword in task_description.lower())
        keyword_score = min(1.0, keyword_count / 10.0)

        # Count conjunctions and conditionals
        conjunction_count = task_description.lower().count(" and ") + task_description.lower().count(" or ")
        conditional_count = task_description.lower().count(" if ") + task_description.lower().count(" when ")
        logic_score = min(1.0, (conjunction_count + conditional_count) / 5.0)

        # Weighted average of scores
        complexity_score = 0.4 * length_score + 0.4 * keyword_score + 0.2 * logic_score

        return complexity_score

    def _get_complexity_threshold(self, depth: int) -> float:
        """
        Get the complexity threshold for a given depth.

        Args:
            depth: Current depth in the task hierarchy

        Returns:
            Complexity threshold between 0.0 and 1.0
        """
        # Higher threshold at deeper levels to avoid over-decomposition
        base_threshold = 0.3
        depth_factor = 0.15 * depth

        return min(0.9, base_threshold + depth_factor)

    def _estimate_task_effort(self, task_description: str) -> float:
        """
        Estimate the effort required for a task based on its description.

        Args:
            task_description: Description of the task

        Returns:
            Effort estimate between 1.0 and 10.0
        """
        # Base effort on complexity
        complexity = self._calculate_task_complexity(task_description)

        # Scale to 1-10 range
        effort = 1.0 + 9.0 * complexity

        return effort

    def _determine_dependency_type(
        self,
        dependency_id: str,
        dependent_id: str,
        dependent_description: str,
        dependency_description: str
    ) -> str:
        """
        Determine the type of dependency between two tasks.

        Args:
            dependency_id: ID of the dependency task
            dependent_id: ID of the dependent task
            dependent_description: Description of the dependent task
            dependency_description: Description of the dependency task

        Returns:
            Dependency type string
        """
        # Look for keywords indicating dependency types
        if "input" in dependent_description.lower() or "output" in dependency_description.lower():
            return "input-output"
        elif "before" in dependent_description.lower() or "after" in dependent_description.lower():
            return "temporal"
        elif "use" in dependent_description.lower() or "utilize" in dependent_description.lower():
            return "resource"
        else:
            return "general"

    def _optimize_hierarchical_structure(self, root_id: str, current_depth: int) -> None:
        """
        Optimize the hierarchical structure of tasks.

        Args:
            root_id: ID of the root task
            current_depth: Current depth in the task hierarchy
        """
        # Get all children of the root
        children = [n for n in self.task_graph.successors(root_id)]

        if not children:
            return

        # Check for redundant tasks (very similar descriptions)
        descriptions = {}
        for child_id in children:
            desc = self.task_graph.nodes[child_id].get("description", "")
            descriptions[child_id] = desc

        # Find potential redundancies
        redundant_pairs = []
        for i, (id1, desc1) in enumerate(descriptions.items()):
            for id2, desc2 in list(descriptions.items())[i+1:]:
                similarity = self._calculate_description_similarity(desc1, desc2)
                if similarity > 0.8:  # High similarity threshold
                    redundant_pairs.append((id1, id2, similarity))

        # Merge redundant tasks
        for id1, id2, similarity in redundant_pairs:
            logger.info(f"Merging redundant tasks: {id1} and {id2} (similarity: {similarity:.2f})")
            self._merge_tasks(id1, id2)

        # Check for unbalanced subtask distribution
        if len(children) < self.min_subtasks_per_level:
            logger.info(f"Too few subtasks ({len(children)}) at depth {current_depth}, considering further decomposition")
            # This would be handled by the calling function's recursive decomposition

        elif len(children) > self.max_subtasks_per_level:
            logger.info(f"Too many subtasks ({len(children)}) at depth {current_depth}, grouping related tasks")
            self._group_related_tasks(root_id, children)

    def _calculate_description_similarity(self, desc1: str, desc2: str) -> float:
        """
        Calculate the similarity between two task descriptions.

        Args:
            desc1: First task description
            desc2: Second task description

        Returns:
            Similarity score between 0.0 and 1.0
        """
        # Simple word overlap similarity
        words1 = set(desc1.lower().split())
        words2 = set(desc2.lower().split())

        if not words1 or not words2:
            return 0.0

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        return len(intersection) / len(union)

    def _merge_tasks(self, task_id1: str, task_id2: str) -> str:
        """
        Merge two tasks into one.

        Args:
            task_id1: ID of the first task
            task_id2: ID of the second task

        Returns:
            ID of the merged task
        """
        # Get task data
        task1 = self.task_graph.nodes[task_id1]
        task2 = self.task_graph.nodes[task_id2]

        # Create merged description
        desc1 = task1.get("description", "")
        desc2 = task2.get("description", "")
        merged_description = f"{desc1} and {desc2}"

        # Merge roles
        roles1 = task1.get("suitable_roles", [])
        roles2 = task2.get("suitable_roles", [])
        merged_roles = list(set(roles1 + roles2))

        # Create merged task
        merged_id = f"merged_{task_id1}_{task_id2}"
        self.task_graph.add_node(
            merged_id,
            description=merged_description,
            suitable_roles=merged_roles,
            depth=task1.get("depth", 0),
            merged_from=[task_id1, task_id2],
            complexity=max(
                task1.get("complexity", 0.5),
                task2.get("complexity", 0.5)
            ),
            estimated_effort=task1.get("estimated_effort", 1.0) + task2.get("estimated_effort", 1.0)
        )

        # Connect to parent
        parent_id = None
        for pred in self.task_graph.predecessors(task_id1):
            if self.task_graph.get_edge_data(pred, task_id1).get("type") == "parent-child":
                parent_id = pred
                break

        if parent_id:
            self.task_graph.add_edge(parent_id, merged_id, type="parent-child")

        # Transfer dependencies
        for pred in set(self.task_graph.predecessors(task_id1)).union(set(self.task_graph.predecessors(task_id2))):
            if pred != parent_id and pred not in [task_id1, task_id2]:
                self.task_graph.add_edge(pred, merged_id, type="dependency")

        # Transfer dependents
        for succ in set(self.task_graph.successors(task_id1)).union(set(self.task_graph.successors(task_id2))):
            if succ not in [task_id1, task_id2]:
                self.task_graph.add_edge(merged_id, succ, type="dependency")

        # Remove original tasks
        self.task_graph.remove_node(task_id1)
        self.task_graph.remove_node(task_id2)

        return merged_id

    def _group_related_tasks(self, parent_id: str, child_ids: List[str]) -> None:
        """
        Group related tasks under intermediate group tasks.

        Args:
            parent_id: ID of the parent task
            child_ids: List of child task IDs to group
        """
        if len(child_ids) <= self.max_subtasks_per_level:
            return

        # Calculate similarity matrix
        similarity_matrix = {}
        for i, id1 in enumerate(child_ids):
            for id2 in child_ids[i+1:]:
                desc1 = self.task_graph.nodes[id1].get("description", "")
                desc2 = self.task_graph.nodes[id2].get("description", "")
                similarity = self._calculate_description_similarity(desc1, desc2)
                similarity_matrix[(id1, id2)] = similarity

        # Group tasks using a simple clustering approach
        groups = []
        remaining = set(child_ids)

        while remaining:
            # Start a new group with the first remaining task
            current_group = [next(iter(remaining))]
            remaining.remove(current_group[0])

            # Find related tasks
            while remaining:
                best_candidate = None
                best_similarity = 0.0

                for task_id in remaining:
                    # Calculate average similarity to current group
                    avg_similarity = 0.0
                    for group_task in current_group:
                        key = (min(task_id, group_task), max(task_id, group_task))
                        if key in similarity_matrix:
                            avg_similarity += similarity_matrix[key]

                    avg_similarity /= len(current_group)

                    if avg_similarity > best_similarity:
                        best_similarity = avg_similarity
                        best_candidate = task_id

                # If we found a related task, add it to the group
                if best_candidate and best_similarity > 0.3:  # Threshold for grouping
                    current_group.append(best_candidate)
                    remaining.remove(best_candidate)
                else:
                    break

            groups.append(current_group)

        # Create group tasks
        for i, group in enumerate(groups):
            if len(group) <= 1:
                continue  # Skip singleton groups

            # Create group task
            group_id = f"group_{parent_id}_{i}"
            group_desc = f"Group of related tasks: {', '.join(self.task_graph.nodes[task_id].get('description', '')[:20] + '...' for task_id in group[:3])}"
            if len(group) > 3:
                group_desc += f" and {len(group) - 3} more"

            # Collect all suitable roles from group members
            all_roles = []
            for task_id in group:
                all_roles.extend(self.task_graph.nodes[task_id].get("suitable_roles", []))

            self.task_graph.add_node(
                group_id,
                description=group_desc,
                suitable_roles=list(set(all_roles)),
                depth=self.task_graph.nodes[group[0]].get("depth", 0),
                type="group",
                group_size=len(group)
            )

            # Connect group to parent
            self.task_graph.add_edge(parent_id, group_id, type="parent-child")

            # Connect group members to group
            for task_id in group:
                # Remove direct connection to parent
                if self.task_graph.has_edge(parent_id, task_id):
                    self.task_graph.remove_edge(parent_id, task_id)

                # Connect to group
                self.task_graph.add_edge(group_id, task_id, type="parent-child")

    def _graph_based_decomposition(
        self,
        main_task_id: str,
        task_description: str,
        available_roles: List[str],
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Perform graph-based decomposition of a task.

        Args:
            main_task_id: ID of the main task
            task_description: Description of the task
            available_roles: List of available agent roles
            context: Additional context for decomposition

        Returns:
            Dictionary containing the decomposed task structure
        """
        logger.info(f"Performing graph-based decomposition for task: {task_description[:50]}...")

        # Generate subtasks with explicit dependencies
        subtasks = self._generate_subtasks(
            task_description,
            available_roles,
            context,
            is_hierarchical=False
        )

        # Add subtasks to the graph with metadata
        for subtask in subtasks:
            subtask_id = subtask["id"]

            # Calculate task complexity and effort
            complexity = self._calculate_task_complexity(subtask["description"])
            effort = self._estimate_task_effort(subtask["description"])

            # Add metadata for better tracking and optimization
            subtask_metadata = {
                "depth": 1,  # All subtasks are at depth 1 in graph-based decomposition
                "complexity": complexity,
                "estimated_effort": effort,
                "created_at": time.time()
            }

            # Merge with existing metadata
            subtask.update(subtask_metadata)

            # Add to graph
            self.task_graph.add_node(
                subtask_id,
                **subtask
            )

            # Connect to parent task with relationship metadata
            self.task_graph.add_edge(
                main_task_id,
                subtask_id,
                type="parent-child",
                relationship_strength=1.0
            )

        # Add dependencies between subtasks with relationship types
        for subtask in subtasks:
            subtask_id = subtask["id"]
            for dep_id in subtask.get("dependencies", []):
                if dep_id in self.task_graph:
                    # Determine the type of dependency
                    dependency_type = self._determine_dependency_type(
                        dep_id,
                        subtask_id,
                        self.task_graph.nodes[dep_id].get("description", ""),
                        self.task_graph.nodes[subtask_id].get("description", "")
                    )

                    # Add edge with dependency metadata
                    self.task_graph.add_edge(
                        dep_id,
                        subtask_id,
                        type=dependency_type,
                        weight=1.0
                    )

        # Verify the graph is acyclic
        if not nx.is_directed_acyclic_graph(self.task_graph):
            logger.warning("Task dependency graph contains cycles, removing cycles")
            self._resolve_dependency_cycles()

        # Optimize the graph structure
        self._optimize_graph_structure(main_task_id)

        # Identify critical path
        critical_path = self._identify_critical_path()
        if critical_path:
            logger.info(f"Critical path identified with {len(critical_path)} tasks")

            # Mark tasks on the critical path
            for task_id in critical_path:
                if task_id in self.task_graph.nodes:
                    self.task_graph.nodes[task_id]["on_critical_path"] = True

        return self._create_result_from_graph(main_task_id)

    def _resolve_dependency_cycles(self) -> int:
        """
        Resolve cycles in the dependency graph by removing edges.

        Returns:
            Number of edges removed to resolve cycles
        """
        # Find cycles in the graph
        cycles = list(nx.simple_cycles(self.task_graph))
        edges_removed = 0

        for cycle in cycles:
            if len(cycle) > 1:
                # Find the edge with the lowest weight to remove
                min_weight = float('inf')
                edge_to_remove = (cycle[-1], cycle[0])

                for i in range(len(cycle)):
                    u = cycle[i]
                    v = cycle[(i + 1) % len(cycle)]

                    if self.task_graph.has_edge(u, v):
                        weight = self.task_graph.edges[u, v].get("weight", 1.0)
                        if weight < min_weight:
                            min_weight = weight
                            edge_to_remove = (u, v)

                # Remove the selected edge
                self.task_graph.remove_edge(*edge_to_remove)
                logger.info(f"Removed edge {edge_to_remove[0]} -> {edge_to_remove[1]} to resolve cycle")
                edges_removed += 1

        return edges_removed

    def _optimize_graph_structure(self, main_task_id: str) -> None:
        """
        Optimize the graph structure for better task execution.

        Args:
            main_task_id: ID of the main task
        """
        # Identify parallel tasks that can be merged
        self._merge_parallel_tasks()

        # Identify bottlenecks and add alternative paths
        self._identify_and_resolve_bottlenecks()

        # Balance the graph structure
        self._balance_graph_structure(main_task_id)

    def _merge_parallel_tasks(self) -> None:
        """
        Identify and merge similar parallel tasks.
        """
        # Get all tasks
        tasks = list(self.task_graph.nodes())

        # Skip if there are too few tasks
        if len(tasks) <= 3:
            return

        # Find tasks with similar descriptions and no dependencies between them
        for i, task1 in enumerate(tasks):
            if task1 not in self.task_graph.nodes:
                continue

            for task2 in tasks[i+1:]:
                if task2 not in self.task_graph.nodes:
                    continue

                # Skip if there's a dependency between the tasks
                if nx.has_path(self.task_graph, task1, task2) or nx.has_path(self.task_graph, task2, task1):
                    continue

                # Check if descriptions are similar
                desc1 = self.task_graph.nodes[task1].get("description", "")
                desc2 = self.task_graph.nodes[task2].get("description", "")

                similarity = self._calculate_description_similarity(desc1, desc2)

                # If similar enough, merge the tasks
                if similarity > 0.7:  # High similarity threshold
                    self._merge_tasks(task1, task2)

    def _identify_and_resolve_bottlenecks(self) -> None:
        """
        Identify bottlenecks in the task graph and resolve them.
        """
        # A bottleneck is a task with many incoming and outgoing edges
        bottlenecks = []

        for node in self.task_graph.nodes():
            in_degree = self.task_graph.in_degree(node)
            out_degree = self.task_graph.out_degree(node)

            # If a node has many dependencies and many dependents, it's a bottleneck
            if in_degree > 2 and out_degree > 2:
                bottlenecks.append(node)

        for bottleneck in bottlenecks:
            logger.info(f"Identified bottleneck task: {bottleneck}")

            # Mark the task as a bottleneck
            self.task_graph.nodes[bottleneck]["is_bottleneck"] = True

            # Increase the estimated effort to reflect its importance
            current_effort = self.task_graph.nodes[bottleneck].get("estimated_effort", 1.0)
            self.task_graph.nodes[bottleneck]["estimated_effort"] = current_effort * 1.5

    def _balance_graph_structure(self, main_task_id: str) -> None:
        """
        Balance the graph structure for better workload distribution.

        Args:
            main_task_id: ID of the main task
        """
        # Get all direct children of the main task
        children = list(self.task_graph.successors(main_task_id))

        # Skip if there are too few children
        if len(children) <= 1:
            return

        # Calculate total effort
        total_effort = sum(
            self.task_graph.nodes[child].get("estimated_effort", 1.0)
            for child in children
        )

        # Calculate average effort per task
        avg_effort = total_effort / len(children)

        # Identify tasks with significantly higher effort
        high_effort_tasks = [
            child for child in children
            if self.task_graph.nodes[child].get("estimated_effort", 1.0) > avg_effort * 1.5
        ]

        # For high-effort tasks, consider breaking them down further
        for task_id in high_effort_tasks:
            task_desc = self.task_graph.nodes[task_id].get("description", "")
            logger.info(f"High-effort task identified: {task_desc[:50]}...")

            # Mark the task for potential further decomposition
            self.task_graph.nodes[task_id]["needs_decomposition"] = True

    def _identify_critical_path(self) -> List[str]:
        """
        Identify the critical path in the task graph.

        The critical path is the longest path through the graph, representing
        the sequence of tasks that determines the minimum time needed to
        complete the entire task set.

        Returns:
            List of task IDs in the critical path
        """
        # Ensure the graph is acyclic
        if not nx.is_directed_acyclic_graph(self.task_graph):
            self._resolve_dependency_cycles()

            # Check again after resolving cycles
            if not nx.is_directed_acyclic_graph(self.task_graph):
                logger.error("Failed to resolve all cycles in the task graph")
                return []

        # Create a copy of the graph with edge weights based on estimated effort
        weighted_graph = self.task_graph.copy()

        # Set edge weights based on the estimated effort of the target node
        for u, v in weighted_graph.edges():
            effort = weighted_graph.nodes[v].get("estimated_effort", 1.0)
            weighted_graph.edges[u, v]["weight"] = -effort  # Negative because we want the longest path

        # Find sources (tasks with no dependencies) and sinks (tasks with no dependents)
        sources = [node for node, in_degree in self.task_graph.in_degree() if in_degree == 0]
        sinks = [node for node, out_degree in self.task_graph.out_degree() if out_degree == 0]

        # If no sources or sinks, return empty path
        if not sources or not sinks:
            return []

        # Find the longest path from any source to any sink
        critical_path = []
        max_path_length = float('-inf')

        for source in sources:
            for sink in sinks:
                try:
                    # Find the shortest path with negative weights (equivalent to longest path)
                    path = nx.shortest_path(weighted_graph, source, sink, weight="weight")
                    path_length = sum(weighted_graph.edges[u, v].get("weight", -1.0) for u, v in zip(path[:-1], path[1:]))

                    if path_length < max_path_length:  # Remember weights are negative
                        max_path_length = path_length
                        critical_path = path
                except nx.NetworkXNoPath:
                    continue

        return critical_path

    def _determine_dependency_type(
        self,
        dependency_id: str,
        dependent_id: str,
        dependency_description: str,
        dependent_description: str
    ) -> str:
        """
        Determine the type of dependency between two tasks.

        Args:
            dependency_id: ID of the dependency task
            dependent_id: ID of the dependent task
            dependency_description: Description of the dependency task
            dependent_description: Description of the dependent task

        Returns:
            Type of dependency: "input-output", "temporal", "resource", or "general"
        """
        # Check for input-output dependency
        input_output_keywords = ["output", "result", "data", "information", "report"]
        if any(keyword in dependent_description.lower() for keyword in input_output_keywords) and \
           any(keyword in dependency_description.lower() for keyword in input_output_keywords):
            return "input-output"

        # Check for temporal dependency
        temporal_keywords = ["after", "before", "then", "first", "second", "next", "previous"]
        if any(keyword in dependent_description.lower() for keyword in temporal_keywords):
            return "temporal"

        # Check for resource dependency
        resource_keywords = ["resource", "tool", "equipment", "material", "budget"]
        if any(keyword in dependent_description.lower() for keyword in resource_keywords) and \
           any(keyword in dependency_description.lower() for keyword in resource_keywords):
            return "resource"

        # Default to general dependency
        return "general"

    def _sequential_decomposition(
        self,
        main_task_id: str,
        task_description: str,
        available_roles: List[str],
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Perform sequential decomposition of a task.

        Args:
            main_task_id: ID of the main task
            task_description: Description of the task
            available_roles: List of available agent roles
            context: Additional context for decomposition

        Returns:
            Dictionary containing the decomposed task structure
        """
        # Generate sequential subtasks
        subtasks = self._generate_subtasks(
            task_description,
            available_roles,
            context,
            is_sequential=True
        )

        # Add subtasks to the graph in sequential order
        prev_subtask_id = None
        for i, subtask in enumerate(subtasks):
            subtask_id = subtask["id"]
            self.task_graph.add_node(
                subtask_id,
                **subtask,
                depth=1,
                order=i
            )
            # Connect to parent task
            self.task_graph.add_edge(main_task_id, subtask_id)

            # Connect to previous subtask to create a chain
            if prev_subtask_id:
                self.task_graph.add_edge(prev_subtask_id, subtask_id)

            prev_subtask_id = subtask_id

        return self._create_result_from_graph(main_task_id)

    def _parallel_decomposition(
        self,
        main_task_id: str,
        task_description: str,
        available_roles: List[str],
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Perform parallel decomposition of a task.

        Args:
            main_task_id: ID of the main task
            task_description: Description of the task
            available_roles: List of available agent roles
            context: Additional context for decomposition

        Returns:
            Dictionary containing the decomposed task structure
        """
        # Generate parallel subtasks (independent tasks)
        subtasks = self._generate_subtasks(
            task_description,
            available_roles,
            context,
            is_parallel=True
        )

        # Add subtasks to the graph with no dependencies between them
        for i, subtask in enumerate(subtasks):
            subtask_id = subtask["id"]
            self.task_graph.add_node(
                subtask_id,
                **subtask,
                depth=1,
                order=i
            )
            # Connect to parent task only
            self.task_graph.add_edge(main_task_id, subtask_id)

        return self._create_result_from_graph(main_task_id)

    def _generate_subtasks(
        self,
        task_description: str,
        available_roles: List[str],
        context: Optional[Dict[str, Any]] = None,
        is_hierarchical: bool = False,
        is_sequential: bool = False,
        is_parallel: bool = False
    ) -> List[Dict[str, Any]]:
        """
        Generate subtasks using the language model.

        Args:
            task_description: Description of the task to decompose
            available_roles: List of available agent roles
            context: Additional context for decomposition
            is_hierarchical: Whether to generate hierarchical subtasks
            is_sequential: Whether to generate sequential subtasks
            is_parallel: Whether to generate parallel subtasks

        Returns:
            List of subtask dictionaries
        """
        # Prepare the system prompt
        system_prompt = self._get_system_prompt(
            is_hierarchical=is_hierarchical,
            is_sequential=is_sequential,
            is_parallel=is_parallel
        )

        # Prepare the user prompt
        user_prompt = self._get_user_prompt(
            task_description=task_description,
            available_roles=available_roles,
            context=context,
            is_hierarchical=is_hierarchical,
            is_sequential=is_sequential,
            is_parallel=is_parallel
        )

        try:
            # Use the CoT reasoner to generate subtasks
            result = self.cot_reasoner.reason(
                query=user_prompt,
                custom_system_prompt=system_prompt
            )

            # Extract the subtasks from the result
            # Handle different return types from the reasoner
            if isinstance(result, dict) and "reasoning" in result:
                reasoning_text = result["reasoning"]
            else:
                # For ReasoningResult objects or other return types
                reasoning_text = str(result)

            subtasks = self._parse_subtasks(reasoning_text, task_description)

            if not subtasks:
                logger.warning(f"Failed to generate subtasks for: {task_description[:50]}...")
                # Return a default subtask as fallback
                return [self._create_default_subtask(task_description)]

            return subtasks

        except Exception as e:
            logger.error(f"Error generating subtasks: {str(e)}")
            # Return a default subtask as fallback
            return [self._create_default_subtask(task_description)]

    def _get_system_prompt(
        self,
        is_hierarchical: bool = False,
        is_sequential: bool = False,
        is_parallel: bool = False
    ) -> str:
        """
        Get the system prompt for task decomposition.

        Args:
            is_hierarchical: Whether to generate hierarchical subtasks
            is_sequential: Whether to generate sequential subtasks
            is_parallel: Whether to generate parallel subtasks

        Returns:
            System prompt string
        """
        base_prompt = """You are an expert task decomposition system that breaks down complex tasks into smaller, manageable subtasks.
Your goal is to create a clear, logical decomposition that can be assigned to different agents with specific roles.

For each subtask, you will:
1. Provide a clear, specific description
2. Identify suitable agent roles that can handle this subtask
3. List any dependencies (other subtasks that must be completed first)
4. Assign a unique ID to each subtask (e.g., "subtask_1", "subtask_2", etc.)

Your output should be in JSON format with the following structure:
{
  "subtasks": [
    {
      "id": "subtask_1",
      "description": "Detailed description of the first subtask",
      "suitable_roles": ["Role1", "Role2"],
      "dependencies": []
    },
    {
      "id": "subtask_2",
      "description": "Detailed description of the second subtask",
      "suitable_roles": ["Role3"],
      "dependencies": ["subtask_1"]
    },
    ...
  ]
}"""

        if is_hierarchical:
            base_prompt += """

For hierarchical decomposition:
- Break the main task into high-level subtasks
- Each subtask should be self-contained but may depend on other subtasks
- Consider which subtasks might need further decomposition into sub-subtasks
- Ensure the decomposition is comprehensive and covers all aspects of the main task"""

        if is_sequential:
            base_prompt += """

For sequential decomposition:
- Break the task into steps that must be performed in a specific order
- Each step should build on the previous steps
- Ensure the sequence is logical and leads to the completion of the main task
- Make dependencies explicit by listing the previous step as a dependency"""

        if is_parallel:
            base_prompt += """

For parallel decomposition:
- Break the task into independent subtasks that can be performed simultaneously
- Minimize dependencies between subtasks
- Ensure each subtask is self-contained
- Focus on maximizing parallelism to speed up task completion"""

        return base_prompt

    def _get_user_prompt(
        self,
        task_description: str,
        available_roles: List[str],
        context: Optional[Dict[str, Any]] = None,
        is_hierarchical: bool = False,
        is_sequential: bool = False,
        is_parallel: bool = False
    ) -> str:
        """
        Get the user prompt for task decomposition.

        Args:
            task_description: Description of the task to decompose
            available_roles: List of available agent roles
            context: Additional context for decomposition
            is_hierarchical: Whether to generate hierarchical subtasks
            is_sequential: Whether to generate sequential subtasks
            is_parallel: Whether to generate parallel subtasks

        Returns:
            User prompt string
        """
        prompt = f"Please decompose the following task into {self.min_subtasks_per_level}-{self.max_subtasks_per_level} subtasks:\n\n"
        prompt += f"TASK: {task_description}\n\n"

        if available_roles:
            prompt += f"Available agent roles: {', '.join(available_roles)}\n\n"

        if context:
            prompt += "Additional context:\n"
            for key, value in context.items():
                prompt += f"- {key}: {value}\n"
            prompt += "\n"

        if is_hierarchical:
            prompt += "Please use hierarchical decomposition to break this task into subtasks.\n"
        elif is_sequential:
            prompt += "Please use sequential decomposition to break this task into ordered steps.\n"
        elif is_parallel:
            prompt += "Please use parallel decomposition to break this task into independent subtasks that can be performed simultaneously.\n"

        prompt += "\nReturn your answer as a JSON object with a 'subtasks' array as described in your instructions."

        return prompt

    def _parse_subtasks(
        self,
        decomposition_text: str,
        original_task: str
    ) -> List[Dict[str, Any]]:
        """
        Parse subtasks from the decomposition text.

        Args:
            decomposition_text: Text containing the decomposition
            original_task: Original task description

        Returns:
            List of subtask dictionaries
        """
        try:
            # Try to extract JSON from the text
            json_match = re.search(r'(\{|\[).*(\}|\])', decomposition_text, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                decomposition = json.loads(json_str)

                # Handle different JSON structures
                if isinstance(decomposition, dict) and "subtasks" in decomposition:
                    subtasks = decomposition["subtasks"]
                elif isinstance(decomposition, list):
                    subtasks = decomposition
                else:
                    raise ValueError("Unexpected JSON structure")

                # Validate and clean up subtasks
                return self._validate_subtasks(subtasks)

        except Exception as e:
            logger.error(f"Error parsing subtasks JSON: {str(e)}")

            # Fallback: try to extract subtasks using regex
            subtasks = []

            # Look for numbered subtasks
            subtask_pattern = r'(?:Subtask|Task|Step)\s+(\d+|[A-Za-z]+)[\s:]+([^\n]+)'
            matches = re.findall(subtask_pattern, decomposition_text)

            if matches:
                for i, (_, description) in enumerate(matches):
                    subtask_id = f"subtask_{i+1}"
                    subtasks.append({
                        "id": subtask_id,
                        "description": description.strip(),
                        "suitable_roles": [],
                        "dependencies": []
                    })

            # If we found subtasks, return them
            if subtasks:
                return self._validate_subtasks(subtasks)

        # If all else fails, create a default subtask
        logger.warning("Failed to parse subtasks, creating default subtask")
        return [self._create_default_subtask(original_task)]

    def _validate_subtasks(self, subtasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Validate and clean up subtasks.

        Args:
            subtasks: List of subtask dictionaries

        Returns:
            Validated list of subtask dictionaries
        """
        valid_subtasks = []

        for i, subtask in enumerate(subtasks):
            # Ensure required fields are present
            if "description" not in subtask:
                logger.warning(f"Subtask {i} missing description, skipping")
                continue

            # Create a clean subtask dictionary
            clean_subtask = {
                "id": subtask.get("id", f"subtask_{i+1}"),
                "description": subtask["description"],
                "suitable_roles": subtask.get("suitable_roles", []),
                "dependencies": subtask.get("dependencies", [])
            }

            # Ensure ID is valid
            if not re.match(r'^[a-zA-Z0-9_]+$', clean_subtask["id"]):
                clean_subtask["id"] = f"subtask_{i+1}"

            valid_subtasks.append(clean_subtask)

        return valid_subtasks

    def _create_default_subtask(self, task_description: str) -> Dict[str, Any]:
        """
        Create a default subtask as a fallback.

        Args:
            task_description: Original task description

        Returns:
            Default subtask dictionary
        """
        return {
            "id": "subtask_1",
            "description": f"Complete the task: {task_description}",
            "suitable_roles": [],
            "dependencies": []
        }

    def _create_result_from_graph(self, main_task_id: str) -> Dict[str, Any]:
        """
        Create a result dictionary from the task graph.

        Args:
            main_task_id: ID of the main task

        Returns:
            Result dictionary with subtasks and dependencies
        """
        # Get the main task
        main_task = self.task_graph.nodes[main_task_id]

        # Get all subtasks (excluding the main task)
        subtasks = []
        for node_id in self.task_graph.nodes:
            if node_id != main_task_id:
                node_data = self.task_graph.nodes[node_id]
                subtask = {
                    "id": node_id,
                    "description": node_data.get("description", ""),
                    "suitable_roles": node_data.get("suitable_roles", []),
                    "dependencies": [],
                    "estimated_effort": node_data.get("estimated_effort", 1.0),
                    "complexity": node_data.get("complexity", 0.5),
                    "on_critical_path": node_data.get("on_critical_path", False),
                    "is_bottleneck": node_data.get("is_bottleneck", False)
                }

                # Get dependencies (incoming edges)
                for pred in self.task_graph.predecessors(node_id):
                    if pred != main_task_id:  # Skip the main task
                        subtask["dependencies"].append(pred)

                subtasks.append(subtask)

        # Create dependencies dictionary
        dependencies = {}
        for subtask in subtasks:
            dependencies[subtask["id"]] = subtask["dependencies"]

        # Get execution order
        execution_order = self.get_execution_order()

        # Create the result dictionary
        result = {
            "main_task": main_task.get("description", ""),
            "subtasks": subtasks,
            "dependencies": dependencies,
            "execution_order": execution_order,
            "metadata": {
                "decomposition_strategy": self.decomposition_strategy,
                "timestamp": time.time()
            }
        }

        # Add workload balancing information if available roles are provided
        available_roles = main_task.get("available_roles", [])
        if available_roles:
            result["workload_assignment"] = self._balance_workload(subtasks, available_roles)

        return result

    def register_agent_skills(self, agent_id: str, skills: Dict[str, float]) -> None:
        """
        Register an agent's skills for skill-based assignment.

        Args:
            agent_id: ID of the agent
            skills: Dictionary mapping skill names to proficiency levels (0.0-1.0)
        """
        if not self.skill_based_assignment:
            logger.warning("Skill-based assignment is disabled, enabling it now")
            self.skill_based_assignment = True

        self.agent_skills[agent_id] = skills
        logger.info(f"Registered skills for agent {agent_id}: {skills}")

    def estimate_task_skill_requirements(self, task_description: str) -> Dict[str, float]:
        """
        Estimate the skill requirements for a task based on its description.

        Args:
            task_description: Description of the task

        Returns:
            Dictionary mapping skill names to required proficiency levels (0.0-1.0)
        """
        # Define common skills and their keywords
        skill_keywords = {
            "research": ["research", "investigate", "analyze", "study", "explore", "review", "examine"],
            "development": ["develop", "code", "program", "implement", "build", "create", "construct"],
            "design": ["design", "architect", "plan", "sketch", "draft", "model", "prototype"],
            "testing": ["test", "verify", "validate", "check", "assess", "evaluate", "debug"],
            "documentation": ["document", "write", "record", "report", "describe", "explain", "detail"],
            "management": ["manage", "coordinate", "organize", "lead", "direct", "supervise", "oversee"],
            "communication": ["communicate", "present", "discuss", "explain", "share", "collaborate", "liaise"],
            "analysis": ["analyze", "evaluate", "assess", "measure", "calculate", "quantify", "estimate"]
        }

        # Initialize skill requirements
        skill_requirements = {skill: 0.0 for skill in skill_keywords}

        # Check for skill keywords in the task description
        task_lower = task_description.lower()
        for skill, keywords in skill_keywords.items():
            # Count occurrences of keywords
            keyword_count = sum(1 for keyword in keywords if keyword in task_lower)

            # Calculate skill requirement based on keyword occurrences
            if keyword_count > 0:
                # More occurrences indicate higher requirement, but cap at 1.0
                skill_requirements[skill] = min(1.0, keyword_count * 0.2)

        # Check for complexity indicators to adjust skill levels
        complexity = self._calculate_task_complexity(task_description)

        # Adjust skill requirements based on complexity
        for skill in skill_requirements:
            if skill_requirements[skill] > 0:
                # Higher complexity requires higher skill levels
                skill_requirements[skill] = min(1.0, skill_requirements[skill] + complexity * 0.3)

        logger.debug(f"Estimated skill requirements for task: {skill_requirements}")
        return skill_requirements

    def calculate_skill_compatibility(
        self,
        agent_skills: Dict[str, float],
        task_requirements: Dict[str, float]
    ) -> float:
        """
        Calculate the compatibility between an agent's skills and a task's requirements.

        Args:
            agent_skills: Dictionary mapping skill names to proficiency levels
            task_requirements: Dictionary mapping skill names to required proficiency levels

        Returns:
            Compatibility score between 0.0 and 1.0
        """
        if not task_requirements:
            return 1.0  # No requirements means perfect compatibility

        # Get all skills that are required for the task
        required_skills = [skill for skill, level in task_requirements.items() if level > 0]

        if not required_skills:
            return 1.0  # No requirements means perfect compatibility

        # Calculate compatibility for each required skill
        skill_scores = []
        for skill in required_skills:
            requirement = task_requirements[skill]
            proficiency = agent_skills.get(skill, 0.0)

            # Calculate score for this skill
            if requirement <= proficiency:
                # Agent meets or exceeds requirement
                score = 1.0
            else:
                # Agent doesn't meet requirement, calculate partial score
                score = proficiency / requirement

            skill_scores.append(score)

        # Calculate overall compatibility as weighted average
        # Give more weight to skills with higher requirements
        weighted_scores = [
            score * task_requirements[skill]
            for score, skill in zip(skill_scores, required_skills)
        ]

        total_weight = sum(task_requirements[skill] for skill in required_skills)

        if total_weight == 0:
            return 1.0

        compatibility = sum(weighted_scores) / total_weight

        return compatibility

    def _balance_workload(self, subtasks: List[Dict[str, Any]], available_roles: List[str]) -> Dict[str, List[str]]:
        """
        Balance workload among available roles.

        Args:
            subtasks: List of subtasks to assign
            available_roles: List of available roles

        Returns:
            Dictionary mapping role names to lists of assigned task IDs
        """
        if not available_roles or not subtasks:
            return {}

        logger.info(f"Balancing workload among {len(available_roles)} roles for {len(subtasks)} tasks")

        # Initialize workload assignment
        workload_assignment = {role: [] for role in available_roles}

        # Initialize role workloads
        role_workloads = {role: 0.0 for role in available_roles}

        # Sort subtasks by priority:
        # 1. Critical path tasks first
        # 2. Bottleneck tasks next
        # 3. Then by effort (highest first)
        sorted_subtasks = sorted(
            subtasks,
            key=lambda t: (
                not t.get("on_critical_path", False),  # Critical path tasks first
                not t.get("is_bottleneck", False),     # Bottleneck tasks next
                -t.get("estimated_effort", 1.0)        # Higher effort tasks first
            )
        )

        # First pass: assign tasks to roles based on suitability and skills
        for subtask in sorted_subtasks:
            subtask_id = subtask["id"]
            suitable_roles = subtask.get("suitable_roles", [])

            # If skill-based assignment is enabled, calculate skill compatibility
            if self.skill_based_assignment and self.agent_skills:
                # Estimate skill requirements for this task
                task_description = subtask.get("description", "")
                skill_requirements = self.estimate_task_skill_requirements(task_description)

                # Store skill requirements for future reference
                self.task_skill_requirements[subtask_id] = skill_requirements

                # Calculate compatibility scores for each available role
                compatibility_scores = {}
                for role in available_roles:
                    # If we have skill data for this role
                    if role in self.agent_skills:
                        compatibility = self.calculate_skill_compatibility(
                            self.agent_skills[role],
                            skill_requirements
                        )
                        compatibility_scores[role] = compatibility
                    else:
                        # Default compatibility for roles without skill data
                        compatibility_scores[role] = 0.5

                # Filter to roles with good compatibility (>0.7) or use all if none are good
                good_roles = [r for r, score in compatibility_scores.items() if score > 0.7]

                if good_roles:
                    # Find the least loaded role among those with good compatibility
                    least_loaded_role = min(
                        good_roles,
                        key=lambda r: role_workloads[r]
                    )

                    # Assign task to this role
                    workload_assignment[least_loaded_role].append(subtask_id)
                    role_workloads[least_loaded_role] += subtask.get("estimated_effort", 1.0)
                    continue

            # If not using skill-based assignment or no good skill matches,
            # fall back to suitable roles
            if suitable_roles and any(role in available_roles for role in suitable_roles):
                # Filter to only include available roles
                available_suitable_roles = [role for role in suitable_roles if role in available_roles]

                if available_suitable_roles:
                    # Find the least loaded suitable role
                    least_loaded_role = min(
                        available_suitable_roles,
                        key=lambda r: role_workloads[r]
                    )

                    # Assign task to this role
                    workload_assignment[least_loaded_role].append(subtask_id)
                    role_workloads[least_loaded_role] += subtask.get("estimated_effort", 1.0)
                    continue

            # If no suitable roles or none are available, assign to the least loaded role
            least_loaded_role = min(
                available_roles,
                key=lambda r: role_workloads[r]
            )

            workload_assignment[least_loaded_role].append(subtask_id)
            role_workloads[least_loaded_role] += subtask.get("estimated_effort", 1.0)

        # Second pass: check for workload imbalance and redistribute if necessary
        self._redistribute_workload(workload_assignment, role_workloads, subtasks)

        return workload_assignment

    def _redistribute_workload(
        self,
        workload_assignment: Dict[str, List[str]],
        role_workloads: Dict[str, float],
        subtasks: List[Dict[str, Any]]
    ) -> None:
        """
        Redistribute workload to balance it better.

        Args:
            workload_assignment: Current workload assignment
            role_workloads: Current workload per role
            subtasks: List of subtasks
        """
        # Create a lookup for subtasks by ID
        subtask_lookup = {subtask["id"]: subtask for subtask in subtasks}

        # Calculate average workload
        avg_workload = sum(role_workloads.values()) / len(role_workloads)

        # Calculate standard deviation
        std_dev = (sum((load - avg_workload) ** 2 for load in role_workloads.values()) / len(role_workloads)) ** 0.5

        # If standard deviation is low, workload is already balanced
        if std_dev < 0.5:  # Threshold for considering workload balanced
            return

        logger.info(f"Redistributing workload (std dev: {std_dev:.2f})")

        # Identify overloaded and underloaded roles
        overloaded_roles = [role for role, load in role_workloads.items() if load > avg_workload + std_dev]
        underloaded_roles = [role for role, load in role_workloads.items() if load < avg_workload - std_dev]

        if not overloaded_roles or not underloaded_roles:
            return

        # For each overloaded role, try to move tasks to underloaded roles
        for overloaded_role in overloaded_roles:
            # Sort tasks by effort (smallest first, easier to move)
            tasks = sorted(
                workload_assignment[overloaded_role],
                key=lambda t: subtask_lookup.get(t, {}).get("estimated_effort", 1.0)
            )

            # Try to move tasks until workload is balanced
            for task_id in tasks:
                # Skip critical path tasks and bottleneck tasks
                if subtask_lookup.get(task_id, {}).get("on_critical_path", False) or \
                   subtask_lookup.get(task_id, {}).get("is_bottleneck", False):
                    continue

                # Find the most underloaded role
                underloaded_role = min(
                    underloaded_roles,
                    key=lambda r: role_workloads[r]
                )

                # Check if moving this task would improve balance
                task_effort = subtask_lookup.get(task_id, {}).get("estimated_effort", 1.0)

                new_overloaded = role_workloads[overloaded_role] - task_effort
                new_underloaded = role_workloads[underloaded_role] + task_effort

                # If this improves balance, move the task
                if abs(new_overloaded - new_underloaded) < abs(role_workloads[overloaded_role] - role_workloads[underloaded_role]):
                    # Move task
                    workload_assignment[overloaded_role].remove(task_id)
                    workload_assignment[underloaded_role].append(task_id)

                    # Update workloads
                    role_workloads[overloaded_role] = new_overloaded
                    role_workloads[underloaded_role] = new_underloaded

                    logger.debug(f"Moved task {task_id} from {overloaded_role} to {underloaded_role}")

                    # Check if role is no longer overloaded
                    if new_overloaded <= avg_workload + std_dev / 2:
                        break

    def _is_complex_task(self, task_description: str) -> bool:
        """
        Determine if a task is complex enough to warrant further decomposition.

        Args:
            task_description: Task description

        Returns:
            True if the task is complex, False otherwise
        """
        # Simple heuristics to determine complexity

        # Length-based complexity
        if len(task_description.split()) > 20:
            return True

        # Keyword-based complexity
        complex_keywords = [
            "analyze", "research", "design", "develop", "implement",
            "evaluate", "compare", "synthesize", "create", "plan",
            "multiple", "complex", "comprehensive", "detailed"
        ]

        for keyword in complex_keywords:
            if keyword in task_description.lower():
                return True

        # Conjunction-based complexity
        if " and " in task_description or " or " in task_description:
            return True

        return False

    def visualize_task_graph(self, output_file: Optional[str] = None) -> Optional[str]:
        """
        Visualize the task dependency graph.

        Args:
            output_file: Optional file path to save the visualization

        Returns:
            Path to the saved visualization file, or None if visualization failed
        """
        try:
            import matplotlib.pyplot as plt

            # Create a new figure
            plt.figure(figsize=(12, 8))

            # Create a layout for the graph
            pos = nx.spring_layout(self.task_graph)

            # Get node colors based on depth
            node_colors = []
            for node in self.task_graph.nodes:
                depth = self.task_graph.nodes[node].get("depth", 0)
                if depth == 0:
                    node_colors.append("lightblue")  # Main task
                elif depth == 1:
                    node_colors.append("lightgreen")  # First-level subtasks
                else:
                    node_colors.append("lightsalmon")  # Deeper subtasks

            # Draw the graph
            nx.draw(
                self.task_graph,
                pos,
                with_labels=True,
                node_color=node_colors,
                node_size=2000,
                font_size=10,
                arrows=True
            )

            # Add edge labels for dependencies
            edge_labels = {(u, v): "depends on" for u, v in self.task_graph.edges}
            nx.draw_networkx_edge_labels(
                self.task_graph,
                pos,
                edge_labels=edge_labels,
                font_size=8
            )

            # Add a title
            plt.title("Task Dependency Graph")

            # Save or show the visualization
            if output_file:
                plt.savefig(output_file)
                logger.info(f"Task graph visualization saved to {output_file}")
                return output_file
            else:
                plt.show()
                return None

        except ImportError as e:
            logger.error(f"Visualization requires matplotlib: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Error visualizing task graph: {str(e)}")
            return None

    def get_execution_order(self) -> List[str]:
        """
        Get the optimal execution order for subtasks based on dependencies.

        Returns:
            List of subtask IDs in execution order
        """
        try:
            # Use topological sort to get execution order
            execution_order = list(nx.topological_sort(self.task_graph))

            # Remove the main task from the execution order
            main_tasks = [node for node, data in self.task_graph.nodes(data=True)
                         if data.get("type") == "main_task"]

            for main_task in main_tasks:
                if main_task in execution_order:
                    execution_order.remove(main_task)

            return execution_order

        except nx.NetworkXUnfeasible:
            logger.error("Task graph contains cycles, cannot determine execution order")
            # Try to resolve cycles
            self._resolve_dependency_cycles()

            # Try again after resolving cycles
            try:
                execution_order = list(nx.topological_sort(self.task_graph))

                # Remove the main task from the execution order
                main_tasks = [node for node, data in self.task_graph.nodes(data=True)
                             if data.get("type") == "main_task"]

                for main_task in main_tasks:
                    if main_task in execution_order:
                        execution_order.remove(main_task)

                return execution_order
            except nx.NetworkXUnfeasible:
                # Fallback: return nodes sorted by in-degree (number of dependencies)
                nodes_with_indegree = [(node, self.task_graph.in_degree(node))
                                      for node in self.task_graph.nodes
                                      if self.task_graph.nodes[node].get("type") != "main_task"]

                sorted_nodes = [node for node, _ in sorted(nodes_with_indegree, key=lambda x: x[1])]
                return sorted_nodes

    def get_parallel_execution_plan(self) -> List[List[str]]:
        """
        Get a parallel execution plan that groups tasks that can be executed in parallel.

        Returns:
            List of lists, where each inner list contains task IDs that can be executed in parallel
        """
        if not self.dependency_aware_scheduling:
            logger.warning("Dependency-aware scheduling is disabled, enabling it now")
            self.dependency_aware_scheduling = True

        # Get the execution order
        execution_order = self.get_execution_order()

        # Create a parallel execution plan
        parallel_plan = []
        executed_tasks = set()

        while execution_order:
            # Find tasks that can be executed in parallel
            parallel_tasks = []

            for task_id in execution_order[:]:
                # Check if all dependencies are executed
                dependencies = list(self.task_graph.predecessors(task_id))

                # Filter out main tasks from dependencies
                dependencies = [dep for dep in dependencies
                               if self.task_graph.nodes[dep].get("type") != "main_task"]

                if all(dep in executed_tasks for dep in dependencies):
                    parallel_tasks.append(task_id)
                    execution_order.remove(task_id)

            if parallel_tasks:
                parallel_plan.append(parallel_tasks)
                executed_tasks.update(parallel_tasks)
            else:
                # If no tasks can be executed, there might be a cycle
                # Add the first task to break the cycle
                if execution_order:
                    task_id = execution_order.pop(0)
                    parallel_plan.append([task_id])
                    executed_tasks.add(task_id)

        return parallel_plan

    def generate_execution_timeline(self, task_durations: Dict[str, float] = None) -> Dict[str, Dict[str, float]]:
        """
        Generate an execution timeline for tasks based on dependencies and durations.

        Args:
            task_durations: Dictionary mapping task IDs to estimated durations
                If not provided, uses estimated_effort from task metadata

        Returns:
            Dictionary mapping task IDs to dictionaries with start and end times
        """
        if not self.dependency_aware_scheduling:
            logger.warning("Dependency-aware scheduling is disabled, enabling it now")
            self.dependency_aware_scheduling = True

        # Get the execution order
        execution_order = self.get_execution_order()

        # Initialize timeline
        timeline = {}

        # Initialize task durations if not provided
        if task_durations is None:
            task_durations = {}
            for task_id in execution_order:
                # Use estimated_effort as duration, default to 1.0
                task_durations[task_id] = self.task_graph.nodes[task_id].get("estimated_effort", 1.0)

        # Calculate earliest start times
        earliest_start = {}
        for task_id in execution_order:
            # Get dependencies
            dependencies = list(self.task_graph.predecessors(task_id))

            # Filter out main tasks from dependencies
            dependencies = [dep for dep in dependencies
                           if self.task_graph.nodes[dep].get("type") != "main_task"]

            if not dependencies:
                # No dependencies, can start at time 0
                earliest_start[task_id] = 0.0
            else:
                # Start after all dependencies are finished
                max_end_time = max(
                    earliest_start.get(dep, 0.0) + task_durations.get(dep, 1.0)
                    for dep in dependencies
                )
                earliest_start[task_id] = max_end_time

        # Create timeline
        for task_id in execution_order:
            start_time = earliest_start[task_id]
            duration = task_durations.get(task_id, 1.0)
            end_time = start_time + duration

            timeline[task_id] = {
                "start": start_time,
                "end": end_time,
                "duration": duration
            }

        # Store the timeline for future reference
        self.execution_timeline = timeline

        return timeline

    def identify_critical_path(self) -> List[str]:
        """
        Identify the critical path in the task graph.

        Returns:
            List of task IDs on the critical path
        """
        if not self.execution_timeline:
            # Generate timeline if not already generated
            self.generate_execution_timeline()

        # Calculate project end time
        project_end = max(data["end"] for data in self.execution_timeline.values())

        # Initialize critical path
        critical_path = []

        # Start with tasks that end at project end time
        current_tasks = [
            task_id for task_id, data in self.execution_timeline.items()
            if abs(data["end"] - project_end) < 0.001  # Use small epsilon for float comparison
        ]

        while current_tasks:
            # Add current tasks to critical path
            critical_path.extend(current_tasks)

            # Find predecessors of current tasks
            predecessors = []
            for task_id in current_tasks:
                # Get dependencies
                deps = list(self.task_graph.predecessors(task_id))

                # Filter out main tasks and tasks already in critical path
                deps = [
                    dep for dep in deps
                    if self.task_graph.nodes[dep].get("type") != "main_task"
                    and dep not in critical_path
                ]

                # Find predecessors that end exactly when this task starts
                task_start = self.execution_timeline[task_id]["start"]
                for dep in deps:
                    if abs(self.execution_timeline[dep]["end"] - task_start) < 0.001:
                        predecessors.append(dep)

            # Update current tasks
            current_tasks = predecessors

        # Reverse to get path from start to end
        critical_path.reverse()

        # Update task graph with critical path information
        for task_id in self.task_graph.nodes:
            if self.task_graph.nodes[task_id].get("type") != "main_task":
                self.task_graph.nodes[task_id]["on_critical_path"] = task_id in critical_path

        return critical_path

    def identify_bottlenecks(self) -> List[str]:
        """
        Identify bottleneck tasks in the task graph.

        Returns:
            List of task IDs that are bottlenecks
        """
        # Get the execution order
        execution_order = self.get_execution_order()

        # Calculate in-degree and out-degree for each task
        in_degrees = {}
        out_degrees = {}

        for task_id in execution_order:
            in_degree = self.task_graph.in_degree(task_id)
            out_degree = self.task_graph.out_degree(task_id)

            in_degrees[task_id] = in_degree
            out_degrees[task_id] = out_degree

        # Identify bottlenecks (high in-degree and high out-degree)
        bottlenecks = []

        for task_id in execution_order:
            in_degree = in_degrees[task_id]
            out_degree = out_degrees[task_id]

            # A bottleneck has multiple incoming and outgoing dependencies
            if in_degree > 1 and out_degree > 1:
                bottlenecks.append(task_id)
                self.task_graph.nodes[task_id]["is_bottleneck"] = True
            else:
                self.task_graph.nodes[task_id]["is_bottleneck"] = False

        return bottlenecks

    def optimize_execution_plan(self) -> Dict[str, Any]:
        """
        Optimize the execution plan by identifying critical path, bottlenecks,
        and generating a parallel execution plan.

        Returns:
            Dictionary with optimization results
        """
        if not self.dependency_aware_scheduling:
            logger.warning("Dependency-aware scheduling is disabled, enabling it now")
            self.dependency_aware_scheduling = True

        # Generate execution timeline
        timeline = self.generate_execution_timeline()

        # Identify critical path
        critical_path = self.identify_critical_path()

        # Identify bottlenecks
        bottlenecks = self.identify_bottlenecks()

        # Generate parallel execution plan
        parallel_plan = self.get_parallel_execution_plan()

        # Calculate project duration
        project_duration = max(data["end"] for data in timeline.values())

        # Calculate parallelism metrics
        max_parallelism = max(len(group) for group in parallel_plan)
        avg_parallelism = len([task for group in parallel_plan for task in group]) / len(parallel_plan)

        # Create optimization result
        result = {
            "timeline": timeline,
            "critical_path": critical_path,
            "bottlenecks": bottlenecks,
            "parallel_plan": parallel_plan,
            "project_duration": project_duration,
            "max_parallelism": max_parallelism,
            "avg_parallelism": avg_parallelism
        }

        return result

    def _calculate_task_complexity(self, task_description: str) -> float:
        """
        Calculate the complexity of a task based on its description.

        Args:
            task_description: Description of the task

        Returns:
            Complexity score between 0.0 and 1.0
        """
        # Initialize complexity score
        complexity = 0.0

        # Check length (longer tasks are generally more complex)
        words = task_description.split()
        length_score = min(1.0, len(words) / 200)  # Cap at 200 words
        complexity += 0.3 * length_score

        # Check for complexity indicators
        complexity_indicators = [
            "complex", "complicated", "difficult", "challenging", "advanced",
            "analyze", "research", "design", "develop", "implement", "optimize",
            "integrate", "coordinate", "multiple", "various", "several"
        ]

        indicator_count = sum(1 for indicator in complexity_indicators if indicator in task_description.lower())
        indicator_score = min(1.0, indicator_count / 5)  # Cap at 5 indicators
        complexity += 0.3 * indicator_score

        # Check for technical terms
        technical_terms = [
            "algorithm", "database", "system", "architecture", "framework",
            "interface", "protocol", "module", "component", "function",
            "class", "object", "method", "variable", "parameter",
            "api", "sdk", "library", "dependency", "integration"
        ]

        technical_count = sum(1 for term in technical_terms if term in task_description.lower())
        technical_score = min(1.0, technical_count / 5)  # Cap at 5 technical terms
        complexity += 0.2 * technical_score

        # Check for dependency indicators
        dependency_indicators = [
            "after", "before", "then", "first", "second", "next", "previous",
            "depends", "dependent", "prerequisite", "requires", "following",
            "once", "when", "if", "condition", "based on"
        ]

        dependency_count = sum(1 for indicator in dependency_indicators if indicator in task_description.lower())
        dependency_score = min(1.0, dependency_count / 5)  # Cap at 5 dependency indicators
        complexity += 0.2 * dependency_score

        logger.debug(f"Task complexity: {complexity:.2f} (length: {length_score:.2f}, "
                    f"indicators: {indicator_score:.2f}, technical: {technical_score:.2f}, "
                    f"dependencies: {dependency_score:.2f})")

        return complexity

    def _select_best_strategy(self, task_description: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Select the best decomposition strategy based on task characteristics and past performance.

        Args:
            task_description: Description of the task
            context: Additional context for decomposition

        Returns:
            Name of the selected strategy
        """
        # Calculate task characteristics
        task_complexity = self._calculate_task_complexity(task_description)
        task_size = len(task_description.split())
        has_dependencies = "after" in task_description.lower() or "before" in task_description.lower()
        is_parallel = "simultaneously" in task_description.lower() or "parallel" in task_description.lower()

        # Calculate strategy scores based on task characteristics and past performance
        scores = {}

        # Hierarchical is good for complex tasks with many subtasks
        scores["hierarchical"] = (
            0.7 * task_complexity +
            0.2 * min(1.0, task_size / 200) +
            0.1 * self.strategy_performance["hierarchical"]["success_rate"]
        )

        # Graph-based is good for tasks with dependencies
        scores["graph"] = (
            0.5 * task_complexity +
            0.3 * (1.0 if has_dependencies else 0.3) +
            0.2 * self.strategy_performance["graph"]["success_rate"]
        )

        # Sequential is good for step-by-step tasks
        scores["sequential"] = (
            0.4 * (1.0 if "step" in task_description.lower() else 0.3) +
            0.4 * (1.0 if has_dependencies else 0.3) +
            0.2 * self.strategy_performance["sequential"]["success_rate"]
        )

        # Parallel is good for tasks that can be done simultaneously
        scores["parallel"] = (
            0.6 * (1.0 if is_parallel else 0.2) +
            0.2 * (1.0 - (1.0 if has_dependencies else 0.0)) +
            0.2 * self.strategy_performance["parallel"]["success_rate"]
        )

        # Select the strategy with the highest score
        best_strategy = max(scores.items(), key=lambda x: x[1])[0]

        logger.info(f"Strategy scores: {scores}, selected: {best_strategy}")
        return best_strategy

    def _process_decomposition_feedback(self, feedback: Dict[str, Any]) -> None:
        """
        Process feedback on previous decompositions to improve future decompositions.

        Args:
            feedback: Dictionary containing feedback on previous decompositions
        """
        if not feedback:
            return

        task_id = feedback.get("task_id")
        strategy = feedback.get("strategy")
        success_rating = feedback.get("success_rating", 0.0)

        if not task_id or not strategy or strategy not in self.strategy_performance:
            logger.warning(f"Invalid feedback: {feedback}")
            return

        # Store feedback in history
        if task_id not in self.feedback_history:
            self.feedback_history[task_id] = []

        self.feedback_history[task_id].append({
            "strategy": strategy,
            "success_rating": success_rating,
            "timestamp": time.time()
        })

        # Update strategy performance metrics
        current_rate = self.strategy_performance[strategy]["success_rate"]
        current_samples = self.strategy_performance[strategy]["samples"]

        # Weighted average with more weight on recent feedback
        new_rate = (current_rate * current_samples + success_rating * 2) / (current_samples + 2)

        self.strategy_performance[strategy]["success_rate"] = new_rate
        self.strategy_performance[strategy]["samples"] = current_samples + 1

        logger.info(f"Updated {strategy} performance: {new_rate:.2f} ({current_samples + 1} samples)")

    def _evaluate_decomposition_quality(self, decomposition_result: Dict[str, Any]) -> float:
        """
        Evaluate the quality of a decomposition result.

        Args:
            decomposition_result: Result of a task decomposition

        Returns:
            Quality score between 0.0 and 1.0
        """
        if not decomposition_result:
            return 0.0

        subtasks = decomposition_result.get("subtasks", [])
        dependencies = decomposition_result.get("dependencies", {})

        # No subtasks means poor decomposition
        if not subtasks:
            return 0.0

        # Calculate various quality metrics
        num_subtasks = len(subtasks)

        # Check if number of subtasks is reasonable
        subtask_count_score = 0.0
        if self.min_subtasks_per_level <= num_subtasks <= self.max_subtasks_per_level:
            subtask_count_score = 1.0
        elif num_subtasks < self.min_subtasks_per_level:
            subtask_count_score = num_subtasks / self.min_subtasks_per_level
        else:
            subtask_count_score = self.max_subtasks_per_level / num_subtasks

        # Check subtask descriptions
        description_scores = []
        for subtask in subtasks:
            desc = subtask.get("description", "")
            # Longer descriptions are generally better
            length_score = min(1.0, len(desc.split()) / 10)
            # Descriptions should be specific
            specificity_score = 0.2
            if any(keyword in desc.lower() for keyword in ["analyze", "research", "create", "develop", "implement"]):
                specificity_score = 0.8
            description_scores.append((length_score + specificity_score) / 2)

        avg_description_score = sum(description_scores) / len(description_scores) if description_scores else 0.0

        # Check dependency structure
        dependency_score = 0.0
        if dependencies:
            # Calculate ratio of tasks with dependencies
            tasks_with_deps = sum(1 for deps in dependencies.values() if deps)
            dependency_ratio = tasks_with_deps / num_subtasks

            # Some dependencies are good, but not too many
            if 0.2 <= dependency_ratio <= 0.8:
                dependency_score = 1.0
            elif dependency_ratio < 0.2:
                dependency_score = dependency_ratio / 0.2
            else:
                dependency_score = 0.8 / dependency_ratio

        # Calculate overall quality score
        quality_score = (
            0.4 * subtask_count_score +
            0.4 * avg_description_score +
            0.2 * dependency_score
        )

        logger.info(f"Decomposition quality: {quality_score:.2f} (subtasks: {subtask_count_score:.2f}, "
                   f"descriptions: {avg_description_score:.2f}, dependencies: {dependency_score:.2f})")

        return quality_score

    def _redecompose_task(
        self,
        main_task_id: str,
        task_description: str,
        available_roles: List[str],
        context: Optional[Dict[str, Any]] = None,
        previous_strategy: str = None
    ) -> Dict[str, Any]:
        """
        Re-decompose a task using a different strategy when the initial decomposition is poor.

        Args:
            main_task_id: ID of the main task
            task_description: Description of the task
            available_roles: List of available agent roles
            context: Additional context for decomposition
            previous_strategy: Previously used strategy to avoid

        Returns:
            Improved decomposition result
        """
        # Reset the task graph for this task
        if main_task_id in self.task_graph:
            # Keep only the main task node
            nodes_to_remove = [n for n in self.task_graph.neighbors(main_task_id)]
            for node in nodes_to_remove:
                self.task_graph.remove_node(node)

        # Select a different strategy
        available_strategies = ["hierarchical", "graph", "sequential", "parallel"]
        if previous_strategy in available_strategies:
            available_strategies.remove(previous_strategy)

        # Choose the best performing remaining strategy
        best_strategy = max(
            available_strategies,
            key=lambda s: self.strategy_performance[s]["success_rate"]
        )

        logger.info(f"Re-decomposing task using {best_strategy} strategy (previous: {previous_strategy})")

        # Apply the new strategy
        if best_strategy == "hierarchical":
            result = self._hierarchical_decomposition(
                main_task_id,
                task_description,
                available_roles,
                context
            )
        elif best_strategy == "graph":
            result = self._graph_based_decomposition(
                main_task_id,
                task_description,
                available_roles,
                context
            )
        elif best_strategy == "sequential":
            result = self._sequential_decomposition(
                main_task_id,
                task_description,
                available_roles,
                context
            )
        elif best_strategy == "parallel":
            result = self._parallel_decomposition(
                main_task_id,
                task_description,
                available_roles,
                context
            )
        else:
            # Fallback to hierarchical if something went wrong
            result = self._hierarchical_decomposition(
                main_task_id,
                task_description,
                available_roles,
                context
            )

        # Update metadata
        if "metadata" not in result:
            result["metadata"] = {}
        result["metadata"]["strategy_used"] = best_strategy
        result["metadata"]["is_redecomposition"] = True
        result["metadata"]["previous_strategy"] = previous_strategy

        # Evaluate the new decomposition
        quality_score = self._evaluate_decomposition_quality(result)
        result["metadata"]["quality_score"] = quality_score

        return result
