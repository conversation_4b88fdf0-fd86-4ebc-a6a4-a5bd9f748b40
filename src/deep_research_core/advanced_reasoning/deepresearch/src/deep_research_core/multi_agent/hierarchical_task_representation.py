"""
Hierarchical task representation for TaskDecomposer.

This module provides classes for representing hierarchical task structures
and managing task dependencies in a more efficient and flexible way.
"""

import uuid
from typing import Dict, List, Any, Optional, Set, Tuple
import networkx as nx
from ..utils.structured_logging import get_logger

logger = get_logger(__name__)


class TaskNode:
    """
    Represents a node in the hierarchical task structure.
    
    A TaskNode can be either a main task or a subtask, and can have
    parent-child relationships as well as dependencies with other nodes.
    """
    
    def __init__(
        self,
        task_id: str,
        description: str,
        node_type: str = "subtask",
        depth: int = 0,
        suitable_roles: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize a task node.
        
        Args:
            task_id: Unique identifier for the task
            description: Description of the task
            node_type: Type of node ("main_task" or "subtask")
            depth: Depth in the task hierarchy (0 for main task)
            suitable_roles: List of agent roles suitable for this task
            metadata: Additional metadata for the task
        """
        self.task_id = task_id
        self.description = description
        self.node_type = node_type
        self.depth = depth
        self.suitable_roles = suitable_roles or []
        self.metadata = metadata or {}
        
        # Relationships
        self.parent = None
        self.children = []
        self.dependencies = []
        self.dependents = []
        
        # Execution state
        self.status = "pending"  # pending, in_progress, completed, failed
        self.assigned_to = None
        self.estimated_effort = 1.0  # Default effort estimation
        self.priority = 1.0  # Default priority
        
        logger.debug(f"Created TaskNode: {task_id} at depth {depth}")
    
    def add_child(self, child_node: 'TaskNode') -> None:
        """
        Add a child node to this node.
        
        Args:
            child_node: Child task node
        """
        if child_node not in self.children:
            self.children.append(child_node)
            child_node.parent = self
            logger.debug(f"Added child {child_node.task_id} to {self.task_id}")
    
    def add_dependency(self, dependency_node: 'TaskNode') -> None:
        """
        Add a dependency to this node.
        
        Args:
            dependency_node: Node that this node depends on
        """
        if dependency_node not in self.dependencies:
            self.dependencies.append(dependency_node)
            dependency_node.dependents.append(self)
            logger.debug(f"Added dependency {dependency_node.task_id} to {self.task_id}")
    
    def remove_dependency(self, dependency_node: 'TaskNode') -> None:
        """
        Remove a dependency from this node.
        
        Args:
            dependency_node: Node to remove as a dependency
        """
        if dependency_node in self.dependencies:
            self.dependencies.remove(dependency_node)
            if self in dependency_node.dependents:
                dependency_node.dependents.remove(self)
            logger.debug(f"Removed dependency {dependency_node.task_id} from {self.task_id}")
    
    def get_all_dependencies(self, recursive: bool = False) -> List['TaskNode']:
        """
        Get all dependencies of this node.
        
        Args:
            recursive: Whether to include dependencies of dependencies
            
        Returns:
            List of dependency nodes
        """
        if not recursive:
            return self.dependencies.copy()
        
        all_deps = set(self.dependencies)
        for dep in self.dependencies:
            all_deps.update(dep.get_all_dependencies(recursive=True))
        
        return list(all_deps)
    
    def get_all_dependents(self, recursive: bool = False) -> List['TaskNode']:
        """
        Get all nodes that depend on this node.
        
        Args:
            recursive: Whether to include dependents of dependents
            
        Returns:
            List of dependent nodes
        """
        if not recursive:
            return self.dependents.copy()
        
        all_deps = set(self.dependents)
        for dep in self.dependents:
            all_deps.update(dep.get_all_dependents(recursive=True))
        
        return list(all_deps)
    
    def get_ancestors(self) -> List['TaskNode']:
        """
        Get all ancestor nodes (parents, grandparents, etc.).
        
        Returns:
            List of ancestor nodes
        """
        ancestors = []
        current = self.parent
        
        while current:
            ancestors.append(current)
            current = current.parent
        
        return ancestors
    
    def get_descendants(self) -> List['TaskNode']:
        """
        Get all descendant nodes (children, grandchildren, etc.).
        
        Returns:
            List of descendant nodes
        """
        descendants = []
        
        def collect_descendants(node):
            for child in node.children:
                descendants.append(child)
                collect_descendants(child)
        
        collect_descendants(self)
        return descendants
    
    def is_blocked(self) -> bool:
        """
        Check if this task is blocked by any dependencies.
        
        Returns:
            True if any dependencies are not completed, False otherwise
        """
        return any(dep.status != "completed" for dep in self.dependencies)
    
    def can_execute(self) -> bool:
        """
        Check if this task can be executed.
        
        Returns:
            True if all dependencies are completed and task is pending
        """
        return self.status == "pending" and not self.is_blocked()
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the task node to a dictionary.
        
        Returns:
            Dictionary representation of the task node
        """
        return {
            "id": self.task_id,
            "description": self.description,
            "type": self.node_type,
            "depth": self.depth,
            "suitable_roles": self.suitable_roles,
            "status": self.status,
            "assigned_to": self.assigned_to,
            "estimated_effort": self.estimated_effort,
            "priority": self.priority,
            "parent": self.parent.task_id if self.parent else None,
            "children": [child.task_id for child in self.children],
            "dependencies": [dep.task_id for dep in self.dependencies],
            "dependents": [dep.task_id for dep in self.dependents],
            "metadata": self.metadata
        }


class HierarchicalTaskStructure:
    """
    Represents a hierarchical structure of tasks with dependencies.
    
    This class provides methods for managing a hierarchical task structure,
    including adding, removing, and querying tasks, as well as managing
    dependencies between tasks.
    """
    
    def __init__(self):
        """Initialize the hierarchical task structure."""
        self.nodes = {}  # Map of task_id to TaskNode
        self.root_nodes = []  # List of root nodes (main tasks)
        self.task_graph = nx.DiGraph()  # Dependency graph
        
        logger.info("Initialized HierarchicalTaskStructure")
    
    def add_task(
        self,
        description: str,
        task_id: Optional[str] = None,
        node_type: str = "subtask",
        depth: int = 0,
        suitable_roles: Optional[List[str]] = None,
        parent_id: Optional[str] = None,
        dependency_ids: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Add a task to the structure.
        
        Args:
            description: Description of the task
            task_id: Optional task ID (generated if not provided)
            node_type: Type of node ("main_task" or "subtask")
            depth: Depth in the task hierarchy
            suitable_roles: List of agent roles suitable for this task
            parent_id: ID of the parent task
            dependency_ids: List of task IDs that this task depends on
            metadata: Additional metadata for the task
            
        Returns:
            ID of the added task
        """
        # Generate task ID if not provided
        if not task_id:
            task_id = f"task_{str(uuid.uuid4())[:8]}"
        
        # Create the task node
        node = TaskNode(
            task_id=task_id,
            description=description,
            node_type=node_type,
            depth=depth,
            suitable_roles=suitable_roles,
            metadata=metadata
        )
        
        # Add to nodes dictionary
        self.nodes[task_id] = node
        
        # Add to task graph
        self.task_graph.add_node(
            task_id,
            description=description,
            type=node_type,
            depth=depth
        )
        
        # Set parent relationship
        if parent_id and parent_id in self.nodes:
            parent_node = self.nodes[parent_id]
            parent_node.add_child(node)
            self.task_graph.add_edge(parent_id, task_id, type="parent-child")
        else:
            # If no parent, this is a root node
            self.root_nodes.append(node)
        
        # Set dependencies
        if dependency_ids:
            for dep_id in dependency_ids:
                if dep_id in self.nodes:
                    dep_node = self.nodes[dep_id]
                    node.add_dependency(dep_node)
                    self.task_graph.add_edge(dep_id, task_id, type="dependency")
        
        logger.info(f"Added task {task_id} to hierarchical structure")
        return task_id
    
    def remove_task(self, task_id: str) -> bool:
        """
        Remove a task from the structure.
        
        Args:
            task_id: ID of the task to remove
            
        Returns:
            True if the task was removed, False otherwise
        """
        if task_id not in self.nodes:
            logger.warning(f"Task {task_id} not found, cannot remove")
            return False
        
        node = self.nodes[task_id]
        
        # Remove from parent's children
        if node.parent:
            node.parent.children.remove(node)
        
        # Remove from root nodes if applicable
        if node in self.root_nodes:
            self.root_nodes.remove(node)
        
        # Update dependencies
        for dep in node.dependencies:
            dep.dependents.remove(node)
        
        for dep in node.dependents:
            dep.dependencies.remove(node)
        
        # Remove children recursively
        for child in node.children.copy():
            self.remove_task(child.task_id)
        
        # Remove from nodes dictionary and task graph
        del self.nodes[task_id]
        self.task_graph.remove_node(task_id)
        
        logger.info(f"Removed task {task_id} from hierarchical structure")
        return True
    
    def get_task(self, task_id: str) -> Optional[TaskNode]:
        """
        Get a task by ID.
        
        Args:
            task_id: ID of the task to get
            
        Returns:
            TaskNode if found, None otherwise
        """
        return self.nodes.get(task_id)
    
    def get_all_tasks(self) -> List[TaskNode]:
        """
        Get all tasks in the structure.
        
        Returns:
            List of all task nodes
        """
        return list(self.nodes.values())
    
    def get_tasks_by_depth(self, depth: int) -> List[TaskNode]:
        """
        Get all tasks at a specific depth.
        
        Args:
            depth: Depth to filter by
            
        Returns:
            List of task nodes at the specified depth
        """
        return [node for node in self.nodes.values() if node.depth == depth]
    
    def get_tasks_by_status(self, status: str) -> List[TaskNode]:
        """
        Get all tasks with a specific status.
        
        Args:
            status: Status to filter by
            
        Returns:
            List of task nodes with the specified status
        """
        return [node for node in self.nodes.values() if node.status == status]
    
    def get_tasks_by_role(self, role: str) -> List[TaskNode]:
        """
        Get all tasks suitable for a specific role.
        
        Args:
            role: Role to filter by
            
        Returns:
            List of task nodes suitable for the specified role
        """
        return [node for node in self.nodes.values() if role in node.suitable_roles]
    
    def get_executable_tasks(self) -> List[TaskNode]:
        """
        Get all tasks that can be executed.
        
        Returns:
            List of task nodes that can be executed
        """
        return [node for node in self.nodes.values() if node.can_execute()]
    
    def update_task_status(self, task_id: str, status: str) -> bool:
        """
        Update the status of a task.
        
        Args:
            task_id: ID of the task to update
            status: New status for the task
            
        Returns:
            True if the status was updated, False otherwise
        """
        if task_id not in self.nodes:
            logger.warning(f"Task {task_id} not found, cannot update status")
            return False
        
        node = self.nodes[task_id]
        old_status = node.status
        node.status = status
        
        logger.info(f"Updated task {task_id} status from {old_status} to {status}")
        return True
    
    def assign_task(self, task_id: str, assignee: str) -> bool:
        """
        Assign a task to an agent.
        
        Args:
            task_id: ID of the task to assign
            assignee: ID or name of the assignee
            
        Returns:
            True if the task was assigned, False otherwise
        """
        if task_id not in self.nodes:
            logger.warning(f"Task {task_id} not found, cannot assign")
            return False
        
        node = self.nodes[task_id]
        node.assigned_to = assignee
        
        logger.info(f"Assigned task {task_id} to {assignee}")
        return True
    
    def get_execution_order(self) -> List[str]:
        """
        Get the optimal execution order for tasks based on dependencies.
        
        Returns:
            List of task IDs in execution order
        """
        try:
            # Use topological sort to get execution order
            execution_order = list(nx.topological_sort(self.task_graph))
            return execution_order
        except nx.NetworkXUnfeasible:
            logger.error("Task graph contains cycles, cannot determine execution order")
            # Fallback: return nodes sorted by in-degree (number of dependencies)
            nodes_with_indegree = [(node, self.task_graph.in_degree(node))
                                  for node in self.task_graph.nodes]
            
            sorted_nodes = [node for node, _ in sorted(nodes_with_indegree, key=lambda x: x[1])]
            return sorted_nodes
    
    def detect_cycles(self) -> List[List[str]]:
        """
        Detect cycles in the task dependency graph.
        
        Returns:
            List of cycles, where each cycle is a list of task IDs
        """
        try:
            cycles = list(nx.simple_cycles(self.task_graph))
            if cycles:
                logger.warning(f"Detected {len(cycles)} cycles in task graph")
            return cycles
        except Exception as e:
            logger.error(f"Error detecting cycles: {str(e)}")
            return []
    
    def resolve_cycles(self) -> int:
        """
        Resolve cycles in the task dependency graph by removing edges.
        
        Returns:
            Number of edges removed to resolve cycles
        """
        cycles = self.detect_cycles()
        edges_removed = 0
        
        for cycle in cycles:
            if len(cycle) > 1:
                # Remove the last edge in the cycle
                self.task_graph.remove_edge(cycle[-1], cycle[0])
                
                # Update the corresponding TaskNode objects
                if cycle[-1] in self.nodes and cycle[0] in self.nodes:
                    self.nodes[cycle[0]].remove_dependency(self.nodes[cycle[-1]])
                
                edges_removed += 1
                logger.info(f"Removed edge {cycle[-1]} -> {cycle[0]} to resolve cycle")
        
        return edges_removed
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the hierarchical task structure to a dictionary.
        
        Returns:
            Dictionary representation of the structure
        """
        tasks = {}
        dependencies = {}
        
        for task_id, node in self.nodes.items():
            tasks[task_id] = {
                "description": node.description,
                "type": node.node_type,
                "depth": node.depth,
                "suitable_roles": node.suitable_roles,
                "status": node.status,
                "assigned_to": node.assigned_to,
                "parent": node.parent.task_id if node.parent else None,
                "children": [child.task_id for child in node.children],
                "metadata": node.metadata
            }
            
            dependencies[task_id] = [dep.task_id for dep in node.dependencies]
        
        return {
            "tasks": tasks,
            "dependencies": dependencies,
            "root_nodes": [node.task_id for node in self.root_nodes]
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'HierarchicalTaskStructure':
        """
        Create a hierarchical task structure from a dictionary.
        
        Args:
            data: Dictionary representation of the structure
            
        Returns:
            HierarchicalTaskStructure instance
        """
        structure = cls()
        
        # First pass: create all nodes
        for task_id, task_data in data["tasks"].items():
            structure.add_task(
                description=task_data["description"],
                task_id=task_id,
                node_type=task_data.get("type", "subtask"),
                depth=task_data.get("depth", 0),
                suitable_roles=task_data.get("suitable_roles", []),
                metadata=task_data.get("metadata", {})
            )
            
            # Set status and assignee
            if "status" in task_data:
                structure.nodes[task_id].status = task_data["status"]
            if "assigned_to" in task_data:
                structure.nodes[task_id].assigned_to = task_data["assigned_to"]
        
        # Second pass: set parent-child relationships
        for task_id, task_data in data["tasks"].items():
            if "parent" in task_data and task_data["parent"]:
                parent_id = task_data["parent"]
                if parent_id in structure.nodes:
                    parent_node = structure.nodes[parent_id]
                    child_node = structure.nodes[task_id]
                    parent_node.add_child(child_node)
                    structure.task_graph.add_edge(parent_id, task_id, type="parent-child")
        
        # Third pass: set dependencies
        for task_id, dep_ids in data["dependencies"].items():
            for dep_id in dep_ids:
                if task_id in structure.nodes and dep_id in structure.nodes:
                    structure.nodes[task_id].add_dependency(structure.nodes[dep_id])
                    structure.task_graph.add_edge(dep_id, task_id, type="dependency")
        
        # Update root nodes
        structure.root_nodes = [
            structure.nodes[task_id] for task_id in data.get("root_nodes", [])
            if task_id in structure.nodes
        ]
        
        return structure
    
    def visualize(self, output_file: Optional[str] = None) -> Optional[str]:
        """
        Visualize the hierarchical task structure.
        
        Args:
            output_file: Optional file path to save the visualization
            
        Returns:
            Path to the saved visualization file, or None if visualization failed
        """
        try:
            import matplotlib.pyplot as plt
            
            # Create a new figure
            plt.figure(figsize=(12, 8))
            
            # Create a layout for the graph
            pos = nx.spring_layout(self.task_graph)
            
            # Get node colors based on depth
            node_colors = []
            for node in self.task_graph.nodes:
                if node in self.nodes:
                    depth = self.nodes[node].depth
                    if depth == 0:
                        node_colors.append("lightblue")  # Main task
                    elif depth == 1:
                        node_colors.append("lightgreen")  # First-level subtasks
                    else:
                        node_colors.append("lightsalmon")  # Deeper subtasks
                else:
                    node_colors.append("gray")  # Unknown nodes
            
            # Draw the graph
            nx.draw(
                self.task_graph,
                pos,
                with_labels=True,
                node_color=node_colors,
                node_size=2000,
                font_size=10,
                arrows=True
            )
            
            # Add edge labels for dependencies
            edge_labels = {}
            for u, v, data in self.task_graph.edges(data=True):
                edge_type = data.get("type", "dependency")
                edge_labels[(u, v)] = edge_type
            
            nx.draw_networkx_edge_labels(
                self.task_graph,
                pos,
                edge_labels=edge_labels,
                font_size=8
            )
            
            # Add a title
            plt.title("Hierarchical Task Structure")
            
            # Save or show the visualization
            if output_file:
                plt.savefig(output_file)
                logger.info(f"Task structure visualization saved to {output_file}")
                return output_file
            else:
                plt.show()
                return None
            
        except ImportError as e:
            logger.error(f"Visualization requires matplotlib: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Error visualizing task structure: {str(e)}")
            return None
