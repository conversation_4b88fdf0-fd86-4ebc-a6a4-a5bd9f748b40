"""
Dependency tracking for task decomposition.

This module provides utilities for tracking and managing dependencies
between tasks in a task decomposition system.
"""

import networkx as nx
from typing import Dict, List, Any, Optional, Set, Tuple
from ..utils.structured_logging import get_logger

logger = get_logger(__name__)


class DependencyTracker:
    """
    Tracks and manages dependencies between tasks.
    
    This class provides methods for adding, removing, and querying dependencies
    between tasks, as well as detecting and resolving dependency cycles.
    """
    
    def __init__(self):
        """Initialize the dependency tracker."""
        self.dependency_graph = nx.DiGraph()
        logger.info("Initialized DependencyTracker")
    
    def add_task(self, task_id: str, metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Add a task to the dependency graph.
        
        Args:
            task_id: ID of the task to add
            metadata: Optional metadata for the task
        """
        self.dependency_graph.add_node(task_id, **(metadata or {}))
        logger.debug(f"Added task {task_id} to dependency graph")
    
    def remove_task(self, task_id: str) -> None:
        """
        Remove a task from the dependency graph.
        
        Args:
            task_id: ID of the task to remove
        """
        if task_id in self.dependency_graph:
            self.dependency_graph.remove_node(task_id)
            logger.debug(f"Removed task {task_id} from dependency graph")
        else:
            logger.warning(f"Task {task_id} not found in dependency graph")
    
    def add_dependency(self, dependent_id: str, dependency_id: str, metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Add a dependency between tasks.
        
        Args:
            dependent_id: ID of the dependent task
            dependency_id: ID of the dependency task
            metadata: Optional metadata for the dependency
        """
        # Ensure both tasks exist in the graph
        if dependent_id not in self.dependency_graph:
            self.add_task(dependent_id)
        
        if dependency_id not in self.dependency_graph:
            self.add_task(dependency_id)
        
        # Add the dependency edge
        self.dependency_graph.add_edge(dependency_id, dependent_id, **(metadata or {}))
        logger.debug(f"Added dependency: {dependent_id} depends on {dependency_id}")
        
        # Check for cycles
        if not nx.is_directed_acyclic_graph(self.dependency_graph):
            logger.warning(f"Adding dependency {dependent_id} -> {dependency_id} created a cycle")
    
    def remove_dependency(self, dependent_id: str, dependency_id: str) -> None:
        """
        Remove a dependency between tasks.
        
        Args:
            dependent_id: ID of the dependent task
            dependency_id: ID of the dependency task
        """
        if self.dependency_graph.has_edge(dependency_id, dependent_id):
            self.dependency_graph.remove_edge(dependency_id, dependent_id)
            logger.debug(f"Removed dependency: {dependent_id} depends on {dependency_id}")
        else:
            logger.warning(f"Dependency {dependent_id} -> {dependency_id} not found")
    
    def get_dependencies(self, task_id: str) -> List[str]:
        """
        Get all dependencies of a task.
        
        Args:
            task_id: ID of the task
            
        Returns:
            List of dependency task IDs
        """
        if task_id not in self.dependency_graph:
            logger.warning(f"Task {task_id} not found in dependency graph")
            return []
        
        return list(self.dependency_graph.predecessors(task_id))
    
    def get_dependents(self, task_id: str) -> List[str]:
        """
        Get all tasks that depend on a task.
        
        Args:
            task_id: ID of the task
            
        Returns:
            List of dependent task IDs
        """
        if task_id not in self.dependency_graph:
            logger.warning(f"Task {task_id} not found in dependency graph")
            return []
        
        return list(self.dependency_graph.successors(task_id))
    
    def get_all_dependencies(self, task_id: str) -> List[str]:
        """
        Get all dependencies of a task, including indirect dependencies.
        
        Args:
            task_id: ID of the task
            
        Returns:
            List of all dependency task IDs
        """
        if task_id not in self.dependency_graph:
            logger.warning(f"Task {task_id} not found in dependency graph")
            return []
        
        # Get all ancestors in the dependency graph
        ancestors = nx.ancestors(self.dependency_graph, task_id)
        return list(ancestors)
    
    def get_all_dependents(self, task_id: str) -> List[str]:
        """
        Get all tasks that depend on a task, including indirect dependents.
        
        Args:
            task_id: ID of the task
            
        Returns:
            List of all dependent task IDs
        """
        if task_id not in self.dependency_graph:
            logger.warning(f"Task {task_id} not found in dependency graph")
            return []
        
        # Get all descendants in the dependency graph
        descendants = nx.descendants(self.dependency_graph, task_id)
        return list(descendants)
    
    def has_dependency(self, dependent_id: str, dependency_id: str) -> bool:
        """
        Check if a task depends on another task.
        
        Args:
            dependent_id: ID of the dependent task
            dependency_id: ID of the dependency task
            
        Returns:
            True if dependent_id depends on dependency_id, False otherwise
        """
        return self.dependency_graph.has_edge(dependency_id, dependent_id)
    
    def has_dependency_path(self, dependent_id: str, dependency_id: str) -> bool:
        """
        Check if there is a dependency path from one task to another.
        
        Args:
            dependent_id: ID of the dependent task
            dependency_id: ID of the dependency task
            
        Returns:
            True if there is a path from dependency_id to dependent_id, False otherwise
        """
        if dependent_id not in self.dependency_graph or dependency_id not in self.dependency_graph:
            return False
        
        return nx.has_path(self.dependency_graph, dependency_id, dependent_id)
    
    def detect_cycles(self) -> List[List[str]]:
        """
        Detect cycles in the dependency graph.
        
        Returns:
            List of cycles, where each cycle is a list of task IDs
        """
        try:
            cycles = list(nx.simple_cycles(self.dependency_graph))
            if cycles:
                logger.warning(f"Detected {len(cycles)} cycles in dependency graph")
            return cycles
        except Exception as e:
            logger.error(f"Error detecting cycles: {str(e)}")
            return []
    
    def resolve_cycles(self) -> int:
        """
        Resolve cycles in the dependency graph by removing edges.
        
        Returns:
            Number of edges removed to resolve cycles
        """
        cycles = self.detect_cycles()
        edges_removed = 0
        
        for cycle in cycles:
            if len(cycle) > 1:
                # Remove the last edge in the cycle
                self.dependency_graph.remove_edge(cycle[-1], cycle[0])
                edges_removed += 1
                logger.info(f"Removed edge {cycle[-1]} -> {cycle[0]} to resolve cycle")
        
        return edges_removed
    
    def get_execution_order(self) -> List[str]:
        """
        Get the optimal execution order for tasks based on dependencies.
        
        Returns:
            List of task IDs in execution order
        """
        try:
            # Use topological sort to get execution order
            execution_order = list(nx.topological_sort(self.dependency_graph))
            return execution_order
        except nx.NetworkXUnfeasible:
            logger.error("Dependency graph contains cycles, cannot determine execution order")
            # Resolve cycles and try again
            self.resolve_cycles()
            try:
                execution_order = list(nx.topological_sort(self.dependency_graph))
                return execution_order
            except nx.NetworkXUnfeasible:
                logger.error("Failed to resolve all cycles in dependency graph")
                # Fallback: return nodes sorted by in-degree (number of dependencies)
                nodes_with_indegree = [(node, self.dependency_graph.in_degree(node))
                                      for node in self.dependency_graph.nodes]
                
                sorted_nodes = [node for node, _ in sorted(nodes_with_indegree, key=lambda x: x[1])]
                return sorted_nodes
    
    def get_critical_path(self) -> List[str]:
        """
        Get the critical path in the dependency graph.
        
        The critical path is the longest path through the graph, representing
        the sequence of tasks that determines the minimum time needed to
        complete the entire task set.
        
        Returns:
            List of task IDs in the critical path
        """
        # Ensure the graph is acyclic
        if not nx.is_directed_acyclic_graph(self.dependency_graph):
            self.resolve_cycles()
        
        # Create a copy of the graph with edge weights
        weighted_graph = self.dependency_graph.copy()
        
        # Set default weights if not present
        for u, v, data in weighted_graph.edges(data=True):
            if "weight" not in data:
                weighted_graph.edges[u, v]["weight"] = 1
        
        # Find the longest path (critical path)
        try:
            # Negate weights to find longest path using shortest path algorithm
            for u, v in weighted_graph.edges():
                weighted_graph.edges[u, v]["weight"] = -weighted_graph.edges[u, v]["weight"]
            
            # Find all paths from sources to sinks
            sources = [node for node, in_degree in weighted_graph.in_degree() if in_degree == 0]
            sinks = [node for node, out_degree in weighted_graph.out_degree() if out_degree == 0]
            
            critical_path = []
            critical_path_length = 0
            
            for source in sources:
                for sink in sinks:
                    try:
                        path = nx.shortest_path(weighted_graph, source, sink, weight="weight")
                        path_length = nx.shortest_path_length(weighted_graph, source, sink, weight="weight")
                        
                        if path_length < critical_path_length:  # Remember weights are negated
                            critical_path = path
                            critical_path_length = path_length
                    except nx.NetworkXNoPath:
                        continue
            
            return critical_path
            
        except Exception as e:
            logger.error(f"Error finding critical path: {str(e)}")
            return []
    
    def get_parallel_tasks(self) -> List[List[str]]:
        """
        Group tasks that can be executed in parallel.
        
        Returns:
            List of lists, where each inner list contains tasks that can be executed in parallel
        """
        # Ensure the graph is acyclic
        if not nx.is_directed_acyclic_graph(self.dependency_graph):
            self.resolve_cycles()
        
        # Get the execution order
        execution_order = self.get_execution_order()
        
        # Group tasks by their longest path length from any source
        task_levels = {}
        
        # Find sources (tasks with no dependencies)
        sources = [node for node, in_degree in self.dependency_graph.in_degree() if in_degree == 0]
        
        # Calculate the longest path length for each task
        for task in execution_order:
            max_path_length = 0
            
            for source in sources:
                try:
                    path_length = len(nx.shortest_path(self.dependency_graph, source, task)) - 1
                    max_path_length = max(max_path_length, path_length)
                except nx.NetworkXNoPath:
                    continue
            
            if max_path_length not in task_levels:
                task_levels[max_path_length] = []
            
            task_levels[max_path_length].append(task)
        
        # Convert the dictionary to a list of lists
        parallel_groups = [task_levels[level] for level in sorted(task_levels.keys())]
        return parallel_groups
    
    def visualize(self, output_file: Optional[str] = None) -> Optional[str]:
        """
        Visualize the dependency graph.
        
        Args:
            output_file: Optional file path to save the visualization
            
        Returns:
            Path to the saved visualization file, or None if visualization failed
        """
        try:
            import matplotlib.pyplot as plt
            
            # Create a new figure
            plt.figure(figsize=(12, 8))
            
            # Create a layout for the graph
            pos = nx.spring_layout(self.dependency_graph)
            
            # Draw the graph
            nx.draw(
                self.dependency_graph,
                pos,
                with_labels=True,
                node_color="lightblue",
                node_size=2000,
                font_size=10,
                arrows=True
            )
            
            # Add edge labels
            edge_labels = {(u, v): "depends on" for u, v in self.dependency_graph.edges()}
            nx.draw_networkx_edge_labels(
                self.dependency_graph,
                pos,
                edge_labels=edge_labels,
                font_size=8
            )
            
            # Add a title
            plt.title("Task Dependency Graph")
            
            # Save or show the visualization
            if output_file:
                plt.savefig(output_file)
                logger.info(f"Dependency graph visualization saved to {output_file}")
                return output_file
            else:
                plt.show()
                return None
            
        except ImportError as e:
            logger.error(f"Visualization requires matplotlib: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Error visualizing dependency graph: {str(e)}")
            return None
