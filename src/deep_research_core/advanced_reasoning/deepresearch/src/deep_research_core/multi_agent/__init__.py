"""
Multi-Agent Collaboration module for Deep Research Core.

This module provides functionality for creating collaborative multi-agent systems
that can work together to solve complex tasks through communication and coordination.
"""

from .core import (
    MultiAgentSystem,
    Agent,
    AgentRole,
    SharedMemory,
    ConsensusMechanism,
    AgentMessage,
    AgentCommunicationProtocol,
    ReasoningAgent
)
from .task_decomposer import TaskDecomposer

from .domain_roles import (
    get_domain_role,
    get_available_domains,
    get_available_roles,
    get_all_domain_roles,
    DOMAIN_ROLES
)

try:
    from .bayesian_consensus import (
        BayesianConsensus,
        ExpertWeightedConsensus,
        MultiRoundConsensus
    )

    # Try to import enhanced consensus mechanisms
    try:
        from .enhanced_bayesian_consensus import EnhancedBayesianConsensus
    except ImportError:
        # Enhanced consensus mechanisms are optional
        pass
except ImportError:
    # Advanced consensus mechanisms are optional
    pass

__all__ = [
    # Core classes
    "MultiAgentSystem",
    "Agent",
    "AgentRole",
    "SharedMemory",
    "ConsensusMechanism",
    "TaskDecomposer",
    "AgentMessage",
    "AgentCommunicationProtocol",
    "ReasoningAgent",

    # Domain roles
    "get_domain_role",
    "get_available_domains",
    "get_available_roles",
    "get_all_domain_roles",
    "DOMAIN_ROLES",

    # Advanced consensus (if available)
    "BayesianConsensus",
    "ExpertWeightedConsensus",
    "MultiRoundConsensus",
    "EnhancedBayesianConsensus"
]