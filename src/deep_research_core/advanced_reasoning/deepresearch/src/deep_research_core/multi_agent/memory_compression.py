"""
Memory compression for SharedMemory.

This module provides compression capabilities for SharedMemory,
allowing more efficient storage and retrieval of data.
"""

import time
import json
import zlib
import base64
import re
from typing import Dict, Any, List, Optional, Union, Callable, Tuple
import logging

# Set up logging
logger = logging.getLogger(__name__)


class MemoryCompressor:
    """
    Memory compressor for SharedMemory.
    
    This class provides methods to compress and decompress data stored in SharedMemory,
    reducing memory usage and improving performance.
    """
    
    def __init__(
        self,
        compression_level: int = 6,
        min_size_for_compression: int = 1024,
        enable_binary_compression: bool = True,
        enable_json_optimization: bool = True,
        enable_text_compression: bool = True,
        enable_semantic_compression: bool = False,
        compression_threshold: float = 0.8,
        token_estimator: Optional[Callable[[str], int]] = None
    ):
        """
        Initialize the memory compressor.
        
        Args:
            compression_level: Compression level (0-9, higher = more compression but slower)
            min_size_for_compression: Minimum size in bytes for compression to be applied
            enable_binary_compression: Whether to enable binary compression
            enable_json_optimization: Whether to enable JSON optimization
            enable_text_compression: Whether to enable text compression
            enable_semantic_compression: Whether to enable semantic compression
            compression_threshold: Threshold for compression ratio (0.0-1.0)
            token_estimator: Function to estimate token count for a string
        """
        self.compression_level = compression_level
        self.min_size_for_compression = min_size_for_compression
        self.enable_binary_compression = enable_binary_compression
        self.enable_json_optimization = enable_json_optimization
        self.enable_text_compression = enable_text_compression
        self.enable_semantic_compression = enable_semantic_compression
        self.compression_threshold = compression_threshold
        self.token_estimator = token_estimator or self._default_token_estimator
        
        # Statistics
        self.stats = {
            "total_compressed": 0,
            "total_decompressed": 0,
            "bytes_before_compression": 0,
            "bytes_after_compression": 0,
            "compression_time": 0.0,
            "decompression_time": 0.0
        }
        
        logger.info(
            f"Initialized MemoryCompressor with compression_level={compression_level}, "
            f"min_size_for_compression={min_size_for_compression}, "
            f"enable_binary_compression={enable_binary_compression}, "
            f"enable_json_optimization={enable_json_optimization}, "
            f"enable_text_compression={enable_text_compression}, "
            f"enable_semantic_compression={enable_semantic_compression}"
        )
    
    def compress(self, value: Any, metadata: Dict[str, Any] = None) -> Tuple[Any, Dict[str, Any]]:
        """
        Compress a value for storage in SharedMemory.
        
        Args:
            value: Value to compress
            metadata: Metadata associated with the value
            
        Returns:
            Tuple of (compressed_value, updated_metadata)
        """
        start_time = time.time()
        
        # Make a copy of metadata to avoid modifying the original
        metadata = metadata.copy() if metadata else {}
        
        # Initialize compression metadata
        compression_info = {
            "compressed": False,
            "original_type": type(value).__name__,
            "compression_method": None
        }
        
        # Skip compression for None or small values
        if value is None:
            return value, metadata
        
        # Determine the best compression method based on value type
        if isinstance(value, str):
            compressed_value, compression_info = self._compress_text(value)
        elif isinstance(value, (dict, list)):
            compressed_value, compression_info = self._compress_json(value)
        elif isinstance(value, bytes):
            compressed_value, compression_info = self._compress_binary(value)
        else:
            # No compression for other types
            return value, metadata
        
        # Update metadata with compression info
        metadata["compression_info"] = compression_info
        
        # Update statistics
        compression_time = time.time() - start_time
        self.stats["compression_time"] += compression_time
        
        if compression_info["compressed"]:
            self.stats["total_compressed"] += 1
            
            # Log compression results
            logger.debug(
                f"Compressed {compression_info['original_type']} using {compression_info['compression_method']}, "
                f"ratio: {compression_info.get('compression_ratio', 'N/A')}, "
                f"time: {compression_time:.4f}s"
            )
        
        return compressed_value, metadata
    
    def decompress(self, value: Any, metadata: Dict[str, Any] = None) -> Any:
        """
        Decompress a value retrieved from SharedMemory.
        
        Args:
            value: Compressed value to decompress
            metadata: Metadata associated with the value
            
        Returns:
            Decompressed value
        """
        start_time = time.time()
        
        # Skip decompression if no metadata or no compression info
        if not metadata or "compression_info" not in metadata:
            return value
        
        compression_info = metadata["compression_info"]
        
        # Skip decompression if not compressed
        if not compression_info.get("compressed", False):
            return value
        
        # Decompress based on compression method
        decompressed_value = value
        compression_method = compression_info.get("compression_method")
        
        if compression_method == "zlib_text":
            decompressed_value = self._decompress_text(value, compression_info)
        elif compression_method == "zlib_binary":
            decompressed_value = self._decompress_binary(value, compression_info)
        elif compression_method == "json_optimization":
            decompressed_value = self._decompress_json(value, compression_info)
        elif compression_method == "semantic_compression":
            decompressed_value = self._decompress_semantic(value, compression_info)
        
        # Update statistics
        decompression_time = time.time() - start_time
        self.stats["decompression_time"] += decompression_time
        self.stats["total_decompressed"] += 1
        
        # Log decompression results
        logger.debug(
            f"Decompressed {compression_info['original_type']} using {compression_method}, "
            f"time: {decompression_time:.4f}s"
        )
        
        return decompressed_value
    
    def _compress_text(self, text: str) -> Tuple[Any, Dict[str, Any]]:
        """
        Compress a text string.
        
        Args:
            text: Text to compress
            
        Returns:
            Tuple of (compressed_text, compression_info)
        """
        # Skip compression for small texts
        if len(text) < self.min_size_for_compression or not self.enable_text_compression:
            return text, {"compressed": False, "original_type": "str", "compression_method": None}
        
        # Compress using zlib
        try:
            text_bytes = text.encode('utf-8')
            compressed_bytes = zlib.compress(text_bytes, self.compression_level)
            compressed_b64 = base64.b64encode(compressed_bytes).decode('ascii')
            
            # Calculate compression ratio
            original_size = len(text_bytes)
            compressed_size = len(compressed_bytes)
            compression_ratio = compressed_size / original_size if original_size > 0 else 1.0
            
            # Update statistics
            self.stats["bytes_before_compression"] += original_size
            self.stats["bytes_after_compression"] += compressed_size
            
            # Only use compression if it actually saves space
            if compression_ratio <= self.compression_threshold:
                return compressed_b64, {
                    "compressed": True,
                    "original_type": "str",
                    "compression_method": "zlib_text",
                    "original_length": len(text),
                    "original_size": original_size,
                    "compressed_size": compressed_size,
                    "compression_ratio": compression_ratio
                }
            else:
                return text, {"compressed": False, "original_type": "str", "compression_method": None}
        except Exception as e:
            logger.warning(f"Text compression failed: {str(e)}")
            return text, {"compressed": False, "original_type": "str", "compression_method": None}
    
    def _decompress_text(self, compressed_text: str, compression_info: Dict[str, Any]) -> str:
        """
        Decompress a compressed text string.
        
        Args:
            compressed_text: Compressed text to decompress
            compression_info: Compression information
            
        Returns:
            Decompressed text
        """
        try:
            compressed_bytes = base64.b64decode(compressed_text)
            decompressed_bytes = zlib.decompress(compressed_bytes)
            return decompressed_bytes.decode('utf-8')
        except Exception as e:
            logger.warning(f"Text decompression failed: {str(e)}")
            return compressed_text
    
    def _compress_binary(self, binary_data: bytes) -> Tuple[Any, Dict[str, Any]]:
        """
        Compress binary data.
        
        Args:
            binary_data: Binary data to compress
            
        Returns:
            Tuple of (compressed_data, compression_info)
        """
        # Skip compression for small binary data
        if len(binary_data) < self.min_size_for_compression or not self.enable_binary_compression:
            return binary_data, {"compressed": False, "original_type": "bytes", "compression_method": None}
        
        # Compress using zlib
        try:
            compressed_bytes = zlib.compress(binary_data, self.compression_level)
            compressed_b64 = base64.b64encode(compressed_bytes).decode('ascii')
            
            # Calculate compression ratio
            original_size = len(binary_data)
            compressed_size = len(compressed_bytes)
            compression_ratio = compressed_size / original_size if original_size > 0 else 1.0
            
            # Update statistics
            self.stats["bytes_before_compression"] += original_size
            self.stats["bytes_after_compression"] += compressed_size
            
            # Only use compression if it actually saves space
            if compression_ratio <= self.compression_threshold:
                return compressed_b64, {
                    "compressed": True,
                    "original_type": "bytes",
                    "compression_method": "zlib_binary",
                    "original_size": original_size,
                    "compressed_size": compressed_size,
                    "compression_ratio": compression_ratio
                }
            else:
                return binary_data, {"compressed": False, "original_type": "bytes", "compression_method": None}
        except Exception as e:
            logger.warning(f"Binary compression failed: {str(e)}")
            return binary_data, {"compressed": False, "original_type": "bytes", "compression_method": None}
    
    def _decompress_binary(self, compressed_data: str, compression_info: Dict[str, Any]) -> bytes:
        """
        Decompress compressed binary data.
        
        Args:
            compressed_data: Compressed data to decompress
            compression_info: Compression information
            
        Returns:
            Decompressed binary data
        """
        try:
            compressed_bytes = base64.b64decode(compressed_data)
            return zlib.decompress(compressed_bytes)
        except Exception as e:
            logger.warning(f"Binary decompression failed: {str(e)}")
            return base64.b64decode(compressed_data) if isinstance(compressed_data, str) else compressed_data
    
    def _compress_json(self, json_data: Union[Dict, List]) -> Tuple[Any, Dict[str, Any]]:
        """
        Compress JSON data.
        
        Args:
            json_data: JSON data to compress
            
        Returns:
            Tuple of (compressed_data, compression_info)
        """
        if not self.enable_json_optimization:
            return json_data, {"compressed": False, "original_type": type(json_data).__name__, "compression_method": None}
        
        # Convert to JSON string
        try:
            json_str = json.dumps(json_data)
            
            # Skip compression for small JSON
            if len(json_str) < self.min_size_for_compression:
                return json_data, {"compressed": False, "original_type": type(json_data).__name__, "compression_method": None}
            
            # Try optimizing the JSON structure
            optimized_data = self._optimize_json_structure(json_data)
            
            # Calculate optimization ratio
            original_size = len(json_str)
            optimized_size = len(json.dumps(optimized_data))
            optimization_ratio = optimized_size / original_size if original_size > 0 else 1.0
            
            # If optimization is not effective, try zlib compression
            if optimization_ratio > self.compression_threshold and self.enable_text_compression:
                compressed_text, compression_info = self._compress_text(json_str)
                if compression_info["compressed"]:
                    compression_info["original_type"] = type(json_data).__name__
                    return compressed_text, compression_info
            
            # Only use optimization if it actually saves space
            if optimization_ratio <= self.compression_threshold:
                return optimized_data, {
                    "compressed": True,
                    "original_type": type(json_data).__name__,
                    "compression_method": "json_optimization",
                    "original_size": original_size,
                    "compressed_size": optimized_size,
                    "optimization_ratio": optimization_ratio
                }
            else:
                return json_data, {"compressed": False, "original_type": type(json_data).__name__, "compression_method": None}
        except Exception as e:
            logger.warning(f"JSON compression failed: {str(e)}")
            return json_data, {"compressed": False, "original_type": type(json_data).__name__, "compression_method": None}
    
    def _decompress_json(self, compressed_data: Any, compression_info: Dict[str, Any]) -> Union[Dict, List]:
        """
        Decompress compressed JSON data.
        
        Args:
            compressed_data: Compressed data to decompress
            compression_info: Compression information
            
        Returns:
            Decompressed JSON data
        """
        try:
            # If it's a string, it might be zlib-compressed JSON
            if isinstance(compressed_data, str) and compression_info.get("compression_method") == "zlib_text":
                json_str = self._decompress_text(compressed_data, compression_info)
                return json.loads(json_str)
            
            # Otherwise, it's an optimized JSON structure
            return self._restore_json_structure(compressed_data, compression_info)
        except Exception as e:
            logger.warning(f"JSON decompression failed: {str(e)}")
            return compressed_data
    
    def _optimize_json_structure(self, data: Union[Dict, List]) -> Union[Dict, List]:
        """
        Optimize a JSON structure by removing redundancy.
        
        Args:
            data: JSON data to optimize
            
        Returns:
            Optimized JSON data
        """
        if isinstance(data, dict):
            # Remove null values and empty containers
            result = {}
            for key, value in data.items():
                if value is None or (isinstance(value, (dict, list)) and not value):
                    continue
                
                # Recursively optimize nested structures
                if isinstance(value, (dict, list)):
                    result[key] = self._optimize_json_structure(value)
                else:
                    result[key] = value
            return result
        elif isinstance(data, list):
            # Remove null values and empty containers from lists
            result = []
            for item in data:
                if item is None or (isinstance(item, (dict, list)) and not item):
                    continue
                
                # Recursively optimize nested structures
                if isinstance(item, (dict, list)):
                    result.append(self._optimize_json_structure(item))
                else:
                    result.append(item)
            return result
        else:
            return data
    
    def _restore_json_structure(self, data: Union[Dict, List], compression_info: Dict[str, Any]) -> Union[Dict, List]:
        """
        Restore an optimized JSON structure.
        
        Args:
            data: Optimized JSON data
            compression_info: Compression information
            
        Returns:
            Restored JSON data
        """
        # For simple JSON optimization, the structure is already valid
        return data
    
    def _compress_semantic(self, text: str) -> Tuple[Any, Dict[str, Any]]:
        """
        Compress text using semantic compression.
        
        Args:
            text: Text to compress
            
        Returns:
            Tuple of (compressed_text, compression_info)
        """
        # Semantic compression is not implemented in the basic version
        return text, {"compressed": False, "original_type": "str", "compression_method": None}
    
    def _decompress_semantic(self, compressed_text: str, compression_info: Dict[str, Any]) -> str:
        """
        Decompress semantically compressed text.
        
        Args:
            compressed_text: Compressed text to decompress
            compression_info: Compression information
            
        Returns:
            Decompressed text
        """
        # Semantic decompression is not implemented in the basic version
        return compressed_text
    
    def _default_token_estimator(self, text: str) -> int:
        """
        Estimate the number of tokens in a text.
        
        Args:
            text: Text to estimate tokens for
            
        Returns:
            Estimated number of tokens
        """
        # Simple estimation: 1 token ≈ 4 characters
        return len(text) // 4
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about the memory compressor.
        
        Returns:
            Dictionary with statistics
        """
        stats = self.stats.copy()
        
        # Calculate additional statistics
        if stats["bytes_before_compression"] > 0:
            stats["overall_compression_ratio"] = stats["bytes_after_compression"] / stats["bytes_before_compression"]
        else:
            stats["overall_compression_ratio"] = 1.0
        
        stats["bytes_saved"] = stats["bytes_before_compression"] - stats["bytes_after_compression"]
        stats["avg_compression_time"] = stats["compression_time"] / stats["total_compressed"] if stats["total_compressed"] > 0 else 0
        stats["avg_decompression_time"] = stats["decompression_time"] / stats["total_decompressed"] if stats["total_decompressed"] > 0 else 0
        
        return stats
    
    def reset_statistics(self) -> None:
        """Reset the statistics."""
        self.stats = {
            "total_compressed": 0,
            "total_decompressed": 0,
            "bytes_before_compression": 0,
            "bytes_after_compression": 0,
            "compression_time": 0.0,
            "decompression_time": 0.0
        }
