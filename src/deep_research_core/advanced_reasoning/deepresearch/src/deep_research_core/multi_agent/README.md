# Multi-Agent Module

The Multi-Agent module provides a framework for creating collaborative multi-agent systems in Deep Research Core. It includes components for agent communication, role definition, consensus mechanisms, and task management.

## Key Components

### Agent Communication

- `AgentMessage`: Standardized message format for agent communication
- `AgentCommunicationProtocol`: Handles message routing and delivery between agents

### Agent Roles

- `AgentRole`: Defines specialized capabilities, knowledge areas, and system prompts for agents

### Consensus Mechanisms

The module provides several consensus strategies for agents to reach agreement:

1. **Simple Voting**: Basic majority voting among proposals
2. **Weighted Voting**: Voting weighted by agent expertise and confidence
3. **Discussion-Based**: Iterative discussion process with feedback and refinement
4. **Bayesian Consensus**: Combines beliefs using Bayesian inference principles
5. **Expert-Weighted Consensus**: Weights opinions based on domain expertise
6. **Multi-Round Consensus**: Applies multiple consensus strategies in sequence

### Task Management

- `TaskDecomposer`: Breaks complex tasks into subtasks
- `SharedMemory`: Provides a unified knowledge base for agents

## Usage Examples

### Creating Agents with Roles

```python
from deep_research_core.multi_agent.core import Agent<PERSON><PERSON>, Agent

# Define a role
researcher_role = Agent<PERSON>ole(
    name="Researcher",
    description="Research expert",
    capabilities=["research", "analysis"],
    knowledge_areas=["science", "history"],
    system_prompt="You are a research expert."
)

# Create an agent with this role
researcher = Agent(
    id="researcher_1",
    role=researcher_role,
    language_model=my_language_model
)
```

### Using Consensus Mechanisms

```python
from deep_research_core.multi_agent.core import ConsensusMechanism

# Create proposals from different agents
proposals = [
    {"agent_id": "agent1", "answer": "Solution A", "confidence": 0.8},
    {"agent_id": "agent2", "answer": "Solution B", "confidence": 0.7},
    {"agent_id": "agent3", "answer": "Solution A", "confidence": 0.6}
]

# Simple voting consensus
voting_consensus = ConsensusMechanism(strategy="voting")
result = voting_consensus.reach_consensus(proposals, agents, context)

# Weighted voting consensus
weighted_consensus = ConsensusMechanism(strategy="weighted_voting")
result = weighted_consensus.reach_consensus(proposals, agents, context)

# Discussion-based consensus
discussion_consensus = ConsensusMechanism(strategy="discussion")
result = discussion_consensus.reach_consensus(proposals, agents, context)

# Bayesian consensus
bayesian_consensus = ConsensusMechanism(strategy="bayesian")
result = bayesian_consensus.reach_consensus(proposals, agents, context)

# Expert-weighted consensus
expert_consensus = ConsensusMechanism(strategy="expert_weighted")
result = expert_consensus.reach_consensus(proposals, agents, context)

# Multi-round consensus
multi_round_consensus = ConsensusMechanism(strategy="multi_round")
result = multi_round_consensus.reach_consensus(proposals, agents, context)
```

### Using Multi-Round Consensus Directly

```python
from deep_research_core.multi_agent.bayesian_consensus import MultiRoundConsensus

# Create a multi-round consensus with custom configuration
multi_round = MultiRoundConsensus(
    rounds_config=[
        {
            'strategy': 'weighted_voting',  # First round: weighted voting
            'filter_ratio': 0.5,            # Keep top 50% of proposals
            'params': {}
        },
        {
            'strategy': 'discussion',       # Second round: discussion
            'filter_ratio': 0.5,            # Keep top 50% of remaining proposals
            'params': {}
        },
        {
            'strategy': 'bayesian',         # Final round: Bayesian consensus
            'filter_ratio': 1.0,            # Final decision
            'params': {}
        }
    ],
    confidence_threshold=0.8,  # Early stopping threshold
    min_proposals_per_round=2, # Minimum proposals to keep per round
    max_rounds=3               # Maximum number of rounds
)

# Reach consensus
result = multi_round.reach_consensus(proposals, agents, context)

# The result includes metadata about the consensus process
print(result["consensus_metadata"]["method"])  # "multi_round_consensus"
print(result["consensus_metadata"]["rounds"])  # List of rounds executed
print(result["consensus_metadata"]["confidence"])  # Final confidence
print(result["consensus_metadata"]["early_stopping"])  # Whether early stopping occurred
```

## Advanced Features

### Multi-Round Consensus

The Multi-Round Consensus mechanism applies different consensus strategies in sequence, progressively refining the set of proposals to reach a more robust agreement. This approach combines the strengths of different consensus methods:

1. **Progressive Filtering**: Each round filters out less promising proposals, focusing on the most promising ones
2. **Strategy Combination**: Leverages different strategies at different stages of the consensus process
3. **Early Stopping**: Stops early if high confidence is reached, saving computational resources
4. **Configurable Rounds**: Fully configurable rounds with different strategies and parameters

This approach is particularly useful for complex decision-making scenarios where a single consensus strategy may not be sufficient.
