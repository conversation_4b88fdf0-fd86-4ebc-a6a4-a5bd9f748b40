"""
Memory versioning system for SharedMemory.

This module provides version control capabilities for the SharedMemory system,
allowing tracking of changes, reverting to previous versions, and resolving conflicts.
"""

import time
import copy
import uuid
import json
import difflib
from typing import Dict, Any, List, Optional, Tuple, Callable, Union
from enum import Enum
import logging

# Set up logging
logger = logging.getLogger(__name__)


class VersionOperation(Enum):
    """Operations that can be performed on memory items."""
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    MERGE = "merge"


class ConflictResolutionStrategy(Enum):
    """Strategies for resolving conflicts between versions."""
    LAST_WRITE_WINS = "last_write_wins"
    FIRST_WRITE_WINS = "first_write_wins"
    MANUAL_RESOLUTION = "manual_resolution"
    MERGE_FIELDS = "merge_fields"
    CUSTOM = "custom"


class MemoryVersion:
    """
    Represents a version of a memory item.
    
    This class stores the state of a memory item at a specific point in time,
    along with metadata about the version.
    """
    
    def __init__(
        self,
        key: str,
        value: Any,
        metadata: Dict[str, Any],
        version_id: Optional[str] = None,
        parent_version_id: Optional[str] = None,
        operation: VersionOperation = VersionOperation.CREATE,
        agent_id: Optional[str] = None,
        timestamp: Optional[float] = None,
        comment: Optional[str] = None
    ):
        """
        Initialize a memory version.
        
        Args:
            key: The key of the memory item
            value: The value of the memory item
            metadata: Metadata associated with the memory item
            version_id: Unique identifier for this version (generated if None)
            parent_version_id: ID of the parent version (None for initial version)
            operation: The operation that created this version
            agent_id: ID of the agent that created this version
            timestamp: When this version was created (current time if None)
            comment: Optional comment about this version
        """
        self.key = key
        self.value = copy.deepcopy(value)
        self.metadata = copy.deepcopy(metadata)
        self.version_id = version_id or str(uuid.uuid4())
        self.parent_version_id = parent_version_id
        self.operation = operation
        self.agent_id = agent_id
        self.timestamp = timestamp or time.time()
        self.comment = comment
        
        # Track children versions
        self.children_version_ids: List[str] = []
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert this version to a dictionary for serialization.
        
        Returns:
            Dictionary representation of this version
        """
        return {
            "key": self.key,
            "value": self.value,
            "metadata": self.metadata,
            "version_id": self.version_id,
            "parent_version_id": self.parent_version_id,
            "operation": self.operation.value,
            "agent_id": self.agent_id,
            "timestamp": self.timestamp,
            "comment": self.comment,
            "children_version_ids": self.children_version_ids
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MemoryVersion':
        """
        Create a version from a dictionary.
        
        Args:
            data: Dictionary representation of a version
            
        Returns:
            A MemoryVersion instance
        """
        return cls(
            key=data["key"],
            value=data["value"],
            metadata=data["metadata"],
            version_id=data["version_id"],
            parent_version_id=data["parent_version_id"],
            operation=VersionOperation(data["operation"]),
            agent_id=data["agent_id"],
            timestamp=data["timestamp"],
            comment=data["comment"]
        )
    
    def diff(self, other: 'MemoryVersion') -> Dict[str, Any]:
        """
        Calculate the difference between this version and another.
        
        Args:
            other: Another version to compare with
            
        Returns:
            Dictionary containing differences
        """
        if self.key != other.key:
            raise ValueError(f"Cannot diff versions with different keys: {self.key} vs {other.key}")
        
        # Convert values to strings for diffing
        try:
            self_value_str = json.dumps(self.value, sort_keys=True, indent=2)
        except (TypeError, ValueError):
            self_value_str = str(self.value)
            
        try:
            other_value_str = json.dumps(other.value, sort_keys=True, indent=2)
        except (TypeError, ValueError):
            other_value_str = str(other.value)
        
        # Calculate diffs
        value_diff = list(difflib.unified_diff(
            self_value_str.splitlines(),
            other_value_str.splitlines(),
            fromfile=f"version_{self.version_id}",
            tofile=f"version_{other.version_id}",
            lineterm=""
        ))
        
        # Calculate metadata diff
        metadata_diff = {}
        all_keys = set(self.metadata.keys()) | set(other.metadata.keys())
        
        for key in all_keys:
            if key not in self.metadata:
                metadata_diff[key] = {"type": "added", "value": other.metadata[key]}
            elif key not in other.metadata:
                metadata_diff[key] = {"type": "removed", "value": self.metadata[key]}
            elif self.metadata[key] != other.metadata[key]:
                metadata_diff[key] = {
                    "type": "changed",
                    "from": self.metadata[key],
                    "to": other.metadata[key]
                }
        
        return {
            "key": self.key,
            "from_version": self.version_id,
            "to_version": other.version_id,
            "value_diff": value_diff,
            "metadata_diff": metadata_diff,
            "timestamp_diff": other.timestamp - self.timestamp
        }


class ConflictResolver:
    """
    Resolves conflicts between different versions of the same memory item.
    
    This class provides strategies for resolving conflicts when multiple agents
    update the same memory item concurrently.
    """
    
    def __init__(
        self,
        default_strategy: ConflictResolutionStrategy = ConflictResolutionStrategy.LAST_WRITE_WINS,
        custom_resolver: Optional[Callable[[List[MemoryVersion]], MemoryVersion]] = None
    ):
        """
        Initialize the conflict resolver.
        
        Args:
            default_strategy: Default strategy to use for conflict resolution
            custom_resolver: Custom function for resolving conflicts
        """
        self.default_strategy = default_strategy
        self.custom_resolver = custom_resolver
    
    def resolve(
        self,
        versions: List[MemoryVersion],
        strategy: Optional[ConflictResolutionStrategy] = None
    ) -> MemoryVersion:
        """
        Resolve conflicts between multiple versions.
        
        Args:
            versions: List of conflicting versions
            strategy: Strategy to use (default_strategy if None)
            
        Returns:
            Resolved version
        """
        if not versions:
            raise ValueError("No versions provided for conflict resolution")
        
        if len(versions) == 1:
            return versions[0]
        
        strategy = strategy or self.default_strategy
        
        if strategy == ConflictResolutionStrategy.LAST_WRITE_WINS:
            return self._resolve_last_write_wins(versions)
        elif strategy == ConflictResolutionStrategy.FIRST_WRITE_WINS:
            return self._resolve_first_write_wins(versions)
        elif strategy == ConflictResolutionStrategy.MERGE_FIELDS:
            return self._resolve_merge_fields(versions)
        elif strategy == ConflictResolutionStrategy.MANUAL_RESOLUTION:
            # Return all versions and let the caller decide
            # This is just a placeholder - in a real system, you'd implement a UI for this
            logger.warning("Manual resolution requested but not implemented")
            return self._resolve_last_write_wins(versions)
        elif strategy == ConflictResolutionStrategy.CUSTOM:
            if self.custom_resolver:
                return self.custom_resolver(versions)
            else:
                logger.warning("Custom resolver requested but not provided")
                return self._resolve_last_write_wins(versions)
        else:
            logger.warning(f"Unknown resolution strategy: {strategy}")
            return self._resolve_last_write_wins(versions)
    
    def _resolve_last_write_wins(self, versions: List[MemoryVersion]) -> MemoryVersion:
        """
        Resolve conflicts by selecting the most recent version.
        
        Args:
            versions: List of conflicting versions
            
        Returns:
            Most recent version
        """
        latest_version = max(versions, key=lambda v: v.timestamp)
        
        # Create a new merged version
        merged = MemoryVersion(
            key=latest_version.key,
            value=latest_version.value,
            metadata=latest_version.metadata,
            parent_version_id=latest_version.parent_version_id,
            operation=VersionOperation.MERGE,
            agent_id=latest_version.agent_id,
            comment=f"Merged using last-write-wins from {len(versions)} versions"
        )
        
        return merged
    
    def _resolve_first_write_wins(self, versions: List[MemoryVersion]) -> MemoryVersion:
        """
        Resolve conflicts by selecting the earliest version.
        
        Args:
            versions: List of conflicting versions
            
        Returns:
            Earliest version
        """
        earliest_version = min(versions, key=lambda v: v.timestamp)
        
        # Create a new merged version
        merged = MemoryVersion(
            key=earliest_version.key,
            value=earliest_version.value,
            metadata=earliest_version.metadata,
            parent_version_id=earliest_version.parent_version_id,
            operation=VersionOperation.MERGE,
            agent_id=earliest_version.agent_id,
            comment=f"Merged using first-write-wins from {len(versions)} versions"
        )
        
        return merged
    
    def _resolve_merge_fields(self, versions: List[MemoryVersion]) -> MemoryVersion:
        """
        Resolve conflicts by merging fields from all versions.
        
        Args:
            versions: List of conflicting versions
            
        Returns:
            Merged version
        """
        if not versions:
            raise ValueError("No versions to merge")
        
        # Start with the latest version as a base
        latest_version = max(versions, key=lambda v: v.timestamp)
        
        # For simple types, use the latest version
        if not isinstance(latest_version.value, dict):
            return self._resolve_last_write_wins(versions)
        
        # For dictionaries, merge fields
        merged_value = {}
        merged_metadata = {}
        
        # Sort versions by timestamp (oldest first)
        sorted_versions = sorted(versions, key=lambda v: v.timestamp)
        
        # Merge values (later versions override earlier ones for the same field)
        for version in sorted_versions:
            if isinstance(version.value, dict):
                merged_value.update(version.value)
            else:
                # If any version has a non-dict value, use that as the final value
                merged_value = version.value
                break
        
        # Merge metadata
        for version in sorted_versions:
            merged_metadata.update(version.metadata)
        
        # Create a new merged version
        merged = MemoryVersion(
            key=latest_version.key,
            value=merged_value,
            metadata=merged_metadata,
            parent_version_id=latest_version.parent_version_id,
            operation=VersionOperation.MERGE,
            agent_id=latest_version.agent_id,
            comment=f"Merged fields from {len(versions)} versions"
        )
        
        return merged


class MemoryVersionManager:
    """
    Manages versions of memory items.
    
    This class provides version control capabilities for SharedMemory,
    tracking the history of changes to memory items and allowing operations
    like reverting to previous versions.
    """
    
    def __init__(
        self,
        conflict_resolver: Optional[ConflictResolver] = None,
        max_versions_per_key: int = 100,
        enable_pruning: bool = True
    ):
        """
        Initialize the version manager.
        
        Args:
            conflict_resolver: Resolver for version conflicts
            max_versions_per_key: Maximum number of versions to keep per key
            enable_pruning: Whether to automatically prune old versions
        """
        self.versions: Dict[str, Dict[str, MemoryVersion]] = {}  # key -> version_id -> version
        self.conflict_resolver = conflict_resolver or ConflictResolver()
        self.max_versions_per_key = max_versions_per_key
        self.enable_pruning = enable_pruning
    
    def create_version(
        self,
        key: str,
        value: Any,
        metadata: Dict[str, Any],
        parent_version_id: Optional[str] = None,
        operation: VersionOperation = VersionOperation.CREATE,
        agent_id: Optional[str] = None,
        comment: Optional[str] = None
    ) -> MemoryVersion:
        """
        Create a new version of a memory item.
        
        Args:
            key: Key of the memory item
            value: Value of the memory item
            metadata: Metadata of the memory item
            parent_version_id: ID of the parent version
            operation: Operation that created this version
            agent_id: ID of the agent creating this version
            comment: Optional comment about this version
            
        Returns:
            The created version
        """
        # Initialize versions dict for this key if needed
        if key not in self.versions:
            self.versions[key] = {}
        
        # Create the new version
        version = MemoryVersion(
            key=key,
            value=value,
            metadata=metadata,
            parent_version_id=parent_version_id,
            operation=operation,
            agent_id=agent_id,
            comment=comment
        )
        
        # Add to versions dict
        self.versions[key][version.version_id] = version
        
        # Update parent's children list if parent exists
        if parent_version_id and parent_version_id in self.versions[key]:
            parent = self.versions[key][parent_version_id]
            parent.children_version_ids.append(version.version_id)
        
        # Prune old versions if needed
        if self.enable_pruning:
            self._prune_versions(key)
        
        return version
    
    def get_version(self, key: str, version_id: str) -> Optional[MemoryVersion]:
        """
        Get a specific version of a memory item.
        
        Args:
            key: Key of the memory item
            version_id: ID of the version to get
            
        Returns:
            The requested version, or None if not found
        """
        if key not in self.versions or version_id not in self.versions[key]:
            return None
        
        return self.versions[key][version_id]
    
    def get_latest_version(self, key: str) -> Optional[MemoryVersion]:
        """
        Get the latest version of a memory item.
        
        Args:
            key: Key of the memory item
            
        Returns:
            The latest version, or None if no versions exist
        """
        if key not in self.versions or not self.versions[key]:
            return None
        
        # Find the version with the latest timestamp
        return max(self.versions[key].values(), key=lambda v: v.timestamp)
    
    def get_version_history(
        self,
        key: str,
        limit: int = 10,
        offset: int = 0
    ) -> List[MemoryVersion]:
        """
        Get the version history of a memory item.
        
        Args:
            key: Key of the memory item
            limit: Maximum number of versions to return
            offset: Number of versions to skip
            
        Returns:
            List of versions, sorted by timestamp (newest first)
        """
        if key not in self.versions:
            return []
        
        # Sort versions by timestamp (newest first)
        sorted_versions = sorted(
            self.versions[key].values(),
            key=lambda v: v.timestamp,
            reverse=True
        )
        
        # Apply limit and offset
        return sorted_versions[offset:offset + limit]
    
    def get_version_tree(self, key: str) -> Dict[str, Any]:
        """
        Get the version tree of a memory item.
        
        Args:
            key: Key of the memory item
            
        Returns:
            Dictionary representing the version tree
        """
        if key not in self.versions:
            return {"key": key, "versions": []}
        
        # Find root versions (those without parents or with parents that don't exist)
        root_versions = [
            v for v in self.versions[key].values()
            if not v.parent_version_id or v.parent_version_id not in self.versions[key]
        ]
        
        # Build tree recursively
        tree = {
            "key": key,
            "versions": [self._build_version_subtree(key, v) for v in root_versions]
        }
        
        return tree
    
    def _build_version_subtree(self, key: str, version: MemoryVersion) -> Dict[str, Any]:
        """
        Build a subtree for a version and its descendants.
        
        Args:
            key: Key of the memory item
            version: Root version of the subtree
            
        Returns:
            Dictionary representing the subtree
        """
        children = [
            self._build_version_subtree(key, self.versions[key][child_id])
            for child_id in version.children_version_ids
            if child_id in self.versions[key]
        ]
        
        return {
            "version_id": version.version_id,
            "operation": version.operation.value,
            "agent_id": version.agent_id,
            "timestamp": version.timestamp,
            "comment": version.comment,
            "children": children
        }
    
    def revert_to_version(
        self,
        key: str,
        version_id: str,
        agent_id: Optional[str] = None,
        comment: Optional[str] = None
    ) -> Optional[MemoryVersion]:
        """
        Revert a memory item to a previous version.
        
        Args:
            key: Key of the memory item
            version_id: ID of the version to revert to
            agent_id: ID of the agent performing the revert
            comment: Optional comment about the revert
            
        Returns:
            The new version created by the revert, or None if the version to revert to wasn't found
        """
        target_version = self.get_version(key, version_id)
        if not target_version:
            return None
        
        # Get the latest version to use as the parent
        latest_version = self.get_latest_version(key)
        if not latest_version:
            return None
        
        # Create a new version based on the target version
        new_version = self.create_version(
            key=key,
            value=target_version.value,
            metadata=target_version.metadata,
            parent_version_id=latest_version.version_id,
            operation=VersionOperation.UPDATE,
            agent_id=agent_id,
            comment=comment or f"Reverted to version {version_id}"
        )
        
        return new_version
    
    def resolve_conflicts(
        self,
        key: str,
        version_ids: List[str],
        strategy: Optional[ConflictResolutionStrategy] = None,
        agent_id: Optional[str] = None,
        comment: Optional[str] = None
    ) -> Optional[MemoryVersion]:
        """
        Resolve conflicts between multiple versions.
        
        Args:
            key: Key of the memory item
            version_ids: IDs of the versions to resolve
            strategy: Strategy to use for resolution
            agent_id: ID of the agent performing the resolution
            comment: Optional comment about the resolution
            
        Returns:
            The new version created by the resolution, or None if any version wasn't found
        """
        if key not in self.versions:
            return None
        
        # Get all specified versions
        versions = []
        for version_id in version_ids:
            version = self.get_version(key, version_id)
            if not version:
                logger.warning(f"Version {version_id} not found for key {key}")
                return None
            versions.append(version)
        
        # Resolve conflicts
        resolved = self.conflict_resolver.resolve(versions, strategy)
        
        # Create a new version with the resolved content
        new_version = self.create_version(
            key=key,
            value=resolved.value,
            metadata=resolved.metadata,
            parent_version_id=versions[-1].version_id,  # Use the last version as parent
            operation=VersionOperation.MERGE,
            agent_id=agent_id,
            comment=comment or f"Resolved conflicts between {len(versions)} versions"
        )
        
        return new_version
    
    def _prune_versions(self, key: str) -> int:
        """
        Prune old versions to stay within the max_versions_per_key limit.
        
        Args:
            key: Key of the memory item
            
        Returns:
            Number of versions pruned
        """
        if key not in self.versions:
            return 0
        
        versions = self.versions[key]
        if len(versions) <= self.max_versions_per_key:
            return 0
        
        # Sort versions by timestamp (oldest first)
        sorted_versions = sorted(
            versions.values(),
            key=lambda v: v.timestamp
        )
        
        # Calculate how many to prune
        to_prune = len(versions) - self.max_versions_per_key
        
        # Prune oldest versions
        pruned = 0
        for version in sorted_versions[:to_prune]:
            # Don't prune versions that have children
            if version.children_version_ids:
                continue
            
            # Remove this version
            del versions[version.version_id]
            pruned += 1
            
            # If we've pruned enough, stop
            if pruned >= to_prune:
                break
        
        return pruned
    
    def clear_versions(self, key: Optional[str] = None) -> int:
        """
        Clear all versions for a key or all keys.
        
        Args:
            key: Key to clear versions for, or None to clear all
            
        Returns:
            Number of versions cleared
        """
        if key:
            if key in self.versions:
                count = len(self.versions[key])
                del self.versions[key]
                return count
            return 0
        else:
            count = sum(len(versions) for versions in self.versions.values())
            self.versions.clear()
            return count
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about the version manager.
        
        Returns:
            Dictionary with statistics
        """
        total_versions = sum(len(versions) for versions in self.versions.values())
        
        # Count versions by operation
        versions_by_operation = {}
        for versions in self.versions.values():
            for version in versions.values():
                op = version.operation.value
                versions_by_operation[op] = versions_by_operation.get(op, 0) + 1
        
        # Count versions by agent
        versions_by_agent = {}
        for versions in self.versions.values():
            for version in versions.values():
                agent = version.agent_id or "unknown"
                versions_by_agent[agent] = versions_by_agent.get(agent, 0) + 1
        
        return {
            "total_keys": len(self.versions),
            "total_versions": total_versions,
            "versions_by_operation": versions_by_operation,
            "versions_by_agent": versions_by_agent,
            "avg_versions_per_key": total_versions / len(self.versions) if self.versions else 0
        }
