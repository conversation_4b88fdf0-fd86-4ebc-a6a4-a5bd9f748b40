"""
Query parser for SharedMemory advanced querying.

This module provides a parser for a simple query language to search
in the SharedMemory system. It supports various operators and
conditions to enable complex queries.
"""

import re
import time
from typing import Any, Callable, Dict, List, Optional, Tuple, Union
from datetime import datetime, timedelta

class QueryToken:
    """Represents a token in the query language."""

    def __init__(self, token_type: str, value: str, position: int):
        self.type = token_type
        self.value = value
        self.position = position

    def __repr__(self):
        return f"Token({self.type}, '{self.value}', pos={self.position})"


class QueryLexer:
    """Lexer for the query language."""

    # Token types
    FIELD = "FIELD"
    OPERATOR = "OPERATOR"
    VALUE = "VALUE"
    LOGICAL = "LOGICAL"
    LPAREN = "LPAREN"
    RPAREN = "RPAREN"

    # Patterns
    FIELD_PATTERN = r'[a-zA-Z0-9_.]+'
    OPERATOR_PATTERN = r'[:~<>]=?'
    VALUE_PATTERN = r'[^:\s()]+'
    LOGICAL_PATTERN = r'AND|OR|NOT'

    def __init__(self, query: str):
        self.query = query
        self.position = 0
        self.tokens = []

    def tokenize(self) -> List[QueryToken]:
        """Convert the query string into tokens."""
        self.tokens = []
        self.position = 0

        while self.position < len(self.query):
            # Skip whitespace
            if self.query[self.position].isspace():
                self.position += 1
                continue

            # Check for parentheses
            if self.query[self.position] == '(':
                self.tokens.append(QueryToken(self.LPAREN, '(', self.position))
                self.position += 1
                continue

            if self.query[self.position] == ')':
                self.tokens.append(QueryToken(self.RPAREN, ')', self.position))
                self.position += 1
                continue

            # Check for logical operators
            logical_match = re.match(self.LOGICAL_PATTERN, self.query[self.position:])
            if logical_match:
                value = logical_match.group(0)
                self.tokens.append(QueryToken(self.LOGICAL, value, self.position))
                self.position += len(value)
                continue

            # Field name
            field_match = re.match(self.FIELD_PATTERN, self.query[self.position:])
            if field_match:
                value = field_match.group(0)
                self.tokens.append(QueryToken(self.FIELD, value, self.position))
                self.position += len(value)

                # Look for operator
                if self.position < len(self.query):
                    operator_match = re.match(self.OPERATOR_PATTERN, self.query[self.position:])
                    if operator_match:
                        value = operator_match.group(0)
                        self.tokens.append(QueryToken(self.OPERATOR, value, self.position))
                        self.position += len(value)

                        # Look for value
                        if self.position < len(self.query):
                            # Handle quoted values
                            if self.query[self.position] == '"':
                                # Find the closing quote
                                start_pos = self.position + 1
                                end_pos = self.query.find('"', start_pos)

                                if end_pos == -1:
                                    # No closing quote found, use the rest of the string
                                    value = self.query[start_pos:]
                                    self.tokens.append(QueryToken(self.VALUE, value, self.position))
                                    self.position = len(self.query)
                                else:
                                    # Extract the quoted value
                                    value = self.query[start_pos:end_pos]
                                    self.tokens.append(QueryToken(self.VALUE, value, self.position))
                                    self.position = end_pos + 1
                            else:
                                # Regular unquoted value
                                value_match = re.match(self.VALUE_PATTERN, self.query[self.position:])
                                if value_match:
                                    value = value_match.group(0)
                                    self.tokens.append(QueryToken(self.VALUE, value, self.position))
                                    self.position += len(value)
                continue

            # If we get here, we couldn't match anything
            raise ValueError(f"Unexpected character at position {self.position}: '{self.query[self.position]}'")

        return self.tokens


class QueryCondition:
    """Represents a single condition in a query."""

    def __init__(self, field: str, operator: str, value: Any):
        self.field = field
        self.operator = operator
        self.value = self._parse_value(value)

    def _parse_value(self, value: str) -> Any:
        """Parse the value into the appropriate type."""
        # Try to parse as number
        if value.isdigit():
            return int(value)

        try:
            return float(value)
        except ValueError:
            pass

        # Try to parse as datetime
        if re.match(r'\d{4}-\d{2}-\d{2}', value):
            try:
                return datetime.fromisoformat(value)
            except ValueError:
                pass

        # Try to parse as relative time
        time_units = {
            's': 'seconds',
            'm': 'minutes',
            'h': 'hours',
            'd': 'days',
            'w': 'weeks'
        }

        time_match = re.match(r'(\d+)([smhdw])', value)
        if time_match:
            amount = int(time_match.group(1))
            unit = time_match.group(2)
            delta_args = {time_units[unit]: amount}
            return datetime.now() - timedelta(**delta_args)

        # Return as string
        return value

    def evaluate(self, item: Dict[str, Any]) -> bool:
        """Evaluate this condition against an item."""
        # Handle special fields
        if self.field == 'key':
            field_value = item.get('key', '')
        elif self.field == 'content' or self.field == 'value':
            field_value = str(item.get('value', ''))
        elif self.field == 'created_at':
            field_value = item.get('created_at', 0)
        elif self.field == 'last_updated':
            field_value = item.get('last_updated', 0)
        elif self.field == 'created_by':
            field_value = item.get('created_by', '')
        elif self.field.startswith('metadata.'):
            # Extract from metadata
            metadata_field = self.field[9:]  # Remove 'metadata.' prefix
            field_value = item.get('metadata', {}).get(metadata_field, None)
        else:
            # Try direct access
            field_value = item.get(self.field, None)

        # If field doesn't exist, condition is false
        if field_value is None:
            return False

        # Handle different operators
        if self.operator == ':':
            # Exact match
            if isinstance(field_value, str) and isinstance(self.value, str):
                return field_value.lower() == self.value.lower()
            return field_value == self.value

        elif self.operator == '~':
            # Contains
            if not isinstance(field_value, str):
                field_value = str(field_value)
            if not isinstance(self.value, str):
                value = str(self.value)
            else:
                value = self.value
            return value.lower() in field_value.lower()

        elif self.operator == '>':
            return field_value > self.value

        elif self.operator == '>=':
            return field_value >= self.value

        elif self.operator == '<':
            return field_value < self.value

        elif self.operator == '<=':
            return field_value <= self.value

        # Unknown operator
        raise ValueError(f"Unknown operator: {self.operator}")

    def __repr__(self):
        return f"Condition({self.field} {self.operator} {self.value})"


class QueryExpression:
    """Base class for query expressions."""

    def evaluate(self, item: Dict[str, Any]) -> bool:
        """Evaluate this expression against an item."""
        raise NotImplementedError()


class QueryConditionExpression(QueryExpression):
    """A query expression that consists of a single condition."""

    def __init__(self, condition: QueryCondition):
        self.condition = condition

    def evaluate(self, item: Dict[str, Any]) -> bool:
        return self.condition.evaluate(item)

    def __repr__(self):
        return repr(self.condition)


class QueryNotExpression(QueryExpression):
    """A query expression that negates another expression."""

    def __init__(self, expression: QueryExpression):
        self.expression = expression

    def evaluate(self, item: Dict[str, Any]) -> bool:
        return not self.expression.evaluate(item)

    def __repr__(self):
        return f"NOT({self.expression})"


class QueryBinaryExpression(QueryExpression):
    """A query expression that combines two expressions with a logical operator."""

    def __init__(self, left: QueryExpression, operator: str, right: QueryExpression):
        self.left = left
        self.operator = operator
        self.right = right

    def evaluate(self, item: Dict[str, Any]) -> bool:
        if self.operator == 'AND':
            return self.left.evaluate(item) and self.right.evaluate(item)
        elif self.operator == 'OR':
            return self.left.evaluate(item) or self.right.evaluate(item)
        else:
            raise ValueError(f"Unknown operator: {self.operator}")

    def __repr__(self):
        return f"({self.left} {self.operator} {self.right})"


class QueryParser:
    """Parser for the query language."""

    def __init__(self, query: str):
        self.lexer = QueryLexer(query)
        self.tokens = self.lexer.tokenize()
        self.position = 0

    def parse(self) -> QueryExpression:
        """Parse the query into an expression tree."""
        if not self.tokens:
            # Empty query matches everything
            return QueryConditionExpression(QueryCondition('key', '~', ''))

        try:
            return self._parse_expression()
        except Exception as e:
            # If parsing fails, fall back to a simple substring search
            # This makes the parser more robust against syntax errors
            return QueryConditionExpression(QueryCondition('content', '~', self.lexer.query))

    def _parse_expression(self) -> QueryExpression:
        """Parse an expression."""
        # Check for NOT
        if self._match(QueryLexer.LOGICAL, 'NOT'):
            self._advance()
            expression = self._parse_primary()
            return QueryNotExpression(expression)

        left = self._parse_primary()

        while self._match(QueryLexer.LOGICAL, 'AND') or self._match(QueryLexer.LOGICAL, 'OR'):
            operator = self._advance().value
            right = self._parse_primary()
            left = QueryBinaryExpression(left, operator, right)

        return left

    def _parse_primary(self) -> QueryExpression:
        """Parse a primary expression (condition or parenthesized expression)."""
        # Check for parentheses
        if self._match(QueryLexer.LPAREN):
            self._advance()
            expression = self._parse_expression()
            self._consume(QueryLexer.RPAREN, "Expected ')'")
            return expression

        # Parse condition
        if self._match(QueryLexer.FIELD):
            field = self._advance().value

            # Default operator and value if not specified
            operator = ':'
            value = ''

            if self.position < len(self.tokens) and self._match(QueryLexer.OPERATOR):
                operator = self._advance().value

                if self.position < len(self.tokens) and self._match(QueryLexer.VALUE):
                    value = self._advance().value

            condition = QueryCondition(field, operator, value)
            return QueryConditionExpression(condition)

        # Handle unexpected token
        if self.position < len(self.tokens):
            raise ValueError(f"Unexpected token: {self.tokens[self.position]}")
        else:
            raise ValueError("Unexpected end of query")

    def _match(self, token_type: str, value: str = None) -> bool:
        """Check if the current token matches the given type and value."""
        if self.position >= len(self.tokens):
            return False

        token = self.tokens[self.position]
        if token.type != token_type:
            return False

        if value is not None and token.value != value:
            return False

        return True

    def _advance(self) -> QueryToken:
        """Advance to the next token and return the current one."""
        token = self.tokens[self.position]
        self.position += 1
        return token

    def _consume(self, token_type: str, error_message: str) -> QueryToken:
        """Consume a token of the given type or raise an error."""
        if self._match(token_type):
            return self._advance()

        raise ValueError(f"{error_message}, got {self.tokens[self.position]}")


class QueryOptimizer:
    """Optimizer for query expressions."""

    def optimize(self, expression: QueryExpression) -> QueryExpression:
        """Optimize a query expression."""
        # This is a placeholder for future optimizations
        # Could implement:
        # - Constant folding
        # - Redundant condition elimination
        # - Condition reordering for efficiency
        return expression


def parse_query(query_string: str) -> QueryExpression:
    """
    Parse a query string into a query expression.

    Args:
        query_string: The query string to parse

    Returns:
        A query expression that can be evaluated against items
    """
    parser = QueryParser(query_string)
    expression = parser.parse()

    # Apply optimizations
    optimizer = QueryOptimizer()
    optimized_expression = optimizer.optimize(expression)

    return optimized_expression
