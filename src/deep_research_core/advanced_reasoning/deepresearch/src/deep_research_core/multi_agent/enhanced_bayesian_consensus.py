"""
Enhanced Bayesian consensus mechanisms for multi-agent systems.

This module provides improved implementations of Bayesian consensus methods
for reaching agreement among multiple agents, with advanced features including:
- Complex data handling
- Performance history-based agent weighting
- Conflict resolution
- Parameter self-adjustment
- Improved posterior probability calculations
"""

from typing import List, Dict, Any, Tuple, Optional, Union
import json
import math

from deep_research_core.utils.structured_logging import get_logger
from deep_research_core.multi_agent.bayesian_consensus import BayesianConsensus

logger = get_logger(__name__)


class EnhancedBayesianConsensus(BayesianConsensus):
    """
    Enhanced Bayesian consensus mechanism for multi-agent systems.

    This class extends the basic Bayesian consensus with advanced features:
    - Better handling of complex data structures
    - Agent weighting based on historical performance
    - Conflict resolution strategies
    - Self-adjusting parameters
    - Improved posterior probability calculations
    """

    def __init__(
        self,
        prior_strength: float = 1.0,
        expertise_weight: float = 2.0,
        confidence_scale: float = 1.0,
        min_evidence_weight: float = 0.5,
        conflict_resolution_threshold: float = 0.3,
        performance_history_weight: float = 1.5,
        self_adjustment_rate: float = 0.1,
        complex_data_similarity_threshold: float = 0.7
    ):
        """
        Initialize the enhanced Bayesian consensus mechanism.

        Args:
            prior_strength: Strength of the prior belief (higher values make prior more influential)
            expertise_weight: Weight multiplier for agent expertise
            confidence_scale: Scaling factor for agent confidence values
            min_evidence_weight: Minimum weight for any piece of evidence
            conflict_resolution_threshold: Threshold for detecting conflicts between agents
            performance_history_weight: Weight given to historical performance metrics
            self_adjustment_rate: Rate at which parameters self-adjust based on feedback
            complex_data_similarity_threshold: Threshold for considering complex data structures similar
        """
        super().__init__(
            prior_strength=prior_strength,
            expertise_weight=expertise_weight,
            confidence_scale=confidence_scale,
            min_evidence_weight=min_evidence_weight
        )

        self.conflict_resolution_threshold = conflict_resolution_threshold
        self.performance_history_weight = performance_history_weight
        self.self_adjustment_rate = self_adjustment_rate
        self.complex_data_similarity_threshold = complex_data_similarity_threshold

        # Track parameter adjustment history
        self.parameter_history = {
            'prior_strength': [prior_strength],
            'expertise_weight': [expertise_weight],
            'confidence_scale': [confidence_scale],
            'performance_history_weight': [performance_history_weight]
        }

        # Track consensus performance for self-adjustment
        self.consensus_performance = []

    def _extract_unique_proposals(self, proposals: List[Dict[str, Any]]) -> Dict[Any, List[int]]:
        """
        Extract unique proposal values and their indices with enhanced handling of complex data.

        Args:
            proposals: List of proposed solutions/answers

        Returns:
            Dictionary mapping unique proposal values to lists of their indices
        """
        unique_proposals = {}

        for i, proposal in enumerate(proposals):
            # Extract the actual proposal value
            value = proposal.get('answer', proposal.get('solution', proposal))

            # Handle different data types
            if isinstance(value, (dict, list)):
                # For complex data structures, use a hashable representation
                value_key = self._get_hashable_representation(value)
            else:
                value_key = value

            # Add to unique proposals
            if value_key not in unique_proposals:
                unique_proposals[value_key] = []
            unique_proposals[value_key].append(i)

        # Group similar complex proposals if needed
        if any(isinstance(proposals[idx].get('answer', proposals[idx].get('solution', proposals[idx])), (dict, list))
               for idx_list in unique_proposals.values() for idx in idx_list):
            unique_proposals = self._group_similar_complex_proposals(unique_proposals, proposals)

        return unique_proposals

    def _get_hashable_representation(self, value: Union[Dict, List, Any]) -> str:
        """
        Convert a complex data structure to a hashable representation.

        Args:
            value: The value to convert (dict, list, or other)

        Returns:
            A hashable representation of the value
        """
        if isinstance(value, dict):
            # Sort dictionary items to ensure consistent representation
            return json.dumps(value, sort_keys=True)
        elif isinstance(value, list):
            # Convert list to a tuple of its string representations
            return json.dumps(value)
        else:
            # For simple types, just return as is
            return value

    def _group_similar_complex_proposals(
        self,
        unique_proposals: Dict[Any, List[int]],
        proposals: List[Dict[str, Any]]
    ) -> Dict[Any, List[int]]:
        """
        Group similar complex proposals based on similarity metrics.

        Args:
            unique_proposals: Dictionary mapping unique proposal values to their indices
            proposals: List of proposed solutions/answers

        Returns:
            Updated dictionary with similar complex proposals grouped together
        """
        # If we have few unique proposals, still proceed with grouping
        # We removed the early return here to ensure grouping happens even with 2 proposals

        # Create a mapping from keys to actual values
        key_to_value = {}
        for key, indices in unique_proposals.items():
            if indices:  # Ensure there's at least one index
                idx = indices[0]
                value = proposals[idx].get('answer', proposals[idx].get('solution', proposals[idx]))
                key_to_value[key] = value

        # Calculate similarity between all pairs of complex values
        similarity_pairs = []
        keys = list(unique_proposals.keys())

        for i in range(len(keys)):
            for j in range(i+1, len(keys)):
                key_i, key_j = keys[i], keys[j]
                value_i, value_j = key_to_value.get(key_i), key_to_value.get(key_j)

                # Skip if either value is None
                if value_i is None or value_j is None:
                    continue

                # Handle different types of values
                if isinstance(value_i, (dict, list)) and isinstance(value_j, (dict, list)):
                    # Both are complex data structures
                    similarity = self._calculate_complex_data_similarity(value_i, value_j)
                    logger.debug(f"Complex data similarity between {key_i} and {key_j}: {similarity:.2f}")
                elif isinstance(value_i, (int, float)) and isinstance(value_j, (int, float)):
                    # Both are numbers - calculate relative similarity
                    max_val = max(abs(value_i), abs(value_j))
                    if max_val == 0:
                        similarity = 1.0  # Both are zero
                    else:
                        similarity = 1.0 - min(1.0, abs(value_i - value_j) / max_val)
                    logger.debug(f"Numeric similarity between {key_i} and {key_j}: {similarity:.2f}")
                elif isinstance(value_i, str) and isinstance(value_j, str):
                    # Both are strings - calculate string similarity
                    similarity = self._calculate_string_similarity(value_i, value_j)
                    logger.debug(f"String similarity between {key_i} and {key_j}: {similarity:.2f}")
                else:
                    # Different types, not comparable
                    similarity = 0.0
                    logger.debug(f"Different types between {key_i} and {key_j}, similarity: 0.0")

                if similarity >= self.complex_data_similarity_threshold:
                    similarity_pairs.append((key_i, key_j, similarity))
                    logger.debug(f"Added similarity pair: {key_i}, {key_j}, {similarity:.2f}")

        # Sort by similarity (highest first)
        similarity_pairs.sort(key=lambda x: x[2], reverse=True)
        logger.debug(f"Sorted similarity pairs: {similarity_pairs}")

        # Merge similar proposals
        merged_proposals = unique_proposals.copy()
        merged_keys = {}  # Track which keys have been merged

        for key_i, key_j, similarity in similarity_pairs:
            # Skip if either key has already been merged or is no longer in merged_proposals
            if (key_i in merged_keys or key_j in merged_keys or
                key_i not in merged_proposals or key_j not in merged_proposals):
                logger.debug(f"Skipping pair ({key_i}, {key_j}) - already merged or removed")
                continue

            # Merge the proposals with lower confidence into the one with higher confidence
            indices_i = merged_proposals[key_i]
            indices_j = merged_proposals[key_j]

            # Determine which proposal has higher average confidence
            conf_i = sum(proposals[idx].get('confidence', 0.5) for idx in indices_i) / max(1, len(indices_i))
            conf_j = sum(proposals[idx].get('confidence', 0.5) for idx in indices_j) / max(1, len(indices_j))

            # Also consider the number of agents supporting each proposal
            support_i = len(indices_i)
            support_j = len(indices_j)

            # Combined score: 70% confidence, 30% support
            score_i = 0.7 * conf_i + 0.3 * (support_i / max(1, (support_i + support_j)))
            score_j = 0.7 * conf_j + 0.3 * (support_j / max(1, (support_i + support_j)))

            logger.debug(f"Proposal {key_i}: conf={conf_i:.2f}, support={support_i}, score={score_i:.2f}")
            logger.debug(f"Proposal {key_j}: conf={conf_j:.2f}, support={support_j}, score={score_j:.2f}")

            if score_i >= score_j:
                # Merge j into i
                merged_proposals[key_i].extend(indices_j)
                del merged_proposals[key_j]
                merged_keys[key_j] = key_i

                # Log the merge
                logger.debug(f"Merged proposal {key_j} into {key_i} with similarity {similarity:.2f}")
            else:
                # Merge i into j
                merged_proposals[key_j].extend(indices_i)
                del merged_proposals[key_i]
                merged_keys[key_i] = key_j

                # Log the merge
                logger.debug(f"Merged proposal {key_i} into {key_j} with similarity {similarity:.2f}")

        logger.debug(f"Final merged proposals: {merged_proposals}")
        return merged_proposals

    def _calculate_string_similarity(self, str1: str, str2: str) -> float:
        """
        Calculate similarity between two strings.

        Args:
            str1: First string
            str2: Second string

        Returns:
            Similarity score between 0.0 and 1.0
        """
        # Handle empty strings
        if not str1 and not str2:
            return 1.0
        if not str1 or not str2:
            return 0.0

        # Simple Levenshtein distance-based similarity
        # For longer strings, we could use more sophisticated algorithms
        max_len = max(len(str1), len(str2))
        distance = self._levenshtein_distance(str1, str2)

        # Convert distance to similarity score
        return 1.0 - (distance / max_len)

    def _levenshtein_distance(self, str1: str, str2: str) -> int:
        """
        Calculate Levenshtein distance between two strings.

        Args:
            str1: First string
            str2: Second string

        Returns:
            Levenshtein distance
        """
        # Create a matrix of size (len(str1)+1) x (len(str2)+1)
        m, n = len(str1), len(str2)
        dp = [[0 for _ in range(n+1)] for _ in range(m+1)]

        # Initialize first row and column
        for i in range(m+1):
            dp[i][0] = i
        for j in range(n+1):
            dp[0][j] = j

        # Fill the matrix
        for i in range(1, m+1):
            for j in range(1, n+1):
                cost = 0 if str1[i-1] == str2[j-1] else 1
                dp[i][j] = min(
                    dp[i-1][j] + 1,      # deletion
                    dp[i][j-1] + 1,      # insertion
                    dp[i-1][j-1] + cost  # substitution
                )

        return dp[m][n]

    def _calculate_complex_data_similarity(self, value1: Union[Dict, List], value2: Union[Dict, List]) -> float:
        """
        Calculate similarity between two complex data structures.

        Args:
            value1: First complex value (dict or list)
            value2: Second complex value (dict or list)

        Returns:
            Similarity score between 0.0 and 1.0
        """
        # Convert to sets of items for comparison
        if isinstance(value1, dict) and isinstance(value2, dict):
            # For dictionaries, compare keys and values
            keys1, keys2 = set(value1.keys()), set(value2.keys())
            common_keys = keys1.intersection(keys2)

            # If no common keys, return 0
            if not common_keys:
                return 0.0

            # Calculate Jaccard similarity for keys
            key_similarity = len(common_keys) / len(keys1.union(keys2))

            # Calculate value similarity for common keys
            value_similarities = []
            for key in common_keys:
                val1, val2 = value1[key], value2[key]

                if isinstance(val1, (dict, list)) and isinstance(val2, (dict, list)):
                    # Recursive call for nested structures
                    value_similarities.append(self._calculate_complex_data_similarity(val1, val2))
                elif isinstance(val1, (int, float)) and isinstance(val2, (int, float)):
                    # For numbers, calculate relative similarity
                    max_val = max(abs(val1), abs(val2))
                    if max_val == 0:
                        value_similarities.append(1.0)  # Both are zero
                    else:
                        value_similarities.append(1.0 - min(1.0, abs(val1 - val2) / max_val))
                else:
                    # For other types, simple equality check
                    value_similarities.append(1.0 if val1 == val2 else 0.0)

            # Combine key and value similarities
            if value_similarities:
                avg_value_similarity = sum(value_similarities) / len(value_similarities)
                return 0.7 * key_similarity + 0.3 * avg_value_similarity
            else:
                return key_similarity

        elif isinstance(value1, list) and isinstance(value2, list):
            # For lists, compare elements
            if not value1 or not value2:
                return 0.0 if (value1 or value2) else 1.0  # Both empty = 1.0, one empty = 0.0

            # If lists are of very different lengths, they're less similar
            len_ratio = min(len(value1), len(value2)) / max(len(value1), len(value2))

            # Calculate similarity between elements
            # For simplicity, we'll compare corresponding elements up to the length of the shorter list
            element_similarities = []
            for i in range(min(len(value1), len(value2))):
                val1, val2 = value1[i], value2[i]

                if isinstance(val1, (dict, list)) and isinstance(val2, (dict, list)):
                    # Recursive call for nested structures
                    element_similarities.append(self._calculate_complex_data_similarity(val1, val2))
                elif isinstance(val1, (int, float)) and isinstance(val2, (int, float)):
                    # For numbers, calculate relative similarity
                    max_val = max(abs(val1), abs(val2))
                    if max_val == 0:
                        element_similarities.append(1.0)  # Both are zero
                    else:
                        element_similarities.append(1.0 - min(1.0, abs(val1 - val2) / max_val))
                else:
                    # For other types, simple equality check
                    element_similarities.append(1.0 if val1 == val2 else 0.0)

            # Combine length ratio and element similarities
            if element_similarities:
                avg_element_similarity = sum(element_similarities) / len(element_similarities)
                return 0.3 * len_ratio + 0.7 * avg_element_similarity
            else:
                return len_ratio

        else:
            # Different types, return 0
            return 0.0

    def _calculate_agent_weights(
        self,
        agents: List[Any],
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[Any, float]:
        """
        Calculate weights for each agent based on expertise, historical performance, and other factors.

        Args:
            agents: List of agents participating in consensus
            context: Additional context about the task

        Returns:
            Dictionary mapping agents to their weights
        """
        agent_weights = {}

        for agent in agents:
            # Base weight of 1.0
            weight = 1.0

            # Add weight based on agent's role expertise (if available)
            if hasattr(agent, 'role') and hasattr(agent.role, 'knowledge_areas'):
                expertise_factor = len(agent.role.knowledge_areas) * 0.1
                weight += expertise_factor * self.expertise_weight

            # Enhanced historical performance weighting
            weight = self._apply_performance_history_weighting(agent, weight, context)

            # Consider task-specific expertise if context is provided
            if context and hasattr(agent, 'role') and hasattr(agent.role, 'knowledge_areas'):
                task_type = context.get('task_type', '')
                domains = context.get('domains', [])

                # Check for task type match
                if task_type and task_type in agent.role.knowledge_areas:
                    weight *= 1.5  # 50% boost for task-specific expertise

                # Check for domain matches
                if domains and hasattr(agent.role, 'knowledge_areas'):
                    matching_domains = [d for d in domains if d in agent.role.knowledge_areas]
                    if matching_domains:
                        # Scale boost based on number of matching domains
                        domain_match_ratio = len(matching_domains) / len(domains)
                        weight *= 1.0 + (0.5 * domain_match_ratio)  # Up to 50% boost

            # Apply recency bias if available
            if hasattr(agent, 'recent_performance') and isinstance(agent.recent_performance, list):
                weight = self._apply_recency_bias(agent, weight)

            # Ensure min_evidence_weight is a float
            min_weight = float(self.min_evidence_weight)
            agent_weights[agent] = max(min_weight, weight)
            logger.debug(f"Agent {agent.id} assigned enhanced Bayesian weight {weight:.2f}")

        return agent_weights

    def _apply_performance_history_weighting(
        self,
        agent: Any,
        base_weight: float,
        context: Optional[Dict[str, Any]] = None
    ) -> float:
        """
        Apply weighting based on agent's historical performance.

        Args:
            agent: The agent to evaluate
            base_weight: The base weight before performance adjustment
            context: Additional context about the task

        Returns:
            Adjusted weight based on performance history
        """
        weight = base_weight
        weight_adjustments = []  # Track all adjustments for logging

        # Check for performance metrics
        if hasattr(agent, 'performance_metrics'):
            metrics = agent.performance_metrics

            # General accuracy
            if hasattr(metrics, 'accuracy'):
                accuracy = getattr(metrics, 'accuracy', 0.5)
                # Convert accuracy to a weight factor with enhanced scaling
                accuracy_factor = 1.0 + (accuracy - 0.5) * 2.0 * self.performance_history_weight
                weight *= accuracy_factor
                weight_adjustments.append(("accuracy", accuracy, accuracy_factor))

            # Domain-specific accuracy
            if context and 'domains' in context and hasattr(metrics, 'domain_accuracy'):
                domains = context.get('domains', [])
                domain_accuracies = []
                domain_weights = {}  # Track individual domain weights for logging

                for domain in domains:
                    if hasattr(metrics.domain_accuracy, domain):
                        domain_acc = getattr(metrics.domain_accuracy, domain, 0.5)
                        domain_accuracies.append(domain_acc)
                        domain_weights[domain] = domain_acc

                if domain_accuracies:
                    # Weight domains by relevance if available in context
                    if 'domain_relevance' in context:
                        domain_relevance = context.get('domain_relevance', {})
                        weighted_sum = 0
                        total_relevance = 0

                        for domain in domains:
                            if domain in domain_weights and domain in domain_relevance:
                                relevance = domain_relevance.get(domain, 1.0)
                                weighted_sum += domain_weights[domain] * relevance
                                total_relevance += relevance

                        if total_relevance > 0:
                            avg_domain_accuracy = weighted_sum / total_relevance
                        else:
                            avg_domain_accuracy = sum(domain_accuracies) / len(domain_accuracies)
                    else:
                        # Simple average if no relevance information
                        avg_domain_accuracy = sum(domain_accuracies) / len(domain_accuracies)

                    # Apply stronger weight for domain-specific performance
                    domain_factor = 1.0 + (avg_domain_accuracy - 0.5) * 2.5 * self.performance_history_weight
                    weight *= domain_factor
                    weight_adjustments.append(("domain_accuracy", avg_domain_accuracy, domain_factor))

            # Consider consistency in performance
            if hasattr(metrics, 'consistency'):
                consistency = getattr(metrics, 'consistency', 0.5)  # 0.0-1.0 scale
                # More consistent agents get a boost
                consistency_factor = 1.0 + (consistency - 0.5) * self.performance_history_weight
                weight *= consistency_factor
                weight_adjustments.append(("consistency", consistency, consistency_factor))

            # Consider success rate on similar tasks
            if context and 'task_type' in context and hasattr(metrics, 'task_success_rates'):
                task_type = context.get('task_type', '')
                if hasattr(metrics.task_success_rates, task_type):
                    success_rate = getattr(metrics.task_success_rates, task_type, 0.5)
                    task_factor = 1.0 + (success_rate - 0.5) * 2.0 * self.performance_history_weight
                    weight *= task_factor
                    weight_adjustments.append(("task_success", success_rate, task_factor))

            # Consider error rate (if available)
            if hasattr(metrics, 'error_rate'):
                error_rate = getattr(metrics, 'error_rate', 0.5)  # 0.0-1.0 scale
                # Lower error rate agents get a boost
                error_factor = 1.0 + (0.5 - error_rate) * 1.5 * self.performance_history_weight
                weight *= error_factor
                weight_adjustments.append(("error_rate", error_rate, error_factor))

            # Consider adaptability to new tasks (if available)
            if hasattr(metrics, 'adaptability'):
                adaptability = getattr(metrics, 'adaptability', 0.5)  # 0.0-1.0 scale
                # More adaptable agents get a boost for novel tasks
                if context and context.get('task_novelty', 0.0) > 0.5:
                    adaptability_factor = 1.0 + (adaptability - 0.5) * 2.0 * self.performance_history_weight
                    weight *= adaptability_factor
                    weight_adjustments.append(("adaptability", adaptability, adaptability_factor))

            # Consider learning rate (if available)
            if hasattr(metrics, 'learning_rate'):
                learning_rate = getattr(metrics, 'learning_rate', 0.5)  # 0.0-1.0 scale
                # Agents with higher learning rates get a boost for complex tasks
                if context and context.get('task_complexity', 0.0) > 0.7:
                    learning_factor = 1.0 + (learning_rate - 0.5) * 1.5 * self.performance_history_weight
                    weight *= learning_factor
                    weight_adjustments.append(("learning_rate", learning_rate, learning_factor))

        # Log all weight adjustments if there were any
        if weight_adjustments and hasattr(agent, 'id'):
            adjustment_str = ", ".join([f"{name}={value:.2f}→{factor:.2f}" for name, value, factor in weight_adjustments])
            logger.debug(f"Agent {agent.id} performance adjustments: {adjustment_str}, final weight: {weight:.2f}")

        return weight

    def _apply_recency_bias(self, agent: Any, base_weight: float) -> float:
        """
        Apply recency bias to agent weighting based on recent performance.

        Args:
            agent: The agent to evaluate
            base_weight: The base weight before recency adjustment

        Returns:
            Adjusted weight with recency bias
        """
        weight = base_weight

        # Check for recent performance data
        if hasattr(agent, 'recent_performance') and isinstance(agent.recent_performance, list):
            recent_results = agent.recent_performance

            if recent_results:
                # Apply exponential decay to weight recent results more heavily
                decay_factor = 0.8
                weighted_sum = 0
                weight_sum = 0

                for i, result in enumerate(reversed(recent_results[-10:])):  # Consider last 10 results
                    result_weight = decay_factor ** i
                    weighted_sum += result * result_weight
                    weight_sum += result_weight

                if weight_sum > 0:
                    recent_avg = weighted_sum / weight_sum
                    # Scale to have more impact (0.0-1.0 scale)
                    weight *= 1.0 + (recent_avg - 0.5) * 1.5

        return weight

    def _detect_and_resolve_conflicts(
        self,
        proposals: List[Dict[str, Any]],
        agents: List[Any],
        posterior_probs: Dict[Any, float]
    ) -> Tuple[Dict[Any, float], Dict[str, Any]]:
        """
        Detect and resolve conflicts between agent proposals.

        Args:
            proposals: List of proposed solutions/answers
            agents: List of agents participating in consensus
            posterior_probs: Current posterior probabilities

        Returns:
            Tuple of (adjusted posterior probabilities, conflict metadata)
        """
        # Initialize conflict metadata
        conflict_metadata = {
            "conflicts_detected": False,
            "conflict_pairs": [],
            "resolution_method": None,
            "adjustments_made": {}
        }

        # If we have fewer than 2 unique proposals, there can't be conflicts
        if len(posterior_probs) < 2:
            return posterior_probs, conflict_metadata

        # Find the top two proposals
        sorted_probs = sorted(posterior_probs.items(), key=lambda x: x[1], reverse=True)
        if len(sorted_probs) < 2:
            return posterior_probs, conflict_metadata

        top_value, top_prob = sorted_probs[0]
        second_value, second_prob = sorted_probs[1]

        # Check if there's a potential conflict (close probabilities)
        probability_gap = top_prob - second_prob

        if probability_gap < self.conflict_resolution_threshold:
            conflict_metadata["conflicts_detected"] = True
            conflict_metadata["conflict_pairs"].append((top_value, second_value))

            # Get the agents supporting each proposal
            top_agents = []
            second_agents = []

            for i, proposal in enumerate(proposals):
                value = proposal.get('answer', proposal.get('solution', proposal))
                if isinstance(value, (dict, list)):
                    value = self._get_hashable_representation(value)

                agent_id = proposal.get('agent_id')
                agent = next((a for a in agents if getattr(a, 'id', None) == agent_id), None)

                if agent:
                    if value == top_value:
                        top_agents.append(agent)
                    elif value == second_value:
                        second_agents.append(agent)

            # Apply conflict resolution strategies
            adjusted_probs = posterior_probs.copy()

            # Strategy 1: Domain expertise resolution
            if self._resolve_by_domain_expertise(top_agents, second_agents, top_value, second_value, adjusted_probs, conflict_metadata):
                conflict_metadata["resolution_method"] = "domain_expertise"

            # Strategy 2: Consistency-based resolution
            elif self._resolve_by_consistency(top_agents, second_agents, top_value, second_value, adjusted_probs, conflict_metadata):
                conflict_metadata["resolution_method"] = "consistency"

            # Strategy 3: Confidence-based resolution
            elif self._resolve_by_confidence(proposals, top_value, second_value, adjusted_probs, conflict_metadata):
                conflict_metadata["resolution_method"] = "confidence"

            # Strategy 4: Bayesian adjustment
            else:
                self._resolve_by_bayesian_adjustment(top_value, second_value, adjusted_probs, conflict_metadata)
                conflict_metadata["resolution_method"] = "bayesian_adjustment"

            # Normalize the adjusted probabilities
            total = sum(adjusted_probs.values())
            if total > 0:
                adjusted_probs = {k: v / total for k, v in adjusted_probs.items()}

            return adjusted_probs, conflict_metadata

        # No conflict detected
        return posterior_probs, conflict_metadata

    def _resolve_by_domain_expertise(
        self,
        top_agents: List[Any],
        second_agents: List[Any],
        top_value: Any,
        second_value: Any,
        adjusted_probs: Dict[Any, float],
        conflict_metadata: Dict[str, Any]
    ) -> bool:
        """
        Resolve conflict based on domain expertise of supporting agents.

        Args:
            top_agents: Agents supporting the top proposal
            second_agents: Agents supporting the second proposal
            top_value: The top proposal value
            second_value: The second proposal value
            adjusted_probs: Probabilities to adjust
            conflict_metadata: Metadata about the conflict resolution

        Returns:
            True if resolution was applied, False otherwise
        """
        if not top_agents or not second_agents:
            return False

        # Calculate average domain expertise for each group
        top_expertise = 0
        for agent in top_agents:
            if hasattr(agent, 'role') and hasattr(agent.role, 'knowledge_areas'):
                top_expertise += len(agent.role.knowledge_areas)
        top_expertise = top_expertise / len(top_agents) if top_agents else 0

        second_expertise = 0
        for agent in second_agents:
            if hasattr(agent, 'role') and hasattr(agent.role, 'knowledge_areas'):
                second_expertise += len(agent.role.knowledge_areas)
        second_expertise = second_expertise / len(second_agents) if second_agents else 0

        # If there's a significant difference in expertise, adjust probabilities
        expertise_gap = abs(top_expertise - second_expertise)
        if expertise_gap >= 1.5:  # Threshold for significant expertise difference
            # Determine which group has higher expertise
            if top_expertise > second_expertise:
                # Boost top proposal
                boost_factor = 1.0 + (expertise_gap / 10.0)
                adjusted_probs[top_value] *= boost_factor
                conflict_metadata["adjustments_made"]["expertise_boost"] = {
                    "value": str(top_value),
                    "factor": boost_factor
                }
            else:
                # Boost second proposal
                boost_factor = 1.0 + (expertise_gap / 10.0)
                adjusted_probs[second_value] *= boost_factor
                conflict_metadata["adjustments_made"]["expertise_boost"] = {
                    "value": str(second_value),
                    "factor": boost_factor
                }
            return True

        return False

    def _resolve_by_consistency(
        self,
        top_agents: List[Any],
        second_agents: List[Any],
        top_value: Any,
        second_value: Any,
        adjusted_probs: Dict[Any, float],
        conflict_metadata: Dict[str, Any]
    ) -> bool:
        """
        Resolve conflict based on consistency of supporting agents.

        Args:
            top_agents: Agents supporting the top proposal
            second_agents: Agents supporting the second proposal
            top_value: The top proposal value
            second_value: The second proposal value
            adjusted_probs: Probabilities to adjust
            conflict_metadata: Metadata about the conflict resolution

        Returns:
            True if resolution was applied, False otherwise
        """
        if not top_agents or not second_agents:
            return False

        # Calculate average consistency for each group
        top_consistency = 0
        top_consistency_count = 0
        for agent in top_agents:
            if hasattr(agent, 'performance_metrics') and hasattr(agent.performance_metrics, 'consistency'):
                top_consistency += agent.performance_metrics.consistency
                top_consistency_count += 1
        top_consistency = top_consistency / top_consistency_count if top_consistency_count else 0

        second_consistency = 0
        second_consistency_count = 0
        for agent in second_agents:
            if hasattr(agent, 'performance_metrics') and hasattr(agent.performance_metrics, 'consistency'):
                second_consistency += agent.performance_metrics.consistency
                second_consistency_count += 1
        second_consistency = second_consistency / second_consistency_count if second_consistency_count else 0

        # If we don't have consistency data, can't use this method
        if top_consistency_count == 0 or second_consistency_count == 0:
            return False

        # If there's a significant difference in consistency, adjust probabilities
        consistency_gap = abs(top_consistency - second_consistency)
        if consistency_gap >= 0.2:  # Threshold for significant consistency difference
            # Determine which group has higher consistency
            if top_consistency > second_consistency:
                # Boost top proposal
                boost_factor = 1.0 + consistency_gap
                adjusted_probs[top_value] *= boost_factor
                conflict_metadata["adjustments_made"]["consistency_boost"] = {
                    "value": str(top_value),
                    "factor": boost_factor
                }
            else:
                # Boost second proposal
                boost_factor = 1.0 + consistency_gap
                adjusted_probs[second_value] *= boost_factor
                conflict_metadata["adjustments_made"]["consistency_boost"] = {
                    "value": str(second_value),
                    "factor": boost_factor
                }
            return True

        return False

    def _resolve_by_confidence(
        self,
        proposals: List[Dict[str, Any]],
        top_value: Any,
        second_value: Any,
        adjusted_probs: Dict[Any, float],
        conflict_metadata: Dict[str, Any]
    ) -> bool:
        """
        Resolve conflict based on confidence of proposals.

        Args:
            proposals: List of proposed solutions/answers
            top_value: The top proposal value
            second_value: The second proposal value
            adjusted_probs: Probabilities to adjust
            conflict_metadata: Metadata about the conflict resolution

        Returns:
            True if resolution was applied, False otherwise
        """
        # Calculate average confidence for each proposal
        top_confidence = []
        second_confidence = []

        for proposal in proposals:
            value = proposal.get('answer', proposal.get('solution', proposal))
            if isinstance(value, (dict, list)):
                value = self._get_hashable_representation(value)

            confidence = proposal.get('confidence', 0.5)

            if value == top_value:
                top_confidence.append(confidence)
            elif value == second_value:
                second_confidence.append(confidence)

        if not top_confidence or not second_confidence:
            return False

        avg_top_confidence = sum(top_confidence) / len(top_confidence)
        avg_second_confidence = sum(second_confidence) / len(second_confidence)

        # If there's a significant difference in confidence, adjust probabilities
        confidence_gap = abs(avg_top_confidence - avg_second_confidence)
        if confidence_gap >= 0.15:  # Threshold for significant confidence difference
            # Determine which proposal has higher confidence
            if avg_top_confidence > avg_second_confidence:
                # Boost top proposal
                boost_factor = 1.0 + confidence_gap * 2.0
                adjusted_probs[top_value] *= boost_factor
                conflict_metadata["adjustments_made"]["confidence_boost"] = {
                    "value": str(top_value),
                    "factor": boost_factor
                }
            else:
                # Boost second proposal
                boost_factor = 1.0 + confidence_gap * 2.0
                adjusted_probs[second_value] *= boost_factor
                conflict_metadata["adjustments_made"]["confidence_boost"] = {
                    "value": str(second_value),
                    "factor": boost_factor
                }
            return True

        return False

    def _resolve_by_bayesian_adjustment(
        self,
        top_value: Any,
        second_value: Any,
        adjusted_probs: Dict[Any, float],
        conflict_metadata: Dict[str, Any]
    ) -> None:
        """
        Apply Bayesian adjustment to resolve conflict.

        Args:
            top_value: The top proposal value
            second_value: The second proposal value
            adjusted_probs: Probabilities to adjust
            conflict_metadata: Metadata about the conflict resolution
        """
        # Calculate probability gap
        top_prob = adjusted_probs[top_value]
        second_prob = adjusted_probs[second_value]
        prob_gap = top_prob - second_prob

        # Determine boost factor based on gap
        # Smaller gaps get larger boosts to break ties more decisively
        if prob_gap < 0.05:
            # Very close tie, apply stronger boost
            boost_factor = 1.2  # 20% boost
            reason = "very_close_tie"
        elif prob_gap < 0.1:
            # Moderate tie, apply medium boost
            boost_factor = 1.15  # 15% boost
            reason = "moderate_tie"
        else:
            # Slight tie, apply small boost
            boost_factor = 1.1  # 10% boost
            reason = "slight_tie"

        # Apply boost to top value
        adjusted_probs[top_value] *= boost_factor

        # Record adjustment in metadata
        conflict_metadata["adjustments_made"]["bayesian_adjustment"] = {
            "value": str(top_value),
            "factor": boost_factor,
            "probability_gap": prob_gap,
            "reason": reason
        }

    def _calculate_posterior(
        self,
        unique_proposals: Dict[Any, List[int]],
        proposals: List[Dict[str, Any]],
        agent_weights: Dict[Any, float],
        prior_probs: Dict[Any, float]
    ) -> Tuple[Dict[Any, float], Dict[Any, Dict[str, Any]]]:
        """
        Calculate posterior probabilities using enhanced Bayesian updating.

        Args:
            unique_proposals: Dictionary mapping unique proposal values to their indices
            proposals: List of proposed solutions/answers
            agent_weights: Dictionary mapping agents to their weights
            prior_probs: Dictionary mapping proposal values to their prior probabilities

        Returns:
            Dictionary mapping proposal values to their posterior probabilities
        """
        # Start with prior probabilities
        posterior_probs = prior_probs.copy()

        # Track evidence quality for each proposal
        evidence_quality = {}

        # Track supporting agents for each proposal (for conflict resolution)
        supporting_agents = {value: [] for value in unique_proposals.keys()}

        # For each unique proposal value
        for value, indices in unique_proposals.items():
            # Calculate the likelihood based on agent weights and confidences
            likelihood = 0.0
            evidence_count = 0
            confidence_sum = 0.0
            reasoning_quality_sum = 0.0

            # Track individual agent contributions for this proposal
            agent_contributions = []

            for idx in indices:
                proposal = proposals[idx]
                agent_id = proposal.get('agent_id')

                # Find the agent with this ID
                agent = next((a for a in agent_weights.keys() if getattr(a, 'id', None) == agent_id), None)

                if agent:
                    # Add to supporting agents list
                    supporting_agents[value].append(agent)

                    # Get the agent's weight
                    weight = agent_weights[agent]

                    # Adjust weight by proposal confidence if available
                    confidence = proposal.get('confidence', 1.0)
                    confidence_sum += confidence

                    # Consider reasoning quality if available
                    reasoning_quality = proposal.get('reasoning_quality', 0.5)
                    reasoning_quality_sum += reasoning_quality

                    # Calculate combined quality factor
                    # 60% confidence, 40% reasoning quality
                    quality_factor = 0.6 * confidence + 0.4 * reasoning_quality

                    # Apply non-linear scaling to quality factor
                    # This rewards high quality more than it penalizes low quality
                    scaled_quality = quality_factor ** 0.8

                    # Adjust weight by quality
                    adjusted_weight = weight * (scaled_quality ** self.confidence_scale)

                    # Add to likelihood
                    likelihood += adjusted_weight
                    evidence_count += 1

                    # Record contribution
                    agent_contributions.append({
                        "agent_id": agent_id,
                        "base_weight": weight,
                        "confidence": confidence,
                        "reasoning_quality": reasoning_quality,
                        "adjusted_weight": adjusted_weight
                    })

            # Calculate average confidence and reasoning quality
            avg_confidence = confidence_sum / max(1, evidence_count)
            avg_reasoning_quality = reasoning_quality_sum / max(1, evidence_count)

            # Store evidence quality metrics
            evidence_quality[value] = {
                "count": evidence_count,
                "avg_confidence": avg_confidence,
                "avg_reasoning_quality": avg_reasoning_quality,
                "total_likelihood": likelihood,
                "agent_contributions": agent_contributions
            }

            # Apply enhanced Bayesian rule with evidence count and quality scaling
            if evidence_count > 0:
                # Scale by evidence count (more evidence = stronger update)
                # Use a sigmoid-like function to scale evidence count
                # This gives diminishing returns for very large numbers of agents
                evidence_count_factor = 2.0 / (1.0 + math.exp(-0.3 * evidence_count)) - 1.0

                # Scale by evidence quality
                quality_factor = 0.5 + (0.5 * (avg_confidence + avg_reasoning_quality) / 2.0)

                # Combined evidence factor
                evidence_factor = evidence_count_factor * quality_factor

                # Apply Bayes' rule with evidence scaling
                # Use a more sophisticated update rule that considers prior strength
                prior_strength_factor = 1.0 / (1.0 + self.prior_strength)
                posterior_probs[value] *= (1.0 + likelihood * evidence_factor * prior_strength_factor)

        # Detect and resolve conflicts using supporting agents information
        posterior_probs, conflict_metadata = self._detect_and_resolve_conflicts(
            proposals,
            list(agent_weights.keys()),
            posterior_probs
        )

        # Normalize posterior probabilities
        total = sum(posterior_probs.values())
        if total > 0:
            posterior_probs = {k: v / total for k, v in posterior_probs.items()}

        # Add evidence quality to posterior probabilities
        # This will be used in the final result metadata
        for value in posterior_probs:
            if value in evidence_quality:
                evidence_quality[value]["posterior_probability"] = posterior_probs[value]

        return posterior_probs, evidence_quality

    def combine_beliefs(
        self,
        proposals: List[Dict[str, Any]],
        agents: List[Any],
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Combine beliefs from multiple agents using enhanced Bayesian inference.

        Args:
            proposals: List of proposed solutions/answers from agents
            agents: List of agents participating in consensus
            context: Additional context about the task

        Returns:
            The consensus solution/answer with metadata
        """
        if not proposals or not agents:
            logger.warning("No proposals or agents available for enhanced Bayesian consensus")
            return proposals[0] if proposals else {}

        # Extract unique proposal values
        unique_proposals = self._extract_unique_proposals(proposals)

        if not unique_proposals:
            logger.warning("No valid proposals found for enhanced Bayesian consensus")
            return proposals[0] if proposals else {}

        # Calculate agent expertise weights
        agent_weights = self._calculate_agent_weights(agents, context)

        # Initialize prior probabilities (uniform prior by default)
        prior_probs = self._initialize_prior(unique_proposals, context)

        # Calculate posterior probabilities using enhanced Bayesian updating
        posterior_probs, evidence_quality = self._calculate_posterior(
            unique_proposals,
            proposals,
            agent_weights,
            prior_probs
        )

        # Select the winning proposal
        winning_proposal, winning_confidence = self._select_winning_proposal(
            unique_proposals,
            posterior_probs,
            proposals
        )

        # Add metadata about the consensus process
        if isinstance(winning_proposal, dict):
            # Get the winning value for evidence quality lookup
            winning_value = None
            for value, indices in unique_proposals.items():
                if indices and indices[0] in [proposals.index(p) for p in proposals if p == winning_proposal]:
                    winning_value = value
                    break

            # Get evidence quality for the winning proposal
            winning_evidence = evidence_quality.get(winning_value, {}) if winning_value else {}

            # Create enhanced metadata
            winning_proposal["consensus_metadata"] = {
                "method": "enhanced_bayesian_consensus",
                "confidence": winning_confidence,
                "posterior_probabilities": {str(k): v for k, v in posterior_probs.items()},
                "agent_weights": {getattr(agent, 'id', str(i)): weight
                                 for i, (agent, weight) in enumerate(agent_weights.items())},
                "evidence_quality": {
                    str(k): {
                        "count": v.get("count", 0),
                        "confidence": v.get("avg_confidence", 0),
                        "reasoning_quality": v.get("avg_reasoning_quality", 0),
                        "posterior_probability": v.get("posterior_probability", 0)
                    } for k, v in evidence_quality.items()
                },
                "winning_evidence": {
                    "count": winning_evidence.get("count", 0),
                    "confidence": winning_evidence.get("avg_confidence", 0),
                    "reasoning_quality": winning_evidence.get("avg_reasoning_quality", 0),
                    "supporting_agents": len(winning_evidence.get("agent_contributions", []))
                },
                "parameters": {
                    "prior_strength": self.prior_strength,
                    "expertise_weight": self.expertise_weight,
                    "confidence_scale": self.confidence_scale,
                    "performance_history_weight": self.performance_history_weight
                }
            }

        # Self-adjust parameters based on results
        self._self_adjust_parameters(winning_confidence, context)

        return winning_proposal

    def _self_adjust_parameters(self, confidence: float, context: Optional[Dict[str, Any]] = None) -> None:
        """
        Self-adjust parameters based on consensus results.

        Args:
            confidence: Confidence in the winning proposal
            context: Additional context about the task
        """
        # Record this consensus performance
        self.consensus_performance.append(confidence)

        # Only adjust after we have enough data points
        if len(self.consensus_performance) < 5:
            return

        # Calculate recent average confidence
        recent_confidence = sum(self.consensus_performance[-5:]) / 5

        # If recent confidence is low, adjust parameters
        if recent_confidence < 0.7:
            # Increase prior strength if confidence is low
            # This makes the system more conservative
            adjustment = self.self_adjustment_rate * (0.7 - recent_confidence)
            self.prior_strength += adjustment
            self.parameter_history['prior_strength'].append(self.prior_strength)

            # Increase expertise weight to rely more on expert agents
            self.expertise_weight += adjustment
            self.parameter_history['expertise_weight'].append(self.expertise_weight)

            logger.info(f"Self-adjusted parameters due to low confidence ({recent_confidence:.2f})")

        # If recent confidence is very high, we might be overconfident
        # Slightly reduce weights to be more balanced
        elif recent_confidence > 0.9:
            adjustment = self.self_adjustment_rate * 0.5  # Smaller adjustment for high confidence

            # Slightly decrease prior strength to be more open to new evidence
            self.prior_strength = max(0.5, self.prior_strength - adjustment)
            self.parameter_history['prior_strength'].append(self.prior_strength)

            # Slightly decrease performance history weight to avoid overreliance on past performance
            self.performance_history_weight = max(0.5, self.performance_history_weight - adjustment)
            self.parameter_history['performance_history_weight'].append(self.performance_history_weight)

            logger.info(f"Self-adjusted parameters due to high confidence ({recent_confidence:.2f})")

        # If context contains feedback about the consensus quality, use it
        if context and 'consensus_feedback' in context:
            feedback = context['consensus_feedback']
            if isinstance(feedback, dict) and 'quality' in feedback:
                quality = feedback['quality']  # Expected to be between 0.0 and 1.0

                # Adjust based on external feedback
                if quality < 0.6:  # Poor quality consensus
                    # Make larger adjustments based on explicit feedback
                    adjustment = self.self_adjustment_rate * 2.0 * (0.6 - quality)

                    # Adjust parameters that might improve quality
                    self.confidence_scale = max(0.5, min(2.0, self.confidence_scale + adjustment))
                    self.parameter_history['confidence_scale'].append(self.confidence_scale)

                    logger.info(f"Self-adjusted parameters due to explicit feedback (quality: {quality:.2f})")
