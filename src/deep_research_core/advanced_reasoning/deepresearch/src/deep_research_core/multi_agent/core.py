"""
Core implementation of the Multi-Agent Collaboration framework.

This module provides the foundational classes and functions for creating
collaborative multi-agent systems in Deep Research Core.
"""

import time
import uuid
from typing import Dict, List, Any, Optional, Union, Tuple, Callable
from ..utils.structured_logging import get_logger
from ..reasoning.base import BaseReasoner
from ..tools.base import BaseTool
from .task_decomposer import TaskDecomposer

logger = get_logger(__name__)


class AgentMessage:
    """
    Message exchanged between agents in a multi-agent system.

    This class represents a standardized message format for agent communication,
    including metadata about sender, receiver, message type, and content.
    """

    def __init__(
        self,
        sender_id: str,
        receiver_id: str,
        message_type: str,
        content: Dict[str, Any],
        timestamp: Optional[float] = None,
        message_id: Optional[str] = None,
        in_reply_to: Optional[str] = None
    ):
        """
        Initialize an agent message.

        Args:
            sender_id: ID of the sending agent
            receiver_id: ID of the receiving agent
            message_type: Type of message ("request", "response", "inform", "query")
            content: Message content as a dictionary
            timestamp: Optional message timestamp (defaults to current time)
            message_id: Optional unique message ID (defaults to a UUID)
            in_reply_to: Optional ID of the message this is replying to
        """
        self.sender_id = sender_id
        self.receiver_id = receiver_id
        self.message_type = message_type  # e.g., "request", "response", "inform", "query"
        self.content = content
        self.timestamp = timestamp or time.time()
        self.message_id = message_id or str(uuid.uuid4())
        self.in_reply_to = in_reply_to  # ID of the message this is replying to

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the message to a dictionary representation.

        Returns:
            Dictionary representation of the message
        """
        return {
            "sender_id": self.sender_id,
            "receiver_id": self.receiver_id,
            "message_type": self.message_type,
            "content": self.content,
            "timestamp": self.timestamp,
            "message_id": self.message_id,
            "in_reply_to": self.in_reply_to
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "AgentMessage":
        """
        Create a message from a dictionary representation.

        Args:
            data: Dictionary representation of a message

        Returns:
            AgentMessage instance
        """
        return cls(
            sender_id=data["sender_id"],
            receiver_id=data["receiver_id"],
            message_type=data["message_type"],
            content=data["content"],
            timestamp=data["timestamp"],
            message_id=data["message_id"],
            in_reply_to=data.get("in_reply_to")
        )


class AgentCommunicationProtocol:
    """
    Communication protocol for agents in a multi-agent system.

    This class handles message routing and delivery between agents.
    """

    def __init__(self):
        """Initialize the communication protocol."""
        self.message_history = []

    def send_message(
        self,
        message: AgentMessage,
        agents: Dict[str, "Agent"]
    ) -> bool:
        """
        Send a message from one agent to another.

        Args:
            message: The message to send
            agents: Dictionary of agent_id -> agent instances

        Returns:
            True if delivered successfully, False otherwise
        """
        if message.receiver_id not in agents:
            logger.error(f"Receiver agent {message.receiver_id} not found")
            return False

        # Record the message in history
        self.message_history.append(message.to_dict())

        # Deliver to the recipient
        # (In a real implementation, this could be asynchronous)
        logger.debug(f"Delivering message from {message.sender_id} to {message.receiver_id}")

        return True

    def broadcast_message(
        self,
        sender_id: str,
        message_type: str,
        content: Dict[str, Any],
        agents: Dict[str, "Agent"]
    ) -> List[str]:
        """
        Broadcast a message to all agents except the sender.

        Args:
            sender_id: ID of the sending agent
            message_type: Type of message
            content: Message content
            agents: Dictionary of agent_id -> agent instances

        Returns:
            List of recipient IDs that received the message
        """
        recipients = []

        for agent_id, agent in agents.items():
            if agent_id != sender_id:
                message = AgentMessage(
                    sender_id=sender_id,
                    receiver_id=agent_id,
                    message_type=message_type,
                    content=content
                )

                if self.send_message(message, agents):
                    recipients.append(agent_id)

        return recipients


class AgentRole:
    """
    Role for an agent in a multi-agent system.

    This class defines the specialized capabilities, knowledge areas,
    and system prompt for an agent role.
    """

    def __init__(
        self,
        name: str,
        description: str,
        capabilities: List[str],
        knowledge_areas: List[str],
        system_prompt: str
    ):
        """
        Initialize an agent role.

        Args:
            name: Name of the role
            description: Description of the role
            capabilities: List of capabilities the role provides
            knowledge_areas: List of knowledge areas the role specializes in
            system_prompt: System prompt to use for agents with this role
        """
        self.name = name
        self.description = description
        self.capabilities = capabilities
        self.knowledge_areas = knowledge_areas
        self.system_prompt = system_prompt

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the role to a dictionary representation.

        Returns:
            Dictionary representation of the role
        """
        return {
            "name": self.name,
            "description": self.description,
            "capabilities": self.capabilities,
            "knowledge_areas": self.knowledge_areas,
            "system_prompt": self.system_prompt
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "AgentRole":
        """
        Create a role from a dictionary representation.

        Args:
            data: Dictionary representation of a role

        Returns:
            AgentRole instance
        """
        return cls(
            name=data["name"],
            description=data["description"],
            capabilities=data["capabilities"],
            knowledge_areas=data["knowledge_areas"],
            system_prompt=data["system_prompt"]
        )


class ConsensusMechanism:
    """
    Mechanism for reaching consensus among agents.

    This class provides methods for agents to reach agreement
    when there are multiple proposed solutions or conflicting opinions.
    """

    def __init__(
        self,
        strategy: str = "voting",  # "voting", "weighted_voting", "discussion", "bayesian", "expert_weighted", "multi_round"
        confidence_threshold: float = 0.7,
        max_iterations: int = 3
    ):
        """
        Initialize the consensus mechanism.

        Args:
            strategy: Consensus strategy to use
            confidence_threshold: Threshold for confidence in a solution
            max_iterations: Maximum number of iterations for reaching consensus
        """
        self.strategy = strategy
        self.confidence_threshold = confidence_threshold
        self.max_iterations = max_iterations

        # Initialize advanced consensus mechanisms if needed
        self._bayesian_consensus = None
        self._expert_weighted_consensus = None
        self._multi_round_consensus = None

        if strategy in ["bayesian", "expert_weighted", "multi_round"]:
            try:
                from .bayesian_consensus import BayesianConsensus, ExpertWeightedConsensus, MultiRoundConsensus
                self._bayesian_consensus = BayesianConsensus()
                self._expert_weighted_consensus = ExpertWeightedConsensus()
                self._multi_round_consensus = MultiRoundConsensus()
            except ImportError:
                logger.warning("Advanced consensus modules not available. Falling back to weighted voting.")
                self.strategy = "weighted_voting"

    def reach_consensus(
        self,
        proposals: List[Dict[str, Any]],
        agents: List["Agent"],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Reach consensus among agents based on the chosen strategy.

        Args:
            proposals: List of proposed solutions/answers
            agents: List of agents participating in consensus
            context: Additional context about the task

        Returns:
            The consensus solution/answer
        """
        if self.strategy == "voting":
            return self._simple_voting(proposals)
        elif self.strategy == "weighted_voting":
            return self._weighted_voting(proposals, agents)
        elif self.strategy == "discussion":
            return self._discussion_based(proposals, agents, context)
        elif self.strategy == "bayesian":
            # Use Bayesian consensus if available
            if self._bayesian_consensus:
                return self._bayesian_consensus.combine_beliefs(proposals, agents, context)
            else:
                logger.warning("Bayesian consensus not available. Falling back to weighted voting.")
                return self._weighted_voting(proposals, agents)
        elif self.strategy == "expert_weighted":
            # Use Expert-weighted consensus if available
            if self._expert_weighted_consensus:
                return self._expert_weighted_consensus.reach_consensus(proposals, agents, context)
            else:
                logger.warning("Expert-weighted consensus not available. Falling back to weighted voting.")
                return self._weighted_voting(proposals, agents)
        elif self.strategy == "multi_round":
            # Use Multi-round consensus if available
            if self._multi_round_consensus:
                return self._multi_round_consensus.reach_consensus(proposals, agents, context)
            else:
                logger.warning("Multi-round consensus not available. Falling back to weighted voting.")
                return self._weighted_voting(proposals, agents)
        else:
            raise ValueError(f"Unknown consensus strategy: {self.strategy}")

    def _simple_voting(self, proposals: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Simple majority voting among proposals.

        Args:
            proposals: List of proposed solutions/answers

        Returns:
            The winning proposal (or the first one in case of a tie)
        """
        if not proposals:
            logger.warning("No proposals available for voting")
            return {}

        # Extract the actual proposal values for comparison
        # We assume each proposal has an 'answer' or 'solution' field
        proposal_values = []
        for p in proposals:
            value = p.get('answer', p.get('solution', p))
            if isinstance(value, dict):
                # Convert dict to a hashable representation for counting
                value = str(sorted(value.items()))
            proposal_values.append(value)

        # Count occurrences of each unique proposal
        from collections import Counter
        proposal_counts = Counter(proposal_values)

        # Find the most common proposal
        most_common_value, count = proposal_counts.most_common(1)[0]
        logger.info(f"Simple voting selected proposal with {count}/{len(proposals)} votes")

        # If there's a tie, we'd need a tiebreaker
        # Here we choose the first proposal that matches the winning value
        for i, value in enumerate(proposal_values):
            if value == most_common_value:
                winning_proposal = proposals[i]
                # Add metadata about the voting results
                if isinstance(winning_proposal, dict):
                    winning_proposal["consensus_metadata"] = {
                        "method": "simple_voting",
                        "vote_count": count,
                        "total_votes": len(proposals),
                        "confidence": count / len(proposals)
                    }
                return winning_proposal

        # Fallback (shouldn't reach here)
        return proposals[0]

    def _weighted_voting(
        self,
        proposals: List[Dict[str, Any]],
        agents: List["Agent"]
    ) -> Dict[str, Any]:
        """
        Weighted voting based on agent confidence and expertise.

        Args:
            proposals: List of proposed solutions/answers
            agents: List of agents participating in consensus

        Returns:
            The winning proposal based on weighted votes
        """
        if not proposals or not agents:
            logger.warning("No proposals or agents available for weighted voting")
            return proposals[0] if proposals else {}

        # Calculate weights for each agent based on their expertise and confidence
        agent_weights = {}
        for agent in agents:
            # Base weight of 1.0
            weight = 1.0

            # Add weight based on agent's role expertise (if available)
            if hasattr(agent, 'role') and hasattr(agent.role, 'knowledge_areas'):
                # For now, we use a simple heuristic: more knowledge areas = higher weight
                weight += len(agent.role.knowledge_areas) * 0.1

            # Add weight based on confidence (if available in the proposal)
            for p in proposals:
                if p.get('agent_id') == agent.id and 'confidence' in p:
                    weight *= max(0.5, min(1.5, p.get('confidence', 1.0)))
                    break

            agent_weights[agent.id] = weight
            logger.debug(f"Agent {agent.id} assigned weight {weight}")

        # Calculate weighted votes for each proposal
        proposal_scores = {}
        for i, proposal in enumerate(proposals):
            agent_id = proposal.get('agent_id')
            if not agent_id:
                logger.warning(f"Proposal {i} missing agent_id, using default weight")
                weight = 1.0
            else:
                weight = agent_weights.get(agent_id, 1.0)

            # Extract the value for comparison
            value = proposal.get('answer', proposal.get('solution', proposal))
            if isinstance(value, dict):
                value = str(sorted(value.items()))

            # Add the weighted vote
            if value in proposal_scores:
                proposal_scores[value] += weight
            else:
                proposal_scores[value] = weight

        # Find the proposal with the highest weighted score
        if not proposal_scores:
            logger.warning("No scoreable proposals found")
            return proposals[0] if proposals else {}

        winning_value = max(proposal_scores.items(), key=lambda x: x[1])
        logger.info(f"Weighted voting selected proposal with score {winning_value[1]}")

        # Find the first proposal that matches the winning value
        for i, proposal in enumerate(proposals):
            value = proposal.get('answer', proposal.get('solution', proposal))
            if isinstance(value, dict):
                value = str(sorted(value.items()))

            if value == winning_value[0]:
                winning_proposal = proposals[i]
                # Add metadata about the voting results
                if isinstance(winning_proposal, dict):
                    winning_proposal["consensus_metadata"] = {
                        "method": "weighted_voting",
                        "score": winning_value[1],
                        "total_votes": len(proposals),
                        "confidence": winning_value[1] / sum(proposal_scores.values())
                    }
                return winning_proposal

        # Fallback (shouldn't reach here)
        return proposals[0] if proposals else {}

    def _discussion_based(
        self,
        proposals: List[Dict[str, Any]],
        agents: List["Agent"],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Discussion-based consensus through agent interaction.

        Args:
            proposals: List of proposed solutions/answers
            agents: List of agents participating in consensus
            context: Additional context about the task

        Returns:
            The consensus solution after discussion
        """
        if not proposals or not agents:
            logger.warning("No proposals or agents available for discussion-based consensus")
            return proposals[0] if proposals else {}

        logger.info(f"Starting discussion-based consensus with {len(agents)} agents and {len(proposals)} proposals")

        # Create a mapping of agent IDs to agents for easier access
        agent_map = {agent.id: agent for agent in agents}

        # Create a mapping of proposals to their originating agents
        proposal_agents = {}
        for proposal in proposals:
            agent_id = proposal.get('agent_id')
            if agent_id in agent_map:
                proposal_agents[str(proposal)] = agent_id

        # Initialize discussion state
        discussion_rounds = []
        current_proposals = proposals.copy()
        consensus_reached = False
        confidence_scores = {}

        # Run discussion for a maximum number of iterations
        for iteration in range(self.max_iterations):
            logger.info(f"Discussion round {iteration + 1}")

            # Format the current state of proposals for discussion
            discussion_context = self._format_discussion_context(
                current_proposals,
                discussion_rounds,
                context
            )

            # Each agent evaluates the proposals and provides feedback
            round_feedback = []
            for agent in agents:
                # Skip agents that don't have language models for reasoning
                if not hasattr(agent, 'language_model'):
                    continue

                # Generate feedback from this agent about the proposals
                feedback = self._generate_agent_feedback(
                    agent,
                    current_proposals,
                    discussion_context
                )

                round_feedback.append({
                    'agent_id': agent.id,
                    'feedback': feedback
                })

                # Update confidence scores based on feedback
                for proposal_idx, confidence in feedback.get('confidence_scores', {}).items():
                    try:
                        proposal_idx = int(proposal_idx)
                        if 0 <= proposal_idx < len(current_proposals):
                            proposal_key = str(current_proposals[proposal_idx])
                            if proposal_key not in confidence_scores:
                                confidence_scores[proposal_key] = []
                            confidence_scores[proposal_key].append(confidence)
                    except (ValueError, IndexError):
                        continue

            # Add this round to the discussion history
            discussion_rounds.append({
                'round': iteration + 1,
                'feedback': round_feedback
            })

            # Check if we've reached consensus
            consensus_proposal, consensus_confidence = self._check_discussion_consensus(
                current_proposals,
                confidence_scores
            )

            if consensus_proposal and consensus_confidence >= self.confidence_threshold:
                logger.info(f"Consensus reached with confidence {consensus_confidence:.2f}")
                consensus_reached = True
                break

            # If no consensus yet, update proposals based on feedback
            if not consensus_reached and iteration < self.max_iterations - 1:
                current_proposals = self._update_proposals_from_feedback(
                    current_proposals,
                    round_feedback,
                    agents
                )

        # If we reached consensus, return that proposal
        if consensus_reached and consensus_proposal:
            # Add metadata about the consensus process
            if isinstance(consensus_proposal, dict):
                consensus_proposal["consensus_metadata"] = {
                    "method": "discussion_based",
                    "rounds": len(discussion_rounds),
                    "confidence": consensus_confidence,
                    "unanimous": all(score >= self.confidence_threshold for score in confidence_scores.get(str(consensus_proposal), []))
                }
            return consensus_proposal

        # If no consensus was reached, fall back to weighted voting
        logger.warning(f"No consensus reached after {self.max_iterations} rounds, falling back to weighted voting")
        return self._weighted_voting(proposals, agents)

    def _format_discussion_context(
        self,
        proposals: List[Dict[str, Any]],
        discussion_rounds: List[Dict[str, Any]],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Format the context for the current discussion round.

        Args:
            proposals: Current list of proposals
            discussion_rounds: History of discussion rounds
            context: Original task context

        Returns:
            Formatted discussion context
        """
        # Extract the task description from the context
        task_description = context.get('task_description', 'Unknown task')

        # Format the proposals
        formatted_proposals = []
        for i, proposal in enumerate(proposals):
            # Extract the core proposal value
            value = proposal.get('answer', proposal.get('solution', proposal))
            if isinstance(value, dict):
                # For dict values, convert to a string representation
                value = str(value)

            # Add to formatted proposals
            formatted_proposals.append({
                'index': i,
                'value': value,
                'agent_id': proposal.get('agent_id', 'unknown'),
                'confidence': proposal.get('confidence', 0.5)
            })

        # Format the discussion history
        formatted_history = []
        for round_data in discussion_rounds:
            round_feedback = []
            for feedback in round_data.get('feedback', []):
                round_feedback.append({
                    'agent_id': feedback.get('agent_id', 'unknown'),
                    'comments': feedback.get('feedback', {}).get('comments', '')
                })
            formatted_history.append({
                'round': round_data.get('round', 0),
                'feedback': round_feedback
            })

        return {
            'task_description': task_description,
            'proposals': formatted_proposals,
            'discussion_history': formatted_history,
            'original_context': context
        }

    def _generate_agent_feedback(
        self,
        agent: "Agent",
        proposals: List[Dict[str, Any]],
        discussion_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generate feedback from an agent about the current proposals.

        Args:
            agent: The agent generating feedback
            proposals: List of current proposals
            discussion_context: Context for the discussion

        Returns:
            Agent's feedback about the proposals
        """
        # This is a simplified implementation
        # In a real system, we would use the agent's language model to generate feedback

        # For now, we'll generate some simple feedback based on proposal confidence
        confidence_scores = {}
        comments = []

        for i, proposal in enumerate(proposals):
            # Base confidence on the proposal's own confidence if available
            base_confidence = proposal.get('confidence', 0.5)

            # Adjust based on agent's role expertise
            adjustment = 0.0
            if hasattr(agent, 'role') and hasattr(agent.role, 'knowledge_areas'):
                # Simple heuristic: more knowledge areas = higher adjustment
                adjustment = min(0.3, len(agent.role.knowledge_areas) * 0.05)

            # Random variation to simulate different agent opinions
            import random
            variation = random.uniform(-0.1, 0.1)

            # Calculate final confidence score
            confidence = max(0.0, min(1.0, base_confidence + adjustment + variation))
            confidence_scores[str(i)] = confidence

            # Generate a comment
            if confidence > 0.7:
                comments.append(f"Proposal {i} seems strong and well-reasoned.")
            elif confidence > 0.4:
                comments.append(f"Proposal {i} has some merit but could be improved.")
            else:
                comments.append(f"Proposal {i} has significant weaknesses.")

        return {
            'confidence_scores': confidence_scores,
            'comments': ". ".join(comments),
            'suggested_improvements': "Consider combining the strengths of multiple proposals."
        }

    def _check_discussion_consensus(
        self,
        proposals: List[Dict[str, Any]],
        confidence_scores: Dict[str, List[float]]
    ) -> Tuple[Optional[Dict[str, Any]], float]:
        """
        Check if consensus has been reached in the discussion.

        Args:
            proposals: Current list of proposals
            confidence_scores: Dictionary mapping proposal keys to lists of confidence scores

        Returns:
            Tuple of (consensus proposal or None, consensus confidence)
        """
        if not proposals or not confidence_scores:
            return None, 0.0

        # Calculate average confidence for each proposal
        avg_confidence = {}
        for proposal_key, scores in confidence_scores.items():
            if scores:
                avg_confidence[proposal_key] = sum(scores) / len(scores)

        # Find the proposal with the highest average confidence
        if not avg_confidence:
            return None, 0.0

        best_proposal_key = max(avg_confidence.items(), key=lambda x: x[1])
        best_confidence = best_proposal_key[1]

        # Find the actual proposal object
        for proposal in proposals:
            if str(proposal) == best_proposal_key[0]:
                return proposal, best_confidence

        # If we couldn't find the proposal (shouldn't happen), return the first one
        return proposals[0], best_confidence

    def _update_proposals_from_feedback(
        self,
        proposals: List[Dict[str, Any]],
        feedback: List[Dict[str, Any]],
        agents: List["Agent"]
    ) -> List[Dict[str, Any]]:
        """
        Update proposals based on feedback from the discussion round.

        Args:
            proposals: Current list of proposals
            feedback: Feedback from agents in the current round
            agents: List of agents participating in consensus

        Returns:
            Updated list of proposals
        """
        # In a real implementation, we would use the feedback to generate improved proposals
        # For this simplified version, we'll just keep the top proposals based on feedback

        # Extract confidence scores from feedback
        proposal_scores = {}
        for i, proposal in enumerate(proposals):
            scores = []
            for agent_feedback in feedback:
                agent_scores = agent_feedback.get('feedback', {}).get('confidence_scores', {})
                if str(i) in agent_scores:
                    scores.append(agent_scores[str(i)])

            if scores:
                proposal_scores[i] = sum(scores) / len(scores)
            else:
                proposal_scores[i] = 0.0

        # Sort proposals by score
        sorted_indices = sorted(proposal_scores.keys(), key=lambda i: proposal_scores[i], reverse=True)

        # Keep only the top half of proposals
        keep_count = max(2, len(proposals) // 2)
        kept_indices = sorted_indices[:keep_count]

        # Return the kept proposals
        return [proposals[i] for i in kept_indices]


class SharedMemory:
    """
    Shared memory system for multi-agent collaboration.

    This class provides a unified knowledge base that all agents
    can access and update during collaboration. It supports advanced
    querying capabilities using a simple query language.
    """

    def __init__(
        self,
        storage_type: str = "in_memory",
        enable_versioning: bool = False,
        max_versions_per_key: int = 100,
        conflict_resolution_strategy: str = "last_write_wins",
        enable_compression: bool = False,
        compression_level: int = 6,
        min_size_for_compression: int = 1024,
        compression_threshold: float = 0.8
    ):
        """
        Initialize the shared memory system.

        Args:
            storage_type: Type of storage to use ("in_memory", "file", "database")
            enable_versioning: Whether to enable version control for memory items
            max_versions_per_key: Maximum number of versions to keep per key
            conflict_resolution_strategy: Strategy for resolving conflicts ("last_write_wins",
                                         "first_write_wins", "merge_fields", "manual_resolution")
            enable_compression: Whether to enable memory compression
            compression_level: Compression level (0-9, higher = more compression but slower)
            min_size_for_compression: Minimum size in bytes for compression to be applied
            compression_threshold: Threshold for compression ratio (0.0-1.0)
        """
        self.storage_type = storage_type
        self.memory = {}
        self.access_log = []
        self.enable_versioning = enable_versioning
        self.enable_compression = enable_compression

        # Initialize version manager if versioning is enabled
        self.version_manager = None
        if enable_versioning:
            try:
                from .memory_version import (
                    MemoryVersionManager, ConflictResolver, ConflictResolutionStrategy
                )

                # Convert string strategy to enum
                strategy_map = {
                    "last_write_wins": ConflictResolutionStrategy.LAST_WRITE_WINS,
                    "first_write_wins": ConflictResolutionStrategy.FIRST_WRITE_WINS,
                    "merge_fields": ConflictResolutionStrategy.MERGE_FIELDS,
                    "manual_resolution": ConflictResolutionStrategy.MANUAL_RESOLUTION
                }

                strategy = strategy_map.get(
                    conflict_resolution_strategy,
                    ConflictResolutionStrategy.LAST_WRITE_WINS
                )

                # Create conflict resolver
                conflict_resolver = ConflictResolver(default_strategy=strategy)

                # Create version manager
                self.version_manager = MemoryVersionManager(
                    conflict_resolver=conflict_resolver,
                    max_versions_per_key=max_versions_per_key,
                    enable_pruning=True
                )

                logger.info(f"Memory versioning enabled with strategy: {conflict_resolution_strategy}")
            except ImportError:
                logger.warning("Memory versioning requested but not available. Versioning will be disabled.")
                self.enable_versioning = False

        # Initialize memory compressor if compression is enabled
        self.memory_compressor = None
        if enable_compression:
            try:
                from .memory_compression import MemoryCompressor

                # Create memory compressor
                self.memory_compressor = MemoryCompressor(
                    compression_level=compression_level,
                    min_size_for_compression=min_size_for_compression,
                    enable_binary_compression=True,
                    enable_json_optimization=True,
                    enable_text_compression=True,
                    enable_semantic_compression=False,
                    compression_threshold=compression_threshold
                )

                logger.info(f"Memory compression enabled with level: {compression_level}")
            except ImportError:
                logger.warning("Memory compression requested but not available. Compression will be disabled.")
                self.enable_compression = False

        # Import query parser lazily to avoid circular imports
        try:
            from .query_parser import parse_query
            self._parse_query = parse_query
        except ImportError:
            logger.warning("Query parser not available. Advanced querying will be limited.")
            self._parse_query = None

    def store(
        self,
        key: str,
        value: Any,
        metadata: Dict[str, Any] = None,
        agent_id: str = None,
        comment: str = None,
        compress: bool = None
    ) -> None:
        """
        Store information in shared memory.

        Args:
            key: Key to store the value under
            value: Value to store
            metadata: Optional metadata about the value
            agent_id: ID of the agent storing the value
            comment: Optional comment about this change (for versioning)
            compress: Whether to compress the value (overrides default setting)
        """
        # Initialize metadata
        metadata = metadata or {}

        # Apply compression if enabled
        if (compress is True) or (compress is None and self.enable_compression and self.memory_compressor):
            try:
                # Compress the value
                compressed_value, updated_metadata = self.memory_compressor.compress(value, metadata)

                # Update value and metadata
                value = compressed_value
                metadata = updated_metadata

                logger.debug(f"Compressed value for key {key}")
            except Exception as e:
                logger.error(f"Error compressing value for key {key}: {e}")

        # Create memory item
        memory_item = {
            "key": key,  # Store key in the item for easier querying
            "value": value,
            "metadata": metadata,
            "created_by": agent_id,
            "created_at": time.time(),
            "last_updated": time.time()
        }

        # Store in memory
        self.memory[key] = memory_item

        # Create version if versioning is enabled
        if self.enable_versioning and self.version_manager:
            try:
                from .memory_version import VersionOperation

                # Create a new version
                self.version_manager.create_version(
                    key=key,
                    value=value,  # Store the compressed value in version history
                    metadata=metadata,
                    operation=VersionOperation.CREATE,
                    agent_id=agent_id,
                    comment=comment
                )

                logger.debug(f"Created version for key {key}")
            except Exception as e:
                logger.error(f"Error creating version for key {key}: {e}")

        # Log access
        self._log_access("store", key, agent_id)

    def retrieve(
        self,
        key: str,
        agent_id: str = None,
        version_id: str = None,
        decompress: bool = True
    ) -> Optional[Any]:
        """
        Retrieve information from shared memory.

        Args:
            key: Key to retrieve the value for
            agent_id: ID of the agent retrieving the value
            version_id: Optional specific version to retrieve (if versioning is enabled)
            decompress: Whether to decompress the value if it was compressed

        Returns:
            The stored value, or None if not found
        """
        # Log access
        self._log_access("retrieve", key, agent_id)

        # If versioning is enabled and a specific version is requested
        if self.enable_versioning and self.version_manager and version_id:
            try:
                # Get the specific version
                version = self.version_manager.get_version(key, version_id)
                if version:
                    value = version.value
                    metadata = version.metadata

                    # Decompress if needed
                    if decompress and self.enable_compression and self.memory_compressor:
                        if metadata and "compression_info" in metadata and metadata["compression_info"].get("compressed", False):
                            try:
                                value = self.memory_compressor.decompress(value, metadata)
                            except Exception as e:
                                logger.error(f"Error decompressing version {version_id} for key {key}: {e}")

                    return value
                else:
                    logger.warning(f"Version {version_id} not found for key {key}")
            except Exception as e:
                logger.error(f"Error retrieving version {version_id} for key {key}: {e}")

        # Default behavior: retrieve from current memory
        if key in self.memory:
            value = self.memory[key]["value"]
            metadata = self.memory[key]["metadata"]

            # Decompress if needed
            if decompress and self.enable_compression and self.memory_compressor:
                if metadata and "compression_info" in metadata and metadata["compression_info"].get("compressed", False):
                    try:
                        value = self.memory_compressor.decompress(value, metadata)
                    except Exception as e:
                        logger.error(f"Error decompressing value for key {key}: {e}")

            return value

        return None

    def update(
        self,
        key: str,
        value: Any,
        metadata: Dict[str, Any] = None,
        agent_id: str = None,
        comment: str = None,
        parent_version_id: str = None,
        compress: bool = None
    ) -> bool:
        """
        Update existing information in shared memory.

        Args:
            key: Key to update the value for
            value: New value
            metadata: Optional metadata to update
            agent_id: ID of the agent updating the value
            comment: Optional comment about this change (for versioning)
            parent_version_id: Optional parent version ID (if versioning is enabled)
            compress: Whether to compress the value (overrides default setting)

        Returns:
            True if updated successfully, False if key not found
        """
        if key not in self.memory:
            return False

        # Update memory
        current_time = time.time()

        # Get existing metadata or initialize new one
        if metadata:
            # Make a copy of existing metadata
            existing_metadata = self.memory[key]["metadata"].copy()

            # If we're updating a compressed item, we need to preserve compression info
            if "compression_info" in existing_metadata and "compression_info" not in metadata:
                metadata["compression_info"] = existing_metadata["compression_info"]

            # Update with new metadata
            existing_metadata.update(metadata)
            metadata = existing_metadata
        else:
            metadata = self.memory[key]["metadata"].copy()

        # Apply compression if enabled
        if (compress is True) or (compress is None and self.enable_compression and self.memory_compressor):
            try:
                # Compress the value
                compressed_value, updated_metadata = self.memory_compressor.compress(value, metadata)

                # Update value and metadata
                value = compressed_value
                metadata = updated_metadata

                logger.debug(f"Compressed updated value for key {key}")
            except Exception as e:
                logger.error(f"Error compressing updated value for key {key}: {e}")

        # Update memory item
        self.memory[key]["value"] = value
        self.memory[key]["metadata"] = metadata
        self.memory[key]["last_updated"] = current_time
        self.memory[key]["last_updated_by"] = agent_id

        # Create version if versioning is enabled
        if self.enable_versioning and self.version_manager:
            try:
                from .memory_version import VersionOperation

                # If no parent version ID is provided, use the latest version
                if not parent_version_id:
                    latest_version = self.version_manager.get_latest_version(key)
                    if latest_version:
                        parent_version_id = latest_version.version_id

                # Create a new version
                self.version_manager.create_version(
                    key=key,
                    value=value,  # Store the compressed value in version history
                    metadata=metadata,
                    parent_version_id=parent_version_id,
                    operation=VersionOperation.UPDATE,
                    agent_id=agent_id,
                    comment=comment
                )

                logger.debug(f"Created new version for key {key}")
            except Exception as e:
                logger.error(f"Error creating version for key {key}: {e}")

        # Log access
        self._log_access("update", key, agent_id)
        return True

    def list_keys(self, prefix: str = "", agent_id: str = None) -> List[str]:
        """
        List all keys in shared memory, optionally filtered by prefix.

        Args:
            prefix: Optional prefix to filter keys by
            agent_id: ID of the agent listing keys

        Returns:
            List of keys
        """
        keys = [k for k in self.memory.keys() if k.startswith(prefix)]
        self._log_access("list_keys", prefix, agent_id)
        return keys

    def query(
        self,
        query_string: str,
        agent_id: str = None,
        limit: int = 100,
        include_metadata: bool = False
    ) -> List[Dict[str, Any]]:
        """
        Query the shared memory using the advanced query language.

        The query language supports:
        - Field queries: key:value, content:text, metadata.field:value
        - Operators: : (equals), ~ (contains), >, >=, <, <=
        - Logical operators: AND, OR, NOT
        - Parentheses for grouping

        Examples:
        - key:solution_123 - Find item with exact key
        - key:prefix:solution - Find items with keys starting with "solution"
        - content~"important information" - Find items containing text
        - created_by:agent1 AND created_at>2023-01-01 - Find items by agent1 created after date
        - metadata.priority:high OR metadata.priority:medium - Find items with specific metadata

        Args:
            query_string: Query string in the query language format
            agent_id: ID of the agent performing the query
            limit: Maximum number of results to return
            include_metadata: Whether to include full metadata in results

        Returns:
            List of matching items (with values and optionally metadata)
        """
        self._log_access("query", query_string, agent_id)

        # If query parser is not available, fall back to simple substring search
        if not self._parse_query:
            return self._simple_query(query_string, limit, include_metadata)

        try:
            # Parse the query into an expression
            expression = self._parse_query(query_string)

            # Evaluate the expression against each item
            results = []
            for key, item in self.memory.items():
                if expression.evaluate(item):
                    if include_metadata:
                        results.append(item)
                    else:
                        results.append({
                            "key": key,
                            "value": item["value"]
                        })

                    if len(results) >= limit:
                        break

            return results

        except Exception as e:
            logger.error(f"Error executing query: {e}")
            # Fall back to simple query on error
            return self._simple_query(query_string, limit, include_metadata)

    def _simple_query(
        self,
        query_string: str,
        limit: int = 100,
        include_metadata: bool = False
    ) -> List[Dict[str, Any]]:
        """
        Perform a simple substring search when the query parser is not available.

        Args:
            query_string: Simple text to search for
            limit: Maximum number of results to return
            include_metadata: Whether to include full metadata in results

        Returns:
            List of matching items
        """
        results = []
        query_lower = query_string.lower()

        for key, item in self.memory.items():
            # Check key
            if query_lower in key.lower():
                if include_metadata:
                    results.append(item)
                else:
                    results.append({
                        "key": key,
                        "value": item["value"]
                    })
                continue

            # Check value (convert to string if not already)
            value_str = str(item["value"]) if not isinstance(item["value"], str) else item["value"]
            if query_lower in value_str.lower():
                if include_metadata:
                    results.append(item)
                else:
                    results.append({
                        "key": key,
                        "value": item["value"]
                    })
                continue

            # Check metadata
            for meta_key, meta_value in item["metadata"].items():
                meta_value_str = str(meta_value) if not isinstance(meta_value, str) else meta_value
                if query_lower in meta_key.lower() or query_lower in meta_value_str.lower():
                    if include_metadata:
                        results.append(item)
                    else:
                        results.append({
                            "key": key,
                            "value": item["value"]
                        })
                    break

            if len(results) >= limit:
                break

        return results

    def search_by_similarity(
        self,
        text: str,
        top_k: int = 5,
        threshold: float = 0.0,
        embedder: Optional[Callable[[str], List[float]]] = None,
        agent_id: str = None
    ) -> List[Tuple[str, Any, float]]:
        """
        Search for items by semantic similarity to the given text.

        Args:
            text: Text to search for
            top_k: Maximum number of results to return
            threshold: Minimum similarity score (0-1)
            embedder: Function to convert text to embeddings
            agent_id: ID of the agent performing the search

        Returns:
            List of tuples (key, value, similarity_score)
        """
        self._log_access("search_similarity", text, agent_id)

        if not embedder:
            logger.error("No embedder provided for similarity search")
            return []

        try:
            # Get embedding for the query
            query_embedding = embedder(text)

            # Calculate similarity for each item
            similarities = []
            for key, item in self.memory.items():
                # Convert value to string if needed
                value = item["value"]
                if not isinstance(value, str):
                    value_str = str(value)
                else:
                    value_str = value

                # Get embedding for the value
                try:
                    value_embedding = embedder(value_str)

                    # Calculate cosine similarity
                    similarity = self._cosine_similarity(query_embedding, value_embedding)

                    if similarity >= threshold:
                        similarities.append((key, value, similarity))
                except Exception as e:
                    logger.warning(f"Error calculating embedding for {key}: {e}")
                    continue

            # Sort by similarity (descending) and take top_k
            similarities.sort(key=lambda x: x[2], reverse=True)
            return similarities[:top_k]

        except Exception as e:
            logger.error(f"Error in similarity search: {e}")
            return []

    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """
        Calculate cosine similarity between two vectors.

        Args:
            vec1: First vector
            vec2: Second vector

        Returns:
            Cosine similarity (0-1)
        """
        if len(vec1) != len(vec2):
            raise ValueError("Vectors must have the same dimension")

        dot_product = sum(a * b for a, b in zip(vec1, vec2))
        norm1 = sum(a * a for a in vec1) ** 0.5
        norm2 = sum(b * b for b in vec2) ** 0.5

        if norm1 == 0 or norm2 == 0:
            return 0.0

        return dot_product / (norm1 * norm2)

    def get_version_history(
        self,
        key: str,
        limit: int = 10,
        agent_id: str = None
    ) -> List[Dict[str, Any]]:
        """
        Get the version history for a key.

        Args:
            key: Key to get version history for
            limit: Maximum number of versions to return
            agent_id: ID of the agent requesting the history

        Returns:
            List of version information dictionaries, or empty list if versioning is disabled
        """
        self._log_access("get_version_history", key, agent_id)

        if not self.enable_versioning or not self.version_manager:
            logger.warning("Version history requested but versioning is disabled")
            return []

        try:
            # Get version history
            versions = self.version_manager.get_version_history(key, limit)

            # Convert to dictionaries
            return [version.to_dict() for version in versions]
        except Exception as e:
            logger.error(f"Error getting version history for key {key}: {e}")
            return []

    def revert_to_version(
        self,
        key: str,
        version_id: str,
        agent_id: str = None,
        comment: str = None
    ) -> bool:
        """
        Revert a key to a previous version.

        Args:
            key: Key to revert
            version_id: ID of the version to revert to
            agent_id: ID of the agent performing the revert
            comment: Optional comment about the revert

        Returns:
            True if successful, False otherwise
        """
        self._log_access("revert_to_version", key, agent_id)

        if not self.enable_versioning or not self.version_manager:
            logger.warning("Revert requested but versioning is disabled")
            return False

        try:
            # Revert to the specified version
            new_version = self.version_manager.revert_to_version(
                key=key,
                version_id=version_id,
                agent_id=agent_id,
                comment=comment or f"Reverted to version {version_id}"
            )

            if not new_version:
                logger.warning(f"Failed to revert key {key} to version {version_id}")
                return False

            # Update memory with the reverted version
            if key in self.memory:
                self.memory[key]["value"] = new_version.value
                self.memory[key]["metadata"] = new_version.metadata.copy()
                self.memory[key]["last_updated"] = time.time()
                self.memory[key]["last_updated_by"] = agent_id

                logger.info(f"Reverted key {key} to version {version_id}")
                return True
            else:
                logger.warning(f"Key {key} not found in memory after revert")
                return False
        except Exception as e:
            logger.error(f"Error reverting key {key} to version {version_id}: {e}")
            return False

    def resolve_conflicts(
        self,
        key: str,
        version_ids: List[str],
        strategy: str = None,
        agent_id: str = None,
        comment: str = None
    ) -> bool:
        """
        Resolve conflicts between multiple versions of a key.

        Args:
            key: Key to resolve conflicts for
            version_ids: IDs of the versions to resolve
            strategy: Conflict resolution strategy (overrides default if provided)
            agent_id: ID of the agent performing the resolution
            comment: Optional comment about the resolution

        Returns:
            True if successful, False otherwise
        """
        self._log_access("resolve_conflicts", key, agent_id)

        if not self.enable_versioning or not self.version_manager:
            logger.warning("Conflict resolution requested but versioning is disabled")
            return False

        try:
            # Convert string strategy to enum if provided
            resolution_strategy = None
            if strategy:
                try:
                    from .memory_version import ConflictResolutionStrategy

                    strategy_map = {
                        "last_write_wins": ConflictResolutionStrategy.LAST_WRITE_WINS,
                        "first_write_wins": ConflictResolutionStrategy.FIRST_WRITE_WINS,
                        "merge_fields": ConflictResolutionStrategy.MERGE_FIELDS,
                        "manual_resolution": ConflictResolutionStrategy.MANUAL_RESOLUTION
                    }

                    resolution_strategy = strategy_map.get(strategy)
                except ImportError:
                    logger.warning("Failed to import ConflictResolutionStrategy")

            # Resolve conflicts
            resolved_version = self.version_manager.resolve_conflicts(
                key=key,
                version_ids=version_ids,
                strategy=resolution_strategy,
                agent_id=agent_id,
                comment=comment or f"Resolved conflicts between {len(version_ids)} versions"
            )

            if not resolved_version:
                logger.warning(f"Failed to resolve conflicts for key {key}")
                return False

            # Update memory with the resolved version
            if key in self.memory:
                self.memory[key]["value"] = resolved_version.value
                self.memory[key]["metadata"] = resolved_version.metadata.copy()
                self.memory[key]["last_updated"] = time.time()
                self.memory[key]["last_updated_by"] = agent_id

                logger.info(f"Resolved conflicts for key {key}")
                return True
            else:
                logger.warning(f"Key {key} not found in memory after conflict resolution")
                return False
        except Exception as e:
            logger.error(f"Error resolving conflicts for key {key}: {e}")
            return False

    def get_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about the shared memory.

        Returns:
            Dictionary with statistics
        """
        if not self.memory:
            return {
                "total_items": 0,
                "total_access_logs": len(self.access_log),
                "versioning_enabled": self.enable_versioning,
                "compression_enabled": self.enable_compression
            }

        # Calculate statistics
        total_items = len(self.memory)

        # Get creation time range
        creation_times = [item["created_at"] for item in self.memory.values()]
        oldest_item = min(creation_times)
        newest_item = max(creation_times)

        # Count by agent
        items_by_agent = {}
        for item in self.memory.values():
            agent_id = item["created_by"]
            if agent_id:
                items_by_agent[agent_id] = items_by_agent.get(agent_id, 0) + 1

        # Count access operations
        access_counts = {}
        for log in self.access_log:
            op = log["operation"]
            access_counts[op] = access_counts.get(op, 0) + 1

        # Get version statistics if versioning is enabled
        version_stats = {}
        if self.enable_versioning and self.version_manager:
            try:
                version_stats = self.version_manager.get_statistics()
            except Exception as e:
                logger.error(f"Error getting version statistics: {e}")

        # Get compression statistics if compression is enabled
        compression_stats = {}
        if self.enable_compression and self.memory_compressor:
            try:
                compression_stats = self.memory_compressor.get_statistics()

                # Count compressed items
                compressed_items = 0
                for item in self.memory.values():
                    if "compression_info" in item["metadata"] and item["metadata"]["compression_info"].get("compressed", False):
                        compressed_items += 1

                compression_stats["compressed_items"] = compressed_items
                compression_stats["compression_ratio"] = compressed_items / total_items if total_items > 0 else 0
            except Exception as e:
                logger.error(f"Error getting compression statistics: {e}")

        stats = {
            "total_items": total_items,
            "oldest_item_time": oldest_item,
            "newest_item_time": newest_item,
            "items_by_agent": items_by_agent,
            "total_access_logs": len(self.access_log),
            "access_counts": access_counts,
            "versioning_enabled": self.enable_versioning,
            "compression_enabled": self.enable_compression
        }

        # Add version statistics if available
        if version_stats:
            stats["version_statistics"] = version_stats

        # Add compression statistics if available
        if compression_stats:
            stats["compression_statistics"] = compression_stats

        return stats

    def _log_access(self, operation: str, key: str, agent_id: str = None) -> None:
        """
        Log memory access for analysis.

        Args:
            operation: Type of operation performed
            key: Key that was accessed
            agent_id: ID of the agent that performed the access
        """
        self.access_log.append({
            "operation": operation,
            "key": key,
            "agent_id": agent_id,
            "timestamp": time.time()
        })


# TaskDecomposer is now imported from task_decomposer.py


class Agent:
    """
    Agent in a multi-agent system.

    This class represents an agent with a specific role that can
    process messages, solve subtasks, and interact with other agents.
    """

    def __init__(
        self,
        id: str,
        role: AgentRole,
        language_model,
        tools: List[BaseTool] = None,
        system_prompt: Optional[str] = None
    ):
        """
        Initialize an agent.

        Args:
            id: Unique identifier for the agent
            role: Role of the agent
            language_model: Language model to use for reasoning
            tools: List of tools available to the agent
            system_prompt: Optional system prompt (defaults to role's system prompt)
        """
        self.id = id
        self.role = role
        self.language_model = language_model
        self.tools = tools or []
        self.system_prompt = system_prompt or role.system_prompt
        self.memory = {}  # Agent's local memory

    def process_message(
        self,
        message: AgentMessage,
        shared_memory: Optional[SharedMemory] = None
    ) -> AgentMessage:
        """
        Process an incoming message and generate a response.

        Args:
            message: The incoming message to process
            shared_memory: Optional shared memory to access

        Returns:
            Agent's response message
        """
        # This is a placeholder implementation
        # A real implementation would use the language model to process the message

        logger.info(f"Agent {self.id} processing message from {message.sender_id}")

        # Simple echo response for now
        response_content = {
            "original_message": message.content,
            "response": f"Echo from {self.id} with role {self.role.name}"
        }

        return AgentMessage(
            sender_id=self.id,
            receiver_id=message.sender_id,
            message_type="response",
            content=response_content,
            in_reply_to=message.message_id
        )

    def solve_subtask(
        self,
        subtask: Dict[str, Any],
        shared_memory: Optional[SharedMemory] = None
    ) -> Dict[str, Any]:
        """
        Solve an assigned subtask.

        Args:
            subtask: The subtask to solve
            shared_memory: Shared memory to access and update

        Returns:
            Solution to the subtask
        """
        # This is a placeholder implementation
        # A real implementation would use the language model to solve the subtask

        logger.info(f"Agent {self.id} solving subtask: {subtask.get('description', 'Unknown task')}")

        # Simple example solution
        solution = {
            "subtask_id": subtask.get("id", "unknown"),
            "solution": f"Solution from {self.id} with role {self.role.name}",
            "confidence": 0.8,
            "reasoning": f"Reasoning process from {self.role.name}"
        }

        # Store solution in shared memory if available
        if shared_memory:
            shared_memory.store(
                key=f"solution_{subtask.get('id', 'unknown')}",
                value=solution,
                metadata={"agent_id": self.id, "role": self.role.name},
                agent_id=self.id
            )

        return solution


class ReasoningAgent(Agent):
    """
    Agent that uses a reasoning method to solve tasks.

    This class extends the base Agent class to incorporate
    reasoning methods like ToT, CoT, and ReAct.
    """

    def __init__(
        self,
        id: str,
        role: AgentRole,
        language_model,
        reasoning_method: str = "tot",  # "tot", "cot", "react"
        reasoner: Optional[BaseReasoner] = None,
        **kwargs
    ):
        """
        Initialize a reasoning agent.

        Args:
            id: Unique identifier for the agent
            role: Role of the agent
            language_model: Language model to use for reasoning
            reasoning_method: Type of reasoning to use
            reasoner: Optional pre-configured reasoner
            **kwargs: Additional arguments for the base Agent class
        """
        super().__init__(id, role, language_model, **kwargs)

        # Initialize the appropriate reasoner
        if reasoner:
            self.reasoner = reasoner
        else:
            # Lazily import reasoners to avoid circular imports
            try:
                if reasoning_method == "tot":
                    from ..reasoning.tot import TreeOfThought
                    self.reasoner = TreeOfThought(
                        provider="openai",
                        model="gpt-4",
                        language="en"
                    )
                elif reasoning_method == "cot":
                    from ..reasoning.cot import ChainOfThought
                    self.reasoner = ChainOfThought(model=language_model)
                elif reasoning_method == "react":
                    from ..reasoning.react import ReAct
                    self.reasoner = ReAct(
                        provider="openai",
                        model="gpt-4",
                        tools=[]
                    )
                else:
                    raise ValueError(f"Unknown reasoning method: {reasoning_method}")
            except ImportError as e:
                logger.error(f"Failed to import reasoning module: {e}")
                # Fallback to a simple implementation
                from ..reasoning.cot import ChainOfThought
                self.reasoner = ChainOfThought(model=language_model)

    def solve_subtask(
        self,
        subtask: Dict[str, Any],
        shared_memory: Optional[SharedMemory] = None
    ) -> Dict[str, Any]:
        """
        Solve a subtask using the agent's reasoning method.

        Args:
            subtask: The subtask to solve
            shared_memory: Shared memory to access and update

        Returns:
            Solution to the subtask
        """
        # Adapt the subtask description for the reasoner
        query = self._format_reasoning_query(subtask, shared_memory)

        # Use the reasoner to solve the task
        reasoning_result = self.reasoner.generate_reasoning(query)

        # Extract and format the solution
        solution = self._extract_solution(reasoning_result, subtask)

        # Update shared memory with relevant information
        if shared_memory:
            self._update_shared_memory(shared_memory, subtask, solution)

        return solution

    def _format_reasoning_query(
        self,
        subtask: Dict[str, Any],
        shared_memory: Optional[SharedMemory] = None
    ) -> str:
        """
        Format a query for the reasoning method based on the subtask.

        Args:
            subtask: The subtask to solve
            shared_memory: Shared memory to access

        Returns:
            Formatted query string
        """
        # This is a placeholder implementation
        # A real implementation would create a more sophisticated prompt

        role_context = f"You are a {self.role.name}. {self.role.description}"
        task_description = subtask.get("description", "Unknown task")

        # Add context from shared memory if available
        context = ""
        if shared_memory:
            # Example: retrieve relevant information from dependencies
            for dep_id in subtask.get("dependencies", []):
                dep_solution = shared_memory.retrieve(f"solution_{dep_id}")
                if dep_solution:
                    context += f"\nInformation from previous subtask {dep_id}: {dep_solution.get('solution', '')}\n"

        query = f"{role_context}\n\nYour task is to {task_description}.\n\n{context}"

        return query

    def _extract_solution(
        self,
        reasoning_result: Dict[str, Any],
        subtask: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Extract a solution from the reasoning result.

        Args:
            reasoning_result: Result from the reasoner
            subtask: The original subtask

        Returns:
            Formatted solution
        """
        # This is a placeholder implementation
        # A real implementation would parse the reasoning result more carefully

        solution = {
            "subtask_id": subtask.get("id", "unknown"),
            "solution": reasoning_result.get("answer", "No answer provided"),
            "confidence": reasoning_result.get("confidence", 0.5),
            "reasoning": reasoning_result.get("reasoning", "")
        }

        return solution

    def _update_shared_memory(
        self,
        shared_memory: SharedMemory,
        subtask: Dict[str, Any],
        solution: Dict[str, Any]
    ) -> None:
        """
        Update shared memory with the solution and other relevant information.

        Args:
            shared_memory: Shared memory to update
            subtask: The solved subtask
            solution: The solution to the subtask
        """
        # Store the solution
        shared_memory.store(
            key=f"solution_{subtask.get('id', 'unknown')}",
            value=solution,
            metadata={
                "agent_id": self.id,
                "role": self.role.name,
                "timestamp": time.time()
            },
            agent_id=self.id
        )

        # Store any extracted entities or concepts that might be useful for other agents
        # This is just a placeholder - a real implementation would extract entities
        if "entities" in solution:
            for entity in solution["entities"]:
                shared_memory.store(
                    key=f"entity_{entity['name']}",
                    value=entity,
                    metadata={"source_task": subtask.get("id", "unknown")},
                    agent_id=self.id
                )


class MultiAgentSystem:
    """
    Multi-agent system for collaborative problem solving.

    This class orchestrates the collaboration between multiple agents
    to solve complex tasks through decomposition and coordination.
    """

    def __init__(
        self,
        agents: List[Agent],
        communication_protocol: Optional[AgentCommunicationProtocol] = None,
        consensus_mechanism: Optional[ConsensusMechanism] = None,
        task_decomposer: Optional[TaskDecomposer] = None,
        shared_memory: Optional[SharedMemory] = None,
        language_model = None
    ):
        """
        Initialize the multi-agent system.

        Args:
            agents: List of agents in the system
            communication_protocol: Protocol for agent communication
            consensus_mechanism: Mechanism for reaching consensus
            task_decomposer: Engine for task decomposition
            shared_memory: Shared memory system
            language_model: Language model for system-level tasks
        """
        self.agents = {agent.id: agent for agent in agents}
        self.communication_protocol = communication_protocol or AgentCommunicationProtocol()
        self.consensus_mechanism = consensus_mechanism or ConsensusMechanism()
        self.task_decomposer = task_decomposer or TaskDecomposer(language_model)
        self.shared_memory = shared_memory or SharedMemory()
        self.language_model = language_model
        self.conversation_history = []

    def solve_task(
        self,
        task_description: str,
        max_iterations: int = 10,
        timeout: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        Solve a complex task using multi-agent collaboration.

        Args:
            task_description: Description of the task to solve
            max_iterations: Maximum number of collaboration iterations
            timeout: Maximum time in seconds to spend on the task

        Returns:
            Solution to the task
        """
        start_time = time.time()

        # Step 1: Decompose the task
        logger.info(f"Decomposing task: {task_description}")
        subtasks = self.task_decomposer.decompose_task(
            task_description,
            available_roles=[agent.role.name for agent in self.agents.values()]
        )

        # Step 2: Assign subtasks to agents
        logger.info("Assigning subtasks to agents")
        assignments = self._assign_subtasks(subtasks)

        # Step 3: Execute subtasks in the correct order
        logger.info("Executing subtasks")
        results = self._execute_subtasks(assignments, max_iterations, timeout)

        # Step 4: Synthesize the final solution
        logger.info("Synthesizing final solution")
        final_solution = self._synthesize_solution(results, task_description)

        execution_time = time.time() - start_time
        logger.info(f"Task completed in {execution_time:.2f} seconds")

        return {
            "task": task_description,
            "solution": final_solution.get("solution", "No solution found"),
            "confidence": final_solution.get("confidence", 0.0),
            "reasoning": final_solution.get("reasoning", ""),
            "execution_time": execution_time,
            "subtask_results": results
        }

    def _assign_subtasks(self, subtasks: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """
        Assign subtasks to appropriate agents based on roles.

        Args:
            subtasks: Dictionary containing subtasks and their metadata

        Returns:
            Dictionary mapping subtask IDs to assignments
        """
        assignments = {}

        for subtask in subtasks.get("subtasks", []):
            subtask_id = subtask.get("id", "unknown")
            suitable_roles = subtask.get("suitable_roles", [])

            # Find agents with suitable roles
            suitable_agents = []
            for agent_id, agent in self.agents.items():
                if agent.role.name in suitable_roles:
                    suitable_agents.append(agent_id)

            if suitable_agents:
                # For now, just assign to the first suitable agent
                # A more sophisticated approach would balance workload
                assigned_agent = suitable_agents[0]
                assignments[subtask_id] = {
                    "subtask": subtask,
                    "agent_id": assigned_agent,
                    "status": "assigned"
                }
                logger.info(f"Assigned subtask {subtask_id} to agent {assigned_agent}")
            else:
                logger.warning(f"No suitable agent found for subtask {subtask_id}")
                assignments[subtask_id] = {
                    "subtask": subtask,
                    "agent_id": None,
                    "status": "unassigned"
                }

        return assignments

    def _execute_subtasks(
        self,
        assignments: Dict[str, Dict[str, Any]],
        max_iterations: int = 10,
        timeout: Optional[float] = None
    ) -> Dict[str, Dict[str, Any]]:
        """
        Execute subtasks in the correct order based on dependencies.

        Args:
            assignments: Dictionary mapping subtask IDs to assignments
            max_iterations: Maximum number of iterations
            timeout: Maximum time in seconds

        Returns:
            Dictionary mapping subtask IDs to results
        """
        results = {}
        start_time = time.time()

        # Get dependencies from the assignments
        dependencies = {}
        for subtask_id, assignment in assignments.items():
            subtask = assignment.get("subtask", {})
            dependencies[subtask_id] = subtask.get("dependencies", [])

        # Track completed subtasks
        completed = set()
        iteration = 0

        while len(completed) < len(assignments) and iteration < max_iterations:
            iteration += 1
            logger.info(f"Execution iteration {iteration}")

            # Check timeout
            if timeout and (time.time() - start_time) > timeout:
                logger.warning(f"Execution timed out after {timeout} seconds")
                break

            # Find subtasks that are ready to execute (all dependencies completed)
            ready_tasks = []
            for subtask_id, deps in dependencies.items():
                if subtask_id not in completed and all(dep in completed for dep in deps):
                    ready_tasks.append(subtask_id)

            if not ready_tasks:
                if len(completed) < len(assignments):
                    logger.warning("No subtasks ready for execution but not all are completed")
                break

            # Execute ready tasks
            for subtask_id in ready_tasks:
                assignment = assignments[subtask_id]
                agent_id = assignment.get("agent_id")

                if not agent_id:
                    logger.warning(f"Subtask {subtask_id} has no assigned agent, skipping")
                    continue

                agent = self.agents.get(agent_id)
                if not agent:
                    logger.warning(f"Agent {agent_id} not found, skipping subtask {subtask_id}")
                    continue

                # Execute the subtask
                logger.info(f"Agent {agent_id} executing subtask {subtask_id}")
                subtask_result = agent.solve_subtask(
                    assignment.get("subtask", {}),
                    shared_memory=self.shared_memory
                )

                # Store the result
                results[subtask_id] = {
                    "subtask_id": subtask_id,
                    "agent_id": agent_id,
                    "result": subtask_result,
                    "execution_time": time.time() - start_time
                }

                # Mark as completed
                completed.add(subtask_id)
                logger.info(f"Completed subtask {subtask_id}")

        # Check for uncompleted tasks
        if len(completed) < len(assignments):
            logger.warning(f"{len(assignments) - len(completed)} subtasks were not completed")

        return results

    def _synthesize_solution(
        self,
        results: Dict[str, Dict[str, Any]],
        task_description: str
    ) -> Dict[str, Any]:
        """
        Synthesize a final solution from the results of all subtasks.

        Args:
            results: Dictionary mapping subtask IDs to results
            task_description: Original task description

        Returns:
            Final solution
        """
        # This is a placeholder implementation
        # A real implementation would use the language model to synthesize a solution

        logger.info("Synthesizing final solution")

        # Extract solutions from results
        solutions = []
        for subtask_id, result_data in results.items():
            if "result" in result_data and "solution" in result_data["result"]:
                solutions.append({
                    "subtask_id": subtask_id,
                    "solution": result_data["result"]["solution"],
                    "confidence": result_data["result"].get("confidence", 0.5)
                })

        # Simple aggregation for now
        if solutions:
            # Sort by confidence
            solutions.sort(key=lambda x: x.get("confidence", 0), reverse=True)

            # Combine the solutions
            combined_solution = "\n\n".join([
                f"From subtask {s['subtask_id']} (confidence: {s.get('confidence', 0):.2f}):\n{s['solution']}"
                for s in solutions
            ])

            # Calculate average confidence
            avg_confidence = sum(s.get("confidence", 0) for s in solutions) / len(solutions)

            final_solution = {
                "solution": combined_solution,
                "confidence": avg_confidence,
                "reasoning": f"Combined {len(solutions)} subtask solutions"
            }
        else:
            final_solution = {
                "solution": "No solutions were generated for the subtasks.",
                "confidence": 0.0,
                "reasoning": "No successful subtask executions"
            }

        return final_solution