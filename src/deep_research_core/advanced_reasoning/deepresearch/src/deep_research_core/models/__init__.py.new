"""Models module for Deep Research Core."""

# Import base classes
from .api.base import BaseAPIProvider
from .local.base import BaseLocalModel

# Import API providers
try:
    from .api.openai import OpenAIProvider, openai_provider
    from .api.anthropic import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, anthropic_provider
    from .api.openrouter import OpenRouterProvider, openrouter_provider
    from .api.google import GoogleProvider, google_provider
    from .api.deepseek import DeepSeekProvider, deepseek_provider
except ImportError:
    # Some providers might be optional
    pass

# Import local model utilities
try:
    from .local.model_loader import (
        load_model,
        load_tokenizer,
        get_available_models,
        local_model_loader
    )
    from .local.quantization import (
        get_quantization_config,
        quantize_model
    )
except ImportError:
    # Local models might be optional
    pass

__all__ = [
    # Base classes
    'BaseAPIProvider',
    'BaseLocalModel',
    
    # API providers
    'OpenAIProvider',
    'AnthropicProvider',
    'OpenRouterProvider',
    'GoogleProvider',
    'DeepSeekProvider',
    
    # Provider instances
    'openai_provider',
    'anthropic_provider',
    'openrouter_provider',
    'google_provider',
    'deepseek_provider',

    # Local model utilities
    'load_model',
    'load_tokenizer',
    'get_available_models',
    'local_model_loader',
    'get_quantization_config',
    'quantize_model'
]
