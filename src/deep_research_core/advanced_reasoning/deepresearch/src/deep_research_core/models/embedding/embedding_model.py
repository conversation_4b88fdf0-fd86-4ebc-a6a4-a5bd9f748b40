"""
Embedding model utilities.

This module provides utilities for working with embedding models.
"""

import os
import torch
from typing import Optional, Dict, Any, List, Union

from deep_research_core.utils.structured_logging import get_logger

logger = get_logger(__name__)

class EmbeddingModel:
    """
    Wrapper class for embedding models.
    
    This class provides a unified interface for different embedding models.
    """
    
    def __init__(
        self,
        model_name: str,
        device: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize the embedding model.
        
        Args:
            model_name: Name of the embedding model
            device: Device to use for computations ('cpu', 'cuda', etc.)
            **kwargs: Additional parameters to pass to the model
        """
        self.model_name = model_name
        self.device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
        self.kwargs = kwargs
        self._model = None
        
    @property
    def model(self):
        """Lazy-load the embedding model."""
        if self._model is None:
            try:
                from sentence_transformers import SentenceTransformer
                self._model = SentenceTransformer(
                    self.model_name,
                    device=self.device
                )
            except ImportError:
                logger.error("SentenceTransformer not installed. Please install it with 'pip install sentence-transformers'.")
                raise
            except Exception as e:
                logger.error(f"Error loading embedding model {self.model_name}: {e}")
                raise
        return self._model
    
    def encode(
        self,
        texts: Union[str, List[str]],
        batch_size: int = 32,
        show_progress_bar: bool = False,
        convert_to_tensor: bool = False,
        normalize_embeddings: bool = True
    ) -> Union[List[List[float]], torch.Tensor]:
        """
        Encode texts to embeddings.
        
        Args:
            texts: Text or list of texts to encode
            batch_size: Batch size for encoding
            show_progress_bar: Whether to show progress bar
            convert_to_tensor: Whether to convert output to tensor
            normalize_embeddings: Whether to normalize embeddings
            
        Returns:
            Embeddings as list or tensor
        """
        return self.model.encode(
            texts,
            batch_size=batch_size,
            show_progress_bar=show_progress_bar,
            convert_to_tensor=convert_to_tensor,
            normalize_embeddings=normalize_embeddings
        )
    
    def get_embedding_dimension(self) -> int:
        """Get the dimension of the embeddings."""
        return self.model.get_sentence_embedding_dimension()
    
    def get_max_seq_length(self) -> int:
        """Get the maximum sequence length."""
        return self.model.get_max_seq_length()
    
    def __repr__(self) -> str:
        """Get string representation."""
        return f"EmbeddingModel(model_name={self.model_name}, device={self.device})"


def get_embedding_model(
    model_name: str = "all-mpnet-base-v2",
    device: Optional[str] = None,
    **kwargs
) -> EmbeddingModel:
    """
    Get an embedding model.
    
    Args:
        model_name: Name of the embedding model
        device: Device to use for computations ('cpu', 'cuda', etc.)
        **kwargs: Additional parameters to pass to the model
        
    Returns:
        Embedding model instance
    """
    return EmbeddingModel(
        model_name=model_name,
        device=device,
        **kwargs
    )
