"""
Embedding models for Deep Research Core.

This module provides embedding models for text, which are used for
semantic similarity, retrieval, and other NLP tasks.
"""

import os
import json
import numpy as np
from typing import List, Dict, Any, Optional, Union
from abc import ABC, abstractmethod

from deep_research_core.utils.structured_logging import get_logger

# Get logger
logger = get_logger(__name__)

# Try to import optional dependencies
try:
    import torch
    from transformers import AutoTokenizer, AutoModel
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    logger.warning("Transformers package not available. Some embedding models will not work.")

try:
    import sentence_transformers
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    logger.warning("Sentence-transformers package not available. Some embedding models will not work.")


class BaseEmbeddingModel(ABC):
    """Base class for embedding models."""
    
    @abstractmethod
    def get_embedding(self, text: str) -> List[float]:
        """
        Get embedding for text.
        
        Args:
            text: Text to embed
            
        Returns:
            Embedding vector
        """
        pass
    
    @abstractmethod
    def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Get embeddings for multiple texts.
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embedding vectors
        """
        pass
    
    @property
    @abstractmethod
    def embedding_dim(self) -> int:
        """Get embedding dimension."""
        pass


class TransformersEmbeddingModel(BaseEmbeddingModel):
    """Embedding model using Hugging Face Transformers."""
    
    def __init__(self, model_name: str):
        """
        Initialize the model.
        
        Args:
            model_name: Name of the model to use
        """
        if not TRANSFORMERS_AVAILABLE:
            raise ImportError("Transformers package not available. Please install it with 'pip install transformers'.")
        
        self.model_name = model_name
        
        # Load tokenizer and model
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModel.from_pretrained(model_name)
        
        # Move model to GPU if available
        if torch.cuda.is_available():
            self.model = self.model.to("cuda")
        
        # Get embedding dimension
        self._embedding_dim = self.model.config.hidden_size
    
    def _mean_pooling(self, model_output, attention_mask):
        """
        Mean pooling of token embeddings.
        
        Args:
            model_output: Output from the model
            attention_mask: Attention mask
            
        Returns:
            Pooled embeddings
        """
        # First element of model_output contains all token embeddings
        token_embeddings = model_output[0]
        
        # Expand attention mask to same dimensions as token embeddings
        input_mask_expanded = attention_mask.unsqueeze(-1).expand(token_embeddings.size()).float()
        
        # Sum token embeddings and divide by the expanded mask sum
        sum_embeddings = torch.sum(token_embeddings * input_mask_expanded, 1)
        sum_mask = torch.clamp(input_mask_expanded.sum(1), min=1e-9)
        
        return sum_embeddings / sum_mask
    
    def get_embedding(self, text: str) -> List[float]:
        """
        Get embedding for text.
        
        Args:
            text: Text to embed
            
        Returns:
            Embedding vector
        """
        # Tokenize text
        encoded_input = self.tokenizer(
            text,
            padding=True,
            truncation=True,
            max_length=512,
            return_tensors='pt'
        )
        
        # Move to GPU if available
        if torch.cuda.is_available():
            encoded_input = {k: v.to("cuda") for k, v in encoded_input.items()}
        
        # Get model output
        with torch.no_grad():
            model_output = self.model(**encoded_input)
        
        # Perform mean pooling
        sentence_embedding = self._mean_pooling(
            model_output,
            encoded_input['attention_mask']
        )
        
        # Normalize embedding
        sentence_embedding = torch.nn.functional.normalize(sentence_embedding, p=2, dim=1)
        
        # Convert to list
        return sentence_embedding[0].cpu().numpy().tolist()
    
    def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Get embeddings for multiple texts.
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embedding vectors
        """
        # Tokenize texts
        encoded_input = self.tokenizer(
            texts,
            padding=True,
            truncation=True,
            max_length=512,
            return_tensors='pt'
        )
        
        # Move to GPU if available
        if torch.cuda.is_available():
            encoded_input = {k: v.to("cuda") for k, v in encoded_input.items()}
        
        # Get model output
        with torch.no_grad():
            model_output = self.model(**encoded_input)
        
        # Perform mean pooling
        sentence_embeddings = self._mean_pooling(
            model_output,
            encoded_input['attention_mask']
        )
        
        # Normalize embeddings
        sentence_embeddings = torch.nn.functional.normalize(sentence_embeddings, p=2, dim=1)
        
        # Convert to list
        return sentence_embeddings.cpu().numpy().tolist()
    
    @property
    def embedding_dim(self) -> int:
        """Get embedding dimension."""
        return self._embedding_dim


class SentenceTransformersEmbeddingModel(BaseEmbeddingModel):
    """Embedding model using sentence-transformers."""
    
    def __init__(self, model_name: str):
        """
        Initialize the model.
        
        Args:
            model_name: Name of the model to use
        """
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            raise ImportError("Sentence-transformers package not available. Please install it with 'pip install sentence-transformers'.")
        
        self.model_name = model_name
        
        # Load model
        self.model = sentence_transformers.SentenceTransformer(model_name)
        
        # Move model to GPU if available
        if torch.cuda.is_available():
            self.model = self.model.to(torch.device("cuda"))
        
        # Get embedding dimension
        self._embedding_dim = self.model.get_sentence_embedding_dimension()
    
    def get_embedding(self, text: str) -> List[float]:
        """
        Get embedding for text.
        
        Args:
            text: Text to embed
            
        Returns:
            Embedding vector
        """
        # Get embedding
        embedding = self.model.encode(text, convert_to_numpy=True)
        
        # Convert to list
        return embedding.tolist()
    
    def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Get embeddings for multiple texts.
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embedding vectors
        """
        # Get embeddings
        embeddings = self.model.encode(texts, convert_to_numpy=True)
        
        # Convert to list
        return embeddings.tolist()
    
    @property
    def embedding_dim(self) -> int:
        """Get embedding dimension."""
        return self._embedding_dim


class RandomEmbeddingModel(BaseEmbeddingModel):
    """Random embedding model for testing."""
    
    def __init__(self, embedding_dim: int = 768):
        """
        Initialize the model.
        
        Args:
            embedding_dim: Dimension of embeddings
        """
        self._embedding_dim = embedding_dim
    
    def get_embedding(self, text: str) -> List[float]:
        """
        Get embedding for text.
        
        Args:
            text: Text to embed
            
        Returns:
            Embedding vector
        """
        # Use hash of text as seed for reproducibility
        np.random.seed(hash(text) % 10000)
        
        # Generate random embedding
        embedding = np.random.rand(self._embedding_dim)
        
        # Normalize
        embedding = embedding / np.linalg.norm(embedding)
        
        return embedding.tolist()
    
    def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Get embeddings for multiple texts.
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embedding vectors
        """
        return [self.get_embedding(text) for text in texts]
    
    @property
    def embedding_dim(self) -> int:
        """Get embedding dimension."""
        return self._embedding_dim


# Dictionary of available models
EMBEDDING_MODELS = {
    # Multilingual models
    "multilingual-e5-small": {
        "class": SentenceTransformersEmbeddingModel if SENTENCE_TRANSFORMERS_AVAILABLE else RandomEmbeddingModel,
        "model_name": "intfloat/multilingual-e5-small",
        "embedding_dim": 384
    },
    "multilingual-e5-base": {
        "class": SentenceTransformersEmbeddingModel if SENTENCE_TRANSFORMERS_AVAILABLE else RandomEmbeddingModel,
        "model_name": "intfloat/multilingual-e5-base",
        "embedding_dim": 768
    },
    "multilingual-e5-large": {
        "class": SentenceTransformersEmbeddingModel if SENTENCE_TRANSFORMERS_AVAILABLE else RandomEmbeddingModel,
        "model_name": "intfloat/multilingual-e5-large",
        "embedding_dim": 1024
    },
    
    # Vietnamese models
    "phobert-base": {
        "class": TransformersEmbeddingModel if TRANSFORMERS_AVAILABLE else RandomEmbeddingModel,
        "model_name": "vinai/phobert-base",
        "embedding_dim": 768
    },
    "viebert-base": {
        "class": TransformersEmbeddingModel if TRANSFORMERS_AVAILABLE else RandomEmbeddingModel,
        "model_name": "FPTAI/viebert-base",
        "embedding_dim": 768
    },
    
    # English models
    "all-MiniLM-L6-v2": {
        "class": SentenceTransformersEmbeddingModel if SENTENCE_TRANSFORMERS_AVAILABLE else RandomEmbeddingModel,
        "model_name": "sentence-transformers/all-MiniLM-L6-v2",
        "embedding_dim": 384
    },
    "all-mpnet-base-v2": {
        "class": SentenceTransformersEmbeddingModel if SENTENCE_TRANSFORMERS_AVAILABLE else RandomEmbeddingModel,
        "model_name": "sentence-transformers/all-mpnet-base-v2",
        "embedding_dim": 768
    }
}


def get_embedding_model(model_name: str) -> BaseEmbeddingModel:
    """
    Get embedding model by name.
    
    Args:
        model_name: Name of the model to use
        
    Returns:
        Embedding model
    """
    if model_name in EMBEDDING_MODELS:
        model_info = EMBEDDING_MODELS[model_name]
        model_class = model_info["class"]
        
        if model_class == RandomEmbeddingModel:
            return RandomEmbeddingModel(model_info["embedding_dim"])
        else:
            return model_class(model_info["model_name"])
    else:
        logger.warning(f"Unknown embedding model: {model_name}. Using random embeddings.")
        return RandomEmbeddingModel()


def list_available_models() -> List[Dict[str, Any]]:
    """
    List available embedding models.
    
    Returns:
        List of model information
    """
    return [
        {
            "name": name,
            "embedding_dim": info["embedding_dim"],
            "is_available": (
                (info["class"] == SentenceTransformersEmbeddingModel and SENTENCE_TRANSFORMERS_AVAILABLE) or
                (info["class"] == TransformersEmbeddingModel and TRANSFORMERS_AVAILABLE) or
                info["class"] == RandomEmbeddingModel
            )
        }
        for name, info in EMBEDDING_MODELS.items()
    ]
