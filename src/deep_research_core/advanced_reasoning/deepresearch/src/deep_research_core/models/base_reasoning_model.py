"""
Base Reasoning Model Interface.

This module defines the base interface for all reasoning models in the system,
providing standardized methods and utilities for model interaction.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union, Callable, Tuple
import time
import json
import os
from pathlib import Path

from ..utils.structured_logging import get_logger
from ..utils.performance_metrics import measure_latency, measure_memory

# Create a logger
logger = get_logger(__name__)


class BaseReasoningModel(ABC):
    """
    Abstract base class for all reasoning models.
    
    This class defines the standard interface that all reasoning models
    must implement, ensuring consistent behavior across different model
    implementations.
    """
    
    def __init__(
        self,
        model_name: str,
        provider: str,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        verbose: bool = False,
        **kwargs
    ):
        """
        Initialize the base reasoning model.
        
        Args:
            model_name: Name of the model to use
            provider: Provider of the model (openai, anthropic, deepseek, etc.)
            temperature: Temperature for sampling (higher = more random)
            max_tokens: Maximum number of tokens to generate
            verbose: Whether to print detailed information
            **kwargs: Additional model-specific parameters
        """
        self.model_name = model_name
        self.provider = provider
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.verbose = verbose
        self.additional_params = kwargs
        
        # Cache for responses
        self.cache = {}
        self.enable_caching = kwargs.get("enable_caching", False)
        self.cache_dir = kwargs.get("cache_dir", None)
        
        # Performance tracking
        self.track_performance = kwargs.get("track_performance", False)
        self.performance_metrics = []
        
        if self.verbose:
            logger.info(
                f"Initialized {self.__class__.__name__} with "
                f"model={model_name}, provider={provider}"
            )
    
    @abstractmethod
    def generate(
        self,
        prompt: str,
        system_message: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate a response for the given prompt.
        
        Args:
            prompt: The prompt to generate a response for
            system_message: Optional system message to guide generation
            temperature: Temperature for sampling (overrides instance value)
            max_tokens: Maximum tokens to generate (overrides instance value)
            stop_sequences: Optional list of sequences that stop generation
            **kwargs: Additional generation parameters
            
        Returns:
            Dict containing response text and metadata
        """
        pass
    
    @abstractmethod
    def batch_generate(
        self,
        prompts: List[str],
        system_message: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Generate responses for multiple prompts in batch.
        
        Args:
            prompts: List of prompts to generate responses for
            system_message: Optional system message to guide generation
            temperature: Temperature for sampling (overrides instance value)
            max_tokens: Maximum tokens to generate (overrides instance value)
            stop_sequences: Optional list of sequences that stop generation
            **kwargs: Additional generation parameters
            
        Returns:
            List of dicts containing response texts and metadata
        """
        pass
    
    @abstractmethod
    def get_token_count(self, text: str) -> int:
        """
        Count the number of tokens in the given text.
        
        Args:
            text: The text to count tokens for
            
        Returns:
            Number of tokens in the text
        """
        pass
    
    def prepare_prompt(
        self,
        prompt: str,
        system_message: Optional[str] = None
    ) -> Union[str, Dict[str, Any]]:
        """
        Prepare the prompt for the specific model format.
        
        Args:
            prompt: User prompt
            system_message: Optional system message
            
        Returns:
            Formatted prompt ready for the model
        """
        # Default implementation just returns the prompt
        # Subclasses should override this if they need specific formatting
        if system_message:
            return {"system": system_message, "user": prompt}
        return prompt
    
    def validate_response(self, response: Dict[str, Any]) -> bool:
        """
        Validate that a response is well-formed.
        
        Args:
            response: The response to validate
            
        Returns:
            True if the response is valid, False otherwise
        """
        # Basic validation - check that text is present
        return "text" in response and isinstance(response["text"], str)
    
    def log_request(self, prompt: str, params: Dict[str, Any]) -> None:
        """
        Log information about a request.
        
        Args:
            prompt: The prompt being sent
            params: The parameters being used
        """
        if self.verbose:
            # Truncate prompt for logging if too long
            truncated_prompt = (
                prompt[:100] + "..." if len(prompt) > 100 else prompt
            )
            logger.info(
                f"Sending request to {self.provider}/{self.model_name}: "
                f"'{truncated_prompt}', params={params}"
            )
    
    def log_response(self, response: Dict[str, Any]) -> None:
        """
        Log information about a response.
        
        Args:
            response: The response received
        """
        if self.verbose:
            # Extract and possibly truncate the response text for logging
            text = response.get("text", "")
            truncated_text = text[:100] + "..." if len(text) > 100 else text
            
            logger.info(
                f"Received response from {self.provider}/{self.model_name}: "
                f"'{truncated_text}'"
            )
            
            # Log any errors
            if "error" in response:
                logger.error(
                    f"Error from {self.provider}/{self.model_name}: "
                    f"{response['error']}"
                )
                
    # Common utility methods for all reasoning models
    
    def generate_with_retry(
        self,
        prompt: str,
        system_message: Optional[str] = None,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        backoff_factor: float = 2.0,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate a response with automatic retry on failure.
        
        Args:
            prompt: The prompt to generate a response for
            system_message: Optional system message
            max_retries: Maximum number of retries
            retry_delay: Initial delay between retries (in seconds)
            backoff_factor: Factor to increase delay by after each retry
            **kwargs: Additional parameters for generate()
            
        Returns:
            Dict containing response text and metadata
        """
        attempt = 0
        last_error = None
        current_delay = retry_delay
        
        while attempt < max_retries + 1:  # +1 for initial attempt
            try:
                return self.generate(
                    prompt=prompt, 
                    system_message=system_message,
                    **kwargs
                )
            except Exception as e:
                last_error = e
                attempt += 1
                
                if attempt > max_retries:
                    break
                    
                logger.warning(
                    f"Attempt {attempt}/{max_retries} failed: {str(e)}. "
                    f"Retrying in {current_delay:.1f}s..."
                )
                time.sleep(current_delay)
                current_delay *= backoff_factor
        
        # If we get here, all retries failed
        error_msg = f"Failed after {max_retries} retries: {str(last_error)}"
        logger.error(error_msg)
        return {
            "text": "",
            "error": error_msg,
            "model": self.model_name,
            "provider": self.provider
        }
    
    def get_cached_response(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """
        Get a cached response if available.
        
        Args:
            cache_key: The cache key to look up
            
        Returns:
            Cached response or None if not found
        """
        if not self.enable_caching:
            return None
            
        # Check in-memory cache first
        if cache_key in self.cache:
            if self.verbose:
                logger.info(f"Cache hit for key: {cache_key[:50]}...")
            return self.cache[cache_key]
            
        # Then check file cache if configured
        if self.cache_dir:
            cache_path = Path(self.cache_dir) / f"{cache_key}.json"
            if cache_path.exists():
                try:
                    with open(cache_path, 'r') as f:
                        response = json.load(f)
                    if self.verbose:
                        logger.info(f"File cache hit for key: {cache_key[:50]}...")
                    # Update in-memory cache
                    self.cache[cache_key] = response
                    return response
                except Exception as e:
                    logger.warning(f"Error reading cache file: {str(e)}")
                    
        return None
    
    def save_to_cache(self, cache_key: str, response: Dict[str, Any]) -> None:
        """
        Save a response to the cache.
        
        Args:
            cache_key: The cache key to use
            response: The response to cache
        """
        if not self.enable_caching:
            return
            
        # Save to in-memory cache
        self.cache[cache_key] = response
        
        # Save to file cache if configured
        if self.cache_dir:
            try:
                cache_dir_path = Path(self.cache_dir)
                cache_dir_path.mkdir(parents=True, exist_ok=True)
                cache_path = cache_dir_path / f"{cache_key}.json"
                with open(cache_path, 'w') as f:
                    json.dump(response, f)
            except Exception as e:
                logger.warning(f"Error writing to cache file: {str(e)}")
    
    def clear_cache(self) -> None:
        """Clear the in-memory cache."""
        self.cache = {}
        logger.info("In-memory cache cleared")
    
    def get_cache_size(self) -> int:
        """
        Get the number of items in the in-memory cache.
        
        Returns:
            Number of cached items
        """
        return len(self.cache)
    
    @measure_latency("model_generate")
    @measure_memory
    def generate_with_metrics(
        self,
        prompt: str,
        system_message: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate a response with performance metrics tracking.
        
        Args:
            prompt: The prompt to generate a response for
            system_message: Optional system message
            **kwargs: Additional parameters for generate()
            
        Returns:
            Dict containing response text, metadata, and performance metrics
        """
        if not self.track_performance:
            return self.generate(prompt, system_message, **kwargs)
            
        start_time = time.time()
        
        # Track token count
        prompt_tokens = self.get_token_count(prompt)
        if system_message:
            prompt_tokens += self.get_token_count(system_message)
            
        # Generate response
        response = self.generate(prompt, system_message, **kwargs)
        
        # Calculate metrics
        completion_tokens = self.get_token_count(response.get("text", ""))
        total_tokens = prompt_tokens + completion_tokens
        response_time = time.time() - start_time
        
        # Add metrics to response
        response["metrics"] = {
            "prompt_tokens": prompt_tokens,
            "completion_tokens": completion_tokens,
            "total_tokens": total_tokens,
            "response_time": response_time
        }
        
        # Save metrics for tracking
        if self.track_performance:
            self.performance_metrics.append(response["metrics"])
            
        return response
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """
        Get a summary of performance metrics.
        
        Returns:
            Dict containing performance summary statistics
        """
        if not self.performance_metrics:
            return {"error": "No performance metrics available"}
            
        # Calculate summary statistics
        total_calls = len(self.performance_metrics)
        total_tokens = sum(m["total_tokens"] for m in self.performance_metrics)
        total_prompt_tokens = sum(m["prompt_tokens"] for m in self.performance_metrics)
        total_completion_tokens = sum(m["completion_tokens"] for m in self.performance_metrics)
        total_time = sum(m["response_time"] for m in self.performance_metrics)
        
        # Calculate averages
        avg_tokens_per_call = total_tokens / total_calls if total_calls > 0 else 0
        avg_time_per_call = total_time / total_calls if total_calls > 0 else 0
        avg_tokens_per_second = total_tokens / total_time if total_time > 0 else 0
        
        return {
            "total_calls": total_calls,
            "total_tokens": total_tokens,
            "total_prompt_tokens": total_prompt_tokens,
            "total_completion_tokens": total_completion_tokens,
            "total_time": total_time,
            "avg_tokens_per_call": avg_tokens_per_call,
            "avg_time_per_call": avg_time_per_call,
            "avg_tokens_per_second": avg_tokens_per_second,
            "model": self.model_name,
            "provider": self.provider
        }
    
    def stream_generate(
        self,
        prompt: str,
        system_message: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate a response with streaming support.
        This is a default implementation that must be overridden by models 
        that support streaming.
        
        Args:
            prompt: The prompt to generate a response for
            system_message: Optional system message
            callback: Function to call with each chunk of the response
            **kwargs: Additional parameters
            
        Returns:
            Dict containing the complete response text and metadata
        """
        # Default implementation just calls generate and simulates streaming
        response = self.generate(prompt, system_message, **kwargs)
        
        if callback and "text" in response:
            # Simulate streaming by sending the response in chunks
            text = response["text"]
            chunk_size = max(1, len(text) // 10)
            
            for i in range(0, len(text), chunk_size):
                chunk = text[i:i+chunk_size]
                callback(chunk)
                time.sleep(0.1)  # Simulate delay
                
        return response
    
    def predict(
        self,
        prompt: str,
        system_message: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        Simple prediction method that returns just the response text.
        
        Args:
            prompt: The prompt to generate a response for
            system_message: Optional system message
            **kwargs: Additional parameters for generate()
            
        Returns:
            Generated text response
        """
        response = self.generate(prompt, system_message, **kwargs)
        return response.get("text", "")
    
    def classify(
        self,
        prompt: str,
        classes: List[str],
        system_message: Optional[str] = None,
        **kwargs
    ) -> Tuple[str, float]:
        """
        Classify the prompt into one of the given classes.
        
        Args:
            prompt: The prompt to classify
            classes: List of possible classes
            system_message: Optional system message
            **kwargs: Additional parameters for generate()
            
        Returns:
            Tuple of (predicted_class, confidence)
        """
        # Construct a classification prompt
        class_list = ", ".join(classes)
        classification_prompt = (
            f"{prompt}\n\n"
            f"Please classify the above text into exactly one of these classes: {class_list}.\n"
            f"Your response should be exactly one class from the list."
        )
        
        # Generate response
        response = self.generate(classification_prompt, system_message, **kwargs)
        predicted_text = response.get("text", "").strip()
        
        # Find the best matching class
        best_match = None
        best_score = 0.0
        
        for cls in classes:
            # Check for exact match first
            if predicted_text.lower() == cls.lower():
                return cls, 1.0
            
            # Otherwise look for the class name in the response
            if cls.lower() in predicted_text.lower():
                # Simple scoring - could be improved
                score = len(cls) / len(predicted_text)
                if score > best_score:
                    best_match = cls
                    best_score = score
        
        # If no match found, return the first class with 0.0 confidence
        if best_match is None:
            return classes[0] if classes else "", 0.0
            
        return best_match, best_score
    
    def extract_entities(
        self,
        prompt: str,
        entity_types: List[str],
        system_message: Optional[str] = None,
        **kwargs
    ) -> Dict[str, List[str]]:
        """
        Extract entities of specified types from text.
        
        Args:
            prompt: The text to extract entities from
            entity_types: List of entity types to extract (e.g., ["person", "organization"])
            system_message: Optional system message
            **kwargs: Additional parameters for generate()
            
        Returns:
            Dictionary mapping entity types to lists of extracted entities
        """
        # Construct an entity extraction prompt
        entity_list = ", ".join(entity_types)
        extraction_prompt = (
            f"{prompt}\n\n"
            f"Please extract all entities of the following types from the above text: {entity_list}.\n"
            f"Format the output as a JSON object where keys are entity types and values are arrays of entities.\n"
            f"Example: {{'person': ['John Smith', 'Jane Doe'], 'organization': ['Acme Corp']}}"
        )
        
        # Generate response
        response = self.generate(extraction_prompt, system_message, **kwargs)
        response_text = response.get("text", "").strip()
        
        # Try to parse as JSON
        try:
            # Find JSON in the response (it might have additional text)
            start_idx = response_text.find("{")
            end_idx = response_text.rfind("}")
            
            if start_idx >= 0 and end_idx > start_idx:
                json_str = response_text[start_idx:end_idx+1]
                entities = json.loads(json_str)
                
                # Ensure all entity types are present (even if empty)
                result = {entity_type: [] for entity_type in entity_types}
                for entity_type, values in entities.items():
                    if entity_type in entity_types and isinstance(values, list):
                        result[entity_type] = values
                        
                return result
        except (json.JSONDecodeError, ValueError):
            pass
            
        # Fallback: return empty lists for all entity types
        return {entity_type: [] for entity_type in entity_types}
    
    def summarize(
        self,
        text: str,
        max_length: Optional[int] = None,
        system_message: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        Summarize the given text.
        
        Args:
            text: The text to summarize
            max_length: Optional maximum length of summary in words
            system_message: Optional system message
            **kwargs: Additional parameters for generate()
            
        Returns:
            Summarized text
        """
        # Construct a summarization prompt
        length_guidance = f" in {max_length} words or less" if max_length else ""
        summary_prompt = (
            f"{text}\n\n"
            f"Please provide a concise summary of the above text{length_guidance}."
        )
        
        # Generate response
        response = self.generate(summary_prompt, system_message, **kwargs)
        return response.get("text", "").strip()
    
    def answer_question(
        self,
        question: str,
        context: Optional[str] = None,
        system_message: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Answer a question, optionally based on provided context.
        
        Args:
            question: The question to answer
            context: Optional context to use when answering
            system_message: Optional system message
            **kwargs: Additional parameters for generate()
            
        Returns:
            Dictionary containing answer and metadata
        """
        # Construct question answering prompt
        if context:
            qa_prompt = (
                f"Context: {context}\n\n"
                f"Question: {question}\n\n"
                f"Please answer the question based on the provided context."
            )
        else:
            qa_prompt = f"Question: {question}\n\nPlease answer this question."
            
        # Generate response
        response = self.generate(qa_prompt, system_message, **kwargs)
        
        # Add question and context to response
        response["question"] = question
        if context:
            response["context"] = context
            
        return response 