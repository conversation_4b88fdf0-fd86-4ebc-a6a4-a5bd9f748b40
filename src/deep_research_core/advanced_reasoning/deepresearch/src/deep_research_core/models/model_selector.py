"""
Model Selector Utility.

This module provides functionality to automatically select the most appropriate model
for a given task based on performance metrics, task requirements, and other criteria.
"""

import os
from typing import Dict, Any, List, Optional, Tuple, Union
import json
import time

from .base_reasoning_model import BaseReasoningModel
from .gpt_o1 import GPTO1Model
from .deepseek_r1 import DeepseekR1Model
from .qwq_32b import QwQ32BModel
from ..utils.structured_logging import get_logger

logger = get_logger(__name__)


class ModelSelector:
    """
    Utility for selecting the most appropriate model for a given task.
    
    This class provides methods to benchmark models, store performance metrics,
    and automatically select the best model for various tasks based on
    historical performance data and task requirements.
    """
    
    def __init__(
        self,
        available_models: Optional[Dict[str, BaseReasoningModel]] = None,
        performance_data_path: Optional[str] = None,
        cache_results: bool = True,
        auto_benchmark: bool = False,
        verbose: bool = False,
    ):
        """
        Initialize the ModelSelector.
        
        Args:
            available_models: Dictionary of model_id -> model_instance
            performance_data_path: Path to store/load performance data
            cache_results: Whether to cache selection results
            auto_benchmark: Whether to automatically benchmark missing models
            verbose: Whether to print detailed information
        """
        self.available_models = available_models or {}
        self.cache_results = cache_results
        self.auto_benchmark = auto_benchmark
        self.verbose = verbose
        
        # Set default performance data path if not provided
        if performance_data_path is None:
            home_dir = os.path.expanduser("~")
            self.performance_data_path = os.path.join(
                home_dir, ".deep_research_core", "model_performance.json"
            )
        else:
            self.performance_data_path = performance_data_path
        
        # Load existing performance data if available
        self.performance_data = self._load_performance_data()
        
        # Cache for selection results
        self.selection_cache = {}
        
        if self.verbose:
            logger.info(f"Initialized ModelSelector with {len(self.available_models)} models")
    
    def register_model(self, model_id: str, model: BaseReasoningModel) -> None:
        """
        Register a model with the selector.
        
        Args:
            model_id: Unique identifier for the model
            model: The model instance
        """
        self.available_models[model_id] = model
        if self.verbose:
            logger.info(f"Registered model: {model_id}")
    
    def unregister_model(self, model_id: str) -> None:
        """
        Unregister a model from the selector.
        
        Args:
            model_id: Unique identifier for the model
        """
        if model_id in self.available_models:
            del self.available_models[model_id]
            if self.verbose:
                logger.info(f"Unregistered model: {model_id}")
        else:
            if self.verbose:
                logger.warning(f"Model {model_id} not found in available models")
    
    def get_available_models(self) -> List[str]:
        """
        Get a list of available model IDs.
        
        Returns:
            List of model IDs
        """
        return list(self.available_models.keys())
    
    def get_model_instance(self, model_id: str) -> Optional[BaseReasoningModel]:
        """
        Get a model instance by ID.
        
        Args:
            model_id: The model ID
            
        Returns:
            The model instance, or None if not found
        """
        return self.available_models.get(model_id)
    
    def benchmark_model(
        self,
        model_id: str,
        task_type: str,
        test_cases: List[Dict[str, Any]],
        metrics: List[str] = ["accuracy", "latency"],
        **kwargs
    ) -> Dict[str, Any]:
        """
        Benchmark a model on specific task types.
        
        Args:
            model_id: The model ID to benchmark
            task_type: The type of task to benchmark on
            test_cases: List of test cases with prompts and expected outputs
            metrics: List of metrics to evaluate
            **kwargs: Additional benchmark parameters
            
        Returns:
            Dictionary of benchmark results
        """
        model = self.get_model_instance(model_id)
        if model is None:
            logger.error(f"Model {model_id} not found")
            return {}
        
        results = {
            "model_id": model_id,
            "task_type": task_type,
            "metrics": {},
            "test_cases": len(test_cases),
            "timestamp": time.time()
        }
        
        # Initialize metric trackers
        accuracy_scores = []
        latency_scores = []
        token_usage = {"prompt": 0, "completion": 0, "total": 0}
        
        for i, test_case in enumerate(test_cases):
            prompt = test_case["prompt"]
            expected = test_case.get("expected")
            system_message = test_case.get("system_message")
            
            # Track start time
            start_time = time.time()
            
            # Generate response
            response = model.generate_with_metrics(
                prompt=prompt,
                system_message=system_message,
                **kwargs
            )
            
            # Calculate latency
            latency = time.time() - start_time
            latency_scores.append(latency)
            
            # Extract generated text
            generated = response.get("text", "")
            
            # Calculate accuracy if expected output is provided
            if expected is not None and "accuracy" in metrics:
                # Simple exact match accuracy - could be improved with semantic similarity
                accuracy = 1.0 if generated.strip() == expected.strip() else 0.0
                accuracy_scores.append(accuracy)
            
            # Track token usage
            if "token_usage" in response:
                usage = response["token_usage"]
                token_usage["prompt"] += usage.get("prompt_tokens", 0)
                token_usage["completion"] += usage.get("completion_tokens", 0)
                token_usage["total"] += usage.get("total_tokens", 0)
            
            if self.verbose and (i + 1) % 5 == 0:
                logger.info(f"Processed {i + 1}/{len(test_cases)} test cases for {model_id}")
        
        # Calculate average metrics
        if accuracy_scores:
            results["metrics"]["accuracy"] = sum(accuracy_scores) / len(accuracy_scores)
        
        if latency_scores:
            results["metrics"]["latency"] = sum(latency_scores) / len(latency_scores)
        
        results["metrics"]["token_usage"] = token_usage
        
        # Store results in performance data
        if model_id not in self.performance_data:
            self.performance_data[model_id] = {}
        
        if task_type not in self.performance_data[model_id]:
            self.performance_data[model_id][task_type] = []
        
        self.performance_data[model_id][task_type].append(results)
        
        # Save updated performance data
        self._save_performance_data()
        
        if self.verbose:
            logger.info(f"Benchmark completed for {model_id} on {task_type}")
        
        return results
    
    def select_model(
        self,
        task_type: str,
        metrics_weights: Optional[Dict[str, float]] = None
    ) -> Tuple[Optional[str], Optional[BaseReasoningModel]]:
        """
        Select the best model for a given task type based on historical performance.
        
        Args:
            task_type: The type of task (e.g., reasoning, qa, summarization)
            metrics_weights: Dictionary of metric -> weight for scoring (default prioritizes accuracy)
            
        Returns:
            Tuple of (model_id, model_instance)
        """
        if not self.available_models:
            logger.warning("No models available for selection")
            return None, None
        
        # Check if we have cached result for this task type
        cache_key = f"{task_type}_{str(metrics_weights)}"
        if self.cache_results and cache_key in self.selection_cache:
            cached_model_id = self.selection_cache[cache_key]
            if cached_model_id in self.available_models:
                if self.verbose:
                    logger.info(f"Using cached model selection {cached_model_id} for {task_type}")
                return cached_model_id, self.get_model_instance(cached_model_id)
        
        # Default weights if not provided
        if metrics_weights is None:
            metrics_weights = {
                "accuracy": 0.7,
                "latency": -0.3,  # Negative because lower is better
            }
        
        best_model_id = None
        best_score = float('-inf')
        
        for model_id in self.available_models:
            model_score = self._calculate_model_score(model_id, task_type, metrics_weights)
            
            # Update best model if score is higher
            if model_score > best_score:
                best_score = model_score
                best_model_id = model_id
        
        # If no model has performance data for this task type, use the first model
        if best_model_id is None and self.available_models:
            best_model_id = next(iter(self.available_models))
            if self.verbose:
                logger.info(f"No performance data for task type {task_type}, using {best_model_id} as default")
        
        # Cache the result
        if self.cache_results and best_model_id is not None:
            self.selection_cache[cache_key] = best_model_id
        
        if self.verbose:
            logger.info(f"Selected model {best_model_id} for {task_type}")
        
        return best_model_id, self.get_model_instance(best_model_id)
    
    def _calculate_model_score(self, model_id: str, task_type: str, metrics_weights: Dict[str, float]) -> float:
        """
        Calculate a weighted score for a model on a specific task type.
        
        Args:
            model_id: The model ID
            task_type: The task type
            metrics_weights: Dictionary of metric -> weight for scoring
            
        Returns:
            Weighted score
        """
        # Check if model has performance data for this task type
        if model_id not in self.performance_data:
            return float('-inf')
        
        if task_type not in self.performance_data[model_id]:
            return float('-inf')
        
        # Get the most recent benchmark result
        benchmark_results = self.performance_data[model_id][task_type]
        if not benchmark_results:
            return float('-inf')
        
        latest_benchmark = benchmark_results[-1]
        
        # Calculate weighted score
        score = 0.0
        for metric, weight in metrics_weights.items():
            if metric in latest_benchmark["metrics"]:
                metric_value = latest_benchmark["metrics"][metric]
                
                # Handle different metric types
                if isinstance(metric_value, (int, float)):
                    score += weight * metric_value
                elif metric == "token_usage" and isinstance(metric_value, dict):
                    # For token usage, lower is better (negative weight expected)
                    if "total" in metric_value and isinstance(metric_value["total"], (int, float)):
                        score += weight * (1.0 / (1.0 + metric_value["total"]))  # Normalize to 0-1 range
        
        return score
    
    def analyze_task(self, task_description: str) -> str:
        """
        Analyze a task description to determine its type.
        
        Args:
            task_description: Description of the task
            
        Returns:
            The identified task type
        """
        # Simple keyword-based task type identification
        task_type_keywords = {
            "reasoning": ["reason", "think", "logic", "solve", "puzzle"],
            "qa": ["question", "answer", "query", "ask", "respond"],
            "summarization": ["summarize", "summary", "condense", "shorten"],
            "translation": ["translate", "translation", "language", "convert"],
            "classification": ["classify", "categorize", "sort", "label"],
            "generation": ["generate", "create", "write", "produce", "draft"],
            "extraction": ["extract", "identify", "find", "locate", "pull"]
        }
        
        # Count keyword occurrences
        task_scores = {task_type: 0 for task_type in task_type_keywords}
        
        for task_type, keywords in task_type_keywords.items():
            for keyword in keywords:
                if keyword.lower() in task_description.lower():
                    task_scores[task_type] += 1
        
        # Get the task type with the highest score
        best_task_type = max(task_scores.items(), key=lambda x: x[1])
        
        # If no matches found, default to "general"
        if best_task_type[1] == 0:
            return "general"
        
        return best_task_type[0]
    
    def select_for_prompt(
        self,
        prompt: str,
        system_message: Optional[str] = None,
        criteria: Optional[Dict[str, float]] = None,
        constraints: Optional[Dict[str, Any]] = None
    ) -> Tuple[str, BaseReasoningModel]:
        """
        Select the best model for a given prompt.
        
        Args:
            prompt: The user prompt
            system_message: Optional system message
            criteria: Dictionary of metric -> weight for scoring
            constraints: Dictionary of constraints
            
        Returns:
            Tuple of (model_id, model_instance)
        """
        # Combine prompt and system message for analysis
        analysis_text = prompt
        if system_message:
            analysis_text = f"{system_message}\n\n{prompt}"
        
        # Analyze the task type
        task_type = self.analyze_task(analysis_text)
        
        # Select model based on task type
        return self.select_model(task_type)
    
    def _load_performance_data(self) -> Dict[str, Any]:
        """
        Load performance data from disk.
        
        Returns:
            Dictionary of performance data
        """
        if not os.path.exists(self.performance_data_path):
            return {}
        
        try:
            with open(self.performance_data_path, "r") as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError) as e:
            logger.error(f"Error loading performance data: {str(e)}")
            return {}
    
    def _save_performance_data(self) -> bool:
        """
        Save performance data to disk.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.performance_data_path), exist_ok=True)
            
            with open(self.performance_data_path, "w") as f:
                json.dump(self.performance_data, f, indent=2)
            
            return True
        except IOError as e:
            logger.error(f"Error saving performance data: {str(e)}")
            return False
    
    def get_model_performance_summary(self, model_id: str) -> Dict[str, Any]:
        """
        Get a summary of performance for a specific model.
        
        Args:
            model_id: The model ID
            
        Returns:
            Dictionary of performance summary
        """
        if model_id not in self.performance_data:
            return {}
        
        summary = {
            "model_id": model_id,
            "task_types": {},
        }
        
        for task_type, results in self.performance_data[model_id].items():
            if not results:
                continue
            
            # Calculate average metrics across all benchmarks
            metrics = {}
            
            for result in results:
                for metric, value in result["metrics"].items():
                    if metric not in metrics:
                        metrics[metric] = []
                    
                    if isinstance(value, (int, float)):
                        metrics[metric].append(value)
            
            # Calculate averages
            avg_metrics = {}
            for metric, values in metrics.items():
                if values:
                    avg_metrics[metric] = sum(values) / len(values)
            
            summary["task_types"][task_type] = {
                "benchmarks": len(results),
                "latest": results[-1]["timestamp"],
                "metrics": avg_metrics
            }
        
        return summary
    
    def recommend_model_improvements(self, model_id: str) -> List[Dict[str, Any]]:
        """
        Recommend improvements for a model based on performance data.
        
        Args:
            model_id: The model ID
            
        Returns:
            List of improvement recommendations
        """
        if model_id not in self.performance_data:
            return []
        
        recommendations = []
        
        # Check each task type
        for task_type, results in self.performance_data[model_id].items():
            if not results:
                continue
            
            latest = results[-1]
            
            # Check accuracy
            accuracy = latest["metrics"].get("accuracy", 0)
            if accuracy < 0.7:
                recommendations.append({
                    "task_type": task_type,
                    "metric": "accuracy",
                    "current": accuracy,
                    "target": 0.7,
                    "recommendation": "Consider fine-tuning the model or using a more capable model for this task type."
                })
            
            # Check latency
            latency = latest["metrics"].get("latency", 0)
            if latency > 5.0:
                recommendations.append({
                    "task_type": task_type,
                    "metric": "latency",
                    "current": latency,
                    "target": 5.0,
                    "recommendation": "Consider optimizing the model or using a faster model for this task type."
                })
        
        return recommendations
    
    def benchmark_models(
        self,
        model_ids: List[str],
        task_type: str,
        test_cases: List[Dict[str, Any]],
        metrics: List[str] = ["accuracy", "latency"],
        parallel: bool = False,
        **kwargs
    ) -> Dict[str, Dict[str, Any]]:
        """
        Benchmark multiple models on the same task type.
        
        Args:
            model_ids: List of model IDs to benchmark
            task_type: The type of task to benchmark on
            test_cases: List of test cases with prompts and expected outputs
            metrics: List of metrics to evaluate
            parallel: Whether to run benchmarks in parallel (if supported)
            **kwargs: Additional benchmark parameters
            
        Returns:
            Dictionary of model_id -> benchmark results
        """
        if not model_ids:
            logger.warning("No models specified for benchmarking")
            return {}
        
        # Validate that all models exist
        invalid_models = [model_id for model_id in model_ids if model_id not in self.available_models]
        if invalid_models:
            logger.error(f"Invalid model IDs: {invalid_models}")
            model_ids = [model_id for model_id in model_ids if model_id in self.available_models]
        
        if not model_ids:
            logger.error("No valid models to benchmark")
            return {}
        
        results = {}
        
        if parallel and len(model_ids) > 1:
            # Implement parallel benchmarking if needed
            # This would require additional dependencies like concurrent.futures
            logger.warning("Parallel benchmarking not implemented, falling back to sequential")
        
        # Run benchmarks sequentially
        for model_id in model_ids:
            if self.verbose:
                logger.info(f"Benchmarking model {model_id} on {task_type}")
            
            results[model_id] = self.benchmark_model(
                model_id=model_id,
                task_type=task_type,
                test_cases=test_cases,
                metrics=metrics,
                **kwargs
            )
        
        # Compare models and log results
        if self.verbose and len(model_ids) > 1:
            self._log_benchmark_comparison(results, metrics)
        
        return results
    
    def _log_benchmark_comparison(self, results: Dict[str, Dict[str, Any]], metrics: List[str]) -> None:
        """
        Log a comparison of benchmark results for multiple models.
        
        Args:
            results: Dictionary of model_id -> benchmark results
            metrics: List of metrics that were evaluated
        """
        if not results:
            return
        
        logger.info("=== Benchmark Comparison ===")
        
        # Extract metrics for each model
        model_metrics = {}
        for model_id, benchmark in results.items():
            model_metrics[model_id] = benchmark.get("metrics", {})
        
        # Print comparison for each metric
        for metric in metrics:
            if metric == "token_usage":
                continue  # Skip token usage for comparison table
                
            logger.info(f"\n{metric.upper()}:")
            for model_id, metric_values in model_metrics.items():
                value = metric_values.get(metric, "N/A")
                logger.info(f"  {model_id}: {value}")
        
        # Compare token usage if available
        if "token_usage" in metrics:
            logger.info("\nTOKEN USAGE:")
            for model_id, metric_values in model_metrics.items():
                token_usage = metric_values.get("token_usage", {})
                if token_usage:
                    logger.info(f"  {model_id}: prompt={token_usage.get('prompt', 'N/A')}, "
                             f"completion={token_usage.get('completion', 'N/A')}, "
                             f"total={token_usage.get('total', 'N/A')}")
                else:
                    logger.info(f"  {model_id}: N/A")
        
        logger.info("===========================")
        
        # Identify best model for each metric
        best_models = {}
        for metric in metrics:
            if metric == "token_usage":
                continue
                
            best_model = None
            best_value = None
            
            for model_id, metric_values in model_metrics.items():
                if metric not in metric_values:
                    continue
                    
                value = metric_values[metric]
                
                # Skip non-numeric values
                if not isinstance(value, (int, float)):
                    continue
                
                # For latency, lower is better
                if metric == "latency":
                    if best_value is None or value < best_value:
                        best_value = value
                        best_model = model_id
                else:
                    # For most metrics like accuracy, higher is better
                    if best_value is None or value > best_value:
                        best_value = value
                        best_model = model_id
            
            if best_model:
                best_models[metric] = (best_model, best_value)
        
        # Log best models
        if best_models:
            logger.info("\nBEST MODELS:")
            for metric, (model_id, value) in best_models.items():
                logger.info(f"  {metric}: {model_id} ({value})")
        
        logger.info("===========================")
    
    def register_model_performance(
        self,
        model_id: str,
        task_type: str,
        metrics: Dict[str, Any],
        test_cases_count: int = 0,
        save: bool = True
    ) -> bool:
        """
        Register performance data for a model without running a benchmark.
        
        This is useful for importing external benchmark results or manually
        setting performance metrics.
        
        Args:
            model_id: The model ID
            task_type: The type of task
            metrics: Dictionary of metrics (e.g., accuracy, latency, token_usage)
            test_cases_count: Number of test cases used to derive the metrics
            save: Whether to save the updated performance data to disk
            
        Returns:
            True if registration was successful, False otherwise
        """
        if model_id not in self.available_models:
            logger.error(f"Cannot register performance for unknown model {model_id}")
            return False
        
        # Initialize model data structure if needed
        if model_id not in self.performance_data:
            self.performance_data[model_id] = {}
        
        if task_type not in self.performance_data[model_id]:
            self.performance_data[model_id][task_type] = []
        
        # Create a performance record
        performance_record = {
            "model_id": model_id,
            "task_type": task_type,
            "metrics": metrics,
            "test_cases": test_cases_count,
            "timestamp": time.time(),
            "manual_entry": True
        }
        
        # Add to performance data
        self.performance_data[model_id][task_type].append(performance_record)
        
        if self.verbose:
            logger.info(f"Registered performance data for {model_id} on {task_type}")
        
        # Save to disk if requested
        if save:
            return self._save_performance_data()
        
        return True
    
    def get_model_recommendations(
        self,
        task_type: str,
        top_n: int = 3,
        metrics_weights: Optional[Dict[str, float]] = None
    ) -> List[Dict[str, Any]]:
        """
        Get recommendations of models for a specific task type.
        
        Args:
            task_type: The type of task
            top_n: Number of recommendations to return
            metrics_weights: Dictionary of metric -> weight for scoring
            
        Returns:
            List of recommendation objects with model_id, score, and metrics
        """
        # Default weights if not provided
        if metrics_weights is None:
            metrics_weights = {
                "accuracy": 0.7,
                "latency": -0.3,  # Negative because lower is better
            }
        
        # Calculate scores for all models
        model_scores = []
        for model_id in self.available_models:
            score = self._calculate_model_score(model_id, task_type, metrics_weights)
            
            # Skip models with no performance data
            if score == float('-inf'):
                continue
            
            # Get the performance metrics
            if (model_id in self.performance_data and 
                task_type in self.performance_data[model_id] and
                self.performance_data[model_id][task_type]):
                latest_benchmark = self.performance_data[model_id][task_type][-1]
                metrics = latest_benchmark.get("metrics", {})
            else:
                metrics = {}
            
            model_scores.append({
                "model_id": model_id,
                "score": score,
                "metrics": metrics
            })
        
        # Sort by score (descending)
        model_scores.sort(key=lambda x: x["score"], reverse=True)
        
        # Return top N recommendations
        recommendations = model_scores[:top_n]
        
        # If no models have performance data, recommend available models
        if not recommendations and self.available_models:
            recommendations = [
                {"model_id": model_id, "score": 0.0, "metrics": {}, "note": "No performance data available"}
                for model_id in list(self.available_models)[:top_n]
            ]
        
        return recommendations
        
    def select_model_for_language(
        self,
        language: str,
        metrics_weights: Optional[Dict[str, float]] = None,
        task_type: Optional[str] = None
    ) -> Tuple[Optional[str], Optional[BaseReasoningModel]]:
        """
        Select the best model for a specific language.
        
        Args:
            language: The language to select a model for (e.g., 'en', 'vi', 'zh')
            metrics_weights: Weights for different metrics (defaults to balanced weights)
            task_type: Optional task type to further refine selection
            
        Returns:
            Tuple of (model_id, model_instance) or (None, None) if no suitable model found
        """
        if not self.available_models:
            logger.warning("No models available for selection")
            return None, None
            
        # Check if result is in cache
        cache_key = f"lang_{language}_{task_type}_{str(metrics_weights)}"
        if self.cache_results and cache_key in self.selection_cache:
            model_id = self.selection_cache[cache_key]
            return model_id, self.get_model_instance(model_id)
        
        # Default weights if not provided
        if metrics_weights is None:
            metrics_weights = {
                "accuracy": 0.6,
                "latency": 0.2,
                "robustness": 0.2
            }
        
        # Filter models that support this language
        language_models = {}
        for model_id, model in self.available_models.items():
            supported_languages = getattr(model, "supported_languages", ["en"])
            
            # Check if this model supports the requested language
            if language in supported_languages:
                language_models[model_id] = model
        
        if not language_models:
            logger.warning(f"No models available that support language: {language}")
            return None, None
        
        # If task_type is provided, further filter by task performance
        if task_type:
            best_model_id = None
            best_score = -float('inf')
            
            for model_id in language_models:
                score = self._calculate_model_score(model_id, task_type, metrics_weights)
                if score > best_score:
                    best_score = score
                    best_model_id = model_id
            
            # Cache result
            if self.cache_results and best_model_id is not None:
                self.selection_cache[cache_key] = best_model_id
            
            return best_model_id, self.get_model_instance(best_model_id)
        
        # Otherwise select the model with the best average performance
        best_model_id = None
        best_avg_score = -float('inf')
        
        for model_id in language_models:
            task_scores = []
            
            # Check performance across all task types
            for task in self.performance_data.get(model_id, {}).get("tasks", {}):
                task_score = self._calculate_model_score(model_id, task, metrics_weights)
                task_scores.append(task_score)
            
            # Calculate average score
            avg_score = sum(task_scores) / len(task_scores) if task_scores else 0
            
            if avg_score > best_avg_score:
                best_avg_score = avg_score
                best_model_id = model_id
        
        # Cache result
        if self.cache_results and best_model_id is not None:
            self.selection_cache[cache_key] = best_model_id
        
        return best_model_id, self.get_model_instance(best_model_id)
    
    def select_model_for_complexity(
        self,
        complexity_level: str,
        metrics_weights: Optional[Dict[str, float]] = None,
        task_type: Optional[str] = None
    ) -> Tuple[Optional[str], Optional[BaseReasoningModel]]:
        """
        Select the best model for a specific complexity level.
        
        Args:
            complexity_level: The complexity level ('simple', 'medium', 'complex')
            metrics_weights: Weights for different metrics (defaults to balanced weights)
            task_type: Optional task type to further refine selection
            
        Returns:
            Tuple of (model_id, model_instance) or (None, None) if no suitable model found
        """
        if not self.available_models:
            logger.warning("No models available for selection")
            return None, None
            
        # Check if result is in cache
        cache_key = f"complexity_{complexity_level}_{task_type}_{str(metrics_weights)}"
        if self.cache_results and cache_key in self.selection_cache:
            model_id = self.selection_cache[cache_key]
            return model_id, self.get_model_instance(model_id)
        
        # Default weights if not provided
        if metrics_weights is None:
            # For simple tasks, prioritize latency
            if complexity_level == "simple":
                metrics_weights = {
                    "accuracy": 0.4,
                    "latency": 0.5,
                    "robustness": 0.1
                }
            # For complex tasks, prioritize accuracy
            elif complexity_level == "complex":
                metrics_weights = {
                    "accuracy": 0.7,
                    "latency": 0.1,
                    "robustness": 0.2
                }
            # For medium complexity, balanced weights
            else:
                metrics_weights = {
                    "accuracy": 0.5,
                    "latency": 0.3,
                    "robustness": 0.2
                }
        
        # Define minimum capabilities for each complexity level
        min_capabilities = {
            "simple": {},  # No minimum requirements for simple tasks
            "medium": {"context_window": 4000},
            "complex": {"context_window": 8000}
        }
        
        # Filter models that meet the minimum requirements
        filtered_models = {}
        requirements = min_capabilities.get(complexity_level, {})
        
        for model_id, model in self.available_models.items():
            meets_requirements = True
            
            # Check context window size
            if "context_window" in requirements:
                model_context_window = getattr(model, "context_window", 0)
                if model_context_window < requirements["context_window"]:
                    meets_requirements = False
            
            if meets_requirements:
                filtered_models[model_id] = model
        
        if not filtered_models:
            logger.warning(f"No models available that meet requirements for complexity: {complexity_level}")
            return None, None
        
        # If task_type is provided, select best model for the task
        if task_type:
            best_model_id = None
            best_score = -float('inf')
            
            for model_id in filtered_models:
                score = self._calculate_model_score(model_id, task_type, metrics_weights)
                if score > best_score:
                    best_score = score
                    best_model_id = model_id
            
            # Cache result
            if self.cache_results and best_model_id is not None:
                self.selection_cache[cache_key] = best_model_id
            
            return best_model_id, self.get_model_instance(best_model_id)
        
        # Otherwise select the model with the best average performance
        best_model_id = None
        best_avg_score = -float('inf')
        
        for model_id in filtered_models:
            task_scores = []
            
            # Check performance across all task types
            for task in self.performance_data.get(model_id, {}).get("tasks", {}):
                task_score = self._calculate_model_score(model_id, task, metrics_weights)
                task_scores.append(task_score)
            
            # Calculate average score
            avg_score = sum(task_scores) / len(task_scores) if task_scores else 0
            
            if avg_score > best_avg_score:
                best_avg_score = avg_score
                best_model_id = model_id
        
        # Cache result
        if self.cache_results and best_model_id is not None:
            self.selection_cache[cache_key] = best_model_id
        
        return best_model_id, self.get_model_instance(best_model_id)
    
    def select_model_for_task(
        self,
        task_description: str,
        metrics_weights: Optional[Dict[str, float]] = None,
        language: Optional[str] = None,
        complexity: Optional[str] = None,
        fallback: bool = True
    ) -> Tuple[Optional[str], Optional[BaseReasoningModel]]:
        """
        Select the best model for a task description.
        
        Args:
            task_description: Description of the task
            metrics_weights: Weights for different metrics
            language: Optional language requirement
            complexity: Optional complexity level
            fallback: Whether to try fallback options if best model isn't available
            
        Returns:
            Tuple of (model_id, model_instance) or (None, None) if no suitable model found
        """
        # Analyze the task to get task type
        task_type = self.analyze_task(task_description)
        
        # First try with all criteria
        if language and complexity:
            # Try to find a model that matches language, complexity and task type
            model_id, model = self.select_model_for_language(
                language=language,
                metrics_weights=metrics_weights,
                task_type=task_type
            )
            
            # Check if the model also meets complexity requirements
            if model_id:
                complexity_model_id, _ = self.select_model_for_complexity(
                    complexity_level=complexity,
                    metrics_weights=metrics_weights,
                    task_type=task_type
                )
                
                if complexity_model_id == model_id:
                    return model_id, model
            
            # If no model satisfies all criteria, try a regular task-based selection
            model_id, model = self.select_model(
                task_type=task_type,
                metrics_weights=metrics_weights
            )
            
            if model_id:
                return model_id, model
        
        # Try with just language
        elif language:
            model_id, model = self.select_model_for_language(
                language=language,
                metrics_weights=metrics_weights,
                task_type=task_type
            )
            
            if model_id:
                return model_id, model
        
        # Try with just complexity
        elif complexity:
            model_id, model = self.select_model_for_complexity(
                complexity_level=complexity,
                metrics_weights=metrics_weights,
                task_type=task_type
            )
            
            if model_id:
                return model_id, model
        
        # If no specific requirements or no model found with requirements,
        # fall back to basic task selection
        model_id, model = self.select_model(
            task_type=task_type,
            metrics_weights=metrics_weights
        )
        
        # If fallback is enabled and no suitable model found, try general-purpose models
        if fallback and not model_id:
            logger.info("No specific model found for task, trying fallback options")
            
            # Get all available models sorted by general performance
            ranked_models = self.get_model_recommendations(
                task_type="general",
                top_n=len(self.available_models),
                metrics_weights=metrics_weights
            )
            
            if ranked_models:
                best_general_model_id = ranked_models[0]["model_id"]
                return best_general_model_id, self.get_model_instance(best_general_model_id)
        
        return model_id, model
    
    def try_with_fallbacks(
        self,
        method_name: str,
        *args,
        max_attempts: int = 3,
        **kwargs
    ) -> Tuple[Optional[str], Optional[BaseReasoningModel]]:
        """
        Try a selection method with fallbacks if the first attempt fails.
        
        Args:
            method_name: The name of the selection method to call
            *args: Positional arguments to pass to the method
            max_attempts: Maximum number of fallback attempts
            **kwargs: Keyword arguments to pass to the method
            
        Returns:
            Tuple of (model_id, model_instance) or (None, None) if all attempts fail
        """
        # Get the selection method
        if not hasattr(self, method_name):
            logger.error(f"Method {method_name} not found in ModelSelector")
            return None, None
        
        method = getattr(self, method_name)
        
        # First attempt with provided parameters
        model_id, model = method(*args, **kwargs)
        if model_id:
            return model_id, model
        
        # Define fallback strategies
        fallback_strategies = [
            # Try with more balanced metrics weights
            {"metrics_weights": {"accuracy": 0.34, "latency": 0.33, "robustness": 0.33}},
            # Try with accuracy-focused metrics weights
            {"metrics_weights": {"accuracy": 0.7, "latency": 0.15, "robustness": 0.15}},
            # Try removing language constraint if present
            {"language": None} if "language" in kwargs else {},
            # Try removing complexity constraint if present
            {"complexity": None} if "complexity" in kwargs else {},
            # Try with general task type
            {"task_type": "general"} if "task_type" in kwargs else {}
        ]
        
        # Try fallback strategies until we find a model or run out of attempts
        attempts = 0
        for strategy in fallback_strategies:
            if attempts >= max_attempts:
                break
                
            attempts += 1
            
            # Update kwargs with this strategy
            fallback_kwargs = kwargs.copy()
            fallback_kwargs.update(strategy)
            
            # Try this strategy
            model_id, model = method(*args, **fallback_kwargs)
            if model_id:
                logger.info(f"Found model {model_id} using fallback strategy: {strategy}")
                return model_id, model
        
        logger.warning(f"All fallback attempts failed for method {method_name}")
        return None, None 