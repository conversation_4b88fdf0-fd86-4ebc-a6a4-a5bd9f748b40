"""
Base model definition for language models.

This module provides the abstract base classes for language models and API providers.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional


class BaseModel(ABC):
    """
    Abstract base class for language models.

    This class defines the interface that all model implementations
    must follow, including methods for text generation and embedding.
    """

    def __init__(
        self,
        model_name: str,
        provider: str = "local",
        temperature: float = 0.7,
        max_tokens: int = 1000,
        **kwargs
    ):
        """
        Initialize a base model.

        Args:
            model_name: The name/identifier of the model
            provider: The provider of the model (e.g., openai, local, deepseek)
            temperature: Temperature for generation (higher = more random)
            max_tokens: Maximum tokens to generate in responses
            **kwargs: Additional model-specific parameters
        """
        self.model_name = model_name
        self.provider = provider
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.kwargs = kwargs

    @abstractmethod
    def generate(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> str:
        """
        Generate text based on a prompt.

        Args:
            prompt: The prompt to generate from
            system_prompt: Optional system prompt or instructions
            temperature: Optional temperature override
            max_tokens: Optional token limit override
            **kwargs: Additional generation parameters

        Returns:
            Generated text as a string
        """
        pass

    def batch_generate(
        self,
        prompts: List[str],
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> List[str]:
        """
        Generate text for multiple prompts.

        The default implementation calls generate() for each prompt.
        Subclasses can override this with more efficient batch implementations.

        Args:
            prompts: List of prompts to generate from
            system_prompt: Optional system prompt or instructions
            temperature: Optional temperature override
            max_tokens: Optional token limit override
            **kwargs: Additional generation parameters

        Returns:
            List of generated text strings
        """
        return [
            self.generate(
                prompt,
                system_prompt,
                temperature,
                max_tokens,
                **kwargs
            ) for prompt in prompts
        ]

    def get_embedding(
        self,
        text: str,
        **kwargs
    ) -> List[float]:
        """
        Get embedding vector for text.

        Args:
            text: The text to embed
            **kwargs: Additional embedding parameters

        Returns:
            List of embedding values (vector)

        Raises:
            NotImplementedError: If the model doesn't support embeddings
        """
        raise NotImplementedError("This model does not support embeddings")

    def batch_get_embeddings(
        self,
        texts: List[str],
        **kwargs
    ) -> List[List[float]]:
        """
        Get embeddings for multiple texts.

        The default implementation calls get_embedding() for each text.
        Subclasses can override this with more efficient batch implementations.

        Args:
            texts: List of texts to embed
            **kwargs: Additional embedding parameters

        Returns:
            List of embedding vectors

        Raises:
            NotImplementedError: If the model doesn't support embeddings
        """
        return [self.get_embedding(text, **kwargs) for text in texts]


class BaseAPIProvider:
    """
    Abstract base class for API providers.

    This class defines the interface that all API provider implementations
    must follow, including methods for text generation and embedding.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        api_base: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize an API provider.

        Args:
            api_key: API key for the provider
            api_base: Base URL for the API
            **kwargs: Additional provider-specific parameters
        """
        self.api_key = api_key
        self.api_base = api_base
        self.kwargs = kwargs

    def generate(
        self,
        prompt: str,
        model: str,
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        json_format: bool = False,
        **kwargs
    ) -> str:
        """
        Generate text based on a prompt.

        Args:
            prompt: The prompt to generate from
            model: The model to use
            system_prompt: Optional system prompt or instructions
            temperature: Temperature for generation
            max_tokens: Maximum tokens to generate
            json_format: Whether to format the response as JSON
            **kwargs: Additional generation parameters

        Returns:
            Generated text as a string
        """
        raise NotImplementedError("This method must be implemented by subclasses")

    def get_embedding(
        self,
        text: str,
        model: Optional[str] = None,
        **kwargs
    ) -> List[float]:
        """
        Get embedding vector for text.

        Args:
            text: The text to embed
            model: The model to use for embedding
            **kwargs: Additional embedding parameters

        Returns:
            List of embedding values (vector)
        """
        raise NotImplementedError("This method must be implemented by subclasses")

    def get_available_models(self) -> List[Dict[str, Any]]:
        """
        Get a list of available models.

        Returns:
            List of model information dictionaries
        """
        raise NotImplementedError("This method must be implemented by subclasses")

    def get_default_model(self) -> str:
        """
        Get the default model for this provider.

        Returns:
            Default model name
        """
        raise NotImplementedError("This method must be implemented by subclasses")
