"""
QwQ-32B Model Integration.

This module provides integration with QwQ's 32B model through their API.
"""

import os
import time
import logging
import json
from typing import Dict, Any, List, Optional, Union, Tuple
import requests

from .base_reasoning_model import BaseReasoningModel
from ..utils.structured_logging import get_logger
from ..models.api.qwq import qwq_provider

logger = get_logger(__name__)


class QwQ32BModel(BaseReasoningModel):
    """
    Integration with QwQ's 32B model.
    
    This class provides methods to interact with QwQ's 32B model through
    their API, following the BaseReasoningModel interface.
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        model_name: str = "qwq-32b",
        temperature: float = 0.7,
        max_tokens: int = 1000,
        request_timeout: int = 60,
        verbose: bool = False,
        **kwargs
    ):
        """
        Initialize the QwQ-32B model.
        
        Args:
            api_key: QwQ API key (if None, reads from QWQ_API_KEY env var)
            model_name: Name of the model to use (e.g., "qwq-32b")
            temperature: Temperature for sampling (higher = more random)
            max_tokens: Maximum number of tokens to generate
            request_timeout: Timeout for API requests in seconds
            verbose: Whether to print detailed information
            **kwargs: Additional parameters
        """
        # Call parent constructor
        super().__init__(
            model_name=model_name,
            provider="qwq",
            temperature=temperature,
            max_tokens=max_tokens,
            verbose=verbose,
            **kwargs
        )
        
        # Set QwQ specific parameters
        self.api_key = api_key or os.environ.get("QWQ_API_KEY")
        self.request_timeout = request_timeout
        
        # Verify required parameters
        if not self.api_key:
            raise ValueError(
                "QwQ API key is required. Either pass it as 'api_key' or "
                "set the QWQ_API_KEY environment variable."
            )
        
        # Initialize QwQ provider
        self.provider_client = qwq_provider
        
        if self.verbose:
            logger.info(f"Initialized QwQ32BModel with model={self.model_name}")
    
    def generate(
        self,
        prompt: str,
        system_message: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate a response for the given prompt.
        
        Args:
            prompt: The prompt to generate a response for
            system_message: Optional system message to guide generation
            temperature: Temperature for sampling (overrides instance value)
            max_tokens: Maximum tokens to generate (overrides instance value)
            stop_sequences: Optional list of sequences that stop generation
            **kwargs: Additional generation parameters
            
        Returns:
            Dict containing response text and metadata
        """
        # Check for cached response if caching is enabled
        if self.enable_caching:
            cache_key = json.dumps({
                "model": self.model_name,
                "prompt": prompt,
                "system_message": system_message,
                "temperature": temperature or self.temperature,
                "max_tokens": max_tokens or self.max_tokens,
                "stop_sequences": stop_sequences,
                **kwargs
            })
            
            cached_response = self.get_cached_response(cache_key)
            if cached_response:
                return cached_response
        
        # Prepare generation parameters
        temp = temperature if temperature is not None else self.temperature
        max_tok = max_tokens if max_tokens is not None else self.max_tokens
        
        # Log the request
        self.log_request(prompt, {
            "model": self.model_name,
            "temperature": temp,
            "max_tokens": max_tok,
            "stop_sequences": stop_sequences
        })
        
        try:
            # Record start time
            start_time = time.time()
            
            # Make API call
            response_text = self.provider_client.generate(
                prompt=prompt,
                model=self.model_name,
                system_prompt=system_message,
                temperature=temp,
                max_tokens=max_tok,
                stream=False
            )
            
            # Record end time
            end_time = time.time()
            
            # Estimate token counts (QwQ API might not return these directly)
            input_tokens = self.get_token_count(prompt)
            if system_message:
                input_tokens += self.get_token_count(system_message)
            output_tokens = self.get_token_count(response_text)
            total_tokens = input_tokens + output_tokens
            
            # Create result dictionary
            result = {
                "text": response_text,
                "model": self.model_name,
                "provider": "qwq",
                "tokens": {
                    "prompt": input_tokens,
                    "completion": output_tokens,
                    "total": total_tokens
                },
                "latency": end_time - start_time
            }
            
            # Log the response
            self.log_response(result)
            
            # Cache the response if caching is enabled
            if self.enable_caching:
                self.save_to_cache(cache_key, result)
            
            return result
        
        except Exception as e:
            error_msg = f"Error generating response: {str(e)}"
            logger.error(error_msg)
            
            return {
                "text": "",
                "error": error_msg,
                "model": self.model_name,
                "provider": "qwq"
            }
    
    def batch_generate(
        self,
        prompts: List[str],
        system_message: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Generate responses for multiple prompts in batch.
        
        Args:
            prompts: List of prompts to generate responses for
            system_message: Optional system message to guide generation
            temperature: Temperature for sampling (overrides instance value)
            max_tokens: Maximum tokens to generate (overrides instance value)
            stop_sequences: Optional list of sequences that stop generation
            **kwargs: Additional generation parameters
            
        Returns:
            List of dicts containing response texts and metadata
        """
        results = []
        
        # Process each prompt individually
        # Note: QwQ doesn't support true batching, so we make individual requests
        for prompt in prompts:
            result = self.generate(
                prompt=prompt,
                system_message=system_message,
                temperature=temperature,
                max_tokens=max_tokens,
                stop_sequences=stop_sequences,
                **kwargs
            )
            results.append(result)
        
        return results
    
    def get_token_count(self, text: str) -> int:
        """
        Count the number of tokens in the given text.
        
        Args:
            text: The text to count tokens for
            
        Returns:
            Number of tokens in the text
        """
        if not text:
            return 0
        
        # Simple estimation (4 characters ≈ 1 token)
        # A more accurate implementation would use a proper tokenizer
        return len(text) // 4 + 1
    
    def stream_generate(
        self,
        prompt: str,
        system_message: Optional[str] = None,
        callback: Optional[callable] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate a response with streaming support.
        
        Args:
            prompt: The prompt to generate a response for
            system_message: Optional system message
            callback: Function to call with each chunk of the response
            temperature: Temperature for sampling (overrides instance value)
            max_tokens: Maximum tokens to generate (overrides instance value)
            stop_sequences: Optional list of sequences that stop generation
            **kwargs: Additional parameters
            
        Returns:
            Dict containing the complete response text and metadata
        """
        if not callback:
            # Fall back to non-streaming if no callback provided
            return self.generate(
                prompt=prompt,
                system_message=system_message,
                temperature=temperature,
                max_tokens=max_tokens,
                stop_sequences=stop_sequences,
                **kwargs
            )
        
        # Prepare generation parameters
        temp = temperature if temperature is not None else self.temperature
        max_tok = max_tokens if max_tokens is not None else self.max_tokens
        
        # Log the request
        self.log_request(prompt, {
            "model": self.model_name,
            "temperature": temp,
            "max_tokens": max_tok,
            "stop_sequences": stop_sequences,
            "stream": True
        })
        
        try:
            # Record start time
            start_time = time.time()
            
            # Make streaming API call
            full_text = self.provider_client.generate(
                prompt=prompt,
                model=self.model_name,
                system_prompt=system_message,
                temperature=temp,
                max_tokens=max_tok,
                stream=True,
                callback=callback
            )
            
            # Record end time
            end_time = time.time()
            
            # Estimate token counts
            input_tokens = self.get_token_count(prompt)
            if system_message:
                input_tokens += self.get_token_count(system_message)
            output_tokens = self.get_token_count(full_text)
            total_tokens = input_tokens + output_tokens
            
            # Create result dictionary
            result = {
                "text": full_text,
                "model": self.model_name,
                "provider": "qwq",
                "tokens": {
                    "prompt": input_tokens,
                    "completion": output_tokens,
                    "total": total_tokens
                },
                "latency": end_time - start_time
            }
            
            # Log the response
            self.log_response(result)
            
            return result
        
        except Exception as e:
            error_msg = f"Error in streaming generation: {str(e)}"
            logger.error(error_msg)
            
            return {
                "text": "",
                "error": error_msg,
                "model": self.model_name,
                "provider": "qwq"
            } 