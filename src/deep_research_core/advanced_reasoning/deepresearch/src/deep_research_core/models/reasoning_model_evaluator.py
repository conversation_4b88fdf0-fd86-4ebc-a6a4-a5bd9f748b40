"""
Reasoning Model Evaluator.

This module provides functionality to evaluate the performance of reasoning models
on various tasks, benchmarks, and metrics.
"""

import os
import json
import time
import csv
import statistics
from typing import Dict, Any, List, Optional, Tuple, Union, Callable
from pathlib import Path

from .base_reasoning_model import BaseReasoningModel
from .model_selector import ModelSelector
from ..utils.structured_logging import get_logger

logger = get_logger(__name__)


class ReasoningModelEvaluator:
    """
    Evaluates the performance of reasoning models on various tasks and benchmarks.
    
    This class provides methods to run evaluations, compare models, generate 
    performance reports, and identify strengths and weaknesses of different models.
    """
    
    def __init__(
        self,
        models: Optional[Dict[str, BaseReasoningModel]] = None,
        benchmark_data_path: Optional[str] = None,
        results_dir: Optional[str] = None,
        cache_results: bool = True,
        verbose: bool = False,
    ):
        """
        Initialize the ReasoningModelEvaluator.
        
        Args:
            models: Dictionary of model_id -> model_instance
            benchmark_data_path: Path to benchmark datasets
            results_dir: Directory to store evaluation results
            cache_results: Whether to cache evaluation results
            verbose: Whether to print detailed information
        """
        self.models = models or {}
        self.cache_results = cache_results
        self.verbose = verbose
        
        # Set default benchmark data path if not provided
        if benchmark_data_path is None:
            home_dir = os.path.expanduser("~")
            self.benchmark_data_path = os.path.join(
                home_dir, ".deep_research_core", "benchmarks"
            )
        else:
            self.benchmark_data_path = benchmark_data_path
            
        # Set default results directory if not provided
        if results_dir is None:
            home_dir = os.path.expanduser("~")
            self.results_dir = os.path.join(
                home_dir, ".deep_research_core", "evaluation_results"
            )
        else:
            self.results_dir = results_dir
            
        # Ensure results directory exists
        os.makedirs(self.results_dir, exist_ok=True)
        
        # Cache for evaluation results
        self.results_cache = {}
        
        if self.verbose:
            logger.info(f"Initialized ReasoningModelEvaluator with {len(self.models)} models")
    
    def register_model(self, model_id: str, model: BaseReasoningModel) -> None:
        """
        Register a model with the evaluator.
        
        Args:
            model_id: Unique identifier for the model
            model: The model instance
        """
        self.models[model_id] = model
        if self.verbose:
            logger.info(f"Registered model: {model_id}")
    
    def get_available_models(self) -> List[str]:
        """
        Get a list of available model IDs.
        
        Returns:
            List of model IDs
        """
        return list(self.models.keys())
    
    def get_available_benchmarks(self) -> List[str]:
        """
        Get a list of available benchmark datasets.
        
        Returns:
            List of benchmark names
        """
        benchmarks = []
        
        if not os.path.exists(self.benchmark_data_path):
            return benchmarks
            
        for filename in os.listdir(self.benchmark_data_path):
            if filename.endswith(".json"):
                benchmarks.append(os.path.splitext(filename)[0])
                
        return benchmarks
    
    def load_benchmark(self, benchmark_name: str) -> List[Dict[str, Any]]:
        """
        Load a benchmark dataset from disk.
        
        Args:
            benchmark_name: Name of the benchmark dataset
            
        Returns:
            List of benchmark examples/test cases
        """
        if not benchmark_name.endswith(".json"):
            benchmark_name += ".json"
            
        benchmark_path = os.path.join(self.benchmark_data_path, benchmark_name)
        
        if not os.path.exists(benchmark_path):
            logger.error(f"Benchmark {benchmark_name} not found at {benchmark_path}")
            return []
            
        try:
            with open(benchmark_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError) as e:
            logger.error(f"Error loading benchmark {benchmark_name}: {str(e)}")
            return []
    
    def evaluate_model(
        self,
        model_id: str,
        benchmark_name: str,
        metrics: List[str] = ["accuracy", "latency", "token_usage"],
        max_examples: Optional[int] = None,
        custom_metrics: Optional[Dict[str, Callable]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Evaluate a model on a specific benchmark.
        
        Args:
            model_id: ID of the model to evaluate
            benchmark_name: Name of the benchmark dataset
            metrics: List of metrics to compute
            max_examples: Maximum number of examples to evaluate (None for all)
            custom_metrics: Dictionary of metric_name -> metric_function for custom metrics
            **kwargs: Additional parameters for model generation
            
        Returns:
            Dictionary with evaluation results
        """
        if model_id not in self.models:
            logger.error(f"Model {model_id} not found")
            return {}
            
        model = self.models[model_id]
        benchmark_data = self.load_benchmark(benchmark_name)
        
        if not benchmark_data:
            return {}
            
        # Check if cached results exist
        cache_key = json.dumps({
            "model_id": model_id,
            "benchmark_name": benchmark_name,
            "metrics": sorted(metrics),
            "max_examples": max_examples,
            **kwargs
        })
        
        if self.cache_results and cache_key in self.results_cache:
            return self.results_cache[cache_key]
            
        # Limit examples if needed
        if max_examples is not None and max_examples > 0:
            benchmark_data = benchmark_data[:max_examples]
            
        results = {
            "model_id": model_id,
            "benchmark_name": benchmark_name,
            "timestamp": time.time(),
            "metrics": {},
            "examples": len(benchmark_data)
        }
        
        # Initialize metrics
        accuracy_scores = []
        latency_scores = []
        token_usage = {"prompt": 0, "completion": 0, "total": 0}
        
        # Initialize custom metrics if provided
        custom_metric_scores = {metric: [] for metric in (custom_metrics or {})}
        
        # Process each example
        for i, example in enumerate(benchmark_data):
            prompt = example["prompt"]
            expected = example.get("expected")
            system_message = example.get("system_message")
            
            # Track start time
            start_time = time.time()
            
            # Generate response
            response = model.generate_with_metrics(
                prompt=prompt,
                system_message=system_message,
                **kwargs
            )
            
            # Calculate latency
            latency = time.time() - start_time
            latency_scores.append(latency)
            
            # Extract generated text
            generated = response.get("text", "")
            
            # Calculate accuracy if expected output is provided
            if expected is not None and "accuracy" in metrics:
                # Simple exact match accuracy - could be improved with semantic similarity
                accuracy = 1.0 if generated.strip() == expected.strip() else 0.0
                accuracy_scores.append(accuracy)
            
            # Track token usage
            if "token_usage" in response and "token_usage" in metrics:
                usage = response["token_usage"]
                token_usage["prompt"] += usage.get("prompt_tokens", 0)
                token_usage["completion"] += usage.get("completion_tokens", 0)
                token_usage["total"] += usage.get("total_tokens", 0)
            
            # Calculate custom metrics if provided
            if custom_metrics:
                for metric_name, metric_func in custom_metrics.items():
                    try:
                        score = metric_func(generated, expected, response)
                        custom_metric_scores[metric_name].append(score)
                    except Exception as e:
                        logger.error(f"Error calculating custom metric {metric_name}: {str(e)}")
            
            if self.verbose and (i + 1) % 5 == 0:
                logger.info(f"Processed {i + 1}/{len(benchmark_data)} examples for {model_id}")
        
        # Calculate average metrics
        if accuracy_scores:
            results["metrics"]["accuracy"] = {
                "mean": statistics.mean(accuracy_scores),
                "std": statistics.stdev(accuracy_scores) if len(accuracy_scores) > 1 else 0,
                "min": min(accuracy_scores),
                "max": max(accuracy_scores)
            }
        
        if latency_scores:
            results["metrics"]["latency"] = {
                "mean": statistics.mean(latency_scores),
                "std": statistics.stdev(latency_scores) if len(latency_scores) > 1 else 0,
                "min": min(latency_scores),
                "max": max(latency_scores),
                "total": sum(latency_scores)
            }
        
        if "token_usage" in metrics:
            results["metrics"]["token_usage"] = token_usage
        
        # Add custom metrics to results
        for metric_name, scores in custom_metric_scores.items():
            if scores:
                results["metrics"][metric_name] = {
                    "mean": statistics.mean(scores),
                    "std": statistics.stdev(scores) if len(scores) > 1 else 0,
                    "min": min(scores),
                    "max": max(scores)
                }
        
        # Cache results if caching is enabled
        if self.cache_results:
            self.results_cache[cache_key] = results
            
        # Save results to disk
        self._save_evaluation_results(model_id, benchmark_name, results)
        
        if self.verbose:
            logger.info(f"Evaluation completed for {model_id} on {benchmark_name}")
            
        return results
    
    def evaluate_all_models(
        self,
        benchmark_name: str,
        metrics: List[str] = ["accuracy", "latency", "token_usage"],
        max_examples: Optional[int] = None,
        **kwargs
    ) -> Dict[str, Dict[str, Any]]:
        """
        Evaluate all registered models on a specific benchmark.
        
        Args:
            benchmark_name: Name of the benchmark dataset
            metrics: List of metrics to compute
            max_examples: Maximum number of examples to evaluate (None for all)
            **kwargs: Additional parameters for model generation
            
        Returns:
            Dictionary of model_id -> evaluation results
        """
        results = {}
        
        for model_id in self.get_available_models():
            logger.info(f"Evaluating model {model_id} on benchmark {benchmark_name}")
            model_results = self.evaluate_model(
                model_id=model_id,
                benchmark_name=benchmark_name,
                metrics=metrics,
                max_examples=max_examples,
                **kwargs
            )
            results[model_id] = model_results
            
        return results
    
    def compare_models(
        self,
        benchmark_name: str,
        model_ids: Optional[List[str]] = None,
        metrics: List[str] = ["accuracy", "latency"],
        **kwargs
    ) -> Dict[str, Any]:
        """
        Compare multiple models on a specific benchmark.
        
        Args:
            benchmark_name: Name of the benchmark dataset
            model_ids: List of model IDs to compare (None for all)
            metrics: List of metrics to compare
            **kwargs: Additional parameters for evaluation
            
        Returns:
            Dictionary with comparison results
        """
        # Use all models if not specified
        if model_ids is None:
            model_ids = self.get_available_models()
            
        if not model_ids:
            logger.error("No models available for comparison")
            return {}
            
        # Evaluate all models
        all_results = {}
        for model_id in model_ids:
            all_results[model_id] = self.evaluate_model(
                model_id=model_id,
                benchmark_name=benchmark_name,
                metrics=metrics,
                **kwargs
            )
            
        # Build comparison result
        comparison = {
            "benchmark_name": benchmark_name,
            "models": model_ids,
            "metrics": {},
            "rankings": {},
            "timestamp": time.time()
        }
        
        # Compare each metric
        for metric in metrics:
            if metric == "token_usage":
                # For token usage, we compare total tokens
                comparison["metrics"][metric] = {
                    model_id: results["metrics"].get(metric, {}).get("total", 0)
                    for model_id, results in all_results.items()
                    if "metrics" in results and metric in results["metrics"]
                }
            else:
                # For other metrics, compare mean values
                comparison["metrics"][metric] = {
                    model_id: results["metrics"].get(metric, {}).get("mean", 0)
                    for model_id, results in all_results.items()
                    if "metrics" in results and metric in results["metrics"]
                }
        
        # Create rankings for each metric
        for metric in metrics:
            metric_values = comparison["metrics"].get(metric, {})
            
            if not metric_values:
                continue
                
            # For latency and token_usage, lower is better
            reverse = metric not in ["latency", "token_usage"]
            
            # Sort models by metric value
            ranked_models = sorted(
                metric_values.items(),
                key=lambda x: x[1],
                reverse=reverse
            )
            
            comparison["rankings"][metric] = [model_id for model_id, _ in ranked_models]
        
        # Save comparison results
        comparison_id = f"comparison_{int(time.time())}"
        comparison_path = os.path.join(
            self.results_dir, f"{comparison_id}_{benchmark_name}.json"
        )
        
        try:
            with open(comparison_path, "w", encoding="utf-8") as f:
                json.dump(comparison, f, indent=2)
        except IOError as e:
            logger.error(f"Error saving comparison results: {str(e)}")
            
        return comparison
    
    def identify_strengths_weaknesses(
        self,
        model_id: str,
        benchmarks: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Identify strengths and weaknesses of a model across benchmarks.
        
        Args:
            model_id: ID of the model to analyze
            benchmarks: List of benchmark names to include (None for all)
            **kwargs: Additional parameters for evaluation
            
        Returns:
            Dictionary with strengths and weaknesses
        """
        if model_id not in self.models:
            logger.error(f"Model {model_id} not found")
            return {}
            
        # Use all benchmarks if not specified
        if benchmarks is None:
            benchmarks = self.get_available_benchmarks()
            
        if not benchmarks:
            logger.error("No benchmarks available for analysis")
            return {}
            
        strengths = []
        weaknesses = []
        
        for benchmark_name in benchmarks:
            # Evaluate model on benchmark
            results = self.evaluate_model(
                model_id=model_id,
                benchmark_name=benchmark_name,
                **kwargs
            )
            
            if not results or "metrics" not in results:
                continue
                
            # Check accuracy
            if "accuracy" in results["metrics"]:
                accuracy = results["metrics"]["accuracy"].get("mean", 0)
                
                if accuracy >= 0.8:
                    strengths.append({
                        "benchmark": benchmark_name,
                        "metric": "accuracy",
                        "value": accuracy,
                        "notes": f"Strong performance on {benchmark_name} with {accuracy:.2%} accuracy"
                    })
                elif accuracy <= 0.5:
                    weaknesses.append({
                        "benchmark": benchmark_name,
                        "metric": "accuracy",
                        "value": accuracy,
                        "notes": f"Poor performance on {benchmark_name} with only {accuracy:.2%} accuracy"
                    })
            
            # Check latency
            if "latency" in results["metrics"]:
                latency = results["metrics"]["latency"].get("mean", 0)
                
                if latency <= 1.0:
                    strengths.append({
                        "benchmark": benchmark_name,
                        "metric": "latency",
                        "value": latency,
                        "notes": f"Fast response time on {benchmark_name} with {latency:.2f}s average latency"
                    })
                elif latency >= 5.0:
                    weaknesses.append({
                        "benchmark": benchmark_name,
                        "metric": "latency",
                        "value": latency,
                        "notes": f"Slow response time on {benchmark_name} with {latency:.2f}s average latency"
                    })
        
        analysis = {
            "model_id": model_id,
            "strengths": strengths,
            "weaknesses": weaknesses,
            "timestamp": time.time()
        }
        
        return analysis
    
    def recommend_best_model(
        self,
        benchmark_name: str,
        criteria: Dict[str, float] = None,
        constraints: Dict[str, Any] = None,
        **kwargs
    ) -> Tuple[Optional[str], Dict[str, Any]]:
        """
        Recommend the best model for a specific benchmark based on criteria.
        
        Args:
            benchmark_name: Name of the benchmark dataset
            criteria: Dictionary of metric -> weight for scoring (None for default)
            constraints: Dictionary of constraints (max_latency, min_accuracy, etc.)
            **kwargs: Additional parameters for evaluation
            
        Returns:
            Tuple of (best_model_id, evaluation_results)
        """
        # Use default criteria if none provided
        if criteria is None:
            criteria = {
                "accuracy": 0.7,
                "latency": 0.3
            }
        
        # Evaluate all models
        all_results = self.evaluate_all_models(
            benchmark_name=benchmark_name,
            metrics=list(criteria.keys()),
            **kwargs
        )
        
        if not all_results:
            logger.error(f"No evaluation results for benchmark {benchmark_name}")
            return None, {}
            
        best_model_id = None
        best_score = -float("inf")
        
        for model_id, results in all_results.items():
            if "metrics" not in results:
                continue
                
            # Check constraints if provided
            if constraints:
                constraint_violated = False
                
                for constraint_key, constraint_value in constraints.items():
                    if constraint_key.startswith("min_"):
                        metric = constraint_key[4:]  # Remove "min_" prefix
                        if metric in results["metrics"]:
                            metric_value = results["metrics"][metric].get("mean", 0)
                            if metric_value < constraint_value:
                                constraint_violated = True
                                break
                    elif constraint_key.startswith("max_"):
                        metric = constraint_key[4:]  # Remove "max_" prefix
                        if metric in results["metrics"]:
                            metric_value = results["metrics"][metric].get("mean", float("inf"))
                            if metric_value > constraint_value:
                                constraint_violated = True
                                break
                
                if constraint_violated:
                    if self.verbose:
                        logger.info(
                            f"Model {model_id} violated constraints for {benchmark_name}. "
                            "Skipping."
                        )
                    continue
            
            # Calculate score based on criteria
            score = 0.0
            
            for metric, weight in criteria.items():
                if metric not in results["metrics"]:
                    continue
                    
                # For latency, lower is better, so we invert the score
                if metric == "latency":
                    # Normalize latency: 1.0 for instant, approaching 0 for very slow
                    latency = results["metrics"][metric].get("mean", float("inf"))
                    if latency > 0:
                        normalized_latency = 1.0 / (1.0 + latency)
                    else:
                        normalized_latency = 1.0
                    
                    score += weight * normalized_latency
                else:
                    # For other metrics, higher is better
                    metric_value = results["metrics"][metric].get("mean", 0)
                    score += weight * metric_value
            
            if score > best_score:
                best_score = score
                best_model_id = model_id
        
        if best_model_id is None:
            logger.warning(f"No suitable model found for benchmark {benchmark_name}")
            return None, {}
            
        return best_model_id, all_results[best_model_id]
    
    def export_results_to_csv(
        self,
        results: Dict[str, Dict[str, Any]],
        output_path: str
    ) -> bool:
        """
        Export evaluation results to a CSV file.
        
        Args:
            results: Dictionary of model_id -> evaluation results
            output_path: Path to save the CSV file
            
        Returns:
            True if successful, False otherwise
        """
        if not results:
            logger.error("No results to export")
            return False
            
        try:
            with open(output_path, "w", newline="", encoding="utf-8") as f:
                # Determine all metrics present in the results
                all_metrics = set()
                for model_results in results.values():
                    if "metrics" in model_results:
                        all_metrics.update(model_results["metrics"].keys())
                
                # Create CSV writer
                fieldnames = ["model_id", "benchmark_name", "examples", "timestamp"]
                for metric in sorted(all_metrics):
                    if metric in ["latency", "accuracy"]:
                        fieldnames.extend([
                            f"{metric}_mean", f"{metric}_min", f"{metric}_max", f"{metric}_std"
                        ])
                    elif metric == "token_usage":
                        fieldnames.extend([
                            "token_usage_prompt", "token_usage_completion", "token_usage_total"
                        ])
                    else:
                        fieldnames.append(metric)
                        
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                
                # Write results for each model
                for model_id, model_results in results.items():
                    row = {
                        "model_id": model_id,
                        "benchmark_name": model_results.get("benchmark_name", ""),
                        "examples": model_results.get("examples", 0),
                        "timestamp": model_results.get("timestamp", "")
                    }
                    
                    # Add metrics
                    metrics = model_results.get("metrics", {})
                    for metric, values in metrics.items():
                        if metric in ["latency", "accuracy"]:
                            row[f"{metric}_mean"] = values.get("mean", "")
                            row[f"{metric}_min"] = values.get("min", "")
                            row[f"{metric}_max"] = values.get("max", "")
                            row[f"{metric}_std"] = values.get("std", "")
                        elif metric == "token_usage":
                            row["token_usage_prompt"] = values.get("prompt", 0)
                            row["token_usage_completion"] = values.get("completion", 0)
                            row["token_usage_total"] = values.get("total", 0)
                        else:
                            row[metric] = values.get("mean", "") if isinstance(values, dict) else values
                    
                    writer.writerow(row)
                    
            logger.info(f"Exported results to {output_path}")
            return True
        except IOError as e:
            logger.error(f"Error exporting results to CSV: {str(e)}")
            return False
    
    def _save_evaluation_results(
        self,
        model_id: str,
        benchmark_name: str,
        results: Dict[str, Any]
    ) -> bool:
        """
        Save evaluation results to disk.
        
        Args:
            model_id: ID of the evaluated model
            benchmark_name: Name of the benchmark dataset
            results: Evaluation results
            
        Returns:
            True if successful, False otherwise
        """
        results_path = os.path.join(
            self.results_dir, f"{model_id}_{benchmark_name}.json"
        )
        
        try:
            with open(results_path, "w", encoding="utf-8") as f:
                json.dump(results, f, indent=2)
            
            return True
        except IOError as e:
            logger.error(f"Error saving evaluation results: {str(e)}")
            return False 