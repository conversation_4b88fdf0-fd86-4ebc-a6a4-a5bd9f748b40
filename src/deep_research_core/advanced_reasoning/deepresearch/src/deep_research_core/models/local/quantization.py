"""
Quantization utilities for local models.
"""

import os
import logging
import torch
from typing import Dict, Any, List, Optional, Union, Callable

logger = logging.getLogger(__name__)

def get_quantization_config(method: str) -> Dict[str, Any]:
    """
    Get a quantization configuration for a specific method.
    
    Args:
        method: Quantization method ("int8", "int4", "none")
        
    Returns:
        Dictionary containing the quantization configuration
    """
    try:
        from transformers import BitsAndBytesConfig
    except ImportError:
        raise ImportError("Transformers package not installed. Please install it with 'pip install transformers'.")
    
    if method == "int8":
        try:
            import bitsandbytes as bnb
        except ImportError:
            raise ImportError("BitsAndBytes package not installed. Please install it with 'pip install bitsandbytes'.")
        
        return BitsAndBytesConfig(
            load_in_8bit=True,
            llm_int8_threshold=6.0,
            llm_int8_has_fp16_weight=False
        )
    elif method == "int4":
        try:
            import bitsandbytes as bnb
        except ImportError:
            raise ImportError("BitsAndBytes package not installed. Please install it with 'pip install bitsandbytes'.")
        
        return BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_use_double_quant=True,
            bnb_4bit_quant_type="nf4"
        )
    else:
        return None

def quantize_model(
    model_path: str,
    output_path: str,
    method: str = "int8",
    device: str = "cuda:0"
) -> bool:
    """
    Quantize a model and save it to disk.
    
    Args:
        model_path: Path to the model to quantize
        output_path: Path to save the quantized model
        method: Quantization method ("int8", "int4")
        device: Device to use for quantization
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Import required libraries
        try:
            from transformers import AutoModelForCausalLM, AutoTokenizer
        except ImportError:
            raise ImportError("Transformers package not installed. Please install it with 'pip install transformers'.")
        
        try:
            from optimum.gptq import GPTQQuantizer
        except ImportError:
            raise ImportError("Optimum package not installed. Please install it with 'pip install optimum'.")
        
        # Get quantization config
        quant_config = get_quantization_config(method)
        if not quant_config and method != "gptq":
            raise ValueError(f"Unsupported quantization method: {method}")
        
        logger.info(f"Quantizing model {model_path} with method {method}")
        
        # Load tokenizer
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        
        if method == "gptq":
            # Use GPTQ quantization
            model = AutoModelForCausalLM.from_pretrained(model_path, device_map=device)
            
            # Create quantizer
            quantizer = GPTQQuantizer(
                bits=4,
                dataset="c4",
                model_seqlen=2048,
                block_name_to_quantize="model.layers"
            )
            
            # Quantize model
            quantized_model = quantizer.quantize_model(model, tokenizer)
            
            # Save quantized model
            quantized_model.save_pretrained(output_path)
            tokenizer.save_pretrained(output_path)
            
        else:
            # Use BitsAndBytes quantization
            model = AutoModelForCausalLM.from_pretrained(
                model_path,
                quantization_config=quant_config,
                device_map=device
            )
            
            # Save quantized model
            model.save_pretrained(output_path)
            tokenizer.save_pretrained(output_path)
        
        logger.info(f"Model quantized and saved to {output_path}")
        return True
        
    except Exception as e:
        logger.error(f"Error quantizing model: {str(e)}")
        return False
