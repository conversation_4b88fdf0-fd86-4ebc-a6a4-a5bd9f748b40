"""
Local models module for Deep Research Core.

This module provides implementations for running local models including
open source models like Llama, Pythia, Falcon, etc.
"""

# Import local model utilities
from .model_loader import local_model_loader
from .quantization import get_quantization_config, quantize_model
from .base import BaseLocalModel
from .llama_model import LlamaModel
from .phi_model import PhiModel

__all__ = [
    'local_model_loader',
    'get_quantization_config',
    'quantize_model',
    "BaseLocalModel",
    "LlamaModel",
    "PhiModel",
]