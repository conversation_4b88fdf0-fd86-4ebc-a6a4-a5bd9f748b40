"""
Llama-3 local model implementation for Deep Research Core.

This module provides a specialized implementation for running the Llama-3
models locally, with support for various quantization options and optimizations.
"""

import os
import torch
from typing import Dict, Any, List, Optional, Union, Tuple

from .base import BaseLocalModel
from ..base_reasoning_model import BaseReasoningModel
from ...utils.structured_logging import get_logger
from ...config.models import LOCAL_MODELS

logger = get_logger(__name__)

class LlamaModel(BaseLocalModel):
    """
    Implementation of Llama-3 local model.
    
    This class provides methods to load, run and manage Llama-3 models locally.
    Supports different sizes (8B, 70B) with appropriate quantization.
    """
    
    def __init__(
        self,
        model_name: str = "llama-3-8b",
        quantization: Optional[str] = None,
        device: Optional[str] = None,
        adapter_name: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize the Llama-3 model.
        
        Args:
            model_name: Name of the model (must be in LOCAL_MODELS)
            quantization: Quantization method (int8, int4, none)
            device: Device to run the model on (cuda:0, cpu)
            adapter_name: Name of adapter to use
            **kwargs: Additional arguments for model initialization
        """
        super().__init__(model_name, quantization, device, adapter_name, **kwargs)
        
        # Llama-specific configurations
        self.supports_chat_format = True
        self.chat_template = "<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n\n{{system}}<|eot_id|><|start_header_id|>user<|end_header_id|>\n\n{{#each messages}}{{#ifEqual role \"user\"}}<|eot_id|><|start_header_id|>user<|end_header_id|>\n\n{{content}}{{/ifEqual}}{{#ifEqual role \"assistant\"}}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n{{content}}{{/ifEqual}}{{/each}}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n"
        
        # Default prompt templates
        self.default_system_prompt = "You are a helpful, harmless, and precise assistant that provides accurate and concise information."
        
        # Initialize auto_load parameter
        self.auto_load = kwargs.get("auto_load", True)
        self.is_loaded = False
        
        # Initialize model
        if self.auto_load:
            self.load_model()
    
    def load_model(self) -> bool:
        """
        Load the Llama model into memory.
        
        Returns:
            bool: True if successful, False otherwise
        """
        if self.is_loaded:
            logger.info(f"Model {self.model_name} already loaded")
            return True
        
        try:
            # Import required libraries
            try:
                from transformers import AutoModelForCausalLM, AutoTokenizer
                from transformers import BitsAndBytesConfig
            except ImportError:
                raise ImportError("Transformers package not installed. Please install it with 'pip install transformers'.")
            
            # Get model configuration
            model_config = LOCAL_MODELS.get(self.model_name, {})
            if "llama-3-70b" in self.model_name:
                model_path = model_config.get("model_path", "meta-llama/Meta-Llama-3-70B-Instruct")
            else:
                model_path = model_config.get("model_path", "meta-llama/Meta-Llama-3-8B-Instruct")
            
            # Set up quantization
            if self.quantization == "int8":
                try:
                    import bitsandbytes as bnb
                except ImportError:
                    raise ImportError("BitsAndBytes package not installed. Please install it with 'pip install bitsandbytes'.")
                
                quantization_config = BitsAndBytesConfig(
                    load_in_8bit=True,
                    llm_int8_threshold=6.0,
                    llm_int8_has_fp16_weight=False
                )
            elif self.quantization == "int4":
                try:
                    import bitsandbytes as bnb
                except ImportError:
                    raise ImportError("BitsAndBytes package not installed. Please install it with 'pip install bitsandbytes'.")
                
                quantization_config = BitsAndBytesConfig(
                    load_in_4bit=True,
                    bnb_4bit_compute_dtype=torch.float16,
                    bnb_4bit_use_double_quant=True,
                    bnb_4bit_quant_type="nf4"
                )
            else:
                quantization_config = None
            
            # Determine device to use
            if not self.device:
                if torch.cuda.is_available():
                    self.device = "cuda:0"
                else:
                    self.device = "cpu"
                    if model_config.get("requires_gpu", True):
                        logger.warning(f"Model {self.model_name} recommends GPU but running on CPU")
            
            # Load tokenizer
            logger.info(f"Loading tokenizer from {model_path}")
            self.tokenizer = AutoTokenizer.from_pretrained(model_path)
            
            # Check for token access
            if "meta-llama" in model_path:
                from huggingface_hub import login
                hf_token = os.environ.get("HUGGINGFACE_TOKEN")
                if not hf_token:
                    logger.warning("No HuggingFace token found in environment. Access to Meta-Llama models may be restricted.")
                else:
                    login(token=hf_token)
            
            # Load model
            logger.info(f"Loading model {self.model_name} from {model_path} with quantization={self.quantization} on device={self.device}")
            
            if quantization_config:
                self.model = AutoModelForCausalLM.from_pretrained(
                    model_path,
                    quantization_config=quantization_config,
                    device_map=self.device,
                    trust_remote_code=True
                )
            else:
                self.model = AutoModelForCausalLM.from_pretrained(
                    model_path,
                    device_map=self.device,
                    trust_remote_code=True
                )
            
            # Load adapter if specified
            if self.adapter_name:
                self._load_adapter()
            
            self.is_loaded = True
            self.context_length = model_config.get("context_length", 8192)
            
            logger.info(f"Successfully loaded {self.model_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load {self.model_name}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False
    
    def format_prompt(
        self,
        prompt: str,
        system_message: Optional[str] = None,
        chat_history: Optional[List[Dict[str, str]]] = None
    ) -> str:
        """
        Format prompt according to the model's expected format.
        
        Args:
            prompt: The user prompt
            system_message: Optional system message
            chat_history: Optional chat history
        
        Returns:
            Formatted prompt string
        """
        if not system_message:
            system_message = self.default_system_prompt
        
        # Format as a chat conversation
        if not chat_history:
            chat_history = []
        
        messages = [
            {"role": "system", "content": system_message}
        ]
        
        for message in chat_history:
            messages.append(message)
        
        # Add the current prompt
        messages.append({"role": "user", "content": prompt})
        
        # Format using the tokenizer's chat template if available
        if hasattr(self.tokenizer, "apply_chat_template"):
            formatted_prompt = self.tokenizer.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
        else:
            # Manual formatting as fallback for Llama-3
            formatted_text = f"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n\n{system_message}<|eot_id|>"
            
            for message in chat_history:
                role = message.get("role", "user")
                content = message.get("content", "")
                formatted_text += f"<|start_header_id|>{role}<|end_header_id|>\n\n{content}<|eot_id|>"
            
            formatted_text += f"<|start_header_id|>user<|end_header_id|>\n\n{prompt}<|eot_id|>"
            formatted_text += "<|start_header_id|>assistant<|end_header_id|>\n\n"
            formatted_prompt = formatted_text
        
        return formatted_prompt
    
    def generate(
        self,
        prompt: str,
        system_message: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        top_p: float = 0.9,
        top_k: int = 40,
        repetition_penalty: float = 1.1,
        chat_history: Optional[List[Dict[str, str]]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate text using the Llama model.
        
        Args:
            prompt: The prompt to generate from
            system_message: Optional system message
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            top_p: Top-p sampling parameter
            top_k: Top-k sampling parameter
            repetition_penalty: Penalty for repetition
            chat_history: Optional chat history
            **kwargs: Additional generation parameters
        
        Returns:
            Dictionary containing generated text and metadata
        """
        if not self.is_loaded:
            self.load_model()
        
        if not self.is_loaded:
            return {"text": "", "error": "Model failed to load"}
        
        try:
            # Format the prompt
            formatted_prompt = self.format_prompt(prompt, system_message, chat_history)
            
            # Tokenize the prompt
            inputs = self.tokenizer(formatted_prompt, return_tensors="pt").to(self.device)
            input_ids = inputs.input_ids
            
            # Check if prompt is too long
            if input_ids.shape[1] > self.context_length - max_tokens:
                logger.warning(f"Prompt too long ({input_ids.shape[1]} tokens). Truncating to fit within context length.")
                input_ids = input_ids[:, -(self.context_length - max_tokens):]
            
            # Start token counting for usage tracking
            prompt_tokens = input_ids.shape[1]
            
            # Generate
            start_time = torch.cuda.Event(enable_timing=True) if torch.cuda.is_available() else None
            end_time = torch.cuda.Event(enable_timing=True) if torch.cuda.is_available() else None
            
            if start_time:
                start_time.record()
            
            # Set up generation config
            generation_config = {
                "max_new_tokens": max_tokens,
                "temperature": temperature,
                "top_p": top_p,
                "top_k": top_k,
                "repetition_penalty": repetition_penalty,
                "do_sample": temperature > 0.0,
                "pad_token_id": self.tokenizer.eos_token_id
            }
            
            # Update with any additional kwargs
            generation_config.update(kwargs)
            
            # Generate
            with torch.no_grad():
                output = self.model.generate(
                    input_ids,
                    **generation_config
                )
            
            if end_time:
                end_time.record()
                torch.cuda.synchronize()
                generation_time = start_time.elapsed_time(end_time) / 1000  # convert to seconds
            else:
                generation_time = 0
            
            # Get generated text
            generated_ids = output[0, input_ids.shape[1]:]
            generated_text = self.tokenizer.decode(generated_ids, skip_special_tokens=True)
            
            # Count tokens for usage tracking
            completion_tokens = len(generated_ids)
            
            return {
                "text": generated_text,
                "token_usage": {
                    "prompt_tokens": prompt_tokens,
                    "completion_tokens": completion_tokens,
                    "total_tokens": prompt_tokens + completion_tokens
                },
                "metadata": {
                    "model": self.model_name,
                    "temperature": temperature,
                    "max_tokens": max_tokens,
                    "generation_time": generation_time,
                    "device": self.device,
                    "quantization": self.quantization
                }
            }
            
        except Exception as e:
            logger.error(f"Error generating text: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return {"text": "", "error": str(e)}
    
    def unload(self) -> bool:
        """
        Unload the model from memory.
        
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.is_loaded:
            return True
        
        try:
            # Delete model and tokenizer
            del self.model
            del self.tokenizer
            
            # Force garbage collection
            import gc
            gc.collect()
            
            # Clear CUDA cache if available
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            self.is_loaded = False
            logger.info(f"Successfully unloaded {self.model_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unload {self.model_name}: {str(e)}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the model.
        
        Returns:
            Dictionary containing model information
        """
        model_config = LOCAL_MODELS.get(self.model_name, {})
        
        return {
            "name": self.model_name,
            "provider": "local",
            "type": "llama",
            "quantization": self.quantization,
            "device": self.device,
            "is_loaded": self.is_loaded,
            "adapter": self.adapter_name,
            "context_length": model_config.get("context_length", 8192),
            "supports": model_config.get("supports", {})
        } 