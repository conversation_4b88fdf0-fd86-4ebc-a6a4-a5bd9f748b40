"""
Base class for local models in Deep Research Core.

This module defines the abstract base class for all local models,
providing a common interface for different implementations.
"""

import abc
from typing import Dict, Any, List, Optional, Union, Callable

from ...utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

class BaseLocalModel(abc.ABC):
    """
    Abstract base class for all local models.
    
    This class defines the common interface that all local models should follow,
    making it easier to switch between different implementations or add new ones.
    """
    
    def __init__(
        self,
        model_name: str,
        model_path: Optional[str] = None,
        device: str = "cpu",
        quantization: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize the BaseLocalModel.
        
        Args:
            model_name: Name of the model
            model_path: Path to the model files (if None, will download)
            device: Device to run the model on ("cpu", "cuda", "mps")
            quantization: Quantization method to use (None, "int8", "int4")
            **kwargs: Additional implementation-specific arguments
                auto_load: Whether to automatically load the model (default: True)
        """
        self.model_name = model_name
        self.model_path = model_path
        self.device = device
        self.quantization = quantization
        self.model = None
        self.tokenizer = None
        self.auto_load = kwargs.get("auto_load", True)
        self.is_loaded = False
        
    @abc.abstractmethod
    def load(self) -> bool:
        """
        Load the model and tokenizer.
        
        Returns:
            bool: True if successful, False otherwise
        """
        pass
    
    @abc.abstractmethod
    def generate(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        stream: bool = False,
        **kwargs
    ) -> Union[str, Callable]:
        """
        Generate text based on the prompt.
        
        Args:
            prompt: The prompt to generate text from
            system_prompt: Optional system prompt
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            stream: Whether to stream the response
            **kwargs: Additional implementation-specific arguments
            
        Returns:
            Generated text or a generator function if stream=True
        """
        pass
    
    @abc.abstractmethod
    def get_embedding(
        self,
        text: str,
        **kwargs
    ) -> List[float]:
        """
        Get embedding for the given text.
        
        Args:
            text: The text to get embedding for
            **kwargs: Additional implementation-specific arguments
            
        Returns:
            Embedding as a list of floats
        """
        pass
    
    @abc.abstractmethod
    def unload(self) -> bool:
        """
        Unload the model and release resources.
        
        Returns:
            bool: True if successful, False otherwise
        """
        pass
