"""
Local model loader for Deep Research Core.
"""

import os
import logging
import sys
from typing import Dict, Any, List, Optional, Union, Callable

# Try to import torch and check if it's the correct version
try:
    import torch
    # Check if this is actually PyTorch and not some other module named torch
    if not hasattr(torch, '__version__'):
        logger = logging.getLogger(__name__)
        logger.error("The imported 'torch' module does not appear to be PyTorch. "
                    "This could be a different module with the same name.")
        logger.error("Please make sure PyTorch is installed correctly: pip install torch")
        # We'll continue but mark torch as potentially problematic
        torch_problematic = True
    else:
        torch_problematic = False
except ImportError as e:
    logger = logging.getLogger(__name__)
    logger.error(f"Error importing PyTorch: {e}")
    logger.error("Please install PyTorch: pip install torch")
    # Create a dummy torch module to prevent crashes
    class DummyTorch:
        """Dummy torch module to prevent crashes when PyTorch is not available."""
        class cuda:
            @staticmethod
            def is_available():
                return False

            @staticmethod
            def device_count():
                return 0

            @staticmethod
            def get_device_name(i):
                return "No CUDA device available"

            @staticmethod
            def empty_cache():
                pass

    torch = DummyTorch()
    torch_problematic = True

from ...config.models import LOCAL_MODELS, MODEL_ADAPTERS

logger = logging.getLogger(__name__)

class LocalModelLoader:
    """
    Handles loading and running local models.
    """

    def __init__(self):
        """
        Initialize the local model loader.
        """
        self.available_models = LOCAL_MODELS
        self.available_adapters = MODEL_ADAPTERS
        self.loaded_models = {}
        self.loaded_adapters = {}

        # Check if PyTorch is problematic
        global torch_problematic
        if torch_problematic:
            logger.warning("PyTorch installation appears to be problematic. Some features may not work correctly.")
            logger.warning("Consider reinstalling PyTorch with: pip install torch")
            self.cuda_available = False
            self.cuda_device_count = 0
            self.cuda_device_names = []
            return

        # Check if CUDA is available
        self.cuda_available = False
        try:
            if hasattr(torch, 'cuda'):
                self.cuda_available = torch.cuda.is_available()
                if self.cuda_available:
                    self.cuda_device_count = torch.cuda.device_count()
                    self.cuda_device_names = [torch.cuda.get_device_name(i) for i in range(self.cuda_device_count)]
                    logger.info(f"CUDA available: {self.cuda_device_count} devices")
                    for i, name in enumerate(self.cuda_device_names):
                        logger.info(f"  Device {i}: {name}")
                else:
                    logger.warning("CUDA not available. Local models will run on CPU, which may be very slow.")
            else:
                logger.warning("PyTorch installation does not include CUDA support. Local models will run on CPU, which may be very slow.")
                self.cuda_device_count = 0
                self.cuda_device_names = []
        except Exception as e:
            logger.warning(f"Error checking CUDA availability: {str(e)}. Local models will run on CPU, which may be very slow.")
            self.cuda_device_count = 0
            self.cuda_device_names = []

    def get_available_models(self) -> List[str]:
        """
        Returns a list of available local models.
        """
        return list(self.available_models.keys())

    def get_available_adapters(self) -> List[str]:
        """
        Returns a list of available model adapters.
        """
        return list(self.available_adapters.keys())

    def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """
        Get information about a specific model.

        Args:
            model_name: Name of the model

        Returns:
            Dictionary containing model information
        """
        if model_name in self.available_models:
            model_info = self.available_models[model_name].copy()
            model_info["name"] = model_name
            model_info["provider"] = "local"
            return model_info

        raise ValueError(f"Model {model_name} not found in local models")

    def load_model(
        self,
        model_name: str,
        adapter_name: Optional[str] = None,
        quantization: Optional[str] = None,
        device: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Load a local model.

        Args:
            model_name: Name of the model to load
            adapter_name: Name of the adapter to use (optional)
            quantization: Quantization method to use (optional, overrides model config)
            device: Device to load the model on (optional)

        Returns:
            Dictionary containing the loaded model and tokenizer
        """
        # Check if PyTorch is problematic
        global torch_problematic
        if torch_problematic:
            error_msg = "Cannot load model: PyTorch installation is problematic."
            logger.error(error_msg)
            logger.error("Please reinstall PyTorch with: pip install torch")
            raise RuntimeError(error_msg)

        try:
            # Check if the model is already loaded
            if model_name in self.loaded_models:
                logger.info(f"Model {model_name} already loaded")
                return self.loaded_models[model_name]

            # Check if the model exists
            if model_name not in self.available_models:
                raise ValueError(f"Model {model_name} not found in local models")

            # Get model config
            model_config = self.available_models[model_name]
            model_path = model_config["model_path"]

            # Determine quantization method
            quant_method = quantization or model_config.get("quantization", "none")

            # Determine device
            if device:
                target_device = device
            elif self.cuda_available and hasattr(torch, 'cuda') and model_config.get("requires_gpu", False):
                target_device = "cuda:0"
            else:
                target_device = "cpu"

            logger.info(f"Loading model {model_name} from {model_path}")
            logger.info(f"  Quantization: {quant_method}")
            logger.info(f"  Device: {target_device}")

            # Import required libraries
            try:
                from transformers import AutoModelForCausalLM, AutoTokenizer
                from transformers import BitsAndBytesConfig
            except ImportError:
                raise ImportError("Transformers package not installed. Please install it with 'pip install transformers'.")

            # Set up quantization config
            if quant_method == "int8":
                try:
                    import bitsandbytes as bnb
                except ImportError:
                    raise ImportError("BitsAndBytes package not installed. Please install it with 'pip install bitsandbytes'.")

                quantization_config = BitsAndBytesConfig(
                    load_in_8bit=True,
                    llm_int8_threshold=6.0,
                    llm_int8_has_fp16_weight=False
                )
            elif quant_method == "int4":
                try:
                    import bitsandbytes as bnb
                except ImportError:
                    raise ImportError("BitsAndBytes package not installed. Please install it with 'pip install bitsandbytes'.")

                quantization_config = BitsAndBytesConfig(
                    load_in_4bit=True,
                    bnb_4bit_compute_dtype=torch.float16,
                    bnb_4bit_use_double_quant=True,
                    bnb_4bit_quant_type="nf4"
                )
            else:
                quantization_config = None

            # Load tokenizer
            tokenizer = AutoTokenizer.from_pretrained(model_path)

            # Load model
            if quantization_config:
                model = AutoModelForCausalLM.from_pretrained(
                    model_path,
                    quantization_config=quantization_config,
                    device_map=target_device,
                    trust_remote_code=True
                )
            else:
                model = AutoModelForCausalLM.from_pretrained(
                    model_path,
                    device_map=target_device,
                    trust_remote_code=True
                )

            # Load adapter if specified
            if adapter_name:
                if adapter_name not in self.available_adapters:
                    raise ValueError(f"Adapter {adapter_name} not found")

                adapter_config = self.available_adapters[adapter_name]

                # Check if the adapter is compatible with the model
                if model_name not in adapter_config.get("compatible_models", []):
                    raise ValueError(f"Adapter {adapter_name} is not compatible with model {model_name}")

                adapter_path = adapter_config["adapter_path"]
                adapter_type = adapter_config.get("adapter_type", "lora")

                logger.info(f"Loading adapter {adapter_name} from {adapter_path}")
                logger.info(f"  Adapter type: {adapter_type}")

                # Load the adapter
                if adapter_type == "lora" or adapter_type == "qlora":
                    try:
                        from peft import PeftModel
                    except ImportError:
                        raise ImportError("PEFT package not installed. Please install it with 'pip install peft'.")

                    model = PeftModel.from_pretrained(model, adapter_path)
                    logger.info(f"Adapter {adapter_name} loaded successfully")
                else:
                    raise ValueError(f"Unsupported adapter type: {adapter_type}")

            # Store the loaded model
            self.loaded_models[model_name] = {
                "model": model,
                "tokenizer": tokenizer,
                "config": model_config,
                "adapter": adapter_name
            }

            logger.info(f"Model {model_name} loaded successfully")

            return self.loaded_models[model_name]

        except Exception as e:
            logger.error(f"Error loading model {model_name}: {str(e)}")
            raise

    def generate(
        self,
        prompt: str,
        model_name: str,
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        adapter_name: Optional[str] = None
    ) -> str:
        """
        Generate text using a local model.

        Args:
            prompt: The prompt to generate from
            model_name: The model to use
            system_prompt: System prompt to use
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            adapter_name: Name of the adapter to use (optional)

        Returns:
            Generated text
        """
        # Check if PyTorch is problematic
        global torch_problematic
        if torch_problematic:
            error_msg = "Cannot generate text: PyTorch installation is problematic."
            logger.error(error_msg)
            logger.error("Please reinstall PyTorch with: pip install torch")
            raise RuntimeError(error_msg)

        try:
            # Load the model if not already loaded
            if model_name not in self.loaded_models:
                self.load_model(model_name, adapter_name)

            # Get the model and tokenizer
            model_data = self.loaded_models[model_name]
            model = model_data["model"]
            tokenizer = model_data["tokenizer"]

            # Prepare the prompt
            if system_prompt:
                # Format with system prompt
                full_prompt = f"<|system|>\n{system_prompt}\n<|user|>\n{prompt}\n<|assistant|>"
            else:
                # Format without system prompt
                full_prompt = f"<|user|>\n{prompt}\n<|assistant|>"

            # Tokenize the prompt
            inputs = tokenizer(full_prompt, return_tensors="pt")
            input_ids = inputs["input_ids"].to(model.device)

            # Generate
            with torch.no_grad():
                outputs = model.generate(
                    input_ids,
                    max_new_tokens=max_tokens,
                    temperature=temperature,
                    top_p=0.95,
                    do_sample=temperature > 0,
                    pad_token_id=tokenizer.eos_token_id
                )

            # Decode the output
            output_text = tokenizer.decode(outputs[0][input_ids.shape[1]:], skip_special_tokens=True)

            return output_text

        except Exception as e:
            logger.error(f"Error generating text with model {model_name}: {str(e)}")
            raise

    def unload_model(self, model_name: str) -> bool:
        """
        Unload a model from memory.

        Args:
            model_name: Name of the model to unload

        Returns:
            True if successful, False otherwise
        """
        try:
            if model_name in self.loaded_models:
                # Get the model data
                model_data = self.loaded_models[model_name]
                model = model_data["model"]

                # Delete the model
                del model
                del self.loaded_models[model_name]

                # Force garbage collection
                import gc
                gc.collect()

                # Clear CUDA cache if available
                if self.cuda_available and hasattr(torch, 'cuda'):
                    try:
                        torch.cuda.empty_cache()
                    except Exception as e:
                        logger.warning(f"Error clearing CUDA cache: {str(e)}")

                logger.info(f"Model {model_name} unloaded successfully")
                return True
            else:
                logger.warning(f"Model {model_name} not loaded")
                return False

        except Exception as e:
            logger.error(f"Error unloading model {model_name}: {str(e)}")
            return False

# Create a singleton instance
local_model_loader = LocalModelLoader()
