"""Models module for Deep Research Core."""

# Import base classes
from .api.base import BaseAPIProvider
from .base_reasoning_model import BaseReasoningModel
# from .local.base import BaseLocalModel

# Import reasoning models
from .gpt_o1 import GPTO1Model
from .deepseek_r1 import DeepseekR1Model
from .qwq_32b import QwQ32BModel

# Import model utilities
from .model_selector import ModelSelector
from .reasoning_model_evaluator import ReasoningModelEvaluator

# Import embedding models
try:
    from .embeddings import (
        BaseEmbeddingModel,
        get_embedding_model,
        list_available_models
    )
except ImportError as e:
    # Embedding models might be optional
    import logging
    logging.getLogger(__name__).error(f"Error importing embedding models: {str(e)}")

# Import API providers - temporarily commented out until modules are implemented
try:
    # from .api.openai import OpenAIProvider, openai_provider
    # from .api.anthropic import AnthropicProvider, anthropic_provider
    from .api.openrouter import OpenRouterProvider
    # from .api.google import GoogleProvider, google_provider
    # from .api.deepseek import DeepSeekProvider, deepseek_provider
except ImportError as e:
    # Some providers might be optional
    import logging
    logging.getLogger(__name__).error(f"Error importing API modules: {str(e)}")

# Import local model utilities
try:
    from .local.model_loader import (
        load_model,
        load_tokenizer,
        get_available_models,
        local_model_loader
    )
    from .local.quantization import (
        get_quantization_config,
        quantize_model
    )
except ImportError:
    # Local models might be optional
    pass

__all__ = [
    # Base classes
    'BaseAPIProvider',
    'BaseReasoningModel',
    # 'BaseLocalModel',

    # Reasoning models
    'GPTO1Model',
    'DeepseekR1Model',
    'QwQ32BModel',

    # Model utilities
    'ModelSelector',
    'ReasoningModelEvaluator',

    # Embedding models
    'BaseEmbeddingModel',
    'get_embedding_model',
    'list_available_models',

    # API providers - temporarily commented out until modules are implemented
    # 'OpenAIProvider',
    # 'AnthropicProvider',
    'OpenRouterProvider',
    # 'GoogleProvider',
    # 'DeepSeekProvider',

    # Provider instances - temporarily commented out until modules are implemented
    # 'openai_provider',
    # 'anthropic_provider',
    # 'openrouter_provider',
    # 'google_provider',
    # 'deepseek_provider',

    # Local model utilities - temporarily commented out until modules are implemented
    # 'load_model',
    # 'load_tokenizer',
    # 'get_available_models',
    # 'local_model_loader',
    # 'get_quantization_config',
    # 'quantize_model'
]
