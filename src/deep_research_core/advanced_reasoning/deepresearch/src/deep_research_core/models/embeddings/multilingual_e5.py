"""
Multilingual E5 embedding model.

This module implements the Multilingual E5 embedding model,
which supports multiple languages including Vietnamese.
"""

import os
from typing import List, Union, Dict, Any, Optional

import numpy as np
import torch
from transformers import AutoTokenizer, AutoModel

from .base import BaseEmbeddingModel
from ...utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

class MultilingualE5(BaseEmbeddingModel):
    """
    Multilingual E5 embedding model.
    
    This class implements the Multilingual E5 embedding model,
    which supports multiple languages including Vietnamese.
    """
    
    def __init__(
        self,
        model_name: str = "intfloat/multilingual-e5-large",
        device: Optional[str] = None,
        max_length: int = 512,
        **kwargs
    ):
        """
        Initialize the MultilingualE5 model.
        
        Args:
            model_name: Name of the model to use
            device: Device to use (cpu, cuda, etc.)
            max_length: Maximum sequence length
            **kwargs: Additional implementation-specific arguments
        """
        super().__init__(**kwargs)
        
        self.model_name = model_name
        self.max_length = max_length
        
        # Determine device
        if device is None:
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            self.device = device
        
        # Load model and tokenizer
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.model = AutoModel.from_pretrained(model_name)
            self.model.to(self.device)
            
            # Set model to evaluation mode
            self.model.eval()
            
            logger.info(f"Loaded MultilingualE5 model {model_name} on {self.device}")
        except Exception as e:
            logger.error(f"Error loading MultilingualE5 model {model_name}: {str(e)}")
            raise
    
    def encode(
        self,
        text: Union[str, List[str]],
        **kwargs
    ) -> Union[np.ndarray, List[np.ndarray]]:
        """
        Encode text into embedding vectors.
        
        Args:
            text: Text to encode (string or list of strings)
            **kwargs: Additional implementation-specific arguments
            
        Returns:
            Embedding vector(s) as numpy array(s)
        """
        # Convert single string to list
        if isinstance(text, str):
            text = [text]
        
        # Prepare inputs
        inputs = []
        for t in text:
            # Add prefix for query
            if kwargs.get("is_query", False):
                t = f"query: {t}"
            # Add prefix for passage
            elif kwargs.get("is_passage", False):
                t = f"passage: {t}"
            inputs.append(t)
        
        # Tokenize
        encoded_inputs = self.tokenizer(
            inputs,
            padding=True,
            truncation=True,
            max_length=self.max_length,
            return_tensors="pt"
        )
        
        # Move to device
        encoded_inputs = {k: v.to(self.device) for k, v in encoded_inputs.items()}
        
        # Compute embeddings
        with torch.no_grad():
            outputs = self.model(**encoded_inputs)
            embeddings = outputs.last_hidden_state[:, 0]  # CLS token
            
            # Normalize embeddings
            embeddings = torch.nn.functional.normalize(embeddings, p=2, dim=1)
        
        # Convert to numpy
        embeddings = embeddings.cpu().numpy()
        
        # Return single embedding if input was a single string
        if len(text) == 1:
            return embeddings[0]
        
        return embeddings
    
    def get_dimension(self) -> int:
        """
        Get the dimension of the embedding vectors.
        
        Returns:
            Dimension of the embedding vectors
        """
        if self.model_name.endswith("small"):
            return 384
        elif self.model_name.endswith("base"):
            return 768
        elif self.model_name.endswith("large"):
            return 1024
        else:
            # Default dimension
            return 768
