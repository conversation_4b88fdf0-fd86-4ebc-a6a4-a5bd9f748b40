"""
Base class for embedding models.

This module defines the abstract base class for all embedding models,
providing a common interface for different implementations.
"""

import abc
from typing import List, Union, Dict, Any, Optional

import numpy as np

from ...utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

class BaseEmbeddingModel(abc.ABC):
    """
    Abstract base class for all embedding models.

    This class defines the common interface that all embedding models should follow,
    making it easier to switch between different implementations or add new ones.
    """

    def __init__(self, **kwargs):
        """
        Initialize the BaseEmbeddingModel.

        Args:
            **kwargs: Additional implementation-specific arguments
        """
        pass

    @abc.abstractmethod
    def encode(
        self,
        text: Union[str, List[str]],
        **kwargs
    ) -> Union[np.ndarray, List[np.ndarray]]:
        """
        Encode text into embedding vectors.

        Args:
            text: Text to encode (string or list of strings)
            **kwargs: Additional implementation-specific arguments

        Returns:
            Embedding vector(s) as numpy array(s)
        """
        pass

    @abc.abstractmethod
    def get_dimension(self) -> int:
        """
        Get the dimension of the embedding vectors.

        Returns:
            Dimension of the embedding vectors
        """
        pass


def get_embedding_model(model_name: str, **kwargs) -> BaseEmbeddingModel:
    """
    Get an embedding model by name.

    Args:
        model_name: Name of the embedding model
        **kwargs: Additional arguments to pass to the model constructor

    Returns:
        An embedding model instance
    """
    # Import here to avoid circular imports
    from .multilingual_e5 import MultilingualE5
    from .openai_embeddings import OpenAIEmbeddings
    from .vietnamese_phobert import VietnamesePhoBERT
    from .vietnamese_viebert import VietnameseVieBERT
    from .vietnamese_xlm_roberta import VietnameseXLMRoBERTa
    from .vietnamese_multilingual_e5 import VietnameseMultilingualE5

    # Map model names to model classes
    model_map = {
        # Multilingual E5 models
        "multilingual-e5-small": lambda: MultilingualE5(
            model_name="intfloat/multilingual-e5-small", **kwargs
        ),
        "multilingual-e5-base": lambda: MultilingualE5(
            model_name="intfloat/multilingual-e5-base", **kwargs
        ),
        "multilingual-e5-large": lambda: MultilingualE5(
            model_name="intfloat/multilingual-e5-large", **kwargs
        ),

        # OpenAI models
        "openai-ada-002": lambda: OpenAIEmbeddings(
            model_name="text-embedding-ada-002", **kwargs
        ),
        "openai-3-small": lambda: OpenAIEmbeddings(
            model_name="text-embedding-3-small", **kwargs
        ),
        "openai-3-large": lambda: OpenAIEmbeddings(
            model_name="text-embedding-3-large", **kwargs
        ),

        # Vietnamese PhoBERT models
        "phobert-base": lambda: VietnamesePhoBERT(
            model_name="vinai/phobert-base", **kwargs
        ),
        "phobert-large": lambda: VietnamesePhoBERT(
            model_name="vinai/phobert-large", **kwargs
        ),

        # Vietnamese VieBERT models
        "viebert-base": lambda: VietnameseVieBERT(
            model_name="FPTAI/viebert-base-cased", **kwargs
        ),

        # Vietnamese XLM-RoBERTa models
        "xlm-roberta-vi-base": lambda: VietnameseXLMRoBERTa(
            model_name="xlm-roberta-base", **kwargs
        ),
        "xlm-roberta-vi-large": lambda: VietnameseXLMRoBERTa(
            model_name="xlm-roberta-large", **kwargs
        ),

        # Vietnamese Multilingual-E5 models
        "vietnamese-multilingual-e5-base": lambda: VietnameseMultilingualE5(
            model_name="intfloat/multilingual-e5-base", **kwargs
        ),
        "vietnamese-multilingual-e5-large": lambda: VietnameseMultilingualE5(
            model_name="intfloat/multilingual-e5-large", **kwargs
        )
    }

    # Get the model constructor
    model_constructor = model_map.get(model_name)

    if model_constructor:
        try:
            return model_constructor()
        except Exception as e:
            logger.error(f"Error creating embedding model {model_name}: {str(e)}")
            # Fall back to multilingual-e5-small
            logger.info("Falling back to multilingual-e5-small")
            return MultilingualE5(model_name="intfloat/multilingual-e5-small", **kwargs)
    else:
        logger.warning(f"Unknown embedding model: {model_name}.")
        logger.info("Falling back to multilingual-e5-small")
        return MultilingualE5(model_name="intfloat/multilingual-e5-small", **kwargs)
