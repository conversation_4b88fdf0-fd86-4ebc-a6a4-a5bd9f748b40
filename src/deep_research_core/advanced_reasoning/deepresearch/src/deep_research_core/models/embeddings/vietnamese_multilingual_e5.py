"""
Vietnamese Multilingual-E5 embedding model.

This module implements the Vietnamese Multilingual-E5 embedding model,
which is a multilingual model with good support for Vietnamese.
"""

import os
from typing import List, Union, Dict, Any, Optional

import numpy as np
import torch
from transformers import AutoTokenizer, AutoModel

from .base import BaseEmbeddingModel
from ...utils.structured_logging import get_logger
from ...utils.vietnamese_text_processor import VietnameseTextProcessor

# Create a logger
logger = get_logger(__name__)

class VietnameseMultilingualE5(BaseEmbeddingModel):
    """
    Vietnamese Multilingual-E5 embedding model.
    
    This class implements the Vietnamese Multilingual-E5 embedding model,
    which is a multilingual model with good support for Vietnamese.
    """
    
    def __init__(
        self,
        model_name: str = "intfloat/multilingual-e5-base",
        device: Optional[str] = None,
        max_length: int = 512,
        use_preprocessing: bool = True,
        **kwargs
    ):
        """
        Initialize the VietnameseMultilingualE5 model.
        
        Args:
            model_name: Name of the model to use (intfloat/multilingual-e5-base, intfloat/multilingual-e5-large)
            device: Device to use (cpu, cuda, etc.)
            max_length: Maximum sequence length
            use_preprocessing: Whether to use Vietnamese-specific preprocessing
            **kwargs: Additional implementation-specific arguments
        """
        super().__init__(**kwargs)
        
        self.model_name = model_name
        self.max_length = max_length
        self.use_preprocessing = use_preprocessing
        
        # Determine device
        if device is None:
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
            if hasattr(torch, "has_mps") and torch.backends.mps.is_available():
                self.device = "mps"  # Apple Silicon GPU
        else:
            self.device = device
        
        # Initialize Vietnamese text processor
        if self.use_preprocessing:
            self.text_processor = VietnameseTextProcessor()
        
        # Initialize tokenizer and model
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            self.model = AutoModel.from_pretrained(self.model_name).to(self.device)
            self.model.eval()
            logger.info(f"Initialized VietnameseMultilingualE5 with model {model_name} on {self.device}")
        except Exception as e:
            logger.error(f"Error initializing VietnameseMultilingualE5: {str(e)}")
            raise
    
    def _preprocess_text(self, text: str) -> str:
        """
        Preprocess text for Multilingual-E5.
        
        Args:
            text: Text to preprocess
            
        Returns:
            Preprocessed text
        """
        if self.use_preprocessing:
            # For Multilingual-E5, we don't need word segmentation
            # But we can still normalize diacritics
            return self.text_processor.normalize_diacritics(text)
        return text
    
    def encode(
        self,
        text: Union[str, List[str]],
        **kwargs
    ) -> Union[np.ndarray, List[np.ndarray]]:
        """
        Encode text into embedding vectors.
        
        Args:
            text: Text to encode (string or list of strings)
            **kwargs: Additional implementation-specific arguments
            
        Returns:
            Embedding vector(s) as numpy array(s)
        """
        # Convert single string to list
        is_single = isinstance(text, str)
        if is_single:
            text = [text]
        
        # Preprocess text
        preprocessed_text = [self._preprocess_text(t) for t in text]
        
        # Add prefix for Multilingual-E5
        # For text embedding, we use "query: " prefix
        prefixed_text = [f"query: {t}" for t in preprocessed_text]
        
        try:
            # Tokenize text
            encoded_inputs = self.tokenizer(
                prefixed_text,
                padding=True,
                truncation=True,
                max_length=self.max_length,
                return_tensors="pt"
            )
            
            # Move to device
            encoded_inputs = {k: v.to(self.device) for k, v in encoded_inputs.items()}
            
            # Compute embeddings
            with torch.no_grad():
                outputs = self.model(**encoded_inputs)
                # Use CLS token for sentence embeddings (first token)
                embeddings = outputs.last_hidden_state[:, 0]
                
                # Normalize embeddings
                embeddings = torch.nn.functional.normalize(embeddings, p=2, dim=1)
            
            # Convert to numpy
            embeddings = embeddings.cpu().numpy()
            
            # Return single embedding if input was a single string
            if is_single:
                return embeddings[0]
            
            return embeddings
            
        except Exception as e:
            logger.error(f"Error generating embeddings with VietnameseMultilingualE5: {str(e)}")
            # Return zero embeddings as fallback
            dim = self.get_dimension()
            if is_single:
                return np.zeros(dim)
            return [np.zeros(dim) for _ in text]
    
    def get_dimension(self) -> int:
        """
        Get the dimension of the embedding vectors.
        
        Returns:
            Dimension of the embedding vectors
        """
        # Dimensions for different models
        dimensions = {
            "intfloat/multilingual-e5-small": 384,
            "intfloat/multilingual-e5-base": 768,
            "intfloat/multilingual-e5-large": 1024
        }
        
        return dimensions.get(self.model_name, 768)
