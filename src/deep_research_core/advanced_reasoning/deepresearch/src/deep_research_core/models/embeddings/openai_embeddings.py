"""
OpenAI embeddings model.

This module implements the OpenAI embeddings model,
which provides high-quality embeddings for various languages.
"""

import os
from typing import List, Union, Dict, Any, Optional

import numpy as np

from .base import BaseEmbeddingModel
from ...utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

class OpenAIEmbeddings(BaseEmbeddingModel):
    """
    OpenAI embeddings model.
    
    This class implements the OpenAI embeddings model,
    which provides high-quality embeddings for various languages.
    """
    
    def __init__(
        self,
        model_name: str = "text-embedding-3-small",
        api_key: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize the OpenAIEmbeddings model.
        
        Args:
            model_name: Name of the model to use
            api_key: OpenAI API key (if None, will use OPENAI_API_KEY environment variable)
            **kwargs: Additional implementation-specific arguments
        """
        super().__init__(**kwargs)
        
        self.model_name = model_name
        
        # Get API key
        self.api_key = api_key or os.environ.get("OPENAI_API_KEY")
        if not self.api_key:
            logger.warning("OpenAI API key not found. Set OPENAI_API_KEY environment variable.")
        
        # Import OpenAI library
        try:
            import openai
            self.client = openai.OpenAI(api_key=self.api_key)
            logger.info(f"Initialized OpenAIEmbeddings with model {model_name}")
        except ImportError:
            logger.error("OpenAI library not found. Install it with 'pip install openai'.")
            raise
    
    def encode(
        self,
        text: Union[str, List[str]],
        **kwargs
    ) -> Union[np.ndarray, List[np.ndarray]]:
        """
        Encode text into embedding vectors.
        
        Args:
            text: Text to encode (string or list of strings)
            **kwargs: Additional implementation-specific arguments
            
        Returns:
            Embedding vector(s) as numpy array(s)
        """
        # Convert single string to list
        if isinstance(text, str):
            text = [text]
        
        try:
            # Call OpenAI API
            response = self.client.embeddings.create(
                model=self.model_name,
                input=text
            )
            
            # Extract embeddings
            embeddings = [np.array(item.embedding) for item in response.data]
            
            # Return single embedding if input was a single string
            if len(text) == 1:
                return embeddings[0]
            
            return embeddings
            
        except Exception as e:
            logger.error(f"Error generating embeddings with OpenAI: {str(e)}")
            # Return zero embeddings as fallback
            dim = self.get_dimension()
            if len(text) == 1:
                return np.zeros(dim)
            return [np.zeros(dim) for _ in text]
    
    def get_dimension(self) -> int:
        """
        Get the dimension of the embedding vectors.
        
        Returns:
            Dimension of the embedding vectors
        """
        # Dimensions for different models
        dimensions = {
            "text-embedding-ada-002": 1536,
            "text-embedding-3-small": 1536,
            "text-embedding-3-large": 3072
        }
        
        return dimensions.get(self.model_name, 1536)
