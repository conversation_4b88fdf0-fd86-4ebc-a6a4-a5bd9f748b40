"""
Embedding models module.

This module provides implementations of embedding models,
which convert text into vector representations.
"""

from .base import BaseEmbeddingModel, get_embedding_model
from .multilingual_e5 import MultilingualE5
from .openai_embeddings import OpenAIEmbeddings
from .vietnamese_phobert import VietnamesePhoBERT
from .vietnamese_viebert import VietnameseVieBERT
from .vietnamese_xlm_roberta import VietnameseXLMRoBERTa
from .vietnamese_multilingual_e5 import VietnameseMultilingualE5

def list_available_models():
    """
    List available embedding models.

    Returns:
        List of available embedding model names
    """
    return [
        "multilingual-e5",
        "openai",
        "vietnamese-phobert",
        "vietnamese-viebert",
        "vietnamese-xlm-roberta",
        "vietnamese-multilingual-e5"
    ]

__all__ = [
    'BaseEmbeddingModel',
    'get_embedding_model',
    'list_available_models',
    'MultilingualE5',
    'OpenAIEmbeddings',
    'VietnamesePhoBERT',
    'VietnameseVieBERT',
    'VietnameseXLMRoBERTa',
    'VietnameseMultilingualE5'
]
