"""
Vietnamese XLM-RoBERTa embedding model.

This module implements the Vietnamese XLM-RoBERTa embedding model,
which is a multilingual model with good support for Vietnamese.
"""

import os
from typing import List, Union, Dict, Any, Optional

import numpy as np
import torch
from transformers import AutoTokenizer, AutoModel

from .base import BaseEmbeddingModel
from ...utils.structured_logging import get_logger
from ...utils.vietnamese_text_processor import VietnameseTextProcessor

# Create a logger
logger = get_logger(__name__)

class VietnameseXLMRoBERTa(BaseEmbeddingModel):
    """
    Vietnamese XLM-RoBERTa embedding model.
    
    This class implements the Vietnamese XLM-RoBERTa embedding model,
    which is a multilingual model with good support for Vietnamese.
    """
    
    def __init__(
        self,
        model_name: str = "xlm-roberta-base",
        device: Optional[str] = None,
        max_length: int = 512,
        use_preprocessing: bool = True,
        **kwargs
    ):
        """
        Initialize the VietnameseXLMRoBERTa model.
        
        Args:
            model_name: Name of the model to use (xlm-roberta-base or xlm-roberta-large)
            device: Device to use (cpu, cuda, etc.)
            max_length: Maximum sequence length
            use_preprocessing: Whether to use Vietnamese-specific preprocessing
            **kwargs: Additional implementation-specific arguments
        """
        super().__init__(**kwargs)
        
        self.model_name = model_name
        self.max_length = max_length
        self.use_preprocessing = use_preprocessing
        
        # Determine device
        if device is None:
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
            if hasattr(torch, "has_mps") and torch.backends.mps.is_available():
                self.device = "mps"  # Apple Silicon GPU
        else:
            self.device = device
        
        # Initialize Vietnamese text processor
        if self.use_preprocessing:
            self.text_processor = VietnameseTextProcessor()
        
        # Initialize tokenizer and model
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            self.model = AutoModel.from_pretrained(self.model_name).to(self.device)
            self.model.eval()
            logger.info(f"Initialized VietnameseXLMRoBERTa with model {model_name} on {self.device}")
        except Exception as e:
            logger.error(f"Error initializing VietnameseXLMRoBERTa: {str(e)}")
            raise
    
    def _preprocess_text(self, text: str) -> str:
        """
        Preprocess text for XLM-RoBERTa.
        
        Args:
            text: Text to preprocess
            
        Returns:
            Preprocessed text
        """
        if self.use_preprocessing:
            # For XLM-RoBERTa, we don't need word segmentation as it uses SentencePiece tokenization
            # But we can still normalize diacritics
            return self.text_processor.normalize_diacritics(text)
        return text
    
    def _mean_pooling(self, model_output, attention_mask):
        """
        Perform mean pooling on model output.
        
        Args:
            model_output: Output from the model
            attention_mask: Attention mask
            
        Returns:
            Mean pooled embeddings
        """
        # First element of model_output contains all token embeddings
        token_embeddings = model_output.last_hidden_state
        
        # Mask away padding tokens
        input_mask_expanded = attention_mask.unsqueeze(-1).expand(token_embeddings.size()).float()
        
        # Sum token embeddings and divide by the total token count
        sum_embeddings = torch.sum(token_embeddings * input_mask_expanded, 1)
        sum_mask = torch.clamp(input_mask_expanded.sum(1), min=1e-9)
        
        # Return mean pooled embeddings
        return sum_embeddings / sum_mask
    
    def encode(
        self,
        text: Union[str, List[str]],
        **kwargs
    ) -> Union[np.ndarray, List[np.ndarray]]:
        """
        Encode text into embedding vectors.
        
        Args:
            text: Text to encode (string or list of strings)
            **kwargs: Additional implementation-specific arguments
            
        Returns:
            Embedding vector(s) as numpy array(s)
        """
        # Convert single string to list
        is_single = isinstance(text, str)
        if is_single:
            text = [text]
        
        # Preprocess text
        preprocessed_text = [self._preprocess_text(t) for t in text]
        
        try:
            # Tokenize text
            encoded_inputs = self.tokenizer(
                preprocessed_text,
                padding=True,
                truncation=True,
                max_length=self.max_length,
                return_tensors="pt"
            )
            
            # Move to device
            encoded_inputs = {k: v.to(self.device) for k, v in encoded_inputs.items()}
            
            # Compute embeddings
            with torch.no_grad():
                outputs = self.model(**encoded_inputs)
                # Use mean pooling for sentence embeddings
                embeddings = self._mean_pooling(outputs, encoded_inputs["attention_mask"])
                
                # Normalize embeddings
                embeddings = torch.nn.functional.normalize(embeddings, p=2, dim=1)
            
            # Convert to numpy
            embeddings = embeddings.cpu().numpy()
            
            # Return single embedding if input was a single string
            if is_single:
                return embeddings[0]
            
            return embeddings
            
        except Exception as e:
            logger.error(f"Error generating embeddings with VietnameseXLMRoBERTa: {str(e)}")
            # Return zero embeddings as fallback
            dim = self.get_dimension()
            if is_single:
                return np.zeros(dim)
            return [np.zeros(dim) for _ in text]
    
    def get_dimension(self) -> int:
        """
        Get the dimension of the embedding vectors.
        
        Returns:
            Dimension of the embedding vectors
        """
        # Dimensions for different models
        dimensions = {
            "xlm-roberta-base": 768,
            "xlm-roberta-large": 1024
        }
        
        return dimensions.get(self.model_name, 768)
