"""
GPT-O1 Model Integration.

This module provides integration with OpenAI's GPT-O1 model through their API.
"""

import os
import time
import logging
import json
import tiktoken
from typing import Dict, Any, List, Optional, Union, Tuple
from functools import lru_cache

from .base_reasoning_model import BaseReasoningModel
from ..utils.structured_logging import get_logger

# Try to import the OpenAI library
try:
    import openai
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

logger = get_logger(__name__)


class GPTO1Model(BaseReasoningModel):
    """
    Integration with OpenAI's GPT-O1 model.
    
    This class provides methods to interact with OpenAI's GPT-O1 model through
    their API, following the BaseReasoningModel interface.
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        organization: Optional[str] = None,
        model_name: str = "gpt-4o",
        temperature: float = 0.7,
        max_tokens: int = 1000,
        top_p: float = 1.0,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        request_timeout: int = 60,
        verbose: bool = False,
        **kwargs
    ):
        """
        Initialize the GPT-O1 model.
        
        Args:
            api_key: OpenAI API key (if None, reads from OPENAI_API_KEY env var)
            organization: OpenAI organization ID (if None, reads from OPENAI_ORGANIZATION env var)
            model_name: Name of the model to use (e.g., "gpt-4o")
            temperature: Temperature for sampling (higher = more random)
            max_tokens: Maximum number of tokens to generate
            top_p: Top-p sampling parameter
            frequency_penalty: Frequency penalty parameter
            presence_penalty: Presence penalty parameter
            request_timeout: Timeout for API requests in seconds
            verbose: Whether to print detailed information
            **kwargs: Additional parameters
        """
        if not OPENAI_AVAILABLE:
            raise ImportError(
                "The 'openai' package is required to use GPTO1Model. "
                "Install it with 'pip install openai'."
            )
        
        # Call parent constructor
        super().__init__(
            model_name=model_name,
            provider="openai",
            temperature=temperature,
            max_tokens=max_tokens,
            verbose=verbose,
            **kwargs
        )
        
        # Set OpenAI specific parameters
        self.api_key = api_key or os.environ.get("OPENAI_API_KEY")
        self.organization = organization or os.environ.get("OPENAI_ORGANIZATION")
        self.top_p = top_p
        self.frequency_penalty = frequency_penalty
        self.presence_penalty = presence_penalty
        self.request_timeout = request_timeout
        
        # Verify required parameters
        if not self.api_key:
            raise ValueError(
                "OpenAI API key is required. Either pass it as 'api_key' or "
                "set the OPENAI_API_KEY environment variable."
            )
        
        # Initialize OpenAI client
        self.client = OpenAI(
            api_key=self.api_key,
            organization=self.organization,
        )
        
        # Initialize tiktoken encoder for token counting
        self.encoding = None
        try:
            self.encoding = tiktoken.encoding_for_model(self.model_name)
        except KeyError:
            logger.warning(
                f"Model {self.model_name} not found in tiktoken. "
                "Using cl100k_base encoding instead."
            )
            self.encoding = tiktoken.get_encoding("cl100k_base")
        
        if self.verbose:
            logger.info(f"Initialized GPTO1Model with model={self.model_name}")
    
    def generate(
        self,
        prompt: str,
        system_message: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate a response for the given prompt.
        
        Args:
            prompt: The prompt to generate a response for
            system_message: Optional system message to guide generation
            temperature: Temperature for sampling (overrides instance value)
            max_tokens: Maximum tokens to generate (overrides instance value)
            stop_sequences: Optional list of sequences that stop generation
            **kwargs: Additional generation parameters
            
        Returns:
            Dict containing response text and metadata
        """
        # Check for cached response if caching is enabled
        if self.enable_caching:
            cache_key = json.dumps({
                "model": self.model_name,
                "prompt": prompt,
                "system_message": system_message,
                "temperature": temperature or self.temperature,
                "max_tokens": max_tokens or self.max_tokens,
                "stop_sequences": stop_sequences,
                **kwargs
            })
            
            cached_response = self.get_cached_response(cache_key)
            if cached_response:
                return cached_response
        
        # Prepare generation parameters
        params = {
            "model": self.model_name,
            "temperature": temperature if temperature is not None else self.temperature,
            "max_tokens": max_tokens if max_tokens is not None else self.max_tokens,
            "top_p": kwargs.get("top_p", self.top_p),
            "frequency_penalty": kwargs.get("frequency_penalty", self.frequency_penalty),
            "presence_penalty": kwargs.get("presence_penalty", self.presence_penalty),
            "timeout": kwargs.get("request_timeout", self.request_timeout),
        }
        
        # Add stop sequences if provided
        if stop_sequences:
            params["stop"] = stop_sequences
        
        # Add additional parameters
        for key, value in kwargs.items():
            if key not in params and key not in [
                "top_p", "frequency_penalty", "presence_penalty", "request_timeout"
            ]:
                params[key] = value
        
        # Log the request
        self.log_request(prompt, params)
        
        # Prepare messages
        messages = []
        
        # Add system message if provided
        if system_message:
            messages.append({"role": "system", "content": system_message})
        
        # Add user message
        messages.append({"role": "user", "content": prompt})
        
        # Create the request
        try:
            # Record start time
            start_time = time.time()
            
            # Make API call
            response = self.client.chat.completions.create(
                messages=messages,
                **params
            )
            
            # Record end time
            end_time = time.time()
            
            # Process the response
            response_text = response.choices[0].message.content
            
            # Calculate token counts
            input_tokens = response.usage.prompt_tokens
            output_tokens = response.usage.completion_tokens
            total_tokens = response.usage.total_tokens
            
            # Create result dictionary
            result = {
                "text": response_text,
                "model": self.model_name,
                "provider": "openai",
                "tokens": {
                    "prompt": input_tokens,
                    "completion": output_tokens,
                    "total": total_tokens
                },
                "latency": end_time - start_time
            }
            
            # Log the response
            self.log_response(result)
            
            # Cache the response if caching is enabled
            if self.enable_caching:
                self.save_to_cache(cache_key, result)
            
            return result
        
        except Exception as e:
            error_msg = f"Error generating response: {str(e)}"
            logger.error(error_msg)
            
            return {
                "text": "",
                "error": error_msg,
                "model": self.model_name,
                "provider": "openai"
            }
    
    def batch_generate(
        self,
        prompts: List[str],
        system_message: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Generate responses for multiple prompts in batch.
        
        Args:
            prompts: List of prompts to generate responses for
            system_message: Optional system message to guide generation
            temperature: Temperature for sampling (overrides instance value)
            max_tokens: Maximum tokens to generate (overrides instance value)
            stop_sequences: Optional list of sequences that stop generation
            **kwargs: Additional generation parameters
            
        Returns:
            List of dicts containing response texts and metadata
        """
        results = []
        
        # Process each prompt individually
        # Note: OpenAI doesn't support true batching, so we make individual requests
        for prompt in prompts:
            result = self.generate(
                prompt=prompt,
                system_message=system_message,
                temperature=temperature,
                max_tokens=max_tokens,
                stop_sequences=stop_sequences,
                **kwargs
            )
            results.append(result)
        
        return results
    
    def get_token_count(self, text: str) -> int:
        """
        Count the number of tokens in the given text.
        
        Args:
            text: The text to count tokens for
            
        Returns:
            Number of tokens in the text
        """
        if not text:
            return 0
        
        # Use the tiktoken encoder
        tokens = self.encoding.encode(text)
        return len(tokens)
    
    def stream_generate(
        self,
        prompt: str,
        system_message: Optional[str] = None,
        callback: Optional[callable] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate a response with streaming support.
        
        Args:
            prompt: The prompt to generate a response for
            system_message: Optional system message
            callback: Function to call with each chunk of the response
            temperature: Temperature for sampling (overrides instance value)
            max_tokens: Maximum tokens to generate (overrides instance value)
            stop_sequences: Optional list of sequences that stop generation
            **kwargs: Additional parameters
            
        Returns:
            Dict containing the complete response text and metadata
        """
        if not callback:
            # Fall back to non-streaming if no callback provided
            return self.generate(
                prompt=prompt,
                system_message=system_message,
                temperature=temperature,
                max_tokens=max_tokens,
                stop_sequences=stop_sequences,
                **kwargs
            )
        
        # Prepare generation parameters
        params = {
            "model": self.model_name,
            "temperature": temperature if temperature is not None else self.temperature,
            "max_tokens": max_tokens if max_tokens is not None else self.max_tokens,
            "top_p": kwargs.get("top_p", self.top_p),
            "frequency_penalty": kwargs.get("frequency_penalty", self.frequency_penalty),
            "presence_penalty": kwargs.get("presence_penalty", self.presence_penalty),
            "timeout": kwargs.get("request_timeout", self.request_timeout),
            "stream": True  # Enable streaming
        }
        
        # Add stop sequences if provided
        if stop_sequences:
            params["stop"] = stop_sequences
        
        # Prepare messages
        messages = []
        
        # Add system message if provided
        if system_message:
            messages.append({"role": "system", "content": system_message})
        
        # Add user message
        messages.append({"role": "user", "content": prompt})
        
        # Log the request
        self.log_request(prompt, params)
        
        try:
            # Record start time
            start_time = time.time()
            
            # Make streaming API call
            response_stream = self.client.chat.completions.create(
                messages=messages,
                **params
            )
            
            # Collect the entire response
            full_text = ""
            
            for chunk in response_stream:
                if hasattr(chunk, 'choices') and chunk.choices:
                    # Extract text from the chunk
                    if hasattr(chunk.choices[0], 'delta') and hasattr(chunk.choices[0].delta, 'content'):
                        chunk_text = chunk.choices[0].delta.content
                        if chunk_text:
                            full_text += chunk_text
                            # Call the callback with the chunk
                            callback(chunk_text)
            
            # Record end time
            end_time = time.time()
            
            # Count tokens in the full response
            input_tokens = self.get_token_count(prompt)
            if system_message:
                input_tokens += self.get_token_count(system_message)
            output_tokens = self.get_token_count(full_text)
            total_tokens = input_tokens + output_tokens
            
            # Create result dictionary
            result = {
                "text": full_text,
                "model": self.model_name,
                "provider": "openai",
                "tokens": {
                    "prompt": input_tokens,
                    "completion": output_tokens,
                    "total": total_tokens
                },
                "latency": end_time - start_time
            }
            
            # Log the response
            self.log_response(result)
            
            return result
        
        except Exception as e:
            error_msg = f"Error in streaming generation: {str(e)}"
            logger.error(error_msg)
            
            return {
                "text": "",
                "error": error_msg,
                "model": self.model_name,
                "provider": "openai"
            } 