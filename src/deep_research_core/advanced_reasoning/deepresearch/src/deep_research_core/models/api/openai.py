"""
OpenAI API integration for Deep Research Core.
"""

import os
import logging
import json
from typing import Dict, Any, List, Optional, Union

import openai
from openai import OpenAI

from ...config import API_PROVIDERS

logger = logging.getLogger(__name__)

class OpenAIProvider:
    """
    Handles interactions with the OpenAI API.
    """

    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the OpenAI provider.

        Args:
            api_key: OpenAI API key. If None, will try to get from environment.
        """
        self.provider_config = API_PROVIDERS.get("openai", {})
        self.api_key = api_key or os.getenv(self.provider_config.get("api_key_env", "OPENAI_API_KEY"))

        if not self.api_key:
            logger.warning("OpenAI API key not found. Set OPENAI_API_KEY environment variable.")

        if self.api_key:
            self.client = OpenAI(api_key=self.api_key)
        else:
            self.client = None
        self.available_models = self.provider_config.get("models", {})

    def get_available_models(self) -> List[str]:
        """
        Returns a list of available models.
        """
        return list(self.available_models.keys())

    def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """
        Returns information about a model.
        """
        if model_name not in self.available_models:
            raise ValueError(f"Model {model_name} not available in OpenAI provider")

        model_config = self.available_models[model_name]
        return {
            "name": model_name,
            "provider": "openai",
            "context_length": model_config.get("context_length", 4096),
            "supports": model_config.get("supports", {})
        }

    def generate(
        self,
        prompt: str,
        model: str = "gpt-4o",
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        stop: Optional[List[str]] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[str, Any]:
        """
        Generate text using the OpenAI API.

        Args:
            prompt: The user prompt
            model: Model to use
            system_prompt: System prompt to use
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            stop: List of stop sequences
            stream: Whether to stream the response
            **kwargs: Additional arguments to pass to the API

        Returns:
            Generated text or stream object
        """
        if model not in self.available_models:
            raise ValueError(f"Model {model} not available in OpenAI provider")

        if not self.client:
            logger.error("OpenAI client not initialized. API key may be missing.")
            raise ValueError("OpenAI client not initialized. API key may be missing.")

        messages = []

        # Add system prompt if provided
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})

        # Add user prompt
        messages.append({"role": "user", "content": prompt})

        try:
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stop=stop,
                stream=stream,
                **kwargs
            )

            if stream:
                return response
            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"Error generating text with OpenAI: {str(e)}")
            raise

    def generate_with_context(
        self,
        prompt: str,
        context: List[Dict[str, str]],
        model: str = "gpt-4o",
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        stop: Optional[List[str]] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[str, Any]:
        """
        Generate text with conversation context using the OpenAI API.

        Args:
            prompt: The user prompt
            context: List of previous messages in the conversation
            model: Model to use
            system_prompt: System prompt to use
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            stop: List of stop sequences
            stream: Whether to stream the response
            **kwargs: Additional arguments to pass to the API

        Returns:
            Generated text or stream object
        """
        if model not in self.available_models:
            raise ValueError(f"Model {model} not available in OpenAI provider")

        if not self.client:
            logger.error("OpenAI client not initialized. API key may be missing.")
            raise ValueError("OpenAI client not initialized. API key may be missing.")

        messages = []

        # Add system prompt if provided
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})

        # Add conversation context
        messages.extend(context)

        # Add user prompt
        messages.append({"role": "user", "content": prompt})

        try:
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stop=stop,
                stream=stream,
                **kwargs
            )

            if stream:
                return response
            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"Error generating text with OpenAI: {str(e)}")
            raise


# Create OpenAIAPI class for compatibility with the API server
class OpenAIAPI(OpenAIProvider):
    """Compatibility class for the API server."""
    pass

# Create a singleton instance
openai_provider = OpenAIProvider()
