"""
Anthropic (<PERSON>) API integration for Deep Research Core.
"""

import os
import logging
import json
from typing import Dict, Any, List, Optional, Union

import anthropic
from anthropic import Anthropic

from ...config import API_PROVIDERS

logger = logging.getLogger(__name__)

class AnthropicProvider:
    """
    Handles interactions with the Anthropic (Claude) API.
    """

    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the Anthropic provider.

        Args:
            api_key: Anthropic API key. If None, will try to get from environment.
        """
        self.provider_config = API_PROVIDERS.get("anthropic", {})
        self.api_key = api_key or os.getenv(self.provider_config.get("api_key_env", "ANTHROPIC_API_KEY"))

        if not self.api_key:
            logger.warning("Anthropic API key not found. Set ANTHROPIC_API_KEY environment variable.")

        if self.api_key:
            self.client = Anthropic(api_key=self.api_key)
        else:
            self.client = None
        self.available_models = self.provider_config.get("models", {})

    def get_available_models(self) -> List[str]:
        """
        Returns a list of available models.
        """
        return list(self.available_models.keys())

    def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """
        Returns information about a model.
        """
        if model_name not in self.available_models:
            raise ValueError(f"Model {model_name} not available in Anthropic provider")

        model_config = self.available_models[model_name]
        return {
            "name": model_name,
            "provider": "anthropic",
            "context_length": model_config.get("context_length", 100000),
            "supports": model_config.get("supports", {})
        }

    def generate(
        self,
        prompt: str,
        model: str = "claude-3-sonnet",
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        stop: Optional[List[str]] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[str, Any]:
        """
        Generate text using the Anthropic API.

        Args:
            prompt: The user prompt
            model: Model to use
            system_prompt: System prompt to use
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            stop: List of stop sequences
            stream: Whether to stream the response
            **kwargs: Additional arguments to pass to the API

        Returns:
            Generated text or stream object
        """
        if model not in self.available_models:
            raise ValueError(f"Model {model} not available in Anthropic provider")

        if not self.client:
            logger.error("Anthropic client not initialized. API key may be missing.")
            raise ValueError("Anthropic client not initialized. API key may be missing.")

        try:
            message_params = {
                "model": model,
                "messages": [{"role": "user", "content": prompt}],
                "temperature": temperature,
                "max_tokens": max_tokens,
                "stream": stream
            }

            # Add system prompt if provided
            if system_prompt:
                message_params["system"] = system_prompt

            # Add stop sequences if provided
            if stop:
                message_params["stop_sequences"] = stop

            # Add additional parameters
            for key, value in kwargs.items():
                message_params[key] = value

            response = self.client.messages.create(**message_params)

            if stream:
                return response
            return response.content[0].text

        except Exception as e:
            logger.error(f"Error generating text with Anthropic: {str(e)}")
            raise

    def generate_with_context(
        self,
        prompt: str,
        context: List[Dict[str, str]],
        model: str = "claude-3-sonnet",
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        stop: Optional[List[str]] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[str, Any]:
        """
        Generate text with conversation context using the Anthropic API.

        Args:
            prompt: The user prompt
            context: List of previous messages in the conversation
            model: Model to use
            system_prompt: System prompt to use
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            stop: List of stop sequences
            stream: Whether to stream the response
            **kwargs: Additional arguments to pass to the API

        Returns:
            Generated text or stream object
        """
        if model not in self.available_models:
            raise ValueError(f"Model {model} not available in Anthropic provider")

        if not self.client:
            logger.error("Anthropic client not initialized. API key may be missing.")
            raise ValueError("Anthropic client not initialized. API key may be missing.")

        try:
            # Convert context to Anthropic format
            messages = []
            for msg in context:
                role = msg.get("role", "user")
                # Map OpenAI roles to Anthropic roles
                if role == "assistant":
                    messages.append({"role": "assistant", "content": msg["content"]})
                else:
                    messages.append({"role": "user", "content": msg["content"]})

            # Add the current prompt
            messages.append({"role": "user", "content": prompt})

            message_params = {
                "model": model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "stream": stream
            }

            # Add system prompt if provided
            if system_prompt:
                message_params["system"] = system_prompt

            # Add stop sequences if provided
            if stop:
                message_params["stop_sequences"] = stop

            # Add additional parameters
            for key, value in kwargs.items():
                message_params[key] = value

            response = self.client.messages.create(**message_params)

            if stream:
                return response
            return response.content[0].text

        except Exception as e:
            logger.error(f"Error generating text with Anthropic: {str(e)}")
            raise


# Create AnthropicAPI class for compatibility with the API server
class AnthropicAPI(AnthropicProvider):
    """Compatibility class for the API server."""
    pass

# Create a singleton instance
anthropic_provider = AnthropicProvider()
