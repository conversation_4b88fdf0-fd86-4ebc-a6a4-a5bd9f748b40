"""
Cohere API provider for Deep Research Core.

This module provides a client for the Cohere API.
"""

import os
import logging
from typing import Dict, List, Any, Optional, Union

from ...config.providers import API_PROVIDERS
from ...providers.cohere import CohereProvider

# Create a logger
logger = logging.getLogger(__name__)

class CohereAPIProvider:
    """
    Handles interactions with the Cohere API.
    """

    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the Cohere provider.

        Args:
            api_key: Cohere API key. If None, will try to get from environment.
        """
        self.provider_config = API_PROVIDERS.get("cohere", {})
        self.api_key = api_key or os.getenv(self.provider_config.get("api_key_env", "COHERE_API_KEY"))

        if not self.api_key:
            logger.warning("Cohere API key not found. Set COHERE_API_KEY environment variable.")

        self.base_url = self.provider_config.get("base_url", "https://api.cohere.ai/v1")
        self.available_models = self.provider_config.get("models", {})
        
        # Initialize the provider
        self.provider = CohereProvider(
            api_key=self.api_key,
            api_base=self.base_url
        )

    def generate(
        self,
        prompt: str,
        model: str = "command",
        max_tokens: int = 1024,
        temperature: float = 0.7,
        system_message: Optional[str] = None,
        stop: Optional[Union[str, List[str]]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate text using the Cohere API.

        Args:
            prompt: The prompt to generate text from
            model: The model to use
            max_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature
            system_message: System message for chat models
            stop: Stop sequences
            **kwargs: Additional parameters

        Returns:
            Generated text and metadata
        """
        try:
            response = self.provider.generate(
                prompt=prompt,
                system_message=system_message,
                max_tokens=max_tokens,
                temperature=temperature,
                model=model,
                stop=stop,
                **kwargs
            )
            
            # Extract the generated text
            text = response["choices"][0]["text"]
            
            return {
                "text": text,
                "model": model,
                "provider": "cohere",
                "usage": response.get("usage", {})
            }
        except Exception as e:
            logger.error(f"Error generating text with Cohere: {str(e)}")
            raise

    def chat(
        self,
        messages: List[Dict[str, str]],
        model: str = "command",
        max_tokens: int = 1024,
        temperature: float = 0.7,
        system_message: Optional[str] = None,
        stop: Optional[Union[str, List[str]]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate chat response using the Cohere API.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            model: The model to use
            max_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature
            system_message: System message for chat models
            stop: Stop sequences
            **kwargs: Additional parameters

        Returns:
            Generated chat response and metadata
        """
        try:
            response = self.provider.chat(
                messages=messages,
                system_message=system_message,
                max_tokens=max_tokens,
                temperature=temperature,
                model=model,
                stop=stop,
                **kwargs
            )
            
            # Extract the generated text
            text = response["choices"][0]["text"]
            
            return {
                "text": text,
                "model": model,
                "provider": "cohere",
                "usage": response.get("usage", {})
            }
        except Exception as e:
            logger.error(f"Error generating chat response with Cohere: {str(e)}")
            raise

    def get_embedding(
        self,
        text: str,
        model: Optional[str] = None,
        **kwargs
    ) -> List[float]:
        """
        Get embedding for the given text.

        Args:
            text: The text to get embedding for
            model: The model to use (if None, will use the default)
            **kwargs: Additional parameters

        Returns:
            Embedding as a list of floats
        """
        try:
            # Use the default embedding model if not specified
            embedding_model = model or "embed-english-v3.0"
            
            # Get embedding
            response = self.provider.embed(
                text=text,
                model=embedding_model,
                **kwargs
            )
            
            # Return the first embedding (for single text input)
            return response["embeddings"][0]
        except Exception as e:
            logger.error(f"Error getting embedding with Cohere: {str(e)}")
            raise

    def get_available_models(self) -> Dict[str, Any]:
        """
        Get available models for this provider.

        Returns:
            Dictionary of available models with their details
        """
        return self.available_models


# Create a singleton instance
cohere_provider = CohereAPIProvider()
