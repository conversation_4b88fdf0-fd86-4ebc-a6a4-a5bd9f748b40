"""
Mistral AI API provider for Deep Research Core.

This module provides a client for the Mistral AI API.
"""

import os
import logging
from typing import Dict, List, Any, Optional, Union

from ...config.providers import API_PROVIDERS
from ...providers.mistral import MistralProvider

# Create a logger
logger = logging.getLogger(__name__)

class MistralAPIProvider:
    """
    Handles interactions with the Mistral AI API.
    """

    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the Mistral AI provider.

        Args:
            api_key: Mistral AI API key. If None, will try to get from environment.
        """
        self.provider_config = API_PROVIDERS.get("mistral", {})
        self.api_key = api_key or os.getenv(self.provider_config.get("api_key_env", "MISTRAL_API_KEY"))

        if not self.api_key:
            logger.warning("Mistral AI API key not found. Set MISTRAL_API_KEY environment variable.")

        self.base_url = self.provider_config.get("base_url", "https://api.mistral.ai/v1")
        self.available_models = self.provider_config.get("models", {})
        
        # Initialize the provider
        self.provider = MistralProvider(
            api_key=self.api_key,
            api_base=self.base_url
        )

    def generate(
        self,
        prompt: str,
        model: str = "mistral-medium",
        max_tokens: int = 1024,
        temperature: float = 0.7,
        system_message: Optional[str] = None,
        stop: Optional[Union[str, List[str]]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate text using the Mistral AI API.

        Args:
            prompt: The prompt to generate text from
            model: The model to use
            max_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature
            system_message: System message for chat models
            stop: Stop sequences
            **kwargs: Additional parameters

        Returns:
            Generated text and metadata
        """
        try:
            response = self.provider.generate(
                prompt=prompt,
                system_message=system_message,
                max_tokens=max_tokens,
                temperature=temperature,
                model=model,
                stop=stop,
                **kwargs
            )
            
            # Extract the generated text
            text = response["choices"][0]["text"]
            
            return {
                "text": text,
                "model": model,
                "provider": "mistral",
                "usage": response.get("usage", {})
            }
        except Exception as e:
            logger.error(f"Error generating text with Mistral AI: {str(e)}")
            raise

    def chat(
        self,
        messages: List[Dict[str, str]],
        model: str = "mistral-medium",
        max_tokens: int = 1024,
        temperature: float = 0.7,
        stop: Optional[Union[str, List[str]]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate chat response using the Mistral AI API.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            model: The model to use
            max_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature
            stop: Stop sequences
            **kwargs: Additional parameters

        Returns:
            Generated chat response and metadata
        """
        try:
            # Mistral uses the same generate method for chat, just with messages
            response = self.provider.generate(
                prompt="",  # Not used when messages are provided
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                model=model,
                stop=stop,
                **kwargs
            )
            
            # Extract the generated text
            text = response["choices"][0]["text"]
            
            return {
                "text": text,
                "model": model,
                "provider": "mistral",
                "usage": response.get("usage", {})
            }
        except Exception as e:
            logger.error(f"Error generating chat response with Mistral AI: {str(e)}")
            raise

    def get_embedding(
        self,
        text: str,
        model: Optional[str] = None,
        **kwargs
    ) -> List[float]:
        """
        Get embedding for the given text.

        Args:
            text: The text to get embedding for
            model: The model to use (if None, will use the default)
            **kwargs: Additional parameters

        Returns:
            Embedding as a list of floats
        """
        try:
            # Use the default embedding model if not specified
            embedding_model = model or "mistral-embed"
            
            # Get embedding
            response = self.provider.embed(
                text=text,
                model=embedding_model,
                **kwargs
            )
            
            # Return the first embedding (for single text input)
            return response["embeddings"][0]
        except Exception as e:
            logger.error(f"Error getting embedding with Mistral AI: {str(e)}")
            raise

    def get_available_models(self) -> Dict[str, Any]:
        """
        Get available models for this provider.

        Returns:
            Dictionary of available models with their details
        """
        return self.available_models


# Create a singleton instance
mistral_provider = MistralAPIProvider()
