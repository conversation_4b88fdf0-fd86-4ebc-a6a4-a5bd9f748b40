"""
OpenRouter API integration for Deep Research Core.
"""

import os
import logging
from typing import Dict, Any, List, Optional, Union

import requests

logger = logging.getLogger(__name__)

class OpenRouterAPI:
    """
    Handles interactions with the OpenRouter API.
    """

    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the OpenRouter provider.

        Args:
            api_key: OpenRouter API key. If None, will try to get from environment.
        """
        self.api_key = api_key or os.getenv("OPENROUTER_API_KEY")
        if not self.api_key:
            logger.warning("OpenRouter API key not found. Set OPENROUTER_API_KEY environment variable.")
        
        self.base_url = "https://openrouter.ai/api/v1"
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
            "HTTP-Referer": "https://github.com/bazi88/deepresearch",
            "X-Title": "Deep Research Core"
        }
    
    def generate(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        model: str = "moonshotai/moonlight-16b-a3b-instruct:free",
        temperature: float = 0.7,
        max_tokens: int = 2000,
        top_p: float = 0.95,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        stop: Optional[List[str]] = None,
        **kwargs
    ) -> str:
        """
        Generate text using the OpenRouter API.
        
        Args:
            prompt: Prompt to generate from.
            system_prompt: System prompt to use.
            model: Model to use for generation.
            temperature: Temperature for generation.
            max_tokens: Maximum number of tokens to generate.
            top_p: Top-p sampling parameter.
            frequency_penalty: Frequency penalty parameter.
            presence_penalty: Presence penalty parameter.
            stop: List of strings to stop generation at.
            **kwargs: Additional parameters to pass to the API.
            
        Returns:
            Generated text.
        """
        # Check if API key is set
        if not self.api_key:
            raise ValueError("OpenRouter API key not found. Set OPENROUTER_API_KEY environment variable.")
        
        # Set parameters
        params = {
            "model": model,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "top_p": top_p,
            "frequency_penalty": frequency_penalty,
            "presence_penalty": presence_penalty,
            **kwargs
        }
        
        # Set messages
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        # Set data
        data = {
            "messages": messages,
            **params
        }
        
        # Add stop sequences if provided
        if stop:
            data["stop"] = stop
        
        # Make request
        try:
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=data,
                timeout=120
            )
            response.raise_for_status()
            
            # Parse response
            result = response.json()
            
            # Extract generated text
            generated_text = result["choices"][0]["message"]["content"]
            
            return generated_text
        except Exception as e:
            logger.error(f"Error generating text with OpenRouter: {str(e)}")
            raise

# Create a singleton instance
openrouter_provider = OpenRouterAPI()
