"""
Anthropic API provider for Deep Research Core.

This module provides a client for the Anthropic API, which allows access to
Claude models.
"""

import os
import json
import time
from typing import Dict, Any, List, Optional, Union, Callable

from ..base import BaseAPIProvider
from ....utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

class AnthropicProvider(BaseAPIProvider):
    """
    Anthropic API provider for Deep Research Core.

    This class provides a client for the Anthropic API, which allows access to
    Claude models.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        model: str = "claude-3-opus-20240229",
        temperature: float = 0.7,
        max_tokens: int = 2000,
        top_p: float = 0.95,
        timeout: int = 120,
        **kwargs
    ):
        """
        Initialize the Anthropic API provider.

        Args:
            api_key: Anthropic API key. If None, will try to get from environment variable.
            model: Model to use for generation.
            temperature: Temperature for generation.
            max_tokens: Maximum number of tokens to generate.
            top_p: Top-p sampling parameter.
            timeout: Timeout for API requests in seconds.
            **kwargs: Additional parameters to pass to the API.
        """
        super().__init__()

        # Set API key
        self.api_key = api_key or os.environ.get("ANTHROPIC_API_KEY")

        # Check if we should show warning
        from deep_research_core.config.api_warnings import should_show_warning
        if not self.api_key and should_show_warning("anthropic"):
            logger.warning("anthropic API key not found. Set ANTHROPIC_API_KEY environment variable.")

        # Set model parameters
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.top_p = top_p
        self.timeout = timeout
        self.kwargs = kwargs

        # Try to import Anthropic
        try:
            import anthropic
            self.anthropic_available = True
            if self.api_key:
                self.client = anthropic.Anthropic(api_key=self.api_key)
            else:
                self.client = None
        except ImportError:
            logger.warning("Anthropic Python package not found. Install with 'pip install anthropic'.")
            self.anthropic_available = False
            self.client = None

    def generate(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        top_p: Optional[float] = None,
        stop: Optional[List[str]] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[str, Any]:
        """
        Generate text using the Anthropic API.

        Args:
            prompt: Prompt to generate from.
            system_prompt: System prompt to use.
            temperature: Temperature for generation. If None, use default.
            max_tokens: Maximum number of tokens to generate. If None, use default.
            top_p: Top-p sampling parameter. If None, use default.
            stop: List of strings to stop generation at.
            stream: Whether to stream the response.
            **kwargs: Additional parameters to pass to the API.

        Returns:
            Generated text or stream object if stream=True.
        """
        # Check if API key is set
        if not self.api_key:
            raise ValueError("Anthropic API key not found. Set ANTHROPIC_API_KEY environment variable.")

        # Check if Anthropic is available
        if not self.anthropic_available:
            raise ImportError("Anthropic Python package not found. Install with 'pip install anthropic'.")

        # Set parameters
        params = {
            "model": self.model,
            "temperature": temperature if temperature is not None else self.temperature,
            "max_tokens": max_tokens if max_tokens is not None else self.max_tokens,
            "top_p": top_p if top_p is not None else self.top_p,
            "stream": stream,
            **self.kwargs,
            **kwargs
        }

        # Set messages
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        # Add stop sequences if provided
        if stop:
            params["stop_sequences"] = stop

        # Make request
        try:
            response = self.client.messages.create(
                messages=messages,
                **params
            )

            # Return stream if requested
            if stream:
                return response

            # Extract generated text
            generated_text = response.content[0].text

            return generated_text
        except Exception as e:
            logger.error(f"Error generating text with Anthropic: {str(e)}")
            raise

    def get_embedding(self, text: str, model: Optional[str] = None) -> List[float]:
        """
        Get embedding for text using the Anthropic API.

        Note: Anthropic doesn't currently provide a dedicated embeddings API.
        This method uses a workaround by generating a vector representation
        through a prompt to Claude.

        Args:
            text: Text to get embedding for.
            model: Model to use for embeddings. If None, use default.

        Returns:
            Embedding vector as a list of floats.
        """
        # Check if API key is set
        if not self.api_key:
            raise ValueError("Anthropic API key not found. Set ANTHROPIC_API_KEY environment variable.")

        # Check if Anthropic is available
        if not self.anthropic_available:
            raise ImportError("Anthropic Python package not found. Install with 'pip install anthropic'.")

        try:
            # Since Anthropic doesn't have a dedicated embeddings API,
            # we'll use a workaround by asking Claude to generate a vector representation
            system_prompt = """
            You are a semantic embedding generator. Your task is to convert text into a
            vector representation that captures its semantic meaning.
            Generate a vector of 1536 dimensions with values between -1 and 1.
            Return ONLY the vector as a JSON array of numbers, nothing else.
            """

            prompt = f"Generate a semantic embedding vector for the following text: {text}"

            response = self.generate(
                prompt=prompt,
                system_prompt=system_prompt,
                temperature=0.0,
                max_tokens=4000
            )

            # Parse the response to extract the vector
            try:
                # Try to parse as JSON directly
                embedding = json.loads(response)
                if isinstance(embedding, list) and len(embedding) > 0 and isinstance(embedding[0], (int, float)):
                    # Ensure we have 1536 dimensions
                    if len(embedding) != 1536:
                        # Pad or truncate to 1536 dimensions
                        if len(embedding) < 1536:
                            embedding.extend([0.0] * (1536 - len(embedding)))
                        else:
                            embedding = embedding[:1536]
                    return embedding
            except:
                # If direct parsing fails, try to extract JSON array from text
                import re
                match = re.search(r'\[[\d\.\-\,\s]+\]', response)
                if match:
                    try:
                        embedding = json.loads(match.group(0))
                        if isinstance(embedding, list) and len(embedding) > 0:
                            # Ensure we have 1536 dimensions
                            if len(embedding) != 1536:
                                # Pad or truncate to 1536 dimensions
                                if len(embedding) < 1536:
                                    embedding.extend([0.0] * (1536 - len(embedding)))
                                else:
                                    embedding = embedding[:1536]
                            return embedding
                    except:
                        pass

            # If all parsing attempts fail, return a random embedding
            logger.error("Failed to parse embedding from Anthropic response")
            import numpy as np
            return list(np.random.rand(1536) * 2 - 1)  # Random values between -1 and 1

        except Exception as e:
            logger.error(f"Error getting embedding with Anthropic: {str(e)}")
            # Return a random embedding as fallback
            import numpy as np
            return list(np.random.rand(1536) * 2 - 1)  # Random values between -1 and 1

    def get_available_models(self) -> Dict[str, Any]:
        """
        Get available models from Anthropic API.

        Returns:
            Dictionary of available models with their details
        """
        # Check if API key is set
        if not self.api_key:
            raise ValueError("Anthropic API key not found. Set ANTHROPIC_API_KEY environment variable.")

        # Check if Anthropic is available
        if not self.anthropic_available:
            raise ImportError("Anthropic Python package not found. Install with 'pip install anthropic'.")

        # Anthropic doesn't have a public API endpoint to list models,
        # so we'll return a hardcoded list of known models
        models = {
            "claude-3-opus-20240229": {
                "id": "claude-3-opus-20240229",
                "name": "Claude 3 Opus",
                "context_length": 200000,
                "pricing": {"input": 0.000015, "output": 0.000075}
            },
            "claude-3-sonnet-20240229": {
                "id": "claude-3-sonnet-20240229",
                "name": "Claude 3 Sonnet",
                "context_length": 200000,
                "pricing": {"input": 0.000003, "output": 0.000015}
            },
            "claude-3-haiku-20240307": {
                "id": "claude-3-haiku-20240307",
                "name": "Claude 3 Haiku",
                "context_length": 200000,
                "pricing": {"input": 0.00000025, "output": 0.00000125}
            },
            "claude-2.1": {
                "id": "claude-2.1",
                "name": "Claude 2.1",
                "context_length": 100000,
                "pricing": {"input": 0.000008, "output": 0.000024}
            },
            "claude-2.0": {
                "id": "claude-2.0",
                "name": "Claude 2.0",
                "context_length": 100000,
                "pricing": {"input": 0.000008, "output": 0.000024}
            },
            "claude-instant-1.2": {
                "id": "claude-instant-1.2",
                "name": "Claude Instant 1.2",
                "context_length": 100000,
                "pricing": {"input": 0.0000008, "output": 0.0000024}
            }
        }

        return models

def generate(
    prompt: str,
    system_prompt: Optional[str] = None,
    model: str = "claude-3-opus-20240229",
    temperature: float = 0.7,
    max_tokens: int = 2000,
    top_p: float = 0.95,
    stop: Optional[List[str]] = None,
    api_key: Optional[str] = None,
    **kwargs
) -> str:
    """
    Generate text using the Anthropic API.

    This is a convenience function that creates an AnthropicProvider instance
    and calls its generate method.

    Args:
        prompt: Prompt to generate from.
        system_prompt: System prompt to use.
        model: Model to use for generation.
        temperature: Temperature for generation.
        max_tokens: Maximum number of tokens to generate.
        top_p: Top-p sampling parameter.
        stop: List of strings to stop generation at.
        api_key: Anthropic API key. If None, will try to get from environment variable.
        **kwargs: Additional parameters to pass to the API.

    Returns:
        Generated text.
    """
    provider = AnthropicProvider(
        api_key=api_key,
        model=model,
        temperature=temperature,
        max_tokens=max_tokens,
        top_p=top_p,
        **kwargs
    )

    return provider.generate(
        prompt=prompt,
        system_prompt=system_prompt,
        stop=stop,
        **kwargs
    )
