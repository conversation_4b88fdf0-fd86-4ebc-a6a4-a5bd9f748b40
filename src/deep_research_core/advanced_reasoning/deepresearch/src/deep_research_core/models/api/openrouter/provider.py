"""
OpenRouter API provider for Deep Research Core.

This module provides a client for the OpenRouter API, which allows access to
various LLM models through a unified API.
"""

import os
import json
import time
import requests
from typing import Dict, Any, List, Optional, Union, Callable

from ..base import BaseAPIProvider
from ....utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

class OpenRouterProvider(BaseAPIProvider):
    """
    OpenRouter API provider for Deep Research Core.

    This class provides a client for the OpenRouter API, which allows access to
    various LLM models through a unified API.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        model: str = "moonshotai/moonlight-16b-a3b-instruct:free",
        temperature: float = 0.7,
        max_tokens: int = 2000,
        top_p: float = 0.95,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        timeout: int = 120,
        base_url: str = "https://openrouter.ai/api/v1",
        **kwargs
    ):
        """
        Initialize the OpenRouter API provider.

        Args:
            api_key: OpenRouter API key. If None, will try to get from environment variable.
            model: Model to use for generation.
            temperature: Temperature for generation.
            max_tokens: Maximum number of tokens to generate.
            top_p: Top-p sampling parameter.
            frequency_penalty: Frequency penalty parameter.
            presence_penalty: Presence penalty parameter.
            timeout: Timeout for API requests in seconds.
            base_url: Base URL for the OpenRouter API.
            **kwargs: Additional parameters to pass to the API.
        """
        super().__init__()

        # Set API key
        self.api_key = api_key or os.environ.get("OPENROUTER_API_KEY")

        # Check if we should show warning
        from deep_research_core.config.api_warnings import should_show_warning
        if not self.api_key and should_show_warning("openrouter"):
            logger.warning("OpenRouter API key not found. Set OPENROUTER_API_KEY environment variable.")

        # Set model parameters
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.top_p = top_p
        self.frequency_penalty = frequency_penalty
        self.presence_penalty = presence_penalty
        self.timeout = timeout
        self.base_url = base_url
        self.kwargs = kwargs

        # Set headers
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
            "HTTP-Referer": "https://github.com/bazi88/deepresearch",
            "X-Title": "Deep Research Core"
        }

    def generate(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        top_p: Optional[float] = None,
        frequency_penalty: Optional[float] = None,
        presence_penalty: Optional[float] = None,
        stop: Optional[List[str]] = None,
        **kwargs
    ) -> str:
        """
        Generate text using the OpenRouter API.

        Args:
            prompt: Prompt to generate from.
            system_prompt: System prompt to use.
            temperature: Temperature for generation. If None, use default.
            max_tokens: Maximum number of tokens to generate. If None, use default.
            top_p: Top-p sampling parameter. If None, use default.
            frequency_penalty: Frequency penalty parameter. If None, use default.
            presence_penalty: Presence penalty parameter. If None, use default.
            stop: List of strings to stop generation at.
            **kwargs: Additional parameters to pass to the API.

        Returns:
            Generated text.
        """
        # Check if API key is set
        if not self.api_key:
            raise ValueError("OpenRouter API key not found. Set OPENROUTER_API_KEY environment variable.")

        # Set parameters
        params = {
            "model": self.model,
            "temperature": temperature if temperature is not None else self.temperature,
            "max_tokens": max_tokens if max_tokens is not None else self.max_tokens,
            "top_p": top_p if top_p is not None else self.top_p,
            "frequency_penalty": frequency_penalty if frequency_penalty is not None else self.frequency_penalty,
            "presence_penalty": presence_penalty if presence_penalty is not None else self.presence_penalty,
            **self.kwargs,
            **kwargs
        }

        # Set messages
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        # Set data
        data = {
            "messages": messages,
            **params
        }

        # Add stop sequences if provided
        if stop:
            data["stop"] = stop

        # Make request
        try:
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=data,
                timeout=self.timeout
            )
            response.raise_for_status()

            # Parse response
            result = response.json()

            # Extract generated text
            generated_text = result["choices"][0]["message"]["content"]

            return generated_text
        except Exception as e:
            logger.error(f"Error generating text with OpenRouter: {str(e)}")
            raise

    def get_embedding(self, text: str, model: Optional[str] = None) -> List[float]:
        """
        Get embedding for text using the OpenRouter API.

        Args:
            text: Text to get embedding for.
            model: Model to use for embeddings. If None, use default embedding model.

        Returns:
            Embedding vector as a list of floats.
        """
        # Check if API key is set
        if not self.api_key:
            raise ValueError("OpenRouter API key not found. Set OPENROUTER_API_KEY environment variable.")

        # OpenRouter supports embeddings through their API
        embedding_model = model or "openai/text-embedding-ada-002"

        try:
            # Set data
            data = {
                "model": embedding_model,
                "input": text
            }

            # Make request
            response = requests.post(
                f"{self.base_url}/embeddings",
                headers=self.headers,
                json=data,
                timeout=self.timeout
            )
            response.raise_for_status()

            # Parse response
            result = response.json()

            # Extract embedding
            embedding = result["data"][0]["embedding"]

            return embedding
        except Exception as e:
            logger.error(f"Error getting embedding with OpenRouter: {str(e)}")
            # Return a random embedding as fallback (1536 dimensions is standard for OpenAI embeddings)
            import numpy as np
            return list(np.random.rand(1536))

    def get_available_models(self) -> Dict[str, Any]:
        """
        Get available models from OpenRouter API.

        Returns:
            Dictionary of available models with their details
        """
        # Check if API key is set
        if not self.api_key:
            raise ValueError("OpenRouter API key not found. Set OPENROUTER_API_KEY environment variable.")

        try:
            # Make request
            response = requests.get(
                f"{self.base_url}/models",
                headers=self.headers,
                timeout=self.timeout
            )
            response.raise_for_status()

            # Parse response
            result = response.json()

            # Extract models
            models = {}
            for model in result.get("data", []):
                model_id = model.get("id", "")
                models[model_id] = model

            return models
        except Exception as e:
            logger.error(f"Error getting available models from OpenRouter: {str(e)}")
            # Return a minimal set of models as fallback
            return {
                "moonshotai/moonlight-16b-a3b-instruct:free": {
                    "id": "moonshotai/moonlight-16b-a3b-instruct:free",
                    "name": "Moonlight 16B (Free)",
                    "context_length": 8192,
                    "pricing": {"prompt": 0.0, "completion": 0.0}
                },
                "openai/gpt-3.5-turbo": {
                    "id": "openai/gpt-3.5-turbo",
                    "name": "GPT-3.5 Turbo",
                    "context_length": 4096,
                    "pricing": {"prompt": 0.0015, "completion": 0.002}
                }
            }

def generate(
    prompt: str,
    system_prompt: Optional[str] = None,
    model: str = "moonshotai/moonlight-16b-a3b-instruct:free",
    temperature: float = 0.7,
    max_tokens: int = 2000,
    top_p: float = 0.95,
    frequency_penalty: float = 0.0,
    presence_penalty: float = 0.0,
    stop: Optional[List[str]] = None,
    api_key: Optional[str] = None,
    **kwargs
) -> str:
    """
    Generate text using the OpenRouter API.

    This is a convenience function that creates an OpenRouterProvider instance
    and calls its generate method.

    Args:
        prompt: Prompt to generate from.
        system_prompt: System prompt to use.
        model: Model to use for generation.
        temperature: Temperature for generation.
        max_tokens: Maximum number of tokens to generate.
        top_p: Top-p sampling parameter.
        frequency_penalty: Frequency penalty parameter.
        presence_penalty: Presence penalty parameter.
        stop: List of strings to stop generation at.
        api_key: OpenRouter API key. If None, will try to get from environment variable.
        **kwargs: Additional parameters to pass to the API.

    Returns:
        Generated text.
    """
    provider = OpenRouterProvider(
        api_key=api_key,
        model=model,
        temperature=temperature,
        max_tokens=max_tokens,
        top_p=top_p,
        frequency_penalty=frequency_penalty,
        presence_penalty=presence_penalty,
        **kwargs
    )

    return provider.generate(
        prompt=prompt,
        system_prompt=system_prompt,
        stop=stop,
        **kwargs
    )
