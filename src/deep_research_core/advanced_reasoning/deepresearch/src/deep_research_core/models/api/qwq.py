"""
QwQ API provider for Deep Research Core.
"""

import os
import logging
import json
import requests
from typing import Dict, Any, List, Optional, Union, Callable

from ...config import API_PROVIDERS

logger = logging.getLogger(__name__)

class QwQProvider:
    """
    Handles interactions with the QwQ API.
    """

    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the QwQ provider.

        Args:
            api_key: QwQ API key. If None, will try to get from environment.
        """
        self.provider_config = API_PROVIDERS.get("qwq", {})
        self.api_key = api_key or os.getenv(self.provider_config.get("api_key_env", "QWQ_API_KEY"))

        if not self.api_key:
            logger.warning("QwQ API key not found. Set QWQ_API_KEY environment variable.")

        self.base_url = self.provider_config.get("base_url", "https://api.qwq.ai/v1")
        self.available_models = self.provider_config.get("models", {})

    def get_available_models(self) -> List[str]:
        """
        Returns a list of available models.
        """
        return list(self.available_models.keys())

    def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """
        Get information about a specific model.

        Args:
            model_name: Name of the model

        Returns:
            Dictionary containing model information
        """
        if model_name in self.available_models:
            model_info = self.available_models[model_name].copy()
            model_info["name"] = model_name
            model_info["provider"] = "qwq"
            return model_info

        raise ValueError(f"Model {model_name} not found in QwQ")

    def generate(
        self,
        prompt: str,
        model: str = "qwq-32b",
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        stream: bool = False,
        callback: Optional[Callable[[str], None]] = None
    ) -> Union[str, Any]:
        """
        Generate text using the QwQ API.

        Args:
            prompt: The prompt to generate from
            model: The model to use
            system_prompt: System prompt to use
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            stream: Whether to stream the response
            callback: Optional callback function for streaming

        Returns:
            Generated text or stream object
        """
        try:
            # Prepare the API request
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            # Prepare the messages
            messages = []

            # Add system prompt if provided
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})

            # Add user prompt
            messages.append({"role": "user", "content": prompt})

            # Prepare the request data
            data = {
                "model": model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "stream": stream
            }

            # Make the API request
            if stream:
                response = requests.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=data,
                    stream=True
                )
                response.raise_for_status()

                if callback:
                    full_response = ""
                    for line in response.iter_lines():
                        if line:
                            try:
                                line_data = json.loads(line.decode("utf-8").replace("data: ", ""))
                                if "choices" in line_data and len(line_data["choices"]) > 0:
                                    if "delta" in line_data["choices"][0] and "content" in line_data["choices"][0]["delta"]:
                                        content = line_data["choices"][0]["delta"]["content"]
                                        if content:
                                            full_response += content
                                            callback(content)
                            except (json.JSONDecodeError, KeyError):
                                continue

                    return full_response
                else:
                    return response
            else:
                response = requests.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=data
                )
                response.raise_for_status()

                response_data = response.json()
                if "choices" in response_data and len(response_data["choices"]) > 0:
                    if "message" in response_data["choices"][0] and "content" in response_data["choices"][0]["message"]:
                        return response_data["choices"][0]["message"]["content"]

                return ""

        except Exception as e:
            logger.error(f"Error generating text with QwQ: {str(e)}")
            raise

# Create a singleton instance
qwq_provider = QwQProvider() 