"""
Google AI (Gemini) API provider for Deep Research Core.
"""

import os
import logging
import json
from typing import Dict, Any, List, Optional, Union, Callable

from ...config import API_PROVIDERS

logger = logging.getLogger(__name__)

class GoogleProvider:
    """
    Handles interactions with the Google AI (Gemini) API.
    """

    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the Google provider.

        Args:
            api_key: Google API key. If None, will try to get from environment.
        """
        self.provider_config = API_PROVIDERS.get("google", {})
        self.api_key = api_key or os.getenv(self.provider_config.get("api_key_env", "GOOGLE_API_KEY"))

        if not self.api_key:
            logger.warning("Google API key not found. Set GOOGLE_API_KEY environment variable.")

        self.base_url = self.provider_config.get("base_url", "https://generativelanguage.googleapis.com/v1")
        self.available_models = self.provider_config.get("models", {})

    def get_available_models(self) -> List[str]:
        """
        Returns a list of available models.
        """
        return list(self.available_models.keys())

    def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """
        Get information about a specific model.

        Args:
            model_name: Name of the model

        Returns:
            Dictionary containing model information
        """
        if model_name in self.available_models:
            model_info = self.available_models[model_name].copy()
            model_info["name"] = model_name
            model_info["provider"] = "google"
            return model_info

        raise ValueError(f"Model {model_name} not found in Google AI")

    def generate(
        self,
        prompt: str,
        model: str = "gemini-pro",
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        stream: bool = False,
        callback: Optional[Callable[[str], None]] = None
    ) -> Union[str, Any]:
        """
        Generate text using the Google AI API.

        Args:
            prompt: The prompt to generate from
            model: The model to use
            system_prompt: System prompt to use
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            stream: Whether to stream the response
            callback: Optional callback function for streaming

        Returns:
            Generated text or stream object
        """
        try:
            # Import the Google AI library
            try:
                import google.generativeai as genai
            except ImportError:
                raise ImportError("Google GenerativeAI package not installed. Please install it with 'pip install google-generativeai'.")

            # Configure the API
            genai.configure(api_key=self.api_key)

            # Set up the generation config
            generation_config = {
                "temperature": temperature,
                "max_output_tokens": max_tokens,
                "top_p": 0.95,
                "top_k": 40,
            }

            # Set up the safety settings
            safety_settings = [
                {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
            ]

            # Create the model
            model_obj = genai.GenerativeModel(
                model_name=model,
                generation_config=generation_config,
                safety_settings=safety_settings
            )

            # Prepare the prompt
            content = []

            # Add system prompt if provided
            if system_prompt:
                content.append({"role": "system", "parts": [system_prompt]})

            # Add user prompt
            content.append({"role": "user", "parts": [prompt]})

            # Generate the response
            if stream:
                response = model_obj.generate_content(content, stream=True)

                if callback:
                    full_response = ""
                    for chunk in response:
                        if hasattr(chunk, 'text'):
                            text = chunk.text
                            full_response += text
                            callback(text)

                    return full_response
                else:
                    return response
            else:
                response = model_obj.generate_content(content)
                return response.text

        except Exception as e:
            logger.error(f"Error generating text with Google AI: {str(e)}")
            raise

# Create a singleton instance
google_provider = GoogleProvider()
