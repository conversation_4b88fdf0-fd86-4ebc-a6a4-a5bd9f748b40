"""
Base class for API providers in Deep Research Core.

This module defines the abstract base class for all API providers,
providing a common interface for different implementations.
"""

import abc
import os
from typing import Dict, Any, List, Optional, Union, Callable

from ...utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

class BaseAPIProvider(abc.ABC):
    """
    Abstract base class for all API providers.
    
    This class defines the common interface that all API providers should follow,
    making it easier to switch between different implementations or add new ones.
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        api_base: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize the BaseAPIProvider.
        
        Args:
            api_key: API key for the provider. If None, will try to get from environment.
            api_base: Base URL for the API. If None, will use the default.
            **kwargs: Additional implementation-specific arguments
        """
        self.api_key = api_key
        self.api_base = api_base
        self.available_models = {}
        
    @abc.abstractmethod
    def generate(
        self,
        prompt: str,
        model: Optional[str] = None,
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        stream: bool = False,
        **kwargs
    ) -> Union[str, Callable]:
        """
        Generate text based on the prompt.
        
        Args:
            prompt: The prompt to generate text from
            model: The model to use (if None, will use the default)
            system_prompt: Optional system prompt
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            stream: Whether to stream the response
            **kwargs: Additional implementation-specific arguments
            
        Returns:
            Generated text or a generator function if stream=True
        """
        pass
    
    @abc.abstractmethod
    def get_embedding(
        self,
        text: str,
        model: Optional[str] = None,
        **kwargs
    ) -> List[float]:
        """
        Get embedding for the given text.
        
        Args:
            text: The text to get embedding for
            model: The model to use (if None, will use the default)
            **kwargs: Additional implementation-specific arguments
            
        Returns:
            Embedding as a list of floats
        """
        pass
    
    @abc.abstractmethod
    def get_available_models(self) -> Dict[str, Any]:
        """
        Get available models for this provider.
        
        Returns:
            Dictionary of available models with their details
        """
        pass
