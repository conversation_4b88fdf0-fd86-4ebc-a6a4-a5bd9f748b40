"""
OpenAI API provider for Deep Research Core.

This module provides a client for the OpenAI API, which allows access to
various OpenAI models.
"""

import os
import json
import time
from typing import Dict, Any, List, Optional, Union, Callable

from ..base import BaseAPIProvider
from ....utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

class OpenAIProvider(BaseAPIProvider):
    """
    OpenAI API provider for Deep Research Core.

    This class provides a client for the OpenAI API, which allows access to
    various OpenAI models.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        model: str = "gpt-4o",
        temperature: float = 0.7,
        max_tokens: int = 2000,
        top_p: float = 0.95,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        timeout: int = 120,
        embedding_model: str = "text-embedding-ada-002",
        **kwargs
    ):
        """
        Initialize the OpenAI API provider.

        Args:
            api_key: OpenAI API key. If None, will try to get from environment variable.
            model: Model to use for generation.
            temperature: Temperature for generation.
            max_tokens: Maximum number of tokens to generate.
            top_p: Top-p sampling parameter.
            frequency_penalty: Frequency penalty parameter.
            presence_penalty: Presence penalty parameter.
            timeout: Timeout for API requests in seconds.
            embedding_model: Model to use for embeddings.
            **kwargs: Additional parameters to pass to the API.
        """
        super().__init__()

        # Set API key
        self.api_key = api_key or os.environ.get("OPENAI_API_KEY")
        if not self.api_key:
            logger.warning("OpenAI API key not found. Set OPENAI_API_KEY environment variable.")

        # Set model parameters
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.top_p = top_p
        self.frequency_penalty = frequency_penalty
        self.presence_penalty = presence_penalty
        self.timeout = timeout
        self.embedding_model = embedding_model
        self.kwargs = kwargs

        # Try to import OpenAI
        try:
            import openai
            self.openai_available = True
            if self.api_key:
                self.client = openai.OpenAI(api_key=self.api_key)
            else:
                self.client = None
        except ImportError:
            logger.warning("OpenAI Python package not found. Install with 'pip install openai'.")
            self.openai_available = False
            self.client = None

    def generate(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        top_p: Optional[float] = None,
        frequency_penalty: Optional[float] = None,
        presence_penalty: Optional[float] = None,
        stop: Optional[List[str]] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[str, Any]:
        """
        Generate text using the OpenAI API.

        Args:
            prompt: Prompt to generate from.
            system_prompt: System prompt to use.
            temperature: Temperature for generation. If None, use default.
            max_tokens: Maximum number of tokens to generate. If None, use default.
            top_p: Top-p sampling parameter. If None, use default.
            frequency_penalty: Frequency penalty parameter. If None, use default.
            presence_penalty: Presence penalty parameter. If None, use default.
            stop: List of strings to stop generation at.
            stream: Whether to stream the response.
            **kwargs: Additional parameters to pass to the API.

        Returns:
            Generated text or stream object if stream=True.
        """
        # Check if API key is set
        if not self.api_key:
            raise ValueError("OpenAI API key not found. Set OPENAI_API_KEY environment variable.")

        # Check if OpenAI is available
        if not self.openai_available:
            raise ImportError("OpenAI Python package not found. Install with 'pip install openai'.")

        # Set parameters
        params = {
            "model": self.model,
            "temperature": temperature if temperature is not None else self.temperature,
            "max_tokens": max_tokens if max_tokens is not None else self.max_tokens,
            "top_p": top_p if top_p is not None else self.top_p,
            "frequency_penalty": frequency_penalty if frequency_penalty is not None else self.frequency_penalty,
            "presence_penalty": presence_penalty if presence_penalty is not None else self.presence_penalty,
            "stream": stream,
            **self.kwargs,
            **kwargs
        }

        # Set messages
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        # Add stop sequences if provided
        if stop:
            params["stop"] = stop

        # Make request
        try:
            response = self.client.chat.completions.create(
                messages=messages,
                **params
            )

            # Return stream if requested
            if stream:
                return response

            # Extract generated text
            generated_text = response.choices[0].message.content

            return generated_text
        except Exception as e:
            logger.error(f"Error generating text with OpenAI: {str(e)}")
            raise

    def get_embedding(self, text: str, model: Optional[str] = None) -> List[float]:
        """
        Get embedding for text using the OpenAI API.

        Args:
            text: Text to get embedding for.
            model: Model to use for embeddings. If None, use default.

        Returns:
            Embedding vector as a list of floats.
        """
        # Check if API key is set
        if not self.api_key:
            raise ValueError("OpenAI API key not found. Set OPENAI_API_KEY environment variable.")

        # Check if OpenAI is available
        if not self.openai_available:
            raise ImportError("OpenAI Python package not found. Install with 'pip install openai'.")

        # Use specified model or default
        embedding_model = model or self.embedding_model

        try:
            # Make request
            response = self.client.embeddings.create(
                input=text,
                model=embedding_model
            )

            # Extract embedding
            embedding = response.data[0].embedding

            return embedding
        except Exception as e:
            logger.error(f"Error getting embedding with OpenAI: {str(e)}")
            # Return a random embedding as fallback (1536 dimensions is standard for OpenAI embeddings)
            import numpy as np
            return list(np.random.rand(1536))

    def get_available_models(self) -> Dict[str, Any]:
        """
        Get available models from OpenAI API.

        Returns:
            Dictionary of available models with their details
        """
        # Check if API key is set
        if not self.api_key:
            raise ValueError("OpenAI API key not found. Set OPENAI_API_KEY environment variable.")

        # Check if OpenAI is available
        if not self.openai_available:
            raise ImportError("OpenAI Python package not found. Install with 'pip install openai'.")

        try:
            # Make request
            response = self.client.models.list()

            # Extract models
            models = {}
            for model in response.data:
                model_id = model.id
                models[model_id] = {
                    "id": model_id,
                    "created": model.created,
                    "owned_by": model.owned_by
                }

            return models
        except Exception as e:
            logger.error(f"Error getting available models from OpenAI: {str(e)}")
            # Return a minimal set of models as fallback
            return {
                "gpt-4o": {
                    "id": "gpt-4o",
                    "created": 1714503870,
                    "owned_by": "openai"
                },
                "gpt-3.5-turbo": {
                    "id": "gpt-3.5-turbo",
                    "created": 1677610602,
                    "owned_by": "openai"
                },
                "text-embedding-ada-002": {
                    "id": "text-embedding-ada-002",
                    "created": 1671217299,
                    "owned_by": "openai"
                }
            }

def generate(
    prompt: str,
    system_prompt: Optional[str] = None,
    model: str = "gpt-4o",
    temperature: float = 0.7,
    max_tokens: int = 2000,
    top_p: float = 0.95,
    frequency_penalty: float = 0.0,
    presence_penalty: float = 0.0,
    stop: Optional[List[str]] = None,
    api_key: Optional[str] = None,
    **kwargs
) -> str:
    """
    Generate text using the OpenAI API.

    This is a convenience function that creates an OpenAIProvider instance
    and calls its generate method.

    Args:
        prompt: Prompt to generate from.
        system_prompt: System prompt to use.
        model: Model to use for generation.
        temperature: Temperature for generation.
        max_tokens: Maximum number of tokens to generate.
        top_p: Top-p sampling parameter.
        frequency_penalty: Frequency penalty parameter.
        presence_penalty: Presence penalty parameter.
        stop: List of strings to stop generation at.
        api_key: OpenAI API key. If None, will try to get from environment variable.
        **kwargs: Additional parameters to pass to the API.

    Returns:
        Generated text.
    """
    provider = OpenAIProvider(
        api_key=api_key,
        model=model,
        temperature=temperature,
        max_tokens=max_tokens,
        top_p=top_p,
        frequency_penalty=frequency_penalty,
        presence_penalty=presence_penalty,
        **kwargs
    )

    return provider.generate(
        prompt=prompt,
        system_prompt=system_prompt,
        stop=stop,
        **kwargs
    )
