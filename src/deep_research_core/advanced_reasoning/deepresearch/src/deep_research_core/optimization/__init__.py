"""
Optimization module for Deep Research Core.

This module provides various optimization techniques for fine-tuning and
adapting language models, including LoRA, QLoRA, and other PEFT methods.
It also includes tools for parallel processing, quantization, memory optimization,
and other performance enhancements.
"""

from . import lora
from . import adapter
from . import prefix_tuning
from . import prompt_tuning
from . import ia3
from . import parallel
from . import mixed_precision
from . import quantization
from . import memory
from .peft_benchmark import PEFTBenchmark

__all__ = [
    # PEFT methods
    'lora',
    'adapter',
    'prefix_tuning',
    'prompt_tuning',
    'ia3',

    # Performance optimization
    'parallel',
    'mixed_precision',
    'quantization',
    'memory',

    # Benchmarking
    'PEFTBenchmark',
]