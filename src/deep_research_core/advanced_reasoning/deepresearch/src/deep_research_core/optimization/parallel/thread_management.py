"""
Thread Management module for parallel processing.

This module provides tools for managing thread pools and monitoring system
resources to optimize parallel processing performance.
"""

import threading
import time
import os
import psutil
import concurrent.futures
from typing import Dict, Any, Optional, Callable, List, Union, Tuple

from ...utils.structured_logging import get_logger

logger = get_logger(__name__)


class ResourceMonitor:
    """
    System resource monitor for adaptive thread management.
    
    This class periodically monitors CPU, memory, and GPU usage (if available),
    and calls a callback function with the current usage statistics.
    """
    
    def __init__(
        self,
        update_interval: float = 5.0,
        callback: Optional[Callable[[float, float, Optional[float]], None]] = None,
        monitor_gpu: bool = True
    ):
        """
        Initialize ResourceMonitor.
        
        Args:
            update_interval: Interval between updates in seconds
            callback: Callback function to call with resource usage data
            monitor_gpu: Whether to monitor GPU usage
        """
        self.update_interval = update_interval
        self.callback = callback
        self.monitor_gpu = monitor_gpu
        self._stop_event = threading.Event()
        self._thread = None
        self._process = psutil.Process(os.getpid())
        
        # GPU monitoring setup
        self.gpu_available = False
        if monitor_gpu:
            try:
                import torch
                self.gpu_available = torch.cuda.is_available()
                if self.gpu_available:
                    logger.info(f"GPU monitoring enabled: {torch.cuda.get_device_name(0)}")
                else:
                    logger.info("GPU monitoring requested but no CUDA-capable GPU available")
            except (ImportError, Exception) as e:
                logger.warning(f"Failed to initialize GPU monitoring: {e}")
    
    def start(self):
        """Start the resource monitoring thread."""
        if self._thread is not None and self._thread.is_alive():
            logger.warning("Resource monitor already running")
            return
            
        self._stop_event.clear()
        self._thread = threading.Thread(
            target=self._monitor_loop,
            name="ResourceMonitor",
            daemon=True
        )
        self._thread.start()
        logger.info(f"Resource monitor started (update_interval={self.update_interval}s)")
    
    def stop(self):
        """Stop the resource monitoring thread."""
        if self._thread is None or not self._thread.is_alive():
            logger.warning("Resource monitor not running")
            return
            
        self._stop_event.set()
        self._thread.join(timeout=2.0)
        if self._thread.is_alive():
            logger.warning("Resource monitor thread did not terminate cleanly")
        else:
            logger.info("Resource monitor stopped")
            
        self._thread = None
    
    def _monitor_loop(self):
        """Main monitoring loop."""
        while not self._stop_event.is_set():
            try:
                # Get CPU and memory usage
                cpu_usage = psutil.cpu_percent(interval=0.1)
                memory_usage = psutil.virtual_memory().percent
                
                # Get process-specific usage
                process_cpu = self._process.cpu_percent() / os.cpu_count()
                process_memory = self._process.memory_percent()
                
                # Get GPU usage if available
                gpu_usage = None
                if self.monitor_gpu and self.gpu_available:
                    try:
                        import torch
                        # This is a simple estimate - in a real implementation,
                        # you would use a more accurate method to get GPU usage
                        gpu_memory_allocated = torch.cuda.memory_allocated(0)
                        gpu_memory_reserved = torch.cuda.memory_reserved(0)
                        gpu_usage = (gpu_memory_allocated / max(1, gpu_memory_reserved)) * 100.0
                    except Exception as e:
                        logger.debug(f"Error getting GPU usage: {e}")
                
                # Log resource usage
                gpu_str = f"{gpu_usage:.1f}%" if gpu_usage is not None else "N/A"
                logger.debug(
                    f"System resources - CPU: {cpu_usage:.1f}%, Memory: {memory_usage:.1f}%, "
                    f"Process CPU: {process_cpu:.1f}%, Process Memory: {process_memory:.1f}%, "
                    f"GPU: {gpu_str}"
                )
                
                # Call the callback if provided
                if self.callback:
                    self.callback(cpu_usage, memory_usage, gpu_usage)
                    
            except Exception as e:
                logger.error(f"Error in resource monitor: {e}")
                
            # Wait for the next update
            self._stop_event.wait(self.update_interval)
    
    def get_current_usage(self) -> Dict[str, float]:
        """
        Get current resource usage.
        
        Returns:
            Dictionary with current CPU, memory, and GPU usage
        """
        try:
            cpu_usage = psutil.cpu_percent(interval=0.1)
            memory_usage = psutil.virtual_memory().percent
            
            result = {
                "cpu_usage": cpu_usage,
                "memory_usage": memory_usage,
                "process_cpu": self._process.cpu_percent() / os.cpu_count(),
                "process_memory": self._process.memory_percent()
            }
            
            if self.monitor_gpu and self.gpu_available:
                try:
                    import torch
                    gpu_memory_allocated = torch.cuda.memory_allocated(0)
                    gpu_memory_reserved = torch.cuda.memory_reserved(0)
                    result["gpu_usage"] = (gpu_memory_allocated / max(1, gpu_memory_reserved)) * 100.0
                except Exception:
                    result["gpu_usage"] = None
                    
            return result
            
        except Exception as e:
            logger.error(f"Error getting resource usage: {e}")
            return {
                "cpu_usage": 0.0,
                "memory_usage": 0.0,
                "process_cpu": 0.0,
                "process_memory": 0.0,
                "gpu_usage": None
            }


class AdaptiveThreadPool:
    """
    Thread pool that can dynamically adjust the number of worker threads.
    
    This class extends the functionality of ThreadPoolExecutor to allow
    dynamic adjustment of the number of worker threads based on system load
    or other criteria.
    """
    
    def __init__(
        self,
        base_workers: int = 4,
        min_workers: int = 1,
        max_workers: int = 16,
        thread_name_prefix: str = "AdaptiveWorker"
    ):
        """
        Initialize AdaptiveThreadPool.
        
        Args:
            base_workers: Initial number of worker threads
            min_workers: Minimum number of worker threads
            max_workers: Maximum number of worker threads
            thread_name_prefix: Prefix for worker thread names
        """
        self.base_workers = base_workers
        self.min_workers = min_workers
        self.max_workers = max_workers
        self.thread_name_prefix = thread_name_prefix
        
        self.current_workers = base_workers
        self._lock = threading.RLock()
        self._executor = concurrent.futures.ThreadPoolExecutor(
            max_workers=base_workers,
            thread_name_prefix=thread_name_prefix
        )
        
        # Task tracking
        self._active_tasks = 0
        self._completed_tasks = 0
        self._task_durations = []
        self._max_task_history = 100
        
        logger.info(
            f"Initialized AdaptiveThreadPool with workers={base_workers} "
            f"(min={min_workers}, max={max_workers})"
        )
    
    def submit(self, fn, *args, **kwargs) -> concurrent.futures.Future:
        """
        Submit a task to the thread pool.
        
        Args:
            fn: Function to execute
            *args: Arguments to pass to the function
            **kwargs: Keyword arguments to pass to the function
            
        Returns:
            Future object representing the task
        """
        with self._lock:
            self._active_tasks += 1
            
        # Wrap the function to track task completion
        def task_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = fn(*args, **kwargs)
                return result
            finally:
                end_time = time.time()
                duration = end_time - start_time
                self._task_completed(duration)
        
        # Submit the wrapped task
        return self._executor.submit(task_wrapper, *args, **kwargs)
    
    def _task_completed(self, duration: float):
        """
        Track task completion.
        
        Args:
            duration: Time taken to complete the task in seconds
        """
        with self._lock:
            self._active_tasks -= 1
            self._completed_tasks += 1
            
            # Update task duration history
            self._task_durations.append(duration)
            if len(self._task_durations) > self._max_task_history:
                self._task_durations.pop(0)
    
    def update_workers(self, num_workers: int):
        """
        Update the number of worker threads.
        
        Args:
            num_workers: New number of worker threads
        """
        # Ensure the number of workers is within bounds
        num_workers = max(self.min_workers, min(self.max_workers, num_workers))
        
        with self._lock:
            if num_workers == self.current_workers:
                return
                
            # Need to create a new executor with the updated worker count
            old_executor = self._executor
            self._executor = concurrent.futures.ThreadPoolExecutor(
                max_workers=num_workers,
                thread_name_prefix=self.thread_name_prefix
            )
            
            # Update current workers
            old_workers = self.current_workers
            self.current_workers = num_workers
            
            logger.info(f"Updated thread pool workers: {old_workers} -> {num_workers}")
            
        # Shut down the old executor without waiting for tasks to complete
        # as they will be completed by the new executor
        old_executor.shutdown(wait=False)
    
    def shutdown(self, wait: bool = True):
        """
        Shut down the thread pool.
        
        Args:
            wait: Whether to wait for pending tasks to complete
        """
        logger.info(f"Shutting down AdaptiveThreadPool (wait={wait})")
        self._executor.shutdown(wait=wait)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get thread pool statistics.
        
        Returns:
            Dictionary of thread pool statistics
        """
        with self._lock:
            avg_duration = (
                sum(self._task_durations) / max(1, len(self._task_durations))
                if self._task_durations else 0.0
            )
            
            return {
                "current_workers": self.current_workers,
                "active_tasks": self._active_tasks,
                "completed_tasks": self._completed_tasks,
                "avg_task_duration": avg_duration,
                "min_workers": self.min_workers,
                "max_workers": self.max_workers
            }


class WorkloadBalancer:
    """
    Workload balancer for distributing tasks across multiple resources.
    
    This class helps to distribute tasks across multiple processing units
    (threads, processes, or even machines) based on their capabilities and
    current workload.
    """
    
    def __init__(
        self,
        resources: List[Dict[str, Any]],
        strategy: str = "round_robin",
        adaptive: bool = True
    ):
        """
        Initialize WorkloadBalancer.
        
        Args:
            resources: List of resources with their capabilities
            strategy: Distribution strategy ("round_robin", "least_loaded", "weighted")
            adaptive: Whether to adaptively adjust distribution based on performance
        """
        self.resources = resources
        self.strategy = strategy
        self.adaptive = adaptive
        
        self._lock = threading.RLock()
        self._next_resource_idx = 0
        self._task_counts = [0] * len(resources)
        self._resource_performance = [1.0] * len(resources)
        
        # Validate and initialize strategy
        valid_strategies = ["round_robin", "least_loaded", "weighted"]
        if strategy not in valid_strategies:
            logger.warning(
                f"Invalid strategy '{strategy}', defaulting to 'round_robin'. "
                f"Valid strategies: {valid_strategies}"
            )
            self.strategy = "round_robin"
            
        logger.info(
            f"Initialized WorkloadBalancer with {len(resources)} resources "
            f"using '{self.strategy}' strategy"
        )
    
    def get_next_resource(self) -> Tuple[int, Dict[str, Any]]:
        """
        Get the next resource to use for a task.
        
        Returns:
            Tuple of (resource_index, resource)
        """
        with self._lock:
            if self.strategy == "round_robin":
                idx = self._next_resource_idx
                self._next_resource_idx = (self._next_resource_idx + 1) % len(self.resources)
            elif self.strategy == "least_loaded":
                idx = self._task_counts.index(min(self._task_counts))
            elif self.strategy == "weighted":
                # Higher performance score = lower task count / performance ratio
                weighted_loads = [
                    self._task_counts[i] / max(0.001, self._resource_performance[i])
                    for i in range(len(self.resources))
                ]
                idx = weighted_loads.index(min(weighted_loads))
            else:
                # Fallback to round robin
                idx = self._next_resource_idx
                self._next_resource_idx = (self._next_resource_idx + 1) % len(self.resources)
                
            # Update task count for the selected resource
            self._task_counts[idx] += 1
            
            return idx, self.resources[idx]
    
    def task_completed(self, resource_idx: int, duration: float):
        """
        Update resource statistics when a task is completed.
        
        Args:
            resource_idx: Index of the resource that completed the task
            duration: Time taken to complete the task in seconds
        """
        with self._lock:
            if 0 <= resource_idx < len(self._task_counts):
                self._task_counts[resource_idx] -= 1
                
                # Update performance metrics if adaptive balancing is enabled
                if self.adaptive and 0 <= resource_idx < len(self._resource_performance):
                    # Simple exponential moving average for performance
                    alpha = 0.2  # Weight for the new observation
                    perf = 1.0 / max(0.001, duration)  # Performance is inverse of duration
                    self._resource_performance[resource_idx] = (
                        alpha * perf + 
                        (1 - alpha) * self._resource_performance[resource_idx]
                    )
    
    def add_resource(self, resource: Dict[str, Any]):
        """
        Add a new resource to the balancer.
        
        Args:
            resource: Resource information
        """
        with self._lock:
            self.resources.append(resource)
            self._task_counts.append(0)
            self._resource_performance.append(1.0)
            
        logger.info(f"Added new resource, now managing {len(self.resources)} resources")
    
    def remove_resource(self, resource_idx: int) -> Optional[Dict[str, Any]]:
        """
        Remove a resource from the balancer.
        
        Args:
            resource_idx: Index of the resource to remove
            
        Returns:
            The removed resource, or None if the index is invalid
        """
        with self._lock:
            if 0 <= resource_idx < len(self.resources):
                resource = self.resources.pop(resource_idx)
                self._task_counts.pop(resource_idx)
                self._resource_performance.pop(resource_idx)
                
                # Adjust next resource index if needed
                if self._next_resource_idx >= len(self.resources) and len(self.resources) > 0:
                    self._next_resource_idx = 0
                    
                logger.info(f"Removed resource, now managing {len(self.resources)} resources")
                return resource
                
        return None
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get workload balancer statistics.
        
        Returns:
            Dictionary of workload balancer statistics
        """
        with self._lock:
            return {
                "num_resources": len(self.resources),
                "strategy": self.strategy,
                "adaptive": self.adaptive,
                "task_counts": self._task_counts.copy(),
                "resource_performance": self._resource_performance.copy(),
                "total_active_tasks": sum(self._task_counts)
            } 