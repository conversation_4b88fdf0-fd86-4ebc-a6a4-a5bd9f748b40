"""
Parallel Training module for optimizing model training.

This module provides tools for running model training in parallel
to improve throughput and efficiency, with support for distributed
training and adaptive resource management.
"""

import time
import threading
import queue
import concurrent.futures
from typing import List, Dict, Any, Optional, Callable, Union, Tuple
import numpy as np
from tqdm import tqdm
import torch

from ...utils.structured_logging import get_logger
from ...models.base import BaseModel
from .thread_management import AdaptiveThreadPool, ResourceMonitor

logger = get_logger(__name__)


class ParallelTraining:
    """
    Parallel training engine for running model training across multiple processes.
    
    This class provides functionality to run training on language models in parallel
    using multiple processes, with support for adaptive resource allocation and
    gradient accumulation for improved efficiency.
    """
    
    def __init__(
        self,
        model: BaseModel,
        max_workers: int = 4,
        batch_size: int = 8,
        adaptive: bool = True,
        device_map: Optional[Dict[str, str]] = None,
        timeout: float = 600.0,
        use_distributed: bool = False,
        gradient_accumulation_steps: int = 1,
        mixed_precision: bool = True,
        memory_efficient: bool = True,
    ):
        """
        Initialize ParallelTraining.
        
        Args:
            model: The model to train
            max_workers: Maximum number of worker processes
            batch_size: Batch size for training
            adaptive: Whether to adaptively adjust process count based on system load
            device_map: Optional device mapping for model components
            timeout: Timeout for training operations in seconds
            use_distributed: Whether to use distributed training (DDP)
            gradient_accumulation_steps: Number of steps to accumulate gradients
            mixed_precision: Whether to use mixed precision training
            memory_efficient: Whether to optimize for memory efficiency
        """
        self.model = model
        self.max_workers = max_workers
        self.batch_size = batch_size
        self.adaptive = adaptive
        self.device_map = device_map
        self.timeout = timeout
        self.use_distributed = use_distributed
        self.gradient_accumulation_steps = gradient_accumulation_steps
        self.mixed_precision = mixed_precision
        self.memory_efficient = memory_efficient
        
        # Initialize process pool for distributed training
        if use_distributed:
            # For distributed training, we'll use multiprocessing
            import torch.multiprocessing as mp
            self.process_pool = mp.Pool(max_workers)
            self.is_distributed = True
            logger.info(f"Initialized distributed training with {max_workers} processes")
        else:
            # For non-distributed training, we'll use threads for data loading
            if adaptive:
                self.thread_pool = AdaptiveThreadPool(
                    base_workers=max_workers,
                    max_workers=max_workers * 2,
                    min_workers=1
                )
                self.resource_monitor = ResourceMonitor(
                    update_interval=5.0,
                    callback=self._update_resource_allocation
                )
                self.resource_monitor.start()
            else:
                self.thread_pool = concurrent.futures.ThreadPoolExecutor(
                    max_workers=max_workers
                )
            self.is_distributed = False
            logger.info(f"Initialized parallel training with {max_workers} workers")
            
        # Initialize training stats
        self.stats = {
            "total_batches": 0,
            "completed_batches": 0,
            "failed_batches": 0,
            "avg_batch_time": 0.0,
            "avg_loss": 0.0,
            "learning_rate": 0.0,
            "epochs_completed": 0,
        }
        
        # Set up locks and queues
        self._lock = threading.RLock()
        self._batch_queue = queue.Queue()
        
        if device_map:
            self._setup_device_mapping()
            
        if mixed_precision and torch.cuda.is_available():
            self._setup_mixed_precision()
            
        logger.info(
            f"Initialized ParallelTraining with max_workers={max_workers}, "
            f"batch_size={batch_size}, adaptive={adaptive}, "
            f"distributed={use_distributed}, "
            f"gradient_accumulation_steps={gradient_accumulation_steps}"
        )
    
    def _setup_device_mapping(self):
        """Set up device mapping for model components."""
        if not hasattr(self.model, "device_map") and self.device_map:
            logger.warning("Model does not support device mapping, ignoring device_map")
            return
            
        try:
            if hasattr(self.model, "device_map"):
                self.model.device_map = self.device_map
                logger.info(f"Applied device mapping: {self.device_map}")
        except Exception as e:
            logger.error(f"Failed to apply device mapping: {e}")
    
    def _setup_mixed_precision(self):
        """Set up mixed precision training."""
        if not torch.cuda.is_available():
            logger.warning("CUDA not available, disabling mixed precision")
            self.mixed_precision = False
            return
            
        try:
            # Set up mixed precision training
            if hasattr(torch.cuda, "amp") and self.mixed_precision:
                self.scaler = torch.cuda.amp.GradScaler()
                logger.info("Mixed precision training enabled")
            else:
                self.mixed_precision = False
                logger.info("Mixed precision training disabled")
        except Exception as e:
            logger.error(f"Failed to set up mixed precision: {e}")
            self.mixed_precision = False
    
    def _update_resource_allocation(self, cpu_usage: float, memory_usage: float, gpu_usage: Optional[float] = None):
        """
        Update thread pool size based on system resource usage.
        
        Args:
            cpu_usage: Current CPU usage (0-100)
            memory_usage: Current memory usage (0-100)
            gpu_usage: Current GPU usage if available (0-100)
        """
        if not self.adaptive or self.is_distributed:
            return
            
        with self._lock:
            # Simple adaptive strategy based on CPU and GPU usage
            if gpu_usage is not None and gpu_usage > 95:
                new_workers = max(1, self.thread_pool.current_workers // 2)
                logger.warning(f"High GPU usage ({gpu_usage}%), reducing workers to {new_workers}")
                self.thread_pool.update_workers(new_workers)
            elif cpu_usage > 90:
                new_workers = max(1, self.thread_pool.current_workers // 2)
                logger.warning(f"High CPU usage ({cpu_usage}%), reducing workers to {new_workers}")
                self.thread_pool.update_workers(new_workers)
            elif (gpu_usage is None or gpu_usage < 70) and cpu_usage < 70 and self.thread_pool.current_workers < self.max_workers:
                new_workers = min(self.max_workers, self.thread_pool.current_workers + 1)
                logger.info(f"Low resource usage (CPU: {cpu_usage}%, GPU: {gpu_usage}%), increasing workers to {new_workers}")
                self.thread_pool.update_workers(new_workers)
    
    def train(
        self,
        train_dataset: List[Dict[str, Any]],
        validation_dataset: Optional[List[Dict[str, Any]]] = None,
        epochs: int = 3,
        learning_rate: float = 5e-5,
        optimizer_type: str = "adamw",
        weight_decay: float = 0.01,
        warmup_steps: int = 0,
        max_grad_norm: float = 1.0,
        show_progress: bool = True,
        save_path: Optional[str] = None,
        save_steps: Optional[int] = None,
        eval_steps: Optional[int] = None,
        callback: Optional[Callable[[Dict[str, Any]], None]] = None,
        model_kwargs: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Train the model on a dataset.
        
        Args:
            train_dataset: Training dataset
            validation_dataset: Optional validation dataset
            epochs: Number of training epochs
            learning_rate: Learning rate
            optimizer_type: Type of optimizer to use
            weight_decay: Weight decay for regularization
            warmup_steps: Number of warmup steps
            max_grad_norm: Maximum gradient norm for clipping
            show_progress: Whether to show a progress bar
            save_path: Path to save model checkpoints
            save_steps: Save model every N steps
            eval_steps: Evaluate model every N steps
            callback: Optional callback function to call with training stats
            model_kwargs: Additional keyword arguments to pass to the model
            
        Returns:
            Dictionary of training statistics
        """
        model_kwargs = model_kwargs or {}
        
        # Prepare model for training
        if hasattr(self.model, "prepare_for_training"):
            self.model.prepare_for_training(
                learning_rate=learning_rate,
                optimizer_type=optimizer_type,
                weight_decay=weight_decay,
                warmup_steps=warmup_steps
            )
        
        # Create data batches
        train_batches = self._create_batches(train_dataset, self.batch_size)
        num_batches = len(train_batches)
        steps_per_epoch = num_batches
        total_steps = steps_per_epoch * epochs
        
        logger.info(f"Training on {len(train_dataset)} examples in {num_batches} batches for {epochs} epochs")
        logger.info(f"Total training steps: {total_steps}")
        
        # Initialize training state
        global_step = 0
        best_eval_loss = float('inf')
        training_losses = []
        
        # Create progress bar if needed
        epoch_pbar = tqdm(total=epochs, desc="Epochs") if show_progress else None
        step_pbar = tqdm(total=total_steps, desc="Steps") if show_progress else None
        
        # Training loop
        start_time = time.time()
        
        for epoch in range(epochs):
            epoch_start = time.time()
            
            # Shuffle batches for each epoch
            np.random.shuffle(train_batches)
            
            # Process each batch
            for batch_idx, batch in enumerate(train_batches):
                try:
                    # Process batch based on training mode
                    if self.is_distributed:
                        # For distributed training, we use the process pool
                        batch_result = self._process_batch_distributed(
                            batch, 
                            global_step, 
                            max_grad_norm,
                            model_kwargs
                        )
                    else:
                        # For non-distributed training, we process the batch directly
                        batch_result = self._process_batch(
                            batch, 
                            global_step, 
                            max_grad_norm,
                            model_kwargs
                        )
                    
                    # Extract batch statistics
                    batch_loss = batch_result.get("loss", 0.0)
                    batch_time = batch_result.get("batch_time", 0.0)
                    
                    # Update training statistics
                    with self._lock:
                        self.stats["total_batches"] += 1
                        self.stats["completed_batches"] += 1
                        self.stats["avg_batch_time"] = (
                            (self.stats["avg_batch_time"] * (self.stats["completed_batches"] - 1) + batch_time) / 
                            self.stats["completed_batches"]
                        )
                        self.stats["avg_loss"] = (
                            (self.stats["avg_loss"] * (self.stats["completed_batches"] - 1) + batch_loss) / 
                            self.stats["completed_batches"]
                        )
                        self.stats["learning_rate"] = batch_result.get("learning_rate", learning_rate)
                    
                    # Track losses
                    training_losses.append(batch_loss)
                    
                    # Update progress
                    if step_pbar:
                        step_pbar.update(1)
                        step_pbar.set_postfix({
                            "loss": f"{batch_loss:.4f}",
                            "avg_loss": f"{self.stats['avg_loss']:.4f}",
                            "lr": f"{self.stats['learning_rate']:.6f}"
                        })
                    
                    # Increment global step
                    global_step += 1
                    
                    # Evaluate if needed
                    if validation_dataset and eval_steps and global_step % eval_steps == 0:
                        eval_results = self.evaluate(
                            validation_dataset,
                            show_progress=False,
                            model_kwargs=model_kwargs
                        )
                        
                        eval_loss = eval_results.get("loss", float('inf'))
                        
                        # Update best model if needed
                        if eval_loss < best_eval_loss and save_path:
                            best_eval_loss = eval_loss
                            self._save_model(f"{save_path}_best", global_step, best_eval_loss)
                            logger.info(f"New best model saved with eval loss: {best_eval_loss:.4f}")
                        
                        # Log evaluation results
                        logger.info(
                            f"Evaluation at step {global_step}: "
                            f"loss={eval_loss:.4f}, "
                            f"perplexity={eval_results.get('perplexity', 0.0):.2f}"
                        )
                    
                    # Save checkpoint if needed
                    if save_path and save_steps and global_step % save_steps == 0:
                        self._save_model(f"{save_path}_step{global_step}", global_step, self.stats["avg_loss"])
                        logger.info(f"Model checkpoint saved at step {global_step}")
                    
                    # Call callback if provided
                    if callback:
                        callback({
                            "step": global_step,
                            "epoch": epoch + 1,
                            "loss": batch_loss,
                            "avg_loss": self.stats["avg_loss"],
                            "learning_rate": self.stats["learning_rate"],
                            "batch_time": batch_time
                        })
                        
                except Exception as e:
                    logger.error(f"Error processing batch {batch_idx} in epoch {epoch + 1}: {e}")
                    with self._lock:
                        self.stats["failed_batches"] += 1
            
            # Update epoch progress
            epoch_time = time.time() - epoch_start
            with self._lock:
                self.stats["epochs_completed"] += 1
            
            if epoch_pbar:
                epoch_pbar.update(1)
                epoch_pbar.set_postfix({
                    "avg_loss": f"{self.stats['avg_loss']:.4f}",
                    "time": f"{epoch_time:.1f}s"
                })
            
            logger.info(
                f"Epoch {epoch + 1}/{epochs} completed in {epoch_time:.2f}s "
                f"(avg loss: {self.stats['avg_loss']:.4f})"
            )
            
            # Save epoch checkpoint if needed
            if save_path:
                self._save_model(f"{save_path}_epoch{epoch + 1}", global_step, self.stats["avg_loss"])
                logger.info(f"Model checkpoint saved after epoch {epoch + 1}")
        
        # Final evaluation
        if validation_dataset:
            final_eval = self.evaluate(
                validation_dataset,
                show_progress=show_progress,
                model_kwargs=model_kwargs
            )
            logger.info(
                f"Final evaluation: "
                f"loss={final_eval.get('loss', 0.0):.4f}, "
                f"perplexity={final_eval.get('perplexity', 0.0):.2f}"
            )
        
        # Save final model
        if save_path:
            self._save_model(f"{save_path}_final", global_step, self.stats["avg_loss"])
            logger.info("Final model saved")
        
        # Close progress bars
        if epoch_pbar:
            epoch_pbar.close()
        if step_pbar:
            step_pbar.close()
        
        # Calculate training time
        total_time = time.time() - start_time
        
        # Update final stats
        final_stats = self.get_stats()
        final_stats.update({
            "total_time": total_time,
            "steps_per_second": global_step / max(0.001, total_time),
            "final_loss": self.stats["avg_loss"],
            "best_eval_loss": best_eval_loss if validation_dataset else None,
            "training_losses": training_losses
        })
        
        logger.info(
            f"Training completed in {total_time:.2f}s "
            f"({global_step / max(0.001, total_time):.2f} steps/s)"
        )
        
        return final_stats
    
    def evaluate(
        self,
        eval_dataset: List[Dict[str, Any]],
        show_progress: bool = True,
        model_kwargs: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Evaluate the model on a dataset.
        
        Args:
            eval_dataset: Evaluation dataset
            show_progress: Whether to show a progress bar
            model_kwargs: Additional keyword arguments to pass to the model
            
        Returns:
            Dictionary of evaluation statistics
        """
        model_kwargs = model_kwargs or {}
        
        # Create data batches
        eval_batches = self._create_batches(eval_dataset, self.batch_size)
        num_batches = len(eval_batches)
        
        logger.info(f"Evaluating on {len(eval_dataset)} examples in {num_batches} batches")
        
        # Initialize evaluation state
        eval_losses = []
        
        # Create progress bar if needed
        pbar = tqdm(total=num_batches, desc="Evaluation") if show_progress else None
        
        # Evaluation loop
        start_time = time.time()
        
        for batch_idx, batch in enumerate(eval_batches):
            try:
                # Process batch for evaluation
                with torch.no_grad():
                    if hasattr(self.model, "evaluate_batch"):
                        batch_result = self.model.evaluate_batch(batch, **model_kwargs)
                    else:
                        # Default evaluation logic
                        batch_result = {
                            "loss": 0.0,
                            "batch_time": 0.0
                        }
                        logger.warning("Model does not have evaluate_batch method, returning default values")
                
                # Extract batch statistics
                batch_loss = batch_result.get("loss", 0.0)
                
                # Track losses
                eval_losses.append(batch_loss)
                
                # Update progress
                if pbar:
                    pbar.update(1)
                    pbar.set_postfix({"loss": f"{batch_loss:.4f}"})
                    
            except Exception as e:
                logger.error(f"Error evaluating batch {batch_idx}: {e}")
        
        # Close progress bar
        if pbar:
            pbar.close()
        
        # Calculate evaluation time
        eval_time = time.time() - start_time
        
        # Calculate statistics
        avg_loss = sum(eval_losses) / max(1, len(eval_losses))
        perplexity = torch.exp(torch.tensor(avg_loss)).item() if avg_loss < 20 else float('inf')
        
        eval_stats = {
            "loss": avg_loss,
            "perplexity": perplexity,
            "eval_time": eval_time,
            "batches_per_second": num_batches / max(0.001, eval_time)
        }
        
        logger.info(
            f"Evaluation completed in {eval_time:.2f}s "
            f"(loss: {avg_loss:.4f}, perplexity: {perplexity:.2f})"
        )
        
        return eval_stats
    
    def _create_batches(self, items: List[Any], batch_size: int) -> List[List[Any]]:
        """
        Create batches from a list of items.
        
        Args:
            items: List of items to batch
            batch_size: Size of each batch
            
        Returns:
            List of batches
        """
        return [items[i:i+batch_size] for i in range(0, len(items), batch_size)]
    
    def _process_batch(
        self,
        batch: List[Dict[str, Any]],
        global_step: int,
        max_grad_norm: float,
        model_kwargs: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Process a single training batch.
        
        Args:
            batch: List of inputs in the batch
            global_step: Current global step
            max_grad_norm: Maximum gradient norm for clipping
            model_kwargs: Additional arguments for the model
            
        Returns:
            Dictionary of batch statistics
        """
        try:
            batch_start = time.time()
            
            # Check if model has a specialized batch training method
            if hasattr(self.model, "train_batch"):
                # Use the model's specialized method
                if self.mixed_precision and hasattr(self, "scaler"):
                    with torch.cuda.amp.autocast():
                        batch_result = self.model.train_batch(
                            batch, 
                            global_step=global_step,
                            gradient_accumulation_steps=self.gradient_accumulation_steps,
                            max_grad_norm=max_grad_norm,
                            **model_kwargs
                        )
                else:
                    batch_result = self.model.train_batch(
                        batch, 
                        global_step=global_step,
                        gradient_accumulation_steps=self.gradient_accumulation_steps,
                        max_grad_norm=max_grad_norm,
                        **model_kwargs
                    )
            else:
                # Default implementation for models without specialized method
                logger.warning("Model does not have train_batch method, using default implementation")
                batch_result = {
                    "loss": 0.0,
                    "learning_rate": model_kwargs.get("learning_rate", 0.0)
                }
            
            batch_time = time.time() - batch_start
            batch_result["batch_time"] = batch_time
            
            return batch_result
            
        except Exception as e:
            logger.error(f"Error in batch processing: {e}")
            return {
                "loss": 0.0,
                "batch_time": 0.0,
                "learning_rate": model_kwargs.get("learning_rate", 0.0),
                "error": str(e)
            }
    
    def _process_batch_distributed(
        self,
        batch: List[Dict[str, Any]],
        global_step: int,
        max_grad_norm: float,
        model_kwargs: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Process a batch in distributed training mode.
        
        Args:
            batch: List of inputs in the batch
            global_step: Current global step
            max_grad_norm: Maximum gradient norm for clipping
            model_kwargs: Additional arguments for the model
            
        Returns:
            Dictionary of batch statistics
        """
        try:
            # This is a placeholder for distributed training
            # In a real implementation, we would use torch.distributed
            logger.warning("Distributed training is not fully implemented yet")
            
            # For now, just call the regular process_batch method
            return self._process_batch(batch, global_step, max_grad_norm, model_kwargs)
            
        except Exception as e:
            logger.error(f"Error in distributed batch processing: {e}")
            return {
                "loss": 0.0,
                "batch_time": 0.0,
                "learning_rate": model_kwargs.get("learning_rate", 0.0),
                "error": str(e)
            }
    
    def _save_model(self, path: str, step: int, loss: float):
        """
        Save model checkpoint.
        
        Args:
            path: Path to save the model
            step: Current training step
            loss: Current loss value
        """
        try:
            if hasattr(self.model, "save"):
                metadata = {
                    "step": step,
                    "loss": loss,
                    "timestamp": time.time(),
                    "stats": self.stats
                }
                self.model.save(path, metadata)
            else:
                logger.warning("Model does not have save method, checkpoint not saved")
        except Exception as e:
            logger.error(f"Error saving model: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get current training statistics.
        
        Returns:
            Dictionary of training statistics
        """
        with self._lock:
            return self.stats.copy()
    
    def shutdown(self):
        """Shut down the training engine and release resources."""
        if hasattr(self, "resource_monitor") and self.resource_monitor:
            self.resource_monitor.stop()
            
        if hasattr(self, "thread_pool"):
            if isinstance(self.thread_pool, AdaptiveThreadPool):
                self.thread_pool.shutdown()
            else:
                self.thread_pool.shutdown(wait=True)
        
        if hasattr(self, "process_pool"):
            self.process_pool.close()
            self.process_pool.join()
            
        logger.info("ParallelTraining shutdown complete")


class DistributedTrainer:
    """
    Utility for distributed training across multiple nodes or GPUs.
    
    This class handles distributed training setup and coordination
    using PyTorch's Distributed Data Parallel (DDP).
    """
    
    def __init__(
        self,
        model_initializer: Callable[[], BaseModel],
        world_size: int = 1,
        backend: str = "nccl",
        init_method: str = "env://",
        find_unused_parameters: bool = False
    ):
        """
        Initialize DistributedTrainer.
        
        Args:
            model_initializer: Function that returns a model instance
            world_size: Number of processes to use
            backend: Distributed backend ("nccl" for GPU, "gloo" for CPU)
            init_method: Initialization method for process group
            find_unused_parameters: Whether to find unused parameters in DDP
        """
        self.model_initializer = model_initializer
        self.world_size = world_size
        self.backend = backend
        self.init_method = init_method
        self.find_unused_parameters = find_unused_parameters
        
        # Check if CUDA is available
        self.use_cuda = torch.cuda.is_available()
        if not self.use_cuda and backend == "nccl":
            logger.warning("CUDA not available, falling back to 'gloo' backend")
            self.backend = "gloo"
        
        logger.info(
            f"Initialized DistributedTrainer with world_size={world_size}, "
            f"backend={self.backend}, cuda={self.use_cuda}"
        )
    
    def train(
        self,
        train_dataset: List[Dict[str, Any]],
        validation_dataset: Optional[List[Dict[str, Any]]] = None,
        **training_kwargs
    ) -> Dict[str, Any]:
        """
        Start distributed training.
        
        Args:
            train_dataset: Training dataset
            validation_dataset: Optional validation dataset
            **training_kwargs: Additional arguments for training
            
        Returns:
            Dictionary of training statistics
        """
        import torch.multiprocessing as mp
        import torch.distributed as dist
        
        # Initialize process group
        if self.world_size > 1:
            mp.spawn(
                self._train_worker,
                args=(train_dataset, validation_dataset, training_kwargs),
                nprocs=self.world_size,
                join=True
            )
            
            # Return stats from rank 0
            if hasattr(self, "rank0_stats"):
                return self.rank0_stats
            else:
                logger.warning("No stats collected from rank 0")
                return {}
        else:
            # Single process training
            return self._train_worker(0, train_dataset, validation_dataset, training_kwargs)
    
    def _train_worker(
        self,
        rank: int,
        train_dataset: List[Dict[str, Any]],
        validation_dataset: Optional[List[Dict[str, Any]]],
        training_kwargs: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Worker function for distributed training.
        
        Args:
            rank: Process rank
            train_dataset: Training dataset
            validation_dataset: Optional validation dataset
            training_kwargs: Additional arguments for training
            
        Returns:
            Dictionary of training statistics
        """
        import torch.distributed as dist
        
        # Initialize process group
        if self.world_size > 1:
            dist.init_process_group(
                backend=self.backend,
                init_method=self.init_method,
                world_size=self.world_size,
                rank=rank
            )
        
        # Set device
        device = torch.device(f"cuda:{rank}" if self.use_cuda else "cpu")
        
        # Initialize model
        model = self.model_initializer()
        model.to(device)
        
        # Wrap model with DDP if using multiple processes
        if self.world_size > 1:
            from torch.nn.parallel import DistributedDataParallel as DDP
            model = DDP(
                model,
                device_ids=[rank] if self.use_cuda else None,
                output_device=rank if self.use_cuda else None,
                find_unused_parameters=self.find_unused_parameters
            )
        
        # Create parallel training engine
        trainer = ParallelTraining(
            model=model,
            max_workers=1,  # Each process uses 1 worker
            adaptive=False,  # No adaptive adjustment in distributed mode
            use_distributed=True
        )
        
        # Train the model
        stats = trainer.train(
            train_dataset=train_dataset,
            validation_dataset=validation_dataset,
            **training_kwargs
        )
        
        # Clean up
        trainer.shutdown()
        if self.world_size > 1:
            dist.destroy_process_group()
        
        # Save stats from rank 0
        if rank == 0:
            self.rank0_stats = stats
        
        return stats
