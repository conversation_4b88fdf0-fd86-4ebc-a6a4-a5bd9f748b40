"""
Workload Distribution module for optimizing parallel processing.

This module provides tools for distributing workloads across multiple
processing units, with support for dynamic load balancing and resource-aware
task scheduling.
"""

import time
import threading
import queue
import concurrent.futures
from typing import List, Dict, Any, Optional, Callable, Union, Tuple, Set
import numpy as np
from tqdm import tqdm

from ...utils.structured_logging import get_logger
from .thread_management import Adapt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ResourceMonitor, WorkloadBalancer

logger = get_logger(__name__)


class ResourceAwareScheduler:
    """
    Resource-aware scheduler for optimizing task execution.

    This class schedules tasks based on available system resources,
    prioritizing tasks that can make the most efficient use of
    the current resource state.
    """

    def __init__(
        self,
        resource_monitor: Optional["ResourceMonitor"] = None,
        cpu_threshold: float = 80.0,
        memory_threshold: float = 80.0,
        gpu_threshold: float = 80.0,
        update_interval: float = 2.0
    ):
        """
        Initialize ResourceAwareScheduler.

        Args:
            resource_monitor: ResourceMonitor instance to use
            cpu_threshold: CPU usage threshold for scheduling decisions
            memory_threshold: Memory usage threshold for scheduling decisions
            gpu_threshold: GPU usage threshold for scheduling decisions
            update_interval: Interval between scheduler updates in seconds
        """
        self.cpu_threshold = cpu_threshold
        self.memory_threshold = memory_threshold
        self.gpu_threshold = gpu_threshold
        self.update_interval = update_interval

        # Initialize resource monitor if not provided
        if resource_monitor is None:
            self.resource_monitor = ResourceMonitor(
                update_interval=update_interval,
                callback=self._update_resource_state
            )
            self.resource_monitor.start()
        else:
            self.resource_monitor = resource_monitor

        # Initialize resource state
        self._lock = threading.RLock()
        self._resource_state = {
            "cpu_available": True,
            "memory_available": True,
            "gpu_available": True,
            "cpu_usage": 0.0,
            "memory_usage": 0.0,
            "gpu_usage": 0.0
        }

        # Initialize task queues
        self._cpu_intensive_tasks = queue.PriorityQueue()
        self._memory_intensive_tasks = queue.PriorityQueue()
        self._gpu_intensive_tasks = queue.PriorityQueue()
        self._balanced_tasks = queue.PriorityQueue()

        # Initialize worker thread
        self._stop_event = threading.Event()
        self._scheduler_thread = threading.Thread(
            target=self._scheduler_loop,
            name="ResourceScheduler",
            daemon=True
        )
        self._scheduler_thread.start()

        # Task tracking
        self._task_counter = 0
        self._active_tasks = {}  # task_id -> task_info
        self._completed_tasks = {}  # task_id -> result

        logger.info(
            f"Initialized ResourceAwareScheduler with thresholds: "
            f"CPU={cpu_threshold}%, Memory={memory_threshold}%, GPU={gpu_threshold}%"
        )

    def _update_resource_state(self, cpu_usage: float, memory_usage: float, gpu_usage: Optional[float] = None):
        """
        Update resource state based on current usage.

        Args:
            cpu_usage: Current CPU usage (0-100)
            memory_usage: Current memory usage (0-100)
            gpu_usage: Current GPU usage if available (0-100)
        """
        with self._lock:
            self._resource_state["cpu_usage"] = cpu_usage
            self._resource_state["memory_usage"] = memory_usage
            if gpu_usage is not None:
                self._resource_state["gpu_usage"] = gpu_usage

            # Update availability flags
            self._resource_state["cpu_available"] = cpu_usage < self.cpu_threshold
            self._resource_state["memory_available"] = memory_usage < self.memory_threshold
            self._resource_state["gpu_available"] = gpu_usage is None or gpu_usage < self.gpu_threshold

            logger.debug(
                f"Resource state updated - CPU: {cpu_usage:.1f}% "
                f"({'available' if self._resource_state['cpu_available'] else 'limited'}), "
                f"Memory: {memory_usage:.1f}% "
                f"({'available' if self._resource_state['memory_available'] else 'limited'}), "
                f"GPU: {gpu_usage if gpu_usage is not None else 'N/A'} "
                f"({'available' if self._resource_state['gpu_available'] else 'limited'})"
            )

    def _scheduler_loop(self):
        """Main scheduler loop."""
        while not self._stop_event.is_set():
            try:
                # Get current resource state
                with self._lock:
                    resource_state = self._resource_state.copy()

                # Determine which tasks to schedule based on resource state
                tasks_to_schedule = []

                # Always schedule balanced tasks if possible
                if not self._balanced_tasks.empty():
                    try:
                        priority, task_id, task_info = self._balanced_tasks.get_nowait()
                        tasks_to_schedule.append((task_id, task_info))
                    except queue.Empty:
                        pass

                # Schedule GPU tasks if GPU is available
                if resource_state["gpu_available"] and not self._gpu_intensive_tasks.empty():
                    try:
                        priority, task_id, task_info = self._gpu_intensive_tasks.get_nowait()
                        tasks_to_schedule.append((task_id, task_info))
                    except queue.Empty:
                        pass

                # Schedule CPU tasks if CPU is available
                if resource_state["cpu_available"] and not self._cpu_intensive_tasks.empty():
                    try:
                        priority, task_id, task_info = self._cpu_intensive_tasks.get_nowait()
                        tasks_to_schedule.append((task_id, task_info))
                    except queue.Empty:
                        pass

                # Schedule memory tasks if memory is available
                if resource_state["memory_available"] and not self._memory_intensive_tasks.empty():
                    try:
                        priority, task_id, task_info = self._memory_intensive_tasks.get_nowait()
                        tasks_to_schedule.append((task_id, task_info))
                    except queue.Empty:
                        pass

                # Execute scheduled tasks
                for task_id, task_info in tasks_to_schedule:
                    self._execute_task(task_id, task_info)

                # Sleep for a short time
                time.sleep(0.1)

            except Exception as e:
                logger.error(f"Error in scheduler loop: {e}")
                time.sleep(1.0)  # Avoid tight loop on error

    def _execute_task(self, task_id: int, task_info: Dict[str, Any]):
        """
        Execute a task.

        Args:
            task_id: ID of the task to execute
            task_info: Task information
        """
        # Extract task information
        task_fn = task_info["task_fn"]
        args = task_info["args"]
        kwargs = task_info["kwargs"]

        # Execute task in a separate thread
        def task_wrapper():
            try:
                logger.debug(f"Executing task {task_id}")
                result = task_fn(*args, **kwargs)

                # Store result
                with self._lock:
                    self._completed_tasks[task_id] = result
                    if task_id in self._active_tasks:
                        del self._active_tasks[task_id]

                logger.debug(f"Task {task_id} completed successfully")
                return result
            except Exception as e:
                logger.error(f"Error executing task {task_id}: {e}")

                # Mark task as failed
                with self._lock:
                    self._completed_tasks[task_id] = None
                    if task_id in self._active_tasks:
                        del self._active_tasks[task_id]

                # Re-raise the exception
                raise

        # Start task thread
        thread = threading.Thread(
            target=task_wrapper,
            name=f"Task-{task_id}",
            daemon=True
        )
        thread.start()

    def submit_task(
        self,
        task_fn: Callable,
        *args,
        resource_profile: str = "balanced",
        priority: int = 0,
        **kwargs
    ) -> int:
        """
        Submit a task for execution.

        Args:
            task_fn: Function to execute
            *args: Arguments to pass to the function
            resource_profile: Resource profile of the task
                ("cpu", "memory", "gpu", "balanced")
            priority: Task priority (lower value = higher priority)
            **kwargs: Keyword arguments to pass to the function

        Returns:
            Task ID
        """
        with self._lock:
            task_id = self._task_counter
            self._task_counter += 1

            # Store task information
            task_info = {
                "task_fn": task_fn,
                "args": args,
                "kwargs": kwargs,
                "resource_profile": resource_profile,
                "priority": priority,
                "submit_time": time.time()
            }

            self._active_tasks[task_id] = task_info

            # Add task to appropriate queue based on resource profile
            if resource_profile == "cpu":
                self._cpu_intensive_tasks.put((priority, task_id, task_info))
            elif resource_profile == "memory":
                self._memory_intensive_tasks.put((priority, task_id, task_info))
            elif resource_profile == "gpu":
                self._gpu_intensive_tasks.put((priority, task_id, task_info))
            else:  # balanced or unknown
                self._balanced_tasks.put((priority, task_id, task_info))

            logger.debug(
                f"Submitted task {task_id} with resource profile '{resource_profile}' "
                f"and priority {priority}"
            )

            return task_id

    def get_task_result(self, task_id: int, wait: bool = False, timeout: Optional[float] = None) -> Any:
        """
        Get the result of a task.

        Args:
            task_id: ID of the task
            wait: Whether to wait for the task to complete
            timeout: Maximum time to wait in seconds

        Returns:
            Task result, or None if the task has not completed
        """
        # If not waiting, return result immediately if available
        if not wait:
            return self._completed_tasks.get(task_id)

        # Wait for task to complete
        start_time = time.time()
        while True:
            # Check if task has completed
            if task_id in self._completed_tasks:
                return self._completed_tasks[task_id]

            # Check if we've timed out
            if timeout is not None and time.time() - start_time > timeout:
                logger.warning(f"Timeout waiting for task {task_id}")
                return None

            # Sleep briefly to avoid tight loop
            time.sleep(0.1)

    def get_resource_state(self) -> Dict[str, Any]:
        """
        Get current resource state.

        Returns:
            Dictionary of resource state
        """
        with self._lock:
            return self._resource_state.copy()

    def get_stats(self) -> Dict[str, Any]:
        """
        Get scheduler statistics.

        Returns:
            Dictionary of scheduler statistics
        """
        with self._lock:
            return {
                "active_tasks": len(self._active_tasks),
                "completed_tasks": len(self._completed_tasks),
                "cpu_queue_size": self._cpu_intensive_tasks.qsize(),
                "memory_queue_size": self._memory_intensive_tasks.qsize(),
                "gpu_queue_size": self._gpu_intensive_tasks.qsize(),
                "balanced_queue_size": self._balanced_tasks.qsize(),
                "resource_state": self._resource_state.copy()
            }

    def shutdown(self, wait: bool = True):
        """
        Shut down the scheduler.

        Args:
            wait: Whether to wait for pending tasks to complete
        """
        logger.info(f"Shutting down ResourceAwareScheduler (wait={wait})")

        # Stop scheduler thread
        self._stop_event.set()
        self._scheduler_thread.join(timeout=2.0)

        # Stop resource monitor
        if self.resource_monitor:
            self.resource_monitor.stop()

        # Wait for active tasks to complete if requested
        if wait:
            active_task_ids = list(self._active_tasks.keys())
            for task_id in active_task_ids:
                logger.debug(f"Waiting for task {task_id} to complete")
                self.get_task_result(task_id, wait=True, timeout=10.0)

        logger.info("ResourceAwareScheduler shutdown complete")


class WorkloadDistributor:
    """
    Workload distributor for parallel processing.

    This class distributes tasks across multiple processing units based on
    their capabilities and current workload, with support for dynamic
    load balancing and resource-aware scheduling.
    """

    def __init__(
        self,
        num_workers: int = 4,
        strategy: str = "adaptive",
        resource_aware: bool = True,
        task_priority: bool = True,
        max_queue_size: int = 1000,
        monitor_interval: float = 5.0
    ):
        """
        Initialize WorkloadDistributor.

        Args:
            num_workers: Number of worker threads/processes
            strategy: Distribution strategy ("round_robin", "least_loaded", "weighted", "adaptive")
            resource_aware: Whether to consider resource usage in scheduling
            task_priority: Whether to support task priorities
            max_queue_size: Maximum size of the task queue
            monitor_interval: Resource monitoring interval in seconds
        """
        self.num_workers = num_workers
        self.strategy = strategy
        self.resource_aware = resource_aware
        self.task_priority = task_priority
        self.max_queue_size = max_queue_size
        self.monitor_interval = monitor_interval

        # Initialize thread pool
        self.thread_pool = AdaptiveThreadPool(
            base_workers=num_workers,
            max_workers=num_workers * 2,
            min_workers=max(1, num_workers // 2)
        )

        # Initialize resource monitor if resource-aware
        if resource_aware:
            self.resource_monitor = ResourceMonitor(
                update_interval=monitor_interval,
                callback=self._update_resource_allocation
            )
            self.resource_monitor.start()
        else:
            self.resource_monitor = None

        # Initialize task queue
        if task_priority:
            self.task_queue = queue.PriorityQueue(maxsize=max_queue_size)
        else:
            self.task_queue = queue.Queue(maxsize=max_queue_size)

        # Initialize workload balancer
        resources = [{"id": i, "capacity": 1.0} for i in range(num_workers)]
        self.workload_balancer = WorkloadBalancer(
            resources=resources,
            strategy="weighted" if strategy == "adaptive" else strategy,
            adaptive=strategy == "adaptive"
        )

        # Initialize worker threads
        self._stop_event = threading.Event()
        self._workers = []
        self._start_workers()

        # Task tracking
        self._lock = threading.RLock()
        self._task_counter = 0
        self._active_tasks = set()
        self._completed_tasks = 0
        self._failed_tasks = 0
        self._task_durations = []
        self._max_task_history = 100

        logger.info(
            f"Initialized WorkloadDistributor with {num_workers} workers "
            f"using '{strategy}' strategy"
        )

    def _start_workers(self):
        """Start worker threads."""
        for i in range(self.num_workers):
            worker = threading.Thread(
                target=self._worker_loop,
                args=(i,),
                name=f"WorkloadWorker-{i}",
                daemon=True
            )
            worker.start()
            self._workers.append(worker)

    def _worker_loop(self, worker_id: int):
        """
        Main worker loop.

        Args:
            worker_id: ID of the worker thread
        """
        logger.info(f"Worker {worker_id} started")

        while not self._stop_event.is_set():
            try:
                # Get a task from the queue
                if self.task_priority:
                    # For priority queue, item is (priority, task_id, task_fn, args, kwargs)
                    try:
                        priority, task_id, task_fn, args, kwargs = self.task_queue.get(timeout=1.0)
                    except queue.Empty:
                        continue
                else:
                    # For regular queue, item is (task_id, task_fn, args, kwargs)
                    try:
                        task_id, task_fn, args, kwargs = self.task_queue.get(timeout=1.0)
                    except queue.Empty:
                        continue

                # Execute the task
                start_time = time.time()
                try:
                    logger.debug(f"Worker {worker_id} executing task {task_id}")
                    result = task_fn(*args, **kwargs)
                    success = True
                except Exception as e:
                    logger.error(f"Error executing task {task_id}: {e}")
                    result = None
                    success = False
                finally:
                    end_time = time.time()
                    duration = end_time - start_time

                    # Update task statistics
                    self._task_completed(task_id, success, duration)

                    # Update workload balancer
                    self.workload_balancer.task_completed(worker_id, duration)

                    # Mark task as done in the queue
                    self.task_queue.task_done()

            except Exception as e:
                logger.error(f"Error in worker {worker_id}: {e}")
                time.sleep(1.0)  # Avoid tight loop on error

        logger.info(f"Worker {worker_id} stopped")

    def _update_resource_allocation(self, cpu_usage: float, memory_usage: float, gpu_usage: Optional[float] = None):
        """
        Update worker allocation based on system resource usage.

        Args:
            cpu_usage: Current CPU usage (0-100)
            memory_usage: Current memory usage (0-100)
            gpu_usage: Current GPU usage if available (0-100)
        """
        if not self.resource_aware:
            return

        # Simple adaptive strategy based on CPU usage
        if cpu_usage > 90:
            new_workers = max(1, self.thread_pool.current_workers // 2)
            logger.warning(f"High CPU usage ({cpu_usage}%), reducing workers to {new_workers}")
            self.thread_pool.update_workers(new_workers)
        elif cpu_usage < 50 and self.thread_pool.current_workers < self.num_workers:
            new_workers = min(self.num_workers, self.thread_pool.current_workers + 1)
            logger.info(f"Low CPU usage ({cpu_usage}%), increasing workers to {new_workers}")
            self.thread_pool.update_workers(new_workers)

    def _task_completed(self, task_id: int, success: bool, duration: float):
        """
        Update task statistics when a task is completed.

        Args:
            task_id: ID of the completed task
            success: Whether the task completed successfully
            duration: Time taken to complete the task in seconds
        """
        with self._lock:
            if task_id in self._active_tasks:
                self._active_tasks.remove(task_id)

            if success:
                self._completed_tasks += 1
            else:
                self._failed_tasks += 1

            # Update task duration history
            self._task_durations.append(duration)
            if len(self._task_durations) > self._max_task_history:
                self._task_durations.pop(0)

    def submit(
        self,
        task_fn: Callable,
        *args,
        priority: int = 0,
        **kwargs
    ) -> int:
        """
        Submit a task for execution.

        Args:
            task_fn: Function to execute
            *args: Arguments to pass to the function
            priority: Task priority (lower value = higher priority)
            **kwargs: Keyword arguments to pass to the function

        Returns:
            Task ID
        """
        with self._lock:
            task_id = self._task_counter
            self._task_counter += 1
            self._active_tasks.add(task_id)

        # Add the task to the queue
        if self.task_priority:
            self.task_queue.put((priority, task_id, task_fn, args, kwargs))
        else:
            self.task_queue.put((task_id, task_fn, args, kwargs))

        logger.debug(f"Submitted task {task_id} with priority {priority}")

        return task_id

    def submit_batch(
        self,
        tasks: List[Tuple[Callable, List, Dict]],
        priorities: Optional[List[int]] = None
    ) -> List[int]:
        """
        Submit a batch of tasks for execution.

        Args:
            tasks: List of (task_fn, args, kwargs) tuples
            priorities: Optional list of priorities for each task

        Returns:
            List of task IDs
        """
        task_ids = []

        for i, (task_fn, args, kwargs) in enumerate(tasks):
            priority = priorities[i] if priorities and i < len(priorities) else 0
            task_id = self.submit(task_fn, *args, priority=priority, **kwargs)
            task_ids.append(task_id)

        logger.info(f"Submitted batch of {len(tasks)} tasks")

        return task_ids

    def wait_for_tasks(self, task_ids: Optional[List[int]] = None, timeout: Optional[float] = None) -> bool:
        """
        Wait for tasks to complete.

        Args:
            task_ids: List of task IDs to wait for, or None to wait for all tasks
            timeout: Maximum time to wait in seconds, or None to wait indefinitely

        Returns:
            True if all tasks completed, False if timeout occurred
        """
        if task_ids is None:
            # Wait for all tasks in the queue
            try:
                self.task_queue.join()
                return True
            except Exception as e:
                logger.error(f"Error waiting for all tasks: {e}")
                return False
        else:
            # Wait for specific tasks
            start_time = time.time()
            pending_tasks = set(task_ids)

            while pending_tasks:
                # Check if any of the tasks have completed
                with self._lock:
                    pending_tasks -= pending_tasks - self._active_tasks

                # Check if we've timed out
                if timeout is not None and time.time() - start_time > timeout:
                    logger.warning(f"Timeout waiting for tasks: {len(pending_tasks)} tasks still pending")
                    return False

                # Sleep briefly to avoid tight loop
                time.sleep(0.1)

            return True

    def cancel_task(self, task_id: int) -> bool:
        """
        Cancel a task if it hasn't started yet.

        Args:
            task_id: ID of the task to cancel

        Returns:
            True if the task was cancelled, False otherwise
        """
        # Note: This is a best-effort cancellation - if the task has already
        # been picked up by a worker, it won't be cancelled
        logger.warning("Task cancellation is not fully implemented")

        with self._lock:
            if task_id in self._active_tasks:
                self._active_tasks.remove(task_id)
                logger.info(f"Marked task {task_id} as cancelled")
                return True

        return False

    def get_task_status(self, task_id: int) -> str:
        """
        Get the status of a task.

        Args:
            task_id: ID of the task

        Returns:
            Task status ("pending", "completed", "failed", "unknown")
        """
        with self._lock:
            if task_id in self._active_tasks:
                return "pending"
            elif task_id < self._task_counter:
                # Task ID is valid, but not active, so it must have completed or failed
                # In a real implementation, we would track the status of each task
                return "completed"
            else:
                return "unknown"

    def get_stats(self) -> Dict[str, Any]:
        """
        Get workload distributor statistics.

        Returns:
            Dictionary of workload distributor statistics
        """
        with self._lock:
            avg_duration = (
                sum(self._task_durations) / max(1, len(self._task_durations))
                if self._task_durations else 0.0
            )

            stats = {
                "active_tasks": len(self._active_tasks),
                "completed_tasks": self._completed_tasks,
                "failed_tasks": self._failed_tasks,
                "avg_task_duration": avg_duration,
                "queue_size": self.task_queue.qsize(),
                "workers": self.thread_pool.current_workers
            }

            # Add workload balancer stats
            balancer_stats = self.workload_balancer.get_stats()
            stats.update({
                "balancer_" + k: v for k, v in balancer_stats.items()
            })

            # Add resource monitor stats if available
            if self.resource_monitor:
                resource_stats = self.resource_monitor.get_current_usage()
                stats.update({
                    "resource_" + k: v for k, v in resource_stats.items()
                })

            return stats

    def shutdown(self, wait: bool = True):
        """
        Shut down the workload distributor.

        Args:
            wait: Whether to wait for pending tasks to complete
        """
        logger.info(f"Shutting down WorkloadDistributor (wait={wait})")

        # Stop accepting new tasks
        self._stop_event.set()

        if wait:
            # Wait for all tasks to complete
            try:
                self.task_queue.join()
                logger.info("All tasks completed")
            except Exception as e:
                logger.error(f"Error waiting for tasks to complete: {e}")

        # Stop resource monitor if running
        if self.resource_monitor:
            self.resource_monitor.stop()

        # Shut down thread pool
        self.thread_pool.shutdown(wait=wait)

        # Wait for worker threads to terminate
        for worker in self._workers:
            worker.join(timeout=2.0)

        logger.info("WorkloadDistributor shutdown complete")


class TaskScheduler:
    """
    Task scheduler for managing complex task dependencies.

    This class manages the execution of tasks with dependencies,
    ensuring that tasks are executed in the correct order and
    that dependent tasks are only executed when their dependencies
    have completed successfully.
    """

    def __init__(
        self,
        workload_distributor: WorkloadDistributor,
        max_retries: int = 3,
        retry_delay: float = 1.0
    ):
        """
        Initialize TaskScheduler.

        Args:
            workload_distributor: WorkloadDistributor to use for task execution
            max_retries: Maximum number of retries for failed tasks
            retry_delay: Delay between retries in seconds
        """
        self.workload_distributor = workload_distributor
        self.max_retries = max_retries
        self.retry_delay = retry_delay

        self._lock = threading.RLock()
        self._tasks = {}  # task_id -> task_info
        self._dependencies = {}  # task_id -> set of dependency task_ids
        self._dependents = {}  # task_id -> set of dependent task_ids
        self._results = {}  # task_id -> task result
        self._status = {}  # task_id -> task status
        self._retries = {}  # task_id -> retry count

        logger.info(
            f"Initialized TaskScheduler with max_retries={max_retries}, "
            f"retry_delay={retry_delay}s"
        )

    def add_task(
        self,
        task_fn: Callable,
        *args,
        task_id: Optional[str] = None,
        dependencies: Optional[List[str]] = None,
        priority: int = 0,
        **kwargs
    ) -> str:
        """
        Add a task to the scheduler.

        Args:
            task_fn: Function to execute
            *args: Arguments to pass to the function
            task_id: Optional task ID (generated if not provided)
            dependencies: List of task IDs that this task depends on
            priority: Task priority (lower value = higher priority)
            **kwargs: Keyword arguments to pass to the function

        Returns:
            Task ID
        """
        with self._lock:
            # Generate task ID if not provided
            if task_id is None:
                task_id = f"task_{len(self._tasks)}"

            # Check if task ID already exists
            if task_id in self._tasks:
                raise ValueError(f"Task ID '{task_id}' already exists")

            # Store task information
            self._tasks[task_id] = {
                "task_fn": task_fn,
                "args": args,
                "kwargs": kwargs,
                "priority": priority
            }

            # Store dependencies
            dependencies = dependencies or []
            self._dependencies[task_id] = set(dependencies)

            # Update dependents
            for dep_id in dependencies:
                if dep_id not in self._dependents:
                    self._dependents[dep_id] = set()
                self._dependents[dep_id].add(task_id)

            # Initialize status and retries
            self._status[task_id] = "pending"
            self._retries[task_id] = 0

            logger.info(
                f"Added task '{task_id}' with {len(dependencies)} dependencies "
                f"and priority {priority}"
            )

            # Submit task if it has no dependencies
            if not dependencies:
                self._submit_task(task_id)

            return task_id

    def _submit_task(self, task_id: str):
        """
        Submit a task for execution.

        Args:
            task_id: ID of the task to submit
        """
        with self._lock:
            # Check if task exists
            if task_id not in self._tasks:
                logger.error(f"Cannot submit unknown task '{task_id}'")
                return

            # Check if task has unmet dependencies
            if any(
                dep_id not in self._results
                for dep_id in self._dependencies.get(task_id, set())
            ):
                logger.warning(f"Cannot submit task '{task_id}' with unmet dependencies")
                return

            # Get task information
            task_info = self._tasks[task_id]

            # Update status
            self._status[task_id] = "running"

            # Submit task to workload distributor
            self.workload_distributor.submit(
                self._execute_task,
                task_id,
                priority=task_info["priority"]
            )

            logger.debug(f"Submitted task '{task_id}' for execution")

    def _execute_task(self, task_id: str):
        """
        Execute a task and handle its result.

        Args:
            task_id: ID of the task to execute

        Returns:
            Task result
        """
        # Get task information
        task_info = self._tasks[task_id]
        task_fn = task_info["task_fn"]
        args = task_info["args"]
        kwargs = task_info["kwargs"]

        try:
            # Execute the task
            logger.debug(f"Executing task '{task_id}'")
            result = task_fn(*args, **kwargs)

            # Store result and update status
            with self._lock:
                self._results[task_id] = result
                self._status[task_id] = "completed"

                # Submit dependent tasks
                for dep_id in self._dependents.get(task_id, set()):
                    # Check if all dependencies of the dependent task are met
                    if all(
                        dep in self._results
                        for dep in self._dependencies.get(dep_id, set())
                    ):
                        self._submit_task(dep_id)

            logger.info(f"Task '{task_id}' completed successfully")
            return result

        except Exception as e:
            logger.error(f"Error executing task '{task_id}': {e}")

            # Handle retry logic
            with self._lock:
                retry_count = self._retries.get(task_id, 0)

                if retry_count < self.max_retries:
                    # Increment retry count
                    self._retries[task_id] = retry_count + 1

                    # Schedule retry after delay
                    logger.info(
                        f"Scheduling retry {retry_count + 1}/{self.max_retries} "
                        f"for task '{task_id}' after {self.retry_delay}s"
                    )

                    # In a real implementation, we would use a proper scheduler
                    # For simplicity, we'll just use a timer
                    timer = threading.Timer(
                        self.retry_delay,
                        self._submit_task,
                        args=(task_id,)
                    )
                    timer.daemon = True
                    timer.start()

                    self._status[task_id] = "retrying"
                else:
                    # Mark task as failed
                    self._status[task_id] = "failed"

                    # Mark dependent tasks as failed
                    for dep_id in self._dependents.get(task_id, set()):
                        self._status[dep_id] = "failed"

                    logger.error(
                        f"Task '{task_id}' failed after {retry_count} retries, "
                        f"marking {len(self._dependents.get(task_id, set()))} dependent tasks as failed"
                    )

            # Re-raise the exception
            raise

    def get_task_result(self, task_id: str, wait: bool = False, timeout: Optional[float] = None) -> Any:
        """
        Get the result of a task.

        Args:
            task_id: ID of the task
            wait: Whether to wait for the task to complete
            timeout: Maximum time to wait in seconds

        Returns:
            Task result, or None if the task has not completed or failed
        """
        # Check if task exists
        if task_id not in self._tasks:
            logger.error(f"Unknown task '{task_id}'")
            return None

        # If not waiting, return result immediately if available
        if not wait:
            return self._results.get(task_id)

        # Wait for task to complete
        start_time = time.time()
        while True:
            # Check if task has completed
            with self._lock:
                if task_id in self._results:
                    return self._results[task_id]

                # Check if task has failed
                if self._status.get(task_id) == "failed":
                    logger.warning(f"Task '{task_id}' failed")
                    return None

            # Check if we've timed out
            if timeout is not None and time.time() - start_time > timeout:
                logger.warning(f"Timeout waiting for task '{task_id}'")
                return None

            # Sleep briefly to avoid tight loop
            time.sleep(0.1)

    def get_task_status(self, task_id: str) -> str:
        """
        Get the status of a task.

        Args:
            task_id: ID of the task

        Returns:
            Task status ("pending", "running", "retrying", "completed", "failed", "unknown")
        """
        with self._lock:
            return self._status.get(task_id, "unknown")

    def get_all_results(self, wait: bool = False, timeout: Optional[float] = None) -> Dict[str, Any]:
        """
        Get the results of all completed tasks.

        Args:
            wait: Whether to wait for all tasks to complete
            timeout: Maximum time to wait in seconds

        Returns:
            Dictionary mapping task IDs to results
        """
        if not wait:
            with self._lock:
                return self._results.copy()

        # Wait for all tasks to complete
        start_time = time.time()
        while True:
            # Check if all tasks have completed or failed
            with self._lock:
                all_done = all(
                    status in ("completed", "failed")
                    for status in self._status.values()
                )

                if all_done:
                    return self._results.copy()

            # Check if we've timed out
            if timeout is not None and time.time() - start_time > timeout:
                logger.warning("Timeout waiting for all tasks to complete")
                with self._lock:
                    return self._results.copy()

            # Sleep briefly to avoid tight loop
            time.sleep(0.1)

    def get_stats(self) -> Dict[str, Any]:
        """
        Get scheduler statistics.

        Returns:
            Dictionary of scheduler statistics
        """
        with self._lock:
            # Count tasks by status
            status_counts = {}
            for status in self._status.values():
                status_counts[status] = status_counts.get(status, 0) + 1

            return {
                "total_tasks": len(self._tasks),
                "completed_tasks": len(self._results),
                "status_counts": status_counts,
                "retry_counts": self._retries.copy()
            }

    def shutdown(self, wait: bool = True):
        """
        Shut down the scheduler.

        Args:
            wait: Whether to wait for pending tasks to complete
        """
        logger.info(f"Shutting down TaskScheduler (wait={wait})")

        # The scheduler itself doesn't need to be shut down,
        # but we should shut down the workload distributor
        self.workload_distributor.shutdown(wait=wait)

        logger.info("TaskScheduler shutdown complete")
