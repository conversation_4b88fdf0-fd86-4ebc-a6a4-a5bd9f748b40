"""
Parallel Inference module for optimizing model inference.

This module provides tools for running model inference in parallel
to improve throughput and latency, with support for batch processing
and adaptive resource management.
"""

import time
import threading
import queue
import concurrent.futures
from typing import List, Dict, Any, Optional, Callable, Union, Tuple
import numpy as np
from tqdm import tqdm
import torch

from ...utils.structured_logging import get_logger
from ...models.base import BaseModel
from .thread_management import AdaptiveThreadPool, ResourceMonitor

logger = get_logger(__name__)


class ParallelInference:
    """
    Parallel inference engine for running model predictions across multiple threads.
    
    This class provides functionality to run inference on language models in parallel
    using a thread pool, with support for adaptive resource allocation and
    batch processing for improved throughput.
    """
    
    def __init__(
        self,
        model: BaseModel,
        max_workers: int = 4,
        batch_size: int = 1,
        adaptive: bool = True,
        device_map: Optional[Dict[str, str]] = None,
        timeout: float = 60.0,
        use_cuda_graphs: bool = False,
        memory_efficient: bool = True,
    ):
        """
        Initialize ParallelInference.
        
        Args:
            model: The model to run inference with
            max_workers: Maximum number of worker threads
            batch_size: Batch size for inference
            adaptive: Whether to adaptively adjust thread count based on system load
            device_map: Optional device mapping for model components
            timeout: Timeout for inference operations in seconds
            use_cuda_graphs: Whether to use CUDA graphs for optimizing repetitive inference
            memory_efficient: Whether to optimize for memory efficiency
        """
        self.model = model
        self.max_workers = max_workers
        self.batch_size = batch_size
        self.adaptive = adaptive
        self.device_map = device_map
        self.timeout = timeout
        self.use_cuda_graphs = use_cuda_graphs
        self.memory_efficient = memory_efficient
        
        # Initialize thread pool
        if adaptive:
            self.thread_pool = AdaptiveThreadPool(
                base_workers=max_workers,
                max_workers=max_workers * 2,
                min_workers=1
            )
            self.resource_monitor = ResourceMonitor(
                update_interval=5.0,
                callback=self._update_resource_allocation
            )
            self.resource_monitor.start()
        else:
            self.thread_pool = concurrent.futures.ThreadPoolExecutor(
                max_workers=max_workers
            )
            
        # Initialize inference stats
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "avg_latency": 0.0,
            "throughput": 0.0,
        }
        
        # Set up locks and queues
        self._lock = threading.RLock()
        self._request_queue = queue.Queue()
        self._result_cache = {}
        
        if device_map:
            self._setup_device_mapping()
            
        if use_cuda_graphs and torch.cuda.is_available():
            self._setup_cuda_graphs()
            
        logger.info(
            f"Initialized ParallelInference with max_workers={max_workers}, "
            f"batch_size={batch_size}, adaptive={adaptive}"
        )
    
    def _setup_device_mapping(self):
        """Set up device mapping for model components."""
        if not hasattr(self.model, "device_map") and self.device_map:
            logger.warning("Model does not support device mapping, ignoring device_map")
            return
            
        try:
            if hasattr(self.model, "device_map"):
                self.model.device_map = self.device_map
                logger.info(f"Applied device mapping: {self.device_map}")
        except Exception as e:
            logger.error(f"Failed to apply device mapping: {e}")
    
    def _setup_cuda_graphs(self):
        """Set up CUDA graphs for optimizing repetitive inference."""
        if not torch.cuda.is_available():
            logger.warning("CUDA not available, disabling CUDA graphs")
            self.use_cuda_graphs = False
            return
            
        try:
            # This is a placeholder for CUDA graph setup
            # Actual implementation would depend on the specific model and framework
            self.cuda_graphs_enabled = True
            logger.info("CUDA graphs initialized for inference optimization")
        except Exception as e:
            logger.error(f"Failed to set up CUDA graphs: {e}")
            self.use_cuda_graphs = False
    
    def _update_resource_allocation(self, cpu_usage: float, memory_usage: float, gpu_usage: Optional[float] = None):
        """
        Update thread pool size based on system resource usage.
        
        Args:
            cpu_usage: Current CPU usage (0-100)
            memory_usage: Current memory usage (0-100)
            gpu_usage: Current GPU usage if available (0-100)
        """
        if not self.adaptive:
            return
            
        with self._lock:
            # Simple adaptive strategy based on CPU usage
            if cpu_usage > 90:
                new_workers = max(1, self.thread_pool.current_workers // 2)
                logger.warning(f"High CPU usage ({cpu_usage}%), reducing workers to {new_workers}")
                self.thread_pool.update_workers(new_workers)
            elif cpu_usage < 50 and self.thread_pool.current_workers < self.max_workers:
                new_workers = min(self.max_workers, self.thread_pool.current_workers + 1)
                logger.info(f"Low CPU usage ({cpu_usage}%), increasing workers to {new_workers}")
                self.thread_pool.update_workers(new_workers)
    
    def infer(
        self,
        inputs: List[Dict[str, Any]],
        model_kwargs: Optional[Dict[str, Any]] = None,
        show_progress: bool = False,
        callback: Optional[Callable[[Dict[str, Any]], None]] = None
    ) -> List[Dict[str, Any]]:
        """
        Run inference on multiple inputs in parallel.
        
        Args:
            inputs: List of input data for model inference
            model_kwargs: Additional keyword arguments to pass to the model
            show_progress: Whether to show a progress bar
            callback: Optional callback function to call with each result
            
        Returns:
            List of inference results
        """
        model_kwargs = model_kwargs or {}
        num_inputs = len(inputs)
        results = [None] * num_inputs
        futures = []
        
        # Create batches
        batches = self._create_batches(inputs, self.batch_size)
        logger.info(f"Processing {num_inputs} inputs in {len(batches)} batches")
        
        # Submit batches to thread pool
        with self._lock:
            self.stats["total_requests"] += num_inputs
            
        start_time = time.time()
        
        # Create progress bar if needed
        pbar = tqdm(total=num_inputs) if show_progress else None
        
        # Submit each batch for processing
        for batch_idx, batch in enumerate(batches):
            if isinstance(self.thread_pool, AdaptiveThreadPool):
                future = self.thread_pool.submit(
                    self._process_batch,
                    batch,
                    batch_idx,
                    model_kwargs,
                    callback,
                    pbar
                )
            else:
                future = self.thread_pool.submit(
                    self._process_batch,
                    batch,
                    batch_idx,
                    model_kwargs,
                    callback,
                    pbar
                )
            futures.append(future)
        
        # Collect results
        for future in concurrent.futures.as_completed(futures):
            try:
                batch_results, batch_indices = future.result(timeout=self.timeout)
                for i, result in enumerate(batch_results):
                    if batch_indices[i] < num_inputs:
                        results[batch_indices[i]] = result
            except Exception as e:
                logger.error(f"Error in batch processing: {e}")
                with self._lock:
                    self.stats["failed_requests"] += 1
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        # Update stats
        with self._lock:
            self.stats["successful_requests"] += sum(1 for r in results if r is not None)
            self.stats["failed_requests"] += sum(1 for r in results if r is None)
            self.stats["avg_latency"] = elapsed / max(1, num_inputs)
            self.stats["throughput"] = num_inputs / max(0.001, elapsed)
        
        if pbar:
            pbar.close()
        
        logger.info(
            f"Completed {len(inputs)} inferences in {elapsed:.2f}s "
            f"(throughput: {num_inputs / max(0.001, elapsed):.2f} items/s)"
        )
        
        return results
    
    def _create_batches(self, items: List[Any], batch_size: int) -> List[List[Any]]:
        """
        Create batches from a list of items.
        
        Args:
            items: List of items to batch
            batch_size: Size of each batch
            
        Returns:
            List of batches
        """
        return [items[i:i+batch_size] for i in range(0, len(items), batch_size)]
    
    def _process_batch(
        self,
        batch: List[Dict[str, Any]],
        batch_idx: int,
        model_kwargs: Dict[str, Any],
        callback: Optional[Callable[[Dict[str, Any]], None]],
        pbar: Optional[tqdm] = None
    ) -> Tuple[List[Dict[str, Any]], List[int]]:
        """
        Process a single batch through the model.
        
        Args:
            batch: List of inputs in the batch
            batch_idx: Index of the batch
            model_kwargs: Additional arguments for the model
            callback: Optional callback function
            pbar: Optional progress bar
            
        Returns:
            Tuple of (results, indices)
        """
        try:
            # Get original indices for mapping results back
            start_idx = batch_idx * self.batch_size
            indices = list(range(start_idx, start_idx + len(batch)))
            
            # Run inference on the batch
            batch_start = time.time()
            
            # Call the model's batch inference method if it exists
            if hasattr(self.model, "batch_inference"):
                results = self.model.batch_inference(batch, **model_kwargs)
            else:
                # Otherwise, run inference on each item individually
                results = []
                for item in batch:
                    result = self.model.inference(item, **model_kwargs)
                    results.append(result)
                    
                    # Call the callback if provided
                    if callback:
                        callback(result)
            
            batch_time = time.time() - batch_start
            
            # Update progress bar
            if pbar:
                pbar.update(len(batch))
                
            logger.debug(
                f"Batch {batch_idx} processed in {batch_time:.3f}s "
                f"({len(batch)/max(0.001, batch_time):.2f} items/s)"
            )
            
            return results, indices
            
        except Exception as e:
            logger.error(f"Error processing batch {batch_idx}: {e}")
            empty_results = [None] * len(batch)
            start_idx = batch_idx * self.batch_size
            indices = list(range(start_idx, start_idx + len(batch)))
            return empty_results, indices
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get current inference statistics.
        
        Returns:
            Dictionary of inference statistics
        """
        with self._lock:
            return self.stats.copy()
    
    def shutdown(self):
        """Shut down the thread pool and release resources."""
        if hasattr(self, "resource_monitor") and self.resource_monitor:
            self.resource_monitor.stop()
            
        if isinstance(self.thread_pool, AdaptiveThreadPool):
            self.thread_pool.shutdown()
        else:
            self.thread_pool.shutdown(wait=True)
            
        logger.info("ParallelInference shutdown complete")


class BatchProcessor:
    """
    Utility for processing large batches of data with parallel inference.
    
    This class handles large datasets by breaking them into manageable chunks
    and processing them through the ParallelInference engine.
    """
    
    def __init__(
        self,
        parallel_inference: ParallelInference,
        max_chunk_size: int = 1000,
        save_interval: Optional[int] = None,
        checkpoint_path: Optional[str] = None
    ):
        """
        Initialize BatchProcessor.
        
        Args:
            parallel_inference: ParallelInference instance to use
            max_chunk_size: Maximum size of each processing chunk
            save_interval: How often to save checkpoints (in chunks)
            checkpoint_path: Path to save checkpoints
        """
        self.parallel_inference = parallel_inference
        self.max_chunk_size = max_chunk_size
        self.save_interval = save_interval
        self.checkpoint_path = checkpoint_path
        self.stats = {
            "total_processed": 0,
            "chunks_processed": 0,
            "avg_chunk_time": 0.0
        }
    
    def process(
        self,
        data: List[Dict[str, Any]],
        model_kwargs: Optional[Dict[str, Any]] = None,
        show_progress: bool = True,
        resume_from: Optional[int] = None,
        callback: Optional[Callable[[List[Dict[str, Any]]], None]] = None
    ) -> List[Dict[str, Any]]:
        """
        Process a large dataset in chunks.
        
        Args:
            data: List of data to process
            model_kwargs: Additional keyword arguments to pass to the model
            show_progress: Whether to show a progress bar
            resume_from: Index to resume processing from
            callback: Optional callback function to call with each chunk result
            
        Returns:
            List of processing results
        """
        model_kwargs = model_kwargs or {}
        total_items = len(data)
        start_idx = resume_from or 0
        results = [None] * total_items
        
        # Create chunks
        chunks = [
            data[i:i+self.max_chunk_size]
            for i in range(start_idx, total_items, self.max_chunk_size)
        ]
        
        logger.info(
            f"Processing {total_items} items in {len(chunks)} chunks "
            f"(chunk_size={self.max_chunk_size})"
        )
        
        # Set up progress bar
        pbar = tqdm(total=total_items - start_idx) if show_progress else None
        
        # Process each chunk
        for chunk_idx, chunk in enumerate(chunks):
            chunk_start = time.time()
            
            # Process the chunk
            chunk_results = self.parallel_inference.infer(
                chunk, 
                model_kwargs=model_kwargs,
                show_progress=False
            )
            
            # Map results back to the full results list
            chunk_start_idx = start_idx + chunk_idx * self.max_chunk_size
            for i, result in enumerate(chunk_results):
                results[chunk_start_idx + i] = result
            
            # Update progress
            if pbar:
                pbar.update(len(chunk))
            
            # Update stats
            chunk_time = time.time() - chunk_start
            self.stats["total_processed"] += len(chunk)
            self.stats["chunks_processed"] += 1
            self.stats["avg_chunk_time"] = (
                (self.stats["avg_chunk_time"] * (self.stats["chunks_processed"] - 1) + chunk_time) / 
                self.stats["chunks_processed"]
            )
            
            # Call callback if provided
            if callback:
                callback(chunk_results)
            
            # Save checkpoint if needed
            if self.save_interval and (chunk_idx + 1) % self.save_interval == 0:
                self._save_checkpoint(results, chunk_start_idx + len(chunk))
        
        if pbar:
            pbar.close()
        
        logger.info(
            f"Completed processing {total_items} items in {self.stats['chunks_processed']} chunks "
            f"(avg chunk time: {self.stats['avg_chunk_time']:.2f}s)"
        )
        
        return results
    
    def _save_checkpoint(self, results: List[Dict[str, Any]], current_idx: int):
        """
        Save processing checkpoint.
        
        Args:
            results: Current results
            current_idx: Current processing index
        """
        if not self.checkpoint_path:
            return
            
        try:
            checkpoint = {
                "results": results,
                "current_idx": current_idx,
                "stats": self.stats,
                "timestamp": time.time()
            }
            
            import pickle
            with open(f"{self.checkpoint_path}_idx{current_idx}.pkl", "wb") as f:
                pickle.dump(checkpoint, f)
                
            logger.info(f"Saved checkpoint at index {current_idx}")
        except Exception as e:
            logger.error(f"Failed to save checkpoint: {e}")
    
    def load_checkpoint(self, checkpoint_file: str) -> Tuple[List[Dict[str, Any]], int]:
        """
        Load processing checkpoint.
        
        Args:
            checkpoint_file: Path to checkpoint file
            
        Returns:
            Tuple of (results, current_idx)
        """
        try:
            import pickle
            with open(checkpoint_file, "rb") as f:
                checkpoint = pickle.load(f)
                
            self.stats = checkpoint["stats"]
            logger.info(
                f"Loaded checkpoint from index {checkpoint['current_idx']} "
                f"(timestamp: {checkpoint['timestamp']})"
            )
            
            return checkpoint["results"], checkpoint["current_idx"]
        except Exception as e:
            logger.error(f"Failed to load checkpoint: {e}")
            return [], 0
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get current processing statistics.
        
        Returns:
            Dictionary of processing statistics
        """
        return self.stats.copy() 