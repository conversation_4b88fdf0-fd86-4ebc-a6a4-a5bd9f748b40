"""
Semantic caching implementation for Deep Research Core.

This module provides a semantic caching mechanism that allows caching
based on semantic similarity rather than exact matches, which is
particularly useful for natural language queries.
"""

import os
import json
import time
import hashlib
import numpy as np
import threading
from typing import Dict, List, Any, Optional, Tuple, Union, Callable, Type
from dataclasses import dataclass, field
from datetime import datetime

from deep_research_core.utils.structured_logging import get_logger
from deep_research_core.utils.text_similarity import semantic_similarity, cosine_similarity
from deep_research_core.models.embeddings import get_embedding_model, BaseEmbeddingModel
from deep_research_core.optimization.caching.eviction_policies import (
    EvictionPolicy, LRUPolicy, LFUPolicy, TTLPolicy, CompositePolicy, EvictionPolicyFactory
)

# Get logger
logger = get_logger(__name__)

@dataclass
class SemanticCacheConfig:
    """Configuration for semantic cache."""

    # Cache capacity (number of entries)
    capacity: int = 1000

    # Similarity threshold for cache hit (0.0-1.0)
    similarity_threshold: float = 0.85

    # Time-to-live in seconds (0 means no expiration)
    ttl: int = 3600 * 24  # 24 hours

    # Embedding model name to use for semantic similarity
    embedding_model_name: str = "multilingual-e5-small"

    # Whether to use partial cache hits
    use_partial_hits: bool = True

    # Minimum similarity for partial hits
    partial_hit_threshold: float = 0.70

    # Cache directory for persistence
    cache_dir: Optional[str] = None

    # Whether to persist cache to disk
    persist_to_disk: bool = True

    # How often to save cache to disk (in seconds)
    persist_interval: int = 300  # 5 minutes

    # Whether to use background thread for persistence
    use_background_thread: bool = True

    # Whether to load cache from disk on initialization
    load_on_init: bool = True

    # Maximum size of cached result (in bytes)
    max_result_size: int = 1024 * 1024  # 1 MB

    # Fields to exclude from similarity calculation
    exclude_fields: List[str] = field(default_factory=lambda: ["timestamp", "metadata"])

    # Additional metadata to store with each entry
    additional_metadata: Dict[str, Any] = field(default_factory=dict)

    # Eviction policy type ('lru', 'lfu', 'ttl', 'composite')
    eviction_policy_type: str = 'lru'

    # Cleanup interval for TTL policy (in seconds)
    cleanup_interval: int = 60


@dataclass
class SemanticCacheEntry:
    """Entry in the semantic cache."""

    # Query that produced this result
    query: str

    # Result of the query
    result: Any

    # Embedding of the query
    embedding: List[float]

    # Timestamp when the entry was created
    timestamp: float = field(default_factory=time.time)

    # Hash of the query (for quick lookups)
    query_hash: str = ""

    # Metadata about the entry
    metadata: Dict[str, Any] = field(default_factory=dict)

    # Size of the result in bytes
    result_size: int = 0

    def __post_init__(self):
        """Initialize derived fields."""
        if not self.query_hash:
            self.query_hash = hashlib.md5(self.query.encode()).hexdigest()

        if not self.result_size:
            # Estimate size of result
            try:
                self.result_size = len(json.dumps(self.result))
            except (TypeError, OverflowError):
                # If result can't be serialized, use a conservative estimate
                self.result_size = len(str(self.result)) * 2

    def is_expired(self, ttl: int) -> bool:
        """Check if the entry is expired."""
        if ttl <= 0:
            return False
        return (time.time() - self.timestamp) > ttl

    def to_dict(self) -> Dict[str, Any]:
        """Convert entry to dictionary for serialization."""
        return {
            "query": self.query,
            "result": self.result,
            "embedding": self.embedding,
            "timestamp": self.timestamp,
            "query_hash": self.query_hash,
            "metadata": self.metadata,
            "result_size": self.result_size
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SemanticCacheEntry':
        """Create entry from dictionary."""
        return cls(
            query=data["query"],
            result=data["result"],
            embedding=data["embedding"],
            timestamp=data.get("timestamp", time.time()),
            query_hash=data.get("query_hash", ""),
            metadata=data.get("metadata", {}),
            result_size=data.get("result_size", 0)
        )


class SemanticCache:
    """
    Semantic cache for caching results based on semantic similarity.

    This cache stores results along with the query embeddings and can
    retrieve results for semantically similar queries, not just exact matches.
    """

    def __init__(self, config: Optional[SemanticCacheConfig] = None):
        """
        Initialize the semantic cache.

        Args:
            config: Configuration for the cache
        """
        self.config = config or SemanticCacheConfig()

        # Initialize cache
        self.cache: Dict[str, SemanticCacheEntry] = {}

        # Initialize embedding model
        self.embedding_model = self._initialize_embedding_model()

        # Initialize lock for thread safety
        self.lock = threading.RLock()

        # Initialize eviction policy
        self.eviction_policy = self._initialize_eviction_policy()

        # Initialize persistence
        self._initialize_persistence()

        # Load cache from disk if enabled
        if self.config.load_on_init and self.config.persist_to_disk:
            self._load_from_disk()

        # Start background thread for persistence if enabled
        if self.config.use_background_thread and self.config.persist_to_disk:
            self._start_persistence_thread()

    def _initialize_embedding_model(self) -> BaseEmbeddingModel:
        """Initialize the embedding model."""
        try:
            return get_embedding_model(self.config.embedding_model_name)
        except Exception as e:
            logger.error(f"Error initializing embedding model: {str(e)}")
            logger.info("Falling back to text-based similarity")
            return None

    def _initialize_persistence(self):
        """Initialize persistence-related attributes."""
        if self.config.persist_to_disk:
            # Create cache directory if it doesn't exist
            if self.config.cache_dir:
                os.makedirs(self.config.cache_dir, exist_ok=True)
            else:
                # Use default directory
                cache_dir = os.path.join(os.path.expanduser("~"), ".deep_research_core", "cache")
                os.makedirs(cache_dir, exist_ok=True)
                self.config.cache_dir = cache_dir

            # Initialize last persist time
            self.last_persist_time = time.time()

            # Initialize persistence thread
            self.persistence_thread = None
            self.stop_persistence_thread = threading.Event()

    def _start_persistence_thread(self):
        """Start background thread for persistence."""
        if self.persistence_thread is not None:
            return

        self.persistence_thread = threading.Thread(
            target=self._persistence_worker,
            daemon=True
        )
        self.persistence_thread.start()

    def _persistence_worker(self):
        """Worker function for persistence thread."""
        while not self.stop_persistence_thread.is_set():
            # Sleep for a while
            time.sleep(10)

            # Check if it's time to persist
            if (time.time() - self.last_persist_time) >= self.config.persist_interval:
                self._save_to_disk()
                self.last_persist_time = time.time()

    def _get_cache_file_path(self) -> str:
        """Get the path to the cache file."""
        return os.path.join(self.config.cache_dir, "semantic_cache.json")

    def _save_to_disk(self):
        """Save cache to disk."""
        if not self.config.persist_to_disk:
            return

        try:
            cache_file = self._get_cache_file_path()

            # Convert cache to serializable format
            with self.lock:
                serializable_cache = {
                    "entries": [entry.to_dict() for entry in self.cache.values()],
                    "metadata": {
                        "timestamp": time.time(),
                        "version": "1.0",
                        "entry_count": len(self.cache),
                        "config": {k: v for k, v in self.config.__dict__.items()
                                  if not k.startswith("_") and k != "additional_metadata"}
                    }
                }

            # Save to disk
            with open(cache_file, "w") as f:
                json.dump(serializable_cache, f)

            logger.info(f"Saved semantic cache to disk ({len(self.cache)} entries)")
        except Exception as e:
            logger.error(f"Error saving cache to disk: {str(e)}")

    def _load_from_disk(self):
        """Load cache from disk."""
        if not self.config.persist_to_disk:
            return

        try:
            cache_file = self._get_cache_file_path()

            if not os.path.exists(cache_file):
                logger.info("No cache file found, starting with empty cache")
                return

            # Load from disk
            with open(cache_file, "r") as f:
                data = json.load(f)

            # Convert to cache entries
            with self.lock:
                for entry_data in data.get("entries", []):
                    entry = SemanticCacheEntry.from_dict(entry_data)

                    # Skip expired entries
                    if entry.is_expired(self.config.ttl):
                        continue

                    self.cache[entry.query_hash] = entry

            logger.info(f"Loaded semantic cache from disk ({len(self.cache)} entries)")
        except Exception as e:
            logger.error(f"Error loading cache from disk: {str(e)}")

    def get_embedding(self, query: str) -> List[float]:
        """
        Get embedding for a query.

        Args:
            query: Query to get embedding for

        Returns:
            Embedding vector
        """
        if self.embedding_model is None:
            # Return a random embedding as fallback
            return list(np.random.rand(768))

        try:
            # Get embedding from model
            return self.embedding_model.get_embedding(query)
        except Exception as e:
            logger.error(f"Error getting embedding: {str(e)}")
            # Return a random embedding as fallback
            return list(np.random.rand(768))

    def calculate_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """
        Calculate similarity between two embeddings.

        Args:
            embedding1: First embedding
            embedding2: Second embedding

        Returns:
            Similarity score (0-1)
        """
        try:
            # Convert to numpy arrays
            vec1 = np.array(embedding1)
            vec2 = np.array(embedding2)

            # Calculate cosine similarity
            return cosine_similarity(vec1, vec2)
        except Exception as e:
            logger.error(f"Error calculating similarity: {str(e)}")
            return 0.0

    def find_most_similar_entry(self, query: str, embedding: List[float]) -> Tuple[Optional[SemanticCacheEntry], float]:
        """
        Find the most similar entry in the cache.

        Args:
            query: Query to find similar entries for
            embedding: Embedding of the query

        Returns:
            Tuple of (most similar entry, similarity score)
        """
        most_similar_entry = None
        highest_similarity = 0.0

        with self.lock:
            # Check for exact match first
            query_hash = hashlib.md5(query.encode()).hexdigest()
            if query_hash in self.cache:
                entry = self.cache[query_hash]
                if not entry.is_expired(self.config.ttl):
                    return entry, 1.0

            # Check for semantic similarity
            for entry in self.cache.values():
                # Skip expired entries
                if entry.is_expired(self.config.ttl):
                    continue

                # Calculate similarity
                similarity = self.calculate_similarity(embedding, entry.embedding)

                # Update most similar entry
                if similarity > highest_similarity:
                    highest_similarity = similarity
                    most_similar_entry = entry

        return most_similar_entry, highest_similarity

    def get(self, query: str) -> Tuple[Any, bool, float]:
        """
        Get result from cache.

        Args:
            query: Query to get result for

        Returns:
            Tuple of (result, is_hit, similarity)
        """
        # Get embedding for query
        embedding = self.get_embedding(query)

        # Find most similar entry
        entry, similarity = self.find_most_similar_entry(query, embedding)

        # Check if we have a cache hit
        if entry is not None:
            # Update eviction policy
            if hasattr(self, 'eviction_policy') and self.eviction_policy is not None:
                self.eviction_policy.get(entry.query_hash)

            if similarity >= self.config.similarity_threshold:
                logger.info(f"Semantic cache hit (similarity: {similarity:.4f})")
                return entry.result, True, similarity

            if self.config.use_partial_hits and similarity >= self.config.partial_hit_threshold:
                logger.info(f"Semantic cache partial hit (similarity: {similarity:.4f})")
                return entry.result, False, similarity

        logger.info("Semantic cache miss")
        return None, False, 0.0

    def put(self, query: str, result: Any, metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Put result in cache.

        Args:
            query: Query that produced the result
            result: Result to cache
            metadata: Additional metadata to store with the entry
        """
        # Check if result is too large
        try:
            result_size = len(json.dumps(result))
            if result_size > self.config.max_result_size:
                logger.warning(f"Result too large to cache ({result_size} bytes)")
                return
        except (TypeError, OverflowError):
            # If result can't be serialized, use a conservative estimate
            result_size = len(str(result)) * 2
            if result_size > self.config.max_result_size:
                logger.warning(f"Result too large to cache (estimated {result_size} bytes)")
                return

        # Get embedding for query
        embedding = self.get_embedding(query)

        # Create cache entry
        entry = SemanticCacheEntry(
            query=query,
            result=result,
            embedding=embedding,
            metadata=metadata or {}
        )

        with self.lock:
            # Add to cache
            self.cache[entry.query_hash] = entry

            # Add to eviction policy
            if hasattr(self, 'eviction_policy') and self.eviction_policy is not None:
                self.eviction_policy.add(entry.query_hash, entry)

            # Evict entries if cache is full
            self._evict_if_needed()

        # Save to disk if needed
        if self.config.persist_to_disk and not self.config.use_background_thread:
            if (time.time() - self.last_persist_time) >= self.config.persist_interval:
                self._save_to_disk()
                self.last_persist_time = time.time()

    def _initialize_eviction_policy(self) -> EvictionPolicy:
        """Initialize the eviction policy."""
        try:
            # Create eviction policy based on configuration
            return EvictionPolicyFactory.create_policy(
                policy_type=self.config.eviction_policy_type,
                capacity=self.config.capacity,
                ttl=self.config.ttl,
                cleanup_interval=self.config.cleanup_interval
            )
        except Exception as e:
            logger.error(f"Error initializing eviction policy: {str(e)}")
            logger.info("Falling back to LRU policy")
            return LRUPolicy(capacity=self.config.capacity)

    def _evict_if_needed(self):
        """Evict entries if cache is full."""
        if len(self.cache) <= self.config.capacity:
            return

        # Use eviction policy to determine which entries to evict
        num_to_evict = len(self.cache) - self.config.capacity

        # Get keys to evict from policy
        keys_to_evict = []

        # If using the eviction policy
        if hasattr(self, 'eviction_policy') and self.eviction_policy is not None:
            # Evict using policy
            evicted_keys = self.eviction_policy.evict_multiple(num_to_evict)
            for key in evicted_keys:
                if isinstance(key, str) and key in self.cache:
                    keys_to_evict.append(key)

        # Fallback to timestamp-based eviction if policy didn't provide enough keys
        if len(keys_to_evict) < num_to_evict:
            # Sort entries by timestamp (oldest first)
            entries = sorted(self.cache.values(), key=lambda e: e.timestamp)

            # Add oldest entries to eviction list
            remaining = num_to_evict - len(keys_to_evict)
            for i in range(min(remaining, len(entries))):
                keys_to_evict.append(entries[i].query_hash)

        # Evict entries
        for key in keys_to_evict:
            if key in self.cache:
                del self.cache[key]

        logger.info(f"Evicted {len(keys_to_evict)} entries from semantic cache")

    def clear(self):
        """Clear the cache."""
        with self.lock:
            self.cache.clear()

            # Clear eviction policy
            if hasattr(self, 'eviction_policy') and self.eviction_policy is not None:
                self.eviction_policy.clear()

        logger.info("Cleared semantic cache")

    def clear_expired(self):
        """Clear expired entries from the cache."""
        with self.lock:
            # Find expired entries
            expired_keys = [
                key for key, entry in self.cache.items()
                if entry.is_expired(self.config.ttl)
            ]

            # Remove expired entries
            for key in expired_keys:
                del self.cache[key]

                # Remove from eviction policy
                if hasattr(self, 'eviction_policy') and self.eviction_policy is not None:
                    self.eviction_policy.remove(key)

        if expired_keys:
            logger.info(f"Cleared {len(expired_keys)} expired entries from semantic cache")

    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the cache.

        Returns:
            Dictionary of statistics
        """
        with self.lock:
            # Count expired entries
            expired_count = sum(
                1 for entry in self.cache.values()
                if entry.is_expired(self.config.ttl)
            )

            # Calculate total size
            total_size = sum(entry.result_size for entry in self.cache.values())

            # Get timestamps
            timestamps = [entry.timestamp for entry in self.cache.values()]

            # Get eviction policy stats
            eviction_policy_stats = {}
            if hasattr(self, 'eviction_policy') and self.eviction_policy is not None:
                eviction_policy_stats = {
                    "policy_type": self.config.eviction_policy_type,
                    "policy_size": self.eviction_policy.size()
                }

            stats = {
                "total_entries": len(self.cache),
                "active_entries": len(self.cache) - expired_count,
                "expired_entries": expired_count,
                "total_size_bytes": total_size,
                "capacity": self.config.capacity,
                "similarity_threshold": self.config.similarity_threshold,
                "ttl": self.config.ttl,
                "oldest_entry": min(timestamps) if timestamps else None,
                "newest_entry": max(timestamps) if timestamps else None,
                "persistence_enabled": self.config.persist_to_disk,
                "eviction_policy": eviction_policy_stats
            }

            # Add last persist time if available
            if hasattr(self, "last_persist_time"):
                stats["last_persist_time"] = self.last_persist_time

            return stats

    def __len__(self) -> int:
        """Get the number of entries in the cache."""
        return len(self.cache)

    def __contains__(self, query: str) -> bool:
        """Check if a query is in the cache."""
        query_hash = hashlib.md5(query.encode()).hexdigest()
        with self.lock:
            return query_hash in self.cache and not self.cache[query_hash].is_expired(self.config.ttl)

    def __del__(self):
        """Clean up resources."""
        if hasattr(self, "stop_persistence_thread") and hasattr(self, "persistence_thread"):
            if self.persistence_thread is not None:
                self.stop_persistence_thread.set()
                self.persistence_thread.join(timeout=1.0)

        # Save cache to disk
        if hasattr(self, "config") and self.config.persist_to_disk:
            self._save_to_disk()


# Create a global instance for convenience
default_semantic_cache = SemanticCache()
