"""
Cache decorators for Deep Research Core.

This module provides decorators for caching function results,
including semantic caching for natural language queries.
"""

import time
import functools
import inspect
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

from deep_research_core.utils.structured_logging import get_logger
from deep_research_core.optimization.caching.semantic_cache import (
    SemanticCache, 
    SemanticCacheConfig,
    default_semantic_cache
)

# Get logger
logger = get_logger(__name__)

def semantic_cache(
    key_arg: Union[str, int] = 0,
    cache_instance: Optional[SemanticCache] = None,
    ttl: Optional[int] = None,
    similarity_threshold: Optional[float] = None,
    metadata_func: Optional[Callable] = None
):
    """
    Decorator for caching function results based on semantic similarity.
    
    Args:
        key_arg: Argument to use as cache key (name or position)
        cache_instance: Cache instance to use (default: global instance)
        ttl: Time-to-live for cache entries (default: from cache config)
        similarity_threshold: Similarity threshold for cache hits (default: from cache config)
        metadata_func: Function to generate metadata for cache entries
        
    Returns:
        Decorated function
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Get cache instance
            cache = cache_instance or default_semantic_cache
            
            # Get cache key
            if isinstance(key_arg, int):
                if key_arg < len(args):
                    cache_key = args[key_arg]
                else:
                    logger.warning(f"Key argument position {key_arg} out of range, using function name as key")
                    cache_key = func.__name__
            else:
                if key_arg in kwargs:
                    cache_key = kwargs[key_arg]
                else:
                    # Try to get from signature
                    sig = inspect.signature(func)
                    params = list(sig.parameters.keys())
                    if key_arg in params:
                        pos = params.index(key_arg)
                        if pos < len(args):
                            cache_key = args[pos]
                        else:
                            logger.warning(f"Key argument {key_arg} not provided, using function name as key")
                            cache_key = func.__name__
                    else:
                        logger.warning(f"Key argument {key_arg} not found, using function name as key")
                        cache_key = func.__name__
            
            # Convert cache key to string
            if not isinstance(cache_key, str):
                cache_key = str(cache_key)
            
            # Try to get from cache
            custom_threshold = similarity_threshold if similarity_threshold is not None else cache.config.similarity_threshold
            result, is_hit, similarity = cache.get(cache_key)
            
            if is_hit or (similarity >= custom_threshold):
                logger.info(f"Cache hit for {func.__name__} (similarity: {similarity:.4f})")
                return result
            
            # Call function
            start_time = time.time()
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # Generate metadata
            metadata = {
                "function": func.__name__,
                "execution_time": execution_time
            }
            
            # Add custom metadata
            if metadata_func:
                try:
                    custom_metadata = metadata_func(*args, **kwargs)
                    if isinstance(custom_metadata, dict):
                        metadata.update(custom_metadata)
                except Exception as e:
                    logger.error(f"Error generating metadata: {str(e)}")
            
            # Store in cache
            custom_ttl = ttl if ttl is not None else cache.config.ttl
            if custom_ttl != cache.config.ttl:
                # Create a copy of the entry with custom TTL
                old_ttl = cache.config.ttl
                cache.config.ttl = custom_ttl
                cache.put(cache_key, result, metadata)
                cache.config.ttl = old_ttl
            else:
                cache.put(cache_key, result, metadata)
            
            return result
        
        return wrapper
    
    return decorator


def query_cache(
    query_arg: Union[str, int] = "query",
    cache_instance: Optional[SemanticCache] = None,
    ttl: Optional[int] = None,
    similarity_threshold: Optional[float] = None
):
    """
    Decorator specifically for caching query results.
    
    This is a convenience wrapper around semantic_cache that is
    specifically designed for functions that process queries.
    
    Args:
        query_arg: Argument containing the query (name or position)
        cache_instance: Cache instance to use (default: global instance)
        ttl: Time-to-live for cache entries (default: from cache config)
        similarity_threshold: Similarity threshold for cache hits (default: from cache config)
        
    Returns:
        Decorated function
    """
    def metadata_func(*args, **kwargs):
        """Generate metadata for query cache entries."""
        # Get query
        if isinstance(query_arg, int):
            if query_arg < len(args):
                query = args[query_arg]
            else:
                query = None
        else:
            if query_arg in kwargs:
                query = kwargs[query_arg]
            else:
                # Try to get from signature
                sig = inspect.signature(func)
                params = list(sig.parameters.keys())
                if query_arg in params:
                    pos = params.index(query_arg)
                    if pos < len(args):
                        query = args[pos]
                    else:
                        query = None
                else:
                    query = None
        
        # Generate metadata
        metadata = {
            "query_length": len(query) if query else 0,
            "query_word_count": len(query.split()) if query else 0,
            "timestamp": time.time()
        }
        
        return metadata
    
    return semantic_cache(
        key_arg=query_arg,
        cache_instance=cache_instance,
        ttl=ttl,
        similarity_threshold=similarity_threshold,
        metadata_func=metadata_func
    )
