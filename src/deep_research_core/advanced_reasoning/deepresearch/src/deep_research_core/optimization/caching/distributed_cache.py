"""
Distributed caching implementation for Deep Research Core.

This module provides a distributed caching mechanism using Redis,
with support for cache synchronization and invalidation.
"""

import time
import json
import hashlib
import threading
import functools
import inspect
from typing import Any, Dict, List, Optional, Set, Tuple, Union, Callable, Type

from deep_research_core.utils.structured_logging import get_logger
from deep_research_core.optimization.caching.redis_provider import (
    RedisProvider,
    RedisConfig,
    default_redis_provider,
    REDIS_AVAILABLE
)
from deep_research_core.optimization.caching.eviction_policies import (
    EvictionPolicy, LRUPolicy, LFUPolicy, TTLPolicy, CompositePolicy, EvictionPolicyFactory
)

# Get logger
logger = get_logger(__name__)


class DistributedCacheConfig:
    """Configuration for distributed cache."""

    def __init__(
        self,
        namespace: str = "default",
        ttl: int = 3600,  # 1 hour
        redis_config: Optional[RedisConfig] = None,
        invalidation_channel: str = "cache_invalidation",
        partition_count: int = 16,
        use_local_cache: bool = True,
        local_cache_size: int = 1000,
        local_cache_ttl: int = 60,  # 1 minute
        serializer: str = "json",
        eviction_policy_type: str = "lru",
        cleanup_interval: int = 60  # 1 minute
    ):
        """
        Initialize distributed cache configuration.

        Args:
            namespace: Cache namespace
            ttl: Default TTL in seconds
            redis_config: Redis configuration
            invalidation_channel: Channel for invalidation messages
            partition_count: Number of partitions for sharding
            use_local_cache: Whether to use local in-memory cache
            local_cache_size: Size of local cache
            local_cache_ttl: TTL for local cache in seconds
            serializer: Serializer to use ('json' or 'pickle')
            eviction_policy_type: Type of eviction policy ('lru', 'lfu', 'ttl', 'composite')
            cleanup_interval: Interval in seconds for automatic cleanup
        """
        self.namespace = namespace
        self.ttl = ttl
        self.redis_config = redis_config or RedisConfig()
        self.invalidation_channel = invalidation_channel
        self.partition_count = partition_count
        self.use_local_cache = use_local_cache
        self.local_cache_size = local_cache_size
        self.local_cache_ttl = local_cache_ttl
        self.serializer = serializer
        self.eviction_policy_type = eviction_policy_type
        self.cleanup_interval = cleanup_interval

        # Validate serializer
        if self.serializer not in ["json", "pickle"]:
            logger.warning(f"Invalid serializer: {self.serializer}. Using 'json' instead.")
            self.serializer = "json"

        # Validate eviction policy type
        if self.eviction_policy_type not in ["lru", "lfu", "ttl", "composite"]:
            logger.warning(f"Invalid eviction policy type: {self.eviction_policy_type}. Using 'lru' instead.")
            self.eviction_policy_type = "lru"


class DistributedCache:
    """
    Distributed cache implementation using Redis.

    This cache provides distributed caching with support for
    cache synchronization, invalidation, and partitioning.
    """

    def __init__(self, config: Optional[DistributedCacheConfig] = None):
        """
        Initialize distributed cache.

        Args:
            config: Cache configuration
        """
        if not REDIS_AVAILABLE:
            raise ImportError("Redis is required for distributed caching. Install with 'pip install redis'.")

        self.config = config or DistributedCacheConfig()

        # Initialize Redis provider
        self.redis = default_redis_provider or RedisProvider(self.config.redis_config)

        # Initialize local cache if enabled
        self.local_cache = {}
        self.local_cache_timestamps = {}
        self.local_cache_lock = threading.RLock()

        # Initialize eviction policy
        self.eviction_policy = self._initialize_eviction_policy()

        # Initialize invalidation listener
        self.invalidation_listener_thread = None
        self.stop_invalidation_listener = threading.Event()

        # Start invalidation listener
        self._start_invalidation_listener()

    def _start_invalidation_listener(self):
        """Start invalidation listener thread."""
        if self.invalidation_listener_thread is not None:
            return

        self.invalidation_listener_thread = threading.Thread(
            target=self._invalidation_listener_worker,
            daemon=True
        )
        self.invalidation_listener_thread.start()

    def _invalidation_listener_worker(self):
        """Worker function for invalidation listener thread."""
        channel = f"{self.config.namespace}:{self.config.invalidation_channel}"

        try:
            for message in self.redis.subscribe(channel):
                if self.stop_invalidation_listener.is_set():
                    break

                if message is None:
                    continue

                try:
                    # Parse invalidation message
                    if isinstance(message, dict) and "key" in message:
                        key = message["key"]
                        pattern = message.get("pattern")

                        # Invalidate local cache
                        if self.config.use_local_cache:
                            with self.local_cache_lock:
                                if pattern:
                                    # Invalidate by pattern
                                    keys_to_remove = []
                                    for cache_key in self.local_cache:
                                        if self._match_pattern(cache_key, pattern):
                                            keys_to_remove.append(cache_key)

                                    for cache_key in keys_to_remove:
                                        del self.local_cache[cache_key]
                                        if cache_key in self.local_cache_timestamps:
                                            del self.local_cache_timestamps[cache_key]
                                else:
                                    # Invalidate specific key
                                    if key in self.local_cache:
                                        del self.local_cache[key]
                                    if key in self.local_cache_timestamps:
                                        del self.local_cache_timestamps[key]

                        logger.debug(f"Cache invalidation: {key} (pattern: {pattern})")
                except Exception as e:
                    logger.error(f"Error processing invalidation message: {str(e)}")
        except Exception as e:
            logger.error(f"Error in invalidation listener: {str(e)}")

            # Try to restart listener
            time.sleep(5)
            if not self.stop_invalidation_listener.is_set():
                self._start_invalidation_listener()

    def _match_pattern(self, key: str, pattern: str) -> bool:
        """
        Check if key matches pattern.

        Args:
            key: Key to check
            pattern: Pattern to match

        Returns:
            True if key matches pattern, False otherwise
        """
        import re

        # Convert Redis pattern to regex
        regex_pattern = pattern.replace("*", ".*").replace("?", ".")
        return bool(re.match(f"^{regex_pattern}$", key))

    def _get_partition_key(self, key: str) -> str:
        """
        Get partition key for sharding.

        Args:
            key: Cache key

        Returns:
            Partition key
        """
        # Calculate hash of key
        hash_value = int(hashlib.md5(key.encode()).hexdigest(), 16)

        # Get partition index
        partition = hash_value % self.config.partition_count

        return f"p{partition}"

    def _get_full_key(self, key: str) -> str:
        """
        Get full key with namespace and partition.

        Args:
            key: Cache key

        Returns:
            Full key
        """
        partition = self._get_partition_key(key)
        return f"{self.config.namespace}:{partition}:{key}"

    def _publish_invalidation(self, key: str, pattern: Optional[str] = None):
        """
        Publish invalidation message.

        Args:
            key: Cache key
            pattern: Pattern for wildcard invalidation
        """
        channel = f"{self.config.namespace}:{self.config.invalidation_channel}"

        message = {
            "key": key,
            "timestamp": time.time()
        }

        if pattern:
            message["pattern"] = pattern

        self.redis.publish(channel, message)

    def _initialize_eviction_policy(self) -> EvictionPolicy:
        """Initialize the eviction policy."""
        try:
            # Create eviction policy based on configuration
            return EvictionPolicyFactory.create_policy(
                policy_type=self.config.eviction_policy_type,
                capacity=self.config.local_cache_size,
                ttl=self.config.local_cache_ttl,
                cleanup_interval=self.config.cleanup_interval
            )
        except Exception as e:
            logger.error(f"Error initializing eviction policy: {str(e)}")
            logger.info("Falling back to LRU policy")
            return LRUPolicy(capacity=self.config.local_cache_size)

    def get(self, key: str) -> Any:
        """
        Get value from cache.

        Args:
            key: Cache key

        Returns:
            Cached value or None if not found
        """
        # Check local cache first if enabled
        if self.config.use_local_cache:
            with self.local_cache_lock:
                if key in self.local_cache:
                    timestamp = self.local_cache_timestamps.get(key, 0)
                    if time.time() - timestamp <= self.config.local_cache_ttl:
                        # Update eviction policy
                        if hasattr(self, 'eviction_policy') and self.eviction_policy is not None:
                            self.eviction_policy.get(key)
                        return self.local_cache[key]

                    # Expired, remove from local cache
                    del self.local_cache[key]
                    del self.local_cache_timestamps[key]
                    # Remove from eviction policy
                    if hasattr(self, 'eviction_policy') and self.eviction_policy is not None:
                        self.eviction_policy.remove(key)

        # Get from Redis
        full_key = self._get_full_key(key)
        value = self.redis.get(full_key)

        # Update local cache if enabled
        if value is not None and self.config.use_local_cache:
            with self.local_cache_lock:
                # Evict if local cache is full
                if len(self.local_cache) >= self.config.local_cache_size:
                    # Use eviction policy if available
                    if hasattr(self, 'eviction_policy') and self.eviction_policy is not None:
                        evicted_key = self.eviction_policy.evict()
                        if evicted_key is not None and evicted_key in self.local_cache:
                            del self.local_cache[evicted_key]
                            del self.local_cache_timestamps[evicted_key]
                    else:
                        # Fallback to removing oldest entry
                        oldest_key = min(
                            self.local_cache_timestamps.keys(),
                            key=lambda k: self.local_cache_timestamps.get(k, 0)
                        )
                        del self.local_cache[oldest_key]
                        del self.local_cache_timestamps[oldest_key]

                # Add to local cache
                self.local_cache[key] = value
                self.local_cache_timestamps[key] = time.time()

                # Add to eviction policy
                if hasattr(self, 'eviction_policy') and self.eviction_policy is not None:
                    self.eviction_policy.add(key, value)

        return value

    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        Set value in cache.

        Args:
            key: Cache key
            value: Value to cache
            ttl: Time-to-live in seconds (None for default TTL)

        Returns:
            True if successful, False otherwise
        """
        # Use default TTL if not specified
        if ttl is None:
            ttl = self.config.ttl

        # Set in Redis
        full_key = self._get_full_key(key)
        success = self.redis.set(full_key, value, ttl)

        # Update local cache if enabled
        if success and self.config.use_local_cache:
            with self.local_cache_lock:
                # Evict if local cache is full
                if len(self.local_cache) >= self.config.local_cache_size:
                    # Use eviction policy if available
                    if hasattr(self, 'eviction_policy') and self.eviction_policy is not None:
                        evicted_key = self.eviction_policy.evict()
                        if evicted_key is not None and evicted_key in self.local_cache:
                            del self.local_cache[evicted_key]
                            del self.local_cache_timestamps[evicted_key]
                    else:
                        # Fallback to removing oldest entry
                        oldest_key = min(
                            self.local_cache_timestamps.keys(),
                            key=lambda k: self.local_cache_timestamps.get(k, 0)
                        )
                        del self.local_cache[oldest_key]
                        del self.local_cache_timestamps[oldest_key]

                # Add to local cache
                self.local_cache[key] = value
                self.local_cache_timestamps[key] = time.time()

                # Add to eviction policy
                if hasattr(self, 'eviction_policy') and self.eviction_policy is not None:
                    self.eviction_policy.add(key, value)

        return success

    def delete(self, key: str) -> bool:
        """
        Delete value from cache.

        Args:
            key: Cache key

        Returns:
            True if successful, False otherwise
        """
        # Delete from Redis
        full_key = self._get_full_key(key)
        success = self.redis.delete(full_key)

        # Delete from local cache if enabled
        if self.config.use_local_cache:
            with self.local_cache_lock:
                if key in self.local_cache:
                    del self.local_cache[key]
                if key in self.local_cache_timestamps:
                    del self.local_cache_timestamps[key]

                # Remove from eviction policy
                if hasattr(self, 'eviction_policy') and self.eviction_policy is not None:
                    self.eviction_policy.remove(key)

        # Publish invalidation
        self._publish_invalidation(key)

        return success

    def exists(self, key: str) -> bool:
        """
        Check if key exists in cache.

        Args:
            key: Cache key

        Returns:
            True if key exists, False otherwise
        """
        # Check local cache first if enabled
        if self.config.use_local_cache:
            with self.local_cache_lock:
                if key in self.local_cache:
                    timestamp = self.local_cache_timestamps.get(key, 0)
                    if time.time() - timestamp <= self.config.local_cache_ttl:
                        return True
                    else:
                        # Expired, remove from local cache
                        del self.local_cache[key]
                        del self.local_cache_timestamps[key]

        # Check in Redis
        full_key = self._get_full_key(key)
        return self.redis.exists(full_key)

    def ttl(self, key: str) -> int:
        """
        Get TTL of key in seconds.

        Args:
            key: Cache key

        Returns:
            TTL in seconds, -1 if no expiration, -2 if key doesn't exist
        """
        full_key = self._get_full_key(key)
        return self.redis.ttl(full_key)

    def expire(self, key: str, ttl: int) -> bool:
        """
        Set TTL for key.

        Args:
            key: Cache key
            ttl: Time-to-live in seconds

        Returns:
            True if successful, False otherwise
        """
        full_key = self._get_full_key(key)
        return self.redis.expire(full_key, ttl)

    def invalidate(self, pattern: str) -> bool:
        """
        Invalidate keys matching pattern.

        Args:
            pattern: Pattern to match

        Returns:
            True if successful, False otherwise
        """
        # Get keys matching pattern
        full_pattern = f"{self.config.namespace}:*:{pattern}"
        keys = self.redis.keys(full_pattern)

        # Delete keys
        success = True
        for key in keys:
            if not self.redis.delete(key):
                success = False

        # Invalidate local cache
        if self.config.use_local_cache:
            with self.local_cache_lock:
                keys_to_remove = []
                for cache_key in self.local_cache:
                    if self._match_pattern(cache_key, pattern):
                        keys_to_remove.append(cache_key)

                for cache_key in keys_to_remove:
                    del self.local_cache[cache_key]
                    if cache_key in self.local_cache_timestamps:
                        del self.local_cache_timestamps[cache_key]

        # Publish invalidation
        self._publish_invalidation("*", pattern)

        return success

    def clear(self) -> bool:
        """
        Clear all keys in namespace.

        Returns:
            True if successful, False otherwise
        """
        # Get all keys in namespace
        pattern = f"{self.config.namespace}:*"
        keys = self.redis.keys(pattern)

        # Delete keys
        success = True
        for key in keys:
            if not self.redis.delete(key):
                success = False

        # Clear local cache
        if self.config.use_local_cache:
            with self.local_cache_lock:
                self.local_cache.clear()
                self.local_cache_timestamps.clear()

                # Clear eviction policy
                if hasattr(self, 'eviction_policy') and self.eviction_policy is not None:
                    self.eviction_policy.clear()

        # Publish invalidation
        self._publish_invalidation("*", "*")

        return success

    def get_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics.

        Returns:
            Dictionary of statistics
        """
        # Get all keys in namespace
        pattern = f"{self.config.namespace}:*"
        keys = self.redis.keys(pattern)

        # Count keys by partition
        partition_counts = {}
        for key in keys:
            parts = key.split(":")
            if len(parts) >= 2:
                partition = parts[1]
                if partition.startswith("p"):
                    partition_counts[partition] = partition_counts.get(partition, 0) + 1

        # Get local cache stats
        local_cache_size = 0
        if self.config.use_local_cache:
            with self.local_cache_lock:
                local_cache_size = len(self.local_cache)

        # Get eviction policy stats
        eviction_policy_stats = {}
        if hasattr(self, 'eviction_policy') and self.eviction_policy is not None:
            eviction_policy_stats = {
                "policy_type": self.config.eviction_policy_type,
                "policy_size": self.eviction_policy.size()
            }

        return {
            "namespace": self.config.namespace,
            "total_keys": len(keys),
            "partition_counts": partition_counts,
            "local_cache_size": local_cache_size,
            "local_cache_enabled": self.config.use_local_cache,
            "local_cache_capacity": self.config.local_cache_size,
            "local_cache_ttl": self.config.local_cache_ttl,
            "default_ttl": self.config.ttl,
            "partition_count": self.config.partition_count,
            "eviction_policy": eviction_policy_stats
        }

    def close(self):
        """Close cache and release resources."""
        # Stop invalidation listener
        if self.invalidation_listener_thread is not None:
            self.stop_invalidation_listener.set()
            self.invalidation_listener_thread.join(timeout=1.0)

        # Close Redis connection
        self.redis.close()

        # Clear local cache
        if self.config.use_local_cache:
            with self.local_cache_lock:
                self.local_cache.clear()
                self.local_cache_timestamps.clear()

                # Clear eviction policy
                if hasattr(self, 'eviction_policy') and self.eviction_policy is not None:
                    self.eviction_policy.clear()


def distributed_cache(
    key_func: Optional[Callable] = None,
    namespace: Optional[str] = None,
    ttl: Optional[int] = None,
    cache_instance: Optional[DistributedCache] = None
):
    """
    Decorator for caching function results in distributed cache.

    Args:
        key_func: Function to generate cache key from arguments
        namespace: Cache namespace
        ttl: Time-to-live in seconds
        cache_instance: Cache instance to use

    Returns:
        Decorated function
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Get cache instance
            nonlocal cache_instance
            if cache_instance is None:
                try:
                    cache_instance = DistributedCache(
                        DistributedCacheConfig(namespace=namespace or func.__name__)
                    )
                except ImportError:
                    # Redis not available, call function directly
                    return func(*args, **kwargs)

            # Generate cache key
            if key_func is not None:
                cache_key = key_func(*args, **kwargs)
            else:
                # Default key generation
                arg_str = ",".join(str(arg) for arg in args)
                kwarg_str = ",".join(f"{k}={v}" for k, v in sorted(kwargs.items()))
                cache_key = f"{func.__module__}.{func.__name__}({arg_str},{kwarg_str})"

            # Try to get from cache
            cached_result = cache_instance.get(cache_key)
            if cached_result is not None:
                return cached_result

            # Call function
            result = func(*args, **kwargs)

            # Store in cache
            cache_instance.set(cache_key, result, ttl)

            return result

        return wrapper

    return decorator


# Create a global instance for convenience
try:
    default_distributed_cache = DistributedCache() if REDIS_AVAILABLE else None
except (ImportError, Exception) as e:
    logger.warning(f"Failed to create default distributed cache: {str(e)}")
    default_distributed_cache = None
