"""
Cache eviction policies for Deep Research Core.

This module provides various cache eviction policies for managing cache size
and improving cache efficiency, including LRU, LFU, and TTL policies.
"""

import time
import threading
import heapq
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Set, Tuple, Union, TypeVar, Generic, Callable
from collections import OrderedDict, Counter, defaultdict

from deep_research_core.utils.structured_logging import get_logger

# Get logger
logger = get_logger(__name__)

# Type variable for cache keys
K = TypeVar('K')
# Type variable for cache values
V = TypeVar('V')


class EvictionPolicy(Generic[K, V], ABC):
    """
    Abstract base class for cache eviction policies.
    
    This class defines the interface that all eviction policies must implement.
    """
    
    @abstractmethod
    def add(self, key: K, value: V) -> None:
        """
        Add a key-value pair to the cache.
        
        Args:
            key: Cache key
            value: Cache value
        """
        pass
    
    @abstractmethod
    def get(self, key: K) -> Optional[V]:
        """
        Get a value from the cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cache value or None if not found
        """
        pass
    
    @abstractmethod
    def remove(self, key: K) -> bool:
        """
        Remove a key from the cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if key was removed, False otherwise
        """
        pass
    
    @abstractmethod
    def clear(self) -> None:
        """Clear the cache."""
        pass
    
    @abstractmethod
    def evict(self) -> Optional[K]:
        """
        Evict a key from the cache according to the policy.
        
        Returns:
            Evicted key or None if cache is empty
        """
        pass
    
    @abstractmethod
    def evict_multiple(self, count: int) -> List[K]:
        """
        Evict multiple keys from the cache according to the policy.
        
        Args:
            count: Number of keys to evict
            
        Returns:
            List of evicted keys
        """
        pass
    
    @abstractmethod
    def contains(self, key: K) -> bool:
        """
        Check if key is in the cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if key is in the cache, False otherwise
        """
        pass
    
    @abstractmethod
    def size(self) -> int:
        """
        Get the number of items in the cache.
        
        Returns:
            Number of items in the cache
        """
        pass
    
    @abstractmethod
    def keys(self) -> List[K]:
        """
        Get all keys in the cache.
        
        Returns:
            List of keys in the cache
        """
        pass
    
    @abstractmethod
    def values(self) -> List[V]:
        """
        Get all values in the cache.
        
        Returns:
            List of values in the cache
        """
        pass
    
    @abstractmethod
    def items(self) -> List[Tuple[K, V]]:
        """
        Get all key-value pairs in the cache.
        
        Returns:
            List of key-value pairs in the cache
        """
        pass


class LRUPolicy(EvictionPolicy[K, V]):
    """
    Least Recently Used (LRU) eviction policy.
    
    This policy evicts the least recently used items first.
    """
    
    def __init__(self, capacity: int = 1000):
        """
        Initialize LRU policy.
        
        Args:
            capacity: Maximum number of items in the cache
        """
        self.capacity = capacity
        self.cache = OrderedDict()  # type: OrderedDict[K, V]
        self.lock = threading.RLock()
    
    def add(self, key: K, value: V) -> None:
        """
        Add a key-value pair to the cache.
        
        Args:
            key: Cache key
            value: Cache value
        """
        with self.lock:
            # If key already exists, remove it first
            if key in self.cache:
                self.cache.pop(key)
            
            # Add key-value pair
            self.cache[key] = value
            
            # Evict if over capacity
            if len(self.cache) > self.capacity:
                self.evict()
    
    def get(self, key: K) -> Optional[V]:
        """
        Get a value from the cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cache value or None if not found
        """
        with self.lock:
            if key not in self.cache:
                return None
            
            # Move key to the end (most recently used)
            value = self.cache.pop(key)
            self.cache[key] = value
            
            return value
    
    def remove(self, key: K) -> bool:
        """
        Remove a key from the cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if key was removed, False otherwise
        """
        with self.lock:
            if key in self.cache:
                self.cache.pop(key)
                return True
            return False
    
    def clear(self) -> None:
        """Clear the cache."""
        with self.lock:
            self.cache.clear()
    
    def evict(self) -> Optional[K]:
        """
        Evict the least recently used key from the cache.
        
        Returns:
            Evicted key or None if cache is empty
        """
        with self.lock:
            if not self.cache:
                return None
            
            # Get the first key (least recently used)
            key, _ = self.cache.popitem(last=False)
            return key
    
    def evict_multiple(self, count: int) -> List[K]:
        """
        Evict multiple keys from the cache.
        
        Args:
            count: Number of keys to evict
            
        Returns:
            List of evicted keys
        """
        with self.lock:
            if not self.cache:
                return []
            
            # Limit count to cache size
            count = min(count, len(self.cache))
            
            # Evict keys
            evicted_keys = []
            for _ in range(count):
                key, _ = self.cache.popitem(last=False)
                evicted_keys.append(key)
            
            return evicted_keys
    
    def contains(self, key: K) -> bool:
        """
        Check if key is in the cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if key is in the cache, False otherwise
        """
        with self.lock:
            return key in self.cache
    
    def size(self) -> int:
        """
        Get the number of items in the cache.
        
        Returns:
            Number of items in the cache
        """
        with self.lock:
            return len(self.cache)
    
    def keys(self) -> List[K]:
        """
        Get all keys in the cache.
        
        Returns:
            List of keys in the cache
        """
        with self.lock:
            return list(self.cache.keys())
    
    def values(self) -> List[V]:
        """
        Get all values in the cache.
        
        Returns:
            List of values in the cache
        """
        with self.lock:
            return list(self.cache.values())
    
    def items(self) -> List[Tuple[K, V]]:
        """
        Get all key-value pairs in the cache.
        
        Returns:
            List of key-value pairs in the cache
        """
        with self.lock:
            return list(self.cache.items())


class LFUPolicy(EvictionPolicy[K, V]):
    """
    Least Frequently Used (LFU) eviction policy.
    
    This policy evicts the least frequently used items first.
    """
    
    def __init__(self, capacity: int = 1000):
        """
        Initialize LFU policy.
        
        Args:
            capacity: Maximum number of items in the cache
        """
        self.capacity = capacity
        self.cache = {}  # type: Dict[K, V]
        self.frequencies = Counter()  # type: Counter[K]
        self.lock = threading.RLock()
    
    def add(self, key: K, value: V) -> None:
        """
        Add a key-value pair to the cache.
        
        Args:
            key: Cache key
            value: Cache value
        """
        with self.lock:
            # Add or update key-value pair
            self.cache[key] = value
            
            # Initialize or increment frequency
            self.frequencies[key] += 1
            
            # Evict if over capacity
            if len(self.cache) > self.capacity:
                self.evict()
    
    def get(self, key: K) -> Optional[V]:
        """
        Get a value from the cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cache value or None if not found
        """
        with self.lock:
            if key not in self.cache:
                return None
            
            # Increment frequency
            self.frequencies[key] += 1
            
            return self.cache[key]
    
    def remove(self, key: K) -> bool:
        """
        Remove a key from the cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if key was removed, False otherwise
        """
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                del self.frequencies[key]
                return True
            return False
    
    def clear(self) -> None:
        """Clear the cache."""
        with self.lock:
            self.cache.clear()
            self.frequencies.clear()
    
    def evict(self) -> Optional[K]:
        """
        Evict the least frequently used key from the cache.
        
        Returns:
            Evicted key or None if cache is empty
        """
        with self.lock:
            if not self.cache:
                return None
            
            # Find the key with the lowest frequency
            min_freq_key = min(self.frequencies.items(), key=lambda x: x[1])[0]
            
            # Remove key from cache and frequencies
            del self.cache[min_freq_key]
            del self.frequencies[min_freq_key]
            
            return min_freq_key
    
    def evict_multiple(self, count: int) -> List[K]:
        """
        Evict multiple keys from the cache.
        
        Args:
            count: Number of keys to evict
            
        Returns:
            List of evicted keys
        """
        with self.lock:
            if not self.cache:
                return []
            
            # Limit count to cache size
            count = min(count, len(self.cache))
            
            # Get keys sorted by frequency
            sorted_keys = sorted(self.frequencies.items(), key=lambda x: x[1])
            
            # Evict keys
            evicted_keys = []
            for i in range(count):
                key = sorted_keys[i][0]
                del self.cache[key]
                del self.frequencies[key]
                evicted_keys.append(key)
            
            return evicted_keys
    
    def contains(self, key: K) -> bool:
        """
        Check if key is in the cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if key is in the cache, False otherwise
        """
        with self.lock:
            return key in self.cache
    
    def size(self) -> int:
        """
        Get the number of items in the cache.
        
        Returns:
            Number of items in the cache
        """
        with self.lock:
            return len(self.cache)
    
    def keys(self) -> List[K]:
        """
        Get all keys in the cache.
        
        Returns:
            List of keys in the cache
        """
        with self.lock:
            return list(self.cache.keys())
    
    def values(self) -> List[V]:
        """
        Get all values in the cache.
        
        Returns:
            List of values in the cache
        """
        with self.lock:
            return list(self.cache.values())
    
    def items(self) -> List[Tuple[K, V]]:
        """
        Get all key-value pairs in the cache.
        
        Returns:
            List of key-value pairs in the cache
        """
        with self.lock:
            return list(self.cache.items())


class TTLPolicy(EvictionPolicy[K, V]):
    """
    Time-To-Live (TTL) eviction policy.
    
    This policy evicts items that have expired based on their TTL.
    """
    
    def __init__(self, default_ttl: int = 3600, capacity: int = 1000, cleanup_interval: int = 60):
        """
        Initialize TTL policy.
        
        Args:
            default_ttl: Default time-to-live in seconds
            capacity: Maximum number of items in the cache
            cleanup_interval: Interval in seconds for automatic cleanup
        """
        self.default_ttl = default_ttl
        self.capacity = capacity
        self.cleanup_interval = cleanup_interval
        
        self.cache = {}  # type: Dict[K, V]
        self.expiry_times = {}  # type: Dict[K, float]
        self.lock = threading.RLock()
        
        # Start cleanup thread
        self.stop_cleanup = threading.Event()
        self.cleanup_thread = threading.Thread(target=self._cleanup_worker, daemon=True)
        self.cleanup_thread.start()
    
    def _cleanup_worker(self):
        """Worker function for cleanup thread."""
        while not self.stop_cleanup.is_set():
            # Sleep for cleanup interval
            self.stop_cleanup.wait(self.cleanup_interval)
            
            # Clean up expired items
            self._cleanup_expired()
    
    def _cleanup_expired(self):
        """Clean up expired items."""
        with self.lock:
            current_time = time.time()
            expired_keys = [key for key, expiry_time in self.expiry_times.items() 
                           if expiry_time <= current_time]
            
            for key in expired_keys:
                del self.cache[key]
                del self.expiry_times[key]
    
    def add(self, key: K, value: V, ttl: Optional[int] = None) -> None:
        """
        Add a key-value pair to the cache.
        
        Args:
            key: Cache key
            value: Cache value
            ttl: Time-to-live in seconds (None for default TTL)
        """
        with self.lock:
            # Add or update key-value pair
            self.cache[key] = value
            
            # Set expiry time
            if ttl is None:
                ttl = self.default_ttl
            
            self.expiry_times[key] = time.time() + ttl
            
            # Evict if over capacity
            if len(self.cache) > self.capacity:
                self.evict()
    
    def get(self, key: K) -> Optional[V]:
        """
        Get a value from the cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cache value or None if not found or expired
        """
        with self.lock:
            if key not in self.cache:
                return None
            
            # Check if expired
            current_time = time.time()
            if self.expiry_times[key] <= current_time:
                # Remove expired item
                del self.cache[key]
                del self.expiry_times[key]
                return None
            
            return self.cache[key]
    
    def remove(self, key: K) -> bool:
        """
        Remove a key from the cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if key was removed, False otherwise
        """
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                del self.expiry_times[key]
                return True
            return False
    
    def clear(self) -> None:
        """Clear the cache."""
        with self.lock:
            self.cache.clear()
            self.expiry_times.clear()
    
    def evict(self) -> Optional[K]:
        """
        Evict the item with the earliest expiry time from the cache.
        
        Returns:
            Evicted key or None if cache is empty
        """
        with self.lock:
            if not self.cache:
                return None
            
            # Find the key with the earliest expiry time
            earliest_key = min(self.expiry_times.items(), key=lambda x: x[1])[0]
            
            # Remove key from cache and expiry times
            del self.cache[earliest_key]
            del self.expiry_times[earliest_key]
            
            return earliest_key
    
    def evict_multiple(self, count: int) -> List[K]:
        """
        Evict multiple keys from the cache.
        
        Args:
            count: Number of keys to evict
            
        Returns:
            List of evicted keys
        """
        with self.lock:
            if not self.cache:
                return []
            
            # Limit count to cache size
            count = min(count, len(self.cache))
            
            # Get keys sorted by expiry time
            sorted_keys = sorted(self.expiry_times.items(), key=lambda x: x[1])
            
            # Evict keys
            evicted_keys = []
            for i in range(count):
                key = sorted_keys[i][0]
                del self.cache[key]
                del self.expiry_times[key]
                evicted_keys.append(key)
            
            return evicted_keys
    
    def contains(self, key: K) -> bool:
        """
        Check if key is in the cache and not expired.
        
        Args:
            key: Cache key
            
        Returns:
            True if key is in the cache and not expired, False otherwise
        """
        with self.lock:
            if key not in self.cache:
                return False
            
            # Check if expired
            current_time = time.time()
            if self.expiry_times[key] <= current_time:
                # Remove expired item
                del self.cache[key]
                del self.expiry_times[key]
                return False
            
            return True
    
    def size(self) -> int:
        """
        Get the number of items in the cache.
        
        Returns:
            Number of items in the cache
        """
        with self.lock:
            return len(self.cache)
    
    def keys(self) -> List[K]:
        """
        Get all keys in the cache.
        
        Returns:
            List of keys in the cache
        """
        with self.lock:
            # Clean up expired items first
            self._cleanup_expired()
            return list(self.cache.keys())
    
    def values(self) -> List[V]:
        """
        Get all values in the cache.
        
        Returns:
            List of values in the cache
        """
        with self.lock:
            # Clean up expired items first
            self._cleanup_expired()
            return list(self.cache.values())
    
    def items(self) -> List[Tuple[K, V]]:
        """
        Get all key-value pairs in the cache.
        
        Returns:
            List of key-value pairs in the cache
        """
        with self.lock:
            # Clean up expired items first
            self._cleanup_expired()
            return list(self.cache.items())
    
    def get_ttl(self, key: K) -> Optional[float]:
        """
        Get the remaining TTL for a key.
        
        Args:
            key: Cache key
            
        Returns:
            Remaining TTL in seconds or None if key not found or expired
        """
        with self.lock:
            if key not in self.expiry_times:
                return None
            
            # Calculate remaining TTL
            current_time = time.time()
            remaining = self.expiry_times[key] - current_time
            
            # If expired, remove and return None
            if remaining <= 0:
                if key in self.cache:
                    del self.cache[key]
                    del self.expiry_times[key]
                return None
            
            return remaining
    
    def update_ttl(self, key: K, ttl: Optional[int] = None) -> bool:
        """
        Update the TTL for a key.
        
        Args:
            key: Cache key
            ttl: New time-to-live in seconds (None for default TTL)
            
        Returns:
            True if TTL was updated, False if key not found
        """
        with self.lock:
            if key not in self.cache:
                return False
            
            # Set new expiry time
            if ttl is None:
                ttl = self.default_ttl
            
            self.expiry_times[key] = time.time() + ttl
            return True
    
    def __del__(self):
        """Clean up resources."""
        self.stop_cleanup.set()
        if hasattr(self, 'cleanup_thread') and self.cleanup_thread.is_alive():
            self.cleanup_thread.join(timeout=1.0)


class CompositePolicy(EvictionPolicy[K, V]):
    """
    Composite eviction policy.
    
    This policy combines multiple eviction policies with priorities.
    """
    
    def __init__(self, policies: List[Tuple[EvictionPolicy[K, V], int]], capacity: int = 1000):
        """
        Initialize composite policy.
        
        Args:
            policies: List of (policy, priority) tuples (higher priority first)
            capacity: Maximum number of items in the cache
        """
        self.policies = sorted(policies, key=lambda x: x[1], reverse=True)
        self.capacity = capacity
        self.cache = {}  # type: Dict[K, V]
        self.lock = threading.RLock()
    
    def add(self, key: K, value: V) -> None:
        """
        Add a key-value pair to the cache.
        
        Args:
            key: Cache key
            value: Cache value
        """
        with self.lock:
            # Add to all policies
            for policy, _ in self.policies:
                policy.add(key, value)
            
            # Add to local cache
            self.cache[key] = value
            
            # Evict if over capacity
            if len(self.cache) > self.capacity:
                self.evict()
    
    def get(self, key: K) -> Optional[V]:
        """
        Get a value from the cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cache value or None if not found
        """
        with self.lock:
            if key not in self.cache:
                return None
            
            # Update all policies
            for policy, _ in self.policies:
                policy.get(key)
            
            return self.cache[key]
    
    def remove(self, key: K) -> bool:
        """
        Remove a key from the cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if key was removed, False otherwise
        """
        with self.lock:
            if key in self.cache:
                # Remove from all policies
                for policy, _ in self.policies:
                    policy.remove(key)
                
                # Remove from local cache
                del self.cache[key]
                return True
            return False
    
    def clear(self) -> None:
        """Clear the cache."""
        with self.lock:
            # Clear all policies
            for policy, _ in self.policies:
                policy.clear()
            
            # Clear local cache
            self.cache.clear()
    
    def evict(self) -> Optional[K]:
        """
        Evict a key from the cache according to the policies.
        
        Returns:
            Evicted key or None if cache is empty
        """
        with self.lock:
            if not self.cache:
                return None
            
            # Try each policy in order of priority
            for policy, _ in self.policies:
                key = policy.evict()
                if key is not None and key in self.cache:
                    # Remove from all other policies
                    for other_policy, _ in self.policies:
                        if other_policy != policy:
                            other_policy.remove(key)
                    
                    # Remove from local cache
                    del self.cache[key]
                    return key
            
            # If all policies failed, remove any key
            key = next(iter(self.cache))
            
            # Remove from all policies
            for policy, _ in self.policies:
                policy.remove(key)
            
            # Remove from local cache
            del self.cache[key]
            return key
    
    def evict_multiple(self, count: int) -> List[K]:
        """
        Evict multiple keys from the cache.
        
        Args:
            count: Number of keys to evict
            
        Returns:
            List of evicted keys
        """
        with self.lock:
            if not self.cache:
                return []
            
            # Limit count to cache size
            count = min(count, len(self.cache))
            
            # Evict keys
            evicted_keys = []
            for _ in range(count):
                key = self.evict()
                if key is not None:
                    evicted_keys.append(key)
            
            return evicted_keys
    
    def contains(self, key: K) -> bool:
        """
        Check if key is in the cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if key is in the cache, False otherwise
        """
        with self.lock:
            return key in self.cache
    
    def size(self) -> int:
        """
        Get the number of items in the cache.
        
        Returns:
            Number of items in the cache
        """
        with self.lock:
            return len(self.cache)
    
    def keys(self) -> List[K]:
        """
        Get all keys in the cache.
        
        Returns:
            List of keys in the cache
        """
        with self.lock:
            return list(self.cache.keys())
    
    def values(self) -> List[V]:
        """
        Get all values in the cache.
        
        Returns:
            List of values in the cache
        """
        with self.lock:
            return list(self.cache.values())
    
    def items(self) -> List[Tuple[K, V]]:
        """
        Get all key-value pairs in the cache.
        
        Returns:
            List of key-value pairs in the cache
        """
        with self.lock:
            return list(self.cache.items())


class EvictionPolicyFactory:
    """Factory for creating eviction policies."""
    
    @staticmethod
    def create_policy(
        policy_type: str,
        capacity: int = 1000,
        ttl: int = 3600,
        cleanup_interval: int = 60
    ) -> EvictionPolicy:
        """
        Create an eviction policy.
        
        Args:
            policy_type: Type of policy ('lru', 'lfu', 'ttl', 'composite')
            capacity: Maximum number of items in the cache
            ttl: Default time-to-live in seconds (for TTL policy)
            cleanup_interval: Interval in seconds for automatic cleanup (for TTL policy)
            
        Returns:
            Eviction policy
        """
        policy_type = policy_type.lower()
        
        if policy_type == 'lru':
            return LRUPolicy(capacity=capacity)
        elif policy_type == 'lfu':
            return LFUPolicy(capacity=capacity)
        elif policy_type == 'ttl':
            return TTLPolicy(default_ttl=ttl, capacity=capacity, cleanup_interval=cleanup_interval)
        elif policy_type == 'composite':
            # Create a composite policy with LRU and TTL
            lru_policy = LRUPolicy(capacity=capacity)
            ttl_policy = TTLPolicy(default_ttl=ttl, capacity=capacity, cleanup_interval=cleanup_interval)
            return CompositePolicy([(lru_policy, 1), (ttl_policy, 2)], capacity=capacity)
        else:
            logger.warning(f"Unknown policy type: {policy_type}. Using LRU policy.")
            return LRUPolicy(capacity=capacity)
