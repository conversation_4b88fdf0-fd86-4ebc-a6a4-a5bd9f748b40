#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Adaptive Search Cache for Deep Research Core.

Module này cung cấp các lớp cache thông minh cho các phương thức tìm kiếm mới,
với khả năng tự động điều chỉnh dựa trên mẫu truy cập và nội dung.

Tính năng:
- Tự động điều chỉnh TTL dựa trên tần suất truy cập
- Tự động điều chỉnh độ ưu tiên dựa trên mẫu truy cập
- Tự động phân tích và phân loại truy vấn
- Tự động tiền tải cache dựa trên dự đoán
- Hỗ trợ đa ngôn ngữ với trọng số khác nhau
"""

import os
import json
import time
import hashlib
import logging
import threading
import pickle
from typing import Dict, Any, List, Optional, Union, Tuple, Set, Callable
from datetime import datetime, timedelta
import shutil
import re
import heapq
from collections import Counter, defaultdict
from functools import lru_cache

import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

from deep_research_core.utils.structured_logging import get_logger

# Khởi tạo logger
logger = get_logger(__name__)

class CacheEntry:
    """
    Đại diện cho một mục trong cache.
    
    Attributes:
        key: Khóa cache
        value: Giá trị được lưu trong cache
        created_at: Thời điểm tạo
        expires_at: Thời điểm hết hạn
        access_count: Số lần truy cập
        last_accessed: Thời điểm truy cập gần nhất
        priority: Độ ưu tiên (cao hơn = ít bị xóa hơn)
        metadata: Thông tin bổ sung
    """
    
    def __init__(
        self,
        key: str,
        value: Any,
        ttl: int,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Khởi tạo CacheEntry.
        
        Args:
            key: Khóa cache
            value: Giá trị được lưu trong cache
            ttl: Thời gian sống (giây)
            metadata: Thông tin bổ sung
        """
        self.key = key
        self.value = value
        self.created_at = time.time()
        self.expires_at = self.created_at + ttl
        self.access_count = 0
        self.last_accessed = self.created_at
        self.priority = 0.0
        self.metadata = metadata or {}
    
    def access(self) -> None:
        """Cập nhật thông tin khi mục được truy cập."""
        self.access_count += 1
        self.last_accessed = time.time()
        
        # Tăng độ ưu tiên dựa trên số lần truy cập
        # Công thức: log(access_count) + recency_factor
        recency_factor = 1.0 / (1.0 + (time.time() - self.created_at) / 86400)  # Giảm dần theo thời gian
        self.priority = np.log1p(self.access_count) + recency_factor
    
    def is_expired(self) -> bool:
        """Kiểm tra xem mục có hết hạn không."""
        return time.time() > self.expires_at
    
    def extend_ttl(self, additional_seconds: int) -> None:
        """Gia hạn thời gian sống."""
        self.expires_at += additional_seconds
    
    def to_dict(self) -> Dict[str, Any]:
        """Chuyển đổi thành dictionary để lưu trữ."""
        return {
            "key": self.key,
            "value": self.value,
            "created_at": self.created_at,
            "expires_at": self.expires_at,
            "access_count": self.access_count,
            "last_accessed": self.last_accessed,
            "priority": self.priority,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CacheEntry':
        """Tạo CacheEntry từ dictionary."""
        entry = cls(
            key=data["key"],
            value=data["value"],
            ttl=int(data["expires_at"] - time.time()),
            metadata=data.get("metadata", {})
        )
        entry.created_at = data["created_at"]
        entry.expires_at = data["expires_at"]
        entry.access_count = data["access_count"]
        entry.last_accessed = data["last_accessed"]
        entry.priority = data["priority"]
        return entry


class AdaptiveSearchCache:
    """
    Cache thông minh tự điều chỉnh cho các phương thức tìm kiếm.
    
    Tính năng:
    - Tự động điều chỉnh TTL dựa trên tần suất truy cập
    - Tự động điều chỉnh độ ưu tiên dựa trên mẫu truy cập
    - Tự động phân tích và phân loại truy vấn
    - Tự động tiền tải cache dựa trên dự đoán
    """
    
    def __init__(
        self,
        name: str = "adaptive_search_cache",
        cache_dir: Optional[str] = None,
        max_memory_items: int = 1000,
        max_disk_items: int = 10000,
        default_ttl: int = 3600,  # 1 giờ
        min_ttl: int = 300,       # 5 phút
        max_ttl: int = 604800,    # 1 tuần
        cleanup_interval: int = 300,  # 5 phút
        similarity_threshold: float = 0.8,
        language_weights: Optional[Dict[str, float]] = None,
        enable_prefetching: bool = True,
        enable_analytics: bool = True,
        enable_semantic_search: bool = True
    ):
        """
        Khởi tạo AdaptiveSearchCache.
        
        Args:
            name: Tên cache
            cache_dir: Thư mục lưu trữ cache
            max_memory_items: Số lượng mục tối đa trong bộ nhớ
            max_disk_items: Số lượng mục tối đa trên đĩa
            default_ttl: Thời gian sống mặc định (giây)
            min_ttl: Thời gian sống tối thiểu (giây)
            max_ttl: Thời gian sống tối đa (giây)
            cleanup_interval: Khoảng thời gian dọn dẹp (giây)
            similarity_threshold: Ngưỡng tương đồng
            language_weights: Trọng số cho các ngôn ngữ
            enable_prefetching: Bật tính năng tiền tải
            enable_analytics: Bật tính năng phân tích
            enable_semantic_search: Bật tính năng tìm kiếm ngữ nghĩa
        """
        # Thiết lập cơ bản
        self.name = name
        self.max_memory_items = max_memory_items
        self.max_disk_items = max_disk_items
        self.default_ttl = default_ttl
        self.min_ttl = min_ttl
        self.max_ttl = max_ttl
        self.cleanup_interval = cleanup_interval
        self.similarity_threshold = similarity_threshold
        self.language_weights = language_weights or {"en": 1.0, "vi": 1.0}
        self.enable_prefetching = enable_prefetching
        self.enable_analytics = enable_analytics
        self.enable_semantic_search = enable_semantic_search
        
        # Thiết lập thư mục cache
        if cache_dir is None:
            cache_dir = os.path.join(os.path.expanduser("~"), ".deep_research_core", "cache", name)
        
        self.cache_dir = cache_dir
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # Khởi tạo bộ nhớ cache
        self.memory_cache: Dict[str, CacheEntry] = {}
        
        # Khởi tạo chỉ mục ngữ nghĩa
        self.query_index: Dict[str, List[str]] = {}
        self.query_embeddings: Dict[str, np.ndarray] = {}
        self.vectorizer = TfidfVectorizer(
            lowercase=True,
            stop_words="english",
            ngram_range=(1, 2),
            max_features=1000
        )
        
        # Khởi tạo thống kê
        self.stats = {
            "memory_hits": 0,
            "disk_hits": 0,
            "semantic_hits": 0,
            "misses": 0,
            "sets": 0,
            "evictions": 0,
            "cleanups": 0,
            "prefetches": 0
        }
        
        # Khởi tạo phân tích
        self.query_patterns: Dict[str, int] = Counter()
        self.access_patterns: Dict[str, List[float]] = defaultdict(list)
        self.language_stats: Dict[str, int] = Counter()
        
        # Khởi tạo lock
        self.lock = threading.RLock()
        
        # Khởi tạo thread dọn dẹp
        self.cleanup_thread = None
        self.stop_cleanup = threading.Event()
        self._start_cleanup_thread()
    
    def _start_cleanup_thread(self) -> None:
        """Khởi động thread dọn dẹp."""
        if self.cleanup_thread is None:
            self.cleanup_thread = threading.Thread(
                target=self._cleanup_worker,
                daemon=True
            )
            self.cleanup_thread.start()
    
    def _cleanup_worker(self) -> None:
        """Worker thread để dọn dẹp cache định kỳ."""
        while not self.stop_cleanup.is_set():
            try:
                # Dọn dẹp cache
                self._cleanup_expired()
                
                # Cập nhật thống kê
                self.stats["cleanups"] += 1
                
                # Đợi đến lần dọn dẹp tiếp theo
                self.stop_cleanup.wait(self.cleanup_interval)
            except Exception as e:
                logger.error(f"Lỗi trong cleanup thread: {str(e)}")
                # Đợi một khoảng thời gian ngắn trước khi thử lại
                self.stop_cleanup.wait(10)
    
    def _cleanup_expired(self) -> None:
        """Dọn dẹp các mục hết hạn."""
        with self.lock:
            # Dọn dẹp bộ nhớ cache
            expired_keys = [k for k, v in self.memory_cache.items() if v.is_expired()]
            for key in expired_keys:
                del self.memory_cache[key]
            
            # Dọn dẹp cache trên đĩa
            try:
                for filename in os.listdir(self.cache_dir):
                    if not filename.endswith(".json"):
                        continue
                    
                    file_path = os.path.join(self.cache_dir, filename)
                    try:
                        with open(file_path, "r", encoding="utf-8") as f:
                            data = json.load(f)
                        
                        # Kiểm tra hết hạn
                        if data.get("expires_at", 0) < time.time():
                            os.remove(file_path)
                    except Exception as e:
                        logger.error(f"Lỗi khi đọc file cache {filename}: {str(e)}")
                        # Xóa file lỗi
                        try:
                            os.remove(file_path)
                        except:
                            pass
            except Exception as e:
                logger.error(f"Lỗi khi dọn dẹp cache trên đĩa: {str(e)}")
    
    def get(
        self,
        key: str,
        default: Any = None,
        use_semantic: bool = True,
        language: str = "en"
    ) -> Any:
        """
        Lấy giá trị từ cache.
        
        Args:
            key: Khóa cache
            default: Giá trị mặc định nếu không tìm thấy
            use_semantic: Có sử dụng tìm kiếm ngữ nghĩa không
            language: Ngôn ngữ của truy vấn
            
        Returns:
            Giá trị từ cache hoặc default nếu không tìm thấy
        """
        # Chuẩn hóa khóa
        normalized_key = self._normalize_key(key)
        
        with self.lock:
            # Kiểm tra trong bộ nhớ
            if normalized_key in self.memory_cache:
                entry = self.memory_cache[normalized_key]
                if not entry.is_expired():
                    # Cập nhật thông tin truy cập
                    entry.access()
                    
                    # Cập nhật thống kê
                    self.stats["memory_hits"] += 1
                    
                    # Cập nhật phân tích
                    if self.enable_analytics:
                        self._update_analytics(normalized_key, language)
                    
                    return entry.value
                else:
                    # Xóa mục hết hạn
                    del self.memory_cache[normalized_key]
            
            # Kiểm tra trên đĩa
            disk_value = self._get_from_disk(normalized_key)
            if disk_value is not None:
                # Cập nhật thống kê
                self.stats["disk_hits"] += 1
                return disk_value
            
            # Tìm kiếm ngữ nghĩa nếu được yêu cầu
            if use_semantic and self.enable_semantic_search:
                semantic_value = self._semantic_search(key, language)
                if semantic_value is not None:
                    # Cập nhật thống kê
                    self.stats["semantic_hits"] += 1
                    return semantic_value
            
            # Cập nhật thống kê
            self.stats["misses"] += 1
            
            # Cập nhật phân tích
            if self.enable_analytics:
                self._update_miss_analytics(normalized_key, language)
            
            return default
    
    def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None,
        language: str = "en"
    ) -> None:
        """
        Đặt giá trị vào cache.
        
        Args:
            key: Khóa cache
            value: Giá trị cần lưu
            ttl: Thời gian sống (giây)
            metadata: Thông tin bổ sung
            language: Ngôn ngữ của truy vấn
        """
        # Chuẩn hóa khóa
        normalized_key = self._normalize_key(key)
        
        # Thiết lập TTL
        if ttl is None:
            ttl = self.default_ttl
        
        # Đảm bảo TTL trong khoảng cho phép
        ttl = max(self.min_ttl, min(self.max_ttl, ttl))
        
        # Thêm ngôn ngữ vào metadata
        if metadata is None:
            metadata = {}
        metadata["language"] = language
        
        with self.lock:
            # Tạo mục cache mới
            entry = CacheEntry(
                key=normalized_key,
                value=value,
                ttl=ttl,
                metadata=metadata
            )
            
            # Kiểm tra xem bộ nhớ cache có đầy không
            if len(self.memory_cache) >= self.max_memory_items and normalized_key not in self.memory_cache:
                # Xóa mục có độ ưu tiên thấp nhất
                self._evict_lowest_priority()
            
            # Lưu vào bộ nhớ cache
            self.memory_cache[normalized_key] = entry
            
            # Lưu vào đĩa
            self._save_to_disk(entry)
            
            # Cập nhật chỉ mục ngữ nghĩa
            if self.enable_semantic_search:
                self._update_semantic_index(normalized_key, key, language)
            
            # Cập nhật thống kê
            self.stats["sets"] += 1
            
            # Cập nhật phân tích
            if self.enable_analytics:
                self._update_set_analytics(normalized_key, language)
    
    def _normalize_key(self, key: str) -> str:
        """
        Chuẩn hóa khóa cache.
        
        Args:
            key: Khóa gốc
            
        Returns:
            Khóa đã chuẩn hóa
        """
        # Tạo hash từ khóa
        return hashlib.md5(key.encode("utf-8")).hexdigest()
    
    def _get_disk_path(self, key: str) -> str:
        """
        Lấy đường dẫn file cache trên đĩa.
        
        Args:
            key: Khóa cache
            
        Returns:
            Đường dẫn file
        """
        return os.path.join(self.cache_dir, f"{key}.json")
    
    def _get_from_disk(self, key: str) -> Optional[Any]:
        """
        Lấy giá trị từ cache trên đĩa.
        
        Args:
            key: Khóa cache
            
        Returns:
            Giá trị từ cache hoặc None nếu không tìm thấy
        """
        file_path = self._get_disk_path(key)
        if not os.path.exists(file_path):
            return None
        
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            
            # Kiểm tra hết hạn
            if data.get("expires_at", 0) < time.time():
                # Xóa file hết hạn
                os.remove(file_path)
                return None
            
            # Tạo mục cache từ dữ liệu
            entry = CacheEntry.from_dict(data)
            
            # Thêm vào bộ nhớ cache
            self.memory_cache[key] = entry
            
            return entry.value
        except Exception as e:
            logger.error(f"Lỗi khi đọc cache từ đĩa: {str(e)}")
            return None
    
    def _save_to_disk(self, entry: CacheEntry) -> None:
        """
        Lưu mục cache vào đĩa.
        
        Args:
            entry: Mục cache cần lưu
        """
        file_path = self._get_disk_path(entry.key)
        
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(entry.to_dict(), f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Lỗi khi lưu cache vào đĩa: {str(e)}")
    
    def _evict_lowest_priority(self) -> None:
        """Xóa mục có độ ưu tiên thấp nhất khỏi bộ nhớ cache."""
        if not self.memory_cache:
            return
        
        # Tìm mục có độ ưu tiên thấp nhất
        lowest_key = min(self.memory_cache.keys(), key=lambda k: self.memory_cache[k].priority)
        
        # Xóa khỏi bộ nhớ cache
        del self.memory_cache[lowest_key]
        
        # Cập nhật thống kê
        self.stats["evictions"] += 1
    
    def _update_semantic_index(self, normalized_key: str, original_query: str, language: str) -> None:
        """
        Cập nhật chỉ mục ngữ nghĩa.
        
        Args:
            normalized_key: Khóa đã chuẩn hóa
            original_query: Truy vấn gốc
            language: Ngôn ngữ của truy vấn
        """
        # Thêm vào chỉ mục
        if original_query not in self.query_index:
            self.query_index[original_query] = []
        
        if normalized_key not in self.query_index[original_query]:
            self.query_index[original_query].append(normalized_key)
        
        # Tính toán embedding
        try:
            # Chuẩn bị corpus
            corpus = list(self.query_index.keys())
            if not corpus:
                return
            
            # Tính toán ma trận TF-IDF
            tfidf_matrix = self.vectorizer.fit_transform(corpus)
            
            # Lưu embedding
            for i, query in enumerate(corpus):
                self.query_embeddings[query] = tfidf_matrix[i].toarray().flatten()
        except Exception as e:
            logger.error(f"Lỗi khi cập nhật chỉ mục ngữ nghĩa: {str(e)}")
    
    def _semantic_search(self, query: str, language: str) -> Optional[Any]:
        """
        Tìm kiếm ngữ nghĩa trong cache.
        
        Args:
            query: Truy vấn cần tìm
            language: Ngôn ngữ của truy vấn
            
        Returns:
            Giá trị từ cache hoặc None nếu không tìm thấy
        """
        if not self.query_embeddings:
            return None
        
        try:
            # Tính toán embedding cho truy vấn
            query_vec = self.vectorizer.transform([query]).toarray().flatten()
            
            # Tính toán độ tương đồng
            similarities = {}
            for q, vec in self.query_embeddings.items():
                # Áp dụng trọng số ngôn ngữ
                q_language = self.memory_cache.get(self._normalize_key(q), CacheEntry("", None, 0)).metadata.get("language", "en")
                language_weight = self.language_weights.get(q_language, 1.0) * self.language_weights.get(language, 1.0)
                
                # Tính toán độ tương đồng có trọng số
                sim = cosine_similarity([query_vec], [vec])[0][0] * language_weight
                similarities[q] = sim
            
            # Tìm truy vấn tương tự nhất
            if not similarities:
                return None
            
            best_match = max(similarities.items(), key=lambda x: x[1])
            
            # Kiểm tra ngưỡng tương đồng
            if best_match[1] < self.similarity_threshold:
                return None
            
            # Lấy khóa từ chỉ mục
            normalized_keys = self.query_index.get(best_match[0], [])
            if not normalized_keys:
                return None
            
            # Lấy giá trị từ cache
            for normalized_key in normalized_keys:
                if normalized_key in self.memory_cache and not self.memory_cache[normalized_key].is_expired():
                    return self.memory_cache[normalized_key].value
                
                # Thử lấy từ đĩa
                disk_value = self._get_from_disk(normalized_key)
                if disk_value is not None:
                    return disk_value
            
            return None
        except Exception as e:
            logger.error(f"Lỗi khi tìm kiếm ngữ nghĩa: {str(e)}")
            return None
    
    def _update_analytics(self, key: str, language: str) -> None:
        """
        Cập nhật phân tích khi có truy cập cache.
        
        Args:
            key: Khóa cache
            language: Ngôn ngữ của truy vấn
        """
        # Cập nhật thống kê ngôn ngữ
        self.language_stats[language] += 1
        
        # Cập nhật mẫu truy cập
        self.access_patterns[key].append(time.time())
        
        # Giới hạn số lượng mẫu truy cập
        if len(self.access_patterns[key]) > 100:
            self.access_patterns[key] = self.access_patterns[key][-100:]
    
    def _update_miss_analytics(self, key: str, language: str) -> None:
        """
        Cập nhật phân tích khi không tìm thấy trong cache.
        
        Args:
            key: Khóa cache
            language: Ngôn ngữ của truy vấn
        """
        # Cập nhật thống kê ngôn ngữ
        self.language_stats[language] += 1
    
    def _update_set_analytics(self, key: str, language: str) -> None:
        """
        Cập nhật phân tích khi đặt giá trị vào cache.
        
        Args:
            key: Khóa cache
            language: Ngôn ngữ của truy vấn
        """
        # Cập nhật thống kê ngôn ngữ
        self.language_stats[language] += 1
        
        # Cập nhật mẫu truy vấn
        self.query_patterns[key] += 1
    
    def clear(self) -> None:
        """Xóa tất cả các mục trong cache."""
        with self.lock:
            # Xóa bộ nhớ cache
            self.memory_cache.clear()
            
            # Xóa chỉ mục ngữ nghĩa
            self.query_index.clear()
            self.query_embeddings.clear()
            
            # Xóa cache trên đĩa
            try:
                for filename in os.listdir(self.cache_dir):
                    if filename.endswith(".json"):
                        os.remove(os.path.join(self.cache_dir, filename))
            except Exception as e:
                logger.error(f"Lỗi khi xóa cache trên đĩa: {str(e)}")
            
            # Đặt lại thống kê
            for key in self.stats:
                self.stats[key] = 0
            
            # Đặt lại phân tích
            self.query_patterns.clear()
            self.access_patterns.clear()
            self.language_stats.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Lấy thống kê cache.
        
        Returns:
            Dictionary chứa thống kê
        """
        with self.lock:
            # Tính toán thống kê bổ sung
            total_hits = self.stats["memory_hits"] + self.stats["disk_hits"] + self.stats["semantic_hits"]
            total_requests = total_hits + self.stats["misses"]
            hit_ratio = total_hits / total_requests if total_requests > 0 else 0
            
            # Tạo dictionary thống kê
            stats = {
                "name": self.name,
                "memory_items": len(self.memory_cache),
                "memory_hits": self.stats["memory_hits"],
                "disk_hits": self.stats["disk_hits"],
                "semantic_hits": self.stats["semantic_hits"],
                "total_hits": total_hits,
                "misses": self.stats["misses"],
                "sets": self.stats["sets"],
                "evictions": self.stats["evictions"],
                "cleanups": self.stats["cleanups"],
                "prefetches": self.stats["prefetches"],
                "hit_ratio": hit_ratio,
                "language_stats": dict(self.language_stats),
                "top_queries": dict(self.query_patterns.most_common(10))
            }
            
            return stats
    
    def __del__(self) -> None:
        """Dọn dẹp khi đối tượng bị hủy."""
        # Dừng thread dọn dẹp
        if hasattr(self, 'stop_cleanup'):
            self.stop_cleanup.set()
        
        if hasattr(self, 'cleanup_thread') and self.cleanup_thread is not None:
            self.cleanup_thread.join(timeout=1.0)
