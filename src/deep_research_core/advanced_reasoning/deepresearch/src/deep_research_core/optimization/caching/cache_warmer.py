"""
Cache warmer implementation for Deep Research Core.

This module provides functionality for warming cache,
including startup warming and background warming.
"""

import time
import json
import threading
import queue
from typing import Dict, List, Any, Optional, Set, Tuple, Union, Callable, TypeVar, Generic

from deep_research_core.utils.structured_logging import get_logger
from deep_research_core.optimization.caching.warming_strategies import (
    WarmingStrategy,
    FrequencyBasedStrategy,
    RecencyBasedStrategy,
    PatternBasedStrategy,
    HybridStrategy,
    PredictiveStrategy,
    WarmingStrategyFactory
)

# Get logger
logger = get_logger(__name__)

# Type variables
K = TypeVar('K')  # Key type
V = TypeVar('V')  # Value type


class CacheWarmerConfig:
    """Configuration for cache warmer."""
    
    def __init__(
        self,
        strategy_type: str = 'hybrid',
        strategy_params: Optional[Dict[str, Any]] = None,
        startup_keys: Optional[List[str]] = None,
        startup_patterns: Optional[List[str]] = None,
        startup_key_file: Optional[str] = None,
        background_warming: bool = True,
        warming_interval: float = 300.0,  # 5 minutes
        max_keys_per_warming: int = 100,
        max_concurrent_warmings: int = 5,
        warming_timeout: float = 60.0,  # 1 minute
        enable_stats: bool = True,
        stats_interval: float = 3600.0  # 1 hour
    ):
        """
        Initialize configuration.
        
        Args:
            strategy_type: Type of warming strategy
            strategy_params: Parameters for warming strategy
            startup_keys: Keys to warm on startup
            startup_patterns: Patterns to warm on startup
            startup_key_file: File containing keys to warm on startup
            background_warming: Whether to enable background warming
            warming_interval: Interval in seconds between warming cycles
            max_keys_per_warming: Maximum number of keys to warm per cycle
            max_concurrent_warmings: Maximum number of concurrent warming operations
            warming_timeout: Timeout in seconds for warming operations
            enable_stats: Whether to collect and report statistics
            stats_interval: Interval in seconds for reporting statistics
        """
        self.strategy_type = strategy_type
        self.strategy_params = strategy_params or {}
        self.startup_keys = startup_keys or []
        self.startup_patterns = startup_patterns or []
        self.startup_key_file = startup_key_file
        self.background_warming = background_warming
        self.warming_interval = warming_interval
        self.max_keys_per_warming = max_keys_per_warming
        self.max_concurrent_warmings = max_concurrent_warmings
        self.warming_timeout = warming_timeout
        self.enable_stats = enable_stats
        self.stats_interval = stats_interval


class CacheWarmer(Generic[K, V]):
    """
    Cache warmer implementation.
    
    This class provides functionality for warming cache,
    including startup warming and background warming.
    """
    
    def __init__(
        self,
        cache: Any,
        fetch_func: Callable[[K], V],
        config: Optional[CacheWarmerConfig] = None,
        predictor: Any = None
    ):
        """
        Initialize cache warmer.
        
        Args:
            cache: Cache to warm
            fetch_func: Function to fetch values for keys
            config: Configuration
            predictor: Predictive cache or access pattern analyzer
        """
        self.cache = cache
        self.fetch_func = fetch_func
        self.config = config or CacheWarmerConfig()
        self.predictor = predictor
        
        # Create warming strategy
        self.strategy = WarmingStrategyFactory.create_strategy(
            strategy_type=self.config.strategy_type,
            predictor=self.predictor,
            **self.config.strategy_params
        )
        
        # Initialize warming queue
        self.warming_queue = queue.PriorityQueue()
        
        # Initialize sets for tracking
        self.warming = set()  # Keys currently being warmed
        self.warmed = set()   # Keys that have been warmed
        
        # Initialize locks
        self.warming_lock = threading.RLock()
        self.warmed_lock = threading.RLock()
        
        # Initialize stats
        self.stats = {
            "total_warmings": 0,
            "successful_warmings": 0,
            "failed_warmings": 0,
            "skipped_warmings": 0,
            "warming_time": 0.0,
            "last_warming_time": 0.0,
            "startup_warmings": 0,
            "background_warmings": 0
        }
        self.stats_lock = threading.RLock()
        
        # Initialize warming thread
        self.stop_warming = threading.Event()
        self.warming_thread = None
        
        # Initialize stats thread
        self.stop_stats = threading.Event()
        self.stats_thread = None
        
        # Perform startup warming
        self._perform_startup_warming()
        
        # Start warming thread if enabled
        if self.config.background_warming:
            self._start_warming_thread()
        
        # Start stats thread if enabled
        if self.config.enable_stats:
            self._start_stats_thread()
    
    def _start_warming_thread(self):
        """Start warming thread."""
        if self.warming_thread is not None:
            return
        
        self.warming_thread = threading.Thread(
            target=self._warming_worker,
            daemon=True
        )
        self.warming_thread.start()
    
    def _warming_worker(self):
        """Worker function for warming thread."""
        while not self.stop_warming.is_set():
            try:
                # Perform background warming
                self._perform_background_warming()
                
                # Sleep for warming interval
                self.stop_warming.wait(self.config.warming_interval)
            
            except Exception as e:
                logger.error(f"Error in warming worker: {str(e)}")
    
    def _start_stats_thread(self):
        """Start stats thread."""
        if self.stats_thread is not None:
            return
        
        self.stats_thread = threading.Thread(
            target=self._stats_worker,
            daemon=True
        )
        self.stats_thread.start()
    
    def _stats_worker(self):
        """Worker function for stats thread."""
        while not self.stop_stats.is_set():
            try:
                # Report stats
                self._report_stats()
                
                # Sleep for stats interval
                self.stop_stats.wait(self.config.stats_interval)
            
            except Exception as e:
                logger.error(f"Error in stats worker: {str(e)}")
    
    def _report_stats(self):
        """Report warming statistics."""
        with self.stats_lock:
            # Calculate success rate
            total = self.stats["successful_warmings"] + self.stats["failed_warmings"]
            success_rate = self.stats["successful_warmings"] / total if total > 0 else 0
            
            # Calculate average warming time
            avg_warming_time = self.stats["warming_time"] / total if total > 0 else 0
            
            logger.info(f"Cache warming stats: total={self.stats['total_warmings']}, "
                       f"successful={self.stats['successful_warmings']}, "
                       f"failed={self.stats['failed_warmings']}, "
                       f"skipped={self.stats['skipped_warmings']}, "
                       f"success_rate={success_rate:.2f}, "
                       f"avg_time={avg_warming_time:.4f}s, "
                       f"startup={self.stats['startup_warmings']}, "
                       f"background={self.stats['background_warmings']}")
    
    def _perform_startup_warming(self):
        """Perform startup warming."""
        startup_keys = set()
        
        # Add keys from config
        startup_keys.update(self.config.startup_keys)
        
        # Add keys from patterns
        for pattern in self.config.startup_patterns:
            if hasattr(self.cache, 'keys'):
                # Get keys matching pattern
                matching_keys = [key for key in self.cache.keys() if self._match_pattern(key, pattern)]
                startup_keys.update(matching_keys)
        
        # Add keys from file
        if self.config.startup_key_file:
            try:
                with open(self.config.startup_key_file, 'r') as f:
                    file_keys = json.load(f)
                    startup_keys.update(file_keys)
            except Exception as e:
                logger.error(f"Error loading startup keys from file: {str(e)}")
        
        # Warm startup keys
        if startup_keys:
            logger.info(f"Performing startup warming for {len(startup_keys)} keys")
            
            # Update stats
            with self.stats_lock:
                self.stats["startup_warmings"] = len(startup_keys)
            
            # Warm keys
            for key in startup_keys:
                self.warm_key(key)
    
    def _perform_background_warming(self):
        """Perform background warming."""
        # Get keys to warm
        keys_to_warm = self.strategy.get_keys_to_warm(self.config.max_keys_per_warming)
        
        if not keys_to_warm:
            return
        
        logger.info(f"Performing background warming for {len(keys_to_warm)} keys")
        
        # Update stats
        with self.stats_lock:
            self.stats["background_warmings"] += len(keys_to_warm)
        
        # Warm keys
        for key in keys_to_warm:
            self.warm_key(key)
    
    def _match_pattern(self, key: str, pattern: str) -> bool:
        """
        Check if key matches pattern.
        
        Args:
            key: Key to check
            pattern: Pattern to match
            
        Returns:
            True if key matches pattern, False otherwise
        """
        import re
        
        # Convert pattern to regex
        regex_pattern = pattern.replace("*", ".*").replace("?", ".")
        
        # Check if key matches pattern
        return bool(re.match(f"^{regex_pattern}$", key))
    
    def warm_key(self, key: K, priority: float = 1.0) -> bool:
        """
        Warm a specific key.
        
        Args:
            key: Key to warm
            priority: Priority of warming (higher is more important)
            
        Returns:
            True if warming was successful, False otherwise
        """
        # Check if already warmed
        with self.warmed_lock:
            if key in self.warmed:
                # Already warmed
                with self.stats_lock:
                    self.stats["skipped_warmings"] += 1
                
                return True
        
        # Check if already warming
        with self.warming_lock:
            if key in self.warming:
                # Already warming
                with self.stats_lock:
                    self.stats["skipped_warmings"] += 1
                
                return True
            
            # Mark as warming
            self.warming.add(key)
        
        # Update stats
        with self.stats_lock:
            self.stats["total_warmings"] += 1
        
        try:
            # Start warming time
            start_time = time.time()
            
            # Check if key is already in cache
            if hasattr(self.cache, 'get'):
                value = self.cache.get(key)
                
                if value is not None:
                    # Already in cache
                    with self.warmed_lock:
                        self.warmed.add(key)
                    
                    # Update stats
                    with self.stats_lock:
                        self.stats["successful_warmings"] += 1
                        self.stats["warming_time"] += time.time() - start_time
                        self.stats["last_warming_time"] = time.time() - start_time
                    
                    # Update strategy
                    self.strategy.update_stats(key, time.time(), True)
                    
                    return True
            
            # Fetch value
            value = self.fetch_func(key)
            
            # Store in cache
            if hasattr(self.cache, 'set'):
                self.cache.set(key, value)
            else:
                # Fallback for dict-like caches
                self.cache[key] = value
            
            # Mark as warmed
            with self.warmed_lock:
                self.warmed.add(key)
            
            # Update stats
            with self.stats_lock:
                self.stats["successful_warmings"] += 1
                self.stats["warming_time"] += time.time() - start_time
                self.stats["last_warming_time"] = time.time() - start_time
            
            # Update strategy
            self.strategy.update_stats(key, time.time(), False)
            
            return True
        
        except Exception as e:
            logger.error(f"Error warming key {key}: {str(e)}")
            
            # Update stats
            with self.stats_lock:
                self.stats["failed_warmings"] += 1
            
            return False
        
        finally:
            # Remove from warming set
            with self.warming_lock:
                self.warming.discard(key)
    
    def warm_keys(self, keys: List[K], priorities: Optional[List[float]] = None) -> int:
        """
        Warm multiple keys.
        
        Args:
            keys: List of keys to warm
            priorities: List of priorities (defaults to 1.0 for all)
            
        Returns:
            Number of successfully warmed keys
        """
        if not keys:
            return 0
        
        # Use default priorities if not provided
        if priorities is None:
            priorities = [1.0] * len(keys)
        
        # Ensure priorities and keys have same length
        if len(priorities) != len(keys):
            priorities = priorities[:len(keys)] + [1.0] * (len(keys) - len(priorities))
        
        # Warm keys
        success_count = 0
        
        for key, priority in zip(keys, priorities):
            if self.warm_key(key, priority):
                success_count += 1
        
        return success_count
    
    def warm_pattern(self, pattern: str) -> int:
        """
        Warm keys matching a pattern.
        
        Args:
            pattern: Pattern to match
            
        Returns:
            Number of successfully warmed keys
        """
        matching_keys = []
        
        # Get keys matching pattern
        if hasattr(self.cache, 'keys'):
            matching_keys = [key for key in self.cache.keys() if self._match_pattern(key, pattern)]
        
        # Warm matching keys
        return self.warm_keys(matching_keys)
    
    def notify_access(self, key: K, hit: bool = True):
        """
        Notify of a cache access.
        
        This updates the warming strategy.
        
        Args:
            key: Accessed key
            hit: Whether the access was a cache hit
        """
        # Update strategy
        self.strategy.update_stats(key, time.time(), hit)
    
    def clear_warmed(self):
        """Clear the set of warmed keys."""
        with self.warmed_lock:
            self.warmed.clear()
    
    def is_warmed(self, key: K) -> bool:
        """
        Check if a key has been warmed.
        
        Args:
            key: Key to check
            
        Returns:
            True if the key has been warmed
        """
        with self.warmed_lock:
            return key in self.warmed
    
    def is_warming(self, key: K) -> bool:
        """
        Check if a key is currently being warmed.
        
        Args:
            key: Key to check
            
        Returns:
            True if the key is being warmed
        """
        with self.warming_lock:
            return key in self.warming
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the warmer.
        
        Returns:
            Dictionary of statistics
        """
        with self.stats_lock, self.warming_lock, self.warmed_lock:
            # Calculate success rate
            total = self.stats["successful_warmings"] + self.stats["failed_warmings"]
            success_rate = self.stats["successful_warmings"] / total if total > 0 else 0
            
            # Calculate average warming time
            avg_warming_time = self.stats["warming_time"] / total if total > 0 else 0
            
            return {
                "total_warmings": self.stats["total_warmings"],
                "successful_warmings": self.stats["successful_warmings"],
                "failed_warmings": self.stats["failed_warmings"],
                "skipped_warmings": self.stats["skipped_warmings"],
                "success_rate": success_rate,
                "avg_warming_time": avg_warming_time,
                "last_warming_time": self.stats["last_warming_time"],
                "startup_warmings": self.stats["startup_warmings"],
                "background_warmings": self.stats["background_warmings"],
                "warming_count": len(self.warming),
                "warmed_count": len(self.warmed),
                "strategy_type": self.config.strategy_type,
                "background_warming": self.config.background_warming,
                "warming_interval": self.config.warming_interval,
                "max_keys_per_warming": self.config.max_keys_per_warming
            }
    
    def close(self):
        """Close warmer and release resources."""
        # Stop warming thread
        if self.warming_thread is not None:
            self.stop_warming.set()
            self.warming_thread.join(timeout=1.0)
        
        # Stop stats thread
        if self.stats_thread is not None:
            self.stop_stats.set()
            self.stats_thread.join(timeout=1.0)
