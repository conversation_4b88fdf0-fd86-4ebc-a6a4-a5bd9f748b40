"""
Cache prefetcher for Deep Research Core.

This module provides functionality for prefetching cache entries
based on predicted access patterns.
"""

import time
import threading
import queue
from typing import Dict, List, Any, Optional, Set, Tuple, Union, Callable

from deep_research_core.utils.structured_logging import get_logger
from deep_research_core.optimization.caching.access_pattern_analyzer import AccessPatt<PERSON><PERSON><PERSON>yzer

# Get logger
logger = get_logger(__name__)


class PrefetchRequest:
    """Request for prefetching a cache entry."""
    
    def __init__(
        self,
        key: str,
        priority: float = 0.0,
        context: Optional[Dict[str, Any]] = None,
        timestamp: float = None
    ):
        """
        Initialize prefetch request.
        
        Args:
            key: Cache key to prefetch
            priority: Priority of the request (higher is more important)
            context: Additional context for the request
            timestamp: Request timestamp (defaults to current time)
        """
        self.key = key
        self.priority = priority
        self.context = context or {}
        self.timestamp = timestamp or time.time()
    
    def __lt__(self, other):
        """
        Compare requests by priority.
        
        This is used for priority queue ordering.
        
        Args:
            other: Other request to compare with
            
        Returns:
            True if this request has lower priority than other
        """
        if not isinstance(other, PrefetchRequest):
            return NotImplemented
        
        # Higher priority comes first
        return self.priority < other.priority


class CachePrefetcherConfig:
    """Configuration for cache prefetcher."""
    
    def __init__(
        self,
        max_queue_size: int = 1000,
        prefetch_interval: float = 0.1,
        max_prefetch_per_interval: int = 5,
        max_concurrent_prefetches: int = 3,
        enable_prefetching: bool = True,
        min_priority: float = 0.1,
        priority_decay_rate: float = 0.9,
        max_retry_count: int = 3,
        retry_delay: float = 1.0
    ):
        """
        Initialize configuration.
        
        Args:
            max_queue_size: Maximum size of prefetch queue
            prefetch_interval: Interval in seconds between prefetch batches
            max_prefetch_per_interval: Maximum number of prefetches per interval
            max_concurrent_prefetches: Maximum number of concurrent prefetches
            enable_prefetching: Whether to enable prefetching
            min_priority: Minimum priority for prefetch requests
            priority_decay_rate: Rate at which priority decays for retries
            max_retry_count: Maximum number of retries for failed prefetches
            retry_delay: Delay in seconds before retrying a failed prefetch
        """
        self.max_queue_size = max_queue_size
        self.prefetch_interval = prefetch_interval
        self.max_prefetch_per_interval = max_prefetch_per_interval
        self.max_concurrent_prefetches = max_concurrent_prefetches
        self.enable_prefetching = enable_prefetching
        self.min_priority = min_priority
        self.priority_decay_rate = priority_decay_rate
        self.max_retry_count = max_retry_count
        self.retry_delay = retry_delay


class CachePrefetcher:
    """
    Prefetcher for cache entries.
    
    This class prefetches cache entries based on predicted access patterns.
    """
    
    def __init__(
        self,
        prefetch_func: Callable[[str, Optional[Dict[str, Any]]], Any],
        analyzer: Optional[AccessPatternAnalyzer] = None,
        config: Optional[CachePrefetcherConfig] = None
    ):
        """
        Initialize prefetcher.
        
        Args:
            prefetch_func: Function to call for prefetching a key
            analyzer: Access pattern analyzer
            config: Configuration
        """
        self.prefetch_func = prefetch_func
        self.analyzer = analyzer
        self.config = config or CachePrefetcherConfig()
        
        # Initialize prefetch queue
        self.prefetch_queue = queue.PriorityQueue(maxsize=self.config.max_queue_size)
        
        # Initialize sets for tracking
        self.prefetching = set()  # Keys currently being prefetched
        self.prefetched = set()   # Keys that have been prefetched
        
        # Initialize locks
        self.prefetching_lock = threading.RLock()
        self.prefetched_lock = threading.RLock()
        
        # Initialize stats
        self.stats = {
            "total_requests": 0,
            "successful_prefetches": 0,
            "failed_prefetches": 0,
            "skipped_prefetches": 0,
            "retried_prefetches": 0
        }
        self.stats_lock = threading.RLock()
        
        # Initialize prefetch thread
        self.stop_prefetching = threading.Event()
        self.prefetch_thread = None
        
        # Start prefetch thread if enabled
        if self.config.enable_prefetching:
            self._start_prefetch_thread()
    
    def _start_prefetch_thread(self):
        """Start prefetch thread."""
        if self.prefetch_thread is not None:
            return
        
        self.prefetch_thread = threading.Thread(
            target=self._prefetch_worker,
            daemon=True
        )
        self.prefetch_thread.start()
    
    def _prefetch_worker(self):
        """Worker function for prefetch thread."""
        while not self.stop_prefetching.is_set():
            try:
                # Process prefetch requests
                self._process_prefetch_requests()
                
                # Sleep for prefetch interval
                self.stop_prefetching.wait(self.config.prefetch_interval)
            
            except Exception as e:
                logger.error(f"Error in prefetch worker: {str(e)}")
    
    def _process_prefetch_requests(self):
        """Process prefetch requests from queue."""
        # Check if we can prefetch more
        with self.prefetching_lock:
            available_slots = self.config.max_concurrent_prefetches - len(self.prefetching)
            
            if available_slots <= 0:
                return
        
        # Process up to max_prefetch_per_interval or available_slots
        count = min(self.config.max_prefetch_per_interval, available_slots)
        
        for _ in range(count):
            try:
                # Get request from queue (non-blocking)
                request = self.prefetch_queue.get_nowait()
                
                # Start prefetch in a separate thread
                threading.Thread(
                    target=self._prefetch_key,
                    args=(request,),
                    daemon=True
                ).start()
            
            except queue.Empty:
                # Queue is empty
                break
    
    def _prefetch_key(self, request: PrefetchRequest, retry_count: int = 0):
        """
        Prefetch a key.
        
        Args:
            request: Prefetch request
            retry_count: Number of retries so far
        """
        key = request.key
        
        # Check if already prefetched or prefetching
        with self.prefetched_lock:
            if key in self.prefetched:
                # Already prefetched
                with self.stats_lock:
                    self.stats["skipped_prefetches"] += 1
                
                # Mark as done in queue
                self.prefetch_queue.task_done()
                return
        
        with self.prefetching_lock:
            if key in self.prefetching:
                # Already prefetching
                with self.stats_lock:
                    self.stats["skipped_prefetches"] += 1
                
                # Mark as done in queue
                self.prefetch_queue.task_done()
                return
            
            # Mark as prefetching
            self.prefetching.add(key)
        
        try:
            # Call prefetch function
            result = self.prefetch_func(key, request.context)
            
            # Mark as prefetched
            with self.prefetched_lock:
                self.prefetched.add(key)
            
            # Update stats
            with self.stats_lock:
                self.stats["successful_prefetches"] += 1
            
            logger.debug(f"Prefetched key: {key}")
        
        except Exception as e:
            logger.error(f"Error prefetching key {key}: {str(e)}")
            
            # Update stats
            with self.stats_lock:
                self.stats["failed_prefetches"] += 1
            
            # Retry if not exceeded max retries
            if retry_count < self.config.max_retry_count:
                # Decay priority
                new_priority = request.priority * self.config.priority_decay_rate
                
                # Only retry if priority is still above minimum
                if new_priority >= self.config.min_priority:
                    # Create new request with decayed priority
                    new_request = PrefetchRequest(
                        key=key,
                        priority=new_priority,
                        context=request.context
                    )
                    
                    # Schedule retry
                    threading.Timer(
                        self.config.retry_delay,
                        self.queue_prefetch_request,
                        args=(new_request, retry_count + 1)
                    ).start()
                    
                    # Update stats
                    with self.stats_lock:
                        self.stats["retried_prefetches"] += 1
        
        finally:
            # Remove from prefetching set
            with self.prefetching_lock:
                self.prefetching.discard(key)
            
            # Mark as done in queue
            self.prefetch_queue.task_done()
    
    def queue_prefetch_request(self, request: PrefetchRequest, retry_count: int = 0):
        """
        Queue a prefetch request.
        
        Args:
            request: Prefetch request
            retry_count: Number of retries so far
        """
        try:
            # Update stats
            with self.stats_lock:
                self.stats["total_requests"] += 1
            
            # Add to queue (non-blocking)
            self.prefetch_queue.put_nowait(request)
            
            # If this is a retry, call _prefetch_key directly
            if retry_count > 0:
                threading.Thread(
                    target=self._prefetch_key,
                    args=(request, retry_count),
                    daemon=True
                ).start()
        
        except queue.Full:
            logger.warning("Prefetch queue is full, skipping request")
            
            # Update stats
            with self.stats_lock:
                self.stats["skipped_prefetches"] += 1
    
    def prefetch_keys(
        self,
        keys: List[str],
        priorities: Optional[List[float]] = None,
        context: Optional[Dict[str, Any]] = None
    ):
        """
        Prefetch multiple keys.
        
        Args:
            keys: List of keys to prefetch
            priorities: List of priorities (defaults to 1.0 for all)
            context: Additional context for the requests
        """
        if not keys:
            return
        
        # Use default priorities if not provided
        if priorities is None:
            priorities = [1.0] * len(keys)
        
        # Ensure priorities and keys have same length
        if len(priorities) != len(keys):
            priorities = priorities[:len(keys)] + [1.0] * (len(keys) - len(priorities))
        
        # Create and queue requests
        for key, priority in zip(keys, priorities):
            request = PrefetchRequest(
                key=key,
                priority=priority,
                context=context
            )
            
            self.queue_prefetch_request(request)
    
    def prefetch_predicted(
        self,
        recent_keys: List[str],
        context: Optional[Dict[str, Any]] = None,
        max_predictions: int = 5
    ):
        """
        Prefetch keys predicted by analyzer.
        
        Args:
            recent_keys: Recently accessed keys
            context: Current context
            max_predictions: Maximum number of predictions to prefetch
        """
        if not self.analyzer:
            return
        
        # Get predictions
        predicted_keys = self.analyzer.predict_next_accesses(
            recent_keys=recent_keys,
            context=context,
            max_predictions=max_predictions
        )
        
        # Calculate priorities based on order
        # (first prediction has highest priority)
        priorities = [
            1.0 - (i / len(predicted_keys)) if len(predicted_keys) > 1 else 1.0
            for i in range(len(predicted_keys))
        ]
        
        # Prefetch predicted keys
        self.prefetch_keys(
            keys=predicted_keys,
            priorities=priorities,
            context=context
        )
    
    def notify_access(
        self,
        key: str,
        context: Optional[Dict[str, Any]] = None,
        is_hit: bool = False
    ):
        """
        Notify of a cache access.
        
        This updates the analyzer and triggers prefetching.
        
        Args:
            key: Accessed key
            context: Access context
            is_hit: Whether the access was a cache hit
        """
        # Record access in analyzer
        if self.analyzer:
            self.analyzer.record_access(
                key=key,
                context=context,
                is_hit=is_hit
            )
    
    def clear_prefetched(self):
        """Clear the set of prefetched keys."""
        with self.prefetched_lock:
            self.prefetched.clear()
    
    def is_prefetched(self, key: str) -> bool:
        """
        Check if a key has been prefetched.
        
        Args:
            key: Key to check
            
        Returns:
            True if the key has been prefetched
        """
        with self.prefetched_lock:
            return key in self.prefetched
    
    def is_prefetching(self, key: str) -> bool:
        """
        Check if a key is currently being prefetched.
        
        Args:
            key: Key to check
            
        Returns:
            True if the key is being prefetched
        """
        with self.prefetching_lock:
            return key in self.prefetching
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the prefetcher.
        
        Returns:
            Dictionary of statistics
        """
        with self.stats_lock, self.prefetching_lock, self.prefetched_lock:
            return {
                "queue_size": self.prefetch_queue.qsize(),
                "prefetching_count": len(self.prefetching),
                "prefetched_count": len(self.prefetched),
                "total_requests": self.stats["total_requests"],
                "successful_prefetches": self.stats["successful_prefetches"],
                "failed_prefetches": self.stats["failed_prefetches"],
                "skipped_prefetches": self.stats["skipped_prefetches"],
                "retried_prefetches": self.stats["retried_prefetches"],
                "config": {
                    "max_queue_size": self.config.max_queue_size,
                    "prefetch_interval": self.config.prefetch_interval,
                    "max_prefetch_per_interval": self.config.max_prefetch_per_interval,
                    "max_concurrent_prefetches": self.config.max_concurrent_prefetches,
                    "enable_prefetching": self.config.enable_prefetching,
                    "min_priority": self.config.min_priority,
                    "priority_decay_rate": self.config.priority_decay_rate,
                    "max_retry_count": self.config.max_retry_count,
                    "retry_delay": self.config.retry_delay
                }
            }
    
    def close(self):
        """Close prefetcher and release resources."""
        # Stop prefetch thread
        if self.prefetch_thread is not None:
            self.stop_prefetching.set()
            self.prefetch_thread.join(timeout=1.0)
        
        # Clear queue
        while not self.prefetch_queue.empty():
            try:
                self.prefetch_queue.get_nowait()
                self.prefetch_queue.task_done()
            except queue.Empty:
                break
