"""
Redis cache provider for Deep Research Core.

This module provides a Redis-based cache provider for distributed caching.
"""

import json
import pickle
import hashlib
import time
from typing import Any, Dict, List, Optional, Set, Tuple, Union

from deep_research_core.utils.structured_logging import get_logger

# Get logger
logger = get_logger(__name__)

# Try to import Redis
try:
    import redis
    from redis.exceptions import RedisError
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logger.warning("Redis package not available. Install with 'pip install redis' to use Redis caching.")


class RedisConfig:
    """Configuration for Redis connection."""
    
    def __init__(
        self,
        host: str = "localhost",
        port: int = 6379,
        db: int = 0,
        password: Optional[str] = None,
        socket_timeout: float = 5.0,
        socket_connect_timeout: float = 5.0,
        socket_keepalive: bool = True,
        retry_on_timeout: bool = True,
        health_check_interval: int = 30,
        ssl: bool = False,
        ssl_cert_reqs: Optional[str] = None,
        max_connections: int = 10,
        prefix: str = "deep_research:",
        serializer: str = "json"
    ):
        """
        Initialize Redis configuration.
        
        Args:
            host: Redis host
            port: Redis port
            db: Redis database number
            password: Redis password
            socket_timeout: Socket timeout in seconds
            socket_connect_timeout: Socket connect timeout in seconds
            socket_keepalive: Whether to use socket keepalive
            retry_on_timeout: Whether to retry on timeout
            health_check_interval: Health check interval in seconds
            ssl: Whether to use SSL
            ssl_cert_reqs: SSL cert requirements
            max_connections: Maximum number of connections in the pool
            prefix: Key prefix for all cache keys
            serializer: Serializer to use ('json' or 'pickle')
        """
        self.host = host
        self.port = port
        self.db = db
        self.password = password
        self.socket_timeout = socket_timeout
        self.socket_connect_timeout = socket_connect_timeout
        self.socket_keepalive = socket_keepalive
        self.retry_on_timeout = retry_on_timeout
        self.health_check_interval = health_check_interval
        self.ssl = ssl
        self.ssl_cert_reqs = ssl_cert_reqs
        self.max_connections = max_connections
        self.prefix = prefix
        self.serializer = serializer
        
        # Validate serializer
        if self.serializer not in ["json", "pickle"]:
            logger.warning(f"Invalid serializer: {self.serializer}. Using 'json' instead.")
            self.serializer = "json"


class RedisProvider:
    """
    Redis cache provider for distributed caching.
    
    This provider uses Redis for distributed caching, with support for
    serialization, TTL, and atomic operations.
    """
    
    def __init__(self, config: Optional[RedisConfig] = None):
        """
        Initialize Redis provider.
        
        Args:
            config: Redis configuration
        """
        if not REDIS_AVAILABLE:
            raise ImportError("Redis package not available. Install with 'pip install redis'.")
        
        self.config = config or RedisConfig()
        self.client = self._create_client()
        
        # Test connection
        try:
            self.client.ping()
            logger.info(f"Connected to Redis at {self.config.host}:{self.config.port}")
        except RedisError as e:
            logger.error(f"Failed to connect to Redis: {str(e)}")
            raise
    
    def _create_client(self) -> redis.Redis:
        """
        Create Redis client.
        
        Returns:
            Redis client
        """
        # Create connection pool
        pool = redis.ConnectionPool(
            host=self.config.host,
            port=self.config.port,
            db=self.config.db,
            password=self.config.password,
            socket_timeout=self.config.socket_timeout,
            socket_connect_timeout=self.config.socket_connect_timeout,
            socket_keepalive=self.config.socket_keepalive,
            retry_on_timeout=self.config.retry_on_timeout,
            health_check_interval=self.config.health_check_interval,
            ssl=self.config.ssl,
            ssl_cert_reqs=self.config.ssl_cert_reqs,
            max_connections=self.config.max_connections
        )
        
        # Create client
        return redis.Redis(connection_pool=pool)
    
    def _prefix_key(self, key: str) -> str:
        """
        Add prefix to key.
        
        Args:
            key: Cache key
            
        Returns:
            Prefixed key
        """
        return f"{self.config.prefix}{key}"
    
    def _serialize(self, value: Any) -> bytes:
        """
        Serialize value to bytes.
        
        Args:
            value: Value to serialize
            
        Returns:
            Serialized value
        """
        try:
            if self.config.serializer == "json":
                return json.dumps(value).encode("utf-8")
            else:  # pickle
                return pickle.dumps(value)
        except (TypeError, ValueError, pickle.PickleError) as e:
            logger.error(f"Failed to serialize value: {str(e)}")
            # Fallback to string representation
            return str(value).encode("utf-8")
    
    def _deserialize(self, value: bytes) -> Any:
        """
        Deserialize value from bytes.
        
        Args:
            value: Value to deserialize
            
        Returns:
            Deserialized value
        """
        if value is None:
            return None
        
        try:
            if self.config.serializer == "json":
                return json.loads(value.decode("utf-8"))
            else:  # pickle
                return pickle.loads(value)
        except (json.JSONDecodeError, UnicodeDecodeError, pickle.PickleError) as e:
            logger.error(f"Failed to deserialize value: {str(e)}")
            # Return raw value as fallback
            return value
    
    def get(self, key: str) -> Any:
        """
        Get value from cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found
        """
        try:
            prefixed_key = self._prefix_key(key)
            value = self.client.get(prefixed_key)
            return self._deserialize(value)
        except RedisError as e:
            logger.error(f"Redis error in get({key}): {str(e)}")
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        Set value in cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time-to-live in seconds (None for no expiration)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            prefixed_key = self._prefix_key(key)
            serialized = self._serialize(value)
            
            if ttl is not None:
                return bool(self.client.setex(prefixed_key, ttl, serialized))
            else:
                return bool(self.client.set(prefixed_key, serialized))
        except RedisError as e:
            logger.error(f"Redis error in set({key}): {str(e)}")
            return False
    
    def delete(self, key: str) -> bool:
        """
        Delete value from cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if successful, False otherwise
        """
        try:
            prefixed_key = self._prefix_key(key)
            return bool(self.client.delete(prefixed_key))
        except RedisError as e:
            logger.error(f"Redis error in delete({key}): {str(e)}")
            return False
    
    def exists(self, key: str) -> bool:
        """
        Check if key exists in cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if key exists, False otherwise
        """
        try:
            prefixed_key = self._prefix_key(key)
            return bool(self.client.exists(prefixed_key))
        except RedisError as e:
            logger.error(f"Redis error in exists({key}): {str(e)}")
            return False
    
    def ttl(self, key: str) -> int:
        """
        Get TTL of key in seconds.
        
        Args:
            key: Cache key
            
        Returns:
            TTL in seconds, -1 if no expiration, -2 if key doesn't exist
        """
        try:
            prefixed_key = self._prefix_key(key)
            return self.client.ttl(prefixed_key)
        except RedisError as e:
            logger.error(f"Redis error in ttl({key}): {str(e)}")
            return -2
    
    def expire(self, key: str, ttl: int) -> bool:
        """
        Set TTL for key.
        
        Args:
            key: Cache key
            ttl: Time-to-live in seconds
            
        Returns:
            True if successful, False otherwise
        """
        try:
            prefixed_key = self._prefix_key(key)
            return bool(self.client.expire(prefixed_key, ttl))
        except RedisError as e:
            logger.error(f"Redis error in expire({key}, {ttl}): {str(e)}")
            return False
    
    def incr(self, key: str, amount: int = 1) -> int:
        """
        Increment value.
        
        Args:
            key: Cache key
            amount: Amount to increment
            
        Returns:
            New value or -1 if failed
        """
        try:
            prefixed_key = self._prefix_key(key)
            return self.client.incrby(prefixed_key, amount)
        except RedisError as e:
            logger.error(f"Redis error in incr({key}, {amount}): {str(e)}")
            return -1
    
    def decr(self, key: str, amount: int = 1) -> int:
        """
        Decrement value.
        
        Args:
            key: Cache key
            amount: Amount to decrement
            
        Returns:
            New value or -1 if failed
        """
        try:
            prefixed_key = self._prefix_key(key)
            return self.client.decrby(prefixed_key, amount)
        except RedisError as e:
            logger.error(f"Redis error in decr({key}, {amount}): {str(e)}")
            return -1
    
    def keys(self, pattern: str = "*") -> List[str]:
        """
        Get keys matching pattern.
        
        Args:
            pattern: Pattern to match
            
        Returns:
            List of matching keys (without prefix)
        """
        try:
            prefixed_pattern = self._prefix_key(pattern)
            keys = self.client.keys(prefixed_pattern)
            
            # Remove prefix from keys
            prefix_len = len(self.config.prefix)
            return [key.decode("utf-8")[prefix_len:] for key in keys]
        except RedisError as e:
            logger.error(f"Redis error in keys({pattern}): {str(e)}")
            return []
    
    def flush(self) -> bool:
        """
        Delete all keys with prefix.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get all keys with prefix
            prefixed_pattern = self._prefix_key("*")
            keys = self.client.keys(prefixed_pattern)
            
            if not keys:
                return True
            
            # Delete keys in batches
            pipeline = self.client.pipeline()
            for key in keys:
                pipeline.delete(key)
            
            pipeline.execute()
            return True
        except RedisError as e:
            logger.error(f"Redis error in flush(): {str(e)}")
            return False
    
    def publish(self, channel: str, message: Any) -> int:
        """
        Publish message to channel.
        
        Args:
            channel: Channel name
            message: Message to publish
            
        Returns:
            Number of clients that received the message
        """
        try:
            prefixed_channel = self._prefix_key(channel)
            serialized = self._serialize(message)
            return self.client.publish(prefixed_channel, serialized)
        except RedisError as e:
            logger.error(f"Redis error in publish({channel}): {str(e)}")
            return 0
    
    def subscribe(self, channel: str) -> Any:
        """
        Subscribe to channel and yield messages.
        
        Args:
            channel: Channel name
            
        Yields:
            Messages from channel
        """
        try:
            prefixed_channel = self._prefix_key(channel)
            pubsub = self.client.pubsub()
            pubsub.subscribe(prefixed_channel)
            
            for message in pubsub.listen():
                if message["type"] == "message":
                    yield self._deserialize(message["data"])
        except RedisError as e:
            logger.error(f"Redis error in subscribe({channel}): {str(e)}")
            yield None
    
    def hash_set(self, key: str, field: str, value: Any) -> bool:
        """
        Set field in hash.
        
        Args:
            key: Hash key
            field: Field name
            value: Field value
            
        Returns:
            True if successful, False otherwise
        """
        try:
            prefixed_key = self._prefix_key(key)
            serialized = self._serialize(value)
            return bool(self.client.hset(prefixed_key, field, serialized))
        except RedisError as e:
            logger.error(f"Redis error in hash_set({key}, {field}): {str(e)}")
            return False
    
    def hash_get(self, key: str, field: str) -> Any:
        """
        Get field from hash.
        
        Args:
            key: Hash key
            field: Field name
            
        Returns:
            Field value or None if not found
        """
        try:
            prefixed_key = self._prefix_key(key)
            value = self.client.hget(prefixed_key, field)
            return self._deserialize(value)
        except RedisError as e:
            logger.error(f"Redis error in hash_get({key}, {field}): {str(e)}")
            return None
    
    def hash_delete(self, key: str, field: str) -> bool:
        """
        Delete field from hash.
        
        Args:
            key: Hash key
            field: Field name
            
        Returns:
            True if successful, False otherwise
        """
        try:
            prefixed_key = self._prefix_key(key)
            return bool(self.client.hdel(prefixed_key, field))
        except RedisError as e:
            logger.error(f"Redis error in hash_delete({key}, {field}): {str(e)}")
            return False
    
    def hash_exists(self, key: str, field: str) -> bool:
        """
        Check if field exists in hash.
        
        Args:
            key: Hash key
            field: Field name
            
        Returns:
            True if field exists, False otherwise
        """
        try:
            prefixed_key = self._prefix_key(key)
            return bool(self.client.hexists(prefixed_key, field))
        except RedisError as e:
            logger.error(f"Redis error in hash_exists({key}, {field}): {str(e)}")
            return False
    
    def hash_keys(self, key: str) -> List[str]:
        """
        Get all fields in hash.
        
        Args:
            key: Hash key
            
        Returns:
            List of field names
        """
        try:
            prefixed_key = self._prefix_key(key)
            return [field.decode("utf-8") for field in self.client.hkeys(prefixed_key)]
        except RedisError as e:
            logger.error(f"Redis error in hash_keys({key}): {str(e)}")
            return []
    
    def hash_values(self, key: str) -> List[Any]:
        """
        Get all values in hash.
        
        Args:
            key: Hash key
            
        Returns:
            List of field values
        """
        try:
            prefixed_key = self._prefix_key(key)
            values = self.client.hvals(prefixed_key)
            return [self._deserialize(value) for value in values]
        except RedisError as e:
            logger.error(f"Redis error in hash_values({key}): {str(e)}")
            return []
    
    def hash_get_all(self, key: str) -> Dict[str, Any]:
        """
        Get all fields and values in hash.
        
        Args:
            key: Hash key
            
        Returns:
            Dictionary of field names and values
        """
        try:
            prefixed_key = self._prefix_key(key)
            hash_dict = self.client.hgetall(prefixed_key)
            
            result = {}
            for field, value in hash_dict.items():
                field_str = field.decode("utf-8")
                result[field_str] = self._deserialize(value)
            
            return result
        except RedisError as e:
            logger.error(f"Redis error in hash_get_all({key}): {str(e)}")
            return {}
    
    def set_add(self, key: str, *values: Any) -> int:
        """
        Add values to set.
        
        Args:
            key: Set key
            *values: Values to add
            
        Returns:
            Number of values added
        """
        try:
            prefixed_key = self._prefix_key(key)
            serialized_values = [self._serialize(value) for value in values]
            
            return self.client.sadd(prefixed_key, *serialized_values)
        except RedisError as e:
            logger.error(f"Redis error in set_add({key}): {str(e)}")
            return 0
    
    def set_remove(self, key: str, *values: Any) -> int:
        """
        Remove values from set.
        
        Args:
            key: Set key
            *values: Values to remove
            
        Returns:
            Number of values removed
        """
        try:
            prefixed_key = self._prefix_key(key)
            serialized_values = [self._serialize(value) for value in values]
            
            return self.client.srem(prefixed_key, *serialized_values)
        except RedisError as e:
            logger.error(f"Redis error in set_remove({key}): {str(e)}")
            return 0
    
    def set_members(self, key: str) -> Set[Any]:
        """
        Get all members of set.
        
        Args:
            key: Set key
            
        Returns:
            Set of members
        """
        try:
            prefixed_key = self._prefix_key(key)
            members = self.client.smembers(prefixed_key)
            
            return {self._deserialize(member) for member in members}
        except RedisError as e:
            logger.error(f"Redis error in set_members({key}): {str(e)}")
            return set()
    
    def set_is_member(self, key: str, value: Any) -> bool:
        """
        Check if value is member of set.
        
        Args:
            key: Set key
            value: Value to check
            
        Returns:
            True if value is member, False otherwise
        """
        try:
            prefixed_key = self._prefix_key(key)
            serialized = self._serialize(value)
            
            return bool(self.client.sismember(prefixed_key, serialized))
        except RedisError as e:
            logger.error(f"Redis error in set_is_member({key}): {str(e)}")
            return False
    
    def list_push(self, key: str, *values: Any, left: bool = False) -> int:
        """
        Push values to list.
        
        Args:
            key: List key
            *values: Values to push
            left: Whether to push to left (head) or right (tail)
            
        Returns:
            New length of list
        """
        try:
            prefixed_key = self._prefix_key(key)
            serialized_values = [self._serialize(value) for value in values]
            
            if left:
                return self.client.lpush(prefixed_key, *serialized_values)
            else:
                return self.client.rpush(prefixed_key, *serialized_values)
        except RedisError as e:
            logger.error(f"Redis error in list_push({key}): {str(e)}")
            return 0
    
    def list_pop(self, key: str, left: bool = False) -> Any:
        """
        Pop value from list.
        
        Args:
            key: List key
            left: Whether to pop from left (head) or right (tail)
            
        Returns:
            Popped value or None if list is empty
        """
        try:
            prefixed_key = self._prefix_key(key)
            
            if left:
                value = self.client.lpop(prefixed_key)
            else:
                value = self.client.rpop(prefixed_key)
            
            return self._deserialize(value)
        except RedisError as e:
            logger.error(f"Redis error in list_pop({key}): {str(e)}")
            return None
    
    def list_range(self, key: str, start: int = 0, end: int = -1) -> List[Any]:
        """
        Get range of values from list.
        
        Args:
            key: List key
            start: Start index
            end: End index
            
        Returns:
            List of values
        """
        try:
            prefixed_key = self._prefix_key(key)
            values = self.client.lrange(prefixed_key, start, end)
            
            return [self._deserialize(value) for value in values]
        except RedisError as e:
            logger.error(f"Redis error in list_range({key}, {start}, {end}): {str(e)}")
            return []
    
    def list_length(self, key: str) -> int:
        """
        Get length of list.
        
        Args:
            key: List key
            
        Returns:
            Length of list
        """
        try:
            prefixed_key = self._prefix_key(key)
            return self.client.llen(prefixed_key)
        except RedisError as e:
            logger.error(f"Redis error in list_length({key}): {str(e)}")
            return 0
    
    def acquire_lock(self, lock_name: str, timeout: int = 10, wait_timeout: int = 10) -> bool:
        """
        Acquire distributed lock.
        
        Args:
            lock_name: Lock name
            timeout: Lock timeout in seconds
            wait_timeout: Maximum time to wait for lock in seconds
            
        Returns:
            True if lock acquired, False otherwise
        """
        try:
            import redis_lock
            
            lock = redis_lock.Lock(self.client, self._prefix_key(lock_name), expire=timeout)
            return lock.acquire(timeout=wait_timeout)
        except (RedisError, ImportError) as e:
            logger.error(f"Error acquiring lock {lock_name}: {str(e)}")
            return False
    
    def release_lock(self, lock_name: str) -> bool:
        """
        Release distributed lock.
        
        Args:
            lock_name: Lock name
            
        Returns:
            True if lock released, False otherwise
        """
        try:
            import redis_lock
            
            lock = redis_lock.Lock(self.client, self._prefix_key(lock_name))
            lock.release()
            return True
        except (RedisError, ImportError) as e:
            logger.error(f"Error releasing lock {lock_name}: {str(e)}")
            return False
    
    def close(self):
        """Close Redis connection."""
        try:
            self.client.close()
            logger.info("Redis connection closed")
        except RedisError as e:
            logger.error(f"Error closing Redis connection: {str(e)}")


# Create a global instance for convenience
try:
    default_redis_provider = RedisProvider() if REDIS_AVAILABLE else None
except (ImportError, RedisError) as e:
    logger.warning(f"Failed to create default Redis provider: {str(e)}")
    default_redis_provider = None
