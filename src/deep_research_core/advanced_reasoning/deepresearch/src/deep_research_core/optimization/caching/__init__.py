"""
Caching strategies for Deep Research Core.

This module provides various caching strategies to improve performance,
including semantic caching, distributed caching, and predictive caching.
"""

from deep_research_core.optimization.caching.semantic_cache import (
    SemanticCache,
    SemanticCacheConfig,
    SemanticCacheEntry
)

from deep_research_core.optimization.caching.eviction_policies import (
    EvictionPolicy,
    LRUPolicy,
    LFUPolicy,
    TTLPolicy,
    CompositePolicy,
    EvictionPolicyFactory
)

from deep_research_core.optimization.caching.access_pattern_analyzer import (
    AccessRecord,
    AccessPatternAnalyzer,
    AccessPatternAnalyzerConfig
)

from deep_research_core.optimization.caching.cache_prefetcher import (
    PrefetchRequest,
    CachePrefetcher,
    CachePrefetcherConfig
)

from deep_research_core.optimization.caching.predictive_cache import (
    PredictiveCache,
    PredictiveCacheConfig
)

from deep_research_core.optimization.caching.warming_strategies import (
    WarmingStrategy,
    FrequencyBasedStrategy,
    RecencyBasedStrategy,
    PatternBasedStrategy,
    HybridStrategy,
    PredictiveStrategy,
    WarmingStrategyFactory
)

from deep_research_core.optimization.caching.cache_warmer import (
    <PERSON><PERSON><PERSON><PERSON>er,
    CacheWarmerConfig
)

# Import distributed caching if Redis is available
try:
    from deep_research_core.optimization.caching.redis_provider import (
        RedisProvider,
        RedisConfig,
        default_redis_provider,
        REDIS_AVAILABLE
    )

    from deep_research_core.optimization.caching.distributed_cache import (
        DistributedCache,
        DistributedCacheConfig,
        distributed_cache,
        default_distributed_cache
    )

    __all__ = [
        # Semantic Cache
        'SemanticCache',
        'SemanticCacheConfig',
        'SemanticCacheEntry',

        # Eviction Policies
        'EvictionPolicy',
        'LRUPolicy',
        'LFUPolicy',
        'TTLPolicy',
        'CompositePolicy',
        'EvictionPolicyFactory',

        # Access Pattern Analyzer
        'AccessRecord',
        'AccessPatternAnalyzer',
        'AccessPatternAnalyzerConfig',

        # Cache Prefetcher
        'PrefetchRequest',
        'CachePrefetcher',
        'CachePrefetcherConfig',

        # Predictive Cache
        'PredictiveCache',
        'PredictiveCacheConfig',

        # Warming Strategies
        'WarmingStrategy',
        'FrequencyBasedStrategy',
        'RecencyBasedStrategy',
        'PatternBasedStrategy',
        'HybridStrategy',
        'PredictiveStrategy',
        'WarmingStrategyFactory',

        # Cache Warmer
        'CacheWarmer',
        'CacheWarmerConfig',

        # Redis Provider
        'RedisProvider',
        'RedisConfig',
        'default_redis_provider',

        # Distributed Cache
        'DistributedCache',
        'DistributedCacheConfig',
        'distributed_cache',
        'default_distributed_cache',
        'REDIS_AVAILABLE'
    ]
except ImportError:
    # Redis not available
    __all__ = [
        # Semantic Cache
        'SemanticCache',
        'SemanticCacheConfig',
        'SemanticCacheEntry',

        # Eviction Policies
        'EvictionPolicy',
        'LRUPolicy',
        'LFUPolicy',
        'TTLPolicy',
        'CompositePolicy',
        'EvictionPolicyFactory',

        # Access Pattern Analyzer
        'AccessRecord',
        'AccessPatternAnalyzer',
        'AccessPatternAnalyzerConfig',

        # Cache Prefetcher
        'PrefetchRequest',
        'CachePrefetcher',
        'CachePrefetcherConfig',

        # Predictive Cache
        'PredictiveCache',
        'PredictiveCacheConfig',

        # Warming Strategies
        'WarmingStrategy',
        'FrequencyBasedStrategy',
        'RecencyBasedStrategy',
        'PatternBasedStrategy',
        'HybridStrategy',
        'PredictiveStrategy',
        'WarmingStrategyFactory',

        # Cache Warmer
        'CacheWarmer',
        'CacheWarmerConfig'
    ]
