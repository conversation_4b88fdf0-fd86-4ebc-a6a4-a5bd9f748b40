"""
Cache warming strategies for Deep Research Core.

This module provides various strategies for warming cache,
including frequency-based, recency-based, and pattern-based strategies.
"""

import time
import json
import random
import threading
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Set, Tuple, Union, Callable, TypeVar, Generic

from deep_research_core.utils.structured_logging import get_logger

# Get logger
logger = get_logger(__name__)

# Type variables
K = TypeVar('K')  # Key type
V = TypeVar('V')  # Value type


class WarmingStrategy(Generic[K], ABC):
    """
    Abstract base class for cache warming strategies.
    
    This class defines the interface that all warming strategies must implement.
    """
    
    @abstractmethod
    def get_keys_to_warm(self, max_keys: int = 100) -> List[K]:
        """
        Get keys to warm.
        
        Args:
            max_keys: Maximum number of keys to return
            
        Returns:
            List of keys to warm
        """
        pass
    
    @abstractmethod
    def update_stats(self, key: K, access_time: float = None, hit: bool = False):
        """
        Update statistics for a key.
        
        Args:
            key: Cache key
            access_time: Access timestamp (defaults to current time)
            hit: Whether the access was a cache hit
        """
        pass
    
    @abstractmethod
    def clear_stats(self):
        """Clear all statistics."""
        pass


class FrequencyBasedStrategy(WarmingStrategy[K]):
    """
    Frequency-based warming strategy.
    
    This strategy warms keys based on their access frequency.
    """
    
    def __init__(self, decay_factor: float = 0.95, min_frequency: float = 0.1):
        """
        Initialize strategy.
        
        Args:
            decay_factor: Factor to decay frequencies over time (0-1)
            min_frequency: Minimum frequency to consider for warming
        """
        self.frequencies: Dict[K, float] = {}
        self.decay_factor = decay_factor
        self.min_frequency = min_frequency
        self.lock = threading.RLock()
    
    def get_keys_to_warm(self, max_keys: int = 100) -> List[K]:
        """
        Get keys to warm based on frequency.
        
        Args:
            max_keys: Maximum number of keys to return
            
        Returns:
            List of keys to warm
        """
        with self.lock:
            # Sort keys by frequency (descending)
            sorted_keys = sorted(
                self.frequencies.items(),
                key=lambda x: x[1],
                reverse=True
            )
            
            # Filter by minimum frequency
            filtered_keys = [
                key for key, freq in sorted_keys
                if freq >= self.min_frequency
            ]
            
            # Limit to max_keys
            return filtered_keys[:max_keys]
    
    def update_stats(self, key: K, access_time: float = None, hit: bool = False):
        """
        Update frequency statistics for a key.
        
        Args:
            key: Cache key
            access_time: Access timestamp (not used in this strategy)
            hit: Whether the access was a cache hit
        """
        with self.lock:
            # Decay all frequencies
            for k in self.frequencies:
                self.frequencies[k] *= self.decay_factor
            
            # Increment frequency for accessed key
            self.frequencies[key] = self.frequencies.get(key, 0) + 1.0
    
    def clear_stats(self):
        """Clear all frequency statistics."""
        with self.lock:
            self.frequencies.clear()


class RecencyBasedStrategy(WarmingStrategy[K]):
    """
    Recency-based warming strategy.
    
    This strategy warms keys based on their recent access time.
    """
    
    def __init__(self, max_age: float = 3600.0):
        """
        Initialize strategy.
        
        Args:
            max_age: Maximum age in seconds to consider for warming
        """
        self.access_times: Dict[K, float] = {}
        self.max_age = max_age
        self.lock = threading.RLock()
    
    def get_keys_to_warm(self, max_keys: int = 100) -> List[K]:
        """
        Get keys to warm based on recency.
        
        Args:
            max_keys: Maximum number of keys to return
            
        Returns:
            List of keys to warm
        """
        with self.lock:
            current_time = time.time()
            
            # Filter by maximum age
            recent_keys = [
                (key, access_time) for key, access_time in self.access_times.items()
                if current_time - access_time <= self.max_age
            ]
            
            # Sort by recency (most recent first)
            sorted_keys = sorted(
                recent_keys,
                key=lambda x: x[1],
                reverse=True
            )
            
            # Extract keys only
            return [key for key, _ in sorted_keys[:max_keys]]
    
    def update_stats(self, key: K, access_time: float = None, hit: bool = False):
        """
        Update recency statistics for a key.
        
        Args:
            key: Cache key
            access_time: Access timestamp (defaults to current time)
            hit: Whether the access was a cache hit
        """
        with self.lock:
            # Update access time
            self.access_times[key] = access_time or time.time()
    
    def clear_stats(self):
        """Clear all recency statistics."""
        with self.lock:
            self.access_times.clear()


class PatternBasedStrategy(WarmingStrategy[K]):
    """
    Pattern-based warming strategy.
    
    This strategy warms keys based on access patterns.
    """
    
    def __init__(self, max_pattern_length: int = 3, min_pattern_frequency: int = 2):
        """
        Initialize strategy.
        
        Args:
            max_pattern_length: Maximum length of patterns to track
            min_pattern_frequency: Minimum frequency for a pattern to be considered
        """
        self.patterns: Dict[Tuple[K, ...], int] = {}
        self.recent_accesses: List[K] = []
        self.max_pattern_length = max_pattern_length
        self.min_pattern_frequency = min_pattern_frequency
        self.lock = threading.RLock()
    
    def get_keys_to_warm(self, max_keys: int = 100) -> List[K]:
        """
        Get keys to warm based on patterns.
        
        Args:
            max_keys: Maximum number of keys to return
            
        Returns:
            List of keys to warm
        """
        with self.lock:
            if not self.recent_accesses or not self.patterns:
                return []
            
            # Get recent pattern
            recent_pattern_length = min(len(self.recent_accesses), self.max_pattern_length)
            recent_pattern = tuple(self.recent_accesses[-recent_pattern_length:])
            
            # Find matching patterns
            next_keys = []
            
            # Check for exact matches
            for pattern, frequency in self.patterns.items():
                if len(pattern) > recent_pattern_length and pattern[:recent_pattern_length] == recent_pattern:
                    next_keys.append((pattern[recent_pattern_length], frequency))
            
            # Check for partial matches (if no exact matches)
            if not next_keys:
                for i in range(recent_pattern_length - 1, 0, -1):
                    partial_pattern = recent_pattern[-i:]
                    
                    for pattern, frequency in self.patterns.items():
                        if len(pattern) > i and pattern[:i] == partial_pattern:
                            next_keys.append((pattern[i], frequency))
                    
                    if next_keys:
                        break
            
            # Sort by frequency (descending)
            sorted_keys = sorted(next_keys, key=lambda x: x[1], reverse=True)
            
            # Extract keys only
            return [key for key, _ in sorted_keys[:max_keys]]
    
    def update_stats(self, key: K, access_time: float = None, hit: bool = False):
        """
        Update pattern statistics for a key.
        
        Args:
            key: Cache key
            access_time: Access timestamp (not used in this strategy)
            hit: Whether the access was a cache hit
        """
        with self.lock:
            # Add to recent accesses
            self.recent_accesses.append(key)
            
            # Trim recent accesses
            if len(self.recent_accesses) > self.max_pattern_length * 2:
                self.recent_accesses = self.recent_accesses[-self.max_pattern_length * 2:]
            
            # Update patterns
            for length in range(2, min(len(self.recent_accesses), self.max_pattern_length + 1)):
                pattern = tuple(self.recent_accesses[-length:])
                self.patterns[pattern] = self.patterns.get(pattern, 0) + 1
            
            # Prune patterns
            self.patterns = {
                pattern: frequency for pattern, frequency in self.patterns.items()
                if frequency >= self.min_pattern_frequency
            }
    
    def clear_stats(self):
        """Clear all pattern statistics."""
        with self.lock:
            self.patterns.clear()
            self.recent_accesses.clear()


class HybridStrategy(WarmingStrategy[K]):
    """
    Hybrid warming strategy.
    
    This strategy combines multiple warming strategies with weights.
    """
    
    def __init__(self, strategies: List[Tuple[WarmingStrategy[K], float]]):
        """
        Initialize strategy.
        
        Args:
            strategies: List of (strategy, weight) tuples
        """
        self.strategies = strategies
        self.lock = threading.RLock()
    
    def get_keys_to_warm(self, max_keys: int = 100) -> List[K]:
        """
        Get keys to warm based on multiple strategies.
        
        Args:
            max_keys: Maximum number of keys to return
            
        Returns:
            List of keys to warm
        """
        with self.lock:
            # Distribute max_keys among strategies based on weights
            total_weight = sum(weight for _, weight in self.strategies)
            
            if total_weight == 0:
                return []
            
            strategy_keys = []
            
            for strategy, weight in self.strategies:
                # Calculate number of keys for this strategy
                strategy_max_keys = int(max_keys * (weight / total_weight))
                
                if strategy_max_keys > 0:
                    # Get keys from strategy
                    keys = strategy.get_keys_to_warm(strategy_max_keys)
                    strategy_keys.extend(keys)
            
            # Remove duplicates
            unique_keys = []
            seen = set()
            
            for key in strategy_keys:
                if key not in seen:
                    unique_keys.append(key)
                    seen.add(key)
                    
                    if len(unique_keys) >= max_keys:
                        break
            
            return unique_keys
    
    def update_stats(self, key: K, access_time: float = None, hit: bool = False):
        """
        Update statistics for all strategies.
        
        Args:
            key: Cache key
            access_time: Access timestamp
            hit: Whether the access was a cache hit
        """
        with self.lock:
            # Update all strategies
            for strategy, _ in self.strategies:
                strategy.update_stats(key, access_time, hit)
    
    def clear_stats(self):
        """Clear statistics for all strategies."""
        with self.lock:
            # Clear all strategies
            for strategy, _ in self.strategies:
                strategy.clear_stats()


class PredictiveStrategy(WarmingStrategy[K]):
    """
    Predictive warming strategy.
    
    This strategy uses predictive caching to determine keys to warm.
    """
    
    def __init__(self, predictor: Any):
        """
        Initialize strategy.
        
        Args:
            predictor: Predictive cache or access pattern analyzer
        """
        self.predictor = predictor
        self.lock = threading.RLock()
    
    def get_keys_to_warm(self, max_keys: int = 100) -> List[K]:
        """
        Get keys to warm based on predictions.
        
        Args:
            max_keys: Maximum number of keys to return
            
        Returns:
            List of keys to warm
        """
        with self.lock:
            # Check if predictor has predict_next_accesses method
            if hasattr(self.predictor, 'predict_next_accesses'):
                # Get recent keys
                recent_keys = []
                
                if hasattr(self.predictor, 'recent_keys'):
                    recent_keys = list(self.predictor.recent_keys)
                
                # Get predictions
                return self.predictor.predict_next_accesses(
                    recent_keys=recent_keys,
                    max_predictions=max_keys
                )
            
            # Check if predictor has prefetcher
            elif hasattr(self.predictor, 'prefetcher') and hasattr(self.predictor.prefetcher, 'analyzer'):
                # Get recent keys
                recent_keys = []
                
                if hasattr(self.predictor, 'recent_keys'):
                    recent_keys = list(self.predictor.recent_keys)
                
                # Get predictions
                return self.predictor.prefetcher.analyzer.predict_next_accesses(
                    recent_keys=recent_keys,
                    max_predictions=max_keys
                )
            
            # Fallback
            return []
    
    def update_stats(self, key: K, access_time: float = None, hit: bool = False):
        """
        Update statistics for predictor.
        
        Args:
            key: Cache key
            access_time: Access timestamp
            hit: Whether the access was a cache hit
        """
        with self.lock:
            # Check if predictor has notify_access method
            if hasattr(self.predictor, 'notify_access'):
                self.predictor.notify_access(key, None, hit)
            
            # Check if predictor has prefetcher with notify_access method
            elif hasattr(self.predictor, 'prefetcher') and hasattr(self.predictor.prefetcher, 'notify_access'):
                self.predictor.prefetcher.notify_access(key, None, hit)
    
    def clear_stats(self):
        """Clear statistics for predictor."""
        with self.lock:
            # Check if predictor has clear method
            if hasattr(self.predictor, 'clear'):
                self.predictor.clear()
            
            # Check if predictor has analyzer with clear method
            elif hasattr(self.predictor, 'analyzer') and hasattr(self.predictor.analyzer, 'clear'):
                self.predictor.analyzer.clear()


class WarmingStrategyFactory:
    """Factory for creating warming strategies."""
    
    @staticmethod
    def create_strategy(
        strategy_type: str,
        predictor: Any = None,
        **kwargs
    ) -> WarmingStrategy:
        """
        Create a warming strategy.
        
        Args:
            strategy_type: Type of strategy ('frequency', 'recency', 'pattern', 'hybrid', 'predictive')
            predictor: Predictive cache or access pattern analyzer (for predictive strategy)
            **kwargs: Additional arguments for specific strategies
            
        Returns:
            Warming strategy
        """
        strategy_type = strategy_type.lower()
        
        if strategy_type == 'frequency':
            return FrequencyBasedStrategy(
                decay_factor=kwargs.get('decay_factor', 0.95),
                min_frequency=kwargs.get('min_frequency', 0.1)
            )
        
        elif strategy_type == 'recency':
            return RecencyBasedStrategy(
                max_age=kwargs.get('max_age', 3600.0)
            )
        
        elif strategy_type == 'pattern':
            return PatternBasedStrategy(
                max_pattern_length=kwargs.get('max_pattern_length', 3),
                min_pattern_frequency=kwargs.get('min_pattern_frequency', 2)
            )
        
        elif strategy_type == 'predictive':
            if predictor is None:
                logger.warning("Predictor is required for predictive strategy. Falling back to frequency-based strategy.")
                return FrequencyBasedStrategy()
            
            return PredictiveStrategy(predictor)
        
        elif strategy_type == 'hybrid':
            # Create strategies
            strategies = []
            
            # Add frequency-based strategy
            if kwargs.get('use_frequency', True):
                strategies.append((
                    FrequencyBasedStrategy(
                        decay_factor=kwargs.get('frequency_decay_factor', 0.95),
                        min_frequency=kwargs.get('frequency_min_frequency', 0.1)
                    ),
                    kwargs.get('frequency_weight', 1.0)
                ))
            
            # Add recency-based strategy
            if kwargs.get('use_recency', True):
                strategies.append((
                    RecencyBasedStrategy(
                        max_age=kwargs.get('recency_max_age', 3600.0)
                    ),
                    kwargs.get('recency_weight', 1.0)
                ))
            
            # Add pattern-based strategy
            if kwargs.get('use_pattern', True):
                strategies.append((
                    PatternBasedStrategy(
                        max_pattern_length=kwargs.get('pattern_max_length', 3),
                        min_pattern_frequency=kwargs.get('pattern_min_frequency', 2)
                    ),
                    kwargs.get('pattern_weight', 1.0)
                ))
            
            # Add predictive strategy
            if kwargs.get('use_predictive', True) and predictor is not None:
                strategies.append((
                    PredictiveStrategy(predictor),
                    kwargs.get('predictive_weight', 1.0)
                ))
            
            # Create hybrid strategy
            return HybridStrategy(strategies)
        
        else:
            logger.warning(f"Unknown strategy type: {strategy_type}. Using frequency-based strategy.")
            return FrequencyBasedStrategy()
