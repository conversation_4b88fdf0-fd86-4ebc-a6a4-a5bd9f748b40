"""
Access pattern analyzer for Deep Research Core.

This module provides functionality for analyzing cache access patterns
to identify common patterns and predict future accesses.
"""

import time
import json
import hashlib
import threading
import collections
from typing import Dict, List, Any, Optional, Set, Tuple, Union, Counter as CounterType
from datetime import datetime, timedelta

from deep_research_core.utils.structured_logging import get_logger

# Get logger
logger = get_logger(__name__)


class AccessRecord:
    """Record of a cache access."""
    
    def __init__(
        self,
        key: str,
        timestamp: float = None,
        context: Optional[Dict[str, Any]] = None,
        is_hit: bool = False,
        access_type: str = "get"
    ):
        """
        Initialize access record.
        
        Args:
            key: Cache key
            timestamp: Access timestamp (defaults to current time)
            context: Additional context for the access
            is_hit: Whether the access was a cache hit
            access_type: Type of access (get, set, delete, etc.)
        """
        self.key = key
        self.timestamp = timestamp or time.time()
        self.context = context or {}
        self.is_hit = is_hit
        self.access_type = access_type
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert to dictionary.
        
        Returns:
            Dictionary representation
        """
        return {
            "key": self.key,
            "timestamp": self.timestamp,
            "context": self.context,
            "is_hit": self.is_hit,
            "access_type": self.access_type
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AccessRecord':
        """
        Create from dictionary.
        
        Args:
            data: Dictionary representation
            
        Returns:
            AccessRecord instance
        """
        return cls(
            key=data["key"],
            timestamp=data["timestamp"],
            context=data["context"],
            is_hit=data["is_hit"],
            access_type=data["access_type"]
        )


class AccessPatternAnalyzerConfig:
    """Configuration for access pattern analyzer."""
    
    def __init__(
        self,
        max_history_size: int = 10000,
        analysis_interval: int = 3600,  # 1 hour
        min_pattern_frequency: int = 3,
        max_pattern_length: int = 5,
        enable_periodic_analysis: bool = True,
        storage_path: Optional[str] = None,
        persist_interval: int = 3600,  # 1 hour
        context_keys: List[str] = None
    ):
        """
        Initialize configuration.
        
        Args:
            max_history_size: Maximum number of access records to keep
            analysis_interval: Interval in seconds between analyses
            min_pattern_frequency: Minimum frequency for a pattern to be considered
            max_pattern_length: Maximum length of patterns to analyze
            enable_periodic_analysis: Whether to enable periodic analysis
            storage_path: Path to store analysis results
            persist_interval: Interval in seconds between persistence
            context_keys: Keys to extract from context for pattern analysis
        """
        self.max_history_size = max_history_size
        self.analysis_interval = analysis_interval
        self.min_pattern_frequency = min_pattern_frequency
        self.max_pattern_length = max_pattern_length
        self.enable_periodic_analysis = enable_periodic_analysis
        self.storage_path = storage_path
        self.persist_interval = persist_interval
        self.context_keys = context_keys or []


class AccessPatternAnalyzer:
    """
    Analyzer for cache access patterns.
    
    This class analyzes cache access patterns to identify common patterns
    and predict future accesses.
    """
    
    def __init__(self, config: Optional[AccessPatternAnalyzerConfig] = None):
        """
        Initialize analyzer.
        
        Args:
            config: Configuration
        """
        self.config = config or AccessPatternAnalyzerConfig()
        
        # Initialize access history
        self.access_history: List[AccessRecord] = []
        
        # Initialize patterns
        self.sequential_patterns: Dict[Tuple[str, ...], int] = {}
        self.temporal_patterns: Dict[str, List[str]] = {}
        self.context_patterns: Dict[str, Dict[str, int]] = {}
        
        # Initialize lock
        self.lock = threading.RLock()
        
        # Initialize analysis thread
        self.stop_analysis = threading.Event()
        self.analysis_thread = None
        
        # Start analysis thread if enabled
        if self.config.enable_periodic_analysis:
            self._start_analysis_thread()
    
    def _start_analysis_thread(self):
        """Start analysis thread."""
        if self.analysis_thread is not None:
            return
        
        self.analysis_thread = threading.Thread(
            target=self._analysis_worker,
            daemon=True
        )
        self.analysis_thread.start()
    
    def _analysis_worker(self):
        """Worker function for analysis thread."""
        while not self.stop_analysis.is_set():
            # Sleep for analysis interval
            self.stop_analysis.wait(self.config.analysis_interval)
            
            # Analyze patterns
            self.analyze_patterns()
            
            # Persist if enabled
            if self.config.storage_path:
                self.persist()
    
    def record_access(
        self,
        key: str,
        context: Optional[Dict[str, Any]] = None,
        is_hit: bool = False,
        access_type: str = "get"
    ):
        """
        Record a cache access.
        
        Args:
            key: Cache key
            context: Additional context for the access
            is_hit: Whether the access was a cache hit
            access_type: Type of access (get, set, delete, etc.)
        """
        with self.lock:
            # Create access record
            record = AccessRecord(
                key=key,
                context=context,
                is_hit=is_hit,
                access_type=access_type
            )
            
            # Add to history
            self.access_history.append(record)
            
            # Trim history if needed
            if len(self.access_history) > self.config.max_history_size:
                self.access_history = self.access_history[-self.config.max_history_size:]
    
    def analyze_patterns(self):
        """
        Analyze access patterns.
        
        This method analyzes the access history to identify common patterns.
        """
        with self.lock:
            if not self.access_history:
                return
            
            # Analyze sequential patterns
            self._analyze_sequential_patterns()
            
            # Analyze temporal patterns
            self._analyze_temporal_patterns()
            
            # Analyze context patterns
            self._analyze_context_patterns()
            
            logger.info(f"Analyzed access patterns: {len(self.sequential_patterns)} sequential, "
                       f"{len(self.temporal_patterns)} temporal, {len(self.context_patterns)} context")
    
    def _analyze_sequential_patterns(self):
        """Analyze sequential access patterns."""
        # Extract keys
        keys = [record.key for record in self.access_history if record.access_type == "get"]
        
        # Count n-grams
        for n in range(2, min(self.config.max_pattern_length + 1, len(keys))):
            for i in range(len(keys) - n + 1):
                pattern = tuple(keys[i:i+n])
                self.sequential_patterns[pattern] = self.sequential_patterns.get(pattern, 0) + 1
        
        # Filter by frequency
        self.sequential_patterns = {
            pattern: count for pattern, count in self.sequential_patterns.items()
            if count >= self.config.min_pattern_frequency
        }
    
    def _analyze_temporal_patterns(self):
        """Analyze temporal access patterns."""
        # Group by hour of day
        hour_patterns: Dict[int, List[str]] = {}
        
        for record in self.access_history:
            dt = datetime.fromtimestamp(record.timestamp)
            hour = dt.hour
            
            if hour not in hour_patterns:
                hour_patterns[hour] = []
            
            hour_patterns[hour].append(record.key)
        
        # Count frequencies for each hour
        for hour, keys in hour_patterns.items():
            counter = collections.Counter(keys)
            common = counter.most_common()
            
            # Keep only keys with frequency >= min_pattern_frequency
            common_keys = [key for key, count in common if count >= self.config.min_pattern_frequency]
            
            if common_keys:
                self.temporal_patterns[f"hour_{hour}"] = common_keys
    
    def _analyze_context_patterns(self):
        """Analyze context-based access patterns."""
        if not self.config.context_keys:
            return
        
        # Group by context values
        for record in self.access_history:
            if not record.context:
                continue
            
            for context_key in self.config.context_keys:
                if context_key not in record.context:
                    continue
                
                context_value = str(record.context[context_key])
                pattern_key = f"{context_key}:{context_value}"
                
                if pattern_key not in self.context_patterns:
                    self.context_patterns[pattern_key] = {}
                
                self.context_patterns[pattern_key][record.key] = \
                    self.context_patterns[pattern_key].get(record.key, 0) + 1
        
        # Filter by frequency
        for pattern_key, key_counts in list(self.context_patterns.items()):
            # Keep only keys with frequency >= min_pattern_frequency
            filtered_counts = {
                key: count for key, count in key_counts.items()
                if count >= self.config.min_pattern_frequency
            }
            
            if filtered_counts:
                self.context_patterns[pattern_key] = filtered_counts
            else:
                del self.context_patterns[pattern_key]
    
    def predict_next_accesses(
        self,
        recent_keys: List[str],
        context: Optional[Dict[str, Any]] = None,
        max_predictions: int = 5
    ) -> List[str]:
        """
        Predict next cache accesses.
        
        Args:
            recent_keys: Recently accessed keys
            context: Current context
            max_predictions: Maximum number of predictions to return
            
        Returns:
            List of predicted keys
        """
        with self.lock:
            predictions = []
            
            # Check sequential patterns
            seq_predictions = self._predict_from_sequential(recent_keys)
            predictions.extend(seq_predictions)
            
            # Check temporal patterns
            temp_predictions = self._predict_from_temporal()
            predictions.extend(temp_predictions)
            
            # Check context patterns
            if context:
                context_predictions = self._predict_from_context(context)
                predictions.extend(context_predictions)
            
            # Remove duplicates and limit
            unique_predictions = []
            for key in predictions:
                if key not in unique_predictions and key not in recent_keys:
                    unique_predictions.append(key)
                    if len(unique_predictions) >= max_predictions:
                        break
            
            return unique_predictions
    
    def _predict_from_sequential(self, recent_keys: List[str]) -> List[str]:
        """
        Predict from sequential patterns.
        
        Args:
            recent_keys: Recently accessed keys
            
        Returns:
            List of predicted keys
        """
        if not recent_keys or not self.sequential_patterns:
            return []
        
        predictions = []
        
        # Check for matches in sequential patterns
        for length in range(min(len(recent_keys), self.config.max_pattern_length), 0, -1):
            recent_pattern = tuple(recent_keys[-length:])
            
            # Find patterns that start with recent_pattern
            for pattern, count in self.sequential_patterns.items():
                if len(pattern) > length and pattern[:length] == recent_pattern:
                    predictions.append((pattern[length], count))
        
        # Sort by count (descending)
        predictions.sort(key=lambda x: x[1], reverse=True)
        
        # Return keys only
        return [key for key, _ in predictions]
    
    def _predict_from_temporal(self) -> List[str]:
        """
        Predict from temporal patterns.
        
        Returns:
            List of predicted keys
        """
        if not self.temporal_patterns:
            return []
        
        # Get current hour
        current_hour = datetime.now().hour
        hour_key = f"hour_{current_hour}"
        
        if hour_key in self.temporal_patterns:
            return self.temporal_patterns[hour_key]
        
        return []
    
    def _predict_from_context(self, context: Dict[str, Any]) -> List[str]:
        """
        Predict from context patterns.
        
        Args:
            context: Current context
            
        Returns:
            List of predicted keys
        """
        if not self.context_patterns or not context:
            return []
        
        predictions = []
        
        # Check for matches in context patterns
        for context_key in self.config.context_keys:
            if context_key not in context:
                continue
            
            context_value = str(context[context_key])
            pattern_key = f"{context_key}:{context_value}"
            
            if pattern_key in self.context_patterns:
                # Sort by count (descending)
                sorted_keys = sorted(
                    self.context_patterns[pattern_key].items(),
                    key=lambda x: x[1],
                    reverse=True
                )
                
                # Add keys to predictions
                for key, _ in sorted_keys:
                    predictions.append(key)
        
        return predictions
    
    def get_common_patterns(self, pattern_type: str = "sequential", limit: int = 10) -> Dict[str, Any]:
        """
        Get common access patterns.
        
        Args:
            pattern_type: Type of patterns to get (sequential, temporal, context)
            limit: Maximum number of patterns to return
            
        Returns:
            Dictionary of common patterns
        """
        with self.lock:
            if pattern_type == "sequential":
                # Sort by count (descending)
                sorted_patterns = sorted(
                    self.sequential_patterns.items(),
                    key=lambda x: x[1],
                    reverse=True
                )
                
                # Format patterns
                return {
                    "type": "sequential",
                    "patterns": [
                        {
                            "sequence": list(pattern),
                            "count": count
                        }
                        for pattern, count in sorted_patterns[:limit]
                    ]
                }
            
            elif pattern_type == "temporal":
                return {
                    "type": "temporal",
                    "patterns": {
                        hour_key: keys[:limit]
                        for hour_key, keys in self.temporal_patterns.items()
                    }
                }
            
            elif pattern_type == "context":
                return {
                    "type": "context",
                    "patterns": {
                        context_key: dict(sorted(
                            key_counts.items(),
                            key=lambda x: x[1],
                            reverse=True
                        )[:limit])
                        for context_key, key_counts in self.context_patterns.items()
                    }
                }
            
            else:
                return {}
    
    def persist(self) -> bool:
        """
        Persist analysis results to disk.
        
        Returns:
            True if successful, False otherwise
        """
        if not self.config.storage_path:
            return False
        
        try:
            # Create data to persist
            data = {
                "timestamp": time.time(),
                "sequential_patterns": {
                    ",".join(pattern): count
                    for pattern, count in self.sequential_patterns.items()
                },
                "temporal_patterns": self.temporal_patterns,
                "context_patterns": self.context_patterns
            }
            
            # Write to file
            with open(self.config.storage_path, "w") as f:
                json.dump(data, f)
            
            logger.info(f"Persisted access patterns to {self.config.storage_path}")
            return True
        
        except Exception as e:
            logger.error(f"Error persisting access patterns: {str(e)}")
            return False
    
    def load(self) -> bool:
        """
        Load analysis results from disk.
        
        Returns:
            True if successful, False otherwise
        """
        if not self.config.storage_path:
            return False
        
        try:
            # Read from file
            with open(self.config.storage_path, "r") as f:
                data = json.load(f)
            
            # Parse sequential patterns
            self.sequential_patterns = {
                tuple(pattern.split(",")): count
                for pattern, count in data.get("sequential_patterns", {}).items()
            }
            
            # Parse temporal patterns
            self.temporal_patterns = data.get("temporal_patterns", {})
            
            # Parse context patterns
            self.context_patterns = data.get("context_patterns", {})
            
            logger.info(f"Loaded access patterns from {self.config.storage_path}")
            return True
        
        except Exception as e:
            logger.error(f"Error loading access patterns: {str(e)}")
            return False
    
    def clear(self):
        """Clear all analysis data."""
        with self.lock:
            self.access_history.clear()
            self.sequential_patterns.clear()
            self.temporal_patterns.clear()
            self.context_patterns.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the analyzer.
        
        Returns:
            Dictionary of statistics
        """
        with self.lock:
            return {
                "history_size": len(self.access_history),
                "sequential_patterns": len(self.sequential_patterns),
                "temporal_patterns": len(self.temporal_patterns),
                "context_patterns": len(self.context_patterns),
                "config": {
                    "max_history_size": self.config.max_history_size,
                    "analysis_interval": self.config.analysis_interval,
                    "min_pattern_frequency": self.config.min_pattern_frequency,
                    "max_pattern_length": self.config.max_pattern_length,
                    "enable_periodic_analysis": self.config.enable_periodic_analysis,
                    "context_keys": self.config.context_keys
                }
            }
    
    def close(self):
        """Close analyzer and release resources."""
        # Stop analysis thread
        if self.analysis_thread is not None:
            self.stop_analysis.set()
            self.analysis_thread.join(timeout=1.0)
        
        # Persist if enabled
        if self.config.storage_path:
            self.persist()
