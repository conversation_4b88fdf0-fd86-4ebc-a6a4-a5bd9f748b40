"""
Predictive caching implementation for Deep Research Core.

This module provides a predictive caching mechanism that uses access pattern
analysis to predict and prefetch cache entries before they are needed.
"""

import time
import threading
from typing import Dict, List, Any, Optional, Set, Tuple, Union, Callable, TypeVar, Generic

from deep_research_core.utils.structured_logging import get_logger
from deep_research_core.optimization.caching.access_pattern_analyzer import (
    AccessPatternAnalyzer,
    AccessPatternAnalyzerConfig
)
from deep_research_core.optimization.caching.cache_prefetcher import (
    CachePrefetcher,
    CachePrefetcherConfig,
    PrefetchRequest
)

# Get logger
logger = get_logger(__name__)

# Type variables
K = TypeVar('K')  # Key type
V = TypeVar('V')  # Value type


class PredictiveCacheConfig:
    """Configuration for predictive cache."""
    
    def __init__(
        self,
        capacity: int = 1000,
        ttl: int = 3600,  # 1 hour
        analyzer_config: Optional[AccessPatternAnalyzerConfig] = None,
        prefetcher_config: Optional[CachePrefetcherConfig] = None,
        max_recent_keys: int = 10,
        prefetch_on_miss: bool = True,
        prefetch_on_hit: bool = False,
        prefetch_threshold: float = 0.5,
        max_prefetch_count: int = 5,
        enable_stats: bool = True,
        stats_interval: int = 3600  # 1 hour
    ):
        """
        Initialize configuration.
        
        Args:
            capacity: Maximum number of entries in the cache
            ttl: Default time-to-live in seconds
            analyzer_config: Configuration for access pattern analyzer
            prefetcher_config: Configuration for cache prefetcher
            max_recent_keys: Maximum number of recent keys to track
            prefetch_on_miss: Whether to trigger prefetching on cache miss
            prefetch_on_hit: Whether to trigger prefetching on cache hit
            prefetch_threshold: Minimum prediction confidence to trigger prefetching
            max_prefetch_count: Maximum number of entries to prefetch at once
            enable_stats: Whether to collect and report statistics
            stats_interval: Interval in seconds for reporting statistics
        """
        self.capacity = capacity
        self.ttl = ttl
        self.analyzer_config = analyzer_config or AccessPatternAnalyzerConfig()
        self.prefetcher_config = prefetcher_config or CachePrefetcherConfig()
        self.max_recent_keys = max_recent_keys
        self.prefetch_on_miss = prefetch_on_miss
        self.prefetch_on_hit = prefetch_on_hit
        self.prefetch_threshold = prefetch_threshold
        self.max_prefetch_count = max_prefetch_count
        self.enable_stats = enable_stats
        self.stats_interval = stats_interval


class PredictiveCache(Generic[K, V]):
    """
    Predictive cache implementation.
    
    This cache uses access pattern analysis to predict and prefetch
    entries before they are needed.
    """
    
    def __init__(
        self,
        backend_cache: Any,
        config: Optional[PredictiveCacheConfig] = None,
        fetch_func: Optional[Callable[[K], V]] = None
    ):
        """
        Initialize predictive cache.
        
        Args:
            backend_cache: Backend cache to use for storage
            config: Configuration
            fetch_func: Function to fetch a value for a key
        """
        self.backend_cache = backend_cache
        self.config = config or PredictiveCacheConfig()
        self.fetch_func = fetch_func
        
        # Initialize access pattern analyzer
        self.analyzer = AccessPatternAnalyzer(self.config.analyzer_config)
        
        # Initialize cache prefetcher
        self.prefetcher = CachePrefetcher(
            prefetch_func=self._prefetch_func,
            analyzer=self.analyzer,
            config=self.config.prefetcher_config
        )
        
        # Initialize recent keys
        self.recent_keys: List[K] = []
        self.recent_keys_lock = threading.RLock()
        
        # Initialize stats
        self.stats = {
            "gets": 0,
            "hits": 0,
            "misses": 0,
            "prefetch_hits": 0,
            "sets": 0,
            "deletes": 0
        }
        self.stats_lock = threading.RLock()
        
        # Initialize stats reporting
        self.stop_stats = threading.Event()
        self.stats_thread = None
        
        # Start stats thread if enabled
        if self.config.enable_stats:
            self._start_stats_thread()
    
    def _start_stats_thread(self):
        """Start stats reporting thread."""
        if self.stats_thread is not None:
            return
        
        self.stats_thread = threading.Thread(
            target=self._stats_worker,
            daemon=True
        )
        self.stats_thread.start()
    
    def _stats_worker(self):
        """Worker function for stats reporting thread."""
        while not self.stop_stats.is_set():
            # Sleep for stats interval
            self.stop_stats.wait(self.config.stats_interval)
            
            # Report stats
            self._report_stats()
    
    def _report_stats(self):
        """Report cache statistics."""
        with self.stats_lock:
            # Calculate hit rate
            total = self.stats["hits"] + self.stats["misses"]
            hit_rate = self.stats["hits"] / total if total > 0 else 0
            
            # Calculate prefetch hit rate
            prefetch_hit_rate = self.stats["prefetch_hits"] / self.stats["hits"] if self.stats["hits"] > 0 else 0
            
            logger.info(f"Predictive cache stats: gets={self.stats['gets']}, "
                       f"hits={self.stats['hits']}, misses={self.stats['misses']}, "
                       f"hit_rate={hit_rate:.2f}, prefetch_hits={self.stats['prefetch_hits']}, "
                       f"prefetch_hit_rate={prefetch_hit_rate:.2f}")
    
    def _prefetch_func(self, key: K, context: Optional[Dict[str, Any]] = None) -> Optional[V]:
        """
        Function to prefetch a value for a key.
        
        Args:
            key: Key to prefetch
            context: Additional context
            
        Returns:
            Prefetched value or None if not available
        """
        # Check if fetch_func is available
        if self.fetch_func is None:
            return None
        
        try:
            # Fetch value
            value = self.fetch_func(key)
            
            # Store in backend cache
            self.backend_cache.set(key, value)
            
            return value
        
        except Exception as e:
            logger.error(f"Error prefetching key {key}: {str(e)}")
            return None
    
    def _update_recent_keys(self, key: K):
        """
        Update the list of recent keys.
        
        Args:
            key: Key to add
        """
        with self.recent_keys_lock:
            # Remove key if already in list
            if key in self.recent_keys:
                self.recent_keys.remove(key)
            
            # Add key to front of list
            self.recent_keys.insert(0, key)
            
            # Trim list if needed
            if len(self.recent_keys) > self.config.max_recent_keys:
                self.recent_keys = self.recent_keys[:self.config.max_recent_keys]
    
    def _trigger_prefetching(self, key: K, context: Optional[Dict[str, Any]] = None, is_hit: bool = False):
        """
        Trigger prefetching based on recent accesses.
        
        Args:
            key: Current key
            context: Additional context
            is_hit: Whether the access was a cache hit
        """
        # Check if prefetching should be triggered
        if (is_hit and not self.config.prefetch_on_hit) or (not is_hit and not self.config.prefetch_on_miss):
            return
        
        # Get recent keys
        with self.recent_keys_lock:
            recent_keys = list(self.recent_keys)
        
        # Trigger prefetching
        self.prefetcher.prefetch_predicted(
            recent_keys=recent_keys,
            context=context,
            max_predictions=self.config.max_prefetch_count
        )
    
    def get(self, key: K, context: Optional[Dict[str, Any]] = None) -> Optional[V]:
        """
        Get value from cache.
        
        Args:
            key: Cache key
            context: Additional context
            
        Returns:
            Cached value or None if not found
        """
        # Update stats
        with self.stats_lock:
            self.stats["gets"] += 1
        
        # Get from backend cache
        value = self.backend_cache.get(key)
        
        # Check if value was found
        is_hit = value is not None
        
        # Check if hit was due to prefetching
        is_prefetch_hit = is_hit and self.prefetcher.is_prefetched(key)
        
        # Update stats
        with self.stats_lock:
            if is_hit:
                self.stats["hits"] += 1
                if is_prefetch_hit:
                    self.stats["prefetch_hits"] += 1
            else:
                self.stats["misses"] += 1
        
        # Update recent keys
        self._update_recent_keys(key)
        
        # Notify analyzer and prefetcher
        self.prefetcher.notify_access(key, context, is_hit)
        
        # Trigger prefetching
        self._trigger_prefetching(key, context, is_hit)
        
        return value
    
    def set(self, key: K, value: V, ttl: Optional[int] = None) -> bool:
        """
        Set value in cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time-to-live in seconds (None for default TTL)
            
        Returns:
            True if successful, False otherwise
        """
        # Update stats
        with self.stats_lock:
            self.stats["sets"] += 1
        
        # Set in backend cache
        if hasattr(self.backend_cache, 'set'):
            if ttl is not None:
                return self.backend_cache.set(key, value, ttl)
            else:
                return self.backend_cache.set(key, value)
        else:
            # Fallback for dict-like backends
            self.backend_cache[key] = value
            return True
    
    def delete(self, key: K) -> bool:
        """
        Delete value from cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if successful, False otherwise
        """
        # Update stats
        with self.stats_lock:
            self.stats["deletes"] += 1
        
        # Delete from backend cache
        if hasattr(self.backend_cache, 'delete'):
            return self.backend_cache.delete(key)
        elif hasattr(self.backend_cache, 'remove'):
            return self.backend_cache.remove(key)
        else:
            # Fallback for dict-like backends
            if key in self.backend_cache:
                del self.backend_cache[key]
                return True
            return False
    
    def clear(self):
        """Clear the cache."""
        # Clear backend cache
        if hasattr(self.backend_cache, 'clear'):
            self.backend_cache.clear()
        else:
            # Fallback for dict-like backends
            self.backend_cache.clear()
        
        # Clear recent keys
        with self.recent_keys_lock:
            self.recent_keys.clear()
        
        # Clear prefetcher
        self.prefetcher.clear_prefetched()
        
        # Clear analyzer
        self.analyzer.clear()
    
    def prefetch(self, keys: List[K], priorities: Optional[List[float]] = None, context: Optional[Dict[str, Any]] = None):
        """
        Explicitly prefetch keys.
        
        Args:
            keys: List of keys to prefetch
            priorities: List of priorities (defaults to 1.0 for all)
            context: Additional context
        """
        self.prefetcher.prefetch_keys(keys, priorities, context)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the cache.
        
        Returns:
            Dictionary of statistics
        """
        with self.stats_lock:
            # Calculate hit rate
            total = self.stats["hits"] + self.stats["misses"]
            hit_rate = self.stats["hits"] / total if total > 0 else 0
            
            # Calculate prefetch hit rate
            prefetch_hit_rate = self.stats["prefetch_hits"] / self.stats["hits"] if self.stats["hits"] > 0 else 0
            
            cache_stats = {
                "gets": self.stats["gets"],
                "hits": self.stats["hits"],
                "misses": self.stats["misses"],
                "hit_rate": hit_rate,
                "prefetch_hits": self.stats["prefetch_hits"],
                "prefetch_hit_rate": prefetch_hit_rate,
                "sets": self.stats["sets"],
                "deletes": self.stats["deletes"]
            }
        
        # Get prefetcher stats
        prefetcher_stats = self.prefetcher.get_stats()
        
        # Get analyzer stats
        analyzer_stats = self.analyzer.get_stats()
        
        # Get backend cache stats
        backend_stats = {}
        if hasattr(self.backend_cache, 'get_stats'):
            backend_stats = self.backend_cache.get_stats()
        
        return {
            "cache": cache_stats,
            "prefetcher": prefetcher_stats,
            "analyzer": analyzer_stats,
            "backend": backend_stats,
            "config": {
                "capacity": self.config.capacity,
                "ttl": self.config.ttl,
                "max_recent_keys": self.config.max_recent_keys,
                "prefetch_on_miss": self.config.prefetch_on_miss,
                "prefetch_on_hit": self.config.prefetch_on_hit,
                "prefetch_threshold": self.config.prefetch_threshold,
                "max_prefetch_count": self.config.max_prefetch_count,
                "enable_stats": self.config.enable_stats,
                "stats_interval": self.config.stats_interval
            }
        }
    
    def close(self):
        """Close cache and release resources."""
        # Stop stats thread
        if self.stats_thread is not None:
            self.stop_stats.set()
            self.stats_thread.join(timeout=1.0)
        
        # Close prefetcher
        self.prefetcher.close()
        
        # Close analyzer
        self.analyzer.close()
        
        # Close backend cache
        if hasattr(self.backend_cache, 'close'):
            self.backend_cache.close()
