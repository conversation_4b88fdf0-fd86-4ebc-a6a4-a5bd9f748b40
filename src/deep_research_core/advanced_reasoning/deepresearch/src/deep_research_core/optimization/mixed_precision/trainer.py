"""
Implementation of the MixedPrecisionTrainer class for mixed precision training.

This module provides the MixedPrecisionTrainer class, which wraps PyTorch models
and optimizers to enable efficient mixed precision training.
"""

import os
import sys
import time
import json
import math
from pathlib import Path
from typing import Dict, Any, List, Optional, Union, Tuple, Callable
import logging
from dataclasses import dataclass

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from torch.optim.lr_scheduler import LambdaLR
from torch.cuda.amp import autocast, GradScaler

from .config import MixedPrecisionConfig
from .utils import (
    optimize_memory_efficiency,
    check_nan_or_inf,
    get_memory_stats,
    apply_mixed_precision_compatibility_fixes
)
from ...utils.structured_logging import get_logger
from ...utils.performance_metrics import (
    measure_latency,
    measure_memory_usage
)
# TODO: Implement measure_throughput decorator
# from ...utils.performance_metrics import measure_throughput
from deep_research_core.optimization.mixed_precision.utils import (
    get_mixed_precision_policy,
    get_model_size_in_bytes,
    is_mixed_precision_beneficial
)

# Create a logger
logger = get_logger(__name__)

@dataclass
class AutocastConfig:
    """Configuration for autocast settings in mixed precision training.

    Attributes:
        enabled (bool): Whether to enable autocast.
        dtype (torch.dtype): The data type to use for autocast.
        cache_enabled (bool): Whether to enable autocast's cache.
        cpu_enabled (bool): Whether to enable autocast on CPU.
        cuda_enabled (bool): Whether to enable autocast on CUDA devices.
    """
    enabled: bool = True
    dtype: Optional[torch.dtype] = None  # Will be determined automatically if None
    cache_enabled: bool = True
    cpu_enabled: bool = False
    cuda_enabled: bool = True

    def __post_init__(self):
        """Initialize dtype if not provided."""
        if self.dtype is None:
            # Get appropriate mixed precision policy based on device capabilities
            self.dtype = get_mixed_precision_policy()


class MixedPrecisionTrainer:
    """
    Trainer class for mixed precision training using PyTorch's Automatic Mixed Precision.

    This class wraps a model and optimizer to enable efficient mixed precision training,
    which can significantly reduce memory usage and training time while maintaining model accuracy.
    """

    def __init__(
        self,
        model: torch.nn.Module,
        optimizer: torch.optim.Optimizer,
        autocast_config: Optional[AutocastConfig] = None,
        scaler_config: Optional[Dict[str, Any]] = None,
        device: Optional[Union[str, torch.device]] = None,
        loss_fn: Optional[Callable] = None,
    ):
        """
        Initialize the MixedPrecisionTrainer.

        Args:
            model: The PyTorch model to train with mixed precision
            optimizer: The optimizer to use for training
            autocast_config: Configuration for the autocast context manager
            scaler_config: Configuration for the GradScaler
            device: The device to use for training (default: current device)
            loss_fn: Loss function to use (if provided)
        """
        self.model = model
        self.optimizer = optimizer
        self.autocast_config = autocast_config or AutocastConfig()
        self.device = device or (torch.device("cuda") if torch.cuda.is_available() else torch.device("cpu"))
        self.loss_fn = loss_fn

        # Set up gradient scaler for FP16 training
        self.scaler = GradScaler(**scaler_config if scaler_config else {})

        # Move model to device if needed
        if self.device and not next(model.parameters()).device == self.device:
            self.model.to(self.device)

        # Check if we're using a device that supports autocast
        self._supports_autocast = torch.cuda.is_available() or hasattr(torch, 'cpu') and hasattr(torch.cpu, 'amp')
        if self.autocast_config.enabled and not self._supports_autocast:
            print("Warning: Autocast enabled but current device doesn't support it. Disabling autocast.")
            self.autocast_config.enabled = False

    def forward(self, inputs: Any, targets: Optional[Any] = None) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        Perform a forward pass with mixed precision.

        Args:
            inputs: The input data
            targets: The target data (optional if loss_fn is not provided)

        Returns:
            A tuple of (outputs, loss) if loss_fn is provided, otherwise (outputs, None)
        """
        # Use autocast for forward pass
        with autocast(
            enabled=self.autocast_config.enabled,
            dtype=self.autocast_config.dtype,
            cache_enabled=self.autocast_config.cache_enabled
        ):
            outputs = self.model(inputs)
            loss = None
            if self.loss_fn is not None and targets is not None:
                loss = self.loss_fn(outputs, targets)

        return outputs, loss

    def forward_pass(self, inputs: Any, targets: Optional[Any] = None) -> torch.Tensor:
        """
        Perform a forward pass with mixed precision and return the loss.

        This method is used in the test suite.

        Args:
            inputs: The input data
            targets: The target data

        Returns:
            The loss tensor
        """
        # Use autocast for forward pass - handle both torch.amp and torch.cuda.amp
        try:
            # Try the new API first
            import torch.amp
            with torch.amp.autocast(
                device_type='cuda' if torch.cuda.is_available() else 'cpu',
                enabled=self.autocast_config.enabled,
                dtype=self.autocast_config.dtype,
                cache_enabled=self.autocast_config.cache_enabled
            ):
                outputs = self.model(inputs)
                if self.loss_fn is not None and targets is not None:
                    loss = self.loss_fn(outputs, targets)
                else:
                    # For testing purposes, just return a dummy loss
                    loss = torch.sum(outputs) if isinstance(outputs, torch.Tensor) else torch.tensor(0.0)
        except (ImportError, AttributeError, TypeError):
            # Fall back to the old API
            with autocast(
                enabled=self.autocast_config.enabled,
                dtype=self.autocast_config.dtype,
                cache_enabled=self.autocast_config.cache_enabled
            ):
                outputs = self.model(inputs)
                if self.loss_fn is not None and targets is not None:
                    loss = self.loss_fn(outputs, targets)
                else:
                    # For testing purposes, just return a dummy loss
                    loss = torch.sum(outputs) if isinstance(outputs, torch.Tensor) else torch.tensor(0.0)

        # Scale loss for testing
        scaled_loss = self.scaler.scale(loss)
        return scaled_loss

    def backward_and_update(self, loss: torch.Tensor) -> None:
        """
        Perform backward pass and parameter update with gradient scaling.

        Args:
            loss: The loss tensor from the forward pass
        """
        # Clear previous gradients
        self.optimizer.zero_grad()

        # Backward pass with gradient scaling
        self.scaler.scale(loss).backward()

        # Unscale gradients and clip if needed
        self.scaler.unscale_(self.optimizer)

        # Optimizer step with gradient scaling
        self.scaler.step(self.optimizer)

        # Update scaler for next iteration
        self.scaler.update()

    def backward_pass(self, scaled_loss: torch.Tensor) -> None:
        """
        Perform backward pass with gradient scaling.

        This method is used in the test suite.

        Args:
            scaled_loss: The scaled loss tensor from forward_pass
        """
        # Backward pass (the loss is already scaled)
        scaled_loss.backward()

        # Unscale gradients
        self.scaler.unscale_(self.optimizer)

        # Optimizer step
        self.scaler.step(self.optimizer)

        # Update scaler for next iteration
        self.scaler.update()

        # Clear gradients
        self.optimizer.zero_grad()

    def train_step(self, inputs: Any, targets: Any) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Perform a complete training step: forward, backward, and update.

        Args:
            inputs: The input data
            targets: The target data

        Returns:
            A tuple of (outputs, loss)
        """
        # Forward pass
        outputs, loss = self.forward(inputs, targets)

        if loss is None:
            if self.loss_fn is None:
                raise ValueError("Loss function must be provided to train_step if not provided in constructor")
            loss = self.loss_fn(outputs, targets)

        # Backward and update
        self.backward_and_update(loss)

        return outputs, loss

    def eval_step(self, inputs: Any, targets: Optional[Any] = None) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        Perform an evaluation step with mixed precision but without gradients.

        Args:
            inputs: The input data
            targets: The target data (optional)

        Returns:
            A tuple of (outputs, loss) if targets and loss_fn are provided, otherwise (outputs, None)
        """
        with torch.no_grad():
            return self.forward(inputs, targets)

    def save_checkpoint(self, path: str) -> None:
        """
        Save a checkpoint of the model, optimizer, and scaler state.

        Args:
            path: The path to save the checkpoint
        """
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scaler_state_dict': self.scaler.state_dict(),
        }, path)

    def load_checkpoint(self, path: str) -> None:
        """
        Load a checkpoint of the model, optimizer, and scaler state.

        Args:
            path: The path to load the checkpoint from
        """
        checkpoint = torch.load(path, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scaler.load_state_dict(checkpoint['scaler_state_dict'])

    def prepare_model(self, model: nn.Module) -> None:
        """
        Prepare the model for mixed-precision training.

        Args:
            model: The PyTorch model to prepare
        """
        # Check if config is compatible with device
        if not self.config.is_compatible_with_device():
            logger.warning("Mixed precision config is not compatible with device.")
            self.config.enabled = False
            self.config.compute_dtype = torch.float32
            self.config.param_dtype = torch.float32

        # Apply memory efficiency optimizations if needed
        if self.config.memory_efficient and self.config.enabled:
            model = optimize_memory_efficiency(model, self.config.compute_dtype)

        # Apply mixed precision compatibility fixes
        model = apply_mixed_precision_compatibility_fixes(model, self.config.compute_dtype)

        # Move model to device
        model = model.to(self.device)

        # Store model
        self.model = model

        # Check if model has prepare_inputs_for_generation for sequence models
        self.has_prepare_inputs = hasattr(model, "prepare_inputs_for_generation")

        # Get initial memory stats
        if torch.cuda.is_available():
            self.initial_memory_stats = get_memory_stats(model)

    def setup_optimizer(
        self,
        optimizer: Optional[torch.optim.Optimizer],
        kwargs: Dict[str, Any]
    ) -> None:
        """
        Setup the optimizer for training.

        Args:
            optimizer: Optional existing optimizer
            kwargs: Additional keyword arguments
        """
        if optimizer is not None:
            self.optimizer = optimizer
        else:
            # Get optimizer parameters from kwargs
            lr = kwargs.get("learning_rate", 5e-5)
            weight_decay = kwargs.get("weight_decay", 0.01)

            # Create parameter groups
            no_decay = ["bias", "LayerNorm.weight", "layer_norm.weight"]
            optimizer_grouped_parameters = [
                {
                    "params": [p for n, p in self.model.named_parameters()
                              if not any(nd in n for nd in no_decay) and p.requires_grad],
                    "weight_decay": weight_decay,
                },
                {
                    "params": [p for n, p in self.model.named_parameters()
                              if any(nd in n for nd in no_decay) and p.requires_grad],
                    "weight_decay": 0.0,
                },
            ]

            # Create optimizer
            self.optimizer = optim.AdamW(
                optimizer_grouped_parameters,
                lr=lr,
                betas=(kwargs.get("adam_beta1", 0.9), kwargs.get("adam_beta2", 0.999)),
                eps=kwargs.get("adam_epsilon", 1e-8)
            )

            logger.info(f"Created AdamW optimizer with lr={lr}, weight_decay={weight_decay}")

    def setup_lr_scheduler(
        self,
        lr_scheduler: Optional[Any],
        kwargs: Dict[str, Any]
    ) -> None:
        """
        Setup the learning rate scheduler.

        Args:
            lr_scheduler: Optional existing scheduler
            kwargs: Additional keyword arguments
        """
        if lr_scheduler is not None:
            self.lr_scheduler = lr_scheduler
        else:
            # Get scheduler parameters from kwargs
            num_training_steps = kwargs.get("num_training_steps", 1000)
            warmup_steps = kwargs.get("warmup_steps", int(0.1 * num_training_steps))

            # Create scheduler function
            def lr_lambda(current_step: int):
                if current_step < warmup_steps:
                    return float(current_step) / float(max(1, warmup_steps))
                return max(
                    0.0, float(num_training_steps - current_step) /
                    float(max(1, num_training_steps - warmup_steps))
                )

            # Create scheduler
            self.lr_scheduler = LambdaLR(self.optimizer, lr_lambda)

            logger.info(f"Created linear scheduler with warmup_steps={warmup_steps}, "
                       f"num_training_steps={num_training_steps}")

    @measure_latency("mixed_precision_training_step")
    # TODO: Implement measure_throughput decorator
    # @measure_throughput(batch_size_param="batch_size", enabled_param="verbose")
    def training_step(
        self,
        batch: Dict[str, torch.Tensor],
        batch_size: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Perform a single training step.

        Args:
            batch: Dictionary containing input tensors
            batch_size: Optional batch size override (for throughput calculation)

        Returns:
            Dict[str, Any]: Dictionary with training metrics
        """
        self.model.train()

        # Move batch to device
        batch = {k: v.to(self.device) if hasattr(v, "to") else v for k, v in batch.items()}

        # Skip if empty batch
        if not batch:
            logger.warning("Skipping empty batch")
            return {"loss": 0.0}

        # Get batch size for throughput calculation
        if batch_size is None:
            batch_size = batch.get("input_ids", next(iter(batch.values()))).shape[0]

        # Prepare inputs based on model type
        inputs = batch

        # Use autocast for mixed precision
        with autocast(enabled=self.config.enabled, dtype=self.config.compute_dtype):
            # Forward pass
            outputs = self.model(**inputs)
            loss = outputs.loss if hasattr(outputs, "loss") else outputs["loss"]

            # Scale loss for gradient accumulation
            if self.gradient_accumulation_steps > 1:
                loss = loss / self.gradient_accumulation_steps

        # Backward pass with gradient scaling for FP16
        self.scaler.scale(loss).backward()

        # Record gradient norms if verbose
        if self.verbose and self.global_step % self.log_every_n_steps == 0:
            grad_norm = self._compute_grad_norm()
            self.gradient_norms.append((self.global_step, grad_norm))

        # Optimizer step if we've accumulated enough gradients
        if (self.global_step + 1) % self.gradient_accumulation_steps == 0:
            # Clip gradients
            if self.config.grad_clip is not None:
                self.scaler.unscale_(self.optimizer)
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(),
                    self.config.grad_clip
                )

            # Optimizer step with gradient scaling
            self.scaler.step(self.optimizer)
            self.scaler.update()
            self.optimizer.zero_grad(set_to_none=True)

            # Learning rate scheduler step
            if self.lr_scheduler is not None:
                self.lr_scheduler.step()

            # Track learning rate
            if self.verbose:
                current_lr = self.optimizer.param_groups[0]["lr"]
                self.learning_rates.append((self.global_step, current_lr))

        # Increment global step
        self.global_step += 1

        # Convert to Python float for logging
        loss_value = loss.item() * (self.gradient_accumulation_steps if self.gradient_accumulation_steps > 1 else 1)

        # Track loss
        self.train_losses.append((self.global_step, loss_value))

        # Take memory snapshot if verbose
        if self.verbose and self.global_step % self.log_every_n_steps == 0:
            if torch.cuda.is_available():
                memory_info = {
                    "step": self.global_step,
                    "allocated_gb": torch.cuda.memory_allocated() / (1024**3),
                    "reserved_gb": torch.cuda.memory_reserved() / (1024**3)
                }
                self.memory_metrics.append(memory_info)

        return {
            "loss": loss_value,
            "lr": self.optimizer.param_groups[0]["lr"]
        }

    @torch.no_grad()
    @measure_latency("mixed_precision_evaluation")
    # TODO: Implement measure_throughput decorator
    # @measure_throughput(batch_size_param="batch_size", enabled_param="verbose")
    def evaluation_step(
        self,
        batch: Dict[str, torch.Tensor]
    ) -> Dict[str, Any]:
        """
        Perform a single evaluation step.

        Args:
            batch: Dictionary containing input tensors

        Returns:
            Dict[str, Any]: Dictionary with evaluation metrics
        """
        self.model.eval()

        # Move batch to device
        batch = {k: v.to(self.device) if hasattr(v, "to") else v for k, v in batch.items()}

        # Skip if empty batch
        if not batch:
            logger.warning("Skipping empty batch")
            return {"loss": 0.0}

        # Prepare inputs based on model type
        inputs = batch

        # Use autocast for mixed precision
        with autocast(enabled=self.config.enabled, dtype=self.config.compute_dtype):
            # Forward pass
            outputs = self.model(**inputs)
            loss = outputs.loss if hasattr(outputs, "loss") else outputs["loss"]

        # Convert to Python float for logging
        loss_value = loss.item()

        return {
            "loss": loss_value
        }

    def train(
        self,
        train_dataloader: DataLoader,
        eval_dataloader: Optional[DataLoader] = None,
        num_epochs: int = 3,
        max_steps: Optional[int] = None,
        eval_steps: Optional[int] = None,
        callbacks: Optional[List[Callable]] = None
    ) -> Dict[str, Any]:
        """
        Train the model using mixed precision.

        Args:
            train_dataloader: DataLoader for training data
            eval_dataloader: Optional DataLoader for evaluation data
            num_epochs: Number of training epochs
            max_steps: Maximum number of training steps (overrides num_epochs)
            eval_steps: How often to evaluate (defaults to config.eval_every_n_steps)
            callbacks: Optional list of callback functions

        Returns:
            Dict[str, Any]: Dictionary with training results
        """
        # Setup callbacks
        callbacks = callbacks or []

        # Setup evaluation steps
        eval_steps = eval_steps or self.eval_every_n_steps

        # Create progress tracking variables
        start_time = time.time()
        self.global_step = 0
        self.epoch = 0
        total_train_loss = 0
        epoch_loss = 0
        steps_in_epoch = 0
        best_eval_loss = float('inf')
        training_finished = False

        # Log starting memory stats
        if torch.cuda.is_available():
            memory_stats = get_memory_stats(self.model)
            logger.info(f"Initial memory usage: {memory_stats['allocated_memory_gb']:.2f} GB allocated")

        logger.info(f"Starting training with {self.config.compute_dtype} compute dtype")
        logger.info(f"Training for {num_epochs} epochs or {max_steps} steps")

        # Training loop
        for epoch in range(num_epochs):
            self.epoch = epoch
            epoch_start_time = time.time()
            epoch_loss = 0
            steps_in_epoch = 0

            # Epoch loop
            for step, batch in enumerate(train_dataloader):
                # Perform training step
                step_result = self.training_step(batch)

                # Update tracking variables
                epoch_loss += step_result["loss"]
                total_train_loss += step_result["loss"]
                steps_in_epoch += 1

                # Log progress
                if self.global_step % self.log_every_n_steps == 0:
                    current_lr = self.optimizer.param_groups[0]["lr"]
                    avg_loss = epoch_loss / steps_in_epoch
                    elapsed = time.time() - start_time
                    logger.info(
                        f"Epoch: {epoch+1}/{num_epochs} | "
                        f"Step: {self.global_step} | "
                        f"Loss: {step_result['loss']:.4f} | "
                        f"Avg Loss: {avg_loss:.4f} | "
                        f"LR: {current_lr:.7f} | "
                        f"Elapsed: {elapsed:.2f}s"
                    )

                    # Check for NaN or Inf values in parameters if verbose
                    if self.verbose:
                        check_results = check_nan_or_inf(self.model)
                        if check_results["has_nan"] or check_results["has_inf"]:
                            logger.warning(f"Found {check_results['problematic_count']} parameters with NaN/Inf values")

                # Run evaluation if needed
                if eval_dataloader is not None and self.global_step % eval_steps == 0:
                    eval_metrics = self.evaluate(eval_dataloader)
                    eval_loss = eval_metrics["loss"]

                    # Log evaluation results
                    logger.info(
                        f"Evaluation | "
                        f"Step: {self.global_step} | "
                        f"Loss: {eval_loss:.4f}"
                    )

                    # Track best model and save checkpoint
                    if eval_loss < best_eval_loss:
                        best_eval_loss = eval_loss
                        logger.info(f"New best evaluation loss: {best_eval_loss:.4f}")

                        # Save best model checkpoint
                        self.save_checkpoint(
                            os.path.join(self.output_dir, "best_model"),
                            {"eval_loss": best_eval_loss}
                        )

                # Save regular checkpoint if needed
                if self.global_step % self.save_every_n_steps == 0:
                    self.save_checkpoint(
                        os.path.join(self.output_dir, f"checkpoint-{self.global_step}"),
                        {"train_loss": epoch_loss / steps_in_epoch if steps_in_epoch > 0 else 0}
                    )

                # Run callbacks
                for callback in callbacks:
                    callback(
                        trainer=self,
                        model=self.model,
                        step=self.global_step,
                        epoch=epoch,
                        metrics={"loss": step_result["loss"], "lr": step_result["lr"]}
                    )

                # Check if we've reached max steps
                if max_steps is not None and self.global_step >= max_steps:
                    logger.info(f"Reached maximum number of steps: {max_steps}")
                    training_finished = True
                    break

            # End of epoch
            epoch_time = time.time() - epoch_start_time
            epoch_avg_loss = epoch_loss / steps_in_epoch if steps_in_epoch > 0 else 0

            logger.info(
                f"Epoch {epoch+1}/{num_epochs} finished in {epoch_time:.2f}s | "
                f"Average Loss: {epoch_avg_loss:.4f}"
            )

            # Save epoch checkpoint
            self.save_checkpoint(
                os.path.join(self.output_dir, f"epoch-{epoch+1}"),
                {"train_loss": epoch_avg_loss}
            )

            if training_finished:
                break

        # Calculate training time
        total_time = time.time() - start_time

        # Final evaluation
        final_eval_metrics = {}
        if eval_dataloader is not None:
            final_eval_metrics = self.evaluate(eval_dataloader)
            logger.info(f"Final evaluation loss: {final_eval_metrics['loss']:.4f}")

        # Save final model
        self.save_checkpoint(
            os.path.join(self.output_dir, "final_model"),
            {"train_loss": total_train_loss / self.global_step if self.global_step > 0 else 0}
        )

        # Generate training summary
        train_summary = {
            "total_steps": self.global_step,
            "total_epochs": self.epoch + 1,
            "best_eval_loss": best_eval_loss,
            "final_eval_loss": final_eval_metrics.get("loss", None),
            "train_loss": total_train_loss / self.global_step if self.global_step > 0 else 0,
            "train_time_seconds": total_time,
            "train_time_minutes": total_time / 60,
            "steps_per_second": self.global_step / total_time if total_time > 0 else 0,
            "mixed_precision_enabled": self.config.enabled,
            "compute_dtype": str(self.config.compute_dtype),
            "param_dtype": str(self.config.param_dtype),
        }

        # Save training summary
        with open(os.path.join(self.output_dir, "training_summary.json"), "w") as f:
            json.dump(train_summary, f, indent=2)

        logger.info(f"Training completed in {total_time:.2f}s ({total_time/60:.2f}m)")
        logger.info(f"Final training loss: {train_summary['train_loss']:.4f}")

        return train_summary

    @torch.no_grad()
    def evaluate(self, dataloader: DataLoader) -> Dict[str, Any]:
        """
        Evaluate the model on a dataset.

        Args:
            dataloader: DataLoader for evaluation data

        Returns:
            Dict[str, Any]: Dictionary with evaluation metrics
        """
        self.model.eval()

        # Initialize metrics
        total_loss = 0
        total_steps = 0

        # Evaluation loop
        for batch in dataloader:
            # Perform evaluation step
            step_result = self.evaluation_step(batch)

            # Update metrics
            total_loss += step_result["loss"]
            total_steps += 1

        # Calculate average loss
        avg_loss = total_loss / total_steps if total_steps > 0 else 0

        # Track evaluation loss
        self.eval_losses.append((self.global_step, avg_loss))

        return {
            "loss": avg_loss,
            "steps": total_steps
        }

    @torch.no_grad()
    def generate(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        **generate_kwargs
    ) -> torch.Tensor:
        """
        Generate text using the model.

        Args:
            input_ids: Input token IDs
            attention_mask: Optional attention mask
            **generate_kwargs: Additional generation parameters

        Returns:
            torch.Tensor: Generated token IDs
        """
        self.model.eval()

        # Move inputs to device
        input_ids = input_ids.to(self.device)
        if attention_mask is not None:
            attention_mask = attention_mask.to(self.device)

        # Prepare inputs for generation
        inputs = {"input_ids": input_ids}
        if attention_mask is not None:
            inputs["attention_mask"] = attention_mask

        # Use autocast for mixed precision
        with autocast(enabled=self.config.enabled, dtype=self.config.compute_dtype):
            # Generate tokens
            if hasattr(self.model, "generate"):
                outputs = self.model.generate(
                    **inputs,
                    **generate_kwargs
                )
            else:
                raise ValueError("Model does not have a generate method")

        return outputs

    def _compute_grad_norm(self) -> float:
        """
        Compute gradient L2 norm across all parameters.

        Returns:
            float: Gradient L2 norm
        """
        total_norm = 0.0
        for p in self.model.parameters():
            if p.grad is not None:
                param_norm = p.grad.data.norm(2).item()
                total_norm += param_norm ** 2

        total_norm = total_norm ** 0.5
        return total_norm

    def get_lr(self) -> float:
        """
        Get current learning rate.

        Returns:
            float: Current learning rate
        """
        return self.optimizer.param_groups[0]["lr"]

    def get_memory_usage(self) -> Dict[str, float]:
        """
        Get current memory usage.

        Returns:
            Dict[str, float]: Dictionary with memory usage information
        """
        if not torch.cuda.is_available():
            return {"error": "CUDA not available"}

        return {
            "allocated_gb": torch.cuda.memory_allocated() / (1024**3),
            "reserved_gb": torch.cuda.memory_reserved() / (1024**3),
            "max_allocated_gb": torch.cuda.max_memory_allocated() / (1024**3)
        }

    def get_training_state(self) -> Dict[str, Any]:
        """
        Get current training state.

        Returns:
            Dict[str, Any]: Dictionary with training state
        """
        return {
            "epoch": self.epoch,
            "global_step": self.global_step,
            "train_losses": self.train_losses,
            "eval_losses": self.eval_losses,
            "learning_rates": self.learning_rates,
            "best_loss": self.best_loss,
            "memory_metrics": self.memory_metrics,
            "gradient_norms": self.gradient_norms,
            "throughput_metrics": self.throughput_metrics
        }

    def plot_training_metrics(self, output_path: Optional[str] = None) -> None:
        """
        Plot training metrics.

        Args:
            output_path: Optional path to save the plot
        """
        try:
            import matplotlib.pyplot as plt
            import numpy as np

            # Create figure
            fig, axs = plt.subplots(2, 2, figsize=(15, 10))

            # Plot training loss
            if self.train_losses:
                steps, losses = zip(*self.train_losses)
                axs[0, 0].plot(steps, losses)
                axs[0, 0].set_title("Training Loss")
                axs[0, 0].set_xlabel("Step")
                axs[0, 0].set_ylabel("Loss")

            # Plot evaluation loss
            if self.eval_losses:
                steps, losses = zip(*self.eval_losses)
                axs[0, 1].plot(steps, losses)
                axs[0, 1].set_title("Evaluation Loss")
                axs[0, 1].set_xlabel("Step")
                axs[0, 1].set_ylabel("Loss")

            # Plot learning rate
            if self.learning_rates:
                steps, lrs = zip(*self.learning_rates)
                axs[1, 0].plot(steps, lrs)
                axs[1, 0].set_title("Learning Rate")
                axs[1, 0].set_xlabel("Step")
                axs[1, 0].set_ylabel("Learning Rate")

            # Plot memory usage
            if self.memory_metrics:
                steps = [m["step"] for m in self.memory_metrics]
                allocated = [m["allocated_gb"] for m in self.memory_metrics]
                reserved = [m["reserved_gb"] for m in self.memory_metrics]

                axs[1, 1].plot(steps, allocated, label="Allocated")
                axs[1, 1].plot(steps, reserved, label="Reserved")
                axs[1, 1].set_title("GPU Memory Usage")
                axs[1, 1].set_xlabel("Step")
                axs[1, 1].set_ylabel("Memory (GB)")
                axs[1, 1].legend()

            plt.tight_layout()

            # Save or show the plot
            if output_path:
                plt.savefig(output_path)
                logger.info(f"Saved metrics plot to {output_path}")
            else:
                plt.show()

        except ImportError:
            logger.warning("Matplotlib not available for plotting metrics")

        except Exception as e:
            logger.error(f"Error plotting metrics: {str(e)}")

    def save_training_metrics(self, output_path: Optional[str] = None) -> None:
        """
        Save training metrics to a JSON file.

        Args:
            output_path: Optional path to save the metrics
        """
        if output_path is None:
            output_path = os.path.join(self.output_dir, "training_metrics.json")

        metrics = self.get_training_state()

        with open(output_path, "w") as f:
            json.dump(metrics, f, indent=2)

        logger.info(f"Saved training metrics to {output_path}")