"""
Mixed precision training module.

This module provides components for training models with mixed precision,
which allows for faster training and reduced memory usage by using lower
precision formats (FP16 or BF16) where appropriate while maintaining model accuracy.
"""

from deep_research_core.optimization.mixed_precision.trainer import MixedPrecisionTrainer, AutocastConfig
from deep_research_core.optimization.mixed_precision.utils import (
    fp16_to_bp16,
    get_mixed_precision_policy,
    get_model_size_in_bytes,
    estimate_memory_savings,
    is_mixed_precision_beneficial,
    get_optimal_batch_size
)

__all__ = [
    'MixedPrecisionTrainer',
    'AutocastConfig',
    'fp16_to_bp16',
    'get_mixed_precision_policy',
    'get_model_size_in_bytes',
    'estimate_memory_savings',
    'is_mixed_precision_beneficial',
    'get_optimal_batch_size'
] 