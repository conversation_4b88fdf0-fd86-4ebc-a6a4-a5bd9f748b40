"""
Configuration for mixed-precision training.

This module provides the configuration class for mixed-precision training
settings and parameters.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional, Union, Tuple
import torch

from ...utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

@dataclass
class MixedPrecisionConfig:
    """
    Configuration for mixed-precision training settings.
    
    This class manages the configuration settings for mixed-precision training,
    including dtype selection, gradient scaling, and other optimization parameters.
    """
    
    # Computational dtype for forward/backward passes
    compute_dtype: torch.dtype = torch.float16
    
    # Parameter storage dtype (can be different from compute_dtype)
    param_dtype: Optional[torch.dtype] = None
    
    # Whether to enable automatic mixed precision
    enabled: bool = True
    
    # Initial loss scale for gradient scaling
    initial_scale: float = 65536.0
    
    # Growth factor for dynamic loss scaling
    growth_factor: float = 2.0
    
    # Backoff factor for dynamic loss scaling
    backoff_factor: float = 0.5
    
    # Growth interval for dynamic loss scaling
    growth_interval: int = 1000
    
    # Whether to use dynamic loss scaling
    dynamic_loss_scaling: bool = True
    
    # Gradient clipping value (if None, no clipping is applied)
    grad_clip: Optional[float] = None
    
    # Minimum scale allowed for dynamic loss scaling
    min_scale: float = 1.0
    
    # Maximum scale allowed for dynamic loss scaling
    max_scale: float = 2**24
    
    # Whether to enable custom memory-efficient operations
    memory_efficient: bool = True
    
    # CPU offloading configuration (offload optimizer states to CPU)
    cpu_offload: bool = False
    
    # Custom memory-efficient optimizations
    custom_optimizations: Dict[str, bool] = field(default_factory=lambda: {
        "fused_optimizer": True,
        "fused_layernorm": True,
        "reduce_scatter": True,
        "flash_attention": True
    })
    
    # Whether to use distributed gradient synchronization
    use_distributed_gradient_sync: bool = True
    
    # Minimum size for gradient all-reduce operations
    min_reduce_size: int = 0
    
    def __post_init__(self):
        """Validate configuration after initialization."""
        # If param_dtype is not set, use compute_dtype
        if self.param_dtype is None:
            self.param_dtype = self.compute_dtype
            
        # Log the configuration
        logger.info(f"Initializing mixed-precision training with {self.compute_dtype} compute dtype")
        if self.param_dtype != self.compute_dtype:
            logger.info(f"Using different parameter storage dtype: {self.param_dtype}")
            
        # Check for DTU (A100, H100) availability for BF16 training
        if self.compute_dtype == torch.bfloat16 and torch.cuda.is_available():
            device_capability = torch.cuda.get_device_capability()
            device_name = torch.cuda.get_device_name()
            
            if device_capability[0] < 8:  # A100/H100 are SM 8.0+
                logger.warning(
                    f"BFloat16 requested but device {device_name} may not have hardware acceleration for BF16. "
                    "This could lead to slower training. Consider using Float16 instead."
                )
    
    def is_compatible_with_device(self) -> bool:
        """
        Check if the configuration is compatible with the current device.
        
        Returns:
            bool: True if the configuration is compatible, False otherwise
        """
        if not torch.cuda.is_available() and self.compute_dtype != torch.float32:
            logger.warning("Mixed precision requested but CUDA is not available. Falling back to FP32.")
            return False
            
        if self.compute_dtype == torch.float16 and torch.cuda.is_available():
            # All CUDA GPUs support FP16, but check capabilities for tensor cores
            device_capability = torch.cuda.get_device_capability()
            if device_capability[0] < 7:  # Volta (SM 7.0) introduced tensor cores
                logger.warning(
                    "FP16 training without tensor cores may not give optimal performance. "
                    "Consider using a Volta architecture (SM 7.0) or higher GPU."
                )
                
        return True
    
    def get_apex_optimization_level(self) -> Optional[str]:
        """
        Get the Apex AMP optimization level corresponding to this configuration.
        
        Returns:
            Optional[str]: The Apex optimization level (O1, O2, O3) or None if not applicable
        """
        if not self.enabled:
            return None
            
        if self.compute_dtype == torch.float16:
            if self.param_dtype == torch.float32:
                return "O1"  # Mixed precision: FP16 for some operations, FP32 for others
            elif self.param_dtype == torch.float16:
                return "O2"  # Mixed precision: FP16 for most operations
                
            return "O3"  # FP16 for everything
            
        return None  # Not applicable for BF16 or other dtypes
    
    def get_accelerate_config(self) -> Dict[str, Any]:
        """
        Get configuration dictionary for HuggingFace Accelerate library.
        
        Returns:
            Dict[str, Any]: Configuration for Accelerate
        """
        fp16 = self.compute_dtype == torch.float16
        bf16 = self.compute_dtype == torch.bfloat16
        
        return {
            "fp16": fp16,
            "bf16": bf16,
            "cpu_offload": self.cpu_offload,
            "gradient_accumulation_steps": 1,  # Default, should be overridden by trainer
            "gradient_clipping": self.grad_clip if self.grad_clip is not None else 0.0,
            "mixed_precision": "fp16" if fp16 else "bf16" if bf16 else "no",
        }
    
    def get_deepspeed_config(self) -> Dict[str, Any]:
        """
        Get configuration dictionary for DeepSpeed.
        
        Returns:
            Dict[str, Any]: Configuration for DeepSpeed
        """
        fp16 = self.compute_dtype == torch.float16
        bf16 = self.compute_dtype == torch.bfloat16
        
        config = {
            "train_batch_size": 1,  # Placeholder, should be set by trainer
            "gradient_clipping": self.grad_clip if self.grad_clip is not None else 0.0,
            "zero_optimization": {
                "stage": 2 if self.memory_efficient else 1,
                "offload_optimizer": {
                    "device": "cpu" if self.cpu_offload else "none"
                },
                "contiguous_gradients": True,
                "overlap_comm": True,
                "reduce_scatter": self.custom_optimizations.get("reduce_scatter", True),
                "reduce_bucket_size": 5e8
            }
        }
        
        if fp16:
            config["fp16"] = {
                "enabled": True,
                "loss_scale": self.initial_scale if not self.dynamic_loss_scaling else 0,
                "loss_scale_window": self.growth_interval,
                "initial_scale_power": 16,  # 2^16 = 65536
                "hysteresis": 2,
                "min_loss_scale": self.min_scale
            }
        
        if bf16:
            config["bf16"] = {
                "enabled": True
            }
            
        return config
            
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the configuration to a dictionary.
        
        Returns:
            Dict[str, Any]: Dictionary representation of the configuration
        """
        # Convert dtype objects to strings for serialization
        result = {k: v for k, v in self.__dict__.items()}
        
        # Special handling for torch.dtype which is not directly serializable
        if 'compute_dtype' in result:
            result['compute_dtype'] = str(result['compute_dtype']).split('.')[-1]
        
        if 'param_dtype' in result and result['param_dtype'] is not None:
            result['param_dtype'] = str(result['param_dtype']).split('.')[-1]
            
        return result
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'MixedPrecisionConfig':
        """
        Create a configuration from a dictionary.
        
        Args:
            config_dict: Dictionary with configuration values
            
        Returns:
            MixedPrecisionConfig: New configuration instance
        """
        # Handle dtype fields
        if 'compute_dtype' in config_dict:
            dtype_str = config_dict['compute_dtype']
            if isinstance(dtype_str, str):
                if 'bfloat16' in dtype_str:
                    config_dict['compute_dtype'] = torch.bfloat16
                elif 'float16' in dtype_str:
                    config_dict['compute_dtype'] = torch.float16
                elif 'float32' in dtype_str:
                    config_dict['compute_dtype'] = torch.float32
                    
        if 'param_dtype' in config_dict and config_dict['param_dtype'] is not None:
            dtype_str = config_dict['param_dtype']
            if isinstance(dtype_str, str):
                if 'bfloat16' in dtype_str:
                    config_dict['param_dtype'] = torch.bfloat16
                elif 'float16' in dtype_str:
                    config_dict['param_dtype'] = torch.float16
                elif 'float32' in dtype_str:
                    config_dict['param_dtype'] = torch.float32
                    
        return cls(**config_dict)


def get_default_mixed_precision_config(device: Optional[str] = None) -> MixedPrecisionConfig:
    """
    Get the default mixed-precision configuration for the current device.
    
    Args:
        device: Device string ('cuda', 'cpu', etc.) or None to detect automatically
        
    Returns:
        MixedPrecisionConfig: Optimal mixed-precision configuration
    """
    if device is None:
        device = "cuda" if torch.cuda.is_available() else "cpu"
        
    if device == "cpu":
        # CPU doesn't support mixed precision well, use FP32
        return MixedPrecisionConfig(
            compute_dtype=torch.float32,
            param_dtype=torch.float32,
            enabled=False
        )
        
    # For CUDA devices, check capabilities
    if device.startswith("cuda") and torch.cuda.is_available():
        device_capability = torch.cuda.get_device_capability()
        device_name = torch.cuda.get_device_name()
        
        # A100/H100 GPUs (SM 8.0+) have good BF16 support
        if device_capability[0] >= 8:
            logger.info(f"Detected {device_name} with SM {device_capability[0]}.{device_capability[1]}. Using BFloat16.")
            return MixedPrecisionConfig(
                compute_dtype=torch.bfloat16,
                param_dtype=torch.bfloat16,
                enabled=True,
                memory_efficient=True
            )
            
        # Volta/Turing/Ampere GPUs (SM 7.0-7.5) have good FP16 support
        if device_capability[0] == 7:
            logger.info(f"Detected {device_name} with SM 7.x. Using Float16.")
            return MixedPrecisionConfig(
                compute_dtype=torch.float16,
                param_dtype=torch.float16,
                enabled=True,
                memory_efficient=True
            )
            
        # Older GPUs - use FP16 with some caution
        logger.info(f"Detected {device_name} with SM {device_capability[0]}.{device_capability[1]}. "
                   "Using Float16 with conservative settings.")
        return MixedPrecisionConfig(
            compute_dtype=torch.float16,
            param_dtype=torch.float32,  # More conservative: store master weights in FP32
            enabled=True,
            dynamic_loss_scaling=True,
            memory_efficient=False
        )
        
    # Default fallback
    logger.warning(f"Unknown device type: {device}. Using Float32 (no mixed precision).")
    return MixedPrecisionConfig(
        compute_dtype=torch.float32,
        param_dtype=torch.float32,
        enabled=False
    ) 