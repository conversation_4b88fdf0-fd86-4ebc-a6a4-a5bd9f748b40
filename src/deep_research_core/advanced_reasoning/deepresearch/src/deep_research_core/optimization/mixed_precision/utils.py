"""
Utility functions for mixed precision training.

This module provides utility functions to support mixed precision training, including
functions to determine the appropriate precision policy, convert between precision formats,
estimate memory usage, and determine optimal batch sizes.
"""

import os
import sys
import time
import json
import math
from typing import Dict, Any, List, Optional, Union, Tuple
import logging

import torch
import torch.nn as nn

from ...utils.structured_logging import get_logger
from ...utils.performance_metrics import measure_latency, measure_memory_usage_decorator

# Create a logger
logger = get_logger(__name__)

def get_optimal_dtype(
    model_size_params: int = 0,
    available_memory_gb: Optional[float] = None,
    device: Optional[str] = None,
    allow_bf16: bool = True
) -> torch.dtype:
    """
    Determine the optimal dtype based on model size and available hardware.

    Args:
        model_size_params: Number of parameters in the model
        available_memory_gb: Available GPU memory in GB (None to auto-detect)
        device: Device string ('cuda', 'cpu', etc.) or None to detect automatically
        allow_bf16: Whether to allow bfloat16 dtype

    Returns:
        torch.dtype: The optimal dtype for the model and hardware
    """
    if device is None:
        device = "cuda" if torch.cuda.is_available() else "cpu"

    # CPU doesn't support mixed precision well, use FP32
    if device == "cpu":
        return torch.float32

    # If CUDA is available, check capabilities
    if device.startswith("cuda") and torch.cuda.is_available():
        # Get device properties
        device_capability = torch.cuda.get_device_capability()
        device_name = torch.cuda.get_device_name()

        # Calculate available memory if not provided
        if available_memory_gb is None:
            try:
                available_memory_bytes = torch.cuda.get_device_properties(0).total_memory
                available_memory_gb = available_memory_bytes / (1024**3)
            except:
                # Fallback estimate
                available_memory_gb = 8.0  # Assume 8GB as a conservative estimate

        # Calculate model memory requirements (rough estimate)
        model_memory_fp32_gb = model_size_params * 4 / (1024**3)  # 4 bytes per parameter
        model_memory_fp16_gb = model_size_params * 2 / (1024**3)  # 2 bytes per parameter

        # Check if model fits in memory with different dtypes
        fits_in_fp32 = model_memory_fp32_gb * 1.5 < available_memory_gb  # 1.5x for optimizer states
        fits_in_fp16 = model_memory_fp16_gb * 1.5 < available_memory_gb

        # A100/H100 GPUs (SM 8.0+) have good BF16 support
        if device_capability[0] >= 8 and allow_bf16:
            logger.info(f"Detected {device_name} with SM {device_capability[0]}.{device_capability[1]}. BFloat16 is optimal.")
            return torch.bfloat16

        # Volta/Turing/Ampere GPUs (SM 7.0-7.5) have good FP16 support
        if device_capability[0] == 7:
            # For large models that don't fit well in memory, use FP16
            if not fits_in_fp32 and fits_in_fp16:
                logger.info(f"Detected {device_name}. Large model detected, using Float16 for memory efficiency.")
                return torch.float16

            # For models that fit in memory, use FP32 for older GPUs, FP16 for newer
            if device_capability[1] >= 5:  # Ampere (SM 7.5)
                logger.info(f"Detected {device_name} with SM 7.5+. Float16 is optimal.")
                return torch.float16
            else:
                logger.info(f"Detected {device_name} with SM 7.0-7.4. Using Float32 for stability.")
                return torch.float32

        # Older GPUs - use FP32 unless memory is a concern
        if not fits_in_fp32 and fits_in_fp16:
            logger.warning(f"Model too large for FP32 on {device_name}. Using Float16 with caution.")
            return torch.float16

        logger.info(f"Using Float32 for {device_name} to ensure training stability.")
        return torch.float32

    # Default fallback
    return torch.float32

def optimize_memory_efficiency(model: nn.Module, dtype: torch.dtype = torch.float16) -> nn.Module:
    """
    Apply memory efficiency optimizations to a model.

    Args:
        model: The PyTorch model to optimize
        dtype: The dtype to use for optimization

    Returns:
        nn.Module: The optimized model
    """
    # Check if we should apply optimizations
    if dtype == torch.float32 or not torch.cuda.is_available():
        logger.info("Skipping memory optimizations for FP32 or non-CUDA setup")
        return model

    logger.info(f"Applying memory efficiency optimizations for {dtype}")

    # Apply gradient checkpointing if available
    if hasattr(model, "gradient_checkpointing_enable"):
        logger.info("Enabling gradient checkpointing")
        model.gradient_checkpointing_enable()
    elif hasattr(model, "enable_input_require_grads"):
        # Manual gradient checkpointing for models that support it
        logger.info("Enabling manual gradient checkpointing")
        model.enable_input_require_grads()

    # Convert to target dtype
    if dtype in [torch.float16, torch.bfloat16]:
        logger.info(f"Converting model to {dtype}")
        model = model.to(dtype=dtype)

    # Apply fused operations where available
    if dtype == torch.float16 and hasattr(model, "config"):
        if hasattr(model.config, "use_cache"):
            # Disable KV cache during training for memory efficiency
            logger.info("Disabling KV cache for training")
            model.config.use_cache = False

    # Return the optimized model
    return model

@measure_memory_usage_decorator()
def get_memory_stats(model: Optional[nn.Module] = None) -> Dict[str, Any]:
    """
    Get detailed memory statistics for the current device and model.

    Args:
        model: Optional model to include in statistics

    Returns:
        Dict[str, Any]: Dictionary with memory statistics
    """
    stats = {}

    # Get CUDA memory stats if available
    if torch.cuda.is_available():
        stats["cuda_available"] = True
        stats["cuda_device_count"] = torch.cuda.device_count()
        stats["cuda_current_device"] = torch.cuda.current_device()

        # Get memory stats for current device
        current_device = torch.cuda.current_device()
        stats["cuda_device_name"] = torch.cuda.get_device_name(current_device)

        # Memory stats
        stats["allocated_memory_bytes"] = torch.cuda.memory_allocated()
        stats["allocated_memory_gb"] = torch.cuda.memory_allocated() / (1024**3)
        stats["reserved_memory_bytes"] = torch.cuda.memory_reserved()
        stats["reserved_memory_gb"] = torch.cuda.memory_reserved() / (1024**3)
        stats["max_memory_allocated_bytes"] = torch.cuda.max_memory_allocated()
        stats["max_memory_allocated_gb"] = torch.cuda.max_memory_allocated() / (1024**3)

        # Get device properties
        device_props = torch.cuda.get_device_properties(current_device)
        stats["total_memory_bytes"] = device_props.total_memory
        stats["total_memory_gb"] = device_props.total_memory / (1024**3)
        stats["cuda_compute_capability"] = f"{device_props.major}.{device_props.minor}"

        # Calculate free memory
        stats["free_memory_bytes"] = device_props.total_memory - torch.cuda.memory_allocated()
        stats["free_memory_gb"] = stats["free_memory_bytes"] / (1024**3)
        stats["free_memory_percentage"] = 100 * stats["free_memory_bytes"] / device_props.total_memory
    else:
        stats["cuda_available"] = False

    # Include model stats if provided
    if model is not None:
        # Get model size in parameters
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

        stats["model_total_parameters"] = total_params
        stats["model_trainable_parameters"] = trainable_params
        stats["model_frozen_parameters"] = total_params - trainable_params
        stats["model_trainable_percentage"] = 100 * trainable_params / total_params if total_params > 0 else 0

        # Estimate model memory usage (rough approximation)
        param_size_bytes = 0
        param_dtype = None

        # Get parameter dtype and size
        for param in model.parameters():
            if param_dtype is None:
                param_dtype = param.dtype

            if param.dtype == torch.float32:
                param_size_bytes += param.numel() * 4
            elif param.dtype in [torch.float16, torch.bfloat16]:
                param_size_bytes += param.numel() * 2
            else:
                # Default estimate
                param_size_bytes += param.numel() * param.element_size()

        stats["model_param_dtype"] = str(param_dtype)
        stats["model_param_size_bytes"] = param_size_bytes
        stats["model_param_size_gb"] = param_size_bytes / (1024**3)

        # Estimate optimizer memory (assuming Adam)
        optimizer_size_bytes = param_size_bytes * 4  # Adam uses ~4x param size
        stats["model_optimizer_size_bytes"] = optimizer_size_bytes
        stats["model_optimizer_size_gb"] = optimizer_size_bytes / (1024**3)

        # Total training memory estimate (model + optimizer + gradients + activations)
        activations_estimate_bytes = param_size_bytes * 0.5  # Rough estimate for activations
        total_estimate_bytes = param_size_bytes + optimizer_size_bytes + param_size_bytes + activations_estimate_bytes

        stats["model_total_training_size_bytes"] = total_estimate_bytes
        stats["model_total_training_size_gb"] = total_estimate_bytes / (1024**3)

    return stats

def check_nan_or_inf(model: nn.Module) -> Dict[str, Any]:
    """
    Check model parameters for NaN or Inf values.

    Args:
        model: The PyTorch model to check

    Returns:
        Dict[str, Any]: Dictionary with check results
    """
    results = {
        "has_nan": False,
        "has_inf": False,
        "problematic_params": []
    }

    for name, param in model.named_parameters():
        if param.requires_grad:
            # Check for NaN
            if torch.isnan(param).any():
                results["has_nan"] = True
                results["problematic_params"].append({
                    "name": name,
                    "issue": "NaN",
                    "shape": list(param.shape)
                })

            # Check for Inf
            if torch.isinf(param).any():
                results["has_inf"] = True
                results["problematic_params"].append({
                    "name": name,
                    "issue": "Inf",
                    "shape": list(param.shape)
                })

    # Add summary
    results["total_params_checked"] = sum(1 for p in model.parameters() if p.requires_grad)
    results["problematic_count"] = len(results["problematic_params"])

    return results

def apply_mixed_precision_compatibility_fixes(
    model: nn.Module,
    dtype: torch.dtype = torch.float16
) -> nn.Module:
    """
    Apply compatibility fixes for mixed-precision training.

    Args:
        model: The PyTorch model to modify
        dtype: The target dtype for mixed-precision training

    Returns:
        nn.Module: The modified model
    """
    if dtype == torch.float32:
        # No fixes needed for FP32
        return model

    # Find and modify any layers that need special handling
    for module in model.modules():
        # Handle layer norm and related layers
        if isinstance(module, (nn.LayerNorm, nn.GroupNorm)):
            # Keep LayerNorm in float32 for stability
            module.to(torch.float32)

        # Handle embedding layers
        if isinstance(module, nn.Embedding):
            # Embeddings can use target dtype but might need scaling adjustments
            if dtype == torch.float16:
                # Scale the embedding weights to avoid precision issues
                with torch.no_grad():
                    module.weight.data = module.weight.data.to(dtype)

    return model

def get_mixed_precision_policy() -> torch.dtype:
    """
    Determine the best mixed precision policy based on available hardware.

    Returns:
        torch.dtype: The recommended dtype for mixed precision training.
    """
    if torch.cuda.is_available():
        device_cap = torch.cuda.get_device_capability()
        if device_cap[0] >= 8:  # Ampere or newer (e.g., A100, H100)
            return torch.bfloat16 if hasattr(torch, 'bfloat16') else torch.float16
        elif device_cap[0] >= 7:  # Volta/Turing or newer (e.g., V100, T4)
            return torch.float16
        else:  # Older GPUs with less support for mixed precision
            return torch.float32
    else:
        # For CPU, bfloat16 is better supported than float16
        if hasattr(torch, 'bfloat16') and hasattr(torch, 'cpu') and hasattr(torch.cpu, 'is_available') and torch.cpu.is_available():
            return torch.bfloat16
        else:
            return torch.float32

def fp16_to_bp16(tensor: torch.Tensor) -> torch.Tensor:
    """
    Convert a float16 tensor to bfloat16.

    Args:
        tensor (torch.Tensor): The float16 tensor to convert.

    Returns:
        torch.Tensor: The converted bfloat16 tensor.
    """
    if not tensor.dtype == torch.float16:
        raise ValueError(f"Expected tensor of dtype torch.float16, got {tensor.dtype}")

    # Check if bfloat16 is available
    if not hasattr(torch, 'bfloat16'):
        logger.warning("bfloat16 not available in this PyTorch version, returning original tensor")
        return tensor

    # Convert to float32 first to avoid precision issues
    tensor_fp32 = tensor.to(torch.float32)

    # Then convert to bfloat16
    return tensor_fp32.to(torch.bfloat16)

def get_model_size_in_bytes(model: nn.Module) -> int:
    """
    Calculate the size of a PyTorch model in bytes.

    Args:
        model (nn.Module): The PyTorch model.

    Returns:
        int: The model size in bytes.
    """
    total_size = 0
    for name, param in model.named_parameters():
        if param.requires_grad:
            param_size = param.nelement() * param.element_size()
            total_size += param_size

    # Account for buffers (e.g., batch norm running mean/var)
    for name, buffer in model.named_buffers():
        buffer_size = buffer.nelement() * buffer.element_size()
        total_size += buffer_size

    return total_size

def estimate_memory_savings(model: nn.Module, target_dtype: torch.dtype = torch.float16) -> Dict[str, Any]:
    """
    Estimate memory savings from using mixed precision.

    Args:
        model (nn.Module): The PyTorch model.
        target_dtype (torch.dtype): The target data type for mixed precision.

    Returns:
        Dict[str, Any]: Dictionary with memory usage stats.
    """
    fp32_size = get_model_size_in_bytes(model)

    # Calculate estimated size in target dtype
    if target_dtype == torch.float16 or target_dtype == torch.bfloat16:
        # Half precision is 16 bits instead of 32 bits
        mixed_precision_size = fp32_size // 2
    else:
        mixed_precision_size = fp32_size

    savings = fp32_size - mixed_precision_size
    savings_percentage = (savings / fp32_size) * 100 if fp32_size > 0 else 0

    return {
        "fp32_size_bytes": fp32_size,
        "mixed_precision_size_bytes": mixed_precision_size,
        "savings_bytes": savings,
        "savings_percentage": savings_percentage,
        "fp32_size_mb": fp32_size / (1024 * 1024),
        "mixed_precision_size_mb": mixed_precision_size / (1024 * 1024),
        "savings_mb": savings / (1024 * 1024)
    }

def is_mixed_precision_beneficial(
    model: nn.Module,
    threshold_mb: float = 100,
    target_dtype: Optional[torch.dtype] = None
) -> bool:
    """
    Determine if mixed precision would be beneficial for a given model.

    Args:
        model (nn.Module): The PyTorch model.
        threshold_mb (float): Minimum model size in MB where mixed precision becomes beneficial.
        target_dtype (torch.dtype, optional): Target dtype for mixed precision.

    Returns:
        bool: True if mixed precision would be beneficial, False otherwise.
    """
    if target_dtype is None:
        _, target_dtype = get_mixed_precision_policy()

    stats = estimate_memory_savings(model, target_dtype)
    model_size_mb = stats["fp32_size_mb"]

    # Check if hardware supports efficient mixed precision
    if torch.cuda.is_available():
        device_cap = torch.cuda.get_device_capability()
        hardware_support = device_cap[0] >= 7  # Volta/Turing or newer
    else:
        hardware_support = hasattr(torch, 'bfloat16') and torch.cpu.is_available()

    return model_size_mb >= threshold_mb and hardware_support

def get_optimal_batch_size(
    model: nn.Module,
    input_shape: List[int],
    max_memory_gb: float = 0.8,
    dtype: torch.dtype = torch.float32,
    mixed_precision: bool = True
) -> int:
    """
    Estimate optimal batch size based on available GPU memory.

    Args:
        model (nn.Module): The PyTorch model.
        input_shape (List[int]): Shape of a single input (excluding batch dimension).
        max_memory_gb (float): Maximum fraction of GPU memory to use.
        dtype (torch.dtype): Data type for full precision calculation.
        mixed_precision (bool): Whether mixed precision is being used.

    Returns:
        int: Estimated optimal batch size.
    """
    if not torch.cuda.is_available():
        # For CPU, return a reasonable default
        return 32

    # Get total available GPU memory
    device = torch.cuda.current_device()
    total_memory = torch.cuda.get_device_properties(device).total_memory
    available_memory = total_memory * max_memory_gb

    # Estimate model memory usage
    model_size = get_model_size_in_bytes(model)
    if mixed_precision:
        # In mixed precision, model weights are kept in fp32, but activations are in fp16
        model_size_with_optimizer = model_size * 3  # Model + gradients + optimizer states
    else:
        model_size_with_optimizer = model_size * 4  # Fully in fp32

    # Estimate memory needed for a single sample (input + output + gradients)
    sample_size = 1
    for dim in input_shape:
        sample_size *= dim

    bytes_per_element = 2 if mixed_precision else 4
    memory_per_sample = sample_size * bytes_per_element * 3  # input + output + gradients

    # Calculate batch size
    remaining_memory = available_memory - model_size_with_optimizer

    if remaining_memory <= 0:
        logger.warning("Model is too large for GPU memory, consider using a smaller model or CPU")
        return 1

    # Estimate optimal batch size
    optimal_batch_size = max(1, int(remaining_memory / memory_per_sample))

    # It's often better to use powers of 2 for batch sizes
    batch_size = 2 ** int(math.log2(optimal_batch_size))

    logger.info(f"Estimated optimal batch size: {batch_size} (raw: {optimal_batch_size})")
    return batch_size

def measure_throughput(
    model: nn.Module,
    input_shape: List[int],
    batch_size: int,
    num_iterations: int = 100,
    warmup_iterations: int = 10,
    device: Optional[torch.device] = None,
    mixed_precision: bool = False
) -> Dict[str, float]:
    """
    Measure model throughput in samples per second.

    Args:
        model (nn.Module): The PyTorch model.
        input_shape (List[int]): Shape of a single input (excluding batch dimension).
        batch_size (int): Batch size to use for measurement.
        num_iterations (int): Number of iterations to run.
        warmup_iterations (int): Number of warmup iterations.
        device (torch.device, optional): Device to run the model on.
        mixed_precision (bool): Whether to use mixed precision.

    Returns:
        Dict[str, float]: Dictionary with throughput statistics.
    """
    import time

    if device is None:
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    model = model.to(device)
    model.eval()

    # Create dummy input
    input_shape = [batch_size] + input_shape
    dummy_input = torch.randn(*input_shape, device=device)

    # Warmup
    for _ in range(warmup_iterations):
        with torch.no_grad():
            if mixed_precision and device.type == 'cuda':
                with torch.cuda.amp.autocast():
                    _ = model(dummy_input)
            else:
                _ = model(dummy_input)

    # Sync before timing
    if device.type == 'cuda':
        torch.cuda.synchronize()

    # Measure throughput
    start_time = time.time()
    for _ in range(num_iterations):
        with torch.no_grad():
            if mixed_precision and device.type == 'cuda':
                with torch.cuda.amp.autocast():
                    _ = model(dummy_input)
            else:
                _ = model(dummy_input)

        # Sync after each iteration for accurate timing
        if device.type == 'cuda':
            torch.cuda.synchronize()

    end_time = time.time()

    elapsed_time = end_time - start_time
    samples_per_second = (batch_size * num_iterations) / elapsed_time
    ms_per_batch = (elapsed_time / num_iterations) * 1000

    return {
        "samples_per_second": samples_per_second,
        "batches_per_second": num_iterations / elapsed_time,
        "ms_per_batch": ms_per_batch,
        "batch_size": batch_size,
        "mixed_precision": mixed_precision,
        "device": str(device)
    }