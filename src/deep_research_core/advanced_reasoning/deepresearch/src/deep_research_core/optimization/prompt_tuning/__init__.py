"""
Prompt Tuning implementations for Deep Research Core.

This module provides implementations for fine-tuning models with Prompt Tuning
techniques, allowing parameter-efficient adaptation of large models by
adding trainable soft prompt tokens.
"""

from .base import BasePromptTuning
# TODO: Implement model adapters
# from .model_adapters import LlamaPromptModel, MistralPromptModel, GemmaPromptModel
# TODO: Implement training and evaluation
# from .training import PromptTuningTrainer
# from .evaluation import PromptTuningEvaluator

__all__ = [
    'BasePromptTuning',
    # 'LlamaPromptModel',
    # 'MistralPromptModel',
    # 'GemmaPromptModel',
    # 'PromptTuningTrainer',
    # 'PromptTuningEvaluator',
]