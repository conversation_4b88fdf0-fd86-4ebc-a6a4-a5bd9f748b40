"""
Base Prompt Tuning implementation.

This module provides the base class for Prompt Tuning implementations in Deep Research Core,
defining the interface and common functionality for applying Prompt Tuning
to various language models.
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, List, Optional, Union, Tuple
import logging
import time

import torch
import torch.nn as nn
from transformers import PreTrainedModel, AutoModelForCausalLM, AutoTokenizer

from ...utils.structured_logging import get_logger
from ...utils.performance_metrics import measure_latency

# Create a logger
logger = get_logger(__name__)

class BasePromptTuning:
    """
    Base class for Prompt Tuning fine-tuning.

    Prompt Tuning prepends a set of trainable soft prompt tokens to the input,
    allowing for efficient fine-tuning by only updating these prompt parameters
    while keeping the pre-trained model weights frozen.
    """

    def __init__(
        self,
        model_name: str,
        num_virtual_tokens: int = 30,
        prompt_init_type: str = "random",
        prompt_init_text: Optional[str] = None,
        tokenizer_name_or_path: Optional[str] = None,
        task_type: str = "CAUSAL_LM",
        output_dir: str = "./prompt_tuning_output",
        device: Optional[str] = None,
        verbose: bool = False,
        **kwargs
    ):
        """
        Initialize the BasePromptTuning.

        Args:
            model_name: Name or path of the base model to fine-tune
            num_virtual_tokens: Number of virtual tokens to use as soft prompts
            prompt_init_type: Type of prompt initialization ('random', 'text', or 'embedding')
            prompt_init_text: Text to use for initialization if init_type is 'text'
            tokenizer_name_or_path: Tokenizer name or path (if different from model)
            task_type: Type of task for the model (default: CAUSAL_LM)
            output_dir: Directory to save prompt tuning weights and configs
            device: Device to load the model on (if None, will use GPU if available)
            verbose: Whether to print verbose output
            **kwargs: Additional implementation-specific arguments
        """
        self.model_name = model_name
        self.num_virtual_tokens = num_virtual_tokens
        self.prompt_init_type = prompt_init_type
        self.prompt_init_text = prompt_init_text
        self.tokenizer_name_or_path = tokenizer_name_or_path or model_name
        self.task_type = task_type
        self.output_dir = output_dir
        self.verbose = verbose
        
        # Detect device
        if device is None:
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            self.device = device

        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir, exist_ok=True)

        # Initialize model, tokenizer, and prompt tuning config
        self.model = None
        self.tokenizer = None
        self.prompt_config = None
        self.peft_model = None

        # Additional attributes from kwargs
        for key, value in kwargs.items():
            setattr(self, key, value)

        logger.info(f"Initialized BasePromptTuning with model: {model_name}, virtual tokens: {num_virtual_tokens}")

    @measure_latency("prompt_tuning_initialization")
    def initialize(self) -> None:
        """
        Initialize the model, tokenizer, and Prompt Tuning configuration.

        This method should be called before any training or inference.
        """
        try:
            # Import PEFT at runtime to avoid dependency issues
            from peft import PromptTuningConfig, PromptTuningInit, get_peft_model

            # Load base model and tokenizer
            logger.info(f"Loading base model {self.model_name}...")
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32
            )
            self.tokenizer = AutoTokenizer.from_pretrained(self.tokenizer_name_or_path)

            # Determine the prompt initialization strategy
            if self.prompt_init_type == "text" and self.prompt_init_text:
                prompt_init = PromptTuningInit.TEXT
                prompt_init_kwargs = {"text": self.prompt_init_text, "tokenizer": self.tokenizer}
            elif self.prompt_init_type == "embedding":
                prompt_init = PromptTuningInit.EMBEDDING
                prompt_init_kwargs = {"tokenizer": self.tokenizer}
            else:  # Default to random
                prompt_init = PromptTuningInit.RANDOM
                prompt_init_kwargs = {}

            # Create Prompt Tuning config
            self.prompt_config = PromptTuningConfig(
                task_type=self.task_type,
                num_virtual_tokens=self.num_virtual_tokens,
                prompt_tuning_init=prompt_init,
                prompt_tuning_init_kwargs=prompt_init_kwargs,
                tokenizer_name_or_path=self.tokenizer_name_or_path
            )

            # Apply Prompt Tuning to the model
            logger.info(f"Applying Prompt Tuning configuration...")
            self.peft_model = get_peft_model(self.model, self.prompt_config)

            # Move model to device
            self.peft_model.to(self.device)

            # Print trainable parameters info if verbose
            if self.verbose:
                self._print_trainable_parameters()

            logger.info(f"Successfully initialized Prompt Tuning model")
            return True

        except Exception as e:
            logger.error(f"Error initializing Prompt Tuning model: {str(e)}")
            raise

    def _print_trainable_parameters(self) -> None:
        """
        Print the number of trainable parameters in the model.
        """
        if self.peft_model is None:
            logger.warning("Model not initialized yet.")
            return

        trainable_params = 0
        all_params = 0

        for _, param in self.peft_model.named_parameters():
            all_params += param.numel()
            if param.requires_grad:
                trainable_params += param.numel()

        percentage = 100 * trainable_params / all_params

        logger.info(
            f"Trainable parameters: {trainable_params:,} ({percentage:.2f}% of {all_params:,} total parameters)"
        )
    
    def save(self, path: Optional[str] = None) -> str:
        """
        Save the Prompt Tuning weights and configuration.

        Args:
            path: Path to save the model to. If None, uses output_dir/timestamp.

        Returns:
            Path where the model was saved
        """
        if self.peft_model is None:
            raise ValueError("Model not initialized. Call initialize() first.")

        # If path not provided, create one with timestamp
        if path is None:
            timestamp = time.strftime("%Y%m%d-%H%M%S")
            path = os.path.join(self.output_dir, f"prompt-{timestamp}")

        # Create directory if it doesn't exist
        os.makedirs(path, exist_ok=True)

        # Save the model
        logger.info(f"Saving Prompt Tuning model to {path}")
        self.peft_model.save_pretrained(path)

        # Save the tokenizer
        self.tokenizer.save_pretrained(path)

        # Save metadata
        metadata = {
            "base_model": self.model_name,
            "num_virtual_tokens": self.num_virtual_tokens,
            "prompt_init_type": self.prompt_init_type,
            "prompt_init_text": self.prompt_init_text,
            "tokenizer_name_or_path": self.tokenizer_name_or_path,
            "task_type": self.task_type,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        }

        metadata_path = os.path.join(path, "prompt_metadata.json")
        with open(metadata_path, "w") as f:
            json.dump(metadata, f, indent=2)

        logger.info(f"Saved metadata to {metadata_path}")
        return path

    def load(self, path: str) -> None:
        """
        Load a saved Prompt Tuning model.

        Args:
            path: Path to the saved prompt tuning model
        """
        try:
            # Import PEFT at runtime to avoid dependency issues
            from peft import PeftModel, PeftConfig

            # Check if path exists
            if not os.path.exists(path):
                raise ValueError(f"Path {path} does not exist")

            logger.info(f"Loading prompt tuning model from {path}...")

            # Load the configuration
            config = PeftConfig.from_pretrained(path)
            
            # Load the base model if not already loaded
            if self.model is None:
                self.model = AutoModelForCausalLM.from_pretrained(
                    config.base_model_name_or_path,
                    torch_dtype=torch.float16 if self.device == "cuda" else torch.float32
                )
            
            # Load tokenizer if not already loaded
            if self.tokenizer is None:
                tokenizer_path = config.tokenizer_name_or_path or config.base_model_name_or_path
                self.tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
                
            # Load the PEFT model
            self.peft_model = PeftModel.from_pretrained(
                self.model,
                path,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32
            )
            
            # Move model to device
            self.peft_model.to(self.device)
            
            # Load metadata if available
            metadata_path = os.path.join(path, "prompt_metadata.json")
            if os.path.exists(metadata_path):
                with open(metadata_path, "r") as f:
                    metadata = json.load(f)
                    for key, value in metadata.items():
                        if not hasattr(self, key) or getattr(self, key) is None:
                            setattr(self, key, value)
            
            logger.info(f"Successfully loaded prompt tuning model from {path}")
            
            # Print trainable parameters if verbose
            if self.verbose:
                self._print_trainable_parameters()
                
            return True
            
        except Exception as e:
            logger.error(f"Error loading prompt tuning model: {str(e)}")
            raise

    def generate(
        self,
        prompt: str,
        max_new_tokens: int = 128,
        temperature: float = 0.7,
        top_p: float = 0.9,
        top_k: int = 50,
        repetition_penalty: float = 1.1,
        **kwargs
    ) -> str:
        """
        Generate text using the prompt tuning model.

        Args:
            prompt: Input text prompt
            max_new_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature
            top_p: Nucleus sampling parameter
            top_k: Top-k sampling parameter
            repetition_penalty: Repetition penalty
            **kwargs: Additional generation parameters

        Returns:
            Generated text as a string
        """
        if self.peft_model is None:
            raise ValueError("Model not initialized. Call initialize() or load() first.")

        # Encode the prompt
        input_ids = self.tokenizer.encode(prompt, return_tensors="pt").to(self.device)

        # Set up generation parameters
        gen_kwargs = {
            "input_ids": input_ids,
            "max_new_tokens": max_new_tokens,
            "temperature": temperature,
            "top_p": top_p,
            "top_k": top_k,
            "repetition_penalty": repetition_penalty,
            "do_sample": temperature > 0,
            "pad_token_id": self.tokenizer.eos_token_id,  # Use EOS as PAD
            **kwargs
        }

        # Generate text
        with torch.no_grad():
            generation_output = self.peft_model.generate(**gen_kwargs)

        # Decode the generated text
        generated_text = self.tokenizer.decode(generation_output[0], skip_special_tokens=True)

        # Return only the newly generated text (without the prompt)
        prompt_length = len(prompt)
        if generated_text.startswith(prompt):
            return generated_text[prompt_length:].strip()
        else:
            return generated_text.strip() 