# IA3 (Infused Adapter by Inhibiting and Amplifying Inner Activations)

## Overview

IA3 is a parameter-efficient fine-tuning (PEFT) method that works by scaling the activations of specific network components. Unlike other adapter methods that add new parameters to the model, IA3 directly modifies existing activations by applying learned vectors that inhibit or amplify inner activations.

This implementation in Deep Research Core provides a comprehensive set of tools for using IA3 with various model architectures including Llama, Mistral, and Gemma.

## Key Features

- **Extremely Parameter-Efficient**: IA3 typically uses even fewer parameters than LoRA or Adapters
- **Model-Specific Optimizations**: Special adapters for Llama, Mistral, and Gemma architectures
- **Comprehensive Training & Evaluation**: Includes complete training and evaluation tools
- **Memory-Efficient**: Reduced memory footprint compared to full fine-tuning
- **Simple API**: Easy-to-use interface for training and inference

## How IA3 Works

IA3 works by:

1. Adding vectors (scaling factors) to specific attention and feed-forward components
2. Only training these vectors while keeping the rest of the model frozen
3. The vectors effectively "modulate" the inner representations by element-wise multiplication
4. This allows the model to adapt to new tasks with minimal parameter changes

## Usage Examples

### Basic Usage

```python
from deep_research_core.optimization.ia3 import BaseIA3

# Initialize IA3
ia3_model = BaseIA3(
    model_name="meta-llama/Llama-2-7b-hf",
    target_modules=["q_proj", "k_proj", "v_proj", "o_proj"],
    output_dir="./ia3_output"
)

# Load and initialize the model
ia3_model.initialize()

# Generate text
output = ia3_model.generate(
    prompt="Explain how parameter efficient fine-tuning works:",
    max_new_tokens=100,
    temperature=0.7
)
print(output)

# Save the model
save_path = ia3_model.save()
```

### Using Model-Specific Adapters

```python
from deep_research_core.optimization.ia3 import LlamaIA3Adapter, MistralIA3Adapter, GemmaIA3Adapter

# Choose the appropriate adapter for your model
model = LlamaIA3Adapter(
    model_name="meta-llama/Llama-2-7b-hf",
    output_dir="./llama_ia3_output"
)
```

### Fine-Tuning with IA3

```python
from deep_research_core.optimization.ia3 import IA3Trainer, IA3TrainingArguments

# Initialize model
ia3_model = LlamaIA3Adapter(model_name="meta-llama/Llama-2-7b-hf")
ia3_model.initialize()

# Define training arguments
training_args = IA3TrainingArguments(
    output_dir="./ia3_finetuned",
    num_train_epochs=3,
    per_device_train_batch_size=8,
    learning_rate=5e-5,
    weight_decay=0.01
)

# Initialize trainer
trainer = IA3Trainer(
    model=ia3_model.peft_model,
    args=training_args,
    train_dataset=train_dataset,  # Your dataset
    eval_dataset=eval_dataset,    # Your evaluation dataset
    tokenizer=ia3_model.tokenizer
)

# Start training
trainer.train()
```

### Evaluation

```python
from deep_research_core.optimization.ia3 import IA3Evaluator

# Initialize evaluator
evaluator = IA3Evaluator(
    model=ia3_model,
    batch_size=4,
    verbose=True
)

# Calculate perplexity
perplexity_results = evaluator.calculate_perplexity(texts=eval_texts)
print(f"Perplexity: {perplexity_results['perplexity']}")

# Benchmark generation
benchmark_results = evaluator.benchmark_generation(
    prompts=benchmark_prompts,
    max_new_tokens=100,
    num_runs=3
)
```

## Comparison with Other PEFT Methods

| Method | Parameter Efficiency | Inference Speed | Memory Usage |
|--------|----------------------|-----------------|--------------|
| IA3    | Very High (0.01%)    | High            | Very Low     |
| LoRA   | High (0.1-1%)        | High            | Low          |
| QLoRA  | High (0.1-1%)        | Medium          | Very Low     |
| Adapter| Medium (1-3%)        | Medium          | Medium       |
| Prefix | Medium (1-5%)        | Medium          | Medium       |

## References

- IA3: Training Task-Specific Deep Learning Models with Parameter-Efficient Activation Infusion (Liu et al., 2022)
- Papers and resources on Parameter-Efficient Fine-Tuning are available in the `docs/references` directory 