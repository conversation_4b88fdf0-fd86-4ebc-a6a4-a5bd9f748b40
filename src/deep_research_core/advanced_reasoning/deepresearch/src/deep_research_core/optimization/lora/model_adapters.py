"""
Model-specific adapters for LoRA fine-tuning.

This module provides specialized adapter classes for applying LoRA to different
model architectures, ensuring optimal configuration and compatibility.
"""

from typing import Dict, Any, List, Optional, Union, Tuple
import logging

import torch
from transformers import AutoConfig

from .base import BaseLoRA
from ...utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

class LlamaLoRAAdapter(BaseLoRA):
    """
    LoRA adapter optimized for Llama architecture models.
    
    This class provides Llama-specific optimizations and configurations for
    applying LoRA fine-tuning to models like Llama 2, Llama 3, and other compatible
    architectures.
    """
    
    def __init__(
        self,
        model_name: str,
        lora_rank: int = 8,
        lora_alpha: int = 16,
        lora_dropout: float = 0.05,
        target_modules: Optional[List[str]] = None,
        output_dir: str = "./llama_lora_output",
        device: Optional[str] = None,
        verbose: bool = False,
        **kwargs
    ):
        """
        Initialize the LlamaLoRAAdapter.
        
        Args:
            model_name: Name or path of the Llama model to fine-tune
            lora_rank: Rank of the LoRA low-rank matrices
            lora_alpha: LoRA scaling factor
            lora_dropout: Dropout probability for LoRA layers
            target_modules: List of module names to apply LoRA to (if None, uses Llama defaults)
            output_dir: Directory to save LoRA weights and configs
            device: Device to load the model on (if None, will use GPU if available)
            verbose: Whether to print verbose output
            **kwargs: Additional implementation-specific arguments
        """
        # Set Llama-specific target modules if not provided
        if target_modules is None:
            target_modules = [
                "q_proj", 
                "v_proj",
                "k_proj", 
                "o_proj", 
                "gate_proj", 
                "up_proj", 
                "down_proj"
            ]
            
        # Initialize the base LoRA with Llama-specific configurations
        super().__init__(
            model_name=model_name,
            lora_rank=lora_rank,
            lora_alpha=lora_alpha,
            lora_dropout=lora_dropout,
            target_modules=target_modules,
            bias="none",  # Llama models typically don't use bias
            task_type="CAUSAL_LM",
            output_dir=output_dir,
            device=device,
            verbose=verbose,
            **kwargs
        )
        
        # Llama-specific attributes
        self.model_type = "llama"
        self.supports_gradient_checkpointing = True
        
        logger.info(f"Initialized LlamaLoRAAdapter for model: {model_name}")
    
    def initialize(self) -> None:
        """
        Initialize the model with Llama-specific optimizations.
        
        This method overrides the base initialize method to add Llama-specific
        configurations and optimizations.
        """
        # Call the parent initialization
        result = super().initialize()
        
        if self.model is not None and self.supports_gradient_checkpointing:
            # Enable gradient checkpointing for memory efficiency during training
            self.model.gradient_checkpointing_enable()
            logger.info("Enabled gradient checkpointing for memory efficiency")
            
        return result
    
    def get_model_specs(self) -> Dict[str, Any]:
        """
        Get Llama-specific model specifications.
        
        Returns:
            Dictionary with model specifications
        """
        if self.model is None:
            raise ValueError("Model not initialized. Call initialize() first.")
            
        try:
            config = AutoConfig.from_pretrained(self.model_name)
            
            return {
                "model_type": self.model_type,
                "hidden_size": config.hidden_size,
                "num_attention_heads": config.num_attention_heads,
                "num_hidden_layers": config.num_hidden_layers,
                "vocab_size": config.vocab_size,
                "max_position_embeddings": config.max_position_embeddings,
                "rope_theta": getattr(config, "rope_theta", None),
                "rope_scaling": getattr(config, "rope_scaling", None)
            }
        except Exception as e:
            logger.warning(f"Could not get full model specs: {str(e)}")
            return {"model_type": self.model_type}


class MistralLoRAAdapter(BaseLoRA):
    """
    LoRA adapter optimized for Mistral architecture models.
    
    This class provides Mistral-specific optimizations and configurations for
    applying LoRA fine-tuning to Mistral models and their derivatives.
    """
    
    def __init__(
        self,
        model_name: str,
        lora_rank: int = 8,
        lora_alpha: int = 16,
        lora_dropout: float = 0.05,
        target_modules: Optional[List[str]] = None,
        output_dir: str = "./mistral_lora_output",
        device: Optional[str] = None,
        verbose: bool = False,
        **kwargs
    ):
        """
        Initialize the MistralLoRAAdapter.
        
        Args:
            model_name: Name or path of the Mistral model to fine-tune
            lora_rank: Rank of the LoRA low-rank matrices
            lora_alpha: LoRA scaling factor
            lora_dropout: Dropout probability for LoRA layers
            target_modules: List of module names to apply LoRA to (if None, uses Mistral defaults)
            output_dir: Directory to save LoRA weights and configs
            device: Device to load the model on (if None, will use GPU if available)
            verbose: Whether to print verbose output
            **kwargs: Additional implementation-specific arguments
        """
        # Set Mistral-specific target modules if not provided
        if target_modules is None:
            target_modules = [
                "q_proj", 
                "v_proj",
                "k_proj", 
                "o_proj", 
                "gate_proj", 
                "up_proj", 
                "down_proj"
            ]
            
        # Initialize the base LoRA with Mistral-specific configurations
        super().__init__(
            model_name=model_name,
            lora_rank=lora_rank,
            lora_alpha=lora_alpha,
            lora_dropout=lora_dropout,
            target_modules=target_modules,
            bias="none",  # Mistral models typically don't use bias
            task_type="CAUSAL_LM",
            output_dir=output_dir,
            device=device,
            verbose=verbose,
            **kwargs
        )
        
        # Mistral-specific attributes
        self.model_type = "mistral"
        self.sliding_window = kwargs.get("sliding_window", True)
        self.window_size = kwargs.get("window_size", 4096)
        
        logger.info(f"Initialized MistralLoRAAdapter for model: {model_name}")
    
    def initialize(self) -> None:
        """
        Initialize the model with Mistral-specific optimizations.
        
        This method overrides the base initialize method to add Mistral-specific
        configurations and optimizations.
        """
        # Call the parent initialization
        result = super().initialize()
        
        if self.model is not None:
            # Configure sliding window attention if needed
            try:
                if self.sliding_window and hasattr(self.model.config, "sliding_window"):
                    self.model.config.sliding_window = self.window_size
                    logger.info(f"Set sliding window size to {self.window_size}")
            except Exception as e:
                logger.warning(f"Could not configure sliding window: {str(e)}")
            
        return result


class GemmaLoRAAdapter(BaseLoRA):
    """
    LoRA adapter optimized for Gemma architecture models.
    
    This class provides Gemma-specific optimizations and configurations for
    applying LoRA fine-tuning to Google's Gemma models.
    """
    
    def __init__(
        self,
        model_name: str,
        lora_rank: int = 8,
        lora_alpha: int = 16,
        lora_dropout: float = 0.05,
        target_modules: Optional[List[str]] = None,
        output_dir: str = "./gemma_lora_output",
        device: Optional[str] = None,
        verbose: bool = False,
        **kwargs
    ):
        """
        Initialize the GemmaLoRAAdapter.
        
        Args:
            model_name: Name or path of the Gemma model to fine-tune
            lora_rank: Rank of the LoRA low-rank matrices
            lora_alpha: LoRA scaling factor
            lora_dropout: Dropout probability for LoRA layers
            target_modules: List of module names to apply LoRA to (if None, uses Gemma defaults)
            output_dir: Directory to save LoRA weights and configs
            device: Device to load the model on (if None, will use GPU if available)
            verbose: Whether to print verbose output
            **kwargs: Additional implementation-specific arguments
        """
        # Set Gemma-specific target modules if not provided
        if target_modules is None:
            target_modules = [
                "q_proj", 
                "v_proj",
                "k_proj", 
                "o_proj", 
                "gate_proj", 
                "up_proj", 
                "down_proj"
            ]
            
        # Initialize the base LoRA with Gemma-specific configurations
        super().__init__(
            model_name=model_name,
            lora_rank=lora_rank,
            lora_alpha=lora_alpha,
            lora_dropout=lora_dropout,
            target_modules=target_modules,
            bias="none",  # Gemma models typically don't use bias
            task_type="CAUSAL_LM",
            output_dir=output_dir,
            device=device,
            verbose=verbose,
            **kwargs
        )
        
        # Gemma-specific attributes
        self.model_type = "gemma"
        
        logger.info(f"Initialized GemmaLoRAAdapter for model: {model_name}")
    
    def get_gemma_specific_config(self) -> Dict[str, Any]:
        """
        Get Gemma-specific configuration information.
        
        Returns:
            Dictionary with Gemma-specific configuration
        """
        if self.model is None:
            raise ValueError("Model not initialized. Call initialize() first.")
            
        try:
            config = self.model.config
            
            # Check for Gemma-specific attributes
            head_dim = getattr(config, "head_dim", None)
            num_key_value_heads = getattr(config, "num_key_value_heads", None)
            
            return {
                "model_type": self.model_type,
                "hidden_size": config.hidden_size,
                "num_attention_heads": config.num_attention_heads,
                "num_key_value_heads": num_key_value_heads,
                "head_dim": head_dim,
                "num_hidden_layers": config.num_hidden_layers,
                "vocab_size": config.vocab_size,
                "rope_theta": getattr(config, "rope_theta", None)
            }
        except Exception as e:
            logger.warning(f"Could not get full Gemma config: {str(e)}")
            return {"model_type": self.model_type} 