"""
Base Low-Rank Adaptation (LoRA) implementation.

This module provides the base class for LoRA implementations in Deep Research Core,
defining the interface and common functionality for applying LoRA fine-tuning
to various language models.
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, List, Optional, Union, Tuple
import logging
import time

import torch
import torch.nn as nn
from transformers import PreTrainedModel, AutoModelForCausalLM, AutoTokenizer

from ...utils.structured_logging import get_logger
from ...utils.performance_metrics import measure_latency

# Create a logger
logger = get_logger(__name__)

class BaseLoRA:
    """
    Base class for Low-Rank Adaptation (LoRA) fine-tuning.

    LoRA applies low-rank decomposition matrices to the weights of a pretrained
    model to create an efficient fine-tuning approach that significantly reduces
    the number of trainable parameters.
    """

    def __init__(
        self,
        model_name: str,
        lora_rank: int = 8,
        lora_alpha: int = 16,
        lora_dropout: float = 0.05,
        target_modules: Optional[List[str]] = None,
        bias: str = "none",
        task_type: str = "CAUSAL_LM",
        output_dir: str = "./lora_output",
        device: Optional[str] = None,
        verbose: bool = False,
        **kwargs
    ):
        """
        Initialize the BaseLoRA.

        Args:
            model_name: Name or path of the base model to fine-tune
            lora_rank: Rank of the LoRA low-rank matrices
            lora_alpha: LoRA scaling factor
            lora_dropout: Dropout probability for LoRA layers
            target_modules: List of module names to apply LoRA to
            bias: How to handle bias parameters ('none', 'all', 'lora_only')
            task_type: Type of task for the model (default: CAUSAL_LM)
            output_dir: Directory to save LoRA weights and configs
            device: Device to load the model on (if None, will use GPU if available)
            verbose: Whether to print verbose output
            **kwargs: Additional implementation-specific arguments
        """
        self.model_name = model_name
        self.lora_rank = lora_rank
        self.lora_alpha = lora_alpha
        self.lora_dropout = lora_dropout

        # If target_modules is not provided, default to common modules for LLMs
        self.target_modules = target_modules or ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]

        self.bias = bias
        self.task_type = task_type
        self.output_dir = output_dir
        self.verbose = verbose

        # Detect device
        if device is None:
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            self.device = device

        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir, exist_ok=True)

        # Initialize model, tokenizer, and LoRA config
        self.model = None
        self.tokenizer = None
        self.lora_config = None
        self.peft_model = None

        # Additional attributes from kwargs
        for key, value in kwargs.items():
            setattr(self, key, value)

        logger.info(f"Initialized BaseLoRA with model: {model_name}, rank: {lora_rank}")

    @measure_latency("lora_initialization")
    def initialize(self) -> None:
        """
        Initialize the model, tokenizer, and LoRA configuration.

        This method should be called before any training or inference.
        """
        try:
            # Import PEFT at runtime to avoid dependency issues
            from peft import LoraConfig, get_peft_model

            # Load base model and tokenizer
            logger.info(f"Loading base model {self.model_name}...")
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32
            )
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)

            # Create LoRA config
            self.lora_config = LoraConfig(
                r=self.lora_rank,
                lora_alpha=self.lora_alpha,
                lora_dropout=self.lora_dropout,
                target_modules=self.target_modules,
                bias=self.bias,
                task_type=self.task_type
            )

            # Apply LoRA to the model
            logger.info(f"Applying LoRA configuration...")
            self.peft_model = get_peft_model(self.model, self.lora_config)

            # Move model to device
            self.peft_model.to(self.device)

            # Print trainable parameters info if verbose
            if self.verbose:
                self._print_trainable_parameters()

            logger.info(f"Successfully initialized LoRA model")
            return True

        except Exception as e:
            logger.error(f"Error initializing LoRA model: {str(e)}")
            raise

    def _print_trainable_parameters(self) -> None:
        """
        Print the number of trainable parameters in the model.
        """
        if self.peft_model is None:
            logger.warning("Model not initialized yet.")
            return

        trainable_params = 0
        all_params = 0

        for _, param in self.peft_model.named_parameters():
            all_params += param.numel()
            if param.requires_grad:
                trainable_params += param.numel()

        percentage = 100 * trainable_params / all_params

        logger.info(
            f"Trainable parameters: {trainable_params:,} ({percentage:.2f}% of {all_params:,} total parameters)"
        )

    def save(self, path: Optional[str] = None) -> str:
        """
        Save the LoRA weights and configuration.

        Args:
            path: Path to save the model to. If None, uses output_dir/timestamp.

        Returns:
            Path where the model was saved
        """
        if self.peft_model is None:
            raise ValueError("Model not initialized. Call initialize() first.")

        # If path not provided, create one with timestamp
        if path is None:
            timestamp = time.strftime("%Y%m%d-%H%M%S")
            path = os.path.join(self.output_dir, f"lora-{timestamp}")

        # Create directory if it doesn't exist
        os.makedirs(path, exist_ok=True)

        # Save the model
        logger.info(f"Saving LoRA model to {path}")
        self.peft_model.save_pretrained(path)

        # Save the tokenizer
        self.tokenizer.save_pretrained(path)

        # Save metadata
        metadata = {
            "base_model": self.model_name,
            "lora_rank": self.lora_rank,
            "lora_alpha": self.lora_alpha,
            "lora_dropout": self.lora_dropout,
            "target_modules": self.target_modules,
            "bias": self.bias,
            "task_type": self.task_type,
            "saved_at": time.strftime("%Y-%m-%d %H:%M:%S")
        }

        with open(os.path.join(path, "lora_metadata.json"), "w") as f:
            json.dump(metadata, f, indent=2)

        logger.info(f"Successfully saved LoRA model to {path}")
        return path

    def load(self, path: str) -> None:
        """
        Load LoRA weights from the specified path.

        Args:
            path: Path to load the LoRA weights from
        """
        try:
            # Import PEFT at runtime to avoid dependency issues
            from peft import PeftModel, PeftConfig

            # Load config
            logger.info(f"Loading LoRA model from {path}")
            config = PeftConfig.from_pretrained(path)

            # Load base model
            self.model = AutoModelForCausalLM.from_pretrained(
                config.base_model_name_or_path,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32
            )

            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(config.base_model_name_or_path)

            # Load LoRA model
            self.peft_model = PeftModel.from_pretrained(
                self.model,
                path,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32
            )

            # Move model to device
            self.peft_model.to(self.device)

            # Load metadata if available
            metadata_path = os.path.join(path, "lora_metadata.json")
            if os.path.exists(metadata_path):
                with open(metadata_path, "r") as f:
                    metadata = json.load(f)

                # Update attributes from metadata
                for key, value in metadata.items():
                    if hasattr(self, key):
                        setattr(self, key, value)

            logger.info(f"Successfully loaded LoRA model from {path}")

        except Exception as e:
            logger.error(f"Error loading LoRA model: {str(e)}")
            raise

    def generate(
        self,
        prompt: str,
        max_new_tokens: int = 128,
        temperature: float = 0.7,
        top_p: float = 0.9,
        top_k: int = 50,
        repetition_penalty: float = 1.1,
        **kwargs
    ) -> str:
        """
        Generate text using the LoRA-tuned model.

        Args:
            prompt: Input text prompt
            max_new_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature
            top_p: Nucleus sampling parameter
            top_k: Top-k sampling parameter
            repetition_penalty: Penalty for repetition
            **kwargs: Additional generation parameters

        Returns:
            Generated text
        """
        if self.peft_model is None:
            raise ValueError("Model not initialized. Call initialize() first.")

        # Prepare inputs
        inputs = self.tokenizer(prompt, return_tensors="pt")

        # Move inputs to device if they are tensors
        if hasattr(inputs, "to"):
            inputs = inputs.to(self.device)
        elif isinstance(inputs, dict):
            for key, value in inputs.items():
                if hasattr(value, "to"):
                    inputs[key] = value.to(self.device)

        # Generate
        with torch.no_grad():
            outputs = self.peft_model.generate(
                input_ids=inputs["input_ids"] if isinstance(inputs, dict) else inputs.input_ids,
                attention_mask=inputs["attention_mask"] if isinstance(inputs, dict) else inputs.attention_mask,
                max_new_tokens=max_new_tokens,
                temperature=temperature,
                top_p=top_p,
                top_k=top_k,
                repetition_penalty=repetition_penalty,
                do_sample=temperature > 0,
                **kwargs
            )

        # Decode and return
        if hasattr(self.tokenizer, "decode"):
            generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        else:
            # Handle mock tokenizer in tests
            generated_text = self.tokenizer.decode(outputs[0])

        # Remove the prompt from the generated text
        if generated_text and isinstance(generated_text, str) and generated_text.startswith(prompt):
            generated_text = generated_text[len(prompt):]

        return generated_text

    def get_trainable_parameters(self) -> List[nn.Parameter]:
        """
        Get the list of trainable parameters in the model.

        Returns:
            List of trainable parameters
        """
        if self.peft_model is None:
            raise ValueError("Model not initialized. Call initialize() first.")

        return [p for p in self.peft_model.parameters() if p.requires_grad]

    def get_parameter_count(self) -> Dict[str, int]:
        """
        Get the count of parameters in the model.

        Returns:
            Dictionary with counts of total and trainable parameters
        """
        if self.peft_model is None:
            raise ValueError("Model not initialized. Call initialize() first.")

        trainable_params = 0
        all_params = 0

        for _, param in self.peft_model.named_parameters():
            all_params += param.numel()
            if param.requires_grad:
                trainable_params += param.numel()

        return {
            "total_params": all_params,
            "trainable_params": trainable_params,
            "percentage_trainable": round(100 * trainable_params / all_params, 2)
        }