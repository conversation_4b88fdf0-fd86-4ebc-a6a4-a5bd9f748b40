"""
LoRA Merging module for Deep Research Core.

This module provides functionality for merging multiple LoRA adapters
into a single adapter or into the base model.
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional, Union, Tuple

import torch
import torch.nn as nn
from peft import PeftModel, PeftConfig

from ...utils.structured_logging import get_logger
from ...utils.performance_metrics import measure_latency, measure_memory_usage_decorator

logger = get_logger(__name__)


class LoRAMerger:
    """
    Class for merging multiple LoRA adapters.
    
    This class provides functionality to merge multiple LoRA adapters
    into a single adapter or directly into the base model.
    """
    
    def __init__(
        self,
        base_model_path: str,
        device: str = "cuda" if torch.cuda.is_available() else "cpu",
        **kwargs
    ):
        """
        Initialize the LoRA merger.
        
        Args:
            base_model_path: Path to the base model
            device: Device to use for merging
            **kwargs: Additional arguments
        """
        self.base_model_path = base_model_path
        self.device = device
        
        logger.info(f"Initialized LoRAMerger for base model: {base_model_path}")
    
    @measure_latency("lora_merge_adapters")
    @measure_memory_usage_decorator(enabled_param="memory_profile")
    def merge_adapters(
        self,
        adapter_paths: List[str],
        weights: Optional[List[float]] = None,
        output_path: Optional[str] = None,
        merge_into_base: bool = False,
        memory_profile: bool = False,
        **kwargs
    ) -> str:
        """
        Merge multiple LoRA adapters.
        
        Args:
            adapter_paths: List of paths to LoRA adapters
            weights: List of weights for each adapter (optional)
            output_path: Path to save the merged adapter (optional)
            merge_into_base: Whether to merge adapters into the base model
            memory_profile: Whether to profile memory usage
            **kwargs: Additional arguments
            
        Returns:
            Path to the merged adapter or model
        """
        # Validate inputs
        if not adapter_paths:
            raise ValueError("No adapter paths provided")
        
        if weights is not None and len(weights) != len(adapter_paths):
            raise ValueError("Number of weights must match number of adapters")
        
        # Use equal weights if not provided
        if weights is None:
            weights = [1.0 / len(adapter_paths)] * len(adapter_paths)
        
        # Normalize weights
        total_weight = sum(weights)
        weights = [w / total_weight for w in weights]
        
        logger.info(f"Merging {len(adapter_paths)} adapters with weights: {weights}")
        
        # Load base model
        from transformers import AutoModelForCausalLM
        
        base_model = AutoModelForCausalLM.from_pretrained(
            self.base_model_path,
            device_map=self.device,
            **kwargs
        )
        
        # If merging into base model
        if merge_into_base:
            return self._merge_into_base(base_model, adapter_paths, weights, output_path)
        
        # Otherwise, merge adapters into a new adapter
        return self._merge_adapters(base_model, adapter_paths, weights, output_path)
    
    def _merge_into_base(
        self,
        base_model: nn.Module,
        adapter_paths: List[str],
        weights: List[float],
        output_path: Optional[str] = None
    ) -> str:
        """
        Merge adapters into the base model.
        
        Args:
            base_model: Base model
            adapter_paths: List of paths to LoRA adapters
            weights: List of weights for each adapter
            output_path: Path to save the merged model (optional)
            
        Returns:
            Path to the merged model
        """
        # Create a merged state dict for the adapters
        merged_adapter = {}
        
        # Process each adapter
        for i, (adapter_path, weight) in enumerate(zip(adapter_paths, weights)):
            # Load adapter config
            config = PeftConfig.from_pretrained(adapter_path)
            
            # Load adapter state dict
            adapter_state_dict = torch.load(
                os.path.join(adapter_path, "adapter_model.bin"),
                map_location=self.device
            )
            
            # Add to merged adapter with weight
            for key, value in adapter_state_dict.items():
                if key in merged_adapter:
                    merged_adapter[key] += value * weight
                else:
                    merged_adapter[key] = value * weight
        
        # Apply merged adapter to base model
        with torch.no_grad():
            for name, param in base_model.named_parameters():
                # Find corresponding adapter parameters
                for adapter_key in merged_adapter:
                    if adapter_key.endswith(f".{name}"):
                        param.data += merged_adapter[adapter_key]
        
        # Save merged model
        if output_path is None:
            output_path = os.path.join(self.base_model_path, "merged_model")
        
        os.makedirs(output_path, exist_ok=True)
        base_model.save_pretrained(output_path)
        
        logger.info(f"Merged adapters into base model and saved to {output_path}")
        
        return output_path
    
    def _merge_adapters(
        self,
        base_model: nn.Module,
        adapter_paths: List[str],
        weights: List[float],
        output_path: Optional[str] = None
    ) -> str:
        """
        Merge multiple adapters into a single adapter.
        
        Args:
            base_model: Base model
            adapter_paths: List of paths to LoRA adapters
            weights: List of weights for each adapter
            output_path: Path to save the merged adapter (optional)
            
        Returns:
            Path to the merged adapter
        """
        # Create a merged state dict for the adapters
        merged_adapter = {}
        
        # Use the first adapter's config as a base
        base_config = PeftConfig.from_pretrained(adapter_paths[0])
        
        # Process each adapter
        for i, (adapter_path, weight) in enumerate(zip(adapter_paths, weights)):
            # Load adapter state dict
            adapter_state_dict = torch.load(
                os.path.join(adapter_path, "adapter_model.bin"),
                map_location=self.device
            )
            
            # Add to merged adapter with weight
            for key, value in adapter_state_dict.items():
                if key in merged_adapter:
                    merged_adapter[key] += value * weight
                else:
                    merged_adapter[key] = value * weight
        
        # Save merged adapter
        if output_path is None:
            output_path = os.path.join(self.base_model_path, "merged_adapter")
        
        os.makedirs(output_path, exist_ok=True)
        
        # Save adapter state dict
        torch.save(
            merged_adapter,
            os.path.join(output_path, "adapter_model.bin")
        )
        
        # Save adapter config
        base_config.save_pretrained(output_path)
        
        logger.info(f"Merged adapters and saved to {output_path}")
        
        return output_path
    
    @measure_latency("lora_evaluate_merged")
    def evaluate_merged(
        self,
        model_path: str,
        eval_dataset,
        **kwargs
    ) -> Dict[str, float]:
        """
        Evaluate a merged model or adapter.
        
        Args:
            model_path: Path to the merged model or adapter
            eval_dataset: Dataset for evaluation
            **kwargs: Additional arguments
            
        Returns:
            Dictionary of evaluation metrics
        """
        # Check if path is a merged model or adapter
        is_adapter = os.path.exists(os.path.join(model_path, "adapter_model.bin"))
        
        # Load model
        from transformers import AutoModelForCausalLM, AutoTokenizer
        
        tokenizer = AutoTokenizer.from_pretrained(self.base_model_path)
        
        if is_adapter:
            # Load base model with adapter
            model = AutoModelForCausalLM.from_pretrained(
                self.base_model_path,
                device_map=self.device,
                **kwargs
            )
            
            # Load adapter
            from peft import PeftModel
            model = PeftModel.from_pretrained(model, model_path)
        else:
            # Load merged model directly
            model = AutoModelForCausalLM.from_pretrained(
                model_path,
                device_map=self.device,
                **kwargs
            )
        
        # Evaluate model
        # This is a placeholder - actual evaluation would depend on the task
        metrics = {
            "perplexity": 0.0,
            "accuracy": 0.0
        }
        
        logger.info(f"Evaluated merged model with metrics: {metrics}")
        
        return metrics
