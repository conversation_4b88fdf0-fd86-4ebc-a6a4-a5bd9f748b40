"""
LoRA Evaluation module for Deep Research Core.

This module provides functionality for evaluating LoRA adapters,
including metrics calculation and comparison.
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional, Union, Tuple

import torch
import torch.nn as nn
from peft import PeftModel, PeftConfig

from ...utils.structured_logging import get_logger
from ...utils.performance_metrics import measure_latency, measure_memory_usage_decorator

logger = get_logger(__name__)


class LoRAEvaluator:
    """
    Class for evaluating LoRA adapters.
    
    This class provides functionality to evaluate LoRA adapters
    on various metrics and tasks.
    """
    
    def __init__(
        self,
        base_model_path: str,
        device: str = "cuda" if torch.cuda.is_available() else "cpu",
        **kwargs
    ):
        """
        Initialize the LoRA evaluator.
        
        Args:
            base_model_path: Path to the base model
            device: Device to use for evaluation
            **kwargs: Additional arguments
        """
        self.base_model_path = base_model_path
        self.device = device
        
        logger.info(f"Initialized LoRAEvaluator for base model: {base_model_path}")
    
    @measure_latency("lora_evaluate")
    @measure_memory_usage_decorator(enabled_param="memory_profile")
    def evaluate(
        self,
        adapter_path: str,
        eval_dataset,
        metrics: List[str] = ["perplexity", "accuracy"],
        memory_profile: bool = False,
        **kwargs
    ) -> Dict[str, float]:
        """
        Evaluate a LoRA adapter.
        
        Args:
            adapter_path: Path to the LoRA adapter
            eval_dataset: Dataset for evaluation
            metrics: List of metrics to evaluate
            memory_profile: Whether to profile memory usage
            **kwargs: Additional arguments
            
        Returns:
            Dictionary of evaluation metrics
        """
        # Load base model
        from transformers import AutoModelForCausalLM, AutoTokenizer
        
        tokenizer = AutoTokenizer.from_pretrained(self.base_model_path)
        
        model = AutoModelForCausalLM.from_pretrained(
            self.base_model_path,
            device_map=self.device,
            **kwargs
        )
        
        # Load adapter
        model = PeftModel.from_pretrained(model, adapter_path)
        
        # Evaluate model
        results = {}
        
        # Calculate perplexity if requested
        if "perplexity" in metrics:
            perplexity = self._calculate_perplexity(model, tokenizer, eval_dataset)
            results["perplexity"] = perplexity
        
        # Calculate accuracy if requested
        if "accuracy" in metrics:
            accuracy = self._calculate_accuracy(model, tokenizer, eval_dataset)
            results["accuracy"] = accuracy
        
        logger.info(f"Evaluated adapter {adapter_path} with metrics: {results}")
        
        return results
    
    @measure_latency("lora_compare")
    def compare(
        self,
        adapter_paths: List[str],
        eval_dataset,
        metrics: List[str] = ["perplexity", "accuracy"],
        **kwargs
    ) -> Dict[str, Dict[str, float]]:
        """
        Compare multiple LoRA adapters.
        
        Args:
            adapter_paths: List of paths to LoRA adapters
            eval_dataset: Dataset for evaluation
            metrics: List of metrics to evaluate
            **kwargs: Additional arguments
            
        Returns:
            Dictionary of evaluation metrics for each adapter
        """
        results = {}
        
        for adapter_path in adapter_paths:
            adapter_name = os.path.basename(adapter_path)
            results[adapter_name] = self.evaluate(
                adapter_path=adapter_path,
                eval_dataset=eval_dataset,
                metrics=metrics,
                **kwargs
            )
        
        # Find best adapter for each metric
        best_adapters = {}
        for metric in metrics:
            if metric == "perplexity":
                # Lower is better for perplexity
                best_adapter = min(
                    results.items(),
                    key=lambda x: x[1].get(metric, float("inf"))
                )[0]
            else:
                # Higher is better for other metrics
                best_adapter = max(
                    results.items(),
                    key=lambda x: x[1].get(metric, 0.0)
                )[0]
            
            best_adapters[metric] = best_adapter
        
        # Add best adapters to results
        results["best_adapters"] = best_adapters
        
        logger.info(f"Compared {len(adapter_paths)} adapters. Best adapters: {best_adapters}")
        
        return results
    
    def _calculate_perplexity(
        self,
        model: nn.Module,
        tokenizer,
        eval_dataset
    ) -> float:
        """
        Calculate perplexity on evaluation dataset.
        
        Args:
            model: Model to evaluate
            tokenizer: Tokenizer for the model
            eval_dataset: Dataset for evaluation
            
        Returns:
            Perplexity score
        """
        model.eval()
        total_loss = 0.0
        total_tokens = 0
        
        with torch.no_grad():
            for item in eval_dataset:
                # Get input text
                if isinstance(item, dict):
                    text = item.get("text", "")
                else:
                    text = str(item)
                
                # Tokenize
                inputs = tokenizer(text, return_tensors="pt").to(model.device)
                
                # Calculate loss
                outputs = model(**inputs, labels=inputs["input_ids"])
                loss = outputs.loss
                
                # Update totals
                total_loss += loss.item() * inputs["input_ids"].size(1)
                total_tokens += inputs["input_ids"].size(1)
        
        # Calculate perplexity
        perplexity = torch.exp(torch.tensor(total_loss / total_tokens)).item()
        
        return perplexity
    
    def _calculate_accuracy(
        self,
        model: nn.Module,
        tokenizer,
        eval_dataset
    ) -> float:
        """
        Calculate accuracy on evaluation dataset.
        
        Args:
            model: Model to evaluate
            tokenizer: Tokenizer for the model
            eval_dataset: Dataset for evaluation
            
        Returns:
            Accuracy score
        """
        model.eval()
        correct = 0
        total = 0
        
        with torch.no_grad():
            for item in eval_dataset:
                # Get input text and label
                if isinstance(item, dict):
                    text = item.get("text", "")
                    label = item.get("label", "")
                else:
                    # Skip if not in expected format
                    continue
                
                # Tokenize
                inputs = tokenizer(text, return_tensors="pt").to(model.device)
                
                # Generate prediction
                outputs = model.generate(
                    inputs["input_ids"],
                    max_new_tokens=50,
                    num_return_sequences=1
                )
                
                # Decode prediction
                prediction = tokenizer.decode(outputs[0], skip_special_tokens=True)
                
                # Check if prediction contains label
                if label in prediction:
                    correct += 1
                
                total += 1
        
        # Calculate accuracy
        accuracy = correct / total if total > 0 else 0.0
        
        return accuracy
