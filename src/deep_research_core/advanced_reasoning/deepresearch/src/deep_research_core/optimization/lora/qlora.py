"""
Quantized Low-Rank Adaptation (QLoRA) implementation.

This module extends the LoRA implementation with quantization-aware functionality,
allowing for fine-tuning of large language models with significantly reduced memory
requirements.
"""

import os
import json
from typing import Dict, Any, List, Optional, Union, Tuple
import logging
import time

import torch
import torch.nn as nn
from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig

from .base import BaseLoRA
from ...utils.structured_logging import get_logger
from ...utils.performance_metrics import measure_latency, measure_memory_usage_decorator

# Create a logger
logger = get_logger(__name__)

class QLoRA(BaseLoRA):
    """
    Quantized Low-Rank Adaptation (QLoRA) for memory-efficient fine-tuning.

    QLoRA combines model quantization with LoRA to enable fine-tuning of large models
    on consumer hardware with limited memory. It uses 4-bit quantization for the base
    model while keeping the LoRA adapters in higher precision.
    """

    def __init__(
        self,
        model_name: str,
        lora_rank: int = 8,
        lora_alpha: int = 16,
        lora_dropout: float = 0.05,
        target_modules: Optional[List[str]] = None,
        bias: str = "none",
        task_type: str = "CAUSAL_LM",
        output_dir: str = "./qlora_output",
        quantization_bits: int = 4,
        double_quant: bool = True,
        quant_type: str = "nf4",
        device: Optional[str] = None,
        verbose: bool = False,
        memory_profile: bool = False,
        **kwargs
    ):
        """
        Initialize the QLoRA.

        Args:
            model_name: Name or path of the base model to fine-tune
            lora_rank: Rank of the LoRA low-rank matrices
            lora_alpha: LoRA scaling factor
            lora_dropout: Dropout probability for LoRA layers
            target_modules: List of module names to apply LoRA to
            bias: How to handle bias parameters ('none', 'all', 'lora_only')
            task_type: Type of task for the model (default: CAUSAL_LM)
            output_dir: Directory to save LoRA weights and configs
            quantization_bits: Number of bits for quantization (4 or 8)
            double_quant: Whether to use double quantization for further memory savings
            quant_type: Quantization data type ('nf4' or 'fp4')
            device: Device to load the model on (if None, will use GPU if available)
            verbose: Whether to print verbose output
            memory_profile: Whether to perform memory profiling
            **kwargs: Additional implementation-specific arguments
        """
        # Initialize the base LoRA
        super().__init__(
            model_name=model_name,
            lora_rank=lora_rank,
            lora_alpha=lora_alpha,
            lora_dropout=lora_dropout,
            target_modules=target_modules,
            bias=bias,
            task_type=task_type,
            output_dir=output_dir,
            device=device,
            verbose=verbose,
            **kwargs
        )

        # Validate quantization parameters
        if quantization_bits not in [4, 8]:
            raise ValueError(f"Quantization bits must be 4 or 8, got {quantization_bits}")

        if quant_type not in ["nf4", "fp4"]:
            raise ValueError(f"Quantization type must be 'nf4' or 'fp4', got {quant_type}")

        # Set QLoRA-specific attributes
        self.quantization_bits = quantization_bits
        self.double_quant = double_quant
        self.quant_type = quant_type
        self.memory_profile = memory_profile

        # Setup memory tracking if requested
        if memory_profile:
            self.memory_snapshots = []

        logger.info(f"Initialized QLoRA with model: {model_name}, quantization: {quantization_bits}-bit {quant_type}")

    @measure_latency("qlora_initialization")
    @measure_memory_usage_decorator(enabled_param="memory_profile")
    def initialize(self) -> None:
        """
        Initialize the model, tokenizer, and LoRA configuration with quantization.

        This method overrides the base class to incorporate quantization aware loading.
        """
        try:
            # Import PEFT at runtime to avoid dependency issues
            from peft import LoraConfig, get_peft_model

            # Configure quantization
            quant_config = BitsAndBytesConfig(
                load_in_4bit=self.quantization_bits == 4,
                load_in_8bit=self.quantization_bits == 8,
                llm_int8_threshold=6.0,
                llm_int8_has_fp16_weight=False,
                bnb_4bit_compute_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                bnb_4bit_use_double_quant=self.double_quant,
                bnb_4bit_quant_type=self.quant_type  # 'nf4' or 'fp4'
            )

            # Take memory snapshot if profiling
            if self.memory_profile:
                self._take_memory_snapshot("before_model_load")

            # Load base model and tokenizer with quantization
            logger.info(f"Loading base model {self.model_name} with {self.quantization_bits}-bit quantization...")
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                quantization_config=quant_config,
                device_map={"": 0} if self.device == "cuda" else "auto"
            )

            # Take memory snapshot if profiling
            if self.memory_profile:
                self._take_memory_snapshot("after_model_load")

            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)

            # Create LoRA config
            self.lora_config = LoraConfig(
                r=self.lora_rank,
                lora_alpha=self.lora_alpha,
                lora_dropout=self.lora_dropout,
                target_modules=self.target_modules,
                bias=self.bias,
                task_type=self.task_type
            )

            # Apply LoRA to the model
            logger.info(f"Applying LoRA configuration...")
            self.peft_model = get_peft_model(self.model, self.lora_config)

            # Take memory snapshot if profiling
            if self.memory_profile:
                self._take_memory_snapshot("after_lora_apply")

            # Print trainable parameters info if verbose
            if self.verbose:
                self._print_trainable_parameters()

            logger.info(f"Successfully initialized QLoRA model")
            return True

        except Exception as e:
            logger.error(f"Error initializing QLoRA model: {str(e)}")
            raise

    def _take_memory_snapshot(self, stage: str) -> None:
        """
        Take a memory usage snapshot.

        Args:
            stage: Name of the snapshot stage
        """
        if not self.memory_profile:
            return

        # Get current memory usage
        if torch.cuda.is_available():
            allocated = torch.cuda.memory_allocated() / (1024 ** 3)  # GB
            reserved = torch.cuda.memory_reserved() / (1024 ** 3)    # GB
            max_allocated = torch.cuda.max_memory_allocated() / (1024 ** 3)  # GB

            snapshot = {
                "stage": stage,
                "timestamp": time.time(),
                "allocated_memory_gb": allocated,
                "reserved_memory_gb": reserved,
                "max_allocated_memory_gb": max_allocated
            }
        else:
            # For CPU, use system memory info
            import psutil
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()

            snapshot = {
                "stage": stage,
                "timestamp": time.time(),
                "rss_gb": memory_info.rss / (1024 ** 3),
                "vms_gb": memory_info.vms / (1024 ** 3)
            }

        self.memory_snapshots.append(snapshot)
        logger.info(f"Memory snapshot at {stage}: {json.dumps(snapshot)}")

    def save(self, path: Optional[str] = None) -> str:
        """
        Save the QLoRA weights, configuration, and memory profile if available.

        Args:
            path: Path to save the model to. If None, uses output_dir/timestamp.

        Returns:
            Path where the model was saved
        """
        # Save the model using the parent class method
        save_path = super().save(path)

        # Save memory profile if available
        if self.memory_profile and self.memory_snapshots:
            memory_profile_path = os.path.join(save_path, "memory_profile.json")
            with open(memory_profile_path, "w") as f:
                json.dump(self.memory_snapshots, f, indent=2)

            logger.info(f"Saved memory profile to {memory_profile_path}")

        # Save QLoRA-specific metadata
        metadata_path = os.path.join(save_path, "qlora_metadata.json")
        metadata = {
            "quantization_bits": self.quantization_bits,
            "double_quant": self.double_quant,
            "quant_type": self.quant_type,
            "saved_at": time.strftime("%Y-%m-%d %H:%M:%S")
        }

        with open(metadata_path, "w") as f:
            json.dump(metadata, f, indent=2)

        return save_path

    def get_memory_profile(self) -> Dict[str, Any]:
        """
        Get the memory profile statistics.

        Returns:
            Dictionary with memory usage information
        """
        if not self.memory_profile or not self.memory_snapshots:
            logger.warning("Memory profiling not enabled or no snapshots available")
            return {}

        # Calculate peak memory usage
        if torch.cuda.is_available():
            peak_allocated = max([s.get("max_allocated_memory_gb", 0) for s in self.memory_snapshots])
            current_allocated = torch.cuda.memory_allocated() / (1024 ** 3)  # GB

            profile = {
                "peak_memory_gb": peak_allocated,
                "current_memory_gb": current_allocated,
                "snapshots": self.memory_snapshots
            }
        else:
            # For CPU
            import psutil
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()

            profile = {
                "current_rss_gb": memory_info.rss / (1024 ** 3),
                "current_vms_gb": memory_info.vms / (1024 ** 3),
                "snapshots": self.memory_snapshots
            }

        return profile

    def estimate_memory_requirements(self, include_batch_size: int = 1) -> Dict[str, float]:
        """
        Estimate memory requirements for the model.

        Args:
            include_batch_size: Batch size to include in the estimation

        Returns:
            Dictionary with estimated memory requirements in GB
        """
        if self.model is None:
            raise ValueError("Model not initialized. Call initialize() first.")

        # Get model size in parameters
        model_params = sum(p.numel() for p in self.model.parameters())

        # Calculate size based on quantization
        if self.quantization_bits == 4:
            bytes_per_param = 0.5  # 4 bits = 0.5 bytes
        elif self.quantization_bits == 8:
            bytes_per_param = 1.0  # 8 bits = 1 byte
        else:
            bytes_per_param = 2.0  # FP16 = 2 bytes

        # Base model memory (quantized)
        base_model_gb = (model_params * bytes_per_param) / (1024 ** 3)

        # LoRA adapter memory (FP16)
        if self.peft_model is not None:
            lora_params = sum(p.numel() for p in self.peft_model.parameters() if p.requires_grad)
            lora_adapter_gb = (lora_params * 2) / (1024 ** 3)  # 2 bytes per parameter for FP16
        else:
            lora_params = 0
            # Estimate based on typical LoRA adapter size (0.1-1% of model size)
            lora_adapter_gb = base_model_gb * 0.01

        # Activation memory (rough estimate)
        avg_seq_len = 512  # Typical sequence length
        hidden_size = 4096  # Typical hidden size, will vary by model
        num_layers = 32     # Typical number of layers, will vary by model

        # Estimate activations for a batch
        activation_memory_gb = (include_batch_size * avg_seq_len * hidden_size * num_layers * 2) / (1024 ** 3)

        # Additional overhead (optimizer states, etc.)
        overhead_gb = lora_adapter_gb * 4  # Optimizer states can be ~4x parameter size

        return {
            "quantized_model_gb": base_model_gb,
            "lora_adapters_gb": lora_adapter_gb,
            "activation_memory_gb": activation_memory_gb,
            "overhead_gb": overhead_gb,
            "total_estimated_gb": base_model_gb + lora_adapter_gb + activation_memory_gb + overhead_gb,
            "model_parameters": model_params,
            "lora_parameters": lora_params,
            "quantization_bits": self.quantization_bits
        }