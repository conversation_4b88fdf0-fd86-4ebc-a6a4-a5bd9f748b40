"""
LoRA Training module for Deep Research Core.

This module provides training functionality for LoRA adapters,
allowing efficient fine-tuning of large language models.
"""

import os
import json
import time
import logging
from typing import Dict, List, Optional, Union, Any, Tuple, Callable
from dataclasses import dataclass, field

import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from transformers import (
    Trainer, 
    TrainingArguments,
    DataCollatorForLanguageModeling,
    get_scheduler
)
from peft import (
    LoraConfig,
    get_peft_model,
    prepare_model_for_kbit_training
)

from ...utils.performance_metrics import measure_latency, measure_memory_usage_decorator
from ...utils.structured_logging import get_logger

logger = get_logger(__name__)


@dataclass
class LoRATrainingArguments:
    """Arguments for LoRA training."""
    
    # Basic training parameters
    output_dir: str = field(
        default="./lora_output",
        metadata={"help": "Output directory for model and checkpoints"}
    )
    num_train_epochs: int = field(
        default=3,
        metadata={"help": "Number of training epochs"}
    )
    per_device_train_batch_size: int = field(
        default=4,
        metadata={"help": "Batch size per device during training"}
    )
    per_device_eval_batch_size: int = field(
        default=4,
        metadata={"help": "Batch size per device during evaluation"}
    )
    gradient_accumulation_steps: int = field(
        default=1,
        metadata={"help": "Number of updates steps to accumulate before backward pass"}
    )
    
    # Learning rate and schedule
    learning_rate: float = field(
        default=5e-5,
        metadata={"help": "Initial learning rate"}
    )
    lr_scheduler_type: str = field(
        default="cosine",
        metadata={"help": "Learning rate scheduler type"}
    )
    warmup_ratio: float = field(
        default=0.1,
        metadata={"help": "Ratio of steps for warmup"}
    )
    
    # Regularization
    weight_decay: float = field(
        default=0.01,
        metadata={"help": "Weight decay for AdamW optimizer"}
    )
    
    # Checkpointing
    save_steps: int = field(
        default=500,
        metadata={"help": "Save checkpoint every X updates steps"}
    )
    save_total_limit: int = field(
        default=3,
        metadata={"help": "Maximum number of checkpoints to keep"}
    )
    
    # Evaluation
    evaluation_strategy: str = field(
        default="steps",
        metadata={"help": "Evaluation strategy to adopt during training"}
    )
    eval_steps: int = field(
        default=500,
        metadata={"help": "Run evaluation every X steps"}
    )
    
    # Logging
    logging_dir: str = field(
        default="./logs",
        metadata={"help": "Logging directory"}
    )
    logging_steps: int = field(
        default=100,
        metadata={"help": "Log every X updates steps"}
    )
    
    # LoRA specific parameters
    lora_r: int = field(
        default=8,
        metadata={"help": "LoRA attention dimension"}
    )
    lora_alpha: int = field(
        default=16,
        metadata={"help": "LoRA alpha parameter"}
    )
    lora_dropout: float = field(
        default=0.05,
        metadata={"help": "LoRA dropout probability"}
    )
    target_modules: List[str] = field(
        default_factory=lambda: ["q_proj", "v_proj"],
        metadata={"help": "List of module names to apply LoRA to"}
    )
    
    # Mixed precision
    fp16: bool = field(
        default=False,
        metadata={"help": "Use mixed precision training"}
    )
    bf16: bool = field(
        default=False,
        metadata={"help": "Use bfloat16 mixed precision training"}
    )
    
    # Misc
    seed: int = field(
        default=42,
        metadata={"help": "Random seed for initialization"}
    )
    push_to_hub: bool = field(
        default=False,
        metadata={"help": "Push model to the hub after training"}
    )
    hub_model_id: Optional[str] = field(
        default=None,
        metadata={"help": "Model ID for pushing to the hub"}
    )
    hub_token: Optional[str] = field(
        default=None,
        metadata={"help": "Token for pushing to the hub"}
    )


class LoRATrainer:
    """
    Trainer for LoRA fine-tuning of language models.
    
    This class provides functionality for training models with LoRA adapters,
    handling the training loop, evaluation, and saving of checkpoints.
    """
    
    def __init__(
        self,
        model: nn.Module,
        train_dataset: Dataset,
        eval_dataset: Optional[Dataset] = None,
        args: Optional[LoRATrainingArguments] = None,
        data_collator: Optional[Callable] = None,
        tokenizer = None,
        callbacks: List[Callable] = None
    ):
        """
        Initialize the LoRA trainer.
        
        Args:
            model: The model to train
            train_dataset: Training dataset
            eval_dataset: Evaluation dataset (optional)
            args: Training arguments (optional)
            data_collator: Data collator function (optional)
            tokenizer: Tokenizer for the model (optional)
            callbacks: List of callback functions (optional)
        """
        self.model = model
        self.train_dataset = train_dataset
        self.eval_dataset = eval_dataset
        self.args = args or LoRATrainingArguments()
        self.tokenizer = tokenizer
        self.callbacks = callbacks or []
        
        # Set up data collator if not provided
        if data_collator is None and tokenizer is not None:
            self.data_collator = DataCollatorForLanguageModeling(
                tokenizer=tokenizer,
                mlm=False
            )
        else:
            self.data_collator = data_collator
        
        # Convert LoRA arguments to HF training arguments
        self.training_args = self._convert_to_training_args()
        
        # Initialize trainer
        self.trainer = None
        
        logger.info(f"Initialized LoRATrainer with {len(train_dataset)} training examples")
        if eval_dataset:
            logger.info(f"Evaluation dataset has {len(eval_dataset)} examples")
    
    def _convert_to_training_args(self) -> TrainingArguments:
        """
        Convert LoRA training arguments to HuggingFace TrainingArguments.
        
        Returns:
            TrainingArguments instance
        """
        return TrainingArguments(
            output_dir=self.args.output_dir,
            num_train_epochs=self.args.num_train_epochs,
            per_device_train_batch_size=self.args.per_device_train_batch_size,
            per_device_eval_batch_size=self.args.per_device_eval_batch_size,
            gradient_accumulation_steps=self.args.gradient_accumulation_steps,
            learning_rate=self.args.learning_rate,
            lr_scheduler_type=self.args.lr_scheduler_type,
            warmup_ratio=self.args.warmup_ratio,
            weight_decay=self.args.weight_decay,
            save_steps=self.args.save_steps,
            save_total_limit=self.args.save_total_limit,
            evaluation_strategy=self.args.evaluation_strategy,
            eval_steps=self.args.eval_steps,
            logging_dir=self.args.logging_dir,
            logging_steps=self.args.logging_steps,
            fp16=self.args.fp16,
            bf16=self.args.bf16,
            seed=self.args.seed,
            push_to_hub=self.args.push_to_hub,
            hub_model_id=self.args.hub_model_id,
            hub_token=self.args.hub_token,
        )
    
    @measure_latency("lora_prepare_model")
    @measure_memory_usage_decorator(enabled_param="memory_profile")
    def prepare_model(self, memory_profile: bool = False) -> nn.Module:
        """
        Prepare the model for LoRA training.
        
        Args:
            memory_profile: Whether to profile memory usage
            
        Returns:
            Prepared model with LoRA adapters
        """
        # Configure LoRA
        lora_config = LoraConfig(
            r=self.args.lora_r,
            lora_alpha=self.args.lora_alpha,
            target_modules=self.args.target_modules,
            lora_dropout=self.args.lora_dropout,
            bias="none",
            task_type="CAUSAL_LM"
        )
        
        # Prepare model for k-bit training if needed
        if getattr(self.model, "is_quantized", False):
            logger.info("Preparing quantized model for LoRA training")
            self.model = prepare_model_for_kbit_training(self.model)
        
        # Add LoRA adapters to the model
        logger.info(f"Adding LoRA adapters to model with config: {lora_config}")
        model = get_peft_model(self.model, lora_config)
        
        # Print trainable parameters
        model.print_trainable_parameters()
        
        return model
    
    @measure_latency("lora_train")
    def train(self, resume_from_checkpoint: bool = False) -> Dict[str, float]:
        """
        Train the model with LoRA.
        
        Args:
            resume_from_checkpoint: Whether to resume from checkpoint
            
        Returns:
            Dictionary of training metrics
        """
        # Prepare model with LoRA adapters
        prepared_model = self.prepare_model()
        
        # Initialize Trainer
        self.trainer = Trainer(
            model=prepared_model,
            args=self.training_args,
            train_dataset=self.train_dataset,
            eval_dataset=self.eval_dataset,
            data_collator=self.data_collator,
            tokenizer=self.tokenizer,
            callbacks=self.callbacks
        )
        
        # Start training
        logger.info("Starting LoRA training")
        start_time = time.time()
        
        train_result = self.trainer.train(resume_from_checkpoint=resume_from_checkpoint)
        
        end_time = time.time()
        training_time = end_time - start_time
        
        # Log training results
        metrics = train_result.metrics
        metrics["training_time"] = training_time
        
        logger.info(f"Training completed in {training_time:.2f} seconds")
        logger.info(f"Training metrics: {metrics}")
        
        # Save the model
        self.trainer.save_model()
        
        # Save training arguments
        if self.trainer.is_world_process_zero():
            with open(os.path.join(self.args.output_dir, "training_args.json"), "w") as f:
                json.dump(vars(self.args), f, indent=2)
        
        return metrics
    
    def evaluate(self) -> Dict[str, float]:
        """
        Evaluate the model.
        
        Returns:
            Dictionary of evaluation metrics
        """
        if self.eval_dataset is None:
            logger.warning("No evaluation dataset provided")
            return {}
        
        if self.trainer is None:
            logger.warning("Trainer not initialized. Call train() first or initialize manually.")
            # Initialize trainer if not already done
            prepared_model = self.prepare_model()
            self.trainer = Trainer(
                model=prepared_model,
                args=self.training_args,
                eval_dataset=self.eval_dataset,
                data_collator=self.data_collator,
                tokenizer=self.tokenizer
            )
        
        logger.info("Starting evaluation")
        metrics = self.trainer.evaluate()
        
        logger.info(f"Evaluation metrics: {metrics}")
        return metrics
    
    def save_model(self, output_dir: Optional[str] = None) -> str:
        """
        Save the model.
        
        Args:
            output_dir: Directory to save the model to (optional)
            
        Returns:
            Path to the saved model
        """
        if self.trainer is None:
            logger.warning("Trainer not initialized. Saving model directly.")
            save_dir = output_dir or self.args.output_dir
            self.model.save_pretrained(save_dir)
            if self.tokenizer:
                self.tokenizer.save_pretrained(save_dir)
            return save_dir
        
        # Save using the trainer
        save_dir = output_dir or self.args.output_dir
        self.trainer.save_model(save_dir)
        return save_dir
    
    @classmethod
    def from_pretrained(
        cls,
        model_path: str,
        train_dataset: Dataset,
        eval_dataset: Optional[Dataset] = None,
        args: Optional[LoRATrainingArguments] = None,
        **kwargs
    ) -> "LoRATrainer":
        """
        Create a LoRATrainer from a pretrained model.
        
        Args:
            model_path: Path to the pretrained model
            train_dataset: Training dataset
            eval_dataset: Evaluation dataset (optional)
            args: Training arguments (optional)
            **kwargs: Additional arguments to pass to the model loading function
            
        Returns:
            LoRATrainer instance
        """
        from transformers import AutoModelForCausalLM, AutoTokenizer
        
        logger.info(f"Loading model from {model_path}")
        
        # Load tokenizer
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        
        # Load model
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            device_map="auto",
            **kwargs
        )
        
        return cls(
            model=model,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            args=args,
            tokenizer=tokenizer
        )
