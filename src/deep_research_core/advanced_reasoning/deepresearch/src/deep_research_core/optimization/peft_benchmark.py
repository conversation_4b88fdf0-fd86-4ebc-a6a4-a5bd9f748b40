"""
PEFT Benchmark Utility

This module provides functionality to benchmark and compare different
Parameter-Efficient Fine-Tuning (PEFT) methods on various tasks and metrics.
"""

import os
import json
import time
from typing import Dict, List, Any, Optional, Union, Tuple
import logging
from pathlib import Path

import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from tqdm import tqdm

from ..utils.structured_logging import get_logger

# Import PEFT implementations
from .lora import BaseLoRA, QLoRA
from .adapter import BaseAdapter
from .prefix_tuning import BasePrefixTuning
from .prompt_tuning import BasePromptTuning

logger = get_logger(__name__)

class PEFTBenchmark:
    """
    Benchmark different Parameter-Efficient Fine-Tuning (PEFT) methods.
    
    This class provides functionality to compare various PEFT methods
    across different metrics like parameter efficiency, training time,
    inference speed, and performance on downstream tasks.
    """
    
    def __init__(
        self,
        model_name: str,
        dataset_path: Optional[str] = None,
        output_dir: str = "./peft_benchmark",
        device: Optional[str] = None,
        metrics: Optional[List[str]] = None,
        verbose: bool = False
    ):
        """
        Initialize the PEFT benchmark.
        
        Args:
            model_name: Base model name or path
            dataset_path: Path to the evaluation dataset
            output_dir: Directory to save benchmark results
            device: Device to run benchmark on (if None, will use GPU if available)
            metrics: List of metrics to evaluate (if None, uses defaults)
            verbose: Whether to print verbose output
        """
        self.model_name = model_name
        self.dataset_path = dataset_path
        self.output_dir = output_dir
        self.verbose = verbose
        
        # Detect device
        if device is None:
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            self.device = device
            
        # Set up default metrics if not provided
        self.metrics = metrics or [
            "trainable_parameters",
            "parameter_efficiency",
            "training_time",
            "inference_speed",
            "memory_usage",
            "perplexity",
            "accuracy"
        ]
        
        # Create output directory
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Initialize results storage
        self.results = {}
        self.current_run_id = None
        
        logger.info(f"Initialized PEFT benchmark for model: {model_name}")
        
    def prepare_peft_methods(
        self,
        base_kwargs: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Prepare a dictionary of PEFT methods with default configurations.
        
        Args:
            base_kwargs: Base kwargs to apply to all methods
            
        Returns:
            Dictionary mapping method names to their configurations
        """
        base_kwargs = base_kwargs or {
            "model_name": self.model_name,
            "device": self.device,
            "verbose": self.verbose
        }
        
        peft_methods = {
            "lora": {
                "class": BaseLoRA,
                "kwargs": {
                    "lora_rank": 8,
                    "lora_alpha": 16,
                    "lora_dropout": 0.05,
                    "output_dir": os.path.join(self.output_dir, "lora"),
                    **base_kwargs
                }
            },
            "qlora": {
                "class": QLoRA,
                "kwargs": {
                    "lora_rank": 8,
                    "lora_alpha": 16,
                    "lora_dropout": 0.05,
                    "quantization_bits": 4,
                    "output_dir": os.path.join(self.output_dir, "qlora"),
                    **base_kwargs
                }
            },
            "adapter": {
                "class": BaseAdapter,
                "kwargs": {
                    "adapter_size": 64,
                    "adapter_dropout": 0.1,
                    "output_dir": os.path.join(self.output_dir, "adapter"),
                    **base_kwargs
                }
            },
            "prefix_tuning": {
                "class": BasePrefixTuning,
                "kwargs": {
                    "num_virtual_tokens": 20,
                    "prefix_projection": True,
                    "output_dir": os.path.join(self.output_dir, "prefix_tuning"),
                    **base_kwargs
                }
            },
            "prompt_tuning": {
                "class": BasePromptTuning,
                "kwargs": {
                    "num_virtual_tokens": 30,
                    "prompt_init_type": "random",
                    "output_dir": os.path.join(self.output_dir, "prompt_tuning"),
                    **base_kwargs
                }
            }
        }
        
        return peft_methods
    
    def benchmark_parameter_efficiency(
        self,
        peft_methods: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Dict[str, Any]]:
        """
        Benchmark parameter efficiency of different PEFT methods.
        
        Args:
            peft_methods: Dictionary of PEFT methods to benchmark
            
        Returns:
            Dictionary with parameter efficiency metrics for each method
        """
        logger.info("Benchmarking parameter efficiency...")
        
        results = {}
        
        for name, method_config in tqdm(peft_methods.items(), desc="Methods"):
            try:
                # Create instance
                method_cls = method_config["class"]
                method_kwargs = method_config["kwargs"]
                method_instance = method_cls(**method_kwargs)
                
                # Initialize model
                method_instance.initialize()
                
                # Get model statistics
                trainable_params = 0
                all_params = 0
                for _, param in method_instance.peft_model.named_parameters():
                    all_params += param.numel()
                    if param.requires_grad:
                        trainable_params += param.numel()
                
                # Calculate parameter efficiency (% of trainable params)
                parameter_efficiency = 100 * (1 - trainable_params / all_params)
                
                # Store results
                results[name] = {
                    "trainable_parameters": trainable_params,
                    "total_parameters": all_params,
                    "parameter_efficiency": parameter_efficiency
                }
                
                if self.verbose:
                    logger.info(f"{name}: Trainable params: {trainable_params:,} ({100-parameter_efficiency:.2f}% of {all_params:,})")
                
            except Exception as e:
                logger.error(f"Error benchmarking {name}: {str(e)}")
                results[name] = {
                    "error": str(e)
                }
        
        return results
    
    def benchmark_inference_speed(
        self,
        peft_methods: Dict[str, Dict[str, Any]],
        prompt: str = "In this tutorial, we will learn how to fine-tune language models using",
        max_new_tokens: int = 100,
        num_runs: int = 5
    ) -> Dict[str, Dict[str, Any]]:
        """
        Benchmark inference speed of different PEFT methods.
        
        Args:
            peft_methods: Dictionary of PEFT methods to benchmark
            prompt: Prompt to use for generation
            max_new_tokens: Maximum number of new tokens to generate
            num_runs: Number of runs for each method
            
        Returns:
            Dictionary with inference speed metrics for each method
        """
        logger.info("Benchmarking inference speed...")
        
        results = {}
        
        for name, method_config in tqdm(peft_methods.items(), desc="Methods"):
            try:
                # Create instance
                method_cls = method_config["class"]
                method_kwargs = method_config["kwargs"]
                method_instance = method_cls(**method_kwargs)
                
                # Initialize model
                method_instance.initialize()
                
                # Warm-up run
                _ = method_instance.generate(prompt, max_new_tokens=10)
                
                # Benchmark runs
                generation_times = []
                tokens_per_second = []
                
                for _ in range(num_runs):
                    # Measure generation time
                    start_time = time.time()
                    generation = method_instance.generate(prompt, max_new_tokens=max_new_tokens)
                    end_time = time.time()
                    
                    # Calculate metrics
                    generation_time = end_time - start_time
                    num_tokens = len(method_instance.tokenizer.encode(generation))
                    tokens_per_sec = num_tokens / generation_time
                    
                    generation_times.append(generation_time)
                    tokens_per_second.append(tokens_per_sec)
                
                # Calculate averages
                avg_generation_time = np.mean(generation_times)
                avg_tokens_per_second = np.mean(tokens_per_second)
                
                # Store results
                results[name] = {
                    "avg_generation_time": avg_generation_time,
                    "avg_tokens_per_second": avg_tokens_per_second,
                    "generation_times": generation_times,
                    "tokens_per_second": tokens_per_second
                }
                
                if self.verbose:
                    logger.info(f"{name}: Avg generation time: {avg_generation_time:.4f}s, Avg tokens/sec: {avg_tokens_per_second:.2f}")
                
            except Exception as e:
                logger.error(f"Error benchmarking {name}: {str(e)}")
                results[name] = {
                    "error": str(e)
                }
        
        return results
    
    def benchmark_memory_usage(
        self,
        peft_methods: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Dict[str, Any]]:
        """
        Benchmark memory usage of different PEFT methods.
        
        Args:
            peft_methods: Dictionary of PEFT methods to benchmark
            
        Returns:
            Dictionary with memory usage metrics for each method
        """
        logger.info("Benchmarking memory usage...")
        
        results = {}
        
        for name, method_config in tqdm(peft_methods.items(), desc="Methods"):
            try:
                # Clear CUDA cache
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    
                # Record initial memory usage
                if torch.cuda.is_available():
                    initial_memory = torch.cuda.memory_allocated() / (1024 ** 2)  # MB
                else:
                    initial_memory = 0
                
                # Create instance
                method_cls = method_config["class"]
                method_kwargs = method_config["kwargs"]
                method_instance = method_cls(**method_kwargs)
                
                # Initialize model
                method_instance.initialize()
                
                # Record peak memory usage
                if torch.cuda.is_available():
                    peak_memory = torch.cuda.max_memory_allocated() / (1024 ** 2)  # MB
                    current_memory = torch.cuda.memory_allocated() / (1024 ** 2)  # MB
                    memory_increase = current_memory - initial_memory
                else:
                    peak_memory = 0
                    current_memory = 0
                    memory_increase = 0
                
                # Store results
                results[name] = {
                    "initial_memory_mb": initial_memory,
                    "peak_memory_mb": peak_memory,
                    "current_memory_mb": current_memory,
                    "memory_increase_mb": memory_increase
                }
                
                if self.verbose:
                    logger.info(f"{name}: Memory increase: {memory_increase:.2f} MB, Peak memory: {peak_memory:.2f} MB")
                
            except Exception as e:
                logger.error(f"Error benchmarking {name}: {str(e)}")
                results[name] = {
                    "error": str(e)
                }
        
        return results
    
    def run_benchmark(
        self,
        methods_to_benchmark: Optional[List[str]] = None,
        custom_peft_configs: Optional[Dict[str, Dict[str, Any]]] = None
    ) -> Dict[str, Dict[str, Any]]:
        """
        Run a comprehensive benchmark of PEFT methods.
        
        Args:
            methods_to_benchmark: List of method names to benchmark (if None, benchmarks all)
            custom_peft_configs: Custom configurations for PEFT methods
            
        Returns:
            Dictionary with all benchmark results
        """
        # Generate a unique run ID based on timestamp
        self.current_run_id = time.strftime("%Y%m%d-%H%M%S")
        
        # Get all available PEFT methods
        all_peft_methods = self.prepare_peft_methods()
        
        # Filter methods if specified
        if methods_to_benchmark:
            peft_methods = {k: v for k, v in all_peft_methods.items() if k in methods_to_benchmark}
        else:
            peft_methods = all_peft_methods
        
        # Apply custom configurations if provided
        if custom_peft_configs:
            for method_name, custom_config in custom_peft_configs.items():
                if method_name in peft_methods:
                    peft_methods[method_name]["kwargs"].update(custom_config)
        
        logger.info(f"Starting PEFT benchmark run {self.current_run_id}")
        logger.info(f"Benchmarking methods: {', '.join(peft_methods.keys())}")
        
        # Initialize results dictionary
        benchmark_results = {
            "run_id": self.current_run_id,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "model_name": self.model_name,
            "device": self.device,
            "methods": {}
        }
        
        # Run benchmarks
        parameter_results = self.benchmark_parameter_efficiency(peft_methods)
        inference_results = self.benchmark_inference_speed(peft_methods)
        memory_results = self.benchmark_memory_usage(peft_methods)
        
        # Combine results
        for method_name in peft_methods.keys():
            benchmark_results["methods"][method_name] = {
                "parameter_efficiency": parameter_results.get(method_name, {}),
                "inference_speed": inference_results.get(method_name, {}),
                "memory_usage": memory_results.get(method_name, {})
            }
        
        # Save results
        self.results[self.current_run_id] = benchmark_results
        self.save_results()
        
        return benchmark_results
    
    def save_results(self) -> str:
        """
        Save benchmark results to disk.
        
        Returns:
            Path to the saved results file
        """
        if not self.current_run_id or not self.results:
            logger.warning("No results to save")
            return ""
        
        # Create results directory
        results_dir = os.path.join(self.output_dir, "results")
        os.makedirs(results_dir, exist_ok=True)
        
        # Save results to JSON file
        results_path = os.path.join(results_dir, f"benchmark_{self.current_run_id}.json")
        with open(results_path, "w") as f:
            json.dump(self.results[self.current_run_id], f, indent=2)
        
        logger.info(f"Saved benchmark results to {results_path}")
        
        # Generate visualization
        self.visualize_results()
        
        return results_path
    
    def visualize_results(self) -> str:
        """
        Generate visualizations of benchmark results.
        
        Returns:
            Path to the saved visualization file
        """
        if not self.current_run_id or not self.results:
            logger.warning("No results to visualize")
            return ""
        
        # Create visualizations directory
        vis_dir = os.path.join(self.output_dir, "visualizations")
        os.makedirs(vis_dir, exist_ok=True)
        
        # Get current results
        results = self.results[self.current_run_id]
        
        # Extract methods and metrics
        methods = list(results["methods"].keys())
        
        # Create a plot for each metric category
        fig, axes = plt.subplots(3, 1, figsize=(12, 18))
        
        # 1. Parameter Efficiency
        param_data = []
        for method in methods:
            method_results = results["methods"][method]["parameter_efficiency"]
            if "error" not in method_results:
                param_data.append({
                    "Method": method,
                    "Trainable Parameters": method_results.get("trainable_parameters", 0),
                    "Parameter Efficiency (%)": method_results.get("parameter_efficiency", 0)
                })
        
        if param_data:
            param_df = pd.DataFrame(param_data)
            param_df.set_index("Method", inplace=True)
            param_df["Parameter Efficiency (%)"].plot(kind="bar", ax=axes[0], color="skyblue")
            axes[0].set_title("Parameter Efficiency (higher is better)")
            axes[0].set_ylabel("Efficiency (%)")
            axes[0].grid(axis="y", linestyle="--", alpha=0.7)
            
            # Add value labels on top of bars
            for i, v in enumerate(param_df["Parameter Efficiency (%)"]):
                axes[0].text(i, v + 0.5, f"{v:.2f}%", ha="center")
        
        # 2. Inference Speed
        speed_data = []
        for method in methods:
            method_results = results["methods"][method]["inference_speed"]
            if "error" not in method_results:
                speed_data.append({
                    "Method": method,
                    "Tokens/Second": method_results.get("avg_tokens_per_second", 0)
                })
        
        if speed_data:
            speed_df = pd.DataFrame(speed_data)
            speed_df.set_index("Method", inplace=True)
            speed_df.plot(kind="bar", ax=axes[1], color="lightgreen")
            axes[1].set_title("Inference Speed (higher is better)")
            axes[1].set_ylabel("Tokens/Second")
            axes[1].grid(axis="y", linestyle="--", alpha=0.7)
            
            # Add value labels on top of bars
            for i, v in enumerate(speed_df["Tokens/Second"]):
                axes[1].text(i, v + 0.5, f"{v:.2f}", ha="center")
        
        # 3. Memory Usage
        memory_data = []
        for method in methods:
            method_results = results["methods"][method]["memory_usage"]
            if "error" not in method_results:
                memory_data.append({
                    "Method": method,
                    "Memory Usage (MB)": method_results.get("memory_increase_mb", 0)
                })
        
        if memory_data:
            memory_df = pd.DataFrame(memory_data)
            memory_df.set_index("Method", inplace=True)
            memory_df.plot(kind="bar", ax=axes[2], color="salmon")
            axes[2].set_title("Memory Usage (lower is better)")
            axes[2].set_ylabel("Memory (MB)")
            axes[2].grid(axis="y", linestyle="--", alpha=0.7)
            
            # Add value labels on top of bars
            for i, v in enumerate(memory_df["Memory Usage (MB)"]):
                axes[2].text(i, v + 0.5, f"{v:.2f} MB", ha="center")
        
        # Adjust layout and save figure
        plt.tight_layout()
        vis_path = os.path.join(vis_dir, f"benchmark_{self.current_run_id}.png")
        plt.savefig(vis_path)
        
        logger.info(f"Saved benchmark visualization to {vis_path}")
        
        return vis_path
    
    def load_results(self, run_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Load benchmark results from disk.
        
        Args:
            run_id: ID of the run to load (if None, loads the most recent)
            
        Returns:
            Dictionary with benchmark results
        """
        results_dir = os.path.join(self.output_dir, "results")
        
        if not os.path.exists(results_dir):
            logger.warning(f"Results directory {results_dir} does not exist")
            return {}
        
        # Get all result files
        result_files = [f for f in os.listdir(results_dir) if f.startswith("benchmark_") and f.endswith(".json")]
        
        if not result_files:
            logger.warning(f"No result files found in {results_dir}")
            return {}
        
        # Load specified run or most recent
        if run_id:
            result_file = f"benchmark_{run_id}.json"
            if result_file not in result_files:
                logger.warning(f"Run ID {run_id} not found")
                return {}
        else:
            # Sort by timestamp (runs are named benchmark_YYYYMMDD-HHMMSS.json)
            result_files.sort(reverse=True)
            result_file = result_files[0]
            run_id = result_file.replace("benchmark_", "").replace(".json", "")
        
        # Load results
        result_path = os.path.join(results_dir, result_file)
        with open(result_path, "r") as f:
            results = json.load(f)
        
        self.results[run_id] = results
        self.current_run_id = run_id
        
        logger.info(f"Loaded benchmark results from {result_path}")
        
        return results
    
    def compare_results(self, run_ids: List[str]) -> Dict[str, Any]:
        """
        Compare results from multiple benchmark runs.
        
        Args:
            run_ids: List of run IDs to compare
            
        Returns:
            Dictionary with comparison results
        """
        # Load all specified runs
        runs_data = {}
        for run_id in run_ids:
            if run_id not in self.results:
                self.load_results(run_id)
            
            if run_id in self.results:
                runs_data[run_id] = self.results[run_id]
        
        if not runs_data:
            logger.warning("No valid runs to compare")
            return {}
        
        # Initialize comparison results
        comparison = {
            "runs": run_ids,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "metrics": {}
        }
        
        # Compare parameter efficiency
        param_comparison = {}
        for run_id, run_data in runs_data.items():
            for method, method_data in run_data["methods"].items():
                if "parameter_efficiency" in method_data and "error" not in method_data["parameter_efficiency"]:
                    method_key = f"{method}_{run_id}"
                    param_comparison[method_key] = {
                        "run_id": run_id,
                        "method": method,
                        "trainable_parameters": method_data["parameter_efficiency"].get("trainable_parameters", 0),
                        "parameter_efficiency": method_data["parameter_efficiency"].get("parameter_efficiency", 0)
                    }
        
        comparison["metrics"]["parameter_efficiency"] = param_comparison
        
        # Compare inference speed
        speed_comparison = {}
        for run_id, run_data in runs_data.items():
            for method, method_data in run_data["methods"].items():
                if "inference_speed" in method_data and "error" not in method_data["inference_speed"]:
                    method_key = f"{method}_{run_id}"
                    speed_comparison[method_key] = {
                        "run_id": run_id,
                        "method": method,
                        "avg_tokens_per_second": method_data["inference_speed"].get("avg_tokens_per_second", 0),
                        "avg_generation_time": method_data["inference_speed"].get("avg_generation_time", 0)
                    }
        
        comparison["metrics"]["inference_speed"] = speed_comparison
        
        # Compare memory usage
        memory_comparison = {}
        for run_id, run_data in runs_data.items():
            for method, method_data in run_data["methods"].items():
                if "memory_usage" in method_data and "error" not in method_data["memory_usage"]:
                    method_key = f"{method}_{run_id}"
                    memory_comparison[method_key] = {
                        "run_id": run_id,
                        "method": method,
                        "memory_increase_mb": method_data["memory_usage"].get("memory_increase_mb", 0),
                        "peak_memory_mb": method_data["memory_usage"].get("peak_memory_mb", 0)
                    }
        
        comparison["metrics"]["memory_usage"] = memory_comparison
        
        # Create visualization
        self.visualize_comparison(comparison)
        
        return comparison
    
    def visualize_comparison(self, comparison: Dict[str, Any]) -> str:
        """
        Generate visualizations for benchmark comparison.
        
        Args:
            comparison: Comparison data
            
        Returns:
            Path to the saved visualization file
        """
        # Create visualizations directory
        vis_dir = os.path.join(self.output_dir, "visualizations")
        os.makedirs(vis_dir, exist_ok=True)
        
        # Extract methods and runs
        methods = set()
        runs = comparison["runs"]
        
        for metric_data in comparison["metrics"].values():
            for method_key, method_data in metric_data.items():
                methods.add(method_data["method"])
        
        methods = sorted(list(methods))
        
        if not methods or not runs:
            logger.warning("No methods or runs to visualize")
            return ""
        
        # Create a plot for each metric category
        fig, axes = plt.subplots(3, 1, figsize=(max(12, len(methods) * 2), 18))
        
        # 1. Parameter Efficiency
        param_data = []
        for method_key, method_data in comparison["metrics"]["parameter_efficiency"].items():
            param_data.append({
                "Method": f"{method_data['method']}_{method_data['run_id']}",
                "Parameter Efficiency (%)": method_data["parameter_efficiency"]
            })
        
        if param_data:
            param_df = pd.DataFrame(param_data)
            param_df.set_index("Method", inplace=True)
            param_df.plot(kind="bar", ax=axes[0], color="skyblue")
            axes[0].set_title("Parameter Efficiency Comparison (higher is better)")
            axes[0].set_ylabel("Efficiency (%)")
            axes[0].grid(axis="y", linestyle="--", alpha=0.7)
            
            # Add value labels on top of bars
            for i, v in enumerate(param_df["Parameter Efficiency (%)"]):
                axes[0].text(i, v + 0.5, f"{v:.2f}%", ha="center")
        
        # 2. Inference Speed
        speed_data = []
        for method_key, method_data in comparison["metrics"]["inference_speed"].items():
            speed_data.append({
                "Method": f"{method_data['method']}_{method_data['run_id']}",
                "Tokens/Second": method_data["avg_tokens_per_second"]
            })
        
        if speed_data:
            speed_df = pd.DataFrame(speed_data)
            speed_df.set_index("Method", inplace=True)
            speed_df.plot(kind="bar", ax=axes[1], color="lightgreen")
            axes[1].set_title("Inference Speed Comparison (higher is better)")
            axes[1].set_ylabel("Tokens/Second")
            axes[1].grid(axis="y", linestyle="--", alpha=0.7)
            
            # Add value labels on top of bars
            for i, v in enumerate(speed_df["Tokens/Second"]):
                axes[1].text(i, v + 0.5, f"{v:.2f}", ha="center")
        
        # 3. Memory Usage
        memory_data = []
        for method_key, method_data in comparison["metrics"]["memory_usage"].items():
            memory_data.append({
                "Method": f"{method_data['method']}_{method_data['run_id']}",
                "Memory Usage (MB)": method_data["memory_increase_mb"]
            })
        
        if memory_data:
            memory_df = pd.DataFrame(memory_data)
            memory_df.set_index("Method", inplace=True)
            memory_df.plot(kind="bar", ax=axes[2], color="salmon")
            axes[2].set_title("Memory Usage Comparison (lower is better)")
            axes[2].set_ylabel("Memory (MB)")
            axes[2].grid(axis="y", linestyle="--", alpha=0.7)
            
            # Add value labels on top of bars
            for i, v in enumerate(memory_df["Memory Usage (MB)"]):
                axes[2].text(i, v + 0.5, f"{v:.2f} MB", ha="center")
        
        # Adjust layout and save figure
        plt.tight_layout()
        comparison_id = "-".join(runs)[:20]  # Limit length of filename
        vis_path = os.path.join(vis_dir, f"comparison_{comparison_id}.png")
        plt.savefig(vis_path)
        
        logger.info(f"Saved comparison visualization to {vis_path}")
        
        return vis_path 