"""
Base Adapter implementation.

This module provides the base class for Adapter implementations in Deep Research Core,
defining the interface and common functionality for applying Adapter fine-tuning
to various language models.
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, List, Optional, Union, Tuple
import logging
import time

import torch
import torch.nn as nn
from transformers import PreTrainedModel, AutoModelForCausalLM, AutoTokenizer

from ...utils.structured_logging import get_logger
from ...utils.performance_metrics import measure_latency

# Create a logger
logger = get_logger(__name__)

class BaseAdapter:
    """
    Base class for Adapter fine-tuning.

    Adapters are small modules inserted between layers of a pre-trained model,
    creating an efficient fine-tuning approach that significantly reduces
    the number of trainable parameters.
    """

    def __init__(
        self,
        model_name: str,
        adapter_size: int = 64,
        adapter_dropout: float = 0.1,
        adapter_init_scale: float = 1e-3,
        target_modules: Optional[List[str]] = None,
        adapter_layers_pattern: Optional[Union[List[int], str]] = None,
        output_dir: str = "./adapter_output",
        device: Optional[str] = None,
        verbose: bool = False,
        **kwargs
    ):
        """
        Initialize the BaseAdapter.

        Args:
            model_name: Name or path of the base model to fine-tune
            adapter_size: Size of the adapter bottleneck dimension
            adapter_dropout: Dropout probability for adapter layers
            adapter_init_scale: Scale for the initialization of adapter weights
            target_modules: List of module names to apply adapters to
            adapter_layers_pattern: Pattern to specify which layers get adapters
            output_dir: Directory to save adapter weights and configs
            device: Device to load the model on (if None, will use GPU if available)
            verbose: Whether to print verbose output
            **kwargs: Additional implementation-specific arguments
        """
        self.model_name = model_name
        self.adapter_size = adapter_size
        self.adapter_dropout = adapter_dropout
        self.adapter_init_scale = adapter_init_scale

        # If target_modules is not provided, default to common modules for LLMs
        self.target_modules = target_modules or ["attention.output.dense", "output.dense"]

        # Adapter layers pattern - can be "all", "even", "odd", or list of indices
        self.adapter_layers_pattern = adapter_layers_pattern or "all"
        
        self.output_dir = output_dir
        self.verbose = verbose

        # Detect device
        if device is None:
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            self.device = device

        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir, exist_ok=True)

        # Initialize model, tokenizer, and adapter config
        self.model = None
        self.tokenizer = None
        self.adapter_config = None
        self.peft_model = None

        # Additional attributes from kwargs
        for key, value in kwargs.items():
            setattr(self, key, value)

        logger.info(f"Initialized BaseAdapter with model: {model_name}, adapter_size: {adapter_size}")

    @measure_latency("adapter_initialization")
    def initialize(self) -> None:
        """
        Initialize the model, tokenizer, and Adapter configuration.

        This method should be called before any training or inference.
        """
        try:
            # Import PEFT at runtime to avoid dependency issues
            from peft import AdapterConfig, get_peft_model

            # Load base model and tokenizer
            logger.info(f"Loading base model {self.model_name}...")
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32
            )
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)

            # Determine which layers to add adapters to
            if isinstance(self.adapter_layers_pattern, list):
                layer_ids = self.adapter_layers_pattern
            elif self.adapter_layers_pattern == "all":
                # Apply to all transformer layers
                layer_ids = list(range(self.model.config.num_hidden_layers))
            elif self.adapter_layers_pattern == "even":
                layer_ids = list(range(0, self.model.config.num_hidden_layers, 2))
            elif self.adapter_layers_pattern == "odd":
                layer_ids = list(range(1, self.model.config.num_hidden_layers, 2))
            else:
                layer_ids = list(range(self.model.config.num_hidden_layers))

            # Create Adapter config
            self.adapter_config = AdapterConfig(
                r=self.adapter_size,
                dropout=self.adapter_dropout,
                bias="none",
                task_type="CAUSAL_LM",
                target_modules=self.target_modules,
                modules_to_save=None,
                init_weights=True,
                inference_mode=False,
                moe_adapter_config=None,
                layer_ids=layer_ids
            )

            # Apply Adapters to the model
            logger.info(f"Applying Adapter configuration...")
            self.peft_model = get_peft_model(self.model, self.adapter_config)

            # Move model to device
            self.peft_model.to(self.device)

            # Print trainable parameters info if verbose
            if self.verbose:
                self._print_trainable_parameters()

            logger.info(f"Successfully initialized Adapter model")
            return True

        except Exception as e:
            logger.error(f"Error initializing Adapter model: {str(e)}")
            raise

    def _print_trainable_parameters(self) -> None:
        """
        Print the number of trainable parameters in the model.
        """
        if self.peft_model is None:
            logger.warning("Model not initialized yet.")
            return

        trainable_params = 0
        all_params = 0

        for _, param in self.peft_model.named_parameters():
            all_params += param.numel()
            if param.requires_grad:
                trainable_params += param.numel()

        percentage = 100 * trainable_params / all_params

        logger.info(
            f"Trainable parameters: {trainable_params:,} ({percentage:.2f}% of {all_params:,} total parameters)"
        )
    
    def save(self, path: Optional[str] = None) -> str:
        """
        Save the Adapter weights and configuration.

        Args:
            path: Path to save the model to. If None, uses output_dir/timestamp.

        Returns:
            Path where the model was saved
        """
        if self.peft_model is None:
            raise ValueError("Model not initialized. Call initialize() first.")

        # If path not provided, create one with timestamp
        if path is None:
            timestamp = time.strftime("%Y%m%d-%H%M%S")
            path = os.path.join(self.output_dir, f"adapter-{timestamp}")

        # Create directory if it doesn't exist
        os.makedirs(path, exist_ok=True)

        # Save the model
        logger.info(f"Saving Adapter model to {path}")
        self.peft_model.save_pretrained(path)

        # Save the tokenizer
        self.tokenizer.save_pretrained(path)

        # Save metadata
        metadata = {
            "base_model": self.model_name,
            "adapter_size": self.adapter_size,
            "adapter_dropout": self.adapter_dropout,
            "adapter_init_scale": self.adapter_init_scale,
            "adapter_layers_pattern": self.adapter_layers_pattern,
            "target_modules": self.target_modules,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        }

        metadata_path = os.path.join(path, "adapter_metadata.json")
        with open(metadata_path, "w") as f:
            json.dump(metadata, f, indent=2)

        logger.info(f"Saved metadata to {metadata_path}")
        return path

    def load(self, path: str) -> None:
        """
        Load a saved Adapter model.

        Args:
            path: Path to the saved adapter model
        """
        try:
            # Import PEFT at runtime to avoid dependency issues
            from peft import PeftModel, PeftConfig

            # Check if path exists
            if not os.path.exists(path):
                raise ValueError(f"Path {path} does not exist")

            logger.info(f"Loading adapter model from {path}...")

            # Load the configuration
            config = PeftConfig.from_pretrained(path)
            
            # Load the base model if not already loaded
            if self.model is None:
                self.model = AutoModelForCausalLM.from_pretrained(
                    config.base_model_name_or_path,
                    torch_dtype=torch.float16 if self.device == "cuda" else torch.float32
                )
            
            # Load tokenizer if not already loaded
            if self.tokenizer is None:
                self.tokenizer = AutoTokenizer.from_pretrained(config.base_model_name_or_path)
                
            # Load the PEFT model
            self.peft_model = PeftModel.from_pretrained(
                self.model,
                path,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32
            )
            
            # Move model to device
            self.peft_model.to(self.device)
            
            # Load metadata if available
            metadata_path = os.path.join(path, "adapter_metadata.json")
            if os.path.exists(metadata_path):
                with open(metadata_path, "r") as f:
                    metadata = json.load(f)
                    for key, value in metadata.items():
                        if not hasattr(self, key) or getattr(self, key) is None:
                            setattr(self, key, value)
            
            logger.info(f"Successfully loaded adapter model from {path}")
            
            # Print trainable parameters if verbose
            if self.verbose:
                self._print_trainable_parameters()
                
            return True
            
        except Exception as e:
            logger.error(f"Error loading adapter model: {str(e)}")
            raise

    def generate(
        self,
        prompt: str,
        max_new_tokens: int = 128,
        temperature: float = 0.7,
        top_p: float = 0.9,
        top_k: int = 50,
        repetition_penalty: float = 1.1,
        **kwargs
    ) -> str:
        """
        Generate text using the adapter model.

        Args:
            prompt: Input text prompt
            max_new_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature
            top_p: Nucleus sampling parameter
            top_k: Top-k sampling parameter
            repetition_penalty: Repetition penalty
            **kwargs: Additional generation parameters

        Returns:
            Generated text as a string
        """
        if self.peft_model is None:
            raise ValueError("Model not initialized. Call initialize() or load() first.")

        # Encode the prompt
        input_ids = self.tokenizer.encode(prompt, return_tensors="pt").to(self.device)

        # Set up generation parameters
        gen_kwargs = {
            "input_ids": input_ids,
            "max_new_tokens": max_new_tokens,
            "temperature": temperature,
            "top_p": top_p,
            "top_k": top_k,
            "repetition_penalty": repetition_penalty,
            "do_sample": temperature > 0,
            "pad_token_id": self.tokenizer.eos_token_id,  # Use EOS as PAD
            **kwargs
        }

        # Generate text
        with torch.no_grad():
            generation_output = self.peft_model.generate(**gen_kwargs)

        # Decode the generated text
        generated_text = self.tokenizer.decode(generation_output[0], skip_special_tokens=True)

        # Return only the newly generated text (without the prompt)
        prompt_length = len(prompt)
        if generated_text.startswith(prompt):
            return generated_text[prompt_length:].strip()
        else:
            return generated_text.strip() 