"""
Vietnamese Model Quantization.

Module này cung cấp các công cụ để lượng tử hóa (quantization) các mô hình tiếng <PERSON>,
gi<PERSON><PERSON> gi<PERSON>m kích thước mô hình và tăng tốc độ suy luận mà vẫn duy trì chất lượng.
"""

import os
import torch
import logging
import json
from typing import Dict, Any, List, Optional, Union, Tuple
from pathlib import Path

from ...utils.structured_logging import get_logger
from ...utils.performance_metrics import measure_latency, measure_memory_usage_decorator
from .mixed_precision_quantization import MixedPrecisionQuantizer

# Create a logger
logger = get_logger(__name__)

# Danh sách các mô hình tiếng Việt hỗ trợ quantization
SUPPORTED_VIETNAMESE_MODELS = {
    # Mô hình generative
    "vinallm": "vinai/vinallm-7b-chat",
    "vietnamese-llama": "bkai-foundation-models/vietnamese-llama2",
    "vietnamese-gpt": "NlpHUST/gpt-neo-vi-small",

    # <PERSON>ô hình embedding lớn
    "phobert-large": "vinai/phobert-large",
    "multilingual-e5-large": "intfloat/multilingual-e5-large",

    # Mô hình embedding cơ bản
    "phobert": "vinai/phobert-base",
    "viebert": "FPTAI/viebert-base-cased",
    "xlm-roberta-vi": "xlm-roberta-base",
    "multilingual-e5": "intfloat/multilingual-e5-base",
    "vietnamese-sbert": "keepitreal/vietnamese-sbert",
    "bkai-foundation-vi": "bkai-foundation-models/vietnamese-bi-encoder",
    "envibert": "nguyenvulebinh/envibert",
    "bartpho": "vinai/bartpho-syllable",
    "velectra": "vinai/velectra-base-discriminator-cased",
}

# Phân loại mô hình theo loại
MODEL_TYPES = {
    "generative": ["vinallm", "vietnamese-llama", "vietnamese-gpt"],
    "embedding_large": ["phobert-large", "multilingual-e5-large"],
    "embedding_base": [
        "phobert", "viebert", "xlm-roberta-vi", "multilingual-e5",
        "vietnamese-sbert", "bkai-foundation-vi", "envibert",
        "bartpho", "velectra"
    ]
}

class VietnameseModelQuantizer:
    """
    Lớp cung cấp các phương thức để lượng tử hóa các mô hình tiếng Việt.
    """

    def __init__(
        self,
        cache_dir: Optional[str] = None,
        device: Optional[str] = None,
        verbose: bool = False
    ):
        """
        Khởi tạo VietnameseModelQuantizer.

        Args:
            cache_dir: Thư mục cache cho các mô hình
            device: Thiết bị để chạy mô hình ("cpu", "cuda", "cuda:0", etc.)
            verbose: Bật chế độ verbose
        """
        self.cache_dir = cache_dir
        self.verbose = verbose

        # Xác định thiết bị
        if device is None:
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            self.device = device

        # Kiểm tra các thư viện cần thiết
        self._check_dependencies()

        if self.verbose:
            logger.info(f"Initialized VietnameseModelQuantizer with device: {self.device}")

    def _check_dependencies(self) -> None:
        """
        Kiểm tra các thư viện phụ thuộc cần thiết.
        """
        missing_deps = []

        try:
            import transformers
        except ImportError:
            missing_deps.append("transformers")

        try:
            import bitsandbytes
        except ImportError:
            missing_deps.append("bitsandbytes")

        try:
            import optimum
        except ImportError:
            missing_deps.append("optimum")

        if missing_deps:
            logger.warning(f"Missing dependencies: {', '.join(missing_deps)}. Some features may not work.")
            logger.warning("Install with: pip install " + " ".join(missing_deps))

    def get_quantization_config(self, method: str = "int8") -> Dict[str, Any]:
        """
        Lấy cấu hình lượng tử hóa cho một phương pháp cụ thể.

        Args:
            method: Phương pháp lượng tử hóa ("int8", "int4", "mixed_precision", "none")

        Returns:
            Dictionary chứa cấu hình lượng tử hóa
        """
        try:
            from transformers import BitsAndBytesConfig
        except ImportError:
            raise ImportError("Transformers package not installed. Please install it with 'pip install transformers'.")

        if method == "int8":
            try:
                import bitsandbytes as bnb
            except ImportError:
                raise ImportError("BitsAndBytes package not installed. Please install it with 'pip install bitsandbytes'.")

            return BitsAndBytesConfig(
                load_in_8bit=True,
                llm_int8_threshold=6.0,
                llm_int8_has_fp16_weight=False
            )
        elif method == "int4":
            try:
                import bitsandbytes as bnb
            except ImportError:
                raise ImportError("BitsAndBytes package not installed. Please install it with 'pip install bitsandbytes'.")

            return BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_compute_dtype=torch.float16,
                bnb_4bit_use_double_quant=True,
                bnb_4bit_quant_type="nf4"
            )
        elif method == "mixed_precision":
            # Mixed precision doesn't use BitsAndBytesConfig directly
            # It will be handled separately in the quantize_model method
            return {
                "method": "mixed_precision",
                "high_sensitivity_threshold": 0.01,
                "medium_sensitivity_threshold": 0.001
            }
        else:
            return None

    @measure_latency("quantize_vietnamese_model")
    @measure_memory_usage_decorator(enabled_param="memory_profile")
    def quantize_model(
        self,
        model_name: str,
        output_dir: str,
        method: str = "int8",
        calibration_data: Optional[List[str]] = None,
        high_sensitivity_threshold: float = 0.01,
        medium_sensitivity_threshold: float = 0.001,
        memory_profile: bool = False
    ) -> bool:
        """
        Lượng tử hóa một mô hình tiếng Việt và lưu vào ổ đĩa.

        Args:
            model_name: Tên mô hình cần lượng tử hóa (từ SUPPORTED_VIETNAMESE_MODELS)
            output_dir: Thư mục để lưu mô hình đã lượng tử hóa
            method: Phương pháp lượng tử hóa ("int8", "int4", "mixed_precision", "gptq")
            calibration_data: Dữ liệu hiệu chuẩn cho mixed-precision quantization
            high_sensitivity_threshold: Ngưỡng độ nhạy cao cho mixed-precision quantization
            medium_sensitivity_threshold: Ngưỡng độ nhạy trung bình cho mixed-precision quantization
            memory_profile: Bật chế độ theo dõi bộ nhớ

        Returns:
            True nếu thành công, False nếu thất bại
        """
        try:
            # Kiểm tra mô hình có được hỗ trợ không
            if model_name not in SUPPORTED_VIETNAMESE_MODELS:
                logger.error(f"Model {model_name} is not supported. Supported models: {list(SUPPORTED_VIETNAMESE_MODELS.keys())}")
                return False

            # Lấy đường dẫn mô hình
            model_path = SUPPORTED_VIETNAMESE_MODELS[model_name]

            # Xác định loại mô hình
            model_type = None
            for type_name, models in MODEL_TYPES.items():
                if model_name in models:
                    model_type = type_name
                    break

            # Import các thư viện cần thiết
            try:
                from transformers import AutoModelForCausalLM, AutoModel, AutoTokenizer
            except ImportError:
                raise ImportError("Transformers package not installed. Please install it with 'pip install transformers'.")

            # Lấy cấu hình lượng tử hóa
            quant_config = self.get_quantization_config(method)
            if not quant_config and method != "gptq":
                raise ValueError(f"Unsupported quantization method: {method}")

            logger.info(f"Quantizing Vietnamese model {model_name} ({model_path}) with method {method}")

            # Tạo thư mục đầu ra nếu chưa tồn tại
            os.makedirs(output_dir, exist_ok=True)

            # Tải tokenizer
            tokenizer = AutoTokenizer.from_pretrained(model_path, cache_dir=self.cache_dir)

            # Lượng tử hóa mô hình dựa trên loại và phương pháp
            if method == "mixed_precision":
                # Sử dụng mixed-precision quantization
                logger.info(f"Using mixed-precision quantization for model {model_name}")

                # Tạo dữ liệu hiệu chuẩn nếu không được cung cấp
                if calibration_data is None:
                    logger.info("No calibration data provided, generating default calibration data")
                    if model_type == "generative":
                        calibration_data = [
                            "Trí tuệ nhân tạo đang phát triển nhanh chóng và có tiềm năng thay đổi nhiều lĩnh vực trong xã hội.",
                            "Việt Nam có nhiều tiềm năng phát triển công nghệ thông tin và trí tuệ nhân tạo.",
                            "Các mô hình ngôn ngữ lớn có thể hỗ trợ nhiều tác vụ khác nhau như dịch thuật, tóm tắt và trả lời câu hỏi.",
                            "Lượng tử hóa mô hình giúp giảm kích thước và tăng tốc độ suy luận mà vẫn duy trì chất lượng."
                        ]
                    else:
                        calibration_data = [
                            "Trí tuệ nhân tạo đang phát triển nhanh chóng.",
                            "Việt Nam có nhiều tiềm năng phát triển công nghệ.",
                            "Các mô hình ngôn ngữ lớn hỗ trợ nhiều tác vụ.",
                            "Lượng tử hóa mô hình giúp tăng hiệu suất."
                        ]

                # Tải mô hình gốc
                if model_type == "generative":
                    model = AutoModelForCausalLM.from_pretrained(
                        model_path,
                        cache_dir=self.cache_dir,
                        device_map=self.device
                    )
                else:
                    model = AutoModel.from_pretrained(
                        model_path,
                        cache_dir=self.cache_dir,
                        device_map=self.device
                    )

                # Khởi tạo mixed-precision quantizer
                mp_quantizer = MixedPrecisionQuantizer(
                    device=self.device,
                    cache_dir=self.cache_dir,
                    verbose=self.verbose
                )

                # Lượng tử hóa mô hình với mixed-precision
                quantized_model, quant_info = mp_quantizer.quantize_model_with_calibration(
                    model=model,
                    tokenizer=tokenizer,
                    calibration_data=calibration_data,
                    output_dir=output_dir,
                    high_sensitivity_threshold=high_sensitivity_threshold,
                    medium_sensitivity_threshold=medium_sensitivity_threshold
                )

                # Cập nhật thông tin lượng tử hóa
                quant_info.update({
                    "model_name": model_name,
                    "model_path": model_path,
                    "model_type": model_type,
                    "device": self.device
                })

                # Lưu thông tin lượng tử hóa
                with open(os.path.join(output_dir, "quantization_info.json"), "w") as f:
                    json.dump(quant_info, f, indent=2)

            elif model_type == "generative":
                if method == "gptq":
                    # Sử dụng GPTQ quantization
                    try:
                        from optimum.gptq import GPTQQuantizer
                    except ImportError:
                        raise ImportError("Optimum package not installed. Please install it with 'pip install optimum'.")

                    # Tải mô hình
                    model = AutoModelForCausalLM.from_pretrained(
                        model_path,
                        cache_dir=self.cache_dir,
                        device_map=self.device
                    )

                    # Tạo quantizer
                    quantizer = GPTQQuantizer(
                        bits=4,
                        dataset="c4",
                        model_seqlen=2048,
                        block_name_to_quantize="model.layers"
                    )

                    # Lượng tử hóa mô hình
                    quantized_model = quantizer.quantize_model(model, tokenizer)

                    # Lưu mô hình đã lượng tử hóa
                    quantized_model.save_pretrained(output_dir)
                    tokenizer.save_pretrained(output_dir)

                else:
                    # Sử dụng BitsAndBytes quantization
                    model = AutoModelForCausalLM.from_pretrained(
                        model_path,
                        quantization_config=quant_config,
                        cache_dir=self.cache_dir,
                        device_map=self.device
                    )

                    # Lưu mô hình đã lượng tử hóa
                    model.save_pretrained(output_dir)
                    tokenizer.save_pretrained(output_dir)
            else:
                # Mô hình embedding
                if method == "gptq":
                    logger.warning(f"GPTQ quantization is not recommended for embedding models. Using int8 instead.")
                    method = "int8"
                    quant_config = self.get_quantization_config(method)

                # Tải mô hình embedding
                model = AutoModel.from_pretrained(
                    model_path,
                    quantization_config=quant_config,
                    cache_dir=self.cache_dir,
                    device_map=self.device
                )

                # Lưu mô hình đã lượng tử hóa
                model.save_pretrained(output_dir)
                tokenizer.save_pretrained(output_dir)

            # Lưu thông tin lượng tử hóa
            quant_info = {
                "model_name": model_name,
                "model_path": model_path,
                "quantization_method": method,
                "model_type": model_type,
                "device": self.device
            }

            with open(os.path.join(output_dir, "quantization_info.json"), "w") as f:
                import json
                json.dump(quant_info, f, indent=2)

            logger.info(f"Model quantized and saved to {output_dir}")
            return True

        except Exception as e:
            logger.error(f"Error quantizing model: {str(e)}")
            return False

    def load_quantized_model(
        self,
        model_dir: str,
        device: Optional[str] = None
    ) -> Tuple[Any, Any]:
        """
        Tải một mô hình tiếng Việt đã lượng tử hóa.

        Args:
            model_dir: Thư mục chứa mô hình đã lượng tử hóa
            device: Thiết bị để chạy mô hình

        Returns:
            Tuple (model, tokenizer)
        """
        try:
            # Xác định thiết bị
            if device is None:
                device = self.device

            # Kiểm tra thông tin lượng tử hóa
            quant_info_path = os.path.join(model_dir, "quantization_info.json")
            if os.path.exists(quant_info_path):
                with open(quant_info_path, "r") as f:
                    quant_info = json.load(f)

                model_type = quant_info.get("model_type")
                quant_method = quant_info.get("quantization_method")

                # Kiểm tra nếu là mixed-precision quantization
                if quant_method == "mixed_precision":
                    logger.info(f"Loading mixed-precision quantized model from {model_dir}")

                    # Import các thư viện cần thiết
                    from transformers import AutoModelForCausalLM, AutoModel, AutoTokenizer

                    # Tải tokenizer
                    tokenizer = AutoTokenizer.from_pretrained(model_dir)

                    # Tải mô hình dựa trên loại
                    if model_type == "generative":
                        model = AutoModelForCausalLM.from_pretrained(
                            model_dir,
                            device_map=device
                        )
                    else:
                        model = AutoModel.from_pretrained(
                            model_dir,
                            device_map=device
                        )

                    logger.info(f"Successfully loaded mixed-precision quantized model from {model_dir}")
                    return model, tokenizer
            else:
                # Nếu không có thông tin, thử đoán loại mô hình
                logger.warning(f"No quantization info found in {model_dir}. Trying to infer model type.")
                # Kiểm tra cấu trúc mô hình để đoán loại
                if os.path.exists(os.path.join(model_dir, "lm_head.weight")) or \
                   os.path.exists(os.path.join(model_dir, "model.layers.0.self_attn.q_proj.weight")):
                    model_type = "generative"
                else:
                    model_type = "embedding_base"
                quant_method = "int8"  # Giả định mặc định

            # Import các thư viện cần thiết
            from transformers import AutoModelForCausalLM, AutoModel, AutoTokenizer

            # Tải tokenizer
            tokenizer = AutoTokenizer.from_pretrained(model_dir)

            # Tải mô hình dựa trên loại
            if model_type == "generative":
                model = AutoModelForCausalLM.from_pretrained(
                    model_dir,
                    device_map=device
                )
            else:
                # Mô hình embedding
                model = AutoModel.from_pretrained(
                    model_dir,
                    device_map=device
                )

            logger.info(f"Loaded quantized model from {model_dir}")
            return model, tokenizer

        except Exception as e:
            logger.error(f"Error loading quantized model: {str(e)}")
            return None, None

    def benchmark_model(
        self,
        model_name: str,
        quantization_methods: List[str] = ["none", "int8", "int4"],
        input_length: int = 128,
        output_length: int = 64,
        num_runs: int = 5
    ) -> Dict[str, Any]:
        """
        Đánh giá hiệu suất của mô hình với các phương pháp lượng tử hóa khác nhau.

        Args:
            model_name: Tên mô hình cần đánh giá
            quantization_methods: Danh sách các phương pháp lượng tử hóa cần đánh giá
            input_length: Độ dài đầu vào để kiểm tra
            output_length: Độ dài đầu ra để kiểm tra (chỉ cho mô hình generative)
            num_runs: Số lần chạy để lấy trung bình

        Returns:
            Dictionary chứa kết quả benchmark
        """
        try:
            # Kiểm tra mô hình có được hỗ trợ không
            if model_name not in SUPPORTED_VIETNAMESE_MODELS:
                logger.error(f"Model {model_name} is not supported. Supported models: {list(SUPPORTED_VIETNAMESE_MODELS.keys())}")
                return {}

            # Lấy đường dẫn mô hình
            model_path = SUPPORTED_VIETNAMESE_MODELS[model_name]

            # Xác định loại mô hình
            model_type = None
            for type_name, models in MODEL_TYPES.items():
                if model_name in models:
                    model_type = type_name
                    break

            # Import các thư viện cần thiết
            from transformers import AutoModelForCausalLM, AutoModel, AutoTokenizer
            import time
            import torch

            # Tạo dữ liệu kiểm tra
            tokenizer = AutoTokenizer.from_pretrained(model_path, cache_dir=self.cache_dir)

            # Tạo văn bản tiếng Việt để kiểm tra
            if model_type == "generative":
                test_text = "Trí tuệ nhân tạo (AI) đang phát triển nhanh chóng và có tiềm năng thay đổi nhiều lĩnh vực trong xã hội. Hãy phân tích tác động của AI đối với nền kinh tế Việt Nam trong 5 năm tới."
            else:
                test_text = "Trí tuệ nhân tạo đang phát triển nhanh chóng và có tiềm năng thay đổi nhiều lĩnh vực trong xã hội."

            # Tokenize văn bản
            inputs = tokenizer(test_text, return_tensors="pt", padding="max_length", max_length=input_length)

            # Kết quả benchmark
            results = {
                "model_name": model_name,
                "model_path": model_path,
                "model_type": model_type,
                "input_length": input_length,
                "output_length": output_length,
                "num_runs": num_runs,
                "methods": {}
            }

            # Chạy benchmark cho từng phương pháp
            for method in quantization_methods:
                logger.info(f"Benchmarking {model_name} with {method} quantization")

                # Tạo thư mục tạm thời cho mô hình lượng tử hóa
                import tempfile
                with tempfile.TemporaryDirectory() as tmp_dir:
                    # Bỏ qua nếu phương pháp là "none"
                    if method == "none":
                        # Tải mô hình không lượng tử hóa
                        if model_type == "generative":
                            model = AutoModelForCausalLM.from_pretrained(
                                model_path,
                                cache_dir=self.cache_dir,
                                device_map=self.device
                            )
                        else:
                            model = AutoModel.from_pretrained(
                                model_path,
                                cache_dir=self.cache_dir,
                                device_map=self.device
                            )
                    else:
                        # Lượng tử hóa mô hình
                        success = self.quantize_model(
                            model_name=model_name,
                            output_dir=tmp_dir,
                            method=method
                        )

                        if not success:
                            logger.error(f"Failed to quantize model with method {method}")
                            continue

                        # Tải mô hình đã lượng tử hóa
                        model, _ = self.load_quantized_model(tmp_dir)

                        if model is None:
                            logger.error(f"Failed to load quantized model with method {method}")
                            continue

                    # Đo kích thước mô hình
                    model_size_mb = self._get_model_size_mb(model)

                    # Đo thời gian suy luận
                    latencies = []
                    memory_usages = []

                    # Chuyển inputs sang thiết bị
                    inputs = {k: v.to(self.device) for k, v in inputs.items()}

                    # Warm-up
                    with torch.no_grad():
                        if model_type == "generative":
                            _ = model.generate(**inputs, max_length=input_length + output_length)
                        else:
                            _ = model(**inputs)

                    # Đo hiệu suất
                    for _ in range(num_runs):
                        # Đo thời gian
                        start_time = time.time()

                        # Đo bộ nhớ trước
                        if torch.cuda.is_available() and self.device.startswith("cuda"):
                            torch.cuda.reset_peak_memory_stats()
                            torch.cuda.empty_cache()
                            memory_before = torch.cuda.max_memory_allocated() / (1024 ** 2)  # MB

                        # Suy luận
                        with torch.no_grad():
                            if model_type == "generative":
                                _ = model.generate(**inputs, max_length=input_length + output_length)
                            else:
                                _ = model(**inputs)

                        # Đo thời gian
                        end_time = time.time()
                        latencies.append(end_time - start_time)

                        # Đo bộ nhớ sau
                        if torch.cuda.is_available() and self.device.startswith("cuda"):
                            memory_after = torch.cuda.max_memory_allocated() / (1024 ** 2)  # MB
                            memory_usages.append(memory_after - memory_before)

                    # Tính trung bình
                    avg_latency = sum(latencies) / len(latencies)
                    avg_memory = sum(memory_usages) / len(memory_usages) if memory_usages else 0

                    # Lưu kết quả
                    results["methods"][method] = {
                        "model_size_mb": model_size_mb,
                        "avg_latency_seconds": avg_latency,
                        "avg_memory_usage_mb": avg_memory
                    }

                    # Giải phóng bộ nhớ
                    del model
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()

            return results

        except Exception as e:
            logger.error(f"Error benchmarking model: {str(e)}")
            return {}

    def _get_model_size_mb(self, model: Any) -> float:
        """
        Tính kích thước của mô hình theo MB.

        Args:
            model: Mô hình cần tính kích thước

        Returns:
            Kích thước mô hình theo MB
        """
        try:
            # Tính tổng số tham số
            total_params = sum(p.numel() for p in model.parameters())

            # Tính kích thước theo byte
            size_bytes = total_params * 4  # Giả sử mỗi tham số là 4 byte (float32)

            # Chuyển đổi sang MB
            size_mb = size_bytes / (1024 ** 2)

            return size_mb
        except Exception as e:
            logger.error(f"Error calculating model size: {str(e)}")
            return 0.0
