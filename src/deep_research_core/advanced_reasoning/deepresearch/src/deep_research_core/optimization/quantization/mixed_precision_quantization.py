"""
Mixed-precision quantization module.

This module provides utilities for mixed-precision quantization of models,
which allows for using different precision formats for different parts of the model
to optimize the trade-off between model size, inference speed, and accuracy.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Tuple, Any, Union

import torch
import torch.nn as nn
from transformers import AutoModel, AutoModelForCausalLM, AutoTokenizer, PreTrainedModel

from deep_research_core.utils.structured_logging import get_logger
from deep_research_core.utils.performance import measure_latency, measure_memory_usage_decorator

logger = get_logger(__name__)

class MixedPrecisionQuantizer:
    """
    Mixed-precision quantizer for neural network models.

    This class provides methods for quantizing models using mixed precision,
    where different parts of the model can use different precision formats
    (e.g., INT8, INT4, FP16) based on their sensitivity to quantization.
    """

    def __init__(
        self,
        device: Optional[str] = None,
        cache_dir: Optional[str] = None,
        verbose: bool = False
    ):
        """
        Initialize the mixed-precision quantizer.

        Args:
            device: Device to use for quantization (e.g., 'cuda:0', 'cpu')
            cache_dir: Directory to cache models
            verbose: Whether to print verbose logs
        """
        self.device = device or ("cuda:0" if torch.cuda.is_available() else "cpu")
        self.cache_dir = cache_dir
        self.verbose = verbose

        # Set up logging
        if verbose:
            logging.basicConfig(level=logging.INFO)

        logger.info(f"Initialized MixedPrecisionQuantizer with device={self.device}")

    def analyze_layer_sensitivity(
        self,
        model: PreTrainedModel,
        tokenizer: Any,
        calibration_data: List[str],
        layer_types_to_analyze: Optional[List[str]] = None
    ) -> Dict[str, Dict[str, float]]:
        """
        Analyze the sensitivity of different layers to quantization.

        Args:
            model: The model to analyze
            tokenizer: Tokenizer for the model
            calibration_data: List of text samples for calibration
            layer_types_to_analyze: List of layer types to analyze (e.g., ['Linear', 'Embedding'])
                                    If None, analyze all layer types

        Returns:
            Dictionary mapping layer names to their sensitivity scores
        """
        logger.info("Analyzing layer sensitivity to quantization...")

        if layer_types_to_analyze is None:
            layer_types_to_analyze = ['Linear', 'Embedding', 'LayerNorm', 'Conv1d', 'Conv2d']

        # Prepare calibration data
        inputs = tokenizer(calibration_data, padding=True, truncation=True, return_tensors="pt")
        inputs = {k: v.to(self.device) for k, v in inputs.items()}

        # Get baseline output
        model.eval()
        with torch.no_grad():
            baseline_output = model(**inputs)

            if hasattr(baseline_output, "logits"):
                baseline_output = baseline_output.logits
            elif hasattr(baseline_output, "last_hidden_state"):
                baseline_output = baseline_output.last_hidden_state

        # Analyze each layer
        sensitivity_scores = {}

        for name, module in model.named_modules():
            module_type = module.__class__.__name__

            if module_type in layer_types_to_analyze:
                # Skip if this is a container module without weights
                if not hasattr(module, "weight") or module.weight is None:
                    continue

                # Save original weights
                original_weight = module.weight.data.clone()

                # Quantize weights to INT8 temporarily
                with torch.no_grad():
                    # Simple INT8 quantization for testing sensitivity
                    int8_weight = torch.quantize_per_tensor(
                        original_weight.float(),
                        scale=1.0/127.0,
                        zero_point=0,
                        dtype=torch.qint8
                    )
                    int8_weight_dequantized = int8_weight.dequantize()
                    module.weight.data = int8_weight_dequantized

                # Get output with quantized layer
                with torch.no_grad():
                    quantized_output = model(**inputs)

                    if hasattr(quantized_output, "logits"):
                        quantized_output = quantized_output.logits
                    elif hasattr(quantized_output, "last_hidden_state"):
                        quantized_output = quantized_output.last_hidden_state

                # Restore original weights
                with torch.no_grad():
                    module.weight.data = original_weight

                # Calculate sensitivity (MSE between baseline and quantized outputs)
                mse = torch.mean((baseline_output - quantized_output) ** 2).item()

                # Store sensitivity score
                sensitivity_scores[name] = {
                    "type": module_type,
                    "sensitivity": mse,
                    "shape": list(module.weight.shape)
                }

                if self.verbose:
                    logger.info(f"Layer {name} ({module_type}) sensitivity: {mse:.6f}")

        # Sort layers by sensitivity
        sorted_scores = {k: v for k, v in sorted(
            sensitivity_scores.items(),
            key=lambda item: item[1]["sensitivity"],
            reverse=True
        )}

        logger.info(f"Analyzed {len(sorted_scores)} layers for quantization sensitivity")
        return sorted_scores

    def get_optimal_precision_map(
        self,
        sensitivity_scores: Dict[str, Dict[str, float]],
        high_sensitivity_threshold: float = 0.01,
        medium_sensitivity_threshold: float = 0.001
    ) -> Dict[str, str]:
        """
        Determine the optimal precision for each layer based on sensitivity scores.

        Args:
            sensitivity_scores: Dictionary mapping layer names to their sensitivity scores
            high_sensitivity_threshold: Threshold for high sensitivity (use FP16)
            medium_sensitivity_threshold: Threshold for medium sensitivity (use INT8)

        Returns:
            Dictionary mapping layer names to their optimal precision format
        """
        precision_map = {}

        for layer_name, info in sensitivity_scores.items():
            sensitivity = info["sensitivity"]

            if sensitivity > high_sensitivity_threshold:
                # High sensitivity layers use higher precision
                precision_map[layer_name] = "fp16"
            elif sensitivity > medium_sensitivity_threshold:
                # Medium sensitivity layers use INT8
                precision_map[layer_name] = "int8"
            else:
                # Low sensitivity layers use INT4
                precision_map[layer_name] = "int4"

        # Count layers for each precision
        precision_counts = {"fp16": 0, "int8": 0, "int4": 0}
        for precision in precision_map.values():
            precision_counts[precision] += 1

        logger.info(f"Precision distribution: {precision_counts}")
        return precision_map

    def apply_mixed_precision_quantization(
        self,
        model: PreTrainedModel,
        precision_map: Dict[str, str]
    ) -> PreTrainedModel:
        """
        Apply mixed-precision quantization to a model.

        Args:
            model: The model to quantize
            precision_map: Dictionary mapping layer names to their precision format

        Returns:
            Quantized model
        """
        logger.info("Applying mixed-precision quantization...")

        try:
            import bitsandbytes as bnb
        except ImportError:
            raise ImportError("BitsAndBytes package not installed. Please install it with 'pip install bitsandbytes'.")

        # Create a copy of the model to avoid modifying the original
        model = model.cpu()  # Move to CPU for quantization

        # Apply quantization to each layer based on the precision map
        for name, module in model.named_modules():
            if name in precision_map:
                precision = precision_map[name]

                # Skip if this is a container module without weights
                if not hasattr(module, "weight") or module.weight is None:
                    continue

                # Apply quantization based on precision
                if precision == "int8":
                    # Replace with 8-bit module
                    if isinstance(module, nn.Linear):
                        parent_name = ".".join(name.split(".")[:-1])
                        child_name = name.split(".")[-1]
                        parent = model.get_submodule(parent_name) if parent_name else model

                        # Create 8-bit linear layer
                        new_module = bnb.nn.Linear8bitLt(
                            module.in_features,
                            module.out_features,
                            bias=module.bias is not None,
                            has_fp16_weights=False,
                            threshold=6.0
                        )

                        # Copy weights and biases
                        with torch.no_grad():
                            new_module.weight.data = module.weight.data
                            if module.bias is not None:
                                new_module.bias.data = module.bias.data

                        # Replace the module
                        setattr(parent, child_name, new_module)

                elif precision == "int4":
                    # Replace with 4-bit module
                    if isinstance(module, nn.Linear):
                        parent_name = ".".join(name.split(".")[:-1])
                        child_name = name.split(".")[-1]
                        parent = model.get_submodule(parent_name) if parent_name else model

                        # Create 4-bit linear layer
                        new_module = bnb.nn.Linear4bit(
                            module.in_features,
                            module.out_features,
                            bias=module.bias is not None,
                            compute_dtype=torch.float16,
                            quant_type="nf4"
                        )

                        # Copy weights and biases
                        with torch.no_grad():
                            new_module.weight.data = module.weight.data
                            if module.bias is not None:
                                new_module.bias.data = module.bias.data

                        # Replace the module
                        setattr(parent, child_name, new_module)

                elif precision == "fp16":
                    # Convert to FP16
                    with torch.no_grad():
                        module.to(torch.float16)

        # Move model back to the specified device
        model = model.to(self.device)

        logger.info("Mixed-precision quantization applied successfully")
        return model

    @measure_latency("mixed_precision_quantize")
    @measure_memory_usage_decorator(enabled_param="memory_profile")
    def quantize_model_with_calibration(
        self,
        model: PreTrainedModel,
        tokenizer: Any,
        calibration_data: List[str],
        output_dir: str,
        high_sensitivity_threshold: float = 0.01,
        medium_sensitivity_threshold: float = 0.001,
        memory_profile: bool = False
    ) -> Tuple[PreTrainedModel, Dict[str, Any]]:
        """
        Quantize a model using mixed precision with calibration data.

        Args:
            model: The model to quantize
            tokenizer: Tokenizer for the model
            calibration_data: List of text samples for calibration
            output_dir: Directory to save the quantized model
            high_sensitivity_threshold: Threshold for high sensitivity (use FP16)
            medium_sensitivity_threshold: Threshold for medium sensitivity (use INT8)
            memory_profile: Whether to profile memory usage

        Returns:
            Tuple of (quantized model, quantization info)
        """
        # Analyze layer sensitivity
        sensitivity_scores = self.analyze_layer_sensitivity(
            model, tokenizer, calibration_data
        )

        # Determine optimal precision for each layer
        precision_map = self.get_optimal_precision_map(
            sensitivity_scores,
            high_sensitivity_threshold,
            medium_sensitivity_threshold
        )

        # Apply mixed-precision quantization
        quantized_model = self.apply_mixed_precision_quantization(model, precision_map)

        # Save quantized model
        os.makedirs(output_dir, exist_ok=True)
        quantized_model.save_pretrained(output_dir)
        tokenizer.save_pretrained(output_dir)

        # Save quantization info
        quant_info = {
            "quantization_method": "mixed_precision",
            "high_sensitivity_threshold": high_sensitivity_threshold,
            "medium_sensitivity_threshold": medium_sensitivity_threshold,
            "precision_map": precision_map,
            "layer_sensitivity": sensitivity_scores
        }

        with open(os.path.join(output_dir, "quantization_info.json"), "w") as f:
            json.dump(quant_info, f, indent=2)

        logger.info(f"Mixed-precision quantized model saved to {output_dir}")

        return quantized_model, quant_info
