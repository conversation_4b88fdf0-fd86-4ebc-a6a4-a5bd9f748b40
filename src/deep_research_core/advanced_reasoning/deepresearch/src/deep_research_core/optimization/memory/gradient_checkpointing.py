"""
Gradient checkpointing implementation for Deep Research Core.

This module provides functionality for gradient checkpointing, which reduces
memory usage during training by trading computation for memory.
"""

import torch
import torch.utils.checkpoint as torch_checkpoint
from typing import Dict, List, Optional, Tuple, Union, Any, Callable
from ...utils.structured_logging import get_logger

logger = get_logger(__name__)

class GradientCheckpointer:
    """
    Implements gradient checkpointing to reduce memory usage during training.
    
    Gradient checkpointing works by saving only some intermediate activations
    and recomputing others during the backward pass, trading computation time
    for memory efficiency.
    """
    
    def __init__(
        self,
        checkpoint_ratio: float = 0.5,
        use_reentrant: bool = True,
        preserve_rng_state: bool = True,
        selective_checkpointing: bool = False
    ):
        """
        Initialize gradient checkpointing.
        
        Args:
            checkpoint_ratio: Ratio of layers to apply checkpointing to (0.0 to 1.0)
            use_reentrant: Whether to use reentrant checkpointing (more memory efficient)
            preserve_rng_state: Whether to preserve RNG state during checkpointing
            selective_checkpointing: Whether to selectively apply checkpointing to memory-intensive layers
        """
        self.checkpoint_ratio = checkpoint_ratio
        self.use_reentrant = use_reentrant
        self.preserve_rng_state = preserve_rng_state
        self.selective_checkpointing = selective_checkpointing
        
        # Validate parameters
        if not 0.0 <= checkpoint_ratio <= 1.0:
            raise ValueError("checkpoint_ratio must be between 0.0 and 1.0")
    
    def checkpoint_function(self, function: Callable, *args, **kwargs) -> Any:
        """
        Apply checkpointing to a function.
        
        Args:
            function: Function to checkpoint
            *args: Arguments to pass to the function
            **kwargs: Keyword arguments to pass to the function
            
        Returns:
            Result of the function
        """
        return torch_checkpoint.checkpoint(
            function,
            *args,
            use_reentrant=self.use_reentrant,
            preserve_rng_state=self.preserve_rng_state,
            **kwargs
        )
    
    def checkpoint_sequential(self, functions: List[Callable], segments: int, *args, **kwargs) -> Any:
        """
        Apply checkpointing to a sequence of functions.
        
        Args:
            functions: List of functions to checkpoint
            segments: Number of segments to divide the sequence into
            *args: Arguments to pass to the functions
            **kwargs: Keyword arguments to pass to the functions
            
        Returns:
            Result of the sequential function application
        """
        return torch_checkpoint.checkpoint_sequential(
            functions,
            segments,
            *args,
            use_reentrant=self.use_reentrant,
            preserve_rng_state=self.preserve_rng_state,
            **kwargs
        )

def apply_gradient_checkpointing(
    model: torch.nn.Module,
    checkpoint_ratio: float = 0.5,
    use_reentrant: bool = True,
    preserve_rng_state: bool = True,
    selective_checkpointing: bool = False,
    layer_types: Optional[List[type]] = None
) -> None:
    """
    Apply gradient checkpointing to a model.
    
    This function modifies the forward pass of specified layers in the model
    to use gradient checkpointing, which reduces memory usage during training.
    
    Args:
        model: The model to apply gradient checkpointing to
        checkpoint_ratio: Ratio of layers to apply checkpointing to (0.0 to 1.0)
        use_reentrant: Whether to use reentrant checkpointing (more memory efficient)
        preserve_rng_state: Whether to preserve RNG state during checkpointing
        selective_checkpointing: Whether to selectively apply checkpointing to memory-intensive layers
        layer_types: List of layer types to apply checkpointing to (if None, applies to all layers)
    """
    if layer_types is None:
        # Default layer types that benefit from checkpointing
        layer_types = [
            torch.nn.TransformerEncoderLayer,
            torch.nn.TransformerDecoderLayer,
            torch.nn.MultiheadAttention
        ]
        
        # Check for common transformer layer types from popular libraries
        try:
            from transformers.models.bert.modeling_bert import BertLayer
            layer_types.append(BertLayer)
        except ImportError:
            pass
            
        try:
            from transformers.models.gpt2.modeling_gpt2 import GPT2Block
            layer_types.append(GPT2Block)
        except ImportError:
            pass
            
        try:
            from transformers.models.t5.modeling_t5 import T5Block
            layer_types.append(T5Block)
        except ImportError:
            pass
    
    # Find all eligible layers
    eligible_layers = []
    for name, module in model.named_modules():
        if any(isinstance(module, layer_type) for layer_type in layer_types):
            eligible_layers.append((name, module))
    
    # Determine which layers to checkpoint based on ratio
    num_layers_to_checkpoint = max(1, int(len(eligible_layers) * checkpoint_ratio))
    
    if selective_checkpointing:
        # Sort layers by parameter count (proxy for memory usage)
        eligible_layers.sort(key=lambda x: sum(p.numel() for p in x[1].parameters()), reverse=True)
    
    # Apply checkpointing to selected layers
    layers_to_checkpoint = eligible_layers[:num_layers_to_checkpoint]
    
    for name, module in layers_to_checkpoint:
        original_forward = module.forward
        
        # Define new forward function with checkpointing
        def make_checkpointed_forward(original_forward):
            def checkpointed_forward(*args, **kwargs):
                return torch_checkpoint.checkpoint(
                    original_forward,
                    *args,
                    use_reentrant=use_reentrant,
                    preserve_rng_state=preserve_rng_state,
                    **kwargs
                )
            return checkpointed_forward
        
        # Replace forward method with checkpointed version
        module.forward = make_checkpointed_forward(original_forward)
        logger.info(f"Applied gradient checkpointing to module: {name}")
    
    logger.info(f"Applied gradient checkpointing to {len(layers_to_checkpoint)} out of {len(eligible_layers)} eligible layers")

def enable_transformer_checkpointing(model: torch.nn.Module) -> None:
    """
    Enable gradient checkpointing for transformer models from the Hugging Face library.
    
    This is a convenience function that applies the appropriate checkpointing
    configuration for common transformer models.
    
    Args:
        model: The transformer model to enable checkpointing for
    """
    # Check if it's a Hugging Face transformer model
    if hasattr(model, "gradient_checkpointing_enable"):
        model.gradient_checkpointing_enable()
        logger.info("Enabled gradient checkpointing using model's built-in method")
        return
    
    # For other transformer models, apply custom checkpointing
    apply_gradient_checkpointing(
        model,
        checkpoint_ratio=0.8,  # Higher ratio for transformers
        use_reentrant=True,
        preserve_rng_state=True,
        selective_checkpointing=True
    )

def estimate_memory_savings(
    model: torch.nn.Module,
    checkpoint_ratio: float = 0.5,
    input_shape: Optional[Tuple[int, ...]] = None,
    batch_size: int = 1
) -> Dict[str, float]:
    """
    Estimate memory savings from using gradient checkpointing.
    
    Args:
        model: The model to estimate memory savings for
        checkpoint_ratio: Ratio of layers to apply checkpointing to
        input_shape: Shape of input tensor (excluding batch dimension)
        batch_size: Batch size for estimation
        
    Returns:
        Dictionary with memory usage estimates
    """
    # Count total parameters
    total_params = sum(p.numel() for p in model.parameters())
    
    # Estimate activation memory (rough approximation)
    if input_shape is not None:
        # Estimate forward activation memory
        # This is a rough approximation based on common neural network architectures
        input_size = batch_size * torch.prod(torch.tensor(input_shape)).item()
        
        # Estimate activations as proportional to input size and parameter count
        # This is a heuristic that works reasonably well for many architectures
        activation_memory = input_size * 4  # Bytes per float32
        
        # For transformer models, activations are typically larger
        if any(isinstance(module, torch.nn.TransformerEncoderLayer) for module in model.modules()):
            activation_memory *= 2
        
        # Estimate memory savings from checkpointing
        # Checkpointing saves most activations at the cost of recomputation
        saved_memory = activation_memory * checkpoint_ratio * 0.8  # 80% efficiency factor
        
        # Convert to MB
        activation_memory_mb = activation_memory / (1024 * 1024)
        saved_memory_mb = saved_memory / (1024 * 1024)
        
        return {
            "total_params": total_params,
            "params_memory_mb": total_params * 4 / (1024 * 1024),  # 4 bytes per parameter
            "estimated_activation_memory_mb": activation_memory_mb,
            "estimated_saved_memory_mb": saved_memory_mb,
            "percentage_savings": (saved_memory / activation_memory) * 100
        }
    else:
        # Without input shape, provide a more conservative estimate
        # based only on parameter count
        params_memory = total_params * 4  # 4 bytes per parameter
        
        # Rough estimate: activations are typically 2-5x parameter memory
        activation_memory = params_memory * 3
        saved_memory = activation_memory * checkpoint_ratio * 0.8
        
        # Convert to MB
        params_memory_mb = params_memory / (1024 * 1024)
        activation_memory_mb = activation_memory / (1024 * 1024)
        saved_memory_mb = saved_memory / (1024 * 1024)
        
        return {
            "total_params": total_params,
            "params_memory_mb": params_memory_mb,
            "estimated_activation_memory_mb": activation_memory_mb,
            "estimated_saved_memory_mb": saved_memory_mb,
            "percentage_savings": (saved_memory / activation_memory) * 100
        }

def create_checkpointed_model(
    model_class: type,
    *args,
    checkpoint_ratio: float = 0.5,
    use_reentrant: bool = True,
    preserve_rng_state: bool = True,
    selective_checkpointing: bool = False,
    **kwargs
) -> torch.nn.Module:
    """
    Create a model with gradient checkpointing already applied.
    
    Args:
        model_class: The model class to instantiate
        *args: Arguments to pass to the model constructor
        checkpoint_ratio: Ratio of layers to apply checkpointing to
        use_reentrant: Whether to use reentrant checkpointing
        preserve_rng_state: Whether to preserve RNG state during checkpointing
        selective_checkpointing: Whether to selectively apply checkpointing
        **kwargs: Keyword arguments to pass to the model constructor
        
    Returns:
        Model instance with gradient checkpointing applied
    """
    # Create model instance
    model = model_class(*args, **kwargs)
    
    # Apply gradient checkpointing
    apply_gradient_checkpointing(
        model,
        checkpoint_ratio=checkpoint_ratio,
        use_reentrant=use_reentrant,
        preserve_rng_state=preserve_rng_state,
        selective_checkpointing=selective_checkpointing
    )
    
    return model
