"""
Pruning hooks module for KV cache pruning.

This module provides hooks for applying KV cache pruning to different
transformer model architectures, including GPT, BERT, T5, and others.
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
import logging
import time
import gc
import inspect
from functools import partial

from deep_research_core.utils.structured_logging import get_logger
from .kv_cache_pruning import KVCachePruner
from .token_importance import TokenImportanceCalculator, create_importance_calculator

# Create a logger
logger = get_logger(__name__)


class PruningHook:
    """
    Base class for KV cache pruning hooks.
    
    This class provides the basic functionality for hooking into
    transformer models to apply KV cache pruning.
    """
    
    def __init__(
        self,
        pruner: KVCachePruner,
        importance_calculator: Optional[TokenImportanceCalculator] = None,
        importance_method: str = 'attention',
        importance_kwargs: Optional[Dict[str, Any]] = None,
        device: Optional[str] = None
    ):
        """
        Initialize the pruning hook.
        
        Args:
            pruner: KV cache pruner to use
            importance_calculator: Token importance calculator to use
            importance_method: Method to use for importance calculation if no calculator is provided
            importance_kwargs: Additional arguments for importance calculator
            device: Device to use for tensor operations
        """
        self.pruner = pruner
        self.device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Create importance calculator if not provided
        if importance_calculator is None:
            importance_kwargs = importance_kwargs or {}
            self.importance_calculator = create_importance_calculator(
                importance_method, **importance_kwargs
            )
        else:
            self.importance_calculator = importance_calculator
        
        # Statistics
        self.hook_calls = 0
        self.pruning_calls = 0
        self.total_tokens_processed = 0
        self.total_tokens_pruned = 0
        self.last_pruning_time = 0
        self.attention_scores = []
        self.token_embeddings = []
    
    def __call__(self, module, inputs, outputs):
        """
        Hook function to be called during forward pass.
        
        Args:
            module: Module being processed
            inputs: Inputs to the module
            outputs: Outputs from the module
            
        Returns:
            Processed outputs
        """
        self.hook_calls += 1
        
        # Extract KV cache from module
        kv_cache = self._extract_kv_cache(module)
        
        if not kv_cache:
            return outputs
        
        # Extract current token index
        current_token_idx = self._extract_current_token_idx(module, inputs)
        
        # Extract attention scores
        attention_scores = self._extract_attention_scores(module, inputs, outputs)
        
        if attention_scores is not None:
            self.attention_scores.append(attention_scores)
        
        # Extract token embeddings
        token_embeddings = self._extract_token_embeddings(module, inputs, outputs)
        
        if token_embeddings is not None:
            self.token_embeddings.append(token_embeddings)
        
        # Calculate token importances
        token_importances = None
        if self.importance_calculator is not None:
            latest_attention = self.attention_scores[-1] if self.attention_scores else None
            latest_embeddings = self.token_embeddings[-1] if self.token_embeddings else None
            
            try:
                token_importances = self.importance_calculator.calculate_importance(
                    kv_cache.get('key'),
                    kv_cache.get('value'),
                    attention_scores=latest_attention,
                    token_embeddings=latest_embeddings
                )
            except Exception as e:
                logger.warning(f"Error calculating token importances: {str(e)}")
        
        # Apply pruning
        pruned_kv_cache = self.pruner.prune(
            kv_cache,
            attention_scores=attention_scores,
            token_importances=token_importances,
            current_token_idx=current_token_idx
        )
        
        # Update statistics
        if pruned_kv_cache != kv_cache:
            self.pruning_calls += 1
            self.last_pruning_time = time.time()
            
            # Calculate tokens pruned
            original_size = kv_cache.get('key').size(1)
            pruned_size = pruned_kv_cache.get('key').size(1)
            tokens_pruned = original_size - pruned_size
            
            self.total_tokens_processed += original_size
            self.total_tokens_pruned += tokens_pruned
        
        # Update KV cache in module
        self._update_kv_cache(module, pruned_kv_cache)
        
        return outputs
    
    def _extract_kv_cache(self, module) -> Dict[str, torch.Tensor]:
        """
        Extract KV cache from module.
        
        Args:
            module: Module to extract KV cache from
            
        Returns:
            Dictionary containing key and value tensors
        """
        # Default implementation - override in subclasses
        kv_cache = {}
        
        # Try to extract key and value tensors
        if hasattr(module, 'key_cache') and hasattr(module, 'value_cache'):
            kv_cache['key'] = module.key_cache
            kv_cache['value'] = module.value_cache
        elif hasattr(module, 'k_cache') and hasattr(module, 'v_cache'):
            kv_cache['key'] = module.k_cache
            kv_cache['value'] = module.v_cache
        elif hasattr(module, 'kv_cache'):
            if isinstance(module.kv_cache, dict):
                kv_cache = module.kv_cache
            elif isinstance(module.kv_cache, (list, tuple)) and len(module.kv_cache) >= 2:
                kv_cache['key'] = module.kv_cache[0]
                kv_cache['value'] = module.kv_cache[1]
        
        return kv_cache
    
    def _update_kv_cache(self, module, kv_cache: Dict[str, torch.Tensor]) -> None:
        """
        Update KV cache in module.
        
        Args:
            module: Module to update KV cache in
            kv_cache: Dictionary containing key and value tensors
        """
        # Default implementation - override in subclasses
        if hasattr(module, 'key_cache') and hasattr(module, 'value_cache'):
            module.key_cache = kv_cache.get('key')
            module.value_cache = kv_cache.get('value')
        elif hasattr(module, 'k_cache') and hasattr(module, 'v_cache'):
            module.k_cache = kv_cache.get('key')
            module.v_cache = kv_cache.get('value')
        elif hasattr(module, 'kv_cache'):
            if isinstance(module.kv_cache, dict):
                module.kv_cache = kv_cache
            elif isinstance(module.kv_cache, (list, tuple)) and len(module.kv_cache) >= 2:
                module.kv_cache = (kv_cache.get('key'), kv_cache.get('value'))
    
    def _extract_current_token_idx(self, module, inputs) -> int:
        """
        Extract current token index from inputs.
        
        Args:
            module: Module being processed
            inputs: Inputs to the module
            
        Returns:
            Current token index
        """
        # Default implementation - override in subclasses
        # Try to extract from inputs
        if isinstance(inputs, tuple) and len(inputs) > 0:
            # Check if first input is input_ids
            if isinstance(inputs[0], torch.Tensor) and inputs[0].dim() >= 2:
                return inputs[0].size(1) - 1
        
        # Fallback to module attributes
        if hasattr(module, 'current_token_idx'):
            return module.current_token_idx
        
        # Fallback to 0
        return 0
    
    def _extract_attention_scores(self, module, inputs, outputs) -> Optional[torch.Tensor]:
        """
        Extract attention scores from outputs.
        
        Args:
            module: Module being processed
            inputs: Inputs to the module
            outputs: Outputs from the module
            
        Returns:
            Attention scores tensor or None
        """
        # Default implementation - override in subclasses
        # Try to extract from outputs
        if isinstance(outputs, tuple) and len(outputs) > 0:
            # Check if first output is attention scores
            if isinstance(outputs[0], torch.Tensor) and outputs[0].dim() >= 3:
                return outputs[0].detach()
        
        # Fallback to module attributes
        if hasattr(module, 'attention_scores'):
            return module.attention_scores.detach()
        
        # Fallback to None
        return None
    
    def _extract_token_embeddings(self, module, inputs, outputs) -> Optional[torch.Tensor]:
        """
        Extract token embeddings from inputs or outputs.
        
        Args:
            module: Module being processed
            inputs: Inputs to the module
            outputs: Outputs from the module
            
        Returns:
            Token embeddings tensor or None
        """
        # Default implementation - override in subclasses
        # Try to extract from inputs
        if isinstance(inputs, tuple) and len(inputs) > 0:
            # Check if first input is token embeddings
            if isinstance(inputs[0], torch.Tensor) and inputs[0].dim() >= 2:
                return inputs[0].detach()
        
        # Try to extract from outputs
        if isinstance(outputs, tuple) and len(outputs) > 0:
            # Check if first output is token embeddings
            if isinstance(outputs[0], torch.Tensor) and outputs[0].dim() >= 2:
                return outputs[0].detach()
        
        # Fallback to module attributes
        if hasattr(module, 'token_embeddings'):
            return module.token_embeddings.detach()
        
        # Fallback to None
        return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get hook statistics.
        
        Returns:
            Dictionary of hook statistics
        """
        return {
            "hook_calls": self.hook_calls,
            "pruning_calls": self.pruning_calls,
            "total_tokens_processed": self.total_tokens_processed,
            "total_tokens_pruned": self.total_tokens_pruned,
            "last_pruning_time": self.last_pruning_time,
            "pruning_ratio": self.total_tokens_pruned / max(1, self.total_tokens_processed),
            "pruner_stats": self.pruner.get_statistics()
        }
    
    def reset_statistics(self) -> None:
        """Reset hook statistics."""
        self.hook_calls = 0
        self.pruning_calls = 0
        self.total_tokens_processed = 0
        self.total_tokens_pruned = 0
        self.last_pruning_time = 0
        self.attention_scores = []
        self.token_embeddings = []
        self.pruner.reset_statistics()


class GPTNeoXPruningHook(PruningHook):
    """
    Pruning hook for GPT-NeoX models.
    
    This hook is designed to work with GPT-NeoX models from the transformers library.
    """
    
    def _extract_kv_cache(self, module) -> Dict[str, torch.Tensor]:
        """
        Extract KV cache from GPT-NeoX module.
        
        Args:
            module: Module to extract KV cache from
            
        Returns:
            Dictionary containing key and value tensors
        """
        kv_cache = {}
        
        # GPT-NeoX specific extraction
        if hasattr(module, 'key_cache') and hasattr(module, 'value_cache'):
            kv_cache['key'] = module.key_cache
            kv_cache['value'] = module.value_cache
        elif hasattr(module, 'attention') and hasattr(module.attention, 'k_cache') and hasattr(module.attention, 'v_cache'):
            kv_cache['key'] = module.attention.k_cache
            kv_cache['value'] = module.attention.v_cache
        
        return kv_cache
    
    def _update_kv_cache(self, module, kv_cache: Dict[str, torch.Tensor]) -> None:
        """
        Update KV cache in GPT-NeoX module.
        
        Args:
            module: Module to update KV cache in
            kv_cache: Dictionary containing key and value tensors
        """
        # GPT-NeoX specific update
        if hasattr(module, 'key_cache') and hasattr(module, 'value_cache'):
            module.key_cache = kv_cache.get('key')
            module.value_cache = kv_cache.get('value')
        elif hasattr(module, 'attention') and hasattr(module.attention, 'k_cache') and hasattr(module.attention, 'v_cache'):
            module.attention.k_cache = kv_cache.get('key')
            module.attention.v_cache = kv_cache.get('value')


class LlamaModelPruningHook(PruningHook):
    """
    Pruning hook for LLaMA models.
    
    This hook is designed to work with LLaMA models from the transformers library.
    """
    
    def _extract_kv_cache(self, module) -> Dict[str, torch.Tensor]:
        """
        Extract KV cache from LLaMA module.
        
        Args:
            module: Module to extract KV cache from
            
        Returns:
            Dictionary containing key and value tensors
        """
        kv_cache = {}
        
        # LLaMA specific extraction
        if hasattr(module, 'past_key_value'):
            if isinstance(module.past_key_value, tuple) and len(module.past_key_value) >= 2:
                kv_cache['key'] = module.past_key_value[0]
                kv_cache['value'] = module.past_key_value[1]
        elif hasattr(module, 'self_attn') and hasattr(module.self_attn, 'past_key_value'):
            if isinstance(module.self_attn.past_key_value, tuple) and len(module.self_attn.past_key_value) >= 2:
                kv_cache['key'] = module.self_attn.past_key_value[0]
                kv_cache['value'] = module.self_attn.past_key_value[1]
        
        return kv_cache
    
    def _update_kv_cache(self, module, kv_cache: Dict[str, torch.Tensor]) -> None:
        """
        Update KV cache in LLaMA module.
        
        Args:
            module: Module to update KV cache in
            kv_cache: Dictionary containing key and value tensors
        """
        # LLaMA specific update
        if hasattr(module, 'past_key_value'):
            module.past_key_value = (kv_cache.get('key'), kv_cache.get('value'))
        elif hasattr(module, 'self_attn') and hasattr(module.self_attn, 'past_key_value'):
            module.self_attn.past_key_value = (kv_cache.get('key'), kv_cache.get('value'))


class GPT2PruningHook(PruningHook):
    """
    Pruning hook for GPT-2 models.
    
    This hook is designed to work with GPT-2 models from the transformers library.
    """
    
    def _extract_kv_cache(self, module) -> Dict[str, torch.Tensor]:
        """
        Extract KV cache from GPT-2 module.
        
        Args:
            module: Module to extract KV cache from
            
        Returns:
            Dictionary containing key and value tensors
        """
        kv_cache = {}
        
        # GPT-2 specific extraction
        if hasattr(module, 'past_key_values'):
            if isinstance(module.past_key_values, list) and len(module.past_key_values) > 0:
                layer_idx = getattr(module, 'layer_idx', 0)
                if layer_idx < len(module.past_key_values) and isinstance(module.past_key_values[layer_idx], tuple) and len(module.past_key_values[layer_idx]) >= 2:
                    kv_cache['key'] = module.past_key_values[layer_idx][0]
                    kv_cache['value'] = module.past_key_values[layer_idx][1]
        elif hasattr(module, 'attn') and hasattr(module.attn, 'past_key_value'):
            if isinstance(module.attn.past_key_value, tuple) and len(module.attn.past_key_value) >= 2:
                kv_cache['key'] = module.attn.past_key_value[0]
                kv_cache['value'] = module.attn.past_key_value[1]
        
        return kv_cache
    
    def _update_kv_cache(self, module, kv_cache: Dict[str, torch.Tensor]) -> None:
        """
        Update KV cache in GPT-2 module.
        
        Args:
            module: Module to update KV cache in
            kv_cache: Dictionary containing key and value tensors
        """
        # GPT-2 specific update
        if hasattr(module, 'past_key_values'):
            if isinstance(module.past_key_values, list) and len(module.past_key_values) > 0:
                layer_idx = getattr(module, 'layer_idx', 0)
                if layer_idx < len(module.past_key_values):
                    module.past_key_values[layer_idx] = (kv_cache.get('key'), kv_cache.get('value'))
        elif hasattr(module, 'attn') and hasattr(module.attn, 'past_key_value'):
            module.attn.past_key_value = (kv_cache.get('key'), kv_cache.get('value'))


class T5PruningHook(PruningHook):
    """
    Pruning hook for T5 models.
    
    This hook is designed to work with T5 models from the transformers library.
    """
    
    def _extract_kv_cache(self, module) -> Dict[str, torch.Tensor]:
        """
        Extract KV cache from T5 module.
        
        Args:
            module: Module to extract KV cache from
            
        Returns:
            Dictionary containing key and value tensors
        """
        kv_cache = {}
        
        # T5 specific extraction
        if hasattr(module, 'layer_past'):
            if isinstance(module.layer_past, tuple) and len(module.layer_past) >= 2:
                kv_cache['key'] = module.layer_past[0]
                kv_cache['value'] = module.layer_past[1]
        elif hasattr(module, 'decoder') and hasattr(module.decoder, 'layer_past'):
            if isinstance(module.decoder.layer_past, tuple) and len(module.decoder.layer_past) >= 2:
                kv_cache['key'] = module.decoder.layer_past[0]
                kv_cache['value'] = module.decoder.layer_past[1]
        
        return kv_cache
    
    def _update_kv_cache(self, module, kv_cache: Dict[str, torch.Tensor]) -> None:
        """
        Update KV cache in T5 module.
        
        Args:
            module: Module to update KV cache in
            kv_cache: Dictionary containing key and value tensors
        """
        # T5 specific update
        if hasattr(module, 'layer_past'):
            module.layer_past = (kv_cache.get('key'), kv_cache.get('value'))
        elif hasattr(module, 'decoder') and hasattr(module.decoder, 'layer_past'):
            module.decoder.layer_past = (kv_cache.get('key'), kv_cache.get('value'))


# Factory function to create pruning hooks
def create_pruning_hook(
    model_type: str,
    pruner: KVCachePruner,
    importance_calculator: Optional[TokenImportanceCalculator] = None,
    importance_method: str = 'attention',
    importance_kwargs: Optional[Dict[str, Any]] = None,
    **kwargs
) -> PruningHook:
    """
    Create a pruning hook for a specific model type.
    
    Args:
        model_type: Type of model to create hook for
        pruner: KV cache pruner to use
        importance_calculator: Token importance calculator to use
        importance_method: Method to use for importance calculation if no calculator is provided
        importance_kwargs: Additional arguments for importance calculator
        **kwargs: Additional arguments for the hook
        
    Returns:
        Pruning hook for the specified model type
    """
    if model_type.lower() in ['gpt-neox', 'gptneox', 'neox']:
        return GPTNeoXPruningHook(
            pruner, importance_calculator, importance_method, importance_kwargs, **kwargs
        )
    elif model_type.lower() in ['llama', 'llama2', 'llama3']:
        return LlamaModelPruningHook(
            pruner, importance_calculator, importance_method, importance_kwargs, **kwargs
        )
    elif model_type.lower() in ['gpt2', 'gpt-2']:
        return GPT2PruningHook(
            pruner, importance_calculator, importance_method, importance_kwargs, **kwargs
        )
    elif model_type.lower() in ['t5', 't5-base', 't5-large']:
        return T5PruningHook(
            pruner, importance_calculator, importance_method, importance_kwargs, **kwargs
        )
    else:
        # Default to base hook
        return PruningHook(
            pruner, importance_calculator, importance_method, importance_kwargs, **kwargs
        )


def apply_pruning_hooks(
    model: torch.nn.Module,
    pruner: KVCachePruner,
    model_type: Optional[str] = None,
    importance_calculator: Optional[TokenImportanceCalculator] = None,
    importance_method: str = 'attention',
    importance_kwargs: Optional[Dict[str, Any]] = None,
    module_filter: Optional[Callable[[str, torch.nn.Module], bool]] = None
) -> List[Tuple[str, torch.nn.Module, torch.utils.hooks.RemovableHandle]]:
    """
    Apply pruning hooks to a model.
    
    Args:
        model: Model to apply hooks to
        pruner: KV cache pruner to use
        model_type: Type of model
        importance_calculator: Token importance calculator to use
        importance_method: Method to use for importance calculation if no calculator is provided
        importance_kwargs: Additional arguments for importance calculator
        module_filter: Function to filter modules to apply hooks to
        
    Returns:
        List of (name, module, hook_handle) tuples
    """
    # Detect model type if not provided
    if model_type is None:
        model_type = _detect_model_type(model)
    
    # Create hook
    hook = create_pruning_hook(
        model_type, pruner, importance_calculator, importance_method, importance_kwargs
    )
    
    # Default module filter
    if module_filter is None:
        def default_filter(name, module):
            return (
                'attention' in name.lower() or
                'self_attn' in name.lower() or
                'decoder' in name.lower()
            )
        module_filter = default_filter
    
    # Apply hooks
    hooks = []
    for name, module in model.named_modules():
        if module_filter(name, module):
            handle = module.register_forward_hook(hook)
            hooks.append((name, module, handle))
    
    logger.info(f"Applied {len(hooks)} pruning hooks to model")
    
    return hooks


def _detect_model_type(model: torch.nn.Module) -> str:
    """
    Detect the type of a model.
    
    Args:
        model: Model to detect type of
        
    Returns:
        Model type
    """
    model_class = model.__class__.__name__.lower()
    
    if 'gpt2' in model_class:
        return 'gpt2'
    elif 'neox' in model_class:
        return 'gpt-neox'
    elif 'llama' in model_class:
        return 'llama'
    elif 't5' in model_class:
        return 't5'
    else:
        # Default to base
        return 'base'
