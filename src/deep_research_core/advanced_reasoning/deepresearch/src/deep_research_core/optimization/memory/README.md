# Memory Optimization Module

This module provides tools and utilities for optimizing memory usage in large language models, including KV cache pruning, quantization, and other memory-efficient techniques.

## KV Cache Pruning

KV cache pruning is a technique to reduce memory usage during inference with transformer models by selectively removing or compressing key-value pairs in the attention cache.

### Available Pruning Strategies

1. **Attention-Based Pruning**: Removes tokens with the lowest attention scores, keeping only the most attended-to tokens.
2. **Sliding Window Pruning**: Keeps only the most recent tokens and a selection of important tokens from earlier in the sequence.
3. **Importance-Based Pruning**: Removes tokens with the lowest importance scores, which can be computed based on various factors.
4. **Adaptive Pruning**: Dynamically selects the best pruning method based on current memory usage and performance requirements.

### Usage Examples

#### Basic Usage

```python
from deep_research_core.optimization.memory import KVCachePruner, SlidingWindowPruning
from transformers import AutoModelForCausalLM, AutoTokenizer

# Load model and tokenizer
model = AutoModelForCausalLM.from_pretrained("gpt2")
tokenizer = AutoTokenizer.from_pretrained("gpt2")

# Create a pruner
pruner = SlidingWindowPruning(
    window_size=1024,
    stride=512,
    keep_first_n=5,
    max_cache_size=2048,
    pruning_interval=1024,
    min_tokens_to_keep=256
)

# Tokenize input
input_text = "This is a long input text that will be used to test KV cache pruning."
input_ids = tokenizer.encode(input_text, return_tensors="pt")

# Apply pruning during generation
from deep_research_core.optimization.memory.kv_cache_pruning import apply_kv_cache_pruning

output_ids, pruning_stats = apply_kv_cache_pruning(
    model,
    pruner,
    input_ids,
    max_length=200,
    do_sample=True,
    temperature=0.7
)

# Decode output
output_text = tokenizer.decode(output_ids[0], skip_special_tokens=True)
print(output_text)

# Print pruning statistics
print(f"Pruning statistics: {pruning_stats}")
```

#### Attention-Based Pruning

```python
from deep_research_core.optimization.memory import AttentionBasedPruning

# Create an attention-based pruner
pruner = AttentionBasedPruning(
    attention_threshold=0.01,
    use_cumulative_attention=True,
    max_cache_size=2048,
    pruning_interval=1024,
    min_tokens_to_keep=256
)

# Apply pruning during generation
output_ids, pruning_stats = apply_kv_cache_pruning(
    model,
    pruner,
    input_ids,
    max_length=200
)
```

#### Importance-Based Pruning

```python
from deep_research_core.optimization.memory import ImportanceBasedPruning

# Define a custom importance function
def custom_importance_fn(key_tensor, value_tensor, attention_scores):
    # Calculate token importance based on key and value norms
    key_norm = torch.norm(key_tensor, dim=-1).mean(dim=(0, 2))
    value_norm = torch.norm(value_tensor, dim=-1).mean(dim=(0, 2))
    attention_importance = attention_scores.mean(dim=(0, 2))
    
    # Combine factors
    importance = key_norm * 0.3 + value_norm * 0.3 + attention_importance * 0.4
    return importance

# Create an importance-based pruner
pruner = ImportanceBasedPruning(
    importance_threshold=0.1,
    importance_fn=custom_importance_fn,
    max_cache_size=2048,
    pruning_interval=1024,
    min_tokens_to_keep=256
)

# Apply pruning during generation
output_ids, pruning_stats = apply_kv_cache_pruning(
    model,
    pruner,
    input_ids,
    max_length=200
)
```

#### Adaptive Pruning

```python
from deep_research_core.optimization.memory import AdaptivePruning

# Create an adaptive pruner
pruner = AdaptivePruning(
    memory_threshold=0.8,  # 80% memory usage
    performance_weight=0.5,  # Balance between memory and performance
    max_cache_size=2048,
    pruning_interval=1024,
    min_tokens_to_keep=256
)

# Apply pruning during generation
output_ids, pruning_stats = apply_kv_cache_pruning(
    model,
    pruner,
    input_ids,
    max_length=200
)
```

### Estimating Memory Savings

```python
from deep_research_core.optimization.memory.kv_cache_pruning import estimate_memory_savings

# Estimate memory savings from pruning
memory_stats = estimate_memory_savings(
    model,
    pruner,
    input_ids,
    output_length=100
)

print(f"Memory without pruning: {memory_stats['memory_without_pruning'] / 1e6:.2f} MB")
print(f"Memory with pruning: {memory_stats['memory_with_pruning'] / 1e6:.2f} MB")
print(f"Memory savings: {memory_stats['memory_savings'] / 1e6:.2f} MB ({memory_stats['memory_savings_percent']:.2f}%)")
print(f"Performance impact: {memory_stats['performance_impact_percent']:.2f}%")
```

### Integration with Hugging Face Transformers

```python
from transformers import AutoModelForCausalLM, AutoTokenizer
from deep_research_core.optimization.memory import SlidingWindowPruning
from deep_research_core.optimization.memory.kv_cache_pruning import apply_kv_cache_pruning

# Load model and tokenizer
model = AutoModelForCausalLM.from_pretrained("gpt2")
tokenizer = AutoTokenizer.from_pretrained("gpt2")

# Create a pruner
pruner = SlidingWindowPruning(
    window_size=1024,
    max_cache_size=2048,
    pruning_interval=1024
)

# Prepare conversation
conversation = [
    {"role": "system", "content": "You are a helpful assistant."},
    {"role": "user", "content": "Tell me about KV cache pruning in transformer models."}
]

# Tokenize conversation
input_text = tokenizer.apply_chat_template(conversation, tokenize=False)
input_ids = tokenizer.encode(input_text, return_tensors="pt")

# Apply pruning during generation
output_ids, _ = apply_kv_cache_pruning(
    model,
    pruner,
    input_ids,
    max_length=input_ids.size(1) + 500,
    do_sample=True,
    temperature=0.7
)

# Decode output
output_text = tokenizer.decode(output_ids[0][input_ids.size(1):], skip_special_tokens=True)
print(output_text)
```

## Performance Considerations

- KV cache pruning introduces some computational overhead, but this is typically outweighed by the memory savings for long sequences.
- The choice of pruning strategy depends on the specific use case:
  - **Attention-Based Pruning**: Best for tasks where attention patterns are informative (e.g., summarization, QA).
  - **Sliding Window Pruning**: Best for tasks with local dependencies (e.g., chat, code completion).
  - **Importance-Based Pruning**: Best for tasks with varying token importance (e.g., document analysis).
  - **Adaptive Pruning**: Best for general-purpose applications with varying memory constraints.
- For maximum efficiency, adjust the pruning parameters based on your specific model and hardware constraints.

## Advanced Configuration

### Customizing Pruning Behavior

```python
# Create a pruner with custom configuration
pruner = AttentionBasedPruning(
    # Pruning threshold
    attention_threshold=0.005,  # Lower threshold keeps more tokens
    
    # Whether to use cumulative attention scores
    use_cumulative_attention=True,  # Accumulate attention scores over time
    
    # Cache size limits
    max_cache_size=4096,  # Maximum number of tokens to keep before forced pruning
    
    # Pruning frequency
    pruning_interval=2048,  # Number of tokens after which to perform pruning
    
    # Safety parameters
    min_tokens_to_keep=512,  # Minimum number of tokens to keep after pruning
    
    # Device
    device="cuda"  # Device to use for tensor operations
)
```

### Creating Custom Pruning Strategies

You can create custom pruning strategies by subclassing `KVCachePruner`:

```python
from deep_research_core.optimization.memory import KVCachePruner
import torch

class CustomPruningStrategy(KVCachePruner):
    def __init__(self, custom_param=0.5, **kwargs):
        super().__init__(**kwargs)
        self.custom_param = custom_param
    
    def prune(
        self,
        kv_cache,
        attention_scores=None,
        token_importances=None,
        current_token_idx=0
    ):
        if not self.should_prune(current_token_idx):
            return kv_cache
        
        # Get the key and value tensors
        key_tensor = kv_cache.get('key')
        value_tensor = kv_cache.get('value')
        
        if key_tensor is None or value_tensor is None:
            return kv_cache
        
        # Record original size
        original_size = key_tensor.size(1)
        
        # Implement your custom pruning logic here
        # ...
        
        # Example: Keep random tokens plus the first and last few
        keep_count = max(self.min_tokens_to_keep, int(original_size * self.custom_param))
        keep_mask = torch.zeros(original_size, dtype=torch.bool, device=key_tensor.device)
        
        # Always keep first few tokens
        keep_mask[:min(10, original_size)] = True
        
        # Always keep last few tokens
        keep_mask[-min(100, original_size):] = True
        
        # Randomly select additional tokens to keep
        remaining_count = keep_count - keep_mask.sum().item()
        if remaining_count > 0:
            remaining_indices = torch.where(~keep_mask)[0]
            if len(remaining_indices) > 0:
                selected_indices = remaining_indices[torch.randperm(len(remaining_indices))[:remaining_count]]
                keep_mask[selected_indices] = True
        
        # Prune the KV cache
        pruned_key = key_tensor[:, keep_mask, :]
        pruned_value = value_tensor[:, keep_mask, :]
        
        # Create the pruned KV cache
        pruned_kv_cache = {
            'key': pruned_key,
            'value': pruned_value
        }
        
        # Update statistics
        pruned_size = pruned_key.size(1)
        self.update_statistics(original_size, pruned_size)
        
        return pruned_kv_cache
```
