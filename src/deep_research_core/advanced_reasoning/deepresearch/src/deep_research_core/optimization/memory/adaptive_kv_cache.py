"""
Adaptive KV Cache module for optimizing memory usage in transformer models.

This module provides an implementation of adaptive KV cache that dynamically
adjusts cache size based on memory usage, sequence length, and performance metrics.
"""

import torch
import numpy as np
import time
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
import gc
from dataclasses import dataclass

from .kv_cache_pruning import KVCachePruner, SlidingWindowPruning
from ...utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)


@dataclass
class CacheStats:
    """Statistics for adaptive KV cache."""
    initial_size: int = 0
    current_size: int = 0
    resize_count: int = 0
    total_tokens_processed: int = 0
    memory_saved_mb: float = 0.0
    last_resize_time: float = 0.0
    avg_token_size_bytes: float = 0.0
    peak_memory_usage: float = 0.0


class AdaptiveKVCache:
    """
    Adaptive KV cache that dynamically adjusts cache size based on runtime conditions.
    
    This class provides a KV cache implementation that can:
    1. Dynamically resize based on memory pressure
    2. Adapt to sequence length requirements
    3. Monitor performance and adjust accordingly
    4. Integrate with existing KV cache pruning strategies
    """
    
    def __init__(
        self,
        initial_size: int = 2048,
        min_size: int = 512,
        max_size: int = 8192,
        growth_factor: float = 1.5,
        shrink_factor: float = 0.8,
        memory_threshold: float = 0.85,
        resize_cooldown: float = 1.0,  # seconds
        pruner: Optional[KVCachePruner] = None,
        device: Optional[str] = None
    ):
        """
        Initialize adaptive KV cache.
        
        Args:
            initial_size: Initial cache size (number of tokens)
            min_size: Minimum cache size (number of tokens)
            max_size: Maximum cache size (number of tokens)
            growth_factor: Factor to grow cache by when needed
            shrink_factor: Factor to shrink cache by when needed
            memory_threshold: Memory usage threshold to trigger resizing (0.0-1.0)
            resize_cooldown: Minimum time between resize operations (seconds)
            pruner: Optional KV cache pruner to use for pruning
            device: Device to use for tensor operations
        """
        self.initial_size = initial_size
        self.min_size = min_size
        self.max_size = max_size
        self.growth_factor = growth_factor
        self.shrink_factor = shrink_factor
        self.memory_threshold = memory_threshold
        self.resize_cooldown = resize_cooldown
        self.device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Use default pruner if none provided
        self.pruner = pruner or SlidingWindowPruning(
            window_size=min_size,
            max_cache_size=initial_size,
            pruning_interval=min_size // 2
        )
        
        # Initialize cache
        self.cache = None
        self.current_size = initial_size
        
        # Statistics
        self.stats = CacheStats(
            initial_size=initial_size,
            current_size=initial_size
        )
        
        logger.info(
            f"Initialized AdaptiveKVCache with initial_size={initial_size}, "
            f"min_size={min_size}, max_size={max_size}, memory_threshold={memory_threshold}"
        )
    
    def allocate(
        self,
        batch_size: int,
        seq_len: int,
        num_heads: int,
        head_dim: int
    ) -> Dict[str, torch.Tensor]:
        """
        Allocate KV cache with appropriate size.
        
        Args:
            batch_size: Batch size
            seq_len: Initial sequence length
            num_heads: Number of attention heads
            head_dim: Dimension of each attention head
            
        Returns:
            Dictionary containing key and value tensors
        """
        # Calculate initial size based on parameters
        target_size = min(max(seq_len * 2, self.min_size), self.max_size)
        
        # Update current size if needed
        if target_size != self.current_size:
            self.current_size = target_size
            self.stats.current_size = target_size
            logger.info(f"Adjusted initial cache size to {target_size} tokens")
        
        # Allocate tensors
        key_tensor = torch.zeros(
            (batch_size, target_size, num_heads, head_dim),
            device=self.device
        )
        value_tensor = torch.zeros(
            (batch_size, target_size, num_heads, head_dim),
            device=self.device
        )
        
        # Create cache
        self.cache = {
            'key': key_tensor,
            'value': value_tensor
        }
        
        # Calculate average token size in bytes
        key_bytes = key_tensor.element_size() * key_tensor.numel() / target_size
        value_bytes = value_tensor.element_size() * value_tensor.numel() / target_size
        self.stats.avg_token_size_bytes = (key_bytes + value_bytes) / batch_size
        
        logger.info(
            f"Allocated KV cache with shape: key={key_tensor.shape}, value={value_tensor.shape}, "
            f"avg_token_size={self.stats.avg_token_size_bytes/1024:.2f} KB"
        )
        
        return self.cache
    
    def resize(
        self,
        kv_cache: Dict[str, torch.Tensor],
        current_token_idx: int,
        force: bool = False
    ) -> Dict[str, torch.Tensor]:
        """
        Resize cache based on current conditions.
        
        Args:
            kv_cache: Current KV cache
            current_token_idx: Current token index
            force: Whether to force resize regardless of cooldown
            
        Returns:
            Resized KV cache
        """
        # Check if resize is needed
        if not force and time.time() - self.stats.last_resize_time < self.resize_cooldown:
            return kv_cache
        
        # Get current memory usage
        current_memory_usage = self._get_memory_usage()
        
        # Update peak memory usage
        self.stats.peak_memory_usage = max(self.stats.peak_memory_usage, current_memory_usage)
        
        # Check if we need to resize
        if current_memory_usage > self.memory_threshold or force:
            # Need to shrink
            new_size = max(int(self.current_size * self.shrink_factor), self.min_size)
            
            if new_size < self.current_size:
                logger.info(
                    f"Shrinking cache from {self.current_size} to {new_size} tokens "
                    f"(memory usage: {current_memory_usage:.2%})"
                )
                
                # Prune cache to new size
                pruned_cache = self._prune_to_size(kv_cache, new_size, current_token_idx)
                
                # Update statistics
                self.stats.resize_count += 1
                self.stats.last_resize_time = time.time()
                self.stats.current_size = new_size
                self.current_size = new_size
                
                # Calculate memory saved
                old_size_bytes = self.current_size * self.stats.avg_token_size_bytes
                new_size_bytes = new_size * self.stats.avg_token_size_bytes
                self.stats.memory_saved_mb += (old_size_bytes - new_size_bytes) / (1024 * 1024)
                
                return pruned_cache
        elif current_memory_usage < self.memory_threshold * 0.7 and self.current_size < self.max_size:
            # Room to grow
            new_size = min(int(self.current_size * self.growth_factor), self.max_size)
            
            if new_size > self.current_size:
                logger.info(
                    f"Growing cache from {self.current_size} to {new_size} tokens "
                    f"(memory usage: {current_memory_usage:.2%})"
                )
                
                # Create new larger cache
                grown_cache = self._grow_cache(kv_cache, new_size)
                
                # Update statistics
                self.stats.resize_count += 1
                self.stats.last_resize_time = time.time()
                self.stats.current_size = new_size
                self.current_size = new_size
                
                return grown_cache
        
        # No resize needed
        return kv_cache
    
    def update(
        self,
        kv_cache: Dict[str, torch.Tensor],
        current_token_idx: int,
        performance_metrics: Optional[Dict[str, float]] = None
    ) -> Dict[str, torch.Tensor]:
        """
        Update cache based on performance metrics.
        
        Args:
            kv_cache: Current KV cache
            current_token_idx: Current token index
            performance_metrics: Optional performance metrics
            
        Returns:
            Updated KV cache
        """
        # Update token count
        self.stats.total_tokens_processed = current_token_idx
        
        # Check if we need to resize based on sequence length
        if current_token_idx > self.current_size * 0.8:
            # Getting close to capacity, resize if possible
            return self.resize(kv_cache, current_token_idx, force=True)
        
        # Check if we need to resize based on memory usage
        return self.resize(kv_cache, current_token_idx)
    
    def get_cache(self) -> Dict[str, torch.Tensor]:
        """
        Get the current cache.
        
        Returns:
            Current KV cache
        """
        return self.cache
    
    def get_stats(self) -> CacheStats:
        """
        Get cache statistics.
        
        Returns:
            Cache statistics
        """
        return self.stats
    
    def _get_memory_usage(self) -> float:
        """
        Get current memory usage as a fraction (0.0-1.0).
        
        Returns:
            Memory usage fraction
        """
        if torch.cuda.is_available() and self.device.startswith('cuda'):
            # Get CUDA memory usage
            allocated = torch.cuda.memory_allocated(0)
            reserved = torch.cuda.memory_reserved(0)
            total = torch.cuda.get_device_properties(0).total_memory
            return allocated / total
        else:
            # CPU memory usage is harder to track accurately
            # Return a conservative estimate
            return 0.7
    
    def _prune_to_size(
        self,
        kv_cache: Dict[str, torch.Tensor],
        target_size: int,
        current_token_idx: int
    ) -> Dict[str, torch.Tensor]:
        """
        Prune cache to target size.
        
        Args:
            kv_cache: Current KV cache
            target_size: Target size in tokens
            current_token_idx: Current token index
            
        Returns:
            Pruned KV cache
        """
        # Get current size
        key_tensor = kv_cache.get('key')
        if key_tensor is None:
            return kv_cache
        
        current_size = key_tensor.size(1)
        
        # If already smaller than target, return as is
        if current_size <= target_size:
            return kv_cache
        
        # Use pruner to prune cache
        # Temporarily adjust pruner parameters
        original_max_cache_size = self.pruner.max_cache_size
        self.pruner.max_cache_size = target_size
        
        # Prune cache
        pruned_cache = self.pruner.prune(
            kv_cache,
            current_token_idx=current_token_idx
        )
        
        # Restore pruner parameters
        self.pruner.max_cache_size = original_max_cache_size
        
        return pruned_cache
    
    def _grow_cache(
        self,
        kv_cache: Dict[str, torch.Tensor],
        target_size: int
    ) -> Dict[str, torch.Tensor]:
        """
        Grow cache to target size.
        
        Args:
            kv_cache: Current KV cache
            target_size: Target size in tokens
            
        Returns:
            Grown KV cache
        """
        # Get current tensors
        key_tensor = kv_cache.get('key')
        value_tensor = kv_cache.get('value')
        
        if key_tensor is None or value_tensor is None:
            return kv_cache
        
        # Get current size and shape
        batch_size, current_size, num_heads, head_dim = key_tensor.shape
        
        # If already larger than target, return as is
        if current_size >= target_size:
            return kv_cache
        
        # Create new larger tensors
        new_key = torch.zeros(
            (batch_size, target_size, num_heads, head_dim),
            device=key_tensor.device,
            dtype=key_tensor.dtype
        )
        new_value = torch.zeros(
            (batch_size, target_size, num_heads, head_dim),
            device=value_tensor.device,
            dtype=value_tensor.dtype
        )
        
        # Copy existing data
        new_key[:, :current_size, :, :] = key_tensor
        new_value[:, :current_size, :, :] = value_tensor
        
        # Create new cache
        grown_cache = {
            'key': new_key,
            'value': new_value
        }
        
        return grown_cache


def apply_adaptive_kv_cache(
    model,
    adaptive_cache: AdaptiveKVCache,
    input_ids: torch.Tensor,
    attention_mask: Optional[torch.Tensor] = None,
    max_length: int = 100,
    **generation_kwargs
) -> Tuple[torch.Tensor, Dict[str, Any]]:
    """
    Apply adaptive KV cache during model generation.
    
    Args:
        model: The model to generate with
        adaptive_cache: AdaptiveKVCache instance
        input_ids: Input token IDs
        attention_mask: Optional attention mask
        max_length: Maximum generation length
        **generation_kwargs: Additional generation arguments
        
    Returns:
        Tuple of (output_ids, cache_stats)
    """
    # Get model parameters
    batch_size = input_ids.size(0)
    seq_len = input_ids.size(1)
    
    # Determine model dimensions
    # This is a heuristic and may need adjustment for different models
    if hasattr(model, 'config'):
        num_heads = getattr(model.config, 'num_attention_heads', 12)
        head_dim = getattr(model.config, 'hidden_size', 768) // num_heads
    else:
        # Default values
        num_heads = 12
        head_dim = 64
    
    # Allocate initial cache
    kv_cache = adaptive_cache.allocate(batch_size, seq_len, num_heads, head_dim)
    
    # Hook to update cache during generation
    def update_cache_hook(module, input, output):
        nonlocal kv_cache
        
        # Get current token index
        current_token_idx = input_ids.size(1)
        
        # Update cache
        kv_cache = adaptive_cache.update(kv_cache, current_token_idx)
        
        return output
    
    # Register hook on attention modules
    hooks = []
    for name, module in model.named_modules():
        if 'attention' in name.lower() and hasattr(module, 'forward'):
            hook = module.register_forward_hook(update_cache_hook)
            hooks.append(hook)
    
    try:
        # Generate with the model
        output_ids = model.generate(
            input_ids,
            attention_mask=attention_mask,
            max_length=max_length,
            **generation_kwargs
        )
    finally:
        # Remove hooks
        for hook in hooks:
            hook.remove()
    
    # Get final statistics
    cache_stats = adaptive_cache.get_stats()
    
    return output_ids, cache_stats.__dict__


def estimate_optimal_cache_size(
    model,
    input_ids: torch.Tensor,
    target_memory_usage: float = 0.7,
    min_size: int = 512,
    max_size: int = 8192,
    device: Optional[str] = None
) -> int:
    """
    Estimate optimal cache size for a model and input.
    
    Args:
        model: The model to estimate for
        input_ids: Sample input token IDs
        target_memory_usage: Target memory usage (0.0-1.0)
        min_size: Minimum cache size
        max_size: Maximum cache size
        device: Device to use
        
    Returns:
        Estimated optimal cache size
    """
    device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Determine model dimensions
    if hasattr(model, 'config'):
        num_heads = getattr(model.config, 'num_attention_heads', 12)
        head_dim = getattr(model.config, 'hidden_size', 768) // num_heads
    else:
        # Default values
        num_heads = 12
        head_dim = 64
    
    # Get batch size and sequence length
    batch_size = input_ids.size(0)
    seq_len = input_ids.size(1)
    
    # Start with a conservative estimate
    initial_estimate = min(max(seq_len * 2, min_size), max_size)
    
    # Create a test tensor to measure memory impact
    if torch.cuda.is_available() and device.startswith('cuda'):
        # Clear cache
        torch.cuda.empty_cache()
        gc.collect()
        
        # Measure baseline memory
        baseline_memory = torch.cuda.memory_allocated(0)
        
        # Create test tensors
        key_tensor = torch.zeros(
            (batch_size, initial_estimate, num_heads, head_dim),
            device=device
        )
        value_tensor = torch.zeros(
            (batch_size, initial_estimate, num_heads, head_dim),
            device=device
        )
        
        # Measure memory with tensors
        with_tensors_memory = torch.cuda.memory_allocated(0)
        
        # Calculate memory per token
        memory_per_token = (with_tensors_memory - baseline_memory) / initial_estimate
        
        # Calculate available memory
        total_memory = torch.cuda.get_device_properties(0).total_memory
        available_memory = total_memory * target_memory_usage - baseline_memory
        
        # Calculate optimal size
        optimal_size = int(available_memory / memory_per_token)
        
        # Clean up
        del key_tensor, value_tensor
        torch.cuda.empty_cache()
        gc.collect()
    else:
        # For CPU, use a simple heuristic
        optimal_size = initial_estimate
    
    # Clamp to min/max
    optimal_size = min(max(optimal_size, min_size), max_size)
    
    return optimal_size
