"""
Chunked Attention implementation for Deep Research Core.

This module provides an implementation of chunked attention, which processes
attention in smaller chunks to reduce memory usage during inference and training.
"""

import torch
import math
from typing import Dict, List, Optional, Tuple, Union, Any
from ...utils.structured_logging import get_logger

logger = get_logger(__name__)

class ChunkedAttention:
    """
    Implements chunked attention to reduce memory usage.

    Chunked attention processes the attention computation in smaller chunks,
    reducing peak memory usage at the cost of slightly increased computation time.
    This is particularly useful for long sequences where the attention matrix
    would otherwise be too large to fit in memory.
    """

    def __init__(
        self,
        chunk_size: int = 128,
        use_flash_attention: bool = True,
        optimize_backward_pass: bool = True,
        gradient_checkpointing: bool = False,
        memory_efficient_qk: bool = True
    ):
        """
        Initialize chunked attention.

        Args:
            chunk_size: Size of chunks for processing attention
            use_flash_attention: Whether to use flash attention when available
            optimize_backward_pass: Whether to optimize the backward pass
            gradient_checkpointing: Whether to use gradient checkpointing
            memory_efficient_qk: Whether to use memory-efficient QK computation
        """
        self.chunk_size = chunk_size
        self.use_flash_attention = use_flash_attention
        self.optimize_backward_pass = optimize_backward_pass
        self.gradient_checkpointing = gradient_checkpointing
        self.memory_efficient_qk = memory_efficient_qk

        # Check if flash attention is available
        self.flash_attn_available = False
        try:
            import flash_attn
            self.flash_attn_available = True
        except ImportError:
            if use_flash_attention:
                logger.warning("Flash Attention requested but not available. Install with: pip install flash-attn")

    def apply(
        self,
        query: torch.Tensor,
        key: torch.Tensor,
        value: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        head_mask: Optional[torch.Tensor] = None,
        output_attentions: bool = False
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        Apply chunked attention.

        Args:
            query: Query tensor [batch_size, num_heads, seq_len, head_dim]
            key: Key tensor [batch_size, num_heads, seq_len, head_dim]
            value: Value tensor [batch_size, num_heads, seq_len, head_dim]
            attention_mask: Optional attention mask [batch_size, 1, seq_len, seq_len]
            head_mask: Optional head mask [batch_size, num_heads, seq_len, seq_len]
            output_attentions: Whether to output attention weights

        Returns:
            Tuple of (context tensor, attention weights if output_attentions=True)
        """
        # Use flash attention if available and requested
        if self.use_flash_attention and self.flash_attn_available:
            return self._apply_flash_attention(query, key, value, attention_mask, head_mask, output_attentions)

        # Fall back to chunked attention implementation
        return self._apply_chunked_attention(query, key, value, attention_mask, head_mask, output_attentions)

    def _apply_flash_attention(
        self,
        query: torch.Tensor,
        key: torch.Tensor,
        value: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        head_mask: Optional[torch.Tensor] = None,
        output_attentions: bool = False
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        Apply flash attention.

        Args:
            query: Query tensor [batch_size, num_heads, seq_len, head_dim]
            key: Key tensor [batch_size, num_heads, seq_len, head_dim]
            value: Value tensor [batch_size, num_heads, seq_len, head_dim]
            attention_mask: Optional attention mask [batch_size, 1, seq_len, seq_len]
            head_mask: Optional head mask [batch_size, num_heads, seq_len, seq_len]
            output_attentions: Whether to output attention weights

        Returns:
            Tuple of (context tensor, attention weights if output_attentions=True)
        """
        from flash_attn import flash_attn_func

        # Reshape tensors for flash attention
        batch_size, num_heads, seq_len, head_dim = query.shape

        # Flash attention expects shape [batch_size, seq_len, num_heads, head_dim]
        query = query.transpose(1, 2)
        key = key.transpose(1, 2)
        value = value.transpose(1, 2)

        # Convert attention mask to proper format if provided
        if attention_mask is not None:
            # Flash attention uses a float mask with 0 for masked positions and 1 for unmasked
            attention_mask = attention_mask.squeeze(1)  # [batch_size, seq_len, seq_len]

            # Create a mask for flash attention
            mask = torch.zeros_like(attention_mask, dtype=torch.bool)
            mask = mask.masked_fill(attention_mask == 0, True)
        else:
            mask = None

        # Apply flash attention
        context = flash_attn_func(
            query, key, value,
            mask=mask,
            causal=False,
            softmax_scale=1.0 / math.sqrt(head_dim)
        )

        # Reshape back to original format [batch_size, num_heads, seq_len, head_dim]
        context = context.transpose(1, 2)

        # Flash attention doesn't support returning attention weights
        attention_weights = None

        return context, attention_weights

    def _apply_chunked_attention(
        self,
        query: torch.Tensor,
        key: torch.Tensor,
        value: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        head_mask: Optional[torch.Tensor] = None,
        output_attentions: bool = False
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        Apply chunked attention.

        Args:
            query: Query tensor [batch_size, num_heads, seq_len, head_dim]
            key: Key tensor [batch_size, num_heads, seq_len, head_dim]
            value: Value tensor [batch_size, num_heads, seq_len, head_dim]
            attention_mask: Optional attention mask [batch_size, 1, seq_len, seq_len]
            head_mask: Optional head mask [batch_size, num_heads, seq_len, seq_len]
            output_attentions: Whether to output attention weights

        Returns:
            Tuple of (context tensor, attention weights if output_attentions=True)
        """
        batch_size, num_heads, seq_len, head_dim = query.shape

        # Initialize output tensors
        context = torch.zeros_like(query)
        attention_weights = torch.zeros(
            (batch_size, num_heads, seq_len, seq_len),
            device=query.device,
            dtype=query.dtype
        ) if output_attentions else None

        # Process in chunks
        for chunk_idx in range(0, seq_len, self.chunk_size):
            # Get current chunk indices
            chunk_end = min(chunk_idx + self.chunk_size, seq_len)

            # Extract query chunk
            query_chunk = query[:, :, chunk_idx:chunk_end, :]

            # Compute attention scores for this chunk
            # [batch_size, num_heads, chunk_size, seq_len]
            attention_scores = torch.matmul(
                query_chunk,
                key.transpose(-1, -2)
            ) / math.sqrt(head_dim)

            # Apply attention mask if provided
            if attention_mask is not None:
                attention_scores = attention_scores + attention_mask[:, :, chunk_idx:chunk_end, :]

            # Apply head mask if provided
            if head_mask is not None:
                # Reshape head_mask to match the attention scores dimensions
                # Head mask is typically [batch_size, num_heads, 1, 1] or [batch_size, num_heads, seq_len, seq_len]
                if head_mask.dim() == 4 and head_mask.size(2) == 1 and head_mask.size(3) == 1:
                    # If head_mask is [batch_size, num_heads, 1, 1], broadcast it
                    attention_scores = attention_scores * head_mask
                else:
                    # If head_mask is [batch_size, num_heads, seq_len, seq_len], slice it
                    attention_scores = attention_scores * head_mask[:, :, chunk_idx:chunk_end, :]

            # Apply softmax
            attention_probs = torch.nn.functional.softmax(attention_scores, dim=-1)

            # Apply head mask again after softmax if provided
            if head_mask is not None:
                if head_mask.dim() == 4 and head_mask.size(2) == 1 and head_mask.size(3) == 1:
                    # If head_mask is [batch_size, num_heads, 1, 1], broadcast it
                    attention_probs = attention_probs * head_mask
                else:
                    # If head_mask is [batch_size, num_heads, seq_len, seq_len], slice it
                    attention_probs = attention_probs * head_mask[:, :, chunk_idx:chunk_end, :]

            # Compute context for this chunk
            chunk_context = torch.matmul(attention_probs, value)

            # Store results
            context[:, :, chunk_idx:chunk_end, :] = chunk_context

            if output_attentions:
                attention_weights[:, :, chunk_idx:chunk_end, :] = attention_probs

        return context, attention_weights

def apply_chunked_attention(
    model,
    chunk_size: int = 128,
    use_flash_attention: bool = True,
    optimize_backward_pass: bool = True,
    gradient_checkpointing: bool = False,
    memory_efficient_qk: bool = True
) -> None:
    """
    Apply chunked attention to a model.

    This function replaces the standard attention mechanism in a model
    with chunked attention to reduce memory usage.

    Args:
        model: The model to modify
        chunk_size: Size of chunks for processing attention
        use_flash_attention: Whether to use flash attention when available
        optimize_backward_pass: Whether to optimize the backward pass
        gradient_checkpointing: Whether to use gradient checkpointing
        memory_efficient_qk: Whether to use memory-efficient QK computation
    """
    chunked_attn = ChunkedAttention(
        chunk_size=chunk_size,
        use_flash_attention=use_flash_attention,
        optimize_backward_pass=optimize_backward_pass,
        gradient_checkpointing=gradient_checkpointing,
        memory_efficient_qk=memory_efficient_qk
    )

    # Find attention modules in the model
    for name, module in model.named_modules():
        # Check if this is an attention module
        if any(attn_name in name.lower() for attn_name in ["attention", "attn"]):
            if hasattr(module, "forward") and hasattr(module, "_attn"):
                # Save original attention function
                original_attn = module._attn

                # Define new attention function using chunked attention
                def new_attn(self, query, key, value, attention_mask=None, head_mask=None, output_attentions=False):
                    return chunked_attn.apply(
                        query, key, value,
                        attention_mask=attention_mask,
                        head_mask=head_mask,
                        output_attentions=output_attentions
                    )

                # Replace attention function
                module._attn = new_attn.__get__(module, type(module))

                logger.info(f"Applied chunked attention to module: {name}")

def estimate_memory_savings(
    batch_size: int,
    seq_len: int,
    num_heads: int,
    head_dim: int,
    chunk_size: int = 128,
    dtype_bytes: int = 2  # fp16 = 2 bytes
) -> Dict[str, float]:
    """
    Estimate memory savings from using chunked attention.

    Args:
        batch_size: Batch size
        seq_len: Sequence length
        num_heads: Number of attention heads
        head_dim: Dimension of each attention head
        chunk_size: Size of chunks for processing attention
        dtype_bytes: Number of bytes per element (2 for fp16, 4 for fp32)

    Returns:
        Dictionary with memory usage estimates
    """
    # Calculate memory usage for standard attention
    # The main memory bottleneck is the attention matrix: [batch_size, num_heads, seq_len, seq_len]
    standard_attention_bytes = batch_size * num_heads * seq_len * seq_len * dtype_bytes

    # Calculate memory usage for chunked attention
    # The attention matrix is now: [batch_size, num_heads, chunk_size, seq_len]
    chunked_attention_bytes = batch_size * num_heads * chunk_size * seq_len * dtype_bytes

    # Convert to MB
    standard_attention_mb = standard_attention_bytes / (1024 * 1024)
    chunked_attention_mb = chunked_attention_bytes / (1024 * 1024)

    # Calculate savings
    absolute_savings_mb = standard_attention_mb - chunked_attention_mb
    percentage_savings = (1 - (chunked_attention_mb / standard_attention_mb)) * 100

    return {
        "standard_attention_mb": standard_attention_mb,
        "chunked_attention_mb": chunked_attention_mb,
        "absolute_savings_mb": absolute_savings_mb,
        "percentage_savings": percentage_savings
    }
