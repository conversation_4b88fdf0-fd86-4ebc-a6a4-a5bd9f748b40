"""
Memory optimization module for Deep Research Core.

This module provides tools and utilities for optimizing memory usage
in large language models, including KV cache pruning, quantization,
chunked attention, gradient checkpointing, gradient accumulation,
and other memory-efficient techniques.
"""

from .kv_cache_pruning import (
    KVCachePruner,
    AttentionBasedPruning,
    SlidingWindowPruning,
    ImportanceBasedPruning,
    ChunkBasedPruning,
    ProgressivePruning,
    AdaptivePruning,
    apply_kv_cache_pruning,
    estimate_memory_savings
)

from .token_importance import (
    TokenImportanceCalculator,
    AttentionBasedImportance,
    SemanticImportance,
    GradientBasedImportance,
    HybridImportance,
    PositionalImportance,
    create_importance_calculator
)

from .pruning_hooks import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    GPTNeoXPruningHook,
    LlamaModelPruningHook,
    GPT2PruningHook,
    T5PruningHook,
    apply_pruning_hooks
)

from .adaptive_kv_cache import (
    AdaptiveKVCache,
    apply_adaptive_kv_cache,
    estimate_optimal_cache_size
)

from .chunked_attention import (
    ChunkedAttention,
    apply_chunked_attention,
    estimate_memory_savings as estimate_chunked_attention_savings
)

from .gradient_checkpointing import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>point<PERSON>,
    apply_gradient_checkpointing,
    enable_transformer_checkpointing,
    estimate_memory_savings as estimate_checkpointing_savings,
    create_checkpointed_model
)

from .gradient_accumulation import (
    GradientAccumulator,
    AccumulationTrainer,
    estimate_memory_savings as estimate_accumulation_savings
)

__all__ = [
    # KV Cache Pruning
    'KVCachePruner',
    'AttentionBasedPruning',
    'SlidingWindowPruning',
    'ImportanceBasedPruning',
    'ChunkBasedPruning',
    'ProgressivePruning',
    'AdaptivePruning',
    'apply_kv_cache_pruning',
    'estimate_memory_savings',

    # Token Importance
    'TokenImportanceCalculator',
    'AttentionBasedImportance',
    'SemanticImportance',
    'GradientBasedImportance',
    'HybridImportance',
    'PositionalImportance',
    'create_importance_calculator',

    # Pruning Hooks
    'PruningHook',
    'GPTNeoXPruningHook',
    'LlamaModelPruningHook',
    'GPT2PruningHook',
    'T5PruningHook',
    'apply_pruning_hooks',

    # Adaptive KV Cache
    'AdaptiveKVCache',
    'apply_adaptive_kv_cache',
    'estimate_optimal_cache_size',

    # Chunked Attention
    'ChunkedAttention',
    'apply_chunked_attention',
    'estimate_chunked_attention_savings',

    # Gradient Checkpointing
    'GradientCheckpointer',
    'apply_gradient_checkpointing',
    'enable_transformer_checkpointing',
    'estimate_checkpointing_savings',
    'create_checkpointed_model',

    # Gradient Accumulation
    'GradientAccumulator',
    'AccumulationTrainer',
    'estimate_accumulation_savings'
]
