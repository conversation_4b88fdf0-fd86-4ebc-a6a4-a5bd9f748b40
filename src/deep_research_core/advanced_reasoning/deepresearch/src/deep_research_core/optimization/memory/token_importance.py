"""
Token importance calculation module for KV cache pruning.

This module provides implementations of various methods for calculating
the importance of tokens in a sequence, which can be used for KV cache pruning.
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
from abc import ABC, abstractmethod

from deep_research_core.utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)


class TokenImportanceCalculator(ABC):
    """
    Abstract base class for token importance calculators.
    
    Token importance calculators determine the importance of each token
    in a sequence, which can be used for KV cache pruning.
    """
    
    def __init__(self, **kwargs):
        """
        Initialize the token importance calculator.
        
        Args:
            **kwargs: Additional implementation-specific arguments
        """
        pass
    
    @abstractmethod
    def calculate_importance(
        self,
        key_tensor: torch.Tensor,
        value_tensor: torch.Tensor,
        attention_scores: Optional[torch.Tensor] = None,
        token_embeddings: Optional[torch.Tensor] = None,
        **kwargs
    ) -> torch.Tensor:
        """
        Calculate the importance of each token.
        
        Args:
            key_tensor: Key tensor from KV cache
            value_tensor: Value tensor from KV cache
            attention_scores: Optional attention scores
            token_embeddings: Optional token embeddings
            **kwargs: Additional arguments
            
        Returns:
            Tensor of importance scores for each token
        """
        pass


class AttentionBasedImportance(TokenImportanceCalculator):
    """
    Calculate token importance based on attention scores.
    
    This method uses attention scores to determine the importance of each token,
    with the assumption that tokens that receive more attention are more important.
    """
    
    def __init__(
        self,
        use_cumulative_attention: bool = True,
        recency_bias: float = 0.1,
        **kwargs
    ):
        """
        Initialize attention-based importance calculator.
        
        Args:
            use_cumulative_attention: Whether to use cumulative attention scores
            recency_bias: Bias factor for more recent tokens
            **kwargs: Additional arguments
        """
        super().__init__(**kwargs)
        self.use_cumulative_attention = use_cumulative_attention
        self.recency_bias = recency_bias
        self.cumulative_attention = None
    
    def calculate_importance(
        self,
        key_tensor: torch.Tensor,
        value_tensor: torch.Tensor,
        attention_scores: Optional[torch.Tensor] = None,
        token_embeddings: Optional[torch.Tensor] = None,
        **kwargs
    ) -> torch.Tensor:
        """
        Calculate token importance based on attention scores.
        
        Args:
            key_tensor: Key tensor from KV cache
            value_tensor: Value tensor from KV cache
            attention_scores: Attention scores
            token_embeddings: Ignored in this method
            **kwargs: Additional arguments
            
        Returns:
            Tensor of importance scores for each token
        """
        if attention_scores is None:
            logger.warning("No attention scores provided for attention-based importance")
            # Return uniform importance if no attention scores
            return torch.ones(key_tensor.size(1), device=key_tensor.device)
        
        # Update cumulative attention scores
        if self.use_cumulative_attention:
            if self.cumulative_attention is None:
                self.cumulative_attention = attention_scores
            else:
                # Resize if needed
                if self.cumulative_attention.size(1) < attention_scores.size(1):
                    padding = torch.zeros(
                        (attention_scores.size(0),
                         attention_scores.size(1) - self.cumulative_attention.size(1),
                         attention_scores.size(2)),
                        device=attention_scores.device
                    )
                    self.cumulative_attention = torch.cat(
                        [self.cumulative_attention, padding], dim=1
                    )
                
                # Add new attention scores
                self.cumulative_attention[:, :attention_scores.size(1), :] += attention_scores
        
        # Determine which scores to use
        if self.use_cumulative_attention:
            # Use the cumulative attention scores
            scores = self.cumulative_attention
        else:
            # Use the current attention scores
            scores = attention_scores
        
        # Average across heads and batches
        # Handle different attention score shapes
        if scores.dim() == 4:  # [batch, heads, seq_len, seq_len]
            # Use the last row of attention scores (attention from the last token)
            last_token_scores = scores[:, :, -1, :].mean(dim=(0, 1))  # Shape: [seq_len]
            token_scores = last_token_scores
        elif scores.dim() == 3:  # [batch, seq_len, seq_len] or [batch, heads, seq_len]
            if scores.size(1) == scores.size(2):  # [batch, seq_len, seq_len]
                last_token_scores = scores[:, -1, :].mean(dim=0)  # Shape: [seq_len]
                token_scores = last_token_scores
            else:  # [batch, heads, seq_len]
                token_scores = scores.mean(dim=(0, 1))  # Shape: [seq_len]
        else:
            # Fallback: just average whatever dimensions we have
            token_scores = scores.mean(dim=tuple(range(scores.dim() - 1)))
        
        # Ensure token_scores has the right length
        if token_scores.size(0) != key_tensor.size(1):
            logger.warning(
                f"Attention scores shape mismatch: {token_scores.size(0)} vs {key_tensor.size(1)}. "
                f"Using uniform scores instead."
            )
            # Use uniform scores as fallback
            token_scores = torch.ones(key_tensor.size(1), device=key_tensor.device)
        
        # Apply recency bias
        if self.recency_bias > 0:
            seq_len = token_scores.size(0)
            recency_weights = torch.arange(seq_len, device=token_scores.device) / seq_len
            recency_weights = 1.0 + self.recency_bias * recency_weights
            token_scores = token_scores * recency_weights
        
        return token_scores


class SemanticImportance(TokenImportanceCalculator):
    """
    Calculate token importance based on semantic information.
    
    This method uses token embeddings to determine the importance of each token,
    with the assumption that tokens with higher semantic content are more important.
    """
    
    def __init__(
        self,
        semantic_threshold: float = 0.5,
        use_pca: bool = True,
        pca_components: int = 10,
        **kwargs
    ):
        """
        Initialize semantic importance calculator.
        
        Args:
            semantic_threshold: Threshold for semantic importance
            use_pca: Whether to use PCA for dimensionality reduction
            pca_components: Number of PCA components to use
            **kwargs: Additional arguments
        """
        super().__init__(**kwargs)
        self.semantic_threshold = semantic_threshold
        self.use_pca = use_pca
        self.pca_components = pca_components
    
    def calculate_importance(
        self,
        key_tensor: torch.Tensor,
        value_tensor: torch.Tensor,
        attention_scores: Optional[torch.Tensor] = None,
        token_embeddings: Optional[torch.Tensor] = None,
        **kwargs
    ) -> torch.Tensor:
        """
        Calculate token importance based on semantic information.
        
        Args:
            key_tensor: Key tensor from KV cache
            value_tensor: Value tensor from KV cache
            attention_scores: Ignored in this method
            token_embeddings: Token embeddings
            **kwargs: Additional arguments
            
        Returns:
            Tensor of importance scores for each token
        """
        # If token embeddings are not provided, use key tensor as proxy
        if token_embeddings is None:
            # Reshape key tensor to [seq_len, batch_size * num_heads * head_dim]
            token_embeddings = key_tensor.permute(1, 0, 2, 3).reshape(
                key_tensor.size(1), -1
            )
        
        # Calculate semantic importance
        seq_len = token_embeddings.size(0)
        
        # Apply PCA if requested
        if self.use_pca and seq_len > self.pca_components:
            # Center the data
            centered_embeddings = token_embeddings - token_embeddings.mean(dim=0, keepdim=True)
            
            # Compute covariance matrix
            cov = torch.mm(centered_embeddings.t(), centered_embeddings) / (seq_len - 1)
            
            # Compute eigenvalues and eigenvectors
            eigenvalues, eigenvectors = torch.linalg.eigh(cov)
            
            # Sort eigenvalues and eigenvectors in descending order
            sorted_indices = torch.argsort(eigenvalues, descending=True)
            eigenvalues = eigenvalues[sorted_indices]
            eigenvectors = eigenvectors[:, sorted_indices]
            
            # Select top components
            top_eigenvectors = eigenvectors[:, :self.pca_components]
            
            # Project data onto principal components
            projected_embeddings = torch.mm(centered_embeddings, top_eigenvectors)
            
            # Calculate importance as L2 norm of projected embeddings
            token_scores = torch.norm(projected_embeddings, dim=1)
        else:
            # Calculate importance as L2 norm of embeddings
            token_scores = torch.norm(token_embeddings, dim=1)
        
        # Normalize scores
        token_scores = token_scores / token_scores.max()
        
        return token_scores


class GradientBasedImportance(TokenImportanceCalculator):
    """
    Calculate token importance based on gradients.
    
    This method uses gradients with respect to the loss to determine
    the importance of each token, with the assumption that tokens with
    higher gradient magnitudes are more important.
    """
    
    def __init__(
        self,
        loss_fn: Optional[Callable] = None,
        **kwargs
    ):
        """
        Initialize gradient-based importance calculator.
        
        Args:
            loss_fn: Loss function to use for gradient calculation
            **kwargs: Additional arguments
        """
        super().__init__(**kwargs)
        self.loss_fn = loss_fn or torch.nn.CrossEntropyLoss()
    
    def calculate_importance(
        self,
        key_tensor: torch.Tensor,
        value_tensor: torch.Tensor,
        attention_scores: Optional[torch.Tensor] = None,
        token_embeddings: Optional[torch.Tensor] = None,
        model_output: Optional[torch.Tensor] = None,
        target: Optional[torch.Tensor] = None,
        **kwargs
    ) -> torch.Tensor:
        """
        Calculate token importance based on gradients.
        
        Args:
            key_tensor: Key tensor from KV cache
            value_tensor: Value tensor from KV cache
            attention_scores: Ignored in this method
            token_embeddings: Token embeddings
            model_output: Model output logits
            target: Target tokens
            **kwargs: Additional arguments
            
        Returns:
            Tensor of importance scores for each token
        """
        # If model output or target is not provided, fall back to uniform importance
        if model_output is None or target is None:
            logger.warning("Model output or target not provided for gradient-based importance")
            return torch.ones(key_tensor.size(1), device=key_tensor.device)
        
        # If token embeddings are not provided, use key tensor as proxy
        if token_embeddings is None:
            # Reshape key tensor to [seq_len, batch_size * num_heads * head_dim]
            token_embeddings = key_tensor.permute(1, 0, 2, 3).reshape(
                key_tensor.size(1), -1
            )
        
        # Ensure token embeddings require gradients
        token_embeddings = token_embeddings.detach().clone().requires_grad_(True)
        
        # Calculate loss
        loss = self.loss_fn(model_output, target)
        
        # Calculate gradients
        loss.backward()
        
        # Get gradients
        gradients = token_embeddings.grad
        
        # Calculate importance as L2 norm of gradients
        token_scores = torch.norm(gradients, dim=1)
        
        # Normalize scores
        token_scores = token_scores / token_scores.max()
        
        return token_scores


class HybridImportance(TokenImportanceCalculator):
    """
    Calculate token importance using a hybrid approach.
    
    This method combines multiple importance calculators to determine
    the importance of each token, allowing for a more robust assessment.
    """
    
    def __init__(
        self,
        calculators: List[TokenImportanceCalculator],
        weights: Optional[List[float]] = None,
        **kwargs
    ):
        """
        Initialize hybrid importance calculator.
        
        Args:
            calculators: List of importance calculators to use
            weights: Weights for each calculator
            **kwargs: Additional arguments
        """
        super().__init__(**kwargs)
        self.calculators = calculators
        
        # If weights are not provided, use equal weights
        if weights is None:
            weights = [1.0 / len(calculators)] * len(calculators)
        
        # Ensure weights sum to 1
        weights = torch.tensor(weights)
        weights = weights / weights.sum()
        
        self.weights = weights
    
    def calculate_importance(
        self,
        key_tensor: torch.Tensor,
        value_tensor: torch.Tensor,
        attention_scores: Optional[torch.Tensor] = None,
        token_embeddings: Optional[torch.Tensor] = None,
        **kwargs
    ) -> torch.Tensor:
        """
        Calculate token importance using a hybrid approach.
        
        Args:
            key_tensor: Key tensor from KV cache
            value_tensor: Value tensor from KV cache
            attention_scores: Attention scores
            token_embeddings: Token embeddings
            **kwargs: Additional arguments
            
        Returns:
            Tensor of importance scores for each token
        """
        # Calculate importance using each calculator
        all_scores = []
        for calculator in self.calculators:
            scores = calculator.calculate_importance(
                key_tensor, value_tensor, attention_scores, token_embeddings, **kwargs
            )
            
            # Normalize scores
            scores = scores / scores.max()
            
            all_scores.append(scores)
        
        # Combine scores using weights
        combined_scores = torch.zeros_like(all_scores[0])
        for i, scores in enumerate(all_scores):
            combined_scores += self.weights[i] * scores
        
        return combined_scores


class PositionalImportance(TokenImportanceCalculator):
    """
    Calculate token importance based on position in the sequence.
    
    This method assigns importance based on the position of tokens in the sequence,
    with the assumption that certain positions (e.g., beginning, end) are more important.
    """
    
    def __init__(
        self,
        beginning_weight: float = 1.0,
        end_weight: float = 1.0,
        middle_weight: float = 0.5,
        **kwargs
    ):
        """
        Initialize positional importance calculator.
        
        Args:
            beginning_weight: Weight for tokens at the beginning
            end_weight: Weight for tokens at the end
            middle_weight: Weight for tokens in the middle
            **kwargs: Additional arguments
        """
        super().__init__(**kwargs)
        self.beginning_weight = beginning_weight
        self.end_weight = end_weight
        self.middle_weight = middle_weight
    
    def calculate_importance(
        self,
        key_tensor: torch.Tensor,
        value_tensor: torch.Tensor,
        attention_scores: Optional[torch.Tensor] = None,
        token_embeddings: Optional[torch.Tensor] = None,
        **kwargs
    ) -> torch.Tensor:
        """
        Calculate token importance based on position.
        
        Args:
            key_tensor: Key tensor from KV cache
            value_tensor: Value tensor from KV cache
            attention_scores: Ignored in this method
            token_embeddings: Ignored in this method
            **kwargs: Additional arguments
            
        Returns:
            Tensor of importance scores for each token
        """
        seq_len = key_tensor.size(1)
        device = key_tensor.device
        
        # Create position-based importance scores
        positions = torch.arange(seq_len, device=device).float()
        
        # Normalize positions to [0, 1]
        normalized_positions = positions / (seq_len - 1)
        
        # Calculate weights based on position
        # Higher weights for beginning and end, lower for middle
        beginning_mask = normalized_positions < 0.1
        end_mask = normalized_positions > 0.9
        middle_mask = ~(beginning_mask | end_mask)
        
        # Apply weights
        token_scores = torch.zeros(seq_len, device=device)
        token_scores[beginning_mask] = self.beginning_weight
        token_scores[end_mask] = self.end_weight
        token_scores[middle_mask] = self.middle_weight
        
        # Ensure first token always has high importance
        token_scores[0] = max(self.beginning_weight, 1.0)
        
        # Ensure last token always has high importance
        token_scores[-1] = max(self.end_weight, 1.0)
        
        return token_scores


# Factory function to create importance calculators
def create_importance_calculator(
    method: str,
    **kwargs
) -> TokenImportanceCalculator:
    """
    Create a token importance calculator.
    
    Args:
        method: Method to use for importance calculation
        **kwargs: Additional arguments for the calculator
        
    Returns:
        Token importance calculator
    """
    if method == 'attention':
        return AttentionBasedImportance(**kwargs)
    elif method == 'semantic':
        return SemanticImportance(**kwargs)
    elif method == 'gradient':
        return GradientBasedImportance(**kwargs)
    elif method == 'positional':
        return PositionalImportance(**kwargs)
    elif method == 'hybrid':
        # Create sub-calculators
        sub_methods = kwargs.pop('sub_methods', ['attention', 'positional'])
        sub_kwargs = kwargs.pop('sub_kwargs', [{} for _ in sub_methods])
        weights = kwargs.pop('weights', None)
        
        calculators = [
            create_importance_calculator(sub_method, **sub_kwargs[i])
            for i, sub_method in enumerate(sub_methods)
        ]
        
        return HybridImportance(calculators, weights, **kwargs)
    else:
        raise ValueError(f"Unknown importance calculation method: {method}")
