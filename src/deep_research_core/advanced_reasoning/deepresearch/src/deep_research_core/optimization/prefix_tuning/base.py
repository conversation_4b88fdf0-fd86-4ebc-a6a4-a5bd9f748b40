"""
Base Prefix Tuning implementation.

This module provides the base class for Prefix Tuning implementations in Deep Research Core,
defining the interface and common functionality for applying Prefix Tuning
to various language models.
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, List, Optional, Union, Tuple
import logging
import time

import torch
import torch.nn as nn
from transformers import PreTrainedModel, AutoModelForCausalLM, AutoTokenizer

from ...utils.structured_logging import get_logger
from ...utils.performance_metrics import measure_latency

# Create a logger
logger = get_logger(__name__)

class BasePrefixTuning:
    """
    Base class for Prefix Tuning fine-tuning.

    Prefix Tuning prepends a series of continuous trainable vectors to the input,
    allowing for efficient fine-tuning by only updating these prefix parameters
    while keeping the pre-trained model weights frozen.
    """

    def __init__(
        self,
        model_name: str,
        num_virtual_tokens: int = 20,
        encoder_hidden_size: Optional[int] = None,
        prefix_projection: bool = True,
        dropout: float = 0.1,
        task_type: str = "CAUSAL_LM",
        output_dir: str = "./prefix_tuning_output",
        device: Optional[str] = None,
        verbose: bool = False,
        **kwargs
    ):
        """
        Initialize the BasePrefixTuning.

        Args:
            model_name: Name or path of the base model to fine-tune
            num_virtual_tokens: Number of virtual tokens to use as prefix
            encoder_hidden_size: Size of the encoder hidden layer for prefix projection
            prefix_projection: Whether to project the prefix vectors
            dropout: Dropout probability for prefix layers
            task_type: Type of task for the model (default: CAUSAL_LM)
            output_dir: Directory to save prefix tuning weights and configs
            device: Device to load the model on (if None, will use GPU if available)
            verbose: Whether to print verbose output
            **kwargs: Additional implementation-specific arguments
        """
        self.model_name = model_name
        self.num_virtual_tokens = num_virtual_tokens
        self.prefix_projection = prefix_projection
        self.dropout = dropout
        self.task_type = task_type
        self.output_dir = output_dir
        self.verbose = verbose
        
        # Detect device
        if device is None:
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            self.device = device

        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir, exist_ok=True)

        # Initialize model, tokenizer, and prefix tuning config
        self.model = None
        self.tokenizer = None
        self.prefix_config = None
        self.peft_model = None
        self.encoder_hidden_size = encoder_hidden_size

        # Additional attributes from kwargs
        for key, value in kwargs.items():
            setattr(self, key, value)

        logger.info(f"Initialized BasePrefixTuning with model: {model_name}, virtual tokens: {num_virtual_tokens}")

    @measure_latency("prefix_tuning_initialization")
    def initialize(self) -> None:
        """
        Initialize the model, tokenizer, and Prefix Tuning configuration.

        This method should be called before any training or inference.
        """
        try:
            # Import PEFT at runtime to avoid dependency issues
            from peft import PrefixTuningConfig, get_peft_model

            # Load base model and tokenizer
            logger.info(f"Loading base model {self.model_name}...")
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32
            )
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)

            # Determine encoder hidden size if not provided
            if self.encoder_hidden_size is None:
                # Typically set to hidden size of base model
                if hasattr(self.model.config, "hidden_size"):
                    self.encoder_hidden_size = self.model.config.hidden_size
                else:
                    # Default fallback
                    self.encoder_hidden_size = 768
                    logger.warning(f"Could not determine hidden size from model config, using default: {self.encoder_hidden_size}")

            # Create Prefix Tuning config
            self.prefix_config = PrefixTuningConfig(
                task_type=self.task_type,
                num_virtual_tokens=self.num_virtual_tokens,
                encoder_hidden_size=self.encoder_hidden_size,
                prefix_projection=self.prefix_projection,
                token_dim=self.model.config.hidden_size
            )

            # Apply Prefix Tuning to the model
            logger.info(f"Applying Prefix Tuning configuration...")
            self.peft_model = get_peft_model(self.model, self.prefix_config)

            # Move model to device
            self.peft_model.to(self.device)

            # Print trainable parameters info if verbose
            if self.verbose:
                self._print_trainable_parameters()

            logger.info(f"Successfully initialized Prefix Tuning model")
            return True

        except Exception as e:
            logger.error(f"Error initializing Prefix Tuning model: {str(e)}")
            raise

    def _print_trainable_parameters(self) -> None:
        """
        Print the number of trainable parameters in the model.
        """
        if self.peft_model is None:
            logger.warning("Model not initialized yet.")
            return

        trainable_params = 0
        all_params = 0

        for _, param in self.peft_model.named_parameters():
            all_params += param.numel()
            if param.requires_grad:
                trainable_params += param.numel()

        percentage = 100 * trainable_params / all_params

        logger.info(
            f"Trainable parameters: {trainable_params:,} ({percentage:.2f}% of {all_params:,} total parameters)"
        )
    
    def save(self, path: Optional[str] = None) -> str:
        """
        Save the Prefix Tuning weights and configuration.

        Args:
            path: Path to save the model to. If None, uses output_dir/timestamp.

        Returns:
            Path where the model was saved
        """
        if self.peft_model is None:
            raise ValueError("Model not initialized. Call initialize() first.")

        # If path not provided, create one with timestamp
        if path is None:
            timestamp = time.strftime("%Y%m%d-%H%M%S")
            path = os.path.join(self.output_dir, f"prefix-{timestamp}")

        # Create directory if it doesn't exist
        os.makedirs(path, exist_ok=True)

        # Save the model
        logger.info(f"Saving Prefix Tuning model to {path}")
        self.peft_model.save_pretrained(path)

        # Save the tokenizer
        self.tokenizer.save_pretrained(path)

        # Save metadata
        metadata = {
            "base_model": self.model_name,
            "num_virtual_tokens": self.num_virtual_tokens,
            "encoder_hidden_size": self.encoder_hidden_size,
            "prefix_projection": self.prefix_projection,
            "dropout": self.dropout,
            "task_type": self.task_type,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        }

        metadata_path = os.path.join(path, "prefix_metadata.json")
        with open(metadata_path, "w") as f:
            json.dump(metadata, f, indent=2)

        logger.info(f"Saved metadata to {metadata_path}")
        return path

    def load(self, path: str) -> None:
        """
        Load a saved Prefix Tuning model.

        Args:
            path: Path to the saved prefix tuning model
        """
        try:
            # Import PEFT at runtime to avoid dependency issues
            from peft import PeftModel, PeftConfig

            # Check if path exists
            if not os.path.exists(path):
                raise ValueError(f"Path {path} does not exist")

            logger.info(f"Loading prefix tuning model from {path}...")

            # Load the configuration
            config = PeftConfig.from_pretrained(path)
            
            # Load the base model if not already loaded
            if self.model is None:
                self.model = AutoModelForCausalLM.from_pretrained(
                    config.base_model_name_or_path,
                    torch_dtype=torch.float16 if self.device == "cuda" else torch.float32
                )
            
            # Load tokenizer if not already loaded
            if self.tokenizer is None:
                self.tokenizer = AutoTokenizer.from_pretrained(config.base_model_name_or_path)
                
            # Load the PEFT model
            self.peft_model = PeftModel.from_pretrained(
                self.model,
                path,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32
            )
            
            # Move model to device
            self.peft_model.to(self.device)
            
            # Load metadata if available
            metadata_path = os.path.join(path, "prefix_metadata.json")
            if os.path.exists(metadata_path):
                with open(metadata_path, "r") as f:
                    metadata = json.load(f)
                    for key, value in metadata.items():
                        if not hasattr(self, key) or getattr(self, key) is None:
                            setattr(self, key, value)
            
            logger.info(f"Successfully loaded prefix tuning model from {path}")
            
            # Print trainable parameters if verbose
            if self.verbose:
                self._print_trainable_parameters()
                
            return True
            
        except Exception as e:
            logger.error(f"Error loading prefix tuning model: {str(e)}")
            raise

    def generate(
        self,
        prompt: str,
        max_new_tokens: int = 128,
        temperature: float = 0.7,
        top_p: float = 0.9,
        top_k: int = 50,
        repetition_penalty: float = 1.1,
        **kwargs
    ) -> str:
        """
        Generate text using the prefix tuning model.

        Args:
            prompt: Input text prompt
            max_new_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature
            top_p: Nucleus sampling parameter
            top_k: Top-k sampling parameter
            repetition_penalty: Repetition penalty
            **kwargs: Additional generation parameters

        Returns:
            Generated text as a string
        """
        if self.peft_model is None:
            raise ValueError("Model not initialized. Call initialize() or load() first.")

        # Encode the prompt
        input_ids = self.tokenizer.encode(prompt, return_tensors="pt").to(self.device)

        # Set up generation parameters
        gen_kwargs = {
            "input_ids": input_ids,
            "max_new_tokens": max_new_tokens,
            "temperature": temperature,
            "top_p": top_p,
            "top_k": top_k,
            "repetition_penalty": repetition_penalty,
            "do_sample": temperature > 0,
            "pad_token_id": self.tokenizer.eos_token_id,  # Use EOS as PAD
            **kwargs
        }

        # Generate text
        with torch.no_grad():
            generation_output = self.peft_model.generate(**gen_kwargs)

        # Decode the generated text
        generated_text = self.tokenizer.decode(generation_output[0], skip_special_tokens=True)

        # Return only the newly generated text (without the prompt)
        prompt_length = len(prompt)
        if generated_text.startswith(prompt):
            return generated_text[prompt_length:].strip()
        else:
            return generated_text.strip() 