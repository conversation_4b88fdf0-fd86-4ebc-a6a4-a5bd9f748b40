"""
Prefix Tuning implementations for Deep Research Core.

This module provides implementations for fine-tuning models with Prefix Tuning
techniques, allowing parameter-efficient adaptation of large models by
adding trainable continuous prefix vectors.
"""

from .base import BasePrefixTuning
# TODO: Implement model adapters
# from .model_adapters import LlamaPrefixModel, MistralPrefixModel, GemmaPrefixModel
# TODO: Implement training and evaluation
# from .training import PrefixTuningTrainer
# from .evaluation import PrefixTuningEvaluator

__all__ = [
    'BasePrefixTuning',
    # 'LlamaPrefixModel',
    # 'MistralPrefixModel',
    # 'GemmaPrefixModel',
    # 'PrefixTuningTrainer',
    # 'PrefixTuningEvaluator',
]