"""
API provider configurations for Deep Research Core.
"""

# API provider configurations
API_PROVIDERS = {
    "openai": {
        "name": "OpenAI",
        "api_key_env": "OPENAI_API_KEY",
        "base_url": "https://api.openai.com/v1",
        "models": {
            "gpt-4o": {
                "context_length": 128000,
                "supports": {
                    "cot": True,
                    "tot": True,
                    "rag": True,
                    "react": True,
                    "reflection": True,
                    "multilingual": True,
                }
            },
            "gpt-4-turbo": {
                "context_length": 128000,
                "supports": {
                    "cot": True,
                    "tot": True,
                    "rag": True,
                    "react": True,
                    "reflection": True,
                    "multilingual": True,
                }
            },
            "gpt-3.5-turbo": {
                "context_length": 16385,
                "supports": {
                    "cot": True,
                    "tot": True,
                    "rag": True,
                    "react": True,
                    "reflection": True,
                    "multilingual": True,
                }
            }
        }
    },
    "anthropic": {
        "name": "Anthropic",
        "api_key_env": "ANTHROPIC_API_KEY",
        "base_url": "https://api.anthropic.com/v1",
        "models": {
            "claude-3-opus": {
                "context_length": 200000,
                "supports": {
                    "cot": True,
                    "tot": True,
                    "rag": True,
                    "react": True,
                    "reflection": True,
                    "multilingual": True,
                }
            },
            "claude-3-sonnet": {
                "context_length": 200000,
                "supports": {
                    "cot": True,
                    "tot": True,
                    "rag": True,
                    "react": True,
                    "reflection": True,
                    "multilingual": True,
                }
            },
            "claude-3-haiku": {
                "context_length": 200000,
                "supports": {
                    "cot": True,
                    "tot": True,
                    "rag": True,
                    "react": True,
                    "reflection": True,
                    "multilingual": True,
                }
            }
        }
    },
    "google": {
        "name": "Google",
        "api_key_env": "GOOGLE_API_KEY",
        "base_url": "https://generativelanguage.googleapis.com/v1",
        "models": {
            "gemini-pro": {
                "context_length": 32768,
                "supports": {
                    "cot": True,
                    "tot": True,
                    "rag": True,
                    "react": True,
                    "reflection": True,
                    "multilingual": True,
                }
            },
            "gemini-ultra": {
                "context_length": 32768,
                "supports": {
                    "cot": True,
                    "tot": True,
                    "rag": True,
                    "react": True,
                    "reflection": True,
                    "multilingual": True,
                }
            }
        }
    },
    "deepseek": {
        "name": "DeepSeek",
        "api_key_env": "DEEPSEEK_API_KEY",
        "base_url": "https://api.deepseek.com/v1",
        "models": {
            "deepseek-chat": {
                "context_length": 32768,
                "supports": {
                    "cot": True,
                    "tot": True,
                    "rag": True,
                    "react": True,
                    "reflection": True,
                    "multilingual": True,
                }
            },
            "deepseek-coder": {
                "context_length": 32768,
                "supports": {
                    "cot": True,
                    "tot": True,
                    "rag": True,
                    "react": True,
                    "reflection": True,
                    "multilingual": True,
                }
            }
        }
    },
    "openrouter": {
        "name": "OpenRouter",
        "api_key_env": "OPENROUTER_API_KEY",
        "base_url": "https://openrouter.ai/api/v1",
        "models": {
            # OpenRouter provides access to multiple models
            # These are just examples, the actual list is much longer
            "anthropic/claude-3-opus": {
                "context_length": 200000,
                "supports": {
                    "cot": True,
                    "tot": True,
                    "rag": True,
                    "react": True,
                    "reflection": True,
                    "multilingual": True,
                }
            },
            "meta-llama/llama-3-70b-instruct": {
                "context_length": 8192,
                "supports": {
                    "cot": True,
                    "tot": True,
                    "rag": True,
                    "react": True,
                    "reflection": True,
                    "multilingual": True,
                }
            },
            "google/gemini-pro": {
                "context_length": 32768,
                "supports": {
                    "cot": True,
                    "tot": True,
                    "rag": True,
                    "react": True,
                    "reflection": True,
                    "multilingual": True,
                }
            }
        }
    },
    "cohere": {
        "name": "Cohere",
        "api_key_env": "COHERE_API_KEY",
        "base_url": "https://api.cohere.ai/v1",
        "models": {
            "command": {
                "context_length": 4096,
                "supports": {
                    "cot": True,
                    "tot": True,
                    "rag": True,
                    "react": True,
                    "reflection": True,
                    "multilingual": True,
                }
            },
            "command-light": {
                "context_length": 4096,
                "supports": {
                    "cot": True,
                    "tot": True,
                    "rag": True,
                    "react": True,
                    "reflection": True,
                    "multilingual": True,
                }
            },
            "command-r": {
                "context_length": 128000,
                "supports": {
                    "cot": True,
                    "tot": True,
                    "rag": True,
                    "react": True,
                    "reflection": True,
                    "multilingual": True,
                }
            },
            "command-r-plus": {
                "context_length": 128000,
                "supports": {
                    "cot": True,
                    "tot": True,
                    "rag": True,
                    "react": True,
                    "reflection": True,
                    "multilingual": True,
                }
            }
        }
    },
    "mistral": {
        "name": "Mistral AI",
        "api_key_env": "MISTRAL_API_KEY",
        "base_url": "https://api.mistral.ai/v1",
        "models": {
            "mistral-tiny": {
                "context_length": 32768,
                "supports": {
                    "cot": True,
                    "tot": True,
                    "rag": True,
                    "react": True,
                    "reflection": True,
                    "multilingual": True,
                }
            },
            "mistral-small": {
                "context_length": 32768,
                "supports": {
                    "cot": True,
                    "tot": True,
                    "rag": True,
                    "react": True,
                    "reflection": True,
                    "multilingual": True,
                }
            },
            "mistral-medium": {
                "context_length": 32768,
                "supports": {
                    "cot": True,
                    "tot": True,
                    "rag": True,
                    "react": True,
                    "reflection": True,
                    "multilingual": True,
                }
            },
            "mistral-large": {
                "context_length": 32768,
                "supports": {
                    "cot": True,
                    "tot": True,
                    "rag": True,
                    "react": True,
                    "reflection": True,
                    "multilingual": True,
                }
            }
        }
    }
}
