"""
Application configuration for Deep Research Core.
"""

import os
from pathlib import Path

# Base directories
BASE_DIR = Path(__file__).parent.parent
DATA_DIR = BASE_DIR / "data"
CACHE_DIR = BASE_DIR / ".cache"

# Create directories if they don't exist
os.makedirs(DATA_DIR, exist_ok=True)
os.makedirs(CACHE_DIR, exist_ok=True)

# Application settings
APP_NAME = "Deep Research Core"
APP_VERSION = "0.1.0"
DEBUG = True

# Default model settings
DEFAULT_MODEL_TYPE = "api"  # "local" or "api"
DEFAULT_MODEL_PROVIDER = "openai"
DEFAULT_MODEL_NAME = "gpt-4o"

# Vector database settings
VECTOR_DB_TYPE = "chroma"  # "faiss", "chroma", "qdrant"
VECTOR_DB_PATH = CACHE_DIR / "vector_db"

# Reasoning settings
DEFAULT_REASONING_METHOD = "cot"  # "cot", "tot", "rag", "react"
MAX_REASONING_DEPTH = 3  # For ToT/GoT
MAX_BRANCHES = 5  # For ToT/GoT

# RAG settings
CHUNK_SIZE = 1000
CHUNK_OVERLAP = 200
TOP_K_RESULTS = 5

# Agent settings
AGENT_TIMEOUT = 60  # seconds
MAX_AGENT_ITERATIONS = 10

# API settings
API_HOST = "0.0.0.0"
API_PORT = 8000
API_WORKERS = 4

# UI settings
ENABLE_WEB_UI = True
ENABLE_CLI = True

# Logging settings
LOG_LEVEL = "INFO"
LOG_FILE = BASE_DIR / "logs" / "app.log"
os.makedirs(BASE_DIR / "logs", exist_ok=True)
