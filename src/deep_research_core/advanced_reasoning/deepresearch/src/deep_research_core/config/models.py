"""
Model configurations for Deep Research Core.
"""

# Local model configurations
LOCAL_MODELS = {
    "mistral-7b": {
        "model_path": "mistralai/Mistral-7B-Instruct-v0.2",
        "quantization": "int8",  # "int8", "int4", "none"
        "context_length": 8192,
        "requires_gpu": True,
        "min_gpu_mem": 8,  # GB
        "recommended_gpu_mem": 12,  # GB
        "supports": {
            "cot": True,
            "tot": True,
            "rag": True,
            "react": True,
            "reflection": True,
            "multilingual": True,
        }
    },
    "phi-3-mini": {
        "model_path": "microsoft/Phi-3-mini-4k-instruct",
        "quantization": "int8",
        "context_length": 4096,
        "requires_gpu": True,
        "min_gpu_mem": 6,  # GB
        "recommended_gpu_mem": 8,  # GB
        "supports": {
            "cot": True,
            "tot": False,
            "rag": True,
            "react": False,
            "reflection": True,
            "multilingual": False,
        }
    },
    "phi-3-small": {
        "model_path": "microsoft/Phi-3-small-8k-instruct",
        "quantization": "int8",
        "context_length": 8192,
        "requires_gpu": True,
        "min_gpu_mem": 8,  # GB
        "recommended_gpu_mem": 12,  # GB
        "supports": {
            "cot": True,
            "tot": True,
            "rag": True,
            "react": True,
            "reflection": True,
            "multilingual": True,
        }
    },
    "gemma-2-9b": {
        "model_path": "google/gemma-2-9b-it",
        "quantization": "int8",
        "context_length": 8192,
        "requires_gpu": True,
        "min_gpu_mem": 10,  # GB
        "recommended_gpu_mem": 12,  # GB
        "supports": {
            "cot": True,
            "tot": True,
            "rag": True,
            "react": True,
            "reflection": True,
            "multilingual": True,
        }
    },
    "llama-3-8b": {
        "model_path": "meta-llama/Meta-Llama-3-8B-Instruct",
        "quantization": "int8",
        "context_length": 8192,
        "requires_gpu": True,
        "min_gpu_mem": 8,  # GB
        "recommended_gpu_mem": 12,  # GB
        "supports": {
            "cot": True,
            "tot": True,
            "rag": True,
            "react": True,
            "reflection": True,
            "multilingual": True,
        }
    },
}

# Model adapter configurations
MODEL_ADAPTERS = {
    "vietnamese": {
        "adapter_path": "adapters/vietnamese",
        "compatible_models": ["mistral-7b", "phi-3-small", "llama-3-8b"],
        "adapter_type": "lora",  # "lora", "qlora", "peft"
        "description": "Vietnamese language adapter for improved performance on Vietnamese text"
    },
    "research": {
        "adapter_path": "adapters/research",
        "compatible_models": ["mistral-7b", "phi-3-small", "llama-3-8b", "gemma-2-9b"],
        "adapter_type": "lora",
        "description": "Research-focused adapter for improved performance on academic and research tasks"
    }
}

# Quantization configurations
QUANTIZATION_CONFIGS = {
    "int8": {
        "bits": 8,
        "method": "gptq",  # "gptq", "awq", "bitsandbytes"
        "group_size": 128,
        "performance_impact": "low",
        "quality_impact": "minimal"
    },
    "int4": {
        "bits": 4,
        "method": "gptq",
        "group_size": 128,
        "performance_impact": "medium",
        "quality_impact": "noticeable"
    },
    "mixed_precision": {
        "bits": "mixed",
        "method": "mixed_precision",
        "description": "Uses different precision formats for different parts of the model based on sensitivity",
        "performance_impact": "low to medium",
        "quality_impact": "minimal",
        "advantages": [
            "Better balance between model size, speed, and accuracy",
            "Optimized for specific hardware (AMD GPUs, CPUs)",
            "Adaptive to model architecture"
        ]
    }
}

# Default prompt templates for different reasoning methods
PROMPT_TEMPLATES = {
    "cot": {
        "system": "You are a helpful AI assistant that adapts your reasoning style to the question at hand. For complex problems requiring step-by-step analysis, use natural transitions like 'First', 'Next', 'Then', 'Additionally', 'Finally' to connect your thoughts. For simpler questions or descriptions, use a more fluid, conversational approach without forced steps. Always prioritize clarity and natural flow in your responses.",
        "user": "{query}\n\nLet me think about this:"
    },
    "tot": {
        "system": "You are a helpful AI assistant that explores multiple reasoning paths to solve problems.",
        "user": "{query}\n\nLet's explore different approaches to solve this problem:"
    },
    "rag": {
        "system": "You are a helpful AI assistant that uses retrieved information to answer questions accurately.",
        "user": "{query}\n\nBased on the following information:\n\n{context}\n\nLet's answer the question:"
    },
    "react": {
        "system": "You are a helpful AI assistant that can think and act to solve problems.",
        "user": "{query}\n\nLet's break this down into thoughts and actions:"
    }
}
