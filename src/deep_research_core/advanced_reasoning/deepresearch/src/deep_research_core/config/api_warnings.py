"""
Configuration for API warnings.

This module provides configuration options for API warnings,
allowing users to disable warnings for missing API keys.
"""

import os
from typing import Dict, Any

# Get logger
from deep_research_core.utils.structured_logging import get_logger
logger = get_logger(__name__)

# Default configuration
DEFAULT_CONFIG = {
    # Whether to show warnings for missing API keys
    "show_api_key_warnings": False,
    
    # API providers to show warnings for (if show_api_key_warnings is True)
    "api_providers": {
        "openai": True,
        "anthropic": True,
        "openrouter": True,
        "cohere": True,
        "mistral": True,
        "deepseek": True,
        "qwq": True,
        "google": True
    }
}

# Current configuration (initialized with default)
_config = DEFAULT_CONFIG.copy()

def load_config_from_env():
    """Load configuration from environment variables."""
    # Check if warnings are enabled
    show_warnings = os.environ.get("DEEP_RESEARCH_SHOW_API_WARNINGS", "").lower()
    if show_warnings in ("true", "1", "yes"):
        _config["show_api_key_warnings"] = True
    elif show_warnings in ("false", "0", "no"):
        _config["show_api_key_warnings"] = False
    
    # Check provider-specific settings
    for provider in _config["api_providers"]:
        env_var = f"DEEP_RESEARCH_SHOW_{provider.upper()}_WARNINGS"
        value = os.environ.get(env_var, "").lower()
        if value in ("true", "1", "yes"):
            _config["api_providers"][provider] = True
        elif value in ("false", "0", "no"):
            _config["api_providers"][provider] = False

def update_config(config: Dict[str, Any]):
    """
    Update the configuration.
    
    Args:
        config: New configuration values
    """
    global _config
    
    # Update top-level keys
    for key, value in config.items():
        if key == "api_providers" and isinstance(value, dict):
            # Update nested provider config
            for provider, enabled in value.items():
                if provider in _config["api_providers"]:
                    _config["api_providers"][provider] = enabled
        elif key in _config:
            _config[key] = value

def should_show_warning(provider: str) -> bool:
    """
    Check if warnings should be shown for the specified provider.
    
    Args:
        provider: API provider name
        
    Returns:
        True if warnings should be shown, False otherwise
    """
    # If warnings are disabled globally, return False
    if not _config["show_api_key_warnings"]:
        return False
    
    # Check provider-specific setting
    return _config["api_providers"].get(provider.lower(), True)

def get_config() -> Dict[str, Any]:
    """
    Get the current configuration.
    
    Returns:
        Current configuration
    """
    return _config.copy()

# Load configuration from environment variables
load_config_from_env()

# Log configuration
logger.debug(f"API warnings configuration: {_config}")
