"""
Monitoring configuration for Deep Research Core.

This module contains configuration settings for monitoring, logging, metrics,
tracing, and alerting.
"""

import os
from typing import Dict, Any, List

# Logging configuration
LOG_CONFIG = {
    "default_level": os.getenv("LOG_LEVEL", "INFO"),
    "json_format": True,
    "log_dir": "logs",
    "main_log_file": "deep_research_core.log",
    "include_stack_info": False
}

# Performance metrics configuration
METRICS_CONFIG = {
    "enabled": True,
    "report_interval": 60,  # Report metrics every 60 seconds
    "memory_measure_interval": 10,  # Measure memory every 10 seconds
    "latency_thresholds": {
        "sqlite_vector_rag_search": 1000,  # 1000ms = 1s
        "sqlite_vector_rag_process": 5000,  # 5000ms = 5s
        "enhanced_cotrag_process": 5000,  # 5000ms = 5s
        "vector_store_search": 500,  # 500ms = 0.5s
        "process_query": 10000  # 10000ms = 10s
    },
    "throughput_thresholds": {
        "sqlite_vector_rag_search": 10,  # At least 10 searches per minute
        "process_query": 1  # At least 1 query per minute
    },
    "memory_thresholds": {
        "process_rss_mb": 1024,  # 1GB
        "system_used_percent": 90  # 90% system memory usage
    }
}

# Tracing configuration
TRACING_CONFIG = {
    "enabled": True,
    "service_name": "deep-research-core",
    "use_opentelemetry": True,
    "otlp_endpoint": os.getenv("OTLP_ENDPOINT", None)
}

# Alerting configuration
ALERTING_CONFIG = {
    "enabled": True,
    "check_interval": 60,  # Check alerts every 60 seconds
    "alerts": [
        {
            "name": "high_search_latency",
            "description": "Search latency is too high",
            "metric_path": "latency.sqlite_vector_rag_search.p95_ms",
            "threshold": 1000,  # 1000ms = 1s
            "comparison": ">",
            "severity": "warning"
        },
        {
            "name": "high_process_latency",
            "description": "Query processing is taking too long",
            "metric_path": "latency.process_query.p95_ms",
            "threshold": 10000,  # 10000ms = 10s
            "comparison": ">",
            "severity": "error"
        },
        {
            "name": "low_search_throughput",
            "description": "Search throughput has dropped significantly",
            "metric_path": "throughput.sqlite_vector_rag_search.throughput_per_second",
            "percentage_change": 50,
            "direction": "decrease",
            "severity": "warning"
        },
        {
            "name": "high_memory_usage",
            "description": "System memory usage is too high",
            "metric_path": "memory.system.latest.system_used_percent",
            "threshold": 90,  # 90% system memory usage
            "comparison": ">",
            "severity": "warning"
        }
    ],
    "notifiers": {
        "email": {
            "enabled": False,
            "smtp_server": os.getenv("SMTP_SERVER", "smtp.gmail.com"),
            "smtp_port": int(os.getenv("SMTP_PORT", "587")),
            "username": os.getenv("SMTP_USERNAME", ""),
            "password": os.getenv("SMTP_PASSWORD", ""),
            "sender": os.getenv("SMTP_SENDER", ""),
            "recipients": os.getenv("SMTP_RECIPIENTS", "").split(","),
            "use_tls": True
        },
        "slack": {
            "enabled": False,
            "webhook_url": os.getenv("SLACK_WEBHOOK_URL", ""),
            "channel": os.getenv("SLACK_CHANNEL", "#alerts")
        }
    }
}

# Function to initialize monitoring based on configuration
def initialize_monitoring():
    """
    Initialize monitoring based on configuration.
    
    This function sets up logging, metrics collection, tracing, and alerting
    based on the configuration settings.
    """
    from ..utils.structured_logging import get_logger
    from ..utils.performance_metrics import start_metrics_collection
    from ..utils.distributed_tracing import configure_tracing
    from ..utils.alerting import (
        add_threshold_alert, add_percentage_change_alert,
        add_email_notifier, add_slack_notifier,
        AlertSeverity, start_alerting
    )
    
    # Set up logging
    logger = get_logger(
        "deep_research_core",
        level=LOG_CONFIG["default_level"],
        json_format=LOG_CONFIG["json_format"],
        include_stack_info=LOG_CONFIG["include_stack_info"],
        log_file=f"{LOG_CONFIG['log_dir']}/{LOG_CONFIG['main_log_file']}"
    )
    
    # Start metrics collection if enabled
    if METRICS_CONFIG["enabled"]:
        start_metrics_collection()
    
    # Configure tracing if enabled
    if TRACING_CONFIG["enabled"]:
        configure_tracing(
            service_name=TRACING_CONFIG["service_name"],
            use_opentelemetry=TRACING_CONFIG["use_opentelemetry"],
            otlp_endpoint=TRACING_CONFIG["otlp_endpoint"]
        )
    
    # Set up alerting if enabled
    if ALERTING_CONFIG["enabled"]:
        # Add threshold alerts
        for alert in ALERTING_CONFIG["alerts"]:
            if "threshold" in alert:
                add_threshold_alert(
                    name=alert["name"],
                    description=alert["description"],
                    metric_path=alert["metric_path"],
                    threshold=alert["threshold"],
                    comparison=alert["comparison"],
                    severity=getattr(AlertSeverity, alert["severity"].upper())
                )
            elif "percentage_change" in alert:
                add_percentage_change_alert(
                    name=alert["name"],
                    description=alert["description"],
                    metric_path=alert["metric_path"],
                    percentage_change=alert["percentage_change"],
                    direction=alert["direction"],
                    severity=getattr(AlertSeverity, alert["severity"].upper())
                )
        
        # Add email notifier if enabled
        if ALERTING_CONFIG["notifiers"]["email"]["enabled"]:
            email_config = ALERTING_CONFIG["notifiers"]["email"]
            add_email_notifier(
                smtp_server=email_config["smtp_server"],
                smtp_port=email_config["smtp_port"],
                username=email_config["username"],
                password=email_config["password"],
                sender=email_config["sender"],
                recipients=email_config["recipients"],
                use_tls=email_config["use_tls"]
            )
        
        # Add Slack notifier if enabled
        if ALERTING_CONFIG["notifiers"]["slack"]["enabled"]:
            slack_config = ALERTING_CONFIG["notifiers"]["slack"]
            add_slack_notifier(
                webhook_url=slack_config["webhook_url"],
                channel=slack_config["channel"]
            )
        
        # Start alerting
        start_alerting()
    
    logger.info("Monitoring initialized successfully")

# Function to shutdown monitoring
def shutdown_monitoring():
    """
    Shutdown monitoring.
    
    This function stops metrics collection, tracing, and alerting.
    """
    from ..utils.performance_metrics import stop_metrics_collection
    from ..utils.alerting import stop_alerting
    
    # Stop metrics collection if enabled
    if METRICS_CONFIG["enabled"]:
        stop_metrics_collection()
    
    # Stop alerting if enabled
    if ALERTING_CONFIG["enabled"]:
        stop_alerting()
