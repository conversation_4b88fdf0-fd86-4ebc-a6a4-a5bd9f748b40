"""
Module định nghĩa lớp cơ sở cho các plugin.

Module này cung cấp lớp BasePlugin làm lớp cơ sở cho tất cả các plugin.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List

# Thiết lập logging
from ..utils.structured_logging import get_logger
logger = get_logger(__name__)


class BasePlugin(ABC):
    """
    Lớp cơ sở cho tất cả các plugin.

    Lớp này định nghĩa giao diện chung cho tất cả các plugin.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Khởi tạo BasePlugin.

        Args:
            config: Cấu hình cho plugin
        """
        self.config = config or {}
        self.enabled = self.config.get("enabled", True)
        self.name = self.__class__.__name__
        logger.info(f"Plugin {self.name} được khởi tạo với cấu hình: {self.config}")

    def is_enabled(self) -> bool:
        """
        Kiểm tra xem plugin có được kích hoạt không.

        Returns:
            True nếu plugin được kích hoạt, False nếu không
        """
        return self.enabled

    def enable(self) -> None:
        """Kích hoạt plugin."""
        self.enabled = True
        logger.info(f"Plugin {self.name} đã được kích hoạt")

    def disable(self) -> None:
        """Vô hiệu hóa plugin."""
        self.enabled = False
        logger.info(f"Plugin {self.name} đã bị vô hiệu hóa")

    def get_config(self) -> Dict[str, Any]:
        """
        Lấy cấu hình của plugin.

        Returns:
            Cấu hình của plugin
        """
        return self.config.copy()

    def update_config(self, config: Dict[str, Any]) -> None:
        """
        Cập nhật cấu hình của plugin.

        Args:
            config: Cấu hình mới
        """
        self.config.update(config)
        logger.info(f"Đã cập nhật cấu hình cho plugin {self.name}: {config}")

    def get_name(self) -> str:
        """
        Lấy tên của plugin.

        Returns:
            Tên của plugin
        """
        return self.name

    def get_description(self) -> str:
        """
        Lấy mô tả của plugin.

        Returns:
            Mô tả của plugin
        """
        return self.__doc__ or "Không có mô tả"

    def get_version(self) -> str:
        """
        Lấy phiên bản của plugin.

        Returns:
            Phiên bản của plugin
        """
        return getattr(self, "VERSION", "1.0.0")

    def get_author(self) -> str:
        """
        Lấy tác giả của plugin.

        Returns:
            Tác giả của plugin
        """
        return getattr(self, "AUTHOR", "Unknown")

    def get_dependencies(self) -> List[str]:
        """
        Lấy danh sách các phụ thuộc của plugin.

        Returns:
            Danh sách các phụ thuộc của plugin
        """
        return getattr(self, "DEPENDENCIES", [])

    def get_hooks(self) -> List[str]:
        """
        Lấy danh sách các hook mà plugin đăng ký.

        Returns:
            Danh sách các hook
        """
        hooks = []
        for attr_name in dir(self):
            if attr_name.startswith('_'):
                continue

            attr = getattr(self, attr_name)
            if callable(attr) and hasattr(attr, '_is_hook') and attr._is_hook:
                hooks.append(attr_name)

        return hooks

    def initialize(self, agent=None) -> bool:
        """
        Khởi tạo plugin.

        Args:
            agent: WebSearchAgent (tùy chọn)

        Returns:
            True nếu khởi tạo thành công, False nếu không
        """
        return True

    def cleanup(self) -> None:
        """Dọn dẹp tài nguyên khi plugin bị hủy."""
        pass
