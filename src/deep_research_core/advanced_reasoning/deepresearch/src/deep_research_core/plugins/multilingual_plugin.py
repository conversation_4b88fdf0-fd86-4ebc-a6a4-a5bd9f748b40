"""
Module cung cấp plugin hỗ trợ đa ngôn ngữ.

Module này cung cấp MultilingualPlugin để hỗ trợ tìm kiếm đa ngôn ngữ.
"""

import re
from typing import Dict, Any, List, Optional, Set

from .base_plugin import BasePlugin
from ..utils.plugin_manager import hook
from ..utils.structured_logging import get_logger

logger = get_logger(__name__)


class MultilingualPlugin(BasePlugin):
    """
    Plugin hỗ trợ tìm kiếm đa ngôn ngữ.

    Plugin này cung cấp các phương thức để hỗ trợ tìm kiếm đa ngôn ngữ,
    bao gồm phát hiện ngôn ngữ, dịch tru<PERSON> vấn, và xử lý kết quả đa ngôn ngữ.
    """

    VERSION = "1.0.0"
    AUTHOR = "Augment Code"
    DEPENDENCIES = []

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Khởi tạo MultilingualPlugin.

        Args:
            config: Cấu hình cho plugin
        """
        default_config = {
            "enabled": True,
            "default_language": "vi",
            "supported_languages": ["vi", "en", "fr", "de", "es", "zh", "ja", "ko"],
            "detect_language": True,
            "translate_query": True,
            "translate_results": False,
            "use_language_specific_engines": True,
            "language_specific_engines": {
                "vi": ["searxng"],
                "en": ["searxng"],
                "fr": ["searxng"],
                "de": ["searxng"],
                "es": ["searxng"],
                "zh": ["searxng"],
                "ja": ["searxng"],
                "ko": ["searxng"]
            },
            "language_specific_domains": {
                "vi": [".vn", ".com.vn"],
                "en": [".com", ".org", ".net", ".edu", ".gov"],
                "fr": [".fr"],
                "de": [".de"],
                "es": [".es"],
                "zh": [".cn", ".com.cn"],
                "ja": [".jp"],
                "ko": [".kr"]
            },
            "language_specific_stopwords": {
                "vi": ["và", "hoặc", "nhưng", "vì", "nếu", "khi", "là", "có", "không", "được", "trong", "ngoài", "trên", "dưới", "của", "với", "cho", "bởi", "về", "theo", "từ", "đến", "tại", "cùng", "như", "để", "do", "bị", "được", "tại", "vào", "ra", "lên", "xuống", "qua", "lại", "đi", "đến", "về", "theo", "trước", "sau", "giữa", "trong", "ngoài", "trên", "dưới", "gần", "xa", "đây", "đó", "kia", "này", "ấy", "vậy", "thế", "nào", "sao", "vì", "tại", "bởi", "do", "vậy", "nên", "mà", "thì", "là", "của", "và", "hay", "hoặc", "nhưng", "mà", "nếu", "như", "khi", "lúc", "hồi", "xưa", "trước", "sau", "lâu", "chóng", "mau", "rồi", "sắp", "sẽ", "đã", "đang", "vừa", "mới", "cũng", "còn", "nữa", "lại", "vẫn", "mãi"],
                "en": ["and", "or", "but", "if", "when", "is", "are", "was", "were", "be", "been", "being", "have", "has", "had", "do", "does", "did", "will", "would", "shall", "should", "may", "might", "must", "can", "could", "a", "an", "the", "this", "that", "these", "those", "my", "your", "his", "her", "its", "our", "their", "mine", "yours", "his", "hers", "ours", "theirs", "i", "you", "he", "she", "it", "we", "they", "me", "him", "her", "us", "them", "who", "whom", "which", "what", "whose", "whoever", "whatever", "whichever", "whomever", "where", "when", "why", "how", "wherever", "whenever", "however", "as", "at", "by", "for", "from", "in", "into", "of", "on", "to", "with", "about", "against", "between", "during", "through", "throughout", "above", "below", "over", "under", "up", "down", "off", "on", "out", "in", "again", "further", "then", "once", "here", "there", "all", "any", "both", "each", "few", "more", "most", "other", "some", "such", "no", "nor", "not", "only", "own", "same", "so", "than", "too", "very", "s", "t", "just", "don", "now"]
            },
            "use_external_translation": False,
            "translation_api_key": "",
            "translation_api_url": "",
            "use_external_language_detection": False,
            "language_detection_api_key": "",
            "language_detection_api_url": ""
        }
        
        # Kết hợp cấu hình mặc định và cấu hình người dùng
        merged_config = default_config.copy()
        if config:
            merged_config.update(config)
        
        super().__init__(merged_config)
        
        # Khởi tạo các thành phần cần thiết
        self._initialize_components()

    def _initialize_components(self) -> None:
        """Khởi tạo các thành phần cần thiết cho plugin."""
        # Khởi tạo các thành phần tùy thuộc vào cấu hình
        self.default_language = self.config.get("default_language", "vi")
        self.supported_languages = set(self.config.get("supported_languages", ["vi", "en"]))
        
        # Kiểm tra xem ngôn ngữ mặc định có được hỗ trợ không
        if self.default_language not in self.supported_languages:
            logger.warning(f"Ngôn ngữ mặc định {self.default_language} không được hỗ trợ. Sử dụng 'vi' làm ngôn ngữ mặc định.")
            self.default_language = "vi"
        
        # Khởi tạo các thành phần phát hiện ngôn ngữ
        self._initialize_language_detection()
        
        # Khởi tạo các thành phần dịch
        self._initialize_translation()

    def _initialize_language_detection(self) -> None:
        """Khởi tạo các thành phần phát hiện ngôn ngữ."""
        # Kiểm tra xem có sử dụng phát hiện ngôn ngữ bên ngoài không
        self.use_external_language_detection = self.config.get("use_external_language_detection", False)
        
        if self.use_external_language_detection:
            # Kiểm tra xem có API key và URL không
            self.language_detection_api_key = self.config.get("language_detection_api_key", "")
            self.language_detection_api_url = self.config.get("language_detection_api_url", "")
            
            if not self.language_detection_api_key or not self.language_detection_api_url:
                logger.warning("API key hoặc URL phát hiện ngôn ngữ không được cung cấp. Sẽ sử dụng phát hiện ngôn ngữ cục bộ.")
                self.use_external_language_detection = False
        
        # Nếu không sử dụng phát hiện ngôn ngữ bên ngoài, thử nhập các thư viện cần thiết
        if not self.use_external_language_detection:
            try:
                import langdetect
                self.langdetect_available = True
            except ImportError:
                logger.warning("langdetect không có sẵn. Sẽ sử dụng phương pháp đơn giản hơn.")
                self.langdetect_available = False

    def _initialize_translation(self) -> None:
        """Khởi tạo các thành phần dịch."""
        # Kiểm tra xem có sử dụng dịch bên ngoài không
        self.use_external_translation = self.config.get("use_external_translation", False)
        
        if self.use_external_translation:
            # Kiểm tra xem có API key và URL không
            self.translation_api_key = self.config.get("translation_api_key", "")
            self.translation_api_url = self.config.get("translation_api_url", "")
            
            if not self.translation_api_key or not self.translation_api_url:
                logger.warning("API key hoặc URL dịch không được cung cấp. Sẽ không sử dụng dịch.")
                self.use_external_translation = False

    @hook(priority=20)
    def pre_search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Hook được gọi trước khi thực hiện tìm kiếm.

        Args:
            query: Truy vấn tìm kiếm
            **kwargs: Các tham số khác

        Returns:
            Dictionary chứa truy vấn đã xử lý và các thông tin khác
        """
        if not self.is_enabled():
            return {"query": query}
        
        # Lấy ngôn ngữ từ tham số hoặc phát hiện ngôn ngữ
        language = kwargs.get("language", self.default_language)
        
        # Nếu không có ngôn ngữ và cấu hình phát hiện ngôn ngữ, phát hiện ngôn ngữ
        if not language and self.config.get("detect_language", True):
            language = self._detect_language(query)
        
        # Nếu vẫn không có ngôn ngữ, sử dụng ngôn ngữ mặc định
        if not language:
            language = self.default_language
        
        # Kiểm tra xem ngôn ngữ có được hỗ trợ không
        if language not in self.supported_languages:
            logger.warning(f"Ngôn ngữ {language} không được hỗ trợ. Sử dụng {self.default_language} làm ngôn ngữ.")
            language = self.default_language
        
        # Xử lý truy vấn dựa trên ngôn ngữ
        processed_query = self._process_query(query, language)
        
        # Dịch truy vấn nếu được cấu hình
        translated_query = None
        if self.config.get("translate_query", True) and language != self.default_language:
            translated_query = self._translate_query(query, language, self.default_language)
        
        # Lấy các công cụ tìm kiếm cụ thể cho ngôn ngữ
        engines = None
        if self.config.get("use_language_specific_engines", True):
            engines = self.config.get("language_specific_engines", {}).get(language, None)
        
        # Trả về kết quả
        result = {
            "query": processed_query,
            "original_query": query,
            "language": language
        }
        
        if translated_query:
            result["translated_query"] = translated_query
        
        if engines:
            result["engines"] = engines
        
        return result

    @hook(priority=70)
    def post_search(self, results: List[Dict[str, Any]], language: str = None, **kwargs) -> Dict[str, Any]:
        """
        Hook được gọi sau khi thực hiện tìm kiếm.

        Args:
            results: Danh sách kết quả tìm kiếm
            language: Ngôn ngữ của truy vấn
            **kwargs: Các tham số khác

        Returns:
            Dictionary chứa kết quả đã xử lý và các thông tin khác
        """
        if not self.is_enabled() or not results:
            return {"results": results}
        
        # Nếu không có ngôn ngữ, sử dụng ngôn ngữ mặc định
        if not language:
            language = self.default_language
        
        # Xử lý kết quả dựa trên ngôn ngữ
        processed_results = self._process_results(results, language)
        
        # Dịch kết quả nếu được cấu hình
        if self.config.get("translate_results", False) and language != self.default_language:
            processed_results = self._translate_results(processed_results, language, self.default_language)
        
        # Trả về kết quả
        return {
            "results": processed_results,
            "original_results": results,
            "language": language
        }

    def _detect_language(self, text: str) -> str:
        """
        Phát hiện ngôn ngữ của văn bản.

        Args:
            text: Văn bản cần phát hiện ngôn ngữ

        Returns:
            Mã ngôn ngữ (ISO 639-1)
        """
        if not text:
            return self.default_language
        
        # Nếu sử dụng phát hiện ngôn ngữ bên ngoài
        if self.use_external_language_detection:
            return self._detect_language_external(text)
        
        # Nếu có langdetect
        if self.langdetect_available:
            try:
                import langdetect
                return langdetect.detect(text)
            except Exception as e:
                logger.warning(f"Lỗi khi phát hiện ngôn ngữ với langdetect: {str(e)}")
        
        # Sử dụng phương pháp đơn giản
        return self._detect_language_simple(text)

    def _detect_language_external(self, text: str) -> str:
        """
        Phát hiện ngôn ngữ của văn bản bằng API bên ngoài.

        Args:
            text: Văn bản cần phát hiện ngôn ngữ

        Returns:
            Mã ngôn ngữ (ISO 639-1)
        """
        # Triển khai phương thức phát hiện ngôn ngữ bên ngoài
        # Đây là phiên bản đơn giản, có thể cải thiện trong tương lai
        return self.default_language

    def _detect_language_simple(self, text: str) -> str:
        """
        Phát hiện ngôn ngữ của văn bản bằng phương pháp đơn giản.

        Args:
            text: Văn bản cần phát hiện ngôn ngữ

        Returns:
            Mã ngôn ngữ (ISO 639-1)
        """
        # Triển khai phương thức phát hiện ngôn ngữ đơn giản
        # Đây là phiên bản đơn giản, có thể cải thiện trong tương lai
        
        # Đếm số lượng stopwords của từng ngôn ngữ
        language_scores = {}
        
        for language, stopwords in self.config.get("language_specific_stopwords", {}).items():
            if language not in self.supported_languages:
                continue
            
            score = 0
            for word in text.lower().split():
                if word in stopwords:
                    score += 1
            
            language_scores[language] = score
        
        # Nếu không có ngôn ngữ nào có điểm, trả về ngôn ngữ mặc định
        if not language_scores:
            return self.default_language
        
        # Trả về ngôn ngữ có điểm cao nhất
        return max(language_scores.items(), key=lambda x: x[1])[0]

    def _process_query(self, query: str, language: str) -> str:
        """
        Xử lý truy vấn dựa trên ngôn ngữ.

        Args:
            query: Truy vấn tìm kiếm
            language: Ngôn ngữ của truy vấn

        Returns:
            Truy vấn đã xử lý
        """
        # Triển khai phương thức xử lý truy vấn
        # Đây là phiên bản đơn giản, có thể cải thiện trong tương lai
        return query

    def _translate_query(self, query: str, source_language: str, target_language: str) -> str:
        """
        Dịch truy vấn từ ngôn ngữ nguồn sang ngôn ngữ đích.

        Args:
            query: Truy vấn tìm kiếm
            source_language: Ngôn ngữ nguồn
            target_language: Ngôn ngữ đích

        Returns:
            Truy vấn đã dịch
        """
        # Triển khai phương thức dịch truy vấn
        # Đây là phiên bản đơn giản, có thể cải thiện trong tương lai
        
        # Nếu sử dụng dịch bên ngoài
        if self.use_external_translation:
            return self._translate_external(query, source_language, target_language)
        
        # Nếu không sử dụng dịch bên ngoài, trả về truy vấn gốc
        return query

    def _translate_external(self, text: str, source_language: str, target_language: str) -> str:
        """
        Dịch văn bản bằng API bên ngoài.

        Args:
            text: Văn bản cần dịch
            source_language: Ngôn ngữ nguồn
            target_language: Ngôn ngữ đích

        Returns:
            Văn bản đã dịch
        """
        # Triển khai phương thức dịch bên ngoài
        # Đây là phiên bản đơn giản, có thể cải thiện trong tương lai
        return text

    def _process_results(self, results: List[Dict[str, Any]], language: str) -> List[Dict[str, Any]]:
        """
        Xử lý kết quả dựa trên ngôn ngữ.

        Args:
            results: Danh sách kết quả tìm kiếm
            language: Ngôn ngữ của truy vấn

        Returns:
            Danh sách kết quả đã xử lý
        """
        # Triển khai phương thức xử lý kết quả
        # Đây là phiên bản đơn giản, có thể cải thiện trong tương lai
        return results

    def _translate_results(self, results: List[Dict[str, Any]], source_language: str, target_language: str) -> List[Dict[str, Any]]:
        """
        Dịch kết quả từ ngôn ngữ nguồn sang ngôn ngữ đích.

        Args:
            results: Danh sách kết quả tìm kiếm
            source_language: Ngôn ngữ nguồn
            target_language: Ngôn ngữ đích

        Returns:
            Danh sách kết quả đã dịch
        """
        # Triển khai phương thức dịch kết quả
        # Đây là phiên bản đơn giản, có thể cải thiện trong tương lai
        return results
