"""
Module định nghĩa ContentEnhancementPlugin.

Plugin này cải thiện nội dung trích xuất từ các trang web.
"""

from typing import Dict, Any, List, Optional
import re
import html
import logging
from ..plugins.base_plugin import BasePlugin
from ..utils.plugin_manager import hook

# Thiết lập logging
from ..utils.structured_logging import get_logger
logger = get_logger(__name__)


class ContentEnhancementPlugin(BasePlugin):
    """
    Plugin cải thiện nội dung trích xuất từ các trang web.
    
    Tính năng:
    - <PERSON><PERSON><PERSON> bỏ các phần thừa (quảng cáo, footer, header, v.v.)
    - Chuẩn hóa định dạng văn bản
    - Tr<PERSON>ch xuất thông tin có cấu trúc (bảng, danh sách, v.v.)
    - <PERSON><PERSON><PERSON> <PERSON><PERSON> hóa nội dung cho LLM
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Khởi tạo ContentEnhancementPlugin.
        
        Args:
            config: C<PERSON>u hình cho plugin
        """
        default_config = {
            "enabled": True,
            "remove_ads": True,
            "remove_navigation": True,
            "remove_footer": True,
            "normalize_whitespace": True,
            "extract_tables": True,
            "extract_lists": True,
            "optimize_for_llm": True,
            "max_content_length": 10000,
            "preserve_headings": True,
            "preserve_links": True,
            "preserve_images": False,
            "preserve_code_blocks": True,
            "remove_duplicate_paragraphs": True,
            "remove_empty_lines": True,
            "remove_html_comments": True,
            "remove_scripts": True,
            "remove_styles": True,
            "remove_social_media_widgets": True,
            "remove_cookie_notices": True,
            "remove_newsletter_signup": True,
            "remove_related_articles": False,
            "remove_pagination": True,
            "extract_main_content": True,
            "extract_metadata": True,
            "extract_author": True,
            "extract_date": True,
            "extract_title": True,
            "extract_description": True,
            "extract_keywords": True,
            "extract_categories": True,
            "extract_tags": True,
            "extract_images": False,
            "extract_videos": False,
            "extract_audio": False,
            "extract_files": False,
            "extract_links": True,
            "extract_emails": True,
            "extract_phone_numbers": True,
            "extract_addresses": True,
            "extract_prices": True,
            "extract_ratings": True,
            "extract_reviews": True,
            "extract_comments": False,
            "extract_social_media": False,
            "extract_code": True,
            "extract_math": True,
            "extract_formulas": True,
            "extract_equations": True,
            "extract_citations": True,
            "extract_references": True,
            "extract_bibliography": True,
            "extract_glossary": True,
            "extract_index": True,
            "extract_appendix": True,
            "extract_footnotes": True,
            "extract_endnotes": True,
            "extract_annotations": True,
            "extract_highlights": True,
            "extract_underlines": True,
            "extract_strikethroughs": True,
            "extract_superscripts": True,
            "extract_subscripts": True,
            "extract_small_text": True,
            "extract_large_text": True,
            "extract_bold_text": True,
            "extract_italic_text": True,
            "extract_monospace_text": True,
            "extract_colored_text": True,
            "extract_background_colored_text": True,
        }
        
        # Merge default config with provided config
        merged_config = {**default_config, **(config or {})}
        super().__init__(merged_config)
        
        # Initialize plugin
        self.name = "ContentEnhancementPlugin"
        self.version = "1.0.0"
        self.description = "Plugin cải thiện nội dung trích xuất từ các trang web"
        self.author = "Augment Code"
        
        logger.info(f"ContentEnhancementPlugin initialized with config: {self.config}")
    
    @hook(priority=50)
    def post_extract_content(self, url: str, content: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Hook được gọi sau khi trích xuất nội dung.
        
        Args:
            url: URL của trang web
            content: Nội dung đã trích xuất
            **kwargs: Các tham số khác
            
        Returns:
            Dict[str, Any]: Nội dung đã cải thiện
        """
        if not self.is_enabled():
            return content
        
        # Kiểm tra nội dung
        if not content or not isinstance(content, dict):
            logger.warning(f"Invalid content for URL: {url}")
            return content
        
        # Lấy nội dung văn bản
        text = content.get("text", "")
        html_content = content.get("html", "")
        
        # Cải thiện nội dung
        enhanced_content = self._enhance_content(text, html_content, url)
        
        # Cập nhật nội dung
        content["text"] = enhanced_content.get("text", text)
        content["enhanced"] = True
        content["enhancement_info"] = {
            "plugin": self.name,
            "version": self.version,
            "enhancements_applied": enhanced_content.get("enhancements_applied", []),
            "original_length": len(text),
            "enhanced_length": len(content["text"]),
            "improvement_ratio": len(content["text"]) / max(1, len(text))
        }
        
        # Thêm metadata nếu có
        if "metadata" in enhanced_content:
            content["metadata"] = enhanced_content.get("metadata", {})
        
        # Thêm các phần trích xuất nếu có
        if "extracted_elements" in enhanced_content:
            content["extracted_elements"] = enhanced_content.get("extracted_elements", {})
        
        logger.info(f"Enhanced content for URL: {url}")
        return content
    
    def _enhance_content(self, text: str, html_content: str, url: str) -> Dict[str, Any]:
        """
        Cải thiện nội dung văn bản.
        
        Args:
            text: Nội dung văn bản
            html_content: Nội dung HTML
            url: URL của trang web
            
        Returns:
            Dict[str, Any]: Nội dung đã cải thiện
        """
        if not text:
            return {"text": "", "enhancements_applied": []}
        
        enhancements_applied = []
        enhanced_text = text
        metadata = {}
        extracted_elements = {}
        
        # Loại bỏ các phần thừa
        if self.config.get("remove_ads", True):
            enhanced_text = self._remove_ads(enhanced_text)
            enhancements_applied.append("remove_ads")
        
        if self.config.get("remove_navigation", True):
            enhanced_text = self._remove_navigation(enhanced_text)
            enhancements_applied.append("remove_navigation")
        
        if self.config.get("remove_footer", True):
            enhanced_text = self._remove_footer(enhanced_text)
            enhancements_applied.append("remove_footer")
        
        # Chuẩn hóa định dạng văn bản
        if self.config.get("normalize_whitespace", True):
            enhanced_text = self._normalize_whitespace(enhanced_text)
            enhancements_applied.append("normalize_whitespace")
        
        if self.config.get("remove_duplicate_paragraphs", True):
            enhanced_text = self._remove_duplicate_paragraphs(enhanced_text)
            enhancements_applied.append("remove_duplicate_paragraphs")
        
        if self.config.get("remove_empty_lines", True):
            enhanced_text = self._remove_empty_lines(enhanced_text)
            enhancements_applied.append("remove_empty_lines")
        
        # Trích xuất thông tin có cấu trúc
        if self.config.get("extract_tables", True) and html_content:
            tables = self._extract_tables(html_content)
            if tables:
                extracted_elements["tables"] = tables
                enhancements_applied.append("extract_tables")
        
        if self.config.get("extract_lists", True) and html_content:
            lists = self._extract_lists(html_content)
            if lists:
                extracted_elements["lists"] = lists
                enhancements_applied.append("extract_lists")
        
        # Trích xuất metadata
        if self.config.get("extract_metadata", True) and html_content:
            metadata = self._extract_metadata(html_content, url)
            enhancements_applied.append("extract_metadata")
        
        # Giới hạn độ dài nội dung
        max_length = self.config.get("max_content_length", 10000)
        if len(enhanced_text) > max_length:
            enhanced_text = enhanced_text[:max_length] + "..."
            enhancements_applied.append("limit_content_length")
        
        return {
            "text": enhanced_text,
            "enhancements_applied": enhancements_applied,
            "metadata": metadata,
            "extracted_elements": extracted_elements
        }
    
    def _remove_ads(self, text: str) -> str:
        """
        Loại bỏ quảng cáo khỏi nội dung.
        
        Args:
            text: Nội dung văn bản
            
        Returns:
            str: Nội dung đã loại bỏ quảng cáo
        """
        # Các mẫu quảng cáo phổ biến
        ad_patterns = [
            r"Advertisement\s*\n",
            r"Sponsored\s*\n",
            r"Ads by Google",
            r"Google Ads",
            r"ADVERTISEMENT",
            r"\[Advertisement\]",
            r"Sponsored Content",
            r"Sponsored Links",
            r"Promoted Content",
            r"Recommended for you",
            r"You might also like",
            r"From our sponsors",
            r"Paid Content",
            r"Paid Partnership",
            r"Partner Content",
        ]
        
        # Loại bỏ các mẫu quảng cáo
        for pattern in ad_patterns:
            text = re.sub(pattern, "", text, flags=re.IGNORECASE)
        
        return text
    
    def _remove_navigation(self, text: str) -> str:
        """
        Loại bỏ phần điều hướng khỏi nội dung.
        
        Args:
            text: Nội dung văn bản
            
        Returns:
            str: Nội dung đã loại bỏ phần điều hướng
        """
        # Các mẫu điều hướng phổ biến
        nav_patterns = [
            r"Home\s*>\s*.*?\s*>\s*.*?\n",
            r"Navigation\s*\n",
            r"Menu\s*\n",
            r"Main Menu\s*\n",
            r"Skip to content",
            r"Skip to main content",
            r"Jump to navigation",
            r"Jump to content",
            r"Breadcrumbs",
            r"You are here:",
            r"Site Map",
            r"Site Index",
        ]
        
        # Loại bỏ các mẫu điều hướng
        for pattern in nav_patterns:
            text = re.sub(pattern, "", text, flags=re.IGNORECASE)
        
        return text
    
    def _remove_footer(self, text: str) -> str:
        """
        Loại bỏ phần footer khỏi nội dung.
        
        Args:
            text: Nội dung văn bản
            
        Returns:
            str: Nội dung đã loại bỏ phần footer
        """
        # Các mẫu footer phổ biến
        footer_patterns = [
            r"Footer\s*\n",
            r"Copyright © \d{4}",
            r"All rights reserved",
            r"Terms of Service",
            r"Privacy Policy",
            r"Contact Us",
            r"About Us",
            r"Follow us on",
            r"Connect with us",
            r"Share this",
            r"Share on",
            r"Tweet",
            r"Like",
            r"Subscribe",
            r"Newsletter",
            r"Sign up for our newsletter",
        ]
        
        # Loại bỏ các mẫu footer
        for pattern in footer_patterns:
            text = re.sub(pattern, "", text, flags=re.IGNORECASE)
        
        return text
    
    def _normalize_whitespace(self, text: str) -> str:
        """
        Chuẩn hóa khoảng trắng trong nội dung.
        
        Args:
            text: Nội dung văn bản
            
        Returns:
            str: Nội dung đã chuẩn hóa khoảng trắng
        """
        # Loại bỏ khoảng trắng thừa
        text = re.sub(r"\s+", " ", text)
        
        # Chuẩn hóa dấu xuống dòng
        text = re.sub(r"\n\s*\n\s*\n", "\n\n", text)
        
        # Chuẩn hóa khoảng trắng đầu dòng
        text = re.sub(r"^\s+", "", text, flags=re.MULTILINE)
        
        return text
    
    def _remove_duplicate_paragraphs(self, text: str) -> str:
        """
        Loại bỏ các đoạn văn trùng lặp.
        
        Args:
            text: Nội dung văn bản
            
        Returns:
            str: Nội dung đã loại bỏ các đoạn văn trùng lặp
        """
        # Tách thành các đoạn văn
        paragraphs = text.split("\n\n")
        
        # Loại bỏ các đoạn văn trùng lặp
        unique_paragraphs = []
        for paragraph in paragraphs:
            if paragraph and paragraph not in unique_paragraphs:
                unique_paragraphs.append(paragraph)
        
        # Ghép lại thành văn bản
        return "\n\n".join(unique_paragraphs)
    
    def _remove_empty_lines(self, text: str) -> str:
        """
        Loại bỏ các dòng trống.
        
        Args:
            text: Nội dung văn bản
            
        Returns:
            str: Nội dung đã loại bỏ các dòng trống
        """
        # Loại bỏ các dòng trống
        return re.sub(r"^\s*$\n", "", text, flags=re.MULTILINE)
    
    def _extract_tables(self, html_content: str) -> List[Dict[str, Any]]:
        """
        Trích xuất bảng từ nội dung HTML.
        
        Args:
            html_content: Nội dung HTML
            
        Returns:
            List[Dict[str, Any]]: Danh sách các bảng
        """
        tables = []
        
        # Tìm tất cả các thẻ table
        table_pattern = r"<table[^>]*>(.*?)</table>"
        table_matches = re.findall(table_pattern, html_content, re.DOTALL)
        
        for i, table_html in enumerate(table_matches):
            # Trích xuất các hàng
            row_pattern = r"<tr[^>]*>(.*?)</tr>"
            row_matches = re.findall(row_pattern, table_html, re.DOTALL)
            
            rows = []
            for row_html in row_matches:
                # Trích xuất các ô
                cell_pattern = r"<t[dh][^>]*>(.*?)</t[dh]>"
                cell_matches = re.findall(cell_pattern, row_html, re.DOTALL)
                
                # Loại bỏ các thẻ HTML và chuẩn hóa nội dung
                cells = [html.unescape(re.sub(r"<[^>]*>", "", cell)) for cell in cell_matches]
                rows.append(cells)
            
            # Thêm bảng vào danh sách
            if rows:
                tables.append({
                    "id": i + 1,
                    "rows": rows,
                    "num_rows": len(rows),
                    "num_cols": max([len(row) for row in rows]) if rows else 0
                })
        
        return tables
    
    def _extract_lists(self, html_content: str) -> List[Dict[str, Any]]:
        """
        Trích xuất danh sách từ nội dung HTML.
        
        Args:
            html_content: Nội dung HTML
            
        Returns:
            List[Dict[str, Any]]: Danh sách các danh sách
        """
        lists = []
        
        # Tìm tất cả các thẻ ul và ol
        list_pattern = r"<(ul|ol)[^>]*>(.*?)</\1>"
        list_matches = re.findall(list_pattern, html_content, re.DOTALL)
        
        for i, (list_type, list_html) in enumerate(list_matches):
            # Trích xuất các mục
            item_pattern = r"<li[^>]*>(.*?)</li>"
            item_matches = re.findall(item_pattern, list_html, re.DOTALL)
            
            # Loại bỏ các thẻ HTML và chuẩn hóa nội dung
            items = [html.unescape(re.sub(r"<[^>]*>", "", item)) for item in item_matches]
            
            # Thêm danh sách vào danh sách
            if items:
                lists.append({
                    "id": i + 1,
                    "type": "ordered" if list_type == "ol" else "unordered",
                    "items": items,
                    "num_items": len(items)
                })
        
        return lists
    
    def _extract_metadata(self, html_content: str, url: str) -> Dict[str, Any]:
        """
        Trích xuất metadata từ nội dung HTML.
        
        Args:
            html_content: Nội dung HTML
            url: URL của trang web
            
        Returns:
            Dict[str, Any]: Metadata
        """
        metadata = {
            "url": url
        }
        
        # Trích xuất tiêu đề
        if self.config.get("extract_title", True):
            title_match = re.search(r"<title[^>]*>(.*?)</title>", html_content, re.DOTALL)
            if title_match:
                metadata["title"] = html.unescape(re.sub(r"<[^>]*>", "", title_match.group(1)))
        
        # Trích xuất mô tả
        if self.config.get("extract_description", True):
            desc_match = re.search(r'<meta\s+name="description"\s+content="([^"]*)"', html_content)
            if desc_match:
                metadata["description"] = html.unescape(desc_match.group(1))
        
        # Trích xuất từ khóa
        if self.config.get("extract_keywords", True):
            keywords_match = re.search(r'<meta\s+name="keywords"\s+content="([^"]*)"', html_content)
            if keywords_match:
                metadata["keywords"] = html.unescape(keywords_match.group(1)).split(",")
        
        # Trích xuất tác giả
        if self.config.get("extract_author", True):
            author_match = re.search(r'<meta\s+name="author"\s+content="([^"]*)"', html_content)
            if author_match:
                metadata["author"] = html.unescape(author_match.group(1))
        
        # Trích xuất ngày
        if self.config.get("extract_date", True):
            date_match = re.search(r'<meta\s+name="date"\s+content="([^"]*)"', html_content)
            if date_match:
                metadata["date"] = html.unescape(date_match.group(1))
        
        return metadata
