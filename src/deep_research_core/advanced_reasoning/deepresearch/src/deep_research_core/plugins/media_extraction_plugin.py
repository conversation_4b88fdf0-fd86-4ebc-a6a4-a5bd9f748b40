"""
Module cung cấp plugin trích xuất đa phương tiện.

Module này triển khai MediaExtractionPlugin để trích xuất hình ảnh, video,
audio và các tài nguyên đa phương tiện khác từ kết quả tìm kiếm.
"""

import re
import os
import time
import urllib.parse
from typing import Dict, Any, List, Optional, Set, Tuple
from urllib.parse import urljoin, urlparse

from .base_plugin import BasePlugin
from ..utils.plugin_manager import hook
from ..utils.structured_logging import get_logger

# Thiết lập logger
logger = get_logger(__name__)

class MediaExtractionPlugin(BasePlugin):
    """
    Plugin trích xuất đa phương tiện.
    
    Plugin này cung cấp khả năng trích xuất các tài nguyên đa phương tiện từ kết quả tìm kiếm:
    - Trích xuất URL hình ảnh
    - Trích xuất URL video
    - Trích xuất URL audio
    - Tùy chọn tải xuống các tài nguyên đa phương tiện
    """
    
    VERSION = "1.0.0"
    AUTHOR = "Augment Code"
    DEPENDENCIES = []
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Khởi tạo MediaExtractionPlugin.
        
        Args:
            config: Cấu hình cho plugin
        """
        default_config = {
            "enabled": True,
            "extract_images": True,
            "extract_videos": True,
            "extract_audio": True,
            "download_media": False,
            "download_dir": "downloads",
            "max_downloads_per_type": 5,
            "min_image_size": 100,  # Kích thước tối thiểu (px)
            "max_download_size": 10 * 1024 * 1024,  # 10MB
            "supported_image_extensions": [".jpg", ".jpeg", ".png", ".gif", ".webp", ".svg"],
            "supported_video_extensions": [".mp4", ".webm", ".ogg", ".mov", ".avi", ".mkv"],
            "supported_audio_extensions": [".mp3", ".wav", ".ogg", ".m4a", ".flac"],
            "extract_embedded_media": True,
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        
        # Kết hợp cấu hình mặc định và cấu hình người dùng
        merged_config = default_config.copy()
        if config:
            merged_config.update(config)
        
        super().__init__(merged_config)
        
        # Khởi tạo các thành phần cần thiết
        self._initialize_components()
        
        # Cache cho kết quả đã trích xuất
        self.media_cache = {}
    
    def _initialize_components(self):
        """Khởi tạo các thành phần cần thiết."""
        # Tạo thư mục tải xuống nếu cần
        if self.config.get("download_media", False):
            download_dir = self.config.get("download_dir", "downloads")
            os.makedirs(download_dir, exist_ok=True)
            
            # Tạo thư mục con cho từng loại media
            os.makedirs(os.path.join(download_dir, "images"), exist_ok=True)
            os.makedirs(os.path.join(download_dir, "videos"), exist_ok=True)
            os.makedirs(os.path.join(download_dir, "audio"), exist_ok=True)
        
        # Thử nhập thư viện requests
        try:
            import requests
            self.requests_available = True
        except ImportError:
            logger.warning("Thư viện requests không có sẵn. Khả năng tải xuống sẽ bị hạn chế.")
            self.requests_available = False
        
        # Thử nhập thư viện BeautifulSoup
        try:
            from bs4 import BeautifulSoup
            self.bs4_available = True
        except ImportError:
            logger.warning("Thư viện BeautifulSoup không có sẵn. Khả năng phân tích HTML sẽ bị hạn chế.")
            self.bs4_available = False
    
    @hook(priority=75)
    def post_search(self, results: List[Dict[str, Any]], **kwargs) -> Dict[str, Any]:
        """
        Hook được gọi sau khi thực hiện tìm kiếm để trích xuất media.
        
        Args:
            results: Danh sách kết quả tìm kiếm
            **kwargs: Các tham số bổ sung
        
        Returns:
            Dictionary chứa kết quả đã phân tích
        """
        if not self.is_enabled() or not results:
            return {"results": results}
        
        # Bắt đầu thời gian đo hiệu suất
        start_time = time.time()
        
        # Tạo cache key từ kết quả
        cache_key = hash(str(results))
        
        # Nếu đã trích xuất trước đó, trả về kết quả từ cache
        if cache_key in self.media_cache:
            logger.debug("Sử dụng kết quả media đã lưu trong cache")
            media_result = self.media_cache[cache_key]
            
            # Cập nhật thời gian
            media_result["performance"]["time_taken"] = time.time() - start_time
            
            return media_result
        
        # Trích xuất media từ kết quả
        extracted_results = []
        all_media = {
            "images": [],
            "videos": [],
            "audio": []
        }
        
        for result in results:
            # Trích xuất HTML content nếu có
            html_content = result.get("html_content", result.get("content", ""))
            url = result.get("url", "")
            
            # Trích xuất media
            media = {}
            
            # Trích xuất hình ảnh nếu được cấu hình
            if self.config.get("extract_images", True):
                images = self._extract_images(html_content, url)
                media["images"] = images
                all_media["images"].extend(images)
            
            # Trích xuất video nếu được cấu hình
            if self.config.get("extract_videos", True):
                videos = self._extract_videos(html_content, url)
                media["videos"] = videos
                all_media["videos"].extend(videos)
            
            # Trích xuất audio nếu được cấu hình
            if self.config.get("extract_audio", True):
                audio = self._extract_audio(html_content, url)
                media["audio"] = audio
                all_media["audio"].extend(audio)
            
            # Thêm media vào kết quả
            result["media"] = media
            extracted_results.append(result)
        
        # Loại bỏ trùng lặp
        all_media["images"] = list({media["url"]: media for media in all_media["images"]}.values())
        all_media["videos"] = list({media["url"]: media for media in all_media["videos"]}.values())
        all_media["audio"] = list({media["url"]: media for media in all_media["audio"]}.values())
        
        # Tải xuống media nếu được cấu hình
        if self.config.get("download_media", False) and self.requests_available:
            self._download_media(all_media)
        
        # Kết thúc thời gian đo hiệu suất
        time_taken = time.time() - start_time
        
        # Tạo kết quả
        media_result = {
            "results": extracted_results,
            "all_media": all_media,
            "performance": {
                "time_taken": time_taken,
                "images_extracted": len(all_media["images"]),
                "videos_extracted": len(all_media["videos"]),
                "audio_extracted": len(all_media["audio"])
            }
        }
        
        # Lưu vào cache
        self.media_cache[cache_key] = media_result
        
        return media_result
    
    def _extract_images(self, html_content: str, base_url: str) -> List[Dict[str, Any]]:
        """
        Trích xuất hình ảnh từ HTML.
        
        Args:
            html_content: Nội dung HTML
            base_url: URL cơ sở
        
        Returns:
            Danh sách các thông tin hình ảnh
        """
        if not html_content:
            return []
        
        images = []
        
        # Sử dụng BeautifulSoup nếu có
        if hasattr(self, "bs4_available") and self.bs4_available:
            try:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(html_content, 'html.parser')
                
                # Tìm tất cả thẻ img
                for img in soup.find_all('img'):
                    src = img.get('src', '')
                    if not src:
                        continue
                    
                    # Tạo URL đầy đủ
                    full_url = urljoin(base_url, src)
                    
                    # Kiểm tra phần mở rộng
                    _, ext = os.path.splitext(urlparse(full_url).path)
                    ext = ext.lower()
                    
                    # Chỉ lấy các hình ảnh có phần mở rộng được hỗ trợ
                    if ext in self.config.get("supported_image_extensions", []):
                        # Lấy alt text và kích thước
                        alt = img.get('alt', '')
                        width = img.get('width', '')
                        height = img.get('height', '')
                        
                        # Chỉ lấy hình ảnh có kích thước đủ lớn
                        min_size = self.config.get("min_image_size", 100)
                        if (width and int(width) < min_size) or (height and int(height) < min_size):
                            continue
                        
                        images.append({
                            "url": full_url,
                            "alt": alt,
                            "width": width,
                            "height": height,
                            "source_url": base_url,
                            "type": "image"
                        })
            except Exception as e:
                logger.warning(f"Lỗi khi sử dụng BeautifulSoup để trích xuất hình ảnh: {str(e)}")
        
        # Nếu không có BeautifulSoup hoặc có lỗi, sử dụng regex
        if not images:
            # Tìm các thẻ img
            img_pattern = r'<img[^>]+src=["\'](.*?)["\'][^>]*>'
            for match in re.finditer(img_pattern, html_content):
                src = match.group(1)
                if not src:
                    continue
                
                # Tạo URL đầy đủ
                full_url = urljoin(base_url, src)
                
                # Kiểm tra phần mở rộng
                _, ext = os.path.splitext(urlparse(full_url).path)
                ext = ext.lower()
                
                # Chỉ lấy các hình ảnh có phần mở rộng được hỗ trợ
                if ext in self.config.get("supported_image_extensions", []):
                    # Tìm alt text
                    alt_match = re.search(r'alt=["\'](.*?)["\']', match.group(0))
                    alt = alt_match.group(1) if alt_match else ""
                    
                    images.append({
                        "url": full_url,
                        "alt": alt,
                        "source_url": base_url,
                        "type": "image"
                    })
        
        return images[:self.config.get("max_downloads_per_type", 5)]
    
    def _extract_videos(self, html_content: str, base_url: str) -> List[Dict[str, Any]]:
        """
        Trích xuất video từ HTML.
        
        Args:
            html_content: Nội dung HTML
            base_url: URL cơ sở
        
        Returns:
            Danh sách các thông tin video
        """
        if not html_content:
            return []
        
        videos = []
        
        # Sử dụng BeautifulSoup nếu có
        if hasattr(self, "bs4_available") and self.bs4_available:
            try:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(html_content, 'html.parser')
                
                # Tìm tất cả thẻ video
                for video in soup.find_all('video'):
                    # Tìm thẻ source hoặc sử dụng src trực tiếp
                    src = video.get('src', '')
                    
                    if not src:
                        source = video.find('source')
                        if source:
                            src = source.get('src', '')
                    
                    if not src:
                        continue
                    
                    # Tạo URL đầy đủ
                    full_url = urljoin(base_url, src)
                    
                    # Kiểm tra phần mở rộng
                    _, ext = os.path.splitext(urlparse(full_url).path)
                    ext = ext.lower()
                    
                    # Chỉ lấy các video có phần mở rộng được hỗ trợ
                    if ext in self.config.get("supported_video_extensions", []):
                        videos.append({
                            "url": full_url,
                            "source_url": base_url,
                            "type": "video"
                        })
                
                # Tìm iframe YouTube
                for iframe in soup.find_all('iframe'):
                    src = iframe.get('src', '')
                    if not src:
                        continue
                    
                    # Kiểm tra xem có phải là YouTube không
                    if 'youtube.com/embed/' in src or 'youtube-nocookie.com/embed/' in src:
                        videos.append({
                            "url": src,
                            "source_url": base_url,
                            "type": "video",
                            "platform": "youtube"
                        })
            except Exception as e:
                logger.warning(f"Lỗi khi sử dụng BeautifulSoup để trích xuất video: {str(e)}")
        
        # Nếu không có BeautifulSoup hoặc có lỗi, sử dụng regex
        if not videos:
            # Tìm các thẻ video
            video_pattern = r'<video[^>]*>.*?<source[^>]+src=["\'](.*?)["\'][^>]*>.*?</video>'
            for match in re.finditer(video_pattern, html_content):
                src = match.group(1)
                if not src:
                    continue
                
                # Tạo URL đầy đủ
                full_url = urljoin(base_url, src)
                
                # Kiểm tra phần mở rộng
                _, ext = os.path.splitext(urlparse(full_url).path)
                ext = ext.lower()
                
                # Chỉ lấy các video có phần mở rộng được hỗ trợ
                if ext in self.config.get("supported_video_extensions", []):
                    videos.append({
                        "url": full_url,
                        "source_url": base_url,
                        "type": "video"
                    })
            
            # Tìm các iframe YouTube
            youtube_pattern = r'<iframe[^>]+src=["\']((?:https?:)?//(?:www\.)?youtube(?:-nocookie)?\.com/embed/[^"\'][^>]*)'
            for match in re.finditer(youtube_pattern, html_content):
                src = match.group(1)
                if not src:
                    continue
                
                videos.append({
                    "url": src,
                    "source_url": base_url,
                    "type": "video",
                    "platform": "youtube"
                })
        
        return videos[:self.config.get("max_downloads_per_type", 5)]
    
    def _extract_audio(self, html_content: str, base_url: str) -> List[Dict[str, Any]]:
        """
        Trích xuất audio từ HTML.
        
        Args:
            html_content: Nội dung HTML
            base_url: URL cơ sở
        
        Returns:
            Danh sách các thông tin audio
        """
        if not html_content:
            return []
        
        audio_files = []
        
        # Sử dụng BeautifulSoup nếu có
        if hasattr(self, "bs4_available") and self.bs4_available:
            try:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(html_content, 'html.parser')
                
                # Tìm tất cả thẻ audio
                for audio in soup.find_all('audio'):
                    # Tìm thẻ source hoặc sử dụng src trực tiếp
                    src = audio.get('src', '')
                    
                    if not src:
                        source = audio.find('source')
                        if source:
                            src = source.get('src', '')
                    
                    if not src:
                        continue
                    
                    # Tạo URL đầy đủ
                    full_url = urljoin(base_url, src)
                    
                    # Kiểm tra phần mở rộng
                    _, ext = os.path.splitext(urlparse(full_url).path)
                    ext = ext.lower()
                    
                    # Chỉ lấy các audio có phần mở rộng được hỗ trợ
                    if ext in self.config.get("supported_audio_extensions", []):
                        audio_files.append({
                            "url": full_url,
                            "source_url": base_url,
                            "type": "audio"
                        })
                
                # Tìm các liên kết đến file audio
                for a in soup.find_all('a'):
                    href = a.get('href', '')
                    if not href:
                        continue
                    
                    # Tạo URL đầy đủ
                    full_url = urljoin(base_url, href)
                    
                    # Kiểm tra phần mở rộng
                    _, ext = os.path.splitext(urlparse(full_url).path)
                    ext = ext.lower()
                    
                    # Chỉ lấy các audio có phần mở rộng được hỗ trợ
                    if ext in self.config.get("supported_audio_extensions", []):
                        audio_files.append({
                            "url": full_url,
                            "text": a.get_text().strip(),
                            "source_url": base_url,
                            "type": "audio"
                        })
            except Exception as e:
                logger.warning(f"Lỗi khi sử dụng BeautifulSoup để trích xuất audio: {str(e)}")
        
        # Nếu không có BeautifulSoup hoặc có lỗi, sử dụng regex
        if not audio_files:
            # Tìm các thẻ audio
            audio_pattern = r'<audio[^>]*>.*?<source[^>]+src=["\'](.*?)["\'][^>]*>.*?</audio>'
            for match in re.finditer(audio_pattern, html_content):
                src = match.group(1)
                if not src:
                    continue
                
                # Tạo URL đầy đủ
                full_url = urljoin(base_url, src)
                
                # Kiểm tra phần mở rộng
                _, ext = os.path.splitext(urlparse(full_url).path)
                ext = ext.lower()
                
                # Chỉ lấy các audio có phần mở rộng được hỗ trợ
                if ext in self.config.get("supported_audio_extensions", []):
                    audio_files.append({
                        "url": full_url,
                        "source_url": base_url,
                        "type": "audio"
                    })
            
            # Tìm các liên kết đến file audio
            a_pattern = r'<a[^>]+href=["\'](.*?\.(?:mp3|wav|ogg|m4a|flac))["\'][^>]*>(.*?)</a>'
            for match in re.finditer(a_pattern, html_content):
                href = match.group(1)
                text = match.group(2)
                
                # Tạo URL đầy đủ
                full_url = urljoin(base_url, href)
                
                audio_files.append({
                    "url": full_url,
                    "text": re.sub(r'<[^>]+>', '', text).strip(),
                    "source_url": base_url,
                    "type": "audio"
                })
        
        return audio_files[:self.config.get("max_downloads_per_type", 5)]
    
    def _download_media(self, all_media: Dict[str, List[Dict[str, Any]]]) -> None:
        """
        Tải xuống các tài nguyên đa phương tiện.
        
        Args:
            all_media: Dictionary chứa thông tin các tài nguyên
        """
        if not self.requests_available:
            logger.warning("Không thể tải xuống vì thư viện requests không có sẵn")
            return
        
        import requests
        
        download_dir = self.config.get("download_dir", "downloads")
        max_downloads_per_type = self.config.get("max_downloads_per_type", 5)
        max_download_size = self.config.get("max_download_size", 10 * 1024 * 1024)  # 10MB
        user_agent = self.config.get("user_agent", "Mozilla/5.0")
        
        headers = {
            "User-Agent": user_agent
        }
        
        # Tải xuống hình ảnh
        for i, image in enumerate(all_media["images"][:max_downloads_per_type]):
            url = image["url"]
            
            try:
                # Tạo tên file
                parsed_url = urlparse(url)
                filename = os.path.basename(parsed_url.path)
                if not filename:
                    filename = f"image_{i}.jpg"
                
                # Đường dẫn đầy đủ
                filepath = os.path.join(download_dir, "images", filename)
                
                # Kiểm tra xem file đã tồn tại chưa
                if os.path.exists(filepath):
                    logger.debug(f"File đã tồn tại: {filepath}")
                    image["local_path"] = filepath
                    continue
                
                # Tải xuống file
                response = requests.get(url, headers=headers, stream=True)
                response.raise_for_status()
                
                # Kiểm tra kích thước
                content_length = int(response.headers.get("Content-Length", 0))
                if content_length > max_download_size:
                    logger.warning(f"File quá lớn: {url} ({content_length} bytes)")
                    continue
                
                # Lưu file
                with open(filepath, "wb") as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                
                logger.debug(f"Đã tải xuống: {filepath}")
                image["local_path"] = filepath
            
            except Exception as e:
                logger.warning(f"Lỗi khi tải xuống hình ảnh {url}: {str(e)}")
        
        # Tải xuống video (tương tự như hình ảnh)
        for i, video in enumerate(all_media["videos"][:max_downloads_per_type]):
            url = video["url"]
            
            # Bỏ qua video từ YouTube
            if video.get("platform") == "youtube":
                continue
            
            try:
                # Tạo tên file
                parsed_url = urlparse(url)
                filename = os.path.basename(parsed_url.path)
                if not filename:
                    filename = f"video_{i}.mp4"
                
                # Đường dẫn đầy đủ
                filepath = os.path.join(download_dir, "videos", filename)
                
                # Kiểm tra xem file đã tồn tại chưa
                if os.path.exists(filepath):
                    logger.debug(f"File đã tồn tại: {filepath}")
                    video["local_path"] = filepath
                    continue
                
                # Tải xuống file
                response = requests.get(url, headers=headers, stream=True)
                response.raise_for_status()
                
                # Kiểm tra kích thước
                content_length = int(response.headers.get("Content-Length", 0))
                if content_length > max_download_size:
                    logger.warning(f"File quá lớn: {url} ({content_length} bytes)")
                    continue
                
                # Lưu file
                with open(filepath, "wb") as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                
                logger.debug(f"Đã tải xuống: {filepath}")
                video["local_path"] = filepath
            
            except Exception as e:
                logger.warning(f"Lỗi khi tải xuống video {url}: {str(e)}")
        
        # Tải xuống audio
        for i, audio in enumerate(all_media["audio"][:max_downloads_per_type]):
            url = audio["url"]
            
            try:
                # Tạo tên file
                parsed_url = urlparse(url)
                filename = os.path.basename(parsed_url.path)
                if not filename:
                    filename = f"audio_{i}.mp3"
                
                # Đường dẫn đầy đủ
                filepath = os.path.join(download_dir, "audio", filename)
                
                # Kiểm tra xem file đã tồn tại chưa
                if os.path.exists(filepath):
                    logger.debug(f"File đã tồn tại: {filepath}")
                    audio["local_path"] = filepath
                    continue
                
                # Tải xuống file
                response = requests.get(url, headers=headers, stream=True)
                response.raise_for_status()
                
                # Kiểm tra kích thước
                content_length = int(response.headers.get("Content-Length", 0))
                if content_length > max_download_size:
                    logger.warning(f"File quá lớn: {url} ({content_length} bytes)")
                    continue
                
                # Lưu file
                with open(filepath, "wb") as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                
                logger.debug(f"Đã tải xuống: {filepath}")
                audio["local_path"] = filepath
            
            except Exception as e:
                logger.warning(f"Lỗi khi tải xuống audio {url}: {str(e)}") 