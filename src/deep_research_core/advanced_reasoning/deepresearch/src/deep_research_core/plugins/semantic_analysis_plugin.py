"""
Module cung cấp plugin phân tích ngữ nghĩa kết quả tìm kiếm.

Module này triển khai SemanticAnalysisPlugin để phân tích ngữ nghĩa kết quả tìm kiếm,
bao gồm đ<PERSON>h giá độ liên quan, trích xuất thực thể, và tóm tắt nội dung.
"""

import re
import time
import string
import math
from typing import Dict, Any, List, Optional, Set, Tuple
from collections import Counter

from .base_plugin import BasePlugin
from ..utils.plugin_manager import hook
from ..utils.structured_logging import get_logger

# Thiết lập logger
logger = get_logger(__name__)

class SemanticAnalysisPlugin(BasePlugin):
    """
    Plugin phân tích ngữ nghĩa kết quả tìm kiếm.
    
    Plugin này cung cấp các công cụ phân tích ngữ nghĩa cho kết quả tìm kiếm, bao gồm:
    - <PERSON><PERSON><PERSON> gi<PERSON> độ liên quan của kết quả tìm kiếm với truy vấn
    - Trích xuất thực thể từ kết quả tìm kiếm (người, tổ chức, địa điểm, v.v.)
    - Tóm tắt nội dung của kết quả tìm kiếm
    - Phân tích tâm điểm của nội dung
    - Tạo biểu diễn vector cho văn bản
    """
    
    VERSION = "1.0.0"
    AUTHOR = "Augment Code"
    DEPENDENCIES = []
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Khởi tạo SemanticAnalysisPlugin.
        
        Args:
            config: Cấu hình cho plugin
        """
        default_config = {
            "enabled": True,
            "relevance_threshold": 0.5,  # Ngưỡng độ liên quan (0-1)
            "enable_entity_extraction": True,  # Bật/tắt trích xuất thực thể
            "enable_summarization": True,  # Bật/tắt tóm tắt
            "enable_topic_analysis": True,  # Bật/tắt phân tích chủ đề
            "enable_vector_representation": True,  # Bật/tắt biểu diễn vector
            "max_entities": 10,  # Số lượng thực thể tối đa trích xuất
            "summary_max_length": 200,  # Độ dài tối đa của tóm tắt
            "summary_algorithm": "extractive",  # Thuật toán tóm tắt: "extractive" hoặc "abstractive"
            "use_external_nlp": False,  # Sử dụng dịch vụ NLP bên ngoài
            "external_nlp_api_key": "",  # API key cho dịch vụ NLP bên ngoài
            "external_nlp_api_url": "",  # URL cho dịch vụ NLP bên ngoài
            "language_specific_stopwords": {
                "vi": ["và", "hoặc", "nhưng", "vì", "nếu", "khi", "là", "có", "không", "được", "trong", "ngoài", "trên", "dưới", "của", "với", "cho", "bởi", "về", "theo", "từ", "đến", "tại", "cùng", "như", "để", "do", "bị", "được", "tại", "vào", "ra"],
                "en": ["and", "or", "but", "if", "when", "is", "are", "was", "were", "be", "been", "being", "have", "has", "had", "do", "does", "did", "will", "would", "shall", "should", "may", "might", "must", "can", "could", "a", "an", "the", "this", "that", "these", "those"]
            },
            "weight_title": 2.0,  # Trọng số cho tiêu đề
            "weight_snippet": 1.0,  # Trọng số cho đoạn trích
            "weight_content": 0.5,  # Trọng số cho nội dung
            "vectorization_method": "tf-idf"  # Phương pháp tạo vector: "tf-idf", "count", "binary"
        }
        
        # Kết hợp cấu hình mặc định và cấu hình người dùng
        merged_config = default_config.copy()
        if config:
            merged_config.update(config)
        
        super().__init__(merged_config)
        
        # Khởi tạo các thành phần NLP
        self._initialize_nlp_components()
        
        # Cache cho kết quả đã phân tích
        self.analysis_cache = {}
        self.entity_cache = {}
        self.summary_cache = {}
        self.vector_cache = {}
    
    def _initialize_nlp_components(self):
        """Khởi tạo các thành phần NLP cần thiết."""
        # Kiểm tra xem có sử dụng dịch vụ NLP bên ngoài không
        self.use_external_nlp = self.config.get("use_external_nlp", False)
        
        if self.use_external_nlp:
            # Kiểm tra xem có API key và URL không
            self.external_nlp_api_key = self.config.get("external_nlp_api_key", "")
            self.external_nlp_api_url = self.config.get("external_nlp_api_url", "")
            
            if not self.external_nlp_api_key or not self.external_nlp_api_url:
                logger.warning("API key hoặc URL NLP không được cung cấp. Sẽ sử dụng NLP cục bộ.")
                self.use_external_nlp = False
        
        # Nếu không sử dụng NLP bên ngoài, thử nhập các thư viện cần thiết
        if not self.use_external_nlp:
            # Thử nhập thư viện spaCy nếu có
            try:
                import spacy
                self.spacy_available = True
                # Thử tải mô hình spaCy
                try:
                    self.nlp = spacy.load("vi_core_news_sm")
                    logger.info("Đã tải mô hình spaCy tiếng Việt")
                except:
                    try:
                        self.nlp = spacy.load("en_core_web_sm")
                        logger.info("Đã tải mô hình spaCy tiếng Anh")
                    except:
                        logger.warning("Không thể tải mô hình spaCy. Sẽ sử dụng phương pháp đơn giản hơn.")
                        self.spacy_available = False
            except ImportError:
                logger.warning("spaCy không có sẵn. Sẽ sử dụng phương pháp đơn giản hơn.")
                self.spacy_available = False
    
    @hook(priority=80)
    def post_search(self, results: List[Dict[str, Any]], query: str, **kwargs) -> Dict[str, Any]:
        """
        Hook được gọi sau khi thực hiện tìm kiếm để phân tích ngữ nghĩa kết quả.
        
        Args:
            results: Danh sách kết quả tìm kiếm
            query: Truy vấn tìm kiếm gốc
            **kwargs: Các tham số bổ sung
        
        Returns:
            Dictionary chứa kết quả đã phân tích và thông tin bổ sung
        """
        if not self.is_enabled() or not results:
            return {"results": results}
        
        # Lấy ngôn ngữ từ tham số, mặc định là tiếng Việt
        language = kwargs.get("language", "vi")
        
        # Bắt đầu thời gian đo hiệu suất
        start_time = time.time()
        
        # Tạo cache key từ query và kết quả
        cache_key = f"{query}_{hash(str(results))}"
        
        # Nếu đã phân tích trước đó, trả về kết quả từ cache
        if cache_key in self.analysis_cache:
            logger.debug(f"Sử dụng kết quả phân tích đã lưu trong cache cho truy vấn: {query}")
            analysis_result = self.analysis_cache[cache_key]
            
            # Cập nhật thời gian (sử dụng cache nên nhanh hơn)
            analysis_result["performance"]["time_taken"] = time.time() - start_time
            
            return analysis_result
        
        # Phân tích ngữ nghĩa kết quả
        analyzed_results = []
        all_entities = {}
        
        for result in results:
            # Tính điểm độ liên quan
            relevance_score = self._calculate_relevance(result, query, language)
            result["semantic_relevance"] = relevance_score
            
            # Trích xuất thực thể nếu được cấu hình
            if self.config.get("enable_entity_extraction", True):
                entities = self._extract_entities(result, language)
                result["entities"] = entities
                
                # Tổng hợp thực thể từ tất cả kết quả
                for entity_type, entity_values in entities.items():
                    if entity_type not in all_entities:
                        all_entities[entity_type] = Counter()
                    all_entities[entity_type].update(entity_values)
            
            # Tóm tắt nếu được cấu hình
            if self.config.get("enable_summarization", True) and "content" in result:
                summary = self._summarize_content(result["content"], language)
                result["summary"] = summary
            
            # Phân tích chủ đề nếu được cấu hình
            if self.config.get("enable_topic_analysis", True) and "content" in result:
                topics = self._analyze_topics(result["content"], language)
                result["topics"] = topics
            
            # Tạo biểu diễn vector nếu được cấu hình
            if self.config.get("enable_vector_representation", True):
                vector = self._vectorize_text(result, query, language)
                result["vector_representation"] = vector
            
            analyzed_results.append(result)
        
        # Sắp xếp lại kết quả theo độ liên quan ngữ nghĩa
        analyzed_results.sort(key=lambda x: x.get("semantic_relevance", 0), reverse=True)
        
        # Kết thúc thời gian đo hiệu suất
        time_taken = time.time() - start_time
        
        # Tạo kết quả phân tích
        analysis_result = {
            "results": analyzed_results,
            "original_results": results,
            "all_entities": {entity_type: dict(counter.most_common(self.config.get("max_entities", 10))) 
                            for entity_type, counter in all_entities.items()},
            "performance": {
                "time_taken": time_taken,
                "processed_results": len(results)
            }
        }
        
        # Lưu vào cache
        self.analysis_cache[cache_key] = analysis_result
        
        return analysis_result
    
    def _calculate_relevance(self, result: Dict[str, Any], query: str, language: str) -> float:
        """
        Tính điểm độ liên quan ngữ nghĩa giữa kết quả và truy vấn.
        
        Args:
            result: Kết quả tìm kiếm
            query: Truy vấn tìm kiếm
            language: Ngôn ngữ
        
        Returns:
            Điểm độ liên quan (0-1)
        """
        # Trích xuất thông tin từ kết quả
        title = result.get("title", "")
        snippet = result.get("snippet", "")
        content = result.get("content", "")
        
        # Tiền xử lý query và các trường văn bản
        query_tokens = self._preprocess_text(query, language)
        title_tokens = self._preprocess_text(title, language)
        snippet_tokens = self._preprocess_text(snippet, language)
        content_tokens = self._preprocess_text(content, language)
        
        # Lấy trọng số từ cấu hình
        weight_title = self.config.get("weight_title", 2.0)
        weight_snippet = self.config.get("weight_snippet", 1.0)
        weight_content = self.config.get("weight_content", 0.5)
        
        # Tính điểm cơ bản dựa trên sự xuất hiện của các từ query trong các trường
        title_score = self._calculate_field_score(query_tokens, title_tokens)
        snippet_score = self._calculate_field_score(query_tokens, snippet_tokens)
        content_score = self._calculate_field_score(query_tokens, content_tokens)
        
        # Tính điểm tổng hợp
        total_score = (title_score * weight_title + 
                      snippet_score * weight_snippet + 
                      content_score * weight_content) / (weight_title + weight_snippet + weight_content)
        
        # Chuẩn hóa về khoảng 0-1
        normalized_score = min(1.0, max(0.0, total_score))
        
        return normalized_score
    
    def _calculate_field_score(self, query_tokens: List[str], field_tokens: List[str]) -> float:
        """
        Tính điểm cho một trường văn bản dựa trên sự xuất hiện của các từ truy vấn.
        
        Args:
            query_tokens: Danh sách từ trong truy vấn
            field_tokens: Danh sách từ trong trường văn bản
        
        Returns:
            Điểm của trường (0-1)
        """
        if not query_tokens or not field_tokens:
            return 0.0
        
        # Đếm số từ truy vấn xuất hiện trong trường
        matches = sum(1 for token in query_tokens if token in field_tokens)
        
        # Tính tỷ lệ từ truy vấn xuất hiện
        match_ratio = matches / len(query_tokens)
        
        # Tính điểm dựa trên TF-IDF
        field_counter = Counter(field_tokens)
        query_counter = Counter(query_tokens)
        
        # Tính điểm TF-IDF cho các từ truy vấn xuất hiện trong trường
        tfidf_score = 0.0
        for token in query_tokens:
            if token in field_tokens:
                # Term Frequency
                tf = field_counter[token] / len(field_tokens)
                # Inverse Document Frequency (đơn giản)
                idf = math.log(2.0)  # Giả định IDF cơ bản
                tfidf_score += tf * idf
        
        # Kết hợp match_ratio và tfidf_score
        combined_score = 0.7 * match_ratio + 0.3 * min(1.0, tfidf_score)
        
        return combined_score
    
    def _extract_entities(self, result: Dict[str, Any], language: str) -> Dict[str, List[str]]:
        """
        Trích xuất thực thể từ kết quả tìm kiếm.
        
        Args:
            result: Kết quả tìm kiếm
            language: Ngôn ngữ
        
        Returns:
            Dictionary chứa các loại thực thể và giá trị
        """
        # Trích xuất văn bản từ kết quả
        title = result.get("title", "")
        snippet = result.get("snippet", "")
        content = result.get("content", "")
        
        # Kết hợp văn bản để phân tích
        full_text = f"{title} {snippet} {content}"
        
        # Tạo cache key
        cache_key = hash(full_text)
        
        # Kiểm tra cache
        if cache_key in self.entity_cache:
            return self.entity_cache[cache_key]
        
        entities = {}
        
        # Nếu có sẵn spaCy, sử dụng nó để trích xuất thực thể
        if hasattr(self, 'spacy_available') and self.spacy_available and hasattr(self, 'nlp'):
            try:
                doc = self.nlp(full_text[:50000])  # Giới hạn độ dài để tránh quá tải
                
                # Nhóm theo loại thực thể
                for ent in doc.ents:
                    entity_type = ent.label_
                    entity_text = ent.text.strip()
                    
                    if entity_type not in entities:
                        entities[entity_type] = []
                    
                    if entity_text and entity_text not in entities[entity_type]:
                        entities[entity_type].append(entity_text)
                
                # Lưu vào cache
                self.entity_cache[cache_key] = entities
                return entities
            except Exception as e:
                logger.warning(f"Lỗi khi sử dụng spaCy để trích xuất thực thể: {str(e)}")
        
        # Nếu không có spaCy hoặc có lỗi, sử dụng phương pháp đơn giản
        # Tìm các mẫu cơ bản cho địa điểm, tổ chức, và người
        
        # Các mẫu đơn giản
        people_patterns = [
            r'[A-Z][a-z]+ [A-Z][a-z]+',  # Tên người (firstname lastname)
            r'ông [A-Z][a-z]+',  # Ông X
            r'bà [A-Z][a-z]+',  # Bà X
            r'anh [A-Z][a-z]+',  # Anh X
            r'chị [A-Z][a-z]+',  # Chị X
        ]
        
        org_patterns = [
            r'[A-Z][A-Za-z]+ (Inc|LLC|Corp|Corporation|Company|Ltd)',
            r'Công ty [A-Za-z0-9 ]+',
            r'Tập đoàn [A-Za-z0-9 ]+',
            r'Trường [A-Za-z0-9 ]+',
            r'Đại học [A-Za-z0-9 ]+'
        ]
        
        location_patterns = [
            r'tại [A-Z][a-z]+',
            r'ở [A-Z][a-z]+',
            r'tỉnh [A-Z][a-z]+',
            r'thành phố [A-Z][a-z]+',
            r'quận [0-9A-Za-z]+',
            r'huyện [A-Z][a-z]+'
        ]
        
        # Tìm thực thể với các mẫu
        entities = {
            "PERSON": [],
            "ORG": [],
            "LOC": []
        }
        
        # Tìm người
        for pattern in people_patterns:
            matches = re.findall(pattern, full_text)
            entities["PERSON"].extend([match.strip() for match in matches if match.strip()])
        
        # Tìm tổ chức
        for pattern in org_patterns:
            matches = re.findall(pattern, full_text)
            entities["ORG"].extend([match.strip() for match in matches if match.strip()])
        
        # Tìm địa điểm
        for pattern in location_patterns:
            matches = re.findall(pattern, full_text)
            entities["LOC"].extend([match.strip() for match in matches if match.strip()])
        
        # Loại bỏ trùng lặp
        for entity_type in entities:
            entities[entity_type] = list(set(entities[entity_type]))[:self.config.get("max_entities", 10)]
        
        # Lưu vào cache
        self.entity_cache[cache_key] = entities
        
        return entities
    
    def _summarize_content(self, content: str, language: str) -> str:
        """
        Tóm tắt nội dung văn bản.
        
        Args:
            content: Nội dung cần tóm tắt
            language: Ngôn ngữ
        
        Returns:
            Tóm tắt nội dung
        """
        if not content:
            return ""
        
        # Tạo cache key
        cache_key = hash(content)
        
        # Kiểm tra cache
        if cache_key in self.summary_cache:
            return self.summary_cache[cache_key]
        
        summary_algorithm = self.config.get("summary_algorithm", "extractive")
        max_length = self.config.get("summary_max_length", 200)
        
        if summary_algorithm == "extractive":
            # Chia văn bản thành các câu
            sentences = re.split(r'(?<=[.!?])\s+', content)
            
            if not sentences:
                return ""
            
            # Nếu chỉ có 1-2 câu, trả về luôn
            if len(sentences) <= 2:
                summary = " ".join(sentences)
                # Lưu vào cache
                self.summary_cache[cache_key] = summary[:max_length]
                return summary[:max_length]
            
            # Tiền xử lý các câu
            preprocessed_sentences = [self._preprocess_text(s, language) for s in sentences]
            
            # Tính điểm cho mỗi câu dựa trên tần suất từ và vị trí
            sentence_scores = []
            
            # Tạo từ điển tần suất
            word_freq = Counter()
            for sentence_tokens in preprocessed_sentences:
                word_freq.update(sentence_tokens)
            
            # Tính điểm cho mỗi câu
            for i, sentence_tokens in enumerate(preprocessed_sentences):
                score = 0
                
                # Tính điểm dựa trên tần suất từ
                for token in sentence_tokens:
                    score += word_freq[token]
                
                # Chuẩn hóa theo độ dài câu
                if len(sentence_tokens) > 0:
                    score = score / len(sentence_tokens)
                
                # Thêm điểm cho các câu ở đầu và cuối văn bản
                position_weight = 0
                if i < len(sentences) * 0.2:  # 20% đầu tiên
                    position_weight = 0.2
                elif i > len(sentences) * 0.8:  # 20% cuối cùng
                    position_weight = 0.1
                
                score = score * (1 + position_weight)
                
                sentence_scores.append((i, score))
            
            # Sắp xếp theo điểm
            sentence_scores.sort(key=lambda x: x[1], reverse=True)
            
            # Chọn các câu có điểm cao nhất
            num_sentences = min(3, len(sentences))
            selected_indices = [idx for idx, _ in sentence_scores[:num_sentences]]
            selected_indices.sort()  # Sắp xếp lại theo thứ tự xuất hiện
            
            # Tạo tóm tắt
            summary = " ".join([sentences[idx] for idx in selected_indices])
            
            # Cắt bớt nếu quá dài
            if len(summary) > max_length:
                summary = summary[:max_length-3] + "..."
            
            # Lưu vào cache
            self.summary_cache[cache_key] = summary
            
            return summary
        
        elif summary_algorithm == "abstractive":
            # Nếu muốn sử dụng tóm tắt trừu tượng, cần có các mô hình nâng cao
            # Ở đây chúng ta sẽ sử dụng một phương pháp đơn giản
            
            # Trả về 1-2 câu đầu tiên
            sentences = re.split(r'(?<=[.!?])\s+', content)
            summary = " ".join(sentences[:2])
            
            # Cắt bớt nếu quá dài
            if len(summary) > max_length:
                summary = summary[:max_length-3] + "..."
            
            # Lưu vào cache
            self.summary_cache[cache_key] = summary
            
            return summary
        
        else:
            # Mặc định trả về 1-2 câu đầu tiên
            sentences = re.split(r'(?<=[.!?])\s+', content)
            summary = " ".join(sentences[:2])
            
            # Cắt bớt nếu quá dài
            if len(summary) > max_length:
                summary = summary[:max_length-3] + "..."
            
            # Lưu vào cache
            self.summary_cache[cache_key] = summary
            
            return summary
    
    def _analyze_topics(self, content: str, language: str) -> List[str]:
        """
        Phân tích chủ đề của nội dung.
        
        Args:
            content: Nội dung cần phân tích
            language: Ngôn ngữ
        
        Returns:
            Danh sách các chủ đề
        """
        if not content:
            return []
        
        # Tiền xử lý nội dung
        tokens = self._preprocess_text(content, language)
        
        # Đếm tần suất từ
        word_freq = Counter(tokens)
        
        # Loại bỏ các từ dừng
        stopwords = self.config.get("language_specific_stopwords", {}).get(language, [])
        for word in stopwords:
            if word in word_freq:
                del word_freq[word]
        
        # Lấy các từ phổ biến nhất là chủ đề
        topics = [word for word, _ in word_freq.most_common(5)]
        
        return topics
    
    def _vectorize_text(self, result: Dict[str, Any], query: str, language: str) -> Dict[str, Any]:
        """
        Tạo biểu diễn vector cho văn bản.
        
        Args:
            result: Kết quả tìm kiếm
            query: Truy vấn tìm kiếm
            language: Ngôn ngữ
        
        Returns:
            Dictionary chứa biểu diễn vector
        """
        # Trích xuất văn bản từ kết quả
        title = result.get("title", "")
        snippet = result.get("snippet", "")
        content = result.get("content", "")
        
        # Kết hợp văn bản để phân tích
        full_text = f"{title} {snippet} {content}"
        
        # Tạo cache key
        cache_key = f"{hash(full_text)}_{hash(query)}"
        
        # Kiểm tra cache
        if cache_key in self.vector_cache:
            return self.vector_cache[cache_key]
        
        # Tiền xử lý văn bản
        tokens = self._preprocess_text(full_text, language)
        query_tokens = self._preprocess_text(query, language)
        
        # Phương pháp tạo vector
        vectorization_method = self.config.get("vectorization_method", "tf-idf")
        
        if vectorization_method == "tf-idf":
            # Tính TF-IDF
            vector = {}
            word_freq = Counter(tokens)
            
            for token in set(tokens):
                # Term Frequency
                tf = word_freq[token] / len(tokens)
                # Inverse Document Frequency (đơn giản)
                idf = 1.0
                if token in query_tokens:
                    idf = 2.0  # Tăng trọng số cho từ trong query
                
                vector[token] = tf * idf
            
            result_vector = {
                "method": "tf-idf",
                "dimensions": len(vector),
                "sparse_vector": vector,
                "query_similarity": self._calculate_cosine_similarity(vector, query_tokens)
            }
            
            # Lưu vào cache
            self.vector_cache[cache_key] = result_vector
            
            return result_vector
        
        elif vectorization_method == "count":
            # Đếm số lần xuất hiện
            vector = dict(Counter(tokens))
            
            result_vector = {
                "method": "count",
                "dimensions": len(vector),
                "sparse_vector": vector,
                "query_similarity": self._calculate_cosine_similarity(vector, query_tokens)
            }
            
            # Lưu vào cache
            self.vector_cache[cache_key] = result_vector
            
            return result_vector
        
        elif vectorization_method == "binary":
            # Vector nhị phân (1 nếu từ xuất hiện, 0 nếu không)
            vector = {token: 1 for token in set(tokens)}
            
            result_vector = {
                "method": "binary",
                "dimensions": len(vector),
                "sparse_vector": vector,
                "query_similarity": self._calculate_cosine_similarity(vector, query_tokens)
            }
            
            # Lưu vào cache
            self.vector_cache[cache_key] = result_vector
            
            return result_vector
        
        else:
            # Mặc định là TF-IDF
            vector = {}
            word_freq = Counter(tokens)
            
            for token in set(tokens):
                tf = word_freq[token] / len(tokens)
                idf = 1.0
                if token in query_tokens:
                    idf = 2.0
                
                vector[token] = tf * idf
            
            result_vector = {
                "method": "tf-idf",
                "dimensions": len(vector),
                "sparse_vector": vector,
                "query_similarity": self._calculate_cosine_similarity(vector, query_tokens)
            }
            
            # Lưu vào cache
            self.vector_cache[cache_key] = result_vector
            
            return result_vector
    
    def _calculate_cosine_similarity(self, vector: Dict[str, float], query_tokens: List[str]) -> float:
        """
        Tính độ tương đồng cosine giữa vector và query.
        
        Args:
            vector: Vector của văn bản
            query_tokens: Danh sách từ trong query
        
        Returns:
            Độ tương đồng cosine
        """
        if not vector or not query_tokens:
            return 0.0
        
        # Tạo vector query
        query_vector = {}
        for token in query_tokens:
            query_vector[token] = query_vector.get(token, 0) + 1
        
        # Tính tử số (dot product)
        dot_product = 0.0
        for token, value in query_vector.items():
            if token in vector:
                dot_product += value * vector[token]
        
        # Tính mẫu số (product of magnitudes)
        magnitude_vector = math.sqrt(sum(value * value for value in vector.values()))
        magnitude_query = math.sqrt(sum(value * value for value in query_vector.values()))
        
        # Tránh chia cho 0
        if magnitude_vector == 0 or magnitude_query == 0:
            return 0.0
        
        # Tính cosine similarity
        cosine_similarity = dot_product / (magnitude_vector * magnitude_query)
        
        return cosine_similarity
    
    def _preprocess_text(self, text: str, language: str) -> List[str]:
        """
        Tiền xử lý văn bản.
        
        Args:
            text: Văn bản cần tiền xử lý
            language: Ngôn ngữ
        
        Returns:
            Danh sách các token
        """
        if not text:
            return []
        
        # Chuyển về chữ thường
        text = text.lower()
        
        # Loại bỏ dấu câu
        text = text.translate(str.maketrans("", "", string.punctuation))
        
        # Tách từ
        tokens = text.split()
        
        # Loại bỏ các từ dừng
        stopwords = self.config.get("language_specific_stopwords", {}).get(language, [])
        tokens = [token for token in tokens if token not in stopwords]
        
        return tokens 