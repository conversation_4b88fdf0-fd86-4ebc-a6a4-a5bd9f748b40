"""
Module cung cấp plugin xử lý CAPTCHA.

Module này cung cấp CaptchaHandlerPlugin để xử lý CAPTCHA trong quá trình tìm kiếm.
"""

import re
import time
from typing import Dict, Any, List, Optional, Set, Tuple

from .base_plugin import BasePlugin
from ..utils.plugin_manager import hook
from ..utils.structured_logging import get_logger

logger = get_logger(__name__)


class CaptchaHandlerPlugin(BasePlugin):
    """
    Plugin xử lý CAPTCHA.

    Plugin này cung cấp các phương thức để phát hiện và xử lý CAPTCHA
    trong quá trình tìm kiếm và trích xuất nội dung.
    """

    VERSION = "1.0.0"
    AUTHOR = "Augment Code"
    DEPENDENCIES = []

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Khởi tạo CaptchaHandlerPlugin.

        Args:
            config: <PERSON><PERSON><PERSON> hình cho plugin
        """
        default_config = {
            "enabled": True,
            "detection_patterns": [
                "captcha",
                "recaptcha",
                "hcaptcha",
                "cloudflare",
                "are you a robot",
                "are you human",
                "verify you are human",
                "security check",
                "challenge",
                "please wait",
                "access denied",
                "blocked",
                "ddos protection",
                "bot protection",
                "human verification",
                "prove you're not a robot",
                "prove you are human",
                "complete the security check",
                "complete this security check",
                "please complete the security check",
                "please solve this captcha",
                "please solve this puzzle",
                "please solve this challenge",
                "please complete this challenge",
                "please complete this puzzle",
                "please complete this captcha",
                "please verify you are human",
                "please verify you're not a robot",
                "please verify you are not a robot",
                "please verify you're human",
                "please prove you are human",
                "please prove you're not a robot",
                "please prove you are not a robot",
                "please prove you're human",
            ],
            "detection_elements": [
                "iframe[src*='captcha']",
                "iframe[src*='recaptcha']",
                "iframe[src*='hcaptcha']",
                "iframe[src*='cloudflare']",
                "div[class*='captcha']",
                "div[class*='recaptcha']",
                "div[class*='hcaptcha']",
                "div[class*='cloudflare']",
                "div[id*='captcha']",
                "div[id*='recaptcha']",
                "div[id*='hcaptcha']",
                "div[id*='cloudflare']",
                "form[action*='captcha']",
                "form[action*='recaptcha']",
                "form[action*='hcaptcha']",
                "form[action*='cloudflare']",
                "input[name*='captcha']",
                "input[name*='recaptcha']",
                "input[name*='hcaptcha']",
                "input[name*='cloudflare']",
                "button[class*='captcha']",
                "button[class*='recaptcha']",
                "button[class*='hcaptcha']",
                "button[class*='cloudflare']",
                "button[id*='captcha']",
                "button[id*='recaptcha']",
                "button[id*='hcaptcha']",
                "button[id*='cloudflare']",
            ],
            "max_retry_attempts": 3,
            "retry_delay": 5,  # seconds
            "use_playwright": True,
            "use_external_solver": False,
            "external_solver_api_key": "",
            "external_solver_api_url": "",
            "external_solver_timeout": 60,  # seconds
            "captcha_domains": [
                "google.com",
                "cloudflare.com",
                "hcaptcha.com",
                "recaptcha.net",
                "captcha.com",
            ],
            "blocked_domains": [],
            "cooldown_period": 300,  # seconds
            "cooldown_domains": {},
            "user_agent_rotation": True,
            "proxy_rotation": False,
            "proxy_list": [],
            "cookie_management": True,
            "cookie_file": "captcha_cookies.json",
            "headless": True,
            "browser_args": [
                "--disable-blink-features=AutomationControlled",
                "--disable-features=IsolateOrigins,site-per-process",
                "--disable-site-isolation-trials",
                "--disable-web-security",
                "--disable-setuid-sandbox",
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-accelerated-2d-canvas",
                "--disable-gpu",
                "--window-size=1920,1080",
                "--start-maximized",
            ],
            "browser_extensions": [],
            "browser_profiles": [],
            "browser_profile_rotation": False,
            "browser_profile_dir": "",
            "browser_profile_name": "",
            "browser_profile_password": "",
            "browser_profile_username": "",
            "browser_profile_email": "",
            "browser_profile_phone": "",
            "browser_profile_country": "",
            "browser_profile_language": "",
            "browser_profile_timezone": "",
            "browser_profile_geolocation": "",
            "browser_profile_useragent": "",
            "browser_profile_resolution": "",
            "browser_profile_platform": "",
            "browser_profile_os": "",
            "browser_profile_browser": "",
            "browser_profile_device": "",
            "browser_profile_mobile": False,
            "browser_profile_tablet": False,
            "browser_profile_desktop": True,
            "browser_profile_touch": False,
            "browser_profile_touch_points": 0,
            "browser_profile_webgl": True,
            "browser_profile_webgl_vendor": "",
            "browser_profile_webgl_renderer": "",
            "browser_profile_canvas": True,
            "browser_profile_canvas_noise": 0.0,
            "browser_profile_audio": True,
            "browser_profile_audio_noise": 0.0,
            "browser_profile_webrtc": True,
            "browser_profile_webrtc_public_ip": "",
            "browser_profile_webrtc_local_ip": "",
            "browser_profile_fonts": [],
            "browser_profile_plugins": [],
            "browser_profile_mime_types": [],
            "browser_profile_languages": [],
            "browser_profile_timezone_offset": 0,
            "browser_profile_navigator_platform": "",
            "browser_profile_navigator_vendor": "",
            "browser_profile_navigator_language": "",
            "browser_profile_navigator_languages": [],
            "browser_profile_navigator_user_agent": "",
            "browser_profile_navigator_hardware_concurrency": 0,
            "browser_profile_navigator_device_memory": 0,
            "browser_profile_navigator_max_touch_points": 0,
            "browser_profile_navigator_connection_type": "",
            "browser_profile_navigator_connection_downlink": 0,
            "browser_profile_navigator_connection_rtt": 0,
            "browser_profile_navigator_connection_downlink_max": 0,
            "browser_profile_navigator_connection_effective_type": "",
            "browser_profile_navigator_connection_save_data": False,
            "browser_profile_navigator_plugins": [],
            "browser_profile_navigator_mime_types": [],
            "browser_profile_navigator_do_not_track": "",
            "browser_profile_navigator_cookie_enabled": True,
            "browser_profile_navigator_app_code_name": "",
            "browser_profile_navigator_app_name": "",
            "browser_profile_navigator_app_version": "",
            "browser_profile_navigator_product": "",
            "browser_profile_navigator_product_sub": "",
            "browser_profile_navigator_vendor_sub": "",
            "browser_profile_navigator_build_id": "",
            "browser_profile_navigator_online": True,
            "browser_profile_navigator_pdf_viewer_enabled": True,
            "browser_profile_navigator_java_enabled": False,
            "browser_profile_navigator_gamepad_events": False,
            "browser_profile_navigator_vr_displays": False,
            "browser_profile_navigator_vr_enabled": False,
            "browser_profile_navigator_vr_displays_connected": False,
            "browser_profile_navigator_vr_displays_presenting": False,
            "browser_profile_navigator_xr_enabled": False,
            "browser_profile_navigator_xr_session_supported": False,
            "browser_profile_navigator_xr_session_device": "",
            "browser_profile_navigator_xr_session_mode": "",
            "browser_profile_navigator_xr_session_features": [],
            "browser_profile_navigator_xr_session_frame_rate": 0,
            "browser_profile_navigator_xr_session_reference_space": "",
            "browser_profile_navigator_xr_session_reference_space_type": "",
            "browser_profile_navigator_xr_session_reference_space_bounds": [],
            "browser_profile_navigator_xr_session_reference_space_origin": [],
            "browser_profile_navigator_xr_session_reference_space_orientation": [],
            "browser_profile_navigator_xr_session_reference_space_position": [],
            "browser_profile_navigator_xr_session_reference_space_transform": [],
            "browser_profile_navigator_xr_session_reference_space_views": [],
            "browser_profile_navigator_xr_session_reference_space_view_count": 0,
            "browser_profile_navigator_xr_session_reference_space_view_offset": [],
            "browser_profile_navigator_xr_session_reference_space_view_orientation": [],
            "browser_profile_navigator_xr_session_reference_space_view_position": [],
            "browser_profile_navigator_xr_session_reference_space_view_transform": [],
            "browser_profile_navigator_xr_session_reference_space_view_projection": [],
            "browser_profile_navigator_xr_session_reference_space_view_matrix": [],
            "browser_profile_navigator_xr_session_reference_space_view_viewport": [],
            "browser_profile_navigator_xr_session_reference_space_view_fov": [],
            "browser_profile_navigator_xr_session_reference_space_view_aspect": [],
            "browser_profile_navigator_xr_session_reference_space_view_near": 0,
            "browser_profile_navigator_xr_session_reference_space_view_far": 0,
            "browser_profile_navigator_xr_session_reference_space_view_eye": [],
            "browser_profile_navigator_xr_session_reference_space_view_eye_offset": [],
            "browser_profile_navigator_xr_session_reference_space_view_eye_orientation": [],
            "browser_profile_navigator_xr_session_reference_space_view_eye_position": [],
            "browser_profile_navigator_xr_session_reference_space_view_eye_transform": [],
            "browser_profile_navigator_xr_session_reference_space_view_eye_projection": [],
            "browser_profile_navigator_xr_session_reference_space_view_eye_matrix": [],
            "browser_profile_navigator_xr_session_reference_space_view_eye_viewport": [],
            "browser_profile_navigator_xr_session_reference_space_view_eye_fov": [],
            "browser_profile_navigator_xr_session_reference_space_view_eye_aspect": [],
            "browser_profile_navigator_xr_session_reference_space_view_eye_near": 0,
            "browser_profile_navigator_xr_session_reference_space_view_eye_far": 0,
        }
        
        # Kết hợp cấu hình mặc định và cấu hình người dùng
        merged_config = default_config.copy()
        if config:
            merged_config.update(config)
        
        super().__init__(merged_config)
        
        # Khởi tạo các thành phần cần thiết
        self._initialize_components()

    def _initialize_components(self) -> None:
        """Khởi tạo các thành phần cần thiết cho plugin."""
        # Biên dịch các biểu thức chính quy cho các mẫu phát hiện CAPTCHA
        self.detection_patterns = self.config.get("detection_patterns", [])
        self.detection_pattern_regex = re.compile(
            "|".join(rf"\b{pattern}\b" for pattern in self.detection_patterns),
            re.IGNORECASE
        )
        
        # Khởi tạo Playwright nếu được cấu hình
        self.use_playwright = self.config.get("use_playwright", True)
        self.playwright_browser = None
        self.playwright_context = None
        
        if self.use_playwright:
            self._initialize_playwright()
        
        # Khởi tạo các biến theo dõi
        self.captcha_encounters = {}
        self.blocked_domains = set(self.config.get("blocked_domains", []))
        self.cooldown_domains = self.config.get("cooldown_domains", {})

    def _initialize_playwright(self) -> None:
        """Khởi tạo Playwright."""
        try:
            from playwright.sync_api import sync_playwright
            
            self.playwright = sync_playwright().start()
            browser_args = self.config.get("browser_args", [])
            
            # Khởi tạo trình duyệt
            self.playwright_browser = self.playwright.chromium.launch(
                headless=self.config.get("headless", True),
                args=browser_args
            )
            
            # Khởi tạo context
            self.playwright_context = self.playwright_browser.new_context(
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            )
            
            logger.info("Đã khởi tạo Playwright thành công")
        except ImportError:
            logger.warning("Không thể nhập Playwright. Vui lòng cài đặt: pip install playwright")
            logger.warning("Sau đó chạy: playwright install")
            self.use_playwright = False
        except Exception as e:
            logger.warning(f"Lỗi khi khởi tạo Playwright: {str(e)}")
            self.use_playwright = False

    def cleanup(self) -> None:
        """Dọn dẹp tài nguyên khi plugin bị hủy."""
        if self.use_playwright and self.playwright_browser:
            try:
                self.playwright_browser.close()
                self.playwright.stop()
                logger.info("Đã dọn dẹp Playwright")
            except Exception as e:
                logger.warning(f"Lỗi khi dọn dẹp Playwright: {str(e)}")

    @hook(priority=30)
    def pre_search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Hook được gọi trước khi thực hiện tìm kiếm.

        Args:
            query: Truy vấn tìm kiếm
            **kwargs: Các tham số khác

        Returns:
            Dictionary chứa truy vấn và các thông tin khác
        """
        if not self.is_enabled():
            return {"query": query}
        
        # Kiểm tra xem có domain nào đang trong thời gian cooldown không
        current_time = time.time()
        for domain, cooldown_until in list(self.cooldown_domains.items()):
            if current_time > cooldown_until:
                # Hết thời gian cooldown
                del self.cooldown_domains[domain]
                logger.info(f"Domain {domain} đã hết thời gian cooldown")
        
        return {"query": query}

    @hook(priority=40)
    def pre_extract_content(self, url: str, **kwargs) -> Dict[str, Any]:
        """
        Hook được gọi trước khi trích xuất nội dung.

        Args:
            url: URL cần trích xuất
            **kwargs: Các tham số khác

        Returns:
            Dictionary chứa URL và các thông tin khác
        """
        if not self.is_enabled():
            return {"url": url}
        
        # Trích xuất domain từ URL
        domain = self._extract_domain(url)
        
        # Kiểm tra xem domain có bị chặn không
        if domain in self.blocked_domains:
            logger.warning(f"Domain {domain} bị chặn do CAPTCHA")
            return {"url": url, "skip": True, "reason": "domain_blocked"}
        
        # Kiểm tra xem domain có đang trong thời gian cooldown không
        current_time = time.time()
        if domain in self.cooldown_domains and current_time < self.cooldown_domains[domain]:
            cooldown_remaining = int(self.cooldown_domains[domain] - current_time)
            logger.warning(f"Domain {domain} đang trong thời gian cooldown ({cooldown_remaining}s)")
            return {"url": url, "skip": True, "reason": "domain_cooldown"}
        
        return {"url": url}

    @hook(priority=60)
    def post_extract_content(self, url: str, content: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Hook được gọi sau khi trích xuất nội dung.

        Args:
            url: URL đã trích xuất
            content: Nội dung đã trích xuất
            **kwargs: Các tham số khác

        Returns:
            Dictionary chứa nội dung đã xử lý
        """
        if not self.is_enabled():
            return content
        
        # Kiểm tra xem nội dung có chứa CAPTCHA không
        if self._detect_captcha(content):
            # Trích xuất domain từ URL
            domain = self._extract_domain(url)
            
            # Tăng số lần gặp CAPTCHA cho domain này
            self.captcha_encounters[domain] = self.captcha_encounters.get(domain, 0) + 1
            
            # Nếu số lần gặp CAPTCHA vượt quá ngưỡng, thêm domain vào danh sách chặn
            max_retry_attempts = self.config.get("max_retry_attempts", 3)
            if self.captcha_encounters[domain] > max_retry_attempts:
                self.blocked_domains.add(domain)
                logger.warning(f"Domain {domain} đã bị chặn do vượt quá số lần thử lại CAPTCHA")
            
            # Thêm domain vào danh sách cooldown
            cooldown_period = self.config.get("cooldown_period", 300)
            self.cooldown_domains[domain] = time.time() + cooldown_period
            
            # Thử giải CAPTCHA nếu được cấu hình
            if self.use_playwright:
                solved_content = self._solve_captcha(url, content)
                if solved_content:
                    return solved_content
            
            # Nếu không thể giải CAPTCHA, đánh dấu nội dung có CAPTCHA
            content["has_captcha"] = True
            content["captcha_solved"] = False
            
            logger.warning(f"Phát hiện CAPTCHA tại {url}")
        
        return content

    def _detect_captcha(self, content: Dict[str, Any]) -> bool:
        """
        Phát hiện CAPTCHA trong nội dung.

        Args:
            content: Nội dung cần kiểm tra

        Returns:
            True nếu phát hiện CAPTCHA, False nếu không
        """
        # Kiểm tra nội dung HTML
        html_content = content.get("html", "")
        if html_content and self.detection_pattern_regex.search(html_content):
            return True
        
        # Kiểm tra nội dung văn bản
        text_content = content.get("text", "")
        if text_content and self.detection_pattern_regex.search(text_content):
            return True
        
        # Kiểm tra tiêu đề
        title = content.get("title", "")
        if title and self.detection_pattern_regex.search(title):
            return True
        
        # Kiểm tra URL
        url = content.get("url", "")
        if url and ("captcha" in url.lower() or "challenge" in url.lower() or "security-check" in url.lower()):
            return True
        
        # Kiểm tra mã trạng thái HTTP
        status_code = content.get("status_code", 200)
        if status_code in [403, 429, 503]:
            return True
        
        return False

    def _solve_captcha(self, url: str, content: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Thử giải CAPTCHA.

        Args:
            url: URL của trang có CAPTCHA
            content: Nội dung trang có CAPTCHA

        Returns:
            Nội dung đã giải CAPTCHA hoặc None nếu không thể giải
        """
        if not self.use_playwright or not self.playwright_browser:
            return None
        
        try:
            # Tạo trang mới
            page = self.playwright_context.new_page()
            
            # Điều hướng đến URL
            page.goto(url, wait_until="domcontentloaded", timeout=30000)
            
            # Chờ trang tải xong
            page.wait_for_load_state("networkidle", timeout=30000)
            
            # Kiểm tra xem có CAPTCHA không
            if not self._has_captcha_elements(page):
                # Không có CAPTCHA, trả về nội dung
                new_content = content.copy()
                new_content["html"] = page.content()
                new_content["text"] = page.inner_text("body")
                new_content["title"] = page.title()
                new_content["has_captcha"] = False
                new_content["captcha_solved"] = True
                
                # Đóng trang
                page.close()
                
                return new_content
            
            # Có CAPTCHA, thử giải
            if self.config.get("use_external_solver", False):
                # Sử dụng solver bên ngoài
                solved = self._solve_with_external_solver(page)
            else:
                # Sử dụng phương pháp đơn giản
                solved = self._solve_with_simple_method(page)
            
            if solved:
                # Chờ trang tải lại sau khi giải CAPTCHA
                page.wait_for_load_state("networkidle", timeout=30000)
                
                # Kiểm tra lại xem còn CAPTCHA không
                if not self._has_captcha_elements(page):
                    # Không còn CAPTCHA, trả về nội dung
                    new_content = content.copy()
                    new_content["html"] = page.content()
                    new_content["text"] = page.inner_text("body")
                    new_content["title"] = page.title()
                    new_content["has_captcha"] = False
                    new_content["captcha_solved"] = True
                    
                    # Đóng trang
                    page.close()
                    
                    return new_content
            
            # Đóng trang
            page.close()
            
            return None
        except Exception as e:
            logger.warning(f"Lỗi khi giải CAPTCHA: {str(e)}")
            return None

    def _has_captcha_elements(self, page) -> bool:
        """
        Kiểm tra xem trang có chứa các phần tử CAPTCHA không.

        Args:
            page: Trang cần kiểm tra

        Returns:
            True nếu trang có chứa phần tử CAPTCHA, False nếu không
        """
        # Kiểm tra các phần tử CAPTCHA
        detection_elements = self.config.get("detection_elements", [])
        for selector in detection_elements:
            try:
                if page.query_selector(selector):
                    return True
            except:
                pass
        
        # Kiểm tra nội dung trang
        try:
            page_content = page.content().lower()
            for pattern in self.detection_patterns:
                if pattern.lower() in page_content:
                    return True
        except:
            pass
        
        return False

    def _solve_with_external_solver(self, page) -> bool:
        """
        Giải CAPTCHA bằng solver bên ngoài.

        Args:
            page: Trang cần giải CAPTCHA

        Returns:
            True nếu giải thành công, False nếu không
        """
        # Triển khai phương thức giải CAPTCHA bằng solver bên ngoài
        # Đây là phiên bản đơn giản, có thể cải thiện trong tương lai
        return False

    def _solve_with_simple_method(self, page) -> bool:
        """
        Giải CAPTCHA bằng phương pháp đơn giản.

        Args:
            page: Trang cần giải CAPTCHA

        Returns:
            True nếu giải thành công, False nếu không
        """
        # Triển khai phương thức giải CAPTCHA bằng phương pháp đơn giản
        # Đây là phiên bản đơn giản, có thể cải thiện trong tương lai
        
        # Thử nhấp vào nút "I'm not a robot"
        try:
            checkbox = page.query_selector("div.recaptcha-checkbox-border")
            if checkbox:
                checkbox.click()
                page.wait_for_timeout(2000)
                return True
        except:
            pass
        
        # Thử nhấp vào nút "Verify"
        try:
            verify_button = page.query_selector("button:has-text('Verify')")
            if verify_button:
                verify_button.click()
                page.wait_for_timeout(2000)
                return True
        except:
            pass
        
        # Thử nhấp vào nút "Continue"
        try:
            continue_button = page.query_selector("button:has-text('Continue')")
            if continue_button:
                continue_button.click()
                page.wait_for_timeout(2000)
                return True
        except:
            pass
        
        return False

    def _extract_domain(self, url: str) -> str:
        """
        Trích xuất domain từ URL.

        Args:
            url: URL

        Returns:
            Domain
        """
        # Triển khai phương thức trích xuất domain
        # Đây là phiên bản đơn giản, có thể cải thiện trong tương lai
        
        # Mặc định domain
        domain = ""
        
        # Trích xuất domain từ URL
        if url:
            # Loại bỏ protocol
            if "://" in url:
                domain = url.split("://")[1]
            else:
                domain = url
            
            # Lấy phần domain
            if "/" in domain:
                domain = domain.split("/")[0]
            
            # Loại bỏ www.
            if domain.startswith("www."):
                domain = domain[4:]
        
        return domain
