"""
Module cung cấp plugin phân tích tâm lý người dùng.

Module này triển khai UserPsychologyPlugin để phân tích tâm lý, nhu cầu,
sở thích và hành vi tìm kiếm của người dùng.
"""

import re
import time
import json
import os
from typing import Dict, Any, List, Optional, Set, Tuple
from collections import Counter, defaultdict

from .base_plugin import BasePlugin
from ..utils.plugin_manager import hook
from ..utils.structured_logging import get_logger

# Thiết lập logger
logger = get_logger(__name__)

class UserPsychologyPlugin(BasePlugin):
    """
    Plugin phân tích tâm lý người dùng.
    
    Plugin này cung cấp các khả năng phân tích tâm lý người dùng:
    - Phân tích mục đích tìm kiếm (thông tin, mua sắm, gi<PERSON><PERSON> trí, học tập...)
    - <PERSON>ân tích cấp độ chuyên môn (ng<PERSON><PERSON><PERSON> mới, trung cấp, chuyên gia)
    - <PERSON>ân tích ngữ cảnh tìm kiếm (cá nhân, công việc, nghiên cứu)
    - Phân tích sở thích và xu hướng
    - Đề xuất kết quả dựa trên phân tích tâm lý
    """
    
    VERSION = "1.0.0"
    AUTHOR = "Augment Code"
    DEPENDENCIES = []
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Khởi tạo UserPsychologyPlugin.
        
        Args:
            config: Cấu hình cho plugin
        """
        default_config = {
            "enabled": True,
            "data_file": "user_psychology_data.json",
            "auto_save": True,
            "save_interval": 300,  # 5 phút
            "enable_learning": True,
            "anonymize_data": True,
            "min_query_length": 3,
            "user_patterns": {
                "shopping": ["mua", "giá", "bán", "sale", "khuyến mãi", "shop", "store", "review"],
                "information": ["là gì", "thông tin", "định nghĩa", "khái niệm", "lý thuyết", "giải thích"],
                "academic": ["nghiên cứu", "luận văn", "khoa học", "academic", "paper", "journal", "thesis"],
                "entertainment": ["phim", "nhạc", "game", "chơi", "giải trí", "xem", "video"],
                "technical": ["code", "lập trình", "error", "bug", "syntax", "function", "framework", "library"],
                "health": ["bệnh", "khám", "thuốc", "triệu chứng", "điều trị", "sức khỏe", "bác sĩ"]
            },
            "expertise_patterns": {
                "beginner": ["cơ bản", "cho người mới", "nhập môn", "bắt đầu", "beginner", "intro"],
                "intermediate": ["nâng cao", "cải thiện", "tối ưu", "intermediate", "improve"],
                "expert": ["chuyên sâu", "chuyên gia", "expert", "advanced", "professional", "deep dive"]
            }
        }
        
        # Kết hợp cấu hình mặc định và cấu hình người dùng
        merged_config = default_config.copy()
        if config:
            merged_config.update(config)
        
        super().__init__(merged_config)
        
        # Khởi tạo dữ liệu người dùng
        self.user_data = defaultdict(lambda: {
            "queries": [],
            "categories": Counter(),
            "expertise_levels": Counter(),
            "click_history": [],
            "preferences": {}
        })
        
        # Thời gian lưu cuối cùng
        self.last_save_time = time.time()
        
        # Tải dữ liệu nếu có
        self._load_data()
    
    def _load_data(self):
        """Tải dữ liệu người dùng từ file."""
        data_file = self.config.get("data_file", "user_psychology_data.json")
        
        try:
            if os.path.exists(data_file):
                with open(data_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                
                # Chuyển đổi dữ liệu JSON thành cấu trúc defaultdict
                for user_id, user_info in data.items():
                    self.user_data[user_id] = user_info
                    
                    # Chuyển đổi categories và expertise_levels thành Counter
                    if "categories" in user_info:
                        self.user_data[user_id]["categories"] = Counter(user_info["categories"])
                    
                    if "expertise_levels" in user_info:
                        self.user_data[user_id]["expertise_levels"] = Counter(user_info["expertise_levels"])
                
                logger.info(f"Đã tải dữ liệu tâm lý người dùng cho {len(self.user_data)} người dùng")
        except Exception as e:
            logger.warning(f"Lỗi khi tải dữ liệu tâm lý người dùng: {str(e)}")
    
    def _save_data(self, force: bool = False):
        """
        Lưu dữ liệu người dùng vào file.
        
        Args:
            force: Bắt buộc lưu ngay lập tức
        """
        if not self.config.get("auto_save", True) and not force:
            return
        
        current_time = time.time()
        save_interval = self.config.get("save_interval", 300)
        
        if force or (current_time - self.last_save_time >= save_interval):
            data_file = self.config.get("data_file", "user_psychology_data.json")
            
            try:
                # Chuyển đổi dữ liệu sang định dạng có thể JSON hóa
                serializable_data = {}
                for user_id, user_info in self.user_data.items():
                    serializable_data[user_id] = {
                        "queries": user_info["queries"],
                        "categories": dict(user_info["categories"]),
                        "expertise_levels": dict(user_info["expertise_levels"]),
                        "click_history": user_info["click_history"],
                        "preferences": user_info["preferences"]
                    }
                
                # Lưu vào file
                with open(data_file, "w", encoding="utf-8") as f:
                    json.dump(serializable_data, f, ensure_ascii=False, indent=2)
                
                self.last_save_time = current_time
                logger.debug(f"Đã lưu dữ liệu tâm lý người dùng cho {len(self.user_data)} người dùng")
            except Exception as e:
                logger.warning(f"Lỗi khi lưu dữ liệu tâm lý người dùng: {str(e)}")
    
    @hook(priority=10)
    def pre_search(self, query: str, user_id: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Hook được gọi trước khi thực hiện tìm kiếm để phân tích tâm lý người dùng.
        
        Args:
            query: Truy vấn tìm kiếm
            user_id: ID người dùng (nếu có)
            **kwargs: Các tham số bổ sung
        
        Returns:
            Dictionary chứa thông tin phân tích và truy vấn đã điều chỉnh
        """
        if not self.is_enabled() or not query:
            return {"query": query}
        
        # Sử dụng ID người dùng ẩn danh nếu không có
        if not user_id:
            user_id = kwargs.get("session_id", "anonymous")
        
        # Phân tích tâm lý dựa trên truy vấn
        result = self._analyze_query(query, user_id)
        
        # Cập nhật dữ liệu người dùng
        if self.config.get("enable_learning", True) and len(query) >= self.config.get("min_query_length", 3):
            self._update_user_data(user_id, query, result)
        
        # Lưu dữ liệu nếu cần
        self._save_data()
        
        # Trả về kết quả
        return {
            "query": query,
            "user_psychology": result,
            "user_id": user_id
        }
    
    @hook(priority=85)
    def post_search(self, results: List[Dict[str, Any]], query: str, user_id: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Hook được gọi sau khi thực hiện tìm kiếm để điều chỉnh kết quả.
        
        Args:
            results: Danh sách kết quả tìm kiếm
            query: Truy vấn tìm kiếm
            user_id: ID người dùng (nếu có)
            **kwargs: Các tham số bổ sung
        
        Returns:
            Dictionary chứa kết quả đã điều chỉnh
        """
        if not self.is_enabled() or not results:
            return {"results": results}
        
        # Sử dụng ID người dùng ẩn danh nếu không có
        if not user_id:
            user_id = kwargs.get("session_id", "anonymous")
        
        # Lấy thông tin tâm lý từ pre_search
        user_psychology = kwargs.get("user_psychology", {})
        
        # Nếu không có, phân tích lại
        if not user_psychology:
            user_psychology = self._analyze_query(query, user_id)
        
        # Điều chỉnh kết quả dựa trên tâm lý người dùng
        adjusted_results = self._adjust_results(results, user_psychology, user_id)
        
        return {
            "results": adjusted_results,
            "original_results": results,
            "user_psychology": user_psychology
        }
    
    def track_click(self, user_id: str, query: str, url: str, position: int, timestamp: Optional[float] = None):
        """
        Theo dõi hành vi click của người dùng.
        
        Args:
            user_id: ID người dùng
            query: Truy vấn tìm kiếm
            url: URL được click
            position: Vị trí của kết quả
            timestamp: Thời gian click (nếu không có sẽ sử dụng thời gian hiện tại)
        """
        if not self.is_enabled():
            return
        
        # Sử dụng thời gian hiện tại nếu không có
        if timestamp is None:
            timestamp = time.time()
        
        # Thêm vào lịch sử click
        click_data = {
            "query": query,
            "url": url,
            "position": position,
            "timestamp": timestamp
        }
        
        self.user_data[user_id]["click_history"].append(click_data)
        
        # Giới hạn số lượng lịch sử click
        max_history = 100
        if len(self.user_data[user_id]["click_history"]) > max_history:
            self.user_data[user_id]["click_history"] = self.user_data[user_id]["click_history"][-max_history:]
        
        # Lưu dữ liệu
        self._save_data()
    
    def _analyze_query(self, query: str, user_id: str) -> Dict[str, Any]:
        """
        Phân tích tâm lý dựa trên truy vấn.
        
        Args:
            query: Truy vấn tìm kiếm
            user_id: ID người dùng
        
        Returns:
            Dictionary chứa thông tin phân tích
        """
        # Chuẩn bị truy vấn để phân tích
        query_lower = query.lower()
        
        # Phân tích mục đích tìm kiếm
        category_scores = {}
        user_patterns = self.config.get("user_patterns", {})
        
        for category, patterns in user_patterns.items():
            score = sum(1 for pattern in patterns if pattern.lower() in query_lower)
            category_scores[category] = score
        
        # Xác định mục đích chính
        primary_category = max(category_scores.items(), key=lambda x: x[1])[0] if category_scores else "information"
        
        # Nếu không có mục đích rõ ràng, sử dụng "information"
        if max(category_scores.values()) == 0:
            primary_category = "information"
        
        # Phân tích cấp độ chuyên môn
        expertise_scores = {}
        expertise_patterns = self.config.get("expertise_patterns", {})
        
        for level, patterns in expertise_patterns.items():
            score = sum(1 for pattern in patterns if pattern.lower() in query_lower)
            expertise_scores[level] = score
        
        # Xác định cấp độ chuyên môn
        expertise_level = max(expertise_scores.items(), key=lambda x: x[1])[0] if expertise_scores else "intermediate"
        
        # Nếu không có cấp độ chuyên môn rõ ràng, sử dụng "intermediate"
        if max(expertise_scores.values()) == 0:
            expertise_level = "intermediate"
        
        # Kết hợp với dữ liệu lịch sử người dùng
        if user_id in self.user_data:
            # Lấy mục đích tìm kiếm phổ biến nhất
            user_categories = self.user_data[user_id]["categories"]
            if user_categories:
                most_common_category = user_categories.most_common(1)[0][0]
                # Nếu không có mục đích rõ ràng từ truy vấn, sử dụng mục đích phổ biến nhất
                if max(category_scores.values()) == 0:
                    primary_category = most_common_category
            
            # Lấy cấp độ chuyên môn phổ biến nhất
            user_expertise = self.user_data[user_id]["expertise_levels"]
            if user_expertise:
                most_common_expertise = user_expertise.most_common(1)[0][0]
                # Nếu không có cấp độ chuyên môn rõ ràng từ truy vấn, sử dụng cấp độ phổ biến nhất
                if max(expertise_scores.values()) == 0:
                    expertise_level = most_common_expertise
        
        # Tạo kết quả phân tích
        result = {
            "query_intent": primary_category,
            "expertise_level": expertise_level,
            "category_scores": category_scores,
            "expertise_scores": expertise_scores,
            "is_personal": self._is_personal_query(query),
            "complexity": self._calculate_query_complexity(query),
            "sentiment": self._analyze_sentiment(query)
        }
        
        return result
    
    def _is_personal_query(self, query: str) -> bool:
        """
        Xác định xem truy vấn có mang tính cá nhân không.
        
        Args:
            query: Truy vấn tìm kiếm
        
        Returns:
            True nếu truy vấn mang tính cá nhân, False nếu không
        """
        personal_indicators = [
            "của tôi", "cho tôi", "tôi cần", "tôi muốn", "tôi thích",
            "của mình", "cho mình", "mình cần", "mình muốn", "mình thích",
            "làm sao để tôi", "làm sao để mình"
        ]
        
        query_lower = query.lower()
        return any(indicator in query_lower for indicator in personal_indicators)
    
    def _calculate_query_complexity(self, query: str) -> float:
        """
        Tính độ phức tạp của truy vấn.
        
        Args:
            query: Truy vấn tìm kiếm
        
        Returns:
            Điểm độ phức tạp (0-1)
        """
        # Các yếu tố ảnh hưởng đến độ phức tạp
        factors = {
            "length": len(query) / 100,  # Độ dài truy vấn
            "words": len(query.split()) / 20,  # Số từ
            "special_chars": len(re.findall(r'[^\w\s]', query)) / 10,  # Số ký tự đặc biệt
            "technical_terms": len(re.findall(r'\b(?:api|code|function|class|method|algorithm|framework|library|implementation|syntax|interface|protocol|database|query|schema|system|architecture|design pattern)\b', query.lower())) / 5  # Số thuật ngữ kỹ thuật
        }
        
        # Tính điểm trung bình
        complexity = sum(factors.values()) / len(factors)
        
        # Chuẩn hóa về khoảng 0-1
        return min(1.0, max(0.0, complexity))
    
    def _analyze_sentiment(self, query: str) -> str:
        """
        Phân tích cảm xúc trong truy vấn.
        
        Args:
            query: Truy vấn tìm kiếm
        
        Returns:
            Cảm xúc: "positive", "negative", "neutral", hoặc "urgent"
        """
        query_lower = query.lower()
        
        # Các từ khóa tích cực
        positive_keywords = ["tốt", "hay", "thích", "yêu", "thú vị", "vui", "hạnh phúc", "tuyệt", "đẹp", "xuất sắc"]
        
        # Các từ khóa tiêu cực
        negative_keywords = ["xấu", "tệ", "ghét", "buồn", "chán", "khó", "thất vọng", "lỗi", "sai", "kém"]
        
        # Các từ khóa khẩn cấp
        urgent_keywords = ["ngay", "nhanh", "khẩn cấp", "gấp", "cứu", "cấp tốc", "cần gấp", "cần ngay"]
        
        # Đếm số từ khóa
        positive_count = sum(1 for keyword in positive_keywords if keyword in query_lower)
        negative_count = sum(1 for keyword in negative_keywords if keyword in query_lower)
        urgent_count = sum(1 for keyword in urgent_keywords if keyword in query_lower)
        
        # Xác định cảm xúc
        if urgent_count > 0:
            return "urgent"
        elif positive_count > negative_count:
            return "positive"
        elif negative_count > positive_count:
            return "negative"
        else:
            return "neutral"
    
    def _update_user_data(self, user_id: str, query: str, analysis: Dict[str, Any]) -> None:
        """
        Cập nhật dữ liệu người dùng.
        
        Args:
            user_id: ID người dùng
            query: Truy vấn tìm kiếm
            analysis: Kết quả phân tích
        """
        # Thêm truy vấn vào lịch sử
        if len(self.user_data[user_id]["queries"]) >= 100:
            self.user_data[user_id]["queries"].pop(0)
        
        self.user_data[user_id]["queries"].append(query)
        
        # Cập nhật thống kê loại truy vấn
        self.user_data[user_id]["categories"][analysis["query_intent"]] += 1
        
        # Cập nhật thống kê cấp độ chuyên môn
        self.user_data[user_id]["expertise_levels"][analysis["expertise_level"]] += 1
        
        # Cập nhật sở thích
        preferences = self.user_data[user_id]["preferences"]
        if "topics" not in preferences:
            preferences["topics"] = Counter()
        
        # Trích xuất các chủ đề từ truy vấn
        topics = self._extract_topics(query)
        for topic in topics:
            preferences["topics"][topic] += 1
    
    def _extract_topics(self, query: str) -> List[str]:
        """
        Trích xuất các chủ đề từ truy vấn.
        
        Args:
            query: Truy vấn tìm kiếm
        
        Returns:
            Danh sách các chủ đề
        """
        # Phân tích cú pháp đơn giản để trích xuất danh từ
        words = query.lower().split()
        
        # Loại bỏ các từ dừng
        stopwords = ["là", "và", "của", "có", "không", "được", "các", "những", "để", "về", "trong", "ngoài", "với", "cho"]
        filtered_words = [word for word in words if word not in stopwords and len(word) > 2]
        
        # Lấy các từ còn lại làm chủ đề
        return filtered_words
    
    def _adjust_results(self, results: List[Dict[str, Any]], psychology: Dict[str, Any], user_id: str) -> List[Dict[str, Any]]:
        """
        Điều chỉnh kết quả dựa trên tâm lý người dùng.
        
        Args:
            results: Danh sách kết quả tìm kiếm
            psychology: Thông tin tâm lý người dùng
            user_id: ID người dùng
        
        Returns:
            Danh sách kết quả đã điều chỉnh
        """
        if not results:
            return results
        
        # Tạo bản sao để tránh thay đổi kết quả gốc
        adjusted_results = results.copy()
        
        # Lấy thông tin tâm lý
        query_intent = psychology.get("query_intent", "information")
        expertise_level = psychology.get("expertise_level", "intermediate")
        is_personal = psychology.get("is_personal", False)
        sentiment = psychology.get("sentiment", "neutral")
        
        # Cấu trúc để lưu điểm số mới cho mỗi kết quả
        result_scores = {}
        
        for i, result in enumerate(adjusted_results):
            # Điểm ban đầu dựa trên vị trí
            score = 1.0 / (i + 1)
            
            # Điều chỉnh dựa trên mục đích tìm kiếm
            if query_intent == "shopping" and any(keyword in result.get("title", "").lower() for keyword in ["mua", "giá", "bán", "shop"]):
                score *= 1.5
            
            elif query_intent == "information" and any(keyword in result.get("title", "").lower() for keyword in ["là gì", "thông tin", "định nghĩa"]):
                score *= 1.5
            
            elif query_intent == "academic" and any(keyword in result.get("url", "").lower() for keyword in ["edu", "ac", "research", "journal"]):
                score *= 1.5
            
            # Điều chỉnh dựa trên cấp độ chuyên môn
            if expertise_level == "beginner" and any(keyword in result.get("title", "").lower() for keyword in ["cơ bản", "cho người mới", "nhập môn"]):
                score *= 1.5
            
            elif expertise_level == "expert" and any(keyword in result.get("title", "").lower() for keyword in ["nâng cao", "chuyên sâu", "advanced"]):
                score *= 1.5
            
            # Điều chỉnh dựa trên tính cá nhân
            if is_personal and "forum" in result.get("url", "").lower():
                score *= 1.3
            
            # Điều chỉnh dựa trên cảm xúc
            if sentiment == "urgent" and any(keyword in result.get("title", "").lower() for keyword in ["nhanh", "ngay", "gấp"]):
                score *= 1.3
            
            # Điều chỉnh dựa trên lịch sử click (nếu có)
            if user_id in self.user_data and result.get("url") is not None:
                for click in self.user_data[user_id]["click_history"]:
                    if click["url"] == result.get("url"):
                        score *= 1.2
                        break
            
            # Lưu điểm số
            result_scores[i] = score
        
        # Sắp xếp lại kết quả dựa trên điểm số mới
        sorted_indices = sorted(range(len(adjusted_results)), key=lambda i: result_scores[i], reverse=True)
        adjusted_results = [adjusted_results[i] for i in sorted_indices]
        
        # Thêm thông tin tâm lý vào mỗi kết quả
        for result in adjusted_results:
            result["psychology_match"] = {
                "intent": query_intent,
                "expertise": expertise_level,
                "is_personal": is_personal,
                "sentiment": sentiment
            }
        
        return adjusted_results 