"""
Module định nghĩa UrlFilterPlugin.

Plugin này lọc URL dựa trên các quy tắc tùy chỉnh.
"""

from typing import Dict, Any, List, Optional, Set, Pattern
import re
import os
import json
from urllib.parse import urlparse
from ..plugins.base_plugin import BasePlugin
from ..utils.plugin_manager import hook

# Thiết lập logging
from ..utils.structured_logging import get_logger
logger = get_logger(__name__)


class UrlFilterPlugin(BasePlugin):
    """
    Plugin lọc URL dựa trên các quy tắc tùy chỉnh.
    
    Tính năng:
    - Lọc URL dựa trên domain
    - Lọc URL dựa trên đường dẫn
    - Lọc URL dựa trên tham số
    - Lọc URL dựa trên biểu thức chính quy
    - Lọc URL dựa trên danh sách đen/trắng
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Khởi tạo UrlFilterPlugin.
        
        Args:
            config: <PERSON><PERSON><PERSON> hình cho plugin
        """
        default_config = {
            "enabled": True,
            "blacklist_domains": [],
            "whitelist_domains": [],
            "blacklist_paths": [],
            "whitelist_paths": [],
            "blacklist_params": [],
            "whitelist_params": [],
            "blacklist_regex": [],
            "whitelist_regex": [],
            "blacklist_file": "",
            "whitelist_file": "",
            "filter_mode": "blacklist",  # "blacklist", "whitelist", "both"
            "filter_priority": "whitelist",  # "whitelist", "blacklist"
            "filter_subdomains": True,
            "filter_www": True,
            "filter_protocol": False,
            "filter_port": False,
            "filter_query": False,
            "filter_fragment": False,
            "filter_case_sensitive": False,
            "filter_empty_paths": False,
            "filter_default_paths": False,
            "filter_trailing_slash": False,
            "filter_duplicate_slashes": False,
            "filter_encoded_chars": False,
            "filter_non_ascii": False,
            "filter_unsafe_chars": False,
            "filter_control_chars": False,
            "filter_spaces": False,
            "filter_long_urls": False,
            "max_url_length": 2000,
            "max_domain_length": 253,
            "max_path_length": 1000,
            "max_query_length": 1000,
            "max_fragment_length": 1000,
            "max_param_name_length": 100,
            "max_param_value_length": 1000,
            "max_params": 100,
            "log_filtered_urls": True,
            "log_allowed_urls": False,
            "auto_update_lists": True,
            "update_interval": 86400,  # 1 ngày
            "cache_results": True,
            "cache_ttl": 3600,  # 1 giờ
            "cache_size": 1000,
        }
        
        # Merge default config with provided config
        merged_config = {**default_config, **(config or {})}
        super().__init__(merged_config)
        
        # Initialize plugin
        self.name = "UrlFilterPlugin"
        self.version = "1.0.0"
        self.description = "Plugin lọc URL dựa trên các quy tắc tùy chỉnh"
        self.author = "Augment Code"
        
        # Initialize filter lists
        self.blacklist_domains: Set[str] = set(self.config.get("blacklist_domains", []))
        self.whitelist_domains: Set[str] = set(self.config.get("whitelist_domains", []))
        self.blacklist_paths: Set[str] = set(self.config.get("blacklist_paths", []))
        self.whitelist_paths: Set[str] = set(self.config.get("whitelist_paths", []))
        self.blacklist_params: Set[str] = set(self.config.get("blacklist_params", []))
        self.whitelist_params: Set[str] = set(self.config.get("whitelist_params", []))
        
        # Compile regex patterns
        self.blacklist_regex: List[Pattern] = []
        for pattern in self.config.get("blacklist_regex", []):
            try:
                flags = 0 if self.config.get("filter_case_sensitive", False) else re.IGNORECASE
                self.blacklist_regex.append(re.compile(pattern, flags))
            except re.error as e:
                logger.error(f"Invalid blacklist regex pattern: {pattern}, error: {str(e)}")
        
        self.whitelist_regex: List[Pattern] = []
        for pattern in self.config.get("whitelist_regex", []):
            try:
                flags = 0 if self.config.get("filter_case_sensitive", False) else re.IGNORECASE
                self.whitelist_regex.append(re.compile(pattern, flags))
            except re.error as e:
                logger.error(f"Invalid whitelist regex pattern: {pattern}, error: {str(e)}")
        
        # Load filter lists from files
        self._load_filter_lists()
        
        # Initialize cache
        self.url_cache: Dict[str, bool] = {}
        self.cache_timestamps: Dict[str, float] = {}
        
        logger.info(f"UrlFilterPlugin initialized with config: {self.config}")
    
    def _load_filter_lists(self) -> None:
        """
        Tải danh sách lọc từ file.
        """
        # Load blacklist
        blacklist_file = self.config.get("blacklist_file", "")
        if blacklist_file and os.path.exists(blacklist_file):
            try:
                with open(blacklist_file, "r", encoding="utf-8") as f:
                    blacklist_data = json.load(f)
                    
                    # Update blacklist domains
                    if "domains" in blacklist_data:
                        self.blacklist_domains.update(blacklist_data["domains"])
                    
                    # Update blacklist paths
                    if "paths" in blacklist_data:
                        self.blacklist_paths.update(blacklist_data["paths"])
                    
                    # Update blacklist params
                    if "params" in blacklist_data:
                        self.blacklist_params.update(blacklist_data["params"])
                    
                    # Update blacklist regex
                    if "regex" in blacklist_data:
                        for pattern in blacklist_data["regex"]:
                            try:
                                flags = 0 if self.config.get("filter_case_sensitive", False) else re.IGNORECASE
                                self.blacklist_regex.append(re.compile(pattern, flags))
                            except re.error as e:
                                logger.error(f"Invalid blacklist regex pattern: {pattern}, error: {str(e)}")
                    
                    logger.info(f"Loaded blacklist from {blacklist_file}")
            except Exception as e:
                logger.error(f"Error loading blacklist: {str(e)}")
        
        # Load whitelist
        whitelist_file = self.config.get("whitelist_file", "")
        if whitelist_file and os.path.exists(whitelist_file):
            try:
                with open(whitelist_file, "r", encoding="utf-8") as f:
                    whitelist_data = json.load(f)
                    
                    # Update whitelist domains
                    if "domains" in whitelist_data:
                        self.whitelist_domains.update(whitelist_data["domains"])
                    
                    # Update whitelist paths
                    if "paths" in whitelist_data:
                        self.whitelist_paths.update(whitelist_data["paths"])
                    
                    # Update whitelist params
                    if "params" in whitelist_data:
                        self.whitelist_params.update(whitelist_data["params"])
                    
                    # Update whitelist regex
                    if "regex" in whitelist_data:
                        for pattern in whitelist_data["regex"]:
                            try:
                                flags = 0 if self.config.get("filter_case_sensitive", False) else re.IGNORECASE
                                self.whitelist_regex.append(re.compile(pattern, flags))
                            except re.error as e:
                                logger.error(f"Invalid whitelist regex pattern: {pattern}, error: {str(e)}")
                    
                    logger.info(f"Loaded whitelist from {whitelist_file}")
            except Exception as e:
                logger.error(f"Error loading whitelist: {str(e)}")
    
    @hook(priority=20)
    def post_search(self, query: str, results: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Hook được gọi sau khi thực hiện tìm kiếm.
        
        Args:
            query: Truy vấn tìm kiếm
            results: Kết quả tìm kiếm
            **kwargs: Các tham số khác
            
        Returns:
            Dict[str, Any]: Kết quả đã lọc
        """
        if not self.is_enabled():
            return results
        
        # Kiểm tra kết quả
        if not results.get("success", False) or not results.get("results", []):
            return results
        
        # Lọc kết quả
        filtered_results = []
        filtered_count = 0
        
        for result in results.get("results", []):
            url = result.get("url", "")
            
            # Kiểm tra URL
            if not url:
                continue
            
            # Kiểm tra xem URL có bị lọc không
            if self._should_filter_url(url):
                filtered_count += 1
                if self.config.get("log_filtered_urls", True):
                    logger.info(f"Filtered URL: {url}")
            else:
                filtered_results.append(result)
                if self.config.get("log_allowed_urls", False):
                    logger.info(f"Allowed URL: {url}")
        
        # Cập nhật kết quả
        results["results"] = filtered_results
        results["filtered_count"] = filtered_count
        results["original_count"] = len(results.get("results", [])) + filtered_count
        
        # Thêm thông tin về plugin
        if "plugin_info" not in results:
            results["plugin_info"] = {}
        
        results["plugin_info"]["url_filter"] = {
            "filtered_count": filtered_count,
            "original_count": results["original_count"],
            "remaining_count": len(filtered_results),
            "filter_ratio": filtered_count / max(1, results["original_count"])
        }
        
        return results
    
    @hook(priority=30)
    def pre_extract_content(self, url: str, **kwargs) -> Dict[str, Any]:
        """
        Hook được gọi trước khi trích xuất nội dung.
        
        Args:
            url: URL của trang web
            **kwargs: Các tham số khác
            
        Returns:
            Dict[str, Any]: Kết quả xử lý
        """
        if not self.is_enabled():
            return {"url": url, "modified": False}
        
        # Kiểm tra xem URL có bị lọc không
        if self._should_filter_url(url):
            if self.config.get("log_filtered_urls", True):
                logger.info(f"Filtered URL for content extraction: {url}")
            
            # Trả về URL rỗng để ngăn trích xuất nội dung
            return {"url": "", "modified": True, "filtered": True}
        
        if self.config.get("log_allowed_urls", False):
            logger.info(f"Allowed URL for content extraction: {url}")
        
        return {"url": url, "modified": False}
    
    def _should_filter_url(self, url: str) -> bool:
        """
        Kiểm tra xem URL có bị lọc không.
        
        Args:
            url: URL cần kiểm tra
            
        Returns:
            bool: True nếu URL bị lọc, False nếu không
        """
        # Kiểm tra cache
        if self.config.get("cache_results", True) and url in self.url_cache:
            return self.url_cache[url]
        
        # Parse URL
        try:
            parsed_url = urlparse(url)
            domain = parsed_url.netloc.lower()
            path = parsed_url.path.lower()
            
            # Loại bỏ www. nếu được cấu hình
            if self.config.get("filter_www", True) and domain.startswith("www."):
                domain = domain[4:]
            
            # Loại bỏ port nếu được cấu hình
            if self.config.get("filter_port", False) and ":" in domain:
                domain = domain.split(":")[0]
        except Exception as e:
            logger.error(f"Error parsing URL: {url}, error: {str(e)}")
            return True
        
        # Kiểm tra độ dài URL
        if self.config.get("filter_long_urls", False):
            if len(url) > self.config.get("max_url_length", 2000):
                self._cache_result(url, True)
                return True
            
            if len(domain) > self.config.get("max_domain_length", 253):
                self._cache_result(url, True)
                return True
            
            if len(path) > self.config.get("max_path_length", 1000):
                self._cache_result(url, True)
                return True
        
        # Kiểm tra whitelist domain
        if self.whitelist_domains:
            domain_in_whitelist = False
            
            # Kiểm tra domain chính xác
            if domain in self.whitelist_domains:
                domain_in_whitelist = True
            
            # Kiểm tra subdomain nếu được cấu hình
            if not domain_in_whitelist and self.config.get("filter_subdomains", True):
                for whitelist_domain in self.whitelist_domains:
                    if domain.endswith("." + whitelist_domain):
                        domain_in_whitelist = True
                        break
            
            # Nếu filter_mode là whitelist hoặc both, và domain không trong whitelist, lọc URL
            if self.config.get("filter_mode", "blacklist") in ["whitelist", "both"] and not domain_in_whitelist:
                self._cache_result(url, True)
                return True
        
        # Kiểm tra blacklist domain
        if self.blacklist_domains:
            domain_in_blacklist = False
            
            # Kiểm tra domain chính xác
            if domain in self.blacklist_domains:
                domain_in_blacklist = True
            
            # Kiểm tra subdomain nếu được cấu hình
            if not domain_in_blacklist and self.config.get("filter_subdomains", True):
                for blacklist_domain in self.blacklist_domains:
                    if domain.endswith("." + blacklist_domain):
                        domain_in_blacklist = True
                        break
            
            # Nếu filter_mode là blacklist hoặc both, và domain trong blacklist, lọc URL
            if self.config.get("filter_mode", "blacklist") in ["blacklist", "both"] and domain_in_blacklist:
                # Kiểm tra filter_priority
                if self.config.get("filter_priority", "whitelist") == "blacklist" or not self.whitelist_domains:
                    self._cache_result(url, True)
                    return True
        
        # Kiểm tra whitelist path
        if self.whitelist_paths:
            path_in_whitelist = False
            
            for whitelist_path in self.whitelist_paths:
                if path.startswith(whitelist_path):
                    path_in_whitelist = True
                    break
            
            # Nếu filter_mode là whitelist hoặc both, và path không trong whitelist, lọc URL
            if self.config.get("filter_mode", "blacklist") in ["whitelist", "both"] and not path_in_whitelist:
                self._cache_result(url, True)
                return True
        
        # Kiểm tra blacklist path
        if self.blacklist_paths:
            path_in_blacklist = False
            
            for blacklist_path in self.blacklist_paths:
                if path.startswith(blacklist_path):
                    path_in_blacklist = True
                    break
            
            # Nếu filter_mode là blacklist hoặc both, và path trong blacklist, lọc URL
            if self.config.get("filter_mode", "blacklist") in ["blacklist", "both"] and path_in_blacklist:
                # Kiểm tra filter_priority
                if self.config.get("filter_priority", "whitelist") == "blacklist" or not self.whitelist_paths:
                    self._cache_result(url, True)
                    return True
        
        # Kiểm tra whitelist regex
        if self.whitelist_regex:
            url_in_whitelist = False
            
            for pattern in self.whitelist_regex:
                if pattern.search(url):
                    url_in_whitelist = True
                    break
            
            # Nếu filter_mode là whitelist hoặc both, và URL không khớp với whitelist regex, lọc URL
            if self.config.get("filter_mode", "blacklist") in ["whitelist", "both"] and not url_in_whitelist:
                self._cache_result(url, True)
                return True
        
        # Kiểm tra blacklist regex
        if self.blacklist_regex:
            url_in_blacklist = False
            
            for pattern in self.blacklist_regex:
                if pattern.search(url):
                    url_in_blacklist = True
                    break
            
            # Nếu filter_mode là blacklist hoặc both, và URL khớp với blacklist regex, lọc URL
            if self.config.get("filter_mode", "blacklist") in ["blacklist", "both"] and url_in_blacklist:
                # Kiểm tra filter_priority
                if self.config.get("filter_priority", "whitelist") == "blacklist" or not self.whitelist_regex:
                    self._cache_result(url, True)
                    return True
        
        # Nếu không bị lọc, lưu vào cache và trả về False
        self._cache_result(url, False)
        return False
    
    def _cache_result(self, url: str, result: bool) -> None:
        """
        Lưu kết quả vào cache.
        
        Args:
            url: URL
            result: Kết quả lọc
        """
        if self.config.get("cache_results", True):
            # Giới hạn kích thước cache
            cache_size = self.config.get("cache_size", 1000)
            if len(self.url_cache) >= cache_size:
                # Xóa mục cũ nhất
                oldest_url = min(self.cache_timestamps.items(), key=lambda x: x[1])[0]
                del self.url_cache[oldest_url]
                del self.cache_timestamps[oldest_url]
            
            # Lưu kết quả vào cache
            self.url_cache[url] = result
            self.cache_timestamps[url] = time.time()
    
    def add_blacklist_domain(self, domain: str) -> None:
        """
        Thêm domain vào blacklist.
        
        Args:
            domain: Domain cần thêm
        """
        self.blacklist_domains.add(domain.lower())
        logger.info(f"Added domain to blacklist: {domain}")
    
    def add_whitelist_domain(self, domain: str) -> None:
        """
        Thêm domain vào whitelist.
        
        Args:
            domain: Domain cần thêm
        """
        self.whitelist_domains.add(domain.lower())
        logger.info(f"Added domain to whitelist: {domain}")
    
    def add_blacklist_path(self, path: str) -> None:
        """
        Thêm path vào blacklist.
        
        Args:
            path: Path cần thêm
        """
        self.blacklist_paths.add(path.lower())
        logger.info(f"Added path to blacklist: {path}")
    
    def add_whitelist_path(self, path: str) -> None:
        """
        Thêm path vào whitelist.
        
        Args:
            path: Path cần thêm
        """
        self.whitelist_paths.add(path.lower())
        logger.info(f"Added path to whitelist: {path}")
    
    def add_blacklist_regex(self, pattern: str) -> None:
        """
        Thêm regex vào blacklist.
        
        Args:
            pattern: Pattern cần thêm
        """
        try:
            flags = 0 if self.config.get("filter_case_sensitive", False) else re.IGNORECASE
            self.blacklist_regex.append(re.compile(pattern, flags))
            logger.info(f"Added regex to blacklist: {pattern}")
        except re.error as e:
            logger.error(f"Invalid blacklist regex pattern: {pattern}, error: {str(e)}")
    
    def add_whitelist_regex(self, pattern: str) -> None:
        """
        Thêm regex vào whitelist.
        
        Args:
            pattern: Pattern cần thêm
        """
        try:
            flags = 0 if self.config.get("filter_case_sensitive", False) else re.IGNORECASE
            self.whitelist_regex.append(re.compile(pattern, flags))
            logger.info(f"Added regex to whitelist: {pattern}")
        except re.error as e:
            logger.error(f"Invalid whitelist regex pattern: {pattern}, error: {str(e)}")
    
    def clear_cache(self) -> None:
        """
        Xóa cache.
        """
        self.url_cache.clear()
        self.cache_timestamps.clear()
        logger.info("URL cache cleared")
