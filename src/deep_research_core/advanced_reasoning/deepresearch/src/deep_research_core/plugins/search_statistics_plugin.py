"""
Module định nghĩa SearchStatisticsPlugin.

Plugin này thu thập và phân tích thống kê về các truy vấn tìm kiếm.
"""

from typing import Dict, Any, List, Optional
import time
import json
import os
import re
from datetime import datetime
from ..plugins.base_plugin import BasePlugin
from ..utils.plugin_manager import hook

# Thiết lập logging
from ..utils.structured_logging import get_logger
logger = get_logger(__name__)


class SearchStatisticsPlugin(BasePlugin):
    """
    Plugin thu thập và phân tích thống kê về các truy vấn tìm kiếm.
    
    Tính năng:
    - <PERSON>hu thập thống kê về truy vấn tìm kiếm
    - Phân tích xu hướng tìm kiếm
    - <PERSON> dõi hiệu suất tìm kiếm
    - Xuất báo cáo thống kê
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Khởi tạo SearchStatisticsPlugin.
        
        Args:
            config: C<PERSON>u hình cho plugin
        """
        default_config = {
            "enabled": True,
            "stats_file": "search_statistics.json",
            "max_history": 1000,
            "track_query_frequency": True,
            "track_query_performance": True,
            "track_query_success_rate": True,
            "track_query_result_count": True,
            "track_query_cache_hits": True,
            "track_query_language": True,
            "track_query_engine": True,
            "track_query_method": True,
            "track_query_time": True,
            "track_query_ip": False,
            "track_query_user_agent": False,
            "track_query_referer": False,
            "track_query_session": False,
            "track_query_user": False,
            "track_query_location": False,
            "track_query_device": False,
            "track_query_browser": False,
            "track_query_os": False,
            "track_query_screen_size": False,
            "track_query_viewport_size": False,
            "track_query_connection_type": False,
            "track_query_connection_speed": False,
            "track_query_battery_level": False,
            "track_query_memory_usage": False,
            "track_query_cpu_usage": False,
            "track_query_disk_usage": False,
            "track_query_network_usage": False,
            "track_query_error": True,
            "track_query_warning": True,
            "track_query_info": True,
            "track_query_debug": False,
            "track_query_trace": False,
            "track_query_log": False,
            "track_query_exception": True,
            "track_query_stack_trace": False,
            "track_query_memory_leak": False,
            "track_query_memory_fragmentation": False,
            "track_query_memory_allocation": False,
            "track_query_memory_deallocation": False,
            "track_query_memory_usage_over_time": False,
            "track_query_cpu_usage_over_time": False,
            "track_query_disk_usage_over_time": False,
            "track_query_network_usage_over_time": False,
            "track_query_battery_level_over_time": False,
            "track_query_connection_speed_over_time": False,
            "track_query_connection_type_over_time": False,
            "track_query_viewport_size_over_time": False,
            "track_query_screen_size_over_time": False,
            "track_query_browser_over_time": False,
            "track_query_os_over_time": False,
            "track_query_device_over_time": False,
            "track_query_location_over_time": False,
            "track_query_user_over_time": False,
            "track_query_session_over_time": False,
            "track_query_referer_over_time": False,
            "track_query_user_agent_over_time": False,
            "track_query_ip_over_time": False,
            "auto_save": True,
            "save_interval": 3600,  # 1 giờ
            "anonymize_data": True,
            "generate_reports": True,
            "report_interval": 86400,  # 1 ngày
            "report_file": "search_statistics_report.json",
            "report_format": "json",
            "report_include_raw_data": False,
            "report_include_aggregated_data": True,
            "report_include_charts": False,
            "report_include_tables": True,
            "report_include_graphs": False,
            "report_include_maps": False,
            "report_include_heatmaps": False,
            "report_include_treemaps": False,
            "report_include_sankey_diagrams": False,
            "report_include_chord_diagrams": False,
            "report_include_network_diagrams": False,
            "report_include_word_clouds": False,
            "report_include_bubble_charts": False,
            "report_include_scatter_plots": False,
            "report_include_line_charts": True,
            "report_include_bar_charts": True,
            "report_include_pie_charts": True,
            "report_include_radar_charts": False,
            "report_include_polar_charts": False,
            "report_include_area_charts": False,
            "report_include_box_plots": False,
            "report_include_violin_plots": False,
            "report_include_histograms": False,
            "report_include_density_plots": False,
            "report_include_cumulative_distribution_functions": False,
            "report_include_quantile_quantile_plots": False,
            "report_include_probability_plots": False,
            "report_include_residual_plots": False,
            "report_include_partial_dependence_plots": False,
            "report_include_interaction_plots": False,
            "report_include_correlation_matrices": False,
            "report_include_covariance_matrices": False,
            "report_include_confusion_matrices": False,
            "report_include_roc_curves": False,
            "report_include_precision_recall_curves": False,
            "report_include_lift_curves": False,
            "report_include_gain_curves": False,
            "report_include_calibration_curves": False,
            "report_include_learning_curves": False,
            "report_include_validation_curves": False,
        }
        
        # Merge default config with provided config
        merged_config = {**default_config, **(config or {})}
        super().__init__(merged_config)
        
        # Initialize plugin
        self.name = "SearchStatisticsPlugin"
        self.version = "1.0.0"
        self.description = "Plugin thu thập và phân tích thống kê về các truy vấn tìm kiếm"
        self.author = "Augment Code"
        
        # Initialize statistics
        self.statistics = {
            "queries": [],
            "query_count": 0,
            "success_count": 0,
            "error_count": 0,
            "cache_hit_count": 0,
            "total_search_time": 0,
            "avg_search_time": 0,
            "avg_result_count": 0,
            "last_save_time": time.time(),
            "last_report_time": time.time(),
            "start_time": time.time(),
            "languages": {},
            "engines": {},
            "methods": {},
            "errors": {},
            "warnings": {},
            "popular_queries": {},
            "query_trends": {},
            "performance_trends": {},
            "success_rate_trends": {},
            "cache_hit_trends": {},
            "result_count_trends": {},
        }
        
        # Load existing statistics if available
        self._load_statistics()
        
        logger.info(f"SearchStatisticsPlugin initialized with config: {self.config}")
    
    def __del__(self):
        """
        Dọn dẹp khi plugin bị hủy.
        """
        # Save statistics before plugin is destroyed
        if self.config.get("auto_save", True):
            self._save_statistics()
    
    @hook(priority=10)
    def pre_search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Hook được gọi trước khi thực hiện tìm kiếm.
        
        Args:
            query: Truy vấn tìm kiếm
            **kwargs: Các tham số khác
            
        Returns:
            Dict[str, Any]: Kết quả xử lý
        """
        if not self.is_enabled():
            return {"query": query}
        
        # Lưu thời gian bắt đầu tìm kiếm
        kwargs["_search_start_time"] = time.time()
        
        return {"query": query}
    
    @hook(priority=90)
    def post_search(self, query: str, results: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Hook được gọi sau khi thực hiện tìm kiếm.
        
        Args:
            query: Truy vấn tìm kiếm
            results: Kết quả tìm kiếm
            **kwargs: Các tham số khác
            
        Returns:
            Dict[str, Any]: Kết quả xử lý
        """
        if not self.is_enabled():
            return results
        
        # Thu thập thống kê
        self._collect_statistics(query, results, **kwargs)
        
        # Tự động lưu thống kê nếu được cấu hình
        if self.config.get("auto_save", True):
            current_time = time.time()
            save_interval = self.config.get("save_interval", 3600)
            
            if current_time - self.statistics["last_save_time"] >= save_interval:
                self._save_statistics()
                self.statistics["last_save_time"] = current_time
        
        # Tự động tạo báo cáo nếu được cấu hình
        if self.config.get("generate_reports", True):
            current_time = time.time()
            report_interval = self.config.get("report_interval", 86400)
            
            if current_time - self.statistics["last_report_time"] >= report_interval:
                self._generate_report()
                self.statistics["last_report_time"] = current_time
        
        return results
    
    def _collect_statistics(self, query: str, results: Dict[str, Any], **kwargs) -> None:
        """
        Thu thập thống kê về truy vấn tìm kiếm.
        
        Args:
            query: Truy vấn tìm kiếm
            results: Kết quả tìm kiếm
            **kwargs: Các tham số khác
        """
        # Tính thời gian tìm kiếm
        search_time = 0
        if "_search_start_time" in kwargs:
            search_time = time.time() - kwargs["_search_start_time"]
        
        # Lấy thông tin từ kết quả
        success = results.get("success", False)
        result_count = len(results.get("results", []))
        error = results.get("error", "")
        cache_hit = results.get("cache_hit", False)
        language = kwargs.get("language", "unknown")
        engine = kwargs.get("engine", "unknown")
        method = kwargs.get("method", "unknown")
        
        # Cập nhật thống kê tổng hợp
        self.statistics["query_count"] += 1
        
        if success:
            self.statistics["success_count"] += 1
        else:
            self.statistics["error_count"] += 1
        
        if cache_hit:
            self.statistics["cache_hit_count"] += 1
        
        self.statistics["total_search_time"] += search_time
        self.statistics["avg_search_time"] = self.statistics["total_search_time"] / self.statistics["query_count"]
        self.statistics["avg_result_count"] = (self.statistics["avg_result_count"] * (self.statistics["query_count"] - 1) + result_count) / self.statistics["query_count"]
        
        # Cập nhật thống kê ngôn ngữ
        if language not in self.statistics["languages"]:
            self.statistics["languages"][language] = 0
        self.statistics["languages"][language] += 1
        
        # Cập nhật thống kê engine
        if engine not in self.statistics["engines"]:
            self.statistics["engines"][engine] = 0
        self.statistics["engines"][engine] += 1
        
        # Cập nhật thống kê phương thức
        if method not in self.statistics["methods"]:
            self.statistics["methods"][method] = 0
        self.statistics["methods"][method] += 1
        
        # Cập nhật thống kê lỗi
        if not success and error:
            if error not in self.statistics["errors"]:
                self.statistics["errors"][error] = 0
            self.statistics["errors"][error] += 1
        
        # Cập nhật thống kê truy vấn phổ biến
        if self.config.get("track_query_frequency", True):
            if query not in self.statistics["popular_queries"]:
                self.statistics["popular_queries"][query] = 0
            self.statistics["popular_queries"][query] += 1
        
        # Thêm truy vấn vào lịch sử
        query_data = {
            "query": query,
            "timestamp": time.time(),
            "datetime": datetime.now().isoformat(),
            "success": success,
            "result_count": result_count,
            "search_time": search_time,
            "cache_hit": cache_hit,
            "language": language,
            "engine": engine,
            "method": method,
        }
        
        # Thêm thông tin lỗi nếu có
        if not success and error:
            query_data["error"] = error
        
        # Thêm vào lịch sử truy vấn
        self.statistics["queries"].append(query_data)
        
        # Giới hạn kích thước lịch sử
        max_history = self.config.get("max_history", 1000)
        if len(self.statistics["queries"]) > max_history:
            self.statistics["queries"] = self.statistics["queries"][-max_history:]
    
    def _load_statistics(self) -> None:
        """
        Tải thống kê từ file.
        """
        stats_file = self.config.get("stats_file", "search_statistics.json")
        
        try:
            if os.path.exists(stats_file):
                with open(stats_file, "r", encoding="utf-8") as f:
                    loaded_stats = json.load(f)
                    
                    # Cập nhật thống kê
                    for key, value in loaded_stats.items():
                        if key in self.statistics:
                            self.statistics[key] = value
                    
                    logger.info(f"Loaded statistics from {stats_file}")
        except Exception as e:
            logger.error(f"Error loading statistics: {str(e)}")
    
    def _save_statistics(self) -> None:
        """
        Lưu thống kê vào file.
        """
        stats_file = self.config.get("stats_file", "search_statistics.json")
        
        try:
            # Tạo thư mục nếu chưa tồn tại
            os.makedirs(os.path.dirname(os.path.abspath(stats_file)), exist_ok=True)
            
            # Lưu thống kê
            with open(stats_file, "w", encoding="utf-8") as f:
                json.dump(self.statistics, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Saved statistics to {stats_file}")
        except Exception as e:
            logger.error(f"Error saving statistics: {str(e)}")
    
    def _generate_report(self) -> None:
        """
        Tạo báo cáo thống kê.
        """
        report_file = self.config.get("report_file", "search_statistics_report.json")
        report_format = self.config.get("report_format", "json")
        
        try:
            # Tạo báo cáo
            report = self._create_report()
            
            # Tạo thư mục nếu chưa tồn tại
            os.makedirs(os.path.dirname(os.path.abspath(report_file)), exist_ok=True)
            
            # Lưu báo cáo
            if report_format == "json":
                with open(report_file, "w", encoding="utf-8") as f:
                    json.dump(report, f, indent=2, ensure_ascii=False)
            else:
                # Các định dạng khác có thể được thêm vào sau
                logger.warning(f"Unsupported report format: {report_format}")
            
            logger.info(f"Generated report to {report_file}")
        except Exception as e:
            logger.error(f"Error generating report: {str(e)}")
    
    def _create_report(self) -> Dict[str, Any]:
        """
        Tạo báo cáo thống kê.
        
        Returns:
            Dict[str, Any]: Báo cáo thống kê
        """
        # Tính toán thời gian hoạt động
        uptime = time.time() - self.statistics["start_time"]
        
        # Tạo báo cáo
        report = {
            "generated_at": datetime.now().isoformat(),
            "uptime": uptime,
            "uptime_formatted": self._format_duration(uptime),
            "summary": {
                "query_count": self.statistics["query_count"],
                "success_count": self.statistics["success_count"],
                "error_count": self.statistics["error_count"],
                "cache_hit_count": self.statistics["cache_hit_count"],
                "success_rate": self.statistics["success_count"] / max(1, self.statistics["query_count"]),
                "cache_hit_rate": self.statistics["cache_hit_count"] / max(1, self.statistics["query_count"]),
                "avg_search_time": self.statistics["avg_search_time"],
                "avg_result_count": self.statistics["avg_result_count"],
            },
            "languages": self._sort_dict_by_value(self.statistics["languages"]),
            "engines": self._sort_dict_by_value(self.statistics["engines"]),
            "methods": self._sort_dict_by_value(self.statistics["methods"]),
            "errors": self._sort_dict_by_value(self.statistics["errors"]),
            "popular_queries": self._sort_dict_by_value(self.statistics["popular_queries"], limit=20),
        }
        
        # Thêm dữ liệu thô nếu được cấu hình
        if self.config.get("report_include_raw_data", False):
            report["raw_data"] = {
                "queries": self.statistics["queries"]
            }
        
        return report
    
    def _sort_dict_by_value(self, d: Dict[str, Any], reverse: bool = True, limit: Optional[int] = None) -> Dict[str, Any]:
        """
        Sắp xếp từ điển theo giá trị.
        
        Args:
            d: Từ điển cần sắp xếp
            reverse: Sắp xếp giảm dần nếu True, tăng dần nếu False
            limit: Giới hạn số lượng phần tử trả về
            
        Returns:
            Dict[str, Any]: Từ điển đã sắp xếp
        """
        sorted_items = sorted(d.items(), key=lambda x: x[1], reverse=reverse)
        
        if limit:
            sorted_items = sorted_items[:limit]
        
        return dict(sorted_items)
    
    def _format_duration(self, seconds: float) -> str:
        """
        Định dạng thời gian.
        
        Args:
            seconds: Thời gian tính bằng giây
            
        Returns:
            str: Thời gian đã định dạng
        """
        days, remainder = divmod(seconds, 86400)
        hours, remainder = divmod(remainder, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        parts = []
        if days > 0:
            parts.append(f"{int(days)} ngày")
        if hours > 0:
            parts.append(f"{int(hours)} giờ")
        if minutes > 0:
            parts.append(f"{int(minutes)} phút")
        if seconds > 0 or not parts:
            parts.append(f"{int(seconds)} giây")
        
        return ", ".join(parts)
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Lấy thống kê.
        
        Returns:
            Dict[str, Any]: Thống kê
        """
        return self.statistics.copy()
    
    def clear_statistics(self) -> None:
        """
        Xóa thống kê.
        """
        # Lưu thời gian bắt đầu
        start_time = self.statistics["start_time"]
        
        # Khởi tạo lại thống kê
        self.statistics = {
            "queries": [],
            "query_count": 0,
            "success_count": 0,
            "error_count": 0,
            "cache_hit_count": 0,
            "total_search_time": 0,
            "avg_search_time": 0,
            "avg_result_count": 0,
            "last_save_time": time.time(),
            "last_report_time": time.time(),
            "start_time": start_time,
            "languages": {},
            "engines": {},
            "methods": {},
            "errors": {},
            "warnings": {},
            "popular_queries": {},
            "query_trends": {},
            "performance_trends": {},
            "success_rate_trends": {},
            "cache_hit_trends": {},
            "result_count_trends": {},
        }
        
        logger.info("Statistics cleared")
    
    def save_statistics(self) -> None:
        """
        Lưu thống kê vào file.
        """
        self._save_statistics()
    
    def generate_report(self) -> None:
        """
        Tạo báo cáo thống kê.
        """
        self._generate_report()
