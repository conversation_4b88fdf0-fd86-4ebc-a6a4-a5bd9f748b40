"""
Module cung cấp plugin xếp hạng kết quả tìm kiếm.

Module này cung cấp ResultRankingPlugin để xếp hạng kết quả tìm kiếm.
"""

from typing import Dict, Any, List, Optional

from .base_plugin import BasePlugin
from ..utils.plugin_manager import hook
from ..utils.structured_logging import get_logger

logger = get_logger(__name__)


class ResultRankingPlugin(BasePlugin):
    """
    Plugin xếp hạng kết quả tìm kiếm.

    Plugin này cung cấp các phương thức để xếp hạng kết quả tìm kiếm
    dựa trên nhiều tiêu chí khác nhau.
    """

    VERSION = "1.0.0"
    AUTHOR = "Augment Code"
    DEPENDENCIES = []

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Khởi tạo ResultRankingPlugin.

        Args:
            config: <PERSON><PERSON><PERSON> hình cho plugin
        """
        default_config = {
            "enabled": True,
            "ranking_factors": {
                "relevance": 0.5,
                "domain_trust": 0.2,
                "content_quality": 0.2,
                "freshness": 0.1
            },
            "trusted_domains": [
                "wikipedia.org",
                "github.com",
                "stackoverflow.com",
                "python.org",
                "docs.python.org",
                "developer.mozilla.org",
                "w3.org",
                "w3schools.com",
                "microsoft.com",
                "docs.microsoft.com",
                "google.com",
                "developers.google.com",
                "apple.com",
                "developer.apple.com",
                "amazon.com",
                "aws.amazon.com",
                "ibm.com",
                "oracle.com",
                "docs.oracle.com",
                "java.com",
                "docs.oracle.com/javase",
                "php.net",
                "ruby-lang.org",
                "rubyonrails.org",
                "nodejs.org",
                "reactjs.org",
                "angular.io",
                "vuejs.org",
                "jquery.com",
                "getbootstrap.com",
                "tensorflow.org",
                "pytorch.org",
                "scikit-learn.org",
                "numpy.org",
                "pandas.pydata.org",
                "matplotlib.org",
                "scipy.org",
                "kaggle.com",
                "arxiv.org",
                "researchgate.net",
                "ieee.org",
                "acm.org",
                "nature.com",
                "science.org",
                "springer.com",
                "elsevier.com",
                "wiley.com",
                "nih.gov",
                "cdc.gov",
                "who.int",
                "un.org",
                "europa.eu",
                "gov",
                "edu"
            ],
            "untrusted_domains": [
                "example.com",
                "example.org",
                "example.net"
            ],
            "min_relevance_score": 0.3,
            "max_results": 10,
            "remove_duplicates": True,
            "remove_similar": True,
            "similarity_threshold": 0.8,
            "boost_recent_results": True,
            "recent_threshold_days": 365,
            "boost_long_content": True,
            "min_content_length": 1000,
            "boost_structured_content": True,
            "language": "vi"
        }
        
        # Kết hợp cấu hình mặc định và cấu hình người dùng
        merged_config = default_config.copy()
        if config:
            merged_config.update(config)
        
        super().__init__(merged_config)

    @hook(priority=50)
    def post_search(self, results: List[Dict[str, Any]], query: str, **kwargs) -> Dict[str, Any]:
        """
        Hook được gọi sau khi thực hiện tìm kiếm.

        Args:
            results: Danh sách kết quả tìm kiếm
            query: Truy vấn tìm kiếm
            **kwargs: Các tham số khác

        Returns:
            Dictionary chứa kết quả đã xếp hạng và các thông tin khác
        """
        if not self.is_enabled() or not results:
            return {"results": results}
        
        # Xếp hạng kết quả
        ranked_results = self._rank_results(results, query)
        
        # Loại bỏ kết quả trùng lặp nếu được cấu hình
        if self.config.get("remove_duplicates", True):
            ranked_results = self._remove_duplicates(ranked_results)
        
        # Loại bỏ kết quả tương tự nếu được cấu hình
        if self.config.get("remove_similar", True):
            ranked_results = self._remove_similar(ranked_results)
        
        # Giới hạn số lượng kết quả
        max_results = self.config.get("max_results", 10)
        if len(ranked_results) > max_results:
            ranked_results = ranked_results[:max_results]
        
        # Trả về kết quả
        return {
            "results": ranked_results,
            "original_results": results,
            "ranking_applied": True
        }

    def _rank_results(self, results: List[Dict[str, Any]], query: str) -> List[Dict[str, Any]]:
        """
        Xếp hạng kết quả tìm kiếm.

        Args:
            results: Danh sách kết quả tìm kiếm
            query: Truy vấn tìm kiếm

        Returns:
            Danh sách kết quả đã xếp hạng
        """
        # Tính điểm cho từng kết quả
        scored_results = []
        for result in results:
            # Tính điểm cho từng yếu tố xếp hạng
            relevance_score = self._calculate_relevance_score(result, query)
            domain_trust_score = self._calculate_domain_trust_score(result)
            content_quality_score = self._calculate_content_quality_score(result)
            freshness_score = self._calculate_freshness_score(result)
            
            # Tính điểm tổng hợp
            ranking_factors = self.config.get("ranking_factors", {})
            final_score = (
                relevance_score * ranking_factors.get("relevance", 0.5) +
                domain_trust_score * ranking_factors.get("domain_trust", 0.2) +
                content_quality_score * ranking_factors.get("content_quality", 0.2) +
                freshness_score * ranking_factors.get("freshness", 0.1)
            )
            
            # Thêm điểm vào kết quả
            result_copy = result.copy()
            result_copy["relevance_score"] = relevance_score
            result_copy["domain_trust_score"] = domain_trust_score
            result_copy["content_quality_score"] = content_quality_score
            result_copy["freshness_score"] = freshness_score
            result_copy["final_score"] = final_score
            
            # Thêm vào danh sách kết quả có điểm
            scored_results.append(result_copy)
        
        # Lọc kết quả có điểm thấp
        min_relevance_score = self.config.get("min_relevance_score", 0.3)
        filtered_results = [r for r in scored_results if r["relevance_score"] >= min_relevance_score]
        
        # Sắp xếp kết quả theo điểm giảm dần
        ranked_results = sorted(filtered_results, key=lambda x: x["final_score"], reverse=True)
        
        return ranked_results

    def _calculate_relevance_score(self, result: Dict[str, Any], query: str) -> float:
        """
        Tính điểm liên quan của kết quả.

        Args:
            result: Kết quả tìm kiếm
            query: Truy vấn tìm kiếm

        Returns:
            Điểm liên quan (0.0 - 1.0)
        """
        # Triển khai phương thức tính điểm liên quan
        # Đây là phiên bản đơn giản, có thể cải thiện trong tương lai
        
        # Mặc định điểm liên quan
        relevance_score = 0.5
        
        # Nếu kết quả đã có điểm liên quan, sử dụng nó
        if "score" in result:
            try:
                relevance_score = float(result["score"])
                # Chuẩn hóa điểm về khoảng 0.0 - 1.0
                relevance_score = max(0.0, min(1.0, relevance_score))
            except (ValueError, TypeError):
                pass
        
        return relevance_score

    def _calculate_domain_trust_score(self, result: Dict[str, Any]) -> float:
        """
        Tính điểm tin cậy của domain.

        Args:
            result: Kết quả tìm kiếm

        Returns:
            Điểm tin cậy (0.0 - 1.0)
        """
        # Triển khai phương thức tính điểm tin cậy của domain
        # Đây là phiên bản đơn giản, có thể cải thiện trong tương lai
        
        # Mặc định điểm tin cậy
        domain_trust_score = 0.5
        
        # Lấy domain từ URL
        url = result.get("url", "")
        domain = self._extract_domain(url)
        
        # Kiểm tra domain có trong danh sách tin cậy không
        trusted_domains = self.config.get("trusted_domains", [])
        untrusted_domains = self.config.get("untrusted_domains", [])
        
        # Tính điểm tin cậy
        if domain:
            # Kiểm tra domain có trong danh sách tin cậy không
            for trusted_domain in trusted_domains:
                if domain.endswith(trusted_domain):
                    domain_trust_score = 1.0
                    break
            
            # Kiểm tra domain có trong danh sách không tin cậy không
            for untrusted_domain in untrusted_domains:
                if domain.endswith(untrusted_domain):
                    domain_trust_score = 0.0
                    break
        
        return domain_trust_score

    def _calculate_content_quality_score(self, result: Dict[str, Any]) -> float:
        """
        Tính điểm chất lượng nội dung.

        Args:
            result: Kết quả tìm kiếm

        Returns:
            Điểm chất lượng (0.0 - 1.0)
        """
        # Triển khai phương thức tính điểm chất lượng nội dung
        # Đây là phiên bản đơn giản, có thể cải thiện trong tương lai
        
        # Mặc định điểm chất lượng
        content_quality_score = 0.5
        
        # Nếu có nội dung, tính điểm dựa trên độ dài
        if "content" in result:
            content = result["content"]
            if content:
                # Tính điểm dựa trên độ dài nội dung
                min_content_length = self.config.get("min_content_length", 1000)
                content_length = len(content)
                
                if content_length >= min_content_length:
                    content_quality_score = 0.7
                
                # Tăng điểm nếu nội dung có cấu trúc
                if self.config.get("boost_structured_content", True):
                    if "<h1>" in content or "<h2>" in content or "<h3>" in content:
                        content_quality_score += 0.1
                    
                    if "<ul>" in content or "<ol>" in content:
                        content_quality_score += 0.1
                    
                    if "<table>" in content:
                        content_quality_score += 0.1
                
                # Giới hạn điểm trong khoảng 0.0 - 1.0
                content_quality_score = max(0.0, min(1.0, content_quality_score))
        
        return content_quality_score

    def _calculate_freshness_score(self, result: Dict[str, Any]) -> float:
        """
        Tính điểm độ mới của kết quả.

        Args:
            result: Kết quả tìm kiếm

        Returns:
            Điểm độ mới (0.0 - 1.0)
        """
        # Triển khai phương thức tính điểm độ mới
        # Đây là phiên bản đơn giản, có thể cải thiện trong tương lai
        
        # Mặc định điểm độ mới
        freshness_score = 0.5
        
        # Nếu có ngày, tính điểm dựa trên ngày
        if "published_date" in result:
            # Tính điểm dựa trên ngày xuất bản
            # Đây là phiên bản đơn giản, có thể cải thiện trong tương lai
            freshness_score = 0.7
        
        return freshness_score

    def _extract_domain(self, url: str) -> str:
        """
        Trích xuất domain từ URL.

        Args:
            url: URL

        Returns:
            Domain
        """
        # Triển khai phương thức trích xuất domain
        # Đây là phiên bản đơn giản, có thể cải thiện trong tương lai
        
        # Mặc định domain
        domain = ""
        
        # Trích xuất domain từ URL
        if url:
            # Loại bỏ protocol
            if "://" in url:
                domain = url.split("://")[1]
            else:
                domain = url
            
            # Lấy phần domain
            if "/" in domain:
                domain = domain.split("/")[0]
            
            # Loại bỏ www.
            if domain.startswith("www."):
                domain = domain[4:]
        
        return domain

    def _remove_duplicates(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Loại bỏ kết quả trùng lặp.

        Args:
            results: Danh sách kết quả tìm kiếm

        Returns:
            Danh sách kết quả không trùng lặp
        """
        # Triển khai phương thức loại bỏ kết quả trùng lặp
        # Đây là phiên bản đơn giản, có thể cải thiện trong tương lai
        
        # Sử dụng URL làm khóa để loại bỏ trùng lặp
        unique_results = []
        seen_urls = set()
        
        for result in results:
            url = result.get("url", "")
            if url and url not in seen_urls:
                seen_urls.add(url)
                unique_results.append(result)
        
        return unique_results

    def _remove_similar(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Loại bỏ kết quả tương tự.

        Args:
            results: Danh sách kết quả tìm kiếm

        Returns:
            Danh sách kết quả không tương tự
        """
        # Triển khai phương thức loại bỏ kết quả tương tự
        # Đây là phiên bản đơn giản, có thể cải thiện trong tương lai
        
        # Sử dụng domain làm khóa để loại bỏ tương tự
        filtered_results = []
        seen_domains = {}
        similarity_threshold = self.config.get("similarity_threshold", 0.8)
        
        for result in results:
            url = result.get("url", "")
            domain = self._extract_domain(url)
            
            # Nếu domain chưa xuất hiện, thêm vào kết quả
            if domain not in seen_domains:
                seen_domains[domain] = result
                filtered_results.append(result)
            else:
                # Nếu domain đã xuất hiện, so sánh điểm
                existing_result = seen_domains[domain]
                if result.get("final_score", 0) > existing_result.get("final_score", 0) * similarity_threshold:
                    # Thay thế kết quả cũ bằng kết quả mới có điểm cao hơn
                    filtered_results.remove(existing_result)
                    filtered_results.append(result)
                    seen_domains[domain] = result
        
        return filtered_results
