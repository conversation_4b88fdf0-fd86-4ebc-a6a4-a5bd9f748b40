"""
Visualization routes for the Deep Research Core API.

This module provides API endpoints for generating and serving visualizations
for ToT-RAG reasoning processes.
"""

import os
import json
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, Body, HTTPException, Query, BackgroundTasks
from pydantic import BaseModel

from deep_research_core.utils.structured_logging import get_logger
from deep_research_core.visualization.tot_rag_visualizer import ToTRAGVisualizer
from deep_research_core.visualization.interactive_visualizer import InteractiveVisualizer

# Create a logger
logger = get_logger(__name__)

# Create API router
visualization_router = APIRouter(
    prefix="/visualization",
    tags=["visualization"],
    responses={404: {"description": "Visualization not found"}},
)

# Create models for request validation
class ToTRAGResult(BaseModel):
    """Model for ToT-RAG result data."""
    query: str
    best_paths: List[tuple]
    retrieved_documents: Optional[List[Dict[str, Any]]] = None
    conflicts: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None

class VisualizationRequest(BaseModel):
    """Model for visualization request."""
    result: ToTRAGResult
    filename_prefix: Optional[str] = None
    max_label_length: Optional[int] = 50
    max_docs: Optional[int] = 10

class VisualizationResponse(BaseModel):
    """Model for visualization response."""
    html_url: str
    image_urls: List[str]
    message: str

# Visualization endpoints
@visualization_router.post("/generate", response_model=VisualizationResponse)
async def generate_visualization(request: VisualizationRequest = Body(...)):
    """
    Generate visualizations for ToT-RAG result.
    
    Generates both static images and interactive HTML visualizations.
    
    Args:
        request: The visualization request containing ToT-RAG result
        
    Returns:
        URLs to the generated visualizations
    """
    try:
        # Convert Pydantic model to dict
        result_dict = request.result.dict()
        
        # Initialize visualizers
        static_viz = ToTRAGVisualizer(output_dir="static/visualizations")
        interactive_viz = InteractiveVisualizer(output_dir="static/visualizations")
        
        # Generate static visualizations
        image_paths = static_viz.create_comprehensive_visualization(
            result_dict,
            filename_prefix=request.filename_prefix,
            show=False
        )
        
        # Generate interactive dashboard
        html_path = interactive_viz.create_interactive_dashboard(
            result_dict,
            filename=f"{request.filename_prefix}_dashboard.html" if request.filename_prefix else None
        )
        
        # Convert paths to URLs
        base_url = "/static/visualizations/"
        html_url = base_url + os.path.basename(html_path)
        image_urls = [base_url + os.path.basename(path) for path in image_paths]
        
        logger.info(f"Generated {len(image_urls)} visualizations")
        
        return {
            "html_url": html_url,
            "image_urls": image_urls,
            "message": "Visualizations generated successfully"
        }
    
    except Exception as e:
        logger.error(f"Error generating visualizations: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error generating visualizations: {str(e)}"
        )

@visualization_router.get("/tree-data")
async def get_tree_data(result_id: str = Query(..., description="ID of the ToT-RAG result")):
    """
    Get tree visualization data for a specific ToT-RAG result.
    
    This endpoint returns the data structure needed to render a tree visualization
    in the frontend using libraries like D3.js.
    
    Args:
        result_id: ID of the ToT-RAG result to visualize
        
    Returns:
        Tree data structure for visualization
    """
    try:
        # In a real implementation, this would fetch the result from a database
        # Here we'll just return a dummy response for now
        tree_data = {
            "name": "Query Root",
            "children": [
                {"name": "Path 1", "children": [{"name": "Step 1"}, {"name": "Step 2"}]},
                {"name": "Path 2", "children": [{"name": "Step 1"}, {"name": "Step 2"}]}
            ]
        }
        
        return tree_data
        
    except Exception as e:
        logger.error(f"Error getting tree data: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting tree data: {str(e)}"
        ) 