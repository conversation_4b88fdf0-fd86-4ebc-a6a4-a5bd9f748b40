#!/usr/bin/env python3
"""
FastAPI backend for Deep Research Core.

This module provides a REST API for the Deep Research Core system.
"""

import os
import sys
import time
import logging
import json
from typing import Dict, Any, List, Optional

from fastapi import FastAPI, HTTPException, Depends, Request, BackgroundTasks
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, Field, ConfigDict

# Import routes
from .routes.visualization import visualization_router

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Import the necessary components - temporarily commented out
# try:
#     from ..models.api.openrouter import OpenRouterAPI
#     from ..models.api.anthropic import AnthropicAPI
#     from ..models.api.openai import OpenAIAPI
# except ImportError as e:
#     logger.error(f"Error importing API modules: {str(e)}")
#     sys.exit(1)

# Create FastAPI app
app = FastAPI(
    title="Deep Research Core API",
    description="API for Deep Research Core system with Chain of Thought (CoT) and Retrieval-Augmented Generation (RAG) capabilities.",
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    swagger_ui_parameters={"defaultModelsExpandDepth": -1, "persistAuthorization": True},
    contact={
        "name": "Quan Nguyen",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT License",
    }
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Define request models
class Document(BaseModel):
    """Document model for RAG."""
    content: str = Field(..., description="The content of the document")
    source: str = Field(..., description="The source of the document")
    title: Optional[str] = Field(None, description="The title of the document")
    date: Optional[str] = Field(None, description="The date of the document")
    id: Optional[int] = Field(None, description="The ID of the document")

    model_config = ConfigDict(
        json_schema_extra = {
            "example": {
                "content": "Quantization is a technique to reduce the precision of weights in AI models to reduce size and increase inference speed.",
                "source": "AI Model Optimization Guide",
                "title": "Quantization Techniques",
                "date": "2023-05-15"
            }
        }
    )

class GenerateRequest(BaseModel):
    """Request model for text generation."""
    prompt: str = Field(..., description="The prompt to generate text from")
    model: str = Field("moonshotai/moonlight-16b-a3b-instruct:free", description="The model to use for generation")
    provider: str = Field("openrouter", description="The provider to use (openrouter, anthropic, openai)")
    temperature: float = Field(0.7, description="Sampling temperature (0.0 to 1.0)")
    max_tokens: int = Field(1000, description="Maximum number of tokens to generate")
    system_prompt: Optional[str] = Field(None, description="Optional system prompt to use")
    stream: bool = Field(False, description="Whether to stream the response")

    model_config = ConfigDict(
        json_schema_extra = {
            "example": {
                "prompt": "Explain the concept of quantization in AI models.",
                "model": "moonshotai/moonlight-16b-a3b-instruct:free",
                "provider": "openrouter",
                "temperature": 0.7,
                "max_tokens": 1000
            }
        }
    )

class RAGRequest(BaseModel):
    """Request model for RAG."""
    query: str = Field(..., description="The query to process using RAG")
    documents: List[Document] = Field(..., description="List of documents to use for retrieval")
    model: str = Field("moonshotai/moonlight-16b-a3b-instruct:free", description="The model to use for generation")
    provider: str = Field("openrouter", description="The provider to use (openrouter, anthropic, openai)")
    temperature: float = Field(0.7, description="Sampling temperature (0.0 to 1.0)")
    max_tokens: int = Field(1000, description="Maximum number of tokens to generate")
    top_k: int = Field(3, description="Number of documents to retrieve")
    system_prompt: Optional[str] = Field(None, description="Optional system prompt to use")

    model_config = ConfigDict(
        json_schema_extra = {
            "example": {
                "query": "What is quantization in AI models?",
                "documents": [
                    {
                        "content": "Quantization is a technique to reduce the precision of weights in AI models to reduce size and increase inference speed.",
                        "source": "AI Model Optimization Guide",
                        "title": "Quantization Techniques"
                    }
                ],
                "model": "moonshotai/moonlight-16b-a3b-instruct:free",
                "provider": "openrouter"
            }
        }
    )

class CoTRequest(BaseModel):
    """Request model for Chain of Thought."""
    prompt: str = Field(..., description="The prompt to process using Chain of Thought")
    model: str = Field("moonshotai/moonlight-16b-a3b-instruct:free", description="The model to use for generation")
    provider: str = Field("openrouter", description="The provider to use (openrouter, anthropic, openai)")
    temperature: float = Field(0.7, description="Sampling temperature (0.0 to 1.0)")
    max_tokens: int = Field(1000, description="Maximum number of tokens to generate")
    system_prompt: Optional[str] = Field(None, description="Optional system prompt to use")

    model_config = ConfigDict(
        json_schema_extra = {
            "example": {
                "prompt": "Explain how to solve this math problem: If a train travels at 60 mph for 3 hours, how far does it go?",
                "model": "moonshotai/moonlight-16b-a3b-instruct:free",
                "provider": "openrouter"
            }
        }
    )

class CoTRAGRequest(BaseModel):
    """Request model for Chain of Thought with RAG."""
    query: str = Field(..., description="The query to process using Chain of Thought with RAG")
    documents: List[Document] = Field(..., description="List of documents to use for retrieval")
    model: str = Field("moonshotai/moonlight-16b-a3b-instruct:free", description="The model to use for generation")
    provider: str = Field("openrouter", description="The provider to use (openrouter, anthropic, openai)")
    temperature: float = Field(0.7, description="Sampling temperature (0.0 to 1.0)")
    max_tokens: int = Field(1000, description="Maximum number of tokens to generate")
    top_k: int = Field(3, description="Number of documents to retrieve")
    system_prompt: Optional[str] = Field(None, description="Optional system prompt to use")
    language: str = Field("en", description="Language to use ('en' for English, 'vi' for Vietnamese)")

    model_config = ConfigDict(
        json_schema_extra = {
            "example": {
                "query": "What are the benefits of quantization in AI models?",
                "documents": [
                    {
                        "content": "Quantization is a technique to reduce the precision of weights in AI models to reduce size and increase inference speed.",
                        "source": "AI Model Optimization Guide",
                        "title": "Quantization Techniques"
                    }
                ],
                "model": "moonshotai/moonlight-16b-a3b-instruct:free",
                "provider": "openrouter",
                "language": "en"
            }
        }
    )

# Define response models
class GenerateResponse(BaseModel):
    """Response model for text generation."""
    text: str
    model: str
    provider: str
    generation_time: float

    model_config = ConfigDict(
        json_schema_extra = {
            "example": {
                "text": "Quantization in AI models is a technique that reduces the precision of weights to decrease model size and increase inference speed. This is achieved by converting high-precision floating-point numbers (like float32) to lower-precision formats (like int8 or float16).",
                "model": "moonshotai/moonlight-16b-a3b-instruct:free",
                "provider": "openrouter",
                "generation_time": 0.85
            }
        }
    )

class RAGResponse(BaseModel):
    """Response model for RAG."""
    query: str
    answer: str
    documents: List[Document]
    model: str
    provider: str
    generation_time: float

    model_config = ConfigDict(
        json_schema_extra = {
            "example": {
                "query": "What is quantization in AI models?",
                "answer": "Quantization in AI models is a technique that reduces the precision of weights to decrease model size and increase inference speed. This is achieved by converting high-precision floating-point numbers to lower-precision formats.",
                "documents": [
                    {
                        "content": "Quantization is a technique to reduce the precision of weights in AI models to reduce size and increase inference speed.",
                        "source": "AI Model Optimization Guide",
                        "title": "Quantization Techniques"
                    }
                ],
                "model": "moonshotai/moonlight-16b-a3b-instruct:free",
                "provider": "openrouter",
                "generation_time": 0.95
            }
        }
    )

class CoTResponse(BaseModel):
    """Response model for Chain of Thought."""
    prompt: str
    answer: str
    model: str
    provider: str
    generation_time: float

    model_config = ConfigDict(
        json_schema_extra = {
            "example": {
                "prompt": "Explain how to solve this math problem: If a train travels at 60 mph for 3 hours, how far does it go?",
                "answer": "To solve this problem, I need to find the distance traveled by the train.\n\nFirst, I'll identify what I know:\n- The train's speed is 60 miles per hour (mph)\n- The travel time is 3 hours\n\nNext, I'll use the formula: Distance = Speed × Time\n\nSubstituting the values:\nDistance = 60 mph × 3 hours = 180 miles\n\nTherefore, if a train travels at 60 mph for 3 hours, it will go 180 miles.",
                "model": "moonshotai/moonlight-16b-a3b-instruct:free",
                "provider": "openrouter",
                "generation_time": 0.75
            }
        }
    )

class CoTRAGResponse(BaseModel):
    """Response model for Chain of Thought with RAG."""
    query: str
    answer: str
    documents: List[Document]
    model: str
    provider: str
    generation_time: float
    language: str

    model_config = ConfigDict(
        json_schema_extra = {
            "example": {
                "query": "What are the benefits of quantization in AI models?",
                "answer": "Let me analyze the benefits of quantization in AI models based on the provided information.\n\nFirst, I'll examine what quantization means in the context of AI models. According to the retrieved document, quantization is a technique to reduce the precision of weights in AI models.\n\nThe main benefits of quantization include:\n1. Reduced model size - By using lower precision for weights (e.g., converting from 32-bit to 8-bit), the overall size of the model decreases significantly\n2. Increased inference speed - With smaller weights, computations can be performed faster\n3. Lower memory requirements - Quantized models require less RAM during execution\n4. Energy efficiency - Particularly important for edge devices and mobile applications\n\nIn conclusion, quantization offers significant advantages in terms of model efficiency while maintaining acceptable accuracy for many applications.",
                "documents": [
                    {
                        "content": "Quantization is a technique to reduce the precision of weights in AI models to reduce size and increase inference speed.",
                        "source": "AI Model Optimization Guide",
                        "title": "Quantization Techniques"
                    }
                ],
                "model": "moonshotai/moonlight-16b-a3b-instruct:free",
                "provider": "openrouter",
                "generation_time": 1.35,
                "language": "en"
            }
        }
    )

# API key cache
api_keys = {
    "openrouter": os.environ.get("OPENROUTER_API_KEY", ""),
    "anthropic": os.environ.get("ANTHROPIC_API_KEY", ""),
    "openai": os.environ.get("OPENAI_API_KEY", ""),
}

# API client cache
api_clients = {}

def get_api_client(provider: str):
    """Get API client for the specified provider."""
    if provider in api_clients:
        return api_clients[provider]

    # Temporarily commented out
    # if provider == "openrouter":
    #     if not api_keys["openrouter"]:
    #         raise HTTPException(status_code=400, detail="OpenRouter API key not set")
    #     client = OpenRouterAPI(api_key=api_keys["openrouter"])
    # elif provider == "anthropic":
    #     if not api_keys["anthropic"]:
    #         raise HTTPException(status_code=400, detail="Anthropic API key not set")
    #     client = AnthropicAPI(api_key=api_keys["anthropic"])
    # elif provider == "openai":
    #     if not api_keys["openai"]:
    #         raise HTTPException(status_code=400, detail="OpenAI API key not set")
    #     client = OpenAIAPI(api_key=api_keys["openai"])
    # else:
    #     raise HTTPException(status_code=400, detail=f"Unsupported provider: {provider}")

    # Temporary placeholder
    client = None

    api_clients[provider] = client
    return client

# Define API tags
tags_metadata = [
    {"name": "General", "description": "General endpoints"},
    {"name": "AI", "description": "AI endpoints for text generation, RAG, and CoT"},
    {"name": "Configuration", "description": "Configuration endpoints"}
]

@app.get("/", tags=["General"])
async def root():
    """Root endpoint."""
    return {"message": "Welcome to Deep Research Core API"}

@app.get("/api/providers", tags=["Configuration"])
async def get_providers():
    """Get available providers.

    Returns a list of configured providers with valid API keys.
    """
    providers = []

    if api_keys["openrouter"]:
        providers.append("openrouter")
    if api_keys["anthropic"]:
        providers.append("anthropic")
    if api_keys["openai"]:
        providers.append("openai")

    return {"providers": providers}

@app.post("/api/generate", response_model=GenerateResponse, tags=["AI"])
async def generate(request: GenerateRequest):
    """Generate text using the specified provider and model.

    This endpoint generates text based on the provided prompt using the specified model and provider.
    """
    try:
        client = get_api_client(request.provider)

        start_time = time.time()
        response = client.generate(
            prompt=request.prompt,
            model=request.model,
            system_prompt=request.system_prompt,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
            stream=request.stream
        )
        end_time = time.time()

        return {
            "text": response,
            "model": request.model,
            "provider": request.provider,
            "generation_time": end_time - start_time
        }
    except Exception as e:
        logger.error(f"Error generating text: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/rag", response_model=RAGResponse, tags=["AI"])
async def rag(request: RAGRequest):
    """Process a query using Retrieval-Augmented Generation (RAG).

    This endpoint processes a query using RAG with the provided documents.
    """
    try:
        client = get_api_client(request.provider)

        # Format context
        context = ""
        for i, doc in enumerate(request.documents):
            context += f"Document {i+1}:\n"
            if doc.title:
                context += f"Title: {doc.title}\n"
            context += f"Source: {doc.source}\n"
            context += f"Content: {doc.content}\n\n"

        # Create prompt
        system_prompt = request.system_prompt or """You are a helpful AI assistant that uses retrieved information to answer questions accurately.
        Always base your answers on the provided documents. If the documents don't contain the information needed,
        acknowledge this limitation."""

        user_prompt = f"""Question: {request.query}

Retrieved Information:
{context}

Please answer the question based on the retrieved information."""

        # Generate answer
        start_time = time.time()
        response = client.generate(
            prompt=user_prompt,
            model=request.model,
            system_prompt=system_prompt,
            temperature=request.temperature,
            max_tokens=request.max_tokens
        )
        end_time = time.time()

        return {
            "query": request.query,
            "answer": response,
            "documents": request.documents,
            "model": request.model,
            "provider": request.provider,
            "generation_time": end_time - start_time
        }
    except Exception as e:
        logger.error(f"Error processing RAG: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/cot", response_model=CoTResponse, tags=["AI"])
async def cot(request: CoTRequest):
    """Process a prompt using Chain of Thought (CoT) reasoning.

    This endpoint processes a prompt using Chain of Thought reasoning to provide step-by-step explanations.
    """
    try:
        client = get_api_client(request.provider)

        # Create system prompt for CoT
        system_prompt = request.system_prompt or """You are a helpful AI assistant that uses Chain of Thought reasoning.
        When answering questions, break down your thinking step by step.
        Use transitions like "First", "Next", "Then", "Additionally", "Finally" to connect your thoughts.
        Make sure your reasoning is clear and logical."""

        # Generate answer
        start_time = time.time()
        response = client.generate(
            prompt=request.prompt,
            model=request.model,
            system_prompt=system_prompt,
            temperature=request.temperature,
            max_tokens=request.max_tokens
        )
        end_time = time.time()

        return {
            "prompt": request.prompt,
            "answer": response,
            "model": request.model,
            "provider": request.provider,
            "generation_time": end_time - start_time
        }
    except Exception as e:
        logger.error(f"Error processing CoT: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/cot-rag", response_model=CoTRAGResponse, tags=["AI"])
async def cot_rag(request: CoTRAGRequest):
    """Process a query using Chain of Thought (CoT) with Retrieval-Augmented Generation (RAG).

    This endpoint combines Chain of Thought reasoning with RAG to provide step-by-step explanations
    based on the retrieved documents. Supports both English and Vietnamese languages.
    """
    try:
        client = get_api_client(request.provider)

        # Format context
        context = ""
        for i, doc in enumerate(request.documents):
            if request.language == "vi":
                context += f"Tài liệu {i+1}:\n"
                if doc.title:
                    context += f"Tiêu đề: {doc.title}\n"
                context += f"Nguồn: {doc.source}\n"
                context += f"Nội dung: {doc.content}\n\n"
            else:
                context += f"Document {i+1}:\n"
                if doc.title:
                    context += f"Title: {doc.title}\n"
                context += f"Source: {doc.source}\n"
                context += f"Content: {doc.content}\n\n"

        # Create prompt with CoT instructions
        if request.language == "vi":
            system_prompt = request.system_prompt or """Bạn là một trợ lý AI hữu ích sử dụng phương pháp Chuỗi Suy Luận (Chain of Thought) kết hợp với thông tin được truy xuất để trả lời câu hỏi một cách chính xác.

            Đầu tiên, phân tích các tài liệu được truy xuất để xác định thông tin liên quan.
            Sau đó, chia nhỏ suy nghĩ của bạn từng bước một, sử dụng các từ chuyển tiếp tự nhiên như "Đầu tiên", "Tiếp theo", "Sau đó", "Ngoài ra", "Cuối cùng".
            Đảm bảo lập luận của bạn rõ ràng, hợp lý và dựa trên các tài liệu được cung cấp.

            Nếu các tài liệu không chứa thông tin cần thiết, hãy thừa nhận giới hạn này và sử dụng kiến thức chung của bạn,
            nhưng hãy chỉ rõ khi bạn đang làm như vậy."""

            user_prompt = f"""Câu hỏi: {request.query}

Thông tin được truy xuất:
{context}

Vui lòng trả lời câu hỏi sử dụng phương pháp Chuỗi Suy Luận dựa trên thông tin được truy xuất.
Chia nhỏ suy nghĩ của bạn từng bước một trước khi đưa ra câu trả lời cuối cùng."""
        else:
            system_prompt = request.system_prompt or """You are a helpful AI assistant that uses Chain of Thought reasoning combined with retrieved information to answer questions accurately.

            First, analyze the retrieved documents to identify relevant information.
            Then, break down your thinking step by step using natural transitions like "First", "Next", "Then", "Additionally", "Finally".
            Make sure your reasoning is clear, logical, and based on the provided documents.

            If the documents don't contain the information needed, acknowledge this limitation and use your general knowledge,
            but clearly indicate when you are doing so."""

            user_prompt = f"""Question: {request.query}

Retrieved Information:
{context}

Please answer the question using Chain of Thought reasoning based on the retrieved information.
Break down your thinking step by step before providing the final answer."""

        # Generate answer
        start_time = time.time()
        response = client.generate(
            prompt=user_prompt,
            model=request.model,
            system_prompt=system_prompt,
            temperature=request.temperature,
            max_tokens=request.max_tokens
        )
        end_time = time.time()

        return {
            "query": request.query,
            "answer": response,
            "documents": request.documents,
            "model": request.model,
            "provider": request.provider,
            "generation_time": end_time - start_time,
            "language": request.language
        }
    except Exception as e:
        logger.error(f"Error processing CoT-RAG: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/set-api-key", tags=["Configuration"])
async def set_api_key(provider: str, api_key: str):
    """Set API key for the specified provider.

    This endpoint allows setting the API key for a specific provider (openrouter, anthropic, openai).
    """
    if provider not in ["openrouter", "anthropic", "openai"]:
        raise HTTPException(status_code=400, detail=f"Unsupported provider: {provider}")

    api_keys[provider] = api_key

    # Clear client cache
    if provider in api_clients:
        del api_clients[provider]

    return {"message": f"{provider} API key set successfully"}

@app.get("/api/health", tags=["General"])
async def health_check():
    """Health check endpoint.

    Returns the current status of the API.
    """
    return {"status": "ok"}

# Add a unified endpoint that connects to all core AI components
class UnifiedAIRequest(BaseModel):
    """Request model for unified AI processing."""
    query: str = Field(..., description="The query or prompt to process")
    mode: str = Field("cotrag", description="Processing mode: 'generate', 'rag', 'cot', or 'cotrag'")
    documents: Optional[List[Document]] = Field(None, description="List of documents for RAG/CoTRAG modes")
    model: str = Field("moonshotai/moonlight-16b-a3b-instruct:free", description="The model to use for generation")
    provider: str = Field("openrouter", description="The provider to use (openrouter, anthropic, openai)")
    temperature: float = Field(0.7, description="Sampling temperature (0.0 to 1.0)")
    max_tokens: int = Field(1000, description="Maximum number of tokens to generate")
    top_k: int = Field(3, description="Number of documents to retrieve (for RAG/CoTRAG)")
    system_prompt: Optional[str] = Field(None, description="Optional system prompt to use")
    language: str = Field("en", description="Language to use ('en' for English, 'vi' for Vietnamese)")

    model_config = ConfigDict(
        json_schema_extra = {
            "example": {
                "query": "What are the benefits of quantization in AI models?",
                "mode": "cotrag",
                "documents": [
                    {
                        "content": "Quantization is a technique to reduce the precision of weights in AI models to reduce size and increase inference speed.",
                        "source": "AI Model Optimization Guide",
                        "title": "Quantization Techniques"
                    }
                ],
                "model": "moonshotai/moonlight-16b-a3b-instruct:free",
                "provider": "openrouter",
                "language": "en"
            }
        }
    )

class UnifiedAIResponse(BaseModel):
    """Response model for unified AI processing."""
    query: str = Field(..., description="The original query or prompt")
    answer: str = Field(..., description="The generated answer")
    mode: str = Field(..., description="The processing mode that was used")
    documents: Optional[List[Document]] = Field(None, description="The documents used (for RAG/CoTRAG)")
    model: str = Field(..., description="The model that was used")
    provider: str = Field(..., description="The provider that was used")
    generation_time: float = Field(..., description="Time taken to generate the answer in seconds")
    language: Optional[str] = Field(None, description="The language used (for CoTRAG)")

    model_config = ConfigDict(
        json_schema_extra = {
            "example": {
                "query": "What are the benefits of quantization in AI models?",
                "answer": "Quantization in AI models offers several benefits: 1) Reduced model size, 2) Faster inference speed, 3) Lower memory requirements, and 4) Energy efficiency for edge devices.",
                "mode": "cotrag",
                "documents": [
                    {
                        "content": "Quantization is a technique to reduce the precision of weights in AI models to reduce size and increase inference speed.",
                        "source": "AI Model Optimization Guide",
                        "title": "Quantization Techniques"
                    }
                ],
                "model": "moonshotai/moonlight-16b-a3b-instruct:free",
                "provider": "openrouter",
                "generation_time": 1.25,
                "language": "en"
            }
        }
    )

@app.post("/api/unified", response_model=UnifiedAIResponse, tags=["AI"])
async def unified_ai(request: UnifiedAIRequest):
    """Process a query using the specified AI mode.

    This endpoint provides a unified interface to all AI capabilities:
    - 'generate': Simple text generation
    - 'rag': Retrieval-Augmented Generation
    - 'cot': Chain of Thought reasoning
    - 'cotrag': Chain of Thought with RAG

    The response format is consistent across all modes for easier integration.
    """
    try:
        start_time = time.time()

        # Process based on mode
        if request.mode == "generate":
            # Convert to GenerateRequest
            gen_request = GenerateRequest(
                prompt=request.query,
                model=request.model,
                provider=request.provider,
                temperature=request.temperature,
                max_tokens=request.max_tokens,
                system_prompt=request.system_prompt
            )
            response = await generate(gen_request)
            answer = response.text
            documents = None
            language = None

        elif request.mode == "rag":
            # Check if documents are provided
            if not request.documents:
                raise HTTPException(status_code=400, detail="Documents are required for RAG mode")

            # Convert to RAGRequest
            rag_request = RAGRequest(
                query=request.query,
                documents=request.documents,
                model=request.model,
                provider=request.provider,
                temperature=request.temperature,
                max_tokens=request.max_tokens,
                top_k=request.top_k,
                system_prompt=request.system_prompt
            )
            response = await rag(rag_request)
            answer = response.answer
            documents = response.documents
            language = None

        elif request.mode == "cot":
            # Convert to CoTRequest
            cot_request = CoTRequest(
                prompt=request.query,
                model=request.model,
                provider=request.provider,
                temperature=request.temperature,
                max_tokens=request.max_tokens,
                system_prompt=request.system_prompt
            )
            response = await cot(cot_request)
            answer = response.answer
            documents = None
            language = None

        elif request.mode == "cotrag":
            # Check if documents are provided
            if not request.documents:
                raise HTTPException(status_code=400, detail="Documents are required for CoTRAG mode")

            # Convert to CoTRAGRequest
            cotrag_request = CoTRAGRequest(
                query=request.query,
                documents=request.documents,
                model=request.model,
                provider=request.provider,
                temperature=request.temperature,
                max_tokens=request.max_tokens,
                top_k=request.top_k,
                system_prompt=request.system_prompt,
                language=request.language
            )
            response = await cot_rag(cotrag_request)
            answer = response.answer
            documents = response.documents
            language = response.language

        else:
            raise HTTPException(status_code=400, detail=f"Unsupported mode: {request.mode}")

        end_time = time.time()
        generation_time = end_time - start_time

        # Return unified response
        return {
            "query": request.query,
            "answer": answer,
            "mode": request.mode,
            "documents": documents,
            "model": request.model,
            "provider": request.provider,
            "generation_time": generation_time,
            "language": language
        }
    except Exception as e:
        logger.error(f"Error in unified AI processing: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Include routers
app.include_router(visualization_router)

# Mount static files directory for visualizations
os.makedirs("static/visualizations", exist_ok=True)
app.mount("/static", StaticFiles(directory="static"), name="static")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
