"""
API module for Deep Research Core.

This module provides API capabilities for Deep Research Core.
"""

from .app import app
from .main import app as main_app

# Import routes
try:
    from .routes import (
        auth_router,
        documents_router,
        reasoning_router,
        admin_router
    )
except ImportError:
    # Routes might not be available yet
    pass

# Import models
try:
    from .models import (
        Document,
        Query,
        User,
        LoginRequest,
        TokenResponse
    )
except ImportError:
    # Models might not be available yet
    pass

__all__ = [
    'app',
    'main_app',
    'auth_router',
    'documents_router',
    'reasoning_router',
    'admin_router',
    'Document',
    'Query',
    'User',
    'LoginRequest',
    'TokenResponse'
]
