"""
API server for Deep Research Core.
"""

import os
import json
from typing import Dict, Any, List, Optional

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Header, Query, Body, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

# Temporarily commented out until modules are implemented
# from ..reasoning import BaseRAG, SQLiteVectorRAG, FAISSRAG
# from ..security import <PERSON><PERSON><PERSON>cation<PERSON>ana<PERSON>, AuthorizationManager, Permission
from ..utils.structured_logging import get_logger
# from ..config.monitoring_config import initialize_monitoring, shutdown_monitoring
# from ..utils.external_monitoring import get_prometheus_exporter

# Initialize monitoring - Temporarily commented out
# initialize_monitoring()

# Create logger
logger = get_logger(__name__)

# Initialize Prometheus exporter - Temporarily commented out
# prometheus_port = int(os.environ.get("PROMETHEUS_PORT", 9090))
# prometheus_exporter = get_prometheus_exporter(port=prometheus_port)
# prometheus_exporter.start()
# logger.info(f"Started Prometheus exporter on port {prometheus_port}")

# Create FastAPI app
app = FastAPI(
    title="Deep Research Core API",
    description="API for Deep Research Core",
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize security - Temporarily commented out
# auth_manager = AuthenticationManager(users_file=os.environ.get("USERS_FILE", "users.json"))
# authz_manager = AuthorizationManager()

# Initialize RAG - Temporarily commented out
# try:
#     # Try to initialize RAG based on environment variable
#     rag_type = os.environ.get("RAG_TYPE", "sqlite").lower()
#
#     if rag_type == "milvus":
#         try:
#             from ..reasoning import MilvusRAG
#             rag = MilvusRAG(
#                 provider=os.environ.get("PROVIDER", "openai"),
#                 model=os.environ.get("MODEL", "gpt-4o"),
#                 temperature=float(os.environ.get("TEMPERATURE", "0.7")),
#                 max_tokens=int(os.environ.get("MAX_TOKENS", "2000")),
#                 embedding_model=os.environ.get("EMBEDDING_MODEL", "text-embedding-ada-002"),
#                 collection_name=os.environ.get("MILVUS_COLLECTION", "deep_research"),
#                 connection_args={
#                     "host": os.environ.get("MILVUS_HOST", "localhost"),
#                     "port": os.environ.get("MILVUS_PORT", "19530")
#                 },
#                 embedding_dim=int(os.environ.get("EMBEDDING_DIM", "1536")),
#                 top_k=int(os.environ.get("TOP_K", "5"))
#             )
#             logger.info("Initialized MilvusRAG")
#         except Exception as e:
#             logger.warning(f"Failed to initialize MilvusRAG: {str(e)}. Falling back to SQLiteVectorRAG.")
#             rag_type = "sqlite"
#
#     if rag_type == "faiss":
#         try:
#             rag = FAISSRAG(
#                 provider=os.environ.get("PROVIDER", "openai"),
#                 model=os.environ.get("MODEL", "gpt-4o"),
#                 temperature=float(os.environ.get("TEMPERATURE", "0.7")),
#                 max_tokens=int(os.environ.get("MAX_TOKENS", "2000")),
#                 embedding_model=os.environ.get("EMBEDDING_MODEL", "text-embedding-ada-002"),
#                 index_path=os.environ.get("FAISS_INDEX_PATH", "faiss_index"),
#                 embedding_dim=int(os.environ.get("EMBEDDING_DIM", "1536")),
#                 index_type=os.environ.get("FAISS_INDEX_TYPE", "Flat"),
#                 metric_type=os.environ.get("FAISS_METRIC_TYPE", "ip"),
#                 use_gpu=os.environ.get("FAISS_USE_GPU", "False").lower() == "true",
#                 top_k=int(os.environ.get("TOP_K", "5"))
#             )
#             logger.info("Initialized FAISSRAG")
#         except Exception as e:
#             logger.warning(f"Failed to initialize FAISSRAG: {str(e)}. Falling back to SQLiteVectorRAG.")
#             rag_type = "sqlite"
#
#     if rag_type == "pinecone":
#         try:
#             from ..reasoning import PineconeRAG
#             rag = PineconeRAG(
#                 provider=os.environ.get("PROVIDER", "openai"),
#                 model=os.environ.get("MODEL", "gpt-4o"),
#                 temperature=float(os.environ.get("TEMPERATURE", "0.7")),
#                 max_tokens=int(os.environ.get("MAX_TOKENS", "2000")),
#                 embedding_model=os.environ.get("EMBEDDING_MODEL", "text-embedding-ada-002"),
#                 api_key=os.environ.get("PINECONE_API_KEY", ""),
#                 environment=os.environ.get("PINECONE_ENVIRONMENT", ""),
#                 index_name=os.environ.get("PINECONE_INDEX", "deep-research"),
#                 namespace=os.environ.get("PINECONE_NAMESPACE", ""),
#                 dimension=int(os.environ.get("EMBEDDING_DIM", "1536")),
#                 top_k=int(os.environ.get("TOP_K", "5"))
#             )
#             logger.info("Initialized PineconeRAG")
#         except Exception as e:
#             logger.warning(f"Failed to initialize PineconeRAG: {str(e)}. Falling back to SQLiteVectorRAG.")
#             rag_type = "sqlite"
#
#     if rag_type == "weaviate":
#         try:
#             from ..reasoning import WeaviateRAG
#             rag = WeaviateRAG(
#                 provider=os.environ.get("PROVIDER", "openai"),
#                 model=os.environ.get("MODEL", "gpt-4o"),
#                 temperature=float(os.environ.get("TEMPERATURE", "0.7")),
#                 max_tokens=int(os.environ.get("MAX_TOKENS", "2000")),
#                 embedding_model=os.environ.get("EMBEDDING_MODEL", "text-embedding-ada-002"),
#                 url=os.environ.get("WEAVIATE_URL", "http://localhost:8080"),
#                 api_key=os.environ.get("WEAVIATE_API_KEY", None),
#                 class_name=os.environ.get("WEAVIATE_CLASS", "Document"),
#                 top_k=int(os.environ.get("TOP_K", "5"))
#             )
#             logger.info("Initialized WeaviateRAG")
#         except Exception as e:
#             logger.warning(f"Failed to initialize WeaviateRAG: {str(e)}. Falling back to SQLiteVectorRAG.")
#             rag_type = "sqlite"
#
#     # Default to SQLiteVectorRAG
#     if rag_type == "sqlite":
#         rag = SQLiteVectorRAG(
#             db_path=os.environ.get("DB_PATH", "deep_research.db"),
#             provider=os.environ.get("PROVIDER", "openai"),
#             model=os.environ.get("MODEL", "gpt-4o"),
#             temperature=float(os.environ.get("TEMPERATURE", "0.7")),
#             max_tokens=int(os.environ.get("MAX_TOKENS", "2000")),
#             embedding_model=os.environ.get("EMBEDDING_MODEL", "text-embedding-ada-002"),
#             top_k=int(os.environ.get("TOP_K", "5"))
#         )
#         logger.info("Initialized SQLiteVectorRAG")
#
# except Exception as e:
#     logger.error(f"Failed to initialize RAG: {str(e)}")
#     raise

# Create default admin user if not exists - Temporarily commented out
# if not auth_manager.get_user("admin"):
#     auth_manager.create_user(
#         username="admin",
#         password=os.environ.get("ADMIN_PASSWORD", "admin"),
#         roles=["admin"],
#         metadata={"description": "Default administrator"}
#     )
#     logger.info("Created default admin user")


# Models
class Document(BaseModel):
    """Document model."""

    content: str
    source: str
    title: Optional[str] = None
    date: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class Query(BaseModel):
    """Query model."""

    query: str
    top_k: Optional[int] = None
    filter_expr: Optional[str] = None


class User(BaseModel):
    """User model."""

    username: str
    password: str
    roles: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None


class LoginRequest(BaseModel):
    """Login request model."""

    username: str
    password: str


# Authentication dependency - Temporarily commented out
# async def get_current_user(
#     authorization: Optional[str] = Header(None),
#     api_key: Optional[str] = Header(None)
# ) -> Dict[str, Any]:
#     """
#     Get the current user from the authorization header or API key.
#
#     Args:
#         authorization: Authorization header
#         api_key: API key header
#
#     Returns:
#         User information
#
#     Raises:
#         HTTPException: If authentication fails
#     """
#     # Check API key
#     if api_key:
#         user = auth_manager.authenticate_api_key(api_key)
#         if user:
#             return user
#
#     # Check JWT token
#     if authorization and authorization.startswith("Bearer "):
#         token = authorization.split(" ")[1]
#         user = auth_manager.authenticate_token(token)
#         if user:
#             return user
#
#     raise HTTPException(
#         status_code=status.HTTP_401_UNAUTHORIZED,
#         detail="Invalid authentication credentials",
#         headers={"WWW-Authenticate": "Bearer"}
#     )


# Routes
@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "Deep Research Core API"}


# @app.post("/auth/login")
# async def login(request: LoginRequest):
#     """
#     Login endpoint.
#
#     Args:
#         request: Login request
#
#     Returns:
#         Authentication result
#     """
#     user = auth_manager.authenticate_password(request.username, request.password)
#     if not user:
#         raise HTTPException(
#             status_code=status.HTTP_401_UNAUTHORIZED,
#             detail="Invalid username or password",
#             headers={"WWW-Authenticate": "Bearer"}
#         )
#
#     # prometheus_exporter.record_request("login", "success")
#
#     return user


# @app.post("/auth/api-key")
# async def create_api_key(current_user: Dict[str, Any] = Depends(get_current_user)):
#     """
#     Create API key endpoint.
#
#     Args:
#         current_user: Current user
#
#     Returns:
#         API key
#     """
#     if not authz_manager.has_permission(current_user["roles"], Permission.USER_WRITE):
#         raise HTTPException(
#             status_code=status.HTTP_403_FORBIDDEN,
#             detail="Not enough permissions"
#         )
#
#     api_key = auth_manager.create_api_key(current_user["username"])
#     if not api_key:
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail="Failed to create API key"
#         )
#
#     # prometheus_exporter.record_request("create_api_key", "success")
#
#     return {"api_key": api_key}


# @app.post("/documents")
# async def add_documents(
#     documents: List[Document],
#     current_user: Dict[str, Any] = Depends(get_current_user)
# ):
#     """
#     Add documents endpoint.
#
#     Args:
#         documents: List of documents
#         current_user: Current user
#
#     Returns:
#         Document IDs
#     """
#     if not authz_manager.has_permission(current_user["roles"], Permission.RAG_WRITE):
#         raise HTTPException(
#             status_code=status.HTTP_403_FORBIDDEN,
#             detail="Not enough permissions"
#         )
#
#     try:
#         # Convert documents to dictionaries
#         doc_dicts = [doc.model_dump() for doc in documents]
#
#         # Add documents
#         doc_ids = rag.add_documents(doc_dicts)
#
#         # prometheus_exporter.record_request("add_documents", "success")
#         # prometheus_exporter.record_document_operation("add", rag.__class__.__name__, len(doc_ids))
#         # prometheus_exporter.record_document_count(rag.__class__.__name__, rag.count())
#
#         return {"document_ids": doc_ids}
#     except Exception as e:
#         logger.error(f"Failed to add documents: {str(e)}")
#         # prometheus_exporter.record_request("add_documents", "error")
#         # prometheus_exporter.record_error("add_documents", str(e))
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"Failed to add documents: {str(e)}"
#         )


# @app.post("/search")
# async def search(
#     query: Query,
#     current_user: Dict[str, Any] = Depends(get_current_user)
# ):
#     """
#     Search endpoint.
#
#     Args:
#         query: Query
#         current_user: Current user
#
#     Returns:
#         Search results
#     """
#     if not authz_manager.has_permission(current_user["roles"], Permission.RAG_SEARCH):
#         raise HTTPException(
#             status_code=status.HTTP_403_FORBIDDEN,
#             detail="Not enough permissions"
#         )
#
#     try:
#         # Search
#         results = rag.search(query.query, query.top_k, query.filter_expr)
#
#         # prometheus_exporter.record_request("search", "success")
#
#         return {"results": results}
#     except Exception as e:
#         logger.error(f"Failed to search: {str(e)}")
#         # prometheus_exporter.record_request("search", "error")
#         # prometheus_exporter.record_error("search", str(e))
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"Failed to search: {str(e)}"
#         )


# @app.post("/process")
# async def process(
#     query: Query,
#     current_user: Dict[str, Any] = Depends(get_current_user)
# ):
#     """
#     Process endpoint.
#
#     Args:
#         query: Query
#         current_user: Current user
#
#     Returns:
#         Process result
#     """
#     if not authz_manager.has_permission(current_user["roles"], Permission.RAG_PROCESS):
#         raise HTTPException(
#             status_code=status.HTTP_403_FORBIDDEN,
#             detail="Not enough permissions"
#         )
#
#     try:
#         # Process
#         result = rag.process(query.query, query.filter_expr)
#
#         # prometheus_exporter.record_request("process", "success")
#
#         return result
#     except Exception as e:
#         logger.error(f"Failed to process: {str(e)}")
#         # prometheus_exporter.record_request("process", "error")
#         # prometheus_exporter.record_error("process", str(e))
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"Failed to process: {str(e)}"
#         )


# @app.delete("/documents")
# async def clear_documents(
#     current_user: Dict[str, Any] = Depends(get_current_user)
# ):
#     """
#     Clear documents endpoint.
#
#     Args:
#         current_user: Current user
#
#     Returns:
#         Success message
#     """
#     if not authz_manager.has_permission(current_user["roles"], Permission.RAG_DELETE):
#         raise HTTPException(
#             status_code=status.HTTP_403_FORBIDDEN,
#             detail="Not enough permissions"
#         )
#
#     try:
#         # Clear
#         rag.clear()
#
#         # prometheus_exporter.record_request("clear", "success")
#         # prometheus_exporter.record_document_operation("clear", rag.__class__.__name__)
#         # prometheus_exporter.record_document_count(rag.__class__.__name__, 0)
#
#         return {"message": "Documents cleared"}
#     except Exception as e:
#         logger.error(f"Failed to clear documents: {str(e)}")
#         # prometheus_exporter.record_request("clear", "error")
#         # prometheus_exporter.record_error("clear", str(e))
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"Failed to clear documents: {str(e)}"
#         )


# @app.get("/documents/count")
# async def count_documents(
#     current_user: Dict[str, Any] = Depends(get_current_user)
# ):
#     """
#     Count documents endpoint.
#
#     Args:
#         current_user: Current user
#
#     Returns:
#         Document count
#     """
#     if not authz_manager.has_permission(current_user["roles"], Permission.RAG_READ):
#         raise HTTPException(
#             status_code=status.HTTP_403_FORBIDDEN,
#             detail="Not enough permissions"
#         )
#
#     try:
#         # Count
#         count = rag.count()
#
#         # prometheus_exporter.record_request("count", "success")
#
#         return {"count": count}
#     except Exception as e:
#         logger.error(f"Failed to count documents: {str(e)}")
#         # prometheus_exporter.record_request("count", "error")
#         # prometheus_exporter.record_error("count", str(e))
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"Failed to count documents: {str(e)}"
#         )


# @app.post("/users")
# async def create_user(
#     user: User,
#     current_user: Dict[str, Any] = Depends(get_current_user)
# ):
#     """
#     Create user endpoint.
#
#     Args:
#         user: User
#         current_user: Current user
#
#     Returns:
#         Success message
#     """
#     if not authz_manager.has_permission(current_user["roles"], Permission.USER_WRITE):
#         raise HTTPException(
#             status_code=status.HTTP_403_FORBIDDEN,
#             detail="Not enough permissions"
#         )
#
#     try:
#         # Create user
#         new_user = auth_manager.create_user(
#             username=user.username,
#             password=user.password,
#             roles=user.roles,
#             metadata=user.metadata
#         )
#
#         if not new_user:
#             raise HTTPException(
#                 status_code=status.HTTP_400_BAD_REQUEST,
#                 detail=f"User {user.username} already exists"
#             )
#
#         # prometheus_exporter.record_request("create_user", "success")
#
#         return {"message": f"User {user.username} created"}
#     except Exception as e:
#         logger.error(f"Failed to create user: {str(e)}")
#         # prometheus_exporter.record_request("create_user", "error")
#         # prometheus_exporter.record_error("create_user", str(e))
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"Failed to create user: {str(e)}"
#         )


# @app.get("/users")
# async def list_users(
#     current_user: Dict[str, Any] = Depends(get_current_user)
# ):
#     """
#     List users endpoint.
#
#     Args:
#         current_user: Current user
#
#     Returns:
#         List of users
#     """
#     if not authz_manager.has_permission(current_user["roles"], Permission.USER_READ):
#         raise HTTPException(
#             status_code=status.HTTP_403_FORBIDDEN,
#             detail="Not enough permissions"
#         )
#
#     try:
#         # List users
#         users = auth_manager.list_users()
#
#         # prometheus_exporter.record_request("list_users", "success")
#
#         return {"users": users}
#     except Exception as e:
#         logger.error(f"Failed to list users: {str(e)}")
#         # prometheus_exporter.record_request("list_users", "error")
#         # prometheus_exporter.record_error("list_users", str(e))
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"Failed to list users: {str(e)}"
#         )


# @app.delete("/users/{username}")
# async def delete_user(
#     username: str,
#     current_user: Dict[str, Any] = Depends(get_current_user)
# ):
#     """
#     Delete user endpoint.
#
#     Args:
#         username: Username
#         current_user: Current user
#
#     Returns:
#         Success message
#     """
#     if not authz_manager.has_permission(current_user["roles"], Permission.USER_DELETE):
#         raise HTTPException(
#             status_code=status.HTTP_403_FORBIDDEN,
#             detail="Not enough permissions"
#         )
#
#     try:
#         # Delete user
#         deleted = auth_manager.delete_user(username)
#
#         if not deleted:
#             raise HTTPException(
#                 status_code=status.HTTP_404_NOT_FOUND,
#                 detail=f"User {username} not found"
#             )
#
#         # prometheus_exporter.record_request("delete_user", "success")
#
#         return {"message": f"User {username} deleted"}
#     except Exception as e:
#         logger.error(f"Failed to delete user: {str(e)}")
#         # prometheus_exporter.record_request("delete_user", "error")
#         # prometheus_exporter.record_error("delete_user", str(e))
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"Failed to delete user: {str(e)}"
#         )


@app.get("/metrics")
async def metrics():
    """Metrics endpoint."""
    # This endpoint is handled by the Prometheus middleware
    pass


# @app.on_event("shutdown")
# async def shutdown_event():
#     """Shutdown event handler."""
#     # Close RAG
#     rag.close()
#
#     # Stop Prometheus exporter
#     # prometheus_exporter.stop()
#
#     # Shutdown monitoring
#     # shutdown_monitoring()
#
#     logger.info("API server shutdown")


if __name__ == "__main__":
    import uvicorn

    # Run server
    uvicorn.run(
        "src.deep_research_core.api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
