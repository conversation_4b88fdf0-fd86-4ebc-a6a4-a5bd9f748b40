#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module cung cấp các tính năng xử lý ngôn ngữ tự nhiên cho WebSearchAgentLocal.

Module này cung cấp các phương thức để phân tích cú pháp, phân tích ngữ nghĩa,
phân tích ý định, và phân tích thực thể trong các truy vấn tìm kiếm.
"""

import os
import re
import logging
import json
from typing import Dict, List, Any, Optional, Union, Tuple, Set
from collections import Counter, defaultdict

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

class NLPEngine:
    """
    Lớp cung cấp các tính năng xử lý ngôn ngữ tự nhiên.
    """

    def __init__(self, **kwargs):
        """
        Khởi tạo NLPEngine.

        Args:
            **kwargs: <PERSON><PERSON><PERSON> tham số tùy chọn
                - language: Ngôn ngữ mặc định (default: "auto")
                - use_spacy: Có sử dụng spaCy hay không (default: True)
                - use_nltk: Có sử dụng NLTK hay không (default: True)
                - use_transformers: Có sử dụng Transformers hay không (default: False)
                - spacy_model: Mô hình spaCy (default: "en_core_web_sm")
                - cache_dir: Thư mục lưu trữ cache (default: None)
                - cache_size: Kích thước cache (default: 1000)
                - verbose: Có hiển thị thông tin chi tiết hay không (default: False)
        """
        # Thiết lập các tham số
        self.language = kwargs.get("language", "auto")
        self.use_spacy = kwargs.get("use_spacy", True)
        self.use_nltk = kwargs.get("use_nltk", True)
        self.use_transformers = kwargs.get("use_transformers", False)
        self.spacy_model = kwargs.get("spacy_model", "en_core_web_sm")
        self.cache_dir = kwargs.get("cache_dir", None)
        self.cache_size = kwargs.get("cache_size", 1000)
        self.verbose = kwargs.get("verbose", False)

        # Khởi tạo cache
        self.cache = {}

        # Khởi tạo các thư viện NLP
        self._init_nlp_libraries()

        # Tải dữ liệu bổ sung
        self._load_additional_data()

        if self.verbose:
            logger.info(f"NLPEngine đã được khởi tạo với language={self.language}")

    def _init_nlp_libraries(self):
        """Khởi tạo các thư viện NLP."""
        # Khởi tạo spaCy
        self.spacy_nlp = None
        if self.use_spacy:
            try:
                import spacy
                try:
                    self.spacy_nlp = spacy.load(self.spacy_model)
                    if self.verbose:
                        logger.info(f"Đã tải mô hình spaCy: {self.spacy_model}")
                except OSError:
                    if self.verbose:
                        logger.warning(f"Không tìm thấy mô hình spaCy: {self.spacy_model}. Đang tải...")
                    try:
                        spacy.cli.download(self.spacy_model)
                        self.spacy_nlp = spacy.load(self.spacy_model)
                        if self.verbose:
                            logger.info(f"Đã tải mô hình spaCy: {self.spacy_model}")
                    except Exception as e:
                        logger.error(f"Lỗi khi tải mô hình spaCy: {str(e)}")
                        self.spacy_nlp = None
            except ImportError:
                if self.verbose:
                    logger.warning("spaCy không khả dụng. Một số tính năng NLP sẽ bị vô hiệu hóa.")

        # Khởi tạo NLTK
        self.nltk_initialized = False
        if self.use_nltk:
            try:
                import nltk
                try:
                    nltk.data.find('tokenizers/punkt')
                except LookupError:
                    if self.verbose:
                        logger.warning("Đang tải dữ liệu NLTK...")
                    nltk.download('punkt')
                    nltk.download('stopwords')
                    nltk.download('wordnet')
                    nltk.download('averaged_perceptron_tagger')
                    nltk.download('maxent_ne_chunker')
                    nltk.download('words')
                self.nltk_initialized = True
                if self.verbose:
                    logger.info("Đã khởi tạo NLTK thành công")
            except ImportError:
                if self.verbose:
                    logger.warning("NLTK không khả dụng. Một số tính năng NLP sẽ bị vô hiệu hóa.")

        # Khởi tạo Transformers
        self.transformers_model = None
        if self.use_transformers:
            try:
                from transformers import pipeline
                self.transformers_model = pipeline("feature-extraction")
                if self.verbose:
                    logger.info("Đã khởi tạo Transformers thành công")
            except ImportError:
                if self.verbose:
                    logger.warning("Transformers không khả dụng. Một số tính năng NLP sẽ bị vô hiệu hóa.")

    def _load_additional_data(self):
        """Tải dữ liệu bổ sung."""
        # Tải danh sách từ dừng (stopwords)
        self.stopwords = set()
        if self.use_nltk and self.nltk_initialized:
            try:
                from nltk.corpus import stopwords
                self.stopwords = set(stopwords.words('english'))
                if self.verbose:
                    logger.info(f"Đã tải {len(self.stopwords)} từ dừng tiếng Anh")
            except:
                if self.verbose:
                    logger.warning("Không thể tải từ dừng từ NLTK")

        # Tải danh sách từ dừng tiếng Việt
        self.vietnamese_stopwords = set()
        vietnamese_stopwords_path = os.path.join(os.path.dirname(__file__), "data", "vietnamese_stopwords.txt")
        if os.path.exists(vietnamese_stopwords_path):
            try:
                with open(vietnamese_stopwords_path, "r", encoding="utf-8") as f:
                    self.vietnamese_stopwords = set(line.strip() for line in f)
                if self.verbose:
                    logger.info(f"Đã tải {len(self.vietnamese_stopwords)} từ dừng tiếng Việt")
            except:
                if self.verbose:
                    logger.warning("Không thể tải từ dừng tiếng Việt")

        # Tải danh sách từ đồng nghĩa
        self.synonyms = {}
        synonyms_path = os.path.join(os.path.dirname(__file__), "data", "synonyms.json")
        if os.path.exists(synonyms_path):
            try:
                with open(synonyms_path, "r", encoding="utf-8") as f:
                    self.synonyms = json.load(f)
                if self.verbose:
                    logger.info(f"Đã tải {len(self.synonyms)} từ đồng nghĩa")
            except:
                if self.verbose:
                    logger.warning("Không thể tải từ đồng nghĩa")

    def detect_language(self, text: str) -> str:
        """
        Phát hiện ngôn ngữ của văn bản.

        Args:
            text: Văn bản cần phát hiện ngôn ngữ

        Returns:
            str: Mã ngôn ngữ (ví dụ: "en", "vi", "fr", v.v.)
        """
        if not text:
            return "en"

        # Kiểm tra cache
        cache_key = f"detect_language:{text[:100]}"
        if cache_key in self.cache:
            return self.cache[cache_key]

        # Phát hiện ngôn ngữ bằng spaCy
        if self.spacy_nlp is not None:
            try:
                from spacy.language import Language
                from spacy_langdetect import LanguageDetector

                if not Language.has_factory("language_detector"):
                    Language.factory("language_detector", func=lambda nlp, name: LanguageDetector())
                    self.spacy_nlp.add_pipe("language_detector", last=True)

                doc = self.spacy_nlp(text[:500])
                language = doc._.language.get("language")
                self.cache[cache_key] = language
                return language
            except:
                if self.verbose:
                    logger.warning("Không thể phát hiện ngôn ngữ bằng spaCy")

        # Phát hiện ngôn ngữ bằng langdetect
        try:
            from langdetect import detect
            language = detect(text)
            self.cache[cache_key] = language
            return language
        except:
            if self.verbose:
                logger.warning("Không thể phát hiện ngôn ngữ bằng langdetect")

        # Phát hiện ngôn ngữ bằng phương pháp đơn giản
        vietnamese_chars = set("àáảãạăằắẳẵặâầấẩẫậèéẻẽẹêềếểễệìíỉĩịòóỏõọôồốổỗộơờớởỡợùúủũụưừứửữựỳýỷỹỵđ")
        text_lower = text.lower()

        # Kiểm tra tiếng Việt
        if any(c in vietnamese_chars for c in text_lower):
            self.cache[cache_key] = "vi"
            return "vi"

        # Mặc định là tiếng Anh
        self.cache[cache_key] = "en"
        return "en"

    def tokenize(self, text: str, language: str = None) -> List[str]:
        """
        Tách từ trong văn bản.

        Args:
            text: Văn bản cần tách từ
            language: Ngôn ngữ của văn bản (nếu None, sẽ tự động phát hiện)

        Returns:
            List[str]: Danh sách các từ
        """
        if not text:
            return []

        # Xác định ngôn ngữ nếu không được cung cấp
        if language is None or language == "auto":
            language = self.detect_language(text)

        # Kiểm tra cache
        cache_key = f"tokenize:{language}:{text[:100]}"
        if cache_key in self.cache:
            return self.cache[cache_key]

        # Tách từ bằng spaCy
        if self.spacy_nlp is not None:
            doc = self.spacy_nlp(text)
            tokens = [token.text for token in doc]
            self.cache[cache_key] = tokens
            return tokens

        # Tách từ bằng NLTK
        if self.nltk_initialized:
            try:
                from nltk.tokenize import word_tokenize
                tokens = word_tokenize(text)
                self.cache[cache_key] = tokens
                return tokens
            except:
                if self.verbose:
                    logger.warning("Không thể tách từ bằng NLTK")

        # Tách từ bằng phương pháp đơn giản
        tokens = re.findall(r'\b\w+\b', text)
        self.cache[cache_key] = tokens
        return tokens

    def parse_syntax(self, text: str, language: str = None) -> Dict[str, Any]:
        """
        Phân tích cú pháp của văn bản.

        Args:
            text: Văn bản cần phân tích
            language: Ngôn ngữ của văn bản (nếu None, sẽ tự động phát hiện)

        Returns:
            Dict[str, Any]: Kết quả phân tích cú pháp
        """
        if not text:
            return {"tokens": [], "pos_tags": [], "dependencies": []}

        # Xác định ngôn ngữ nếu không được cung cấp
        if language is None or language == "auto":
            language = self.detect_language(text)

        # Kiểm tra cache
        cache_key = f"parse_syntax:{language}:{text[:100]}"
        if cache_key in self.cache:
            return self.cache[cache_key]

        # Phân tích cú pháp bằng spaCy
        if self.spacy_nlp is not None:
            doc = self.spacy_nlp(text)
            result = {
                "tokens": [token.text for token in doc],
                "pos_tags": [(token.text, token.pos_) for token in doc],
                "dependencies": [(token.text, token.dep_, token.head.text) for token in doc],
                "noun_chunks": [chunk.text for chunk in doc.noun_chunks],
                "sentences": [sent.text for sent in doc.sents]
            }
            self.cache[cache_key] = result
            return result

        # Phân tích cú pháp bằng NLTK
        if self.nltk_initialized:
            try:
                from nltk import pos_tag, word_tokenize, sent_tokenize
                from nltk.chunk import ne_chunk

                tokens = word_tokenize(text)
                pos_tags = pos_tag(tokens)
                sentences = sent_tokenize(text)

                result = {
                    "tokens": tokens,
                    "pos_tags": pos_tags,
                    "dependencies": [],
                    "noun_chunks": [],
                    "sentences": sentences
                }
                self.cache[cache_key] = result
                return result
            except:
                if self.verbose:
                    logger.warning("Không thể phân tích cú pháp bằng NLTK")

        # Phân tích cú pháp đơn giản
        tokens = self.tokenize(text, language)
        sentences = re.split(r'[.!?]+', text)
        sentences = [s.strip() for s in sentences if s.strip()]

        result = {
            "tokens": tokens,
            "pos_tags": [],
            "dependencies": [],
            "noun_chunks": [],
            "sentences": sentences
        }
        self.cache[cache_key] = result
        return result

    def parse_semantics(self, text: str, language: str = None) -> Dict[str, Any]:
        """
        Phân tích ngữ nghĩa của văn bản.

        Args:
            text: Văn bản cần phân tích
            language: Ngôn ngữ của văn bản (nếu None, sẽ tự động phát hiện)

        Returns:
            Dict[str, Any]: Kết quả phân tích ngữ nghĩa
        """
        if not text:
            return {"keywords": [], "topics": [], "sentiment": "neutral", "entities": []}

        # Xác định ngôn ngữ nếu không được cung cấp
        if language is None or language == "auto":
            language = self.detect_language(text)

        # Kiểm tra cache
        cache_key = f"parse_semantics:{language}:{text[:100]}"
        if cache_key in self.cache:
            return self.cache[cache_key]

        # Phân tích ngữ nghĩa bằng spaCy
        if self.spacy_nlp is not None:
            doc = self.spacy_nlp(text)

            # Trích xuất từ khóa
            keywords = []
            for token in doc:
                if not token.is_stop and not token.is_punct and token.pos_ in ["NOUN", "PROPN", "ADJ", "VERB"]:
                    keywords.append(token.text)

            # Trích xuất thực thể
            entities = [(ent.text, ent.label_) for ent in doc.ents]

            # Phân tích tình cảm đơn giản
            sentiment = "neutral"

            result = {
                "keywords": keywords,
                "topics": [],
                "sentiment": sentiment,
                "entities": entities
            }
            self.cache[cache_key] = result
            return result

        # Phân tích ngữ nghĩa bằng NLTK
        if self.nltk_initialized:
            try:
                from nltk import pos_tag, word_tokenize, ne_chunk
                from nltk.corpus import stopwords

                tokens = word_tokenize(text)
                filtered_tokens = [token for token in tokens if token.lower() not in self.stopwords]
                pos_tags = pos_tag(filtered_tokens)

                # Trích xuất từ khóa
                keywords = [token for token, pos in pos_tags if pos in ["NN", "NNS", "NNP", "NNPS", "JJ", "VB", "VBD", "VBG", "VBN", "VBP", "VBZ"]]

                # Trích xuất thực thể
                named_entities = ne_chunk(pos_tags)
                entities = []
                for chunk in named_entities:
                    if hasattr(chunk, 'label'):
                        entities.append((' '.join(c[0] for c in chunk), chunk.label()))

                result = {
                    "keywords": keywords,
                    "topics": [],
                    "sentiment": "neutral",
                    "entities": entities
                }
                self.cache[cache_key] = result
                return result
            except:
                if self.verbose:
                    logger.warning("Không thể phân tích ngữ nghĩa bằng NLTK")

        # Phân tích ngữ nghĩa đơn giản
        tokens = self.tokenize(text, language)

        # Loại bỏ từ dừng
        stopwords_set = self.vietnamese_stopwords if language == "vi" else self.stopwords
        filtered_tokens = [token for token in tokens if token.lower() not in stopwords_set]

        # Đếm tần suất từ
        counter = Counter(filtered_tokens)
        keywords = [token for token, count in counter.most_common(10)]

        result = {
            "keywords": keywords,
            "topics": [],
            "sentiment": "neutral",
            "entities": []
        }
        self.cache[cache_key] = result
        return result

    def analyze_intent(self, text: str, language: str = None) -> Dict[str, Any]:
        """
        Phân tích ý định của truy vấn.

        Args:
            text: Truy vấn cần phân tích
            language: Ngôn ngữ của truy vấn (nếu None, sẽ tự động phát hiện)

        Returns:
            Dict[str, Any]: Kết quả phân tích ý định
        """
        if not text:
            return {"intent": "unknown", "confidence": 0.0, "parameters": {}}

        # Xác định ngôn ngữ nếu không được cung cấp
        if language is None or language == "auto":
            language = self.detect_language(text)

        # Kiểm tra cache
        cache_key = f"analyze_intent:{language}:{text}"
        if cache_key in self.cache:
            return self.cache[cache_key]

        # Phân tích cú pháp
        syntax_result = self.parse_syntax(text, language)

        # Phân tích ngữ nghĩa
        semantics_result = self.parse_semantics(text, language)

        # Phân tích ý định dựa trên từ khóa
        intent = "unknown"
        confidence = 0.0
        parameters = {}

        # Các từ khóa cho các ý định
        intent_keywords = {
            "search": ["search", "find", "look for", "tìm", "kiếm", "tra cứu"],
            "compare": ["compare", "versus", "vs", "difference", "so sánh", "khác nhau"],
            "define": ["define", "what is", "meaning", "định nghĩa", "là gì", "nghĩa là"],
            "how_to": ["how to", "how do I", "steps to", "cách", "làm sao", "làm thế nào"],
            "when": ["when", "date", "time", "khi nào", "lúc nào", "thời gian"],
            "where": ["where", "location", "place", "ở đâu", "nơi nào", "địa điểm"],
            "who": ["who", "person", "ai", "người nào"],
            "why": ["why", "reason", "tại sao", "vì sao", "lý do"],
            "list": ["list", "top", "best", "danh sách", "liệt kê", "tốt nhất"]
        }

        # Kiểm tra từng ý định
        text_lower = text.lower()
        max_score = 0
        for intent_name, keywords in intent_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text_lower)
            if score > max_score:
                max_score = score
                intent = intent_name
                confidence = min(1.0, score / 3)  # Giới hạn confidence trong khoảng [0, 1]

        # Trích xuất tham số
        if intent != "unknown":
            # Trích xuất thực thể làm tham số
            for entity, entity_type in semantics_result.get("entities", []):
                if entity_type in ["PERSON", "ORG", "GPE", "LOC", "PRODUCT"]:
                    parameters[entity_type.lower()] = entity

            # Trích xuất từ khóa làm tham số
            parameters["keywords"] = semantics_result.get("keywords", [])[:5]

        result = {
            "intent": intent,
            "confidence": confidence,
            "parameters": parameters
        }
        self.cache[cache_key] = result
        return result

    def recognize_entities(self, text: str, language: str = None) -> List[Dict[str, Any]]:
        """
        Nhận dạng các thực thể trong văn bản.

        Args:
            text: Văn bản cần nhận dạng thực thể
            language: Ngôn ngữ của văn bản (nếu None, sẽ tự động phát hiện)

        Returns:
            List[Dict[str, Any]]: Danh sách các thực thể được nhận dạng
        """
        if not text:
            return []

        # Xác định ngôn ngữ nếu không được cung cấp
        if language is None or language == "auto":
            language = self.detect_language(text)

        # Kiểm tra cache
        cache_key = f"recognize_entities:{language}:{text[:100]}"
        if cache_key in self.cache:
            return self.cache[cache_key]

        # Nhận dạng thực thể bằng spaCy
        if self.spacy_nlp is not None:
            doc = self.spacy_nlp(text)
            entities = []
            for ent in doc.ents:
                entities.append({
                    "text": ent.text,
                    "type": ent.label_,
                    "start": ent.start_char,
                    "end": ent.end_char
                })
            self.cache[cache_key] = entities
            return entities

        # Nhận dạng thực thể bằng NLTK
        if self.nltk_initialized:
            try:
                from nltk import pos_tag, word_tokenize, ne_chunk
                from nltk.tree import Tree

                tokens = word_tokenize(text)
                pos_tags = pos_tag(tokens)
                named_entities = ne_chunk(pos_tags)

                entities = []
                for i, chunk in enumerate(named_entities):
                    if isinstance(chunk, Tree):
                        entity_text = ' '.join(c[0] for c in chunk.leaves())
                        entity_type = chunk.label()
                        # Tính toán vị trí gần đúng
                        start = text.find(entity_text)
                        end = start + len(entity_text) if start != -1 else -1

                        entities.append({
                            "text": entity_text,
                            "type": entity_type,
                            "start": start,
                            "end": end
                        })

                self.cache[cache_key] = entities
                return entities
            except:
                if self.verbose:
                    logger.warning("Không thể nhận dạng thực thể bằng NLTK")

        # Nhận dạng thực thể đơn giản
        entities = []

        # Nhận dạng ngày tháng
        date_patterns = [
            r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}',  # DD/MM/YYYY hoặc MM/DD/YYYY
            r'\d{1,2}\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{2,4}',  # DD Month YYYY
        ]

        for pattern in date_patterns:
            for match in re.finditer(pattern, text):
                entities.append({
                    "text": match.group(),
                    "type": "DATE",
                    "start": match.start(),
                    "end": match.end()
                })

        # Nhận dạng số điện thoại
        phone_patterns = [
            r'\+\d{1,3}\s*\d{3,4}[-\s]?\d{3,4}[-\s]?\d{3,4}',  # +84 123 456 789
            r'\(\d{3,4}\)\s*\d{3,4}[-\s]?\d{3,4}',  # (123) 456 789
            r'\d{3,4}[-\s]?\d{3,4}[-\s]?\d{3,4}'  # 123 456 789
        ]

        for pattern in phone_patterns:
            for match in re.finditer(pattern, text):
                entities.append({
                    "text": match.group(),
                    "type": "PHONE",
                    "start": match.start(),
                    "end": match.end()
                })

        # Nhận dạng email
        email_pattern = r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
        for match in re.finditer(email_pattern, text):
            entities.append({
                "text": match.group(),
                "type": "EMAIL",
                "start": match.start(),
                "end": match.end()
            })

        # Nhận dạng URL
        url_pattern = r'https?://[^\s]+'
        for match in re.finditer(url_pattern, text):
            entities.append({
                "text": match.group(),
                "type": "URL",
                "start": match.start(),
                "end": match.end()
            })

        self.cache[cache_key] = entities
        return entities
