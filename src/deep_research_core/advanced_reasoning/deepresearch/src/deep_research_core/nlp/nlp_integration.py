#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module tích hợp NLPEngine với WebSearchAgentLocal.

Module này cung cấp các phương thức để tích hợp NLPEngine với WebSearchAgentLocal,
gi<PERSON><PERSON> cải thiện chất lượng tìm kiếm bằng cách sử dụng các tính năng xử lý ngôn ngữ tự nhiên.
"""

import os
import logging
from typing import Dict, List, Any, Optional, Union, Tuple, Set
import json

from .nlp_engine import NLPEngine

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

def integrate_nlp_engine(agent, **kwargs):
    """
    Tích hợp NLPEngine với WebSearchAgentLocal.

    Args:
        agent: WebSearchAgentLocal instance
        **kwargs: <PERSON><PERSON><PERSON> tham số tùy chọn cho NLPEngine
    """
    try:
        # Khởi tạo NLPEngine
        agent.nlp_engine = NLPEngine(**kwargs)
        agent.use_nlp = True
        
        # Thêm các phương thức vào agent
        agent.parse_syntax = lambda text, language=None: agent.nlp_engine.parse_syntax(text, language)
        agent.parse_semantics = lambda text, language=None: agent.nlp_engine.parse_semantics(text, language)
        agent.analyze_intent = lambda text, language=None: agent.nlp_engine.analyze_intent(text, language)
        agent.recognize_entities = lambda text, language=None: agent.nlp_engine.recognize_entities(text, language)
        agent.detect_language = lambda text: agent.nlp_engine.detect_language(text)
        
        # Ghi log
        if kwargs.get("verbose", False):
            logger.info("Đã tích hợp NLPEngine với WebSearchAgentLocal")
        
        return True
    except Exception as e:
        logger.error(f"Lỗi khi tích hợp NLPEngine: {str(e)}")
        agent.use_nlp = False
        return False

def optimize_query(agent, query: str, **kwargs) -> str:
    """
    Tối ưu hóa truy vấn tìm kiếm bằng cách sử dụng NLPEngine.

    Args:
        agent: WebSearchAgentLocal instance
        query: Truy vấn tìm kiếm
        **kwargs: Các tham số tùy chọn

    Returns:
        str: Truy vấn đã được tối ưu hóa
    """
    if not hasattr(agent, "use_nlp") or not agent.use_nlp:
        return query
    
    try:
        # Phân tích cú pháp
        syntax_result = agent.parse_syntax(query)
        
        # Phân tích ngữ nghĩa
        semantics_result = agent.parse_semantics(query)
        
        # Phân tích ý định
        intent_result = agent.analyze_intent(query)
        
        # Tối ưu hóa truy vấn dựa trên kết quả phân tích
        optimized_query = query
        
        # Thêm từ khóa từ kết quả phân tích ngữ nghĩa
        keywords = semantics_result.get("keywords", [])
        if keywords:
            # Loại bỏ các từ khóa đã có trong truy vấn
            new_keywords = [kw for kw in keywords[:3] if kw.lower() not in query.lower()]
            if new_keywords:
                optimized_query += " " + " ".join(new_keywords)
        
        # Thêm thực thể từ kết quả phân tích ngữ nghĩa
        entities = semantics_result.get("entities", [])
        if entities:
            # Thêm các thực thể quan trọng
            important_entities = [entity for entity, entity_type in entities if entity_type in ["PERSON", "ORG", "GPE", "LOC", "PRODUCT"]]
            if important_entities:
                # Loại bỏ các thực thể đã có trong truy vấn
                new_entities = [entity for entity in important_entities[:2] if entity.lower() not in query.lower()]
                if new_entities:
                    optimized_query += " " + " ".join(new_entities)
        
        # Thêm từ đồng nghĩa cho các từ khóa trong truy vấn
        if hasattr(agent.nlp_engine, "synonyms") and agent.nlp_engine.synonyms:
            for word, synonyms in agent.nlp_engine.synonyms.items():
                if word.lower() in query.lower():
                    # Chọn một từ đồng nghĩa ngẫu nhiên
                    import random
                    synonym = random.choice(synonyms)
                    if synonym.lower() not in query.lower() and synonym.lower() not in optimized_query.lower():
                        optimized_query += f" {synonym}"
                    break
        
        # Ghi log
        if kwargs.get("verbose", False):
            logger.info(f"Truy vấn gốc: {query}")
            logger.info(f"Truy vấn đã tối ưu hóa: {optimized_query}")
        
        return optimized_query
    except Exception as e:
        logger.error(f"Lỗi khi tối ưu hóa truy vấn: {str(e)}")
        return query

def extract_search_parameters(agent, query: str, **kwargs) -> Dict[str, Any]:
    """
    Trích xuất các tham số tìm kiếm từ truy vấn.

    Args:
        agent: WebSearchAgentLocal instance
        query: Truy vấn tìm kiếm
        **kwargs: Các tham số tùy chọn

    Returns:
        Dict[str, Any]: Các tham số tìm kiếm
    """
    if not hasattr(agent, "use_nlp") or not agent.use_nlp:
        return {}
    
    try:
        # Phân tích ý định
        intent_result = agent.analyze_intent(query)
        
        # Trích xuất các tham số từ kết quả phân tích ý định
        parameters = intent_result.get("parameters", {})
        
        # Trích xuất các tham số bổ sung
        search_params = {}
        
        # Xác định loại tìm kiếm dựa trên ý định
        intent = intent_result.get("intent", "unknown")
        if intent == "search":
            search_params["search_type"] = "general"
        elif intent == "compare":
            search_params["search_type"] = "comparison"
        elif intent == "define":
            search_params["search_type"] = "definition"
        elif intent == "how_to":
            search_params["search_type"] = "how_to"
        elif intent == "list":
            search_params["search_type"] = "list"
        else:
            search_params["search_type"] = "general"
        
        # Xác định số lượng kết quả dựa trên độ phức tạp của truy vấn
        query_complexity = _evaluate_query_complexity(agent, query)
        if query_complexity > 0.7:
            search_params["num_results"] = 10
        elif query_complexity > 0.4:
            search_params["num_results"] = 5
        else:
            search_params["num_results"] = 3
        
        # Xác định ngôn ngữ
        language = agent.detect_language(query)
        search_params["language"] = language
        
        # Thêm các tham số từ kết quả phân tích ý định
        search_params.update(parameters)
        
        # Ghi log
        if kwargs.get("verbose", False):
            logger.info(f"Truy vấn: {query}")
            logger.info(f"Các tham số tìm kiếm: {search_params}")
        
        return search_params
    except Exception as e:
        logger.error(f"Lỗi khi trích xuất các tham số tìm kiếm: {str(e)}")
        return {}

def _evaluate_query_complexity(agent, query: str) -> float:
    """
    Đánh giá độ phức tạp của truy vấn.

    Args:
        agent: WebSearchAgentLocal instance
        query: Truy vấn tìm kiếm

    Returns:
        float: Độ phức tạp của truy vấn (0.0 - 1.0)
    """
    try:
        # Phân tích cú pháp
        syntax_result = agent.parse_syntax(query)
        
        # Tính toán độ phức tạp dựa trên số lượng từ
        tokens = syntax_result.get("tokens", [])
        token_count = len(tokens)
        
        # Tính toán độ phức tạp dựa trên số lượng câu
        sentences = syntax_result.get("sentences", [])
        sentence_count = len(sentences)
        
        # Tính toán độ phức tạp dựa trên số lượng thực thể
        entities = agent.recognize_entities(query)
        entity_count = len(entities)
        
        # Tính toán độ phức tạp tổng hợp
        complexity = 0.0
        
        # Độ phức tạp dựa trên số lượng từ
        if token_count <= 3:
            complexity += 0.1
        elif token_count <= 6:
            complexity += 0.3
        elif token_count <= 10:
            complexity += 0.5
        elif token_count <= 15:
            complexity += 0.7
        else:
            complexity += 0.9
        
        # Độ phức tạp dựa trên số lượng câu
        if sentence_count == 1:
            complexity += 0.1
        elif sentence_count == 2:
            complexity += 0.3
        elif sentence_count == 3:
            complexity += 0.5
        else:
            complexity += 0.7
        
        # Độ phức tạp dựa trên số lượng thực thể
        if entity_count == 0:
            complexity += 0.1
        elif entity_count == 1:
            complexity += 0.3
        elif entity_count == 2:
            complexity += 0.5
        else:
            complexity += 0.7
        
        # Tính trung bình
        complexity /= 3.0
        
        return complexity
    except Exception as e:
        logger.error(f"Lỗi khi đánh giá độ phức tạp của truy vấn: {str(e)}")
        return 0.5
