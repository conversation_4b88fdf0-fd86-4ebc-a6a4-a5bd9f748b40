#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module tích hợp VietnameseNLP với WebSearchAgentLocal.

Module này cung cấp các phương thức để tích hợp VietnameseNLP với WebSearchAgentLocal,
gi<PERSON><PERSON> cải thiện chất lư<PERSON>ng tìm kiếm tiếng Việt.
"""

import os
import logging
from typing import Dict, List, Any, Optional, Union, Tuple, Set
import json

# Tạo logger
from ..utils.structured_logging import get_logger
logger = get_logger(__name__)

# Ki<PERSON>m tra xem VietnameseNLP có khả dụng không
try:
    from ..utils.vietnamese_nlp import (
        detect_vietnamese,
        normalize_vietnamese,
        remove_vietnamese_stopwords,
        tokenize_vietnamese,
        pos_tag_vietnamese,
        extract_vietnamese_keywords,
        get_vietnamese_query_variants,
        get_vietnamese_synonyms,
        calculate_vietnamese_similarity
    )
    vietnamese_nlp_available = True
    vietnamese_nlp_fallback_needed = False
except ImportError:
    try:
        # Sử dụng fallback nếu không có VietnameseNLP
        from ..agents.vietnamese_nlp_fallback import (
            detect_vietnamese,
            normalize_vietnamese,
            remove_vietnamese_stopwords,
            tokenize_vietnamese,
            pos_tag_vietnamese,
            extract_vietnamese_keywords,
            get_vietnamese_query_variants,
            get_vietnamese_synonyms,
            calculate_vietnamese_similarity
        )
        vietnamese_nlp_available = True
        vietnamese_nlp_fallback_needed = True
        logger.info("Sử dụng VietnameseNLP Fallback thay vì VietnameseNLP.")
    except ImportError:
        vietnamese_nlp_available = False
        vietnamese_nlp_fallback_needed = False
        logger.warning("VietnameseNLP không khả dụng và không thể sử dụng fallback. Hỗ trợ tiếng Việt sẽ bị hạn chế.")

def integrate_vietnamese_nlp(agent, **kwargs):
    """
    Tích hợp VietnameseNLP với WebSearchAgentLocal.

    Args:
        agent: WebSearchAgentLocal instance
        **kwargs: Các tham số tùy chọn cho VietnameseNLP

    Returns:
        bool: True nếu tích hợp thành công, False nếu không
    """
    if not vietnamese_nlp_available:
        logger.warning("VietnameseNLP không khả dụng. Không thể tích hợp.")
        return False

    try:
        # Thêm các phương thức vào agent
        agent.detect_vietnamese = detect_vietnamese
        agent.normalize_vietnamese = normalize_vietnamese
        agent.remove_vietnamese_stopwords = remove_vietnamese_stopwords
        agent.tokenize_vietnamese = tokenize_vietnamese
        agent.pos_tag_vietnamese = pos_tag_vietnamese
        agent.extract_vietnamese_keywords = extract_vietnamese_keywords
        agent.get_vietnamese_query_variants = get_vietnamese_query_variants
        agent.get_vietnamese_synonyms = get_vietnamese_synonyms
        agent.calculate_vietnamese_similarity = calculate_vietnamese_similarity

        # Thêm phương thức tối ưu hóa truy vấn tiếng Việt
        agent.optimize_vietnamese_query = optimize_vietnamese_query.__get__(agent)

        # Thêm phương thức cải thiện kết quả tiếng Việt
        agent.enhance_vietnamese_results = enhance_vietnamese_results.__get__(agent)

        # Đánh dấu VietnameseNLP đã được tích hợp
        agent.vietnamese_nlp_integrated = True

        # Ghi log
        if kwargs.get("verbose", False):
            logger.info("Đã tích hợp VietnameseNLP với WebSearchAgentLocal")

        return True
    except Exception as e:
        logger.error(f"Lỗi khi tích hợp VietnameseNLP: {str(e)}")
        agent.vietnamese_nlp_integrated = False
        return False

def optimize_vietnamese_query(self, query: str, **kwargs) -> str:
    """
    Tối ưu hóa truy vấn tiếng Việt.

    Args:
        query: Truy vấn tiếng Việt
        **kwargs: Các tham số tùy chọn

    Returns:
        str: Truy vấn đã được tối ưu hóa
    """
    if not hasattr(self, "vietnamese_nlp_integrated") or not self.vietnamese_nlp_integrated:
        return query

    try:
        # Kiểm tra xem truy vấn có phải tiếng Việt không
        if not detect_vietnamese(query):
            return query

        # Chuẩn hóa truy vấn
        normalized_query = normalize_vietnamese(query)

        # Tách từ
        tokens = tokenize_vietnamese(normalized_query)

        # Loại bỏ từ dừng
        filtered_query = remove_vietnamese_stopwords(normalized_query)

        # Trích xuất từ khóa
        keywords = extract_vietnamese_keywords(normalized_query, top_n=5)

        # Lấy từ đồng nghĩa
        synonyms = get_vietnamese_synonyms(normalized_query)

        # Tạo truy vấn tối ưu
        optimized_query = query

        # Thêm từ đồng nghĩa vào truy vấn
        if synonyms:
            # Chỉ thêm từ đồng nghĩa không có trong truy vấn gốc
            new_synonyms = [syn for syn in synonyms[:2] if syn.lower() not in query.lower()]
            if new_synonyms:
                optimized_query += " " + " ".join(new_synonyms)

        return optimized_query
    except Exception as e:
        logger.warning(f"Lỗi khi tối ưu hóa truy vấn tiếng Việt: {str(e)}")
        return query

def enhance_vietnamese_results(self, results: Dict[str, Any], query: str, **kwargs) -> Dict[str, Any]:
    """
    Cải thiện kết quả tìm kiếm tiếng Việt.

    Args:
        results: Kết quả tìm kiếm
        query: Truy vấn tìm kiếm
        **kwargs: Các tham số tùy chọn

    Returns:
        Dict[str, Any]: Kết quả tìm kiếm đã được cải thiện
    """
    if not hasattr(self, "vietnamese_nlp_integrated") or not self.vietnamese_nlp_integrated:
        return results

    try:
        # Kiểm tra xem truy vấn có phải tiếng Việt không
        if not detect_vietnamese(query):
            return results

        # Kiểm tra kết quả
        if not results.get("success") or not results.get("results"):
            return results

        # Chuẩn hóa truy vấn
        normalized_query = normalize_vietnamese(query)

        # Trích xuất từ khóa từ truy vấn
        query_keywords = extract_vietnamese_keywords(normalized_query)

        # Tính điểm tương đồng cho từng kết quả
        for result in results.get("results", []):
            # Lấy tiêu đề và mô tả
            title = result.get("title", "")
            snippet = result.get("snippet", "")

            # Tính điểm tương đồng
            title_score = calculate_vietnamese_similarity(normalized_query, title)
            snippet_score = calculate_vietnamese_similarity(normalized_query, snippet)

            # Tính điểm tổng hợp
            result["vietnamese_relevance"] = (title_score * 0.6 + snippet_score * 0.4)

        # Sắp xếp lại kết quả theo điểm tương đồng
        results["results"] = sorted(
            results.get("results", []),
            key=lambda x: x.get("vietnamese_relevance", 0),
            reverse=True
        )

        # Thêm metadata
        if "metadata" not in results:
            results["metadata"] = {}

        results["metadata"]["vietnamese_query_analysis"] = {
            "is_vietnamese": True,
            "normalized_query": normalized_query,
            "keywords": query_keywords,
        }

        return results
    except Exception as e:
        logger.warning(f"Lỗi khi cải thiện kết quả tiếng Việt: {str(e)}")
        return results
