#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module cung cấp các lớp và phương thức để xử lý cấu trúc trang web.

Module này cung cấp các lớp và phương thức để xử lý cấu trúc trang web,
bao gồm việc phân tích robots.txt, sitemap, và xây dựng cấu trúc trang web.
"""

import re
import time
import logging
import requests
import xml.etree.ElementTree as ET
from urllib.parse import urlparse, urljoin
from typing import Dict, List, Any, Optional, Tuple, Set, Union
from bs4 import BeautifulSoup
import concurrent.futures
from collections import defaultdict

from ..utils.structured_logging import get_logger

# Thiết lập logging
logger = get_logger(__name__)


class SiteStructureHandler:
    """
    Lớp cung cấp các phương thức để xử lý cấu trúc trang web.

    T<PERSON>h năng:
    - <PERSON><PERSON> tích robots.txt
    - <PERSON><PERSON> tích sitemap
    - Xây dựng cấu trúc trang web
    - Ưu tiên hóa URL
    """

    def __init__(
        self,
        respect_robots: bool = True,
        use_sitemap: bool = True,
        max_depth: int = 3,
        max_urls: int = 1000,
        max_urls_per_domain: int = 100,
        timeout: float = 10.0,
        max_retries: int = 3,
        retry_delay: float = 2.0,
        user_agent: Optional[str] = None,
        verify_ssl: bool = True,
        proxies: Optional[Dict[str, str]] = None,
        cookies: Optional[Dict[str, str]] = None,
        headers: Optional[Dict[str, str]] = None,
        excluded_extensions: Optional[List[str]] = None,
        excluded_patterns: Optional[List[str]] = None,
        included_patterns: Optional[List[str]] = None,
        prioritize_patterns: Optional[List[str]] = None,
        max_concurrent_requests: int = 5,
        verbose: bool = False,
    ):
        """
        Khởi tạo SiteStructureHandler.

        Args:
            respect_robots: Tuân thủ robots.txt hay không
            use_sitemap: Sử dụng sitemap hay không
            max_depth: Độ sâu tối đa khi xây dựng cấu trúc
            max_urls: Số lượng URL tối đa
            max_urls_per_domain: Số lượng URL tối đa cho mỗi domain
            timeout: Thời gian timeout cho mỗi request (giây)
            max_retries: Số lần thử lại tối đa khi request thất bại
            retry_delay: Thời gian chờ giữa các lần thử lại (giây)
            user_agent: User-Agent header
            verify_ssl: Xác minh chứng chỉ SSL hay không
            proxies: Danh sách proxy
            cookies: Cookies cho request
            headers: Headers cho request
            excluded_extensions: Danh sách các phần mở rộng bị loại trừ
            excluded_patterns: Danh sách các mẫu URL bị loại trừ
            included_patterns: Danh sách các mẫu URL được bao gồm
            prioritize_patterns: Danh sách các mẫu URL được ưu tiên
            max_concurrent_requests: Số lượng request đồng thời tối đa
            verbose: Ghi log chi tiết hay không
        """
        # Cấu hình
        self.respect_robots = respect_robots
        self.use_sitemap = use_sitemap
        self.max_depth = max_depth
        self.max_urls = max_urls
        self.max_urls_per_domain = max_urls_per_domain
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.user_agent = user_agent or "SiteStructureHandler/1.0"
        self.verify_ssl = verify_ssl
        self.proxies = proxies or {}
        self.cookies = cookies or {}
        self.headers = headers or {}
        self.excluded_extensions = excluded_extensions or [
            ".jpg",
            ".jpeg",
            ".png",
            ".gif",
            ".css",
            ".js",
            ".ico",
            ".svg",
            ".woff",
            ".woff2",
            ".ttf",
            ".eot",
            ".mp3",
            ".mp4",
            ".webm",
            ".ogg",
            ".wav",
            ".avi",
            ".mov",
            ".wmv",
            ".flv",
            ".swf",
        ]
        self.excluded_patterns = excluded_patterns or [
            r"/wp-admin/",
            r"/wp-includes/",
            r"/wp-content/uploads/",
            r"/admin/",
            r"/login/",
            r"/logout/",
            r"/signin/",
            r"/signout/",
            r"/register/",
            r"/cart/",
            r"/checkout/",
            r"/account/",
            r"/profile/",
            r"/search\?",
            r"/tag/",
            r"/category/",
            r"/page/\d+/",
            r"/comment-page-\d+/",
            r"/trackback/",
            r"/feed/",
            r"/rss/",
            r"/atom/",
            r"/xmlrpc.php",
            r"/wp-json/",
            r"/api/",
            r"/ajax/",
            r"/cgi-bin/",
            r"/wp-login.php",
            r"/wp-signup.php",
            r"/wp-activate.php",
            r"/wp-comments-post.php",
            r"/wp-cron.php",
            r"/wp-mail.php",
            r"/wp-links-opml.php",
            r"/wp-load.php",
            r"/wp-settings.php",
            r"/wp-trackback.php",
            r"/wp-blog-header.php",
            r"/wp-config.php",
            r"/wp-config-sample.php",
            r"/wp-signup.php",
            r"/wp-activate.php",
            r"/wp-comments-post.php",
            r"/wp-cron.php",
            r"/wp-mail.php",
            r"/wp-links-opml.php",
            r"/wp-load.php",
            r"/wp-settings.php",
            r"/wp-trackback.php",
            r"/wp-blog-header.php",
            r"/wp-config.php",
            r"/wp-config-sample.php",
        ]
        self.included_patterns = included_patterns or []
        self.prioritize_patterns = prioritize_patterns or []
        self.max_concurrent_requests = max_concurrent_requests
        self.verbose = verbose

        # Khởi tạo session
        self.session = requests.Session()
        self.session.headers.update({"User-Agent": self.user_agent})
        if self.cookies:
            self.session.cookies.update(self.cookies)
        if self.headers:
            self.session.headers.update(self.headers)
        if self.proxies:
            self.session.proxies.update(self.proxies)

        # Khởi tạo các thuộc tính khác
        self.robots_cache = {}
        self.sitemap_cache = {}
        self.url_priorities = {}
        self.visited_urls = set()
        self.domain_counts = defaultdict(int)
        self.stats = {
            "robots_parsed": 0,
            "sitemaps_parsed": 0,
            "urls_found": 0,
            "urls_visited": 0,
            "urls_excluded": 0,
            "urls_included": 0,
            "urls_prioritized": 0,
            "domains_found": 0,
        }

    def parse_robots_txt(self, url: str) -> Dict[str, Any]:
        """
        Phân tích robots.txt.

        Args:
            url: URL của trang web

        Returns:
            Dict[str, Any]: Thông tin robots.txt
        """
        # Khởi tạo kết quả
        result = {
            "success": False,
            "url": url,
            "disallowed_paths": [],
            "allowed_paths": [],
            "sitemaps": [],
            "crawl_delay": 0,
            "host": "",
            "error": "",
        }

        # Kiểm tra cache
        domain = urlparse(url).netloc
        if domain in self.robots_cache:
            return self.robots_cache[domain]

        # Tạo URL robots.txt
        parsed_url = urlparse(url)
        robots_url = f"{parsed_url.scheme}://{parsed_url.netloc}/robots.txt"

        try:
            # Tải robots.txt
            response = self.session.get(
                robots_url,
                timeout=self.timeout,
                verify=self.verify_ssl,
            )

            # Kiểm tra status code
            if response.status_code != 200:
                result["error"] = f"HTTP error: {response.status_code}"
                self.robots_cache[domain] = result
                return result

            # Phân tích nội dung
            robots_txt = response.text
            lines = robots_txt.splitlines()

            # Phân tích từng dòng
            current_agent = None
            for line in lines:
                line = line.strip()

                # Bỏ qua comment và dòng trống
                if not line or line.startswith("#"):
                    continue

                # Phân tích dòng
                parts = line.split(":", 1)
                if len(parts) != 2:
                    continue

                directive = parts[0].strip().lower()
                value = parts[1].strip()

                # Xử lý User-agent
                if directive == "user-agent":
                    current_agent = value
                # Xử lý Disallow
                elif directive == "disallow" and (current_agent == "*" or current_agent == self.user_agent):
                    if value:
                        result["disallowed_paths"].append(value)
                # Xử lý Allow
                elif directive == "allow" and (current_agent == "*" or current_agent == self.user_agent):
                    if value:
                        result["allowed_paths"].append(value)
                # Xử lý Sitemap
                elif directive == "sitemap":
                    if value:
                        result["sitemaps"].append(value)
                # Xử lý Crawl-delay
                elif directive == "crawl-delay" and (current_agent == "*" or current_agent == self.user_agent):
                    if value.isdigit():
                        result["crawl_delay"] = int(value)
                # Xử lý Host
                elif directive == "host":
                    result["host"] = value

            # Cập nhật kết quả
            result["success"] = True
            self.stats["robots_parsed"] += 1
        except Exception as e:
            result["error"] = str(e)

        # Lưu vào cache
        self.robots_cache[domain] = result

        return result

    def parse_sitemap(self, url: str) -> Dict[str, Any]:
        """
        Phân tích sitemap.

        Args:
            url: URL của sitemap

        Returns:
            Dict[str, Any]: Thông tin sitemap
        """
        # Khởi tạo kết quả
        result = {
            "success": False,
            "url": url,
            "urls": [],
            "sitemaps": [],
            "error": "",
        }

        # Kiểm tra cache
        if url in self.sitemap_cache:
            return self.sitemap_cache[url]

        try:
            # Tải sitemap
            response = self.session.get(
                url,
                timeout=self.timeout,
                verify=self.verify_ssl,
            )

            # Kiểm tra status code
            if response.status_code != 200:
                result["error"] = f"HTTP error: {response.status_code}"
                self.sitemap_cache[url] = result
                return result

            # Phân tích nội dung
            sitemap_content = response.text

            # Kiểm tra xem có phải là sitemap index không
            if "<sitemapindex" in sitemap_content:
                # Phân tích sitemap index
                root = ET.fromstring(sitemap_content)
                ns = self._get_namespace(root)

                # Tìm tất cả sitemap
                for sitemap in root.findall(f".//{{{ns}}}sitemap"):
                    # Lấy URL của sitemap
                    loc = sitemap.find(f"{{{ns}}}loc")
                    if loc is not None and loc.text:
                        result["sitemaps"].append(loc.text)
            else:
                # Phân tích sitemap thông thường
                root = ET.fromstring(sitemap_content)
                ns = self._get_namespace(root)

                # Tìm tất cả URL
                for url_element in root.findall(f".//{{{ns}}}url"):
                    # Lấy URL
                    loc = url_element.find(f"{{{ns}}}loc")
                    if loc is not None and loc.text:
                        # Lấy các thông tin khác
                        lastmod = url_element.find(f"{{{ns}}}lastmod")
                        changefreq = url_element.find(f"{{{ns}}}changefreq")
                        priority = url_element.find(f"{{{ns}}}priority")

                        # Tạo URL object
                        url_obj = {
                            "url": loc.text,
                            "lastmod": lastmod.text if lastmod is not None else None,
                            "changefreq": changefreq.text if changefreq is not None else None,
                            "priority": float(priority.text) if priority is not None else 0.5,
                        }

                        # Thêm vào danh sách URL
                        result["urls"].append(url_obj)

            # Cập nhật kết quả
            result["success"] = True
            self.stats["sitemaps_parsed"] += 1
            self.stats["urls_found"] += len(result["urls"])
        except Exception as e:
            result["error"] = str(e)

        # Lưu vào cache
        self.sitemap_cache[url] = result

        return result

    def _get_namespace(self, element: ET.Element) -> str:
        """
        Lấy namespace của XML.

        Args:
            element: Phần tử XML

        Returns:
            str: Namespace
        """
        m = re.match(r"\{(.*?)\}", element.tag)
        return m.group(1) if m else ""

    def build_site_structure(self, start_url: str, max_depth: Optional[int] = None) -> Dict[str, Any]:
        """
        Xây dựng cấu trúc trang web.

        Args:
            start_url: URL bắt đầu
            max_depth: Độ sâu tối đa

        Returns:
            Dict[str, Any]: Cấu trúc trang web
        """
        # Sử dụng max_depth từ tham số hoặc từ cấu hình
        max_depth = max_depth or self.max_depth

        # Khởi tạo kết quả
        result = {
            "success": False,
            "start_url": start_url,
            "max_depth": max_depth,
            "structure": {},
            "urls": [],
            "domains": [],
            "error": "",
        }

        # Phân tích URL bắt đầu
        parsed_url = urlparse(start_url)
        start_domain = parsed_url.netloc

        # Kiểm tra robots.txt
        if self.respect_robots:
            robots_info = self.parse_robots_txt(start_url)
            if robots_info["success"]:
                # Lấy danh sách sitemap
                sitemaps = robots_info["sitemaps"]
                
                # Phân tích sitemap
                if self.use_sitemap and sitemaps:
                    for sitemap_url in sitemaps:
                        sitemap_info = self.parse_sitemap(sitemap_url)
                        if sitemap_info["success"]:
                            # Thêm URL từ sitemap vào danh sách
                            for url_obj in sitemap_info["urls"]:
                                url = url_obj["url"]
                                
                                # Kiểm tra URL
                                if self._is_valid_url(url, start_domain):
                                    # Thêm vào danh sách URL
                                    result["urls"].append(url)
                                    
                                    # Cập nhật ưu tiên
                                    self.url_priorities[url] = url_obj["priority"]

        # Khởi tạo hàng đợi URL
        url_queue = [(start_url, 0)]  # (url, depth)
        visited = set()
        structure = {}

        # Xử lý từng URL trong hàng đợi
        while url_queue and len(visited) < self.max_urls:
            # Lấy URL từ hàng đợi
            current_url, depth = url_queue.pop(0)
            
            # Kiểm tra xem URL đã được xử lý chưa
            if current_url in visited:
                continue
            
            # Đánh dấu URL đã được xử lý
            visited.add(current_url)
            
            # Phân tích URL
            parsed_url = urlparse(current_url)
            domain = parsed_url.netloc
            
            # Cập nhật thống kê
            self.domain_counts[domain] += 1
            self.stats["urls_visited"] += 1
            
            # Thêm domain vào danh sách
            if domain not in result["domains"]:
                result["domains"].append(domain)
                self.stats["domains_found"] += 1
            
            # Kiểm tra độ sâu
            if depth >= max_depth:
                continue
            
            # Kiểm tra số lượng URL tối đa cho mỗi domain
            if self.domain_counts[domain] >= self.max_urls_per_domain:
                continue
            
            try:
                # Tải trang
                response = self.session.get(
                    current_url,
                    timeout=self.timeout,
                    verify=self.verify_ssl,
                )
                
                # Kiểm tra status code
                if response.status_code != 200:
                    continue
                
                # Phân tích nội dung
                soup = BeautifulSoup(response.text, "html.parser")
                
                # Tìm tất cả link
                links = []
                for a in soup.find_all("a", href=True):
                    href = a.get("href", "")
                    
                    # Bỏ qua các href không hợp lệ
                    if not href or href.startswith("#") or href.startswith("javascript:"):
                        continue
                    
                    # Chuyển đổi relative URL thành absolute URL
                    if not href.startswith("http"):
                        href = urljoin(current_url, href)
                    
                    # Kiểm tra URL
                    if self._is_valid_url(href, start_domain):
                        # Thêm vào danh sách link
                        links.append(href)
                        
                        # Thêm vào hàng đợi URL
                        url_queue.append((href, depth + 1))
                
                # Cập nhật cấu trúc
                structure[current_url] = links
            except Exception as e:
                if self.verbose:
                    logger.error(f"Error processing URL {current_url}: {str(e)}")
                continue

        # Cập nhật kết quả
        result["success"] = True
        result["structure"] = structure
        result["urls"] = list(visited)

        return result

    def _is_valid_url(self, url: str, start_domain: str) -> bool:
        """
        Kiểm tra xem URL có hợp lệ không.

        Args:
            url: URL cần kiểm tra
            start_domain: Domain bắt đầu

        Returns:
            bool: True nếu URL hợp lệ, False nếu không
        """
        # Phân tích URL
        parsed_url = urlparse(url)
        domain = parsed_url.netloc
        path = parsed_url.path
        
        # Kiểm tra domain
        if not domain:
            return False
        
        # Kiểm tra phần mở rộng
        if any(path.lower().endswith(ext) for ext in self.excluded_extensions):
            self.stats["urls_excluded"] += 1
            return False
        
        # Kiểm tra mẫu loại trừ
        if any(re.search(pattern, url) for pattern in self.excluded_patterns):
            self.stats["urls_excluded"] += 1
            return False
        
        # Kiểm tra mẫu bao gồm
        if self.included_patterns and not any(re.search(pattern, url) for pattern in self.included_patterns):
            self.stats["urls_excluded"] += 1
            return False
        
        # Kiểm tra mẫu ưu tiên
        if any(re.search(pattern, url) for pattern in self.prioritize_patterns):
            self.url_priorities[url] = 1.0
            self.stats["urls_prioritized"] += 1
        
        # Cập nhật thống kê
        self.stats["urls_included"] += 1
        
        return True

    def prioritize_urls(self, urls: List[str], start_url: str) -> List[str]:
        """
        Ưu tiên hóa URL.

        Args:
            urls: Danh sách URL
            start_url: URL bắt đầu

        Returns:
            List[str]: Danh sách URL đã ưu tiên hóa
        """
        # Phân tích URL bắt đầu
        parsed_start_url = urlparse(start_url)
        start_domain = parsed_start_url.netloc
        start_path = parsed_start_url.path
        
        # Tạo danh sách URL với ưu tiên
        url_priorities = []
        for url in urls:
            # Lấy ưu tiên từ cache
            priority = self.url_priorities.get(url, 0.5)
            
            # Phân tích URL
            parsed_url = urlparse(url)
            domain = parsed_url.netloc
            path = parsed_url.path
            
            # Tăng ưu tiên cho URL cùng domain
            if domain == start_domain:
                priority += 0.2
            
            # Tăng ưu tiên cho URL cùng path
            if path.startswith(start_path):
                priority += 0.1
            
            # Tăng ưu tiên cho URL ngắn
            if len(path.split("/")) <= 3:
                priority += 0.1
            
            # Thêm vào danh sách
            url_priorities.append((url, priority))
        
        # Sắp xếp theo ưu tiên giảm dần
        url_priorities.sort(key=lambda x: x[1], reverse=True)
        
        # Trả về danh sách URL đã ưu tiên hóa
        return [url for url, _ in url_priorities]

    def is_allowed_by_robots(self, url: str) -> bool:
        """
        Kiểm tra xem URL có được phép bởi robots.txt hay không.

        Args:
            url: URL cần kiểm tra

        Returns:
            bool: True nếu được phép, False nếu không
        """
        if not self.respect_robots:
            return True
        
        # Phân tích URL
        parsed_url = urlparse(url)
        domain = parsed_url.netloc
        path = parsed_url.path
        
        # Lấy thông tin robots.txt
        robots_info = self.parse_robots_txt(url)
        
        # Kiểm tra disallowed paths
        for disallowed_path in robots_info["disallowed_paths"]:
            if path.startswith(disallowed_path):
                # Kiểm tra allowed paths (ưu tiên cao hơn)
                for allowed_path in robots_info["allowed_paths"]:
                    if path.startswith(allowed_path) and len(allowed_path) > len(disallowed_path):
                        return True
                return False
        
        return True
