#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module cung cấp lớp DistributedCrawler, một phần mở rộng cho AdaptiveCrawler hỗ trợ crawl phân tán.

Module này cung cấp lớp DistributedCrawler, một phần mở rộng cho AdaptiveCrawler
hỗ trợ crawl phân tán trên nhiều máy, giúp tăng tốc độ crawl và xử lý khối lượng lớn URL.
"""

import os
import time
import json
import uuid
import socket
import logging
import threading
import multiprocessing
import queue
from typing import Dict, List, Any, Optional, Tuple, Set, Union
import requests
from urllib.parse import urlparse

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DistributedCrawler:
    """
    Lớp cung cấp các phương thức để crawl phân tán trên nhiều máy.

    Tính năng:
    - <PERSON><PERSON> phối URL cần crawl cho nhiều worker
    - Hỗ trợ nhiều chế độ phân phối (round-robin, weighted, adaptive)
    - Tự động cân bằng tải giữa các worker
    - Xử lý lỗi và retry
    - Hỗ trợ checkpoint và resume
    - Tổng hợp kết quả từ nhiều worker
    """

    def __init__(
        self,
        master_node: Optional[str] = None,
        worker_nodes: Optional[List[str]] = None,
        port: int = 8765,
        distribution_mode: str = "round_robin",
        batch_size: int = 10,
        timeout: float = 30.0,
        max_retries: int = 3,
        retry_delay: float = 2.0,
        checkpoint_interval: int = 100,
        checkpoint_path: Optional[str] = None,
        use_local_workers: bool = True,
        max_local_workers: int = 4,
        verbose: bool = False,
    ):
        """
        Khởi tạo DistributedCrawler.

        Args:
            master_node: Địa chỉ của node master (None nếu node hiện tại là master)
            worker_nodes: Danh sách địa chỉ của các node worker
            port: Cổng để giao tiếp giữa các node
            distribution_mode: Chế độ phân phối URL ("round_robin", "weighted", "adaptive")
            batch_size: Số lượng URL gửi cho mỗi worker trong một lần
            timeout: Thời gian chờ tối đa cho mỗi request (giây)
            max_retries: Số lần thử lại tối đa khi gặp lỗi
            retry_delay: Thời gian chờ giữa các lần thử lại (giây)
            checkpoint_interval: Số URL đã xử lý trước khi tạo checkpoint
            checkpoint_path: Đường dẫn để lưu checkpoint
            use_local_workers: Có sử dụng worker trên máy local hay không
            max_local_workers: Số lượng worker tối đa trên máy local
            verbose: Ghi log chi tiết hay không
        """
        # Cấu hình
        self.master_node = master_node
        self.worker_nodes = worker_nodes or []
        self.port = port
        self.distribution_mode = distribution_mode
        self.batch_size = batch_size
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.checkpoint_interval = checkpoint_interval
        self.checkpoint_path = checkpoint_path or os.path.join(os.getcwd(), "distributed_crawler_checkpoint.json")
        self.use_local_workers = use_local_workers
        self.max_local_workers = max_local_workers
        self.verbose = verbose

        # Khởi tạo ID cho node
        self.node_id = str(uuid.uuid4())

        # Khởi tạo các thuộc tính
        self.is_master = master_node is None
        self.is_running = False
        self.url_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.processed_urls = set()
        self.worker_stats = {}
        self.local_workers = []
        self.server_thread = None
        self.checkpoint_thread = None
        self.url_count = 0
        self.result_count = 0

        # Khởi tạo các thành phần
        self._initialize_components()

        logger.info(f"DistributedCrawler được khởi tạo với node_id={self.node_id}, is_master={self.is_master}")

    def _initialize_components(self):
        """Khởi tạo các thành phần."""
        # Khởi tạo worker stats
        for node in self.worker_nodes:
            self.worker_stats[node] = {
                "urls_assigned": 0,
                "urls_completed": 0,
                "success_rate": 1.0,
                "avg_response_time": 0.0,
                "last_heartbeat": time.time(),
                "status": "unknown"
            }

        # Tải checkpoint nếu có
        self._load_checkpoint()

    def start(self):
        """Bắt đầu DistributedCrawler."""
        if self.is_running:
            logger.warning("DistributedCrawler đã đang chạy")
            return

        self.is_running = True

        # Nếu là master, khởi động server
        if self.is_master:
            self._start_server()
            self._start_checkpoint_thread()

            # Khởi động local workers nếu cần
            if self.use_local_workers:
                self._start_local_workers()
        else:
            # Nếu là worker, kết nối đến master
            self._connect_to_master()

        logger.info(f"DistributedCrawler đã bắt đầu với chế độ {'master' if self.is_master else 'worker'}")

    def stop(self):
        """Dừng DistributedCrawler."""
        if not self.is_running:
            logger.warning("DistributedCrawler không đang chạy")
            return

        self.is_running = False

        # Dừng server nếu là master
        if self.is_master and self.server_thread:
            # TODO: Implement server stop
            pass

        # Dừng checkpoint thread nếu là master
        if self.is_master and self.checkpoint_thread:
            # TODO: Implement checkpoint thread stop
            pass

        # Dừng local workers nếu có
        if self.use_local_workers and self.local_workers:
            # TODO: Implement local workers stop
            pass

        logger.info("DistributedCrawler đã dừng")

    def add_urls(self, urls: List[str]):
        """
        Thêm URLs vào hàng đợi.

        Args:
            urls: Danh sách URL cần crawl
        """
        if not self.is_master:
            logger.warning("Chỉ master node mới có thể thêm URLs")
            return

        for url in urls:
            if url not in self.processed_urls:
                self.url_queue.put(url)
                self.url_count += 1

        logger.info(f"Đã thêm {len(urls)} URLs vào hàng đợi")

    def get_results(self, wait: bool = True, timeout: Optional[float] = None) -> List[Dict[str, Any]]:
        """
        Lấy kết quả crawl.

        Args:
            wait: Có đợi cho đến khi có kết quả hay không
            timeout: Thời gian chờ tối đa (giây)

        Returns:
            List[Dict[str, Any]]: Danh sách kết quả crawl
        """
        if not self.is_master:
            logger.warning("Chỉ master node mới có thể lấy kết quả")
            return []

        results = []
        try:
            while not self.result_queue.empty():
                results.append(self.result_queue.get(block=wait, timeout=timeout))
                self.result_queue.task_done()
        except queue.Empty:
            pass

        logger.info(f"Đã lấy {len(results)} kết quả")
        return results

    def _start_server(self):
        """Khởi động server để giao tiếp với các worker."""
        # TODO: Implement server start
        pass

    def _start_checkpoint_thread(self):
        """Khởi động thread để tạo checkpoint định kỳ."""
        # TODO: Implement checkpoint thread start
        pass

    def _start_local_workers(self):
        """Khởi động các worker trên máy local."""
        # TODO: Implement local workers start
        pass

    def _connect_to_master(self):
        """Kết nối đến master node."""
        # TODO: Implement master connection
        pass

    def _load_checkpoint(self):
        """Tải checkpoint nếu có."""
        if not os.path.exists(self.checkpoint_path):
            logger.info(f"Không tìm thấy checkpoint tại {self.checkpoint_path}")
            return

        try:
            with open(self.checkpoint_path, "r") as f:
                checkpoint = json.load(f)

            # Khôi phục trạng thái
            self.processed_urls = set(checkpoint.get("processed_urls", []))
            self.url_count = checkpoint.get("url_count", 0)
            self.result_count = checkpoint.get("result_count", 0)

            # Thêm URLs chưa xử lý vào hàng đợi
            for url in checkpoint.get("pending_urls", []):
                self.url_queue.put(url)

            logger.info(f"Đã tải checkpoint từ {self.checkpoint_path}")
        except Exception as e:
            logger.error(f"Lỗi khi tải checkpoint: {str(e)}")

    def _save_checkpoint(self):
        """Lưu checkpoint."""
        # Tạo checkpoint
        checkpoint = {
            "processed_urls": list(self.processed_urls),
            "url_count": self.url_count,
            "result_count": self.result_count,
            "pending_urls": list(self.url_queue.queue),
            "timestamp": time.time()
        }

        # Lưu checkpoint
        try:
            with open(self.checkpoint_path, "w") as f:
                json.dump(checkpoint, f)

            logger.info(f"Đã lưu checkpoint vào {self.checkpoint_path}")
        except Exception as e:
            logger.error(f"Lỗi khi lưu checkpoint: {str(e)}")
