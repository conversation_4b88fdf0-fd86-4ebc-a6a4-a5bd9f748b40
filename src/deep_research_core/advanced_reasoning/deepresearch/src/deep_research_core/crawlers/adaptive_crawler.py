#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module cung cấp các lớp và phương thức để crawl nội dung web một cách thích ứng.

Module này cung cấp các lớp và phương thức để crawl nội dung web một cách thích <PERSON>ng,
tự động điều chỉnh chiến lược crawl dựa trên loại trang web, nội dung, và các ràng buộc.
"""

import time
import logging
import re
import random
import requests
from urllib.parse import urlparse, urljoin
from typing import Dict, List, Any, Optional, Tuple, Set, Union
from bs4 import BeautifulSoup
import concurrent.futures
from collections import defaultdict
import json
import hashlib

from ..utils.structured_logging import get_logger

# Thiết lập logging
logger = get_logger(__name__)


class AdaptiveCrawler:
    """
    Lớp cung cấp các phương thức để crawl nội dung web một cách thích ứng.

    T<PERSON>h năng:
    - Tự động điều chỉnh chiến lược crawl dựa trên loại trang web
    - Hỗ trợ nhiều loại trang web (tin tức, blog, diễn đàn, wiki, v.v.)
    - Tuân thủ robots.txt và các ràng buộc crawl
    - Xử lý CAPTCHA và các cơ chế chống bot
    - Tối ưu hóa tốc độ và hiệu suất crawl
    - Hỗ trợ crawl đa luồng và phân tán
    """

    def __init__(
        self,
        max_depth: int = 2,
        max_urls_per_domain: int = 5,
        max_total_urls: int = 100,
        delay: float = 1.0,
        timeout: float = 10.0,
        user_agent: Optional[str] = None,
        respect_robots_txt: bool = True,
        follow_redirects: bool = True,
        max_redirects: int = 5,
        max_retries: int = 3,
        retry_delay: float = 2.0,
        max_threads: int = 5,
        verify_ssl: bool = True,
        proxies: Optional[Dict[str, str]] = None,
        cookies: Optional[Dict[str, str]] = None,
        headers: Optional[Dict[str, str]] = None,
        cache_enabled: bool = True,
        cache_ttl: int = 3600,
        cache_size: int = 1000,
        content_types: Optional[List[str]] = None,
        excluded_extensions: Optional[List[str]] = None,
        excluded_domains: Optional[List[str]] = None,
        included_domains: Optional[List[str]] = None,
        url_patterns: Optional[List[str]] = None,
        content_patterns: Optional[List[str]] = None,
        min_content_length: int = 500,
        max_content_length: int = 5000000,
        extract_metadata: bool = True,
        extract_links: bool = True,
        extract_images: bool = False,
        extract_files: bool = False,
        extract_structured_data: bool = False,
        detect_language: bool = True,
        detect_encoding: bool = True,
        detect_content_type: bool = True,
        detect_site_type: bool = True,
        handle_javascript: bool = False,
        handle_captcha: bool = False,
        verbose: bool = False,
    ):
        """
        Khởi tạo AdaptiveCrawler.

        Args:
            max_depth: Độ sâu tối đa khi crawl
            max_urls_per_domain: Số lượng URL tối đa cho mỗi domain
            max_total_urls: Tổng số URL tối đa sẽ crawl
            delay: Thời gian chờ giữa các request (giây)
            timeout: Thời gian timeout cho mỗi request (giây)
            user_agent: User-Agent header
            respect_robots_txt: Tuân thủ robots.txt hay không
            follow_redirects: Theo dõi chuyển hướng hay không
            max_redirects: Số lượng chuyển hướng tối đa
            max_retries: Số lần thử lại tối đa khi request thất bại
            retry_delay: Thời gian chờ giữa các lần thử lại (giây)
            max_threads: Số luồng tối đa khi crawl
            verify_ssl: Xác minh chứng chỉ SSL hay không
            proxies: Danh sách proxy
            cookies: Cookies cho request
            headers: Headers cho request
            cache_enabled: Bật cache hay không
            cache_ttl: Thời gian sống của cache (giây)
            cache_size: Kích thước tối đa của cache
            content_types: Danh sách các loại nội dung được phép
            excluded_extensions: Danh sách các phần mở rộng bị loại trừ
            excluded_domains: Danh sách các domain bị loại trừ
            included_domains: Danh sách các domain được phép
            url_patterns: Danh sách các mẫu URL được phép
            content_patterns: Danh sách các mẫu nội dung được phép
            min_content_length: Độ dài tối thiểu của nội dung
            max_content_length: Độ dài tối đa của nội dung
            extract_metadata: Trích xuất metadata hay không
            extract_links: Trích xuất links hay không
            extract_images: Trích xuất hình ảnh hay không
            extract_files: Trích xuất files hay không
            extract_structured_data: Trích xuất dữ liệu có cấu trúc hay không
            detect_language: Phát hiện ngôn ngữ hay không
            detect_encoding: Phát hiện mã hóa hay không
            detect_content_type: Phát hiện loại nội dung hay không
            detect_site_type: Phát hiện loại trang web hay không
            handle_javascript: Xử lý JavaScript hay không
            handle_captcha: Xử lý CAPTCHA hay không
            verbose: Ghi log chi tiết hay không
        """
        # Cấu hình crawl
        self.max_depth = max_depth
        self.max_urls_per_domain = max_urls_per_domain
        self.max_total_urls = max_total_urls
        self.delay = delay
        self.timeout = timeout
        self.user_agent = user_agent or "AdaptiveCrawler/1.0"
        self.respect_robots_txt = respect_robots_txt
        self.follow_redirects = follow_redirects
        self.max_redirects = max_redirects
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.max_threads = max_threads
        self.verify_ssl = verify_ssl
        self.proxies = proxies or {}
        self.cookies = cookies or {}
        self.headers = headers or {}
        self.cache_enabled = cache_enabled
        self.cache_ttl = cache_ttl
        self.cache_size = cache_size
        self.content_types = content_types or [
            "text/html",
            "text/plain",
            "application/json",
        ]
        self.excluded_extensions = excluded_extensions or [
            ".jpg",
            ".jpeg",
            ".png",
            ".gif",
            ".css",
            ".js",
            ".ico",
            ".svg",
        ]
        self.excluded_domains = excluded_domains or []
        self.included_domains = included_domains or []
        self.url_patterns = url_patterns or []
        self.content_patterns = content_patterns or []
        self.min_content_length = min_content_length
        self.max_content_length = max_content_length
        self.extract_metadata = extract_metadata
        self.extract_links = extract_links
        self.extract_images = extract_images
        self.extract_files = extract_files
        self.extract_structured_data = extract_structured_data
        self.detect_language = detect_language
        self.detect_encoding = detect_encoding
        self.detect_content_type = detect_content_type
        self.detect_site_type = detect_site_type
        self.handle_javascript = handle_javascript
        self.handle_captcha = handle_captcha
        self.verbose = verbose

        # Khởi tạo các thuộc tính khác
        self.visited_urls = set()
        self.url_queue = []
        self.domain_counts = defaultdict(int)
        self.robots_txt_cache = {}
        self.content_cache = {}
        self.last_request_time = defaultdict(float)
        self.site_type_cache = {}
        self.language_cache = {}
        self.encoding_cache = {}
        self.content_type_cache = {}
        self.error_counts = defaultdict(int)
        self.stats = {
            "total_urls_crawled": 0,
            "successful_crawls": 0,
            "failed_crawls": 0,
            "total_content_size": 0,
            "total_links_extracted": 0,
            "total_time": 0,
            "captchas_encountered": 0,
            "captchas_solved": 0,
            "robots_txt_blocked": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "retries": 0,
            "timeouts": 0,
            "redirects": 0,
            "errors": defaultdict(int),
        }

        # Khởi tạo session
        self.session = requests.Session()
        self.session.headers.update({"User-Agent": self.user_agent})
        if self.cookies:
            self.session.cookies.update(self.cookies)
        if self.headers:
            self.session.headers.update(self.headers)
        if self.proxies:
            self.session.proxies.update(self.proxies)

        # Khởi tạo executor
        self.executor = None

        # Khởi tạo các bộ phát hiện
        self._initialize_detectors()

    def _initialize_detectors(self):
        """
        Khởi tạo các bộ phát hiện.
        """
        # Khởi tạo bộ phát hiện ngôn ngữ
        self.language_detector = None
        if self.detect_language:
            try:
                from ..multilingual import LanguageDetector

                self.language_detector = LanguageDetector()
                logger.info("Language detector initialized")
            except ImportError:
                logger.warning("Language detector not available")

        # Khởi tạo bộ phát hiện loại trang web
        self.site_type_detector = None
        if self.detect_site_type:
            try:
                from ..agents.domain_analyzers import SiteTypeDetector

                self.site_type_detector = SiteTypeDetector()
                logger.info("Site type detector initialized")
            except ImportError:
                logger.warning("Site type detector not available")

        # Khởi tạo bộ xử lý CAPTCHA
        self.captcha_handler = None
        self.has_captcha_handler = False
        if self.handle_captcha:
            try:
                from .adaptive_crawler_captcha_integration import integrate_captcha_handler

                # Tích hợp CaptchaHandler mới
                integrate_captcha_handler(self)
                logger.info("CaptchaHandler integrated successfully")
            except ImportError:
                # Fallback to old CaptchaHandler
                try:
                    from ..agents import CaptchaHandler
                    self.captcha_handler = CaptchaHandler()
                    logger.info("Legacy CAPTCHA handler initialized")
                except ImportError:
                    logger.warning("CAPTCHA handler not available")

        # Khởi tạo bộ xử lý JavaScript
        self.javascript_handler = None
        if self.handle_javascript:
            try:
                import selenium
                from selenium import webdriver
                from selenium.webdriver.chrome.options import Options

                chrome_options = Options()
                chrome_options.add_argument("--headless")
                chrome_options.add_argument("--no-sandbox")
                chrome_options.add_argument("--disable-dev-shm-usage")
                chrome_options.add_argument(f"--user-agent={self.user_agent}")

                self.javascript_handler = webdriver.Chrome(options=chrome_options)
                logger.info("JavaScript handler initialized")
            except ImportError:
                logger.warning("JavaScript handler not available")

    def crawl(self, url: str, depth: int = 0, **kwargs) -> Dict[str, Any]:
        """
        Crawl một URL.

        Args:
            url: URL cần crawl
            depth: Độ sâu hiện tại
            **kwargs: Các tham số bổ sung

        Returns:
            Dict[str, Any]: Kết quả crawl
        """
        # Kiểm tra URL đã được crawl chưa
        if url in self.visited_urls:
            return {"success": False, "url": url, "error": "URL already crawled"}

        # Kiểm tra độ sâu
        if depth > self.max_depth:
            return {"success": False, "url": url, "error": "Max depth exceeded"}

        # Kiểm tra số lượng URL tối đa
        if self.stats["total_urls_crawled"] >= self.max_total_urls:
            return {"success": False, "url": url, "error": "Max total URLs exceeded"}

        # Phân tích URL
        parsed_url = urlparse(url)
        domain = parsed_url.netloc

        # Kiểm tra domain
        if self.excluded_domains and domain in self.excluded_domains:
            return {"success": False, "url": url, "error": "Domain excluded"}

        if self.included_domains and domain not in self.included_domains:
            return {"success": False, "url": url, "error": "Domain not included"}

        # Kiểm tra số lượng URL tối đa cho mỗi domain
        if self.domain_counts[domain] >= self.max_urls_per_domain:
            return {
                "success": False,
                "url": url,
                "error": "Max URLs per domain exceeded",
            }

        # Kiểm tra phần mở rộng
        if any(url.lower().endswith(ext) for ext in self.excluded_extensions):
            return {"success": False, "url": url, "error": "Excluded extension"}

        # Kiểm tra mẫu URL
        if self.url_patterns and not any(
            re.search(pattern, url) for pattern in self.url_patterns
        ):
            return {"success": False, "url": url, "error": "URL pattern not matched"}

        # Kiểm tra robots.txt
        if self.respect_robots_txt and not self._is_allowed_by_robots_txt(url):
            self.stats["robots_txt_blocked"] += 1
            return {"success": False, "url": url, "error": "Blocked by robots.txt"}

        # Kiểm tra cache
        if self.cache_enabled:
            cache_key = self._get_cache_key(url)
            cached_result = self._get_from_cache(cache_key)
            if cached_result:
                self.stats["cache_hits"] += 1
                return cached_result
            self.stats["cache_misses"] += 1

        # Thực hiện crawl
        start_time = time.time()
        result = self._crawl_url(url, depth, **kwargs)
        end_time = time.time()

        # Cập nhật thống kê
        self.stats["total_urls_crawled"] += 1
        self.domain_counts[domain] += 1
        self.visited_urls.add(url)

        if result.get("success", False):
            self.stats["successful_crawls"] += 1
            self.stats["total_content_size"] += len(result.get("content", ""))
            self.stats["total_links_extracted"] += len(result.get("links", []))
        else:
            self.stats["failed_crawls"] += 1
            error_type = result.get("error_type", "unknown")
            self.stats["errors"][error_type] += 1

        result["crawl_time"] = end_time - start_time
        self.stats["total_time"] += result["crawl_time"]

        # Lưu vào cache
        if self.cache_enabled and result.get("success", False):
            cache_key = self._get_cache_key(url)
            self._add_to_cache(cache_key, result)

        return result

    def _crawl_url(self, url: str, depth: int = 0, **kwargs) -> Dict[str, Any]:
        """
        Thực hiện crawl một URL.

        Args:
            url: URL cần crawl
            depth: Độ sâu hiện tại
            **kwargs: Các tham số bổ sung

        Returns:
            Dict[str, Any]: Kết quả crawl
        """
        # Khởi tạo kết quả
        result = {
            "success": False,
            "url": url,
            "depth": depth,
            "timestamp": time.time(),
        }

        # Thực hiện request
        try:
            # Chờ theo delay
            self._respect_delay(url)

            # Thực hiện request
            if self.handle_javascript:
                response = self._fetch_with_javascript(url)
            else:
                response = self._fetch_with_requests(url)

            # Kiểm tra response
            if not response:
                result["error"] = "Failed to fetch URL"
                result["error_type"] = "fetch_error"
                return result

            # Kiểm tra status code
            if response.get("status_code", 0) >= 400:
                result["error"] = f"HTTP error: {response.get('status_code')}"
                result["error_type"] = "http_error"
                result["status_code"] = response.get("status_code")
                return result

            # Kiểm tra CAPTCHA
            if self._is_captcha(response.get("content", "")):
                self.stats["captchas_encountered"] += 1
                if self.handle_captcha and self.captcha_handler:
                    # Thử giải CAPTCHA
                    solved = self._solve_captcha(url, response)
                    if solved:
                        self.stats["captchas_solved"] += 1
                        # Thực hiện lại request
                        return self._crawl_url(url, depth, **kwargs)

                result["error"] = "CAPTCHA detected"
                result["error_type"] = "captcha"
                return result

            # Xử lý nội dung
            content = response.get("content", "")

            # Kiểm tra độ dài nội dung
            if len(content) < self.min_content_length:
                result["error"] = "Content too short"
                result["error_type"] = "content_too_short"
                return result

            if len(content) > self.max_content_length:
                result["error"] = "Content too long"
                result["error_type"] = "content_too_long"
                return result

            # Kiểm tra mẫu nội dung
            if self.content_patterns and not any(
                re.search(pattern, content) for pattern in self.content_patterns
            ):
                result["error"] = "Content pattern not matched"
                result["error_type"] = "content_pattern_not_matched"
                return result

            # Phát hiện loại trang web
            site_type = self._detect_site_type(url, content)

            # Phát hiện ngôn ngữ
            language = self._detect_language(content)

            # Phát hiện mã hóa
            encoding = response.get("encoding", "utf-8")

            # Phát hiện loại nội dung
            content_type = response.get("content_type", "text/html")

            # Trích xuất metadata
            metadata = (
                self._extract_metadata(url, content) if self.extract_metadata else {}
            )

            # Trích xuất links
            links = self._extract_links(url, content) if self.extract_links else []

            # Trích xuất hình ảnh
            images = self._extract_images(url, content) if self.extract_images else []

            # Trích xuất files
            files = self._extract_files(url, content) if self.extract_files else []

            # Trích xuất dữ liệu có cấu trúc
            structured_data = (
                self._extract_structured_data(url, content)
                if self.extract_structured_data
                else {}
            )

            # Cập nhật kết quả
            result.update(
                {
                    "success": True,
                    "content": content,
                    "site_type": site_type,
                    "language": language,
                    "encoding": encoding,
                    "content_type": content_type,
                    "metadata": metadata,
                    "links": links,
                    "images": images,
                    "files": files,
                    "structured_data": structured_data,
                    "status_code": response.get("status_code"),
                    "headers": response.get("headers", {}),
                }
            )

            return result
        except Exception as e:
            # Xử lý lỗi
            result["error"] = str(e)
            result["error_type"] = type(e).__name__
            return result

    def _respect_delay(self, url: str) -> None:
        """
        Chờ theo delay để tránh quá tải server.

        Args:
            url: URL cần crawl
        """
        domain = urlparse(url).netloc
        last_request = self.last_request_time[domain]
        current_time = time.time()

        # Tính toán thời gian cần chờ
        time_since_last_request = current_time - last_request
        if time_since_last_request < self.delay:
            wait_time = self.delay - time_since_last_request
            if self.verbose:
                logger.debug(f"Waiting {wait_time:.2f}s for {domain}")
            time.sleep(wait_time)

        # Cập nhật thời gian request cuối cùng
        self.last_request_time[domain] = time.time()

    def _fetch_with_requests(self, url: str) -> Dict[str, Any]:
        """
        Fetch URL sử dụng thư viện requests.

        Args:
            url: URL cần fetch

        Returns:
            Dict[str, Any]: Kết quả fetch
        """
        # Khởi tạo kết quả
        result = {"url": url, "success": False}

        # Thực hiện request với retry
        for attempt in range(self.max_retries):
            try:
                response = self.session.get(
                    url,
                    timeout=self.timeout,
                    allow_redirects=self.follow_redirects,
                    verify=self.verify_ssl,
                    headers=self.headers,
                    proxies=self.proxies,
                    cookies=self.cookies,
                )

                # Cập nhật kết quả
                result.update(
                    {
                        "success": True,
                        "status_code": response.status_code,
                        "content": response.text,
                        "content_type": response.headers.get("Content-Type", ""),
                        "encoding": response.encoding,
                        "headers": dict(response.headers),
                        "cookies": dict(response.cookies),
                        "url": response.url,  # URL sau khi redirect
                        "redirects": len(response.history),
                    }
                )

                # Cập nhật thống kê
                self.stats["redirects"] += len(response.history)

                return result
            except requests.Timeout:
                self.stats["timeouts"] += 1
                result["error"] = "Request timed out"
                result["error_type"] = "timeout"
            except requests.RequestException as e:
                self.stats["errors"][type(e).__name__] += 1
                result["error"] = str(e)
                result["error_type"] = type(e).__name__

            # Tăng số lần retry
            self.stats["retries"] += 1

            # Chờ trước khi thử lại
            if attempt < self.max_retries - 1:
                time.sleep(self.retry_delay * (attempt + 1))

        return result

    def _fetch_with_javascript(self, url: str) -> Dict[str, Any]:
        """
        Fetch URL sử dụng trình duyệt headless để xử lý JavaScript.

        Args:
            url: URL cần fetch

        Returns:
            Dict[str, Any]: Kết quả fetch
        """
        # Kiểm tra JavaScript handler
        if not self.javascript_handler:
            logger.warning("JavaScript handler not available")
            return self._fetch_with_requests(url)

        # Khởi tạo kết quả
        result = {"url": url, "success": False}

        # Thực hiện request với retry
        for attempt in range(self.max_retries):
            try:
                # Mở URL
                self.javascript_handler.get(url)

                # Chờ trang tải xong
                time.sleep(2)

                # Lấy nội dung
                content = self.javascript_handler.page_source

                # Lấy URL hiện tại (sau khi redirect)
                current_url = self.javascript_handler.current_url

                # Cập nhật kết quả
                result.update(
                    {
                        "success": True,
                        "status_code": 200,  # Selenium không cung cấp status code
                        "content": content,
                        "content_type": "text/html",  # Selenium không cung cấp content type
                        "encoding": "utf-8",  # Selenium không cung cấp encoding
                        "headers": {},  # Selenium không cung cấp headers
                        "cookies": self.javascript_handler.get_cookies(),
                        "url": current_url,
                        "redirects": 1 if current_url != url else 0,
                    }
                )

                return result
            except Exception as e:
                self.stats["errors"][type(e).__name__] += 1
                result["error"] = str(e)
                result["error_type"] = type(e).__name__

            # Tăng số lần retry
            self.stats["retries"] += 1

            # Chờ trước khi thử lại
            if attempt < self.max_retries - 1:
                time.sleep(self.retry_delay * (attempt + 1))

        return result

    def _is_captcha(self, content: str) -> bool:
        """
        Kiểm tra xem nội dung có chứa CAPTCHA hay không.

        Args:
            content: Nội dung cần kiểm tra

        Returns:
            bool: True nếu phát hiện CAPTCHA, False nếu không
        """
        # Các từ khóa CAPTCHA phổ biến
        captcha_keywords = [
            "captcha",
            "recaptcha",
            "robot",
            "human verification",
            "bot check",
            "security check",
            "verify you are human",
            "prove you're not a robot",
            "xác minh",
            "xác nhận",
            "mã xác nhận",
            "không phải robot",
        ]

        # Kiểm tra từ khóa
        content_lower = content.lower()
        if any(keyword in content_lower for keyword in captcha_keywords):
            return True

        # Kiểm tra các mẫu HTML của CAPTCHA
        captcha_patterns = [
            r'class=["\']g-recaptcha["\']',
            r'class=["\']h-captcha["\']',
            r'class=["\']captcha["\']',
            r'id=["\']captcha["\']',
            r'name=["\']captcha["\']',
            r'src=["\'][^"\']*captcha[^"\']*["\']',
            r'src=["\'][^"\']*recaptcha[^"\']*["\']',
            r'src=["\'][^"\']*hcaptcha[^"\']*["\']',
        ]

        for pattern in captcha_patterns:
            if re.search(pattern, content):
                return True

        return False

    def _solve_captcha(self, url: str, response: Dict[str, Any]) -> bool:
        """
        Thử giải CAPTCHA.

        Args:
            url: URL chứa CAPTCHA
            response: Response chứa CAPTCHA

        Returns:
            bool: True nếu giải thành công, False nếu không
        """
        if not self.captcha_handler:
            return False

        content = response.get("content", "")

        # Phát hiện loại CAPTCHA
        if "recaptcha" in content.lower():
            # Xử lý reCAPTCHA
            return self.captcha_handler.solve_recaptcha(url)
        elif "hcaptcha" in content.lower():
            # Xử lý hCAPTCHA
            return self.captcha_handler.solve_hcaptcha(url)
        else:
            # Xử lý CAPTCHA thông thường
            return self.captcha_handler.solve_image_captcha(url)

    def _detect_site_type(self, url: str, content: str) -> str:
        """
        Phát hiện loại trang web.

        Args:
            url: URL của trang web
            content: Nội dung của trang web

        Returns:
            str: Loại trang web
        """
        # Kiểm tra cache
        domain = urlparse(url).netloc
        if domain in self.site_type_cache:
            return self.site_type_cache[domain]

        # Sử dụng bộ phát hiện loại trang web nếu có
        if self.site_type_detector:
            site_type = self.site_type_detector.detect_site_type(url, content)
            self.site_type_cache[domain] = site_type
            return site_type

        # Phát hiện thủ công
        content_lower = content.lower()

        # Kiểm tra các mẫu của từng loại trang web
        if re.search(r'<meta\s+name=["\']generator["\'][^>]*wordpress', content):
            site_type = "blog"
        elif re.search(r'<meta\s+name=["\']generator["\'][^>]*joomla', content):
            site_type = "cms"
        elif re.search(r'<meta\s+name=["\']generator["\'][^>]*drupal', content):
            site_type = "cms"
        elif re.search(r'<meta\s+name=["\']generator["\'][^>]*mediawiki', content):
            site_type = "wiki"
        elif re.search(r'<meta\s+name=["\']generator["\'][^>]*phpbb', content):
            site_type = "forum"
        elif re.search(r'<meta\s+name=["\']generator["\'][^>]*vbulletin', content):
            site_type = "forum"
        elif re.search(r'<meta\s+name=["\']generator["\'][^>]*shopify', content):
            site_type = "ecommerce"
        elif re.search(r'<meta\s+name=["\']generator["\'][^>]*magento', content):
            site_type = "ecommerce"
        elif re.search(r'<meta\s+name=["\']generator["\'][^>]*woocommerce', content):
            site_type = "ecommerce"
        elif "forum" in url or "forum" in content_lower or "bbs" in url:
            site_type = "forum"
        elif "blog" in url or "blog" in content_lower:
            site_type = "blog"
        elif "wiki" in url or "wiki" in content_lower:
            site_type = "wiki"
        elif (
            "news" in url
            or "news" in content_lower
            or "tin tuc" in url
            or "tin-tuc" in url
        ):
            site_type = "news"
        elif "shop" in url or "store" in url or "product" in content_lower:
            site_type = "ecommerce"
        elif "edu" in domain or "ac." in domain or ".edu" in domain:
            site_type = "education"
        elif "gov" in domain or ".gov" in domain:
            site_type = "government"
        else:
            site_type = "general"

        # Lưu vào cache
        self.site_type_cache[domain] = site_type

        return site_type

    def _detect_language(self, content: str) -> str:
        """
        Phát hiện ngôn ngữ của nội dung.

        Args:
            content: Nội dung cần phát hiện ngôn ngữ

        Returns:
            str: Mã ngôn ngữ
        """
        # Sử dụng bộ phát hiện ngôn ngữ nếu có
        if self.language_detector:
            return self.language_detector.detect_language(content)

        # Phát hiện thủ công
        # Kiểm tra các từ tiếng Việt phổ biến
        vietnamese_words = [
            "của",
            "và",
            "là",
            "trong",
            "có",
            "được",
            "không",
            "những",
            "người",
            "để",
            "với",
            "các",
            "một",
            "về",
            "cho",
            "đã",
            "từ",
            "này",
            "khi",
        ]

        content_lower = content.lower()
        vietnamese_count = sum(1 for word in vietnamese_words if word in content_lower)

        # Nếu có nhiều từ tiếng Việt, đây có thể là nội dung tiếng Việt
        if vietnamese_count >= 5:
            return "vi"

        # Mặc định là tiếng Anh
        return "en"

    def _extract_metadata(self, url: str, content: str) -> Dict[str, Any]:
        """
        Trích xuất metadata từ nội dung.

        Args:
            url: URL của trang web
            content: Nội dung của trang web

        Returns:
            Dict[str, Any]: Metadata
        """
        metadata = {
            "title": "",
            "description": "",
            "keywords": [],
            "author": "",
            "published_date": "",
            "modified_date": "",
            "canonical_url": "",
            "og_title": "",
            "og_description": "",
            "og_image": "",
            "og_type": "",
            "twitter_card": "",
            "twitter_title": "",
            "twitter_description": "",
            "twitter_image": "",
        }

        # Trích xuất title
        title_match = re.search(
            r"<title[^>]*>(.*?)</title>", content, re.IGNORECASE | re.DOTALL
        )
        if title_match:
            metadata["title"] = title_match.group(1).strip()

        # Trích xuất meta tags
        meta_tags = re.findall(r"<meta\s+([^>]*)>", content, re.IGNORECASE)
        for meta_tag in meta_tags:
            # Description
            if 'name="description"' in meta_tag or "name='description'" in meta_tag:
                content_match = re.search(r'content=["\'](.*?)["\']', meta_tag)
                if content_match:
                    metadata["description"] = content_match.group(1).strip()

            # Keywords
            elif 'name="keywords"' in meta_tag or "name='keywords'" in meta_tag:
                content_match = re.search(r'content=["\'](.*?)["\']', meta_tag)
                if content_match:
                    keywords = content_match.group(1).strip()
                    metadata["keywords"] = [k.strip() for k in keywords.split(",")]

            # Author
            elif 'name="author"' in meta_tag or "name='author'" in meta_tag:
                content_match = re.search(r'content=["\'](.*?)["\']', meta_tag)
                if content_match:
                    metadata["author"] = content_match.group(1).strip()

            # Published date
            elif (
                'name="date"' in meta_tag
                or "name='date'" in meta_tag
                or 'name="published_date"' in meta_tag
            ):
                content_match = re.search(r'content=["\'](.*?)["\']', meta_tag)
                if content_match:
                    metadata["published_date"] = content_match.group(1).strip()

            # Modified date
            elif (
                'name="modified_date"' in meta_tag or "name='modified_date'" in meta_tag
            ):
                content_match = re.search(r'content=["\'](.*?)["\']', meta_tag)
                if content_match:
                    metadata["modified_date"] = content_match.group(1).strip()

            # Canonical URL
            elif 'rel="canonical"' in meta_tag or "rel='canonical'" in meta_tag:
                href_match = re.search(r'href=["\'](.*?)["\']', meta_tag)
                if href_match:
                    metadata["canonical_url"] = href_match.group(1).strip()

            # Open Graph tags
            elif 'property="og:title"' in meta_tag or "property='og:title'" in meta_tag:
                content_match = re.search(r'content=["\'](.*?)["\']', meta_tag)
                if content_match:
                    metadata["og_title"] = content_match.group(1).strip()

            elif (
                'property="og:description"' in meta_tag
                or "property='og:description'" in meta_tag
            ):
                content_match = re.search(r'content=["\'](.*?)["\']', meta_tag)
                if content_match:
                    metadata["og_description"] = content_match.group(1).strip()

            elif 'property="og:image"' in meta_tag or "property='og:image'" in meta_tag:
                content_match = re.search(r'content=["\'](.*?)["\']', meta_tag)
                if content_match:
                    metadata["og_image"] = content_match.group(1).strip()

            elif 'property="og:type"' in meta_tag or "property='og:type'" in meta_tag:
                content_match = re.search(r'content=["\'](.*?)["\']', meta_tag)
                if content_match:
                    metadata["og_type"] = content_match.group(1).strip()

            # Twitter Card tags
            elif 'name="twitter:card"' in meta_tag or "name='twitter:card'" in meta_tag:
                content_match = re.search(r'content=["\'](.*?)["\']', meta_tag)
                if content_match:
                    metadata["twitter_card"] = content_match.group(1).strip()

            elif (
                'name="twitter:title"' in meta_tag or "name='twitter:title'" in meta_tag
            ):
                content_match = re.search(r'content=["\'](.*?)["\']', meta_tag)
                if content_match:
                    metadata["twitter_title"] = content_match.group(1).strip()

            elif (
                'name="twitter:description"' in meta_tag
                or "name='twitter:description'" in meta_tag
            ):
                content_match = re.search(r'content=["\'](.*?)["\']', meta_tag)
                if content_match:
                    metadata["twitter_description"] = content_match.group(1).strip()

            elif (
                'name="twitter:image"' in meta_tag or "name='twitter:image'" in meta_tag
            ):
                content_match = re.search(r'content=["\'](.*?)["\']', meta_tag)
                if content_match:
                    metadata["twitter_image"] = content_match.group(1).strip()

        return metadata

    def _extract_links(self, url: str, content: str) -> List[Dict[str, Any]]:
        """
        Trích xuất links từ nội dung.

        Args:
            url: URL của trang web
            content: Nội dung của trang web

        Returns:
            List[Dict[str, Any]]: Danh sách links
        """
        links = []
        base_url = url

        # Trích xuất base tag
        base_match = re.search(r'<base\s+href=["\'](.*?)["\']', content, re.IGNORECASE)
        if base_match:
            base_url = base_match.group(1).strip()

        # Trích xuất a tags
        a_tags = re.findall(
            r"<a\s+([^>]*)>(.*?)</a>", content, re.IGNORECASE | re.DOTALL
        )
        for a_tag, a_text in a_tags:
            href_match = re.search(r'href=["\'](.*?)["\']', a_tag)
            if href_match:
                href = href_match.group(1).strip()

                # Bỏ qua các href không hợp lệ
                if not href or href.startswith("#") or href.startswith("javascript:"):
                    continue

                # Chuyển đổi relative URL thành absolute URL
                if not href.startswith("http"):
                    href = urljoin(base_url, href)

                # Trích xuất title
                title_match = re.search(r'title=["\'](.*?)["\']', a_tag)
                title = title_match.group(1).strip() if title_match else ""

                # Trích xuất text
                text = re.sub(r"<[^>]*>", "", a_text).strip()

                # Thêm vào danh sách links
                links.append(
                    {"url": href, "text": text, "title": title, "source_url": url}
                )

        return links

    def _extract_images(self, url: str, content: str) -> List[Dict[str, Any]]:
        """
        Trích xuất hình ảnh từ nội dung.

        Args:
            url: URL của trang web
            content: Nội dung của trang web

        Returns:
            List[Dict[str, Any]]: Danh sách hình ảnh
        """
        images = []
        base_url = url

        # Trích xuất base tag
        base_match = re.search(r'<base\s+href=["\'](.*?)["\']', content, re.IGNORECASE)
        if base_match:
            base_url = base_match.group(1).strip()

        # Trích xuất img tags
        img_tags = re.findall(r"<img\s+([^>]*)>", content, re.IGNORECASE)
        for img_tag in img_tags:
            src_match = re.search(r'src=["\'](.*?)["\']', img_tag)
            if src_match:
                src = src_match.group(1).strip()

                # Bỏ qua các src không hợp lệ
                if not src or src.startswith("data:"):
                    continue

                # Chuyển đổi relative URL thành absolute URL
                if not src.startswith("http"):
                    src = urljoin(base_url, src)

                # Trích xuất alt
                alt_match = re.search(r'alt=["\'](.*?)["\']', img_tag)
                alt = alt_match.group(1).strip() if alt_match else ""

                # Trích xuất title
                title_match = re.search(r'title=["\'](.*?)["\']', img_tag)
                title = title_match.group(1).strip() if title_match else ""

                # Trích xuất width và height
                width_match = re.search(r'width=["\'](.*?)["\']', img_tag)
                width = width_match.group(1).strip() if width_match else ""

                height_match = re.search(r'height=["\'](.*?)["\']', img_tag)
                height = height_match.group(1).strip() if height_match else ""

                # Thêm vào danh sách hình ảnh
                images.append(
                    {
                        "url": src,
                        "alt": alt,
                        "title": title,
                        "width": width,
                        "height": height,
                        "source_url": url,
                    }
                )

        return images

    def _extract_files(self, url: str, content: str) -> List[Dict[str, Any]]:
        """
        Trích xuất files từ nội dung.

        Args:
            url: URL của trang web
            content: Nội dung của trang web

        Returns:
            List[Dict[str, Any]]: Danh sách files
        """
        files = []
        base_url = url

        # Trích xuất base tag
        base_match = re.search(r'<base\s+href=["\'](.*?)["\']', content, re.IGNORECASE)
        if base_match:
            base_url = base_match.group(1).strip()

        # Các phần mở rộng file phổ biến
        file_extensions = [
            ".pdf",
            ".doc",
            ".docx",
            ".xls",
            ".xlsx",
            ".ppt",
            ".pptx",
            ".zip",
            ".rar",
            ".7z",
            ".tar",
            ".gz",
            ".csv",
            ".txt",
        ]

        # Trích xuất a tags
        a_tags = re.findall(
            r"<a\s+([^>]*)>(.*?)</a>", content, re.IGNORECASE | re.DOTALL
        )
        for a_tag, a_text in a_tags:
            href_match = re.search(r'href=["\'](.*?)["\']', a_tag)
            if href_match:
                href = href_match.group(1).strip()

                # Bỏ qua các href không hợp lệ
                if not href or href.startswith("#") or href.startswith("javascript:"):
                    continue

                # Kiểm tra phần mở rộng
                if not any(href.lower().endswith(ext) for ext in file_extensions):
                    continue

                # Chuyển đổi relative URL thành absolute URL
                if not href.startswith("http"):
                    href = urljoin(base_url, href)

                # Trích xuất text
                text = re.sub(r"<[^>]*>", "", a_text).strip()

                # Phân tích URL để lấy tên file
                parsed_url = urlparse(href)
                path = parsed_url.path
                filename = path.split("/")[-1]

                # Xác định loại file
                file_type = "unknown"
                for ext in file_extensions:
                    if filename.lower().endswith(ext):
                        file_type = ext[1:]  # Bỏ dấu chấm
                        break

                # Thêm vào danh sách files
                files.append(
                    {
                        "url": href,
                        "filename": filename,
                        "text": text,
                        "type": file_type,
                        "source_url": url,
                    }
                )

        return files

    def _extract_structured_data(self, url: str, content: str) -> Dict[str, Any]:
        """
        Trích xuất dữ liệu có cấu trúc từ nội dung.

        Args:
            url: URL của trang web
            content: Nội dung của trang web

        Returns:
            Dict[str, Any]: Dữ liệu có cấu trúc
        """
        structured_data = {"json_ld": [], "microdata": [], "rdfa": []}

        # Trích xuất JSON-LD
        json_ld_matches = re.findall(
            r'<script\s+type=["\'](application/ld\+json)["\'][^>]*>(.*?)</script>',
            content,
            re.IGNORECASE | re.DOTALL,
        )

        for _, json_ld_content in json_ld_matches:
            try:
                json_data = json.loads(json_ld_content)
                structured_data["json_ld"].append(json_data)
            except json.JSONDecodeError:
                pass

        # Trích xuất Microdata
        itemscope_elements = re.findall(
            r"<[^>]*itemscope[^>]*>", content, re.IGNORECASE
        )
        for element in itemscope_elements:
            itemtype_match = re.search(r'itemtype=["\'](.*?)["\']', element)
            if itemtype_match:
                itemtype = itemtype_match.group(1).strip()
                structured_data["microdata"].append({"type": itemtype})

        # Trích xuất RDFa
        rdfa_elements = re.findall(
            r'<[^>]*property=["\'](.*?)["\'][^>]*>', content, re.IGNORECASE
        )
        for property_value in rdfa_elements:
            structured_data["rdfa"].append({"property": property_value})

        return structured_data

    def _extract_images(self, url: str, content: str) -> List[Dict[str, Any]]:
        """
        Trích xuất hình ảnh từ nội dung.

        Args:
            url: URL của trang web
            content: Nội dung của trang web

        Returns:
            List[Dict[str, Any]]: Danh sách hình ảnh
        """
        images = []
        base_url = url

        # Trích xuất base tag
        base_match = re.search(r'<base\s+href=["\'](.*?)["\']', content, re.IGNORECASE)
        if base_match:
            base_url = base_match.group(1).strip()

        # Trích xuất img tags
        img_tags = re.findall(r"<img\s+([^>]*)>", content, re.IGNORECASE)
        for img_tag in img_tags:
            src_match = re.search(r'src=["\'](.*?)["\']', img_tag)
            if src_match:
                src = src_match.group(1).strip()

                # Bỏ qua các src không hợp lệ
                if not src or src.startswith("data:"):
                    continue

                # Chuyển đổi relative URL thành absolute URL
                if not src.startswith("http"):
                    src = urljoin(base_url, src)

                # Trích xuất alt
                alt_match = re.search(r'alt=["\'](.*?)["\']', img_tag)
                alt = alt_match.group(1).strip() if alt_match else ""

                # Trích xuất title
                title_match = re.search(r'title=["\'](.*?)["\']', img_tag)
                title = title_match.group(1).strip() if title_match else ""

                # Trích xuất width và height
                width_match = re.search(r'width=["\'](.*?)["\']', img_tag)
                width = width_match.group(1).strip() if width_match else ""

                height_match = re.search(r'height=["\'](.*?)["\']', img_tag)
                height = height_match.group(1).strip() if height_match else ""

                # Thêm vào danh sách hình ảnh
                images.append(
                    {
                        "url": src,
                        "alt": alt,
                        "title": title,
                        "width": width,
                        "height": height,
                        "source_url": url,
                    }
                )

        return images

    def _extract_files(self, url: str, content: str) -> List[Dict[str, Any]]:
        """
        Trích xuất files từ nội dung.

        Args:
            url: URL của trang web
            content: Nội dung của trang web

        Returns:
            List[Dict[str, Any]]: Danh sách files
        """
        files = []
        base_url = url

        # Trích xuất base tag
        base_match = re.search(r'<base\s+href=["\'](.*?)["\']', content, re.IGNORECASE)
        if base_match:
            base_url = base_match.group(1).strip()

        # Các phần mở rộng file phổ biến
        file_extensions = [
            ".pdf",
            ".doc",
            ".docx",
            ".xls",
            ".xlsx",
            ".ppt",
            ".pptx",
            ".zip",
            ".rar",
            ".7z",
            ".tar",
            ".gz",
            ".csv",
            ".txt",
        ]

        # Trích xuất a tags
        a_tags = re.findall(
            r"<a\s+([^>]*)>(.*?)</a>", content, re.IGNORECASE | re.DOTALL
        )
        for a_tag, a_text in a_tags:
            href_match = re.search(r'href=["\'](.*?)["\']', a_tag)
            if href_match:
                href = href_match.group(1).strip()

                # Bỏ qua các href không hợp lệ
                if not href or href.startswith("#") or href.startswith("javascript:"):
                    continue

                # Kiểm tra phần mở rộng
                if not any(href.lower().endswith(ext) for ext in file_extensions):
                    continue

                # Chuyển đổi relative URL thành absolute URL
                if not href.startswith("http"):
                    href = urljoin(base_url, href)

                # Trích xuất text
                text = re.sub(r"<[^>]*>", "", a_text).strip()

                # Phân tích URL để lấy tên file
                parsed_url = urlparse(href)
                path = parsed_url.path
                filename = path.split("/")[-1]

                # Xác định loại file
                file_type = "unknown"
                for ext in file_extensions:
                    if filename.lower().endswith(ext):
                        file_type = ext[1:]  # Bỏ dấu chấm
                        break

                # Thêm vào danh sách files
                files.append(
                    {
                        "url": href,
                        "filename": filename,
                        "text": text,
                        "type": file_type,
                        "source_url": url,
                    }
                )

        return files

    def _is_allowed_by_robots_txt(self, url: str) -> bool:
        """
        Kiểm tra xem URL có được phép bởi robots.txt hay không.

        Args:
            url: URL cần kiểm tra

        Returns:
            bool: True nếu được phép, False nếu không
        """
        if not self.respect_robots_txt:
            return True

        # Phân tích URL
        parsed_url = urlparse(url)
        domain = parsed_url.netloc
        path = parsed_url.path

        # Kiểm tra cache
        if domain in self.robots_txt_cache:
            robots_txt = self.robots_txt_cache[domain]
        else:
            # Tải robots.txt
            robots_url = f"{parsed_url.scheme}://{domain}/robots.txt"
            try:
                response = self.session.get(
                    robots_url, timeout=self.timeout, verify=self.verify_ssl
                )

                if response.status_code == 200:
                    robots_txt = response.text
                else:
                    # Không tìm thấy robots.txt, cho phép tất cả
                    robots_txt = ""

                # Lưu vào cache
                self.robots_txt_cache[domain] = robots_txt
            except Exception:
                # Lỗi khi tải robots.txt, cho phép tất cả
                robots_txt = ""
                self.robots_txt_cache[domain] = robots_txt

        # Kiểm tra quy tắc
        user_agent = "*"  # Mặc định

        # Phân tích robots.txt
        current_agent = None
        disallowed_paths = []

        for line in robots_txt.splitlines():
            line = line.strip()

            # Bỏ qua comment và dòng trống
            if not line or line.startswith("#"):
                continue

            # Phân tích dòng
            parts = line.split(":", 1)
            if len(parts) != 2:
                continue

            directive = parts[0].strip().lower()
            value = parts[1].strip()

            # Xử lý User-agent
            if directive == "user-agent":
                current_agent = value
                if current_agent == user_agent:
                    disallowed_paths = []

            # Xử lý Disallow
            elif directive == "disallow" and (
                current_agent == user_agent or current_agent == "*"
            ):
                if value:
                    disallowed_paths.append(value)

        # Kiểm tra path
        for disallowed_path in disallowed_paths:
            if path.startswith(disallowed_path):
                return False

        return True

    def _get_cache_key(self, url: str) -> str:
        """
        Tạo khóa cache cho URL.

        Args:
            url: URL cần tạo khóa cache

        Returns:
            str: Khóa cache
        """
        # Sử dụng MD5 để tạo khóa cache
        return hashlib.md5(url.encode()).hexdigest()

    def _get_from_cache(self, key: str) -> Optional[Dict[str, Any]]:
        """
        Lấy kết quả từ cache.

        Args:
            key: Khóa cache

        Returns:
            Optional[Dict[str, Any]]: Kết quả từ cache hoặc None nếu không tìm thấy
        """
        if not self.cache_enabled:
            return None

        # Kiểm tra cache
        if key in self.content_cache:
            cache_entry = self.content_cache[key]

            # Kiểm tra thời gian sống
            if time.time() - cache_entry["timestamp"] < self.cache_ttl:
                return cache_entry["data"]

            # Xóa cache hết hạn
            del self.content_cache[key]

        return None

    def _add_to_cache(self, key: str, data: Dict[str, Any]) -> None:
        """
        Thêm kết quả vào cache.

        Args:
            key: Khóa cache
            data: Dữ liệu cần lưu vào cache
        """

        if not self.cache_enabled:
            return

        # Thêm vào cache
        self.content_cache[key] = {"timestamp": time.time(), "data": data}

        # Kiểm tra kích thước cache
        if len(self.content_cache) > self.cache_size:
            # Xóa mục cũ nhất
            oldest_key = min(
                self.content_cache.keys(),
                key=lambda k: self.content_cache[k]["timestamp"],
            )
            del self.content_cache[oldest_key]

    def _get_cache_key(self, url: str) -> str:
        """
        Tạo khóa cache cho URL.

        Args:
            url: URL cần tạo khóa cache

        Returns:
            str: Khóa cache
        """
        # Sử dụng MD5 để tạo khóa cache
        return hashlib.md5(url.encode()).hexdigest()

    def _get_from_cache(self, key: str) -> Optional[Dict[str, Any]]:
        """
        Lấy kết quả từ cache.

        Args:
            key: Khóa cache

        Returns:
            Optional[Dict[str, Any]]: Kết quả từ cache hoặc None nếu không tìm thấy
        """
        if not self.cache_enabled:
            return None

        # Kiểm tra cache
        if key in self.content_cache:
            cache_entry = self.content_cache[key]

            # Kiểm tra thời gian sống
            if time.time() - cache_entry["timestamp"] < self.cache_ttl:
                return cache_entry["data"]

            # Xóa cache hết hạn
            del self.content_cache[key]

        return None

    def _add_to_cache(self, key: str, data: Dict[str, Any]) -> None:
        """
        Thêm kết quả vào cache.

        Args:
            key: Khóa cache
            data: Dữ liệu cần lưu vào cache
        """
        if not self.cache_enabled:
            return

        # Thêm vào cache
        self.content_cache[key] = {"timestamp": time.time(), "data": data}

        # Kiểm tra kích thước cache
        if len(self.content_cache) > self.cache_size:
            # Xóa mục cũ nhất
            oldest_key = min(
                self.content_cache.keys(),
                key=lambda k: self.content_cache[k]["timestamp"],
            )
            del self.content_cache[oldest_key]
