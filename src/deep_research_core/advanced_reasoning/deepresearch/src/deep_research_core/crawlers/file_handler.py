#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module cung cấp các lớp và phương thức để xử lý việc trích xuất và tải xuống file.

Module này cung cấp các lớp và phương thức để xử lý việc trích xuất và tải xuống file
từ các trang web, bao gồm các định dạng file khác nhau như PDF, DOCX, XLSX, v.v.
"""

import os
import re
import time
import logging
import requests
import mimetypes
import hashlib
from urllib.parse import urlparse, urljoin
from typing import Dict, List, Any, Optional, Tuple, Set, Union
from bs4 import BeautifulSoup
import concurrent.futures

from ..utils.structured_logging import get_logger

# Thiết lập logging
logger = get_logger(__name__)


class FileHandler:
    """
    Lớp cung cấp các phư<PERSON><PERSON> thức để xử lý việc trích xuất và tải xuống file.

    Tính năng:
    - Trích xuất file từ trang web
    - Tải xuống file
    - Phân loại file
    - Trích xuất nội dung từ file
    - Xử lý các định dạng file khác nhau
    """

    def __init__(
        self,
        download_path: str = "downloads/files",
        max_size_mb: int = 50,
        timeout: float = 30.0,
        max_retries: int = 3,
        retry_delay: float = 2.0,
        user_agent: Optional[str] = None,
        verify_ssl: bool = True,
        proxies: Optional[Dict[str, str]] = None,
        cookies: Optional[Dict[str, str]] = None,
        headers: Optional[Dict[str, str]] = None,
        file_extensions: Optional[List[str]] = None,
        extract_file_content: bool = True,
        max_files_per_page: int = 100,
        max_concurrent_downloads: int = 5,
        organize_by_type: bool = True,
        create_subfolders: bool = True,
        skip_existing: bool = True,
        verbose: bool = False,
    ):
        """
        Khởi tạo FileHandler.

        Args:
            download_path: Đường dẫn để lưu file
            max_size_mb: Kích thước tối đa của file (MB)
            timeout: Thời gian timeout cho mỗi request (giây)
            max_retries: Số lần thử lại tối đa khi request thất bại
            retry_delay: Thời gian chờ giữa các lần thử lại (giây)
            user_agent: User-Agent header
            verify_ssl: Xác minh chứng chỉ SSL hay không
            proxies: Danh sách proxy
            cookies: Cookies cho request
            headers: Headers cho request
            file_extensions: Danh sách các phần mở rộng file được hỗ trợ
            extract_file_content: Trích xuất nội dung từ file hay không
            max_files_per_page: Số lượng file tối đa trên mỗi trang
            max_concurrent_downloads: Số lượng tải xuống đồng thời tối đa
            organize_by_type: Tổ chức file theo loại hay không
            create_subfolders: Tạo thư mục con hay không
            skip_existing: Bỏ qua file đã tồn tại hay không
            verbose: Ghi log chi tiết hay không
        """
        # Cấu hình
        self.download_path = download_path
        self.max_size_mb = max_size_mb
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.user_agent = user_agent or "FileHandler/1.0"
        self.verify_ssl = verify_ssl
        self.proxies = proxies or {}
        self.cookies = cookies or {}
        self.headers = headers or {}
        self.file_extensions = file_extensions or [
            ".pdf",
            ".doc",
            ".docx",
            ".xls",
            ".xlsx",
            ".ppt",
            ".pptx",
            ".zip",
            ".rar",
            ".7z",
            ".tar",
            ".gz",
            ".csv",
            ".txt",
            ".rtf",
            ".odt",
            ".ods",
            ".odp",
            ".epub",
            ".mobi",
            ".djvu",
            ".chm",
            ".xml",
            ".json",
            ".yaml",
            ".yml",
            ".md",
            ".markdown",
            ".tex",
            ".bib",
            ".sql",
            ".db",
            ".sqlite",
            ".accdb",
            ".mdb",
            ".psd",
            ".ai",
            ".indd",
            ".svg",
            ".eps",
            ".ps",
            ".ttf",
            ".otf",
            ".woff",
            ".woff2",
            ".eot",
        ]
        self.extract_file_content = extract_file_content
        self.max_files_per_page = max_files_per_page
        self.max_concurrent_downloads = max_concurrent_downloads
        self.organize_by_type = organize_by_type
        self.create_subfolders = create_subfolders
        self.skip_existing = skip_existing
        self.verbose = verbose

        # Khởi tạo session
        self.session = requests.Session()
        self.session.headers.update({"User-Agent": self.user_agent})
        if self.cookies:
            self.session.cookies.update(self.cookies)
        if self.headers:
            self.session.headers.update(self.headers)
        if self.proxies:
            self.session.proxies.update(self.proxies)

        # Tạo thư mục lưu trữ nếu chưa tồn tại
        os.makedirs(self.download_path, exist_ok=True)

        # Khởi tạo các thuộc tính khác
        self.stats = {
            "total_files_extracted": 0,
            "total_files_downloaded": 0,
            "total_download_size": 0,
            "failed_downloads": 0,
            "skipped_downloads": 0,
            "file_types": {},
        }

        # Khởi tạo executor
        self.executor = None

    def extract_files(
        self, content: str, url: str, page: Optional[Any] = None
    ) -> List[Dict[str, Any]]:
        """
        Trích xuất file từ nội dung.

        Args:
            content: Nội dung HTML
            url: URL của trang web
            page: Đối tượng trang web (nếu sử dụng Playwright/Selenium)

        Returns:
            List[Dict[str, Any]]: Danh sách file
        """
        files = []
        base_url = url

        # Trích xuất base tag
        base_match = re.search(r'<base\s+href=["\'](.*?)["\']', content, re.IGNORECASE)
        if base_match:
            base_url = base_match.group(1).strip()

        # Sử dụng BeautifulSoup để phân tích HTML
        soup = BeautifulSoup(content, "html.parser")

        # Tìm tất cả thẻ a
        links = soup.find_all("a")
        
        # Giới hạn số lượng file
        links = links[:self.max_files_per_page]
        
        # Phân tích từng link
        for link in links:
            href = link.get("href", "")
            
            # Bỏ qua các href không hợp lệ
            if not href or href.startswith("#") or href.startswith("javascript:"):
                continue
            
            # Chuyển đổi relative URL thành absolute URL
            if not href.startswith("http"):
                href = urljoin(base_url, href)
            
            # Kiểm tra phần mở rộng
            parsed_url = urlparse(href)
            path = parsed_url.path
            filename = path.split("/")[-1]
            
            # Kiểm tra xem có phải là file không
            is_file = False
            file_extension = ""
            
            # Kiểm tra phần mở rộng
            for ext in self.file_extensions:
                if filename.lower().endswith(ext):
                    is_file = True
                    file_extension = ext
                    break
            
            # Nếu là file, thêm vào danh sách
            if is_file:
                # Lấy text của link
                text = link.get_text().strip()
                
                # Lấy title của link
                title = link.get("title", "")
                
                # Xác định loại file
                file_type = self._get_file_type(file_extension)
                
                # Thêm vào danh sách file
                files.append(
                    {
                        "url": href,
                        "filename": filename,
                        "text": text,
                        "title": title,
                        "extension": file_extension,
                        "type": file_type,
                        "source_url": url,
                    }
                )
                
                # Cập nhật thống kê
                self.stats["total_files_extracted"] += 1
                self.stats["file_types"][file_type] = self.stats["file_types"].get(file_type, 0) + 1

        return files

    def download_file(
        self, url: str, save_path: Optional[str] = None, file_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Tải xuống file.

        Args:
            url: URL của file
            save_path: Đường dẫn để lưu file
            file_type: Loại file

        Returns:
            Dict[str, Any]: Kết quả tải xuống
        """
        # Khởi tạo kết quả
        result = {
            "success": False,
            "url": url,
            "file_path": "",
            "file_size": 0,
            "content_type": "",
            "download_time": 0,
            "error": "",
        }

        # Xác định đường dẫn lưu
        if not save_path:
            # Tạo tên file từ URL
            parsed_url = urlparse(url)
            path = parsed_url.path
            filename = path.split("/")[-1]
            
            # Nếu không có tên file, tạo tên file từ hash của URL
            if not filename or "." not in filename:
                filename = hashlib.md5(url.encode()).hexdigest()
                
                # Thêm phần mở rộng dựa trên file_type
                if file_type:
                    if file_type == "pdf":
                        filename += ".pdf"
                    elif file_type == "document":
                        filename += ".docx"
                    elif file_type == "spreadsheet":
                        filename += ".xlsx"
                    elif file_type == "presentation":
                        filename += ".pptx"
                    elif file_type == "archive":
                        filename += ".zip"
                    elif file_type == "text":
                        filename += ".txt"
                    else:
                        filename += ".bin"
            
            # Xác định thư mục lưu
            if self.organize_by_type and file_type:
                save_dir = os.path.join(self.download_path, file_type)
            else:
                save_dir = self.download_path
            
            # Tạo thư mục nếu chưa tồn tại
            os.makedirs(save_dir, exist_ok=True)
            
            # Xác định đường dẫn lưu
            save_path = os.path.join(save_dir, filename)

        # Kiểm tra xem file đã tồn tại chưa
        if self.skip_existing and os.path.exists(save_path):
            result["success"] = True
            result["file_path"] = save_path
            result["file_size"] = os.path.getsize(save_path)
            result["error"] = "File already exists"
            self.stats["skipped_downloads"] += 1
            return result

        # Tạo thư mục cha nếu chưa tồn tại
        os.makedirs(os.path.dirname(save_path), exist_ok=True)

        # Thực hiện tải xuống
        start_time = time.time()
        
        try:
            # Tải xuống với stream để kiểm tra kích thước
            with self.session.get(
                url,
                stream=True,
                timeout=self.timeout,
                verify=self.verify_ssl,
                headers=self.headers,
                proxies=self.proxies,
                cookies=self.cookies,
            ) as response:
                # Kiểm tra status code
                if response.status_code != 200:
                    result["error"] = f"HTTP error: {response.status_code}"
                    self.stats["failed_downloads"] += 1
                    return result
                
                # Lấy content type
                content_type = response.headers.get("Content-Type", "")
                result["content_type"] = content_type
                
                # Kiểm tra kích thước
                content_length = int(response.headers.get("Content-Length", 0))
                if content_length > self.max_size_mb * 1024 * 1024:
                    result["error"] = f"File too large: {content_length} bytes"
                    self.stats["skipped_downloads"] += 1
                    return result
                
                # Tải xuống file
                with open(save_path, "wb") as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                
                # Lấy kích thước file
                file_size = os.path.getsize(save_path)
                result["file_size"] = file_size
                
                # Cập nhật kết quả
                result["success"] = True
                result["file_path"] = save_path
                
                # Cập nhật thống kê
                self.stats["total_files_downloaded"] += 1
                self.stats["total_download_size"] += file_size
        except Exception as e:
            result["error"] = str(e)
            self.stats["failed_downloads"] += 1
            return result
        
        # Tính thời gian tải xuống
        end_time = time.time()
        result["download_time"] = end_time - start_time
        
        return result

    def download_files(
        self, files: List[Dict[str, Any]], concurrent: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Tải xuống nhiều file.

        Args:
            files: Danh sách file cần tải xuống
            concurrent: Tải xuống đồng thời hay không

        Returns:
            List[Dict[str, Any]]: Danh sách kết quả tải xuống
        """
        results = []
        
        if concurrent and self.max_concurrent_downloads > 1:
            # Tải xuống đồng thời
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_concurrent_downloads) as executor:
                # Tạo các future
                futures = []
                for file in files:
                    future = executor.submit(
                        self.download_file,
                        url=file["url"],
                        file_type=file["type"],
                    )
                    futures.append(future)
                
                # Lấy kết quả
                for future in concurrent.futures.as_completed(futures):
                    result = future.result()
                    results.append(result)
        else:
            # Tải xuống tuần tự
            for file in files:
                result = self.download_file(
                    url=file["url"],
                    file_type=file["type"],
                )
                results.append(result)
        
        return results

    def extract_file_content(self, file_path: str) -> Dict[str, Any]:
        """
        Trích xuất nội dung từ file.

        Args:
            file_path: Đường dẫn đến file

        Returns:
            Dict[str, Any]: Nội dung file
        """
        # Khởi tạo kết quả
        result = {
            "success": False,
            "file_path": file_path,
            "content": "",
            "metadata": {},
            "error": "",
        }
        
        # Kiểm tra xem file có tồn tại không
        if not os.path.exists(file_path):
            result["error"] = "File not found"
            return result
        
        # Lấy phần mở rộng
        _, file_extension = os.path.splitext(file_path)
        file_extension = file_extension.lower()
        
        try:
            # Xử lý từng loại file
            if file_extension == ".pdf":
                # Trích xuất nội dung từ PDF
                result = self._extract_pdf_content(file_path)
            elif file_extension in [".doc", ".docx"]:
                # Trích xuất nội dung từ Word
                result = self._extract_word_content(file_path)
            elif file_extension in [".xls", ".xlsx"]:
                # Trích xuất nội dung từ Excel
                result = self._extract_excel_content(file_path)
            elif file_extension in [".ppt", ".pptx"]:
                # Trích xuất nội dung từ PowerPoint
                result = self._extract_powerpoint_content(file_path)
            elif file_extension in [".txt", ".csv", ".md", ".markdown", ".xml", ".json", ".yaml", ".yml"]:
                # Trích xuất nội dung từ file văn bản
                result = self._extract_text_content(file_path)
            else:
                # Không hỗ trợ loại file này
                result["error"] = f"Unsupported file type: {file_extension}"
        except Exception as e:
            result["error"] = str(e)
        
        return result

    def _extract_pdf_content(self, file_path: str) -> Dict[str, Any]:
        """
        Trích xuất nội dung từ file PDF.

        Args:
            file_path: Đường dẫn đến file PDF

        Returns:
            Dict[str, Any]: Nội dung file PDF
        """
        # Khởi tạo kết quả
        result = {
            "success": False,
            "file_path": file_path,
            "content": "",
            "metadata": {},
            "error": "",
        }
        
        try:
            # Sử dụng PyPDF2 để trích xuất nội dung
            import PyPDF2
            
            with open(file_path, "rb") as f:
                # Tạo PDF reader
                pdf_reader = PyPDF2.PdfReader(f)
                
                # Lấy số trang
                num_pages = len(pdf_reader.pages)
                
                # Lấy metadata
                metadata = pdf_reader.metadata
                if metadata:
                    result["metadata"] = {
                        "title": metadata.get("/Title", ""),
                        "author": metadata.get("/Author", ""),
                        "subject": metadata.get("/Subject", ""),
                        "creator": metadata.get("/Creator", ""),
                        "producer": metadata.get("/Producer", ""),
                        "creation_date": metadata.get("/CreationDate", ""),
                        "modification_date": metadata.get("/ModDate", ""),
                    }
                
                # Trích xuất nội dung từ mỗi trang
                content = ""
                for page_num in range(num_pages):
                    page = pdf_reader.pages[page_num]
                    content += page.extract_text() + "\n\n"
                
                # Cập nhật kết quả
                result["success"] = True
                result["content"] = content
        except ImportError:
            result["error"] = "PyPDF2 not installed"
        except Exception as e:
            result["error"] = str(e)
        
        return result

    def _extract_word_content(self, file_path: str) -> Dict[str, Any]:
        """
        Trích xuất nội dung từ file Word.

        Args:
            file_path: Đường dẫn đến file Word

        Returns:
            Dict[str, Any]: Nội dung file Word
        """
        # Khởi tạo kết quả
        result = {
            "success": False,
            "file_path": file_path,
            "content": "",
            "metadata": {},
            "error": "",
        }
        
        try:
            # Sử dụng python-docx để trích xuất nội dung
            import docx
            
            # Mở file
            doc = docx.Document(file_path)
            
            # Lấy metadata
            core_properties = doc.core_properties
            result["metadata"] = {
                "title": core_properties.title or "",
                "author": core_properties.author or "",
                "subject": core_properties.subject or "",
                "keywords": core_properties.keywords or "",
                "category": core_properties.category or "",
                "comments": core_properties.comments or "",
                "created": str(core_properties.created) if core_properties.created else "",
                "modified": str(core_properties.modified) if core_properties.modified else "",
            }
            
            # Trích xuất nội dung
            content = ""
            for paragraph in doc.paragraphs:
                content += paragraph.text + "\n"
            
            # Cập nhật kết quả
            result["success"] = True
            result["content"] = content
        except ImportError:
            result["error"] = "python-docx not installed"
        except Exception as e:
            result["error"] = str(e)
        
        return result

    def _extract_excel_content(self, file_path: str) -> Dict[str, Any]:
        """
        Trích xuất nội dung từ file Excel.

        Args:
            file_path: Đường dẫn đến file Excel

        Returns:
            Dict[str, Any]: Nội dung file Excel
        """
        # Khởi tạo kết quả
        result = {
            "success": False,
            "file_path": file_path,
            "content": "",
            "metadata": {},
            "error": "",
        }
        
        try:
            # Sử dụng openpyxl để trích xuất nội dung
            import openpyxl
            
            # Mở file
            wb = openpyxl.load_workbook(file_path, read_only=True)
            
            # Lấy metadata
            result["metadata"] = {
                "title": wb.properties.title or "",
                "subject": wb.properties.subject or "",
                "creator": wb.properties.creator or "",
                "keywords": wb.properties.keywords or "",
                "description": wb.properties.description or "",
                "category": wb.properties.category or "",
                "created": str(wb.properties.created) if wb.properties.created else "",
                "modified": str(wb.properties.modified) if wb.properties.modified else "",
            }
            
            # Trích xuất nội dung
            content = ""
            for sheet_name in wb.sheetnames:
                sheet = wb[sheet_name]
                content += f"Sheet: {sheet_name}\n"
                
                for row in sheet.rows:
                    row_values = [str(cell.value) if cell.value is not None else "" for cell in row]
                    content += "\t".join(row_values) + "\n"
                
                content += "\n"
            
            # Cập nhật kết quả
            result["success"] = True
            result["content"] = content
        except ImportError:
            result["error"] = "openpyxl not installed"
        except Exception as e:
            result["error"] = str(e)
        
        return result

    def _extract_powerpoint_content(self, file_path: str) -> Dict[str, Any]:
        """
        Trích xuất nội dung từ file PowerPoint.

        Args:
            file_path: Đường dẫn đến file PowerPoint

        Returns:
            Dict[str, Any]: Nội dung file PowerPoint
        """
        # Khởi tạo kết quả
        result = {
            "success": False,
            "file_path": file_path,
            "content": "",
            "metadata": {},
            "error": "",
        }
        
        try:
            # Sử dụng python-pptx để trích xuất nội dung
            import pptx
            
            # Mở file
            presentation = pptx.Presentation(file_path)
            
            # Lấy metadata
            core_properties = presentation.core_properties
            result["metadata"] = {
                "title": core_properties.title or "",
                "author": core_properties.author or "",
                "subject": core_properties.subject or "",
                "keywords": core_properties.keywords or "",
                "category": core_properties.category or "",
                "comments": core_properties.comments or "",
                "created": str(core_properties.created) if core_properties.created else "",
                "modified": str(core_properties.modified) if core_properties.modified else "",
            }
            
            # Trích xuất nội dung
            content = ""
            for i, slide in enumerate(presentation.slides):
                content += f"Slide {i+1}:\n"
                
                for shape in slide.shapes:
                    if hasattr(shape, "text"):
                        content += shape.text + "\n"
                
                content += "\n"
            
            # Cập nhật kết quả
            result["success"] = True
            result["content"] = content
        except ImportError:
            result["error"] = "python-pptx not installed"
        except Exception as e:
            result["error"] = str(e)
        
        return result

    def _extract_text_content(self, file_path: str) -> Dict[str, Any]:
        """
        Trích xuất nội dung từ file văn bản.

        Args:
            file_path: Đường dẫn đến file văn bản

        Returns:
            Dict[str, Any]: Nội dung file văn bản
        """
        # Khởi tạo kết quả
        result = {
            "success": False,
            "file_path": file_path,
            "content": "",
            "metadata": {},
            "error": "",
        }
        
        try:
            # Mở file
            with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
                content = f.read()
            
            # Cập nhật kết quả
            result["success"] = True
            result["content"] = content
            
            # Lấy metadata
            result["metadata"] = {
                "size": os.path.getsize(file_path),
                "created": os.path.getctime(file_path),
                "modified": os.path.getmtime(file_path),
            }
        except Exception as e:
            result["error"] = str(e)
        
        return result

    def _get_file_type(self, extension: str) -> str:
        """
        Xác định loại file từ phần mở rộng.

        Args:
            extension: Phần mở rộng của file

        Returns:
            str: Loại file
        """
        extension = extension.lower()
        
        # Phân loại file
        if extension in [".pdf"]:
            return "pdf"
        elif extension in [".doc", ".docx", ".odt", ".rtf"]:
            return "document"
        elif extension in [".xls", ".xlsx", ".ods", ".csv"]:
            return "spreadsheet"
        elif extension in [".ppt", ".pptx", ".odp"]:
            return "presentation"
        elif extension in [".zip", ".rar", ".7z", ".tar", ".gz"]:
            return "archive"
        elif extension in [".txt", ".md", ".markdown", ".xml", ".json", ".yaml", ".yml"]:
            return "text"
        elif extension in [".epub", ".mobi", ".djvu", ".chm"]:
            return "ebook"
        elif extension in [".sql", ".db", ".sqlite", ".accdb", ".mdb"]:
            return "database"
        elif extension in [".psd", ".ai", ".indd", ".svg", ".eps", ".ps"]:
            return "design"
        elif extension in [".ttf", ".otf", ".woff", ".woff2", ".eot"]:
            return "font"
        else:
            return "other"
