#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module cung cấp các hàm tích hợp CaptchaHandler với AdaptiveCrawler.

Module này cung cấp các hàm để tích hợp CaptchaHandler với AdaptiveCrawler,
cho phép AdaptiveCrawler sử dụng CaptchaHandler để xử lý CAPTCHA.
"""

import logging
import time
import traceback
import os
import json
from typing import Dict, List, Any, Optional, Tuple, Union, Callable

from ..utils.structured_logging import get_logger
from ..utils.error_handling import (
    SearchError,
    RateLimitError,
    ConnectionError,
    ContentExtractionError,
    BotDetectionError,
    TimeoutError,
    format_error_response
)

# Import CaptchaHandler nếu có thể
try:
    from ..utils.captcha_handler import CaptchaHandler
    CAPTCHA_HANDLER_AVAILABLE = True
except ImportError:
    CAPTCHA_HANDLER_AVAILABLE = False
    logger = get_logger(__name__)
    logger.warning("CaptchaHandler không khả dụng. Xử lý CAPTCHA sẽ bị hạn chế.")

# Thiết lập logging
logger = get_logger(__name__)

# Định nghĩa các lỗi cụ thể cho CaptchaHandler
class CaptchaHandlerError(Exception):
    """Lỗi cơ bản cho CaptchaHandler."""
    def __init__(self, message: str, url: str = None, details: Dict[str, Any] = None):
        self.message = message
        self.url = url
        self.details = details or {}
        super().__init__(self.message)

class CaptchaHandlerNotAvailableError(CaptchaHandlerError):
    """Lỗi khi CaptchaHandler không khả dụng."""
    pass

class CaptchaHandlerConfigError(CaptchaHandlerError):
    """Lỗi cấu hình CaptchaHandler."""
    pass

class CaptchaHandlerDetectionError(CaptchaHandlerError):
    """Lỗi phát hiện CAPTCHA."""
    pass

class CaptchaHandlerSolvingError(CaptchaHandlerError):
    """Lỗi giải CAPTCHA."""
    pass

# Hàm xử lý lỗi
def handle_captcha_handler_error(e: Exception, url: str = None, context: str = None) -> Dict[str, Any]:
    """
    Xử lý lỗi từ CaptchaHandler và trả về kết quả lỗi có cấu trúc.

    Args:
        e: Exception đã xảy ra
        url: URL đang được xử lý
        context: Ngữ cảnh của lỗi

    Returns:
        Dict[str, Any]: Kết quả lỗi có cấu trúc
    """
    error_type = type(e).__name__
    error_message = str(e)
    error_traceback = traceback.format_exc()

    # Ghi log lỗi
    logger.error(f"CaptchaHandler error in {context or 'unknown context'}: {error_type}: {error_message}")
    if logger.isEnabledFor(logging.DEBUG):
        logger.debug(f"Traceback: {error_traceback}")

    # Phân loại lỗi
    if isinstance(e, CaptchaHandlerError):
        error_category = "captcha_handler_error"
        error_details = e.details if hasattr(e, "details") else {}
        error_url = e.url if hasattr(e, "url") else url
    elif isinstance(e, ConnectionError) or "ConnectionError" in error_type:
        error_category = "connection_error"
        error_details = {"traceback": error_traceback}
        error_url = url
    elif isinstance(e, TimeoutError) or "TimeoutError" in error_type or "timeout" in error_message.lower():
        error_category = "timeout_error"
        error_details = {"traceback": error_traceback}
        error_url = url
    else:
        error_category = "unknown_error"
        error_details = {"traceback": error_traceback}
        error_url = url

    # Tạo kết quả lỗi
    error_result = {
        "success": False,
        "url": error_url,
        "error": error_message,
        "error_type": error_type,
        "error_category": error_category,
        "error_details": error_details,
        "content": f"[Error: {error_message} for URL: {error_url}]"
    }

    return error_result

def integrate_captcha_handler(crawler, config: Optional[Dict[str, Any]] = None) -> None:
    """
    Tích hợp CaptchaHandler vào AdaptiveCrawler.

    Args:
        crawler: AdaptiveCrawler instance
        config: Cấu hình cho CaptchaHandler
    """
    if not CAPTCHA_HANDLER_AVAILABLE:
        logger.warning("CaptchaHandler không khả dụng. Xử lý CAPTCHA sẽ bị hạn chế.")
        crawler.has_captcha_handler = False
        return

    # Cấu hình mặc định
    default_config = {
        "max_retries": 5
    }

    # Kết hợp cấu hình mặc định và cấu hình người dùng
    captcha_config = default_config.copy()
    if config:
        captcha_config.update(config)

    # Khởi tạo CaptchaHandler
    try:
        crawler._captcha_handler = CaptchaHandler(**captcha_config)
        crawler.has_captcha_handler = True
        logger.info("CaptchaHandler initialized successfully")

        # Lưu phương thức gốc
        if hasattr(crawler, '_is_captcha'):
            crawler._original_is_captcha = crawler._is_captcha
        if hasattr(crawler, '_solve_captcha'):
            crawler._original_solve_captcha = crawler._solve_captcha

        # Thay thế phương thức _is_captcha và _solve_captcha
        crawler._is_captcha = lambda content: is_captcha(crawler, content)
        crawler._solve_captcha = lambda url, response: solve_captcha(crawler, url, response)

        # Lưu cấu hình đã sử dụng
        crawler._captcha_handler_config = captcha_config

        # Khởi tạo các thuộc tính cần thiết
        crawler.captcha_domains = set()
        crawler.last_captcha_detection = {}

        logger.info("CaptchaHandler đã được tích hợp thành công vào AdaptiveCrawler")
    except Exception as e:
        logger.error(f"Failed to integrate CaptchaHandler: {str(e)}")
        crawler.has_captcha_handler = False

def is_captcha(crawler, content: str) -> bool:
    """
    Kiểm tra xem nội dung có chứa CAPTCHA hay không.

    Args:
        crawler: AdaptiveCrawler instance
        content: Nội dung cần kiểm tra

    Returns:
        bool: True nếu phát hiện CAPTCHA, False nếu không
    """
    if not hasattr(crawler, "_captcha_handler") or crawler._captcha_handler is None:
        # Sử dụng phương thức mặc định nếu không có CaptchaHandler
        return False

    try:
        # Sử dụng CaptchaHandler để phát hiện CAPTCHA
        has_captcha, captcha_type, captcha_data = crawler._captcha_handler.detect_captcha(content)
        return has_captcha
    except Exception as e:
        logger.error(f"Error detecting CAPTCHA: {str(e)}")
        # Trả về False nếu có lỗi
        return False

def solve_captcha(crawler, url: str, response: Dict[str, Any]) -> bool:
    """
    Thử giải CAPTCHA.

    Args:
        crawler: AdaptiveCrawler instance
        url: URL chứa CAPTCHA
        response: Response chứa CAPTCHA

    Returns:
        bool: True nếu giải thành công, False nếu không
    """
    if not hasattr(crawler, "_captcha_handler") or crawler._captcha_handler is None:
        # Sử dụng phương thức mặc định nếu không có CaptchaHandler
        return False

    try:
        content = response.get("content", "")

        # Phát hiện CAPTCHA
        has_captcha, captcha_type, captcha_data = crawler._captcha_handler.detect_captcha(content)

        if not has_captcha:
            return False

        # Ghi nhận domain có CAPTCHA
        from urllib.parse import urlparse
        domain = urlparse(url).netloc
        if not hasattr(crawler, 'captcha_domains'):
            crawler.captcha_domains = set()
        crawler.captcha_domains.add(domain)

        # Ghi nhận thời gian phát hiện CAPTCHA
        if not hasattr(crawler, 'last_captcha_detection'):
            crawler.last_captcha_detection = {}
        crawler.last_captcha_detection[url] = time.time()

        # Xử lý CAPTCHA
        result = crawler._captcha_handler.handle_captcha(url, content)

        # Kiểm tra kết quả
        if result.get("success", False) and result.get("handled", False):
            return True

        return False
    except Exception as e:
        logger.error(f"Error solving CAPTCHA: {str(e)}")
        # Trả về False nếu có lỗi
        return False

def is_domain_known_captcha(crawler, url: str) -> bool:
    """
    Kiểm tra xem domain có phải là domain đã biết có CAPTCHA không.

    Args:
        crawler: AdaptiveCrawler instance
        url: URL cần kiểm tra

    Returns:
        bool: True nếu domain đã biết có CAPTCHA, False nếu không
    """
    if not hasattr(crawler, "_captcha_handler") or crawler._captcha_handler is None:
        return False

    try:
        from urllib.parse import urlparse
        domain = urlparse(url).netloc

        # Kiểm tra trong danh sách domain có CAPTCHA
        if hasattr(crawler, 'captcha_domains') and domain in crawler.captcha_domains:
            return True

        # Kiểm tra trong CaptchaHandler
        if hasattr(crawler._captcha_handler, 'is_domain_known_captcha'):
            return crawler._captcha_handler.is_domain_known_captcha(url)

        return False
    except Exception as e:
        logger.error(f"Error checking if domain is known CAPTCHA: {str(e)}")
        return False

def clear_captcha_cache(crawler) -> None:
    """
    Xóa cache CAPTCHA.

    Args:
        crawler: AdaptiveCrawler instance
    """
    if not hasattr(crawler, "_captcha_handler") or crawler._captcha_handler is None:
        return

    try:
        # Xóa cache trong CaptchaHandler
        if hasattr(crawler._captcha_handler, 'clear_captcha_cache'):
            crawler._captcha_handler.clear_captcha_cache()

        # Xóa cache trong crawler
        if hasattr(crawler, 'captcha_domains'):
            crawler.captcha_domains.clear()

        if hasattr(crawler, 'last_captcha_detection'):
            crawler.last_captcha_detection.clear()

        logger.info("CAPTCHA cache cleared")
    except Exception as e:
        logger.error(f"Error clearing CAPTCHA cache: {str(e)}")
