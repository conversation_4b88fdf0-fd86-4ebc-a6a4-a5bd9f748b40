#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module cung cấp lớp UserAgentRotationManager, một công cụ để quản lý và xoay vòng user agent.

Module này cung cấp lớp UserAgentRotationManager, một công cụ để quản lý và xoay vòng user agent
gi<PERSON><PERSON> tr<PERSON>h bị phát hiện khi crawl nhiều trang web, đặc biệt là các trang web có cơ chế chống bot.
"""

import os
import time
import json
import random
import logging
import threading
from typing import Dict, List, Any, Optional, Tuple, Set, Union, Callable

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UserAgentRotationManager:
    """
    Lớp cung cấp các phương thức để quản lý và xoay vòng user agent.

    Tính năng:
    - Qu<PERSON>n lý danh sách user agent
    - Xoay vòng user agent theo nhiều chiến lược
    - Tạo user agent ngẫu nhiên
    - Hỗ trợ nhiều loại user agent (desktop, mobile, tablet, bot)
    - Hỗ trợ nhiều trình duyệt (Chrome, Firefox, Safari, Edge, Opera)
    - Hỗ trợ nhiều hệ điều hành (Windows, macOS, Linux, Android, iOS)
    """

    # Danh sách user agent phổ biến
    DEFAULT_USER_AGENTS = [
        # Chrome trên Windows
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36",
        # Firefox trên Windows
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101 Firefox/91.0",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:92.0) Gecko/20100101 Firefox/92.0",
        # Edge trên Windows
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36 Edg/92.0.902.62",
        # Chrome trên macOS
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
        # Safari trên macOS
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15",
        # Firefox trên macOS
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:90.0) Gecko/20100101 Firefox/90.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:91.0) Gecko/20100101 Firefox/91.0",
        # Chrome trên Linux
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
        # Firefox trên Linux
        "Mozilla/5.0 (X11; Linux x86_64; rv:90.0) Gecko/20100101 Firefox/90.0",
        "Mozilla/5.0 (X11; Linux x86_64; rv:91.0) Gecko/20100101 Firefox/91.0",
        # Chrome trên Android
        "Mozilla/5.0 (Linux; Android 10; SM-A205U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36",
        "Mozilla/5.0 (Linux; Android 11; Pixel 5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.115 Mobile Safari/537.36",
        # Safari trên iOS
        "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
        # Firefox trên iOS
        "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) FxiOS/34.0 Mobile/15E148 Safari/605.1.15",
        "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) FxiOS/34.0 Mobile/15E148 Safari/605.1.15"
    ]

    def __init__(
        self,
        user_agent_list: Optional[List[str]] = None,
        user_agent_file: Optional[str] = None,
        rotation_strategy: str = "round_robin",
        device_type: str = "all",
        browser_type: str = "all",
        os_type: str = "all",
        include_random: bool = False,
        random_ratio: float = 0.1,
        verbose: bool = False,
    ):
        """
        Khởi tạo UserAgentRotationManager.

        Args:
            user_agent_list: Danh sách user agent
            user_agent_file: Đường dẫn đến file chứa danh sách user agent
            rotation_strategy: Chiến lược xoay vòng user agent ("round_robin", "random", "weighted")
            device_type: Loại thiết bị ("all", "desktop", "mobile", "tablet", "bot")
            browser_type: Loại trình duyệt ("all", "chrome", "firefox", "safari", "edge", "opera")
            os_type: Loại hệ điều hành ("all", "windows", "macos", "linux", "android", "ios")
            include_random: Có bao gồm user agent ngẫu nhiên hay không
            random_ratio: Tỷ lệ sử dụng user agent ngẫu nhiên (0.0 đến 1.0)
            verbose: Ghi log chi tiết hay không
        """
        # Cấu hình
        self.user_agent_list = user_agent_list or self.DEFAULT_USER_AGENTS.copy()
        self.user_agent_file = user_agent_file
        self.rotation_strategy = rotation_strategy
        self.device_type = device_type
        self.browser_type = browser_type
        self.os_type = os_type
        self.include_random = include_random
        self.random_ratio = random_ratio
        self.verbose = verbose

        # Khởi tạo các thuộc tính
        self.current_index = 0
        self.filtered_user_agents = []
        self.user_agent_stats = {}
        self.user_agent_lock = threading.Lock()

        # Khởi tạo các thành phần
        self._initialize_components()

        logger.info(f"UserAgentRotationManager được khởi tạo với {len(self.filtered_user_agents)} user agent")

    def _initialize_components(self):
        """Khởi tạo các thành phần."""
        # Tải user agent từ file nếu có
        if self.user_agent_file and os.path.exists(self.user_agent_file):
            self._load_user_agents_from_file()

        # Lọc user agent theo loại thiết bị, trình duyệt và hệ điều hành
        self._filter_user_agents()

        # Khởi tạo thống kê cho mỗi user agent
        for user_agent in self.filtered_user_agents:
            self.user_agent_stats[user_agent] = {
                "usage_count": 0,
                "last_used": 0
            }

    def get_user_agent(self, strategy: Optional[str] = None) -> str:
        """
        Lấy user agent theo chiến lược xoay vòng.

        Args:
            strategy: Chiến lược xoay vòng user agent (None để sử dụng chiến lược mặc định)

        Returns:
            str: User agent
        """
        with self.user_agent_lock:
            if not self.filtered_user_agents:
                logger.warning("Không có user agent khả dụng, sử dụng user agent mặc định")
                return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"

            # Tạo user agent ngẫu nhiên nếu cần
            if self.include_random and random.random() < self.random_ratio:
                return self._generate_random_user_agent()

            strategy = strategy or self.rotation_strategy

            if strategy == "round_robin":
                return self._get_user_agent_round_robin()
            elif strategy == "random":
                return self._get_user_agent_random()
            elif strategy == "weighted":
                return self._get_user_agent_weighted()
            else:
                logger.warning(f"Chiến lược không hỗ trợ: {strategy}, sử dụng round_robin")
                return self._get_user_agent_round_robin()

    def add_user_agent(self, user_agent: str) -> bool:
        """
        Thêm user agent vào danh sách.

        Args:
            user_agent: User agent cần thêm

        Returns:
            bool: True nếu thêm thành công, False nếu không
        """
        with self.user_agent_lock:
            if user_agent in self.user_agent_list:
                logger.warning(f"User agent đã tồn tại: {user_agent}")
                return False

            # Thêm user agent vào danh sách
            self.user_agent_list.append(user_agent)

            # Lọc lại user agent
            self._filter_user_agents()

            # Khởi tạo thống kê cho user agent nếu cần
            if user_agent in self.filtered_user_agents and user_agent not in self.user_agent_stats:
                self.user_agent_stats[user_agent] = {
                    "usage_count": 0,
                    "last_used": 0
                }

            logger.info(f"Đã thêm user agent: {user_agent}")
            return True

    def remove_user_agent(self, user_agent: str) -> bool:
        """
        Xóa user agent khỏi danh sách.

        Args:
            user_agent: User agent cần xóa

        Returns:
            bool: True nếu xóa thành công, False nếu không
        """
        with self.user_agent_lock:
            if user_agent not in self.user_agent_list:
                logger.warning(f"User agent không tồn tại: {user_agent}")
                return False

            # Xóa user agent khỏi danh sách
            self.user_agent_list.remove(user_agent)

            # Lọc lại user agent
            self._filter_user_agents()

            # Xóa thống kê của user agent nếu cần
            if user_agent in self.user_agent_stats:
                del self.user_agent_stats[user_agent]

            logger.info(f"Đã xóa user agent: {user_agent}")
            return True

    def _load_user_agents_from_file(self):
        """Tải user agent từ file."""
        try:
            with open(self.user_agent_file, "r") as f:
                user_agents = [line.strip() for line in f.readlines() if line.strip()]
                self.user_agent_list.extend(user_agents)
                logger.info(f"Đã tải {len(user_agents)} user agent từ {self.user_agent_file}")
        except Exception as e:
            logger.error(f"Lỗi khi tải user agent từ file: {str(e)}")

    def _filter_user_agents(self):
        """Lọc user agent theo loại thiết bị, trình duyệt và hệ điều hành."""
        self.filtered_user_agents = []

        for user_agent in self.user_agent_list:
            # Lọc theo loại thiết bị
            if self.device_type != "all":
                if self.device_type == "desktop" and not self._is_desktop_user_agent(user_agent):
                    continue
                elif self.device_type == "mobile" and not self._is_mobile_user_agent(user_agent):
                    continue
                elif self.device_type == "tablet" and not self._is_tablet_user_agent(user_agent):
                    continue
                elif self.device_type == "bot" and not self._is_bot_user_agent(user_agent):
                    continue

            # Lọc theo loại trình duyệt
            if self.browser_type != "all":
                if self.browser_type == "chrome" and "Chrome" not in user_agent:
                    continue
                elif self.browser_type == "firefox" and "Firefox" not in user_agent:
                    continue
                elif self.browser_type == "safari" and "Safari" in user_agent and "Chrome" not in user_agent:
                    continue
                elif self.browser_type == "edge" and "Edg" not in user_agent:
                    continue
                elif self.browser_type == "opera" and "OPR" not in user_agent:
                    continue

            # Lọc theo loại hệ điều hành
            if self.os_type != "all":
                if self.os_type == "windows" and "Windows" not in user_agent:
                    continue
                elif self.os_type == "macos" and "Macintosh" not in user_agent:
                    continue
                elif self.os_type == "linux" and "Linux" not in user_agent and "X11" not in user_agent:
                    continue
                elif self.os_type == "android" and "Android" not in user_agent:
                    continue
                elif self.os_type == "ios" and "iPhone" not in user_agent and "iPad" not in user_agent:
                    continue

            # Thêm vào danh sách đã lọc
            self.filtered_user_agents.append(user_agent)

        logger.info(f"Đã lọc {len(self.user_agent_list)} user agent thành {len(self.filtered_user_agents)} user agent")

    def _is_desktop_user_agent(self, user_agent: str) -> bool:
        """
        Kiểm tra xem user agent có phải là của desktop hay không.

        Args:
            user_agent: User agent cần kiểm tra

        Returns:
            bool: True nếu là desktop, False nếu không
        """
        return "Windows" in user_agent or "Macintosh" in user_agent or ("Linux" in user_agent and "Android" not in user_agent)

    def _is_mobile_user_agent(self, user_agent: str) -> bool:
        """
        Kiểm tra xem user agent có phải là của mobile hay không.

        Args:
            user_agent: User agent cần kiểm tra

        Returns:
            bool: True nếu là mobile, False nếu không
        """
        return "Android" in user_agent or "iPhone" in user_agent or "Mobile" in user_agent

    def _is_tablet_user_agent(self, user_agent: str) -> bool:
        """
        Kiểm tra xem user agent có phải là của tablet hay không.

        Args:
            user_agent: User agent cần kiểm tra

        Returns:
            bool: True nếu là tablet, False nếu không
        """
        return "iPad" in user_agent or ("Android" in user_agent and "Mobile" not in user_agent)

    def _is_bot_user_agent(self, user_agent: str) -> bool:
        """
        Kiểm tra xem user agent có phải là của bot hay không.

        Args:
            user_agent: User agent cần kiểm tra

        Returns:
            bool: True nếu là bot, False nếu không
        """
        bot_keywords = ["bot", "crawler", "spider", "slurp", "search", "fetch", "http", "java", "python"]
        return any(keyword in user_agent.lower() for keyword in bot_keywords)

    def _get_user_agent_round_robin(self) -> str:
        """
        Lấy user agent theo chiến lược round-robin.

        Returns:
            str: User agent
        """
        user_agent = self.filtered_user_agents[self.current_index]
        self.current_index = (self.current_index + 1) % len(self.filtered_user_agents)
        self.user_agent_stats[user_agent]["usage_count"] += 1
        self.user_agent_stats[user_agent]["last_used"] = time.time()
        return user_agent

    def _get_user_agent_random(self) -> str:
        """
        Lấy user agent ngẫu nhiên.

        Returns:
            str: User agent
        """
        user_agent = random.choice(self.filtered_user_agents)
        self.user_agent_stats[user_agent]["usage_count"] += 1
        self.user_agent_stats[user_agent]["last_used"] = time.time()
        return user_agent

    def _get_user_agent_weighted(self) -> str:
        """
        Lấy user agent theo trọng số.

        Returns:
            str: User agent
        """
        # Tính trọng số dựa trên tần suất sử dụng
        weights = []
        for user_agent in self.filtered_user_agents:
            stats = self.user_agent_stats[user_agent]
            usage_count = stats["usage_count"]
            if usage_count == 0:
                weights.append(1.0)
            else:
                weights.append(1.0 / usage_count)

        # Lấy user agent theo trọng số
        user_agent = random.choices(self.filtered_user_agents, weights=weights, k=1)[0]
        self.user_agent_stats[user_agent]["usage_count"] += 1
        self.user_agent_stats[user_agent]["last_used"] = time.time()
        return user_agent

    def _generate_random_user_agent(self) -> str:
        """
        Tạo user agent ngẫu nhiên.

        Returns:
            str: User agent
        """
        # Tạo user agent ngẫu nhiên
        return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"

    def start(self):
        """Bắt đầu UserAgentRotationManager."""
        self.is_running = True
        logger.info("UserAgentRotationManager đã bắt đầu")

    def stop(self):
        """Dừng UserAgentRotationManager."""
        if not self.is_running:
            logger.warning("UserAgentRotationManager không đang chạy")
            return

        self.is_running = False
        logger.info("UserAgentRotationManager đã dừng")
