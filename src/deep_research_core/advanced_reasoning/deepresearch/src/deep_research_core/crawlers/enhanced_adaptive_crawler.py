#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module cung cấp lớp EnhancedAdaptiveCrawler, một phiên bản nâng cao của AdaptiveCrawler.

Module này cung cấp lớp Enhanced<PERSON>daptiveCrawler, một phiên bản nâng cao của AdaptiveCrawler
với nhiều tính năng mới như xử lý media, file, phân trang, cuộn vô hạn, JavaScript, form, v.v.
"""

import os
import time
import logging
import re
import random
import requests
from urllib.parse import urlparse, urljoin
from typing import Dict, List, Any, Optional, Tuple, Set, Union
from bs4 import BeautifulSoup
import concurrent.futures
from collections import defaultdict
import json
import hashlib

from .adaptive_crawler import AdaptiveCrawler
from .media_handler import MediaHandler
from .file_handler import FileHandler
from .site_structure_handler import SiteStructureHandler
from .pagination_handler import PaginationHandler
from .infinite_scroll_handler import InfiniteScrollHandler
from .javascript_handler import JavaScriptHandler
from .form_handler import FormHandler

from ..utils.structured_logging import get_logger

# Thiết lập logging
logger = get_logger(__name__)


class EnhancedAdaptiveCrawler(AdaptiveCrawler):
    """
    Lớp cung cấp các phương thức để crawl nội dung web một cách thích ứng với nhiều tính năng nâng cao.

    Tính năng:
    - Tất cả tính năng của AdaptiveCrawler
    - Xử lý media (hình ảnh, video, audio)
    - Xử lý file (PDF, DOCX, XLSX, v.v.)
    - Xử lý cấu trúc trang web
    - Xử lý phân trang
    - Xử lý cuộn vô hạn
    - Xử lý JavaScript
    - Xử lý form
    - Nhiều chế độ crawl (basic, full_site, content_only, media_only)
    - Tải xuống media và file
    - Tạo sitemap
    - Và nhiều tính năng khác
    """

    def __init__(
        self,
        max_depth: int = 2,
        max_urls_per_domain: int = 5,
        max_total_urls: int = 100,
        delay: float = 1.0,
        timeout: float = 10.0,
        user_agent: Optional[str] = None,
        respect_robots_txt: bool = True,
        follow_redirects: bool = True,
        max_redirects: int = 5,
        max_retries: int = 3,
        retry_delay: float = 2.0,
        max_threads: int = 5,
        verify_ssl: bool = True,
        proxies: Optional[Dict[str, str]] = None,
        cookies: Optional[Dict[str, str]] = None,
        headers: Optional[Dict[str, str]] = None,
        cache_enabled: bool = True,
        cache_ttl: int = 3600,
        cache_size: int = 1000,
        content_types: Optional[List[str]] = None,
        excluded_extensions: Optional[List[str]] = None,
        excluded_domains: Optional[List[str]] = None,
        included_domains: Optional[List[str]] = None,
        url_patterns: Optional[List[str]] = None,
        content_patterns: Optional[List[str]] = None,
        min_content_length: int = 500,
        max_content_length: int = 5000000,
        extract_metadata: bool = True,
        extract_links: bool = True,
        extract_images: bool = False,
        extract_files: bool = False,
        extract_structured_data: bool = False,
        detect_language: bool = True,
        detect_encoding: bool = True,
        detect_content_type: bool = True,
        detect_site_type: bool = True,
        handle_javascript: bool = False,
        handle_captcha: bool = False,
        verbose: bool = False,
        # Tham số mới
        crawl_mode: str = "basic",
        download_media: bool = False,
        download_path: str = "downloads",
        site_map_enabled: bool = False,
        max_media_size_mb: int = 10,
        extract_videos: bool = False,
        extract_audio: bool = False,
        extract_forms: bool = False,
        extract_comments: bool = False,
        extract_tables: bool = False,
        handle_pagination: bool = False,
        handle_infinite_scroll: bool = False,
        handle_spa: bool = False,
        handle_ajax: bool = False,
        handle_forms: bool = False,
        max_pages_per_pagination: int = 5,
        max_scrolls_per_page: int = 5,
        save_html: bool = False,
        save_screenshots: bool = False,
        organize_by_domain: bool = True,
        organize_by_type: bool = True,
        extract_file_content: bool = False,
        max_file_size_mb: int = 50,
    ):
        """
        Khởi tạo EnhancedAdaptiveCrawler.

        Args:
            max_depth: Độ sâu tối đa khi crawl
            max_urls_per_domain: Số lượng URL tối đa cho mỗi domain
            max_total_urls: Tổng số URL tối đa sẽ crawl
            delay: Thời gian chờ giữa các request (giây)
            timeout: Thời gian timeout cho mỗi request (giây)
            user_agent: User-Agent header
            respect_robots_txt: Tuân thủ robots.txt hay không
            follow_redirects: Theo dõi chuyển hướng hay không
            max_redirects: Số lượng chuyển hướng tối đa
            max_retries: Số lần thử lại tối đa khi request thất bại
            retry_delay: Thời gian chờ giữa các lần thử lại (giây)
            max_threads: Số luồng tối đa khi crawl
            verify_ssl: Xác minh chứng chỉ SSL hay không
            proxies: Danh sách proxy
            cookies: Cookies cho request
            headers: Headers cho request
            cache_enabled: Bật cache hay không
            cache_ttl: Thời gian sống của cache (giây)
            cache_size: Kích thước tối đa của cache
            content_types: Danh sách các loại nội dung được phép
            excluded_extensions: Danh sách các phần mở rộng bị loại trừ
            excluded_domains: Danh sách các domain bị loại trừ
            included_domains: Danh sách các domain được phép
            url_patterns: Danh sách các mẫu URL được phép
            content_patterns: Danh sách các mẫu nội dung được phép
            min_content_length: Độ dài tối thiểu của nội dung
            max_content_length: Độ dài tối đa của nội dung
            extract_metadata: Trích xuất metadata hay không
            extract_links: Trích xuất links hay không
            extract_images: Trích xuất hình ảnh hay không
            extract_files: Trích xuất files hay không
            extract_structured_data: Trích xuất dữ liệu có cấu trúc hay không
            detect_language: Phát hiện ngôn ngữ hay không
            detect_encoding: Phát hiện mã hóa hay không
            detect_content_type: Phát hiện loại nội dung hay không
            detect_site_type: Phát hiện loại trang web hay không
            handle_javascript: Xử lý JavaScript hay không
            handle_captcha: Xử lý CAPTCHA hay không
            verbose: Ghi log chi tiết hay không
            crawl_mode: Chế độ crawl (basic, full_site, content_only, media_only)
            download_media: Tải xuống media hay không
            download_path: Đường dẫn để lưu các file đã tải xuống
            site_map_enabled: Tạo sitemap hay không
            max_media_size_mb: Kích thước tối đa của media (MB)
            extract_videos: Trích xuất video hay không
            extract_audio: Trích xuất audio hay không
            extract_forms: Trích xuất form hay không
            extract_comments: Trích xuất comment hay không
            extract_tables: Trích xuất bảng hay không
            handle_pagination: Xử lý phân trang hay không
            handle_infinite_scroll: Xử lý cuộn vô hạn hay không
            handle_spa: Xử lý SPA hay không
            handle_ajax: Xử lý AJAX hay không
            handle_forms: Xử lý form hay không
            max_pages_per_pagination: Số trang tối đa cho mỗi phân trang
            max_scrolls_per_page: Số lần cuộn tối đa cho mỗi trang
            save_html: Lưu HTML hay không
            save_screenshots: Lưu ảnh chụp màn hình hay không
            organize_by_domain: Tổ chức theo domain hay không
            organize_by_type: Tổ chức theo loại hay không
            extract_file_content: Trích xuất nội dung file hay không
            max_file_size_mb: Kích thước tối đa của file (MB)
        """
        # Gọi constructor của lớp cha
        super().__init__(
            max_depth=max_depth,
            max_urls_per_domain=max_urls_per_domain,
            max_total_urls=max_total_urls,
            delay=delay,
            timeout=timeout,
            user_agent=user_agent,
            respect_robots_txt=respect_robots_txt,
            follow_redirects=follow_redirects,
            max_redirects=max_redirects,
            max_retries=max_retries,
            retry_delay=retry_delay,
            max_threads=max_threads,
            verify_ssl=verify_ssl,
            proxies=proxies,
            cookies=cookies,
            headers=headers,
            cache_enabled=cache_enabled,
            cache_ttl=cache_ttl,
            cache_size=cache_size,
            content_types=content_types,
            excluded_extensions=excluded_extensions,
            excluded_domains=excluded_domains,
            included_domains=included_domains,
            url_patterns=url_patterns,
            content_patterns=content_patterns,
            min_content_length=min_content_length,
            max_content_length=max_content_length,
            extract_metadata=extract_metadata,
            extract_links=extract_links,
            extract_images=extract_images,
            extract_files=extract_files,
            extract_structured_data=extract_structured_data,
            detect_language=detect_language,
            detect_encoding=detect_encoding,
            detect_content_type=detect_content_type,
            detect_site_type=detect_site_type,
            handle_javascript=handle_javascript,
            handle_captcha=handle_captcha,
            verbose=verbose,
        )

        # Cấu hình mới
        self.crawl_mode = crawl_mode
        self.download_media = download_media
        self.download_path = download_path
        self.site_map_enabled = site_map_enabled
        self.max_media_size_mb = max_media_size_mb
        self.extract_videos = extract_videos
        self.extract_audio = extract_audio
        self.extract_forms = extract_forms
        self.extract_comments = extract_comments
        self.extract_tables = extract_tables
        self.handle_pagination = handle_pagination
        self.handle_infinite_scroll = handle_infinite_scroll
        self.handle_spa = handle_spa
        self.handle_ajax = handle_ajax
        self.handle_forms = handle_forms
        self.max_pages_per_pagination = max_pages_per_pagination
        self.max_scrolls_per_page = max_scrolls_per_page
        self.save_html = save_html
        self.save_screenshots = save_screenshots
        self.organize_by_domain = organize_by_domain
        self.organize_by_type = organize_by_type
        self.extract_file_content = extract_file_content
        self.max_file_size_mb = max_file_size_mb

        # Tạo thư mục lưu trữ nếu chưa tồn tại
        if self.download_media or self.extract_file_content or self.save_html or self.save_screenshots:
            os.makedirs(self.download_path, exist_ok=True)

        # Khởi tạo các handlers
        self._initialize_handlers()

        # Mở rộng thống kê
        self.stats.update({
            "media_extracted": 0,
            "media_downloaded": 0,
            "files_extracted": 0,
            "files_downloaded": 0,
            "forms_extracted": 0,
            "forms_filled": 0,
            "forms_submitted": 0,
            "pagination_detected": 0,
            "pages_crawled": 0,
            "infinite_scroll_detected": 0,
            "scrolls_performed": 0,
            "spa_detected": 0,
            "ajax_detected": 0,
            "html_saved": 0,
            "screenshots_saved": 0,
            "file_content_extracted": 0,
        })

    def _initialize_handlers(self):
        """
        Khởi tạo các handlers.
        """
        # Khởi tạo MediaHandler
        self.media_handler = MediaHandler(
            download_path=os.path.join(self.download_path, "media"),
            max_size_mb=self.max_media_size_mb,
            timeout=self.timeout,
            max_retries=self.max_retries,
            retry_delay=self.retry_delay,
            user_agent=self.user_agent,
            verify_ssl=self.verify_ssl,
            proxies=self.proxies,
            cookies=self.cookies,
            headers=self.headers,
            extract_css_images=True,
            extract_background_images=True,
            extract_video_thumbnails=True,
            extract_video_sources=True,
            extract_audio_sources=True,
            extract_iframe_sources=True,
            extract_svg_images=True,
            verbose=self.verbose,
        )

        # Khởi tạo FileHandler
        self.file_handler = FileHandler(
            download_path=os.path.join(self.download_path, "files"),
            max_size_mb=self.max_file_size_mb,
            timeout=self.timeout,
            max_retries=self.max_retries,
            retry_delay=self.retry_delay,
            user_agent=self.user_agent,
            verify_ssl=self.verify_ssl,
            proxies=self.proxies,
            cookies=self.cookies,
            headers=self.headers,
            extract_file_content=self.extract_file_content,
            organize_by_type=self.organize_by_type,
            verbose=self.verbose,
        )

        # Khởi tạo SiteStructureHandler
        self.site_structure_handler = SiteStructureHandler(
            respect_robots=self.respect_robots_txt,
            use_sitemap=self.site_map_enabled,
            max_depth=self.max_depth,
            max_urls=self.max_total_urls,
            max_urls_per_domain=self.max_urls_per_domain,
            timeout=self.timeout,
            max_retries=self.max_retries,
            retry_delay=self.retry_delay,
            user_agent=self.user_agent,
            verify_ssl=self.verify_ssl,
            proxies=self.proxies,
            cookies=self.cookies,
            headers=self.headers,
            excluded_extensions=self.excluded_extensions,
            max_concurrent_requests=self.max_threads,
            verbose=self.verbose,
        )

        # Khởi tạo PaginationHandler
        self.pagination_handler = PaginationHandler(
            max_pages=self.max_pages_per_pagination,
            detect_infinite_scroll=self.handle_infinite_scroll,
            detect_ajax_pagination=self.handle_ajax,
            detect_url_patterns=True,
            verbose=self.verbose,
        )

        # Khởi tạo InfiniteScrollHandler
        self.infinite_scroll_handler = InfiniteScrollHandler(
            max_scrolls=self.max_scrolls_per_page,
            scroll_delay=self.delay,
            wait_for_content=True,
            detect_no_more_content=True,
            detect_ajax_loading=self.handle_ajax,
            detect_lazy_loading=True,
            verbose=self.verbose,
        )

        # Khởi tạo JavaScriptHandler
        self.javascript_handler = JavaScriptHandler(
            detect_spa=self.handle_spa,
            detect_ajax=self.handle_ajax,
            detect_lazy_loading=True,
            detect_infinite_scroll=self.handle_infinite_scroll,
            detect_event_handlers=True,
            detect_form_validation=self.handle_forms,
            detect_dynamic_content=True,
            wait_for_load=True,
            wait_for_network_idle=True,
            wait_timeout=self.timeout,
            verbose=self.verbose,
        )

        # Khởi tạo FormHandler
        self.form_handler = FormHandler(
            auto_fill=self.handle_forms,
            auto_submit=False,
            ignore_hidden_inputs=False,
            ignore_submit_buttons=False,
            max_form_depth=3,
            max_forms_per_page=10,
            max_submit_attempts=self.max_retries,
            submit_delay=self.delay,
            verbose=self.verbose,
        )

    def crawl(self, url: str, depth: int = 0, **kwargs) -> Dict[str, Any]:
        """
        Crawl một URL với các tính năng nâng cao.

        Args:
            url: URL cần crawl
            depth: Độ sâu hiện tại
            **kwargs: Các tham số bổ sung

        Returns:
            Dict[str, Any]: Kết quả crawl
        """
        # Xác định chế độ crawl
        crawl_mode = kwargs.get("crawl_mode", self.crawl_mode)

        # Crawl theo chế độ
        if crawl_mode == "basic":
            # Sử dụng phương thức crawl của lớp cha
            result = super().crawl(url, depth, **kwargs)
        elif crawl_mode == "full_site":
            # Crawl toàn bộ trang web
            result = self._crawl_full_site(url, depth, **kwargs)
        elif crawl_mode == "content_only":
            # Chỉ crawl nội dung
            result = self._crawl_content_only(url, depth, **kwargs)
        elif crawl_mode == "media_only":
            # Chỉ crawl media
            result = self._crawl_media_only(url, depth, **kwargs)
        else:
            # Chế độ không hợp lệ, sử dụng chế độ basic
            logger.warning(f"Invalid crawl mode: {crawl_mode}, using basic mode")
            result = super().crawl(url, depth, **kwargs)

        return result

    def _crawl_full_site(self, url: str, depth: int = 0, **kwargs) -> Dict[str, Any]:
        """
        Crawl toàn bộ trang web.

        Args:
            url: URL cần crawl
            depth: Độ sâu hiện tại
            **kwargs: Các tham số bổ sung

        Returns:
            Dict[str, Any]: Kết quả crawl
        """
        # Xây dựng cấu trúc trang web
        site_structure = self.site_structure_handler.build_site_structure(url, self.max_depth)

        # Khởi tạo kết quả
        result = {
            "success": True,
            "url": url,
            "depth": depth,
            "timestamp": time.time(),
            "site_structure": site_structure,
            "pages": [],
            "media": [],
            "files": [],
            "forms": [],
        }

        # Crawl từng URL trong cấu trúc trang web
        urls = site_structure["urls"]
        prioritized_urls = self.site_structure_handler.prioritize_urls(urls, url)

        # Giới hạn số lượng URL
        prioritized_urls = prioritized_urls[:self.max_total_urls]

        # Crawl từng URL
        for url in prioritized_urls:
            # Crawl URL
            page_result = super().crawl(url, depth, **kwargs)

            # Nếu crawl thành công
            if page_result.get("success", False):
                # Trích xuất media
                if self.extract_images or self.extract_videos or self.extract_audio:
                    media = self._extract_media(page_result["content"], url)
                    page_result["media"] = media
                    result["media"].extend(media)

                # Trích xuất file
                if self.extract_files:
                    files = self._extract_files(page_result["content"], url)
                    page_result["files"] = files
                    result["files"].extend(files)

                # Trích xuất form
                if self.extract_forms:
                    forms = self._extract_forms(page_result["content"], url)
                    page_result["forms"] = forms
                    result["forms"].extend(forms)

                # Xử lý phân trang
                if self.handle_pagination:
                    pagination_info = self._handle_pagination(page_result["content"], url)
                    page_result["pagination"] = pagination_info

                # Thêm vào danh sách trang
                result["pages"].append(page_result)

                # Cập nhật thống kê
                self.stats["pages_crawled"] += 1

        # Tải xuống media
        if self.download_media and result["media"]:
            self._download_media(result["media"])

        # Tải xuống file
        if self.download_media and result["files"]:
            self._download_files(result["files"])

        # Tạo sitemap
        if self.site_map_enabled:
            sitemap = self._create_sitemap(result["pages"])
            result["sitemap"] = sitemap

        return result

    def _crawl_content_only(self, url: str, depth: int = 0, **kwargs) -> Dict[str, Any]:
        """
        Chỉ crawl nội dung.

        Args:
            url: URL cần crawl
            depth: Độ sâu hiện tại
            **kwargs: Các tham số bổ sung

        Returns:
            Dict[str, Any]: Kết quả crawl
        """
        # Crawl URL
        result = super().crawl(url, depth, **kwargs)

        # Nếu crawl thành công
        if result.get("success", False):
            # Xử lý phân trang
            if self.handle_pagination:
                pagination_info = self._handle_pagination(result["content"], url)
                result["pagination"] = pagination_info

                # Nếu có trang tiếp theo
                if pagination_info["has_pagination"] and pagination_info["next_page"]:
                    # Crawl các trang tiếp theo
                    next_pages = []
                    next_url = pagination_info["next_page"]
                    page_count = 1

                    while next_url and page_count < self.max_pages_per_pagination:
                        # Crawl trang tiếp theo
                        next_result = super().crawl(next_url, depth, **kwargs)

                        # Nếu crawl thành công
                        if next_result.get("success", False):
                            # Thêm vào danh sách trang
                            next_pages.append(next_result)

                            # Cập nhật thống kê
                            self.stats["pages_crawled"] += 1

                            # Xử lý phân trang của trang tiếp theo
                            next_pagination_info = self._handle_pagination(next_result["content"], next_url)
                            next_result["pagination"] = next_pagination_info

                            # Cập nhật URL tiếp theo
                            next_url = next_pagination_info["next_page"] if next_pagination_info["has_pagination"] else None

                            # Tăng số lượng trang
                            page_count += 1
                        else:
                            # Nếu crawl thất bại, dừng
                            break

                    # Thêm các trang tiếp theo vào kết quả
                    result["next_pages"] = next_pages

            # Xử lý cuộn vô hạn
            if self.handle_infinite_scroll and self.handle_javascript:
                # Cần sử dụng Playwright/Selenium để xử lý cuộn vô hạn
                # Đây là phần giả định, cần triển khai thực tế với Playwright/Selenium
                pass

        return result

    def _crawl_media_only(self, url: str, depth: int = 0, **kwargs) -> Dict[str, Any]:
        """
        Chỉ crawl media.

        Args:
            url: URL cần crawl
            depth: Độ sâu hiện tại
            **kwargs: Các tham số bổ sung

        Returns:
            Dict[str, Any]: Kết quả crawl
        """
        # Crawl URL
        result = super().crawl(url, depth, **kwargs)

        # Khởi tạo danh sách media và file
        media = []
        files = []

        # Nếu crawl thành công
        if result.get("success", False):
            # Trích xuất media
            if self.extract_images or self.extract_videos or self.extract_audio:
                media = self._extract_media(result["content"], url)
                result["media"] = media

            # Trích xuất file
            if self.extract_files:
                files = self._extract_files(result["content"], url)
                result["files"] = files

            # Tải xuống media
            if self.download_media and media:
                downloaded_media = self._download_media(media)
                result["downloaded_media"] = downloaded_media

            # Tải xuống file
            if self.download_media and files:
                downloaded_files = self._download_files(files)
                result["downloaded_files"] = downloaded_files

        return result

    def _extract_media(self, content: str, url: str) -> List[Dict[str, Any]]:
        """
        Trích xuất media từ nội dung.

        Args:
            content: Nội dung HTML
            url: URL của trang web

        Returns:
            List[Dict[str, Any]]: Danh sách media
        """
        media = []

        # Trích xuất hình ảnh
        if self.extract_images:
            images = self.media_handler.extract_images(content, url)
            media.extend(images)
            self.stats["media_extracted"] += len(images)

        # Trích xuất video
        if self.extract_videos:
            videos = self.media_handler.extract_videos(content, url)
            media.extend(videos)
            self.stats["media_extracted"] += len(videos)

        # Trích xuất audio
        if self.extract_audio:
            audios = self.media_handler.extract_audio(content, url)
            media.extend(audios)
            self.stats["media_extracted"] += len(audios)

        return media

    def _extract_files(self, content: str, url: str) -> List[Dict[str, Any]]:
        """
        Trích xuất file từ nội dung.

        Args:
            content: Nội dung HTML
            url: URL của trang web

        Returns:
            List[Dict[str, Any]]: Danh sách file
        """
        files = self.file_handler.extract_files(content, url)
        self.stats["files_extracted"] += len(files)
        return files

    def _extract_forms(self, content: str, url: str) -> List[Dict[str, Any]]:
        """
        Trích xuất form từ nội dung.

        Args:
            content: Nội dung HTML
            url: URL của trang web

        Returns:
            List[Dict[str, Any]]: Danh sách form
        """
        forms = self.form_handler.detect_forms(content, url)
        self.stats["forms_extracted"] += len(forms)
        return forms

    def _handle_pagination(self, content: str, url: str) -> Dict[str, Any]:
        """
        Xử lý phân trang.

        Args:
            content: Nội dung HTML
            url: URL của trang web

        Returns:
            Dict[str, Any]: Thông tin phân trang
        """
        pagination_info = self.pagination_handler.detect_pagination(content, url)
        if pagination_info["has_pagination"]:
            self.stats["pagination_detected"] += 1
        return pagination_info

    def _download_media(self, media: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Tải xuống media.

        Args:
            media: Danh sách media cần tải xuống

        Returns:
            List[Dict[str, Any]]: Danh sách kết quả tải xuống
        """
        results = []

        # Tải xuống từng media
        for item in media:
            # Lấy URL và loại media
            url = item.get("url", "")
            media_type = item.get("type", "")

            # Bỏ qua nếu không có URL
            if not url:
                continue

            # Tải xuống media
            result = self.media_handler.download_media(url, media_type=media_type)

            # Thêm vào danh sách kết quả
            results.append(result)

            # Cập nhật thống kê
            if result["success"]:
                self.stats["media_downloaded"] += 1

        return results

    def _download_files(self, files: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Tải xuống file.

        Args:
            files: Danh sách file cần tải xuống

        Returns:
            List[Dict[str, Any]]: Danh sách kết quả tải xuống
        """
        results = []

        # Tải xuống từng file
        for item in files:
            # Lấy URL và loại file
            url = item.get("url", "")
            file_type = item.get("type", "")

            # Bỏ qua nếu không có URL
            if not url:
                continue

            # Tải xuống file
            result = self.file_handler.download_file(url, file_type=file_type)

            # Thêm vào danh sách kết quả
            results.append(result)

            # Cập nhật thống kê
            if result["success"]:
                self.stats["files_downloaded"] += 1

                # Trích xuất nội dung file nếu cần
                if self.extract_file_content and result["file_path"]:
                    file_content = self.file_handler.extract_file_content(result["file_path"])
                    result["file_content"] = file_content
                    if file_content["success"]:
                        self.stats["file_content_extracted"] += 1

        return results

    def _create_sitemap(self, pages: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Tạo sitemap từ các trang đã crawl.

        Args:
            pages: Danh sách các trang đã crawl

        Returns:
            Dict[str, Any]: Sitemap
        """
        # Khởi tạo sitemap
        sitemap = {
            "urls": [],
            "domains": set(),
            "total_urls": 0,
        }

        # Thêm URL từ mỗi trang
        for page in pages:
            # Lấy URL
            url = page.get("url", "")

            # Bỏ qua nếu không có URL
            if not url:
                continue

            # Lấy domain
            parsed_url = urlparse(url)
            domain = parsed_url.netloc

            # Thêm vào sitemap
            sitemap["urls"].append({
                "url": url,
                "lastmod": page.get("timestamp", ""),
                "changefreq": "monthly",
                "priority": 0.5,
            })

            # Thêm domain
            sitemap["domains"].add(domain)

            # Thêm URL từ links
            links = page.get("links", [])
            for link in links:
                link_url = link.get("url", "")

                # Bỏ qua nếu không có URL
                if not link_url:
                    continue

                # Lấy domain
                parsed_link_url = urlparse(link_url)
                link_domain = parsed_link_url.netloc

                # Thêm vào sitemap
                sitemap["urls"].append({
                    "url": link_url,
                    "lastmod": page.get("timestamp", ""),
                    "changefreq": "monthly",
                    "priority": 0.3,
                })

                # Thêm domain
                sitemap["domains"].add(link_domain)

        # Cập nhật tổng số URL
        sitemap["total_urls"] = len(sitemap["urls"])

        # Chuyển domains từ set sang list
        sitemap["domains"] = list(sitemap["domains"])

        return sitemap