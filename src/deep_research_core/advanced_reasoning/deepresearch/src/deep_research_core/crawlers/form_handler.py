#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module cung cấp các lớp và phương thức để xử lý form trên trang web.

Module này cung cấp các lớp và phương thức để xử lý form trên trang web,
bao gồm việc phát hiện form, điền form, và gửi form.
"""

import re
import time
import logging
import random
from typing import Dict, List, Any, Optional, Tuple, Set, Union
from bs4 import BeautifulSoup
from urllib.parse import urlparse, urljoin, parse_qs

from ..utils.structured_logging import get_logger

# Thiết lập logging
logger = get_logger(__name__)


class FormHandler:
    """
    Lớp cung cấp các phương thức để xử lý form trên trang web.

    Tính năng:
    - Phát hiện form trên trang web
    - Phân tích cấu trúc form
    - Điền form tự động
    - Gửi form
    - <PERSON><PERSON> lý các loại input khác nhau
    """

    def __init__(
        self,
        auto_fill: bool = True,
        auto_submit: bool = False,
        form_data: Optional[Dict[str, Any]] = None,
        form_selectors: Optional[List[str]] = None,
        ignore_hidden_inputs: bool = False,
        ignore_submit_buttons: bool = False,
        ignore_reset_buttons: bool = True,
        ignore_file_inputs: bool = True,
        ignore_disabled_inputs: bool = True,
        max_form_depth: int = 3,
        max_forms_per_page: int = 10,
        max_inputs_per_form: int = 50,
        max_submit_attempts: int = 3,
        submit_delay: float = 1.0,
        verbose: bool = False,
    ):
        """
        Khởi tạo FormHandler.

        Args:
            auto_fill: Tự động điền form hay không
            auto_submit: Tự động gửi form hay không
            form_data: Dữ liệu để điền vào form
            form_selectors: Các CSS selector để tìm form
            ignore_hidden_inputs: Bỏ qua các input ẩn hay không
            ignore_submit_buttons: Bỏ qua các nút submit hay không
            ignore_reset_buttons: Bỏ qua các nút reset hay không
            ignore_file_inputs: Bỏ qua các input file hay không
            ignore_disabled_inputs: Bỏ qua các input bị vô hiệu hóa hay không
            max_form_depth: Độ sâu tối đa của form
            max_forms_per_page: Số lượng form tối đa trên mỗi trang
            max_inputs_per_form: Số lượng input tối đa trong mỗi form
            max_submit_attempts: Số lần thử gửi form tối đa
            submit_delay: Thời gian chờ giữa các lần gửi form (giây)
            verbose: Ghi log chi tiết hay không
        """
        # Cấu hình
        self.auto_fill = auto_fill
        self.auto_submit = auto_submit
        self.form_data = form_data or {}
        self.form_selectors = form_selectors or ["form"]
        self.ignore_hidden_inputs = ignore_hidden_inputs
        self.ignore_submit_buttons = ignore_submit_buttons
        self.ignore_reset_buttons = ignore_reset_buttons
        self.ignore_file_inputs = ignore_file_inputs
        self.ignore_disabled_inputs = ignore_disabled_inputs
        self.max_form_depth = max_form_depth
        self.max_forms_per_page = max_forms_per_page
        self.max_inputs_per_form = max_inputs_per_form
        self.max_submit_attempts = max_submit_attempts
        self.submit_delay = submit_delay
        self.verbose = verbose

        # Khởi tạo các thuộc tính khác
        self.stats = {
            "forms_detected": 0,
            "forms_filled": 0,
            "forms_submitted": 0,
            "inputs_filled": 0,
            "submit_attempts": 0,
            "submit_failures": 0,
        }

        # Dữ liệu mẫu cho các loại input phổ biến
        self.sample_data = {
            "text": ["John Doe", "Jane Smith", "Example Text", "Test Data"],
            "email": ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
            "password": ["Password123", "SecurePass!"],
            "search": ["search query", "example search"],
            "tel": ["1234567890", "+84 123 456 789"],
            "number": ["123", "456", "789"],
            "date": ["2023-01-01", "2023-12-31"],
            "time": ["12:00", "15:30"],
            "url": ["https://example.com", "https://test.com"],
            "color": ["#FF0000", "#00FF00", "#0000FF"],
            "range": ["50", "75"],
        }

    def detect_forms(self, content: str, url: str) -> List[Dict[str, Any]]:
        """
        Phát hiện form trên trang web.

        Args:
            content: Nội dung HTML
            url: URL của trang web

        Returns:
            List[Dict[str, Any]]: Danh sách form
        """
        forms = []
        base_url = url

        # Trích xuất base tag
        base_match = re.search(r'<base\s+href=["\'](.*?)["\']', content, re.IGNORECASE)
        if base_match:
            base_url = base_match.group(1).strip()

        # Sử dụng BeautifulSoup để phân tích HTML
        soup = BeautifulSoup(content, "html.parser")

        # Tìm tất cả form
        form_elements = []
        for selector in self.form_selectors:
            form_elements.extend(soup.select(selector))

        # Giới hạn số lượng form
        form_elements = form_elements[: self.max_forms_per_page]

        # Phân tích từng form
        for form_element in form_elements:
            # Trích xuất thuộc tính của form
            form_id = form_element.get("id", "")
            form_name = form_element.get("name", "")
            form_class = form_element.get("class", [])
            form_action = form_element.get("action", "")
            form_method = form_element.get("method", "get").lower()
            form_enctype = form_element.get("enctype", "application/x-www-form-urlencoded")

            # Chuyển đổi relative URL thành absolute URL
            if form_action and not form_action.startswith("http"):
                form_action = urljoin(base_url, form_action)

            # Nếu không có action, sử dụng URL hiện tại
            if not form_action:
                form_action = url

            # Phân tích các input trong form
            inputs = self._parse_inputs(form_element)

            # Tạo form object
            form = {
                "id": form_id,
                "name": form_name,
                "class": form_class,
                "action": form_action,
                "method": form_method,
                "enctype": form_enctype,
                "inputs": inputs,
                "source_url": url,
            }

            # Thêm vào danh sách form
            forms.append(form)

        # Cập nhật thống kê
        self.stats["forms_detected"] += len(forms)

        return forms

    def _parse_inputs(self, form_element: Any) -> List[Dict[str, Any]]:
        """
        Phân tích các input trong form.

        Args:
            form_element: Phần tử form

        Returns:
            List[Dict[str, Any]]: Danh sách input
        """
        inputs = []

        # Tìm tất cả input
        input_elements = form_element.find_all(["input", "textarea", "select", "button"])

        # Giới hạn số lượng input
        input_elements = input_elements[: self.max_inputs_per_form]

        # Phân tích từng input
        for input_element in input_elements:
            tag_name = input_element.name.lower()

            # Xử lý input tag
            if tag_name == "input":
                input_type = input_element.get("type", "text").lower()
                input_name = input_element.get("name", "")
                input_id = input_element.get("id", "")
                input_value = input_element.get("value", "")
                input_placeholder = input_element.get("placeholder", "")
                input_required = input_element.has_attr("required")
                input_disabled = input_element.has_attr("disabled")
                input_readonly = input_element.has_attr("readonly")
                input_checked = input_element.has_attr("checked")
                input_min = input_element.get("min", "")
                input_max = input_element.get("max", "")
                input_pattern = input_element.get("pattern", "")
                input_step = input_element.get("step", "")
                input_maxlength = input_element.get("maxlength", "")
                input_minlength = input_element.get("minlength", "")
                input_autocomplete = input_element.get("autocomplete", "")

                # Bỏ qua các input theo cấu hình
                if (
                    (input_type == "hidden" and self.ignore_hidden_inputs)
                    or (input_type in ["submit", "image"] and self.ignore_submit_buttons)
                    or (input_type == "reset" and self.ignore_reset_buttons)
                    or (input_type == "file" and self.ignore_file_inputs)
                    or (input_disabled and self.ignore_disabled_inputs)
                ):
                    continue

                # Tạo input object
                input_obj = {
                    "type": input_type,
                    "name": input_name,
                    "id": input_id,
                    "value": input_value,
                    "placeholder": input_placeholder,
                    "required": input_required,
                    "disabled": input_disabled,
                    "readonly": input_readonly,
                    "checked": input_checked,
                    "min": input_min,
                    "max": input_max,
                    "pattern": input_pattern,
                    "step": input_step,
                    "maxlength": input_maxlength,
                    "minlength": input_minlength,
                    "autocomplete": input_autocomplete,
                    "tag": tag_name,
                }

                # Thêm vào danh sách input
                inputs.append(input_obj)

            # Xử lý textarea tag
            elif tag_name == "textarea":
                input_name = input_element.get("name", "")
                input_id = input_element.get("id", "")
                input_value = input_element.string or ""
                input_placeholder = input_element.get("placeholder", "")
                input_required = input_element.has_attr("required")
                input_disabled = input_element.has_attr("disabled")
                input_readonly = input_element.has_attr("readonly")
                input_maxlength = input_element.get("maxlength", "")
                input_minlength = input_element.get("minlength", "")
                input_rows = input_element.get("rows", "")
                input_cols = input_element.get("cols", "")

                # Bỏ qua các input theo cấu hình
                if input_disabled and self.ignore_disabled_inputs:
                    continue

                # Tạo input object
                input_obj = {
                    "type": "textarea",
                    "name": input_name,
                    "id": input_id,
                    "value": input_value,
                    "placeholder": input_placeholder,
                    "required": input_required,
                    "disabled": input_disabled,
                    "readonly": input_readonly,
                    "maxlength": input_maxlength,
                    "minlength": input_minlength,
                    "rows": input_rows,
                    "cols": input_cols,
                    "tag": tag_name,
                }

                # Thêm vào danh sách input
                inputs.append(input_obj)

            # Xử lý select tag
            elif tag_name == "select":
                input_name = input_element.get("name", "")
                input_id = input_element.get("id", "")
                input_required = input_element.has_attr("required")
                input_disabled = input_element.has_attr("disabled")
                input_multiple = input_element.has_attr("multiple")
                input_size = input_element.get("size", "")

                # Bỏ qua các input theo cấu hình
                if input_disabled and self.ignore_disabled_inputs:
                    continue

                # Tìm tất cả option
                options = []
                for option in input_element.find_all("option"):
                    option_value = option.get("value", "")
                    option_text = option.string or ""
                    option_selected = option.has_attr("selected")
                    option_disabled = option.has_attr("disabled")

                    options.append(
                        {
                            "value": option_value,
                            "text": option_text,
                            "selected": option_selected,
                            "disabled": option_disabled,
                        }
                    )

                # Tạo input object
                input_obj = {
                    "type": "select",
                    "name": input_name,
                    "id": input_id,
                    "required": input_required,
                    "disabled": input_disabled,
                    "multiple": input_multiple,
                    "size": input_size,
                    "options": options,
                    "tag": tag_name,
                }

                # Thêm vào danh sách input
                inputs.append(input_obj)

            # Xử lý button tag
            elif tag_name == "button":
                input_type = input_element.get("type", "submit").lower()
                input_name = input_element.get("name", "")
                input_id = input_element.get("id", "")
                input_value = input_element.get("value", "")
                input_disabled = input_element.has_attr("disabled")
                input_text = input_element.string or ""

                # Bỏ qua các input theo cấu hình
                if (
                    (input_type == "submit" and self.ignore_submit_buttons)
                    or (input_type == "reset" and self.ignore_reset_buttons)
                    or (input_disabled and self.ignore_disabled_inputs)
                ):
                    continue

                # Tạo input object
                input_obj = {
                    "type": input_type,
                    "name": input_name,
                    "id": input_id,
                    "value": input_value,
                    "disabled": input_disabled,
                    "text": input_text,
                    "tag": tag_name,
                }

                # Thêm vào danh sách input
                inputs.append(input_obj)

        return inputs

    def fill_form(self, form: Dict[str, Any]) -> Dict[str, Any]:
        """
        Điền form.

        Args:
            form: Form cần điền

        Returns:
            Dict[str, Any]: Form đã điền
        """
        # Tạo bản sao của form
        filled_form = form.copy()
        filled_form["inputs"] = [input_obj.copy() for input_obj in form["inputs"]]
        filled_data = {}

        # Điền từng input
        for i, input_obj in enumerate(filled_form["inputs"]):
            input_type = input_obj.get("type", "")
            input_name = input_obj.get("name", "")
            input_id = input_obj.get("id", "")
            input_value = input_obj.get("value", "")
            input_tag = input_obj.get("tag", "")

            # Bỏ qua các input đã có giá trị
            if input_value and input_type not in ["checkbox", "radio"]:
                filled_data[input_name] = input_value
                continue

            # Bỏ qua các input không có name
            if not input_name:
                continue

            # Kiểm tra xem có dữ liệu được cung cấp cho input này không
            if input_name in self.form_data:
                # Sử dụng dữ liệu được cung cấp
                new_value = self.form_data[input_name]
            elif input_id in self.form_data:
                # Sử dụng dữ liệu được cung cấp theo ID
                new_value = self.form_data[input_id]
            else:
                # Tự động tạo dữ liệu
                new_value = self._generate_input_value(input_obj)

            # Cập nhật giá trị
            if input_type in ["checkbox", "radio"]:
                # Đối với checkbox và radio, cập nhật thuộc tính checked
                if new_value:
                    filled_form["inputs"][i]["checked"] = True
                    filled_data[input_name] = input_value or "on"
                else:
                    filled_form["inputs"][i]["checked"] = False
            elif input_type == "select":
                # Đối với select, cập nhật thuộc tính selected của option
                options = input_obj.get("options", [])
                for j, option in enumerate(options):
                    if option["value"] == new_value or option["text"] == new_value:
                        filled_form["inputs"][i]["options"][j]["selected"] = True
                        filled_data[input_name] = option["value"] or option["text"]
                        break
            else:
                # Đối với các input khác, cập nhật giá trị
                filled_form["inputs"][i]["value"] = new_value
                filled_data[input_name] = new_value

            # Cập nhật thống kê
            self.stats["inputs_filled"] += 1

        # Cập nhật form với dữ liệu đã điền
        filled_form["filled_data"] = filled_data
        self.stats["forms_filled"] += 1

        return filled_form

    def _generate_input_value(self, input_obj: Dict[str, Any]) -> Any:
        """
        Tạo giá trị cho input.

        Args:
            input_obj: Input cần tạo giá trị

        Returns:
            Any: Giá trị được tạo
        """
        input_type = input_obj.get("type", "")
        input_tag = input_obj.get("tag", "")

        # Xử lý từng loại input
        if input_type == "text" or (input_tag == "textarea" and not input_type):
            return random.choice(self.sample_data["text"])
        elif input_type == "email":
            return random.choice(self.sample_data["email"])
        elif input_type == "password":
            return random.choice(self.sample_data["password"])
        elif input_type == "search":
            return random.choice(self.sample_data["search"])
        elif input_type == "tel":
            return random.choice(self.sample_data["tel"])
        elif input_type == "number":
            return random.choice(self.sample_data["number"])
        elif input_type == "date":
            return random.choice(self.sample_data["date"])
        elif input_type == "time":
            return random.choice(self.sample_data["time"])
        elif input_type == "url":
            return random.choice(self.sample_data["url"])
        elif input_type == "color":
            return random.choice(self.sample_data["color"])
        elif input_type == "range":
            return random.choice(self.sample_data["range"])
        elif input_type == "checkbox" or input_type == "radio":
            return random.choice([True, False])
        elif input_type == "select":
            options = input_obj.get("options", [])
            if options:
                # Chọn một option ngẫu nhiên không bị disabled
                valid_options = [opt for opt in options if not opt.get("disabled", False)]
                if valid_options:
                    selected_option = random.choice(valid_options)
                    return selected_option["value"] or selected_option["text"]
            return ""
        elif input_type in ["submit", "button", "reset", "image"]:
            return input_obj.get("value", "")
        else:
            return ""

    def get_form_data(self, filled_form: Dict[str, Any]) -> Dict[str, Any]:
        """
        Lấy dữ liệu form.

        Args:
            filled_form: Form đã điền

        Returns:
            Dict[str, Any]: Dữ liệu form
        """
        return filled_form.get("filled_data", {})

    def get_submit_url(self, filled_form: Dict[str, Any]) -> str:
        """
        Lấy URL để gửi form.

        Args:
            filled_form: Form đã điền

        Returns:
            str: URL để gửi form
        """
        return filled_form.get("action", "")

    def get_submit_method(self, filled_form: Dict[str, Any]) -> str:
        """
        Lấy phương thức để gửi form.

        Args:
            filled_form: Form đã điền

        Returns:
            str: Phương thức để gửi form
        """
        return filled_form.get("method", "get").lower()

    def get_submit_enctype(self, filled_form: Dict[str, Any]) -> str:
        """
        Lấy enctype để gửi form.

        Args:
            filled_form: Form đã điền

        Returns:
            str: Enctype để gửi form
        """
        return filled_form.get("enctype", "application/x-www-form-urlencoded")
