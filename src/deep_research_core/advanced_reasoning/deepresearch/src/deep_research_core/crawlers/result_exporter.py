#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module cung cấp lớp ResultExporter, một công cụ để xuất kết quả crawl ra nhiều định dạng.

Module này cung cấp lớp ResultExporter, một công cụ để xuất kết quả crawl ra nhiều định dạng
như JSON, CSV, XML, HTML, Markdown, Excel, SQLite, v.v.
"""

import os
import json
import csv
import logging
import xml.dom.minidom
import xml.etree.ElementTree as ET
from typing import Dict, List, Any, Optional, Tuple, Set, Union, Callable

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ResultExporter:
    """
    Lớp cung cấp các phương thức để xuất kết quả crawl ra nhiều định dạng.

    Tính năng:
    - <PERSON><PERSON>t kết quả ra nhiều định dạng (JSON, CSV, XML, HTML, Markdown, Excel, SQLite)
    - Hỗ trợ xuất kết quả theo cấu trúc tùy chỉnh
    - Hỗ trợ xuất kết quả theo nhiều mẫu
    - Hỗ trợ xuất kết quả theo nhiều ngôn ngữ
    - Hỗ trợ xuất kết quả theo nhiều định dạng thời gian
    """

    def __init__(
        self,
        output_dir: Optional[str] = None,
        default_format: str = "json",
        pretty_print: bool = True,
        include_metadata: bool = True,
        include_stats: bool = True,
        include_headers: bool = True,
        include_timestamp: bool = True,
        timestamp_format: str = "%Y-%m-%d %H:%M:%S",
        file_prefix: str = "crawl_result",
        file_suffix: str = "",
        overwrite: bool = False,
        encoding: str = "utf-8",
        verbose: bool = False,
    ):
        """
        Khởi tạo ResultExporter.

        Args:
            output_dir: Thư mục đầu ra
            default_format: Định dạng mặc định
            pretty_print: Có định dạng đẹp hay không
            include_metadata: Có bao gồm metadata hay không
            include_stats: Có bao gồm thống kê hay không
            include_headers: Có bao gồm headers hay không
            include_timestamp: Có bao gồm timestamp hay không
            timestamp_format: Định dạng timestamp
            file_prefix: Tiền tố tên file
            file_suffix: Hậu tố tên file
            overwrite: Có ghi đè file hay không
            encoding: Mã hóa file
            verbose: Ghi log chi tiết hay không
        """
        # Cấu hình
        self.output_dir = output_dir or os.path.join(os.getcwd(), "crawl_results")
        self.default_format = default_format
        self.pretty_print = pretty_print
        self.include_metadata = include_metadata
        self.include_stats = include_stats
        self.include_headers = include_headers
        self.include_timestamp = include_timestamp
        self.timestamp_format = timestamp_format
        self.file_prefix = file_prefix
        self.file_suffix = file_suffix
        self.overwrite = overwrite
        self.encoding = encoding
        self.verbose = verbose

        # Khởi tạo các thành phần
        self._initialize_components()

        logger.info(f"ResultExporter được khởi tạo với output_dir={self.output_dir}")

    def _initialize_components(self):
        """Khởi tạo các thành phần."""
        # Tạo thư mục đầu ra nếu chưa tồn tại
        os.makedirs(self.output_dir, exist_ok=True)

    def export(
        self,
        results: Union[Dict[str, Any], List[Dict[str, Any]]],
        format: Optional[str] = None,
        filename: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        Xuất kết quả crawl.

        Args:
            results: Kết quả crawl
            format: Định dạng xuất (None để sử dụng định dạng mặc định)
            filename: Tên file (None để tạo tên file tự động)
            **kwargs: Các tham số bổ sung

        Returns:
            str: Đường dẫn đến file đã xuất
        """
        format = format or self.default_format
        format = format.lower()

        # Tạo tên file nếu chưa có
        if filename is None:
            import time
            timestamp = time.strftime(self.timestamp_format)
            filename = f"{self.file_prefix}_{timestamp}{self.file_suffix}"

        # Thêm phần mở rộng nếu chưa có
        if not filename.endswith(f".{format}"):
            filename = f"{filename}.{format}"

        # Đường dẫn đầy đủ
        filepath = os.path.join(self.output_dir, filename)

        # Kiểm tra xem file đã tồn tại chưa
        if os.path.exists(filepath) and not self.overwrite:
            logger.warning(f"File đã tồn tại: {filepath}")
            # Thêm số thứ tự vào tên file
            i = 1
            while os.path.exists(filepath):
                filename = f"{self.file_prefix}_{i}{self.file_suffix}.{format}"
                filepath = os.path.join(self.output_dir, filename)
                i += 1

        # Xuất kết quả theo định dạng
        if format == "json":
            self._export_json(results, filepath, **kwargs)
        elif format == "csv":
            self._export_csv(results, filepath, **kwargs)
        elif format == "xml":
            self._export_xml(results, filepath, **kwargs)
        elif format == "html":
            self._export_html(results, filepath, **kwargs)
        elif format == "markdown" or format == "md":
            self._export_markdown(results, filepath, **kwargs)
        elif format == "excel" or format == "xlsx":
            self._export_excel(results, filepath, **kwargs)
        elif format == "sqlite" or format == "db":
            self._export_sqlite(results, filepath, **kwargs)
        else:
            logger.error(f"Định dạng không hỗ trợ: {format}")
            return ""

        logger.info(f"Đã xuất kết quả ra {filepath}")
        return filepath

    def _export_json(
        self,
        results: Union[Dict[str, Any], List[Dict[str, Any]]],
        filepath: str,
        **kwargs
    ) -> None:
        """
        Xuất kết quả ra định dạng JSON.

        Args:
            results: Kết quả crawl
            filepath: Đường dẫn đến file
            **kwargs: Các tham số bổ sung
        """
        try:
            with open(filepath, "w", encoding=self.encoding) as f:
                if self.pretty_print:
                    json.dump(results, f, indent=4, ensure_ascii=False)
                else:
                    json.dump(results, f, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Lỗi khi xuất JSON: {str(e)}")

    def _export_csv(
        self,
        results: Union[Dict[str, Any], List[Dict[str, Any]]],
        filepath: str,
        **kwargs
    ) -> None:
        """
        Xuất kết quả ra định dạng CSV.

        Args:
            results: Kết quả crawl
            filepath: Đường dẫn đến file
            **kwargs: Các tham số bổ sung
        """
        try:
            # Chuyển đổi kết quả thành danh sách nếu là dict
            if isinstance(results, dict):
                results = [results]

            # Lấy tất cả các khóa
            fieldnames = set()
            for result in results:
                fieldnames.update(self._flatten_dict(result).keys())

            # Sắp xếp fieldnames
            fieldnames = sorted(fieldnames)

            with open(filepath, "w", encoding=self.encoding, newline="") as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                for result in results:
                    writer.writerow(self._flatten_dict(result))
        except Exception as e:
            logger.error(f"Lỗi khi xuất CSV: {str(e)}")

    def _export_xml(
        self,
        results: Union[Dict[str, Any], List[Dict[str, Any]]],
        filepath: str,
        **kwargs
    ) -> None:
        """
        Xuất kết quả ra định dạng XML.

        Args:
            results: Kết quả crawl
            filepath: Đường dẫn đến file
            **kwargs: Các tham số bổ sung
        """
        try:
            # Tạo root element
            root = ET.Element("results")

            # Chuyển đổi kết quả thành danh sách nếu là dict
            if isinstance(results, dict):
                results = [results]

            # Thêm các result vào root
            for result in results:
                result_element = ET.SubElement(root, "result")
                self._dict_to_xml(result, result_element)

            # Tạo XML string
            xml_string = ET.tostring(root, encoding=self.encoding)

            # Định dạng đẹp nếu cần
            if self.pretty_print:
                dom = xml.dom.minidom.parseString(xml_string)
                xml_string = dom.toprettyxml(indent="  ", encoding=self.encoding)

            # Ghi ra file
            with open(filepath, "wb") as f:
                f.write(xml_string)
        except Exception as e:
            logger.error(f"Lỗi khi xuất XML: {str(e)}")

    def _export_html(
        self,
        results: Union[Dict[str, Any], List[Dict[str, Any]]],
        filepath: str,
        **kwargs
    ) -> None:
        """
        Xuất kết quả ra định dạng HTML.

        Args:
            results: Kết quả crawl
            filepath: Đường dẫn đến file
            **kwargs: Các tham số bổ sung
        """
        try:
            # Chuyển đổi kết quả thành danh sách nếu là dict
            if isinstance(results, dict):
                results = [results]

            # Tạo HTML
            html = """<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Crawl Results</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; }
        th { background-color: #f2f2f2; text-align: left; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .container { margin-bottom: 20px; }
        h1 { color: #333; }
    </style>
</head>
<body>
    <h1>Crawl Results</h1>
"""

            # Thêm timestamp nếu cần
            if self.include_timestamp:
                import time
                timestamp = time.strftime(self.timestamp_format)
                html += f"    <p>Generated at: {timestamp}</p>\n"

            # Thêm kết quả
            html += "    <div class='container'>\n"
            html += "        <table>\n"

            # Lấy tất cả các khóa
            fieldnames = set()
            for result in results:
                fieldnames.update(self._flatten_dict(result).keys())

            # Sắp xếp fieldnames
            fieldnames = sorted(fieldnames)

            # Thêm header
            html += "            <tr>\n"
            for field in fieldnames:
                html += f"                <th>{field}</th>\n"
            html += "            </tr>\n"

            # Thêm dữ liệu
            for result in results:
                html += "            <tr>\n"
                flat_result = self._flatten_dict(result)
                for field in fieldnames:
                    value = flat_result.get(field, "")
                    html += f"                <td>{value}</td>\n"
                html += "            </tr>\n"

            html += "        </table>\n"
            html += "    </div>\n"
            html += "</body>\n</html>"

            # Ghi ra file
            with open(filepath, "w", encoding=self.encoding) as f:
                f.write(html)
        except Exception as e:
            logger.error(f"Lỗi khi xuất HTML: {str(e)}")

    def _export_markdown(
        self,
        results: Union[Dict[str, Any], List[Dict[str, Any]]],
        filepath: str,
        **kwargs
    ) -> None:
        """
        Xuất kết quả ra định dạng Markdown.

        Args:
            results: Kết quả crawl
            filepath: Đường dẫn đến file
            **kwargs: Các tham số bổ sung
        """
        try:
            # Chuyển đổi kết quả thành danh sách nếu là dict
            if isinstance(results, dict):
                results = [results]

            # Tạo Markdown
            markdown = "# Crawl Results\n\n"

            # Thêm timestamp nếu cần
            if self.include_timestamp:
                import time
                timestamp = time.strftime(self.timestamp_format)
                markdown += f"Generated at: {timestamp}\n\n"

            # Thêm kết quả
            for i, result in enumerate(results):
                markdown += f"## Result {i+1}\n\n"
                flat_result = self._flatten_dict(result)
                for key, value in flat_result.items():
                    markdown += f"- **{key}**: {value}\n"
                markdown += "\n"

            # Ghi ra file
            with open(filepath, "w", encoding=self.encoding) as f:
                f.write(markdown)
        except Exception as e:
            logger.error(f"Lỗi khi xuất Markdown: {str(e)}")

    def _export_excel(
        self,
        results: Union[Dict[str, Any], List[Dict[str, Any]]],
        filepath: str,
        **kwargs
    ) -> None:
        """
        Xuất kết quả ra định dạng Excel.

        Args:
            results: Kết quả crawl
            filepath: Đường dẫn đến file
            **kwargs: Các tham số bổ sung
        """
        try:
            # Kiểm tra xem openpyxl có sẵn không
            import openpyxl
            from openpyxl import Workbook
            from openpyxl.styles import Font, Alignment, PatternFill
        except ImportError:
            logger.error("Thư viện openpyxl không có sẵn. Không thể xuất Excel.")
            return

        try:
            # Chuyển đổi kết quả thành danh sách nếu là dict
            if isinstance(results, dict):
                results = [results]

            # Tạo workbook
            wb = Workbook()
            ws = wb.active
            ws.title = "Crawl Results"

            # Lấy tất cả các khóa
            fieldnames = set()
            for result in results:
                fieldnames.update(self._flatten_dict(result).keys())

            # Sắp xếp fieldnames
            fieldnames = sorted(fieldnames)

            # Thêm header
            for i, field in enumerate(fieldnames, 1):
                cell = ws.cell(row=1, column=i, value=field)
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal="center")
                cell.fill = PatternFill(start_color="DDDDDD", end_color="DDDDDD", fill_type="solid")

            # Thêm dữ liệu
            for i, result in enumerate(results, 2):
                flat_result = self._flatten_dict(result)
                for j, field in enumerate(fieldnames, 1):
                    ws.cell(row=i, column=j, value=str(flat_result.get(field, "")))

            # Lưu workbook
            wb.save(filepath)
        except Exception as e:
            logger.error(f"Lỗi khi xuất Excel: {str(e)}")

    def _export_sqlite(
        self,
        results: Union[Dict[str, Any], List[Dict[str, Any]]],
        filepath: str,
        **kwargs
    ) -> None:
        """
        Xuất kết quả ra định dạng SQLite.

        Args:
            results: Kết quả crawl
            filepath: Đường dẫn đến file
            **kwargs: Các tham số bổ sung
        """
        try:
            # Kiểm tra xem sqlite3 có sẵn không
            import sqlite3
        except ImportError:
            logger.error("Thư viện sqlite3 không có sẵn. Không thể xuất SQLite.")
            return

        try:
            # Chuyển đổi kết quả thành danh sách nếu là dict
            if isinstance(results, dict):
                results = [results]

            # Kết nối đến database
            conn = sqlite3.connect(filepath)
            cursor = conn.cursor()

            # Tạo bảng
            table_name = kwargs.get("table_name", "crawl_results")

            # Lấy tất cả các khóa
            fieldnames = set()
            for result in results:
                fieldnames.update(self._flatten_dict(result).keys())

            # Sắp xếp fieldnames
            fieldnames = sorted(fieldnames)

            # Tạo câu lệnh CREATE TABLE
            create_table_sql = f"CREATE TABLE IF NOT EXISTS {table_name} ("
            create_table_sql += "id INTEGER PRIMARY KEY AUTOINCREMENT, "
            create_table_sql += ", ".join([f"{field} TEXT" for field in fieldnames])
            create_table_sql += ")"

            # Thực thi câu lệnh CREATE TABLE
            cursor.execute(create_table_sql)

            # Thêm dữ liệu
            for result in results:
                flat_result = self._flatten_dict(result)
                insert_sql = f"INSERT INTO {table_name} ("
                insert_sql += ", ".join(fieldnames)
                insert_sql += ") VALUES ("
                insert_sql += ", ".join(["?" for _ in fieldnames])
                insert_sql += ")"

                values = [str(flat_result.get(field, "")) for field in fieldnames]
                cursor.execute(insert_sql, values)

            # Lưu thay đổi
            conn.commit()
            conn.close()
        except Exception as e:
            logger.error(f"Lỗi khi xuất SQLite: {str(e)}")

    def _flatten_dict(self, d: Dict[str, Any], parent_key: str = "", sep: str = "_") -> Dict[str, Any]:
        """
        Làm phẳng dict nhiều cấp thành dict một cấp.

        Args:
            d: Dict cần làm phẳng
            parent_key: Khóa cha
            sep: Ký tự phân cách

        Returns:
            Dict[str, Any]: Dict đã làm phẳng
        """
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(self._flatten_dict(v, new_key, sep).items())
            elif isinstance(v, list):
                for i, item in enumerate(v):
                    if isinstance(item, dict):
                        items.extend(self._flatten_dict(item, f"{new_key}{sep}{i}", sep).items())
                    else:
                        items.append((f"{new_key}{sep}{i}", item))
            else:
                items.append((new_key, v))
        return dict(items)

    def _dict_to_xml(self, d: Dict[str, Any], parent_element: ET.Element) -> None:
        """
        Chuyển đổi dict thành XML.

        Args:
            d: Dict cần chuyển đổi
            parent_element: Element cha
        """
        for k, v in d.items():
            if isinstance(v, dict):
                element = ET.SubElement(parent_element, k)
                self._dict_to_xml(v, element)
            elif isinstance(v, list):
                for item in v:
                    element = ET.SubElement(parent_element, k)
                    if isinstance(item, dict):
                        self._dict_to_xml(item, element)
                    else:
                        element.text = str(item)
            else:
                element = ET.SubElement(parent_element, k)
                element.text = str(v)
