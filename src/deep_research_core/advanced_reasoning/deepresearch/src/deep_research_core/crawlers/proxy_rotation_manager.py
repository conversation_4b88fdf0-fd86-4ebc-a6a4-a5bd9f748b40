#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module cung cấp lớp ProxyRotationManager, một công cụ để quản lý và xoay vòng proxy.

Module này cung cấp lớp ProxyRotationManager, một công cụ để quản lý và xoay vòng proxy
giúp tránh bị chặn khi crawl nhiều trang web, đặc biệt là các trang web có cơ chế chống bot.
"""

import os
import time
import json
import random
import logging
import threading
import requests
from typing import Dict, List, Any, Optional, Tuple, Set, Union, Callable

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProxyRotationManager:
    """
    Lớp cung cấp các phương thức để quản lý và xoay vòng proxy.

    Tính năng:
    - <PERSON><PERSON><PERSON><PERSON> lý danh sách proxy
    - Xoay vòng proxy theo nhiều chiến lược
    - Kiểm tra tính khả dụng của proxy
    - Tự động loại bỏ proxy không hoạt động
    - Hỗ trợ nhiều loại proxy (HTTP, HTTPS, SOCKS4, SOCKS5)
    - Hỗ trợ proxy có xác thực
    """

    def __init__(
        self,
        proxy_list: Optional[List[str]] = None,
        proxy_file: Optional[str] = None,
        proxy_api_url: Optional[str] = None,
        proxy_api_key: Optional[str] = None,
        rotation_strategy: str = "round_robin",
        check_proxy: bool = True,
        check_interval: int = 300,
        timeout: float = 10.0,
        max_retries: int = 3,
        retry_delay: float = 2.0,
        auto_update: bool = False,
        update_interval: int = 3600,
        min_proxies: int = 5,
        verbose: bool = False,
    ):
        """
        Khởi tạo ProxyRotationManager.

        Args:
            proxy_list: Danh sách proxy
            proxy_file: Đường dẫn đến file chứa danh sách proxy
            proxy_api_url: URL của API cung cấp proxy
            proxy_api_key: Khóa API để truy cập API cung cấp proxy
            rotation_strategy: Chiến lược xoay vòng proxy ("round_robin", "random", "weighted")
            check_proxy: Có kiểm tra tính khả dụng của proxy hay không
            check_interval: Khoảng thời gian giữa các lần kiểm tra proxy (giây)
            timeout: Thời gian chờ tối đa cho mỗi request (giây)
            max_retries: Số lần thử lại tối đa khi gặp lỗi
            retry_delay: Thời gian chờ giữa các lần thử lại (giây)
            auto_update: Có tự động cập nhật danh sách proxy hay không
            update_interval: Khoảng thời gian giữa các lần cập nhật proxy (giây)
            min_proxies: Số lượng proxy tối thiểu cần có
            verbose: Ghi log chi tiết hay không
        """
        # Cấu hình
        self.proxy_list = proxy_list or []
        self.proxy_file = proxy_file
        self.proxy_api_url = proxy_api_url
        self.proxy_api_key = proxy_api_key
        self.rotation_strategy = rotation_strategy
        self.check_proxy = check_proxy
        self.check_interval = check_interval
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.auto_update = auto_update
        self.update_interval = update_interval
        self.min_proxies = min_proxies
        self.verbose = verbose

        # Khởi tạo các thuộc tính
        self.current_index = 0
        self.proxy_stats = {}
        self.working_proxies = []
        self.failed_proxies = []
        self.check_thread = None
        self.update_thread = None
        self.is_running = False
        self.proxy_lock = threading.Lock()

        # Khởi tạo các thành phần
        self._initialize_components()

        logger.info(f"ProxyRotationManager được khởi tạo với {len(self.proxy_list)} proxy")

    def _initialize_components(self):
        """Khởi tạo các thành phần."""
        # Tải proxy từ file nếu có
        if self.proxy_file and os.path.exists(self.proxy_file):
            self._load_proxies_from_file()

        # Tải proxy từ API nếu có
        if self.proxy_api_url and self.proxy_api_key:
            self._load_proxies_from_api()

        # Khởi tạo thống kê cho mỗi proxy
        for proxy in self.proxy_list:
            self.proxy_stats[proxy] = {
                "success": 0,
                "failure": 0,
                "last_used": 0,
                "last_checked": 0,
                "response_time": 0,
                "status": "unknown"
            }

        # Kiểm tra proxy nếu cần
        if self.check_proxy:
            self._check_proxies()

    def start(self):
        """Bắt đầu ProxyRotationManager."""
        if self.is_running:
            logger.warning("ProxyRotationManager đã đang chạy")
            return

        self.is_running = True

        # Khởi động thread kiểm tra proxy nếu cần
        if self.check_proxy:
            self._start_check_thread()

        # Khởi động thread cập nhật proxy nếu cần
        if self.auto_update:
            self._start_update_thread()

        logger.info("ProxyRotationManager đã bắt đầu")

    def stop(self):
        """Dừng ProxyRotationManager."""
        if not self.is_running:
            logger.warning("ProxyRotationManager không đang chạy")
            return

        self.is_running = False

        # Dừng thread kiểm tra proxy nếu có
        if self.check_thread:
            # TODO: Implement check thread stop
            pass

        # Dừng thread cập nhật proxy nếu có
        if self.update_thread:
            # TODO: Implement update thread stop
            pass

        logger.info("ProxyRotationManager đã dừng")

    def get_proxy(self, strategy: Optional[str] = None) -> Optional[str]:
        """
        Lấy proxy theo chiến lược xoay vòng.

        Args:
            strategy: Chiến lược xoay vòng proxy (None để sử dụng chiến lược mặc định)

        Returns:
            Optional[str]: Proxy hoặc None nếu không có proxy khả dụng
        """
        with self.proxy_lock:
            if not self.working_proxies:
                logger.warning("Không có proxy khả dụng")
                return None

            strategy = strategy or self.rotation_strategy

            if strategy == "round_robin":
                return self._get_proxy_round_robin()
            elif strategy == "random":
                return self._get_proxy_random()
            elif strategy == "weighted":
                return self._get_proxy_weighted()
            else:
                logger.warning(f"Chiến lược không hỗ trợ: {strategy}, sử dụng round_robin")
                return self._get_proxy_round_robin()

    def add_proxy(self, proxy: str, check: bool = True) -> bool:
        """
        Thêm proxy vào danh sách.

        Args:
            proxy: Proxy cần thêm
            check: Có kiểm tra tính khả dụng của proxy hay không

        Returns:
            bool: True nếu thêm thành công, False nếu không
        """
        with self.proxy_lock:
            if proxy in self.proxy_list:
                logger.warning(f"Proxy đã tồn tại: {proxy}")
                return False

            # Thêm proxy vào danh sách
            self.proxy_list.append(proxy)

            # Khởi tạo thống kê cho proxy
            self.proxy_stats[proxy] = {
                "success": 0,
                "failure": 0,
                "last_used": 0,
                "last_checked": 0,
                "response_time": 0,
                "status": "unknown"
            }

            # Kiểm tra proxy nếu cần
            if check and self.check_proxy:
                if self._check_proxy(proxy):
                    self.working_proxies.append(proxy)
                    logger.info(f"Đã thêm proxy khả dụng: {proxy}")
                    return True
                else:
                    self.failed_proxies.append(proxy)
                    logger.warning(f"Đã thêm proxy không khả dụng: {proxy}")
                    return False
            else:
                self.working_proxies.append(proxy)
                logger.info(f"Đã thêm proxy: {proxy}")
                return True

    def remove_proxy(self, proxy: str) -> bool:
        """
        Xóa proxy khỏi danh sách.

        Args:
            proxy: Proxy cần xóa

        Returns:
            bool: True nếu xóa thành công, False nếu không
        """
        with self.proxy_lock:
            if proxy not in self.proxy_list:
                logger.warning(f"Proxy không tồn tại: {proxy}")
                return False

            # Xóa proxy khỏi danh sách
            self.proxy_list.remove(proxy)

            # Xóa proxy khỏi danh sách proxy hoạt động
            if proxy in self.working_proxies:
                self.working_proxies.remove(proxy)

            # Xóa proxy khỏi danh sách proxy không hoạt động
            if proxy in self.failed_proxies:
                self.failed_proxies.remove(proxy)

            # Xóa thống kê của proxy
            if proxy in self.proxy_stats:
                del self.proxy_stats[proxy]

            logger.info(f"Đã xóa proxy: {proxy}")
            return True

    def format_for_requests(self, proxy: Optional[str] = None) -> Dict[str, str]:
        """
        Định dạng proxy để sử dụng với thư viện requests.

        Args:
            proxy: Proxy cần định dạng (None để lấy proxy theo chiến lược xoay vòng)

        Returns:
            Dict[str, str]: Proxy đã định dạng
        """
        proxy = proxy or self.get_proxy()
        if not proxy:
            return {}

        # Phân tích proxy
        if proxy.startswith("http://"):
            return {"http": proxy, "https": proxy}
        elif proxy.startswith("https://"):
            return {"https": proxy}
        elif proxy.startswith("socks4://"):
            return {"http": proxy, "https": proxy}
        elif proxy.startswith("socks5://"):
            return {"http": proxy, "https": proxy}
        else:
            return {"http": f"http://{proxy}", "https": f"http://{proxy}"}

    def _get_proxy_round_robin(self) -> str:
        """
        Lấy proxy theo chiến lược round-robin.

        Returns:
            str: Proxy
        """
        proxy = self.working_proxies[self.current_index]
        self.current_index = (self.current_index + 1) % len(self.working_proxies)
        self.proxy_stats[proxy]["last_used"] = time.time()
        return proxy

    def _get_proxy_random(self) -> str:
        """
        Lấy proxy ngẫu nhiên.

        Returns:
            str: Proxy
        """
        proxy = random.choice(self.working_proxies)
        self.proxy_stats[proxy]["last_used"] = time.time()
        return proxy

    def _get_proxy_weighted(self) -> str:
        """
        Lấy proxy theo trọng số.

        Returns:
            str: Proxy
        """
        # Tính trọng số dựa trên tỷ lệ thành công
        weights = []
        for proxy in self.working_proxies:
            stats = self.proxy_stats[proxy]
            success = stats["success"]
            failure = stats["failure"]
            if success + failure == 0:
                weights.append(1.0)
            else:
                weights.append(success / (success + failure))

        # Lấy proxy theo trọng số
        proxy = random.choices(self.working_proxies, weights=weights, k=1)[0]
        self.proxy_stats[proxy]["last_used"] = time.time()
        return proxy
