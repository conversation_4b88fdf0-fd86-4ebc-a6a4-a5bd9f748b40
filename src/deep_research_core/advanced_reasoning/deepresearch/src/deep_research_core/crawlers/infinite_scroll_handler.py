#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module cung cấp các lớp và phương thức để xử lý cuộn vô hạn.

Module này cung cấp các lớp và phương thức để xử lý cuộn vô hạn trên trang web,
bao gồm việc phát hiện cuộn vô hạn, cuộn trang, và trích xuất nội dung mới.
"""

import re
import time
import logging
from typing import Dict, List, Any, Optional, Tuple, Set, Union
from bs4 import BeautifulSoup

from ..utils.structured_logging import get_logger

# Thiết lập logging
logger = get_logger(__name__)


class InfiniteScrollHandler:
    """
    Lớp cung cấp các phương thức để xử lý cuộn vô hạn.

    Tính năng:
    - <PERSON><PERSON>t hiện cuộn vô hạn trên trang web
    - Cuộn trang để tải nội dung mới
    - Trích xuất nội dung mới
    - <PERSON><PERSON> lý các loại cuộn vô hạn khác nhau
    """

    def __init__(
        self,
        scroll_selectors: Optional[List[str]] = None,
        load_more_selectors: Optional[List[str]] = None,
        content_selectors: Optional[List[str]] = None,
        max_scrolls: int = 10,
        scroll_delay: float = 1.0,
        scroll_height: int = 800,
        wait_for_content: bool = True,
        wait_timeout: float = 5.0,
        detect_no_more_content: bool = True,
        no_more_content_selectors: Optional[List[str]] = None,
        detect_ajax_loading: bool = True,
        detect_lazy_loading: bool = True,
        verbose: bool = False,
    ):
        """
        Khởi tạo InfiniteScrollHandler.

        Args:
            scroll_selectors: Các CSS selector để tìm vùng cuộn
            load_more_selectors: Các CSS selector để tìm nút "Tải thêm"
            content_selectors: Các CSS selector để tìm nội dung
            max_scrolls: Số lần cuộn tối đa
            scroll_delay: Thời gian chờ giữa các lần cuộn (giây)
            scroll_height: Chiều cao mỗi lần cuộn (pixel)
            wait_for_content: Chờ nội dung mới hay không
            wait_timeout: Thời gian chờ tối đa cho nội dung mới (giây)
            detect_no_more_content: Phát hiện hết nội dung hay không
            no_more_content_selectors: Các CSS selector để phát hiện hết nội dung
            detect_ajax_loading: Phát hiện tải AJAX hay không
            detect_lazy_loading: Phát hiện tải lazy hay không
            verbose: Ghi log chi tiết hay không
        """
        # Cấu hình
        self.scroll_selectors = scroll_selectors or [
            "window",
            "body",
            "html",
            ".infinite-scroll",
            ".infinite-content",
            ".content-wrapper",
            ".content-container",
            ".main-content",
            ".posts-container",
            ".articles-container",
            ".products-container",
            ".items-container",
            ".results-container",
            ".search-results",
            ".listing-container",
            ".feed-container",
            ".timeline-container",
            ".stream-container",
            ".grid-container",
            ".masonry-container",
        ]
        self.load_more_selectors = load_more_selectors or [
            "a:contains(Load more)",
            "a:contains(Tải thêm)",
            "a:contains(Xem thêm)",
            "a:contains(More)",
            "a:contains(Show more)",
            "a:contains(View more)",
            "button:contains(Load more)",
            "button:contains(Tải thêm)",
            "button:contains(Xem thêm)",
            "button:contains(More)",
            "button:contains(Show more)",
            "button:contains(View more)",
            ".load-more",
            ".loadmore",
            ".load_more",
            ".more-items",
            ".more_items",
            ".more-posts",
            ".more_posts",
            ".view-more",
            ".viewmore",
            ".view_more",
            ".show-more",
            ".showmore",
            ".show_more",
        ]
        self.content_selectors = content_selectors or [
            "article",
            ".post",
            ".item",
            ".product",
            ".card",
            ".entry",
            ".result",
            ".search-result",
            ".listing",
            ".feed-item",
            ".timeline-item",
            ".stream-item",
            ".grid-item",
            ".masonry-item",
            ".content-item",
            ".article",
            ".news-item",
            ".blog-post",
            ".list-item",
            ".row-item",
        ]
        self.max_scrolls = max_scrolls
        self.scroll_delay = scroll_delay
        self.scroll_height = scroll_height
        self.wait_for_content = wait_for_content
        self.wait_timeout = wait_timeout
        self.detect_no_more_content = detect_no_more_content
        self.no_more_content_selectors = no_more_content_selectors or [
            ".no-more-content",
            ".no-more-items",
            ".no-more-posts",
            ".no-more-results",
            ".end-of-content",
            ".end-of-items",
            ".end-of-posts",
            ".end-of-results",
            ".end-of-feed",
            ".end-of-timeline",
            ".end-of-stream",
            ".end-of-list",
            ".end-of-page",
            ".end-of-section",
            ".end-message",
            ".end-notice",
            ".end-text",
            ".end-indicator",
            ".end-marker",
            ".end-signal",
            "div:contains(No more content)",
            "div:contains(No more items)",
            "div:contains(No more posts)",
            "div:contains(No more results)",
            "div:contains(End of content)",
            "div:contains(End of items)",
            "div:contains(End of posts)",
            "div:contains(End of results)",
            "div:contains(End of feed)",
            "div:contains(End of timeline)",
            "div:contains(End of stream)",
            "div:contains(End of list)",
            "div:contains(End of page)",
            "div:contains(End of section)",
            "div:contains(That's all)",
            "div:contains(No more to load)",
            "div:contains(No more to show)",
            "div:contains(No more to display)",
            "div:contains(No more to view)",
            "div:contains(No more to see)",
            "div:contains(No more to browse)",
            "div:contains(No more to scroll)",
            "div:contains(No more to fetch)",
            "div:contains(No more to get)",
            "div:contains(No more to retrieve)",
            "div:contains(No more to find)",
            "div:contains(No more to discover)",
            "div:contains(No more to explore)",
            "div:contains(No more to search)",
            "div:contains(No more to look for)",
            "div:contains(No more to look up)",
            "div:contains(No more to check)",
            "div:contains(No more to check out)",
            "div:contains(No more to investigate)",
            "div:contains(No more to examine)",
            "div:contains(No more to inspect)",
            "div:contains(No more to analyze)",
            "div:contains(No more to review)",
            "div:contains(No more to study)",
            "div:contains(No more to observe)",
            "div:contains(No more to watch)",
            "div:contains(No more to monitor)",
            "div:contains(No more to track)",
            "div:contains(No more to follow)",
            "div:contains(No more to pursue)",
            "div:contains(No more to chase)",
            "div:contains(No more to hunt)",
            "div:contains(No more to seek)",
            "div:contains(No more to search for)",
            "div:contains(No more to look for)",
            "div:contains(No more to find)",
            "div:contains(No more to discover)",
            "div:contains(No more to explore)",
            "div:contains(No more to investigate)",
            "div:contains(No more to examine)",
            "div:contains(No more to inspect)",
            "div:contains(No more to analyze)",
            "div:contains(No more to review)",
            "div:contains(No more to study)",
            "div:contains(No more to observe)",
            "div:contains(No more to watch)",
            "div:contains(No more to monitor)",
            "div:contains(No more to track)",
            "div:contains(No more to follow)",
            "div:contains(No more to pursue)",
            "div:contains(No more to chase)",
            "div:contains(No more to hunt)",
            "div:contains(No more to seek)",
            "div:contains(Không còn nội dung)",
            "div:contains(Hết nội dung)",
            "div:contains(Đã hết nội dung)",
            "div:contains(Không còn bài viết)",
            "div:contains(Hết bài viết)",
            "div:contains(Đã hết bài viết)",
            "div:contains(Không còn kết quả)",
            "div:contains(Hết kết quả)",
            "div:contains(Đã hết kết quả)",
            "div:contains(Không còn sản phẩm)",
            "div:contains(Hết sản phẩm)",
            "div:contains(Đã hết sản phẩm)",
            "div:contains(Không còn mục)",
            "div:contains(Hết mục)",
            "div:contains(Đã hết mục)",
            "div:contains(Không còn dữ liệu)",
            "div:contains(Hết dữ liệu)",
            "div:contains(Đã hết dữ liệu)",
            "div:contains(Không còn thông tin)",
            "div:contains(Hết thông tin)",
            "div:contains(Đã hết thông tin)",
            "div:contains(Không còn tin tức)",
            "div:contains(Hết tin tức)",
            "div:contains(Đã hết tin tức)",
            "div:contains(Không còn bài báo)",
            "div:contains(Hết bài báo)",
            "div:contains(Đã hết bài báo)",
            "div:contains(Không còn mục lục)",
            "div:contains(Hết mục lục)",
            "div:contains(Đã hết mục lục)",
            "div:contains(Không còn danh sách)",
            "div:contains(Hết danh sách)",
            "div:contains(Đã hết danh sách)",
            "div:contains(Không còn trang)",
            "div:contains(Hết trang)",
            "div:contains(Đã hết trang)",
            "div:contains(Không còn phần)",
            "div:contains(Hết phần)",
            "div:contains(Đã hết phần)",
            "div:contains(Không còn mục)",
            "div:contains(Hết mục)",
            "div:contains(Đã hết mục)",
            "div:contains(Không còn nội dung để hiển thị)",
            "div:contains(Không còn nội dung để tải)",
            "div:contains(Không còn nội dung để xem)",
            "div:contains(Không còn nội dung để đọc)",
            "div:contains(Không còn nội dung để theo dõi)",
            "div:contains(Không còn nội dung để kiểm tra)",
            "div:contains(Không còn nội dung để tìm kiếm)",
            "div:contains(Không còn nội dung để khám phá)",
            "div:contains(Không còn nội dung để tìm hiểu)",
            "div:contains(Không còn nội dung để nghiên cứu)",
            "div:contains(Không còn nội dung để phân tích)",
            "div:contains(Không còn nội dung để đánh giá)",
            "div:contains(Không còn nội dung để xem xét)",
            "div:contains(Không còn nội dung để kiểm tra)",
            "div:contains(Không còn nội dung để theo dõi)",
            "div:contains(Không còn nội dung để tìm kiếm)",
            "div:contains(Không còn nội dung để khám phá)",
            "div:contains(Không còn nội dung để tìm hiểu)",
            "div:contains(Không còn nội dung để nghiên cứu)",
            "div:contains(Không còn nội dung để phân tích)",
            "div:contains(Không còn nội dung để đánh giá)",
            "div:contains(Không còn nội dung để xem xét)",
            "div:contains(Không còn nội dung để kiểm tra)",
            "div:contains(Không còn nội dung để theo dõi)",
            "div:contains(Không còn nội dung để tìm kiếm)",
            "div:contains(Không còn nội dung để khám phá)",
            "div:contains(Không còn nội dung để tìm hiểu)",
            "div:contains(Không còn nội dung để nghiên cứu)",
            "div:contains(Không còn nội dung để phân tích)",
            "div:contains(Không còn nội dung để đánh giá)",
            "div:contains(Không còn nội dung để xem xét)",
            "div:contains(Không còn nội dung để kiểm tra)",
            "div:contains(Không còn nội dung để theo dõi)",
            "div:contains(Không còn nội dung để tìm kiếm)",
            "div:contains(Không còn nội dung để khám phá)",
            "div:contains(Không còn nội dung để tìm hiểu)",
            "div:contains(Không còn nội dung để nghiên cứu)",
            "div:contains(Không còn nội dung để phân tích)",
            "div:contains(Không còn nội dung để đánh giá)",
            "div:contains(Không còn nội dung để xem xét)",
        ]
        self.detect_ajax_loading = detect_ajax_loading
        self.detect_lazy_loading = detect_lazy_loading
        self.verbose = verbose

        # Khởi tạo các thuộc tính khác
        self.stats = {
            "total_scrolls": 0,
            "total_content_items": 0,
            "total_load_more_clicks": 0,
            "no_more_content_detected": 0,
            "ajax_loading_detected": 0,
            "lazy_loading_detected": 0,
        }

    def scroll_page(self, page: Any) -> bool:
        """
        Cuộn trang để tải nội dung mới.

        Args:
            page: Đối tượng trang web (Playwright/Selenium)

        Returns:
            bool: True nếu cuộn thành công, False nếu không
        """
        try:
            # Lấy chiều cao trang trước khi cuộn
            old_height = page.evaluate("document.body.scrollHeight")
            
            # Cuộn trang
            page.evaluate(f"window.scrollBy(0, {self.scroll_height})")
            
            # Cập nhật thống kê
            self.stats["total_scrolls"] += 1
            
            # Chờ một chút
            time.sleep(self.scroll_delay)
            
            # Nếu cần chờ nội dung mới
            if self.wait_for_content:
                # Chờ cho đến khi chiều cao trang thay đổi hoặc hết thời gian chờ
                start_time = time.time()
                while time.time() - start_time < self.wait_timeout:
                    # Lấy chiều cao trang hiện tại
                    new_height = page.evaluate("document.body.scrollHeight")
                    
                    # Nếu chiều cao đã thay đổi, nội dung mới đã được tải
                    if new_height > old_height:
                        return True
                    
                    # Chờ một chút
                    time.sleep(0.1)
            
            return True
        except Exception as e:
            if self.verbose:
                logger.error(f"Error scrolling page: {str(e)}")
            return False

    def click_load_more(self, page: Any) -> bool:
        """
        Nhấp vào nút "Tải thêm" để tải nội dung mới.

        Args:
            page: Đối tượng trang web (Playwright/Selenium)

        Returns:
            bool: True nếu nhấp thành công, False nếu không
        """
        try:
            # Tìm nút "Tải thêm"
            for selector in self.load_more_selectors:
                # Kiểm tra xem nút có tồn tại không
                if page.query_selector(selector):
                    # Nhấp vào nút
                    page.click(selector)
                    
                    # Cập nhật thống kê
                    self.stats["total_load_more_clicks"] += 1
                    
                    # Chờ một chút
                    time.sleep(self.scroll_delay)
                    
                    return True
            
            return False
        except Exception as e:
            if self.verbose:
                logger.error(f"Error clicking load more button: {str(e)}")
            return False

    def is_no_more_content(self, page: Any) -> bool:
        """
        Kiểm tra xem đã hết nội dung hay chưa.

        Args:
            page: Đối tượng trang web (Playwright/Selenium)

        Returns:
            bool: True nếu đã hết nội dung, False nếu chưa
        """
        if not self.detect_no_more_content:
            return False
        
        try:
            # Tìm các dấu hiệu của việc hết nội dung
            for selector in self.no_more_content_selectors:
                # Kiểm tra xem dấu hiệu có tồn tại không
                if page.query_selector(selector):
                    # Cập nhật thống kê
                    self.stats["no_more_content_detected"] += 1
                    
                    return True
            
            return False
        except Exception as e:
            if self.verbose:
                logger.error(f"Error checking for no more content: {str(e)}")
            return False

    def get_content_items(self, page: Any) -> List[Any]:
        """
        Lấy các mục nội dung từ trang.

        Args:
            page: Đối tượng trang web (Playwright/Selenium)

        Returns:
            List[Any]: Danh sách các mục nội dung
        """
        content_items = []
        
        try:
            # Tìm các mục nội dung
            for selector in self.content_selectors:
                # Lấy tất cả các mục nội dung
                items = page.query_selector_all(selector)
                content_items.extend(items)
            
            # Cập nhật thống kê
            self.stats["total_content_items"] = len(content_items)
            
            return content_items
        except Exception as e:
            if self.verbose:
                logger.error(f"Error getting content items: {str(e)}")
            return []

    def extract_content(self, page: Any, max_items: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Trích xuất nội dung từ trang.

        Args:
            page: Đối tượng trang web (Playwright/Selenium)
            max_items: Số lượng mục tối đa để trích xuất

        Returns:
            List[Dict[str, Any]]: Danh sách nội dung
        """
        content = []
        
        try:
            # Lấy các mục nội dung
            content_items = self.get_content_items(page)
            
            # Giới hạn số lượng mục
            if max_items is not None:
                content_items = content_items[:max_items]
            
            # Trích xuất nội dung từ mỗi mục
            for item in content_items:
                # Lấy HTML của mục
                html = item.inner_html()
                
                # Lấy text của mục
                text = item.text_content()
                
                # Lấy các thuộc tính của mục
                attributes = {}
                for attr in item.get_attribute_names():
                    attributes[attr] = item.get_attribute(attr)
                
                # Thêm vào danh sách nội dung
                content.append(
                    {
                        "html": html,
                        "text": text,
                        "attributes": attributes,
                    }
                )
            
            return content
        except Exception as e:
            if self.verbose:
                logger.error(f"Error extracting content: {str(e)}")
            return []

    def load_all_content(
        self, page: Any, max_scrolls: Optional[int] = None, max_items: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Tải tất cả nội dung từ trang.

        Args:
            page: Đối tượng trang web (Playwright/Selenium)
            max_scrolls: Số lần cuộn tối đa
            max_items: Số lượng mục tối đa để trích xuất

        Returns:
            List[Dict[str, Any]]: Danh sách nội dung
        """
        # Sử dụng max_scrolls từ tham số hoặc từ cấu hình
        max_scrolls = max_scrolls or self.max_scrolls
        
        # Đếm số lần cuộn
        scroll_count = 0
        
        # Cuộn trang cho đến khi đạt đến giới hạn hoặc hết nội dung
        while scroll_count < max_scrolls:
            # Kiểm tra xem đã hết nội dung hay chưa
            if self.is_no_more_content(page):
                break
            
            # Thử nhấp vào nút "Tải thêm" trước
            if self.click_load_more(page):
                # Đã nhấp vào nút "Tải thêm", không cần cuộn
                pass
            else:
                # Không có nút "Tải thêm", cuộn trang
                if not self.scroll_page(page):
                    # Không thể cuộn thêm
                    break
            
            # Tăng số lần cuộn
            scroll_count += 1
        
        # Trích xuất nội dung
        return self.extract_content(page, max_items)
