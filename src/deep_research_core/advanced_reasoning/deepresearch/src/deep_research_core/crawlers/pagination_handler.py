#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module cung cấp các lớp và phương thức để xử lý phân trang.

Module này cung cấp các lớp và phương thức để xử lý phân trang trên trang web,
bao gồm việc phát hiện phân trang, trích xuất liên kết phân trang, và điều hướng qua các trang.
"""

import re
import time
import logging
from typing import Dict, List, Any, Optional, Tuple, Set, Union
from bs4 import BeautifulSoup
from urllib.parse import urlparse, urljoin, parse_qs, urlunparse, urlencode

from ..utils.structured_logging import get_logger

# Thiết lập logging
logger = get_logger(__name__)


class PaginationHandler:
    """
    Lớp cung cấp các phương thức để xử lý phân trang.

    T<PERSON>h năng:
    - <PERSON><PERSON><PERSON> hiện phân trang trên trang web
    - Tr<PERSON><PERSON> xuất liên kết phân trang
    - <PERSON><PERSON><PERSON><PERSON> hướng qua các trang
    - Xử lý các loại phân trang khác nhau
    """

    def __init__(
        self,
        pagination_selectors: Optional[List[str]] = None,
        next_page_selectors: Optional[List[str]] = None,
        prev_page_selectors: Optional[List[str]] = None,
        page_number_selectors: Optional[List[str]] = None,
        max_pages: int = 10,
        max_page_depth: int = 5,
        detect_infinite_scroll: bool = True,
        detect_load_more: bool = True,
        detect_ajax_pagination: bool = True,
        detect_url_patterns: bool = True,
        url_param_page_names: Optional[List[str]] = None,
        url_path_page_patterns: Optional[List[str]] = None,
        verbose: bool = False,
    ):
        """
        Khởi tạo PaginationHandler.

        Args:
            pagination_selectors: Các CSS selector để tìm phân trang
            next_page_selectors: Các CSS selector để tìm nút "Trang tiếp theo"
            prev_page_selectors: Các CSS selector để tìm nút "Trang trước"
            page_number_selectors: Các CSS selector để tìm số trang
            max_pages: Số lượng trang tối đa để xử lý
            max_page_depth: Độ sâu tối đa của phân trang
            detect_infinite_scroll: Phát hiện cuộn vô hạn hay không
            detect_load_more: Phát hiện nút "Tải thêm" hay không
            detect_ajax_pagination: Phát hiện phân trang AJAX hay không
            detect_url_patterns: Phát hiện mẫu URL hay không
            url_param_page_names: Các tên tham số URL cho số trang
            url_path_page_patterns: Các mẫu đường dẫn URL cho số trang
            verbose: Ghi log chi tiết hay không
        """
        # Cấu hình
        self.pagination_selectors = pagination_selectors or [
            ".pagination",
            ".pager",
            ".pages",
            "nav.pagination",
            "ul.pagination",
            "div.pagination",
            ".page-numbers",
            ".paginate",
            ".paginator",
        ]
        self.next_page_selectors = next_page_selectors or [
            "a.next",
            "a.next-page",
            "a[rel=next]",
            "a[aria-label=Next]",
            "a:contains(Next)",
            "a:contains(Tiếp)",
            "a:contains(Tiếp theo)",
            "a:contains(Trang sau)",
            "a:contains(Trang tiếp)",
            "a:contains(»)",
            "a:contains(>)",
            "a.page-next",
            "a.pagination-next",
            "a.next-page-link",
            "a.next-posts-link",
            "a.nextpostslink",
            "a.next-link",
            "a.next-button",
            "a.next-page-button",
            "a.next-posts",
            "a.next-post",
            "a.next-posts-page",
            "a.next-page-posts",
            "a.next-page-post",
            "a.next-post-page",
            "a.next-post-link",
            "a.next-posts-page-link",
            "a.next-page-posts-link",
            "a.next-page-post-link",
            "a.next-post-page-link",
        ]
        self.prev_page_selectors = prev_page_selectors or [
            "a.prev",
            "a.prev-page",
            "a[rel=prev]",
            "a[aria-label=Previous]",
            "a:contains(Previous)",
            "a:contains(Prev)",
            "a:contains(Trước)",
            "a:contains(Trang trước)",
            "a:contains(«)",
            "a:contains(<)",
            "a.page-prev",
            "a.pagination-prev",
            "a.prev-page-link",
            "a.prev-posts-link",
            "a.prevpostslink",
            "a.prev-link",
            "a.prev-button",
            "a.prev-page-button",
            "a.prev-posts",
            "a.prev-post",
            "a.prev-posts-page",
            "a.prev-page-posts",
            "a.prev-page-post",
            "a.prev-post-page",
            "a.prev-post-link",
            "a.prev-posts-page-link",
            "a.prev-page-posts-link",
            "a.prev-page-post-link",
            "a.prev-post-page-link",
        ]
        self.page_number_selectors = page_number_selectors or [
            "a.page-numbers",
            "a.page",
            "a.page-link",
            "a.page-item",
            "a.pagination-link",
            "a.pagination-item",
            "a.pagination-page",
            "a.page-pagination",
            "a.item-pagination",
            "a.link-pagination",
            "a.pagination-page-link",
            "a.page-pagination-link",
            "a.item-pagination-link",
            "a.link-pagination-page",
            "a.page-link-pagination",
            "a.pagination-link-page",
            "a.pagination-page-item",
            "a.page-pagination-item",
            "a.item-pagination-page",
            "a.page-item-pagination",
            "a.pagination-item-page",
            "a.pagination-page-number",
            "a.page-pagination-number",
            "a.number-pagination-page",
            "a.page-number-pagination",
            "a.pagination-number-page",
        ]
        self.max_pages = max_pages
        self.max_page_depth = max_page_depth
        self.detect_infinite_scroll = detect_infinite_scroll
        self.detect_load_more = detect_load_more
        self.detect_ajax_pagination = detect_ajax_pagination
        self.detect_url_patterns = detect_url_patterns
        self.url_param_page_names = url_param_page_names or [
            "page",
            "p",
            "pg",
            "paged",
            "pagenum",
            "pageNumber",
            "page_number",
            "page-number",
            "page_num",
            "page-num",
            "pageNo",
            "pageno",
            "page_no",
            "page-no",
            "trang",
            "sotrang",
            "so-trang",
            "so_trang",
            "trangso",
            "trang-so",
            "trang_so",
        ]
        self.url_path_page_patterns = url_path_page_patterns or [
            r"/page/(\d+)",
            r"/p/(\d+)",
            r"/trang/(\d+)",
            r"/trang-(\d+)",
            r"/page-(\d+)",
            r"/p-(\d+)",
            r"/pages/(\d+)",
            r"/pages-(\d+)",
            r"/paged/(\d+)",
            r"/paged-(\d+)",
            r"/paging/(\d+)",
            r"/paging-(\d+)",
            r"/pagination/(\d+)",
            r"/pagination-(\d+)",
        ]
        self.verbose = verbose

        # Khởi tạo các thuộc tính khác
        self.stats = {
            "pagination_detected": 0,
            "next_pages_found": 0,
            "prev_pages_found": 0,
            "page_numbers_found": 0,
            "infinite_scroll_detected": 0,
            "load_more_detected": 0,
            "ajax_pagination_detected": 0,
            "url_patterns_detected": 0,
        }

    def detect_pagination(self, content: str, url: str) -> Dict[str, Any]:
        """
        Phát hiện phân trang trên trang web.

        Args:
            content: Nội dung HTML
            url: URL của trang web

        Returns:
            Dict[str, Any]: Thông tin phân trang
        """
        # Khởi tạo kết quả
        result = {
            "has_pagination": False,
            "pagination_type": "none",
            "next_page": None,
            "prev_page": None,
            "current_page": 1,
            "total_pages": 1,
            "page_numbers": [],
            "pagination_elements": [],
        }

        # Sử dụng BeautifulSoup để phân tích HTML
        soup = BeautifulSoup(content, "html.parser")

        # Phát hiện phân trang dựa trên các selector
        pagination_elements = []
        for selector in self.pagination_selectors:
            elements = soup.select(selector)
            pagination_elements.extend(elements)

        # Nếu tìm thấy phân trang
        if pagination_elements:
            result["has_pagination"] = True
            result["pagination_type"] = "standard"
            result["pagination_elements"] = [str(el) for el in pagination_elements]
            self.stats["pagination_detected"] += 1

            # Tìm nút "Trang tiếp theo"
            next_page = None
            for selector in self.next_page_selectors:
                next_elements = soup.select(selector)
                if next_elements:
                    next_href = next_elements[0].get("href")
                    if next_href:
                        next_page = urljoin(url, next_href)
                        break

            if next_page:
                result["next_page"] = next_page
                self.stats["next_pages_found"] += 1

            # Tìm nút "Trang trước"
            prev_page = None
            for selector in self.prev_page_selectors:
                prev_elements = soup.select(selector)
                if prev_elements:
                    prev_href = prev_elements[0].get("href")
                    if prev_href:
                        prev_page = urljoin(url, prev_href)
                        break

            if prev_page:
                result["prev_page"] = prev_page
                self.stats["prev_pages_found"] += 1

            # Tìm các số trang
            page_numbers = []
            for selector in self.page_number_selectors:
                page_elements = soup.select(selector)
                for element in page_elements:
                    page_href = element.get("href")
                    page_text = element.get_text().strip()
                    
                    # Kiểm tra xem text có phải là số không
                    if page_text.isdigit():
                        page_number = int(page_text)
                        page_url = urljoin(url, page_href) if page_href else None
                        
                        page_numbers.append(
                            {"number": page_number, "url": page_url, "text": page_text}
                        )

            if page_numbers:
                result["page_numbers"] = page_numbers
                self.stats["page_numbers_found"] += len(page_numbers)
                
                # Xác định trang hiện tại và tổng số trang
                current_page = 1
                total_pages = 1
                
                # Tìm trang hiện tại (thường là trang được đánh dấu là active)
                for element in pagination_elements:
                    active_elements = element.select(".active, .current, .selected")
                    for active in active_elements:
                        active_text = active.get_text().strip()
                        if active_text.isdigit():
                            current_page = int(active_text)
                            break
                
                # Tổng số trang là số trang lớn nhất
                if page_numbers:
                    total_pages = max(page["number"] for page in page_numbers)
                
                result["current_page"] = current_page
                result["total_pages"] = total_pages

        # Phát hiện phân trang dựa trên URL
        if self.detect_url_patterns and not result["has_pagination"]:
            # Phân tích URL
            parsed_url = urlparse(url)
            query_params = parse_qs(parsed_url.query)
            
            # Kiểm tra tham số trang trong URL
            for param_name in self.url_param_page_names:
                if param_name in query_params:
                    result["has_pagination"] = True
                    result["pagination_type"] = "url_param"
                    
                    # Lấy số trang hiện tại
                    current_page = int(query_params[param_name][0])
                    result["current_page"] = current_page
                    
                    # Tạo URL cho trang tiếp theo
                    if current_page < self.max_pages:
                        next_query_params = query_params.copy()
                        next_query_params[param_name] = [str(current_page + 1)]
                        next_query = urlencode(next_query_params, doseq=True)
                        next_url = urlunparse(
                            (
                                parsed_url.scheme,
                                parsed_url.netloc,
                                parsed_url.path,
                                parsed_url.params,
                                next_query,
                                parsed_url.fragment,
                            )
                        )
                        result["next_page"] = next_url
                        self.stats["next_pages_found"] += 1
                    
                    # Tạo URL cho trang trước
                    if current_page > 1:
                        prev_query_params = query_params.copy()
                        prev_query_params[param_name] = [str(current_page - 1)]
                        prev_query = urlencode(prev_query_params, doseq=True)
                        prev_url = urlunparse(
                            (
                                parsed_url.scheme,
                                parsed_url.netloc,
                                parsed_url.path,
                                parsed_url.params,
                                prev_query,
                                parsed_url.fragment,
                            )
                        )
                        result["prev_page"] = prev_url
                        self.stats["prev_pages_found"] += 1
                    
                    self.stats["url_patterns_detected"] += 1
                    break
            
            # Kiểm tra mẫu đường dẫn trong URL
            if not result["has_pagination"]:
                path = parsed_url.path
                for pattern in self.url_path_page_patterns:
                    match = re.search(pattern, path)
                    if match:
                        result["has_pagination"] = True
                        result["pagination_type"] = "url_path"
                        
                        # Lấy số trang hiện tại
                        current_page = int(match.group(1))
                        result["current_page"] = current_page
                        
                        # Tạo URL cho trang tiếp theo
                        if current_page < self.max_pages:
                            next_path = re.sub(
                                pattern, f"{match.group(0)[:-len(match.group(1))]}{current_page + 1}", path
                            )
                            next_url = urlunparse(
                                (
                                    parsed_url.scheme,
                                    parsed_url.netloc,
                                    next_path,
                                    parsed_url.params,
                                    parsed_url.query,
                                    parsed_url.fragment,
                                )
                            )
                            result["next_page"] = next_url
                            self.stats["next_pages_found"] += 1
                        
                        # Tạo URL cho trang trước
                        if current_page > 1:
                            prev_path = re.sub(
                                pattern, f"{match.group(0)[:-len(match.group(1))]}{current_page - 1}", path
                            )
                            prev_url = urlunparse(
                                (
                                    parsed_url.scheme,
                                    parsed_url.netloc,
                                    prev_path,
                                    parsed_url.params,
                                    parsed_url.query,
                                    parsed_url.fragment,
                                )
                            )
                            result["prev_page"] = prev_url
                            self.stats["prev_pages_found"] += 1
                        
                        self.stats["url_patterns_detected"] += 1
                        break

        # Phát hiện cuộn vô hạn
        if self.detect_infinite_scroll and not result["has_pagination"]:
            # Tìm các dấu hiệu của cuộn vô hạn
            infinite_scroll_keywords = [
                "infinite scroll",
                "infinite-scroll",
                "infinitescroll",
                "endless scroll",
                "endless-scroll",
                "endlessscroll",
                "lazy load",
                "lazy-load",
                "lazyload",
            ]
            
            # Kiểm tra trong các thẻ script
            for script in soup.find_all("script"):
                script_content = script.string or ""
                if any(keyword in script_content.lower() for keyword in infinite_scroll_keywords):
                    result["has_pagination"] = True
                    result["pagination_type"] = "infinite_scroll"
                    self.stats["infinite_scroll_detected"] += 1
                    break

        # Phát hiện nút "Tải thêm"
        if self.detect_load_more and not result["has_pagination"]:
            # Tìm các dấu hiệu của nút "Tải thêm"
            load_more_selectors = [
                "a:contains(Load more)",
                "a:contains(Tải thêm)",
                "a:contains(Xem thêm)",
                "a:contains(More)",
                "a:contains(Show more)",
                "a:contains(View more)",
                "button:contains(Load more)",
                "button:contains(Tải thêm)",
                "button:contains(Xem thêm)",
                "button:contains(More)",
                "button:contains(Show more)",
                "button:contains(View more)",
                ".load-more",
                ".loadmore",
                ".load_more",
                ".more-items",
                ".more_items",
                ".more-posts",
                ".more_posts",
                ".view-more",
                ".viewmore",
                ".view_more",
                ".show-more",
                ".showmore",
                ".show_more",
            ]
            
            for selector in load_more_selectors:
                elements = soup.select(selector)
                if elements:
                    result["has_pagination"] = True
                    result["pagination_type"] = "load_more"
                    
                    # Lấy URL của nút "Tải thêm" (nếu có)
                    load_more_url = None
                    for element in elements:
                        if element.name == "a" and element.get("href"):
                            load_more_url = urljoin(url, element.get("href"))
                            break
                    
                    if load_more_url:
                        result["next_page"] = load_more_url
                        self.stats["next_pages_found"] += 1
                    
                    self.stats["load_more_detected"] += 1
                    break

        # Phát hiện phân trang AJAX
        if self.detect_ajax_pagination and not result["has_pagination"]:
            # Tìm các dấu hiệu của phân trang AJAX
            ajax_pagination_keywords = [
                "ajax pagination",
                "ajax-pagination",
                "ajaxpagination",
                "ajax paging",
                "ajax-paging",
                "ajaxpaging",
                "ajax load",
                "ajax-load",
                "ajaxload",
            ]
            
            # Kiểm tra trong các thẻ script
            for script in soup.find_all("script"):
                script_content = script.string or ""
                if any(keyword in script_content.lower() for keyword in ajax_pagination_keywords):
                    result["has_pagination"] = True
                    result["pagination_type"] = "ajax_pagination"
                    self.stats["ajax_pagination_detected"] += 1
                    break

        return result

    def get_next_page_url(self, pagination_info: Dict[str, Any]) -> Optional[str]:
        """
        Lấy URL của trang tiếp theo.

        Args:
            pagination_info: Thông tin phân trang

        Returns:
            Optional[str]: URL của trang tiếp theo hoặc None nếu không có
        """
        return pagination_info.get("next_page")

    def get_prev_page_url(self, pagination_info: Dict[str, Any]) -> Optional[str]:
        """
        Lấy URL của trang trước.

        Args:
            pagination_info: Thông tin phân trang

        Returns:
            Optional[str]: URL của trang trước hoặc None nếu không có
        """
        return pagination_info.get("prev_page")

    def get_page_urls(self, pagination_info: Dict[str, Any]) -> List[str]:
        """
        Lấy danh sách URL của các trang.

        Args:
            pagination_info: Thông tin phân trang

        Returns:
            List[str]: Danh sách URL của các trang
        """
        page_numbers = pagination_info.get("page_numbers", [])
        return [page["url"] for page in page_numbers if page["url"]]

    def get_pagination_type(self, pagination_info: Dict[str, Any]) -> str:
        """
        Lấy loại phân trang.

        Args:
            pagination_info: Thông tin phân trang

        Returns:
            str: Loại phân trang
        """
        return pagination_info.get("pagination_type", "none")

    def has_pagination(self, pagination_info: Dict[str, Any]) -> bool:
        """
        Kiểm tra xem có phân trang hay không.

        Args:
            pagination_info: Thông tin phân trang

        Returns:
            bool: True nếu có phân trang, False nếu không
        """
        return pagination_info.get("has_pagination", False)

    def get_current_page(self, pagination_info: Dict[str, Any]) -> int:
        """
        Lấy số trang hiện tại.

        Args:
            pagination_info: Thông tin phân trang

        Returns:
            int: Số trang hiện tại
        """
        return pagination_info.get("current_page", 1)

    def get_total_pages(self, pagination_info: Dict[str, Any]) -> int:
        """
        Lấy tổng số trang.

        Args:
            pagination_info: Thông tin phân trang

        Returns:
            int: Tổng số trang
        """
        return pagination_info.get("total_pages", 1)
