#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module cung cấp lớp ScreenshotManager, một công cụ để chụp và quản lý ảnh chụp màn hình.

Module này cung cấp lớp ScreenshotManager, một công cụ để chụp và quản lý ảnh chụp màn hình
của các trang web đã crawl, giúp lưu trữ và phân tích trực quan nội dung trang web.
"""

import os
import time
import logging
import hashlib
import base64
from typing import Dict, List, Any, Optional, Tuple, Set, Union, Callable
from urllib.parse import urlparse

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ScreenshotManager:
    """
    Lớp cung cấp các phương thức để chụp và quản lý ảnh chụp màn hình.

    Tính năng:
    - Chụp ảnh màn hình của trang web
    - Lưu trữ ảnh chụp màn hình
    - Hỗ trợ nhiều định dạng ảnh (PNG, JPEG, WebP)
    - Hỗ trợ chụp ảnh màn hình toàn trang
    - Hỗ trợ chụp ảnh màn hình của một phần trang
    - Hỗ trợ chụp ảnh màn hình của một phần tử
    - Hỗ trợ chụp ảnh màn hình với độ phân giải khác nhau
    """

    def __init__(
        self,
        output_dir: Optional[str] = None,
        format: str = "png",
        quality: int = 80,
        full_page: bool = True,
        width: int = 1280,
        height: int = 800,
        device_scale_factor: float = 1.0,
        wait_before_screenshot: float = 1.0,
        wait_for_network_idle: bool = True,
        wait_for_selector: Optional[str] = None,
        wait_timeout: float = 30.0,
        file_prefix: str = "screenshot",
        file_suffix: str = "",
        use_url_as_filename: bool = True,
        overwrite: bool = False,
        max_filename_length: int = 100,
        verbose: bool = False,
    ):
        """
        Khởi tạo ScreenshotManager.

        Args:
            output_dir: Thư mục đầu ra
            format: Định dạng ảnh ("png", "jpeg", "webp")
            quality: Chất lượng ảnh (0-100)
            full_page: Có chụp toàn bộ trang hay không
            width: Chiều rộng viewport
            height: Chiều cao viewport
            device_scale_factor: Tỷ lệ thiết bị
            wait_before_screenshot: Thời gian chờ trước khi chụp (giây)
            wait_for_network_idle: Có đợi mạng ổn định hay không
            wait_for_selector: Selector để đợi trước khi chụp
            wait_timeout: Thời gian chờ tối đa (giây)
            file_prefix: Tiền tố tên file
            file_suffix: Hậu tố tên file
            use_url_as_filename: Có sử dụng URL làm tên file hay không
            overwrite: Có ghi đè file hay không
            max_filename_length: Độ dài tối đa của tên file
            verbose: Ghi log chi tiết hay không
        """
        # Cấu hình
        self.output_dir = output_dir or os.path.join(os.getcwd(), "screenshots")
        self.format = format
        self.quality = quality
        self.full_page = full_page
        self.width = width
        self.height = height
        self.device_scale_factor = device_scale_factor
        self.wait_before_screenshot = wait_before_screenshot
        self.wait_for_network_idle = wait_for_network_idle
        self.wait_for_selector = wait_for_selector
        self.wait_timeout = wait_timeout
        self.file_prefix = file_prefix
        self.file_suffix = file_suffix
        self.use_url_as_filename = use_url_as_filename
        self.overwrite = overwrite
        self.max_filename_length = max_filename_length
        self.verbose = verbose

        # Khởi tạo các thành phần
        self._initialize_components()

        logger.info(f"ScreenshotManager được khởi tạo với output_dir={self.output_dir}")

    def _initialize_components(self):
        """Khởi tạo các thành phần."""
        # Tạo thư mục đầu ra nếu chưa tồn tại
        os.makedirs(self.output_dir, exist_ok=True)

    def take_screenshot(
        self,
        page: Any,
        url: str,
        filename: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        Chụp ảnh màn hình của trang web.

        Args:
            page: Đối tượng Playwright Page
            url: URL của trang web
            filename: Tên file (None để tạo tên file tự động)
            **kwargs: Các tham số bổ sung

        Returns:
            str: Đường dẫn đến file ảnh chụp màn hình
        """
        try:
            # Tạo tên file nếu chưa có
            if filename is None:
                if self.use_url_as_filename:
                    filename = self._url_to_filename(url)
                else:
                    import time
                    timestamp = time.strftime("%Y%m%d%H%M%S")
                    filename = f"{self.file_prefix}_{timestamp}{self.file_suffix}"

            # Thêm phần mở rộng nếu chưa có
            if not filename.endswith(f".{self.format}"):
                filename = f"{filename}.{self.format}"

            # Đường dẫn đầy đủ
            filepath = os.path.join(self.output_dir, filename)

            # Kiểm tra xem file đã tồn tại chưa
            if os.path.exists(filepath) and not self.overwrite:
                logger.warning(f"File đã tồn tại: {filepath}")
                # Thêm số thứ tự vào tên file
                i = 1
                while os.path.exists(filepath):
                    filename = f"{os.path.splitext(filename)[0]}_{i}.{self.format}"
                    filepath = os.path.join(self.output_dir, filename)
                    i += 1

            # Thiết lập viewport
            page.set_viewport_size({"width": self.width, "height": self.height})

            # Đợi trước khi chụp
            if self.wait_before_screenshot > 0:
                page.wait_for_timeout(self.wait_before_screenshot * 1000)

            # Đợi mạng ổn định nếu cần
            if self.wait_for_network_idle:
                page.wait_for_load_state("networkidle", timeout=self.wait_timeout * 1000)

            # Đợi selector nếu cần
            if self.wait_for_selector:
                page.wait_for_selector(self.wait_for_selector, timeout=self.wait_timeout * 1000)

            # Chụp ảnh màn hình
            screenshot_options = {
                "path": filepath,
                "type": self.format,
                "full_page": self.full_page
            }

            # Thêm quality nếu không phải PNG
            if self.format != "png":
                screenshot_options["quality"] = self.quality

            # Ghi đè các tham số nếu có
            screenshot_options.update(kwargs)

            # Chụp ảnh màn hình
            page.screenshot(**screenshot_options)

            logger.info(f"Đã chụp ảnh màn hình: {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"Lỗi khi chụp ảnh màn hình: {str(e)}")
            return ""

    def take_element_screenshot(
        self,
        page: Any,
        selector: str,
        url: str,
        filename: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        Chụp ảnh màn hình của một phần tử.

        Args:
            page: Đối tượng Playwright Page
            selector: CSS selector của phần tử
            url: URL của trang web
            filename: Tên file (None để tạo tên file tự động)
            **kwargs: Các tham số bổ sung

        Returns:
            str: Đường dẫn đến file ảnh chụp màn hình
        """
        try:
            # Tạo tên file nếu chưa có
            if filename is None:
                if self.use_url_as_filename:
                    filename = self._url_to_filename(url)
                    filename = f"{filename}_element"
                else:
                    import time
                    timestamp = time.strftime("%Y%m%d%H%M%S")
                    filename = f"{self.file_prefix}_element_{timestamp}{self.file_suffix}"

            # Thêm phần mở rộng nếu chưa có
            if not filename.endswith(f".{self.format}"):
                filename = f"{filename}.{self.format}"

            # Đường dẫn đầy đủ
            filepath = os.path.join(self.output_dir, filename)

            # Kiểm tra xem file đã tồn tại chưa
            if os.path.exists(filepath) and not self.overwrite:
                logger.warning(f"File đã tồn tại: {filepath}")
                # Thêm số thứ tự vào tên file
                i = 1
                while os.path.exists(filepath):
                    filename = f"{os.path.splitext(filename)[0]}_{i}.{self.format}"
                    filepath = os.path.join(self.output_dir, filename)
                    i += 1

            # Đợi selector
            element = page.wait_for_selector(selector, timeout=self.wait_timeout * 1000)
            if element is None:
                logger.error(f"Không tìm thấy phần tử: {selector}")
                return ""

            # Chụp ảnh màn hình của phần tử
            screenshot_options = {
                "path": filepath,
                "type": self.format
            }

            # Thêm quality nếu không phải PNG
            if self.format != "png":
                screenshot_options["quality"] = self.quality

            # Ghi đè các tham số nếu có
            screenshot_options.update(kwargs)

            # Chụp ảnh màn hình
            element.screenshot(**screenshot_options)

            logger.info(f"Đã chụp ảnh màn hình phần tử: {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"Lỗi khi chụp ảnh màn hình phần tử: {str(e)}")
            return ""

    def take_full_page_screenshot(
        self,
        browser: Any,
        url: str,
        filename: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        Chụp ảnh màn hình toàn trang.

        Args:
            browser: Đối tượng Playwright Browser
            url: URL của trang web
            filename: Tên file (None để tạo tên file tự động)
            **kwargs: Các tham số bổ sung

        Returns:
            str: Đường dẫn đến file ảnh chụp màn hình
        """
        try:
            # Tạo context và page
            context = browser.new_context(
                viewport={"width": self.width, "height": self.height},
                device_scale_factor=self.device_scale_factor
            )
            page = context.new_page()

            # Điều hướng đến URL
            page.goto(url, wait_until="load", timeout=self.wait_timeout * 1000)

            # Chụp ảnh màn hình
            filepath = self.take_screenshot(page, url, filename, **kwargs)

            # Đóng context
            context.close()

            return filepath
        except Exception as e:
            logger.error(f"Lỗi khi chụp ảnh màn hình toàn trang: {str(e)}")
            return ""

    def _url_to_filename(self, url: str) -> str:
        """
        Chuyển đổi URL thành tên file.

        Args:
            url: URL cần chuyển đổi

        Returns:
            str: Tên file
        """
        # Phân tích URL
        parsed_url = urlparse(url)
        domain = parsed_url.netloc
        path = parsed_url.path

        # Tạo tên file
        if path and path != "/":
            # Loại bỏ các ký tự không hợp lệ
            path = path.replace("/", "_").replace(".", "_").replace("?", "_").replace("&", "_")
            filename = f"{domain}{path}"
        else:
            filename = domain

        # Thêm tiền tố và hậu tố
        filename = f"{self.file_prefix}_{filename}{self.file_suffix}"

        # Giới hạn độ dài
        if len(filename) > self.max_filename_length:
            # Tạo hash cho phần vượt quá
            hash_part = hashlib.md5(filename.encode()).hexdigest()[:8]
            filename = f"{filename[:self.max_filename_length - 9]}_{hash_part}"

        return filename

    def get_screenshot_as_base64(
        self,
        page: Any,
        **kwargs
    ) -> str:
        """
        Lấy ảnh chụp màn hình dưới dạng base64.

        Args:
            page: Đối tượng Playwright Page
            **kwargs: Các tham số bổ sung

        Returns:
            str: Ảnh chụp màn hình dưới dạng base64
        """
        try:
            # Thiết lập viewport
            page.set_viewport_size({"width": self.width, "height": self.height})

            # Đợi trước khi chụp
            if self.wait_before_screenshot > 0:
                page.wait_for_timeout(self.wait_before_screenshot * 1000)

            # Đợi mạng ổn định nếu cần
            if self.wait_for_network_idle:
                page.wait_for_load_state("networkidle", timeout=self.wait_timeout * 1000)

            # Đợi selector nếu cần
            if self.wait_for_selector:
                page.wait_for_selector(self.wait_for_selector, timeout=self.wait_timeout * 1000)

            # Chụp ảnh màn hình
            screenshot_options = {
                "type": self.format,
                "full_page": self.full_page
            }

            # Thêm quality nếu không phải PNG
            if self.format != "png":
                screenshot_options["quality"] = self.quality

            # Ghi đè các tham số nếu có
            screenshot_options.update(kwargs)

            # Chụp ảnh màn hình
            screenshot_bytes = page.screenshot(**screenshot_options)

            # Chuyển đổi sang base64
            base64_screenshot = base64.b64encode(screenshot_bytes).decode("utf-8")

            return base64_screenshot
        except Exception as e:
            logger.error(f"Lỗi khi lấy ảnh chụp màn hình dưới dạng base64: {str(e)}")
            return ""
