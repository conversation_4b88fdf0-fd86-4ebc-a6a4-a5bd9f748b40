#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module cung cấp các lớp và phương thức để xử lý JavaScript trên trang web.

Module này cung cấp các lớp và phương thức để xử lý JavaScript trên trang web,
bao gồm việc phát hiện JavaScript, thực thi JavaScript, và xử lý các tương tác JavaScript.
"""

import re
import time
import logging
import json
from typing import Dict, List, Any, Optional, Tuple, Set, Union
from bs4 import BeautifulSoup
from urllib.parse import urlparse, urljoin

from ..utils.structured_logging import get_logger

# Thiết lập logging
logger = get_logger(__name__)


class JavaScriptHandler:
    """
    Lớp cung cấp các phương thức để xử lý JavaScript trên trang web.

    Tính năng:
    - Phát hiện JavaScript trên trang web
    - Thực thi JavaScript
    - Xử lý các tương tác JavaScript
    - Xử lý các loại JavaScript khác nhau
    """

    def __init__(
        self,
        browser: Optional[Any] = None,
        detect_spa: bool = True,
        detect_ajax: bool = True,
        detect_lazy_loading: bool = True,
        detect_infinite_scroll: bool = True,
        detect_event_handlers: bool = True,
        detect_form_validation: bool = True,
        detect_dynamic_content: bool = True,
        wait_for_load: bool = True,
        wait_for_network_idle: bool = True,
        wait_for_animations: bool = True,
        wait_timeout: float = 30.0,
        execute_on_load: bool = True,
        execute_on_dom_content_loaded: bool = True,
        execute_on_page_load: bool = True,
        execute_on_ready: bool = True,
        max_wait_time: float = 60.0,
        max_execution_time: float = 10.0,
        max_scripts: int = 100,
        verbose: bool = False,
    ):
        """
        Khởi tạo JavaScriptHandler.

        Args:
            browser: Đối tượng trình duyệt (Playwright/Selenium)
            detect_spa: Phát hiện Single Page Application hay không
            detect_ajax: Phát hiện AJAX hay không
            detect_lazy_loading: Phát hiện lazy loading hay không
            detect_infinite_scroll: Phát hiện infinite scroll hay không
            detect_event_handlers: Phát hiện event handlers hay không
            detect_form_validation: Phát hiện form validation hay không
            detect_dynamic_content: Phát hiện dynamic content hay không
            wait_for_load: Chờ trang tải xong hay không
            wait_for_network_idle: Chờ mạng ổn định hay không
            wait_for_animations: Chờ animations hoàn thành hay không
            wait_timeout: Thời gian chờ tối đa (giây)
            execute_on_load: Thực thi JavaScript khi trang tải xong hay không
            execute_on_dom_content_loaded: Thực thi JavaScript khi DOM tải xong hay không
            execute_on_page_load: Thực thi JavaScript khi trang tải xong hay không
            execute_on_ready: Thực thi JavaScript khi trang sẵn sàng hay không
            max_wait_time: Thời gian chờ tối đa (giây)
            max_execution_time: Thời gian thực thi tối đa (giây)
            max_scripts: Số lượng scripts tối đa để thực thi
            verbose: Ghi log chi tiết hay không
        """
        # Cấu hình
        self.browser = browser
        self.detect_spa = detect_spa
        self.detect_ajax = detect_ajax
        self.detect_lazy_loading = detect_lazy_loading
        self.detect_infinite_scroll = detect_infinite_scroll
        self.detect_event_handlers = detect_event_handlers
        self.detect_form_validation = detect_form_validation
        self.detect_dynamic_content = detect_dynamic_content
        self.wait_for_load = wait_for_load
        self.wait_for_network_idle = wait_for_network_idle
        self.wait_for_animations = wait_for_animations
        self.wait_timeout = wait_timeout
        self.execute_on_load = execute_on_load
        self.execute_on_dom_content_loaded = execute_on_dom_content_loaded
        self.execute_on_page_load = execute_on_page_load
        self.execute_on_ready = execute_on_ready
        self.max_wait_time = max_wait_time
        self.max_execution_time = max_execution_time
        self.max_scripts = max_scripts
        self.verbose = verbose

        # Khởi tạo các thuộc tính khác
        self.stats = {
            "scripts_detected": 0,
            "scripts_executed": 0,
            "ajax_requests_detected": 0,
            "spa_detected": 0,
            "lazy_loading_detected": 0,
            "infinite_scroll_detected": 0,
            "event_handlers_detected": 0,
            "form_validation_detected": 0,
            "dynamic_content_detected": 0,
            "execution_errors": 0,
            "execution_timeouts": 0,
        }

    def detect_javascript(self, content: str) -> Dict[str, Any]:
        """
        Phát hiện JavaScript trên trang web.

        Args:
            content: Nội dung HTML

        Returns:
            Dict[str, Any]: Thông tin JavaScript
        """
        # Khởi tạo kết quả
        result = {
            "has_javascript": False,
            "scripts": [],
            "inline_scripts": [],
            "external_scripts": [],
            "event_handlers": [],
            "ajax_requests": [],
            "is_spa": False,
            "has_lazy_loading": False,
            "has_infinite_scroll": False,
            "has_form_validation": False,
            "has_dynamic_content": False,
        }

        # Sử dụng BeautifulSoup để phân tích HTML
        soup = BeautifulSoup(content, "html.parser")

        # Tìm tất cả script tags
        script_tags = soup.find_all("script")
        
        # Nếu có script tags, trang có JavaScript
        if script_tags:
            result["has_javascript"] = True
            
            # Phân tích từng script tag
            for script in script_tags:
                # Lấy src attribute
                src = script.get("src", "")
                
                # Lấy type attribute
                script_type = script.get("type", "")
                
                # Lấy nội dung script
                script_content = script.string or ""
                
                # Tạo script object
                script_obj = {
                    "src": src,
                    "type": script_type,
                    "content": script_content,
                    "is_external": bool(src),
                    "is_module": script_type == "module",
                    "is_async": script.has_attr("async"),
                    "is_defer": script.has_attr("defer"),
                }
                
                # Thêm vào danh sách scripts
                result["scripts"].append(script_obj)
                
                # Phân loại script
                if src:
                    result["external_scripts"].append(script_obj)
                else:
                    result["inline_scripts"].append(script_obj)
                
                # Phát hiện AJAX
                if self.detect_ajax and "ajax" in script_content.lower():
                    result["ajax_requests"].append(script_obj)
                    self.stats["ajax_requests_detected"] += 1
                
                # Phát hiện SPA
                if self.detect_spa and any(
                    framework in script_content.lower()
                    for framework in ["react", "vue", "angular", "ember", "backbone", "knockout"]
                ):
                    result["is_spa"] = True
                    self.stats["spa_detected"] += 1
                
                # Phát hiện lazy loading
                if self.detect_lazy_loading and "lazy" in script_content.lower():
                    result["has_lazy_loading"] = True
                    self.stats["lazy_loading_detected"] += 1
                
                # Phát hiện infinite scroll
                if self.detect_infinite_scroll and "scroll" in script_content.lower():
                    result["has_infinite_scroll"] = True
                    self.stats["infinite_scroll_detected"] += 1
                
                # Phát hiện form validation
                if self.detect_form_validation and "valid" in script_content.lower():
                    result["has_form_validation"] = True
                    self.stats["form_validation_detected"] += 1
                
                # Phát hiện dynamic content
                if self.detect_dynamic_content and "append" in script_content.lower():
                    result["has_dynamic_content"] = True
                    self.stats["dynamic_content_detected"] += 1

        # Tìm tất cả các event handlers
        if self.detect_event_handlers:
            # Tìm tất cả các thẻ có attribute bắt đầu bằng "on"
            for tag in soup.find_all(lambda tag: any(attr.startswith("on") for attr in tag.attrs)):
                for attr, value in tag.attrs.items():
                    if attr.startswith("on"):
                        # Tạo event handler object
                        event_handler = {
                            "event": attr[2:],  # Bỏ "on" ở đầu
                            "handler": value,
                            "element": tag.name,
                            "id": tag.get("id", ""),
                            "class": tag.get("class", []),
                        }
                        
                        # Thêm vào danh sách event handlers
                        result["event_handlers"].append(event_handler)
                        self.stats["event_handlers_detected"] += 1

        # Cập nhật thống kê
        self.stats["scripts_detected"] = len(result["scripts"])

        return result

    def execute_javascript(self, page: Any, script: str) -> Any:
        """
        Thực thi JavaScript.

        Args:
            page: Đối tượng trang web (Playwright/Selenium)
            script: Mã JavaScript cần thực thi

        Returns:
            Any: Kết quả thực thi
        """
        try:
            # Thực thi JavaScript
            result = page.evaluate(script)
            
            # Cập nhật thống kê
            self.stats["scripts_executed"] += 1
            
            return result
        except Exception as e:
            # Cập nhật thống kê
            self.stats["execution_errors"] += 1
            
            if self.verbose:
                logger.error(f"Error executing JavaScript: {str(e)}")
            
            return None

    def wait_for_javascript(self, page: Any) -> bool:
        """
        Chờ JavaScript thực thi xong.

        Args:
            page: Đối tượng trang web (Playwright/Selenium)

        Returns:
            bool: True nếu chờ thành công, False nếu không
        """
        try:
            # Chờ trang tải xong
            if self.wait_for_load:
                page.wait_for_load_state("load", timeout=self.wait_timeout * 1000)
            
            # Chờ mạng ổn định
            if self.wait_for_network_idle:
                page.wait_for_load_state("networkidle", timeout=self.wait_timeout * 1000)
            
            # Chờ animations hoàn thành
            if self.wait_for_animations:
                # Thực thi JavaScript để chờ animations hoàn thành
                page.evaluate("""
                    () => {
                        return new Promise(resolve => {
                            const interval = setInterval(() => {
                                const animations = document.getAnimations();
                                if (animations.length === 0 || animations.every(a => a.playState === 'finished')) {
                                    clearInterval(interval);
                                    resolve();
                                }
                            }, 100);
                            
                            // Timeout sau 5 giây
                            setTimeout(() => {
                                clearInterval(interval);
                                resolve();
                            }, 5000);
                        });
                    }
                """)
            
            return True
        except Exception as e:
            if self.verbose:
                logger.error(f"Error waiting for JavaScript: {str(e)}")
            
            return False

    def execute_scripts(self, page: Any, scripts: List[Dict[str, Any]]) -> List[Any]:
        """
        Thực thi nhiều scripts.

        Args:
            page: Đối tượng trang web (Playwright/Selenium)
            scripts: Danh sách scripts cần thực thi

        Returns:
            List[Any]: Danh sách kết quả thực thi
        """
        results = []
        
        # Giới hạn số lượng scripts
        scripts = scripts[:self.max_scripts]
        
        # Thực thi từng script
        for script in scripts:
            # Bỏ qua các external scripts
            if script["is_external"]:
                continue
            
            # Lấy nội dung script
            script_content = script["content"]
            
            # Bỏ qua các script rỗng
            if not script_content:
                continue
            
            # Thực thi script
            result = self.execute_javascript(page, script_content)
            
            # Thêm vào danh sách kết quả
            results.append(result)
        
        return results

    def handle_spa(self, page: Any) -> bool:
        """
        Xử lý Single Page Application.

        Args:
            page: Đối tượng trang web (Playwright/Selenium)

        Returns:
            bool: True nếu xử lý thành công, False nếu không
        """
        if not self.detect_spa:
            return False
        
        try:
            # Kiểm tra xem trang có phải là SPA không
            is_spa = page.evaluate("""
                () => {
                    return !!(
                        window.React ||
                        window.Vue ||
                        window.Angular ||
                        window.Ember ||
                        window.Backbone ||
                        window.ko
                    );
                }
            """)
            
            if is_spa:
                # Cập nhật thống kê
                self.stats["spa_detected"] += 1
                
                # Chờ SPA tải xong
                page.wait_for_load_state("networkidle", timeout=self.wait_timeout * 1000)
                
                return True
            
            return False
        except Exception as e:
            if self.verbose:
                logger.error(f"Error handling SPA: {str(e)}")
            
            return False

    def handle_ajax(self, page: Any) -> bool:
        """
        Xử lý AJAX.

        Args:
            page: Đối tượng trang web (Playwright/Selenium)

        Returns:
            bool: True nếu xử lý thành công, False nếu không
        """
        if not self.detect_ajax:
            return False
        
        try:
            # Kiểm tra xem trang có sử dụng AJAX không
            has_ajax = page.evaluate("""
                () => {
                    return !!(
                        window.XMLHttpRequest ||
                        window.fetch ||
                        window.$ && window.$.ajax
                    );
                }
            """)
            
            if has_ajax:
                # Cập nhật thống kê
                self.stats["ajax_requests_detected"] += 1
                
                # Chờ AJAX hoàn thành
                page.wait_for_load_state("networkidle", timeout=self.wait_timeout * 1000)
                
                return True
            
            return False
        except Exception as e:
            if self.verbose:
                logger.error(f"Error handling AJAX: {str(e)}")
            
            return False

    def handle_lazy_loading(self, page: Any) -> bool:
        """
        Xử lý lazy loading.

        Args:
            page: Đối tượng trang web (Playwright/Selenium)

        Returns:
            bool: True nếu xử lý thành công, False nếu không
        """
        if not self.detect_lazy_loading:
            return False
        
        try:
            # Kiểm tra xem trang có sử dụng lazy loading không
            has_lazy_loading = page.evaluate("""
                () => {
                    const lazyElements = document.querySelectorAll('[loading="lazy"], [data-src], [data-srcset]');
                    return lazyElements.length > 0;
                }
            """)
            
            if has_lazy_loading:
                # Cập nhật thống kê
                self.stats["lazy_loading_detected"] += 1
                
                # Cuộn trang để kích hoạt lazy loading
                page.evaluate("""
                    () => {
                        window.scrollTo(0, document.body.scrollHeight);
                        window.scrollTo(0, 0);
                    }
                """)
                
                # Chờ một chút
                time.sleep(2)
                
                return True
            
            return False
        except Exception as e:
            if self.verbose:
                logger.error(f"Error handling lazy loading: {str(e)}")
            
            return False

    def get_ajax_requests(self, page: Any) -> List[Dict[str, Any]]:
        """
        Lấy danh sách các AJAX requests.

        Args:
            page: Đối tượng trang web (Playwright/Selenium)

        Returns:
            List[Dict[str, Any]]: Danh sách AJAX requests
        """
        try:
            # Lấy danh sách AJAX requests
            ajax_requests = page.evaluate("""
                () => {
                    if (!window._ajaxRequests) {
                        window._ajaxRequests = [];
                        
                        // Ghi lại XMLHttpRequest
                        const originalXHR = window.XMLHttpRequest;
                        window.XMLHttpRequest = function() {
                            const xhr = new originalXHR();
                            const originalOpen = xhr.open;
                            const originalSend = xhr.send;
                            
                            xhr.open = function(method, url) {
                                xhr._method = method;
                                xhr._url = url;
                                originalOpen.apply(xhr, arguments);
                            };
                            
                            xhr.send = function(data) {
                                window._ajaxRequests.push({
                                    method: xhr._method,
                                    url: xhr._url,
                                    data: data,
                                    timestamp: Date.now()
                                });
                                originalSend.apply(xhr, arguments);
                            };
                            
                            return xhr;
                        };
                        
                        // Ghi lại fetch
                        const originalFetch = window.fetch;
                        window.fetch = function(url, options = {}) {
                            window._ajaxRequests.push({
                                method: options.method || 'GET',
                                url: url,
                                data: options.body,
                                timestamp: Date.now()
                            });
                            return originalFetch.apply(window, arguments);
                        };
                        
                        // Ghi lại jQuery AJAX
                        if (window.$ && window.$.ajax) {
                            const originalAjax = window.$.ajax;
                            window.$.ajax = function(options) {
                                window._ajaxRequests.push({
                                    method: options.type || 'GET',
                                    url: options.url,
                                    data: options.data,
                                    timestamp: Date.now()
                                });
                                return originalAjax.apply(window.$, arguments);
                            };
                        }
                    }
                    
                    return window._ajaxRequests;
                }
            """)
            
            return ajax_requests or []
        except Exception as e:
            if self.verbose:
                logger.error(f"Error getting AJAX requests: {str(e)}")
            
            return []
