#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module cung cấp các lớp và phương thức để xử lý việc trích xuất và tải xuống media.

Module này cung cấp các lớp và phương thức để xử lý việc trích xuất và tải xuống media
từ các trang web, bao gồm hình <PERSON>nh, video, audio, v.v.
"""

import os
import re
import time
import logging
import requests
from urllib.parse import urlparse, urljoin
from typing import Dict, List, Any, Optional, Tuple, Set, Union
from bs4 import BeautifulSoup
import concurrent.futures
import hashlib
import mimetypes
import shutil

from ..utils.structured_logging import get_logger

# Thiết lập logging
logger = get_logger(__name__)


class MediaHandler:
    """
    Lớp cung cấp các phương thức để xử lý việc trích xu<PERSON>t và tải xuống media.

    Tính năng:
    - <PERSON>r<PERSON><PERSON> xu<PERSON>t hình ảnh từ trang web
    - Tr<PERSON>ch xuất video từ trang web
    - Trích xuất audio từ trang web
    - Tải xuống media
    - Phân loại media
    - Xử lý các định dạng media khác nhau
    """

    def __init__(
        self,
        download_path: str = "downloads/media",
        max_size_mb: int = 10,
        timeout: float = 30.0,
        max_retries: int = 3,
        retry_delay: float = 2.0,
        user_agent: Optional[str] = None,
        verify_ssl: bool = True,
        proxies: Optional[Dict[str, str]] = None,
        cookies: Optional[Dict[str, str]] = None,
        headers: Optional[Dict[str, str]] = None,
        extract_css_images: bool = True,
        extract_background_images: bool = True,
        extract_video_thumbnails: bool = True,
        extract_video_sources: bool = True,
        extract_audio_sources: bool = True,
        extract_iframe_sources: bool = True,
        extract_svg_images: bool = True,
        extract_data_urls: bool = False,
        extract_base64_images: bool = False,
        verbose: bool = False,
    ):
        """
        Khởi tạo MediaHandler.

        Args:
            download_path: Đường dẫn để lưu media
            max_size_mb: Kích thước tối đa của media (MB)
            timeout: Thời gian timeout cho mỗi request (giây)
            max_retries: Số lần thử lại tối đa khi request thất bại
            retry_delay: Thời gian chờ giữa các lần thử lại (giây)
            user_agent: User-Agent header
            verify_ssl: Xác minh chứng chỉ SSL hay không
            proxies: Danh sách proxy
            cookies: Cookies cho request
            headers: Headers cho request
            extract_css_images: Trích xuất hình ảnh từ CSS hay không
            extract_background_images: Trích xuất hình ảnh nền hay không
            extract_video_thumbnails: Trích xuất thumbnail video hay không
            extract_video_sources: Trích xuất nguồn video hay không
            extract_audio_sources: Trích xuất nguồn audio hay không
            extract_iframe_sources: Trích xuất nguồn iframe hay không
            extract_svg_images: Trích xuất hình ảnh SVG hay không
            extract_data_urls: Trích xuất data URL hay không
            extract_base64_images: Trích xuất hình ảnh base64 hay không
            verbose: Ghi log chi tiết hay không
        """
        # Cấu hình
        self.download_path = download_path
        self.max_size_mb = max_size_mb
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.user_agent = user_agent or "MediaHandler/1.0"
        self.verify_ssl = verify_ssl
        self.proxies = proxies or {}
        self.cookies = cookies or {}
        self.headers = headers or {}
        self.extract_css_images = extract_css_images
        self.extract_background_images = extract_background_images
        self.extract_video_thumbnails = extract_video_thumbnails
        self.extract_video_sources = extract_video_sources
        self.extract_audio_sources = extract_audio_sources
        self.extract_iframe_sources = extract_iframe_sources
        self.extract_svg_images = extract_svg_images
        self.extract_data_urls = extract_data_urls
        self.extract_base64_images = extract_base64_images
        self.verbose = verbose

        # Khởi tạo session
        self.session = requests.Session()
        self.session.headers.update({"User-Agent": self.user_agent})
        if self.cookies:
            self.session.cookies.update(self.cookies)
        if self.headers:
            self.session.headers.update(self.headers)
        if self.proxies:
            self.session.proxies.update(self.proxies)

        # Tạo thư mục lưu trữ nếu chưa tồn tại
        os.makedirs(self.download_path, exist_ok=True)

        # Khởi tạo các thuộc tính khác
        self.stats = {
            "total_images_extracted": 0,
            "total_videos_extracted": 0,
            "total_audios_extracted": 0,
            "total_media_downloaded": 0,
            "total_download_size": 0,
            "failed_downloads": 0,
            "skipped_downloads": 0,
        }

    def extract_images(
        self, content: str, url: str, page: Optional[Any] = None
    ) -> List[Dict[str, Any]]:
        """
        Trích xuất hình ảnh từ nội dung.

        Args:
            content: Nội dung HTML
            url: URL của trang web
            page: Đối tượng trang web (nếu sử dụng Playwright/Selenium)

        Returns:
            List[Dict[str, Any]]: Danh sách hình ảnh
        """
        images = []
        base_url = url

        # Trích xuất base tag
        base_match = re.search(r'<base\s+href=["\'](.*?)["\']', content, re.IGNORECASE)
        if base_match:
            base_url = base_match.group(1).strip()

        # Sử dụng BeautifulSoup để phân tích HTML
        soup = BeautifulSoup(content, "html.parser")

        # Trích xuất img tags
        for img in soup.find_all("img"):
            src = img.get("src", "")
            if not src or src.startswith("data:") and not self.extract_data_urls:
                continue

            # Chuyển đổi relative URL thành absolute URL
            if not src.startswith("http"):
                src = urljoin(base_url, src)

            # Trích xuất thông tin
            alt = img.get("alt", "")
            title = img.get("title", "")
            width = img.get("width", "")
            height = img.get("height", "")

            # Thêm vào danh sách hình ảnh
            images.append(
                {
                    "url": src,
                    "alt": alt,
                    "title": title,
                    "width": width,
                    "height": height,
                    "source_url": url,
                    "type": "image",
                }
            )

        # Trích xuất hình ảnh SVG
        if self.extract_svg_images:
            for svg in soup.find_all("svg"):
                # Tạo ID duy nhất cho SVG
                svg_id = hashlib.md5(str(svg).encode()).hexdigest()
                
                # Thêm vào danh sách hình ảnh
                images.append(
                    {
                        "url": f"{url}#{svg_id}",
                        "alt": svg.get("aria-label", ""),
                        "title": svg.get("title", ""),
                        "width": svg.get("width", ""),
                        "height": svg.get("height", ""),
                        "source_url": url,
                        "type": "svg",
                        "content": str(svg),
                    }
                )

        # Trích xuất hình ảnh nền từ style attribute
        if self.extract_background_images:
            for element in soup.find_all(lambda tag: tag.has_attr("style")):
                style = element.get("style", "")
                bg_match = re.search(r"background-image\s*:\s*url\(['\"]?(.*?)['\"]?\)", style)
                if bg_match:
                    bg_url = bg_match.group(1).strip()
                    
                    # Chuyển đổi relative URL thành absolute URL
                    if not bg_url.startswith("http"):
                        bg_url = urljoin(base_url, bg_url)
                    
                    # Thêm vào danh sách hình ảnh
                    images.append(
                        {
                            "url": bg_url,
                            "alt": "",
                            "title": "",
                            "width": "",
                            "height": "",
                            "source_url": url,
                            "type": "background-image",
                        }
                    )

        # Trích xuất hình ảnh từ CSS
        if self.extract_css_images:
            for style in soup.find_all("style"):
                css_content = style.string or ""
                css_urls = re.findall(r"url\(['\"]?(.*?)['\"]?\)", css_content)
                for css_url in css_urls:
                    if not css_url or css_url.startswith("data:") and not self.extract_data_urls:
                        continue
                    
                    # Chuyển đổi relative URL thành absolute URL
                    if not css_url.startswith("http"):
                        css_url = urljoin(base_url, css_url)
                    
                    # Thêm vào danh sách hình ảnh
                    images.append(
                        {
                            "url": css_url,
                            "alt": "",
                            "title": "",
                            "width": "",
                            "height": "",
                            "source_url": url,
                            "type": "css-image",
                        }
                    )

        # Cập nhật thống kê
        self.stats["total_images_extracted"] += len(images)

        return images

    def extract_videos(
        self, content: str, url: str, page: Optional[Any] = None
    ) -> List[Dict[str, Any]]:
        """
        Trích xuất video từ nội dung.

        Args:
            content: Nội dung HTML
            url: URL của trang web
            page: Đối tượng trang web (nếu sử dụng Playwright/Selenium)

        Returns:
            List[Dict[str, Any]]: Danh sách video
        """
        videos = []
        base_url = url

        # Trích xuất base tag
        base_match = re.search(r'<base\s+href=["\'](.*?)["\']', content, re.IGNORECASE)
        if base_match:
            base_url = base_match.group(1).strip()

        # Sử dụng BeautifulSoup để phân tích HTML
        soup = BeautifulSoup(content, "html.parser")

        # Trích xuất video tags
        for video in soup.find_all("video"):
            # Trích xuất nguồn video
            if self.extract_video_sources:
                for source in video.find_all("source"):
                    src = source.get("src", "")
                    if not src:
                        continue
                    
                    # Chuyển đổi relative URL thành absolute URL
                    if not src.startswith("http"):
                        src = urljoin(base_url, src)
                    
                    # Trích xuất thông tin
                    type = source.get("type", "")
                    
                    # Thêm vào danh sách video
                    videos.append(
                        {
                            "url": src,
                            "type": type or "video",
                            "width": video.get("width", ""),
                            "height": video.get("height", ""),
                            "poster": video.get("poster", ""),
                            "controls": video.has_attr("controls"),
                            "autoplay": video.has_attr("autoplay"),
                            "loop": video.has_attr("loop"),
                            "muted": video.has_attr("muted"),
                            "source_url": url,
                        }
                    )
            
            # Trích xuất poster (thumbnail)
            if self.extract_video_thumbnails and video.has_attr("poster"):
                poster = video.get("poster", "")
                if poster:
                    # Chuyển đổi relative URL thành absolute URL
                    if not poster.startswith("http"):
                        poster = urljoin(base_url, poster)
                    
                    # Thêm vào danh sách video
                    videos.append(
                        {
                            "url": poster,
                            "type": "video-poster",
                            "width": video.get("width", ""),
                            "height": video.get("height", ""),
                            "source_url": url,
                        }
                    )

        # Trích xuất iframe (YouTube, Vimeo, v.v.)
        if self.extract_iframe_sources:
            for iframe in soup.find_all("iframe"):
                src = iframe.get("src", "")
                if not src:
                    continue
                
                # Chuyển đổi relative URL thành absolute URL
                if not src.startswith("http"):
                    src = urljoin(base_url, src)
                
                # Kiểm tra xem iframe có phải là video không
                is_video = False
                video_domains = ["youtube.com", "youtu.be", "vimeo.com", "dailymotion.com", "twitch.tv"]
                parsed_url = urlparse(src)
                domain = parsed_url.netloc
                
                if any(video_domain in domain for video_domain in video_domains):
                    is_video = True
                
                if is_video:
                    # Thêm vào danh sách video
                    videos.append(
                        {
                            "url": src,
                            "type": "iframe-video",
                            "width": iframe.get("width", ""),
                            "height": iframe.get("height", ""),
                            "source_url": url,
                            "domain": domain,
                        }
                    )

        # Cập nhật thống kê
        self.stats["total_videos_extracted"] += len(videos)

        return videos

    def extract_audio(
        self, content: str, url: str, page: Optional[Any] = None
    ) -> List[Dict[str, Any]]:
        """
        Trích xuất audio từ nội dung.

        Args:
            content: Nội dung HTML
            url: URL của trang web
            page: Đối tượng trang web (nếu sử dụng Playwright/Selenium)

        Returns:
            List[Dict[str, Any]]: Danh sách audio
        """
        audios = []
        base_url = url

        # Trích xuất base tag
        base_match = re.search(r'<base\s+href=["\'](.*?)["\']', content, re.IGNORECASE)
        if base_match:
            base_url = base_match.group(1).strip()

        # Sử dụng BeautifulSoup để phân tích HTML
        soup = BeautifulSoup(content, "html.parser")

        # Trích xuất audio tags
        for audio in soup.find_all("audio"):
            # Trích xuất nguồn audio
            if self.extract_audio_sources:
                for source in audio.find_all("source"):
                    src = source.get("src", "")
                    if not src:
                        continue
                    
                    # Chuyển đổi relative URL thành absolute URL
                    if not src.startswith("http"):
                        src = urljoin(base_url, src)
                    
                    # Trích xuất thông tin
                    type = source.get("type", "")
                    
                    # Thêm vào danh sách audio
                    audios.append(
                        {
                            "url": src,
                            "type": type or "audio",
                            "controls": audio.has_attr("controls"),
                            "autoplay": audio.has_attr("autoplay"),
                            "loop": audio.has_attr("loop"),
                            "muted": audio.has_attr("muted"),
                            "source_url": url,
                        }
                    )

        # Cập nhật thống kê
        self.stats["total_audios_extracted"] += len(audios)

        return audios

    def download_media(
        self, url: str, save_path: Optional[str] = None, media_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Tải xuống media.

        Args:
            url: URL của media
            save_path: Đường dẫn để lưu media
            media_type: Loại media

        Returns:
            Dict[str, Any]: Kết quả tải xuống
        """
        # Khởi tạo kết quả
        result = {
            "success": False,
            "url": url,
            "file_path": "",
            "file_size": 0,
            "content_type": "",
            "download_time": 0,
            "error": "",
        }

        # Xác định đường dẫn lưu
        if not save_path:
            # Tạo tên file từ URL
            parsed_url = urlparse(url)
            path = parsed_url.path
            filename = path.split("/")[-1]
            
            # Nếu không có tên file, tạo tên file từ hash của URL
            if not filename or "." not in filename:
                filename = hashlib.md5(url.encode()).hexdigest()
                
                # Thêm phần mở rộng dựa trên media_type
                if media_type:
                    if media_type == "image":
                        filename += ".jpg"
                    elif media_type == "video":
                        filename += ".mp4"
                    elif media_type == "audio":
                        filename += ".mp3"
                    else:
                        filename += ".bin"
            
            save_path = os.path.join(self.download_path, filename)

        # Tạo thư mục cha nếu chưa tồn tại
        os.makedirs(os.path.dirname(save_path), exist_ok=True)

        # Thực hiện tải xuống
        start_time = time.time()
        
        try:
            # Tải xuống với stream để kiểm tra kích thước
            with self.session.get(
                url,
                stream=True,
                timeout=self.timeout,
                verify=self.verify_ssl,
                headers=self.headers,
                proxies=self.proxies,
                cookies=self.cookies,
            ) as response:
                # Kiểm tra status code
                if response.status_code != 200:
                    result["error"] = f"HTTP error: {response.status_code}"
                    self.stats["failed_downloads"] += 1
                    return result
                
                # Lấy content type
                content_type = response.headers.get("Content-Type", "")
                result["content_type"] = content_type
                
                # Kiểm tra kích thước
                content_length = int(response.headers.get("Content-Length", 0))
                if content_length > self.max_size_mb * 1024 * 1024:
                    result["error"] = f"File too large: {content_length} bytes"
                    self.stats["skipped_downloads"] += 1
                    return result
                
                # Tải xuống file
                with open(save_path, "wb") as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                
                # Lấy kích thước file
                file_size = os.path.getsize(save_path)
                result["file_size"] = file_size
                
                # Cập nhật kết quả
                result["success"] = True
                result["file_path"] = save_path
                
                # Cập nhật thống kê
                self.stats["total_media_downloaded"] += 1
                self.stats["total_download_size"] += file_size
        except Exception as e:
            result["error"] = str(e)
            self.stats["failed_downloads"] += 1
            return result
        
        # Tính thời gian tải xuống
        end_time = time.time()
        result["download_time"] = end_time - start_time
        
        return result
