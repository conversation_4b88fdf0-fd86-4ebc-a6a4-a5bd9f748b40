#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module tích hợp các tính năng nâng cao vào AdaptiveCrawler.

Module này tích hợp các tính năng nâng cao vào AdaptiveCrawler như crawl phân tán,
checkpoint và resume, proxy rotation, user agent rotation, xuất kết quả nhiều định dạng,
và lưu trữ ảnh chụp màn hình.
"""

import os
import logging
from typing import Dict, List, Any, Optional, Tuple, Set, Union, Callable

# Import các module cần thiết
try:
    from .distributed_crawler import DistributedCrawler
    DISTRIBUTED_CRAWLER_AVAILABLE = True
except ImportError:
    DISTRIBUTED_CRAWLER_AVAILABLE = False

try:
    from .checkpoint_manager import CheckpointManager
    CHECKPOINT_MANAGER_AVAILABLE = True
except ImportError:
    CHECKPOINT_MANAGER_AVAILABLE = False

try:
    from .proxy_rotation_manager import ProxyRotationManager
    PROXY_ROTATION_MANAGER_AVAILABLE = True
except ImportError:
    PROXY_ROTATION_MANAGER_AVAILABLE = False

try:
    from .user_agent_rotation_manager import UserAgentRotationManager
    USER_AGENT_ROTATION_MANAGER_AVAILABLE = True
except ImportError:
    USER_AGENT_ROTATION_MANAGER_AVAILABLE = False

try:
    from .result_exporter import ResultExporter
    RESULT_EXPORTER_AVAILABLE = True
except ImportError:
    RESULT_EXPORTER_AVAILABLE = False

try:
    from .screenshot_manager import ScreenshotManager
    SCREENSHOT_MANAGER_AVAILABLE = True
except ImportError:
    SCREENSHOT_MANAGER_AVAILABLE = False

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def integrate_advanced_features(crawler, config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Tích hợp các tính năng nâng cao vào AdaptiveCrawler.

    Args:
        crawler: AdaptiveCrawler instance
        config: Cấu hình cho các tính năng nâng cao

    Returns:
        Dict[str, Any]: Thông tin về các tính năng đã tích hợp
    """
    if config is None:
        config = {}

    # Kết quả tích hợp
    integration_results = {
        "distributed_crawler": False,
        "checkpoint_manager": False,
        "proxy_rotation_manager": False,
        "user_agent_rotation_manager": False,
        "result_exporter": False,
        "screenshot_manager": False
    }

    # Tích hợp DistributedCrawler
    if config.get("enable_distributed_crawler", False):
        integration_results["distributed_crawler"] = integrate_distributed_crawler(crawler, config.get("distributed_crawler", {}))

    # Tích hợp CheckpointManager
    if config.get("enable_checkpoint_manager", False):
        integration_results["checkpoint_manager"] = integrate_checkpoint_manager(crawler, config.get("checkpoint_manager", {}))

    # Tích hợp ProxyRotationManager
    if config.get("enable_proxy_rotation_manager", False):
        integration_results["proxy_rotation_manager"] = integrate_proxy_rotation_manager(crawler, config.get("proxy_rotation_manager", {}))

    # Tích hợp UserAgentRotationManager
    if config.get("enable_user_agent_rotation_manager", False):
        integration_results["user_agent_rotation_manager"] = integrate_user_agent_rotation_manager(crawler, config.get("user_agent_rotation_manager", {}))

    # Tích hợp ResultExporter
    if config.get("enable_result_exporter", False):
        integration_results["result_exporter"] = integrate_result_exporter(crawler, config.get("result_exporter", {}))

    # Tích hợp ScreenshotManager
    if config.get("enable_screenshot_manager", False):
        integration_results["screenshot_manager"] = integrate_screenshot_manager(crawler, config.get("screenshot_manager", {}))

    logger.info(f"Đã tích hợp các tính năng nâng cao: {integration_results}")
    return integration_results

def integrate_distributed_crawler(crawler, config: Dict[str, Any]) -> bool:
    """
    Tích hợp DistributedCrawler vào AdaptiveCrawler.

    Args:
        crawler: AdaptiveCrawler instance
        config: Cấu hình cho DistributedCrawler

    Returns:
        bool: True nếu tích hợp thành công, False nếu không
    """
    if not DISTRIBUTED_CRAWLER_AVAILABLE:
        logger.warning("DistributedCrawler không khả dụng")
        return False

    try:
        # Khởi tạo DistributedCrawler
        distributed_crawler = DistributedCrawler(
            master_node=config.get("master_node"),
            worker_nodes=config.get("worker_nodes"),
            port=config.get("port", 8765),
            distribution_mode=config.get("distribution_mode", "round_robin"),
            batch_size=config.get("batch_size", 10),
            timeout=config.get("timeout", 30.0),
            max_retries=config.get("max_retries", 3),
            retry_delay=config.get("retry_delay", 2.0),
            checkpoint_interval=config.get("checkpoint_interval", 100),
            checkpoint_path=config.get("checkpoint_path"),
            use_local_workers=config.get("use_local_workers", True),
            max_local_workers=config.get("max_local_workers", 4),
            verbose=config.get("verbose", False)
        )

        # Gán DistributedCrawler vào AdaptiveCrawler
        crawler.distributed_crawler = distributed_crawler

        # Gán phương thức crawl_distributed
        crawler.crawl_distributed = lambda urls, **kwargs: crawl_distributed(crawler, urls, **kwargs)

        logger.info("DistributedCrawler đã được tích hợp thành công")
        return True
    except Exception as e:
        logger.error(f"Lỗi khi tích hợp DistributedCrawler: {str(e)}")
        return False

def integrate_checkpoint_manager(crawler, config: Dict[str, Any]) -> bool:
    """
    Tích hợp CheckpointManager vào AdaptiveCrawler.

    Args:
        crawler: AdaptiveCrawler instance
        config: Cấu hình cho CheckpointManager

    Returns:
        bool: True nếu tích hợp thành công, False nếu không
    """
    if not CHECKPOINT_MANAGER_AVAILABLE:
        logger.warning("CheckpointManager không khả dụng")
        return False

    try:
        # Khởi tạo CheckpointManager
        checkpoint_manager = CheckpointManager(
            checkpoint_path=config.get("checkpoint_path"),
            checkpoint_interval=config.get("checkpoint_interval", 100),
            checkpoint_format=config.get("checkpoint_format", "json"),
            compress=config.get("compress", False),
            max_checkpoints=config.get("max_checkpoints", 5),
            auto_checkpoint=config.get("auto_checkpoint", True),
            verbose=config.get("verbose", False)
        )

        # Gán CheckpointManager vào AdaptiveCrawler
        crawler.checkpoint_manager = checkpoint_manager

        # Gán phương thức save_checkpoint và load_checkpoint
        crawler.save_checkpoint = lambda state, **kwargs: save_checkpoint(crawler, state, **kwargs)
        crawler.load_checkpoint = lambda **kwargs: load_checkpoint(crawler, **kwargs)

        logger.info("CheckpointManager đã được tích hợp thành công")
        return True
    except Exception as e:
        logger.error(f"Lỗi khi tích hợp CheckpointManager: {str(e)}")
        return False

def integrate_proxy_rotation_manager(crawler, config: Dict[str, Any]) -> bool:
    """
    Tích hợp ProxyRotationManager vào AdaptiveCrawler.

    Args:
        crawler: AdaptiveCrawler instance
        config: Cấu hình cho ProxyRotationManager

    Returns:
        bool: True nếu tích hợp thành công, False nếu không
    """
    if not PROXY_ROTATION_MANAGER_AVAILABLE:
        logger.warning("ProxyRotationManager không khả dụng")
        return False

    try:
        # Khởi tạo ProxyRotationManager
        proxy_rotation_manager = ProxyRotationManager(
            proxy_list=config.get("proxy_list"),
            proxy_file=config.get("proxy_file"),
            proxy_api_url=config.get("proxy_api_url"),
            proxy_api_key=config.get("proxy_api_key"),
            rotation_strategy=config.get("rotation_strategy", "round_robin"),
            check_proxy=config.get("check_proxy", True),
            check_interval=config.get("check_interval", 300),
            timeout=config.get("timeout", 10.0),
            max_retries=config.get("max_retries", 3),
            retry_delay=config.get("retry_delay", 2.0),
            auto_update=config.get("auto_update", False),
            update_interval=config.get("update_interval", 3600),
            min_proxies=config.get("min_proxies", 5),
            verbose=config.get("verbose", False)
        )

        # Gán ProxyRotationManager vào AdaptiveCrawler
        crawler.proxy_rotation_manager = proxy_rotation_manager

        # Gán phương thức get_proxy
        crawler.get_proxy = lambda **kwargs: get_proxy(crawler, **kwargs)

        logger.info("ProxyRotationManager đã được tích hợp thành công")
        return True
    except Exception as e:
        logger.error(f"Lỗi khi tích hợp ProxyRotationManager: {str(e)}")
        return False

def integrate_user_agent_rotation_manager(crawler, config: Dict[str, Any]) -> bool:
    """
    Tích hợp UserAgentRotationManager vào AdaptiveCrawler.

    Args:
        crawler: AdaptiveCrawler instance
        config: Cấu hình cho UserAgentRotationManager

    Returns:
        bool: True nếu tích hợp thành công, False nếu không
    """
    if not USER_AGENT_ROTATION_MANAGER_AVAILABLE:
        logger.warning("UserAgentRotationManager không khả dụng")
        return False

    try:
        # Khởi tạo UserAgentRotationManager
        user_agent_rotation_manager = UserAgentRotationManager(
            user_agent_list=config.get("user_agent_list"),
            user_agent_file=config.get("user_agent_file"),
            rotation_strategy=config.get("rotation_strategy", "round_robin"),
            device_type=config.get("device_type", "all"),
            browser_type=config.get("browser_type", "all"),
            os_type=config.get("os_type", "all"),
            include_random=config.get("include_random", False),
            random_ratio=config.get("random_ratio", 0.1),
            verbose=config.get("verbose", False)
        )

        # Gán UserAgentRotationManager vào AdaptiveCrawler
        crawler.user_agent_rotation_manager = user_agent_rotation_manager

        # Gán phương thức get_user_agent
        crawler.get_user_agent = lambda **kwargs: get_user_agent(crawler, **kwargs)

        logger.info("UserAgentRotationManager đã được tích hợp thành công")
        return True
    except Exception as e:
        logger.error(f"Lỗi khi tích hợp UserAgentRotationManager: {str(e)}")
        return False

def integrate_result_exporter(crawler, config: Dict[str, Any]) -> bool:
    """
    Tích hợp ResultExporter vào AdaptiveCrawler.

    Args:
        crawler: AdaptiveCrawler instance
        config: Cấu hình cho ResultExporter

    Returns:
        bool: True nếu tích hợp thành công, False nếu không
    """
    if not RESULT_EXPORTER_AVAILABLE:
        logger.warning("ResultExporter không khả dụng")
        return False

    try:
        # Khởi tạo ResultExporter
        result_exporter = ResultExporter(
            output_dir=config.get("output_dir"),
            default_format=config.get("default_format", "json"),
            pretty_print=config.get("pretty_print", True),
            include_metadata=config.get("include_metadata", True),
            include_stats=config.get("include_stats", True),
            include_headers=config.get("include_headers", True),
            include_timestamp=config.get("include_timestamp", True),
            timestamp_format=config.get("timestamp_format", "%Y-%m-%d %H:%M:%S"),
            file_prefix=config.get("file_prefix", "crawl_result"),
            file_suffix=config.get("file_suffix", ""),
            overwrite=config.get("overwrite", False),
            encoding=config.get("encoding", "utf-8"),
            verbose=config.get("verbose", False)
        )

        # Gán ResultExporter vào AdaptiveCrawler
        crawler.result_exporter = result_exporter

        # Gán phương thức export_results
        crawler.export_results = lambda results, **kwargs: export_results(crawler, results, **kwargs)

        logger.info("ResultExporter đã được tích hợp thành công")
        return True
    except Exception as e:
        logger.error(f"Lỗi khi tích hợp ResultExporter: {str(e)}")
        return False

def integrate_screenshot_manager(crawler, config: Dict[str, Any]) -> bool:
    """
    Tích hợp ScreenshotManager vào AdaptiveCrawler.

    Args:
        crawler: AdaptiveCrawler instance
        config: Cấu hình cho ScreenshotManager

    Returns:
        bool: True nếu tích hợp thành công, False nếu không
    """
    if not SCREENSHOT_MANAGER_AVAILABLE:
        logger.warning("ScreenshotManager không khả dụng")
        return False

    try:
        # Khởi tạo ScreenshotManager
        screenshot_manager = ScreenshotManager(
            output_dir=config.get("output_dir"),
            format=config.get("format", "png"),
            quality=config.get("quality", 80),
            full_page=config.get("full_page", True),
            width=config.get("width", 1280),
            height=config.get("height", 800),
            device_scale_factor=config.get("device_scale_factor", 1.0),
            wait_before_screenshot=config.get("wait_before_screenshot", 1.0),
            wait_for_network_idle=config.get("wait_for_network_idle", True),
            wait_for_selector=config.get("wait_for_selector"),
            wait_timeout=config.get("wait_timeout", 30.0),
            file_prefix=config.get("file_prefix", "screenshot"),
            file_suffix=config.get("file_suffix", ""),
            use_url_as_filename=config.get("use_url_as_filename", True),
            overwrite=config.get("overwrite", False),
            max_filename_length=config.get("max_filename_length", 100),
            verbose=config.get("verbose", False)
        )

        # Gán ScreenshotManager vào AdaptiveCrawler
        crawler.screenshot_manager = screenshot_manager

        # Gán phương thức take_screenshot
        crawler.take_screenshot = lambda page, url, **kwargs: take_screenshot(crawler, page, url, **kwargs)

        logger.info("ScreenshotManager đã được tích hợp thành công")
        return True
    except Exception as e:
        logger.error(f"Lỗi khi tích hợp ScreenshotManager: {str(e)}")
        return False

# Các hàm wrapper

def crawl_distributed(crawler, urls: List[str], **kwargs) -> Dict[str, Any]:
    """
    Crawl phân tán.

    Args:
        crawler: AdaptiveCrawler instance
        urls: Danh sách URL cần crawl
        **kwargs: Các tham số bổ sung

    Returns:
        Dict[str, Any]: Kết quả crawl
    """
    if not hasattr(crawler, "distributed_crawler") or crawler.distributed_crawler is None:
        logger.warning("DistributedCrawler không khả dụng")
        return {"success": False, "error": "DistributedCrawler không khả dụng"}

    # Thêm URLs vào hàng đợi
    crawler.distributed_crawler.add_urls(urls)

    # Bắt đầu crawl
    if not crawler.distributed_crawler.is_running:
        crawler.distributed_crawler.start()

    # Lấy kết quả
    results = crawler.distributed_crawler.get_results(wait=True, timeout=kwargs.get("timeout", 60.0))

    return {"success": True, "results": results, "count": len(results)}

def save_checkpoint(crawler, state: Dict[str, Any], **kwargs) -> bool:
    """
    Lưu checkpoint.

    Args:
        crawler: AdaptiveCrawler instance
        state: Trạng thái cần lưu
        **kwargs: Các tham số bổ sung

    Returns:
        bool: True nếu lưu thành công, False nếu không
    """
    if not hasattr(crawler, "checkpoint_manager") or crawler.checkpoint_manager is None:
        logger.warning("CheckpointManager không khả dụng")
        return False

    return crawler.checkpoint_manager.save_checkpoint(state)

def load_checkpoint(crawler, **kwargs) -> Optional[Dict[str, Any]]:
    """
    Tải checkpoint.

    Args:
        crawler: AdaptiveCrawler instance
        **kwargs: Các tham số bổ sung

    Returns:
        Optional[Dict[str, Any]]: Trạng thái đã lưu hoặc None nếu không có
    """
    if not hasattr(crawler, "checkpoint_manager") or crawler.checkpoint_manager is None:
        logger.warning("CheckpointManager không khả dụng")
        return None

    return crawler.checkpoint_manager.load_checkpoint()

def get_proxy(crawler, **kwargs) -> Optional[str]:
    """
    Lấy proxy.

    Args:
        crawler: AdaptiveCrawler instance
        **kwargs: Các tham số bổ sung

    Returns:
        Optional[str]: Proxy hoặc None nếu không có
    """
    if not hasattr(crawler, "proxy_rotation_manager") or crawler.proxy_rotation_manager is None:
        logger.warning("ProxyRotationManager không khả dụng")
        return None

    return crawler.proxy_rotation_manager.get_proxy(**kwargs)

def get_user_agent(crawler, **kwargs) -> str:
    """
    Lấy user agent.

    Args:
        crawler: AdaptiveCrawler instance
        **kwargs: Các tham số bổ sung

    Returns:
        str: User agent
    """
    if not hasattr(crawler, "user_agent_rotation_manager") or crawler.user_agent_rotation_manager is None:
        logger.warning("UserAgentRotationManager không khả dụng")
        return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"

    return crawler.user_agent_rotation_manager.get_user_agent(**kwargs)

def export_results(crawler, results: Union[Dict[str, Any], List[Dict[str, Any]]], **kwargs) -> str:
    """
    Xuất kết quả.

    Args:
        crawler: AdaptiveCrawler instance
        results: Kết quả cần xuất
        **kwargs: Các tham số bổ sung

    Returns:
        str: Đường dẫn đến file đã xuất
    """
    if not hasattr(crawler, "result_exporter") or crawler.result_exporter is None:
        logger.warning("ResultExporter không khả dụng")
        return ""

    return crawler.result_exporter.export(results, **kwargs)

def take_screenshot(crawler, page: Any, url: str, **kwargs) -> str:
    """
    Chụp ảnh màn hình.

    Args:
        crawler: AdaptiveCrawler instance
        page: Đối tượng Playwright Page
        url: URL của trang web
        **kwargs: Các tham số bổ sung

    Returns:
        str: Đường dẫn đến file ảnh chụp màn hình
    """
    if not hasattr(crawler, "screenshot_manager") or crawler.screenshot_manager is None:
        logger.warning("ScreenshotManager không khả dụng")
        return ""

    return crawler.screenshot_manager.take_screenshot(page, url, **kwargs)
