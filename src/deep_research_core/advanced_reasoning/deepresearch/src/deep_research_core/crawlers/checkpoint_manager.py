#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module cung cấp lớp CheckpointManager, một công cụ để lưu và khôi phục trạng thái crawl.

Module này cung cấp lớp CheckpointManager, một công cụ để lưu trạng thái crawl
và có thể tiếp tục từ điểm dừng, giúp tăng tính ổn định và hiệu quả của quá trình crawl.
"""

import os
import time
import json
import logging
import threading
from typing import Dict, List, Any, Optional, Tuple, Set, Union, Callable

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CheckpointManager:
    """
    Lớp cung cấp các phương thức để lưu và khôi phục trạng thái crawl.

    Tính năng:
    - <PERSON><PERSON><PERSON> trạng thái crawl định kỳ
    - Khôi phục trạng thái crawl từ checkpoint
    - Hỗ trợ nhiều định dạng lưu trữ (JSON, SQLite, Redis)
    - Hỗ trợ nén dữ liệu để tiết kiệm không gian
    - Hỗ trợ lưu trữ phân tán
    """

    def __init__(
        self,
        checkpoint_path: Optional[str] = None,
        checkpoint_interval: int = 100,
        checkpoint_format: str = "json",
        compress: bool = False,
        max_checkpoints: int = 5,
        auto_checkpoint: bool = True,
        verbose: bool = False,
    ):
        """
        Khởi tạo CheckpointManager.

        Args:
            checkpoint_path: Đường dẫn để lưu checkpoint
            checkpoint_interval: Số URL đã xử lý trước khi tạo checkpoint
            checkpoint_format: Định dạng lưu trữ ("json", "sqlite", "redis")
            compress: Có nén dữ liệu hay không
            max_checkpoints: Số lượng checkpoint tối đa để giữ lại
            auto_checkpoint: Có tự động tạo checkpoint hay không
            verbose: Ghi log chi tiết hay không
        """
        # Cấu hình
        self.checkpoint_path = checkpoint_path or os.path.join(os.getcwd(), "crawler_checkpoint")
        self.checkpoint_interval = checkpoint_interval
        self.checkpoint_format = checkpoint_format
        self.compress = compress
        self.max_checkpoints = max_checkpoints
        self.auto_checkpoint = auto_checkpoint
        self.verbose = verbose

        # Khởi tạo các thuộc tính
        self.checkpoint_count = 0
        self.url_count = 0
        self.last_checkpoint_time = 0
        self.checkpoint_thread = None
        self.is_running = False
        self.checkpoint_lock = threading.Lock()
        self.checkpoint_callbacks = []

        # Khởi tạo các thành phần
        self._initialize_components()

        logger.info(f"CheckpointManager được khởi tạo với checkpoint_path={self.checkpoint_path}")

    def _initialize_components(self):
        """Khởi tạo các thành phần."""
        # Tạo thư mục checkpoint nếu chưa tồn tại
        if self.checkpoint_format == "json":
            os.makedirs(os.path.dirname(self.checkpoint_path), exist_ok=True)

    def start(self):
        """Bắt đầu CheckpointManager."""
        if self.is_running:
            logger.warning("CheckpointManager đã đang chạy")
            return

        self.is_running = True

        # Khởi động thread tự động tạo checkpoint nếu cần
        if self.auto_checkpoint:
            self._start_checkpoint_thread()

        logger.info("CheckpointManager đã bắt đầu")

    def stop(self):
        """Dừng CheckpointManager."""
        if not self.is_running:
            logger.warning("CheckpointManager không đang chạy")
            return

        self.is_running = False

        # Dừng thread tự động tạo checkpoint nếu có
        if self.checkpoint_thread:
            # TODO: Implement checkpoint thread stop
            pass

        logger.info("CheckpointManager đã dừng")

    def save_checkpoint(self, state: Dict[str, Any]) -> bool:
        """
        Lưu checkpoint.

        Args:
            state: Trạng thái cần lưu

        Returns:
            bool: True nếu lưu thành công, False nếu không
        """
        with self.checkpoint_lock:
            try:
                # Thêm metadata
                state["checkpoint_time"] = time.time()
                state["checkpoint_id"] = self.checkpoint_count

                # Lưu checkpoint theo định dạng
                if self.checkpoint_format == "json":
                    return self._save_json_checkpoint(state)
                elif self.checkpoint_format == "sqlite":
                    return self._save_sqlite_checkpoint(state)
                elif self.checkpoint_format == "redis":
                    return self._save_redis_checkpoint(state)
                else:
                    logger.error(f"Định dạng checkpoint không hỗ trợ: {self.checkpoint_format}")
                    return False
            except Exception as e:
                logger.error(f"Lỗi khi lưu checkpoint: {str(e)}")
                return False

    def load_checkpoint(self) -> Optional[Dict[str, Any]]:
        """
        Tải checkpoint.

        Returns:
            Optional[Dict[str, Any]]: Trạng thái đã lưu hoặc None nếu không có
        """
        with self.checkpoint_lock:
            try:
                # Tải checkpoint theo định dạng
                if self.checkpoint_format == "json":
                    return self._load_json_checkpoint()
                elif self.checkpoint_format == "sqlite":
                    return self._load_sqlite_checkpoint()
                elif self.checkpoint_format == "redis":
                    return self._load_redis_checkpoint()
                else:
                    logger.error(f"Định dạng checkpoint không hỗ trợ: {self.checkpoint_format}")
                    return None
            except Exception as e:
                logger.error(f"Lỗi khi tải checkpoint: {str(e)}")
                return None

    def add_checkpoint_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """
        Thêm callback được gọi sau khi tạo checkpoint.

        Args:
            callback: Hàm callback nhận trạng thái đã lưu
        """
        self.checkpoint_callbacks.append(callback)

    def _start_checkpoint_thread(self):
        """Khởi động thread để tạo checkpoint định kỳ."""
        # TODO: Implement checkpoint thread start
        pass

    def _save_json_checkpoint(self, state: Dict[str, Any]) -> bool:
        """
        Lưu checkpoint dưới dạng JSON.

        Args:
            state: Trạng thái cần lưu

        Returns:
            bool: True nếu lưu thành công, False nếu không
        """
        checkpoint_file = f"{self.checkpoint_path}_{self.checkpoint_count}.json"

        try:
            with open(checkpoint_file, "w") as f:
                json.dump(state, f)

            # Cập nhật thông tin
            self.checkpoint_count += 1
            self.last_checkpoint_time = time.time()

            # Xóa checkpoint cũ nếu vượt quá số lượng tối đa
            self._cleanup_old_checkpoints()

            logger.info(f"Đã lưu checkpoint vào {checkpoint_file}")
            return True
        except Exception as e:
            logger.error(f"Lỗi khi lưu checkpoint JSON: {str(e)}")
            return False

    def _load_json_checkpoint(self) -> Optional[Dict[str, Any]]:
        """
        Tải checkpoint từ JSON.

        Returns:
            Optional[Dict[str, Any]]: Trạng thái đã lưu hoặc None nếu không có
        """
        # Tìm checkpoint mới nhất
        checkpoint_files = [f for f in os.listdir(os.path.dirname(self.checkpoint_path))
                           if f.startswith(os.path.basename(self.checkpoint_path)) and f.endswith(".json")]

        if not checkpoint_files:
            logger.info("Không tìm thấy checkpoint JSON")
            return None

        # Sắp xếp theo thời gian tạo
        checkpoint_files.sort(key=lambda f: os.path.getmtime(os.path.join(os.path.dirname(self.checkpoint_path), f)), reverse=True)
        latest_checkpoint = os.path.join(os.path.dirname(self.checkpoint_path), checkpoint_files[0])

        try:
            with open(latest_checkpoint, "r") as f:
                state = json.load(f)

            logger.info(f"Đã tải checkpoint từ {latest_checkpoint}")
            return state
        except Exception as e:
            logger.error(f"Lỗi khi tải checkpoint JSON: {str(e)}")
            return None

    def _save_sqlite_checkpoint(self, state: Dict[str, Any]) -> bool:
        """
        Lưu checkpoint vào SQLite.

        Args:
            state: Trạng thái cần lưu

        Returns:
            bool: True nếu lưu thành công, False nếu không
        """
        # TODO: Implement SQLite checkpoint save
        return False

    def _load_sqlite_checkpoint(self) -> Optional[Dict[str, Any]]:
        """
        Tải checkpoint từ SQLite.

        Returns:
            Optional[Dict[str, Any]]: Trạng thái đã lưu hoặc None nếu không có
        """
        # TODO: Implement SQLite checkpoint load
        return None

    def _save_redis_checkpoint(self, state: Dict[str, Any]) -> bool:
        """
        Lưu checkpoint vào Redis.

        Args:
            state: Trạng thái cần lưu

        Returns:
            bool: True nếu lưu thành công, False nếu không
        """
        # TODO: Implement Redis checkpoint save
        return False

    def _load_redis_checkpoint(self) -> Optional[Dict[str, Any]]:
        """
        Tải checkpoint từ Redis.

        Returns:
            Optional[Dict[str, Any]]: Trạng thái đã lưu hoặc None nếu không có
        """
        # TODO: Implement Redis checkpoint load
        return None

    def _cleanup_old_checkpoints(self):
        """Xóa checkpoint cũ nếu vượt quá số lượng tối đa."""
        if self.checkpoint_format != "json":
            return

        checkpoint_files = [f for f in os.listdir(os.path.dirname(self.checkpoint_path))
                           if f.startswith(os.path.basename(self.checkpoint_path)) and f.endswith(".json")]

        if len(checkpoint_files) <= self.max_checkpoints:
            return

        # Sắp xếp theo thời gian tạo
        checkpoint_files.sort(key=lambda f: os.path.getmtime(os.path.join(os.path.dirname(self.checkpoint_path), f)))

        # Xóa checkpoint cũ
        for i in range(len(checkpoint_files) - self.max_checkpoints):
            os.remove(os.path.join(os.path.dirname(self.checkpoint_path), checkpoint_files[i]))
            logger.info(f"Đã xóa checkpoint cũ: {checkpoint_files[i]}")
