"""
Vietnamese prompt optimizer for Deep Research Core.

This module provides functionality to optimize prompts for Vietnamese language
based on domain and query characteristics.
"""

import re
from typing import Dict, Any, List, Optional, Set

from ..utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

class VietnamesePromptOptimizer:
    """
    Optimize prompts for Vietnamese language.
    
    This class provides methods to optimize prompts for Vietnamese language
    based on domain detection and query characteristics.
    """
    
    # Singleton instance
    _instance = None
    
    def __new__(cls):
        """Implement singleton pattern."""
        if cls._instance is None:
            cls._instance = super(VietnamesePromptOptimizer, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """Initialize the Vietnamese prompt optimizer."""
        # Skip initialization if already initialized
        if self._initialized:
            return
        
        # Domain-specific keywords
        self.domain_keywords = {
            "y_te": [
                "bệnh", "triệu chứng", "điều trị", "thuốc", "b<PERSON><PERSON> s<PERSON>", "bệnh viện",
                "sức khỏe", "y tế", "phẫu thuật", "chẩn đo<PERSON>", "d<PERSON><PERSON><PERSON> ph<PERSON>m", "ti<PERSON>m",
                "v<PERSON>c-xin", "kh<PERSON>g sinh", "virus", "vi khu<PERSON>n", "tế b<PERSON>o", "gen",
                "dị <PERSON>ng", "mi<PERSON>n d<PERSON>ch", "tim m<PERSON>ch", "hô hấp", "tiêu hóa", "thần kinh"
            ],
            "luat": [
                "ph<PERSON>p luật", "điều khoản", "hợp đồng", "quyền", "nghĩa vụ", "tòa án",
                "luật", "luật sư", "thẩm phán", "bị cáo", "nguyên đơn", "bị đơn",
                "khiếu nại", "tố tụng", "bồi thường", "vi phạm", "trách nhiệm",
                "hiến pháp", "bộ luật", "nghị định", "thông tư", "quyết định"
            ],
            "cong_nghe": [
                "phần mềm", "phần cứng", "mạng", "dữ liệu", "thuật toán", "mã nguồn",
                "công nghệ", "máy tính", "internet", "website", "ứng dụng", "AI",
                "trí tuệ nhân tạo", "học máy", "blockchain", "cloud", "đám mây",
                "bảo mật", "hacker", "mã hóa", "server", "database", "cơ sở dữ liệu"
            ],
            "giao_duc": [
                "học sinh", "giáo viên", "trường học", "bài giảng", "kiến thức", "kỹ năng",
                "giáo dục", "đại học", "cao đẳng", "trung học", "tiểu học", "mầm non",
                "học tập", "giảng dạy", "đào tạo", "thi cử", "bằng cấp", "chứng chỉ",
                "môn học", "chương trình", "giáo trình", "học phí", "học bổng"
            ],
            "kinh_te": [
                "kinh tế", "tài chính", "ngân hàng", "đầu tư", "chứng khoán", "lãi suất",
                "lạm phát", "GDP", "thuế", "doanh nghiệp", "công ty", "thị trường",
                "xuất khẩu", "nhập khẩu", "thương mại", "tiền tệ", "ngoại hối",
                "cổ phiếu", "trái phiếu", "vốn", "lợi nhuận", "chi phí", "doanh thu"
            ],
            "nong_nghiep": [
                "nông nghiệp", "trồng trọt", "chăn nuôi", "thủy sản", "lâm nghiệp",
                "cây trồng", "vật nuôi", "phân bón", "thuốc trừ sâu", "giống", "đất đai",
                "tưới tiêu", "thu hoạch", "nông dân", "hợp tác xã", "trang trại",
                "sản xuất", "chế biến", "bảo quản", "nông sản", "thực phẩm"
            ],
            "moi_truong": [
                "môi trường", "ô nhiễm", "biến đổi khí hậu", "sinh thái", "tài nguyên",
                "năng lượng", "tái chế", "rác thải", "bảo tồn", "đa dạng sinh học",
                "phát triển bền vững", "carbon", "khí thải", "nhiệt độ", "nước biển dâng",
                "bão", "lũ lụt", "hạn hán", "thiên tai", "năng lượng tái tạo"
            ]
        }
        
        # Domain-specific prompts
        self.domain_prompts = {
            "y_te": """Hãy suy luận từng bước về vấn đề y tế này, sử dụng thuật ngữ y khoa chính xác và dựa trên kiến thức y học hiện đại. Đảm bảo:
1. Phân tích các triệu chứng hoặc hiện tượng y tế một cách khoa học
2. Dựa vào bằng chứng y học và nghiên cứu có căn cứ
3. Xem xét các yếu tố liên quan đến bệnh lý, sinh lý, và dược lý
4. Đưa ra kết luận cẩn thận, có tính đến các trường hợp ngoại lệ
5. Sử dụng thuật ngữ y khoa chính xác nhưng giải thích rõ ràng""",
            
            "luat": """Hãy phân tích vấn đề pháp lý này theo từng bước, dựa trên các quy định pháp luật và án lệ liên quan. Đảm bảo:
1. Xác định rõ các vấn đề pháp lý cần giải quyết
2. Trích dẫn các điều luật, nghị định hoặc văn bản pháp lý liên quan
3. Phân tích cách áp dụng luật vào tình huống cụ thể
4. Xem xét các tiền lệ pháp lý tương tự nếu có
5. Đưa ra kết luận pháp lý có căn cứ và cân nhắc các khía cạnh khác nhau""",
            
            "cong_nghe": """Hãy giải thích vấn đề công nghệ này theo từng bước, sử dụng thuật ngữ kỹ thuật chính xác. Đảm bảo:
1. Phân tích các khía cạnh kỹ thuật của vấn đề
2. Giải thích các nguyên lý hoặc thuật toán liên quan
3. Xem xét các ưu điểm, nhược điểm và hạn chế của giải pháp
4. Đề cập đến các công nghệ hoặc phương pháp thay thế nếu phù hợp
5. Sử dụng thuật ngữ kỹ thuật chính xác nhưng giải thích rõ ràng""",
            
            "giao_duc": """Hãy phân tích vấn đề giáo dục này theo từng bước, dựa trên các nguyên lý sư phạm và tâm lý học giáo dục. Đảm bảo:
1. Xem xét các lý thuyết giáo dục và phương pháp sư phạm liên quan
2. Phân tích tác động đến người học và quá trình học tập
3. Đánh giá hiệu quả của các phương pháp giáo dục
4. Xem xét các yếu tố văn hóa, xã hội ảnh hưởng đến giáo dục
5. Đề xuất các phương pháp tiếp cận phù hợp với đối tượng học sinh""",
            
            "kinh_te": """Hãy phân tích vấn đề kinh tế này theo từng bước, dựa trên các nguyên lý kinh tế học và dữ liệu thực tế. Đảm bảo:
1. Xác định các khái niệm và nguyên lý kinh tế liên quan
2. Phân tích các yếu tố ảnh hưởng đến vấn đề kinh tế
3. Xem xét các mối quan hệ cung-cầu và các biến số kinh tế vĩ mô
4. Đánh giá tác động của các chính sách kinh tế
5. Dự đoán các xu hướng hoặc kết quả dựa trên phân tích""",
            
            "nong_nghiep": """Hãy phân tích vấn đề nông nghiệp này theo từng bước, dựa trên kiến thức nông học và thực tiễn canh tác. Đảm bảo:
1. Xem xét các yếu tố sinh học, hóa học và môi trường
2. Phân tích các kỹ thuật canh tác và chăn nuôi liên quan
3. Đánh giá tính bền vững và hiệu quả kinh tế
4. Xem xét ảnh hưởng của thời tiết, khí hậu và điều kiện địa phương
5. Đề xuất các phương pháp cải thiện năng suất và chất lượng""",
            
            "moi_truong": """Hãy phân tích vấn đề môi trường này theo từng bước, dựa trên khoa học môi trường và sinh thái học. Đảm bảo:
1. Xác định các tác động môi trường và nguyên nhân
2. Phân tích mối quan hệ giữa các yếu tố trong hệ sinh thái
3. Đánh giá tính bền vững và tác động dài hạn
4. Xem xét các giải pháp bảo vệ môi trường và phát triển bền vững
5. Cân nhắc các khía cạnh kinh tế, xã hội và chính sách"""
        }
        
        # Question type patterns
        self.question_types = {
            "dinh_nghia": [
                r"(.*) là gì\?",
                r"định nghĩa (.*)\?",
                r"khái niệm (.*) là gì\?",
                r"giải thích (.*) là gì\?"
            ],
            "so_sanh": [
                r"so sánh (.*) và (.*)\?",
                r"sự khác nhau giữa (.*) và (.*)\?",
                r"(.*) khác với (.*) như thế nào\?",
                r"điểm giống và khác nhau giữa (.*) và (.*)\?"
            ],
            "nguyen_nhan": [
                r"tại sao (.*)\?",
                r"vì sao (.*)\?",
                r"nguyên nhân (.*)\?",
                r"lý do (.*)\?"
            ],
            "quy_trinh": [
                r"làm thế nào để (.*)\?",
                r"cách (.*)\?",
                r"quy trình (.*)\?",
                r"các bước (.*)\?"
            ],
            "phan_tich": [
                r"phân tích (.*)\?",
                r"đánh giá (.*)\?",
                r"nhận xét về (.*)\?",
                r"bình luận về (.*)\?"
            ],
            "ung_dung": [
                r"ứng dụng của (.*)\?",
                r"(.*) được ứng dụng như thế nào\?",
                r"ứng dụng (.*) trong (.*)\?",
                r"(.*) có thể được sử dụng để (.*) không\?"
            ]
        }
        
        # Question type prompts
        self.question_type_prompts = {
            "dinh_nghia": """Hãy suy luận từng bước để đưa ra định nghĩa chính xác và đầy đủ. Đảm bảo:
1. Xác định rõ phạm vi và bối cảnh của khái niệm
2. Phân tích các thành phần hoặc đặc điểm chính
3. So sánh với các khái niệm liên quan để làm rõ sự khác biệt
4. Đưa ra định nghĩa chính thức và dễ hiểu
5. Cung cấp ví dụ minh họa nếu cần thiết""",
            
            "so_sanh": """Hãy suy luận từng bước để so sánh một cách có hệ thống. Đảm bảo:
1. Xác định rõ các đối tượng cần so sánh
2. Thiết lập các tiêu chí so sánh cụ thể và phù hợp
3. Phân tích điểm giống nhau một cách có hệ thống
4. Phân tích điểm khác nhau một cách có hệ thống
5. Đưa ra kết luận tổng hợp về sự so sánh""",
            
            "nguyen_nhan": """Hãy suy luận từng bước để phân tích nguyên nhân một cách logic. Đảm bảo:
1. Xác định rõ hiện tượng hoặc vấn đề cần giải thích
2. Phân tích các nguyên nhân trực tiếp và gián tiếp
3. Xem xét mối quan hệ nhân quả giữa các yếu tố
4. Đánh giá mức độ đóng góp của từng nguyên nhân
5. Tổng hợp thành giải thích toàn diện và logic""",
            
            "quy_trinh": """Hãy suy luận từng bước để mô tả quy trình một cách rõ ràng. Đảm bảo:
1. Xác định rõ mục tiêu và kết quả mong muốn của quy trình
2. Liệt kê các bước theo thứ tự logic và thời gian
3. Giải thích chi tiết cách thực hiện từng bước
4. Nêu rõ các điều kiện, yêu cầu hoặc lưu ý cho mỗi bước
5. Tổng kết quy trình và đánh giá hiệu quả""",
            
            "phan_tich": """Hãy suy luận từng bước để phân tích vấn đề một cách toàn diện. Đảm bảo:
1. Xác định rõ vấn đề và bối cảnh cần phân tích
2. Chia nhỏ vấn đề thành các thành phần hoặc khía cạnh
3. Phân tích sâu từng thành phần với bằng chứng và lý luận
4. Xem xét mối quan hệ giữa các thành phần
5. Tổng hợp thành đánh giá toàn diện và cân bằng""",
            
            "ung_dung": """Hãy suy luận từng bước để phân tích ứng dụng một cách thực tế. Đảm bảo:
1. Xác định rõ đối tượng và lĩnh vực ứng dụng
2. Phân tích các nguyên lý hoặc cơ chế cho phép ứng dụng
3. Mô tả các ứng dụng cụ thể với ví dụ thực tế
4. Đánh giá hiệu quả, ưu điểm và hạn chế của ứng dụng
5. Xem xét tiềm năng phát triển trong tương lai"""
        }
        
        # Mark as initialized
        self._initialized = True
        logger.info("Initialized VietnamesePromptOptimizer")
    
    @classmethod
    def get_instance(cls) -> 'VietnamesePromptOptimizer':
        """
        Get singleton instance of VietnamesePromptOptimizer.
        
        Returns:
            Instance of VietnamesePromptOptimizer
        """
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def detect_domain(self, query: str) -> str:
        """
        Detect the domain of a query.
        
        Args:
            query: Query to analyze
            
        Returns:
            Detected domain
        """
        # Convert to lowercase for case-insensitive matching
        query_lower = query.lower()
        
        # Calculate domain scores
        domain_scores = {}
        for domain, keywords in self.domain_keywords.items():
            # Count keyword occurrences
            score = sum(1 for keyword in keywords if keyword in query_lower)
            
            # Normalize by number of keywords
            if keywords:
                score = score / len(keywords) * 10  # Scale to 0-10
            
            domain_scores[domain] = score
        
        # Get domain with highest score
        if domain_scores:
            max_score = max(domain_scores.values())
            if max_score > 0:
                max_domain = max(domain_scores.items(), key=lambda x: x[1])[0]
                logger.debug(f"Detected domain '{max_domain}' for query: {query[:50]}...")
                return max_domain
        
        # Default domain
        logger.debug(f"No specific domain detected for query: {query[:50]}...")
        return "general"
    
    def detect_question_type(self, query: str) -> str:
        """
        Detect the type of question.
        
        Args:
            query: Query to analyze
            
        Returns:
            Detected question type
        """
        # Convert to lowercase for case-insensitive matching
        query_lower = query.lower()
        
        # Check each question type pattern
        for q_type, patterns in self.question_types.items():
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    logger.debug(f"Detected question type '{q_type}' for query: {query[:50]}...")
                    return q_type
        
        # Default question type
        logger.debug(f"No specific question type detected for query: {query[:50]}...")
        return "general"
    
    def optimize_prompt(
        self,
        query: str,
        base_prompt: str,
        use_domain_prompts: bool = True,
        use_question_type_prompts: bool = True
    ) -> str:
        """
        Optimize a prompt for Vietnamese language.
        
        Args:
            query: Query to optimize for
            base_prompt: Base prompt to optimize
            use_domain_prompts: Whether to use domain-specific prompts
            use_question_type_prompts: Whether to use question type prompts
            
        Returns:
            Optimized prompt
        """
        optimized_prompt = base_prompt
        
        # Apply domain-specific optimization
        if use_domain_prompts:
            domain = self.detect_domain(query)
            if domain in self.domain_prompts:
                optimized_prompt = self.domain_prompts[domain]
                logger.info(f"Applied domain-specific prompt for '{domain}'")
        
        # Apply question type optimization
        if use_question_type_prompts:
            question_type = self.detect_question_type(query)
            if question_type in self.question_type_prompts:
                # If already using domain prompt, combine them
                if optimized_prompt != base_prompt:
                    optimized_prompt = f"{optimized_prompt}\n\nNgoài ra, vì đây là câu hỏi dạng {question_type}:\n{self.question_type_prompts[question_type]}"
                else:
                    optimized_prompt = self.question_type_prompts[question_type]
                logger.info(f"Applied question type prompt for '{question_type}'")
        
        return optimized_prompt
