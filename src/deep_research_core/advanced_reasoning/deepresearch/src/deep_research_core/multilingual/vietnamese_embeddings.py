"""
Vietnamese-specific embeddings for Deep Research Core.

This module provides Vietnamese-specific embedding models and utilities
for improved multilingual support in Deep Research Core.
"""

import os
import torch
import numpy as np
from typing import Dict, Any, List, Optional, Union, Tuple

from sentence_transformers import SentenceTransformer
from transformers import AutoTokenizer, AutoModel

from ..utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

class VietnameseEmbeddings:
    """
    Vietnamese-specific embeddings for Deep Research Core.

    This class provides methods to generate embeddings for Vietnamese text
    using specialized models trained on Vietnamese data.
    """

    AVAILABLE_MODELS = {
        # Mô hình cơ bản
        "phobert": "vinai/phobert-base",
        "phobert-large": "vinai/phobert-large",
        "viebert": "FPTAI/viebert-base-cased",
        "xlm-roberta-vi": "xlm-roberta-base",
        "multilingual-e5": "intfloat/multilingual-e5-base",
        "multilingual-e5-large": "intfloat/multilingual-e5-large",
        "vietnamese-sbert": "keepitreal/vietnamese-sbert",

        # <PERSON>ô hình mới
        "bkai-foundation-vi": "bkai-foundation-models/vietnamese-bi-encoder",
        "envibert": "nguyenvulebinh/envibert",
        "bartpho": "vinai/bartpho-syllable",
        "velectra": "vinai/velectra-base-discriminator-cased",
        "vibert4news": "FPTAI/vibert4news-base-cased",
        "vi-mrc": "nguyenvulebinh/vi-mrc-base",
        "vi-qa": "nguyenvulebinh/vi-qa-base",

        # Mô hình mới bổ sung
        "vit5": "VietAI/vit5-base",
        "vinallm": "vinai/vinallm-7b-chat",
        "vietnamese-llama": "bkai-foundation-models/vietnamese-llama2",
        "vietnamese-gpt": "NlpHUST/gpt-neo-vi-small",
        "vietnamese-roberta": "wonrax/phobert-base-vietnamese-sentiment"
    }

    # Model categories
    MODEL_CATEGORIES = {
        "general": ["phobert", "phobert-large", "viebert", "xlm-roberta-vi", "multilingual-e5", "multilingual-e5-large", "vietnamese-sbert"],
        "specialized": ["bkai-foundation-vi", "envibert", "bartpho", "velectra", "vibert4news"],
        "task_specific": ["vi-mrc", "vi-qa", "vietnamese-roberta"],
        "generative": ["vit5", "vinallm", "vietnamese-llama", "vietnamese-gpt"]
    }

    def __init__(
        self,
        model_name: str = "phobert",
        device: Optional[str] = None,
        cache_dir: Optional[str] = None
    ):
        """
        Initialize Vietnamese embeddings.

        Args:
            model_name: Name of the embedding model to use
            device: Device to use for inference ("cpu", "cuda", "mps")
            cache_dir: Directory to cache models
        """
        self.model_name = model_name
        self.cache_dir = cache_dir

        # Determine device
        if device is None:
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
            if hasattr(torch, "has_mps") and torch.has_mps:
                self.device = "mps"  # Apple Silicon GPU
        else:
            self.device = device

        # Get model path
        self.model_path = self.AVAILABLE_MODELS.get(model_name, model_name)

        # Initialize model
        self._initialize_model()

    def _initialize_model(self) -> None:
        """
        Initialize the embedding model.
        """
        try:
            # Nhóm 1: Các mô hình sử dụng AutoTokenizer và AutoModel trực tiếp
            if self.model_name in [
                "phobert", "phobert-large", "viebert", "xlm-roberta-vi",
                "envibert", "bartpho", "velectra", "vibert4news"
            ]:
                # Initialize tokenizer and model from Hugging Face
                self.tokenizer = AutoTokenizer.from_pretrained(
                    self.model_path,
                    cache_dir=self.cache_dir
                )
                self.model = AutoModel.from_pretrained(
                    self.model_path,
                    cache_dir=self.cache_dir
                ).to(self.device)

                # Set to evaluation mode
                self.model.eval()

                # Use sentence-transformers style
                self.use_sentence_transformers = False

            # Nhóm 2: Các mô hình chuyên biệt cho nhiệm vụ MRC và QA
            elif self.model_name in ["vi-mrc", "vi-qa"]:
                # Initialize tokenizer and model from Hugging Face
                self.tokenizer = AutoTokenizer.from_pretrained(
                    self.model_path,
                    cache_dir=self.cache_dir
                )
                self.model = AutoModel.from_pretrained(
                    self.model_path,
                    cache_dir=self.cache_dir
                ).to(self.device)

                # Set to evaluation mode
                self.model.eval()

                # Use sentence-transformers style with special handling
                self.use_sentence_transformers = False
                self.is_qa_model = True

            # Nhóm 3: Các mô hình sử dụng SentenceTransformer
            else:
                # Use sentence-transformers
                self.model = SentenceTransformer(
                    self.model_path,
                    cache_folder=self.cache_dir
                ).to(self.device)

                self.use_sentence_transformers = True
                self.is_qa_model = False

            logger.info(f"Initialized Vietnamese embedding model: {self.model_name}")

        except Exception as e:
            logger.error(f"Error initializing Vietnamese embedding model: {str(e)}")
            raise

    def _mean_pooling(
        self,
        model_output: torch.Tensor,
        attention_mask: torch.Tensor
    ) -> torch.Tensor:
        """
        Perform mean pooling on model output.

        Args:
            model_output: Output from the model
            attention_mask: Attention mask

        Returns:
            Pooled embeddings
        """
        # First element of model_output contains all token embeddings
        token_embeddings = model_output[0]

        # Expand attention mask to match token embeddings
        input_mask_expanded = attention_mask.unsqueeze(-1).expand(token_embeddings.size()).float()

        # Sum token embeddings and divide by the expanded mask sum
        sum_embeddings = torch.sum(token_embeddings * input_mask_expanded, 1)
        sum_mask = torch.clamp(input_mask_expanded.sum(1), min=1e-9)

        # Return mean pooled embeddings
        return sum_embeddings / sum_mask

    def get_embeddings(self, texts: List[str]) -> np.ndarray:
        """
        Get embeddings for Vietnamese texts.

        Args:
            texts: List of Vietnamese texts

        Returns:
            Numpy array of embeddings
        """
        if not texts:
            return np.array([])

        try:
            if self.use_sentence_transformers:
                # Use sentence-transformers directly
                embeddings = self.model.encode(
                    texts,
                    convert_to_numpy=True,
                    show_progress_bar=False
                )
            else:
                # Process with Hugging Face models
                with torch.no_grad():
                    # Tokenize texts
                    encoded_input = self.tokenizer(
                        texts,
                        padding=True,
                        truncation=True,
                        max_length=512,
                        return_tensors="pt"
                    ).to(self.device)

                    # Get model output
                    model_output = self.model(**encoded_input)

                    # Perform mean pooling
                    embeddings = self._mean_pooling(
                        model_output,
                        encoded_input["attention_mask"]
                    )

                    # Convert to numpy
                    embeddings = embeddings.cpu().numpy()

            # Normalize embeddings
            embeddings = embeddings / np.linalg.norm(embeddings, axis=1, keepdims=True)

            return embeddings

        except Exception as e:
            logger.error(f"Error generating Vietnamese embeddings: {str(e)}")
            raise

    def get_embedding(self, text: str) -> np.ndarray:
        """
        Get embedding for a single Vietnamese text.

        Args:
            text: Vietnamese text

        Returns:
            Numpy array of embedding
        """
        embeddings = self.get_embeddings([text])
        return embeddings[0] if len(embeddings) > 0 else np.array([])

    def get_embedding_dimension(self) -> int:
        """
        Get the dimension of the embeddings.

        Returns:
            Dimension of the embeddings
        """
        # Get embedding for a sample text
        sample_embedding = self.get_embedding("Xin chào")

        return sample_embedding.shape[0]

    def calculate_similarity(
        self,
        text1: str,
        text2: str,
        method: str = "cosine"
    ) -> float:
        """
        Calculate similarity between two Vietnamese texts.

        Args:
            text1: First Vietnamese text
            text2: Second Vietnamese text
            method: Similarity method ("cosine", "dot", "euclidean")

        Returns:
            Similarity score
        """
        # Get embeddings
        embedding1 = self.get_embedding(text1)
        embedding2 = self.get_embedding(text2)

        # Calculate similarity
        if method == "cosine":
            return np.dot(embedding1, embedding2)
        elif method == "dot":
            return np.dot(embedding1, embedding2)
        elif method == "euclidean":
            return 1.0 / (1.0 + np.linalg.norm(embedding1 - embedding2))
        else:
            raise ValueError(f"Unsupported similarity method: {method}")

    def find_most_similar(
        self,
        query: str,
        candidates: List[str],
        top_k: int = 5,
        method: str = "cosine"
    ) -> List[Tuple[int, float]]:
        """
        Find the most similar Vietnamese texts to a query.

        Args:
            query: Query Vietnamese text
            candidates: List of candidate Vietnamese texts
            top_k: Number of results to return
            method: Similarity method ("cosine", "dot", "euclidean")

        Returns:
            List of tuples (index, similarity_score)
        """
        # Get embeddings
        query_embedding = self.get_embedding(query)
        candidate_embeddings = self.get_embeddings(candidates)

        # Calculate similarities
        if method == "cosine" or method == "dot":
            similarities = np.dot(candidate_embeddings, query_embedding)
        elif method == "euclidean":
            distances = np.linalg.norm(candidate_embeddings - query_embedding, axis=1)
            similarities = 1.0 / (1.0 + distances)
        else:
            raise ValueError(f"Unsupported similarity method: {method}")

        # Get top-k indices
        top_indices = np.argsort(similarities)[::-1][:top_k]

        # Return (index, similarity) tuples
        return [(int(idx), float(similarities[idx])) for idx in top_indices]


class VietnameseEmbeddingFactory:
    """
    Factory for Vietnamese embedding models.

    This class provides methods to create and manage Vietnamese embedding models.
    """

    _instances: Dict[str, VietnameseEmbeddings] = {}

    @classmethod
    def get_embedding_model(
        cls,
        model_name: str = "phobert",
        device: Optional[str] = None,
        cache_dir: Optional[str] = None
    ) -> VietnameseEmbeddings:
        """
        Get a Vietnamese embedding model.

        Args:
            model_name: Name of the embedding model to use
            device: Device to use for inference ("cpu", "cuda", "mps")
            cache_dir: Directory to cache models

        Returns:
            Vietnamese embedding model
        """
        # Create a unique key for this configuration
        key = f"{model_name}_{device}_{cache_dir}"

        # Return existing instance if available
        if key in cls._instances:
            return cls._instances[key]

        # Create new instance
        embedding_model = VietnameseEmbeddings(
            model_name=model_name,
            device=device,
            cache_dir=cache_dir
        )

        # Store instance
        cls._instances[key] = embedding_model

        return embedding_model
