"""
Integration of abbreviation detection with RAG (Retrieval-Augmented Generation).

This module provides functionality to integrate abbreviation detection and expansion
with RAG systems to improve retrieval quality and relevance.
"""

import logging
from typing import List, Dict, Any, Tuple, Optional

# Create logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Try to import abbreviation processors
try:
    from .abbreviation_processor import AbbreviationProcessor
    ABBREVIATION_PROCESSOR_AVAILABLE = True
except ImportError:
    ABBREVIATION_PROCESSOR_AVAILABLE = False
    logger.warning("AbbreviationProcessor is not available. Basic functionality will be limited.")

try:
    from .abbreviation_ml_detector import AbbreviationMLDetector
    ABBREVIATION_ML_DETECTOR_AVAILABLE = True
except ImportError:
    ABBREVIATION_ML_DETECTOR_AVAILABLE = False
    logger.warning("AbbreviationMLDetector is not available. ML-based detection will be disabled.")

try:
    from .abbreviation_ml_advanced import AdvancedAbbreviationMLDetector
    ADVANCED_ML_DETECTOR_AVAILABLE = True
except ImportError:
    ADVANCED_ML_DETECTOR_AVAILABLE = False
    logger.warning("AdvancedAbbreviationMLDetector is not available. Advanced ML models will be disabled.")

# Try to import multilingual utils
try:
    from ..utils.multilingual_utils import detect_language
    MULTILINGUAL_UTILS_AVAILABLE = True
except ImportError:
    MULTILINGUAL_UTILS_AVAILABLE = False
    logger.warning("multilingual_utils is not available. Language detection will be limited.")

    # Define a simple fallback
    def detect_language(text: str) -> str:
        """Simple fallback for language detection."""
        # Check for Vietnamese characters
        if any(c in text for c in "áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđ"):
            return "vi"
        return "en"

class AbbreviationRAGEnhancer:
    """
    Enhances RAG systems with abbreviation detection and expansion.

    This class provides methods to enhance RAG queries and results by
    detecting and expanding abbreviations, improving retrieval quality.
    """

    # Singleton instance
    _instance = None

    @classmethod
    def get_instance(cls, **kwargs) -> 'AbbreviationRAGEnhancer':
        """
        Get singleton instance of AbbreviationRAGEnhancer.

        Args:
            **kwargs: Additional parameters for initialization

        Returns:
            Instance of AbbreviationRAGEnhancer
        """
        if cls._instance is None:
            cls._instance = cls(**kwargs)
        return cls._instance

    def __init__(
        self,
        use_ml: bool = True,
        use_advanced_ml: bool = True,
        ml_model_type: str = "transformer",
        enable_query_expansion: bool = True,
        enable_result_enhancement: bool = True,
        abbreviation_weight: float = 0.3,
        expansion_weight: float = 0.7,
        cache_size: int = 1024
    ):
        """
        Initialize the AbbreviationRAGEnhancer.

        Args:
            use_ml: Whether to use ML-based detection
            use_advanced_ml: Whether to use advanced ML models
            ml_model_type: Type of ML model to use
            enable_query_expansion: Whether to enable query expansion
            enable_result_enhancement: Whether to enhance search results
            abbreviation_weight: Weight for abbreviation in hybrid queries
            expansion_weight: Weight for expansion in hybrid queries
            cache_size: Size of the cache for processed queries
        """
        self.use_ml = use_ml
        self.use_advanced_ml = use_advanced_ml
        self.ml_model_type = ml_model_type
        self.enable_query_expansion = enable_query_expansion
        self.enable_result_enhancement = enable_result_enhancement
        self.abbreviation_weight = abbreviation_weight
        self.expansion_weight = expansion_weight
        self.cache_size = cache_size

        # Initialize abbreviation processors
        self._initialize_processors()

    def _initialize_processors(self):
        """Initialize abbreviation processors based on availability."""
        self.abbreviation_processor = None
        self.ml_detector = None
        self.advanced_ml_detector = None

        # Initialize rule-based processor
        if ABBREVIATION_PROCESSOR_AVAILABLE:
            self.abbreviation_processor = AbbreviationProcessor.get_instance()

        # Initialize ML detector
        if self.use_ml and ABBREVIATION_ML_DETECTOR_AVAILABLE:
            self.ml_detector = AbbreviationMLDetector.get_instance(
                model_type="naive_bayes" if self.ml_model_type == "naive_bayes" else "lstm"
            )

        # Initialize advanced ML detector
        if self.use_ml and self.use_advanced_ml and ADVANCED_ML_DETECTOR_AVAILABLE:
            self.advanced_ml_detector = AdvancedAbbreviationMLDetector.get_instance(
                model_type=self.ml_model_type,
                language="en"  # Will be updated per query
            )

    def enhance_query(self, query: str, language: Optional[str] = None) -> Dict[str, Any]:
        """
        Enhance a RAG query with abbreviation detection and expansion.

        Args:
            query: Original query
            language: Language code (if None, will be detected)

        Returns:
            Dictionary with enhanced query information
        """
        # Detect language if not provided
        if language is None and MULTILINGUAL_UTILS_AVAILABLE:
            language = detect_language(query)
        elif language is None:
            language = "en"

        # Detect abbreviations
        abbreviations = self._detect_abbreviations(query, language)

        # Expand abbreviations
        expansions = self._expand_abbreviations(abbreviations, language, context=query)

        # Create enhanced queries
        enhanced_queries = self._create_enhanced_queries(query, abbreviations, expansions)

        return {
            "original_query": query,
            "language": language,
            "abbreviations": abbreviations,
            "expansions": expansions,
            "enhanced_queries": enhanced_queries
        }

    def enhance_results(self, results: List[Dict[str, Any]], query_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Enhance RAG results with abbreviation information.

        Args:
            results: Original RAG results
            query_info: Query information from enhance_query

        Returns:
            Enhanced results with abbreviation information
        """
        if not self.enable_result_enhancement:
            return results

        enhanced_results = []

        for result in results:
            # Copy the original result
            enhanced_result = result.copy()

            # Add abbreviation information
            enhanced_result["abbreviation_info"] = {
                "query_abbreviations": query_info.get("abbreviations", []),
                "query_expansions": query_info.get("expansions", {})
            }

            # Detect abbreviations in the result content
            content = result.get("content", "")
            if content:
                language = query_info.get("language", "en")
                result_abbreviations = self._detect_abbreviations(content, language)
                result_expansions = self._expand_abbreviations(result_abbreviations, language, context=content)

                enhanced_result["abbreviation_info"]["result_abbreviations"] = result_abbreviations
                enhanced_result["abbreviation_info"]["result_expansions"] = result_expansions

                # Calculate abbreviation overlap score
                overlap_score = self._calculate_abbreviation_overlap(
                    query_info.get("abbreviations", []),
                    result_abbreviations
                )

                enhanced_result["abbreviation_overlap_score"] = overlap_score

            enhanced_results.append(enhanced_result)

        # Re-rank results based on abbreviation overlap if needed
        if any("abbreviation_overlap_score" in result for result in enhanced_results):
            enhanced_results = self._rerank_results(enhanced_results)

        return enhanced_results

    def _detect_abbreviations(self, text: str, language: str) -> List[Tuple[str, int, int, float]]:
        """
        Detect abbreviations in text.

        Args:
            text: Text to analyze
            language: Language code

        Returns:
            List of tuples containing (abbreviation, start_idx, end_idx, confidence)
        """
        abbreviations = []

        # Try advanced ML detector first
        if self.use_ml and self.use_advanced_ml and self.advanced_ml_detector:
            try:
                # Update language
                detector = AdvancedAbbreviationMLDetector.get_instance(
                    model_type=self.ml_model_type,
                    language=language
                )

                # Detect abbreviations
                abbreviations = detector.find_abbreviations_in_text(text)

                if abbreviations:
                    return abbreviations
            except Exception as e:
                logger.warning(f"Error in advanced ML abbreviation detection: {str(e)}")

        # Try ML detector
        if self.use_ml and self.ml_detector:
            try:
                abbreviations = self.ml_detector.find_abbreviations_in_text(text)

                if abbreviations:
                    return abbreviations
            except Exception as e:
                logger.warning(f"Error in ML abbreviation detection: {str(e)}")

        # Fall back to rule-based detection
        if self.abbreviation_processor:
            try:
                rule_based_abbreviations = self.abbreviation_processor.detect_abbreviations(
                    text=text,
                    language=language
                )

                # Convert to the expected format
                abbreviations = [(abbr, start, end, 0.7) for abbr, start, end in rule_based_abbreviations]
            except Exception as e:
                logger.warning(f"Error in rule-based abbreviation detection: {str(e)}")

        return abbreviations

    def _expand_abbreviations(
        self,
        abbreviations: List[Tuple[str, int, int, float]],
        language: str,
        context: Optional[str] = None
    ) -> Dict[str, str]:
        """
        Expand detected abbreviations.

        Args:
            abbreviations: List of detected abbreviations
            language: Language code
            context: Context text for better expansion

        Returns:
            Dictionary mapping abbreviations to their expansions
        """
        expansions = {}

        if not abbreviations or not self.abbreviation_processor:
            return expansions

        for abbr, _, _, _ in abbreviations:
            try:
                expansion = self.abbreviation_processor.expand_abbreviation(
                    abbreviation=abbr,
                    language=language,
                    context=context
                )

                if expansion:
                    expansions[abbr] = expansion
            except Exception as e:
                logger.warning(f"Error expanding abbreviation {abbr}: {str(e)}")

        return expansions

    def _create_enhanced_queries(
        self,
        original_query: str,
        abbreviations: List[Tuple[str, int, int, float]],
        expansions: Dict[str, str]
    ) -> List[Dict[str, Any]]:
        """
        Create enhanced queries for RAG.

        Args:
            original_query: Original query
            abbreviations: Detected abbreviations
            expansions: Abbreviation expansions

        Returns:
            List of enhanced queries
        """
        if not self.enable_query_expansion or not abbreviations or not expansions:
            return [{"query": original_query, "type": "original", "weight": 1.0}]

        enhanced_queries = [
            {"query": original_query, "type": "original", "weight": 1.0}
        ]

        # Create expanded query (replace abbreviations with expansions)
        expanded_query = original_query
        for abbr, start, end, _ in sorted(abbreviations, key=lambda x: x[1], reverse=True):
            if abbr in expansions:
                expanded_query = expanded_query[:start] + expansions[abbr] + expanded_query[end:]

        if expanded_query != original_query:
            enhanced_queries.append({
                "query": expanded_query,
                "type": "expanded",
                "weight": self.expansion_weight
            })

        # Create hybrid query (abbreviation + expansion)
        hybrid_queries = []
        for abbr, _, _, _ in abbreviations:
            if abbr in expansions:
                hybrid_query = original_query + " " + expansions[abbr]
                hybrid_queries.append({
                    "query": hybrid_query,
                    "type": "hybrid",
                    "weight": self.abbreviation_weight
                })

        enhanced_queries.extend(hybrid_queries)

        return enhanced_queries

    def _calculate_abbreviation_overlap(
        self,
        query_abbreviations: List[Tuple[str, int, int, float]],
        result_abbreviations: List[Tuple[str, int, int, float]]
    ) -> float:
        """
        Calculate abbreviation overlap between query and result.

        Args:
            query_abbreviations: Abbreviations in the query
            result_abbreviations: Abbreviations in the result

        Returns:
            Overlap score between 0 and 1
        """
        if not query_abbreviations or not result_abbreviations:
            return 0.0

        query_abbrs = {abbr.lower() for abbr, _, _, _ in query_abbreviations}
        result_abbrs = {abbr.lower() for abbr, _, _, _ in result_abbreviations}

        # Calculate similarity
        intersection = len(query_abbrs.intersection(result_abbrs))
        union = len(query_abbrs.union(result_abbrs))

        if union == 0:
            return 0.0

        return intersection / union

    def _rerank_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Re-rank results based on abbreviation overlap.

        Args:
            results: Original results

        Returns:
            Re-ranked results
        """
        # Sort by abbreviation overlap score (higher is better)
        reranked = sorted(
            results,
            key=lambda x: x.get("abbreviation_overlap_score", 0.0),
            reverse=True
        )

        return reranked
