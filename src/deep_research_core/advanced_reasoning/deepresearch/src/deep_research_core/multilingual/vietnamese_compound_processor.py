"""
Vietnamese Compound Word Processor.

This module provides functionality for processing Vietnamese compound words,
which are common in Vietnamese language. It handles detection, segmentation,
and analysis of different types of Vietnamese compound words.
"""

import re
from typing import Dict, List, Set, Tuple, Optional
import unicodedata
from collections import Counter

from .vietnamese_diacritic_processor import VietnameseDiacriticProcessor
from .vietnamese_domain_compounds import VietnameseDomainCompounds


class VietnameseCompoundProcessor:
    """
    Processor for Vietnamese compound words.
    
    This class provides methods for analyzing and processing Vietnamese compound words,
    including reduplicated forms, coordinated compounds, and subordinated compounds.
    """
    
    _instance = None
    
    @classmethod
    def get_instance(cls) -> 'VietnameseCompoundProcessor':
        """Get the singleton instance of the processor."""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def __init__(self):
        """Initialize the Vietnamese compound word processor."""
        self.diacritic_processor = VietnameseDiacriticProcessor.get_instance()
        
        # Common Vietnamese reduplication patterns
        self._reduplication_patterns = [
            r'(\w+)\s+\1',                  # Full reduplication: đẹp đẹp
            r'(\w+)\s+\w*\1\w*',            # Partial reduplication: xanh xanh lơ
            r'(\w{1})(\w+)\s+\1\w+',        # Alliteration: lung linh
            r'(\w+)(\w{1})\s+\w+\2',        # Rhyming: lấp lánh
            r'(\w+)\s+(\w+\s+\1)',          # ABA pattern: xanh đỏ xanh
            r'(\w+)(\s+\w+)+\s+\1',         # Distant reduplication: đẹp như hoa đẹp
            r'(\w+)\s+\1\s+(\w+)',          # AA-B pattern: đẹp đẹp quá
            r'(\w+)\s+(\w+)\s+\1\s+\2',     # ABAB pattern: xanh đỏ xanh đỏ
            r'(\w{2})(\w*)\s+\1\w*',        # Partial syllable reduplication: thành phần
        ]
        
        # Load common Vietnamese compounds dictionary (example)
        self._compound_dict = self._load_compound_dictionary()
        
        # Vietnamese grammatical particles that often appear in compounds
        self.grammatical_particles = {
            'của', 'và', 'hay', 'hoặc', 'với', 'cùng', 'mà', 'để',
            'thì', 'là', 'như', 'mới', 'cũng', 'đã', 'sẽ', 'vì', 'nên'
        }
        
        # Common Vietnamese compound word formations 
        self.common_compound_formations = {
            # Format: (prefix, suffix): semantic_relationship
            ('nhà', ''): 'profession',      # nhà báo, nhà văn
            ('', 'viên'): 'profession',     # nhân viên, học viên
            ('', 'gia'): 'profession',      # nghệ sĩ, khoa học gia
            ('', 'sĩ'): 'profession',       # nghệ sĩ, kỹ sĩ
            ('', 'phẩm'): 'product',        # sản phẩm, tác phẩm
            ('', 'cụ'): 'tool',             # dụng cụ, đồ cụ
            ('', 'liệu'): 'material',       # nguyên liệu, tài liệu
        }
        
    def _load_compound_dictionary(self) -> Dict[str, str]:
        """
        Load a dictionary of common Vietnamese compound words.
        
        Returns:
            Dictionary mapping compound words to their types
        """
        # Base dictionary with common compounds
        compounds_dict = {
            "xe máy": "subordinate",
            "bàn ghế": "coordinate",
            "quần áo": "coordinate",
            "đường sá": "reduplicated",
            "học hành": "reduplicated",
            "thức ăn": "subordinate",
            "nhà cửa": "coordinate",
            "cây cối": "coordinate",
            "nồi niêu": "coordinate",
            "bánh trái": "coordinate",
            "chén đĩa": "coordinate",
            "người người": "reduplicated",
            "lăng xăng": "reduplicated",
            "lụa là": "reduplicated",
            "mặt mũi": "coordinate",
            "tay chân": "coordinate",
            "trường học": "subordinate",
            "bệnh viện": "subordinate",
            "chợ búa": "coordinate",
            "giường chiếu": "coordinate",
            "than thở": "reduplicated",
            "đi lại": "reduplicated",
            "nhà báo": "subordinate",
            "cửa sổ": "subordinate"
        }
        
        # Add domain-specific compounds
        try:
            domain_compounds = VietnameseDomainCompounds.get_all_domain_compounds()
            compounds_dict.update(domain_compounds)
        except Exception as e:
            print(f"Warning: Could not load domain-specific compounds: {str(e)}")
            
        return compounds_dict
    
    def detect_compound_words(self, text: str) -> List[Tuple[str, int, int, str]]:
        """
        Detect compound words in Vietnamese text.
        
        Args:
            text: Vietnamese text to analyze
            
        Returns:
            List of tuples containing (compound_word, start_idx, end_idx, compound_type)
        """
        compounds = []
        
        # Normalize diacritics for consistent processing
        normalized_text = self.diacritic_processor.normalize_diacritics(text)
        
        # Dictionary-based detection
        for compound, compound_type in self._compound_dict.items():
            for match in re.finditer(rf'\b{re.escape(compound)}\b', normalized_text):
                compounds.append((
                    match.group(0),
                    match.start(),
                    match.end(),
                    compound_type
                ))
        
        # Pattern-based detection for reduplication
        for pattern in self._reduplication_patterns:
            for match in re.finditer(pattern, normalized_text):
                compound = match.group(0)
                # Avoid duplicates that were already found in the dictionary
                if not any(c[0] == compound and c[1] == match.start() for c in compounds):
                    compounds.append((
                        compound,
                        match.start(),
                        match.end(),
                        "reduplicated"
                    ))
        
        # Detect common compound formations
        words = normalized_text.split()
        for i in range(len(words) - 1):
            pair = f"{words[i]} {words[i+1]}"
            start_idx = normalized_text.find(pair)
            if start_idx >= 0 and not any(c[0] == pair and c[1] == start_idx for c in compounds):
                # Check if this follows a common formation pattern
                for (prefix, suffix), relationship in self.common_compound_formations.items():
                    if (prefix and words[i] == prefix) or (suffix and words[i+1] == suffix):
                        compounds.append((
                            pair,
                            start_idx,
                            start_idx + len(pair),
                            "subordinate"
                        ))
                        break
                        
        # Sort by position in text
        compounds.sort(key=lambda x: x[1])
        
        return compounds
    
    def segment_compound_words(self, text: str) -> List[str]:
        """
        Segment text into words, properly handling compound words.
        
        Args:
            text: Vietnamese text to segment
            
        Returns:
            List of segmented words, with compounds preserved as single units
        """
        # Normalize text first
        text = self.diacritic_processor.normalize_diacritics(text)
        
        # Find all compounds
        compounds = self.detect_compound_words(text)
        
        # If no compounds, do simple whitespace tokenization
        if not compounds:
            return text.split()
        
        # Process text with compounds
        result = []
        last_end = 0
        
        for compound, start, end, _ in compounds:
            # Add any text before this compound
            if start > last_end:
                result.extend(text[last_end:start].split())
            
            # Add the compound as a single unit
            result.append(compound)
            last_end = end
        
        # Add any remaining text
        if last_end < len(text):
            result.extend(text[last_end:].split())
        
        return result
    
    def analyze_compound_distribution(self, text: str) -> Dict:
        """
        Analyze the distribution of compound word types in text.
        
        Args:
            text: Vietnamese text to analyze
            
        Returns:
            Dictionary with statistics about compound word usage
        """
        compounds = self.detect_compound_words(text)
        
        # Count by type
        type_counter = Counter(comp_type for _, _, _, comp_type in compounds)
        
        # Get total tokens (approximate)
        tokens = len(text.split())
        
        return {
            "total_compounds": len(compounds),
            "type_distribution": dict(type_counter),
            "compounds_per_token": len(compounds) / tokens if tokens > 0 else 0,
            "compound_ratio": len(compounds) / tokens if tokens > 0 else 0,
            "compound_examples": [comp for comp, _, _, _ in compounds[:5]],
        }
    
    def is_compound_word(self, text: str) -> bool:
        """
        Check if the given text is a Vietnamese compound word.
        
        Args:
            text: Word or phrase to check
            
        Returns:
            Boolean indicating if the text is a compound word
        """
        # Normalize text
        text = self.diacritic_processor.normalize_diacritics(text)
        
        # Dictionary check
        if text in self._compound_dict:
            return True
        
        # Pattern check for reduplication
        if any(re.fullmatch(pattern, text) for pattern in self._reduplication_patterns):
            return True
        
        # Check if it has space and no grammatical particles
        if ' ' in text and not any(particle in text.split() for particle in self.grammatical_particles):
            return True
            
        return False
    
    def classify_compound_type(self, compound: str) -> str:
        """
        Classify the type of Vietnamese compound word.
        
        Args:
            compound: Compound word to classify
            
        Returns:
            Type of compound: "coordinate", "subordinate", "reduplicated", or "unknown"
        """
        # Normalize text
        compound = self.diacritic_processor.normalize_diacritics(compound)
        
        # Dictionary lookup
        if compound in self._compound_dict:
            return self._compound_dict[compound]
        
        # Reduplication pattern check
        if any(re.fullmatch(pattern, compound) for pattern in self._reduplication_patterns):
            return "reduplicated"
        
        # Basic classification heuristics
        words = compound.split()
        
        if len(words) != 2:
            return "unknown"  # We only handle 2-word compounds with this heuristic
        
        # Check for common compound formations
        for (prefix, suffix), _ in self.common_compound_formations.items():
            if (prefix and words[0] == prefix) or (suffix and words[1] == suffix):
                return "subordinate"
        
        # Check for common coordinate compound patterns (similar words)
        if self._are_semantically_related(words[0], words[1]):
            return "coordinate"
        
        # Default to subordinate
        return "subordinate"
    
    def _are_semantically_related(self, word1: str, word2: str) -> bool:
        """
        Check if two words are semantically related (for coordinate compounds).
        
        This is a simple heuristic that could be improved with a real semantic database.
        
        Args:
            word1: First word
            word2: Second word
            
        Returns:
            Boolean indicating if words are likely semantically related
        """
        # Simple check for reduplication
        if word1 == word2:
            return True
            
        # Check for partial reduplication (shared letters)
        if len(word1) >= 2 and len(word2) >= 2:
            if word1[0] == word2[0]:  # Same initial consonant
                return True
            if word1[-1] == word2[-1]:  # Same final sound
                return True
                
        # More sophisticated methods would use a lexical database
        return False
    
    def extract_core_meaning(self, compound: str) -> str:
        """
        Extract the core semantic component from a compound word.
        
        For subordinated compounds, this is typically the head noun.
        
        Args:
            compound: Compound word to analyze
            
        Returns:
            Core semantic component
        """
        words = compound.split()
        
        if len(words) != 2:
            return compound  # Can't determine for compounds with != 2 words
            
        compound_type = self.classify_compound_type(compound)
        
        if compound_type == "subordinate":
            # In Vietnamese subordinate compounds, the head is typically first
            return words[0]
        elif compound_type == "coordinate":
            # For coordinate compounds, both elements carry meaning
            # Return the first one as a simplification
            return words[0]
        elif compound_type == "reduplicated":
            # For reduplications, the base form is typically the first occurrence
            return words[0]
        else:
            return compound
    
    def process_phrasal_compounds(self, text: str) -> str:
        """
        Process text to properly handle Vietnamese phrasal compounds.
        
        This method identifies and joins phrasal compounds with underscores 
        for downstream NLP tasks.
        
        Args:
            text: Vietnamese text to process
            
        Returns:
            Text with phrasal compounds joined with underscores
        """
        compounds = self.detect_compound_words(text)
        
        # If no compounds found, return original text
        if not compounds:
            return text
            
        # Process compounds from end to start to avoid offset issues
        result = text
        for compound, start, end, _ in sorted(compounds, key=lambda x: x[1], reverse=True):
            # Replace space with underscore in compound
            joined_compound = compound.replace(' ', '_')
            result = result[:start] + joined_compound + result[end:]
            
        return result
    
    def get_semantically_similar_compounds(self, compound: str, threshold: float = 0.7) -> List[str]:
        """
        Find semantically similar compounds to the given compound.
        
        This is a placeholder implementation. In a full implementation, this would
        use vector embeddings or a lexical database.
        
        Args:
            compound: Compound word to find similar matches for
            threshold: Similarity threshold (0-1)
            
        Returns:
            List of semantically similar compounds
        """
        # Simple implementation based on compound type and shared words
        compound_type = self.classify_compound_type(compound)
        words = compound.split()
        
        similar_compounds = []
        
        for candidate in self._compound_dict:
            if self.classify_compound_type(candidate) == compound_type:
                candidate_words = candidate.split()
                
                # Check for shared words
                shared = sum(1 for w in words if w in candidate_words)
                similarity = shared / max(len(words), len(candidate_words))
                
                if similarity >= threshold and candidate != compound:
                    similar_compounds.append(candidate)
                    
        return similar_compounds
    
    def filter_by_domain(self, text: str, domain: str) -> List[Tuple[str, int, int, str]]:
        """
        Detect compound words in Vietnamese text filtered by a specific domain.
        
        Args:
            text: Vietnamese text to analyze
            domain: Domain to filter compounds by (e.g., "medical", "legal", "technology")
            
        Returns:
            List of tuples containing (compound_word, start_idx, end_idx, compound_type)
        """
        # Get all compounds first
        all_compounds = self.detect_compound_words(text)
        
        try:
            # Get domain-specific compounds dictionary
            domain_dict = VietnameseDomainCompounds.get_domain_compounds(domain)
            
            # Filter compounds that are in the domain dictionary
            domain_compounds = []
            for compound, start, end, comp_type in all_compounds:
                if compound in domain_dict:
                    domain_compounds.append((compound, start, end, comp_type))
                    
            return domain_compounds
            
        except Exception as e:
            print(f"Warning: Error filtering by domain: {str(e)}")
            return all_compounds
    
    def get_available_domains(self) -> List[str]:
        """
        Get a list of available domains for compound filtering.
        
        Returns:
            List of domain names
        """
        try:
            return VietnameseDomainCompounds.get_available_domains()
        except Exception as e:
            print(f"Warning: Could not get available domains: {str(e)}")
            return []
    
    def detect_domain_specific_compounds(self, text: str) -> Dict[str, List[Tuple[str, int, int, str]]]:
        """
        Detect and categorize compound words by domain.
        
        This method analyzes the text and categorizes detected compounds into domains.
        Each compound may appear in multiple domains if it's present in multiple domain dictionaries.
        
        Args:
            text: Vietnamese text to analyze
            
        Returns:
            Dictionary mapping domain names to lists of compound tuples
        """
        # Get all compound words in the text
        all_compounds = self.detect_compound_words(text)
        
        # Initialize result dictionary
        domain_compounds = {}
        
        try:
            # Get available domains
            domains = self.get_available_domains()
            
            # For each domain, filter compounds
            for domain in domains:
                domain_dict = VietnameseDomainCompounds.get_domain_compounds(domain)
                
                # Find compounds that belong to this domain
                domain_compounds[domain] = []
                for compound, start, end, comp_type in all_compounds:
                    if compound in domain_dict:
                        domain_compounds[domain].append((compound, start, end, comp_type))
                        
        except Exception as e:
            print(f"Warning: Error in domain-specific detection: {str(e)}")
            
        return domain_compounds


# Singleton instance
vietnamese_compound_processor = VietnameseCompoundProcessor.get_instance() 