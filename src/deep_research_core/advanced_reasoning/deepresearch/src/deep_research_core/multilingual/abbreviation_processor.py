"""
Multilingual Abbreviation Processor.

This module provides functionality for processing abbreviations and acronyms
in multiple languages. It handles detection, expansion, and normalization
of abbreviations across different languages.
"""

import re
import os
import json
import logging
import string
from typing import Dict, List, Set, Tuple, Optional, Any, Union
from collections import defaultdict, Counter

# Create logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Try to import optional dependencies for ML-based detection
try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False

try:
    from sklearn.feature_extraction.text import CountVectorizer
    from sklearn.naive_bayes import MultinomialNB
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

class AbbreviationProcessor:
    """
    Multilingual abbreviation processor.

    This class provides functionality for processing abbreviations and acronyms
    in multiple languages, including detection, expansion, and normalization.
    """

    # Singleton instance
    _instance = None

    # Common abbreviation patterns
    ABBREVIATION_PATTERNS = {
        # General patterns that work across many languages
        "general": [
            r'\b([A-Z][A-Z0-9]+)\b',  # All caps abbreviations (NASA, FBI)
            r'\b([A-Z]\.)+\b',         # Abbreviations with periods (U.S.A.)
            r'\b([A-Z]\.)([A-Z]\.)+\b' # Abbreviations with periods (U.S.A.)
        ],
        # English-specific patterns
        "en": [
            r'\b(Mr|Mrs|Ms|Dr|Prof|Inc|Ltd|Co|Jr|Sr|St)\.',
            r'\b(Jan|Feb|Mar|Apr|Jun|Jul|Aug|Sep|Sept|Oct|Nov|Dec)\.',
            r'\b(Mon|Tue|Tues|Wed|Thu|Thurs|Fri|Sat|Sun)\.'
        ],
        # Vietnamese-specific patterns
        "vi": [
            r'\b(TS|ThS|BS|KS|GS|PGS|CSKH|UBND|HĐND|ĐBQH)\b',
            r'\b(ĐH|CĐ|THPT|THCS|TH|MN)\b',
            r'\b(tp|tỉnh|huyện|xã|phường)\.'
        ],
        # Add patterns for other languages as needed
    }

    # Default abbreviation dictionaries
    DEFAULT_ABBREVIATION_DICTS = {
        "en": {
            # General abbreviations
            "AI": "Artificial Intelligence",
            "ML": "Machine Learning",
            "NLP": "Natural Language Processing",
            "API": "Application Programming Interface",
            "UI": "User Interface",
            "UX": "User Experience",
            "CEO": "Chief Executive Officer",
            "CTO": "Chief Technology Officer",
            "FAQ": "Frequently Asked Questions",
            "ASAP": "As Soon As Possible",
            "e.g.": "for example",
            "i.e.": "that is",
            "etc.": "et cetera",
            "vs.": "versus",
            "approx.": "approximately",
            "dept.": "department",
            "govt.": "government",
            "tech.": "technology",
            "corp.": "corporation",
            "inc.": "incorporated",

            # IT/Computer Science abbreviations
            "CPU": "Central Processing Unit",
            "GPU": "Graphics Processing Unit",
            "RAM": "Random Access Memory",
            "ROM": "Read-Only Memory",
            "SSD": "Solid State Drive",
            "HDD": "Hard Disk Drive",
            "OS": "Operating System",
            "DB": "Database",
            "SQL": "Structured Query Language",
            "NoSQL": "Not Only SQL",
            "HTTP": "Hypertext Transfer Protocol",
            "HTTPS": "Hypertext Transfer Protocol Secure",
            "FTP": "File Transfer Protocol",
            "SSH": "Secure Shell",
            "DNS": "Domain Name System",
            "IP": "Internet Protocol",
            "TCP": "Transmission Control Protocol",
            "UDP": "User Datagram Protocol",
            "IoT": "Internet of Things",
            "VR": "Virtual Reality",
            "AR": "Augmented Reality",
            "MR": "Mixed Reality",
            "XR": "Extended Reality",
            "IDE": "Integrated Development Environment",
            "SDK": "Software Development Kit",
            "API": "Application Programming Interface",
            "REST": "Representational State Transfer",
            "SOAP": "Simple Object Access Protocol",
            "JSON": "JavaScript Object Notation",
            "XML": "eXtensible Markup Language",
            "HTML": "Hypertext Markup Language",
            "CSS": "Cascading Style Sheets",
            "JS": "JavaScript",
            "TS": "TypeScript",
            "OOP": "Object-Oriented Programming",
            "FP": "Functional Programming",
            "CI": "Continuous Integration",
            "CD": "Continuous Deployment",
            "DevOps": "Development and Operations",
            "SRE": "Site Reliability Engineering",
            "SDLC": "Software Development Life Cycle",
            "QA": "Quality Assurance",
            "TDD": "Test-Driven Development",
            "BDD": "Behavior-Driven Development",
            "DDD": "Domain-Driven Design",
            "CRUD": "Create, Read, Update, Delete",
            "MVC": "Model-View-Controller",
            "MVP": "Model-View-Presenter",
            "MVVM": "Model-View-ViewModel",
            "SPA": "Single Page Application",
            "PWA": "Progressive Web Application",
            "SEO": "Search Engine Optimization",

            # Medical abbreviations
            "BP": "Blood Pressure",
            "HR": "Heart Rate",
            "RR": "Respiratory Rate",
            "BMI": "Body Mass Index",
            "ECG": "Electrocardiogram",
            "EEG": "Electroencephalogram",
            "MRI": "Magnetic Resonance Imaging",
            "CT": "Computed Tomography",
            "PET": "Positron Emission Tomography",
            "IV": "Intravenous",
            "IM": "Intramuscular",
            "SC": "Subcutaneous",
            "PO": "Per Os (by mouth)",
            "PRN": "Pro Re Nata (as needed)",
            "BID": "Bis In Die (twice a day)",
            "TID": "Ter In Die (three times a day)",
            "QID": "Quater In Die (four times a day)",
            "QD": "Quaque Die (once a day)",
            "QOD": "Quaque Altera Die (every other day)",
            "NPO": "Nil Per Os (nothing by mouth)",
            "Rx": "Prescription",
            "Dx": "Diagnosis",
            "Tx": "Treatment",
            "Hx": "History",
            "Fx": "Fracture",
            "Sx": "Symptoms",
            "CBC": "Complete Blood Count",
            "WBC": "White Blood Cell",
            "RBC": "Red Blood Cell",
            "Hgb": "Hemoglobin",
            "Hct": "Hematocrit",
            "BUN": "Blood Urea Nitrogen",
            "UTI": "Urinary Tract Infection",
            "URI": "Upper Respiratory Infection",
            "COPD": "Chronic Obstructive Pulmonary Disease",
            "CHF": "Congestive Heart Failure",
            "MI": "Myocardial Infarction",
            "CVA": "Cerebrovascular Accident",
            "TIA": "Transient Ischemic Attack",
            "HTN": "Hypertension",
            "DM": "Diabetes Mellitus",
            "GERD": "Gastroesophageal Reflux Disease",
            "IBD": "Inflammatory Bowel Disease",
            "IBS": "Irritable Bowel Syndrome",
            "RA": "Rheumatoid Arthritis",
            "OA": "Osteoarthritis",
            "MS": "Multiple Sclerosis",
            "AD": "Alzheimer's Disease",
            "PD": "Parkinson's Disease",

            # Economics/Business abbreviations
            "GDP": "Gross Domestic Product",
            "GNP": "Gross National Product",
            "CPI": "Consumer Price Index",
            "PPI": "Producer Price Index",
            "ROI": "Return on Investment",
            "ROA": "Return on Assets",
            "ROE": "Return on Equity",
            "EBITDA": "Earnings Before Interest, Taxes, Depreciation, and Amortization",
            "EPS": "Earnings Per Share",
            "P/E": "Price to Earnings Ratio",
            "P/B": "Price to Book Ratio",
            "IPO": "Initial Public Offering",
            "M&A": "Mergers and Acquisitions",
            "VC": "Venture Capital",
            "PE": "Private Equity",
            "B2B": "Business to Business",
            "B2C": "Business to Consumer",
            "C2C": "Consumer to Consumer",
            "SaaS": "Software as a Service",
            "PaaS": "Platform as a Service",
            "IaaS": "Infrastructure as a Service",
            "CRM": "Customer Relationship Management",
            "ERP": "Enterprise Resource Planning",
            "SCM": "Supply Chain Management",
            "HRM": "Human Resource Management",
            "KPI": "Key Performance Indicator",
            "OKR": "Objectives and Key Results",
            "SWOT": "Strengths, Weaknesses, Opportunities, Threats",
            "PEST": "Political, Economic, Social, Technological",
            "PESTEL": "Political, Economic, Social, Technological, Environmental, Legal",
            "USP": "Unique Selling Proposition",
            "RFP": "Request for Proposal",
            "RFQ": "Request for Quotation",
            "NDA": "Non-Disclosure Agreement",
            "MOU": "Memorandum of Understanding",
            "LOI": "Letter of Intent",
            "YOY": "Year Over Year",
            "QOQ": "Quarter Over Quarter",
            "MOM": "Month Over Month",
            "CAGR": "Compound Annual Growth Rate",
            "CAPEX": "Capital Expenditure",
            "OPEX": "Operating Expenditure",

            # Science abbreviations
            "DNA": "Deoxyribonucleic Acid",
            "RNA": "Ribonucleic Acid",
            "ATP": "Adenosine Triphosphate",
            "ADP": "Adenosine Diphosphate",
            "NADH": "Nicotinamide Adenine Dinucleotide (reduced form)",
            "NADPH": "Nicotinamide Adenine Dinucleotide Phosphate (reduced form)",
            "pH": "Potential of Hydrogen",
            "H2O": "Water",
            "CO2": "Carbon Dioxide",
            "O2": "Oxygen",
            "N2": "Nitrogen",
            "NaCl": "Sodium Chloride",
            "HCl": "Hydrochloric Acid",
            "H2SO4": "Sulfuric Acid",
            "NaOH": "Sodium Hydroxide",
            "UV": "Ultraviolet",
            "IR": "Infrared",
            "NMR": "Nuclear Magnetic Resonance",
            "MS": "Mass Spectrometry",
            "GC": "Gas Chromatography",
            "HPLC": "High-Performance Liquid Chromatography",
            "PCR": "Polymerase Chain Reaction",
            "CRISPR": "Clustered Regularly Interspaced Short Palindromic Repeats",
            "GMO": "Genetically Modified Organism",
            "STEM": "Science, Technology, Engineering, and Mathematics",
            "eV": "Electron Volt",
            "Hz": "Hertz",
            "km": "Kilometer",
            "cm": "Centimeter",
            "mm": "Millimeter",
            "μm": "Micrometer",
            "nm": "Nanometer",
            "kg": "Kilogram",
            "g": "Gram",
            "mg": "Milligram",
            "μg": "Microgram",
            "L": "Liter",
            "mL": "Milliliter",
            "μL": "Microliter",
            "mol": "Mole",
            "mmol": "Millimole",
            "μmol": "Micromole",
            "M": "Molar",
            "mM": "Millimolar",
            "μM": "Micromolar",
            "s": "Second",
            "ms": "Millisecond",
            "μs": "Microsecond",
            "ns": "Nanosecond",
            "min": "Minute",
            "h": "Hour"
        },
        "vi": {
            "ĐHQG": "Đại học Quốc gia",
            "ĐHBK": "Đại học Bách khoa",
            "KHTN": "Khoa học Tự nhiên",
            "KHXH": "Khoa học Xã hội",
            "CNTT": "Công nghệ Thông tin",
            "TTNN": "Tiếng Anh",
            "UBND": "Ủy ban Nhân dân",
            "HĐND": "Hội đồng Nhân dân",
            "TPHCM": "Thành phố Hồ Chí Minh",
            "HN": "Hà Nội",
            "ĐN": "Đà Nẵng",
            "THPT": "Trung học Phổ thông",
            "THCS": "Trung học Cơ sở",
            "TS": "Tiến sĩ",
            "ThS": "Thạc sĩ",
            "GS": "Giáo sư",
            "PGS": "Phó Giáo sư",
            "BS": "Bác sĩ",
            "KS": "Kỹ sư",
            "TNHH": "Trách nhiệm Hữu hạn",
            "CP": "Cổ phần"
        },
        # French abbreviations
        "fr": {
            "IA": "Intelligence Artificielle",
            "AA": "Apprentissage Automatique",
            "TLN": "Traitement du Langage Naturel",
            "PDG": "Président-Directeur Général",
            "DG": "Directeur Général",
            "RH": "Ressources Humaines",
            "TTC": "Toutes Taxes Comprises",
            "HT": "Hors Taxes",
            "SNCF": "Société Nationale des Chemins de fer Français",
            "EDF": "Électricité de France",
            "BNP": "Banque Nationale de Paris",
            "RATP": "Régie Autonome des Transports Parisiens",
            "CNRS": "Centre National de la Recherche Scientifique",
            "INSERM": "Institut National de la Santé et de la Recherche Médicale",
            "INRIA": "Institut National de Recherche en Informatique et en Automatique",
            "p. ex.": "par exemple",
            "c.-à-d.": "c'est-à-dire",
            "etc.": "et cetera",
            "Dr": "Docteur",
            "M.": "Monsieur",
            "Mme": "Madame",
            "Mlle": "Mademoiselle"
        },
        # German abbreviations
        "de": {
            "KI": "Künstliche Intelligenz",
            "ML": "Maschinelles Lernen",
            "NLP": "Natürliche Sprachverarbeitung",
            "GmbH": "Gesellschaft mit beschränkter Haftung",
            "AG": "Aktiengesellschaft",
            "z.B.": "zum Beispiel",
            "d.h.": "das heißt",
            "usw.": "und so weiter",
            "etc.": "et cetera",
            "ca.": "circa",
            "Dr.": "Doktor",
            "Prof.": "Professor",
            "Dipl.": "Diplom",
            "Ing.": "Ingenieur",
            "Tel.": "Telefon",
            "Nr.": "Nummer",
            "Str.": "Straße",
            "Hrsg.": "Herausgeber",
            "Abs.": "Absatz",
            "Jh.": "Jahrhundert"
        },
        # Spanish abbreviations
        "es": {
            "IA": "Inteligencia Artificial",
            "AA": "Aprendizaje Automático",
            "PLN": "Procesamiento del Lenguaje Natural",
            "SA": "Sociedad Anónima",
            "SL": "Sociedad Limitada",
            "PYME": "Pequeña y Mediana Empresa",
            "p. ej.": "por ejemplo",
            "etc.": "etcétera",
            "Dr.": "Doctor",
            "Dra.": "Doctora",
            "Sr.": "Señor",
            "Sra.": "Señora",
            "Srta.": "Señorita",
            "Av.": "Avenida",
            "c/": "calle",
            "tel.": "teléfono",
            "apdo.": "apartado",
            "núm.": "número",
            "pág.": "página",
            "cap.": "capítulo"
        },
        # Chinese abbreviations
        "zh": {
            "中共": "中国共产党",
            "人大": "人民代表大会",
            "北大": "北京大学",
            "清华": "清华大学",
            "公安": "公共安全",
            "高考": "高等教育入学考试",
            "中科院": "中国科学院",
            "工信部": "工业和信息化部",
            "人工智能": "人工智能",
            "机器学习": "机器学习",
            "自然语言处理": "自然语言处理",
            "北京": "北京市",
            "上海": "上海市",
            "广州": "广州市",
            "深圳": "深圳市",
            "等等": "等等"
        },
        # Japanese abbreviations
        "ja": {
            "AI": "人工知能",
            "ML": "機械学習",
            "NLP": "自然言語処理",
            "東大": "東京大学",
            "京大": "京都大学",
            "阪大": "大阪大学",
            "東工大": "東京工業大学",
            "早大": "早稲田大学",
            "慶大": "慶應義塾大学",
            "国立": "国立大学",
            "私立": "私立大学",
            "株式会社": "株式会社",
            "有限会社": "有限会社",
            "合同会社": "合同会社",
            "財団法人": "財団法人",
            "社団法人": "社団法人",
            "独立行政法人": "独立行政法人",
            "等": "など"
        }
    }

    @classmethod
    def get_instance(cls) -> 'AbbreviationProcessor':
        """
        Get singleton instance of AbbreviationProcessor.

        Returns:
            Singleton instance of AbbreviationProcessor
        """
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def __init__(
        self,
        custom_dicts_path: Optional[str] = None,
        enable_learning: bool = True,
        cache_path: Optional[str] = None,
        enable_ml: bool = False
    ):
        """
        Initialize the AbbreviationProcessor.

        Args:
            custom_dicts_path: Path to custom abbreviation dictionaries
            enable_learning: Whether to enable learning new abbreviations
            cache_path: Path to cache file for learned abbreviations
            enable_ml: Whether to enable ML-based detection
        """
        # Load default abbreviation dictionaries
        self.abbreviation_dicts = self.DEFAULT_ABBREVIATION_DICTS.copy()

        # Load custom dictionaries if provided
        if custom_dicts_path and os.path.exists(custom_dicts_path):
            try:
                with open(custom_dicts_path, 'r', encoding='utf-8') as f:
                    custom_dicts = json.load(f)

                # Merge custom dictionaries with defaults
                for lang, abbr_dict in custom_dicts.items():
                    if lang in self.abbreviation_dicts:
                        self.abbreviation_dicts[lang].update(abbr_dict)
                    else:
                        self.abbreviation_dicts[lang] = abbr_dict

                logger.info(f"Loaded custom abbreviation dictionaries from {custom_dicts_path}")
            except Exception as e:
                logger.error(f"Error loading custom abbreviation dictionaries: {str(e)}")

        # Initialize learned abbreviations
        self.learned_abbreviations = defaultdict(dict)
        self.enable_learning = enable_learning
        self.cache_path = cache_path

        # Initialize ML-based detection
        self.enable_ml = enable_ml and SKLEARN_AVAILABLE and NUMPY_AVAILABLE
        self.ml_model = None
        self.vectorizer = None

        if self.enable_ml:
            self._initialize_ml_model()

        # Load learned abbreviations from cache if available
        if cache_path and os.path.exists(cache_path):
            try:
                with open(cache_path, 'r', encoding='utf-8') as f:
                    learned_data = json.load(f)

                for lang, abbr_dict in learned_data.items():
                    self.learned_abbreviations[lang] = abbr_dict

                logger.info(f"Loaded learned abbreviations from {cache_path}")
            except Exception as e:
                logger.error(f"Error loading learned abbreviations: {str(e)}")

    def _initialize_ml_model(self):
        """
        Initialize the ML model for abbreviation detection.

        This method initializes a simple Naive Bayes classifier for
        detecting whether a word is an abbreviation or not.
        """
        if not SKLEARN_AVAILABLE or not NUMPY_AVAILABLE:
            logger.warning("scikit-learn or numpy is not available. ML-based detection is disabled.")
            self.enable_ml = False
            return

        try:
            # Create a vectorizer for feature extraction
            self.vectorizer = CountVectorizer(
                analyzer='char',
                ngram_range=(1, 3),
                lowercase=True
            )

            # Create a Naive Bayes classifier
            self.ml_model = MultinomialNB()

            # Train the model with some examples
            # Collect training data from abbreviation dictionaries
            abbreviations = []
            non_abbreviations = [
                "the", "and", "for", "with", "that", "this", "have", "from",
                "they", "will", "would", "there", "their", "what", "about",
                "which", "when", "make", "like", "time", "just", "know",
                "take", "people", "into", "year", "your", "good", "some",
                "could", "them", "other", "than", "then", "look", "only",
                "come", "over", "think", "also", "back", "after", "work",
                "first", "well", "even", "want", "because", "these", "give"
            ]

            # Add abbreviations from dictionaries
            for lang, abbr_dict in self.abbreviation_dicts.items():
                abbreviations.extend(list(abbr_dict.keys()))

            # Remove duplicates
            abbreviations = list(set(abbreviations))

            # Create training data
            X_train = abbreviations + non_abbreviations
            y_train = [1] * len(abbreviations) + [0] * len(non_abbreviations)

            # Transform text to features
            X_train_features = self.vectorizer.fit_transform(X_train)

            # Train the model
            self.ml_model.fit(X_train_features, y_train)

            logger.info("ML model for abbreviation detection initialized successfully.")
        except Exception as e:
            logger.error(f"Error initializing ML model: {str(e)}")
            self.enable_ml = False

    def detect_abbreviations(
        self,
        text: str,
        language: str = "en",
        use_context: bool = False,
        use_ml: bool = False
    ) -> List[Tuple[str, int, int]]:
        """
        Detect abbreviations in text.

        Args:
            text: Text to analyze
            language: Language code
            use_context: Whether to use context-based detection
            use_ml: Whether to use ML-based detection

        Returns:
            List of tuples containing (abbreviation, start_idx, end_idx)
        """
        abbreviations = []

        # Get patterns for the specified language and general patterns
        patterns = self.ABBREVIATION_PATTERNS.get("general", [])
        if language in self.ABBREVIATION_PATTERNS:
            patterns.extend(self.ABBREVIATION_PATTERNS[language])

        # Find abbreviations using patterns
        for pattern in patterns:
            for match in re.finditer(pattern, text):
                abbreviations.append((
                    match.group(0),
                    match.start(),
                    match.end()
                ))

        # Use context-based detection if enabled
        if use_context:
            context_abbreviations = self._detect_abbreviations_with_context(text, language)
            # Merge with pattern-based abbreviations
            for abbr, start, end in context_abbreviations:
                # Check if this abbreviation is already detected
                if not any(a[0] == abbr and a[1] == start and a[2] == end for a in abbreviations):
                    abbreviations.append((abbr, start, end))

        # Use ML-based detection if enabled
        if use_ml and self.enable_ml and self.ml_model is not None:
            ml_abbreviations = self._detect_abbreviations_with_ml(text)
            # Merge with other abbreviations
            for abbr, start, end in ml_abbreviations:
                # Check if this abbreviation is already detected
                if not any(a[0] == abbr and a[1] == start and a[2] == end for a in abbreviations):
                    abbreviations.append((abbr, start, end))

        # Sort by position in text
        abbreviations.sort(key=lambda x: x[1])

        return abbreviations

    def _detect_abbreviations_with_ml(
        self,
        text: str
    ) -> List[Tuple[str, int, int]]:
        """
        Detect abbreviations in text using machine learning.

        This method uses a trained ML model to identify potential abbreviations
        in the text based on character patterns.

        Args:
            text: Text to analyze

        Returns:
            List of tuples containing (abbreviation, start_idx, end_idx)
        """
        if not self.enable_ml or self.ml_model is None or self.vectorizer is None:
            return []

        abbreviations = []

        try:
            # Split text into words
            words = re.findall(r'\b\w+\b', text)

            # Skip empty list
            if not words:
                return []

            # Transform words to features
            X_features = self.vectorizer.transform(words)

            # Predict abbreviations
            predictions = self.ml_model.predict(X_features)

            # Find positions of predicted abbreviations in the text
            word_positions = []
            for word in words:
                start = 0
                while start < len(text):
                    start = text.find(word, start)
                    if start == -1:
                        break

                    # Check if it's a whole word (not part of another word)
                    if (start == 0 or not text[start-1].isalnum()) and \
                       (start + len(word) >= len(text) or not text[start + len(word)].isalnum()):
                        word_positions.append((word, start, start + len(word)))
                        break

                    start += 1

            # Match predictions with positions
            for i, (word, start, end) in enumerate(word_positions):
                if i < len(predictions) and predictions[i] == 1:
                    # Additional filtering to reduce false positives
                    if len(word) >= 2 and any(c.isupper() for c in word):
                        abbreviations.append((word, start, end))

            return abbreviations
        except Exception as e:
            logger.error(f"Error in ML-based abbreviation detection: {str(e)}")
            return []

    def _detect_abbreviations_with_context(
        self,
        text: str,
        language: str = "en"
    ) -> List[Tuple[str, int, int]]:
        """
        Detect abbreviations in text using context.

        This method uses contextual clues to detect abbreviations that might not
        match the standard patterns, such as abbreviations that are defined
        in the text itself (e.g., "Artificial Intelligence (AI)").

        Args:
            text: Text to analyze
            language: Language code

        Returns:
            List of tuples containing (abbreviation, start_idx, end_idx)
        """
        abbreviations = []

        # Pattern for detecting abbreviations defined in parentheses
        # Example: "Artificial Intelligence (AI)" -> "AI"
        definition_pattern = r'([A-Za-z][A-Za-z\s]+)\s+\(([A-Z][A-Za-z0-9]*(?:[-\.][A-Z][A-Za-z0-9]*)*)\)'
        for match in re.finditer(definition_pattern, text):
            full_term = match.group(1).strip()
            abbreviation = match.group(2).strip()

            # Check if the abbreviation is formed from the initial letters of the full term
            # or if it's a reasonable abbreviation (at least 2 characters)
            if len(abbreviation) >= 2:
                # Add the abbreviation to the list
                abbreviations.append((
                    abbreviation,
                    match.start(2),
                    match.end(2)
                ))

                # Learn this abbreviation if learning is enabled
                if self.enable_learning:
                    self.learn_abbreviation(abbreviation, full_term, language)

        # Pattern for detecting abbreviations with their definitions
        # Example: "AI (Artificial Intelligence)" -> "AI"
        reverse_pattern = r'\b([A-Z][A-Za-z0-9]*(?:[-\.][A-Z][A-Za-z0-9]*)*)\s+\(([A-Za-z][A-Za-z\s]+)\)'
        for match in re.finditer(reverse_pattern, text):
            abbreviation = match.group(1).strip()
            full_term = match.group(2).strip()

            # Check if it's a reasonable abbreviation (at least 2 characters)
            if len(abbreviation) >= 2:
                # Add the abbreviation to the list
                abbreviations.append((
                    abbreviation,
                    match.start(1),
                    match.end(1)
                ))

                # Learn this abbreviation if learning is enabled
                if self.enable_learning:
                    self.learn_abbreviation(abbreviation, full_term, language)

        # Pattern for detecting abbreviations with their definitions using colon
        # Example: "AI: Artificial Intelligence" -> "AI"
        colon_pattern = r'\b([A-Z][A-Za-z0-9]*(?:[-\.][A-Z][A-Za-z0-9]*)*)\s*:\s*([A-Za-z][A-Za-z\s]+)'
        for match in re.finditer(colon_pattern, text):
            abbreviation = match.group(1).strip()
            full_term = match.group(2).strip()

            # Check if it's a reasonable abbreviation (at least 2 characters)
            if len(abbreviation) >= 2:
                # Add the abbreviation to the list
                abbreviations.append((
                    abbreviation,
                    match.start(1),
                    match.end(1)
                ))

                # Learn this abbreviation if learning is enabled
                if self.enable_learning:
                    self.learn_abbreviation(abbreviation, full_term, language)

        return abbreviations

    def expand_abbreviation(
        self,
        abbreviation: str,
        language: str = "en",
        context: Optional[str] = None
    ) -> Optional[str]:
        """
        Expand an abbreviation.

        Args:
            abbreviation: Abbreviation to expand
            language: Language code
            context: Optional context to help with disambiguation

        Returns:
            Expanded form of the abbreviation, or None if not found
        """
        # Normalize abbreviation
        abbr_normalized = abbreviation.strip().upper()

        # Check learned abbreviations first
        if language in self.learned_abbreviations and abbr_normalized in self.learned_abbreviations[language]:
            return self.learned_abbreviations[language][abbr_normalized]

        # Check language-specific dictionary
        if language in self.abbreviation_dicts and abbr_normalized in self.abbreviation_dicts[language]:
            return self.abbreviation_dicts[language][abbr_normalized]

        # Check English dictionary as fallback for technical terms
        if language != "en" and "en" in self.abbreviation_dicts and abbr_normalized in self.abbreviation_dicts["en"]:
            return self.abbreviation_dicts["en"][abbr_normalized]

        # Try to extract from context if provided
        if context:
            expansion = self._extract_from_context(abbr_normalized, context, language)
            if expansion:
                # Learn this abbreviation if learning is enabled
                if self.enable_learning:
                    self.learn_abbreviation(abbreviation, expansion, language)
                return expansion

        # Not found
        return None

    def _extract_from_context(
        self,
        abbreviation: str,
        context: str,
        language: str = "en"
    ) -> Optional[str]:
        """
        Extract the expansion of an abbreviation from context.

        This method tries to find the expansion of an abbreviation in the provided context
        by looking for patterns like "abbreviation (expansion)" or "expansion (abbreviation)".

        Args:
            abbreviation: Abbreviation to expand (normalized)
            context: Context text to search in
            language: Language code

        Returns:
            Expanded form of the abbreviation, or None if not found
        """
        # Pattern for "expansion (abbreviation)"
        pattern1 = r'([A-Za-z][A-Za-z\s]+)\s+\(\s*' + re.escape(abbreviation) + r'\s*\)'
        match = re.search(pattern1, context, re.IGNORECASE)
        if match:
            return match.group(1).strip()

        # Pattern for "abbreviation (expansion)"
        pattern2 = r'\b' + re.escape(abbreviation) + r'\s+\(([A-Za-z][A-Za-z\s]+)\)'
        match = re.search(pattern2, context, re.IGNORECASE)
        if match:
            return match.group(1).strip()

        # Pattern for "abbreviation: expansion"
        pattern3 = r'\b' + re.escape(abbreviation) + r'\s*:\s*([A-Za-z][A-Za-z\s]+)'
        match = re.search(pattern3, context, re.IGNORECASE)
        if match:
            return match.group(1).strip()

        # Pattern for "expansion, or abbreviation,"
        pattern4 = r'([A-Za-z][A-Za-z\s]+),\s*(?:or|also called)\s+' + re.escape(abbreviation) + r'\b'
        match = re.search(pattern4, context, re.IGNORECASE)
        if match:
            return match.group(1).strip()

        # Pattern for "abbreviation, or expansion,"
        pattern5 = r'\b' + re.escape(abbreviation) + r'\b,\s*(?:or|also called)\s+([A-Za-z][A-Za-z\s]+)'
        match = re.search(pattern5, context, re.IGNORECASE)
        if match:
            return match.group(1).strip()

        # Not found in context
        return None

    def process_text(
        self,
        text: str,
        language: str = "en",
        mode: str = "expand_all"
    ) -> str:
        """
        Process text to handle abbreviations.

        Args:
            text: Text to process
            language: Language code
            mode: Processing mode:
                  - "expand_all": Expand all abbreviations
                  - "expand_first": Expand only the first occurrence
                  - "annotate": Add annotations (e.g., tooltips)

        Returns:
            Processed text
        """
        # Detect abbreviations
        abbreviations = self.detect_abbreviations(text, language)

        # If no abbreviations found, return original text
        if not abbreviations:
            return text

        # Process abbreviations based on mode
        if mode == "expand_all":
            # Process from end to start to avoid offset issues
            result = text
            expanded_abbrs = set()

            for abbr, start, end in sorted(abbreviations, key=lambda x: x[1], reverse=True):
                expansion = self.expand_abbreviation(abbr, language)
                if expansion:
                    if abbr not in expanded_abbrs:
                        replacement = f"{expansion} ({abbr})"
                        expanded_abbrs.add(abbr)
                    else:
                        replacement = expansion

                    result = result[:start] + replacement + result[end:]

            return result

        elif mode == "expand_first":
            # Expand only the first occurrence of each abbreviation
            result = text
            expanded_abbrs = set()
            offset = 0

            for abbr, start, end in abbreviations:
                if abbr not in expanded_abbrs:
                    expansion = self.expand_abbreviation(abbr, language)
                    if expansion:
                        replacement = f"{expansion} ({abbr})"
                        result = (
                            result[:start + offset] +
                            replacement +
                            result[end + offset:]
                        )
                        offset += len(replacement) - (end - start)
                        expanded_abbrs.add(abbr)

            return result

        elif mode == "annotate":
            # Add annotations (for HTML output)
            result = text
            offset = 0

            for abbr, start, end in abbreviations:
                expansion = self.expand_abbreviation(abbr, language)
                if expansion:
                    replacement = f'<abbr title="{expansion}">{abbr}</abbr>'
                    result = (
                        result[:start + offset] +
                        replacement +
                        result[end + offset:]
                    )
                    offset += len(replacement) - (end - start)

            return result

        # Default: return original text
        return text

    def learn_abbreviation(
        self,
        abbreviation: str,
        expansion: str,
        language: str = "en"
    ) -> None:
        """
        Learn a new abbreviation.

        Args:
            abbreviation: Abbreviation to learn
            expansion: Expansion of the abbreviation
            language: Language code
        """
        if not self.enable_learning:
            return

        # Normalize abbreviation
        abbr_normalized = abbreviation.strip().upper()

        # Add to learned abbreviations
        self.learned_abbreviations[language][abbr_normalized] = expansion
        logger.info(f"Learned new abbreviation: {abbr_normalized} = {expansion} ({language})")

        # Save to cache if enabled
        self._save_cache()

    def _save_cache(self) -> None:
        """
        Save learned abbreviations to cache.
        """
        if not self.enable_learning or not self.cache_path:
            return

        try:
            # Create directory if it doesn't exist
            cache_dir = os.path.dirname(self.cache_path)
            if cache_dir and not os.path.exists(cache_dir):
                os.makedirs(cache_dir)

            # Save to file
            with open(self.cache_path, 'w', encoding='utf-8') as f:
                json.dump(dict(self.learned_abbreviations), f, ensure_ascii=False, indent=2)

            logger.info(f"Saved learned abbreviations to {self.cache_path}")
        except Exception as e:
            logger.error(f"Error saving learned abbreviations: {str(e)}")
