"""
Vietnamese diacritic processor.

This module provides utilities for processing, normalizing, and restoring
Vietnamese diacritics (tone marks).
"""

import re
import unicodedata
from typing import Dict, List, Set, Tuple, Optional, Union, Any, Callable

from ..utils.structured_logging import get_logger
from ..utils.vietnamese_utils import detect_vietnamese

logger = get_logger(__name__)

class VietnameseDiacriticProcessor:
    """
    Vietnamese diacritic processor.
    
    This class provides methods for processing, normalizing, and restoring
    Vietnamese diacritics (tone marks).
    """
    
    # Singleton instance
    _instance = None
    
    # Vietnamese vowels with diacritics
    # Format: {base_vowel: [all_variants]}
    VOWEL_MAP = {
        'a': ['a', 'à', 'á', 'ả', 'ã', 'ạ', 'ă', 'ằ', 'ắ', 'ẳ', 'ẵ', 'ặ', 'â', 'ầ', 'ấ', 'ẩ', 'ẫ', 'ậ'],
        'e': ['e', 'è', 'é', 'ẻ', 'ẽ', 'ẹ', 'ê', 'ề', 'ế', 'ể', 'ễ', 'ệ'],
        'i': ['i', 'ì', 'í', 'ỉ', 'ĩ', 'ị'],
        'o': ['o', 'ò', 'ó', 'ỏ', 'õ', 'ọ', 'ô', 'ồ', 'ố', 'ổ', 'ỗ', 'ộ', 'ơ', 'ờ', 'ớ', 'ở', 'ỡ', 'ợ'],
        'u': ['u', 'ù', 'ú', 'ủ', 'ũ', 'ụ', 'ư', 'ừ', 'ứ', 'ử', 'ữ', 'ự'],
        'y': ['y', 'ỳ', 'ý', 'ỷ', 'ỹ', 'ỵ'],
        'd': ['d', 'đ']
    }
    
    # Vietnamese diacritic marks (tone)
    TONE_MARKS = {
        '': 0,  # No tone (ngang)
        '\u0300': 1,  # Huyền (grave accent)
        '\u0301': 2,  # Sắc (acute accent)
        '\u0309': 3,  # Hỏi (hook above)
        '\u0303': 4,  # Ngã (tilde)
        '\u0323': 5,  # Nặng (dot below)
    }
    
    # Vietnamese diacritic marks (accent)
    ACCENT_MARKS = {
        '': 0,  # No accent
        '\u0306': 1,  # Breve (ă)
        '\u0302': 2,  # Circumflex (â, ê, ô)
        '\u031B': 3,  # Horn (ơ, ư)
    }
    
    # Vietnamese vowel to base mapping
    VOWEL_TO_BASE = {}
    
    # Initialize vowel to base mapping
    for base, variants in VOWEL_MAP.items():
        for variant in variants:
            VOWEL_TO_BASE[variant] = base
    
    def __init__(self):
        """Initialize Vietnamese diacritic processor."""
        logger.info("Initialized VietnameseDiacriticProcessor")
    
    @classmethod
    def get_instance(cls) -> 'VietnameseDiacriticProcessor':
        """
        Get singleton instance of VietnameseDiacriticProcessor.
        
        Returns:
            Singleton instance of VietnameseDiacriticProcessor
        """
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def remove_diacritics(self, text: str) -> str:
        """
        Remove all diacritics from Vietnamese text.
        
        Args:
            text: Vietnamese text
            
        Returns:
            Text with diacritics removed
        """
        if not text:
            return text
        
        # Normalize text to NFD form to separate diacritics
        text = unicodedata.normalize('NFD', text)
        
        # Remove diacritic marks
        text = re.sub(r'[\u0300-\u036f]', '', text)
        
        # Convert 'đ' to 'd'
        text = text.replace('đ', 'd').replace('Đ', 'D')
        
        return text
    
    def normalize_diacritics(self, text: str) -> str:
        """
        Normalize diacritics in Vietnamese text to ensure consistency.
        
        Args:
            text: Vietnamese text
            
        Returns:
            Text with normalized diacritics
        """
        if not text:
            return text
        
        # Normalize to NFC form (Vietnamese standard)
        text = unicodedata.normalize('NFC', text)
        
        return text
    
    def has_vietnamese_diacritics(self, text: str) -> bool:
        """
        Check if text contains Vietnamese diacritics.
        
        Args:
            text: Text to check
            
        Returns:
            True if text contains Vietnamese diacritics, False otherwise
        """
        if not text:
            return False
        
        # Normalize text
        text = unicodedata.normalize('NFD', text)
        
        # Check for diacritic marks
        return bool(re.search(r'[\u0300-\u036f]', text))
    
    def classify_diacritic_consistency(self, text: str) -> Dict[str, Any]:
        """
        Classify diacritic consistency in Vietnamese text.
        
        Args:
            text: Vietnamese text
            
        Returns:
            Dictionary with classification results
        """
        if not text or not detect_vietnamese(text):
            return {
                "has_diacritics": False,
                "consistency": 1.0,
                "missing_diacritics": 0,
                "total_vietnamese_words": 0,
                "percentage_with_diacritics": 0.0,
                "is_consistent": True
            }
        
        # Split text into words
        words = text.split()
        
        # Count words with and without diacritics
        vietnamese_chars = set('àáảãạăằắẳẵặâầấẩẫậèéẻẽẹêềếểễệìíỉĩịòóỏõọôồốổỗộơờớởỡợùúủũụưừứửữựỳýỷỹỵđ')
        
        words_with_diacritics = 0
        words_without_diacritics = 0
        
        for word in words:
            has_vietnamese_char = any(char.lower() in vietnamese_chars for char in word)
            
            if has_vietnamese_char:
                words_with_diacritics += 1
            elif re.match(r'^[a-zA-Z]+$', word):  # Only count Latin alphabet words
                words_without_diacritics += 1
        
        # Total Vietnamese words (with or without diacritics)
        total_vietnamese_words = words_with_diacritics + words_without_diacritics
        
        # Calculate consistency
        if total_vietnamese_words == 0:
            consistency = 1.0
        else:
            # If all words have diacritics or all words don't have diacritics, then consistent
            if words_with_diacritics == 0 or words_without_diacritics == 0:
                consistency = 1.0
            else:
                # Otherwise, calculate consistency as the max percentage
                consistency = max(words_with_diacritics, words_without_diacritics) / total_vietnamese_words
        
        # Calculate percentage of words with diacritics
        percentage_with_diacritics = (
            words_with_diacritics / total_vietnamese_words if total_vietnamese_words > 0 else 0.0
        )
        
        return {
            "has_diacritics": words_with_diacritics > 0,
            "consistency": consistency,
            "missing_diacritics": words_without_diacritics if words_with_diacritics > 0 else 0,
            "total_vietnamese_words": total_vietnamese_words,
            "percentage_with_diacritics": percentage_with_diacritics,
            "is_consistent": consistency > 0.95  # Consider >95% as consistent
        }
    
    def convert_telex_to_vietnamese(self, text: str) -> str:
        """
        Convert TELEX input to Vietnamese with proper diacritics.
        
        Args:
            text: TELEX formatted text
            
        Returns:
            Vietnamese text with proper diacritics
        """
        # TELEX rules
        # Tone marks: f (huyền), s (sắc), r (hỏi), x (ngã), j (nặng)
        # Accent marks: w (horn/breve), a (â), e (ê), o (ô)
        
        # Process TELEX input
        result = ""
        i = 0
        while i < len(text):
            if i + 1 < len(text) and text[i:i+2].lower() in ["aw", "ow", "uw"]:
                # Handle horn/breve
                base = text[i].lower()
                if base == "a":
                    result += "ă" if text[i].islower() else "Ă"
                elif base == "o":
                    result += "ơ" if text[i].islower() else "Ơ"
                elif base == "u":
                    result += "ư" if text[i].islower() else "Ư"
                i += 2
            elif i + 1 < len(text) and text[i].lower() in ["a", "e", "o"] and text[i+1].lower() == text[i].lower():
                # Handle circumflex
                base = text[i].lower()
                if base == "a":
                    result += "â" if text[i].islower() else "Â"
                elif base == "e":
                    result += "ê" if text[i].islower() else "Ê"
                elif base == "o":
                    result += "ô" if text[i].islower() else "Ô"
                i += 2
            elif i + 1 < len(text) and text[i+1].lower() in ["f", "s", "r", "x", "j"] and text[i].lower() in "aeiouy":
                # Handle tone marks
                vowel = text[i]
                tone = text[i+1].lower()
                
                if tone == "f":  # Huyền
                    if vowel.lower() == "a":
                        result += "à" if vowel.islower() else "À"
                    elif vowel.lower() == "e":
                        result += "è" if vowel.islower() else "È"
                    elif vowel.lower() == "i":
                        result += "ì" if vowel.islower() else "Ì"
                    elif vowel.lower() == "o":
                        result += "ò" if vowel.islower() else "Ò"
                    elif vowel.lower() == "u":
                        result += "ù" if vowel.islower() else "Ù"
                    elif vowel.lower() == "y":
                        result += "ỳ" if vowel.islower() else "Ỳ"
                elif tone == "s":  # Sắc
                    if vowel.lower() == "a":
                        result += "á" if vowel.islower() else "Á"
                    elif vowel.lower() == "e":
                        result += "é" if vowel.islower() else "É"
                    elif vowel.lower() == "i":
                        result += "í" if vowel.islower() else "Í"
                    elif vowel.lower() == "o":
                        result += "ó" if vowel.islower() else "Ó"
                    elif vowel.lower() == "u":
                        result += "ú" if vowel.islower() else "Ú"
                    elif vowel.lower() == "y":
                        result += "ý" if vowel.islower() else "Ý"
                elif tone == "r":  # Hỏi
                    if vowel.lower() == "a":
                        result += "ả" if vowel.islower() else "Ả"
                    elif vowel.lower() == "e":
                        result += "ẻ" if vowel.islower() else "Ẻ"
                    elif vowel.lower() == "i":
                        result += "ỉ" if vowel.islower() else "Ỉ"
                    elif vowel.lower() == "o":
                        result += "ỏ" if vowel.islower() else "Ỏ"
                    elif vowel.lower() == "u":
                        result += "ủ" if vowel.islower() else "Ủ"
                    elif vowel.lower() == "y":
                        result += "ỷ" if vowel.islower() else "Ỷ"
                elif tone == "x":  # Ngã
                    if vowel.lower() == "a":
                        result += "ã" if vowel.islower() else "Ã"
                    elif vowel.lower() == "e":
                        result += "ẽ" if vowel.islower() else "Ẽ"
                    elif vowel.lower() == "i":
                        result += "ĩ" if vowel.islower() else "Ĩ"
                    elif vowel.lower() == "o":
                        result += "õ" if vowel.islower() else "Õ"
                    elif vowel.lower() == "u":
                        result += "ũ" if vowel.islower() else "Ũ"
                    elif vowel.lower() == "y":
                        result += "ỹ" if vowel.islower() else "Ỹ"
                elif tone == "j":  # Nặng
                    if vowel.lower() == "a":
                        result += "ạ" if vowel.islower() else "Ạ"
                    elif vowel.lower() == "e":
                        result += "ẹ" if vowel.islower() else "Ẹ"
                    elif vowel.lower() == "i":
                        result += "ị" if vowel.islower() else "Ị"
                    elif vowel.lower() == "o":
                        result += "ọ" if vowel.islower() else "Ọ"
                    elif vowel.lower() == "u":
                        result += "ụ" if vowel.islower() else "Ụ"
                    elif vowel.lower() == "y":
                        result += "ỵ" if vowel.islower() else "Ỵ"
                
                i += 2
            elif i + 1 < len(text) and text[i].lower() == "d" and text[i+1].lower() == "d":
                # Handle đ
                result += "đ" if text[i].islower() else "Đ"
                i += 2
            else:
                # Keep character as is
                result += text[i]
                i += 1
        
        return result
    
    def get_diacritic_info(self, char: str) -> Dict[str, Any]:
        """
        Get diacritic information for a Vietnamese character.
        
        Args:
            char: Vietnamese character
            
        Returns:
            Dictionary with diacritic information
        """
        if not char or len(char) != 1:
            return {
                "base": char,
                "has_diacritics": False,
                "tone": None,
                "accent": None
            }
        
        # Normalize to NFD to separate base and diacritics
        char_nfd = unicodedata.normalize('NFD', char)
        
        # Get base character
        base = char_nfd[0]
        
        # Check if it's a Vietnamese character with diacritics
        if base.lower() not in 'aeiouy' and base.lower() != 'd':
            return {
                "base": base,
                "has_diacritics": False,
                "tone": None,
                "accent": None
            }
        
        # Get diacritic marks
        diacritics = char_nfd[1:] if len(char_nfd) > 1 else ''
        
        # Determine tone and accent
        tone = None
        accent = None
        
        for diacritic in diacritics:
            if diacritic in self.TONE_MARKS:
                tone = diacritic
            elif diacritic in self.ACCENT_MARKS:
                accent = diacritic
        
        return {
            "base": base,
            "has_diacritics": bool(diacritics),
            "tone": tone,
            "accent": accent,
            "tone_name": self._get_tone_name(tone),
            "accent_name": self._get_accent_name(accent)
        }
    
    def _get_tone_name(self, tone: Optional[str]) -> Optional[str]:
        """
        Get the name of a tone mark.
        
        Args:
            tone: Tone mark
            
        Returns:
            Name of the tone mark
        """
        if tone is None:
            return None
        
        tone_names = {
            '': 'ngang',
            '\u0300': 'huyền',
            '\u0301': 'sắc',
            '\u0309': 'hỏi',
            '\u0303': 'ngã',
            '\u0323': 'nặng'
        }
        
        return tone_names.get(tone)
    
    def _get_accent_name(self, accent: Optional[str]) -> Optional[str]:
        """
        Get the name of an accent mark.
        
        Args:
            accent: Accent mark
            
        Returns:
            Name of the accent mark
        """
        if accent is None:
            return None
        
        accent_names = {
            '': 'none',
            '\u0306': 'breve',
            '\u0302': 'circumflex',
            '\u031B': 'horn'
        }
        
        return accent_names.get(accent)
    
    def analyze_diacritics(self, text: str) -> Dict[str, Any]:
        """
        Analyze diacritics in Vietnamese text.
        
        Args:
            text: Vietnamese text
            
        Returns:
            Dictionary with analysis results
        """
        if not text or not detect_vietnamese(text):
            return {
                "has_diacritics": False,
                "consistency": 1.0,
                "diacritic_chars": 0,
                "total_chars": len(text),
                "percentage_with_diacritics": 0.0,
                "tone_distribution": {},
                "accent_distribution": {}
            }
        
        # Analyze diacritic consistency at word level
        consistency_result = self.classify_diacritic_consistency(text)
        
        # Count chars with diacritics
        diacritic_chars = 0
        total_vietnamese_chars = 0
        
        # Track tone and accent distribution
        tone_distribution = {}
        accent_distribution = {}
        
        for char in text:
            if char.lower() in 'aeiouyd':
                total_vietnamese_chars += 1
                
                # Check if it has diacritics
                info = self.get_diacritic_info(char)
                
                if info["has_diacritics"]:
                    diacritic_chars += 1
                
                # Update tone distribution
                tone_name = info["tone_name"] or "ngang"
                tone_distribution[tone_name] = tone_distribution.get(tone_name, 0) + 1
                
                # Update accent distribution
                accent_name = info["accent_name"] or "none"
                accent_distribution[accent_name] = accent_distribution.get(accent_name, 0) + 1
        
        # Calculate percentage of chars with diacritics
        percentage_with_diacritics = (
            diacritic_chars / total_vietnamese_chars if total_vietnamese_chars > 0 else 0.0
        )
        
        return {
            "has_diacritics": diacritic_chars > 0,
            "consistency": consistency_result["consistency"],
            "diacritic_chars": diacritic_chars,
            "total_vietnamese_chars": total_vietnamese_chars,
            "percentage_with_diacritics": percentage_with_diacritics,
            "tone_distribution": tone_distribution,
            "accent_distribution": accent_distribution,
            "word_level_analysis": consistency_result
        } 