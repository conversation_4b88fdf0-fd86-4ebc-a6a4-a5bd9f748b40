"""
Enhanced Vietnamese Embeddings.

This module provides enhanced Vietnamese embedding models and language-specific processing.
"""

import os
import torch
import numpy as np
import re
from typing import List, Dict, Any, Optional, Union, Tuple
from dataclasses import dataclass, field
from transformers import AutoTokenizer, AutoModel
from sentence_transformers import SentenceTransformer

from ..utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

class EnhancedVietnameseEmbeddings:
    """
    Mô hình embedding tiếng Việt nâng cao với hỗ trợ cho nhiều mô hình và xử lý ngôn ngữ đặc thù.
    """
    
    # Singleton instances
    _instances = {}
    
    # Available Vietnamese embedding models
    AVAILABLE_MODELS = {
        # Mô hình cơ bản
        "phobert": "vinai/phobert-base",
        "phobert-large": "vinai/phobert-large",
        "viebert": "FPTAI/viebert-base-cased",
        "xlm-roberta-vi": "xlm-roberta-base",
        "multilingual-e5": "intfloat/multilingual-e5-base",
        "multilingual-e5-large": "intfloat/multilingual-e5-large",
        "vietnamese-sbert": "keepitreal/vietnamese-sbert",

        # Mô hình mới
        "bkai-foundation-vi": "bkai-foundation-models/vietnamese-bi-encoder",
        "envibert": "nguyenvulebinh/envibert",
        "bartpho": "vinai/bartpho-syllable",
        "velectra": "vinai/velectra-base-discriminator-cased",
        "vibert4news": "FPTAI/vibert4news-base-cased",
        "vi-mrc": "nguyenvulebinh/vi-mrc-base",
        "vi-qa": "nguyenvulebinh/vi-qa-base",
        
        # Mô hình mới bổ sung
        "vit5": "VietAI/vit5-base",
        "vinallm": "vinai/vinallm-7b-chat",
        "vietnamese-llama": "bkai-foundation-models/vietnamese-llama2",
        "vietnamese-gpt": "NlpHUST/gpt-neo-vi-small",
        "vietnamese-roberta": "wonrax/phobert-base-vietnamese-sentiment"
    }
    
    # Model categories
    MODEL_CATEGORIES = {
        "general": ["phobert", "phobert-large", "viebert", "xlm-roberta-vi", "multilingual-e5", "multilingual-e5-large", "vietnamese-sbert"],
        "specialized": ["bkai-foundation-vi", "envibert", "bartpho", "velectra", "vibert4news"],
        "task_specific": ["vi-mrc", "vi-qa", "vietnamese-roberta"],
        "generative": ["vit5", "vinallm", "vietnamese-llama", "vietnamese-gpt"]
    }
    
    # Domain-specific recommendations
    DOMAIN_RECOMMENDATIONS = {
        "general": "phobert",
        "news": "vibert4news",
        "sentiment": "vietnamese-roberta",
        "qa": "vi-qa",
        "medical": "envibert",
        "legal": "viebert",
        "technical": "multilingual-e5",
        "educational": "phobert-large"
    }
    
    # Vietnamese stopwords
    VIETNAMESE_STOPWORDS = {
        "và", "của", "cho", "là", "để", "trong", "được", "với", "có", "không",
        "những", "này", "đó", "các", "một", "về", "như", "từ", "khi", "đến",
        "theo", "tại", "vì", "bởi", "nhưng", "mà", "nên", "thì", "trên", "dưới",
        "đã", "sẽ", "phải", "vẫn", "rằng", "tuy", "cũng", "nếu", "còn", "đang",
        "bị", "làm", "ra", "vào", "hay", "tới", "cùng", "sự", "việc", "lúc",
        "thế", "nào", "rồi", "lại", "vừa", "mới", "sau", "trước", "đều", "đây",
        "ai", "mình", "chúng", "họ", "tôi", "bạn", "anh", "chị", "em", "ông",
        "bà", "ta", "tớ", "cậu", "mày", "tao", "nó", "hắn", "thằng", "con",
        "người", "thời", "gian", "năm", "tháng", "ngày", "giờ", "phút", "giây",
        "lần", "nhiều", "ít", "rất", "quá", "gì", "thật", "chỉ", "đang", "nữa"
    }
    
    def __init__(
        self,
        model_name: str = "phobert",
        device: Optional[str] = None,
        cache_dir: Optional[str] = None,
        use_preprocessing: bool = True,
        use_stopword_removal: bool = False,
        max_length: int = 512
    ):
        """
        Khởi tạo EnhancedVietnameseEmbeddings.

        Args:
            model_name: Tên của mô hình embedding
            device: Thiết bị để sử dụng cho inference ("cpu", "cuda", "mps")
            cache_dir: Thư mục để cache mô hình
            use_preprocessing: Có sử dụng tiền xử lý tiếng Việt hay không
            use_stopword_removal: Có loại bỏ stopword hay không
            max_length: Độ dài tối đa của văn bản đầu vào
        """
        self.model_name = model_name
        self.cache_dir = cache_dir
        self.use_preprocessing = use_preprocessing
        self.use_stopword_removal = use_stopword_removal
        self.max_length = max_length

        # Determine device
        if device is None:
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
            if hasattr(torch, "has_mps") and torch.backends.mps.is_available():
                self.device = "mps"  # Apple Silicon GPU
        else:
            self.device = device

        # Get model path
        self.model_path = self.AVAILABLE_MODELS.get(model_name, model_name)

        # Initialize model
        self._initialize_model()
        
        logger.info(f"Initialized EnhancedVietnameseEmbeddings with model: {model_name} on device: {self.device}")
    
    def _initialize_model(self) -> None:
        """
        Khởi tạo mô hình embedding.
        """
        try:
            # Nhóm 1: Các mô hình sử dụng AutoTokenizer và AutoModel trực tiếp
            if self.model_name in [
                "phobert", "phobert-large", "viebert", "xlm-roberta-vi",
                "envibert", "bartpho", "velectra", "vibert4news", "vietnamese-roberta"
            ]:
                # Initialize tokenizer and model from Hugging Face
                self.tokenizer = AutoTokenizer.from_pretrained(
                    self.model_path,
                    cache_dir=self.cache_dir
                )
                self.model = AutoModel.from_pretrained(
                    self.model_path,
                    cache_dir=self.cache_dir
                ).to(self.device)

                # Set to evaluation mode
                self.model.eval()

                # Use sentence-transformers style
                self.use_sentence_transformers = False
                self.is_qa_model = False
                self.is_generative_model = False

            # Nhóm 2: Các mô hình chuyên biệt cho nhiệm vụ MRC và QA
            elif self.model_name in ["vi-mrc", "vi-qa"]:
                # Initialize tokenizer and model from Hugging Face
                self.tokenizer = AutoTokenizer.from_pretrained(
                    self.model_path,
                    cache_dir=self.cache_dir
                )
                self.model = AutoModel.from_pretrained(
                    self.model_path,
                    cache_dir=self.cache_dir
                ).to(self.device)

                # Set to evaluation mode
                self.model.eval()

                # Use sentence-transformers style with special handling
                self.use_sentence_transformers = False
                self.is_qa_model = True
                self.is_generative_model = False

            # Nhóm 3: Các mô hình sử dụng SentenceTransformer
            elif self.model_name in ["multilingual-e5", "multilingual-e5-large", "vietnamese-sbert", "bkai-foundation-vi"]:
                # Use sentence-transformers
                self.model = SentenceTransformer(
                    self.model_path,
                    cache_folder=self.cache_dir
                ).to(self.device)

                self.use_sentence_transformers = True
                self.is_qa_model = False
                self.is_generative_model = False
                
            # Nhóm 4: Các mô hình sinh (generative models)
            elif self.model_name in ["vit5", "vinallm", "vietnamese-llama", "vietnamese-gpt"]:
                # Initialize tokenizer and model from Hugging Face
                self.tokenizer = AutoTokenizer.from_pretrained(
                    self.model_path,
                    cache_dir=self.cache_dir
                )
                self.model = AutoModel.from_pretrained(
                    self.model_path,
                    cache_dir=self.cache_dir
                ).to(self.device)

                # Set to evaluation mode
                self.model.eval()

                # Use sentence-transformers style
                self.use_sentence_transformers = False
                self.is_qa_model = False
                self.is_generative_model = True
            
            else:
                # Default to SentenceTransformer for unknown models
                self.model = SentenceTransformer(
                    self.model_path,
                    cache_folder=self.cache_dir
                ).to(self.device)

                self.use_sentence_transformers = True
                self.is_qa_model = False
                self.is_generative_model = False
                
            # Get embedding dimension
            self.embedding_dim = self._get_embedding_dimension()
            
        except Exception as e:
            logger.error(f"Error initializing model {self.model_name}: {str(e)}")
            raise
    
    def _get_embedding_dimension(self) -> int:
        """
        Lấy kích thước của vector embedding.
        
        Returns:
            Kích thước của vector embedding
        """
        if self.use_sentence_transformers:
            return self.model.get_sentence_embedding_dimension()
        else:
            # For Hugging Face models, get the hidden size from the config
            return self.model.config.hidden_size
    
    def _mean_pooling(self, model_output, attention_mask):
        """
        Thực hiện mean pooling trên đầu ra của mô hình.
        
        Args:
            model_output: Đầu ra của mô hình
            attention_mask: Mask chú ý
            
        Returns:
            Vector embedding đã được pooling
        """
        # Get token embeddings
        token_embeddings = model_output[0]
        
        # Expand attention mask to match token embeddings
        input_mask_expanded = attention_mask.unsqueeze(-1).expand(token_embeddings.size()).float()
        
        # Sum token embeddings and divide by attention mask sum
        sum_embeddings = torch.sum(token_embeddings * input_mask_expanded, 1)
        sum_mask = torch.clamp(input_mask_expanded.sum(1), min=1e-9)
        
        # Return mean pooled embeddings
        return sum_embeddings / sum_mask
    
    def preprocess_vietnamese_text(self, text: str) -> str:
        """
        Tiền xử lý văn bản tiếng Việt.
        
        Args:
            text: Văn bản cần tiền xử lý
            
        Returns:
            Văn bản đã được tiền xử lý
        """
        if not self.use_preprocessing:
            return text
        
        # Chuẩn hóa khoảng trắng
        text = re.sub(r'\s+', ' ', text)
        
        # Loại bỏ các ký tự đặc biệt
        text = re.sub(r'[^\w\s\d,.?!;:()[\]{}""''""…–—-]', '', text)
        
        # Loại bỏ stopwords nếu được yêu cầu
        if self.use_stopword_removal:
            words = text.split()
            words = [word for word in words if word.lower() not in self.VIETNAMESE_STOPWORDS]
            text = ' '.join(words)
        
        return text.strip()
    
    def get_embeddings(self, texts: List[str]) -> np.ndarray:
        """
        Lấy vector embedding cho danh sách văn bản.
        
        Args:
            texts: Danh sách văn bản cần embedding
            
        Returns:
            Ma trận các vector embedding
        """
        if not texts:
            return np.array([])
        
        # Tiền xử lý văn bản
        if self.use_preprocessing:
            texts = [self.preprocess_vietnamese_text(text) for text in texts]
        
        try:
            if self.use_sentence_transformers:
                # Use sentence-transformers directly
                embeddings = self.model.encode(
                    texts,
                    convert_to_numpy=True,
                    show_progress_bar=False
                )
            else:
                # Process with Hugging Face models
                with torch.no_grad():
                    # Tokenize texts
                    encoded_input = self.tokenizer(
                        texts,
                        padding=True,
                        truncation=True,
                        max_length=self.max_length,
                        return_tensors="pt"
                    ).to(self.device)

                    # Get model output
                    model_output = self.model(**encoded_input)

                    # Perform mean pooling
                    embeddings = self._mean_pooling(
                        model_output,
                        encoded_input["attention_mask"]
                    )

                    # Convert to numpy
                    embeddings = embeddings.cpu().numpy()

            # Normalize embeddings
            embeddings = embeddings / np.linalg.norm(embeddings, axis=1, keepdims=True)

            return embeddings
        except Exception as e:
            logger.error(f"Error getting embeddings: {str(e)}")
            # Return zero embeddings as fallback
            return np.zeros((len(texts), self.embedding_dim))
    
    def get_embedding_dimension(self) -> int:
        """
        Lấy kích thước của vector embedding.
        
        Returns:
            Kích thước của vector embedding
        """
        return self.embedding_dim
    
    def find_most_similar(
        self,
        query: str,
        candidates: List[str],
        top_k: int = 5
    ) -> List[Tuple[int, float]]:
        """
        Tìm các văn bản tương tự nhất với truy vấn.
        
        Args:
            query: Truy vấn
            candidates: Danh sách văn bản ứng viên
            top_k: Số lượng kết quả trả về
            
        Returns:
            Danh sách các cặp (chỉ số, điểm tương đồng)
        """
        if not candidates:
            return []
        
        # Lấy embedding cho truy vấn
        query_embedding = self.get_embeddings([query])[0]
        
        # Lấy embedding cho các ứng viên
        candidate_embeddings = self.get_embeddings(candidates)
        
        # Tính toán độ tương đồng cosine
        similarities = np.dot(candidate_embeddings, query_embedding)
        
        # Lấy top_k kết quả
        top_indices = np.argsort(similarities)[::-1][:top_k]
        
        # Trả về các cặp (chỉ số, điểm tương đồng)
        return [(int(idx), float(similarities[idx])) for idx in top_indices]
    
    def compare_models(
        self,
        texts: List[str],
        models: List[str] = None
    ) -> Dict[str, Dict[str, float]]:
        """
        So sánh hiệu suất của các mô hình embedding khác nhau.
        
        Args:
            texts: Danh sách văn bản để so sánh
            models: Danh sách tên mô hình để so sánh (nếu None, sẽ sử dụng tất cả)
            
        Returns:
            Dict chứa kết quả so sánh
        """
        if models is None:
            models = list(self.AVAILABLE_MODELS.keys())
        
        results = {}
        current_model = self.model_name
        
        for model_name in models:
            try:
                # Khởi tạo mô hình
                model = EnhancedVietnameseEmbeddingFactory.get_embedding_model(
                    model_name=model_name,
                    device=self.device,
                    cache_dir=self.cache_dir
                )
                
                # Đo thời gian lấy embedding
                start_time = time.time()
                embeddings = model.get_embeddings(texts)
                elapsed_time = time.time() - start_time
                
                # Tính toán kích thước trung bình của embedding
                avg_size = embeddings.nbytes / len(texts) if len(texts) > 0 else 0
                
                # Lưu kết quả
                results[model_name] = {
                    "time": elapsed_time,
                    "avg_size": avg_size,
                    "dimension": model.get_embedding_dimension()
                }
            except Exception as e:
                logger.error(f"Error comparing model {model_name}: {str(e)}")
                results[model_name] = {
                    "error": str(e)
                }
        
        # Khôi phục mô hình ban đầu
        if current_model != self.model_name:
            self.model_name = current_model
            self._initialize_model()
        
        return results
    
    @classmethod
    def get_embedding_model(
        cls,
        model_name: str = "phobert",
        device: Optional[str] = None,
        cache_dir: Optional[str] = None,
        use_preprocessing: bool = True,
        use_stopword_removal: bool = False,
        max_length: int = 512
    ) -> "EnhancedVietnameseEmbeddings":
        """
        Lấy một mô hình embedding tiếng Việt.

        Args:
            model_name: Tên của mô hình embedding
            device: Thiết bị để sử dụng cho inference ("cpu", "cuda", "mps")
            cache_dir: Thư mục để cache mô hình
            use_preprocessing: Có sử dụng tiền xử lý tiếng Việt hay không
            use_stopword_removal: Có loại bỏ stopword hay không
            max_length: Độ dài tối đa của văn bản đầu vào

        Returns:
            Mô hình embedding tiếng Việt
        """
        # Create a unique key for this configuration
        key = f"{model_name}_{device}_{cache_dir}_{use_preprocessing}_{use_stopword_removal}_{max_length}"

        # Return existing instance if available
        if key in cls._instances:
            return cls._instances[key]

        # Create new instance
        embedding_model = cls(
            model_name=model_name,
            device=device,
            cache_dir=cache_dir,
            use_preprocessing=use_preprocessing,
            use_stopword_removal=use_stopword_removal,
            max_length=max_length
        )

        # Store instance
        cls._instances[key] = embedding_model

        return embedding_model
    
    @classmethod
    def recommend_model_for_domain(cls, domain: str) -> str:
        """
        Đề xuất mô hình embedding phù hợp nhất cho một lĩnh vực cụ thể.
        
        Args:
            domain: Lĩnh vực cần đề xuất mô hình
            
        Returns:
            Tên của mô hình được đề xuất
        """
        return cls.DOMAIN_RECOMMENDATIONS.get(domain, "phobert")
    
    @classmethod
    def list_available_models(cls) -> Dict[str, Dict[str, Any]]:
        """
        Liệt kê các mô hình embedding có sẵn.
        
        Returns:
            Dict chứa thông tin về các mô hình có sẵn
        """
        models = {}
        for model_name, model_path in cls.AVAILABLE_MODELS.items():
            # Xác định danh mục
            category = "unknown"
            for cat, models_list in cls.MODEL_CATEGORIES.items():
                if model_name in models_list:
                    category = cat
                    break
            
            # Xác định các lĩnh vực được đề xuất
            recommended_domains = []
            for domain, recommended_model in cls.DOMAIN_RECOMMENDATIONS.items():
                if recommended_model == model_name:
                    recommended_domains.append(domain)
            
            models[model_name] = {
                "path": model_path,
                "category": category,
                "recommended_domains": recommended_domains
            }
        
        return models


# Alias for backward compatibility
EnhancedVietnameseEmbeddingFactory = EnhancedVietnameseEmbeddings
