"""
Vietnamese dialect processor module.

This module provides utilities for processing and normalizing different Vietnamese dialects.
"""

from typing import Dict, List, Set

from ..utils.structured_logging import get_logger
from ..utils.vietnamese_utils import normalize_vietnamese_text, detect_vietnamese

logger = get_logger(__name__)

class VietnameseDialectProcessor:
    """
    Vietnamese dialect processor for handling regional language variations.

    This class provides methods to detect, normalize, and convert between
    different Vietnamese dialects (Northern, Central, Southern).
    """

    # Singleton instances
    _instances = {}

    # Dialect markers
    NORTHERN_MARKERS = {
        # Pronouns
        "tao": True, "mày": True, "hắn": True, "chúng mày": True, "chúng nó": True,
        # Vocabulary variations
        "thìa": True, "bát": True, "quần": True, "cái": True,
        "ô tô": True, "tất": True, "đũa": True, "cắp": True,
        "gì": True, "thế": True, "vậy": True, "đấy": True, "này": True,
        "cần": True, "cần phải": True, "bị": True, "bị cảm": True, "bị ốm": True,
        # Phrases
        "đi thôi": True, "thế nhé": True, "đâu phải": True, "thế à": True,
        "có phải không": True, "có đúng không": True, "thế nào": True
    }

    CENTRAL_MARKERS = {
        # Pronouns
        "tau": True, "mi": True, "hắn": True, "bay": True, "bầy": True, "tụi bay": True,
        # Vocabulary variations
        "chén": True, "quần": True, "đồ": True, "chi": True,
        "mô": True, "nớ": True, "ni": True, "răng": True, "rứa": True,
        "mồ": True, "mồ tê": True, "mồ tê rồi": True,
        # Phrases
        "đi đi": True, "răng rứa": True, "chi rứa": True, "mô tê": True,
        "rứa đó": True, "bầy chớ": True, "bầy rứa": True, "rứa hé": True
    }

    SOUTHERN_MARKERS = {
        # Pronouns
        "tui": True, "bạn": True, "nó": True, "tao": True, "mày": True, "tụi nó": True,
        # Vocabulary variations
        "muỗng": True, "chén": True, "quần": True, "cái": True,
        "xe hơi": True, "vớ": True, "đủa": True, "xách": True,
        "gì": True, "vậy": True, "đó": True, "nè": True, "dzậy": True,
        "hông": True, "hông phải": True, "hổng": True, "hổng có": True,
        # Phrases
        "đi đi": True, "vậy nha": True, "đâu có": True, "vậy đó": True,
        "phải hông": True, "đúng hông": True, "làm sao": True
    }

    # Conversion dictionaries
    NORTHERN_TO_SOUTHERN = {
        "tao": "tui",
        "mày": "bạn",
        "thìa": "muỗng",
        "bát": "chén",
        "ô tô": "xe hơi",
        "tất": "vớ",
        "cắp": "xách",
        "thế": "vậy",
        "này": "nè",
        "đấy": "đó",
        "thế nhé": "vậy nha",
        "đâu phải": "đâu có"
    }

    SOUTHERN_TO_NORTHERN = {
        "tui": "tao",
        "bạn": "mày",
        "muỗng": "thìa",
        "chén": "bát",
        "xe hơi": "ô tô",
        "vớ": "tất",
        "xách": "cắp",
        "vậy": "thế",
        "nè": "này",
        "đó": "đấy",
        "vậy nha": "thế nhé",
        "đâu có": "đâu phải"
    }

    CENTRAL_TO_NORTHERN = {
        "tau": "tao",
        "mi": "mày",
        "chén": "bát",
        "chi": "gì",
        "mô": "đâu",
        "ni": "này",
        "nớ": "đấy",
        "răng": "sao",
        "rứa": "thế",
        "chi rứa": "gì thế",
        "mô tê": "đâu rồi"
    }

    def __init__(self):
        """Initialize VietnameseDialectProcessor."""
        logger.info("Initializing VietnameseDialectProcessor")

    @classmethod
    def get_instance(cls) -> 'VietnameseDialectProcessor':
        """
        Get singleton instance of VietnameseDialectProcessor.

        Returns:
            Instance of VietnameseDialectProcessor
        """
        if cls not in cls._instances:
            cls._instances[cls] = cls()
        return cls._instances[cls]

    def detect_dialect(self, text: str) -> Dict[str, float]:
        """
        Detect the dialect of a Vietnamese text.

        Args:
            text: Vietnamese text to analyze

        Returns:
            Dictionary with dialect probabilities
        """
        if not detect_vietnamese(text):
            return {"northern": 0.33, "central": 0.33, "southern": 0.33}

        # Normalize the text
        text = normalize_vietnamese_text(text)

        # Split into words
        words = text.split()

        # Initialize counters
        northern_count = 0
        central_count = 0
        southern_count = 0
        total_dialect_markers = 0

        # Count dialect markers
        for word in words:
            if word in self.NORTHERN_MARKERS:
                northern_count += 1
                total_dialect_markers += 1

            if word in self.CENTRAL_MARKERS:
                central_count += 1
                total_dialect_markers += 1

            if word in self.SOUTHERN_MARKERS:
                southern_count += 1
                total_dialect_markers += 1

        # Check for two-word phrases
        for i in range(len(words) - 1):
            phrase = f"{words[i]} {words[i+1]}"

            if phrase in self.NORTHERN_MARKERS:
                northern_count += 1
                total_dialect_markers += 1

            if phrase in self.CENTRAL_MARKERS:
                central_count += 1
                total_dialect_markers += 1

            if phrase in self.SOUTHERN_MARKERS:
                southern_count += 1
                total_dialect_markers += 1

        # If no dialect markers found, return equal probabilities
        if total_dialect_markers == 0:
            return {"northern": 0.33, "central": 0.33, "southern": 0.33}

        # Calculate probabilities
        northern_prob = northern_count / total_dialect_markers
        central_prob = central_count / total_dialect_markers
        southern_prob = southern_count / total_dialect_markers

        return {
            "northern": northern_prob,
            "central": central_prob,
            "southern": southern_prob
        }

    def normalize_to_dialect(self, text: str, target_dialect: str = "northern") -> str:
        """
        Normalize Vietnamese text to a specific dialect.

        Args:
            text: Vietnamese text to normalize
            target_dialect: Target dialect (northern, central, southern)

        Returns:
            Normalized text in the target dialect
        """
        if not detect_vietnamese(text):
            return text

        # Normalize the text
        text = normalize_vietnamese_text(text)

        # Detect source dialect
        dialect_probs = self.detect_dialect(text)
        source_dialect = max(dialect_probs, key=dialect_probs.get)

        # If already in target dialect or cannot determine, return as is
        if source_dialect == target_dialect or dialect_probs[source_dialect] < 0.4:
            return text

        # Split into words
        words = text.split()
        normalized_words = []

        # Convert individual words
        i = 0
        while i < len(words):
            # Check for two-word phrases
            if i < len(words) - 1:
                phrase = f"{words[i]} {words[i+1]}"
                converted = False

                if source_dialect == "northern" and target_dialect == "southern":
                    if phrase in self.NORTHERN_TO_SOUTHERN:
                        normalized_words.append(self.NORTHERN_TO_SOUTHERN[phrase])
                        converted = True
                        i += 2

                elif source_dialect == "southern" and target_dialect == "northern":
                    if phrase in self.SOUTHERN_TO_NORTHERN:
                        normalized_words.append(self.SOUTHERN_TO_NORTHERN[phrase])
                        converted = True
                        i += 2

                elif source_dialect == "central" and target_dialect == "northern":
                    if phrase in self.CENTRAL_TO_NORTHERN:
                        normalized_words.append(self.CENTRAL_TO_NORTHERN[phrase])
                        converted = True
                        i += 2

                if converted:
                    continue

            # Convert single words
            word = words[i]

            if source_dialect == "northern" and target_dialect == "southern":
                if word in self.NORTHERN_TO_SOUTHERN:
                    normalized_words.append(self.NORTHERN_TO_SOUTHERN[word])
                else:
                    normalized_words.append(word)

            elif source_dialect == "southern" and target_dialect == "northern":
                if word in self.SOUTHERN_TO_NORTHERN:
                    normalized_words.append(self.SOUTHERN_TO_NORTHERN[word])
                else:
                    normalized_words.append(word)

            elif source_dialect == "central" and target_dialect == "northern":
                if word in self.CENTRAL_TO_NORTHERN:
                    normalized_words.append(self.CENTRAL_TO_NORTHERN[word])
                else:
                    normalized_words.append(word)

            else:
                normalized_words.append(word)

            i += 1

        # Join words back into text
        normalized_text = " ".join(normalized_words)

        return normalized_text

    def get_dialect_markers(self, dialect: str) -> Set[str]:
        """
        Get the set of dialect markers for a specific dialect.

        Args:
            dialect: Dialect (northern, central, southern)

        Returns:
            Set of dialect markers
        """
        if dialect == "northern":
            return set(self.NORTHERN_MARKERS.keys())
        if dialect == "central":
            return set(self.CENTRAL_MARKERS.keys())
        if dialect == "southern":
            return set(self.SOUTHERN_MARKERS.keys())
        return set()

    def extract_dialect_markers(self, text: str) -> Dict[str, List[str]]:
        """
        Extract dialect markers from text.

        Args:
            text: Vietnamese text to analyze

        Returns:
            Dictionary with dialect markers
        """
        if not detect_vietnamese(text):
            return {"northern": [], "central": [], "southern": []}

        # Normalize the text
        text = normalize_vietnamese_text(text)

        # Split into words
        words = text.split()

        northern_markers = []
        central_markers = []
        southern_markers = []

        # Check single words
        for word in words:
            if word in self.NORTHERN_MARKERS:
                northern_markers.append(word)

            if word in self.CENTRAL_MARKERS:
                central_markers.append(word)

            if word in self.SOUTHERN_MARKERS:
                southern_markers.append(word)

        # Check for two-word phrases
        for i in range(len(words) - 1):
            phrase = f"{words[i]} {words[i+1]}"

            if phrase in self.NORTHERN_MARKERS:
                northern_markers.append(phrase)

            if phrase in self.CENTRAL_MARKERS:
                central_markers.append(phrase)

            if phrase in self.SOUTHERN_MARKERS:
                southern_markers.append(phrase)

        return {
            "northern": northern_markers,
            "central": central_markers,
            "southern": southern_markers
        }
