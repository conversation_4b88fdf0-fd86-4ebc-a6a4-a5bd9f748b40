"""
Integration of abbreviation detection with reasoning modules (CoT and ToT).

This module provides functionality to integrate abbreviation detection and expansion
with Chain of Thought (CoT) and Tree of Thought (ToT) reasoning modules to improve
reasoning quality and clarity.
"""

import re
import logging
from typing import List, Dict, Any, Tuple, Optional, Union, Set
from functools import lru_cache

# Create logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Try to import abbreviation processors
try:
    from .abbreviation_processor import AbbreviationProcessor
    ABBREVIATION_PROCESSOR_AVAILABLE = True
except ImportError:
    ABBREVIATION_PROCESSOR_AVAILABLE = False
    logger.warning("AbbreviationProcessor is not available. Basic functionality will be limited.")

try:
    from .abbreviation_ml_detector import AbbreviationMLDetector
    ABBREVIATION_ML_DETECTOR_AVAILABLE = True
except ImportError:
    ABBREVIATION_ML_DETECTOR_AVAILABLE = False
    logger.warning("AbbreviationMLDetector is not available. ML-based detection will be disabled.")

try:
    from .abbreviation_ml_advanced import AdvancedAbbreviationMLDetector
    ADVANCED_ML_DETECTOR_AVAILABLE = True
except ImportError:
    ADVANCED_ML_DETECTOR_AVAILABLE = False
    logger.warning("AdvancedAbbreviationMLDetector is not available. Advanced ML models will be disabled.")

# Try to import multilingual utils
try:
    from ..utils.multilingual_utils import detect_language
    MULTILINGUAL_UTILS_AVAILABLE = True
except ImportError:
    MULTILINGUAL_UTILS_AVAILABLE = False
    logger.warning("multilingual_utils is not available. Language detection will be limited.")

    # Define a simple fallback
    def detect_language(text: str) -> str:
        """Simple fallback for language detection."""
        # Check for Vietnamese characters
        if any(c in text for c in "áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđ"):
            return "vi"
        return "en"

class AbbreviationReasoningEnhancer:
    """
    Enhances reasoning modules with abbreviation detection and expansion.

    This class provides methods to enhance Chain of Thought (CoT) and
    Tree of Thought (ToT) reasoning with abbreviation detection and expansion.
    """

    # Singleton instance
    _instance = None

    @classmethod
    def get_instance(cls, **kwargs) -> 'AbbreviationReasoningEnhancer':
        """
        Get singleton instance of AbbreviationReasoningEnhancer.

        Args:
            **kwargs: Additional parameters for initialization

        Returns:
            Instance of AbbreviationReasoningEnhancer
        """
        if cls._instance is None:
            cls._instance = cls(**kwargs)
        return cls._instance

    def __init__(
        self,
        use_ml: bool = True,
        use_advanced_ml: bool = True,
        ml_model_type: str = "transformer",
        enable_prompt_enhancement: bool = True,
        enable_thought_enhancement: bool = True,
        enable_explanation: bool = True,
        cache_size: int = 1024
    ):
        """
        Initialize the AbbreviationReasoningEnhancer.

        Args:
            use_ml: Whether to use ML-based detection
            use_advanced_ml: Whether to use advanced ML models
            ml_model_type: Type of ML model to use
            enable_prompt_enhancement: Whether to enhance prompts
            enable_thought_enhancement: Whether to enhance thoughts
            enable_explanation: Whether to add explanations for abbreviations
            cache_size: Size of the cache for processed text
        """
        self.use_ml = use_ml
        self.use_advanced_ml = use_advanced_ml
        self.ml_model_type = ml_model_type
        self.enable_prompt_enhancement = enable_prompt_enhancement
        self.enable_thought_enhancement = enable_thought_enhancement
        self.enable_explanation = enable_explanation
        self.cache_size = cache_size

        # Initialize abbreviation processors
        self._initialize_processors()

    def _initialize_processors(self):
        """Initialize abbreviation processors based on availability."""
        self.abbreviation_processor = None
        self.ml_detector = None
        self.advanced_ml_detector = None

        # Initialize rule-based processor
        if ABBREVIATION_PROCESSOR_AVAILABLE:
            self.abbreviation_processor = AbbreviationProcessor.get_instance()

        # Initialize ML detector
        if self.use_ml and ABBREVIATION_ML_DETECTOR_AVAILABLE:
            self.ml_detector = AbbreviationMLDetector.get_instance(
                model_type="naive_bayes" if self.ml_model_type == "naive_bayes" else "lstm"
            )

        # Initialize advanced ML detector
        if self.use_ml and self.use_advanced_ml and ADVANCED_ML_DETECTOR_AVAILABLE:
            self.advanced_ml_detector = AdvancedAbbreviationMLDetector.get_instance(
                model_type=self.ml_model_type,
                language="en"  # Will be updated per query
            )

    @lru_cache(maxsize=1024)
    def enhance_prompt(self, prompt: str, language: Optional[str] = None) -> Dict[str, Any]:
        """
        Enhance a reasoning prompt with abbreviation detection and expansion.

        Args:
            prompt: Original prompt
            language: Language code (if None, will be detected)

        Returns:
            Dictionary with enhanced prompt information
        """
        if not self.enable_prompt_enhancement:
            return {
                "original_prompt": prompt,
                "enhanced_prompt": prompt,
                "abbreviations": [],
                "expansions": {}
            }

        # Detect language if not provided
        if language is None and MULTILINGUAL_UTILS_AVAILABLE:
            language = detect_language(prompt)
        elif language is None:
            language = "en"

        # Detect abbreviations
        abbreviations = self._detect_abbreviations(prompt, language)

        # Expand abbreviations
        expansions = self._expand_abbreviations(abbreviations, language, context=prompt)

        # Create enhanced prompt
        enhanced_prompt = self._create_enhanced_prompt(prompt, abbreviations, expansions)

        return {
            "original_prompt": prompt,
            "enhanced_prompt": enhanced_prompt,
            "abbreviations": abbreviations,
            "expansions": expansions,
            "language": language
        }

    def enhance_thought(self, thought: str, prompt_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhance a reasoning thought with abbreviation detection and expansion.

        Args:
            thought: Original thought
            prompt_info: Prompt information from enhance_prompt

        Returns:
            Dictionary with enhanced thought information
        """
        if not self.enable_thought_enhancement:
            return {
                "original_thought": thought,
                "enhanced_thought": thought,
                "abbreviations": [],
                "expansions": {}
            }

        language = prompt_info.get("language", "en")

        # Detect abbreviations
        abbreviations = self._detect_abbreviations(thought, language)

        # Expand abbreviations
        expansions = self._expand_abbreviations(abbreviations, language, context=thought)

        # Add abbreviations from prompt if they appear in the thought
        prompt_abbreviations = prompt_info.get("abbreviations", [])
        prompt_expansions = prompt_info.get("expansions", {})

        for abbr, _, _, _ in prompt_abbreviations:
            if abbr in prompt_expansions and abbr in thought:
                # Check if this abbreviation is already detected
                if not any(a == abbr for a, _, _, _ in abbreviations):
                    # Find position in thought
                    for match in re.finditer(r'\b' + re.escape(abbr) + r'\b', thought):
                        abbreviations.append((abbr, match.start(), match.end(), 0.9))
                        expansions[abbr] = prompt_expansions[abbr]

        # Create enhanced thought
        enhanced_thought = self._create_enhanced_thought(thought, abbreviations, expansions)

        return {
            "original_thought": thought,
            "enhanced_thought": enhanced_thought,
            "abbreviations": abbreviations,
            "expansions": expansions
        }

    def enhance_tot_node(self, node: Dict[str, Any], prompt_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhance a Tree of Thought (ToT) node with abbreviation detection and expansion.

        Args:
            node: Original ToT node
            prompt_info: Prompt information from enhance_prompt

        Returns:
            Enhanced ToT node
        """
        # Copy the node
        enhanced_node = node.copy()

        # Enhance the thought
        thought = node.get("thought", "")
        if thought:
            thought_info = self.enhance_thought(thought, prompt_info)
            enhanced_node["thought"] = thought_info["enhanced_thought"]
            enhanced_node["abbreviation_info"] = {
                "abbreviations": thought_info["abbreviations"],
                "expansions": thought_info["expansions"]
            }

        # Enhance children recursively
        children = node.get("children", [])
        if children:
            enhanced_children = []
            for child in children:
                enhanced_child = self.enhance_tot_node(child, prompt_info)
                enhanced_children.append(enhanced_child)
            enhanced_node["children"] = enhanced_children

        return enhanced_node

    def _detect_abbreviations(self, text: str, language: str) -> List[Tuple[str, int, int, float]]:
        """
        Detect abbreviations in text.

        Args:
            text: Text to analyze
            language: Language code

        Returns:
            List of tuples containing (abbreviation, start_idx, end_idx, confidence)
        """
        abbreviations = []

        # Try advanced ML detector first
        if self.use_ml and self.use_advanced_ml and self.advanced_ml_detector:
            try:
                # Update language
                detector = AdvancedAbbreviationMLDetector.get_instance(
                    model_type=self.ml_model_type,
                    language=language
                )

                # Detect abbreviations
                abbreviations = detector.find_abbreviations_in_text(text)

                if abbreviations:
                    return abbreviations
            except Exception as e:
                logger.warning(f"Error in advanced ML abbreviation detection: {str(e)}")

        # Try ML detector
        if self.use_ml and self.ml_detector:
            try:
                abbreviations = self.ml_detector.find_abbreviations_in_text(text)

                if abbreviations:
                    return abbreviations
            except Exception as e:
                logger.warning(f"Error in ML abbreviation detection: {str(e)}")

        # Fall back to rule-based detection
        if self.abbreviation_processor:
            try:
                rule_based_abbreviations = self.abbreviation_processor.detect_abbreviations(
                    text=text,
                    language=language
                )

                # Convert to the expected format
                abbreviations = [(abbr, start, end, 0.7) for abbr, start, end in rule_based_abbreviations]
            except Exception as e:
                logger.warning(f"Error in rule-based abbreviation detection: {str(e)}")

        return abbreviations

    def _expand_abbreviations(
        self,
        abbreviations: List[Tuple[str, int, int, float]],
        language: str,
        context: Optional[str] = None
    ) -> Dict[str, str]:
        """
        Expand detected abbreviations.

        Args:
            abbreviations: List of detected abbreviations
            language: Language code
            context: Context text for better expansion

        Returns:
            Dictionary mapping abbreviations to their expansions
        """
        expansions = {}

        if not abbreviations or not self.abbreviation_processor:
            return expansions

        for abbr, _, _, _ in abbreviations:
            try:
                expansion = self.abbreviation_processor.expand_abbreviation(
                    abbreviation=abbr,
                    language=language,
                    context=context
                )

                if expansion:
                    expansions[abbr] = expansion
            except Exception as e:
                logger.warning(f"Error expanding abbreviation {abbr}: {str(e)}")

        return expansions

    def _create_enhanced_prompt(
        self,
        prompt: str,
        abbreviations: List[Tuple[str, int, int, float]],
        expansions: Dict[str, str]
    ) -> str:
        """
        Create enhanced prompt with abbreviation explanations.

        Args:
            prompt: Original prompt
            abbreviations: Detected abbreviations
            expansions: Abbreviation expansions

        Returns:
            Enhanced prompt with abbreviation explanations
        """
        if not self.enable_explanation or not abbreviations or not expansions:
            return prompt

        # Add abbreviation explanations
        enhanced_prompt = prompt

        # Add explanations section at the end
        if abbreviations and expansions:
            explanation_section = "\n\nAbbreviations in this prompt:\n"
            for abbr, _, _, _ in abbreviations:
                if abbr in expansions:
                    explanation_section += f"- {abbr}: {expansions[abbr]}\n"

            enhanced_prompt += explanation_section

        return enhanced_prompt

    def _create_enhanced_thought(
        self,
        thought: str,
        abbreviations: List[Tuple[str, int, int, float]],
        expansions: Dict[str, str]
    ) -> str:
        """
        Create enhanced thought with abbreviation explanations.

        Args:
            thought: Original thought
            abbreviations: Detected abbreviations
            expansions: Abbreviation expansions

        Returns:
            Enhanced thought with abbreviation explanations
        """
        if not self.enable_explanation or not abbreviations or not expansions:
            return thought

        # Add inline explanations for first occurrence of each abbreviation
        enhanced_thought = thought
        offset = 0

        # Track which abbreviations have been explained
        explained_abbrs = set()

        # Sort abbreviations by position
        sorted_abbrs = sorted(abbreviations, key=lambda x: x[1])

        for abbr, start, end, _ in sorted_abbrs:
            if abbr in expansions and abbr not in explained_abbrs:
                # Adjust positions based on previous insertions
                adjusted_start = start + offset
                adjusted_end = end + offset

                # Add explanation
                explanation = f" ({expansions[abbr]})"
                enhanced_thought = enhanced_thought[:adjusted_end] + explanation + enhanced_thought[adjusted_end:]

                # Update offset
                offset += len(explanation)

                # Mark as explained
                explained_abbrs.add(abbr)

        return enhanced_thought
