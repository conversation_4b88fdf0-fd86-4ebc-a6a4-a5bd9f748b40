"""
Advanced machine learning models for abbreviation detection.

This module provides advanced machine learning models for detecting abbreviations
in text, including LSTM and Transformer-based models.
"""

import os
import re
import json
import logging
import numpy as np
from typing import List, Dict, Tuple, Optional, Any, Union, Set
from collections import defaultdict, Counter

# Create logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Try to import optional dependencies
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import Dataset, DataLoader
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    logger.warning("PyTorch is not available. LSTM and Transformer models will be disabled.")

try:
    from sklearn.feature_extraction.text import CountVectorizer, TfidfVectorizer
    from sklearn.naive_bayes import MultinomialNB
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import accuracy_score, precision_recall_fscore_support
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logger.warning("scikit-learn is not available. ML-based detection will be limited.")

try:
    from transformers import AutoTokenizer, AutoModelForSequenceClassification, AutoModelForTokenClassification
    from transformers import Trainer, TrainingArguments
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    logger.warning("transformers library is not available. Transformer models will be disabled.")


class AbbreviationMLDetector:
    """
    Advanced machine learning detector for abbreviations.

    This class provides methods for detecting abbreviations using various
    machine learning models, including Naive Bayes, LSTM, and Transformer-based models.
    """

    # Singleton instance
    _instances = {}

    @classmethod
    def get_instance(cls, model_type: str = "naive_bayes", **kwargs) -> 'AbbreviationMLDetector':
        """
        Get singleton instance of AbbreviationMLDetector.

        Args:
            model_type: Type of model to use ("naive_bayes", "lstm", "transformer")
            **kwargs: Additional parameters for the model

        Returns:
            Instance of AbbreviationMLDetector
        """
        if model_type not in cls._instances:
            cls._instances[model_type] = cls(model_type=model_type, **kwargs)
        return cls._instances[model_type]

    def __init__(
        self,
        model_type: str = "naive_bayes",
        model_path: Optional[str] = None,
        pretrained_model: Optional[str] = None,
        enable_learning: bool = True,
        cache_path: Optional[str] = None
    ):
        """
        Initialize the AbbreviationMLDetector.

        Args:
            model_type: Type of model to use ("naive_bayes", "lstm", "transformer")
            model_path: Path to save/load model
            pretrained_model: Name of pretrained model (for transformer)
            enable_learning: Whether to enable learning from user data
            cache_path: Path to cache file for learned data
        """
        self.model_type = model_type
        self.model_path = model_path
        self.pretrained_model = pretrained_model
        self.enable_learning = enable_learning
        self.cache_path = cache_path

        # Initialize model-specific components
        self.model = None
        self.vectorizer = None
        self.tokenizer = None
        self.char_to_idx = {}
        self.idx_to_char = {}
        self.max_length = 20

        # Training data
        self.training_data = []
        self.user_data = []

        # Check dependencies
        self._check_dependencies()

        # Initialize model
        self._initialize_model()

        # Load user data if available
        if self.enable_learning and self.cache_path and os.path.exists(self.cache_path):
            self._load_user_data()

    def _check_dependencies(self):
        """Check if required dependencies are available for the selected model."""
        if self.model_type == "naive_bayes" and not SKLEARN_AVAILABLE:
            logger.warning("scikit-learn is not available. Falling back to rule-based detection.")
            self.model_type = "rule_based"

        elif self.model_type == "lstm" and not TORCH_AVAILABLE:
            logger.warning("PyTorch is not available. Falling back to naive_bayes.")
            self.model_type = "naive_bayes" if SKLEARN_AVAILABLE else "rule_based"

        elif self.model_type == "transformer" and not TRANSFORMERS_AVAILABLE:
            logger.warning("transformers library is not available. Falling back to naive_bayes.")
            self.model_type = "naive_bayes" if SKLEARN_AVAILABLE else "rule_based"

    def _initialize_model(self):
        """Initialize the selected model."""
        if self.model_type == "naive_bayes":
            self._initialize_naive_bayes()
        elif self.model_type == "lstm":
            self._initialize_lstm()
        elif self.model_type == "transformer":
            self._initialize_transformer()
        else:
            logger.info("Using rule-based detection (no ML model).")

    def _initialize_naive_bayes(self):
        """Initialize Naive Bayes model."""
        if not SKLEARN_AVAILABLE:
            return

        try:
            # Create a vectorizer for feature extraction
            self.vectorizer = CountVectorizer(
                analyzer='char',
                ngram_range=(1, 3),
                lowercase=True
            )

            # Create a Naive Bayes classifier
            self.model = MultinomialNB()

            # Load model if available
            if self.model_path and os.path.exists(self.model_path):
                self._load_model()
            else:
                # Train with default data
                self._train_with_default_data()

            logger.info("Naive Bayes model for abbreviation detection initialized.")
        except Exception as e:
            logger.error(f"Error initializing Naive Bayes model: {str(e)}")

    def _initialize_lstm(self):
        """Initialize LSTM model."""
        if not TORCH_AVAILABLE:
            return

        try:
            # Define character vocabulary
            chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789.-_"
            self.char_to_idx = {c: i+1 for i, c in enumerate(chars)}
            self.char_to_idx['<PAD>'] = 0
            self.idx_to_char = {i: c for c, i in self.char_to_idx.items()}

            # Create LSTM model
            class LSTMAbbreviationDetector(nn.Module):
                def __init__(self, vocab_size, embedding_dim, hidden_dim, output_dim):
                    super().__init__()
                    self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx=0)
                    self.lstm = nn.LSTM(embedding_dim, hidden_dim, batch_first=True, bidirectional=True)
                    self.fc = nn.Linear(hidden_dim * 2, output_dim)

                def forward(self, x):
                    embedded = self.embedding(x)
                    output, (hidden, cell) = self.lstm(embedded)
                    hidden = torch.cat((hidden[-2,:,:], hidden[-1,:,:]), dim=1)
                    return self.fc(hidden)

            # Initialize model
            vocab_size = len(self.char_to_idx)
            embedding_dim = 50
            hidden_dim = 100
            output_dim = 1

            self.model = LSTMAbbreviationDetector(vocab_size, embedding_dim, hidden_dim, output_dim)

            # Load model if available
            if self.model_path and os.path.exists(self.model_path):
                self._load_model()
            else:
                # Train with default data
                self._train_with_default_data()

            logger.info("LSTM model for abbreviation detection initialized.")
        except Exception as e:
            logger.error(f"Error initializing LSTM model: {str(e)}")

    def _initialize_transformer(self):
        """Initialize Transformer model."""
        if not TRANSFORMERS_AVAILABLE:
            return

        try:
            # Use pretrained model or default
            model_name = self.pretrained_model or "distilbert-base-uncased"

            # Initialize tokenizer and model
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.model = AutoModelForSequenceClassification.from_pretrained(
                model_name,
                num_labels=2  # Binary classification: abbreviation or not
            )

            # Load fine-tuned model if available
            if self.model_path and os.path.exists(self.model_path):
                self._load_model()

            logger.info(f"Transformer model ({model_name}) for abbreviation detection initialized.")
        except Exception as e:
            logger.error(f"Error initializing Transformer model: {str(e)}")

    def _train_with_default_data(self):
        """Train model with default abbreviation data."""
        # Collect training data
        abbreviations = [
            "AI", "ML", "NLP", "API", "UI", "UX", "CEO", "CTO", "FAQ", "ASAP",
            "e.g.", "i.e.", "etc.", "vs.", "approx.", "dept.", "govt.", "tech.",
            "corp.", "inc.", "CPU", "GPU", "RAM", "ROM", "SSD", "HDD", "OS", "DB",
            "SQL", "HTTP", "HTTPS", "FTP", "SSH", "DNS", "IP", "TCP", "UDP", "IoT",
            "VR", "AR", "MR", "XR", "IDE", "SDK", "REST", "SOAP", "JSON", "XML",
            "HTML", "CSS", "JS", "TS", "OOP", "FP", "CI", "CD", "DevOps", "SRE",
            "SDLC", "QA", "TDD", "BDD", "DDD", "CRUD", "MVC", "MVP", "MVVM", "SPA",
            "PWA", "SEO", "GDP", "GNP", "CPI", "PPI", "ROI", "ROA", "ROE", "EBITDA",
            "EPS", "P/E", "P/B", "IPO", "M&A", "VC", "PE", "B2B", "B2C", "C2C",
            "SaaS", "PaaS", "IaaS", "CRM", "ERP", "SCM", "HRM", "KPI", "OKR", "SWOT"
        ]

        non_abbreviations = [
            "the", "and", "for", "with", "that", "this", "have", "from",
            "they", "will", "would", "there", "their", "what", "about",
            "which", "when", "make", "like", "time", "just", "know",
            "take", "people", "into", "year", "your", "good", "some",
            "could", "them", "other", "than", "then", "look", "only",
            "come", "over", "think", "also", "back", "after", "work",
            "first", "well", "even", "want", "because", "these", "give",
            "most", "world", "information", "technology", "computer",
            "system", "process", "business", "company", "organization",
            "development", "management", "analysis", "design", "implementation",
            "testing", "maintenance", "support", "service", "customer",
            "product", "market", "industry", "economy", "finance", "investment",
            "strategy", "planning", "execution", "evaluation", "improvement",
            "innovation", "research", "education", "training", "learning",
            "knowledge", "experience", "expertise", "skill", "ability",
            "capability", "competence", "performance", "quality", "efficiency",
            "effectiveness", "productivity", "profitability", "sustainability"
        ]

        if self.model_type == "naive_bayes":
            self._train_naive_bayes(abbreviations, non_abbreviations)
        elif self.model_type == "lstm":
            self._train_lstm(abbreviations, non_abbreviations)
        elif self.model_type == "transformer":
            self._train_transformer(abbreviations, non_abbreviations)

    def _train_naive_bayes(self, abbreviations, non_abbreviations):
        """Train Naive Bayes model."""
        if not SKLEARN_AVAILABLE or self.model is None or self.vectorizer is None:
            return

        try:
            # Create training data
            X_train = abbreviations + non_abbreviations
            y_train = [1] * len(abbreviations) + [0] * len(non_abbreviations)

            # Transform text to features
            X_train_features = self.vectorizer.fit_transform(X_train)

            # Train the model
            self.model.fit(X_train_features, y_train)

            logger.info("Naive Bayes model trained successfully.")
        except Exception as e:
            logger.error(f"Error training Naive Bayes model: {str(e)}")

    def _train_lstm(self, abbreviations, non_abbreviations):
        """Train LSTM model."""
        if not TORCH_AVAILABLE or self.model is None:
            return

        try:
            # Create training data
            X_train = abbreviations + non_abbreviations
            y_train = [1] * len(abbreviations) + [0] * len(non_abbreviations)

            # Convert text to sequences
            X_sequences = []
            for text in X_train:
                seq = [self.char_to_idx.get(c, 0) for c in text[:self.max_length]]
                seq = seq + [0] * (self.max_length - len(seq))  # Padding
                X_sequences.append(seq)

            # Convert to tensors
            X_tensor = torch.tensor(X_sequences, dtype=torch.long)
            y_tensor = torch.tensor(y_train, dtype=torch.float).unsqueeze(1)

            # Define optimizer and loss function
            optimizer = optim.Adam(self.model.parameters(), lr=0.001)
            criterion = nn.BCEWithLogitsLoss()

            # Train the model
            self.model.train()
            epochs = 10
            batch_size = 32

            for epoch in range(epochs):
                total_loss = 0

                # Create batches
                for i in range(0, len(X_tensor), batch_size):
                    # Get batch
                    X_batch = X_tensor[i:i+batch_size]
                    y_batch = y_tensor[i:i+batch_size]

                    # Forward pass
                    optimizer.zero_grad()
                    predictions = self.model(X_batch)
                    loss = criterion(predictions, y_batch)

                    # Backward pass
                    loss.backward()
                    optimizer.step()

                    total_loss += loss.item()

                logger.info(f"Epoch {epoch+1}/{epochs}, Loss: {total_loss:.4f}")

            logger.info("LSTM model trained successfully.")
        except Exception as e:
            logger.error(f"Error training LSTM model: {str(e)}")

    def _train_transformer(self, abbreviations, non_abbreviations):
        """Train Transformer model."""
        if not TRANSFORMERS_AVAILABLE or self.model is None or self.tokenizer is None:
            return

        try:
            # Create training data
            texts = abbreviations + non_abbreviations
            labels = [1] * len(abbreviations) + [0] * len(non_abbreviations)

            # Create dataset
            class AbbreviationDataset(torch.utils.data.Dataset):
                def __init__(self, texts, labels, tokenizer, max_length=32):
                    self.texts = texts
                    self.labels = labels
                    self.tokenizer = tokenizer
                    self.max_length = max_length

                def __len__(self):
                    return len(self.texts)

                def __getitem__(self, idx):
                    text = self.texts[idx]
                    label = self.labels[idx]

                    encoding = self.tokenizer(
                        text,
                        truncation=True,
                        padding='max_length',
                        max_length=self.max_length,
                        return_tensors='pt'
                    )

                    return {
                        'input_ids': encoding['input_ids'].flatten(),
                        'attention_mask': encoding['attention_mask'].flatten(),
                        'labels': torch.tensor(label, dtype=torch.long)
                    }

            # Split data
            train_texts, val_texts, train_labels, val_labels = train_test_split(
                texts, labels, test_size=0.2, random_state=42
            )

            # Create datasets
            train_dataset = AbbreviationDataset(train_texts, train_labels, self.tokenizer)
            val_dataset = AbbreviationDataset(val_texts, val_labels, self.tokenizer)

            # Define training arguments
            training_args = TrainingArguments(
                output_dir='./results',
                num_train_epochs=3,
                per_device_train_batch_size=16,
                per_device_eval_batch_size=16,
                warmup_steps=500,
                weight_decay=0.01,
                logging_dir='./logs',
                logging_steps=10,
                evaluation_strategy="epoch"
            )

            # Create trainer
            trainer = Trainer(
                model=self.model,
                args=training_args,
                train_dataset=train_dataset,
                eval_dataset=val_dataset
            )

            # Train the model
            trainer.train()

            logger.info("Transformer model trained successfully.")
        except Exception as e:
            logger.error(f"Error training Transformer model: {str(e)}")

    def _load_model(self):
        """Load model from file."""
        try:
            if self.model_type == "naive_bayes":
                with open(self.model_path, 'rb') as f:
                    import pickle
                    data = pickle.load(f)
                    self.model = data['model']
                    self.vectorizer = data['vectorizer']
                logger.info(f"Loaded Naive Bayes model from {self.model_path}")

            elif self.model_type == "lstm":
                self.model.load_state_dict(torch.load(self.model_path))
                logger.info(f"Loaded LSTM model from {self.model_path}")

            elif self.model_type == "transformer":
                self.model = AutoModelForSequenceClassification.from_pretrained(self.model_path)
                logger.info(f"Loaded Transformer model from {self.model_path}")

        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")

    def _save_model(self):
        """Save model to file."""
        if not self.model_path:
            return

        try:
            os.makedirs(os.path.dirname(self.model_path), exist_ok=True)

            if self.model_type == "naive_bayes":
                with open(self.model_path, 'wb') as f:
                    import pickle
                    pickle.dump({'model': self.model, 'vectorizer': self.vectorizer}, f)

            elif self.model_type == "lstm":
                torch.save(self.model.state_dict(), self.model_path)

            elif self.model_type == "transformer":
                self.model.save_pretrained(self.model_path)
                self.tokenizer.save_pretrained(self.model_path)

            logger.info(f"Saved model to {self.model_path}")

        except Exception as e:
            logger.error(f"Error saving model: {str(e)}")

    def _load_user_data(self):
        """Load user data from cache."""
        if not self.cache_path:
            return

        try:
            with open(self.cache_path, 'r', encoding='utf-8') as f:
                self.user_data = json.load(f)
            logger.info(f"Loaded {len(self.user_data)} user data samples from {self.cache_path}")
        except Exception as e:
            logger.error(f"Error loading user data: {str(e)}")

    def _save_user_data(self):
        """Save user data to cache."""
        if not self.cache_path or not self.user_data:
            return

        try:
            os.makedirs(os.path.dirname(self.cache_path), exist_ok=True)
            with open(self.cache_path, 'w', encoding='utf-8') as f:
                json.dump(self.user_data, f, ensure_ascii=False, indent=2)
            logger.info(f"Saved {len(self.user_data)} user data samples to {self.cache_path}")
        except Exception as e:
            logger.error(f"Error saving user data: {str(e)}")

    def add_user_data(self, text: str, is_abbreviation: bool):
        """
        Add user data for training.

        Args:
            text: Text to add
            is_abbreviation: Whether the text is an abbreviation
        """
        if not self.enable_learning:
            return

        self.user_data.append({
            'text': text,
            'is_abbreviation': is_abbreviation
        })

        # Save user data
        self._save_user_data()

        # Retrain model if enough new data
        if len(self.user_data) % 10 == 0:
            self.train_with_user_data()

    def train_with_user_data(self):
        """Train model with user data."""
        if not self.enable_learning or not self.user_data:
            return

        try:
            # Split user data
            abbreviations = [item['text'] for item in self.user_data if item['is_abbreviation']]
            non_abbreviations = [item['text'] for item in self.user_data if not item['is_abbreviation']]

            if not abbreviations or not non_abbreviations:
                logger.warning("Not enough user data for training.")
                return

            # Train model
            if self.model_type == "naive_bayes":
                self._train_naive_bayes(abbreviations, non_abbreviations)
            elif self.model_type == "lstm":
                self._train_lstm(abbreviations, non_abbreviations)
            elif self.model_type == "transformer":
                self._train_transformer(abbreviations, non_abbreviations)

            # Save model
            self._save_model()

            logger.info(f"Model retrained with {len(abbreviations)} abbreviations and {len(non_abbreviations)} non-abbreviations.")
        except Exception as e:
            logger.error(f"Error training with user data: {str(e)}")

    def detect_abbreviations(self, text: str) -> List[Tuple[str, float]]:
        """
        Detect abbreviations in text using ML model.

        Args:
            text: Text to analyze

        Returns:
            List of tuples containing (word, probability)
        """
        # Split text into words
        words = re.findall(r'\b\w+\b', text)

        # Skip empty list
        if not words:
            return []

        results = []

        try:
            if self.model_type == "naive_bayes":
                results = self._detect_with_naive_bayes(words)
            elif self.model_type == "lstm":
                results = self._detect_with_lstm(words)
            elif self.model_type == "transformer":
                results = self._detect_with_transformer(words)
            else:
                # Fallback to rule-based detection
                results = self._detect_with_rules(words)

            return results
        except Exception as e:
            logger.error(f"Error detecting abbreviations: {str(e)}")
            return []

    def _detect_with_naive_bayes(self, words: List[str]) -> List[Tuple[str, float]]:
        """
        Detect abbreviations using Naive Bayes model.

        Args:
            words: List of words to analyze

        Returns:
            List of tuples containing (word, probability)
        """
        if not SKLEARN_AVAILABLE or self.model is None or self.vectorizer is None:
            return []

        try:
            # Transform words to features
            X_features = self.vectorizer.transform(words)

            # Get probabilities
            probabilities = self.model.predict_proba(X_features)

            # Extract abbreviation probabilities (class 1)
            results = []
            for i, word in enumerate(words):
                if i < len(probabilities):
                    # Get probability of being an abbreviation (class 1)
                    prob = probabilities[i][1]

                    # Filter by threshold
                    if prob >= 0.5:
                        results.append((word, float(prob)))

            return results
        except Exception as e:
            logger.error(f"Error in Naive Bayes detection: {str(e)}")
            return []

    def _detect_with_lstm(self, words: List[str]) -> List[Tuple[str, float]]:
        """
        Detect abbreviations using LSTM model.

        Args:
            words: List of words to analyze

        Returns:
            List of tuples containing (word, probability)
        """
        if not TORCH_AVAILABLE or self.model is None:
            return []

        try:
            # Convert words to sequences
            sequences = []
            for word in words:
                seq = [self.char_to_idx.get(c, 0) for c in word[:self.max_length]]
                seq = seq + [0] * (self.max_length - len(seq))  # Padding
                sequences.append(seq)

            # Convert to tensor
            X_tensor = torch.tensor(sequences, dtype=torch.long)

            # Set model to evaluation mode
            self.model.eval()

            # Get predictions
            with torch.no_grad():
                predictions = self.model(X_tensor)
                probabilities = torch.sigmoid(predictions).squeeze().tolist()

            # Ensure probabilities is a list
            if not isinstance(probabilities, list):
                probabilities = [probabilities]

            # Extract results
            results = []
            for i, word in enumerate(words):
                if i < len(probabilities):
                    prob = probabilities[i]

                    # Filter by threshold
                    if prob >= 0.5:
                        results.append((word, float(prob)))

            return results
        except Exception as e:
            logger.error(f"Error in LSTM detection: {str(e)}")
            return []

    def _detect_with_transformer(self, words: List[str]) -> List[Tuple[str, float]]:
        """
        Detect abbreviations using Transformer model.

        Args:
            words: List of words to analyze

        Returns:
            List of tuples containing (word, probability)
        """
        if not TRANSFORMERS_AVAILABLE or self.model is None or self.tokenizer is None:
            return []

        try:
            results = []

            # Process words in batches to avoid memory issues
            batch_size = 16
            for i in range(0, len(words), batch_size):
                batch = words[i:i+batch_size]

                # Tokenize
                inputs = self.tokenizer(
                    batch,
                    padding=True,
                    truncation=True,
                    max_length=32,
                    return_tensors="pt"
                )

                # Get predictions
                self.model.eval()
                with torch.no_grad():
                    outputs = self.model(**inputs)
                    logits = outputs.logits
                    probabilities = torch.softmax(logits, dim=1)

                    # Extract abbreviation probabilities (class 1)
                    for j, word in enumerate(batch):
                        if j < probabilities.shape[0]:
                            # Get probability of being an abbreviation (class 1)
                            prob = probabilities[j, 1].item()

                            # Filter by threshold
                            if prob >= 0.5:
                                results.append((word, float(prob)))

            return results
        except Exception as e:
            logger.error(f"Error in Transformer detection: {str(e)}")
            return []

    def _detect_with_rules(self, words: List[str]) -> List[Tuple[str, float]]:
        """
        Detect abbreviations using rule-based approach.

        Args:
            words: List of words to analyze

        Returns:
            List of tuples containing (word, probability)
        """
        results = []

        for word in words:
            # Check if word is all uppercase
            if word.isupper() and len(word) >= 2 and len(word) <= 10:
                results.append((word, 0.9))

            # Check if word has periods (e.g., U.S.A.)
            elif "." in word and len(word) >= 2:
                results.append((word, 0.8))

            # Check if word has mixed case with uppercase first letter
            elif word[0].isupper() and any(c.islower() for c in word) and any(c.isupper() for c in word[1:]):
                results.append((word, 0.7))

        return results

    def find_abbreviations_in_text(
        self,
        text: str
    ) -> List[Tuple[str, int, int, float]]:
        """
        Find abbreviations in text with their positions.

        Args:
            text: Text to analyze

        Returns:
            List of tuples containing (abbreviation, start_idx, end_idx, probability)
        """
        # Detect potential abbreviations
        potential_abbrs = self.detect_abbreviations(text)

        # Find positions in text
        results = []
        for abbr, prob in potential_abbrs:
            # Find all occurrences
            for match in re.finditer(r'\b' + re.escape(abbr) + r'\b', text):
                results.append((
                    abbr,
                    match.start(),
                    match.end(),
                    prob
                ))

        # Sort by position
        results.sort(key=lambda x: x[1])

        return results
