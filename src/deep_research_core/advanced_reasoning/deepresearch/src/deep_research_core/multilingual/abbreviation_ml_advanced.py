"""
Advanced machine learning models for abbreviation detection.

This module provides advanced machine learning models for detecting abbreviations
in text, including BiLSTM with attention and specialized Transformer models for
different languages.
"""

import os
import re
import json
import logging
import numpy as np
from typing import List, Dict, Tuple, Optional, Any, Union, Set, Callable
from collections import defaultdict, Counter
from functools import lru_cache

# Create logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Try to import optional dependencies
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import Dataset, DataLoader
    from torch.nn.utils.rnn import pad_sequence, pack_padded_sequence, pad_packed_sequence
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    logger.warning("PyTorch is not available. Advanced ML models will be disabled.")

try:
    from transformers import (
        AutoTokenizer, AutoModelForTokenClassification, AutoModelForSequenceClassification,
        Trainer, TrainingArguments, DataCollatorForTokenClassification
    )
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    logger.warning("transformers library is not available. Transformer models will be disabled.")

# Import base detector if available
try:
    from .abbreviation_ml_detector import AbbreviationMLDetector
    BASE_DETECTOR_AVAILABLE = True
except ImportError:
    BASE_DETECTOR_AVAILABLE = False
    logger.warning("Base AbbreviationMLDetector is not available. Some features will be limited.")

# Language-specific models
LANGUAGE_MODELS = {
    "en": "distilbert-base-uncased",
    "vi": "vinai/phobert-base",
    "zh": "bert-base-chinese",
    "ja": "cl-tohoku/bert-base-japanese",
    "ko": "klue/bert-base",
    "th": "airesearch/wangchanberta-base-att-spm-uncased"
}

class AbbreviationDataset(Dataset):
    """Dataset for abbreviation detection."""

    def __init__(self, texts, labels, tokenizer=None, max_length=128):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length

    def __len__(self):
        return len(self.texts)

    def __getitem__(self, idx):
        text = self.texts[idx]
        label = self.labels[idx]

        if self.tokenizer:
            # For transformer models
            encoding = self.tokenizer(
                text,
                truncation=True,
                padding='max_length',
                max_length=self.max_length,
                return_tensors='pt'
            )

            return {
                'input_ids': encoding['input_ids'].flatten(),
                'attention_mask': encoding['attention_mask'].flatten(),
                'labels': torch.tensor(label, dtype=torch.long)
            }
        else:
            # For LSTM models
            return text, label

class BiLSTMAttention(nn.Module):
    """BiLSTM with attention mechanism for abbreviation detection."""

    def __init__(self, vocab_size, embedding_dim, hidden_dim, output_dim, n_layers=2, dropout=0.2):
        super().__init__()

        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx=0)
        self.lstm = nn.LSTM(
            embedding_dim,
            hidden_dim,
            num_layers=n_layers,
            bidirectional=True,
            dropout=dropout if n_layers > 1 else 0,
            batch_first=True
        )

        # Attention mechanism
        self.attention = nn.Linear(hidden_dim * 2, 1)

        # Output layer
        self.fc = nn.Linear(hidden_dim * 2, output_dim)
        self.dropout = nn.Dropout(dropout)

    def forward(self, text, text_lengths):
        # text: [batch size, seq len]
        embedded = self.embedding(text)
        # embedded: [batch size, seq len, embedding dim]

        # Pack sequence
        packed_embedded = pack_padded_sequence(embedded, text_lengths, batch_first=True, enforce_sorted=False)

        # Pass through LSTM
        packed_output, (hidden, cell) = self.lstm(packed_embedded)

        # Unpack sequence
        output, output_lengths = pad_packed_sequence(packed_output, batch_first=True)
        # output: [batch size, seq len, hidden dim * 2]

        # Apply attention
        attention_scores = self.attention(output)
        # attention_scores: [batch size, seq len, 1]

        # Apply softmax to get attention weights
        attention_weights = torch.softmax(attention_scores, dim=1)

        # Apply attention weights to output
        context_vector = torch.sum(attention_weights * output, dim=1)
        # context_vector: [batch size, hidden dim * 2]

        # Apply dropout and pass through linear layer
        output = self.fc(self.dropout(context_vector))
        # output: [batch size, output dim]

        return output

class AdvancedAbbreviationMLDetector:
    """
    Advanced machine learning detector for abbreviations.

    This class provides methods for detecting abbreviations using advanced
    machine learning models, including BiLSTM with attention and specialized
    Transformer models for different languages.
    """

    # Singleton instance
    _instances = {}

    @classmethod
    def get_instance(cls, model_type: str = "bilstm", language: str = "en", **kwargs) -> 'AdvancedAbbreviationMLDetector':
        """
        Get singleton instance of AdvancedAbbreviationMLDetector.

        Args:
            model_type: Type of model to use ("bilstm", "transformer")
            language: Language code
            **kwargs: Additional parameters for the model

        Returns:
            Instance of AdvancedAbbreviationMLDetector
        """
        key = f"{model_type}_{language}"
        if key not in cls._instances:
            cls._instances[key] = cls(model_type=model_type, language=language, **kwargs)
        return cls._instances[key]

    def __init__(
        self,
        model_type: str = "bilstm",
        language: str = "en",
        model_path: Optional[str] = None,
        pretrained_model: Optional[str] = None,
        enable_learning: bool = True,
        cache_path: Optional[str] = None,
        data_sources: Optional[List[str]] = None
    ):
        """
        Initialize the AdvancedAbbreviationMLDetector.

        Args:
            model_type: Type of model to use ("bilstm", "transformer")
            language: Language code
            model_path: Path to save/load model
            pretrained_model: Name of pretrained model (for transformer)
            enable_learning: Whether to enable learning from user data
            cache_path: Path to cache file for learned data
            data_sources: List of paths to additional data sources
        """
        self.model_type = model_type
        self.language = language
        self.model_path = model_path
        self.pretrained_model = pretrained_model
        self.enable_learning = enable_learning
        self.cache_path = cache_path
        self.data_sources = data_sources or []

        # Initialize model-specific components
        self.model = None
        self.tokenizer = None
        self.char_to_idx = {}
        self.idx_to_char = {}
        self.max_length = 128

        # Training data
        self.training_data = []
        self.user_data = []

        # Check dependencies
        self._check_dependencies()

        # Initialize model
        self._initialize_model()

        # Load user data if available
        if self.enable_learning and self.cache_path and os.path.exists(self.cache_path):
            self._load_user_data()

    def _check_dependencies(self):
        """Check if required dependencies are available for the selected model."""
        if self.model_type == "bilstm" and not TORCH_AVAILABLE:
            logger.warning("PyTorch is not available. Falling back to base detector.")
            self.model_type = "base"

        elif self.model_type == "transformer" and not TRANSFORMERS_AVAILABLE:
            logger.warning("transformers library is not available. Falling back to base detector.")
            self.model_type = "base"

    def _initialize_model(self):
        """Initialize the selected model."""
        if self.model_type == "bilstm":
            self._initialize_bilstm()
        elif self.model_type == "transformer":
            self._initialize_transformer()
        elif self.model_type == "base" and BASE_DETECTOR_AVAILABLE:
            # Use base detector as fallback
            self.base_detector = AbbreviationMLDetector.get_instance(
                model_type="naive_bayes",
                model_path=self.model_path,
                enable_learning=self.enable_learning,
                cache_path=self.cache_path
            )
        else:
            logger.info("Using rule-based detection (no ML model).")

    def _initialize_bilstm(self):
        """Initialize BiLSTM model with attention."""
        if not TORCH_AVAILABLE:
            return

        try:
            # Define character vocabulary
            chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789.-_"
            self.char_to_idx = {c: i+1 for i, c in enumerate(chars)}
            self.char_to_idx['<PAD>'] = 0
            self.idx_to_char = {i: c for c, i in self.char_to_idx.items()}

            # Add special characters for non-Latin languages
            if self.language in ["zh", "ja", "ko", "th"]:
                # Add common characters for these languages
                if self.language == "zh":
                    # Add common Chinese characters
                    for i, c in enumerate("的一是不了人我在有他这为之大来以个中上们"):
                        self.char_to_idx[c] = len(self.char_to_idx) + i
                elif self.language == "ja":
                    # Add common Japanese characters
                    for i, c in enumerate("のはをにたがでしものとりる"):
                        self.char_to_idx[c] = len(self.char_to_idx) + i
                elif self.language == "ko":
                    # Add common Korean characters
                    for i, c in enumerate("은는이가을를에서의로"):
                        self.char_to_idx[c] = len(self.char_to_idx) + i

            # Update idx_to_char
            self.idx_to_char = {i: c for c, i in self.char_to_idx.items()}

            # Create BiLSTM model
            vocab_size = len(self.char_to_idx)
            embedding_dim = 100
            hidden_dim = 128
            output_dim = 1

            self.model = BiLSTMAttention(vocab_size, embedding_dim, hidden_dim, output_dim)

            # Load model if available
            if self.model_path and os.path.exists(self.model_path):
                self._load_model()
            else:
                # Train with default data
                self._train_with_default_data()

            logger.info("BiLSTM model with attention for abbreviation detection initialized.")
        except Exception as e:
            logger.error(f"Error initializing BiLSTM model: {str(e)}")

    def _initialize_transformer(self):
        """Initialize Transformer model."""
        if not TRANSFORMERS_AVAILABLE:
            return

        try:
            # Use language-specific model or default
            model_name = self.pretrained_model
            if not model_name:
                model_name = LANGUAGE_MODELS.get(self.language, "distilbert-base-uncased")

            # Initialize tokenizer and model
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.model = AutoModelForTokenClassification.from_pretrained(
                model_name,
                num_labels=2  # Binary classification: abbreviation or not
            )

            # Load fine-tuned model if available
            if self.model_path and os.path.exists(self.model_path):
                self._load_model()

            logger.info(f"Transformer model ({model_name}) for abbreviation detection initialized.")
        except Exception as e:
            logger.error(f"Error initializing Transformer model: {str(e)}")

    def _load_model(self):
        """Load model from disk."""
        try:
            if self.model_type == "bilstm":
                self.model.load_state_dict(torch.load(self.model_path))
                self.model.eval()
                logger.info(f"Loaded BiLSTM model from {self.model_path}")
            elif self.model_type == "transformer":
                self.model = AutoModelForTokenClassification.from_pretrained(self.model_path)
                logger.info(f"Loaded Transformer model from {self.model_path}")
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")

    def _save_model(self):
        """Save model to disk."""
        if not self.model_path:
            logger.warning("No model path specified. Model will not be saved.")
            return

        try:
            os.makedirs(os.path.dirname(self.model_path), exist_ok=True)

            if self.model_type == "bilstm":
                torch.save(self.model.state_dict(), self.model_path)
                logger.info(f"Saved BiLSTM model to {self.model_path}")
            elif self.model_type == "transformer":
                self.model.save_pretrained(self.model_path)
                self.tokenizer.save_pretrained(self.model_path)
                logger.info(f"Saved Transformer model to {self.model_path}")
        except Exception as e:
            logger.error(f"Error saving model: {str(e)}")

    def _load_user_data(self):
        """Load user data from cache."""
        if not self.cache_path:
            return

        try:
            with open(self.cache_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.user_data = data.get('user_data', [])
                logger.info(f"Loaded {len(self.user_data)} user data samples from {self.cache_path}")
        except Exception as e:
            logger.error(f"Error loading user data: {str(e)}")

    def _save_user_data(self):
        """Save user data to cache."""
        if not self.cache_path or not self.enable_learning:
            return

        try:
            os.makedirs(os.path.dirname(self.cache_path), exist_ok=True)

            with open(self.cache_path, 'w', encoding='utf-8') as f:
                json.dump({'user_data': self.user_data}, f, ensure_ascii=False, indent=2)
                logger.info(f"Saved {len(self.user_data)} user data samples to {self.cache_path}")
        except Exception as e:
            logger.error(f"Error saving user data: {str(e)}")

    def _train_with_default_data(self):
        """Train model with default data."""
        # Load default training data for the language
        default_data = self._load_default_data()

        if not default_data:
            logger.warning(f"No default training data available for language {self.language}")
            return

        # Train model
        self._train_model(default_data)

    def _load_default_data(self) -> List[Dict[str, Any]]:
        """Load default training data for the language."""
        default_data = []

        # Add language-specific default data
        if self.language == "en":
            default_data.extend([
                {"text": "I work at NASA and use AI for research.", "abbreviations": [("NASA", 10, 14), ("AI", 24, 26)]},
                {"text": "The CEO of IBM announced a new AI product.", "abbreviations": [("CEO", 4, 7), ("IBM", 11, 14), ("AI", 29, 31)]},
                {"text": "The USA is a member of the UN and NATO.", "abbreviations": [("USA", 4, 7), ("UN", 25, 27), ("NATO", 32, 36)]},
                {"text": "The FBI and CIA are intelligence agencies.", "abbreviations": [("FBI", 4, 7), ("CIA", 12, 15)]},
                {"text": "I have a PhD in Computer Science from MIT.", "abbreviations": [("PhD", 9, 12), ("MIT", 37, 40)]},
                {"text": "The GDP of the USA increased by 2% last year.", "abbreviations": [("GDP", 4, 7), ("USA", 15, 18)]},
                {"text": "The HTML and CSS are used for web development.", "abbreviations": [("HTML", 4, 8), ("CSS", 13, 16)]},
                {"text": "The WHO announced new guidelines for COVID-19 prevention.", "abbreviations": [("WHO", 4, 7), ("COVID-19", 35, 43)]},
                {"text": "The EU and UK signed a trade agreement.", "abbreviations": [("EU", 4, 6), ("UK", 11, 13)]},
                {"text": "The CPU and GPU are important components of a computer.", "abbreviations": [("CPU", 4, 7), ("GPU", 12, 15)]}
            ])
        elif self.language == "vi":
            default_data.extend([
                {"text": "Tôi làm việc tại ĐHQG và sử dụng TTNT cho nghiên cứu.", "abbreviations": [("ĐHQG", 16, 20), ("TTNT", 31, 35)]},
                {"text": "TGĐ của TPHCM đã công bố một sản phẩm TTNT mới.", "abbreviations": [("TGĐ", 0, 3), ("TPHCM", 8, 13), ("TTNT", 38, 42)]},
                {"text": "Nước CHXHCN VN là thành viên của LHQ và NATO.", "abbreviations": [("CHXHCN", 5, 11), ("VN", 12, 14), ("LHQ", 32, 35), ("NATO", 39, 43)]},
                {"text": "Cục CSĐT và CATP là các cơ quan thực thi pháp luật.", "abbreviations": [("CSĐT", 4, 8), ("CATP", 12, 16)]},
                {"text": "Tôi có bằng TS về KHMT từ ĐHBK.", "abbreviations": [("TS", 12, 14), ("KHMT", 18, 22), ("ĐHBK", 26, 30)]},
                {"text": "GDP của VN tăng 2% năm ngoái.", "abbreviations": [("GDP", 0, 3), ("VN", 8, 10)]},
                {"text": "HTML và CSS được sử dụng cho phát triển web.", "abbreviations": [("HTML", 0, 4), ("CSS", 8, 11)]},
                {"text": "WHO đã công bố hướng dẫn mới về phòng chống COVID-19.", "abbreviations": [("WHO", 0, 3), ("COVID-19", 42, 50)]},
                {"text": "EU và UK đã ký một thỏa thuận thương mại.", "abbreviations": [("EU", 0, 2), ("UK", 6, 8)]},
                {"text": "CPU và GPU là các thành phần quan trọng của máy tính.", "abbreviations": [("CPU", 0, 3), ("GPU", 7, 10)]}
            ])
        elif self.language == "zh":
            default_data.extend([
                {"text": "我在中科院工作，使用人工智能进行研究。", "abbreviations": [("中科院", 2, 5)]},
                {"text": "中共中央政治局召开会议。", "abbreviations": [("中共", 0, 2)]},
                {"text": "北大和清华是中国顶尖大学。", "abbreviations": [("北大", 0, 2), ("清华", 3, 5)]},
                {"text": "工信部发布了新的政策。", "abbreviations": [("工信部", 0, 3)]},
                {"text": "高考成绩将在下周公布。", "abbreviations": [("高考", 0, 2)]}
            ])
        elif self.language == "ja":
            default_data.extend([
                {"text": "私は東大で人工知能の研究をしています。", "abbreviations": [("東大", 2, 4)]},
                {"text": "AIとMLは密接に関連しています。", "abbreviations": [("AI", 0, 2), ("ML", 3, 5)]},
                {"text": "早大と慶大は私立大学です。", "abbreviations": [("早大", 0, 2), ("慶大", 3, 5), ("私立", 6, 8)]},
                {"text": "NLPの研究が進んでいます。", "abbreviations": [("NLP", 0, 3)]},
                {"text": "株式会社トヨタは自動車メーカーです。", "abbreviations": [("株式会社", 0, 4)]}
            ])
        elif self.language == "ko":
            default_data.extend([
                {"text": "서울대에서 인공지능을 연구하고 있습니다.", "abbreviations": [("서울대", 0, 3)]},
                {"text": "AI와 ML은 밀접하게 관련되어 있습니다.", "abbreviations": [("AI", 0, 2), ("ML", 3, 5)]},
                {"text": "한국과학기술원(KAIST)은 대한민국의 대학입니다.", "abbreviations": [("KAIST", 8, 13)]},
                {"text": "NLP 연구가 진행 중입니다.", "abbreviations": [("NLP", 0, 3)]},
                {"text": "삼성전자(주)는 전자제품을 생산합니다.", "abbreviations": [("주", 6, 7)]}
            ])

        # Add data from additional sources
        for source in self.data_sources:
            if os.path.exists(source):
                try:
                    with open(source, 'r', encoding='utf-8') as f:
                        source_data = json.load(f)
                        if isinstance(source_data, list):
                            default_data.extend(source_data)
                            logger.info(f"Loaded {len(source_data)} samples from {source}")
                except Exception as e:
                    logger.error(f"Error loading data from {source}: {str(e)}")

        # Add user data if available
        if self.user_data:
            default_data.extend(self.user_data)
            logger.info(f"Added {len(self.user_data)} user data samples to training data")

        return default_data

    def _train_model(self, training_data: List[Dict[str, Any]]):
        """Train model with the provided training data."""
        if not training_data:
            logger.warning("No training data provided.")
            return

        if self.model_type == "bilstm":
            self._train_bilstm(training_data)
        elif self.model_type == "transformer":
            self._train_transformer(training_data)

    def _train_bilstm(self, training_data: List[Dict[str, Any]]):
        """Train BiLSTM model."""
        if not TORCH_AVAILABLE:
            return

        try:
            # Prepare training data
            X = []
            y = []

            for item in training_data:
                text = item["text"]
                abbreviations = item["abbreviations"]

                # Create character-level features
                for i in range(len(text) - 1):
                    # Extract a window of characters
                    window_size = 5
                    start = max(0, i - window_size)
                    end = min(len(text), i + window_size + 1)
                    window = text[start:end]

                    # Convert to indices
                    indices = [self.char_to_idx.get(c, 0) for c in window]

                    # Check if current position is part of an abbreviation
                    is_abbr = any(start_idx <= i <= end_idx for _, start_idx, end_idx in abbreviations)

                    X.append(indices)
                    y.append(1 if is_abbr else 0)

            # Convert to tensors
            X_tensor = [torch.tensor(x, dtype=torch.long) for x in X]
            y_tensor = torch.tensor(y, dtype=torch.float)

            # Create data loader
            X_padded = pad_sequence(X_tensor, batch_first=True)
            dataset = torch.utils.data.TensorDataset(X_padded, y_tensor)
            dataloader = torch.utils.data.DataLoader(dataset, batch_size=32, shuffle=True)

            # Train model
            optimizer = optim.Adam(self.model.parameters(), lr=0.001)
            criterion = nn.BCEWithLogitsLoss()

            self.model.train()
            for epoch in range(10):
                total_loss = 0
                for batch_X, batch_y in dataloader:
                    # Forward pass
                    outputs = self.model(batch_X, torch.tensor([len(x) for x in batch_X]))

                    # Calculate loss
                    loss = criterion(outputs.squeeze(), batch_y)

                    # Backward pass and optimize
                    optimizer.zero_grad()
                    loss.backward()
                    optimizer.step()

                    total_loss += loss.item()

                logger.info(f"Epoch {epoch+1}, Loss: {total_loss/len(dataloader):.4f}")

            # Save model
            self._save_model()

            logger.info("BiLSTM model training completed.")
        except Exception as e:
            logger.error(f"Error training BiLSTM model: {str(e)}")

    def _train_transformer(self, training_data: List[Dict[str, Any]]):
        """Train Transformer model."""
        if not TRANSFORMERS_AVAILABLE:
            return

        try:
            # Prepare training data
            texts = []
            labels = []

            for item in training_data:
                text = item["text"]
                abbreviations = item["abbreviations"]

                # Create token-level labels
                tokens = self.tokenizer.tokenize(text)
                token_labels = [0] * len(tokens)  # 0 = not abbreviation, 1 = abbreviation

                # Map abbreviations to tokens
                for abbr, start_idx, end_idx in abbreviations:
                    # Find tokens that correspond to the abbreviation
                    abbr_tokens = self.tokenizer.tokenize(text[start_idx:end_idx])

                    # Find the position of these tokens in the full tokenized text
                    for i in range(len(tokens) - len(abbr_tokens) + 1):
                        if tokens[i:i+len(abbr_tokens)] == abbr_tokens:
                            for j in range(i, i+len(abbr_tokens)):
                                token_labels[j] = 1

                texts.append(text)
                labels.append(token_labels)

            # Create dataset
            dataset = AbbreviationDataset(texts, labels, self.tokenizer)

            # Training arguments
            training_args = TrainingArguments(
                output_dir="./results",
                num_train_epochs=3,
                per_device_train_batch_size=8,
                warmup_steps=500,
                weight_decay=0.01,
                logging_dir="./logs",
                logging_steps=10,
            )

            # Data collator
            data_collator = DataCollatorForTokenClassification(self.tokenizer)

            # Trainer
            trainer = Trainer(
                model=self.model,
                args=training_args,
                train_dataset=dataset,
                data_collator=data_collator,
            )

            # Train model
            trainer.train()

            # Save model
            self._save_model()

            logger.info("Transformer model training completed.")
        except Exception as e:
            logger.error(f"Error training Transformer model: {str(e)}")

    def find_abbreviations_in_text(self, text: str) -> List[Tuple[str, int, int, float]]:
        """
        Find abbreviations in text using the advanced ML model.

        Args:
            text: Text to analyze

        Returns:
            List of tuples containing (abbreviation, start_idx, end_idx, confidence)
        """
        if not text:
            return []

        # Use appropriate model based on type
        if self.model_type == "bilstm":
            return self._find_abbreviations_bilstm(text)
        elif self.model_type == "transformer":
            return self._find_abbreviations_transformer(text)
        elif self.model_type == "base" and BASE_DETECTOR_AVAILABLE:
            # Use base detector as fallback
            return self.base_detector.find_abbreviations_in_text(text)
        else:
            # Fallback to rule-based detection
            return self._find_abbreviations_rule_based(text)

    def _find_abbreviations_bilstm(self, text: str) -> List[Tuple[str, int, int, float]]:
        """
        Find abbreviations in text using BiLSTM model.

        Args:
            text: Text to analyze

        Returns:
            List of tuples containing (abbreviation, start_idx, end_idx, confidence)
        """
        if not TORCH_AVAILABLE or not self.model:
            return self._find_abbreviations_rule_based(text)

        try:
            # Set model to evaluation mode
            self.model.eval()

            # Process text character by character with sliding window
            abbreviations = []
            current_abbr = None
            current_start = -1
            current_confidence = 0.0

            for i in range(len(text)):
                # Extract window
                window_size = 5
                start = max(0, i - window_size)
                end = min(len(text), i + window_size + 1)
                window = text[start:end]

                # Convert to indices
                indices = [self.char_to_idx.get(c, 0) for c in window]

                # Convert to tensor
                x_tensor = torch.tensor(indices, dtype=torch.long).unsqueeze(0)

                # Get prediction
                with torch.no_grad():
                    output = self.model(x_tensor, torch.tensor([len(indices)]))
                    prob = torch.sigmoid(output).item()

                # Check if current character is part of an abbreviation
                is_abbr = prob > 0.5

                if is_abbr:
                    if current_abbr is None:
                        # Start of a new abbreviation
                        current_abbr = text[i]
                        current_start = i
                        current_confidence = prob
                    else:
                        # Continue current abbreviation
                        current_abbr += text[i]
                        current_confidence = (current_confidence + prob) / 2
                elif current_abbr is not None:
                    # End of an abbreviation
                    if self._is_valid_abbreviation(current_abbr):
                        abbreviations.append((
                            current_abbr,
                            current_start,
                            i,
                            current_confidence
                        ))

                    # Reset
                    current_abbr = None
                    current_start = -1
                    current_confidence = 0.0

            # Check if there's an abbreviation at the end of the text
            if current_abbr is not None and self._is_valid_abbreviation(current_abbr):
                abbreviations.append((
                    current_abbr,
                    current_start,
                    len(text),
                    current_confidence
                ))

            return abbreviations

        except Exception as e:
            logger.error(f"Error in BiLSTM abbreviation detection: {str(e)}")
            return self._find_abbreviations_rule_based(text)

    def _find_abbreviations_transformer(self, text: str) -> List[Tuple[str, int, int, float]]:
        """
        Find abbreviations in text using Transformer model.

        Args:
            text: Text to analyze

        Returns:
            List of tuples containing (abbreviation, start_idx, end_idx, confidence)
        """
        if not TRANSFORMERS_AVAILABLE or not self.model or not self.tokenizer:
            return self._find_abbreviations_rule_based(text)

        try:
            # Set model to evaluation mode
            self.model.eval()

            # Tokenize text
            inputs = self.tokenizer(
                text,
                return_tensors="pt",
                return_offsets_mapping=True,
                padding=True,
                truncation=True,
                max_length=self.max_length
            )

            # Get token offsets
            offset_mapping = inputs.pop("offset_mapping")[0].tolist()

            # Get predictions
            with torch.no_grad():
                outputs = self.model(**inputs)
                logits = outputs.logits
                probs = torch.softmax(logits, dim=-1)
                predictions = torch.argmax(probs, dim=-1)[0].tolist()
                confidences = [probs[0, i, pred].item() for i, pred in enumerate(predictions)]

            # Extract abbreviations
            abbreviations = []
            current_abbr = None
            current_start = -1
            current_confidence = 0.0

            for i, (pred, conf, (start, end)) in enumerate(zip(predictions, confidences, offset_mapping)):
                if pred == 1:  # Abbreviation
                    if current_abbr is None:
                        # Start of a new abbreviation
                        current_abbr = text[start:end]
                        current_start = start
                        current_confidence = conf
                    else:
                        # Continue current abbreviation
                        current_abbr += text[start:end]
                        current_confidence = (current_confidence + conf) / 2
                elif current_abbr is not None:
                    # End of an abbreviation
                    if self._is_valid_abbreviation(current_abbr):
                        abbreviations.append((
                            current_abbr,
                            current_start,
                            offset_mapping[i-1][1],
                            current_confidence
                        ))

                    # Reset
                    current_abbr = None
                    current_start = -1
                    current_confidence = 0.0

            # Check if there's an abbreviation at the end of the text
            if current_abbr is not None and self._is_valid_abbreviation(current_abbr):
                abbreviations.append((
                    current_abbr,
                    current_start,
                    offset_mapping[-1][1],
                    current_confidence
                ))

            return abbreviations

        except Exception as e:
            logger.error(f"Error in Transformer abbreviation detection: {str(e)}")
            return self._find_abbreviations_rule_based(text)

    def _find_abbreviations_rule_based(self, text: str) -> List[Tuple[str, int, int, float]]:
        """
        Find abbreviations in text using rule-based methods.

        Args:
            text: Text to analyze

        Returns:
            List of tuples containing (abbreviation, start_idx, end_idx, confidence)
        """
        abbreviations = []

        # Pattern for English abbreviations
        if self.language == "en":
            # Common patterns for English abbreviations
            patterns = [
                r'\b([A-Z][A-Z0-9]+)\b',  # All caps, like NASA, FBI
                r'\b([A-Z][a-z]*\.)+\b',  # Periods, like U.S.A., Ph.D.
                r'\b([A-Z][a-z0-9]*[A-Z][a-z0-9]*[A-Z][a-z0-9]*)\b'  # CamelCase with at least 3 capitals
            ]

            for pattern in patterns:
                for match in re.finditer(pattern, text):
                    abbr = match.group(1)
                    if self._is_valid_abbreviation(abbr):
                        abbreviations.append((
                            abbr,
                            match.start(1),
                            match.end(1),
                            0.8  # Default confidence for rule-based detection
                        ))

        # Pattern for Vietnamese abbreviations
        elif self.language == "vi":
            # Common patterns for Vietnamese abbreviations
            patterns = [
                r'\b([A-ZĐÁÀẢÃẠĂẮẰẲẴẶÂẤẦẨẪẬÉÈẺẼẸÊẾỀỂỄỆÍÌỈĨỊÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÚÙỦŨỤƯỨỪỬỮỰÝỲỶỸỴ][A-ZĐÁÀẢÃẠĂẮẰẲẴẶÂẤẦẨẪẬÉÈẺẼẸÊẾỀỂỄỆÍÌỈĨỊÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÚÙỦŨỤƯỨỪỬỮỰÝỲỶỸỴ0-9]+)\b',  # All caps
                r'\b([A-ZĐÁÀẢÃẠĂẮẰẲẴẶÂẤẦẨẪẬÉÈẺẼẸÊẾỀỂỄỆÍÌỈĨỊÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÚÙỦŨỤƯỨỪỬỮỰÝỲỶỸỴ][a-zđáàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵ]*\.)+\b'  # Periods
            ]

            for pattern in patterns:
                for match in re.finditer(pattern, text):
                    abbr = match.group(1)
                    if self._is_valid_abbreviation(abbr):
                        abbreviations.append((
                            abbr,
                            match.start(1),
                            match.end(1),
                            0.8
                        ))

        # Pattern for Chinese abbreviations
        elif self.language == "zh":
            # Common patterns for Chinese abbreviations
            # Chinese abbreviations are often formed by taking the first character of each word
            # This is a simplified approach and may need refinement
            for match in re.finditer(r'([一-龥]{2,4})', text):
                abbr = match.group(1)
                if self._is_valid_abbreviation(abbr):
                    abbreviations.append((
                        abbr,
                        match.start(1),
                        match.end(1),
                        0.7
                    ))

        # Pattern for Japanese abbreviations
        elif self.language == "ja":
            # Common patterns for Japanese abbreviations
            # Japanese abbreviations can be in katakana, hiragana, or kanji
            for match in re.finditer(r'([ァ-ヶー]{2,5}|[一-龥]{2,4})', text):
                abbr = match.group(1)
                if self._is_valid_abbreviation(abbr):
                    abbreviations.append((
                        abbr,
                        match.start(1),
                        match.end(1),
                        0.7
                    ))

        # Pattern for Korean abbreviations
        elif self.language == "ko":
            # Common patterns for Korean abbreviations
            # Korean abbreviations can be in hangul
            for match in re.finditer(r'([가-힣]{2,5})', text):
                abbr = match.group(1)
                if self._is_valid_abbreviation(abbr):
                    abbreviations.append((
                        abbr,
                        match.start(1),
                        match.end(1),
                        0.7
                    ))

        # Default pattern for other languages
        else:
            # Generic pattern for abbreviations in Latin script
            for match in re.finditer(r'\b([A-Z][A-Z0-9]+)\b', text):
                abbr = match.group(1)
                if self._is_valid_abbreviation(abbr):
                    abbreviations.append((
                        abbr,
                        match.start(1),
                        match.end(1),
                        0.7
                    ))

        return abbreviations

    def _is_valid_abbreviation(self, text: str) -> bool:
        """
        Check if a string is a valid abbreviation.

        Args:
            text: String to check

        Returns:
            True if valid abbreviation, False otherwise
        """
        # Empty string is not an abbreviation
        if not text:
            return False

        # Single character is not an abbreviation (except for special cases)
        if len(text) == 1 and text not in ['I', 'A']:
            return False

        # Very long strings are unlikely to be abbreviations
        if len(text) > 10:
            return False

        # For English and similar languages
        if self.language in ["en", "fr", "de", "es", "it"]:
            # Check if all uppercase or has periods
            return text.isupper() or '.' in text

        # For Vietnamese
        elif self.language == "vi":
            # Check if all uppercase or has periods
            return text.isupper() or '.' in text

        # For Chinese, Japanese, Korean
        elif self.language in ["zh", "ja", "ko"]:
            # These languages don't use uppercase, so we rely on length and other heuristics
            return len(text) >= 2 and len(text) <= 5

        # Default case
        return text.isupper() or '.' in text

    def learn_from_example(self, text: str, abbreviations: List[Tuple[str, int, int]]):
        """
        Learn from a new example.

        Args:
            text: Text containing abbreviations
            abbreviations: List of (abbreviation, start_idx, end_idx) tuples
        """
        if not self.enable_learning:
            return

        # Add to user data
        self.user_data.append({
            "text": text,
            "abbreviations": abbreviations
        })

        # Save user data
        self._save_user_data()

        # Retrain model if needed
        if len(self.user_data) % 10 == 0:  # Retrain every 10 examples
            logger.info(f"Retraining model with {len(self.user_data)} user examples")
            self._train_model(self.user_data)
