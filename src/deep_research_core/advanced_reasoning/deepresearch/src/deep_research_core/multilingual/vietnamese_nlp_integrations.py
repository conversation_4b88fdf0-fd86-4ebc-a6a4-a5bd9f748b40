"""
Vietnamese NLP integrations module.

This module provides integrations with popular Vietnamese NLP tools.
"""

import os
import importlib
import json
from typing import Dict, List, Any, Optional, Tuple, Union, Set
from functools import lru_cache

from ..utils.structured_logging import get_logger
from ..utils.vietnamese_utils import normalize_vietnamese_text, detect_vietnamese

logger = get_logger(__name__)

class VietnameseNLPIntegrations:
    """
    Vietnamese NLP integrations with popular tools.
    
    This class provides a unified interface to popular Vietnamese NLP tools
    such as underthesea, VnCoreNLP, and others.
    """
    
    # Singleton instances
    _instances = {}
    
    def __init__(
        self, 
        use_underthesea: bool = True,
        use_vncorenlp: bool = False,
        use_phonlp: bool = False,
        cache_results: bool = True,
        vncorenlp_dir: Optional[str] = None
    ):
        """
        Initialize VietnameseNLPIntegrations.
        
        Args:
            use_underthesea: Whether to use underthesea
            use_vncorenlp: Whether to use VnCoreNLP
            use_phonlp: Whether to use PhoNLP
            cache_results: Whether to cache results
            vncorenlp_dir: Directory containing VnCoreNLP models
        """
        self.use_underthesea = use_underthesea
        self.use_vncorenlp = use_vncorenlp
        self.use_phonlp = use_phonlp
        self.cache_results = cache_results
        self.vncorenlp_dir = vncorenlp_dir
        
        # Check for underthesea
        self.underthesea_available = False
        try:
            import underthesea
            self.underthesea = underthesea
            self.underthesea_available = True
            logger.info("Underthesea loaded successfully")
        except ImportError:
            logger.warning("Underthesea not available. Install with 'pip install underthesea'")
        
        # Check for VnCoreNLP
        self.vncorenlp_available = False
        if self.use_vncorenlp:
            try:
                from py_vncorenlp import VnCoreNLP
                if self.vncorenlp_dir is None:
                    # Default path
                    self.vncorenlp_dir = os.path.join(os.path.expanduser("~"), "vncorenlp")
                
                if os.path.exists(os.path.join(self.vncorenlp_dir, "VnCoreNLP-1.1.1.jar")):
                    try:
                        self.vncorenlp = VnCoreNLP(
                            annotators=["wseg", "pos", "ner", "parse"],
                            save_dir=self.vncorenlp_dir
                        )
                        self.vncorenlp_available = True
                        logger.info("VnCoreNLP loaded successfully")
                    except Exception as e:
                        logger.error(f"Error loading VnCoreNLP: {str(e)}")
                else:
                    logger.warning(
                        f"VnCoreNLP model not found at {self.vncorenlp_dir}. "
                        "Download from https://github.com/vncorenlp/VnCoreNLP"
                    )
            except ImportError:
                logger.warning("VnCoreNLP not available. Install with 'pip install py_vncorenlp'")
        
        # Check for PhoNLP
        self.phonlp_available = False
        if self.use_phonlp:
            try:
                import phonlp
                self.phonlp = phonlp
                self.phonlp_model = phonlp.load(save_dir=os.path.expanduser("~"))
                self.phonlp_available = True
                logger.info("PhoNLP loaded successfully")
            except ImportError:
                logger.warning("PhoNLP not available. Install with 'pip install phonlp'")
    
    @classmethod
    def get_instance(
        cls,
        use_underthesea: bool = True,
        use_vncorenlp: bool = False,
        use_phonlp: bool = False,
        cache_results: bool = True,
        vncorenlp_dir: Optional[str] = None
    ) -> 'VietnameseNLPIntegrations':
        """
        Get singleton instance of VietnameseNLPIntegrations.
        
        Args:
            use_underthesea: Whether to use underthesea
            use_vncorenlp: Whether to use VnCoreNLP
            use_phonlp: Whether to use PhoNLP
            cache_results: Whether to cache results
            vncorenlp_dir: Directory containing VnCoreNLP models
            
        Returns:
            Instance of VietnameseNLPIntegrations
        """
        key = f"{use_underthesea}_{use_vncorenlp}_{use_phonlp}_{vncorenlp_dir}"
        if key not in cls._instances:
            cls._instances[key] = cls(
                use_underthesea=use_underthesea,
                use_vncorenlp=use_vncorenlp,
                use_phonlp=use_phonlp,
                cache_results=cache_results,
                vncorenlp_dir=vncorenlp_dir
            )
        return cls._instances[key]
    
    @lru_cache(maxsize=1024)
    def word_segment(self, text: str, tool: Optional[str] = None) -> List[str]:
        """
        Perform word segmentation on Vietnamese text.
        
        Args:
            text: Vietnamese text to segment
            tool: Tool to use (underthesea, vncorenlp, phonlp)
            
        Returns:
            List of segmented words
        """
        if not detect_vietnamese(text):
            return text.split()
        
        # Normalize the text
        text = normalize_vietnamese_text(text)
        
        # Select tool
        if tool is None:
            # Use first available tool
            if self.underthesea_available:
                tool = "underthesea"
            elif self.vncorenlp_available:
                tool = "vncorenlp"
            elif self.phonlp_available:
                tool = "phonlp"
            else:
                logger.warning("No Vietnamese NLP tools available. Falling back to simple splitting.")
                return text.split()
        
        # Use the selected tool
        if tool == "underthesea" and self.underthesea_available:
            try:
                return self.underthesea.word_tokenize(text)
            except Exception as e:
                logger.error(f"Error in underthesea word segmentation: {str(e)}")
                return text.split()
        
        elif tool == "vncorenlp" and self.vncorenlp_available:
            try:
                result = self.vncorenlp.annotate(text)
                return result["tokens"][0]
            except Exception as e:
                logger.error(f"Error in VnCoreNLP word segmentation: {str(e)}")
                return text.split()
        
        elif tool == "phonlp" and self.phonlp_available:
            try:
                result = self.phonlp_model.annotate(text)
                return [token for sent in result["tokens"] for token in sent]
            except Exception as e:
                logger.error(f"Error in PhoNLP word segmentation: {str(e)}")
                return text.split()
        
        else:
            logger.warning(f"Requested tool '{tool}' not available. Falling back to simple splitting.")
            return text.split()
    
    @lru_cache(maxsize=1024)
    def pos_tag(self, text: str, tool: Optional[str] = None) -> List[Tuple[str, str]]:
        """
        Perform POS tagging on Vietnamese text.
        
        Args:
            text: Vietnamese text to tag
            tool: Tool to use (underthesea, vncorenlp, phonlp)
            
        Returns:
            List of word-tag pairs
        """
        if not detect_vietnamese(text):
            logger.warning("Non-Vietnamese text. POS tagging might be inaccurate.")
        
        # Normalize the text
        text = normalize_vietnamese_text(text)
        
        # Select tool
        if tool is None:
            # Use first available tool
            if self.underthesea_available:
                tool = "underthesea"
            elif self.vncorenlp_available:
                tool = "vncorenlp"
            elif self.phonlp_available:
                tool = "phonlp"
            else:
                logger.warning("No Vietnamese NLP tools available. Cannot perform POS tagging.")
                return [(word, "X") for word in text.split()]
        
        # Use the selected tool
        if tool == "underthesea" and self.underthesea_available:
            try:
                return self.underthesea.pos_tag(text)
            except Exception as e:
                logger.error(f"Error in underthesea POS tagging: {str(e)}")
                return [(word, "X") for word in text.split()]
        
        elif tool == "vncorenlp" and self.vncorenlp_available:
            try:
                result = self.vncorenlp.annotate(text)
                return [(word, pos) for word, pos in zip(result["tokens"][0], result["pos"][0])]
            except Exception as e:
                logger.error(f"Error in VnCoreNLP POS tagging: {str(e)}")
                return [(word, "X") for word in text.split()]
        
        elif tool == "phonlp" and self.phonlp_available:
            try:
                result = self.phonlp_model.annotate(text)
                return [(token, pos) for sent_tokens, sent_pos in zip(result["tokens"], result["pos"]) 
                        for token, pos in zip(sent_tokens, sent_pos)]
            except Exception as e:
                logger.error(f"Error in PhoNLP POS tagging: {str(e)}")
                return [(word, "X") for word in text.split()]
        
        else:
            logger.warning(f"Requested tool '{tool}' not available. Cannot perform POS tagging.")
            return [(word, "X") for word in text.split()]
    
    @lru_cache(maxsize=1024)
    def ner(self, text: str, tool: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Perform Named Entity Recognition on Vietnamese text.
        
        Args:
            text: Vietnamese text to analyze
            tool: Tool to use (underthesea, vncorenlp, phonlp)
            
        Returns:
            List of named entities with their types
        """
        if not detect_vietnamese(text):
            logger.warning("Non-Vietnamese text. NER might be inaccurate.")
        
        # Normalize the text
        text = normalize_vietnamese_text(text)
        
        # Select tool
        if tool is None:
            # Use first available tool
            if self.underthesea_available:
                tool = "underthesea"
            elif self.vncorenlp_available:
                tool = "vncorenlp"
            elif self.phonlp_available:
                tool = "phonlp"
            else:
                logger.warning("No Vietnamese NLP tools available. Cannot perform NER.")
                return []
        
        # Use the selected tool
        if tool == "underthesea" and self.underthesea_available:
            try:
                ner_result = self.underthesea.ner(text)
                entities = []
                current_entity = None
                
                for word, tag in ner_result:
                    if tag != "O":
                        entity_type = tag.split("-")[1] if "-" in tag else tag
                        if tag.startswith("B-") or current_entity is None:
                            if current_entity is not None:
                                entities.append(current_entity)
                            current_entity = {"entity": word, "type": entity_type, "start": len(entities)}
                        elif tag.startswith("I-") and current_entity is not None:
                            current_entity["entity"] += " " + word
                    else:
                        if current_entity is not None:
                            entities.append(current_entity)
                            current_entity = None
                
                if current_entity is not None:
                    entities.append(current_entity)
                    
                return entities
            except Exception as e:
                logger.error(f"Error in underthesea NER: {str(e)}")
                return []
        
        elif tool == "vncorenlp" and self.vncorenlp_available:
            try:
                result = self.vncorenlp.annotate(text)
                entities = []
                
                tokens = result["tokens"][0]
                ner_tags = result["ner"][0]
                
                current_entity = None
                for i, (token, tag) in enumerate(zip(tokens, ner_tags)):
                    if tag != "O":
                        entity_type = tag.split("-")[1] if "-" in tag else tag
                        if tag.startswith("B-") or current_entity is None:
                            if current_entity is not None:
                                entities.append(current_entity)
                            current_entity = {"entity": token, "type": entity_type, "start": i}
                        elif tag.startswith("I-") and current_entity is not None:
                            current_entity["entity"] += " " + token
                    else:
                        if current_entity is not None:
                            entities.append(current_entity)
                            current_entity = None
                
                if current_entity is not None:
                    entities.append(current_entity)
                    
                return entities
            except Exception as e:
                logger.error(f"Error in VnCoreNLP NER: {str(e)}")
                return []
        
        elif tool == "phonlp" and self.phonlp_available:
            try:
                result = self.phonlp_model.annotate(text)
                entities = []
                
                offset = 0
                for sent_tokens, sent_ner in zip(result["tokens"], result["ner"]):
                    current_entity = None
                    
                    for i, (token, tag) in enumerate(zip(sent_tokens, sent_ner)):
                        if tag != "O":
                            entity_type = tag.split("-")[1] if "-" in tag else tag
                            if tag.startswith("B-") or current_entity is None:
                                if current_entity is not None:
                                    entities.append(current_entity)
                                current_entity = {"entity": token, "type": entity_type, "start": offset + i}
                            elif tag.startswith("I-") and current_entity is not None:
                                current_entity["entity"] += " " + token
                        else:
                            if current_entity is not None:
                                entities.append(current_entity)
                                current_entity = None
                    
                    if current_entity is not None:
                        entities.append(current_entity)
                    
                    offset += len(sent_tokens)
                    
                return entities
            except Exception as e:
                logger.error(f"Error in PhoNLP NER: {str(e)}")
                return []
        
        else:
            logger.warning(f"Requested tool '{tool}' not available. Cannot perform NER.")
            return []
    
    @lru_cache(maxsize=1024)
    def sentiment_analysis(self, text: str) -> Dict[str, Any]:
        """
        Perform sentiment analysis on Vietnamese text.
        
        Args:
            text: Vietnamese text to analyze
            
        Returns:
            Dictionary with sentiment analysis results
        """
        if not detect_vietnamese(text):
            logger.warning("Non-Vietnamese text. Sentiment analysis might be inaccurate.")
        
        # Normalize the text
        text = normalize_vietnamese_text(text)
        
        # Only underthesea supports sentiment analysis
        if self.underthesea_available:
            try:
                sentiment = self.underthesea.sentiment(text)
                
                # Convert to standard format
                if isinstance(sentiment, str):
                    # Old underthesea versions return a string
                    label = sentiment
                    score = 1.0 if sentiment == "positive" else (0.0 if sentiment == "negative" else 0.5)
                else:
                    # Newer versions return a dictionary
                    label = sentiment["label"]
                    score = sentiment.get("score", 1.0)
                
                return {
                    "label": label,
                    "score": score,
                    "positive": label == "positive",
                    "negative": label == "negative",
                    "neutral": label == "neutral"
                }
            except Exception as e:
                logger.error(f"Error in underthesea sentiment analysis: {str(e)}")
                return {"label": "unknown", "score": 0.5, "positive": False, "negative": False, "neutral": True}
        else:
            logger.warning("Underthesea not available. Cannot perform sentiment analysis.")
            return {"label": "unknown", "score": 0.5, "positive": False, "negative": False, "neutral": True}
    
    def get_available_tools(self) -> Dict[str, bool]:
        """
        Get the availability status of Vietnamese NLP tools.
        
        Returns:
            Dictionary with tool availability
        """
        return {
            "underthesea": self.underthesea_available,
            "vncorenlp": self.vncorenlp_available,
            "phonlp": self.phonlp_available
        }
    
    @lru_cache(maxsize=1024)
    def analyze_text(self, text: str, tools: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Perform comprehensive analysis on Vietnamese text.
        
        Args:
            text: Vietnamese text to analyze
            tools: List of analysis types to perform
            
        Returns:
            Dictionary with analysis results
        """
        if not tools:
            tools = ["word_segment", "pos_tag", "ner", "sentiment"]
        
        results = {}
        
        if "word_segment" in tools:
            results["word_segment"] = self.word_segment(text)
        
        if "pos_tag" in tools:
            results["pos_tag"] = self.pos_tag(text)
        
        if "ner" in tools:
            results["ner"] = self.ner(text)
        
        if "sentiment" in tools:
            results["sentiment"] = self.sentiment_analysis(text)
        
        return results 