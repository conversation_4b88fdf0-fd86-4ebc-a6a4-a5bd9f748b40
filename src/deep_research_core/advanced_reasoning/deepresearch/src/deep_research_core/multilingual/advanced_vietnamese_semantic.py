"""
Advanced Vietnamese Semantic Processing for WebSearchAgent.

Module này cung cấp các công cụ xử lý ngữ nghĩa tiếng Việt nâng cao cho WebSearchAgent,
tập trung vào việc cải thiện các truy vấn tìm kiếm, phân tích kết quả và xếp hạng
các trang web bằng kỹ thuật xử lý ngôn ngữ tự nhiên tiên tiến.
"""

import re
import logging
from typing import List, Dict, Tuple, Any, Optional, Set, Union
import numpy as np
from dataclasses import dataclass, field

from ..utils.structured_logging import get_logger
from ..utils.vietnamese_utils import (
    detect_vietnamese, normalize_vietnamese_text, 
    extract_vietnamese_keywords, detect_vietnamese_dialect
)
from ..utils.text_similarity import semantic_similarity
from ..multilingual.enhanced_vietnamese_embeddings import EnhancedVietnameseEmbeddings
from ..multilingual.vietnamese_prompt_optimizer import VietnamesePromptOptimizer

# Create logger
logger = get_logger(__name__)

@dataclass
class VietnameseQueryContext:
    """
    Lớp lưu trữ ngữ cảnh của truy vấn tiếng Việt.

    Attributes:
        original_query: Truy vấn gốc
        normalized_query: Truy vấn đã được chuẩn hóa
        keywords: Các từ khóa chính
        expanded_keywords: Các từ khóa đã được mở rộng
        dialect: Phương ngữ phát hiện được
        domain: Lĩnh vực phát hiện được
        semantic_variations: Các biến thể ngữ nghĩa của truy vấn
        context_data: Dữ liệu ngữ cảnh bổ sung
    """
    original_query: str
    normalized_query: str = ""
    keywords: List[str] = field(default_factory=list)
    expanded_keywords: List[str] = field(default_factory=list)
    dialect: Optional[str] = None
    domain: Optional[str] = None
    semantic_variations: List[str] = field(default_factory=list)
    context_data: Dict[str, Any] = field(default_factory=dict)

class AdvancedVietnameseSemanticProcessor:
    """
    Bộ xử lý ngữ nghĩa tiếng Việt nâng cao cho WebSearchAgent.

    Lớp này cung cấp các phương thức để xử lý ngữ nghĩa tiếng Việt nâng cao,
    bao gồm phân tích ngữ nghĩa, mở rộng truy vấn, và đánh giá kết quả tìm kiếm.
    """

    # Mô hình embedding mặc định
    DEFAULT_EMBEDDING_MODEL = "phobert"

    # Từ điển mở rộng truy vấn theo lĩnh vực
    DOMAIN_EXPANSION_TERMS = {
        "y_te": [
            "sức khỏe", "bệnh", "triệu chứng", "điều trị", "thuốc",
            "bác sĩ", "bệnh viện", "phòng khám", "chẩn đoán", "phẫu thuật"
        ],
        "giao_duc": [
            "học tập", "giáo dục", "trường học", "đại học", "giảng dạy",
            "học sinh", "sinh viên", "giáo viên", "kiến thức", "kỹ năng"
        ],
        "cong_nghe": [
            "công nghệ", "phần mềm", "phần cứng", "máy tính", "internet",
            "ứng dụng", "AI", "dữ liệu", "thuật toán", "lập trình"
        ],
        "kinh_te": [
            "kinh tế", "tài chính", "đầu tư", "thị trường", "doanh nghiệp",
            "lợi nhuận", "cổ phiếu", "ngân hàng", "lãi suất", "thuế"
        ],
        "luat": [
            "pháp luật", "luật", "quy định", "điều khoản", "hợp đồng",
            "quyền", "nghĩa vụ", "tòa án", "khiếu nại", "bồi thường"
        ]
    }

    # Từ đồng nghĩa phổ biến tiếng Việt
    VIETNAMESE_SYNONYMS = {
        # Động từ
        "làm": ["thực hiện", "tiến hành", "thi hành", "thi công"],
        "nói": ["phát biểu", "trình bày", "diễn đạt", "phát ngôn", "thuyết trình"],
        "thấy": ["nhìn", "quan sát", "nhận thấy", "phát hiện", "chứng kiến"],
        "hiểu": ["lĩnh hội", "thấu hiểu", "nắm bắt", "lãnh hội", "thông suốt"],
        "giúp": ["hỗ trợ", "trợ giúp", "tiếp sức", "giúp đỡ", "hậu thuẫn"],
        "tạo": ["tạo ra", "xây dựng", "hình thành", "thiết lập", "sáng tạo"],
        "phát triển": ["mở rộng", "tiến triển", "nâng cao", "cải thiện", "tăng trưởng"],
        "sử dụng": ["dùng", "áp dụng", "vận dụng", "ứng dụng", "tận dụng"],
        "cho": ["tặng", "biếu", "cấp", "phát", "cung cấp"],
        "tìm": ["kiếm", "tìm kiếm", "tìm hiểu", "khám phá", "truy tìm"],

        # Danh từ
        "vấn đề": ["khó khăn", "trở ngại", "thách thức", "bài toán", "tình huống"],
        "thời gian": ["thời điểm", "thời kỳ", "giai đoạn", "khoảng thời gian", "thời hạn"],
        "công việc": ["nhiệm vụ", "trách nhiệm", "công tác", "việc làm", "nghề nghiệp"],
        "ý kiến": ["quan điểm", "nhận định", "góc nhìn", "tư tưởng", "ý tưởng"],
        "phương pháp": ["cách thức", "kỹ thuật", "phương thức", "quy trình", "cách làm"],
        "dữ liệu": ["thông tin", "số liệu", "tài liệu", "tư liệu", "dữ kiện"],
        "kết quả": ["thành quả", "thành tựu", "hiệu quả", "hệ quả", "kết luận"],
        "lợi ích": ["lợi điểm", "ưu điểm", "mặt tích cực", "mặt thuận lợi", "điểm mạnh"],
        "ảnh hưởng": ["tác động", "tác dụng", "hiệu ứng", "hậu quả", "ảnh hưởng"],
        "nhu cầu": ["đòi hỏi", "yêu cầu", "mong muốn", "mong đợi", "kỳ vọng"],

        # Tính từ
        "tốt": ["hay", "giỏi", "xuất sắc", "hiệu quả", "chất lượng"],
        "quan trọng": ["thiết yếu", "then chốt", "trọng yếu", "cốt lõi", "then chốt"],
        "mới": ["hiện đại", "tiên tiến", "cập nhật", "tân tiến", "đương đại"],
        "khó": ["phức tạp", "thách thức", "gian nan", "khó khăn", "gay go"],
        "nhanh": ["mau", "lẹ", "tức thì", "tức tốc", "tức thời"],
        "lớn": ["to", "rộng", "khổng lồ", "đồ sộ", "vĩ đại"],
        "nhỏ": ["bé", "nhỏ bé", "tí hon", "li ti", "nhỏ nhắn"],
        "tệ": ["xấu", "kém", "tồi", "dở", "yếu"],
        "đẹp": ["xinh", "đẹp đẽ", "mỹ lệ", "tuyệt mỹ", "lộng lẫy"],
        "thông minh": ["thông thái", "sáng dạ", "sáng suốt", "thông tuệ", "minh mẫn"]
    }

    def __init__(
        self, 
        embedding_model: str = DEFAULT_EMBEDDING_MODEL, 
        use_advanced_embeddings: bool = True, 
        optimize_queries: bool = True,
        max_variations: int = 3
    ):
        """
        Khởi tạo bộ xử lý ngữ nghĩa tiếng Việt nâng cao.

        Args:
            embedding_model: Tên mô hình embedding (mặc định: "phobert")
            use_advanced_embeddings: Sử dụng mô hình embedding nâng cao
            optimize_queries: Tự động tối ưu hóa truy vấn
            max_variations: Số lượng biến thể tối đa cho mỗi truy vấn
        """
        self.use_advanced_embeddings = use_advanced_embeddings
        self.optimize_queries = optimize_queries
        self.max_variations = max_variations

        # Khởi tạo prompt optimizer
        self.prompt_optimizer = VietnamesePromptOptimizer.get_instance()

        # Khởi tạo mô hình embedding
        if use_advanced_embeddings:
            try:
                self.embedding_model = EnhancedVietnameseEmbeddings.get_embedding_model(
                    model_name=embedding_model,
                    use_preprocessing=True
                )
                logger.info(f"Initialized EnhancedVietnameseEmbeddings with model: {embedding_model}")
            except Exception as e:
                logger.warning(f"Error initializing advanced embeddings: {str(e)}. Falling back to basic similarity.")
                self.use_advanced_embeddings = False
        else:
            self.embedding_model = None

        logger.info("Initialized AdvancedVietnameseSemanticProcessor")

    def process_query(self, query: str) -> VietnameseQueryContext:
        """
        Xử lý truy vấn tiếng Việt và tạo ngữ cảnh ngữ nghĩa.

        Args:
            query: Truy vấn cần xử lý

        Returns:
            VietnameseQueryContext chứa thông tin ngữ cảnh của truy vấn
        """
        # Tạo ngữ cảnh truy vấn cơ bản
        query_context = VietnameseQueryContext(original_query=query)

        # Chuẩn hóa truy vấn
        query_context.normalized_query = normalize_vietnamese_text(query)

        # Phát hiện phương ngữ
        query_context.dialect = detect_vietnamese_dialect(query)

        # Phát hiện lĩnh vực
        query_context.domain = self.prompt_optimizer.detect_domain(query)

        # Trích xuất từ khóa chính
        query_context.keywords = extract_vietnamese_keywords(query, num_keywords=5)

        # Mở rộng từ khóa theo lĩnh vực
        query_context.expanded_keywords = self._expand_keywords(
            query_context.keywords, 
            query_context.domain
        )

        # Tạo các biến thể ngữ nghĩa của truy vấn
        query_context.semantic_variations = self._generate_query_variations(
            query_context.normalized_query,
            query_context.keywords,
            query_context.expanded_keywords
        )

        return query_context

    def _expand_keywords(self, keywords: List[str], domain: Optional[str]) -> List[str]:
        """
        Mở rộng từ khóa dựa trên lĩnh vực và từ đồng nghĩa.

        Args:
            keywords: Danh sách từ khóa cần mở rộng
            domain: Lĩnh vực của truy vấn

        Returns:
            Danh sách từ khóa đã mở rộng
        """
        expanded_keywords = list(keywords)  # Bắt đầu với các từ khóa hiện có

        # Thêm từ khóa theo lĩnh vực
        if domain and domain in self.DOMAIN_EXPANSION_TERMS:
            domain_terms = self.DOMAIN_EXPANSION_TERMS[domain]
            for term in domain_terms:
                if term not in expanded_keywords:
                    expanded_keywords.append(term)

        # Thêm từ đồng nghĩa
        synonym_expanded = []
        for keyword in keywords:
            if keyword in self.VIETNAMESE_SYNONYMS:
                # Thêm tối đa 2 từ đồng nghĩa cho mỗi từ khóa
                synonyms = self.VIETNAMESE_SYNONYMS[keyword][:2]
                synonym_expanded.extend([s for s in synonyms if s not in expanded_keywords])

        expanded_keywords.extend(synonym_expanded)

        # Giới hạn số lượng từ khóa mở rộng
        return expanded_keywords[:15]  # Giới hạn tối đa 15 từ khóa

    def _generate_query_variations(
        self, 
        query: str, 
        keywords: List[str], 
        expanded_keywords: List[str]
    ) -> List[str]:
        """
        Tạo các biến thể ngữ nghĩa của truy vấn.

        Args:
            query: Truy vấn đã chuẩn hóa
            keywords: Từ khóa chính
            expanded_keywords: Từ khóa đã mở rộng

        Returns:
            Danh sách các biến thể của truy vấn
        """
        variations = []

        # Biến thể 1: Truy vấn gốc kết hợp với từ khóa chính
        if keywords:
            variation1 = f"{query} {' '.join(keywords[:3])}"
            variations.append(variation1)

        # Biến thể 2: Mở rộng truy vấn với từ khóa bổ sung
        if expanded_keywords:
            additional_keywords = [k for k in expanded_keywords if k not in keywords][:3]
            if additional_keywords:
                variation2 = f"{query} {' '.join(additional_keywords)}"
                variations.append(variation2)

        # Biến thể 3: Truy vấn cụ thể hơn với cấu trúc "Tìm kiếm về [query]"
        variation3 = f"Tìm kiếm chi tiết về {query}"
        variations.append(variation3)

        # Loại bỏ trùng lặp và giới hạn số lượng biến thể
        unique_variations = [v for v in variations if v != query]
        return unique_variations[:self.max_variations]

    def optimize_search_query(self, query: str) -> str:
        """
        Tối ưu hóa truy vấn tìm kiếm tiếng Việt.

        Args:
            query: Truy vấn tìm kiếm gốc

        Returns:
            Truy vấn đã được tối ưu hóa
        """
        if not detect_vietnamese(query):
            return query

        # Xử lý truy vấn để tạo ngữ cảnh
        query_context = self.process_query(query)

        # Truy vấn đã chuẩn hóa
        optimized_query = query_context.normalized_query

        # Thêm từ khóa quan trọng
        if query_context.keywords:
            important_keywords = query_context.keywords[:2]  # Lấy 2 từ khóa quan trọng nhất
            for keyword in important_keywords:
                if keyword not in optimized_query:
                    optimized_query += f" {keyword}"

        return optimized_query.strip()

    def generate_alternative_queries(self, query: str, max_alternatives: int = 3) -> List[str]:
        """
        Tạo các truy vấn thay thế cho truy vấn gốc.

        Args:
            query: Truy vấn gốc
            max_alternatives: Số lượng truy vấn thay thế tối đa

        Returns:
            Danh sách các truy vấn thay thế
        """
        if not detect_vietnamese(query):
            return [query]  # Trả về truy vấn gốc nếu không phải tiếng Việt

        # Xử lý truy vấn để tạo ngữ cảnh
        query_context = self.process_query(query)

        # Lấy các biến thể từ ngữ cảnh
        alternatives = query_context.semantic_variations.copy()

        # Nếu không đủ biến thể, tạo thêm
        if len(alternatives) < max_alternatives:
            # Tạo truy vấn thay thế bằng cách kết hợp từ khóa và từ khóa mở rộng
            for i in range(len(alternatives), max_alternatives):
                # Lựa chọn ngẫu nhiên từ khóa và từ khóa mở rộng
                import random
                
                all_keywords = query_context.keywords + query_context.expanded_keywords
                random.shuffle(all_keywords)
                
                # Tạo truy vấn thay thế
                alt_query = f"{query_context.normalized_query} {' '.join(all_keywords[:3])}"
                
                if alt_query not in alternatives:
                    alternatives.append(alt_query)

        return alternatives[:max_alternatives]

    def rank_search_results(
        self, 
        query: str, 
        results: List[Dict[str, Any]], 
        use_embeddings: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Xếp hạng kết quả tìm kiếm dựa trên độ tương đồng ngữ nghĩa.

        Args:
            query: Truy vấn tìm kiếm
            results: Danh sách kết quả tìm kiếm
            use_embeddings: Sử dụng embeddings cho việc xếp hạng

        Returns:
            Danh sách kết quả tìm kiếm đã được xếp hạng
        """
        if not results:
            return results

        # Nếu không phải tiếng Việt, trả về kết quả gốc
        if not detect_vietnamese(query):
            return results

        # Xử lý truy vấn
        query_context = self.process_query(query)

        # Tính điểm cho mỗi kết quả
        scored_results = []
        for result in results:
            # Lấy nội dung để so sánh
            title = result.get("title", "")
            snippet = result.get("snippet", "")
            content = f"{title} {snippet}"

            # Tính điểm tương đồng
            if use_embeddings and self.use_advanced_embeddings and self.embedding_model:
                try:
                    # Sử dụng mô hình embedding nâng cao
                    similarity_score = self.embedding_model.calculate_similarity(
                        query_context.normalized_query, 
                        content
                    )
                except Exception as e:
                    logger.warning(f"Error calculating embedding similarity: {str(e)}. Falling back to basic similarity.")
                    # Sử dụng phương pháp dự phòng
                    similarity_score = semantic_similarity(query_context.normalized_query, content)
            else:
                # Sử dụng phương pháp đơn giản hơn
                similarity_score = semantic_similarity(query_context.normalized_query, content)

            # Tính điểm từ khóa
            keyword_score = self._calculate_keyword_score(content, query_context.keywords, query_context.expanded_keywords)

            # Tính điểm tổng hợp (70% tương đồng ngữ nghĩa, 30% điểm từ khóa)
            final_score = 0.7 * similarity_score + 0.3 * keyword_score

            # Thêm điểm vào kết quả
            result_with_score = result.copy()
            result_with_score["semantic_score"] = final_score
            scored_results.append(result_with_score)

        # Sắp xếp kết quả theo điểm giảm dần
        ranked_results = sorted(scored_results, key=lambda x: x.get("semantic_score", 0), reverse=True)
        return ranked_results

    def _calculate_keyword_score(
        self, 
        content: str, 
        keywords: List[str], 
        expanded_keywords: List[str]
    ) -> float:
        """
        Tính điểm dựa trên sự xuất hiện của từ khóa trong nội dung.

        Args:
            content: Nội dung cần tính điểm
            keywords: Danh sách từ khóa chính
            expanded_keywords: Danh sách từ khóa mở rộng

        Returns:
            Điểm từ khóa (0-1)
        """
        content_lower = content.lower()
        
        # Trọng số cho từ khóa chính và từ khóa mở rộng
        primary_weight = 0.7
        expanded_weight = 0.3
        
        # Tính điểm cho từ khóa chính
        primary_score = 0
        for keyword in keywords:
            if keyword.lower() in content_lower:
                primary_score += 1
        
        # Chuẩn hóa điểm từ khóa chính
        if keywords:
            primary_score /= len(keywords)
        else:
            primary_score = 0
        
        # Tính điểm cho từ khóa mở rộng
        expanded_score = 0
        expanded_keywords_filtered = [k for k in expanded_keywords if k not in keywords]
        for keyword in expanded_keywords_filtered:
            if keyword.lower() in content_lower:
                expanded_score += 1
        
        # Chuẩn hóa điểm từ khóa mở rộng
        if expanded_keywords_filtered:
            expanded_score /= len(expanded_keywords_filtered)
        else:
            expanded_score = 0
        
        # Tính điểm tổng hợp
        final_score = primary_weight * primary_score + expanded_weight * expanded_score
        return final_score

    def enrich_search_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Làm phong phú kết quả tìm kiếm với dữ liệu bổ sung.

        Args:
            results: Danh sách kết quả tìm kiếm

        Returns:
            Danh sách kết quả tìm kiếm đã được làm phong phú
        """
        if not results:
            return results

        enriched_results = []
        for result in results:
            enriched_result = result.copy()
            
            # Trích xuất thông tin từ nội dung
            snippet = result.get("snippet", "")
            title = result.get("title", "")
            
            # Phát hiện ngôn ngữ của kết quả
            is_vietnamese = detect_vietnamese(snippet) or detect_vietnamese(title)
            enriched_result["is_vietnamese"] = is_vietnamese
            
            # Nếu là tiếng Việt, thêm thông tin phương ngữ
            if is_vietnamese:
                dialect = detect_vietnamese_dialect(snippet)
                if dialect:
                    enriched_result["dialect"] = dialect
            
            # Trích xuất từ khóa từ nội dung
            if is_vietnamese:
                try:
                    content_keywords = extract_vietnamese_keywords(snippet, num_keywords=5)
                    enriched_result["content_keywords"] = content_keywords
                except Exception as e:
                    logger.warning(f"Error extracting keywords: {str(e)}")
            
            enriched_results.append(enriched_result)
        
        return enriched_results 