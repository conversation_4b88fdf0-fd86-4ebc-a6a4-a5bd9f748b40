"""
Vietnamese Domain-specific Compound Words.

This module provides specialized compound word dictionaries for different domains
in Vietnamese language. These domain-specific dictionaries enhance the compound word
processing capabilities of the Vietnamese Compound Processor.
"""

from typing import Dict, List, Set, Optional


class VietnameseDomainCompounds:
    """
    Domain-specific compound word collections for Vietnamese language.
    
    This class provides specialized compound dictionaries for various domains
    like medical, legal, technical, education, etc. to enhance the compound
    word processing capabilities in domain-specific contexts.
    """
    
    # Y tế / Medical domain
    MEDICAL_COMPOUNDS = {
        "sức khỏe": "coordinate",
        "bệnh tật": "coordinate",
        "y tế": "coordinate",
        "nhân viên y tế": "subordinate",
        "bệnh viện": "subordinate",
        "phòng khám": "subordinate",
        "phòng mổ": "subordinate",
        "phòng cấp cứu": "subordinate",
        "bác sĩ": "subordinate",
        "y tá": "coordinate",
        "dược sĩ": "subordinate",
        "thuốc men": "coordinate",
        "kháng sinh": "subordinate",
        "kháng viêm": "subordinate",
        "kháng histamin": "subordinate",
        "huyết áp": "subordinate",
        "tim mạch": "coordinate",
        "mạch máu": "subordinate",
        "hô hấp": "coordinate",
        "phổi": "subordinate",
        "thần kinh": "coordinate",
        "não bộ": "subordinate",
        "tiêu hóa": "coordinate",
        "dạ dày": "subordinate",
        "ruột già": "subordinate",
        "ruột non": "subordinate",
        "tiết niệu": "coordinate",
        "thận": "subordinate",
        "gan mật": "coordinate",
        "tuyến giáp": "subordinate",
        "nội tiết": "coordinate",
        "đái tháo đường": "subordinate",
        "tiểu đường": "subordinate",
        "ung thư": "subordinate",
        "hóa trị": "subordinate",
        "xạ trị": "subordinate",
        "tế bào": "subordinate",
        "tế bào gốc": "subordinate",
        "hệ miễn dịch": "subordinate",
        "kháng thể": "subordinate",
        "kháng nguyên": "subordinate",
        "vắc xin": "subordinate",
        "siêu âm": "subordinate",
        "chụp X-quang": "subordinate",
        "chụp cộng hưởng từ": "subordinate",
        "xét nghiệm máu": "subordinate"
    }
    
    # Pháp luật / Legal domain
    LEGAL_COMPOUNDS = {
        "pháp luật": "coordinate",
        "luật pháp": "coordinate",
        "điều luật": "subordinate",
        "bộ luật": "subordinate",
        "luật sư": "subordinate",
        "thẩm phán": "subordinate",
        "công tố viên": "subordinate",
        "tòa án": "subordinate",
        "tòa án nhân dân": "subordinate",
        "tòa phúc thẩm": "subordinate",
        "tòa thượng thẩm": "subordinate",
        "viện kiểm sát": "subordinate",
        "nguyên đơn": "subordinate",
        "bị đơn": "subordinate",
        "bị cáo": "subordinate",
        "nguyên cáo": "subordinate",
        "nhân chứng": "subordinate",
        "bằng chứng": "subordinate",
        "chứng cứ": "coordinate",
        "lời khai": "subordinate",
        "biên bản": "subordinate",
        "hợp đồng": "subordinate",
        "vi phạm": "subordinate",
        "vi phạm hành chính": "subordinate",
        "tội phạm": "subordinate",
        "hình sự": "coordinate",
        "dân sự": "coordinate",
        "hành chính": "coordinate",
        "thương mại": "coordinate",
        "lao động": "coordinate",
        "hôn nhân": "subordinate",
        "ly hôn": "subordinate",
        "thừa kế": "subordinate",
        "di chúc": "subordinate",
        "quyền sở hữu": "subordinate",
        "sở hữu trí tuệ": "subordinate",
        "bản quyền": "subordinate",
        "nhãn hiệu": "subordinate",
        "sáng chế": "subordinate",
        "khiếu nại": "subordinate",
        "tố cáo": "subordinate",
        "tranh chấp": "subordinate",
        "hòa giải": "coordinate",
        "trọng tài": "subordinate",
        "bồi thường": "subordinate"
    }
    
    # Công nghệ / Technology domain
    TECHNOLOGY_COMPOUNDS = {
        "công nghệ": "coordinate",
        "khoa học": "coordinate",
        "trí tuệ nhân tạo": "subordinate",
        "học máy": "subordinate",
        "học sâu": "subordinate",
        "mạng nơ-ron": "subordinate",
        "mạng nơ-ron nhân tạo": "subordinate",
        "mạng nơ-ron sâu": "subordinate",
        "thị giác máy tính": "subordinate",
        "nhận dạng hình ảnh": "subordinate",
        "xử lý ảnh": "subordinate",
        "xử lý ngôn ngữ tự nhiên": "subordinate",
        "xử lý tiếng nói": "subordinate",
        "công nghệ thông tin": "subordinate",
        "khoa học máy tính": "subordinate",
        "hệ điều hành": "subordinate",
        "phần mềm": "subordinate",
        "phần cứng": "subordinate",
        "mã nguồn": "subordinate",
        "mã nguồn mở": "subordinate",
        "cơ sở dữ liệu": "subordinate",
        "dữ liệu lớn": "subordinate",
        "kho dữ liệu": "subordinate",
        "phân tích dữ liệu": "subordinate",
        "khai phá dữ liệu": "subordinate",
        "điện toán đám mây": "subordinate",
        "học tăng cường": "subordinate",
        "học có giám sát": "subordinate", 
        "học không giám sát": "subordinate",
        "internet vạn vật": "subordinate",
        "chuỗi khối": "subordinate",
        "thực tế ảo": "subordinate",
        "thực tế tăng cường": "subordinate",
        "trợ lý ảo": "subordinate",
        "robot thông minh": "subordinate",
        "hệ thống chuyên gia": "subordinate",
        "dịch máy": "subordinate",
        "tóm tắt văn bản": "subordinate",
        "phân loại văn bản": "subordinate",
        "phân tích tình cảm": "subordinate",
        "trả lời câu hỏi": "subordinate",
        "máy chủ": "subordinate",
        "máy trạm": "subordinate",
        "mạng lưới": "subordinate"
    }
    
    # Giáo dục / Education domain
    EDUCATION_COMPOUNDS = {
        "giáo dục": "coordinate",
        "đào tạo": "coordinate",
        "giảng dạy": "coordinate",
        "học tập": "coordinate",
        "trường học": "subordinate",
        "trường mầm non": "subordinate",
        "trường tiểu học": "subordinate",
        "trường trung học": "subordinate",
        "trường trung học cơ sở": "subordinate",
        "trường trung học phổ thông": "subordinate",
        "trường đại học": "subordinate",
        "trường cao đẳng": "subordinate",
        "trường dạy nghề": "subordinate",
        "giáo viên": "subordinate",
        "học sinh": "subordinate",
        "sinh viên": "subordinate",
        "lớp học": "subordinate",
        "phòng học": "subordinate",
        "bài giảng": "subordinate",
        "giáo trình": "subordinate",
        "sách giáo khoa": "subordinate",
        "sách tham khảo": "subordinate",
        "kiểm tra": "coordinate",
        "bài kiểm tra": "subordinate",
        "thi cử": "coordinate",
        "kỳ thi": "subordinate",
        "điểm số": "subordinate",
        "học bổng": "subordinate",
        "học phí": "subordinate",
        "chương trình học": "subordinate",
        "môn học": "subordinate",
        "nghiên cứu": "coordinate",
        "nghiên cứu sinh": "subordinate",
        "luận văn": "subordinate",
        "luận án": "subordinate",
        "khóa luận": "subordinate",
        "bằng cấp": "subordinate",
        "bằng tốt nghiệp": "subordinate",
        "chứng chỉ": "subordinate",
        "đại học": "coordinate",
        "cao đẳng": "coordinate",
        "sau đại học": "subordinate",
        "tiến sĩ": "subordinate",
        "thạc sĩ": "subordinate"
    }
    
    # Kinh tế / Economics domain
    ECONOMICS_COMPOUNDS = {
        "kinh tế": "coordinate",
        "tài chính": "coordinate",
        "kế toán": "coordinate",
        "ngân hàng": "subordinate",
        "thị trường": "subordinate",
        "thị trường chứng khoán": "subordinate",
        "chứng khoán": "subordinate",
        "cổ phiếu": "subordinate",
        "trái phiếu": "subordinate",
        "quỹ đầu tư": "subordinate",
        "đầu tư": "coordinate",
        "nhà đầu tư": "subordinate",
        "doanh nghiệp": "subordinate",
        "công ty": "subordinate",
        "công ty cổ phần": "subordinate",
        "công ty trách nhiệm hữu hạn": "subordinate",
        "doanh thu": "subordinate",
        "lợi nhuận": "subordinate",
        "chi phí": "subordinate",
        "thuế thu nhập": "subordinate",
        "thuế giá trị gia tăng": "subordinate",
        "lạm phát": "subordinate",
        "giảm phát": "subordinate",
        "tăng trưởng": "subordinate",
        "suy thoái": "subordinate",
        "khủng hoảng": "subordinate",
        "ngoại tệ": "subordinate",
        "tỷ giá": "subordinate",
        "vốn đầu tư": "subordinate",
        "vốn điều lệ": "subordinate",
        "vốn lưu động": "subordinate",
        "tài sản": "coordinate",
        "bất động sản": "subordinate",
        "thương mại điện tử": "subordinate",
        "xuất khẩu": "subordinate",
        "nhập khẩu": "subordinate",
        "cán cân thương mại": "subordinate",
        "cung cầu": "coordinate",
        "giá cả": "coordinate",
        "thị phần": "subordinate",
        "doanh nhân": "subordinate",
        "khởi nghiệp": "subordinate",
        "năng suất": "subordinate",
        "hiệu quả": "coordinate"
    }
    
    # Nông nghiệp / Agriculture domain
    AGRICULTURE_COMPOUNDS = {
        "nông nghiệp": "coordinate",
        "trồng trọt": "coordinate",
        "chăn nuôi": "coordinate",
        "thủy sản": "coordinate",
        "lâm nghiệp": "coordinate",
        "cây trồng": "subordinate",
        "vật nuôi": "subordinate",
        "gia súc": "subordinate",
        "gia cầm": "subordinate",
        "thủy hải sản": "coordinate",
        "rừng nguyên sinh": "subordinate",
        "rừng phòng hộ": "subordinate",
        "rừng sản xuất": "subordinate",
        "phân bón": "subordinate",
        "phân hữu cơ": "subordinate",
        "phân vô cơ": "subordinate",
        "thuốc trừ sâu": "subordinate",
        "thuốc diệt cỏ": "subordinate",
        "thuốc bảo vệ thực vật": "subordinate",
        "giống cây trồng": "subordinate",
        "giống vật nuôi": "subordinate",
        "năng suất cây trồng": "subordinate",
        "sản lượng nông nghiệp": "subordinate",
        "thu hoạch": "subordinate",
        "mùa vụ": "subordinate",
        "vụ đông xuân": "subordinate",
        "vụ hè thu": "subordinate",
        "vụ mùa": "subordinate",
        "đất nông nghiệp": "subordinate",
        "đất canh tác": "subordinate",
        "ruộng đồng": "coordinate",
        "nông trại": "subordinate",
        "trang trại": "subordinate",
        "hợp tác xã": "subordinate",
        "nông dân": "subordinate",
        "máy nông nghiệp": "subordinate",
        "máy cày": "subordinate",
        "máy gặt": "subordinate",
        "hệ thống tưới tiêu": "subordinate",
        "thủy lợi": "coordinate",
        "đê điều": "coordinate",
        "sản phẩm nông nghiệp": "subordinate",
        "nông sản": "subordinate",
        "lúa gạo": "coordinate",
        "rau củ": "coordinate",
        "trái cây": "coordinate"
    }
    
    # Môi trường / Environment domain
    ENVIRONMENT_COMPOUNDS = {
        "môi trường": "coordinate",
        "sinh thái": "coordinate",
        "ô nhiễm": "subordinate",
        "ô nhiễm không khí": "subordinate",
        "ô nhiễm nước": "subordinate",
        "ô nhiễm đất": "subordinate",
        "ô nhiễm tiếng ồn": "subordinate",
        "hiệu ứng nhà kính": "subordinate",
        "khí nhà kính": "subordinate",
        "biến đổi khí hậu": "subordinate",
        "nóng lên toàn cầu": "subordinate",
        "băng tan": "subordinate",
        "nước biển dâng": "subordinate",
        "thiên tai": "subordinate",
        "bão lụt": "coordinate",
        "hạn hán": "coordinate",
        "sạt lở": "coordinate",
        "cháy rừng": "subordinate",
        "đa dạng sinh học": "subordinate",
        "bảo tồn": "subordinate",
        "loài quý hiếm": "subordinate",
        "loài nguy cấp": "subordinate",
        "phát triển bền vững": "subordinate",
        "năng lượng tái tạo": "subordinate",
        "năng lượng mặt trời": "subordinate",
        "năng lượng gió": "subordinate",
        "năng lượng thủy điện": "subordinate",
        "năng lượng sinh khối": "subordinate",
        "rác thải": "subordinate",
        "chất thải": "subordinate",
        "chất thải rắn": "subordinate",
        "chất thải lỏng": "subordinate",
        "chất thải nguy hại": "subordinate",
        "tái chế": "subordinate",
        "tái sử dụng": "subordinate",
        "xử lý chất thải": "subordinate",
        "xử lý nước thải": "subordinate",
        "nước sạch": "subordinate",
        "nguồn nước": "subordinate",
        "tài nguyên thiên nhiên": "subordinate",
        "tài nguyên khoáng sản": "subordinate",
        "bảo vệ môi trường": "subordinate",
        "luật môi trường": "subordinate",
        "thân thiện môi trường": "subordinate"
    }
    
    # Khoa học cơ bản / Basic Science domain
    SCIENCE_COMPOUNDS = {
        "khoa học": "coordinate",
        "vật lý": "coordinate",
        "hóa học": "coordinate", 
        "sinh học": "coordinate",
        "địa chất": "coordinate",
        "thiên văn": "coordinate",
        "toán học": "coordinate",
        "định luật": "subordinate",
        "nguyên lý": "subordinate",
        "nguyên tử": "subordinate",
        "phân tử": "subordinate",
        "electron": "subordinate",
        "proton": "subordinate",
        "neutron": "subordinate",
        "quark": "subordinate",
        "hạt cơ bản": "subordinate",
        "nguyên tố hóa học": "subordinate",
        "bảng tuần hoàn": "subordinate",
        "phản ứng hóa học": "subordinate",
        "axit": "subordinate",
        "bazơ": "subordinate",
        "muối": "subordinate",
        "hidroxit": "subordinate",
        "hợp chất": "subordinate",
        "oxy hóa": "subordinate",
        "khử": "subordinate",
        "tế bào": "subordinate",
        "nhiễm sắc thể": "subordinate",
        "ADN": "subordinate",
        "gen": "subordinate",
        "protein": "subordinate",
        "enzyme": "subordinate",
        "vi khuẩn": "subordinate",
        "virus": "subordinate",
        "nấm": "subordinate",
        "thực vật": "subordinate",
        "động vật": "subordinate",
        "hệ sinh thái": "subordinate",
        "địa tầng": "subordinate",
        "khoáng vật": "subordinate",
        "đá magma": "subordinate",
        "đá trầm tích": "subordinate",
        "đá biến chất": "subordinate",
        "thiên thạch": "subordinate",
        "hành tinh": "subordinate",
        "thiên hà": "subordinate",
        "lỗ đen": "subordinate",
        "sao neutron": "subordinate",
        "lượng tử": "subordinate"
    }
    
    @classmethod
    def get_all_domain_compounds(cls) -> Dict[str, str]:
        """
        Get a combined dictionary of all domain-specific compound words.
        
        Returns:
            Dictionary mapping compound words to their types
        """
        all_compounds = {}
        
        # Add compounds from all domains
        all_compounds.update(cls.MEDICAL_COMPOUNDS)
        all_compounds.update(cls.LEGAL_COMPOUNDS)
        all_compounds.update(cls.TECHNOLOGY_COMPOUNDS)
        all_compounds.update(cls.EDUCATION_COMPOUNDS)
        all_compounds.update(cls.ECONOMICS_COMPOUNDS)
        all_compounds.update(cls.AGRICULTURE_COMPOUNDS)
        all_compounds.update(cls.ENVIRONMENT_COMPOUNDS)
        all_compounds.update(cls.SCIENCE_COMPOUNDS)
        
        return all_compounds
    
    @classmethod
    def get_domain_compounds(cls, domain: str) -> Dict[str, str]:
        """
        Get compound words for a specific domain.
        
        Args:
            domain: Domain name (medical, legal, technology, education, 
                    economics, agriculture, environment, science)
                    
        Returns:
            Dictionary mapping compound words to their types for the specified domain
        """
        domain_map = {
            "medical": cls.MEDICAL_COMPOUNDS,
            "legal": cls.LEGAL_COMPOUNDS,
            "technology": cls.TECHNOLOGY_COMPOUNDS,
            "education": cls.EDUCATION_COMPOUNDS,
            "economics": cls.ECONOMICS_COMPOUNDS,
            "agriculture": cls.AGRICULTURE_COMPOUNDS,
            "environment": cls.ENVIRONMENT_COMPOUNDS,
            "science": cls.SCIENCE_COMPOUNDS
        }
        
        return domain_map.get(domain.lower(), {})
    
    @classmethod
    def get_available_domains(cls) -> List[str]:
        """
        Get list of available domains.
        
        Returns:
            List of available domain names
        """
        return ["medical", "legal", "technology", "education", 
                "economics", "agriculture", "environment", "science"]


# Singleton instance
vietnamese_domain_compounds = VietnameseDomainCompounds() 