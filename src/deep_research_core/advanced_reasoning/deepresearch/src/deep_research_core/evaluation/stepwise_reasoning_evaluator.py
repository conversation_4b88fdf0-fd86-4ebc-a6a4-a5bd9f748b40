"""
Stepwise reasoning evaluator for Deep Research Core.

This module provides tools for evaluating the quality of each step in a
Chain of Thought reasoning process.
"""

import re
import json
import time
from typing import Dict, Any, List, Optional, Tuple

from ..utils.structured_logging import get_logger
from ..utils.performance_metrics import measure_latency
from ..utils.distributed_tracing import trace_function
from ..models.api.openai import openai_provider
from ..models.api.anthropic import anthropic_provider
from ..models.api.openrouter import openrouter_provider

# Create a logger
logger = get_logger(__name__)

class StepwiseReasoningEvaluator:
    """
    Evaluator for individual reasoning steps in Chain of Thought.
    
    This class provides methods for evaluating the quality of each step
    in a Chain of Thought reasoning process, including relevance, accuracy,
    logic, and progress toward the answer.
    """
    
    def __init__(
        self,
        provider: str = "openai",
        model: Optional[str] = None,
        temperature: float = 0.3,
        max_tokens: int = 1000,
        language: str = "en",
        use_cache: bool = True,
        cache_size: int = 100
    ):
        """
        Initialize the stepwise reasoning evaluator.
        
        Args:
            provider: The provider to use ("openai", "anthropic", etc.)
            model: The model to use (if None, will use provider's default)
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            language: Language for prompts ("en", "vi", etc.)
            use_cache: Whether to use caching
            cache_size: Size of the cache
        """
        self.provider = provider
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.language = language
        self.use_cache = use_cache
        
        # Set up the provider
        if provider == "openai":
            self.api_provider = openai_provider
            self.model = model or "gpt-4o"
        elif provider == "anthropic":
            self.api_provider = anthropic_provider
            self.model = model or "claude-3-sonnet"
        elif provider == "openrouter":
            self.api_provider = openrouter_provider
            self.model = model or "anthropic/claude-3-opus"
        else:
            raise ValueError(f"Unsupported provider: {provider}")
        
        # Set up caching
        if use_cache:
            from functools import lru_cache
            self._generate_cached = lru_cache(maxsize=cache_size)(self._generate)
        else:
            self._generate_cached = self._generate
        
        # Log initialization
        logger.info(f"Initialized StepwiseReasoningEvaluator with provider={provider}, model={self.model}")
    
    @trace_function(name="stepwise_evaluator_evaluate_steps")
    @measure_latency("stepwise_evaluator_evaluate_steps")
    def evaluate_steps(
        self,
        query: str,
        steps: List[str],
        context: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Evaluate each step in a reasoning process.
        
        Args:
            query: The query being reasoned about
            steps: List of reasoning steps
            context: Optional context information
            
        Returns:
            List of evaluation results for each step
        """
        evaluations = []
        
        for i, step in enumerate(steps):
            # Evaluate the current step
            evaluation = self._evaluate_single_step(
                query=query,
                step=step,
                step_index=i,
                previous_steps=steps[:i],
                context=context
            )
            
            evaluations.append(evaluation)
        
        # Log results
        logger.info(f"Evaluated {len(steps)} reasoning steps for query: {query[:50]}...")
        
        return evaluations
    
    @trace_function(name="stepwise_evaluator_evaluate_single_step")
    def _evaluate_single_step(
        self,
        query: str,
        step: str,
        step_index: int,
        previous_steps: List[str],
        context: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Evaluate a single reasoning step.
        
        Args:
            query: The query being reasoned about
            step: The reasoning step to evaluate
            step_index: Index of the step in the reasoning process
            previous_steps: List of previous reasoning steps
            context: Optional context information
            
        Returns:
            Evaluation results for the step
        """
        # Get system prompt
        system_prompt = self._get_system_prompt()
        
        # Prepare user prompt
        user_prompt = self._get_user_prompt(
            query=query,
            step=step,
            step_index=step_index,
            previous_steps=previous_steps,
            context=context
        )
        
        # Generate evaluation
        try:
            evaluation_text = self._generate_cached(
                system_prompt=system_prompt,
                user_prompt=user_prompt
            )
            
            # Parse the evaluation
            evaluation = self._parse_evaluation(evaluation_text, step_index)
            
            return evaluation
        
        except Exception as e:
            logger.error(f"Error evaluating step {step_index}: {str(e)}")
            
            # Return default evaluation if parsing fails
            return {
                "step_index": step_index,
                "relevance": 0.5,
                "accuracy": 0.5,
                "logic": 0.5,
                "progress": 0.5,
                "overall": 0.5,
                "feedback": f"Error evaluating step: {str(e)}"
            }
    
    def _get_system_prompt(self) -> str:
        """
        Get the system prompt based on the language.
        
        Returns:
            System prompt
        """
        if self.language == "vi":
            return """Bạn là một hệ thống đánh giá chất lượng suy luận.
            Hãy đánh giá bước suy luận được cung cấp dựa trên các tiêu chí:
            1. Tính liên quan: Bước suy luận có liên quan đến câu hỏi không?
            2. Tính chính xác: Thông tin trong bước suy luận có chính xác không?
            3. Tính logic: Bước suy luận có logic và nhất quán với các bước trước đó không?
            4. Tính tiến triển: Bước suy luận có đưa quá trình suy luận tiến gần hơn đến câu trả lời không?
            
            Trả về đánh giá dưới dạng JSON với các trường:
            {
                "relevance": số từ 0-1,
                "accuracy": số từ 0-1,
                "logic": số từ 0-1,
                "progress": số từ 0-1,
                "overall": số từ 0-1,
                "feedback": "Nhận xét chi tiết"
            }"""
        else:
            return """You are a system that evaluates the quality of reasoning steps.
            Evaluate the provided reasoning step based on these criteria:
            1. Relevance: Is the reasoning step relevant to the question?
            2. Accuracy: Is the information in the reasoning step accurate?
            3. Logic: Is the reasoning step logical and consistent with previous steps?
            4. Progress: Does the reasoning step move the reasoning process closer to an answer?
            
            Return the evaluation as JSON with these fields:
            {
                "relevance": number from 0-1,
                "accuracy": number from 0-1,
                "logic": number from 0-1,
                "progress": number from 0-1,
                "overall": number from 0-1,
                "feedback": "Detailed feedback"
            }"""
    
    def _get_user_prompt(
        self,
        query: str,
        step: str,
        step_index: int,
        previous_steps: List[str],
        context: Optional[str] = None
    ) -> str:
        """
        Get the user prompt for step evaluation.
        
        Args:
            query: The query being reasoned about
            step: The reasoning step to evaluate
            step_index: Index of the step
            previous_steps: List of previous reasoning steps
            context: Optional context information
            
        Returns:
            User prompt
        """
        # Create context section
        context_section = ""
        if context:
            if self.language == "vi":
                context_section = f"Ngữ cảnh:\n{context}\n\n"
            else:
                context_section = f"Context:\n{context}\n\n"
        
        # Create previous steps section
        previous_steps_section = ""
        if previous_steps:
            if self.language == "vi":
                previous_steps_section = "Các bước suy luận trước đó:\n"
                for i, prev_step in enumerate(previous_steps):
                    previous_steps_section += f"Bước {i+1}: {prev_step}\n"
                previous_steps_section += "\n"
            else:
                previous_steps_section = "Previous reasoning steps:\n"
                for i, prev_step in enumerate(previous_steps):
                    previous_steps_section += f"Step {i+1}: {prev_step}\n"
                previous_steps_section += "\n"
        
        # Create the prompt
        if self.language == "vi":
            return f"""Câu hỏi: {query}

{context_section}{previous_steps_section}Bước suy luận cần đánh giá (Bước {step_index+1}): {step}

Hãy đánh giá bước suy luận này dựa trên các tiêu chí đã nêu. Trả về kết quả dưới dạng JSON."""
        else:
            return f"""Question: {query}

{context_section}{previous_steps_section}Reasoning step to evaluate (Step {step_index+1}): {step}

Please evaluate this reasoning step based on the criteria provided. Return the result as JSON."""
    
    def _generate(self, system_prompt: str, user_prompt: str) -> str:
        """
        Generate text using the API provider.
        
        Args:
            system_prompt: System prompt
            user_prompt: User prompt
            
        Returns:
            Generated text
        """
        try:
            response = self.api_provider.generate(
                prompt=user_prompt,
                model=self.model,
                system_prompt=system_prompt,
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            
            return response
        
        except Exception as e:
            logger.error(f"Error generating evaluation: {str(e)}")
            raise
    
    def _parse_evaluation(self, evaluation_text: str, step_index: int) -> Dict[str, Any]:
        """
        Parse the evaluation text into a structured evaluation.
        
        Args:
            evaluation_text: The evaluation text from the model
            step_index: Index of the step being evaluated
            
        Returns:
            Structured evaluation
        """
        try:
            # Try to parse as JSON
            # Extract JSON if it's embedded in text
            json_match = re.search(r'(\{|\[).*(\}|\])', evaluation_text, re.DOTALL)
            if json_match:
                evaluation_text = json_match.group(0)
            
            # Parse JSON
            evaluation = json.loads(evaluation_text)
            
            # Ensure all required fields are present
            required_fields = ["relevance", "accuracy", "logic", "progress", "feedback"]
            for field in required_fields:
                if field not in evaluation:
                    evaluation[field] = 0.5 if field != "feedback" else "No feedback provided"
            
            # Calculate overall score if not provided
            if "overall" not in evaluation:
                evaluation["overall"] = (
                    evaluation["relevance"] * 0.25 +
                    evaluation["accuracy"] * 0.25 +
                    evaluation["logic"] * 0.25 +
                    evaluation["progress"] * 0.25
                )
            
            # Add step index
            evaluation["step_index"] = step_index
            
            return evaluation
        
        except Exception as e:
            logger.error(f"Error parsing evaluation: {str(e)}")
            
            # Return default evaluation if parsing fails
            return {
                "step_index": step_index,
                "relevance": 0.5,
                "accuracy": 0.5,
                "logic": 0.5,
                "progress": 0.5,
                "overall": 0.5,
                "feedback": f"Error parsing evaluation: {str(e)}"
            }
