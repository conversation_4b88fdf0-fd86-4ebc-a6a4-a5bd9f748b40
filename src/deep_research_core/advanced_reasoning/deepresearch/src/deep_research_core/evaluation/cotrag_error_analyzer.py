"""
CoTRAG Error Analyzer.

This module provides functionality to analyze errors in CoTRAG results
and identify whether they are caused by CoT reasoning or RAG retrieval issues.
"""

import re
import logging
import json
import os
from typing import Dict, Any, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class CoTRAGErrorAnalyzer:
    """
    Analyzer for identifying and diagnosing errors in CoTRAG results.

    This class analyzes CoTRAG results to identify whether errors are caused by:
    1. Chain of Thought reasoning issues
    2. Retrieval-Augmented Generation issues
    3. Integration issues between CoT and RAG
    """

    def __init__(
        self,
        provider: str = "openai",
        model: Optional[str] = None,
        verbose: bool = False,
        error_history_path: Optional[str] = None,
        max_history_size: int = 1000,
        learn_from_errors: bool = True,
        detailed_analysis: bool = False,
        error_classification_threshold: float = 0.6,
        generate_improvement_suggestions: bool = True,
        track_error_patterns: bool = True,
        vietnamese_support: bool = True
    ):
        """
        Initialize the CoTRAGErrorAnalyzer.

        Args:
            provider: The provider to use for analysis ("openai", "anthropic", etc.)
            model: The model to use (if None, will use provider's default)
            verbose: Whether to log detailed analysis information
            error_history_path: Path to store error history
            max_history_size: Maximum number of error history entries to store
            learn_from_errors: Whether to learn from past errors to improve analysis
            detailed_analysis: Whether to perform detailed analysis of errors
            error_classification_threshold: Threshold for classifying errors (0.0 to 1.0)
            generate_improvement_suggestions: Whether to generate improvement suggestions
            track_error_patterns: Whether to track patterns in errors over time
            vietnamese_support: Whether to enable Vietnamese language support
        """
        self.provider = provider
        self.model = model
        self.verbose = verbose
        self.error_history_path = error_history_path
        self.max_history_size = max_history_size
        self.learn_from_errors = learn_from_errors
        self.detailed_analysis = detailed_analysis
        self.error_classification_threshold = error_classification_threshold
        self.generate_improvement_suggestions = generate_improvement_suggestions
        self.track_error_patterns = track_error_patterns
        self.vietnamese_support = vietnamese_support

        # Initialize error history
        self.error_history = []

        # Load error history if path is provided
        if self.error_history_path and os.path.exists(self.error_history_path):
            try:
                with open(self.error_history_path, 'r', encoding='utf-8') as f:
                    self.error_history = json.load(f)
                logger.info("Loaded %d error history entries", len(self.error_history))
            except Exception as e:
                logger.error("Error loading error history: %s", str(e))
                self.error_history = []

        # Common error patterns in reasoning
        self.reasoning_error_patterns = [
            r'logical fallacy',
            r'circular reasoning',
            r'false assumption',
            r'incorrect inference',
            r'non sequitur',
            r'contradiction',
            r'inconsistent',
            # Vietnamese patterns
            r'sai lầm logic',
            r'lập luận vòng tròn',
            r'giả định sai',
            r'suy luận không chính xác',
            r'mâu thuẫn',
            r'không nhất quán'
        ]

        # Common error patterns in retrieval
        self.retrieval_error_patterns = [
            r'irrelevant information',
            r'missing context',
            r'outdated information',
            r'insufficient data',
            r'no relevant documents',
            r'unrelated documents',
            # Vietnamese patterns
            r'thông tin không liên quan',
            r'thiếu ngữ cảnh',
            r'thông tin lỗi thời',
            r'dữ liệu không đủ',
            r'không có tài liệu liên quan',
            r'tài liệu không liên quan'
        ]

    def analyze(
        self,
        query: str,
        documents: List[Dict[str, Any]],
        reasoning: str,
        answer: str,
        expected_answer: Optional[str] = None,
        result_id: Optional[str] = None,
        feedback: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Analyze a CoTRAG result to identify potential errors.

        Args:
            query: The original query
            documents: The retrieved documents
            reasoning: The reasoning process
            answer: The generated answer
            expected_answer: Optional expected answer for comparison
            result_id: Optional ID for the result being analyzed
            feedback: Optional user feedback on the result

        Returns:
            Dictionary with error analysis results
        """
        # Analyze document relevance
        document_analysis = self._analyze_documents(query, documents)

        # Analyze reasoning quality
        reasoning_analysis = self._analyze_reasoning(query, reasoning, documents)

        # Analyze answer quality
        answer_analysis = self._analyze_answer(answer, expected_answer)

        # Apply learning from past errors if enabled
        if self.learn_from_errors and self.error_history:
            self._apply_error_history_learning(document_analysis, reasoning_analysis, answer_analysis, query)

        # Determine error source
        error_source = self._determine_error_source(
            document_analysis, reasoning_analysis, answer_analysis
        )

        # Perform detailed analysis if enabled
        detailed_error_analysis = None
        if self.detailed_analysis:
            detailed_error_analysis = self._perform_detailed_analysis(
                query, documents, reasoning, answer, error_source
            )

        # Generate improvement suggestions if enabled
        suggestions = None
        if self.generate_improvement_suggestions:
            suggestions = self._generate_suggestions(error_source, query, documents, reasoning)

        # Compile the analysis
        analysis = {
            "query": query,
            "document_analysis": document_analysis,
            "reasoning_analysis": reasoning_analysis,
            "answer_analysis": answer_analysis,
            "error_source": error_source,
            "timestamp": datetime.now().isoformat(),
            "result_id": result_id
        }

        # Add detailed analysis if available
        if detailed_error_analysis:
            analysis["detailed_analysis"] = detailed_error_analysis

        # Add suggestions if available
        if suggestions:
            analysis["suggestions"] = suggestions

        # Add to error history if enabled
        if self.error_history_path and self.track_error_patterns:
            self._add_to_error_history(analysis, feedback)

        if self.verbose:
            logger.info("Error source: %s", error_source['primary_source'])
            logger.info("Confidence: %.2f", error_source['confidence'])
            if suggestions:
                logger.info("Suggestions: %s", suggestions['primary_suggestion'])

        return analysis

    def _analyze_documents(
        self,
        query: str,
        documents: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Analyze the relevance and quality of retrieved documents.

        Args:
            query: The original query
            documents: The retrieved documents

        Returns:
            Dictionary with document analysis results
        """
        if not documents:
            return {
                "relevance_score": 0.0,
                "has_relevant_documents": False,
                "avg_score": 0.0,
                "issues": ["No documents retrieved"]
            }

        # Extract relevance scores if available
        relevance_scores = [doc.get("score", 0.0) for doc in documents if "score" in doc]

        if relevance_scores:
            avg_score = sum(relevance_scores) / len(relevance_scores)
        else:
            # Estimate relevance based on keyword matching
            query_keywords = set(query.lower().split())
            avg_score = 0.0

            for doc in documents:
                content = doc.get("content", "").lower()
                content_words = set(content.split())

                # Calculate keyword overlap
                if query_keywords and content_words:
                    overlap = len(query_keywords.intersection(content_words)) / len(query_keywords)
                    avg_score += overlap

            avg_score = avg_score / len(documents) if documents else 0.0

        # Determine if documents are relevant
        has_relevant_documents = avg_score > 0.5

        # Identify potential issues
        issues = []

        if avg_score < 0.3:
            issues.append("Low relevance documents retrieved")

        if len(documents) < 2:
            issues.append("Insufficient number of documents")

        # Check for document diversity
        if len(documents) >= 2:
            contents = [doc.get("content", "") for doc in documents]
            similarity = self._calculate_content_similarity(contents)

            if similarity > 0.7:
                issues.append("Low diversity in retrieved documents")

        return {
            "relevance_score": avg_score,
            "has_relevant_documents": has_relevant_documents,
            "avg_score": avg_score,
            "num_documents": len(documents),
            "issues": issues
        }

    def _analyze_reasoning(
        self,
        query: str,
        reasoning: str,
        documents: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Analyze the quality of the reasoning process.

        Args:
            query: The original query
            reasoning: The reasoning process
            documents: The retrieved documents

        Returns:
            Dictionary with reasoning analysis results
        """
        # Count reasoning steps
        steps = self._extract_reasoning_steps(reasoning)
        step_count = len(steps)

        # Check for document citations
        citation_count = 0
        for doc in documents:
            content = doc.get("content", "")
            # Count significant phrases from the document that appear in the reasoning
            significant_phrases = self._extract_significant_phrases(content)
            for phrase in significant_phrases:
                if phrase in reasoning:
                    citation_count += 1

        # Normalize citation count
        citation_score = min(citation_count / max(len(documents), 1), 1.0)

        # Check for reasoning errors
        error_count = 0
        for pattern in self.reasoning_error_patterns:
            if re.search(pattern, reasoning.lower()):
                error_count += 1

        # Calculate reasoning quality score
        if step_count == 0:
            reasoning_quality = 0.0
        else:
            # More steps and citations generally indicate better reasoning
            reasoning_quality = (0.5 * min(step_count / 5, 1.0) +
                                0.5 * citation_score) * (1.0 - min(error_count / 3, 1.0))

        # Identify potential issues
        issues = []

        if step_count < 2:
            issues.append("Insufficient reasoning steps")

        if citation_score < 0.3:
            issues.append("Poor integration of retrieved information")

        if error_count > 0:
            issues.append(f"Detected {error_count} potential reasoning errors")

        return {
            "reasoning_quality": reasoning_quality,
            "step_count": step_count,
            "citation_score": citation_score,
            "error_count": error_count,
            "issues": issues
        }

    def _analyze_answer(
        self,
        answer: str,
        expected_answer: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Analyze the quality of the generated answer.

        Args:
            answer: The generated answer
            expected_answer: Optional expected answer for comparison

        Returns:
            Dictionary with answer analysis results
        """
        # Check answer length
        answer_length = len(answer.split())

        # Check for uncertainty markers
        uncertainty_markers = [
            "uncertain", "not sure", "might be", "possibly", "perhaps",
            "could be", "may be", "I don't know", "unclear", "insufficient information",
            # Vietnamese patterns
            "không chắc chắn", "có thể là", "có lẽ", "có khả năng",
            "tôi không biết", "không rõ", "thông tin không đủ"
        ]

        uncertainty_count = sum(1 for marker in uncertainty_markers
                               if marker in answer.lower())

        # Calculate answer quality score
        answer_quality = 1.0 - min(uncertainty_count / 3, 1.0)

        # If expected answer is provided, compare with generated answer
        similarity_score = None
        if expected_answer:
            similarity_score = self._calculate_answer_similarity(answer, expected_answer)
            answer_quality = similarity_score

        # Identify potential issues
        issues = []

        if answer_length < 10:
            issues.append("Answer too short")

        if uncertainty_count > 2:
            issues.append("High uncertainty in answer")

        if similarity_score is not None and similarity_score < 0.5:
            issues.append("Answer differs significantly from expected answer")

        return {
            "answer_quality": answer_quality,
            "answer_length": answer_length,
            "uncertainty_count": uncertainty_count,
            "similarity_score": similarity_score,
            "issues": issues
        }

    def _determine_error_source(
        self,
        document_analysis: Dict[str, Any],
        reasoning_analysis: Dict[str, Any],
        answer_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Determine the primary source of errors in the CoTRAG result.

        Args:
            document_analysis: Results of document analysis
            reasoning_analysis: Results of reasoning analysis
            answer_analysis: Results of answer analysis

        Returns:
            Dictionary with error source determination
        """
        # Calculate error scores for each component
        document_error_score = 1.0 - document_analysis["relevance_score"]
        reasoning_error_score = 1.0 - reasoning_analysis["reasoning_quality"]
        answer_error_score = 1.0 - answer_analysis["answer_quality"]

        # Check for specific error patterns
        has_low_relevance = document_analysis.get("relevance_score", 1.0) < 0.4
        has_insufficient_steps = reasoning_analysis.get("step_count", 0) < 2
        has_high_uncertainty = answer_analysis.get("uncertainty_count", 0) > 2
        has_citation_issues = reasoning_analysis.get("citation_score", 1.0) < 0.3

        # Determine primary error source with more specific classification
        if document_error_score > self.error_classification_threshold and \
           document_error_score > reasoning_error_score and \
           document_error_score > answer_error_score:
            if has_low_relevance:
                primary_source = "RAG-LowRelevance"
                details = "The primary issue is low relevance of retrieved documents"
            else:
                primary_source = "RAG"
                details = "The primary issue appears to be with document retrieval"
            confidence = document_error_score

        elif reasoning_error_score > self.error_classification_threshold and \
             reasoning_error_score > document_error_score and \
             reasoning_error_score > answer_error_score:
            if has_insufficient_steps:
                primary_source = "CoT-InsufficientSteps"
                details = "The primary issue is insufficient reasoning steps"
            elif has_citation_issues:
                primary_source = "CoT-PoorCitation"
                details = "The primary issue is poor integration of retrieved information in reasoning"
            else:
                primary_source = "CoT"
                details = "The primary issue appears to be with the reasoning process"
            confidence = reasoning_error_score

        elif answer_error_score > self.error_classification_threshold:
            if has_high_uncertainty:
                primary_source = "Answer-Uncertainty"
                details = "The primary issue is high uncertainty in the answer"
            else:
                primary_source = "Answer Generation"
                details = "The primary issue appears to be with answer generation"
            confidence = answer_error_score

        else:
            # Check for integration issues
            if has_citation_issues and document_error_score < 0.4:
                primary_source = "Integration-Citation"
                details = "The issue is poor integration of relevant documents in reasoning"
            elif has_low_relevance and has_insufficient_steps:
                primary_source = "Integration-RetrievalReasoning"
                details = "The issue is a combination of poor retrieval and insufficient reasoning"
            else:
                primary_source = "Integration"
                details = "The issue appears to be with the integration of RAG and CoT"
            confidence = max(document_error_score, reasoning_error_score, answer_error_score)

        # Determine secondary error source
        error_scores = [
            ("RAG", document_error_score),
            ("CoT", reasoning_error_score),
            ("Answer Generation", answer_error_score)
        ]

        # Sort by error score in descending order
        error_scores.sort(key=lambda x: x[1], reverse=True)

        # Secondary source is the second highest error score
        secondary_source = error_scores[1][0] if len(error_scores) > 1 else None

        return {
            "primary_source": primary_source,
            "secondary_source": secondary_source,
            "confidence": confidence,
            "details": details,
            "document_error_score": document_error_score,
            "reasoning_error_score": reasoning_error_score,
            "answer_error_score": answer_error_score
        }

    def _generate_suggestions(
        self,
        error_source: Dict[str, Any],
        query: str,
        documents: List[Dict[str, Any]],
        reasoning: str
    ) -> Dict[str, Any]:
        """
        Generate suggestions for improving CoTRAG results.

        Args:
            error_source: Error source determination
            query: The original query
            documents: The retrieved documents
            reasoning: The reasoning process

        Returns:
            Dictionary with improvement suggestions
        """
        primary_source = error_source["primary_source"]

        # Generate primary suggestion based on error source
        if primary_source == "RAG":
            if not documents:
                primary_suggestion = "Increase the number of retrieved documents"
            elif error_source["document_error_score"] > 0.7:
                primary_suggestion = "Improve document retrieval by using query expansion or hybrid search"
            else:
                primary_suggestion = "Adjust the balance to rely more on CoT reasoning"

            specific_suggestions = [
                "Try using query expansion to improve retrieval",
                "Consider using hybrid search (vector + keyword)",
                "Increase the number of retrieved documents",
                "Use a more advanced embedding model for document retrieval"
            ]

        elif primary_source == "CoT":
            if len(self._extract_reasoning_steps(reasoning)) < 2:
                primary_suggestion = "Improve reasoning by encouraging more detailed step-by-step analysis"
            elif error_source["reasoning_error_score"] > 0.7:
                primary_suggestion = "Use a more advanced model for reasoning or adjust the system prompt"
            else:
                primary_suggestion = "Adjust the balance to rely more on retrieved documents"

            specific_suggestions = [
                "Modify the system prompt to encourage more detailed reasoning",
                "Use a more advanced model for the reasoning component",
                "Increase the temperature parameter for more creative reasoning",
                "Add explicit instructions to analyze the retrieved information critically"
            ]

        elif primary_source == "Answer Generation":
            primary_suggestion = "Improve answer generation by adjusting the prompt or model"

            specific_suggestions = [
                "Modify the prompt to encourage more concise and direct answers",
                "Use a more advanced model for answer generation",
                "Add explicit instructions to summarize the reasoning clearly",
                "Adjust the max_tokens parameter to allow for more detailed answers"
            ]

        else:  # Integration
            primary_suggestion = "Improve the integration of RAG and CoT"

            specific_suggestions = [
                "Adjust the weights between CoT and RAG dynamically based on query type",
                "Implement a two-stage approach: first retrieve, then reason separately",
                "Add a reranking step after initial retrieval",
                "Implement a feedback loop where reasoning can guide additional retrieval"
            ]

        return {
            "primary_suggestion": primary_suggestion,
            "specific_suggestions": specific_suggestions
        }

    def _extract_reasoning_steps(self, reasoning: str) -> List[str]:
        """
        Extract individual reasoning steps from the reasoning text.

        Args:
            reasoning: The reasoning text

        Returns:
            List of reasoning steps
        """
        # Look for numbered steps or step markers
        step_markers = [
            r'Step \d+[:.]\s*(.*?)(?=Step \d+[:.]\s*|$)',
            r'First[,.]\s*(.*?)(?=Second[,.]\s*|$)',
            r'Second[,.]\s*(.*?)(?=Third[,.]\s*|$)',
            r'Third[,.]\s*(.*?)(?=Fourth[,.]\s*|$)',
            r'Fourth[,.]\s*(.*?)(?=Fifth[,.]\s*|$)',
            r'Fifth[,.]\s*(.*?)(?=Sixth[,.]\s*|$)',
            r'Next[,.]\s*(.*?)(?=Next[,.]\s*|Finally[,.]\s*|$)',
            r'Finally[,.]\s*(.*?)(?=$)',
            r'Đầu tiên[,.]\s*(.*?)(?=Tiếp theo[,.]\s*|$)',
            r'Tiếp theo[,.]\s*(.*?)(?=Tiếp theo[,.]\s*|Cuối cùng[,.]\s*|$)',
            r'Cuối cùng[,.]\s*(.*?)(?=$)'
        ]

        steps = []
        for marker in step_markers:
            matches = re.findall(marker, reasoning, re.DOTALL)
            steps.extend([match.strip() for match in matches if match.strip()])

        # If no explicit steps found, split by sentences as a fallback
        if not steps:
            sentences = re.split(r'[.!?]\s+', reasoning)
            steps = [s.strip() for s in sentences if len(s.strip()) > 10]

        return steps

    def _extract_significant_phrases(self, text: str, min_length: int = 5) -> List[str]:
        """
        Extract significant phrases from text.

        Args:
            text: The text to extract phrases from
            min_length: Minimum word length for a phrase to be considered significant

        Returns:
            List of significant phrases
        """
        # Split text into sentences
        sentences = re.split(r'[.!?]\s+', text)

        # Extract noun phrases and other significant segments
        phrases = []
        for sentence in sentences:
            words = sentence.split()
            if len(words) >= min_length:
                # Add the full sentence if it's not too long
                if len(words) <= 15:
                    phrases.append(sentence.strip())

                # Add the first half of longer sentences
                else:
                    half_point = len(words) // 2
                    first_half = ' '.join(words[:half_point])
                    second_half = ' '.join(words[half_point:])
                    phrases.append(first_half.strip())
                    phrases.append(second_half.strip())

        return phrases

    def _calculate_content_similarity(self, contents: List[str]) -> float:
        """
        Calculate the average similarity between document contents.

        Args:
            contents: List of document contents

        Returns:
            Average similarity score (0.0 to 1.0)
        """
        if len(contents) <= 1:
            return 0.0

        # Calculate pairwise similarity using word overlap
        total_similarity = 0.0
        comparison_count = 0

        for i in range(len(contents)):
            words1 = set(contents[i].lower().split())

            for j in range(i + 1, len(contents)):
                words2 = set(contents[j].lower().split())

                # Calculate Jaccard similarity
                if words1 and words2:
                    intersection = len(words1.intersection(words2))
                    union = len(words1.union(words2))
                    similarity = intersection / union

                    total_similarity += similarity
                    comparison_count += 1

        # Calculate average similarity
        avg_similarity = total_similarity / comparison_count if comparison_count > 0 else 0.0

        return avg_similarity

    def _calculate_answer_similarity(self, answer1: str, answer2: str) -> float:
        """
        Calculate the similarity between two answers.

        Args:
            answer1: First answer
            answer2: Second answer

        Returns:
            Similarity score (0.0 to 1.0)
        """
        # Convert to lowercase and split into words
        words1 = set(answer1.lower().split())
        words2 = set(answer2.lower().split())

        # Calculate Jaccard similarity
        if words1 and words2:
            intersection = len(words1.intersection(words2))
            union = len(words1.union(words2))
            similarity = intersection / union
        else:
            similarity = 0.0

        return similarity

    def _add_to_error_history(self, analysis: Dict[str, Any], feedback: Optional[Dict[str, Any]] = None) -> None:
        """
        Add an error analysis to the history for future learning.

        Args:
            analysis: The error analysis result
            feedback: Optional user feedback on the result
        """
        # Create a record
        record = {
            "timestamp": datetime.now().isoformat(),
            "query": analysis.get("query", ""),
            "error_source": analysis.get("error_source", {}),
            "document_analysis": analysis.get("document_analysis", {}),
            "reasoning_analysis": analysis.get("reasoning_analysis", {}),
            "answer_analysis": analysis.get("answer_analysis", {})
        }

        if feedback:
            record["feedback"] = feedback

        # Add to history
        self.error_history.append(record)

        # Trim history if needed
        if len(self.error_history) > self.max_history_size:
            self.error_history = self.error_history[-self.max_history_size:]

        # Save history if path is provided
        if self.error_history_path:
            try:
                with open(self.error_history_path, 'w', encoding='utf-8') as f:
                    json.dump(self.error_history, f)
                logger.debug("Saved %d error history entries", len(self.error_history))
            except Exception as e:
                logger.error("Error saving error history: %s", str(e))

    def _apply_error_history_learning(
        self,
        document_analysis: Dict[str, Any],
        reasoning_analysis: Dict[str, Any],
        answer_analysis: Dict[str, Any],
        query: str
    ) -> None:
        """
        Apply learning from past errors to improve current analysis.

        Args:
            document_analysis: Current document analysis
            reasoning_analysis: Current reasoning analysis
            answer_analysis: Current answer analysis
            query: The current query
        """
        if not self.error_history:
            return

        # Find similar queries in history
        similar_records = self._find_similar_records(query)

        if not similar_records:
            return

        # Analyze patterns in similar records
        doc_issues_count = {}
        reasoning_issues_count = {}
        answer_issues_count = {}

        for record in similar_records:
            # Count document issues
            for issue in record.get("document_analysis", {}).get("issues", []):
                doc_issues_count[issue] = doc_issues_count.get(issue, 0) + 1

            # Count reasoning issues
            for issue in record.get("reasoning_analysis", {}).get("issues", []):
                reasoning_issues_count[issue] = reasoning_issues_count.get(issue, 0) + 1

            # Count answer issues
            for issue in record.get("answer_analysis", {}).get("issues", []):
                answer_issues_count[issue] = answer_issues_count.get(issue, 0) + 1

        # Apply learning to current analysis
        self._apply_issue_learning(document_analysis, doc_issues_count)
        self._apply_issue_learning(reasoning_analysis, reasoning_issues_count)
        self._apply_issue_learning(answer_analysis, answer_issues_count)

    def _find_similar_records(self, query: str, threshold: float = 0.3) -> List[Dict[str, Any]]:
        """
        Find records with similar queries in the error history.

        Args:
            query: The current query
            threshold: Similarity threshold (0.0 to 1.0)

        Returns:
            List of similar records
        """
        similar_records = []
        query_words = set(query.lower().split())

        for record in self.error_history:
            record_query = record.get("query", "")
            record_words = set(record_query.lower().split())

            # Calculate word overlap similarity
            if query_words and record_words:
                intersection = len(query_words.intersection(record_words))
                union = len(query_words.union(record_words))
                similarity = intersection / union

                if similarity >= threshold:
                    similar_records.append(record)

        return similar_records

    def _perform_detailed_analysis(
        self,
        query: str,
        documents: List[Dict[str, Any]],
        reasoning: str,
        answer: str,
        error_source: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Perform detailed analysis of errors in CoTRAG results.

        This method provides a more detailed analysis of the errors,
        including specific error patterns, root causes, and impact.

        Args:
            query: The original query
            documents: The retrieved documents
            reasoning: The reasoning process
            answer: The generated answer
            error_source: The error source determination

        Returns:
            Dictionary with detailed error analysis
        """
        primary_source = error_source.get("primary_source", "Unknown")
        confidence = error_source.get("confidence", 0.0)

        # Analyze specific error patterns based on the primary source
        error_patterns = []
        root_causes = []
        impact_assessment = {}

        # Check for RAG-related errors
        if primary_source.startswith("RAG"):
            # Check for specific RAG error patterns
            if not documents:
                error_patterns.append("No documents retrieved")
                root_causes.append("Empty vector store or index")
            elif len(documents) < 2:
                error_patterns.append("Insufficient number of documents")
                root_causes.append("Low recall in retrieval")

            # Check document relevance
            relevance_scores = [doc.get("score", 0.0) for doc in documents if "score" in doc]
            if relevance_scores:
                avg_score = sum(relevance_scores) / len(relevance_scores)
                if avg_score < 0.3:
                    error_patterns.append(f"Very low document relevance (avg: {avg_score:.2f})")
                    root_causes.append("Poor embedding or retrieval model")
                elif avg_score < 0.5:
                    error_patterns.append(f"Moderate document relevance (avg: {avg_score:.2f})")
                    root_causes.append("Suboptimal query or document embeddings")

            # Assess impact
            impact_assessment = {
                "information_quality": "low" if not documents or avg_score < 0.3 else "medium",
                "reasoning_impact": "high" if not documents or avg_score < 0.3 else "medium",
                "answer_accuracy": "low" if not documents or avg_score < 0.3 else "medium"
            }

        # Check for CoT-related errors
        elif primary_source.startswith("CoT"):
            # Extract reasoning steps
            steps = self._extract_reasoning_steps(reasoning)

            # Check for specific CoT error patterns
            if len(steps) < 2:
                error_patterns.append("Insufficient reasoning steps")
                root_causes.append("Inadequate prompting for step-by-step reasoning")

            # Check for logical errors in reasoning
            logical_errors = [pattern for pattern in self.reasoning_error_patterns
                             if re.search(pattern, reasoning.lower())]
            if logical_errors:
                error_patterns.append("Logical errors in reasoning")
                root_causes.append("Complex query requiring better reasoning")

            # Check for citation of documents
            citation_count = 0
            for doc in documents:
                content = doc.get("content", "")
                significant_phrases = self._extract_significant_phrases(content)
                for phrase in significant_phrases:
                    if phrase in reasoning:
                        citation_count += 1

            if citation_count < len(documents) / 2:
                error_patterns.append("Poor integration of retrieved information")
                root_causes.append("Weak connection between retrieval and reasoning")

            # Assess impact
            impact_assessment = {
                "information_quality": "medium",
                "reasoning_impact": "high",
                "answer_accuracy": "medium" if logical_errors else "high"
            }

        # Check for Answer-related errors
        elif primary_source.startswith("Answer"):
            # Check for uncertainty markers
            uncertainty_markers = [
                "uncertain", "not sure", "might be", "possibly", "perhaps",
                "could be", "may be", "I don't know", "unclear", "insufficient information"
            ]

            uncertainty_count = sum(1 for marker in uncertainty_markers
                                  if marker in answer.lower())

            if uncertainty_count > 2:
                error_patterns.append("High uncertainty in answer")
                root_causes.append("Insufficient information or complex query")

            # Check answer length
            if len(answer.split()) < 10:
                error_patterns.append("Answer too short")
                root_causes.append("Insufficient elaboration or information")

            # Assess impact
            impact_assessment = {
                "information_quality": "medium",
                "reasoning_impact": "low",
                "answer_accuracy": "low" if uncertainty_count > 2 else "medium"
            }

        # Check for Integration-related errors
        elif primary_source.startswith("Integration"):
            # Check for specific integration error patterns
            if "Citation" in primary_source:
                error_patterns.append("Poor citation of retrieved information")
                root_causes.append("Weak integration between retrieval and reasoning")
            elif "RetrievalReasoning" in primary_source:
                error_patterns.append("Combined retrieval and reasoning issues")
                root_causes.append("Multiple subsystem failures")
            else:
                error_patterns.append("General integration issues")
                root_causes.append("Suboptimal balance between CoT and RAG")

            # Assess impact
            impact_assessment = {
                "information_quality": "medium",
                "reasoning_impact": "medium",
                "answer_accuracy": "medium"
            }

        # Compile detailed analysis
        detailed_analysis = {
            "error_patterns": error_patterns,
            "root_causes": root_causes,
            "impact_assessment": impact_assessment,
            "confidence": confidence
        }

        # Add language-specific analysis if Vietnamese support is enabled
        if self.vietnamese_support and any(c > chr(127) for c in query):
            detailed_analysis["language_specific"] = self._analyze_vietnamese_specific(query, reasoning, answer)

        return detailed_analysis

    def _analyze_vietnamese_specific(self, query: str, reasoning: str, answer: str) -> Dict[str, Any]:
        """
        Analyze Vietnamese-specific aspects of the query, reasoning, and answer.

        Args:
            query: The original query in Vietnamese
            reasoning: The reasoning process
            answer: The generated answer

        Returns:
            Dictionary with Vietnamese-specific analysis
        """
        # Check for Vietnamese-specific patterns
        vietnamese_patterns = {
            "diacritics_missing": r'[a-zA-Z]+(\s+[a-zA-Z]+)+',  # Latin characters without diacritics
            "mixed_language": r'[\p{Latin}\s]+(\s+[\p{Han}\p{Hiragana}\p{Katakana}]+)+',  # Mixed scripts
            "formal_language": r'(xin\s+|làm\s+ơn\s+|vui\s+lòng\s+)'
        }

        issues = []

        # Check for missing diacritics in reasoning or answer
        if re.search(vietnamese_patterns["diacritics_missing"], reasoning) or \
           re.search(vietnamese_patterns["diacritics_missing"], answer):
            issues.append("Missing diacritics in Vietnamese text")

        # Check for mixed language
        if re.search(vietnamese_patterns["mixed_language"], reasoning) or \
           re.search(vietnamese_patterns["mixed_language"], answer):
            issues.append("Mixed language scripts")

        return {
            "issues": issues,
            "language": "vi",
            "suggestions": [
                "Use specialized Vietnamese embedding models",
                "Ensure proper diacritics in Vietnamese text",
                "Maintain consistent language throughout the response"
            ] if issues else []
        }

    def _apply_issue_learning(self, analysis: Dict[str, Any], issues_count: Dict[str, int]) -> None:
        """
        Apply learning from historical issues to current analysis.

        Args:
            analysis: The current analysis to update
            issues_count: Count of issues from similar historical records
        """
        if not issues_count:
            return

        # Get current issues
        current_issues = analysis.get("issues", [])

        # Find common issues that aren't already in current issues
        for issue, count in issues_count.items():
            if count >= 2 and issue not in current_issues:  # Issue appeared at least twice in history
                current_issues.append(f"{issue} (historical pattern)")

        # Update analysis
        analysis["issues"] = current_issues

        # If we added historical issues, adjust the quality score
        if len(current_issues) > len(analysis.get("issues", [])):
            if "reasoning_quality" in analysis:
                analysis["reasoning_quality"] = max(0.0, analysis["reasoning_quality"] - 0.1)
            if "relevance_score" in analysis:
                analysis["relevance_score"] = max(0.0, analysis["relevance_score"] - 0.1)
            if "answer_quality" in analysis:
                analysis["answer_quality"] = max(0.0, analysis["answer_quality"] - 0.1)
