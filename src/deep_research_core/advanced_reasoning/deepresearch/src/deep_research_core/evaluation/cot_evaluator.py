"""
Evaluator for Chain of Thought reasoning.

This module provides tools for evaluating the quality of Chain of Thought reasoning,
including metrics for step clarity, logical flow, evidence usage, and conclusion strength.
"""

import re
import json
import time
from typing import Dict, Any, List, Optional, Union, Tuple

from ..utils.structured_logging import get_logger
from ..utils.performance_metrics import measure_latency
from ..utils.distributed_tracing import trace_function, span
from ..models.api.openai import openai_provider
from ..models.api.anthropic import anthropic_provider
from ..models.api.openrouter import openrouter_provider

# Create a logger
logger = get_logger(__name__)

class CoTEvaluator:
    """
    Evaluator for Chain of Thought reasoning.

    This class provides methods for evaluating the quality of Chain of Thought reasoning,
    including both heuristic-based and model-based evaluation approaches.

    Features:
    - Evaluation of reasoning steps clarity and logical flow
    - Assessment of evidence usage and conclusion strength
    - Comparison between different reasoning approaches
    - Support for multiple languages (English and Vietnamese)
    - Domain-specific evaluation criteria
    """

    def __init__(
        self,
        provider: str = "openai",
        model: Optional[str] = None,
        temperature: float = 0.3,  # Lower temperature for more consistent evaluation
        max_tokens: int = 1000,
        language: str = "en",
        use_model_evaluation: bool = True,
        verbose: bool = False
    ):
        """
        Initialize the CoT evaluator.

        Args:
            provider: The provider to use for model-based evaluation
            model: The model to use for evaluation
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            language: Language to use for evaluation
            use_model_evaluation: Whether to use model-based evaluation
            verbose: Whether to print verbose output
        """
        self.provider = provider
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.language = language
        self.use_model_evaluation = use_model_evaluation
        self.verbose = verbose

        # Set up the provider for model-based evaluation
        if use_model_evaluation:
            if provider == "openai":
                self.api_provider = openai_provider
                self.model = model or "gpt-4o"
            elif provider == "anthropic":
                self.api_provider = anthropic_provider
                self.model = model or "claude-3-sonnet"
            elif provider == "openrouter":
                self.api_provider = openrouter_provider
                self.model = model or "anthropic/claude-3-opus"
            else:
                raise ValueError(f"Unsupported provider: {provider}")
        else:
            self.api_provider = None
            self.model = None

        # Set up evaluation prompts based on language
        if language.lower() in ["vi", "vietnamese"]:
            self.evaluation_prompt = """Đánh giá chất lượng của quá trình suy luận sau đây dựa trên các tiêu chí sau:

1. Rõ ràng về các bước: Mức độ rõ ràng và dễ hiểu của từng bước suy luận
2. Luồng logic: Mức độ liên kết logic giữa các bước
3. Sử dụng bằng chứng: Mức độ sử dụng và tích hợp bằng chứng, dữ liệu hoặc ví dụ
4. Độ mạnh của kết luận: Mức độ kết luận được hỗ trợ bởi các bước suy luận trước đó
5. Chất lượng tổng thể: Đánh giá tổng thể về chất lượng suy luận

Quá trình suy luận:
{reasoning}

Vui lòng đánh giá mỗi tiêu chí trên thang điểm từ 0 đến 10, trong đó 0 là rất kém và 10 là xuất sắc.
Đưa ra phản hồi theo định dạng JSON như sau:

```json
{{
  "step_clarity": điểm,
  "logical_flow": điểm,
  "evidence_usage": điểm,
  "conclusion_strength": điểm,
  "overall_quality": điểm,
  "explanation": "Giải thích ngắn gọn về đánh giá của bạn"
}}
```"""
        else:
            self.evaluation_prompt = """Evaluate the quality of the following reasoning process based on these criteria:

1. Step clarity: How clear and understandable each reasoning step is
2. Logical flow: How well the steps connect logically to each other
3. Evidence usage: How well evidence, data, or examples are used and integrated
4. Conclusion strength: How well the conclusion is supported by the preceding reasoning steps
5. Overall quality: Overall assessment of reasoning quality

Reasoning process:
{reasoning}

Please rate each criterion on a scale from 0 to 10, where 0 is very poor and 10 is excellent.
Provide your feedback in JSON format as follows:

```json
{{
  "step_clarity": score,
  "logical_flow": score,
  "evidence_usage": score,
  "conclusion_strength": score,
  "overall_quality": score,
  "explanation": "Brief explanation of your evaluation"
}}
```"""

        # Log initialization
        logger.info(f"Initialized CoTEvaluator with provider={provider}, model={self.model}")

    def _extract_reasoning_steps(self, reasoning: str) -> List[str]:
        """
        Extract individual reasoning steps from the full reasoning text.

        Args:
            reasoning: The full reasoning text

        Returns:
            List of individual reasoning steps
        """
        # Try to find numbered steps first
        step_pattern = re.compile(r'(?:Step\s*(\d+)|(\d+)\.\s*|(?:First|Second|Third|Fourth|Fifth|Next|Then|Finally)[:\s]+)([^\n]+(?:\n(?!\s*(?:Step\s*\d+|\d+\.\s*|(?:First|Second|Third|Fourth|Fifth|Next|Then|Finally)[:\s]+))[^\n]+)*)',
                                 re.IGNORECASE)

        steps = []
        for match in step_pattern.finditer(reasoning):
            step_text = match.group(3).strip()
            if step_text:
                steps.append(step_text)

        # If no steps found, try to split by paragraphs
        if not steps:
            paragraphs = [p.strip() for p in reasoning.split('\n\n') if p.strip()]

            # Filter out very short paragraphs and the final answer
            steps = [p for p in paragraphs if len(p) > 20 and not any(
                marker in p.lower() for marker in [
                    "therefore, the answer is",
                    "thus, the answer is",
                    "in conclusion",
                    "the final answer is"
                ]
            )]

        return steps

    @trace_function(name="cot_evaluator_heuristic_evaluation")
    def heuristic_evaluation(self, reasoning: str) -> Dict[str, float]:
        """
        Evaluate reasoning quality using heuristics.

        Args:
            reasoning: The reasoning text to evaluate

        Returns:
            Dictionary with quality metrics
        """
        # Initialize metrics
        metrics = {
            "step_clarity": 0.0,
            "logical_flow": 0.0,
            "evidence_usage": 0.0,
            "conclusion_strength": 0.0,
            "overall_quality": 0.0
        }

        # Extract reasoning steps
        steps = self._extract_reasoning_steps(reasoning)

        # 1. Step clarity - Check for clear step indicators and step length
        step_indicators = re.findall(r'(Step \d+|First|Second|Third|Next|Then|Finally):', reasoning, re.IGNORECASE)
        avg_step_length = sum(len(step) for step in steps) / max(len(steps), 1) if steps else 0
        metrics["step_clarity"] = min((len(step_indicators) / max(len(steps), 1) * 5) +
                                     (min(avg_step_length / 100, 1.0) * 5), 10.0)

        # 2. Logical flow - Check for logical connectors
        logical_connectors = re.findall(r'\b(because|therefore|thus|consequently|as a result|since|given that)\b',
                                       reasoning, re.IGNORECASE)
        metrics["logical_flow"] = min(len(logical_connectors) / max(len(steps), 1) * 5, 10.0)

        # 3. Evidence usage - Check for evidence markers
        evidence_markers = re.findall(r'\b(evidence|data|research|study|according to|shows|demonstrates|indicates)\b',
                                     reasoning, re.IGNORECASE)
        metrics["evidence_usage"] = min(len(evidence_markers) / 2, 1.0) * 10

        # 4. Conclusion strength - Check for conclusion markers
        conclusion_markers = re.findall(r'\b(in conclusion|to summarize|therefore|thus|the answer is|we can conclude)\b',
                                       reasoning, re.IGNORECASE)
        metrics["conclusion_strength"] = min(len(conclusion_markers), 1.0) * 10

        # 5. Overall quality - Weighted average of other metrics
        metrics["overall_quality"] = (
            0.3 * metrics["step_clarity"] +
            0.3 * metrics["logical_flow"] +
            0.2 * metrics["evidence_usage"] +
            0.2 * metrics["conclusion_strength"]
        )

        return metrics

    @trace_function(name="cot_evaluator_model_evaluation")
    def model_evaluation(self, reasoning: str) -> Dict[str, float]:
        """
        Evaluate reasoning quality using a model.

        Args:
            reasoning: The reasoning text to evaluate

        Returns:
            Dictionary with quality metrics
        """
        if not self.use_model_evaluation or not self.api_provider:
            logger.warning("Model evaluation is disabled or provider not set")
            return self.heuristic_evaluation(reasoning)

        try:
            # Prepare the evaluation prompt
            prompt = self.evaluation_prompt.format(reasoning=reasoning)

            # Generate evaluation
            evaluation_text = self.api_provider.generate(
                prompt=prompt,
                model=self.model,
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )

            # Extract JSON from the response
            json_match = re.search(r'```json\s*(.*?)\s*```', evaluation_text, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
                try:
                    metrics = json.loads(json_str)

                    # Ensure all required metrics are present
                    required_metrics = ["step_clarity", "logical_flow", "evidence_usage",
                                       "conclusion_strength", "overall_quality"]

                    for metric in required_metrics:
                        if metric not in metrics:
                            metrics[metric] = 5.0  # Default value

                    return metrics
                except json.JSONDecodeError:
                    logger.error(f"Failed to parse evaluation JSON: {json_str}")

            # Fallback to heuristic evaluation
            logger.warning("Failed to extract metrics from model evaluation, falling back to heuristics")
            return self.heuristic_evaluation(reasoning)

        except Exception as e:
            logger.error(f"Error during model evaluation: {str(e)}")
            return self.heuristic_evaluation(reasoning)

    @trace_function(name="cot_evaluator_evaluate")
    @measure_latency("cot_evaluator_evaluate")
    def evaluate(self, reasoning: str, query: str = None, context: str = None) -> Dict[str, Any]:
        """
        Evaluate the quality of Chain of Thought reasoning.

        Args:
            reasoning: The reasoning text to evaluate
            query: Optional query that prompted the reasoning
            context: Optional context used for the reasoning

        Returns:
            Dictionary with evaluation results
        """
        start_time = time.time()

        # Extract reasoning steps
        steps = self._extract_reasoning_steps(reasoning)

        # Perform evaluation
        if self.use_model_evaluation:
            metrics = self.model_evaluation(reasoning)
        else:
            metrics = self.heuristic_evaluation(reasoning)

        # Calculate latency
        latency = time.time() - start_time

        # Prepare result
        result = {
            "metrics": metrics,
            "step_count": len(steps),
            "latency": latency,
            "steps": steps
        }

        # Add detailed evaluation if query and context are provided
        if query and context and self.use_model_evaluation:
            try:
                # Prepare the evaluation prompt with query and context
                prompt = f"""Evaluate the following chain-of-thought reasoning for answering the given query based on the provided context.

Query: {query}

Context: {context}

Reasoning: {reasoning}

Evaluate the reasoning on these criteria:
1. Relevance to the query
2. Accuracy based on the context
3. Logical coherence
4. Completeness

Provide a detailed evaluation with specific strengths and weaknesses.
Format your response as JSON:
{{"relevance": score_0_to_10, "accuracy": score_0_to_10, "coherence": score_0_to_10, "completeness": score_0_to_10, "overall": score_0_to_10, "strengths": ["strength1", "strength2"], "weaknesses": ["weakness1", "weakness2"], "explanation": "detailed explanation"}}"""

                # Generate detailed evaluation
                detailed_eval_text = self.api_provider.generate(
                    prompt=prompt,
                    model=self.model,
                    temperature=self.temperature,
                    max_tokens=self.max_tokens
                )

                # Extract JSON from the response
                json_match = re.search(r'\{.*\}', detailed_eval_text, re.DOTALL)
                if json_match:
                    try:
                        detailed_metrics = json.loads(json_match.group(0))
                        result["detailed_evaluation"] = detailed_metrics
                    except json.JSONDecodeError:
                        logger.error(f"Failed to parse detailed evaluation JSON")
            except Exception as e:
                logger.error(f"Error during detailed evaluation: {str(e)}")

        if self.verbose:
            logger.info(f"Evaluation completed in {latency:.2f} seconds")
            logger.info(f"Step count: {len(steps)}")
            logger.info(f"Overall quality: {metrics.get('overall_quality', 0.0):.2f}")

        return result

    @trace_function(name="cot_evaluator_compare")
    def compare(self, reasoning_a: str, reasoning_b: str) -> Dict[str, Any]:
        """
        Compare the quality of two Chain of Thought reasoning processes.

        Args:
            reasoning_a: First reasoning text to compare
            reasoning_b: Second reasoning text to compare

        Returns:
            Dictionary with comparison results
        """
        # Evaluate both reasoning processes
        eval_a = self.evaluate(reasoning_a)
        eval_b = self.evaluate(reasoning_b)

        # Calculate differences
        metrics_a = eval_a.get("metrics", {})
        metrics_b = eval_b.get("metrics", {})

        differences = {}
        for metric in metrics_a:
            if metric in metrics_b:
                differences[metric] = metrics_b[metric] - metrics_a[metric]

        # Determine which is better
        overall_diff = differences.get("overall_quality", 0.0)
        better = "B" if overall_diff > 0 else "A" if overall_diff < 0 else "Equal"

        # Prepare result
        result = {
            "evaluation_a": eval_a,
            "evaluation_b": eval_b,
            "differences": differences,
            "better": better,
            "improvement": abs(overall_diff)
        }

        if self.verbose:
            logger.info(f"Comparison completed. Better reasoning: {better}")
            logger.info(f"Improvement: {abs(overall_diff):.2f} points")

        return result
