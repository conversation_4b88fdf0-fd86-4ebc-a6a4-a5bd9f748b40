"""
Integrated Evaluator.

Module này tích hợp các công cụ đánh giá khác nhau trong hệ thống để cung cấp
một giao diện thống nhất cho việc đánh giá chất lượng phản hồi.
"""

from typing import Dict, List, Any, Optional, Union

import numpy as np

from ..utils.structured_logging import get_logger
from ..utils.vietnamese_utils import detect_vietnamese
from .base_evaluator import BaseEvaluator

# Import các evaluator nếu có
try:
    from .rl_evaluator import RLEvaluator
except ImportError:
    RLEvaluator = None

try:
    from .vietnamese_rl_evaluator import VietnameseRLEvaluator
except ImportError:
    VietnameseRLEvaluator = None

try:
    from .reasoning_evaluator import ReasoningEvaluator
except ImportError:
    ReasoningEvaluator = None

try:
    from .factuality_evaluator import FactualityEvaluator
except ImportError:
    FactualityEvaluator = None

# Create a logger
logger = get_logger(__name__)

class IntegratedEvaluator:
    """
    T<PERSON><PERSON> hợp các công cụ đánh giá khác nhau trong hệ thống.

    Lớp này cung cấp một giao diện thống nhất cho việc đánh giá chất lượng phản hồi,
    tự động chọn công cụ đánh giá phù hợp dựa trên ngôn ngữ và loại nhiệm vụ.
    """

    def __init__(
        self,
        use_vietnamese_evaluator: bool = True,
        use_rl_evaluator: bool = True,
        use_reasoning_evaluator: bool = True,
        use_factuality_evaluator: bool = True,
        vietnamese_embedding_model: Optional[str] = "phobert",
        verbose: bool = False
    ):
        """
        Khởi tạo IntegratedEvaluator.

        Args:
            use_vietnamese_evaluator: Sử dụng Vietnamese RL Evaluator
            use_rl_evaluator: Sử dụng RL Evaluator
            use_reasoning_evaluator: Sử dụng Reasoning Evaluator
            use_factuality_evaluator: Sử dụng Factuality Evaluator
            vietnamese_embedding_model: Mô hình embedding tiếng Việt
            verbose: Bật chế độ verbose
        """
        self.verbose = verbose

        # Khởi tạo các công cụ đánh giá
        self.evaluators = {}

        if use_vietnamese_evaluator:
            try:
                self.evaluators["vietnamese"] = VietnameseRLEvaluator(
                    embedding_model=vietnamese_embedding_model,
                    verbose=verbose
                )
                if verbose:
                    logger.info("Initialized Vietnamese RL Evaluator")
            except Exception as e:
                logger.error(f"Error initializing Vietnamese RL Evaluator: {str(e)}")

        if use_rl_evaluator:
            try:
                self.evaluators["rl"] = RLEvaluator(verbose=verbose)
                if verbose:
                    logger.info("Initialized RL Evaluator")
            except Exception as e:
                logger.error(f"Error initializing RL Evaluator: {str(e)}")

        if use_reasoning_evaluator:
            try:
                self.evaluators["reasoning"] = ReasoningEvaluator(verbose=verbose)
                if verbose:
                    logger.info("Initialized Reasoning Evaluator")
            except Exception as e:
                logger.error(f"Error initializing Reasoning Evaluator: {str(e)}")

        if use_factuality_evaluator:
            try:
                self.evaluators["factuality"] = FactualityEvaluator(verbose=verbose)
                if verbose:
                    logger.info("Initialized Factuality Evaluator")
            except Exception as e:
                logger.error(f"Error initializing Factuality Evaluator: {str(e)}")

    def evaluate(
        self,
        response: str,
        reference_response: Optional[str] = None,
        task_description: Optional[str] = None,
        expected_keywords: Optional[List[str]] = None,
        previous_responses: Optional[List[str]] = None,
        task_type: Optional[str] = None,
        language: Optional[str] = None,
        context: Optional[str] = None,
        facts: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Đánh giá chất lượng phản hồi.

        Args:
            response: Phản hồi cần đánh giá
            reference_response: Phản hồi tham chiếu (nếu có)
            task_description: Mô tả nhiệm vụ (nếu có)
            expected_keywords: Danh sách các từ khóa mong đợi trong phản hồi
            previous_responses: Danh sách các phản hồi trước đó để đánh giá tính nhất quán
            task_type: Loại nhiệm vụ (rl, reasoning, factuality)
            language: Ngôn ngữ của phản hồi (auto, en, vi)
            context: Ngữ cảnh của phản hồi (nếu có)
            facts: Danh sách các sự kiện để đánh giá tính chính xác (nếu có)

        Returns:
            Dictionary chứa các metrics đánh giá
        """
        if not response:
            return {"overall_score": 0.0, "error": "Empty response"}

        # Xác định ngôn ngữ nếu không được chỉ định
        if not language:
            is_vietnamese = detect_vietnamese(response)
            language = "vi" if is_vietnamese else "en"

        # Xác định loại nhiệm vụ nếu không được chỉ định
        if not task_type:
            if facts:
                task_type = "factuality"
            elif previous_responses:
                task_type = "rl"
            elif task_description and "reasoning" in task_description.lower():
                task_type = "reasoning"
            else:
                task_type = "rl"  # Mặc định

        # Chọn công cụ đánh giá phù hợp
        evaluator = None
        metrics = {}

        if language == "vi" and "vietnamese" in self.evaluators:
            # Sử dụng Vietnamese RL Evaluator cho tiếng Việt
            evaluator = self.evaluators["vietnamese"]
            metrics = evaluator.evaluate_all_metrics(
                response=response,
                reference_response=reference_response,
                task_description=task_description,
                expected_keywords=expected_keywords,
                previous_responses=previous_responses
            )
        elif task_type == "rl" and "rl" in self.evaluators:
            # Sử dụng RL Evaluator cho nhiệm vụ RL
            evaluator = self.evaluators["rl"]
            metrics = evaluator.evaluate_all_metrics(
                response=response,
                reference_response=reference_response,
                task_description=task_description,
                expected_keywords=expected_keywords,
                previous_responses=previous_responses
            )
        elif task_type == "reasoning" and "reasoning" in self.evaluators:
            # Sử dụng Reasoning Evaluator cho nhiệm vụ reasoning
            evaluator = self.evaluators["reasoning"]
            metrics = evaluator.evaluate_reasoning(
                response=response,
                task_description=task_description,
                context=context
            )
        elif task_type == "factuality" and "factuality" in self.evaluators:
            # Sử dụng Factuality Evaluator cho nhiệm vụ factuality
            evaluator = self.evaluators["factuality"]
            metrics = evaluator.evaluate_factuality(
                response=response,
                facts=facts,
                context=context
            )

        # Nếu không có công cụ đánh giá phù hợp, sử dụng công cụ mặc định
        if not metrics and "rl" in self.evaluators:
            evaluator = self.evaluators["rl"]
            metrics = evaluator.evaluate_all_metrics(
                response=response,
                reference_response=reference_response,
                task_description=task_description,
                expected_keywords=expected_keywords,
                previous_responses=previous_responses
            )

        # Thêm thông tin về ngôn ngữ và loại nhiệm vụ
        metrics["language"] = language
        metrics["task_type"] = task_type
        metrics["evaluator_used"] = evaluator.__class__.__name__ if evaluator else "None"

        return metrics

    def evaluate_batch(
        self,
        responses: List[str],
        reference_responses: Optional[List[str]] = None,
        task_descriptions: Optional[List[str]] = None,
        expected_keywords_list: Optional[List[List[str]]] = None,
        previous_responses_list: Optional[List[List[str]]] = None,
        task_types: Optional[List[str]] = None,
        languages: Optional[List[str]] = None,
        contexts: Optional[List[str]] = None,
        facts_list: Optional[List[List[str]]] = None
    ) -> List[Dict[str, Any]]:
        """
        Đánh giá chất lượng của nhiều phản hồi.

        Args:
            responses: Danh sách các phản hồi cần đánh giá
            reference_responses: Danh sách các phản hồi tham chiếu
            task_descriptions: Danh sách các mô tả nhiệm vụ
            expected_keywords_list: Danh sách các danh sách từ khóa mong đợi
            previous_responses_list: Danh sách các danh sách phản hồi trước đó
            task_types: Danh sách các loại nhiệm vụ
            languages: Danh sách các ngôn ngữ
            contexts: Danh sách các ngữ cảnh
            facts_list: Danh sách các danh sách sự kiện

        Returns:
            Danh sách các dictionary chứa các metrics đánh giá
        """
        results = []

        for i, response in enumerate(responses):
            # Lấy các tham số tương ứng nếu có
            reference_response = reference_responses[i] if reference_responses and i < len(reference_responses) else None
            task_description = task_descriptions[i] if task_descriptions and i < len(task_descriptions) else None
            expected_keywords = expected_keywords_list[i] if expected_keywords_list and i < len(expected_keywords_list) else None
            previous_responses = previous_responses_list[i] if previous_responses_list and i < len(previous_responses_list) else None
            task_type = task_types[i] if task_types and i < len(task_types) else None
            language = languages[i] if languages and i < len(languages) else None
            context = contexts[i] if contexts and i < len(contexts) else None
            facts = facts_list[i] if facts_list and i < len(facts_list) else None

            # Đánh giá phản hồi
            metrics = self.evaluate(
                response=response,
                reference_response=reference_response,
                task_description=task_description,
                expected_keywords=expected_keywords,
                previous_responses=previous_responses,
                task_type=task_type,
                language=language,
                context=context,
                facts=facts
            )

            results.append(metrics)

        return results

    def get_available_evaluators(self) -> List[str]:
        """
        Lấy danh sách các công cụ đánh giá có sẵn.

        Returns:
            Danh sách tên các công cụ đánh giá có sẵn
        """
        return list(self.evaluators.keys())

    def get_evaluator(self, name: str) -> Optional[BaseEvaluator]:
        """
        Lấy công cụ đánh giá theo tên.

        Args:
            name: Tên công cụ đánh giá

        Returns:
            Công cụ đánh giá hoặc None nếu không tìm thấy
        """
        return self.evaluators.get(name)
