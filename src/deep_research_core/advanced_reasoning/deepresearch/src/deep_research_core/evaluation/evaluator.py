"""
Evaluator for Deep Research Core.
"""

import os
import json
import logging
import time
from typing import Dict, Any, List, Optional, Union, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed

from config.app_config import CACHE_DIR
from evaluation.evaluation_dataset import get_evaluation_queries
from evaluation.metrics import calculate_metrics
from reasoning.enhanced_cotrag import EnhancedCoTRAG
from reasoning.cot_rag import CoTRAG

logger = logging.getLogger(__name__)

class Evaluator:
    """
    Evaluator for Deep Research Core.
    """
    
    def __init__(
        self,
        provider: str = "openai",
        model: Optional[str] = None,
        language: str = "en",
        documents: List[Dict[str, Any]] = None,
        results_dir: Optional[str] = None
    ):
        """
        Initialize the evaluator.
        
        Args:
            provider: The provider to use
            model: The model to use
            language: Language to evaluate in
            documents: List of documents to use for retrieval
            results_dir: Directory to save results
        """
        self.provider = provider
        self.model = model
        self.language = language
        self.documents = documents or []
        self.results_dir = results_dir or os.path.join(str(CACHE_DIR), "evaluation_results")
        
        # Create results directory
        os.makedirs(self.results_dir, exist_ok=True)
    
    def evaluate_cotrag(
        self,
        question_type: Optional[str] = None,
        num_queries: int = -1,
        parallel: bool = False,
        max_workers: int = 4
    ) -> Dict[str, Any]:
        """
        Evaluate CoTRAG on the evaluation dataset.
        
        Args:
            question_type: Type of question to evaluate (if None, evaluate all)
            num_queries: Number of queries to evaluate (-1 for all)
            parallel: Whether to evaluate in parallel
            max_workers: Maximum number of workers for parallel evaluation
            
        Returns:
            Dictionary containing evaluation results
        """
        # Get evaluation queries
        queries = get_evaluation_queries(question_type, self.language)
        
        # Limit the number of queries if specified
        if num_queries > 0:
            queries = queries[:num_queries]
        
        # Initialize CoTRAG
        cotrag = CoTRAG(
            provider=self.provider,
            model=self.model,
            vector_store=None,
            documents=self.documents
        )
        
        # Evaluate queries
        if parallel and len(queries) > 1:
            results = self._evaluate_parallel(cotrag, queries, max_workers)
        else:
            results = self._evaluate_sequential(cotrag, queries)
        
        # Calculate aggregate metrics
        aggregate_metrics = self._calculate_aggregate_metrics(results)
        
        # Create final results
        evaluation_results = {
            "provider": self.provider,
            "model": self.model,
            "language": self.language,
            "question_type": question_type or "all",
            "num_queries": len(queries),
            "aggregate_metrics": aggregate_metrics,
            "query_results": results
        }
        
        # Save results
        self._save_results(evaluation_results, "cotrag")
        
        return evaluation_results
    
    def evaluate_enhanced_cotrag(
        self,
        question_type: Optional[str] = None,
        num_queries: int = -1,
        parallel: bool = False,
        max_workers: int = 4
    ) -> Dict[str, Any]:
        """
        Evaluate Enhanced CoTRAG on the evaluation dataset.
        
        Args:
            question_type: Type of question to evaluate (if None, evaluate all)
            num_queries: Number of queries to evaluate (-1 for all)
            parallel: Whether to evaluate in parallel
            max_workers: Maximum number of workers for parallel evaluation
            
        Returns:
            Dictionary containing evaluation results
        """
        # Get evaluation queries
        queries = get_evaluation_queries(question_type, self.language)
        
        # Limit the number of queries if specified
        if num_queries > 0:
            queries = queries[:num_queries]
        
        # Initialize Enhanced CoTRAG
        enhanced_cotrag = EnhancedCoTRAG(
            provider=self.provider,
            model=self.model,
            documents=self.documents,
            language=self.language
        )
        
        # Evaluate queries
        if parallel and len(queries) > 1:
            results = self._evaluate_parallel(enhanced_cotrag, queries, max_workers)
        else:
            results = self._evaluate_sequential(enhanced_cotrag, queries)
        
        # Calculate aggregate metrics
        aggregate_metrics = self._calculate_aggregate_metrics(results)
        
        # Create final results
        evaluation_results = {
            "provider": self.provider,
            "model": self.model,
            "language": self.language,
            "question_type": question_type or "all",
            "num_queries": len(queries),
            "aggregate_metrics": aggregate_metrics,
            "query_results": results
        }
        
        # Save results
        self._save_results(evaluation_results, "enhanced_cotrag")
        
        return evaluation_results
    
    def compare_methods(
        self,
        question_type: Optional[str] = None,
        num_queries: int = -1
    ) -> Dict[str, Any]:
        """
        Compare CoTRAG and Enhanced CoTRAG on the evaluation dataset.
        
        Args:
            question_type: Type of question to evaluate (if None, evaluate all)
            num_queries: Number of queries to evaluate (-1 for all)
            
        Returns:
            Dictionary containing comparison results
        """
        # Evaluate CoTRAG
        cotrag_results = self.evaluate_cotrag(question_type, num_queries)
        
        # Evaluate Enhanced CoTRAG
        enhanced_cotrag_results = self.evaluate_enhanced_cotrag(question_type, num_queries)
        
        # Compare results
        comparison = {
            "provider": self.provider,
            "model": self.model,
            "language": self.language,
            "question_type": question_type or "all",
            "num_queries": cotrag_results["num_queries"],
            "cotrag_metrics": cotrag_results["aggregate_metrics"],
            "enhanced_cotrag_metrics": enhanced_cotrag_results["aggregate_metrics"],
            "metric_differences": self._calculate_metric_differences(
                cotrag_results["aggregate_metrics"],
                enhanced_cotrag_results["aggregate_metrics"]
            )
        }
        
        # Save comparison
        self._save_results(comparison, "comparison")
        
        return comparison
    
    def _evaluate_sequential(
        self,
        model: Union[CoTRAG, EnhancedCoTRAG],
        queries: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Evaluate queries sequentially.
        
        Args:
            model: Model to evaluate
            queries: List of queries to evaluate
            
        Returns:
            List of evaluation results
        """
        results = []
        
        for i, query_data in enumerate(queries):
            query = query_data["query"]
            question_type = query_data["question_type"]
            expected_sources = query_data["expected_sources"]
            expected_keywords = query_data["expected_keywords"]
            
            logger.info(f"Evaluating query {i+1}/{len(queries)}: {query}")
            
            try:
                # Process the query
                start_time = time.time()
                
                if isinstance(model, EnhancedCoTRAG):
                    response = model.process(query=query, question_type=question_type)
                else:
                    response = model.process(query=query)
                
                end_time = time.time()
                
                # Calculate metrics
                metrics = calculate_metrics(response, expected_sources, expected_keywords)
                
                # Add to results
                results.append({
                    "query": query,
                    "question_type": question_type,
                    "expected_sources": expected_sources,
                    "expected_keywords": expected_keywords,
                    "response": response,
                    "metrics": metrics,
                    "processing_time": end_time - start_time
                })
                
            except Exception as e:
                logger.error(f"Error evaluating query: {str(e)}")
                
                # Add error to results
                results.append({
                    "query": query,
                    "question_type": question_type,
                    "expected_sources": expected_sources,
                    "expected_keywords": expected_keywords,
                    "error": str(e)
                })
        
        return results
    
    def _evaluate_parallel(
        self,
        model: Union[CoTRAG, EnhancedCoTRAG],
        queries: List[Dict[str, Any]],
        max_workers: int
    ) -> List[Dict[str, Any]]:
        """
        Evaluate queries in parallel.
        
        Args:
            model: Model to evaluate
            queries: List of queries to evaluate
            max_workers: Maximum number of workers
            
        Returns:
            List of evaluation results
        """
        results = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit tasks
            futures = {}
            for i, query_data in enumerate(queries):
                future = executor.submit(
                    self._evaluate_single_query,
                    model,
                    query_data,
                    i,
                    len(queries)
                )
                futures[future] = query_data
            
            # Collect results
            for future in as_completed(futures):
                query_data = futures[future]
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    logger.error(f"Error evaluating query: {str(e)}")
                    
                    # Add error to results
                    results.append({
                        "query": query_data["query"],
                        "question_type": query_data["question_type"],
                        "expected_sources": query_data["expected_sources"],
                        "expected_keywords": query_data["expected_keywords"],
                        "error": str(e)
                    })
        
        return results
    
    def _evaluate_single_query(
        self,
        model: Union[CoTRAG, EnhancedCoTRAG],
        query_data: Dict[str, Any],
        index: int,
        total: int
    ) -> Dict[str, Any]:
        """
        Evaluate a single query.
        
        Args:
            model: Model to evaluate
            query_data: Query data
            index: Index of the query
            total: Total number of queries
            
        Returns:
            Evaluation result
        """
        query = query_data["query"]
        question_type = query_data["question_type"]
        expected_sources = query_data["expected_sources"]
        expected_keywords = query_data["expected_keywords"]
        
        logger.info(f"Evaluating query {index+1}/{total}: {query}")
        
        try:
            # Process the query
            start_time = time.time()
            
            if isinstance(model, EnhancedCoTRAG):
                response = model.process(query=query, question_type=question_type)
            else:
                response = model.process(query=query)
            
            end_time = time.time()
            
            # Calculate metrics
            metrics = calculate_metrics(response, expected_sources, expected_keywords)
            
            # Return result
            return {
                "query": query,
                "question_type": question_type,
                "expected_sources": expected_sources,
                "expected_keywords": expected_keywords,
                "response": response,
                "metrics": metrics,
                "processing_time": end_time - start_time
            }
            
        except Exception as e:
            logger.error(f"Error evaluating query: {str(e)}")
            raise
    
    def _calculate_aggregate_metrics(self, results: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        Calculate aggregate metrics from individual results.
        
        Args:
            results: List of evaluation results
            
        Returns:
            Dictionary containing aggregate metrics
        """
        # Initialize aggregate metrics
        aggregate_metrics = {
            "source_recall": 0.0,
            "keyword_presence": 0.0,
            "reasoning_steps": 0.0,
            "citation_count": 0.0,
            "response_length": 0.0,
            "processing_time": 0.0
        }
        
        # Count valid results
        valid_results = 0
        
        # Sum metrics
        for result in results:
            if "metrics" in result:
                metrics = result["metrics"]
                aggregate_metrics["source_recall"] += metrics["source_recall"]
                aggregate_metrics["keyword_presence"] += metrics["keyword_presence"]
                aggregate_metrics["reasoning_steps"] += metrics["reasoning_steps"]
                aggregate_metrics["citation_count"] += metrics["citation_count"]
                aggregate_metrics["response_length"] += metrics["response_length"]
                aggregate_metrics["processing_time"] += result.get("processing_time", 0.0)
                valid_results += 1
        
        # Calculate averages
        if valid_results > 0:
            for key in aggregate_metrics:
                aggregate_metrics[key] /= valid_results
        
        return aggregate_metrics
    
    def _calculate_metric_differences(
        self,
        metrics1: Dict[str, float],
        metrics2: Dict[str, float]
    ) -> Dict[str, float]:
        """
        Calculate differences between two sets of metrics.
        
        Args:
            metrics1: First set of metrics
            metrics2: Second set of metrics
            
        Returns:
            Dictionary containing metric differences
        """
        differences = {}
        
        for key in metrics1:
            if key in metrics2:
                differences[key] = metrics2[key] - metrics1[key]
        
        return differences
    
    def _save_results(self, results: Dict[str, Any], prefix: str) -> None:
        """
        Save evaluation results to a file.
        
        Args:
            results: Evaluation results
            prefix: Prefix for the filename
        """
        # Create filename
        timestamp = int(time.time())
        question_type = results.get("question_type", "all")
        filename = f"{prefix}_{self.provider}_{question_type}_{timestamp}.json"
        filepath = os.path.join(self.results_dir, filename)
        
        # Save results
        try:
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            logger.info(f"Saved evaluation results to {filepath}")
        except Exception as e:
            logger.error(f"Error saving evaluation results: {str(e)}")
