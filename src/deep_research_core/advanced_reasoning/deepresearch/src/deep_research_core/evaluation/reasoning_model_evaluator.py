"""
Reasoning Model Evaluator.

This module provides tools for evaluating the performance of reasoning models
across various reasoning tasks and benchmarks.
"""

import os
import json
import time
import uuid
from typing import Dict, List, Any, Optional, Union, Tuple, Callable
import numpy as np
import pandas as pd
from tqdm import tqdm
import matplotlib.pyplot as plt
from concurrent.futures import ThreadPoolExecutor, as_completed

from ..models.base_reasoning_model import BaseReasoningModel
from ..utils.structured_logging import get_logger

logger = get_logger(__name__)


class ReasoningBenchmark:
    """
    Benchmark for evaluating reasoning performance of models.
    
    This class represents a collection of reasoning tasks that can be used
    to evaluate the performance of reasoning models.
    """
    
    def __init__(
        self,
        name: str,
        description: str,
        tasks: List[Dict[str, Any]],
        metrics: List[str],
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the reasoning benchmark.
        
        Args:
            name: Name of the benchmark
            description: Description of the benchmark
            tasks: List of tasks in the benchmark
            metrics: List of metrics to evaluate
            metadata: Additional metadata about the benchmark
        """
        self.name = name
        self.description = description
        self.tasks = tasks
        self.metrics = metrics
        self.metadata = metadata or {}
        
        logger.info(f"Initialized benchmark '{name}' with {len(tasks)} tasks")
    
    def __len__(self) -> int:
        """
        Return the number of tasks in the benchmark.
        
        Returns:
            Number of tasks
        """
        return len(self.tasks)
    
    def __getitem__(self, idx: int) -> Dict[str, Any]:
        """
        Get a task by index.
        
        Args:
            idx: Index of the task
            
        Returns:
            Task dictionary
        """
        return self.tasks[idx]
    
    @classmethod
    def from_file(cls, file_path: str) -> "ReasoningBenchmark":
        """
        Load a benchmark from a JSON file.
        
        Args:
            file_path: Path to the JSON file
            
        Returns:
            ReasoningBenchmark instance
        """
        with open(file_path, "r", encoding="utf-8") as f:
            data = json.load(f)
        
        return cls(
            name=data["name"],
            description=data["description"],
            tasks=data["tasks"],
            metrics=data["metrics"],
            metadata=data.get("metadata")
        )
    
    def to_file(self, file_path: str) -> None:
        """
        Save the benchmark to a JSON file.
        
        Args:
            file_path: Path to save the JSON file
        """
        data = {
            "name": self.name,
            "description": self.description,
            "tasks": self.tasks,
            "metrics": self.metrics,
            "metadata": self.metadata
        }
        
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=2)
    
    def filter_tasks(
        self,
        criteria: Dict[str, Any],
        mode: str = "and"
    ) -> "ReasoningBenchmark":
        """
        Filter tasks based on criteria.
        
        Args:
            criteria: Dictionary of field-value pairs to filter on
            mode: 'and' for all criteria to match, 'or' for any to match
            
        Returns:
            A new benchmark with filtered tasks
        """
        filtered_tasks = []
        
        for task in self.tasks:
            matches = []
            
            for field, value in criteria.items():
                if field in task and task[field] == value:
                    matches.append(True)
                else:
                    matches.append(False)
            
            # Determine if task should be included based on mode
            if mode == "and" and all(matches):
                filtered_tasks.append(task)
            elif mode == "or" and any(matches):
                filtered_tasks.append(task)
        
        return ReasoningBenchmark(
            name=f"{self.name}_filtered",
            description=f"Filtered version of {self.name}",
            tasks=filtered_tasks,
            metrics=self.metrics,
            metadata={**self.metadata, "filter_criteria": criteria, "filter_mode": mode}
        )
    
    def add_task(self, task: Dict[str, Any]) -> None:
        """
        Add a task to the benchmark.
        
        Args:
            task: Task dictionary to add
        """
        self.tasks.append(task)
        logger.info(f"Added task to benchmark '{self.name}', now has {len(self.tasks)} tasks")


class ReasoningModelEvaluator:
    """
    Evaluator for assessing reasoning model performance.
    
    This class provides methods for evaluating and comparing reasoning models
    on various tasks and benchmarks.
    """
    
    def __init__(
        self,
        benchmarks: Optional[Dict[str, ReasoningBenchmark]] = None,
        models: Optional[Dict[str, BaseReasoningModel]] = None,
        results_dir: str = "./evaluation_results",
        parallel: bool = True,
        max_workers: int = 4,
        verbose: bool = False
    ):
        """
        Initialize the reasoning model evaluator.
        
        Args:
            benchmarks: Dictionary of benchmark_id -> benchmark
            models: Dictionary of model_id -> model
            results_dir: Directory to store evaluation results
            parallel: Whether to run evaluations in parallel
            max_workers: Maximum number of parallel workers
            verbose: Whether to print detailed information
        """
        self.benchmarks = benchmarks or {}
        self.models = models or {}
        self.results_dir = results_dir
        self.parallel = parallel
        self.max_workers = max_workers
        self.verbose = verbose
        
        # Create results directory if it doesn't exist
        os.makedirs(results_dir, exist_ok=True)
        
        # Dictionary to store evaluation results
        self.results = {}
        
        logger.info(f"Initialized ReasoningModelEvaluator with {len(self.benchmarks)} benchmarks and {len(self.models)} models")
    
    def register_benchmark(self, benchmark_id: str, benchmark: ReasoningBenchmark) -> None:
        """
        Register a benchmark with the evaluator.
        
        Args:
            benchmark_id: Unique identifier for the benchmark
            benchmark: The benchmark instance
        """
        self.benchmarks[benchmark_id] = benchmark
        logger.info(f"Registered benchmark: {benchmark_id}")
    
    def register_model(self, model_id: str, model: BaseReasoningModel) -> None:
        """
        Register a model with the evaluator.
        
        Args:
            model_id: Unique identifier for the model
            model: The model instance
        """
        self.models[model_id] = model
        logger.info(f"Registered model: {model_id}")
    
    def evaluate_model(
        self,
        model_id: str,
        benchmark_id: str,
        max_samples: Optional[int] = None,
        tasks_filter: Optional[Dict[str, Any]] = None,
        filter_mode: str = "and",
        retry_count: int = 1,
        **generation_kwargs
    ) -> Dict[str, Any]:
        """
        Evaluate a model on a benchmark.
        
        Args:
            model_id: ID of the model to evaluate
            benchmark_id: ID of the benchmark to evaluate on
            max_samples: Maximum number of tasks to evaluate (None for all)
            tasks_filter: Optional filter for tasks
            filter_mode: 'and' for all criteria to match, 'or' for any to match
            retry_count: Number of retries for failed generations
            **generation_kwargs: Additional arguments for model.generate
            
        Returns:
            Dictionary of evaluation results
        """
        # Get model and benchmark
        model = self.models.get(model_id)
        if model is None:
            raise ValueError(f"Model '{model_id}' not found")
        
        benchmark = self.benchmarks.get(benchmark_id)
        if benchmark is None:
            raise ValueError(f"Benchmark '{benchmark_id}' not found")
        
        # Filter tasks if requested
        if tasks_filter:
            benchmark = benchmark.filter_tasks(tasks_filter, mode=filter_mode)
        
        # Apply max_samples if specified
        tasks = benchmark.tasks
        if max_samples is not None and max_samples < len(tasks):
            tasks = tasks[:max_samples]
        
        # Prepare results structure
        result_id = f"{model_id}_{benchmark_id}_{uuid.uuid4().hex[:8]}"
        results = {
            "result_id": result_id,
            "model_id": model_id,
            "benchmark_id": benchmark_id,
            "timestamp": time.time(),
            "num_tasks": len(tasks),
            "metrics": {},
            "task_results": [],
            "generation_kwargs": generation_kwargs,
            "metadata": {}
        }
        
        # Set up metric aggregations
        metric_values = {metric: [] for metric in benchmark.metrics}
        
        # Define evaluation function for a single task
        def evaluate_task(task):
            task_result = {
                "task_id": task.get("id", str(uuid.uuid4())),
                "input": task.get("input"),
                "expected": task.get("expected"),
                "metrics": {}
            }
            
            # Extract input and prompt
            prompt = task.get("input", "")
            expected = task.get("expected")
            system_message = task.get("system_message")
            
            # Track start time
            start_time = time.time()
            
            # Generate response with retries
            response = None
            error = None
            
            for attempt in range(retry_count):
                try:
                    response = model.generate_with_metrics(
                        prompt=prompt,
                        system_message=system_message,
                        **generation_kwargs
                    )
                    error = None
                    break
                except Exception as e:
                    error = str(e)
                    logger.warning(f"Error in attempt {attempt+1}/{retry_count}: {str(e)}")
                    time.sleep(1)  # Brief pause before retrying
            
            # Calculate latency
            latency = time.time() - start_time
            
            if error:
                task_result["error"] = error
                task_result["output"] = None
                task_result["metrics"] = {
                    "success": 0.0,
                    "latency": latency
                }
                return task_result
            
            # Extract generated text and metrics
            generated = response.get("text", "")
            task_result["output"] = generated
            task_result["response"] = response
            
            # Calculate basic metrics
            task_metrics = {
                "latency": latency,
                "success": 1.0
            }
            
            # Calculate accuracy if expected output is provided
            if expected is not None and "accuracy" in benchmark.metrics:
                # Simple exact match accuracy
                accuracy = 1.0 if generated.strip() == expected.strip() else 0.0
                task_metrics["accuracy"] = accuracy
            
            # Track token usage
            if "token_usage" in response:
                usage = response["token_usage"]
                for key, value in usage.items():
                    task_metrics[f"tokens_{key}"] = value
            
            # Calculate any custom metrics specified in the task
            task_metric_funcs = task.get("metric_functions", {})
            for metric_name, metric_func in task_metric_funcs.items():
                if callable(metric_func):
                    task_metrics[metric_name] = metric_func(generated, expected)
            
            task_result["metrics"] = task_metrics
            return task_result
        
        # Evaluate all tasks
        start_time = time.time()
        
        if self.parallel and len(tasks) > 1:
            with ThreadPoolExecutor(max_workers=min(self.max_workers, len(tasks))) as executor:
                future_to_task = {executor.submit(evaluate_task, task): task for task in tasks}
                
                if self.verbose:
                    future_iter = tqdm(as_completed(future_to_task), total=len(tasks), desc=f"Evaluating {model_id} on {benchmark_id}")
                else:
                    future_iter = as_completed(future_to_task)
                
                task_results = [future.result() for future in future_iter]
        else:
            if self.verbose:
                task_iter = tqdm(tasks, desc=f"Evaluating {model_id} on {benchmark_id}")
            else:
                task_iter = tasks
                
            task_results = [evaluate_task(task) for task in task_iter]
        
        # Calculate total time
        total_time = time.time() - start_time
        
        # Aggregate metrics
        for task_result in task_results:
            for metric, value in task_result["metrics"].items():
                if metric in metric_values:
                    metric_values[metric].append(value)
        
        # Calculate aggregate metrics
        for metric, values in metric_values.items():
            if values:
                results["metrics"][metric] = {
                    "mean": float(np.mean(values)),
                    "median": float(np.median(values)),
                    "min": float(np.min(values)),
                    "max": float(np.max(values)),
                    "std": float(np.std(values))
                }
        
        # Add task results and metadata
        results["task_results"] = task_results
        results["metadata"]["total_time"] = total_time
        results["metadata"]["avg_time_per_task"] = total_time / len(tasks) if tasks else 0
        
        # Store results
        self.results[result_id] = results
        
        # Save results to file
        results_path = os.path.join(self.results_dir, f"{result_id}.json")
        with open(results_path, "w", encoding="utf-8") as f:
            json.dump(results, f, indent=2)
        
        return results
    
    def compare_models(
        self,
        model_ids: List[str],
        benchmark_id: str,
        max_samples: Optional[int] = None,
        tasks_filter: Optional[Dict[str, Any]] = None,
        filter_mode: str = "and",
        **generation_kwargs
    ) -> Dict[str, Any]:
        """
        Compare multiple models on the same benchmark.
        
        Args:
            model_ids: List of model IDs to compare
            benchmark_id: ID of the benchmark to evaluate on
            max_samples: Maximum number of tasks to evaluate (None for all)
            tasks_filter: Optional filter for tasks
            filter_mode: 'and' for all criteria to match, 'or' for any to match
            **generation_kwargs: Additional arguments for model.generate
            
        Returns:
            Dictionary of comparison results
        """
        # Get benchmark
        benchmark = self.benchmarks.get(benchmark_id)
        if benchmark is None:
            raise ValueError(f"Benchmark '{benchmark_id}' not found")
        
        # Validate models
        for model_id in model_ids:
            if model_id not in self.models:
                raise ValueError(f"Model '{model_id}' not found")
        
        # Evaluate each model
        model_results = {}
        for model_id in model_ids:
            logger.info(f"Evaluating {model_id} on {benchmark_id}")
            results = self.evaluate_model(
                model_id=model_id,
                benchmark_id=benchmark_id,
                max_samples=max_samples,
                tasks_filter=tasks_filter,
                filter_mode=filter_mode,
                **generation_kwargs
            )
            model_results[model_id] = results
        
        # Compile comparison results
        comparison_id = f"comparison_{benchmark_id}_{uuid.uuid4().hex[:8]}"
        comparison = {
            "comparison_id": comparison_id,
            "benchmark_id": benchmark_id,
            "model_ids": model_ids,
            "timestamp": time.time(),
            "metrics_comparison": {},
            "model_results": {model_id: result["result_id"] for model_id, result in model_results.items()},
            "metadata": {}
        }
        
        # Compare metrics
        for metric in benchmark.metrics:
            comparison["metrics_comparison"][metric] = {}
            for model_id, results in model_results.items():
                if metric in results["metrics"]:
                    comparison["metrics_comparison"][metric][model_id] = results["metrics"][metric]
        
        # Save comparison to file
        comparison_path = os.path.join(self.results_dir, f"{comparison_id}.json")
        with open(comparison_path, "w", encoding="utf-8") as f:
            json.dump(comparison, f, indent=2)
        
        return comparison
    
    def generate_report(
        self,
        result_id: str,
        output_format: str = "html",
        output_path: Optional[str] = None
    ) -> str:
        """
        Generate a report for evaluation results.
        
        Args:
            result_id: ID of the evaluation results
            output_format: Format of the report ('html', 'md', 'json')
            output_path: Path to save the report (default: results_dir/reports)
            
        Returns:
            Path to the generated report
        """
        # Get results
        results = self.results.get(result_id)
        if results is None:
            # Try to load from file
            try:
                with open(os.path.join(self.results_dir, f"{result_id}.json"), "r") as f:
                    results = json.load(f)
            except:
                raise ValueError(f"Results '{result_id}' not found")
        
        # Create reports directory if it doesn't exist
        reports_dir = os.path.join(self.results_dir, "reports")
        os.makedirs(reports_dir, exist_ok=True)
        
        # Set default output path if not provided
        if output_path is None:
            output_path = os.path.join(reports_dir, f"{result_id}.{output_format}")
        
        # Generate report based on format
        if output_format == "json":
            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(results, f, indent=2)
        elif output_format in ["html", "md"]:
            # Get model and benchmark info
            model_id = results["model_id"]
            benchmark_id = results["benchmark_id"]
            
            model = self.models.get(model_id)
            model_name = model.model_name if model else model_id
            
            benchmark = self.benchmarks.get(benchmark_id)
            benchmark_name = benchmark.name if benchmark else benchmark_id
            
            # Create report content
            if output_format == "html":
                report = self._generate_html_report(results, model_name, benchmark_name)
            else:  # markdown
                report = self._generate_md_report(results, model_name, benchmark_name)
            
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(report)
        else:
            raise ValueError(f"Unsupported output format: {output_format}")
        
        return output_path
    
    def _generate_html_report(
        self,
        results: Dict[str, Any],
        model_name: str,
        benchmark_name: str
    ) -> str:
        """
        Generate an HTML report for evaluation results.
        
        Args:
            results: Evaluation results
            model_name: Name of the model
            benchmark_name: Name of the benchmark
            
        Returns:
            HTML report as a string
        """
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Evaluation Report: {model_name} on {benchmark_name}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
                h1, h2, h3 {{ color: #333; }}
                table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .metrics {{ margin-bottom: 30px; }}
                .task {{ margin-bottom: 20px; padding: 10px; border: 1px solid #eee; }}
                .success {{ color: green; }}
                .failure {{ color: red; }}
                .chart {{ margin: 20px 0; }}
            </style>
        </head>
        <body>
            <h1>Evaluation Report</h1>
            <p><strong>Model:</strong> {model_name}</p>
            <p><strong>Benchmark:</strong> {benchmark_name}</p>
            <p><strong>Date:</strong> {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(results["timestamp"]))}</p>
            <p><strong>Number of Tasks:</strong> {results["num_tasks"]}</p>
            
            <h2>Aggregate Metrics</h2>
            <div class="metrics">
                <table>
                    <tr>
                        <th>Metric</th>
                        <th>Mean</th>
                        <th>Median</th>
                        <th>Min</th>
                        <th>Max</th>
                        <th>Std Dev</th>
                    </tr>
        """
        
        # Add metrics rows
        for metric, values in results["metrics"].items():
            html += f"""
                    <tr>
                        <td>{metric}</td>
                        <td>{values["mean"]:.4f}</td>
                        <td>{values["median"]:.4f}</td>
                        <td>{values["min"]:.4f}</td>
                        <td>{values["max"]:.4f}</td>
                        <td>{values["std"]:.4f}</td>
                    </tr>
            """
        
        html += """
                </table>
            </div>
            
            <h2>Task Results</h2>
        """
        
        # Add task results
        for i, task in enumerate(results["task_results"][:10]):  # Limit to first 10 tasks
            success_class = "success" if task.get("error") is None else "failure"
            html += f"""
            <div class="task">
                <h3>Task {i+1}: {task.get("task_id", "")}</h3>
                <p><strong>Input:</strong> {task.get("input", "")}</p>
                <p><strong>Expected:</strong> {task.get("expected", "")}</p>
                <p><strong>Output:</strong> <span class="{success_class}">{task.get("output", "")}</span></p>
                <p><strong>Metrics:</strong></p>
                <ul>
            """
            
            for metric, value in task.get("metrics", {}).items():
                html += f"<li>{metric}: {value}</li>\n"
            
            html += """
                </ul>
            </div>
            """
        
        # Add note if there are more tasks
        if len(results["task_results"]) > 10:
            html += f"<p>Showing 10 of {len(results['task_results'])} tasks. See full results in the JSON file.</p>"
        
        html += """
        </body>
        </html>
        """
        
        return html
    
    def _generate_md_report(
        self,
        results: Dict[str, Any],
        model_name: str,
        benchmark_name: str
    ) -> str:
        """
        Generate a Markdown report for evaluation results.
        
        Args:
            results: Evaluation results
            model_name: Name of the model
            benchmark_name: Name of the benchmark
            
        Returns:
            Markdown report as a string
        """
        md = f"""# Evaluation Report

**Model:** {model_name}
**Benchmark:** {benchmark_name}
**Date:** {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(results["timestamp"]))}
**Number of Tasks:** {results["num_tasks"]}

## Aggregate Metrics

| Metric | Mean | Median | Min | Max | Std Dev |
|--------|------|--------|-----|-----|---------|
"""
        
        # Add metrics rows
        for metric, values in results["metrics"].items():
            md += f"| {metric} | {values['mean']:.4f} | {values['median']:.4f} | {values['min']:.4f} | {values['max']:.4f} | {values['std']:.4f} |\n"
        
        md += "\n## Task Results\n\n"
        
        # Add task results
        for i, task in enumerate(results["task_results"][:10]):  # Limit to first 10 tasks
            md += f"### Task {i+1}: {task.get('task_id', '')}\n\n"
            md += f"**Input:** {task.get('input', '')}\n\n"
            md += f"**Expected:** {task.get('expected', '')}\n\n"
            md += f"**Output:** {task.get('output', '')}\n\n"
            md += "**Metrics:**\n\n"
            
            for metric, value in task.get("metrics", {}).items():
                md += f"- {metric}: {value}\n"
            
            md += "\n"
        
        # Add note if there are more tasks
        if len(results["task_results"]) > 10:
            md += f"\nShowing 10 of {len(results['task_results'])} tasks. See full results in the JSON file.\n"
        
        return md
    
    def visualize_comparison(
        self,
        comparison_id: str,
        metrics: Optional[List[str]] = None,
        output_path: Optional[str] = None,
        chart_type: str = "bar",
        figsize: Tuple[int, int] = (12, 8)
    ) -> str:
        """
        Visualize a comparison of models.
        
        Args:
            comparison_id: ID of the comparison
            metrics: List of metrics to visualize (default: all)
            output_path: Path to save the visualization
            chart_type: Type of chart ('bar', 'radar', 'heatmap')
            figsize: Figure size (width, height)
            
        Returns:
            Path to the visualization
        """
        # Load comparison results
        comparison_path = os.path.join(self.results_dir, f"{comparison_id}.json")
        try:
            with open(comparison_path, "r") as f:
                comparison = json.load(f)
        except:
            raise ValueError(f"Comparison '{comparison_id}' not found")
        
        # Set default output path if not provided
        if output_path is None:
            output_path = os.path.join(self.results_dir, "reports", f"{comparison_id}_viz.png")
        
        # Create reports directory if needed
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Filter metrics if specified
        metrics_comparison = comparison["metrics_comparison"]
        if metrics is not None:
            metrics_comparison = {k: v for k, v in metrics_comparison.items() if k in metrics}
        
        # Prepare data for visualization
        model_ids = comparison["model_ids"]
        all_metrics = list(metrics_comparison.keys())
        
        if not all_metrics:
            raise ValueError("No metrics found in comparison")
        
        # Create DataFrame for plotting
        data = []
        for metric in all_metrics:
            metric_data = metrics_comparison[metric]
            for model_id in model_ids:
                if model_id in metric_data:
                    data.append({
                        "Model": model_id,
                        "Metric": metric,
                        "Value": metric_data[model_id]["mean"]
                    })
        
        df = pd.DataFrame(data)
        
        # Create visualization based on chart type
        plt.figure(figsize=figsize)
        
        if chart_type == "bar":
            ax = df.pivot(index="Metric", columns="Model", values="Value").plot(kind="bar", figsize=figsize)
            ax.set_ylabel("Value")
            ax.set_title("Model Comparison by Metric")
            plt.xticks(rotation=45)
            plt.tight_layout()
        elif chart_type == "radar":
            # Radar chart needs special handling
            # Convert to polar coordinates
            N = len(all_metrics)
            angles = [n / float(N) * 2 * np.pi for n in range(N)]
            angles += angles[:1]  # Close the loop
            
            fig, ax = plt.subplots(figsize=figsize, subplot_kw=dict(polar=True))
            
            for model_id in model_ids:
                values = []
                for metric in all_metrics:
                    if model_id in metrics_comparison[metric]:
                        values.append(metrics_comparison[metric][model_id]["mean"])
                    else:
                        values.append(0)
                values += values[:1]  # Close the loop
                
                ax.plot(angles, values, linewidth=1, label=model_id)
                ax.fill(angles, values, alpha=0.1)
            
            ax.set_xticks(angles[:-1])
            ax.set_xticklabels(all_metrics)
            ax.set_title("Model Comparison by Metric")
            plt.legend(loc="upper right")
        elif chart_type == "heatmap":
            pivot_df = df.pivot(index="Metric", columns="Model", values="Value")
            ax = plt.pcolor(pivot_df)
            plt.yticks(np.arange(0.5, len(pivot_df.index), 1), pivot_df.index)
            plt.xticks(np.arange(0.5, len(pivot_df.columns), 1), pivot_df.columns)
            plt.colorbar(ax)
            plt.title("Model Comparison Heatmap")
            plt.tight_layout()
        else:
            raise ValueError(f"Unsupported chart type: {chart_type}")
        
        # Save visualization
        plt.savefig(output_path, dpi=300, bbox_inches="tight")
        
        return output_path
    
    def get_best_model(
        self,
        benchmark_id: str,
        metric: str,
        higher_is_better: bool = True
    ) -> Tuple[Optional[str], float]:
        """
        Get the best model for a specific benchmark and metric.
        
        Args:
            benchmark_id: ID of the benchmark
            metric: Metric to compare on
            higher_is_better: Whether higher values are better
            
        Returns:
            Tuple of (best_model_id, metric_value) or (None, 0) if no results
        """
        # Find all results for this benchmark
        benchmark_results = []
        for result_id, results in self.results.items():
            if results["benchmark_id"] == benchmark_id:
                benchmark_results.append(results)
        
        if not benchmark_results:
            return None, 0
        
        # Compare models based on metric
        best_model_id = None
        best_value = float('-inf') if higher_is_better else float('inf')
        
        for results in benchmark_results:
            if metric in results["metrics"]:
                value = results["metrics"][metric]["mean"]
                
                if higher_is_better:
                    if value > best_value:
                        best_value = value
                        best_model_id = results["model_id"]
                else:
                    if value < best_value:
                        best_value = value
                        best_model_id = results["model_id"]
        
        return best_model_id, best_value


# Register predefined benchmarks
def create_reasoning_benchmark(name, description=None, metrics=None):
    """
    Create a reasoning benchmark with a specific name and configuration.
    
    This is a helper function to create common reasoning benchmarks.
    
    Args:
        name: Name of the benchmark to create
        description: Optional description
        metrics: Optional list of metrics
        
    Returns:
        ReasoningBenchmark instance
    """
    if name == "logical_reasoning":
        if description is None:
            description = "Benchmark for evaluating logical reasoning capabilities"
        
        if metrics is None:
            metrics = ["accuracy", "latency"]
        
        tasks = [
            {
                "id": "logical_1",
                "input": "If all A are B, and all B are C, what can we conclude?",
                "expected": "All A are C",
                "type": "logical_deduction"
            },
            {
                "id": "logical_2",
                "input": "If it is raining, then the ground is wet. The ground is wet. Can we conclude that it is raining?",
                "expected": "No, we cannot conclude that it is raining. The ground could be wet for other reasons.",
                "type": "logical_fallacy"
            },
            {
                "id": "logical_3",
                "input": "All birds can fly. Penguins are birds. Can penguins fly?",
                "expected": "The premise 'All birds can fly' is false. Many birds, including penguins, cannot fly.",
                "type": "premise_evaluation"
            }
        ]
        
        return ReasoningBenchmark(
            name=name,
            description=description,
            tasks=tasks,
            metrics=metrics
        )
    
    elif name == "mathematical_reasoning":
        if description is None:
            description = "Benchmark for evaluating mathematical reasoning capabilities"
        
        if metrics is None:
            metrics = ["accuracy", "latency", "step_accuracy"]
        
        tasks = [
            {
                "id": "math_1",
                "input": "What is the sum of all integers from 1 to 100?",
                "expected": "5050",
                "type": "arithmetic"
            },
            {
                "id": "math_2",
                "input": "If x + y = 10 and x - y = 4, what are the values of x and y?",
                "expected": "x = 7, y = 3",
                "type": "algebra"
            },
            {
                "id": "math_3",
                "input": "A circle has radius 5 units. What is its area in square units?",
                "expected": "25π square units",
                "type": "geometry"
            }
        ]
        
        return ReasoningBenchmark(
            name=name,
            description=description,
            tasks=tasks,
            metrics=metrics
        )
    
    elif name == "common_sense_reasoning":
        if description is None:
            description = "Benchmark for evaluating common sense reasoning capabilities"
        
        if metrics is None:
            metrics = ["accuracy", "latency"]
        
        tasks = [
            {
                "id": "common_1",
                "input": "If I put a glass of water in a freezer, what will happen after several hours?",
                "expected": "The water will freeze and become ice.",
                "type": "physical_common_sense"
            },
            {
                "id": "common_2",
                "input": "If someone is hungry, what might they do?",
                "expected": "They might eat food or look for something to eat.",
                "type": "social_common_sense"
            },
            {
                "id": "common_3",
                "input": "If I leave my car outside during a rainstorm, what will happen to the car?",
                "expected": "The car will get wet.",
                "type": "physical_common_sense"
            }
        ]
        
        return ReasoningBenchmark(
            name=name,
            description=description,
            tasks=tasks,
            metrics=metrics
        )
    
    else:
        raise ValueError(f"Unknown benchmark name: {name}") 