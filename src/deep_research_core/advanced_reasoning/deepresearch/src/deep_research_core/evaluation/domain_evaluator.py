"""
Domain-specific evaluator for Deep Research Core.

This module provides evaluation capabilities specific to different domains,
such as medical, legal, technical, and educational.
"""

import os
import json
import logging
import time
from typing import Dict, Any, List, Optional, Union, Tuple

from .evaluator import Evaluator
from .domain_metrics import get_domain_metrics
from ..utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

class DomainEvaluator(Evaluator):
    """
    Domain-specific evaluator for Deep Research Core.
    
    This class extends the base Evaluator with domain-specific evaluation capabilities.
    """
    
    def __init__(
        self,
        provider: str,
        model: str,
        language: str = "en",
        documents: Optional[List[Dict[str, Any]]] = None,
        domain: str = "general",
        expected_terms: Optional[Dict[str, List[str]]] = None,
        cache_dir: Optional[str] = None
    ):
        """
        Initialize the domain evaluator.
        
        Args:
            provider: Model provider (openai, anthropic, openrouter)
            model: Model name
            language: Language for evaluation
            documents: List of documents to use for evaluation
            domain: Domain to evaluate ("general", "medical", "legal", "technical", "educational")
            expected_terms: Dictionary mapping query types to lists of expected technical terms
            cache_dir: Directory to cache evaluation results
        """
        # Initialize the parent class
        super().__init__(
            provider=provider,
            model=model,
            language=language,
            documents=documents,
            cache_dir=cache_dir
        )
        
        # Store domain-specific parameters
        self.domain = domain
        self.expected_terms = expected_terms or {}
        
        # Initialize domain metrics
        self.domain_metrics = get_domain_metrics(domain)
    
    def evaluate_domain_response(
        self,
        response: Dict[str, Any],
        query_type: Optional[str] = None
    ) -> Dict[str, float]:
        """
        Evaluate a response using domain-specific metrics.
        
        Args:
            response: Response from the model
            query_type: Type of query (for expected terms)
            
        Returns:
            Dictionary containing domain-specific evaluation metrics
        """
        # Get expected terms for this query type
        expected_terms = self.expected_terms.get(query_type, []) if query_type else []
        
        # Calculate domain-specific metrics
        metrics = self.domain_metrics.calculate_domain_metrics(response, expected_terms)
        
        return metrics
    
    def _evaluate_sequential_with_domain(
        self,
        model: Any,
        queries: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Evaluate queries sequentially with domain-specific metrics.
        
        Args:
            model: Model to evaluate
            queries: List of queries to evaluate
            
        Returns:
            List of evaluation results
        """
        results = []
        
        for query_data in queries:
            query = query_data["query"]
            question_type = query_data.get("question_type")
            expected_sources = query_data.get("expected_sources", [])
            expected_keywords = query_data.get("expected_keywords", [])
            
            logger.info(f"Evaluating query: {query}")
            
            try:
                # Process the query
                start_time = time.time()
                
                if hasattr(model, "process"):
                    response = model.process(query=query, question_type=question_type)
                else:
                    response = model(query=query)
                
                end_time = time.time()
                
                # Calculate standard metrics
                standard_metrics = self._calculate_metrics(response, expected_sources, expected_keywords)
                
                # Calculate domain-specific metrics
                domain_metrics = self.evaluate_domain_response(response, question_type)
                
                # Combine metrics
                metrics = {**standard_metrics, **domain_metrics}
                
                # Add to results
                results.append({
                    "query": query,
                    "question_type": question_type,
                    "expected_sources": expected_sources,
                    "expected_keywords": expected_keywords,
                    "response": response,
                    "metrics": metrics,
                    "processing_time": end_time - start_time
                })
                
            except Exception as e:
                logger.error(f"Error evaluating query: {str(e)}")
                results.append({
                    "query": query,
                    "question_type": question_type,
                    "error": str(e)
                })
        
        return results
    
    def evaluate_cotrag_with_domain(
        self,
        question_type: Optional[str] = None,
        num_queries: int = -1,
        parallel: bool = False,
        max_workers: int = 4
    ) -> Dict[str, Any]:
        """
        Evaluate CoTRAG on the evaluation dataset with domain-specific metrics.
        
        Args:
            question_type: Type of question to evaluate (if None, evaluate all)
            num_queries: Number of queries to evaluate (-1 for all)
            parallel: Whether to evaluate in parallel
            max_workers: Maximum number of workers for parallel evaluation
            
        Returns:
            Dictionary containing evaluation results
        """
        # Get evaluation queries
        queries = self._get_evaluation_queries(question_type, self.language)
        
        # Limit the number of queries if specified
        if num_queries > 0:
            queries = queries[:num_queries]
        
        # Initialize CoTRAG
        cotrag = self._initialize_cotrag()
        
        # Evaluate queries
        if parallel and len(queries) > 1:
            results = self._evaluate_parallel(cotrag, queries, max_workers)
        else:
            results = self._evaluate_sequential_with_domain(cotrag, queries)
        
        # Calculate aggregate metrics
        aggregate_metrics = self._calculate_aggregate_metrics_with_domain(results)
        
        # Create final results
        evaluation_results = {
            "provider": self.provider,
            "model": self.model,
            "language": self.language,
            "domain": self.domain,
            "question_type": question_type or "all",
            "num_queries": len(queries),
            "aggregate_metrics": aggregate_metrics,
            "query_results": results
        }
        
        # Save results
        self._save_domain_results(evaluation_results, "cotrag")
        
        return evaluation_results
    
    def evaluate_enhanced_cotrag_with_domain(
        self,
        question_type: Optional[str] = None,
        num_queries: int = -1,
        parallel: bool = False,
        max_workers: int = 4
    ) -> Dict[str, Any]:
        """
        Evaluate Enhanced CoTRAG on the evaluation dataset with domain-specific metrics.
        
        Args:
            question_type: Type of question to evaluate (if None, evaluate all)
            num_queries: Number of queries to evaluate (-1 for all)
            parallel: Whether to evaluate in parallel
            max_workers: Maximum number of workers for parallel evaluation
            
        Returns:
            Dictionary containing evaluation results
        """
        # Get evaluation queries
        queries = self._get_evaluation_queries(question_type, self.language)
        
        # Limit the number of queries if specified
        if num_queries > 0:
            queries = queries[:num_queries]
        
        # Initialize Enhanced CoTRAG
        enhanced_cotrag = self._initialize_enhanced_cotrag()
        
        # Evaluate queries
        if parallel and len(queries) > 1:
            results = self._evaluate_parallel(enhanced_cotrag, queries, max_workers)
        else:
            results = self._evaluate_sequential_with_domain(enhanced_cotrag, queries)
        
        # Calculate aggregate metrics
        aggregate_metrics = self._calculate_aggregate_metrics_with_domain(results)
        
        # Create final results
        evaluation_results = {
            "provider": self.provider,
            "model": self.model,
            "language": self.language,
            "domain": self.domain,
            "question_type": question_type or "all",
            "num_queries": len(queries),
            "aggregate_metrics": aggregate_metrics,
            "query_results": results
        }
        
        # Save results
        self._save_domain_results(evaluation_results, "enhanced_cotrag")
        
        return evaluation_results
    
    def _calculate_aggregate_metrics_with_domain(self, results: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        Calculate aggregate metrics including domain-specific metrics.
        
        Args:
            results: List of evaluation results
            
        Returns:
            Dictionary containing aggregate metrics
        """
        # Initialize aggregate metrics
        aggregate_metrics = {}
        
        # Get all metric names
        metric_names = set()
        for result in results:
            if "metrics" in result:
                metric_names.update(result["metrics"].keys())
        
        # Calculate average for each metric
        for metric_name in metric_names:
            values = [result["metrics"][metric_name] for result in results if "metrics" in result and metric_name in result["metrics"]]
            if values:
                aggregate_metrics[metric_name] = sum(values) / len(values)
        
        # Calculate overall score
        if "overall_domain_score" in aggregate_metrics:
            # Domain-specific overall score
            aggregate_metrics["overall_score"] = aggregate_metrics["overall_domain_score"]
        else:
            # Standard overall score
            standard_metrics = ["source_recall", "keyword_presence", "reasoning_steps", "citation_count"]
            standard_values = [aggregate_metrics.get(metric, 0.0) for metric in standard_metrics if metric in aggregate_metrics]
            if standard_values:
                aggregate_metrics["overall_score"] = sum(standard_values) / len(standard_values)
        
        return aggregate_metrics
    
    def _save_domain_results(self, results: Dict[str, Any], method: str) -> None:
        """
        Save domain evaluation results to a file.
        
        Args:
            results: Evaluation results
            method: Evaluation method ("cotrag", "enhanced_cotrag")
        """
        if not self.cache_dir:
            return
        
        # Create results directory
        results_dir = os.path.join(self.cache_dir, "domain_evaluation_results")
        os.makedirs(results_dir, exist_ok=True)
        
        # Create filename
        question_type = results.get("question_type", "all")
        filename = f"{method}_{self.domain}_{self.language}_{question_type}_{self.provider}_{self.model}.json"
        filepath = os.path.join(results_dir, filename)
        
        # Save results
        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Saved domain evaluation results to {filepath}")
    
    def compare_methods_with_domain(
        self,
        question_type: Optional[str] = None,
        num_queries: int = -1
    ) -> Dict[str, Any]:
        """
        Compare CoTRAG and Enhanced CoTRAG with domain-specific metrics.
        
        Args:
            question_type: Type of question to evaluate (if None, evaluate all)
            num_queries: Number of queries to evaluate (-1 for all)
            
        Returns:
            Dictionary containing comparison results
        """
        # Evaluate CoTRAG
        cotrag_results = self.evaluate_cotrag_with_domain(question_type, num_queries)
        
        # Evaluate Enhanced CoTRAG
        enhanced_cotrag_results = self.evaluate_enhanced_cotrag_with_domain(question_type, num_queries)
        
        # Compare results
        comparison = {
            "provider": self.provider,
            "model": self.model,
            "language": self.language,
            "domain": self.domain,
            "question_type": question_type or "all",
            "num_queries": cotrag_results["num_queries"],
            "cotrag_metrics": cotrag_results["aggregate_metrics"],
            "enhanced_cotrag_metrics": enhanced_cotrag_results["aggregate_metrics"],
            "metric_differences": self._calculate_metric_differences(
                cotrag_results["aggregate_metrics"],
                enhanced_cotrag_results["aggregate_metrics"]
            )
        }
        
        # Save comparison
        if self.cache_dir:
            results_dir = os.path.join(self.cache_dir, "domain_evaluation_results")
            os.makedirs(results_dir, exist_ok=True)
            
            filename = f"comparison_{self.domain}_{self.language}_{question_type or 'all'}_{self.provider}_{self.model}.json"
            filepath = os.path.join(results_dir, filename)
            
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(comparison, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Saved domain comparison results to {filepath}")
        
        return comparison
