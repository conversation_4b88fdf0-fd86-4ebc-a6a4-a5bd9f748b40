"""
Vietnamese benchmark datasets and evaluation utilities.

This module provides standardized datasets and evaluation methods
specifically designed for Vietnamese language models.
"""

import os
import json
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Union, Set
from dataclasses import dataclass, field

from ..utils.structured_logging import get_logger
from ..utils.vietnamese_utils import detect_vietnamese, normalize_vietnamese_text
from .vietnamese_metrics import VietnameseEvaluationMetrics

logger = get_logger(__name__)

@dataclass
class VietnameseBenchmarkExample:
    """Single example for Vietnamese benchmark evaluation."""
    
    id: str
    question: str
    context: Optional[str] = None
    reference_answer: Optional[str] = None
    reference_answers: List[str] = field(default_factory=list)
    domain: str = "general"
    difficulty: str = "medium"
    dialect: str = "northern"
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class VietnameseBenchmarkDataset:
    """Dataset for Vietnamese benchmark evaluation."""
    
    name: str
    description: str
    version: str
    examples: List[VietnameseBenchmarkExample]
    domain: str = "general"
    dialect: str = "northern"
    metrics: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Initialize after creation."""
        # Set default metrics if not provided
        if not self.metrics:
            self.metrics = [
                "diacritic_consistency", 
                "compound_word_accuracy",
                "dialect_consistency", 
                "reasoning_quality",
                "lexical_diversity", 
                "academic_language_usage",
                "coherence", 
                "grammatical_correctness"
            ]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert dataset to dictionary."""
        return {
            "name": self.name,
            "description": self.description,
            "version": self.version,
            "domain": self.domain,
            "dialect": self.dialect,
            "metrics": self.metrics,
            "metadata": self.metadata,
            "examples": [
                {
                    "id": ex.id,
                    "question": ex.question,
                    "context": ex.context,
                    "reference_answer": ex.reference_answer,
                    "reference_answers": ex.reference_answers,
                    "domain": ex.domain,
                    "difficulty": ex.difficulty,
                    "dialect": ex.dialect,
                    "metadata": ex.metadata
                }
                for ex in self.examples
            ]
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "VietnameseBenchmarkDataset":
        """Create dataset from dictionary."""
        examples = [
            VietnameseBenchmarkExample(
                id=ex["id"],
                question=ex["question"],
                context=ex.get("context"),
                reference_answer=ex.get("reference_answer"),
                reference_answers=ex.get("reference_answers", []),
                domain=ex.get("domain", "general"),
                difficulty=ex.get("difficulty", "medium"),
                dialect=ex.get("dialect", "northern"),
                metadata=ex.get("metadata", {})
            )
            for ex in data["examples"]
        ]
        
        return cls(
            name=data["name"],
            description=data["description"],
            version=data["version"],
            examples=examples,
            domain=data.get("domain", "general"),
            dialect=data.get("dialect", "northern"),
            metrics=data.get("metrics", []),
            metadata=data.get("metadata", {})
        )
    
    def save(self, path: str) -> None:
        """Save dataset to file."""
        with open(path, "w", encoding="utf-8") as f:
            json.dump(self.to_dict(), f, ensure_ascii=False, indent=2)
    
    @classmethod
    def load(cls, path: str) -> "VietnameseBenchmarkDataset":
        """Load dataset from file."""
        with open(path, "r", encoding="utf-8") as f:
            data = json.load(f)
        
        return cls.from_dict(data)
    
    def get_example(self, example_id: str) -> Optional[VietnameseBenchmarkExample]:
        """Get example by ID."""
        for example in self.examples:
            if example.id == example_id:
                return example
        return None
    
    def filter_by_domain(self, domain: str) -> "VietnameseBenchmarkDataset":
        """Filter examples by domain."""
        filtered_examples = [ex for ex in self.examples if ex.domain == domain]
        
        return VietnameseBenchmarkDataset(
            name=f"{self.name}-{domain}",
            description=f"{self.description} (filtered by domain: {domain})",
            version=self.version,
            examples=filtered_examples,
            domain=domain,
            dialect=self.dialect,
            metrics=self.metrics,
            metadata=self.metadata
        )
    
    def filter_by_dialect(self, dialect: str) -> "VietnameseBenchmarkDataset":
        """Filter examples by dialect."""
        filtered_examples = [ex for ex in self.examples if ex.dialect == dialect]
        
        return VietnameseBenchmarkDataset(
            name=f"{self.name}-{dialect}",
            description=f"{self.description} (filtered by dialect: {dialect})",
            version=self.version,
            examples=filtered_examples,
            domain=self.domain,
            dialect=dialect,
            metrics=self.metrics,
            metadata=self.metadata
        )
    
    def filter_by_difficulty(self, difficulty: str) -> "VietnameseBenchmarkDataset":
        """Filter examples by difficulty."""
        filtered_examples = [ex for ex in self.examples if ex.difficulty == difficulty]
        
        return VietnameseBenchmarkDataset(
            name=f"{self.name}-{difficulty}",
            description=f"{self.description} (filtered by difficulty: {difficulty})",
            version=self.version,
            examples=filtered_examples,
            domain=self.domain,
            dialect=self.dialect,
            metrics=self.metrics,
            metadata=self.metadata
        )

class VietnameseBenchmarkRegistry:
    """Registry for Vietnamese benchmark datasets."""
    
    # Default dataset registry
    _datasets = {}
    
    # Default dataset directory
    _dataset_dir = os.path.join(os.path.dirname(__file__), "..", "..", "..", "data", "benchmarks", "vietnamese")
    
    @classmethod
    def register_dataset(cls, dataset: VietnameseBenchmarkDataset) -> None:
        """Register dataset."""
        cls._datasets[dataset.name] = dataset
        logger.info(f"Registered Vietnamese benchmark dataset: {dataset.name}")
    
    @classmethod
    def get_dataset(cls, name: str) -> Optional[VietnameseBenchmarkDataset]:
        """Get dataset by name."""
        # If dataset is already registered, return it
        if name in cls._datasets:
            return cls._datasets[name]
        
        # Try to load dataset from file
        dataset_path = os.path.join(cls._dataset_dir, f"{name}.json")
        if os.path.exists(dataset_path):
            try:
                dataset = VietnameseBenchmarkDataset.load(dataset_path)
                cls.register_dataset(dataset)
                return dataset
            except Exception as e:
                logger.error(f"Error loading dataset {name}: {str(e)}")
        
        return None
    
    @classmethod
    def list_datasets(cls) -> List[str]:
        """List all registered datasets."""
        # Check for datasets in the dataset directory
        if os.path.exists(cls._dataset_dir):
            for filename in os.listdir(cls._dataset_dir):
                if filename.endswith(".json"):
                    name = filename[:-5]  # Remove .json extension
                    if name not in cls._datasets:
                        try:
                            dataset = VietnameseBenchmarkDataset.load(
                                os.path.join(cls._dataset_dir, filename)
                            )
                            cls.register_dataset(dataset)
                        except Exception as e:
                            logger.error(f"Error loading dataset {name}: {str(e)}")
        
        return list(cls._datasets.keys())
    
    @classmethod
    def create_default_datasets(cls) -> None:
        """Create and register default Vietnamese benchmark datasets."""
        # Create general QA dataset
        general_qa_examples = [
            VietnameseBenchmarkExample(
                id="general-qa-001",
                question="Thủ đô của Việt Nam là gì?",
                reference_answer="Hà Nội là thủ đô của Việt Nam.",
                reference_answers=["Hà Nội", "Thủ đô của Việt Nam là Hà Nội."],
                domain="general",
                difficulty="easy",
                dialect="northern"
            ),
            VietnameseBenchmarkExample(
                id="general-qa-002",
                question="Việt Nam có bao nhiêu tỉnh thành?",
                reference_answer="Việt Nam có 63 tỉnh thành, bao gồm 58 tỉnh và 5 thành phố trực thuộc trung ương.",
                reference_answers=[
                    "63 tỉnh thành", 
                    "63", 
                    "Việt Nam có 63 tỉnh thành."
                ],
                domain="general",
                difficulty="easy",
                dialect="northern"
            ),
            VietnameseBenchmarkExample(
                id="general-qa-003",
                question="Ai là người đầu tiên đặt chân lên mặt trăng?",
                reference_answer="Neil Armstrong là người đầu tiên đặt chân lên mặt trăng vào ngày 20 tháng 7 năm 1969.",
                reference_answers=[
                    "Neil Armstrong", 
                    "Phi hành gia Neil Armstrong"
                ],
                domain="general",
                difficulty="easy",
                dialect="northern"
            )
        ]
        
        general_qa_dataset = VietnameseBenchmarkDataset(
            name="vietnamese-general-qa",
            description="Bộ dữ liệu hỏi đáp tổng quát tiếng Việt",
            version="1.0.0",
            examples=general_qa_examples,
            domain="general",
            dialect="northern"
        )
        
        cls.register_dataset(general_qa_dataset)
        
        # Create science QA dataset
        science_qa_examples = [
            VietnameseBenchmarkExample(
                id="science-qa-001",
                question="Định luật vạn vật hấp dẫn của Newton phát biểu như thế nào?",
                reference_answer="Định luật vạn vật hấp dẫn của Newton phát biểu rằng mọi vật thể trong vũ trụ đều hút nhau với một lực tỷ lệ thuận với tích khối lượng của chúng và tỷ lệ nghịch với bình phương khoảng cách giữa chúng.",
                domain="science",
                difficulty="medium",
                dialect="northern"
            ),
            VietnameseBenchmarkExample(
                id="science-qa-002",
                question="DNA là viết tắt của từ gì?",
                reference_answer="DNA là viết tắt của Deoxyribonucleic Acid (Axit Deoxyribonucleic).",
                reference_answers=[
                    "Deoxyribonucleic Acid", 
                    "Axit Deoxyribonucleic"
                ],
                domain="science",
                difficulty="easy",
                dialect="northern"
            ),
            VietnameseBenchmarkExample(
                id="science-qa-003",
                question="Nguyên tố hóa học nào chiếm tỷ lệ cao nhất trong vỏ Trái Đất?",
                reference_answer="Oxy (O) là nguyên tố hóa học chiếm tỷ lệ cao nhất trong vỏ Trái Đất, chiếm khoảng 46% theo khối lượng.",
                reference_answers=["Oxy", "O", "Oxy (O)"],
                domain="science",
                difficulty="medium",
                dialect="northern"
            )
        ]
        
        science_qa_dataset = VietnameseBenchmarkDataset(
            name="vietnamese-science-qa",
            description="Bộ dữ liệu hỏi đáp khoa học tiếng Việt",
            version="1.0.0",
            examples=science_qa_examples,
            domain="science",
            dialect="northern"
        )
        
        cls.register_dataset(science_qa_dataset)
        
        # Create Southern dialect dataset
        southern_dialect_examples = [
            VietnameseBenchmarkExample(
                id="southern-dialect-001",
                question="Xe hơi màu gì đậu trước nhà bạn?",
                reference_answer="Chiếc xe hơi màu trắng đang đậu trước nhà tui.",
                domain="general",
                difficulty="easy",
                dialect="southern"
            ),
            VietnameseBenchmarkExample(
                id="southern-dialect-002",
                question="Bạn thích ăn món gì nhất ở Sài Gòn?",
                reference_answer="Tui thích ăn hủ tiếu Nam Vang và bánh tráng trộn nhất khi ở Sài Gòn.",
                domain="food",
                difficulty="easy",
                dialect="southern"
            ),
            VietnameseBenchmarkExample(
                id="southern-dialect-003",
                question="Hôm nay trời nóng quá, phải không?",
                reference_answer="Đúng rồi, hôm nay trời nóng dữ lắm, tui đi ra ngoài một chút là muốn xỉu liền.",
                domain="general",
                difficulty="easy",
                dialect="southern"
            )
        ]
        
        southern_dialect_dataset = VietnameseBenchmarkDataset(
            name="vietnamese-southern-dialect",
            description="Bộ dữ liệu tiếng Việt phương ngữ Nam Bộ",
            version="1.0.0",
            examples=southern_dialect_examples,
            domain="general",
            dialect="southern"
        )
        
        cls.register_dataset(southern_dialect_dataset)
        
        # Save datasets to files
        os.makedirs(cls._dataset_dir, exist_ok=True)
        
        general_qa_dataset.save(os.path.join(cls._dataset_dir, "vietnamese-general-qa.json"))
        science_qa_dataset.save(os.path.join(cls._dataset_dir, "vietnamese-science-qa.json"))
        southern_dialect_dataset.save(os.path.join(cls._dataset_dir, "vietnamese-southern-dialect.json"))

class VietnameseBenchmarkEvaluator:
    """Evaluator for Vietnamese benchmark datasets."""
    
    def __init__(self, metrics: Optional[List[str]] = None):
        """
        Initialize Vietnamese benchmark evaluator.
        
        Args:
            metrics: List of metrics to evaluate
        """
        self.metrics_calculator = VietnameseEvaluationMetrics()
        
        # Default metrics
        self.metrics = metrics or [
            "diacritic_consistency", 
            "compound_word_accuracy",
            "dialect_consistency", 
            "reasoning_quality",
            "lexical_diversity", 
            "academic_language_usage",
            "coherence", 
            "grammatical_correctness"
        ]
    
    def evaluate_answer(
        self, 
        predicted_answer: str, 
        reference_answer: str, 
        reference_answers: Optional[List[str]] = None,
        metrics: Optional[List[str]] = None
    ) -> Dict[str, float]:
        """
        Evaluate a single answer.
        
        Args:
            predicted_answer: Predicted answer
            reference_answer: Reference answer
            reference_answers: Alternative reference answers
            metrics: Metrics to evaluate
            
        Returns:
            Dictionary with evaluation results
        """
        if not metrics:
            metrics = self.metrics
        
        results = {}
        
        # Calculate Vietnamese-specific metrics
        if "diacritic_consistency" in metrics:
            results["diacritic_consistency"] = self.metrics_calculator.calculate_diacritic_consistency(
                predicted_answer
            )
        
        if "compound_word_accuracy" in metrics:
            results["compound_word_accuracy"] = self.metrics_calculator.calculate_compound_word_accuracy(
                predicted_answer
            )
        
        if "dialect_consistency" in metrics:
            results["dialect_consistency"] = self.metrics_calculator.calculate_dialect_consistency(
                predicted_answer
            )
        
        if "reasoning_quality" in metrics:
            results["reasoning_quality"] = self.metrics_calculator.calculate_reasoning_quality(
                predicted_answer
            )
        
        if "lexical_diversity" in metrics:
            results["lexical_diversity"] = self.metrics_calculator.calculate_lexical_diversity(
                predicted_answer
            )
        
        if "academic_language_usage" in metrics:
            results["academic_language_usage"] = self.metrics_calculator.calculate_academic_language_usage(
                predicted_answer
            )
        
        if "coherence" in metrics:
            results["coherence"] = self.metrics_calculator.calculate_coherence(
                predicted_answer
            )
        
        if "grammatical_correctness" in metrics:
            results["grammatical_correctness"] = self.metrics_calculator.calculate_grammatical_correctness(
                predicted_answer
            )
        
        # Calculate overall score as average of all metrics
        if results:
            results["overall_score"] = sum(results.values()) / len(results)
        else:
            results["overall_score"] = 0.0
        
        return results
    
    def evaluate_dataset(
        self, 
        dataset: VietnameseBenchmarkDataset, 
        predicted_answers: Dict[str, str],
        metrics: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Evaluate a dataset.
        
        Args:
            dataset: Dataset to evaluate
            predicted_answers: Dictionary of predicted answers keyed by example ID
            metrics: Metrics to evaluate
            
        Returns:
            Dictionary with evaluation results
        """
        if not metrics:
            metrics = dataset.metrics or self.metrics
        
        results = {
            "dataset_name": dataset.name,
            "dataset_version": dataset.version,
            "metrics": metrics,
            "examples": {},
            "aggregates": {},
            "overall_score": 0.0
        }
        
        # Evaluate each example
        for example in dataset.examples:
            if example.id in predicted_answers:
                predicted_answer = predicted_answers[example.id]
                reference_answer = example.reference_answer or ""
                reference_answers = example.reference_answers or []
                
                example_results = self.evaluate_answer(
                    predicted_answer=predicted_answer,
                    reference_answer=reference_answer,
                    reference_answers=reference_answers,
                    metrics=metrics
                )
                
                results["examples"][example.id] = {
                    "question": example.question,
                    "predicted_answer": predicted_answer,
                    "reference_answer": reference_answer,
                    "metrics": example_results
                }
        
        # Calculate aggregate results
        if results["examples"]:
            # Initialize aggregates
            for metric in metrics + ["overall_score"]:
                results["aggregates"][metric] = {
                    "mean": 0.0,
                    "median": 0.0,
                    "min": 1.0,
                    "max": 0.0,
                    "std": 0.0
                }
            
            # Collect metric values
            metric_values = {metric: [] for metric in metrics + ["overall_score"]}
            
            for example_id, example_result in results["examples"].items():
                for metric, value in example_result["metrics"].items():
                    if metric in metric_values:
                        metric_values[metric].append(value)
            
            # Calculate statistics
            for metric, values in metric_values.items():
                if values:
                    results["aggregates"][metric]["mean"] = float(np.mean(values))
                    results["aggregates"][metric]["median"] = float(np.median(values))
                    results["aggregates"][metric]["min"] = float(np.min(values))
                    results["aggregates"][metric]["max"] = float(np.max(values))
                    results["aggregates"][metric]["std"] = float(np.std(values))
            
            # Calculate overall score
            if "overall_score" in results["aggregates"]:
                results["overall_score"] = results["aggregates"]["overall_score"]["mean"]
        
        return results
    
    def save_evaluation_results(self, results: Dict[str, Any], path: str) -> None:
        """
        Save evaluation results to file.
        
        Args:
            results: Evaluation results
            path: Path to save results
        """
        with open(path, "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
    
    @classmethod
    def load_evaluation_results(cls, path: str) -> Dict[str, Any]:
        """
        Load evaluation results from file.
        
        Args:
            path: Path to results file
            
        Returns:
            Evaluation results
        """
        with open(path, "r", encoding="utf-8") as f:
            return json.load(f)

# Initialize default datasets
if os.environ.get("INITIALIZE_VIETNAMESE_BENCHMARKS", "0") == "1":
    VietnameseBenchmarkRegistry.create_default_datasets()
