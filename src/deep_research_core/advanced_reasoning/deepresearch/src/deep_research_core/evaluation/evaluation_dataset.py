"""
Evaluation dataset for Deep Research Core.
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional

from config.app_config import CACHE_DIR

logger = logging.getLogger(__name__)

# Define evaluation dataset
EVALUATION_DATASET = {
    "factual": [
        {
            "query": "What is quantization in AI model optimization?",
            "query_vi": "Quantization trong tối ưu hóa mô hình AI là gì?",
            "expected_sources": ["Kỹ thuật Quantization"],
            "expected_keywords": ["INT8", "INT4", "32-bit", "FP32", "precision", "độ chính xác"]
        },
        {
            "query": "What is LoRA and how does it work?",
            "query_vi": "LoRA là gì và nó hoạt động như thế nào?",
            "expected_sources": ["Kỹ thuật LoRA"],
            "expected_keywords": ["Low-Rank Adaptation", "fine-tuning", "matrices", "ma trận", "memory", "bộ nhớ"]
        },
        {
            "query": "What is PEFT in AI?",
            "query_vi": "PEFT trong AI là gì?",
            "expected_sources": ["Tổng quan về PEFT"],
            "expected_keywords": ["Parameter-Efficient Fine-Tuning", "LoRA", "Prefix Tuning", "Prompt Tuning"]
        }
    ],
    "analytical": [
        {
            "query": "Compare quantization and LoRA as model optimization techniques",
            "query_vi": "So sánh quantization và LoRA như các kỹ thuật tối ưu hóa mô hình",
            "expected_sources": ["Kỹ thuật Quantization", "Kỹ thuật LoRA"],
            "expected_keywords": ["precision", "fine-tuning", "memory", "performance", "trade-offs", "hiệu suất"]
        },
        {
            "query": "Analyze the benefits of using RAG with Chain of Thought reasoning",
            "query_vi": "Phân tích lợi ích của việc sử dụng RAG với phương pháp suy luận Chain of Thought",
            "expected_sources": ["Chain of Thought", "Retrieval-Augmented Generation"],
            "expected_keywords": ["accuracy", "reasoning", "retrieval", "step-by-step", "knowledge", "kiến thức"]
        }
    ],
    "procedural": [
        {
            "query": "How to implement quantization for a large language model?",
            "query_vi": "Làm thế nào để triển khai quantization cho một mô hình ngôn ngữ lớn?",
            "expected_sources": ["Kỹ thuật Quantization"],
            "expected_keywords": ["INT8", "INT4", "conversion", "chuyển đổi", "steps", "các bước"]
        },
        {
            "query": "What are the steps to fine-tune a model using LoRA?",
            "query_vi": "Các bước để fine-tune một mô hình sử dụng LoRA là gì?",
            "expected_sources": ["Kỹ thuật LoRA"],
            "expected_keywords": ["rank", "matrices", "ma trận", "training", "huấn luyện", "adapter", "bộ điều hợp"]
        }
    ],
    "comparative": [
        {
            "query": "Compare Chain of Thought and RAG approaches",
            "query_vi": "So sánh phương pháp Chain of Thought và RAG",
            "expected_sources": ["Chain of Thought", "Retrieval-Augmented Generation"],
            "expected_keywords": ["reasoning", "retrieval", "suy luận", "truy xuất", "steps", "information", "thông tin"]
        },
        {
            "query": "What are the differences between LoRA and PEFT?",
            "query_vi": "Sự khác biệt giữa LoRA và PEFT là gì?",
            "expected_sources": ["Kỹ thuật LoRA", "Tổng quan về PEFT"],
            "expected_keywords": ["subset", "tập con", "parameters", "tham số", "techniques", "kỹ thuật"]
        }
    ]
}

def load_evaluation_dataset() -> Dict[str, List[Dict[str, Any]]]:
    """
    Load the evaluation dataset.
    
    Returns:
        Dictionary containing the evaluation dataset
    """
    # Check if a custom dataset exists
    custom_dataset_path = os.path.join(str(CACHE_DIR), "evaluation_dataset.json")
    if os.path.exists(custom_dataset_path):
        try:
            with open(custom_dataset_path, "r", encoding="utf-8") as f:
                custom_dataset = json.load(f)
            logger.info(f"Loaded custom evaluation dataset from {custom_dataset_path}")
            return custom_dataset
        except Exception as e:
            logger.error(f"Error loading custom evaluation dataset: {str(e)}")
    
    # Return the default dataset
    return EVALUATION_DATASET

def save_evaluation_dataset(dataset: Dict[str, List[Dict[str, Any]]]) -> bool:
    """
    Save the evaluation dataset.
    
    Args:
        dataset: Dictionary containing the evaluation dataset
        
    Returns:
        True if successful, False otherwise
    """
    custom_dataset_path = os.path.join(str(CACHE_DIR), "evaluation_dataset.json")
    try:
        os.makedirs(os.path.dirname(custom_dataset_path), exist_ok=True)
        with open(custom_dataset_path, "w", encoding="utf-8") as f:
            json.dump(dataset, f, ensure_ascii=False, indent=2)
        logger.info(f"Saved custom evaluation dataset to {custom_dataset_path}")
        return True
    except Exception as e:
        logger.error(f"Error saving custom evaluation dataset: {str(e)}")
        return False

def get_evaluation_queries(question_type: Optional[str] = None, language: str = "en") -> List[Dict[str, Any]]:
    """
    Get evaluation queries for a specific question type and language.
    
    Args:
        question_type: Type of question to get queries for (if None, get all)
        language: Language of the queries ("en" or "vi")
        
    Returns:
        List of evaluation queries
    """
    dataset = load_evaluation_dataset()
    
    queries = []
    
    # If question_type is provided, only get queries for that type
    if question_type and question_type in dataset:
        for item in dataset[question_type]:
            query = item["query_vi"] if language == "vi" else item["query"]
            queries.append({
                "query": query,
                "question_type": question_type,
                "expected_sources": item["expected_sources"],
                "expected_keywords": item["expected_keywords"]
            })
    else:
        # Get all queries
        for qtype, items in dataset.items():
            for item in items:
                query = item["query_vi"] if language == "vi" else item["query"]
                queries.append({
                    "query": query,
                    "question_type": qtype,
                    "expected_sources": item["expected_sources"],
                    "expected_keywords": item["expected_keywords"]
                })
    
    return queries
