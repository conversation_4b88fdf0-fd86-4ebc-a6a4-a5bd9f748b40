"""
Domain-specific evaluation metrics for Deep Research Core.

This module provides evaluation metrics specific to different domains,
such as medical, legal, technical, and educational.
"""

import re
import nltk
from typing import Dict, Any, List, Optional, Tuple, Union, Callable

from ..utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

# Try to download NLTK resources if not already available
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt', quiet=True)

class DomainMetrics:
    """
    Domain-specific evaluation metrics.
    
    This class provides methods to evaluate responses based on domain-specific criteria.
    """
    
    def __init__(self, domain: str = "general"):
        """
        Initialize domain metrics.
        
        Args:
            domain: Domain to evaluate ("general", "medical", "legal", "technical", "educational")
        """
        self.domain = domain
        
        # Domain-specific keywords
        self.domain_keywords = {
            "medical": [
                "diagnosis", "treatment", "symptoms", "patient", "clinical",
                "disease", "disorder", "syndrome", "therapy", "medication",
                "prognosis", "etiology", "pathology", "anatomy", "physiology"
            ],
            "legal": [
                "law", "regulation", "statute", "legal", "court", "judge",
                "plaintiff", "defendant", "contract", "liability", "tort",
                "jurisdiction", "precedent", "legislation", "compliance"
            ],
            "technical": [
                "algorithm", "system", "implementation", "architecture", "framework",
                "protocol", "interface", "component", "module", "function",
                "parameter", "configuration", "optimization", "performance", "efficiency"
            ],
            "educational": [
                "learning", "teaching", "student", "curriculum", "assessment",
                "education", "instruction", "pedagogy", "knowledge", "skill",
                "competency", "objective", "outcome", "evaluation", "development"
            ],
            "general": []
        }
        
        # Domain-specific citation patterns
        self.citation_patterns = {
            "medical": [
                r"\((?:[A-Za-z]+\s+et\s+al\.,\s+\d{4})\)",
                r"\[(\d+)\]",
                r"according to (?:[A-Za-z]+\s+et\s+al\.,\s+\d{4})"
            ],
            "legal": [
                r"(?:[A-Za-z]+\s+v\.\s+[A-Za-z]+)",
                r"(?:\d+\s+U\.S\.\s+\d+)",
                r"(?:\d+\s+F\.\d+\s+\d+)",
                r"(?:[A-Za-z]+\s+Code\s+§\s+\d+)"
            ],
            "technical": [
                r"\((?:[A-Za-z]+\s+et\s+al\.,\s+\d{4})\)",
                r"\[(\d+)\]",
                r"(?:RFC\s+\d+)"
            ],
            "educational": [
                r"\((?:[A-Za-z]+\s+et\s+al\.,\s+\d{4})\)",
                r"\[(\d+)\]",
                r"according to (?:[A-Za-z]+\s+et\s+al\.,\s+\d{4})"
            ],
            "general": [
                r"\((?:[A-Za-z]+\s+et\s+al\.,\s+\d{4})\)",
                r"\[(\d+)\]"
            ]
        }
    
    def calculate_domain_relevance(self, response: Dict[str, Any]) -> float:
        """
        Calculate domain relevance score.
        
        Args:
            response: Response from the model
            
        Returns:
            Domain relevance score (0.0 to 1.0)
        """
        if self.domain == "general":
            return 1.0
        
        # Get the reasoning or answer from the response
        reasoning = response.get("reasoning", "")
        answer = response.get("answer", "")
        
        # Combine reasoning and answer
        text = reasoning + " " + answer
        text = text.lower()
        
        # Count domain-specific keywords
        domain_keywords = self.domain_keywords.get(self.domain, [])
        if not domain_keywords:
            return 1.0
        
        keyword_count = 0
        for keyword in domain_keywords:
            keyword_count += text.count(keyword.lower())
        
        # Normalize by text length
        word_count = len(text.split())
        if word_count == 0:
            return 0.0
        
        # Calculate normalized score (capped at 1.0)
        score = min(1.0, keyword_count / (word_count * 0.05))  # Expect about 5% domain keywords
        
        return score
    
    def calculate_citation_quality(self, response: Dict[str, Any]) -> float:
        """
        Calculate citation quality score.
        
        Args:
            response: Response from the model
            
        Returns:
            Citation quality score (0.0 to 1.0)
        """
        # Get the reasoning or answer from the response
        reasoning = response.get("reasoning", "")
        answer = response.get("answer", "")
        
        # Combine reasoning and answer
        text = reasoning + " " + answer
        
        # Get domain-specific citation patterns
        patterns = self.citation_patterns.get(self.domain, self.citation_patterns["general"])
        
        # Count citations
        citation_count = 0
        for pattern in patterns:
            citation_count += len(re.findall(pattern, text, re.IGNORECASE))
        
        # Get expected citation count based on text length
        word_count = len(text.split())
        expected_citations = max(1, word_count // 200)  # Expect 1 citation per 200 words
        
        # Calculate score (capped at 1.0)
        score = min(1.0, citation_count / expected_citations)
        
        return score
    
    def calculate_technical_accuracy(self, response: Dict[str, Any], expected_terms: List[str]) -> float:
        """
        Calculate technical accuracy score.
        
        Args:
            response: Response from the model
            expected_terms: List of expected technical terms
            
        Returns:
            Technical accuracy score (0.0 to 1.0)
        """
        if not expected_terms:
            return 1.0
        
        # Get the reasoning or answer from the response
        reasoning = response.get("reasoning", "")
        answer = response.get("answer", "")
        
        # Combine reasoning and answer
        text = reasoning + " " + answer
        text = text.lower()
        
        # Count expected terms
        term_count = 0
        for term in expected_terms:
            if term.lower() in text:
                term_count += 1
        
        # Calculate score
        score = term_count / len(expected_terms)
        
        return score
    
    def calculate_explanation_depth(self, response: Dict[str, Any]) -> float:
        """
        Calculate explanation depth score.
        
        Args:
            response: Response from the model
            
        Returns:
            Explanation depth score (0.0 to 1.0)
        """
        # Get the reasoning or answer from the response
        reasoning = response.get("reasoning", "")
        answer = response.get("answer", "")
        
        # Combine reasoning and answer
        text = reasoning + " " + answer
        
        # Tokenize into sentences
        sentences = nltk.sent_tokenize(text)
        
        # Count sentences
        sentence_count = len(sentences)
        
        # Calculate average sentence length
        if sentence_count == 0:
            return 0.0
        
        total_words = sum(len(sentence.split()) for sentence in sentences)
        avg_sentence_length = total_words / sentence_count
        
        # Calculate depth score based on sentence count and average length
        depth_score = min(1.0, (sentence_count / 10) * (avg_sentence_length / 15))
        
        return depth_score
    
    def calculate_readability(self, response: Dict[str, Any]) -> float:
        """
        Calculate readability score using Flesch Reading Ease.
        
        Args:
            response: Response from the model
            
        Returns:
            Readability score (0.0 to 1.0)
        """
        # Get the reasoning or answer from the response
        reasoning = response.get("reasoning", "")
        answer = response.get("answer", "")
        
        # Combine reasoning and answer
        text = reasoning + " " + answer
        
        # Tokenize into sentences and words
        sentences = nltk.sent_tokenize(text)
        words = text.split()
        
        # Count sentences, words, and syllables
        sentence_count = len(sentences)
        word_count = len(words)
        
        if sentence_count == 0 or word_count == 0:
            return 0.0
        
        # Estimate syllable count (simplified)
        syllable_count = 0
        for word in words:
            word = word.lower()
            if word.endswith(('es', 'ed')):
                word = word[:-2]
            elif word.endswith('e'):
                word = word[:-1]
            
            vowels = 'aeiouy'
            count = 0
            prev_is_vowel = False
            
            for char in word:
                is_vowel = char in vowels
                if is_vowel and not prev_is_vowel:
                    count += 1
                prev_is_vowel = is_vowel
            
            syllable_count += max(1, count)
        
        # Calculate Flesch Reading Ease
        # FRE = 206.835 - 1.015 * (words / sentences) - 84.6 * (syllables / words)
        words_per_sentence = word_count / sentence_count
        syllables_per_word = syllable_count / word_count
        
        flesch_reading_ease = 206.835 - (1.015 * words_per_sentence) - (84.6 * syllables_per_word)
        
        # Normalize to 0.0-1.0 range (FRE ranges from 0 to 100)
        # Higher FRE means easier to read
        normalized_score = max(0.0, min(1.0, flesch_reading_ease / 100.0))
        
        return normalized_score
    
    def calculate_domain_metrics(
        self,
        response: Dict[str, Any],
        expected_terms: Optional[List[str]] = None
    ) -> Dict[str, float]:
        """
        Calculate all domain-specific metrics.
        
        Args:
            response: Response from the model
            expected_terms: List of expected technical terms
            
        Returns:
            Dictionary containing all domain-specific metrics
        """
        metrics = {
            "domain_relevance": self.calculate_domain_relevance(response),
            "citation_quality": self.calculate_citation_quality(response),
            "explanation_depth": self.calculate_explanation_depth(response),
            "readability": self.calculate_readability(response)
        }
        
        if expected_terms:
            metrics["technical_accuracy"] = self.calculate_technical_accuracy(response, expected_terms)
        
        # Calculate overall domain score (weighted average)
        weights = {
            "domain_relevance": 0.3,
            "citation_quality": 0.2,
            "explanation_depth": 0.2,
            "readability": 0.1,
            "technical_accuracy": 0.2
        }
        
        total_weight = 0.0
        weighted_sum = 0.0
        
        for metric, score in metrics.items():
            weight = weights.get(metric, 0.0)
            weighted_sum += score * weight
            total_weight += weight
        
        if total_weight > 0:
            metrics["overall_domain_score"] = weighted_sum / total_weight
        else:
            metrics["overall_domain_score"] = 0.0
        
        return metrics


class MedicalMetrics(DomainMetrics):
    """Medical domain-specific metrics."""
    
    def __init__(self):
        """Initialize medical metrics."""
        super().__init__(domain="medical")
        
        # Additional medical-specific patterns
        self.evidence_levels = [
            r"Level (?:I|II|III|IV|V) evidence",
            r"(?:strong|moderate|weak) evidence",
            r"(?:randomized controlled trial|RCT)",
            r"(?:meta-analysis|systematic review)",
            r"(?:cohort study|case-control study)"
        ]
    
    def calculate_evidence_quality(self, response: Dict[str, Any]) -> float:
        """
        Calculate evidence quality score for medical responses.
        
        Args:
            response: Response from the model
            
        Returns:
            Evidence quality score (0.0 to 1.0)
        """
        # Get the reasoning or answer from the response
        reasoning = response.get("reasoning", "")
        answer = response.get("answer", "")
        
        # Combine reasoning and answer
        text = reasoning + " " + answer
        
        # Count evidence level mentions
        evidence_count = 0
        for pattern in self.evidence_levels:
            evidence_count += len(re.findall(pattern, text, re.IGNORECASE))
        
        # Calculate score (capped at 1.0)
        score = min(1.0, evidence_count / 2)  # Expect at least 2 evidence mentions
        
        return score
    
    def calculate_domain_metrics(
        self,
        response: Dict[str, Any],
        expected_terms: Optional[List[str]] = None
    ) -> Dict[str, float]:
        """
        Calculate all medical domain-specific metrics.
        
        Args:
            response: Response from the model
            expected_terms: List of expected technical terms
            
        Returns:
            Dictionary containing all medical domain-specific metrics
        """
        # Get base metrics
        metrics = super().calculate_domain_metrics(response, expected_terms)
        
        # Add medical-specific metrics
        metrics["evidence_quality"] = self.calculate_evidence_quality(response)
        
        # Recalculate overall score
        weights = {
            "domain_relevance": 0.25,
            "citation_quality": 0.15,
            "explanation_depth": 0.15,
            "readability": 0.1,
            "technical_accuracy": 0.15,
            "evidence_quality": 0.2
        }
        
        total_weight = 0.0
        weighted_sum = 0.0
        
        for metric, score in metrics.items():
            if metric != "overall_domain_score":  # Skip the overall score
                weight = weights.get(metric, 0.0)
                weighted_sum += score * weight
                total_weight += weight
        
        if total_weight > 0:
            metrics["overall_domain_score"] = weighted_sum / total_weight
        
        return metrics


class LegalMetrics(DomainMetrics):
    """Legal domain-specific metrics."""
    
    def __init__(self):
        """Initialize legal metrics."""
        super().__init__(domain="legal")
        
        # Additional legal-specific patterns
        self.legal_authority_patterns = [
            r"(?:Supreme Court|Court of Appeals|District Court)",
            r"(?:statute|regulation|code|law)",
            r"(?:precedent|ruling|decision|opinion)",
            r"(?:pursuant to|in accordance with|as required by)"
        ]
    
    def calculate_legal_authority(self, response: Dict[str, Any]) -> float:
        """
        Calculate legal authority score.
        
        Args:
            response: Response from the model
            
        Returns:
            Legal authority score (0.0 to 1.0)
        """
        # Get the reasoning or answer from the response
        reasoning = response.get("reasoning", "")
        answer = response.get("answer", "")
        
        # Combine reasoning and answer
        text = reasoning + " " + answer
        
        # Count legal authority mentions
        authority_count = 0
        for pattern in self.legal_authority_patterns:
            authority_count += len(re.findall(pattern, text, re.IGNORECASE))
        
        # Calculate score (capped at 1.0)
        score = min(1.0, authority_count / 3)  # Expect at least 3 authority mentions
        
        return score
    
    def calculate_domain_metrics(
        self,
        response: Dict[str, Any],
        expected_terms: Optional[List[str]] = None
    ) -> Dict[str, float]:
        """
        Calculate all legal domain-specific metrics.
        
        Args:
            response: Response from the model
            expected_terms: List of expected technical terms
            
        Returns:
            Dictionary containing all legal domain-specific metrics
        """
        # Get base metrics
        metrics = super().calculate_domain_metrics(response, expected_terms)
        
        # Add legal-specific metrics
        metrics["legal_authority"] = self.calculate_legal_authority(response)
        
        # Recalculate overall score
        weights = {
            "domain_relevance": 0.25,
            "citation_quality": 0.2,
            "explanation_depth": 0.15,
            "readability": 0.05,
            "technical_accuracy": 0.15,
            "legal_authority": 0.2
        }
        
        total_weight = 0.0
        weighted_sum = 0.0
        
        for metric, score in metrics.items():
            if metric != "overall_domain_score":  # Skip the overall score
                weight = weights.get(metric, 0.0)
                weighted_sum += score * weight
                total_weight += weight
        
        if total_weight > 0:
            metrics["overall_domain_score"] = weighted_sum / total_weight
        
        return metrics


class TechnicalMetrics(DomainMetrics):
    """Technical domain-specific metrics."""
    
    def __init__(self):
        """Initialize technical metrics."""
        super().__init__(domain="technical")
        
        # Additional technical-specific patterns
        self.code_patterns = [
            r"```(?:python|java|javascript|c\+\+|go|rust|php|ruby|swift|kotlin|typescript|html|css|sql|bash|shell)[\s\S]*?```",
            r"`[^`\n]+`"
        ]
    
    def calculate_code_quality(self, response: Dict[str, Any]) -> float:
        """
        Calculate code quality score.
        
        Args:
            response: Response from the model
            
        Returns:
            Code quality score (0.0 to 1.0)
        """
        # Get the reasoning or answer from the response
        reasoning = response.get("reasoning", "")
        answer = response.get("answer", "")
        
        # Combine reasoning and answer
        text = reasoning + " " + answer
        
        # Extract code blocks
        code_blocks = []
        for pattern in self.code_patterns:
            code_blocks.extend(re.findall(pattern, text))
        
        if not code_blocks:
            return 0.0
        
        # Calculate code quality based on length and number of blocks
        total_code_length = sum(len(block) for block in code_blocks)
        code_block_count = len(code_blocks)
        
        # Calculate score based on code presence and complexity
        score = min(1.0, (code_block_count / 3) * (total_code_length / 500))
        
        return score
    
    def calculate_domain_metrics(
        self,
        response: Dict[str, Any],
        expected_terms: Optional[List[str]] = None
    ) -> Dict[str, float]:
        """
        Calculate all technical domain-specific metrics.
        
        Args:
            response: Response from the model
            expected_terms: List of expected technical terms
            
        Returns:
            Dictionary containing all technical domain-specific metrics
        """
        # Get base metrics
        metrics = super().calculate_domain_metrics(response, expected_terms)
        
        # Add technical-specific metrics
        metrics["code_quality"] = self.calculate_code_quality(response)
        
        # Recalculate overall score
        weights = {
            "domain_relevance": 0.2,
            "citation_quality": 0.1,
            "explanation_depth": 0.2,
            "readability": 0.1,
            "technical_accuracy": 0.2,
            "code_quality": 0.2
        }
        
        total_weight = 0.0
        weighted_sum = 0.0
        
        for metric, score in metrics.items():
            if metric != "overall_domain_score":  # Skip the overall score
                weight = weights.get(metric, 0.0)
                weighted_sum += score * weight
                total_weight += weight
        
        if total_weight > 0:
            metrics["overall_domain_score"] = weighted_sum / total_weight
        
        return metrics


class EducationalMetrics(DomainMetrics):
    """Educational domain-specific metrics."""
    
    def __init__(self):
        """Initialize educational metrics."""
        super().__init__(domain="educational")
        
        # Additional educational-specific patterns
        self.learning_objective_patterns = [
            r"(?:learning objective|learning goal|learning outcome)",
            r"(?:students will|learners will)",
            r"(?:by the end of this|after completing this)",
            r"(?:understand|comprehend|analyze|evaluate|create|apply)"
        ]
    
    def calculate_pedagogical_quality(self, response: Dict[str, Any]) -> float:
        """
        Calculate pedagogical quality score.
        
        Args:
            response: Response from the model
            
        Returns:
            Pedagogical quality score (0.0 to 1.0)
        """
        # Get the reasoning or answer from the response
        reasoning = response.get("reasoning", "")
        answer = response.get("answer", "")
        
        # Combine reasoning and answer
        text = reasoning + " " + answer
        
        # Count learning objective mentions
        objective_count = 0
        for pattern in self.learning_objective_patterns:
            objective_count += len(re.findall(pattern, text, re.IGNORECASE))
        
        # Check for examples
        has_examples = "for example" in text.lower() or "example:" in text.lower()
        
        # Check for step-by-step explanations
        has_steps = re.search(r"(?:step\s+\d+|first|second|third|next|finally)", text, re.IGNORECASE) is not None
        
        # Calculate score
        score = min(1.0, (objective_count / 2) + (0.3 if has_examples else 0) + (0.3 if has_steps else 0))
        
        return score
    
    def calculate_domain_metrics(
        self,
        response: Dict[str, Any],
        expected_terms: Optional[List[str]] = None
    ) -> Dict[str, float]:
        """
        Calculate all educational domain-specific metrics.
        
        Args:
            response: Response from the model
            expected_terms: List of expected technical terms
            
        Returns:
            Dictionary containing all educational domain-specific metrics
        """
        # Get base metrics
        metrics = super().calculate_domain_metrics(response, expected_terms)
        
        # Add educational-specific metrics
        metrics["pedagogical_quality"] = self.calculate_pedagogical_quality(response)
        
        # Recalculate overall score
        weights = {
            "domain_relevance": 0.2,
            "citation_quality": 0.1,
            "explanation_depth": 0.2,
            "readability": 0.2,
            "technical_accuracy": 0.1,
            "pedagogical_quality": 0.2
        }
        
        total_weight = 0.0
        weighted_sum = 0.0
        
        for metric, score in metrics.items():
            if metric != "overall_domain_score":  # Skip the overall score
                weight = weights.get(metric, 0.0)
                weighted_sum += score * weight
                total_weight += weight
        
        if total_weight > 0:
            metrics["overall_domain_score"] = weighted_sum / total_weight
        
        return metrics


def get_domain_metrics(domain: str) -> DomainMetrics:
    """
    Get domain-specific metrics for a given domain.
    
    Args:
        domain: Domain to get metrics for ("general", "medical", "legal", "technical", "educational")
        
    Returns:
        Domain-specific metrics instance
    """
    if domain == "medical":
        return MedicalMetrics()
    elif domain == "legal":
        return LegalMetrics()
    elif domain == "technical":
        return TechnicalMetrics()
    elif domain == "educational":
        return EducationalMetrics()
    else:
        return DomainMetrics(domain="general")
