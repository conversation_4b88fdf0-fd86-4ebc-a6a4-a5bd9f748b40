"""
QA Evaluator for RAG systems.

This module provides functionality for evaluating the quality of answers
generated by RAG systems, including relevance, factual accuracy, and completeness.
"""

import time
import json
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Callable, Union

from deep_research_core.utils.structured_logging import get_logger
from deep_research_core.models.api.openai import openai_provider
from deep_research_core.models.api.anthropic import anthropic_provider
from deep_research_core.models.api.openrouter import openrouter_provider
from deep_research_core.models.api.cohere import cohere_provider
from deep_research_core.models.api.mistral import mistral_provider

# Get logger
logger = get_logger(__name__)

class QAEvaluator:
    """
    Evaluator for question answering systems.
    
    This class provides methods for evaluating the quality of answers generated
    by RAG systems, including relevance, factual accuracy, and completeness.
    """
    
    def __init__(
        self,
        provider: str = "openai",
        model: str = "gpt-4o",
        use_model_evaluation: bool = True,
        use_heuristics: bool = True,
        metrics: Optional[List[str]] = None,
        language: str = "en",
        verbose: bool = False
    ):
        """
        Initialize the QA evaluator.
        
        Args:
            provider: Provider to use for model-based evaluation
            model: Model to use for evaluation
            use_model_evaluation: Whether to use model-based evaluation
            use_heuristics: Whether to use heuristic-based evaluation
            metrics: List of metrics to evaluate (defaults to all)
            language: Language of the content to evaluate
            verbose: Whether to print verbose logs
        """
        self.provider = provider
        self.model = model
        self.use_model_evaluation = use_model_evaluation
        self.use_heuristics = use_heuristics
        self.metrics = metrics or [
            "relevance", "factual_accuracy", "completeness", 
            "coherence", "conciseness", "source_attribution"
        ]
        self.language = language
        self.verbose = verbose
        
        # Set up provider
        self._setup_provider()
        
        # Set up evaluation prompts
        self._setup_prompts()
        
    def _setup_provider(self) -> None:
        """Set up the provider for model-based evaluation."""
        if self.provider == "openai":
            self.api_provider = openai_provider
        elif self.provider == "anthropic":
            self.api_provider = anthropic_provider
        elif self.provider == "openrouter":
            self.api_provider = openrouter_provider
        elif self.provider == "cohere":
            self.api_provider = cohere_provider
        elif self.provider == "mistral":
            self.api_provider = mistral_provider
        else:
            raise ValueError(f"Unsupported provider: {self.provider}")
            
    def _setup_prompts(self) -> None:
        """Set up evaluation prompts."""
        # Base evaluation prompt
        self.evaluation_prompt = """
        You are an expert evaluator for question answering systems. Your task is to evaluate the quality of an answer to a given question.
        
        Question: {question}
        
        Retrieved Documents:
        {documents}
        
        Answer: {answer}
        
        Please evaluate the answer on the following criteria:
        
        1. Relevance (0-10): How relevant is the answer to the question?
        2. Factual Accuracy (0-10): How factually accurate is the answer based on the retrieved documents?
        3. Completeness (0-10): How complete is the answer? Does it address all aspects of the question?
        4. Coherence (0-10): How coherent and well-structured is the answer?
        5. Conciseness (0-10): How concise is the answer without sacrificing completeness?
        6. Source Attribution (0-10): How well does the answer attribute information to the retrieved documents?
        
        For each criterion, provide a score from 0 to 10 and a brief explanation.
        
        Then, provide an overall score from 0 to 10 and a summary of the evaluation.
        
        Format your response as a JSON object with the following structure:
        {{
            "relevance": {{
                "score": <score>,
                "explanation": "<explanation>"
            }},
            "factual_accuracy": {{
                "score": <score>,
                "explanation": "<explanation>"
            }},
            "completeness": {{
                "score": <score>,
                "explanation": "<explanation>"
            }},
            "coherence": {{
                "score": <score>,
                "explanation": "<explanation>"
            }},
            "conciseness": {{
                "score": <score>,
                "explanation": "<explanation>"
            }},
            "source_attribution": {{
                "score": <score>,
                "explanation": "<explanation>"
            }},
            "overall": {{
                "score": <score>,
                "explanation": "<explanation>"
            }}
        }}
        """
        
        # Vietnamese evaluation prompt
        self.vietnamese_evaluation_prompt = """
        Bạn là một chuyên gia đánh giá hệ thống trả lời câu hỏi. Nhiệm vụ của bạn là đánh giá chất lượng câu trả lời cho một câu hỏi đã cho.
        
        Câu hỏi: {question}
        
        Tài liệu được truy xuất:
        {documents}
        
        Câu trả lời: {answer}
        
        Vui lòng đánh giá câu trả lời dựa trên các tiêu chí sau:
        
        1. Độ liên quan (0-10): Câu trả lời có liên quan đến câu hỏi như thế nào?
        2. Độ chính xác về mặt thực tế (0-10): Câu trả lời có chính xác về mặt thực tế dựa trên các tài liệu được truy xuất không?
        3. Tính đầy đủ (0-10): Câu trả lời có đầy đủ không? Nó có đề cập đến tất cả các khía cạnh của câu hỏi không?
        4. Tính mạch lạc (0-10): Câu trả lời có mạch lạc và cấu trúc tốt không?
        5. Tính súc tích (0-10): Câu trả lời có súc tích mà không hy sinh tính đầy đủ không?
        6. Trích dẫn nguồn (0-10): Câu trả lời có trích dẫn thông tin từ các tài liệu được truy xuất tốt không?
        
        Đối với mỗi tiêu chí, hãy đưa ra điểm số từ 0 đến 10 và một lời giải thích ngắn gọn.
        
        Sau đó, đưa ra điểm tổng thể từ 0 đến 10 và tóm tắt đánh giá.
        
        Định dạng phản hồi của bạn dưới dạng đối tượng JSON với cấu trúc sau:
        {{
            "relevance": {{
                "score": <điểm>,
                "explanation": "<giải thích>"
            }},
            "factual_accuracy": {{
                "score": <điểm>,
                "explanation": "<giải thích>"
            }},
            "completeness": {{
                "score": <điểm>,
                "explanation": "<giải thích>"
            }},
            "coherence": {{
                "score": <điểm>,
                "explanation": "<giải thích>"
            }},
            "conciseness": {{
                "score": <điểm>,
                "explanation": "<giải thích>"
            }},
            "source_attribution": {{
                "score": <điểm>,
                "explanation": "<giải thích>"
            }},
            "overall": {{
                "score": <điểm>,
                "explanation": "<giải thích>"
            }}
        }}
        """
        
    def evaluate(
        self,
        question: str,
        answer: str,
        documents: List[Dict[str, Any]],
        expected_answer: Optional[str] = None,
        expected_sources: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Evaluate the quality of an answer.
        
        Args:
            question: The question being answered
            answer: The answer to evaluate
            documents: The retrieved documents used to generate the answer
            expected_answer: Optional expected answer for comparison
            expected_sources: Optional list of expected source IDs
            
        Returns:
            Dictionary containing evaluation results
        """
        start_time = time.time()
        
        # Initialize results
        results = {
            "question": question,
            "answer": answer,
            "documents_count": len(documents),
            "metrics": {},
            "overall_score": 0.0,
            "evaluation_time": 0.0
        }
        
        # Add expected answer and sources if provided
        if expected_answer:
            results["expected_answer"] = expected_answer
        
        if expected_sources:
            results["expected_sources"] = expected_sources
        
        # Perform model-based evaluation if enabled
        if self.use_model_evaluation:
            model_results = self._model_evaluation(question, answer, documents)
            
            if model_results:
                # Add model evaluation results
                for metric, data in model_results.items():
                    if metric != "overall":
                        results["metrics"][metric] = {
                            "score": data["score"],
                            "explanation": data["explanation"]
                        }
                
                # Add overall score
                if "overall" in model_results:
                    results["overall_score"] = model_results["overall"]["score"]
                    results["overall_explanation"] = model_results["overall"]["explanation"]
        
        # Perform heuristic-based evaluation if enabled
        if self.use_heuristics:
            heuristic_results = self._heuristic_evaluation(
                question, answer, documents, expected_answer, expected_sources
            )
            
            # Add heuristic evaluation results
            for metric, data in heuristic_results.items():
                # If metric already exists from model evaluation, average the scores
                if metric in results["metrics"]:
                    results["metrics"][metric]["score"] = (
                        results["metrics"][metric]["score"] + data["score"]
                    ) / 2
                    results["metrics"][metric]["explanation"] += f"\n\nHeuristic: {data['explanation']}"
                else:
                    results["metrics"][metric] = data
        
        # Calculate overall score if not already set
        if results["overall_score"] == 0.0 and results["metrics"]:
            results["overall_score"] = sum(
                metric["score"] for metric in results["metrics"].values()
            ) / len(results["metrics"])
        
        # Add evaluation time
        results["evaluation_time"] = time.time() - start_time
        
        return results
    
    def _model_evaluation(
        self,
        question: str,
        answer: str,
        documents: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Perform model-based evaluation.
        
        Args:
            question: The question being answered
            answer: The answer to evaluate
            documents: The retrieved documents used to generate the answer
            
        Returns:
            Dictionary containing evaluation results
        """
        try:
            # Format documents for the prompt
            formatted_docs = "\n\n".join([
                f"Document {i+1}:\nTitle: {doc.get('title', 'Untitled')}\nContent: {doc.get('content', doc.get('text', ''))}"
                for i, doc in enumerate(documents)
            ])
            
            # Select prompt based on language
            prompt = self.vietnamese_evaluation_prompt if self.language == "vi" else self.evaluation_prompt
            
            # Format the prompt
            formatted_prompt = prompt.format(
                question=question,
                answer=answer,
                documents=formatted_docs
            )
            
            # Generate evaluation using the model
            response = self.api_provider.generate(
                prompt=formatted_prompt,
                model=self.model,
                temperature=0.2,
                max_tokens=1500
            )
            
            # Parse the response as JSON
            try:
                # Extract JSON from the response
                json_str = response
                
                # If the response contains markdown code blocks, extract the JSON
                if "```json" in response:
                    json_str = response.split("```json")[1].split("```")[0].strip()
                elif "```" in response:
                    json_str = response.split("```")[1].split("```")[0].strip()
                
                # Parse the JSON
                evaluation = json.loads(json_str)
                
                # Validate the evaluation
                required_keys = ["relevance", "factual_accuracy", "completeness", "coherence", "conciseness", "source_attribution", "overall"]
                for key in required_keys:
                    if key not in evaluation:
                        logger.warning(f"Missing key in evaluation: {key}")
                        evaluation[key] = {"score": 5.0, "explanation": "Not evaluated"}
                
                return evaluation
                
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing evaluation JSON: {str(e)}")
                logger.error(f"Response: {response}")
                return {}
                
        except Exception as e:
            logger.error(f"Error in model evaluation: {str(e)}")
            return {}
    
    def _heuristic_evaluation(
        self,
        question: str,
        answer: str,
        documents: List[Dict[str, Any]],
        expected_answer: Optional[str] = None,
        expected_sources: Optional[List[str]] = None
    ) -> Dict[str, Dict[str, Union[float, str]]]:
        """
        Perform heuristic-based evaluation.
        
        Args:
            question: The question being answered
            answer: The answer to evaluate
            documents: The retrieved documents used to generate the answer
            expected_answer: Optional expected answer for comparison
            expected_sources: Optional list of expected source IDs
            
        Returns:
            Dictionary containing evaluation results
        """
        results = {}
        
        # Evaluate relevance
        if "relevance" in self.metrics:
            relevance_score = self._evaluate_relevance(question, answer, documents)
            results["relevance"] = {
                "score": relevance_score,
                "explanation": "Heuristic evaluation of relevance based on keyword matching and semantic similarity."
            }
        
        # Evaluate source attribution
        if "source_attribution" in self.metrics:
            attribution_score = self._evaluate_source_attribution(answer, documents)
            results["source_attribution"] = {
                "score": attribution_score,
                "explanation": "Heuristic evaluation of source attribution based on citation patterns and content matching."
            }
        
        # Evaluate factual accuracy if expected answer is provided
        if "factual_accuracy" in self.metrics and expected_answer:
            accuracy_score = self._evaluate_factual_accuracy(answer, expected_answer, documents)
            results["factual_accuracy"] = {
                "score": accuracy_score,
                "explanation": "Heuristic evaluation of factual accuracy based on comparison with expected answer."
            }
        
        # Evaluate completeness
        if "completeness" in self.metrics:
            completeness_score = self._evaluate_completeness(question, answer, documents)
            results["completeness"] = {
                "score": completeness_score,
                "explanation": "Heuristic evaluation of completeness based on question coverage and document utilization."
            }
        
        return results
    
    def _evaluate_relevance(
        self,
        question: str,
        answer: str,
        documents: List[Dict[str, Any]]
    ) -> float:
        """
        Evaluate the relevance of the answer to the question.
        
        Args:
            question: The question being answered
            answer: The answer to evaluate
            documents: The retrieved documents used to generate the answer
            
        Returns:
            Relevance score (0-10)
        """
        # Simple keyword matching
        question_words = set(question.lower().split())
        answer_words = set(answer.lower().split())
        
        # Calculate word overlap
        overlap = len(question_words.intersection(answer_words)) / len(question_words) if question_words else 0
        
        # Calculate relevance score (0-10)
        relevance_score = min(10, overlap * 10)
        
        # Adjust score based on document relevance
        doc_relevance = 0
        for doc in documents:
            doc_content = doc.get("content", doc.get("text", "")).lower()
            doc_overlap = len(question_words.intersection(set(doc_content.split()))) / len(question_words) if question_words else 0
            doc_relevance += doc_overlap
        
        # Average document relevance
        if documents:
            doc_relevance /= len(documents)
            
            # Combine answer relevance and document relevance
            relevance_score = (relevance_score + doc_relevance * 10) / 2
        
        return relevance_score
    
    def _evaluate_source_attribution(
        self,
        answer: str,
        documents: List[Dict[str, Any]]
    ) -> float:
        """
        Evaluate the source attribution in the answer.
        
        Args:
            answer: The answer to evaluate
            documents: The retrieved documents used to generate the answer
            
        Returns:
            Source attribution score (0-10)
        """
        # Check for citation patterns
        citation_patterns = [
            "according to", "as stated in", "as mentioned in", 
            "based on", "from", "cited in", "reference", "source"
        ]
        
        # Vietnamese citation patterns
        if self.language == "vi":
            citation_patterns.extend([
                "theo", "như đã nêu trong", "như đã đề cập trong",
                "dựa trên", "từ", "trích dẫn trong", "tài liệu", "nguồn"
            ])
        
        # Count citations
        citation_count = sum(1 for pattern in citation_patterns if pattern.lower() in answer.lower())
        
        # Check for document content in answer
        content_match = 0
        for doc in documents:
            doc_content = doc.get("content", doc.get("text", ""))
            doc_sentences = [s.strip() for s in doc_content.split(".") if s.strip()]
            
            for sentence in doc_sentences:
                if len(sentence) > 20 and sentence.lower() in answer.lower():
                    content_match += 1
        
        # Calculate attribution score (0-10)
        attribution_score = min(10, (citation_count + content_match) * 2)
        
        return attribution_score
    
    def _evaluate_factual_accuracy(
        self,
        answer: str,
        expected_answer: str,
        documents: List[Dict[str, Any]]
    ) -> float:
        """
        Evaluate the factual accuracy of the answer.
        
        Args:
            answer: The answer to evaluate
            expected_answer: The expected answer
            documents: The retrieved documents used to generate the answer
            
        Returns:
            Factual accuracy score (0-10)
        """
        # Simple word overlap with expected answer
        answer_words = set(answer.lower().split())
        expected_words = set(expected_answer.lower().split())
        
        # Calculate word overlap
        overlap = len(answer_words.intersection(expected_words)) / len(expected_words) if expected_words else 0
        
        # Calculate accuracy score (0-10)
        accuracy_score = min(10, overlap * 10)
        
        return accuracy_score
    
    def _evaluate_completeness(
        self,
        question: str,
        answer: str,
        documents: List[Dict[str, Any]]
    ) -> float:
        """
        Evaluate the completeness of the answer.
        
        Args:
            question: The question being answered
            answer: The answer to evaluate
            documents: The retrieved documents used to generate the answer
            
        Returns:
            Completeness score (0-10)
        """
        # Extract key terms from question
        question_terms = set(question.lower().split())
        
        # Check if answer addresses all key terms
        answer_terms = set(answer.lower().split())
        term_coverage = len(question_terms.intersection(answer_terms)) / len(question_terms) if question_terms else 0
        
        # Check answer length relative to documents
        doc_length = sum(len(doc.get("content", doc.get("text", ""))) for doc in documents)
        answer_length = len(answer)
        length_ratio = min(1.0, answer_length / (doc_length * 0.2)) if doc_length > 0 else 0.5
        
        # Calculate completeness score (0-10)
        completeness_score = min(10, (term_coverage * 0.7 + length_ratio * 0.3) * 10)
        
        return completeness_score
