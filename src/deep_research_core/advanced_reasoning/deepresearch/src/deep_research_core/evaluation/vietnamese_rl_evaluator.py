"""
Vietnamese RL Evaluator.

Module này cung cấp các metrics đánh giá chất lượng đặc thù cho mô hình RL-tuning với tiếng <PERSON>,
bao gồm đánh giá chất lượng phản hồi, t<PERSON><PERSON> nh<PERSON><PERSON> quán, và hiệu suất của mô hình.
"""

import re
import string
import math
from typing import Dict, List, Any, Optional, Tuple, Union, Set

import numpy as np

from ..utils.structured_logging import get_logger
from ..utils.vietnamese_utils import (
    normalize_vietnamese_text,
    detect_vietnamese,
    extract_vietnamese_keywords
)
from .vietnamese_metrics import VietnameseEvaluationMetrics
from .vietnamese_quality_metrics import VietnameseQualityMetrics

try:
    import underthesea
    UNDERTHESEA_AVAILABLE = True
except ImportError:
    UNDERTHESEA_AVAILABLE = False

try:
    from pyvi import ViTokenizer, ViPosTagger
    PYVI_AVAILABLE = True
except ImportError:
    PYVI_AVAILABLE = False

# Create a logger
logger = get_logger(__name__)

class VietnameseRLEvaluator:
    """
    Metrics đánh giá chất lượng đặc thù cho mô hình RL-tuning với tiếng Việt.

    Lớp này cung cấp các phương thức đánh giá chất lượng đặc thù cho mô hình RL-tuning với tiếng Việt,
    bao gồm đánh giá chất lượng phản hồi, tính nhất quán, và hiệu suất của mô hình.
    """

    def __init__(
        self,
        embedding_model: Optional[str] = None,
        vncorenlp_annotator_path: Optional[str] = None,
        verbose: bool = False
    ):
        """
        Khởi tạo VietnameseRLEvaluator.

        Args:
            embedding_model: Tên mô hình embedding tiếng Việt (phobert, viebert, xlm-roberta-vi, multilingual-e5)
            vncorenlp_annotator_path: Đường dẫn đến mô hình VnCoreNLP
            verbose: Bật chế độ verbose
        """
        self.verbose = verbose
        self.embedding_model = embedding_model

        # Khởi tạo các metrics cơ bản
        self.basic_metrics = VietnameseEvaluationMetrics()
        self.quality_metrics = VietnameseQualityMetrics(vncorenlp_annotator_path)

        # Các từ khóa đặc trưng cho RL-tuning
        self.rl_keywords = [
            "học tăng cường", "phần thưởng", "chính sách", "quỹ đạo",
            "môi trường", "trạng thái", "hành động", "tác tử", "đại lý",
            "PPO", "GRPO", "SFT", "RLHF", "RLAIF", "DPO", "KTO", "REINFORCE",
            "A2C", "A3C", "TRPO", "SAC", "TD3", "DDPG", "Q-learning"
        ]

        # Các từ chỉ thị phản hồi tốt trong RL
        self.rl_good_response_markers = [
            "phân tích", "đánh giá", "so sánh", "giải thích", "minh họa",
            "ví dụ cụ thể", "bước thực hiện", "quy trình", "phương pháp",
            "kết quả thực nghiệm", "dữ liệu", "nghiên cứu", "tài liệu tham khảo",
            "ưu điểm", "nhược điểm", "hạn chế", "cải tiến", "tối ưu hóa"
        ]

        # Các từ chỉ thị phản hồi kém trong RL
        self.rl_poor_response_markers = [
            "không chắc chắn", "có thể", "có lẽ", "tôi nghĩ", "tôi đoán",
            "không rõ", "không biết", "khó nói", "tùy thuộc", "không đủ thông tin"
        ]

        # Khởi tạo embedding model nếu được chỉ định
        self.embedding_model_instance = None
        if embedding_model:
            self._init_embedding_model(embedding_model)

        if self.verbose:
            logger.info(f"Initialized VietnameseRLEvaluator with embedding model: {embedding_model}")

    def _init_embedding_model(self, model_name: str):
        """
        Khởi tạo mô hình embedding.

        Args:
            model_name: Tên mô hình embedding
        """
        try:
            # Mô hình cơ bản
            if model_name == "phobert":
                from ..models.embeddings.vietnamese_phobert import VietnamesePhoBERT
                self.embedding_model_instance = VietnamesePhoBERT()
            elif model_name == "viebert":
                from ..models.embeddings.vietnamese_viebert import VietnameseVieBERT
                self.embedding_model_instance = VietnameseVieBERT()
            elif model_name == "xlm-roberta-vi":
                from ..models.embeddings.vietnamese_xlm_roberta import VietnameseXLMRoBERTa
                self.embedding_model_instance = VietnameseXLMRoBERTa()
            elif model_name == "multilingual-e5":
                from ..models.embeddings.vietnamese_multilingual_e5 import VietnameseMultilingualE5
                self.embedding_model_instance = VietnameseMultilingualE5()
            # Mô hình mới
            elif model_name == "bkai-foundation-vi":
                from ..models.embeddings.vietnamese_bkai_foundation import VietnameseBKAIFoundation
                self.embedding_model_instance = VietnameseBKAIFoundation()
            elif model_name == "envibert":
                from ..models.embeddings.vietnamese_envibert import VietnameseEnViBERT
                self.embedding_model_instance = VietnameseEnViBERT()
            elif model_name == "bartpho":
                from ..models.embeddings.vietnamese_bartpho import VietnameseBartPho
                self.embedding_model_instance = VietnameseBartPho()
            elif model_name == "velectra":
                from ..models.embeddings.vietnamese_velectra import VietnameseVELECTRA
                self.embedding_model_instance = VietnameseVELECTRA()
            elif model_name == "vibert4news":
                from ..models.embeddings.vietnamese_vibert4news import VietnameseViBERT4News
                self.embedding_model_instance = VietnameseViBERT4News()
            # Mô hình chuyên biệt
            elif model_name == "vi-mrc":
                from ..models.embeddings.vietnamese_vi_mrc import VietnameseViMRC
                self.embedding_model_instance = VietnameseViMRC()
            elif model_name == "vi-qa":
                from ..models.embeddings.vietnamese_vi_qa import VietnameseViQA
                self.embedding_model_instance = VietnameseViQA()
            elif model_name == "vietnamese-roberta":
                from ..models.embeddings.vietnamese_roberta import VietnameseRoBERTa
                self.embedding_model_instance = VietnameseRoBERTa()
            else:
                logger.warning(f"Unknown embedding model: {model_name}, using default metrics")
        except ImportError as e:
            logger.error(f"Error importing embedding model: {str(e)}")
        except Exception as e:
            logger.error(f"Error initializing embedding model: {str(e)}")

    def evaluate_rl_response_quality(self, response: str) -> float:
        """
        Đánh giá chất lượng phản hồi của mô hình RL-tuning với tiếng Việt.

        Args:
            response: Phản hồi cần đánh giá

        Returns:
            Điểm đánh giá từ 0.0 đến 1.0
        """
        if not response or not detect_vietnamese(response):
            return 0.0  # Không phải tiếng Việt hoặc phản hồi trống

        # Chuẩn hóa văn bản
        response = normalize_vietnamese_text(response)

        # Đếm số từ chỉ thị phản hồi tốt
        good_marker_count = 0
        for marker in self.rl_good_response_markers:
            good_marker_count += response.lower().count(marker.lower())

        # Đếm số từ chỉ thị phản hồi kém
        poor_marker_count = 0
        for marker in self.rl_poor_response_markers:
            poor_marker_count += response.lower().count(marker.lower())

        # Đếm số từ khóa RL
        rl_keyword_count = 0
        for keyword in self.rl_keywords:
            rl_keyword_count += response.lower().count(keyword.lower())

        # Tính tổng số từ
        word_count = len(response.split())

        if word_count < 10:
            return 0.0  # Phản hồi quá ngắn

        # Tính điểm dựa trên tỷ lệ từ chỉ thị và từ khóa
        good_marker_score = min(1.0, good_marker_count / (word_count / 30))
        poor_marker_penalty = min(0.5, poor_marker_count / (word_count / 20))
        rl_keyword_score = min(1.0, rl_keyword_count / 3)  # Mong đợi ít nhất 3 từ khóa RL

        # Tính điểm tổng hợp
        quality_score = (good_marker_score * 0.5) + (rl_keyword_score * 0.3) - (poor_marker_penalty * 0.2)

        # Đảm bảo điểm nằm trong khoảng 0.0 đến 1.0
        return max(0.0, min(1.0, quality_score))

    def evaluate_rl_consistency(self, responses: List[str]) -> float:
        """
        Đánh giá tính nhất quán của các phản hồi từ mô hình RL-tuning với tiếng Việt.

        Args:
            responses: Danh sách các phản hồi cần đánh giá

        Returns:
            Điểm đánh giá từ 0.0 đến 1.0
        """
        if not responses or len(responses) < 2:
            return 1.0  # Không đủ phản hồi để đánh giá tính nhất quán

        # Kiểm tra xem các phản hồi có phải tiếng Việt không
        vietnamese_responses = [r for r in responses if detect_vietnamese(r)]
        if len(vietnamese_responses) < 2:
            return 1.0  # Không đủ phản hồi tiếng Việt để đánh giá

        # Chuẩn hóa các phản hồi
        normalized_responses = [normalize_vietnamese_text(r) for r in vietnamese_responses]

        # Tính toán độ nhất quán dựa trên embedding nếu có
        if self.embedding_model_instance:
            try:
                # Tạo embedding cho các phản hồi
                embeddings = [self.embedding_model_instance.encode(r) for r in normalized_responses]

                # Tính toán độ tương đồng cosine giữa các cặp embedding
                similarities = []
                for i in range(len(embeddings)):
                    for j in range(i+1, len(embeddings)):
                        similarity = self._cosine_similarity(embeddings[i], embeddings[j])
                        similarities.append(similarity)

                # Tính trung bình độ tương đồng
                avg_similarity = sum(similarities) / len(similarities) if similarities else 0.0

                # Điểm nhất quán dựa trên độ tương đồng
                # Chúng ta muốn các phản hồi nhất quán nhưng không giống hệt nhau
                # Điểm tối ưu là khoảng 0.7-0.9
                if avg_similarity > 0.95:  # Quá giống nhau
                    consistency_score = 0.8
                elif avg_similarity < 0.3:  # Quá khác nhau
                    consistency_score = 0.3
                else:
                    # Điểm cao nhất ở khoảng 0.7-0.9
                    consistency_score = 1.0 - abs(0.8 - avg_similarity)

                return consistency_score
            except Exception as e:
                logger.error(f"Error calculating embedding-based consistency: {str(e)}")
                # Fallback to basic consistency

        # Phương pháp đơn giản: Đánh giá dựa trên độ dài và từ khóa
        lengths = [len(r.split()) for r in normalized_responses]
        avg_length = sum(lengths) / len(lengths)
        length_variance = sum((l - avg_length)**2 for l in lengths) / len(lengths)
        length_consistency = 1.0 / (1.0 + math.sqrt(length_variance) / avg_length)

        # Đếm số từ khóa RL trong mỗi phản hồi
        keyword_counts = []
        for response in normalized_responses:
            count = 0
            for keyword in self.rl_keywords:
                count += response.lower().count(keyword.lower())
            keyword_counts.append(count)

        # Tính độ nhất quán của số từ khóa
        avg_keyword_count = sum(keyword_counts) / len(keyword_counts)
        keyword_variance = sum((c - avg_keyword_count)**2 for c in keyword_counts) / len(keyword_counts)
        keyword_consistency = 1.0 / (1.0 + math.sqrt(keyword_variance) / max(1, avg_keyword_count))

        # Tính điểm tổng hợp
        consistency_score = (length_consistency * 0.4) + (keyword_consistency * 0.6)

        return consistency_score

    def evaluate_rl_performance(
        self,
        response: str,
        reference_response: Optional[str] = None,
        task_description: Optional[str] = None
    ) -> float:
        """
        Đánh giá hiệu suất của mô hình RL-tuning với tiếng Việt.

        Args:
            response: Phản hồi cần đánh giá
            reference_response: Phản hồi tham chiếu (nếu có)
            task_description: Mô tả nhiệm vụ (nếu có)

        Returns:
            Điểm đánh giá từ 0.0 đến 1.0
        """
        if not response or not detect_vietnamese(response):
            return 0.0  # Không phải tiếng Việt hoặc phản hồi trống

        # Chuẩn hóa văn bản
        response = normalize_vietnamese_text(response)

        # Tính điểm chất lượng phản hồi
        response_quality = self.evaluate_rl_response_quality(response)

        # Tính điểm chất lượng lập luận
        reasoning_quality = self.basic_metrics.calculate_reasoning_quality(response)

        # Tính điểm đa dạng từ vựng
        lexical_diversity = self.basic_metrics.calculate_lexical_diversity(response)

        # Tính điểm ngữ pháp
        grammatical_correctness = self.basic_metrics.calculate_grammatical_correctness(response)

        # Nếu có phản hồi tham chiếu, so sánh với phản hồi tham chiếu
        reference_similarity = 0.5  # Giá trị mặc định
        if reference_response and detect_vietnamese(reference_response):
            reference_response = normalize_vietnamese_text(reference_response)

            if self.embedding_model_instance:
                try:
                    # Tạo embedding cho phản hồi và phản hồi tham chiếu
                    response_embedding = self.embedding_model_instance.encode(response)
                    reference_embedding = self.embedding_model_instance.encode(reference_response)

                    # Tính toán độ tương đồng cosine
                    reference_similarity = self._cosine_similarity(response_embedding, reference_embedding)
                except Exception as e:
                    logger.error(f"Error calculating embedding-based similarity: {str(e)}")
                    # Fallback to basic similarity

            # Phương pháp đơn giản: Đếm số từ chung
            response_words = set(response.lower().split())
            reference_words = set(reference_response.lower().split())
            common_words = response_words.intersection(reference_words)

            # Tính điểm dựa trên tỷ lệ từ chung
            word_overlap = len(common_words) / max(1, min(len(response_words), len(reference_words)))

            # Kết hợp với reference_similarity nếu đã tính
            reference_similarity = (reference_similarity + word_overlap) / 2

        # Tính điểm tổng hợp
        performance_score = (
            (response_quality * 0.3) +
            (reasoning_quality * 0.3) +
            (lexical_diversity * 0.1) +
            (grammatical_correctness * 0.1) +
            (reference_similarity * 0.2)
        )

        return performance_score

    def evaluate_rl_task_completion(
        self,
        response: str,
        task_description: str,
        expected_keywords: Optional[List[str]] = None
    ) -> float:
        """
        Đánh giá mức độ hoàn thành nhiệm vụ của mô hình RL-tuning với tiếng Việt.

        Args:
            response: Phản hồi cần đánh giá
            task_description: Mô tả nhiệm vụ
            expected_keywords: Danh sách các từ khóa mong đợi trong phản hồi

        Returns:
            Điểm đánh giá từ 0.0 đến 1.0
        """
        if not response or not detect_vietnamese(response):
            return 0.0  # Không phải tiếng Việt hoặc phản hồi trống

        if not task_description:
            return 0.5  # Không có mô tả nhiệm vụ, không thể đánh giá chính xác

        # Chuẩn hóa văn bản
        response = normalize_vietnamese_text(response)
        task_description = normalize_vietnamese_text(task_description)

        # Trích xuất từ khóa từ mô tả nhiệm vụ nếu không có danh sách từ khóa mong đợi
        if not expected_keywords:
            expected_keywords = extract_vietnamese_keywords(task_description, num_keywords=10)

        # Đếm số từ khóa mong đợi xuất hiện trong phản hồi
        keyword_count = 0
        for keyword in expected_keywords:
            if keyword.lower() in response.lower():
                keyword_count += 1

        # Tính điểm dựa trên tỷ lệ từ khóa mong đợi
        keyword_score = keyword_count / max(1, len(expected_keywords))

        # Tính điểm dựa trên độ tương đồng ngữ nghĩa nếu có embedding model
        semantic_score = 0.5  # Giá trị mặc định
        if self.embedding_model_instance:
            try:
                # Tạo embedding cho phản hồi và mô tả nhiệm vụ
                response_embedding = self.embedding_model_instance.encode(response)
                task_embedding = self.embedding_model_instance.encode(task_description)

                # Tính toán độ tương đồng cosine
                semantic_score = self._cosine_similarity(response_embedding, task_embedding)
            except Exception as e:
                logger.error(f"Error calculating embedding-based task similarity: {str(e)}")
                # Fallback to keyword score

        # Tính điểm tổng hợp
        completion_score = (keyword_score * 0.6) + (semantic_score * 0.4)

        return completion_score

    def evaluate_vietnamese_specific_metrics(self, response: str) -> Dict[str, float]:
        """
        Đánh giá các metrics đặc thù cho tiếng Việt.

        Args:
            response: Phản hồi cần đánh giá

        Returns:
            Dictionary chứa các metrics đánh giá đặc thù cho tiếng Việt
        """
        if not response or not detect_vietnamese(response):
            return {
                "vietnamese_compound_word_usage": 0.0,
                "vietnamese_idiom_usage": 0.0,
                "vietnamese_cultural_context": 0.0,
                "vietnamese_tone_consistency": 0.0,
                "vietnamese_regional_term_usage": 0.0,
                "vietnamese_formal_language": 0.0,
                "vietnamese_technical_term_usage": 0.0
            }

        # Chuẩn hóa văn bản
        response = normalize_vietnamese_text(response)

        # Đánh giá sử dụng từ ghép tiếng Việt
        compound_word_usage = self.quality_metrics.evaluate_compound_word_accuracy(response)

        # Đánh giá sử dụng thành ngữ, tục ngữ tiếng Việt
        idiom_usage = self.quality_metrics.evaluate_idiom_usage(response)

        # Đánh giá ngữ cảnh văn hóa tiếng Việt
        cultural_context = 0.5  # Giá trị mặc định

        # Kiểm tra các từ khóa văn hóa tiếng Việt
        vietnamese_cultural_keywords = [
            "tết", "áo dài", "nón lá", "phở", "bánh chưng", "bánh tét", "chùa",
            "đình", "đền", "miếu", "lễ hội", "cúng", "thờ cúng", "tổ tiên",
            "truyền thống", "văn hóa", "dân tộc", "bản sắc", "di sản"
        ]

        cultural_keyword_count = 0
        for keyword in vietnamese_cultural_keywords:
            if keyword.lower() in response.lower():
                cultural_keyword_count += 1

        if cultural_keyword_count > 0:
            cultural_context = min(1.0, cultural_keyword_count / 3)

        # Đánh giá tính nhất quán về giọng điệu (tone)
        tone_consistency = self._evaluate_vietnamese_tone_consistency(response)

        # Đánh giá sử dụng từ ngữ vùng miền
        regional_term_usage = self._evaluate_vietnamese_regional_term_usage(response)

        # Đánh giá mức độ trang trọng của ngôn ngữ
        formal_language = self._evaluate_vietnamese_formal_language(response)

        # Đánh giá sử dụng thuật ngữ chuyên ngành
        technical_term_usage = self._evaluate_vietnamese_technical_term_usage(response)

        return {
            "vietnamese_compound_word_usage": compound_word_usage,
            "vietnamese_idiom_usage": idiom_usage,
            "vietnamese_cultural_context": cultural_context,
            "vietnamese_tone_consistency": tone_consistency,
            "vietnamese_regional_term_usage": regional_term_usage,
            "vietnamese_formal_language": formal_language,
            "vietnamese_technical_term_usage": technical_term_usage
        }

    def _evaluate_vietnamese_tone_consistency(self, response: str) -> float:
        """
        Đánh giá tính nhất quán về giọng điệu (tone) trong văn bản tiếng Việt.

        Args:
            response: Phản hồi cần đánh giá

        Returns:
            Điểm đánh giá từ 0.0 đến 1.0
        """
        # Phân tích câu để xác định giọng điệu
        sentences = response.split('.')
        sentences = [s.strip() for s in sentences if s.strip()]

        if not sentences:
            return 0.5

        # Các từ chỉ thị giọng điệu
        formal_tone_markers = ["xin", "kính", "trân trọng", "vui lòng", "cảm ơn", "thưa"]
        informal_tone_markers = ["nhé", "nha", "đấy", "thôi", "vậy đó", "ấy chứ"]
        assertive_tone_markers = ["phải", "cần", "nên", "chắc chắn", "nhất định", "bắt buộc"]
        questioning_tone_markers = ["không", "chứ", "à", "nhỉ", "vậy", "sao", "thế"]

        # Đếm số lần xuất hiện của các loại giọng điệu
        formal_count = sum(1 for marker in formal_tone_markers if marker in response.lower())
        informal_count = sum(1 for marker in informal_tone_markers if marker in response.lower())
        assertive_count = sum(1 for marker in assertive_tone_markers if marker in response.lower())
        questioning_count = sum(1 for marker in questioning_tone_markers if marker in response.lower())

        # Tổng số marker
        total_markers = formal_count + informal_count + assertive_count + questioning_count

        if total_markers == 0:
            return 0.7  # Giá trị mặc định nếu không có marker nào

        # Tính tỷ lệ của loại giọng điệu chiếm ưu thế
        dominant_tone = max(formal_count, informal_count, assertive_count, questioning_count)
        tone_ratio = dominant_tone / total_markers

        # Điểm nhất quán dựa trên tỷ lệ giọng điệu chiếm ưu thế
        # Nếu một loại giọng điệu chiếm ưu thế rõ rệt, điểm nhất quán cao
        return min(1.0, 0.5 + tone_ratio / 2)

    def _evaluate_vietnamese_regional_term_usage(self, response: str) -> float:
        """
        Đánh giá sử dụng từ ngữ vùng miền trong văn bản tiếng Việt.

        Args:
            response: Phản hồi cần đánh giá

        Returns:
            Điểm đánh giá từ 0.0 đến 1.0
        """
        # Từ ngữ đặc trưng cho miền Bắc
        northern_terms = ["tôi", "ông ấy", "bà ấy", "đấy", "thế", "này", "kia", "ấy", "vâng", "đúng rồi"]

        # Từ ngữ đặc trưng cho miền Nam
        southern_terms = ["tui", "ổng", "bả", "đó", "vậy", "nè", "đó", "đây", "dạ", "đúng rồi đó"]

        # Từ ngữ đặc trưng cho miền Trung
        central_terms = ["tau", "hắn", "mệ", "rứa", "ni", "tê", "đây ni", "dạ", "đúng rồi đó"]

        # Đếm số lần xuất hiện của từng loại
        northern_count = sum(1 for term in northern_terms if f" {term} " in f" {response.lower()} ")
        southern_count = sum(1 for term in southern_terms if f" {term} " in f" {response.lower()} ")
        central_count = sum(1 for term in central_terms if f" {term} " in f" {response.lower()} ")

        # Tổng số từ vùng miền
        total_regional_terms = northern_count + southern_count + central_count

        if total_regional_terms == 0:
            return 0.5  # Giá trị mặc định nếu không có từ vùng miền nào

        # Tính tỷ lệ của loại từ vùng miền chiếm ưu thế
        dominant_region = max(northern_count, southern_count, central_count)
        region_ratio = dominant_region / total_regional_terms

        # Điểm nhất quán dựa trên tỷ lệ từ vùng miền chiếm ưu thế
        # Nếu một loại từ vùng miền chiếm ưu thế rõ rệt, điểm nhất quán cao
        return min(1.0, 0.5 + region_ratio / 2)

    def _evaluate_vietnamese_formal_language(self, response: str) -> float:
        """
        Đánh giá mức độ trang trọng của ngôn ngữ trong văn bản tiếng Việt.

        Args:
            response: Phản hồi cần đánh giá

        Returns:
            Điểm đánh giá từ 0.0 đến 1.0 (1.0 là rất trang trọng)
        """
        # Các từ ngữ trang trọng
        formal_terms = [
            "kính thưa", "trân trọng", "xin phép", "kính mời", "xin cảm ơn",
            "xin chân thành", "kính chúc", "xin kính", "xin lỗi", "xin phép",
            "chúng tôi", "quý vị", "quý khách", "kính gửi", "thưa ông", "thưa bà"
        ]

        # Các từ ngữ không trang trọng
        informal_terms = [
            "này", "ê", "ơi", "nhé", "nha", "thôi", "vậy đó", "đấy", "nhớ", "nghe",
            "tao", "mày", "tui", "bạn", "ông", "bà", "anh", "chị", "mình", "cậu"
        ]

        # Đếm số lần xuất hiện
        formal_count = sum(1 for term in formal_terms if term in response.lower())
        informal_count = sum(1 for term in informal_terms if term in response.lower())

        # Tính điểm trang trọng
        if formal_count == 0 and informal_count == 0:
            return 0.5  # Giá trị mặc định

        # Điểm trang trọng dựa trên tỷ lệ giữa từ trang trọng và không trang trọng
        formal_ratio = formal_count / max(1, formal_count + informal_count)

        return formal_ratio

    def _evaluate_vietnamese_technical_term_usage(self, response: str) -> float:
        """
        Đánh giá sử dụng thuật ngữ chuyên ngành trong văn bản tiếng Việt.

        Args:
            response: Phản hồi cần đánh giá

        Returns:
            Điểm đánh giá từ 0.0 đến 1.0
        """
        # Các thuật ngữ chuyên ngành CNTT
        it_terms = [
            "máy tính", "phần mềm", "phần cứng", "hệ điều hành", "cơ sở dữ liệu",
            "mạng", "bảo mật", "thuật toán", "lập trình", "mã nguồn", "giao diện",
            "trí tuệ nhân tạo", "học máy", "dữ liệu lớn", "điện toán đám mây"
        ]

        # Các thuật ngữ chuyên ngành AI/ML
        ai_terms = [
            "học máy", "học sâu", "mạng nơ-ron", "học tăng cường", "học có giám sát",
            "học không giám sát", "mô hình", "huấn luyện", "tập dữ liệu", "độ chính xác",
            "độ nhớ", "độ chính xác", "hàm mất mát", "tối ưu hóa", "gradient", "epoch"
        ]

        # Các thuật ngữ chuyên ngành kinh tế
        economics_terms = [
            "lạm phát", "tỷ giá", "thị trường", "cung cầu", "lãi suất", "cổ phiếu",
            "trái phiếu", "đầu tư", "tài chính", "ngân sách", "thuế", "doanh thu",
            "lợi nhuận", "chi phí", "tăng trưởng", "suy thoái", "kinh tế vĩ mô"
        ]

        # Các thuật ngữ chuyên ngành y học
        medical_terms = [
            "bệnh lý", "chẩn đoán", "điều trị", "triệu chứng", "bệnh nhân", "thuốc",
            "phẫu thuật", "xét nghiệm", "huyết áp", "tim mạch", "hô hấp", "tiêu hóa",
            "thần kinh", "nội tiết", "miễn dịch", "vi khuẩn", "virus", "vắc-xin"
        ]

        # Đếm số lần xuất hiện của các thuật ngữ chuyên ngành
        it_count = sum(1 for term in it_terms if term in response.lower())
        ai_count = sum(1 for term in ai_terms if term in response.lower())
        economics_count = sum(1 for term in economics_terms if term in response.lower())
        medical_count = sum(1 for term in medical_terms if term in response.lower())

        # Tổng số thuật ngữ chuyên ngành
        total_technical_terms = it_count + ai_count + economics_count + medical_count

        # Tổng số từ trong văn bản
        total_words = len(response.split())

        if total_words < 10:
            return 0.0  # Văn bản quá ngắn

        # Tính tỷ lệ thuật ngữ chuyên ngành trên tổng số từ
        technical_ratio = total_technical_terms / total_words

        # Điểm sử dụng thuật ngữ chuyên ngành
        # Nếu tỷ lệ quá cao (> 0.3) hoặc quá thấp (< 0.05), điểm sẽ thấp
        if technical_ratio > 0.3:
            return 0.7  # Quá nhiều thuật ngữ chuyên ngành
        elif technical_ratio < 0.05:
            return 0.3  # Quá ít thuật ngữ chuyên ngành
        else:
            # Điểm cao nhất khi tỷ lệ khoảng 0.1-0.2
            return min(1.0, 0.5 + technical_ratio * 2.5)

    def evaluate_rl_vietnamese_alignment(self, response: str) -> float:
        """
        Đánh giá mức độ phù hợp của phản hồi RL với ngôn ngữ tiếng Việt.

        Args:
            response: Phản hồi cần đánh giá

        Returns:
            Điểm đánh giá từ 0.0 đến 1.0
        """
        if not response or not detect_vietnamese(response):
            return 0.0

        # Chuẩn hóa văn bản
        response = normalize_vietnamese_text(response)

        # Đánh giá dấu thanh
        diacritic_score = self.basic_metrics.calculate_diacritic_consistency(response)

        # Đánh giá phương ngữ
        dialect_score = self.basic_metrics.calculate_dialect_consistency(response)

        # Đánh giá ngữ pháp
        grammar_score = self.basic_metrics.calculate_grammatical_correctness(response)

        # Đánh giá từ vựng
        vocabulary_score = self.basic_metrics.calculate_lexical_diversity(response)

        # Tính điểm tổng hợp
        alignment_score = (
            (diacritic_score * 0.3) +
            (dialect_score * 0.2) +
            (grammar_score * 0.3) +
            (vocabulary_score * 0.2)
        )

        return alignment_score

    def evaluate_all_metrics(
        self,
        response: str,
        reference_response: Optional[str] = None,
        task_description: Optional[str] = None,
        expected_keywords: Optional[List[str]] = None,
        previous_responses: Optional[List[str]] = None
    ) -> Dict[str, float]:
        """
        Đánh giá tất cả các metrics cho mô hình RL-tuning với tiếng Việt.

        Args:
            response: Phản hồi cần đánh giá
            reference_response: Phản hồi tham chiếu (nếu có)
            task_description: Mô tả nhiệm vụ (nếu có)
            expected_keywords: Danh sách các từ khóa mong đợi trong phản hồi
            previous_responses: Danh sách các phản hồi trước đó để đánh giá tính nhất quán

        Returns:
            Dictionary chứa các metrics đánh giá
        """
        metrics = {}

        # Metrics cơ bản
        metrics["diacritic_consistency"] = self.basic_metrics.calculate_diacritic_consistency(response)
        metrics["dialect_consistency"] = self.basic_metrics.calculate_dialect_consistency(response)
        metrics["reasoning_quality"] = self.basic_metrics.calculate_reasoning_quality(response)
        metrics["lexical_diversity"] = self.basic_metrics.calculate_lexical_diversity(response)
        metrics["grammatical_correctness"] = self.basic_metrics.calculate_grammatical_correctness(response)

        # Metrics đặc thù cho RL
        metrics["rl_response_quality"] = self.evaluate_rl_response_quality(response)
        metrics["rl_performance"] = self.evaluate_rl_performance(
            response,
            reference_response=reference_response,
            task_description=task_description
        )

        if task_description:
            metrics["rl_task_completion"] = self.evaluate_rl_task_completion(
                response,
                task_description=task_description,
                expected_keywords=expected_keywords
            )

        if previous_responses:
            all_responses = previous_responses + [response]
            metrics["rl_consistency"] = self.evaluate_rl_consistency(all_responses)

        # Metrics đặc thù cho tiếng Việt
        vietnamese_metrics = self.evaluate_vietnamese_specific_metrics(response)
        metrics.update(vietnamese_metrics)

        # Metric đánh giá mức độ phù hợp với tiếng Việt
        metrics["rl_vietnamese_alignment"] = self.evaluate_rl_vietnamese_alignment(response)

        # Phân loại metrics theo nhóm
        metric_groups = {
            "basic": {
                "diacritic_consistency": 0.15,
                "dialect_consistency": 0.15,
                "reasoning_quality": 0.30,
                "lexical_diversity": 0.20,
                "grammatical_correctness": 0.20
            },
            "rl": {
                "rl_response_quality": 0.30,
                "rl_performance": 0.30,
                "rl_task_completion": 0.25,
                "rl_consistency": 0.15
            },
            "vietnamese_specific": {
                "vietnamese_compound_word_usage": 0.15,
                "vietnamese_idiom_usage": 0.15,
                "vietnamese_cultural_context": 0.15,
                "vietnamese_tone_consistency": 0.15,
                "vietnamese_regional_term_usage": 0.15,
                "vietnamese_formal_language": 0.10,
                "vietnamese_technical_term_usage": 0.15
            }
        }

        # Trọng số cho mỗi nhóm metrics
        group_weights = {
            "basic": 0.25,
            "rl": 0.40,
            "vietnamese_specific": 0.30,
            "alignment": 0.05  # rl_vietnamese_alignment được xử lý riêng
        }

        # Tính điểm cho mỗi nhóm
        group_scores = {}

        # Tính điểm cho nhóm basic
        basic_metrics = {k: metrics[k] for k in metric_groups["basic"] if k in metrics}
        if basic_metrics:
            basic_weights = {k: metric_groups["basic"][k] for k in basic_metrics}
            total_basic_weight = sum(basic_weights.values())
            group_scores["basic"] = sum(basic_metrics[k] * basic_weights[k] / total_basic_weight for k in basic_metrics)
        else:
            group_scores["basic"] = 0.0

        # Tính điểm cho nhóm rl
        rl_metrics = {k: metrics[k] for k in metric_groups["rl"] if k in metrics}
        if rl_metrics:
            rl_weights = {k: metric_groups["rl"][k] for k in rl_metrics}
            total_rl_weight = sum(rl_weights.values())
            group_scores["rl"] = sum(rl_metrics[k] * rl_weights[k] / total_rl_weight for k in rl_metrics)
        else:
            group_scores["rl"] = 0.0

        # Tính điểm cho nhóm vietnamese_specific
        vn_metrics = {k: metrics[k] for k in metric_groups["vietnamese_specific"] if k in metrics}
        if vn_metrics:
            vn_weights = {k: metric_groups["vietnamese_specific"][k] for k in vn_metrics}
            total_vn_weight = sum(vn_weights.values())
            group_scores["vietnamese_specific"] = sum(vn_metrics[k] * vn_weights[k] / total_vn_weight for k in vn_metrics)
        else:
            group_scores["vietnamese_specific"] = 0.0

        # Lấy điểm alignment
        group_scores["alignment"] = metrics.get("rl_vietnamese_alignment", 0.0)

        # Tính điểm tổng hợp từ các nhóm
        available_groups = {k: v for k, v in group_weights.items() if k in group_scores}
        total_group_weight = sum(available_groups.values())

        if total_group_weight > 0:
            metrics["overall_score"] = sum(group_scores[k] * group_weights[k] / total_group_weight for k in available_groups)
        else:
            metrics["overall_score"] = 0.0

        # Thêm điểm cho mỗi nhóm vào kết quả
        metrics["basic_metrics_score"] = group_scores.get("basic", 0.0)
        metrics["rl_metrics_score"] = group_scores.get("rl", 0.0)
        metrics["vietnamese_specific_score"] = group_scores.get("vietnamese_specific", 0.0)

        return metrics

    def _cosine_similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """
        Tính toán độ tương đồng cosine giữa hai vector.

        Args:
            vec1: Vector thứ nhất
            vec2: Vector thứ hai

        Returns:
            Độ tương đồng cosine từ 0.0 đến 1.0
        """
        if len(vec1) != len(vec2):
            logger.warning(f"Vector dimensions do not match: {len(vec1)} vs {len(vec2)}")
            return 0.0

        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        return np.dot(vec1, vec2) / (norm1 * norm2)
