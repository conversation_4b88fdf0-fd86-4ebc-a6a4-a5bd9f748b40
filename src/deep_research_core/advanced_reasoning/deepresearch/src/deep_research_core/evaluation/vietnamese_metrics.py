"""
Vietnamese-specific evaluation metrics.

Module này cung cấp các metrics đánh giá chất lượng đặc thù cho tiếng Việt.
"""

import re
import string
import math
from typing import Dict, List, Any, Optional, Tuple, Union, Set

import numpy as np

from ..utils.structured_logging import get_logger
from ..utils.vietnamese_utils import (
    normalize_vietnamese_text,
    detect_vietnamese,
    extract_vietnamese_keywords
)

try:
    import underthesea
    UNDERTHESEA_AVAILABLE = True
except ImportError:
    UNDERTHESEA_AVAILABLE = False

# Create a logger
logger = get_logger(__name__)

class VietnameseEvaluationMetrics:
    """
    Metrics đánh giá chất lượng đặc thù cho tiếng Việt.

    Lớp này cung cấp các phương thức để đánh giá chất lượng của các mô hình
    xử lý ngôn ngữ tự nhiên tiếng Vi<PERSON>t.
    """

    def __init__(self):
        """Khởi tạo VietnameseEvaluationMetrics."""
        # <PERSON><PERSON><PERSON> từ chỉ thị phổ biến trong tiếng Việt
        self.vietnamese_discourse_markers = [
            "đầu tiên", "thứ hai", "thứ ba", "tiếp theo", "sau đó",
            "ngoài ra", "hơn nữa", "hơn thế nữa", "cuối cùng", "kết luận",
            "tóm lại", "vì vậy", "do đó", "từ đó", "kết quả là",
            "trước hết", "tiếp đến", "kế đến", "sau cùng", "tóm tắt lại",
            "nói cách khác", "nói một cách khác", "nói ngắn gọn", "nói tóm tắt"
        ]

        # Các từ chỉ thị lập luận trong tiếng Việt
        self.vietnamese_reasoning_markers = [
            "bởi vì", "vì", "do", "nhờ", "nhờ vào", "dựa vào", "dựa trên",
            "theo", "căn cứ vào", "xét về", "xét theo", "nếu", "giả sử",
            "trong trường hợp", "với điều kiện", "mặc dù", "tuy", "tuy nhiên",
            "mặc dù vậy", "ngược lại", "trái lại", "thay vào đó",
            "vì lẽ đó", "chính vì thế", "do vậy", "vì thế", "vì vậy",
            "bởi lẽ", "xét thấy", "nhận thấy", "có thể thấy", "dễ dàng nhận ra",
            "rõ ràng là", "hiển nhiên là", "chắc chắn là", "không thể phủ nhận"
        ]

        # Các từ chỉ thị trích dẫn trong tiếng Việt
        self.vietnamese_citation_markers = [
            "theo", "dựa theo", "căn cứ vào", "trích dẫn từ", "như đã nêu trong",
            "như đã đề cập trong", "theo như", "dựa trên", "tham khảo từ",
            "theo nghiên cứu của", "theo số liệu từ", "theo báo cáo của",
            "theo kết quả từ", "dẫn lời", "trích lời", "như đã chỉ ra trong",
            "như đã phân tích trong", "như đã chứng minh trong", "theo quan điểm của"
        ]

        # Các từ chỉ thị kết luận trong tiếng Việt
        self.vietnamese_conclusion_markers = [
            "tóm lại", "kết luận", "tổng kết", "tóm tắt", "nói tóm lại",
            "tổng quan", "nhìn chung", "nhìn tổng thể", "cuối cùng",
            "tóm lược", "tổng hợp lại", "kết quả là", "như vậy", "do đó",
            "vì vậy", "từ đó", "tóm tắt lại", "nói cách khác", "tổng quát hóa",
            "rút ra", "có thể kết luận", "điều này cho thấy", "qua đó thấy được"
        ]

        # Các từ ngữ học thuật trong tiếng Việt
        self.vietnamese_academic_terms = [
            "nghiên cứu", "phân tích", "đánh giá", "khảo sát", "thực nghiệm",
            "lý thuyết", "mô hình", "phương pháp", "cách tiếp cận", "giả thuyết",
            "kết quả", "dữ liệu", "số liệu", "bằng chứng", "minh chứng",
            "luận điểm", "luận cứ", "luận chứng", "quan điểm", "lập trường",
            "khái niệm", "định nghĩa", "thuật ngữ", "chuyên ngành", "lĩnh vực",
            "tổng hợp", "so sánh", "đối chiếu", "tương quan", "tương đồng",
            "khác biệt", "đặc điểm", "đặc trưng", "đặc tính", "tính chất"
        ]

        # Các từ ngữ chuyên ngành khoa học trong tiếng Việt
        self.vietnamese_scientific_terms = [
            "thuật toán", "mô hình", "dữ liệu", "biến số", "hàm số",
            "phương trình", "hệ số", "tham số", "đạo hàm", "tích phân",
            "xác suất", "thống kê", "phân phối", "hồi quy", "tương quan",
            "giả thuyết", "kiểm định", "độ tin cậy", "sai số", "độ lệch",
            "trí tuệ nhân tạo", "học máy", "học sâu", "mạng nơ-ron", "vector",
            "ma trận", "tensor", "gradient", "tối ưu hóa", "hàm mất mát"
        ]

    def calculate_diacritic_consistency(self, text: str) -> float:
        """
        Tính toán độ nhất quán của dấu thanh trong văn bản tiếng Việt.

        Args:
            text: Văn bản tiếng Việt cần đánh giá

        Returns:
            Điểm đánh giá từ 0.0 đến 1.0
        """
        if not text or not detect_vietnamese(text):
            return 1.0  # Không phải tiếng Việt hoặc văn bản trống

        # Chuẩn hóa văn bản
        text = normalize_vietnamese_text(text)

        # Tách thành các từ
        words = text.split()

        # Đếm số từ có dấu thanh và không có dấu thanh
        vietnamese_chars = set('àáảãạăằắẳẵặâầấẩẫậèéẻẽẹêềếểễệìíỉĩịòóỏõọôồốổỗộơờớởỡợùúủũụưừứửữựỳýỷỹỵđ')

        words_with_diacritics = 0
        words_without_diacritics = 0

        for word in words:
            has_vietnamese_char = any(char in vietnamese_chars for char in word.lower())

            if has_vietnamese_char:
                words_with_diacritics += 1
            elif re.match(r'^[a-zA-Z]+$', word):  # Chỉ xét các từ chỉ chứa chữ cái Latin
                words_without_diacritics += 1

        # Tính tỷ lệ nhất quán
        total_vietnamese_words = words_with_diacritics + words_without_diacritics

        if total_vietnamese_words == 0:
            return 1.0

        # Nếu tất cả các từ đều có dấu thanh hoặc đều không có dấu thanh, thì nhất quán
        if words_with_diacritics == 0 or words_without_diacritics == 0:
            return 1.0

        # Nếu có cả từ có dấu thanh và không có dấu thanh, tính tỷ lệ nhất quán
        consistency = max(words_with_diacritics, words_without_diacritics) / total_vietnamese_words

        return consistency

    def calculate_compound_word_accuracy(self, text: str, reference_compounds: Optional[List[str]] = None) -> float:
        """
        Tính toán độ chính xác của từ ghép trong văn bản tiếng Việt.

        Args:
            text: Văn bản tiếng Việt cần đánh giá
            reference_compounds: Danh sách các từ ghép tham chiếu

        Returns:
            Điểm đánh giá từ 0.0 đến 1.0
        """
        if not text or not detect_vietnamese(text):
            return 1.0  # Không phải tiếng Việt hoặc văn bản trống

        # Nếu không có danh sách từ ghép tham chiếu, sử dụng danh sách mặc định
        if reference_compounds is None:
            reference_compounds = [
                "trí tuệ nhân tạo", "học máy", "xử lý ngôn ngữ tự nhiên",
                "thị giác máy tính", "khai phá dữ liệu", "học sâu",
                "mạng nơ-ron", "mạng nơ-ron nhân tạo", "mạng nơ-ron sâu",
                "học tăng cường", "học có giám sát", "học không giám sát",
                "xử lý ảnh", "nhận dạng giọng nói", "nhận dạng khuôn mặt",
                "phân tích dữ liệu", "khoa học dữ liệu", "dữ liệu lớn",
                "internet vạn vật", "điện toán đám mây", "chuỗi khối",
                "thực tế ảo", "thực tế tăng cường", "trợ lý ảo",
                "xe tự lái", "robot thông minh", "hệ thống chuyên gia",
                "xử lý ngôn ngữ", "dịch máy", "tóm tắt văn bản",
                "phân loại văn bản", "phân tích tình cảm", "trả lời câu hỏi"
            ]

        # Chuẩn hóa văn bản
        text = normalize_vietnamese_text(text)

        # Đếm số từ ghép xuất hiện trong văn bản
        compound_count = 0
        for compound in reference_compounds:
            if compound.lower() in text.lower():
                compound_count += 1

        # Tính điểm dựa trên tỷ lệ từ ghép được sử dụng chính xác
        # Nếu không có từ ghép nào trong danh sách tham chiếu, trả về 1.0
        if len(reference_compounds) == 0:
            return 1.0

        # Nếu văn bản quá ngắn, không mong đợi nhiều từ ghép
        text_length = len(text.split())
        expected_compounds = min(len(reference_compounds), max(1, text_length // 20))

        # Tính điểm dựa trên số từ ghép được tìm thấy so với số từ ghép mong đợi
        score = min(1.0, compound_count / expected_compounds)

        return score

    def calculate_dialect_consistency(self, text: str) -> float:
        """
        Tính toán độ nhất quán của phương ngữ trong văn bản tiếng Việt.

        Args:
            text: Văn bản tiếng Việt cần đánh giá

        Returns:
            Điểm đánh giá từ 0.0 đến 1.0
        """
        if not text or not detect_vietnamese(text):
            return 1.0  # Không phải tiếng Việt hoặc văn bản trống

        # Chuẩn hóa văn bản
        text = normalize_vietnamese_text(text)

        # Các từ đặc trưng cho phương ngữ Bắc
        northern_dialect = [
            "tao", "mày", "ông", "bà", "bố", "mẹ", "anh", "chị", "em",
            "cháu", "ông nội", "bà nội", "ông ngoại", "bà ngoại",
            "thế", "thế này", "thế kia", "thế nào", "thế à",
            "này", "kia", "kìa", "đây", "đấy", "đằng kia",
            "vâng", "dạ", "ừ", "ừm", "vậy", "thôi", "đi", "nhé"
        ]

        # Các từ đặc trưng cho phương ngữ Nam
        southern_dialect = [
            "tui", "bạn", "ba", "má", "anh", "chị", "em",
            "cháu", "ông nội", "bà nội", "ông ngoại", "bà ngoại",
            "vầy", "vầy nè", "vậy đó", "sao", "sao vậy",
            "nè", "đó", "kìa", "đây", "đó", "đằng kia",
            "dạ", "ừ", "ừm", "vậy", "rồi", "đi", "nha"
        ]

        # Các từ đặc trưng cho phương ngữ Trung
        central_dialect = [
            "tau", "mi", "răng", "rứa", "mô", "tê", "ni",
            "nớ", "đàng", "đó", "đây", "chừ", "bây chừ",
            "rồi", "thôi", "đi", "nghe"
        ]

        # Đếm số từ thuộc mỗi phương ngữ
        northern_count = 0
        southern_count = 0
        central_count = 0

        words = text.split()
        for word in words:
            if word.lower() in northern_dialect:
                northern_count += 1
            if word.lower() in southern_dialect:
                southern_count += 1
            if word.lower() in central_dialect:
                central_count += 1

        # Tính tổng số từ phương ngữ
        total_dialect_words = northern_count + southern_count + central_count

        if total_dialect_words == 0:
            return 1.0  # Không có từ phương ngữ nào

        # Tính độ nhất quán dựa trên phương ngữ chiếm ưu thế
        max_dialect_count = max(northern_count, southern_count, central_count)
        consistency = max_dialect_count / total_dialect_words

        return consistency

    def calculate_reasoning_quality(self, text: str) -> float:
        """
        Tính toán chất lượng lập luận trong văn bản tiếng Việt.

        Args:
            text: Văn bản tiếng Việt cần đánh giá

        Returns:
            Điểm đánh giá từ 0.0 đến 1.0
        """
        if not text or not detect_vietnamese(text):
            return 0.0  # Không phải tiếng Việt hoặc văn bản trống

        # Chuẩn hóa văn bản
        text = normalize_vietnamese_text(text)

        # Đếm số từ chỉ thị lập luận
        reasoning_marker_count = 0
        for marker in self.vietnamese_reasoning_markers:
            reasoning_marker_count += text.count(marker)

        # Đếm số từ chỉ thị trích dẫn
        citation_marker_count = 0
        for marker in self.vietnamese_citation_markers:
            citation_marker_count += text.count(marker)

        # Đếm số từ chỉ thị kết luận
        conclusion_marker_count = 0
        for marker in self.vietnamese_conclusion_markers:
            conclusion_marker_count += text.count(marker)

        # Tính tổng số từ
        word_count = len(text.split())

        if word_count < 10:
            return 0.0  # Văn bản quá ngắn

        # Tính điểm dựa trên tỷ lệ từ chỉ thị lập luận, trích dẫn và kết luận
        reasoning_score = min(1.0, reasoning_marker_count / (word_count / 50))
        citation_score = min(1.0, citation_marker_count / (word_count / 100))
        conclusion_score = min(1.0, conclusion_marker_count / (word_count / 200))

        # Tính điểm tổng hợp
        overall_score = (reasoning_score * 0.5) + (citation_score * 0.3) + (conclusion_score * 0.2)

        return overall_score

    def calculate_lexical_diversity(self, text: str) -> float:
        """
        Tính toán độ đa dạng từ vựng trong văn bản tiếng Việt.

        Args:
            text: Văn bản tiếng Việt cần đánh giá

        Returns:
            Điểm đánh giá từ 0.0 đến 1.0
        """
        if not text or not detect_vietnamese(text):
            return 0.0  # Không phải tiếng Việt hoặc văn bản trống

        # Chuẩn hóa văn bản
        text = normalize_vietnamese_text(text)

        # Tách thành các từ
        words = text.split()

        if len(words) == 0:
            return 0.0

        # Tính tỷ lệ từ khác nhau
        unique_words = set(words)
        diversity = len(unique_words) / len(words)

        # Áp dụng hàm logarit để chuẩn hóa kết quả
        # Văn bản dài hơn sẽ có xu hướng có tỷ lệ từ khác nhau thấp hơn
        if len(words) > 100:
            diversity = diversity * (1 + math.log10(len(words) / 100) * 0.1)

        return min(1.0, diversity)

    def calculate_academic_language_usage(self, text: str) -> float:
        """
        Tính toán mức độ sử dụng ngôn ngữ học thuật trong văn bản tiếng Việt.

        Args:
            text: Văn bản tiếng Việt cần đánh giá

        Returns:
            Điểm đánh giá từ 0.0 đến 1.0
        """
        if not text or not detect_vietnamese(text):
            return 0.0  # Không phải tiếng Việt hoặc văn bản trống

        # Chuẩn hóa văn bản
        text = normalize_vietnamese_text(text)

        # Đếm số từ ngữ học thuật
        academic_term_count = 0
        for term in self.vietnamese_academic_terms:
            academic_term_count += text.lower().count(term.lower())

        # Đếm số từ ngữ khoa học
        scientific_term_count = 0
        for term in self.vietnamese_scientific_terms:
            scientific_term_count += text.lower().count(term.lower())

        # Tính tổng số từ
        word_count = len(text.split())

        if word_count < 10:
            return 0.0  # Văn bản quá ngắn

        # Tính điểm dựa trên tỷ lệ từ ngữ học thuật và khoa học
        academic_score = min(1.0, academic_term_count / (word_count / 30))
        scientific_score = min(1.0, scientific_term_count / (word_count / 50))

        # Tính điểm tổng hợp
        overall_score = (academic_score * 0.7) + (scientific_score * 0.3)

        return overall_score

    def calculate_coherence(self, text: str) -> float:
        """
        Tính toán độ mạch lạc của văn bản tiếng Việt.

        Args:
            text: Văn bản tiếng Việt cần đánh giá

        Returns:
            Điểm đánh giá từ 0.0 đến 1.0
        """
        if not text or not detect_vietnamese(text):
            return 0.0  # Không phải tiếng Việt hoặc văn bản trống

        # Chuẩn hóa văn bản
        text = normalize_vietnamese_text(text)

        # Tách thành các đoạn
        paragraphs = text.split('\n\n')
        if len(paragraphs) <= 1:
            paragraphs = [text]  # Nếu không có đoạn, coi toàn bộ văn bản là một đoạn

        # Đếm số từ chỉ thị liên kết giữa các đoạn
        discourse_marker_count = 0
        for marker in self.vietnamese_discourse_markers:
            discourse_marker_count += text.lower().count(marker.lower())

        # Tính điểm dựa trên số từ chỉ thị liên kết và số đoạn
        expected_markers = max(1, len(paragraphs) - 1)  # Mong đợi ít nhất một từ chỉ thị cho mỗi liên kết đoạn
        coherence_score = min(1.0, discourse_marker_count / expected_markers)

        # Nếu chỉ có một đoạn, đánh giá mạch lạc dựa trên cấu trúc câu
        if len(paragraphs) <= 1:
            # Tách thành các câu
            sentences = re.split(r'[.!?]+', text)
            sentences = [s.strip() for s in sentences if s.strip()]

            if len(sentences) <= 1:
                return 0.5  # Chỉ có một câu, khó đánh giá mạch lạc

            # Đếm số từ chỉ thị liên kết giữa các câu
            sentence_marker_count = 0
            for i, sentence in enumerate(sentences):
                if i > 0:  # Bỏ qua câu đầu tiên
                    for marker in self.vietnamese_discourse_markers:
                        if sentence.lower().startswith(marker.lower()):
                            sentence_marker_count += 1
                            break

            # Tính điểm dựa trên số từ chỉ thị liên kết và số câu
            expected_sentence_markers = max(1, len(sentences) - 1) / 2  # Mong đợi khoảng một nửa số câu có từ chỉ thị
            sentence_coherence_score = min(1.0, sentence_marker_count / expected_sentence_markers)

            # Kết hợp với điểm trước đó
            coherence_score = (coherence_score + sentence_coherence_score) / 2

        return coherence_score

    def calculate_grammatical_correctness(self, text: str) -> float:
        """
        Tính toán độ chính xác ngữ pháp của văn bản tiếng Việt.

        Args:
            text: Văn bản tiếng Việt cần đánh giá

        Returns:
            Điểm đánh giá từ 0.0 đến 1.0
        """
        if not text or not detect_vietnamese(text):
            return 0.0  # Không phải tiếng Việt hoặc văn bản trống

        # Chuẩn hóa văn bản
        text = normalize_vietnamese_text(text)

        # Nếu underthesea không khả dụng, sử dụng phương pháp đơn giản hơn
        if not UNDERTHESEA_AVAILABLE:
            # Kiểm tra dấu câu cơ bản
            sentences = re.split(r'[.!?]+', text)
            sentences = [s.strip() for s in sentences if s.strip()]

            if len(sentences) == 0:
                return 0.0

            # Kiểm tra xem mỗi câu có bắt đầu bằng chữ hoa không
            capitalization_score = sum(1 for s in sentences if s and s[0].isupper()) / len(sentences)

            # Kiểm tra xem văn bản có kết thúc bằng dấu câu không
            punctuation_score = 1.0 if text.rstrip()[-1] in '.!?' else 0.0

            # Tính điểm tổng hợp
            return (capitalization_score * 0.7) + (punctuation_score * 0.3)

        # Sử dụng underthesea để phân tích cú pháp
        try:
            # Tách thành các câu
            sentences = underthesea.sent_tokenize(text)

            if len(sentences) == 0:
                return 0.0

            # Phân tích từ loại cho mỗi câu
            pos_tags_list = [underthesea.pos_tag(sentence) for sentence in sentences]

            # Kiểm tra các lỗi ngữ pháp cơ bản
            error_count = 0
            total_checks = 0

            for pos_tags in pos_tags_list:
                # Kiểm tra cấu trúc chủ-vị
                has_noun = any(tag[1].startswith('N') for tag in pos_tags)
                has_verb = any(tag[1].startswith('V') for tag in pos_tags)

                if not (has_noun and has_verb):
                    error_count += 1

                total_checks += 1

            # Tính điểm dựa trên tỷ lệ lỗi
            if total_checks == 0:
                return 0.5  # Không đủ thông tin để đánh giá

            correctness_score = 1.0 - (error_count / total_checks)

            return correctness_score

        except Exception as e:
            logger.error(f"Lỗi khi phân tích ngữ pháp: {str(e)}")
            return 0.5  # Giá trị mặc định khi có lỗi

    def calculate_vietnamese_metrics(self, text: str, reference_compounds: Optional[List[str]] = None) -> Dict[str, float]:
        """
        Tính toán tất cả các metrics đánh giá chất lượng tiếng Việt.

        Args:
            text: Văn bản tiếng Việt cần đánh giá
            reference_compounds: Danh sách các từ ghép tham chiếu

        Returns:
            Dictionary chứa các metrics đánh giá
        """
        metrics = {
            # Metrics cơ bản
            "diacritic_consistency": self.calculate_diacritic_consistency(text),
            "compound_word_accuracy": self.calculate_compound_word_accuracy(text, reference_compounds),
            "dialect_consistency": self.calculate_dialect_consistency(text),
            "reasoning_quality": self.calculate_reasoning_quality(text),

            # Metrics nâng cao
            "lexical_diversity": self.calculate_lexical_diversity(text),
            "academic_language_usage": self.calculate_academic_language_usage(text),
            "coherence": self.calculate_coherence(text),
            "grammatical_correctness": self.calculate_grammatical_correctness(text)
        }

        # Tính điểm tổng hợp
        metrics["overall_score"] = (
            metrics["diacritic_consistency"] * 0.15 +
            metrics["compound_word_accuracy"] * 0.10 +
            metrics["dialect_consistency"] * 0.10 +
            metrics["reasoning_quality"] * 0.20 +
            metrics["lexical_diversity"] * 0.15 +
            metrics["academic_language_usage"] * 0.10 +
            metrics["coherence"] * 0.10 +
            metrics["grammatical_correctness"] * 0.10
        )

        return metrics
