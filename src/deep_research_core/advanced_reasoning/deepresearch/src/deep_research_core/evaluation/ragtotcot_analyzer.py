"""
Phân tích hiệu suất cho RAG-TOT-COT.

Mo<PERSON>le này cung cấp các công cụ để phân tích hiệu suất của RAG-TOT-COT,
bao gồm đánh giá đóng góp của từng kỹ thuật và đề xuất cải tiến.
"""

import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import matplotlib.pyplot as plt
import io
import base64

from ..utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

class RAGTOTCOTAnalyzer:
    """
    Phân tích hiệu suất của RAG-TOT-COT.
    
    Lớp này cung cấp các phương thức để:
    1. <PERSON>ân tích đóng góp của từng kỹ thuật
    2. <PERSON><PERSON><PERSON> gi<PERSON> hiệu suất tổng thể
    3. <PERSON><PERSON> xuất cải tiến
    """
    
    def __init__(self, language: str = "vi", verbose: bool = False):
        """
        Khởi tạo bộ phân tích hiệu suất.
        
        Args:
            language: Ngôn ngữ để sử dụng (vi hoặc en)
            verbose: Có in thông tin chi tiết hay không
        """
        self.language = language
        self.verbose = verbose
        
        # Khởi tạo vectorizer
        self.vectorizer = TfidfVectorizer(ngram_range=(1, 2), max_features=1000)
        
        logger.info(f"Initialized RAGTOTCOTAnalyzer with language: {language}")
    
    def analyze_contribution(
        self,
        query: str,
        rag_result: Dict[str, Any],
        tot_result: Dict[str, Any],
        cot_result: Dict[str, Any],
        combined_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Phân tích đóng góp của từng kỹ thuật vào kết quả cuối cùng.
        
        Args:
            query: Truy vấn đã xử lý
            rag_result: Kết quả từ RAG
            tot_result: Kết quả từ TOT
            cot_result: Kết quả từ COT
            combined_result: Kết quả kết hợp
            
        Returns:
            Dict chứa phân tích đóng góp
        """
        # Trích xuất câu trả lời
        rag_answer = rag_result.get("answer", "")
        tot_answer = tot_result.get("answer", "")
        cot_answer = cot_result.get("answer", "")
        combined_answer = combined_result.get("answer", "")
        
        # Tính toán độ tương đồng giữa các câu trả lời
        answers = [rag_answer, tot_answer, cot_answer, combined_answer]
        
        # Fit vectorizer
        self.vectorizer.fit(answers)
        
        # Chuyển đổi câu trả lời thành vector
        answer_vectors = self.vectorizer.transform(answers)
        
        # Tính toán độ tương đồng
        similarities = cosine_similarity(answer_vectors)
        
        # Tính toán đóng góp dựa trên độ tương đồng với kết quả kết hợp
        rag_similarity = similarities[0, 3]  # Độ tương đồng giữa RAG và kết quả kết hợp
        tot_similarity = similarities[1, 3]  # Độ tương đồng giữa TOT và kết quả kết hợp
        cot_similarity = similarities[2, 3]  # Độ tương đồng giữa COT và kết quả kết hợp
        
        # Chuẩn hóa đóng góp
        total_similarity = rag_similarity + tot_similarity + cot_similarity
        if total_similarity > 0:
            contributions = {
                "rag": rag_similarity / total_similarity,
                "tot": tot_similarity / total_similarity,
                "cot": cot_similarity / total_similarity
            }
        else:
            contributions = {"rag": 0.33, "tot": 0.33, "cot": 0.34}
        
        # Tính toán độ tương đồng giữa các kỹ thuật
        technique_similarities = {
            "rag_tot": similarities[0, 1],
            "rag_cot": similarities[0, 2],
            "tot_cot": similarities[1, 2]
        }
        
        # Tính toán độ dài câu trả lời
        answer_lengths = {
            "rag": len(rag_answer),
            "tot": len(tot_answer),
            "cot": len(cot_answer),
            "combined": len(combined_answer)
        }
        
        # Tính toán thời gian xử lý
        latencies = {
            "rag": rag_result.get("latency", 0),
            "tot": tot_result.get("latency", 0),
            "cot": cot_result.get("latency", 0),
            "combined": combined_result.get("total_latency", 0)
        }
        
        # Tạo kết quả phân tích
        analysis = {
            "query": query,
            "contributions": contributions,
            "technique_similarities": technique_similarities,
            "answer_lengths": answer_lengths,
            "latencies": latencies
        }
        
        if self.verbose:
            logger.info(f"Contribution analysis: {analysis}")
        
        return analysis
    
    def evaluate_performance(
        self,
        analysis: Dict[str, Any],
        expected_answer: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Đánh giá hiệu suất tổng thể.
        
        Args:
            analysis: Kết quả phân tích từ analyze_contribution
            expected_answer: Câu trả lời mong đợi (nếu có)
            
        Returns:
            Dict chứa đánh giá hiệu suất
        """
        # Trích xuất thông tin từ phân tích
        contributions = analysis.get("contributions", {})
        technique_similarities = analysis.get("technique_similarities", {})
        answer_lengths = analysis.get("answer_lengths", {})
        latencies = analysis.get("latencies", {})
        
        # Tính toán điểm cân bằng
        balance_score = 1.0 - np.std(list(contributions.values()))
        
        # Tính toán điểm hiệu quả thời gian
        if latencies.get("combined", 0) > 0:
            time_efficiency = (latencies.get("rag", 0) + latencies.get("tot", 0) + latencies.get("cot", 0)) / (3 * latencies.get("combined", 1))
        else:
            time_efficiency = 1.0
        
        # Tính toán điểm đồng thuận
        consensus_score = np.mean(list(technique_similarities.values()))
        
        # Tính toán điểm tổng hợp
        composite_score = (balance_score + time_efficiency + consensus_score) / 3
        
        # Tạo đánh giá hiệu suất
        evaluation = {
            "balance_score": balance_score,
            "time_efficiency": time_efficiency,
            "consensus_score": consensus_score,
            "composite_score": composite_score
        }
        
        # Đánh giá với câu trả lời mong đợi (nếu có)
        if expected_answer:
            # Tính toán độ tương đồng với câu trả lời mong đợi
            answers = [
                analysis.get("rag_answer", ""),
                analysis.get("tot_answer", ""),
                analysis.get("cot_answer", ""),
                analysis.get("combined_answer", ""),
                expected_answer
            ]
            
            # Fit vectorizer
            self.vectorizer.fit(answers)
            
            # Chuyển đổi câu trả lời thành vector
            answer_vectors = self.vectorizer.transform(answers)
            
            # Tính toán độ tương đồng
            similarities = cosine_similarity(answer_vectors)
            
            # Tính toán độ tương đồng với câu trả lời mong đợi
            accuracy_scores = {
                "rag": similarities[0, 4],
                "tot": similarities[1, 4],
                "cot": similarities[2, 4],
                "combined": similarities[3, 4]
            }
            
            # Thêm vào đánh giá
            evaluation["accuracy_scores"] = accuracy_scores
            evaluation["accuracy"] = accuracy_scores.get("combined", 0)
        
        if self.verbose:
            logger.info(f"Performance evaluation: {evaluation}")
        
        return evaluation
    
    def suggest_improvements(
        self,
        analysis: Dict[str, Any],
        evaluation: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Đề xuất cải tiến dựa trên phân tích và đánh giá.
        
        Args:
            analysis: Kết quả phân tích từ analyze_contribution
            evaluation: Kết quả đánh giá từ evaluate_performance
            
        Returns:
            Dict chứa đề xuất cải tiến
        """
        # Trích xuất thông tin từ phân tích và đánh giá
        contributions = analysis.get("contributions", {})
        technique_similarities = analysis.get("technique_similarities", {})
        balance_score = evaluation.get("balance_score", 0)
        consensus_score = evaluation.get("consensus_score", 0)
        
        # Khởi tạo danh sách đề xuất
        suggestions = []
        
        # Đề xuất dựa trên cân bằng đóng góp
        if balance_score < 0.7:
            # Tìm kỹ thuật có đóng góp thấp nhất và cao nhất
            min_technique = min(contributions, key=contributions.get)
            max_technique = max(contributions, key=contributions.get)
            
            suggestions.append(f"Tăng trọng số cho {min_technique} và giảm trọng số cho {max_technique} để cân bằng đóng góp")
        
        # Đề xuất dựa trên đồng thuận
        if consensus_score < 0.5:
            # Tìm cặp kỹ thuật có độ tương đồng thấp nhất
            min_similarity_pair = min(technique_similarities, key=technique_similarities.get)
            techniques = min_similarity_pair.split("_")
            
            suggestions.append(f"Cải thiện sự kết hợp giữa {techniques[0]} và {techniques[1]} để tăng đồng thuận")
        
        # Đề xuất dựa trên hiệu quả thời gian
        latencies = analysis.get("latencies", {})
        if latencies.get("combined", 0) > 1.5 * (latencies.get("rag", 0) + latencies.get("tot", 0) + latencies.get("cot", 0)) / 3:
            suggestions.append("Tối ưu hóa quá trình kết hợp để giảm thời gian xử lý")
        
        # Đề xuất dựa trên độ chính xác (nếu có)
        if "accuracy_scores" in evaluation:
            accuracy_scores = evaluation.get("accuracy_scores", {})
            min_accuracy = min(accuracy_scores, key=accuracy_scores.get)
            
            if min_accuracy == "combined":
                suggestions.append("Cải thiện chiến lược kết hợp để tăng độ chính xác")
            else:
                suggestions.append(f"Cải thiện hiệu suất của {min_accuracy} để tăng độ chính xác tổng thể")
        
        # Tạo đề xuất cải tiến
        improvements = {
            "suggestions": suggestions,
            "recommended_weights": self._recommend_weights(contributions, evaluation)
        }
        
        if self.verbose:
            logger.info(f"Improvement suggestions: {improvements}")
        
        return improvements
    
    def _recommend_weights(
        self,
        contributions: Dict[str, float],
        evaluation: Dict[str, Any]
    ) -> Dict[str, float]:
        """
        Đề xuất trọng số dựa trên đóng góp và đánh giá.
        
        Args:
            contributions: Đóng góp của từng kỹ thuật
            evaluation: Kết quả đánh giá
            
        Returns:
            Dict chứa trọng số đề xuất
        """
        # Trích xuất thông tin
        balance_score = evaluation.get("balance_score", 0)
        
        # Nếu cân bằng đã tốt, giữ nguyên trọng số
        if balance_score > 0.8:
            return contributions
        
        # Tính toán trọng số mới
        total = sum(contributions.values())
        avg_contribution = total / len(contributions)
        
        # Điều chỉnh trọng số để cân bằng hơn
        recommended_weights = {}
        for technique, contribution in contributions.items():
            # Điều chỉnh về phía trung bình
            adjustment = 0.3 * (avg_contribution - contribution)
            recommended_weights[technique] = contribution + adjustment
        
        # Chuẩn hóa trọng số
        total_weight = sum(recommended_weights.values())
        recommended_weights = {k: v / total_weight for k, v in recommended_weights.items()}
        
        return recommended_weights
    
    def generate_visualization(self, analysis: Dict[str, Any]) -> str:
        """
        Tạo trực quan hóa cho phân tích.
        
        Args:
            analysis: Kết quả phân tích từ analyze_contribution
            
        Returns:
            Chuỗi base64 của hình ảnh trực quan hóa
        """
        # Trích xuất thông tin từ phân tích
        contributions = analysis.get("contributions", {})
        latencies = analysis.get("latencies", {})
        
        # Tạo hình
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # Biểu đồ đóng góp
        techniques = list(contributions.keys())
        contribution_values = [contributions[t] for t in techniques]
        ax1.bar(techniques, contribution_values, color=['blue', 'green', 'red'])
        ax1.set_title('Đóng góp của từng kỹ thuật')
        ax1.set_ylim(0, 1)
        
        # Biểu đồ thời gian xử lý
        technique_latencies = [latencies.get(t, 0) for t in techniques + ['combined']]
        ax2.bar(techniques + ['combined'], technique_latencies, color=['blue', 'green', 'red', 'purple'])
        ax2.set_title('Thời gian xử lý (giây)')
        
        # Điều chỉnh bố cục
        plt.tight_layout()
        
        # Chuyển đổi hình thành chuỗi base64
        buf = io.BytesIO()
        plt.savefig(buf, format='png')
        buf.seek(0)
        img_str = base64.b64encode(buf.read()).decode('utf-8')
        plt.close()
        
        return img_str
