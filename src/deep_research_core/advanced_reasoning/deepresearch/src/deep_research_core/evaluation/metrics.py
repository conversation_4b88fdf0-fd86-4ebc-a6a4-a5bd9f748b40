"""
Evaluation metrics for Deep Research Core.
"""

import re
import logging
from typing import Dict, Any, <PERSON>, Set, Tuple, Optional, Union

logger = logging.getLogger(__name__)

def calculate_source_recall(
    response: Dict[str, Any],
    expected_sources: List[str]
) -> float:
    """
    Calculate the source recall metric.
    
    Args:
        response: Response from the model
        expected_sources: List of expected sources
        
    Returns:
        Source recall score (0.0 to 1.0)
    """
    if not expected_sources:
        return 1.0
    
    # Get the documents from the response
    documents = response.get("documents", [])
    
    # Extract sources from the documents
    sources = set()
    for doc in documents:
        metadata = doc.get("metadata", {})
        source = metadata.get("source", "")
        if source:
            sources.add(source)
        
        # Also check for title as some sources might be in the title
        title = metadata.get("title", "")
        if title:
            sources.add(title)
    
    # Count how many expected sources were retrieved
    found_sources = 0
    for expected_source in expected_sources:
        for source in sources:
            if expected_source.lower() in source.lower():
                found_sources += 1
                break
    
    # Calculate recall
    recall = found_sources / len(expected_sources) if expected_sources else 0.0
    
    return recall

def calculate_keyword_presence(
    response: Dict[str, Any],
    expected_keywords: List[str]
) -> float:
    """
    Calculate the keyword presence metric.
    
    Args:
        response: Response from the model
        expected_keywords: List of expected keywords
        
    Returns:
        Keyword presence score (0.0 to 1.0)
    """
    if not expected_keywords:
        return 1.0
    
    # Get the reasoning or answer from the response
    reasoning = response.get("reasoning", "")
    answer = response.get("answer", "")
    
    # Combine reasoning and answer
    text = reasoning + " " + answer
    text = text.lower()
    
    # Count how many expected keywords are present
    found_keywords = 0
    for keyword in expected_keywords:
        if keyword.lower() in text:
            found_keywords += 1
    
    # Calculate keyword presence
    presence = found_keywords / len(expected_keywords) if expected_keywords else 0.0
    
    return presence

def calculate_reasoning_steps(response: Dict[str, Any]) -> int:
    """
    Calculate the number of reasoning steps.
    
    Args:
        response: Response from the model
        
    Returns:
        Number of reasoning steps
    """
    # Get the reasoning from the response
    reasoning = response.get("reasoning", "")
    
    # Define patterns for reasoning steps
    patterns = [
        r"(?:First|1\.)",
        r"(?:Second|2\.)",
        r"(?:Third|3\.)",
        r"(?:Fourth|4\.)",
        r"(?:Fifth|5\.)",
        r"(?:Sixth|6\.)",
        r"(?:Seventh|7\.)",
        r"(?:Eighth|8\.)",
        r"(?:Ninth|9\.)",
        r"(?:Tenth|10\.)",
        r"(?:Next|Then)",
        r"(?:Additionally|Furthermore|Moreover)",
        r"(?:Finally|In conclusion|To summarize)"
    ]
    
    # Count the number of reasoning steps
    steps = 0
    for pattern in patterns:
        steps += len(re.findall(pattern, reasoning, re.IGNORECASE))
    
    return steps

def calculate_citation_count(response: Dict[str, Any]) -> int:
    """
    Calculate the number of citations.
    
    Args:
        response: Response from the model
        
    Returns:
        Number of citations
    """
    # Get the reasoning or answer from the response
    reasoning = response.get("reasoning", "")
    answer = response.get("answer", "")
    
    # Combine reasoning and answer
    text = reasoning + " " + answer
    
    # Define patterns for citations
    patterns = [
        r"(?:Document \d+)",
        r"(?:Source \d+)",
        r"(?:Reference \d+)",
        r"(?:According to .*?document)",
        r"(?:As stated in .*?document)",
        r"(?:As mentioned in .*?document)",
        r"(?:Based on .*?document)",
        r"(?:From .*?document)"
    ]
    
    # Count the number of citations
    citations = 0
    for pattern in patterns:
        citations += len(re.findall(pattern, text, re.IGNORECASE))
    
    return citations

def calculate_response_length(response: Dict[str, Any]) -> int:
    """
    Calculate the length of the response.
    
    Args:
        response: Response from the model
        
    Returns:
        Length of the response in characters
    """
    # Get the reasoning or answer from the response
    reasoning = response.get("reasoning", "")
    answer = response.get("answer", "")
    
    # Combine reasoning and answer
    text = reasoning + " " + answer
    
    return len(text)

def calculate_metrics(
    response: Dict[str, Any],
    expected_sources: List[str],
    expected_keywords: List[str]
) -> Dict[str, float]:
    """
    Calculate all metrics for a response.
    
    Args:
        response: Response from the model
        expected_sources: List of expected sources
        expected_keywords: List of expected keywords
        
    Returns:
        Dictionary containing all metrics
    """
    metrics = {
        "source_recall": calculate_source_recall(response, expected_sources),
        "keyword_presence": calculate_keyword_presence(response, expected_keywords),
        "reasoning_steps": calculate_reasoning_steps(response),
        "citation_count": calculate_citation_count(response),
        "response_length": calculate_response_length(response)
    }
    
    return metrics
