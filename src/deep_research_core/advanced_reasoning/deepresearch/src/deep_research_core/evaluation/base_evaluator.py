"""
Base Evaluator.

<PERSON><PERSON>le này định nghĩa lớp cơ sở cho các công cụ đánh giá trong hệ thống.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional

class BaseEvaluator(ABC):
    """
    Lớ<PERSON> cơ sở cho các công cụ đánh giá.
    
    Lớp này định nghĩa giao diện chung cho tất cả các công cụ đánh giá trong hệ thống.
    """
    
    def __init__(self, verbose: bool = False):
        """
        Khởi tạo BaseEvaluator.
        
        Args:
            verbose: Bật chế độ verbose
        """
        self.verbose = verbose
    
    @abstractmethod
    def evaluate(self, response: str, **kwargs) -> Dict[str, Any]:
        """
        Đánh giá chất lượng phản hồi.
        
        Args:
            response: <PERSON><PERSON><PERSON> hồi cần đánh giá
            **kwargs: <PERSON><PERSON><PERSON> tham số bổ sung
            
        Returns:
            Dictionary chứa các metrics đánh giá
        """
        pass
