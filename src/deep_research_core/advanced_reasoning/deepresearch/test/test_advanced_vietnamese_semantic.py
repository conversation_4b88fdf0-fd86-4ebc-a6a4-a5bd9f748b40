"""
Tests for advanced Vietnamese semantic processing module.
"""

import unittest
from unittest.mock import patch, MagicMock
import sys
from typing import List, Dict, Any

sys.path.append("src")
from deep_research_core.multilingual.advanced_vietnamese_semantic import (
    AdvancedVietnameseSemanticProcessor,
    VietnameseQueryContext
)

class TestAdvancedVietnameseSemanticProcessor(unittest.TestCase):
    """Test cases for AdvancedVietnameseSemanticProcessor class."""

    def setUp(self):
        """Set up test environment."""
        # Khởi tạo processor với embedding gi<PERSON> lập để tr<PERSON>h phụ thuộc vào mô hình thực
        with patch("deep_research_core.multilingual.advanced_vietnamese_semantic.EnhancedVietnameseEmbeddings"):
            with patch("deep_research_core.multilingual.advanced_vietnamese_semantic.VietnamesePromptOptimizer"):
                self.processor = AdvancedVietnameseSemanticProcessor(
                    use_advanced_embeddings=False
                )
                # Mockup detect_domain
                self.processor.prompt_optimizer = MagicMock()
                self.processor.prompt_optimizer.detect_domain.return_value = "cong_nghe"

    def test_init(self):
        """Test initialization."""
        self.assertIsNotNone(self.processor)
        self.assertEqual(self.processor.max_variations, 3)
        self.assertFalse(self.processor.use_advanced_embeddings)

    def test_process_query(self):
        """Test process_query method."""
        with patch("deep_research_core.multilingual.advanced_vietnamese_semantic.normalize_vietnamese_text") as mock_normalize:
            with patch("deep_research_core.multilingual.advanced_vietnamese_semantic.detect_vietnamese_dialect") as mock_dialect:
                with patch("deep_research_core.multilingual.advanced_vietnamese_semantic.extract_vietnamese_keywords") as mock_keywords:
                    # Set up mocks
                    mock_normalize.return_value = "trí tuệ nhân tạo"
                    mock_dialect.return_value = "northern"
                    mock_keywords.return_value = ["trí tuệ", "nhân tạo", "AI"]
                    
                    # Process a query
                    query_context = self.processor.process_query("Trí tuệ nhân tạo là gì?")
                    
                    # Verify results
                    self.assertEqual(query_context.original_query, "Trí tuệ nhân tạo là gì?")
                    self.assertEqual(query_context.normalized_query, "trí tuệ nhân tạo")
                    self.assertEqual(query_context.dialect, "northern")
                    self.assertEqual(query_context.domain, "cong_nghe")
                    self.assertEqual(query_context.keywords, ["trí tuệ", "nhân tạo", "AI"])
                    self.assertGreater(len(query_context.expanded_keywords), 3)
                    self.assertGreater(len(query_context.semantic_variations), 0)

    def test_expand_keywords(self):
        """Test _expand_keywords method."""
        keywords = ["trí tuệ", "nhân tạo"]
        domain = "cong_nghe"
        expanded = self.processor._expand_keywords(keywords, domain)
        
        # Verify that original keywords are preserved
        for keyword in keywords:
            self.assertIn(keyword, expanded)
        
        # Verify that domain terms are added
        for term in self.processor.DOMAIN_EXPANSION_TERMS[domain][:5]:  # Check first 5 terms
            if term not in keywords:  # Avoid checking original keywords again
                self.assertIn(term, expanded)

    def test_generate_query_variations(self):
        """Test _generate_query_variations method."""
        query = "trí tuệ nhân tạo"
        keywords = ["trí tuệ", "nhân tạo", "AI"]
        expanded_keywords = ["trí tuệ", "nhân tạo", "AI", "công nghệ", "phần mềm", "thuật toán"]
        
        variations = self.processor._generate_query_variations(query, keywords, expanded_keywords)
        
        # Verify number of variations
        self.assertLessEqual(len(variations), self.processor.max_variations)
        
        # Verify variations contain original query
        for variation in variations:
            self.assertIn(query, variation)

    def test_optimize_search_query(self):
        """Test optimize_search_query method."""
        with patch("deep_research_core.multilingual.advanced_vietnamese_semantic.detect_vietnamese") as mock_detect:
            mock_detect.return_value = True
            
            with patch.object(self.processor, "process_query") as mock_process:
                # Create mock query context
                mock_context = VietnameseQueryContext(original_query="Trí tuệ nhân tạo là gì?")
                mock_context.normalized_query = "trí tuệ nhân tạo"
                mock_context.keywords = ["trí tuệ", "nhân tạo", "AI"]
                mock_process.return_value = mock_context
                
                # Test optimization
                optimized = self.processor.optimize_search_query("Trí tuệ nhân tạo là gì?")
                
                # Verify result
                self.assertEqual(optimized, "trí tuệ nhân tạo trí tuệ nhân tạo")

    def test_non_vietnamese_query(self):
        """Test handling of non-Vietnamese queries."""
        with patch("deep_research_core.multilingual.advanced_vietnamese_semantic.detect_vietnamese") as mock_detect:
            mock_detect.return_value = False
            
            # Test optimization of non-Vietnamese query
            query = "Artificial Intelligence"
            optimized = self.processor.optimize_search_query(query)
            
            # Verify original query is returned unchanged
            self.assertEqual(optimized, query)
            
            # Test alternative query generation
            alternatives = self.processor.generate_alternative_queries(query)
            
            # Verify only original query is returned
            self.assertEqual(len(alternatives), 1)
            self.assertEqual(alternatives[0], query)

    def test_generate_alternative_queries(self):
        """Test generate_alternative_queries method."""
        with patch("deep_research_core.multilingual.advanced_vietnamese_semantic.detect_vietnamese") as mock_detect:
            mock_detect.return_value = True
            
            with patch.object(self.processor, "process_query") as mock_process:
                # Create mock query context
                mock_context = VietnameseQueryContext(original_query="Trí tuệ nhân tạo là gì?")
                mock_context.normalized_query = "trí tuệ nhân tạo"
                mock_context.keywords = ["trí tuệ", "nhân tạo", "AI"]
                mock_context.expanded_keywords = ["trí tuệ", "nhân tạo", "AI", "công nghệ", "phần mềm"]
                mock_context.semantic_variations = [
                    "trí tuệ nhân tạo trí tuệ nhân tạo AI",
                    "trí tuệ nhân tạo công nghệ phần mềm"
                ]
                mock_process.return_value = mock_context
                
                # Test alternative generation
                alternatives = self.processor.generate_alternative_queries("Trí tuệ nhân tạo là gì?", max_alternatives=3)
                
                # Verify results
                self.assertEqual(len(alternatives), 2)  # Should match mock semantic_variations
                self.assertEqual(alternatives[0], "trí tuệ nhân tạo trí tuệ nhân tạo AI")

    def test_rank_search_results(self):
        """Test rank_search_results method."""
        with patch("deep_research_core.multilingual.advanced_vietnamese_semantic.detect_vietnamese") as mock_detect:
            mock_detect.return_value = True
            
            with patch.object(self.processor, "process_query") as mock_process:
                # Create mock query context
                mock_context = VietnameseQueryContext(original_query="Trí tuệ nhân tạo")
                mock_context.normalized_query = "trí tuệ nhân tạo"
                mock_context.keywords = ["trí tuệ", "nhân tạo"]
                mock_context.expanded_keywords = ["trí tuệ", "nhân tạo", "AI", "máy học"]
                mock_process.return_value = mock_context
                
                with patch("deep_research_core.multilingual.advanced_vietnamese_semantic.semantic_similarity") as mock_sim:
                    # Set up mock similarity scores
                    mock_sim.side_effect = [0.9, 0.6, 0.7]  # For three results
                    
                    # Test result ranking
                    results = [
                        {"title": "Trí tuệ nhân tạo là gì", "snippet": "Giải thích về AI và ứng dụng"},
                        {"title": "Lịch sử máy tính", "snippet": "Không liên quan đến AI"},
                        {"title": "Machine Learning", "snippet": "Có đề cập đến trí tuệ nhân tạo"}
                    ]
                    
                    ranked = self.processor.rank_search_results(
                        "Trí tuệ nhân tạo",
                        results,
                        use_embeddings=False
                    )
                    
                    # Verify ranking order
                    self.assertEqual(ranked[0]["title"], "Trí tuệ nhân tạo là gì")  # Highest score
                    self.assertEqual(ranked[1]["title"], "Machine Learning")  # Middle score
                    self.assertEqual(ranked[2]["title"], "Lịch sử máy tính")  # Lowest score
                    
                    # Verify semantic scores are added
                    self.assertIn("semantic_score", ranked[0])
                    self.assertIn("semantic_score", ranked[1])
                    self.assertIn("semantic_score", ranked[2])

    def test_calculate_keyword_score(self):
        """Test _calculate_keyword_score method."""
        content = "Trí tuệ nhân tạo (AI) là công nghệ giúp máy tính học hỏi và suy luận như con người"
        keywords = ["trí tuệ", "nhân tạo", "machine learning"]
        expanded_keywords = ["trí tuệ", "nhân tạo", "AI", "máy học", "deep learning", "neural network"]
        
        score = self.processor._calculate_keyword_score(content, keywords, expanded_keywords)
        
        # Verify score is between 0 and 1
        self.assertGreaterEqual(score, 0)
        self.assertLessEqual(score, 1)
        
        # Content has 2/3 primary keywords and 1/3 expanded keywords
        # Using weights 0.7 for primary and 0.3 for expanded: (0.7 * 2/3) + (0.3 * 1/3) = 0.567
        self.assertAlmostEqual(score, 0.567, places=2)

    def test_enrich_search_results(self):
        """Test enrich_search_results method."""
        with patch("deep_research_core.multilingual.advanced_vietnamese_semantic.detect_vietnamese") as mock_detect:
            with patch("deep_research_core.multilingual.advanced_vietnamese_semantic.detect_vietnamese_dialect") as mock_dialect:
                with patch("deep_research_core.multilingual.advanced_vietnamese_semantic.extract_vietnamese_keywords") as mock_keywords:
                    # Set up mocks
                    mock_detect.return_value = True
                    mock_dialect.return_value = "northern"
                    mock_keywords.return_value = ["trí tuệ", "nhân tạo", "công nghệ", "tương lai", "ứng dụng"]
                    
                    # Test enrichment
                    results = [
                        {
                            "title": "Trí tuệ nhân tạo là gì",
                            "snippet": "Trí tuệ nhân tạo (AI) là lĩnh vực nghiên cứu về cách máy tính có thể mô phỏng trí thông minh con người."
                        }
                    ]
                    
                    enriched = self.processor.enrich_search_results(results)
                    
                    # Verify enriched result
                    self.assertTrue(enriched[0]["is_vietnamese"])
                    self.assertEqual(enriched[0]["dialect"], "northern")
                    self.assertEqual(len(enriched[0]["content_keywords"]), 5)
                    self.assertIn("trí tuệ", enriched[0]["content_keywords"])


if __name__ == "__main__":
    unittest.main() 