"""
Tests for RL-Tuning Model Paradigm with Vietnamese support.
"""

import unittest
from unittest.mock import MagicMock, patch
import torch
import os
import tempfile
import shutil

from deep_research_core.rl_tuning.model_paradigm.rl_model_paradigm import RLModelParadigm


class TestRLModelParadigmVietnamese(unittest.TestCase):
    """Test cases for RL-Tuning Model Paradigm with Vietnamese support."""

    def setUp(self):
        """Set up test environment."""
        # Create a temporary directory for model storage
        self.temp_dir = tempfile.mkdtemp()
        
        # Mock AutoModelForCausalLM and AutoTokenizer
        self.patch_model = patch('deep_research_core.rl_tuning.model_paradigm.rl_model_paradigm.AutoModelForCausalLM')
        self.mock_model_class = self.patch_model.start()
        self.mock_model = MagicMock()
        self.mock_model_class.from_pretrained.return_value = self.mock_model
        
        self.patch_tokenizer = patch('deep_research_core.rl_tuning.model_paradigm.rl_model_paradigm.AutoTokenizer')
        self.mock_tokenizer_class = self.patch_tokenizer.start()
        self.mock_tokenizer = MagicMock()
        self.mock_tokenizer.pad_token = None
        self.mock_tokenizer.eos_token = "<eos>"
        self.mock_tokenizer_class.from_pretrained.return_value = self.mock_tokenizer
        
        # Mock Vietnamese utilities
        self.patch_get_tokens = patch('deep_research_core.utils.vietnamese_utils.get_vietnamese_special_tokens')
        self.mock_get_tokens = self.patch_get_tokens.start()
        self.mock_get_tokens.return_value = ["<vi>", "</vi>", "<northern>", "</northern>"]
        
        # Standard config
        self.standard_config = {
            "learning_rate": 1e-5,
            "batch_size": 4,
            "num_epochs": 3,
            "device": "cpu" if not torch.cuda.is_available() else "cuda",
            "output_dir": os.path.join(self.temp_dir, "model_output")
        }
        
        # Vietnamese config
        self.vietnamese_config = {
            **self.standard_config,
            "use_vietnamese": True,
            "add_vietnamese_tokens": True,
            "revision": "vietnamese"
        }

    def tearDown(self):
        """Clean up after tests."""
        # Remove temporary directory
        shutil.rmtree(self.temp_dir)
        
        # Stop patches
        self.patch_model.stop()
        self.patch_tokenizer.stop()
        self.patch_get_tokens.stop()

    def test_standard_model_initialization(self):
        """Test standard model initialization without Vietnamese support."""
        # Initialize model paradigm with standard config
        model_paradigm = RLModelParadigm(
            model_id="test-model",
            config=self.standard_config
        )
        
        # Check that model was initialized correctly
        self.mock_model_class.from_pretrained.assert_called_once_with("test-model")
        self.mock_tokenizer_class.from_pretrained.assert_called_once_with("test-model")
        
        # Check that pad token was set
        self.assertEqual(self.mock_tokenizer.pad_token, "<eos>")
        
        # Check that model was moved to the correct device
        self.mock_model.to.assert_called_once_with(self.standard_config["device"])

    def test_vietnamese_model_initialization(self):
        """Test model initialization with Vietnamese support."""
        # Initialize model paradigm with Vietnamese config
        model_paradigm = RLModelParadigm(
            model_id="test-model",
            config=self.vietnamese_config
        )
        
        # Check that model was initialized with Vietnamese-specific parameters
        self.mock_model_class.from_pretrained.assert_called_once_with(
            "test-model",
            trust_remote_code=True,
            revision="vietnamese"
        )
        
        # Check that tokenizer was initialized with Vietnamese-specific parameters
        self.mock_tokenizer_class.from_pretrained.assert_called_once_with(
            "test-model",
            trust_remote_code=True,
            use_fast=True,
            revision="vietnamese"
        )
        
        # Check that Vietnamese special tokens were added
        self.mock_get_tokens.assert_called_once()
        self.mock_tokenizer.add_special_tokens.assert_called_once()
        
        # Check that token embeddings were resized
        self.mock_model.resize_token_embeddings.assert_called_once_with(len(self.mock_tokenizer))

    def test_vietnamese_model_without_special_tokens(self):
        """Test Vietnamese model initialization without adding special tokens."""
        # Modify config to not add Vietnamese tokens
        config = self.vietnamese_config.copy()
        config["add_vietnamese_tokens"] = False
        
        # Initialize model paradigm
        model_paradigm = RLModelParadigm(
            model_id="test-model",
            config=config
        )
        
        # Check that Vietnamese special tokens were not added
        self.mock_get_tokens.assert_not_called()
        self.mock_tokenizer.add_special_tokens.assert_not_called()
        self.mock_model.resize_token_embeddings.assert_not_called()

    def test_vietnamese_model_with_custom_revision(self):
        """Test Vietnamese model initialization with custom revision."""
        # Modify config to use custom revision
        config = self.vietnamese_config.copy()
        config["revision"] = "custom-vietnamese"
        
        # Initialize model paradigm
        model_paradigm = RLModelParadigm(
            model_id="test-model",
            config=config
        )
        
        # Check that model was initialized with custom revision
        self.mock_model_class.from_pretrained.assert_called_once_with(
            "test-model",
            trust_remote_code=True,
            revision="custom-vietnamese"
        )
        
        # Check that tokenizer was initialized with custom revision
        self.mock_tokenizer_class.from_pretrained.assert_called_once_with(
            "test-model",
            trust_remote_code=True,
            use_fast=True,
            revision="custom-vietnamese"
        )


if __name__ == "__main__":
    unittest.main()
