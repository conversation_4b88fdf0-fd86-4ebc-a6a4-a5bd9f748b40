"""
Test script for ReAct.

This script tests the functionality of the ReAct class.
"""

import os
import sys
import unittest
from unittest.mock import MagicMock, patch
import json

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.reasoning.react import ReAct, AdaptiveReAct
from src.deep_research_core.tools.base import BaseTool, register_tool

# Create a mock tool for testing
@register_tool
class MockTool(BaseTool):
    """Mock tool for testing."""

    name = "mock_tool"
    description = "A mock tool for testing"

    def run(self, input_text: str, **kwargs):
        """Run the mock tool."""
        return {
            "success": True,
            "input": input_text,
            "output": f"Processed: {input_text}"
        }

class TestReAct(unittest.TestCase):
    """Test case for ReAct."""

    def setUp(self):
        """Set up test fixtures."""
        # Create mock API provider
        self.mock_api_provider = MagicMock()

        # Set up mock responses
        self.mock_api_provider.generate.side_effect = [
            # First response: Action
            """
            Thought: I need to process the input text using the mock tool.
            Action: {
              "name": "mock_tool",
              "args": {
                "input_text": "test input"
              }
            }
            """,
            # Second response: Final answer
            """
            Thought: I have processed the input and got the result.
            Answer: The processed result is: Processed: test input
            """
        ]

        # Mock the _parse_response method to return expected values
        self.original_parse_response = ReAct._parse_response
        ReAct._parse_response = MagicMock(side_effect=[
            {"type": "action", "action": {"name": "mock_tool", "args": {"input_text": "test input"}}},
            {"type": "answer", "content": "The processed result is: Processed: test input"}
        ])

        # Create patches
        self.patches = [
            patch('src.deep_research_core.reasoning.react.openai_provider', self.mock_api_provider),
            patch('src.deep_research_core.reasoning.react.anthropic_provider', self.mock_api_provider),
            patch('src.deep_research_core.reasoning.react.openrouter_provider', self.mock_api_provider)
        ]

        # Start patches
        for p in self.patches:
            p.start()

        # Create mock tool
        self.mock_tool = MockTool()

        # Create ReAct instance
        self.react = ReAct(
            provider="openai",
            model="gpt-4o",
            temperature=0.7,
            max_tokens=2000,
            language="en",
            max_iterations=5,
            tools=[self.mock_tool],
            verbose=False
        )

    def tearDown(self):
        """Tear down test fixtures."""
        # Stop patches
        for p in self.patches:
            p.stop()

        # Restore original _parse_response method
        ReAct._parse_response = self.original_parse_response

    def test_reason(self):
        """Test the reason method."""
        # Perform reasoning
        result = self.react.reason("Process this text: test input")

        # Check that the API was called twice
        self.assertEqual(self.mock_api_provider.generate.call_count, 2)

        # Check that the result contains the expected fields
        self.assertIn("query", result)
        self.assertIn("answer", result)
        self.assertIn("actions", result)
        self.assertIn("iterations", result)
        self.assertIn("success", result)
        self.assertIn("latency", result)
        self.assertIn("conversation", result)

        # Check that the answer is correct
        self.assertEqual(result["answer"], "The processed result is: Processed: test input")

        # Check that the action was recorded
        self.assertEqual(len(result["actions"]), 1)
        self.assertEqual(result["actions"][0]["name"], "mock_tool")
        self.assertEqual(result["actions"][0]["args"]["input_text"], "test input")

        # Check that the iterations count is correct
        self.assertEqual(result["iterations"], 2)

        # Check that the success flag is set
        self.assertTrue(result["success"])

    def test_parse_response(self):
        """Test the _parse_response method."""
        # Temporarily restore the original _parse_response method
        ReAct._parse_response = self.original_parse_response

        # Test parsing an action
        action_response = """
        Thought: I need to process the input text using the mock tool.
        Action: {
          "name": "mock_tool",
          "args": {
            "input_text": "test input"
          }
        }
        """

        parsed_action = self.react._parse_response(action_response)
        self.assertEqual(parsed_action["type"], "action")
        self.assertEqual(parsed_action["action"]["name"], "mock_tool")
        self.assertEqual(parsed_action["action"]["args"]["input_text"], "test input")

        # Test parsing an answer
        answer_response = """
        Thought: I have processed the input and got the result.
        Answer: The processed result is: Processed: test input
        """

        parsed_answer = self.react._parse_response(answer_response)
        self.assertEqual(parsed_answer["type"], "answer")
        self.assertEqual(parsed_answer["content"], "The processed result is: Processed: test input")

        # Test parsing an invalid response
        invalid_response = """
        This is an invalid response.
        """

        parsed_invalid = self.react._parse_response(invalid_response)
        self.assertEqual(parsed_invalid["type"], "error")

        # Restore the mock for other tests
        ReAct._parse_response = MagicMock(side_effect=[
            {"type": "action", "action": {"name": "mock_tool", "args": {"input_text": "test input"}}},
            {"type": "answer", "content": "The processed result is: Processed: test input"}
        ])

    def test_execute_action(self):
        """Test the _execute_action method."""
        # Execute a valid action
        result = self.react._execute_action("mock_tool", {"input_text": "test input"})
        self.assertTrue(result["success"])
        self.assertEqual(result["input"], "test input")
        self.assertEqual(result["output"], "Processed: test input")

        # Execute an invalid action
        result = self.react._execute_action("invalid_tool", {"input_text": "test input"})
        self.assertFalse(result["success"])
        self.assertIn("error", result)

    def test_add_remove_tool(self):
        """Test adding and removing tools."""
        # Create a new mock tool
        @register_tool
        class AnotherMockTool(BaseTool):
            """Another mock tool for testing."""

            name = "another_mock_tool"
            description = "Another mock tool for testing"

            def run(self, input_text: str, **kwargs):
                """Run the mock tool."""
                return {
                    "success": True,
                    "input": input_text,
                    "output": f"Another processed: {input_text}"
                }

        # Create an instance of the new tool
        another_mock_tool = AnotherMockTool()

        # Add the tool
        self.react.add_tool(another_mock_tool)

        # Check that the tool was added
        self.assertIn("another_mock_tool", self.react.tool_registry)

        # Remove the tool
        result = self.react.remove_tool("another_mock_tool")

        # Check that the tool was removed
        self.assertTrue(result)
        self.assertNotIn("another_mock_tool", self.react.tool_registry)

        # Try to remove a non-existent tool
        result = self.react.remove_tool("non_existent_tool")

        # Check that the removal failed
        self.assertFalse(result)

    def test_list_tools(self):
        """Test listing tools."""
        # List tools
        tools = self.react.list_tools()

        # Check that the list contains the mock tool
        self.assertEqual(len(tools), 1)
        self.assertEqual(tools[0]["name"], "mock_tool")
        self.assertEqual(tools[0]["description"], "A mock tool for testing")


class TestAdaptiveReAct(unittest.TestCase):
    """Test case for AdaptiveReAct."""

    def setUp(self):
        """Set up test fixtures."""
        # Create mock API provider
        self.mock_api_provider = MagicMock()

        # Set up mock responses
        self.mock_api_provider.generate.side_effect = [
            # First response: Action
            """
            Thought: I need to process the input text using the mock tool.
            Action: {
              "name": "mock_tool",
              "args": {
                "input_text": "test input"
              }
            }
            """,
            # Second response: Final answer
            """
            Thought: I have processed the input and got the result.
            Answer: The processed result is: Processed: test input
            """
        ]

        # Create patches
        self.patches = [
            patch('src.deep_research_core.reasoning.react.openai_provider', self.mock_api_provider),
            patch('src.deep_research_core.reasoning.react.anthropic_provider', self.mock_api_provider),
            patch('src.deep_research_core.reasoning.react.openrouter_provider', self.mock_api_provider)
        ]

        # Start patches
        for p in self.patches:
            p.start()

        # Create mock tool
        self.mock_tool = MockTool()

        # Create AdaptiveReAct instance
        self.adaptive_react = AdaptiveReAct(
            provider="openai",
            model="gpt-4o",
            temperature=0.7,
            max_tokens=2000,
            language="en",
            max_iterations=5,
            tools=[self.mock_tool],
            verbose=False,
            learning_rate=0.1
        )

    def tearDown(self):
        """Tear down test fixtures."""
        # Stop patches
        for p in self.patches:
            p.stop()

    def test_reason(self):
        """Test the reason method."""
        # Perform reasoning
        result = self.adaptive_react.reason("Process this text: test input")

        # Check that the API was called twice
        self.assertEqual(self.mock_api_provider.generate.call_count, 2)

        # Check that the result contains the expected fields
        self.assertIn("query", result)
        self.assertIn("answer", result)
        self.assertIn("actions", result)
        self.assertIn("iterations", result)
        self.assertIn("success", result)
        self.assertIn("latency", result)
        self.assertIn("conversation", result)
        self.assertIn("adaptation", result)

        # Check that the adaptation information is present
        self.assertIn("complexity", result["adaptation"])
        self.assertIn("adjusted_params", result["adaptation"])
        self.assertIn("tool_success_rates", result["adaptation"])

    def test_estimate_query_complexity(self):
        """Test the _estimate_query_complexity method."""
        # Test simple query
        complexity = self.adaptive_react._estimate_query_complexity("Simple query")
        self.assertLess(complexity, 0.5)

        # Test complex query
        complexity = self.adaptive_react._estimate_query_complexity(
            "How does the interaction between quantum mechanics and general relativity affect our understanding of black holes and the early universe, and what are the implications for developing a unified theory of physics?"
        )
        self.assertGreater(complexity, 0.5)

    def test_adjust_parameters(self):
        """Test the _adjust_parameters method."""
        # Test parameter adjustment for simple query
        adjusted_params = self.adaptive_react._adjust_parameters("Simple query", 0.2)
        self.assertLess(adjusted_params["temperature"], self.adaptive_react.temperature)
        self.assertLess(adjusted_params["max_iterations"], self.adaptive_react.max_iterations)

        # Test parameter adjustment for complex query
        adjusted_params = self.adaptive_react._adjust_parameters("Complex query", 0.8)
        self.assertGreater(adjusted_params["temperature"], self.adaptive_react.temperature)
        self.assertGreater(adjusted_params["max_iterations"], self.adaptive_react.max_iterations)

    def test_provide_feedback(self):
        """Test the provide_feedback method."""
        # Provide feedback
        self.adaptive_react.provide_feedback(
            query="Process this text: test input",
            feedback={
                "complexity": 0.5,
                "tool_feedback": {
                    "mock_tool": True
                }
            }
        )

        # Check that the feedback was recorded
        self.assertEqual(
            self.adaptive_react.query_complexity_estimates["Process this text: test input"],
            0.5
        )
        self.assertEqual(
            self.adaptive_react.tool_success_rates["mock_tool"]["successes"],
            1
        )

    def test_get_tool_success_rate(self):
        """Test the get_tool_success_rate method."""
        # Initially, success rate should be 0
        self.assertEqual(self.adaptive_react.get_tool_success_rate("mock_tool"), 0.0)

        # Update success rate
        self.adaptive_react.tool_success_rates["mock_tool"]["successes"] = 3
        self.adaptive_react.tool_success_rates["mock_tool"]["total"] = 5

        # Check updated success rate
        self.assertEqual(self.adaptive_react.get_tool_success_rate("mock_tool"), 0.6)

        # Check non-existent tool
        self.assertEqual(self.adaptive_react.get_tool_success_rate("non_existent_tool"), 0.0)


if __name__ == '__main__':
    unittest.main()
