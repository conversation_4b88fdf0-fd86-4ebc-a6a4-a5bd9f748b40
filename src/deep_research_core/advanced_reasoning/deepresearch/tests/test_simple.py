"""
Simple test for the refactored code.
"""

import unittest


class TestSimple(unittest.TestCase):
    """Simple test case."""

    def test_format_results(self):
        """Test a simple formatting function."""
        def format_results(success, results, error=None):
            """Format results."""
            response = {
                "success": success,
                "results": results or []
            }
            if error:
                response["error"] = error
            return response
        
        # Test successful results
        results = [{"title": "Test", "url": "http://example.com"}]
        formatted = format_results(True, results)
        
        self.assertTrue(formatted["success"])
        self.assertEqual(formatted["results"], results)
        
        # Test error results
        error_formatted = format_results(False, [], "Test error")
        
        self.assertFalse(error_formatted["success"])
        self.assertEqual(error_formatted["error"], "Test error")
        self.assertEqual(error_formatted["results"], [])


if __name__ == '__main__':
    unittest.main()
