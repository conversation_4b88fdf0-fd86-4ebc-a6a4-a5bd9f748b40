"""
Test module for IntegratedWebSearchAgent.

Mo<PERSON><PERSON> n<PERSON>y kiểm tra các chức năng của IntegratedWebSearchAgent.
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock
import json
import time

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.agents.integrated_web_search_agent import IntegratedWebSearchAgent

class TestIntegratedWebSearchAgent(unittest.TestCase):
    """
    Test class for IntegratedWebSearchAgent.
    """
    
    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary cache directory
        self.cache_dir = os.path.join(os.path.dirname(__file__), 'test_cache')
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # Create the agent with minimal configuration
        self.agent = IntegratedWebSearchAgent(
            search_method="auto",
            cache_dir=self.cache_dir,
            use_smart_cache=True,
            use_query_optimization=False,  # Disable for testing
            use_result_analysis=False,  # Disable for testing
            use_result_optimization=False,  # Disable for testing
            verbose=True
        )
        
    def tearDown(self):
        """Tear down test fixtures."""
        # Clean up the cache directory
        if os.path.exists(self.cache_dir):
            for file in os.listdir(self.cache_dir):
                os.remove(os.path.join(self.cache_dir, file))
            os.rmdir(self.cache_dir)
            
    @patch('src.deep_research_core.agents.integrated_web_search_agent.search_with_fallback')
    def test_search_with_searxng(self, mock_search_with_fallback):
        """Test search with SearXNG."""
        # Mock the search_with_fallback function
        mock_search_with_fallback.return_value = {
            "success": True,
            "query": "test query",
            "results": [
                {
                    "title": "Test Result 1",
                    "url": "https://example.com/1",
                    "snippet": "This is a test result 1."
                },
                {
                    "title": "Test Result 2",
                    "url": "https://example.com/2",
                    "snippet": "This is a test result 2."
                }
            ],
            "search_method": "searxng",
            "timestamp": time.time()
        }
        
        # Call the search method
        results = self.agent.search(
            query="test query",
            num_results=2,
            method="searxng"
        )
        
        # Check that the search_with_fallback function was called
        mock_search_with_fallback.assert_called_once()
        
        # Check the results
        self.assertTrue(results["success"])
        self.assertEqual(results["original_query"], "test query")
        self.assertEqual(len(results["results"]), 2)
        self.assertEqual(results["results"][0]["title"], "Test Result 1")
        self.assertEqual(results["results"][1]["title"], "Test Result 2")
        
    @patch('src.deep_research_core.agents.integrated_web_search_agent.search_with_searxng_crawlee')
    def test_search_with_crawlee(self, mock_search_with_searxng_crawlee):
        """Test search with Crawlee."""
        # Mock the search_with_searxng_crawlee function
        mock_search_with_searxng_crawlee.return_value = {
            "success": True,
            "query": "test query",
            "results": [
                {
                    "title": "Test Result 1",
                    "url": "https://example.com/1",
                    "content": "This is a test result 1 with detailed content."
                },
                {
                    "title": "Test Result 2",
                    "url": "https://example.com/2",
                    "content": "This is a test result 2 with detailed content."
                }
            ],
            "search_method": "crawlee",
            "timestamp": time.time()
        }
        
        # Call the search method
        results = self.agent.search(
            query="test query",
            num_results=2,
            method="crawlee",
            get_content=True
        )
        
        # Check that the search_with_searxng_crawlee function was called
        mock_search_with_searxng_crawlee.assert_called_once()
        
        # Check the results
        self.assertTrue(results["success"])
        self.assertEqual(results["original_query"], "test query")
        self.assertEqual(len(results["results"]), 2)
        self.assertEqual(results["results"][0]["title"], "Test Result 1")
        self.assertEqual(results["results"][1]["title"], "Test Result 2")
        self.assertTrue("content" in results["results"][0])
        self.assertTrue("content" in results["results"][1])
        
    @patch('src.deep_research_core.agents.integrated_web_search_agent.search_with_fallback')
    @patch('src.deep_research_core.agents.integrated_web_search_agent.search_with_searxng_crawlee')
    def test_search_with_auto_method(self, mock_search_with_searxng_crawlee, mock_search_with_fallback):
        """Test search with auto method selection."""
        # Mock the search_with_fallback function to return a successful result
        mock_search_with_fallback.return_value = {
            "success": True,
            "query": "test query",
            "results": [
                {
                    "title": "Test Result 1",
                    "url": "https://example.com/1",
                    "snippet": "This is a test result 1."
                },
                {
                    "title": "Test Result 2",
                    "url": "https://example.com/2",
                    "snippet": "This is a test result 2."
                }
            ],
            "search_method": "searxng",
            "timestamp": time.time()
        }
        
        # Call the search method with auto method
        results = self.agent.search(
            query="test query",
            num_results=2,
            method="auto"
        )
        
        # Check that the search_with_fallback function was called
        mock_search_with_fallback.assert_called_once()
        
        # Check that the search_with_searxng_crawlee function was not called
        mock_search_with_searxng_crawlee.assert_not_called()
        
        # Check the results
        self.assertTrue(results["success"])
        self.assertEqual(results["original_query"], "test query")
        self.assertEqual(len(results["results"]), 2)
        
    @patch('src.deep_research_core.agents.integrated_web_search_agent.search_with_fallback')
    @patch('src.deep_research_core.agents.integrated_web_search_agent.search_with_searxng_crawlee')
    def test_search_with_auto_method_fallback(self, mock_search_with_searxng_crawlee, mock_search_with_fallback):
        """Test search with auto method selection with fallback to Crawlee."""
        # Mock the search_with_fallback function to return a failed result
        mock_search_with_fallback.return_value = {
            "success": False,
            "query": "test query",
            "error": "SearXNG search failed",
            "results": [],
            "search_method": "searxng",
            "timestamp": time.time()
        }
        
        # Mock the search_with_searxng_crawlee function to return a successful result
        mock_search_with_searxng_crawlee.return_value = {
            "success": True,
            "query": "test query",
            "results": [
                {
                    "title": "Test Result 1",
                    "url": "https://example.com/1",
                    "content": "This is a test result 1 with detailed content."
                },
                {
                    "title": "Test Result 2",
                    "url": "https://example.com/2",
                    "content": "This is a test result 2 with detailed content."
                }
            ],
            "search_method": "crawlee",
            "timestamp": time.time()
        }
        
        # Call the search method with auto method
        results = self.agent.search(
            query="test query",
            num_results=2,
            method="auto",
            get_content=True
        )
        
        # Check that both functions were called
        mock_search_with_fallback.assert_called_once()
        mock_search_with_searxng_crawlee.assert_called_once()
        
        # Check the results
        self.assertTrue(results["success"])
        self.assertEqual(results["original_query"], "test query")
        self.assertEqual(len(results["results"]), 2)
        self.assertEqual(results["results"][0]["title"], "Test Result 1")
        self.assertEqual(results["results"][1]["title"], "Test Result 2")
        self.assertTrue("content" in results["results"][0])
        self.assertTrue("content" in results["results"][1])
        
    def test_check_rate_limit(self):
        """Test rate limiting."""
        # Set a low rate limit for testing
        self.agent.rate_limit = 2
        
        # First call should succeed
        self.assertTrue(self.agent._check_rate_limit())
        
        # Second call should succeed
        self.assertTrue(self.agent._check_rate_limit())
        
        # Third call should fail
        self.assertFalse(self.agent._check_rate_limit())
        
        # Reset the rate limit window
        self.agent.last_request_time = 0
        
        # Next call should succeed again
        self.assertTrue(self.agent._check_rate_limit())
        
    @patch('src.deep_research_core.agents.integrated_web_search_agent.extract_content_with_playwright')
    def test_extract_content(self, mock_extract_content_with_playwright):
        """Test content extraction."""
        # Mock the extract_content_with_playwright function
        mock_extract_content_with_playwright.return_value = {
            "success": True,
            "url": "https://example.com",
            "content": "This is the extracted content.",
            "title": "Example Page"
        }
        
        # Call the extract_content method
        result = self.agent.extract_content(
            url="https://example.com",
            timeout=10,
            max_content_length=1000
        )
        
        # Check that the extract_content_with_playwright function was called
        mock_extract_content_with_playwright.assert_called_once()
        
        # Check the result
        self.assertTrue(result["success"])
        self.assertEqual(result["url"], "https://example.com")
        self.assertEqual(result["content"], "This is the extracted content.")
        self.assertEqual(result["title"], "Example Page")
        
    def test_cache(self):
        """Test caching."""
        # Create a mock search result
        mock_result = {
            "success": True,
            "query": "cache test",
            "results": [
                {
                    "title": "Cache Test Result",
                    "url": "https://example.com/cache",
                    "snippet": "This is a cache test result."
                }
            ],
            "search_method": "searxng",
            "timestamp": time.time()
        }
        
        # Set up the cache
        cache_key = {
            "query": "cache test",
            "optimized_query": "cache test",
            "num_results": 1,
            "method": "searxng",
            "language": None,
            "get_content": False
        }
        
        # Add the result to the cache
        self.agent.cache.set(cache_key, mock_result)
        
        # Mock the search methods to ensure they are not called
        with patch('src.deep_research_core.agents.integrated_web_search_agent.search_with_fallback') as mock_search_with_fallback:
            with patch('src.deep_research_core.agents.integrated_web_search_agent.search_with_searxng_crawlee') as mock_search_with_searxng_crawlee:
                # Call the search method with the same parameters
                result = self.agent.search(
                    query="cache test",
                    num_results=1,
                    method="searxng",
                    force_refresh=False
                )
                
                # Check that the search methods were not called
                mock_search_with_fallback.assert_not_called()
                mock_search_with_searxng_crawlee.assert_not_called()
                
                # Check that the result matches the cached result
                self.assertEqual(result["success"], mock_result["success"])
                self.assertEqual(result["query"], mock_result["query"])
                self.assertEqual(len(result["results"]), len(mock_result["results"]))
                self.assertEqual(result["results"][0]["title"], mock_result["results"][0]["title"])
                
if __name__ == '__main__':
    unittest.main()
