#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Tests for AdaptiveScraper.
"""

import unittest
import os
import sys
import time
from unittest.mock import patch, MagicMock

# Add parent directory to path to import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

try:
    from src.deep_research_core.scrapers.adaptive_scraper import AdaptiveScraper
except ImportError:
    # Mock AdaptiveScraper if not available
    AdaptiveScraper = MagicMock()

class TestAdaptiveScraper(unittest.TestCase):
    """
    Test cases for AdaptiveScraper.
    """
    
    def setUp(self):
        """
        Set up test fixtures.
        """
        # Skip tests if AdaptiveScraper is not available
        if isinstance(AdaptiveScraper, MagicMock):
            self.skipTest("AdaptiveScraper not available")
        
        # Create AdaptiveScraper instance
        self.scraper = AdaptiveScraper(
            timeout=5.0,
            cache_enabled=True,
            extract_metadata=True,
            extract_main_content=True,
            extract_links=True,
            extract_headings=True,
            detect_language=True,
            detect_site_type=True,
            clean_content=True,
            verbose=False
        )
    
    def test_initialization(self):
        """
        Test initialization of AdaptiveScraper.
        """
        self.assertIsNotNone(self.scraper)
        self.assertEqual(self.scraper.timeout, 5.0)
        self.assertTrue(self.scraper.cache_enabled)
        self.assertTrue(self.scraper.extract_metadata)
        self.assertTrue(self.scraper.extract_main_content)
        self.assertTrue(self.scraper.extract_links)
        self.assertTrue(self.scraper.extract_headings)
        self.assertTrue(self.scraper.detect_language)
        self.assertTrue(self.scraper.detect_site_type)
        self.assertTrue(self.scraper.clean_content)
        self.assertFalse(self.scraper.verbose)
    
    @patch('requests.Session.get')
    def test_scrape_success(self, mock_get):
        """
        Test successful scraping.
        """
        # Mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = """
        <html>
            <head>
                <title>Test Page</title>
                <meta name="description" content="Test description">
            </head>
            <body>
                <article>
                    <h1>Test Heading</h1>
                    <p>Test paragraph with some content.</p>
                    <a href="https://example.com">Test link</a>
                </article>
            </body>
        </html>
        """
        mock_response.headers = {"Content-Type": "text/html"}
        mock_response.encoding = "utf-8"
        mock_get.return_value = mock_response
        
        # Test scrape
        result = self.scraper.scrape("https://example.com")
        
        # Verify result
        self.assertTrue(result["success"])
        self.assertEqual(result["url"], "https://example.com")
        self.assertEqual(result["status_code"], 200)
        self.assertIn("content", result)
        self.assertIn("metadata", result)
        self.assertIn("elements", result)
        
        # Verify metadata
        self.assertEqual(result["metadata"]["title"], "Test Page")
        self.assertEqual(result["metadata"]["description"], "Test description")
        
        # Verify elements
        self.assertIn("headings", result["elements"])
        self.assertIn("links", result["elements"])
    
    @patch('requests.Session.get')
    def test_scrape_error(self, mock_get):
        """
        Test scraping with error.
        """
        # Mock response
        mock_get.side_effect = Exception("Test error")
        
        # Test scrape
        result = self.scraper.scrape("https://example.com")
        
        # Verify result
        self.assertFalse(result["success"])
        self.assertEqual(result["url"], "https://example.com")
        self.assertIn("error", result)
        self.assertEqual(result["error_type"], "Exception")
    
    @patch('requests.Session.get')
    def test_scrape_http_error(self, mock_get):
        """
        Test scraping with HTTP error.
        """
        # Mock response
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_get.return_value = mock_response
        
        # Test scrape
        result = self.scraper.scrape("https://example.com")
        
        # Verify result
        self.assertFalse(result["success"])
        self.assertEqual(result["url"], "https://example.com")
        self.assertIn("error", result)
        self.assertEqual(result["error_type"], "http_error")
        self.assertEqual(result["status_code"], 404)
    
    @patch('requests.Session.get')
    def test_cache(self, mock_get):
        """
        Test caching.
        """
        # Mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = "<html><body>Test</body></html>"
        mock_response.headers = {"Content-Type": "text/html"}
        mock_response.encoding = "utf-8"
        mock_get.return_value = mock_response
        
        # First request
        result1 = self.scraper.scrape("https://example.com")
        
        # Second request (should use cache)
        result2 = self.scraper.scrape("https://example.com")
        
        # Verify cache hit
        self.assertEqual(mock_get.call_count, 1)
        self.assertEqual(self.scraper.stats["cache_hits"], 1)
        self.assertEqual(self.scraper.stats["cache_misses"], 1)
        
        # Verify results are the same
        self.assertEqual(result1["content"], result2["content"])

if __name__ == '__main__':
    unittest.main()
