#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test file để kiểm tra các sửa lỗi và chức năng mới trong IntegratedWebSearchAgent.
"""

import os
import sys
import unittest
import logging
from unittest.mock import patch

# Thêm thư mục gốc vào sys.path để import các module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.deep_research_core.agents.integrated_web_search_agent import IntegratedWebSearchAgent
from src.deep_research_core.agents.query_optimizer import QueryOptimizer

# Tắt logging để test không bị nhiễu
logging.disable(logging.CRITICAL)

class TestIntegratedWebSearchAgentFixes(unittest.TestCase):
    """
    Test case cho các sửa lỗi và chức năng mới trong IntegratedWebSearchAgent.
    """

    def setUp(self):
        """
        Thiết lập môi trường test.
        """
        # Tạo mock cho các phụ thuộc
        self.mock_search_result = {
            "success": True,
            "results": [
                {
                    "title": "Test Result",
                    "url": "https://example.com",
                    "snippet": "This is a test result"
                }
            ]
        }

    def test_query_optimizer_initialization(self):
        """
        Test xem QueryOptimizer có được khởi tạo đúng không.
        """
        # Tạo agent với use_query_optimization=True
        agent = IntegratedWebSearchAgent(
            search_method="auto",
            use_query_optimization=True
        )

        # Kiểm tra xem query_optimizer có phải là instance của QueryOptimizer không
        self.assertIsInstance(agent.query_optimizer, QueryOptimizer)

    def test_search_method_selection(self):
        """
        Test xem phương thức tìm kiếm có được chọn đúng không.
        """
        # Tạo agent với search_method="searxng"
        agent = IntegratedWebSearchAgent(
            search_method="searxng",
            use_query_optimization=False
        )

        # Kiểm tra xem phương thức search có tồn tại không
        self.assertTrue(hasattr(agent, 'search'))
        self.assertTrue(callable(getattr(agent, 'search')))

        # Kiểm tra xem phương thức _search_searxng có tồn tại không
        self.assertTrue(hasattr(agent, '_search_searxng'))
        self.assertTrue(callable(getattr(agent, '_search_searxng')))

    def test_extract_content_method(self):
        """
        Test phương thức extract_content.
        """
        # Tạo agent
        agent = IntegratedWebSearchAgent(search_method="auto")

        # Kiểm tra xem phương thức extract_content có tồn tại không
        self.assertTrue(hasattr(agent, 'extract_content'))
        self.assertTrue(callable(getattr(agent, 'extract_content')))

    def test_query_optimizer_not_initialized(self):
        """
        Test xem QueryOptimizer có không được khởi tạo khi use_query_optimization=False không.
        """
        # Tạo agent với use_query_optimization=False
        agent = IntegratedWebSearchAgent(
            search_method="auto",
            use_query_optimization=False
        )

        # Kiểm tra xem query_optimizer có phải là None không
        self.assertIsNone(agent.query_optimizer)

if __name__ == '__main__':
    unittest.main()
