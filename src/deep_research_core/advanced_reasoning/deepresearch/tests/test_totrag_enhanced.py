"""
Test script for enhanced ToTRAG.

This script tests the enhanced features of ToTRAG:
- Adaptive parameter adjustment
- Caching
- Result evaluation
"""

import os
import sys
import unittest
from unittest.mock import MagicMock, patch
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.reasoning.tot_rag import ToTRAG
from src.deep_research_core.reasoning.cot_optimization import SelectiveCache
from src.deep_research_core.evaluation.cot_evaluator import CoTEvaluator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestToTRAGEnhanced(unittest.TestCase):
    """Test case for enhanced ToTRAG features."""

    def setUp(self):
        """Set up test fixtures."""
        # Create mock vector store
        self.mock_vector_store = MagicMock()
        self.mock_vector_store.search.return_value = [
            {
                "content": "This is a test document about artificial intelligence.",
                "metadata": {"source": "Test Source", "title": "Test Document"},
                "score": 0.95
            }
        ]

        # Create mock API provider
        self.mock_api_provider = MagicMock()

        # Create mock TreeOfThought
        self.mock_tot = MagicMock()
        self.mock_tot._estimate_complexity.return_value = 0.75
        self.mock_tot.reason.return_value = {
            "best_path": [
                {
                    "reasoning": "First, I'll analyze the information. The document mentions artificial intelligence.",
                    "value": 0.8
                },
                {
                    "reasoning": "Next, I'll consider what AI is. AI is a field of computer science.",
                    "value": 0.9
                }
            ],
            "answer": "AI is a field of computer science.",
            "paths_explored": 4,
            "max_depth_reached": 2
        }

        # Create patches
        self.patches = [
            patch('src.deep_research_core.reasoning.tot_rag.get_vector_store', return_value=self.mock_vector_store),
            patch('src.deep_research_core.reasoning.tot_rag.openai_provider', self.mock_api_provider),
            patch('src.deep_research_core.reasoning.tot_rag.anthropic_provider', self.mock_api_provider),
            patch('src.deep_research_core.reasoning.tot_rag.openrouter_provider', self.mock_api_provider),
            patch('src.deep_research_core.reasoning.tot_rag.TreeOfThought', return_value=self.mock_tot)
        ]

        # Start patches
        for p in self.patches:
            p.start()

        # Create ToTRAG instance with enhanced features
        self.totrag = ToTRAG(
            provider="openai",
            model="gpt-4o",
            temperature=0.7,
            max_tokens=2000,
            max_branches=3,
            max_depth=3,
            adaptive=True,
            use_cache=True,
            evaluate_results=True
        )

        # Mock the evaluator
        self.totrag.evaluator = MagicMock()
        self.totrag.evaluator.evaluate.return_value = {
            "metrics": {
                "step_clarity": 8.0,
                "logical_flow": 7.5,
                "evidence_usage": 7.0,
                "conclusion_strength": 8.0,
                "overall_quality": 7.6
            },
            "step_count": 2,
            "latency": 0.5
        }

    def tearDown(self):
        """Tear down test fixtures."""
        # Stop patches
        for p in self.patches:
            p.stop()

    def test_adaptive_parameters(self):
        """Test adaptive parameter adjustment."""
        # Set up the mock to return a specific complexity value
        self.mock_tot._estimate_complexity.return_value = 0.75

        # Manually calculate the expected adjusted values based on the formula in ToTRAG.process
        complexity = 0.75
        expected_max_branches = max(2, min(int(3 * (0.5 + complexity)), 3 + 2))  # Should be 3
        expected_max_depth = max(2, min(int(3 * (0.5 + complexity)), 3 + 1))    # Should be 3

        # Process a query
        result = self.totrag.process("What is artificial intelligence?")

        # Check that the ToT parameters were adjusted correctly
        self.assertEqual(self.mock_tot.max_branches, expected_max_branches)  # Should be 3 based on the formula
        self.assertEqual(self.mock_tot.max_depth, expected_max_depth)        # Should be 3 (max allowed)

    def test_caching(self):
        """Test result caching."""
        # Process the same query twice
        query = "What is artificial intelligence?"
        self.totrag.process(query)
        self.totrag.process(query)

        # Check that the ToT reason method was called only once
        self.assertEqual(self.mock_tot.reason.call_count, 1)

        # Process with force_refresh
        self.totrag.process(query, force_refresh=True)

        # Check that the ToT reason method was called again
        self.assertEqual(self.mock_tot.reason.call_count, 2)

    def test_evaluation(self):
        """Test result evaluation."""
        # Process a query
        result = self.totrag.process("What is artificial intelligence?")

        # Check that evaluation was performed
        self.totrag.evaluator.evaluate.assert_called_once()

        # Check that evaluation results are included in the result
        self.assertIn("evaluation", result)
        self.assertEqual(result["evaluation"]["metrics"]["overall_quality"], 7.6)

    def test_disable_features(self):
        """Test disabling enhanced features."""
        # Create a new mock for the TreeOfThought
        new_mock_tot = MagicMock()
        new_mock_tot._estimate_complexity.return_value = 0.75
        new_mock_tot.reason.return_value = {
            "best_path": [],
            "answer": "AI is a field of computer science.",
            "paths_explored": 3,
            "max_depth_reached": 2
        }

        # Create a patch for TreeOfThought
        with patch('src.deep_research_core.reasoning.tot_rag.TreeOfThought', return_value=new_mock_tot):
            # Create ToTRAG instance with disabled features
            totrag_basic = ToTRAG(
                provider="openai",
                model="gpt-4o",
                adaptive=False,
                use_cache=False,
                evaluate_results=False
            )

            # Process a query
            result = totrag_basic.process("What is artificial intelligence?")

            # Since adaptive=False, we don't need to check the exact value of max_branches
            # Instead, we just verify that the _estimate_complexity method was not called
            new_mock_tot._estimate_complexity.assert_not_called()

            # Check that evaluation results are not included
            self.assertNotIn("evaluation", result)

if __name__ == '__main__':
    unittest.main()
