# Hướng dẫn chạy test case cho WebSearchAgentLocal

Tài liệu này mô tả cách chạy các test case cho WebSearchAgentLocal, bao gồm các test case cơ bản và nâng cao.

## Mục lục

1. [Cài đặt môi trường](#1-cài-đặt-môi-trường)
2. [Cấu trúc test case](#2-cấu-trúc-test-case)
3. [Chạy tất cả các test case](#3-chạy-tất-cả-các-test-case)
4. [Chạy từng test case riêng lẻ](#4-chạy-từng-test-case-riêng-lẻ)
5. [Xem kết quả test](#5-xem-kết-quả-test)
6. [Thêm test case mới](#6-thêm-test-case-mới)
7. [<PERSON><PERSON> lý lỗi thường gặp](#7-xử-lý-lỗi-thường-gặp)

## 1. Cài đặt môi trường

Trước khi chạy các test case, bạn cần cài đặt các thư viện cần thiết:

```bash
# Cài đặt các thư viện cần thiết
pip install -r requirements.txt

# Cài đặt thư viện phát triển (nếu cần)
pip install -r requirements-dev.txt
```

## 2. Cấu trúc test case

Các test case được tổ chức thành các module sau:

- **test_web_search_agent_local.py**: Test các tính năng cơ bản của WebSearchAgentLocal
- **test_web_search_agent_local_comprehensive.py**: Test toàn diện các tính năng của WebSearchAgentLocal
- **test_web_search_agent_local_comprehensive_all.py**: Test toàn diện tất cả các tính năng của WebSearchAgentLocal, bao gồm cả các tính năng mới
- **test_web_search_agent_local_file_extraction.py**: Test trích xuất nội dung từ các định dạng file
- **test_web_search_agent_local_multimedia.py**: Test tìm kiếm đa phương tiện
- **test_web_search_agent_local_improvements.py**: Test các cải tiến mới cho WebSearchAgentLocal

## 3. Chạy tất cả các test case

Để chạy tất cả các test case, sử dụng script `run_all_tests.py`:

```bash
# Chạy từ thư mục gốc của dự án
python run_all_tests.py
```

Hoặc chạy từng thư mục test:

```bash
# Chạy tất cả các test trong thư mục tests
python run_all_tests.py --dir tests
```

## 4. Chạy từng test case riêng lẻ

Để chạy từng test case riêng lẻ, sử dụng lệnh sau:

```bash
# Chạy test_web_search_agent_local.py
python -m unittest tests.test_web_search_agent_local

# Chạy test_web_search_agent_local_comprehensive.py
python -m unittest tests.test_web_search_agent_local_comprehensive

# Chạy test_web_search_agent_local_comprehensive_all.py
python -m unittest tests.test_web_search_agent_local_comprehensive_all

# Chạy test_web_search_agent_local_file_extraction.py
python -m unittest tests.test_web_search_agent_local_file_extraction

# Chạy test_web_search_agent_local_multimedia.py
python -m unittest tests.test_web_search_agent_local_multimedia

# Chạy test_web_search_agent_local_improvements.py
python -m unittest tests.test_web_search_agent_local_improvements
```

Hoặc chạy một test case cụ thể:

```bash
# Chạy test_01_basic_search trong test_web_search_agent_local_comprehensive_all.py
python -m unittest tests.test_web_search_agent_local_comprehensive_all.TestWebSearchAgentLocalComprehensive.test_01_basic_search
```

## 5. Xem kết quả test

Kết quả test được lưu trong thư mục `test_results`. Mỗi test case sẽ tạo một file JSON tương ứng với kết quả của nó.

```bash
# Xem danh sách file kết quả
ls -la test_results/

# Xem nội dung file kết quả
cat test_results/basic_search_result.json
```

Ngoài ra, kết quả tổng hợp được lưu trong file `test_results/summary.json`.

## 6. Thêm test case mới

Để thêm test case mới, bạn cần tạo một file Python mới trong thư mục `tests` với tên bắt đầu bằng `test_`. Sau đó, bạn cần thêm các test case vào file này.

Ví dụ:

```python
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test case mới cho WebSearchAgentLocal.
"""

import unittest
from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal

class TestWebSearchAgentLocalNew(unittest.TestCase):
    """Test case mới cho WebSearchAgentLocal."""
    
    def setUp(self):
        """Thiết lập trước mỗi test."""
        self.agent = WebSearchAgentLocal(verbose=True)
    
    def test_new_feature(self):
        """Test tính năng mới."""
        # Viết test case ở đây
        pass
    
    def tearDown(self):
        """Dọn dẹp sau mỗi test."""
        pass

if __name__ == "__main__":
    unittest.main()
```

Sau đó, bạn cần thêm file test mới vào danh sách test trong file `run_all_tests.py`.

## 7. Xử lý lỗi thường gặp

### 7.1. Lỗi import module

Nếu bạn gặp lỗi import module, hãy đảm bảo rằng bạn đã thêm thư mục gốc của dự án vào `sys.path`:

```python
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
```

### 7.2. Lỗi thiếu thư viện

Nếu bạn gặp lỗi thiếu thư viện, hãy cài đặt thư viện đó:

```bash
pip install <tên_thư_viện>
```

### 7.3. Lỗi kết nối

Nếu bạn gặp lỗi kết nối khi chạy test, hãy đảm bảo rằng bạn đã kết nối internet và SearXNG đang chạy:

```bash
# Kiểm tra SearXNG
curl http://localhost:8080/search?q=test
```

### 7.4. Lỗi timeout

Nếu bạn gặp lỗi timeout, hãy tăng thời gian chờ trong test case:

```python
# Tăng thời gian chờ
result = self.agent.search(query, num_results=5, timeout=60)
```

### 7.5. Lỗi CAPTCHA

Nếu bạn gặp lỗi CAPTCHA, hãy đảm bảo rằng bạn đã cài đặt CaptchaHandler và VietnameseCaptchaHandler:

```python
# Kiểm tra CaptchaHandler
if hasattr(self.agent, 'handle_captcha'):
    # Xử lý CAPTCHA
    pass
```

## Kết luận

Các test case này giúp bạn kiểm tra tất cả các tính năng của WebSearchAgentLocal, bao gồm cả các tính năng cơ bản và nâng cao. Nếu bạn gặp bất kỳ vấn đề nào khi chạy test, hãy tham khảo phần "Xử lý lỗi thường gặp" hoặc liên hệ với người phát triển.
