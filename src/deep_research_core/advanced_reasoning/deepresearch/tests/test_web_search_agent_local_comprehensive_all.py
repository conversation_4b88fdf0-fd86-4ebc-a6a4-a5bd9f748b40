#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test case to<PERSON>n diện cho WebSearchAgentLocal.

Module này cung cấp các test case to<PERSON><PERSON> diện cho tất cả các tính năng của WebSearchAgentLocal,
bao gồm cả các tính năng cơ bản và nâng cao.
"""

import os
import sys
import time
import json
import random
import unittest
import logging
from unittest.mock import patch, MagicMock
from typing import Dict, Any, List, Optional

# Thêm thư mục gốc vào sys.path để import các module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Import các module c<PERSON><PERSON> thiết
try:
    from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal
    from src.deep_research_core.agents.web_search_agent_local_improvements import integrate_improvements
    from src.deep_research_core.agents.vietnamese_search_integration_improved import integrate_vietnamese_search_improved
    from src.deep_research_core.utils.vietnamese_captcha_handler import VietnameseCaptchaHandler
    from src.deep_research_core.agents.performance_optimizer_improved import PerformanceOptimizerImproved
    from src.deep_research_core.agents.module_integration_manager import ModuleIntegrationManager
except ImportError:
    try:
        # Thử import từ thư mục hiện tại
        from web_search_agent_local import WebSearchAgentLocal
        logger.warning("Importing from current directory. Some features may not be available.")
        
        # Định nghĩa các class giả nếu không import được
        class MockClass:
            def __init__(self, *args, **kwargs):
                pass
                
        integrate_improvements = lambda *args, **kwargs: {"success": True}
        integrate_vietnamese_search_improved = lambda *args, **kwargs: None
        VietnameseCaptchaHandler = MockClass
        PerformanceOptimizerImproved = MockClass
        ModuleIntegrationManager = MockClass
    except ImportError:
        logger.error("Không thể import WebSearchAgentLocal. Vui lòng kiểm tra đường dẫn.")
        sys.exit(1)

# Hàm tiện ích
def print_section(title):
    """In tiêu đề phần."""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, "="))
    print("=" * 80)

def print_result(result):
    """In kết quả tìm kiếm."""
    if not result.get("success", False):
        print(f"Lỗi: {result.get('error', 'Unknown error')}")
        return
    
    print(f"Tìm thấy {len(result.get('results', []))} kết quả")
    
    for i, item in enumerate(result.get("results", [])[:3]):  # Chỉ in 3 kết quả đầu tiên
        print(f"\nKết quả {i+1}:")
        print(f"  Tiêu đề: {item.get('title', 'N/A')}")
        print(f"  URL: {item.get('url', 'N/A')}")
        print(f"  Snippet: {item.get('snippet', 'N/A')[:100]}..." if len(item.get('snippet', '')) > 100 else f"  Snippet: {item.get('snippet', 'N/A')}")
        
        if "content" in item:
            content = item["content"]
            print(f"  Nội dung: {content[:100]}..." if len(content) > 100 else f"  Nội dung: {content}")

def save_results_to_json(results, filename):
    """Lưu kết quả vào file JSON."""
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print(f"Đã lưu kết quả vào {filename}")

class TestWebSearchAgentLocalComprehensive(unittest.TestCase):
    """Test case toàn diện cho WebSearchAgentLocal."""
    
    @classmethod
    def setUpClass(cls):
        """Thiết lập trước khi chạy tất cả các test."""
        print_section("Thiết lập test toàn diện WebSearchAgentLocal")
        
        # Tạo thư mục lưu kết quả nếu chưa tồn tại
        os.makedirs("test_results", exist_ok=True)
        
        # Khởi tạo WebSearchAgentLocal với cấu hình mặc định
        cls.agent = WebSearchAgentLocal(
            search_method="auto",
            api_search_config={
                "engine": "searx",
                "searx_url": "http://localhost:8080",
                "language": "auto"
            },
            crawlee_search_config={
                "max_depth": 2,
                "max_pages_per_url": 3,
                "max_urls": 5,
                "timeout": 30
            },
            verbose=True
        )
        
        # Tích hợp các cải tiến
        try:
            cls.improvements_result = integrate_improvements(cls.agent)
            print(f"Tích hợp cải tiến: {cls.improvements_result['success']}")
            print(f"Các module đã tích hợp: {cls.improvements_result.get('integrated_modules', [])}")
            print(f"Các module thất bại: {cls.improvements_result.get('failed_modules', [])}")
        except Exception as e:
            print(f"Lỗi khi tích hợp cải tiến: {str(e)}")
        
        # Danh sách câu hỏi đơn giản
        cls.simple_queries = [
            "Thủ đô của Việt Nam",
            "Python programming language",
            "Capital of France",
            "Who is Albert Einstein",
            "Công thức nước là gì"
        ]
        
        # Danh sách câu hỏi phức tạp
        cls.complex_queries = [
            "Phân tích tác động của biến đổi khí hậu đến nông nghiệp Việt Nam trong 10 năm qua",
            "So sánh chi tiết giữa React và Vue.js về hiệu suất và quản lý state",
            "Giải thích cơ chế hoạt động của mạng neural tích chập (CNN) trong xử lý ảnh",
            "Phân tích ưu nhược điểm của các phương pháp xác thực hai yếu tố phổ biến hiện nay",
            "Tổng hợp các nghiên cứu gần đây về ứng dụng trí tuệ nhân tạo trong y tế"
        ]
        
        # Danh sách câu hỏi tiếng Việt
        cls.vietnamese_queries = [
            "Thủ đô của Việt Nam là gì",
            "Lịch sử hình thành của Hà Nội",
            "Các món ăn đặc sản của Huế",
            "Tác giả của Truyện Kiều là ai",
            "Cách làm bánh chưng truyền thống"
        ]
        
        # Danh sách URL để test trích xuất nội dung
        cls.test_urls = [
            "https://www.python.org/",
            "https://en.wikipedia.org/wiki/Python_(programming_language)",
            "https://github.com/python/cpython",
            "https://docs.python.org/3/",
            "https://www.w3schools.com/python/"
        ]
        
        # Danh sách URL tiếng Việt để test trích xuất nội dung
        cls.vietnamese_urls = [
            "https://vi.wikipedia.org/wiki/H%C3%A0_N%E1%BB%99i",
            "https://dantri.com.vn/",
            "https://vnexpress.net/",
            "https://tuoitre.vn/",
            "https://thanhnien.vn/"
        ]
    
    def setUp(self):
        """Thiết lập trước mỗi test."""
        # Đợi một chút giữa các test để tránh rate limit
        time.sleep(1)
    
    def test_01_basic_search(self):
        """Test tìm kiếm cơ bản."""
        print_section("Test tìm kiếm cơ bản")
        
        # Chọn một câu hỏi đơn giản
        query = random.choice(self.simple_queries)
        print(f"Truy vấn đơn giản: {query}")
        
        # Thực hiện tìm kiếm
        result = self.agent.search(query, num_results=5)
        print_result(result)
        
        # Kiểm tra kết quả
        self.assertTrue(result.get('success', False), "Tìm kiếm cơ bản không thành công")
        self.assertGreater(len(result.get('results', [])), 0, "Không có kết quả tìm kiếm")
        
        # Lưu kết quả
        save_results_to_json(result, "test_results/basic_search_result.json")
    
    def test_02_search_with_content(self):
        """Test tìm kiếm với trích xuất nội dung."""
        print_section("Test tìm kiếm với trích xuất nội dung")
        
        # Chọn một câu hỏi đơn giản
        query = random.choice(self.simple_queries)
        print(f"Truy vấn đơn giản: {query}")
        
        # Thực hiện tìm kiếm với trích xuất nội dung
        result = self.agent.search(query, num_results=3, get_content=True)
        print_result(result)
        
        # Kiểm tra kết quả
        self.assertTrue(result.get('success', False), "Tìm kiếm với trích xuất nội dung không thành công")
        self.assertGreater(len(result.get('results', [])), 0, "Không có kết quả tìm kiếm")
        
        # Kiểm tra nội dung
        for item in result.get('results', []):
            self.assertIn('content', item, "Không có nội dung trong kết quả")
            self.assertTrue(item.get('content', ''), "Nội dung trống")
        
        # Lưu kết quả
        save_results_to_json(result, "test_results/search_with_content_result.json")
    
    def test_03_search_with_language(self):
        """Test tìm kiếm với ngôn ngữ cụ thể."""
        print_section("Test tìm kiếm với ngôn ngữ cụ thể")
        
        # Chọn một câu hỏi tiếng Việt
        query = random.choice(self.vietnamese_queries)
        print(f"Truy vấn tiếng Việt: {query}")
        
        # Thực hiện tìm kiếm với ngôn ngữ tiếng Việt
        result = self.agent.search(query, num_results=5, language="vi")
        print_result(result)
        
        # Kiểm tra kết quả
        self.assertTrue(result.get('success', False), "Tìm kiếm với ngôn ngữ cụ thể không thành công")
        self.assertGreater(len(result.get('results', [])), 0, "Không có kết quả tìm kiếm")
        
        # Lưu kết quả
        save_results_to_json(result, "test_results/search_with_language_result.json")
    
    def test_04_complex_query(self):
        """Test tìm kiếm với câu hỏi phức tạp."""
        print_section("Test tìm kiếm với câu hỏi phức tạp")
        
        # Chọn một câu hỏi phức tạp
        query = random.choice(self.complex_queries)
        print(f"Truy vấn phức tạp: {query}")
        
        # Thực hiện tìm kiếm
        result = self.agent.search(
            query, 
            num_results=5, 
            get_content=True,
            evaluate_question=True
        )
        print_result(result)
        
        # Kiểm tra kết quả
        self.assertTrue(result.get('success', False), "Tìm kiếm với câu hỏi phức tạp không thành công")
        self.assertGreater(len(result.get('results', [])), 0, "Không có kết quả tìm kiếm")
        
        # Kiểm tra đánh giá câu hỏi
        self.assertIn('question_evaluation', result, "Không có đánh giá câu hỏi")
        
        # Lưu kết quả
        save_results_to_json(result, "test_results/complex_query_result.json")
    
    def test_05_extract_content(self):
        """Test trích xuất nội dung từ URL."""
        print_section("Test trích xuất nội dung từ URL")
        
        # Chọn một URL
        url = random.choice(self.test_urls)
        print(f"URL: {url}")
        
        # Trích xuất nội dung
        result = self.agent._extract_content_sync(url)
        
        # Kiểm tra kết quả
        self.assertTrue(result.get('success', False), f"Trích xuất nội dung từ {url} không thành công")
        self.assertTrue(result.get('text', ''), "Nội dung trích xuất trống")
        
        # In kết quả
        print(f"Tiêu đề: {result.get('title', 'N/A')}")
        text = result.get('text', '')
        print(f"Nội dung: {text[:200]}..." if len(text) > 200 else f"Nội dung: {text}")
        
        # Lưu kết quả
        save_results_to_json(result, "test_results/extract_content_result.json")
    
    def test_06_deep_crawl(self):
        """Test deep crawl."""
        print_section("Test deep crawl")
        
        # Chọn một URL
        url = random.choice(self.test_urls)
        print(f"URL: {url}")
        
        # Thực hiện deep crawl
        result = self.agent._deep_crawl(url, max_depth=1, max_pages=2)
        
        # Kiểm tra kết quả
        self.assertTrue(result.get('success', False), f"Deep crawl {url} không thành công")
        self.assertTrue(result.get('text', ''), "Nội dung crawl trống")
        
        # In kết quả
        print(f"Số trang đã crawl: {result.get('pages_crawled', 0)}")
        text = result.get('text', '')
        print(f"Nội dung: {text[:200]}..." if len(text) > 200 else f"Nội dung: {text}")
        
        # Lưu kết quả
        save_results_to_json(result, "test_results/deep_crawl_result.json")
    
    def test_07_vietnamese_search(self):
        """Test tìm kiếm tiếng Việt."""
        print_section("Test tìm kiếm tiếng Việt")
        
        # Chọn một câu hỏi tiếng Việt
        query = random.choice(self.vietnamese_queries)
        print(f"Truy vấn tiếng Việt: {query}")
        
        # Thực hiện tìm kiếm tiếng Việt
        if hasattr(self.agent, 'search_vietnamese'):
            result = self.agent.search_vietnamese(query, num_results=5)
            print_result(result)
            
            # Kiểm tra kết quả
            self.assertTrue(result.get('success', False), "Tìm kiếm tiếng Việt không thành công")
            self.assertGreater(len(result.get('results', [])), 0, "Không có kết quả tìm kiếm tiếng Việt")
            
            # Lưu kết quả
            save_results_to_json(result, "test_results/vietnamese_search_result.json")
        else:
            print("Phương thức search_vietnamese không khả dụng")
            self.skipTest("Phương thức search_vietnamese không khả dụng")
    
    def test_08_vietnamese_captcha_handler(self):
        """Test xử lý CAPTCHA tiếng Việt."""
        print_section("Test xử lý CAPTCHA tiếng Việt")
        
        # Tạo HTML giả có CAPTCHA tiếng Việt
        html_content = """
        <html>
        <body>
            <div>
                <h1>Xác thực bảo mật</h1>
                <p>Vui lòng nhập mã xác nhận để tiếp tục</p>
                <img src="captcha.png" alt="Mã xác nhận">
                <input type="text" placeholder="Nhập mã xác nhận">
                <button>Xác nhận</button>
            </div>
        </body>
        </html>
        """
        
        # Kiểm tra phát hiện CAPTCHA
        if hasattr(self.agent, 'handle_vietnamese_captcha'):
            result = self.agent.handle_vietnamese_captcha("https://example.com", html_content)
            
            # In kết quả
            print(f"Kết quả xử lý CAPTCHA: {result.get('success', False)}")
            if not result.get('success', False):
                print(f"Lỗi: {result.get('error', 'Unknown error')}")
            
            # Không kiểm tra kết quả vì đây là HTML giả
            # Chỉ kiểm tra xem phương thức có chạy không lỗi
            
            # Lưu kết quả
            save_results_to_json(result, "test_results/vietnamese_captcha_handler_result.json")
        else:
            print("Phương thức handle_vietnamese_captcha không khả dụng")
            self.skipTest("Phương thức handle_vietnamese_captcha không khả dụng")
    
    def test_09_performance_optimizer(self):
        """Test tối ưu hóa hiệu suất."""
        print_section("Test tối ưu hóa hiệu suất")
        
        # Chọn một câu hỏi phức tạp
        query = random.choice(self.complex_queries)
        print(f"Truy vấn phức tạp: {query}")
        
        # Thực hiện tìm kiếm với tối ưu hóa hiệu suất
        if hasattr(self.agent, 'optimize_query_execution'):
            result = self.agent.optimize_query_execution(
                query=query,
                complexity_level="complex",
                num_results=5,
                get_content=True
            )
            print_result(result)
            
            # Kiểm tra kết quả
            self.assertTrue(result.get('success', False), "Tìm kiếm với tối ưu hóa hiệu suất không thành công")
            self.assertGreater(len(result.get('results', [])), 0, "Không có kết quả tìm kiếm")
            
            # Kiểm tra thông tin hiệu suất
            self.assertIn('performance', result, "Không có thông tin hiệu suất")
            
            # In thông tin hiệu suất
            performance = result.get('performance', {})
            print(f"Thời gian thực thi: {performance.get('execution_time', 0):.2f} giây")
            print(f"Mức độ phức tạp: {performance.get('complexity_level', 'unknown')}")
            print(f"Số lượng worker tối đa: {performance.get('max_workers', 0)}")
            
            # Lưu kết quả
            save_results_to_json(result, "test_results/performance_optimizer_result.json")
        else:
            print("Phương thức optimize_query_execution không khả dụng")
            self.skipTest("Phương thức optimize_query_execution không khả dụng")
    
    def test_10_module_integration_manager(self):
        """Test quản lý tích hợp module."""
        print_section("Test quản lý tích hợp module")
        
        # Kiểm tra module_manager
        if hasattr(self.agent, 'module_manager'):
            # Lấy trạng thái của các module
            module_status = self.agent.module_manager.get_module_status()
            print(f"Trạng thái của các module: {module_status}")
            
            # Lấy danh sách module đã tích hợp
            integrated_modules = self.agent.module_manager.get_integrated_modules()
            print(f"Các module đã tích hợp: {integrated_modules}")
            
            # Lấy danh sách module thất bại
            failed_modules = self.agent.module_manager.get_failed_modules()
            print(f"Các module thất bại: {failed_modules}")
            
            # Kiểm tra kết quả
            self.assertIsInstance(module_status, dict, "Trạng thái của các module không phải là dict")
            self.assertIsInstance(integrated_modules, set, "Danh sách module đã tích hợp không phải là set")
            self.assertIsInstance(failed_modules, set, "Danh sách module thất bại không phải là set")
            
            # Lưu kết quả
            result = {
                "module_status": module_status,
                "integrated_modules": list(integrated_modules),
                "failed_modules": list(failed_modules)
            }
            save_results_to_json(result, "test_results/module_integration_manager_result.json")
        else:
            print("Thuộc tính module_manager không khả dụng")
            self.skipTest("Thuộc tính module_manager không khả dụng")
    
    @classmethod
    def tearDownClass(cls):
        """Dọn dẹp sau khi chạy tất cả các test."""
        print_section("Dọn dẹp sau khi chạy tất cả các test")
        
        # Dọn dẹp tài nguyên
        if hasattr(cls.agent, '_performance_optimizer_improved'):
            cls.agent._performance_optimizer_improved.cleanup()

if __name__ == "__main__":
    unittest.main()
