"""
Tests for Vietnamese language support.
"""

import unittest
import re
from deep_research_core.utils.vietnamese_utils import (
    detect_vietnamese,
    has_vietnamese_diacritics,
    detect_vietnamese_dialect,
    contains_vietnamese_compound_words,
    get_vietnamese_compound_words,
    translate_to_vietnamese,
    adapt_prompt_for_vietnamese
)


class TestVietnameseSupport(unittest.TestCase):
    """Test cases for Vietnamese language support."""

    def test_detect_vietnamese(self):
        """Test Vietnamese language detection."""
        # Test with Vietnamese text
        self.assertTrue(detect_vietnamese("Xin chào thế giới"))
        self.assertTrue(detect_vietnamese("Tôi đang học tiếng Việt"))
        
        # Test with non-Vietnamese text
        self.assertFalse(detect_vietnamese("Hello world"))
        self.assertFalse(detect_vietnamese("こんにちは世界"))
        
        # Test with mixed text
        self.assertTrue(detect_vietnamese("Hello, tôi tên là <PERSON>"))
        
        # Test with empty text
        self.assertFalse(detect_vietnamese(""))
        self.assertFalse(detect_vietnamese(None))

    def test_has_vietnamese_diacritics(self):
        """Test Vietnamese diacritics detection."""
        # Test with diacritics
        self.assertTrue(has_vietnamese_diacritics("Xin chào"))
        self.assertTrue(has_vietnamese_diacritics("Tiếng Việt"))
        
        # Test without diacritics
        self.assertFalse(has_vietnamese_diacritics("Tieng Viet"))
        self.assertFalse(has_vietnamese_diacritics("Hello world"))
        
        # Test with empty text
        self.assertFalse(has_vietnamese_diacritics(""))
        self.assertFalse(has_vietnamese_diacritics(None))

    def test_detect_vietnamese_dialect(self):
        """Test Vietnamese dialect detection."""
        # Northern dialect
        northern_text = "Tôi đang ở Hà Nội và tôi thích ăn phở ở đây."
        self.assertEqual(detect_vietnamese_dialect(northern_text), "northern")
        
        # Southern dialect
        southern_text = "Tui đang ở Sài Gòn và tui thích ăn hủ tiếu ở đây."
        self.assertEqual(detect_vietnamese_dialect(southern_text), "southern")
        
        # Test with non-Vietnamese text
        self.assertIsNone(detect_vietnamese_dialect("Hello world"))
        
        # Test with empty text
        self.assertIsNone(detect_vietnamese_dialect(""))
        self.assertIsNone(detect_vietnamese_dialect(None))

    def test_contains_vietnamese_compound_words(self):
        """Test Vietnamese compound words detection."""
        # Test with compound words
        self.assertTrue(contains_vietnamese_compound_words("Hôm nay tôi đi chợ Bến Thành"))
        
        # Test with domain-specific compound words
        medical_text = "Bệnh viện Bạch Mai là một trong những bệnh viện lớn nhất Việt Nam"
        self.assertTrue(contains_vietnamese_compound_words(medical_text, "medical"))
        
        # Test without compound words
        self.assertFalse(contains_vietnamese_compound_words("Hello world"))
        
        # Test with empty text
        self.assertFalse(contains_vietnamese_compound_words(""))
        self.assertFalse(contains_vietnamese_compound_words(None))

    def test_get_vietnamese_compound_words(self):
        """Test getting Vietnamese compound words."""
        # Test getting all compound words
        all_words = get_vietnamese_compound_words()
        self.assertIsInstance(all_words, list)
        self.assertGreater(len(all_words), 0)
        
        # Test getting domain-specific compound words
        if "medical" in all_words:
            medical_words = get_vietnamese_compound_words("medical")
            self.assertIsInstance(medical_words, list)
            self.assertGreater(len(medical_words), 0)

    def test_translate_to_vietnamese(self):
        """Test translation to Vietnamese."""
        # Test basic translation
        english_text = "Hello world"
        vietnamese_text = translate_to_vietnamese(english_text)
        self.assertIsInstance(vietnamese_text, str)
        self.assertIn(english_text, vietnamese_text)  # Placeholder implementation includes original text

    def test_adapt_prompt_for_vietnamese(self):
        """Test prompt adaptation for Vietnamese."""
        # Test with English transition words
        prompt = "First, let's analyze the problem. Then, we can solve it step by step."
        adapted = adapt_prompt_for_vietnamese(prompt)
        
        # Check if English transition words were replaced
        self.assertIn("Đầu tiên", adapted)
        self.assertIn("Sau đó", adapted)
        self.assertNotIn("First", adapted)
        self.assertNotIn("Then", adapted)
        
        # Test with no transition words
        prompt = "This is a simple sentence without transition words."
        adapted = adapt_prompt_for_vietnamese(prompt)
        self.assertEqual(prompt, adapted)


if __name__ == "__main__":
    unittest.main()
