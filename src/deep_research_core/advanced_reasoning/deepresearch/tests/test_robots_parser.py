"""
Test for robots.txt parser.
"""

import unittest
import sys
import os
from unittest.mock import patch, MagicMock

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.utils.robots_parser import RobotsParser


class TestRobotsParser(unittest.TestCase):
    """Test the robots.txt parser."""
    
    def test_init(self):
        """Test initialization."""
        parser = RobotsParser(
            user_agent="TestBot",
            respect_robots=True,
            cache_ttl=3600,
            max_cache_size=1000
        )
        
        self.assertEqual(parser.user_agent, "TestBot")
        self.assertTrue(parser.respect_robots)
        self.assertEqual(parser.cache_ttl, 3600)
        self.assertEqual(len(parser.parsers), 0)
        self.assertEqual(len(parser.can_fetch_cache), 0)
    
    @patch('requests.get')
    def test_can_fetch_allowed(self, mock_get):
        """Test can_fetch when URL is allowed."""
        # Mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = """
        User-agent: *
        Allow: /allowed
        Disallow: /disallowed
        """
        mock_get.return_value = mock_response
        
        # Create parser
        parser = RobotsParser(user_agent="TestBot")
        
        # Test allowed URL
        result = parser.can_fetch("https://example.com/allowed")
        
        self.assertTrue(result)
        mock_get.assert_called_once()
    
    @patch('requests.get')
    def test_can_fetch_disallowed(self, mock_get):
        """Test can_fetch when URL is disallowed."""
        # Mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = """
        User-agent: *
        Allow: /allowed
        Disallow: /disallowed
        """
        mock_get.return_value = mock_response
        
        # Create parser
        parser = RobotsParser(user_agent="TestBot")
        
        # Test disallowed URL
        result = parser.can_fetch("https://example.com/disallowed")
        
        self.assertFalse(result)
    
    @patch('requests.get')
    def test_can_fetch_no_robots(self, mock_get):
        """Test can_fetch when robots.txt doesn't exist."""
        # Mock response
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_get.return_value = mock_response
        
        # Create parser
        parser = RobotsParser(user_agent="TestBot")
        
        # Test URL
        result = parser.can_fetch("https://example.com/page")
        
        self.assertTrue(result)
    
    @patch('requests.get')
    def test_can_fetch_respect_robots_false(self, mock_get):
        """Test can_fetch when respect_robots is False."""
        # Create parser
        parser = RobotsParser(user_agent="TestBot", respect_robots=False)
        
        # Test URL
        result = parser.can_fetch("https://example.com/disallowed")
        
        self.assertTrue(result)
        mock_get.assert_not_called()
    
    @patch('requests.get')
    def test_can_fetch_cached(self, mock_get):
        """Test can_fetch with caching."""
        # Mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = """
        User-agent: *
        Allow: /allowed
        Disallow: /disallowed
        """
        mock_get.return_value = mock_response
        
        # Create parser
        parser = RobotsParser(user_agent="TestBot")
        
        # First call
        result1 = parser.can_fetch("https://example.com/allowed")
        
        # Second call (should use cache)
        result2 = parser.can_fetch("https://example.com/allowed")
        
        self.assertTrue(result1)
        self.assertTrue(result2)
        mock_get.assert_called_once()
    
    @patch('requests.get')
    def test_crawl_delay(self, mock_get):
        """Test crawl-delay extraction."""
        # Mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = """
        User-agent: *
        Crawl-delay: 5
        Allow: /
        """
        mock_get.return_value = mock_response
        
        # Create parser
        parser = RobotsParser(user_agent="TestBot")
        
        # Test URL
        parser.can_fetch("https://example.com/page")
        
        # Get crawl-delay
        delay = parser.get_crawl_delay("example.com")
        
        # Note: This test might be flaky because the crawl-delay extraction
        # uses internal attributes of RobotFileParser
        self.assertGreaterEqual(delay, 0)
    
    @patch('requests.get')
    def test_blocked_domains(self, mock_get):
        """Test blocked domains."""
        # Mock response for forbidden robots.txt
        mock_response = MagicMock()
        mock_response.status_code = 403
        mock_get.return_value = mock_response
        
        # Create parser
        parser = RobotsParser(user_agent="TestBot")
        
        # Test URL
        result = parser.can_fetch("https://example.com/page")
        
        # Domain should be blocked
        self.assertIn("example.com", parser.blocked_domains)
    
    def test_clear_cache(self):
        """Test clear_cache."""
        # Create parser
        parser = RobotsParser(user_agent="TestBot")
        
        # Add some items to cache
        parser.parsers["example.com"] = "parser"
        parser.parser_timestamps["example.com"] = 123456
        parser.can_fetch_cache["https://example.com:TestBot"] = True
        parser.can_fetch_timestamps["https://example.com:TestBot"] = 123456
        parser.checked_domains.add("example.com")
        parser.blocked_domains.add("blocked.com")
        
        # Clear cache
        parser.clear_cache()
        
        # Check that cache is empty
        self.assertEqual(len(parser.parsers), 0)
        self.assertEqual(len(parser.parser_timestamps), 0)
        self.assertEqual(len(parser.can_fetch_cache), 0)
        self.assertEqual(len(parser.can_fetch_timestamps), 0)
        self.assertEqual(len(parser.checked_domains), 0)
        self.assertEqual(len(parser.blocked_domains), 0)
    
    def test_get_stats(self):
        """Test get_stats."""
        # Create parser
        parser = RobotsParser(user_agent="TestBot")
        
        # Add some items to cache
        parser.parsers["example.com"] = "parser"
        parser.parser_timestamps["example.com"] = 123456
        parser.can_fetch_cache["https://example.com:TestBot"] = True
        parser.can_fetch_timestamps["https://example.com:TestBot"] = 123456
        parser.checked_domains.add("example.com")
        parser.blocked_domains.add("blocked.com")
        parser.crawl_delays["example.com"] = 5.0
        
        # Get stats
        stats = parser.get_stats()
        
        # Check stats
        self.assertEqual(stats["checked_domains"], 1)
        self.assertEqual(stats["blocked_domains"], 1)
        self.assertEqual(stats["parsers_cached"], 1)
        self.assertEqual(stats["can_fetch_cached"], 1)
        self.assertEqual(stats["domains_with_crawl_delay"], 1)
        self.assertEqual(stats["respect_robots"], True)
        self.assertEqual(stats["user_agent"], "TestBot")


if __name__ == '__main__':
    unittest.main()
