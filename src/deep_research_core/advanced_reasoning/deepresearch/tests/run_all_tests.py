#!/usr/bin/env python3
"""
Run all tests in the project.

This script runs all the test modules for the Deep Research Core project.
It provides options for running different types of tests and generating coverage reports.

Usage:
    python run_all_tests.py [options]

Options:
    --unit           Run only unit tests
    --integration    Run only integration tests
    --performance    Run only performance tests
    --coverage       Generate coverage report
    --verbose        Show verbose output
    --parallel       Run tests in parallel
    --benchmark      Run benchmark tests
    --xml            Generate XML report
    --html           Generate HTML report
    --all            Run all tests (default)

Examples:
    python run_all_tests.py --unit --coverage --html
    python run_all_tests.py --integration --verbose
    python run_all_tests.py --performance --benchmark
    python run_all_tests.py --all --parallel --coverage
"""

import unittest
import sys
import os
import argparse
import time
import subprocess
import multiprocessing

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../')))

def parse_arguments():
    """
    Parse command line arguments.

    Returns:
        argparse.Namespace: Parsed arguments
    """
    parser = argparse.ArgumentParser(description='Run tests for Deep Research Core')
    parser.add_argument('--unit', action='store_true', help='Run only unit tests')
    parser.add_argument('--integration', action='store_true', help='Run only integration tests')
    parser.add_argument('--performance', action='store_true', help='Run only performance tests')
    parser.add_argument('--coverage', action='store_true', help='Generate coverage report')
    parser.add_argument('--verbose', action='store_true', help='Show verbose output')
    parser.add_argument('--parallel', action='store_true', help='Run tests in parallel')
    parser.add_argument('--benchmark', action='store_true', help='Run benchmark tests')
    parser.add_argument('--xml', action='store_true', help='Generate XML report')
    parser.add_argument('--html', action='store_true', help='Generate HTML report')
    parser.add_argument('--all', action='store_true', help='Run all tests')
    parser.add_argument('--optimize', action='store_true', help='Enable performance optimizations')
    parser.add_argument('--cache', action='store_true', help='Use test cache')
    parser.add_argument('--timeout', type=int, default=300, help='Set test timeout in seconds')
    parser.add_argument('--workers', type=int, default=0, help='Set number of parallel workers')
    parser.add_argument('--analyze', action='store_true', help='Analyze test performance')

    args = parser.parse_args()

    # If no test type is specified, run all tests
    if not (args.unit or args.integration or args.performance or args.benchmark or args.all):
        args.all = True

    return args

def run_tests_with_pytest(args):
    """
    Run tests using pytest with the specified options.

    Args:
        args (argparse.Namespace): Command line arguments

    Returns:
        int: Exit code (0 for success, non-zero for failure)
    """
    pytest_args = ['pytest']

    # Add verbosity
    if args.verbose:
        pytest_args.append('-v')

    # Add parallel execution
    if args.parallel:
        if args.workers > 0:
            num_cores = args.workers
        else:
            num_cores = max(1, multiprocessing.cpu_count() - 1)  # Leave one core free
        pytest_args.extend(['-n', str(num_cores)])

        # Use load balancing if optimizing
        if args.optimize:
            pytest_args.append('--dist=loadscope')

    # Add optimization options
    if args.optimize:
        pytest_args.extend(['--durations=10', '--durations-min=1.0', '--no-header', '--tb=native'])

    # Add cache options
    if args.cache:
        pytest_args.extend(['--lf', '--cache-clear=false'])

    # Add timeout
    pytest_args.extend(['--timeout', str(args.timeout)])

    # Add coverage
    if args.coverage:
        pytest_args.append('--cov=src/deep_research_core')

        if args.xml:
            pytest_args.append('--cov-report=xml')

        if args.html:
            pytest_args.append('--cov-report=html')

    # Determine which tests to run
    if args.unit:
        pytest_args.append('tests/unit/')
    elif args.integration:
        pytest_args.append('tests/integration/')
    elif args.performance:
        pytest_args.append('-k')
        pytest_args.append('performance')
    elif args.benchmark:
        pytest_args.append('--benchmark-only')
        pytest_args.append('--benchmark-json=./benchmark.json')
    else:  # args.all
        pytest_args.append('tests/')

    # Print the command being run
    print(f"Running: {' '.join(pytest_args)}")

    # Run pytest
    start_time = time.time()
    result = subprocess.run(pytest_args)
    end_time = time.time()

    # Print summary
    print(f"\nTests completed in {end_time - start_time:.2f} seconds")
    print(f"Exit code: {result.returncode}")

    if args.coverage:
        print("\nCoverage report generated")
        if args.html:
            print("HTML report available in htmlcov/index.html")
        if args.xml:
            print("XML report available in coverage.xml")

    if args.benchmark:
        print("\nBenchmark results available in benchmark.json")

    return result.returncode

def run_tests_with_unittest(args):
    """
    Run tests using unittest with the specified options.

    Args:
        args (argparse.Namespace): Command line arguments

    Returns:
        bool: True if all tests passed, False otherwise
    """
    # Determine test directory
    if args.unit:
        start_dir = os.path.join(os.path.dirname(__file__), 'unit')
    elif args.integration:
        start_dir = os.path.join(os.path.dirname(__file__), 'integration')
    else:  # args.all
        start_dir = os.path.dirname(__file__)

    # Discover and run all tests in the specified directory
    test_suite = unittest.defaultTestLoader.discover(
        start_dir=start_dir,
        pattern='test_*.py'
    )

    # Set verbosity
    verbosity = 2 if args.verbose else 1

    # Run the tests
    print(f"Running tests from {start_dir}")
    start_time = time.time()
    result = unittest.TextTestRunner(verbosity=verbosity).run(test_suite)
    end_time = time.time()

    # Print summary
    print(f"\nTests completed in {end_time - start_time:.2f} seconds")
    print(f"Ran {result.testsRun} tests")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Skipped: {len(result.skipped)}")

    return result.wasSuccessful()

def run_test_performance_analysis(args):
    """
    Run test performance analysis.

    Args:
        args (argparse.Namespace): Command line arguments

    Returns:
        int: Exit code (0 for success, non-zero for failure)
    """
    # Run the analyze_test_performance.py script
    cmd = [
        'python',
        os.path.join(os.path.dirname(__file__), '../scripts/analyze_test_performance.py')
    ]

    if args.verbose:
        cmd.append('--verbose')

    # Run the script
    result = subprocess.run(cmd, check=False)
    return result.returncode

if __name__ == '__main__':
    parsed_args = parse_arguments()

    # Run test performance analysis if requested
    if parsed_args.analyze:
        exit_code = run_test_performance_analysis(parsed_args)
        sys.exit(exit_code)

    # Use pytest if coverage, parallel, benchmark, performance tests, or optimization options are requested
    if (parsed_args.coverage or parsed_args.parallel or parsed_args.benchmark or
            parsed_args.performance or parsed_args.xml or parsed_args.html or
            parsed_args.optimize or parsed_args.cache):
        exit_code = run_tests_with_pytest(parsed_args)
        sys.exit(exit_code)
    else:
        # Otherwise use unittest
        success = run_tests_with_unittest(parsed_args)
        sys.exit(not success)
