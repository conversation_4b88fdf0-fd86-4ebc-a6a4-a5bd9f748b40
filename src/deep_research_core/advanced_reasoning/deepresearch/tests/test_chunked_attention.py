"""
Tests for the chunked attention implementation.
"""

import unittest
import torch
import numpy as np
from src.deep_research_core.optimization.memory.chunked_attention import (
    ChunkedAttention,
    apply_chunked_attention,
    estimate_memory_savings
)

class TestChunkedAttention(unittest.TestCase):
    """Test cases for the ChunkedAttention class."""

    def setUp(self):
        """Set up test fixtures."""
        self.batch_size = 2
        self.num_heads = 4
        self.seq_len = 128
        self.head_dim = 64
        self.chunk_size = 32

        # Create random tensors for testing
        self.query = torch.randn(self.batch_size, self.num_heads, self.seq_len, self.head_dim)
        self.key = torch.randn(self.batch_size, self.num_heads, self.seq_len, self.head_dim)
        self.value = torch.randn(self.batch_size, self.num_heads, self.seq_len, self.head_dim)
        self.attention_mask = torch.ones(self.batch_size, 1, self.seq_len, self.seq_len)

        # Create chunked attention instance
        self.chunked_attn = ChunkedAttention(chunk_size=self.chunk_size)

    def test_chunked_attention_shape(self):
        """Test that chunked attention returns the correct shape."""
        context, _ = self.chunked_attn.apply(
            self.query, self.key, self.value, self.attention_mask
        )

        self.assertEqual(
            context.shape,
            (self.batch_size, self.num_heads, self.seq_len, self.head_dim),
            "Chunked attention output shape is incorrect"
        )

    def test_chunked_attention_with_mask(self):
        """Test chunked attention with attention mask."""
        # Create a causal mask (upper triangular)
        causal_mask = torch.triu(
            torch.ones(self.batch_size, 1, self.seq_len, self.seq_len) * -1e9,
            diagonal=1
        )

        context, _ = self.chunked_attn.apply(
            self.query, self.key, self.value, causal_mask
        )

        self.assertEqual(
            context.shape,
            (self.batch_size, self.num_heads, self.seq_len, self.head_dim),
            "Chunked attention with mask output shape is incorrect"
        )

    def test_chunked_attention_with_head_mask(self):
        """Test chunked attention with head mask."""
        # Create a head mask that completely masks out the first head
        head_mask = torch.ones(self.batch_size, self.num_heads, 1, 1)
        head_mask[:, 0] = 0  # Mask the first head

        # Create a reference context without masking
        context_no_mask, _ = self.chunked_attn.apply(
            self.query, self.key, self.value, self.attention_mask
        )

        # Create a context with head masking
        context_with_mask, _ = self.chunked_attn.apply(
            self.query, self.key, self.value, self.attention_mask, head_mask
        )

        self.assertEqual(
            context_with_mask.shape,
            (self.batch_size, self.num_heads, self.seq_len, self.head_dim),
            "Chunked attention with head mask output shape is incorrect"
        )

        # Check that the first head is different from the unmasked version
        # The masked head should be all zeros, so it should be different from the unmasked version
        first_head_diff = (context_with_mask[:, 0] - context_no_mask[:, 0]).abs().sum().item()
        self.assertGreater(
            first_head_diff, 0,
            "Head masking did not have any effect"
        )

        # Check that the first head in the masked version is all zeros
        first_head_sum = context_with_mask[:, 0].abs().sum().item()
        self.assertEqual(
            first_head_sum, 0.0,
            "Head masking did not zero out the first head correctly"
        )

    def test_output_attentions(self):
        """Test that attention weights are returned when requested."""
        _, attention_weights = self.chunked_attn.apply(
            self.query, self.key, self.value,
            self.attention_mask,
            output_attentions=True
        )

        self.assertIsNotNone(
            attention_weights,
            "Attention weights should be returned when output_attentions=True"
        )

        self.assertEqual(
            attention_weights.shape,
            (self.batch_size, self.num_heads, self.seq_len, self.seq_len),
            "Attention weights shape is incorrect"
        )

    def test_memory_savings_estimation(self):
        """Test the memory savings estimation function."""
        savings = estimate_memory_savings(
            batch_size=self.batch_size,
            seq_len=self.seq_len,
            num_heads=self.num_heads,
            head_dim=self.head_dim,
            chunk_size=self.chunk_size
        )

        self.assertIn('standard_attention_mb', savings)
        self.assertIn('chunked_attention_mb', savings)
        self.assertIn('absolute_savings_mb', savings)
        self.assertIn('percentage_savings', savings)

        # Verify that chunked attention uses less memory
        self.assertGreater(
            savings['standard_attention_mb'],
            savings['chunked_attention_mb'],
            "Chunked attention should use less memory than standard attention"
        )

        # Verify percentage savings calculation
        expected_percentage = (1 - (self.chunk_size / self.seq_len)) * 100
        self.assertAlmostEqual(
            savings['percentage_savings'],
            expected_percentage,
            delta=1.0,  # Allow for small floating point differences
            msg="Percentage savings calculation is incorrect"
        )

    def test_apply_chunked_attention(self):
        """Test the apply_chunked_attention function."""
        # Create a mock model with attention modules
        class MockAttentionModule:
            def __init__(self):
                self._attn = self._original_attn

            def _original_attn(self, query, key, value, attention_mask=None, head_mask=None, output_attentions=False):
                # Simple implementation for testing
                attention_scores = torch.matmul(query, key.transpose(-1, -2))
                attention_scores = attention_scores / (query.size(-1) ** 0.5)

                if attention_mask is not None:
                    attention_scores = attention_scores + attention_mask

                attention_probs = torch.nn.functional.softmax(attention_scores, dim=-1)

                if head_mask is not None:
                    attention_probs = attention_probs * head_mask

                context_layer = torch.matmul(attention_probs, value)

                return context_layer, attention_probs if output_attentions else None

        class MockModel:
            def __init__(self):
                self.attention = MockAttentionModule()
                self.self_attention = MockAttentionModule()
                self.cross_attention = MockAttentionModule()

            def named_modules(self):
                return [
                    ('attention', self.attention),
                    ('self_attention', self.self_attention),
                    ('cross_attention', self.cross_attention)
                ]

        model = MockModel()

        # Apply chunked attention
        apply_chunked_attention(model, chunk_size=self.chunk_size)

        # Verify that the attention function was replaced
        self.assertNotEqual(
            model.attention._attn.__func__,
            model.attention._original_attn,
            "Attention function should be replaced"
        )

        self.assertNotEqual(
            model.self_attention._attn.__func__,
            model.self_attention._original_attn,
            "Self-attention function should be replaced"
        )

        self.assertNotEqual(
            model.cross_attention._attn.__func__,
            model.cross_attention._original_attn,
            "Cross-attention function should be replaced"
        )

if __name__ == '__main__':
    unittest.main()
