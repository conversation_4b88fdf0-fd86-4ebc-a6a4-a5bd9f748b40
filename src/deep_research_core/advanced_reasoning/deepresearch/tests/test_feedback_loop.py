"""
Tests for the feedback loop module.

This module contains tests for the feedback loop, which is used to improve
RAG systems based on user feedback.
"""

import os
import json
import unittest
import tempfile
from unittest.mock import patch, MagicMock

import pytest
import numpy as np

from deep_research_core.reasoning.feedback_loop import Feedback<PERSON>oop
from deep_research_core.evaluation.qa_evaluator import QAEvaluator


class TestFeedbackLoop(unittest.TestCase):
    """Tests for the feedback loop."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary directory for feedback data
        self.temp_dir = tempfile.mkdtemp()
        
        # Create a mock evaluator
        self.mock_evaluator = MagicMock(spec=QAEvaluator)
        self.mock_evaluator.evaluate.return_value = {
            "overall_score": 8.5,
            "metrics": {
                "relevance": {"score": 9.0, "explanation": "Relevant"},
                "factual_accuracy": {"score": 8.0, "explanation": "Accurate"}
            }
        }
        
        # Create a feedback loop with the mock evaluator
        self.feedback_loop = FeedbackLoop(
            storage_path=self.temp_dir,
            evaluator=self.mock_evaluator,
            auto_apply_improvements=False,
            min_feedback_count=3,
            language="en"
        )
        
        # Sample data for testing
        self.query_id = "query123"
        self.query = "What is the capital of France?"
        self.answer = "The capital of France is Paris."
        self.documents = [
            {
                "id": "doc1",
                "title": "France",
                "content": "France is a country in Western Europe. Its capital is Paris."
            }
        ]
        self.user_id = "user123"
        
        # Sample feedback
        self.feedback = {
            "rating": 4,
            "comment": "Good answer, but could include more details.",
            "document_relevance": 5
        }
        
        # Mock RAG system
        self.mock_rag_system = MagicMock()
        self.mock_rag_system.top_k = 5
        self.mock_rag_system.reranking_weight = 0.5
        self.mock_rag_system.prompt_template = "Default prompt"
        self.mock_rag_system.use_query_expansion = False

    def tearDown(self):
        """Tear down test fixtures."""
        # Clean up temporary directory
        for file in os.listdir(self.temp_dir):
            os.remove(os.path.join(self.temp_dir, file))
        os.rmdir(self.temp_dir)

    def test_add_feedback(self):
        """Test adding feedback."""
        # Add feedback
        feedback_id = self.feedback_loop.add_feedback(
            query_id=self.query_id,
            query=self.query,
            answer=self.answer,
            documents=self.documents,
            feedback=self.feedback,
            user_id=self.user_id
        )
        
        # Check that the feedback was added
        self.assertIn(feedback_id, self.feedback_loop.feedback_data)
        
        # Check that the feedback contains the expected data
        stored_feedback = self.feedback_loop.feedback_data[feedback_id]
        self.assertEqual(stored_feedback["query_id"], self.query_id)
        self.assertEqual(stored_feedback["query"], self.query)
        self.assertEqual(stored_feedback["answer"], self.answer)
        self.assertEqual(stored_feedback["feedback"], self.feedback)
        self.assertEqual(stored_feedback["user_id"], self.user_id)
        
        # Check that the evaluator was called
        self.mock_evaluator.evaluate.assert_called_once_with(
            self.query, self.answer, self.documents
        )
        
        # Check that the evaluation was stored
        self.assertIn("evaluation", stored_feedback)
        
    def test_get_feedback(self):
        """Test getting feedback by ID."""
        # Add feedback
        feedback_id = self.feedback_loop.add_feedback(
            query_id=self.query_id,
            query=self.query,
            answer=self.answer,
            documents=self.documents,
            feedback=self.feedback,
            user_id=self.user_id
        )
        
        # Get feedback
        feedback = self.feedback_loop.get_feedback(feedback_id)
        
        # Check that the feedback matches the expected data
        self.assertEqual(feedback["query_id"], self.query_id)
        self.assertEqual(feedback["query"], self.query)
        self.assertEqual(feedback["answer"], self.answer)
        self.assertEqual(feedback["feedback"], self.feedback)
        self.assertEqual(feedback["user_id"], self.user_id)
        
    def test_get_feedback_by_query(self):
        """Test getting feedback by query ID."""
        # Add multiple feedback entries for the same query
        feedback_id1 = self.feedback_loop.add_feedback(
            query_id=self.query_id,
            query=self.query,
            answer=self.answer,
            documents=self.documents,
            feedback=self.feedback,
            user_id=self.user_id
        )
        
        feedback_id2 = self.feedback_loop.add_feedback(
            query_id=self.query_id,
            query=self.query,
            answer="Paris is the capital of France.",
            documents=self.documents,
            feedback={"rating": 5, "comment": "Perfect answer"},
            user_id="user456"
        )
        
        # Get feedback by query ID
        feedback_list = self.feedback_loop.get_feedback_by_query(self.query_id)
        
        # Check that both feedback entries were returned
        self.assertEqual(len(feedback_list), 2)
        self.assertIn(self.feedback_loop.feedback_data[feedback_id1], feedback_list)
        self.assertIn(self.feedback_loop.feedback_data[feedback_id2], feedback_list)
        
    def test_analyze_feedback_insufficient_data(self):
        """Test analyzing feedback with insufficient data."""
        # Add a single feedback entry
        self.feedback_loop.add_feedback(
            query_id=self.query_id,
            query=self.query,
            answer=self.answer,
            documents=self.documents,
            feedback=self.feedback,
            user_id=self.user_id
        )
        
        # Analyze feedback
        result = self.feedback_loop.analyze_feedback()
        
        # Check that the result indicates insufficient data
        self.assertEqual(result["status"], "insufficient_data")
        self.assertIn("minimum", result["message"])
        self.assertEqual(len(result["improvements"]), 0)
        
    def test_analyze_feedback_sufficient_data(self):
        """Test analyzing feedback with sufficient data."""
        # Add multiple feedback entries
        for i in range(5):
            self.feedback_loop.add_feedback(
                query_id=f"query{i}",
                query=self.query,
                answer=self.answer,
                documents=self.documents,
                feedback={"rating": 3, "document_relevance": 3, "comment": "Average answer"},
                user_id=f"user{i}"
            )
        
        # Analyze feedback
        result = self.feedback_loop.analyze_feedback()
        
        # Check that the result indicates success
        self.assertEqual(result["status"], "success")
        self.assertIn("improvements", result)
        
    def test_apply_improvements(self):
        """Test applying improvements."""
        # Create improvements to apply
        improvements = [
            {
                "type": "retrieval_count",
                "metric": "relevance",
                "current_score": 6.0,
                "suggested_value": 10,
                "suggestion": "Increase the number of retrieved documents",
                "confidence": 0.8
            },
            {
                "type": "reranking_weight",
                "metric": "factual_accuracy",
                "current_score": 5.0,
                "suggested_value": 0.7,
                "suggestion": "Increase reranking weight",
                "confidence": 0.75
            },
            {
                "type": "unknown_type",
                "metric": "unknown",
                "current_score": 5.0,
                "suggested_value": "unknown",
                "suggestion": "Unknown improvement",
                "confidence": 0.9
            }
        ]
        
        # Apply improvements
        result = self.feedback_loop.apply_improvements(improvements, self.mock_rag_system)
        
        # Check that the result contains the expected categories
        self.assertIn("applied", result)
        self.assertIn("failed", result)
        self.assertIn("skipped", result)
        
        # Check that the correct improvements were applied
        self.assertEqual(len(result["applied"]), 2)
        self.assertEqual(len(result["skipped"]), 1)
        
        # Check that the RAG system was updated
        self.assertEqual(self.mock_rag_system.top_k, 10)
        self.assertEqual(self.mock_rag_system.reranking_weight, 0.7)
        
        # Check that the improvement history was updated
        self.assertEqual(len(self.feedback_loop.improvement_history), 2)
        
    def test_get_improvement_history(self):
        """Test getting improvement history."""
        # Apply improvements
        improvements = [
            {
                "type": "retrieval_count",
                "metric": "relevance",
                "current_score": 6.0,
                "suggested_value": 10,
                "suggestion": "Increase the number of retrieved documents",
                "confidence": 0.8
            }
        ]
        
        self.feedback_loop.apply_improvements(improvements, self.mock_rag_system)
        
        # Get improvement history
        history = self.feedback_loop.get_improvement_history()
        
        # Check that the history contains the applied improvement
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0]["improvement"]["type"], "retrieval_count")
        self.assertEqual(history[0]["old_value"], 5)
        self.assertEqual(history[0]["new_value"], 10)
        
    def test_analyze_by_rating(self):
        """Test analyzing feedback by rating."""
        # Add feedback with low ratings
        for i in range(5):
            self.feedback_loop.add_feedback(
                query_id=f"query{i}",
                query=self.query,
                answer=self.answer,
                documents=self.documents,
                feedback={"rating": 2},
                user_id=f"user{i}"
            )
        
        # Analyze by rating
        improvements = self.feedback_loop._analyze_by_rating()
        
        # Check that improvements were suggested
        self.assertGreater(len(improvements), 0)
        
    def test_analyze_by_document_relevance(self):
        """Test analyzing feedback by document relevance."""
        # Add feedback with low document relevance
        for i in range(5):
            self.feedback_loop.add_feedback(
                query_id=f"query{i}",
                query=self.query,
                answer=self.answer,
                documents=self.documents,
                feedback={"document_relevance": 2},
                user_id=f"user{i}"
            )
        
        # Analyze by document relevance
        improvements = self.feedback_loop._analyze_by_document_relevance()
        
        # Check that improvements were suggested
        self.assertGreater(len(improvements), 0)
        
    def test_analyze_by_query_type(self):
        """Test analyzing feedback by query type."""
        # Add feedback for different query types
        query_types = [
            "What is the capital of France?",
            "How does photosynthesis work?",
            "Why is the sky blue?",
            "Compare democracy and autocracy."
        ]
        
        for i, query in enumerate(query_types):
            self.feedback_loop.add_feedback(
                query_id=f"query{i}",
                query=query,
                answer="Generic answer",
                documents=self.documents,
                feedback={"rating": 2},
                user_id=f"user{i}"
            )
        
        # Analyze by query type
        improvements = self.feedback_loop._analyze_by_query_type()
        
        # Check that improvements were suggested
        self.assertGreater(len(improvements), 0)


if __name__ == "__main__":
    unittest.main()
