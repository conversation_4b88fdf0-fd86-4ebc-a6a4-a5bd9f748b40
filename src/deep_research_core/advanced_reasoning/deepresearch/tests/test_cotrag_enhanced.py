"""
Test script for enhanced CoTRAG.

This script tests the enhanced features of CoTRAG:
- Adaptive parameter adjustment
- Caching
- Result evaluation
"""

import os
import sys
import unittest
from unittest.mock import MagicMock, patch
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.reasoning.cot_rag import CoTRAG
from src.deep_research_core.reasoning.cot_optimization import CoTOptimization, SelectiveCache
from src.deep_research_core.evaluation.cot_evaluator import CoTEvaluator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestCoTRAGEnhanced(unittest.TestCase):
    """Test case for enhanced CoTRAG features."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create mock vector store
        self.mock_vector_store = MagicMock()
        self.mock_vector_store.search.return_value = [
            {
                "content": "This is a test document about artificial intelligence.",
                "metadata": {"source": "Test Source", "title": "Test Document"},
                "score": 0.95
            }
        ]
        
        # Create mock API provider
        self.mock_api_provider = MagicMock()
        self.mock_api_provider.generate.return_value = "First, I'll analyze the information. The document mentions artificial intelligence. Therefore, the answer is that AI is a field of computer science."
        
        # Create patches
        self.patches = [
            patch('src.deep_research_core.reasoning.cot_rag.get_vector_store', return_value=self.mock_vector_store),
            patch('src.deep_research_core.reasoning.cot_rag.openai_provider', self.mock_api_provider),
            patch('src.deep_research_core.reasoning.cot_rag.anthropic_provider', self.mock_api_provider),
            patch('src.deep_research_core.reasoning.cot_rag.openrouter_provider', self.mock_api_provider),
            patch('src.deep_research_core.reasoning.cot_optimization.CoTOptimization.estimate_complexity', return_value=0.75),
            patch('src.deep_research_core.reasoning.cot_optimization.CoTOptimization.adjust_temperature', return_value=0.8),
            patch('src.deep_research_core.reasoning.cot_optimization.CoTOptimization.adjust_max_tokens', return_value=2500)
        ]
        
        # Start patches
        for p in self.patches:
            p.start()
        
        # Create CoTRAG instance with enhanced features
        self.cotrag = CoTRAG(
            provider="openai",
            model="gpt-4o",
            temperature=0.7,
            max_tokens=2000,
            adaptive=True,
            use_cache=True,
            evaluate_results=True
        )
        
        # Mock the evaluator
        self.cotrag.evaluator = MagicMock()
        self.cotrag.evaluator.evaluate.return_value = {
            "metrics": {
                "step_clarity": 8.0,
                "logical_flow": 7.5,
                "evidence_usage": 7.0,
                "conclusion_strength": 8.0,
                "overall_quality": 7.6
            },
            "step_count": 2,
            "latency": 0.5
        }
    
    def tearDown(self):
        """Tear down test fixtures."""
        # Stop patches
        for p in self.patches:
            p.stop()
    
    def test_adaptive_parameters(self):
        """Test adaptive parameter adjustment."""
        # Process a query
        result = self.cotrag.process("What is artificial intelligence?")
        
        # Check that the API was called with adjusted parameters
        self.mock_api_provider.generate.assert_called_once()
        call_args = self.mock_api_provider.generate.call_args[1]
        self.assertEqual(call_args["temperature"], 0.8)
        self.assertEqual(call_args["max_tokens"], 2500)
    
    def test_caching(self):
        """Test result caching."""
        # Process the same query twice
        query = "What is artificial intelligence?"
        self.cotrag.process(query)
        self.cotrag.process(query)
        
        # Check that the API was called only once
        self.assertEqual(self.mock_api_provider.generate.call_count, 1)
        
        # Process with force_refresh
        self.cotrag.process(query, force_refresh=True)
        
        # Check that the API was called again
        self.assertEqual(self.mock_api_provider.generate.call_count, 2)
    
    def test_evaluation(self):
        """Test result evaluation."""
        # Process a query
        result = self.cotrag.process("What is artificial intelligence?")
        
        # Check that evaluation was performed
        self.cotrag.evaluator.evaluate.assert_called_once()
        
        # Check that evaluation results are included in the result
        self.assertIn("evaluation", result)
        self.assertEqual(result["evaluation"]["metrics"]["overall_quality"], 7.6)
    
    def test_disable_features(self):
        """Test disabling enhanced features."""
        # Create CoTRAG instance with disabled features
        cotrag_basic = CoTRAG(
            provider="openai",
            model="gpt-4o",
            adaptive=False,
            use_cache=False,
            evaluate_results=False
        )
        
        # Process a query
        result = cotrag_basic.process("What is artificial intelligence?")
        
        # Check that the API was called with original parameters
        call_args = self.mock_api_provider.generate.call_args[1]
        self.assertEqual(call_args["temperature"], 0.7)
        self.assertEqual(call_args["max_tokens"], 2000)
        
        # Check that evaluation results are not included
        self.assertNotIn("evaluation", result)

if __name__ == '__main__':
    unittest.main()
