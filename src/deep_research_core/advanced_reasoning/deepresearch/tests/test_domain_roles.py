"""
Tests for the domain-specific role templates.
"""

import unittest
from src.deep_research_core.multi_agent.domain_roles import (
    get_domain_role,
    get_available_domains,
    get_available_roles,
    get_all_domain_roles,
    DOMAIN_ROLES
)
from src.deep_research_core.multi_agent.core import AgentRole

class TestDomainRoles(unittest.TestCase):
    """Test cases for the domain-specific role templates."""
    
    def test_get_available_domains(self):
        """Test that get_available_domains returns the correct domains."""
        domains = get_available_domains()
        
        self.assertIsInstance(domains, list)
        self.assertGreater(len(domains), 0)
        
        # Check that all expected domains are present
        expected_domains = ['medical', 'legal', 'technical', 'educational', 
                           'financial', 'vietnamese', 'ai']
        for domain in expected_domains:
            self.assertIn(domain, domains)
    
    def test_get_available_roles(self):
        """Test that get_available_roles returns the correct roles for each domain."""
        # Test medical domain
        medical_roles = get_available_roles('medical')
        self.assertIsInstance(medical_roles, list)
        self.assertGreater(len(medical_roles), 0)
        self.assertIn('physician', medical_roles)
        self.assertIn('researcher', medical_roles)
        self.assertIn('ethicist', medical_roles)
        
        # Test legal domain
        legal_roles = get_available_roles('legal')
        self.assertIsInstance(legal_roles, list)
        self.assertGreater(len(legal_roles), 0)
        self.assertIn('advisor', legal_roles)
        self.assertIn('researcher', legal_roles)
        self.assertIn('compliance', legal_roles)
        
        # Test technical domain
        technical_roles = get_available_roles('technical')
        self.assertIsInstance(technical_roles, list)
        self.assertGreater(len(technical_roles), 0)
        self.assertIn('architect', technical_roles)
        self.assertIn('developer', technical_roles)
        self.assertIn('security', technical_roles)
        
        # Test invalid domain
        with self.assertRaises(ValueError):
            get_available_roles('invalid_domain')
    
    def test_get_domain_role(self):
        """Test that get_domain_role returns the correct role."""
        # Test medical physician role
        physician_role = get_domain_role('medical', 'physician')
        self.assertIsInstance(physician_role, AgentRole)
        self.assertEqual(physician_role.name, "Medical Physician")
        self.assertIn("medical_diagnosis", physician_role.capabilities)
        
        # Test legal advisor role
        advisor_role = get_domain_role('legal', 'advisor')
        self.assertIsInstance(advisor_role, AgentRole)
        self.assertEqual(advisor_role.name, "Legal Advisor")
        self.assertIn("legal_analysis", advisor_role.capabilities)
        
        # Test technical architect role
        architect_role = get_domain_role('technical', 'architect')
        self.assertIsInstance(architect_role, AgentRole)
        self.assertEqual(architect_role.name, "Technical Architect")
        self.assertIn("system_architecture_design", architect_role.capabilities)
        
        # Test case insensitivity
        physician_role_case_insensitive = get_domain_role('MeDiCaL', 'PhYsIcIaN')
        self.assertEqual(physician_role_case_insensitive.name, "Medical Physician")
        
        # Test invalid domain
        with self.assertRaises(ValueError):
            get_domain_role('invalid_domain', 'physician')
        
        # Test invalid role
        with self.assertRaises(ValueError):
            get_domain_role('medical', 'invalid_role')
    
    def test_get_all_domain_roles(self):
        """Test that get_all_domain_roles returns all domain roles."""
        all_roles = get_all_domain_roles()
        
        self.assertIsInstance(all_roles, dict)
        self.assertGreater(len(all_roles), 0)
        
        # Check that all expected domains are present
        expected_domains = ['medical', 'legal', 'technical', 'educational', 
                           'financial', 'vietnamese', 'ai']
        for domain in expected_domains:
            self.assertIn(domain, all_roles)
            self.assertIsInstance(all_roles[domain], dict)
            self.assertGreater(len(all_roles[domain]), 0)
    
    def test_domain_roles_structure(self):
        """Test the structure of the DOMAIN_ROLES dictionary."""
        self.assertIsInstance(DOMAIN_ROLES, dict)
        
        for domain, roles in DOMAIN_ROLES.items():
            self.assertIsInstance(domain, str)
            self.assertIsInstance(roles, dict)
            
            for role_name, role in roles.items():
                self.assertIsInstance(role_name, str)
                self.assertIsInstance(role, AgentRole)
                
                # Check that the role has all required attributes
                self.assertIsInstance(role.name, str)
                self.assertIsInstance(role.description, str)
                self.assertIsInstance(role.capabilities, list)
                self.assertIsInstance(role.knowledge_areas, list)
                self.assertIsInstance(role.system_prompt, str)
                
                # Check that the role has non-empty attributes
                self.assertGreater(len(role.name), 0)
                self.assertGreater(len(role.description), 0)
                self.assertGreater(len(role.capabilities), 0)
                self.assertGreater(len(role.knowledge_areas), 0)
                self.assertGreater(len(role.system_prompt), 0)
    
    def test_vietnamese_roles(self):
        """Test the Vietnamese-specific roles."""
        vietnamese_roles = DOMAIN_ROLES['vietnamese']
        
        self.assertIn('language_specialist', vietnamese_roles)
        self.assertIn('translator', vietnamese_roles)
        self.assertIn('content_creator', vietnamese_roles)
        
        # Test Vietnamese language specialist role
        specialist_role = vietnamese_roles['language_specialist']
        self.assertEqual(specialist_role.name, "Vietnamese Language Specialist")
        self.assertIn("vietnamese_language_analysis", specialist_role.capabilities)
        self.assertIn("vietnamese_linguistics", specialist_role.knowledge_areas)
        
        # Test Vietnamese translator role
        translator_role = vietnamese_roles['translator']
        self.assertEqual(translator_role.name, "Vietnamese Translator")
        self.assertIn("vietnamese_translation", translator_role.capabilities)
        self.assertIn("vietnamese_language", translator_role.knowledge_areas)
        
        # Test Vietnamese content creator role
        creator_role = vietnamese_roles['content_creator']
        self.assertEqual(creator_role.name, "Vietnamese Content Creator")
        self.assertIn("vietnamese_content_creation", creator_role.capabilities)
        self.assertIn("vietnamese_writing_styles", creator_role.knowledge_areas)
    
    def test_ai_roles(self):
        """Test the AI-specific roles."""
        ai_roles = DOMAIN_ROLES['ai']
        
        self.assertIn('researcher', ai_roles)
        self.assertIn('engineer', ai_roles)
        self.assertIn('ethicist', ai_roles)
        
        # Test AI researcher role
        researcher_role = ai_roles['researcher']
        self.assertEqual(researcher_role.name, "AI Researcher")
        self.assertIn("ai_research_design", researcher_role.capabilities)
        self.assertIn("machine_learning", researcher_role.knowledge_areas)
        
        # Test AI engineer role
        engineer_role = ai_roles['engineer']
        self.assertEqual(engineer_role.name, "AI Engineer")
        self.assertIn("ai_system_implementation", engineer_role.capabilities)
        self.assertIn("machine_learning_frameworks", engineer_role.knowledge_areas)
        
        # Test AI ethicist role
        ethicist_role = ai_roles['ethicist']
        self.assertEqual(ethicist_role.name, "AI Ethicist")
        self.assertIn("ethical_analysis", ethicist_role.capabilities)
        self.assertIn("ai_ethics", ethicist_role.knowledge_areas)

if __name__ == '__main__':
    unittest.main()
