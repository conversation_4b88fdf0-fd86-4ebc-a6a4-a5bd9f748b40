"""
Test module for form handling.
"""

import os
import sys
import unittest
import asyncio
from unittest.mock import patch, MagicMock

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))

from deep_research_core.agents.form_handler import FormHandler
from deep_research_core.agents.select_box_handler import SelectBoxHandler

class TestFormHandler(unittest.TestCase):
    """Test cases for FormHandler."""

    def setUp(self):
        """Set up test fixtures."""
        self.form_handler = FormHandler(
            timeout=10,
            wait_time=0.5,
            max_retries=2
        )

    @patch('deep_research_core.agents.form_handler.async_playwright')
    async def test_fill_form_basic(self, mock_playwright):
        """Test basic form filling."""
        # Mock Playwright objects
        mock_browser = MagicMock()
        mock_context = MagicMock()
        mock_page = MagicMock()
        
        # Set up the mock chain
        mock_playwright.return_value.__aenter__.return_value.chromium.launch.return_value = mock_browser
        mock_browser.new_context.return_value = mock_context
        mock_context.new_page.return_value = mock_page
        
        # Mock form field detection
        mock_page.query_selector.return_value = MagicMock()
        mock_page.query_selector.return_value.evaluate.side_effect = ["input", "text"]
        
        # Execute the test
        form_data = {
            "username": "testuser",
            "password": "testpass"
        }
        
        result = await self.form_handler.fill_form(
            page=mock_page,
            form_data=form_data,
            submit=True
        )
        
        # Assertions
        self.assertTrue(result["success"])
        self.assertTrue(result["form_submitted"])
        # Verify that fill was called for each field
        mock_page.query_selector.return_value.fill.assert_called()

    @patch('deep_research_core.agents.form_handler.async_playwright')
    async def test_fill_form_with_select(self, mock_playwright):
        """Test form filling with select box."""
        # Mock Playwright objects
        mock_browser = MagicMock()
        mock_context = MagicMock()
        mock_page = MagicMock()
        mock_select = MagicMock()
        
        # Set up the mock chain
        mock_playwright.return_value.__aenter__.return_value.chromium.launch.return_value = mock_browser
        mock_browser.new_context.return_value = mock_context
        mock_context.new_page.return_value = mock_page
        
        # Mock field detection for different field types
        def mock_query_selector(selector):
            if "username" in selector:
                field = MagicMock()
                field.evaluate.return_value = "input"
                return field
            elif "country" in selector:
                field = MagicMock()
                field.evaluate.return_value = "select"
                return field
            elif "submit" in selector:
                return MagicMock()
            return None
        
        mock_page.query_selector.side_effect = mock_query_selector
        
        # Execute the test
        form_data = {
            "username": "testuser",
            "country": "vietnam"
        }
        
        result = await self.form_handler.fill_form(
            page=mock_page,
            form_data=form_data,
            submit=True,
            submit_selector="button[type='submit']"
        )
        
        # Assertions
        self.assertTrue(result["success"])
        self.assertTrue(result["form_submitted"])

    @patch('deep_research_core.agents.form_handler.async_playwright')
    async def test_fill_form_with_checkbox(self, mock_playwright):
        """Test form filling with checkbox."""
        # Mock Playwright objects
        mock_browser = MagicMock()
        mock_context = MagicMock()
        mock_page = MagicMock()
        mock_checkbox = MagicMock()
        
        # Set up the mock chain
        mock_playwright.return_value.__aenter__.return_value.chromium.launch.return_value = mock_browser
        mock_browser.new_context.return_value = mock_context
        mock_context.new_page.return_value = mock_page
        
        # Mock field detection for checkbox
        def mock_query_selector(selector):
            if "remember" in selector:
                field = MagicMock()
                field.evaluate.side_effect = ["input", "checkbox"]
                field.is_checked.return_value = False
                return field
            return None
        
        mock_page.query_selector.side_effect = mock_query_selector
        
        # Execute the test
        form_data = {
            "remember": True
        }
        
        result = await self.form_handler.fill_form(
            page=mock_page,
            form_data=form_data,
            submit=False
        )
        
        # Assertions
        self.assertTrue(result["success"])
        # Verify that click was called for the checkbox
        mock_page.query_selector.return_value.click.assert_called()

class TestSelectBoxHandler(unittest.TestCase):
    """Test cases for SelectBoxHandler."""

    def setUp(self):
        """Set up test fixtures."""
        self.select_handler = SelectBoxHandler(
            timeout=10,
            wait_time=0.5,
            max_retries=2
        )

    @patch('deep_research_core.agents.select_box_handler.async_playwright')
    async def test_select_option_standard(self, mock_playwright):
        """Test selecting option in standard select box."""
        # Mock Playwright objects
        mock_browser = MagicMock()
        mock_context = MagicMock()
        mock_page = MagicMock()
        mock_select = MagicMock()
        
        # Set up the mock chain
        mock_playwright.return_value.__aenter__.return_value.chromium.launch.return_value = mock_browser
        mock_browser.new_context.return_value = mock_context
        mock_context.new_page.return_value = mock_page
        
        # Mock select element
        mock_page.query_selector.return_value = mock_select
        mock_select.evaluate.return_value = "select"  # It's a standard select
        
        # Mock selected value after selection
        mock_select.evaluate.side_effect = ["select", ["option2"], ["Option 2"]]
        
        # Execute the test
        result = await self.select_handler.select_option(
            page=mock_page,
            selector="#country",
            value="option2"
        )
        
        # Assertions
        self.assertTrue(result["success"])
        self.assertEqual(result["selected_value"], ["option2"])
        self.assertEqual(result["selected_text"], ["Option 2"])
        # Verify that select_option was called
        mock_select.select_option.assert_called_with(value=["option2"])

    @patch('deep_research_core.agents.select_box_handler.async_playwright')
    async def test_select_option_custom(self, mock_playwright):
        """Test selecting option in custom select box."""
        # Mock Playwright objects
        mock_browser = MagicMock()
        mock_context = MagicMock()
        mock_page = MagicMock()
        mock_select = MagicMock()
        mock_option = MagicMock()
        
        # Set up the mock chain
        mock_playwright.return_value.__aenter__.return_value.chromium.launch.return_value = mock_browser
        mock_browser.new_context.return_value = mock_context
        mock_context.new_page.return_value = mock_page
        
        # Mock select element
        mock_page.query_selector.return_value = mock_select
        mock_select.evaluate.return_value = "div"  # It's a custom select
        mock_select.text_content.return_value = "Option 2"
        
        # Mock dropdown options
        mock_page.query_selector_all.return_value = [mock_option]
        mock_option.text_content.return_value = "Option 2"
        mock_option.get_attribute.return_value = "option2"
        
        # Execute the test
        result = await self.select_handler.select_option(
            page=mock_page,
            selector=".custom-select",
            value="option2"
        )
        
        # Assertions
        self.assertTrue(result["success"])
        self.assertEqual(result["selected_text"], "Option 2")
        # Verify that click was called on the select and option
        mock_select.click.assert_called()
        mock_option.click.assert_called()

    @patch('deep_research_core.agents.select_box_handler.async_playwright')
    async def test_get_all_options(self, mock_playwright):
        """Test getting all options from select box."""
        # Mock Playwright objects
        mock_browser = MagicMock()
        mock_context = MagicMock()
        mock_page = MagicMock()
        mock_select = MagicMock()
        
        # Set up the mock chain
        mock_playwright.return_value.__aenter__.return_value.chromium.launch.return_value = mock_browser
        mock_browser.new_context.return_value = mock_context
        mock_context.new_page.return_value = mock_page
        
        # Mock select element
        mock_page.query_selector.return_value = mock_select
        mock_select.evaluate.side_effect = [
            "select",  # It's a standard select
            [
                {"value": "option1", "text": "Option 1", "disabled": False, "selected": True},
                {"value": "option2", "text": "Option 2", "disabled": False, "selected": False},
                {"value": "option3", "text": "Option 3", "disabled": True, "selected": False}
            ]
        ]
        
        # Execute the test
        result = await self.select_handler.get_all_options(
            page=mock_page,
            selector="#country"
        )
        
        # Assertions
        self.assertTrue(result["success"])
        self.assertEqual(len(result["options"]), 3)
        self.assertEqual(result["options"][0]["value"], "option1")
        self.assertEqual(result["options"][0]["text"], "Option 1")
        self.assertTrue(result["options"][0]["selected"])
        self.assertTrue(result["options"][2]["disabled"])

if __name__ == '__main__':
    # Run the async tests
    unittest.main()
