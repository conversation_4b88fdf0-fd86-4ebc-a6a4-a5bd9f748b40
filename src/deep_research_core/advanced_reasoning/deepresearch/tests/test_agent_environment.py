"""
Tests for the agent environment module.

This module contains tests for the agent environment, including action execution
and reward calculation.
"""

import os
import json
import unittest
from unittest.mock import patch, MagicMock

import pytest
import numpy as np

from deep_research_core.rl_tuning.environment.agent_environment import AgentEnvironment
from deep_research_core.rl_tuning.environment.reward_shaping import <PERSON><PERSON><PERSON><PERSON><PERSON>, MultiObjectiveReward


class TestAgentEnvironment(unittest.TestCase):
    """Tests for the agent environment."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a test environment
        self.env = AgentEnvironment(
            name="test_environment",
            max_steps_per_episode=10,
            verbose=False
        )
        
        # Reset the environment to initialize state
        self.env.reset(query="Test query")

    def test_execute_action_respond(self):
        """Test executing a respond action."""
        # Create a respond action
        action = {
            "action_type": "respond",
            "content": "This is a test response."
        }
        
        # Execute the action
        info = self.env._execute_action(action)
        
        # Check that the action was executed
        self.assertTrue(info["action_executed"])
        self.assertTrue(info["response_processed"])
        self.assertEqual(info["response_content"], "This is a test response.")
        
        # Check that the state was updated
        self.assertEqual(self.env.current_state["last_response"], "This is a test response.")
        
    def test_execute_action_think(self):
        """Test executing a think action."""
        # Create a think action
        action = {
            "action_type": "think",
            "content": "Let me think about this problem step by step."
        }
        
        # Execute the action
        info = self.env._execute_action(action)
        
        # Check that the action was executed
        self.assertTrue(info["action_executed"])
        self.assertTrue(info["thinking_processed"])
        self.assertEqual(info["thinking_content"], "Let me think about this problem step by step.")
        
        # Check that the state was updated
        self.assertIn("thinking_steps", self.env.current_state)
        self.assertEqual(len(self.env.current_state["thinking_steps"]), 1)
        self.assertEqual(self.env.current_state["thinking_steps"][0], "Let me think about this problem step by step.")
        
    def test_execute_action_use_tool(self):
        """Test executing a use_tool action."""
        # Mock the _execute_tool method
        self.env._execute_tool = MagicMock(return_value={"success": True, "result": "Tool result"})
        
        # Create a use_tool action
        action = {
            "action_type": "use_tool",
            "tool_name": "test_tool",
            "tool_input": {"param": "value"}
        }
        
        # Execute the action
        info = self.env._execute_action(action)
        
        # Check that the action was executed
        self.assertTrue(info["action_executed"])
        self.assertEqual(info["tool_used"], "test_tool")
        self.assertEqual(info["tool_input"], {"param": "value"})
        
        # Check that the tool was executed
        self.env._execute_tool.assert_called_once_with("test_tool", {"param": "value"})
        
        # Check that the state was updated
        self.assertIn("tools_used", self.env.current_state)
        self.assertEqual(len(self.env.current_state["tools_used"]), 1)
        self.assertEqual(self.env.current_state["tools_used"][0], "test_tool")
        self.assertEqual(self.env.current_state["last_tool_result"], {"success": True, "result": "Tool result"})
        self.assertEqual(self.env.current_state["last_successful_tool"], "test_tool")
        
    def test_execute_action_query(self):
        """Test executing a query action."""
        # Create a query action
        action = {
            "action_type": "query",
            "content": "What is the capital of France?"
        }
        
        # Execute the action
        info = self.env._execute_action(action)
        
        # Check that the action was executed
        self.assertTrue(info["action_executed"])
        self.assertTrue(info["query_processed"])
        self.assertEqual(info["query_text"], "What is the capital of France?")
        
        # Check that the state was updated
        self.assertEqual(self.env.current_state["last_query"], "What is the capital of France?")
        self.assertIn("queries", self.env.current_state)
        self.assertEqual(len(self.env.current_state["queries"]), 1)
        self.assertEqual(self.env.current_state["queries"][0], "What is the capital of France?")
        
    def test_execute_action_search(self):
        """Test executing a search action."""
        # Create a search action
        action = {
            "action_type": "search",
            "content": "capital of France"
        }
        
        # Execute the action
        info = self.env._execute_action(action)
        
        # Check that the action was executed
        self.assertTrue(info["action_executed"])
        self.assertTrue(info["search_processed"])
        self.assertEqual(info["search_query"], "capital of France")
        
        # Check that the state was updated
        self.assertEqual(self.env.current_state["last_search"], "capital of France")
        self.assertIn("searches", self.env.current_state)
        self.assertEqual(len(self.env.current_state["searches"]), 1)
        self.assertEqual(self.env.current_state["searches"][0], "capital of France")
        
    def test_execute_action_unknown(self):
        """Test executing an unknown action type."""
        # Create an action with unknown type
        action = {
            "action_type": "unknown",
            "content": "This is an unknown action."
        }
        
        # Execute the action
        info = self.env._execute_action(action)
        
        # Check that the action was executed but marked as unknown
        self.assertTrue(info["action_executed"])
        self.assertEqual(info["unknown_action_type"], "unknown")
        
    def test_infer_action_type(self):
        """Test inferring action type from content."""
        # Test inferring respond
        action = {"content": "This is a simple response."}
        self.assertEqual(self.env._infer_action_type(action), "respond")
        
        # Test inferring think
        action = {"content": "Let me think about this problem step by step."}
        self.assertEqual(self.env._infer_action_type(action), "think")
        
        # Test inferring use_tool
        action = {"content": "Using a tool", "tool_name": "test_tool"}
        self.assertEqual(self.env._infer_action_type(action), "use_tool")
        
        # Test inferring query
        action = {"content": "query: What is the capital of France?"}
        self.assertEqual(self.env._infer_action_type(action), "query")
        
        # Test inferring search
        action = {"content": "search: capital of France"}
        self.assertEqual(self.env._infer_action_type(action), "search")
        
    def test_analyze_response_quality(self):
        """Test analyzing response quality."""
        # Test empty response
        metrics = self.env._analyze_response_quality("")
        self.assertTrue(metrics["empty_response"])
        
        # Test short response
        metrics = self.env._analyze_response_quality("Short.")
        self.assertTrue(metrics["very_short_response"])
        
        # Test well-structured response
        response = "This is paragraph 1.\n\nThis is paragraph 2.\n\nThis is paragraph 3."
        metrics = self.env._analyze_response_quality(response)
        self.assertTrue(metrics["well_structured"])
        
        # Test response with formatting
        response = "# Title\n\n- Item 1\n- Item 2\n\n```python\nprint('Hello')\n```"
        metrics = self.env._analyze_response_quality(response)
        self.assertTrue(metrics["contains_code_blocks"])
        self.assertTrue(metrics["contains_bullet_list"])
        
    def test_analyze_thinking_quality(self):
        """Test analyzing thinking quality."""
        # Test empty thinking
        metrics = self.env._analyze_thinking_quality("")
        self.assertTrue(metrics["empty_thinking"])
        
        # Test short thinking
        metrics = self.env._analyze_thinking_quality("Short thinking.")
        self.assertTrue(metrics["very_short_thinking"])
        
        # Test deep thinking
        thinking = "First, I need to consider the problem. However, there are multiple approaches. If we use method A, then we get result X. But if we use method B, then we get result Y. Therefore, method B is better."
        metrics = self.env._analyze_thinking_quality(thinking)
        self.assertTrue(metrics["deep_thinking"])
        self.assertTrue(metrics["structured_thinking"])
        self.assertTrue(metrics["logical_reasoning"])
        
    def test_analyze_tool_usage_pattern(self):
        """Test analyzing tool usage pattern."""
        # Test empty tool usage
        metrics = self.env._analyze_tool_usage_pattern([])
        self.assertEqual(metrics, {})
        
        # Test repetitive tool usage
        metrics = self.env._analyze_tool_usage_pattern(["tool1", "tool1", "tool1"])
        self.assertTrue(metrics["repetitive_tool_usage"])
        self.assertTrue(metrics["inefficient_tool_pattern"])
        
        # Test efficient pattern
        metrics = self.env._analyze_tool_usage_pattern(["search", "read_file"])
        self.assertTrue(metrics["efficient_search_read_pattern"])
        
        # Test diverse tool usage
        metrics = self.env._analyze_tool_usage_pattern(["tool1", "tool2", "tool3", "tool4"])
        self.assertTrue(metrics["diverse_tool_usage"])
        self.assertEqual(metrics["unique_tool_count"], 4)
        
    def test_check_query_similarity(self):
        """Test checking query similarity."""
        # Test empty queries
        similarity = self.env._check_query_similarity("query", [])
        self.assertEqual(similarity, 0.0)
        
        # Test similar queries
        similarity = self.env._check_query_similarity(
            "What is the capital of France?",
            ["What is the population of France?", "What is the capital of France?"]
        )
        self.assertGreater(similarity, 0.5)
        
        # Test different queries
        similarity = self.env._check_query_similarity(
            "What is the capital of France?",
            ["What is the capital of Germany?", "What is the population of Italy?"]
        )
        self.assertLess(similarity, 0.5)
        
    def test_default_reward_function(self):
        """Test the default reward function."""
        # Create states and action
        state = {"step_count": 1}
        action = {"action_type": "respond", "content": "Test response"}
        next_state = {"step_count": 2, "is_success": True}
        
        # Calculate reward
        reward = self.env._default_reward_function(state, action, next_state)
        
        # Check that reward is a float
        self.assertIsInstance(reward, float)
        
        # Check that reward is within expected range
        self.assertGreaterEqual(reward, -1.0)
        self.assertLessEqual(reward, 1.0)


class TestRewardShaping(unittest.TestCase):
    """Tests for the reward shaping module."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a reward shaper
        self.reward_shaper = RewardShaper(
            objectives={
                "task_completion": 1.0,
                "efficiency": 0.5,
                "quality": 0.7,
                "exploration": 0.3
            },
            adaptive=True,
            verbose=False
        )

    def test_calculate_reward(self):
        """Test calculating reward."""
        # Create states and action
        state = {"step_count": 1}
        action = {"action_type": "respond", "content": "Test response"}
        next_state = {"step_count": 2, "is_success": True}
        
        # Calculate reward
        reward = self.reward_shaper.calculate_reward(state, action, next_state)
        
        # Check that reward is a float
        self.assertIsInstance(reward, float)
        
        # Check that reward is within expected range
        self.assertGreaterEqual(reward, -1.0)
        self.assertLessEqual(reward, 1.0)
        
    def test_adapt_objectives(self):
        """Test adapting objectives."""
        # Add some reward history
        for _ in range(10):
            self.reward_shaper.reward_history["task_completion"].append(1.0)
            self.reward_shaper.reward_history["efficiency"].append(0.5)
            self.reward_shaper.reward_history["quality"].append(0.7)
            self.reward_shaper.reward_history["exploration"].append(0.3)
        
        # Adapt objectives
        objectives = self.reward_shaper.adapt_objectives(performance_metric=1.0)
        
        # Check that objectives were adapted
        self.assertIsInstance(objectives, dict)
        self.assertEqual(len(objectives), 4)
        
        # Check that weights are still positive
        for weight in objectives.values():
            self.assertGreater(weight, 0.0)
            
    def test_reset(self):
        """Test resetting the reward shaper."""
        # Add some episode rewards
        self.reward_shaper.episode_rewards = [0.1, 0.2, 0.3]
        
        # Reset
        self.reward_shaper.reset()
        
        # Check that episode rewards were reset
        self.assertEqual(len(self.reward_shaper.episode_rewards), 0)


class TestMultiObjectiveReward(unittest.TestCase):
    """Tests for the multi-objective reward function."""

    def setUp(self):
        """Set up test fixtures."""
        # Create reward functions
        def reward_func1(state, action, next_state):
            return 0.5
            
        def reward_func2(state, action, next_state):
            return -0.2
        
        # Create a multi-objective reward
        self.multi_reward = MultiObjectiveReward(
            reward_functions=[
                (reward_func1, 0.7),
                (reward_func2, 0.3)
            ],
            use_normalization=True,
            verbose=False
        )

    def test_calculate_reward(self):
        """Test calculating reward."""
        # Create states and action
        state = {"step_count": 1}
        action = {"action_type": "respond", "content": "Test response"}
        next_state = {"step_count": 2}
        
        # Calculate reward
        reward = self.multi_reward.calculate_reward(state, action, next_state)
        
        # Check that reward is a float
        self.assertIsInstance(reward, float)
        
        # Check that reward is within expected range
        self.assertGreaterEqual(reward, -1.0)
        self.assertLessEqual(reward, 1.0)
        
        # Check that reward is close to expected value (0.5 * 0.7 + -0.2 * 0.3) / (0.7 + 0.3) = 0.29
        self.assertAlmostEqual(reward, 0.29, places=1)
        
    def test_add_reward_function(self):
        """Test adding a reward function."""
        # Define a new reward function
        def reward_func3(state, action, next_state):
            return 0.8
            
        # Add the reward function
        self.multi_reward.add_reward_function(reward_func3, 0.5)
        
        # Check that the reward function was added
        self.assertEqual(len(self.multi_reward.reward_functions), 3)
        
        # Calculate reward with the new function
        state = {"step_count": 1}
        action = {"action_type": "respond", "content": "Test response"}
        next_state = {"step_count": 2}
        
        reward = self.multi_reward.calculate_reward(state, action, next_state)
        
        # Check that reward is close to expected value
        # (0.5 * 0.7 + -0.2 * 0.3 + 0.8 * 0.5) / (0.7 + 0.3 + 0.5) = 0.42
        self.assertAlmostEqual(reward, 0.42, places=1)
        
    def test_reset(self):
        """Test resetting the multi-objective reward."""
        # Add some reward history
        self.multi_reward.reward_history = [0.1, 0.2, 0.3]
        
        # Reset
        self.multi_reward.reset()
        
        # Check that reward history was reset
        self.assertEqual(len(self.multi_reward.reward_history), 0)


if __name__ == "__main__":
    unittest.main()
