import os
import sys
import unittest
from unittest.mock import MagicMock, patch
import tempfile
import shutil
import json

# Thêm đường dẫn thư mục gốc vào sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.agents.adaptive_crawler import AdaptiveCrawler


class TestInfiniteScroll(unittest.TestCase):
    """
    Test case cho tính năng xử lý infinite scroll trong AdaptiveCrawler
    """

    def setUp(self):
        """
        Thiết lập môi trường test
        """
        # Tạo thư mục tạm để lưu file
        self.temp_dir = tempfile.mkdtemp()
        
        # Tạo AdaptiveCrawler với các tham số infinite scroll
        self.crawler = AdaptiveCrawler(
            download_path=self.temp_dir,
            enable_javascript=True,
            wait_for_selector=".content",
            wait_for_timeout=2000,
            handle_infinite_scroll=True,
            infinite_scroll_max_scrolls=5,
            infinite_scroll_timeout=1000
        )

    def tearDown(self):
        """
        Dọn dẹp sau khi test
        """
        # <PERSON><PERSON><PERSON> thư mục tạm
        shutil.rmtree(self.temp_dir)

    @patch('playwright.sync_api.sync_playwright')
    def test_handle_infinite_scroll(self, mock_playwright):
        """
        Test phương thức _handle_infinite_scroll
        """
        # Tạo mock cho Playwright
        mock_context = MagicMock()
        mock_page = MagicMock()
        mock_browser = MagicMock()
        
        # Thiết lập mock cho evaluate để trả về True cho has_infinite_scroll
        mock_page.evaluate.side_effect = [
            True,  # has_infinite_scroll
            100,   # before_height lần 1
            None,  # cuộn xuống lần 1
            200,   # after_height lần 1
            200,   # before_height lần 2
            None,  # cuộn xuống lần 2
            300,   # after_height lần 2
            300,   # before_height lần 3
            None,  # cuộn xuống lần 3
            400,   # after_height lần 3
            400,   # before_height lần 4
            None,  # cuộn xuống lần 4
            500,   # after_height lần 4
            500,   # before_height lần 5
            None,  # cuộn xuống lần 5
            500,   # after_height lần 5 (không thay đổi, dừng cuộn)
            None   # cuộn lên đầu trang
        ]
        
        # Thiết lập mock cho browser và context
        mock_browser.new_context.return_value = mock_context
        mock_context.new_page.return_value = mock_page
        
        # Thiết lập mock cho playwright
        mock_playwright_instance = MagicMock()
        mock_playwright_instance.chromium.launch.return_value = mock_browser
        mock_playwright.return_value.__enter__.return_value = mock_playwright_instance
        
        # Gọi phương thức _handle_infinite_scroll
        self.crawler._handle_infinite_scroll(mock_page, "https://example.com")
        
        # Kiểm tra xem các phương thức đã được gọi đúng cách
        mock_page.wait_for_timeout.assert_any_call(1000)  # Đợi JavaScript thực thi
        
        # Kiểm tra xem phương thức evaluate đã được gọi để kiểm tra infinite scroll
        mock_page.evaluate.assert_any_call("""() => {
                // Kiểm tra các dấu hiệu của infinite scroll
                return !!(
                    // Kiểm tra các thuộc tính phổ biến
                    document.querySelector('.infinite-scroll') ||
                    document.querySelector('[data-infinite-scroll]') ||
                    document.querySelector('.load-more') ||
                    
                    // Kiểm tra các thư viện phổ biến
                    (window.jQuery && window.jQuery.fn.infiniteScroll) ||
                    window.InfiniteScroll ||
                    
                    // Kiểm tra các thuộc tính của React, Vue, Angular
                    document.querySelector('[data-scroll="infinite"]') ||
                    document.querySelector('[v-infinite-scroll]') ||
                    document.querySelector('[ng-infinite-scroll]')
                );
            }""")
        
        # Kiểm tra xem phương thức cuộn trang đã được gọi đúng số lần
        # Chúng ta mong đợi 5 lần cuộn xuống và 1 lần cuộn lên
        self.assertEqual(mock_page.evaluate.call_count, 17)
        
        # Kiểm tra xem phương thức wait_for_timeout đã được gọi sau mỗi lần cuộn
        self.assertEqual(mock_page.wait_for_timeout.call_count, 6)  # 1 lần ban đầu + 5 lần sau mỗi lần cuộn

    @patch('playwright.sync_api.sync_playwright')
    def test_crawl_with_infinite_scroll(self, mock_playwright):
        """
        Test tính năng crawl với infinite scroll
        """
        # Tạo mock cho Playwright
        mock_context = MagicMock()
        mock_page = MagicMock()
        mock_browser = MagicMock()
        
        # Thiết lập mock cho page
        mock_page.title.return_value = "Test Infinite Scroll Page"
        mock_page.content.return_value = "<html><body><div class='content'>Test content with infinite scroll</div></body></html>"
        
        # Thiết lập mock cho evaluate để trả về True cho has_infinite_scroll
        mock_page.evaluate.side_effect = [
            True,  # has_infinite_scroll
            100,   # before_height lần 1
            None,  # cuộn xuống lần 1
            200,   # after_height lần 1
            None,  # cuộn lên đầu trang
            "Test content extracted with infinite scroll"  # Nội dung trích xuất
        ]
        
        # Thiết lập mock cho browser và context
        mock_browser.new_context.return_value = mock_context
        mock_context.new_page.return_value = mock_page
        
        # Thiết lập mock cho playwright
        mock_playwright_instance = MagicMock()
        mock_playwright_instance.chromium.launch.return_value = mock_browser
        mock_playwright.return_value.__enter__.return_value = mock_playwright_instance
        
        # Gọi phương thức crawl
        result = self.crawler.crawl(
            urls=["https://example.com/infinite-scroll"]
        )
        
        # Kiểm tra kết quả
        self.assertTrue(result["success"])
        self.assertEqual(len(result["results"]), 1)
        self.assertEqual(result["results"][0]["title"], "Test Infinite Scroll Page")
        
        # Kiểm tra xem các tham số infinite scroll đã được sử dụng
        mock_page.wait_for_timeout.assert_called()
        
        # Lưu kết quả ra file để kiểm tra
        result_path = os.path.join(self.temp_dir, "infinite_scroll_crawl_result.json")
        with open(result_path, "w", encoding="utf-8") as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
            
        print(f"Đã lưu kết quả crawl vào: {result_path}")


if __name__ == '__main__':
    unittest.main()
