"""
Test the RAGTOTCOTWeightOptimizer.

This module tests the automatic weight adjustment functionality for RAG-TOT-COT.
"""

import unittest
from unittest.mock import patch, MagicMock
import numpy as np
import sys
import os

# Add the parent directory to the path so we can import the modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from deep_research_core.reasoning.ragtotcot_weight_optimizer import RAGTOTCOTWeightOptimizer
from deep_research_core.reasoning.rag_tot_cot import RAGTOTCOTReasoner


class TestRAGTOTCOTWeightOptimizer(unittest.TestCase):
    """Test the RAGTOTCOTWeightOptimizer class."""

    def setUp(self):
        """Set up the test environment."""
        self.optimizer = RAGTOTCOTWeightOptimizer(
            default_rag_weight=0.4,
            default_tot_weight=0.3,
            default_cot_weight=0.3,
            min_weight=0.1,
            max_weight=0.7,
            learning_rate=0.05,
            use_historical_data=True,
            max_history_size=100,
            verbose=False
        )

    def test_initialization(self):
        """Test the initialization of the optimizer."""
        self.assertEqual(self.optimizer.default_weights["rag"], 0.4)
        self.assertEqual(self.optimizer.default_weights["tot"], 0.3)
        self.assertEqual(self.optimizer.default_weights["cot"], 0.3)
        self.assertEqual(self.optimizer.min_weight, 0.1)
        self.assertEqual(self.optimizer.max_weight, 0.7)
        self.assertEqual(self.optimizer.learning_rate, 0.05)
        self.assertTrue(self.optimizer.use_historical_data)
        self.assertEqual(self.optimizer.max_history_size, 100)
        self.assertFalse(self.optimizer.verbose)

    def test_classify_query(self):
        """Test the query classification."""
        # Test factual query
        query = "What is the capital of Vietnam?"
        query_type = self.optimizer.classify_query(query)
        self.assertEqual(query_type, "factual")

        # Test analytical query
        query = "Analyze the impact of climate change on agriculture in Vietnam"
        query_type = self.optimizer.classify_query(query)
        self.assertEqual(query_type, "analytical")

    def test_get_weights_for_query(self):
        """Test getting weights for a query."""
        # Test factual query
        query = "What is the capital of Vietnam?"
        weights = self.optimizer.get_weights_for_query(query)
        self.assertIn("rag", weights)
        self.assertIn("tot", weights)
        self.assertIn("cot", weights)
        self.assertAlmostEqual(sum(weights.values()), 1.0, places=5)

        # Test analytical query
        query = "Analyze the impact of climate change on agriculture in Vietnam"
        weights = self.optimizer.get_weights_for_query(query)
        self.assertIn("rag", weights)
        self.assertIn("tot", weights)
        self.assertIn("cot", weights)
        self.assertAlmostEqual(sum(weights.values()), 1.0, places=5)

    def test_update_weights_from_feedback(self):
        """Test updating weights from feedback."""
        query = "What is the capital of Vietnam?"
        query_type = "factual"

        # Initial weights
        initial_weights = self.optimizer.get_weights_for_query(query)

        # Provide feedback
        feedback = {"rag": 0.8, "tot": 0.3, "cot": 0.5}
        updated_weights = self.optimizer.update_weights_from_feedback(
            query=query,
            feedback=feedback,
            query_type=query_type
        )

        # Check that weights have been updated
        self.assertNotEqual(initial_weights["rag"], updated_weights["rag"])
        self.assertNotEqual(initial_weights["tot"], updated_weights["tot"])
        self.assertNotEqual(initial_weights["cot"], updated_weights["cot"])

        # Check that weights sum to 1
        self.assertAlmostEqual(sum(updated_weights.values()), 1.0, places=5)

        # Check that weights are within bounds
        for weight in updated_weights.values():
            self.assertGreaterEqual(weight, self.optimizer.min_weight)
            self.assertLessEqual(weight, self.optimizer.max_weight)

    def test_get_optimal_strategy(self):
        """Test getting the optimal strategy."""
        query = "What is the capital of Vietnam?"
        strategy = self.optimizer.get_optimal_strategy(query)

        self.assertIn("use_rag", strategy)
        self.assertIn("use_tot", strategy)
        self.assertIn("use_cot", strategy)
        self.assertIn("weights", strategy)
        self.assertIn("query_type", strategy)

        self.assertIsInstance(strategy["use_rag"], bool)
        self.assertIsInstance(strategy["use_tot"], bool)
        self.assertIsInstance(strategy["use_cot"], bool)
        self.assertIsInstance(strategy["weights"], dict)
        self.assertIsInstance(strategy["query_type"], str)


class TestRAGTOTCOTReasonerWithWeightOptimizer(unittest.TestCase):
    """Test the RAGTOTCOTReasoner with weight optimizer."""

    def setUp(self):
        """Set up the test environment."""
        # Mock the RAG system
        self.mock_rag = MagicMock()
        self.mock_rag.query.return_value = [{"content": "Test content", "source": "Test source"}]

        # Create the reasoner
        self.reasoner = RAGTOTCOTReasoner(
            rag_system=self.mock_rag,
            provider="openai",
            model="gpt-4",
            use_weight_optimizer=True,
            use_query_classifier=True,
            verbose=False
        )

        # Mock the TOT and COT reasoners
        self.reasoner.tot.reason = MagicMock(return_value={
            "query": "test query",
            "reasoning": "test reasoning",
            "answer": "test answer",
            "model": "gpt-4",
            "provider": "openai",
            "latency": 1.0
        })

        self.reasoner.cot.reason = MagicMock(return_value={
            "reasoning": "test reasoning",
            "answer": "test answer",
            "latency": 1.0
        })

    @patch('deep_research_core.reasoning.ragtotcot_weight_optimizer.RAGTOTCOTWeightOptimizer.get_weights_for_query')
    def test_reason_with_weight_optimizer(self, mock_get_weights):
        """Test reasoning with weight optimizer."""
        # Mock the weight optimizer
        mock_get_weights.return_value = {"rag": 0.6, "tot": 0.2, "cot": 0.2}

        # Test reasoning
        query = "What is the capital of Vietnam?"
        result = self.reasoner.reason(query)

        # Check that the result contains the expected keys
        self.assertIn("answer", result)
        self.assertIn("weights_used", result)
        self.assertIn("query_type", result)
        self.assertIn("methods_used", result)

        # Check that the weights were used
        self.assertEqual(result["weights_used"]["rag"], 0.6)
        self.assertEqual(result["weights_used"]["tot"], 0.2)
        self.assertEqual(result["weights_used"]["cot"], 0.2)

    def test_provide_feedback(self):
        """Test providing feedback."""
        query = "What is the capital of Vietnam?"
        feedback = {"rag": 0.8, "tot": 0.3, "cot": 0.5}

        # Mock the weight optimizer
        self.reasoner.weight_optimizer.update_weights_from_feedback = MagicMock(
            return_value={"rag": 0.5, "tot": 0.25, "cot": 0.25}
        )

        # Test providing feedback
        updated_weights = self.reasoner.provide_feedback(query, feedback)

        # Check that the weights were updated
        self.assertEqual(updated_weights["rag"], 0.5)
        self.assertEqual(updated_weights["tot"], 0.25)
        self.assertEqual(updated_weights["cot"], 0.25)

        # Check that the weight optimizer was called
        self.reasoner.weight_optimizer.update_weights_from_feedback.assert_called_once_with(
            query=query,
            feedback=feedback,
            query_type=None
        )


if __name__ == '__main__':
    unittest.main()
