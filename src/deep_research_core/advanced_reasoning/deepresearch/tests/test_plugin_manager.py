"""
Unit test cho PluginManager.

Module n<PERSON><PERSON> ch<PERSON><PERSON> c<PERSON>c test case cho PluginManager.
"""

import unittest
import os
import sys
import tempfile
from unittest.mock import MagicMock, patch

# Th<PERSON><PERSON> thư mục cha vào sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.deep_research_core.utils.plugin_manager import PluginManager, hook
from src.deep_research_core.plugins.base_plugin import BasePlugin


class TestPlugin(BasePlugin):
    """Plugin test."""

    VERSION = "1.0.0"
    AUTHOR = "Test Author"
    DEPENDENCIES = []

    def __init__(self, config=None):
        """Khởi tạo plugin test."""
        super().__init__(config)
        self.pre_search_called = False
        self.post_search_called = False

    @hook(priority=10)
    def pre_search(self, query, **kwargs):
        """Hook pre_search."""
        self.pre_search_called = True
        return {"query": f"modified_{query}", "original_query": query}

    @hook(priority=20)
    def post_search(self, results, **kwargs):
        """Hook post_search."""
        self.post_search_called = True
        if isinstance(results, list):
            return {"results": results, "modified": True}
        elif isinstance(results, dict) and "results" in results:
            return results
        else:
            return {"results": results, "modified": True}


class AnotherTestPlugin(BasePlugin):
    """Plugin test khác."""

    VERSION = "1.0.0"
    AUTHOR = "Test Author"
    DEPENDENCIES = []

    def __init__(self, config=None):
        """Khởi tạo plugin test khác."""
        super().__init__(config)
        self.pre_search_called = False
        self.post_search_called = False

    @hook(priority=5)
    def pre_search(self, query, **kwargs):
        """Hook pre_search."""
        self.pre_search_called = True
        return {"query": f"another_{query}", "original_query": query}

    @hook(priority=30)
    def post_search(self, results, **kwargs):
        """Hook post_search."""
        self.post_search_called = True
        if isinstance(results, list):
            return {"results": [r + "_modified" for r in results], "modified": True}
        elif isinstance(results, dict) and "results" in results:
            return {"results": [r + "_modified" for r in results["results"]], "modified": True}
        else:
            return results


class TestPluginManager(unittest.TestCase):
    """Test case cho PluginManager."""

    def setUp(self):
        """Thiết lập trước mỗi test case."""
        self.plugin_manager = PluginManager()
        self.test_plugin = TestPlugin()
        self.another_test_plugin = AnotherTestPlugin()

    def test_register_plugin(self):
        """Test đăng ký plugin."""
        # Đăng ký plugin
        self.plugin_manager.register_plugin("test_plugin", self.test_plugin)

        # Kiểm tra plugin đã được đăng ký
        self.assertTrue(self.plugin_manager.has_plugin("test_plugin"))
        self.assertEqual(self.plugin_manager.get_plugin("test_plugin"), self.test_plugin)

    def test_unregister_plugin(self):
        """Test hủy đăng ký plugin."""
        # Đăng ký plugin
        self.plugin_manager.register_plugin("test_plugin", self.test_plugin)

        # Hủy đăng ký plugin
        self.plugin_manager.unregister_plugin("test_plugin")

        # Kiểm tra plugin đã bị hủy đăng ký
        self.assertFalse(self.plugin_manager.has_plugin("test_plugin"))
        self.assertIsNone(self.plugin_manager.get_plugin("test_plugin"))

    def test_get_all_plugins(self):
        """Test lấy tất cả các plugin."""
        # Đăng ký plugin
        self.plugin_manager.register_plugin("test_plugin", self.test_plugin)
        self.plugin_manager.register_plugin("another_test_plugin", self.another_test_plugin)

        # Lấy tất cả các plugin
        plugins = self.plugin_manager.get_all_plugins()

        # Kiểm tra kết quả
        self.assertEqual(len(plugins), 2)
        self.assertEqual(plugins["test_plugin"], self.test_plugin)
        self.assertEqual(plugins["another_test_plugin"], self.another_test_plugin)

    def test_execute_hook(self):
        """Test thực thi hook."""
        # Đăng ký plugin
        self.plugin_manager.register_plugin("test_plugin", self.test_plugin)
        self.plugin_manager.register_plugin("another_test_plugin", self.another_test_plugin)

        # Thực thi hook pre_search
        results = self.plugin_manager.execute_hook("pre_search", query="test")

        # Kiểm tra kết quả
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0]["plugin"], "another_test_plugin")
        self.assertEqual(results[0]["result"]["query"], "another_test")
        self.assertEqual(results[1]["plugin"], "test_plugin")
        self.assertEqual(results[1]["result"]["query"], "modified_test")

        # Kiểm tra plugin đã được gọi
        self.assertTrue(self.test_plugin.pre_search_called)
        self.assertTrue(self.another_test_plugin.pre_search_called)

    def test_execute_hook_until_success(self):
        """Test thực thi hook cho đến khi thành công."""
        # Đăng ký plugin
        self.plugin_manager.register_plugin("test_plugin", self.test_plugin)
        self.plugin_manager.register_plugin("another_test_plugin", self.another_test_plugin)

        # Thực thi hook pre_search
        result = self.plugin_manager.execute_hook_until_success("pre_search", query="test")

        # Kiểm tra kết quả
        self.assertIsNotNone(result)
        self.assertEqual(result["plugin"], "another_test_plugin")
        self.assertEqual(result["result"]["query"], "another_test")

        # Kiểm tra plugin đã được gọi
        self.assertFalse(self.test_plugin.pre_search_called)
        self.assertTrue(self.another_test_plugin.pre_search_called)

    def test_execute_hook_pipeline(self):
        """Test thực thi hook dưới dạng pipeline."""
        # Đăng ký plugin
        self.plugin_manager.register_plugin("test_plugin", self.test_plugin)
        self.plugin_manager.register_plugin("another_test_plugin", self.another_test_plugin)

        # Thực thi hook post_search
        result = self.plugin_manager.execute_hook_pipeline("post_search", ["result1", "result2"])

        # Kiểm tra kết quả
        self.assertEqual(result["results"], ["result1_modified", "result2_modified"])
        self.assertTrue(result["modified"])

        # Kiểm tra plugin đã được gọi
        self.assertTrue(self.test_plugin.post_search_called)
        self.assertTrue(self.another_test_plugin.post_search_called)


if __name__ == "__main__":
    unittest.main()
