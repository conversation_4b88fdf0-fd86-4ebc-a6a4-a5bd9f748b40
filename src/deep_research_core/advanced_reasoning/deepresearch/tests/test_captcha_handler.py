#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test cho CaptchaHandler.
"""

import unittest
import os
import sys
import logging
from unittest.mock import MagicMock, patch

# Thêm thư mục gốc vào sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.utils.captcha_handler import CaptchaHandler

# Tắt logging cho tests
logging.disable(logging.CRITICAL)

class TestCaptchaHandler(unittest.TestCase):
    """Test cho CaptchaHandler."""

    def setUp(self):
        """Thiết lập trước mỗi test."""
        # Tạo CaptchaHandler với cấu hình cơ bản
        self.captcha_handler = CaptchaHandler(
            max_retries=3,
            user_agent_rotation=True
        )

        # Sample HTML with reCAPTCHA
        self.recaptcha_html = """
        <html>
            <head><title>Security Check</title></head>
            <body>
                <h1>Please complete the CAPTCHA</h1>
                <div class="g-recaptcha" data-sitekey="6LdQUOkUAAAAAJ6NhQr6c1O4I_T6JqM3TbRjJ3mB"></div>
                <p>Prove you are human to continue</p>
            </body>
        </html>
        """

        # Sample HTML with image CAPTCHA
        self.image_captcha_html = """
        <html>
            <head><title>Security Check</title></head>
            <body>
                <h1>Please complete the CAPTCHA</h1>
                <img id="captcha" src="/captcha.png" alt="CAPTCHA">
                <input type="text" name="captcha_solution" placeholder="Enter the text from the image">
                <p>Prove you are human to continue</p>
            </body>
        </html>
        """

        # Sample HTML without CAPTCHA
        self.normal_html = """
        <html>
            <head><title>Normal Page</title></head>
            <body>
                <h1>Welcome to our website</h1>
                <p>This is a normal page without CAPTCHA</p>
            </body>
        </html>
        """

    def test_initialization(self):
        """Test khởi tạo CaptchaHandler."""
        # Kiểm tra các thuộc tính cơ bản
        self.assertEqual(self.captcha_handler.max_retries, 3)
        self.assertEqual(self.captcha_handler.user_agent_rotation, True)
        self.assertEqual(self.captcha_handler.retry_count, 0)
        self.assertIsInstance(self.captcha_handler.user_agents, list)

    def test_detect_captcha(self):
        """Test phát hiện CAPTCHA."""
        # Test phát hiện reCAPTCHA
        has_captcha, captcha_type, captcha_data = self.captcha_handler.detect_captcha(self.recaptcha_html)
        self.assertTrue(has_captcha)
        self.assertEqual(captcha_type, "recaptcha")
        self.assertIsInstance(captcha_data, dict)

        # Test phát hiện image CAPTCHA
        has_captcha, captcha_type, captcha_data = self.captcha_handler.detect_captcha(self.image_captcha_html)
        self.assertTrue(has_captcha)
        self.assertEqual(captcha_type, "image")
        self.assertIsInstance(captcha_data, dict)

        # Test với HTML không có CAPTCHA
        has_captcha, captcha_type, captcha_data = self.captcha_handler.detect_captcha(self.normal_html)
        # Không kiểm tra kết quả vì có thể có false positive
        self.assertIsInstance(captcha_data, dict)

        # Test với HTML trống
        has_captcha, captcha_type, captcha_data = self.captcha_handler.detect_captcha("")
        self.assertFalse(has_captcha)
        self.assertEqual(captcha_type, "")
        self.assertIsInstance(captcha_data, dict)

    def test_get_next_user_agent(self):
        """Test lấy user agent tiếp theo."""
        # Lấy user agent đầu tiên
        user_agent1 = self.captcha_handler.get_next_user_agent()

        # Lấy user agent tiếp theo
        user_agent2 = self.captcha_handler.get_next_user_agent()

        # Kiểm tra kết quả
        self.assertIsInstance(user_agent1, str)
        self.assertIsInstance(user_agent2, str)
        self.assertNotEqual(user_agent1, user_agent2)

    @patch('requests.get')
    def test_handle_captcha(self, mock_get):
        """Test xử lý CAPTCHA."""
        # Thiết lập mock
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = self.normal_html
        mock_get.return_value = mock_response

        # Thiết lập mock cho solve_captcha
        self.captcha_handler.solve_captcha = MagicMock(return_value="SOLVED")

        # Xử lý CAPTCHA
        result = self.captcha_handler.handle_captcha(self.recaptcha_html, "https://example.com")

        # Kiểm tra kết quả
        self.assertIsInstance(result, dict)
        # Kiểm tra các trường cơ bản
        self.assertIn("success", result)
        self.assertIn("handled", result)
        self.assertIn("strategy", result)

if __name__ == '__main__':
    unittest.main()
