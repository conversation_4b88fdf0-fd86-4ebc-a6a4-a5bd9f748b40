"""
Unit tests for the improved ReActFeedbackProcessor.
"""

import unittest
import tempfile
import os
from unittest.mock import patch, MagicMock

from src.deep_research_core.utils.react_feedback import ReActFeedbackProcessor


class TestReActFeedbackProcessorImprovements(unittest.TestCase):
    """Test the improved features of ReActFeedbackProcessor."""

    def setUp(self):
        """Set up test environment."""
        # Create a temporary database file
        self.temp_db = tempfile.NamedTemporaryFile(delete=False)
        self.temp_db.close()
        
        # Initialize processor with the temporary database
        self.processor = ReActFeedbackProcessor(
            feedback_db_path=self.temp_db.name,
            enable_reinforcement_learning=True,
            learning_rate=0.1
        )
    
    def tearDown(self):
        """Clean up after tests."""
        self.processor.close()
        os.unlink(self.temp_db.name)
    
    def test_query_complexity_estimation(self):
        """Test the automatic query complexity estimation."""
        # Test simple query
        simple_query = "What is 2+2?"
        simple_complexity = self.processor._estimate_query_complexity(simple_query)
        self.assertLess(simple_complexity, 0.5, "Simple query should have low complexity")
        
        # Test complex query
        complex_query = "Explain the relationship between quantum mechanics and general relativity, focusing on the challenges of reconciling these theories in extreme gravitational fields."
        complex_complexity = self.processor._estimate_query_complexity(complex_query)
        self.assertGreater(complex_complexity, 0.7, "Complex query should have high complexity")
        
        # Test Vietnamese query
        vietnamese_query = "Giải thích lý thuyết lượng tử và mối quan hệ với thuyết tương đối rộng."
        vietnamese_complexity = self.processor._estimate_query_complexity(vietnamese_query)
        self.assertGreater(vietnamese_complexity, 0.5, "Vietnamese query should have appropriate complexity")
        
        # Test multi-part query
        multipart_query = "What is quantum mechanics? How does it relate to classical physics? What are its practical applications?"
        multipart_complexity = self.processor._estimate_query_complexity(multipart_query)
        self.assertGreater(multipart_complexity, 0.6, "Multi-part query should have higher complexity")
    
    def test_vietnamese_query_detection(self):
        """Test detection of Vietnamese queries."""
        # Test with Vietnamese characters
        self.assertTrue(self.processor._is_vietnamese_query("Xin chào thế giới"), "Should detect Vietnamese with diacritics")
        
        # Test with Vietnamese common words
        self.assertTrue(self.processor._is_vietnamese_query("Tôi là một người Việt Nam"), "Should detect Vietnamese with common words")
        
        # Test with English
        self.assertFalse(self.processor._is_vietnamese_query("Hello world"), "Should not detect English as Vietnamese")
        
        # Test with mixed content
        self.assertTrue(self.processor._is_vietnamese_query("Hello và xin chào"), "Should detect mixed content with Vietnamese")
    
    def test_query_type_detection(self):
        """Test detection of query types."""
        # Test calculation query
        self.assertEqual(self.processor._detect_query_type("Calculate the area of a circle with radius 5"), "calculation")
        self.assertEqual(self.processor._detect_query_type("Tính diện tích hình tròn bán kính 5"), "calculation")
        
        # Test explanation query
        self.assertEqual(self.processor._detect_query_type("Explain how photosynthesis works"), "explanation")
        self.assertEqual(self.processor._detect_query_type("Giải thích quá trình quang hợp"), "explanation")
        
        # Test comparison query
        self.assertEqual(self.processor._detect_query_type("Compare Python and JavaScript"), "comparison")
        self.assertEqual(self.processor._detect_query_type("So sánh giữa Python và JavaScript"), "comparison")
        
        # Test procedure query
        self.assertEqual(self.processor._detect_query_type("How to make chocolate cake"), "procedure")
        self.assertEqual(self.processor._detect_query_type("Làm thế nào để làm bánh sô-cô-la"), "procedure")
        
        # Test analysis query
        self.assertEqual(self.processor._detect_query_type("Analyze the impact of climate change"), "analysis")
        self.assertEqual(self.processor._detect_query_type("Phân tích tác động của biến đổi khí hậu"), "analysis")
        
        # Test creative query
        self.assertEqual(self.processor._detect_query_type("Create a story about a dragon"), "creative")
        self.assertEqual(self.processor._detect_query_type("Tạo một câu chuyện về rồng"), "creative")
        
        # Test decision query
        self.assertEqual(self.processor._detect_query_type("Should I learn Python or JavaScript?"), "decision")
        self.assertEqual(self.processor._detect_query_type("Nên học Python hay JavaScript?"), "decision")
        
        # Test general query
        self.assertEqual(self.processor._detect_query_type("The sky is blue"), "general")
    
    def test_domain_detection(self):
        """Test detection of query domains."""
        # Test technical domain
        self.assertEqual(self.processor._detect_query_domain("How to implement a binary search algorithm"), "technical")
        self.assertEqual(self.processor._detect_query_domain("Cách triển khai thuật toán tìm kiếm nhị phân"), "technical")
        
        # Test scientific domain
        self.assertEqual(self.processor._detect_query_domain("Explain the theory of relativity"), "scientific")
        self.assertEqual(self.processor._detect_query_domain("Giải thích lý thuyết tương đối"), "scientific")
        
        # Test medical domain
        self.assertEqual(self.processor._detect_query_domain("What are the symptoms of diabetes"), "medical")
        self.assertEqual(self.processor._detect_query_domain("Các triệu chứng của bệnh tiểu đường"), "medical")
        
        # Test educational domain
        self.assertEqual(self.processor._detect_query_domain("Best ways to study for exams"), "educational")
        self.assertEqual(self.processor._detect_query_domain("Cách học tốt nhất cho kỳ thi"), "educational")
        
        # Test business domain
        self.assertEqual(self.processor._detect_query_domain("How to start a small business"), "business")
        self.assertEqual(self.processor._detect_query_domain("Cách bắt đầu kinh doanh nhỏ"), "business")
        
        # Test legal domain
        self.assertEqual(self.processor._detect_query_domain("Explain copyright law"), "legal")
        self.assertEqual(self.processor._detect_query_domain("Giải thích luật bản quyền"), "legal")
        
        # Test financial domain
        self.assertEqual(self.processor._detect_query_domain("How to invest in stocks"), "financial")
        self.assertEqual(self.processor._detect_query_domain("Cách đầu tư vào chứng khoán"), "financial")
        
        # Test no specific domain
        self.assertIsNone(self.processor._detect_query_domain("What is the weather today"), "Should not detect domain for general query")
    
    def test_prompt_adjustments_with_complexity_estimation(self):
        """Test that prompt adjustments are made based on estimated complexity."""
        # Test with a complex query
        complex_query = "Explain the relationship between quantum mechanics and general relativity, focusing on the challenges of reconciling these theories in extreme gravitational fields."
        adjustments = self.processor.get_prompt_adjustments(complex_query)
        
        # Should add step-by-step and detailed instructions for complex queries
        self.assertTrue(adjustments.get("add_step_by_step", False), "Should add step-by-step for complex queries")
        self.assertTrue(adjustments.get("add_detailed_instructions", False), "Should add detailed instructions for complex queries")
        
        # Test with a simple query
        simple_query = "What is 2+2?"
        adjustments = self.processor.get_prompt_adjustments(simple_query)
        
        # Should not add step-by-step and detailed instructions for simple queries
        self.assertFalse(adjustments.get("add_step_by_step", False), "Should not add step-by-step for simple queries")
        self.assertFalse(adjustments.get("add_detailed_instructions", False), "Should not add detailed instructions for simple queries")
    
    def test_domain_specific_adjustments(self):
        """Test that domain-specific adjustments are made."""
        # Test technical domain
        technical_query = "How to implement a binary search algorithm in Python"
        adjustments = self.processor.get_prompt_adjustments(technical_query)
        self.assertTrue(adjustments.get("add_code_examples", False), "Should add code examples for technical queries")
        
        # Test scientific domain with high complexity
        scientific_query = "Explain the implications of quantum entanglement for our understanding of locality and causality in physics"
        # Manually set complexity to ensure domain-specific adjustments
        self.processor.query_complexity_estimates[scientific_query] = 0.8
        adjustments = self.processor.get_prompt_adjustments(scientific_query)
        self.assertTrue(adjustments.get("add_scientific_context", False), "Should add scientific context")
        self.assertTrue(adjustments.get("add_references", False), "Should add references for complex scientific queries")
        
        # Test medical domain
        medical_query = "What are the treatment options for type 2 diabetes?"
        adjustments = self.processor.get_prompt_adjustments(medical_query)
        self.assertTrue(adjustments.get("add_medical_disclaimer", False), "Should add medical disclaimer")
        self.assertTrue(adjustments.get("use_medical_format", False), "Should use medical format")
        
        # Test educational domain
        educational_query = "How do students learn mathematics effectively?"
        adjustments = self.processor.get_prompt_adjustments(educational_query)
        self.assertTrue(adjustments.get("use_educational_format", False), "Should use educational format")
        self.assertTrue(adjustments.get("add_examples", False), "Should add examples for educational queries")
        
        # Test legal domain
        legal_query = "Explain the basics of copyright law"
        adjustments = self.processor.get_prompt_adjustments(legal_query)
        self.assertTrue(adjustments.get("add_legal_disclaimer", False), "Should add legal disclaimer")
        self.assertTrue(adjustments.get("use_formal_language", False), "Should use formal language for legal queries")
        
        # Test financial domain with high complexity
        financial_query = "What are the best strategies for long-term investment in a volatile market?"
        # Manually set complexity to ensure domain-specific adjustments
        self.processor.query_complexity_estimates[financial_query] = 0.7
        adjustments = self.processor.get_prompt_adjustments(financial_query)
        self.assertTrue(adjustments.get("add_financial_disclaimer", False), "Should add financial disclaimer")
        self.assertTrue(adjustments.get("add_risk_assessment", False), "Should add risk assessment for complex financial queries")
    
    def test_vietnamese_specific_adjustments(self):
        """Test that Vietnamese-specific adjustments are made."""
        # Test Vietnamese query
        vietnamese_query = "Giải thích lý thuyết lượng tử và mối quan hệ với thuyết tương đối rộng."
        adjustments = self.processor.get_prompt_adjustments(vietnamese_query)
        self.assertTrue(adjustments.get("use_vietnamese_instructions", False), "Should use Vietnamese instructions")
        
        # Test Vietnamese translation query
        vietnamese_translation_query = "Dịch đoạn văn sau sang tiếng Anh"
        adjustments = self.processor.get_prompt_adjustments(vietnamese_translation_query)
        self.assertTrue(adjustments.get("use_vietnamese_instructions", False), "Should use Vietnamese instructions")
        self.assertTrue(adjustments.get("use_translation_format", False), "Should use translation format")


if __name__ == "__main__":
    unittest.main()
