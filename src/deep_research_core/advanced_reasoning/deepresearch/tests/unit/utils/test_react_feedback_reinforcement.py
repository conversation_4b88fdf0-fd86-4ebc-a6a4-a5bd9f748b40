"""
Unit tests for reinforcement learning capabilities of ReActFeedbackProcessor.

This module contains tests for the reinforcement learning algorithms used
by the ReActFeedbackProcessor class to optimize feedback-based adaptations.
"""

import unittest
import os
import tempfile
import json
import time
import numpy as np
from unittest.mock import patch, MagicMock

# Fix import path
from src.deep_research_core.utils.react_feedback import ReActFeedbackProcessor


class TestReActFeedbackReinforcementLearning(unittest.TestCase):
    """Tests for the reinforcement learning capabilities of ReActFeedbackProcessor."""

    def setUp(self):
        """Set up test environment before each test."""
        # Create a temporary database file
        self.temp_db_fd, self.temp_db_path = tempfile.mkstemp()

        # Initialize the processor with the temporary database and RL enabled
        self.processor = ReActFeedbackProcessor(
            feedback_db_path=self.temp_db_path,
            learning_rate=0.2,  # Higher learning rate for faster adaptation in tests
            min_feedback_samples=2,  # Small value for testing
            max_feedback_history=100,
            enable_reinforcement_learning=True,  # Enable RL
            verbose=False
        )

    def tearDown(self):
        """Clean up after each test."""
        # Close the processor (which closes the database connection)
        self.processor.close()

        # Close and remove the temporary database file
        os.close(self.temp_db_fd)
        os.unlink(self.temp_db_path)

    def test_exponential_moving_average_adaptation(self):
        """Test that the tool weights adapt using exponential moving average."""
        # Process first feedback
        self.processor.process_feedback(
            query="Simple math question",
            feedback={
                "complexity": 0.2,
                "tool_feedback": {
                    "calculator": True
                }
            }
        )

        # Process second feedback to meet min_feedback_samples
        self.processor.process_feedback(
            query="Another math question",
            feedback={
                "complexity": 0.2,
                "tool_feedback": {
                    "calculator": True
                }
            }
        )

        # Get initial weight
        initial_weight = self.processor.tool_weights.get("calculator", 0)

        # Process another positive feedback
        self.processor.process_feedback(
            query="What is 2+2?",
            feedback={
                "complexity": 0.1,
                "tool_feedback": {
                    "calculator": True
                }
            }
        )

        # Weight should increase towards 1.0
        current_weight = self.processor.tool_weights.get("calculator", 0)
        self.assertGreater(current_weight, initial_weight)

        # Process negative feedback
        self.processor.process_feedback(
            query="What is 3+3?",
            feedback={
                "complexity": 0.1,
                "tool_feedback": {
                    "calculator": False
                }
            }
        )

        # Weight should decrease
        new_weight = self.processor.tool_weights.get("calculator", 0)
        self.assertLess(new_weight, current_weight)

    def test_strategy_adaptation_over_time(self):
        """Test that strategy weights adapt over time based on effectiveness."""
        # Add several feedback entries with varying effectiveness
        effectiveness_values = [0.5, 0.6, 0.7, 0.8, 0.9]

        # Process initial feedbacks to meet min_feedback_samples
        for i in range(2):
            self.processor.process_feedback(
                query=f"Test query {i}",
                feedback={
                    "strategy_feedback": {
                        "direct_answer": effectiveness_values[i]
                    }
                }
            )

        # Track weight changes
        weights = []
        weights.append(self.processor.strategy_weights.get("direct_answer", 0))

        # Process more feedbacks and track weight changes
        for i in range(2, len(effectiveness_values)):
            self.processor.process_feedback(
                query=f"Test query {i}",
                feedback={
                    "strategy_feedback": {
                        "direct_answer": effectiveness_values[i]
                    }
                }
            )
            weights.append(self.processor.strategy_weights.get("direct_answer", 0))

        # Weights should be increasing as effectiveness increases
        for i in range(1, len(weights)):
            self.assertGreaterEqual(weights[i], weights[i-1])

        # Now decrease effectiveness
        for i in range(3):
            self.processor.process_feedback(
                query=f"Test query decline {i}",
                feedback={
                    "strategy_feedback": {
                        "direct_answer": 0.3
                    }
                }
            )

        # Weight should decrease
        final_weight = self.processor.strategy_weights.get("direct_answer", 0)
        self.assertLess(final_weight, weights[-1])

    def test_convergence_of_weights(self):
        """Test that weights converge to expected values after many samples."""
        # Simulate many feedback samples for a tool
        tool_name = "web_search"
        success_rate = 0.8  # 80% success rate

        # Process enough feedback to exceed min_feedback_samples
        for i in range(50):  # Large number of samples
            success = np.random.random() < success_rate  # Randomly succeed based on probability
            self.processor.process_feedback(
                query=f"Query {i}",
                feedback={
                    "tool_feedback": {
                        tool_name: success
                    }
                }
            )

        # Check that the weight has converged close to the success rate
        final_weight = self.processor.tool_weights.get(tool_name, 0)
        self.assertAlmostEqual(final_weight, success_rate, delta=0.15)  # Allow some deviation

    def test_adaptive_learning_rate(self):
        """Test adaptation with different learning rates."""
        # Create temporary database files
        fast_db_fd, fast_db_path = tempfile.mkstemp()

        # Create processors with different learning rates
        fast_processor = ReActFeedbackProcessor(
            feedback_db_path=fast_db_path,
            learning_rate=0.5,  # High learning rate
            min_feedback_samples=2,
            enable_reinforcement_learning=True
        )

        # Create temporary database files
        slow_db_fd, slow_db_path = tempfile.mkstemp()

        slow_processor = ReActFeedbackProcessor(
            feedback_db_path=slow_db_path,
            learning_rate=0.1,  # Low learning rate
            min_feedback_samples=2,
            enable_reinforcement_learning=True
        )

        # Process identical feedback for both processors
        for i in range(5):
            # First two feedbacks are positive, then negative
            success = i < 2

            fast_processor.process_feedback(
                query=f"Query {i}",
                feedback={
                    "tool_feedback": {
                        "test_tool": success
                    }
                }
            )

            slow_processor.process_feedback(
                query=f"Query {i}",
                feedback={
                    "tool_feedback": {
                        "test_tool": success
                    }
                }
            )

        # Fast processor should have adapted more dramatically
        fast_weight = fast_processor.tool_weights.get("test_tool", 0)
        slow_weight = slow_processor.tool_weights.get("test_tool", 0)

        # Since we started positive then went negative, the fast processor
        # should have a lower final weight
        self.assertLess(fast_weight, slow_weight)

        # Clean up
        fast_processor.close()
        slow_processor.close()

        # Close and remove temporary database files
        os.close(fast_db_fd)
        os.unlink(fast_db_path)
        os.close(slow_db_fd)
        os.unlink(slow_db_path)

    def test_multi_tool_adaptation(self):
        """Test adaptation with multiple competing tools."""
        tools = ["web_search", "calculator", "code_interpreter"]

        # Process feedback showing preference for different tools
        # web_search: good for information queries
        # calculator: good for math
        # code_interpreter: good for coding

        # Process information queries
        for i in range(5):
            self.processor.process_feedback(
                query=f"What is the capital of country {i}?",
                feedback={
                    "tool_feedback": {
                        "web_search": True,
                        "calculator": False,
                        "code_interpreter": False
                    }
                }
            )

        # Process math queries
        for i in range(5):
            self.processor.process_feedback(
                query=f"Calculate {i} + {i*2}",
                feedback={
                    "tool_feedback": {
                        "web_search": False,
                        "calculator": True,
                        "code_interpreter": False
                    }
                }
            )

        # Process coding queries
        for i in range(5):
            self.processor.process_feedback(
                query=f"Write a function to compute factorial {i}",
                feedback={
                    "tool_feedback": {
                        "web_search": False,
                        "calculator": False,
                        "code_interpreter": True
                    }
                }
            )

        # Get recommendations for each query type
        info_recommendations = self.processor.get_tool_recommendations(
            query="What is the capital of France?",
            available_tools=tools
        )

        math_recommendations = self.processor.get_tool_recommendations(
            query="Calculate 5 + 10",
            available_tools=tools
        )

        code_recommendations = self.processor.get_tool_recommendations(
            query="Write a function to sort an array",
            available_tools=tools
        )

        # Check that the right tool is recommended for each query type
        self.assertEqual(info_recommendations[0][0], "web_search")
        self.assertEqual(math_recommendations[0][0], "calculator")
        self.assertEqual(code_recommendations[0][0], "code_interpreter")


if __name__ == "__main__":
    unittest.main()