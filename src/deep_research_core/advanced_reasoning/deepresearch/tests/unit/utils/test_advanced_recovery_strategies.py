"""
Unit tests for advanced recovery strategies.

This module contains tests for the advanced error recovery strategies.
"""

import unittest
from unittest.mock import patch, MagicMock

from src.deep_research_core.utils.advanced_recovery_strategies import (
    ContextAwareReformulationStrategy,
    PromptEngineeringStrategy,
    ModelFallbackStrategy,
    ToolParameterAdjustmentStrategy,
    AdaptiveRecoveryStrategy
)


class TestContextAwareReformulationStrategy(unittest.TestCase):
    """Test the ContextAwareReformulationStrategy class."""

    def setUp(self):
        """Set up test fixtures."""
        self.strategy = ContextAwareReformulationStrategy()

    def test_can_handle(self):
        """Test checking if the strategy can handle an error."""
        # Error that can be handled
        error = ValueError("Invalid query format")
        context = {
            "tool_name": "web_search",
            "tool_args": {"query": "test query"}
        }
        self.assertTrue(self.strategy.can_handle(error, context))
        
        # Error that cannot be handled (missing tool_name)
        error = ValueError("Invalid query format")
        context = {
            "tool_args": {"query": "test query"}
        }
        self.assertFalse(self.strategy.can_handle(error, context))
        
        # Error that cannot be handled (missing tool_args)
        error = ValueError("Invalid query format")
        context = {
            "tool_name": "web_search"
        }
        self.assertFalse(self.strategy.can_handle(error, context))
        
        # Error that cannot be handled (unknown tool)
        error = ValueError("Invalid query format")
        context = {
            "tool_name": "unknown_tool",
            "tool_args": {"query": "test query"}
        }
        self.assertFalse(self.strategy.can_handle(error, context))
        
        # Error that cannot be handled (already attempted)
        error = ValueError("Invalid query format")
        context = {
            "tool_name": "web_search",
            "tool_args": {"query": "test query"},
            "context_reformulation_attempted": True
        }
        self.assertFalse(self.strategy.can_handle(error, context))

    def test_recover(self):
        """Test recovering from an error."""
        # Test with web_search tool and invalid query error
        error = ValueError("Invalid query format")
        context = {
            "tool_name": "web_search",
            "tool_args": {"query": "test query"}
        }
        
        result = self.strategy.recover(error, context)
        
        self.assertTrue(result["success"])
        self.assertEqual(result["strategy"], "context_aware_reformulation")
        self.assertIn("original_args", result)
        self.assertIn("reformulated_args", result)
        self.assertEqual(result["original_args"]["query"], "test query")
        self.assertEqual(result["reformulated_args"]["query"], "test query")
        
        # Test with web_search tool and too many results error
        error = ValueError("Too many results returned")
        context = {
            "tool_name": "web_search",
            "tool_args": {"query": "test query"}
        }
        
        result = self.strategy.recover(error, context)
        
        self.assertTrue(result["success"])
        self.assertEqual(result["strategy"], "context_aware_reformulation")
        self.assertIn("original_args", result)
        self.assertIn("reformulated_args", result)
        self.assertEqual(result["original_args"]["query"], "test query")
        self.assertEqual(result["reformulated_args"]["query"], "\"test query\"")
        
        # Test with file_read tool and not found error
        error = FileNotFoundError("File 'test.txt' not found")
        context = {
            "tool_name": "file_read",
            "tool_args": {"path": "test.txt"}
        }
        
        result = self.strategy.recover(error, context)
        
        self.assertTrue(result["success"])
        self.assertEqual(result["strategy"], "context_aware_reformulation")
        self.assertIn("original_args", result)
        self.assertIn("reformulated_args", result)
        self.assertEqual(result["original_args"]["path"], "test.txt")
        self.assertEqual(result["reformulated_args"]["path"], "./test.txt")


class TestPromptEngineeringStrategy(unittest.TestCase):
    """Test the PromptEngineeringStrategy class."""

    def setUp(self):
        """Set up test fixtures."""
        self.strategy = PromptEngineeringStrategy()

    def test_can_handle(self):
        """Test checking if the strategy can handle an error."""
        # Error that can be handled
        error = ValueError("Invalid arguments")
        context = {
            "tool_name": "test_tool",
            "api_provider": MagicMock()
        }
        self.assertTrue(self.strategy.can_handle(error, context))
        
        # Error that cannot be handled (missing tool_name)
        error = ValueError("Invalid arguments")
        context = {
            "api_provider": MagicMock()
        }
        self.assertFalse(self.strategy.can_handle(error, context))
        
        # Error that cannot be handled (missing api_provider)
        error = ValueError("Invalid arguments")
        context = {
            "tool_name": "test_tool"
        }
        self.assertFalse(self.strategy.can_handle(error, context))
        
        # Error that cannot be handled (already attempted)
        error = ValueError("Invalid arguments")
        context = {
            "tool_name": "test_tool",
            "api_provider": MagicMock(),
            "prompt_engineering_attempted": True
        }
        self.assertFalse(self.strategy.can_handle(error, context))

    @patch("src.deep_research_core.utils.advanced_recovery_strategies.PromptEngineeringStrategy.recover")
    def test_recover(self, mock_recover):
        """Test recovering from an error."""
        # Mock the api_provider
        api_provider = MagicMock()
        api_provider.generate.return_value = "Use the test_tool with argument 'value'"
        
        # Set up the mock recover method
        mock_recover.return_value = {
            "success": True,
            "strategy": "prompt_engineering",
            "message": "Generated alternative approach using prompt engineering",
            "prompt": "I need to use the test_tool tool but encountered an error: Invalid arguments. Please provide a correct example of how to use this tool with valid arguments.",
            "response": "Use the test_tool with argument 'value'",
            "suggested_approach": "Use the test_tool with argument 'value'"
        }
        
        # Test with invalid arguments error
        error = ValueError("Invalid arguments")
        context = {
            "tool_name": "test_tool",
            "api_provider": api_provider,
            "model": "gpt-3.5-turbo"
        }
        
        result = mock_recover(error, context)
        
        self.assertTrue(result["success"])
        self.assertEqual(result["strategy"], "prompt_engineering")
        self.assertIn("prompt", result)
        self.assertIn("response", result)
        self.assertIn("suggested_approach", result)


class TestModelFallbackStrategy(unittest.TestCase):
    """Test the ModelFallbackStrategy class."""

    def setUp(self):
        """Set up test fixtures."""
        self.strategy = ModelFallbackStrategy()

    def test_can_handle(self):
        """Test checking if the strategy can handle an error."""
        # Error that can be handled
        error = ValueError("Model error")
        context = {
            "api_provider": MagicMock(),
            "model": "gpt-3.5-turbo"
        }
        self.assertTrue(self.strategy.can_handle(error, context))
        
        # Error that cannot be handled (missing api_provider)
        error = ValueError("Model error")
        context = {
            "model": "gpt-3.5-turbo"
        }
        self.assertFalse(self.strategy.can_handle(error, context))
        
        # Error that cannot be handled (missing model)
        error = ValueError("Model error")
        context = {
            "api_provider": MagicMock()
        }
        self.assertFalse(self.strategy.can_handle(error, context))
        
        # Error that cannot be handled (all models tried)
        error = ValueError("Model error")
        context = {
            "api_provider": MagicMock(),
            "model": "gpt-3.5-turbo",
            "tried_models": [
                "gpt-4", "gpt-3.5-turbo", "claude-3-opus-20240229",
                "claude-3-sonnet-20240229", "claude-3-haiku-20240307",
                "gemini-1.0-pro", "mistral-large-latest", "mistral-medium-latest",
                "llama-3-70b-instruct", "llama-3-8b-instruct"
            ]
        }
        self.assertFalse(self.strategy.can_handle(error, context))

    def test_recover(self):
        """Test recovering from an error."""
        # Test with model error
        error = ValueError("Model error")
        context = {
            "api_provider": MagicMock(),
            "model": "gpt-3.5-turbo"
        }
        
        result = self.strategy.recover(error, context)
        
        self.assertTrue(result["success"])
        self.assertEqual(result["strategy"], "model_fallback")
        self.assertEqual(result["original_model"], "gpt-3.5-turbo")
        self.assertEqual(result["fallback_model"], "gpt-4")
        self.assertIn("tried_models", result)
        self.assertIn("gpt-3.5-turbo", result["tried_models"])
        self.assertIn("gpt-4", result["tried_models"])
        
        # Test with model error and some models already tried
        error = ValueError("Model error")
        context = {
            "api_provider": MagicMock(),
            "model": "gpt-3.5-turbo",
            "tried_models": ["gpt-4"]
        }
        
        result = self.strategy.recover(error, context)
        
        self.assertTrue(result["success"])
        self.assertEqual(result["strategy"], "model_fallback")
        self.assertEqual(result["original_model"], "gpt-3.5-turbo")
        self.assertEqual(result["fallback_model"], "claude-3-opus-20240229")
        self.assertIn("tried_models", result)
        self.assertIn("gpt-3.5-turbo", result["tried_models"])
        self.assertIn("gpt-4", result["tried_models"])
        self.assertIn("claude-3-opus-20240229", result["tried_models"])


class TestToolParameterAdjustmentStrategy(unittest.TestCase):
    """Test the ToolParameterAdjustmentStrategy class."""

    def setUp(self):
        """Set up test fixtures."""
        self.strategy = ToolParameterAdjustmentStrategy()

    def test_can_handle(self):
        """Test checking if the strategy can handle an error."""
        # Error that can be handled
        error = TimeoutError("Request timed out")
        context = {
            "tool_name": "api_request",
            "tool_args": {"timeout": 10}
        }
        self.assertTrue(self.strategy.can_handle(error, context))
        
        # Error that cannot be handled (missing tool_name)
        error = TimeoutError("Request timed out")
        context = {
            "tool_args": {"timeout": 10}
        }
        self.assertFalse(self.strategy.can_handle(error, context))
        
        # Error that cannot be handled (missing tool_args)
        error = TimeoutError("Request timed out")
        context = {
            "tool_name": "api_request"
        }
        self.assertFalse(self.strategy.can_handle(error, context))
        
        # Error that cannot be handled (unknown tool)
        error = TimeoutError("Request timed out")
        context = {
            "tool_name": "unknown_tool",
            "tool_args": {"timeout": 10}
        }
        self.assertFalse(self.strategy.can_handle(error, context))
        
        # Error that cannot be handled (already attempted)
        error = TimeoutError("Request timed out")
        context = {
            "tool_name": "api_request",
            "tool_args": {"timeout": 10},
            "parameter_adjustment_attempted": True
        }
        self.assertFalse(self.strategy.can_handle(error, context))

    def test_recover(self):
        """Test recovering from an error."""
        # Test with api_request tool and timeout error
        error = TimeoutError("Request timed out")
        context = {
            "tool_name": "api_request",
            "tool_args": {"timeout": 10}
        }
        
        result = self.strategy.recover(error, context)
        
        self.assertTrue(result["success"])
        self.assertEqual(result["strategy"], "tool_parameter_adjustment")
        self.assertIn("original_args", result)
        self.assertIn("adjusted_args", result)
        self.assertEqual(result["original_args"]["timeout"], 10)
        self.assertEqual(result["adjusted_args"]["timeout"], 20)
        self.assertIn("adjustments", result)
        self.assertIn("timeout", result["adjustments"])
        
        # Test with generate tool and token limit error
        error = ValueError("Token limit exceeded")
        context = {
            "tool_name": "generate",
            "tool_args": {"max_tokens": 1000}
        }
        
        result = self.strategy.recover(error, context)
        
        self.assertTrue(result["success"])
        self.assertEqual(result["strategy"], "tool_parameter_adjustment")
        self.assertIn("original_args", result)
        self.assertIn("adjusted_args", result)
        self.assertEqual(result["original_args"]["max_tokens"], 1000)
        self.assertEqual(result["adjusted_args"]["max_tokens"], 500)
        self.assertIn("adjustments", result)
        self.assertIn("max_tokens", result["adjustments"])


class TestAdaptiveRecoveryStrategy(unittest.TestCase):
    """Test the AdaptiveRecoveryStrategy class."""

    def setUp(self):
        """Set up test fixtures."""
        # Create mock strategies
        self.strategy1 = MagicMock()
        self.strategy1.name = "strategy1"
        self.strategy1.effectiveness = 0.8
        
        self.strategy2 = MagicMock()
        self.strategy2.name = "strategy2"
        self.strategy2.effectiveness = 0.5
        
        # Create error learning manager
        self.error_learning_manager = MagicMock()
        
        # Create adaptive strategy
        self.strategy = AdaptiveRecoveryStrategy(
            strategies=[self.strategy1, self.strategy2],
            error_learning_manager=self.error_learning_manager
        )

    def test_can_handle(self):
        """Test checking if the strategy can handle an error."""
        # Set up mock strategies
        self.strategy1.can_handle.return_value = True
        self.strategy2.can_handle.return_value = False
        
        # Error that can be handled
        error = ValueError("Test error")
        context = {"test": "context"}
        
        self.assertTrue(self.strategy.can_handle(error, context))
        self.strategy1.can_handle.assert_called_once_with(error, context)
        self.strategy2.can_handle.assert_called_once_with(error, context)
        
        # Reset mocks
        self.strategy1.can_handle.reset_mock()
        self.strategy2.can_handle.reset_mock()
        
        # Set up mock strategies
        self.strategy1.can_handle.return_value = False
        self.strategy2.can_handle.return_value = False
        
        # Error that cannot be handled
        error = ValueError("Test error")
        context = {"test": "context"}
        
        self.assertFalse(self.strategy.can_handle(error, context))
        self.strategy1.can_handle.assert_called_once_with(error, context)
        self.strategy2.can_handle.assert_called_once_with(error, context)

    def test_recover_with_error_learning(self):
        """Test recovering from an error with error learning."""
        # Set up mock strategies
        self.strategy1.can_handle.return_value = True
        self.strategy2.can_handle.return_value = True
        
        self.strategy1.recover.return_value = {
            "success": True,
            "strategy": "strategy1",
            "message": "Recovered with strategy1"
        }
        
        # Set up error learning manager
        self.error_learning_manager.get_best_strategy.return_value = "strategy1"
        
        # Error to recover from
        error = ValueError("Test error")
        context = {"test": "context"}
        
        # Recover from the error
        result = self.strategy.recover(error, context)
        
        # Check that the error learning manager was used
        self.error_learning_manager.get_best_strategy.assert_called_once_with(error, context)
        
        # Check that the best strategy was used
        self.strategy1.recover.assert_called_once_with(error, context)
        self.strategy2.recover.assert_not_called()
        
        # Check that the strategy effectiveness was updated
        self.strategy1.update_effectiveness.assert_called_once_with(True)
        
        # Check that the error was tracked
        self.error_learning_manager.track_error.assert_called_once()
        
        # Check the result
        self.assertTrue(result["success"])
        self.assertEqual(result["strategy"], "adaptive_recovery")
        self.assertEqual(result["adaptive_strategy"], "strategy1")

    def test_recover_without_error_learning(self):
        """Test recovering from an error without error learning."""
        # Create adaptive strategy without error learning
        strategy = AdaptiveRecoveryStrategy(
            strategies=[self.strategy1, self.strategy2]
        )
        
        # Set up mock strategies
        self.strategy1.can_handle.return_value = True
        self.strategy2.can_handle.return_value = True
        
        self.strategy1.recover.return_value = {
            "success": True,
            "strategy": "strategy1",
            "message": "Recovered with strategy1"
        }
        
        # Error to recover from
        error = ValueError("Test error")
        context = {"test": "context"}
        
        # Recover from the error
        result = strategy.recover(error, context)
        
        # Check that the most effective strategy was used
        self.strategy1.recover.assert_called_once_with(error, context)
        self.strategy2.recover.assert_not_called()
        
        # Check that the strategy effectiveness was updated
        self.strategy1.update_effectiveness.assert_called_once_with(True)
        
        # Check the result
        self.assertTrue(result["success"])
        self.assertEqual(result["strategy"], "adaptive_recovery")
        self.assertEqual(result["adaptive_strategy"], "strategy1")


if __name__ == '__main__':
    unittest.main()
