"""
Unit tests for error learning.

This module contains tests for the error learning system.
"""

import unittest
import os
import tempfile
import json
from unittest.mock import patch, MagicMock

from src.deep_research_core.utils.error_learning import (
    ErrorPattern, ErrorLearningManager, SQLiteErrorLearningManager
)


class TestErrorPattern(unittest.TestCase):
    """Test the ErrorPattern class."""

    def test_initialization(self):
        """Test initializing an error pattern."""
        pattern = ErrorPattern(
            category="invalid_arguments",
            tool_name="test_tool",
            error_message="Missing required argument 'query'"
        )
        
        self.assertEqual(pattern.category, "invalid_arguments")
        self.assertEqual(pattern.tool_name, "test_tool")
        self.assertEqual(pattern.error_message, "Missing required argument 'query'")
        self.assertEqual(pattern.occurrences, 0)
        self.assertEqual(pattern.strategy_effectiveness, {})

    def test_update(self):
        """Test updating an error pattern."""
        pattern = ErrorPattern(
            category="invalid_arguments",
            tool_name="test_tool",
            error_message="Missing required argument 'query'"
        )
        
        # Update with a successful recovery
        pattern.update("retry", True)
        
        self.assertEqual(pattern.occurrences, 1)
        self.assertIn("retry", pattern.strategy_effectiveness)
        self.assertEqual(pattern.strategy_effectiveness["retry"]["attempts"], 1)
        self.assertEqual(pattern.strategy_effectiveness["retry"]["successes"], 1)
        self.assertEqual(pattern.strategy_effectiveness["retry"]["success_rate"], 1.0)
        
        # Update with a failed recovery
        pattern.update("retry", False)
        
        self.assertEqual(pattern.occurrences, 2)
        self.assertEqual(pattern.strategy_effectiveness["retry"]["attempts"], 2)
        self.assertEqual(pattern.strategy_effectiveness["retry"]["successes"], 1)
        self.assertEqual(pattern.strategy_effectiveness["retry"]["success_rate"], 0.5)
        
        # Update with a different strategy
        pattern.update("alternative_tool", True)
        
        self.assertEqual(pattern.occurrences, 3)
        self.assertIn("alternative_tool", pattern.strategy_effectiveness)
        self.assertEqual(pattern.strategy_effectiveness["alternative_tool"]["attempts"], 1)
        self.assertEqual(pattern.strategy_effectiveness["alternative_tool"]["successes"], 1)
        self.assertEqual(pattern.strategy_effectiveness["alternative_tool"]["success_rate"], 1.0)

    def test_get_best_strategy(self):
        """Test getting the best strategy for an error pattern."""
        pattern = ErrorPattern(
            category="invalid_arguments",
            tool_name="test_tool",
            error_message="Missing required argument 'query'"
        )
        
        # No strategies yet
        self.assertIsNone(pattern.get_best_strategy())
        
        # Add some strategies
        pattern.update("retry", True)
        pattern.update("retry", False)
        pattern.update("alternative_tool", True)
        
        # Check best strategy
        self.assertEqual(pattern.get_best_strategy(), "alternative_tool")
        
        # Add more updates to change the best strategy
        pattern.update("input_reformulation", True)
        pattern.update("input_reformulation", True)
        
        # Check best strategy again
        self.assertEqual(pattern.get_best_strategy(), "input_reformulation")

    def test_to_dict_and_from_dict(self):
        """Test converting an error pattern to and from a dictionary."""
        pattern = ErrorPattern(
            category="invalid_arguments",
            tool_name="test_tool",
            error_message="Missing required argument 'query'"
        )
        
        # Add some strategies
        pattern.update("retry", True)
        pattern.update("retry", False)
        pattern.update("alternative_tool", True)
        
        # Convert to dictionary
        pattern_dict = pattern.to_dict()
        
        # Check dictionary
        self.assertEqual(pattern_dict["category"], "invalid_arguments")
        self.assertEqual(pattern_dict["tool_name"], "test_tool")
        self.assertEqual(pattern_dict["error_message"], "Missing required argument 'query'")
        self.assertEqual(pattern_dict["occurrences"], 3)
        self.assertIn("strategy_effectiveness", pattern_dict)
        
        # Convert back to pattern
        new_pattern = ErrorPattern.from_dict(pattern_dict)
        
        # Check new pattern
        self.assertEqual(new_pattern.category, "invalid_arguments")
        self.assertEqual(new_pattern.tool_name, "test_tool")
        self.assertEqual(new_pattern.error_message, "Missing required argument 'query'")
        self.assertEqual(new_pattern.occurrences, 3)
        self.assertEqual(new_pattern.strategy_effectiveness, pattern.strategy_effectiveness)


class TestErrorLearningManager(unittest.TestCase):
    """Test the ErrorLearningManager class."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary directory for storage
        self.temp_dir = tempfile.mkdtemp()
        self.storage_path = os.path.join(self.temp_dir, "error_patterns.json")
        
        # Create an error learning manager
        self.manager = ErrorLearningManager(
            storage_path=self.storage_path,
            max_patterns=100
        )

    def tearDown(self):
        """Clean up after the test."""
        # Remove the temporary directory
        import shutil
        shutil.rmtree(self.temp_dir)

    def test_track_error(self):
        """Test tracking an error."""
        # Create an error and context
        error = ValueError("Missing required argument 'query'")
        context = {
            "tool_name": "search_tool",
            "tool_args": {},
            "category": "invalid_arguments"
        }
        
        # Create a recovery result
        recovery_result = {
            "success": True,
            "strategy": "input_reformulation",
            "message": "Reformulated input"
        }
        
        # Track the error
        self.manager.track_error(error, context, recovery_result)
        
        # Check that the error pattern was created
        pattern_key = "invalid_arguments:search_tool:Missing required argument 'query'"
        self.assertIn(pattern_key, self.manager.error_patterns)
        
        # Check that the pattern was updated
        pattern = self.manager.error_patterns[pattern_key]
        self.assertEqual(pattern.occurrences, 1)
        self.assertIn("input_reformulation", pattern.strategy_effectiveness)
        
        # Check that the strategy effectiveness was updated
        self.assertIn("input_reformulation", self.manager.strategy_effectiveness)
        self.assertEqual(self.manager.strategy_effectiveness["input_reformulation"]["attempts"], 1)
        self.assertEqual(self.manager.strategy_effectiveness["input_reformulation"]["successes"], 1)
        
        # Check that the patterns were saved
        self.assertTrue(os.path.exists(self.storage_path))

    def test_get_best_strategy(self):
        """Test getting the best strategy for an error."""
        # Add some error patterns
        error1 = ValueError("Missing required argument 'query'")
        context1 = {
            "tool_name": "search_tool",
            "tool_args": {},
            "category": "invalid_arguments"
        }
        recovery_result1 = {
            "success": True,
            "strategy": "input_reformulation",
            "message": "Reformulated input"
        }
        self.manager.track_error(error1, context1, recovery_result1)
        
        error2 = ValueError("Missing required argument 'query'")
        context2 = {
            "tool_name": "search_tool",
            "tool_args": {},
            "category": "invalid_arguments"
        }
        recovery_result2 = {
            "success": False,
            "strategy": "retry",
            "message": "Retry failed"
        }
        self.manager.track_error(error2, context2, recovery_result2)
        
        # Get the best strategy for the same error
        best_strategy = self.manager.get_best_strategy(error1, context1)
        
        # Check that the best strategy is input_reformulation
        self.assertEqual(best_strategy, "input_reformulation")
        
        # Get the best strategy for a different error
        error3 = ValueError("Tool 'nonexistent_tool' not found")
        context3 = {
            "tool_name": "nonexistent_tool",
            "category": "tool_not_found"
        }
        
        # Should return None since we haven't seen this error before
        best_strategy = self.manager.get_best_strategy(error3, context3)
        self.assertIsNone(best_strategy)

    def test_get_strategy_ranking(self):
        """Test getting a ranking of strategies for an error."""
        # Add some error patterns
        error1 = ValueError("Missing required argument 'query'")
        context1 = {
            "tool_name": "search_tool",
            "tool_args": {},
            "category": "invalid_arguments"
        }
        
        # Add some strategies with different success rates
        recovery_result1 = {
            "success": True,
            "strategy": "input_reformulation",
            "message": "Reformulated input"
        }
        self.manager.track_error(error1, context1, recovery_result1)
        
        recovery_result2 = {
            "success": False,
            "strategy": "retry",
            "message": "Retry failed"
        }
        self.manager.track_error(error1, context1, recovery_result2)
        
        recovery_result3 = {
            "success": True,
            "strategy": "alternative_tool",
            "message": "Used alternative tool"
        }
        self.manager.track_error(error1, context1, recovery_result3)
        
        recovery_result4 = {
            "success": True,
            "strategy": "alternative_tool",
            "message": "Used alternative tool"
        }
        self.manager.track_error(error1, context1, recovery_result4)
        
        # Get the strategy ranking
        ranking = self.manager.get_strategy_ranking(error1, context1)
        
        # Check that the ranking is correct
        self.assertEqual(len(ranking), 3)
        self.assertEqual(ranking[0][0], "alternative_tool")  # 2/2 = 1.0
        self.assertEqual(ranking[1][0], "input_reformulation")  # 1/1 = 1.0
        self.assertEqual(ranking[2][0], "retry")  # 0/1 = 0.0
        
        # Check that the ranking is sorted by success rate
        self.assertGreaterEqual(ranking[0][1], ranking[1][1])
        self.assertGreaterEqual(ranking[1][1], ranking[2][1])

    def test_get_stats(self):
        """Test getting error learning statistics."""
        # Add some error patterns
        error1 = ValueError("Missing required argument 'query'")
        context1 = {
            "tool_name": "search_tool",
            "tool_args": {},
            "category": "invalid_arguments"
        }
        recovery_result1 = {
            "success": True,
            "strategy": "input_reformulation",
            "message": "Reformulated input"
        }
        self.manager.track_error(error1, context1, recovery_result1)
        
        error2 = ValueError("Tool 'nonexistent_tool' not found")
        context2 = {
            "tool_name": "nonexistent_tool",
            "category": "tool_not_found"
        }
        recovery_result2 = {
            "success": True,
            "strategy": "alternative_tool",
            "message": "Used alternative tool"
        }
        self.manager.track_error(error2, context2, recovery_result2)
        
        # Get the stats
        stats = self.manager.get_stats()
        
        # Check the stats
        self.assertEqual(stats["total_patterns"], 2)
        self.assertEqual(stats["total_errors"], 2)
        self.assertIn("strategy_effectiveness", stats)
        self.assertIn("top_patterns", stats)
        self.assertEqual(len(stats["top_patterns"]), 2)

    def test_save_and_load_patterns(self):
        """Test saving and loading error patterns."""
        # Add some error patterns
        error1 = ValueError("Missing required argument 'query'")
        context1 = {
            "tool_name": "search_tool",
            "tool_args": {},
            "category": "invalid_arguments"
        }
        recovery_result1 = {
            "success": True,
            "strategy": "input_reformulation",
            "message": "Reformulated input"
        }
        self.manager.track_error(error1, context1, recovery_result1)
        
        # Create a new manager that should load the patterns
        new_manager = ErrorLearningManager(
            storage_path=self.storage_path,
            max_patterns=100
        )
        
        # Check that the patterns were loaded
        pattern_key = "invalid_arguments:search_tool:Missing required argument 'query'"
        self.assertIn(pattern_key, new_manager.error_patterns)
        
        # Check that the pattern was loaded correctly
        pattern = new_manager.error_patterns[pattern_key]
        self.assertEqual(pattern.occurrences, 1)
        self.assertIn("input_reformulation", pattern.strategy_effectiveness)

    def test_prune_patterns(self):
        """Test pruning error patterns."""
        # Set a small max_patterns
        self.manager.max_patterns = 2
        
        # Add more patterns than the limit
        for i in range(3):
            error = ValueError(f"Error {i}")
            context = {
                "tool_name": f"tool_{i}",
                "category": "test"
            }
            recovery_result = {
                "success": True,
                "strategy": "test",
                "message": "Test"
            }
            self.manager.track_error(error, context, recovery_result)
        
        # Check that only the most recent patterns were kept
        self.assertEqual(len(self.manager.error_patterns), 2)
        
        # Check that the oldest pattern was removed
        pattern_key = "test:tool_0:Error 0"
        self.assertNotIn(pattern_key, self.manager.error_patterns)
        
        # Check that the newer patterns were kept
        pattern_key1 = "test:tool_1:Error 1"
        pattern_key2 = "test:tool_2:Error 2"
        self.assertTrue(
            pattern_key1 in self.manager.error_patterns or
            pattern_key2 in self.manager.error_patterns
        )


if __name__ == '__main__':
    unittest.main()
