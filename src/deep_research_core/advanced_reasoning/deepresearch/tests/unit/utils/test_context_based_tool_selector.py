"""
Unit tests for context-based tool selection.

This module contains tests for the ContextBasedToolSelector class.
"""

import unittest
import os
import tempfile
import json
from unittest.mock import patch, MagicMock

from src.deep_research_core.utils.context_based_tool_selector import ContextBasedToolSelector
from src.deep_research_core.utils.tool_selection import ToolSelector
from src.deep_research_core.utils.tool_complexity_analyzer import Tool<PERSON><PERSON>plexityAnalyzer
from src.deep_research_core.tools.base import BaseTool


class MockTool(BaseTool):
    """Mock tool for testing."""

    def __init__(self, name, description="A mock tool for testing"):
        """Initialize the mock tool."""
        super().__init__()
        self.name = name
        self.description = description
        self.call_count = 0

    def run(self, input_text: str = "", **kwargs):
        """Run the mock tool."""
        self.call_count += 1
        return {
            "success": True,
            "input": input_text,
            "result": f"Result from {self.name}: {input_text}"
        }


class TestContextBasedToolSelector(unittest.TestCase):
    """Test the ContextBasedToolSelector class."""

    def setUp(self):
        """Set up test fixtures."""
        # Create mock tools with different descriptions
        self.simple_tool = MockTool("simple_tool", "A simple tool for basic tasks")
        self.complex_tool = MockTool("complex_tool", "An advanced tool for complex analysis")
        self.search_tool = MockTool("search_tool", "A tool for searching information")
        self.math_tool = MockTool("math_tool", "A tool for mathematical calculations")
        self.code_tool = MockTool("code_tool", "A tool for code generation and analysis")
        self.file_tool = MockTool("file_tool", "A tool for file operations and management")
        self.web_tool = MockTool("web_tool", "A tool for web requests and URL processing")

        # Create a temporary directory for storage
        self.temp_dir = tempfile.mkdtemp()
        self.storage_path = os.path.join(self.temp_dir, "tool_selection_history.json")

        # Create a ContextBasedToolSelector instance
        self.tool_selector = ContextBasedToolSelector(
            tools=[
                self.simple_tool, self.complex_tool, self.search_tool, 
                self.math_tool, self.code_tool, self.file_tool, self.web_tool
            ],
            context_weight=0.4,
            history_weight=0.3,
            complexity_weight=0.3,
            storage_path=self.storage_path,
            max_history_items=100
        )

    def tearDown(self):
        """Clean up after the test."""
        # Remove the temporary directory
        import shutil
        shutil.rmtree(self.temp_dir)

    def test_initialization(self):
        """Test initialization of ContextBasedToolSelector."""
        # Check that the selector was initialized correctly
        self.assertEqual(len(self.tool_selector.tools), 7)
        self.assertEqual(self.tool_selector.context_weight, 0.4)
        self.assertEqual(self.tool_selector.history_weight, 0.3)
        self.assertEqual(self.tool_selector.complexity_weight, 0.3)
        self.assertEqual(self.tool_selector.storage_path, self.storage_path)
        self.assertEqual(self.tool_selector.max_history_items, 100)
        
        # Check that the context patterns were initialized
        self.assertIsNotNone(self.tool_selector.context_patterns)
        self.assertTrue(len(self.tool_selector.context_patterns) > 0)
        
        # Check that the tool selector and complexity analyzer were initialized
        self.assertIsNotNone(self.tool_selector.tool_selector)
        self.assertIsNotNone(self.tool_selector.complexity_analyzer)

    def test_analyze_context_with_code(self):
        """Test analyzing context with code blocks."""
        # Create a context with code blocks
        context = """
        Here's a Python code example:
        ```python
        def hello_world():
            print("Hello, world!")
        ```
        
        And here's another one:
        ```
        for i in range(10):
            print(i)
        ```
        """
        
        # Analyze the context
        result = self.tool_selector._analyze_context(context)
        
        # Check that the analysis detected code blocks
        self.assertIn("context_analysis", result)
        self.assertTrue(result["context_analysis"]["has_code"])
        self.assertEqual(result["context_analysis"]["code_block_count"], 2)
        
        # Check that code_tool is recommended
        self.assertIn("recommended_tools", result)
        self.assertIn("code_tool", result["recommended_tools"])

    def test_analyze_context_with_urls(self):
        """Test analyzing context with URLs."""
        # Create a context with URLs
        context = """
        Check out these websites:
        https://example.com
        http://test.org/page.html
        
        They have useful information.
        """
        
        # Analyze the context
        result = self.tool_selector._analyze_context(context)
        
        # Check that the analysis detected URLs
        self.assertIn("context_analysis", result)
        self.assertTrue(result["context_analysis"]["has_urls"])
        self.assertEqual(result["context_analysis"]["url_count"], 2)
        
        # Check that web_tool is recommended
        self.assertIn("recommended_tools", result)
        self.assertIn("web_tool", result["recommended_tools"])

    def test_analyze_context_with_file_paths(self):
        """Test analyzing context with file paths."""
        # Create a context with file paths
        context = """
        Please check these files:
        /path/to/file.txt
        src/main.py
        data/config.json
        """
        
        # Analyze the context
        result = self.tool_selector._analyze_context(context)
        
        # Check that the analysis detected file paths
        self.assertIn("context_analysis", result)
        self.assertTrue(result["context_analysis"]["has_file_paths"])
        self.assertEqual(result["context_analysis"]["file_path_count"], 3)
        
        # Check that file_tool is recommended
        self.assertIn("recommended_tools", result)
        self.assertIn("file_tool", result["recommended_tools"])

    def test_analyze_history(self):
        """Test analyzing interaction history."""
        # Create a mock interaction history
        history = [
            {"query": "How do I calculate the average?", "tools": ["math_tool", "simple_tool"]},
            {"query": "What is 2+2?", "tools": ["math_tool"]},
            {"query": "Search for information about Python", "tools": ["search_tool", "code_tool"]}
        ]
        
        # Analyze the history with a similar query
        result = self.tool_selector._analyze_history(history, "How to calculate the mean of numbers?")
        
        # Check that the analysis found similar queries
        self.assertIn("history_analysis", result)
        self.assertEqual(result["history_analysis"]["similar_queries"], 1)
        
        # Check that math_tool is recommended
        self.assertIn("recommended_tools", result)
        self.assertIn("math_tool", result["recommended_tools"])
        
        # Check that the similar tool usage was recorded
        self.assertIn("similar_tool_usage", result["history_analysis"])
        self.assertIn("math_tool", result["history_analysis"]["similar_tool_usage"])

    def test_is_similar_query(self):
        """Test query similarity detection."""
        # Test similar queries
        self.assertTrue(self.tool_selector._is_similar_query(
            "How do I calculate the average?",
            "How to calculate the mean of numbers?"
        ))
        
        # Test dissimilar queries
        self.assertFalse(self.tool_selector._is_similar_query(
            "How do I calculate the average?",
            "What is the weather today?"
        ))
        
        # Test edge cases
        self.assertFalse(self.tool_selector._is_similar_query("", "test"))
        self.assertFalse(self.tool_selector._is_similar_query("test", ""))
        self.assertFalse(self.tool_selector._is_similar_query("", ""))

    def test_select_tools_with_context(self):
        """Test selecting tools with context."""
        # Create a query and context
        query = "How do I process this code?"
        context = """
        I'm working with this Python code:
        ```python
        def process_data(data):
            return [x * 2 for x in data]
        ```
        """
        
        # Select tools with context
        result = self.tool_selector.select_tools(query, context)
        
        # Check that the result has the expected structure
        self.assertIn("recommended_tools", result)
        self.assertIn("selection_confidence", result)
        self.assertIn("selection_reasoning", result)
        self.assertIn("context_analysis", result)
        
        # Check that code_tool is recommended
        self.assertIn("code_tool", result["recommended_tools"])

    def test_select_tools_with_history(self):
        """Test selecting tools with interaction history."""
        # Add some items to the selection history
        self.tool_selector._add_to_history("How do I calculate the average?", ["math_tool", "simple_tool"], False)
        self.tool_selector._add_to_history("What is 2+2?", ["math_tool"], False)
        
        # Create a query and interaction history
        query = "How to calculate the mean of numbers?"
        history = self.tool_selector.selection_history
        
        # Select tools with history
        result = self.tool_selector.select_tools(query, interaction_history=history)
        
        # Check that the result has the expected structure
        self.assertIn("recommended_tools", result)
        self.assertIn("selection_confidence", result)
        self.assertIn("selection_reasoning", result)
        self.assertIn("history_analysis", result)
        
        # Check that math_tool is recommended
        self.assertIn("math_tool", result["recommended_tools"])

    def test_select_tools_combined(self):
        """Test selecting tools with context, history, and complexity."""
        # Add some items to the selection history
        self.tool_selector._add_to_history("How do I analyze this data?", ["complex_tool", "math_tool"], False)
        
        # Create a query, context, and history
        query = "How should I analyze this dataset?"
        context = """
        I have a large dataset with numerical values and need to perform statistical analysis.
        """
        history = self.tool_selector.selection_history
        
        # Select tools with context and history
        result = self.tool_selector.select_tools(query, context, history)
        
        # Check that the result has the expected structure
        self.assertIn("recommended_tools", result)
        self.assertIn("selection_confidence", result)
        self.assertIn("selection_reasoning", result)
        self.assertIn("context_analysis", result)
        self.assertIn("history_analysis", result)
        
        # Check that complex_tool and math_tool are recommended
        self.assertTrue(
            "complex_tool" in result["recommended_tools"] or 
            "math_tool" in result["recommended_tools"]
        )

    def test_generate_selection_reasoning(self):
        """Test generating selection reasoning."""
        # Create test data
        query = "How do I analyze this code?"
        selected_tools = ["code_tool", "complex_tool"]
        selection_confidence = {"code_tool": 0.8, "complex_tool": 0.6}
        basic_tools = ["code_tool", "search_tool"]
        complexity_tools = ["complex_tool", "code_tool"]
        context_tools = ["code_tool", "file_tool"]
        history_tools = ["code_tool"]
        context_analysis = {
            "has_code": True,
            "code_block_count": 1,
            "matching_patterns": {"code": 2, "analysis": 1}
        }
        history_analysis = {
            "total_interactions": 5,
            "similar_queries": 1,
            "similar_tool_usage": {"code_tool": 1}
        }
        
        # Generate reasoning
        reasoning = self.tool_selector._generate_selection_reasoning(
            query, selected_tools, selection_confidence,
            basic_tools, complexity_tools, context_tools, history_tools,
            context_analysis, history_analysis
        )
        
        # Check that the reasoning contains expected elements
        self.assertIn(query, reasoning)
        self.assertIn("code_tool", reasoning)
        self.assertIn("complex_tool", reasoning)
        self.assertIn("confidence: 0.80", reasoning)
        self.assertIn("confidence: 0.60", reasoning)
        self.assertIn("query relevance", reasoning)
        self.assertIn("query complexity", reasoning)
        self.assertIn("context match", reasoning)
        self.assertIn("interaction history", reasoning)
        self.assertIn("Contains code blocks", reasoning)
        self.assertIn("Similar queries", reasoning)

    def test_add_to_history(self):
        """Test adding selections to history."""
        # Add some items to the selection history
        self.tool_selector._add_to_history("Query 1", ["tool1", "tool2"], True)
        self.tool_selector._add_to_history("Query 2", ["tool3"], False)
        
        # Check that the items were added
        self.assertEqual(len(self.tool_selector.selection_history), 2)
        self.assertEqual(self.tool_selector.selection_history[0]["query"], "Query 1")
        self.assertEqual(self.tool_selector.selection_history[0]["tools"], ["tool1", "tool2"])
        self.assertTrue(self.tool_selector.selection_history[0]["has_context"])
        self.assertEqual(self.tool_selector.selection_history[1]["query"], "Query 2")
        self.assertEqual(self.tool_selector.selection_history[1]["tools"], ["tool3"])
        self.assertFalse(self.tool_selector.selection_history[1]["has_context"])
        
        # Check that the history was saved
        self.assertTrue(os.path.exists(self.storage_path))

    def test_history_limit(self):
        """Test that history is limited to max_history_items."""
        # Set a small max_history_items
        self.tool_selector.max_history_items = 3
        
        # Add more items than the limit
        for i in range(5):
            self.tool_selector._add_to_history(f"Query {i}", [f"tool{i}"], False)
        
        # Check that only the most recent items were kept
        self.assertEqual(len(self.tool_selector.selection_history), 3)
        self.assertEqual(self.tool_selector.selection_history[0]["query"], "Query 2")
        self.assertEqual(self.tool_selector.selection_history[1]["query"], "Query 3")
        self.assertEqual(self.tool_selector.selection_history[2]["query"], "Query 4")

    def test_save_and_load_history(self):
        """Test saving and loading selection history."""
        # Add some items to the selection history
        self.tool_selector._add_to_history("Query 1", ["tool1", "tool2"], True)
        self.tool_selector._add_to_history("Query 2", ["tool3"], False)
        
        # Save the history
        self.tool_selector._save_history()
        
        # Create a new selector that should load the history
        new_selector = ContextBasedToolSelector(
            tools=[self.simple_tool, self.complex_tool],
            storage_path=self.storage_path
        )
        
        # Check that the history was loaded
        self.assertEqual(len(new_selector.selection_history), 2)
        self.assertEqual(new_selector.selection_history[0]["query"], "Query 1")
        self.assertEqual(new_selector.selection_history[0]["tools"], ["tool1", "tool2"])
        self.assertTrue(new_selector.selection_history[0]["has_context"])
        self.assertEqual(new_selector.selection_history[1]["query"], "Query 2")
        self.assertEqual(new_selector.selection_history[1]["tools"], ["tool3"])
        self.assertFalse(new_selector.selection_history[1]["has_context"])

    def test_get_history_stats(self):
        """Test getting history statistics."""
        # Add some items to the selection history
        self.tool_selector._add_to_history("Query 1", ["tool1", "tool2"], True)
        self.tool_selector._add_to_history("Query 2", ["tool1"], False)
        self.tool_selector._add_to_history("Query 1", ["tool2"], True)  # Duplicate query
        
        # Get history stats
        stats = self.tool_selector.get_history_stats()
        
        # Check that the stats have the expected structure
        self.assertIn("total_selections", stats)
        self.assertIn("unique_queries", stats)
        self.assertIn("tool_usage", stats)
        self.assertIn("context_usage", stats)
        self.assertIn("context_usage_percent", stats)
        
        # Check the stats values
        self.assertEqual(stats["total_selections"], 3)
        self.assertEqual(stats["unique_queries"], 2)
        self.assertEqual(stats["tool_usage"]["tool1"], 2)
        self.assertEqual(stats["tool_usage"]["tool2"], 2)
        self.assertEqual(stats["context_usage"], 2)
        self.assertEqual(stats["context_usage_percent"], 2/3 * 100)


if __name__ == '__main__':
    unittest.main()
