"""
Unit tests for feedback persistence in ReActFeedbackProcessor.

This module contains tests for the feedback persistence mechanisms in
ReActFeedbackProcessor, including database operations and feedback history management.
"""

import unittest
import os
import tempfile
import time
# No datetime imports needed
import json
import sqlite3

from src.deep_research_core.utils.react_feedback import ReActFeedbackProcessor


class TestFeedbackPersistence(unittest.TestCase):
    """Test the feedback persistence mechanisms in ReActFeedbackProcessor."""

    def setUp(self):
        """Set up test environment."""
        # Create a temporary database file for feedback using with statement
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            self.temp_db_path = temp_file.name

        # Create ReActFeedbackProcessor instance
        self.processor = ReActFeedbackProcessor(
            feedback_db_path=self.temp_db_path,
            enable_reinforcement_learning=True,
            learning_rate=0.1,
            min_feedback_samples=3,
            beta_prior=(1.0, 1.0),
            use_thompson_sampling=True,
            max_feedback_history=10,  # Small value for testing
            verbose=False,
            feedback_expiration_days=7,  # Short expiration for testing
            normalize_weights=False,
            max_history_size=10
        )

    def tearDown(self):
        """Clean up after tests."""
        os.unlink(self.temp_db_path)

    def test_database_initialization(self):
        """Test that the database is properly initialized."""
        # Skip this test if database connection is not available
        if self.processor.conn is None:
            self.skipTest("Database connection not available")

        # Check that the database file exists
        self.assertTrue(os.path.exists(self.temp_db_path))

        # Connect to the database and check that the tables exist
        conn = sqlite3.connect(self.temp_db_path)
        cursor = conn.cursor()

        # Check for feedback table
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='feedback'")
        self.assertIsNotNone(cursor.fetchone())

        # Check for tool_feedback table
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='tool_feedback'")
        self.assertIsNotNone(cursor.fetchone())

        # Check for strategy_feedback table
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='strategy_feedback'")
        self.assertIsNotNone(cursor.fetchone())

        conn.close()

    def test_store_and_retrieve_feedback(self):
        """Test storing feedback to the database and retrieving it back."""
        # Create test feedback data
        feedback_data = {
            "query": "What is the capital of France?",
            "timestamp": time.time(),
            "feedback_data": json.dumps({
                "tool_feedback": {
                    "web_search": True,
                    "calculator": False
                },
                "strategy_feedback": {
                    "strategy": "direct_answer",
                    "effectiveness": 0.8
                },
                "overall_rating": 4,
                "comments": "Good response, but could be more detailed."
            }),
            "source": "test",
            "session_id": "test_session_1"
        }

        # Store feedback directly using SQL
        if self.processor.conn is not None:
            self.processor.feedback_cursor.execute(
                "INSERT INTO feedback (query, timestamp, feedback_data, source, session_id) "
                "VALUES (?, ?, ?, ?, ?)",
                (
                    feedback_data["query"],
                    feedback_data["timestamp"],
                    feedback_data["feedback_data"],
                    feedback_data["source"],
                    feedback_data["session_id"]
                )
            )
            self.processor.conn.commit()

            # Retrieve feedback
            self.processor.feedback_cursor.execute(
                "SELECT query, feedback_data FROM feedback ORDER BY timestamp DESC LIMIT 1"
            )
            result = self.processor.feedback_cursor.fetchone()

            # Check that the feedback was stored correctly
            self.assertIsNotNone(result)
            if result:
                query, feedback_json = result
                self.assertEqual(query, "What is the capital of France?")

                # Parse the JSON feedback data
                feedback = json.loads(feedback_json)

                # Check tool feedback
                self.assertTrue(feedback["tool_feedback"]["web_search"])
                self.assertFalse(feedback["tool_feedback"]["calculator"])

                # Check strategy feedback
                self.assertEqual(feedback["strategy_feedback"]["strategy"], "direct_answer")
                self.assertEqual(feedback["strategy_feedback"]["effectiveness"], 0.8)

    def test_feedback_expiration(self):
        """Test that expired feedback is properly handled."""
        # Skip this test if database connection is not available
        if self.processor.conn is None:
            self.skipTest("Database connection not available")

        # Create old feedback data (beyond expiration date)
        old_timestamp = time.time() - (10 * 24 * 60 * 60)  # 10 days ago
        old_feedback_data = json.dumps({
            "tool_feedback": {"web_search": True},
            "overall_rating": 3
        })

        # Create recent feedback data
        recent_timestamp = time.time()
        recent_feedback_data = json.dumps({
            "tool_feedback": {"web_search": True},
            "overall_rating": 5
        })

        # Store both feedback entries directly using SQL
        self.processor.feedback_cursor.execute(
            "INSERT INTO feedback (query, timestamp, feedback_data, source, session_id) "
            "VALUES (?, ?, ?, ?, ?)",
            (
                "What is the capital of Germany?",
                old_timestamp,
                old_feedback_data,
                "test",
                "test_session_old"
            )
        )

        self.processor.feedback_cursor.execute(
            "INSERT INTO feedback (query, timestamp, feedback_data, source, session_id) "
            "VALUES (?, ?, ?, ?, ?)",
            (
                "What is the capital of Italy?",
                recent_timestamp,
                recent_feedback_data,
                "test",
                "test_session_recent"
            )
        )
        self.processor.conn.commit()

        # Retrieve feedback with expiration filter (only recent entries)
        expiration_time = time.time() - (self.processor.feedback_expiration_days * 24 * 60 * 60)
        self.processor.feedback_cursor.execute(
            "SELECT COUNT(*) FROM feedback WHERE timestamp > ?",
            (expiration_time,)
        )
        recent_count = self.processor.feedback_cursor.fetchone()[0]

        # Check that only the recent feedback is counted
        self.assertEqual(recent_count, 1)

        # Retrieve all feedback without expiration filter
        self.processor.feedback_cursor.execute("SELECT COUNT(*) FROM feedback")
        all_count = self.processor.feedback_cursor.fetchone()[0]

        # Check that both feedback entries are counted
        self.assertEqual(all_count, 2)

    def test_store_and_load_weights(self):
        """Test storing weights to the database and loading them back."""
        # Skip this test if database connection is not available
        if self.processor.conn is None:
            self.skipTest("Database connection not available")

        # Set some tool weights
        self.processor.tool_weights = {
            "web_search": 0.8,
            "calculator": 0.4,
            "code_interpreter": 0.6
        }

        # Set some strategy weights
        self.processor.strategy_weights = {
            "direct_answer": 0.7,
            "step_by_step": 0.5
        }

        # Store tool weights directly
        for tool_id, weight in self.processor.tool_weights.items():
            self.processor.feedback_cursor.execute(
                "INSERT OR REPLACE INTO tool_stats (tool_id, weight, total_uses, successes, success_rate, beta_alpha, beta_beta) "
                "VALUES (?, ?, ?, ?, ?, ?, ?)",
                (tool_id, weight, 0, 0, 0.5, 1.0, 1.0)
            )

        # Store strategy weights directly
        for strategy_id, weight in self.processor.strategy_weights.items():
            self.processor.feedback_cursor.execute(
                "INSERT OR REPLACE INTO strategy_stats (strategy_id, weight, total_uses, successes, success_rate, beta_alpha, beta_beta) "
                "VALUES (?, ?, ?, ?, ?, ?, ?)",
                (strategy_id, weight, 0, 0, 0.5, 1.0, 1.0)
            )

        self.processor.conn.commit()

        # Create a new processor instance that will load weights from the database
        new_processor = ReActFeedbackProcessor(
            feedback_db_path=self.temp_db_path,
            enable_reinforcement_learning=True
        )

        # Load statistics from the database by directly querying
        # This avoids using protected methods
        if new_processor.conn is not None:
            # Load tool weights
            new_processor.feedback_cursor.execute("SELECT tool_id, weight FROM tool_stats")
            for tool_id, weight in new_processor.feedback_cursor.fetchall():
                new_processor.tool_weights[tool_id] = weight

            # Load strategy weights
            new_processor.feedback_cursor.execute("SELECT strategy_id, weight FROM strategy_stats")
            for strategy_id, weight in new_processor.feedback_cursor.fetchall():
                new_processor.strategy_weights[strategy_id] = weight

        # Check that the tool weights were loaded correctly
        self.assertEqual(new_processor.tool_weights.get("web_search", 0), 0.8)
        self.assertEqual(new_processor.tool_weights.get("calculator", 0), 0.4)
        self.assertEqual(new_processor.tool_weights.get("code_interpreter", 0), 0.6)

        # Check that the strategy weights were loaded correctly
        self.assertEqual(new_processor.strategy_weights.get("direct_answer", 0), 0.7)
        self.assertEqual(new_processor.strategy_weights.get("step_by_step", 0), 0.5)

    def test_feedback_history_limit(self):
        """Test that the feedback history is properly limited."""
        # Skip this test if database connection is not available
        if self.processor.conn is None:
            self.skipTest("Database connection not available")

        # Create and store more feedback entries than the limit
        for i in range(15):  # max_feedback_history is set to 10 in setUp
            timestamp = time.time() - (14 - i)  # Make timestamps sequential
            feedback_data = json.dumps({
                "query_id": f"test_query_{i}",
                "overall_rating": i % 5 + 1
            })

            # Store feedback directly
            self.processor.feedback_cursor.execute(
                "INSERT INTO feedback (query, timestamp, feedback_data, source, session_id) "
                "VALUES (?, ?, ?, ?, ?)",
                (f"Test query {i}", timestamp, feedback_data, "test", f"test_session_{i}")
            )

        self.processor.conn.commit()

        # Count all feedback entries
        self.processor.feedback_cursor.execute("SELECT COUNT(*) FROM feedback")
        total_count = self.processor.feedback_cursor.fetchone()[0]

        # Check that all 15 entries were stored
        self.assertEqual(total_count, 15)

        # Retrieve only the most recent entries (limited by max_feedback_history)
        self.processor.feedback_cursor.execute(
            "SELECT feedback_data FROM feedback ORDER BY timestamp DESC LIMIT ?",
            (self.processor.max_feedback_history,)
        )
        recent_entries = self.processor.feedback_cursor.fetchall()

        # Check that only the maximum number of feedback entries are retrieved
        self.assertEqual(len(recent_entries), 10)  # max_feedback_history is 10

        # Check that the most recent feedback entries are kept
        # The IDs should be the highest ones (5 through 14)
        query_ids = []
        for entry in recent_entries:
            feedback_data = json.loads(entry[0])
            query_ids.append(feedback_data["query_id"])

        for i in range(5, 15):
            self.assertIn(f"test_query_{i}", query_ids)

    def test_concurrent_database_access(self):
        """Test that concurrent database access is handled properly."""
        # Skip this test if database connection is not available
        if self.processor.conn is None:
            self.skipTest("Database connection not available")

        # This test simulates concurrent access by creating multiple connections
        # to the same database file

        # Create additional database connections
        connections = []
        for _ in range(3):
            conn = sqlite3.connect(self.temp_db_path)
            connections.append(conn)

        # Have each connection insert some feedback
        for i, conn in enumerate(connections):
            cursor = conn.cursor()
            timestamp = time.time()
            feedback_data = json.dumps({
                "query_id": f"concurrent_query_{i}",
                "overall_rating": i + 3
            })

            cursor.execute(
                "INSERT INTO feedback (query, timestamp, feedback_data, source, session_id) "
                "VALUES (?, ?, ?, ?, ?)",
                (f"Concurrent test query {i}", timestamp, feedback_data, "test", f"concurrent_session_{i}")
            )
            conn.commit()

        # Close all connections
        for conn in connections:
            conn.close()

        # Count feedback entries from the original processor
        self.processor.feedback_cursor.execute(
            "SELECT COUNT(*) FROM feedback WHERE source = 'test' AND session_id LIKE 'concurrent_session_%'"
        )
        count = self.processor.feedback_cursor.fetchone()[0]

        # Check that all feedback entries were saved correctly
        self.assertEqual(count, 3)

        # Retrieve the feedback entries
        self.processor.feedback_cursor.execute(
            "SELECT feedback_data FROM feedback WHERE source = 'test' AND session_id LIKE 'concurrent_session_%'"
        )
        entries = self.processor.feedback_cursor.fetchall()

        # Check that the feedback entries have the correct query IDs
        query_ids = []
        for entry in entries:
            feedback_data = json.loads(entry[0])
            query_ids.append(feedback_data["query_id"])

        for i in range(3):
            self.assertIn(f"concurrent_query_{i}", query_ids)

    def test_database_error_handling(self):
        """Test that database errors are handled gracefully."""
        # Create a processor with no database path
        # This avoids the error in _initialize_feedback_db
        processor_no_db = ReActFeedbackProcessor(
            feedback_db_path=None,
            enable_reinforcement_learning=False
        )

        # Check that tool_weights and strategy_weights are initialized with defaults
        self.assertIsInstance(processor_no_db.tool_weights, dict)
        self.assertIsInstance(processor_no_db.strategy_weights, dict)

        # Check that tool_effectiveness and strategy_effectiveness are initialized
        self.assertIsInstance(processor_no_db.tool_effectiveness, dict)
        self.assertIsInstance(processor_no_db.strategy_effectiveness, dict)

        # Check that the processor can be used without a database
        self.assertEqual(processor_no_db.get_tool_recommendations("test query", ["web_search"]), [("web_search", 0.5)])


if __name__ == '__main__':
    unittest.main()
