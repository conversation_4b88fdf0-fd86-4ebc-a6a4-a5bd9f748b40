"""
Unit tests for reinforcement learning methods in ReActFeedbackProcessor.

This module contains tests for the reinforcement learning algorithms used in
ReActFeedbackProcessor for adapting tool and strategy weights based on feedback.
"""

import unittest
import os
import tempfile
# No mocks needed for this test
import numpy as np

from src.deep_research_core.utils.react_feedback import ReActFeedbackProcessor


class TestReinforcementLearning(unittest.TestCase):
    """Test the reinforcement learning algorithms in ReActFeedbackProcessor."""

    def setUp(self):
        """Set up test environment."""
        # Create a temporary database file for feedback using with statement
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            self.temp_db_path = temp_file.name

        # Create ReActFeedbackProcessor instance with reinforcement learning enabled
        self.processor = ReActFeedbackProcessor(
            feedback_db_path=self.temp_db_path,
            enable_reinforcement_learning=True,
            learning_rate=0.1,
            min_feedback_samples=3,
            beta_prior=(1.0, 1.0),
            use_thompson_sampling=True,
            max_feedback_history=100,
            verbose=False,
            feedback_expiration_days=30,
            normalize_weights=False,
            max_history_size=100
        )

    def tearDown(self):
        """Clean up after tests."""
        os.unlink(self.temp_db_path)

    def test_tool_weight_adaptation(self):
        """Test that tool weights are adapted correctly based on feedback."""
        # Initial tool weights
        self.processor.tool_weights = {
            "calculator": 0.5,
            "web_search": 0.5,
            "code_interpreter": 0.5
        }

        # Initialize tool effectiveness
        self.processor.tool_effectiveness = {
            "calculator": {
                "uses": 0,
                "successes": 0,
                "success_rate": 0.5,
                "success_count": 0,
                "failure_count": 1
            },
            "web_search": {
                "uses": 0,
                "successes": 0,
                "success_rate": 0.5,
                "success_count": 0,
                "failure_count": 1
            },
            "code_interpreter": {
                "uses": 0,
                "successes": 0,
                "success_rate": 0.5,
                "success_count": 0,
                "failure_count": 1
            }
        }

        # Provide positive feedback for calculator
        feedback_data = {
            "tool_feedback": {
                "calculator": True
            }
        }

        updated_weights = self.processor._adapt_tool_weights(feedback_data)

        # Check that calculator weight increased
        self.assertGreater(updated_weights["calculator"], 0.5)

        # Provide negative feedback for calculator
        feedback_data = {
            "tool_feedback": {
                "calculator": False
            }
        }

        updated_weights = self.processor._adapt_tool_weights(feedback_data)

        # Check that calculator weight decreased
        self.assertLess(updated_weights["calculator"], 0.6)  # Less than previous value

    def test_strategy_weight_adaptation(self):
        """Test that strategy weights are adapted correctly based on feedback."""
        # Initial strategy weights
        self.processor.strategy_weights = {
            "direct_answer": 0.5,
            "step_by_step": 0.5
        }

        # Provide positive feedback for direct_answer strategy
        updated_weights = self.processor._adapt_strategy_weights("direct_answer", 0.8)

        # Check that direct_answer weight increased
        self.assertGreater(updated_weights["direct_answer"], 0.5)

        # Provide negative feedback for step_by_step strategy
        updated_weights = self.processor._adapt_strategy_weights("step_by_step", 0.3)

        # Check that step_by_step weight decreased
        self.assertLess(updated_weights["step_by_step"], 0.5)

    def test_exponential_moving_average_adaptation(self):
        """Test that exponential moving average is applied correctly for weight adaptation."""
        # Initial tool weights
        self.processor.tool_weights = {
            "calculator": 0.5
        }

        # Initialize tool effectiveness
        self.processor.tool_effectiveness = {
            "calculator": {
                "uses": 0,
                "successes": 0,
                "success_rate": 0.5,
                "success_count": 0,
                "failure_count": 1
            }
        }

        # Provide a series of feedback with varying effectiveness
        feedback_sequence = [
            {"tool_feedback": {"calculator": True}},
            {"tool_feedback": {"calculator": True}},
            {"tool_feedback": {"calculator": False}},
            {"tool_feedback": {"calculator": True}},
            {"tool_feedback": {"calculator": True}}
        ]

        weights_history = [0.5]  # Starting weight

        for feedback in feedback_sequence:
            updated_weights = self.processor._adapt_tool_weights(feedback)
            weights_history.append(updated_weights["calculator"])

        # Check that weights follow an exponential moving average pattern
        # After positive feedback, weight should increase
        self.assertGreater(weights_history[1], weights_history[0])
        self.assertGreater(weights_history[2], weights_history[1])

        # After negative feedback, weight should decrease
        self.assertLess(weights_history[3], weights_history[2])

        # After positive feedback again, weight should increase
        self.assertGreater(weights_history[4], weights_history[3])
        self.assertGreater(weights_history[5], weights_history[4])

        # Check that the final weight is higher than initial (3 positive, 1 negative)
        self.assertGreater(weights_history[-1], weights_history[0])

    def test_learning_rate_impact(self):
        """Test that different learning rates have appropriate impact on adaptation speed."""
        # Create processors with different learning rates
        slow_processor = ReActFeedbackProcessor(
            feedback_db_path=self.temp_db_path,
            enable_reinforcement_learning=True,
            learning_rate=0.05,  # Slower learning rate
            min_feedback_samples=3,
            beta_prior=(1.0, 1.0),
            use_thompson_sampling=True
        )

        fast_processor = ReActFeedbackProcessor(
            feedback_db_path=self.temp_db_path,
            enable_reinforcement_learning=True,
            learning_rate=0.2,  # Faster learning rate
            min_feedback_samples=3,
            beta_prior=(1.0, 1.0),
            use_thompson_sampling=True
        )

        # Set initial weights
        slow_processor.tool_weights = {"calculator": 0.5}
        fast_processor.tool_weights = {"calculator": 0.5}

        # Initialize tool effectiveness
        slow_processor.tool_effectiveness = {
            "calculator": {
                "uses": 0,
                "successes": 0,
                "success_rate": 0.5,
                "success_count": 0,
                "failure_count": 1
            }
        }

        fast_processor.tool_effectiveness = {
            "calculator": {
                "uses": 0,
                "successes": 0,
                "success_rate": 0.5,
                "success_count": 0,
                "failure_count": 1
            }
        }

        # Provide the same positive feedback to both
        feedback_data = {
            "tool_feedback": {
                "calculator": True
            }
        }

        slow_weights = slow_processor._adapt_tool_weights(feedback_data)
        fast_weights = fast_processor._adapt_tool_weights(feedback_data)

        # Fast processor should adapt more quickly (larger weight change)
        slow_change = slow_weights["calculator"] - 0.5
        fast_change = fast_weights["calculator"] - 0.5

        self.assertGreater(fast_change, slow_change)

    def test_weight_bounds(self):
        """Test that weights remain within valid bounds [0.1, 1.0]."""
        # Set initial weights
        self.processor.tool_weights = {"calculator": 0.95}  # Near upper bound
        self.processor.strategy_weights = {"direct_answer": 0.15}  # Near lower bound

        # Initialize tool effectiveness
        self.processor.tool_effectiveness = {
            "calculator": {
                "uses": 0,
                "successes": 0,
                "success_rate": 0.5,
                "success_count": 0,
                "failure_count": 1
            }
        }

        # Provide multiple positive feedback for calculator to push it toward upper bound
        for _ in range(10):
            feedback_data = {
                "tool_feedback": {
                    "calculator": True
                }
            }
            updated_tool_weights = self.processor._adapt_tool_weights(feedback_data)

        # Provide multiple negative feedback for direct_answer to push it toward lower bound
        for _ in range(10):
            updated_strategy_weights = self.processor._adapt_strategy_weights("direct_answer", 0.1)

        # Check that weights are clamped to valid range
        self.assertLessEqual(updated_tool_weights["calculator"], 1.0)
        self.assertGreaterEqual(updated_strategy_weights["direct_answer"], 0.1)

    def test_weight_normalization(self):
        """Test that weights are normalized correctly when normalize_weights is enabled."""
        # Create processor with weight normalization
        normalized_processor = ReActFeedbackProcessor(
            feedback_db_path=self.temp_db_path,
            enable_reinforcement_learning=True,
            learning_rate=0.1,
            min_feedback_samples=3,
            beta_prior=(1.0, 1.0),
            use_thompson_sampling=True,
            normalize_weights=True
        )

        # Set initial weights
        normalized_processor.tool_weights = {
            "calculator": 0.5,
            "web_search": 0.7,
            "code_interpreter": 0.3
        }

        # Initialize tool effectiveness
        normalized_processor.tool_effectiveness = {
            "calculator": {
                "uses": 0,
                "successes": 0,
                "success_rate": 0.5,
                "success_count": 0,
                "failure_count": 1
            },
            "web_search": {
                "uses": 0,
                "successes": 0,
                "success_rate": 0.5,
                "success_count": 0,
                "failure_count": 1
            },
            "code_interpreter": {
                "uses": 0,
                "successes": 0,
                "success_rate": 0.5,
                "success_count": 0,
                "failure_count": 1
            }
        }

        # Provide feedback
        feedback_data = {
            "tool_feedback": {
                "calculator": True,
                "web_search": False
            }
        }

        updated_weights = normalized_processor._adapt_tool_weights(feedback_data)

        # Check that weights sum to 1.0
        self.assertAlmostEqual(sum(updated_weights.values()), 1.0, places=5)

    def test_adaptive_learning_rate(self):
        """Test that adaptive learning rate adjusts based on feedback consistency."""
        # Create processor with adaptive learning rate
        adaptive_processor = ReActFeedbackProcessor(
            feedback_db_path=self.temp_db_path,
            enable_reinforcement_learning=True,
            learning_rate=0.1,
            min_feedback_samples=3,
            beta_prior=(1.0, 1.0),
            use_thompson_sampling=True
        )

        # Add method to calculate adaptive learning rate
        def calculate_adaptive_learning_rate(self, tool_id):
            """Calculate adaptive learning rate based on feedback consistency."""
            if not hasattr(self, 'tool_weight_history') or tool_id not in self.tool_weight_history:
                return self.learning_rate

            # Get recent weight history
            history = self.tool_weight_history[tool_id]
            if len(history) < 3:
                return self.learning_rate

            # Calculate variance of recent weights
            recent_weights = history[-3:]
            variance = np.var(recent_weights)

            # Adjust learning rate based on variance
            # High variance (inconsistent feedback) -> lower learning rate
            # Low variance (consistent feedback) -> higher learning rate
            base_rate = self.learning_rate
            if variance > 0.01:
                return base_rate * 0.5  # Reduce learning rate
            return base_rate * 1.5  # Increase learning rate

        # Monkey patch the method
        adaptive_processor.calculate_adaptive_learning_rate = \
            calculate_adaptive_learning_rate.__get__(adaptive_processor)

        # Set initial weights and history
        adaptive_processor.tool_weights = {"calculator": 0.5}
        adaptive_processor.tool_weight_history = {
            "calculator": [0.5, 0.52, 0.54]  # Consistent history
        }

        # Initialize tool effectiveness
        adaptive_processor.tool_effectiveness = {
            "calculator": {
                "uses": 0,
                "successes": 0,
                "success_rate": 0.5,
                "success_count": 0,
                "failure_count": 1
            }
        }

        # Get adaptive learning rate
        adaptive_rate = adaptive_processor.calculate_adaptive_learning_rate("calculator")

        # Should be higher than base rate due to consistent history
        self.assertGreater(adaptive_rate, adaptive_processor.learning_rate)

        # Change to inconsistent history
        adaptive_processor.tool_weight_history["calculator"] = [
            0.3, 0.6, 0.4  # Inconsistent history
        ]

        # Get adaptive learning rate
        adaptive_rate = adaptive_processor.calculate_adaptive_learning_rate("calculator")

        # Should be lower than base rate due to inconsistent history
        self.assertLess(adaptive_rate, adaptive_processor.learning_rate)


if __name__ == '__main__':
    unittest.main()
