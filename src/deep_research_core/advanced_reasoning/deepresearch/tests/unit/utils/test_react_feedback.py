"""
Unit tests for ReActFeedbackProcessor in the Deep Research Core.

This module contains tests for the ReActFeedbackProcessor class, which
processes feedback for ReAct and adapts behavior based on that feedback.
"""

import unittest
import os
import tempfile
import json
import time
from unittest.mock import patch, MagicMock

from deep_research_core.utils.react_feedback import ReActFeedbackProcessor


class TestReActFeedbackProcessor(unittest.TestCase):
    """Tests for the ReActFeedbackProcessor class."""

    def setUp(self):
        """Set up test environment before each test."""
        # Create a temporary database file
        self.temp_db_fd, self.temp_db_path = tempfile.mkstemp()

        # Initialize the processor with the temporary database
        self.processor = ReActFeedbackProcessor(
            feedback_db_path=self.temp_db_path,
            learning_rate=0.1,
            min_feedback_samples=2,  # Small value for testing
            max_feedback_history=100,
            enable_reinforcement_learning=False,
            verbose=False
        )

    def tearDown(self):
        """Clean up after each test."""
        # Close the processor (which closes the database connection)
        self.processor.close()

        # Close and remove the temporary database file
        os.close(self.temp_db_fd)
        os.unlink(self.temp_db_path)

    def test_initialization(self):
        """Test that the processor initializes correctly."""
        self.assertEqual(self.processor.learning_rate, 0.1)
        self.assertEqual(self.processor.min_feedback_samples, 2)
        self.assertEqual(self.processor.max_feedback_history, 100)
        self.assertEqual(self.processor.enable_reinforcement_learning, False)
        self.assertEqual(self.processor.verbose, False)
        self.assertEqual(self.processor.feedback_db_path, self.temp_db_path)

    def test_process_feedback_basic(self):
        """Test basic feedback processing."""
        # Process a simple feedback
        result = self.processor.process_feedback(
            query="What is the capital of France?",
            feedback={
                "overall_rating": 0.8,
                "complexity": 0.3,
                "tool_feedback": {
                    "web_search": True,
                    "calculator": False
                }
            },
            source="user",
            session_id="test_session_1"
        )

        # Check that the feedback was processed successfully
        self.assertIn("feedback_id", result)
        self.assertIn("adaptations", result)
        self.assertEqual(result["message"], "Feedback processed successfully")

        # Check that the tool success rates were updated
        self.assertEqual(self.processor.tool_success_rates["web_search"]["successes"], 1)
        self.assertEqual(self.processor.tool_success_rates["web_search"]["failures"], 0)
        self.assertEqual(self.processor.tool_success_rates["calculator"]["successes"], 0)
        self.assertEqual(self.processor.tool_success_rates["calculator"]["failures"], 1)

        # Check that the query complexity was stored
        self.assertEqual(self.processor.query_complexity_estimates["What is the capital of France?"], 0.3)

    def test_process_feedback_multiple(self):
        """Test processing multiple feedback entries."""
        # Process first feedback
        self.processor.process_feedback(
            query="What is the capital of France?",
            feedback={
                "overall_rating": 0.8,
                "complexity": 0.3,
                "tool_feedback": {
                    "web_search": True
                }
            },
            source="user",
            session_id="test_session_1"
        )

        # Process second feedback for the same tool
        self.processor.process_feedback(
            query="What is the capital of Germany?",
            feedback={
                "overall_rating": 0.7,
                "complexity": 0.3,
                "tool_feedback": {
                    "web_search": True
                }
            },
            source="user",
            session_id="test_session_1"
        )

        # Check that the tool success rates were updated correctly
        self.assertEqual(self.processor.tool_success_rates["web_search"]["successes"], 2)
        self.assertEqual(self.processor.tool_success_rates["web_search"]["failures"], 0)

        # Check that both query complexities were stored
        self.assertEqual(self.processor.query_complexity_estimates["What is the capital of France?"], 0.3)
        self.assertEqual(self.processor.query_complexity_estimates["What is the capital of Germany?"], 0.3)

        # Now we should have enough samples to get tool weights
        recommendations = self.processor.get_tool_recommendations(
            query="What is the capital of Italy?",
            available_tools=["web_search", "calculator"]
        )

        # Check that web_search has a higher weight than calculator
        self.assertEqual(recommendations[0][0], "web_search")
        self.assertGreater(recommendations[0][1], 0.5)  # Should be higher than default

    def test_get_tool_recommendations(self):
        """Test getting tool recommendations."""
        # Add some feedback to establish tool weights
        self.processor.process_feedback(
            query="What is 2+2?",
            feedback={
                "overall_rating": 0.9,
                "complexity": 0.1,
                "tool_feedback": {
                    "calculator": True,
                    "web_search": False
                }
            }
        )

        self.processor.process_feedback(
            query="What is 3+3?",
            feedback={
                "overall_rating": 0.9,
                "complexity": 0.1,
                "tool_feedback": {
                    "calculator": True,
                    "web_search": False
                }
            }
        )

        # Get recommendations
        recommendations = self.processor.get_tool_recommendations(
            query="What is 4+4?",
            available_tools=["calculator", "web_search", "code_interpreter"],
            context={
                "complexity_preference": {
                    "calculator": 0.2,  # Prefers simple queries
                    "web_search": 0.7,  # Prefers complex queries
                    "code_interpreter": 0.5  # Neutral
                }
            }
        )

        # Check that calculator is recommended first for a simple math query
        self.assertEqual(recommendations[0][0], "calculator")
        self.assertGreater(recommendations[0][1], recommendations[1][1])  # Calculator should have higher weight

    def test_get_prompt_adjustments(self):
        """Test getting prompt adjustments."""
        # Add feedback for a complex query with low rating
        self.processor.process_feedback(
            query="Explain the theory of relativity in detail",
            feedback={
                "overall_rating": 0.4,
                "complexity": 0.8
            }
        )

        # Manually add complexity for the test query
        self.processor.query_complexity_estimates["Explain quantum mechanics in detail"] = 0.8

        # Get prompt adjustments for a similar complex query
        adjustments = self.processor.get_prompt_adjustments(
            query="Explain quantum mechanics in detail"
        )

        # Should suggest adding detailed instructions
        self.assertTrue(adjustments.get("add_detailed_instructions", False))

        # Add feedback for a simple query with low rating
        self.processor.process_feedback(
            query="What is 2+2?",
            feedback={
                "overall_rating": 0.4,
                "complexity": 0.1
            }
        )

        # Manually add complexity for the test query
        self.processor.query_complexity_estimates["What is 3+3?"] = 0.1

        # Get prompt adjustments for a similar simple query
        adjustments = self.processor.get_prompt_adjustments(
            query="What is 3+3?"
        )

        # Should not suggest adding detailed instructions for simple queries
        self.assertFalse(adjustments.get("add_detailed_instructions", False))

    def test_adapt_behavior(self):
        """Test behavior adaptation based on feedback."""
        # Process feedback with strategy effectiveness
        result = self.processor.process_feedback(
            query="What is the capital of France?",
            feedback={
                "overall_rating": 0.8,
                "complexity": 0.3,
                "tool_feedback": {
                    "web_search": True
                },
                "strategy_feedback": {
                    "direct_answer": 0.9,
                    "step_by_step": 0.5
                }
            }
        )

        # Check that adaptations were made
        self.assertIn("adaptations", result)
        adaptations = result["adaptations"]

        # Process another feedback to meet min_feedback_samples
        self.processor.process_feedback(
            query="What is the capital of Germany?",
            feedback={
                "overall_rating": 0.7,
                "complexity": 0.3,
                "tool_feedback": {
                    "web_search": True
                },
                "strategy_feedback": {
                    "direct_answer": 0.8,
                    "step_by_step": 0.6
                }
            }
        )

        # Get strategy recommendations
        recommendations = self.processor.get_strategy_recommendations(
            query="What is the capital of Italy?",
            available_strategies=["direct_answer", "step_by_step"]
        )

        # Check that direct_answer has a higher weight
        self.assertEqual(recommendations[0][0], "direct_answer")
        self.assertGreater(recommendations[0][1], recommendations[1][1])

    def test_cleanup_old_feedback(self):
        """Test cleaning up old feedback."""
        # Add some feedback
        self.processor.process_feedback(
            query="Old query",
            feedback={
                "overall_rating": 0.5
            }
        )

        # Manually modify the timestamp to make it old
        cursor = self.processor.conn.cursor()
        cursor.execute(
            "UPDATE feedback SET timestamp = ? WHERE query = ?",
            (time.time() - (31 * 24 * 60 * 60), "Old query")  # 31 days old
        )
        cursor.close()

        # Add some new feedback
        self.processor.process_feedback(
            query="New query",
            feedback={
                "overall_rating": 0.8
            }
        )

        # Force cleanup
        self.processor._cleanup_old_feedback()

        # Check that old feedback was removed
        cursor = self.processor.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM feedback WHERE query = ?", ("Old query",))
        count = cursor.fetchone()[0]
        cursor.close()

        self.assertEqual(count, 0)

        # Check that new feedback remains
        cursor = self.processor.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM feedback WHERE query = ?", ("New query",))
        count = cursor.fetchone()[0]
        cursor.close()

        self.assertEqual(count, 1)


if __name__ == "__main__":
    unittest.main()
