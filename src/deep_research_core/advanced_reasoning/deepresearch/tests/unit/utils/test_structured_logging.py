"""
Unit tests for the structured logging module.

This module contains tests for the structured logging functionality.
"""

import unittest
import os
import tempfile
import json
import logging
from unittest.mock import MagicMock, patch

from src.deep_research_core.utils.structured_logging import (
    StructuredLogger,
    get_logger,
    JSONFormatter
)


class TestStructuredLogging(unittest.TestCase):
    """Test case for the structured logging module."""

    def setUp(self):
        """Set up the test case."""
        # Create a temporary directory for log files
        self.temp_dir = tempfile.mkdtemp()
        self.log_file = os.path.join(self.temp_dir, "test.log")

    def tearDown(self):
        """Clean up after the test."""
        # Remove the temporary directory
        if os.path.exists(self.temp_dir):
            for file in os.listdir(self.temp_dir):
                os.remove(os.path.join(self.temp_dir, file))
            os.rmdir(self.temp_dir)

    def test_get_logger(self):
        """Test getting a logger."""
        # Get a logger
        logger = get_logger(
            name="test_logger",
            level="INFO",
            json_format=True,
            include_stack_info=False,
            log_file=self.log_file,
            log_to_console=True
        )

        # Check the logger
        self.assertIsInstance(logger, StructuredLogger)
        self.assertEqual(logger.logger.name, "test_logger")
        self.assertEqual(logger.logger.level, logging.INFO)
        self.assertTrue(logger.json_format)

        # Check that the logger has handlers
        self.assertGreater(len(logger.logger.handlers), 0)

        # Check that at least one handler is a FileHandler
        file_handlers = [h for h in logger.logger.handlers if isinstance(h, logging.FileHandler)]
        self.assertGreater(len(file_handlers), 0)

        # Check that at least one handler is a StreamHandler
        stream_handlers = [h for h in logger.logger.handlers if isinstance(h, logging.StreamHandler) and not isinstance(h, logging.FileHandler)]
        self.assertGreater(len(stream_handlers), 0)

    def test_json_formatter(self):
        """Test the JSON formatter."""
        # Create a formatter
        formatter = JSONFormatter(include_stack_info=False)

        # Create a log record
        record = logging.LogRecord(
            name="test_logger",
            level=logging.INFO,
            pathname="test.py",
            lineno=1,
            msg="Test message",
            args=(),
            exc_info=None
        )

        # Format the record
        formatted = formatter.format(record)

        # Check that the formatted record is valid JSON
        log_data = json.loads(formatted)

        # Check the log data
        self.assertEqual(log_data["logger"], "test_logger")
        self.assertEqual(log_data["level"], "INFO")
        self.assertEqual(log_data["message"], "Test message")
        self.assertIn("timestamp", log_data)

    def test_json_formatter_with_extra(self):
        """Test the JSON formatter with extra attributes."""
        # Create a formatter
        formatter = JSONFormatter(include_stack_info=False)

        # Create a log record with extra attributes
        record = logging.LogRecord(
            name="test_logger",
            level=logging.INFO,
            pathname="test.py",
            lineno=1,
            msg="Test message",
            args=(),
            exc_info=None
        )
        record.extra = {"key1": "value1", "key2": "value2"}

        # Format the record
        formatted = formatter.format(record)

        # Check that the formatted record is valid JSON
        log_data = json.loads(formatted)

        # Check the log data
        self.assertEqual(log_data["logger"], "test_logger")
        self.assertEqual(log_data["level"], "INFO")
        self.assertEqual(log_data["message"], "Test message")
        self.assertEqual(log_data["key1"], "value1")
        self.assertEqual(log_data["key2"], "value2")

    def test_structured_logger_info(self):
        """Test logging an info message."""
        # Create a logger
        logger = get_logger(
            name="test_logger",
            level="INFO",
            json_format=True,
            include_stack_info=False,
            log_file=self.log_file,
            log_to_console=False
        )

        # Log a message
        logger.info("Test info message")

        # Check that the message was logged to the file
        with open(self.log_file, "r", encoding="utf-8") as f:
            log_content = f.read()

        # Check that the log content is valid JSON
        log_data = json.loads(log_content)

        # Check the log data
        self.assertEqual(log_data["logger"], "test_logger")
        self.assertEqual(log_data["level"], "INFO")
        self.assertEqual(log_data["message"], "Test info message")

    def test_structured_logger_error(self):
        """Test logging an error message."""
        # Create a logger
        logger = get_logger(
            name="test_logger",
            level="INFO",
            json_format=True,
            include_stack_info=False,
            log_file=self.log_file,
            log_to_console=False
        )

        # Log an error message
        logger.error("Test error message")

        # Check that the message was logged to the file
        with open(self.log_file, "r", encoding="utf-8") as f:
            log_content = f.read()

        # Check that the log content is valid JSON
        log_data = json.loads(log_content)

        # Check the log data
        self.assertEqual(log_data["logger"], "test_logger")
        self.assertEqual(log_data["level"], "ERROR")
        self.assertEqual(log_data["message"], "Test error message")

    def test_structured_logger_with_context(self):
        """Test logging with context."""
        # Create a logger
        logger = get_logger(
            name="test_logger",
            level="INFO",
            json_format=True,
            include_stack_info=False,
            log_file=self.log_file,
            log_to_console=False
        )

        # Set context
        logger.with_context(request_id="123", user_id="456")

        # Log a message
        logger.info("Test message with context")

        # Check that the message was logged to the file
        with open(self.log_file, "r", encoding="utf-8") as f:
            log_content = f.read()

        # Check that the log content is valid JSON
        log_data = json.loads(log_content)

        # Check the log data
        self.assertEqual(log_data["logger"], "test_logger")
        self.assertEqual(log_data["level"], "INFO")
        self.assertEqual(log_data["message"], "Test message with context")
        self.assertEqual(log_data["request_id"], "123")
        self.assertEqual(log_data["user_id"], "456")

    def test_structured_logger_with_extra(self):
        """Test logging with extra attributes."""
        # Create a logger
        logger = get_logger(
            name="test_logger",
            level="INFO",
            json_format=True,
            include_stack_info=False,
            log_file=self.log_file,
            log_to_console=False
        )

        # Log a message with extra attributes
        logger.info("Test message with extra", key1="value1", key2="value2")

        # Check that the message was logged to the file
        with open(self.log_file, "r", encoding="utf-8") as f:
            log_content = f.read()

        # Check that the log content is valid JSON
        log_data = json.loads(log_content)

        # Check the log data
        self.assertEqual(log_data["logger"], "test_logger")
        self.assertEqual(log_data["level"], "INFO")
        self.assertEqual(log_data["message"], "Test message with extra")
        self.assertEqual(log_data["key1"], "value1")
        self.assertEqual(log_data["key2"], "value2")

    def test_structured_logger_with_context_and_extra(self):
        """Test logging with context and extra attributes."""
        # Create a logger
        logger = get_logger(
            name="test_logger",
            level="INFO",
            json_format=True,
            include_stack_info=False,
            log_file=self.log_file,
            log_to_console=False
        )

        # Set context
        logger.with_context(request_id="123", user_id="456")

        # Log a message with extra attributes
        logger.info("Test message with context and extra", key1="value1", key2="value2")

        # Check that the message was logged to the file
        with open(self.log_file, "r", encoding="utf-8") as f:
            log_content = f.read()

        # Check that the log content is valid JSON
        log_data = json.loads(log_content)

        # Check the log data
        self.assertEqual(log_data["logger"], "test_logger")
        self.assertEqual(log_data["level"], "INFO")
        self.assertEqual(log_data["message"], "Test message with context and extra")
        self.assertEqual(log_data["request_id"], "123")
        self.assertEqual(log_data["user_id"], "456")
        self.assertEqual(log_data["key1"], "value1")
        self.assertEqual(log_data["key2"], "value2")

    def test_structured_logger_with_exception(self):
        """Test logging an exception."""
        # Create a logger
        logger = get_logger(
            name="test_logger",
            level="INFO",
            json_format=True,
            include_stack_info=True,
            log_file=self.log_file,
            log_to_console=False
        )

        # Log an exception
        try:
            raise ValueError("Test exception")
        except ValueError:
            logger.exception("Test exception message")

        # Check that the message was logged to the file
        with open(self.log_file, "r", encoding="utf-8") as f:
            log_content = f.read()

        # Check that the log content is valid JSON
        log_data = json.loads(log_content)

        # Check the log data
        self.assertEqual(log_data["logger"], "test_logger")
        self.assertEqual(log_data["level"], "ERROR")
        self.assertEqual(log_data["message"], "Test exception message")
        self.assertIn("exception", log_data)
        self.assertEqual(log_data["exception"]["type"], "ValueError")
        self.assertEqual(log_data["exception"]["message"], "Test exception")
        self.assertIn("ValueError: Test exception", str(log_data["exception"]["traceback"]))

    def test_structured_logger_with_stack_info(self):
        """Test logging with stack info."""
        # Create a logger
        logger = get_logger(
            name="test_logger",
            level="INFO",
            json_format=True,
            include_stack_info=True,
            log_file=self.log_file,
            log_to_console=False
        )

        # Log a message with stack info
        logger.info("Test message with stack info", stack_info=True)

        # Check that the message was logged to the file
        with open(self.log_file, "r", encoding="utf-8") as f:
            log_content = f.read()

        # Check that the log content is valid JSON
        log_data = json.loads(log_content)

        # Check the log data
        self.assertEqual(log_data["logger"], "test_logger")
        self.assertEqual(log_data["level"], "INFO")
        self.assertEqual(log_data["message"], "Test message with stack info")
        self.assertIn("stack_info", log_data)

    def test_structured_logger_with_rotation(self):
        """Test logging with file rotation."""
        # Create a logger with size-based rotation
        logger = get_logger(
            name="test_logger",
            level="INFO",
            json_format=True,
            include_stack_info=False,
            log_file=self.log_file,
            log_to_console=False,
            rotation_type="size",
            max_bytes=100,
            backup_count=3
        )

        # Log multiple messages to trigger rotation
        for i in range(100):
            logger.info(f"Test message {i}")

        # Check that the log file exists
        self.assertTrue(os.path.exists(self.log_file))

        # Check that at least one backup file exists
        backup_file = f"{self.log_file}.1"
        self.assertTrue(os.path.exists(backup_file))


if __name__ == "__main__":
    unittest.main()
