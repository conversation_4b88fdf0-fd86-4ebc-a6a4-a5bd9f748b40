"""
Unit tests for intelligent error detection.

This module contains tests for the intelligent error detection system.
"""

import unittest
from unittest.mock import patch, MagicMock

from src.deep_research_core.utils.intelligent_error_detection import (
    ErrorDetector, ErrorCategory, ErrorSeverity
)


class TestErrorDetector(unittest.TestCase):
    """Test the ErrorDetector class."""

    def setUp(self):
        """Set up test fixtures."""
        self.detector = ErrorDetector()

    def test_detect_tool_not_found(self):
        """Test detecting tool not found errors."""
        error = ValueError("Tool 'nonexistent_tool' not found")
        context = {"tool_name": "nonexistent_tool"}
        
        result = self.detector.detect(error, context)
        
        self.assertEqual(result["category"], ErrorCategory.TOOL_NOT_FOUND.value)
        self.assertTrue(result["recoverable"])

    def test_detect_invalid_arguments(self):
        """Test detecting invalid arguments errors."""
        error = TypeError("Missing required argument 'query'")
        context = {"tool_name": "search_tool", "tool_args": {}}
        
        result = self.detector.detect(error, context)
        
        self.assertEqual(result["category"], ErrorCategory.INVALID_ARGUMENTS.value)
        self.assertTrue(result["recoverable"])
        self.assertEqual(result["relevant_info"].get("problematic_arg"), "query")

    def test_detect_permission_denied(self):
        """Test detecting permission denied errors."""
        error = PermissionError("Permission denied: '/root/file.txt'")
        context = {"tool_name": "file_tool", "tool_args": {"path": "/root/file.txt"}}
        
        result = self.detector.detect(error, context)
        
        self.assertEqual(result["category"], ErrorCategory.PERMISSION_DENIED.value)
        self.assertEqual(result["severity"], ErrorSeverity.HIGH.value)

    def test_detect_resource_not_found(self):
        """Test detecting resource not found errors."""
        error = FileNotFoundError("File '/path/to/file.txt' does not exist")
        context = {"tool_name": "file_tool", "tool_args": {"path": "/path/to/file.txt"}}
        
        result = self.detector.detect(error, context)
        
        self.assertEqual(result["category"], ErrorCategory.RESOURCE_NOT_FOUND.value)
        self.assertEqual(result["relevant_info"].get("resource"), "/path/to/file.txt")

    def test_detect_rate_limit(self):
        """Test detecting rate limit errors."""
        error = Exception("Rate limit exceeded: 100 requests per minute")
        context = {"tool_name": "api_tool"}
        
        result = self.detector.detect(error, context)
        
        self.assertEqual(result["category"], ErrorCategory.RATE_LIMIT.value)
        self.assertEqual(result["relevant_info"].get("limit"), "100")
        self.assertEqual(result["relevant_info"].get("period"), "minute")

    def test_detect_timeout(self):
        """Test detecting timeout errors."""
        error = TimeoutError("Request timed out after 30 seconds")
        context = {"tool_name": "api_tool"}
        
        result = self.detector.detect(error, context)
        
        self.assertEqual(result["category"], ErrorCategory.TIMEOUT.value)
        self.assertEqual(result["severity"], ErrorSeverity.MEDIUM.value)

    def test_detect_network_error(self):
        """Test detecting network errors."""
        error = ConnectionError("Connection refused: api.example.com:443")
        context = {"tool_name": "api_tool"}
        
        result = self.detector.detect(error, context)
        
        self.assertEqual(result["category"], ErrorCategory.NETWORK.value)
        self.assertEqual(result["severity"], ErrorSeverity.HIGH.value)

    def test_detect_api_error(self):
        """Test detecting API errors."""
        error = Exception("API error: Invalid API key 'abc123'")
        context = {"tool_name": "api_tool"}
        
        result = self.detector.detect(error, context)
        
        self.assertEqual(result["category"], ErrorCategory.API.value)
        self.assertEqual(result["relevant_info"].get("api_key"), "abc123")

    def test_detect_parsing_error(self):
        """Test detecting parsing errors."""
        error = ValueError("Invalid JSON: Unexpected token at position 10")
        context = {"tool_name": "json_tool"}
        
        result = self.detector.detect(error, context)
        
        self.assertEqual(result["category"], ErrorCategory.PARSING.value)
        self.assertTrue(result["recoverable"])

    def test_detect_execution_error(self):
        """Test detecting execution errors."""
        error = RuntimeError("Error executing tool 'calculator': Division by zero")
        context = {"tool_name": "calculator"}
        
        result = self.detector.detect(error, context)
        
        self.assertEqual(result["category"], ErrorCategory.EXECUTION.value)
        self.assertEqual(result["severity"], ErrorSeverity.HIGH.value)

    def test_detect_unknown_error(self):
        """Test detecting unknown errors."""
        error = Exception("Some unknown error")
        context = {"tool_name": "unknown_tool"}
        
        result = self.detector.detect(error, context)
        
        self.assertEqual(result["category"], ErrorCategory.UNKNOWN.value)
        self.assertEqual(result["severity"], ErrorSeverity.MEDIUM.value)

    def test_detect_critical_error(self):
        """Test detecting critical errors."""
        error = Exception("CRITICAL: System failure, unrecoverable state")
        context = {"tool_name": "system_tool"}
        
        result = self.detector.detect(error, context)
        
        self.assertEqual(result["severity"], ErrorSeverity.CRITICAL.value)
        self.assertFalse(result["recoverable"])

    def test_assess_severity_with_retry_count(self):
        """Test severity assessment with retry count."""
        error = ValueError("Invalid input")
        
        # Test with no retries
        context = {"tool_name": "test_tool", "retry_count": 0}
        result = self.detector.detect(error, context)
        self.assertEqual(result["severity"], ErrorSeverity.MEDIUM.value)
        
        # Test with multiple retries
        context = {"tool_name": "test_tool", "retry_count": 3}
        result = self.detector.detect(error, context)
        self.assertEqual(result["severity"], ErrorSeverity.HIGH.value)
        
        # Test with many retries
        context = {"tool_name": "test_tool", "retry_count": 5}
        result = self.detector.detect(error, context)
        self.assertEqual(result["severity"], ErrorSeverity.CRITICAL.value)

    def test_is_recoverable(self):
        """Test recoverability assessment."""
        # Test recoverable error
        error = ValueError("Invalid input")
        context = {"tool_name": "test_tool"}
        result = self.detector.detect(error, context)
        self.assertTrue(result["recoverable"])
        
        # Test recoverable error with tool alternatives
        error = ValueError("Tool 'old_tool' not found")
        context = {
            "tool_name": "old_tool",
            "tool_alternatives": {"old_tool": ["new_tool"]}
        }
        result = self.detector.detect(error, context)
        self.assertTrue(result["recoverable"])
        
        # Test unrecoverable error
        error = Exception("CRITICAL: System failure")
        context = {"tool_name": "system_tool"}
        result = self.detector.detect(error, context)
        self.assertFalse(result["recoverable"])


if __name__ == '__main__':
    unittest.main()
