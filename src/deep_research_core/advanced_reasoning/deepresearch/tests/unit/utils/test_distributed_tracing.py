"""
Unit tests for the distributed tracing module.

This module contains tests for the distributed tracing functionality.
"""

import unittest
from unittest.mock import MagicMock, patch

from src.deep_research_core.utils.distributed_tracing import (
    trace_function,
    span,
    configure_tracing,
    TracingManager,
    SimpleSpan
)


class TestDistributedTracing(unittest.TestCase):
    """Test case for the distributed tracing module."""

    def setUp(self):
        """Set up the test case."""
        # Create a mock tracing manager
        self.mock_tracing_manager = MagicMock()

        # Patch the global tracing_manager
        self.patcher = patch("src.deep_research_core.utils.distributed_tracing.tracing_manager", self.mock_tracing_manager)
        self.patcher.start()

    def tearDown(self):
        """Clean up after the test."""
        # Stop the patcher
        self.patcher.stop()

    def test_trace_function_decorator(self):
        """Test the trace_function decorator."""
        # Define a function with the decorator
        @trace_function(name="test_function")
        def test_function():
            return "test"

        # Mock the start_trace and end_trace methods
        mock_span = MagicMock()
        self.mock_tracing_manager.start_trace.return_value = mock_span

        # Call the function
        result = test_function()

        # Check the result
        self.assertEqual(result, "test")

        # Check that start_trace was called
        self.mock_tracing_manager.start_trace.assert_called_once_with(
            "test_function",
            None
        )

        # Check that end_span was called
        self.mock_tracing_manager.end_span.assert_called_once_with(mock_span)

    def test_trace_function_with_attributes(self):
        """Test the trace_function decorator with attributes."""
        # Define a function with the decorator
        @trace_function(name="test_function", attributes={"key": "value"})
        def test_function():
            return "test"

        # Mock the start_trace and end_trace methods
        mock_span = MagicMock()
        self.mock_tracing_manager.start_trace.return_value = mock_span

        # Call the function
        result = test_function()

        # Check the result
        self.assertEqual(result, "test")

        # Check that start_trace was called with attributes
        self.mock_tracing_manager.start_trace.assert_called_once_with(
            "test_function",
            {"key": "value"}
        )

    def test_span_context_manager(self):
        """Test the span context manager."""
        # Mock the start_trace and end_trace methods
        mock_span = MagicMock()
        self.mock_tracing_manager.start_trace.return_value = mock_span

        # Use the span context manager
        with span("test_span"):
            pass

        # Check that start_trace was called
        self.mock_tracing_manager.start_trace.assert_called_once_with(
            "test_span",
            None
        )

        # Check that end_span was called
        self.mock_tracing_manager.end_span.assert_called_once_with(mock_span)

    def test_span_with_attributes(self):
        """Test the span context manager with attributes."""
        # Mock the start_trace and end_trace methods
        mock_span = MagicMock()
        self.mock_tracing_manager.start_trace.return_value = mock_span

        # Use the span context manager with attributes
        with span("test_span", attributes={"key": "value"}):
            pass

        # Check that start_trace was called with attributes
        self.mock_tracing_manager.start_trace.assert_called_once_with(
            "test_span",
            {"key": "value"}
        )

    def test_configure_tracing(self):
        """Test configuring tracing."""
        # Patch the TracingManager class
        with patch("src.deep_research_core.utils.distributed_tracing.TracingManager") as mock_tracing_manager_class:
            # Configure tracing
            configure_tracing(
                service_name="test_service",
                use_opentelemetry=True,
                otlp_endpoint="http://localhost:4317"
            )

            # Check that TracingManager was instantiated with the correct arguments
            mock_tracing_manager_class.assert_called_once_with(
                "test_service",
                True,
                "http://localhost:4317"
            )

    def test_simple_span(self):
        """Test the SimpleSpan class."""
        # Create a simple span
        span = SimpleSpan("test_span")

        # Check the span
        self.assertEqual(span.name, "test_span")
        self.assertEqual(span.attributes, {})

        # Set an attribute
        span.set_attribute("key", "value")

        # Check the attribute
        self.assertEqual(span.attributes["key"], "value")

        # Use the span as a context manager
        with span:
            pass

        # Check that the span was ended
        self.assertTrue(span.end_time > 0)

    def test_tracing_manager_initialization(self):
        """Test initializing the TracingManager."""
        # Create a tracing manager
        manager = TracingManager(
            service_name="test_service",
            use_opentelemetry=False
        )

        # Check the manager
        self.assertEqual(manager.service_name, "test_service")
        self.assertFalse(manager.use_opentelemetry)

    def test_tracing_manager_start_trace(self):
        """Test starting a trace with the TracingManager."""
        # Create a tracing manager
        manager = TracingManager(
            service_name="test_service",
            use_opentelemetry=False
        )

        # Start a trace
        span = manager.start_trace("test_span")

        # Check the span
        self.assertIsInstance(span, SimpleSpan)
        self.assertEqual(span.name, "test_span")

    def test_tracing_manager_with_opentelemetry(self):
        """Test the TracingManager with OpenTelemetry."""
        # Skip if OpenTelemetry is not available
        try:
            import opentelemetry.trace
        except ImportError:
            self.skipTest("OpenTelemetry is not available")

        # Patch the OpenTelemetry modules
        with patch("src.deep_research_core.utils.distributed_tracing.OPENTELEMETRY_AVAILABLE", True), \
             patch("src.deep_research_core.utils.distributed_tracing.trace") as mock_trace:

            # Mock the tracer
            mock_tracer = MagicMock()
            mock_trace.get_tracer.return_value = mock_tracer

            # Create a tracing manager
            manager = TracingManager(
                service_name="test_service",
                use_opentelemetry=True
            )

            # Check that get_tracer was called
            mock_trace.get_tracer.assert_called_once_with("test_service", "0.1.0")

            # Start a trace
            mock_span = MagicMock()
            mock_tracer.start_span.return_value = mock_span

            span = manager.start_trace("test_span")

            # Check that start_span was called
            mock_tracer.start_span.assert_called_once_with("test_span")

            # Check that the span was returned
            self.assertEqual(span, mock_span)


if __name__ == "__main__":
    unittest.main()
