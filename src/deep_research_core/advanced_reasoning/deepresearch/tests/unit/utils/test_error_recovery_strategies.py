"""
Unit tests for error recovery strategies.

This module contains tests for the error recovery strategies.
"""

import unittest
from unittest.mock import patch, MagicMock

from src.deep_research_core.utils.error_recovery import (
    ErrorRecoveryStrategy, RetryStrategy, AlternativeToolStrategy,
    InputReformulationStrategy, FallbackResultStrategy
)
from src.deep_research_core.utils.advanced_recovery_strategies import (
    ContextAwareReformulationStrategy, ToolParameterAdjustmentStrategy,
    AdaptiveRecoveryStrategy
)


class TestErrorRecoveryStrategies(unittest.TestCase):
    """Test the error recovery strategies."""

    def test_retry_strategy(self):
        """Test the retry strategy."""
        # Create a test error
        test_error = ValueError("Test error")
        
        # Create a context
        context = {
            "tool_name": "test_tool",
            "tool_args": {"param": "value"},
            "retry_count": 0
        }
        
        # Create a retry strategy
        strategy = RetryStrategy(max_retries=3, initial_delay=0.01)
        
        # Check that the strategy can handle the error
        self.assertTrue(strategy.can_handle(test_error, context))
        
        # Recover from the error
        result = strategy.recover(test_error, context)
        
        # Check the result
        self.assertTrue(result["success"])
        self.assertEqual(result["strategy"], "retry")
        self.assertEqual(context["retry_count"], 1)
        
        # Try again with retry_count at the limit
        context["retry_count"] = 3
        self.assertFalse(strategy.can_handle(test_error, context))

    def test_alternative_tool_strategy(self):
        """Test the alternative tool strategy."""
        # Create a test error
        test_error = ValueError("Tool not found")
        
        # Create a context
        context = {
            "tool_name": "test_tool",
            "tool_args": {"param": "value"},
            "tried_alternatives": []
        }
        
        # Create a tool registry
        tool_registry = {
            "test_tool": MagicMock(),
            "alternative_tool": MagicMock()
        }
        
        # Create tool alternatives
        tool_alternatives = {
            "test_tool": ["alternative_tool"]
        }
        
        # Create an alternative tool strategy
        strategy = AlternativeToolStrategy(
            tool_alternatives=tool_alternatives,
            tool_registry=tool_registry
        )
        
        # Check that the strategy can handle the error
        self.assertTrue(strategy.can_handle(test_error, context))
        
        # Recover from the error
        result = strategy.recover(test_error, context)
        
        # Check the result
        self.assertTrue(result["success"])
        self.assertEqual(result["strategy"], "alternative_tool")
        self.assertEqual(result["alternative_tool_name"], "alternative_tool")
        self.assertEqual(context["tried_alternatives"], ["alternative_tool"])
        
        # Try again with all alternatives tried
        context["tried_alternatives"] = ["alternative_tool"]
        self.assertFalse(strategy.can_handle(test_error, context))

    def test_context_aware_reformulation_strategy(self):
        """Test the context-aware reformulation strategy."""
        # Create a test error
        test_error = ValueError("Invalid query format")
        
        # Create a context
        context = {
            "tool_name": "web_search",
            "tool_args": {"query": "test query"},
            "context_reformulation_attempted": False
        }
        
        # Create a context-aware reformulation strategy
        strategy = ContextAwareReformulationStrategy()
        
        # Check that the strategy can handle the error
        self.assertTrue(strategy.can_handle(test_error, context))
        
        # Recover from the error
        result = strategy.recover(test_error, context)
        
        # Check the result
        self.assertTrue(result["success"])
        self.assertEqual(result["strategy"], "context_aware_reformulation")
        self.assertEqual(result["reformulated_args"]["query"], "test query")
        self.assertTrue(context["context_reformulation_attempted"])
        
        # Try again with reformulation already attempted
        context["context_reformulation_attempted"] = True
        self.assertFalse(strategy.can_handle(test_error, context))

    def test_tool_parameter_adjustment_strategy(self):
        """Test the tool parameter adjustment strategy."""
        # Create a test error
        test_error = TimeoutError("Request timed out")
        
        # Create a context
        context = {
            "tool_name": "api_request",
            "tool_args": {"timeout": 10},
            "parameter_adjustment_attempted": False
        }
        
        # Create a tool parameter adjustment strategy
        strategy = ToolParameterAdjustmentStrategy()
        
        # Check that the strategy can handle the error
        self.assertTrue(strategy.can_handle(test_error, context))
        
        # Recover from the error
        result = strategy.recover(test_error, context)
        
        # Check the result
        self.assertTrue(result["success"])
        self.assertEqual(result["strategy"], "tool_parameter_adjustment")
        self.assertEqual(result["adjusted_args"]["timeout"], 20)
        self.assertTrue(context["parameter_adjustment_attempted"])
        
        # Try again with parameter adjustment already attempted
        context["parameter_adjustment_attempted"] = True
        self.assertFalse(strategy.can_handle(test_error, context))

    def test_adaptive_recovery_strategy(self):
        """Test the adaptive recovery strategy."""
        # Create mock strategies
        mock_strategy1 = MagicMock(spec=ErrorRecoveryStrategy)
        mock_strategy1.name = "strategy1"
        mock_strategy1.effectiveness = 0.8
        mock_strategy1.can_handle.return_value = True
        mock_strategy1.recover.return_value = {
            "success": True,
            "strategy": "strategy1",
            "message": "Recovered with strategy1"
        }
        
        mock_strategy2 = MagicMock(spec=ErrorRecoveryStrategy)
        mock_strategy2.name = "strategy2"
        mock_strategy2.effectiveness = 0.5
        mock_strategy2.can_handle.return_value = True
        mock_strategy2.recover.return_value = {
            "success": True,
            "strategy": "strategy2",
            "message": "Recovered with strategy2"
        }
        
        # Create a mock error learning manager
        mock_error_learning_manager = MagicMock()
        mock_error_learning_manager.get_best_strategy.return_value = "strategy2"
        
        # Create an adaptive recovery strategy
        strategy = AdaptiveRecoveryStrategy(
            strategies=[mock_strategy1, mock_strategy2],
            error_learning_manager=mock_error_learning_manager
        )
        
        # Create a test error
        test_error = ValueError("Test error")
        
        # Create a context
        context = {"test": "context"}
        
        # Check that the strategy can handle the error
        self.assertTrue(strategy.can_handle(test_error, context))
        
        # Recover from the error
        result = strategy.recover(test_error, context)
        
        # Check the result
        self.assertTrue(result["success"])
        self.assertEqual(result["strategy"], "adaptive_recovery")
        self.assertEqual(result["adaptive_strategy"], "strategy2")
        
        # Check that the error learning manager was used
        mock_error_learning_manager.get_best_strategy.assert_called_once_with(test_error, context)
        
        # Check that the chosen strategy was used
        mock_strategy2.recover.assert_called_once_with(test_error, context)
        
        # Check that the strategy effectiveness was updated
        mock_strategy2.update_effectiveness.assert_called_once_with(True)
        
        # Check that the error was tracked
        mock_error_learning_manager.track_error.assert_called_once()


if __name__ == "__main__":
    unittest.main()
