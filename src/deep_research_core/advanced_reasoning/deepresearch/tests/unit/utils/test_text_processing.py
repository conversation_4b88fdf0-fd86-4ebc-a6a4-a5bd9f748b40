"""
Unit tests for text processing utilities.
"""

import unittest
from deep_research_core.utils.text_processing import (
    split_text, normalize_text, extract_sentences
)


class TestTextProcessing(unittest.TestCase):
    """Tests for text processing utilities."""

    def test_split_text_small_text(self):
        """Test splitting text smaller than max_length."""
        text = "This is a short text."
        chunks = split_text(text, max_length=100)
        self.assertEqual(len(chunks), 1)
        self.assertEqual(chunks[0], "This is a short text.")

    def test_split_text_by_paragraph(self):
        """Test splitting text by paragraphs."""
        text = """This is paragraph 1.
        
This is paragraph 2.

This is paragraph 3."""
        chunks = split_text(text, max_length=30, overlap=0)
        self.assertEqual(len(chunks), 3)
        self.assertTrue("paragraph 1" in chunks[0])
        self.assertTrue("paragraph 2" in chunks[1])
        self.assertTrue("paragraph 3" in chunks[2])

    def test_split_text_fixed_size(self):
        """Test splitting text by fixed size."""
        text = "This is a long text that needs to be split into multiple chunks."
        chunks = split_text(
            text, 
            max_length=20, 
            overlap=5, 
            split_by_paragraph=False
        )
        
        # Check number of chunks
        self.assertEqual(len(chunks), 3)
        
        # Check first chunk
        self.assertEqual(chunks[0], "This is a long text ")
        
        # Check overlap in second chunk
        self.assertTrue(chunks[1].startswith("text "))
        
        # Check last chunk
        self.assertTrue("chunks." in chunks[2])

    def test_split_text_with_long_paragraph(self):
        """Test splitting a text with a long paragraph."""
        text = "Short paragraph.\n\n" + "x" * 100 + "\n\nFinal paragraph."
        chunks = split_text(text, max_length=50, overlap=10)
        
        # Should split into at least 3 chunks
        self.assertGreaterEqual(len(chunks), 3)
        
        # First chunk should contain the short paragraph
        self.assertTrue("Short paragraph" in chunks[0])
        
        # Last chunk should contain the final paragraph
        self.assertTrue("Final paragraph" in chunks[-1])

    def test_normalize_text_lowercase(self):
        """Test normalizing text to lowercase."""
        text = "This is MIXED case Text."
        result = normalize_text(text, lowercase=True)
        self.assertEqual(result, "this is mixed case text.")

    def test_normalize_text_remove_punctuation(self):
        """Test removing punctuation from text."""
        text = "Hello, world! How are you?"
        result = normalize_text(text, remove_punctuation=True)
        self.assertEqual(result, "hello world how are you")

    def test_normalize_text_remove_whitespace(self):
        """Test removing extra whitespace from text."""
        text = "  Too   much \n\n whitespace.  "
        result = normalize_text(text)
        self.assertEqual(result, "too much whitespace.")

    def test_normalize_text_all_options(self):
        """Test normalizing text with all options."""
        text = "  Hello,  WORLD! \n How are you?  "
        result = normalize_text(
            text, 
            lowercase=True, 
            remove_punctuation=True,
            remove_extra_whitespace=True
        )
        self.assertEqual(result, "hello world how are you")

    def test_extract_sentences_basic(self):
        """Test basic sentence extraction."""
        text = "This is sentence one. This is sentence two! Is this sentence three?"
        sentences = extract_sentences(text)
        self.assertEqual(len(sentences), 3)
        self.assertEqual(sentences[0], "This is sentence one")
        self.assertEqual(sentences[1], "This is sentence two")
        self.assertEqual(sentences[2], "Is this sentence three")

    def test_extract_sentences_min_length(self):
        """Test sentence extraction with minimum length."""
        text = "Short. This is a longer sentence. Another one."
        sentences = extract_sentences(text, min_length=10)
        self.assertEqual(len(sentences), 2)
        self.assertEqual(sentences[0], "This is a longer sentence")
        self.assertEqual(sentences[1], "Another one")

    def test_extract_sentences_max_length(self):
        """Test sentence extraction with maximum length."""
        text = "This is a very long sentence with lots of words, commas, and other punctuation that should be split into smaller parts. Short sentence."
        sentences = extract_sentences(text, max_length=40)
        self.assertGreater(len(sentences), 2)  # Should split the long sentence
        self.assertTrue(all(len(s) <= 40 for s in sentences))  # All parts <= max_length

if __name__ == "__main__":
    unittest.main() 