"""
Unit tests for the ToTRAG Visualizer and API endpoints.
"""

import os
import unittest
from unittest.mock import patch, MagicMock
import json
import tempfile
from fastapi.testclient import TestClient

from deep_research_core.visualization.tot_rag_visualizer import ToTRAGVisualizer
from deep_research_core.visualization.interactive_visualizer import InteractiveVisualizer
from deep_research_core.api.app import app
from deep_research_core.api.routes.visualization import visualization_router

class TestToTRAGVisualizer(unittest.TestCase):
    """Tests for the ToTRAG Visualizer class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary directory for test outputs
        self.temp_dir = tempfile.mkdtemp()
        self.visualizer = ToTRAGVisualizer(output_dir=self.temp_dir)
        
        # Create test data
        self.test_result = {
            "query": "What are the benefits of ToT reasoning?",
            "best_paths": [
                [0.9, "Path 1 step 1\n\nPath 1 step 2"],
                [0.7, "Path 2 step 1\n\nPath 2 step 2"]
            ],
            "retrieved_documents": [
                {
                    "content": "Document 1 content",
                    "source": "Source 1",
                    "title": "Title 1"
                },
                {
                    "content": "Document 2 content",
                    "source": "Source 2",
                    "title": "Title 2"
                }
            ]
        }
    
    def tearDown(self):
        """Clean up test fixtures."""
        # Clean up temporary files
        for file in os.listdir(self.temp_dir):
            os.remove(os.path.join(self.temp_dir, file))
        os.rmdir(self.temp_dir)
    
    @patch('matplotlib.pyplot.savefig')
    def test_visualize_reasoning_tree(self, mock_savefig):
        """Test visualize_reasoning_tree method."""
        # Configure mock
        mock_savefig.return_value = None
        
        # Call method
        output_path = self.visualizer.visualize_reasoning_tree(
            self.test_result,
            filename="test_tree.png",
            show=False
        )
        
        # Check that savefig was called
        mock_savefig.assert_called_once()
        
        # Check that output path is correct
        self.assertEqual(output_path, os.path.join(self.temp_dir, "test_tree.png"))
    
    @patch('matplotlib.pyplot.savefig')
    def test_visualize_document_retrieval(self, mock_savefig):
        """Test visualize_document_retrieval method."""
        # Configure mock
        mock_savefig.return_value = None
        
        # Call method
        output_path = self.visualizer.visualize_document_retrieval(
            self.test_result,
            filename="test_docs.png",
            show=False
        )
        
        # Check that savefig was called
        mock_savefig.assert_called_once()
        
        # Check that output path is correct
        self.assertEqual(output_path, os.path.join(self.temp_dir, "test_docs.png"))
    
    @patch('matplotlib.pyplot.savefig')
    def test_create_comprehensive_visualization(self, mock_savefig):
        """Test create_comprehensive_visualization method."""
        # Configure mock
        mock_savefig.return_value = None
        
        # Call method
        output_paths = self.visualizer.create_comprehensive_visualization(
            self.test_result,
            filename_prefix="test",
            show=False
        )
        
        # Check that savefig was called multiple times
        self.assertGreater(mock_savefig.call_count, 1)
        
        # Check that output paths are returned
        self.assertIsInstance(output_paths, list)
        self.assertGreater(len(output_paths), 0)


class TestVisualizationAPI(unittest.TestCase):
    """Tests for the visualization API endpoints."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.client = TestClient(app)
        
        # Create test data
        self.test_request = {
            "result": {
                "query": "What are the benefits of ToT reasoning?",
                "best_paths": [
                    [0.9, "Path 1 step 1\n\nPath 1 step 2"],
                    [0.7, "Path 2 step 1\n\nPath 2 step 2"]
                ],
                "retrieved_documents": [
                    {
                        "content": "Document 1 content",
                        "source": "Source 1",
                        "title": "Title 1"
                    },
                    {
                        "content": "Document 2 content",
                        "source": "Source 2",
                        "title": "Title 2"
                    }
                ]
            },
            "filename_prefix": "test_api"
        }
    
    @patch('deep_research_core.visualization.tot_rag_visualizer.ToTRAGVisualizer.create_comprehensive_visualization')
    @patch('deep_research_core.visualization.interactive_visualizer.InteractiveVisualizer.create_interactive_dashboard')
    def test_generate_visualization_endpoint(self, mock_interactive, mock_comprehensive):
        """Test the /visualization/generate endpoint."""
        # Configure mocks
        mock_comprehensive.return_value = [
            "/path/to/test_reasoning_tree.png",
            "/path/to/test_document_retrieval.png"
        ]
        mock_interactive.return_value = "/path/to/test_dashboard.html"
        
        # Send request
        response = self.client.post(
            "/visualization/generate",
            json=self.test_request
        )
        
        # Check response
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        # Check returned URLs
        self.assertIn("html_url", data)
        self.assertIn("image_urls", data)
        self.assertEqual(len(data["image_urls"]), 2)
        
        # Check that the visualizers were called
        mock_comprehensive.assert_called_once()
        mock_interactive.assert_called_once()
    
    def test_tree_data_endpoint(self):
        """Test the /visualization/tree-data endpoint."""
        # Send request
        response = self.client.get(
            "/visualization/tree-data",
            params={"result_id": "test123"}
        )
        
        # Check response
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        # Check returned data structure
        self.assertIn("name", data)
        self.assertIn("children", data)
        self.assertIsInstance(data["children"], list)


if __name__ == "__main__":
    unittest.main() 