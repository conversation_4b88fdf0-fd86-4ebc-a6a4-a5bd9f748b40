"""Unit tests for the TrajectoryCollector class."""

import os
import json
import shutil
import tempfile
import unittest
from unittest.mock import MagicMock, patch

from deep_research_core.rl_tuning.trajectories.trajectory_collector import TrajectoryCollector


class TestTrajectoryCollector(unittest.TestCase):
    """Test cases for the TrajectoryCollector class."""

    def setUp(self):
        """Set up test environment before each test case."""
        # Create a temporary directory for testing
        self.temp_dir = tempfile.mkdtemp()
        
        # Mock model and environment
        self.mock_model = MagicMock()
        self.mock_model.generate.return_value = "Mock model response"
        
        self.mock_env = MagicMock()
        self.mock_env.reset.return_value = {"observation": "Initial state"}
        self.mock_env.step.return_value = (
            {"observation": "New state", "reasoning_steps": 1},
            1.0,
            False,
            {"info": "test"}
        )
        
        # Sample tasks for testing
        self.tasks = [
            {
                "id": "test_task_1",
                "query": "Test query 1",
                "type": "test",
                "difficulty": "easy"
            },
            {
                "id": "test_task_2",
                "query": "Test query 2",
                "type": "test",
                "difficulty": "medium"
            }
        ]
        
        # Create collector with mock objects
        self.collector = TrajectoryCollector(
            storage_path=self.temp_dir,
            models={"test_model": self.mock_model},
            environments={"test_env": self.mock_env},
            tasks=self.tasks,
            max_trajectories_per_task=2,
            save_interval=1,
            verbose=True
        )

    def tearDown(self):
        """Clean up after each test case."""
        # Remove the temporary directory
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_initialization(self):
        """Test proper initialization of the collector."""
        # Check that directories were created
        self.assertTrue(os.path.exists(os.path.join(self.temp_dir, "raw")))
        self.assertTrue(os.path.exists(os.path.join(self.temp_dir, "processed")))
        self.assertTrue(os.path.exists(os.path.join(self.temp_dir, "filtered")))
        
        # Check that models and environments were stored
        self.assertEqual(len(self.collector.models), 1)
        self.assertEqual(len(self.collector.environments), 1)
        self.assertIn("test_model", self.collector.models)
        self.assertIn("test_env", self.collector.environments)
        
        # Check that tasks were stored
        self.assertEqual(len(self.collector.tasks), 2)

    def test_add_model(self):
        """Test adding a model to the collector."""
        # Create a new mock model
        new_model = MagicMock()
        
        # Add the model
        self.collector.add_model("new_model", new_model)
        
        # Check that the model was added
        self.assertEqual(len(self.collector.models), 2)
        self.assertIn("new_model", self.collector.models)
        self.assertEqual(self.collector.models["new_model"], new_model)

    def test_add_environment(self):
        """Test adding an environment to the collector."""
        # Create a new mock environment
        new_env = MagicMock()
        
        # Add the environment
        self.collector.add_environment("new_env", new_env)
        
        # Check that the environment was added
        self.assertEqual(len(self.collector.environments), 2)
        self.assertIn("new_env", self.collector.environments)
        self.assertEqual(self.collector.environments["new_env"], new_env)

    def test_add_task(self):
        """Test adding a task to the collector."""
        # Create a new task
        new_task = {
            "id": "test_task_3",
            "query": "Test query 3",
            "type": "test",
            "difficulty": "hard"
        }
        
        # Add the task
        self.collector.add_task(new_task)
        
        # Check that the task was added
        self.assertEqual(len(self.collector.tasks), 3)
        task_ids = [task["id"] for task in self.collector.tasks]
        self.assertIn("test_task_3", task_ids)

    def test_load_tasks_from_file(self):
        """Test loading tasks from a file."""
        # Create a temporary tasks file
        tasks_file = os.path.join(self.temp_dir, "test_tasks.json")
        with open(tasks_file, "w") as f:
            json.dump(self.tasks, f)
        
        # Create a new collector
        new_collector = TrajectoryCollector(
            storage_path=self.temp_dir,
            models={"test_model": self.mock_model},
            environments={"test_env": self.mock_env},
            tasks=[],
            max_trajectories_per_task=2,
            save_interval=1,
            verbose=True
        )
        
        # Load tasks from file
        new_collector.load_tasks_from_file(tasks_file)
        
        # Check that tasks were loaded
        self.assertEqual(len(new_collector.tasks), 2)
        task_ids = [task["id"] for task in new_collector.tasks]
        self.assertIn("test_task_1", task_ids)
        self.assertIn("test_task_2", task_ids)

    @patch("deep_research_core.rl_tuning.trajectories.trajectory_collector.uuid.uuid4")
    def test_collect_trajectory(self, mock_uuid):
        """Test collecting a single trajectory."""
        # Mock UUID to get a deterministic ID
        mock_uuid.return_value = "test-uuid"
        
        # Collect a trajectory
        trajectory = self.collector.collect_trajectory(
            model_name="test_model",
            environment_name="test_env",
            task=self.tasks[0]
        )
        
        # Check the trajectory
        self.assertIsNotNone(trajectory)
        self.assertEqual(trajectory["id"], "test-uuid")
        self.assertEqual(trajectory["model"], "test_model")
        self.assertEqual(trajectory["environment"], "test_env")
        self.assertEqual(trajectory["task"]["id"], "test_task_1")
        self.assertIn("states", trajectory)
        self.assertIn("actions", trajectory)
        self.assertIn("rewards", trajectory)
        self.assertIn("timestamps", trajectory)

    def test_save_load_trajectories(self):
        """Test saving and loading trajectories."""
        # Create a sample trajectory
        trajectory = {
            "id": "test-trajectory",
            "model": "test_model",
            "environment": "test_env",
            "task": self.tasks[0],
            "states": [{"observation": "Initial state"}, {"observation": "New state"}],
            "actions": ["action1"],
            "rewards": [1.0],
            "timestamps": [0, 1],
            "metadata": {"duration": 1.0}
        }
        
        # Create a list of trajectories
        trajectories = [trajectory]
        
        # Save trajectories
        self.collector.save_trajectories(trajectories, subdir="raw")
        
        # Load trajectories
        loaded_trajectories = self.collector.load_trajectories(subdir="raw")
        
        # Check that trajectories were saved and loaded correctly
        self.assertEqual(len(loaded_trajectories), 1)
        self.assertEqual(loaded_trajectories[0]["id"], "test-trajectory")
        self.assertEqual(loaded_trajectories[0]["model"], "test_model")
        self.assertEqual(loaded_trajectories[0]["task"]["id"], "test_task_1")

    def test_filter_trajectories(self):
        """Test filtering trajectories."""
        # Create sample trajectories
        trajectories = [
            {
                "id": "traj1",
                "model": "test_model",
                "environment": "test_env",
                "task": {"id": "test_task_1", "difficulty": "easy"},
                "metadata": {"total_reward": 5.0, "success": True}
            },
            {
                "id": "traj2",
                "model": "test_model",
                "environment": "test_env",
                "task": {"id": "test_task_2", "difficulty": "medium"},
                "metadata": {"total_reward": 2.0, "success": False}
            }
        ]
        
        # Define filter function to select trajectories with reward > 3
        def filter_func(traj):
            return traj["metadata"]["total_reward"] > 3.0
        
        # Filter trajectories
        filtered = self.collector.filter_trajectories(trajectories, filter_func)
        
        # Check that filtering worked correctly
        self.assertEqual(len(filtered), 1)
        self.assertEqual(filtered[0]["id"], "traj1")

    def test_preprocess_trajectories(self):
        """Test preprocessing trajectories."""
        # Create sample trajectories
        trajectories = [
            {
                "id": "traj1",
                "model": "test_model",
                "environment": "test_env",
                "task": {"id": "test_task_1"},
                "states": [{"obs": "state1"}, {"obs": "state2"}],
                "actions": ["action1"],
                "rewards": [1.0]
            }
        ]
        
        # Define preprocessing function to add cumulative rewards
        def preprocess_func(traj):
            traj["cumulative_reward"] = sum(traj["rewards"])
            return traj
        
        # Preprocess trajectories
        processed = self.collector.preprocess_trajectories(trajectories, preprocess_func)
        
        # Check that preprocessing worked correctly
        self.assertEqual(len(processed), 1)
        self.assertEqual(processed[0]["id"], "traj1")
        self.assertIn("cumulative_reward", processed[0])
        self.assertEqual(processed[0]["cumulative_reward"], 1.0)

    @patch.object(TrajectoryCollector, "collect_trajectory")
    def test_collect_batch(self, mock_collect_trajectory):
        """Test collecting a batch of trajectories."""
        # Mock the collect_trajectory method
        mock_collect_trajectory.return_value = {"id": "mock-trajectory"}
        
        # Collect a batch
        trajectories = self.collector.collect_batch(
            model_name="test_model",
            environment_name="test_env",
            tasks=self.tasks,
            trajectories_per_task=1
        )
        
        # Check that collect_trajectory was called twice (once for each task)
        self.assertEqual(mock_collect_trajectory.call_count, 2)
        
        # Check that two trajectories were returned
        self.assertEqual(len(trajectories), 2)

    @patch.object(TrajectoryCollector, "collect_batch")
    def test_collect_trajectories(self, mock_collect_batch):
        """Test collecting trajectories with all models and environments."""
        # Mock the collect_batch method
        mock_collect_batch.return_value = [{"id": "mock-trajectory"}]
        
        # Collect trajectories
        num_collected = self.collector.collect_trajectories()
        
        # Check that collect_batch was called once
        self.assertEqual(mock_collect_batch.call_count, 1)
        
        # Check that one trajectory was collected
        self.assertEqual(num_collected, 1)

    def test_get_statistics(self):
        """Test getting statistics about trajectories."""
        # Create some sample trajectories
        trajectories = [
            {
                "id": "traj1",
                "model": "model1",
                "environment": "env1",
                "task": {"id": "task1", "type": "math", "difficulty": "easy"},
                "metadata": {"total_reward": 5.0, "success": True}
            },
            {
                "id": "traj2",
                "model": "model2",
                "environment": "env1",
                "task": {"id": "task2", "type": "logic", "difficulty": "hard"},
                "metadata": {"total_reward": 2.0, "success": False}
            }
        ]
        
        # Save trajectories
        self.collector.save_trajectories(trajectories, subdir="raw")
        
        # Get statistics
        stats = self.collector.get_statistics()
        
        # Check statistics
        self.assertEqual(stats["total_trajectories"], 2)
        self.assertEqual(stats["by_model"]["model1"], 1)
        self.assertEqual(stats["by_model"]["model2"], 1)
        self.assertEqual(stats["by_task_type"]["math"], 1)
        self.assertEqual(stats["by_task_type"]["logic"], 1)
        self.assertEqual(stats["by_difficulty"]["easy"], 1)
        self.assertEqual(stats["by_difficulty"]["hard"], 1)
        self.assertEqual(stats["by_success"]["success"], 1)
        self.assertEqual(stats["by_success"]["failure"], 1)


if __name__ == "__main__":
    unittest.main() 