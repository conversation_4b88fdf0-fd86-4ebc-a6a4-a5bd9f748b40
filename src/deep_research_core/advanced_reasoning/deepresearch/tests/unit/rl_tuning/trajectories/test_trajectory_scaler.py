"""
Unit tests for the TrajectoryScaler in the RL tuning module.

Tests the functionality of the TrajectoryScaler class for scaling trajectories
based on diversity, performance, and other metrics.
"""

import unittest
from unittest.mock import MagicMock, patch
import json
import tempfile
import os
import numpy as np
from typing import List, Dict, Any, Optional

from deep_research_core.rl_tuning.trajectories.trajectory_scaler import TrajectoryScaler


class TestTrajectoryScaler(unittest.TestCase):
    """Test cases for the TrajectoryScaler class."""
    
    def setUp(self):
        """Set up test environment before each test."""
        self.scaler = TrajectoryScaler(
            base_num_trajectories=2,
            max_trajectories=5,
            diversity_weight=0.6,
            performance_weight=0.4
        )
        
        # Sample trajectories
        self.trajectories = [
            {
                "steps": [
                    {"observation": "You are in a room", "action": "look around", "reward": 0.1},
                    {"observation": "You see a door", "action": "open door", "reward": 0.2},
                    {"observation": "The door opens", "action": "go through", "reward": 0.5}
                ],
                "total_reward": 0.8,
                "metadata": {"length": 3, "success": True}
            },
            {
                "steps": [
                    {"observation": "You are in a room", "action": "check window", "reward": 0.2},
                    {"observation": "The window is locked", "action": "break window", "reward": -0.1},
                    {"observation": "The window breaks", "action": "go through window", "reward": 0.5}
                ],
                "total_reward": 0.6,
                "metadata": {"length": 3, "success": False}
            },
            {
                "steps": [
                    {"observation": "You are in a room", "action": "search furniture", "reward": 0.3},
                    {"observation": "You find a key", "action": "take key", "reward": 0.2},
                    {"observation": "You have a key", "action": "use key on door", "reward": 0.4}
                ],
                "total_reward": 0.9,
                "metadata": {"length": 3, "success": True}
            },
            {
                "steps": [
                    {"observation": "You are in a room", "action": "sit down", "reward": 0.0},
                    {"observation": "You are sitting", "action": "stand up", "reward": 0.0},
                    {"observation": "You are standing", "action": "check ceiling", "reward": 0.1}
                ],
                "total_reward": 0.1,
                "metadata": {"length": 3, "success": False}
            }
        ]
        
        # Sample tasks with different difficulties
        self.tasks = {
            "easy": {"id": "task_easy", "difficulty": "easy", "description": "An easy task"},
            "medium": {"id": "task_medium", "difficulty": "medium", "description": "A medium task"},
            "hard": {"id": "task_hard", "difficulty": "hard", "description": "A hard task"},
            "very_hard": {"id": "task_very_hard", "difficulty": "very_hard", "description": "A very hard task"}
        }

    def test_initialization(self):
        """Test initialization of TrajectoryScaler."""
        self.assertEqual(self.scaler.base_num_trajectories, 2)
        self.assertEqual(self.scaler.max_trajectories, 5)
        self.assertEqual(self.scaler.diversity_weight, 0.6)
        self.assertEqual(self.scaler.performance_weight, 0.4)
        
        # Test with custom parameters
        custom_scaler = TrajectoryScaler(
            base_num_trajectories=3,
            max_trajectories=10,
            diversity_weight=0.7,
            performance_weight=0.3
        )
        
        self.assertEqual(custom_scaler.base_num_trajectories, 3)
        self.assertEqual(custom_scaler.max_trajectories, 10)
        self.assertEqual(custom_scaler.diversity_weight, 0.7)
        self.assertEqual(custom_scaler.performance_weight, 0.3)
        
        # Test with invalid weights
        with self.assertRaises(ValueError):
            TrajectoryScaler(
                base_num_trajectories=2,
                max_trajectories=5,
                diversity_weight=0.7,
                performance_weight=0.7  # Sum > 1.0
            )

    def test_determine_optimal_scaling(self):
        """Test determining optimal trajectory scaling based on task difficulty."""
        # Test scaling for different difficulties
        for difficulty, expected_scale in [("easy", 1), ("medium", 2), ("hard", 3), ("very_hard", 4)]:
            scaling = self.scaler.determine_optimal_scaling(self.tasks[difficulty])
            self.assertEqual(scaling, expected_scale)
        
        # Test with resource constraints
        high_resource_usage = {
            "memory_usage": 0.9,
            "cpu_usage": 0.9,
            "time_budget": 0.5
        }
        
        # Hard task with resource constraints should scale down
        scaling_with_constraints = self.scaler.determine_optimal_scaling(
            self.tasks["hard"], 
            resource_usage=high_resource_usage
        )
        
        # Should scale down from 3 due to resource constraints
        self.assertLess(scaling_with_constraints, 3)

    def test_scale_trajectories_no_scaling(self):
        """Test scaling trajectories with no additional trajectories needed."""
        # Test with easy task (scaling factor 1)
        scaled_easy = self.scaler.scale_trajectories(self.trajectories, self.tasks["easy"])
        self.assertEqual(len(scaled_easy), 1)  # Only use 1 trajectory
        
        # Should select the trajectory with highest reward
        self.assertEqual(scaled_easy[0]["total_reward"], 0.9)
        
        # Test with medium task (scaling factor 2)
        scaled_medium = self.scaler.scale_trajectories(self.trajectories, self.tasks["medium"])
        self.assertEqual(len(scaled_medium), 2)  # Use 2 trajectories
        
        # Should select the trajectories with highest diversity and performance score
        self.assertTrue(
            scaled_medium[0]["total_reward"] == 0.9 or 
            scaled_medium[1]["total_reward"] == 0.9
        )

    @patch('deep_research_core.rl_tuning.trajectories.trajectory_scaler.TrajectoryScaler._generate_trajectories')
    def test_scale_trajectories_with_additional(self, mock_generate):
        """Test scaling trajectories with additional trajectories needed."""
        # Setup mock to return additional trajectories
        additional_trajectory = {
            "steps": [
                {"observation": "New task", "action": "search", "reward": 0.3},
                {"observation": "Found key", "action": "use key", "reward": 0.4}
            ],
            "total_reward": 0.7,
            "metadata": {"length": 2, "success": True}
        }
        mock_generate.return_value = [additional_trajectory]
        
        # Test with hard task (scaling factor 3) but only 2 trajectories provided
        limited_trajectories = self.trajectories[:2]  # Only 2 trajectories
        scaled_hard = self.scaler.scale_trajectories(limited_trajectories, self.tasks["hard"])
        
        # Should have 3 trajectories (2 original + 1 generated)
        self.assertEqual(len(scaled_hard), 3)
        mock_generate.assert_called_once_with(1, self.tasks["hard"])

    def test_diversity_calculation(self):
        """Test calculation of trajectory diversity."""
        # Testing function that internally uses _calculate_diversity
        # For a reference trajectory, calculate diversity against all trajectories
        reference_traj = self.trajectories[0]
        all_trajs = self.trajectories
        
        # Direct call to internal method
        diversity = self.scaler._calculate_diversity(reference_traj, all_trajs)
        
        # Diversity should be a float value between 0 and 1
        self.assertIsInstance(diversity, float)
        self.assertGreaterEqual(diversity, 0.0)
        self.assertLessEqual(diversity, 1.0)
        
        # Diversity of identical trajectories should be 0
        identical_traj = self.trajectories[0]
        identical_trajs = [identical_traj, identical_traj]
        zero_diversity = self.scaler._calculate_diversity(identical_traj, identical_trajs)
        self.assertAlmostEqual(zero_diversity, 0.0, places=1)
        
        # Diversity of very different trajectories should be higher
        different_traj = self.trajectories[0]
        different_trajs = [different_traj, self.trajectories[3]]
        high_diversity = self.scaler._calculate_diversity(different_traj, different_trajs)
        self.assertGreater(high_diversity, 0.3)  # Reasonably different trajectories

    def test_select_best_trajectories(self):
        """Test selecting the best trajectories based on diversity and performance."""
        # Select 2 best trajectories from 4
        task = {"id": "test_task"}
        best_trajectories = self.scaler._select_best_trajectories(self.trajectories, task, 2)
        
        # Should have 2 trajectories
        self.assertEqual(len(best_trajectories), 2)
        
        # Should include the highest reward trajectory
        highest_reward = max(self.trajectories, key=lambda t: t.get("total_reward", 0))
        self.assertIn(highest_reward, best_trajectories)
        
        # Test with different weights
        diversity_focused_scaler = TrajectoryScaler(
            base_num_trajectories=2,
            max_trajectories=5,
            diversity_weight=0.9,
            performance_weight=0.1
        )
        
        performance_focused_scaler = TrajectoryScaler(
            base_num_trajectories=2,
            max_trajectories=5,
            diversity_weight=0.1,
            performance_weight=0.9
        )
        
        diversity_selection = diversity_focused_scaler._select_best_trajectories(self.trajectories, task, 2)
        performance_selection = performance_focused_scaler._select_best_trajectories(self.trajectories, task, 2)
        
        # These selections should be different due to different weighting strategies
        self.assertNotEqual(
            [t["total_reward"] for t in diversity_selection],
            [t["total_reward"] for t in performance_selection]
        )

    def test_get_performance_metrics(self):
        """Test retrieving performance metrics of the scaler."""
        # Initially, metrics should be zero
        initial_metrics = self.scaler.get_performance_metrics()
        self.assertEqual(initial_metrics["total_scaling_operations"], 0)
        
        # Scale some trajectories to update metrics
        with patch('deep_research_core.rl_tuning.trajectories.trajectory_scaler.TrajectoryScaler._generate_trajectories') as mock_generate:
            mock_generate.return_value = [self.trajectories[0]]
            self.scaler.scale_trajectories(self.trajectories[:1], self.tasks["medium"])
            
            # Check updated metrics
            updated_metrics = self.scaler.get_performance_metrics()
            self.assertEqual(updated_metrics["total_scaling_operations"], 1)

    def test_trajectory_features(self):
        """Test trajectory features and similarity comparison."""
        # Instead of testing a specific encoding method, test the diversity logic
        # which would use trajectory features internally
        
        # Get most diverse trajectories
        similar_trajectories = [self.trajectories[0], self.trajectories[0]]
        diverse_trajectories = [self.trajectories[0], self.trajectories[3]]
        
        # Similar trajectories should have lower diversity
        similarity_score = self.scaler._calculate_diversity(similar_trajectories[0], similar_trajectories)
        diversity_score = self.scaler._calculate_diversity(diverse_trajectories[0], diverse_trajectories)
        
        # Diversity score should be higher for more diverse trajectories
        self.assertGreater(diversity_score, similarity_score)

    def test_performance_scoring(self):
        """Test performance scoring of trajectories."""
        # Test by reviewing the selected trajectories in scaling
        scaled_trajectories = self.scaler.scale_trajectories(self.trajectories, self.tasks["easy"])
        
        # Should select the trajectory with highest reward when scaling to 1
        self.assertEqual(len(scaled_trajectories), 1)
        self.assertEqual(scaled_trajectories[0]["total_reward"], 0.9)
        
        # Check that performance weight affects selection
        # Create a scaler that only considers performance
        performance_only_scaler = TrajectoryScaler(
            base_num_trajectories=2,
            max_trajectories=5,
            diversity_weight=0.0,
            performance_weight=1.0
        )
        
        # Scale to 2 trajectories using performance only
        performance_scaled = performance_only_scaler.scale_trajectories(self.trajectories, self.tasks["medium"])
        
        # Should select the two highest reward trajectories
        self.assertEqual(len(performance_scaled), 2)
        rewards = [t["total_reward"] for t in performance_scaled]
        self.assertIn(0.9, rewards)  # Highest reward
        self.assertIn(0.8, rewards)  # Second highest reward


if __name__ == "__main__":
    unittest.main() 