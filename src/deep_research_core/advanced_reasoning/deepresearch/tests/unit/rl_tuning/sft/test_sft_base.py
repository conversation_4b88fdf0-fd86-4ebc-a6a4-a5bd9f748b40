"""
Unit tests for the SFT base module.
"""

import os
import json
import tempfile
import unittest
from unittest import mock
from abc import abstractmethod

import torch
from torch.utils.data import DataLoader

from deep_research_core.rl_tuning.sft.base import BaseSFT, SFTDataset


class TestSFTDataset(unittest.TestCase):
    """Tests for the SFTDataset class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.sample_data = [
            {"input": "What is the capital of France?", "output": "Paris"},
            {"input": "Who wrote <PERSON> and <PERSON>?", "output": "William Shakespeare"},
            {"input": "What is 2+2?", "output": "4"}
        ]
    
    def test_init(self):
        """Test initialization of SFTDataset."""
        dataset = SFTDataset(data=self.sample_data)
        self.assertEqual(len(dataset), 3)
        self.assertEqual(dataset.input_key, "input")
        self.assertEqual(dataset.output_key, "output")
    
    def test_getitem(self):
        """Test retrieving an item from the dataset."""
        dataset = SFTDataset(data=self.sample_data)
        item = dataset[0]
        self.assertEqual(item["raw_input"], "What is the capital of France?")
        self.assertEqual(item["raw_output"], "Paris")
    
    def test_format_func(self):
        """Test applying a format function to the dataset."""
        def custom_format(input_text, output_text):
            return f"Q: {input_text}", f"A: {output_text}"
        
        dataset = SFTDataset(data=self.sample_data, format_func=custom_format)
        item = dataset[0]
        self.assertEqual(item["raw_input"], "Q: What is the capital of France?")
        self.assertEqual(item["raw_output"], "A: Paris")
    
    def test_from_file_json(self):
        """Test loading dataset from a JSON file."""
        with tempfile.NamedTemporaryFile(suffix=".json", mode="w+", delete=False) as f:
            json.dump(self.sample_data, f)
            temp_file = f.name
        
        try:
            dataset = SFTDataset.from_file(file_path=temp_file)
            self.assertEqual(len(dataset), 3)
            self.assertEqual(dataset.data[0]["input"], "What is the capital of France?")
        finally:
            os.unlink(temp_file)
    
    def test_from_file_jsonl(self):
        """Test loading dataset from a JSONL file."""
        with tempfile.NamedTemporaryFile(suffix=".jsonl", mode="w+", delete=False) as f:
            for item in self.sample_data:
                f.write(json.dumps(item) + "\n")
            temp_file = f.name
        
        try:
            dataset = SFTDataset.from_file(file_path=temp_file)
            self.assertEqual(len(dataset), 3)
            self.assertEqual(dataset.data[1]["input"], "Who wrote Romeo and Juliet?")
        finally:
            os.unlink(temp_file)


class TestBaseSFT(unittest.TestCase):
    """Tests for the BaseSFT abstract class."""
    
    def test_init(self):
        """Test initialization of BaseSFT."""
        # Create a concrete subclass for testing
        class ConcreteSFT(BaseSFT):
            def prepare_data(self, train_data, eval_data=None, input_key="input", output_key="output", **kwargs):
                pass
            
            def train_model(self, train_dataset, eval_dataset=None, num_epochs=3, batch_size=4, learning_rate=5e-5, **kwargs):
                return {"loss": 0.1}
            
            def save_model(self, save_path=None):
                return self.output_dir
            
            def load_model(self, load_path):
                pass
        
        with tempfile.TemporaryDirectory() as temp_dir:
            sft = ConcreteSFT(model_name="test_model", output_dir=temp_dir)
            self.assertEqual(sft.model_name, "test_model")
            self.assertEqual(sft.output_dir, temp_dir)
            self.assertTrue(os.path.exists(temp_dir))
    
    def test_abstract_methods(self):
        """Test that BaseSFT has the expected abstract methods."""
        # Check that the class has the expected abstract methods
        abstract_methods = []
        for attr_name in dir(BaseSFT):
            attr = getattr(BaseSFT, attr_name)
            if hasattr(attr, "__isabstractmethod__") and attr.__isabstractmethod__:
                abstract_methods.append(attr_name)
        
        # Verify the expected abstract methods are present
        self.assertIn("prepare_data", abstract_methods)
        self.assertIn("train_model", abstract_methods)
        self.assertIn("save_model", abstract_methods)
        self.assertIn("load_model", abstract_methods)
    
    def test_generate_text(self):
        """Test the default generate_text implementation."""
        # Create a concrete subclass for testing
        class ConcreteSFT(BaseSFT):
            def prepare_data(self, train_data, eval_data=None, input_key="input", output_key="output", **kwargs):
                pass
            
            def train_model(self, train_dataset, eval_dataset=None, num_epochs=3, batch_size=4, learning_rate=5e-5, **kwargs):
                return {"loss": 0.1}
            
            def save_model(self, save_path=None):
                return self.output_dir
            
            def load_model(self, load_path):
                pass
        
        with tempfile.TemporaryDirectory() as temp_dir:
            sft = ConcreteSFT(model_name="test_model", output_dir=temp_dir)
            text = sft.generate_text("Hello, world!")
            self.assertEqual(text, "Not implemented in base class")
    
    def test_evaluate_model(self):
        """Test the default evaluate_model implementation."""
        # Create a concrete subclass for testing
        class ConcreteSFT(BaseSFT):
            def prepare_data(self, train_data, eval_data=None, input_key="input", output_key="output", **kwargs):
                pass
            
            def train_model(self, train_dataset, eval_dataset=None, num_epochs=3, batch_size=4, learning_rate=5e-5, **kwargs):
                return {"loss": 0.1}
            
            def save_model(self, save_path=None):
                return self.output_dir
            
            def load_model(self, load_path):
                pass
        
        with tempfile.TemporaryDirectory() as temp_dir:
            sft = ConcreteSFT(model_name="test_model", output_dir=temp_dir)
            mock_dataset = mock.MagicMock(spec=SFTDataset)
            mock_dataset.__len__.return_value = 10
            
            metrics = sft.evaluate_model(mock_dataset)
            self.assertEqual(metrics, {"error": "Not implemented in base class"})


if __name__ == "__main__":
    unittest.main()
