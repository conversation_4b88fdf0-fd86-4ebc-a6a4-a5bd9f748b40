"""
Unit tests for SFT evaluation utilities.

This module contains tests for the SFTEvaluator class and related functions.
"""

import unittest
from unittest.mock import patch, MagicMock
import os
import json
import tempfile
import numpy as np

from deep_research_core.rl_tuning.sft.evaluation import SFTEvaluator, evaluate_model


class TestSFTEvaluator(unittest.TestCase):
    """Tests for the SFTEvaluator class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create a mock model generator
        self.model_generator = MagicMock(return_value="Generated response")
        
        # Create a temporary directory for outputs
        self.temp_dir = tempfile.TemporaryDirectory()
        
        # Initialize the SFTEvaluator
        self.evaluator = SFTEvaluator(
            model_generator=self.model_generator,
            output_dir=self.temp_dir.name,
            verbose=True
        )
        
        # Create sample prompts and references
        self.prompts = ["Prompt 1", "Prompt 2", "Prompt 3"]
        self.references = ["Reference 1", "Reference 2", "Reference 3"]
        self.labels = ["Label 1", "Label 2", "Label 3"]
    
    def tearDown(self):
        """Tear down test fixtures."""
        # Clean up the temporary directory
        self.temp_dir.cleanup()
    
    def test_initialization(self):
        """Test initialization of SFTEvaluator."""
        # Check that the model generator was set correctly
        self.assertEqual(self.evaluator.model_generator, self.model_generator)
        
        # Check that the output directory was set correctly
        self.assertEqual(self.evaluator.output_dir, self.temp_dir.name)
        
        # Check that the verbose flag was set correctly
        self.assertTrue(self.evaluator.verbose)
        
        # Check that the evaluation results were initialized
        self.assertEqual(self.evaluator.evaluation_results, {})
    
    def test_evaluate_exact_match(self):
        """Test evaluate_exact_match method."""
        # Configure mock model generator
        self.model_generator.side_effect = ["Reference 1", "Different", "Reference 3"]
        
        # Evaluate exact match
        results = self.evaluator.evaluate_exact_match(
            prompts=self.prompts,
            references=self.references,
            save_results=True
        )
        
        # Check that the model generator was called for each prompt
        self.assertEqual(self.model_generator.call_count, len(self.prompts))
        
        # Check that results were returned
        self.assertIn("exact_match_accuracy", results)
        self.assertIn("num_examples", results)
        self.assertIn("num_exact_matches", results)
        
        # Check that the accuracy is correct (2/3 matches)
        self.assertAlmostEqual(results["exact_match_accuracy"], 2/3, places=5)
        
        # Check that the number of examples is correct
        self.assertEqual(results["num_examples"], 3)
        
        # Check that the number of exact matches is correct
        self.assertEqual(results["num_exact_matches"], 2)
        
        # Check that results were saved
        result_files = [f for f in os.listdir(self.temp_dir.name) if f.startswith("exact_match_results_")]
        self.assertEqual(len(result_files), 1)
    
    def test_evaluate_similarity(self):
        """Test evaluate_similarity method."""
        # Configure mock model generator
        self.model_generator.side_effect = ["Reference 1", "Different", "Reference 3"]
        
        # Evaluate similarity
        results = self.evaluator.evaluate_similarity(
            prompts=self.prompts,
            references=self.references,
            save_results=True
        )
        
        # Check that the model generator was called for each prompt
        self.assertEqual(self.model_generator.call_count, len(self.prompts))
        
        # Check that results were returned
        self.assertIn("average_similarity", results)
        self.assertIn("num_examples", results)
        self.assertIn("min_similarity", results)
        self.assertIn("max_similarity", results)
        
        # Check that the number of examples is correct
        self.assertEqual(results["num_examples"], 3)
        
        # Check that similarity values are between 0 and 1
        self.assertGreaterEqual(results["average_similarity"], 0.0)
        self.assertLessEqual(results["average_similarity"], 1.0)
        self.assertGreaterEqual(results["min_similarity"], 0.0)
        self.assertLessEqual(results["max_similarity"], 1.0)
        self.assertGreaterEqual(results["max_similarity"], results["min_similarity"])
        
        # Check that results were saved
        result_files = [f for f in os.listdir(self.temp_dir.name) if f.startswith("similarity_results_")]
        self.assertEqual(len(result_files), 1)
    
    def test_evaluate_classification(self):
        """Test evaluate_classification method."""
        # Configure mock model generator to include the label in the response
        self.model_generator.side_effect = [
            f"The answer is {self.labels[0]}",
            f"I think it's {self.labels[1]}",
            f"The label is {self.labels[2]}"
        ]
        
        # Evaluate classification
        results = self.evaluator.evaluate_classification(
            prompts=self.prompts,
            references=self.labels,
            labels=self.labels,
            save_results=True
        )
        
        # Check that the model generator was called for each prompt
        self.assertEqual(self.model_generator.call_count, len(self.prompts))
        
        # Check that results were returned
        self.assertIn("accuracy", results)
        self.assertIn("precision", results)
        self.assertIn("recall", results)
        self.assertIn("f1_score", results)
        self.assertIn("num_examples", results)
        
        # Check that the number of examples is correct
        self.assertEqual(results["num_examples"], 3)
        
        # Check that metric values are between 0 and 1
        self.assertGreaterEqual(results["accuracy"], 0.0)
        self.assertLessEqual(results["accuracy"], 1.0)
        self.assertGreaterEqual(results["precision"], 0.0)
        self.assertLessEqual(results["precision"], 1.0)
        self.assertGreaterEqual(results["recall"], 0.0)
        self.assertLessEqual(results["recall"], 1.0)
        self.assertGreaterEqual(results["f1_score"], 0.0)
        self.assertLessEqual(results["f1_score"], 1.0)
        
        # Check that results were saved
        result_files = [f for f in os.listdir(self.temp_dir.name) if f.startswith("classification_results_")]
        self.assertEqual(len(result_files), 1)
    
    def test_evaluate_generation_quality(self):
        """Test evaluate_generation_quality method."""
        # Configure mock model generator
        self.model_generator.side_effect = [
            "This is a high quality response with several words.",
            "Short answer.",
            "This response has some repeated words words words."
        ]
        
        # Evaluate generation quality
        results = self.evaluator.evaluate_generation_quality(
            prompts=self.prompts,
            references=self.references,
            save_results=True
        )
        
        # Check that the model generator was called for each prompt
        self.assertEqual(self.model_generator.call_count, len(self.prompts))
        
        # Check that results were returned
        self.assertIn("average_length", results)
        self.assertIn("average_diversity", results)
        self.assertIn("num_examples", results)
        self.assertIn("min_length", results)
        self.assertIn("max_length", results)
        
        # Check that the number of examples is correct
        self.assertEqual(results["num_examples"], 3)
        
        # Check that length values are non-negative
        self.assertGreaterEqual(results["average_length"], 0.0)
        self.assertGreaterEqual(results["min_length"], 0)
        self.assertGreaterEqual(results["max_length"], results["min_length"])
        
        # Check that diversity values are between 0 and 1
        self.assertGreaterEqual(results["average_diversity"], 0.0)
        self.assertLessEqual(results["average_diversity"], 1.0)
        
        # Check that results were saved
        result_files = [f for f in os.listdir(self.temp_dir.name) if f.startswith("generation_quality_results_")]
        self.assertEqual(len(result_files), 1)
    
    def test_evaluate_all(self):
        """Test evaluate_all method."""
        # Configure mock model generator
        self.model_generator.return_value = "Generated response"
        
        # Evaluate all
        results = self.evaluator.evaluate_all(
            prompts=self.prompts,
            references=self.references,
            labels=self.labels,
            save_results=True
        )
        
        # Check that results were returned for all evaluation types
        self.assertIn("exact_match", results)
        self.assertIn("similarity", results)
        self.assertIn("generation_quality", results)
        self.assertIn("classification", results)
        
        # Check that a combined results file was created
        result_files = [f for f in os.listdir(self.temp_dir.name) if f.startswith("all_results_")]
        self.assertEqual(len(result_files), 1)
    
    @patch('deep_research_core.rl_tuning.sft.evaluation.plt')
    def test_visualize_results(self, mock_plt):
        """Test visualize_results method."""
        # Set up evaluation results
        self.evaluator.evaluation_results = {
            "exact_match": {"exact_match_accuracy": 0.7},
            "similarity": {"average_similarity": 0.8},
            "classification": {
                "accuracy": 0.9,
                "precision": 0.85,
                "recall": 0.8,
                "f1_score": 0.825
            },
            "generation_quality": {
                "average_length": 50,
                "average_diversity": 0.6
            }
        }
        
        # Visualize results
        save_path = os.path.join(self.temp_dir.name, "visualization.png")
        self.evaluator.visualize_results(save_path=save_path)
        
        # Check that matplotlib functions were called
        mock_plt.figure.assert_called_once()
        self.assertEqual(mock_plt.subplot.call_count, 4)
        mock_plt.tight_layout.assert_called_once()
        mock_plt.savefig.assert_called_once_with(save_path)
        mock_plt.close.assert_called_once()


class TestEvaluateModel(unittest.TestCase):
    """Tests for the evaluate_model function."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create a mock model generator
        self.model_generator = MagicMock(return_value="Generated response")
        
        # Create a temporary directory for outputs
        self.temp_dir = tempfile.TemporaryDirectory()
        
        # Create sample evaluation data
        self.eval_data = [
            {"input": "Prompt 1", "output": "Reference 1"},
            {"input": "Prompt 2", "output": "Reference 2"},
            {"input": "Prompt 3", "output": "Reference 3"}
        ]
        
        # Create sample labels
        self.labels = ["Label 1", "Label 2", "Label 3"]
    
    def tearDown(self):
        """Tear down test fixtures."""
        # Clean up the temporary directory
        self.temp_dir.cleanup()
    
    @patch('deep_research_core.rl_tuning.sft.evaluation.SFTEvaluator')
    def test_evaluate_model(self, mock_evaluator_class):
        """Test evaluate_model function."""
        # Configure mock evaluator
        mock_evaluator = MagicMock()
        mock_evaluator_class.return_value = mock_evaluator
        mock_evaluator.evaluate_all.return_value = {"test": "results"}
        
        # Evaluate model
        results = evaluate_model(
            model_generator=self.model_generator,
            eval_data=self.eval_data,
            input_key="input",
            output_key="output",
            labels=self.labels,
            output_dir=self.temp_dir.name,
            verbose=True
        )
        
        # Check that the evaluator was created correctly
        mock_evaluator_class.assert_called_once_with(
            model_generator=self.model_generator,
            output_dir=self.temp_dir.name,
            verbose=True
        )
        
        # Check that evaluate_all was called correctly
        mock_evaluator.evaluate_all.assert_called_once_with(
            prompts=[example["input"] for example in self.eval_data],
            references=[example["output"] for example in self.eval_data],
            labels=self.labels,
            save_results=True
        )
        
        # Check that visualize_results was called
        mock_evaluator.visualize_results.assert_called_once()
        
        # Check that results were returned
        self.assertEqual(results, {"test": "results"})


if __name__ == "__main__":
    unittest.main()
