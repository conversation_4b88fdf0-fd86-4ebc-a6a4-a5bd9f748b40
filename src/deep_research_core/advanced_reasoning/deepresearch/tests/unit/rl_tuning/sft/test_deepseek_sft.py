"""
Unit tests for the DeepSeekSFT class.
"""

import os
import json
import unittest
from unittest.mock import patch, MagicMock

import pytest
import requests

from deep_research_core.rl_tuning.sft import DeepSeekSFT, SFTDataset


class TestDeepSeekSFT(unittest.TestCase):
    """Test cases for the DeepSeekSFT class."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a mock API key
        self.api_key = "test_api_key"

        # Create test data
        self.test_data = [
            {
                "input": "What is machine learning?",
                "output": "Machine learning is a field of AI that enables systems to learn from data."
            },
            {
                "input": "Explain neural networks",
                "output": "Neural networks are computational models inspired by the human brain."
            }
        ]

        # Initialize the DeepSeekSFT instance with the mock API key
        self.sft = DeepSeekSFT(
            model_name="deepseek-chat",
            output_dir="./test_output",
            api_key=self.api_key
        )

    @patch("deep_research_core.models.api.deepseek.deepseek_provider")
    def test_init(self, mock_provider):
        """Test initialization of DeepSeekSFT."""
        sft = DeepSeekSFT(
            model_name="deepseek-chat",
            output_dir="./test_output",
            api_key=self.api_key
        )

        self.assertEqual(sft.model_name, "deepseek-chat")
        self.assertEqual(sft.output_dir, "./test_output")
        self.assertEqual(sft.api_key, self.api_key)
        self.assertIsNone(sft.fine_tuned_model)

    @patch("deep_research_core.models.api.deepseek.deepseek_provider")
    def test_prepare_data(self, mock_provider):
        """Test data preparation."""
        train_dataset, _ = self.sft.prepare_data(
            train_data=self.test_data,
            input_key="input",
            output_key="output"
        )

        self.assertIsInstance(train_dataset, SFTDataset)
        self.assertEqual(len(train_dataset), 2)
        self.assertEqual(train_dataset.data, self.test_data)

    @patch("deep_research_core.models.api.deepseek.deepseek_provider")
    def test_convert_dataset_format(self, mock_provider):
        """Test conversion of dataset to DeepSeek format."""
        train_dataset, _ = self.sft.prepare_data(
            train_data=self.test_data
        )

        deepseek_format = self.sft._convert_dataset_to_deepseek_format(
            train_dataset,
            system_message="You are a helpful assistant."
        )

        self.assertEqual(len(deepseek_format), 2)
        self.assertEqual(len(deepseek_format[0]["messages"]), 3)
        self.assertEqual(deepseek_format[0]["messages"][0]["role"], "system")
        self.assertEqual(deepseek_format[0]["messages"][1]["role"], "user")
        self.assertEqual(deepseek_format[0]["messages"][2]["role"], "assistant")
        self.assertEqual(deepseek_format[0]["messages"][1]["content"], "What is machine learning?")

    @patch("deep_research_core.models.api.deepseek.deepseek_provider")
    @patch("requests.post")
    def test_train_model_file_upload(self, mock_post, mock_provider):
        """Test file upload during training."""
        # Mock the response for file upload
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"id": "file_123456"}
        mock_post.return_value = mock_response

        # Prepare test data
        train_dataset, _ = self.sft.prepare_data(
            train_data=self.test_data
        )

        # Mock file operations
        with patch("builtins.open", unittest.mock.mock_open(read_data="test data")):
            with patch("os.makedirs"):
                # Start training but interrupt after file upload
                with patch("requests.post") as mock_post:
                    # First response is file upload
                    file_response = MagicMock()
                    file_response.status_code = 200
                    file_response.json.return_value = {"id": "file_123456"}

                    # Second response is fine-tuning job creation
                    job_response = MagicMock()
                    job_response.status_code = 200
                    job_response.json.return_value = {
                        "id": "job_123456",
                        "status": "created"
                    }

                    mock_post.side_effect = [file_response, job_response]

                    result = self.sft.train_model(
                        train_dataset=train_dataset,
                        num_epochs=3,
                        wait_for_completion=False
                    )

                    self.assertEqual(result["job_id"], "job_123456")
                    self.assertEqual(result["status"], "created")

    @patch("deep_research_core.models.api.deepseek.deepseek_provider")
    def test_generate_text(self, mock_provider):
        """Test text generation with the model."""
        # Set up the mock provider's generate method
        mock_provider.generate.return_value = "This is a test response."

        # Generate text
        response = self.sft.generate_text(
            prompt="What is AI?",
            max_length=100,
            temperature=0.7
        )

        # Check that the provider was called correctly
        mock_provider.generate.assert_called_once()
        call_args = mock_provider.generate.call_args[1]
        self.assertEqual(call_args["prompt"], "What is AI?")
        self.assertEqual(call_args["max_tokens"], 100)
        self.assertEqual(call_args["temperature"], 0.7)

        # Check the response
        self.assertEqual(response, "This is a test response.")

    @patch("deep_research_core.models.api.deepseek.deepseek_provider")
    def test_save_and_load_model(self, mock_provider):
        """Test saving and loading model information."""
        # Set the fine-tuned model ID
        self.sft.fine_tuned_model = "ft:deepseek-chat:123456"

        # Mock file operations
        with patch("builtins.open", unittest.mock.mock_open()):
            with patch("os.makedirs"):
                with patch("json.dump") as mock_json_dump:
                    # Save model
                    save_path = self.sft.save_model("test_model_info.json")

                    # Check that json.dump was called with correct arguments
                    mock_json_dump.assert_called_once()
                    args = mock_json_dump.call_args[0]
                    self.assertEqual(args[0]["model_id"], "ft:deepseek-chat:123456")
                    self.assertEqual(args[0]["provider"], "deepseek")

        # Reset the fine-tuned model ID
        self.sft.fine_tuned_model = None

        # Mock file operations for loading
        mock_data = {"model_id": "ft:deepseek-chat:789012"}
        with patch("builtins.open", unittest.mock.mock_open()):
            with patch("os.path.exists", return_value=True):
                with patch("json.load", return_value=mock_data):
                    # Load model
                    self.sft.load_model("test_model_info.json")

                    # Check that the model ID was set correctly
                    self.assertEqual(self.sft.fine_tuned_model, "ft:deepseek-chat:789012")


if __name__ == "__main__":
    unittest.main()