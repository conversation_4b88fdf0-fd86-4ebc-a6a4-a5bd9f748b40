"""
Unit tests for QwQ SFT implementation.

This module contains tests for the QwQSFT class.
"""

import unittest
from unittest.mock import patch, MagicMock, ANY
import os
import json
import tempfile

from deep_research_core.rl_tuning.sft.qwq_sft import QwQSFT
from deep_research_core.rl_tuning.sft.base import SFTDataset


class TestQwQSFT(unittest.TestCase):
    """Tests for the QwQSFT class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create a mock QwQ provider
        self.mock_provider_patcher = patch('deep_research_core.rl_tuning.sft.qwq_sft.QwQProvider')
        self.mock_provider_class = self.mock_provider_patcher.start()
        self.mock_provider = MagicMock()
        self.mock_provider_class.return_value = self.mock_provider
        
        # Mock the generate method
        self.mock_provider.generate.return_value = {
            "choices": [{"text": "Generated response"}]
        }
        
        # Create a temporary directory for outputs
        self.temp_dir = tempfile.TemporaryDirectory()
        
        # Create sample training data
        self.train_data = [
            {"input": "Question 1", "output": "Answer 1"},
            {"input": "Question 2", "output": "Answer 2"}
        ]
        
        # Create sample evaluation data
        self.eval_data = [
            {"input": "Test Question 1", "output": "Test Answer 1"},
            {"input": "Test Question 2", "output": "Test Answer 2"}
        ]
        
        # Initialize the QwQSFT instance
        self.sft = QwQSFT(
            model_name="test-model",
            api_key="test-key",
            output_dir=self.temp_dir.name
        )
    
    def tearDown(self):
        """Tear down test fixtures."""
        # Stop the provider patcher
        self.mock_provider_patcher.stop()
        
        # Clean up the temporary directory
        self.temp_dir.cleanup()
    
    def test_initialization(self):
        """Test initialization of QwQSFT."""
        # Check that the provider was initialized correctly
        self.mock_provider_class.assert_called_once_with(
            api_key="test-key",
            api_base=None,
            model="test-model"
        )
        
        # Check that the model name was set correctly
        self.assertEqual(self.sft.model_name, "test-model")
        
        # Check that the output directory was set correctly
        self.assertEqual(self.sft.output_dir, self.temp_dir.name)
        
        # Check that the device was set correctly
        self.assertEqual(self.sft.device, "qwq")
    
    def test_prepare_data(self):
        """Test prepare_data method."""
        # Prepare data
        train_dataset, eval_dataset = self.sft.prepare_data(
            train_data=self.train_data,
            eval_data=self.eval_data
        )
        
        # Check that datasets were created correctly
        self.assertIsInstance(train_dataset, SFTDataset)
        self.assertIsInstance(eval_dataset, SFTDataset)
        
        # Check that datasets have the correct number of examples
        self.assertEqual(len(train_dataset), 2)
        self.assertEqual(len(eval_dataset), 2)
        
        # Check that examples have the correct structure
        example = train_dataset[0]
        self.assertEqual(example["raw_input"], "Question 1")
        self.assertEqual(example["raw_output"], "Answer 1")
    
    def test_prepare_data_from_file(self):
        """Test prepare_data method with file input."""
        # Create a temporary data file
        data_file = os.path.join(self.temp_dir.name, "train_data.json")
        with open(data_file, "w", encoding="utf-8") as f:
            json.dump(self.train_data, f)
        
        # Prepare data
        train_dataset, eval_dataset = self.sft.prepare_data(
            train_data=data_file,
            eval_data=None
        )
        
        # Check that dataset was created correctly
        self.assertIsInstance(train_dataset, SFTDataset)
        
        # Check that dataset has the correct number of examples
        self.assertEqual(len(train_dataset), 2)
    
    def test_train_model(self):
        """Test train_model method."""
        # Prepare data
        train_dataset, eval_dataset = self.sft.prepare_data(
            train_data=self.train_data,
            eval_data=self.eval_data
        )
        
        # Train model
        metrics = self.sft.train_model(
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            num_epochs=2,
            batch_size=1,
            learning_rate=1e-5
        )
        
        # Check that metrics were returned
        self.assertIn("fine_tuning_job_id", metrics)
        self.assertIn("fine_tuned_model", metrics)
        self.assertIn("final_train_loss", metrics)
        self.assertIn("final_eval_loss", metrics)
        self.assertIn("num_epochs", metrics)
        self.assertIn("num_examples", metrics)
        
        # Check that fine-tuning job ID was set
        self.assertIsNotNone(self.sft.fine_tuning_job_id)
        
        # Check that model name was updated
        self.assertTrue(self.sft.model_name.startswith("test-model:ft-"))
        
        # Check that training metrics were updated
        self.assertIn("epoch_1", self.sft.training_metrics)
        self.assertIn("epoch_2", self.sft.training_metrics)
        
        # Check that a data file was created
        self.assertTrue(os.path.exists(os.path.join(self.temp_dir.name, "fine_tuning_data.jsonl")))
        
        # Check that a model info file was created
        model_info_files = [f for f in os.listdir(self.temp_dir.name) if f.startswith("model_info_")]
        self.assertEqual(len(model_info_files), 1)
    
    def test_save_and_load_model(self):
        """Test save_model and load_model methods."""
        # Set up model state
        self.sft.fine_tuning_job_id = "ft-123456"
        self.sft.model_name = "test-model:ft-123456"
        self.sft.training_metrics = {
            "epoch_1": {"train_loss": 0.5, "eval_loss": 0.6}
        }
        
        # Save model
        save_path = self.sft.save_model()
        
        # Check that the save file exists
        self.assertTrue(os.path.exists(save_path))
        
        # Create a new SFT instance
        new_sft = QwQSFT(
            model_name="new-model",
            output_dir=self.temp_dir.name
        )
        
        # Load model
        new_sft.load_model(save_path)
        
        # Check that model state was loaded correctly
        self.assertEqual(new_sft.model_name, "test-model:ft-123456")
        self.assertEqual(new_sft.fine_tuning_job_id, "ft-123456")
        self.assertEqual(new_sft.training_metrics, {"epoch_1": {"train_loss": 0.5, "eval_loss": 0.6}})
        
        # Check that provider model was updated
        self.assertEqual(new_sft.provider.model, "test-model:ft-123456")
    
    def test_evaluate_model(self):
        """Test evaluate_model method."""
        # Prepare data
        _, eval_dataset = self.sft.prepare_data(
            train_data=self.train_data,
            eval_data=self.eval_data
        )
        
        # Evaluate model
        metrics = self.sft.evaluate_model(eval_dataset)
        
        # Check that metrics were returned
        self.assertIn("loss", metrics)
        self.assertIn("accuracy", metrics)
        self.assertIn("exact_match", metrics)
        
        # Check that the provider's generate method was called
        self.assertEqual(self.mock_provider.generate.call_count, 2)
    
    def test_generate_text(self):
        """Test generate_text method."""
        # Generate text
        text = self.sft.generate_text("Test prompt")
        
        # Check that the provider's generate method was called correctly
        self.mock_provider.generate.assert_called_with(
            prompt="Test prompt",
            max_tokens=100,  # Default max_length
            temperature=0.7  # Default temperature
        )
        
        # Check that the correct text was returned
        self.assertEqual(text, "Generated response")


if __name__ == "__main__":
    unittest.main()
