"""
Unit tests for SFT data augmentation utilities.

This module contains tests for the SFTDataAugmenter class and related functions.
"""

import unittest
import random
import numpy as np

from deep_research_core.rl_tuning.sft.data_augmentation import SFTDataAugmenter, augment_sft_data


class TestSFTDataAugmenter(unittest.TestCase):
    """Tests for the SFTDataAugmenter class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Set random seed for reproducibility
        random.seed(42)
        np.random.seed(42)
        
        # Initialize the SFTDataAugmenter
        self.augmenter = SFTDataAugmenter(
            input_key="input",
            output_key="output",
            random_seed=42,
            verbose=True
        )
        
        # Create sample training data
        self.train_data = [
            {"input": "This is a sample input text for testing.", "output": "Sample output 1."},
            {"input": "Another example of input text with more words.", "output": "Sample output 2."},
            {"input": "A third input example with different words.", "output": "Sample output 3."}
        ]
    
    def test_initialization(self):
        """Test initialization of SFTDataAugmenter."""
        # Check that the input key was set correctly
        self.assertEqual(self.augmenter.input_key, "input")
        
        # Check that the output key was set correctly
        self.assertEqual(self.augmenter.output_key, "output")
        
        # Check that the verbose flag was set correctly
        self.assertTrue(self.augmenter.verbose)
    
    def test_augment_data_with_invalid_method(self):
        """Test augment_data method with invalid augmentation method."""
        # Augment data with invalid method
        augmented_data = self.augmenter.augment_data(
            data=self.train_data,
            augmentation_methods=["invalid_method"],
            augmentation_factor=2
        )
        
        # Check that the original data was returned unchanged
        self.assertEqual(augmented_data, self.train_data)
    
    def test_augment_data_with_synonym_replacement(self):
        """Test augment_data method with synonym replacement."""
        # Augment data with synonym replacement
        augmented_data = self.augmenter.augment_data(
            data=self.train_data,
            augmentation_methods=["synonym_replacement"],
            augmentation_factor=2
        )
        
        # Check that the augmented data has the correct size
        self.assertEqual(len(augmented_data), len(self.train_data) * 2)
        
        # Check that the original examples are included
        for example in self.train_data:
            self.assertIn(example, augmented_data)
        
        # Check that the augmented examples have the correct structure
        for example in augmented_data[len(self.train_data):]:
            self.assertIn(self.augmenter.input_key, example)
            self.assertIn(self.augmenter.output_key, example)
            self.assertIn("augmentation_method", example)
            self.assertEqual(example["augmentation_method"], "synonym_replacement")
    
    def test_augment_data_with_random_deletion(self):
        """Test augment_data method with random deletion."""
        # Augment data with random deletion
        augmented_data = self.augmenter.augment_data(
            data=self.train_data,
            augmentation_methods=["random_deletion"],
            augmentation_factor=2
        )
        
        # Check that the augmented data has the correct size
        self.assertEqual(len(augmented_data), len(self.train_data) * 2)
        
        # Check that the original examples are included
        for example in self.train_data:
            self.assertIn(example, augmented_data)
        
        # Check that the augmented examples have the correct structure
        for example in augmented_data[len(self.train_data):]:
            self.assertIn(self.augmenter.input_key, example)
            self.assertIn(self.augmenter.output_key, example)
            self.assertIn("augmentation_method", example)
            self.assertEqual(example["augmentation_method"], "random_deletion")
            
            # Check that the augmented input is shorter than the original
            original_input = self.train_data[augmented_data.index(example) - len(self.train_data)]["input"]
            self.assertLessEqual(len(example["input"].split()), len(original_input.split()))
    
    def test_augment_data_with_random_swap(self):
        """Test augment_data method with random swap."""
        # Augment data with random swap
        augmented_data = self.augmenter.augment_data(
            data=self.train_data,
            augmentation_methods=["random_swap"],
            augmentation_factor=2
        )
        
        # Check that the augmented data has the correct size
        self.assertEqual(len(augmented_data), len(self.train_data) * 2)
        
        # Check that the original examples are included
        for example in self.train_data:
            self.assertIn(example, augmented_data)
        
        # Check that the augmented examples have the correct structure
        for example in augmented_data[len(self.train_data):]:
            self.assertIn(self.augmenter.input_key, example)
            self.assertIn(self.augmenter.output_key, example)
            self.assertIn("augmentation_method", example)
            self.assertEqual(example["augmentation_method"], "random_swap")
            
            # Check that the augmented input has the same number of words as the original
            original_input = self.train_data[augmented_data.index(example) - len(self.train_data)]["input"]
            self.assertEqual(len(example["input"].split()), len(original_input.split()))
    
    def test_augment_data_with_random_insertion(self):
        """Test augment_data method with random insertion."""
        # Augment data with random insertion
        augmented_data = self.augmenter.augment_data(
            data=self.train_data,
            augmentation_methods=["random_insertion"],
            augmentation_factor=2
        )
        
        # Check that the augmented data has the correct size
        self.assertEqual(len(augmented_data), len(self.train_data) * 2)
        
        # Check that the original examples are included
        for example in self.train_data:
            self.assertIn(example, augmented_data)
        
        # Check that the augmented examples have the correct structure
        for example in augmented_data[len(self.train_data):]:
            self.assertIn(self.augmenter.input_key, example)
            self.assertIn(self.augmenter.output_key, example)
            self.assertIn("augmentation_method", example)
            self.assertEqual(example["augmentation_method"], "random_insertion")
            
            # Check that the augmented input is longer than the original
            original_input = self.train_data[augmented_data.index(example) - len(self.train_data)]["input"]
            self.assertGreaterEqual(len(example["input"].split()), len(original_input.split()))
    
    def test_augment_data_with_back_translation(self):
        """Test augment_data method with back translation."""
        # Augment data with back translation
        augmented_data = self.augmenter.augment_data(
            data=self.train_data,
            augmentation_methods=["back_translation"],
            augmentation_factor=2
        )
        
        # Check that the augmented data has the correct size
        self.assertEqual(len(augmented_data), len(self.train_data) * 2)
        
        # Check that the original examples are included
        for example in self.train_data:
            self.assertIn(example, augmented_data)
        
        # Check that the augmented examples have the correct structure
        for example in augmented_data[len(self.train_data):]:
            self.assertIn(self.augmenter.input_key, example)
            self.assertIn(self.augmenter.output_key, example)
            self.assertIn("augmentation_method", example)
            self.assertEqual(example["augmentation_method"], "back_translation")
            
            # Check that the augmented input contains the back-translation marker
            self.assertIn("[back-translated]", example["input"])
    
    def test_augment_data_with_paraphrasing(self):
        """Test augment_data method with paraphrasing."""
        # Augment data with paraphrasing
        augmented_data = self.augmenter.augment_data(
            data=self.train_data,
            augmentation_methods=["paraphrasing"],
            augmentation_factor=2
        )
        
        # Check that the augmented data has the correct size
        self.assertEqual(len(augmented_data), len(self.train_data) * 2)
        
        # Check that the original examples are included
        for example in self.train_data:
            self.assertIn(example, augmented_data)
        
        # Check that the augmented examples have the correct structure
        for example in augmented_data[len(self.train_data):]:
            self.assertIn(self.augmenter.input_key, example)
            self.assertIn(self.augmenter.output_key, example)
            self.assertIn("augmentation_method", example)
            self.assertEqual(example["augmentation_method"], "paraphrasing")
            
            # Check that the augmented input contains the paraphrasing marker
            self.assertIn("[paraphrased]", example["input"])
    
    def test_augment_data_with_eda(self):
        """Test augment_data method with EDA."""
        # Augment data with EDA
        augmented_data = self.augmenter.augment_data(
            data=self.train_data,
            augmentation_methods=["eda"],
            augmentation_factor=2
        )
        
        # Check that the augmented data has the correct size
        self.assertEqual(len(augmented_data), len(self.train_data) * 2)
        
        # Check that the original examples are included
        for example in self.train_data:
            self.assertIn(example, augmented_data)
        
        # Check that the augmented examples have the correct structure
        for example in augmented_data[len(self.train_data):]:
            self.assertIn(self.augmenter.input_key, example)
            self.assertIn(self.augmenter.output_key, example)
            self.assertIn("augmentation_method", example)
            self.assertEqual(example["augmentation_method"], "eda")
    
    def test_augment_data_with_multiple_methods(self):
        """Test augment_data method with multiple augmentation methods."""
        # Augment data with multiple methods
        augmented_data = self.augmenter.augment_data(
            data=self.train_data,
            augmentation_methods=["synonym_replacement", "random_deletion", "random_swap", "random_insertion"],
            augmentation_factor=3
        )
        
        # Check that the augmented data has the correct size
        self.assertEqual(len(augmented_data), len(self.train_data) * 3)
        
        # Check that the original examples are included
        for example in self.train_data:
            self.assertIn(example, augmented_data)
        
        # Check that the augmented examples have the correct structure
        for example in augmented_data[len(self.train_data):]:
            self.assertIn(self.augmenter.input_key, example)
            self.assertIn(self.augmenter.output_key, example)
            self.assertIn("augmentation_method", example)
    
    def test_augment_data_with_higher_factor(self):
        """Test augment_data method with higher augmentation factor."""
        # Augment data with higher factor
        augmented_data = self.augmenter.augment_data(
            data=self.train_data,
            augmentation_methods=["synonym_replacement"],
            augmentation_factor=5
        )
        
        # Check that the augmented data has the correct size
        self.assertEqual(len(augmented_data), len(self.train_data) * 5)
        
        # Check that the original examples are included
        for example in self.train_data:
            self.assertIn(example, augmented_data)


class TestAugmentSFTData(unittest.TestCase):
    """Tests for the augment_sft_data function."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Set random seed for reproducibility
        random.seed(42)
        np.random.seed(42)
        
        # Create sample training data
        self.train_data = [
            {"input": "This is a sample input text for testing.", "output": "Sample output 1."},
            {"input": "Another example of input text with more words.", "output": "Sample output 2."},
            {"input": "A third input example with different words.", "output": "Sample output 3."}
        ]
    
    def test_augment_sft_data_with_default_parameters(self):
        """Test augment_sft_data function with default parameters."""
        # Augment data with default parameters
        augmented_data = augment_sft_data(
            data=self.train_data,
            random_seed=42,
            verbose=True
        )
        
        # Check that the augmented data has the correct size
        self.assertEqual(len(augmented_data), len(self.train_data) * 2)
        
        # Check that the original examples are included
        for example in self.train_data:
            self.assertIn(example, augmented_data)
    
    def test_augment_sft_data_with_custom_parameters(self):
        """Test augment_sft_data function with custom parameters."""
        # Augment data with custom parameters
        augmented_data = augment_sft_data(
            data=self.train_data,
            input_key="input",
            output_key="output",
            augmentation_methods=["synonym_replacement", "random_swap"],
            augmentation_factor=3,
            random_seed=42,
            verbose=True
        )
        
        # Check that the augmented data has the correct size
        self.assertEqual(len(augmented_data), len(self.train_data) * 3)
        
        # Check that the original examples are included
        for example in self.train_data:
            self.assertIn(example, augmented_data)


if __name__ == "__main__":
    unittest.main()
