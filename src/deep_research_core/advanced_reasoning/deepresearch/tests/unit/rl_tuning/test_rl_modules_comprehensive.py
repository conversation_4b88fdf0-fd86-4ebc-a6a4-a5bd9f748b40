"""
Comprehensive test suite for RL modules (PPO, SFT, GRPO).

This test suite provides more thorough testing of the RL modules,
including initialization, reward functions, and basic functionality.
"""

import os
import sys
import unittest
import numpy as np
from typing import Dict, Any, List, Optional

# Add the src directory to the path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))

from src.deep_research_core.utils.test_utils import setup_test_environment, restore_environment, get_test_model
from src.deep_research_core.rl_tuning.providers.ppo_providers import get_ppo_provider
from src.deep_research_core.rl_tuning.providers.sft_providers import get_sft_provider
from src.deep_research_core.rl_tuning.providers.grpo_providers import get_grpo_provider
from src.deep_research_core.rl_tuning.grpo.base import FormatRewardFunction
from src.deep_research_core.rl_tuning.grpo.outcome_rewards import AccuracyR<PERSON>ard, ReasoningQualityReward


class TestPPOProviders(unittest.TestCase):
    """Test PPO provider initialization and basic functionality."""
    
    def setUp(self):
        """Set up the test environment."""
        self.original_env = setup_test_environment()
        
        # Define a simple reward function for testing
        def simple_reward_fn(prompt, response):
            return 0.5
            
        self.reward_fn = simple_reward_fn
    
    def tearDown(self):
        """Restore the original environment."""
        restore_environment(self.original_env)
    
    def test_openrouter_ppo_init(self):
        """Test OpenRouter PPO initialization."""
        model_name = get_test_model("openrouter", "cheap")
        ppo = get_ppo_provider(
            model_name=model_name,
            provider_type="openrouter",
            reward_fn=self.reward_fn
        )
        self.assertIsNotNone(ppo)
        self.assertEqual(ppo.model_name, model_name)
    
    def test_ppo_generate(self):
        """Test PPO generate method."""
        model_name = get_test_model("openrouter", "cheap")
        ppo = get_ppo_provider(
            model_name=model_name,
            provider_type="openrouter",
            reward_fn=self.reward_fn
        )
        
        # Test generation
        prompt = "What is the capital of France?"
        response = ppo.generate(prompt)
        
        self.assertIsNotNone(response)
        self.assertIsInstance(response, str)
        self.assertTrue(len(response) > 0)


class TestSFTProviders(unittest.TestCase):
    """Test SFT provider initialization and basic functionality."""
    
    def setUp(self):
        """Set up the test environment."""
        self.original_env = setup_test_environment()
    
    def tearDown(self):
        """Restore the original environment."""
        restore_environment(self.original_env)
    
    def test_openrouter_sft_init(self):
        """Test OpenRouter SFT initialization."""
        model_name = get_test_model("openrouter", "cheap")
        sft = get_sft_provider(
            model_name=model_name,
            provider_type="openrouter"
        )
        self.assertIsNotNone(sft)
        self.assertEqual(sft.model_name, model_name)
    
    def test_sft_generate(self):
        """Test SFT generate method."""
        model_name = get_test_model("openrouter", "cheap")
        sft = get_sft_provider(
            model_name=model_name,
            provider_type="openrouter"
        )
        
        # Test generation
        prompt = "What is the capital of France?"
        response = sft.generate(prompt)
        
        self.assertIsNotNone(response)
        self.assertIsInstance(response, str)
        self.assertTrue(len(response) > 0)


class TestGRPOProviders(unittest.TestCase):
    """Test GRPO provider initialization and basic functionality."""
    
    def setUp(self):
        """Set up the test environment."""
        self.original_env = setup_test_environment()
    
    def tearDown(self):
        """Restore the original environment."""
        restore_environment(self.original_env)
    
    def test_openrouter_grpo_init(self):
        """Test OpenRouter GRPO initialization."""
        model_name = get_test_model("openrouter", "cheap")
        grpo = get_grpo_provider(
            model_name=model_name,
            provider_type="openrouter"
        )
        self.assertIsNotNone(grpo)
        self.assertEqual(grpo.model_name, model_name)
    
    def test_grpo_generate(self):
        """Test GRPO generate method."""
        model_name = get_test_model("openrouter", "cheap")
        grpo = get_grpo_provider(
            model_name=model_name,
            provider_type="openrouter"
        )
        
        # Test generation
        prompt = "What is the capital of France?"
        response = grpo.generate(prompt)
        
        self.assertIsNotNone(response)
        self.assertIsInstance(response, str)
        self.assertTrue(len(response) > 0)


class TestRewardFunctions(unittest.TestCase):
    """Test reward functions for RL tuning."""
    
    def test_format_reward_function(self):
        """Test FormatRewardFunction."""
        format_reward = FormatRewardFunction(
            format_pattern="JSON",
            regex_patterns=[r"\{.*\}", r"^\s*\{"]
        )
        
        outputs = [
            '{"name": "John", "age": 30}',
            'The answer is 42',
            '{ "result": true }'
        ]
        
        rewards = format_reward.compute_reward(outputs)
        
        self.assertIsInstance(rewards, np.ndarray)
        self.assertEqual(len(rewards), len(outputs))
        self.assertEqual(rewards[0], 1.0)  # First output is valid JSON
        self.assertEqual(rewards[1], 0.0)  # Second output is not JSON
        self.assertEqual(rewards[2], 1.0)  # Third output is valid JSON
    
    def test_accuracy_reward_function(self):
        """Test AccuracyReward."""
        accuracy_reward = AccuracyReward()
        
        outputs = [
            "The capital of France is Paris",
            "The capital of Germany is Berlin",
            "The capital of Italy is Rome"
        ]
        
        references = [
            "Paris is the capital of France",
            "Berlin is the capital of Germany",
            "Milan is the capital of Italy"  # Incorrect reference
        ]
        
        rewards = accuracy_reward.compute_reward(outputs, references)
        
        self.assertIsInstance(rewards, np.ndarray)
        self.assertEqual(len(rewards), len(outputs))
        
        # Check that the first two rewards are higher than the third
        # (since the first two outputs match their references better)
        self.assertGreater(rewards[0], 0.0)
        self.assertGreater(rewards[1], 0.0)
        self.assertLess(rewards[2], rewards[0])  # Third reward should be lower
    
    def test_reasoning_quality_reward_function(self):
        """Test ReasoningQualityReward."""
        reasoning_reward = ReasoningQualityReward()
        
        outputs = [
            # Good reasoning with steps and conclusion
            "First, we need to understand the problem. Second, we analyze the options. "
            "Finally, we can conclude that option A is correct because it satisfies all conditions.",
            
            # Poor reasoning with no steps or conclusion
            "The answer is 42."
        ]
        
        rewards = reasoning_reward.compute_reward(outputs)
        
        self.assertIsInstance(rewards, np.ndarray)
        self.assertEqual(len(rewards), len(outputs))
        
        # First output should have higher reward than second
        self.assertGreater(rewards[0], rewards[1])


class TestIntegration(unittest.TestCase):
    """Test integration between different RL components."""
    
    def setUp(self):
        """Set up the test environment."""
        self.original_env = setup_test_environment()
        
        # Define a simple reward function for testing
        def simple_reward_fn(prompt, response):
            return 0.5
            
        self.reward_fn = simple_reward_fn
    
    def tearDown(self):
        """Restore the original environment."""
        restore_environment(self.original_env)
    
    def test_ppo_with_reward_function(self):
        """Test PPO with a custom reward function."""
        model_name = get_test_model("openrouter", "cheap")
        
        # Create a custom reward function
        format_reward = FormatRewardFunction(
            format_pattern="JSON",
            regex_patterns=[r"\{.*\}", r"^\s*\{"]
        )
        
        # Create a reward function that wraps the format reward
        def custom_reward_fn(prompt, response):
            return float(format_reward.compute_reward([response])[0])
        
        # Initialize PPO with the custom reward function
        ppo = get_ppo_provider(
            model_name=model_name,
            provider_type="openrouter",
            reward_fn=custom_reward_fn
        )
        
        # Test generation
        prompt = "Generate a JSON object with a name and age field."
        response = ppo.generate(prompt)
        
        self.assertIsNotNone(response)
        self.assertIsInstance(response, str)
        
        # Check if the response contains JSON
        self.assertTrue("{" in response and "}" in response)


if __name__ == "__main__":
    unittest.main()
