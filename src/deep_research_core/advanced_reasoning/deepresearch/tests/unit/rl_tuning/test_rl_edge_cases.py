"""
Test edge cases and error handling for RL modules.

This test suite focuses on testing edge cases and error handling
for the RL tuning modules (PPO, SFT, GRPO).
"""

import os
import sys
import unittest
import numpy as np
from typing import Dict, Any, List, Optional

# Add the src directory to the path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))

from src.deep_research_core.utils.test_utils import setup_test_environment, restore_environment, get_test_model
from src.deep_research_core.rl_tuning.providers.ppo_providers import get_ppo_provider
from src.deep_research_core.rl_tuning.providers.sft_providers import get_sft_provider
from src.deep_research_core.rl_tuning.providers.grpo_providers import get_grpo_provider
from src.deep_research_core.rl_tuning.grpo.base import FormatRewardFunction, RewardFunction
from src.deep_research_core.rl_tuning.grpo.outcome_rewards import AccuracyReward


class TestPPOEdgeCases(unittest.TestCase):
    """Test edge cases for PPO providers."""
    
    def setUp(self):
        """Set up the test environment."""
        self.original_env = setup_test_environment()
        
        # Define a simple reward function for testing
        def simple_reward_fn(prompt, response):
            return 0.5
            
        self.reward_fn = simple_reward_fn
    
    def tearDown(self):
        """Restore the original environment."""
        restore_environment(self.original_env)
    
    def test_empty_prompt(self):
        """Test PPO with an empty prompt."""
        model_name = get_test_model("openrouter", "cheap")
        ppo = get_ppo_provider(
            model_name=model_name,
            provider_type="openrouter",
            reward_fn=self.reward_fn
        )
        
        # Test with empty prompt
        response = ppo.generate("")
        
        # Should return a non-empty response or handle gracefully
        self.assertIsNotNone(response)
    
    def test_very_long_prompt(self):
        """Test PPO with a very long prompt."""
        model_name = get_test_model("openrouter", "cheap")
        ppo = get_ppo_provider(
            model_name=model_name,
            provider_type="openrouter",
            reward_fn=self.reward_fn
        )
        
        # Create a very long prompt (10,000 characters)
        long_prompt = "This is a test. " * 1000
        
        # Should handle long prompts gracefully (either by truncating or returning an error)
        try:
            response = ppo.generate(long_prompt)
            self.assertIsNotNone(response)
        except Exception as e:
            # If it raises an exception, it should be a specific type related to context length
            self.assertIn("context", str(e).lower())
    
    def test_invalid_reward_function(self):
        """Test PPO with an invalid reward function."""
        model_name = get_test_model("openrouter", "cheap")
        
        # Define an invalid reward function that raises an exception
        def invalid_reward_fn(prompt, response):
            raise ValueError("Invalid reward function")
        
        ppo = get_ppo_provider(
            model_name=model_name,
            provider_type="openrouter",
            reward_fn=invalid_reward_fn
        )
        
        # Test generation - should handle the invalid reward function gracefully
        prompt = "What is the capital of France?"
        
        try:
            response = ppo.generate(prompt)
            self.assertIsNotNone(response)
        except Exception as e:
            # If it raises an exception, it should be handled or propagated clearly
            self.assertIn("reward", str(e).lower())


class TestSFTEdgeCases(unittest.TestCase):
    """Test edge cases for SFT providers."""
    
    def setUp(self):
        """Set up the test environment."""
        self.original_env = setup_test_environment()
    
    def tearDown(self):
        """Restore the original environment."""
        restore_environment(self.original_env)
    
    def test_empty_prompt(self):
        """Test SFT with an empty prompt."""
        model_name = get_test_model("openrouter", "cheap")
        sft = get_sft_provider(
            model_name=model_name,
            provider_type="openrouter"
        )
        
        # Test with empty prompt
        response = sft.generate("")
        
        # Should return a non-empty response or handle gracefully
        self.assertIsNotNone(response)
    
    def test_invalid_training_data(self):
        """Test SFT with invalid training data."""
        model_name = get_test_model("openrouter", "cheap")
        sft = get_sft_provider(
            model_name=model_name,
            provider_type="openrouter"
        )
        
        # Test with invalid training data (empty list)
        with self.assertRaises(Exception):
            sft.train([])
        
        # Test with invalid training data (wrong format)
        with self.assertRaises(Exception):
            sft.train(["invalid data"])


class TestGRPOEdgeCases(unittest.TestCase):
    """Test edge cases for GRPO providers."""
    
    def setUp(self):
        """Set up the test environment."""
        self.original_env = setup_test_environment()
    
    def tearDown(self):
        """Restore the original environment."""
        restore_environment(self.original_env)
    
    def test_empty_prompt(self):
        """Test GRPO with an empty prompt."""
        model_name = get_test_model("openrouter", "cheap")
        grpo = get_grpo_provider(
            model_name=model_name,
            provider_type="openrouter"
        )
        
        # Test with empty prompt
        response = grpo.generate("")
        
        # Should return a non-empty response or handle gracefully
        self.assertIsNotNone(response)
    
    def test_no_reward_functions(self):
        """Test GRPO with no reward functions."""
        model_name = get_test_model("openrouter", "cheap")
        grpo = get_grpo_provider(
            model_name=model_name,
            provider_type="openrouter"
        )
        
        # Remove all reward functions
        grpo.reward_functions = {}
        
        # Test generation - should handle the lack of reward functions gracefully
        prompt = "What is the capital of France?"
        
        try:
            response = grpo.generate(prompt)
            self.assertIsNotNone(response)
        except Exception as e:
            # If it raises an exception, it should be handled or propagated clearly
            self.assertIn("reward", str(e).lower())


class TestRewardFunctionEdgeCases(unittest.TestCase):
    """Test edge cases for reward functions."""
    
    def test_empty_outputs(self):
        """Test reward functions with empty outputs."""
        format_reward = FormatRewardFunction(
            format_pattern="JSON",
            regex_patterns=[r"\{.*\}", r"^\s*\{"]
        )
        
        # Test with empty outputs list
        rewards = format_reward.compute_reward([])
        
        # Should return an empty array
        self.assertIsInstance(rewards, np.ndarray)
        self.assertEqual(len(rewards), 0)
    
    def test_empty_output_string(self):
        """Test reward functions with empty output strings."""
        format_reward = FormatRewardFunction(
            format_pattern="JSON",
            regex_patterns=[r"\{.*\}", r"^\s*\{"]
        )
        
        # Test with empty output string
        rewards = format_reward.compute_reward([""])
        
        # Should return a valid reward (likely 0.0)
        self.assertIsInstance(rewards, np.ndarray)
        self.assertEqual(len(rewards), 1)
        self.assertEqual(rewards[0], 0.0)
    
    def test_mismatched_references(self):
        """Test reward functions with mismatched references."""
        accuracy_reward = AccuracyReward()
        
        outputs = ["Output 1", "Output 2", "Output 3"]
        references = ["Reference 1", "Reference 2"]  # One fewer reference than outputs
        
        # Should handle mismatched lengths gracefully
        rewards = accuracy_reward.compute_reward(outputs, references)
        
        # Should return rewards for the minimum length
        self.assertIsInstance(rewards, np.ndarray)
        self.assertEqual(len(rewards), 2)  # Should match the shorter length
    
    def test_custom_reward_function(self):
        """Test creating and using a custom reward function."""
        # Create a custom reward function
        class CustomReward(RewardFunction):
            def compute_reward(self, outputs, references=None, **kwargs):
                # Simple reward function that returns 1.0 for outputs containing "correct"
                rewards = np.zeros(len(outputs))
                for i, output in enumerate(outputs):
                    if "correct" in output.lower():
                        rewards[i] = 1.0
                return rewards
        
        custom_reward = CustomReward()
        
        # Test the custom reward function
        outputs = ["This is correct", "This is wrong", "Also correct answer"]
        rewards = custom_reward.compute_reward(outputs)
        
        # Check the rewards
        self.assertIsInstance(rewards, np.ndarray)
        self.assertEqual(len(rewards), 3)
        self.assertEqual(rewards[0], 1.0)  # Contains "correct"
        self.assertEqual(rewards[1], 0.0)  # Does not contain "correct"
        self.assertEqual(rewards[2], 1.0)  # Contains "correct"


if __name__ == "__main__":
    unittest.main()
