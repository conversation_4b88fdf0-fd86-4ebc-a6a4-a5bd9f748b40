"""
Unit tests for the Agent Environment module.
"""

import os
import unittest
import numpy as np
from typing import Dict, Any

from deep_research_core.rl_tuning.environment import AgentEnvironment, ReasoningEnvironment


class TestAgentEnvironment(unittest.TestCase):
    """Test cases for the AgentEnvironment class."""

    def setUp(self):
        """Set up test environment."""
        self.env = AgentEnvironment(
            name="test_environment",
            verbose=False
        )

    def test_initialization(self):
        """Test environment initialization."""
        self.assertEqual(self.env.name, "test_environment")
        self.assertFalse(self.env.is_initialized)
        self.assertFalse(self.env.is_terminated)
        self.assertIsNone(self.env.current_state)

    def test_reset(self):
        """Test environment reset."""
        observation = self.env.reset(
            query="Test query",
            context="Test context"
        )

        self.assertTrue(self.env.is_initialized)
        self.assertEqual(self.env.current_step, 0)
        self.assertEqual(len(self.env.episode_rewards), 0)
        self.assertEqual(observation.get("query"), "Test query")
        self.assertEqual(observation.get("context"), "Test context")

    def test_step(self):
        """Test environment step."""
        # Reset environment
        self.env.reset(query="Test query")

        # Take a step
        action = {
            "action_type": "think",
            "content": "Test thinking"
        }

        next_observation, reward, done, info = self.env.step(action)

        self.assertEqual(self.env.current_step, 1)
        self.assertEqual(len(self.env.episode_rewards), 1)
        self.assertFalse(done)
        self.assertTrue("action_executed" in info)

    def test_multiple_steps(self):
        """Test multiple steps in the environment."""
        # Reset environment
        self.env.reset(query="Test query")

        # Take multiple steps
        actions = [
            {"action_type": "think", "content": "Test thinking"},
            {"action_type": "use_tool", "tool_name": "test_tool", "content": "Test tool usage"},
            {"action_type": "respond", "content": "Test response"}
        ]

        for i, action in enumerate(actions):
            next_observation, reward, done, info = self.env.step(action)
            self.assertEqual(self.env.current_step, i + 1)

        # Check if history is properly tracked
        self.assertEqual(len(self.env.episode_history), 3)

    def test_invalid_action(self):
        """Test invalid action handling."""
        # Reset environment
        self.env.reset(query="Test query")

        # Invalid action type
        with self.assertRaises(ValueError):
            action = {
                "action_type": "invalid_type",
                "content": "Test content"
            }
            self.env.step(action)

        # Missing action_type
        with self.assertRaises(ValueError):
            action = {
                "content": "Test content"
            }
            self.env.step(action)

        # Invalid action structure
        with self.assertRaises(ValueError):
            action = "not_a_dict"
            self.env.step(action)

    def test_termination(self):
        """Test environment termination."""
        # Reset environment with max_steps_per_episode=2
        env = AgentEnvironment(
            name="test_termination",
            max_steps_per_episode=2,
            verbose=False
        )
        env.reset(query="Test query")

        # Take 2 steps (should terminate after the second)
        action = {"action_type": "think", "content": "Test thinking"}
        _, _, done1, _ = env.step(action)
        self.assertFalse(done1)

        action = {"action_type": "respond", "content": "Test response"}
        _, _, done2, _ = env.step(action)
        self.assertTrue(done2)
        self.assertTrue(env.is_terminated)

    def test_reward_calculation(self):
        """Test reward calculation."""
        # Reset environment
        self.env.reset(query="Test query")

        # Take a step
        action = {
            "action_type": "think",
            "content": "Test thinking"
        }

        _, reward, _, _ = self.env.step(action)

        # Default reward should be small negative (efficiency penalty)
        self.assertLess(reward, 0)

        # Test success reward
        self.env.current_state["is_success"] = True
        reward = self.env.reward_function(
            self.env.current_state,
            action,
            self.env.current_state
        )
        self.assertGreater(reward, 0)

    def test_episode_stats(self):
        """Test episode statistics."""
        # Reset environment
        self.env.reset(query="Test query")

        # Take steps
        action1 = {"action_type": "think", "content": "Test thinking"}
        self.env.step(action1)

        action2 = {"action_type": "respond", "content": "Test response"}
        self.env.step(action2)

        # Get stats
        stats = self.env.get_episode_stats()

        self.assertEqual(stats["steps"], 2)
        self.assertEqual(len(self.env.episode_rewards), 2)
        self.assertEqual(stats["total_reward"], sum(self.env.episode_rewards))


class TestReasoningEnvironment(unittest.TestCase):
    """Test cases for the ReasoningEnvironment class."""

    def setUp(self):
        """Set up test environment."""
        self.env = ReasoningEnvironment(
            name="test_reasoning",
            reasoning_type="cot",
            task_type="qa",
            max_reasoning_steps=3,
            verbose=False
        )

    def test_initialization(self):
        """Test reasoning environment initialization."""
        self.assertEqual(self.env.name, "test_reasoning")
        self.assertEqual(self.env.reasoning_type, "cot")
        self.assertEqual(self.env.task_type, "qa")
        self.assertEqual(self.env.max_reasoning_steps, 3)

    def test_reset_with_task(self):
        """Test reset with specific task."""
        task = {
            "query": "Test question?",
            "context": "Test context",
            "facts": ["Fact 1", "Fact 2"],
            "ground_truth": "Test answer"
        }

        observation = self.env.reset_with_task(task)

        self.assertEqual(observation["query"], "Test question?")
        self.assertEqual(observation["reasoning_type"], "cot")
        self.assertEqual(observation["reasoning_step"], 0)
        self.assertEqual(len(observation["facts"]), 2)
        self.assertEqual(self.env.reasoning_state["ground_truth"], "Test answer")

    def test_reasoning_steps(self):
        """Test reasoning-specific steps."""
        # Reset environment
        task = {
            "query": "Test question?",
            "ground_truth": "Test answer"
        }
        self.env.reset_with_task(task)

        # Step 1: Think
        action1 = {
            "action_type": "think",
            "content": "Test thinking",
            "reasoning_step": {
                "step_type": "observation",
                "content": "Initial observation",
                "confidence": 0.8
            }
        }
        obs1, reward1, done1, info1 = self.env.step(action1)

        self.assertEqual(obs1["reasoning_step"], 1)
        self.assertEqual(len(obs1["reasoning_history"]), 1)
        self.assertEqual(self.env.reasoning_state["current_reasoning_step"], 1)

        # Step 2: Explain
        action2 = {
            "action_type": "explain",
            "content": "Test explanation",
            "reasoning_step": {
                "step_type": "inference",
                "content": "Inference step",
                "confidence": 0.9
            }
        }
        obs2, reward2, done2, info2 = self.env.step(action2)

        self.assertEqual(obs2["reasoning_step"], 2)
        self.assertEqual(len(obs2["reasoning_history"]), 2)

        # Step 3: Conclude with correct answer
        action3 = {
            "action_type": "conclude",
            "content": "Test answer",
            "reasoning_step": {
                "step_type": "conclusion",
                "content": "Final conclusion",
                "confidence": 0.95
            }
        }
        obs3, reward3, done3, info3 = self.env.step(action3)

        self.assertEqual(self.env.reasoning_state["final_answer"], "Test answer")
        self.assertTrue(done3)  # Should be done after conclude
        self.assertTrue(info3["is_correct"])  # Answer matches ground truth

    def test_reasoning_stats(self):
        """Test reasoning statistics."""
        # Reset environment
        task = {
            "query": "Test question?",
            "ground_truth": "Test answer"
        }
        self.env.reset_with_task(task)

        # Take some steps
        action1 = {"action_type": "think", "content": "Test thinking"}
        self.env.step(action1)

        action2 = {"action_type": "explain", "content": "Test explanation"}
        self.env.step(action2)

        action3 = {"action_type": "conclude", "content": "Test answer"}
        self.env.step(action3)

        # Get reasoning stats
        stats = self.env.get_reasoning_stats()

        self.assertEqual(stats["reasoning_type"], "cot")
        self.assertEqual(stats["task_type"], "qa")
        self.assertEqual(stats["reasoning_steps"], 3)
        self.assertEqual(stats["final_answer"], "Test answer")
        self.assertEqual(stats["ground_truth"], "Test answer")
        self.assertTrue(stats["success"])
        self.assertTrue(stats["correctness"])

    def test_tool_usage(self):
        """Test tool usage in reasoning environment."""
        # Reset environment with tools
        task = {
            "query": "Test question?",
            "tools": ["test_tool_1", "test_tool_2"]
        }
        self.env.reset_with_task(task)

        # Use a tool
        action = {
            "action_type": "use_tool",
            "tool_name": "test_tool_1",
            "tool_input": "test input",
            "content": "Using test tool"
        }
        obs, reward, done, info = self.env.step(action)

        self.assertIn("test_tool_1", self.env.reasoning_state["tools_used"])
        self.assertTrue("tool_result" in obs)
        self.assertTrue(info["tool_result"]["success"])


if __name__ == "__main__":
    unittest.main() 