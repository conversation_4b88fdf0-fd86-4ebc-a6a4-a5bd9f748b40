"""
Unit tests for ReAct error recovery.

This module contains tests for the ReAct error recovery system.
"""

import unittest
from unittest.mock import patch, MagicMock, call

# Mock the BaseReasoner class
from src.deep_research_core.reasoning.base import BaseReasoner
class MockBaseReasoner(BaseReasoner):
    def __init__(self, provider=None, model=None, temperature=None, max_tokens=None, **kwargs):
        self.provider = provider
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.kwargs = kwargs

    def reason(self, query, **kwargs):
        return {"answer": "Mock answer"}

# Patch BaseReasoner
patch('src.deep_research_core.reasoning.base.BaseReasoner', MockBaseReasoner).start()

from src.deep_research_core.reasoning.react import ReAct
from src.deep_research_core.utils.error_recovery import (
    ErrorRecoveryStrategy, RetryStrategy, AlternativeToolStrategy,
    InputReformulationStrategy, FallbackResultStrategy
)
from src.deep_research_core.utils.intelligent_error_detection import ErrorDetector
from src.deep_research_core.utils.error_learning import ErrorLearningManager
from src.deep_research_core.utils.advanced_recovery_strategies import (
    ContextAwareReformulationStrategy, PromptEngineeringStrategy,
    ModelFallbackStrategy, ToolParameterAdjustmentStrategy,
    AdaptiveRecoveryStrategy
)


class TestReActErrorRecovery(unittest.TestCase):
    """Test the ReAct error recovery system."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a mock API provider
        self.api_provider = MagicMock()
        self.api_provider.generate.return_value = "Test response"

        # Create a mock tool registry
        self.tool_registry = {
            "test_tool": MagicMock(),
            "alternative_tool": MagicMock()
        }

        # Create tool alternatives
        self.tool_alternatives = {
            "test_tool": ["alternative_tool"]
        }

        # Create a ReAct instance with error recovery
        self.react = ReAct(
            api_provider=self.api_provider,
            model="test-model",
            tool_registry=self.tool_registry,
            tool_alternatives=self.tool_alternatives,
            use_error_recovery=True,
            max_retries=3,
            retry_delay=0.1
        )

    def test_error_recovery_initialization(self):
        """Test that error recovery is initialized correctly."""
        # Check that error recovery is enabled
        self.assertTrue(self.react.use_error_recovery)

        # Check that error recovery manager is created
        self.assertIsNotNone(self.react.error_recovery)

        # Check that error detector is created
        self.assertIsNotNone(self.react.error_detector)

        # Check that error learning manager is created
        self.assertIsNotNone(self.react.error_learning_manager)

        # Check that strategies are created
        strategies = self.react.error_recovery.strategies
        self.assertGreaterEqual(len(strategies), 1)

        # Check that we have an adaptive strategy
        self.assertEqual(strategies[0].name, "adaptive_recovery")

    @patch("src.deep_research_core.reasoning.react.ErrorDetector")
    def test_error_detection(self, mock_error_detector_class):
        """Test error detection."""
        # Create a mock error detector
        mock_error_detector = MagicMock()
        mock_error_detector.detect.return_value = {
            "category": "invalid_arguments",
            "severity": "medium",
            "relevant_info": {"problematic_arg": "query"},
            "recoverable": True
        }
        mock_error_detector_class.return_value = mock_error_detector

        # Create a ReAct instance with the mock error detector
        react = ReAct(
            api_provider=self.api_provider,
            model="test-model",
            tool_registry=self.tool_registry,
            tool_alternatives=self.tool_alternatives,
            use_error_recovery=True
        )

        # Replace the error detector with our mock
        react.error_detector = mock_error_detector

        # Create a mock error recovery manager
        mock_error_recovery = MagicMock()
        mock_error_recovery.recover.return_value = {
            "success": True,
            "strategy": "retry",
            "message": "Retrying..."
        }
        react.error_recovery = mock_error_recovery

        # Create a test error
        test_error = ValueError("Missing required argument 'query'")

        # Create a mock _execute_action method that raises the test error
        original_execute_action = react._execute_action

        def mock_execute_action(action_name, action_args):
            if action_name == "test_tool" and not action_args.get("query"):
                raise test_error
            return {"result": "success"}

        react._execute_action = mock_execute_action

        # Call _execute_action with an error
        try:
            result = react._execute_action("test_tool", {})
        except Exception:
            # If error recovery fails, the error will be re-raised
            pass

        # Check that error detection was called
        mock_error_detector.detect.assert_called_once()

        # Check that the error context includes the detection result
        error_context = mock_error_recovery.recover.call_args[0][1]
        self.assertEqual(error_context["category"], "invalid_arguments")
        self.assertEqual(error_context["severity"], "medium")
        self.assertEqual(error_context["relevant_info"]["problematic_arg"], "query")
        self.assertTrue(error_context["recoverable"])

        # Restore the original _execute_action method
        react._execute_action = original_execute_action

    @patch("src.deep_research_core.reasoning.react.ErrorLearningManager")
    def test_error_learning(self, mock_error_learning_manager_class):
        """Test error learning."""
        # Create a mock error learning manager
        mock_error_learning_manager = MagicMock()
        mock_error_learning_manager.get_best_strategy.return_value = "retry"
        mock_error_learning_manager_class.return_value = mock_error_learning_manager

        # Create a ReAct instance with the mock error learning manager
        react = ReAct(
            api_provider=self.api_provider,
            model="test-model",
            tool_registry=self.tool_registry,
            tool_alternatives=self.tool_alternatives,
            use_error_recovery=True
        )

        # Replace the error learning manager with our mock
        react.error_learning_manager = mock_error_learning_manager

        # Create a mock error recovery manager
        mock_error_recovery = MagicMock()
        mock_error_recovery.recover.return_value = {
            "success": True,
            "strategy": "retry",
            "message": "Retrying..."
        }
        react.error_recovery = mock_error_recovery

        # Create a test error
        test_error = ValueError("Missing required argument 'query'")

        # Create a mock _execute_action method that raises the test error
        original_execute_action = react._execute_action

        def mock_execute_action(action_name, action_args):
            if action_name == "test_tool" and not action_args.get("query"):
                raise test_error
            return {"result": "success"}

        react._execute_action = mock_execute_action

        # Call _execute_action with an error
        try:
            result = react._execute_action("test_tool", {})
        except Exception:
            # If error recovery fails, the error will be re-raised
            pass

        # Check that error tracking was called
        mock_error_learning_manager.track_error.assert_called_once()

        # Restore the original _execute_action method
        react._execute_action = original_execute_action

    def test_retry_strategy(self):
        """Test the retry strategy."""
        # Create a test error
        test_error = ValueError("Test error")

        # Create a context
        context = {
            "tool_name": "test_tool",
            "tool_args": {"param": "value"},
            "retry_count": 0
        }

        # Create a retry strategy
        strategy = RetryStrategy(max_retries=3, initial_delay=0.01)

        # Check that the strategy can handle the error
        self.assertTrue(strategy.can_handle(test_error, context))

        # Recover from the error
        result = strategy.recover(test_error, context)

        # Check the result
        self.assertTrue(result["success"])
        self.assertEqual(result["strategy"], "retry")
        self.assertEqual(context["retry_count"], 1)

        # Try again with retry_count at the limit
        context["retry_count"] = 3
        self.assertFalse(strategy.can_handle(test_error, context))

    def test_alternative_tool_strategy(self):
        """Test the alternative tool strategy."""
        # Create a test error
        test_error = ValueError("Tool not found")

        # Create a context
        context = {
            "tool_name": "test_tool",
            "tool_args": {"param": "value"},
            "tried_alternatives": []
        }

        # Create an alternative tool strategy
        strategy = AlternativeToolStrategy(
            tool_alternatives=self.tool_alternatives,
            tool_registry=self.tool_registry
        )

        # Check that the strategy can handle the error
        self.assertTrue(strategy.can_handle(test_error, context))

        # Recover from the error
        result = strategy.recover(test_error, context)

        # Check the result
        self.assertTrue(result["success"])
        self.assertEqual(result["strategy"], "alternative_tool")
        self.assertEqual(result["alternative_tool_name"], "alternative_tool")
        self.assertEqual(context["tried_alternatives"], ["alternative_tool"])

        # Try again with all alternatives tried
        context["tried_alternatives"] = ["alternative_tool"]
        self.assertFalse(strategy.can_handle(test_error, context))

    def test_context_aware_reformulation_strategy(self):
        """Test the context-aware reformulation strategy."""
        # Create a test error
        test_error = ValueError("Invalid query format")

        # Create a context
        context = {
            "tool_name": "web_search",
            "tool_args": {"query": "test query"},
            "context_reformulation_attempted": False
        }

        # Create a context-aware reformulation strategy
        strategy = ContextAwareReformulationStrategy()

        # Check that the strategy can handle the error
        self.assertTrue(strategy.can_handle(test_error, context))

        # Recover from the error
        result = strategy.recover(test_error, context)

        # Check the result
        self.assertTrue(result["success"])
        self.assertEqual(result["strategy"], "context_aware_reformulation")
        self.assertEqual(result["reformulated_args"]["query"], "test query")
        self.assertTrue(context["context_reformulation_attempted"])

        # Try again with reformulation already attempted
        context["context_reformulation_attempted"] = True
        self.assertFalse(strategy.can_handle(test_error, context))

    def test_tool_parameter_adjustment_strategy(self):
        """Test the tool parameter adjustment strategy."""
        # Create a test error
        test_error = TimeoutError("Request timed out")

        # Create a context
        context = {
            "tool_name": "api_request",
            "tool_args": {"timeout": 10},
            "parameter_adjustment_attempted": False
        }

        # Create a tool parameter adjustment strategy
        strategy = ToolParameterAdjustmentStrategy()

        # Check that the strategy can handle the error
        self.assertTrue(strategy.can_handle(test_error, context))

        # Recover from the error
        result = strategy.recover(test_error, context)

        # Check the result
        self.assertTrue(result["success"])
        self.assertEqual(result["strategy"], "tool_parameter_adjustment")
        self.assertEqual(result["adjusted_args"]["timeout"], 20)
        self.assertTrue(context["parameter_adjustment_attempted"])

        # Try again with parameter adjustment already attempted
        context["parameter_adjustment_attempted"] = True
        self.assertFalse(strategy.can_handle(test_error, context))

    def test_model_fallback_strategy(self):
        """Test the model fallback strategy."""
        # Create a test error
        test_error = ValueError("Model error")

        # Create a context
        context = {
            "api_provider": self.api_provider,
            "model": "test-model",
            "tried_models": []
        }

        # Create a model fallback strategy
        strategy = ModelFallbackStrategy(fallback_models=["fallback-model"])

        # Check that the strategy can handle the error
        self.assertTrue(strategy.can_handle(test_error, context))

        # Recover from the error
        result = strategy.recover(test_error, context)

        # Check the result
        self.assertTrue(result["success"])
        self.assertEqual(result["strategy"], "model_fallback")
        self.assertEqual(result["original_model"], "test-model")
        self.assertEqual(result["fallback_model"], "fallback-model")
        self.assertEqual(context["tried_models"], ["test-model", "fallback-model"])

        # Try again with all models tried
        context["tried_models"] = ["test-model", "fallback-model"]
        self.assertFalse(strategy.can_handle(test_error, context))

    def test_adaptive_recovery_strategy(self):
        """Test the adaptive recovery strategy."""
        # Create mock strategies
        mock_strategy1 = MagicMock(spec=ErrorRecoveryStrategy)
        mock_strategy1.name = "strategy1"
        mock_strategy1.effectiveness = 0.8
        mock_strategy1.can_handle.return_value = True
        mock_strategy1.recover.return_value = {
            "success": True,
            "strategy": "strategy1",
            "message": "Recovered with strategy1"
        }

        mock_strategy2 = MagicMock(spec=ErrorRecoveryStrategy)
        mock_strategy2.name = "strategy2"
        mock_strategy2.effectiveness = 0.5
        mock_strategy2.can_handle.return_value = True
        mock_strategy2.recover.return_value = {
            "success": True,
            "strategy": "strategy2",
            "message": "Recovered with strategy2"
        }

        # Create a mock error learning manager
        mock_error_learning_manager = MagicMock()
        mock_error_learning_manager.get_best_strategy.return_value = "strategy2"

        # Create an adaptive recovery strategy
        strategy = AdaptiveRecoveryStrategy(
            strategies=[mock_strategy1, mock_strategy2],
            error_learning_manager=mock_error_learning_manager
        )

        # Create a test error
        test_error = ValueError("Test error")

        # Create a context
        context = {"test": "context"}

        # Check that the strategy can handle the error
        self.assertTrue(strategy.can_handle(test_error, context))

        # Recover from the error
        result = strategy.recover(test_error, context)

        # Check the result
        self.assertTrue(result["success"])
        self.assertEqual(result["strategy"], "adaptive_recovery")
        self.assertEqual(result["adaptive_strategy"], "strategy2")

        # Check that the error learning manager was used
        mock_error_learning_manager.get_best_strategy.assert_called_once_with(test_error, context)

        # Check that the chosen strategy was used
        mock_strategy2.recover.assert_called_once_with(test_error, context)

        # Check that the strategy effectiveness was updated
        mock_strategy2.update_effectiveness.assert_called_once_with(True)

        # Check that the error was tracked
        mock_error_learning_manager.track_error.assert_called_once()

    def test_integration(self):
        """Test the integration of all components."""
        # Create a test error
        test_error = ValueError("Missing required argument 'query'")

        # Create a mock _execute_action method that raises the test error on first call
        # and succeeds on second call
        original_execute_action = self.react._execute_action
        call_count = [0]

        def mock_execute_action(action_name, action_args):
            call_count[0] += 1
            if call_count[0] == 1:
                raise test_error
            return {"result": "success"}

        self.react._execute_action = mock_execute_action

        # Call _execute_action with an error
        result = self.react._execute_action("test_tool", {})

        # Check that the error was recovered from
        self.assertEqual(result["result"], "success")
        self.assertEqual(call_count[0], 2)

        # Restore the original _execute_action method
        self.react._execute_action = original_execute_action


if __name__ == "__main__":
    unittest.main()
