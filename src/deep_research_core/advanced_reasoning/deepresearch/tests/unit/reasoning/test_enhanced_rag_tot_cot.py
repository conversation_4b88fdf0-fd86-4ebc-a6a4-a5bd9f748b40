"""
Unit tests for EnhancedRAGTOTCOTReasoner.

This module contains unit tests for the EnhancedRAGTOTCOTReasoner class,
which combines RAG, TOT, and COT with advanced optimization.
"""

import os
import unittest
from unittest.mock import patch, MagicMock, ANY
import json
import tempfile
from pathlib import Path

from src.deep_research_core.reasoning.enhanced_rag_tot_cot import EnhancedRAGTOTCOTReasoner
from src.deep_research_core.reasoning.ragtotcot_weight_optimizer import RAGTOTCOTWeightOptimizer
from src.deep_research_core.reasoning.ragtotcot_query_classifier import RAGTOTCOTQueryClassifier
from src.deep_research_core.evaluation.ragtotcot_analyzer import RAGTOTCOTAnalyzer
from src.deep_research_core.rag.base import BaseRAG

# Set API key for testing
os.environ["OPENROUTER_API_KEY"] = "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"

class TestEnhancedRAGTOTCOTReasoner(unittest.TestCase):
    """Test EnhancedRAGTOTCOTReasoner."""

    def setUp(self):
        """Set up test fixtures."""
        # Create mocks
        self.mock_rag = MagicMock(spec=BaseRAG)
        self.mock_weight_optimizer = MagicMock(spec=RAGTOTCOTWeightOptimizer)
        self.mock_query_classifier = MagicMock(spec=RAGTOTCOTQueryClassifier)
        self.mock_performance_analyzer = MagicMock(spec=RAGTOTCOTAnalyzer)
        
        # Set up mock returns
        self.mock_rag.query.return_value = {
            "documents": [
                {"content": "Test document 1", "metadata": {"source": "test1"}},
                {"content": "Test document 2", "metadata": {"source": "test2"}}
            ]
        }
        
        self.mock_weight_optimizer.get_weights_for_query.return_value = {
            "rag": 0.4, "tot": 0.3, "cot": 0.3
        }
        
        self.mock_weight_optimizer.get_optimal_strategy.return_value = {
            "use_rag": True,
            "use_tot": True,
            "use_cot": True,
            "weights": {"rag": 0.4, "tot": 0.3, "cot": 0.3},
            "query_type": "analytical"
        }
        
        self.mock_query_classifier.classify_query.return_value = "analytical"
        
        self.mock_query_classifier.get_optimal_strategy.return_value = {
            "use_rag": True,
            "use_tot": True,
            "use_cot": True,
            "weights": {"rag": 0.3, "tot": 0.4, "cot": 0.3},
            "query_type": "analytical"
        }
        
        self.mock_performance_analyzer.analyze_contribution.return_value = {
            "contributions": {"rag": 0.4, "tot": 0.3, "cot": 0.3}
        }
        
        self.mock_performance_analyzer.recommend_weights.return_value = {
            "rag": 0.35, "tot": 0.35, "cot": 0.3
        }
        
        self.mock_performance_analyzer.generate_visualization.return_value = "visualization_data"
        
        # Create patches
        self.patches = [
            patch('src.deep_research_core.reasoning.tot.TreeOfThought'),
            patch('src.deep_research_core.reasoning.cot.ChainOfThought'),
            patch('src.deep_research_core.reasoning.ragtotcot_weight_optimizer.RAGTOTCOTWeightOptimizer'),
            patch('src.deep_research_core.reasoning.ragtotcot_query_classifier.RAGTOTCOTQueryClassifier'),
            patch('src.deep_research_core.evaluation.ragtotcot_analyzer.RAGTOTCOTAnalyzer'),
            patch('src.deep_research_core.multilingual.vietnamese_embeddings.VietnameseEmbeddingFactory')
        ]
        
        # Start patches
        self.mock_tot_class = self.patches[0].start()
        self.mock_cot_class = self.patches[1].start()
        self.mock_weight_optimizer_class = self.patches[2].start()
        self.mock_query_classifier_class = self.patches[3].start()
        self.mock_performance_analyzer_class = self.patches[4].start()
        self.mock_vietnamese_embedding_factory = self.patches[5].start()
        
        # Set up mock instances
        self.mock_tot = MagicMock()
        self.mock_cot = MagicMock()
        
        self.mock_tot_class.return_value = self.mock_tot
        self.mock_cot_class.return_value = self.mock_cot
        self.mock_weight_optimizer_class.return_value = self.mock_weight_optimizer
        self.mock_query_classifier_class.return_value = self.mock_query_classifier
        self.mock_performance_analyzer_class.return_value = self.mock_performance_analyzer
        
        # Set up mock returns for TOT and COT
        self.mock_tot.reason.return_value = {
            "query": "Test query",
            "reasoning": "Test TOT reasoning",
            "answer": "Test TOT answer",
            "model": "test-model",
            "provider": "test-provider",
            "latency": 1.0,
            "explored_paths": 3
        }
        
        self.mock_cot.reason.return_value = {
            "query": "Test query",
            "reasoning": "Test COT reasoning",
            "answer": "Test COT answer",
            "model": "test-model",
            "provider": "test-provider",
            "latency": 0.5
        }
        
        # Create EnhancedRAGTOTCOTReasoner instance
        self.reasoner = EnhancedRAGTOTCOTReasoner(
            rag_system=self.mock_rag,
            provider="openrouter",
            model="moonshotai/moonlight-16b-a3b-instruct:free",
            temperature=0.7,
            max_tokens=2000,
            language="vi",
            max_branches=3,
            max_depth=2,
            adaptive=True,
            use_advanced_optimization=True,
            verbose=True,
            use_weight_optimizer=True,
            use_query_classifier=True,
            use_performance_analyzer=True,
            use_vietnamese_optimization=True,
            vietnamese_embedding_model="phobert"
        )
        
        # Replace components with mocks
        self.reasoner.weight_optimizer = self.mock_weight_optimizer
        self.reasoner.query_classifier = self.mock_query_classifier
        self.reasoner.performance_analyzer = self.mock_performance_analyzer
        self.reasoner.tot = self.mock_tot
        self.reasoner.cot = self.mock_cot

    def tearDown(self):
        """Tear down test fixtures."""
        # Stop patches
        for p in self.patches:
            p.stop()

    def test_initialization(self):
        """Test initialization of EnhancedRAGTOTCOTReasoner."""
        self.assertEqual(self.reasoner.provider, "openrouter")
        self.assertEqual(self.reasoner.model, "moonshotai/moonlight-16b-a3b-instruct:free")
        self.assertEqual(self.reasoner.language, "vi")
        self.assertEqual(self.reasoner.max_branches, 3)
        self.assertEqual(self.reasoner.max_depth, 2)
        self.assertTrue(self.reasoner.use_weight_optimizer)
        self.assertTrue(self.reasoner.use_query_classifier)
        self.assertTrue(self.reasoner.use_performance_analyzer)
        self.assertTrue(self.reasoner.use_vietnamese_optimization)
        self.assertEqual(self.reasoner.vietnamese_embedding_model, "phobert")
        
        # Check that components were initialized
        self.mock_weight_optimizer_class.assert_called_once()
        self.mock_query_classifier_class.assert_called_once()
        self.mock_performance_analyzer_class.assert_called_once()
        self.mock_vietnamese_embedding_factory.get_embedding_model.assert_called_once_with(
            model_name="phobert"
        )

    def test_get_optimal_strategy(self):
        """Test getting optimal strategy for a query."""
        # Call _get_optimal_strategy
        strategy = self.reasoner._get_optimal_strategy("Test query")
        
        # Check that the query classifier was called
        self.mock_query_classifier.classify_query.assert_called_once_with("Test query")
        
        # Check that the weight optimizer was called
        self.mock_weight_optimizer.get_weights_for_query.assert_called_once_with("Test query")
        
        # Check the strategy
        self.assertTrue(strategy["use_rag"])
        self.assertTrue(strategy["use_tot"])
        self.assertTrue(strategy["use_cot"])
        self.assertEqual(strategy["weights"], {"rag": 0.4, "tot": 0.3, "cot": 0.3})
        self.assertEqual(strategy["query_type"], "analytical")

    def test_analyze_performance(self):
        """Test analyzing performance."""
        # Create test data
        query = "Test query"
        result = {
            "query": "Test query",
            "answer": "Test answer",
            "reasoning": "Test reasoning",
            "enhanced_reasoning": "Test enhanced reasoning",
            "total_latency": 1.5,
            "methods_used": "RAG-TOT-COT"
        }
        strategy = {
            "use_rag": True,
            "use_tot": True,
            "use_cot": True,
            "weights": {"rag": 0.4, "tot": 0.3, "cot": 0.3},
            "query_type": "analytical"
        }
        
        # Call _analyze_performance
        analysis = self.reasoner._analyze_performance(query, result, strategy)
        
        # Check that the performance analyzer was called
        self.mock_performance_analyzer.analyze_contribution.assert_called_once_with(
            query=query,
            result=result,
            weights_used=strategy["weights"]
        )
        
        # Check that the visualization was generated
        self.mock_performance_analyzer.generate_visualization.assert_called_once()
        
        # Check that weights were recommended
        self.mock_performance_analyzer.recommend_weights.assert_called_once()
        
        # Check the analysis
        self.assertEqual(analysis["query"], query)
        self.assertEqual(analysis["query_type"], "analytical")
        self.assertEqual(analysis["weights_used"], {"rag": 0.4, "tot": 0.3, "cot": 0.3})
        self.assertEqual(analysis["technique_contributions"], {"rag": 0.4, "tot": 0.3, "cot": 0.3})
        self.assertEqual(analysis["recommended_weights"], {"rag": 0.35, "tot": 0.35, "cot": 0.3})
        self.assertEqual(analysis["visualization"], "visualization_data")

    def test_update_weights_from_feedback(self):
        """Test updating weights from feedback."""
        # Create test data
        query = "Test query"
        feedback = {"rag": 0.8, "tot": 0.6, "cot": 0.7}
        query_type = "analytical"
        
        # Call _update_weights_from_feedback
        updated_weights = self.reasoner._update_weights_from_feedback(query, feedback, query_type)
        
        # Check that the weight optimizer was called
        self.mock_weight_optimizer.update_weights_from_feedback.assert_called_once_with(
            query=query,
            feedback=feedback,
            query_type=query_type
        )
        
        # Check the updated weights
        self.assertEqual(updated_weights, self.mock_weight_optimizer.update_weights_from_feedback.return_value)

    def test_reason_with_all_components(self):
        """Test reasoning with all components enabled."""
        # Call reason
        result = self.reasoner.reason(
            query="Test query",
            use_rag=None,
            use_tot=None,
            use_cot=None
        )
        
        # Check that the optimal strategy was determined
        self.mock_weight_optimizer.get_weights_for_query.assert_called_once()
        
        # Check that RAG was used
        self.mock_rag.query.assert_called_once()
        
        # Check that TOT was used
        self.mock_tot.reason.assert_called_once()
        
        # Check that COT was used to enhance TOT result
        self.mock_cot.reason.assert_called_once()
        
        # Check that performance was analyzed
        self.mock_performance_analyzer.analyze_contribution.assert_called_once()
        
        # Check the result
        self.assertIn("strategy", result)
        self.assertIn("performance_analysis", result)
        self.assertIn("weights_used", result)
        self.assertIn("query_type", result)
        self.assertIn("enhanced_latency", result)

    def test_reason_with_custom_parameters(self):
        """Test reasoning with custom parameters."""
        # Call reason with custom parameters
        result = self.reasoner.reason(
            query="Test query",
            use_rag=False,
            use_tot=True,
            use_cot=True,
            weights={"rag": 0.0, "tot": 0.6, "cot": 0.4}
        )
        
        # Check that RAG was not used
        self.mock_rag.query.assert_not_called()
        
        # Check that TOT was used
        self.mock_tot.reason.assert_called_once()
        
        # Check that COT was used to enhance TOT result
        self.mock_cot.reason.assert_called_once()
        
        # Check the result
        self.assertEqual(result["strategy"]["use_rag"], False)
        self.assertEqual(result["strategy"]["use_tot"], True)
        self.assertEqual(result["strategy"]["use_cot"], True)
        self.assertEqual(result["strategy"]["weights"], {"rag": 0.0, "tot": 0.6, "cot": 0.4})

    def test_provide_feedback(self):
        """Test providing feedback."""
        # Create test data
        query = "Test query"
        feedback = {"rag": 0.8, "tot": 0.6, "cot": 0.7}
        
        # Call provide_feedback
        updated_weights = self.reasoner.provide_feedback(query, feedback)
        
        # Check that the weight optimizer was called
        self.mock_weight_optimizer.update_weights_from_feedback.assert_called_once_with(
            query=query,
            feedback=feedback,
            query_type=None
        )
        
        # Check the updated weights
        self.assertEqual(updated_weights, self.mock_weight_optimizer.update_weights_from_feedback.return_value)

    def test_compare_methods_with_analysis(self):
        """Test comparing methods with analysis."""
        # Mock the parent class's compare_methods
        with patch.object(self.reasoner, 'compare_methods') as mock_compare_methods:
            mock_compare_methods.return_value = {
                "query": "Test query",
                "methods": {
                    "cot_only": {"answer": "COT answer", "latency": 0.5, "answer_length": 100},
                    "rag_cot": {"answer": "RAG+COT answer", "latency": 0.8, "answer_length": 150},
                    "tot_cot": {"answer": "TOT+COT answer", "latency": 1.0, "answer_length": 200},
                    "rag_tot_cot": {"answer": "RAG+TOT+COT answer", "latency": 1.5, "answer_length": 250}
                }
            }
            
            # Call compare_methods_with_analysis
            result = self.reasoner.compare_methods_with_analysis("Test query")
            
            # Check that the parent method was called
            mock_compare_methods.assert_called_once_with("Test query", None)
            
            # Check that performance was analyzed for each method
            self.assertEqual(self.mock_performance_analyzer.analyze_performance.call_count, 0)
            
            # Check the result
            self.assertIn("best_method", result)
            for method in result["methods"]:
                self.assertIn("analysis", result["methods"][method])

    def test_vietnamese_optimization(self):
        """Test Vietnamese optimization."""
        # Check that Vietnamese embedding model was initialized
        self.mock_vietnamese_embedding_factory.get_embedding_model.assert_called_once_with(
            model_name="phobert"
        )
        
        # Check that Vietnamese prompts were enhanced
        self.assertTrue(hasattr(self.reasoner, '_enhance_vietnamese_prompts'))

    def test_save_analysis_result(self):
        """Test saving analysis result."""
        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_path = temp_file.name
        
        try:
            # Set the analysis results path
            self.reasoner.analysis_results_path = temp_path
            
            # Create test data
            analysis_result = {
                "query": "Test query",
                "query_type": "analytical",
                "weights_used": {"rag": 0.4, "tot": 0.3, "cot": 0.3},
                "technique_contributions": {"rag": 0.4, "tot": 0.3, "cot": 0.3},
                "recommended_weights": {"rag": 0.35, "tot": 0.35, "cot": 0.3},
                "visualization": "visualization_data",
                "timestamp": 1234567890.0
            }
            
            # Call _save_analysis_result
            self.reasoner._save_analysis_result(analysis_result)
            
            # Check that the file was created
            self.assertTrue(os.path.exists(temp_path))
            
            # Read the file
            with open(temp_path, "r", encoding="utf-8") as f:
                saved_data = json.load(f)
            
            # Check the saved data
            self.assertEqual(len(saved_data), 1)
            self.assertEqual(saved_data[0]["query"], "Test query")
            self.assertEqual(saved_data[0]["query_type"], "analytical")
            
            # Add another result
            self.reasoner._save_analysis_result(analysis_result)
            
            # Read the file again
            with open(temp_path, "r", encoding="utf-8") as f:
                saved_data = json.load(f)
            
            # Check that there are now two results
            self.assertEqual(len(saved_data), 2)
            
        finally:
            # Clean up
            if os.path.exists(temp_path):
                os.unlink(temp_path)

if __name__ == '__main__':
    unittest.main()
