"""
Unit tests for CoTRAG Vietnamese Embedding Adapter.

This module contains comprehensive unit tests for the CoTRAGVietnameseEmbeddingAdapter class,
testing all features including initialization, processing, and hybrid search.
"""

import unittest
import os
import sys
import numpy as np
from unittest.mock import patch, MagicMock

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))

from src.deep_research_core.reasoning.cotrag_vietnamese_embedding_adapter import CoTRAGVietnameseEmbeddingAdapter
from src.deep_research_core.reasoning.cot_rag import CoTRAG
from src.deep_research_core.multilingual.vietnamese_embeddings import VietnameseEmbeddings

class TestCoTRAGVietnameseEmbeddingAdapter(unittest.TestCase):
    """Test cases for CoTRAGVietnameseEmbeddingAdapter."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create a mock CoTRAG instance
        self.mock_cotrag = MagicMock(spec=CoTRAG)
        
        # Create a mock vector store
        self.mock_vector_store = MagicMock()
        self.mock_cotrag.vector_store = self.mock_vector_store
        
        # Create a mock for VietnameseEmbeddingFactory
        self.embedding_factory_patch = patch('src.deep_research_core.reasoning.cotrag_vietnamese_embedding_adapter.VietnameseEmbeddingFactory')
        self.mock_embedding_factory = self.embedding_factory_patch.start()
        
        # Create a mock embedding model
        self.mock_embedding_model = MagicMock(spec=VietnameseEmbeddings)
        self.mock_embedding_factory.get_embedding_model.return_value = self.mock_embedding_model
        
        # Create a test instance
        self.adapter = CoTRAGVietnameseEmbeddingAdapter(
            cotrag_instance=self.mock_cotrag,
            embedding_model="phobert",
            device="cpu",
            use_hybrid_search=True,
            use_vietnamese_preprocessing=True
        )
    
    def tearDown(self):
        """Tear down test fixtures."""
        # Stop the patches
        self.embedding_factory_patch.stop()
    
    def test_initialization(self):
        """Test initialization of CoTRAGVietnameseEmbeddingAdapter."""
        # Check that the CoTRAG instance is set correctly
        self.assertEqual(self.adapter.cotrag, self.mock_cotrag)
        
        # Check that the embedding model name is set correctly
        self.assertEqual(self.adapter.embedding_model_name, "phobert")
        
        # Check that the use_hybrid_search flag is set correctly
        self.assertTrue(self.adapter.use_hybrid_search)
        
        # Check that the use_vietnamese_preprocessing flag is set correctly
        self.assertTrue(self.adapter.use_vietnamese_preprocessing)
        
        # Check that the embedding model is initialized
        self.assertEqual(self.adapter.vietnamese_embeddings, self.mock_embedding_model)
        
        # Check that the embedding factory was called with the correct parameters
        self.mock_embedding_factory.get_embedding_model.assert_called_once_with(
            model_name="phobert",
            device="cpu",
            cache_dir=None
        )
    
    def test_preprocess_vietnamese_text(self):
        """Test preprocessing of Vietnamese text."""
        # Test with preprocessing enabled
        self.adapter.use_vietnamese_preprocessing = True
        
        # Test with a simple text
        text = "  Xin chào  "
        processed_text = self.adapter.preprocess_vietnamese_text(text)
        
        # Check that the text is stripped
        self.assertEqual(processed_text, "Xin chào")
        
        # Test with preprocessing disabled
        self.adapter.use_vietnamese_preprocessing = False
        
        # Test with the same text
        processed_text = self.adapter.preprocess_vietnamese_text(text)
        
        # Check that the text is not processed
        self.assertEqual(processed_text, text)
    
    def test_process(self):
        """Test processing a Vietnamese query."""
        # Set up the mock embedding model to return a fixed embedding
        mock_embedding = np.array([0.1, 0.2, 0.3])
        self.mock_embedding_model.get_embedding.return_value = mock_embedding
        
        # Set up the mock vector store to return fixed documents
        mock_documents = [{"content": "Document 1"}, {"content": "Document 2"}]
        self.mock_vector_store.search.return_value = mock_documents
        
        # Set up the mock CoTRAG to return a fixed result
        mock_result = {
            "query": "Xin chào",
            "documents": mock_documents,
            "answer": "Chào bạn"
        }
        self.mock_cotrag.process.return_value = mock_result
        
        # Test processing a query
        query = "Xin chào"
        result = self.adapter.process(query)
        
        # Check that the query was preprocessed
        self.mock_embedding_model.get_embedding.assert_called_once_with("Xin chào")
        
        # Check that the vector store was called with the query embedding
        self.mock_vector_store.search.assert_called_once_with(mock_embedding.tolist(), top_k=5)
        
        # Check that the CoTRAG process method was called with the correct parameters
        self.mock_cotrag.process.assert_called_once_with(
            query="Xin chào",
            top_k=5,
            custom_system_prompt=None,
            custom_user_prompt=None,
            callback=None,
            force_refresh=False,
            expected_answer=None
        )
        
        # Check that the result contains the Vietnamese metadata
        self.assertIn("vietnamese", result)
        self.assertEqual(result["vietnamese"]["embedding_model"], "phobert")
        self.assertTrue(result["vietnamese"]["preprocessing"])
        self.assertTrue(result["vietnamese"]["hybrid_search"])
        self.assertIn("processing_time", result["vietnamese"])
    
    def test_hybrid_search(self):
        """Test hybrid search for Vietnamese queries."""
        # Set up the mock vector store to return fixed documents
        mock_semantic_results = [
            {"id": "doc1", "content": "Document 1", "score": 0.9},
            {"id": "doc2", "content": "Document 2", "score": 0.8}
        ]
        self.mock_vector_store.search.return_value = mock_semantic_results
        
        # Set up the mock vector store to return all documents
        mock_all_documents = [
            {"id": "doc1", "content": "Document 1"},
            {"id": "doc2", "content": "Document 2"},
            {"id": "doc3", "content": "Document 3 with query terms"},
            {"id": "doc4", "content": "Document 4"}
        ]
        self.mock_vector_store.get_documents.return_value = mock_all_documents
        
        # Test hybrid search
        query = "query terms"
        query_embedding = [0.1, 0.2, 0.3]
        results = self.adapter._hybrid_search(query, query_embedding)
        
        # Check that the vector store search method was called with the query embedding
        self.mock_vector_store.search.assert_called_once_with(query_embedding, top_k=5)
        
        # Check that the vector store get_documents method was called
        self.mock_vector_store.get_documents.assert_called_once()
        
        # Check that the results include both semantic and lexical results
        self.assertEqual(len(results), 3)  # 2 semantic + 1 lexical
        
        # Check that the semantic results are included
        self.assertEqual(results[0]["id"], "doc1")
        self.assertEqual(results[1]["id"], "doc2")
        
        # Check that the lexical result is included
        self.assertEqual(results[2]["id"], "doc3")

if __name__ == '__main__':
    unittest.main()
