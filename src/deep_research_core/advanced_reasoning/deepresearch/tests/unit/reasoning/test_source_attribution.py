"""
Unit tests for Source Attribution module.
"""

import unittest
from datetime import datetime
from src.deep_research_core.reasoning.source_attribution import SourceAttribution

class TestSourceAttribution(unittest.TestCase):
    """Test cases for SourceAttribution class."""
    
    def setUp(self):
        """Set up test cases."""
        self.attribution = SourceAttribution(
            citation_style="apa",
            track_token_level=True,
            language="en"
        )
        
        # Add some test sources
        self.attribution.register_source(
            "source1",
            {
                "title": "Test Document 1",
                "content": "This is a test document with some content about AI.",
                "author": "<PERSON>",
                "publication_date": "2023",
                "url": "https://example.com/doc1",
                "publisher": "Example Publisher"
            }
        )
        
        self.attribution.register_source(
            "source2",
            {
                "title": "Test Document 2",
                "content": "This is another test document with more details.",
                "author": "<PERSON>",
                "publication_date": "2022",
                "url": "https://example.org/doc2"
            }
        )
    
    def test_register_source(self):
        """Test registering a source."""
        self.attribution.register_source(
            "source3",
            {
                "title": "Test Document 3",
                "content": "This is yet another test document."
            }
        )
        
        self.assertIn("source3", self.attribution.sources)
        self.assertEqual(self.attribution.sources["source3"]["title"], "Test Document 3")
        self.assertEqual(self.attribution.sources["source3"]["author"], "Unknown")
    
    def test_generate_citation_apa(self):
        """Test generating APA citation."""
        citation = self.attribution.generate_citation("source1")
        expected = f"John Doe (2023). Test Document 1. Example Publisher. Retrieved from https://example.com/doc1"
        self.assertEqual(citation, expected)
    
    def test_generate_citation_mla(self):
        """Test generating MLA citation."""
        self.attribution.citation_style = "mla"
        citation = self.attribution.generate_citation("source1")
        self.assertIn("John Doe", citation)
        self.assertIn("Test Document 1", citation)
        self.assertIn("Example Publisher", citation)
        self.assertIn("2023", citation)
    
    def test_track_information_usage(self):
        """Test tracking information usage."""
        content = "AI systems are getting more advanced every day."
        self.attribution.track_information_usage(content, "source1", (0, 10))
        
        self.assertIn("source1", self.attribution.used_sources)
        self.assertIn("AI systems", self.attribution.token_source_map)
    
    def test_add_citations_to_text(self):
        """Test adding citations to text."""
        content = "AI systems are becoming more advanced. Machine learning is a subset of AI."
        
        # Track usage
        self.attribution.track_information_usage(content, "source1", (0, 10))
        self.attribution.track_information_usage(content, "source2", (43, 58))
        
        # Add citations
        result = self.attribution.add_citations_to_text(content)
        
        self.assertIn("AI systems John Doe (2023)", result)
        self.assertIn("Machine learning Jane Smith (2022)", result)
    
    def test_bibliography(self):
        """Test generating bibliography."""
        # Generate citations to add sources to cited_sources
        self.attribution.generate_citation("source1")
        self.attribution.generate_citation("source2")
        
        bibliography = self.attribution.get_bibliography()
        
        self.assertEqual(len(bibliography), 2)
        self.assertIn("John Doe", bibliography[0])
        self.assertIn("Jane Smith", bibliography[1])
    
    def test_citation_metrics(self):
        """Test citation metrics."""
        # Generate citations and track usage
        self.attribution.track_information_usage("Some content", "source1")
        self.attribution.track_information_usage("More content", "source2")
        self.attribution.generate_citation("source1")
        
        metrics = self.attribution.get_citation_metrics()
        
        self.assertEqual(metrics["total_sources"], 2)
        self.assertEqual(metrics["used_sources"], 2)
        self.assertEqual(metrics["cited_sources"], 1)
        self.assertEqual(metrics["citation_ratio"], 0.5)
    
    def test_vietnamese_language(self):
        """Test Vietnamese language support."""
        vi_attribution = SourceAttribution(
            citation_style="apa",
            language="vi"
        )
        
        vi_attribution.register_source(
            "source_vi",
            {
                "title": "Tài liệu Tiếng Việt",
                "content": "Đây là nội dung tiếng Việt.",
                "author": "Nguyễn Văn A",
                "publication_date": "2023",
                "url": "https://example.vn/doc"
            }
        )
        
        citation = vi_attribution.generate_citation("source_vi")
        self.assertIn("Nguyễn Văn A", citation)
        self.assertIn("Tài liệu Tiếng Việt", citation)
        self.assertIn("Truy cập từ", citation)
    
    def test_footnotes(self):
        """Test footnotes functionality."""
        footnote_attribution = SourceAttribution(
            use_footnotes=True
        )
        
        footnote_attribution.register_source(
            "source1",
            {
                "title": "Test Document with Footnotes",
                "content": "This is a test document.",
                "author": "John Doe",
                "publication_date": "2023"
            }
        )
        
        # Generate citation with footnote
        citation = footnote_attribution.generate_citation("source1")
        self.assertEqual(citation, "[1]")
        
        # Check footnote was created
        self.assertEqual(len(footnote_attribution.footnotes), 1)
        self.assertEqual(footnote_attribution.footnotes[0]["index"], 1)
        
        # Test adding footnotes to text
        footnote_attribution.track_token_level = True
        footnote_attribution.track_information_usage("Test content", "source1", (0, 12))
        
        result = footnote_attribution.add_citations_to_text("Test content is from the document.")
        
        self.assertIn("Test content [1]", result)
        self.assertIn("Footnotes:", result)
        self.assertIn("[1] John Doe", result)


if __name__ == "__main__":
    unittest.main() 