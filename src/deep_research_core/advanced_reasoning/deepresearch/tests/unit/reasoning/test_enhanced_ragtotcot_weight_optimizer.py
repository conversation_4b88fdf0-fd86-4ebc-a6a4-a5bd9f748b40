"""
Unit tests for EnhancedRAGTOTCOTWeightOptimizer.

This module contains comprehensive unit tests for the EnhancedRAGTOTCOTWeightOptimizer class,
testing all features including pre-trained weights, momentum-based learning, domain-specific
classification, and performance analysis.
"""

import unittest
from unittest.mock import patch, MagicMock
import sys
import os
import json
import tempfile
import numpy as np
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))

from src.deep_research_core.reasoning.enhanced_ragtotcot_weight_optimizer import EnhancedRAGTOTCOTWeightOptimizer


class TestEnhancedRAGTOTCOTWeightOptimizer(unittest.TestCase):
    """Test cases for EnhancedRAGTOTCOTWeightOptimizer."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary directory for test files
        self.temp_dir = tempfile.TemporaryDirectory()

        # Create a test weights file
        self.test_weights_path = os.path.join(self.temp_dir.name, "test_weights.json")
        test_weights = {
            "factual": {"rag": 0.6, "tot": 0.2, "cot": 0.2},
            "analytical": {"rag": 0.3, "tot": 0.4, "cot": 0.3},
            "test_domain": {"rag": 0.5, "tot": 0.3, "cot": 0.2}
        }
        with open(self.test_weights_path, 'w', encoding='utf-8') as f:
            json.dump(test_weights, f)

        # Initialize EnhancedRAGTOTCOTWeightOptimizer with default settings
        self.optimizer = EnhancedRAGTOTCOTWeightOptimizer(
            default_rag_weight=0.4,
            default_tot_weight=0.3,
            default_cot_weight=0.3,
            min_weight=0.1,
            max_weight=0.7,
            learning_rate=0.05,
            use_historical_data=True,
            max_history_size=100,
            verbose=True,
            use_momentum=True,
            momentum_factor=0.9,
            use_decay=True,
            decay_rate=0.99,
            decay_steps=100,
            use_pretrained_weights=True,
            weights_path=self.test_weights_path,
            domain_specific_classification=True,
            visualization_enabled=True
        )

    def tearDown(self):
        """Tear down test fixtures."""
        # Clean up temporary directory
        self.temp_dir.cleanup()

    def test_initialization(self):
        """Test initialization of EnhancedRAGTOTCOTWeightOptimizer with default parameters."""
        self.assertEqual(self.optimizer.default_weights["rag"], 0.4)
        self.assertEqual(self.optimizer.default_weights["tot"], 0.3)
        self.assertEqual(self.optimizer.default_weights["cot"], 0.3)
        self.assertEqual(self.optimizer.min_weight, 0.1)
        self.assertEqual(self.optimizer.max_weight, 0.7)
        self.assertEqual(self.optimizer.learning_rate, 0.05)
        self.assertTrue(self.optimizer.use_historical_data)
        self.assertEqual(self.optimizer.max_history_size, 100)
        self.assertTrue(self.optimizer.verbose)
        self.assertTrue(self.optimizer.use_momentum)
        self.assertEqual(self.optimizer.momentum_factor, 0.9)
        self.assertTrue(self.optimizer.use_decay)
        self.assertEqual(self.optimizer.decay_rate, 0.99)
        self.assertEqual(self.optimizer.decay_steps, 100)
        self.assertTrue(self.optimizer.use_pretrained_weights)
        self.assertEqual(self.optimizer.weights_path, self.test_weights_path)
        self.assertTrue(self.optimizer.domain_specific_classification)
        self.assertTrue(self.optimizer.visualization_enabled)

    def test_load_pretrained_weights(self):
        """Test loading of pre-trained weights."""
        # Check that weights were loaded from the test file
        self.assertIn("factual", self.optimizer.query_type_weights)
        self.assertIn("analytical", self.optimizer.query_type_weights)
        self.assertIn("test_domain", self.optimizer.query_type_weights)

        # Check specific weight values
        self.assertEqual(self.optimizer.query_type_weights["factual"]["rag"], 0.6)
        self.assertEqual(self.optimizer.query_type_weights["analytical"]["tot"], 0.4)
        self.assertEqual(self.optimizer.query_type_weights["test_domain"]["cot"], 0.2)

        # Check that default pre-trained weights are also loaded
        self.assertIn("creative", self.optimizer.query_type_weights)
        self.assertIn("procedural", self.optimizer.query_type_weights)
        self.assertIn("opinion", self.optimizer.query_type_weights)

    def test_vietnamese_query_examples(self):
        """Test Vietnamese query examples."""
        # Check that Vietnamese examples were loaded
        self.assertIn("medical", self.optimizer.query_type_examples)
        self.assertIn("legal", self.optimizer.query_type_examples)
        self.assertIn("technical", self.optimizer.query_type_examples)
        self.assertIn("educational", self.optimizer.query_type_examples)

        # Check that examples contain Vietnamese text
        for domain in ["medical", "legal", "technical", "educational"]:
            examples = self.optimizer.query_type_examples[domain]
            self.assertTrue(any(self.optimizer._is_vietnamese(example) for example in examples))

    def test_is_vietnamese(self):
        """Test Vietnamese language detection."""
        # Test with Vietnamese text
        self.assertTrue(self.optimizer._is_vietnamese("Trí tuệ nhân tạo là gì?"))
        self.assertTrue(self.optimizer._is_vietnamese("Xin chào thế giới!"))

        # Test with non-Vietnamese text
        self.assertFalse(self.optimizer._is_vietnamese("What is artificial intelligence?"))
        self.assertFalse(self.optimizer._is_vietnamese("Hello world!"))

    def test_classify_query(self):
        """Test query classification."""
        # Test factual query
        factual_query = "Trí tuệ nhân tạo là gì?"
        self.assertEqual(self.optimizer.classify_query(factual_query), "vietnamese")

        # Test analytical query
        analytical_query = "So sánh học máy và học sâu"
        self.assertEqual(self.optimizer.classify_query(analytical_query), "vietnamese")

        # Test English query
        english_query = "What is artificial intelligence?"
        query_type = self.optimizer.classify_query(english_query)
        self.assertIn(query_type, self.optimizer.query_type_weights.keys())

    def test_get_weights_for_query(self):
        """Test getting weights for a query."""
        # Test factual query
        factual_query = "Trí tuệ nhân tạo là gì?"
        factual_weights = self.optimizer.get_weights_for_query(factual_query)
        self.assertIn("rag", factual_weights)
        self.assertIn("tot", factual_weights)
        self.assertIn("cot", factual_weights)
        self.assertAlmostEqual(sum(factual_weights.values()), 1.0)

        # Test analytical query
        analytical_query = "So sánh học máy và học sâu"
        analytical_weights = self.optimizer.get_weights_for_query(analytical_query)
        self.assertIn("rag", analytical_weights)
        self.assertIn("tot", analytical_weights)
        self.assertIn("cot", analytical_weights)
        self.assertAlmostEqual(sum(analytical_weights.values()), 1.0)

    def test_update_weights_from_feedback(self):
        """Test updating weights from feedback."""
        # Test with factual query
        factual_query = "Trí tuệ nhân tạo là gì?"
        feedback = {"rag": 0.8, "tot": 0.6, "cot": 0.7}

        # Get initial weights
        initial_weights = self.optimizer.get_weights_for_query(factual_query)

        # Update weights
        updated_weights = self.optimizer.update_weights_from_feedback(factual_query, feedback)

        # Check that weights were updated
        self.assertNotEqual(updated_weights, initial_weights)
        self.assertIn("rag", updated_weights)
        self.assertIn("tot", updated_weights)
        self.assertIn("cot", updated_weights)
        self.assertAlmostEqual(sum(updated_weights.values()), 1.0)

        # Check that learning rate was decayed
        self.assertLess(self.optimizer.current_learning_rate, self.optimizer.learning_rate)

    def test_momentum_learning(self):
        """Test momentum-based learning."""
        # Test with analytical query
        analytical_query = "So sánh học máy và học sâu"
        query_type = self.optimizer.classify_query(analytical_query)

        # Initialize velocities
        if query_type not in self.optimizer.weight_velocities:
            self.optimizer.weight_velocities[query_type] = {"rag": 0.0, "tot": 0.0, "cot": 0.0}

        # First update
        feedback1 = {"rag": 0.3, "tot": 0.5, "cot": 0.2}
        weights1 = self.optimizer.update_weights_from_feedback(analytical_query, feedback1)

        # Get velocities after first update
        velocities1 = self.optimizer.weight_velocities[query_type].copy()

        # Second update with same feedback
        weights2 = self.optimizer.update_weights_from_feedback(analytical_query, feedback1)

        # Get velocities after second update
        velocities2 = self.optimizer.weight_velocities[query_type].copy()

        # Check that velocities increased (momentum effect)
        for technique in ["rag", "tot", "cot"]:
            if abs(velocities1[technique]) > 0.001:  # Only check if there was a significant velocity
                # With momentum, velocity should increase in the same direction
                if velocities1[technique] > 0:
                    self.assertGreater(velocities2[technique], velocities1[technique])
                else:
                    self.assertLess(velocities2[technique], velocities1[technique])

    def test_learning_rate_decay(self):
        """Test learning rate decay."""
        # Get initial learning rate
        initial_lr = self.optimizer.current_learning_rate

        # Perform multiple updates
        query = "What is machine learning?"
        feedback = {"rag": 0.5, "tot": 0.3, "cot": 0.2}

        for _ in range(10):
            self.optimizer.update_weights_from_feedback(query, feedback)

        # Check that learning rate decreased
        self.assertLess(self.optimizer.current_learning_rate, initial_lr)

    def test_save_weights(self):
        """Test saving weights to file."""
        # Create a path for saving weights
        save_path = os.path.join(self.temp_dir.name, "saved_weights.json")

        # Save weights
        self.optimizer.save_weights(save_path)

        # Check that file was created
        self.assertTrue(os.path.exists(save_path))

        # Load saved weights
        with open(save_path, 'r', encoding='utf-8') as f:
            saved_weights = json.load(f)

        # Check that saved weights match optimizer weights
        for query_type, weights in self.optimizer.query_type_weights.items():
            self.assertIn(query_type, saved_weights)
            for technique, weight in weights.items():
                self.assertIn(technique, saved_weights[query_type])
                self.assertEqual(saved_weights[query_type][technique], weight)

    def test_analyze_historical_performance(self):
        """Test analyzing historical performance."""
        # Add some performance records
        query_type = "factual"
        weights = {"rag": 0.6, "tot": 0.2, "cot": 0.2}
        metrics = {"accuracy": 0.8, "latency": 0.5}

        for i in range(5):
            self.optimizer.record_performance(
                query=f"Test query {i}",
                query_type=query_type,
                weights_used=weights,
                performance_metrics=metrics
            )

        # Analyze performance
        analysis = self.optimizer.analyze_historical_performance()

        # Check analysis results
        self.assertIn(query_type, analysis)
        self.assertEqual(analysis[query_type]["count"], 5)
        self.assertIn("avg_metrics", analysis[query_type])
        self.assertIn("avg_weights", analysis[query_type])
        self.assertIn("recent_weights", analysis[query_type])

    def test_generate_weight_visualization(self):
        """Test generating weight visualization."""
        # Add some performance records
        query_type = "analytical"
        weights1 = {"rag": 0.4, "tot": 0.3, "cot": 0.3}
        weights2 = {"rag": 0.35, "tot": 0.35, "cot": 0.3}
        metrics = {"accuracy": 0.8, "latency": 0.5}

        # Add records with different timestamps
        self.optimizer.record_performance(
            query="Test query 1",
            query_type=query_type,
            weights_used=weights1,
            performance_metrics=metrics
        )

        # Modify the timestamp of the first record to simulate time passing
        self.optimizer.performance_history[0]["timestamp"] -= 3600  # 1 hour ago

        self.optimizer.record_performance(
            query="Test query 2",
            query_type=query_type,
            weights_used=weights2,
            performance_metrics=metrics
        )

        # Generate visualization
        visualization = self.optimizer.generate_weight_visualization(query_type)

        # Check that visualization was generated
        self.assertIsNotNone(visualization)
        self.assertTrue(isinstance(visualization, str))
        self.assertTrue(len(visualization) > 0)

    def test_get_learning_stats(self):
        """Test getting learning statistics."""
        # Perform some updates
        query = "What is deep learning?"
        feedback = {"rag": 0.4, "tot": 0.4, "cot": 0.2}

        for _ in range(3):
            self.optimizer.update_weights_from_feedback(query, feedback)

        # Get learning stats
        stats = self.optimizer.get_learning_stats()

        # Check stats
        self.assertIn("update_count", stats)
        self.assertEqual(stats["update_count"], 3)
        self.assertIn("current_learning_rate", stats)
        self.assertIn("weight_velocities", stats)
        self.assertIn("query_types", stats)
        self.assertIn("performance_history_size", stats)

    def test_optimal_strategy(self):
        """Test getting optimal strategy for a query."""
        # Test with factual query
        factual_query = "Trí tuệ nhân tạo là gì?"
        strategy = self.optimizer.get_optimal_strategy(factual_query)

        # Check strategy
        self.assertIn("use_rag", strategy)
        self.assertIn("use_tot", strategy)
        self.assertIn("use_cot", strategy)
        self.assertIn("weights", strategy)
        self.assertIn("query_type", strategy)

        # Check that weights are included
        self.assertIn("rag", strategy["weights"])
        self.assertIn("tot", strategy["weights"])
        self.assertIn("cot", strategy["weights"])
        self.assertAlmostEqual(sum(strategy["weights"].values()), 1.0)

    def test_reset_learning_rate(self):
        """Test resetting learning rate."""
        # Get initial learning rate
        initial_lr = self.optimizer.current_learning_rate

        # Perform multiple updates to decay learning rate
        query = "What is machine learning?"
        feedback = {"rag": 0.5, "tot": 0.3, "cot": 0.2}

        for _ in range(10):
            self.optimizer.update_weights_from_feedback(query, feedback)

        # Check that learning rate decreased
        self.assertLess(self.optimizer.current_learning_rate, initial_lr)

        # Reset learning rate
        self.optimizer.reset_learning_rate()

        # Check that learning rate was reset
        self.assertEqual(self.optimizer.current_learning_rate, initial_lr)

    def test_classify_query_with_domain(self):
        """Test query classification with domain."""
        # Test medical query
        query_type, domain = self.optimizer.classify_query_with_domain("Triệu chứng của bệnh tiểu đường là gì?")
        self.assertEqual(domain, "medical")

        # Test legal query
        query_type, domain = self.optimizer.classify_query_with_domain("Quy định về thời gian thử việc theo luật lao động")
        self.assertEqual(domain, "legal")

        # Test technical query
        query_type, domain = self.optimizer.classify_query_with_domain("Cách cài đặt mạng neural sử dụng TensorFlow")
        self.assertEqual(domain, "technical")

        # Test educational query
        query_type, domain = self.optimizer.classify_query_with_domain("Phương pháp học tiếng Anh hiệu quả")
        self.assertEqual(domain, "educational")

        # Test query with no specific domain
        query_type, domain = self.optimizer.classify_query_with_domain("What is the weather today?")
        self.assertIsNone(domain)

    def test_add_query_examples(self):
        """Test adding query examples."""
        # Initial count of examples
        query_type = "test_query_type"
        initial_count = len(self.optimizer.query_type_examples.get(query_type, []))

        # Add examples
        new_examples = [
            "Example query 1",
            "Example query 2",
            "Example query 3"
        ]
        self.optimizer.add_query_examples(query_type, new_examples)

        # Check that examples were added
        self.assertIn(query_type, self.optimizer.query_type_examples)
        self.assertEqual(len(self.optimizer.query_type_examples[query_type]), initial_count + len(new_examples))

        # Check that specific examples were added
        for example in new_examples:
            self.assertIn(example, self.optimizer.query_type_examples[query_type])

    def test_add_domain_weights(self):
        """Test adding domain weights."""
        # Add new domain
        domain = "test_domain"
        weights = {
            "factual": {"rag": 0.6, "tot": 0.2, "cot": 0.2},
            "analytical": {"rag": 0.4, "tot": 0.3, "cot": 0.3}
        }
        self.optimizer.add_domain_weights(domain, weights)

        # Check that domain was added
        self.assertIn(domain, self.optimizer.domain_specific_weights)

        # Check specific weight values
        self.assertEqual(self.optimizer.domain_specific_weights[domain]["factual"]["rag"], 0.6)
        self.assertEqual(self.optimizer.domain_specific_weights[domain]["analytical"]["tot"], 0.3)

        # Update existing domain
        updated_weights = {
            "factual": {"rag": 0.7, "tot": 0.1, "cot": 0.2},
            "procedural": {"rag": 0.5, "tot": 0.2, "cot": 0.3}
        }
        self.optimizer.add_domain_weights(domain, updated_weights)

        # Check that weights were updated
        self.assertEqual(self.optimizer.domain_specific_weights[domain]["factual"]["rag"], 0.7)
        self.assertEqual(self.optimizer.domain_specific_weights[domain]["factual"]["tot"], 0.1)

        # Check that new query type was added
        self.assertIn("procedural", self.optimizer.domain_specific_weights[domain])
        self.assertEqual(self.optimizer.domain_specific_weights[domain]["procedural"]["rag"], 0.5)


if __name__ == '__main__':
    unittest.main()
