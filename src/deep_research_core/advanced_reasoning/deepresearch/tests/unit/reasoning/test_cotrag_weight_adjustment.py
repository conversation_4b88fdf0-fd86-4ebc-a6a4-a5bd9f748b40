"""
Unit tests for automatic weight adjustment in CoTRAG.

This module contains tests for the automatic weight adjustment between CoT and RAG
based on query type, document relevance, and user feedback.
"""

import unittest
from unittest.mock import MagicMock, patch, ANY
import sys
import os
import json
import tempfile
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))

from src.deep_research_core.reasoning.cot_rag import CoTRAG
from src.deep_research_core.reasoning.cotrag_weight_optimizer import CoTRAGWeightOptimizer
from src.deep_research_core.reasoning.cotrag_adaptive_learning import CoTRAGAdaptiveLearning


class TestCoTRAGWeightAdjustment(unittest.TestCase):
    """Test case for automatic weight adjustment in CoTRAG."""

    def setUp(self):
        """Set up test fixtures."""
        # Create mock objects
        self.mock_api_provider = MagicMock()
        self.mock_api_provider.generate.return_value = "AI là trí tuệ nhân tạo. Nó là một lĩnh vực của khoa học máy tính."

        self.mock_vector_store = MagicMock()
        self.mock_vector_store.search.return_value = [
            {
                "content": "AI là viết tắt của Artificial Intelligence (Trí tuệ nhân tạo).",
                "score": 0.95
            },
            {
                "content": "Trí tuệ nhân tạo là một nhánh của khoa học máy tính.",
                "score": 0.85
            }
        ]

        # Create patches
        self.patches = [
            patch('src.deep_research_core.models.api.openai.openai_provider.generate',
                  return_value="AI là trí tuệ nhân tạo. Nó là một lĩnh vực của khoa học máy tính."),
            patch('src.deep_research_core.models.api.openrouter.openrouter_provider.generate',
                  return_value="AI là trí tuệ nhân tạo. Nó là một lĩnh vực của khoa học máy tính."),
            patch('src.deep_research_core.models.api.anthropic.anthropic_provider.generate',
                  return_value="AI là trí tuệ nhân tạo. Nó là một lĩnh vực của khoa học máy tính."),
            patch('src.deep_research_core.retrieval.vector_store.get_vector_store',
                  return_value=self.mock_vector_store)
        ]

        # Start patches
        for p in self.patches:
            p.start()

        # Create sample data
        self.sample_queries = {
            "factual": "AI là gì?",
            "reasoning": "Tại sao AI quan trọng trong thời đại số?",
            "complex": "So sánh và phân tích sự khác biệt giữa học máy, học sâu và trí tuệ nhân tạo, đồng thời giải thích cách chúng liên quan đến nhau.",
            "irrelevant": "Làm thế nào để nấu phở bò ngon?",
            "vietnamese": "Trí tuệ nhân tạo có thể giúp gì cho nông nghiệp Việt Nam?"
        }

    def tearDown(self):
        """Clean up after tests."""
        # Stop patches
        for p in self.patches:
            p.stop()

    def test_automatic_weight_adjustment_by_query_type(self):
        """Test automatic weight adjustment based on query type."""
        # Create a real CoTRAGWeightOptimizer
        optimizer = CoTRAGWeightOptimizer(
            min_cot_weight=0.3,
            max_cot_weight=0.8,
            default_cot_weight=0.5,
            strategy="query_type",
            use_adaptive_weights=False
        )

        # Test factual query
        factual_weights = optimizer.get_optimal_weights(self.sample_queries["factual"])
        self.assertEqual(factual_weights["query_type"], "factual")
        self.assertLessEqual(factual_weights["cot_weight"], 0.4)  # Factual queries should rely more on RAG

        # Test reasoning query
        reasoning_weights = optimizer.get_optimal_weights(self.sample_queries["reasoning"])
        self.assertEqual(reasoning_weights["query_type"], "reasoning")
        self.assertGreaterEqual(reasoning_weights["cot_weight"], 0.6)  # Reasoning queries should rely more on CoT

        # Test complex query
        complex_weights = optimizer.get_optimal_weights(self.sample_queries["complex"])
        self.assertEqual(complex_weights["query_type"], "reasoning")
        self.assertGreaterEqual(complex_weights["cot_weight"], 0.6)  # Complex queries should rely more on CoT

    def test_automatic_weight_adjustment_by_document_relevance(self):
        """Test automatic weight adjustment based on document relevance."""
        # Create a real CoTRAGWeightOptimizer
        optimizer = CoTRAGWeightOptimizer(
            min_cot_weight=0.3,
            max_cot_weight=0.8,
            default_cot_weight=0.5,
            strategy="document_relevance",
            use_adaptive_weights=False
        )

        # Test with relevant documents
        relevant_docs = [
            {
                "content": "AI là viết tắt của Artificial Intelligence (Trí tuệ nhân tạo).",
                "score": 0.95
            },
            {
                "content": "Trí tuệ nhân tạo là một nhánh của khoa học máy tính.",
                "score": 0.85
            }
        ]
        
        relevant_weights = optimizer.get_optimal_weights(
            self.sample_queries["factual"], 
            documents=relevant_docs
        )
        
        # With high relevance, should rely more on RAG
        self.assertLessEqual(relevant_weights["cot_weight"], 0.5)
        
        # Test with irrelevant documents
        irrelevant_docs = [
            {
                "content": "Phở là một món ăn truyền thống của Việt Nam.",
                "score": 0.3
            },
            {
                "content": "Cách nấu phở bò ngon nhất là dùng xương bò hầm trong 8 giờ.",
                "score": 0.25
            }
        ]
        
        irrelevant_weights = optimizer.get_optimal_weights(
            self.sample_queries["factual"], 
            documents=irrelevant_docs
        )
        
        # With low relevance, should rely more on CoT
        self.assertGreaterEqual(irrelevant_weights["cot_weight"], 0.6)

    def test_adaptive_weight_adjustment_with_feedback(self):
        """Test adaptive weight adjustment based on user feedback."""
        # Create a temporary file for feedback history
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_path = temp_file.name

        # Create CoTRAGAdaptiveLearning
        cotrag_adaptive = CoTRAGAdaptiveLearning(
            provider="openai",
            model="gpt-4o",
            temperature=0.7,
            max_tokens=2000,
            feedback_history_path=temp_path,
            min_cot_weight=0.3,
            max_cot_weight=0.8,
            default_cot_weight=0.5,
            weighting_strategy="auto",
            learning_rate=0.1  # Higher learning rate for faster adaptation
        )

        # Mock components
        cotrag_adaptive.api_provider = self.mock_api_provider
        cotrag_adaptive.vector_store = self.mock_vector_store

        # Process a query
        result1 = cotrag_adaptive.process(self.sample_queries["factual"])
        initial_weight = result1["weights"]["cot_weight"]

        # Add feedback indicating that CoT should be weighted more
        cotrag_adaptive.add_feedback(
            query=self.sample_queries["factual"],
            result=result1,
            feedback_score=0.5,  # Medium score
            feedback_type="user",
            feedback_notes="Need more reasoning, less reliance on documents."
        )

        # Process the same query again
        result2 = cotrag_adaptive.process(self.sample_queries["factual"])
        
        # The weight should have been adjusted based on feedback
        self.assertNotEqual(result2["weights"]["cot_weight"], initial_weight)
        
        # Clean up
        os.unlink(temp_path)

    def test_weight_adjustment_integration_with_cotrag(self):
        """Test integration of weight adjustment with CoTRAG."""
        # Create CoTRAG with dynamic weighting
        cotrag = CoTRAG(
            provider="openai",
            model="gpt-4o",
            temperature=0.7,
            max_tokens=2000,
            use_dynamic_weighting=True,
            min_cot_weight=0.3,
            max_cot_weight=0.8,
            default_cot_weight=0.5,
            weighting_strategy="auto"
        )

        # Mock components
        cotrag.api_provider = self.mock_api_provider
        cotrag.vector_store = self.mock_vector_store

        # Process different types of queries
        factual_result = cotrag.process(self.sample_queries["factual"])
        reasoning_result = cotrag.process(self.sample_queries["reasoning"])
        complex_result = cotrag.process(self.sample_queries["complex"])

        # Verify that weights are different for different query types
        self.assertIn("weights", factual_result)
        self.assertIn("weights", reasoning_result)
        self.assertIn("weights", complex_result)

        # Factual queries should rely more on RAG
        self.assertLessEqual(factual_result["weights"]["cot_weight"], 0.5)
        
        # Reasoning and complex queries should rely more on CoT
        self.assertGreaterEqual(reasoning_result["weights"]["cot_weight"], 0.5)
        self.assertGreaterEqual(complex_result["weights"]["cot_weight"], 0.5)

    def test_weight_adjustment_with_irrelevant_documents(self):
        """Test weight adjustment when documents are irrelevant."""
        # Create CoTRAG with dynamic weighting
        cotrag = CoTRAG(
            provider="openai",
            model="gpt-4o",
            temperature=0.7,
            max_tokens=2000,
            use_dynamic_weighting=True,
            handle_irrelevant_docs=True,
            relevance_threshold=0.5,
            min_cot_weight=0.3,
            max_cot_weight=0.8,
            default_cot_weight=0.5,
            weighting_strategy="document_relevance"
        )

        # Mock components
        cotrag.api_provider = self.mock_api_provider
        
        # Mock vector store to return irrelevant documents
        mock_irrelevant_store = MagicMock()
        mock_irrelevant_store.search.return_value = [
            {
                "content": "Phở là một món ăn truyền thống của Việt Nam.",
                "score": 0.3
            },
            {
                "content": "Cách nấu phở bò ngon nhất là dùng xương bò hầm trong 8 giờ.",
                "score": 0.25
            }
        ]
        cotrag.vector_store = mock_irrelevant_store

        # Process a query
        result = cotrag.process(self.sample_queries["factual"])

        # Verify that weights are adjusted for irrelevant documents
        self.assertIn("weights", result)
        self.assertGreaterEqual(result["weights"]["cot_weight"], 0.6)  # Should rely more on CoT
        self.assertIn("low_relevance_documents", result)  # Should indicate low relevance


if __name__ == "__main__":
    unittest.main()
