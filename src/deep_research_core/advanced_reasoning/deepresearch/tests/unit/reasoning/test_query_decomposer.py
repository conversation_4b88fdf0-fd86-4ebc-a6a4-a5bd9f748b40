"""
Unit tests for the QueryDecomposer class.
"""

import unittest
from unittest.mock import patch, MagicMock

from src.deep_research_core.reasoning.query_decomposer import QueryDecomposer

class TestQueryDecomposer(unittest.TestCase):
    """Test the QueryDecomposer class."""

    def setUp(self):
        """Set up test environment."""
        self.decomposer = QueryDecomposer(
            provider="openrouter",
            model="moonshotai/moonlight-16b-a3b-instruct:free",
            language="en",
            use_cache=False
        )

    def test_initialization(self):
        """Test initialization of QueryDecomposer."""
        self.assertEqual(self.decomposer.provider, "openrouter")
        self.assertEqual(self.decomposer.model, "moonshotai/moonlight-16b-a3b-instruct:free")
        self.assertEqual(self.decomposer.language, "en")
        self.assertFalse(self.decomposer.use_cache)

    def test_is_complex_query(self):
        """Test detection of complex queries."""
        # Test long query
        long_query = "This is a very long query that has more than thirty words and it needs to be really long to trigger the length detection in the _is_complex_query method so I am adding more words to make sure it exceeds the threshold. Adding even more words to be absolutely certain."
        self.assertTrue(self.decomposer._is_complex_query(long_query))

        # Test multiple questions
        multi_question = "What is the capital of France? What is the population of Paris?"
        self.assertTrue(self.decomposer._is_complex_query(multi_question))

        # Test complex markers
        complex_query = "Explain the theory of relativity and its implications."
        self.assertTrue(self.decomposer._is_complex_query(complex_query))

        # Test conjunctions
        conjunction_query = "Tell me about quantum physics and its applications."
        self.assertTrue(self.decomposer._is_complex_query(conjunction_query))

        # Test simple query
        simple_query = "What is the capital of France?"
        self.assertFalse(self.decomposer._is_complex_query(simple_query))

    @patch('src.deep_research_core.models.api.openrouter.provider.OpenRouterProvider.generate')
    def test_decompose_simple_query(self, mock_generate):
        """Test decomposition of a simple query."""
        query = "What is the capital of France?"

        # Call the decompose method
        result = self.decomposer.decompose(query)

        # Check that the API was not called
        mock_generate.assert_not_called()

        # Check the result
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["query"], query)
        self.assertTrue(result[0]["is_original"])
        self.assertEqual(result[0]["complexity"], "simple")

    @patch('src.deep_research_core.models.api.openrouter.provider.OpenRouterProvider.generate')
    def test_decompose_complex_query(self, mock_generate):
        """Test decomposition of a complex query."""
        query = "Compare and contrast the economic systems of the United States and China, and explain how they have evolved over the past 50 years."

        # Mock the API response
        mock_generate.return_value = """
        [
            {
                "query": "What are the key characteristics of the economic system in the United States?",
                "focus": "US Economic System",
                "complexity": "medium",
                "order": 1
            },
            {
                "query": "What are the key characteristics of the economic system in China?",
                "focus": "China Economic System",
                "complexity": "medium",
                "order": 2
            },
            {
                "query": "How has the US economic system evolved over the past 50 years?",
                "focus": "US Economic Evolution",
                "complexity": "medium",
                "order": 3
            },
            {
                "query": "How has China's economic system evolved over the past 50 years?",
                "focus": "China Economic Evolution",
                "complexity": "medium",
                "order": 4
            },
            {
                "query": "What are the main similarities and differences between the economic systems of the US and China?",
                "focus": "Comparison",
                "complexity": "complex",
                "order": 5
            }
        ]
        """

        # Call the decompose method
        result = self.decomposer.decompose(query)

        # Check that the API was called
        mock_generate.assert_called_once()

        # Check the result
        self.assertEqual(len(result), 5)
        self.assertEqual(result[0]["query"], "What are the key characteristics of the economic system in the United States?")
        self.assertEqual(result[0]["focus"], "US Economic System")
        self.assertEqual(result[0]["complexity"], "medium")
        self.assertEqual(result[0]["order"], 1)
        self.assertFalse(result[0]["is_original"])

    @patch('src.deep_research_core.models.api.openrouter.provider.OpenRouterProvider.generate')
    def test_decompose_vietnamese_query(self, mock_generate):
        """Test decomposition of a Vietnamese query."""
        query = "So sánh và đối chiếu hệ thống kinh tế của Hoa Kỳ và Trung Quốc, và giải thích cách chúng phát triển trong 50 năm qua."

        # Set language to Vietnamese
        self.decomposer.language = "vi"

        # Mock the API response
        mock_generate.return_value = """
        [
            {
                "query": "Hệ thống kinh tế của Hoa Kỳ có những đặc điểm chính nào?",
                "focus": "Hệ thống kinh tế Hoa Kỳ",
                "complexity": "medium",
                "order": 1
            },
            {
                "query": "Hệ thống kinh tế của Trung Quốc có những đặc điểm chính nào?",
                "focus": "Hệ thống kinh tế Trung Quốc",
                "complexity": "medium",
                "order": 2
            },
            {
                "query": "Hệ thống kinh tế Hoa Kỳ đã phát triển như thế nào trong 50 năm qua?",
                "focus": "Sự phát triển kinh tế Hoa Kỳ",
                "complexity": "medium",
                "order": 3
            },
            {
                "query": "Hệ thống kinh tế Trung Quốc đã phát triển như thế nào trong 50 năm qua?",
                "focus": "Sự phát triển kinh tế Trung Quốc",
                "complexity": "medium",
                "order": 4
            },
            {
                "query": "Hệ thống kinh tế của Hoa Kỳ và Trung Quốc có những điểm giống và khác nhau chính nào?",
                "focus": "So sánh",
                "complexity": "complex",
                "order": 5
            }
        ]
        """

        # Call the decompose method
        result = self.decomposer.decompose(query)

        # Check that the API was called
        mock_generate.assert_called_once()

        # Check the result
        self.assertEqual(len(result), 5)
        self.assertEqual(result[0]["query"], "Hệ thống kinh tế của Hoa Kỳ có những đặc điểm chính nào?")
        self.assertEqual(result[0]["focus"], "Hệ thống kinh tế Hoa Kỳ")
        self.assertEqual(result[0]["complexity"], "medium")
        self.assertEqual(result[0]["order"], 1)
        self.assertFalse(result[0]["is_original"])

    @patch('src.deep_research_core.models.api.openrouter.provider.OpenRouterProvider.generate')
    def test_decompose_with_api_error(self, mock_generate):
        """Test decomposition when API returns an error."""
        query = "Compare and contrast the economic systems of the United States and China."

        # Mock the API to raise an exception
        mock_generate.side_effect = Exception("API error")

        # Call the decompose method
        result = self.decomposer.decompose(query)

        # Check that the API was called
        mock_generate.assert_called_once()

        # Check the result (should return original query)
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["query"], query)
        self.assertTrue(result[0]["is_original"])
        self.assertEqual(result[0]["complexity"], "unknown")

    @patch('src.deep_research_core.models.api.openrouter.provider.OpenRouterProvider.generate')
    def test_decompose_with_invalid_json(self, mock_generate):
        """Test decomposition when API returns invalid JSON."""
        query = "Compare and contrast the economic systems of the United States and China."

        # Mock the API to return invalid JSON
        mock_generate.return_value = "This is not valid JSON"

        # Call the decompose method
        result = self.decomposer.decompose(query)

        # Check that the API was called
        mock_generate.assert_called_once()

        # Check the result (should return original query)
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["query"], query)
        self.assertTrue(result[0]["is_original"])
        self.assertEqual(result[0]["complexity"], "complex")

if __name__ == '__main__':
    unittest.main()
