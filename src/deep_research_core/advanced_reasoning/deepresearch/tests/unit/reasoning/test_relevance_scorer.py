"""
Unit tests for the RelevanceScorer class.
"""

import os
import json
import tempfile
import unittest
from unittest.mock import MagicMock, patch

import torch
import numpy as np

from deep_research_core.reasoning.relevance_scorer import RelevanceScorer


class TestRelevanceScorer(unittest.TestCase):
    """Test cases for the RelevanceScorer class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Sample documents for testing
        self.test_documents = [
            {
                "id": "doc1",
                "content": "This is a document about artificial intelligence and machine learning techniques.",
                "title": "AI Fundamentals",
                "original_rank": 3
            },
            {
                "id": "doc2",
                "content": "Deep learning is a subset of machine learning focused on neural networks.",
                "title": "Deep Learning Overview",
                "original_rank": 1
            },
            {
                "id": "doc3",
                "content": "Natural language processing enables computers to understand human language.",
                "title": "Introduction to NLP",
                "original_rank": 2
            },
            {
                "id": "doc4",
                "content": "This document is completely unrelated to the query and talks about gardening.",
                "title": "Gardening Tips",
                "original_rank": 4
            }
        ]
        
        # Create a scorer with mocked SentenceTransformer
        with patch('deep_research_core.reasoning.relevance_scorer.SentenceTransformer') as mock_st:
            mock_st.return_value = MagicMock()
            self.scorer = RelevanceScorer(
                model_name="test-model",
                config={"cache_embeddings": False}
            )
        
        # Mock the _get_embedding method
        self.scorer._get_embedding = MagicMock()
        self.scorer._get_embedding.return_value = torch.tensor([0.5, 0.5])
    
    def test_init(self):
        """Test initialization."""
        scorer = RelevanceScorer(model_name="test-model")
        self.assertEqual(scorer.model_name, "test-model")
        self.assertIn("semantic_similarity", scorer.scoring_methods)
        self.assertIn("keyword_matching", scorer.scoring_methods)
        self.assertIn("cache_embeddings", scorer.config)
    
    def test_get_document_text(self):
        """Test extracting text from documents."""
        # Test with content field
        doc1 = {"content": "This is content"}
        self.assertEqual(self.scorer._get_document_text(doc1), "This is content")
        
        # Test with text field
        doc2 = {"text": "This is text"}
        self.assertEqual(self.scorer._get_document_text(doc2), "This is text")
        
        # Test with title and content
        doc3 = {"title": "Title", "content": "Content"}
        self.assertEqual(self.scorer._get_document_text(doc3), "Title Content")
        
        # Test with other structure
        doc4 = {"other_field": "value"}
        self.assertEqual(self.scorer._get_document_text(doc4), '{"other_field": "value"}')
    
    def test_simple_text_similarity(self):
        """Test simple text similarity calculation."""
        text1 = "machine learning artificial intelligence"
        text2 = "artificial intelligence and machine learning"
        text3 = "completely different text"
        
        # Similar texts
        similarity1 = self.scorer._simple_text_similarity(text1, text2)
        self.assertGreater(similarity1, 0.5)
        
        # Different texts
        similarity2 = self.scorer._simple_text_similarity(text1, text3)
        self.assertLess(similarity2, 0.2)
        
        # Empty text
        self.assertEqual(self.scorer._simple_text_similarity("", ""), 0.0)
    
    def test_extract_simple_keywords(self):
        """Test simple keyword extraction."""
        text = "The artificial intelligence and machine learning"
        keywords = self.scorer._extract_simple_keywords(text)
        
        self.assertIn("artificial", keywords)
        self.assertIn("intelligence", keywords)
        self.assertIn("machine", keywords)
        self.assertIn("learning", keywords)
        self.assertNotIn("the", keywords)
        self.assertNotIn("and", keywords)
    
    @patch('deep_research_core.reasoning.relevance_scorer.util.cos_sim')
    def test_semantic_similarity_score(self, mock_cos_sim):
        """Test semantic similarity scoring."""
        # Mock cosine similarity result
        mock_cos_sim.return_value = torch.tensor([0.8])
        
        query = "machine learning"
        doc = {"content": "This is about machine learning algorithms"}
        
        # Test with model available
        self.scorer.model = MagicMock()
        score = self.scorer._semantic_similarity_score(query, doc)
        
        # Scale from [-1, 1] to [0, 1]: (0.8 + 1) / 2 = 0.9
        self.assertAlmostEqual(score, 0.9, places=1)
        
        # Test with model exception
        self.scorer._get_embedding.side_effect = Exception("Test error")
        score = self.scorer._semantic_similarity_score(query, doc)
        self.assertEqual(score, 0.5)  # Default error score
        
        # Test with no model
        self.scorer.model = None
        self.scorer._get_embedding.side_effect = None
        score = self.scorer._semantic_similarity_score(query, doc)
        # Should fall back to simple text similarity
        self.assertGreaterEqual(score, 0.0)
        self.assertLessEqual(score, 1.0)
    
    def test_keyword_matching_score(self):
        """Test keyword matching scoring."""
        query = "machine learning algorithms"
        
        # Document with matching keywords
        doc1 = {"content": "This is about advanced machine learning algorithms"}
        score1 = self.scorer._keyword_matching_score(query, doc1)
        
        # Document with fewer matching keywords
        doc2 = {"content": "This is about algorithms but not learning"}
        score2 = self.scorer._keyword_matching_score(query, doc2)
        
        # Document with no matching keywords
        doc3 = {"content": "This is completely unrelated"}
        score3 = self.scorer._keyword_matching_score(query, doc3)
        
        self.assertGreater(score1, score2)
        self.assertGreater(score2, score3)
        self.assertEqual(score3, 0.0)
        
        # Test with keywords disabled
        old_config = self.scorer.config["use_keywords"]
        self.scorer.config["use_keywords"] = False
        score4 = self.scorer._keyword_matching_score(query, doc1)
        self.assertEqual(score4, 0.5)  # Neutral score
        self.scorer.config["use_keywords"] = old_config
    
    def test_position_bias_score(self):
        """Test position bias scoring."""
        # Document with original rank
        doc1 = {"original_rank": 1}
        score1 = self.scorer._position_bias_score(doc1)
        
        # Document with higher (worse) rank
        doc2 = {"original_rank": 10}
        score2 = self.scorer._position_bias_score(doc2)
        
        # Document with index instead of rank
        doc3 = {"index": 0}
        score3 = self.scorer._position_bias_score(doc3)
        
        # Document with no position info
        doc4 = {"content": "No position info"}
        score4 = self.scorer._position_bias_score(doc4)
        
        self.assertGreater(score1, score2)  # Lower rank (better) gets higher score
        self.assertGreaterEqual(score3, 0.0)
        self.assertEqual(score4, 0.5)  # Default neutral score
    
    def test_length_normalization_score(self):
        """Test length normalization scoring."""
        # Very short document
        doc1 = {"content": "Short"}
        score1 = self.scorer._length_normalization_score(doc1)
        
        # Ideal length document
        doc2 = {"content": "x" * 500}
        score2 = self.scorer._length_normalization_score(doc2)
        
        # Very long document
        doc3 = {"content": "x" * 5000}
        score3 = self.scorer._length_normalization_score(doc3)
        
        self.assertLess(score1, score2)  # Short doc penalized
        self.assertEqual(score2, 1.0)  # Ideal length gets maximum score
        self.assertLess(score3, score2)  # Long doc penalized but not too much
    
    def test_metadata_boost_score(self):
        """Test metadata boosting scoring."""
        query = "machine learning"
        
        # Document with matching title
        doc1 = {"title": "Introduction to Machine Learning"}
        score1 = self.scorer._metadata_boost_score(query, doc1)
        
        # Document with matching category
        doc2 = {"category": "Artificial Intelligence and Machine Learning"}
        score2 = self.scorer._metadata_boost_score(query, doc2)
        
        # Document with matching tags
        doc3 = {"tags": ["AI", "machine learning", "algorithms"]}
        score3 = self.scorer._metadata_boost_score(query, doc3)
        
        # Document with existing score
        doc4 = {"score": 0.95}
        score4 = self.scorer._metadata_boost_score(query, doc4)
        
        self.assertGreater(score1, 0.5)
        self.assertGreaterEqual(score2, 0.8)
        self.assertGreaterEqual(score3, 0.9)
        self.assertEqual(score4, 0.95)
    
    def test_score(self):
        """Test document scoring and re-ranking."""
        query = "machine learning"
        
        # Mock semantic scores to get predictable results
        def mock_calculate_score(q, doc):
            if "machine" in doc.get("content", "").lower():
                return 0.9
            elif "learning" in doc.get("content", "").lower():
                return 0.7
            return 0.2
        
        self.scorer._calculate_relevance_score = MagicMock(side_effect=mock_calculate_score)
        
        # Test regular scoring
        reranked_docs = self.scorer.score(query, self.test_documents)
        
        # Doc2 should be first (has both machine and learning)
        self.assertEqual(reranked_docs[0]["id"], "doc2")
        
        # Test with score return
        reranked_docs, scores = self.scorer.score(query, self.test_documents, return_scores=True)
        self.assertEqual(len(reranked_docs), len(scores))
        
        # Test with minimum score filtering
        old_min_score = self.scorer.config["minimum_score"]
        self.scorer.config["minimum_score"] = 0.8
        reranked_docs = self.scorer.score(query, self.test_documents)
        # Only docs with machine (score 0.9) should remain
        for doc in reranked_docs[:len(self.test_documents)-2]:  # Exclude docs_rest
            self.assertIn("machine", doc["content"].lower())
        self.scorer.config["minimum_score"] = old_min_score
    
    def test_batch_score(self):
        """Test batch document scoring."""
        queries = ["machine learning", "natural language processing"]
        doc_lists = [self.test_documents[:2], self.test_documents[2:]]
        
        # Mock score method
        self.scorer.score = MagicMock(return_value=self.test_documents[:1])
        
        # Test batch scoring
        results = self.scorer.batch_score(queries, doc_lists)
        self.assertEqual(len(results), len(queries))
        self.assertEqual(self.scorer.score.call_count, 2)
        
        # Test with mismatched lengths
        with self.assertRaises(ValueError):
            self.scorer.batch_score(queries, [self.test_documents])
    
    def test_config_serialization(self):
        """Test saving and loading configuration."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = os.path.join(temp_dir, "config.json")
            
            # Save configuration
            self.scorer.save_config(config_path)
            
            # Check file exists
            self.assertTrue(os.path.exists(config_path))
            
            # Check content
            with open(config_path, "r") as f:
                saved_config = json.load(f)
            
            self.assertEqual(saved_config["model_name"], "test-model")
            self.assertIn("scoring_methods", saved_config)
            self.assertIn("config", saved_config)
            
            # Test class method from_config
            with patch('deep_research_core.reasoning.relevance_scorer.RelevanceScorer.__init__') as mock_init:
                mock_init.return_value = None
                RelevanceScorer.from_config(config_path)
                mock_init.assert_called_once()
                # Check correct arguments are passed
                args = mock_init.call_args[1]
                self.assertEqual(args["model_name"], "test-model")


if __name__ == "__main__":
    unittest.main() 