"""
Unit tests for the EnhancedCoTRAG implementation.

This module contains tests for the Enhanced Chain of Thought RAG implementation.
"""

import unittest
from unittest.mock import MagicMock, patch

from src.deep_research_core.reasoning.enhanced_cotrag import EnhancedCoTRAG


class TestEnhancedCoTRAG(unittest.TestCase):
    """Test case for the EnhancedCoTRAG implementation."""
    
    def setUp(self):
        """Set up the test case."""
        # Create mock RAG instance
        self.mock_rag = MagicMock()
        self.mock_rag.process.return_value = {
            "answer": "This is a test answer.",
            "documents": [
                {
                    "content": "This is a test document.",
                    "source": "Test Source",
                    "title": "Test Document",
                    "score": 0.95
                },
                {
                    "content": "This is another test document.",
                    "source": "Test Source 2",
                    "title": "Test Document 2",
                    "score": 0.85
                }
            ],
            "query": "Test query",
            "model": "gpt-4o",
            "provider": "openai"
        }
        
        # Create mock API provider
        self.mock_api_provider = MagicMock()
        self.mock_api_provider.complete.return_value = "This is a test answer with reasoning."
        
        # Patch the API provider factory
        self.patcher = patch("src.deep_research_core.reasoning.enhanced_cotrag.openai_provider")
        self.mock_provider_factory = self.patcher.start()
        self.mock_provider_factory.return_value = self.mock_api_provider
        
        # Initialize EnhancedCoTRAG
        self.enhanced_cot_rag = EnhancedCoTRAG(
            rag_instance=self.mock_rag,
            provider="openai",
            model="gpt-4o",
            temperature=0.7,
            max_tokens=2000,
            language="en",
            use_hybrid_search=True,
            use_reranking=True,
            use_query_expansion=True,
            verbose=True
        )
    
    def tearDown(self):
        """Clean up after the test."""
        # Stop the patcher
        self.patcher.stop()
    
    def test_initialization(self):
        """Test initialization of EnhancedCoTRAG."""
        self.assertEqual(self.enhanced_cot_rag.rag_instance, self.mock_rag)
        self.assertEqual(self.enhanced_cot_rag.provider, "openai")
        self.assertEqual(self.enhanced_cot_rag.model, "gpt-4o")
        self.assertEqual(self.enhanced_cot_rag.temperature, 0.7)
        self.assertEqual(self.enhanced_cot_rag.max_tokens, 2000)
        self.assertEqual(self.enhanced_cot_rag.language, "en")
        self.assertTrue(self.enhanced_cot_rag.use_hybrid_search)
        self.assertTrue(self.enhanced_cot_rag.use_reranking)
        self.assertTrue(self.enhanced_cot_rag.use_query_expansion)
        self.assertTrue(self.enhanced_cot_rag.verbose)
        self.assertEqual(self.enhanced_cot_rag.api_provider, self.mock_api_provider)
    
    def test_process(self):
        """Test processing a query."""
        # Process a query
        result = self.enhanced_cot_rag.process("Test query")
        
        # Check that the RAG instance was called
        self.mock_rag.process.assert_called_once()
        
        # Check that the API provider was called
        self.mock_api_provider.complete.assert_called_once()
        
        # Check the result
        self.assertEqual(result["query"], "Test query")
        self.assertEqual(result["answer"], "This is a test answer with reasoning.")
        self.assertEqual(result["documents"], self.mock_rag.process.return_value["documents"])
        self.assertEqual(result["model"], "gpt-4o")
        self.assertEqual(result["provider"], "openai")
        self.assertIn("cot_prompt", result)
        self.assertIn("latency", result)
        self.assertIn("enhanced_features", result)
        self.assertIn("hybrid_search", result["enhanced_features"])
        self.assertIn("reranking", result["enhanced_features"])
        self.assertIn("query_expansion", result["enhanced_features"])
    
    def test_process_with_callback(self):
        """Test processing a query with a callback function."""
        # Create a callback function
        callback_results = []
        def callback(content):
            callback_results.append(content)
        
        # Process a query with the callback
        result = self.enhanced_cot_rag.process("Test query", callback=callback)
        
        # Check that the API provider was called with the callback
        call_args = self.mock_api_provider.complete.call_args[1]
        self.assertTrue(call_args["stream"])
        self.assertEqual(call_args["callback"], callback)
    
    def test_process_with_custom_prompts(self):
        """Test processing a query with custom prompts."""
        # Custom prompts
        custom_system_prompt = "You are a custom assistant..."
        custom_user_prompt = "Custom question: {query}\n{documents}"
        
        # Process a query with custom prompts
        result = self.enhanced_cot_rag.process(
            "Test query",
            custom_system_prompt=custom_system_prompt,
            custom_user_prompt=custom_user_prompt
        )
        
        # Check that the API provider was called with the custom prompts
        call_args = self.mock_api_provider.complete.call_args[1]
        self.assertEqual(call_args["system_prompt"], custom_system_prompt)
        self.assertIn("Custom question: Test query", call_args["user_prompt"])
    
    def test_expand_query(self):
        """Test query expansion."""
        # Patch the API provider's complete method
        self.mock_api_provider.complete.return_value = "Original query: Test query\nExpanded query: Test query about something specific"
        
        # Expand a query
        expanded_query = self.enhanced_cot_rag._expand_query("Test query")
        
        # Check the expanded query
        self.assertEqual(expanded_query, "Test query about something specific")
        
        # Check that the API provider was called
        self.mock_api_provider.complete.assert_called_once()
    
    def test_rerank_documents(self):
        """Test document reranking."""
        # Sample documents
        documents = [
            {
                "content": "This is a test document.",
                "source": "Test Source",
                "title": "Test Document",
                "score": 0.95
            },
            {
                "content": "This is another test document.",
                "source": "Test Source 2",
                "title": "Test Document 2",
                "score": 0.85
            }
        ]
        
        # Patch the API provider's complete method
        self.mock_api_provider.complete.return_value = """
        Document 1: 0.8
        Document 2: 0.9
        """
        
        # Rerank the documents
        reranked_docs = self.enhanced_cot_rag._rerank_documents("Test query", documents)
        
        # Check the reranked documents
        self.assertEqual(len(reranked_docs), 2)
        self.assertEqual(reranked_docs[0]["content"], "This is another test document.")
        self.assertEqual(reranked_docs[0]["score"], 0.9)
        self.assertEqual(reranked_docs[1]["content"], "This is a test document.")
        self.assertEqual(reranked_docs[1]["score"], 0.8)
        
        # Check that the API provider was called
        self.mock_api_provider.complete.assert_called_once()
    
    def test_create_enhanced_cot_prompt(self):
        """Test creating an enhanced Chain of Thought prompt."""
        # Create a prompt
        prompt = self.enhanced_cot_rag._create_enhanced_cot_prompt(
            "Test query",
            [
                {
                    "content": "This is a test document.",
                    "source": "Test Source",
                    "title": "Test Document",
                    "score": 0.95
                }
            ]
        )
        
        # Check the prompt
        self.assertIn("Test query", prompt)
        self.assertIn("This is a test document.", prompt)
        self.assertIn("Let's think step by step", prompt)
        self.assertIn("Relevance score: 0.95", prompt)
    
    def test_error_handling(self):
        """Test error handling in EnhancedCoTRAG."""
        # Make the RAG instance raise an exception
        self.mock_rag.process.side_effect = Exception("Test error")
        
        # Process a query
        result = self.enhanced_cot_rag.process("Test query")
        
        # Check the result
        self.assertEqual(result["query"], "Test query")
        self.assertIn("error", result)
        self.assertIn("Test error", result["error"])
    
    def test_with_different_language(self):
        """Test using a different language."""
        # Initialize EnhancedCoTRAG with Vietnamese
        enhanced_cot_rag = EnhancedCoTRAG(
            rag_instance=self.mock_rag,
            provider="openai",
            model="gpt-4o",
            language="vi",
            use_hybrid_search=True,
            use_reranking=True,
            use_query_expansion=True
        )
        
        # Process a query
        result = enhanced_cot_rag.process("Câu hỏi thử nghiệm")
        
        # Check the result
        self.assertEqual(result["query"], "Câu hỏi thử nghiệm")
        self.assertEqual(result["language"], "vi")
        
        # Check that the API provider was called with Vietnamese prompts
        call_args = self.mock_api_provider.complete.call_args[1]
        self.assertIn("Hãy suy nghĩ từng bước", call_args["user_prompt"])
    
    def test_without_enhanced_features(self):
        """Test without enhanced features."""
        # Initialize EnhancedCoTRAG without enhanced features
        enhanced_cot_rag = EnhancedCoTRAG(
            rag_instance=self.mock_rag,
            provider="openai",
            model="gpt-4o",
            use_hybrid_search=False,
            use_reranking=False,
            use_query_expansion=False
        )
        
        # Process a query
        result = enhanced_cot_rag.process("Test query")
        
        # Check the result
        self.assertEqual(result["enhanced_features"]["hybrid_search"], False)
        self.assertEqual(result["enhanced_features"]["reranking"], False)
        self.assertEqual(result["enhanced_features"]["query_expansion"], False)
        
        # Check that the RAG instance was called with the original query
        self.mock_rag.process.assert_called_once_with(
            "Test query",
            filter_expr=None,
            hybrid_weight=None,
            callback=None
        )


if __name__ == "__main__":
    unittest.main()
