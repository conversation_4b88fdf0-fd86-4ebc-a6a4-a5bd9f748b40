"""
Unit tests for Multi-source Validation module.
"""

import unittest
import json
from unittest.mock import patch, MagicMock
from src.deep_research_core.reasoning.multi_source_validator import MultiSourceValidator

class TestMultiSourceValidator(unittest.TestCase):
    """Test cases for MultiSourceValidator class."""
    
    def setUp(self):
        """Set up test cases."""
        # Create a mock api_provider for testing without real API calls
        self.mock_provider = MagicMock()
        self.mock_provider.get_default_model.return_value = "gpt-4o"
        
        # Sample validation response
        self.mock_provider.generate.return_value = json.dumps({
            "consistency_score": 0.8,
            "support_score": 0.7,
            "inconsistencies": ["Minor date discrepancy in Source 2"],
            "supporting_evidence": ["Both sources confirm the main event", "Details are consistent in Source 1 and Source 3"],
            "contradicting_evidence": ["Source 2 mentions a different location"],
            "credible_sources": ["source1", "source3"],
            "uncertain_aspects": ["Exact time of the event"]
        })
        
        # Create validator instance with mocked provider
        with patch('src.deep_research_core.reasoning.multi_source_validator.get_provider') as mock_get_provider:
            mock_get_provider.return_value = self.mock_provider
            self.validator = MultiSourceValidator(
                provider="openai",
                language="en",
                min_sources=2
            )
    
    def test_validate_enough_sources(self):
        """Test validation with enough sources."""
        information = "The event occurred on January 15, 2023, in New York."
        sources = [
            {
                "id": "source1",
                "title": "News Article 1",
                "content": "A major event took place on January 15, 2023, in New York City.",
                "author": "John Doe",
                "publication_date": "2023-01-16"
            },
            {
                "id": "source2",
                "title": "News Article 2",
                "content": "The event happened on January 15, 2023, in Brooklyn, NY.",
                "author": "Jane Smith",
                "publication_date": "2023-01-16"
            },
            {
                "id": "source3",
                "title": "News Article 3",
                "content": "On January 15, 2023, New York saw a significant event occur.",
                "author": "Alex Brown",
                "publication_date": "2023-01-17"
            }
        ]
        
        result = self.validator.validate(information, sources)
        
        self.assertTrue(result["is_validated"])
        self.assertEqual(result["consistency_score"], 0.8)
        self.assertEqual(result["support_score"], 0.7)
        self.assertEqual(result["confidence_score"], 0.75)
        self.assertEqual(len(result["inconsistencies"]), 1)
        self.assertEqual(len(result["supporting_evidence"]), 2)
        self.assertEqual(len(result["contradicting_evidence"]), 1)
        self.assertEqual(len(result["credible_sources"]), 2)
        self.assertEqual(result["sources_count"], 3)
    
    def test_validate_insufficient_sources(self):
        """Test validation with insufficient sources."""
        information = "The event occurred on January 15, 2023, in New York."
        sources = [
            {
                "id": "source1",
                "title": "News Article 1",
                "content": "A major event took place on January 15, 2023, in New York City.",
                "author": "John Doe",
                "publication_date": "2023-01-16"
            }
        ]
        
        # Temporarily set min_sources to 2
        self.validator.min_sources = 2
        
        result = self.validator.validate(information, sources)
        
        self.assertFalse(result["is_validated"])
        self.assertEqual(result["confidence_score"], 0.0)
        self.assertEqual(result["sources_count"], 1)
        self.assertIn("Insufficient sources", result["reason"])
    
    def test_batch_validate(self):
        """Test batch validation."""
        information_items = [
            "The event occurred on January 15, 2023, in New York.",
            "The participants included representatives from 10 countries."
        ]
        
        sources = [
            {
                "id": "source1",
                "title": "News Article 1",
                "content": "A major event took place on January 15, 2023, in New York City with representatives from 10 countries.",
                "author": "John Doe",
                "publication_date": "2023-01-16"
            },
            {
                "id": "source2",
                "title": "News Article 2",
                "content": "The event happened on January 15, 2023, in Brooklyn, NY. Representatives from various countries attended.",
                "author": "Jane Smith",
                "publication_date": "2023-01-16"
            }
        ]
        
        results = self.validator.batch_validate(information_items, sources)
        
        self.assertEqual(len(results), 2)
        self.assertTrue(results[0]["is_validated"])
        self.assertTrue(results[1]["is_validated"])
    
    def test_analyze_contradictions(self):
        """Test analyzing contradictions between information items."""
        information_items = [
            "The event occurred on January 15, 2023.",
            "The event occurred on January 16, 2023."
        ]
        
        sources = [
            {
                "id": "source1",
                "title": "News Article 1",
                "content": "A major event took place on January 15, 2023.",
                "author": "John Doe",
                "publication_date": "2023-01-16"
            },
            {
                "id": "source2",
                "title": "News Article 2",
                "content": "The event happened on January 15, 2023.",
                "author": "Jane Smith",
                "publication_date": "2023-01-16"
            }
        ]
        
        # Mock contradiction check
        self.mock_provider.generate.side_effect = [
            # First and second calls for validate()
            json.dumps({
                "consistency_score": 0.8,
                "support_score": 0.7,
                "inconsistencies": [],
                "supporting_evidence": ["Source 1 confirms date"],
                "contradicting_evidence": [],
                "credible_sources": ["source1"],
                "uncertain_aspects": []
            }),
            json.dumps({
                "consistency_score": 0.2,
                "support_score": 0.1,
                "inconsistencies": ["Date is wrong"],
                "supporting_evidence": [],
                "contradicting_evidence": ["Both sources mention January 15, not 16"],
                "credible_sources": [],
                "uncertain_aspects": ["Accuracy of date"]
            }),
            # Third call for _check_contradiction()
            json.dumps({
                "is_contradiction": True,
                "contradiction_level": 0.9,
                "reason": "The dates are different: January 15 vs January 16"
            })
        ]
        
        result = self.validator.analyze_contradictions(information_items, sources)
        
        self.assertEqual(len(result["contradictions"]), 1)
        self.assertEqual(len(result["agreements"]), 0)
        self.assertEqual(result["items_count"], 2)
        self.assertEqual(result["sources_count"], 2)
    
    def test_get_most_reliable_information(self):
        """Test getting the most reliable information."""
        information_items = [
            "The event occurred on January 15, 2023, with 100 participants.",
            "The event occurred on January 15, 2023, with approximately 100-120 participants.",
            "The event may have occurred in mid-January with around 100 participants."
        ]
        
        sources = [
            {
                "id": "source1",
                "title": "News Article 1",
                "content": "A major event took place on January 15, 2023, with 100 participants.",
                "author": "John Doe",
                "publication_date": "2023-01-16"
            },
            {
                "id": "source2",
                "title": "News Article 2",
                "content": "The event on January 15 had approximately 100-120 participants.",
                "author": "Jane Smith",
                "publication_date": "2023-01-16"
            }
        ]
        
        # Mock validation responses with different confidence scores
        self.mock_provider.generate.side_effect = [
            json.dumps({
                "consistency_score": 0.9,
                "support_score": 0.9,
                "inconsistencies": [],
                "supporting_evidence": ["Exact match in Source 1"],
                "contradicting_evidence": [],
                "credible_sources": ["source1"],
                "uncertain_aspects": []
            }),
            json.dumps({
                "consistency_score": 0.8,
                "support_score": 0.7,
                "inconsistencies": ["Minor participant count difference"],
                "supporting_evidence": ["Source 2 supports approximate count"],
                "contradicting_evidence": [],
                "credible_sources": ["source2"],
                "uncertain_aspects": []
            }),
            json.dumps({
                "consistency_score": 0.5,
                "support_score": 0.4,
                "inconsistencies": ["Date is vague", "No exact date mentioned"],
                "supporting_evidence": ["Participant count is generally correct"],
                "contradicting_evidence": ["No source mentions 'mid-January'"],
                "credible_sources": [],
                "uncertain_aspects": ["Exact date"]
            })
        ]
        
        result = self.validator.get_most_reliable_information(information_items, sources)
        
        self.assertEqual(result["most_reliable_information"], information_items[0])
        self.assertEqual(result["confidence_score"], 0.9)
    
    def test_evaluate_source_credibility(self):
        """Test evaluating source credibility."""
        sources = [
            {
                "id": "source1",
                "title": "Academic Paper",
                "content": "A detailed analysis of the event with extensive data." * 50,  # Long content
                "author": "Dr. John Smith",
                "publication_date": "2023",
                "url": "https://university.edu/paper",
                "publisher": "University Press"
            },
            {
                "id": "source2",
                "title": "Blog Post",
                "content": "My thoughts on the event.",
                "author": "Random Blogger",
                "publication_date": "2023",
                "url": "https://blog.com/post",
                "publisher": ""
            }
        ]
        
        # Test educational URL source
        score1 = self.validator._evaluate_source_credibility(sources[0])
        # Test commercial URL source with short content
        score2 = self.validator._evaluate_source_credibility(sources[1])
        
        self.assertGreater(score1, score2)
        self.assertGreaterEqual(score1, 0.9)  # Academic source with .edu domain should have high score
        self.assertLessEqual(score2, 0.8)  # Blog should have lower score
    
    def test_vietnamese_language_support(self):
        """Test Vietnamese language support."""
        with patch('src.deep_research_core.reasoning.multi_source_validator.get_provider') as mock_get_provider:
            mock_get_provider.return_value = self.mock_provider
            vi_validator = MultiSourceValidator(
                provider="openai",
                language="vi",
                min_sources=2
            )
        
        # Check that the system prompt is in Vietnamese
        system_prompt = vi_validator._get_validation_system_prompt()
        contradiction_prompt = vi_validator._get_contradiction_system_prompt()
        
        self.assertIn("Bạn là một hệ thống kiểm tra", system_prompt)
        self.assertIn("Đầu ra của bạn phải ở định dạng JSON", system_prompt)
        self.assertIn("Bạn là một hệ thống phát hiện mâu thuẫn", contradiction_prompt)
    
    def test_parse_validation_result(self):
        """Test parsing validation result."""
        # Test valid JSON result
        json_result = '{"consistency_score": 0.85, "support_score": 0.75, "inconsistencies": ["Minor issue"], "supporting_evidence": ["Evidence A"]}'
        parsed = self.validator._parse_validation_result(json_result)
        
        self.assertEqual(parsed["consistency_score"], 0.85)
        self.assertEqual(parsed["support_score"], 0.75)
        self.assertEqual(parsed["inconsistencies"], ["Minor issue"])
        
        # Test JSON with missing fields
        json_result = '{"consistency_score": 0.85}'
        parsed = self.validator._parse_validation_result(json_result)
        
        self.assertEqual(parsed["consistency_score"], 0.85)
        self.assertEqual(parsed["support_score"], 0.0)  # Default value
        self.assertEqual(parsed["inconsistencies"], [])  # Default value
        
        # Test invalid JSON
        invalid_result = "Not a JSON"
        parsed = self.validator._parse_validation_result(invalid_result)
        
        self.assertEqual(parsed["consistency_score"], 0.0)
        self.assertEqual(parsed["support_score"], 0.0)
        self.assertIn("Error parsing validation result", parsed["inconsistencies"][0])


if __name__ == "__main__":
    unittest.main() 