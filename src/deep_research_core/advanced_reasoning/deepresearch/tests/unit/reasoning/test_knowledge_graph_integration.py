"""
Unit tests for the Knowledge Graph Integration module.
"""

import unittest
from unittest.mock import patch, MagicMock
import tempfile
import os
import json
import uuid

from src.deep_research_core.reasoning.knowledge_graph_integration import KnowledgeGraphIntegration
from src.deep_research_core.retrieval.weaviate_vector_store import WeaviateVectorStore


class TestKnowledgeGraphIntegration(unittest.TestCase):
    """Test the KnowledgeGraphIntegration class."""

    @patch('src.deep_research_core.reasoning.knowledge_graph_integration.WeaviateVectorStore')
    def setUp(self, mock_vector_store):
        """Set up test fixtures."""
        self.mock_vector_store = mock_vector_store
        self.mock_entity_store = MagicMock()
        self.mock_relation_store = MagicMock()

        # Configure the mock to return the mock stores
        mock_vector_store.side_effect = [self.mock_entity_store, self.mock_relation_store]

        # Create the KnowledgeGraphIntegration instance
        self.kg = KnowledgeGraphIntegration(
            url="http://localhost:8080",
            api_key="test-api-key",
            entity_class_name="TestEntity",
            relation_class_name="TestRelation",
            create_schema=True
        )

        # Mock the _create_schema method
        self.kg._create_schema = MagicMock()

        # Reset the mock counts for subsequent tests
        mock_vector_store.reset_mock()
        self.mock_entity_store.reset_mock()
        self.mock_relation_store.reset_mock()

    def tearDown(self):
        """Tear down test environment."""
        self.mock_vector_store.stop()

    def test_initialization(self):
        """Test initialization of KnowledgeGraphIntegration."""
        self.assertEqual(self.kg.url, "http://localhost:8080")
        self.assertEqual(self.kg.api_key, "test-api-key")
        self.assertEqual(self.kg.entity_class_name, "TestEntity")
        self.assertEqual(self.kg.relation_class_name, "TestRelation")

        # Verify WeaviateVectorStore was called correctly
        self.mock_vector_store.assert_any_call(
            url="http://localhost:8080",
            api_key="test-api-key",
            class_name="TestEntity",
            create_class=True,
            vector_index_type="hnsw"
        )

        self.mock_vector_store.assert_any_call(
            url="http://localhost:8080",
            api_key="test-api-key",
            class_name="TestRelation",
            create_class=True,
            vector_index_type="hnsw"
        )

    def test_create_schema(self):
        """Test schema creation."""
        # Reset mock to clear initialization calls
        self.mock_entity_store.client.reset_mock()

        # Mock schema existence check
        self.mock_entity_store.client.schema.exists.side_effect = [False, False]

        # Call _create_schema
        self.kg._create_schema()

        # Verify schema existence was checked
        self.mock_entity_store.client.schema.exists.assert_any_call("TestEntity")
        self.mock_entity_store.client.schema.exists.assert_any_call("TestRelation")

        # Verify schema creation was called
        self.assertEqual(self.mock_entity_store.client.schema.create_class.call_count, 2)

        # Verify entity class schema
        entity_schema = self.mock_entity_store.client.schema.create_class.call_args_list[0][0][0]
        self.assertEqual(entity_schema["class"], "TestEntity")
        self.assertEqual(len(entity_schema["properties"]), 4)

        # Verify relation class schema
        relation_schema = self.mock_entity_store.client.schema.create_class.call_args_list[1][0][0]
        self.assertEqual(relation_schema["class"], "TestRelation")
        self.assertEqual(len(relation_schema["properties"]), 5)

    def test_add_entity(self):
        """Test adding an entity."""
        # Mock add_documents
        self.mock_entity_store.add_documents.return_value = ["test-entity-id"]

        # Call add_entity
        entity_id = self.kg.add_entity(
            name="Test Entity",
            entity_type="Person",
            description="A test entity",
            properties={"age": 30}
        )

        # Verify add_documents was called
        self.mock_entity_store.add_documents.assert_called_once()

        # Verify the entity data
        args = self.mock_entity_store.add_documents.call_args[0]
        entity_data = args[0][0]
        self.assertEqual(entity_data["name"], "Test Entity")
        self.assertEqual(entity_data["type"], "Person")
        self.assertEqual(entity_data["description"], "A test entity")
        self.assertEqual(entity_data["properties"], {"age": 30})

        # Verify embedding was provided
        embedding = args[1][0]
        self.assertEqual(len(embedding), 768)  # Default embedding dimension

        # Verify return value
        self.assertEqual(entity_id, "test-entity-id")

    def test_add_relation(self):
        """Test adding a relation."""
        # Mock add_documents
        self.mock_relation_store.add_documents.return_value = ["test-relation-id"]

        # Call add_relation
        relation_id = self.kg.add_relation(
            source_id="source-entity-id",
            target_id="target-entity-id",
            relation_type="KNOWS",
            weight=0.8,
            properties={"since": 2020}
        )

        # Verify add_documents was called
        self.mock_relation_store.add_documents.assert_called_once()

        # Verify the relation data
        args = self.mock_relation_store.add_documents.call_args[0]
        relation_data = args[0][0]
        self.assertEqual(relation_data["source"], "source-entity-id")
        self.assertEqual(relation_data["target"], "target-entity-id")
        self.assertEqual(relation_data["type"], "KNOWS")
        self.assertEqual(relation_data["weight"], 0.8)
        self.assertEqual(relation_data["properties"], {"since": 2020})

        # Verify embedding was provided
        embedding = args[1][0]
        self.assertEqual(len(embedding), 768)  # Default embedding dimension

        # Verify return value
        self.assertEqual(relation_id, "test-relation-id")

    def test_get_entity(self):
        """Test getting an entity."""
        # Mock get_document
        self.mock_entity_store.get_document.return_value = {
            "id": "test-entity-id",
            "name": "Test Entity",
            "type": "Person",
            "description": "A test entity",
            "properties": {"age": 30}
        }

        # Call get_entity
        entity = self.kg.get_entity("test-entity-id")

        # Verify get_document was called
        self.mock_entity_store.get_document.assert_called_once_with("test-entity-id")

        # Verify return value
        self.assertEqual(entity["id"], "test-entity-id")
        self.assertEqual(entity["name"], "Test Entity")

    def test_get_relation(self):
        """Test getting a relation."""
        # Mock get_document
        self.mock_relation_store.get_document.return_value = {
            "id": "test-relation-id",
            "source": "source-entity-id",
            "target": "target-entity-id",
            "type": "KNOWS",
            "weight": 0.8,
            "properties": {"since": 2020}
        }

        # Call get_relation
        relation = self.kg.get_relation("test-relation-id")

        # Verify get_document was called
        self.mock_relation_store.get_document.assert_called_once_with("test-relation-id")

        # Verify return value
        self.assertEqual(relation["id"], "test-relation-id")
        self.assertEqual(relation["type"], "KNOWS")

    def test_search_entities(self):
        """Test searching for entities."""
        # Mock search
        self.mock_entity_store.search.return_value = [
            {
                "id": "entity-1",
                "name": "Entity 1",
                "type": "Person",
                "score": 0.9
            },
            {
                "id": "entity-2",
                "name": "Entity 2",
                "type": "Person",
                "score": 0.8
            }
        ]

        # Create a query embedding
        query_embedding = [0.1] * 768

        # Call search_entities
        results = self.kg.search_entities(
            query_embedding=query_embedding,
            entity_type="Person",
            top_k=2
        )

        # Verify search was called
        self.mock_entity_store.search.assert_called_once_with(
            query_embedding, 2, "type == 'Person'"
        )

        # Verify return value
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0]["id"], "entity-1")
        self.assertEqual(results[1]["id"], "entity-2")

    def test_search_relations(self):
        """Test searching for relations."""
        # Mock search
        self.mock_relation_store.search.return_value = [
            {
                "id": "relation-1",
                "source": "entity-1",
                "target": "entity-2",
                "type": "KNOWS",
                "score": 0.9
            },
            {
                "id": "relation-2",
                "source": "entity-1",
                "target": "entity-3",
                "type": "KNOWS",
                "score": 0.8
            }
        ]

        # Create a query embedding
        query_embedding = [0.1] * 768

        # Call search_relations
        results = self.kg.search_relations(
            query_embedding=query_embedding,
            relation_type="KNOWS",
            source_id="entity-1",
            top_k=2
        )

        # Verify search was called
        self.mock_relation_store.search.assert_called_once_with(
            query_embedding, 2, "type == 'KNOWS' and source == 'entity-1'"
        )

        # Verify return value
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0]["id"], "relation-1")
        self.assertEqual(results[1]["id"], "relation-2")

    def test_get_entity_relations(self):
        """Test getting entity relations."""
        # Mock get_documents
        self.mock_relation_store.get_documents.return_value = [
            {
                "id": "relation-1",
                "source": "entity-1",
                "target": "entity-2",
                "type": "KNOWS"
            },
            {
                "id": "relation-2",
                "source": "entity-1",
                "target": "entity-3",
                "type": "WORKS_WITH"
            }
        ]

        # Call get_entity_relations
        relations = self.kg.get_entity_relations(
            entity_id="entity-1",
            relation_type="KNOWS",
            direction="outgoing"
        )

        # Verify get_documents was called
        self.mock_relation_store.get_documents.assert_called_once_with(
            filter_expr="(source == 'entity-1') and type == 'KNOWS'"
        )

        # Verify return value
        self.assertEqual(len(relations), 2)

    def test_get_connected_entities(self):
        """Test getting connected entities."""
        # Mock get_entity_relations
        self.kg.get_entity_relations = MagicMock(return_value=[
            {
                "id": "relation-1",
                "source": "entity-1",
                "target": "entity-2",
                "type": "KNOWS"
            },
            {
                "id": "relation-2",
                "source": "entity-1",
                "target": "entity-3",
                "type": "KNOWS"
            }
        ])

        # Mock get_entity
        self.kg.get_entity = MagicMock(side_effect=lambda entity_id: {
            "id": entity_id,
            "name": f"Entity {entity_id}",
            "type": "Person"
        })

        # Call get_connected_entities
        entities = self.kg.get_connected_entities(
            entity_id="entity-1",
            relation_type="KNOWS",
            direction="outgoing",
            max_depth=1
        )

        # Verify get_entity_relations was called
        self.kg.get_entity_relations.assert_called_once_with(
            "entity-1", "KNOWS", "outgoing"
        )

        # Verify get_entity was called for each connected entity
        self.kg.get_entity.assert_any_call("entity-2")
        self.kg.get_entity.assert_any_call("entity-3")

        # Verify return value
        self.assertEqual(len(entities), 2)
        # The order of entities can vary, so we check that both are in the result
        entity_ids = [entity["id"] for entity in entities]
        self.assertIn("entity-2", entity_ids)
        self.assertIn("entity-3", entity_ids)

    def test_visualize(self):
        """Test knowledge graph visualization."""
        # Mock get_documents for entities
        self.mock_entity_store.get_documents.return_value = [
            {
                "id": "entity-1",
                "name": "Entity 1",
                "type": "Person",
                "properties": {}
            },
            {
                "id": "entity-2",
                "name": "Entity 2",
                "type": "Person",
                "properties": {}
            }
        ]

        # Mock get_entity_relations
        self.kg.get_entity_relations = MagicMock(side_effect=lambda entity_id, direction: [
            {
                "id": f"relation-{entity_id}-1",
                "source": entity_id,
                "target": "entity-3",
                "type": "KNOWS",
                "weight": 1.0,
                "properties": {}
            }
        ])

        # Call visualize
        visualization = self.kg.visualize()

        # Verify get_documents was called
        self.mock_entity_store.get_documents.assert_called_once_with(limit=100)

        # Verify get_entity_relations was called for each entity
        self.kg.get_entity_relations.assert_any_call("entity-1", direction="both")
        self.kg.get_entity_relations.assert_any_call("entity-2", direction="both")

        # Verify return value
        self.assertIn("nodes", visualization)
        self.assertIn("edges", visualization)
        self.assertEqual(len(visualization["nodes"]), 2)

    def test_query(self):
        """Test GraphQL query execution."""
        # Mock raw query
        self.mock_entity_store.client.query.raw.return_value = {
            "data": {
                "Get": {
                    "TestEntity": [
                        {
                            "name": "Entity 1",
                            "type": "Person"
                        }
                    ]
                }
            }
        }

        # Call query
        result = self.kg.query(
            query="""
            {
                Get {
                    TestEntity {
                        name
                        type
                    }
                }
            }
            """
        )

        # Verify raw query was called
        self.mock_entity_store.client.query.raw.assert_called_once()

        # Verify return value
        self.assertIn("data", result)
        self.assertIn("Get", result["data"])

    def test_export_import(self):
        """Test exporting and importing the knowledge graph."""
        # Create temp file
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_path = temp_file.name

        try:
            # Mock get_documents for entities
            self.mock_entity_store.get_documents.return_value = [
                {
                    "id": "entity-1",
                    "name": "Entity 1",
                    "type": "Person",
                    "description": "A person",
                    "properties": {"age": 30},
                    "vector": [0.1] * 768
                }
            ]

            # Mock get_documents for relations
            self.mock_relation_store.get_documents.return_value = [
                {
                    "id": "relation-1",
                    "source": "entity-1",
                    "target": "entity-2",
                    "type": "KNOWS",
                    "weight": 1.0,
                    "properties": {"since": 2020},
                    "vector": [0.2] * 768
                }
            ]

            # Call export
            result = self.kg.export(temp_path)

            # Verify export was successful
            self.assertTrue(result)

            # Verify file was created
            self.assertTrue(os.path.exists(temp_path))

            # Verify file content
            with open(temp_path, "r") as f:
                export_data = json.load(f)
                self.assertIn("entities", export_data)
                self.assertIn("relations", export_data)
                self.assertEqual(len(export_data["entities"]), 1)
                self.assertEqual(len(export_data["relations"]), 1)

            # Mock clear
            self.kg.clear = MagicMock(return_value=True)

            # Call import
            result = self.kg.import_from_file(temp_path)

            # Verify import was successful
            self.assertTrue(result)

            # Verify clear was called
            self.kg.clear.assert_called_once()

            # Verify add_documents was called for entities and relations
            self.mock_entity_store.add_documents.assert_called_once()
            self.mock_relation_store.add_documents.assert_called_once()

        finally:
            # Clean up
            if os.path.exists(temp_path):
                os.unlink(temp_path)

    def test_clear(self):
        """Test clearing the knowledge graph."""
        # Call clear
        result = self.kg.clear()

        # Verify clear was called on both stores
        self.mock_entity_store.clear.assert_called_once()
        self.mock_relation_store.clear.assert_called_once()

        # Verify return value
        self.assertTrue(result)

    @patch('src.deep_research_core.reasoning.knowledge_graph_integration.SentenceTransformer')
    @patch('src.deep_research_core.reasoning.knowledge_graph_integration.split_text')
    def test_build_from_documents(self, mock_split_text, mock_sentence_transformer):
        """Test building a knowledge graph from documents."""
        # Configure mocks
        mock_llm_provider = MagicMock()
        mock_model = "test-model"
        
        # Mock SentenceTransformer
        mock_embedding_model = MagicMock()
        mock_sentence_transformer.return_value = mock_embedding_model
        mock_embedding_model.encode.return_value = [0.1] * 768
        
        # Mock split_text to return the original text
        mock_split_text.side_effect = lambda text, **kwargs: [text]
        
        # Mock LLM responses
        entity_response = json.dumps([
            {
                "name": "John Doe",
                "type": "Person",
                "description": "A software engineer",
                "properties": {"age": 30}
            },
            {
                "name": "Acme Inc",
                "type": "Organization",
                "description": "A technology company",
                "properties": {"founded": 2010}
            }
        ])
        
        relation_response = json.dumps([
            {
                "source": 1,
                "target": 2,
                "type": "WORKS_FOR",
                "properties": {"position": "Engineer"}
            }
        ])
        
        # Configure LLM provider mock to return responses
        mock_llm_provider.generate.side_effect = [entity_response, relation_response]
        
        # Mock add_entity and add_relation methods
        self.kg.add_entity = MagicMock(side_effect=["entity1", "entity2"])
        self.kg.add_relation = MagicMock(return_value="relation1")
        self.kg.get_entity = MagicMock(return_value=None)
        
        # Sample documents to process
        documents = [
            {
                "id": "doc1",
                "content": "John Doe is a software engineer at Acme Inc.",
                "metadata": {"source": "test", "date": "2023-01-01"}
            }
        ]
        
        # Call build_from_documents
        num_entities, num_relations = self.kg.build_from_documents(
            documents=documents,
            llm_provider=mock_llm_provider,
            model=mock_model,
            batch_size=1,
            extract_relations=True
        )
        
        # Verify LLM was called for entity extraction
        mock_llm_provider.generate.assert_any_call(
            prompt=unittest.mock.ANY,  # Don't check exact prompt
            model=mock_model,
            temperature=0.2,
            max_tokens=2000
        )
        
        # Verify LLM was called for relation extraction
        assert mock_llm_provider.generate.call_count == 2
        
        # Verify entities were added
        assert self.kg.add_entity.call_count == 2
        
        # Verify relations were added (1 WORKS_FOR relation and 2 MENTIONED_IN relations)
        assert self.kg.add_relation.call_count == 3
        
        # Verify correct counts returned
        self.assertEqual(num_entities, 2)  # 2 entities
        self.assertEqual(num_relations, 3)  # 3 relations

    @patch('src.deep_research_core.reasoning.knowledge_graph_integration.SentenceTransformer')
    @patch('src.deep_research_core.reasoning.knowledge_graph_integration.split_text')
    def test_build_from_documents_with_merging(self, mock_split_text, mock_sentence_transformer):
        """Test building a knowledge graph with entity merging."""
        # Configure mocks
        mock_llm_provider = MagicMock()
        mock_model = "test-model"
        
        # Mock SentenceTransformer
        mock_embedding_model = MagicMock()
        mock_sentence_transformer.return_value = mock_embedding_model
        mock_embedding_model.encode.return_value = [0.1] * 768
        
        # Mock split_text to return the original text
        mock_split_text.side_effect = lambda text, **kwargs: [text]
        
        # Mock LLM responses for two documents with overlapping entities
        entity_response1 = json.dumps([
            {
                "name": "John Doe",
                "type": "Person",
                "description": "A software engineer",
                "properties": {"age": 30}
            }
        ])
        
        entity_response2 = json.dumps([
            {
                "name": "John Doe",
                "type": "Person",
                "description": "A senior developer",
                "properties": {"experience": "10 years"}
            }
        ])
        
        # Configure LLM provider mock to return responses
        mock_llm_provider.generate.side_effect = [entity_response1, entity_response2]
        
        # Mock add_entity, get_entity, and update_entity methods
        self.kg.add_entity = MagicMock(return_value="entity1")
        
        # First call to get_entity returns None (entity doesn't exist yet)
        # Second call returns a mock entity (after it's been created)
        self.kg.get_entity = MagicMock(side_effect=[
            None,  # For similarity check
            {      # For update check
                "id": "entity1",
                "name": "John Doe",
                "type": "Person",
                "description": "A software engineer",
                "properties": {"age": 30}
            }
        ])
        
        self.kg.update_entity = MagicMock(return_value=True)
        self.kg.add_relation = MagicMock(return_value="relation1")
        
        # Sample documents to process
        documents = [
            {
                "id": "doc1",
                "content": "John Doe is a software engineer.",
                "metadata": {"source": "test1"}
            },
            {
                "id": "doc2",
                "content": "John Doe has 10 years of experience.",
                "metadata": {"source": "test2"}
            }
        ]
        
        # Call build_from_documents
        num_entities, num_relations = self.kg.build_from_documents(
            documents=documents,
            llm_provider=mock_llm_provider,
            model=mock_model,
            batch_size=1,
            extract_relations=False,
            merge_entities=True
        )
        
        # Verify add_entity was called only once (first document)
        self.kg.add_entity.assert_called_once()
        
        # Verify update_entity was called once (second document)
        self.kg.update_entity.assert_called_once()
        
        # Verify update_entity was called with merged properties
        update_call = self.kg.update_entity.call_args
        self.assertEqual(update_call[1]["entity_id"], "entity1")
        self.assertEqual(update_call[1]["name"], "John Doe")
        self.assertEqual(update_call[1]["entity_type"], "Person")
        self.assertEqual(update_call[1]["description"], "A senior developer")
        
        # Properties should be merged from both documents
        expected_properties = {
            "age": 30,
            "experience": "10 years",
            "doc_source": "test2"  # From second document
        }
        self.assertEqual(update_call[1]["properties"], expected_properties)
        
        # Verify add_relation was called twice (one for each document)
        self.assertEqual(self.kg.add_relation.call_count, 2)
        
        # Verify correct counts returned
        self.assertEqual(num_entities, 1)  # 1 entity (merged)
        self.assertEqual(num_relations, 2)  # 2 MENTIONED_IN relations


if __name__ == '__main__':
    unittest.main()
