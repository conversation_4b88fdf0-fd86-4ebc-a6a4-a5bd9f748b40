"""
Unit tests for error analysis in CoTRAG.

This module contains tests for the error analysis mechanisms in CoTRAG,
including identifying error sources and generating improvement suggestions.
"""

import unittest
from unittest.mock import MagicMock, patch, ANY
import sys
import os
import json
import tempfile
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))

from src.deep_research_core.reasoning.cot_rag import CoTRAG
from src.deep_research_core.evaluation.cotrag_error_analyzer import CoTRAGErrorAnalyzer


class TestCoTRAGErrorAnalysis(unittest.TestCase):
    """Test case for error analysis in CoTRAG."""

    def setUp(self):
        """Set up test fixtures."""
        # Create mock objects
        self.mock_api_provider = MagicMock()
        self.mock_api_provider.generate.return_value = "AI là trí tuệ nhân tạo. Nó là một lĩnh vực của khoa học máy t<PERSON>h."

        self.mock_vector_store = MagicMock()
        self.mock_vector_store.search.return_value = [
            {
                "content": "AI là viết tắt của Artificial Intelligence (Trí tuệ nhân tạo).",
                "score": 0.95
            },
            {
                "content": "Trí tuệ nhân tạo là một nhánh của khoa học máy tính.",
                "score": 0.85
            }
        ]

        # Create patches
        self.patches = [
            patch('src.deep_research_core.models.api.openai.openai_provider.generate',
                  return_value="AI là trí tuệ nhân tạo. Nó là một lĩnh vực của khoa học máy tính."),
            patch('src.deep_research_core.models.api.openrouter.openrouter_provider.generate',
                  return_value="AI là trí tuệ nhân tạo. Nó là một lĩnh vực của khoa học máy tính."),
            patch('src.deep_research_core.models.api.anthropic.anthropic_provider.generate',
                  return_value="AI là trí tuệ nhân tạo. Nó là một lĩnh vực của khoa học máy tính."),
            patch('src.deep_research_core.retrieval.vector_store.get_vector_store',
                  return_value=self.mock_vector_store)
        ]

        # Start patches
        for p in self.patches:
            p.start()

        # Create sample data
        self.sample_queries = {
            "factual": "AI là gì?",
            "reasoning": "Tại sao AI quan trọng trong thời đại số?",
            "complex": "So sánh và phân tích sự khác biệt giữa học máy, học sâu và trí tuệ nhân tạo, đồng thời giải thích cách chúng liên quan đến nhau.",
            "irrelevant": "Làm thế nào để nấu phở bò ngon?",
            "vietnamese": "Trí tuệ nhân tạo có thể giúp gì cho nông nghiệp Việt Nam?"
        }

        self.sample_documents = [
            {
                "content": "AI là viết tắt của Artificial Intelligence (Trí tuệ nhân tạo).",
                "score": 0.95
            },
            {
                "content": "Trí tuệ nhân tạo là một nhánh của khoa học máy tính.",
                "score": 0.85
            }
        ]

        self.sample_reasoning = "AI là viết tắt của Artificial Intelligence, tức là Trí tuệ nhân tạo. Đây là một lĩnh vực của khoa học máy tính."
        self.sample_answer = "AI là trí tuệ nhân tạo, một lĩnh vực của khoa học máy tính."

    def tearDown(self):
        """Clean up after tests."""
        # Stop patches
        for p in self.patches:
            p.stop()

    def test_error_analyzer_initialization(self):
        """Test initialization of CoTRAGErrorAnalyzer."""
        # Create a temporary file for error history
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_path = temp_file.name

        # Create CoTRAGErrorAnalyzer
        analyzer = CoTRAGErrorAnalyzer(
            provider="openai",
            model="gpt-4o",
            verbose=True,
            error_history_path=temp_path,
            learn_from_errors=True
        )

        # Verify initialization
        self.assertEqual(analyzer.provider, "openai")
        self.assertEqual(analyzer.model, "gpt-4o")
        self.assertTrue(analyzer.verbose)
        self.assertEqual(analyzer.error_history_path, temp_path)
        self.assertTrue(analyzer.learn_from_errors)
        self.assertEqual(len(analyzer.error_history), 0)

        # Clean up
        os.unlink(temp_path)

    def test_document_analysis(self):
        """Test document analysis functionality."""
        # Create CoTRAGErrorAnalyzer
        analyzer = CoTRAGErrorAnalyzer(
            provider="openai",
            model="gpt-4o",
            verbose=True
        )

        # Test with relevant documents
        doc_analysis = analyzer._analyze_documents(
            self.sample_queries["factual"],
            self.sample_documents
        )

        # Verify analysis
        self.assertIn("relevance_score", doc_analysis)
        self.assertIn("has_relevant_documents", doc_analysis)
        self.assertIn("avg_score", doc_analysis)
        self.assertIn("issues", doc_analysis)
        self.assertTrue(doc_analysis["has_relevant_documents"])
        self.assertGreaterEqual(doc_analysis["relevance_score"], 0.7)

        # Test with irrelevant documents
        irrelevant_docs = [
            {
                "content": "Phở là một món ăn truyền thống của Việt Nam.",
                "score": 0.3
            },
            {
                "content": "Cách nấu phở bò ngon nhất là dùng xương bò hầm trong 8 giờ.",
                "score": 0.25
            }
        ]

        irrelevant_analysis = analyzer._analyze_documents(
            self.sample_queries["factual"],
            irrelevant_docs
        )

        # Verify analysis
        self.assertFalse(irrelevant_analysis["has_relevant_documents"])
        self.assertLessEqual(irrelevant_analysis["relevance_score"], 0.5)
        self.assertIn("Low relevance documents retrieved", irrelevant_analysis["issues"])

    def test_reasoning_analysis(self):
        """Test reasoning analysis functionality."""
        # Create CoTRAGErrorAnalyzer
        analyzer = CoTRAGErrorAnalyzer(
            provider="openai",
            model="gpt-4o",
            verbose=True
        )

        # Test with good reasoning
        good_reasoning = """
        First, let's understand what AI is. AI stands for Artificial Intelligence, which refers to the simulation of human intelligence in machines.
        
        Next, AI is a branch of computer science focused on creating systems capable of performing tasks that typically require human intelligence.
        
        Finally, these tasks include learning, reasoning, problem-solving, perception, and language understanding.
        """

        reasoning_analysis = analyzer._analyze_reasoning(
            self.sample_queries["factual"],
            good_reasoning,
            self.sample_documents
        )

        # Verify analysis
        self.assertIn("reasoning_quality", reasoning_analysis)
        self.assertIn("step_count", reasoning_analysis)
        self.assertIn("citation_score", reasoning_analysis)
        self.assertIn("error_count", reasoning_analysis)
        self.assertIn("issues", reasoning_analysis)
        self.assertGreaterEqual(reasoning_analysis["reasoning_quality"], 0.5)
        self.assertGreaterEqual(reasoning_analysis["step_count"], 3)

        # Test with poor reasoning
        poor_reasoning = "AI is artificial intelligence."

        poor_analysis = analyzer._analyze_reasoning(
            self.sample_queries["factual"],
            poor_reasoning,
            self.sample_documents
        )

        # Verify analysis
        self.assertLessEqual(poor_analysis["reasoning_quality"], 0.5)
        self.assertLessEqual(poor_analysis["step_count"], 1)
        self.assertIn("Insufficient reasoning steps", poor_analysis["issues"])

    def test_answer_analysis(self):
        """Test answer analysis functionality."""
        # Create CoTRAGErrorAnalyzer
        analyzer = CoTRAGErrorAnalyzer(
            provider="openai",
            model="gpt-4o",
            verbose=True
        )

        # Test with good answer
        good_answer = "AI (Artificial Intelligence) is the simulation of human intelligence in machines that are programmed to think and learn like humans. It is a branch of computer science."
        
        answer_analysis = analyzer._analyze_answer(
            good_answer,
            expected_answer=None
        )

        # Verify analysis
        self.assertIn("answer_quality", answer_analysis)
        self.assertIn("answer_length", answer_analysis)
        self.assertIn("uncertainty_count", answer_analysis)
        self.assertIn("issues", answer_analysis)
        self.assertGreaterEqual(answer_analysis["answer_quality"], 0.7)
        self.assertEqual(answer_analysis["uncertainty_count"], 0)

        # Test with uncertain answer
        uncertain_answer = "AI might be artificial intelligence, but I'm not sure. It could be related to computer science, perhaps."
        
        uncertain_analysis = analyzer._analyze_answer(
            uncertain_answer,
            expected_answer=None
        )

        # Verify analysis
        self.assertLessEqual(uncertain_analysis["answer_quality"], 0.5)
        self.assertGreaterEqual(uncertain_analysis["uncertainty_count"], 2)
        self.assertIn("High uncertainty in answer", uncertain_analysis["issues"])

        # Test with expected answer
        expected_answer = "AI is artificial intelligence, a field of computer science."
        
        expected_analysis = analyzer._analyze_answer(
            good_answer,
            expected_answer=expected_answer
        )

        # Verify analysis
        self.assertIn("similarity_score", expected_analysis)
        self.assertGreaterEqual(expected_analysis["similarity_score"], 0.5)

    def test_error_source_determination(self):
        """Test error source determination functionality."""
        # Create CoTRAGErrorAnalyzer
        analyzer = CoTRAGErrorAnalyzer(
            provider="openai",
            model="gpt-4o",
            verbose=True
        )

        # Create sample analyses
        document_analysis = {
            "relevance_score": 0.9,
            "has_relevant_documents": True,
            "avg_score": 0.9,
            "issues": []
        }

        reasoning_analysis = {
            "reasoning_quality": 0.8,
            "step_count": 3,
            "citation_score": 0.7,
            "error_count": 0,
            "issues": []
        }

        answer_analysis = {
            "answer_quality": 0.9,
            "answer_length": 50,
            "uncertainty_count": 0,
            "issues": []
        }

        # Test with good analyses (no errors)
        error_source = analyzer._determine_error_source(
            document_analysis,
            reasoning_analysis,
            answer_analysis
        )

        # Verify determination
        self.assertIn("primary_source", error_source)
        self.assertIn("secondary_source", error_source)
        self.assertIn("confidence", error_source)
        self.assertLessEqual(error_source["confidence"], 0.3)  # Low confidence because no clear errors

        # Test with document retrieval error
        document_analysis["relevance_score"] = 0.2
        document_analysis["has_relevant_documents"] = False
        document_analysis["issues"] = ["Low relevance documents retrieved"]

        error_source = analyzer._determine_error_source(
            document_analysis,
            reasoning_analysis,
            answer_analysis
        )

        # Verify determination
        self.assertEqual(error_source["primary_source"], "RAG")
        self.assertGreaterEqual(error_source["confidence"], 0.7)

        # Test with reasoning error
        document_analysis["relevance_score"] = 0.9
        document_analysis["has_relevant_documents"] = True
        document_analysis["issues"] = []

        reasoning_analysis["reasoning_quality"] = 0.2
        reasoning_analysis["step_count"] = 1
        reasoning_analysis["issues"] = ["Insufficient reasoning steps"]

        error_source = analyzer._determine_error_source(
            document_analysis,
            reasoning_analysis,
            answer_analysis
        )

        # Verify determination
        self.assertEqual(error_source["primary_source"], "CoT")
        self.assertGreaterEqual(error_source["confidence"], 0.7)

    def test_suggestion_generation(self):
        """Test suggestion generation functionality."""
        # Create CoTRAGErrorAnalyzer
        analyzer = CoTRAGErrorAnalyzer(
            provider="openai",
            model="gpt-4o",
            verbose=True
        )

        # Test with RAG error
        rag_error = {
            "primary_source": "RAG",
            "secondary_source": "CoT",
            "confidence": 0.8,
            "document_error_score": 0.8,
            "reasoning_error_score": 0.3,
            "answer_error_score": 0.2
        }

        rag_suggestions = analyzer._generate_suggestions(
            rag_error,
            self.sample_queries["factual"],
            [],  # Empty documents
            self.sample_reasoning
        )

        # Verify suggestions
        self.assertIn("primary_suggestion", rag_suggestions)
        self.assertIn("specific_suggestions", rag_suggestions)
        self.assertIn("Increase the number of retrieved documents", rag_suggestions["primary_suggestion"])

        # Test with CoT error
        cot_error = {
            "primary_source": "CoT",
            "secondary_source": "RAG",
            "confidence": 0.8,
            "document_error_score": 0.3,
            "reasoning_error_score": 0.8,
            "answer_error_score": 0.2
        }

        cot_suggestions = analyzer._generate_suggestions(
            cot_error,
            self.sample_queries["factual"],
            self.sample_documents,
            "AI is artificial intelligence."  # Poor reasoning
        )

        # Verify suggestions
        self.assertIn("primary_suggestion", cot_suggestions)
        self.assertIn("specific_suggestions", cot_suggestions)
        self.assertIn("Improve reasoning", cot_suggestions["primary_suggestion"].lower())

    def test_error_history_learning(self):
        """Test learning from error history."""
        # Create a temporary file for error history
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_path = temp_file.name

        # Create CoTRAGErrorAnalyzer with error history
        analyzer = CoTRAGErrorAnalyzer(
            provider="openai",
            model="gpt-4o",
            verbose=True,
            error_history_path=temp_path,
            learn_from_errors=True
        )

        # Add some error history
        analyzer.error_history = [
            {
                "query": "AI là gì?",
                "document_analysis": {
                    "relevance_score": 0.3,
                    "has_relevant_documents": False,
                    "issues": ["Low relevance documents retrieved"]
                },
                "reasoning_analysis": {
                    "reasoning_quality": 0.7,
                    "issues": []
                },
                "answer_analysis": {
                    "answer_quality": 0.8,
                    "issues": []
                },
                "error_source": {
                    "primary_source": "RAG"
                },
                "timestamp": datetime.now().isoformat()
            },
            {
                "query": "What is AI?",
                "document_analysis": {
                    "relevance_score": 0.2,
                    "has_relevant_documents": False,
                    "issues": ["Low relevance documents retrieved"]
                },
                "reasoning_analysis": {
                    "reasoning_quality": 0.6,
                    "issues": []
                },
                "answer_analysis": {
                    "answer_quality": 0.7,
                    "issues": []
                },
                "error_source": {
                    "primary_source": "RAG"
                },
                "timestamp": datetime.now().isoformat()
            }
        ]

        # Save error history
        with open(temp_path, 'w', encoding='utf-8') as f:
            json.dump(analyzer.error_history, f)

        # Create new document analysis
        document_analysis = {
            "relevance_score": 0.5,
            "has_relevant_documents": True,
            "avg_score": 0.5,
            "issues": []
        }

        reasoning_analysis = {
            "reasoning_quality": 0.7,
            "step_count": 2,
            "citation_score": 0.6,
            "error_count": 0,
            "issues": []
        }

        answer_analysis = {
            "answer_quality": 0.8,
            "answer_length": 40,
            "uncertainty_count": 0,
            "issues": []
        }

        # Apply learning
        analyzer._apply_error_history_learning(
            document_analysis,
            reasoning_analysis,
            answer_analysis,
            "AI là gì?"
        )

        # Verify that historical patterns were applied
        self.assertIn("Low relevance documents retrieved (historical pattern)", document_analysis["issues"])

        # Clean up
        os.unlink(temp_path)

    def test_integration_with_cotrag(self):
        """Test integration of error analysis with CoTRAG."""
        # Create CoTRAG with error analysis
        cotrag = CoTRAG(
            provider="openai",
            model="gpt-4o",
            temperature=0.7,
            max_tokens=2000,
            analyze_errors=True
        )

        # Mock components
        cotrag.api_provider = self.mock_api_provider
        cotrag.vector_store = self.mock_vector_store

        # Process a query with expected answer
        result = cotrag.process(
            self.sample_queries["factual"],
            expected_answer="AI is artificial intelligence, a field of computer science."
        )

        # Verify that error analysis is included
        self.assertIn("error_analysis", result)
        self.assertIn("document_analysis", result["error_analysis"])
        self.assertIn("reasoning_analysis", result["error_analysis"])
        self.assertIn("answer_analysis", result["error_analysis"])
        self.assertIn("error_source", result["error_analysis"])
        self.assertIn("suggestions", result["error_analysis"])


if __name__ == "__main__":
    unittest.main()
