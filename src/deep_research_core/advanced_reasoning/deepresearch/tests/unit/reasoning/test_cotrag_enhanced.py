"""
Unit tests for the enhanced CoTRAG implementation.

This module contains tests for the enhanced CoTRAG implementation:
- CoTRAGVietnamese
- CoTRAGAdaptiveLearning
- CoTRAGAdvancedStrategies
- CoTRAGEnhanced
"""

import unittest
from unittest.mock import MagicMock, patch, ANY
import sys
import os
import json
import tempfile

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))

from src.deep_research_core.reasoning.cotrag_vietnamese import CoTRAGVietnamese
from src.deep_research_core.reasoning.cotrag_adaptive_learning import CoTRAGAdaptiveLearning
from src.deep_research_core.reasoning.cotrag_advanced_strategies import CoTRAGAdvancedStrategies
from src.deep_research_core.reasoning.cotrag_enhanced import CoTRAGEnhanced


class TestCoTRAGVietnamese(unittest.TestCase):
    """Test case for the CoTRAGVietnamese implementation."""

    def setUp(self):
        """Set up test fixtures."""
        # Create mock RAG instance
        self.mock_vector_store = MagicMock()
        self.mock_vector_store.search.return_value = [
            {
                "content": "Trí tuệ nhân tạo là một nhánh của khoa học máy tính.",
                "metadata": {"source": "Test Source", "title": "Test Document"},
                "score": 0.85
            },
            {
                "content": "Học máy là một phần của trí tuệ nhân tạo.",
                "metadata": {"source": "Test Source 2", "title": "Test Document 2"},
                "score": 0.75
            }
        ]

        # Create mock API provider
        self.mock_api_provider = MagicMock()
        self.mock_api_provider.generate.return_value = "Đầu tiên, tôi sẽ phân tích thông tin. Các tài liệu đề cập đến trí tuệ nhân tạo và học máy. Vì vậy, câu trả lời là học máy là một phần của trí tuệ nhân tạo."

        # Create mock Vietnamese embeddings
        self.mock_vietnamese_embeddings = MagicMock()
        self.mock_vietnamese_embeddings.get_embeddings.return_value = [[0.1, 0.2, 0.3]]
        self.mock_vietnamese_embeddings.get_embedding_dimension.return_value = 768

        # Create patches
        self.patches = [
            patch('src.deep_research_core.reasoning.cot_rag.get_vector_store', return_value=self.mock_vector_store),
            patch('src.deep_research_core.models.api.openai.openai_provider', self.mock_api_provider),
            patch('src.deep_research_core.models.api.anthropic.anthropic_provider', self.mock_api_provider),
            patch('src.deep_research_core.models.api.openrouter.openrouter_provider', self.mock_api_provider),
            patch('src.deep_research_core.multilingual.vietnamese_embeddings.VietnameseEmbeddingFactory.get_embedding_model', return_value=self.mock_vietnamese_embeddings)
        ]

        # Start patches
        for p in self.patches:
            p.start()

        # Create CoTRAGVietnamese instance
        self.cotrag_vi = CoTRAGVietnamese(
            provider="openrouter",
            model="anthropic/claude-3-opus",
            vietnamese_embedding_model="phobert",
            verbose=True
        )

    def tearDown(self):
        """Tear down test fixtures."""
        # Stop patches
        for p in self.patches:
            p.stop()

    def test_vietnamese_system_prompt(self):
        """Test that the system prompt is in Vietnamese."""
        self.assertIn("trợ lý AI hữu ích", self.cotrag_vi.system_prompt)
        self.assertIn("suy luận", self.cotrag_vi.system_prompt)

    def test_vietnamese_user_prompt(self):
        """Test that the user prompt template is in Vietnamese."""
        self.assertIn("Câu hỏi:", self.cotrag_vi.user_prompt_template)
        self.assertIn("Thông tin được truy xuất", self.cotrag_vi.user_prompt_template)

    def test_vietnamese_processing(self):
        """Test processing a Vietnamese query."""
        result = self.cotrag_vi.process("Mối quan hệ giữa trí tuệ nhân tạo và học máy là gì?")

        # Check that the API was called
        self.mock_api_provider.generate.assert_called_once()

        # Check that the result contains expected fields
        self.assertEqual(result["query"], "Mối quan hệ giữa trí tuệ nhân tạo và học máy là gì?")
        self.assertEqual(len(result["documents"]), 2)
        self.assertIn("Vì vậy, câu trả lời là", result["answer"])
        self.assertEqual(result["language"], "vi")
        self.assertEqual(result["vietnamese_embedding_model"], "phobert")

    def test_vietnamese_embeddings(self):
        """Test that Vietnamese embeddings are used."""
        self.cotrag_vi._get_embeddings(["Trí tuệ nhân tạo là gì?"])
        self.mock_vietnamese_embeddings.get_embeddings.assert_called_once()

    def test_vietnamese_low_relevance_prompt(self):
        """Test the low relevance system prompt in Vietnamese."""
        prompt = self.cotrag_vi._get_low_relevance_system_prompt()
        self.assertIn("tài liệu được truy xuất có độ liên quan thấp", prompt)
        self.assertIn("kiến thức và khả năng suy luận của riêng bạn", prompt)

    def test_vietnamese_weighted_prompt(self):
        """Test creating weighted prompts in Vietnamese."""
        # Test with high CoT weight
        weights_high_cot = {"cot_weight": 0.8, "rag_weight": 0.2}
        prompt_high_cot = self.cotrag_vi._create_weighted_prompt(
            "Trí tuệ nhân tạo là gì?",
            "Nội dung test",
            weights_high_cot
        )
        self.assertIn("tập trung chủ yếu vào quá trình suy luận cẩn thận", prompt_high_cot)

        # Test with low CoT weight
        weights_low_cot = {"cot_weight": 0.2, "rag_weight": 0.8}
        prompt_low_cot = self.cotrag_vi._create_weighted_prompt(
            "Trí tuệ nhân tạo là gì?",
            "Nội dung test",
            weights_low_cot
        )
        self.assertIn("chủ yếu dựa trên thông tin được truy xuất", prompt_low_cot)


class TestCoTRAGAdaptiveLearning(unittest.TestCase):
    """Test case for the CoTRAGAdaptiveLearning implementation."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary file for feedback history
        self.temp_file = tempfile.NamedTemporaryFile(delete=False)
        self.temp_file.close()

        # Create mock RAG instance
        self.mock_vector_store = MagicMock()
        self.mock_vector_store.search.return_value = [
            {
                "content": "Artificial intelligence is a branch of computer science.",
                "metadata": {"source": "Test Source", "title": "Test Document"},
                "score": 0.85
            }
        ]

        # Create mock API provider
        self.mock_api_provider = MagicMock()
        self.mock_api_provider.generate.return_value = "First, I'll analyze the information. The document mentions that AI is a branch of computer science. Therefore, artificial intelligence is a field of computer science."

        # Create mock weight optimizer
        self.mock_weight_optimizer = MagicMock()
        self.mock_weight_optimizer.get_optimal_weights.return_value = {
            "cot_weight": 0.6,
            "rag_weight": 0.4
        }
        self.mock_weight_optimizer.analyze_query.return_value = {
            "query_type_analysis": {
                "query_type": "factual",
                "factual_count": 1,
                "reasoning_count": 0,
                "creative_count": 0
            }
        }

        # Create patches
        self.patches = [
            patch('src.deep_research_core.reasoning.cot_rag.get_vector_store', return_value=self.mock_vector_store),
            patch('src.deep_research_core.models.api.openai.openai_provider', self.mock_api_provider),
            patch('src.deep_research_core.models.api.anthropic.anthropic_provider', self.mock_api_provider),
            patch('src.deep_research_core.models.api.openrouter.openrouter_provider', self.mock_api_provider)
        ]

        # Start patches
        for p in self.patches:
            p.start()

        # Create CoTRAGAdaptiveLearning instance
        self.cotrag_adaptive = CoTRAGAdaptiveLearning(
            provider="openai",
            model="gpt-4o",
            learning_rate=0.05,
            feedback_history_path=self.temp_file.name,
            min_samples_for_adjustment=2,
            auto_adjust_interval=5,
            verbose=True
        )

        # Set mock weight optimizer
        self.cotrag_adaptive.weight_optimizer = self.mock_weight_optimizer

    def tearDown(self):
        """Tear down test fixtures."""
        # Stop patches
        for p in self.patches:
            p.stop()

        # Remove temporary file
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)

    def test_feedback_storage(self):
        """Test storing and loading feedback."""
        # Process a query
        result = self.cotrag_adaptive.process("What is AI?")

        # Add feedback
        self.cotrag_adaptive.add_feedback(
            query="What is AI?",
            result=result,
            feedback_score=0.9,
            feedback_type="user",
            feedback_notes="Good answer"
        )

        # Check that feedback was stored
        self.assertEqual(len(self.cotrag_adaptive.feedback_history), 1)
        self.assertEqual(self.cotrag_adaptive.feedback_history[0]["query"], "What is AI?")
        self.assertEqual(self.cotrag_adaptive.feedback_history[0]["feedback_score"], 0.9)
        self.assertEqual(self.cotrag_adaptive.feedback_history[0]["feedback_type"], "user")
        self.assertEqual(self.cotrag_adaptive.feedback_history[0]["feedback_notes"], "Good answer")

        # Check that feedback was saved to file
        with open(self.temp_file.name, "r") as f:
            saved_feedback = json.load(f)
        self.assertEqual(len(saved_feedback), 1)
        self.assertEqual(saved_feedback[0]["query"], "What is AI?")

    def test_parameter_adjustment(self):
        """Test parameter adjustment based on feedback."""
        # Process queries and add feedback
        for i in range(3):
            result = self.cotrag_adaptive.process(f"Query {i}")
            self.cotrag_adaptive.add_feedback(
                query=f"Query {i}",
                result=result,
                feedback_score=0.8,
                feedback_type="user"
            )

        # Get original parameters
        original_min_cot = self.cotrag_adaptive.min_cot_weight

        # Adjust parameters
        adjusted_params = self.cotrag_adaptive.adjust_parameters()

        # Check that parameters were adjusted
        self.assertNotEqual(original_min_cot, self.cotrag_adaptive.min_cot_weight)
        self.assertIn("min_cot_weight", adjusted_params)

    def test_parameter_stats(self):
        """Test getting parameter statistics."""
        # Process queries and add feedback
        for i in range(3):
            result = self.cotrag_adaptive.process(f"Query {i}")
            self.cotrag_adaptive.add_feedback(
                query=f"Query {i}",
                result=result,
                feedback_score=0.8,
                feedback_type="user"
            )

        # Get parameter stats
        stats = self.cotrag_adaptive.get_parameter_stats()

        # Check that stats contain expected fields
        self.assertIn("min_cot_weight", stats)
        self.assertIn("current_value", stats["min_cot_weight"])
        self.assertIn("average_value", stats["min_cot_weight"])

    def test_feedback_trends(self):
        """Test analyzing feedback trends."""
        # Process queries and add feedback
        for i in range(3):
            result = self.cotrag_adaptive.process(f"Query {i}")
            self.cotrag_adaptive.add_feedback(
                query=f"Query {i}",
                result=result,
                feedback_score=0.8,
                feedback_type="user"
            )

        # Get feedback trends
        trends = self.cotrag_adaptive.analyze_feedback_trends()

        # Check that trends contain expected fields
        self.assertIn("total_entries", trends)
        self.assertIn("average_score", trends)
        self.assertEqual(trends["total_entries"], 3)
        self.assertAlmostEqual(trends["average_score"], 0.8)

    def test_auto_feedback(self):
        """Test automatic feedback generation."""
        # Mock evaluator
        self.cotrag_adaptive.evaluator = MagicMock()
        self.cotrag_adaptive.evaluator.evaluate.return_value = {
            "metrics": {
                "overall_quality": 8.5
            }
        }

        # Process with auto feedback
        result = self.cotrag_adaptive.process("What is AI?", auto_feedback=True)

        # Check that auto feedback was generated
        self.assertIn("auto_feedback", result)
        self.assertAlmostEqual(result["auto_feedback"]["score"], 0.85)

        # Check that feedback was stored
        self.assertEqual(len(self.cotrag_adaptive.feedback_history), 1)
        self.assertEqual(self.cotrag_adaptive.feedback_history[0]["feedback_type"], "automatic")


class TestCoTRAGAdvancedStrategies(unittest.TestCase):
    """Test case for the CoTRAGAdvancedStrategies implementation."""

    def setUp(self):
        """Set up test fixtures."""
        # Create mock RAG instance
        self.mock_vector_store = MagicMock()
        self.mock_vector_store.search.return_value = [
            {
                "content": "Artificial intelligence is a branch of computer science.",
                "metadata": {"source": "Test Source", "title": "Test Document"},
                "score": 0.85
            }
        ]

        # Create mock API provider
        self.mock_api_provider = MagicMock()
        self.mock_api_provider.generate.return_value = "First, I'll analyze the information. The document mentions that AI is a branch of computer science. Therefore, artificial intelligence is a field of computer science."

        # Create patches
        self.patches = [
            patch('src.deep_research_core.reasoning.cot_rag.get_vector_store', return_value=self.mock_vector_store),
            patch('src.deep_research_core.models.api.openai.openai_provider', self.mock_api_provider),
            patch('src.deep_research_core.models.api.anthropic.anthropic_provider', self.mock_api_provider),
            patch('src.deep_research_core.models.api.openrouter.openrouter_provider', self.mock_api_provider)
        ]

        # Start patches
        for p in self.patches:
            p.start()

        # Create CoTRAGAdvancedStrategies instance
        self.cotrag_advanced = CoTRAGAdvancedStrategies(
            provider="openai",
            model="gpt-4o",
            use_query_expansion=True,
            use_iterative_retrieval=True,
            use_fallback_strategies=True,
            verbose=True
        )

    def tearDown(self):
        """Tear down test fixtures."""
        # Stop patches
        for p in self.patches:
            p.stop()

    def test_query_expansion(self):
        """Test query expansion."""
        # Mock query expansion
        expanded_queries = ["What is artificial intelligence?", "Define AI", "Explain artificial intelligence"]
        self.cotrag_advanced._expand_query = MagicMock(return_value=expanded_queries)

        # Process a query
        result = self.cotrag_advanced.process("What is AI?")

        # Check that query expansion was called
        self.cotrag_advanced._expand_query.assert_called_once()

        # Check that the vector store was called
        self.mock_vector_store.search.assert_called()

    def test_iterative_retrieval(self):
        """Test iterative retrieval."""
        # Mock iterative retrieval
        improved_documents = [
            {
                "content": "Artificial intelligence is the simulation of human intelligence by machines.",
                "metadata": {"source": "Better Source", "title": "Better Document"},
                "score": 0.95,
                "refined_query": "What is the definition of artificial intelligence?"
            }
        ]
        self.cotrag_advanced._iterative_retrieval = MagicMock(return_value=improved_documents)

        # Set up low relevance documents
        self.mock_vector_store.search.return_value = [
            {
                "content": "Computer science is a field of study.",
                "metadata": {"source": "Test Source", "title": "Test Document"},
                "score": 0.2
            }
        ]

        # Process a query
        result = self.cotrag_advanced.process("What is AI?")

        # Check that iterative retrieval was called
        self.cotrag_advanced._iterative_retrieval.assert_called_once()

    def test_fallback_strategy(self):
        """Test fallback strategy."""
        # Mock process to raise an exception
        original_process = self.cotrag_advanced.process
        self.cotrag_advanced.process = MagicMock(side_effect=Exception("Test error"))

        # Mock fallback strategy
        fallback_result = {
            "query": "What is AI?",
            "documents": [],
            "reasoning": "Fallback reasoning",
            "answer": "Fallback answer",
            "fallback": {"type": "pure_cot"}
        }
        self.cotrag_advanced._apply_fallback_strategy = MagicMock(return_value=fallback_result)

        # Process a query
        result = self.cotrag_advanced._apply_fallback_strategy("What is AI?")

        # Check that fallback strategy was called
        self.cotrag_advanced._apply_fallback_strategy.assert_called_once()

        # Restore original process method
        self.cotrag_advanced.process = original_process

    def test_enhanced_low_relevance_prompt(self):
        """Test enhanced low relevance system prompt."""
        prompt = self.cotrag_advanced._get_low_relevance_system_prompt()
        self.assertIn("low relevance to the query", prompt)
        self.assertIn("step-by-step reasoning approach", prompt)
        self.assertIn("different perspectives", prompt)


class TestCoTRAGEnhanced(unittest.TestCase):
    """Test case for the CoTRAGEnhanced implementation."""

    def setUp(self):
        """Set up test fixtures."""
        # Create mock implementations
        self.mock_cotrag = MagicMock()
        self.mock_cotrag.process.return_value = {
            "query": "What is AI?",
            "documents": [{"content": "AI is artificial intelligence."}],
            "reasoning": "AI stands for artificial intelligence.",
            "answer": "AI is artificial intelligence.",
            "latency": 1.0
        }
        self.mock_cotrag.analyze_query.return_value = {"complexity": 0.5}
        self.mock_cotrag.retrieve.return_value = [{"content": "AI is artificial intelligence."}]

        self.mock_adaptive_learning = MagicMock()
        self.mock_adaptive_learning.process.return_value = {
            "query": "What is AI?",
            "documents": [{"content": "AI is artificial intelligence."}],
            "reasoning": "AI stands for artificial intelligence.",
            "answer": "AI is artificial intelligence.",
            "latency": 1.0,
            "adaptive_learning": True
        }
        self.mock_adaptive_learning.add_feedback.return_value = None
        self.mock_adaptive_learning.adjust_parameters.return_value = {"min_cot_weight": 0.4}
        self.mock_adaptive_learning.get_parameter_stats.return_value = {"min_cot_weight": {"current_value": 0.4}}
        self.mock_adaptive_learning.analyze_feedback_trends.return_value = {"total_entries": 5}

        # Create patches
        self.patches = [
            patch('src.deep_research_core.reasoning.cotrag_enhanced.CoTRAGVietnamese', return_value=self.mock_cotrag),
            patch('src.deep_research_core.reasoning.cotrag_enhanced.CoTRAGAdvancedStrategies', return_value=self.mock_cotrag),
            patch('src.deep_research_core.reasoning.cotrag_enhanced.CoTRAG', return_value=self.mock_cotrag),
            patch('src.deep_research_core.reasoning.cotrag_enhanced.CoTRAGAdaptiveLearning', return_value=self.mock_adaptive_learning)
        ]

        # Start patches
        for p in self.patches:
            p.start()

    def tearDown(self):
        """Tear down test fixtures."""
        # Stop patches
        for p in self.patches:
            p.stop()

    def test_english_processing(self):
        """Test processing an English query."""
        # Create CoTRAGEnhanced instance
        cotrag_enhanced = CoTRAGEnhanced(
            provider="openai",
            model="gpt-4o",
            language="en",
            use_adaptive_learning=True,
            use_advanced_strategies=True,
            verbose=True
        )

        # Process a query
        result = cotrag_enhanced.process("What is AI?")

        # Check that adaptive learning was used
        self.mock_adaptive_learning.process.assert_called_once()

        # Check that the result contains expected fields
        self.assertEqual(result["query"], "What is AI?")
        self.assertEqual(result["language"], "en")
        self.assertTrue(result["enhanced"])
        self.assertTrue(result["use_adaptive_learning"])
        self.assertTrue(result["use_advanced_strategies"])

    def test_vietnamese_processing(self):
        """Test processing a Vietnamese query."""
        # Create CoTRAGEnhanced instance
        cotrag_enhanced = CoTRAGEnhanced(
            provider="openrouter",
            model="anthropic/claude-3-opus",
            language="vi",
            vietnamese_embedding_model="phobert",
            use_adaptive_learning=False,
            use_advanced_strategies=False,
            verbose=True
        )

        # Process a query
        result = cotrag_enhanced.process("Trí tuệ nhân tạo là gì?")

        # Check that the Vietnamese implementation was used
        self.mock_cotrag.process.assert_called_once()

        # Check that the result contains expected fields
        self.assertEqual(result["query"], "What is AI?")
        self.assertEqual(result["language"], "vi")
        self.assertTrue(result["enhanced"])
        self.assertFalse(result["use_adaptive_learning"])
        self.assertFalse(result["use_advanced_strategies"])

    def test_feedback_handling(self):
        """Test feedback handling."""
        # Create CoTRAGEnhanced instance
        cotrag_enhanced = CoTRAGEnhanced(
            provider="openai",
            model="gpt-4o",
            language="en",
            use_adaptive_learning=True,
            use_advanced_strategies=True,
            verbose=True
        )

        # Process a query
        result = cotrag_enhanced.process("What is AI?")

        # Add feedback
        cotrag_enhanced.add_feedback(
            query="What is AI?",
            result=result,
            feedback_score=0.9,
            feedback_type="user",
            feedback_notes="Good answer"
        )

        # Check that feedback was added
        self.mock_adaptive_learning.add_feedback.assert_called_once()

    def test_parameter_adjustment(self):
        """Test parameter adjustment."""
        # Create CoTRAGEnhanced instance
        cotrag_enhanced = CoTRAGEnhanced(
            provider="openai",
            model="gpt-4o",
            language="en",
            use_adaptive_learning=True,
            use_advanced_strategies=True,
            verbose=True
        )

        # Adjust parameters
        adjusted_params = cotrag_enhanced.adjust_parameters()

        # Check that parameters were adjusted
        self.mock_adaptive_learning.adjust_parameters.assert_called_once()
        self.assertEqual(adjusted_params, {"min_cot_weight": 0.4})

    def test_parameter_stats(self):
        """Test getting parameter statistics."""
        # Create CoTRAGEnhanced instance
        cotrag_enhanced = CoTRAGEnhanced(
            provider="openai",
            model="gpt-4o",
            language="en",
            use_adaptive_learning=True,
            use_advanced_strategies=True,
            verbose=True
        )

        # Get parameter stats
        stats = cotrag_enhanced.get_parameter_stats()

        # Check that stats were retrieved
        self.mock_adaptive_learning.get_parameter_stats.assert_called_once()
        self.assertEqual(stats, {"min_cot_weight": {"current_value": 0.4}})

    def test_feedback_trends(self):
        """Test analyzing feedback trends."""
        # Create CoTRAGEnhanced instance
        cotrag_enhanced = CoTRAGEnhanced(
            provider="openai",
            model="gpt-4o",
            language="en",
            use_adaptive_learning=True,
            use_advanced_strategies=True,
            verbose=True
        )

        # Get feedback trends
        trends = cotrag_enhanced.analyze_feedback_trends()

        # Check that trends were analyzed
        self.mock_adaptive_learning.analyze_feedback_trends.assert_called_once()
        self.assertEqual(trends, {"total_entries": 5})

    def test_query_analysis(self):
        """Test query analysis."""
        # Create CoTRAGEnhanced instance
        cotrag_enhanced = CoTRAGEnhanced(
            provider="openai",
            model="gpt-4o",
            language="en",
            use_adaptive_learning=True,
            use_advanced_strategies=True,
            verbose=True
        )

        # Analyze a query
        analysis = cotrag_enhanced.analyze_query("What is AI?")

        # Check that the query was analyzed
        self.mock_cotrag.analyze_query.assert_called_once()
        self.assertEqual(analysis, {"complexity": 0.5})

    def test_document_retrieval(self):
        """Test document retrieval."""
        # Create CoTRAGEnhanced instance
        cotrag_enhanced = CoTRAGEnhanced(
            provider="openai",
            model="gpt-4o",
            language="en",
            use_adaptive_learning=True,
            use_advanced_strategies=True,
            verbose=True
        )

        # Retrieve documents
        documents = cotrag_enhanced.retrieve("What is AI?")

        # Check that documents were retrieved
        self.mock_cotrag.retrieve.assert_called_once()
        self.assertEqual(documents, [{"content": "AI is artificial intelligence."}])


if __name__ == "__main__":
    unittest.main()
