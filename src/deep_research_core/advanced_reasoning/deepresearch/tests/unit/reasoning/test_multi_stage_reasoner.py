"""
Unit tests for the MultiStageReasoner class.
"""

import os
import json
import tempfile
import unittest
from unittest.mock import Magic<PERSON><PERSON>, patch

from deep_research_core.reasoning.multi_stage_reasoner import (
    MultiStageReasoner, 
    ReasoningStage
)
from deep_research_core.reasoning.base_reasoner import BaseReasoner


class Mock<PERSON><PERSON>oner(BaseReasoner):
    """Mock reasoner for testing."""
    
    def __init__(self, output=None, name="mock_reasoner"):
        super().__init__(name=name)
        self.output = output or {"result": "mock result"}
        self.called_with = None
    
    def reason(self, **kwargs):
        """Mock reason method."""
        self.called_with = kwargs
        return self.output


class TestMultiStageReasoner(unittest.TestCase):
    """Test cases for MultiStageReasoner."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_reasoner1 = MockReasoner(output={"result1": "result from stage 1"})
        self.mock_reasoner2 = MockReasoner(output={"result2": "result from stage 2"})
        self.mock_reasoner3 = MockReasoner(output={"final_answer": "final result"})
        
        # Create reasoning stages
        self.stage1 = ReasoningStage(
            name="stage1", 
            reasoner=self.mock_reasoner1,
            input_keys=["query"],
            description="First stage"
        )
        
        self.stage2 = ReasoningStage(
            name="stage2", 
            reasoner=self.mock_reasoner2,
            input_keys=["query", "result1"],
            description="Second stage"
        )
        
        self.stage3 = ReasoningStage(
            name="stage3", 
            reasoner=self.mock_reasoner3,
            input_keys=["result1", "result2"],
            description="Final stage"
        )
        
        # Create multi-stage reasoner
        self.reasoner = MultiStageReasoner(
            name="test_multi_stage",
            stages=[self.stage1, self.stage2, self.stage3],
            config={"cache_results": False}  # Disable caching for tests
        )
    
    def test_init(self):
        """Test initialization."""
        reasoner = MultiStageReasoner(name="test_reasoner")
        self.assertEqual(reasoner.name, "test_reasoner")
        self.assertEqual(reasoner.stages, [])
        self.assertEqual(reasoner.context, {})
        self.assertFalse(reasoner.config["continue_on_error"])
        self.assertTrue(reasoner.config["cache_results"])
    
    def test_add_stage(self):
        """Test adding a stage."""
        reasoner = MultiStageReasoner()
        stage = ReasoningStage(
            name="test_stage",
            reasoner=MockReasoner()
        )
        reasoner.add_stage(stage)
        self.assertEqual(len(reasoner.stages), 1)
        self.assertEqual(reasoner.stages[0], stage)
    
    def test_create_stage(self):
        """Test creating a stage."""
        with patch("deep_research_core.reasoning.multi_stage_reasoner.ReActReasoner") as mock_react:
            mock_react.return_value = MockReasoner()
            
            reasoner = MultiStageReasoner()
            stage = reasoner.create_stage(
                name="test_stage",
                reasoner_type="react",
                input_keys=["query"],
                description="Test stage"
            )
            
            self.assertEqual(stage.name, "test_stage")
            self.assertEqual(stage.input_keys, ["query"])
            self.assertEqual(stage.description, "Test stage")
            self.assertEqual(len(reasoner.stages), 1)
            mock_react.assert_called_once()
    
    def test_create_stage_unknown_type(self):
        """Test creating a stage with an unknown reasoner type."""
        reasoner = MultiStageReasoner()
        with self.assertRaises(ValueError):
            reasoner.create_stage(
                name="test_stage",
                reasoner_type="unknown_type"
            )
    
    def test_reason_basic(self):
        """Test basic reasoning flow."""
        result = self.reasoner.reason(query="test query")
        
        # Check that all stages were executed
        self.assertEqual(self.mock_reasoner1.called_with["query"], "test query")
        self.assertEqual(self.mock_reasoner2.called_with["query"], "test query")
        self.assertEqual(self.mock_reasoner2.called_with["result1"], "result from stage 1")
        self.assertEqual(self.mock_reasoner3.called_with["result1"], "result from stage 1")
        self.assertEqual(self.mock_reasoner3.called_with["result2"], "result from stage 2")
        
        # Check the final result
        self.assertEqual(result["final_output"], "final result")
        self.assertEqual(result["successful_stages"], 3)
        self.assertEqual(result["total_stages"], 3)
        self.assertEqual(len(result["stage_outputs"]), 3)
    
    def test_reason_with_error(self):
        """Test reasoning with an error in a stage."""
        # Make the second reasoner raise an exception
        def reason_with_error(**kwargs):
            raise ValueError("Test error")
        
        self.mock_reasoner2.reason = reason_with_error
        
        # Set continue_on_error to False
        self.reasoner.config["continue_on_error"] = False
        
        result = self.reasoner.reason(query="test query")
        
        # Check that execution stopped after the error
        self.assertEqual(self.mock_reasoner1.called_with["query"], "test query")
        self.assertIsNone(self.mock_reasoner3.called_with)
        
        # Check the result
        self.assertEqual(result["successful_stages"], 1)
        self.assertEqual(len(result["stage_outputs"]), 1)
        
        # Set continue_on_error to True
        self.reasoner.config["continue_on_error"] = True
        
        # Reset mock
        self.mock_reasoner3.called_with = None
        
        result = self.reasoner.reason(query="test query")
        
        # Check that execution continued after the error
        self.assertEqual(self.mock_reasoner1.called_with["query"], "test query")
        self.assertIsNotNone(self.mock_reasoner3.called_with)
        
        # Check the result
        self.assertEqual(result["successful_stages"], 2)
        self.assertEqual(len(result["stage_outputs"]), 2)
    
    def test_extract_final_output(self):
        """Test extracting the final output."""
        # Set up the context
        self.reasoner.context = {
            "result1": "result 1",
            "result2": "result 2",
            "other_key": "other value"
        }
        
        # Case 1: Final stage has final_answer
        self.mock_reasoner3.output = {"final_answer": "final answer"}
        self.stage3.outputs = self.mock_reasoner3.output
        self.stage3.completed = True
        
        result = self.reasoner._extract_final_output()
        self.assertEqual(result, "final answer")
        
        # Case 2: Final stage has answer
        self.mock_reasoner3.output = {"answer": "the answer"}
        self.stage3.outputs = self.mock_reasoner3.output
        
        result = self.reasoner._extract_final_output()
        self.assertEqual(result, "the answer")
        
        # Case 3: Final stage has result
        self.mock_reasoner3.output = {"result": "the result"}
        self.stage3.outputs = self.mock_reasoner3.output
        
        result = self.reasoner._extract_final_output()
        self.assertEqual(result, "the result")
        
        # Case 4: No specific output in final stage, but context has answer
        self.mock_reasoner3.output = {"other_key": "other value"}
        self.stage3.outputs = self.mock_reasoner3.output
        self.reasoner.context["answer"] = "context answer"
        
        result = self.reasoner._extract_final_output()
        self.assertEqual(result, "context answer")
        
        # Case 5: No specific output anywhere
        self.mock_reasoner3.output = {"other_key": "other value"}
        self.stage3.outputs = self.mock_reasoner3.output
        self.reasoner.context = {"other_key": "other value"}
        
        result = self.reasoner._extract_final_output()
        self.assertEqual(result, self.stage3.outputs)
    
    def test_adapt_pipeline(self):
        """Test adapting the pipeline."""
        # Set adaptive_stages to True
        self.reasoner.config["adaptive_stages"] = True
        
        # Set up a context with high complexity
        self.reasoner.context = {"complexity": 0.9}
        
        # Initial number of stages
        initial_stages = len(self.reasoner.stages)
        
        # Call _adapt_pipeline
        self.reasoner._adapt_pipeline()
        
        # Check that a verification stage was added
        self.assertEqual(len(self.reasoner.stages), initial_stages + 1)
        self.assertTrue("verification" in self.reasoner.stages[-1].name.lower())
        
        # Call _adapt_pipeline again, should not add another verification stage
        self.reasoner._adapt_pipeline()
        
        # Check that no new stage was added
        self.assertEqual(len(self.reasoner.stages), initial_stages + 1)
    
    def test_config_serialization(self):
        """Test saving and loading configuration."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = os.path.join(temp_dir, "config.json")
            
            # Save configuration
            config = self.reasoner.to_config(config_path)
            
            # Check that the file was created
            self.assertTrue(os.path.exists(config_path))
            
            # Load configuration
            new_reasoner = MultiStageReasoner()
            new_reasoner.from_config(config_path)
            
            # Check that the stages were loaded
            self.assertEqual(len(new_reasoner.stages), len(self.reasoner.stages))
            for i, stage in enumerate(new_reasoner.stages):
                self.assertEqual(stage.name, self.reasoner.stages[i].name)
                self.assertEqual(stage.description, self.reasoner.stages[i].description)
                self.assertEqual(stage.input_keys, self.reasoner.stages[i].input_keys)


if __name__ == "__main__":
    unittest.main() 