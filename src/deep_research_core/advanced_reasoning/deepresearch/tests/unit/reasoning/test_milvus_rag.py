"""
Unit tests for the MilvusRAG implementation.
"""

import unittest
from unittest.mock import MagicMock, patch, call
from typing import List, Dict, Any
import json
import numpy as np

from src.deep_research_core.reasoning.milvus_rag import MilvusRAG
from src.deep_research_core.utils.structured_logging import get_logger


class TestMilvusRAG(unittest.TestCase):
    """Test cases for the MilvusRAG implementation."""

    @patch("src.deep_research_core.retrieval.milvus_vector_store.MilvusVectorStore")
    @patch("src.deep_research_core.models.api.openai.openai_provider")
    def setUp(self, mock_openai_provider, mock_milvus_vector_store):
        """Set up test fixtures."""
        # Create a mock MilvusVectorStore
        self.mock_vector_store = MagicMock()
        mock_milvus_vector_store.return_value = self.mock_vector_store

        # Create a mock OpenAI provider
        self.mock_api_provider = MagicMock()
        mock_openai_provider.return_value = self.mock_api_provider

        # Initialize MilvusRAG
        self.rag = MilvusRAG(
            provider="openai",
            model="gpt-4o",
            temperature=0.7,
            max_tokens=2000,
            embedding_model="text-embedding-ada-002",
            collection_name="test_collection",
            connection_args={"host": "localhost", "port": "19530"},
            embedding_dim=1536,
            top_k=5
        )

        # Sample documents for testing
        self.sample_documents = [
            {
                "content": "This is a test document.",
                "source": "Test Source",
                "title": "Test Document"
            },
            {
                "content": "This is another test document.",
                "source": "Test Source 2",
                "title": "Test Document 2"
            }
        ]

        # Sample embeddings
        self.sample_embeddings = [
            [0.1] * 1536,
            [0.2] * 1536
        ]

        # Sample search results
        self.sample_search_results = [
            {
                "content": "This is a test document.",
                "source": "Test Source",
                "title": "Test Document",
                "score": 0.95
            },
            {
                "content": "This is another test document.",
                "source": "Test Source 2",
                "title": "Test Document 2",
                "score": 0.85
            }
        ]

        # Set up mock behavior
        self.mock_vector_store.add_documents.return_value = [1, 2]
        self.mock_vector_store.search.return_value = self.sample_search_results
        self.mock_vector_store.count.return_value = 2

        # Mock the _get_embeddings method
        self.rag._get_embeddings = MagicMock(return_value=self.sample_embeddings)

        # Mock the API provider
        self.rag.api_provider.complete = MagicMock(return_value="This is a test answer.")

    def test_initialization(self):
        """Test initialization of MilvusRAG."""
        self.assertEqual(self.rag.provider, "openai")
        self.assertEqual(self.rag.model, "gpt-4o")
        self.assertEqual(self.rag.temperature, 0.7)
        self.assertEqual(self.rag.max_tokens, 2000)
        self.assertEqual(self.rag.embedding_model, "text-embedding-ada-002")
        self.assertEqual(self.rag.top_k, 5)
        self.assertIsNotNone(self.rag.vector_store)

    def test_add_documents(self):
        """Test adding documents."""
        result = self.rag.add_documents(self.sample_documents)

        # Check that _get_embeddings was called with the correct arguments
        self.rag._get_embeddings.assert_called_once_with(
            ["This is a test document.", "This is another test document."]
        )

        # Check that vector_store.add_documents was called with the correct arguments
        self.mock_vector_store.add_documents.assert_called_once_with(
            self.sample_documents, self.sample_embeddings
        )

        # Check the result
        self.assertEqual(result, [1, 2])

    def test_search(self):
        """Test searching for documents."""
        # Mock the _get_embeddings method for the query
        query_embedding = [0.3] * 1536
        self.rag._get_embeddings = MagicMock(return_value=[query_embedding])

        # Call the search method
        results = self.rag.search("Test query")

        # Check that _get_embeddings was called with the correct arguments
        self.rag._get_embeddings.assert_called_once_with(["Test query"])

        # Check that vector_store.search was called with the correct arguments
        self.mock_vector_store.search.assert_called_once_with(
            query_embedding=query_embedding,
            top_k=5,
            filter_expr=None
        )

        # Check the results
        self.assertEqual(results, self.sample_search_results)

    def test_process(self):
        """Test processing a query."""
        # Mock the search method
        self.rag.search = MagicMock(return_value=self.sample_search_results)

        # Mock the _format_documents method
        formatted_docs = "Document 1:\nContent: This is a test document.\n..."
        self.rag._format_documents = MagicMock(return_value=formatted_docs)

        # Mock the _create_system_prompt method
        system_prompt = "You are a helpful assistant..."
        self.rag._create_system_prompt = MagicMock(return_value=system_prompt)

        # Mock the _create_user_prompt method
        user_prompt = "Question: Test query\nRetrieved Documents:..."
        self.rag._create_user_prompt = MagicMock(return_value=user_prompt)

        # Call the process method
        result = self.rag.process("Test query")

        # Check that search was called with the correct arguments
        self.rag.search.assert_called_once_with("Test query", filter_expr=None)

        # Check that _format_documents was called with the correct arguments
        self.rag._format_documents.assert_called_once_with(self.sample_search_results)

        # Check that _create_system_prompt was called
        self.rag._create_system_prompt.assert_called_once()

        # Check that _create_user_prompt was called with the correct arguments
        self.rag._create_user_prompt.assert_called_once_with("Test query", formatted_docs)

        # Check that api_provider.complete was called with the correct arguments
        self.rag.api_provider.complete.assert_called_once_with(
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            model="gpt-4o",
            temperature=0.7,
            max_tokens=2000,
            stream=False,
            callback=None
        )

        # Check the result
        self.assertEqual(result["query"], "Test query")
        self.assertEqual(result["answer"], "This is a test answer.")
        self.assertEqual(result["documents"], self.sample_search_results)
        self.assertEqual(result["system_prompt"], system_prompt)
        self.assertEqual(result["user_prompt"], user_prompt)
        self.assertEqual(result["model"], "gpt-4o")
        self.assertEqual(result["provider"], "openai")

    def test_clear(self):
        """Test clearing documents."""
        self.rag.clear()
        self.mock_vector_store.clear.assert_called_once()

    def test_count(self):
        """Test counting documents."""
        count = self.rag.count()
        self.mock_vector_store.count.assert_called_once()
        self.assertEqual(count, 2)

    def test_close(self):
        """Test closing the connection."""
        self.rag.close()
        self.mock_vector_store.close.assert_called_once()


    def test_process_with_callback(self):
        """Test processing a query with a callback function."""
        # Mock the search method
        self.rag.search = MagicMock(return_value=self.sample_search_results)

        # Mock the _format_documents method
        formatted_docs = "Document 1:\nContent: This is a test document.\n..."
        self.rag._format_documents = MagicMock(return_value=formatted_docs)

        # Mock the _create_system_prompt method
        system_prompt = "You are a helpful assistant..."
        self.rag._create_system_prompt = MagicMock(return_value=system_prompt)

        # Mock the _create_user_prompt method
        user_prompt = "Question: Test query\nRetrieved Documents:..."
        self.rag._create_user_prompt = MagicMock(return_value=user_prompt)

        # Create a callback function
        callback_results = []
        def callback(content):
            callback_results.append(content)

        # Call the process method with the callback
        result = self.rag.process("Test query", callback=callback)

        # Check that api_provider.complete was called with the callback
        self.rag.api_provider.complete.assert_called_once_with(
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            model="gpt-4o",
            temperature=0.7,
            max_tokens=2000,
            stream=True,
            callback=callback
        )

    def test_process_with_filter(self):
        """Test processing a query with a filter expression."""
        # Mock the search method
        self.rag.search = MagicMock(return_value=self.sample_search_results)

        # Call the process method with a filter
        filter_expr = "source == 'Test Source'"
        result = self.rag.process("Test query", filter_expr=filter_expr)

        # Check that search was called with the filter
        self.rag.search.assert_called_once_with("Test query", filter_expr=filter_expr)

    def test_process_with_custom_prompts(self):
        """Test processing a query with custom prompts."""
        # Mock the search method
        self.rag.search = MagicMock(return_value=self.sample_search_results)

        # Mock the _format_documents method
        formatted_docs = "Document 1:\nContent: This is a test document.\n..."
        self.rag._format_documents = MagicMock(return_value=formatted_docs)

        # Custom prompts
        custom_system_prompt = "You are a custom assistant..."
        custom_user_prompt = "Custom question: {query}\n{documents}"

        # Call the process method with custom prompts
        result = self.rag.process(
            "Test query",
            custom_system_prompt=custom_system_prompt,
            custom_user_prompt=custom_user_prompt
        )

        # Check that api_provider.complete was called with the custom prompts
        self.rag.api_provider.complete.assert_called_once_with(
            system_prompt=custom_system_prompt,
            user_prompt=custom_user_prompt.format(query="Test query", documents=formatted_docs),
            model="gpt-4o",
            temperature=0.7,
            max_tokens=2000,
            stream=False,
            callback=None
        )

    def test_error_handling_add_documents(self):
        """Test error handling when adding documents."""
        # Make the vector store raise an exception
        self.mock_vector_store.add_documents.side_effect = Exception("Test error")

        # Call the add_documents method and check that it raises an exception
        with self.assertRaises(Exception):
            self.rag.add_documents(self.sample_documents)

    def test_error_handling_search(self):
        """Test error handling when searching."""
        # Make the vector store raise an exception
        self.mock_vector_store.search.side_effect = Exception("Test error")

        # Mock the _get_embeddings method for the query
        query_embedding = [0.3] * 1536
        self.rag._get_embeddings = MagicMock(return_value=[query_embedding])

        # Call the search method and check that it raises an exception
        with self.assertRaises(Exception):
            self.rag.search("Test query")

    def test_error_handling_process(self):
        """Test error handling when processing a query."""
        # Make the search method raise an exception
        self.rag.search = MagicMock(side_effect=Exception("Test error"))

        # Call the process method and check that it returns an error message
        result = self.rag.process("Test query")

        # Check the result
        self.assertEqual(result["query"], "Test query")
        self.assertIn("error", result)
        self.assertIn("Test error", result["error"])

    def test_format_documents(self):
        """Test formatting documents for the prompt."""
        # Call the _format_documents method
        formatted_docs = self.rag._format_documents(self.sample_search_results)

        # Check the formatted documents
        self.assertIn("Document 1", formatted_docs)
        self.assertIn("This is a test document.", formatted_docs)
        self.assertIn("Document 2", formatted_docs)
        self.assertIn("This is another test document.", formatted_docs)

    def test_create_system_prompt(self):
        """Test creating the system prompt."""
        # Call the _create_system_prompt method
        system_prompt = self.rag._create_system_prompt()

        # Check the system prompt
        self.assertIn("You are a helpful assistant", system_prompt)

    def test_create_user_prompt(self):
        """Test creating the user prompt."""
        # Call the _create_user_prompt method
        user_prompt = self.rag._create_user_prompt("Test query", "Formatted documents")

        # Check the user prompt
        self.assertIn("Test query", user_prompt)
        self.assertIn("Formatted documents", user_prompt)

    def test_get_embeddings(self):
        """Test getting embeddings for texts."""
        # Reset the mock
        self.rag._get_embeddings = MilvusRAG._get_embeddings.__get__(self.rag, MilvusRAG)

        # Mock the OpenAI API
        with patch("src.deep_research_core.models.api.openai.openai.Embedding.create") as mock_create:
            mock_create.return_value = {
                "data": [
                    {"embedding": [0.1] * 1536},
                    {"embedding": [0.2] * 1536}
                ]
            }

            # Call the _get_embeddings method
            embeddings = self.rag._get_embeddings(["Text 1", "Text 2"])

            # Check the embeddings
            self.assertEqual(len(embeddings), 2)
            self.assertEqual(len(embeddings[0]), 1536)
            self.assertEqual(len(embeddings[1]), 1536)

    def test_logging(self):
        """Test that logging is used correctly."""
        # Mock the logger
        with patch("src.deep_research_core.reasoning.milvus_rag.logger") as mock_logger:
            # Call methods that should log
            self.rag.add_documents(self.sample_documents)
            self.rag.search("Test query")
            self.rag.process("Test query")

            # Check that logger.info was called
            mock_logger.info.assert_called()

if __name__ == "__main__":
    unittest.main()
