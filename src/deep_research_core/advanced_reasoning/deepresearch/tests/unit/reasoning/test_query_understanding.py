"""
Unit tests for the Advanced Query Understanding module.
"""

import unittest
from unittest.mock import patch, MagicMock
import json
from typing import Dict, Any, List

from deep_research_core.reasoning.query_understanding import QueryUnderstanding


class TestQueryUnderstanding(unittest.TestCase):
    """Test cases for the QueryUnderstanding class."""

    def setUp(self):
        """Set up test environment before each test."""
        self.query_understanding = QueryUnderstanding(
            provider="openai",
            model="gpt-4o",
            temperature=0.3,
            enable_entity_extraction=True,
            enable_intent_detection=True,
            enable_semantic_analysis=True,
            enable_query_expansion=True
        )
        
        # Sample queries
        self.sample_queries = [
            "What is the capital of France?",
            "How do solar panels work?",
            "Compare the performance of EVs versus gas cars",
            "What's the best recipe for chocolate chip cookies?",
            "Who was the first person to land on the moon?"
        ]

    @patch("deep_research_core.models.api.openai.generate")
    def test_analyze_query(self, mock_generate):
        """Test analyzing a query."""
        # Mock the API response
        mock_response = json.dumps({
            "intent": "information_seeking",
            "semantic_analysis": "The query is seeking factual information about the capital city of France.",
            "entities": [
                {"name": "France", "type": "country", "relevance": 0.9}
            ],
            "context_requirements": ["geographical knowledge"],
            "expanded_queries": [
                "What city is the capital of France?",
                "What is Paris, the capital of France?",
                "France's capital city information"
            ],
            "query_type": "factual",
            "complexity": 0.2,
            "ambiguity_score": 0.1,
            "suggested_retrieval_strategy": "direct_lookup"
        })
        mock_generate.return_value = mock_response
        
        # Analyze the query
        result = self.query_understanding.analyze(
            query="What is the capital of France?"
        )
        
        # Assertions
        self.assertEqual(result["intent"], "information_seeking")
        self.assertEqual(result["query_type"], "factual")
        self.assertLessEqual(result["complexity"], 0.5)  # Simple question should have low complexity
        self.assertGreaterEqual(len(result["entities"]), 1)
        self.assertEqual(result["entities"][0]["name"], "France")
        self.assertEqual(len(result["expanded_queries"]), 3)
        
        # Verify the API was called with the correct parameters
        mock_generate.assert_called_once()
        
        # Check that the query was stored in history
        self.assertIn("What is the capital of France?", self.query_understanding.query_history)

    @patch("deep_research_core.models.api.openai.generate")
    def test_analyze_query_with_context(self, mock_generate):
        """Test analyzing a query with context."""
        # Mock the API response
        mock_response = json.dumps({
            "intent": "comparison",
            "semantic_analysis": "The query is asking for a comparison between electric vehicles and gasoline-powered cars in terms of performance.",
            "entities": [
                {"name": "EVs", "type": "vehicle_type", "relevance": 0.9},
                {"name": "gas cars", "type": "vehicle_type", "relevance": 0.9}
            ],
            "context_requirements": ["automotive knowledge", "performance metrics"],
            "expanded_queries": [
                "Compare performance metrics between electric vehicles and gasoline cars",
                "EV vs gas car performance comparison",
                "How do electric cars perform compared to gasoline vehicles?"
            ],
            "query_type": "comparison",
            "complexity": 0.6,
            "ambiguity_score": 0.4,
            "suggested_retrieval_strategy": "multi_document_synthesis"
        })
        mock_generate.return_value = mock_response
        
        # Context information
        context = {
            "user_profile": "automotive enthusiast",
            "previous_topics": "renewable energy, sustainability"
        }
        
        # Analyze the query with context
        result = self.query_understanding.analyze(
            query="Compare the performance of EVs versus gas cars",
            context=context
        )
        
        # Assertions
        self.assertEqual(result["intent"], "comparison")
        self.assertEqual(result["query_type"], "comparison")
        self.assertGreaterEqual(result["complexity"], 0.5)  # Comparison query should be more complex
        self.assertEqual(len(result["entities"]), 2)
        self.assertEqual(result["suggested_retrieval_strategy"], "multi_document_synthesis")
        
        # Check that the query was stored in history
        self.assertIn("Compare the performance of EVs versus gas cars", self.query_understanding.query_history)

    @patch("deep_research_core.models.api.openai.generate")
    def test_query_with_conversation_history(self, mock_generate):
        """Test analyzing a query with conversation history."""
        # Mock the API response
        mock_response = json.dumps({
            "intent": "followup_question",
            "semantic_analysis": "This is a follow-up question related to the previous conversation about France.",
            "entities": [
                {"name": "population", "type": "demographic_metric", "relevance": 0.9},
                {"name": "France", "type": "country", "relevance": 0.7, "from_context": True}
            ],
            "context_requirements": ["previous conversation context", "demographic data"],
            "expanded_queries": [
                "What is the population of France?",
                "How many people live in France?",
                "France population statistics"
            ],
            "query_type": "factual",
            "complexity": 0.3,
            "ambiguity_score": 0.3,
            "suggested_retrieval_strategy": "direct_lookup_with_context"
        })
        mock_generate.return_value = mock_response
        
        # Conversation history
        conversation_history = [
            {"role": "user", "content": "What is the capital of France?"},
            {"role": "system", "content": "The capital of France is Paris."},
            {"role": "user", "content": "What's its population?"}
        ]
        
        # Analyze the query with conversation history
        result = self.query_understanding.analyze(
            query="What's its population?",
            conversation_history=conversation_history
        )
        
        # Assertions
        self.assertEqual(result["intent"], "followup_question")
        self.assertIn("France", [entity["name"] for entity in result["entities"]])
        self.assertEqual(result["suggested_retrieval_strategy"], "direct_lookup_with_context")
        
        # Verify the API was called with the correct parameters
        mock_generate.assert_called_once()

    @patch("deep_research_core.models.api.openai.generate")
    def test_expand_query(self, mock_generate):
        """Test expanding a query."""
        # Set up mock responses
        mock_analysis = json.dumps({
            "intent": "recipe_seeking",
            "entities": [
                {"name": "chocolate chip cookies", "type": "food", "relevance": 0.95}
            ],
            "expanded_queries": []  # Empty to force separate expansion call
        })
        
        mock_expansions = """
        Best homemade chocolate chip cookie recipes
        How to make the perfect chocolate chip cookies
        Classic chocolate chip cookie recipe with tips
        """
        
        mock_generate.side_effect = [mock_analysis, mock_expansions]
        
        # First analyze the query (should have no expansions)
        self.query_understanding.analyze(
            query="What's the best recipe for chocolate chip cookies?"
        )
        
        # Then expand the query
        expansions = self.query_understanding.expand_query(
            query="What's the best recipe for chocolate chip cookies?",
            expansion_type="semantic",
            max_expansions=3
        )
        
        # Assertions
        self.assertEqual(len(expansions), 3)
        self.assertTrue(any("homemade" in exp.lower() for exp in expansions))
        self.assertTrue(any("perfect" in exp.lower() for exp in expansions))
        
        # Verify the API was called twice (once for analysis, once for expansion)
        self.assertEqual(mock_generate.call_count, 2)

    @patch("deep_research_core.models.api.openai.generate")
    def test_extract_entities(self, mock_generate):
        """Test extracting entities from a query."""
        # Mock the API response
        mock_response = json.dumps({
            "intent": "historical_information",
            "entities": [
                {"name": "first person", "type": "concept", "relevance": 0.7},
                {"name": "moon", "type": "celestial_body", "relevance": 0.9},
                {"name": "land", "type": "action", "relevance": 0.8}
            ]
        })
        mock_generate.return_value = mock_response
        
        # Extract entities
        entities = self.query_understanding.extract_entities(
            query="Who was the first person to land on the moon?"
        )
        
        # Assertions
        self.assertEqual(len(entities), 3)
        self.assertEqual(entities[1]["name"], "moon")
        self.assertEqual(entities[1]["type"], "celestial_body")
        self.assertGreaterEqual(entities[1]["relevance"], 0.9)
        
        # Verify we called the API
        mock_generate.assert_called_once()

    @patch("deep_research_core.models.api.openai.generate")
    def test_detect_intent(self, mock_generate):
        """Test detecting intent of a query."""
        # Mock the API response
        mock_response = json.dumps({
            "intent": "scientific_explanation",
            "query_type": "process_explanation",
            "ambiguity_score": 0.2
        })
        mock_generate.return_value = mock_response
        
        # Detect intent
        intent_info = self.query_understanding.detect_intent(
            query="How do solar panels work?"
        )
        
        # Assertions
        self.assertEqual(intent_info["intent"], "scientific_explanation")
        self.assertEqual(intent_info["query_type"], "process_explanation")
        self.assertGreaterEqual(intent_info["confidence"], 0.7)  # 1.0 - ambiguity_score
        
        # Verify we called the API
        mock_generate.assert_called_once()

    @patch("deep_research_core.models.api.openai.generate")
    def test_get_retrieval_strategy(self, mock_generate):
        """Test getting retrieval strategy for a query."""
        # Mock the API response
        mock_response = json.dumps({
            "suggested_retrieval_strategy": "semantic_search_with_filtering",
            "expanded_queries": [
                "How do photovoltaic cells in solar panels convert sunlight to electricity?",
                "Scientific explanation of solar panel functionality"
            ],
            "context_requirements": ["physics knowledge", "renewable energy context"],
            "complexity": 0.7
        })
        mock_generate.return_value = mock_response
        
        # Get retrieval strategy
        strategy_info = self.query_understanding.get_retrieval_strategy(
            query="How do solar panels work?"
        )
        
        # Assertions
        self.assertEqual(strategy_info["strategy"], "semantic_search_with_filtering")
        self.assertEqual(len(strategy_info["expanded_queries"]), 2)
        self.assertEqual(len(strategy_info["context_requirements"]), 2)
        self.assertEqual(strategy_info["complexity"], 0.7)
        
        # Verify we called the API
        mock_generate.assert_called_once()

    def test_query_caching(self):
        """Test that similar queries use cached results."""
        # First, mock the analysis for the initial query
        with patch("deep_research_core.models.api.openai.generate") as mock_generate:
            mock_generate.return_value = json.dumps({
                "intent": "information_seeking",
                "entities": [{"name": "France", "type": "country", "relevance": 0.9}],
                "expanded_queries": ["What is the capital city of France?"]
            })
            
            # Analyze the initial query
            self.query_understanding.analyze(query="What is the capital of France?")
        
        # Now try a similar query - should use cache without calling the API
        with patch("deep_research_core.models.api.openai.generate") as mock_generate:
            # Analyze a similar query
            result = self.query_understanding.analyze(query="What's the capital of France?")
            
            # Verify we didn't call the API again
            mock_generate.assert_not_called()
            
            # Verify the result is from cache
            self.assertTrue(result["is_cached"])
            self.assertEqual(result["original_query"], "What is the capital of France?")

    def test_parsing_expanded_queries(self):
        """Test parsing expanded queries from different formats."""
        # Test with newline-separated list
        newline_result = """
        What is the capital city of France?
        Paris France capital information
        Capital of the French Republic
        """
        parsed_newline = self.query_understanding._parse_expanded_queries(newline_result)
        self.assertEqual(len(parsed_newline), 3)
        
        # Test with numbered list
        numbered_result = """
        1. What is the capital city of France?
        2. Paris France capital information
        3. Capital of the French Republic
        """
        parsed_numbered = self.query_understanding._parse_expanded_queries(numbered_result)
        self.assertEqual(len(parsed_numbered), 3)
        self.assertEqual(parsed_numbered[0], "What is the capital city of France?")
        
        # Test with JSON array
        json_result = """
        [
            "What is the capital city of France?",
            "Paris France capital information",
            "Capital of the French Republic"
        ]
        """
        parsed_json = self.query_understanding._parse_expanded_queries(json_result)
        self.assertEqual(len(parsed_json), 3)

    def test_find_similar_query(self):
        """Test finding similar queries in history."""
        # Add some queries to history
        self.query_understanding.query_history = {
            "What is the capital of France?": {"intent": "information_seeking"},
            "How do solar panels work?": {"intent": "scientific_explanation"},
            "Compare EVs and gas cars": {"intent": "comparison"}
        }
        
        # Test exact match
        exact_match = self.query_understanding._find_similar_query("What is the capital of France?")
        self.assertEqual(exact_match, "What is the capital of France?")
        
        # Test similar match
        similar_match = self.query_understanding._find_similar_query("What's the capital of France?")
        self.assertEqual(similar_match, "What is the capital of France?")
        
        # Test no match
        no_match = self.query_understanding._find_similar_query("What is the population of Germany?")
        self.assertIsNone(no_match)


if __name__ == "__main__":
    unittest.main() 