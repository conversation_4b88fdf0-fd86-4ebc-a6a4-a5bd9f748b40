"""
Unit tests for the Evidence-Based Verification module.
"""

import unittest
from unittest.mock import patch, MagicMock
import json
import time
from typing import Dict, Any, List

from deep_research_core.reasoning.evidence_verifier import EvidenceVerifier


class TestEvidenceVerifier(unittest.TestCase):
    """Test cases for the EvidenceVerifier class."""

    def setUp(self):
        """Set up test environment before each test."""
        self.verifier = EvidenceVerifier(
            provider="openai",
            model="gpt-4o",
            minimum_evidence_count=2,
            confidence_threshold=0.7,
            verification_mode="balanced"
        )
        
        # Sample evidence items
        self.evidence_items = [
            {
                "id": "source_1",
                "content": "Earth is the third planet from the Sun and the only astronomical object known to harbor life.",
                "metadata": {
                    "type": "scientific_journal",
                    "year": 2022,
                    "author_credentials": "PhD in Astrophysics"
                }
            },
            {
                "id": "source_2",
                "content": "The Earth is the fifth largest planet in the solar system and is the only planet known to have liquid water on its surface.",
                "metadata": {
                    "type": "academic",
                    "year": 2021
                }
            },
            {
                "id": "source_3",
                "content": "Some people believe the Earth is flat, despite overwhelming evidence to the contrary.",
                "metadata": {
                    "type": "blog",
                    "year": 2020
                }
            }
        ]

    @patch("deep_research_core.models.api.openai.generate")
    def test_verify_claim_with_sufficient_evidence(self, mock_generate):
        """Test verifying a claim with sufficient evidence."""
        # Mock the API response
        mock_response = json.dumps({
            "verified": True,
            "confidence": 0.85,
            "reasoning": "Multiple reliable sources confirm that Earth is the third planet from the Sun.",
            "supporting_evidence": [
                {"id": "source_1", "relevance": 0.9, "excerpt": "Earth is the third planet from the Sun"},
                {"id": "source_2", "relevance": 0.7, "excerpt": "The Earth is the fifth largest planet in the solar system"}
            ],
            "contradicting_evidence": [],
            "uncertainty_factors": []
        })
        mock_generate.return_value = mock_response
        
        # Verify the claim
        result = self.verifier.verify(
            claim="Earth is the third planet from the Sun.",
            evidence=self.evidence_items
        )
        
        # Assertions
        self.assertTrue(result["verified"])
        self.assertGreaterEqual(result["confidence"], 0.7)
        self.assertEqual(len(result["supporting_evidence"]), 2)
        self.assertEqual(len(result["contradicting_evidence"]), 0)
        
        # Verify the API was called with the correct parameters
        mock_generate.assert_called_once()
        
        # Check that verification ID was generated
        self.assertIn("verification_id", result)
        self.assertTrue(result["verification_id"].startswith("verif_"))

    @patch("deep_research_core.models.api.openai.generate")
    def test_verify_claim_with_contradicting_evidence(self, mock_generate):
        """Test verifying a claim with contradicting evidence."""
        # Mock the API response
        mock_response = json.dumps({
            "verified": False,
            "confidence": 0.3,
            "reasoning": "There is contradicting evidence about the claim.",
            "supporting_evidence": [
                {"id": "source_1", "relevance": 0.9, "excerpt": "Earth is the third planet from the Sun"}
            ],
            "contradicting_evidence": [
                {"id": "source_3", "relevance": 0.6, "excerpt": "Some people believe the Earth is flat"}
            ],
            "uncertainty_factors": ["Source 3 appears to be unreliable"]
        })
        mock_generate.return_value = mock_response
        
        # Verify the claim
        result = self.verifier.verify(
            claim="The Earth is flat.",
            evidence=self.evidence_items
        )
        
        # Assertions
        self.assertFalse(result["verified"])
        self.assertLess(result["confidence"], 0.7)
        self.assertEqual(len(result["supporting_evidence"]), 1)
        self.assertEqual(len(result["contradicting_evidence"]), 1)
        
        # Verify the verification result was stored
        verification_id = result["verification_id"]
        stored_result = self.verifier.get_verification_result(verification_id)
        self.assertEqual(stored_result["verified"], False)

    def test_verification_with_insufficient_evidence(self):
        """Test verification with insufficient evidence."""
        # Single evidence item (below minimum)
        result = self.verifier.verify(
            claim="Earth is the third planet from the Sun.",
            evidence=[self.evidence_items[0]]  # Only one evidence item
        )
        
        # Assertions
        self.assertFalse(result["verified"])
        self.assertEqual(result["confidence"], 0.0)
        self.assertIn("Insufficient evidence", result["reasoning"])
        self.assertEqual(result["evidence_count"], 1)

    @patch("deep_research_core.models.api.openai.generate")
    def test_batch_verify(self, mock_generate):
        """Test batch verification of multiple claims."""
        # Mock the API responses for two different claims
        mock_responses = [
            json.dumps({
                "verified": True,
                "confidence": 0.85,
                "reasoning": "Verified claim 1",
                "supporting_evidence": [{"id": "source_1", "relevance": 0.9, "excerpt": "relevant excerpt"}],
                "contradicting_evidence": [],
                "uncertainty_factors": []
            }),
            json.dumps({
                "verified": False,
                "confidence": 0.3,
                "reasoning": "Rejected claim 2",
                "supporting_evidence": [],
                "contradicting_evidence": [{"id": "source_2", "relevance": 0.8, "excerpt": "contradicts claim"}],
                "uncertainty_factors": ["uncertainty factor"]
            })
        ]
        mock_generate.side_effect = mock_responses
        
        # Batch verify two claims
        results = self.verifier.batch_verify(
            claims=[
                "Earth is the third planet from the Sun.",
                "The Sun revolves around the Earth."
            ],
            evidence=self.evidence_items
        )
        
        # Assertions
        self.assertEqual(len(results), 2)
        self.assertTrue(results[0]["verified"])
        self.assertFalse(results[1]["verified"])
        
        # Verify the API was called twice
        self.assertEqual(mock_generate.call_count, 2)

    def test_evidence_quality_evaluation(self):
        """Test evaluating evidence quality."""
        quality_metrics = self.verifier.evaluate_evidence_quality(self.evidence_items)
        
        # Assertions
        self.assertEqual(quality_metrics["total_evidence"], 3)
        self.assertGreaterEqual(quality_metrics["average_quality"], 0.0)
        self.assertLessEqual(quality_metrics["average_quality"], 1.0)
        self.assertIn("source_types", quality_metrics)
        
        # High-quality sources should include scientific_journal and academic
        self.assertGreaterEqual(len(quality_metrics["high_quality_sources"]), 1)
        
        # Low-quality sources might include blog
        if quality_metrics["low_quality_sources"]:
            self.assertIn("source_3", quality_metrics["low_quality_sources"])

    def test_evidence_source_rating(self):
        """Test rating evidence sources."""
        # Scientific journal should get high rating
        scientific_rating = self.verifier._rate_evidence_source(self.evidence_items[0])
        
        # Blog should get lower rating
        blog_rating = self.verifier._rate_evidence_source(self.evidence_items[2])
        
        # Assertions
        self.assertGreater(scientific_rating, blog_rating)
        self.assertGreaterEqual(scientific_rating, 0.6)  # Should be well above average
        self.assertLessEqual(blog_rating, 0.5)  # Should be average or below

    def test_verification_prompt_generation(self):
        """Test generation of verification prompts."""
        # Test with balanced mode
        balanced_prompt = self.verifier._generate_verification_prompt(
            claim="Earth is the third planet from the Sun.",
            evidence=self.evidence_items
        )
        
        # Test with strict mode
        self.verifier.verification_mode = "strict"
        strict_prompt = self.verifier._generate_verification_prompt(
            claim="Earth is the third planet from the Sun.",
            evidence=self.evidence_items
        )
        
        # Assertions
        self.assertIn("Claim to verify: Earth is the third planet from the Sun.", balanced_prompt)
        self.assertIn("Evidence #1", balanced_prompt)
        self.assertIn("balanced verification mode", balanced_prompt)
        self.assertIn("strict verification mode", strict_prompt)
        
        # Test with context
        context_prompt = self.verifier._generate_verification_prompt(
            claim="Earth is the third planet from the Sun.",
            evidence=self.evidence_items,
            context={"domain": "astronomy", "relevance": "high"}
        )
        
        self.assertIn("Additional context", context_prompt)
        self.assertIn("domain: astronomy", context_prompt)

    def test_parse_verification_result(self):
        """Test parsing verification results from different formats."""
        # Test with clean JSON
        clean_json = '{"verified": true, "confidence": 0.8, "reasoning": "Good evidence"}'
        clean_result = self.verifier._parse_verification_result(clean_json)
        
        # Test with JSON in code block
        code_block_json = '```json\n{"verified": true, "confidence": 0.8, "reasoning": "Good evidence"}\n```'
        code_block_result = self.verifier._parse_verification_result(code_block_json)
        
        # Test with incomplete JSON
        incomplete_json = '{"verified": true}'
        incomplete_result = self.verifier._parse_verification_result(incomplete_json)
        
        # Assertions
        self.assertTrue(clean_result["verified"])
        self.assertEqual(clean_result["confidence"], 0.8)
        
        self.assertTrue(code_block_result["verified"])
        self.assertEqual(code_block_result["confidence"], 0.8)
        
        self.assertTrue(incomplete_result["verified"])
        self.assertEqual(incomplete_result["reasoning"], "No reasoning provided")
        self.assertEqual(incomplete_result["supporting_evidence"], [])

    def test_verification_id_generation(self):
        """Test generation of verification IDs."""
        # Generate IDs for two different claims
        id1 = self.verifier._generate_verification_id("Claim 1")
        id2 = self.verifier._generate_verification_id("Claim 2")
        
        # Generate ID for the same claim again
        id1_repeat = self.verifier._generate_verification_id("Claim 1")
        
        # Assertions
        self.assertNotEqual(id1, id2)  # Different claims should have different IDs
        self.assertNotEqual(id1, id1_repeat)  # Same claim at different times should have different IDs
        
        # Verify ID format
        self.assertTrue(id1.startswith("verif_"))
        self.assertEqual(len(id1.split("_")), 3)


if __name__ == "__main__":
    unittest.main() 