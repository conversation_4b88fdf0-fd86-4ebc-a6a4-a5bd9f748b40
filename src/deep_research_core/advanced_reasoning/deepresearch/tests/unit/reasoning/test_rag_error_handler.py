"""
Unit tests for RAGError<PERSON><PERSON><PERSON> and RAGFallbackGenerator.
"""

import unittest
from unittest.mock import patch, MagicMock, call
import time
import json

from src.deep_research_core.reasoning.rag_error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RAGFallbackGenerator


class TestRAGError<PERSON>andler(unittest.TestCase):
    """Test the RAGErrorHandler class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.error_handler = RAGErrorHandler(
            enable_fallbacks=True,
            max_retries=2,
            retry_delay=0.1,  # Short delay for tests
            fallback_providers=["openrouter", "openai", "anthropic"],
            fallback_models={
                "openai": ["gpt-4", "gpt-3.5-turbo"],
                "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229"],
                "openrouter": ["mistral-large-latest", "mistral-medium-latest"]
            },
            enable_detailed_logging=True
        )
        
        # Sample data for tests
        self.query = "What is machine learning?"
        self.documents = [
            {"content": "Machine learning is a field of AI.", "source": "Wikipedia"},
            {"content": "Neural networks are used in deep learning.", "source": "Book"}
        ]
    
    @patch('time.sleep')  # Mock sleep to speed up tests
    def test_handle_retrieval_error_with_retry(self, mock_sleep):
        """Test handling retrieval error with retry."""
        error = TimeoutError("Request timed out")
        context = {"expanded_query": "What is machine learning? definition"}
        
        success, docs, error_info = self.error_handler.handle_retrieval_error(
            error=error,
            query=self.query,
            context=context,
            retry_count=0
        )
        
        # Should recommend retry
        self.assertFalse(success)
        self.assertIsNone(docs)
        self.assertTrue(error_info["retry"])
        self.assertEqual(error_info["retry_count"], 1)
        self.assertEqual(error_info["error_type"], "TimeoutError")
        mock_sleep.assert_called_once_with(0.1)
    
    def test_handle_retrieval_error_max_retries(self):
        """Test handling retrieval error after max retries."""
        error = ConnectionError("Connection failed")
        context = {"expanded_query": "What is machine learning? definition"}
        
        success, docs, error_info = self.error_handler.handle_retrieval_error(
            error=error,
            query=self.query,
            context=context,
            retry_count=2  # Already at max retries
        )
        
        # Should return empty results
        self.assertTrue(success)
        self.assertEqual(docs, [])
        self.assertFalse(error_info["retry"])
        self.assertEqual(error_info["fallback"], "empty_results")
    
    @patch('time.sleep')  # Mock sleep to speed up tests
    def test_handle_generation_error_with_retry(self, mock_sleep):
        """Test handling generation error with retry."""
        error = ValueError("Invalid response format")
        context = {"custom_prompt": False}
        
        success, answer, error_info = self.error_handler.handle_generation_error(
            error=error,
            query=self.query,
            documents=self.documents,
            context=context,
            provider="openai",
            model="gpt-4",
            retry_count=0
        )
        
        # Should recommend retry
        self.assertFalse(success)
        self.assertIsNone(answer)
        self.assertTrue(error_info["retry"])
        self.assertEqual(error_info["retry_count"], 1)
        self.assertEqual(error_info["error_type"], "ValueError")
        mock_sleep.assert_called_once_with(0.1)
    
    def test_handle_generation_error_fallback_model(self):
        """Test handling generation error with fallback model."""
        error = Exception("API Error: 429 Too Many Requests")
        context = {"custom_prompt": True}
        
        success, answer, error_info = self.error_handler.handle_generation_error(
            error=error,
            query=self.query,
            documents=self.documents,
            context=context,
            provider="openai",
            model="gpt-4",
            retry_count=2  # Already at max retries
        )
        
        # Should recommend fallback model
        self.assertFalse(success)
        self.assertIsNone(answer)
        self.assertFalse(error_info["retry"])
        self.assertEqual(error_info["fallback"], "model")
        self.assertEqual(error_info["fallback_model"], "gpt-3.5-turbo")
        self.assertEqual(error_info["original_model"], "gpt-4")
    
    def test_handle_generation_error_fallback_provider(self):
        """Test handling generation error with fallback provider."""
        error = Exception("API Error: 429 Too Many Requests")
        context = {"custom_prompt": True}
        
        # Use the last model in the list to trigger provider fallback
        success, answer, error_info = self.error_handler.handle_generation_error(
            error=error,
            query=self.query,
            documents=self.documents,
            context=context,
            provider="openai",
            model="gpt-3.5-turbo",  # Last model for this provider
            retry_count=2  # Already at max retries
        )
        
        # Should recommend fallback provider
        self.assertFalse(success)
        self.assertIsNone(answer)
        self.assertFalse(error_info["retry"])
        self.assertEqual(error_info["fallback"], "provider")
        self.assertEqual(error_info["fallback_provider"], "openrouter")
        self.assertEqual(error_info["fallback_model"], "mistral-large-latest")
        self.assertEqual(error_info["original_provider"], "openai")
    
    def test_handle_generation_error_simple_fallback(self):
        """Test handling generation error with simple fallback."""
        error = Exception("Unknown error")
        context = {"custom_prompt": False}
        
        # Use a provider not in the fallback list
        success, answer, error_info = self.error_handler.handle_generation_error(
            error=error,
            query=self.query,
            documents=self.documents,
            context=context,
            provider="unknown",
            model="unknown-model",
            retry_count=2  # Already at max retries
        )
        
        # Should provide a simple fallback answer
        self.assertTrue(success)
        self.assertIsNotNone(answer)
        self.assertFalse(error_info["retry"])
        self.assertEqual(error_info["fallback"], "simple_answer")
    
    @patch('src.deep_research_core.reasoning.rag_error_handler.logger')
    def test_log_retrieval_success(self, mock_logger):
        """Test logging successful retrieval."""
        self.error_handler.log_retrieval_success(
            query=self.query,
            documents=self.documents,
            latency=0.5,
            metrics={"expanded_query": True, "hybrid_weight": 0.7}
        )
        
        # Check that logger.info was called with the right arguments
        mock_logger.info.assert_called_once()
        log_message = mock_logger.info.call_args[0][0]
        self.assertIn("Retrieval successful", log_message)
        self.assertIn("2 documents", log_message)
    
    @patch('src.deep_research_core.reasoning.rag_error_handler.logger')
    def test_log_generation_success(self, mock_logger):
        """Test logging successful generation."""
        self.error_handler.log_generation_success(
            query=self.query,
            answer="Machine learning is a field of AI that focuses on algorithms that learn from data.",
            documents=self.documents,
            latency=0.8,
            provider="openai",
            model="gpt-4",
            metrics={"custom_prompt_used": True}
        )
        
        # Check that logger.info was called with the right arguments
        mock_logger.info.assert_called_once()
        log_message = mock_logger.info.call_args[0][0]
        self.assertIn("Generation successful", log_message)


class TestRAGFallbackGenerator(unittest.TestCase):
    """Test the RAGFallbackGenerator class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.fallback_generator = RAGFallbackGenerator(
            enable_keyword_fallback=True,
            enable_summary_fallback=True,
            enable_template_fallback=True,
            max_documents=2,
            max_snippet_length=100
        )
        
        # Sample data for tests
        self.query = "What is machine learning?"
        self.documents = [
            {
                "content": "Machine learning is a field of artificial intelligence that focuses on algorithms that can learn from data.",
                "source": "Wikipedia",
                "title": "Machine Learning"
            },
            {
                "content": "Neural networks are a type of machine learning model inspired by the human brain.",
                "source": "Book",
                "title": "Deep Learning"
            },
            {
                "content": "Supervised learning is a type of machine learning where the model is trained on labeled data.",
                "source": "Article",
                "title": "Supervised Learning"
            }
        ]
        self.error_info = {"error": "API Error: 429 Too Many Requests"}
    
    def test_generate_template_fallback(self):
        """Test generating template-based fallback."""
        answer = self.fallback_generator.generate_fallback(
            query=self.query,
            documents=self.documents,
            error_info=self.error_info
        )
        
        # Check that the answer contains expected elements
        self.assertIn("I found some information", answer)
        self.assertIn("Document 1", answer)
        self.assertIn("Document 2", answer)
        self.assertIn("Machine Learning", answer)
        self.assertIn("Wikipedia", answer)
        self.assertIn("Book", answer)
        
        # Should only include max_documents (2)
        self.assertNotIn("Document 3", answer)
        self.assertNotIn("Supervised Learning", answer)
    
    def test_generate_fallback_no_documents(self):
        """Test generating fallback with no documents."""
        answer = self.fallback_generator.generate_fallback(
            query=self.query,
            documents=[],
            error_info=self.error_info
        )
        
        # Check that the answer contains expected elements
        self.assertIn("I apologize", answer)
        self.assertIn("couldn't find any relevant information", answer)
    
    def test_extract_keywords(self):
        """Test keyword extraction."""
        keywords = self.fallback_generator._extract_keywords(
            "What is the difference between supervised and unsupervised learning?"
        )
        
        # Check that common words are filtered out
        self.assertNotIn("what", keywords)
        self.assertNotIn("is", keywords)
        self.assertNotIn("the", keywords)
        self.assertNotIn("between", keywords)
        self.assertNotIn("and", keywords)
        
        # Check that important keywords are included
        self.assertIn("difference", keywords)
        self.assertIn("supervised", keywords)
        self.assertIn("unsupervised", keywords)
        self.assertIn("learning", keywords)
    
    def test_generate_keyword_fallback(self):
        """Test generating keyword-based fallback."""
        # Disable template fallback to test keyword fallback
        self.fallback_generator.enable_template_fallback = False
        self.fallback_generator.enable_summary_fallback = False
        
        answer = self.fallback_generator.generate_fallback(
            query=self.query,
            documents=self.documents,
            error_info=self.error_info
        )
        
        # Check that the answer contains expected elements
        self.assertIn("machine", answer)
        self.assertIn("learning", answer)
        self.assertIn("Wikipedia", answer)
        self.assertIn("Book", answer)


if __name__ == '__main__':
    unittest.main()
