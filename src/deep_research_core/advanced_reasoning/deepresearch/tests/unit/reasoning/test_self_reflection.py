"""
Unit tests for Self-reflection & Self-correction module.
"""

import unittest
import json
from unittest.mock import patch, MagicMock
from src.deep_research_core.reasoning.self_reflection import SelfReflection

class TestSelfReflection(unittest.TestCase):
    """Test cases for SelfReflection class."""
    
    def setUp(self):
        """Set up test cases."""
        # Create a mock api_provider for testing without real API calls
        self.mock_provider = MagicMock()
        self.mock_provider.call_api.return_value = {
            "choices": [
                {
                    "message": {
                        "content": json.dumps({
                            "errors": [
                                {
                                    "type": "LogicalError",
                                    "description": "Inconsistent reasoning in step 2",
                                    "severity": "medium"
                                }
                            ],
                            "confidence_score": 0.65,
                            "reasoning_quality": "fair",
                            "correction_needed": True,
                            "correction_suggestions": [
                                "Revisit step 2 and ensure consistency with step 1"
                            ]
                        })
                    }
                }
            ]
        }
        
        # Create SelfReflection instance with mocked provider
        with patch('src.deep_research_core.reasoning.self_reflection.openai_provider', self.mock_provider):
            self.reflection = SelfReflection(
                provider="openai",
                language="en",
                reflection_depth=1
            )
            self.reflection.api_provider = self.mock_provider
    
    def test_reflect_on_reasoning(self):
        """Test reflecting on reasoning."""
        reasoning = "Step 1: The sky is blue because of Rayleigh scattering. Step 2: The sun appears yellow due to gravity."
        
        result = self.reflection.reflect_on_reasoning(reasoning)
        
        self.assertEqual(len(result["errors"]), 1)
        self.assertEqual(result["errors"][0]["type"], "LogicalError")
        self.assertEqual(result["confidence_score"], 0.65)
        self.assertEqual(result["reasoning_quality"], "fair")
        self.assertTrue(result["correction_needed"])
    
    def test_correct_reasoning(self):
        """Test correcting reasoning."""
        reasoning = "Step 1: The sky is blue because of Rayleigh scattering. Step 2: The sun appears yellow due to gravity."
        
        reflection_result = {
            "errors": [
                {
                    "type": "LogicalError",
                    "description": "Inconsistent reasoning in step 2",
                    "severity": "medium"
                }
            ],
            "confidence_score": 0.65,
            "reasoning_quality": "fair",
            "correction_needed": True,
            "correction_suggestions": [
                "Revisit step 2 and ensure consistency with step 1"
            ]
        }
        
        # Set up the mock for correction
        self.mock_provider.call_api.return_value = {
            "choices": [
                {
                    "message": {
                        "content": json.dumps({
                            "corrected_reasoning": "Step 1: The sky is blue because of Rayleigh scattering. Step 2: The sun appears yellow because its light is scattered by Earth's atmosphere, with blue light scattered more than red/yellow.",
                            "correction_applied": True,
                            "confidence_score": 0.85,
                            "corrections_made": [
                                "Corrected explanation of sun's color"
                            ]
                        })
                    }
                }
            ]
        }
        
        result = self.reflection.correct_reasoning(reasoning, reflection_result)
        
        self.assertTrue(result["correction_applied"])
        self.assertIn("Rayleigh scattering", result["corrected_reasoning"])
        self.assertIn("blue light scattered more", result["corrected_reasoning"])
        self.assertEqual(result["confidence_score"], 0.85)
        self.assertEqual(len(result["corrections_made"]), 1)
    
    def test_no_correction_needed(self):
        """Test when no correction is needed."""
        reasoning = "The Earth orbits the Sun in an elliptical path."
        
        reflection_result = {
            "errors": [],
            "confidence_score": 0.9,
            "reasoning_quality": "excellent",
            "correction_needed": False
        }
        
        result = self.reflection.correct_reasoning(reasoning, reflection_result)
        
        self.assertEqual(result["corrected_reasoning"], reasoning)
        self.assertFalse(result["correction_applied"])
        self.assertEqual(result["confidence_score"], 0.9)
    
    def test_evaluate_confidence(self):
        """Test evaluating confidence."""
        reasoning = "The Earth orbits the Sun in an elliptical path."
        
        # Set up the mock for confidence evaluation
        self.mock_provider.call_api.return_value = {
            "choices": [
                {
                    "message": {
                        "content": "confidence_score: 0.85"
                    }
                }
            ]
        }
        
        confidence = self.reflection.evaluate_confidence(reasoning)
        
        self.assertEqual(confidence, 0.85)
    
    def test_parse_reflection_json(self):
        """Test parsing reflection from JSON."""
        reflection_text = """```json
        {
            "errors": [
                {
                    "type": "Assumption",
                    "description": "Assumed X without evidence",
                    "severity": "high"
                }
            ],
            "confidence_score": 0.4,
            "reasoning_quality": "poor",
            "correction_needed": true
        }
        ```"""
        
        result = self.reflection._parse_reflection(reflection_text)
        
        self.assertEqual(len(result["errors"]), 1)
        self.assertEqual(result["errors"][0]["type"], "Assumption")
        self.assertEqual(result["confidence_score"], 0.4)
        self.assertEqual(result["reasoning_quality"], "poor")
        self.assertTrue(result["correction_needed"])
    
    def test_parse_correction_json(self):
        """Test parsing correction from JSON."""
        correction_text = """```json
        {
            "corrected_reasoning": "This is the corrected reasoning.",
            "correction_applied": true,
            "confidence_score": 0.75,
            "corrections_made": ["Fixed assumption A", "Added missing context B"]
        }
        ```"""
        
        result = self.reflection._parse_correction(correction_text)
        
        self.assertEqual(result["corrected_reasoning"], "This is the corrected reasoning.")
        self.assertTrue(result["correction_applied"])
        self.assertEqual(result["confidence_score"], 0.75)
        self.assertEqual(len(result["corrections_made"]), 2)
    
    def test_reflection_with_facts_and_context(self):
        """Test reflection with known facts and context."""
        reasoning = "The sky is green because plants reflect green light."
        facts = ["The sky appears blue during the day."]
        context = {"topic": "Atmospheric optics"}
        
        # We only want to test that the facts and context are properly included in the prompt
        self.reflection.reflect_on_reasoning(reasoning, context, facts)
        
        # Check that the mock was called once
        self.mock_provider.call_api.assert_called_once()
        
        # Get the call arguments
        call_args = self.mock_provider.call_api.call_args[1]
        
        # Check that messages were passed
        self.assertIn("messages", call_args)
        
        # Check for the user message containing facts and context
        user_message = call_args["messages"][1]["content"]
        self.assertIn("The sky appears blue during the day", user_message)
        self.assertIn("topic: Atmospheric optics", user_message)
    
    def test_vietnamese_language_support(self):
        """Test Vietnamese language support."""
        with patch('src.deep_research_core.reasoning.self_reflection.openai_provider', self.mock_provider):
            vi_reflection = SelfReflection(
                provider="openai",
                language="vi",
                reflection_depth=1
            )
            vi_reflection.api_provider = self.mock_provider
        
        # Just check that the system prompt is in Vietnamese
        system_prompt = vi_reflection._get_system_prompt()
        self.assertIn("Bạn là một hệ thống phân tích", system_prompt)
        self.assertIn("Điểm tin cậy", system_prompt)


if __name__ == "__main__":
    unittest.main() 