"""
Unit tests for the Knowledge Graph RAG module.
"""

import unittest
from unittest.mock import patch, MagicMock
import json

from src.deep_research_core.reasoning.knowledge_graph_rag import KnowledgeGraphRAG


class TestKnowledgeGraphRAG(unittest.TestCase):
    """Test the KnowledgeGraphRAG class."""

    def setUp(self):
        """Set up test environment."""
        # Mock the WeaviateVectorStore
        self.patcher1 = patch('src.deep_research_core.reasoning.knowledge_graph_rag.WeaviateVectorStore')
        self.mock_vector_store = self.patcher1.start()

        # Mock the KnowledgeGraphIntegration
        self.patcher2 = patch('src.deep_research_core.reasoning.knowledge_graph_rag.KnowledgeGraphIntegration')
        self.mock_kg = self.patcher2.start()

        # Mock the openai_provider
        self.patcher3 = patch('src.deep_research_core.reasoning.knowledge_graph_rag.openai_provider')
        self.mock_openai = self.patcher3.start()

        # Create mock instances
        self.mock_document_store = MagicMock()
        self.mock_kg_instance = MagicMock()

        # Configure the mocks to return these instances
        self.mock_vector_store.return_value = self.mock_document_store
        self.mock_kg.return_value = self.mock_kg_instance

        # Initialize the KnowledgeGraphRAG
        self.kg_rag = KnowledgeGraphRAG(
            provider="openai",
            model="gpt-3.5-turbo",
            temperature=0.7,
            max_tokens=2000,
            embedding_model="text-embedding-ada-002",
            url="http://localhost:8080",
            api_key="test-api-key",
            document_class_name="TestDocument",
            entity_class_name="TestEntity",
            relation_class_name="TestRelation",
            create_schema=True,
            top_k=5,
            kg_weight=0.3,
            use_kg_for_query_expansion=True,
            use_kg_for_context_enrichment=True
        )

    def tearDown(self):
        """Tear down test environment."""
        self.patcher1.stop()
        self.patcher2.stop()
        self.patcher3.stop()

    def test_initialization(self):
        """Test initialization of KnowledgeGraphRAG."""
        self.assertEqual(self.kg_rag.provider, "openai")
        self.assertEqual(self.kg_rag.model, "gpt-3.5-turbo")
        self.assertEqual(self.kg_rag.temperature, 0.7)
        self.assertEqual(self.kg_rag.max_tokens, 2000)
        self.assertEqual(self.kg_rag.embedding_model, "text-embedding-ada-002")
        self.assertEqual(self.kg_rag.url, "http://localhost:8080")
        self.assertEqual(self.kg_rag.api_key, "test-api-key")
        self.assertEqual(self.kg_rag.document_class_name, "TestDocument")
        self.assertEqual(self.kg_rag.entity_class_name, "TestEntity")
        self.assertEqual(self.kg_rag.relation_class_name, "TestRelation")
        self.assertEqual(self.kg_rag.top_k, 5)
        self.assertEqual(self.kg_rag.kg_weight, 0.3)
        self.assertTrue(self.kg_rag.use_kg_for_query_expansion)
        self.assertTrue(self.kg_rag.use_kg_for_context_enrichment)

        # Verify WeaviateVectorStore was called correctly
        self.mock_vector_store.assert_called_once_with(
            url="http://localhost:8080",
            api_key="test-api-key",
            class_name="TestDocument",
            create_class=True,
            vector_index_type="hnsw",
            vector_index_config=None
        )

        # Verify KnowledgeGraphIntegration was called correctly
        self.mock_kg.assert_called_once_with(
            url="http://localhost:8080",
            api_key="test-api-key",
            entity_class_name="TestEntity",
            relation_class_name="TestRelation",
            create_schema=True
        )

    def test_add_documents(self):
        """Test adding documents."""
        # Mock add_documents
        self.mock_document_store.add_documents.return_value = ["doc1", "doc2"]

        # Create test documents
        documents = [
            {
                "content": "This is document 1 about John Doe who works at Tech Corp.",
                "metadata": {"source": "test1.txt"}
            },
            {
                "content": "This is document 2 about Jane Smith who works at Tech Corp.",
                "metadata": {"source": "test2.txt"}
            }
        ]

        # Mock _extract_entities_from_documents
        self.kg_rag._extract_entities_from_documents = MagicMock()

        # Call add_documents
        document_ids = self.kg_rag.add_documents(documents)

        # Verify add_documents was called
        self.mock_document_store.add_documents.assert_called_once_with(documents, None)

        # Verify _extract_entities_from_documents was called
        self.kg_rag._extract_entities_from_documents.assert_called_once_with(documents, ["doc1", "doc2"])

        # Verify return value
        self.assertEqual(document_ids, ["doc1", "doc2"])

    def test_extract_entities_from_documents(self):
        """Test extracting entities from documents."""
        # Mock generate
        self.mock_openai.generate.return_value = """
        [
            {
                "name": "John Doe",
                "type": "Person",
                "description": "A person mentioned in the document",
                "properties": {"role": "Employee"},
                "relations": [
                    {
                        "target": "Tech Corp",
                        "type": "WORKS_FOR",
                        "weight": 1.0,
                        "properties": {"position": "Engineer"}
                    }
                ]
            },
            {
                "name": "Tech Corp",
                "type": "Organization",
                "description": "A company mentioned in the document",
                "properties": {"industry": "Technology"},
                "relations": []
            }
        ]
        """

        # Mock add_entity
        self.mock_kg_instance.add_entity.side_effect = ["entity1", "entity2"]

        # Mock add_relation
        self.mock_kg_instance.add_relation.return_value = "relation1"

        # Create test documents
        documents = [
            {
                "content": "This is a document about John Doe who works at Tech Corp.",
                "metadata": {"source": "test.txt"}
            }
        ]

        document_ids = ["doc1"]

        # Call _extract_entities_from_documents
        self.kg_rag._extract_entities_from_documents(documents, document_ids)

        # Verify generate was called
        self.mock_openai.generate.assert_called_once()

        # Verify add_entity was called twice
        self.assertEqual(self.mock_kg_instance.add_entity.call_count, 2)

        # Verify add_relation was called three times (2 for MENTIONED_IN, 1 for WORKS_FOR)
        self.assertEqual(self.mock_kg_instance.add_relation.call_count, 3)

    def test_process_query(self):
        """Test processing a query."""
        # Mock _expand_query_with_kg
        self.kg_rag._expand_query_with_kg = MagicMock(return_value="expanded query")

        # Mock _get_embedding
        self.kg_rag._get_embedding = MagicMock(return_value=[0.1] * 1536)

        # Mock search
        self.kg_rag.search = MagicMock(return_value=[
            {
                "id": "doc1",
                "content": "Document 1 content",
                "score": 0.9
            },
            {
                "id": "doc2",
                "content": "Document 2 content",
                "score": 0.8
            }
        ])

        # Mock _get_kg_context
        self.kg_rag._get_kg_context = MagicMock(return_value="KG context")

        # Mock _prepare_context
        self.kg_rag._prepare_context = MagicMock(return_value="Prepared context")

        # Mock _generate_response
        self.kg_rag._generate_response = MagicMock(return_value="Generated response")

        # Call process
        result = self.kg_rag.process("test query")

        # Verify _expand_query_with_kg was called
        self.kg_rag._expand_query_with_kg.assert_called_once_with("test query")

        # Verify search was called
        self.kg_rag.search.assert_called_once_with("test query", 5, None)

        # Verify _get_kg_context was called
        self.kg_rag._get_kg_context.assert_called_once_with("test query", [0.1] * 1536)

        # Verify _prepare_context was called
        self.kg_rag._prepare_context.assert_called_once()

        # Verify _generate_response was called
        self.kg_rag._generate_response.assert_called_once_with("test query", "Prepared context")

        # Verify return value
        self.assertEqual(result["query"], "test query")
        self.assertEqual(result["expanded_query"], "expanded query")
        self.assertEqual(result["response"], "Generated response")

    def test_expand_query_with_kg(self):
        """Test expanding a query with knowledge graph."""
        # Mock _get_embedding
        self.kg_rag._get_embedding = MagicMock(return_value=[0.1] * 1536)

        # Mock search_entities
        self.mock_kg_instance.search_entities.return_value = [
            {
                "id": "entity1",
                "name": "John Doe",
                "type": "Person",
                "description": "A software engineer"
            },
            {
                "id": "entity2",
                "name": "Tech Corp",
                "type": "Organization",
                "description": "A technology company"
            }
        ]

        # Mock generate
        self.mock_openai.generate.return_value = "expanded query about John Doe and Tech Corp"

        # Call _expand_query_with_kg
        expanded_query = self.kg_rag._expand_query_with_kg("test query")

        # Verify _get_embedding was called
        self.kg_rag._get_embedding.assert_called_once_with("test query")

        # Verify search_entities was called
        self.mock_kg_instance.search_entities.assert_called_once_with([0.1] * 1536, top_k=3)

        # Verify generate was called
        self.mock_openai.generate.assert_called_once()

        # Verify return value
        self.assertEqual(expanded_query, "expanded query about John Doe and Tech Corp")

    def test_get_kg_context(self):
        """Test getting knowledge graph context."""
        # Mock search_entities
        self.mock_kg_instance.search_entities.return_value = [
            {
                "id": "entity1",
                "name": "John Doe",
                "type": "Person",
                "description": "A software engineer",
                "properties": {"age": 30}
            }
        ]

        # Mock get_connected_entities
        self.mock_kg_instance.get_connected_entities.return_value = [
            {
                "id": "entity2",
                "name": "Tech Corp",
                "type": "Organization",
                "description": "A technology company",
                "properties": {"founded": 2010}
            }
        ]

        # Mock get_entity_relations
        self.mock_kg_instance.get_entity_relations.return_value = [
            {
                "id": "relation1",
                "source": "entity1",
                "target": "entity2",
                "type": "WORKS_FOR",
                "weight": 1.0,
                "properties": {"position": "Engineer"}
            }
        ]

        # Call _get_kg_context
        kg_context = self.kg_rag._get_kg_context("test query", [0.1] * 1536)

        # Verify search_entities was called
        self.mock_kg_instance.search_entities.assert_called_once_with([0.1] * 1536, top_k=5)

        # Verify get_connected_entities was called
        self.mock_kg_instance.get_connected_entities.assert_called_once_with("entity1", max_depth=1)

        # Verify get_entity_relations was called
        self.mock_kg_instance.get_entity_relations.assert_any_call("entity1", direction="both")
        self.mock_kg_instance.get_entity_relations.assert_any_call("entity2", direction="both")

        # Verify return value contains entity and relation information
        self.assertIn("Entities:", kg_context)
        self.assertIn("John Doe (Person)", kg_context)
        self.assertIn("Tech Corp (Organization)", kg_context)
        self.assertIn("Relations:", kg_context)
        self.assertIn("WORKS_FOR", kg_context)

    def test_prepare_context(self):
        """Test preparing context."""
        # Create test documents
        documents = [
            {
                "id": "doc1",
                "content": "Document 1 content",
                "score": 0.9
            },
            {
                "id": "doc2",
                "content": "Document 2 content",
                "score": 0.8
            }
        ]

        # Create test KG context
        kg_context = "Knowledge graph context"

        # Call _prepare_context
        context = self.kg_rag._prepare_context(documents, kg_context)

        # Verify return value
        self.assertIn("Document Context:", context)
        self.assertIn("[Document 1]", context)
        self.assertIn("Document 1 content", context)
        self.assertIn("[Document 2]", context)
        self.assertIn("Document 2 content", context)
        self.assertIn("Knowledge Graph Context:", context)
        self.assertIn("Knowledge graph context", context)

    def test_generate_response(self):
        """Test generating a response."""
        # Mock generate
        self.mock_openai.generate.return_value = "Generated response"

        # Call _generate_response
        response = self.kg_rag._generate_response("test query", "test context")

        # Verify generate was called
        self.mock_openai.generate.assert_called_once()

        # Verify return value
        self.assertEqual(response, "Generated response")

    def test_get_embedding(self):
        """Test getting an embedding."""
        # Mock get_embedding
        self.mock_openai.get_embedding.return_value = [0.1] * 1536

        # Call _get_embedding
        embedding = self.kg_rag._get_embedding("test text")

        # Verify get_embedding was called
        self.mock_openai.get_embedding.assert_called_once_with(
            text="test text",
            model="text-embedding-ada-002"
        )

        # Verify return value
        self.assertEqual(embedding, [0.1] * 1536)

    def test_update_document(self):
        """Test updating a document."""
        # Mock update_document
        self.mock_document_store.update_document.return_value = True

        # Mock _extract_entities_from_documents
        self.kg_rag._extract_entities_from_documents = MagicMock()

        # Create test document
        document = {
            "content": "Updated document content",
            "metadata": {"source": "test.txt"}
        }

        # Call update_document
        result = self.kg_rag.update_document("doc1", document)

        # Verify update_document was called
        self.mock_document_store.update_document.assert_called_once_with("doc1", document, None)

        # Verify _extract_entities_from_documents was called
        self.kg_rag._extract_entities_from_documents.assert_called_once_with([document], ["doc1"])

        # Verify return value
        self.assertTrue(result)

    def test_delete_document(self):
        """Test deleting a document."""
        # Mock delete_document
        self.mock_document_store.delete_document.return_value = True

        # Mock get_entity_relations
        self.mock_kg_instance.get_entity_relations.return_value = [
            {
                "id": "relation1",
                "source": "entity1",
                "target": "doc1",
                "type": "MENTIONED_IN"
            }
        ]

        # Mock delete_document for relation
        self.mock_kg_instance.relation_store.delete_document.return_value = True

        # Call delete_document
        result = self.kg_rag.delete_document("doc1")

        # Verify delete_document was called
        self.mock_document_store.delete_document.assert_called_once_with("doc1")

        # Verify get_entity_relations was called
        self.mock_kg_instance.get_entity_relations.assert_called_once_with(
            "doc1",
            relation_type="MENTIONED_IN",
            direction="incoming"
        )

        # Verify delete_document was called for relation
        self.mock_kg_instance.relation_store.delete_document.assert_called_once_with("relation1")

        # Verify return value
        self.assertTrue(result)

    def test_get_document(self):
        """Test getting a document."""
        # Mock get_document
        self.mock_document_store.get_document.return_value = {
            "id": "doc1",
            "content": "Document content",
            "metadata": {"source": "test.txt"}
        }

        # Call get_document
        document = self.kg_rag.get_document("doc1")

        # Verify get_document was called
        self.mock_document_store.get_document.assert_called_once_with("doc1")

        # Verify return value
        self.assertEqual(document["id"], "doc1")
        self.assertEqual(document["content"], "Document content")

    def test_get_documents(self):
        """Test getting documents."""
        # Mock get_documents
        self.mock_document_store.get_documents.return_value = [
            {
                "id": "doc1",
                "content": "Document 1 content",
                "metadata": {"source": "test1.txt"}
            },
            {
                "id": "doc2",
                "content": "Document 2 content",
                "metadata": {"source": "test2.txt"}
            }
        ]

        # Call get_documents
        documents = self.kg_rag.get_documents(filter_expr="metadata.source == 'test1.txt'", limit=10)

        # Verify get_documents was called
        self.mock_document_store.get_documents.assert_called_once_with("metadata.source == 'test1.txt'", 10)

        # Verify return value
        self.assertEqual(len(documents), 2)
        self.assertEqual(documents[0]["id"], "doc1")
        self.assertEqual(documents[1]["id"], "doc2")

    def test_clear(self):
        """Test clearing the RAG system."""
        # Mock clear for document store
        self.mock_document_store.clear.return_value = True

        # Mock clear for knowledge graph
        self.mock_kg_instance.clear.return_value = True

        # Call clear
        self.kg_rag.clear()

        # Verify clear was called for both stores
        self.mock_document_store.clear.assert_called_once()
        self.mock_kg_instance.clear.assert_called_once()


if __name__ == '__main__':
    unittest.main()
