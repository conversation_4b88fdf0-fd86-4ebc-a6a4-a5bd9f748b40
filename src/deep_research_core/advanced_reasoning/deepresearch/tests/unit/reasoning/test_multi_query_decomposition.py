"""
Unit tests for Multi-query Decomposition module.
"""

import unittest
from unittest.mock import patch, MagicMock
from src.deep_research_core.reasoning.multi_query_decomposer import MultiQueryDecomposition

class TestMultiQueryDecomposition(unittest.TestCase):
    """Test cases for MultiQueryDecomposition class."""
    
    def setUp(self):
        """Set up test cases."""
        # Create a mock for QueryDecomposer
        self.mock_decomposer_patcher = patch('src.deep_research_core.reasoning.multi_query_decomposer.QueryDecomposer')
        self.mock_decomposer_class = self.mock_decomposer_patcher.start()
        self.mock_decomposer = MagicMock()
        self.mock_decomposer_class.return_value = self.mock_decomposer
        
        # Set up mock behavior for is_complex_query
        self.mock_decomposer._is_complex_query.side_effect = lambda q: len(q.split()) > 5
        
        # Set up mock behavior for decompose
        self.mock_decomposer.decompose.return_value = [
            {
                "query": "What is AI?",
                "focus": "Definition",
                "complexity": "simple",
                "order": 1,
                "is_original": False
            },
            {
                "query": "What are the applications of AI?",
                "focus": "Applications",
                "complexity": "medium",
                "order": 2,
                "is_original": False
            }
        ]
        
        # Create MultiQueryDecomposition instance
        self.decomposition = MultiQueryDecomposition(
            provider="openai",
            model="gpt-4o",
            language="en",
            max_depth=2
        )
        
        # Mock the api_provider.generate method
        self.mock_api_provider = MagicMock()
        self.mock_api_provider.generate.return_value = "Synthesized answer about AI"
        self.decomposition.base_decomposer.api_provider = self.mock_api_provider
    
    def tearDown(self):
        """Tear down test cases."""
        self.mock_decomposer_patcher.stop()
    
    def test_decompose_simple_query(self):
        """Test decomposing a simple query that doesn't need decomposition."""
        query = "What is AI"
        
        result = self.decomposition.decompose(query)
        
        self.assertFalse(result["is_decomposed"])
        self.assertEqual(len(result["sub_queries"]), 1)
        self.assertEqual(result["sub_queries"][0]["query"], query)
        self.assertEqual(len(result["processing_order"]), 1)
    
    def test_decompose_complex_query(self):
        """Test decomposing a complex query."""
        query = "What is AI and what are its applications in healthcare and education?"
        
        result = self.decomposition.decompose(query)
        
        self.assertTrue(result["is_decomposed"])
        self.assertEqual(len(result["sub_queries"]), 2)
        self.assertEqual(len(result["dependency_graph"]), 3)  # Original + 2 sub-queries
        self.assertEqual(len(result["processing_order"]), 3)
    
    def test_recursive_decomposition(self):
        """Test recursive decomposition of queries."""
        query = "What is AI and what are its applications in healthcare and education?"
        
        # First level decomposition
        first_level = [
            {
                "query": "What is AI?",
                "focus": "Definition",
                "complexity": "simple",
                "order": 1,
                "is_original": False
            },
            {
                "query": "What are the applications of AI in healthcare and education?",
                "focus": "Applications",
                "complexity": "medium",
                "order": 2,
                "is_original": False
            }
        ]
        
        # Second level decomposition for the second sub-query
        second_level = [
            {
                "query": "What are the applications of AI in healthcare?",
                "focus": "Healthcare",
                "complexity": "simple",
                "order": 1,
                "is_original": False
            },
            {
                "query": "What are the applications of AI in education?",
                "focus": "Education",
                "complexity": "simple",
                "order": 2,
                "is_original": False
            }
        ]
        
        # Set up the mock to return different values for different calls
        self.mock_decomposer.decompose.side_effect = [first_level, second_level]
        
        # Set up the mock to indicate the second first-level query is complex
        self.mock_decomposer._is_complex_query.side_effect = lambda q: q == query or "healthcare and education" in q
        
        result = self.decomposition.decompose(query)
        
        self.assertTrue(result["is_decomposed"])
        self.assertEqual(len(result["sub_queries"]), 3)  # 1 from first level + 2 from second level
    
    def test_synthesize_results(self):
        """Test synthesizing results from sub-queries."""
        # Set up query graph
        query = "What is AI and what are its applications?"
        query_id = self.decomposition._generate_query_id(query)
        sub1_id = self.decomposition._generate_query_id("What is AI?")
        sub2_id = self.decomposition._generate_query_id("What are the applications of AI?")
        
        self.decomposition.query_graph = {
            query_id: {
                "id": query_id,
                "query": query,
                "is_original": True,
                "complexity": "complex",
                "dependencies": [],
                "depth": 0,
                "children": [sub1_id, sub2_id]
            },
            sub1_id: {
                "id": sub1_id,
                "query": "What is AI?",
                "focus": "Definition",
                "complexity": "simple",
                "order": 1,
                "is_original": False,
                "parent_id": query_id,
                "dependencies": [],
                "depth": 1
            },
            sub2_id: {
                "id": sub2_id,
                "query": "What are the applications of AI?",
                "focus": "Applications",
                "complexity": "medium",
                "order": 2,
                "is_original": False,
                "parent_id": query_id,
                "dependencies": [sub1_id],
                "depth": 1
            }
        }
        
        # Set up query results
        query_results = {
            sub1_id: "AI stands for Artificial Intelligence. It refers to systems that can perform tasks that typically require human intelligence.",
            sub2_id: "AI has applications in healthcare, education, finance, transportation, and many other fields."
        }
        
        result = self.decomposition.synthesize_results(query_results, query)
        
        self.assertEqual(result, "Synthesized answer about AI")
        self.mock_api_provider.generate.assert_called_once()
    
    def test_determine_processing_order(self):
        """Test determining the optimal processing order."""
        # Set up a query graph with dependencies
        query = "Complex query"
        query_id = self.decomposition._generate_query_id(query)
        a_id = self.decomposition._generate_query_id("A")
        b_id = self.decomposition._generate_query_id("B")
        c_id = self.decomposition._generate_query_id("C")
        d_id = self.decomposition._generate_query_id("D")
        
        self.decomposition.query_graph = {
            query_id: {"id": query_id, "dependencies": []},
            a_id: {"id": a_id, "dependencies": []},
            b_id: {"id": b_id, "dependencies": [a_id]},
            c_id: {"id": c_id, "dependencies": [b_id]},
            d_id: {"id": d_id, "dependencies": [a_id, b_id]}
        }
        
        order = self.decomposition._determine_processing_order()
        
        # Check that dependencies are respected
        a_pos = order.index(a_id)
        b_pos = order.index(b_id)
        c_pos = order.index(c_id)
        d_pos = order.index(d_id)
        
        self.assertLess(a_pos, b_pos, "A should come before B")
        self.assertLess(b_pos, c_pos, "B should come before C")
        self.assertLess(a_pos, d_pos, "A should come before D")
        self.assertLess(b_pos, d_pos, "B should come before D")
    
    def test_identify_dependencies(self):
        """Test identifying dependencies between sub-queries."""
        # Set up test queries
        query_ids = []
        for i, q in enumerate(["First query", "Second query", "Third query"]):
            query_id = self.decomposition._generate_query_id(q)
            query_ids.append(query_id)
            self.decomposition.query_graph[query_id] = {
                "id": query_id,
                "query": q,
                "order": i+1,
                "dependencies": []
            }
        
        # Identify dependencies
        self.decomposition._identify_dependencies(query_ids)
        
        # Check dependencies were created correctly
        self.assertEqual(len(self.decomposition.query_graph[query_ids[0]]["dependencies"]), 0)
        self.assertEqual(len(self.decomposition.query_graph[query_ids[1]]["dependencies"]), 1)
        self.assertEqual(len(self.decomposition.query_graph[query_ids[2]]["dependencies"]), 1)
        
        self.assertEqual(self.decomposition.query_graph[query_ids[1]]["dependencies"][0], query_ids[0])
        self.assertEqual(self.decomposition.query_graph[query_ids[2]]["dependencies"][0], query_ids[1])
    
    def test_vietnamese_language_support(self):
        """Test Vietnamese language support."""
        # Create a Vietnamese-language instance
        vi_decomposition = MultiQueryDecomposition(
            provider="openai",
            model="gpt-4o",
            language="vi",
            max_depth=1
        )
        
        # Check that Vietnamese prompts are used
        system_prompt = vi_decomposition._get_synthesis_system_prompt()
        self.assertIn("Bạn là một hệ thống tổng hợp", system_prompt)
        
        # Test user prompt
        user_prompt = vi_decomposition._get_synthesis_user_prompt(
            "Trí tuệ nhân tạo là gì?",
            [{"query": "Q1", "result": "A1"}, {"query": "Q2", "result": "A2"}]
        )
        self.assertIn("Câu hỏi gốc", user_prompt)
        self.assertIn("Câu trả lời từ các câu hỏi con", user_prompt)


if __name__ == "__main__":
    unittest.main() 