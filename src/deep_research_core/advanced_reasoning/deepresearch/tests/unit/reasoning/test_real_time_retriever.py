"""
Unit tests for the Real-time Information Retrieval module.
"""

import unittest
from unittest.mock import patch, MagicMock, Mock
import json
import time
import datetime
from typing import Dict, Any, List

from deep_research_core.reasoning.real_time_retriever import RealTimeRetriever


class TestRealTimeRetriever(unittest.TestCase):
    """Test cases for the RealTimeRetriever class."""

    def setUp(self):
        """Set up test environment before each test."""
        self.retriever = RealTimeRetriever(
            provider="openai",
            model="gpt-4o",
            temperature=0.3,
            max_tokens=1000,
            update_interval=3600,
            max_age=86400,
            cache_enabled=True,
            cache_ttl=300
        )
        
        # Sample queries
        self.sample_queries = [
            "What are the latest news about AI?",
            "Weather in Paris today",
            "Stock price for AAPL",
            "Recent updates on climate change",
            "Current events in technology"
        ]

        # Mock source handlers to avoid external API calls
        self.retriever._web_search_handler = self._mock_web_search_handler
        self.retriever._news_api_handler = self._mock_news_api_handler
        self.retriever._rss_feed_handler = self._mock_rss_feed_handler
        self.retriever._weather_api_handler = self._mock_weather_api_handler
        self.retriever._stocks_api_handler = self._mock_stocks_api_handler

    def _mock_web_search_handler(self, query, config):
        """Mock web search handler."""
        current_time = time.time()
        return [
            {
                "title": f"Web result 1 for {query}",
                "url": "https://example.com/result1",
                "content": f"This is web content about {query}.",
                "timestamp": current_time - 3600,
                "relevance": 0.8
            },
            {
                "title": f"Web result 2 for {query}",
                "url": "https://example.com/result2",
                "content": f"More web content about {query}.",
                "timestamp": current_time - 7200,
                "relevance": 0.7
            }
        ]

    def _mock_news_api_handler(self, query, config):
        """Mock news API handler."""
        current_time = time.time()
        return [
            {
                "title": f"News article 1 about {query}",
                "url": "https://news-example.com/article1",
                "content": f"Breaking news about {query}.",
                "timestamp": current_time - 1800,
                "source_name": "News Source 1",
                "type": "news",
                "relevance": 0.9
            },
            {
                "title": f"News article 2 about {query}",
                "url": "https://news-example.com/article2",
                "content": f"Latest developments regarding {query}.",
                "timestamp": current_time - 3600,
                "source_name": "News Source 2",
                "type": "news",
                "relevance": 0.85
            }
        ]

    def _mock_rss_feed_handler(self, query, config):
        """Mock RSS feed handler."""
        current_time = time.time()
        return [
            {
                "title": f"RSS item about {query}",
                "url": "https://rss-example.com/item1",
                "content": f"RSS feed content about {query}.",
                "timestamp": current_time - 5400,
                "source_name": "RSS Feed 1",
                "type": "rss",
                "relevance": 0.75
            }
        ]

    def _mock_weather_api_handler(self, query, config):
        """Mock weather API handler."""
        current_time = time.time()
        return [
            {
                "title": "Weather information",
                "content": f"Weather for location mentioned in '{query}': 22°C, Partly cloudy.",
                "timestamp": current_time,
                "type": "weather",
                "location": "Example Location",
                "relevance": 0.95
            }
        ]

    def _mock_stocks_api_handler(self, query, config):
        """Mock stocks API handler."""
        current_time = time.time()
        return [
            {
                "title": "Stock data",
                "content": f"Stock information related to '{query}': AAPL: $150.25, ↑$2.5 (1.7%).",
                "timestamp": current_time,
                "type": "financial",
                "symbol": "AAPL",
                "relevance": 0.9
            }
        ]

    def test_initialization(self):
        """Test proper initialization of RealTimeRetriever."""
        self.assertEqual(self.retriever.provider, "openai")
        self.assertEqual(self.retriever.model, "gpt-4o")
        self.assertEqual(self.retriever.update_interval, 3600)
        self.assertEqual(self.retriever.max_age, 86400)
        self.assertTrue(self.retriever.cache_enabled)
        self.assertEqual(self.retriever.cache_ttl, 300)
        self.assertIsInstance(self.retriever.source_handlers, dict)
        self.assertIn("web_search", self.retriever.source_handlers)
        self.assertIn("news_api", self.retriever.source_handlers)
        self.assertIn("rss_feed", self.retriever.source_handlers)
        self.assertIn("weather_api", self.retriever.source_handlers)
        self.assertIn("stocks_api", self.retriever.source_handlers)

    def test_source_selection(self):
        """Test source selection based on query content."""
        # News-related query
        news_sources = self.retriever._select_sources_for_query("Latest news about AI")
        self.assertIn("news_api", news_sources)
        
        # Weather-related query
        weather_sources = self.retriever._select_sources_for_query("What's the weather in New York?")
        self.assertIn("weather_api", weather_sources)
        
        # Finance-related query
        finance_sources = self.retriever._select_sources_for_query("Stock price for AAPL")
        self.assertIn("stocks_api", finance_sources)
        
        # General query
        general_sources = self.retriever._select_sources_for_query("What is machine learning?")
        self.assertIn("web_search", general_sources)

    def test_retrieve_basic(self):
        """Test basic retrieval functionality."""
        result = self.retriever.retrieve(
            query="Latest AI developments",
            max_results=3
        )
        
        self.assertEqual(result["query"], "Latest AI developments")
        self.assertIn("results", result)
        self.assertIn("content", result)
        self.assertIn("sources_used", result)
        self.assertIn("timestamp", result)
        self.assertIn("is_cached", result)
        self.assertFalse(result["is_cached"])
        self.assertLessEqual(len(result["results"]), 3)

    def test_retrieve_with_specific_sources(self):
        """Test retrieval using specific sources."""
        result = self.retriever.retrieve(
            query="AI news",
            sources=["news_api", "web_search"],
            max_results=4
        )
        
        self.assertEqual(result["query"], "AI news")
        self.assertEqual(result["sources_used"], ["news_api", "web_search"])
        self.assertLessEqual(len(result["results"]), 4)
        
        # Verify results come from specified sources
        source_types = [r.get("source") for r in result["results"]]
        for source_type in source_types:
            self.assertIn(source_type, ["news_api", "web_search"])

    def test_caching(self):
        """Test that caching works correctly."""
        # First request should not be cached
        first_result = self.retriever.retrieve(
            query="AI advancements",
            max_results=2
        )
        self.assertFalse(first_result["is_cached"])
        
        # Second identical request should be cached
        second_result = self.retriever.retrieve(
            query="AI advancements",
            max_results=2
        )
        self.assertTrue(second_result["is_cached"])
        
        # Force update should bypass cache
        force_result = self.retriever.retrieve(
            query="AI advancements",
            max_results=2,
            force_update=True
        )
        self.assertFalse(force_result["is_cached"])
        
        # Clear cache and verify cache is empty
        self.retriever.clear_cache()
        self.assertEqual(len(self.retriever.cache), 0)
        
        # After clearing cache, request should not be cached
        after_clear_result = self.retriever.retrieve(
            query="AI advancements",
            max_results=2
        )
        self.assertFalse(after_clear_result["is_cached"])

    def test_filtering_by_age(self):
        """Test filtering results by age."""
        # Create test results with different ages
        current_time = time.time()
        test_results = [
            {"content": "Recent result", "timestamp": current_time - 3600},  # 1 hour old
            {"content": "Old result", "timestamp": current_time - 90000},    # 25 hours old
            {"content": "Very recent", "timestamp": current_time - 60}       # 1 minute old
        ]
        
        # Set max_age to 24 hours
        self.retriever.max_age = 86400  # 24 hours in seconds
        
        # Filter results
        filtered = self.retriever._filter_by_age(test_results)
        
        # Should only have 2 results (the 25-hour old one should be filtered out)
        self.assertEqual(len(filtered), 2)
        
        # Verify the old result was filtered out
        contents = [r["content"] for r in filtered]
        self.assertIn("Recent result", contents)
        self.assertIn("Very recent", contents)
        self.assertNotIn("Old result", contents)

    def test_ranking_results(self):
        """Test ranking results by relevance and recency."""
        # Create test results with different relevance and recency
        current_time = time.time()
        test_results = [
            {"content": "Medium relevance, old", "relevance": 0.5, "timestamp": current_time - 43200},  # 12 hours old
            {"content": "High relevance, newer", "relevance": 0.9, "timestamp": current_time - 7200},   # 2 hours old
            {"content": "Low relevance, newest", "relevance": 0.3, "timestamp": current_time - 300}     # 5 minutes old
        ]
        
        # Rank results
        ranked = self.retriever._rank_results(test_results, "test query")
        
        # The high relevance, newer result should be first due to combined score
        self.assertEqual(ranked[0]["content"], "High relevance, newer")
        
        # The low relevance, newest result should be second due to recency
        self.assertEqual(ranked[1]["content"], "Low relevance, newest")
        
        # The medium relevance, old result should be last
        self.assertEqual(ranked[2]["content"], "Medium relevance, old")

    @patch('threading.Thread')
    def test_continuous_update(self, mock_thread):
        """Test continuous update functionality."""
        # Mock the Thread class to avoid actual threading
        mock_thread_instance = MagicMock()
        mock_thread.return_value = mock_thread_instance
        
        # Create mock callback
        callback = MagicMock()
        
        # Start continuous update
        update_id = self.retriever.start_continuous_update(
            query="AI news",
            update_callback=callback,
            update_interval=60,
            max_results=2
        )
        
        # Verify update was started
        self.assertIsInstance(update_id, str)
        self.assertIn(update_id, self.retriever._active_updates)
        self.assertEqual(self.retriever._active_updates[update_id]["query"], "AI news")
        self.assertEqual(self.retriever._active_updates[update_id]["update_interval"], 60)
        
        # Verify thread was started
        mock_thread_instance.start.assert_called_once()
        
        # Stop continuous update
        result = self.retriever.stop_continuous_update(update_id)
        self.assertTrue(result)
        self.assertNotIn(update_id, self.retriever._active_updates)
        
        # Try to stop non-existent update
        result = self.retriever.stop_continuous_update("non-existent-id")
        self.assertFalse(result)

    def test_integrate_with_rag(self):
        """Test integration with RAG results."""
        # Mock RAG results
        rag_results = [
            {
                "content": "RAG document content about AI",
                "source": "document_db",
                "timestamp": time.time() - 86400 * 30  # 30 days old
            },
            {
                "content": "Another RAG document about machine learning",
                "source": "knowledge_base",
                "timestamp": time.time() - 86400 * 15  # 15 days old
            }
        ]
        
        # Integrate with real-time information
        integrated = self.retriever.integrate_with_rag(
            query="Latest AI research",
            rag_results=rag_results,
            max_results=2
        )
        
        # Verify structure
        self.assertEqual(integrated["query"], "Latest AI research")
        self.assertEqual(integrated["rag_results"], rag_results)
        self.assertIn("realtime_results", integrated)
        self.assertIn("combined_content", integrated)
        
        # Verify combined content has both RAG and real-time results
        content_types = [item["type"] for item in integrated["combined_content"]]
        self.assertIn("document", content_types)
        self.assertIn("realtime", content_types)
        
        # Verify content is sorted by recency (real-time results should come first)
        self.assertEqual(integrated["combined_content"][0]["type"], "realtime")

    def test_search_timerange(self):
        """Test searching within a specific time range."""
        # Set up time range
        end_time = time.time()
        start_time = end_time - 7200  # 2 hours ago
        
        # Search within time range
        result = self.retriever.search_timerange(
            query="AI developments",
            start_time=start_time,
            end_time=end_time,
            max_results=3
        )
        
        # Verify structure
        self.assertEqual(result["query"], "AI developments")
        self.assertIn("results", result)
        self.assertIn("timerange", result)
        self.assertEqual(result["timerange"]["start"], start_time)
        self.assertEqual(result["timerange"]["end"], end_time)
        
        # Verify all results are within the time range
        for item in result["results"]:
            item_time = item.get("timestamp") or item.get("retrieved_at")
            self.assertIsNotNone(item_time)
            self.assertGreaterEqual(item_time, start_time)
            self.assertLessEqual(item_time, end_time)

    def test_get_source_info(self):
        """Test getting information about available sources."""
        source_info = self.retriever.get_source_info()
        
        # Verify structure
        self.assertIsInstance(source_info, dict)
        self.assertIn("web_search", source_info)
        self.assertIn("news_api", source_info)
        self.assertIn("rss_feed", source_info)
        self.assertIn("weather_api", source_info)
        self.assertIn("stocks_api", source_info)
        
        # Verify each source has the required fields
        for source_name, info in source_info.items():
            self.assertIn("enabled", info)
            self.assertIn("description", info)
            self.assertIn("update_interval", info)
            self.assertIn("max_age", info)

    def test_datetime_handling_in_timerange_search(self):
        """Test handling different datetime formats in timerange search."""
        # Test with datetime objects
        now = datetime.datetime.now()
        two_hours_ago = now - datetime.timedelta(hours=2)
        
        result1 = self.retriever.search_timerange(
            query="AI research",
            start_time=two_hours_ago,
            end_time=now,
            max_results=2
        )
        self.assertIn("timerange", result1)
        
        # Test with ISO format strings
        now_str = now.isoformat()
        two_hours_ago_str = two_hours_ago.isoformat()
        
        result2 = self.retriever.search_timerange(
            query="AI research",
            start_time=two_hours_ago_str,
            end_time=now_str,
            max_results=2
        )
        self.assertIn("timerange", result2)
        
        # Test with only start_time (end_time defaults to now)
        result3 = self.retriever.search_timerange(
            query="AI research",
            start_time=two_hours_ago,
            max_results=2
        )
        self.assertIn("timerange", result3)

    def test_weather_handler_location_extraction(self):
        """Test weather handler's ability to extract location."""
        # Create a test instance with a real weather handler for this test
        retriever = RealTimeRetriever(cache_enabled=False)
        retriever._weather_api_handler = self.retriever._weather_api_handler
        
        # Test with location in query
        result1 = retriever.retrieve(
            query="Weather in Paris today",
            sources=["weather_api"],
            max_results=1
        )
        self.assertEqual(len(result1["results"]), 1)
        self.assertIn("Paris", result1["results"][0]["content"])
        
        # Test with different location
        result2 = retriever.retrieve(
            query="Weather in Tokyo tomorrow",
            sources=["weather_api"],
            max_results=1
        )
        self.assertEqual(len(result2["results"]), 1)
        self.assertIn("Tokyo", result2["results"][0]["content"])

    def test_stocks_handler_symbol_extraction(self):
        """Test stocks handler's ability to extract stock symbols."""
        # Create a test instance with a real stocks handler for this test
        retriever = RealTimeRetriever(cache_enabled=False)
        retriever._stocks_api_handler = self.retriever._stocks_api_handler
        
        # Test with explicit symbol
        result1 = retriever.retrieve(
            query="Stock price for AAPL",
            sources=["stocks_api"],
            max_results=1
        )
        self.assertEqual(len(result1["results"]), 1)
        self.assertIn("AAPL", result1["results"][0]["content"])
        
        # Test with company name
        result2 = retriever.retrieve(
            query="What is the current price of Apple stock?",
            sources=["stocks_api"],
            max_results=1
        )
        self.assertEqual(len(result2["results"]), 1)
        self.assertIn("AAPL", result2["results"][0]["content"])


if __name__ == "__main__":
    unittest.main() 