"""
Unit tests for the FAISSRAG implementation.

This module contains tests for the FAISSRAG class.
"""

import unittest
import tempfile
import shutil
import os
from unittest.mock import MagicMock, patch

from src.deep_research_core.reasoning.faiss_rag import FAISSRAG


class TestFAISSRAG(unittest.TestCase):
    """Test case for the FAISSRAG class."""

    @patch("src.deep_research_core.reasoning.faiss_rag.FAISSVectorStore")
    @patch("src.deep_research_core.reasoning.faiss_rag.openai_provider")
    @patch("src.deep_research_core.reasoning.faiss_rag.anthropic_provider")
    @patch("src.deep_research_core.reasoning.faiss_rag.openrouter_provider")
    def setUp(self, mock_openrouter, mock_anthropic, mock_openai, mock_vector_store_class):
        """Set up the test case."""
        # Create a temporary directory for the index
        self.temp_dir = tempfile.mkdtemp()
        self.index_path = os.path.join(self.temp_dir, "faiss_index")

        # Create mock vector store
        self.mock_vector_store = MagicMock()
        mock_vector_store_class.return_value = self.mock_vector_store

        # Initialize FAISSRAG
        self.rag = FAISSRAG(
            provider="openai",
            model="gpt-4o",
            temperature=0.7,
            max_tokens=2000,
            embedding_model="text-embedding-ada-002",
            index_path=self.index_path,
            embedding_dim=1536,
            index_type="Flat",
            metric_type="ip",
            use_gpu=False,
            gpu_id=0,
            top_k=5
        )

        # Sample documents for testing
        self.sample_documents = [
            {
                "content": "This is a test document.",
                "source": "Test Source",
                "title": "Test Document"
            },
            {
                "content": "This is another test document.",
                "source": "Test Source 2",
                "title": "Test Document 2"
            }
        ]

        # Sample embeddings
        self.sample_embeddings = [
            [0.1] * 1536,
            [0.2] * 1536
        ]

        # Mock _get_embeddings method
        self.rag._get_embeddings = MagicMock(return_value=self.sample_embeddings)

        # Mock vector store methods
        self.mock_vector_store.add_documents.return_value = ["doc1", "doc2"]
        self.mock_vector_store.search.return_value = self.sample_documents
        self.mock_vector_store.update_documents.return_value = True
        self.mock_vector_store.delete_documents.return_value = True
        self.mock_vector_store.get_document.return_value = self.sample_documents[0]
        self.mock_vector_store.get_documents.return_value = self.sample_documents
        self.mock_vector_store.get_embedding.return_value = [0.1] * 1536
        self.mock_vector_store.get_embeddings.return_value = [[0.1] * 1536, [0.2] * 1536]
        self.mock_vector_store.create_index.return_value = True
        self.mock_vector_store.optimize.return_value = True
        self.mock_vector_store.backup.return_value = True
        self.mock_vector_store.restore.return_value = True
        self.mock_vector_store.clear.return_value = True
        self.mock_vector_store.count.return_value = 2

        # Mock API provider
        self.mock_api_provider = MagicMock()
        self.mock_api_provider.complete.return_value = "This is a test answer."
        self.rag.api_provider = self.mock_api_provider

    def tearDown(self):
        """Clean up after the test."""
        # Remove the temporary directory
        shutil.rmtree(self.temp_dir)

    def test_initialization(self):
        """Test initialization of FAISSRAG."""
        self.assertEqual(self.rag.provider, "openai")
        self.assertEqual(self.rag.model, "gpt-4o")
        self.assertEqual(self.rag.temperature, 0.7)
        self.assertEqual(self.rag.max_tokens, 2000)
        self.assertEqual(self.rag.embedding_model, "text-embedding-ada-002")
        self.assertEqual(self.rag.top_k, 5)
        self.assertEqual(self.rag.index_path, self.index_path)
        self.assertEqual(self.rag.embedding_dim, 1536)
        self.assertEqual(self.rag.index_type, "Flat")
        self.assertEqual(self.rag.metric_type, "ip")
        self.assertFalse(self.rag.use_gpu)
        self.assertEqual(self.rag.gpu_id, 0)

    def test_add_documents(self):
        """Test adding documents."""
        result = self.rag.add_documents(self.sample_documents)

        # Check that _get_embeddings was called with the correct arguments
        self.rag._get_embeddings.assert_called_once_with(
            ["This is a test document.", "This is another test document."]
        )

        # Check that vector_store.add_documents was called with the correct arguments
        self.mock_vector_store.add_documents.assert_called_once_with(
            self.sample_documents, self.sample_embeddings
        )

        # Check the result
        self.assertEqual(result, ["doc1", "doc2"])

    def test_search(self):
        """Test searching for documents."""
        result = self.rag.search("Test query")

        # Check that _get_embeddings was called with the correct arguments
        self.rag._get_embeddings.assert_called_once_with(["Test query"])

        # Check that vector_store.search was called with the correct arguments
        self.mock_vector_store.search.assert_called_once_with(
            query_embedding=self.sample_embeddings[0],
            top_k=5,
            filter_expr=None
        )

        # Check the result
        self.assertEqual(result, self.sample_documents)

    def test_search_with_filter(self):
        """Test searching for documents with a filter."""
        filter_expr = "source == 'Test Source'"
        result = self.rag.search("Test query", filter_expr=filter_expr)

        # Check that vector_store.search was called with the correct arguments
        self.mock_vector_store.search.assert_called_once_with(
            query_embedding=self.sample_embeddings[0],
            top_k=5,
            filter_expr=filter_expr
        )

        # Check the result
        self.assertEqual(result, self.sample_documents)

    def test_process(self):
        """Test processing a query."""
        result = self.rag.process("Test query")

        # Check that search was called
        self.rag._get_embeddings.assert_called_once_with(["Test query"])
        self.mock_vector_store.search.assert_called_once()

        # Check that the API provider was called
        self.mock_api_provider.complete.assert_called_once()

        # Check the result
        self.assertEqual(result["query"], "Test query")
        self.assertEqual(result["answer"], "This is a test answer.")
        self.assertEqual(result["documents"], self.sample_documents)
        self.assertEqual(result["model"], "gpt-4o")
        self.assertEqual(result["provider"], "openai")

    def test_process_with_callback(self):
        """Test processing a query with a callback."""
        callback = MagicMock()
        result = self.rag.process("Test query", callback=callback)

        # Check that the API provider was called with the callback
        self.mock_api_provider.complete.assert_called_once()
        args, kwargs = self.mock_api_provider.complete.call_args
        self.assertTrue(kwargs["stream"])
        self.assertEqual(kwargs["callback"], callback)

    def test_error_handling_add_documents(self):
        """Test error handling when adding documents."""
        # Make the vector store raise an exception
        self.mock_vector_store.add_documents.side_effect = Exception("Test error")

        # Check that the exception is propagated
        with self.assertRaises(Exception):
            self.rag.add_documents(self.sample_documents)

    def test_error_handling_search(self):
        """Test error handling when searching."""
        # Make the vector store raise an exception
        self.mock_vector_store.search.side_effect = Exception("Test error")

        # Check that the exception is propagated
        with self.assertRaises(Exception):
            self.rag.search("Test query")

    def test_error_handling_process(self):
        """Test error handling when processing a query."""
        # Make the vector store raise an exception
        self.mock_vector_store.search.side_effect = Exception("Test error")

        # Check that the exception is propagated
        with self.assertRaises(Exception):
            self.rag.process("Test query")

    def test_update_documents(self):
        """Test updating documents."""
        result = self.rag.update_documents(["doc1"], [self.sample_documents[0]])

        # Check that _get_embeddings was called with the correct arguments
        self.rag._get_embeddings.assert_called_with(["This is a test document."])

        # Check that vector_store.update_documents was called with the correct arguments
        self.mock_vector_store.update_documents.assert_called_once_with(
            ["doc1"], [self.sample_documents[0]], self.sample_embeddings
        )

        # Check the result
        self.assertTrue(result)

    def test_delete_documents(self):
        """Test deleting documents."""
        result = self.rag.delete_documents(["doc1"])

        # Check that vector_store.delete_documents was called with the correct arguments
        self.mock_vector_store.delete_documents.assert_called_once_with(["doc1"])

        # Check the result
        self.assertTrue(result)

    def test_get_document(self):
        """Test getting a document by ID."""
        result = self.rag.get_document("doc1")

        # Check that vector_store.get_document was called with the correct arguments
        self.mock_vector_store.get_document.assert_called_once_with("doc1")

        # Check the result
        self.assertEqual(result, self.sample_documents[0])

    def test_get_documents(self):
        """Test getting multiple documents by ID."""
        result = self.rag.get_documents(["doc1", "doc2"])

        # Check that vector_store.get_documents was called with the correct arguments
        self.mock_vector_store.get_documents.assert_called_once_with(["doc1", "doc2"])

        # Check the result
        self.assertEqual(result, self.sample_documents)

    def test_get_embedding(self):
        """Test getting an embedding for a document."""
        result = self.rag.get_embedding("doc1")

        # Check that vector_store.get_embedding was called with the correct arguments
        self.mock_vector_store.get_embedding.assert_called_once_with("doc1")

        # Check the result
        self.assertEqual(result, [0.1] * 1536)

    def test_get_embeddings(self):
        """Test getting embeddings for multiple documents."""
        result = self.rag.get_embeddings(["doc1", "doc2"])

        # Check that vector_store.get_embeddings was called with the correct arguments
        self.mock_vector_store.get_embeddings.assert_called_once_with(["doc1", "doc2"])

        # Check the result
        self.assertEqual(result, [[0.1] * 1536, [0.2] * 1536])

    def test_create_index(self):
        """Test creating a new index."""
        index_params = {"index_type": "HNSW"}
        result = self.rag.create_index(index_params)

        # Check that vector_store.create_index was called with the correct arguments
        self.mock_vector_store.create_index.assert_called_once_with(index_params)

        # Check the result
        self.assertTrue(result)

    def test_optimize(self):
        """Test optimizing the index."""
        result = self.rag.optimize()

        # Check that vector_store.optimize was called
        self.mock_vector_store.optimize.assert_called_once()

        # Check the result
        self.assertTrue(result)

    def test_backup(self):
        """Test backing up the index."""
        backup_path = os.path.join(self.temp_dir, "backup")
        result = self.rag.backup(backup_path)

        # Check that vector_store.backup was called with the correct arguments
        self.mock_vector_store.backup.assert_called_once_with(backup_path)

        # Check the result
        self.assertTrue(result)

    def test_restore(self):
        """Test restoring the index from a backup."""
        backup_path = os.path.join(self.temp_dir, "backup")
        result = self.rag.restore(backup_path)

        # Check that vector_store.restore was called with the correct arguments
        self.mock_vector_store.restore.assert_called_once_with(backup_path)

        # Check the result
        self.assertTrue(result)

    def test_clear(self):
        """Test clearing the index."""
        result = self.rag.clear()

        # Check that vector_store.clear was called
        self.mock_vector_store.clear.assert_called_once()

        # Check the result
        self.assertTrue(result)

    def test_count(self):
        """Test counting documents in the index."""
        result = self.rag.count()

        # Check that vector_store.count was called
        self.mock_vector_store.count.assert_called_once()

        # Check the result
        self.assertEqual(result, 2)


if __name__ == "__main__":
    unittest.main()
