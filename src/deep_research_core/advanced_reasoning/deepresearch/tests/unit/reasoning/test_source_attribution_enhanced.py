"""
Unit tests for enhanced Source Attribution module.
"""

import unittest
from datetime import datetime
from src.deep_research_core.reasoning.source_attribution import SourceAttribution
from src.deep_research_core.reasoning.source_attribution_rag import SourceAttributionRAG

# Mock RAG class for testing
class MockRAG:
    def __init__(self):
        self.documents = []
    
    def add_document(self, content, metadata=None):
        doc_id = f"doc_{len(self.documents)}"
        self.documents.append({
            "id": doc_id,
            "content": content,
            "metadata": metadata or {}
        })
        return doc_id
    
    def process(self, query, **kwargs):
        return {
            "query": query,
            "documents": self.documents,
            "response": f"Response to: {query}. Using information from documents."
        }

class TestSourceAttributionEnhanced(unittest.TestCase):
    """Test cases for enhanced SourceAttribution class."""
    
    def setUp(self):
        """Set up test cases."""
        self.attribution = SourceAttribution(
            citation_style="apa",
            track_token_level=True,
            language="en",
            auto_detect_sources=True,
            similarity_threshold=0.7,
            citation_format="inline"
        )
        
        # Add some test sources
        self.attribution.register_source(
            "source1",
            {
                "title": "Test Document 1",
                "content": "This is a test document with some content about artificial intelligence.",
                "author": "John Doe",
                "publication_date": "2023",
                "url": "https://example.com/doc1",
                "publisher": "Example Publisher"
            }
        )
        
        self.attribution.register_source(
            "source2",
            {
                "title": "Test Document 2",
                "content": "This is another test document with more details about machine learning.",
                "author": "Jane Smith",
                "publication_date": "2022",
                "url": "https://example.org/doc2"
            }
        )
    
    def test_detect_source_usage(self):
        """Test automatic source usage detection."""
        text = "Artificial intelligence is a rapidly growing field. Machine learning is a subset of AI."
        
        # Detect source usage
        detected = self.attribution.detect_source_usage(text)
        
        # Check that at least one source was detected
        self.assertTrue(len(detected) > 0)
        
        # Check that the detected source is in used_sources
        for source_id in detected:
            self.assertIn(source_id, self.attribution.used_sources)
    
    def test_find_source_by_content(self):
        """Test finding a source by content."""
        # Exact content match
        source_id = self.attribution.find_source_by_content(
            "This is a test document with some content about artificial intelligence."
        )
        self.assertEqual(source_id, "source1")
        
        # Similar content
        source_id = self.attribution.find_source_by_content(
            "This is a test document with content about AI."
        )
        self.assertIsNotNone(source_id)
    
    def test_register_sources_batch(self):
        """Test registering multiple sources at once."""
        sources = [
            {
                "id": "source3",
                "title": "Test Document 3",
                "content": "This is the third test document.",
                "author": "Alice Johnson"
            },
            {
                "id": "source4",
                "title": "Test Document 4",
                "content": "This is the fourth test document.",
                "author": "Bob Williams"
            }
        ]
        
        # Register sources in batch
        source_ids = self.attribution.register_sources_batch(sources)
        
        # Check that both sources were registered
        self.assertEqual(len(source_ids), 2)
        self.assertEqual(source_ids[0], "source3")
        self.assertEqual(source_ids[1], "source4")
        
        # Check that sources are in the sources dictionary
        self.assertIn("source3", self.attribution.sources)
        self.assertIn("source4", self.attribution.sources)
    
    def test_get_source_content(self):
        """Test getting source content."""
        content = self.attribution.get_source_content("source1")
        self.assertEqual(content, "This is a test document with some content about artificial intelligence.")
        
        # Test with non-existent source
        content = self.attribution.get_source_content("non_existent")
        self.assertIsNone(content)

class TestSourceAttributionRAG(unittest.TestCase):
    """Test cases for SourceAttributionRAG class."""
    
    def setUp(self):
        """Set up test cases."""
        self.mock_rag = MockRAG()
        
        # Add some test documents
        self.mock_rag.add_document(
            "This is a test document with information about artificial intelligence.",
            {"title": "AI Document", "author": "John Doe", "date": "2023"}
        )
        
        self.mock_rag.add_document(
            "Machine learning is a subset of artificial intelligence.",
            {"title": "ML Document", "author": "Jane Smith", "date": "2022"}
        )
        
        # Initialize SourceAttributionRAG
        self.attribution_rag = SourceAttributionRAG(
            rag_instance=self.mock_rag,
            citation_style="apa",
            track_token_level=True,
            language="en",
            auto_register_sources=True,
            auto_detect_usage=True,
            citation_format="inline"
        )
    
    def test_process(self):
        """Test processing a query with source attribution."""
        result = self.attribution_rag.process(
            query="What is artificial intelligence?",
            include_citations=True,
            include_bibliography=True
        )
        
        # Check that the result contains the expected keys
        self.assertIn("response", result)
        self.assertIn("response_with_citations", result)
        self.assertIn("bibliography", result)
        self.assertIn("citation_metrics", result)
        
        # Check that sources were registered
        self.assertTrue(len(self.attribution_rag.attribution.sources) > 0)
    
    def test_add_citations(self):
        """Test adding citations to text."""
        text = "Artificial intelligence is a rapidly growing field. Machine learning is a subset of AI."
        
        # Process a query first to register sources
        self.attribution_rag.process(
            query="What is artificial intelligence?",
            include_citations=True
        )
        
        # Add citations to text
        text_with_citations = self.attribution_rag.add_citations(text)
        
        # Check that citations were added
        self.assertNotEqual(text, text_with_citations)
    
    def test_get_bibliography(self):
        """Test getting bibliography."""
        # Process a query first to register sources and track usage
        self.attribution_rag.process(
            query="What is artificial intelligence?",
            include_citations=True
        )
        
        # Get bibliography
        bibliography = self.attribution_rag.get_bibliography()
        
        # Check that bibliography is not empty
        self.assertTrue(len(bibliography) > 0)
    
    def test_get_citation_metrics(self):
        """Test getting citation metrics."""
        # Process a query first to register sources and track usage
        self.attribution_rag.process(
            query="What is artificial intelligence?",
            include_citations=True
        )
        
        # Get citation metrics
        metrics = self.attribution_rag.get_citation_metrics()
        
        # Check that metrics contain expected keys
        self.assertIn("total_sources", metrics)
        self.assertIn("used_sources", metrics)
        self.assertIn("cited_sources", metrics)
        self.assertIn("citation_ratio", metrics)
        self.assertIn("avg_credibility", metrics)

if __name__ == "__main__":
    unittest.main()
