"""
Unit tests for handling irrelevant documents in CoTRAG.

This module contains tests for the special case handling when RAG returns
irrelevant documents, including fallback strategies and optimizations.
"""

import unittest
from unittest.mock import MagicMock, patch, ANY
import sys
import os
import json
import tempfile
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))

from src.deep_research_core.reasoning.cot_rag import CoTRAG
from src.deep_research_core.reasoning.cotrag_advanced_strategies import CoTRAGAdvancedStrategies


class TestCoTRAGIrrelevantDocs(unittest.TestCase):
    """Test case for handling irrelevant documents in CoTRAG."""

    def setUp(self):
        """Set up test fixtures."""
        # Create mock objects
        self.mock_api_provider = MagicMock()
        self.mock_api_provider.generate.return_value = "AI là trí tuệ nhân tạo. Nó là một lĩnh vực của khoa học máy tính."

        # Create relevant documents
        self.relevant_docs = [
            {
                "content": "AI là viết tắt của Artificial Intelligence (Trí tuệ nhân tạo).",
                "score": 0.95
            },
            {
                "content": "Trí tuệ nhân tạo là một nhánh của khoa học máy tính.",
                "score": 0.85
            }
        ]

        # Create irrelevant documents
        self.irrelevant_docs = [
            {
                "content": "Phở là một món ăn truyền thống của Việt Nam.",
                "score": 0.3
            },
            {
                "content": "Cách nấu phở bò ngon nhất là dùng xương bò hầm trong 8 giờ.",
                "score": 0.25
            }
        ]

        # Create mock vector stores
        self.mock_relevant_store = MagicMock()
        self.mock_relevant_store.search.return_value = self.relevant_docs

        self.mock_irrelevant_store = MagicMock()
        self.mock_irrelevant_store.search.return_value = self.irrelevant_docs

        # Create patches
        self.patches = [
            patch('src.deep_research_core.models.api.openai.openai_provider.generate',
                  return_value="AI là trí tuệ nhân tạo. Nó là một lĩnh vực của khoa học máy tính."),
            patch('src.deep_research_core.models.api.openrouter.openrouter_provider.generate',
                  return_value="AI là trí tuệ nhân tạo. Nó là một lĩnh vực của khoa học máy tính."),
            patch('src.deep_research_core.models.api.anthropic.anthropic_provider.generate',
                  return_value="AI là trí tuệ nhân tạo. Nó là một lĩnh vực của khoa học máy tính.")
        ]

        # Start patches
        for p in self.patches:
            p.start()

        # Create sample queries
        self.sample_queries = {
            "factual": "AI là gì?",
            "reasoning": "Tại sao AI quan trọng trong thời đại số?",
            "complex": "So sánh và phân tích sự khác biệt giữa học máy, học sâu và trí tuệ nhân tạo, đồng thời giải thích cách chúng liên quan đến nhau.",
            "irrelevant": "Làm thế nào để nấu phở bò ngon?",
            "vietnamese": "Trí tuệ nhân tạo có thể giúp gì cho nông nghiệp Việt Nam?"
        }

    def tearDown(self):
        """Clean up after tests."""
        # Stop patches
        for p in self.patches:
            p.stop()

    def test_basic_irrelevant_docs_detection(self):
        """Test basic detection of irrelevant documents."""
        # Create CoTRAG with irrelevant document handling
        cotrag = CoTRAG(
            provider="openai",
            model="gpt-4o",
            temperature=0.7,
            max_tokens=2000,
            handle_irrelevant_docs=True,
            relevance_threshold=0.5
        )

        # Mock components
        cotrag.api_provider = self.mock_api_provider
        cotrag.vector_store = self.mock_irrelevant_store

        # Process a query
        result = cotrag.process(self.sample_queries["factual"])

        # Verify that irrelevant documents are detected
        self.assertIn("low_relevance_documents", result)
        self.assertTrue(result["low_relevance_documents"])

    def test_special_system_prompt_for_irrelevant_docs(self):
        """Test that a special system prompt is used for irrelevant documents."""
        # Create CoTRAG with irrelevant document handling
        cotrag = CoTRAG(
            provider="openai",
            model="gpt-4o",
            temperature=0.7,
            max_tokens=2000,
            handle_irrelevant_docs=True,
            relevance_threshold=0.5
        )

        # Mock components
        cotrag.api_provider = self.mock_api_provider
        cotrag.vector_store = self.mock_irrelevant_store

        # Process a query
        cotrag.process(self.sample_queries["factual"])

        # Verify that the API was called with the special system prompt
        call_args = self.mock_api_provider.generate.call_args[1]
        self.assertIn("system_prompt", call_args)
        self.assertIn("low relevance", call_args["system_prompt"].lower())

    def test_weight_adjustment_for_irrelevant_docs(self):
        """Test weight adjustment when documents are irrelevant."""
        # Create CoTRAG with dynamic weighting and irrelevant document handling
        cotrag = CoTRAG(
            provider="openai",
            model="gpt-4o",
            temperature=0.7,
            max_tokens=2000,
            use_dynamic_weighting=True,
            handle_irrelevant_docs=True,
            relevance_threshold=0.5,
            weighting_strategy="document_relevance"
        )

        # Mock components
        cotrag.api_provider = self.mock_api_provider
        
        # Test with relevant documents
        cotrag.vector_store = self.mock_relevant_store
        relevant_result = cotrag.process(self.sample_queries["factual"])
        
        # Test with irrelevant documents
        cotrag.vector_store = self.mock_irrelevant_store
        irrelevant_result = cotrag.process(self.sample_queries["factual"])
        
        # Verify that weights are adjusted for irrelevant documents
        self.assertIn("weights", relevant_result)
        self.assertIn("weights", irrelevant_result)
        
        # With irrelevant documents, should rely more on CoT
        self.assertGreater(
            irrelevant_result["weights"]["cot_weight"],
            relevant_result["weights"]["cot_weight"]
        )

    def test_advanced_strategies_for_irrelevant_docs(self):
        """Test advanced strategies for handling irrelevant documents."""
        # Create CoTRAGAdvancedStrategies
        cotrag_advanced = CoTRAGAdvancedStrategies(
            provider="openai",
            model="gpt-4o",
            temperature=0.7,
            max_tokens=2000,
            handle_irrelevant_docs=True,
            relevance_threshold=0.5,
            use_query_expansion=True,
            use_iterative_retrieval=True
        )

        # Mock components
        cotrag_advanced.api_provider = self.mock_api_provider
        cotrag_advanced.vector_store = self.mock_irrelevant_store
        
        # Mock query expansion
        original_expand_query = cotrag_advanced._expand_query
        cotrag_advanced._expand_query = MagicMock()
        cotrag_advanced._expand_query.return_value = [
            self.sample_queries["factual"],
            "Trí tuệ nhân tạo là gì?",
            "Định nghĩa của AI"
        ]
        
        # Mock iterative retrieval
        original_iterative_retrieval = cotrag_advanced._iterative_retrieval
        cotrag_advanced._iterative_retrieval = MagicMock()
        cotrag_advanced._iterative_retrieval.return_value = self.relevant_docs
        
        # Process a query
        result = cotrag_advanced.process(self.sample_queries["factual"])
        
        # Verify that advanced strategies were used
        cotrag_advanced._expand_query.assert_called_once()
        cotrag_advanced._iterative_retrieval.assert_called_once()
        
        # Verify that advanced strategies are in the result
        self.assertIn("advanced_strategies", result)
        self.assertTrue(result["advanced_strategies"]["query_expansion"])
        self.assertTrue(result["advanced_strategies"]["iterative_retrieval"])
        
        # Restore original methods
        cotrag_advanced._expand_query = original_expand_query
        cotrag_advanced._iterative_retrieval = original_iterative_retrieval

    def test_iterative_retrieval_improvement(self):
        """Test that iterative retrieval improves document relevance."""
        # Create CoTRAGAdvancedStrategies
        cotrag_advanced = CoTRAGAdvancedStrategies(
            provider="openai",
            model="gpt-4o",
            temperature=0.7,
            max_tokens=2000,
            handle_irrelevant_docs=True,
            relevance_threshold=0.5,
            use_iterative_retrieval=True,
            max_retrieval_iterations=2
        )

        # Mock components
        cotrag_advanced.api_provider = self.mock_api_provider
        
        # First return irrelevant docs, then relevant docs on second call
        mock_vector_store = MagicMock()
        mock_vector_store.search.side_effect = [
            self.irrelevant_docs,  # First call returns irrelevant docs
            self.relevant_docs     # Second call returns relevant docs
        ]
        cotrag_advanced.vector_store = mock_vector_store
        
        # Process a query
        result = cotrag_advanced.process(self.sample_queries["factual"])
        
        # Verify that vector store was called twice (initial + iterative)
        self.assertEqual(mock_vector_store.search.call_count, 2)
        
        # Verify that documents in result are the relevant ones
        self.assertGreaterEqual(result["documents"][0]["score"], 0.8)

    def test_fallback_to_pure_cot_for_irrelevant_docs(self):
        """Test fallback to pure CoT when documents are irrelevant."""
        # Create CoTRAGAdvancedStrategies with fallback strategies
        cotrag_advanced = CoTRAGAdvancedStrategies(
            provider="openai",
            model="gpt-4o",
            temperature=0.7,
            max_tokens=2000,
            handle_irrelevant_docs=True,
            relevance_threshold=0.9,  # High threshold to force fallback
            use_fallback_strategies=True
        )

        # Mock components
        cotrag_advanced.api_provider = self.mock_api_provider
        
        # Mock vector store to return very low relevance documents
        mock_vector_store = MagicMock()
        mock_vector_store.search.return_value = [
            {
                "content": "Completely unrelated content.",
                "score": 0.1
            }
        ]
        cotrag_advanced.vector_store = mock_vector_store
        
        # Mock _apply_fallback_strategy to simulate pure CoT fallback
        original_fallback = cotrag_advanced._apply_fallback_strategy
        cotrag_advanced._apply_fallback_strategy = MagicMock()
        cotrag_advanced._apply_fallback_strategy.return_value = {
            "query": self.sample_queries["factual"],
            "documents": [],
            "reasoning": "AI là viết tắt của Artificial Intelligence, tức là Trí tuệ nhân tạo.",
            "answer": "AI là trí tuệ nhân tạo.",
            "model": "gpt-4o",
            "provider": "openai",
            "latency": 1.0,
            "fallback": {
                "type": "pure_cot"
            }
        }
        
        # Simulate an error during processing to trigger fallback
        cotrag_advanced.process = MagicMock(side_effect=Exception("Test error"))
        
        # Call the fallback method directly
        result = cotrag_advanced._apply_fallback_strategy(
            query=self.sample_queries["factual"],
            error="Test error"
        )
        
        # Verify that fallback was used
        self.assertIn("fallback", result)
        self.assertEqual(result["fallback"]["type"], "pure_cot")
        
        # Restore original method
        cotrag_advanced._apply_fallback_strategy = original_fallback


if __name__ == "__main__":
    unittest.main()
