"""
Unit tests for the PineconeRAG implementation.

This module contains tests for the PineconeRAG class.
"""

import unittest
from unittest.mock import MagicMock, patch
import pytest

# Try to import PineconeRAG and check if pinecone is available
try:
    from src.deep_research_core.reasoning.pinecone_rag import PineconeRAG
    from src.deep_research_core.retrieval.pinecone_vector_store import PINECONE_AVAILABLE
except ImportError:
    PINECONE_AVAILABLE = False


# Skip all tests if pinecone is not available
pinecone_not_available = pytest.mark.skipif(
    not PINECONE_AVAILABLE,
    reason="Pinecone library not installed. Install with: pip install pinecone-client"
)

@pinecone_not_available
class TestPineconeRAG(unittest.TestCase):
    """Test case for the PineconeRAG class."""

    @patch("src.deep_research_core.reasoning.pinecone_rag.PineconeVectorStore")
    @patch("src.deep_research_core.reasoning.pinecone_rag.openai_provider")
    @patch("src.deep_research_core.reasoning.pinecone_rag.anthropic_provider")
    @patch("src.deep_research_core.reasoning.pinecone_rag.openrouter_provider")
    def setUp(self, mock_openrouter, mock_anthropic, mock_openai, mock_vector_store_class):
        """Set up the test case."""
        # Create mock vector store
        self.mock_vector_store = MagicMock()
        mock_vector_store_class.return_value = self.mock_vector_store

        # Initialize PineconeRAG
        self.rag = PineconeRAG(
            provider="openai",
            model="gpt-4o",
            temperature=0.7,
            max_tokens=2000,
            embedding_model="text-embedding-ada-002",
            api_key="test_api_key",
            environment="test_environment",
            index_name="test_index",
            namespace="test_namespace",
            dimension=1536,
            metric="cosine",
            pod_type="p1",
            create_index=False,
            top_k=5
        )

        # Sample documents for testing
        self.sample_documents = [
            {
                "content": "This is a test document.",
                "source": "Test Source",
                "title": "Test Document"
            },
            {
                "content": "This is another test document.",
                "source": "Test Source 2",
                "title": "Test Document 2"
            }
        ]

        # Sample embeddings
        self.sample_embeddings = [
            [0.1] * 1536,
            [0.2] * 1536
        ]

        # Mock _get_embeddings method
        self.rag._get_embeddings = MagicMock(return_value=self.sample_embeddings)

        # Mock vector store methods
        self.mock_vector_store.add_documents.return_value = ["doc1", "doc2"]
        self.mock_vector_store.search.return_value = self.sample_documents
        self.mock_vector_store.update_documents.return_value = True
        self.mock_vector_store.delete_documents.return_value = True
        self.mock_vector_store.get_document.return_value = self.sample_documents[0]
        self.mock_vector_store.get_documents.return_value = self.sample_documents
        self.mock_vector_store.get_embedding.return_value = [0.1] * 1536
        self.mock_vector_store.get_embeddings.return_value = [[0.1] * 1536, [0.2] * 1536]
        self.mock_vector_store.create_index.return_value = True
        self.mock_vector_store.optimize.return_value = True
        self.mock_vector_store.clear.return_value = True
        self.mock_vector_store.count.return_value = 2

        # Mock API provider
        self.mock_api_provider = MagicMock()
        self.mock_api_provider.complete.return_value = "This is a test answer."
        self.rag.api_provider = self.mock_api_provider

    def test_initialization(self):
        """Test initialization of PineconeRAG."""
        self.assertEqual(self.rag.provider, "openai")
        self.assertEqual(self.rag.model, "gpt-4o")
        self.assertEqual(self.rag.temperature, 0.7)
        self.assertEqual(self.rag.max_tokens, 2000)
        self.assertEqual(self.rag.embedding_model, "text-embedding-ada-002")
        self.assertEqual(self.rag.top_k, 5)
        self.assertEqual(self.rag.api_key, "test_api_key")
        self.assertEqual(self.rag.environment, "test_environment")
        self.assertEqual(self.rag.index_name, "test_index")
        self.assertEqual(self.rag.namespace, "test_namespace")
        self.assertEqual(self.rag.dimension, 1536)
        self.assertEqual(self.rag.metric, "cosine")
        self.assertEqual(self.rag.pod_type, "p1")
        self.assertFalse(self.rag.create_index)

    def test_add_documents(self):
        """Test adding documents."""
        result = self.rag.add_documents(self.sample_documents)

        # Check that _get_embeddings was called with the correct arguments
        self.rag._get_embeddings.assert_called_once_with(
            ["This is a test document.", "This is another test document."]
        )

        # Check that vector_store.add_documents was called with the correct arguments
        self.mock_vector_store.add_documents.assert_called_once_with(
            self.sample_documents, self.sample_embeddings
        )

        # Check the result
        self.assertEqual(result, ["doc1", "doc2"])

    def test_search(self):
        """Test searching for documents."""
        result = self.rag.search("Test query")

        # Check that _get_embeddings was called with the correct arguments
        self.rag._get_embeddings.assert_called_once_with(["Test query"])

        # Check that vector_store.search was called with the correct arguments
        self.mock_vector_store.search.assert_called_once_with(
            query_embedding=self.sample_embeddings[0],
            top_k=5,
            filter_expr=None
        )

        # Check the result
        self.assertEqual(result, self.sample_documents)

    def test_search_with_filter(self):
        """Test searching for documents with a filter."""
        filter_expr = {"source": "Test Source"}
        result = self.rag.search("Test query", filter_expr=filter_expr)

        # Check that vector_store.search was called with the correct arguments
        self.mock_vector_store.search.assert_called_once_with(
            query_embedding=self.sample_embeddings[0],
            top_k=5,
            filter_expr=filter_expr
        )

        # Check the result
        self.assertEqual(result, self.sample_documents)

    def test_process(self):
        """Test processing a query."""
        result = self.rag.process("Test query")

        # Check that search was called
        self.rag._get_embeddings.assert_called_once_with(["Test query"])
        self.mock_vector_store.search.assert_called_once()

        # Check that the API provider was called
        self.mock_api_provider.complete.assert_called_once()

        # Check the result
        self.assertEqual(result["query"], "Test query")
        self.assertEqual(result["answer"], "This is a test answer.")
        self.assertEqual(result["documents"], self.sample_documents)
        self.assertEqual(result["model"], "gpt-4o")
        self.assertEqual(result["provider"], "openai")

    def test_process_with_callback(self):
        """Test processing a query with a callback."""
        callback = MagicMock()
        result = self.rag.process("Test query", callback=callback)

        # Check that the API provider was called with the callback
        self.mock_api_provider.complete.assert_called_once()
        args, kwargs = self.mock_api_provider.complete.call_args
        self.assertTrue(kwargs["stream"])
        self.assertEqual(kwargs["callback"], callback)

    def test_process_with_filter(self):
        """Test processing a query with a filter."""
        filter_expr = {"source": "Test Source"}
        result = self.rag.process("Test query", filter_expr=filter_expr)

        # Check that search was called with the filter
        self.mock_vector_store.search.assert_called_once_with(
            query_embedding=self.sample_embeddings[0],
            top_k=5,
            filter_expr=filter_expr
        )

    def test_error_handling_add_documents(self):
        """Test error handling when adding documents."""
        # Make the vector store raise an exception
        self.mock_vector_store.add_documents.side_effect = Exception("Test error")

        # Check that the exception is propagated
        with self.assertRaises(Exception):
            self.rag.add_documents(self.sample_documents)

    def test_error_handling_search(self):
        """Test error handling when searching."""
        # Make the vector store raise an exception
        self.mock_vector_store.search.side_effect = Exception("Test error")

        # Check that the exception is propagated
        with self.assertRaises(Exception):
            self.rag.search("Test query")

    def test_error_handling_process(self):
        """Test error handling when processing a query."""
        # Make the vector store raise an exception
        self.mock_vector_store.search.side_effect = Exception("Test error")

        # Check that the exception is propagated
        with self.assertRaises(Exception):
            self.rag.process("Test query")

    def test_update_documents(self):
        """Test updating documents."""
        result = self.rag.update_documents(["doc1"], [self.sample_documents[0]])

        # Check that _get_embeddings was called with the correct arguments
        self.rag._get_embeddings.assert_called_with(["This is a test document."])

        # Check that vector_store.delete_documents was called with the correct arguments
        self.mock_vector_store.delete_documents.assert_called_once_with(["doc1"])

        # Check that vector_store.add_documents was called
        self.mock_vector_store.add_documents.assert_called_with(
            [{'content': 'This is a test document.', 'source': 'Test Source', 'title': 'Test Document', 'id': 'doc1'}],
            self.sample_embeddings
        )

        # Check the result
        self.assertTrue(result)

    def test_delete_documents(self):
        """Test deleting documents."""
        result = self.rag.delete_documents(["doc1"])

        # Check that vector_store.delete_documents was called with the correct arguments
        self.mock_vector_store.delete_documents.assert_called_once_with(["doc1"])

        # Check the result
        self.assertTrue(result)

    def test_get_document(self):
        """Test getting a document by ID."""
        result = self.rag.get_document("doc1")

        # Check that vector_store.get_document was called with the correct arguments
        self.mock_vector_store.get_document.assert_called_once_with("doc1")

        # Check the result
        self.assertEqual(result, self.sample_documents[0])

    def test_get_documents(self):
        """Test getting multiple documents by ID."""
        result = self.rag.get_documents(["doc1", "doc2"])

        # Check that vector_store.get_documents was called with the correct arguments
        self.mock_vector_store.get_documents.assert_called_once_with(["doc1", "doc2"])

        # Check the result
        self.assertEqual(result, self.sample_documents)

    def test_get_embedding(self):
        """Test getting an embedding for a document."""
        result = self.rag.get_embedding("doc1")

        # Check that vector_store.get_embedding was called with the correct arguments
        self.mock_vector_store.get_embedding.assert_called_once_with("doc1")

        # Check the result
        self.assertEqual(result, [0.1] * 1536)

    def test_get_embeddings(self):
        """Test getting embeddings for multiple documents."""
        result = self.rag.get_embeddings(["doc1", "doc2"])

        # Check that vector_store.get_embeddings was called with the correct arguments
        self.mock_vector_store.get_embeddings.assert_called_once_with(["doc1", "doc2"])

        # Check the result
        self.assertEqual(result, [[0.1] * 1536, [0.2] * 1536])

    def test_create_index(self):
        """Test creating a new index."""
        index_params = {"dimension": 768, "metric": "dotproduct"}
        result = self.rag.create_index(index_params)

        # Check that vector_store.create_index was called with the correct arguments
        self.mock_vector_store.create_index.assert_called_once_with(index_params)

        # Check the result
        self.assertTrue(result)

    def test_optimize(self):
        """Test optimizing the index."""
        result = self.rag.optimize()

        # Check that vector_store.optimize was called
        self.mock_vector_store.optimize.assert_called_once()

        # Check the result
        self.assertTrue(result)

    def test_backup(self):
        """Test backing up the index."""
        result = self.rag.backup("backup_path")

        # Check the result (should be False as Pinecone doesn't support direct backups)
        self.assertFalse(result)

    def test_restore(self):
        """Test restoring the index from a backup."""
        result = self.rag.restore("backup_path")

        # Check the result (should be False as Pinecone doesn't support direct restores)
        self.assertFalse(result)

    def test_clear(self):
        """Test clearing the index."""
        result = self.rag.clear()

        # Check that vector_store.clear was called
        self.mock_vector_store.clear.assert_called_once()

        # Check the result
        self.assertTrue(result)

    def test_count(self):
        """Test counting documents in the index."""
        result = self.rag.count()

        # Check that vector_store.count was called
        self.mock_vector_store.count.assert_called_once()

        # Check the result
        self.assertEqual(result, 2)


if __name__ == "__main__":
    unittest.main()
