"""
Tests for HyDE (Hypothetical Document Embedding) Retriever.
"""

import os
import json
import unittest
from unittest.mock import Mock, patch, MagicMock

import pytest
import numpy as np

from deep_research_core.retrieval.hyde_retriever import HyDERetriever


class TestHyDERetriever(unittest.TestCase):
    """Test cases for HyDERetriever."""

    def setUp(self):
        """Set up test fixtures."""
        # Mock language model
        self.language_model = Mock()
        self.language_model.generate.return_value = "This is a hypothetical document that answers the query about AI."
        
        # Mock vector store
        self.vector_store = Mock()
        self.vector_store.search_by_text.return_value = [
            {
                "id": "doc1",
                "content": "AI is a technology that simulates human intelligence.",
                "metadata": {},
                "score": 0.95
            },
            {
                "id": "doc2",
                "content": "Machine learning is a subset of AI.",
                "metadata": {},
                "score": 0.85
            }
        ]
        
        # Create HyDERetriever instance
        self.retriever = HyDERetriever(
            language_model=self.language_model,
            vector_store=self.vector_store,
            cache_dir=None
        )

    def test_initialization(self):
        """Test initialization of HyDERetriever."""
        # Check that attributes are set correctly
        self.assertEqual(self.retriever.language_model, self.language_model)
        self.assertEqual(self.retriever.vector_store, self.vector_store)
        self.assertEqual(self.retriever.max_tokens, 300)
        self.assertEqual(self.retriever.temperature, 0.1)
        self.assertIsNotNone(self.retriever.hyde_prompt_template)
        
    def test_get_default_prompt_template(self):
        """Test default prompt template."""
        template = self.retriever._get_default_prompt_template()
        self.assertIn("{query}", template)
        
    def test_create_hyde_prompt(self):
        """Test creation of HyDE prompt."""
        query = "What is artificial intelligence?"
        prompt = self.retriever._create_hyde_prompt(query)
        self.assertIn(query, prompt)
        
    def test_generate_hypothetical_document(self):
        """Test generation of hypothetical document."""
        query = "What is artificial intelligence?"
        document = self.retriever._generate_hypothetical_document(query)
        
        # Check that language model was called
        self.language_model.generate.assert_called_once()
        self.assertEqual(document, "This is a hypothetical document that answers the query about AI.")
        
        # Check caching
        self.retriever._generate_hypothetical_document(query)
        # Language model should not be called again
        self.assertEqual(self.language_model.generate.call_count, 1)
        
    def test_retrieve(self):
        """Test document retrieval."""
        query = "What is artificial intelligence?"
        documents = self.retriever.retrieve(query, top_k=2)
        
        # Check that vector store was called with hypothetical document
        self.vector_store.search_by_text.assert_called_once()
        self.assertEqual(len(documents), 2)
        
        # Check that retrieval method metadata was added
        for doc in documents:
            self.assertEqual(doc["metadata"]["retrieval_method"], "hyde")
            
    def test_retrieve_with_original_query(self):
        """Test retrieval with both hypothetical document and original query."""
        query = "What is artificial intelligence?"
        
        # Configure vector store to return different documents for different queries
        self.vector_store.search_by_text.side_effect = lambda text, **kwargs: [
            {
                "id": "doc1" if "hypothetical" in text else "doc3",
                "content": "Content for " + text[:20],
                "metadata": {},
                "score": 0.9
            },
            {
                "id": "doc2" if "hypothetical" in text else "doc4",
                "content": "More content for " + text[:20],
                "metadata": {},
                "score": 0.8
            }
        ]
        
        documents = self.retriever.retrieve_with_original_query(
            query,
            hypothetical_weight=0.7,
            original_weight=0.3,
            top_k=2
        )
        
        # Check that vector store was called twice
        self.assertEqual(self.vector_store.search_by_text.call_count, 2)
        
        # Check the number of returned documents
        self.assertEqual(len(documents), 2)
        
    def test_combine_results(self):
        """Test combining results from different retrieval methods."""
        hyde_docs = [
            {"id": "doc1", "content": "Content 1", "score": 0.9},
            {"id": "doc2", "content": "Content 2", "score": 0.8}
        ]
        
        original_docs = [
            {"id": "doc1", "content": "Content 1", "score": 0.7},  # Same doc as in hyde_docs
            {"id": "doc3", "content": "Content 3", "score": 0.6}   # Different doc
        ]
        
        combined = self.retriever._combine_results(
            hyde_docs,
            original_docs,
            hyde_weight=0.7,
            original_weight=0.3
        )
        
        # Should have 3 docs (doc1, doc2, doc3)
        self.assertEqual(len(combined), 3)
        
        # doc1 should be first with combined score
        self.assertEqual(combined[0]["id"], "doc1")
        
    def test_get_relevant_documents(self):
        """Test the abstract method implementation."""
        query = "What is artificial intelligence?"
        
        # Spy on the retrieve method
        with patch.object(self.retriever, 'retrieve') as mock_retrieve:
            mock_retrieve.return_value = [{"id": "doc1", "content": "Test content"}]
            
            # Call the abstract method
            documents = self.retriever.get_relevant_documents(query, top_k=3)
            
            # Verify retrieve was called with the right parameters
            mock_retrieve.assert_called_once_with(query, top_k=3)
            self.assertEqual(len(documents), 1)
        
    @patch("os.makedirs")
    @patch("builtins.open", new_callable=unittest.mock.mock_open)
    def test_cache_functionality(self, mock_open, mock_makedirs):
        """Test caching functionality."""
        # Create retriever with cache
        cached_retriever = HyDERetriever(
            language_model=self.language_model,
            vector_store=self.vector_store,
            cache_dir="/tmp/hyde_cache"
        )
        
        # Check that cache directory was created
        mock_makedirs.assert_called_once_with("/tmp/hyde_cache", exist_ok=True)
        
        # Generate document
        query = "What is artificial intelligence?"
        document = cached_retriever._generate_hypothetical_document(query)
        
        # Check that document was saved to cache
        mock_open.assert_called_once()
        handle = mock_open()
        handle.write.assert_called_once_with(document)


if __name__ == "__main__":
    unittest.main() 