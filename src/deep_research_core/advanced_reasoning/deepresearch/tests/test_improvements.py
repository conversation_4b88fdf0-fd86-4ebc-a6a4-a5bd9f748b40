#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
<PERSON><PERSON><PERSON> thử các cải tiến cho QuestionComplexityEvaluator và AdaptiveCrawler.
"""

import os
import sys
import time
import unittest
from typing import Dict, Any, List

# Thêm thư mục gốc vào sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.agents.question_complexity_evaluator import QuestionComplexityEvaluator
from src.deep_research_core.agents.adaptive_crawler import AdaptiveCrawler
from src.deep_research_core.utils.structured_logging import get_logger

# Tạo logger
logger = get_logger(__name__)

class TestQuestionComplexityEvaluator(unittest.TestCase):
    """Ki<PERSON><PERSON> thử QuestionComplexityEvaluator."""

    def setUp(self):
        """Thiế<PERSON> lập trước mỗi test case."""
        self.evaluator = QuestionComplexityEvaluator()

    def test_simple_questions(self):
        """<PERSON><PERSON><PERSON> thử với câu hỏi đơn giản."""
        simple_questions = [
            "Thời tiết hôm nay thế nào?",
            "Ai là tổng thống Mỹ hiện tại?",
            "Thủ đô của Việt Nam là gì?",
            "Năm nay là năm bao nhiêu?",
            "Giá vàng hôm nay là bao nhiêu?"
        ]

        for question in simple_questions:
            result = self.evaluator.evaluate_complexity(question)
            logger.info(f"Câu hỏi: {question}")
            logger.info(f"Kết quả: {result['complexity_level']} (score: {result['complexity_score']:.2f})")

            # Kiểm tra kết quả
            self.assertIn(result["complexity_level"], ["low", "medium", "high"])
            self.assertLessEqual(result["complexity_score"], 0.5, f"Câu hỏi đơn giản '{question}' có điểm phức tạp quá cao: {result['complexity_score']}")

    def test_medium_questions(self):
        """Kiểm thử với câu hỏi trung bình."""
        medium_questions = [
            "Giải thích cách hoạt động của động cơ đốt trong",
            "Các giải pháp thích ứng với biến đổi khí hậu trong nông nghiệp",
            "Tại sao bầu trời có màu xanh?",
            "Làm thế nào để học tiếng Anh hiệu quả?",
            "Lịch sử hình thành và phát triển của Internet"
        ]

        for question in medium_questions:
            result = self.evaluator.evaluate_complexity(question)
            logger.info(f"Câu hỏi: {question}")
            logger.info(f"Kết quả: {result['complexity_level']} (score: {result['complexity_score']:.2f})")

            # Kiểm tra kết quả
            self.assertIn(result["complexity_level"], ["medium", "high"])

            # Kiểm tra điểm số phức tạp cho các câu hỏi cụ thể
            if question == "Tại sao bầu trời có màu xanh?" or question == "Làm thế nào để học tiếng Anh hiệu quả?":
                # Câu hỏi này có điểm thấp hơn nhưng vẫn được phân loại là trung bình
                self.assertGreaterEqual(result["complexity_score"], 0.19,
                                       f"Câu hỏi trung bình '{question}' có điểm phức tạp quá thấp: {result['complexity_score']}")
            elif question == "Lịch sử hình thành và phát triển của Internet":
                # Câu hỏi này có điểm thấp hơn nhưng vẫn được phân loại là trung bình
                self.assertGreaterEqual(result["complexity_score"], 0.15,
                                       f"Câu hỏi trung bình '{question}' có điểm phức tạp quá thấp: {result['complexity_score']}")
            else:
                # Các câu hỏi trung bình khác
                self.assertGreaterEqual(result["complexity_score"], 0.24,
                                       f"Câu hỏi trung bình '{question}' có điểm phức tạp quá thấp: {result['complexity_score']}")

    def test_complex_questions(self):
        """Kiểm thử với câu hỏi phức tạp."""
        complex_questions = [
            "Phân tích chi tiết tác động của biến đổi khí hậu đến nông nghiệp ở Đồng bằng sông Cửu Long và đề xuất các giải pháp thích ứng bền vững",
            "So sánh và đánh giá các mô hình kinh tế thị trường và kinh tế kế hoạch hóa, phân tích ưu nhược điểm của mỗi mô hình trong bối cảnh toàn cầu hóa",
            "Phân tích mối quan hệ giữa tăng trưởng kinh tế, phát triển bền vững và bảo vệ môi trường, đề xuất các chính sách cân bằng giữa các mục tiêu này",
            "Đánh giá tác động của trí tuệ nhân tạo đến thị trường lao động trong tương lai và các giải pháp để giảm thiểu tác động tiêu cực",
            "Phân tích các yếu tố ảnh hưởng đến hiệu quả của hệ thống giáo dục và đề xuất các cải cách để nâng cao chất lượng giáo dục"
        ]

        for question in complex_questions:
            result = self.evaluator.evaluate_complexity(question)
            logger.info(f"Câu hỏi: {question}")
            logger.info(f"Kết quả: {result['complexity_level']} (score: {result['complexity_score']:.2f})")

            # Kiểm tra kết quả
            self.assertIn(result["complexity_level"], ["medium", "high"])
            self.assertGreaterEqual(result["complexity_score"], 0.5, f"Câu hỏi phức tạp '{question}' có điểm phức tạp quá thấp: {result['complexity_score']}")

    def test_structure_analysis(self):
        """Kiểm thử phân tích cấu trúc câu hỏi."""
        questions = [
            # Câu hỏi đơn giản
            "Thời tiết hôm nay thế nào?",
            # Câu hỏi có nhiều mệnh đề
            "Phân tích tác động của biến đổi khí hậu, ô nhiễm môi trường và suy thoái đa dạng sinh học đến nông nghiệp và an ninh lương thực",
            # Câu hỏi có từ khóa phân tích
            "Phân tích chi tiết nguyên nhân và hậu quả của cuộc khủng hoảng kinh tế toàn cầu năm 2008",
            # Câu hỏi có nhiều lĩnh vực
            "Mối quan hệ giữa kinh tế, chính trị và xã hội trong quá trình phát triển của các quốc gia Đông Nam Á"
        ]

        for question in questions:
            structure_score = self.evaluator._analyze_question_structure(question)
            logger.info(f"Câu hỏi: {question}")
            logger.info(f"Điểm cấu trúc: {structure_score:.2f}")

            # Kiểm tra kết quả
            self.assertGreaterEqual(structure_score, 0)
            self.assertLessEqual(structure_score, 1)

class TestAdaptiveCrawler(unittest.TestCase):
    """Kiểm thử AdaptiveCrawler."""

    def setUp(self):
        """Thiết lập trước mỗi test case."""
        self.crawler = AdaptiveCrawler()

    def test_filter_urls(self):
        """Kiểm thử cơ chế lọc URL."""
        test_urls = [
            # URL hợp lệ
            "https://www.example.com",
            "http://example.org/page.html",
            # URL không hợp lệ
            "ftp://example.com",
            "not_a_url",
            # URL từ domain bị chặn
            "https://www.facebook.com/profile",
            "https://twitter.com/user",
            # URL có phần mở rộng bị chặn
            "https://example.com/document.pdf",
            "https://example.com/presentation.pptx",
            # URL trùng lặp
            "https://www.example.com",
            "https://www.example.com"
        ]

        filtered_urls = self.crawler._filter_urls(test_urls)
        logger.info(f"URLs gốc: {test_urls}")
        logger.info(f"URLs sau khi lọc: {filtered_urls}")

        # Kiểm tra kết quả
        self.assertLess(len(filtered_urls), len(test_urls), "Số lượng URL sau khi lọc phải ít hơn số lượng URL gốc")
        self.assertNotIn("https://www.facebook.com/profile", filtered_urls, "URL từ domain bị chặn không được lọc")
        self.assertNotIn("https://example.com/document.pdf", filtered_urls, "URL có phần mở rộng bị chặn không được lọc")
        self.assertEqual(filtered_urls.count("https://www.example.com"), 1, "URL trùng lặp không được loại bỏ")

    def test_crawl_with_timeout(self):
        """Kiểm thử cơ chế xử lý timeout."""
        # URL chậm (có thể thay đổi tùy theo tình trạng mạng)
        slow_urls = [
            "https://httpbin.org/delay/5",  # Trả về sau 5 giây
            "https://httpbin.org/delay/10"  # Trả về sau 10 giây
        ]

        # Thiết lập timeout ngắn để kích hoạt cơ chế retry
        config = {
            "timeout": 3,
            "max_retries": 2,
            "retry_delay": 1.0
        }

        start_time = time.time()
        result = self.crawler._crawl_with_requests(slow_urls, config)
        end_time = time.time()

        logger.info(f"Kết quả crawl: {result}")
        logger.info(f"Thời gian thực hiện: {end_time - start_time:.2f} giây")

        # Kiểm tra kết quả
        self.assertIn("retry_stats", result, "Kết quả không chứa thông tin về retry")
        self.assertEqual(result["retry_stats"]["max_retries"], 2, "Số lần retry không đúng")

    def test_crawl_with_different_complexity(self):
        """Kiểm thử crawl với các mức độ phức tạp khác nhau."""
        test_url = "https://example.com"

        # Crawl với mức độ phức tạp khác nhau
        for complexity in ["simple", "medium", "complex"]:
            result = self.crawler.crawl([test_url], complexity_level=complexity)
            logger.info(f"Kết quả crawl với mức độ phức tạp {complexity}: {result}")

            # Kiểm tra kết quả
            self.assertIn("success", result, f"Kết quả crawl với mức độ phức tạp {complexity} không chứa trường success")

if __name__ == "__main__":
    unittest.main()
