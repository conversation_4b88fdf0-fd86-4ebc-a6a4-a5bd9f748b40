"""
Tests for the QueryMemorySystem class.
"""

import unittest
import os
import json
import time
import tempfile
from unittest.mock import patch, MagicMock

from deepresearch.src.deep_research_core.utils.query_memory_system import QueryMemorySystem

class TestQueryMemorySystem(unittest.TestCase):
    """Test cases for QueryMemorySystem."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Tạo file tạm thời cho memory file
        self.temp_dir = tempfile.TemporaryDirectory()
        self.memory_file = os.path.join(self.temp_dir.name, "memory.json")
        
        # Khởi tạo QueryMemorySystem
        self.memory_system = QueryMemorySystem(
            memory_file=self.memory_file,
            max_history=10,
            similarity_threshold=0.7,
            auto_save=True,
            save_interval=1  # 1 giây để dễ test
        )
    
    def tearDown(self):
        """Tear down test fixtures."""
        # Xóa file tạm thời
        self.temp_dir.cleanup()
    
    def test_record_search_result(self):
        """Test recording search results."""
        # Tạ<PERSON> kết quả tìm kiếm mẫu
        search_result = {
            "success": True,
            "results": [
                {"title": "Result 1", "url": "https://example.com/1"},
                {"title": "Result 2", "url": "https://example.com/2"}
            ],
            "search_method": "searxng",
            "execution_time": 0.5
        }
        
        # Ghi nhận kết quả tìm kiếm
        self.memory_system.record_search_result("python programming", search_result)
        
        # Kiểm tra lịch sử
        self.assertEqual(len(self.memory_system.history), 1)
        entry = self.memory_system.history[0]
        self.assertEqual(entry["query"], "python programming")
        self.assertEqual(entry["normalized_query"], "python programming")
        self.assertEqual(entry["success"], True)
        self.assertEqual(entry["num_results"], 2)
        self.assertEqual(entry["search_method"], "searxng")
        self.assertEqual(entry["execution_time"], 0.5)
        self.assertIn("timestamp", entry)
        
        # Kiểm tra results_summary
        self.assertIn("results_summary", entry)
        self.assertEqual(len(entry["results_summary"]), 2)
        self.assertEqual(entry["results_summary"][0]["title"], "Result 1")
        self.assertEqual(entry["results_summary"][0]["url"], "https://example.com/1")
    
    def test_find_similar_queries(self):
        """Test finding similar queries."""
        # Thêm một số mục vào lịch sử
        search_result = {"success": True, "results": [], "search_method": "searxng", "execution_time": 0.5}
        self.memory_system.record_search_result("python programming", search_result)
        self.memory_system.record_search_result("python tutorial", search_result)
        self.memory_system.record_search_result("java programming", search_result)
        
        # Tìm các truy vấn tương tự
        similar_queries = self.memory_system.find_similar_queries("python programming language")
        
        # Kiểm tra kết quả
        self.assertEqual(len(similar_queries), 2)  # python programming và python tutorial
        self.assertEqual(similar_queries[0]["query"], "python programming")
        self.assertGreater(similar_queries[0]["similarity"], 0.5)
    
    def test_get_successful_methods(self):
        """Test getting successful search methods."""
        # Thêm một số mục vào lịch sử
        success_result = {"success": True, "results": [], "search_method": "searxng", "execution_time": 0.5}
        failure_result = {"success": False, "results": [], "search_method": "crawlee", "execution_time": 1.0}
        
        self.memory_system.record_search_result("python programming", success_result)
        self.memory_system.record_search_result("python tutorial", success_result)
        self.memory_system.record_search_result("python course", failure_result)
        
        # Lấy các phương thức thành công
        methods = self.memory_system.get_successful_methods("python programming")
        
        # Kiểm tra kết quả
        self.assertEqual(methods, {"searxng": 2})
    
    def test_get_average_execution_time(self):
        """Test getting average execution time."""
        # Thêm một số mục vào lịch sử
        result1 = {"success": True, "results": [], "search_method": "searxng", "execution_time": 0.5}
        result2 = {"success": True, "results": [], "search_method": "searxng", "execution_time": 1.5}
        
        self.memory_system.record_search_result("python programming", result1)
        self.memory_system.record_search_result("python tutorial", result2)
        
        # Lấy thời gian thực hiện trung bình
        avg_times = self.memory_system.get_average_execution_time("python programming")
        
        # Kiểm tra kết quả
        self.assertEqual(avg_times, {"searxng": 1.0})  # (0.5 + 1.5) / 2 = 1.0
    
    def test_get_search_trends(self):
        """Test getting search trends."""
        # Thêm một số mục vào lịch sử
        result = {"success": True, "results": [], "search_method": "searxng", "execution_time": 0.5}
        
        self.memory_system.record_search_result("python programming", result)
        self.memory_system.record_search_result("python programming", result)
        self.memory_system.record_search_result("python tutorial", result)
        
        # Lấy xu hướng tìm kiếm
        trends = self.memory_system.get_search_trends()
        
        # Kiểm tra kết quả
        self.assertEqual(trends["query_count"], 3)
        self.assertEqual(trends["method_counts"], {"searxng": 3})
        self.assertEqual(trends["success_rate"], 1.0)
        self.assertEqual(trends["avg_execution_time"], 0.5)
        
        # Kiểm tra top_queries
        top_queries = trends["top_queries"]
        self.assertEqual(len(top_queries), 2)
        self.assertEqual(top_queries[0][0], "python programming")
        self.assertEqual(top_queries[0][1], 2)
    
    def test_normalize_query(self):
        """Test query normalization."""
        # Kiểm tra chuẩn hóa truy vấn
        normalized = self.memory_system._normalize_query("Python Programming!")
        self.assertEqual(normalized, "python programming")
        
        normalized = self.memory_system._normalize_query("  Python  Programming  ")
        self.assertEqual(normalized, "python programming")
        
        normalized = self.memory_system._normalize_query("Python, Programming.")
        self.assertEqual(normalized, "python programming")
    
    def test_calculate_similarity(self):
        """Test similarity calculation."""
        # Kiểm tra tính toán độ tương đồng
        similarity = self.memory_system._calculate_similarity("python programming", "python tutorial")
        self.assertGreater(similarity, 0.3)
        
        similarity = self.memory_system._calculate_similarity("python programming", "java programming")
        self.assertGreater(similarity, 0.3)
        
        similarity = self.memory_system._calculate_similarity("python programming", "javascript tutorial")
        self.assertLess(similarity, 0.3)
    
    def test_save_and_load_history(self):
        """Test saving and loading history."""
        # Thêm một số mục vào lịch sử
        result = {"success": True, "results": [], "search_method": "searxng", "execution_time": 0.5}
        self.memory_system.record_search_result("python programming", result)
        
        # Đợi để auto_save được kích hoạt
        time.sleep(1.5)
        
        # Kiểm tra file đã được tạo
        self.assertTrue(os.path.exists(self.memory_file))
        
        # Tạo một QueryMemorySystem mới để tải lịch sử
        new_memory_system = QueryMemorySystem(
            memory_file=self.memory_file,
            max_history=10
        )
        
        # Kiểm tra lịch sử đã được tải
        self.assertEqual(len(new_memory_system.history), 1)
        self.assertEqual(new_memory_system.history[0]["query"], "python programming")
    
    def test_max_history(self):
        """Test max history limit."""
        # Thêm nhiều mục vào lịch sử
        result = {"success": True, "results": [], "search_method": "searxng", "execution_time": 0.5}
        
        for i in range(15):  # Thêm 15 mục, nhưng max_history = 10
            self.memory_system.record_search_result(f"query {i}", result)
        
        # Kiểm tra lịch sử chỉ giữ 10 mục mới nhất
        self.assertEqual(len(self.memory_system.history), 10)
        self.assertEqual(self.memory_system.history[0]["query"], "query 5")
        self.assertEqual(self.memory_system.history[9]["query"], "query 14")
    
    def test_clear_history(self):
        """Test clearing history."""
        # Thêm một số mục vào lịch sử
        result = {"success": True, "results": [], "search_method": "searxng", "execution_time": 0.5}
        self.memory_system.record_search_result("python programming", result)
        
        # Xóa lịch sử
        self.memory_system.clear_history()
        
        # Kiểm tra lịch sử đã được xóa
        self.assertEqual(len(self.memory_system.history), 0)
    
    def test_get_statistics(self):
        """Test getting statistics."""
        # Thêm một số mục vào lịch sử
        success_result = {"success": True, "results": [{}], "search_method": "searxng", "execution_time": 0.5}
        failure_result = {"success": False, "results": [], "search_method": "crawlee", "execution_time": 1.0}
        
        self.memory_system.record_search_result("python programming", success_result)
        self.memory_system.record_search_result("python tutorial", success_result)
        self.memory_system.record_search_result("python course", failure_result)
        
        # Lấy thống kê
        stats = self.memory_system.get_statistics()
        
        # Kiểm tra kết quả
        self.assertEqual(stats["total_queries"], 3)
        self.assertEqual(stats["success_rate"], 2/3)
        self.assertEqual(stats["avg_execution_time"], (0.5 + 0.5 + 1.0) / 3)
        self.assertEqual(stats["avg_results_per_query"], (1 + 1 + 0) / 3)
        self.assertEqual(stats["method_counts"], {"searxng": 2, "crawlee": 1})

if __name__ == "__main__":
    unittest.main()
