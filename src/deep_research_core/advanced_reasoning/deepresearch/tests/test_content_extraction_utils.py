"""
Test module for content_extraction_utils.py
"""

import unittest
import sys
import os
from unittest.mock import patch, MagicMock

# Add the src directory to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from src.deep_research_core.utils.content_extraction_utils import (
    extract_main_content,
    find_content_by_density,
    clean_soup,
    extract_images,
    extract_links,
    extract_tables,
    extract_metadata,
    summarize_content
)
from bs4 import BeautifulSoup


class TestContentExtractionUtils(unittest.TestCase):
    """Test cases for content_extraction_utils.py"""

    def setUp(self):
        """Set up test fixtures"""
        self.html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Test Page</title>
            <meta name="description" content="Test description">
            <meta property="og:title" content="Test Open Graph Title">
            <style>
                body { font-family: Arial; }
            </style>
            <script>
                console.log("Test script");
            </script>
        </head>
        <body>
            <header>
                <nav>
                    <ul>
                        <li><a href="/">Home</a></li>
                        <li><a href="/about">About</a></li>
                    </ul>
                </nav>
            </header>
            <main>
                <article>
                    <h1>Main Article Title</h1>
                    <p>This is the first paragraph of the main content.</p>
                    <p>This is the second paragraph with <a href="https://example.com">a link</a>.</p>
                    <img src="image.jpg" alt="Test Image" title="Image Title">
                    <table>
                        <caption>Test Table</caption>
                        <thead>
                            <tr>
                                <th>Header 1</th>
                                <th>Header 2</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Cell 1</td>
                                <td>Cell 2</td>
                            </tr>
                            <tr>
                                <td>Cell 3</td>
                                <td>Cell 4</td>
                            </tr>
                        </tbody>
                    </table>
                </article>
            </main>
            <footer>
                <p>Copyright 2023</p>
            </footer>
        </body>
        </html>
        """
        self.soup = BeautifulSoup(self.html_content, "html.parser")

    def test_extract_main_content(self):
        """Test extract_main_content function"""
        result = extract_main_content(self.html_content)

        # Check basic structure
        self.assertTrue(result["success"])
        self.assertIn("content", result)
        self.assertIn("title", result)
        self.assertIn("images", result)
        self.assertIn("links", result)
        self.assertIn("tables", result)
        self.assertIn("metadata", result)

        # Check content
        self.assertIn("Main Article Title", result["content"])
        self.assertIn("first paragraph", result["content"])
        self.assertIn("second paragraph", result["content"])

        # Check title
        self.assertEqual("Test Page", result["title"])

        # Check images
        self.assertEqual(1, len(result["images"]))
        self.assertEqual("image.jpg", result["images"][0]["src"])
        self.assertEqual("Test Image", result["images"][0]["alt"])

        # Check links
        self.assertTrue(any(link["url"] == "https://example.com" for link in result["links"]))

        # Check tables
        self.assertEqual(1, len(result["tables"]))
        self.assertEqual("Test Table", result["tables"][0]["caption"])

        # Check metadata - only check if keys exist since readability might not be available
        if "description" in result["metadata"]:
            self.assertEqual("Test description", result["metadata"]["description"])
        if "og:title" in result["metadata"]:
            self.assertEqual("Test Open Graph Title", result["metadata"]["og:title"])

    def test_find_content_by_density(self):
        """Test find_content_by_density function"""
        main_content = find_content_by_density(self.soup)

        # Check that we found some content
        self.assertIsNotNone(main_content)

        # Check that the content contains the main article
        content_text = main_content.get_text()
        self.assertIn("Main Article Title", content_text)
        self.assertIn("first paragraph", content_text)

    def test_clean_soup(self):
        """Test clean_soup function"""
        # Make a copy of the soup
        soup_copy = BeautifulSoup(str(self.soup), "html.parser")

        # Clean the soup
        clean_soup(soup_copy)

        # Check that script and style tags are removed
        self.assertEqual(0, len(soup_copy.find_all("script")))
        self.assertEqual(0, len(soup_copy.find_all("style")))

        # Check that nav, header, and footer tags are removed
        self.assertEqual(0, len(soup_copy.find_all("nav")))
        self.assertEqual(0, len(soup_copy.find_all("header")))
        self.assertEqual(0, len(soup_copy.find_all("footer")))

        # Check that main content is preserved
        self.assertIsNotNone(soup_copy.find("main"))
        self.assertIsNotNone(soup_copy.find("article"))
        self.assertIn("Main Article Title", soup_copy.get_text())

    def test_extract_images(self):
        """Test extract_images function"""
        images = extract_images(self.soup)

        # Check that we found the image
        self.assertEqual(1, len(images))
        self.assertEqual("image.jpg", images[0]["src"])
        self.assertEqual("Test Image", images[0]["alt"])
        self.assertEqual("Image Title", images[0]["title"])

    def test_extract_links(self):
        """Test extract_links function"""
        links = extract_links(self.soup)

        # Check that we found all links
        self.assertEqual(3, len(links))

        # Check that the links have the correct URLs
        urls = [link["url"] for link in links]
        self.assertIn("/", urls)
        self.assertIn("/about", urls)
        self.assertIn("https://example.com", urls)

        # Test with base_url
        links_with_base = extract_links(self.soup, "https://example.org")

        # Check that relative URLs are converted to absolute URLs
        urls = [link["url"] for link in links_with_base]
        self.assertIn("https://example.org/", urls)
        self.assertIn("https://example.org/about", urls)

    def test_extract_tables(self):
        """Test extract_tables function"""
        tables = extract_tables(self.soup)

        # Check that we found the table
        self.assertEqual(1, len(tables))

        # Check table caption
        self.assertEqual("Test Table", tables[0]["caption"])

        # Check table headers
        self.assertEqual(["Header 1", "Header 2"], tables[0]["headers"])

        # Check table rows
        self.assertEqual(3, len(tables[0]["rows"]))  # Including header row
        self.assertEqual(["Header 1", "Header 2"], tables[0]["rows"][0])
        self.assertEqual(["Cell 1", "Cell 2"], tables[0]["rows"][1])
        self.assertEqual(["Cell 3", "Cell 4"], tables[0]["rows"][2])

    def test_extract_metadata(self):
        """Test extract_metadata function"""
        metadata = extract_metadata(self.soup)

        # Check that we found the metadata
        self.assertEqual("Test description", metadata["description"])
        self.assertEqual("Test Open Graph Title", metadata["og:title"])

    def test_summarize_content(self):
        """Test summarize_content function"""
        # Create a long text with multiple sentences
        long_text = """
        This is the first sentence of the text. This is the second sentence with some important keywords.
        This is the third sentence that doesn't contain many important words.
        This is the fourth sentence with more important keywords and information.
        This is the fifth sentence that repeats some keywords from earlier.
        This is the sixth sentence with unique information not mentioned before.
        This is the seventh sentence that doesn't add much value.
        This is the eighth sentence with critical information about the topic.
        This is the ninth sentence that summarizes the main points.
        This is the tenth and final sentence that concludes the text.
        """

        # Summarize the text
        summary = summarize_content(long_text, max_sentences=3)

        # Check that the summary is shorter than the original text
        self.assertLess(len(summary), len(long_text))

        # Check that the summary contains important sentences
        self.assertIn("first sentence", summary)  # First sentences are usually important

        # Test with text shorter than max_sentences
        short_text = "This is a short text with only one sentence."
        short_summary = summarize_content(short_text, max_sentences=3)

        # Check that the summary is the same as the original text
        self.assertEqual(short_text, short_summary)


if __name__ == "__main__":
    unittest.main()
