"""
Unit tests for specialized content extractors.
"""

import unittest
import os
import sys
from unittest.mock import patch, MagicMock

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from src.deep_research_core.utils.specialized_extractors import (
    BaseExtractor,
    WikipediaExtractor,
    StackOverflowExtractor,
    GitHubExtractor,
    NewsExtractor,
    get_extractor_for_url,
)


class TestBaseExtractor(unittest.TestCase):
    """Test BaseExtractor class."""

    def setUp(self):
        """Set up test environment."""
        self.extractor = BaseExtractor()

    def test_can_handle(self):
        """Test can_handle method."""
        # BaseExtractor should not handle any URL
        self.assertFalse(self.extractor.can_handle("https://example.com"))

    def test_extract_title(self):
        """Test _extract_title method."""
        # Create a mock soup
        mock_soup = MagicMock()
        mock_soup.title.text = "Test Title"

        # Test title extraction
        title = self.extractor._extract_title(mock_soup)
        self.assertEqual(title, "Test Title")

    def test_extract_main_content(self):
        """Test _extract_main_content method."""
        # Create a mock soup
        mock_soup = MagicMock()
        mock_soup.select.return_value = []
        mock_soup.find_all.return_value = []
        mock_soup.get_text.return_value = "Test Content"

        # Test content extraction
        content = self.extractor._extract_main_content(mock_soup)
        self.assertEqual(content, "Test Content")


class TestWikipediaExtractor(unittest.TestCase):
    """Test WikipediaExtractor class."""

    def setUp(self):
        """Set up test environment."""
        self.extractor = WikipediaExtractor()

    def test_can_handle(self):
        """Test can_handle method."""
        # WikipediaExtractor should handle Wikipedia URLs
        self.assertTrue(self.extractor.can_handle("https://en.wikipedia.org/wiki/Python"))
        self.assertTrue(self.extractor.can_handle("https://vi.wikipedia.org/wiki/Python"))
        self.assertFalse(self.extractor.can_handle("https://example.com"))


class TestStackOverflowExtractor(unittest.TestCase):
    """Test StackOverflowExtractor class."""

    def setUp(self):
        """Set up test environment."""
        self.extractor = StackOverflowExtractor()

    def test_can_handle(self):
        """Test can_handle method."""
        # StackOverflowExtractor should handle Stack Overflow URLs
        self.assertTrue(
            self.extractor.can_handle(
                "https://stackoverflow.com/questions/1234/how-to-use-python"
            )
        )
        self.assertTrue(
            self.extractor.can_handle(
                "https://serverfault.stackexchange.com/questions/1234/how-to-configure-server"
            )
        )
        self.assertFalse(self.extractor.can_handle("https://example.com"))


class TestGitHubExtractor(unittest.TestCase):
    """Test GitHubExtractor class."""

    def setUp(self):
        """Set up test environment."""
        self.extractor = GitHubExtractor()

    def test_can_handle(self):
        """Test can_handle method."""
        # GitHubExtractor should handle GitHub URLs
        self.assertTrue(
            self.extractor.can_handle("https://github.com/username/repo")
        )
        self.assertTrue(
            self.extractor.can_handle("https://github.com/username/repo/issues/1")
        )
        self.assertFalse(self.extractor.can_handle("https://example.com"))


class TestNewsExtractor(unittest.TestCase):
    """Test NewsExtractor class."""

    def setUp(self):
        """Set up test environment."""
        self.extractor = NewsExtractor()

    def test_can_handle(self):
        """Test can_handle method."""
        # NewsExtractor should handle news URLs
        self.assertTrue(
            self.extractor.can_handle("https://www.cnn.com/2023/01/01/politics/article")
        )
        self.assertTrue(
            self.extractor.can_handle("https://www.bbc.com/news/world-123456")
        )
        self.assertFalse(self.extractor.can_handle("https://example.com"))

    def test_can_handle_with_html(self):
        """Test can_handle method with HTML."""
        # Create HTML with article metadata
        html = """
        <html>
        <head>
            <meta property="og:type" content="article">
        </head>
        <body>
            <article>
                <h1>Test Article</h1>
                <p>Test content</p>
            </article>
        </body>
        </html>
        """

        # NewsExtractor should handle URLs with article metadata
        self.assertTrue(
            self.extractor.can_handle("https://example.com/news/article", html)
        )


class TestGetExtractorForUrl(unittest.TestCase):
    """Test get_extractor_for_url function."""

    def test_get_extractor_for_url(self):
        """Test get_extractor_for_url function."""
        # Test Wikipedia URL
        extractor = get_extractor_for_url("https://en.wikipedia.org/wiki/Python")
        self.assertIsInstance(extractor, WikipediaExtractor)

        # Test Stack Overflow URL
        extractor = get_extractor_for_url(
            "https://stackoverflow.com/questions/1234/how-to-use-python"
        )
        self.assertIsInstance(extractor, StackOverflowExtractor)

        # Test GitHub URL
        extractor = get_extractor_for_url("https://github.com/username/repo")
        self.assertIsInstance(extractor, GitHubExtractor)

        # Test news URL
        extractor = get_extractor_for_url("https://www.cnn.com/2023/01/01/politics/article")
        self.assertIsInstance(extractor, NewsExtractor)

        # Test unknown URL
        extractor = get_extractor_for_url("https://example.com")
        self.assertIsInstance(extractor, BaseExtractor)


if __name__ == "__main__":
    unittest.main()
