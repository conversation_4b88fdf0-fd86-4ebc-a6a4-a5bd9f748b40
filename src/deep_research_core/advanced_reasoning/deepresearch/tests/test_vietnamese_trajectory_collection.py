"""
Tests for Vietnamese trajectory collection.
"""

import unittest
import os
import tempfile
import shutil
from unittest.mock import MagicMock, patch

from deep_research_core.rl_tuning.trajectories.trajectory_collector import TrajectoryCollector
from deep_research_core.rl_tuning.environment import AgentEnvironment


class TestVietnameseTrajectoryCollection(unittest.TestCase):
    """Test cases for Vietnamese trajectory collection."""

    def setUp(self):
        """Set up test environment."""
        # Create a temporary directory for trajectory storage
        self.temp_dir = tempfile.mkdtemp()
        
        # Create mock models and environments
        self.mock_model = MagicMock()
        self.mock_model.generate.return_value = '{"action_type": "think", "content": "Test action"}'
        
        self.mock_env = MagicMock(spec=AgentEnvironment)
        self.mock_env.get_action_space.return_value = {
            "type": "object",
            "properties": {
                "action_type": {
                    "type": "string",
                    "enum": ["think", "answer", "use_tool"]
                },
                "content": {"type": "string"}
            },
            "required": ["action_type", "content"]
        }
        self.mock_env.step.return_value = (
            {"observation": "Test observation"},
            1.0,
            False,
            {"info": "Test info"}
        )
        
        # Create test tasks
        self.test_tasks = [
            {"id": "task1", "query": "What is the capital of Vietnam?"},
            {"id": "task2", "query": "Thủ đô của Việt Nam là gì?", "language": "vi"},
            {"id": "task3", "query": "Explain the history of Vietnam."}
        ]
        
        # Initialize trajectory collector with Vietnamese support
        self.collector = TrajectoryCollector(
            storage_path=os.path.join(self.temp_dir, "trajectories"),
            models={"test_model": self.mock_model},
            environments={"test_env": self.mock_env},
            tasks=self.test_tasks,
            max_trajectories_per_task=2,
            save_interval=1,
            verbose=True,
            vietnamese_support=True,
            vietnamese_config={
                "collect_bilingual": True,
                "translate_tasks": True,
                "vietnamese_ratio": 0.5
            }
        )
        
        # Mock Vietnamese utilities
        self.patch_detect = patch('deep_research_core.rl_tuning.trajectories.trajectory_collector.TrajectoryCollector.detect_vietnamese')
        self.mock_detect = self.patch_detect.start()
        self.mock_detect.side_effect = lambda text: "Việt" in text or "việt" in text or text and text.startswith("Thủ đô")
        
        self.patch_translate = patch('deep_research_core.rl_tuning.trajectories.trajectory_collector.TrajectoryCollector.translate_to_vietnamese')
        self.mock_translate = self.patch_translate.start()
        self.mock_translate.side_effect = lambda text: f"[VI] {text}"

    def tearDown(self):
        """Clean up after tests."""
        # Remove temporary directory
        shutil.rmtree(self.temp_dir)
        
        # Stop patches
        self.patch_detect.stop()
        self.patch_translate.stop()

    def test_vietnamese_support_initialization(self):
        """Test Vietnamese support initialization."""
        self.assertTrue(self.collector.vietnamese_support)
        self.assertEqual(self.collector.vietnamese_config["vietnamese_ratio"], 0.5)
        self.assertEqual(self.collector.vietnamese_config["collect_bilingual"], True)
        self.assertEqual(self.collector.vietnamese_config["translate_tasks"], True)

    def test_collect_vietnamese_trajectories(self):
        """Test collecting Vietnamese trajectories."""
        # Collect Vietnamese trajectories
        num_collected = self.collector.collect_vietnamese_trajectories()
        
        # Check that trajectories were collected
        self.assertGreater(num_collected, 0)
        self.assertGreater(self.collector.vietnamese_trajectories, 0)
        
        # Check that Vietnamese tasks were processed
        vietnamese_trajectories = [t for t in self.collector.trajectories if 
                                  t.get("is_vietnamese", False) or 
                                  t.get("task", {}).get("language") == "vi"]
        self.assertGreater(len(vietnamese_trajectories), 0)

    def test_collect_bilingual_trajectories(self):
        """Test collecting bilingual trajectories."""
        # Collect bilingual trajectories
        num_collected = self.collector.collect_bilingual_trajectories()
        
        # Check that trajectories were collected
        self.assertGreater(num_collected, 0)
        self.assertGreater(self.collector.bilingual_trajectories, 0)
        
        # Check that bilingual tasks were processed
        bilingual_trajectories = [t for t in self.collector.trajectories if 
                                 t.get("is_bilingual", False)]
        self.assertGreater(len(bilingual_trajectories), 0)

    def test_vietnamese_statistics(self):
        """Test Vietnamese statistics in trajectory collection."""
        # Collect some trajectories
        self.collector.collect_vietnamese_trajectories()
        self.collector.collect_bilingual_trajectories()
        
        # Get statistics
        stats = self.collector.get_statistics()
        
        # Check that language statistics are included
        self.assertIn("language_stats", stats)
        self.assertIn("vietnamese_trajectories", stats["language_stats"])
        self.assertIn("bilingual_trajectories", stats["language_stats"])
        self.assertIn("vietnamese_success_rate", stats["language_stats"])
        
        # Check that counts match
        self.assertEqual(stats["language_stats"]["vietnamese_trajectories"], self.collector.vietnamese_trajectories)
        self.assertEqual(stats["language_stats"]["bilingual_trajectories"], self.collector.bilingual_trajectories)


if __name__ == "__main__":
    unittest.main()
