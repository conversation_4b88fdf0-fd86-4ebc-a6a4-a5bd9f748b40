#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
<PERSON><PERSON><PERSON> thử cho EnhancedWebSearchAgent.

Module này chứa các bài kiểm thử cho EnhancedWebSearchAgent và các thành phần liên quan:
1. <PERSON><PERSON><PERSON> thử DistributedCache
2. <PERSON><PERSON><PERSON> thử QualityEvaluator
3. <PERSON><PERSON><PERSON> thử PluginSystem
4. <PERSON><PERSON><PERSON> thử EnhancedWebSearchAgent
"""

import os
import sys
import unittest
import tempfile
import shutil
import time
import json
from unittest.mock import patch, MagicMock

# Thêm thư mục gốc vào sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# Import các module cần kiểm thử
from src.deep_research_core.agents.enhanced_web_search_agent import EnhancedWebSearchAgent, DistributedCache
from src.deep_research_core.agents.quality_evaluator import QualityEvaluator
from src.deep_research_core.agents.plugin_system import PluginInterface, PluginManager


class TestDistributedCache(unittest.TestCase):
    """Ki<PERSON>m thử cho DistributedCache."""
    
    def setUp(self):
        """Thiết lập trước mỗi bài kiểm thử."""
        # Tạo thư mục tạm cho cache
        self.temp_dir = tempfile.mkdtemp()
        self.cache = DistributedCache(cache_dir=self.temp_dir, ttl=1, cleanup_interval=5)
    
    def tearDown(self):
        """Dọn dẹp sau mỗi bài kiểm thử."""
        # Xóa thư mục tạm
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_set_get(self):
        """Kiểm thử set và get."""
        # Set giá trị
        self.cache.set("test_key", "test_value")
        
        # Get giá trị
        value = self.cache.get("test_key")
        
        # Kiểm tra
        self.assertEqual(value, "test_value")
    
    def test_ttl(self):
        """Kiểm thử TTL."""
        # Set giá trị với TTL = 1 giây
        self.cache.set("test_key", "test_value")
        
        # Kiểm tra ngay lập tức
        value1 = self.cache.get("test_key")
        self.assertEqual(value1, "test_value")
        
        # Đợi 2 giây (lớn hơn TTL)
        time.sleep(2)
        
        # Kiểm tra lại
        value2 = self.cache.get("test_key")
        self.assertIsNone(value2)
    
    def test_delete(self):
        """Kiểm thử delete."""
        # Set giá trị
        self.cache.set("test_key", "test_value")
        
        # Xóa
        result = self.cache.delete("test_key")
        
        # Kiểm tra kết quả xóa
        self.assertTrue(result)
        
        # Kiểm tra giá trị đã bị xóa
        value = self.cache.get("test_key")
        self.assertIsNone(value)
    
    def test_clear(self):
        """Kiểm thử clear."""
        # Set nhiều giá trị
        self.cache.set("key1", "value1")
        self.cache.set("key2", "value2")
        self.cache.set("key3", "value3")
        
        # Xóa tất cả
        result = self.cache.clear()
        
        # Kiểm tra kết quả xóa
        self.assertTrue(result)
        
        # Kiểm tra các giá trị đã bị xóa
        self.assertIsNone(self.cache.get("key1"))
        self.assertIsNone(self.cache.get("key2"))
        self.assertIsNone(self.cache.get("key3"))
    
    def test_stats(self):
        """Kiểm thử thống kê."""
        # Set và get để tạo thống kê
        self.cache.set("key1", "value1")
        self.cache.get("key1")  # Hit
        self.cache.get("key2")  # Miss
        
        # Lấy thống kê
        stats = self.cache.get_stats()
        
        # Kiểm tra
        self.assertEqual(stats["hits"], 1)
        self.assertEqual(stats["misses"], 1)
        self.assertEqual(stats["sets"], 1)


class TestQualityEvaluator(unittest.TestCase):
    """Kiểm thử cho QualityEvaluator."""
    
    def setUp(self):
        """Thiết lập trước mỗi bài kiểm thử."""
        self.evaluator = QualityEvaluator()
    
    def test_evaluate_source_reliability(self):
        """Kiểm thử đánh giá độ tin cậy của nguồn."""
        # Nguồn tin cậy cao
        result1 = self.evaluator._evaluate_source_reliability("https://www.bbc.com/news/article")
        self.assertGreaterEqual(result1, 0.9)
        
        # Nguồn tin cậy trung bình
        result2 = self.evaluator._evaluate_source_reliability("https://example.com/article")
        self.assertLess(result2, 0.9)
        
        # Không có URL
        result3 = self.evaluator._evaluate_source_reliability("")
        self.assertEqual(result3, 0.5)
    
    def test_detect_spam(self):
        """Kiểm thử phát hiện spam."""
        # Nội dung spam
        result1 = self.evaluator._detect_spam("Buy now! Limited time offer!", "Get rich quick with this one weird trick")
        self.assertGreaterEqual(result1, 0.6)
        
        # Nội dung bình thường
        result2 = self.evaluator._detect_spam("Python Tutorial", "Learn Python programming with examples")
        self.assertLess(result2, 0.3)
        
        # Không có nội dung
        result3 = self.evaluator._detect_spam("", "")
        self.assertEqual(result3, 0.5)
    
    def test_evaluate_content_quality(self):
        """Kiểm thử đánh giá chất lượng nội dung."""
        # Nội dung dài và đa dạng
        long_content = "Python is a high-level, interpreted, general-purpose programming language. " * 10
        result1 = self.evaluator._evaluate_content_quality(long_content)
        self.assertGreaterEqual(result1, 0.7)
        
        # Nội dung ngắn
        short_content = "Python is a programming language."
        result2 = self.evaluator._evaluate_content_quality(short_content)
        self.assertLess(result2, 0.7)
        
        # Không có nội dung
        result3 = self.evaluator._evaluate_content_quality("")
        self.assertEqual(result3, 0.5)
    
    def test_evaluate_result(self):
        """Kiểm thử đánh giá kết quả tìm kiếm."""
        # Tạo kết quả giả
        result = {
            "url": "https://www.bbc.com/news/article",
            "title": "Python Tutorial",
            "content": "Python is a high-level, interpreted, general-purpose programming language. " * 10
        }
        
        # Đánh giá
        evaluation = self.evaluator.evaluate_result(result)
        
        # Kiểm tra
        self.assertIn("source_reliability", evaluation)
        self.assertIn("spam_score", evaluation)
        self.assertIn("content_quality", evaluation)
        self.assertIn("overall_score", evaluation)
        
        # Kiểm tra điểm tổng hợp
        self.assertGreaterEqual(evaluation["overall_score"], 0)
        self.assertLessEqual(evaluation["overall_score"], 1)


class TestPluginSystem(unittest.TestCase):
    """Kiểm thử cho PluginSystem."""
    
    def setUp(self):
        """Thiết lập trước mỗi bài kiểm thử."""
        # Tạo agent giả
        self.mock_agent = MagicMock()
        
        # Tạo plugin manager
        self.plugin_manager = PluginManager(self.mock_agent)
        
        # Tạo plugin giả
        class TestPlugin(PluginInterface):
            def __init__(self):
                super().__init__()
                self.name = "TestPlugin"
                self.version = "1.0.0"
            
            def pre_search(self, query, **kwargs):
                return {"query": f"{query} enhanced", "modified": True}
            
            def post_search(self, query, results, **kwargs):
                results["enhanced"] = True
                return results
        
        self.test_plugin = TestPlugin()
    
    def test_register_plugin(self):
        """Kiểm thử đăng ký plugin."""
        # Đăng ký plugin
        result = self.plugin_manager.register_plugin(self.test_plugin)
        
        # Kiểm tra
        self.assertTrue(result)
        self.assertIn(self.test_plugin.name, self.plugin_manager.plugins)
        self.assertIn(self.test_plugin, self.plugin_manager.hooks["pre_search"])
        self.assertIn(self.test_plugin, self.plugin_manager.hooks["post_search"])
    
    def test_unregister_plugin(self):
        """Kiểm thử hủy đăng ký plugin."""
        # Đăng ký plugin
        self.plugin_manager.register_plugin(self.test_plugin)
        
        # Hủy đăng ký
        result = self.plugin_manager.unregister_plugin(self.test_plugin.name)
        
        # Kiểm tra
        self.assertTrue(result)
        self.assertNotIn(self.test_plugin.name, self.plugin_manager.plugins)
        self.assertNotIn(self.test_plugin, self.plugin_manager.hooks["pre_search"])
        self.assertNotIn(self.test_plugin, self.plugin_manager.hooks["post_search"])
    
    def test_call_hook(self):
        """Kiểm thử gọi hook."""
        # Đăng ký plugin
        self.plugin_manager.register_plugin(self.test_plugin)
        
        # Gọi hook pre_search
        query = "test query"
        result1 = self.plugin_manager.call_hook("pre_search", query)
        
        # Kiểm tra
        self.assertEqual(result1, f"{query} enhanced")
        
        # Gọi hook post_search
        results = {"success": True, "results": []}
        result2 = self.plugin_manager.call_hook("post_search", query, results)
        
        # Kiểm tra
        self.assertTrue(result2["enhanced"])


class TestAsyncAndIntegration(unittest.TestCase):
    """Kiểm thử bất đồng bộ và tích hợp module, công cụ tìm kiếm bổ sung."""
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        with patch("src.deep_research_core.agents.enhanced_web_search_agent.ImprovedWebSearchAgent"):
            self.agent = EnhancedWebSearchAgent(
                use_distributed_cache=True,
                enable_semantic_analysis=True,
                enable_quality_evaluation=True,
                enable_plugins=True,
                cache_dir=self.temp_dir
            )
    def tearDown(self):
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    def test_async_parallel_search(self):
        """Kiểm thử tìm kiếm song song bất đồng bộ."""
        import concurrent.futures
        queries = [f"test async {i}" for i in range(5)]
        with patch.object(self.agent, "search", return_value={"success": True, "results": [1]}) as mock_search:
            with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
                results = list(executor.map(self.agent.search, queries))
            self.assertEqual(len(results), 5)
            for r in results:
                self.assertTrue(r["success"])
                
    def test_async_search_with_timeout(self):
        """Kiểm thử tìm kiếm bất đồng bộ với timeout."""
        import asyncio
        import concurrent.futures
        
        # Tạo một hàm tìm kiếm giả lập với độ trễ
        async def mock_async_search(query, delay=0.1):
            await asyncio.sleep(delay)
            return {"success": True, "query": query, "results": [1]}
        
        # Patch phương thức async_search
        with patch.object(self.agent, "async_search", side_effect=mock_async_search):
            # Tạo event loop
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Trường hợp 1: Tìm kiếm thành công trong thời gian timeout
            result1 = loop.run_until_complete(
                asyncio.wait_for(self.agent.async_search("test query"), timeout=1.0)
            )
            self.assertTrue(result1["success"])
            
            # Trường hợp 2: Tìm kiếm vượt quá thời gian timeout
            with self.assertRaises(asyncio.TimeoutError):
                loop.run_until_complete(
                    asyncio.wait_for(self.agent.async_search("test query", delay=2.0), timeout=0.5)
                )
            
            # Dọn dẹp
            loop.close()
    
    def test_integration_between_modules(self):
        """Kiểm thử tích hợp giữa agent, cache, plugin, quality evaluator."""
        with patch.object(self.agent, "search", return_value={"success": True, "results": [1]}) as mock_search:
            self.agent.cache.set("integration_test", "ok")
            self.assertEqual(self.agent.cache.get("integration_test"), "ok")
            class DummyPlugin(PluginInterface):
                def __init__(self):
                    super().__init__()
                    self.name = "DummyPlugin"
                def pre_search(self, query, **kwargs):
                    return {"query": query + " dummy", "modified": True}
            self.agent.plugin_manager = PluginManager(self.agent)
            self.agent.register_plugin(DummyPlugin())
            result = self.agent.search("integration test")
            self.assertTrue(result["success"])
    
    def test_integration_with_extra_search_engines(self):
        """Kiểm thử tích hợp với SearXNG, Qwant, engine nội địa (giả lập)."""
        engines = ["searxng", "qwant", "coccoc"]
        for engine in engines:
            with patch.object(self.agent, "search", return_value={"success": True, "engine": engine}) as mock_search:
                result = self.agent.search(f"test engine {engine}")
                self.assertTrue(result["success"])
                self.assertEqual(result["engine"], engine)
    
    def test_advanced_vietnamese_processing(self):
        """Kiểm thử xử lý tiếng Việt nâng cao: có dấu, không dấu, truy vấn phức tạp."""
        queries = [
            "Tìm hiểu về trí tuệ nhân tạo",
            "tim hieu ve tri tue nhan tao",
            "AI là gì? Ứng dụng AI trong đời sống hiện đại",
            "ai la gi ung dung ai trong doi song hien dai"
        ]
        with patch.object(self.agent, "search", return_value={"success": True, "results": [1]}) as mock_search:
            for q in queries:
                result = self.agent.search(q)
                self.assertTrue(result["success"])
    
    def test_vietnamese_search_optimization(self):
        """Kiểm thử tối ưu hóa tìm kiếm tiếng Việt."""
        # Patch phương thức optimize_vietnamese_query
        with patch.object(self.agent, "optimize_vietnamese_query", return_value="tối ưu hóa truy vấn") as mock_optimize:
            # Gọi phương thức search với truy vấn tiếng Việt
            with patch.object(self.agent, "search", return_value={"success": True, "results": [1]}) as mock_search:
                result = self.agent.search("Tìm kiếm tiếng Việt")
                
                # Kiểm tra phương thức optimize_vietnamese_query được gọi
                mock_optimize.assert_called_once_with("Tìm kiếm tiếng Việt")
                
                # Kiểm tra kết quả
                self.assertTrue(result["success"])
    
    def test_vietnamese_content_extraction(self):
        """Kiểm thử trích xuất nội dung tiếng Việt."""
        # Tạo nội dung HTML giả lập với tiếng Việt
        html_content = """
        <html>
            <head><title>Tiêu đề tiếng Việt</title></head>
            <body>
                <h1>Nội dung tiếng Việt</h1>
                <p>Đây là một đoạn văn bản tiếng Việt có dấu.</p>
                <p>Đây là đoạn văn bản thứ hai.</p>
            </body>
        </html>
        """
        
        # Patch phương thức _fetch_url để trả về nội dung HTML giả lập
        with patch.object(self.agent, "_fetch_url", return_value={"success": True, "content": html_content}) as mock_fetch:
            # Patch phương thức extract_vietnamese_content
            with patch.object(self.agent, "extract_vietnamese_content", return_value={
                "success": True,
                "title": "Tiêu đề tiếng Việt",
                "content": "Nội dung tiếng Việt\nĐây là một đoạn văn bản tiếng Việt có dấu.\nĐây là đoạn văn bản thứ hai."
            }) as mock_extract:
                # Gọi phương thức extract_content
                result = self.agent.extract_content("https://example.vn/article")
                
                # Kiểm tra kết quả
                self.assertTrue(result["success"])
                self.assertEqual(result["title"], "Tiêu đề tiếng Việt")
                self.assertIn("Nội dung tiếng Việt", result["content"])
                self.assertIn("đoạn văn bản tiếng Việt có dấu", result["content"])
    
    def test_advanced_captcha_handling(self):
        """Kiểm thử xử lý CAPTCHA nâng cao: nhiều loại, động, phức tạp."""
        captcha_types = ["recaptcha", "hcaptcha", "cloudflare", "slider", "turnstile"]
        for ctype in captcha_types:
            with patch("src.deep_research_core.agents.enhanced_web_search_agent.ImprovedWebSearchAgent.solve_captcha", return_value={"success": True, "type": ctype}) as mock_captcha:
                result = self.agent.agent.solve_captcha("http://test.com", ctype)
                self.assertTrue(result["success"])
                self.assertEqual(result["type"], ctype)
    
    def test_vietnamese_captcha_detection(self):
        """Kiểm thử phát hiện CAPTCHA tiếng Việt."""
        # Tạo nội dung HTML giả lập với CAPTCHA tiếng Việt
        html_content = """
        <html>
            <body>
                <div class="captcha-container">
                    <p>Vui lòng xác nhận bạn không phải là robot</p>
                    <div class="g-recaptcha" data-sitekey="abc123"></div>
                </div>
            </body>
        </html>
        """
        
        # Patch phương thức detect_captcha
        with patch.object(self.agent, "detect_captcha", return_value=(True, "recaptcha")) as mock_detect:
            # Gọi phương thức _fetch_url với nội dung HTML giả lập
            with patch.object(self.agent, "_fetch_url", return_value={"success": True, "content": html_content}):
                # Gọi phương thức extract_content
                with patch.object(self.agent.agent, "solve_captcha", return_value={"success": True, "type": "recaptcha"}):
                    result = self.agent.extract_content("https://example.vn/captcha-page")
                    
                    # Kiểm tra phương thức detect_captcha được gọi
                    mock_detect.assert_called_once()
                    
                    # Kiểm tra kết quả
                    self.assertTrue(result["success"])
    def test_search(self, mock_search):
        """Kiểm thử phương thức search."""
        # Thiết lập mock
        mock_search.return_value = {
            "success": True,
            "query": "test query",
            "results": [
                {"url": "https://example.com", "title": "Example", "content": "Example content"}
            ]
        }
        
        # Gọi phương thức search
        result = self.agent.search("test query")
        
        # Kiểm tra
        self.assertTrue(result["success"])
        self.assertEqual(result["query"], "test query")
        self.assertEqual(len(result["results"]), 1)
    
    @patch("src.deep_research_core.agents.enhanced_web_search_agent.ImprovedWebSearchAgent.extract_content")
    def test_extract_content(self, mock_extract):
        """Kiểm thử phương thức extract_content."""
        # Thiết lập mock
        mock_extract.return_value = {
            "success": True,
            "url": "https://example.com",
            "title": "Example",
            "content": "Example content"
        }
        
        # Gọi phương thức extract_content
        result = self.agent.extract_content("https://example.com")
        
        # Kiểm tra
        self.assertTrue(result["success"])
        self.assertEqual(result["url"], "https://example.com")
        self.assertEqual(result["title"], "Example")
        self.assertEqual(result["content"], "Example content")
    
    def test_plugin_integration(self):
        """Kiểm thử tích hợp plugin."""
        # Tạo plugin giả
        class TestPlugin(PluginInterface):
            def __init__(self):
                super().__init__()
                self.name = "TestPlugin"
                self.version = "1.0.0"
            
            def pre_search(self, query, **kwargs):
                return {"query": f"{query} enhanced", "modified": True}
        
        # Đăng ký plugin
        plugin = TestPlugin()
        
        # Gán plugin_manager giả
        self.agent.plugin_manager = PluginManager(self.agent)
        result = self.agent.register_plugin(plugin)
        
        # Kiểm tra
        self.assertTrue(result)
        
        # Kiểm tra thông tin plugin
        plugin_info = self.agent.get_plugin_info()
        self.assertEqual(len(plugin_info), 1)
        self.assertEqual(plugin_info[0]["name"], "TestPlugin")


if __name__ == "__main__":
    unittest.main()
