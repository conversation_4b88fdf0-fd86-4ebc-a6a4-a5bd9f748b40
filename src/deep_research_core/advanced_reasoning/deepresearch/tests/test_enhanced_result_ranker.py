"""
Tests for the EnhancedResultRanker class.
"""

import unittest
from unittest.mock import patch, MagicMock
import time

from deepresearch.src.deep_research_core.agents.enhanced_result_ranker import EnhancedResultRanker

class TestEnhancedResultRanker(unittest.TestCase):
    """Test cases for EnhancedResultRanker."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.ranker = EnhancedResultRanker()
        
        # Sample results
        self.sample_results = [
            {
                "title": "Python Programming Language",
                "snippet": "Python is a high-level, interpreted programming language.",
                "url": "https://www.python.org/about/",
            },
            {
                "title": "Learn Python - Free Interactive Python Tutorial",
                "snippet": "Learn Python, a powerful programming language used for web development and data analysis.",
                "url": "https://www.learnpython.org/",
            },
            {
                "title": "Random Website with Python Mention",
                "snippet": "This website has nothing to do with Python programming.",
                "url": "https://www.example.com/random",
            },
            {
                "title": "Python Documentation",
                "snippet": "Official documentation for the Python programming language.",
                "url": "https://docs.python.org/3/",
                "timestamp": time.time(),  # Current time
            },
            {
                "title": "Old Python Article",
                "snippet": "An old article about Python programming.",
                "url": "https://www.example.org/python-article",
                "timestamp": time.time() - 365 * 24 * 3600,  # 1 year ago
            },
        ]
    
    def test_rank_results(self):
        """Test ranking of search results."""
        query = "python programming language"
        ranked_results = self.ranker.rank_results(query, self.sample_results)
        
        # Check that results are returned
        self.assertTrue(len(ranked_results) > 0)
        
        # Check that results are sorted by final_score
        for i in range(len(ranked_results) - 1):
            self.assertGreaterEqual(
                ranked_results[i]["final_score"],
                ranked_results[i + 1]["final_score"]
            )
        
        # Check that scores are added to results
        for result in ranked_results:
            self.assertIn("relevance_score", result)
            self.assertIn("domain_trust_score", result)
            self.assertIn("content_quality_score", result)
            self.assertIn("freshness_score", result)
            self.assertIn("final_score", result)
    
    def test_calculate_relevance_score(self):
        """Test calculation of relevance score."""
        query = "python programming"
        
        # Test with highly relevant result
        result = self.sample_results[0]  # "Python Programming Language"
        score = self.ranker._calculate_relevance_score(query, result, "en")
        self.assertGreater(score, 0.5)
        
        # Test with less relevant result
        result = self.sample_results[2]  # "Random Website with Python Mention"
        score = self.ranker._calculate_relevance_score(query, result, "en")
        self.assertLess(score, 0.5)
        
        # Test with pre-existing relevance score
        result_with_score = result.copy()
        result_with_score["relevance_score"] = 0.75
        score = self.ranker._calculate_relevance_score(query, result_with_score, "en")
        self.assertEqual(score, 0.75)
    
    def test_calculate_domain_trust_score(self):
        """Test calculation of domain trust score."""
        # Test with trusted domain
        result = self.sample_results[0]  # python.org
        score = self.ranker._calculate_domain_trust_score(result)
        self.assertGreater(score, 0.8)
        
        # Test with unknown domain
        result = self.sample_results[2]  # example.com
        score = self.ranker._calculate_domain_trust_score(result)
        self.assertEqual(score, 0.5)
        
        # Test with no URL
        result = {"title": "No URL"}
        score = self.ranker._calculate_domain_trust_score(result)
        self.assertEqual(score, 0.5)
    
    def test_calculate_freshness_score(self):
        """Test calculation of freshness score."""
        # Test with recent content
        result = self.sample_results[3]  # Current time
        score = self.ranker._calculate_freshness_score(result)
        self.assertEqual(score, 1.0)
        
        # Test with old content
        result = self.sample_results[4]  # 1 year ago
        score = self.ranker._calculate_freshness_score(result)
        self.assertEqual(score, 0.4)
        
        # Test with no timestamp
        result = self.sample_results[0]  # No timestamp
        score = self.ranker._calculate_freshness_score(result)
        self.assertEqual(score, 0.5)
    
    def test_tokenize(self):
        """Test tokenization of text."""
        # Test English tokenization
        text = "Python is a programming language"
        tokens = self.ranker._tokenize(text, "en")
        self.assertEqual(tokens, ["Python", "is", "a", "programming", "language"])
        
        # Test Vietnamese tokenization
        text = "Python là ngôn ngữ lập trình"
        tokens = self.ranker._tokenize(text, "vi")
        self.assertEqual(tokens, ["Python", "là", "ngôn", "ngữ", "lập", "trình"])
    
    def test_calculate_keyword_density(self):
        """Test calculation of keyword density."""
        text = "Python is a programming language. Python is used for web development."
        keywords = {"python", "programming"}
        density = self.ranker._calculate_keyword_density(text, keywords, "en")
        
        # Expected: 3 matches (2 "python", 1 "programming") out of 12 words
        expected_density = min(1.0, (3 / 12) * 5)
        self.assertEqual(density, expected_density)

if __name__ == "__main__":
    unittest.main()
