"""
Test for improved search functionality.
"""

import unittest
import sys
import os
import asyncio
from unittest.mock import patch, MagicMock

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.utils.query_analyzer import QueryAnalyzer
from src.deep_research_core.utils.async_content_extractor import AsyncContentExtractor
from src.deep_research_core.utils.search_worker_pool import SearchWorkerPool
from src.deep_research_core.utils.error_handling import (
    SearchError, RateLimitError, ConnectionError, ContentExtractionError,
    BotDetectionError, TimeoutError, with_retry, format_error_response
)


class TestQueryAnalyzer(unittest.TestCase):
    """Test the query analyzer."""
    
    def test_analyze_query(self):
        """Test query analysis."""
        analyzer = QueryAnalyzer()
        
        # Test English query
        result = analyzer.analyze_query("What is the capital of France?")
        self.assertEqual(result["language"], "en")
        self.assertEqual(result["intent"]["primary_intent"], "informational")
        self.assertTrue("capital" in result["keywords"])
        self.assertTrue("france" in result["keywords"])
        self.assertTrue(result["is_question"])
        
        # Test Vietnamese query
        result = analyzer.analyze_query("Thủ đô của Pháp là gì?")
        self.assertEqual(result["language"], "vi")
        self.assertTrue(result["is_question"])
    
    def test_optimize_query(self):
        """Test query optimization."""
        analyzer = QueryAnalyzer()
        
        # Test optimization of a long query
        long_query = "I need to find information about the history of artificial intelligence and its development over the years, including key milestones and important researchers in the field"
        result = analyzer.optimize_query(long_query)
        
        self.assertIsNotNone(result["optimized_query"])
        self.assertLess(len(result["optimized_query"]), len(long_query))
        self.assertGreater(len(result["variants"]), 0)


class TestAsyncContentExtractor(unittest.TestCase):
    """Test the async content extractor."""
    
    def test_init(self):
        """Test initialization."""
        extractor = AsyncContentExtractor(
            max_concurrent_requests=5,
            timeout=10,
            max_retries=2
        )
        
        self.assertEqual(extractor.max_concurrent_requests, 5)
        self.assertEqual(extractor.timeout, 10)
        self.assertEqual(extractor.max_retries, 2)
    
    @patch('aiohttp.ClientSession')
    async def test_extract_content(self, mock_session):
        """Test content extraction."""
        # Mock response
        mock_response = MagicMock()
        mock_response.status = 200
        mock_response.text.return_value = "<html><title>Test Page</title><body><p>Test content</p></body></html>"
        
        # Mock session
        mock_session_instance = MagicMock()
        mock_session_instance.__aenter__.return_value = mock_session_instance
        mock_session_instance.get.return_value.__aenter__.return_value = mock_response
        mock_session.return_value = mock_session_instance
        
        # Create extractor
        extractor = AsyncContentExtractor(use_playwright=False)
        
        # Test extraction
        result = await extractor.extract_content_from_urls(["http://example.com"])
        
        self.assertTrue(result["success"])
        self.assertEqual(len(result["results"]), 1)
        self.assertEqual(result["results"][0]["url"], "http://example.com")
        self.assertEqual(result["results"][0]["title"], "Test Page")
    
    def test_prioritize_urls(self):
        """Test URL prioritization."""
        extractor = AsyncContentExtractor()
        
        urls = [
            "http://example.com",
            "https://en.wikipedia.org/wiki/Test",
            "https://official.site.com",
            "http://random-blog.com",
            "https://government.gov/info"
        ]
        
        prioritized = extractor._prioritize_urls(urls)
        
        # Wikipedia, .gov and official sites should be prioritized
        self.assertEqual(prioritized[0], "https://official.site.com")
        self.assertTrue(prioritized.index("https://en.wikipedia.org/wiki/Test") < prioritized.index("http://random-blog.com"))
        self.assertTrue(prioritized.index("https://government.gov/info") < prioritized.index("http://example.com"))


class TestSearchWorkerPool(unittest.TestCase):
    """Test the search worker pool."""
    
    def test_init(self):
        """Test initialization."""
        pool = SearchWorkerPool(num_workers=3, max_queue_size=50)
        
        self.assertEqual(pool.num_workers, 3)
        self.assertEqual(pool.max_queue_size, 50)
        self.assertEqual(len(pool.workers), 3)
    
    def test_submit_task(self):
        """Test task submission."""
        pool = SearchWorkerPool(num_workers=1)
        
        # Define a simple task function
        def task_func(x, y):
            return x + y
        
        # Submit task
        task_id = pool.submit_task(task_func, 2, 3)
        
        # Check task ID
        self.assertIsNotNone(task_id)
        
        # Get result
        result = pool.get_result(task_id, timeout=1.0)
        
        # Check result
        self.assertEqual(result["status"], "completed")
        self.assertEqual(result["result"], 5)
    
    def test_priority(self):
        """Test task priority."""
        pool = SearchWorkerPool(num_workers=1)
        
        # Define a task function that sleeps
        def slow_task(sleep_time):
            import time
            time.sleep(sleep_time)
            return sleep_time
        
        # Submit low priority task that takes longer
        low_priority_id = pool.submit_task(slow_task, 0.5, priority=0)
        
        # Submit high priority task
        high_priority_id = pool.submit_task(slow_task, 0.1, priority=10)
        
        # Get high priority result first
        high_result = pool.get_result(high_priority_id, timeout=1.0)
        
        # Then get low priority result
        low_result = pool.get_result(low_priority_id, timeout=1.0)
        
        # Check results
        self.assertEqual(high_result["result"], 0.1)
        self.assertEqual(low_result["result"], 0.5)


class TestErrorHandling(unittest.TestCase):
    """Test error handling."""
    
    def test_search_error(self):
        """Test SearchError."""
        error = SearchError("Test error", {"detail": "test"})
        
        self.assertEqual(error.message, "Test error")
        self.assertEqual(error.details["detail"], "test")
        
        # Test conversion to dict
        error_dict = error.to_dict()
        self.assertEqual(error_dict["error_type"], "SearchError")
        self.assertEqual(error_dict["message"], "Test error")
    
    def test_rate_limit_error(self):
        """Test RateLimitError."""
        error = RateLimitError("Rate limited", "google", 30.0)
        
        self.assertEqual(error.message, "Rate limited")
        self.assertEqual(error.details["engine"], "google")
        self.assertEqual(error.details["retry_after"], 30.0)
    
    def test_with_retry_decorator(self):
        """Test with_retry decorator."""
        # Counter for number of calls
        calls = {"count": 0}
        
        # Function that fails twice then succeeds
        @with_retry(max_retries=3, initial_backoff=0.1)
        def flaky_function():
            calls["count"] += 1
            if calls["count"] < 3:
                raise ConnectionError("Connection failed")
            return "success"
        
        # Call the function
        result = flaky_function()
        
        # Check result
        self.assertEqual(result, "success")
        self.assertEqual(calls["count"], 3)
    
    def test_format_error_response(self):
        """Test format_error_response."""
        # Test with string error
        response = format_error_response(
            query="test query",
            error="Test error",
            engine="test_engine",
            search_method="test_method"
        )
        
        self.assertFalse(response["success"])
        self.assertEqual(response["error"], "Test error")
        self.assertEqual(response["query"], "test query")
        self.assertEqual(response["engine"], "test_engine")
        
        # Test with exception
        error = RateLimitError("Rate limited", "google", 30.0)
        response = format_error_response(
            query="test query",
            error=error,
            engine="google",
            search_method="api"
        )
        
        self.assertFalse(response["success"])
        self.assertEqual(response["error"], "Rate limited")
        self.assertEqual(response["error_type"], "RateLimitError")


if __name__ == '__main__':
    unittest.main()
