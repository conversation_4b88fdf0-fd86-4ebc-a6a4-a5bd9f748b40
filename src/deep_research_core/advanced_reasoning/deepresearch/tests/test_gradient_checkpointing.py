"""
Tests for the gradient checkpointing implementation.
"""

import unittest
import torch
import torch.nn as nn
from src.deep_research_core.optimization.memory.gradient_checkpointing import (
    GradientCheckpointer,
    apply_gradient_checkpointing,
    enable_transformer_checkpointing,
    estimate_memory_savings,
    create_checkpointed_model
)

class SimpleModel(nn.Module):
    """A simple model for testing gradient checkpointing."""

    def __init__(self, input_size=10, hidden_size=20, num_layers=5):
        super().__init__()
        self.layers = nn.ModuleList([
            nn.Sequential(
                nn.Linear(input_size if i == 0 else hidden_size, hidden_size),
                nn.ReLU()
            )
            for i in range(num_layers)
        ])
        self.output = nn.Linear(hidden_size, 1)

    def forward(self, x):
        for layer in self.layers:
            x = layer(x)
        return self.output(x)

class TestGradientCheckpointing(unittest.TestCase):
    """Test cases for the GradientCheckpointing class."""

    def setUp(self):
        """Set up test fixtures."""
        self.model = SimpleModel()
        self.input_tensor = torch.randn(32, 10)  # batch_size=32, input_size=10
        self.checkpointer = GradientCheckpointer()

    def test_gradient_checkpointer_init(self):
        """Test initialization of GradientCheckpointer."""
        # Test with default parameters
        checkpointer = GradientCheckpointer()
        self.assertEqual(checkpointer.checkpoint_ratio, 0.5)
        self.assertTrue(checkpointer.use_reentrant)
        self.assertTrue(checkpointer.preserve_rng_state)
        self.assertFalse(checkpointer.selective_checkpointing)

        # Test with custom parameters
        checkpointer = GradientCheckpointer(
            checkpoint_ratio=0.7,
            use_reentrant=False,
            preserve_rng_state=False,
            selective_checkpointing=True
        )
        self.assertEqual(checkpointer.checkpoint_ratio, 0.7)
        self.assertFalse(checkpointer.use_reentrant)
        self.assertFalse(checkpointer.preserve_rng_state)
        self.assertTrue(checkpointer.selective_checkpointing)

        # Test with invalid checkpoint_ratio
        with self.assertRaises(ValueError):
            GradientCheckpointer(checkpoint_ratio=1.5)

    def test_checkpoint_function(self):
        """Test checkpointing a function."""
        def example_function(x):
            return x * 2

        input_tensor = torch.randn(10, requires_grad=True)

        # Run function normally
        normal_output = example_function(input_tensor)

        # Run function with checkpointing
        checkpointed_output = self.checkpointer.checkpoint_function(example_function, input_tensor)

        # Check that outputs are the same
        self.assertTrue(torch.allclose(normal_output, checkpointed_output))

        # Check that gradients can flow through checkpointed function
        checkpointed_output.sum().backward()
        self.assertIsNotNone(input_tensor.grad)

    def test_apply_gradient_checkpointing(self):
        """Test applying gradient checkpointing to a model."""
        # Create a copy of the model for comparison
        model_copy = SimpleModel()
        model_copy.load_state_dict(self.model.state_dict())

        # Apply gradient checkpointing to the model
        apply_gradient_checkpointing(self.model, checkpoint_ratio=1.0)

        # Check that the model still produces the same output
        with torch.no_grad():
            normal_output = model_copy(self.input_tensor)
            checkpointed_output = self.model(self.input_tensor)
            self.assertTrue(torch.allclose(normal_output, checkpointed_output))

    def test_estimate_memory_savings(self):
        """Test estimating memory savings from gradient checkpointing."""
        # Test with input shape
        savings = estimate_memory_savings(
            self.model,
            checkpoint_ratio=0.5,
            input_shape=(10,),
            batch_size=32
        )

        # Check that the dictionary contains the expected keys
        expected_keys = [
            "total_params",
            "params_memory_mb",
            "estimated_activation_memory_mb",
            "estimated_saved_memory_mb",
            "percentage_savings"
        ]
        for key in expected_keys:
            self.assertIn(key, savings)

        # Check that the values are reasonable
        self.assertGreater(savings["total_params"], 0)
        self.assertGreater(savings["params_memory_mb"], 0)
        self.assertGreater(savings["estimated_activation_memory_mb"], 0)
        self.assertGreater(savings["estimated_saved_memory_mb"], 0)
        self.assertGreater(savings["percentage_savings"], 0)

        # Test without input shape
        savings_no_input = estimate_memory_savings(
            self.model,
            checkpoint_ratio=0.5
        )

        # Check that the dictionary contains the expected keys
        for key in expected_keys:
            self.assertIn(key, savings_no_input)

    def test_create_checkpointed_model(self):
        """Test creating a model with gradient checkpointing already applied."""
        # Create a checkpointed model
        checkpointed_model = create_checkpointed_model(
            SimpleModel,
            input_size=10,
            hidden_size=20,
            num_layers=5,
            checkpoint_ratio=1.0
        )

        # Check that the model has the expected structure
        self.assertIsInstance(checkpointed_model, SimpleModel)
        self.assertEqual(len(checkpointed_model.layers), 5)

        # Check that the model can still be used for forward pass
        output = checkpointed_model(self.input_tensor)
        self.assertEqual(output.shape, (32, 1))

class TestTransformerCheckpointing(unittest.TestCase):
    """Test cases for transformer-specific checkpointing functions."""

    def setUp(self):
        """Set up test fixtures."""
        # Skip tests if transformers library is not available
        try:
            import transformers
            self.transformers_available = True
        except ImportError:
            self.transformers_available = False

    def test_enable_transformer_checkpointing(self):
        """Test enabling gradient checkpointing for transformer models."""
        if not self.transformers_available:
            self.skipTest("transformers library not available")

        from transformers import BertModel, BertConfig

        # Create a small BERT model
        config = BertConfig(
            hidden_size=32,
            num_hidden_layers=2,
            num_attention_heads=2,
            intermediate_size=64
        )
        model = BertModel(config)

        # Enable gradient checkpointing
        enable_transformer_checkpointing(model)

        # Check that gradient checkpointing is enabled
        # Different transformer models might have different attribute names
        self.assertTrue(
            hasattr(model, 'gradient_checkpointing') or
            hasattr(model, 'is_gradient_checkpointing') or
            hasattr(model, 'gradient_checkpointing_enable')
        )

        # Create a simple model without the gradient_checkpointing_enable method
        class CustomModel(nn.Module):
            def __init__(self):
                super().__init__()
                self.layers = nn.ModuleList([
                    nn.Linear(32, 32),
                    nn.ReLU(),
                    nn.Linear(32, 32)
                ])

            def forward(self, x):
                for layer in self.layers:
                    x = layer(x)
                return x

        custom_model = CustomModel()

        # Enable gradient checkpointing
        enable_transformer_checkpointing(custom_model)

        # Check that the model still works
        input_tensor = torch.randn(8, 32)  # [batch_size, features]
        output = custom_model(input_tensor)
        self.assertEqual(output.shape, (8, 32))

if __name__ == '__main__':
    unittest.main()
