"""
Test module for SearchResultOptimizer.
"""

import unittest
import json
import os
import sys
import os.path

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import directly without going through __init__.py
from src.deep_research_core.agents.search_result_optimizer import SearchResultOptimizer

class TestSearchResultOptimizer(unittest.TestCase):
    """
    Test cases for SearchResultOptimizer.
    """

    def setUp(self):
        """
        Set up test fixtures.
        """
        self.optimizer = SearchResultOptimizer(
            relevance_threshold=0.3,
            quality_threshold=0.2,
            max_content_length=1000,
            max_results=5,
            vietnamese_support=True
        )

        # Sample search results
        self.sample_results = {
            "success": True,
            "query": "Python programming",
            "results": [
                {
                    "title": "Python Programming Language",
                    "url": "https://www.python.org/",
                    "snippet": "Python is a programming language that lets you work quickly and integrate systems more effectively.",
                    "content": "Python is a programming language that lets you work quickly and integrate systems more effectively. Python is a high-level, interpreted, general-purpose programming language. Its design philosophy emphasizes code readability with the use of significant indentation. Python is dynamically-typed and garbage-collected."
                },
                {
                    "title": "Learn Python - Free Interactive Python Tutorial",
                    "url": "https://www.learnpython.org/",
                    "snippet": "Learn Python, a powerful programming language used for many different applications.",
                    "content": "Learn Python, a powerful programming language used for many different applications. Python is a widely used high-level programming language for general-purpose programming, created by Guido van Rossum and first released in 1991. Python features a dynamic type system and automatic memory management and supports multiple programming paradigms."
                },
                {
                    "title": "Python Tutorial - W3Schools",
                    "url": "https://www.w3schools.com/python/",
                    "snippet": "Python is a popular programming language. Python can be used on a server to create web applications.",
                    "content": "Python is a popular programming language. Python can be used on a server to create web applications. Python is a popular programming language. It was created by Guido van Rossum, and released in 1991. It is used for web development, software development, mathematics, system scripting, etc."
                },
                {
                    "title": "Buy Python Books Online",
                    "url": "https://www.amazon.com/python-books/",
                    "snippet": "Shop for Python programming books and other related products.",
                    "content": "Shop for Python programming books and other related products. Find the best deals on Python books, courses, and accessories. Python is one of the most popular programming languages today."
                },
                {
                    "title": "Python Snake Facts",
                    "url": "https://www.snakefacts.com/python/",
                    "snippet": "Learn about python snakes, their habitat, diet, and behavior.",
                    "content": "Python snakes are some of the largest snakes in the world. They are constrictors, which means they coil around their prey and squeeze until the prey can no longer breathe. Pythons are found in Africa, Asia, and Australia."
                }
            ],
            "timestamp": 1625097600
        }

        # Sample Vietnamese search results
        self.vietnamese_results = {
            "success": True,
            "query": "Lập trình Python",
            "results": [
                {
                    "title": "Ngôn ngữ lập trình Python",
                    "url": "https://vi.wikipedia.org/wiki/Python_(ngôn_ngữ_lập_trình)",
                    "snippet": "Python là một ngôn ngữ lập trình bậc cao, thông dịch, hướng đối tượng.",
                    "content": "Python là một ngôn ngữ lập trình bậc cao, thông dịch, hướng đối tượng. Python được tạo ra bởi Guido van Rossum và lần đầu tiên được phát hành vào năm 1991. Python có cú pháp đơn giản, dễ đọc, giảm chi phí bảo trì chương trình."
                },
                {
                    "title": "Học Python cơ bản",
                    "url": "https://www.howkteam.vn/course/lap-trinh-python-co-ban-37",
                    "snippet": "Khóa học Python cơ bản dành cho người mới bắt đầu.",
                    "content": "Khóa học Python cơ bản dành cho người mới bắt đầu. Python là một ngôn ngữ lập trình phổ biến được sử dụng trong nhiều lĩnh vực khác nhau như phát triển web, khoa học dữ liệu, trí tuệ nhân tạo, v.v."
                },
                {
                    "title": "Tài liệu học Python tiếng Việt",
                    "url": "https://pythonvietnam.info/",
                    "snippet": "Tài liệu học Python đầy đủ bằng tiếng Việt cho người mới bắt đầu.",
                    "content": "Tài liệu học Python đầy đủ bằng tiếng Việt cho người mới bắt đầu. Python là ngôn ngữ lập trình được thiết kế để dễ đọc, dễ viết và dễ bảo trì. Python có thể được sử dụng để phát triển các ứng dụng web, phân tích dữ liệu, trí tuệ nhân tạo và nhiều lĩnh vực khác."
                },
                {
                    "title": "Trăn Python khổng lồ",
                    "url": "https://vnexpress.net/tran-python-khong-lo-4123456.html",
                    "snippet": "Phát hiện trăn Python dài 6m tại rừng U Minh Hạ.",
                    "content": "Phát hiện trăn Python dài 6m tại rừng U Minh Hạ. Đây là một trong những loài trăn lớn nhất thế giới, có thể nuốt chửng một con trâu."
                }
            ],
            "timestamp": 1625097600
        }

    def test_optimize_english_results(self):
        """
        Test optimizing English search results.
        """
        # Optimize results
        optimized = self.optimizer.optimize(self.sample_results, "Python programming")

        # Check if optimization was successful
        self.assertTrue(optimized.get("success"))
        self.assertTrue(optimized.get("optimized"))

        # Check if results were filtered (snake result should be removed)
        results = optimized.get("results", [])
        self.assertLessEqual(len(results), 5)

        # Check if all results have the required fields
        for result in results:
            self.assertIn("title", result)
            self.assertIn("url", result)
            self.assertIn("content", result)
            self.assertIn("relevance_score", result)

            # Check if content length is limited
            self.assertLessEqual(len(result.get("content", "")), self.optimizer.max_content_length)

            # Check if snake result is not included
            self.assertNotIn("snake", result.get("title", "").lower())

    def test_optimize_vietnamese_results(self):
        """
        Test optimizing Vietnamese search results.
        """
        # Optimize results
        optimized = self.optimizer.optimize(self.vietnamese_results, "Lập trình Python")

        # Check if optimization was successful
        self.assertTrue(optimized.get("success"))
        self.assertTrue(optimized.get("optimized"))

        # Check if results were filtered (snake result should be removed)
        results = optimized.get("results", [])
        self.assertLessEqual(len(results), 5)

        # Check if all results have the required fields
        for result in results:
            self.assertIn("title", result)
            self.assertIn("url", result)
            self.assertIn("content", result)
            self.assertIn("relevance_score", result)

            # Check if content length is limited
            self.assertLessEqual(len(result.get("content", "")), self.optimizer.max_content_length)

            # Check if snake result is not included
            self.assertNotIn("trăn", result.get("title", "").lower())

    def test_empty_results(self):
        """
        Test optimizing empty search results.
        """
        # Create empty results
        empty_results = {
            "success": True,
            "query": "Python programming",
            "results": [],
            "timestamp": 1625097600
        }

        # Optimize results
        optimized = self.optimizer.optimize(empty_results, "Python programming")

        # Check if optimization was successful
        self.assertTrue(optimized.get("success"))
        self.assertTrue(optimized.get("optimized"))

        # Check if results are still empty
        self.assertEqual(len(optimized.get("results", [])), 0)

    def test_failed_search(self):
        """
        Test optimizing failed search results.
        """
        # Create failed search results
        failed_results = {
            "success": False,
            "error": "Search failed",
            "query": "Python programming",
            "results": [],
            "timestamp": 1625097600
        }

        # Optimize results
        optimized = self.optimizer.optimize(failed_results, "Python programming")

        # Check if optimization preserved the failed status
        self.assertFalse(optimized.get("success"))
        self.assertTrue(optimized.get("optimized"))
        self.assertEqual(optimized.get("error"), "Search failed")

if __name__ == "__main__":
    unittest.main()
