"""
Tests for Vietnamese domain-specific compound words functionality.

This module contains tests for the VietnameseDomainCompounds class and the
domain-specific features of the VietnameseCompoundProcessor.
"""

import unittest
import sys
import os

# Add the parent directory to the path so we can import the package
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.multilingual.vietnamese_domain_compounds import VietnameseDomainCompounds
from src.deep_research_core.multilingual.vietnamese_compound_processor import VietnameseCompoundProcessor


class TestVietnameseDomainCompounds(unittest.TestCase):
    """Test cases for Vietnamese domain-specific compound words."""

    def setUp(self):
        """Set up test fixtures."""
        # Get an instance of the domain compounds class
        self.domain_compounds = VietnameseDomainCompounds()
        
        # Get an instance of the compound processor
        self.processor = VietnameseCompoundProcessor.get_instance()
        
        # Test samples for each domain
        self.test_texts = {
            "medical": "Bệnh viện đa khoa thành phố đang điều trị nhiều bệnh nhân ung thư và tim mạch. <PERSON><PERSON><PERSON> bác sĩ và y tá làm việc chăm chỉ để chăm sóc sức khỏe của mọi người.",
            "legal": "Luật sư đang soạn thảo hợp đồng mua bán nhà đất. Vụ án hình sự sẽ được tòa án nhân dân xét xử vào tuần tới, với sự tham gia của công tố viên và các bên tranh chấp.",
            "technology": "Trí tuệ nhân tạo và học máy đang là xu hướng công nghệ nổi bật. Các kỹ sư phần mềm làm việc với cơ sở dữ liệu và mã nguồn mở trên điện toán đám mây.",
            "education": "Trường đại học đang tổ chức kỳ thi học kỳ. Giáo viên soạn giáo trình và bài giảng cho lớp học, trong khi sinh viên nghiên cứu để viết luận văn tốt nghiệp.",
            "economics": "Thị trường chứng khoán đang biến động mạnh, ảnh hưởng đến các nhà đầu tư. Công ty cổ phần cần phân tích doanh thu và lợi nhuận để đối phó với lạm phát và tăng trưởng kinh tế.",
            "agriculture": "Nông dân trồng trọt và chăn nuôi sử dụng phân bón và thuốc trừ sâu. Vụ đông xuân năm nay có năng suất cây trồng cao nhờ hệ thống tưới tiêu tốt.",
            "environment": "Ô nhiễm không khí và biến đổi khí hậu là những vấn đề môi trường quan trọng. Năng lượng tái tạo và tái chế chất thải giúp bảo vệ môi trường và phát triển bền vững.",
            "science": "Phản ứng hóa học giữa axit và bazơ tạo ra muối. Các nhà khoa học nghiên cứu về tế bào và ADN để hiểu thêm về gen và protein trong cơ thể."
        }

    def test_get_all_domain_compounds(self):
        """Test retrieving all domain-specific compound words."""
        all_compounds = VietnameseDomainCompounds.get_all_domain_compounds()
        
        # Verify that the dictionary is not empty
        self.assertGreater(len(all_compounds), 0, "Domain compounds dictionary should not be empty")
        
        # Verify some key compounds from different domains
        self.assertIn("trí tuệ nhân tạo", all_compounds, "Expected 'trí tuệ nhân tạo' in compounds")
        self.assertIn("bệnh viện", all_compounds, "Expected 'bệnh viện' in compounds")
        self.assertIn("luật sư", all_compounds, "Expected 'luật sư' in compounds")
        self.assertIn("trường học", all_compounds, "Expected 'trường học' in compounds")

    def test_get_domain_compounds(self):
        """Test retrieving compounds for specific domains."""
        # Test for medical domain
        medical_compounds = VietnameseDomainCompounds.get_domain_compounds("medical")
        self.assertIn("bệnh viện", medical_compounds, "Expected 'bệnh viện' in medical compounds")
        self.assertIn("sức khỏe", medical_compounds, "Expected 'sức khỏe' in medical compounds")
        
        # Test for technology domain
        technology_compounds = VietnameseDomainCompounds.get_domain_compounds("technology")
        self.assertIn("trí tuệ nhân tạo", technology_compounds, "Expected 'trí tuệ nhân tạo' in technology compounds")
        self.assertIn("học máy", technology_compounds, "Expected 'học máy' in technology compounds")
        
        # Test for non-existent domain
        nonexistent_compounds = VietnameseDomainCompounds.get_domain_compounds("nonexistent")
        self.assertEqual(len(nonexistent_compounds), 0, "Expected empty dictionary for non-existent domain")

    def test_get_available_domains(self):
        """Test retrieving the list of available domains."""
        domains = VietnameseDomainCompounds.get_available_domains()
        
        # Verify that all expected domains are available
        expected_domains = ["medical", "legal", "technology", "education", 
                            "economics", "agriculture", "environment", "science"]
        
        for domain in expected_domains:
            self.assertIn(domain, domains, f"Expected domain '{domain}' in available domains")
            
        # Verify count matches expected
        self.assertEqual(len(domains), len(expected_domains), 
                         f"Expected {len(expected_domains)} domains, got {len(domains)}")

    def test_processor_integration(self):
        """Test integration with VietnameseCompoundProcessor."""
        # Test that the processor loads domain compounds
        compounds_dict = self.processor._compound_dict
        
        # Verify some domain-specific compounds are in the processor's dictionary
        self.assertIn("trí tuệ nhân tạo", compounds_dict, "Expected 'trí tuệ nhân tạo' in processor dictionary")
        self.assertIn("bệnh viện", compounds_dict, "Expected 'bệnh viện' in processor dictionary")
        self.assertIn("luật sư", compounds_dict, "Expected 'luật sư' in processor dictionary")

    def test_filter_by_domain(self):
        """Test filtering compounds by domain."""
        # Test medical domain filtering
        medical_text = self.test_texts["medical"]
        medical_compounds = self.processor.filter_by_domain(medical_text, "medical")
        
        # Verify that medical compounds are detected
        found_compounds = [compound for compound, _, _, _ in medical_compounds]
        self.assertIn("bệnh viện", found_compounds, "Expected 'bệnh viện' to be detected")
        self.assertIn("sức khỏe", found_compounds, "Expected 'sức khỏe' to be detected")
        self.assertIn("tim mạch", found_compounds, "Expected 'tim mạch' to be detected")
        self.assertIn("ung thư", found_compounds, "Expected 'ung thư' to be detected")
        
        # Verify that non-medical compounds are not in the results
        self.assertNotIn("luật sư", found_compounds, "Did not expect 'luật sư' in medical domain results")
        
        # Test technology domain filtering
        tech_text = self.test_texts["technology"]
        tech_compounds = self.processor.filter_by_domain(tech_text, "technology")
        
        # Verify that technology compounds are detected
        found_tech_compounds = [compound for compound, _, _, _ in tech_compounds]
        self.assertIn("trí tuệ nhân tạo", found_tech_compounds, "Expected 'trí tuệ nhân tạo' to be detected")
        self.assertIn("học máy", found_tech_compounds, "Expected 'học máy' to be detected")
        self.assertIn("công nghệ", found_tech_compounds, "Expected 'công nghệ' to be detected")
        self.assertIn("phần mềm", found_tech_compounds, "Expected 'phần mềm' to be detected")
        self.assertIn("mã nguồn mở", found_tech_compounds, "Expected 'mã nguồn mở' to be detected")
        self.assertIn("cơ sở dữ liệu", found_tech_compounds, "Expected 'cơ sở dữ liệu' to be detected")
        self.assertIn("điện toán đám mây", found_tech_compounds, "Expected 'điện toán đám mây' to be detected")

    def test_detect_domain_specific_compounds(self):
        """Test detecting and categorizing compounds by domain."""
        # Create a mixed text with compounds from multiple domains
        mixed_text = ("Bệnh viện đang áp dụng trí tuệ nhân tạo trong chẩn đoán bệnh. "
                     "Công ty cổ phần vừa ký hợp đồng với trường đại học để phát triển "
                     "phần mềm xử lý ô nhiễm môi trường và tăng năng suất cây trồng.")
        
        # Detect domain-specific compounds
        domain_results = self.processor.detect_domain_specific_compounds(mixed_text)
        
        # Check that we have results for multiple domains
        self.assertGreater(len(domain_results), 3, "Expected results for multiple domains")
        
        # Check specific domains
        if "medical" in domain_results:
            medical_found = [compound for compound, _, _, _ in domain_results["medical"]]
            self.assertIn("bệnh viện", medical_found, "Expected 'bệnh viện' in medical results")
            
        if "technology" in domain_results:
            tech_found = [compound for compound, _, _, _ in domain_results["technology"]]
            self.assertIn("trí tuệ nhân tạo", tech_found, "Expected 'trí tuệ nhân tạo' in technology results")
            self.assertIn("phần mềm", tech_found, "Expected 'phần mềm' in technology results")
            
        if "economics" in domain_results:
            econ_found = [compound for compound, _, _, _ in domain_results["economics"]]
            self.assertIn("công ty cổ phần", econ_found, "Expected 'công ty cổ phần' in economics results")
            
        if "education" in domain_results:
            edu_found = [compound for compound, _, _, _ in domain_results["education"]]
            self.assertIn("trường đại học", edu_found, "Expected 'trường đại học' in education results")
            
        if "environment" in domain_results:
            env_found = [compound for compound, _, _, _ in domain_results["environment"]]
            self.assertIn("ô nhiễm môi trường", env_found, "Expected 'ô nhiễm môi trường' in environment results")
            
        if "agriculture" in domain_results:
            agri_found = [compound for compound, _, _, _ in domain_results["agriculture"]]
            self.assertIn("năng suất cây trồng", agri_found, "Expected 'năng suất cây trồng' in agriculture results")


if __name__ == '__main__':
    unittest.main() 