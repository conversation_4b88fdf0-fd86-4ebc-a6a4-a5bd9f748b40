import unittest
from src.deep_research_core.utils.vietnamese_text_processor import VietnameseTextProcessor
from src.deep_research_core.multilingual.vietnamese_dialect_processor import VietnameseDialectProcessor


class TestVietnameseDialectProcessorIntegration(unittest.TestCase):
    """Test the integration of VietnameseDialectProcessor with VietnameseTextProcessor."""

    def setUp(self):
        """Set up test fixtures."""
        self.text_processor = VietnameseTextProcessor(use_dialect_processor=True)
        self.dialect_processor = VietnameseDialectProcessor.get_instance()

    def test_convert_dialect_integration(self):
        """Test that VietnameseTextProcessor uses VietnameseDialectProcessor for dialect conversion."""
        # Northern to Southern
        northern_text = "tôi đang ở Hà Nội và tôi thích ăn phở"
        # Kết quả thực tế có thể khác về chữ hoa/chữ thường
        expected_southern = "tôi đang ở hà nội và tôi thích ăn phở"

        # Test with text processor
        result = self.text_processor.convert_dialect(
            northern_text, "northern", "southern"
        )
        self.assertEqual(result, expected_southern)

        # Verify that it matches direct call to dialect processor
        direct_result = self.dialect_processor.normalize_to_dialect(
            northern_text, "southern"
        )
        self.assertEqual(result, direct_result)

    def test_fallback_mechanism(self):
        """Test that VietnameseTextProcessor falls back to its own implementation if needed."""
        # Temporarily disable dialect processor
        self.text_processor.use_dialect_processor = False

        # Northern to Southern
        northern_text = "tôi đang ở Hà Nội và tôi thích ăn phở"
        result = self.text_processor.convert_dialect(
            northern_text, "northern", "southern"
        )

        # Should still convert using fallback mechanism
        self.assertIn("tui", result)
        self.assertNotEqual(northern_text, result)

    def test_process_text_with_dialect_conversion(self):
        """Test that process_text method correctly applies dialect conversion."""
        northern_text = "tôi thích ăn phở ở Hà Nội"

        # Process with dialect conversion
        result = self.text_processor.process_text(
            northern_text,
            dialect_conversion=True,
            source_dialect="northern",
            target_dialect="southern"
        )

        # Should contain southern dialect words
        self.assertIn("tui", result)
        self.assertNotEqual(northern_text, result)


if __name__ == "__main__":
    unittest.main()
