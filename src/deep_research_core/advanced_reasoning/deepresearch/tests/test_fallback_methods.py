"""
Test fallback methods for missing libraries.
"""

import unittest
import os
import sys
import tempfile
from unittest.mock import patch, MagicMock

# Add the src directory to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal
from src.deep_research_core.agents.web_search_agent_local_improvements import _handle_missing_libraries, _create_fallback_method


class TestFallbackMethods(unittest.TestCase):
    """Test fallback methods for missing libraries."""

    def setUp(self):
        """Set up the test."""
        self.agent = MagicMock()
        self.agent._extract_pdf_content = None
        self.agent._extract_docx_content = None
        self.agent._extract_pptx_content = None
        self.agent._extract_epub_content = None
        self.agent._extract_markdown_content = None
        self.agent._extract_pandoc_content = None
        self.agent._extract_pdf_content_advanced = None
        self.agent._extract_image_text = None
        self.agent._process_image = None
        self.agent._extract_html_content = None
        self.agent._render_javascript = None
        self.agent._process_text = None

    def test_create_fallback_method(self):
        """Test creating a fallback method."""
        # Create a fallback method
        fallback_method = _create_fallback_method(
            self.agent, 
            "test_lib", 
            "_extract_test_content",
            alternative_available=False,
            missing_dependencies=[]
        )

        # Test the fallback method
        result = fallback_method("test_url")
        
        # Check the result
        self.assertFalse(result["success"])
        self.assertEqual(result["error"], "Thư viện test_lib không khả dụng")
        self.assertEqual(result["content"], "")
        self.assertEqual(result["text"], "")
        self.assertEqual(result["title"], "Không thể trích xuất - test_lib không khả dụng")
        self.assertEqual(result["metadata"]["extracted_with"], "fallback")
        self.assertEqual(result["metadata"]["missing_library"], "test_lib")
        self.assertEqual(result["metadata"]["url_or_file"], "test_url")
        self.assertEqual(result["metadata"]["method"], "_extract_test_content")
        self.assertEqual(result["metadata"]["alternative_available"], False)
        self.assertEqual(result["metadata"]["missing_dependencies"], [])

    def test_create_fallback_method_with_alternative(self):
        """Test creating a fallback method with an alternative."""
        # Create a fallback method
        fallback_method = _create_fallback_method(
            self.agent, 
            "test_lib", 
            "_extract_test_content",
            alternative_available=True,
            missing_dependencies=[]
        )

        # Test the fallback method
        result = fallback_method("test_url")
        
        # Check the result
        self.assertFalse(result["success"])
        self.assertEqual(result["metadata"]["alternative_available"], True)

    def test_create_fallback_method_with_dependencies(self):
        """Test creating a fallback method with missing dependencies."""
        # Create a fallback method
        fallback_method = _create_fallback_method(
            self.agent, 
            "test_lib", 
            "_extract_test_content",
            alternative_available=False,
            missing_dependencies=["dep1", "dep2"]
        )

        # Test the fallback method
        result = fallback_method("test_url")
        
        # Check the result
        self.assertFalse(result["success"])
        self.assertEqual(result["metadata"]["missing_dependencies"], ["dep1", "dep2"])

    def test_handle_missing_libraries(self):
        """Test handling missing libraries."""
        # Mock the import function to always raise ImportError
        with patch('builtins.__import__', side_effect=ImportError):
            # Handle missing libraries
            _handle_missing_libraries(self.agent)
            
            # Check that fallback methods were created
            self.assertIsNotNone(self.agent._extract_pdf_content)
            self.assertIsNotNone(self.agent._extract_docx_content)
            self.assertIsNotNone(self.agent._extract_pptx_content)
            self.assertIsNotNone(self.agent._extract_epub_content)
            self.assertIsNotNone(self.agent._extract_markdown_content)
            self.assertIsNotNone(self.agent._extract_pandoc_content)
            self.assertIsNotNone(self.agent._extract_pdf_content_advanced)
            self.assertIsNotNone(self.agent._extract_image_text)
            self.assertIsNotNone(self.agent._process_image)
            self.assertIsNotNone(self.agent._extract_html_content)
            self.assertIsNotNone(self.agent._render_javascript)
            self.assertIsNotNone(self.agent._process_text)

    def test_fallback_method_with_file(self):
        """Test fallback method with a file."""
        # Create a temporary file
        with tempfile.NamedTemporaryFile(suffix='.pdf') as temp_file:
            # Create a fallback method
            fallback_method = _create_fallback_method(
                self.agent, 
                "PyPDF2", 
                "_extract_pdf_content",
                alternative_available=False,
                missing_dependencies=[]
            )

            # Test the fallback method with file_path
            result = fallback_method(file_path=temp_file.name)
            
            # Check the result
            self.assertFalse(result["success"])
            self.assertEqual(result["metadata"]["url_or_file"], temp_file.name)

    def test_fallback_method_for_process(self):
        """Test fallback method for process functions."""
        # Create a fallback method
        fallback_method = _create_fallback_method(
            self.agent, 
            "pillow", 
            "_process_image",
            alternative_available=False,
            missing_dependencies=[]
        )

        # Test the fallback method
        result = fallback_method("test_image.jpg")
        
        # Check the result
        self.assertIsNone(result)

    def test_fallback_method_for_detect(self):
        """Test fallback method for detect functions."""
        # Create a fallback method
        fallback_method = _create_fallback_method(
            self.agent, 
            "fasttext", 
            "_detect_language",
            alternative_available=False,
            missing_dependencies=[]
        )

        # Test the fallback method
        result = fallback_method("test text")
        
        # Check the result
        self.assertFalse(result)

    def test_fallback_method_for_convert(self):
        """Test fallback method for convert functions."""
        # Create a fallback method
        fallback_method = _create_fallback_method(
            self.agent, 
            "pandoc", 
            "_convert_markdown_to_html",
            alternative_available=False,
            missing_dependencies=[]
        )

        # Test the fallback method
        test_input = "# Test Markdown"
        result = fallback_method(test_input)
        
        # Check the result - should return the input
        self.assertEqual(result, test_input)


if __name__ == '__main__':
    unittest.main()
