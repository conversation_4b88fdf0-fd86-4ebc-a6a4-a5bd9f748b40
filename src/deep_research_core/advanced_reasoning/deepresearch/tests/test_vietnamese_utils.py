"""
Test module for vietnamese_utils.py
"""

import unittest
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from src.deep_research_core.utils.vietnamese_utils import (
    normalize_vietnamese_text,
    normalize_vietnamese_diacritics,
    detect_vietnamese,
    simple_vietnamese_tokenize,
    advanced_vietnamese_tokenize,
    remove_vietnamese_stopwords,
    extract_vietnamese_keywords,
    vietnamese_text_similarity,
    contains_vietnamese_compound_words,
    get_vietnamese_compound_words,
    get_vietnamese_special_tokens
)


class TestVietnameseUtils(unittest.TestCase):
    """Test cases for vietnamese_utils.py"""

    def test_normalize_vietnamese_text(self):
        """Test normalize_vietnamese_text function"""
        # Test with empty string
        self.assertEqual(normalize_vietnamese_text(""), "")
        
        # Test with normal text
        self.assertEqual(normalize_vietnamese_text("Hello world"), "Hello world")
        
        # Test with Vietnamese text
        self.assertEqual(normalize_vietnamese_text("Xin chào thế giới"), "Xin chào thế giới")
        
        # Test with Vietnamese text with non-standard diacritics
        self.assertEqual(normalize_vietnamese_text("Hoà bình"), "Hòa bình")
        self.assertEqual(normalize_vietnamese_text("Việt Nam"), "Việt Nam")
        
        # Test with mixed text
        self.assertEqual(normalize_vietnamese_text("Hello Việt Nam"), "Hello Việt Nam")
        
        # Test with text containing Telex input method remnants
        self.assertEqual(normalize_vietnamese_text("Vieejt Nam"), "Việt Nam")
        self.assertEqual(normalize_vietnamese_text("Hoaf binh"), "Hoà bình")

    def test_normalize_vietnamese_diacritics(self):
        """Test normalize_vietnamese_diacritics function"""
        # Test with empty string
        self.assertEqual(normalize_vietnamese_diacritics(""), "")
        
        # Test with normal text
        self.assertEqual(normalize_vietnamese_diacritics("Hello world"), "Hello world")
        
        # Test with Vietnamese text with non-standard diacritics
        self.assertEqual(normalize_vietnamese_diacritics("Hoà bình"), "Hòa bình")
        self.assertEqual(normalize_vietnamese_diacritics("Việt Nam"), "Việt Nam")
        
        # Test with text containing Telex input method remnants
        self.assertEqual(normalize_vietnamese_diacritics("Vieejt Nam"), "Việt Nam")
        self.assertEqual(normalize_vietnamese_diacritics("Hoaf binh"), "Hoà bình")
        
        # Test with uppercase text
        self.assertEqual(normalize_vietnamese_diacritics("HOÀ BÌNH"), "HÒA BÌNH")
        self.assertEqual(normalize_vietnamese_diacritics("VIỆT NAM"), "VIỆT NAM")

    def test_detect_vietnamese(self):
        """Test detect_vietnamese function"""
        # Test with empty string
        self.assertFalse(detect_vietnamese(""))
        
        # Test with non-Vietnamese text
        self.assertFalse(detect_vietnamese("Hello world"))
        self.assertFalse(detect_vietnamese("Bonjour le monde"))
        
        # Test with Vietnamese text
        self.assertTrue(detect_vietnamese("Xin chào thế giới"))
        self.assertTrue(detect_vietnamese("Việt Nam"))
        self.assertTrue(detect_vietnamese("Hòa bình và phát triển"))
        
        # Test with mixed text
        self.assertTrue(detect_vietnamese("Hello Việt Nam"))
        self.assertTrue(detect_vietnamese("Python và lập trình"))

    def test_simple_vietnamese_tokenize(self):
        """Test simple_vietnamese_tokenize function"""
        # Test with empty string
        self.assertEqual(simple_vietnamese_tokenize(""), [])
        
        # Test with normal text
        tokens = simple_vietnamese_tokenize("Xin chào thế giới")
        self.assertIsInstance(tokens, list)
        self.assertTrue(all(isinstance(token, str) for token in tokens))
        
        # Test with Vietnamese text containing compound words
        tokens = simple_vietnamese_tokenize("Hòa bình và phát triển")
        self.assertIn("Hòa bình", tokens)
        self.assertIn("phát triển", tokens)
        
        # Test with text containing punctuation
        tokens = simple_vietnamese_tokenize("Việt Nam, một đất nước tuyệt vời!")
        self.assertIn("Việt Nam", tokens)
        self.assertIn(",", tokens)
        self.assertIn("!", tokens)

    def test_advanced_vietnamese_tokenize(self):
        """Test advanced_vietnamese_tokenize function"""
        # Test with empty string
        self.assertEqual(advanced_vietnamese_tokenize(""), [])
        
        # Test with normal text
        tokens = advanced_vietnamese_tokenize("Xin chào thế giới")
        self.assertIsInstance(tokens, list)
        self.assertTrue(all(isinstance(token, str) for token in tokens))
        
        # Test with Vietnamese text containing compound words
        tokens = advanced_vietnamese_tokenize("Hòa bình và phát triển")
        self.assertIn("Hòa bình", tokens)
        self.assertIn("phát triển", tokens)
        
        # Test with complex Vietnamese text
        tokens = advanced_vietnamese_tokenize("Việt Nam là một quốc gia có nền văn hóa lâu đời")
        self.assertIn("Việt Nam", tokens)
        self.assertIn("quốc gia", tokens)
        self.assertIn("văn hóa", tokens)

    def test_remove_vietnamese_stopwords(self):
        """Test remove_vietnamese_stopwords function"""
        # Test with empty string
        self.assertEqual(remove_vietnamese_stopwords(""), "")
        
        # Test with text containing stopwords
        text = "Tôi là một người Việt Nam"
        filtered_text = remove_vietnamese_stopwords(text)
        self.assertNotIn("là", filtered_text)
        self.assertNotIn("một", filtered_text)
        self.assertIn("người", filtered_text)
        self.assertIn("Việt Nam", filtered_text)
        
        # Test with text without stopwords
        text = "Việt Nam đất nước"
        filtered_text = remove_vietnamese_stopwords(text)
        self.assertEqual(filtered_text, text)

    def test_extract_vietnamese_keywords(self):
        """Test extract_vietnamese_keywords function"""
        # Test with empty string
        self.assertEqual(extract_vietnamese_keywords(""), [])
        
        # Test with normal text
        text = "Việt Nam là một quốc gia có nền văn hóa lâu đời và con người thân thiện"
        keywords = extract_vietnamese_keywords(text, num_keywords=3)
        self.assertIsInstance(keywords, list)
        self.assertEqual(len(keywords), 3)
        self.assertTrue(all(isinstance(keyword, str) for keyword in keywords))
        
        # Test with num_keywords greater than number of words
        text = "Việt Nam đất nước"
        keywords = extract_vietnamese_keywords(text, num_keywords=10)
        self.assertLessEqual(len(keywords), 4)  # Maximum 4 words in the text

    def test_vietnamese_text_similarity(self):
        """Test vietnamese_text_similarity function"""
        # Test with identical texts
        text1 = "Việt Nam là một quốc gia tuyệt vời"
        text2 = "Việt Nam là một quốc gia tuyệt vời"
        similarity = vietnamese_text_similarity(text1, text2)
        self.assertEqual(similarity, 1.0)
        
        # Test with completely different texts
        text1 = "Việt Nam là một quốc gia tuyệt vời"
        text2 = "Python là ngôn ngữ lập trình phổ biến"
        similarity = vietnamese_text_similarity(text1, text2)
        self.assertLess(similarity, 0.5)
        
        # Test with similar texts
        text1 = "Việt Nam là một quốc gia tuyệt vời"
        text2 = "Việt Nam là đất nước tuyệt vời"
        similarity = vietnamese_text_similarity(text1, text2)
        self.assertGreater(similarity, 0.5)

    def test_contains_vietnamese_compound_words(self):
        """Test contains_vietnamese_compound_words function"""
        # Test with text containing compound words
        self.assertTrue(contains_vietnamese_compound_words("Hòa bình và phát triển"))
        self.assertTrue(contains_vietnamese_compound_words("Việt Nam là một quốc gia"))
        
        # Test with text without compound words
        self.assertFalse(contains_vietnamese_compound_words("Xin chào"))
        self.assertFalse(contains_vietnamese_compound_words("Hello world"))

    def test_get_vietnamese_compound_words(self):
        """Test get_vietnamese_compound_words function"""
        # Test without domain
        compound_words = get_vietnamese_compound_words()
        self.assertIsInstance(compound_words, list)
        self.assertTrue(all(isinstance(word, str) for word in compound_words))
        
        # Test with specific domain
        compound_words = get_vietnamese_compound_words(domain="general")
        self.assertIsInstance(compound_words, list)
        self.assertTrue(all(isinstance(word, str) for word in compound_words))

    def test_get_vietnamese_special_tokens(self):
        """Test get_vietnamese_special_tokens function"""
        special_tokens = get_vietnamese_special_tokens()
        self.assertIsInstance(special_tokens, list)
        self.assertTrue(all(isinstance(token, str) for token in special_tokens))


if __name__ == "__main__":
    unittest.main()
