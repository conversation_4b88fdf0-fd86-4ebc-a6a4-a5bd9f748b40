#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test trích xuất nội dung từ các định dạng file cho WebSearchAgentLocal.

Mo<PERSON>le này kiểm tra khả năng trích xuất nội dung từ các định dạng file khác nhau
của WebSearchAgentLocal, bao gồm PDF, DOCX, XLSX, PPTX, và các định dạng khác.
"""

import os
import sys
import time
import json
import unittest
import logging
from unittest.mock import patch, MagicMock
from typing import Dict, Any, List, Optional

# Thêm thư mục gốc vào sys.path để import các module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Import các module cần thiết
try:
    from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal
    from src.deep_research_core.agents.document_extractors import DocumentExtractor
    from src.deep_research_core.agents.document_extractors_extended import ExtendedDocumentExtractor
    from src.deep_research_core.agents.web_search_file_processor import WebSearchFileProcessor
except ImportError:
    try:
        # Thử import từ thư mục hiện tại
        from web_search_agent_local import WebSearchAgentLocal
        logger.warning("Importing from current directory. Some features may not be available.")
        
        # Định nghĩa các class giả nếu không import được
        class MockClass:
            def __init__(self, *args, **kwargs):
                pass
                
        DocumentExtractor = MockClass
        ExtendedDocumentExtractor = MockClass
        WebSearchFileProcessor = MockClass
    except ImportError:
        logger.error("Không thể import WebSearchAgentLocal. Vui lòng kiểm tra đường dẫn.")
        sys.exit(1)

# Hàm tiện ích
def print_section(title):
    """In tiêu đề phần."""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, "="))
    print("=" * 80)

def save_results_to_json(results, filename):
    """Lưu kết quả vào file JSON."""
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print(f"Đã lưu kết quả vào {filename}")

class TestWebSearchAgentLocalFileExtraction(unittest.TestCase):
    """Test trích xuất nội dung từ các định dạng file cho WebSearchAgentLocal."""
    
    @classmethod
    def setUpClass(cls):
        """Thiết lập trước khi chạy tất cả các test."""
        print_section("Thiết lập test trích xuất file WebSearchAgentLocal")
        
        # Tạo thư mục lưu kết quả nếu chưa tồn tại
        os.makedirs("test_results", exist_ok=True)
        
        # Khởi tạo WebSearchAgentLocal
        cls.agent = WebSearchAgentLocal(
            verbose=True,
            enable_file_download=True,
            file_download_config={
                "max_file_size": 10 * 1024 * 1024,  # 10MB
                "allowed_extensions": ["pdf", "docx", "xlsx", "pptx", "txt", "csv", "json", "xml"],
                "download_timeout": 30,
                "max_retries": 3
            }
        )
        
        # Danh sách URL file để test
        cls.file_urls = {
            "pdf": [
                "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf",
                "https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf"
            ],
            "docx": [
                "https://file-examples.com/wp-content/uploads/2017/02/file-sample_100kB.docx"
            ],
            "xlsx": [
                "https://file-examples.com/wp-content/uploads/2017/02/file_example_XLSX_10.xlsx"
            ],
            "pptx": [
                "https://file-examples.com/wp-content/uploads/2017/08/file_example_PPT_250kB.ppt"
            ],
            "txt": [
                "https://www.w3.org/TR/PNG/iso_8859-1.txt"
            ]
        }
    
    def setUp(self):
        """Thiết lập trước mỗi test."""
        # Đợi một chút giữa các test để tránh rate limit
        time.sleep(1)
    
    def test_01_document_extractor_initialization(self):
        """Test khởi tạo DocumentExtractor."""
        print_section("Test khởi tạo DocumentExtractor")
        
        # Khởi tạo DocumentExtractor
        try:
            extractor = DocumentExtractor()
            print(f"Đã khởi tạo DocumentExtractor thành công")
            
            # Kiểm tra các thuộc tính
            supported_formats = extractor.get_supported_formats()
            print(f"Các định dạng được hỗ trợ: {supported_formats}")
            
            # Kiểm tra kết quả
            self.assertIsInstance(supported_formats, list, "Danh sách định dạng được hỗ trợ không phải là list")
            self.assertGreater(len(supported_formats), 0, "Danh sách định dạng được hỗ trợ trống")
            
            # Lưu kết quả
            result = {
                "supported_formats": supported_formats
            }
            save_results_to_json(result, "test_results/document_extractor_initialization.json")
        except Exception as e:
            print(f"Lỗi khi khởi tạo DocumentExtractor: {str(e)}")
            self.skipTest(f"Lỗi khi khởi tạo DocumentExtractor: {str(e)}")
    
    def test_02_extended_document_extractor_initialization(self):
        """Test khởi tạo ExtendedDocumentExtractor."""
        print_section("Test khởi tạo ExtendedDocumentExtractor")
        
        # Khởi tạo ExtendedDocumentExtractor
        try:
            extractor = ExtendedDocumentExtractor()
            print(f"Đã khởi tạo ExtendedDocumentExtractor thành công")
            
            # Kiểm tra các thuộc tính
            supported_formats = extractor.get_supported_formats()
            print(f"Các định dạng được hỗ trợ: {supported_formats}")
            
            # Kiểm tra kết quả
            self.assertIsInstance(supported_formats, list, "Danh sách định dạng được hỗ trợ không phải là list")
            self.assertGreater(len(supported_formats), 0, "Danh sách định dạng được hỗ trợ trống")
            
            # Lưu kết quả
            result = {
                "supported_formats": supported_formats
            }
            save_results_to_json(result, "test_results/extended_document_extractor_initialization.json")
        except Exception as e:
            print(f"Lỗi khi khởi tạo ExtendedDocumentExtractor: {str(e)}")
            self.skipTest(f"Lỗi khi khởi tạo ExtendedDocumentExtractor: {str(e)}")
    
    def test_03_web_search_file_processor_initialization(self):
        """Test khởi tạo WebSearchFileProcessor."""
        print_section("Test khởi tạo WebSearchFileProcessor")
        
        # Khởi tạo WebSearchFileProcessor
        try:
            processor = WebSearchFileProcessor()
            print(f"Đã khởi tạo WebSearchFileProcessor thành công")
            
            # Kiểm tra các thuộc tính
            supported_extensions = processor.get_supported_extensions()
            print(f"Các phần mở rộng được hỗ trợ: {supported_extensions}")
            
            # Kiểm tra kết quả
            self.assertIsInstance(supported_extensions, list, "Danh sách phần mở rộng được hỗ trợ không phải là list")
            self.assertGreater(len(supported_extensions), 0, "Danh sách phần mở rộng được hỗ trợ trống")
            
            # Lưu kết quả
            result = {
                "supported_extensions": supported_extensions
            }
            save_results_to_json(result, "test_results/web_search_file_processor_initialization.json")
        except Exception as e:
            print(f"Lỗi khi khởi tạo WebSearchFileProcessor: {str(e)}")
            self.skipTest(f"Lỗi khi khởi tạo WebSearchFileProcessor: {str(e)}")
    
    def test_04_extract_pdf_content(self):
        """Test trích xuất nội dung từ file PDF."""
        print_section("Test trích xuất nội dung từ file PDF")
        
        # Chọn URL PDF
        if not self.file_urls.get("pdf"):
            self.skipTest("Không có URL PDF để test")
        
        url = self.file_urls["pdf"][0]
        print(f"URL PDF: {url}")
        
        # Trích xuất nội dung
        if hasattr(self.agent, 'extract_file_content'):
            result = self.agent.extract_file_content(url)
            
            # Kiểm tra kết quả
            self.assertTrue(result.get('success', False), f"Trích xuất nội dung từ {url} không thành công")
            self.assertTrue(result.get('text', ''), "Nội dung trích xuất trống")
            
            # In kết quả
            print(f"Tiêu đề: {result.get('title', 'N/A')}")
            text = result.get('text', '')
            print(f"Nội dung: {text[:200]}..." if len(text) > 200 else f"Nội dung: {text}")
            
            # Lưu kết quả
            save_results_to_json(result, "test_results/extract_pdf_content.json")
        else:
            print("Phương thức extract_file_content không khả dụng")
            
            # Thử sử dụng DocumentExtractor trực tiếp
            try:
                extractor = DocumentExtractor()
                result = extractor.extract_from_url(url)
                
                # Kiểm tra kết quả
                self.assertTrue(result.get('success', False), f"Trích xuất nội dung từ {url} không thành công")
                self.assertTrue(result.get('text', ''), "Nội dung trích xuất trống")
                
                # In kết quả
                print(f"Tiêu đề: {result.get('title', 'N/A')}")
                text = result.get('text', '')
                print(f"Nội dung: {text[:200]}..." if len(text) > 200 else f"Nội dung: {text}")
                
                # Lưu kết quả
                save_results_to_json(result, "test_results/extract_pdf_content_direct.json")
            except Exception as e:
                print(f"Lỗi khi trích xuất nội dung PDF trực tiếp: {str(e)}")
                self.skipTest(f"Lỗi khi trích xuất nội dung PDF: {str(e)}")
    
    def test_05_extract_docx_content(self):
        """Test trích xuất nội dung từ file DOCX."""
        print_section("Test trích xuất nội dung từ file DOCX")
        
        # Chọn URL DOCX
        if not self.file_urls.get("docx"):
            self.skipTest("Không có URL DOCX để test")
        
        url = self.file_urls["docx"][0]
        print(f"URL DOCX: {url}")
        
        # Trích xuất nội dung
        if hasattr(self.agent, 'extract_file_content'):
            result = self.agent.extract_file_content(url)
            
            # Kiểm tra kết quả
            self.assertTrue(result.get('success', False), f"Trích xuất nội dung từ {url} không thành công")
            self.assertTrue(result.get('text', ''), "Nội dung trích xuất trống")
            
            # In kết quả
            print(f"Tiêu đề: {result.get('title', 'N/A')}")
            text = result.get('text', '')
            print(f"Nội dung: {text[:200]}..." if len(text) > 200 else f"Nội dung: {text}")
            
            # Lưu kết quả
            save_results_to_json(result, "test_results/extract_docx_content.json")
        else:
            print("Phương thức extract_file_content không khả dụng")
            
            # Thử sử dụng DocumentExtractor trực tiếp
            try:
                extractor = DocumentExtractor()
                result = extractor.extract_from_url(url)
                
                # Kiểm tra kết quả
                self.assertTrue(result.get('success', False), f"Trích xuất nội dung từ {url} không thành công")
                self.assertTrue(result.get('text', ''), "Nội dung trích xuất trống")
                
                # In kết quả
                print(f"Tiêu đề: {result.get('title', 'N/A')}")
                text = result.get('text', '')
                print(f"Nội dung: {text[:200]}..." if len(text) > 200 else f"Nội dung: {text}")
                
                # Lưu kết quả
                save_results_to_json(result, "test_results/extract_docx_content_direct.json")
            except Exception as e:
                print(f"Lỗi khi trích xuất nội dung DOCX trực tiếp: {str(e)}")
                self.skipTest(f"Lỗi khi trích xuất nội dung DOCX: {str(e)}")
    
    def test_06_search_with_file_extraction(self):
        """Test tìm kiếm với trích xuất nội dung từ file."""
        print_section("Test tìm kiếm với trích xuất nội dung từ file")
        
        # Truy vấn liên quan đến file
        query = "sample PDF document"
        print(f"Truy vấn: {query}")
        
        # Thực hiện tìm kiếm
        result = self.agent.search(
            query,
            num_results=5,
            get_content=True,
            enable_file_extraction=True
        )
        
        # Kiểm tra kết quả
        self.assertTrue(result.get('success', False), "Tìm kiếm với trích xuất nội dung từ file không thành công")
        self.assertGreater(len(result.get('results', [])), 0, "Không có kết quả tìm kiếm")
        
        # Kiểm tra xem có kết quả nào là file không
        has_file_result = False
        for item in result.get('results', []):
            url = item.get('url', '')
            if url and any(url.lower().endswith(f".{ext}") for ext in ["pdf", "docx", "xlsx", "pptx", "txt"]):
                has_file_result = True
                print(f"Tìm thấy kết quả file: {url}")
                print(f"Tiêu đề: {item.get('title', 'N/A')}")
                content = item.get('content', '')
                print(f"Nội dung: {content[:200]}..." if len(content) > 200 else f"Nội dung: {content}")
                break
        
        # Không fail test nếu không tìm thấy kết quả file
        if not has_file_result:
            print("Không tìm thấy kết quả file trong kết quả tìm kiếm")
        
        # Lưu kết quả
        save_results_to_json(result, "test_results/search_with_file_extraction.json")
    
    @classmethod
    def tearDownClass(cls):
        """Dọn dẹp sau khi chạy tất cả các test."""
        print_section("Dọn dẹp sau khi chạy tất cả các test")
        
        # Dọn dẹp tài nguyên
        pass

if __name__ == "__main__":
    unittest.main()
