"""
Unit tests for ResourceManager.
"""

import os
import sys
import unittest
import time
import threading
from unittest.mock import patch, MagicMock

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# Import ResourceManager
try:
    from deepresearch.src.deep_research_core.utils.resource_manager import (
        ResourceManager,
    )

    resource_manager_available = True
except ImportError:
    resource_manager_available = False


@unittest.skipIf(not resource_manager_available, "ResourceManager not available")
class TestResourceManager(unittest.TestCase):
    """Test cases for ResourceManager."""

    def setUp(self):
        """Set up test fixtures."""
        self.resource_manager = ResourceManager(
            max_cpu_percent=80.0,
            max_memory_percent=80.0,
            max_connections=100,
            check_interval=0.1,  # Shorter interval for testing
            enable_auto_scaling=True,
            enable_monitoring=False,  # Disable monitoring for testing
            thread_pool_size=2,
            verbose=False,
        )

    def tearDown(self):
        """Clean up test fixtures."""
        if hasattr(self.resource_manager, "thread_pool"):
            self.resource_manager.thread_pool.shutdown(wait=False)

    def test_init(self):
        """Test initialization."""
        self.assertEqual(self.resource_manager.max_cpu_percent, 80.0)
        self.assertEqual(self.resource_manager.max_memory_percent, 80.0)
        self.assertEqual(self.resource_manager.max_connections, 100)
        self.assertEqual(self.resource_manager.check_interval, 0.1)
        self.assertEqual(self.resource_manager.enable_auto_scaling, True)
        self.assertEqual(self.resource_manager.thread_pool_size, 2)

    @patch("psutil.cpu_percent")
    @patch("psutil.virtual_memory")
    def test_update_resource_info(self, mock_virtual_memory, mock_cpu_percent):
        """Test update_resource_info method."""
        # Mock CPU and memory info
        mock_cpu_percent.return_value = 50.0
        mock_memory = MagicMock()
        mock_memory.percent = 60.0
        mock_virtual_memory.return_value = mock_memory

        # Update resource info
        self.resource_manager.update_resource_info()

        # Check values
        self.assertEqual(self.resource_manager.current_cpu_percent, 50.0)
        self.assertEqual(self.resource_manager.current_memory_percent, 60.0)

    @patch("psutil.cpu_percent")
    @patch("psutil.virtual_memory")
    def test_check_and_adjust_resources(self, mock_virtual_memory, mock_cpu_percent):
        """Test _check_and_adjust_resources method."""
        # Mock CPU and memory info
        mock_cpu_percent.return_value = 90.0  # High CPU
        mock_memory = MagicMock()
        mock_memory.percent = 50.0  # Normal memory
        mock_virtual_memory.return_value = mock_memory

        # Update resource info
        self.resource_manager.update_resource_info()

        # Check and adjust resources
        self.resource_manager._check_and_adjust_resources()

        # Check throttle level
        self.assertTrue(self.resource_manager.is_throttling)
        self.assertGreaterEqual(
            self.resource_manager.throttle_level, 2
        )  # Should be at least 2 due to high CPU

    @patch("psutil.cpu_percent")
    @patch("psutil.virtual_memory")
    def test_free_memory(self, mock_virtual_memory, mock_cpu_percent):
        """Test _free_memory method."""
        # Mock memory info
        mock_memory1 = MagicMock()
        mock_memory1.percent = 90.0  # High memory
        mock_memory2 = MagicMock()
        mock_memory2.percent = 80.0  # After GC
        mock_virtual_memory.side_effect = [mock_memory1, mock_memory2]

        # Set current memory percent
        self.resource_manager.current_memory_percent = 90.0

        # Free memory
        with patch("gc.collect") as mock_gc_collect:
            self.resource_manager._free_memory()
            mock_gc_collect.assert_called_once()

        # Check memory percent
        self.assertEqual(self.resource_manager.current_memory_percent, 80.0)

    def test_should_throttle(self):
        """Test should_throttle method."""
        # Not throttling
        self.resource_manager.is_throttling = False
        self.assertFalse(self.resource_manager.should_throttle())

        # Throttling
        self.resource_manager.is_throttling = True
        self.assertTrue(self.resource_manager.should_throttle())

    def test_get_throttle_delay(self):
        """Test get_throttle_delay method."""
        # Not throttling
        self.resource_manager.is_throttling = False
        self.assertEqual(self.resource_manager.get_throttle_delay(), 0.0)

        # Throttling level 1
        self.resource_manager.is_throttling = True
        self.resource_manager.throttle_level = 1
        self.assertGreater(self.resource_manager.get_throttle_delay(), 0.0)

        # Throttling level 2
        self.resource_manager.throttle_level = 2
        self.assertGreater(self.resource_manager.get_throttle_delay(), 0.0)

        # Throttling level 3
        self.resource_manager.throttle_level = 3
        self.assertGreater(self.resource_manager.get_throttle_delay(), 0.0)

    def test_execute_with_resource_control(self):
        """Test execute_with_resource_control method."""

        # Create a test function
        def test_func(x, y):
            return x + y

        # Not throttling
        self.resource_manager.is_throttling = False
        result = self.resource_manager.execute_with_resource_control(test_func, 1, 2)
        self.assertEqual(result, 3)

        # Throttling
        self.resource_manager.is_throttling = True
        self.resource_manager.throttle_level = 1
        with patch("time.sleep") as mock_sleep:
            result = self.resource_manager.execute_with_resource_control(
                test_func, 3, 4
            )
            mock_sleep.assert_called_once()
            self.assertEqual(result, 7)

    @unittest.skipIf(
        sys.platform != "darwin" and sys.platform != "linux",
        "Test only runs on macOS and Linux",
    )
    def test_get_stats(self):
        """Test get_stats method."""
        # Get stats
        stats = self.resource_manager.get_stats()

        # Check stats
        self.assertIn("current", stats)
        self.assertIn("limits", stats)
        self.assertIn("history", stats)
        self.assertIn("system_info", stats)

        # Check current stats
        self.assertIn("cpu_percent", stats["current"])
        self.assertIn("memory_percent", stats["current"])
        self.assertIn("connections", stats["current"])
        self.assertIn("throttle_level", stats["current"])
        self.assertIn("is_throttling", stats["current"])

        # Check limits
        self.assertEqual(stats["limits"]["max_cpu_percent"], 80.0)
        self.assertEqual(stats["limits"]["max_memory_percent"], 80.0)
        self.assertEqual(stats["limits"]["max_connections"], 100)

    @patch("threading.Thread")
    def test_start_monitoring(self, mock_thread):
        """Test start_monitoring method."""
        # Start monitoring
        self.resource_manager.start_monitoring()

        # Check thread
        mock_thread.assert_called_once()
        mock_thread.return_value.start.assert_called_once()

    @patch("threading.Thread")
    def test_stop_monitoring(self, mock_thread):
        """Test stop_monitoring method."""
        # Create a mock thread
        mock_thread_instance = MagicMock()
        mock_thread.return_value = mock_thread_instance
        self.resource_manager.monitoring_thread = mock_thread_instance
        self.resource_manager.monitoring_thread.is_alive.return_value = True

        # Stop monitoring
        self.resource_manager.stop_monitoring()

        # Check thread
        self.assertTrue(self.resource_manager.stop_monitoring.is_set())
        mock_thread_instance.join.assert_called_once()


if __name__ == "__main__":
    unittest.main()
