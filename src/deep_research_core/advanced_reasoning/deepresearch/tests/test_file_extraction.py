import os
import sys
import unittest
from unittest.mock import MagicMock, patch
import tempfile
import shutil

# Thêm đường dẫn thư mục gốc vào sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.agents.adaptive_crawler import AdaptiveCrawler
from src.deep_research_core.agents.document_extractors import DocumentExtractor


class TestFileExtraction(unittest.TestCase):
    """
    Test case cho tính năng trích xuất nội dung file trong AdaptiveCrawler
    """

    def setUp(self):
        """
        Thiết lập môi trường test
        """
        # Tạo thư mục tạm để lưu file
        self.temp_dir = tempfile.mkdtemp()

        # Tạo AdaptiveCrawler với extract_file_content=True
        self.crawler = AdaptiveCrawler(
            download_path=self.temp_dir,
            download_media=True,
            extract_file_content=True
        )

        # Tạo một file PDF giả lập để test
        self.pdf_content = b"%PDF-1.4\n1 0 obj\n<</Type/Catalog/Pages 2 0 R>>\nendobj\n2 0 obj\n<</Type/Pages/Kids[3 0 R]/Count 1>>\nendobj\n3 0 obj\n<</Type/Page/MediaBox[0 0 595 842]/Parent 2 0 R/Resources<<>>>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000010 00000 n \n0000000053 00000 n \n0000000102 00000 n \ntrailer\n<</Size 4/Root 1 0 R>>\nstartxref\n178\n%%EOF"
        self.pdf_path = os.path.join(self.temp_dir, "test.pdf")
        with open(self.pdf_path, "wb") as f:
            f.write(self.pdf_content)

        # Tạo một file text giả lập để test
        self.text_content = "Đây là nội dung file text để test tính năng trích xuất nội dung."
        self.text_path = os.path.join(self.temp_dir, "test.txt")
        with open(self.text_path, "w", encoding="utf-8") as f:
            f.write(self.text_content)

    def tearDown(self):
        """
        Dọn dẹp sau khi test
        """
        # Xóa thư mục tạm
        shutil.rmtree(self.temp_dir)

    def test_extract_file_content(self):
        """
        Test phương thức _extract_file_content
        """
        # Test với file text
        result_text = self.crawler._extract_file_content(self.text_path, mime_type="text/plain")
        self.assertTrue(result_text["success"])
        self.assertEqual(result_text["text"], self.text_content)

        # Test với file PDF
        with patch.object(DocumentExtractor, 'extract_text', return_value={"success": True, "text": "PDF content"}):
            result_pdf = self.crawler._extract_file_content(self.pdf_path, mime_type="application/pdf")
            self.assertTrue(result_pdf["success"])
            self.assertEqual(result_pdf["text"], "PDF content")

        # Test với file không tồn tại
        result_not_exist = self.crawler._extract_file_content("not_exist.txt")
        self.assertFalse(result_not_exist["success"])
        self.assertIn("không tồn tại", result_not_exist["error"])

        # Test khi document_extractor là None
        self.crawler.document_extractor = None
        result_no_extractor = self.crawler._extract_file_content(self.text_path)
        self.assertFalse(result_no_extractor["success"])
        self.assertIn("Document extractor không được khởi tạo", result_no_extractor["error"])

    def test_download_files_with_extraction(self):
        """
        Test tính năng trích xuất nội dung khi tải file
        """
        # Tạo danh sách file giả lập
        files = [
            {
                "url": "http://example.com/test.txt",
                "filename": "test.txt",
                "text": "Test file",
                "type": "txt"
            }
        ]

        # Mock phương thức requests.get để trả về nội dung file text
        with patch('requests.get') as mock_get, \
             patch.object(DocumentExtractor, 'extract_text',
                         return_value={"success": True, "text": self.text_content}):
            mock_response = MagicMock()
            mock_response.headers = {'content-type': 'text/plain'}
            mock_response.iter_content.return_value = [self.text_content.encode('utf-8')]
            mock_get.return_value = mock_response

            # Gọi phương thức _download_files
            self.crawler._download_files(files)

            # Kiểm tra kết quả
            self.assertTrue(files[0]['downloaded'])
            self.assertTrue(files[0]['content_extraction_success'])
            self.assertEqual(files[0]['extracted_content'], self.text_content)


if __name__ == '__main__':
    unittest.main()
