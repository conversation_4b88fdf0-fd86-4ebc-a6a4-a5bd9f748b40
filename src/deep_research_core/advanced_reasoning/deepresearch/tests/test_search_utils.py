"""
Test the search utility functions.
"""

import unittest
from unittest.mock import patch, MagicMock

import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.utils.search_utils import (
    detect_language,
    is_vietnamese,
    format_search_results,
    merge_search_results
)


class TestSearchUtils(unittest.TestCase):
    """Test the search utility functions."""

    def test_detect_language(self):
        """Test language detection."""
        # Test English detection
        self.assertEqual(detect_language("Hello world", "unknown"), "en")
        
        # Test with default fallback
        with patch('src.deep_research_core.utils.search_utils.detect', side_effect=Exception("Test error")):
            self.assertEqual(detect_language("Some text", "fallback"), "fallback")

    def test_is_vietnamese(self):
        """Test Vietnamese detection."""
        # Test Vietnamese text
        self.assertTrue(is_vietnamese("Xin chào thế giới"))
        
        # Test non-Vietnamese text
        self.assertFalse(is_vietnamese("Hello world"))
        
        # Test with exception
        with patch('src.deep_research_core.utils.search_utils.detect', side_effect=Exception("Test error")):
            # Should fall back to character-based detection
            self.assertTrue(is_vietnamese("Xin chào"))
            self.assertFalse(is_vietnamese("Hello"))

    def test_format_search_results(self):
        """Test search result formatting."""
        # Test successful results
        results = [{"title": "Test", "url": "http://example.com"}]
        formatted = format_search_results(
            query="test query",
            results=results,
            engine="test_engine",
            search_method="test_method"
        )
        
        self.assertTrue(formatted["success"])
        self.assertEqual(formatted["query"], "test query")
        self.assertEqual(formatted["engine"], "test_engine")
        self.assertEqual(formatted["search_method"], "test_method")
        self.assertEqual(formatted["results"], results)
        self.assertIn("timestamp", formatted)
        
        # Test error results
        error_formatted = format_search_results(
            query="test query",
            results=[],
            engine="test_engine",
            search_method="test_method",
            success=False,
            error="Test error",
            detailed_errors={"engine1": "Error 1"}
        )
        
        self.assertFalse(error_formatted["success"])
        self.assertEqual(error_formatted["error"], "Test error")
        self.assertEqual(error_formatted["detailed_errors"], {"engine1": "Error 1"})
        
        # Test with additional data
        additional_formatted = format_search_results(
            query="test query",
            results=results,
            engine="test_engine",
            search_method="test_method",
            additional_data={"extra_field": "extra_value"}
        )
        
        self.assertEqual(additional_formatted["extra_field"], "extra_value")

    def test_merge_search_results(self):
        """Test merging search results."""
        # Test merging multiple result sets
        result1 = {
            "success": True,
            "query": "test",
            "engine": "engine1",
            "results": [
                {"title": "Result 1", "url": "http://example.com/1"},
                {"title": "Result 2", "url": "http://example.com/2"}
            ]
        }
        
        result2 = {
            "success": True,
            "query": "test",
            "engine": "engine2",
            "results": [
                {"title": "Result 3", "url": "http://example.com/3"},
                {"title": "Result 2", "url": "http://example.com/2"}  # Duplicate
            ]
        }
        
        merged = merge_search_results([result1, result2])
        
        self.assertTrue(merged["success"])
        self.assertEqual(len(merged["results"]), 3)  # Should remove duplicate
        self.assertTrue(merged["merged"])
        self.assertEqual(merged["merged_count"], 2)
        
        # Test with empty results
        empty_merged = merge_search_results([])
        self.assertFalse(empty_merged["success"])
        self.assertEqual(empty_merged["error"], "No search results to merge")
        
        # Test with all failed results
        failed1 = {"success": False, "error": "Error 1", "results": []}
        failed2 = {"success": False, "error": "Error 2", "results": []}
        
        failed_merged = merge_search_results([failed1, failed2])
        self.assertFalse(failed_merged["success"])
        self.assertEqual(failed_merged["error"], "No results found after merging")


if __name__ == '__main__':
    unittest.main()
