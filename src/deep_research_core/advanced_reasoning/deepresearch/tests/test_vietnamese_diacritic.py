"""Tests for the Vietnamese diacritic processor."""

import unittest
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../')))

from src.deep_research_core.multilingual.vietnamese_diacritic_processor import VietnameseDiacriticProcessor


class TestVietnameseDiacriticProcessor(unittest.TestCase):
    """Test cases for Vietnamese diacritic processor."""

    def setUp(self):
        """Set up test environment."""
        self.processor = VietnameseDiacriticProcessor.get_instance()
        
        # Test samples
        self.text_with_diacritics = "Tiếng Việt có năm thanh điệu khác nhau"
        self.text_without_diacritics = "Tieng Viet co nam thanh dieu khac nhau"
        self.text_with_mixed_diacritics = "Tiếng Viet có nam thanh điệu khác nhau"
        self.text_telex = "Tie61ng Vie65t co1 na8m thanh ddie65u kha1c nhau"
        
    def test_remove_diacritics(self):
        """Test removing diacritics from Vietnamese text."""
        result = self.processor.remove_diacritics(self.text_with_diacritics)
        self.assertEqual(result, self.text_without_diacritics)
        
        # Test with already non-diacritic text
        result2 = self.processor.remove_diacritics(self.text_without_diacritics)
        self.assertEqual(result2, self.text_without_diacritics)
        
        # Test with empty string
        self.assertEqual(self.processor.remove_diacritics(""), "")
        
    def test_normalize_diacritics(self):
        """Test normalizing diacritics in Vietnamese text."""
        # Create a denormalized text
        denormalized = "Ti\u0065\u0301ng Vi\u0065\u0302t co\u0301 na\u0306m thanh đi\u0065\u0302\u0301u kha\u0301c nhau"
        
        result = self.processor.normalize_diacritics(denormalized)
        self.assertEqual(result, self.text_with_diacritics)
        
        # Test with empty string
        self.assertEqual(self.processor.normalize_diacritics(""), "")
        
    def test_has_vietnamese_diacritics(self):
        """Test checking if text has Vietnamese diacritics."""
        self.assertTrue(self.processor.has_vietnamese_diacritics(self.text_with_diacritics))
        self.assertFalse(self.processor.has_vietnamese_diacritics(self.text_without_diacritics))
        self.assertTrue(self.processor.has_vietnamese_diacritics(self.text_with_mixed_diacritics))
        
        # Test with empty string
        self.assertFalse(self.processor.has_vietnamese_diacritics(""))
        
        # Test with non-Vietnamese text
        self.assertFalse(self.processor.has_vietnamese_diacritics("Hello world"))
        
    def test_classify_diacritic_consistency(self):
        """Test classifying diacritic consistency in Vietnamese text."""
        # Test with fully diacritical text
        result1 = self.processor.classify_diacritic_consistency(self.text_with_diacritics)
        self.assertTrue(result1["has_diacritics"])
        self.assertTrue(result1["is_consistent"])
        self.assertEqual(result1["consistency"], 1.0)
        
        # Test with non-diacritical text
        result2 = self.processor.classify_diacritic_consistency(self.text_without_diacritics)
        self.assertFalse(result2["has_diacritics"])
        self.assertTrue(result2["is_consistent"])
        self.assertEqual(result2["consistency"], 1.0)
        
        # Test with mixed diacritical text
        result3 = self.processor.classify_diacritic_consistency(self.text_with_mixed_diacritics)
        self.assertTrue(result3["has_diacritics"])
        self.assertFalse(result3["is_consistent"])
        self.assertLess(result3["consistency"], 1.0)
        
        # Test with empty string
        result4 = self.processor.classify_diacritic_consistency("")
        self.assertFalse(result4["has_diacritics"])
        self.assertEqual(result4["consistency"], 1.0)
        
    def test_get_diacritic_info(self):
        """Test getting diacritic information for Vietnamese characters."""
        # Test with non-diacritic character
        result1 = self.processor.get_diacritic_info("a")
        self.assertEqual(result1["base"], "a")
        self.assertFalse(result1["has_diacritics"])
        self.assertIsNone(result1["tone"])
        self.assertIsNone(result1["accent"])
        
        # Test with diacritic character
        result2 = self.processor.get_diacritic_info("ế")
        self.assertEqual(result2["base"], "e")
        self.assertTrue(result2["has_diacritics"])
        self.assertEqual(result2["tone_name"], "sắc")
        self.assertEqual(result2["accent_name"], "circumflex")
        
        # Test with special character đ
        result3 = self.processor.get_diacritic_info("đ")
        self.assertEqual(result3["base"], "d")
        self.assertTrue(result3["has_diacritics"])
        
        # Test with non-Vietnamese character
        result4 = self.processor.get_diacritic_info("z")
        self.assertEqual(result4["base"], "z")
        self.assertFalse(result4["has_diacritics"])
        
    def test_analyze_diacritics(self):
        """Test analyzing diacritics in Vietnamese text."""
        result = self.processor.analyze_diacritics(self.text_with_diacritics)
        
        self.assertTrue(result["has_diacritics"])
        self.assertEqual(result["consistency"], 1.0)
        self.assertGreater(result["diacritic_chars"], 0)
        self.assertGreater(result["total_vietnamese_chars"], 0)
        self.assertGreater(result["percentage_with_diacritics"], 0.0)
        
        # Check tone distribution
        self.assertIn("ngang", result["tone_distribution"])
        self.assertIn("sắc", result["tone_distribution"])
        
        # Check accent distribution
        self.assertIn("none", result["accent_distribution"])
        self.assertIn("circumflex", result["accent_distribution"])
        
    def test_telex_conversion(self):
        """Test converting TELEX input to Vietnamese with diacritics."""
        # Test simple TELEX conversions
        self.assertEqual(self.processor.convert_telex_to_vietnamese("anh"), "anh")
        self.assertEqual(self.processor.convert_telex_to_vietnamese("af"), "à")
        self.assertEqual(self.processor.convert_telex_to_vietnamese("as"), "á")
        self.assertEqual(self.processor.convert_telex_to_vietnamese("ar"), "ả")
        self.assertEqual(self.processor.convert_telex_to_vietnamese("ax"), "ã")
        self.assertEqual(self.processor.convert_telex_to_vietnamese("aj"), "ạ")
        
        # Test with accents
        self.assertEqual(self.processor.convert_telex_to_vietnamese("aw"), "ă")
        self.assertEqual(self.processor.convert_telex_to_vietnamese("aa"), "â")
        self.assertEqual(self.processor.convert_telex_to_vietnamese("ow"), "ơ")
        self.assertEqual(self.processor.convert_telex_to_vietnamese("oo"), "ô")
        self.assertEqual(self.processor.convert_telex_to_vietnamese("uw"), "ư")
        
        # Test with combined marks
        self.assertEqual(self.processor.convert_telex_to_vietnamese("aas"), "ấ")
        self.assertEqual(self.processor.convert_telex_to_vietnamese("awr"), "ẳ")
        
        # Test with đ
        self.assertEqual(self.processor.convert_telex_to_vietnamese("dd"), "đ")
        
        # Test case preservation
        self.assertEqual(self.processor.convert_telex_to_vietnamese("Af"), "À")
        self.assertEqual(self.processor.convert_telex_to_vietnamese("DD"), "Đ")


if __name__ == "__main__":
    unittest.main() 