# Tests for Deep Research Core

This directory contains tests for the Deep Research Core project.

## Directory Structure

```
tests/
├── unit/                  # Unit tests
│   ├── agents/            # Tests for agents
│   ├── optimization/      # Tests for optimization modules
│   │   └── caching/       # Tests for caching modules
│   ├── reasoning/         # Tests for reasoning module
│   │   ├── tot/           # Tests for Tree of Thought
│   │   ├── combined/      # Tests for combined reasoning methods
│   │   └── ...
│   ├── retrieval/         # Tests for retrieval module
│   └── utils/             # Tests for utilities
├── integration/           # Integration tests
│   ├── reasoning/         # Integration tests for reasoning
│   └── test_search_cache_integration.py  # Integration tests for search cache
└── performance/           # Performance tests
    └── test_cache_performance.py  # Performance tests for cache
```

## Running Tests

You can run tests using the `run_tests.py` script in the project root:

```bash
# Run all tests
python run_tests.py

# Run unit tests only
python run_tests.py --unit

# Run integration tests only
python run_tests.py --integration

# Run tests for a specific module
python run_tests.py --module reasoning

# Run unit tests for a specific module
python run_tests.py --unit --module reasoning

# Run with verbose output
python run_tests.py -v
```

Alternatively, you can use pytest directly:

```bash
# Run all tests
pytest

# Run unit tests only
pytest tests/unit

# Run tests for a specific module
pytest tests/unit/reasoning

# Run a specific test file
pytest tests/unit/reasoning/tot/test_tot_basic.py
```

### Running Cache Tests

For the new caching system, you can run the following tests:

```bash
# Run unit tests for AdaptiveSearchCache
pytest tests/unit/optimization/caching/test_adaptive_search_cache.py
pytest tests/unit/optimization/caching/test_adaptive_search_cache_detailed.py

# Run unit tests for CachedWebSearchAgent
pytest tests/unit/agents/test_web_search_agent_cache.py
pytest tests/unit/agents/test_web_search_agent_cache_detailed.py

# Run integration tests for search cache
pytest tests/integration/test_search_cache_integration.py

# Run performance tests for cache
python tests/performance/test_cache_performance.py --benchmark all --queries 1000 --unique 100 --similarity 0.2 --semantic --delay 0.1 --output results.json
```

### Performance Test Options

The cache performance tests support the following options:

- `--benchmark`: Type of benchmark (`adaptive`, `agent`, or `all`)
- `--queries`: Total number of queries
- `--unique`: Number of unique queries
- `--similarity`: Ratio of similar queries (0.0 - 1.0)
- `--semantic`: Enable semantic search
- `--delay`: Simulated delay in seconds
- `--output`: Output file (JSON)

## Writing Tests

When writing tests, follow these guidelines:

1. **Test Structure**: Use the `unittest` framework and organize tests into classes.
2. **Test Naming**: Name test files with the prefix `test_` and test methods with the prefix `test_`.
3. **Mocking**: Use `unittest.mock` for mocking external dependencies.
4. **Setup and Teardown**: Use `setUp` and `tearDown` methods for common setup and cleanup.
5. **Assertions**: Use appropriate assertions for different types of checks.

Example:

```python
import unittest
from unittest.mock import patch, MagicMock

class TestMyFeature(unittest.TestCase):
    def setUp(self):
        # Setup code
        pass

    def tearDown(self):
        # Cleanup code
        pass

    def test_some_functionality(self):
        # Test code
        self.assertEqual(expected, actual)

    @patch('module.function')
    def test_with_mock(self, mock_function):
        # Configure mock
        mock_function.return_value = "mocked result"

        # Test code
        result = function_under_test()

        # Assertions
        self.assertEqual("mocked result", result)
        mock_function.assert_called_once()
```

## Common Issues and Solutions

### ImportError

If you encounter `ImportError`, make sure the project root directory is added to `sys.path`:

```python
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
```

### File Access Errors

If you encounter file access errors, make sure you use temporary directories for tests:

```python
import tempfile
temp_dir = tempfile.mkdtemp()
# Use temp_dir
import shutil
shutil.rmtree(temp_dir)  # Clean up
```

### Thread Errors

If you encounter thread-related errors, make sure to stop all threads before ending the test:

```python
def tearDown(self):
    # Stop thread
    self.cache.stop_cleanup.set()
    if self.cache.cleanup_thread:
        self.cache.cleanup_thread.join(timeout=1.0)
```