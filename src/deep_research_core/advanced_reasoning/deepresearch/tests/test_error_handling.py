"""
Unit tests for error handling and fallback mechanisms.
"""

import unittest
import os
import sys
import time
from unittest.mock import patch, MagicMock

# Add the src directory to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from src.deep_research_core.utils.searxng_errors import (
    SearXNGLocalError,
    SearXNGConnectionError,
    SearXNGTimeoutError,
    SearXNGParsingError,
    SearXNGConfigError,
    <PERSON><PERSON><PERSON>Error,
    PlaywrightError,
    detect_searxng_error
)
from src.deep_research_core.utils.retry_handler import RetryHandler, with_smart_retry
from src.deep_research_core.utils.health_checker import Sear<PERSON>NG<PERSON>ealthChecker, check_searxng_instance, get_available_instances
from src.deep_research_core.agents.web_search_agent_local_helpers import execute_search_with_fallback, _get_fallback_chain


class TestSearXNGErrors(unittest.TestCase):
    """Test SearXNG error classes."""
    
    def test_searxng_local_error(self):
        """Test SearXNGLocalError."""
        error = SearXNGLocalError("Test error", "http://localhost:8080")
        self.assertEqual(str(error), "Test error")
        self.assertEqual(error.details["instance"], "http://localhost:8080")
    
    def test_searxng_connection_error(self):
        """Test SearXNGConnectionError."""
        error = SearXNGConnectionError("Connection error", "http://localhost:8080", 500)
        self.assertEqual(str(error), "Connection error")
        self.assertEqual(error.details["instance"], "http://localhost:8080")
        self.assertEqual(error.details["status_code"], 500)
    
    def test_searxng_timeout_error(self):
        """Test SearXNGTimeoutError."""
        error = SearXNGTimeoutError("Timeout error", "http://localhost:8080", 10.0)
        self.assertEqual(str(error), "Timeout error")
        self.assertEqual(error.details["instance"], "http://localhost:8080")
        self.assertEqual(error.details["timeout"], 10.0)
    
    def test_detect_searxng_error(self):
        """Test detect_searxng_error function."""
        # Test connection error
        error = detect_searxng_error("Connection refused", "http://localhost:8080")
        self.assertIsInstance(error, SearXNGConnectionError)
        
        # Test timeout error
        error = detect_searxng_error("Timeout occurred", "http://localhost:8080")
        self.assertIsInstance(error, SearXNGTimeoutError)
        
        # Test parsing error
        error = detect_searxng_error("JSON decode error", "http://localhost:8080")
        self.assertIsInstance(error, SearXNGParsingError)
        
        # Test config error
        error = detect_searxng_error("Invalid configuration", "http://localhost:8080")
        self.assertIsInstance(error, SearXNGConfigError)
        
        # Test with status code
        error = detect_searxng_error("Server error", "http://localhost:8080", 500)
        self.assertIsInstance(error, SearXNGConnectionError)
        self.assertEqual(error.details["status_code"], 500)


class TestRetryHandler(unittest.TestCase):
    """Test RetryHandler class."""
    
    def test_retry_handler_success(self):
        """Test RetryHandler with successful function."""
        retry_handler = RetryHandler(max_retries=3)
        
        # Function that succeeds
        def success_func():
            return "success"
        
        result = retry_handler.execute(success_func)
        self.assertEqual(result, "success")
    
    def test_retry_handler_retry_and_succeed(self):
        """Test RetryHandler with function that fails first then succeeds."""
        retry_handler = RetryHandler(max_retries=3, initial_backoff=0.1)
        
        # Counter for number of calls
        calls = [0]
        
        # Function that fails twice then succeeds
        def fail_then_succeed():
            calls[0] += 1
            if calls[0] < 3:
                raise SearXNGConnectionError("Connection error", "http://localhost:8080")
            return "success after retry"
        
        result = retry_handler.execute(fail_then_succeed)
        self.assertEqual(result, "success after retry")
        self.assertEqual(calls[0], 3)
    
    def test_retry_handler_max_retries_exceeded(self):
        """Test RetryHandler with function that always fails."""
        retry_handler = RetryHandler(max_retries=2, initial_backoff=0.1)
        
        # Function that always fails
        def always_fail():
            raise SearXNGConnectionError("Connection error", "http://localhost:8080")
        
        with self.assertRaises(SearXNGConnectionError):
            retry_handler.execute(always_fail)
    
    def test_retry_handler_empty_results(self):
        """Test RetryHandler with function that returns empty results."""
        retry_handler = RetryHandler(
            max_retries=2, 
            initial_backoff=0.1, 
            retry_on_empty_results=True
        )
        
        # Counter for number of calls
        calls = [0]
        
        # Function that returns empty results twice then succeeds
        def empty_then_succeed():
            calls[0] += 1
            if calls[0] < 3:
                return {"success": True, "results": []}
            return {"success": True, "results": ["result"]}
        
        result = retry_handler.execute(empty_then_succeed)
        self.assertEqual(result["results"], ["result"])
        self.assertEqual(calls[0], 3)
    
    def test_with_smart_retry_decorator(self):
        """Test with_smart_retry decorator."""
        # Counter for number of calls
        calls = [0]
        
        # Function with decorator that fails twice then succeeds
        @with_smart_retry(max_retries=3, initial_backoff=0.1)
        def decorated_func():
            calls[0] += 1
            if calls[0] < 3:
                raise SearXNGConnectionError("Connection error", "http://localhost:8080")
            return "success after retry"
        
        result = decorated_func()
        self.assertEqual(result, "success after retry")
        self.assertEqual(calls[0], 3)


class TestHealthChecker(unittest.TestCase):
    """Test SearXNGHealthChecker class."""
    
    @patch('requests.get')
    def test_check_searxng_instance(self, mock_get):
        """Test check_searxng_instance function."""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_get.return_value = mock_response
        
        is_available, error = check_searxng_instance("http://localhost:8080")
        self.assertTrue(is_available)
        self.assertIsNone(error)
        
        # Mock failed response
        mock_response.status_code = 500
        is_available, error = check_searxng_instance("http://localhost:8080")
        self.assertFalse(is_available)
        self.assertEqual(error, "HTTP error: 500")
        
        # Mock connection error
        mock_get.side_effect = Exception("Connection error")
        is_available, error = check_searxng_instance("http://localhost:8080")
        self.assertFalse(is_available)
        self.assertEqual(error, "Connection error")
    
    @patch('requests.get')
    def test_get_available_instances(self, mock_get):
        """Test get_available_instances function."""
        # Mock responses
        def mock_response(url, **kwargs):
            mock_resp = MagicMock()
            if url == "http://localhost:8080/healthz":
                mock_resp.status_code = 200
            elif url == "http://localhost:8081/healthz":
                mock_resp.status_code = 500
            elif url == "http://localhost:8082/healthz":
                mock_resp.status_code = 200
            return mock_resp
        
        mock_get.side_effect = mock_response
        
        instances = ["http://localhost:8080", "http://localhost:8081", "http://localhost:8082"]
        available = get_available_instances(instances)
        
        self.assertEqual(available, ["http://localhost:8080", "http://localhost:8082"])
    
    @patch('subprocess.run')
    @patch('requests.get')
    def test_health_checker_restart(self, mock_get, mock_run):
        """Test SearXNGHealthChecker restart_searxng method."""
        # Mock successful response for health check
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_get.return_value = mock_response
        
        # Mock successful subprocess run
        mock_run.return_value.stdout = "searxng"
        
        health_checker = SearXNGHealthChecker(
            instance_url="http://localhost:8080",
            docker_container_name="searxng"
        )
        
        # Test restart
        result = health_checker.restart_searxng()
        self.assertTrue(result)
        
        # Verify subprocess calls
        self.assertEqual(mock_run.call_count, 2)
        
        # Test check_now
        status = health_checker.check_now()
        self.assertEqual(status["status"], "ok")


class TestFallbackMechanisms(unittest.TestCase):
    """Test fallback mechanisms."""
    
    def test_get_fallback_chain(self):
        """Test _get_fallback_chain function."""
        # Test default chain
        chain = _get_fallback_chain("auto", False)
        self.assertEqual(len(chain), 4)
        self.assertEqual(chain[0]["method"], "searxng")
        
        # Test content chain
        chain = _get_fallback_chain("auto", True)
        self.assertEqual(len(chain), 4)
        self.assertEqual(chain[0]["method"], "crawlee")
        
        # Test with query analysis
        query_analysis = {
            "query_type": "COMPLEX",
            "complexity": 0.8
        }
        chain = _get_fallback_chain("auto", False, query_analysis)
        self.assertEqual(len(chain), 4)
        self.assertEqual(chain[0]["method"], "crawlee")
        
        # Test with question query
        query_analysis = {
            "query_type": "QUESTION",
            "complexity": 0.5
        }
        chain = _get_fallback_chain("auto", False, query_analysis)
        self.assertEqual(len(chain), 4)
        self.assertEqual(chain[0]["method"], "searxng")
    
    @patch('src.deep_research_core.agents.web_search_agent_local_helpers.execute_search')
    def test_execute_search_with_fallback(self, mock_execute_search):
        """Test execute_search_with_fallback function."""
        # Mock search agent
        search_agent = MagicMock()
        
        # Mock successful search
        mock_execute_search.return_value = {
            "success": True,
            "results": ["result1", "result2"],
            "engine": "searxng"
        }
        
        # Test successful search
        result = execute_search_with_fallback(
            search_agent=search_agent,
            query="test query",
            num_results=10,
            language="en"
        )
        
        self.assertTrue(result["success"])
        self.assertEqual(result["results"], ["result1", "result2"])
        self.assertEqual(result["engine"], "searxng")
        
        # Mock failed search then successful
        mock_execute_search.side_effect = [
            {"success": False, "error": "Search failed"},
            {"success": True, "results": ["result1"], "engine": "crawlee"}
        ]
        
        # Test fallback
        result = execute_search_with_fallback(
            search_agent=search_agent,
            query="test query",
            num_results=10,
            language="en"
        )
        
        self.assertTrue(result["success"])
        self.assertEqual(result["results"], ["result1"])
        self.assertEqual(result["engine"], "crawlee")
        self.assertEqual(result["fallback_method"], "crawlee")
        
        # Mock all searches fail
        mock_execute_search.side_effect = [
            {"success": False, "error": "Search failed"},
            {"success": False, "error": "Search failed again"},
            {"success": False, "error": "Search failed yet again"},
            {"success": False, "error": "Search failed one more time"}
        ]
        
        # Test all fallbacks fail
        result = execute_search_with_fallback(
            search_agent=search_agent,
            query="test query",
            num_results=10,
            language="en"
        )
        
        self.assertFalse(result["success"])
        self.assertEqual(result["error"], "All search methods failed")


if __name__ == "__main__":
    unittest.main()
