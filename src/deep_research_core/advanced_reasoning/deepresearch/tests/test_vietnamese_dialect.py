"""
Test module for Vietnamese dialect processor.
"""

import unittest
import os
import sys

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.multilingual.vietnamese_dialect_processor import VietnameseDialectProcessor


class TestVietnameseDialectProcessor(unittest.TestCase):
    """Test case for the Vietnamese dialect processor."""

    def setUp(self):
        """Set up test fixtures."""
        self.processor = VietnameseDialectProcessor.get_instance()
        
        # Test sentences in different dialects
        self.northern_text = "Tao thấy món này ngon đấy, mày có muốn ăn không?"
        self.central_text = "Tau thấy món ni ngon rứa, mi có muốn ăn không?"
        self.southern_text = "Tui thấy món này ngon đó, bạn có muốn ăn không?"

    def test_dialect_detection(self):
        """Test dialect detection functionality."""
        # Test Northern dialect detection
        northern_probs = self.processor.detect_dialect(self.northern_text)
        self.assertEqual(max(northern_probs, key=northern_probs.get), "northern")
        
        # Test Central dialect detection
        central_probs = self.processor.detect_dialect(self.central_text)
        self.assertEqual(max(central_probs, key=central_probs.get), "central")
        
        # Test Southern dialect detection
        southern_probs = self.processor.detect_dialect(self.southern_text)
        self.assertEqual(max(southern_probs, key=southern_probs.get), "southern")

    def test_dialect_normalization(self):
        """Test dialect normalization functionality."""
        # Southern to Northern normalization
        normalized_to_northern = self.processor.normalize_to_dialect(
            self.southern_text, target_dialect="northern"
        )
        self.assertIn("tao", normalized_to_northern)
        self.assertNotIn("tui", normalized_to_northern)
        
        # Northern to Southern normalization
        normalized_to_southern = self.processor.normalize_to_dialect(
            self.northern_text, target_dialect="southern"
        )
        self.assertIn("bạn", normalized_to_southern)
        self.assertNotIn("mày", normalized_to_southern)

    def test_extract_dialect_markers(self):
        """Test extraction of dialect markers."""
        # Extract markers from Northern text
        northern_markers = self.processor.extract_dialect_markers(self.northern_text)
        self.assertTrue(len(northern_markers["northern"]) > 0)
        
        # Extract markers from Southern text
        southern_markers = self.processor.extract_dialect_markers(self.southern_text)
        self.assertTrue(len(southern_markers["southern"]) > 0)
        
        # Extract markers from Central text
        central_markers = self.processor.extract_dialect_markers(self.central_text)
        self.assertTrue(len(central_markers["central"]) > 0)

    def test_non_vietnamese_text(self):
        """Test behavior with non-Vietnamese text."""
        english_text = "This is an English sentence."
        
        # Detect dialect (should return equal probabilities)
        probs = self.processor.detect_dialect(english_text)
        self.assertAlmostEqual(probs["northern"], 0.33, places=2)
        self.assertAlmostEqual(probs["central"], 0.33, places=2)
        self.assertAlmostEqual(probs["southern"], 0.33, places=2)
        
        # Normalize (should return the original text)
        normalized = self.processor.normalize_to_dialect(english_text, target_dialect="northern")
        self.assertEqual(normalized, english_text)
        
        # Extract markers (should return empty lists)
        markers = self.processor.extract_dialect_markers(english_text)
        self.assertEqual(len(markers["northern"]), 0)
        self.assertEqual(len(markers["central"]), 0)
        self.assertEqual(len(markers["southern"]), 0)

    def test_mixed_dialect_text(self):
        """Test behavior with mixed dialect text."""
        mixed_text = "Tui đang ở Hà Nội và tao thấy thành phố này đẹp quá."
        
        # Detect dialect (should detect both Northern and Southern)
        probs = self.processor.detect_dialect(mixed_text)
        self.assertTrue(probs["northern"] > 0)
        self.assertTrue(probs["southern"] > 0)
        
        # Extract markers
        markers = self.processor.extract_dialect_markers(mixed_text)
        self.assertTrue(len(markers["northern"]) > 0)
        self.assertTrue(len(markers["southern"]) > 0)


if __name__ == "__main__":
    unittest.main() 