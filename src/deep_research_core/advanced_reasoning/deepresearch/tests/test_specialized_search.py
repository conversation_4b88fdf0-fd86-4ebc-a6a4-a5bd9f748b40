"""
Test module for specialized search functionality in WebSearchAgentLocal.
"""

import unittest
import sys
import os
import json
from unittest.mock import patch, MagicMock

# Add the parent directory to the path so we can import the module
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import directly from the file to avoid circular imports
import src.deep_research_core.agents.web_search_agent_local as web_search_agent_local_module
from src.deep_research_core.utils.local_query_analyzer import LocalQueryAnalyzer

# Create a mock class for WebSearchAgentLocal to avoid circular imports
class MockWebSearchAgentLocal:
    def __init__(self, **kwargs):
        self.search_method = kwargs.get("search_method", "searxng")
        self.api_search_config = kwargs.get("api_search_config", {})
        self.verbose = kwargs.get("verbose", False)
        self.cache = {}
        self.cache_timestamps = {}
        self.engine_request_counts = {}
        self.engine_reset_times = {}
        self.last_captcha_detection = {}

    def _detect_query_domain(self, query):
        # Simple implementation for testing
        if "sách" in query.lower() or "book" in query.lower():
            return "books"
        elif "bản đồ" in query.lower() or "map" in query.lower():
            return "maps"
        elif "nghiên cứu" in query.lower() or "academic" in query.lower():
            return "academic"
        elif "tin tức" in query.lower() or "news" in query.lower():
            return "news"
        elif "sức khỏe" in query.lower() or "health" in query.lower():
            return "health"
        elif "công nghệ" in query.lower() or "technology" in query.lower():
            return "technology"
        else:
            return "general"

    def search(self, query, **kwargs):
        # Mock implementation for testing
        return {
            "success": True,
            "results": [
                {
                    "title": "Test Result",
                    "url": "https://example.com",
                    "snippet": "This is a test result"
                }
            ]
        }

    def search_specialized_local(self, query, domain, **kwargs):
        # Mock implementation for testing
        return {
            "success": True,
            "domain": domain,
            "specialized_search": True,
            "results": [
                {
                    "title": f"{domain.capitalize()} Result",
                    "url": f"https://example.com/{domain}",
                    "snippet": f"This is a {domain} result"
                }
            ]
        }


class TestSpecializedSearch(unittest.TestCase):
    """
    Test class for specialized search functionality in WebSearchAgentLocal.
    """

    def setUp(self):
        """
        Set up the test environment.
        """
        # Create a MockWebSearchAgentLocal instance
        self.agent = MockWebSearchAgentLocal(
            search_method="searxng",
            api_search_config={
                "searxng_url": "http://localhost:8080",
                "language": "auto",
                "safe_search": False,
                "time_range": "",
                "categories": ["general"],
            },
            verbose=True
        )

    def tearDown(self):
        """
        Clean up after the test.
        """
        # Nothing to clean up for MockWebSearchAgentLocal
        pass

    def test_detect_query_domain(self):
        """
        Test the _detect_query_domain method.
        """
        # Test book domain
        domain = self.agent._detect_query_domain("Tìm kiếm sách về lập trình Python")
        self.assertEqual(domain, "books")

        # Test technology domain
        domain = self.agent._detect_query_domain("Hướng dẫn lập trình phần mềm")
        self.assertEqual(domain, "technology")

        # Test health domain
        domain = self.agent._detect_query_domain("Triệu chứng của bệnh covid-19")
        self.assertEqual(domain, "health")

        # Test news domain
        domain = self.agent._detect_query_domain("Tin tức thời sự mới nhất")
        self.assertEqual(domain, "news")

        # Test academic domain
        domain = self.agent._detect_query_domain("Nghiên cứu khoa học về vật lý lượng tử")
        self.assertEqual(domain, "academic")

        # Test maps domain
        domain = self.agent._detect_query_domain("Bản đồ thành phố Hà Nội")
        self.assertEqual(domain, "maps")

        # Test general domain
        domain = self.agent._detect_query_domain("Thông tin chung")
        self.assertEqual(domain, "general")

    @patch('src.deep_research_core.agents.web_search_agent_local.WebSearchAgentLocal.search_specialized_local')
    def test_search_with_specialized_search(self, mock_search_specialized):
        """
        Test that search method calls search_specialized_local when appropriate.
        """
        # Set up the mock
        mock_search_specialized.return_value = {
            "success": True,
            "domain": "books",
            "specialized_search": True,
            "results": [
                {
                    "title": "Book Result",
                    "url": "https://example.com/book",
                    "snippet": "This is a book result"
                }
            ]
        }

        # Call search with a book-related query
        result = self.agent.search("Tìm kiếm sách về lập trình Python")

        # Verify that search_specialized_local was called
        mock_search_specialized.assert_called_once()

        # Verify that the result has the expected domain
        self.assertEqual(result["domain"], "books")
        self.assertTrue(result["specialized_search"])

    @patch('src.deep_research_core.agents.web_search_agent_local.WebSearchAgentLocal.search')
    def test_search_specialized_local(self, mock_search):
        """
        Test the search_specialized_local method.
        """
        # Set up the mock
        mock_search.return_value = {
            "success": True,
            "results": [
                {
                    "title": "Health Result",
                    "url": "https://example.com/health",
                    "snippet": "This is a health result"
                }
            ]
        }

        # Call search_specialized_local directly
        result = self.agent.search_specialized_local(
            query="Triệu chứng của bệnh covid-19",
            domain="health",
            num_results=5,
            get_content=True
        )

        # Verify that search was called with the expected parameters
        mock_search.assert_called_once()
        args, kwargs = mock_search.call_args

        # Check that the engines parameter includes health-related engines
        self.assertIn("wikipedia", kwargs["engines"])
        self.assertIn("wikidata", kwargs["engines"])

        # Check that the categories parameter includes science
        self.assertIn("science", kwargs["categories"])

        # Verify that the result has the expected domain
        self.assertEqual(result["domain"], "health")
        self.assertTrue(result["specialized_search"])


if __name__ == '__main__':
    unittest.main()
