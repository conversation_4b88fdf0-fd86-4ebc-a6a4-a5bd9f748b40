import os
import sys
import unittest
from unittest.mock import MagicMock, patch
import tempfile
import shutil
import json

# Thêm đường dẫn thư mục gốc vào sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.agents.adaptive_crawler import AdaptiveCrawler


class TestSPAHandling(unittest.TestCase):
    """
    Test case cho tính năng xử lý JavaScript và SPA trong AdaptiveCrawler
    """

    def setUp(self):
        """
        Thiết lập môi trường test
        """
        # Tạo thư mục tạm để lưu file
        self.temp_dir = tempfile.mkdtemp()

        # Tạo AdaptiveCrawler với các tham số JavaScript và SPA
        self.crawler = AdaptiveCrawler(
            download_path=self.temp_dir,
            enable_javascript=True,
            wait_for_selector=".content",
            wait_for_timeout=2000,
            handle_spa=True
        )

    def tearDown(self):
        """
        Dọn dẹp sau khi test
        """
        # X<PERSON><PERSON> thư mục tạm
        shutil.rmtree(self.temp_dir)

    @patch('playwright.sync_api.sync_playwright')
    def test_handle_spa(self, mock_playwright):
        """
        Test phương thức _handle_spa
        """
        # Tạo mock cho Playwright
        mock_context = MagicMock()
        mock_page = MagicMock()
        mock_browser = MagicMock()

        # Thiết lập mock cho evaluate để trả về True cho is_spa
        mock_page.evaluate.side_effect = [
            True,  # is_spa
            None,  # scroll
            None   # click tabs
        ]

        # Thiết lập mock cho browser và context
        mock_browser.new_context.return_value = mock_context
        mock_context.new_page.return_value = mock_page

        # Thiết lập mock cho playwright
        mock_playwright_instance = MagicMock()
        mock_playwright_instance.chromium.launch.return_value = mock_browser
        mock_playwright.return_value.__enter__.return_value = mock_playwright_instance

        # Gọi phương thức _handle_spa
        self.crawler._handle_spa(mock_page, "https://example.com")

        # Kiểm tra xem các phương thức đã được gọi đúng cách
        mock_page.wait_for_timeout.assert_any_call(2000)  # Đợi JavaScript thực thi
        # Kiểm tra xem evaluate đã được gọi với một hàm JavaScript
        self.assertTrue(mock_page.evaluate.called)
        # Lấy tham số đầu tiên của lần gọi đầu tiên
        first_call_args = mock_page.evaluate.call_args_list[0][0]
        self.assertTrue(isinstance(first_call_args[0], str))
        self.assertTrue("window.React" in first_call_args[0])

        # Kiểm tra xem phương thức cuộn trang đã được gọi
        mock_page.evaluate.assert_any_call("""() => {
                    return new Promise((resolve) => {
                        let totalHeight = 0;
                        const distance = 100;
                        const timer = setInterval(() => {
                            const scrollHeight = document.body.scrollHeight;
                            window.scrollBy(0, distance);
                            totalHeight += distance;

                            if(totalHeight >= scrollHeight){
                                clearInterval(timer);
                                resolve();
                            }
                        }, 100);
                    });
                }""")

        # Kiểm tra xem phương thức nhấp vào tab đã được gọi
        mock_page.evaluate.assert_any_call("""() => {
                    // Tìm các tab và nút
                    const clickables = Array.from(document.querySelectorAll('button, .tab, [role="tab"], .nav-item, .accordion-header'));

                    // Nhấp vào mỗi phần tử
                    clickables.slice(0, 5).forEach(el => {
                        try {
                            el.click();
                        } catch (e) {
                            // Bỏ qua lỗi
                        }
                    });
                }""")

        # Kiểm tra xem phương thức wait_for_timeout đã được gọi sau khi nhấp
        mock_page.wait_for_timeout.assert_any_call(1000)

    @patch('playwright.sync_api.sync_playwright')
    def test_crawl_with_javascript_and_spa(self, mock_playwright):
        """
        Test tính năng crawl với JavaScript và SPA
        """
        # Tạo mock cho Playwright
        mock_context = MagicMock()
        mock_page = MagicMock()
        mock_browser = MagicMock()

        # Thiết lập mock cho page
        mock_page.title.return_value = "Test SPA Page"
        mock_page.content.return_value = "<html><body><div class='content'>Test content</div></body></html>"
        mock_page.evaluate.return_value = "Test content extracted with JavaScript"

        # Thiết lập mock cho browser và context
        mock_browser.new_context.return_value = mock_context
        mock_context.new_page.return_value = mock_page

        # Thiết lập mock cho playwright
        mock_playwright_instance = MagicMock()
        mock_playwright_instance.chromium.launch.return_value = mock_browser
        mock_playwright.return_value.__enter__.return_value = mock_playwright_instance

        # Gọi phương thức crawl
        result = self.crawler.crawl(
            urls=["https://example.com/spa"]
        )

        # Kiểm tra kết quả
        self.assertTrue(result["success"])
        self.assertEqual(len(result["results"]), 1)
        self.assertEqual(result["results"][0]["title"], "Test SPA Page")

        # Kiểm tra xem các phương thức đã được gọi
        self.assertTrue(mock_page.wait_for_timeout.called)
        self.assertTrue(mock_page.wait_for_selector.called)

        # Lưu kết quả ra file để kiểm tra
        result_path = os.path.join(self.temp_dir, "spa_crawl_result.json")
        with open(result_path, "w", encoding="utf-8") as f:
            json.dump(result, f, ensure_ascii=False, indent=2)

        print(f"Đã lưu kết quả crawl vào: {result_path}")


if __name__ == '__main__':
    unittest.main()
