#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test cho việc tích hợp AdaptiveCrawler với WebSearchAgentLocal.
"""

import os
import sys
import unittest
import json
from unittest.mock import patch, MagicMock

# Thêm thư mục gốc vào sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import các module cần thiết
from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal
from src.deep_research_core.agents.adaptive_crawler_integration import integrate_adaptive_crawler
from src.deep_research_core.agents.deep_research_integration import integrate_deep_research

class TestAdaptiveCrawlerIntegration(unittest.TestCase):
    """
    Test case cho việc tích hợp AdaptiveCrawler với WebSearchAgentLocal.
    """

    def setUp(self):
        """
        Thi<PERSON><PERSON> lập cho các test case.
        """
        # Tạo một instance của WebSearchAgentLocal
        self.agent = WebSearchAgentLocal(
            search_method="auto",
            verbose=True,
            enable_plugins=False,
            enable_vietnamese_search=False,
            enable_nlp=False,
            enable_multimedia_search=False,
            enable_performance_optimization=False,
            enable_file_download=False
        )

        # Mock AdaptiveCrawler
        self.mock_adaptive_crawler = MagicMock()
        self.mock_adaptive_crawler.crawl.return_value = {
            "success": True,
            "results": [
                {
                    "url": "https://example.com",
                    "title": "Example Domain",
                    "content": "This domain is for use in illustrative examples in documents.",
                    "links": ["https://www.iana.org/domains/example"],
                    "metadata": {"description": "Example Domain"}
                }
            ]
        }

    def test_integrate_adaptive_crawler(self):
        """
        Test tích hợp AdaptiveCrawler với WebSearchAgentLocal.
        """
        # Patch AdaptiveCrawler
        with patch('src.deep_research_core.agents.adaptive_crawler_integration.AdaptiveCrawler', return_value=self.mock_adaptive_crawler):
            # Tích hợp AdaptiveCrawler
            integrate_adaptive_crawler(self.agent, {
                "use_memory_optimization": True,
                "use_playwright": True,
                "verbose": True
            })

            # Kiểm tra xem AdaptiveCrawler đã được tích hợp chưa
            self.assertTrue(hasattr(self.agent, "_adaptive_crawler"))
            self.assertEqual(self.agent._adaptive_crawler, self.mock_adaptive_crawler)

            # Kiểm tra xem các phương thức đã được tích hợp chưa
            self.assertTrue(hasattr(self.agent, "_crawl_url"))
            self.assertTrue(hasattr(self.agent, "_crawl_urls"))
            self.assertTrue(hasattr(self.agent, "_extract_content_from_url"))
            self.assertTrue(hasattr(self.agent, "_extract_links_from_url"))
            self.assertTrue(hasattr(self.agent, "_extract_metadata_from_url"))
            self.assertTrue(hasattr(self.agent, "_deep_crawl_with_adaptive_crawler"))

    def test_deep_crawl_with_adaptive_crawler(self):
        """
        Test phương thức _deep_crawl_with_adaptive_crawler.
        """
        # Patch AdaptiveCrawler
        with patch('src.deep_research_core.agents.adaptive_crawler_integration.AdaptiveCrawler', return_value=self.mock_adaptive_crawler):
            # Tích hợp AdaptiveCrawler
            integrate_adaptive_crawler(self.agent, {
                "use_memory_optimization": True,
                "use_playwright": True,
                "verbose": True
            })

            # Gọi phương thức _deep_crawl_with_adaptive_crawler
            result = self.agent._deep_crawl_with_adaptive_crawler("https://example.com")

            # Kiểm tra kết quả
            self.assertTrue(result["success"])
            self.assertEqual(result["url"], "https://example.com")
            self.assertIn("content", result)
            self.assertIn("links", result)
            self.assertIn("metadata", result)
            self.assertIn("crawl_stats", result)

            # Kiểm tra xem AdaptiveCrawler.crawl đã được gọi chưa
            self.mock_adaptive_crawler.crawl.assert_called_once()

    def test_deep_crawl_uses_adaptive_crawler(self):
        """
        Test phương thức _deep_crawl sử dụng AdaptiveCrawler.
        """
        # Patch AdaptiveCrawler
        with patch('src.deep_research_core.agents.adaptive_crawler_integration.AdaptiveCrawler', return_value=self.mock_adaptive_crawler):
            # Tích hợp AdaptiveCrawler
            integrate_adaptive_crawler(self.agent, {
                "use_memory_optimization": True,
                "use_playwright": True,
                "verbose": True
            })

            # Patch phương thức _deep_crawl_with_adaptive_crawler
            with patch.object(self.agent, '_deep_crawl_with_adaptive_crawler') as mock_deep_crawl:
                mock_deep_crawl.return_value = {
                    "success": True,
                    "url": "https://example.com",
                    "content": "Example content",
                    "links": ["https://www.example.com/link1"],
                    "metadata": {"description": "Example Domain"}
                }

                # Gọi phương thức _deep_crawl
                self.agent._deep_crawl("https://example.com")

                # Kiểm tra xem _deep_crawl_with_adaptive_crawler đã được gọi chưa
                mock_deep_crawl.assert_called_once_with(
                    url="https://example.com",
                    max_depth=1,
                    max_pages=3,
                    timeout=30,
                    include_html=False,
                    respect_robots=True
                )

    def test_integrate_deep_research(self):
        """
        Test tích hợp AdaptiveCrawler với deep_research.
        """
        # Patch AdaptiveCrawler
        with patch('src.deep_research_core.crawlers.adaptive_crawler.AdaptiveCrawler', return_value=self.mock_adaptive_crawler):
            # Tích hợp AdaptiveCrawler với deep_research
            integrate_deep_research(self.agent, {
                "use_memory_optimization": True,
                "use_playwright": True,
                "verbose": True
            })

            # Kiểm tra xem AdaptiveCrawler đã được tích hợp chưa
            self.assertTrue(hasattr(self.agent, "_adaptive_crawler"))

            # Kiểm tra xem phương thức deep_research đã được tích hợp chưa
            self.assertTrue(hasattr(self.agent, "deep_research"))

if __name__ == '__main__':
    unittest.main()
