"""
Tests for the Vietnamese compound word processor.

This module contains tests for the VietnameseCompoundProcessor class,
which handles Vietnamese compound word detection, segmentation, and analysis.
"""

import unittest
import sys
import os

# Add the parent directory to the path so we can import the package
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.multilingual.vietnamese_compound_processor import VietnameseCompoundProcessor


class TestVietnameseCompoundProcessor(unittest.TestCase):
    """Test cases for the Vietnamese compound word processor."""

    def setUp(self):
        """Set up test fixtures."""
        self.processor = VietnameseCompoundProcessor.get_instance()
        
        # Test samples
        self.samples = {
            "compounds": [
                "xe máy",
                "bàn ghế",
                "quần áo",
                "đường sá",
                "học hành",
                "thức ăn",
                "nhà cửa",
            ],
            "test_texts": [
                "Xe máy của tôi đang đỗ ở nhà cửa của bạn.",
                "Quần áo và bàn ghế đều là vật dụng cần thiết.",
                "<PERSON><PERSON><PERSON> hành chăm chỉ sẽ đem lại kết quả tốt.",
                "Thức ăn ngon là thức ăn tươi sạch.",
                "Đường sá ngày nay rất hiện đại và tiện lợi.",
            ],
            "reduplicated": [
                "đẹp đẹp",
                "xanh xanh",
                "lung linh",
                "lăng xăng",
                "lấp lánh",
                "xanh đỏ xanh",
                "người người",
            ],
            "non_compounds": [
                "và",
                "của",
                "với",
                "cùng",
                "nhà",
                "xe",
                "tôi đang",
            ]
        }

    def test_is_compound_word(self):
        """Test detection of compound words."""
        # Test with compound words
        for compound in self.samples["compounds"]:
            self.assertTrue(
                self.processor.is_compound_word(compound),
                f"Failed to identify '{compound}' as a compound word"
            )
            
        # Test with reduplicated words
        for reduplicated in self.samples["reduplicated"]:
            self.assertTrue(
                self.processor.is_compound_word(reduplicated),
                f"Failed to identify '{reduplicated}' as a reduplicated compound word"
            )
            
        # Test with non-compound words
        for non_compound in self.samples["non_compounds"]:
            self.assertFalse(
                self.processor.is_compound_word(non_compound),
                f"Incorrectly identified '{non_compound}' as a compound word"
            )

    def test_detect_compound_words(self):
        """Test detection of compound words in text."""
        # Test with a sentence containing compounds
        text = "Xe máy của tôi đang đỗ ở nhà cửa của bạn."
        results = self.processor.detect_compound_words(text)
        
        # Check that we found both compounds
        self.assertEqual(len(results), 2, f"Expected 2 compounds, found {len(results)}")
        
        # Check the first compound
        self.assertEqual(results[0][0], "xe máy", "Failed to detect 'xe máy'")
        self.assertEqual(results[0][3], "subordinate", "Incorrect type for 'xe máy'")
        
        # Check the second compound
        self.assertEqual(results[1][0], "nhà cửa", "Failed to detect 'nhà cửa'")
        self.assertEqual(results[1][3], "coordinate", "Incorrect type for 'nhà cửa'")

    def test_segment_compound_words(self):
        """Test segmentation of text with compound words."""
        # Test with a simple sentence
        text = "Xe máy của tôi đang đỗ ở nhà cửa của bạn."
        segments = self.processor.segment_compound_words(text)
        
        # Expected segmentation: ["xe máy", "của", "tôi", "đang", "đỗ", "ở", "nhà cửa", "của", "bạn", "."]
        expected = ["xe máy", "của", "tôi", "đang", "đỗ", "ở", "nhà cửa", "của", "bạn", "."]
        self.assertEqual(segments, expected, "Incorrect segmentation")
        
        # Test with a more complex text
        text = "Quần áo và bàn ghế đều là vật dụng cần thiết."
        segments = self.processor.segment_compound_words(text)
        
        # Check that compounds are preserved
        self.assertIn("quần áo", segments, "Missing compound 'quần áo'")
        self.assertIn("bàn ghế", segments, "Missing compound 'bàn ghế'")

    def test_classify_compound_type(self):
        """Test classification of compound word types."""
        # Test subordinate compounds
        self.assertEqual(
            self.processor.classify_compound_type("xe máy"),
            "subordinate",
            "Incorrect classification for 'xe máy'"
        )
        self.assertEqual(
            self.processor.classify_compound_type("thức ăn"),
            "subordinate",
            "Incorrect classification for 'thức ăn'"
        )
        
        # Test coordinate compounds
        self.assertEqual(
            self.processor.classify_compound_type("bàn ghế"),
            "coordinate",
            "Incorrect classification for 'bàn ghế'"
        )
        self.assertEqual(
            self.processor.classify_compound_type("quần áo"),
            "coordinate",
            "Incorrect classification for 'quần áo'"
        )
        
        # Test reduplicated compounds
        self.assertEqual(
            self.processor.classify_compound_type("đường sá"),
            "reduplicated",
            "Incorrect classification for 'đường sá'"
        )
        self.assertEqual(
            self.processor.classify_compound_type("học hành"),
            "reduplicated",
            "Incorrect classification for 'học hành'"
        )
        self.assertEqual(
            self.processor.classify_compound_type("đẹp đẹp"),
            "reduplicated",
            "Incorrect classification for 'đẹp đẹp'"
        )

    def test_analyze_compound_distribution(self):
        """Test analysis of compound word distribution in text."""
        # Test with a text containing multiple compound types
        text = "Xe máy và bàn ghế là những vật dụng quan trọng trong nhà cửa. Học hành chăm chỉ để mua được thức ăn ngon."
        
        analysis = self.processor.analyze_compound_distribution(text)
        
        # Check the analysis results
        self.assertGreaterEqual(analysis["total_compounds"], 5, "Expected at least 5 compounds")
        self.assertIn("subordinate", analysis["type_distribution"], "Missing subordinate compounds")
        self.assertIn("coordinate", analysis["type_distribution"], "Missing coordinate compounds")
        self.assertIn("reduplicated", analysis["type_distribution"], "Missing reduplicated compounds")
        
        # Check that we have examples
        self.assertGreaterEqual(len(analysis["compound_examples"]), 1, "Missing compound examples")

    def test_extract_core_meaning(self):
        """Test extraction of core meaning from compound words."""
        # Test with subordinate compounds
        self.assertEqual(
            self.processor.extract_core_meaning("xe máy"),
            "xe",
            "Incorrect core meaning for 'xe máy'"
        )
        
        # Test with coordinate compounds
        self.assertEqual(
            self.processor.extract_core_meaning("bàn ghế"),
            "bàn",
            "Incorrect core meaning for 'bàn ghế'"
        )
        
        # Test with reduplicated compounds
        self.assertEqual(
            self.processor.extract_core_meaning("đẹp đẹp"),
            "đẹp",
            "Incorrect core meaning for 'đẹp đẹp'"
        )

    def test_are_semantically_related(self):
        """Test detection of semantically related words."""
        # Test with identical words
        self.assertTrue(
            self.processor._are_semantically_related("đẹp", "đẹp"),
            "Failed to identify identical words as related"
        )
        
        # Test with words sharing the same initial consonant
        self.assertTrue(
            self.processor._are_semantically_related("lung", "linh"),
            "Failed to identify words with same initial consonant as related"
        )
        
        # Test with words sharing the same final sound
        self.assertTrue(
            self.processor._are_semantically_related("lấp", "láp"),
            "Failed to identify words with same final sound as related"
        )
        
        # Test with unrelated words
        self.assertFalse(
            self.processor._are_semantically_related("xanh", "đỏ"),
            "Incorrectly identified unrelated words as related"
        )


if __name__ == '__main__':
    unittest.main() 