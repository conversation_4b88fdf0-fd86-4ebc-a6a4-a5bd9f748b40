#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test cơ bản cho AdaptiveCrawler.
"""

import unittest
import os
import sys
import time
from unittest.mock import MagicMock, patch

# Thêm thư mục gốc vào sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.crawlers.adaptive_crawler import AdaptiveCrawler

class TestAdaptiveCrawlerBasic(unittest.TestCase):
    """Test cơ bản cho AdaptiveCrawler."""

    def setUp(self):
        """Thiết lập trước mỗi test."""
        # Tạo AdaptiveCrawler với cấu hình cơ bản
        self.crawler = AdaptiveCrawler(
            max_depth=2,
            max_urls_per_domain=5,
            max_total_urls=10,
            verbose=True
        )

    def test_initialization(self):
        """Test khởi tạo AdaptiveCrawler."""
        # <PERSON><PERSON><PERSON> tra các thuộ<PERSON> t<PERSON>h cơ bản
        self.assertEqual(self.crawler.max_depth, 2)
        self.assertEqual(self.crawler.max_urls_per_domain, 5)
        self.assertEqual(self.crawler.max_total_urls, 10)
        self.assertEqual(self.crawler.verbose, True)

        # Kiểm tra các thuộc tính khác
        self.assertIsInstance(self.crawler.visited_urls, set)
        self.assertIsInstance(self.crawler.url_queue, list)
        self.assertIsInstance(self.crawler.domain_counts, dict)
        self.assertIsInstance(self.crawler.stats, dict)

    @patch('requests.Session.get')
    def test_crawl_url(self, mock_get):
        """Test crawl một URL."""
        # Thiết lập mock
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = "<html><body><h1>Test Page</h1><p>This is a test page.</p></body></html>"
        mock_response.content = mock_response.text.encode('utf-8')
        mock_response.headers = {'Content-Type': 'text/html'}
        mock_get.return_value = mock_response

        # Thiết lập các phương thức cần thiết
        self.crawler._fetch_with_requests = MagicMock(return_value={
            "success": True,
            "status_code": 200,
            "content": "<html><body><h1>Test Page</h1><p>This is a test page.</p></body></html>",
            "content_type": "text/html",
            "encoding": "utf-8",
            "url": "https://example.com"
        })

        # Crawl URL
        url = "https://example.com"
        result = self.crawler.crawl(url)

        # Kiểm tra kết quả
        self.assertEqual(result.get("url"), url)
        self.assertIn(url, self.crawler.visited_urls)
        self.assertEqual(self.crawler.stats["total_urls_crawled"], 1)

    @patch('requests.Session.get')
    def test_crawl_with_error(self, mock_get):
        """Test crawl với lỗi."""
        # Thiết lập mock để gây ra lỗi
        mock_get.side_effect = Exception("Test error")

        # Crawl URL
        url = "https://example.com"
        result = self.crawler.crawl(url)

        # Kiểm tra kết quả
        self.assertFalse(result.get("success", True))
        self.assertEqual(result.get("url"), url)
        self.assertIn("error", result)
        self.assertEqual(self.crawler.stats["failed_crawls"], 1)

    def test_max_depth_limit(self):
        """Test giới hạn độ sâu."""
        # Crawl với độ sâu vượt quá giới hạn
        url = "https://example.com"
        result = self.crawler.crawl(url, depth=3)

        # Kiểm tra kết quả
        self.assertFalse(result.get("success", True))
        self.assertEqual(result.get("error"), "Max depth exceeded")

    def test_domain_limit(self):
        """Test giới hạn domain."""
        # Thiết lập domain_counts
        self.crawler.domain_counts["example.com"] = 5

        # Crawl URL
        url = "https://example.com/page"
        result = self.crawler.crawl(url)

        # Kiểm tra kết quả
        self.assertFalse(result.get("success", True))
        self.assertEqual(result.get("error"), "Max URLs per domain exceeded")

if __name__ == "__main__":
    unittest.main()
