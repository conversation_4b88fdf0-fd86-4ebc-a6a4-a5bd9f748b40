"""
Test module for DynamicContentHandler.
"""

import os
import sys
import unittest
import asyncio
from unittest.mock import patch, MagicMock

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))

from deep_research_core.agents.dynamic_content_handler import DynamicContentHandler

class TestDynamicContentHandler(unittest.TestCase):
    """Test cases for DynamicContentHandler."""

    def setUp(self):
        """Set up test fixtures."""
        self.handler = DynamicContentHandler(
            timeout=10,
            respect_robots=True,
            config={
                "wait_for_idle_network": True,
                "wait_for_selectors": True,
                "handle_lazy_loading": True,
                "handle_infinite_scroll": False,
                "handle_pagination": False,
                "handle_popups": True,
                "handle_iframes": False,
                "extract_shadow_dom": True,
                "extract_dynamic_data": True,
                "block_ads": True,
                "block_trackers": True,
                "block_media": True,
            }
        )

    @patch('deep_research_core.agents.dynamic_content_handler.async_playwright')
    @patch('deep_research_core.agents.dynamic_content_handler.robots_parser')
    async def test_extract_content_basic(self, mock_robots_parser, mock_playwright):
        """Test basic content extraction."""
        # Mock robots.txt check
        mock_robots_parser.can_fetch.return_value = True
        
        # Mock Playwright objects
        mock_browser = MagicMock()
        mock_context = MagicMock()
        mock_page = MagicMock()
        mock_response = MagicMock()
        
        # Set up the mock chain
        mock_playwright.return_value.__aenter__.return_value.chromium.launch.return_value = mock_browser
        mock_browser.new_context.return_value = mock_context
        mock_context.new_page.return_value = mock_page
        mock_page.goto.return_value = mock_response
        
        # Mock page content
        mock_page.title.return_value = "Test Page"
        mock_page.content.return_value = "<html><body><h1>Test Content</h1></body></html>"
        
        # Mock evaluate to return content
        mock_page.evaluate.return_value = "Test Content"
        
        # Mock links extraction
        mock_page.evaluate.side_effect = [
            "Test Content",  # First call for content
            [{"url": "https://example.com/page1", "text": "Link 1", "title": ""}]  # Second call for links
        ]
        
        # Execute the test
        result = await self.handler.extract_content("https://example.com")
        
        # Assertions
        self.assertTrue(result["success"])
        self.assertEqual(result["url"], "https://example.com")
        self.assertEqual(result["title"], "Test Page")
        self.assertEqual(result["content"], "Test Content")
        self.assertEqual(len(result["links"]), 1)
        self.assertEqual(result["links"][0]["url"], "https://example.com/page1")

    @patch('deep_research_core.agents.dynamic_content_handler.async_playwright')
    @patch('deep_research_core.agents.dynamic_content_handler.robots_parser')
    async def test_extract_content_with_selectors(self, mock_robots_parser, mock_playwright):
        """Test content extraction with specific selectors."""
        # Mock robots.txt check
        mock_robots_parser.can_fetch.return_value = True
        
        # Mock Playwright objects
        mock_browser = MagicMock()
        mock_context = MagicMock()
        mock_page = MagicMock()
        mock_response = MagicMock()
        mock_element = MagicMock()
        
        # Set up the mock chain
        mock_playwright.return_value.__aenter__.return_value.chromium.launch.return_value = mock_browser
        mock_browser.new_context.return_value = mock_context
        mock_context.new_page.return_value = mock_page
        mock_page.goto.return_value = mock_response
        
        # Mock page content
        mock_page.title.return_value = "Test Page"
        
        # Mock query_selector_all to return elements
        mock_page.query_selector_all.return_value = [mock_element]
        mock_element.text_content.return_value = "Selected Content"
        
        # Execute the test
        result = await self.handler.extract_content(
            url="https://example.com",
            selectors={"main_content": "main", "sidebar": ".sidebar"}
        )
        
        # Assertions
        self.assertTrue(result["success"])
        self.assertEqual(result["url"], "https://example.com")
        self.assertEqual(result["title"], "Test Page")
        self.assertEqual(result["content"], "Selected Content\n\nSelected Content")

    @patch('deep_research_core.agents.dynamic_content_handler.async_playwright')
    @patch('deep_research_core.agents.dynamic_content_handler.robots_parser')
    async def test_handle_popups(self, mock_robots_parser, mock_playwright):
        """Test popup handling."""
        # Mock robots.txt check
        mock_robots_parser.can_fetch.return_value = True
        
        # Mock Playwright objects
        mock_browser = MagicMock()
        mock_context = MagicMock()
        mock_page = MagicMock()
        mock_response = MagicMock()
        
        # Set up the mock chain
        mock_playwright.return_value.__aenter__.return_value.chromium.launch.return_value = mock_browser
        mock_browser.new_context.return_value = mock_context
        mock_context.new_page.return_value = mock_page
        mock_page.goto.return_value = mock_response
        
        # Mock query_selector to simulate popup presence
        mock_page.query_selector.return_value = True
        
        # Execute the test
        result = await self.handler.extract_content("https://example.com")
        
        # Assertions
        self.assertTrue(result["success"])
        # Verify that click was called (popup was closed)
        mock_page.click.assert_called()

    @patch('deep_research_core.agents.dynamic_content_handler.async_playwright')
    @patch('deep_research_core.agents.dynamic_content_handler.robots_parser')
    async def test_robots_txt_disallowed(self, mock_robots_parser, mock_playwright):
        """Test handling of URLs disallowed by robots.txt."""
        # Mock robots.txt check to disallow
        mock_robots_parser.can_fetch.return_value = False
        
        # Execute the test
        result = await self.handler.extract_content("https://example.com/disallowed")
        
        # Assertions
        self.assertFalse(result["success"])
        self.assertEqual(result["error"], "URL is disallowed by robots.txt")
        # Verify that Playwright was not used
        mock_playwright.assert_not_called()

    @patch('deep_research_core.agents.dynamic_content_handler.async_playwright')
    @patch('deep_research_core.agents.dynamic_content_handler.robots_parser')
    async def test_extract_json_ld(self, mock_robots_parser, mock_playwright):
        """Test extraction of JSON-LD data."""
        # Mock robots.txt check
        mock_robots_parser.can_fetch.return_value = True
        
        # Mock Playwright objects
        mock_browser = MagicMock()
        mock_context = MagicMock()
        mock_page = MagicMock()
        mock_response = MagicMock()
        
        # Set up the mock chain
        mock_playwright.return_value.__aenter__.return_value.chromium.launch.return_value = mock_browser
        mock_browser.new_context.return_value = mock_context
        mock_context.new_page.return_value = mock_page
        mock_page.goto.return_value = mock_response
        
        # Mock page content
        mock_page.title.return_value = "Test Page"
        mock_page.content.return_value = "<html><body><h1>Test Content</h1></body></html>"
        
        # Mock evaluate to return content and JSON-LD data
        json_ld_data = [{"@context": "https://schema.org", "@type": "WebPage", "name": "Test Page"}]
        mock_page.evaluate.side_effect = [
            "Test Content",  # First call for content
            [],  # Links
            [],  # Images
            json_ld_data  # JSON-LD data
        ]
        
        # Execute the test
        result = await self.handler.extract_content(
            url="https://example.com",
            extract_json_ld=True
        )
        
        # Assertions
        self.assertTrue(result["success"])
        self.assertEqual(result["json_ld"], json_ld_data)

if __name__ == '__main__':
    # Run the async tests
    unittest.main()
