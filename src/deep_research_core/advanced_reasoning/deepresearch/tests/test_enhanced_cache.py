"""
Unit tests for EnhancedWebSearchCache.
"""

import os
import time
import unittest
import tempfile
import shutil
from unittest.mock import patch, MagicMock

import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from src.deep_research_core.utils.enhanced_cache import DiskCache, EnhancedWebSearchCache


class TestDiskCache(unittest.TestCase):
    """Test DiskCache class."""
    
    def setUp(self):
        """Set up test environment."""
        # Create a temporary directory for cache
        self.temp_dir = tempfile.mkdtemp()
        self.cache = DiskCache(cache_dir=self.temp_dir, limit=100)
    
    def tearDown(self):
        """Clean up test environment."""
        # Remove the temporary directory
        shutil.rmtree(self.temp_dir)
    
    def test_set_get(self):
        """Test set and get methods."""
        # Set a value
        self.cache.set("test_key", "test_value")
        
        # Get the value
        value = self.cache.get("test_key")
        
        # Check the value
        self.assertEqual(value, "test_value")
    
    def test_set_get_with_ttl(self):
        """Test set and get methods with TTL."""
        # Set a value with short TTL
        self.cache.set("test_key", "test_value", ttl=1)
        
        # Get the value immediately
        value = self.cache.get("test_key")
        self.assertEqual(value, "test_value")
        
        # Wait for TTL to expire
        time.sleep(1.1)
        
        # Get the value again
        value = self.cache.get("test_key")
        self.assertIsNone(value)
    
    def test_has(self):
        """Test has method."""
        # Set a value
        self.cache.set("test_key", "test_value")
        
        # Check if key exists
        self.assertTrue(self.cache.has("test_key"))
        
        # Check if non-existent key exists
        self.assertFalse(self.cache.has("non_existent_key"))
    
    def test_delete(self):
        """Test delete method."""
        # Set a value
        self.cache.set("test_key", "test_value")
        
        # Delete the key
        result = self.cache.delete("test_key")
        
        # Check the result
        self.assertTrue(result)
        
        # Check if key exists
        self.assertFalse(self.cache.has("test_key"))
    
    def test_clear(self):
        """Test clear method."""
        # Set multiple values
        self.cache.set("test_key1", "test_value1")
        self.cache.set("test_key2", "test_value2")
        
        # Clear the cache
        result = self.cache.clear()
        
        # Check the result
        self.assertTrue(result)
        
        # Check if keys exist
        self.assertFalse(self.cache.has("test_key1"))
        self.assertFalse(self.cache.has("test_key2"))
    
    def test_compression(self):
        """Test compression."""
        # Set a value with compression
        large_value = "x" * 10000
        self.cache.set("test_key", large_value, compress=True)
        
        # Get the value
        value = self.cache.get("test_key")
        
        # Check the value
        self.assertEqual(value, large_value)
        
        # Check compression stats
        stats = self.cache.get_stats()
        compression_stats = stats.get("compression_stats", {})
        
        # Check if compression ratio is less than 1 (compressed)
        self.assertIn("compression_ratio", compression_stats)
        self.assertLess(compression_stats["compression_ratio"], 1.0)
    
    def test_metadata(self):
        """Test metadata."""
        # Set a value with metadata
        metadata = {"test_key": "test_value"}
        self.cache.set("test_key", "test_value", metadata=metadata)
        
        # Check metadata in cache entry
        self.assertIn("test_key", self.cache.metadata["entries"]["test_key"]["metadata"])
        self.assertEqual(self.cache.metadata["entries"]["test_key"]["metadata"]["test_key"], "test_value")


class TestEnhancedWebSearchCache(unittest.TestCase):
    """Test EnhancedWebSearchCache class."""
    
    def setUp(self):
        """Set up test environment."""
        # Create a temporary directory for cache
        self.temp_dir = tempfile.mkdtemp()
        
        # Create a cache with small limits for testing
        self.cache = EnhancedWebSearchCache(
            cache_dir=self.temp_dir,
            memory_limit=10,
            disk_limit=20,
            memory_ttl=1,
            disk_ttl=2,
            enable_prefetching=False,  # Disable prefetching for testing
            enable_compression=True,
            enable_semantic_search=False  # Disable semantic search for testing
        )
    
    def tearDown(self):
        """Clean up test environment."""
        # Remove the temporary directory
        shutil.rmtree(self.temp_dir)
    
    def test_set_get(self):
        """Test set and get methods."""
        # Set a value
        self.cache.set("test_key", "test_value")
        
        # Get the value
        value = self.cache.get("test_key")
        
        # Check the value
        self.assertEqual(value, "test_value")
    
    def test_memory_cache_hit(self):
        """Test memory cache hit."""
        # Set a value
        self.cache.set("test_key", "test_value")
        
        # Get the value
        value = self.cache.get("test_key")
        
        # Check the value
        self.assertEqual(value, "test_value")
        
        # Check memory hit count
        self.assertEqual(self.cache.stats["memory_hits"], 1)
    
    def test_disk_cache_hit(self):
        """Test disk cache hit."""
        # Set a value
        self.cache.set("test_key", "test_value")
        
        # Clear memory cache
        self.cache.memory_cache.clear()
        
        # Get the value
        value = self.cache.get("test_key")
        
        # Check the value
        self.assertEqual(value, "test_value")
        
        # Check disk hit count
        self.assertEqual(self.cache.stats["disk_hits"], 1)
    
    def test_cache_miss(self):
        """Test cache miss."""
        # Get a non-existent value
        value = self.cache.get("non_existent_key")
        
        # Check the value
        self.assertIsNone(value)
        
        # Check miss count
        self.assertEqual(self.cache.stats["misses"], 1)
    
    def test_memory_ttl(self):
        """Test memory TTL."""
        # Set a value with short TTL
        self.cache.set("test_key", "test_value")
        
        # Get the value immediately
        value = self.cache.get("test_key")
        self.assertEqual(value, "test_value")
        
        # Wait for memory TTL to expire
        time.sleep(1.1)
        
        # Get the value again (should hit disk cache)
        value = self.cache.get("test_key")
        self.assertEqual(value, "test_value")
        
        # Check hit counts
        self.assertEqual(self.cache.stats["memory_hits"], 1)
        self.assertEqual(self.cache.stats["disk_hits"], 1)
    
    def test_disk_ttl(self):
        """Test disk TTL."""
        # Set a value with short TTL
        self.cache.set("test_key", "test_value")
        
        # Wait for both memory and disk TTL to expire
        time.sleep(2.1)
        
        # Get the value again (should miss)
        value = self.cache.get("test_key")
        self.assertIsNone(value)
        
        # Check miss count
        self.assertEqual(self.cache.stats["misses"], 1)
    
    def test_lfu_eviction(self):
        """Test LFU eviction."""
        # Fill memory cache
        for i in range(15):
            self.cache.set(f"key{i}", f"value{i}")
        
        # Check memory cache size
        self.assertLessEqual(len(self.cache.memory_cache), self.cache.memory_limit)
        
        # Access some keys multiple times
        for _ in range(5):
            self.cache.get("key0")
            self.cache.get("key1")
        
        # Add more items to trigger eviction
        self.cache.set("new_key", "new_value")
        
        # Check that frequently accessed keys are still in memory cache
        self.assertIn("key0", self.cache.memory_cache)
        self.assertIn("key1", self.cache.memory_cache)
    
    def test_delete(self):
        """Test delete method."""
        # Set a value
        self.cache.set("test_key", "test_value")
        
        # Delete the key
        result = self.cache.delete("test_key")
        
        # Check the result
        self.assertTrue(result)
        
        # Check if key exists
        self.assertIsNone(self.cache.get("test_key"))
    
    def test_clear(self):
        """Test clear method."""
        # Set multiple values
        self.cache.set("test_key1", "test_value1")
        self.cache.set("test_key2", "test_value2")
        
        # Clear the cache
        result = self.cache.clear()
        
        # Check the result
        self.assertTrue(result)
        
        # Check if keys exist
        self.assertIsNone(self.cache.get("test_key1"))
        self.assertIsNone(self.cache.get("test_key2"))
    
    def test_stats(self):
        """Test stats method."""
        # Set and get some values
        self.cache.set("test_key1", "test_value1")
        self.cache.set("test_key2", "test_value2")
        self.cache.get("test_key1")
        self.cache.get("non_existent_key")
        
        # Get stats
        stats = self.cache.get_stats()
        
        # Check stats
        self.assertEqual(stats["memory_cache"]["hits"], 1)
        self.assertEqual(stats["overall"]["misses"], 1)
        self.assertEqual(stats["memory_cache"]["size"], 2)
        self.assertEqual(stats["disk_cache"]["size"], 2)
        self.assertEqual(stats["features"]["prefetching"], False)
        self.assertEqual(stats["features"]["compression"], True)
        self.assertEqual(stats["features"]["semantic_search"], False)


if __name__ == "__main__":
    unittest.main()
