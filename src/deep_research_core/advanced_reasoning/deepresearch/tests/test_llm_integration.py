"""
Tests for LLM integration components.

This module contains tests for the LLM action interpreter and observation formatter.
"""

import json
import unittest
from unittest.mock import patch, MagicMock

import pytest

from deep_research_core.rl_tuning.environment.llm_action_interpreter import LLMActionInterpreter
from deep_research_core.rl_tuning.environment.observation_formatter import ObservationFormatter


class TestLLMActionInterpreter(unittest.TestCase):
    """Tests for the LLM action interpreter."""

    def setUp(self):
        """Set up test fixtures."""
        self.llm_mock = MagicMock()
        self.llm_mock.model_instance = MagicMock()
        self.llm_mock.model_instance.generate = MagicMock(return_value='{"action_type": "respond", "content": "Fixed action"}')
        
        self.action_interpreter = LLMActionInterpreter(
            llm_integration=self.llm_mock,
            use_schema_validation=True,
            fix_invalid_actions=True,
            verbose=False
        )

    def test_interpret_action_valid(self):
        """Test interpreting a valid action."""
        # Create a valid action
        action = {
            "action_type": "respond",
            "content": "This is a valid response"
        }
        
        # Interpret action
        interpreted_action = self.action_interpreter.interpret_action(action)
        
        # Check that action was not modified
        self.assertEqual(interpreted_action["action_type"], "respond")
        self.assertEqual(interpreted_action["content"], "This is a valid response")
        self.assertIn("timestamp", interpreted_action)
        
    def test_interpret_action_string(self):
        """Test interpreting an action from a string."""
        # Create an action string
        action_string = "This is a response"
        
        # Interpret action
        interpreted_action = self.action_interpreter.interpret_action(action_string)
        
        # Check that action was parsed correctly
        self.assertEqual(interpreted_action["action_type"], "respond")
        self.assertEqual(interpreted_action["content"], "This is a response")
        self.assertIn("timestamp", interpreted_action)
        
    def test_interpret_action_json_string(self):
        """Test interpreting an action from a JSON string."""
        # Create an action JSON string
        action_string = '{"action_type": "think", "content": "Let me think about this"}'
        
        # Interpret action
        interpreted_action = self.action_interpreter.interpret_action(action_string)
        
        # Check that action was parsed correctly
        self.assertEqual(interpreted_action["action_type"], "think")
        self.assertEqual(interpreted_action["content"], "Let me think about this")
        self.assertIn("timestamp", interpreted_action)
        
    def test_interpret_action_invalid(self):
        """Test interpreting an invalid action."""
        # Create an invalid action
        action = {
            "action_type": "invalid_type",
            "content": "This has an invalid action type"
        }
        
        # Interpret action
        interpreted_action = self.action_interpreter.interpret_action(action)
        
        # Check that action was fixed
        self.assertNotEqual(interpreted_action["action_type"], "invalid_type")
        self.assertIn(interpreted_action["action_type"], ["respond", "think", "use_tool", "query", "search"])
        
    def test_interpret_action_missing_fields(self):
        """Test interpreting an action with missing required fields."""
        # Create an action with missing fields
        action = {
            "action_type": "respond"
            # Missing content field
        }
        
        # Interpret action
        interpreted_action = self.action_interpreter.interpret_action(action)
        
        # Check that action was fixed
        self.assertEqual(interpreted_action["action_type"], "respond")
        self.assertIn("content", interpreted_action)
        
    def test_interpret_action_tool_format(self):
        """Test interpreting a tool action in a special format."""
        # Create a tool action in a special format
        action_string = 'use_tool[search_web](query="Python programming")'
        
        # Interpret action
        interpreted_action = self.action_interpreter.interpret_action(action_string)
        
        # Check that action was parsed correctly
        self.assertEqual(interpreted_action["action_type"], "use_tool")
        self.assertEqual(interpreted_action["tool_name"], "search_web")
        self.assertEqual(interpreted_action["tool_input"]["query"], "Python programming")
        
    def test_interpret_action_with_llm(self):
        """Test interpreting an action using LLM."""
        # Create an invalid action that needs LLM fixing
        action = {
            "action_type": "invalid_type",
            "content": "This has an invalid action type"
        }
        
        # Mock the _apply_simple_fixes method to return the original action
        original_method = self.action_interpreter._apply_simple_fixes
        self.action_interpreter._apply_simple_fixes = MagicMock(return_value=action)
        
        # Interpret action
        interpreted_action = self.action_interpreter.interpret_action(action)
        
        # Restore original method
        self.action_interpreter._apply_simple_fixes = original_method
        
        # Check that LLM was used to fix the action
        self.llm_mock.model_instance.generate.assert_called_once()
        
    def test_format_action_for_agent(self):
        """Test formatting an action for an agent."""
        # Create actions of different types
        respond_action = {"action_type": "respond", "content": "This is a response"}
        think_action = {"action_type": "think", "content": "Let me think about this"}
        tool_action = {
            "action_type": "use_tool",
            "tool_name": "search_web",
            "tool_input": {"query": "Python programming"}
        }
        
        # Format actions
        respond_formatted = self.action_interpreter.format_action_for_agent(respond_action)
        think_formatted = self.action_interpreter.format_action_for_agent(think_action)
        tool_formatted = self.action_interpreter.format_action_for_agent(tool_action)
        
        # Check formatting
        self.assertIn("Response:", respond_formatted)
        self.assertIn("This is a response", respond_formatted)
        
        self.assertIn("Thinking:", think_formatted)
        self.assertIn("Let me think about this", think_formatted)
        
        self.assertIn("Tool: search_web", tool_formatted)
        self.assertIn("Input:", tool_formatted)
        self.assertIn("query", tool_formatted)
        
    def test_get_stats(self):
        """Test getting statistics."""
        # Create some actions to interpret
        valid_action = {"action_type": "respond", "content": "Valid"}
        invalid_action = {"action_type": "invalid", "content": "Invalid"}
        
        # Interpret actions
        self.action_interpreter.interpret_action(valid_action)
        self.action_interpreter.interpret_action(invalid_action)
        
        # Get stats
        stats = self.action_interpreter.get_stats()
        
        # Check stats
        self.assertEqual(stats["interpreted_actions"], 2)
        self.assertGreaterEqual(stats["invalid_actions"], 1)
        
    def test_reset_stats(self):
        """Test resetting statistics."""
        # Create some actions to interpret
        valid_action = {"action_type": "respond", "content": "Valid"}
        invalid_action = {"action_type": "invalid", "content": "Invalid"}
        
        # Interpret actions
        self.action_interpreter.interpret_action(valid_action)
        self.action_interpreter.interpret_action(invalid_action)
        
        # Reset stats
        self.action_interpreter.reset_stats()
        
        # Get stats
        stats = self.action_interpreter.get_stats()
        
        # Check stats
        self.assertEqual(stats["interpreted_actions"], 0)
        self.assertEqual(stats["invalid_actions"], 0)
        self.assertEqual(stats["fixed_actions"], 0)


class TestObservationFormatter(unittest.TestCase):
    """Tests for the observation formatter."""

    def setUp(self):
        """Set up test fixtures."""
        self.formatter = ObservationFormatter(
            max_history_length=5,
            include_timestamps=False,
            include_metrics=True,
            verbose=False
        )
        
        # Create a sample observation
        self.observation = {
            "query": "What is Python?",
            "context": "User is asking about programming languages",
            "tools": [
                {"name": "search_web", "description": "Search the web for information"},
                {"name": "run_code", "description": "Run Python code"}
            ],
            "history": [
                {"action_type": "respond", "content": "How can I help you?", "timestamp": 1000},
                {"action_type": "use_tool", "tool_name": "search_web", "tool_input": {"query": "Python"}, "timestamp": 1001}
            ],
            "state": {
                "step_count": 2,
                "is_success": False,
                "is_failure": False,
                "last_response": "How can I help you?",
                "last_tool_result": {"result": "Python is a programming language"},
                "metrics": {"response_quality": 0.8}
            }
        }

    def test_format_observation_text(self):
        """Test formatting an observation as text."""
        # Format observation
        formatted = self.formatter.format_observation(self.observation, format_type="text")
        
        # Check formatting
        self.assertIsInstance(formatted, str)
        self.assertIn("What is Python?", formatted)
        self.assertIn("User is asking about programming languages", formatted)
        self.assertIn("search_web", formatted)
        self.assertIn("run_code", formatted)
        self.assertIn("How can I help you?", formatted)
        self.assertIn("search_web", formatted)
        
    def test_format_observation_json(self):
        """Test formatting an observation as JSON."""
        # Format observation
        formatted = self.formatter.format_observation(self.observation, format_type="json")
        
        # Check formatting
        self.assertIsInstance(formatted, dict)
        self.assertIn("query", formatted)
        self.assertIn("context", formatted)
        self.assertIn("tools", formatted)
        self.assertIn("history", formatted)
        self.assertIn("state", formatted)
        
    def test_format_observation_llm_prompt(self):
        """Test formatting an observation as an LLM prompt."""
        # Format observation
        formatted = self.formatter.format_observation(self.observation, format_type="llm_prompt")
        
        # Check formatting
        self.assertIsInstance(formatted, str)
        self.assertIn("QUERY:", formatted)
        self.assertIn("CONTEXT:", formatted)
        self.assertIn("AVAILABLE TOOLS:", formatted)
        self.assertIn("HISTORY:", formatted)
        self.assertIn("CURRENT STATE:", formatted)
        self.assertIn("Think step by step", formatted)
        
    def test_format_observation_human(self):
        """Test formatting an observation for human consumption."""
        # Format observation
        formatted = self.formatter.format_observation(self.observation, format_type="human")
        
        # Check formatting
        self.assertIsInstance(formatted, str)
        self.assertIn("Task:", formatted)
        self.assertIn("Background:", formatted)
        self.assertIn("Available Tools:", formatted)
        self.assertIn("Previous Actions:", formatted)
        self.assertIn("Current Status:", formatted)
        
    def test_format_observation_custom_template(self):
        """Test formatting an observation with a custom template."""
        # Create a custom template
        custom_template = """
        Q: {query}
        
        C: {context}
        
        T: {tools}
        
        H: {history}
        
        S: {state}
        """
        
        # Format observation
        formatted = self.formatter.format_observation(
            self.observation,
            format_type="text",
            custom_template=custom_template
        )
        
        # Check formatting
        self.assertIsInstance(formatted, str)
        self.assertIn("Q: What is Python?", formatted)
        self.assertIn("C: User is asking about programming languages", formatted)
        self.assertIn("T:", formatted)
        self.assertIn("H:", formatted)
        self.assertIn("S:", formatted)
        
    def test_format_for_llm(self):
        """Test formatting an observation specifically for an LLM."""
        # Format observation for LLM
        formatted = self.formatter.format_for_llm(self.observation)
        
        # Check formatting
        self.assertIsInstance(formatted, dict)
        self.assertIn("system_prompt", formatted)
        self.assertIn("user_prompt", formatted)
        self.assertIn("QUERY:", formatted["user_prompt"])
        self.assertIn("CONTEXT:", formatted["user_prompt"])
        self.assertIn("AVAILABLE TOOLS:", formatted["user_prompt"])
        self.assertIn("HISTORY:", formatted["user_prompt"])
        self.assertIn("CURRENT STATE:", formatted["user_prompt"])
        
    def test_format_for_human(self):
        """Test formatting an observation specifically for human consumption."""
        # Format observation for human
        formatted = self.formatter.format_for_human(self.observation)
        
        # Check formatting
        self.assertIsInstance(formatted, str)
        self.assertIn("Task:", formatted)
        self.assertIn("Background:", formatted)
        self.assertIn("Available Tools:", formatted)
        self.assertIn("Previous Actions:", formatted)
        self.assertIn("Current Status:", formatted)
        
    def test_format_for_agent(self):
        """Test formatting an observation specifically for an agent."""
        # Format observation for different agent types
        json_formatted = self.formatter.format_for_agent(self.observation, agent_type="json")
        text_formatted = self.formatter.format_for_agent(self.observation, agent_type="text")
        llm_formatted = self.formatter.format_for_agent(self.observation, agent_type="llm")
        
        # Check formatting
        self.assertIsInstance(json_formatted, dict)
        
        self.assertIsInstance(text_formatted, dict)
        self.assertIn("observation", text_formatted)
        
        self.assertIsInstance(llm_formatted, dict)
        self.assertIn("system_prompt", llm_formatted)
        self.assertIn("user_prompt", llm_formatted)
        
    def test_add_format_template(self):
        """Test adding a new format template."""
        # Add a new template
        new_template = "Query: {query}\nContext: {context}"
        self.formatter.add_format_template("simple", new_template)
        
        # Format observation with new template
        formatted = self.formatter.format_observation(self.observation, format_type="simple")
        
        # Check formatting
        self.assertIsInstance(formatted, str)
        self.assertIn("Query: What is Python?", formatted)
        self.assertIn("Context: User is asking about programming languages", formatted)
        
    def test_get_format_template(self):
        """Test getting a format template."""
        # Get an existing template
        template = self.formatter.get_format_template("text")
        
        # Check template
        self.assertIsNotNone(template)
        self.assertIsInstance(template, str)
        
    def test_list_format_templates(self):
        """Test listing format templates."""
        # List templates
        templates = self.formatter.list_format_templates()
        
        # Check templates
        self.assertIsInstance(templates, list)
        self.assertIn("text", templates)
        self.assertIn("json", templates)
        self.assertIn("llm_prompt", templates)
        self.assertIn("human", templates)


if __name__ == "__main__":
    unittest.main()
