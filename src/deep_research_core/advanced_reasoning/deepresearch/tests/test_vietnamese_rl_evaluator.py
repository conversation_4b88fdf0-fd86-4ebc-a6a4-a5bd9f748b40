"""
Tests for Vietnamese RL Evaluator.
"""

import unittest
from deep_research_core.evaluation.vietnamese_rl_evaluator import VietnameseRLEvaluator

class TestVietnameseRLEvaluator(unittest.TestCase):
    """Test cases for VietnameseRLEvaluator."""

    def setUp(self):
        """Set up test fixtures."""
        self.evaluator = VietnameseRLEvaluator()
        
        # Sample Vietnamese responses for testing
        self.good_response = """
        Học tăng cường (Reinforcement Learning) là một phương pháp học máy trong đó một tác tử (agent) 
        học cách đưa ra quyết định bằng cách tương tác với môi trường. Phương pháp PPO (Proximal Policy Optimization) 
        là một thuật toán học tăng cường hiện đại, đư<PERSON><PERSON> phát triển bởi OpenAI, giúp cải thiện hiệu suất 
        và ổn định trong quá trình huấn luyện.
        
        <PERSON>hi áp dụng PPO cho mô hình ngôn ngữ, chúng ta thường sử dụng phần thưởng (reward) dựa trên 
        phản hồi của con người hoặc mô hình đánh giá. Quá trình này còn được gọi là RLHF 
        (Reinforcement Learning from Human Feedback).
        
        Ưu điểm của PPO bao gồm:
        1. Ổn định trong quá trình huấn luyện
        2. Hiệu quả về mặt tính toán
        3. Dễ triển khai và tinh chỉnh
        
        Tuy nhiên, PPO cũng có một số hạn chế như:
        1. Cần nhiều dữ liệu để hội tụ
        2. Nhạy cảm với việc lựa chọn siêu tham số
        
        Để cải thiện hiệu suất của PPO trong ứng dụng xử lý ngôn ngữ tiếng Việt, 
        chúng ta có thể kết hợp với các kỹ thuật như PEFT (Parameter-Efficient Fine-Tuning) 
        và LoRA (Low-Rank Adaptation).
        """
        
        self.poor_response = """
        Tôi nghĩ học tăng cường có lẽ là một phương pháp học máy, nhưng tôi không chắc chắn lắm. 
        Có thể nó liên quan đến việc tác tử học từ môi trường, nhưng tôi không rõ chi tiết.
        
        PPO có lẽ là một thuật toán gì đó trong học tăng cường, nhưng tôi không biết nó hoạt động như thế nào.
        
        Tôi đoán RLHF có liên quan đến phản hồi của con người, nhưng không đủ thông tin để nói rõ hơn.
        """
        
        self.task_description = """
        Giải thích về phương pháp học tăng cường (Reinforcement Learning) và thuật toán PPO (Proximal Policy Optimization) 
        trong bối cảnh huấn luyện mô hình ngôn ngữ. Nêu ưu điểm và hạn chế của PPO, cũng như cách áp dụng cho tiếng Việt.
        """
        
        self.expected_keywords = [
            "học tăng cường", "reinforcement learning", "ppo", "proximal policy optimization", 
            "tác tử", "agent", "môi trường", "phần thưởng", "reward", "rlhf", "human feedback"
        ]
        
        self.previous_responses = [
            """
            Học tăng cường (Reinforcement Learning) là một lĩnh vực của học máy tập trung vào việc 
            tác tử (agent) học cách tương tác với môi trường để tối đa hóa phần thưởng (reward) tích lũy.
            
            Trong bối cảnh mô hình ngôn ngữ, RLHF (Reinforcement Learning from Human Feedback) 
            là phương pháp sử dụng phản hồi của con người để huấn luyện mô hình ngôn ngữ thông qua học tăng cường.
            """,
            
            """
            PPO (Proximal Policy Optimization) là một thuật toán học tăng cường được phát triển bởi OpenAI. 
            Thuật toán này cải thiện hiệu suất của các phương pháp policy gradient truyền thống 
            bằng cách giới hạn sự thay đổi của chính sách (policy) giữa các lần cập nhật.
            
            Khi áp dụng cho mô hình ngôn ngữ, PPO giúp mô hình học cách tạo ra văn bản 
            tối ưu hóa theo các tiêu chí được định nghĩa bởi hàm phần thưởng (reward function).
            """
        ]

    def test_evaluate_rl_response_quality(self):
        """Test evaluating RL response quality."""
        good_score = self.evaluator.evaluate_rl_response_quality(self.good_response)
        poor_score = self.evaluator.evaluate_rl_response_quality(self.poor_response)
        
        # Good response should have higher score than poor response
        self.assertGreater(good_score, poor_score)
        
        # Good response should have reasonably high score
        self.assertGreater(good_score, 0.5)
        
        # Poor response should have low score
        self.assertLess(poor_score, 0.5)

    def test_evaluate_rl_consistency(self):
        """Test evaluating RL consistency."""
        # Test with previous responses and good response
        all_responses = self.previous_responses + [self.good_response]
        consistency_score = self.evaluator.evaluate_rl_consistency(all_responses)
        
        # Consistency score should be reasonable
        self.assertGreaterEqual(consistency_score, 0.0)
        self.assertLessEqual(consistency_score, 1.0)
        
        # Test with empty list
        empty_consistency = self.evaluator.evaluate_rl_consistency([])
        self.assertEqual(empty_consistency, 1.0)  # Default for not enough responses

    def test_evaluate_rl_performance(self):
        """Test evaluating RL performance."""
        # Test with good response
        performance_score = self.evaluator.evaluate_rl_performance(
            self.good_response,
            reference_response=self.previous_responses[1],
            task_description=self.task_description
        )
        
        # Performance score should be reasonable
        self.assertGreaterEqual(performance_score, 0.0)
        self.assertLessEqual(performance_score, 1.0)
        
        # Test with poor response
        poor_performance = self.evaluator.evaluate_rl_performance(
            self.poor_response,
            reference_response=self.previous_responses[1],
            task_description=self.task_description
        )
        
        # Good response should have higher performance than poor response
        self.assertGreater(performance_score, poor_performance)

    def test_evaluate_rl_task_completion(self):
        """Test evaluating RL task completion."""
        # Test with good response
        completion_score = self.evaluator.evaluate_rl_task_completion(
            self.good_response,
            task_description=self.task_description,
            expected_keywords=self.expected_keywords
        )
        
        # Completion score should be reasonable
        self.assertGreaterEqual(completion_score, 0.0)
        self.assertLessEqual(completion_score, 1.0)
        
        # Test with poor response
        poor_completion = self.evaluator.evaluate_rl_task_completion(
            self.poor_response,
            task_description=self.task_description,
            expected_keywords=self.expected_keywords
        )
        
        # Good response should have higher completion score than poor response
        self.assertGreater(completion_score, poor_completion)

    def test_evaluate_all_metrics(self):
        """Test evaluating all metrics."""
        # Test with good response
        all_metrics = self.evaluator.evaluate_all_metrics(
            self.good_response,
            reference_response=self.previous_responses[1],
            task_description=self.task_description,
            expected_keywords=self.expected_keywords,
            previous_responses=self.previous_responses
        )
        
        # Check that all expected metrics are present
        expected_metrics = [
            "diacritic_consistency", "dialect_consistency", "reasoning_quality",
            "lexical_diversity", "grammatical_correctness", "rl_response_quality",
            "rl_performance", "rl_task_completion", "rl_consistency", "overall_score"
        ]
        
        for metric in expected_metrics:
            self.assertIn(metric, all_metrics)
            self.assertGreaterEqual(all_metrics[metric], 0.0)
            self.assertLessEqual(all_metrics[metric], 1.0)
        
        # Overall score should be reasonable
        self.assertGreaterEqual(all_metrics["overall_score"], 0.0)
        self.assertLessEqual(all_metrics["overall_score"], 1.0)

if __name__ == "__main__":
    unittest.main()
