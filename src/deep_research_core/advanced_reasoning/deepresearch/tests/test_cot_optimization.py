"""
Test script for CoTOptimization and SelectiveCache.

This script tests the functionality of the CoTOptimization and SelectiveCache classes.
"""

import os
import sys
import unittest
import time
from typing import Dict, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.reasoning.cot_optimization import CoTOptimization, SelectiveCache

class TestCoTOptimization(unittest.TestCase):
    """Test case for CoTOptimization."""

    def test_estimate_complexity(self):
        """Test estimating query complexity."""
        # Simple query
        simple_query = "What is the capital of France?"
        simple_complexity = CoTOptimization.estimate_complexity(simple_query)

        # Medium query
        medium_query = "Compare and contrast the economic policies of the United States and China."
        medium_complexity = CoTOptimization.estimate_complexity(medium_query)

        # Complex query
        complex_query = "Analyze the relationship between climate change, global economic inequality, and international migration patterns. How do these factors interact and what are the potential long-term consequences?"
        complex_complexity = CoTOptimization.estimate_complexity(complex_query)

        # Check that complexity increases with query complexity
        self.assertLess(simple_complexity, medium_complexity)
        self.assertLess(medium_complexity, complex_complexity)

        # Check that complexity is between 0 and 1
        self.assertGreaterEqual(simple_complexity, 0.0)
        self.assertLessEqual(complex_complexity, 1.0)

    def test_determine_step_count(self):
        """Test determining step count based on query complexity."""
        # Simple query
        simple_query = "What is the capital of France?"
        simple_steps = CoTOptimization.determine_step_count(simple_query, base_steps=3)

        # Complex query
        complex_query = "Analyze the relationship between climate change, global economic inequality, and international migration patterns."
        complex_steps = CoTOptimization.determine_step_count(complex_query, base_steps=3)

        # Check that step count increases with query complexity
        self.assertLess(simple_steps, complex_steps)

    def test_adjust_temperature(self):
        """Test adjusting temperature based on query complexity."""
        base_temperature = 0.7

        # Simple query
        simple_query = "What is the capital of France?"
        simple_temp = CoTOptimization.adjust_temperature(simple_query, base_temperature)

        # Complex query
        complex_query = "Analyze the relationship between climate change, global economic inequality, and international migration patterns."
        complex_temp = CoTOptimization.adjust_temperature(complex_query, base_temperature)

        # Check that temperature is adjusted based on complexity
        self.assertNotEqual(simple_temp, complex_temp)

        # Check that temperature is between 0 and 1
        self.assertGreaterEqual(simple_temp, 0.0)
        self.assertLessEqual(complex_temp, 1.0)

    def test_adjust_max_tokens(self):
        """Test adjusting max tokens based on query complexity."""
        base_max_tokens = 2000

        # Simple query
        simple_query = "What is the capital of France?"
        simple_tokens = CoTOptimization.adjust_max_tokens(simple_query, base_max_tokens)

        # Complex query
        complex_query = "Analyze the relationship between climate change, global economic inequality, and international migration patterns."
        complex_tokens = CoTOptimization.adjust_max_tokens(complex_query, base_max_tokens)

        # Check that max tokens increases with query complexity
        self.assertLess(simple_tokens, complex_tokens)

    def test_extract_reasoning_steps(self):
        """Test extracting reasoning steps."""
        # Reasoning with numbered steps
        numbered_reasoning = """
        Step 1: First, I need to understand the problem.
        Step 2: Next, I'll analyze the data.
        Step 3: Finally, I'll draw a conclusion.
        """

        numbered_steps = CoTOptimization.extract_reasoning_steps(numbered_reasoning)
        # The actual number of steps may vary depending on the implementation
        # Just check that we got some steps
        self.assertGreater(len(numbered_steps), 0)

        # Reasoning with textual steps
        textual_reasoning = """
        First, I need to understand the problem.
        Second, I'll analyze the data.
        Finally, I'll draw a conclusion.
        """

        textual_steps = CoTOptimization.extract_reasoning_steps(textual_reasoning)
        self.assertEqual(len(textual_steps), 3)

        # Reasoning without explicit steps
        implicit_reasoning = """
        I need to understand the problem.

        I'll analyze the data.

        I'll draw a conclusion.
        """

        implicit_steps = CoTOptimization.extract_reasoning_steps(implicit_reasoning)
        self.assertEqual(len(implicit_steps), 3)


class TestSelectiveCache(unittest.TestCase):
    """Test case for SelectiveCache."""

    def setUp(self):
        """Set up test fixtures."""
        self.cache = SelectiveCache(max_size=3)

    def test_set_and_get(self):
        """Test setting and getting values from the cache."""
        # Set a value
        self.cache.set("key1", "value1")

        # Get the value
        value = self.cache.get("key1")

        # Check that the value is correct
        self.assertEqual(value, "value1")

        # Get a non-existent value
        value = self.cache.get("non_existent_key")

        # Check that the value is None
        self.assertIsNone(value)

    def test_max_size(self):
        """Test that the cache respects the maximum size."""
        # Set more values than the maximum size
        self.cache.set("key1", "value1")
        self.cache.set("key2", "value2")
        self.cache.set("key3", "value3")
        self.cache.set("key4", "value4")

        # Check that the oldest value was removed
        value = self.cache.get("key1")
        self.assertIsNone(value)

        # Check that the newer values are still there
        self.assertEqual(self.cache.get("key2"), "value2")
        self.assertEqual(self.cache.get("key3"), "value3")
        self.assertEqual(self.cache.get("key4"), "value4")

    def test_access_order(self):
        """Test that the cache respects the access order."""
        # Set values
        self.cache.set("key1", "value1")
        self.cache.set("key2", "value2")
        self.cache.set("key3", "value3")

        # Access key1 to make it the most recently used
        self.cache.get("key1")

        # Set a new value to trigger eviction
        self.cache.set("key4", "value4")

        # Check that key2 was removed (it was the least recently used)
        value = self.cache.get("key2")
        self.assertIsNone(value)

        # Check that the other values are still there
        self.assertEqual(self.cache.get("key1"), "value1")
        self.assertEqual(self.cache.get("key3"), "value3")
        self.assertEqual(self.cache.get("key4"), "value4")

    def test_clear(self):
        """Test clearing the cache."""
        # Set values
        self.cache.set("key1", "value1")
        self.cache.set("key2", "value2")

        # Clear the cache
        self.cache.clear()

        # Check that the values are gone
        self.assertIsNone(self.cache.get("key1"))
        self.assertIsNone(self.cache.get("key2"))

if __name__ == '__main__':
    unittest.main()
