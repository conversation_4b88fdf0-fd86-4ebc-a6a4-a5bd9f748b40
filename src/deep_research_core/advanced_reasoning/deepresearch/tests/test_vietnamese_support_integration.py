"""
Integration tests for Vietnamese language support.

This module tests the integration of Vietnamese language support across
different components of the Deep Research Core library.
"""

import unittest
from unittest.mock import MagicMock, patch
import os
import tempfile
import shutil
import json

from deep_research_core.rl_tuning.trajectories.trajectory_collector import TrajectoryCollector
from deep_research_core.rl_tuning.environment.vietnamese_support import VietnameseSupport
from deep_research_core.rl_tuning.environment import ReasoningEnvironment
from deep_research_core.reasoning.cot import CoTReasoning


class TestVietnameseSupportIntegration(unittest.TestCase):
    """Integration tests for Vietnamese language support."""

    def setUp(self):
        """Set up test environment."""
        # Create a temporary directory
        self.temp_dir = tempfile.mkdtemp()
        
        # Create mock model
        self.mock_model = MagicMock()
        self.mock_model.generate.return_value = '{"action_type": "think", "content": "Test action"}'
        
        # Create mock environment
        self.mock_env = MagicMock(spec=ReasoningEnvironment)
        self.mock_env.reasoning_type = "cot"
        self.mock_env.get_action_space.return_value = {
            "type": "object",
            "properties": {
                "action_type": {
                    "type": "string",
                    "enum": ["think", "answer", "use_tool"]
                },
                "content": {"type": "string"}
            },
            "required": ["action_type", "content"]
        }
        self.mock_env.step.return_value = (
            {"observation": "Test observation"},
            1.0,
            False,
            {"info": "Test info"}
        )
        
        # Create test tasks
        self.test_tasks = [
            {"id": "task1", "query": "What is the capital of Vietnam?"},
            {"id": "task2", "query": "Thủ đô của Việt Nam là gì?", "language": "vi"},
            {"id": "task3", "query": "Explain the history of Vietnam."}
        ]
        
        # Initialize Vietnamese support
        self.vietnamese_support = VietnameseSupport(
            fallback_to_english=False,
            verbose=True
        )
        
        # Mock Vietnamese utilities
        self.patch_detect = patch('deep_research_core.utils.vietnamese_utils.detect_vietnamese')
        self.mock_detect = self.patch_detect.start()
        self.mock_detect.side_effect = lambda text: "Việt" in text or "việt" in text or text and text.startswith("Thủ đô")
        
        self.patch_translate = patch('deep_research_core.utils.vietnamese_utils.translate_to_vietnamese')
        self.mock_translate = self.patch_translate.start()
        self.mock_translate.side_effect = lambda text: f"[VI] {text}"

    def tearDown(self):
        """Clean up after tests."""
        # Remove temporary directory
        shutil.rmtree(self.temp_dir)
        
        # Stop patches
        self.patch_detect.stop()
        self.patch_translate.stop()

    def test_trajectory_collector_vietnamese_support(self):
        """Test TrajectoryCollector with Vietnamese support."""
        # Initialize trajectory collector with Vietnamese support
        collector = TrajectoryCollector(
            storage_path=os.path.join(self.temp_dir, "trajectories"),
            models={"test_model": self.mock_model},
            environments={"test_env": self.mock_env},
            tasks=self.test_tasks,
            max_trajectories_per_task=2,
            save_interval=1,
            verbose=True,
            vietnamese_support=True,
            vietnamese_config={
                "collect_bilingual": True,
                "translate_tasks": True,
                "vietnamese_ratio": 0.5
            }
        )
        
        # Check that Vietnamese support is enabled
        self.assertTrue(collector.vietnamese_support)
        self.assertEqual(collector.vietnamese_config["vietnamese_ratio"], 0.5)
        
        # Check that Vietnamese utilities are available
        self.assertTrue(hasattr(collector, "detect_vietnamese"))
        self.assertTrue(hasattr(collector, "translate_to_vietnamese"))
        
        # Test system prompt creation
        system_prompt = collector._create_system_prompt(
            env=self.mock_env,
            action_space={"type": "object"},
            trajectory={"task": {"query": "Thủ đô của Việt Nam là gì?", "language": "vi"}}
        )
        
        # Check that system prompt is in Vietnamese
        self.assertIn("Bạn là một tác nhân thông minh", system_prompt)
        self.assertIn("NHIỆM VỤ", system_prompt)
        self.assertIn("Thủ đô của Việt Nam là gì?", system_prompt)
        
        # Test bilingual system prompt
        bilingual_prompt = collector._create_system_prompt(
            env=self.mock_env,
            action_space={"type": "object"},
            trajectory={"task": {"query": "What is the capital of Vietnam?"}, "is_bilingual": True}
        )
        
        # Check that bilingual instruction is included
        self.assertIn("IMPORTANT: You should provide your reasoning in both Vietnamese and English", bilingual_prompt)
        self.assertIn("QUAN TRỌNG: Bạn nên cung cấp lý luận của mình bằng cả tiếng Việt và tiếng Anh", bilingual_prompt)

    def test_vietnamese_support_action_processing(self):
        """Test VietnameseSupport action processing."""
        # Test Vietnamese to English action processing
        vi_action = {
            "action_type": "think",
            "content": "Tôi đang suy nghĩ về vấn đề này.",
            "tool_input": "Đây là dữ liệu đầu vào."
        }
        
        processed_vi_action = self.vietnamese_support.process_action(vi_action, source_lang="vi")
        
        # Check that content and tool_input were processed
        self.assertNotEqual(processed_vi_action["content"], vi_action["content"])
        self.assertNotEqual(processed_vi_action["tool_input"], vi_action["tool_input"])
        
        # Test English to Vietnamese action processing
        en_action = {
            "action_type": "think",
            "content": "I am thinking about this problem.",
            "tool_input": "This is the input data."
        }
        
        processed_en_action = self.vietnamese_support.process_action(en_action, source_lang="en")
        
        # Check that content and tool_input were processed
        self.assertNotEqual(processed_en_action["content"], en_action["content"])
        self.assertNotEqual(processed_en_action["tool_input"], en_action["tool_input"])

    def test_vietnamese_support_response_enhancement(self):
        """Test VietnameseSupport response enhancement."""
        # Test response enhancement
        response = "Đây là câu trả lời đơn giản."
        enhanced = self.vietnamese_support.enhance_vietnamese_response(response)
        
        # Check that response was enhanced
        self.assertNotEqual(enhanced, response)
        self.assertGreater(len(enhanced), len(response))
        
        # Test that non-Vietnamese responses are not enhanced
        en_response = "This is a simple response."
        en_enhanced = self.vietnamese_support.enhance_vietnamese_response(en_response)
        
        # Check that response was not enhanced
        self.assertEqual(en_enhanced, en_response)

    def test_vietnamese_support_text_analysis(self):
        """Test VietnameseSupport text analysis."""
        # Test Vietnamese text complexity analysis
        text = "Hà Nội là thủ đô của Việt Nam. Đây là một thành phố có lịch sử lâu đời với nhiều di tích văn hóa quan trọng."
        complexity = self.vietnamese_support.analyze_vietnamese_text_complexity(text)
        
        # Check that complexity analysis was performed
        self.assertIsInstance(complexity, dict)
        self.assertIn("complexity", complexity)
        self.assertIn("score", complexity)
        self.assertIn("metrics", complexity)
        
        # Test Vietnamese dialect detection
        dialect = self.vietnamese_support.detect_vietnamese_dialect(text)
        
        # Check that dialect was detected
        self.assertIsNotNone(dialect)
        self.assertNotEqual(dialect, "unknown")

    def test_end_to_end_vietnamese_trajectory(self):
        """Test end-to-end Vietnamese trajectory collection."""
        # Initialize trajectory collector with Vietnamese support
        collector = TrajectoryCollector(
            storage_path=os.path.join(self.temp_dir, "trajectories"),
            models={"test_model": self.mock_model},
            environments={"test_env": self.mock_env},
            tasks=self.test_tasks,
            max_trajectories_per_task=1,
            save_interval=1,
            verbose=True,
            vietnamese_support=True,
            vietnamese_config={
                "collect_bilingual": True,
                "translate_tasks": True,
                "vietnamese_ratio": 0.5
            }
        )
        
        # Collect Vietnamese trajectories
        num_collected = collector.collect_vietnamese_trajectories(
            model_names=["test_model"],
            env_names=["test_env"],
            max_per_task=1
        )
        
        # Check that trajectories were collected
        self.assertGreater(num_collected, 0)
        self.assertGreater(collector.vietnamese_trajectories, 0)
        
        # Check that trajectories were saved
        trajectory_files = os.listdir(os.path.join(self.temp_dir, "trajectories", "raw"))
        self.assertGreater(len(trajectory_files), 0)
        
        # Check trajectory content
        with open(os.path.join(self.temp_dir, "trajectories", "raw", trajectory_files[0]), "r") as f:
            trajectories = json.load(f)
        
        # Check that trajectories have Vietnamese markers
        for trajectory in trajectories:
            if "task" in trajectory and "language" in trajectory["task"]:
                if trajectory["task"]["language"] == "vi":
                    self.assertTrue(trajectory["task"].get("is_vietnamese", False))


if __name__ == "__main__":
    unittest.main()
