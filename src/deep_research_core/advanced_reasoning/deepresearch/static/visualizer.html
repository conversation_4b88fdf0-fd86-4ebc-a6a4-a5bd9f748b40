<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ToT-RAG Visualizer - Deep Research Core</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding-top: 2rem;
            padding-bottom: 2rem;
        }
        .visualization-container {
            margin-top: 2rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 1rem;
        }
        .viz-tabs {
            margin-bottom: 1rem;
        }
        .viz-content {
            min-height: 400px;
        }
        pre.json {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            max-height: 300px;
            overflow: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="mb-4">
            <h1>ToT-RAG Visualizer</h1>
            <p class="lead">Interactive visualization tool for Tree of Thought with Retrieval-Augmented Generation</p>
        </header>

        <div class="row">
            <div class="col-md-5">
                <div class="card">
                    <div class="card-header">
                        <h5>Input ToT-RAG Data</h5>
                    </div>
                    <div class="card-body">
                        <form id="visualization-form">
                            <div class="mb-3">
                                <label for="query" class="form-label">Query</label>
                                <input type="text" class="form-control" id="query" required>
                            </div>
                            <div class="mb-3">
                                <label for="best-paths" class="form-label">Best Paths (JSON)</label>
                                <textarea class="form-control" id="best-paths" rows="6" required></textarea>
                                <div class="form-text">Format: [[score, "path text"], [score, "path text"], ...]</div>
                            </div>
                            <div class="mb-3">
                                <label for="documents" class="form-label">Retrieved Documents (JSON, optional)</label>
                                <textarea class="form-control" id="documents" rows="4"></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="filename-prefix" class="form-label">Filename Prefix (optional)</label>
                                <input type="text" class="form-control" id="filename-prefix">
                            </div>
                            <button type="submit" class="btn btn-primary">Generate Visualizations</button>
                        </form>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h5>Example Data</h5>
                    </div>
                    <div class="card-body">
                        <button id="load-example" class="btn btn-outline-secondary">Load Example Data</button>
                    </div>
                </div>
            </div>

            <div class="col-md-7">
                <div id="results-container" class="visualization-container d-none">
                    <ul class="nav nav-tabs viz-tabs" id="viz-tabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="tree-tab" data-bs-toggle="tab" data-bs-target="#tree-viz" type="button" role="tab">Tree Visualization</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="documents-tab" data-bs-toggle="tab" data-bs-target="#documents-viz" type="button" role="tab">Documents</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="interactive-tab" data-bs-toggle="tab" data-bs-target="#interactive-viz" type="button" role="tab">Interactive</button>
                        </li>
                    </ul>
                    <div class="tab-content viz-content">
                        <div class="tab-pane fade show active" id="tree-viz" role="tabpanel">
                            <div id="tree-image-container" class="text-center"></div>
                        </div>
                        <div class="tab-pane fade" id="documents-viz" role="tabpanel">
                            <div id="documents-image-container" class="text-center"></div>
                        </div>
                        <div class="tab-pane fade" id="interactive-viz" role="tabpanel">
                            <div class="mb-3">
                                <p>Open the interactive dashboard in a new tab:</p>
                                <a id="interactive-link" href="#" target="_blank" class="btn btn-outline-primary">Open Interactive Dashboard</a>
                            </div>
                            <div>
                                <iframe id="interactive-iframe" src="" width="100%" height="400" style="border:none;"></iframe>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="loading-container" class="text-center d-none">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Generating visualizations...</p>
                </div>

                <div id="error-container" class="alert alert-danger mt-3 d-none"></div>

                <div id="api-response" class="mt-3 d-none">
                    <h5>API Response</h5>
                    <pre class="json" id="api-response-json"></pre>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('visualization-form');
            const loadExampleBtn = document.getElementById('load-example');
            const resultsContainer = document.getElementById('results-container');
            const loadingContainer = document.getElementById('loading-container');
            const errorContainer = document.getElementById('error-container');
            const apiResponseContainer = document.getElementById('api-response');
            const apiResponseJson = document.getElementById('api-response-json');
            
            // Example data
            const exampleData = {
                query: "What are the main benefits of tree of thought reasoning?",
                best_paths: [
                    [9.5, "First, let's understand what Tree of Thought (ToT) reasoning is.\n\nTree of Thought is an approach where an AI explores multiple reasoning paths simultaneously, branching out like a tree, instead of following a single chain of thought.\n\nNow, the main benefits:\n\n1. Improved problem-solving through exploration of multiple solution paths\n2. Better handling of complex reasoning tasks that require considering alternatives\n3. Reduced likelihood of getting stuck in reasoning dead-ends\n4. More robust conclusions by comparing different reasoning approaches\n5. Ability to backtrack and explore different branches when one path doesn't yield good results"],
                    [8.7, "Tree of Thought reasoning offers several key benefits:\n\n1. Parallel exploration: Instead of following a single line of reasoning, ToT explores multiple paths simultaneously.\n\n2. Improved decision-making: By considering various alternatives, the AI can make more informed choices.\n\n3. Better handling of uncertainty: When faced with ambiguous information, ToT can explore different interpretations.\n\n4. Enhanced problem-solving: Complex problems often require exploring different solution strategies, which ToT naturally supports.\n\n5. Reduced errors: By not committing to a single reasoning path, ToT reduces the risk of making critical reasoning errors."]
                ],
                documents: [
                    {
                        "content": "Tree of Thought (ToT) is a problem-solving framework that enhances language models' reasoning by encouraging them to explore multiple different reasoning paths for solving a problem and then selecting the most promising paths to continue the search process. This approach allows models to perform deliberate decision making through exploration, evaluation, and selection among multiple reasoning paths.",
                        "source": "Research Paper",
                        "title": "Tree of Thought: Deliberate Problem Solving with Large Language Models"
                    },
                    {
                        "content": "Unlike the single-path Chain of Thought approach, Tree of Thought enables language models to consider multiple reasoning paths, backtrack as needed, and make global decisions by evaluating states using value functions. This method has shown significant improvements in solving complex reasoning tasks.",
                        "source": "AI Blog",
                        "title": "Advanced Reasoning Techniques in AI"
                    }
                ]
            };
            
            // Load example data
            loadExampleBtn.addEventListener('click', function() {
                document.getElementById('query').value = exampleData.query;
                document.getElementById('best-paths').value = JSON.stringify(exampleData.best_paths, null, 2);
                document.getElementById('documents').value = JSON.stringify(exampleData.documents, null, 2);
                document.getElementById('filename-prefix').value = "tot_example";
            });
            
            // Form submission
            form.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                // Show loading, hide results and errors
                loadingContainer.classList.remove('d-none');
                resultsContainer.classList.add('d-none');
                errorContainer.classList.add('d-none');
                apiResponseContainer.classList.add('d-none');
                
                try {
                    // Parse form data
                    const query = document.getElementById('query').value;
                    const bestPathsJson = document.getElementById('best-paths').value;
                    const documentsJson = document.getElementById('documents').value;
                    const filenamePrefix = document.getElementById('filename-prefix').value;
                    
                    // Parse JSON fields
                    let bestPaths, documents;
                    try {
                        bestPaths = JSON.parse(bestPathsJson);
                    } catch (err) {
                        throw new Error('Invalid JSON in Best Paths field');
                    }
                    
                    try {
                        documents = documentsJson ? JSON.parse(documentsJson) : [];
                    } catch (err) {
                        throw new Error('Invalid JSON in Documents field');
                    }
                    
                    // Create request payload
                    const payload = {
                        result: {
                            query: query,
                            best_paths: bestPaths,
                            retrieved_documents: documents
                        },
                        filename_prefix: filenamePrefix || null
                    };
                    
                    // Send request to API
                    const response = await fetch('/visualization/generate', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(payload)
                    });
                    
                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.detail || 'Error generating visualizations');
                    }
                    
                    const data = await response.json();
                    
                    // Display API response
                    apiResponseJson.textContent = JSON.stringify(data, null, 2);
                    apiResponseContainer.classList.remove('d-none');
                    
                    // Update visualization containers
                    if (data.image_urls && data.image_urls.length > 0) {
                        // Find tree visualization
                        const treeImageUrl = data.image_urls.find(url => url.includes('reasoning_tree'));
                        if (treeImageUrl) {
                            document.getElementById('tree-image-container').innerHTML = 
                                `<img src="${treeImageUrl}" class="img-fluid" alt="Reasoning Tree">`;
                        }
                        
                        // Find document visualization
                        const docImageUrl = data.image_urls.find(url => url.includes('document_retrieval'));
                        if (docImageUrl) {
                            document.getElementById('documents-image-container').innerHTML = 
                                `<img src="${docImageUrl}" class="img-fluid" alt="Document Retrieval">`;
                        }
                    }
                    
                    // Update interactive dashboard
                    if (data.html_url) {
                        document.getElementById('interactive-link').href = data.html_url;
                        document.getElementById('interactive-iframe').src = data.html_url;
                    }
                    
                    // Show results
                    resultsContainer.classList.remove('d-none');
                } catch (error) {
                    // Display error
                    errorContainer.textContent = `Error: ${error.message}`;
                    errorContainer.classList.remove('d-none');
                } finally {
                    // Hide loading
                    loadingContainer.classList.add('d-none');
                }
            });
        });
    </script>
</body>
</html> 