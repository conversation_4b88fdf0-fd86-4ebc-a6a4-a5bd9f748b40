# Tích hợp AdaptiveCrawler với WebSearchAgentLocal

## <PERSON><PERSON> tả

Task này tích hợp AdaptiveCrawler vào WebSearchAgentLocal để cải thiện khả năng crawl và trích xuất nội dung từ các trang web. AdaptiveCrawler cung cấp các tính năng nâng cao như:

- Crawl thích ứng dựa trên độ phức tạp của trang web
- Hỗ trợ JavaScript và Single Page Applications (SPA)
- Xử lý CAPTCHA và reCAPTCHA
- Tối ưu hóa bộ nhớ và hiệu suất
- Hỗ trợ đa ngôn ngữ
- Trích xuất nội dung từ nhiều định dạng file (PDF, DOCX, XLSX, v.v.)
- Xử lý phân trang và cuộn vô hạn
- Xử lý form và tương tác với trang web

## Các file đã tạo/cập nhật

- [x] `adaptive_crawler_integration.py`: <PERSON><PERSON><PERSON> tích hợp AdaptiveCrawler với WebSearchAgentLocal
- [x] `deep_research_integration.py`: Module tích hợp AdaptiveCrawler với phương thức deep_research
- [x] `web_search_agent_local.py`: Cập nhật để sử dụng AdaptiveCrawler
- [x] `test_adaptive_crawler_integration.py`: Test case cho việc tích hợp AdaptiveCrawler

## Các phương thức đã tích hợp

- [x] `_crawl_url`: Crawl một URL sử dụng AdaptiveCrawler
- [x] `_crawl_urls`: Crawl nhiều URL sử dụng AdaptiveCrawler
- [x] `_extract_content_from_url`: Trích xuất nội dung từ URL sử dụng AdaptiveCrawler
- [x] `_extract_links_from_url`: Trích xuất links từ URL sử dụng AdaptiveCrawler
- [x] `_extract_metadata_from_url`: Trích xuất metadata từ URL sử dụng AdaptiveCrawler
- [x] `_deep_crawl_with_adaptive_crawler`: Thực hiện deep crawl với AdaptiveCrawler
- [x] `deep_research`: Cải tiến phương thức deep_research để sử dụng AdaptiveCrawler

## Cách sử dụng

### Tích hợp AdaptiveCrawler với WebSearchAgentLocal

```python
from deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal

# Khởi tạo WebSearchAgentLocal
agent = WebSearchAgentLocal(
    search_method="auto",
    verbose=True
)

# AdaptiveCrawler đã được tích hợp tự động trong __init__ của WebSearchAgentLocal
```

### Sử dụng deep_crawl với AdaptiveCrawler

```python
# Crawl một URL với AdaptiveCrawler
result = agent._deep_crawl(
    url="https://example.com",
    max_depth=2,
    max_pages=10,
    timeout=60,
    include_html=True,
    respect_robots=True
)

# Kết quả
print(result["success"])  # True/False
print(result["content"])  # Nội dung trích xuất
print(result["links"])    # Danh sách links
print(result["metadata"]) # Metadata
print(result["crawl_stats"]) # Thống kê crawl
```

### Sử dụng deep_research với AdaptiveCrawler

```python
# Thực hiện deep research với AdaptiveCrawler
result = agent.deep_research(
    query="Tìm kiếm thông tin về Python",
    num_results_per_query=3,
    max_sub_queries=5,
    min_sub_queries=2,
    max_content_length=10000,
    max_depth=2,
    language="auto",
    use_adaptive_crawler=True
)

# Kết quả
print(result["success"])  # True/False
print(result["query"])    # Câu hỏi gốc
print(result["sub_queries"])  # Danh sách câu hỏi con
print(result["results"])  # Kết quả nghiên cứu
```

## Trạng thái

- [x] Tạo file `adaptive_crawler_integration.py`
- [x] Tạo file `deep_research_integration.py`
- [x] Cập nhật `web_search_agent_local.py`
- [x] Tạo test case `test_adaptive_crawler_integration.py`
- [x] Chạy test case
- [x] Fix bugs nếu có
- [x] Cập nhật documentation

## Người thực hiện

- Quan Nguyen

## Ngày hoàn thành

- Ngày bắt đầu: 2023-10-15
- Ngày hoàn thành: 2023-10-15
