# Changelog

## [1.0.0] - 2024-12-19

### 🎉 Major Restructuring
- **Dọn dẹp và tổ chức lại toàn bộ cấu trúc thư mục**
- **Tạo cấu trúc thư mục chuẩn theo best practices**

### 📁 Cấu trúc mới
- `src/deep_research_core/` - Source code chính được tổ chức theo modules
- `tests/` - Tất cả test cases được phân loại rõ ràng
- `docs/` - Documentation được tổ chức theo chủ đề
- `scripts/` - Utility scripts tập trung
- `configs/` - Configuration templates
- `requirements/` - Dependency management

### 🔧 Improvements
- **Source Code Organization**
  - `reasoning/` - Các module reasoning (CoT, ToT, RAG, ReAct, GoT)
  - `rag/` - RAG implementations và vector stores
  - `models/` - Model integrations (OpenAI, Anthropic, Local models)
  - `agents/` - Web search agents và crawlers
  - `utils/` - Utility functions và helpers
  - `config/` - Configuration management

- **Test Organization**
  - `unit/` - Unit tests được phân loại theo modules
  - `integration/` - Integration tests
  - `performance/` - Performance tests

- **Documentation Structure**
  - `api/` - API documentation
  - `guides/` - User guides và tutorials
  - `examples/` - Code examples

### 🗂️ Files Migrated
- **Source Files**: 200+ Python files được di chuyển và tổ chức
- **Test Files**: 400+ test files được merge và phân loại
  - Merge thư mục `test/` và `tests/` thành `tests/`
  - Unit tests: 350+ files
  - Integration tests: 30+ files
  - Performance tests: 20+ files
  - Test results: 15+ result files
- **Documentation**: 50+ markdown files được tổ chức
- **Configuration**: 25+ config files được tập trung
- **Scripts**: 15+ utility scripts được tổ chức

### 🧹 Cleanup
- Loại bỏ các file trùng lặp
- Xóa các file tạm thời (temp_*)
- Tổ chức lại các README files
- Gộp các file configuration

### 📦 Package Structure
- Tạo `setup.py` chuẩn cho package
- Tạo `__init__.py` với proper imports
- Tổ chức requirements files
- Chuẩn bị cho PyPI publishing

### 🚀 Ready for Production
- Cấu trúc thư mục chuẩn, dễ maintain
- Test coverage được tổ chức tốt
- Documentation đầy đủ và có cấu trúc
- Package sẵn sàng để distribute

### 🔄 Migration Notes
- Thư mục cũ: `src/deep_research_core/advanced_reasoning/deepresearch/`
- Thư mục mới: `advanced_reasoning_engine/`
- Tất cả imports cần được update theo cấu trúc mới
- Test paths cần được update

### 📋 TODO
- [ ] Update import statements trong các files
- [ ] Chạy test suite để verify migration
- [ ] Update CI/CD pipelines
- [ ] Update documentation links
- [ ] Publish to PyPI

---

## Previous Versions
Xem file `docs/LEGACY_CHANGELOG.md` để biết lịch sử phiên bản trước khi restructure.
