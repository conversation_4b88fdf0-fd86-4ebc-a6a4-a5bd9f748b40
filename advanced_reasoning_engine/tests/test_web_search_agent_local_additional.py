#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test case bổ sung cho WebSearchAgentLocal.

Test các tính năng mới đã được cải tiến:
- <PERSON><PERSON> lý tiếng Việt nâng cao
- Xử lý CAPTCHA nâng cao
- Tối ưu hóa hiệu suất với xử lý bất đồng bộ
- Tích hợp giữa các module
"""

import os
import sys
import time
import unittest
import random
import json
import logging
from unittest.mock import patch, MagicMock

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Thêm thư mục gốc vào sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# Import các module cầ<PERSON> thiết
try:
    from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal
    from src.deep_research_core.agents.web_search_agent_local_improvements import integrate_improvements
    from src.deep_research_core.agents.vietnamese_search_integration_improved import integrate_vietnamese_search_improved
    from src.deep_research_core.utils.vietnamese_captcha_handler import VietnameseCaptchaHandler
    from src.deep_research_core.agents.performance_optimizer_improved import PerformanceOptimizerImproved
    from src.deep_research_core.utils.async_search_executor import AsyncSearchExecutor
except ImportError:
    try:
        # Thử import từ thư mục hiện tại
        from web_search_agent_local import WebSearchAgentLocal
        from web_search_agent_local_improvements import integrate_improvements
        from vietnamese_search_integration_improved import integrate_vietnamese_search_improved
        from vietnamese_captcha_handler import VietnameseCaptchaHandler
        from performance_optimizer_improved import PerformanceOptimizerImproved
        from async_search_executor import AsyncSearchExecutor
    except ImportError:
        logger.error("Không thể import các module cần thiết")
        sys.exit(1)

# Hàm tiện ích
def print_section(title):
    """In tiêu đề phần."""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, "="))
    print("=" * 80 + "\n")

def print_result(result, detailed=False):
    """In kết quả tìm kiếm."""
    if not result:
        print("Không có kết quả")
        return

    print(f"Thành công: {result.get('success', False)}")
    if not result.get('success', False):
        print(f"Lỗi: {result.get('error', 'Unknown error')}")
        return

    results = result.get('results', [])
    print(f"Số lượng kết quả: {len(results)}")

    if detailed and results:
        for i, item in enumerate(results[:3]):  # Chỉ in 3 kết quả đầu tiên
            print(f"\nKết quả {i+1}:")
            print(f"  Tiêu đề: {item.get('title', 'N/A')}")
            print(f"  URL: {item.get('url', 'N/A')}")
            snippet = item.get('snippet', '')
            print(f"  Đoạn trích: {snippet[:100]}..." if len(snippet) > 100 else f"  Đoạn trích: {snippet}")
            if 'content' in item:
                content = item.get('content', '')
                print(f"  Nội dung: {content[:100]}..." if len(content) > 100 else f"  Nội dung: {content}")

def save_result_to_json(result, filename):
    """Lưu kết quả vào file JSON."""
    os.makedirs(os.path.dirname(filename), exist_ok=True)
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    print(f"Đã lưu kết quả vào {filename}")

class TestWebSearchAgentLocalAdditional(unittest.TestCase):
    """Test case bổ sung cho WebSearchAgentLocal."""

    @classmethod
    def setUpClass(cls):
        """Thiết lập trước khi chạy tất cả các test."""
        print_section("Thiết lập test bổ sung cho WebSearchAgentLocal")

        # Tạo thư mục lưu kết quả nếu chưa tồn tại
        os.makedirs("test_results", exist_ok=True)

        # Khởi tạo WebSearchAgentLocal
        cls.agent = WebSearchAgentLocal(verbose=True)

        # Tích hợp các cải tiến
        try:
            integrate_improvements(cls.agent)
            print("Đã tích hợp các cải tiến vào WebSearchAgentLocal")
        except Exception as e:
            print(f"Lỗi khi tích hợp các cải tiến: {str(e)}")

        # Danh sách truy vấn tiếng Việt
        cls.vietnamese_queries = [
            "Thủ đô của Việt Nam là gì",
            "Cách nấu phở bò truyền thống",
            "Lịch sử Việt Nam thời kỳ đổi mới",
            "Đặc sản miền Trung Việt Nam",
            "Kinh tế Việt Nam năm 2023"
        ]

        # Danh sách truy vấn phức tạp
        cls.complex_queries = [
            "So sánh chi tiết giữa React và Vue.js về hiệu suất và quản lý state",
            "Phân tích tác động của biến đổi khí hậu đến nông nghiệp Việt Nam",
            "Giải thích cơ chế hoạt động của mạng neural tích chập trong xử lý ảnh",
            "Tổng hợp các nghiên cứu gần đây về ứng dụng trí tuệ nhân tạo trong y tế",
            "Phân tích ưu nhược điểm của các phương pháp xác thực hai yếu tố phổ biến"
        ]

    def test_01_vietnamese_dialect_detection(self):
        """Test phát hiện phương ngữ tiếng Việt."""
        print_section("Test phát hiện phương ngữ tiếng Việt")
        
        # Các văn bản tiếng Việt với phương ngữ khác nhau
        texts = {
            "northern": "Tôi đang ở Hà Nội và tôi thích ăn phở ở đây.",
            "southern": "Tui đang ở Sài Gòn và tui thích ăn hủ tiếu ở đây.",
            "central": "Tôi đang ở Huế và tôi thích ăn bún bò ở đây."
        }
        
        # Kiểm tra phát hiện phương ngữ
        if hasattr(self.agent, 'detect_vietnamese_dialect'):
            for dialect, text in texts.items():
                result = self.agent.detect_vietnamese_dialect(text)
                print(f"Văn bản: {text}")
                print(f"Phương ngữ phát hiện: {result}")
                
                # Lưu kết quả
                save_result_to_json(
                    {"text": text, "detected_dialect": result},
                    f"test_results/vietnamese_dialect_{dialect}.json"
                )
        else:
            print("Phương thức detect_vietnamese_dialect không khả dụng")
            self.skipTest("Phương thức detect_vietnamese_dialect không khả dụng")

    def test_02_vietnamese_text_normalization(self):
        """Test chuẩn hóa văn bản tiếng Việt."""
        print_section("Test chuẩn hóa văn bản tiếng Việt")
        
        # Các văn bản tiếng Việt cần chuẩn hóa
        texts = [
            "Tôi đang học tiếng Việt",
            "Hà Nội là thủ đô của Việt Nam",
            "Việt Nam có 54 dân tộc anh em",
            "Tiếng Việt có 6 thanh điệu: ngang, huyền, sắc, hỏi, ngã, nặng",
            "Bánh mì Việt Nam nổi tiếng trên thế giới"
        ]
        
        # Kiểm tra chuẩn hóa văn bản
        if hasattr(self.agent, 'normalize_vietnamese_text'):
            for i, text in enumerate(texts):
                result = self.agent.normalize_vietnamese_text(text)
                print(f"Văn bản gốc: {text}")
                print(f"Văn bản chuẩn hóa: {result}")
                
                # Lưu kết quả
                save_result_to_json(
                    {"original_text": text, "normalized_text": result},
                    f"test_results/vietnamese_normalization_{i+1}.json"
                )
        else:
            print("Phương thức normalize_vietnamese_text không khả dụng")
            self.skipTest("Phương thức normalize_vietnamese_text không khả dụng")

    def test_03_vietnamese_tokenization(self):
        """Test tách từ tiếng Việt."""
        print_section("Test tách từ tiếng Việt")
        
        # Các văn bản tiếng Việt cần tách từ
        texts = [
            "Tôi đang học tiếng Việt",
            "Hà Nội là thủ đô của Việt Nam",
            "Việt Nam có 54 dân tộc anh em",
            "Tiếng Việt có 6 thanh điệu: ngang, huyền, sắc, hỏi, ngã, nặng",
            "Bánh mì Việt Nam nổi tiếng trên thế giới"
        ]
        
        # Kiểm tra tách từ
        if hasattr(self.agent, 'tokenize_vietnamese'):
            for i, text in enumerate(texts):
                result = self.agent.tokenize_vietnamese(text)
                print(f"Văn bản gốc: {text}")
                print(f"Kết quả tách từ: {result}")
                
                # Lưu kết quả
                save_result_to_json(
                    {"original_text": text, "tokens": result},
                    f"test_results/vietnamese_tokenization_{i+1}.json"
                )
        else:
            print("Phương thức tokenize_vietnamese không khả dụng")
            self.skipTest("Phương thức tokenize_vietnamese không khả dụng")

    def test_04_recaptcha_v3_handling(self):
        """Test xử lý reCAPTCHA v3."""
        print_section("Test xử lý reCAPTCHA v3")
        
        # HTML giả lập chứa reCAPTCHA v3
        html_content = """
        <html>
        <head>
            <script src="https://www.google.com/recaptcha/api.js?render=6LdQUeIUAAAAAOUAWAYqRyxlj5PJZ7vXVpYJMiRl"></script>
            <script>
            grecaptcha.ready(function() {
                grecaptcha.execute('6LdQUeIUAAAAAOUAWAYqRyxlj5PJZ7vXVpYJMiRl', {action: 'homepage'}).then(function(token) {
                    document.getElementById('g-recaptcha-response').value = token;
                });
            });
            </script>
        </head>
        <body>
            <form>
                <input type="hidden" id="g-recaptcha-response" name="g-recaptcha-response">
                <button type="submit">Submit</button>
            </form>
        </body>
        </html>
        """
        
        # Kiểm tra xử lý reCAPTCHA v3
        if hasattr(self.agent, 'handle_captcha'):
            result = self.agent.handle_captcha("https://example.com", html_content)
            
            # Kiểm tra kết quả
            print(f"Kết quả xử lý reCAPTCHA v3: {result}")
            
            # Lưu kết quả
            save_result_to_json(result, "test_results/recaptcha_v3_handling.json")
        else:
            print("Phương thức handle_captcha không khả dụng")
            self.skipTest("Phương thức handle_captcha không khả dụng")

    def test_05_hcaptcha_handling(self):
        """Test xử lý hCAPTCHA."""
        print_section("Test xử lý hCAPTCHA")
        
        # HTML giả lập chứa hCAPTCHA
        html_content = """
        <html>
        <head>
            <script src="https://js.hcaptcha.com/1/api.js" async defer></script>
        </head>
        <body>
            <form>
                <div class="h-captcha" data-sitekey="10000000-ffff-ffff-ffff-000000000001"></div>
                <button type="submit">Submit</button>
            </form>
        </body>
        </html>
        """
        
        # Kiểm tra xử lý hCAPTCHA
        if hasattr(self.agent, 'handle_captcha'):
            result = self.agent.handle_captcha("https://example.com", html_content)
            
            # Kiểm tra kết quả
            print(f"Kết quả xử lý hCAPTCHA: {result}")
            
            # Lưu kết quả
            save_result_to_json(result, "test_results/hcaptcha_handling.json")
        else:
            print("Phương thức handle_captcha không khả dụng")
            self.skipTest("Phương thức handle_captcha không khả dụng")

    def test_06_async_content_extraction(self):
        """Test trích xuất nội dung bất đồng bộ."""
        print_section("Test trích xuất nội dung bất đồng bộ")
        
        # Danh sách URL để trích xuất
        urls = [
            "https://www.python.org/",
            "https://www.wikipedia.org/",
            "https://www.github.com/"
        ]
        
        # Trích xuất nội dung bất đồng bộ
        if hasattr(self.agent, 'extract_content_async'):
            results = self.agent.extract_content_async(urls)
            
            # Kiểm tra kết quả
            print(f"Kết quả trích xuất nội dung bất đồng bộ: {results.get('success', False)}")
            print(f"Số lượng kết quả: {len(results.get('results', []))}")
            
            # Lưu kết quả
            save_result_to_json(results, "test_results/async_content_extraction.json")
        else:
            print("Phương thức extract_content_async không khả dụng")
            self.skipTest("Phương thức extract_content_async không khả dụng")

    def test_07_async_search(self):
        """Test tìm kiếm bất đồng bộ."""
        print_section("Test tìm kiếm bất đồng bộ")
        
        # Danh sách truy vấn
        queries = [
            "Python programming",
            "Machine learning",
            "Artificial intelligence"
        ]
        
        # Tìm kiếm bất đồng bộ
        if hasattr(self.agent, 'search_async'):
            results = self.agent.search_async(queries)
            
            # Kiểm tra kết quả
            print(f"Kết quả tìm kiếm bất đồng bộ: {results.get('success', False)}")
            print(f"Số lượng kết quả: {len(results.get('results', []))}")
            
            # Lưu kết quả
            save_result_to_json(results, "test_results/async_search.json")
        else:
            print("Phương thức search_async không khả dụng")
            self.skipTest("Phương thức search_async không khả dụng")

    def test_08_module_integration(self):
        """Test tích hợp giữa các module."""
        print_section("Test tích hợp giữa các module")
        
        # Tích hợp các module
        if hasattr(self.agent, 'integrate_improvements'):
            result = self.agent.integrate_improvements()
            
            # Kiểm tra kết quả
            print(f"Kết quả tích hợp các module: {result.get('success', False)}")
            print(f"Các module đã tích hợp: {result.get('integrated_modules', [])}")
            print(f"Các module thất bại: {result.get('failed_modules', [])}")
            
            # Lưu kết quả
            save_result_to_json(result, "test_results/module_integration.json")
        else:
            print("Phương thức integrate_improvements không khả dụng")
            self.skipTest("Phương thức integrate_improvements không khả dụng")

    def test_09_performance_optimization(self):
        """Test tối ưu hóa hiệu suất."""
        print_section("Test tối ưu hóa hiệu suất")
        
        # Chọn một câu hỏi phức tạp
        query = random.choice(self.complex_queries)
        print(f"Truy vấn phức tạp: {query}")
        
        # Thực hiện tìm kiếm với tối ưu hóa hiệu suất
        if hasattr(self.agent, 'optimize_query_execution'):
            start_time = time.time()
            result = self.agent.optimize_query_execution(
                query=query,
                complexity_level="complex",
                num_results=5,
                get_content=True
            )
            end_time = time.time()
            
            # Kiểm tra kết quả
            print(f"Thời gian thực thi: {end_time - start_time:.2f} giây")
            print(f"Kết quả tối ưu hóa hiệu suất: {result.get('success', False)}")
            print(f"Số lượng kết quả: {len(result.get('results', []))}")
            
            # Lưu kết quả
            save_result_to_json(result, "test_results/performance_optimization.json")
        else:
            print("Phương thức optimize_query_execution không khả dụng")
            self.skipTest("Phương thức optimize_query_execution không khả dụng")

    def test_10_vietnamese_search_with_optimization(self):
        """Test tìm kiếm tiếng Việt với tối ưu hóa."""
        print_section("Test tìm kiếm tiếng Việt với tối ưu hóa")
        
        # Chọn một câu hỏi tiếng Việt
        query = random.choice(self.vietnamese_queries)
        print(f"Truy vấn tiếng Việt: {query}")
        
        # Thực hiện tìm kiếm tiếng Việt với tối ưu hóa
        if hasattr(self.agent, 'search_vietnamese_improved') and hasattr(self.agent, 'optimize_query_execution'):
            start_time = time.time()
            result = self.agent.optimize_query_execution(
                query=query,
                complexity_level="medium",
                num_results=5,
                get_content=True,
                language="vi"
            )
            end_time = time.time()
            
            # Kiểm tra kết quả
            print(f"Thời gian thực thi: {end_time - start_time:.2f} giây")
            print(f"Kết quả tìm kiếm tiếng Việt với tối ưu hóa: {result.get('success', False)}")
            print(f"Số lượng kết quả: {len(result.get('results', []))}")
            
            # Lưu kết quả
            save_result_to_json(result, "test_results/vietnamese_search_with_optimization.json")
        else:
            print("Phương thức search_vietnamese_improved hoặc optimize_query_execution không khả dụng")
            self.skipTest("Phương thức search_vietnamese_improved hoặc optimize_query_execution không khả dụng")

if __name__ == "__main__":
    unittest.main()
