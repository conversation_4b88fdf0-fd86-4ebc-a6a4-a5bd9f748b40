"""
Tests for Vietnamese support in Agent Environment.
"""

import unittest
from unittest.mock import MagicMock, patch
import datetime
import re

from deep_research_core.rl_tuning.environment.vietnamese_support import VietnameseSupport


class TestVietnameseEnvironmentSupport(unittest.TestCase):
    """Test cases for Vietnamese support in Agent Environment."""

    def setUp(self):
        """Set up test environment."""
        # Initialize Vietnamese support
        self.vietnamese_support = VietnameseSupport(
            fallback_to_english=False,
            translation_model=None
        )
        
        # Mock translation methods
        self.vietnamese_support.translate_to_english = MagicMock(return_value="[EN] Test text")
        self.vietnamese_support.translate_to_vietnamese = MagicMock(return_value="[VI] Test text")
        self.vietnamese_support.is_vietnamese = MagicMock(return_value=True)
        
        # Mock time of day
        self.patch_datetime = patch('datetime.datetime')
        self.mock_datetime = self.patch_datetime.start()
        self.mock_now = MagicMock()
        self.mock_datetime.now.return_value = self.mock_now

    def tearDown(self):
        """Clean up after tests."""
        self.patch_datetime.stop()

    def test_initialization(self):
        """Test initialization of Vietnamese support."""
        self.assertFalse(self.vietnamese_support.fallback_to_english)
        self.assertIsNone(self.vietnamese_support.translation_model)
        
        # Test with fallback to English
        support_with_fallback = VietnameseSupport(fallback_to_english=True)
        self.assertTrue(support_with_fallback.fallback_to_english)

    def test_process_action_vietnamese_to_english(self):
        """Test processing action from Vietnamese to English."""
        # Set up Vietnamese support with fallback to English
        support = VietnameseSupport(fallback_to_english=True)
        support.translate_to_english = MagicMock(return_value="[EN] Test content")
        
        # Create a Vietnamese action
        action = {
            "action_type": "think",
            "content": "Tôi đang suy nghĩ",
            "tool_input": "Đây là dữ liệu đầu vào"
        }
        
        # Process the action
        processed = support.process_action(action, source_lang="vi")
        
        # Check that content and tool_input were translated
        self.assertEqual(processed["content"], "[EN] Test content")
        self.assertEqual(processed["tool_input"], "[EN] Test content")
        
        # Check that action_type was preserved
        self.assertEqual(processed["action_type"], "think")

    def test_process_action_english_to_vietnamese(self):
        """Test processing action from English to Vietnamese."""
        # Create an English action
        action = {
            "action_type": "think",
            "content": "I am thinking",
            "tool_input": "This is input data"
        }
        
        # Process the action
        processed = self.vietnamese_support.process_action(action, source_lang="en")
        
        # Check that content and tool_input were translated
        self.assertEqual(processed["content"], "[VI] Test text")
        self.assertEqual(processed["tool_input"], "[VI] Test text")
        
        # Check that action_type was preserved
        self.assertEqual(processed["action_type"], "think")

    def test_enhance_vietnamese_response(self):
        """Test enhancing Vietnamese response."""
        # Test with Vietnamese response
        response = "Đây là một câu trả lời đơn giản."
        enhanced = self.vietnamese_support.enhance_vietnamese_response(response)
        
        # Check that greeting was added
        self.assertNotEqual(enhanced, response)
        self.assertGreater(len(enhanced), len(response))
        
        # Test with non-Vietnamese response
        self.vietnamese_support.is_vietnamese.return_value = False
        response = "This is a simple response."
        enhanced = self.vietnamese_support.enhance_vietnamese_response(response)
        
        # Check that response was not modified
        self.assertEqual(enhanced, response)
        
        # Test with empty response
        enhanced = self.vietnamese_support.enhance_vietnamese_response("")
        self.assertEqual(enhanced, "")
        
        enhanced = self.vietnamese_support.enhance_vietnamese_response(None)
        self.assertEqual(enhanced, None)

    def test_get_vietnamese_greeting(self):
        """Test getting Vietnamese greeting based on time of day."""
        # Test morning greeting
        self.mock_now.hour = 8
        greeting = self.vietnamese_support._get_vietnamese_greeting("morning")
        self.assertEqual(greeting, "Chào buổi sáng!")
        
        # Test afternoon greeting
        self.mock_now.hour = 14
        greeting = self.vietnamese_support._get_vietnamese_greeting("afternoon")
        self.assertEqual(greeting, "Chào buổi chiều!")
        
        # Test evening greeting
        self.mock_now.hour = 20
        greeting = self.vietnamese_support._get_vietnamese_greeting("evening")
        self.assertEqual(greeting, "Chào buổi tối!")
        
        # Test night greeting
        self.mock_now.hour = 23
        greeting = self.vietnamese_support._get_vietnamese_greeting("night")
        self.assertEqual(greeting, "Chào bạn!")
        
        # Test default greeting
        greeting = self.vietnamese_support._get_vietnamese_greeting("invalid")
        self.assertEqual(greeting, "Xin chào!")

    def test_get_time_of_day(self):
        """Test getting time of day."""
        # Test morning
        self.mock_now.hour = 8
        time_of_day = self.vietnamese_support._get_time_of_day()
        self.assertEqual(time_of_day, "morning")
        
        # Test afternoon
        self.mock_now.hour = 14
        time_of_day = self.vietnamese_support._get_time_of_day()
        self.assertEqual(time_of_day, "afternoon")
        
        # Test evening
        self.mock_now.hour = 20
        time_of_day = self.vietnamese_support._get_time_of_day()
        self.assertEqual(time_of_day, "evening")
        
        # Test night
        self.mock_now.hour = 23
        time_of_day = self.vietnamese_support._get_time_of_day()
        self.assertEqual(time_of_day, "night")

    def test_get_vietnamese_closing(self):
        """Test getting Vietnamese closing phrase."""
        # Test that a closing phrase is returned
        closing = self.vietnamese_support._get_vietnamese_closing()
        self.assertIsInstance(closing, str)
        self.assertGreater(len(closing), 0)

    def test_analyze_vietnamese_text_complexity(self):
        """Test analyzing Vietnamese text complexity."""
        # Test with Vietnamese text
        text = "Đây là một đoạn văn bản tiếng Việt với nhiều dấu. Nó có nhiều câu và từ phức tạp."
        complexity = self.vietnamese_support.analyze_vietnamese_text_complexity(text)
        
        # Check that complexity analysis was performed
        self.assertIsInstance(complexity, dict)
        self.assertIn("complexity", complexity)
        self.assertIn("score", complexity)
        self.assertIn("metrics", complexity)
        
        # Check metrics
        metrics = complexity["metrics"]
        self.assertIn("word_count", metrics)
        self.assertIn("sentence_count", metrics)
        self.assertIn("avg_sentence_length", metrics)
        self.assertIn("diacritic_count", metrics)
        self.assertIn("diacritic_density", metrics)
        
        # Test with non-Vietnamese text
        self.vietnamese_support.is_vietnamese.return_value = False
        complexity = self.vietnamese_support.analyze_vietnamese_text_complexity("This is English text")
        
        # Check that default values were returned
        self.assertEqual(complexity["complexity"], "unknown")
        self.assertEqual(complexity["score"], 0.0)
        
        # Test with empty text
        complexity = self.vietnamese_support.analyze_vietnamese_text_complexity("")
        self.assertEqual(complexity["complexity"], "unknown")
        self.assertEqual(complexity["score"], 0.0)
        
        complexity = self.vietnamese_support.analyze_vietnamese_text_complexity(None)
        self.assertEqual(complexity["complexity"], "unknown")
        self.assertEqual(complexity["score"], 0.0)

    def test_detect_vietnamese_dialect(self):
        """Test detecting Vietnamese dialect."""
        # Test with Vietnamese utils available
        with patch('deep_research_core.utils.vietnamese_utils.detect_vietnamese_dialect') as mock_detect:
            mock_detect.return_value = "northern"
            dialect = self.vietnamese_support.detect_vietnamese_dialect("Test text")
            self.assertEqual(dialect, "northern")
            mock_detect.assert_called_once_with("Test text")
        
        # Test with Vietnamese utils not available (fallback)
        with patch('deep_research_core.utils.vietnamese_utils.detect_vietnamese_dialect', side_effect=ImportError):
            # Test northern dialect
            dialect = self.vietnamese_support.detect_vietnamese_dialect("Tôi đang ở Hà Nội và tôi thích ăn phở ở đấy")
            self.assertEqual(dialect, "northern")
            
            # Test central dialect
            dialect = self.vietnamese_support.detect_vietnamese_dialect("Mi đang ở Huế và mi thích ăn bún bò Huế ở mô")
            self.assertEqual(dialect, "central")
            
            # Test southern dialect
            dialect = self.vietnamese_support.detect_vietnamese_dialect("Tui đang ở Sài Gòn và tui thích ăn hủ tiếu ở đó hen")
            self.assertEqual(dialect, "southern")
            
            # Test with non-Vietnamese text
            self.vietnamese_support.is_vietnamese.return_value = False
            dialect = self.vietnamese_support.detect_vietnamese_dialect("This is English text")
            self.assertEqual(dialect, "unknown")
            
            # Test with empty text
            dialect = self.vietnamese_support.detect_vietnamese_dialect("")
            self.assertEqual(dialect, "unknown")
            
            dialect = self.vietnamese_support.detect_vietnamese_dialect(None)
            self.assertEqual(dialect, "unknown")


if __name__ == "__main__":
    unittest.main()
