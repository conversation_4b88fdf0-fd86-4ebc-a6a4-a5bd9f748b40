#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test cho FileFormatHandler.
"""

import unittest
import os
import sys
import tempfile
import json

# Thêm thư mục gốc vào sys.path để import các module
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.utils.file_format_handler import FileFormatHandler

class TestFileFormatHandler(unittest.TestCase):
    """Test cho FileFormatHandler."""

    def setUp(self):
        """Thiết lập trước mỗi test."""
        self.handler = FileFormatHandler()
        self.temp_dir = tempfile.mkdtemp()
        
        # Tạo các file test
        self.text_file = os.path.join(self.temp_dir, "test.txt")
        with open(self.text_file, "w", encoding="utf-8") as f:
            f.write("Đ<PERSON>y là file văn bản thử nghiệm.\nDòng thứ hai.")
        
        self.json_file = os.path.join(self.temp_dir, "test.json")
        with open(self.json_file, "w", encoding="utf-8") as f:
            json.dump({"name": "Test", "value": 123}, f, ensure_ascii=False)
        
        self.html_file = os.path.join(self.temp_dir, "test.html")
        with open(self.html_file, "w", encoding="utf-8") as f:
            f.write("""
            <html>
                <head><title>Test Page</title></head>
                <body>
                    <h1>Test Page</h1>
                    <p>This is a test page.</p>
                    <a href="https://example.com">Example Link</a>
                    <img src="image.jpg" alt="Test Image">
                </body>
            </html>
            """)

    def tearDown(self):
        """Dọn dẹp sau mỗi test."""
        import shutil
        shutil.rmtree(self.temp_dir)

    def test_detect_file_type(self):
        """Test phát hiện loại file."""
        # Test file văn bản
        category, ext = self.handler.detect_file_type(self.text_file)
        self.assertEqual(category, "text")
        self.assertEqual(ext, ".txt")
        
        # Test file JSON
        category, ext = self.handler.detect_file_type(self.json_file)
        self.assertEqual(category, "data")
        self.assertEqual(ext, ".json")
        
        # Test file HTML
        category, ext = self.handler.detect_file_type(self.html_file)
        self.assertEqual(category, "web")
        self.assertEqual(ext, ".html")
        
        # Test file không tồn tại
        category, ext = self.handler.detect_file_type("nonexistent.pdf")
        self.assertEqual(category, "document")
        self.assertEqual(ext, ".pdf")

    def test_extract_text_content(self):
        """Test trích xuất nội dung từ file văn bản."""
        result = self.handler.extract_content(self.text_file)
        
        self.assertTrue(result["success"])
        self.assertEqual(result["file_path"], self.text_file)
        self.assertEqual(result["file_type"], ".txt")
        self.assertEqual(result["content"], "Đây là file văn bản thử nghiệm.\nDòng thứ hai.")
        self.assertIn("file_size", result["metadata"])
        self.assertIn("line_count", result["metadata"])
        self.assertIn("char_count", result["metadata"])
        self.assertEqual(result["metadata"]["line_count"], 2)

    def test_extract_json_content(self):
        """Test trích xuất nội dung từ file JSON."""
        result = self.handler.extract_content(self.json_file)
        
        self.assertTrue(result["success"])
        self.assertEqual(result["file_path"], self.json_file)
        self.assertEqual(result["file_type"], ".json")
        self.assertIn("data", result)
        self.assertEqual(result["data"]["name"], "Test")
        self.assertEqual(result["data"]["value"], 123)
        self.assertIn("file_size", result["metadata"])
        self.assertEqual(result["metadata"]["type"], "json")

    def test_extract_html_content(self):
        """Test trích xuất nội dung từ file HTML."""
        result = self.handler.extract_content(self.html_file)
        
        self.assertTrue(result["success"])
        self.assertEqual(result["file_path"], self.html_file)
        self.assertEqual(result["file_type"], ".html")
        self.assertIn("Test Page", result["content"])
        self.assertIn("This is a test page", result["content"])
        
        # Kiểm tra nếu BeautifulSoup được cài đặt
        try:
            import bs4
            self.assertIn("links", result)
            self.assertIn("images", result)
            self.assertGreater(len(result["links"]), 0)
            self.assertGreater(len(result["images"]), 0)
            self.assertEqual(result["links"][0]["text"], "Example Link")
            self.assertEqual(result["links"][0]["href"], "https://example.com")
            self.assertEqual(result["images"][0]["alt"], "Test Image")
            self.assertEqual(result["images"][0]["src"], "image.jpg")
        except ImportError:
            # BeautifulSoup không được cài đặt, bỏ qua kiểm tra
            pass

    def test_unsupported_format(self):
        """Test trích xuất nội dung từ định dạng không được hỗ trợ."""
        # Tạo file với định dạng không được hỗ trợ
        unsupported_file = os.path.join(self.temp_dir, "test.xyz")
        with open(unsupported_file, "w") as f:
            f.write("Test content")
        
        result = self.handler.extract_content(unsupported_file)
        
        self.assertFalse(result["success"])
        self.assertIn("error", result)
        self.assertEqual(result["file_path"], unsupported_file)
        self.assertEqual(result["file_type"], ".xyz")

if __name__ == '__main__':
    unittest.main()
