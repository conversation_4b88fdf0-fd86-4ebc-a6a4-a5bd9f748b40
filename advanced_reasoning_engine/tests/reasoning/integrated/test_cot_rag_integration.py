"""
Tests for the CoT-RAG integration module.

This file contains unit tests for the CoT-RAG integrator, which combines
Chain of Thought reasoning with Retrieval-Augmented Generation.
"""

import unittest
from unittest.mock import MagicMock, patch

from deep_research_core.models.base import BaseModel
from deep_research_core.reasoning.cot import ChainOfThought
from deep_research_core.reasoning.rag.implementation import <PERSON><PERSON>easoner
from deep_research_core.reasoning.integrated.cot_rag_integration import CoTRAGIntegrator
from deep_research_core.utils.types import Document, ReasoningResult


class TestCoTRAGIntegration(unittest.TestCase):
    """Test cases for the CoT-RAG integration."""

    def setUp(self):
        """Set up test fixtures."""
        # Create mock objects
        self.mock_model = MagicMock(spec=BaseModel)
        self.mock_model.provider = "test_provider"
        self.mock_model.model_name = "test_model"
        self.mock_model.generate.return_value = "Generated text response"
        
        self.mock_cot_reasoner = MagicMock(spec=ChainOfThought)
        self.mock_cot_reasoner.reason.return_value = "CoT reasoning result"
        
        self.mock_rag_reasoner = MagicMock(spec=RAGReasoner)
        self.mock_rag_reasoner.retrieve_documents.return_value = [
            Document(id="doc1", content="Test document 1", source="source1"),
            Document(id="doc2", content="Test document 2", source="source2"),
        ]
        
        # Create the integrator with mocks
        self.integrator = CoTRAGIntegrator(
            model=self.mock_model,
            cot_reasoner=self.mock_cot_reasoner,
            rag_reasoner=self.mock_rag_reasoner,
            max_documents_per_step=2,
            verbose=True
        )

    def test_initialization(self):
        """Test initialization of the CoT-RAG integrator."""
        self.assertEqual(self.integrator.model, self.mock_model)
        self.assertEqual(self.integrator.cot_reasoner, self.mock_cot_reasoner)
        self.assertEqual(self.integrator.rag_reasoner, self.mock_rag_reasoner)
        self.assertEqual(self.integrator.max_documents_per_step, 2)
        self.assertEqual(self.integrator.language, "en")
        self.assertTrue(self.integrator.verbose)
        
    def test_initialization_error_no_rag_reasoner(self):
        """Test that initialization fails without a RAG reasoner."""
        with self.assertRaises(ValueError) as context:
            CoTRAGIntegrator(
                model=self.mock_model,
                cot_reasoner=self.mock_cot_reasoner,
                rag_reasoner=None
            )
        
        self.assertIn("concrete RAG reasoner implementation must be provided", str(context.exception))
    
    def test_extract_reasoning_steps(self):
        """Test extraction of reasoning steps."""
        # Create a mock ReasoningResult with formatted steps
        mock_cot_result = MagicMock(spec=ReasoningResult)
        mock_cot_result.answer = """
        Step 1: Analyze the query to understand what is being asked
        Some reasoning for step 1.
        
        Step 2: Identify key concepts and information needed
        Some reasoning for step 2.
        
        Step 3: Evaluate available evidence and sources
        Some reasoning for step 3.
        
        Step 4: Draw conclusions based on the analysis
        Some reasoning for step 4.
        """
        
        steps = self.integrator.extract_reasoning_steps(mock_cot_result)
        
        # Check the extract_reasoning_steps method processed the steps correctly
        self.assertEqual(len(steps), 4)
        self.assertEqual(steps[0]["step"], 1)
        self.assertEqual(steps[0]["description"], "Analyze the query to understand what is being asked")
        self.assertEqual(steps[1]["step"], 2)
        self.assertEqual(steps[1]["description"], "Identify key concepts and information needed")
        self.assertEqual(steps[2]["step"], 3)
        self.assertEqual(steps[2]["description"], "Evaluate available evidence and sources")
        self.assertEqual(steps[3]["step"], 4)
        self.assertEqual(steps[3]["description"], "Draw conclusions based on the analysis")
    
    def test_process_reasoning_step_step_based(self):
        """Test processing a single reasoning step with step-based retrieval."""
        # Configure the integrator to use step-based retrieval
        self.integrator.retrieval_strategy = "step_based"
        
        # Create a step to process
        step = {
            "step": 1,
            "description": "Analyze geographical facts about France",
            "reasoning": "Initial reasoning about France",
            "documents": []
        }
        
        # Mock the rag_reasoner.retrieve method
        mock_rag_result = MagicMock(spec=ReasoningResult)
        mock_rag_result.metadata = {
            "documents": [
                Document(id="doc1", content="Test document 1", source="source1"),
                Document(id="doc2", content="Test document 2", source="source2")
            ]
        }
        self.mock_rag_reasoner.retrieve.return_value = mock_rag_result
        
        # Mock the model's generate method
        self.mock_model.generate.return_value = "Enhanced reasoning for this step"
        
        # Process the step
        processed_step = self.integrator.process_reasoning_step(
            step=step,
            query="What is the capital of France?",
            all_documents=[]
        )
        
        # Check the result
        self.assertEqual(processed_step["step"], 1)
        self.assertEqual(processed_step["description"], "Analyze geographical facts about France")
        self.assertEqual(processed_step["reasoning"], "Enhanced reasoning for this step")
        self.assertEqual(len(processed_step["documents"]), 2)
        
        # Verify the RAG reasoner was called for document retrieval
        self.mock_rag_reasoner.retrieve.assert_called_once()
    
    def test_process_reasoning_step_initial(self):
        """Test processing a single reasoning step with initial retrieval only."""
        # Configure the integrator to use initial retrieval
        self.integrator.retrieval_strategy = "initial"
        
        # Create a step to process
        step = {
            "step": 1,
            "description": "Analyze geographical facts about France",
            "reasoning": "Initial reasoning about France",
            "documents": []
        }
        
        # Create some initial docs
        all_documents = [
            Document(id="init_doc1", content="Initial document 1", source="initial_source1"),
            Document(id="init_doc2", content="Initial document 2", source="initial_source2")
        ]
        
        # Mock the model's generate method
        self.mock_model.generate.return_value = "Enhanced reasoning from initial documents"
        
        # Process the step
        processed_step = self.integrator.process_reasoning_step(
            step=step,
            query="What is the capital of France?",
            all_documents=all_documents
        )
        
        # Check that only the initial docs were used
        self.assertEqual(len(processed_step["documents"]), 2)
        self.assertEqual(processed_step["documents"][0].id, "init_doc1")
        self.assertEqual(processed_step["documents"][1].id, "init_doc2")
        
        # Verify the RAG reasoner was NOT called for document retrieval
        self.mock_rag_reasoner.retrieve.assert_not_called()
    
    def test_generate_final_answer(self):
        """Test generation of the final answer."""
        # Mock data
        query = "What is the capital of France?"
        reasoning_steps = [
            {
                "step": 1,
                "description": "Analyze geographical facts about France",
                "reasoning": "France is a country in Western Europe.",
                "documents": [Document(id="geo_doc", content="France is in Europe", source="geo_source")]
            },
            {
                "step": 2,
                "description": "Identify the capital city",
                "reasoning": "The capital of France is Paris.",
                "documents": [Document(id="city_doc", content="Paris is the capital of France", source="city_source")]
            }
        ]
        
        # Mock the model's generate method
        self.mock_model.generate.return_value = "The capital of France is Paris, which is known for the Eiffel Tower."
        
        # Generate the final answer
        final_answer = self.integrator.generate_final_answer(
            query=query,
            reasoning_steps=reasoning_steps
        )
        
        # Check the result
        self.assertEqual(final_answer, "The capital of France is Paris, which is known for the Eiffel Tower.")
        
        # Verify the model was called with the right prompts
        self.mock_model.generate.assert_called_once()
    
    def test_full_reasoning_flow(self):
        """Test the complete reasoning flow with Vietnamese language."""
        # Configure the integrator to use Vietnamese
        self.integrator.language = "vi"
        
        # Create a mock CoT result
        mock_cot_result = MagicMock(spec=ReasoningResult)
        mock_cot_result.answer = """
        Bước 1: Tìm hiểu về nước Pháp
        Pháp là một quốc gia ở Tây Âu.
        
        Bước 2: Xác định thủ đô
        Thủ đô của Pháp là Paris.
        
        Bước 3: Kiểm tra thông tin
        Đã xác nhận Paris là thủ đô của Pháp.
        """
        
        # Mock the cot_reasoner.reason method
        self.mock_cot_reasoner.reason.return_value = mock_cot_result
        
        # Mock the rag_reasoner.retrieve method
        mock_rag_result = MagicMock(spec=ReasoningResult)
        mock_rag_result.metadata = {
            "documents": [
                Document(id="vi_geo_doc", content="Pháp nằm ở châu Âu", source="vi_geo_source"),
                Document(id="vi_city_doc", content="Paris là thủ đô của Pháp", source="vi_city_source"),
                Document(id="vi_fact_doc", content="Paris là thành phố lớn nhất của Pháp", source="vi_fact_source")
            ]
        }
        self.mock_rag_reasoner.retrieve.return_value = mock_rag_result
        
        # Mock the model.generate method for the final answer
        self.mock_model.generate.return_value = "Thủ đô của Pháp là Paris."
        
        # Call the reason method
        result = self.integrator.reason("Thủ đô của Pháp là gì?")
        
        # Verify the result
        self.assertEqual(result.query, "Thủ đô của Pháp là gì?")
        self.assertEqual(result.answer, "Thủ đô của Pháp là Paris.")
        self.assertEqual(result.metadata["language"], "vi")


if __name__ == "__main__":
    unittest.main() 