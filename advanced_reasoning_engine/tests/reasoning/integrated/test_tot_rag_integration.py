"""
Tests for the ToT-RAG integration module.
"""

import unittest
from unittest.mock import MagicMock, patch
import pytest

from deep_research_core.reasoning.integrated.tot_rag_integration import ToTRAGIntegrator
from deep_research_core.models.base import BaseModel
from deep_research_core.reasoning.tot.implementation import TreeOfThought
from deep_research_core.reasoning.rag.implementation import <PERSON><PERSON>easoner
from deep_research_core.utils.types import Document, ThoughtNode, ReasoningResult


class TestToTRAGIntegration(unittest.TestCase):
    """Test suite for the ToT-RAG integration."""

    def setUp(self):
        # Create mock model and reasoners
        self.mock_model = MagicMock(spec=BaseModel)
        self.mock_tot_reasoner = MagicMock(spec=TreeOfThought)
        self.mock_rag_reasoner = MagicMock(spec=RAGReasoner)
        
        # Configure mock behaviors
        self.mock_model.generate.return_value = "This is a mock answer."
        
        self.mock_rag_reasoner.retrieve_documents.return_value = [
            Document(
                id="doc1",
                content="This is document 1 content",
                source="Source 1",
                metadata={"relevance": 0.8}
            ),
            Document(
                id="doc2",
                content="This is document 2 content",
                source="Source 2",
                metadata={"relevance": 0.7}
            )
        ]
        
        # Mock the ToT explore tree method
        self.mock_tot_reasoner._explore_tree.return_value = {
            "id": "root",
            "thought": "I need to answer the question: test query",
            "score": 1.0,
            "depth": 0,
            "documents": self.mock_rag_reasoner.retrieve_documents.return_value,
            "children": [
                {
                    "id": "child1",
                    "thought": "First thought branch",
                    "score": 0.8,
                    "depth": 1,
                    "documents": self.mock_rag_reasoner.retrieve_documents.return_value,
                    "children": [],
                    "parent": MagicMock()  # This will be a reference to the parent in reality
                }
            ]
        }
        
        # Setup the integrator with mocks
        self.integrator = ToTRAGIntegrator(
            model=self.mock_model,
            tot_reasoner=self.mock_tot_reasoner,
            rag_reasoner=self.mock_rag_reasoner,
            verbose=True
        )

    def test_initialization(self):
        """Test the initialization of the ToTRAGIntegrator."""
        self.assertEqual(self.integrator.model, self.mock_model)
        self.assertEqual(self.integrator.tot_reasoner, self.mock_tot_reasoner)
        self.assertEqual(self.integrator.rag_reasoner, self.mock_rag_reasoner)
        self.assertEqual(self.integrator.retrieval_depth, 2)
        self.assertEqual(self.integrator.max_documents_per_node, 3)
        self.assertTrue(self.integrator.verbose)

    def test_reason_basic_flow(self):
        """Test the basic reasoning flow."""
        # Call the reason method
        result = self.integrator.reason("test query")
        
        # Check if the necessary methods were called
        self.mock_rag_reasoner.retrieve_documents.assert_called()
        self.mock_tot_reasoner._explore_tree.assert_called()
        self.mock_model.generate.assert_called()
        
        # Check the result type
        self.assertIsInstance(result, ReasoningResult)
        self.assertEqual(result.query, "test query")
        self.assertEqual(result.answer, "This is a mock answer.")

    def test_document_context_creation(self):
        """Test creation of document context."""
        documents = [
            Document(id="doc1", content="Content 1", source="Source 1"),
            Document(id="doc2", content="Content 2", source="Source 2")
        ]
        
        context = self.integrator._create_document_context(documents)
        
        self.assertIn("Relevant information:", context)
        self.assertIn("[1] Content 1 (Source: Source 1)", context)
        self.assertIn("[2] Content 2 (Source: Source 2)", context)

    def test_empty_document_context(self):
        """Test document context when no documents are provided."""
        context = self.integrator._create_document_context([])
        self.assertEqual(context, "")

    def test_evidence_support_calculation(self):
        """Test evidence support calculation."""
        node = {
            "thought": "This is a test thought",
            "documents": [
                Document(id="doc1", content="Related content", source="Source 1"),
                Document(id="doc2", content="Unrelated content", source="Source 2")
            ]
        }
        
        # Mock the calculate_relevance method if it exists
        with patch.object(self.mock_rag_reasoner, 'calculate_relevance', 
                          side_effect=[0.9, 0.8, 0.5, 0.3], create=True):
            support = self.integrator._calculate_evidence_support(node, "test query")
            self.assertGreaterEqual(support, 0)
            self.assertLessEqual(support, 1)

    def test_coherence_calculation(self):
        """Test coherence calculation."""
        root_node = {"depth": 0}
        node1 = {"depth": 1}
        node2 = {"depth": 2}
        
        coherence0 = self.integrator._calculate_coherence(root_node, root_node)
        coherence1 = self.integrator._calculate_coherence(node1, root_node)
        coherence2 = self.integrator._calculate_coherence(node2, root_node)
        
        self.assertEqual(coherence0, 1.0)  # Root node should have max coherence
        self.assertLess(coherence2, coherence1)  # Deeper nodes should have lower coherence

    def test_select_best_path(self):
        """Test selecting the best path."""
        # Create a simple tree
        leaf1 = {"id": "leaf1", "score": 0.7, "parent": None}
        leaf2 = {"id": "leaf2", "score": 0.9, "parent": None}
        
        node = {"id": "root", "children": [leaf1, leaf2]}
        
        # Set parent references
        leaf1["parent"] = node
        leaf2["parent"] = node
        
        # Patch the _get_leaf_nodes method to return our leaves
        with patch.object(self.integrator, '_get_leaf_nodes', return_value=[leaf1, leaf2]):
            path = self.integrator._select_best_path(node)
            
            # Should select the path to leaf2 as it has higher score
            self.assertEqual(len(path), 2)
            self.assertEqual(path[0]["id"], "root")
            self.assertEqual(path[1]["id"], "leaf2")

    def test_get_leaf_nodes(self):
        """Test getting leaf nodes."""
        # Create a simple tree
        leaf1 = {"id": "leaf1", "children": []}
        leaf2 = {"id": "leaf2", "children": []}
        
        node1 = {"id": "node1", "children": [leaf1]}
        node2 = {"id": "node2", "children": [leaf2]}
        
        root = {"id": "root", "children": [node1, node2]}
        
        leaves = self.integrator._get_leaf_nodes(root)
        
        self.assertEqual(len(leaves), 2)
        leaf_ids = [leaf["id"] for leaf in leaves]
        self.assertIn("leaf1", leaf_ids)
        self.assertIn("leaf2", leaf_ids)


@pytest.mark.parametrize(
    "query,expected_contains",
    [
        ("What are the effects of climate change?", "climate change"),
        ("Who was Albert Einstein?", "Albert Einstein"),
    ]
)
def test_final_answer_generation(query, expected_contains):
    """Test final answer generation with different queries."""
    mock_model = MagicMock(spec=BaseModel)
    mock_model.generate.return_value = f"Answer about {expected_contains}"
    
    integrator = ToTRAGIntegrator(model=mock_model)
    
    # Create a simple path
    path = [
        {"thought": f"I need to answer about {expected_contains}"},
        {"thought": f"Let me think about {expected_contains}"},
        {"thought": f"I can conclude about {expected_contains}"}
    ]
    
    documents = [
        Document(id="doc1", content=f"Facts about {expected_contains}", source="Source 1")
    ]
    
    answer = integrator._generate_final_answer(query, path, documents)
    
    assert expected_contains in answer.lower() 