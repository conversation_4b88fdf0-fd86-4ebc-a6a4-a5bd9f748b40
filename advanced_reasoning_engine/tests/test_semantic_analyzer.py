#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Tests for SemanticAnalyzer.
"""

import unittest
import os
import sys
from unittest.mock import patch, MagicMock

# Add parent directory to path to import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

try:
    from src.deep_research_core.analyzers.semantic_analyzer import SemanticAnalyzer
except ImportError:
    # Mock SemanticAnalyzer if not available
    SemanticAnalyzer = MagicMock()

class TestSemanticAnalyzer(unittest.TestCase):
    """
    Test cases for SemanticAnalyzer.
    """
    
    def setUp(self):
        """
        Set up test fixtures.
        """
        # Skip tests if SemanticAnalyzer is not available
        if isinstance(SemanticAnalyzer, MagicMock):
            self.skipTest("SemanticAnalyzer not available")
        
        # Create SemanticAnalyzer instance
        self.analyzer = SemanticAnalyzer(
            language="auto",
            use_nlp=True,
            use_entity_recognition=True,
            use_sentiment_analysis=True,
            use_topic_analysis=True,
            use_relation_extraction=True,
            use_classification=True,
            use_summarization=True,
            use_keyword_extraction=True,
            use_question_answering=True,
            use_vietnamese_nlp=True,
            use_english_nlp=True,
            cache_enabled=True,
            cache_ttl=3600,
            cache_size=1000,
            verbose=False
        )
    
    def test_initialization(self):
        """
        Test initialization of SemanticAnalyzer.
        """
        self.assertIsNotNone(self.analyzer)
        self.assertEqual(self.analyzer.language, "auto")
        self.assertTrue(self.analyzer.use_nlp)
        self.assertTrue(self.analyzer.use_entity_recognition)
        self.assertTrue(self.analyzer.use_sentiment_analysis)
        self.assertTrue(self.analyzer.use_topic_analysis)
        self.assertTrue(self.analyzer.use_relation_extraction)
        self.assertTrue(self.analyzer.use_classification)
        self.assertTrue(self.analyzer.use_summarization)
        self.assertTrue(self.analyzer.use_keyword_extraction)
        self.assertTrue(self.analyzer.use_question_answering)
        self.assertTrue(self.analyzer.use_vietnamese_nlp)
        self.assertTrue(self.analyzer.use_english_nlp)
        self.assertTrue(self.analyzer.cache_enabled)
        self.assertEqual(self.analyzer.cache_ttl, 3600)
        self.assertEqual(self.analyzer.cache_size, 1000)
        self.assertFalse(self.analyzer.verbose)
    
    def test_analyze_content_english(self):
        """
        Test analyzing English content.
        """
        content = """
        The quick brown fox jumps over the lazy dog.
        This is a test sentence for analyzing English content.
        Natural language processing is a field of artificial intelligence.
        """
        
        result = self.analyzer.analyze_content(content)
        
        # Verify result
        self.assertTrue(result["success"])
        self.assertIn("language", result)
        self.assertIn("analysis", result)
        self.assertIn("tokens", result["analysis"])
        self.assertIn("sentences", result["analysis"])
        self.assertIn("word_count", result["analysis"])
        self.assertIn("sentence_count", result["analysis"])
        self.assertIn("char_count", result["analysis"])
        self.assertIn("avg_word_length", result["analysis"])
        self.assertIn("avg_sentence_length", result["analysis"])
        self.assertIn("freq_dist", result["analysis"])
        self.assertIn("top_words", result["analysis"])
    
    def test_analyze_content_vietnamese(self):
        """
        Test analyzing Vietnamese content.
        """
        content = """
        Đây là một câu tiếng Việt để kiểm tra khả năng phân tích.
        Xử lý ngôn ngữ tự nhiên là một lĩnh vực của trí tuệ nhân tạo.
        Việt Nam là một quốc gia ở Đông Nam Á.
        """
        
        result = self.analyzer.analyze_content(content)
        
        # Verify result
        self.assertTrue(result["success"])
        self.assertIn("language", result)
        self.assertIn("analysis", result)
        self.assertIn("tokens", result["analysis"])
        self.assertIn("sentences", result["analysis"])
        self.assertIn("word_count", result["analysis"])
        self.assertIn("sentence_count", result["analysis"])
        self.assertIn("char_count", result["analysis"])
        self.assertIn("avg_word_length", result["analysis"])
        self.assertIn("avg_sentence_length", result["analysis"])
        self.assertIn("freq_dist", result["analysis"])
        self.assertIn("top_words", result["analysis"])
    
    def test_cache(self):
        """
        Test caching.
        """
        content = "This is a test sentence for caching."
        
        # First request
        result1 = self.analyzer.analyze_content(content)
        
        # Second request (should use cache)
        result2 = self.analyzer.analyze_content(content)
        
        # Verify cache hit
        self.assertEqual(self.analyzer.stats["cache_hits"], 1)
        self.assertEqual(self.analyzer.stats["cache_misses"], 1)
        
        # Verify results are the same
        self.assertEqual(result1["analysis"], result2["analysis"])
    
    def test_error_handling(self):
        """
        Test error handling.
        """
        # Mock analyze_content to raise an exception
        with patch.object(self.analyzer, '_analyze_content', side_effect=Exception("Test error")):
            result = self.analyzer.analyze_content("Test content")
            
            # Verify result
            self.assertFalse(result["success"])
            self.assertIn("error", result)
            self.assertEqual(result["error_type"], "Exception")
            self.assertEqual(result["error"], "Test error")

if __name__ == '__main__':
    unittest.main()
