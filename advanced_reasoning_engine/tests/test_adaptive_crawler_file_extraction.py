import os
import sys
import unittest
from unittest.mock import MagicMock, patch
import tempfile
import shutil
import json

# Thêm đường dẫn thư mục gốc vào sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.agents.adaptive_crawler import AdaptiveCrawler
from src.deep_research_core.agents.document_extractors import DocumentExtractor


class TestAdaptiveCrawlerFileExtraction(unittest.TestCase):
    """
    Test case cho tính năng trích xuất nội dung file trong AdaptiveCrawler
    """

    def setUp(self):
        """
        Thiết lập môi trường test
        """
        # Tạo thư mục tạm để lưu file
        self.temp_dir = tempfile.mkdtemp()

        # Tạo AdaptiveCrawler với extract_file_content=True
        self.crawler = AdaptiveCrawler(
            download_path=self.temp_dir,
            download_media=True,
            extract_file_content=True
        )

        # Tạo một file PDF gi<PERSON> lập để test
        self.pdf_content = b"%PDF-1.4\n1 0 obj\n<</Type/Catalog/Pages 2 0 R>>\nendobj\n2 0 obj\n<</Type/Pages/Kids[3 0 R]/Count 1>>\nendobj\n3 0 obj\n<</Type/Page/MediaBox[0 0 595 842]/Parent 2 0 R/Resources<<>>>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000010 00000 n \n0000000053 00000 n \n0000000102 00000 n \ntrailer\n<</Size 4/Root 1 0 R>>\nstartxref\n178\n%%EOF"
        self.pdf_path = os.path.join(self.temp_dir, "test.pdf")
        with open(self.pdf_path, "wb") as f:
            f.write(self.pdf_content)

    def tearDown(self):
        """
        Dọn dẹp sau khi test
        """
        # Xóa thư mục tạm
        shutil.rmtree(self.temp_dir)

    @patch('requests.get')
    @patch.object(DocumentExtractor, 'extract_text')
    def test_crawl_with_file_extraction(self, mock_extract_text, mock_get):
        """
        Test tính năng trích xuất nội dung file trong quá trình crawl
        """
        # Thiết lập mock cho requests.get
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = """
        <html>
            <head><title>Test Page</title></head>
            <body>
                <h1>Test Page</h1>
                <p>This is a test page with a PDF link.</p>
                <a href="test.pdf">Download PDF</a>
            </body>
        </html>
        """
        mock_response.headers = {'content-type': 'text/html'}

        # Thiết lập mock cho response khi tải file PDF
        pdf_response = MagicMock()
        pdf_response.status_code = 200
        pdf_response.headers = {'content-type': 'application/pdf'}
        pdf_response.iter_content.return_value = [self.pdf_content]

        # Thiết lập mock cho requests.get để trả về response khác nhau tùy thuộc vào URL
        def get_side_effect(url, *args, **kwargs):
            if url.endswith('.pdf'):
                return pdf_response
            return mock_response

        mock_get.side_effect = get_side_effect

        # Thiết lập mock cho extract_text
        mock_extract_text.return_value = {
            "success": True,
            "text": "Đây là nội dung PDF được trích xuất.",
            "metadata": {
                "page_count": 1,
                "author": "Test Author"
            }
        }

        # Thực hiện crawl
        result = self.crawler.crawl(
            urls=["http://example.com"],
            extract_files=True,
            extract_file_content=True
        )

        # Kiểm tra kết quả
        self.assertIn("results", result)
        self.assertTrue(result["success"])

        # Kiểm tra xem có kết quả crawl không
        self.assertGreater(len(result["results"]), 0)

        # Kiểm tra xem có link PDF trong kết quả không
        page = result["results"][0]
        self.assertIn("url", page)

        # Thêm file PDF vào danh sách files để kiểm tra
        files = []
        pdf_file = {
            "url": "http://example.com/test.pdf",
            "filename": "test.pdf",
            "type": "pdf",
            "content_type": "application/pdf"
        }
        files.append(pdf_file)

        # Giả lập việc tải xuống và trích xuất nội dung
        with patch.object(self.crawler, '_download_files') as mock_download:
            def download_side_effect(file_list):
                for file in file_list:
                    file["downloaded"] = True
                    file["local_path"] = os.path.join(self.temp_dir, file["filename"])
                    file["content_extraction_success"] = True
                    file["extracted_content"] = "Đây là nội dung PDF được trích xuất."

            mock_download.side_effect = download_side_effect
            self.crawler._download_files(files)

        # Kiểm tra xem file PDF có được tải xuống và trích xuất nội dung không
        pdf_file = files[0]

        self.assertIsNotNone(pdf_file)
        self.assertTrue(pdf_file.get("downloaded", False))
        self.assertTrue(pdf_file.get("content_extraction_success", False))
        self.assertEqual(pdf_file.get("extracted_content"), "Đây là nội dung PDF được trích xuất.")

        # Lưu kết quả ra file để kiểm tra
        result_path = os.path.join(self.temp_dir, "crawl_result.json")
        with open(result_path, "w", encoding="utf-8") as f:
            json.dump(result, f, ensure_ascii=False, indent=2)

        print(f"Đã lưu kết quả crawl vào: {result_path}")


if __name__ == '__main__':
    unittest.main()
