"""
Tests for the Vietnamese language support in Agent Environment.
"""

import unittest
from unittest.mock import MagicMock, patch

from deep_research_core.rl_tuning.environment.vietnamese_support import VietnameseSupport
from deep_research_core.rl_tuning.environment.agent_environment import AgentEnvironment


class TestVietnameseAgentSupport(unittest.TestCase):
    """Test cases for Vietnamese language support in Agent Environment."""

    def setUp(self):
        """Set up test environment."""
        # Create a mock for language detection and translation
        self.mock_detect = MagicMock(return_value="vi")
        self.mock_translate = MagicMock(return_value="Translated text")
        
        # Create VietnameseSupport instance with mocked dependencies
        with patch("deep_research_core.rl_tuning.environment.vietnamese_support.detect", self.mock_detect):
            with patch("deep_research_core.rl_tuning.environment.vietnamese_support.translator", MagicMock()):
                self.vietnamese_support = VietnameseSupport(fallback_to_english=False, verbose=True)
                
                # Mock translator method
                self.vietnamese_support.translate = self.mock_translate
                
                # Set templates available flag
                self.vietnamese_support.vietnamese_templates_available = True
                
                # Create mock get_vietnamese_template_fn
                self.mock_get_template = MagicMock()
                self.mock_get_template.return_value = "Template content"
                self.vietnamese_support.get_vietnamese_template_fn = self.mock_get_template

    def test_is_vietnamese(self):
        """Test Vietnamese language detection."""
        # Test with Vietnamese characters
        self.assertTrue(self.vietnamese_support.is_vietnamese("Xin chào"))
        
        # Test with non-Vietnamese text
        self.mock_detect.return_value = "en"
        self.assertFalse(self.vietnamese_support.is_vietnamese("Hello"))
        
        # Test with mixed text
        self.mock_detect.return_value = "vi"
        self.assertTrue(self.vietnamese_support.is_vietnamese("Hello và xin chào"))

    def test_get_prompt_template(self):
        """Test getting Vietnamese prompt templates."""
        # Test with available template
        template = self.vietnamese_support.get_prompt_template(
            template_key="default",
            template_type="system",
            domain="medical"
        )
        self.assertEqual(template, "Template content")
        self.mock_get_template.assert_called_with(
            template_type="system",
            template_key="default",
            domain="medical"
        )
        
        # Test with unavailable template but fallback
        self.mock_get_template.return_value = ""
        self.vietnamese_support.prompt_templates = {"fallback_key": "Fallback content"}
        template = self.vietnamese_support.get_prompt_template(
            template_key="fallback_key",
            template_type="system"
        )
        self.assertEqual(template, "Fallback content")
        
        # Test with unavailable template and no fallback
        template = self.vietnamese_support.get_prompt_template(
            template_key="nonexistent_key",
            template_type="system",
            english_fallback="English fallback"
        )
        self.assertEqual(template, "English fallback")

    def test_process_observation(self):
        """Test processing observations for Vietnamese support."""
        # Create a test observation
        observation = {
            "query": "Xin chào, đây là câu hỏi tiếng Việt",
            "context": "This is English context",
            "tools": [
                {"name": "tool1", "description": "Tool description in English"}
            ]
        }
        
        # Process observation
        processed = self.vietnamese_support.process_observation(observation)
        
        # Check that language was detected
        self.assertEqual(processed["language"], "vi")
        
        # Check that context was translated
        self.mock_translate.assert_any_call("This is English context", source_lang='en', target_lang='vi')
        
        # Check that tool description was translated
        self.mock_translate.assert_any_call("Tool description in English", source_lang='en', target_lang='vi')

    def test_process_action(self):
        """Test processing actions for Vietnamese support."""
        # Create a test action
        action = {
            "action_type": "respond",
            "content": "Đây là câu trả lời tiếng Việt"
        }
        
        # Process action with Vietnamese source
        processed = self.vietnamese_support.process_action(action, source_lang='vi')
        
        # Check that content was translated to English (fallback_to_english=False in setUp)
        self.assertEqual(processed["content"], "Đây là câu trả lời tiếng Việt")
        
        # Change fallback_to_english to True
        self.vietnamese_support.fallback_to_english = True
        
        # Process action again
        processed = self.vietnamese_support.process_action(action, source_lang='vi')
        
        # Check that content was translated to English
        self.mock_translate.assert_called_with("Đây là câu trả lời tiếng Việt", source_lang='vi', target_lang='en')

    def test_enhance_vietnamese_response(self):
        """Test enhancing Vietnamese responses."""
        # Create a test response
        response = "Đây là câu trả lời tiếng Việt không có lời chào và kết thúc."
        
        # Mock is_vietnamese to return True
        self.vietnamese_support.is_vietnamese = MagicMock(return_value=True)
        
        # Mock time of day and greeting/closing functions
        self.vietnamese_support._get_time_of_day = MagicMock(return_value="morning")
        self.vietnamese_support._get_vietnamese_greeting = MagicMock(return_value="Chào buổi sáng!")
        self.vietnamese_support._get_vietnamese_closing = MagicMock(return_value="Trân trọng,")
        
        # Enhance response
        enhanced = self.vietnamese_support.enhance_vietnamese_response(response)
        
        # Check that greeting and closing were added
        self.assertIn("Chào buổi sáng!", enhanced)
        self.assertIn("Trân trọng,", enhanced)
        self.assertIn(response, enhanced)

    def test_agent_environment_integration(self):
        """Test integration with AgentEnvironment."""
        # Create a mock LLM
        mock_llm = MagicMock()
        mock_llm.set_system_prompt = MagicMock()
        
        # Create a mock for VietnameseSupport
        mock_vietnamese_support = MagicMock()
        mock_vietnamese_support.get_prompt_template.return_value = "Vietnamese system prompt"
        
        # Create AgentEnvironment with mocked dependencies
        with patch("deep_research_core.rl_tuning.environment.agent_environment.VietnameseSupport", 
                  return_value=mock_vietnamese_support):
            # Mock LLMIntegration
            with patch("deep_research_core.rl_tuning.environment.agent_environment.LLMIntegration"):
                env = AgentEnvironment(
                    name="test_env",
                    llm_integration={"model_id": "test-model"},
                    enable_vietnamese=True,
                    vietnamese_config={"fallback_to_english": False, "domain": "medical"}
                )
                
                # Check that Vietnamese support was set up
                self.assertEqual(env.vietnamese_support, mock_vietnamese_support)
                self.assertEqual(env.vietnamese_domain, "medical")
                
                # Check that system prompt was set
                mock_vietnamese_support.get_prompt_template.assert_called_with(
                    template_key="default",
                    template_type="system",
                    domain="medical"
                )

    def test_process_vietnamese_action_result(self):
        """Test processing action results for Vietnamese support."""
        # Create a mock for AgentEnvironment
        env = MagicMock()
        env.vietnamese_support = self.vietnamese_support
        env.vietnamese_domain = "education"
        
        # Create a test info dictionary
        info = {
            "error": "Timeout occurred while processing action",
            "response_feedback": "Your response was good but could be improved",
            "response": "This is a response in English"
        }
        
        # Create a test action
        action = {
            "action_type": "respond",
            "content": "This is an action"
        }
        
        # Get the _process_vietnamese_action_result method
        with patch("deep_research_core.rl_tuning.environment.agent_environment.AgentEnvironment._process_vietnamese_action_result") as mock_process:
            # Set up the mock to call the actual method
            mock_process.side_effect = lambda info, action: AgentEnvironment._process_vietnamese_action_result(env, info, action)
            
            # Process the action result
            processed_info = mock_process(info, action)
            
            # Check that error was processed
            self.mock_get_template.assert_any_call(
                template_key="timeout",
                template_type="error",
                domain="education"
            )
            
            # Check that response feedback was translated
            self.mock_translate.assert_any_call("Your response was good but could be improved")


if __name__ == "__main__":
    unittest.main()
