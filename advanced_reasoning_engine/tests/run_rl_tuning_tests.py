#!/usr/bin/env python
"""
Test runner for RL-Tuning Model Paradigm tests.

This script runs all the unit tests for the RL-Tuning Model Paradigm module.
"""

import os
import sys
import unittest
import argparse


def run_tests(test_pattern=None, verbose=False):
    """
    Run the RL-Tuning Model Paradigm tests.
    
    Args:
        test_pattern: Optional pattern to filter tests
        verbose: Whether to print verbose output
    
    Returns:
        True if all tests pass, False otherwise
    """
    # Get the directory of this script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Add the parent directory to the Python path
    parent_dir = os.path.dirname(script_dir)
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)
    
    # Create a test loader
    loader = unittest.TestLoader()
    
    # Load tests from the unit/rl_tuning/model_paradigm directory
    test_dir = os.path.join(script_dir, "unit", "rl_tuning", "model_paradigm")
    
    if test_pattern:
        # Load tests matching the pattern
        suite = loader.discover(test_dir, pattern=f"test_{test_pattern}.py")
    else:
        # Load all tests
        suite = loader.discover(test_dir)
    
    # Create a test runner
    runner = unittest.TextTestRunner(verbosity=2 if verbose else 1)
    
    # Run the tests
    result = runner.run(suite)
    
    # Return True if all tests pass
    return result.wasSuccessful()


if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Run RL-Tuning Model Paradigm tests")
    parser.add_argument("--pattern", help="Pattern to filter tests")
    parser.add_argument("--verbose", action="store_true", help="Print verbose output")
    args = parser.parse_args()
    
    # Run the tests
    success = run_tests(args.pattern, args.verbose)
    
    # Exit with appropriate status code
    sys.exit(0 if success else 1)
