"""
Tests for the performance profiler.
"""

import unittest
import time
from datetime import datetime, timedelta
from src.deep_research_core.web.utils.performance_profiler import PerformanceProfiler

class TestPerformanceProfiler(unittest.TestCase):
    """Test cases for the PerformanceProfiler class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Clear any existing profiling data
        PerformanceProfiler._active_sessions = {}
        PerformanceProfiler._performance_data = []
        PerformanceProfiler._component_data = []
        PerformanceProfiler._bottleneck_data = []
        PerformanceProfiler._flame_graph_data = None
        PerformanceProfiler._is_monitoring_active = False
        
        # Start a test session
        self.session_result = PerformanceProfiler.start_profiling()
        self.session_id = self.session_result['session_id']
    
    def tearDown(self):
        """Tear down test fixtures."""
        # Stop any active sessions
        if PerformanceProfiler._is_monitoring_active:
            for session_id in list(PerformanceProfiler._active_sessions.keys()):
                PerformanceProfiler.stop_profiling(session_id)
    
    def test_start_profiling(self):
        """Test starting a profiling session."""
        # Check that the session was created successfully
        self.assertTrue(self.session_result['success'])
        self.assertIsNotNone(self.session_id)
        self.assertIn('start_time', self.session_result)
        
        # Check that the session exists in active sessions
        self.assertIn(self.session_id, PerformanceProfiler._active_sessions)
        
        # Check that monitoring is active
        self.assertTrue(PerformanceProfiler._is_monitoring_active)
    
    def test_stop_profiling(self):
        """Test stopping a profiling session."""
        # Record some test data
        PerformanceProfiler.record_function_call(
            component='test_component',
            function='test_function',
            execution_time=100.0,
            memory_usage=10.0,
            error=False,
            session_id=self.session_id
        )
        
        # Stop the session
        stop_result = PerformanceProfiler.stop_profiling(self.session_id)
        
        # Check that the session was stopped successfully
        self.assertTrue(stop_result['success'])
        self.assertEqual(stop_result['session_id'], self.session_id)
        self.assertIn('duration', stop_result)
        
        # Check that the session was removed from active sessions
        self.assertNotIn(self.session_id, PerformanceProfiler._active_sessions)
        
        # Check that performance data was generated
        self.assertGreater(len(PerformanceProfiler._performance_data), 0)
        self.assertGreater(len(PerformanceProfiler._component_data), 0)
    
    def test_record_function_call(self):
        """Test recording a function call."""
        # Record a function call
        PerformanceProfiler.record_function_call(
            component='test_component',
            function='test_function',
            execution_time=100.0,
            memory_usage=10.0,
            error=False,
            session_id=self.session_id
        )
        
        # Check that the function call was recorded
        session = PerformanceProfiler._active_sessions[self.session_id]
        self.assertIn('test_component', session['components'])
        self.assertEqual(len(session['components']['test_component']), 1)
        
        func_data = session['components']['test_component'][0]
        self.assertEqual(func_data['function'], 'test_function')
        self.assertEqual(func_data['calls'], 1)
        self.assertEqual(func_data['total_time'], 100.0)
        self.assertEqual(func_data['max_time'], 100.0)
        self.assertEqual(func_data['memory'], 10.0)
        self.assertEqual(func_data['errors'], 0)
        
        # Check that latency was recorded
        self.assertIn(100.0, session['metrics']['latency'])
    
    def test_record_function_call_with_error(self):
        """Test recording a function call with an error."""
        # Record a function call with an error
        PerformanceProfiler.record_function_call(
            component='test_component',
            function='test_function',
            execution_time=100.0,
            memory_usage=10.0,
            error=True,
            session_id=self.session_id
        )
        
        # Check that the error was recorded
        session = PerformanceProfiler._active_sessions[self.session_id]
        func_data = session['components']['test_component'][0]
        self.assertEqual(func_data['errors'], 1)
    
    def test_record_multiple_function_calls(self):
        """Test recording multiple function calls."""
        # Record multiple function calls
        PerformanceProfiler.record_function_call(
            component='test_component',
            function='test_function',
            execution_time=100.0,
            memory_usage=10.0,
            error=False,
            session_id=self.session_id
        )
        
        PerformanceProfiler.record_function_call(
            component='test_component',
            function='test_function',
            execution_time=200.0,
            memory_usage=20.0,
            error=True,
            session_id=self.session_id
        )
        
        # Check that the function calls were aggregated
        session = PerformanceProfiler._active_sessions[self.session_id]
        func_data = session['components']['test_component'][0]
        self.assertEqual(func_data['calls'], 2)
        self.assertEqual(func_data['total_time'], 300.0)
        self.assertEqual(func_data['max_time'], 200.0)
        self.assertEqual(func_data['memory'], 30.0)
        self.assertEqual(func_data['errors'], 1)
        
        # Check that latencies were recorded
        self.assertIn(100.0, session['metrics']['latency'])
        self.assertIn(200.0, session['metrics']['latency'])
    
    def test_get_performance_data(self):
        """Test getting performance data."""
        # Record some test data and stop the session
        PerformanceProfiler.record_function_call(
            component='test_component',
            function='test_function',
            execution_time=100.0,
            memory_usage=10.0,
            error=False,
            session_id=self.session_id
        )
        
        PerformanceProfiler.stop_profiling(self.session_id)
        
        # Get performance data
        data = PerformanceProfiler.get_performance_data()
        
        # Check that data was returned
        self.assertIn('performance_data', data)
        self.assertIn('component_data', data)
        self.assertIn('flame_graph_data', data)
        self.assertIn('bottleneck_data', data)
        
        # Check that performance data contains the expected fields
        perf_data = data['performance_data'][0]
        self.assertIn('timestamp', perf_data)
        self.assertIn('avg_latency', perf_data)
        self.assertIn('max_latency', perf_data)
        self.assertIn('min_latency', perf_data)
        self.assertIn('throughput', perf_data)
        self.assertIn('cpu_usage', perf_data)
        self.assertIn('memory_usage', perf_data)
        self.assertIn('duration', perf_data)
        
        # Check that component data contains the expected fields
        comp_data = data['component_data'][0]
        self.assertEqual(comp_data['component'], 'test_component')
        self.assertEqual(comp_data['function'], 'test_function')
        self.assertEqual(comp_data['calls'], 1)
        self.assertEqual(comp_data['avg_latency'], 100.0)
        self.assertEqual(comp_data['max_latency'], 100.0)
        self.assertEqual(comp_data['memory'], 10.0)
        self.assertEqual(comp_data['errors'], 0)
    
    def test_get_performance_data_with_filters(self):
        """Test getting performance data with filters."""
        # Record some test data for different components
        PerformanceProfiler.record_function_call(
            component='component1',
            function='function1',
            execution_time=100.0,
            memory_usage=10.0,
            error=False,
            session_id=self.session_id
        )
        
        PerformanceProfiler.record_function_call(
            component='component2',
            function='function2',
            execution_time=200.0,
            memory_usage=20.0,
            error=False,
            session_id=self.session_id
        )
        
        PerformanceProfiler.stop_profiling(self.session_id)
        
        # Get performance data filtered by component
        data = PerformanceProfiler.get_performance_data(component='component1')
        
        # Check that only component1 data is returned
        self.assertEqual(len(data['component_data']), 1)
        self.assertEqual(data['component_data'][0]['component'], 'component1')
    
    def test_generate_mock_data(self):
        """Test generating mock performance data."""
        # Generate mock data
        PerformanceProfiler.generate_mock_data()
        
        # Check that mock data was generated
        self.assertGreater(len(PerformanceProfiler._performance_data), 0)
        self.assertGreater(len(PerformanceProfiler._component_data), 0)
        self.assertIsNotNone(PerformanceProfiler._flame_graph_data)
        
        # Get performance data
        data = PerformanceProfiler.get_performance_data()
        
        # Check that data was returned
        self.assertGreater(len(data['performance_data']), 0)
        self.assertGreater(len(data['component_data']), 0)
        self.assertIsNotNone(data['flame_graph_data'])

if __name__ == '__main__':
    unittest.main()
