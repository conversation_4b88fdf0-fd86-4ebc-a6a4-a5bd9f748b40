"""
Unit tests for QueryAnalyzerCache.
"""

import os
import sys
import time
import unittest
import tempfile
import shutil
from unittest.mock import patch, MagicMock

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# Import QueryAnalyzerCache
try:
    from src.deep_research_core.utils.query_analyzer_cache import QueryAnalyzerCache
    cache_available = True
except ImportError:
    cache_available = False


@unittest.skipIf(not cache_available, "QueryAnalyzerCache not available")
class TestQueryAnalyzerCache(unittest.TestCase):
    """Test cases for QueryAnalyzerCache."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary directory for cache
        self.temp_dir = tempfile.mkdtemp()
        self.cache = QueryAnalyzerCache(
            cache_dir=self.temp_dir,
            memory_limit=10,
            disk_limit=20,
            memory_ttl=1,  # 1 second for testing
            disk_ttl=2,    # 2 seconds for testing
            enable_semantic_search=False  # Disable semantic search for testing
        )

    def tearDown(self):
        """Clean up test fixtures."""
        # Remove the temporary directory
        shutil.rmtree(self.temp_dir)

    def test_set_get(self):
        """Test set and get methods."""
        # Set a value
        test_query = "python programming"
        test_result = {"query_type": "technical", "complexity": 0.7}
        self.cache.set(test_query, test_result)

        # Get the value
        result = self.cache.get(test_query)

        # Check the value
        self.assertEqual(result, test_result)

    def test_memory_cache_hit(self):
        """Test memory cache hit."""
        # Set a value
        test_query = "python programming"
        test_result = {"query_type": "technical", "complexity": 0.7}
        self.cache.set(test_query, test_result)

        # Get the value
        result = self.cache.get(test_query)

        # Check the value
        self.assertEqual(result, test_result)

        # Check memory hit count
        self.assertEqual(self.cache.stats["memory_hits"], 1)

    def test_disk_cache_hit(self):
        """Test disk cache hit."""
        # Set a value
        test_query = "python programming"
        test_result = {"query_type": "technical", "complexity": 0.7}
        self.cache.set(test_query, test_result)

        # Clear memory cache
        self.cache.memory_cache.clear()

        # Get the value
        result = self.cache.get(test_query)

        # Check the value
        self.assertEqual(result, test_result)

        # Check disk hit count
        self.assertEqual(self.cache.stats["disk_hits"], 1)

    def test_cache_miss(self):
        """Test cache miss."""
        # Get a non-existent value
        result = self.cache.get("non_existent_query")

        # Check the value
        self.assertIsNone(result)

        # Check miss count
        self.assertEqual(self.cache.stats["misses"], 1)

    def test_memory_ttl(self):
        """Test memory TTL."""
        # Set a value with short TTL
        test_query = "python programming"
        test_result = {"query_type": "technical", "complexity": 0.7}
        self.cache.set(test_query, test_result)

        # Get the value immediately
        result = self.cache.get(test_query)
        self.assertEqual(result, test_result)

        # Wait for memory TTL to expire
        time.sleep(1.1)

        # Get the value again (should hit disk cache)
        result = self.cache.get(test_query)
        self.assertEqual(result, test_result)

        # Check hit counts
        self.assertEqual(self.cache.stats["memory_hits"], 1)
        self.assertEqual(self.cache.stats["disk_hits"], 1)

    def test_disk_ttl(self):
        """Test disk TTL."""
        # Set a value with short TTL
        test_query = "python programming"
        test_result = {"query_type": "technical", "complexity": 0.7}
        self.cache.set(test_query, test_result)

        # Wait for both memory and disk TTL to expire
        time.sleep(2.1)

        # Get the value again (should miss)
        result = self.cache.get(test_query)
        self.assertIsNone(result)

        # Check miss count
        self.assertEqual(self.cache.stats["misses"], 1)

    def test_delete(self):
        """Test delete method."""
        # Set a value
        test_query = "python programming"
        test_result = {"query_type": "technical", "complexity": 0.7}
        self.cache.set(test_query, test_result)

        # Delete the key
        result = self.cache.delete(test_query)

        # Check the result
        self.assertTrue(result)

        # Check if key exists
        self.assertIsNone(self.cache.get(test_query))

    def test_clear(self):
        """Test clear method."""
        # Set multiple values
        self.cache.set("query1", {"result": 1})
        self.cache.set("query2", {"result": 2})

        # Clear the cache
        result = self.cache.clear()

        # Check the result
        self.assertTrue(result)

        # Check if keys exist
        self.assertIsNone(self.cache.get("query1"))
        self.assertIsNone(self.cache.get("query2"))

    def test_memory_eviction(self):
        """Test memory eviction."""
        # Fill memory cache
        for i in range(15):
            self.cache.set(f"query{i}", {"result": i})

        # Check memory cache size
        self.assertLessEqual(len(self.cache.memory_cache), self.cache.memory_limit)

    def test_stats(self):
        """Test stats method."""
        # Set and get some values
        self.cache.set("query1", {"result": 1})
        self.cache.set("query2", {"result": 2})
        self.cache.get("query1")
        self.cache.get("non_existent_query")

        # Get stats
        stats = self.cache.get_stats()

        # Check stats
        self.assertEqual(stats["memory_cache"]["hits"], 1)
        self.assertEqual(stats["overall"]["misses"], 1)
        self.assertEqual(stats["memory_cache"]["size"], 2)
        self.assertEqual(stats["disk_cache"]["size"], 2)


@unittest.skipIf(not cache_available, "QueryAnalyzerCache not available")
class TestLocalQueryAnalyzerWithCache(unittest.TestCase):
    """Test cases for LocalQueryAnalyzer with cache."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary directory for cache
        self.temp_dir = tempfile.mkdtemp()

        # Import LocalQueryAnalyzer
        try:
            from src.deep_research_core.utils.local_query_analyzer import LocalQueryAnalyzer
            self.LocalQueryAnalyzer = LocalQueryAnalyzer
        except ImportError:
            self.skipTest("LocalQueryAnalyzer not available")

        # Create a LocalQueryAnalyzer with cache
        self.analyzer = self.LocalQueryAnalyzer(
            use_cache=True,
            cache_config={
                "cache_dir": self.temp_dir,
                "memory_limit": 10,
                "disk_limit": 20,
                "memory_ttl": 1,  # 1 second for testing
                "disk_ttl": 2,    # 2 seconds for testing
                "enable_semantic_search": False  # Disable semantic search for testing
            }
        )

    def tearDown(self):
        """Clean up test fixtures."""
        # Remove the temporary directory
        shutil.rmtree(self.temp_dir)

    def test_cache_hit(self):
        """Test cache hit."""
        # Analyze a query
        query = "python programming"
        result1 = self.analyzer.analyze_query(query)

        # Analyze the same query again
        result2 = self.analyzer.analyze_query(query)

        # Check that the results are the same
        self.assertEqual(result1["query_type"], result2["query_type"])
        self.assertEqual(result1["complexity"], result2["complexity"])

        # Check that the cache was used
        self.assertEqual(self.analyzer.cache.stats["memory_hits"], 1)

    def test_cache_miss(self):
        """Test cache miss."""
        # Analyze a query
        query1 = "python programming"
        self.analyzer.analyze_query(query1)

        # Analyze a different query
        query2 = "javascript programming"
        self.analyzer.analyze_query(query2)

        # Check that the cache was not used
        self.assertEqual(self.analyzer.cache.stats["misses"], 1)

    def test_cache_ttl(self):
        """Test cache TTL."""
        # Analyze a query
        query = "python programming"
        result1 = self.analyzer.analyze_query(query)

        # Wait for memory TTL to expire
        time.sleep(1.1)

        # Analyze the same query again
        result2 = self.analyzer.analyze_query(query)

        # Check that the results are the same
        self.assertEqual(result1["query_type"], result2["query_type"])
        self.assertEqual(result1["complexity"], result2["complexity"])

        # Check that the disk cache was used
        self.assertEqual(self.analyzer.cache.stats["disk_hits"], 1)


if __name__ == "__main__":
    unittest.main()
