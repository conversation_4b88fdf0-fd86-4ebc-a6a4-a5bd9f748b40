"""
Tests for the error recovery system.

This module contains tests for the error recovery system.
"""

import os
import sys
import unittest
import time
import random
from unittest.mock import MagicMock, patch

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), ".."))

from src.deep_research_core.utils.reasoning_error_recovery import ReasoningErrorRecovery
from src.deep_research_core.utils.error_recovery import <PERSON>rrorRecoveryManager, RetryStrategy, AlternativeToolStrategy, InputReformulationStrategy, FallbackResultStrategy
from src.deep_research_core.utils.intelligent_error_detection import ErrorDetector
from src.deep_research_core.utils.error_learning import ErrorLearningManager
from src.deep_research_core.utils.error_analytics import ErrorAnalytics
from src.deep_research_core.utils.error_visualization import ErrorVisualization
from src.deep_research_core.utils.error_strategy_optimizer import ErrorStrategyOptimizerManager
from src.deep_research_core.utils.error_monitoring import ErrorMonitor
from src.deep_research_core.utils.ml_error_classifier import MLErrorClassifier
from src.deep_research_core.utils.distributed_error_recovery import DistributedErrorRecovery


class TestReasoningErrorRecovery(unittest.TestCase):
    """Tests for the ReasoningErrorRecovery class."""

    def setUp(self):
        """Set up test fixtures."""
        self.recovery = ReasoningErrorRecovery(
            use_error_recovery=True,
            max_retries=3,
            retry_delay=0.1,
            cache_path=None,
            use_advanced_strategies=True
        )

    def test_initialization(self):
        """Test initialization of ReasoningErrorRecovery."""
        self.assertTrue(self.recovery.use_error_recovery)
        self.assertEqual(self.recovery.max_retries, 3)
        self.assertEqual(self.recovery.retry_delay, 0.1)
        self.assertIsNone(self.recovery.cache_path)
        self.assertTrue(self.recovery.use_advanced_strategies)
        self.assertIsNotNone(self.recovery.error_recovery_manager)
        self.assertIsNotNone(self.recovery.error_detector)
        self.assertIsNotNone(self.recovery.error_learning_manager)

    def test_handle_error_with_recovery_disabled(self):
        """Test handling an error with recovery disabled."""
        recovery = ReasoningErrorRecovery(use_error_recovery=False)
        error = ValueError("Test error")
        context = {"tool_name": "test_tool"}
        result = recovery.handle_error(error, context)
        self.assertFalse(result.get("success", False))
        self.assertEqual(result.get("message"), "Error recovery is disabled")

    def test_handle_error_with_recovery_enabled(self):
        """Test handling an error with recovery enabled."""
        # Mock the error recovery manager to always succeed
        self.recovery.error_recovery_manager.recover = MagicMock(return_value={
            "success": True,
            "strategy": "test_strategy",
            "result": "Recovered result"
        })
        
        error = ValueError("Test error")
        context = {"tool_name": "test_tool"}
        result = self.recovery.handle_error(error, context)
        
        self.assertTrue(result.get("success", False))
        self.assertEqual(result.get("strategy"), "test_strategy")
        self.assertEqual(result.get("result"), "Recovered result")

    def test_handle_error_with_recovery_failure(self):
        """Test handling an error with recovery failure."""
        # Mock the error recovery manager to always fail
        self.recovery.error_recovery_manager.recover = MagicMock(return_value={
            "success": False,
            "message": "Recovery failed"
        })
        
        error = ValueError("Test error")
        context = {"tool_name": "test_tool"}
        result = self.recovery.handle_error(error, context)
        
        self.assertFalse(result.get("success", False))
        self.assertEqual(result.get("message"), "Recovery failed")

    def test_handle_error_with_analytics(self):
        """Test handling an error with analytics."""
        # Create a recovery with analytics
        recovery = ReasoningErrorRecovery(
            use_error_recovery=True,
            max_retries=3,
            retry_delay=0.1,
            cache_path=None,
            use_advanced_strategies=True,
            use_error_analytics=True
        )
        
        # Mock the error recovery manager to always succeed
        recovery.error_recovery_manager.recover = MagicMock(return_value={
            "success": True,
            "strategy": "test_strategy",
            "result": "Recovered result"
        })
        
        # Mock the error analytics
        recovery.error_analytics = MagicMock()
        recovery.error_analytics.track_error = MagicMock()
        
        error = ValueError("Test error")
        context = {"tool_name": "test_tool"}
        result = recovery.handle_error(error, context)
        
        # Check that analytics was called
        recovery.error_analytics.track_error.assert_called_once()

    def test_handle_error_with_monitoring(self):
        """Test handling an error with monitoring."""
        # Create a recovery with monitoring
        recovery = ReasoningErrorRecovery(
            use_error_recovery=True,
            max_retries=3,
            retry_delay=0.1,
            cache_path=None,
            use_advanced_strategies=True,
            use_error_monitoring=True
        )
        
        # Mock the error recovery manager to always succeed
        recovery.error_recovery_manager.recover = MagicMock(return_value={
            "success": True,
            "strategy": "test_strategy",
            "result": "Recovered result"
        })
        
        # Mock the error monitor
        recovery.error_monitor = MagicMock()
        recovery.error_monitor.track_error = MagicMock()
        
        error = ValueError("Test error")
        context = {"tool_name": "test_tool"}
        result = recovery.handle_error(error, context)
        
        # Check that monitor was called
        recovery.error_monitor.track_error.assert_called_once()

    def test_handle_error_with_distributed_recovery(self):
        """Test handling an error with distributed recovery."""
        # Create a recovery with distributed recovery
        recovery = ReasoningErrorRecovery(
            use_error_recovery=True,
            max_retries=3,
            retry_delay=0.1,
            cache_path=None,
            use_advanced_strategies=True,
            use_distributed_error_recovery=True
        )
        
        # Mock the error recovery manager to always succeed
        recovery.error_recovery_manager.recover = MagicMock(return_value={
            "success": True,
            "strategy": "test_strategy",
            "result": "Recovered result"
        })
        
        # Mock the distributed error recovery
        recovery.distributed_error_recovery = MagicMock()
        recovery.distributed_error_recovery.track_error = MagicMock()
        
        error = ValueError("Test error")
        context = {"tool_name": "test_tool"}
        result = recovery.handle_error(error, context)
        
        # Check that distributed recovery was called
        recovery.distributed_error_recovery.track_error.assert_called_once()

    def test_handle_error_with_ml_classifier(self):
        """Test handling an error with ML classifier."""
        # Create a recovery with ML classifier
        recovery = ReasoningErrorRecovery(
            use_error_recovery=True,
            max_retries=3,
            retry_delay=0.1,
            cache_path=None,
            use_advanced_strategies=True,
            use_ml_error_classifier=True
        )
        
        # Mock the error recovery manager to always succeed
        recovery.error_recovery_manager.recover = MagicMock(return_value={
            "success": True,
            "strategy": "test_strategy",
            "result": "Recovered result"
        })
        
        # Mock the ML classifier
        recovery.ml_error_classifier = MagicMock()
        recovery.ml_error_classifier.classify = MagicMock(return_value={
            "category": "test_category",
            "confidence": 0.9,
            "severity": "medium",
            "recoverable": True
        })
        
        error = ValueError("Test error")
        context = {"tool_name": "test_tool"}
        result = recovery.handle_error(error, context)
        
        # Check that ML classifier was called
        recovery.ml_error_classifier.classify.assert_called_once()


class TestErrorRecoveryIntegration(unittest.TestCase):
    """Integration tests for the error recovery system."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary directory for cache files
        self.temp_dir = os.path.join(os.path.dirname(__file__), "temp_cache")
        os.makedirs(self.temp_dir, exist_ok=True)
        
        # Create a recovery system
        self.recovery = ReasoningErrorRecovery(
            use_error_recovery=True,
            max_retries=3,
            retry_delay=0.1,
            cache_path=os.path.join(self.temp_dir, "error_recovery.json"),
            use_advanced_strategies=True,
            use_error_analytics=True,
            use_error_visualization=True,
            use_error_strategy_optimizer=True,
            use_error_monitoring=True,
            use_ml_error_classifier=True,
            use_distributed_error_recovery=True
        )

    def tearDown(self):
        """Tear down test fixtures."""
        # Clean up temporary directory
        for file in os.listdir(self.temp_dir):
            os.remove(os.path.join(self.temp_dir, file))
        os.rmdir(self.temp_dir)

    def test_retry_strategy(self):
        """Test the retry strategy."""
        # Create a mock function that fails twice then succeeds
        mock_func = MagicMock(side_effect=[
            ValueError("First failure"),
            ValueError("Second failure"),
            "Success"
        ])
        
        # Create a context with the mock function
        context = {
            "tool_name": "test_tool",
            "tool_func": mock_func,
            "tool_args": {"arg1": "value1"}
        }
        
        # Create a retry strategy
        retry_strategy = RetryStrategy(max_retries=3, initial_delay=0.1)
        
        # Test the strategy
        result = retry_strategy.recover(ValueError("Test error"), context)
        
        # Check that the function was called multiple times
        self.assertEqual(mock_func.call_count, 3)
        
        # Check that the strategy succeeded
        self.assertTrue(result.get("success", False))
        self.assertEqual(result.get("result"), "Success")

    def test_alternative_tool_strategy(self):
        """Test the alternative tool strategy."""
        # Create mock tools
        primary_tool = MagicMock(side_effect=ValueError("Primary tool failure"))
        alternative_tool = MagicMock(return_value="Alternative tool success")
        
        # Create a tool registry
        tool_registry = {
            "primary_tool": primary_tool,
            "alternative_tool": alternative_tool
        }
        
        # Create tool alternatives
        tool_alternatives = {
            "primary_tool": ["alternative_tool"]
        }
        
        # Create a context
        context = {
            "tool_name": "primary_tool",
            "tool_args": {"arg1": "value1"}
        }
        
        # Create an alternative tool strategy
        strategy = AlternativeToolStrategy(
            tool_alternatives=tool_alternatives,
            tool_registry=tool_registry
        )
        
        # Test the strategy
        result = strategy.recover(ValueError("Test error"), context)
        
        # Check that the alternative tool was called
        alternative_tool.assert_called_once_with(arg1="value1")
        
        # Check that the strategy succeeded
        self.assertTrue(result.get("success", False))
        self.assertEqual(result.get("result"), "Alternative tool success")

    def test_input_reformulation_strategy(self):
        """Test the input reformulation strategy."""
        # Create a mock function that fails with the original input but succeeds with reformulated input
        def mock_func(input_text):
            if "error" in input_text:
                raise ValueError("Input contains 'error'")
            return "Success"
        
        # Create a context
        context = {
            "tool_name": "test_tool",
            "tool_func": mock_func,
            "tool_args": {"input_text": "This will cause an error"}
        }
        
        # Create an input reformulation strategy
        strategy = InputReformulationStrategy()
        
        # Mock the reformulate method to remove "error"
        strategy.reformulate = MagicMock(return_value={
            "input_text": "This will succeed"
        })
        
        # Test the strategy
        result = strategy.recover(ValueError("Test error"), context)
        
        # Check that the strategy succeeded
        self.assertTrue(result.get("success", False))
        self.assertEqual(result.get("result"), "Success")

    def test_fallback_result_strategy(self):
        """Test the fallback result strategy."""
        # Create a context
        context = {
            "tool_name": "test_tool",
            "query": "What is the capital of France?"
        }
        
        # Create a fallback result strategy
        strategy = FallbackResultStrategy()
        
        # Test the strategy
        result = strategy.recover(ValueError("Test error"), context)
        
        # Check that the strategy succeeded
        self.assertTrue(result.get("success", False))
        self.assertIsNotNone(result.get("result"))


if __name__ == "__main__":
    unittest.main()
