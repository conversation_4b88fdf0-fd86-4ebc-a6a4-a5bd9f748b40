"""
Test script for new features in QueryAnalyzer.
"""

import os
import sys

# Add the src directory to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.agents.query_analyzer import QueryAnalyzer

def test_new_query_analyzer_features():
    """Test the new features in QueryAnalyzer."""
    print("\n=== Testing New QueryAnalyzer Features ===")
    
    analyzer = QueryAnalyzer()
    
    queries = [
        "Trí tuệ nhân tạo là gì",
        "<PERSON><PERSON> đi<PERSON>n thoại iPhone 15 giá rẻ",
        "Trang web chính thức của <PERSON> h<PERSON>c gia Hà Nội",
        "So sánh iPhone 15 và Samsung Galaxy S24",
        "Cách nấu phở bò ngon nhất"
    ]
    
    for query in queries:
        print(f"\nQuery: {query}")
        analysis = analyzer.analyze_query(query)
        print(f"Complexity: {analysis['complexity']}")
        print(f"Language: {analysis['language']}")
        print(f"Query type: {analysis['query_type']}")
        print(f"Search intent: {analysis.get('search_intent', 'N/A')}")
        print(f"Sentiment: {analysis.get('sentiment', 'N/A')}")
        print(f"Recommended search method: {analysis['recommended_search_method']}")

if __name__ == "__main__":
    test_new_query_analyzer_features()
