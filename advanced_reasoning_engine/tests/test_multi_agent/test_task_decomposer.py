"""
Tests for the TaskDecomposer class.
"""

import unittest
from unittest.mock import MagicMock, patch
import networkx as nx
import json
from src.deep_research_core.multi_agent.task_decomposer import TaskDecomposer


class TestTaskDecomposer(unittest.TestCase):
    """Test the TaskDecomposer class."""

    def setUp(self):
        """Set up test environment."""
        # Create a mock language model
        self.mock_language_model = MagicMock()

        # Create a mock CoT reasoner
        self.mock_cot_patcher = patch('src.deep_research_core.reasoning.cot.ChainOfThought')
        self.mock_cot_class = self.mock_cot_patcher.start()
        self.mock_cot = MagicMock()
        self.mock_cot_class.return_value = self.mock_cot

        # Set up the mock reasoner to return a dictionary with a reasoning field
        self.mock_cot.reason.return_value = {
            "reasoning": json.dumps({
                "subtasks": [
                    {
                        "id": "subtask_1",
                        "description": "Research the topic",
                        "suitable_roles": ["Researcher"],
                        "dependencies": []
                    },
                    {
                        "id": "subtask_2",
                        "description": "Analyze the data",
                        "suitable_roles": ["Analyst"],
                        "dependencies": ["subtask_1"]
                    },
                    {
                        "id": "subtask_3",
                        "description": "Write the report",
                        "suitable_roles": ["Writer"],
                        "dependencies": ["subtask_2"]
                    }
                ]
            })
        }

        # Initialize the TaskDecomposer with the mock language model
        with patch('src.deep_research_core.multi_agent.task_decomposer.TaskDecomposer._parse_subtasks') as mock_parse:
            # Set up the mock to return a list of subtasks
            mock_parse.return_value = [
                {
                    "id": "subtask_1",
                    "description": "Research the topic",
                    "suitable_roles": ["Researcher"],
                    "dependencies": []
                },
                {
                    "id": "subtask_2",
                    "description": "Analyze the data",
                    "suitable_roles": ["Analyst"],
                    "dependencies": ["subtask_1"]
                },
                {
                    "id": "subtask_3",
                    "description": "Write the report",
                    "suitable_roles": ["Writer"],
                    "dependencies": ["subtask_2"]
                }
            ]

            self.task_decomposer = TaskDecomposer(
                language_model=self.mock_language_model,
                decomposition_strategy="hierarchical",
                max_depth=2,
                use_cache=False
            )

            # Save the mock for later use
            self.mock_parse = mock_parse

    def tearDown(self):
        """Tear down test environment."""
        self.mock_cot_patcher.stop()

    def test_initialization(self):
        """Test initialization of TaskDecomposer."""
        self.assertEqual(self.task_decomposer.language_model, self.mock_language_model)
        self.assertEqual(self.task_decomposer.decomposition_strategy, "hierarchical")
        self.assertEqual(self.task_decomposer.max_depth, 2)
        self.assertFalse(self.task_decomposer.use_cache)

    def test_is_complex_task(self):
        """Test detection of complex tasks."""
        # Test long task
        long_task = "This is a very long task description that has more than twenty words and it should be detected as complex by the _is_complex_task method."
        self.assertTrue(self.task_decomposer._is_complex_task(long_task))

        # Test task with complex keywords
        complex_task = "Analyze the data and develop a comprehensive plan."
        self.assertTrue(self.task_decomposer._is_complex_task(complex_task))

        # Test task with conjunctions
        conjunction_task = "Create a report and present the findings."
        self.assertTrue(self.task_decomposer._is_complex_task(conjunction_task))

        # Test simple task
        simple_task = "Write a short email."
        self.assertFalse(self.task_decomposer._is_complex_task(simple_task))

    def test_hierarchical_decomposition(self):
        """Test hierarchical decomposition of a task."""
        # Reset the mocks
        self.mock_cot.reset_mock()
        self.mock_parse.reset_mock()

        # Set up mock response
        mock_response = {
            "reasoning": json.dumps({
                "subtasks": [
                    {
                        "id": "subtask_1",
                        "description": "Research the topic",
                        "suitable_roles": ["Researcher"],
                        "dependencies": []
                    },
                    {
                        "id": "subtask_2",
                        "description": "Analyze the data",
                        "suitable_roles": ["Analyst"],
                        "dependencies": ["subtask_1"]
                    },
                    {
                        "id": "subtask_3",
                        "description": "Write the report",
                        "suitable_roles": ["Writer"],
                        "dependencies": ["subtask_2"]
                    }
                ]
            })
        }
        self.mock_cot.reason.return_value = mock_response

        # Set up the mock to return a list of subtasks
        self.mock_parse.return_value = [
            {
                "id": "subtask_1",
                "description": "Research the topic",
                "suitable_roles": ["Researcher"],
                "dependencies": []
            },
            {
                "id": "subtask_2",
                "description": "Analyze the data",
                "suitable_roles": ["Analyst"],
                "dependencies": ["subtask_1"]
            },
            {
                "id": "subtask_3",
                "description": "Write the report",
                "suitable_roles": ["Writer"],
                "dependencies": ["subtask_2"]
            }
        ]

        # Call the decompose_task method
        task_description = "Create a comprehensive research report on AI trends."
        available_roles = ["Researcher", "Analyst", "Writer"]
        result = self.task_decomposer.decompose_task(task_description, available_roles)

        # Check that the CoT reasoner was called
        self.mock_cot.reason.assert_called()

        # Check the result
        self.assertEqual(result["main_task"], task_description)
        self.assertEqual(len(result["subtasks"]), 3)
        self.assertEqual(result["subtasks"][0]["id"], "subtask_1")
        self.assertEqual(result["subtasks"][0]["description"], "Research the topic")
        self.assertEqual(result["subtasks"][0]["suitable_roles"], ["Researcher"])
        self.assertEqual(result["subtasks"][0]["dependencies"], [])

        # Check dependencies
        self.assertEqual(result["dependencies"]["subtask_1"], [])
        self.assertEqual(result["dependencies"]["subtask_2"], ["subtask_1"])
        self.assertEqual(result["dependencies"]["subtask_3"], ["subtask_2"])

    def test_graph_based_decomposition(self):
        """Test graph-based decomposition of a task."""
        # Reset the mocks
        self.mock_cot.reset_mock()
        self.mock_parse.reset_mock()

        # Set up the task decomposer with graph strategy
        self.task_decomposer.decomposition_strategy = "graph"

        # Set up mock response for graph-based decomposition
        graph_response = {
            "reasoning": json.dumps({
                "subtasks": [
                    {
                        "id": "subtask_1",
                        "description": "Gather requirements",
                        "suitable_roles": ["Analyst"],
                        "dependencies": []
                    },
                    {
                        "id": "subtask_2",
                        "description": "Design architecture",
                        "suitable_roles": ["Architect"],
                        "dependencies": ["subtask_1"]
                    },
                    {
                        "id": "subtask_3",
                        "description": "Implement frontend",
                        "suitable_roles": ["Frontend Developer"],
                        "dependencies": ["subtask_2"]
                    },
                    {
                        "id": "subtask_4",
                        "description": "Implement backend",
                        "suitable_roles": ["Backend Developer"],
                        "dependencies": ["subtask_2"]
                    },
                    {
                        "id": "subtask_5",
                        "description": "Test the system",
                        "suitable_roles": ["Tester"],
                        "dependencies": ["subtask_3", "subtask_4"]
                    }
                ]
            })
        }
        self.mock_cot.reason.return_value = graph_response

        # Set up the mock to return a list of subtasks
        self.mock_parse.return_value = [
            {
                "id": "subtask_1",
                "description": "Gather requirements",
                "suitable_roles": ["Analyst"],
                "dependencies": []
            },
            {
                "id": "subtask_2",
                "description": "Design architecture",
                "suitable_roles": ["Architect"],
                "dependencies": ["subtask_1"]
            },
            {
                "id": "subtask_3",
                "description": "Implement frontend",
                "suitable_roles": ["Frontend Developer"],
                "dependencies": ["subtask_2"]
            },
            {
                "id": "subtask_4",
                "description": "Implement backend",
                "suitable_roles": ["Backend Developer"],
                "dependencies": ["subtask_2"]
            },
            {
                "id": "subtask_5",
                "description": "Test the system",
                "suitable_roles": ["Tester"],
                "dependencies": ["subtask_3", "subtask_4"]
            }
        ]

        # Call the decompose_task method
        task_description = "Build a web application for customer management."
        available_roles = ["Analyst", "Architect", "Frontend Developer", "Backend Developer", "Tester"]
        result = self.task_decomposer.decompose_task(task_description, available_roles)

        # Check that the CoT reasoner was called
        self.mock_cot.reason.assert_called()

        # Check the result
        self.assertEqual(result["main_task"], task_description)
        self.assertEqual(len(result["subtasks"]), 5)

        # Check dependencies
        self.assertEqual(result["dependencies"]["subtask_1"], [])
        self.assertEqual(result["dependencies"]["subtask_2"], ["subtask_1"])
        self.assertEqual(result["dependencies"]["subtask_3"], ["subtask_2"])
        self.assertEqual(result["dependencies"]["subtask_4"], ["subtask_2"])
        self.assertEqual(sorted(result["dependencies"]["subtask_5"]), sorted(["subtask_3", "subtask_4"]))

    def test_sequential_decomposition(self):
        """Test sequential decomposition of a task."""
        # Reset the mocks
        self.mock_cot.reset_mock()
        self.mock_parse.reset_mock()

        # Set up the task decomposer with sequential strategy
        self.task_decomposer.decomposition_strategy = "sequential"

        # Set up mock response for sequential decomposition
        sequential_response = {
            "reasoning": json.dumps({
                "subtasks": [
                    {
                        "id": "subtask_1",
                        "description": "Define project scope",
                        "suitable_roles": ["Project Manager"],
                        "dependencies": []
                    },
                    {
                        "id": "subtask_2",
                        "description": "Create project plan",
                        "suitable_roles": ["Project Manager"],
                        "dependencies": ["subtask_1"]
                    },
                    {
                        "id": "subtask_3",
                        "description": "Execute project tasks",
                        "suitable_roles": ["Team Member"],
                        "dependencies": ["subtask_2"]
                    },
                    {
                        "id": "subtask_4",
                        "description": "Monitor progress",
                        "suitable_roles": ["Project Manager"],
                        "dependencies": ["subtask_3"]
                    },
                    {
                        "id": "subtask_5",
                        "description": "Close the project",
                        "suitable_roles": ["Project Manager"],
                        "dependencies": ["subtask_4"]
                    }
                ]
            })
        }
        self.mock_cot.reason.return_value = sequential_response

        # Set up the mock to return a list of subtasks
        self.mock_parse.return_value = [
            {
                "id": "subtask_1",
                "description": "Define project scope",
                "suitable_roles": ["Project Manager"],
                "dependencies": []
            },
            {
                "id": "subtask_2",
                "description": "Create project plan",
                "suitable_roles": ["Project Manager"],
                "dependencies": ["subtask_1"]
            },
            {
                "id": "subtask_3",
                "description": "Execute project tasks",
                "suitable_roles": ["Team Member"],
                "dependencies": ["subtask_2"]
            },
            {
                "id": "subtask_4",
                "description": "Monitor progress",
                "suitable_roles": ["Project Manager"],
                "dependencies": ["subtask_3"]
            },
            {
                "id": "subtask_5",
                "description": "Close the project",
                "suitable_roles": ["Project Manager"],
                "dependencies": ["subtask_4"]
            }
        ]

        # Call the decompose_task method
        task_description = "Manage a software development project."
        available_roles = ["Project Manager", "Team Member"]
        result = self.task_decomposer.decompose_task(task_description, available_roles)

        # Check that the CoT reasoner was called
        self.mock_cot.reason.assert_called()

        # Check the result
        self.assertEqual(result["main_task"], task_description)
        self.assertEqual(len(result["subtasks"]), 5)

        # Check dependencies form a chain
        self.assertEqual(result["dependencies"]["subtask_1"], [])
        self.assertEqual(result["dependencies"]["subtask_2"], ["subtask_1"])
        self.assertEqual(result["dependencies"]["subtask_3"], ["subtask_2"])
        self.assertEqual(result["dependencies"]["subtask_4"], ["subtask_3"])
        self.assertEqual(result["dependencies"]["subtask_5"], ["subtask_4"])

    def test_parallel_decomposition(self):
        """Test parallel decomposition of a task."""
        # Reset the mocks
        self.mock_cot.reset_mock()
        self.mock_parse.reset_mock()

        # Set up the task decomposer with parallel strategy
        self.task_decomposer.decomposition_strategy = "parallel"

        # Set up mock response for parallel decomposition
        parallel_response = {
            "reasoning": json.dumps({
                "subtasks": [
                    {
                        "id": "subtask_1",
                        "description": "Research market trends",
                        "suitable_roles": ["Market Researcher"],
                        "dependencies": []
                    },
                    {
                        "id": "subtask_2",
                        "description": "Analyze competitor products",
                        "suitable_roles": ["Competitive Analyst"],
                        "dependencies": []
                    },
                    {
                        "id": "subtask_3",
                        "description": "Conduct customer interviews",
                        "suitable_roles": ["User Researcher"],
                        "dependencies": []
                    },
                    {
                        "id": "subtask_4",
                        "description": "Review technical feasibility",
                        "suitable_roles": ["Technical Architect"],
                        "dependencies": []
                    }
                ]
            })
        }
        self.mock_cot.reason.return_value = parallel_response

        # Set up the mock to return a list of subtasks
        self.mock_parse.return_value = [
            {
                "id": "subtask_1",
                "description": "Research market trends",
                "suitable_roles": ["Market Researcher"],
                "dependencies": []
            },
            {
                "id": "subtask_2",
                "description": "Analyze competitor products",
                "suitable_roles": ["Competitive Analyst"],
                "dependencies": []
            },
            {
                "id": "subtask_3",
                "description": "Conduct customer interviews",
                "suitable_roles": ["User Researcher"],
                "dependencies": []
            },
            {
                "id": "subtask_4",
                "description": "Review technical feasibility",
                "suitable_roles": ["Technical Architect"],
                "dependencies": []
            }
        ]

        # Call the decompose_task method
        task_description = "Conduct product research for a new mobile app."
        available_roles = ["Market Researcher", "Competitive Analyst", "User Researcher", "Technical Architect"]
        result = self.task_decomposer.decompose_task(task_description, available_roles)

        # Check that the CoT reasoner was called
        self.mock_cot.reason.assert_called()

        # Check the result
        self.assertEqual(result["main_task"], task_description)
        self.assertEqual(len(result["subtasks"]), 4)

        # Check all subtasks have no dependencies (parallel)
        for subtask_id in result["dependencies"]:
            self.assertEqual(result["dependencies"][subtask_id], [])

    def test_get_execution_order(self):
        """Test getting the execution order of subtasks."""
        # Create a task graph manually
        self.task_decomposer.task_graph = nx.DiGraph()

        # Add nodes
        self.task_decomposer.task_graph.add_node("main", description="Main task", type="main_task", depth=0)
        self.task_decomposer.task_graph.add_node("subtask_1", description="Subtask 1", depth=1)
        self.task_decomposer.task_graph.add_node("subtask_2", description="Subtask 2", depth=1)
        self.task_decomposer.task_graph.add_node("subtask_3", description="Subtask 3", depth=1)
        self.task_decomposer.task_graph.add_node("subtask_4", description="Subtask 4", depth=1)

        # Add edges (dependencies)
        self.task_decomposer.task_graph.add_edge("main", "subtask_1")
        self.task_decomposer.task_graph.add_edge("main", "subtask_2")
        self.task_decomposer.task_graph.add_edge("subtask_1", "subtask_3")
        self.task_decomposer.task_graph.add_edge("subtask_2", "subtask_3")
        self.task_decomposer.task_graph.add_edge("subtask_3", "subtask_4")

        # Get execution order
        execution_order = self.task_decomposer.get_execution_order()

        # Check the order
        self.assertEqual(len(execution_order), 4)  # All subtasks, not including main

        # Check specific ordering constraints
        self.assertIn("subtask_1", execution_order)
        self.assertIn("subtask_2", execution_order)
        self.assertIn("subtask_3", execution_order)
        self.assertIn("subtask_4", execution_order)

        # Check that dependencies are respected
        subtask_1_index = execution_order.index("subtask_1")
        subtask_2_index = execution_order.index("subtask_2")
        subtask_3_index = execution_order.index("subtask_3")
        subtask_4_index = execution_order.index("subtask_4")

        self.assertLess(subtask_1_index, subtask_3_index)  # subtask_1 before subtask_3
        self.assertLess(subtask_2_index, subtask_3_index)  # subtask_2 before subtask_3
        self.assertLess(subtask_3_index, subtask_4_index)  # subtask_3 before subtask_4

    def test_parse_subtasks_with_invalid_json(self):
        """Test parsing subtasks when the model returns invalid JSON."""
        # Set up mock response with invalid JSON
        invalid_json_response = {
            "reasoning": """
            Here are the subtasks:

            Subtask 1: Research the topic
            Subtask 2: Analyze the data
            Subtask 3: Write the report
            """
        }
        self.mock_cot.reason.return_value = invalid_json_response

        # Reset the mock_parse to track new calls
        self.mock_parse.reset_mock()

        # Set up the mock to return a list of subtasks for this specific test
        self.mock_parse.return_value = [
            {
                "id": "subtask_1",
                "description": "Research the topic",
                "suitable_roles": [],
                "dependencies": []
            },
            {
                "id": "subtask_2",
                "description": "Analyze the data",
                "suitable_roles": [],
                "dependencies": []
            },
            {
                "id": "subtask_3",
                "description": "Write the report",
                "suitable_roles": [],
                "dependencies": []
            }
        ]

        # Call the decompose_task method
        task_description = "Create a research report."
        result = self.task_decomposer.decompose_task(task_description)

        # Check that the parsing method was called
        self.mock_parse.assert_called()

        # Check that the result contains the expected subtasks
        self.assertEqual(len(result["subtasks"]), 3)
        self.assertEqual(result["subtasks"][0]["description"], "Research the topic")
        self.assertEqual(result["subtasks"][1]["description"], "Analyze the data")
        self.assertEqual(result["subtasks"][2]["description"], "Write the report")

    def test_create_default_subtask(self):
        """Test creating a default subtask."""
        task_description = "Complete this task."
        default_subtask = self.task_decomposer._create_default_subtask(task_description)

        self.assertEqual(default_subtask["id"], "subtask_1")
        self.assertEqual(default_subtask["description"], f"Complete the task: {task_description}")
        self.assertEqual(default_subtask["suitable_roles"], [])
        self.assertEqual(default_subtask["dependencies"], [])

    @patch('matplotlib.pyplot.savefig')
    @patch('matplotlib.pyplot.figure')
    @patch('networkx.spring_layout')
    def test_visualize_task_graph(self, mock_spring_layout, mock_figure, mock_savefig):
        """Test visualizing the task graph."""
        # Set up a simple task graph
        self.task_decomposer.task_graph = nx.DiGraph()
        self.task_decomposer.task_graph.add_node("main", description="Main task", type="main_task", depth=0)
        self.task_decomposer.task_graph.add_node("subtask_1", description="Subtask 1", depth=1)
        self.task_decomposer.task_graph.add_node("subtask_2", description="Subtask 2", depth=1)
        self.task_decomposer.task_graph.add_edge("main", "subtask_1")
        self.task_decomposer.task_graph.add_edge("main", "subtask_2")

        # Mock the spring layout
        mock_spring_layout.return_value = {
            "main": (0, 0),
            "subtask_1": (1, 0),
            "subtask_2": (0, 1)
        }

        # Call visualize_task_graph
        output_file = "test_graph.png"
        result = self.task_decomposer.visualize_task_graph(output_file)

        # Check that the visualization functions were called
        mock_figure.assert_called_once()
        mock_spring_layout.assert_called_once()
        mock_savefig.assert_called_once_with(output_file)

        # Check the result
        self.assertEqual(result, output_file)


if __name__ == '__main__':
    unittest.main()
