import unittest
from unittest.mock import MagicMock, patch
import json
from src.deep_research_core.multi_agent.task_decomposer import TaskDecomposer

class TestTaskDecomposerSimple(unittest.TestCase):
    """Test the TaskDecomposer class with simple unit tests."""

    def setUp(self):
        """Set up test environment."""
        # Create a mock language model
        self.mock_language_model = MagicMock()

        # Initialize the TaskDecomposer with the mock language model
        self.task_decomposer = TaskDecomposer(
            language_model=self.mock_language_model,
            decomposition_strategy="hierarchical",
            max_depth=2,
            use_cache=False
        )

        # Mock the _generate_subtasks method to avoid calling the language model
        self.generate_subtasks_patcher = patch.object(
            self.task_decomposer,
            '_generate_subtasks'
        )
        self.mock_generate_subtasks = self.generate_subtasks_patcher.start()

        # Set up default subtasks for testing
        self.default_subtasks = [
            {
                "id": "subtask_1",
                "description": "Research the topic",
                "suitable_roles": ["Researcher"],
                "dependencies": []
            },
            {
                "id": "subtask_2",
                "description": "Analyze the data",
                "suitable_roles": ["Analyst"],
                "dependencies": ["subtask_1"]
            },
            {
                "id": "subtask_3",
                "description": "Write the report",
                "suitable_roles": ["Writer"],
                "dependencies": ["subtask_2"]
            }
        ]

        # Set the mock to return the default subtasks
        self.mock_generate_subtasks.return_value = self.default_subtasks

    def tearDown(self):
        """Tear down test environment."""
        self.generate_subtasks_patcher.stop()

    def test_decompose_task_hierarchical(self):
        """Test hierarchical decomposition of a task."""
        # Patch the _is_complex_task method to return False to avoid recursive decomposition
        with patch.object(self.task_decomposer, '_is_complex_task', return_value=False):
            # Call the decompose_task method
            task_description = "Create a comprehensive research report on AI trends."
            available_roles = ["Researcher", "Analyst", "Writer"]
            result = self.task_decomposer.decompose_task(task_description, available_roles)

            # Check that the _generate_subtasks method was called
            self.mock_generate_subtasks.assert_called_once()

            # Check the result
            self.assertEqual(result["main_task"], task_description)
            self.assertEqual(len(result["subtasks"]), 3)

            # Check that subtasks have the correct structure
            self.assertEqual(result["subtasks"][0]["description"], "Research the topic")
            self.assertEqual(result["subtasks"][1]["description"], "Analyze the data")
            self.assertEqual(result["subtasks"][2]["description"], "Write the report")

            # Check dependencies
            self.assertEqual(result["dependencies"]["subtask_1"], [])
            self.assertEqual(result["dependencies"]["subtask_2"], ["subtask_1"])
            self.assertEqual(result["dependencies"]["subtask_3"], ["subtask_2"])

    def test_decompose_task_graph(self):
        """Test graph-based decomposition of a task."""
        # Set up the task decomposer with graph strategy
        self.task_decomposer.decomposition_strategy = "graph"

        # Set up mock response for graph-based decomposition
        graph_subtasks = [
            {
                "id": "subtask_1",
                "description": "Gather requirements",
                "suitable_roles": ["Analyst"],
                "dependencies": []
            },
            {
                "id": "subtask_2",
                "description": "Design architecture",
                "suitable_roles": ["Architect"],
                "dependencies": ["subtask_1"]
            },
            {
                "id": "subtask_3",
                "description": "Implement frontend",
                "suitable_roles": ["Frontend Developer"],
                "dependencies": ["subtask_2"]
            },
            {
                "id": "subtask_4",
                "description": "Implement backend",
                "suitable_roles": ["Backend Developer"],
                "dependencies": ["subtask_2"]
            },
            {
                "id": "subtask_5",
                "description": "Test the system",
                "suitable_roles": ["Tester"],
                "dependencies": ["subtask_3", "subtask_4"]
            }
        ]
        self.mock_generate_subtasks.return_value = graph_subtasks

        # Patch the _is_complex_task method to return False to avoid recursive decomposition
        with patch.object(self.task_decomposer, '_is_complex_task', return_value=False):
            # Call the decompose_task method
            task_description = "Build a web application for customer management."
            available_roles = ["Analyst", "Architect", "Frontend Developer",
                              "Backend Developer", "Tester"]
            result = self.task_decomposer.decompose_task(task_description, available_roles)

            # Check that the _generate_subtasks method was called
            self.mock_generate_subtasks.assert_called_once()

            # Check the result
            self.assertEqual(result["main_task"], task_description)
            self.assertEqual(len(result["subtasks"]), 5)

            # Check dependencies
            self.assertEqual(result["dependencies"]["subtask_1"], [])
            self.assertEqual(result["dependencies"]["subtask_2"], ["subtask_1"])
            self.assertEqual(result["dependencies"]["subtask_3"], ["subtask_2"])
            self.assertEqual(result["dependencies"]["subtask_4"], ["subtask_2"])
            deps = ["subtask_3", "subtask_4"]
            self.assertEqual(sorted(result["dependencies"]["subtask_5"]), sorted(deps))

    def test_decompose_task_sequential(self):
        """Test sequential decomposition of a task."""
        # Set up the task decomposer with sequential strategy
        self.task_decomposer.decomposition_strategy = "sequential"

        # Set up mock response for sequential decomposition
        sequential_subtasks = [
            {
                "id": "subtask_1",
                "description": "Define project scope",
                "suitable_roles": ["Project Manager"],
                "dependencies": []
            },
            {
                "id": "subtask_2",
                "description": "Create project plan",
                "suitable_roles": ["Project Manager"],
                "dependencies": ["subtask_1"]
            },
            {
                "id": "subtask_3",
                "description": "Execute project tasks",
                "suitable_roles": ["Team Member"],
                "dependencies": ["subtask_2"]
            },
            {
                "id": "subtask_4",
                "description": "Monitor progress",
                "suitable_roles": ["Project Manager"],
                "dependencies": ["subtask_3"]
            },
            {
                "id": "subtask_5",
                "description": "Close the project",
                "suitable_roles": ["Project Manager"],
                "dependencies": ["subtask_4"]
            }
        ]
        self.mock_generate_subtasks.return_value = sequential_subtasks

        # Patch the _is_complex_task method to return False to avoid recursive decomposition
        with patch.object(self.task_decomposer, '_is_complex_task', return_value=False):
            # Call the decompose_task method
            task_description = "Manage a software development project."
            available_roles = ["Project Manager", "Team Member"]
            result = self.task_decomposer.decompose_task(task_description, available_roles)

            # Check that the _generate_subtasks method was called
            self.mock_generate_subtasks.assert_called_once()

            # Check the result
            self.assertEqual(result["main_task"], task_description)
            self.assertEqual(len(result["subtasks"]), 5)

            # Check dependencies form a chain
            self.assertEqual(result["dependencies"]["subtask_1"], [])
            self.assertEqual(result["dependencies"]["subtask_2"], ["subtask_1"])
            self.assertEqual(result["dependencies"]["subtask_3"], ["subtask_2"])
            self.assertEqual(result["dependencies"]["subtask_4"], ["subtask_3"])
            self.assertEqual(result["dependencies"]["subtask_5"], ["subtask_4"])

    def test_decompose_task_parallel(self):
        """Test parallel decomposition of a task."""
        # Set up the task decomposer with parallel strategy
        self.task_decomposer.decomposition_strategy = "parallel"

        # Set up mock response for parallel decomposition
        parallel_subtasks = [
            {
                "id": "subtask_1",
                "description": "Research market trends",
                "suitable_roles": ["Market Researcher"],
                "dependencies": []
            },
            {
                "id": "subtask_2",
                "description": "Analyze competitor products",
                "suitable_roles": ["Competitive Analyst"],
                "dependencies": []
            },
            {
                "id": "subtask_3",
                "description": "Conduct customer interviews",
                "suitable_roles": ["User Researcher"],
                "dependencies": []
            },
            {
                "id": "subtask_4",
                "description": "Review technical feasibility",
                "suitable_roles": ["Technical Architect"],
                "dependencies": []
            }
        ]
        self.mock_generate_subtasks.return_value = parallel_subtasks

        # Patch the _is_complex_task method to return False to avoid recursive decomposition
        with patch.object(self.task_decomposer, '_is_complex_task', return_value=False):
            # Call the decompose_task method
            task_description = "Conduct product research for a new mobile app."
            available_roles = [
                "Market Researcher",
                "Competitive Analyst",
                "User Researcher",
                "Technical Architect"
            ]
            result = self.task_decomposer.decompose_task(task_description, available_roles)

            # Check that the _generate_subtasks method was called
            self.mock_generate_subtasks.assert_called_once()

            # Check the result
            self.assertEqual(result["main_task"], task_description)
            self.assertEqual(len(result["subtasks"]), 4)

            # Check all subtasks have no dependencies (parallel)
            for subtask_id in result["dependencies"]:
                self.assertEqual(result["dependencies"][subtask_id], [])

    def test_create_default_subtask(self):
        """Test creating a default subtask."""
        # We're testing a protected method, but it's okay in a test
        task_description = "Complete this task."
        # pylint: disable=protected-access
        default_subtask = self.task_decomposer._create_default_subtask(task_description)

        self.assertEqual(default_subtask["id"], "subtask_1")
        self.assertEqual(default_subtask["description"], f"Complete the task: {task_description}")
        self.assertEqual(default_subtask["suitable_roles"], [])
        self.assertEqual(default_subtask["dependencies"], [])

    def test_is_complex_task(self):
        """Test the _is_complex_task method."""
        # We're testing a protected method, but it's okay in a test
        # pylint: disable=protected-access

        # Test task with more than 20 words
        long_task = (
            "This is a very long task description that has more than twenty words "
            "and it should be detected as complex by the _is_complex_task method."
        )
        self.assertTrue(self.task_decomposer._is_complex_task(long_task))

        # Test task with complex keywords
        complex_task = "Analyze the data and develop a comprehensive plan."
        self.assertTrue(self.task_decomposer._is_complex_task(complex_task))

        # Test task with conjunctions
        conjunction_task = "Create a report and present the findings."
        self.assertTrue(self.task_decomposer._is_complex_task(conjunction_task))

        # Test simple task
        simple_task = "Write a short email."
        self.assertFalse(self.task_decomposer._is_complex_task(simple_task))

if __name__ == '__main__':
    unittest.main()
