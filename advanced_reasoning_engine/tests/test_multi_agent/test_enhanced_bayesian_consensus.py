"""
Unit tests for Enhanced Bayesian consensus mechanism.

This module tests the Enhanced Bayesian consensus mechanism for the multi-agent system,
which provides improved data handling, agent weighting, conflict resolution, and parameter self-adjustment.
"""

import unittest
from unittest.mock import MagicMock, patch
from typing import Dict, Any, List

from deep_research_core.multi_agent.enhanced_bayesian_consensus import EnhancedBayesianConsensus


class TestEnhancedBayesianConsensus(unittest.TestCase):
    """Test cases for the Enhanced Bayesian consensus mechanism."""

    def setUp(self):
        """Set up test fixtures."""
        self.enhanced_consensus = EnhancedBayesianConsensus(
            prior_strength=1.0,
            expertise_weight=2.0,
            confidence_scale=1.0,
            min_evidence_weight=0.5,
            conflict_resolution_threshold=0.3,
            performance_history_weight=1.5,
            self_adjustment_rate=0.1,
            complex_data_similarity_threshold=0.7
        )

        # Create mock agents with different expertise levels
        self.agent1 = MagicMock()
        self.agent1.id = "agent1"
        self.agent1.role = MagicMock()
        self.agent1.role.knowledge_areas = ["research_methods"]

        self.agent2 = MagicMock()
        self.agent2.id = "agent2"
        self.agent2.role = MagicMock()
        self.agent2.role.knowledge_areas = ["research_methods", "information_evaluation"]

        self.agent3 = MagicMock()
        self.agent3.id = "agent3"
        self.agent3.role = MagicMock()
        self.agent3.role.knowledge_areas = ["research_methods", "information_evaluation", "critical_thinking"]
        self.agent3.performance_metrics = MagicMock()
        self.agent3.performance_metrics.accuracy = 0.9
        self.agent3.performance_metrics.consistency = 0.8

        self.agents = [self.agent1, self.agent2, self.agent3]

        # Create simple proposals
        self.simple_proposals = [
            {"agent_id": "agent1", "answer": "42", "confidence": 0.7},
            {"agent_id": "agent2", "answer": "42", "confidence": 0.8},
            {"agent_id": "agent3", "answer": "43", "confidence": 0.9}
        ]

        # Create complex proposals
        self.complex_proposals = [
            {
                "agent_id": "agent1",
                "solution": {"result": 42, "method": "calculation", "steps": ["step1", "step2"]},
                "confidence": 0.7
            },
            {
                "agent_id": "agent2",
                "solution": {"result": 42, "method": "lookup", "source": "database"},
                "confidence": 0.8
            },
            {
                "agent_id": "agent3",
                "solution": {"result": 43, "method": "estimation", "confidence_interval": [40, 45]},
                "confidence": 0.9
            }
        ]

        # Create context
        self.context = {
            "task_type": "research",
            "domains": ["research_methods", "information_evaluation"],
            "prior_beliefs": {
                "42": 0.6,
                "43": 0.4
            }
        }

    def test_extract_unique_proposals_simple(self):
        """Test extraction of unique simple proposals."""
        unique_proposals = self.enhanced_consensus._extract_unique_proposals(self.simple_proposals)

        self.assertEqual(len(unique_proposals), 2)
        self.assertIn("42", unique_proposals)
        self.assertIn("43", unique_proposals)
        self.assertEqual(len(unique_proposals["42"]), 2)
        self.assertEqual(len(unique_proposals["43"]), 1)

    def test_extract_unique_proposals_complex(self):
        """Test extraction of unique complex proposals."""
        unique_proposals = self.enhanced_consensus._extract_unique_proposals(self.complex_proposals)

        self.assertEqual(len(unique_proposals), 3)  # All are unique due to different structures

    def test_calculate_complex_data_similarity(self):
        """Test calculation of similarity between complex data structures."""
        # Similar dictionaries
        dict1 = {"result": 42, "method": "calculation", "steps": ["step1", "step2"]}
        dict2 = {"result": 42, "method": "calculation", "steps": ["step1", "step3"]}

        similarity = self.enhanced_consensus._calculate_complex_data_similarity(dict1, dict2)
        self.assertGreater(similarity, 0.7)  # Should be considered similar

        # Different dictionaries
        dict3 = {"result": 43, "method": "estimation", "confidence_interval": [40, 45]}

        similarity = self.enhanced_consensus._calculate_complex_data_similarity(dict1, dict3)
        self.assertLess(similarity, 0.7)  # Should not be considered similar

        # Similar lists
        list1 = [1, 2, 3, 4, 5]
        list2 = [1, 2, 3, 4, 6]

        similarity = self.enhanced_consensus._calculate_complex_data_similarity(list1, list2)
        self.assertGreater(similarity, 0.7)  # Should be considered similar

        # Different lists
        list3 = [10, 20, 30, 40, 50]

        similarity = self.enhanced_consensus._calculate_complex_data_similarity(list1, list3)
        self.assertLess(similarity, 0.7)  # Should not be considered similar

    def test_group_similar_complex_proposals(self):
        """Test grouping of similar complex proposals."""
        # Create test proposals with very similar complex data
        test_proposals = [
            {
                "agent_id": "agent1",
                "solution": {"result": 42, "method": "calculation", "steps": ["step1", "step2"]},
                "confidence": 0.7
            },
            {
                "agent_id": "agent2",
                "solution": {"result": 42, "method": "calculation", "steps": ["step1", "step2", "step3"]},
                "confidence": 0.8
            }
        ]

        # Extract unique proposals
        unique_proposals = {}
        for i, proposal in enumerate(test_proposals):
            value = proposal.get('solution')
            value_key = self.enhanced_consensus._get_hashable_representation(value)
            if value_key not in unique_proposals:
                unique_proposals[value_key] = []
            unique_proposals[value_key].append(i)

        # Verify we have 2 unique proposals initially
        self.assertEqual(len(unique_proposals), 2, "Should have 2 unique proposals initially")

        # Set a very high similarity threshold to ensure grouping
        original_threshold = self.enhanced_consensus.complex_data_similarity_threshold
        self.enhanced_consensus.complex_data_similarity_threshold = 0.5

        try:
            # Calculate similarity directly to verify it's high enough
            value1 = test_proposals[0]['solution']
            value2 = test_proposals[1]['solution']
            similarity = self.enhanced_consensus._calculate_complex_data_similarity(value1, value2)

            # Print the similarity for debugging
            print(f"Similarity between proposals: {similarity}")

            # Verify the similarity is above our threshold
            self.assertGreaterEqual(similarity, self.enhanced_consensus.complex_data_similarity_threshold,
                                   "Proposals should be similar enough to be grouped")

            # Group similar proposals
            grouped_proposals = self.enhanced_consensus._group_similar_complex_proposals(
                unique_proposals, test_proposals
            )

            # Check that similar proposals were grouped
            self.assertEqual(len(grouped_proposals), 1,
                            "Similar proposals should be grouped together")
        finally:
            # Restore original threshold
            self.enhanced_consensus.complex_data_similarity_threshold = original_threshold

    def test_calculate_agent_weights_with_performance_history(self):
        """Test calculation of agent weights with performance history."""
        # Create real agents instead of MagicMock for this test
        class TestAgent:
            def __init__(self, id, knowledge_areas, accuracy=None, consistency=None, recent_performance=None):
                self.id = id
                self.role = type('Role', (), {'knowledge_areas': knowledge_areas})
                if accuracy or consistency:
                    self.performance_metrics = type('Metrics', (), {})
                    if accuracy:
                        self.performance_metrics.accuracy = accuracy
                    if consistency:
                        self.performance_metrics.consistency = consistency
                    # Add domain accuracy if needed
                    if hasattr(self, 'domain_accuracy_data'):
                        self.performance_metrics.domain_accuracy = type('DomainAccuracy', (), self.domain_accuracy_data)
                if recent_performance:
                    self.recent_performance = recent_performance

        # Create test agents
        agent1 = TestAgent("agent1", ["research_methods"])
        agent2 = TestAgent("agent2", ["research_methods", "information_evaluation"], accuracy=0.7)
        agent3 = TestAgent("agent3", ["research_methods", "information_evaluation", "critical_thinking"],
                          accuracy=0.9, consistency=0.8)
        agent3.recent_performance = [0.9, 0.85, 0.95, 0.9, 0.8]
        agent3.domain_accuracy_data = {
            'research_methods': 0.9,
            'information_evaluation': 0.85
        }

        test_agents = [agent1, agent2, agent3]

        agent_weights = self.enhanced_consensus._calculate_agent_weights(test_agents, self.context)

        self.assertEqual(len(agent_weights), 3)

        # Agent3 should have the highest weight due to more knowledge areas, high accuracy, and good performance history
        weights = [agent_weights[a] for a in test_agents]
        self.assertGreater(weights[2], weights[1])
        self.assertGreater(weights[1], weights[0])

    def test_detect_and_resolve_conflicts(self):
        """Test detection and resolution of conflicts between proposals."""
        # Create a situation with a conflict (close probabilities)
        posterior_probs = {
            "42": 0.51,  # Very close
            "43": 0.49
        }

        # Create real agents instead of MagicMock for this test
        class TestAgent:
            def __init__(self, id, knowledge_areas, accuracy=None, consistency=None):
                self.id = id
                self.role = type('Role', (), {'knowledge_areas': knowledge_areas})
                if accuracy or consistency:
                    self.performance_metrics = type('Metrics', (), {})
                    if accuracy:
                        self.performance_metrics.accuracy = accuracy
                    if consistency:
                        self.performance_metrics.consistency = consistency

        # Create test agents
        agent1 = TestAgent("agent1", ["research_methods"])
        agent2 = TestAgent("agent2", ["research_methods", "information_evaluation"])
        agent3 = TestAgent("agent3", ["research_methods", "information_evaluation",
                                     "critical_thinking", "estimation"],
                          accuracy=0.9, consistency=0.8)

        test_agents = [agent1, agent2, agent3]

        # Resolve conflicts
        adjusted_probs, conflict_metadata = self.enhanced_consensus._detect_and_resolve_conflicts(
            self.simple_proposals,
            test_agents,
            posterior_probs
        )

        # Verify conflict was detected
        self.assertTrue(conflict_metadata["conflicts_detected"])
        self.assertIn("resolution_method", conflict_metadata)
        self.assertIn("adjustments_made", conflict_metadata)

        # Verify probabilities were adjusted
        self.assertNotEqual(adjusted_probs["42"] / adjusted_probs["43"], posterior_probs["42"] / posterior_probs["43"])

    def test_combine_beliefs_simple(self):
        """Test the complete enhanced Bayesian belief combination process with simple proposals."""
        # Create real agents instead of MagicMock for this test
        class TestAgent:
            def __init__(self, id, knowledge_areas, accuracy=None):
                self.id = id
                self.role = type('Role', (), {'knowledge_areas': knowledge_areas})
                if accuracy:
                    self.performance_metrics = type('Metrics', (), {'accuracy': accuracy})

        # Create test agents
        agent1 = TestAgent("agent1", ["research_methods"])
        agent2 = TestAgent("agent2", ["research_methods", "information_evaluation"], accuracy=0.7)
        agent3 = TestAgent("agent3", ["research_methods", "information_evaluation", "critical_thinking"], accuracy=0.9)

        test_agents = [agent1, agent2, agent3]

        result = self.enhanced_consensus.combine_beliefs(self.simple_proposals, test_agents, self.context)

        self.assertIsNotNone(result)
        self.assertIn("answer", result)
        self.assertIn("consensus_metadata", result)
        self.assertEqual(result["consensus_metadata"]["method"], "enhanced_bayesian_consensus")

        # Check that posterior probabilities and agent weights are included
        self.assertIn("posterior_probabilities", result["consensus_metadata"])
        self.assertIn("agent_weights", result["consensus_metadata"])
        self.assertIn("parameters", result["consensus_metadata"])

    def test_combine_beliefs_complex(self):
        """Test the enhanced Bayesian belief combination with complex proposals."""
        # Create real agents instead of MagicMock for this test
        class TestAgent:
            def __init__(self, id, knowledge_areas, accuracy=None):
                self.id = id
                self.role = type('Role', (), {'knowledge_areas': knowledge_areas})
                if accuracy:
                    self.performance_metrics = type('Metrics', (), {'accuracy': accuracy})

        # Create test agents
        agent1 = TestAgent("agent1", ["research_methods"])
        agent2 = TestAgent("agent2", ["research_methods", "information_evaluation"], accuracy=0.7)
        agent3 = TestAgent("agent3", ["research_methods", "information_evaluation", "critical_thinking"], accuracy=0.9)

        test_agents = [agent1, agent2, agent3]

        result = self.enhanced_consensus.combine_beliefs(self.complex_proposals, test_agents, self.context)

        self.assertIsNotNone(result)
        self.assertIn("solution", result)
        self.assertIn("consensus_metadata", result)
        self.assertEqual(result["consensus_metadata"]["method"], "enhanced_bayesian_consensus")

    def test_self_adjust_parameters(self):
        """Test self-adjustment of parameters based on consensus results."""
        # Record initial parameter values
        initial_prior_strength = self.enhanced_consensus.prior_strength
        initial_expertise_weight = self.enhanced_consensus.expertise_weight

        # Add some consensus performance history
        self.enhanced_consensus.consensus_performance = [0.6, 0.55, 0.65, 0.6, 0.58]

        # Trigger self-adjustment with low confidence
        self.enhanced_consensus._self_adjust_parameters(0.6, self.context)

        # Parameters should be adjusted upward for low confidence
        self.assertGreater(self.enhanced_consensus.prior_strength, initial_prior_strength)
        self.assertGreater(self.enhanced_consensus.expertise_weight, initial_expertise_weight)

        # Reset and test high confidence
        self.enhanced_consensus.prior_strength = initial_prior_strength
        self.enhanced_consensus.expertise_weight = initial_expertise_weight
        self.enhanced_consensus.consensus_performance = [0.95, 0.92, 0.94, 0.93, 0.96]

        # Trigger self-adjustment with high confidence
        self.enhanced_consensus._self_adjust_parameters(0.95, self.context)

        # Parameters should be adjusted downward for high confidence
        self.assertLess(self.enhanced_consensus.prior_strength, initial_prior_strength)

    def test_empty_proposals(self):
        """Test handling of empty proposals list."""
        result = self.enhanced_consensus.combine_beliefs([], self.agents, self.context)

        # Should return an empty dict
        self.assertEqual(result, {})

    def test_no_agents(self):
        """Test handling of empty agents list."""
        result = self.enhanced_consensus.combine_beliefs(self.simple_proposals, [], self.context)

        # Should return the first proposal
        self.assertEqual(result, self.simple_proposals[0])

    def test_calculate_posterior(self):
        """Test the enhanced posterior probability calculation."""
        # Create test agents
        class TestAgent:
            def __init__(self, id, knowledge_areas, accuracy=None):
                self.id = id
                self.role = type('Role', (), {'knowledge_areas': knowledge_areas})
                if accuracy:
                    self.performance_metrics = type('Metrics', (), {'accuracy': accuracy})

        # Create test agents
        agent1 = TestAgent("agent1", ["research_methods"])
        agent2 = TestAgent("agent2", ["research_methods", "information_evaluation"], accuracy=0.7)
        agent3 = TestAgent("agent3", ["research_methods", "information_evaluation", "critical_thinking"], accuracy=0.9)

        test_agents = [agent1, agent2, agent3]

        # Create test proposals with reasoning quality
        test_proposals = [
            {"agent_id": "agent1", "answer": "42", "confidence": 0.7, "reasoning_quality": 0.6},
            {"agent_id": "agent2", "answer": "42", "confidence": 0.8, "reasoning_quality": 0.7},
            {"agent_id": "agent3", "answer": "43", "confidence": 0.9, "reasoning_quality": 0.9}
        ]

        # Extract unique proposals
        unique_proposals = self.enhanced_consensus._extract_unique_proposals(test_proposals)

        # Calculate agent weights
        agent_weights = {agent: 1.0 for agent in test_agents}

        # Initialize prior probabilities
        prior_probs = {value: 1.0 / len(unique_proposals) for value in unique_proposals}

        # Calculate posterior probabilities
        posterior_probs, evidence_quality = self.enhanced_consensus._calculate_posterior(
            unique_proposals,
            test_proposals,
            agent_weights,
            prior_probs
        )

        # Check that posterior probabilities were calculated
        self.assertEqual(len(posterior_probs), len(unique_proposals))

        # Check that evidence quality was calculated
        self.assertEqual(len(evidence_quality), len(unique_proposals))

        # Check that evidence quality contains expected fields
        for value, quality in evidence_quality.items():
            self.assertIn("count", quality)
            self.assertIn("avg_confidence", quality)
            self.assertIn("avg_reasoning_quality", quality)
            self.assertIn("agent_contributions", quality)

        # Check that posterior probabilities sum to approximately 1
        self.assertAlmostEqual(sum(posterior_probs.values()), 1.0, places=5)

        # Check that enhanced metadata is included in combine_beliefs result
        result = self.enhanced_consensus.combine_beliefs(test_proposals, test_agents, self.context)
        self.assertIn("consensus_metadata", result)
        self.assertIn("evidence_quality", result["consensus_metadata"])
        self.assertIn("winning_evidence", result["consensus_metadata"])


if __name__ == "__main__":
    unittest.main()
