"""
Unit tests for the consensus mechanisms in multi-agent systems.
"""

import unittest
from unittest.mock import MagicMock, patch
from typing import Dict, Any, List

from src.deep_research_core.multi_agent.core import ConsensusMechanism, AgentRole, Agent

# Try to import Bayesian consensus classes for testing
try:
    from src.deep_research_core.multi_agent.bayesian_consensus import (
        BayesianConsensus,
        ExpertWeightedConsensus
    )
    BAYESIAN_CONSENSUS_AVAILABLE = True
except ImportError:
    BAYESIAN_CONSENSUS_AVAILABLE = False


class MockAgent:
    """Mock agent for testing consensus mechanisms."""

    def __init__(self, id: str, role=None, knowledge_areas=None):
        self.id = id
        if role is None:
            self.role = MagicMock()
            self.role.knowledge_areas = knowledge_areas or []
        else:
            self.role = role


class TestConsensusMechanism(unittest.TestCase):
    """Tests for the ConsensusMechanism class."""

    def setUp(self):
        """Set up test environment."""
        # Create mock agents with different expertise
        self.agent1 = MockAgent("agent1", knowledge_areas=["math", "logic"])
        self.agent2 = MockAgent("agent2", knowledge_areas=["history"])
        self.agent3 = MockAgent("agent3", knowledge_areas=["science", "physics", "chemistry"])

        self.agents = [self.agent1, self.agent2, self.agent3]

        # Simple test proposals
        self.simple_proposals = [
            {"agent_id": "agent1", "answer": "42", "confidence": 0.8},
            {"agent_id": "agent2", "answer": "42", "confidence": 0.7},
            {"agent_id": "agent3", "answer": "43", "confidence": 0.9}
        ]

        # Dictionary test proposals
        self.dict_proposals = [
            {"agent_id": "agent1", "solution": {"result": 42, "method": "calculation"}, "confidence": 0.8},
            {"agent_id": "agent2", "solution": {"result": 42, "method": "lookup"}, "confidence": 0.7},
            {"agent_id": "agent3", "solution": {"result": 43, "method": "estimation"}, "confidence": 0.9}
        ]

        # Test context
        self.context = {"task": "Find the answer to the ultimate question"}

    def test_simple_voting_with_clear_winner(self):
        """Test simple voting with a clear winner."""
        consensus = ConsensusMechanism(strategy="voting")
        result = consensus.reach_consensus(self.simple_proposals, self.agents, self.context)

        # "42" should win with 2 votes vs 1 vote for "43"
        self.assertEqual(result["answer"], "42")
        self.assertIn("consensus_metadata", result)
        self.assertEqual(result["consensus_metadata"]["vote_count"], 2)
        self.assertEqual(result["consensus_metadata"]["total_votes"], 3)
        self.assertAlmostEqual(result["consensus_metadata"]["confidence"], 2/3)

    def test_simple_voting_with_dictionary_values(self):
        """Test simple voting with dictionary values."""
        # In this case, we're checking that dictionaries can be properly compared
        consensus = ConsensusMechanism(strategy="voting")
        result = consensus.reach_consensus(self.dict_proposals, self.agents, self.context)

        # The solution with result 42 should win (2 votes vs 1)
        self.assertEqual(result["solution"]["result"], 42)

    def test_weighted_voting_with_expertise(self):
        """Test weighted voting considering agent expertise."""
        consensus = ConsensusMechanism(strategy="weighted_voting")

        # Create proposals where the expert (agent3) has a different opinion
        proposals = [
            {"agent_id": "agent1", "answer": "42", "confidence": 0.8},
            {"agent_id": "agent2", "answer": "42", "confidence": 0.7},
            {"agent_id": "agent3", "answer": "43", "confidence": 0.9}  # Expert with highest confidence
        ]

        result = consensus.reach_consensus(proposals, self.agents, self.context)

        # "43" should win because agent3 has more expertise (3 knowledge areas) and higher confidence
        self.assertEqual(result["answer"], "43")
        self.assertIn("consensus_metadata", result)
        self.assertEqual(result["consensus_metadata"]["method"], "weighted_voting")

    def test_weighted_voting_with_missing_agent_id(self):
        """Test weighted voting with a proposal missing agent_id."""
        consensus = ConsensusMechanism(strategy="weighted_voting")

        # Create proposals with one missing agent_id
        proposals = [
            {"answer": "42", "confidence": 0.8},  # Missing agent_id
            {"agent_id": "agent2", "answer": "42", "confidence": 0.7},
            {"agent_id": "agent3", "answer": "43", "confidence": 0.9}
        ]

        result = consensus.reach_consensus(proposals, self.agents, self.context)

        # Should still work, using default weight for the proposal without agent_id
        self.assertIsNotNone(result)
        self.assertIn("answer", result)

    def test_empty_proposals(self):
        """Test handling of empty proposals list."""
        consensus = ConsensusMechanism(strategy="voting")
        result = consensus.reach_consensus([], self.agents, self.context)

        # Should return an empty dict
        self.assertEqual(result, {})

        # Test with weighted voting too
        consensus = ConsensusMechanism(strategy="weighted_voting")
        result = consensus.reach_consensus([], self.agents, self.context)

        # Should return an empty dict
        self.assertEqual(result, {})

    def test_invalid_strategy(self):
        """Test handling of invalid consensus strategy."""
        consensus = ConsensusMechanism(strategy="invalid_strategy")

        # Should raise ValueError
        with self.assertRaises(ValueError):
            consensus.reach_consensus(self.simple_proposals, self.agents, self.context)

    @unittest.skipIf(not BAYESIAN_CONSENSUS_AVAILABLE, "Bayesian consensus module not available")
    def test_bayesian_consensus_integration(self):
        """Test integration of Bayesian consensus with ConsensusMechanism."""
        # Create a ConsensusMechanism with Bayesian strategy
        consensus = ConsensusMechanism(strategy="bayesian")

        # Add performance metrics to agent3
        self.agent3.performance_metrics = MagicMock()
        self.agent3.performance_metrics.accuracy = 0.9

        # Add prior beliefs to context
        context = self.context.copy()
        context["prior_beliefs"] = {
            "42": 0.6,
            "43": 0.4
        }

        # Test with proposals where agent3 (expert) has a different opinion
        result = consensus.reach_consensus(self.simple_proposals, self.agents, context)

        # Verify that Bayesian consensus was used
        self.assertIsNotNone(result)
        self.assertIn("consensus_metadata", result)
        self.assertEqual(result["consensus_metadata"]["method"], "bayesian_consensus")

        # Verify that posterior probabilities are included
        self.assertIn("posterior_probabilities", result["consensus_metadata"])
        self.assertIn("agent_weights", result["consensus_metadata"])

    @unittest.skipIf(not BAYESIAN_CONSENSUS_AVAILABLE, "Bayesian consensus module not available")
    def test_expert_weighted_consensus_integration(self):
        """Test integration of Expert-weighted consensus with ConsensusMechanism."""
        # Create a ConsensusMechanism with expert_weighted strategy
        consensus = ConsensusMechanism(strategy="expert_weighted")

        # Add task domains to context
        context = self.context.copy()
        context["domains"] = ["science", "physics"]  # Matches agent3's expertise

        # Test with proposals where agent3 (expert) has a different opinion
        result = consensus.reach_consensus(self.simple_proposals, self.agents, context)

        # Verify that Expert-weighted consensus was used
        self.assertIsNotNone(result)
        self.assertIn("consensus_metadata", result)
        self.assertEqual(result["consensus_metadata"]["method"], "expert_weighted_consensus")

        # Verify that expertise weights are included
        self.assertIn("expertise_weights", result["consensus_metadata"])
        self.assertIn("task_domains", result["consensus_metadata"])

        # Agent3 should have higher weight due to matching expertise
        expertise_weights = result["consensus_metadata"]["expertise_weights"]
        self.assertIn("agent3", expertise_weights)

        # The answer should be "43" due to agent3's expertise in the task domains
        self.assertEqual(result["answer"], "43")

    @unittest.skipIf(not BAYESIAN_CONSENSUS_AVAILABLE, "Bayesian consensus module not available")
    def test_fallback_when_bayesian_not_available(self):
        """Test fallback to weighted voting when Bayesian consensus is not available."""
        # Create a ConsensusMechanism with Bayesian strategy
        with patch("src.deep_research_core.multi_agent.core.ConsensusMechanism.__init__") as mock_init:
            # Force _bayesian_consensus to be None
            mock_init.return_value = None
            consensus = ConsensusMechanism(strategy="bayesian")
            consensus.strategy = "bayesian"
            consensus._bayesian_consensus = None
            consensus._weighted_voting = MagicMock(return_value={"answer": "fallback"})

            # Call reach_consensus
            result = consensus.reach_consensus(self.simple_proposals, self.agents, self.context)

            # Verify that weighted voting was used as fallback
            consensus._weighted_voting.assert_called_once()
            self.assertEqual(result["answer"], "fallback")


if __name__ == '__main__':
    unittest.main()