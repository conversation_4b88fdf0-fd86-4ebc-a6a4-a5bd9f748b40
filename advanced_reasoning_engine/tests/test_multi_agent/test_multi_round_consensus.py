"""
Unit tests for Multi-Round consensus mechanism.

This module tests the Multi-Round consensus mechanism for the multi-agent system,
which applies different consensus strategies in sequence to reach a more robust agreement.
"""

import unittest
from unittest.mock import MagicMock, patch
from typing import Dict, Any, List

from deep_research_core.multi_agent.bayesian_consensus import (
    MultiRoundConsensus,
    BayesianConsensus,
    ExpertWeightedConsensus
)


class TestMultiRoundConsensus(unittest.TestCase):
    """Test cases for the Multi-Round consensus mechanism."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a MultiRoundConsensus instance with custom configuration
        self.multi_round_consensus = MultiRoundConsensus(
            rounds_config=[
                {
                    'strategy': 'weighted_voting',
                    'filter_ratio': 0.7,
                    'params': {}
                },
                {
                    'strategy': 'bayesian',
                    'filter_ratio': 1.0,
                    'params': {}
                }
            ],
            confidence_threshold=0.8,
            min_proposals_per_round=2,
            max_rounds=2
        )

        # Create mock agents with different expertise levels
        self.agent1 = MagicMock()
        self.agent1.id = "agent1"
        self.agent1.role = MagicMock()
        self.agent1.role.knowledge_areas = ["research_methods"]

        self.agent2 = MagicMock()
        self.agent2.id = "agent2"
        self.agent2.role = MagicMock()
        self.agent2.role.knowledge_areas = ["data_analysis", "statistics"]

        self.agent3 = MagicMock()
        self.agent3.id = "agent3"
        self.agent3.role = MagicMock()
        self.agent3.role.knowledge_areas = ["research_methods", "information_evaluation", "critical_thinking"]
        self.agent3.performance_metrics = MagicMock()
        self.agent3.performance_metrics.accuracy = 0.9

        self.agents = [self.agent1, self.agent2, self.agent3]

        # Create proposals
        self.proposals = [
            {"agent_id": "agent1", "answer": "42", "confidence": 0.7},
            {"agent_id": "agent2", "answer": "43", "confidence": 0.8},
            {"agent_id": "agent3", "answer": "42", "confidence": 0.9}
        ]

        # Create context with task domains
        self.context = {
            "task_type": "research",
            "domains": ["research_methods", "information_evaluation"],
            "prior_beliefs": {
                "42": 0.6,
                "43": 0.4
            }
        }

    def test_apply_round_strategy(self):
        """Test applying a specific consensus strategy for a round."""
        # Create real agents instead of MagicMock for this test
        class TestAgent:
            def __init__(self, id, knowledge_areas, accuracy=None):
                self.id = id
                self.role = type('Role', (), {'knowledge_areas': knowledge_areas})
                if accuracy:
                    self.performance_metrics = type('Metrics', (), {'accuracy': accuracy})

        # Create test agents
        agent1 = TestAgent("agent1", ["research_methods"])
        agent2 = TestAgent("agent2", ["research_methods", "information_evaluation"], accuracy=0.7)
        agent3 = TestAgent("agent3", ["research_methods", "information_evaluation", "critical_thinking"], accuracy=0.9)

        test_agents = [agent1, agent2, agent3]

        # Test weighted_voting strategy
        result = self.multi_round_consensus._apply_round_strategy(
            'weighted_voting',
            self.proposals,
            test_agents,
            self.context,
            {}
        )

        self.assertIsNotNone(result)
        self.assertIn("answer", result)
        self.assertEqual(result["answer"], "42")  # Should win due to agent3's expertise

        # Test bayesian strategy
        result = self.multi_round_consensus._apply_round_strategy(
            'bayesian',
            self.proposals,
            test_agents,
            self.context,
            {}
        )

        self.assertIsNotNone(result)
        self.assertIn("answer", result)
        self.assertEqual(result["answer"], "42")  # Should win due to prior and agent3

        # Test unknown strategy (should fall back to weighted_voting)
        result = self.multi_round_consensus._apply_round_strategy(
            'unknown_strategy',
            self.proposals,
            test_agents,
            self.context,
            {}
        )

        self.assertIsNotNone(result)
        self.assertIn("answer", result)

    def test_filter_proposals_for_next_round(self):
        """Test filtering proposals for the next round."""
        # Create a current result
        current_result = {"answer": "42", "confidence": 0.8}

        # Filter with high ratio (should keep most proposals)
        filtered = self.multi_round_consensus._filter_proposals_for_next_round(
            self.proposals,
            current_result,
            0.7
        )

        self.assertGreaterEqual(len(filtered), 2)

        # Filter with low ratio (should keep minimum number)
        filtered = self.multi_round_consensus._filter_proposals_for_next_round(
            self.proposals,
            current_result,
            0.3
        )

        self.assertEqual(len(filtered), self.multi_round_consensus.min_proposals_per_round)

        # Check that proposals matching the current result are prioritized
        self.assertEqual(filtered[0]["answer"], "42")

    def test_reach_consensus_multiple_rounds(self):
        """Test reaching consensus through multiple rounds."""
        result = self.multi_round_consensus.reach_consensus(
            self.proposals,
            self.agents,
            self.context
        )

        self.assertIsNotNone(result)
        self.assertIn("answer", result)
        self.assertIn("consensus_metadata", result)
        self.assertEqual(result["consensus_metadata"]["method"], "multi_round_consensus")

        # Check that rounds history is included
        self.assertIn("rounds", result["consensus_metadata"])
        rounds_history = result["consensus_metadata"]["rounds"]
        self.assertGreaterEqual(len(rounds_history), 1)

        # The answer should be "42" due to agent expertise and prior beliefs
        self.assertEqual(result["answer"], "42")

    def test_early_stopping(self):
        """Test early stopping when confidence threshold is reached."""
        # Create a multi-round consensus with a low confidence threshold
        multi_round = MultiRoundConsensus(
            confidence_threshold=0.6,  # Low threshold to trigger early stopping
            max_rounds=3
        )

        # Create proposals with high confidence
        high_confidence_proposals = [
            {
                "agent_id": "agent1",
                "answer": "42",
                "confidence": 0.9  # High confidence
            },
            {
                "agent_id": "agent2",
                "answer": "42",  # Same proposal
                "confidence": 0.85  # High confidence
            },
            {
                "agent_id": "agent3",
                "answer": "43",
                "confidence": 0.7
            }
        ]

        result = multi_round.reach_consensus(high_confidence_proposals, self.agents, self.context)

        # Verify early stopping occurred
        self.assertIn("consensus_metadata", result)
        self.assertEqual(result["consensus_metadata"]["method"], "multi_round_consensus")
        self.assertTrue(result["consensus_metadata"]["early_stopping"])
        self.assertEqual(result["answer"], "42")

    def test_empty_proposals(self):
        """Test handling of empty proposals list."""
        result = self.multi_round_consensus.reach_consensus([], self.agents, self.context)

        # Should return an empty dict
        self.assertEqual(result, {})

    def test_no_agents(self):
        """Test handling of empty agents list."""
        result = self.multi_round_consensus.reach_consensus(self.proposals, [], self.context)

        # Should return the first proposal
        self.assertEqual(result, self.proposals[0])

    def test_single_proposal(self):
        """Test handling of a single proposal."""
        single_proposal = [self.proposals[0]]
        result = self.multi_round_consensus.reach_consensus(single_proposal, self.agents, self.context)

        # Should return the single proposal with metadata
        self.assertEqual(result["answer"], "42")
        self.assertIn("consensus_metadata", result)
        self.assertEqual(result["consensus_metadata"]["method"], "multi_round_consensus")

    def test_max_rounds_limit(self):
        """Test that max_rounds limit is respected."""
        # Create a multi-round consensus with many rounds but a low max_rounds
        multi_round = MultiRoundConsensus(
            rounds_config=[
                {'strategy': 'voting', 'filter_ratio': 0.8, 'params': {}},
                {'strategy': 'weighted_voting', 'filter_ratio': 0.8, 'params': {}},
                {'strategy': 'discussion', 'filter_ratio': 0.8, 'params': {}},
                {'strategy': 'bayesian', 'filter_ratio': 1.0, 'params': {}}
            ],
            max_rounds=2,  # Only allow 2 rounds
            confidence_threshold=0.99  # High threshold to prevent early stopping
        )

        result = multi_round.reach_consensus(self.proposals, self.agents, self.context)

        # Verify only max_rounds were executed
        self.assertIn("consensus_metadata", result)
        self.assertIn("rounds", result["consensus_metadata"])
        self.assertLessEqual(len(result["consensus_metadata"]["rounds"]), 2)

    def test_custom_rounds_config(self):
        """Test using a custom rounds configuration."""
        # Create real agents instead of MagicMock for this test
        class TestAgent:
            def __init__(self, id, knowledge_areas, accuracy=None):
                self.id = id
                self.role = type('Role', (), {'knowledge_areas': knowledge_areas})
                if accuracy:
                    self.performance_metrics = type('Metrics', (), {'accuracy': accuracy})

        # Create test agents
        agent1 = TestAgent("agent1", ["research_methods"])
        agent2 = TestAgent("agent2", ["research_methods", "information_evaluation"], accuracy=0.7)
        agent3 = TestAgent("agent3", ["research_methods", "information_evaluation", "critical_thinking"], accuracy=0.9)

        test_agents = [agent1, agent2, agent3]

        # Create a multi-round consensus with a custom configuration
        custom_config = [
            {
                'strategy': 'voting',  # Simple voting first
                'filter_ratio': 0.8,
                'params': {}
            },
            {
                'strategy': 'weighted_voting',  # Then weighted voting
                'filter_ratio': 1.0,
                'params': {}
            }
        ]

        multi_round = MultiRoundConsensus(
            rounds_config=custom_config,
            confidence_threshold=0.99,  # High threshold to prevent early stopping
            min_proposals_per_round=1  # Allow continuing with just 1 proposal
        )

        result = multi_round.reach_consensus(self.proposals, test_agents, self.context)

        # Verify the custom configuration was used
        self.assertIn("consensus_metadata", result)
        self.assertIn("rounds", result["consensus_metadata"])
        rounds_history = result["consensus_metadata"]["rounds"]

        # Check that at least one round was executed
        self.assertGreaterEqual(len(rounds_history), 1)
        self.assertEqual(rounds_history[0]["strategy"], "voting")


if __name__ == "__main__":
    unittest.main()
