#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test các tính năng nâng cao của AdaptiveCrawler.

Module này kiểm tra các tính năng nâng cao của AdaptiveCrawler như crawl phân tán,
checkpoint và resume, proxy rotation, user agent rotation, xu<PERSON>t kết quả nhiều định dạng,
và lưu trữ ảnh chụp màn hình.
"""

import os
import sys
import unittest
import tempfile
import json
import shutil
from unittest.mock import patch, MagicMock

# Thêm thư mục gốc vào sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import các module cần thiết
from src.deep_research_core.crawlers.distributed_crawler import DistributedCrawler
from src.deep_research_core.crawlers.checkpoint_manager import CheckpointManager
from src.deep_research_core.crawlers.proxy_rotation_manager import ProxyRotationManager
from src.deep_research_core.crawlers.user_agent_rotation_manager import UserAgentRotationManager
from src.deep_research_core.crawlers.result_exporter import ResultExporter
from src.deep_research_core.crawlers.screenshot_manager import ScreenshotManager
from src.deep_research_core.crawlers.advanced_features_integration import integrate_advanced_features
from src.deep_research_core.agents.adaptive_crawler import AdaptiveCrawler

class TestAdvancedFeatures(unittest.TestCase):
    """Test các tính năng nâng cao của AdaptiveCrawler."""

    def setUp(self):
        """Thiết lập trước mỗi test."""
        # Tạo thư mục tạm thời để lưu kết quả test
        self.test_dir = tempfile.mkdtemp()
        
        # Tạo AdaptiveCrawler
        self.crawler = AdaptiveCrawler()
        
        # Tạo các đường dẫn tạm thời
        self.checkpoint_path = os.path.join(self.test_dir, "checkpoint")
        self.download_path = os.path.join(self.test_dir, "downloads")
        self.screenshot_path = os.path.join(self.test_dir, "screenshots")
        self.export_path = os.path.join(self.test_dir, "exports")
        
        # Tạo các thư mục
        os.makedirs(self.download_path, exist_ok=True)
        os.makedirs(self.screenshot_path, exist_ok=True)
        os.makedirs(self.export_path, exist_ok=True)
        
        # Tạo cấu hình test
        self.config = {
            "enable_distributed_crawler": True,
            "enable_checkpoint_manager": True,
            "enable_proxy_rotation_manager": True,
            "enable_user_agent_rotation_manager": True,
            "enable_result_exporter": True,
            "enable_screenshot_manager": True,
            "distributed_crawler": {
                "master_node": None,
                "worker_nodes": [],
                "port": 8765,
                "distribution_mode": "round_robin",
                "batch_size": 10,
                "timeout": 30.0,
                "max_retries": 3,
                "retry_delay": 2.0,
                "checkpoint_interval": 100,
                "checkpoint_path": os.path.join(self.checkpoint_path, "distributed_crawler_checkpoint.json"),
                "use_local_workers": True,
                "max_local_workers": 4,
                "verbose": False
            },
            "checkpoint_manager": {
                "checkpoint_path": os.path.join(self.checkpoint_path, "crawler_checkpoint"),
                "checkpoint_interval": 100,
                "checkpoint_format": "json",
                "compress": False,
                "max_checkpoints": 5,
                "auto_checkpoint": True,
                "verbose": False
            },
            "proxy_rotation_manager": {
                "proxy_list": ["http://proxy1.example.com:8080", "http://proxy2.example.com:8080"],
                "proxy_file": None,
                "proxy_api_url": None,
                "proxy_api_key": None,
                "rotation_strategy": "round_robin",
                "check_proxy": False,
                "check_interval": 300,
                "timeout": 10.0,
                "max_retries": 3,
                "retry_delay": 2.0,
                "auto_update": False,
                "update_interval": 3600,
                "min_proxies": 5,
                "verbose": False
            },
            "user_agent_rotation_manager": {
                "user_agent_list": None,
                "user_agent_file": None,
                "rotation_strategy": "round_robin",
                "device_type": "all",
                "browser_type": "all",
                "os_type": "all",
                "include_random": False,
                "random_ratio": 0.1,
                "verbose": False
            },
            "result_exporter": {
                "output_dir": self.export_path,
                "default_format": "json",
                "pretty_print": True,
                "include_metadata": True,
                "include_stats": True,
                "include_headers": True,
                "include_timestamp": True,
                "timestamp_format": "%Y-%m-%d %H:%M:%S",
                "file_prefix": "test_result",
                "file_suffix": "",
                "overwrite": True,
                "encoding": "utf-8",
                "verbose": False
            },
            "screenshot_manager": {
                "output_dir": self.screenshot_path,
                "format": "png",
                "quality": 80,
                "full_page": True,
                "width": 1280,
                "height": 800,
                "device_scale_factor": 1.0,
                "wait_before_screenshot": 1.0,
                "wait_for_network_idle": True,
                "wait_for_selector": None,
                "wait_timeout": 30.0,
                "file_prefix": "test_screenshot",
                "file_suffix": "",
                "use_url_as_filename": True,
                "overwrite": True,
                "max_filename_length": 100,
                "verbose": False
            }
        }

    def tearDown(self):
        """Dọn dẹp sau mỗi test."""
        # Xóa thư mục tạm thời
        shutil.rmtree(self.test_dir)

    def test_distributed_crawler_initialization(self):
        """Kiểm tra khởi tạo DistributedCrawler."""
        distributed_crawler = DistributedCrawler(
            master_node=None,
            worker_nodes=[],
            port=8765,
            distribution_mode="round_robin",
            batch_size=10,
            timeout=30.0,
            max_retries=3,
            retry_delay=2.0,
            checkpoint_interval=100,
            checkpoint_path=os.path.join(self.checkpoint_path, "distributed_crawler_checkpoint.json"),
            use_local_workers=True,
            max_local_workers=4,
            verbose=False
        )
        
        self.assertIsNotNone(distributed_crawler)
        self.assertEqual(distributed_crawler.port, 8765)
        self.assertEqual(distributed_crawler.distribution_mode, "round_robin")
        self.assertEqual(distributed_crawler.batch_size, 10)
        self.assertEqual(distributed_crawler.timeout, 30.0)
        self.assertEqual(distributed_crawler.max_retries, 3)
        self.assertEqual(distributed_crawler.retry_delay, 2.0)
        self.assertEqual(distributed_crawler.checkpoint_interval, 100)
        self.assertEqual(distributed_crawler.checkpoint_path, os.path.join(self.checkpoint_path, "distributed_crawler_checkpoint.json"))
        self.assertEqual(distributed_crawler.use_local_workers, True)
        self.assertEqual(distributed_crawler.max_local_workers, 4)
        self.assertEqual(distributed_crawler.verbose, False)

    def test_checkpoint_manager_initialization(self):
        """Kiểm tra khởi tạo CheckpointManager."""
        checkpoint_manager = CheckpointManager(
            checkpoint_path=os.path.join(self.checkpoint_path, "crawler_checkpoint"),
            checkpoint_interval=100,
            checkpoint_format="json",
            compress=False,
            max_checkpoints=5,
            auto_checkpoint=True,
            verbose=False
        )
        
        self.assertIsNotNone(checkpoint_manager)
        self.assertEqual(checkpoint_manager.checkpoint_path, os.path.join(self.checkpoint_path, "crawler_checkpoint"))
        self.assertEqual(checkpoint_manager.checkpoint_interval, 100)
        self.assertEqual(checkpoint_manager.checkpoint_format, "json")
        self.assertEqual(checkpoint_manager.compress, False)
        self.assertEqual(checkpoint_manager.max_checkpoints, 5)
        self.assertEqual(checkpoint_manager.auto_checkpoint, True)
        self.assertEqual(checkpoint_manager.verbose, False)

    def test_proxy_rotation_manager_initialization(self):
        """Kiểm tra khởi tạo ProxyRotationManager."""
        proxy_rotation_manager = ProxyRotationManager(
            proxy_list=["http://proxy1.example.com:8080", "http://proxy2.example.com:8080"],
            proxy_file=None,
            proxy_api_url=None,
            proxy_api_key=None,
            rotation_strategy="round_robin",
            check_proxy=False,
            check_interval=300,
            timeout=10.0,
            max_retries=3,
            retry_delay=2.0,
            auto_update=False,
            update_interval=3600,
            min_proxies=5,
            verbose=False
        )
        
        self.assertIsNotNone(proxy_rotation_manager)
        self.assertEqual(proxy_rotation_manager.proxy_list, ["http://proxy1.example.com:8080", "http://proxy2.example.com:8080"])
        self.assertEqual(proxy_rotation_manager.rotation_strategy, "round_robin")
        self.assertEqual(proxy_rotation_manager.check_proxy, False)
        self.assertEqual(proxy_rotation_manager.check_interval, 300)
        self.assertEqual(proxy_rotation_manager.timeout, 10.0)
        self.assertEqual(proxy_rotation_manager.max_retries, 3)
        self.assertEqual(proxy_rotation_manager.retry_delay, 2.0)
        self.assertEqual(proxy_rotation_manager.auto_update, False)
        self.assertEqual(proxy_rotation_manager.update_interval, 3600)
        self.assertEqual(proxy_rotation_manager.min_proxies, 5)
        self.assertEqual(proxy_rotation_manager.verbose, False)

    def test_user_agent_rotation_manager_initialization(self):
        """Kiểm tra khởi tạo UserAgentRotationManager."""
        user_agent_rotation_manager = UserAgentRotationManager(
            user_agent_list=None,
            user_agent_file=None,
            rotation_strategy="round_robin",
            device_type="all",
            browser_type="all",
            os_type="all",
            include_random=False,
            random_ratio=0.1,
            verbose=False
        )
        
        self.assertIsNotNone(user_agent_rotation_manager)
        self.assertEqual(user_agent_rotation_manager.rotation_strategy, "round_robin")
        self.assertEqual(user_agent_rotation_manager.device_type, "all")
        self.assertEqual(user_agent_rotation_manager.browser_type, "all")
        self.assertEqual(user_agent_rotation_manager.os_type, "all")
        self.assertEqual(user_agent_rotation_manager.include_random, False)
        self.assertEqual(user_agent_rotation_manager.random_ratio, 0.1)
        self.assertEqual(user_agent_rotation_manager.verbose, False)

    def test_result_exporter_initialization(self):
        """Kiểm tra khởi tạo ResultExporter."""
        result_exporter = ResultExporter(
            output_dir=self.export_path,
            default_format="json",
            pretty_print=True,
            include_metadata=True,
            include_stats=True,
            include_headers=True,
            include_timestamp=True,
            timestamp_format="%Y-%m-%d %H:%M:%S",
            file_prefix="test_result",
            file_suffix="",
            overwrite=True,
            encoding="utf-8",
            verbose=False
        )
        
        self.assertIsNotNone(result_exporter)
        self.assertEqual(result_exporter.output_dir, self.export_path)
        self.assertEqual(result_exporter.default_format, "json")
        self.assertEqual(result_exporter.pretty_print, True)
        self.assertEqual(result_exporter.include_metadata, True)
        self.assertEqual(result_exporter.include_stats, True)
        self.assertEqual(result_exporter.include_headers, True)
        self.assertEqual(result_exporter.include_timestamp, True)
        self.assertEqual(result_exporter.timestamp_format, "%Y-%m-%d %H:%M:%S")
        self.assertEqual(result_exporter.file_prefix, "test_result")
        self.assertEqual(result_exporter.file_suffix, "")
        self.assertEqual(result_exporter.overwrite, True)
        self.assertEqual(result_exporter.encoding, "utf-8")
        self.assertEqual(result_exporter.verbose, False)

    def test_screenshot_manager_initialization(self):
        """Kiểm tra khởi tạo ScreenshotManager."""
        screenshot_manager = ScreenshotManager(
            output_dir=self.screenshot_path,
            format="png",
            quality=80,
            full_page=True,
            width=1280,
            height=800,
            device_scale_factor=1.0,
            wait_before_screenshot=1.0,
            wait_for_network_idle=True,
            wait_for_selector=None,
            wait_timeout=30.0,
            file_prefix="test_screenshot",
            file_suffix="",
            use_url_as_filename=True,
            overwrite=True,
            max_filename_length=100,
            verbose=False
        )
        
        self.assertIsNotNone(screenshot_manager)
        self.assertEqual(screenshot_manager.output_dir, self.screenshot_path)
        self.assertEqual(screenshot_manager.format, "png")
        self.assertEqual(screenshot_manager.quality, 80)
        self.assertEqual(screenshot_manager.full_page, True)
        self.assertEqual(screenshot_manager.width, 1280)
        self.assertEqual(screenshot_manager.height, 800)
        self.assertEqual(screenshot_manager.device_scale_factor, 1.0)
        self.assertEqual(screenshot_manager.wait_before_screenshot, 1.0)
        self.assertEqual(screenshot_manager.wait_for_network_idle, True)
        self.assertEqual(screenshot_manager.wait_for_selector, None)
        self.assertEqual(screenshot_manager.wait_timeout, 30.0)
        self.assertEqual(screenshot_manager.file_prefix, "test_screenshot")
        self.assertEqual(screenshot_manager.file_suffix, "")
        self.assertEqual(screenshot_manager.use_url_as_filename, True)
        self.assertEqual(screenshot_manager.overwrite, True)
        self.assertEqual(screenshot_manager.max_filename_length, 100)
        self.assertEqual(screenshot_manager.verbose, False)

    @patch('src.deep_research_core.crawlers.advanced_features_integration.integrate_distributed_crawler')
    @patch('src.deep_research_core.crawlers.advanced_features_integration.integrate_checkpoint_manager')
    @patch('src.deep_research_core.crawlers.advanced_features_integration.integrate_proxy_rotation_manager')
    @patch('src.deep_research_core.crawlers.advanced_features_integration.integrate_user_agent_rotation_manager')
    @patch('src.deep_research_core.crawlers.advanced_features_integration.integrate_result_exporter')
    @patch('src.deep_research_core.crawlers.advanced_features_integration.integrate_screenshot_manager')
    def test_integrate_advanced_features(self, mock_integrate_screenshot_manager, mock_integrate_result_exporter, 
                                        mock_integrate_user_agent_rotation_manager, mock_integrate_proxy_rotation_manager, 
                                        mock_integrate_checkpoint_manager, mock_integrate_distributed_crawler):
        """Kiểm tra tích hợp các tính năng nâng cao."""
        # Thiết lập mock
        mock_integrate_distributed_crawler.return_value = True
        mock_integrate_checkpoint_manager.return_value = True
        mock_integrate_proxy_rotation_manager.return_value = True
        mock_integrate_user_agent_rotation_manager.return_value = True
        mock_integrate_result_exporter.return_value = True
        mock_integrate_screenshot_manager.return_value = True
        
        # Gọi hàm tích hợp
        result = integrate_advanced_features(self.crawler, self.config)
        
        # Kiểm tra kết quả
        self.assertTrue(result["distributed_crawler"])
        self.assertTrue(result["checkpoint_manager"])
        self.assertTrue(result["proxy_rotation_manager"])
        self.assertTrue(result["user_agent_rotation_manager"])
        self.assertTrue(result["result_exporter"])
        self.assertTrue(result["screenshot_manager"])
        
        # Kiểm tra các hàm tích hợp đã được gọi
        mock_integrate_distributed_crawler.assert_called_once()
        mock_integrate_checkpoint_manager.assert_called_once()
        mock_integrate_proxy_rotation_manager.assert_called_once()
        mock_integrate_user_agent_rotation_manager.assert_called_once()
        mock_integrate_result_exporter.assert_called_once()
        mock_integrate_screenshot_manager.assert_called_once()

if __name__ == '__main__':
    unittest.main()
