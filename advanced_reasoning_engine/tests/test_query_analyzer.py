"""
Tests for the QueryAnalyzer class.
"""

import unittest
from src.deep_research_core.agents.query_analyzer import QueryAnalyzer

class TestQueryAnalyzer(unittest.TestCase):
    """Test cases for the QueryAnalyzer class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.analyzer = QueryAnalyzer()
    
    def test_clean_query(self):
        """Test query cleaning."""
        # Test lowercase conversion
        self.assertEqual(self.analyzer._clean_query("TEST QUERY"), "test query")
        
        # Test whitespace normalization
        self.assertEqual(self.analyzer._clean_query("  test   query  "), "test query")
        
        # Test punctuation removal at the end
        self.assertEqual(self.analyzer._clean_query("test query?"), "test query")
        self.assertEqual(self.analyzer._clean_query("test query!"), "test query")
        self.assertEqual(self.analyzer._clean_query("test query."), "test query")
    
    def test_analyze_complexity(self):
        """Test complexity analysis."""
        # Test simple queries
        simple_query = "weather today"
        complexity, score = self.analyzer._analyze_complexity(simple_query)
        self.assertEqual(complexity, "low")
        self.assertLess(score, 0.3)
        
        # Test medium complexity queries
        medium_query = "what is machine learning"
        complexity, score = self.analyzer._analyze_complexity(medium_query)
        self.assertEqual(complexity, "medium")
        self.assertGreaterEqual(score, 0.3)
        self.assertLess(score, 0.6)
        
        # Test high complexity queries
        complex_query = "compare the advantages and disadvantages of neural networks and decision trees"
        complexity, score = self.analyzer._analyze_complexity(complex_query)
        self.assertEqual(complexity, "high")
        self.assertGreaterEqual(score, 0.6)
    
    def test_detect_language(self):
        """Test language detection."""
        # Test English
        self.assertEqual(self.analyzer._detect_language("This is an English text"), "en")
        
        # Test Vietnamese
        self.assertEqual(self.analyzer._detect_language("Đây là văn bản tiếng Việt"), "vi")
    
    def test_classify_query_type(self):
        """Test query type classification."""
        # Test academic query
        self.assertEqual(self.analyzer._classify_query_type("research papers on machine learning"), "academic")
        
        # Test technical query
        self.assertEqual(self.analyzer._classify_query_type("python programming tutorial"), "technical")
        
        # Test medical query
        self.assertEqual(self.analyzer._classify_query_type("symptoms of covid-19"), "medical")
        
        # Test legal query
        self.assertEqual(self.analyzer._classify_query_type("copyright law in vietnam"), "legal")
        
        # Test general query
        self.assertEqual(self.analyzer._classify_query_type("best restaurants in hanoi"), "general")
    
    def test_detect_time_range(self):
        """Test time range detection."""
        # Test recent
        self.assertEqual(self.analyzer._detect_time_range("latest news about AI"), "recent")
        
        # Test past day
        self.assertEqual(self.analyzer._detect_time_range("yesterday's weather"), "past_day")
        
        # Test past week
        self.assertEqual(self.analyzer._detect_time_range("news from last week"), "past_week")
        
        # Test past month
        self.assertEqual(self.analyzer._detect_time_range("events in the past 30 days"), "past_month")
        
        # Test past year
        self.assertEqual(self.analyzer._detect_time_range("best movies from last year"), "past_year")
        
        # Test specific year
        self.assertEqual(self.analyzer._detect_time_range("world cup 2022"), "specific_year")
    
    def test_detect_region(self):
        """Test region detection."""
        # Test US
        self.assertEqual(self.analyzer._detect_region("news from usa"), "us")
        
        # Test UK
        self.assertEqual(self.analyzer._detect_region("universities in united kingdom"), "uk")
        
        # Test EU
        self.assertEqual(self.analyzer._detect_region("european union regulations"), "eu")
        
        # Test Asia
        self.assertEqual(self.analyzer._detect_region("asian cuisine"), "asia")
        
        # Test Vietnam
        self.assertEqual(self.analyzer._detect_region("việt nam culture"), "vn")
    
    def test_determine_search_method(self):
        """Test search method determination."""
        # Test academic query
        self.assertEqual(
            self.analyzer._determine_search_method(
                complexity="medium",
                complexity_score=0.5,
                query_type="academic",
                language="en",
                time_range="",
                region=""
            ),
            "academic_api"
        )
        
        # Test high complexity query
        self.assertEqual(
            self.analyzer._determine_search_method(
                complexity="high",
                complexity_score=0.8,
                query_type="general",
                language="en",
                time_range="",
                region=""
            ),
            "crawler"
        )
        
        # Test medium complexity query with time range
        self.assertEqual(
            self.analyzer._determine_search_method(
                complexity="medium",
                complexity_score=0.5,
                query_type="general",
                language="en",
                time_range="recent",
                region=""
            ),
            "api"
        )
        
        # Test low complexity query
        self.assertEqual(
            self.analyzer._determine_search_method(
                complexity="low",
                complexity_score=0.2,
                query_type="general",
                language="en",
                time_range="",
                region=""
            ),
            "api"
        )
    
    def test_recommend_search_engine(self):
        """Test search engine recommendation."""
        # Test academic query
        self.assertEqual(
            self.analyzer.recommend_search_engine("arxiv papers on machine learning", "academic"),
            "arxiv"
        )
        
        self.assertEqual(
            self.analyzer.recommend_search_engine("medical research on cancer treatment", "academic"),
            "pubmed"
        )
        
        self.assertEqual(
            self.analyzer.recommend_search_engine("citation count for deep learning papers", "academic"),
            "semantic_scholar"
        )
        
        self.assertEqual(
            self.analyzer.recommend_search_engine("research on natural language processing", "academic"),
            "google_scholar"
        )
        
        # Test general query
        self.assertEqual(
            self.analyzer.recommend_search_engine("what is the difference between machine learning and deep learning", "general"),
            "google"
        )
        
        self.assertEqual(
            self.analyzer.recommend_search_engine("latest news about AI", "general"),
            "bing"
        )
        
        self.assertEqual(
            self.analyzer.recommend_search_engine("weather today", "general"),
            "duckduckgo"
        )
    
    def test_analyze_query(self):
        """Test the full query analysis."""
        # Test academic query
        result = self.analyzer.analyze_query("research papers on machine learning from 2022")
        self.assertEqual(result["query_type"], "academic")
        self.assertEqual(result["time_range"], "specific_year")
        self.assertEqual(result["recommended_search_method"], "academic_api")
        
        # Test complex query
        result = self.analyzer.analyze_query("compare the advantages and disadvantages of neural networks and decision trees")
        self.assertEqual(result["complexity"], "high")
        self.assertGreaterEqual(result["complexity_score"], 0.6)
        self.assertEqual(result["recommended_search_method"], "crawler")
        
        # Test simple query
        result = self.analyzer.analyze_query("weather in hanoi today")
        self.assertEqual(result["complexity"], "low")
        self.assertLess(result["complexity_score"], 0.3)
        self.assertEqual(result["recommended_search_method"], "api")
        
        # Test Vietnamese query
        result = self.analyzer.analyze_query("nghiên cứu khoa học về trí tuệ nhân tạo")
        self.assertEqual(result["language"], "vi")
        self.assertEqual(result["query_type"], "academic")

if __name__ == "__main__":
    unittest.main()
