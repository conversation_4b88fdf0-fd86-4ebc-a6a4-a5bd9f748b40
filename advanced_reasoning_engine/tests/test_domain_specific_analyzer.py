"""
Unit tests for domain-specific analyzer.
"""

import unittest
from unittest.mock import patch, MagicMock, mock_open
import sys
import os
import json
import tempfile
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.deep_research_core.agents.domain_analyzers.domain_specific_analyzer import DomainSpecificAnalyzer

class TestDomainSpecificAnalyzer(unittest.TestCase):
    """Test cases for DomainSpecificAnalyzer."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Sample domain data for testing
        self.test_domain_data = {
            "health": {
                "terminology": {
                    "en": ["symptoms", "diagnosis", "treatment", "disease"],
                    "vi": ["triệu chứng", "chẩn đo<PERSON>", "điều trị", "bệnh"]
                },
                "entities": {
                    "en": ["doctor", "patient", "hospital"],
                    "vi": ["bác sĩ", "bệnh nhân", "bệnh viện"]
                },
                "patterns": {
                    "en": ["medical", "health"],
                    "vi": ["y tế", "sức khỏe"]
                },
                "credible_sources": ["who.int", "cdc.gov", "nih.gov"],
                "keywords": {
                    "en": ["medicine", "healthcare"],
                    "vi": ["thuốc", "chăm sóc sức khỏe"]
                }
            },
            "finance": {
                "terminology": {
                    "en": ["investment", "stock", "bond", "dividend"],
                    "vi": ["đầu tư", "cổ phiếu", "trái phiếu", "cổ tức"]
                },
                "entities": {
                    "en": ["bank", "investor", "market"],
                    "vi": ["ngân hàng", "nhà đầu tư", "thị trường"]
                },
                "patterns": {
                    "en": ["finance", "economic"],
                    "vi": ["tài chính", "kinh tế"]
                },
                "credible_sources": ["bloomberg.com", "wsj.com", "cnbc.com"],
                "keywords": {
                    "en": ["money", "finance"],
                    "vi": ["tiền", "tài chính"]
                }
            }
        }
        
        # Sample test texts
        self.health_text_en = "The patient presented with symptoms of fever and cough. The doctor recommended treatment with antibiotics."
        self.health_text_vi = "Bệnh nhân có các triệu chứng sốt và ho. Bác sĩ đề nghị điều trị bằng thuốc kháng sinh."
        
        self.finance_text_en = "The stock market declined by 2% today. Investors are concerned about dividend yields."
        self.finance_text_vi = "Thị trường chứng khoán giảm 2% hôm nay. Các nhà đầu tư lo ngại về lợi suất cổ tức."
        
        # Sample search results
        self.sample_search_results = {
            "success": True,
            "query": "COVID-19 symptoms treatment",
            "results": [
                {
                    "url": "https://www.who.int/health-topics/coronavirus",
                    "title": "Coronavirus disease (COVID-19)",
                    "snippet": "Learn about COVID-19 symptoms and treatments",
                    "content": self.health_text_en
                },
                {
                    "url": "https://example.com/covid19",
                    "title": "COVID-19 Information",
                    "snippet": "Information about the coronavirus pandemic",
                    "content": "COVID-19 has affected millions worldwide. " + self.health_text_en
                },
                {
                    "url": "https://finance.example.com/covid-economy",
                    "title": "COVID-19 Economic Impact",
                    "snippet": "How the pandemic affected markets",
                    "content": self.finance_text_en + " Many industries were affected by the pandemic."
                }
            ]
        }

    @patch("os.path.exists")
    @patch("json.load")
    @patch("builtins.open", new_callable=mock_open)
    def test_load_domain_data(self, mock_file, mock_json_load, mock_exists):
        """Test loading domain data."""
        # Setup mocks
        mock_exists.return_value = True
        mock_json_load.return_value = self.test_domain_data["health"]
        
        # Create analyzer
        analyzer = DomainSpecificAnalyzer(domain_type="health")
        
        # Check that domain data was loaded
        mock_exists.assert_called()
        mock_file.assert_called()
        mock_json_load.assert_called()
        
        # Make sure analyzer has domain data
        self.assertIn("health", analyzer.domain_data)
    
    @patch.object(DomainSpecificAnalyzer, "_load_domain_data")
    def test_detect_domain_type(self, mock_load):
        """Test detecting domain type from text."""
        # Setup mock
        mock_load.return_value = None
        
        # Create analyzer and set test data
        analyzer = DomainSpecificAnalyzer()
        analyzer.domain_data = self.test_domain_data
        
        # Test health text in English
        result = analyzer.detect_domain_type(self.health_text_en, language="en")
        self.assertEqual(result["domain"], "health")
        self.assertGreater(result["confidence"], 0.5)
        
        # Test health text in Vietnamese
        result = analyzer.detect_domain_type(self.health_text_vi, language="vi")
        self.assertEqual(result["domain"], "health")
        self.assertGreater(result["confidence"], 0.5)
        
        # Test finance text in English
        result = analyzer.detect_domain_type(self.finance_text_en, language="en")
        self.assertEqual(result["domain"], "finance")
        self.assertGreater(result["confidence"], 0.5)
        
        # Test finance text in Vietnamese
        result = analyzer.detect_domain_type(self.finance_text_vi, language="vi")
        self.assertEqual(result["domain"], "finance")
        self.assertGreater(result["confidence"], 0.5)
        
        # Test mixed domain text
        mixed_text = "The hospital reported financial losses. The stock market affected healthcare budgets."
        result = analyzer.detect_domain_type(mixed_text, language="en")
        # Should detect one of the domains with reasonable confidence
        self.assertIn(result["domain"], ["health", "finance"])
        
        # Test text with no clear domain
        random_text = "The weather was nice today. I went for a walk in the park."
        result = analyzer.detect_domain_type(random_text, language="en")
        self.assertEqual(result["domain"], "unknown")
        self.assertLess(result["confidence"], 0.2)
    
    def test_detect_language(self):
        """Test language detection."""
        analyzer = DomainSpecificAnalyzer()
        
        # Test English
        self.assertEqual(analyzer._detect_language("This is English text"), "en")
        
        # Test Vietnamese
        self.assertEqual(analyzer._detect_language("Đây là văn bản tiếng Việt"), "vi")
        
        # Test mixed but with Vietnamese characters
        self.assertEqual(analyzer._detect_language("This text has some tiếng Việt"), "vi")
    
    @patch.object(DomainSpecificAnalyzer, "_load_domain_data")
    def test_analyze_text_for_domain(self, mock_load):
        """Test analyzing text for domain-specific content."""
        # Setup mock
        mock_load.return_value = None
        
        # Create analyzer and set test data
        analyzer = DomainSpecificAnalyzer()
        analyzer.domain_data = self.test_domain_data
        
        # Test health text
        result = analyzer._analyze_text_for_domain(
            self.health_text_en,
            "https://example.com/health",
            "health"
        )
        
        # Check basic structure
        self.assertEqual(result["domain"], "example.com")
        self.assertEqual(result["language"], "en")
        self.assertEqual(result["domain_type"], "health")
        
        # Check terminology matches
        self.assertIn("symptoms", result["terminology"])
        self.assertIn("treatment", result["terminology"])
        
        # Check domain relevance score
        self.assertGreater(result["domain_relevance_score"], 0.3)
        
        # Test finance text
        result = analyzer._analyze_text_for_domain(
            self.finance_text_en,
            "https://wsj.com/markets",
            "finance"
        )
        
        # Check basic structure
        self.assertEqual(result["domain"], "wsj.com")
        self.assertEqual(result["domain_type"], "finance")
        
        # Check terminology matches
        self.assertIn("stock", result["terminology"])
        self.assertIn("dividend", result["terminology"])
        
        # Check domain credibility
        self.assertTrue(result["is_domain_specific_source"])
        
        # Check domain relevance score
        self.assertGreater(result["domain_relevance_score"], 0.3)
    
    @patch.object(DomainSpecificAnalyzer, "_load_domain_data")
    def test_analyze_domain_specific_content(self, mock_load):
        """Test analyzing search results for domain-specific content."""
        # Setup mock
        mock_load.return_value = None
        
        # Create analyzer and set test data
        analyzer = DomainSpecificAnalyzer()
        analyzer.domain_data = self.test_domain_data
        
        # Test with health domain
        results = analyzer.analyze_domain_specific_content(
            self.sample_search_results,
            domain_type="health"
        )
        
        # Check basic structure
        self.assertTrue(results["success"])
        self.assertEqual(results["query"], "COVID-19 symptoms treatment")
        
        # Check domain analysis
        domain_analysis = results["domain_analysis"]
        self.assertEqual(domain_analysis["domain_type"], "health")
        self.assertEqual(len(domain_analysis["result_analysis"]), 3)
        
        # Check terminology frequency
        terminology = domain_analysis["terminology_frequency"]
        self.assertGreater(len(terminology), 0)
        self.assertIn("symptoms", terminology)
        
        # Check domain specific sources
        self.assertGreater(len(domain_analysis["domain_specific_sources"]), 0)
        
        # Check domain relevance score
        self.assertGreater(domain_analysis["domain_relevance_score"], 0)
    
    @patch.object(DomainSpecificAnalyzer, "_load_domain_data")
    def test_filter_results_by_domain(self, mock_load):
        """Test filtering search results by domain relevance."""
        # Setup mock
        mock_load.return_value = None
        
        # Create analyzer and set test data
        analyzer = DomainSpecificAnalyzer()
        analyzer.domain_data = self.test_domain_data
        
        # Mock analyze_domain_specific_content to return predictable results
        analyzer.analyze_domain_specific_content = MagicMock(return_value={
            "success": True,
            "query": "COVID-19 symptoms treatment",
            "domain_analysis": {
                "domain_type": "health",
                "result_analysis": [
                    {
                        "url": "https://www.who.int/health-topics/coronavirus",
                        "domain_relevance_score": 0.9
                    },
                    {
                        "url": "https://example.com/covid19",
                        "domain_relevance_score": 0.7
                    },
                    {
                        "url": "https://finance.example.com/covid-economy",
                        "domain_relevance_score": 0.2
                    }
                ]
            }
        })
        
        # Test filtering with high threshold (should keep 1 result)
        results = analyzer.filter_results_by_domain(
            self.sample_search_results,
            domain_type="health",
            min_relevance_score=0.8
        )
        
        # Check filtered results
        self.assertEqual(results["original_result_count"], 3)
        self.assertEqual(results["filtered_result_count"], 1)
        self.assertEqual(results["domain_type"], "health")
        self.assertEqual(results["min_relevance_score"], 0.8)
        self.assertEqual(len(results["results"]), 1)
        self.assertEqual(results["results"][0]["url"], "https://www.who.int/health-topics/coronavirus")
        
        # Test filtering with medium threshold (should keep 2 results)
        results = analyzer.filter_results_by_domain(
            self.sample_search_results,
            domain_type="health",
            min_relevance_score=0.5
        )
        
        # Check filtered results
        self.assertEqual(results["filtered_result_count"], 2)
        self.assertEqual(len(results["results"]), 2)
    
    @patch.object(DomainSpecificAnalyzer, "_load_domain_data")
    def test_add_domain_terminology(self, mock_load):
        """Test adding terminology to a domain."""
        # Setup mock
        mock_load.return_value = None
        
        # Create temporary directory for test
        with tempfile.TemporaryDirectory() as tmpdir:
            # Create analyzer and set test data
            analyzer = DomainSpecificAnalyzer()
            analyzer.domain_data = {"health": {"terminology": {"en": ["existing_term"]}}}
            
            # Override DATA_DIR path
            original_data_dir = DomainSpecificAnalyzer.DATA_DIR
            DomainSpecificAnalyzer.DATA_DIR = Path(tmpdir)
            
            # Test adding new terms
            new_terms = ["new_term1", "new_term2"]
            
            # Mock json.dump to avoid actually writing files
            with patch("json.dump") as mock_dump:
                result = analyzer.add_domain_terminology("health", new_terms, "en")
                
                # Check result
                self.assertTrue(result)
                mock_dump.assert_called_once()
                
                # Check updated terminology
                terminology = analyzer.domain_data["health"]["terminology"]["en"]
                self.assertIn("existing_term", terminology)
                self.assertIn("new_term1", terminology)
                self.assertIn("new_term2", terminology)
            
            # Restore original DATA_DIR
            DomainSpecificAnalyzer.DATA_DIR = original_data_dir
    
    @patch.object(DomainSpecificAnalyzer, "_load_domain_data")
    def test_add_credible_sources(self, mock_load):
        """Test adding credible sources to a domain."""
        # Setup mock
        mock_load.return_value = None
        
        # Create temporary directory for test
        with tempfile.TemporaryDirectory() as tmpdir:
            # Create analyzer and set test data
            analyzer = DomainSpecificAnalyzer()
            analyzer.domain_data = {"health": {"credible_sources": ["existing_source.com"]}}
            
            # Override DATA_DIR path
            original_data_dir = DomainSpecificAnalyzer.DATA_DIR
            DomainSpecificAnalyzer.DATA_DIR = Path(tmpdir)
            
            # Test adding new sources
            new_sources = ["new_source1.com", "new_source2.org"]
            
            # Mock json.dump to avoid actually writing files
            with patch("json.dump") as mock_dump:
                result = analyzer.add_credible_sources("health", new_sources)
                
                # Check result
                self.assertTrue(result)
                mock_dump.assert_called_once()
                
                # Check updated sources
                sources = analyzer.domain_data["health"]["credible_sources"]
                self.assertIn("existing_source.com", sources)
                self.assertIn("new_source1.com", sources)
                self.assertIn("new_source2.org", sources)
            
            # Restore original DATA_DIR
            DomainSpecificAnalyzer.DATA_DIR = original_data_dir


if __name__ == '__main__':
    unittest.main() 