"""
Test script for RAGToTCoT integration.

This script tests the integration of RAG, ToT, and CoT in the RAGToTCoT class.
"""

import os
import sys
import unittest
from unittest.mock import MagicMock, patch
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.reasoning.rag_tot_cot import RAGTOTCOTReasoner

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestRAGToTCoTIntegration(unittest.TestCase):
    """Test case for RAGToTCoT integration."""

    def setUp(self):
        """Set up test fixtures."""
        # Create mock API provider
        self.mock_api_provider = MagicMock()
        self.mock_api_provider.generate.return_value = "This is a combined answer that synthesizes both approaches."

        # Create patches
        self.patches = [
            patch('src.deep_research_core.reasoning.rag_tot_cot.openai_provider', self.mock_api_provider),
            patch('src.deep_research_core.reasoning.rag_tot_cot.anthropic_provider', self.mock_api_provider),
            patch('src.deep_research_core.reasoning.rag_tot_cot.openrouter_provider', self.mock_api_provider)
        ]

        # Start patches
        for p in self.patches:
            p.start()

        # Create mock ToT
        self.mock_tot = MagicMock()
        self.mock_tot.reason.return_value = {
            "query": "What is artificial intelligence?",
            "best_path": [
                {"reasoning": "First, I'll analyze what AI is."},
                {"reasoning": "AI stands for Artificial Intelligence."}
            ],
            "answer": "AI is a field of computer science."
        }

        # Create mock CoT
        self.mock_cot = MagicMock()
        self.mock_cot.reason.return_value = {
            "reasoning": "AI stands for Artificial Intelligence. It is a field of computer science.",
            "answer": "AI is a field of computer science focused on creating intelligent machines.",
            "steps": 2
        }

        # Create mock RAG system
        self.mock_rag = MagicMock()
        self.mock_rag.query.return_value = [
            {"content": "AI is a field of computer science.", "source": "Test Source"}
        ]

        # Create RAGTOTCOTReasoner instance
        self.rag_tot_cot = RAGTOTCOTReasoner(
            rag_system=self.mock_rag,
            provider="openrouter",
            model="anthropic/claude-3-opus",
            temperature=0.7,
            max_tokens=2000,
            adaptive=True,
            use_advanced_optimization=True,
            verbose=True
        )

        # Replace ToT and CoT with mocks
        self.rag_tot_cot.tot = self.mock_tot
        self.rag_tot_cot.cot = self.mock_cot

    def tearDown(self):
        """Tear down test fixtures."""
        # Stop patches
        for p in self.patches:
            p.stop()

    def test_reason(self):
        """Test the reason method."""
        # Process a query
        result = self.rag_tot_cot.reason("What is artificial intelligence?")

        # Check that ToT and CoT were called
        self.mock_tot.reason.assert_called_once()
        self.mock_cot.reason.assert_called_once()

        # Check that the API provider was used
        self.assertIsNotNone(result)

        # Check that the result contains the expected fields
        self.assertIn("query", result)
        self.assertIn("methods_used", result)
        self.assertIn("rag_context_used", result)
        self.assertIn("total_latency", result)

    def test_compare_methods_result(self):
        """Test the result of compare_methods."""
        # Reset the mock call counts
        self.mock_tot.reset_mock()
        self.mock_cot.reset_mock()

        # Call compare_methods
        comparison = self.rag_tot_cot.compare_methods("What is artificial intelligence?")

        # Check that the comparison contains the expected fields
        self.assertIn("query", comparison)
        self.assertIn("methods", comparison)

        # Check that all methods were tested
        self.assertIn("cot_only", comparison["methods"])
        self.assertIn("rag_cot", comparison["methods"])
        self.assertIn("tot_cot", comparison["methods"])
        self.assertIn("rag_tot_cot", comparison["methods"])



if __name__ == '__main__':
    unittest.main()
