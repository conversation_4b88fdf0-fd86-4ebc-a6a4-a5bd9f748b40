#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test cho AdaptiveCrawler với CaptchaHandler mới.
"""

import unittest
import os
import sys
import time
from unittest.mock import MagicMock, patch

# Thêm thư mục gốc vào sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.crawlers.adaptive_crawler import AdaptiveCrawler
from src.deep_research_core.crawlers.adaptive_crawler_captcha_integration import (
    integrate_captcha_handler,
    is_captcha,
    solve_captcha,
    is_domain_known_captcha,
    clear_captcha_cache
)

class TestAdaptiveCrawlerCaptchaIntegration(unittest.TestCase):
    """Test cho AdaptiveCrawler với CaptchaHandler mới."""
    
    def setUp(self):
        """Thiết lập trước mỗi test."""
        # Tạo AdaptiveCrawler với handle_captcha=True
        self.crawler = AdaptiveCrawler(
            max_depth=2,
            max_urls_per_domain=5,
            max_total_urls=10,
            handle_captcha=True,
            verbose=True
        )
        
        # Sample HTML with CAPTCHA
        self.captcha_html = """
        <html>
            <head><title>Security Check</title></head>
            <body>
                <h1>Please complete the CAPTCHA</h1>
                <div class="g-recaptcha" data-sitekey="6LdQUOkUAAAAAJ6NhQr6c1O4I_T6JqM3TbRjJ3mB"></div>
                <p>Prove you are human to continue</p>
            </body>
        </html>
        """

        # Sample HTML without CAPTCHA
        self.normal_html = """
        <html>
            <head><title>Normal Page</title></head>
            <body>
                <h1>Welcome to our website</h1>
                <p>This is a normal page without CAPTCHA</p>
            </body>
        </html>
        """
        
        # Sample response with CAPTCHA
        self.captcha_response = {
            "content": self.captcha_html,
            "status_code": 200,
            "url": "https://example.com"
        }
        
        # Sample response without CAPTCHA
        self.normal_response = {
            "content": self.normal_html,
            "status_code": 200,
            "url": "https://example.com"
        }
        
    @patch('src.deep_research_core.crawlers.adaptive_crawler_captcha_integration.CaptchaHandler')
    def test_integrate_captcha_handler(self, mock_captcha_handler):
        """Test tích hợp CaptchaHandler vào AdaptiveCrawler."""
        # Thiết lập mock
        mock_captcha_handler.return_value = MagicMock()
        
        # Lưu phương thức gốc
        original_is_captcha = self.crawler._is_captcha
        original_solve_captcha = self.crawler._solve_captcha
        
        # Gọi hàm tích hợp
        integrate_captcha_handler(self.crawler)
        
        # Kiểm tra kết quả
        self.assertTrue(self.crawler.has_captcha_handler)
        self.assertIsNotNone(self.crawler._captcha_handler)
        self.assertTrue(hasattr(self.crawler, 'captcha_domains'))
        self.assertTrue(hasattr(self.crawler, 'last_captcha_detection'))
        
        # Kiểm tra phương thức đã được thay thế
        self.assertNotEqual(self.crawler._is_captcha, original_is_captcha)
        self.assertNotEqual(self.crawler._solve_captcha, original_solve_captcha)
        
    @patch('src.deep_research_core.crawlers.adaptive_crawler_captcha_integration.CaptchaHandler')
    def test_is_captcha(self, mock_captcha_handler):
        """Test phương thức is_captcha."""
        # Thiết lập mock
        mock_handler = MagicMock()
        mock_handler.detect_captcha.return_value = (True, "recaptcha", {"site_key": "test"})
        mock_captcha_handler.return_value = mock_handler
        
        # Tích hợp CaptchaHandler
        integrate_captcha_handler(self.crawler)
        
        # Gọi phương thức is_captcha
        result = is_captcha(self.crawler, self.captcha_html)
        
        # Kiểm tra kết quả
        self.assertTrue(result)
        mock_handler.detect_captcha.assert_called_once_with(self.captcha_html)
        
    @patch('src.deep_research_core.crawlers.adaptive_crawler_captcha_integration.CaptchaHandler')
    def test_solve_captcha(self, mock_captcha_handler):
        """Test phương thức solve_captcha."""
        # Thiết lập mock
        mock_handler = MagicMock()
        mock_handler.detect_captcha.return_value = (True, "recaptcha", {"site_key": "test"})
        mock_handler.handle_captcha.return_value = {
            "success": True,
            "handled": True,
            "message": "CAPTCHA đã được xử lý"
        }
        mock_captcha_handler.return_value = mock_handler
        
        # Tích hợp CaptchaHandler
        integrate_captcha_handler(self.crawler)
        
        # Gọi phương thức solve_captcha
        result = solve_captcha(self.crawler, "https://example.com", self.captcha_response)
        
        # Kiểm tra kết quả
        self.assertTrue(result)
        mock_handler.detect_captcha.assert_called_once_with(self.captcha_html)
        mock_handler.handle_captcha.assert_called_once()
        
    @patch('src.deep_research_core.crawlers.adaptive_crawler_captcha_integration.CaptchaHandler')
    def test_is_domain_known_captcha(self, mock_captcha_handler):
        """Test phương thức is_domain_known_captcha."""
        # Thiết lập mock
        mock_handler = MagicMock()
        mock_handler.is_domain_known_captcha.return_value = True
        mock_captcha_handler.return_value = mock_handler
        
        # Tích hợp CaptchaHandler
        integrate_captcha_handler(self.crawler)
        
        # Thêm domain vào danh sách domain có CAPTCHA
        self.crawler.captcha_domains.add("example.com")
        
        # Gọi phương thức is_domain_known_captcha
        result = is_domain_known_captcha(self.crawler, "https://example.com/page")
        
        # Kiểm tra kết quả
        self.assertTrue(result)
        
    @patch('src.deep_research_core.crawlers.adaptive_crawler_captcha_integration.CaptchaHandler')
    def test_clear_captcha_cache(self, mock_captcha_handler):
        """Test phương thức clear_captcha_cache."""
        # Thiết lập mock
        mock_handler = MagicMock()
        mock_captcha_handler.return_value = mock_handler
        
        # Tích hợp CaptchaHandler
        integrate_captcha_handler(self.crawler)
        
        # Thêm domain vào danh sách domain có CAPTCHA
        self.crawler.captcha_domains.add("example.com")
        
        # Thêm thông tin phát hiện CAPTCHA
        self.crawler.last_captcha_detection = {
            "https://example.com": time.time()
        }
        
        # Gọi phương thức clear_captcha_cache
        clear_captcha_cache(self.crawler)
        
        # Kiểm tra kết quả
        self.assertEqual(len(self.crawler.captcha_domains), 0)
        self.assertEqual(len(self.crawler.last_captcha_detection), 0)
        mock_handler.clear_captcha_cache.assert_called_once()
        
    @patch('src.deep_research_core.crawlers.adaptive_crawler_captcha_integration.CaptchaHandler')
    def test_crawl_with_captcha(self, mock_captcha_handler):
        """Test crawl với CAPTCHA."""
        # Bỏ qua test này vì cần môi trường thực tế
        self.skipTest("Cần môi trường thực tế để test")
        
    @patch('src.deep_research_core.crawlers.adaptive_crawler_captcha_integration.CaptchaHandler')
    def test_fallback_to_original_methods(self, mock_captcha_handler):
        """Test fallback về phương thức gốc khi có lỗi."""
        # Thiết lập mock để gây lỗi
        mock_captcha_handler.return_value = MagicMock()
        mock_captcha_handler.return_value.detect_captcha.side_effect = Exception("Test error")
        
        # Tích hợp CaptchaHandler
        integrate_captcha_handler(self.crawler)
        
        # Lưu phương thức gốc
        original_is_captcha = self.crawler._original_is_captcha
        
        # Gọi phương thức is_captcha
        result = is_captcha(self.crawler, self.captcha_html)
        
        # Kiểm tra kết quả - phải sử dụng phương thức gốc
        self.assertEqual(original_is_captcha, self.crawler._original_is_captcha)
        
    @patch('src.deep_research_core.crawlers.adaptive_crawler_captcha_integration.CaptchaHandler')
    def test_integration_with_crawl_url(self, mock_captcha_handler):
        """Test tích hợp với phương thức _crawl_url."""
        # Bỏ qua test này vì cần môi trường thực tế
        self.skipTest("Cần môi trường thực tế để test")
        
if __name__ == "__main__":
    unittest.main()
