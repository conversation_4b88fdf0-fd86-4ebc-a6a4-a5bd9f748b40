"""
Test script for SQLiteVectorStore.

This script tests the basic functionality of the SQLiteVectorStore class.
"""

import os
import sys
import unittest
import tempfile
import numpy as np
from typing import List, Dict, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.retrieval.vector_store.sqlite_vector_store import SQLiteVectorStore

class TestSQLiteVectorStore(unittest.TestCase):
    """Test case for SQLiteVectorStore."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary database file
        self.temp_dir = tempfile.TemporaryDirectory()
        self.db_path = os.path.join(self.temp_dir.name, "test_vector_store.sqlite")
        
        # Create a vector store
        self.vector_store = SQLiteVectorStore(
            db_path=self.db_path,
            table_name="test_vectors",
            embedding_dim=4,
            create_if_not_exists=True
        )
        
        # Sample data
        self.ids = ["doc1", "doc2", "doc3"]
        self.embeddings = [
            np.array([1.0, 0.0, 0.0, 0.0], dtype=np.float32),
            np.array([0.0, 1.0, 0.0, 0.0], dtype=np.float32),
            np.array([0.0, 0.0, 1.0, 0.0], dtype=np.float32)
        ]
        self.documents = [
            {"content": "This is document 1", "metadata": {"source": "test", "category": "A"}},
            {"content": "This is document 2", "metadata": {"source": "test", "category": "B"}},
            {"content": "This is document 3", "metadata": {"source": "test", "category": "A"}}
        ]
    
    def tearDown(self):
        """Tear down test fixtures."""
        # Close the vector store
        self.vector_store.close()
        
        # Remove the temporary directory
        self.temp_dir.cleanup()
    
    def test_add_and_count(self):
        """Test adding documents and counting them."""
        # Add documents
        added_ids = self.vector_store.add(self.ids, self.embeddings, self.documents)
        
        # Check that all documents were added
        self.assertEqual(len(added_ids), len(self.ids))
        
        # Check the count
        count = self.vector_store.count()
        self.assertEqual(count, len(self.ids))
    
    def test_search(self):
        """Test searching for documents."""
        # Add documents
        self.vector_store.add(self.ids, self.embeddings, self.documents)
        
        # Search for documents similar to the first document
        results = self.vector_store.search(self.embeddings[0], top_k=2)
        
        # Check that we got the expected number of results
        self.assertEqual(len(results), 2)
        
        # Check that the first result is the first document
        self.assertEqual(results[0]["id"], self.ids[0])
        
        # Check that the score is high for the first document
        self.assertGreater(results[0]["score"], 0.9)
    
    def test_get(self):
        """Test getting a document by ID."""
        # Add documents
        self.vector_store.add(self.ids, self.embeddings, self.documents)
        
        # Get the first document
        doc = self.vector_store.get(self.ids[0])
        
        # Check that we got the expected document
        self.assertEqual(doc["id"], self.ids[0])
        self.assertEqual(doc["content"], self.documents[0]["content"])
        self.assertEqual(doc["metadata"], self.documents[0]["metadata"])
    
    def test_delete(self):
        """Test deleting documents."""
        # Add documents
        self.vector_store.add(self.ids, self.embeddings, self.documents)
        
        # Delete the first document
        self.vector_store.delete([self.ids[0]])
        
        # Check that the count is reduced
        count = self.vector_store.count()
        self.assertEqual(count, len(self.ids) - 1)
        
        # Check that the first document is no longer there
        doc = self.vector_store.get(self.ids[0])
        self.assertIsNone(doc)
    
    def test_clear(self):
        """Test clearing all documents."""
        # Add documents
        self.vector_store.add(self.ids, self.embeddings, self.documents)
        
        # Clear all documents
        self.vector_store.clear()
        
        # Check that the count is zero
        count = self.vector_store.count()
        self.assertEqual(count, 0)

if __name__ == '__main__':
    unittest.main()
