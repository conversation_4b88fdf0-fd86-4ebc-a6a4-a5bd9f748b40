"""
Unit tests for AdaptiveRateLimiter.
"""

import os
import sys
import unittest
import time
import asyncio
from unittest.mock import patch, MagicMock

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# Import AdaptiveRateLimiter
try:
    from deepresearch.src.deep_research_core.utils.adaptive_rate_limiter import AdaptiveRateLimiter
    rate_limiter_available = True
except ImportError:
    rate_limiter_available = False


@unittest.skipIf(not rate_limiter_available, "AdaptiveRateLimiter not available")
class TestAdaptiveRateLimiter(unittest.TestCase):
    """Test cases for AdaptiveRateLimiter."""

    def setUp(self):
        """Set up test fixtures."""
        self.rate_limiter = AdaptiveRateLimiter(
            default_rate=10.0,
            domain_specific_rates={
                "example.com": 5.0,
                "test.com": 20.0
            },
            adaptive=True,
            backoff_factor=2.0,
            max_backoff=10.0,
            min_rate=1.0,
            max_rate=50.0,
            jitter=False,  # Disable jitter for predictable tests
            success_threshold=3,
            error_threshold=2,
            rate_increase_factor=1.5,
            rate_decrease_factor=0.5,
            verbose=False
        )

    def test_init(self):
        """Test initialization."""
        self.assertEqual(self.rate_limiter.default_rate, 10.0)
        self.assertEqual(self.rate_limiter.domain_specific_rates["example.com"], 5.0)
        self.assertEqual(self.rate_limiter.domain_specific_rates["test.com"], 20.0)
        self.assertTrue(self.rate_limiter.adaptive)
        self.assertEqual(self.rate_limiter.backoff_factor, 2.0)
        self.assertEqual(self.rate_limiter.max_backoff, 10.0)
        self.assertEqual(self.rate_limiter.min_rate, 1.0)
        self.assertEqual(self.rate_limiter.max_rate, 50.0)
        self.assertFalse(self.rate_limiter.jitter)
        self.assertEqual(self.rate_limiter.success_threshold, 3)
        self.assertEqual(self.rate_limiter.error_threshold, 2)
        self.assertEqual(self.rate_limiter.rate_increase_factor, 1.5)
        self.assertEqual(self.rate_limiter.rate_decrease_factor, 0.5)
        self.assertFalse(self.rate_limiter.verbose)

    def test_get_domain(self):
        """Test _get_domain method."""
        self.assertEqual(self.rate_limiter._get_domain("https://example.com/page"), "example.com")
        self.assertEqual(self.rate_limiter._get_domain("http://test.com/page?q=1"), "test.com")
        self.assertEqual(self.rate_limiter._get_domain("https://sub.domain.com/path"), "sub.domain.com")
        self.assertEqual(self.rate_limiter._get_domain("invalid-url"), "invalid-url")

    def test_get_rate(self):
        """Test _get_rate method."""
        # Domain with specific rate
        self.assertEqual(self.rate_limiter._get_rate("example.com"), 5.0)
        
        # Domain with specific rate
        self.assertEqual(self.rate_limiter._get_rate("test.com"), 20.0)
        
        # Domain without specific rate
        self.assertEqual(self.rate_limiter._get_rate("unknown.com"), 10.0)
        
        # Check that domain is added to domain_rates
        self.assertIn("unknown.com", self.rate_limiter.domain_rates)
        self.assertEqual(self.rate_limiter.domain_rates["unknown.com"], 10.0)

    def test_wait(self):
        """Test wait method."""
        with patch('time.sleep') as mock_sleep:
            # First request should not wait
            wait_time = self.rate_limiter.wait("https://example.com/page1")
            self.assertEqual(wait_time, 0.0)
            mock_sleep.assert_not_called()
            
            # Second request to same domain should wait
            wait_time = self.rate_limiter.wait("https://example.com/page2")
            self.assertGreater(wait_time, 0.0)
            mock_sleep.assert_called_once()
            
            # Request to different domain should not wait
            mock_sleep.reset_mock()
            wait_time = self.rate_limiter.wait("https://test.com/page")
            self.assertEqual(wait_time, 0.0)
            mock_sleep.assert_not_called()

    @unittest.skipIf(sys.version_info < (3, 7), "asyncio.run requires Python 3.7+")
    def test_wait_async(self):
        """Test wait_async method."""
        async def test_async_wait():
            with patch('asyncio.sleep') as mock_sleep:
                # First request should not wait
                wait_time = await self.rate_limiter.wait_async("https://example.com/page1")
                self.assertEqual(wait_time, 0.0)
                mock_sleep.assert_not_called()
                
                # Second request to same domain should wait
                wait_time = await self.rate_limiter.wait_async("https://example.com/page2")
                self.assertGreater(wait_time, 0.0)
                mock_sleep.assert_called_once()
                
                # Request to different domain should not wait
                mock_sleep.reset_mock()
                wait_time = await self.rate_limiter.wait_async("https://test.com/page")
                self.assertEqual(wait_time, 0.0)
                mock_sleep.assert_not_called()
        
        asyncio.run(test_async_wait())

    def test_report_success(self):
        """Test report_success method."""
        # Report success multiple times
        for _ in range(3):
            self.rate_limiter.report_success("https://example.com/page")
        
        # Check that rate was increased
        self.assertGreater(self.rate_limiter.domain_rates["example.com"], 5.0)
        
        # Check that success count was reset
        self.assertEqual(self.rate_limiter.domain_success_counts["example.com"], 0)

    def test_report_error(self):
        """Test report_error method."""
        # Report error multiple times
        for _ in range(2):
            self.rate_limiter.report_error("https://example.com/page", "timeout")
        
        # Check that rate was decreased
        self.assertLess(self.rate_limiter.domain_rates["example.com"], 5.0)
        
        # Check that error count was reset
        self.assertEqual(self.rate_limiter.domain_error_counts["example.com"], 0)
        
        # Check that backoff was increased
        self.assertGreater(self.rate_limiter.domain_backoff_times["example.com"], 0.0)

    def test_increase_rate(self):
        """Test _increase_rate method."""
        # Initial rate
        self.assertEqual(self.rate_limiter.domain_rates["example.com"], 5.0)
        
        # Increase rate
        self.rate_limiter._increase_rate("example.com")
        
        # Check new rate
        self.assertEqual(self.rate_limiter.domain_rates["example.com"], 7.5)  # 5.0 * 1.5
        
        # Check that rate doesn't exceed max_rate
        self.rate_limiter.domain_rates["example.com"] = 40.0
        self.rate_limiter._increase_rate("example.com")
        self.assertEqual(self.rate_limiter.domain_rates["example.com"], 50.0)  # max_rate

    def test_decrease_rate(self):
        """Test _decrease_rate method."""
        # Initial rate
        self.assertEqual(self.rate_limiter.domain_rates["example.com"], 5.0)
        
        # Decrease rate
        self.rate_limiter._decrease_rate("example.com")
        
        # Check new rate
        self.assertEqual(self.rate_limiter.domain_rates["example.com"], 2.5)  # 5.0 * 0.5
        
        # Check that rate doesn't go below min_rate
        self.rate_limiter.domain_rates["example.com"] = 1.5
        self.rate_limiter._decrease_rate("example.com")
        self.assertEqual(self.rate_limiter.domain_rates["example.com"], 1.0)  # min_rate

    def test_increase_backoff(self):
        """Test _increase_backoff method."""
        # Initial backoff
        self.assertNotIn("example.com", self.rate_limiter.domain_backoff_times)
        
        # Increase backoff
        self.rate_limiter._increase_backoff("example.com")
        
        # Check new backoff
        self.assertEqual(self.rate_limiter.domain_backoff_times["example.com"], 1.0)
        
        # Increase backoff again
        self.rate_limiter._increase_backoff("example.com")
        
        # Check new backoff
        self.assertEqual(self.rate_limiter.domain_backoff_times["example.com"], 2.0)  # 1.0 * 2.0
        
        # Check that backoff doesn't exceed max_backoff
        self.rate_limiter.domain_backoff_times["example.com"] = 6.0
        self.rate_limiter._increase_backoff("example.com")
        self.assertEqual(self.rate_limiter.domain_backoff_times["example.com"], 10.0)  # max_backoff

    def test_get_stats(self):
        """Test get_stats method."""
        # Make some requests
        self.rate_limiter.wait("https://example.com/page1")
        self.rate_limiter.wait("https://example.com/page2")
        self.rate_limiter.wait("https://test.com/page")
        
        # Report success and error
        self.rate_limiter.report_success("https://example.com/page")
        self.rate_limiter.report_error("https://test.com/page", "timeout")
        
        # Get stats
        stats = self.rate_limiter.get_stats()
        
        # Check stats
        self.assertIn("domains", stats)
        self.assertIn("config", stats)
        self.assertIn("stats", stats)
        
        # Check domain stats
        self.assertIn("example.com", stats["domains"])
        self.assertIn("test.com", stats["domains"])
        
        # Check request stats
        self.assertEqual(stats["stats"]["requests"]["example.com"], 2)
        self.assertEqual(stats["stats"]["requests"]["test.com"], 1)
        
        # Check error stats
        self.assertIn("test.com", stats["stats"]["errors"])
        self.assertEqual(stats["stats"]["errors"]["test.com"]["timeout"], 1)

    def test_reset(self):
        """Test reset method."""
        # Make some requests
        self.rate_limiter.wait("https://example.com/page")
        self.rate_limiter.wait("https://test.com/page")
        
        # Reset one domain
        self.rate_limiter.reset("example.com")
        
        # Check that domain was reset
        self.assertNotIn("example.com", self.rate_limiter.domain_rates)
        self.assertNotIn("example.com", self.rate_limiter.domain_last_request)
        
        # Check that other domain was not reset
        self.assertIn("test.com", self.rate_limiter.domain_rates)
        self.assertIn("test.com", self.rate_limiter.domain_last_request)
        
        # Reset all domains
        self.rate_limiter.reset()
        
        # Check that all domains were reset
        self.assertEqual(len(self.rate_limiter.domain_rates), 0)
        self.assertEqual(len(self.rate_limiter.domain_last_request), 0)


if __name__ == "__main__":
    unittest.main()
