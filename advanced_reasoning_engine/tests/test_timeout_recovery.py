"""
Tests for timeout and recovery mechanisms.

This module contains tests for the timeout handler and recovery handler.
"""

import time
import unittest
from unittest.mock import patch, MagicMock

import pytest

from deep_research_core.rl_tuning.environment.timeout_handler import TimeoutHandler, TimeoutError
from deep_research_core.rl_tuning.environment.recovery_handler import RecoveryHandler


class TestTimeoutHandler(unittest.TestCase):
    """Tests for the timeout handler."""

    def setUp(self):
        """Set up test fixtures."""
        self.timeout_handler = TimeoutHandler(
            default_timeout=1.0,
            adaptive_timeout=True,
            min_timeout=0.5,
            max_timeout=5.0,
            verbose=False
        )

    def test_get_timeout(self):
        """Test getting timeout for an action type."""
        # Default timeout for unknown action type
        timeout = self.timeout_handler.get_timeout("unknown")
        self.assertEqual(timeout, 1.0)
        
        # Update timeout for an action type
        self.timeout_handler.update_timeout("test_action", 0.8)
        timeout = self.timeout_handler.get_timeout("test_action")
        self.assertEqual(timeout, 0.8)
        
    def test_update_timeout(self):
        """Test updating timeout based on execution time."""
        # Update timeout with execution time
        new_timeout = self.timeout_handler.update_timeout("test_action", 0.7)
        self.assertGreaterEqual(new_timeout, 0.7)
        
        # Check that timeout was updated
        timeout = self.timeout_handler.get_timeout("test_action")
        self.assertGreaterEqual(timeout, 0.7)
        
        # Update with longer execution time
        new_timeout = self.timeout_handler.update_timeout("test_action", 1.2)
        self.assertGreaterEqual(new_timeout, 1.2)
        
        # Check that timeout was increased
        timeout = self.timeout_handler.get_timeout("test_action")
        self.assertGreaterEqual(timeout, 1.2)
        
    def test_execute_with_timeout_success(self):
        """Test executing a function with timeout (success case)."""
        # Define a test function that completes quickly
        def test_func():
            return "success"
            
        # Execute with timeout
        result, timed_out, execution_time = self.timeout_handler.execute_with_timeout(
            func=test_func,
            action_type="test_action",
            timeout=1.0
        )
        
        # Check results
        self.assertEqual(result, "success")
        self.assertFalse(timed_out)
        self.assertLess(execution_time, 1.0)
        
    def test_execute_with_timeout_timeout(self):
        """Test executing a function with timeout (timeout case)."""
        # Define a test function that takes too long
        def test_func():
            time.sleep(2.0)
            return "success"
            
        # Execute with timeout
        result, timed_out, execution_time = self.timeout_handler.execute_with_timeout(
            func=test_func,
            action_type="test_action",
            timeout=0.5
        )
        
        # Check results
        self.assertIsNone(result)
        self.assertTrue(timed_out)
        self.assertGreaterEqual(execution_time, 0.5)
        
    def test_execute_with_timeout_recovery(self):
        """Test executing a function with timeout and recovery."""
        # Define a test function that takes too long
        def test_func():
            time.sleep(2.0)
            return "success"
            
        # Define a recovery function
        def recovery_func():
            return "recovered"
            
        # Execute with timeout and recovery
        result, timed_out, execution_time = self.timeout_handler.execute_with_timeout(
            func=test_func,
            action_type="test_action",
            timeout=0.5,
            on_timeout=recovery_func
        )
        
        # Check results
        self.assertEqual(result, "recovered")
        self.assertTrue(timed_out)
        self.assertGreaterEqual(execution_time, 0.5)
        
    def test_timeout_decorator(self):
        """Test the timeout decorator."""
        # Define a test function with the timeout decorator
        @self.timeout_handler.timeout_decorator(action_type="test_action", timeout=0.5)
        def test_func():
            time.sleep(0.1)
            return "success"
            
        # Call the decorated function
        result = test_func()
        self.assertEqual(result, "success")
        
        # Define a function that will timeout
        @self.timeout_handler.timeout_decorator(action_type="test_action", timeout=0.5)
        def slow_func():
            time.sleep(1.0)
            return "success"
            
        # Call the decorated function that will timeout
        with self.assertRaises(TimeoutError):
            slow_func()
            
    def test_get_stats(self):
        """Test getting timeout statistics."""
        # Execute a function that times out
        def test_func():
            time.sleep(2.0)
            return "success"
            
        self.timeout_handler.execute_with_timeout(
            func=test_func,
            action_type="test_action",
            timeout=0.5
        )
        
        # Get stats
        stats = self.timeout_handler.get_stats()
        
        # Check stats
        self.assertEqual(stats["timeout_count"], 1)
        self.assertEqual(stats["timeout_recovery_count"], 0)
        self.assertEqual(stats["timeout_recovery_success_rate"], 0.0)
        
    def test_reset(self):
        """Test resetting the timeout handler."""
        # Execute a function that times out
        def test_func():
            time.sleep(2.0)
            return "success"
            
        self.timeout_handler.execute_with_timeout(
            func=test_func,
            action_type="test_action",
            timeout=0.5
        )
        
        # Reset the timeout handler
        self.timeout_handler.reset()
        
        # Get stats
        stats = self.timeout_handler.get_stats()
        
        # Check stats
        self.assertEqual(stats["timeout_count"], 0)
        self.assertEqual(stats["timeout_recovery_count"], 0)
        self.assertEqual(stats["timeout_recovery_success_rate"], 0.0)


class TestRecoveryHandler(unittest.TestCase):
    """Tests for the recovery handler."""

    def setUp(self):
        """Set up test fixtures."""
        self.recovery_handler = RecoveryHandler(
            max_recovery_attempts=3,
            use_llm_recovery=False,
            verbose=False
        )

    def test_recover_from_error_parameter_fix(self):
        """Test recovering from a parameter error."""
        # Create an error and context
        error = "Missing required parameter: 'file_path'"
        context = {
            "action": {
                "action_type": "use_tool",
                "tool_name": "read_file",
                "tool_input": {}
            },
            "recovery_attempts": 0
        }
        
        # Mock the _recover_using_parameter_fix method
        original_method = self.recovery_handler._recover_using_parameter_fix
        self.recovery_handler._recover_using_parameter_fix = MagicMock(return_value={
            "recovered": True,
            "action_taken": "added_missing_parameter",
            "missing_parameter": "file_path",
            "fixed_input": {"file_path": "default_file.txt"},
            "updated_action": {
                "action_type": "use_tool",
                "tool_name": "read_file",
                "tool_input": {"file_path": "default_file.txt"}
            }
        })
        
        # Recover from error
        recovery_info = self.recovery_handler.recover_from_error(
            error=error,
            context=context,
            recovery_methods=["parameter_fix"]
        )
        
        # Restore original method
        self.recovery_handler._recover_using_parameter_fix = original_method
        
        # Check recovery info
        self.assertTrue(recovery_info["recovered"])
        self.assertEqual(recovery_info["recovery_method"], "parameter_fix")
        self.assertEqual(recovery_info["action_taken"], "added_missing_parameter")
        self.assertEqual(recovery_info["missing_parameter"], "file_path")
        self.assertEqual(recovery_info["fixed_input"], {"file_path": "default_file.txt"})
        
    def test_recover_from_error_type_conversion(self):
        """Test recovering from a type error."""
        # Create an error and context
        error = "Expected type 'int' for parameter 'count'"
        context = {
            "action": {
                "action_type": "use_tool",
                "tool_name": "list_items",
                "tool_input": {"count": "10"}
            },
            "recovery_attempts": 0
        }
        
        # Mock the _recover_using_type_conversion method
        original_method = self.recovery_handler._recover_using_type_conversion
        self.recovery_handler._recover_using_type_conversion = MagicMock(return_value={
            "recovered": True,
            "action_taken": "converted_parameter_type",
            "parameter": "count",
            "original_value": "10",
            "original_type": "str",
            "converted_value": 10,
            "converted_type": "int",
            "updated_action": {
                "action_type": "use_tool",
                "tool_name": "list_items",
                "tool_input": {"count": 10}
            }
        })
        
        # Recover from error
        recovery_info = self.recovery_handler.recover_from_error(
            error=error,
            context=context,
            recovery_methods=["type_conversion"]
        )
        
        # Restore original method
        self.recovery_handler._recover_using_type_conversion = original_method
        
        # Check recovery info
        self.assertTrue(recovery_info["recovered"])
        self.assertEqual(recovery_info["recovery_method"], "type_conversion")
        self.assertEqual(recovery_info["action_taken"], "converted_parameter_type")
        self.assertEqual(recovery_info["parameter"], "count")
        self.assertEqual(recovery_info["original_value"], "10")
        self.assertEqual(recovery_info["converted_value"], 10)
        
    def test_recover_from_error_format_fix(self):
        """Test recovering from a format error."""
        # Create an error and context
        error = "Invalid JSON format for parameter 'data'"
        context = {
            "action": {
                "action_type": "use_tool",
                "tool_name": "process_data",
                "tool_input": {"data": "{name: 'test'}"}
            },
            "recovery_attempts": 0
        }
        
        # Mock the _recover_using_format_fix method
        original_method = self.recovery_handler._recover_using_format_fix
        self.recovery_handler._recover_using_format_fix = MagicMock(return_value={
            "recovered": True,
            "action_taken": "fixed_json_format",
            "parameter": "data",
            "original_value": "{name: 'test'}",
            "fixed_value": {"name": "test"},
            "updated_action": {
                "action_type": "use_tool",
                "tool_name": "process_data",
                "tool_input": {"data": {"name": "test"}}
            }
        })
        
        # Recover from error
        recovery_info = self.recovery_handler.recover_from_error(
            error=error,
            context=context,
            recovery_methods=["format_fix"]
        )
        
        # Restore original method
        self.recovery_handler._recover_using_format_fix = original_method
        
        # Check recovery info
        self.assertTrue(recovery_info["recovered"])
        self.assertEqual(recovery_info["recovery_method"], "format_fix")
        self.assertEqual(recovery_info["action_taken"], "fixed_json_format")
        self.assertEqual(recovery_info["parameter"], "data")
        self.assertEqual(recovery_info["original_value"], "{name: 'test'}")
        self.assertEqual(recovery_info["fixed_value"], {"name": "test"})
        
    def test_recover_from_error_retry(self):
        """Test recovering by retrying."""
        # Create an error and context
        error = "Temporary error"
        context = {
            "action": {
                "action_type": "use_tool",
                "tool_name": "api_call",
                "tool_input": {"endpoint": "test"}
            },
            "recovery_attempts": 0,
            "max_retries": 3
        }
        
        # Mock the _recover_using_retry method
        original_method = self.recovery_handler._recover_using_retry
        self.recovery_handler._recover_using_retry = MagicMock(return_value={
            "recovered": True,
            "action_taken": "simple_retry",
            "retry_count": 1,
            "max_retries": 3,
            "updated_action": {
                "action_type": "use_tool",
                "tool_name": "api_call",
                "tool_input": {"endpoint": "test"}
            },
            "updated_context": {
                "action": {
                    "action_type": "use_tool",
                    "tool_name": "api_call",
                    "tool_input": {"endpoint": "test"}
                },
                "recovery_attempts": 0,
                "max_retries": 3,
                "retry_count": 1
            }
        })
        
        # Recover from error
        recovery_info = self.recovery_handler.recover_from_error(
            error=error,
            context=context,
            recovery_methods=["retry"]
        )
        
        # Restore original method
        self.recovery_handler._recover_using_retry = original_method
        
        # Check recovery info
        self.assertTrue(recovery_info["recovered"])
        self.assertEqual(recovery_info["recovery_method"], "retry")
        self.assertEqual(recovery_info["action_taken"], "simple_retry")
        self.assertEqual(recovery_info["retry_count"], 1)
        self.assertEqual(recovery_info["max_retries"], 3)
        
    def test_recover_from_error_max_attempts(self):
        """Test recovering with maximum attempts reached."""
        # Create an error and context with max attempts reached
        error = "Persistent error"
        context = {
            "action": {
                "action_type": "use_tool",
                "tool_name": "api_call",
                "tool_input": {"endpoint": "test"}
            },
            "recovery_attempts": 3
        }
        
        # Recover from error
        recovery_info = self.recovery_handler.recover_from_error(
            error=error,
            context=context
        )
        
        # Check recovery info
        self.assertFalse(recovery_info["recovered"])
        self.assertTrue(recovery_info["max_attempts_reached"])
        
    def test_get_stats(self):
        """Test getting recovery statistics."""
        # Mock a successful recovery
        self.recovery_handler.recovery_attempts = 5
        self.recovery_handler.recovery_successes = 3
        self.recovery_handler.recovery_failures = 2
        self.recovery_handler.recovery_methods = {
            "parameter_fix": 1,
            "type_conversion": 1,
            "retry": 1
        }
        
        # Get stats
        stats = self.recovery_handler.get_stats()
        
        # Check stats
        self.assertEqual(stats["recovery_attempts"], 5)
        self.assertEqual(stats["recovery_successes"], 3)
        self.assertEqual(stats["recovery_failures"], 2)
        self.assertEqual(stats["success_rate"], 0.6)
        self.assertEqual(stats["recovery_methods"]["parameter_fix"], 1)
        self.assertEqual(stats["recovery_methods"]["type_conversion"], 1)
        self.assertEqual(stats["recovery_methods"]["retry"], 1)
        
    def test_reset(self):
        """Test resetting the recovery handler."""
        # Mock a successful recovery
        self.recovery_handler.recovery_attempts = 5
        self.recovery_handler.recovery_successes = 3
        self.recovery_handler.recovery_failures = 2
        self.recovery_handler.recovery_methods = {
            "parameter_fix": 1,
            "type_conversion": 1,
            "retry": 1
        }
        
        # Reset the recovery handler
        self.recovery_handler.reset()
        
        # Get stats
        stats = self.recovery_handler.get_stats()
        
        # Check stats
        self.assertEqual(stats["recovery_attempts"], 0)
        self.assertEqual(stats["recovery_successes"], 0)
        self.assertEqual(stats["recovery_failures"], 0)
        self.assertEqual(stats["success_rate"], 0.0)
        self.assertEqual(stats["recovery_methods"], {})


if __name__ == "__main__":
    unittest.main()
