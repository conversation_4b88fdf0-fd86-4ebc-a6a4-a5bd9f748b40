import unittest
from unittest.mock import patch, MagicMock
import json
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.deep_research_core.agents.result_analyzer import ResultAnalyzer

class TestResultAnalyzer(unittest.TestCase):
    """Test cases for ResultAnalyzer."""

    def setUp(self):
        """Set up test fixtures."""
        self.analyzer = ResultAnalyzer()
        
        # Sample search results for testing
        self.sample_results = {
            "success": True,
            "query": "test query",
            "results": [
                {
                    "url": "https://example.edu/research",
                    "title": "Example Research",
                    "snippet": "This is a research paper about testing.",
                    "content": "This is a detailed research paper about testing methodology with scientific evidence and data."
                },
                {
                    "url": "https://blog.example.com/opinion",
                    "title": "My Opinion on Testing",
                    "snippet": "I believe testing is important.",
                    "content": "I strongly believe testing is incredibly important. Everyone should do more testing!"
                },
                {
                    "url": "https://gov.example.org/statistics",
                    "title": "Testing Statistics",
                    "snippet": "Official statistics on testing.",
                    "content": "According to official data, 75% of projects benefit from testing. Studies show increased reliability."
                }
            ]
        }

    def test_analyze_results_sequential(self):
        """Test analyze_results method with sequential processing."""
        results = self.analyzer.analyze_results(self.sample_results, parallel=False)
        
        # Check basic structure
        self.assertTrue(results["success"])
        self.assertEqual(results["original_query"], "test query")
        self.assertEqual(results["analyzed_count"], 3)
        
        # Check analysis results
        analysis = results["analysis"]
        self.assertIn("overall_factuality_score", analysis)
        self.assertIn("overall_sentiment_score", analysis)
        self.assertEqual(len(analysis["results_analysis"]), 3)
        
        # Check sentiment and factuality distributions
        self.assertIn("sentiment_distribution", analysis)
        self.assertIn("factuality_distribution", analysis)

    @patch('concurrent.futures.ThreadPoolExecutor')
    def test_analyze_results_parallel(self, mock_executor):
        """Test analyze_results method with parallel processing."""
        # Mock the executor
        mock_context = MagicMock()
        mock_executor.return_value.__enter__.return_value = mock_context
        
        # Create mock futures
        future1, future2, future3 = MagicMock(), MagicMock(), MagicMock()
        mock_context.submit.side_effect = [future1, future2, future3]
        
        # Set up future results
        future1.result.return_value = {
            "domain": "example.edu",
            "domain_credibility_score": 0.9,
            "factuality_score": 0.85,
            "factuality_level": "high",
            "sentiment": "neutral",
            "sentiment_score": 0.1
        }
        
        future2.result.return_value = {
            "domain": "blog.example.com",
            "domain_credibility_score": 0.5,
            "factuality_score": 0.4,
            "factuality_level": "medium",
            "sentiment": "positive",
            "sentiment_score": 0.7
        }
        
        future3.result.return_value = {
            "domain": "gov.example.org",
            "domain_credibility_score": 0.85,
            "factuality_score": 0.75,
            "factuality_level": "high",
            "sentiment": "neutral",
            "sentiment_score": 0.0
        }
        
        # Mock as_completed to return our futures in order
        with patch('concurrent.futures.as_completed', return_value=[future1, future2, future3]):
            results = self.analyzer.analyze_results(self.sample_results, parallel=True)
        
        # Verify executor was called correctly
        mock_executor.assert_called_once()
        self.assertEqual(mock_context.submit.call_count, 3)
        
        # Check results
        self.assertTrue(results["success"])
        analysis = results["analysis"]
        self.assertEqual(len(analysis["results_analysis"]), 3)
        
        # Verify distributions were updated
        self.assertEqual(analysis["sentiment_distribution"]["positive"], 1)
        self.assertEqual(analysis["sentiment_distribution"]["neutral"], 2)
        self.assertEqual(analysis["factuality_distribution"]["high"], 2)
        self.assertEqual(analysis["factuality_distribution"]["medium"], 1)

    def test_extract_domain_info_with_tldextract(self):
        """Test domain extraction with tldextract."""
        with patch('src.deep_research_core.agents.result_analyzer.TLDEXTRACT_AVAILABLE', True):
            with patch('tldextract.extract') as mock_extract:
                # Mock tldextract.extract result
                extract_result = MagicMock()
                extract_result.subdomain = "www"
                extract_result.domain = "example"
                extract_result.suffix = "com"
                mock_extract.return_value = extract_result
                
                # Call the method
                domain_info = self.analyzer._extract_domain_info("https://www.example.com/path")
                
                # Check results
                self.assertEqual(domain_info["domain"], "example")
                self.assertEqual(domain_info["full_domain"], "www.example")
                self.assertEqual(domain_info["full_domain_with_suffix"], "www.example.com")
                self.assertEqual(domain_info["tld"], "com")
                self.assertFalse(domain_info["is_academic"])
                self.assertFalse(domain_info["is_government"])
                self.assertFalse(domain_info["is_organization"])

    def test_extract_domain_info_without_tldextract(self):
        """Test fallback domain extraction without tldextract."""
        with patch('src.deep_research_core.agents.result_analyzer.TLDEXTRACT_AVAILABLE', False):
            # Call the method
            domain_info = self.analyzer._extract_domain_info("https://www.example.com/path")
            
            # Check results
            self.assertEqual(domain_info["domain"], "www.example.com")
            self.assertEqual(domain_info["full_domain"], "www.example.com")
            self.assertEqual(domain_info["full_domain_with_suffix"], "www.example.com")
            
            # Test academic domain detection in fallback mode
            domain_info = self.analyzer._extract_domain_info("https://university.edu/research")
            self.assertTrue(domain_info["is_academic"])
            
            # Test government domain detection in fallback mode
            domain_info = self.analyzer._extract_domain_info("https://example.gov/data")
            self.assertTrue(domain_info["is_government"])
            
            # Test organization domain detection in fallback mode
            domain_info = self.analyzer._extract_domain_info("https://example.org/about")
            self.assertTrue(domain_info["is_organization"])

    def test_get_factuality_metrics(self):
        """Test getting factuality metrics from analysis results."""
        # Create sample analysis results
        analysis_results = {
            "success": True,
            "original_query": "test query",
            "analyzed_count": 3,
            "analysis": {
                "overall_factuality_score": 0.75,
                "factuality_distribution": {"high": 2, "medium": 1, "low": 0},
                "domain_credibility": {
                    "example.edu": 0.9,
                    "blog.example.com": 0.5,
                    "gov.example.org": 0.85
                }
            }
        }
        
        # Get metrics
        metrics = self.analyzer.get_factuality_metrics(analysis_results)
        
        # Check results
        self.assertEqual(metrics["overall_factuality_score"], 75)  # Converted to percentage
        self.assertEqual(metrics["factuality_level"], "high")
        self.assertGreaterEqual(len(metrics["most_credible_sources"]), 1)
        
        # Check that domains are sorted correctly
        if len(metrics["most_credible_sources"]) >= 2:
            first_source = metrics["most_credible_sources"][0]
            second_source = metrics["most_credible_sources"][1]
            self.assertGreaterEqual(first_source["score"], second_source["score"])

    def test_get_sentiment_metrics(self):
        """Test getting sentiment metrics from analysis results."""
        # Create sample analysis results
        analysis_results = {
            "success": True,
            "original_query": "test query",
            "analyzed_count": 3,
            "analysis": {
                "overall_sentiment_score": 0.3,
                "sentiment_distribution": {"positive": 2, "neutral": 1, "negative": 0},
                "results_analysis": [
                    {
                        "domain": "example.edu",
                        "sentiment": "positive",
                        "sentiment_score": 0.4
                    },
                    {
                        "domain": "blog.example.com",
                        "sentiment": "positive",
                        "sentiment_score": 0.6
                    },
                    {
                        "domain": "gov.example.org",
                        "sentiment": "neutral",
                        "sentiment_score": 0.1
                    }
                ]
            }
        }
        
        # Get metrics
        metrics = self.analyzer.get_sentiment_metrics(analysis_results)
        
        # Check results
        self.assertEqual(metrics["overall_sentiment"], "positive")
        self.assertEqual(metrics["sentiment_score"], 30)  # Converted to percentage
        self.assertEqual(len(metrics["sentiment_by_source"]), 3)
        
        # Check distribution
        self.assertEqual(metrics["sentiment_distribution"]["positive"], 2)
        self.assertEqual(metrics["sentiment_distribution"]["neutral"], 1)
        self.assertEqual(metrics["sentiment_distribution"]["negative"], 0)

if __name__ == '__main__':
    unittest.main() 