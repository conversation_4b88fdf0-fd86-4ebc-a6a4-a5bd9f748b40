"""
Tests for Vietnamese RL Adapter.

This module tests the VietnameseRLAdapter class for integrating Vietnamese language support
with RL frameworks.
"""

import unittest
from unittest.mock import MagicMock, patch
import torch
from typing import Dict, Any

from deep_research_core.rl_tuning.frameworks.vietnamese_adapter import VietnameseRLAdapter


class TestVietnameseRLAdapter(unittest.TestCase):
    """Test cases for Vietnamese RL Adapter."""

    def setUp(self):
        """Set up test environment."""
        # Create mock framework
        self.mock_framework = MagicMock()
        self.mock_framework.train = MagicMock(return_value={"loss": 0.1})
        self.mock_framework.predict = MagicMock(return_value="Test response")
        
        # Create mock Vietnamese support
        self.patch_vietnamese_support = patch('deep_research_core.rl_tuning.environment.vietnamese_support.VietnameseSupport')
        self.mock_vietnamese_support = self.patch_vietnamese_support.start()
        self.mock_vietnamese_support_instance = MagicMock()
        self.mock_vietnamese_support_instance.is_vietnamese = MagicMock(return_value=True)
        self.mock_vietnamese_support_instance.translate_to_vietnamese = MagicMock(return_value="[VI] Test text")
        self.mock_vietnamese_support_instance.enhance_vietnamese_response = MagicMock(return_value="Enhanced response")
        self.mock_vietnamese_support.return_value = self.mock_vietnamese_support_instance
        
        # Create mock Vietnamese model support
        self.patch_vietnamese_model_support = patch('deep_research_core.rl_tuning.model_paradigm.vietnamese_support.VietnameseModelSupport')
        self.mock_vietnamese_model_support = self.patch_vietnamese_model_support.start()
        self.mock_vietnamese_model_support_instance = MagicMock()
        self.mock_vietnamese_model_support_instance.get_embeddings = MagicMock(return_value=torch.zeros((1, 768)))
        self.mock_vietnamese_model_support_instance.get_prompt_template = MagicMock(return_value="Vietnamese prompt template")
        self.mock_vietnamese_model_support.return_value = self.mock_vietnamese_model_support_instance
        
        # Create mock Vietnamese utils
        self.patch_detect_vietnamese = patch('deep_research_core.utils.vietnamese_utils.detect_vietnamese')
        self.mock_detect_vietnamese = self.patch_detect_vietnamese.start()
        self.mock_detect_vietnamese.return_value = True
        
        self.patch_translate_vietnamese = patch('deep_research_core.utils.vietnamese_utils.translate_to_vietnamese')
        self.mock_translate_vietnamese = self.patch_translate_vietnamese.start()
        self.mock_translate_vietnamese.return_value = "[VI] Translated text"
        
        self.patch_adapt_prompt = patch('deep_research_core.utils.vietnamese_utils.adapt_prompt_for_vietnamese')
        self.mock_adapt_prompt = self.patch_adapt_prompt.start()
        self.mock_adapt_prompt.return_value = "Adapted prompt"
        
        # Create adapter
        self.adapter = VietnameseRLAdapter(
            framework_type="verl",
            framework_instance=self.mock_framework,
            config={
                "enable_vietnamese": True,
                "translate_prompts": True,
                "adapt_prompts": True,
                "vietnamese_ratio": 0.5,
                "bilingual_training": True,
                "dialect": "northern",
                "embedding_model": "phobert"
            },
            device="cpu",
            verbose=True
        )

    def tearDown(self):
        """Clean up after tests."""
        self.patch_vietnamese_support.stop()
        self.patch_vietnamese_model_support.stop()
        self.patch_detect_vietnamese.stop()
        self.patch_translate_vietnamese.stop()
        self.patch_adapt_prompt.stop()

    def test_initialization(self):
        """Test initialization of Vietnamese RL Adapter."""
        self.assertEqual(self.adapter.framework_type, "verl")
        self.assertEqual(self.adapter.framework, self.mock_framework)
        self.assertEqual(self.adapter.device, "cpu")
        self.assertTrue(self.adapter.verbose)
        
        # Check config
        self.assertTrue(self.adapter.config["enable_vietnamese"])
        self.assertTrue(self.adapter.config["translate_prompts"])
        self.assertTrue(self.adapter.config["adapt_prompts"])
        self.assertEqual(self.adapter.config["vietnamese_ratio"], 0.5)
        self.assertTrue(self.adapter.config["bilingual_training"])
        self.assertEqual(self.adapter.config["dialect"], "northern")
        self.assertEqual(self.adapter.config["embedding_model"], "phobert")
        
        # Check support instances
        self.assertIsNotNone(self.adapter.vietnamese_model_support)
        self.assertIsNotNone(self.adapter.vietnamese_env_support)

    def test_process_batch(self):
        """Test processing a batch of data."""
        # Create test batch
        batch = {
            "prompts": [
                "Explain photosynthesis.",
                "Tại sao bầu trời có màu xanh?"
            ],
            "responses": [
                "Photosynthesis is a process...",
                "Bầu trời có màu xanh vì..."
            ]
        }
        
        # Process batch
        processed_batch = self.adapter.process_batch(batch)
        
        # Check that batch was processed
        self.assertIsNotNone(processed_batch)
        self.assertEqual(len(processed_batch["prompts"]), 2)
        self.assertEqual(len(processed_batch["responses"]), 2)
        
        # Check that Vietnamese detection was called
        self.mock_vietnamese_support_instance.is_vietnamese.assert_called()
        
        # Check that adapt_prompt was called
        self.mock_adapt_prompt.assert_called()

    def test_create_vietnamese_prompt(self):
        """Test creating a Vietnamese prompt."""
        # Create Vietnamese prompt
        prompt = "Explain the process of photosynthesis in plants."
        vietnamese_prompt = self.adapter.create_vietnamese_prompt(prompt, algorithm="ppo")
        
        # Check that prompt was created
        self.assertIsNotNone(vietnamese_prompt)
        
        # Check that translate_to_vietnamese was called
        self.mock_translate_vietnamese.assert_called_with(prompt)
        
        # Check that adapt_prompt was called
        self.mock_adapt_prompt.assert_called()

    def test_process_vietnamese_response(self):
        """Test processing a Vietnamese response."""
        # Process Vietnamese response
        response = "Quá trình quang hợp là quá trình thực vật sử dụng năng lượng ánh sáng."
        processed_response = self.adapter.process_vietnamese_response(response)
        
        # Check that response was processed
        self.assertIsNotNone(processed_response)
        
        # Check that enhance_vietnamese_response was called
        self.mock_vietnamese_support_instance.enhance_vietnamese_response.assert_called_with(response)

    def test_is_vietnamese_batch(self):
        """Test checking if a batch contains Vietnamese text."""
        # Create test batch
        batch = {
            "inputs": [
                "Explain photosynthesis.",
                "Tại sao bầu trời có màu xanh?"
            ]
        }
        
        # Check if batch contains Vietnamese text
        is_vietnamese = self.adapter.is_vietnamese_batch(batch)
        
        # Check that is_vietnamese was called
        self.mock_vietnamese_support_instance.is_vietnamese.assert_called()
        
        # Check result
        self.assertTrue(is_vietnamese)

    def test_create_bilingual_batch(self):
        """Test creating a bilingual batch."""
        # Create test batch
        batch = {
            "inputs": [
                "Explain photosynthesis.",
                "Why is the sky blue?"
            ]
        }
        
        # Create bilingual batch
        bilingual_batch = self.adapter.create_bilingual_batch(batch)
        
        # Check that batch was created
        self.assertIsNotNone(bilingual_batch)
        self.assertEqual(len(bilingual_batch["inputs"]), 2)
        
        # Check that translate_to_vietnamese was called
        self.mock_vietnamese_support_instance.translate_to_vietnamese.assert_called()

    def test_adapt_model_for_vietnamese(self):
        """Test adapting a model for Vietnamese."""
        # Create mock model
        mock_model = MagicMock()
        mock_model.tokenizer = MagicMock()
        mock_model.tokenizer.add_special_tokens = MagicMock(return_value=4)
        mock_model.model = MagicMock()
        mock_model.model.resize_token_embeddings = MagicMock()
        
        # Adapt model
        adapted_model = self.adapter.adapt_model_for_vietnamese(mock_model)
        
        # Check that model was adapted
        self.assertIsNotNone(adapted_model)
        
        # Check that add_special_tokens was called
        mock_model.tokenizer.add_special_tokens.assert_called()
        
        # Check that resize_token_embeddings was called
        mock_model.model.resize_token_embeddings.assert_called()

    def test_framework_specific_adaptations(self):
        """Test framework-specific adaptations."""
        # Test Verl-specific adaptations
        verl_adapter = VietnameseRLAdapter(
            framework_type="verl",
            framework_instance=self.mock_framework,
            config={"enable_vietnamese": True},
            device="cpu"
        )
        
        # Check that Verl-specific adaptations were applied
        self.assertEqual(verl_adapter.framework_type, "verl")
        
        # Test TinyZero-specific adaptations
        tinyzero_adapter = VietnameseRLAdapter(
            framework_type="tinyzero",
            framework_instance=self.mock_framework,
            config={"enable_vietnamese": True},
            device="cpu"
        )
        
        # Check that TinyZero-specific adaptations were applied
        self.assertEqual(tinyzero_adapter.framework_type, "tinyzero")
        
        # Test OpenR1-specific adaptations
        openr1_adapter = VietnameseRLAdapter(
            framework_type="openr1",
            framework_instance=self.mock_framework,
            config={"enable_vietnamese": True},
            device="cpu"
        )
        
        # Check that OpenR1-specific adaptations were applied
        self.assertEqual(openr1_adapter.framework_type, "openr1")
        
        # Test Trlx-specific adaptations
        trlx_adapter = VietnameseRLAdapter(
            framework_type="trlx",
            framework_instance=self.mock_framework,
            config={"enable_vietnamese": True},
            device="cpu"
        )
        
        # Check that Trlx-specific adaptations were applied
        self.assertEqual(trlx_adapter.framework_type, "trlx")


if __name__ == "__main__":
    unittest.main()
