"""
Tests for the QA evaluator module.

This module contains tests for the QA evaluator, which is used to evaluate
the quality of answers generated by RAG systems.
"""

import os
import json
import unittest
from unittest.mock import patch, MagicMock

import pytest
import numpy as np

from deep_research_core.evaluation.qa_evaluator import QAEvaluator


class TestQAEvaluator(unittest.TestCase):
    """Tests for the QA evaluator."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a mock evaluator with mocked provider
        self.evaluator = QAEvaluator(
            provider="openai",
            model="gpt-4o",
            use_model_evaluation=True,
            use_heuristics=True,
            language="en"
        )
        
        # Sample data for testing
        self.question = "What is the capital of France?"
        self.answer = "The capital of France is Paris."
        self.documents = [
            {
                "id": "doc1",
                "title": "France",
                "content": "France is a country in Western Europe. Its capital is Paris, which is known for the Eiffel Tower."
            },
            {
                "id": "doc2",
                "title": "European Capitals",
                "content": "Paris is the capital of France. London is the capital of the United Kingdom."
            }
        ]
        self.expected_answer = "Paris is the capital of France."
        
        # Mock response for model evaluation
        self.mock_evaluation = {
            "relevance": {
                "score": 9.0,
                "explanation": "The answer directly addresses the question about the capital of France."
            },
            "factual_accuracy": {
                "score": 10.0,
                "explanation": "The answer correctly states that Paris is the capital of France, which is supported by the documents."
            },
            "completeness": {
                "score": 8.0,
                "explanation": "The answer provides the basic information requested but could include more details about Paris."
            },
            "coherence": {
                "score": 9.0,
                "explanation": "The answer is clear and well-structured."
            },
            "conciseness": {
                "score": 10.0,
                "explanation": "The answer is concise and to the point."
            },
            "source_attribution": {
                "score": 7.0,
                "explanation": "The answer does not explicitly cite the sources."
            },
            "overall": {
                "score": 8.8,
                "explanation": "Overall, the answer is accurate, relevant, and well-structured, but could improve on source attribution."
            }
        }

    @patch('deep_research_core.models.api.openai.openai_provider.generate')
    def test_model_evaluation(self, mock_generate):
        """Test model-based evaluation."""
        # Mock the API response
        mock_generate.return_value = json.dumps(self.mock_evaluation)
        
        # Call the _model_evaluation method
        result = self.evaluator._model_evaluation(
            self.question, self.answer, self.documents
        )
        
        # Check that the API was called with the correct parameters
        mock_generate.assert_called_once()
        
        # Check that the result matches the expected evaluation
        self.assertEqual(result, self.mock_evaluation)
        
    def test_heuristic_evaluation(self):
        """Test heuristic-based evaluation."""
        # Call the _heuristic_evaluation method
        result = self.evaluator._heuristic_evaluation(
            self.question, self.answer, self.documents, self.expected_answer
        )
        
        # Check that the result contains the expected metrics
        self.assertIn("relevance", result)
        self.assertIn("source_attribution", result)
        self.assertIn("factual_accuracy", result)
        self.assertIn("completeness", result)
        
        # Check that the scores are within the expected range
        for metric, data in result.items():
            self.assertGreaterEqual(data["score"], 0.0)
            self.assertLessEqual(data["score"], 10.0)
            self.assertIn("explanation", data)
            
    @patch('deep_research_core.models.api.openai.openai_provider.generate')
    def test_evaluate(self, mock_generate):
        """Test the evaluate method."""
        # Mock the API response
        mock_generate.return_value = json.dumps(self.mock_evaluation)
        
        # Call the evaluate method
        result = self.evaluator.evaluate(
            self.question, self.answer, self.documents, self.expected_answer
        )
        
        # Check that the result contains the expected fields
        self.assertIn("question", result)
        self.assertIn("answer", result)
        self.assertIn("documents_count", result)
        self.assertIn("metrics", result)
        self.assertIn("overall_score", result)
        self.assertIn("evaluation_time", result)
        
        # Check that the metrics contain the expected fields
        for metric, data in result["metrics"].items():
            self.assertIn("score", data)
            self.assertIn("explanation", data)
            
        # Check that the overall score is within the expected range
        self.assertGreaterEqual(result["overall_score"], 0.0)
        self.assertLessEqual(result["overall_score"], 10.0)
        
    def test_evaluate_relevance(self):
        """Test the _evaluate_relevance method."""
        # Call the _evaluate_relevance method
        score = self.evaluator._evaluate_relevance(
            self.question, self.answer, self.documents
        )
        
        # Check that the score is within the expected range
        self.assertGreaterEqual(score, 0.0)
        self.assertLessEqual(score, 10.0)
        
    def test_evaluate_source_attribution(self):
        """Test the _evaluate_source_attribution method."""
        # Call the _evaluate_source_attribution method
        score = self.evaluator._evaluate_source_attribution(
            self.answer, self.documents
        )
        
        # Check that the score is within the expected range
        self.assertGreaterEqual(score, 0.0)
        self.assertLessEqual(score, 10.0)
        
    def test_evaluate_factual_accuracy(self):
        """Test the _evaluate_factual_accuracy method."""
        # Call the _evaluate_factual_accuracy method
        score = self.evaluator._evaluate_factual_accuracy(
            self.answer, self.expected_answer, self.documents
        )
        
        # Check that the score is within the expected range
        self.assertGreaterEqual(score, 0.0)
        self.assertLessEqual(score, 10.0)
        
    def test_evaluate_completeness(self):
        """Test the _evaluate_completeness method."""
        # Call the _evaluate_completeness method
        score = self.evaluator._evaluate_completeness(
            self.question, self.answer, self.documents
        )
        
        # Check that the score is within the expected range
        self.assertGreaterEqual(score, 0.0)
        self.assertLessEqual(score, 10.0)
        
    @patch('deep_research_core.models.api.openai.openai_provider.generate')
    def test_vietnamese_evaluation(self, mock_generate):
        """Test evaluation with Vietnamese content."""
        # Create a Vietnamese evaluator
        vi_evaluator = QAEvaluator(
            provider="openai",
            model="gpt-4o",
            language="vi"
        )
        
        # Vietnamese test data
        vi_question = "Thủ đô của Pháp là gì?"
        vi_answer = "Thủ đô của Pháp là Paris."
        vi_documents = [
            {
                "id": "doc1",
                "title": "Pháp",
                "content": "Pháp là một quốc gia ở Tây Âu. Thủ đô của Pháp là Paris, nổi tiếng với tháp Eiffel."
            }
        ]
        
        # Mock the API response
        mock_generate.return_value = json.dumps(self.mock_evaluation)
        
        # Call the evaluate method
        result = vi_evaluator.evaluate(
            vi_question, vi_answer, vi_documents
        )
        
        # Check that the API was called with the correct parameters
        mock_generate.assert_called_once()
        
        # Check that the result contains the expected fields
        self.assertIn("question", result)
        self.assertIn("answer", result)
        self.assertIn("metrics", result)
        
        # Check that the Vietnamese prompt was used
        call_args = mock_generate.call_args[1]
        self.assertIn("Câu hỏi:", call_args["prompt"])


if __name__ == "__main__":
    unittest.main()
