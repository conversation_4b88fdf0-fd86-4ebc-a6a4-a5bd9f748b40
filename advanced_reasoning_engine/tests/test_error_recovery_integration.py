"""
Tests for the integration of error recovery with ToT and RAG.

This module contains tests for the integration of error recovery with
Tree of Thought (ToT) and Retrieval-Augmented Generation (RAG).
"""

import os
import sys
import unittest
import time
import random
from unittest.mock import MagicMock, patch

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), ".."))

from src.deep_research_core.reasoning.tot import TreeOfThought
from src.deep_research_core.rag.sqlite_vector_rag import SQLiteVectorRAG
from src.deep_research_core.rag.error_recovery_rag import ErrorRecoveryRAG
from src.deep_research_core.utils.reasoning_error_recovery import ReasoningErrorRecovery
from src.deep_research_core.models.base import BaseModel


class ErrorProneModel(BaseModel):
    """
    A model that deliberately fails sometimes.
    
    This model is used to demonstrate the error recovery system.
    """
    
    def __init__(self, failure_rate: float = 0.5, **kwargs):
        """
        Initialize the error-prone model.
        
        Args:
            failure_rate: Probability of failure (0.0 to 1.0)
            **kwargs: Additional arguments to pass to the parent class
        """
        super().__init__(**kwargs)
        self.failure_rate = failure_rate
        self.call_count = 0
        self.name = "ErrorProneModel"
        self.temperature = 0.7
        self.max_tokens = 1000
    
    def generate(
        self,
        prompt: str,
        system_prompt: str = None,
        temperature: float = None,
        max_tokens: int = None,
        stream: bool = False,
        **kwargs
    ) -> str:
        """
        Generate text, with deliberate failures.
        
        Args:
            prompt: The prompt to generate from
            system_prompt: The system prompt
            temperature: The temperature
            max_tokens: The maximum number of tokens
            stream: Whether to stream the response
            **kwargs: Additional arguments
            
        Returns:
            Generated text
            
        Raises:
            Various exceptions to test error recovery
        """
        self.call_count += 1
        
        # Simulate different types of errors
        if random.random() < self.failure_rate:
            error_type = random.choice([
                "timeout", "connection", "rate_limit", "invalid_request", 
                "server_error", "authentication", "context_length"
            ])
            
            if error_type == "timeout":
                raise TimeoutError("Request timed out after 10 seconds")
            elif error_type == "connection":
                raise ConnectionError("Failed to connect to API endpoint")
            elif error_type == "invalid_request":
                raise ValueError("Invalid request parameters")
            elif error_type == "server_error":
                raise Exception("Server error: 500 Internal Server Error")
            elif error_type == "authentication":
                raise PermissionError("Authentication failed")
            elif error_type == "rate_limit":
                raise Exception("Rate limit exceeded")
            elif error_type == "context_length":
                raise ValueError("Maximum context length exceeded")
        
        # If no error, generate a simple response
        if "Generate" in prompt and "different" in prompt and ("approaches" in prompt or "next steps" in prompt):
            # Generate multiple thought branches
            num_branches = 3
            if "next steps" in prompt:
                return (
                    "1. Analyze the problem from a different angle\n"
                    "Let's consider this from a mathematical perspective instead of a logical one.\n\n"
                    "2. Apply a specific technique\n"
                    "I'll try using the divide and conquer approach to break this down further.\n\n"
                    "3. Consider edge cases\n"
                    "What if we examine the boundary conditions of this problem?"
                )
            else:
                return (
                    "1. First approach: Mathematical analysis\n"
                    "I'll start by formalizing the problem mathematically and identifying key variables.\n\n"
                    "2. Second approach: Analogical reasoning\n"
                    "I'll compare this problem to similar ones I've seen before and adapt those solutions.\n\n"
                    "3. Third approach: First principles thinking\n"
                    "I'll break down the problem to its fundamental components and build up a solution."
                )
        elif "evaluate" in prompt.lower() and "path" in prompt.lower():
            # Evaluation prompt
            return (
                "Path 1: Score 8/10\n"
                "This path is very promising because it breaks down the problem systematically and considers multiple angles.\n\n"
                "Path 2: Score 6/10\n"
                "This approach is reasonable but doesn't fully address some key aspects of the problem.\n\n"
                "Path 3: Score 9/10\n"
                "This is the most promising approach as it's comprehensive, logical, and addresses potential edge cases."
            )
        elif "continue" in prompt.lower() and "conclusion" in prompt.lower():
            # Expansion prompt
            return (
                "Building on the previous reasoning, I can now see that the key insight is to recognize the pattern in the data.\n\n"
                "When we apply this pattern recognition, we can determine that the underlying principle is one of exponential growth rather than linear progression.\n\n"
                "Therefore, the answer is that we should expect the system to double in size every 3.5 time units, which means it will reach critical mass at approximately 28 time units from the starting point."
            )
        else:
            return "This is a simple response for a prompt that doesn't match any specific pattern."


class ErrorProneEmbeddingModel(BaseModel):
    """
    A model that deliberately fails sometimes.
    
    This model is used to demonstrate the error recovery system.
    """
    
    def __init__(self, failure_rate: float = 0.5, embedding_dim: int = 768, **kwargs):
        """
        Initialize the error-prone embedding model.
        
        Args:
            failure_rate: Probability of failure (0.0 to 1.0)
            embedding_dim: Dimension of the embedding vectors
            **kwargs: Additional arguments to pass to the parent class
        """
        super().__init__(**kwargs)
        self.failure_rate = failure_rate
        self.embedding_dim = embedding_dim
        self.call_count = 0
        self.name = "ErrorProneEmbeddingModel"
    
    def encode(self, text: str) -> list:
        """
        Encode text into an embedding vector, with deliberate failures.
        
        Args:
            text: The text to encode
            
        Returns:
            Embedding vector
            
        Raises:
            Various exceptions to test error recovery
        """
        self.call_count += 1
        
        # Simulate different types of errors
        if random.random() < self.failure_rate:
            error_type = random.choice([
                "timeout", "connection", "rate_limit", "invalid_request", 
                "server_error", "authentication", "context_length"
            ])
            
            if error_type == "timeout":
                raise TimeoutError("Request timed out after 10 seconds")
            elif error_type == "connection":
                raise ConnectionError("Failed to connect to API endpoint")
            elif error_type == "invalid_request":
                raise ValueError("Invalid request parameters")
            elif error_type == "server_error":
                raise Exception("Server error: 500 Internal Server Error")
            elif error_type == "authentication":
                raise PermissionError("Authentication failed")
            elif error_type == "rate_limit":
                raise Exception("Rate limit exceeded")
            elif error_type == "context_length":
                raise ValueError("Maximum context length exceeded")
        
        # If no error, generate a random embedding
        return [random.random() for _ in range(self.embedding_dim)]


class TestToTErrorRecoveryIntegration(unittest.TestCase):
    """Tests for the integration of error recovery with ToT."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary directory for cache files
        self.temp_dir = os.path.join(os.path.dirname(__file__), "temp_cache")
        os.makedirs(self.temp_dir, exist_ok=True)
        
        # Create an error-prone model
        self.model = ErrorProneModel(failure_rate=0.7)
        
        # Create a ToT reasoner with error recovery
        self.tot = TreeOfThought(
            provider="custom",  # Custom provider
            model=self.model,
            language="en",
            max_branches=3,
            max_depth=3,
            adaptive=True,
            verbose=True,
            use_error_recovery=True,
            max_retries=3,
            retry_delay=0.1,
            cache_path=os.path.join(self.temp_dir, "tot_cache"),
            use_advanced_strategies=True
        )

    def tearDown(self):
        """Tear down test fixtures."""
        # Clean up temporary directory
        for file in os.listdir(self.temp_dir):
            os.remove(os.path.join(self.temp_dir, file))
        os.rmdir(self.temp_dir)

    def test_tot_with_error_recovery(self):
        """Test ToT with error recovery."""
        # Set a fixed seed for reproducibility
        random.seed(42)
        
        # Run a query that will likely cause errors
        result = self.tot.reason("What is the capital of France?")
        
        # Check that we got a result
        self.assertIsNotNone(result)
        self.assertIsNotNone(result.get("answer"))
        
        # Check that the model was called multiple times (due to retries)
        self.assertGreater(self.model.call_count, 1)

    def test_tot_with_error_recovery_disabled(self):
        """Test ToT with error recovery disabled."""
        # Create a ToT reasoner without error recovery
        tot = TreeOfThought(
            provider="custom",  # Custom provider
            model=self.model,
            language="en",
            max_branches=3,
            max_depth=3,
            adaptive=True,
            verbose=True,
            use_error_recovery=False
        )
        
        # Set a fixed seed for reproducibility
        random.seed(42)
        
        # Reset the model call count
        self.model.call_count = 0
        
        # Run a query that will likely cause errors
        try:
            result = tot.reason("What is the capital of France?")
            # If we get here, the model didn't fail
            self.assertIsNotNone(result)
        except Exception:
            # If we get here, the model failed and there was no recovery
            pass
        
        # Check that the model was called at least once
        self.assertGreaterEqual(self.model.call_count, 1)


class TestRAGErrorRecoveryIntegration(unittest.TestCase):
    """Tests for the integration of error recovery with RAG."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary directory for cache files
        self.temp_dir = os.path.join(os.path.dirname(__file__), "temp_cache")
        os.makedirs(self.temp_dir, exist_ok=True)
        
        # Create a temporary database file
        self.db_path = os.path.join(self.temp_dir, "test_rag.sqlite")
        
        # Create an error-prone embedding model
        self.embedding_model = ErrorProneEmbeddingModel(failure_rate=0.7)
        
        # Create a SQLiteVectorRAG with the error-prone embedding model
        self.rag_system = SQLiteVectorRAG(
            db_path=self.db_path,
            table_name="documents",
            embedding_model="custom",  # This will be ignored since we're providing the model
            embedding_dim=768,
            create_if_not_exists=True
        )
        
        # Replace the embedding model with our error-prone one
        self.rag_system.embedding_model = self.embedding_model
        
        # Wrap the RAG system with error recovery
        self.error_recovery_rag = ErrorRecoveryRAG(
            rag_system=self.rag_system,
            use_error_recovery=True,
            max_retries=3,
            retry_delay=0.1,
            cache_path=os.path.join(self.temp_dir, "rag_cache"),
            use_advanced_strategies=True
        )
        
        # Sample documents
        self.documents = [
            {
                "content": "The capital of France is Paris. It is known for the Eiffel Tower and the Louvre Museum.",
                "metadata": {"source": "geography.txt", "category": "geography"}
            },
            {
                "content": "Python is a programming language. It is known for its simplicity and readability.",
                "metadata": {"source": "programming.txt", "category": "programming"}
            }
        ]

    def tearDown(self):
        """Tear down test fixtures."""
        # Close the RAG system
        self.error_recovery_rag.close()
        
        # Clean up temporary directory
        for file in os.listdir(self.temp_dir):
            os.remove(os.path.join(self.temp_dir, file))
        os.rmdir(self.temp_dir)

    def test_rag_with_error_recovery(self):
        """Test RAG with error recovery."""
        # Set a fixed seed for reproducibility
        random.seed(42)
        
        # Add documents
        doc_ids = []
        for doc in self.documents:
            doc_id = self.error_recovery_rag.add_document(
                content=doc["content"],
                metadata=doc["metadata"]
            )
            if doc_id:
                doc_ids.append(doc_id)
        
        # Check that at least one document was added
        self.assertGreater(len(doc_ids), 0)
        
        # Reset the embedding model call count
        self.embedding_model.call_count = 0
        
        # Query the RAG system
        results = self.error_recovery_rag.query("What is the capital of France?")
        
        # Check that we got results
        self.assertIsNotNone(results)
        
        # Check that the embedding model was called multiple times (due to retries)
        self.assertGreater(self.embedding_model.call_count, 1)

    def test_rag_with_error_recovery_disabled(self):
        """Test RAG with error recovery disabled."""
        # Create a RAG system without error recovery
        rag_system = SQLiteVectorRAG(
            db_path=self.db_path,
            table_name="documents",
            embedding_model="custom",  # This will be ignored since we're providing the model
            embedding_dim=768,
            create_if_not_exists=True
        )
        
        # Replace the embedding model with our error-prone one
        rag_system.embedding_model = self.embedding_model
        
        # Set a fixed seed for reproducibility
        random.seed(42)
        
        # Reset the embedding model call count
        self.embedding_model.call_count = 0
        
        # Add a document (this will likely fail)
        try:
            doc_id = rag_system.add_document(
                content=self.documents[0]["content"],
                metadata=self.documents[0]["metadata"]
            )
            # If we get here, the model didn't fail
            self.assertIsNotNone(doc_id)
        except Exception:
            # If we get here, the model failed and there was no recovery
            pass
        
        # Check that the embedding model was called at least once
        self.assertGreaterEqual(self.embedding_model.call_count, 1)
        
        # Close the RAG system
        rag_system.close()


if __name__ == "__main__":
    unittest.main()
