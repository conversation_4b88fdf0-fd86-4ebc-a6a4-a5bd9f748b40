"""
Test for Vietnamese Quality Metrics.

<PERSON><PERSON>le nà<PERSON> kiểm tra các metrics đánh giá chất lượng đặc thù cho tiếng Việt.
"""

import os
import sys
import unittest
from typing import Dict, List, Any, Optional

# Add the src directory to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from src.deep_research_core.evaluation.vietnamese_quality_metrics import VietnameseQualityMetrics

class TestVietnameseQualityMetrics(unittest.TestCase):
    """Test case for VietnameseQualityMetrics."""

    def setUp(self):
        """Set up test fixtures."""
        self.metrics = VietnameseQualityMetrics()
        
        # Văn bản tiếng Việt chuẩn
        self.standard_text = """
        Tiếng Việt là ngôn ngữ của người Việt và là ngôn ngữ chính thức tại Việt Nam. 
        Tiếng Việt còn được sử dụng bởi cộng đồng người Việt tại nhiều quốc gia khác trên thế giới. 
        Đây là một ngôn ngữ có thanh điệu, thuộc ngữ hệ Nam Á, chi ngành Việt-Mường.
        """
        
        # Văn bản tiếng Việt có lỗi dấu thanh
        self.diacritic_error_text = """
        Tieng Viet la ngon ngu cua nguoi Viet và là ngôn ngữ chinh thuc tai Viet Nam. 
        Tiếng Việt con duoc su dung boi cong dong nguoi Viet tai nhieu quoc gia khac tren the gioi. 
        Đây là một ngôn ngữ co thanh dieu, thuoc ngu he Nam A, chi nganh Viet-Muong.
        """
        
        # Văn bản tiếng Việt có nhiều từ láy
        self.reduplicative_text = """
        Trời xanh xanh, mây trắng trắng, gió nhè nhẹ thổi qua cánh đồng lúa vàng vàng. 
        Những giọt sương long lanh đọng trên lá, tí tách rơi xuống mặt đất. 
        Xa xa, tiếng chim hót líu lo, ríu rít chào đón một ngày mới.
        """
        
        # Văn bản tiếng Việt có nhiều thành ngữ, tục ngữ
        self.idiom_text = """
        Ăn quả nhớ kẻ trồng cây, uống nước nhớ nguồn là đạo lý của người Việt. 
        Có công mài sắt có ngày nên kim, đi một ngày đàng học một sàng khôn. 
        Đói cho sạch rách cho thơm, học ăn học nói học gói học mở.
        """
        
        # Văn bản tiếng Việt phương ngữ Bắc
        self.northern_dialect_text = """
        Tao bảo mày đi với bố mẹ sang nhà ông bà nội chơi nhé. 
        Thế này thì làm sao được, thế kia thì phải làm thế nào? 
        Vâng, cháu sẽ đi ngay đây ạ.
        """
        
        # Văn bản tiếng Việt phương ngữ Nam
        self.southern_dialect_text = """
        Tui nói bạn đi với ba má qua nhà ông bà nội chơi nha. 
        Vầy thì làm sao được, vậy đó thì phải làm sao? 
        Dạ, con sẽ đi liền đây.
        """
        
        # Văn bản tiếng Việt phương ngữ Trung
        self.central_dialect_text = """
        Tau bảo mi đi với cha mạ lên nhà ông bà nội chơi nghe. 
        Rứa thì làm răng được, tê thì phải làm răng? 
        Dạ, con sẽ đi chừ.
        """
        
        # Văn bản tiếng Việt có nhiều từ ghép
        self.compound_word_text = """
        Trí tuệ nhân tạo đang phát triển nhanh chóng trong lĩnh vực xử lý ngôn ngữ tự nhiên. 
        Học máy và học sâu là hai kỹ thuật quan trọng trong lĩnh vực này. 
        Mạng nơ-ron nhân tạo và mạng nơ-ron sâu đã đạt được nhiều thành tựu đáng kể.
        """

    def test_evaluate_diacritic_accuracy(self):
        """Test evaluate_diacritic_accuracy method."""
        # Kiểm tra văn bản chuẩn
        accuracy = self.metrics.evaluate_diacritic_accuracy(self.standard_text)
        self.assertGreaterEqual(accuracy, 0.9, "Văn bản chuẩn phải có độ chính xác dấu thanh cao")
        
        # Kiểm tra văn bản có lỗi dấu thanh
        accuracy = self.metrics.evaluate_diacritic_accuracy(self.diacritic_error_text)
        self.assertLessEqual(accuracy, 0.8, "Văn bản có lỗi dấu thanh phải có độ chính xác dấu thanh thấp")
        
        # Kiểm tra với văn bản tham chiếu
        accuracy = self.metrics.evaluate_diacritic_accuracy(self.diacritic_error_text, self.standard_text)
        self.assertLessEqual(accuracy, 0.7, "Văn bản có lỗi dấu thanh so với văn bản tham chiếu phải có độ chính xác thấp")

    def test_evaluate_reduplicative_usage(self):
        """Test evaluate_reduplicative_usage method."""
        # Kiểm tra văn bản có nhiều từ láy
        score = self.metrics.evaluate_reduplicative_usage(self.reduplicative_text)
        self.assertGreaterEqual(score, 0.8, "Văn bản có nhiều từ láy phải có điểm cao")
        
        # Kiểm tra văn bản chuẩn
        score = self.metrics.evaluate_reduplicative_usage(self.standard_text)
        self.assertLessEqual(score, 0.8, "Văn bản chuẩn phải có điểm từ láy trung bình")

    def test_evaluate_idiom_usage(self):
        """Test evaluate_idiom_usage method."""
        # Kiểm tra văn bản có nhiều thành ngữ, tục ngữ
        score = self.metrics.evaluate_idiom_usage(self.idiom_text)
        self.assertGreaterEqual(score, 0.8, "Văn bản có nhiều thành ngữ, tục ngữ phải có điểm cao")
        
        # Kiểm tra văn bản chuẩn
        score = self.metrics.evaluate_idiom_usage(self.standard_text)
        self.assertLessEqual(score, 0.7, "Văn bản chuẩn phải có điểm thành ngữ, tục ngữ trung bình")

    def test_evaluate_dialect_consistency(self):
        """Test evaluate_dialect_consistency method."""
        # Kiểm tra văn bản phương ngữ Bắc
        score = self.metrics.evaluate_dialect_consistency(self.northern_dialect_text)
        self.assertGreaterEqual(score, 0.8, "Văn bản phương ngữ Bắc phải có độ nhất quán cao")
        
        # Kiểm tra văn bản phương ngữ Nam
        score = self.metrics.evaluate_dialect_consistency(self.southern_dialect_text)
        self.assertGreaterEqual(score, 0.8, "Văn bản phương ngữ Nam phải có độ nhất quán cao")
        
        # Kiểm tra văn bản phương ngữ Trung
        score = self.metrics.evaluate_dialect_consistency(self.central_dialect_text)
        self.assertGreaterEqual(score, 0.8, "Văn bản phương ngữ Trung phải có độ nhất quán cao")
        
        # Kiểm tra văn bản hỗn hợp phương ngữ
        mixed_dialect_text = self.northern_dialect_text + self.southern_dialect_text
        score = self.metrics.evaluate_dialect_consistency(mixed_dialect_text)
        self.assertLessEqual(score, 0.7, "Văn bản hỗn hợp phương ngữ phải có độ nhất quán thấp")

    def test_evaluate_compound_word_accuracy(self):
        """Test evaluate_compound_word_accuracy method."""
        # Kiểm tra văn bản có nhiều từ ghép
        score = self.metrics.evaluate_compound_word_accuracy(self.compound_word_text)
        self.assertGreaterEqual(score, 0.7, "Văn bản có nhiều từ ghép phải có điểm cao")
        
        # Kiểm tra với danh sách từ ghép tham chiếu
        reference_compounds = ["trí tuệ nhân tạo", "xử lý ngôn ngữ tự nhiên", "học máy", "học sâu"]
        score = self.metrics.evaluate_compound_word_accuracy(self.compound_word_text, reference_compounds)
        self.assertGreaterEqual(score, 0.8, "Văn bản có nhiều từ ghép tham chiếu phải có điểm cao")

    def test_evaluate_vietnamese_quality(self):
        """Test evaluate_vietnamese_quality method."""
        # Kiểm tra văn bản chuẩn
        metrics = self.metrics.evaluate_vietnamese_quality(self.standard_text)
        self.assertGreaterEqual(metrics["overall_quality"], 0.7, "Văn bản chuẩn phải có chất lượng tổng thể cao")
        
        # Kiểm tra văn bản có lỗi dấu thanh
        metrics = self.metrics.evaluate_vietnamese_quality(self.diacritic_error_text)
        self.assertLessEqual(metrics["overall_quality"], 0.7, "Văn bản có lỗi dấu thanh phải có chất lượng tổng thể thấp hơn")
        
        # Kiểm tra văn bản có nhiều từ láy
        metrics = self.metrics.evaluate_vietnamese_quality(self.reduplicative_text)
        self.assertGreaterEqual(metrics["reduplicative_usage"], 0.8, "Văn bản có nhiều từ láy phải có điểm từ láy cao")
        
        # Kiểm tra văn bản có nhiều thành ngữ, tục ngữ
        metrics = self.metrics.evaluate_vietnamese_quality(self.idiom_text)
        self.assertGreaterEqual(metrics["idiom_usage"], 0.8, "Văn bản có nhiều thành ngữ, tục ngữ phải có điểm thành ngữ, tục ngữ cao")

if __name__ == '__main__':
    unittest.main()
