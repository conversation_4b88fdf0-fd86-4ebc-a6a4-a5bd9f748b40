"""
Test script for CoTEvaluator.

This script tests the functionality of the CoTEvaluator class.
"""

import os
import sys
import unittest
from unittest.mock import MagicMock, patch
import json

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.evaluation.cot_evaluator import CoTEvaluator

class TestCoTEvaluator(unittest.TestCase):
    """Test case for CoTEvaluator."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create mock API provider
        self.mock_api_provider = MagicMock()
        self.mock_api_provider.generate.return_value = """```json
{
  "step_clarity": 8.0,
  "logical_flow": 7.5,
  "evidence_usage": 7.0,
  "conclusion_strength": 8.0,
  "overall_quality": 7.6,
  "explanation": "The reasoning is clear and well-structured."
}
```"""
        
        # Create patches
        self.patches = [
            patch('src.deep_research_core.evaluation.cot_evaluator.openai_provider', self.mock_api_provider),
            patch('src.deep_research_core.evaluation.cot_evaluator.anthropic_provider', self.mock_api_provider),
            patch('src.deep_research_core.evaluation.cot_evaluator.openrouter_provider', self.mock_api_provider)
        ]
        
        # Start patches
        for p in self.patches:
            p.start()
        
        # Create evaluator
        self.evaluator = CoTEvaluator(
            provider="openai",
            model="gpt-4o",
            use_model_evaluation=True,
            verbose=False
        )
        
        # Sample reasoning
        self.reasoning = """
        Step 1: First, I need to understand the problem. The question asks about the capital of France.
        
        Step 2: I know that Paris is the capital of France.
        
        Step 3: Therefore, the answer is Paris.
        """
    
    def tearDown(self):
        """Tear down test fixtures."""
        # Stop patches
        for p in self.patches:
            p.stop()
    
    def test_extract_reasoning_steps(self):
        """Test extracting reasoning steps."""
        steps = self.evaluator._extract_reasoning_steps(self.reasoning)
        
        # Check that the correct number of steps were extracted
        self.assertEqual(len(steps), 3)
    
    def test_heuristic_evaluation(self):
        """Test heuristic evaluation."""
        metrics = self.evaluator.heuristic_evaluation(self.reasoning)
        
        # Check that all required metrics are present
        self.assertIn("step_clarity", metrics)
        self.assertIn("logical_flow", metrics)
        self.assertIn("evidence_usage", metrics)
        self.assertIn("conclusion_strength", metrics)
        self.assertIn("overall_quality", metrics)
        
        # Check that metrics are within the expected range
        for metric, value in metrics.items():
            self.assertGreaterEqual(value, 0.0)
            self.assertLessEqual(value, 10.0)
    
    def test_model_evaluation(self):
        """Test model evaluation."""
        metrics = self.evaluator.model_evaluation(self.reasoning)
        
        # Check that the API was called
        self.mock_api_provider.generate.assert_called_once()
        
        # Check that all required metrics are present
        self.assertIn("step_clarity", metrics)
        self.assertIn("logical_flow", metrics)
        self.assertIn("evidence_usage", metrics)
        self.assertIn("conclusion_strength", metrics)
        self.assertIn("overall_quality", metrics)
        
        # Check that metrics match the mock response
        self.assertEqual(metrics["step_clarity"], 8.0)
        self.assertEqual(metrics["logical_flow"], 7.5)
        self.assertEqual(metrics["evidence_usage"], 7.0)
        self.assertEqual(metrics["conclusion_strength"], 8.0)
        self.assertEqual(metrics["overall_quality"], 7.6)
    
    def test_evaluate(self):
        """Test the evaluate method."""
        result = self.evaluator.evaluate(self.reasoning)
        
        # Check that the result contains the expected fields
        self.assertIn("metrics", result)
        self.assertIn("step_count", result)
        self.assertIn("latency", result)
        self.assertIn("steps", result)
        
        # Check that the step count is correct
        self.assertEqual(result["step_count"], 3)
    
    def test_compare(self):
        """Test the compare method."""
        # Create a second reasoning
        reasoning_b = """
        Step 1: The question asks about the capital of France.
        
        Step 2: France is a country in Europe.
        
        Step 3: Paris is the capital city of France.
        
        Step 4: Therefore, the answer is Paris.
        """
        
        # Mock the evaluate method to return different results
        self.evaluator.evaluate = MagicMock(side_effect=[
            {
                "metrics": {
                    "step_clarity": 7.0,
                    "logical_flow": 7.0,
                    "evidence_usage": 6.0,
                    "conclusion_strength": 7.0,
                    "overall_quality": 6.8
                },
                "step_count": 3
            },
            {
                "metrics": {
                    "step_clarity": 8.0,
                    "logical_flow": 8.0,
                    "evidence_usage": 7.0,
                    "conclusion_strength": 8.0,
                    "overall_quality": 7.8
                },
                "step_count": 4
            }
        ])
        
        # Compare the reasonings
        result = self.evaluator.compare(self.reasoning, reasoning_b)
        
        # Check that the result contains the expected fields
        self.assertIn("evaluation_a", result)
        self.assertIn("evaluation_b", result)
        self.assertIn("differences", result)
        self.assertIn("better", result)
        self.assertIn("improvement", result)
        
        # Check that the better reasoning is identified correctly
        self.assertEqual(result["better"], "B")
        
        # Check that the improvement is calculated correctly
        self.assertEqual(result["improvement"], 1.0)

if __name__ == '__main__':
    unittest.main()
