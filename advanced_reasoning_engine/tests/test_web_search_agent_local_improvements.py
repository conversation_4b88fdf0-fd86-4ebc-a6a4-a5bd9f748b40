#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test WebSearchAgentLocal Improvements.

Module này kiểm tra các cải tiến cho WebSearchAgentLocal.
"""

import unittest
import os
import sys
import time
import json
from unittest.mock import patch, MagicMock

# Thêm thư mục gốc vào sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal
from src.deep_research_core.agents.web_search_agent_local_improvements import integrate_improvements
from src.deep_research_core.agents.vietnamese_search_integration_improved import integrate_vietnamese_search_improved
from src.deep_research_core.utils.vietnamese_captcha_handler import VietnameseCaptchaHandler
from src.deep_research_core.agents.performance_optimizer_improved import PerformanceOptimizerImproved
from src.deep_research_core.agents.module_integration_manager import ModuleIntegrationManager

class TestWebSearchAgentLocalImprovements(unittest.TestCase):
    """Kiểm tra các cải tiến cho WebSearchAgentLocal."""
    
    def setUp(self):
        """Thiết lập trước mỗi test."""
        # Tạo mock cho WebSearchAgentLocal
        self.agent = MagicMock(spec=WebSearchAgentLocal)
        
        # Thiết lập các thuộc tính cần thiết
        self.agent.available_engines = ["duckduckgo", "searx", "qwant"]
        self.agent.search = MagicMock(return_value={"success": True, "results": []})
        self.agent._extract_content_sync = MagicMock(return_value={"success": True, "text": ""})
        self.agent._deep_crawl = MagicMock(return_value={"success": True, "text": ""})
    
    def test_integrate_improvements(self):
        """Kiểm tra tích hợp các cải tiến."""
        # Gọi hàm tích hợp
        result = integrate_improvements(self.agent)
        
        # Kiểm tra kết quả
        self.assertTrue(result["success"])
        self.assertIn("module_manager", result["integrated_modules"])
    
    def test_vietnamese_search_integration(self):
        """Kiểm tra tích hợp tìm kiếm tiếng Việt."""
        # Gọi hàm tích hợp
        integrate_vietnamese_search_improved(self.agent)
        
        # Kiểm tra các thuộc tính và phương thức đã được thêm
        self.assertTrue(hasattr(self.agent, "search_vietnamese"))
        self.assertTrue(hasattr(self.agent, "detect_vietnamese_query"))
        self.assertTrue(hasattr(self.agent, "extract_vietnamese_content"))
        
        # Kiểm tra danh sách công cụ tìm kiếm
        self.assertIn("coccoc", self.agent.available_engines)
        self.assertIn("wikitiengviet", self.agent.available_engines)
        self.assertIn("baomoi", self.agent.available_engines)
    
    @patch("src.deep_research_core.utils.vietnamese_captcha_handler.VietnameseCaptchaHandler.detect_vietnamese_captcha")
    def test_vietnamese_captcha_handler(self, mock_detect):
        """Kiểm tra xử lý CAPTCHA tiếng Việt."""
        # Thiết lập mock
        mock_detect.return_value = (True, "recaptcha")
        
        # Tạo VietnameseCaptchaHandler
        handler = VietnameseCaptchaHandler()
        
        # Kiểm tra phát hiện CAPTCHA
        has_captcha, captcha_type = handler.detect_vietnamese_captcha("<html>xác thực captcha</html>")
        
        # Kiểm tra kết quả
        self.assertTrue(has_captcha)
        self.assertEqual(captcha_type, "recaptcha")
    
    def test_performance_optimizer(self):
        """Kiểm tra tối ưu hóa hiệu suất."""
        # Tạo PerformanceOptimizerImproved
        optimizer = PerformanceOptimizerImproved(max_workers=2)
        
        # Kiểm tra số lượng worker
        self.assertEqual(optimizer.max_workers, 2)
        
        # Kiểm tra thống kê hiệu suất
        self.assertEqual(optimizer.performance_stats["total_queries"], 0)
        self.assertEqual(optimizer.performance_stats["complex_queries"], 0)
    
    def test_module_integration_manager(self):
        """Kiểm tra quản lý tích hợp module."""
        # Tạo ModuleIntegrationManager
        manager = ModuleIntegrationManager(self.agent, auto_detect=False)
        
        # Đăng ký fallback
        manager.register_fallback("captcha_handler", "vietnamese_captcha_handler")
        
        # Kiểm tra fallback đã được đăng ký
        self.assertIn("captcha_handler", manager.module_fallbacks)
        self.assertEqual(manager.module_fallbacks["captcha_handler"]["module_name"], "vietnamese_captcha_handler")
    
    @patch("src.deep_research_core.agents.vietnamese_search_integration_improved.detect_vietnamese_query")
    def test_detect_vietnamese_query(self, mock_detect):
        """Kiểm tra phát hiện truy vấn tiếng Việt."""
        # Thiết lập mock
        mock_detect.return_value = True
        
        # Tích hợp tìm kiếm tiếng Việt
        integrate_vietnamese_search_improved(self.agent)
        
        # Gọi phương thức detect_vietnamese_query
        result = self.agent.detect_vietnamese_query("Thủ đô của Việt Nam là gì?")
        
        # Kiểm tra kết quả
        self.assertTrue(result)
    
    @patch("src.deep_research_core.agents.performance_optimizer_improved.PerformanceOptimizerImproved.optimize_query_execution")
    def test_improved_search(self, mock_optimize):
        """Kiểm tra phương thức search cải tiến."""
        # Thiết lập mock
        mock_optimize.return_value = {"success": True, "results": [{"title": "Test", "url": "https://example.com"}]}
        
        # Tích hợp các cải tiến
        integrate_improvements(self.agent)
        
        # Gọi phương thức search
        from src.deep_research_core.agents.web_search_agent_local_improvements import improved_search
        result = improved_search(self.agent, "test query")
        
        # Kiểm tra kết quả
        self.assertTrue(result["success"])
    
    @patch("src.deep_research_core.agents.vietnamese_search_integration_improved.extract_vietnamese_content")
    def test_improved_extract_content_sync(self, mock_extract):
        """Kiểm tra phương thức _extract_content_sync cải tiến."""
        # Thiết lập mock
        mock_extract.return_value = {"success": True, "content": "Nội dung tiếng Việt"}
        
        # Tích hợp các cải tiến
        integrate_improvements(self.agent)
        
        # Gọi phương thức _extract_content_sync
        from src.deep_research_core.agents.web_search_agent_local_improvements import improved_extract_content_sync
        result = improved_extract_content_sync(self.agent, "https://vnexpress.net")
        
        # Kiểm tra kết quả
        self.assertTrue(result["success"])
    
    @patch("src.deep_research_core.agents.adaptive_crawler.AdaptiveCrawler.crawl")
    def test_improved_deep_crawl(self, mock_crawl):
        """Kiểm tra phương thức _deep_crawl cải tiến."""
        # Thiết lập mock
        mock_crawl.return_value = {"success": True, "results": [{"url": "https://example.com", "content": "Test content"}]}
        
        # Tích hợp các cải tiến
        integrate_improvements(self.agent)
        
        # Thiết lập AdaptiveCrawler
        self.agent._adaptive_crawler = MagicMock()
        self.agent._adaptive_crawler.crawl = mock_crawl
        
        # Gọi phương thức _deep_crawl
        from src.deep_research_core.agents.web_search_agent_local_improvements import improved_deep_crawl
        result = improved_deep_crawl(self.agent, "https://example.com")
        
        # Kiểm tra kết quả
        self.assertTrue(result["success"])
    
    def test_integration_with_real_agent(self):
        """Kiểm tra tích hợp với WebSearchAgentLocal thật."""
        try:
            # Tạo WebSearchAgentLocal thật
            agent = WebSearchAgentLocal(verbose=True)
            
            # Tích hợp các cải tiến
            result = integrate_improvements(agent)
            
            # Kiểm tra kết quả
            self.assertTrue(result["success"])
            
            # Kiểm tra các thuộc tính và phương thức đã được thêm
            self.assertTrue(hasattr(agent, "module_manager"))
            
            # Dọn dẹp
            if hasattr(agent, "_performance_optimizer_improved"):
                agent._performance_optimizer_improved.cleanup()
        except Exception as e:
            # Bỏ qua lỗi khi không thể tạo WebSearchAgentLocal thật
            print(f"Không thể tạo WebSearchAgentLocal thật: {str(e)}")
    
    def tearDown(self):
        """Dọn dẹp sau mỗi test."""
        # Dọn dẹp các tài nguyên
        pass

if __name__ == "__main__":
    unittest.main()
