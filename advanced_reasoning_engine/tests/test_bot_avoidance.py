"""
Test the bot avoidance utilities.
"""

import unittest
from unittest.mock import patch, MagicMock

import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.utils.bot_avoidance import BotAvoidanceManager


class TestBotAvoidance(unittest.TestCase):
    """Test the bot avoidance utilities."""

    def test_user_agent_rotation(self):
        """Test user agent rotation."""
        manager = BotAvoidanceManager()
        
        # Get initial user agent
        initial_ua = manager.current_user_agent
        
        # Rotate user agent
        new_ua = manager.rotate_user_agent()
        
        # Check that it changed
        self.assertNotEqual(initial_ua, new_ua)
        self.assertEqual(new_ua, manager.current_user_agent)
        
        # Check that we have multiple user agents
        self.assertGreater(len(manager.user_agents), 5)

    def test_request_headers(self):
        """Test request headers generation."""
        manager = BotAvoidanceManager()
        
        # Get headers
        headers = manager.get_request_headers(rotate=False)
        
        # Check required headers
        self.assertIn("User-Agent", headers)
        self.assertIn("Accept", headers)
        self.assertIn("Accept-Language", headers)
        
        # Check stealth mode headers
        self.assertIn("Sec-Fetch-Dest", headers)
        self.assertIn("Sec-Fetch-Mode", headers)
        
        # Test with rotation
        initial_ua = headers["User-Agent"]
        headers_rotated = manager.get_request_headers(rotate=True)
        self.assertNotEqual(initial_ua, headers_rotated["User-Agent"])

    def test_request_delay(self):
        """Test request delay."""
        manager = BotAvoidanceManager()
        
        # First request should add delay
        with patch('time.sleep') as mock_sleep:
            delay = manager.add_request_delay(min_delay=0.5, max_delay=1.0)
            mock_sleep.assert_called_once()
            
        # Update last request time to simulate time passing
        manager.last_request_time = 0
        
        # Second request should also add delay
        with patch('time.sleep') as mock_sleep:
            delay = manager.add_request_delay(min_delay=0.5, max_delay=1.0)
            mock_sleep.assert_called_once()

    def test_browser_fingerprint(self):
        """Test browser fingerprint generation."""
        manager = BotAvoidanceManager()
        
        # Get fingerprint
        fingerprint = manager.get_browser_fingerprint_overrides()
        
        # Check required fields
        self.assertIn("viewport", fingerprint)
        self.assertIn("color_scheme", fingerprint)
        self.assertIn("locale", fingerprint)
        self.assertIn("timezone_id", fingerprint)
        
        # Check viewport
        self.assertIn("width", fingerprint["viewport"])
        self.assertIn("height", fingerprint["viewport"])
        
        # Check that values are reasonable
        self.assertGreater(fingerprint["viewport"]["width"], 800)
        self.assertGreater(fingerprint["viewport"]["height"], 600)

    def test_stealth_mode(self):
        """Test stealth mode application."""
        manager = BotAvoidanceManager()
        
        # Create mock page
        mock_page = MagicMock()
        mock_page.evaluate = MagicMock()
        mock_page.set_extra_http_headers = MagicMock()
        mock_page.set_viewport_size = MagicMock()
        mock_page.mouse = MagicMock()
        mock_page.mouse.move = MagicMock()
        
        # Apply stealth mode
        manager.apply_stealth_mode_to_page(mock_page)
        
        # Check that methods were called
        mock_page.set_extra_http_headers.assert_called_once()
        mock_page.set_viewport_size.assert_called_once()
        self.assertGreater(mock_page.evaluate.call_count, 0)
        self.assertGreater(mock_page.mouse.move.call_count, 0)


if __name__ == '__main__':
    unittest.main()
