"""
Tests for performance optimization modules.
"""

import unittest
import time
import threading
import asyncio
import os
import sys
import tempfile
from unittest.mock import patch, MagicMock

# Add parent directory to path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.utils.performance_monitor import PerformanceMonitor
from src.deep_research_core.utils.async_search_executor import AsyncSearchExecutor
from src.deep_research_core.utils.resource_limiter import ResourceLimiter, ResourceError
from src.deep_research_core.utils.adaptive_parameters import AdaptiveParameter, AdaptiveParameterManager

class TestPerformanceMonitor(unittest.TestCase):
    """Test cases for PerformanceMonitor."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.monitor = PerformanceMonitor(
            enabled=True,
            log_interval=1,
            memory_threshold=90.0,
            cpu_threshold=90.0,
            log_to_file=False,
            trace_memory=False
        )
    
    def tearDown(self):
        """Tear down test fixtures."""
        if hasattr(self, 'monitor'):
            self.monitor.stop()
    
    def test_start_stop(self):
        """Test starting and stopping the monitor."""
        self.monitor.start()
        self.assertTrue(self.monitor.monitoring_thread.is_alive())
        
        self.monitor.stop()
        time.sleep(0.1)  # Give thread time to stop
        self.assertFalse(self.monitor.monitoring_thread.is_alive())
    
    def test_time_method(self):
        """Test timing a method."""
        # Define a test method
        @self.monitor.time_method("test_method")
        def test_method(sleep_time):
            time.sleep(sleep_time)
            return "result"
        
        # Call the method
        result = test_method(0.1)
        
        # Check result
        self.assertEqual(result, "result")
        
        # Check timing
        self.assertIn("test_method", self.monitor.method_timings)
        self.assertEqual(self.monitor.method_timings["test_method"]["count"], 1)
        self.assertGreaterEqual(self.monitor.method_timings["test_method"]["total_time"], 0.1)
    
    def test_get_performance_summary(self):
        """Test getting performance summary."""
        # Add some test data
        self.monitor.memory_usage = [(time.time(), 50.0)]
        self.monitor.cpu_usage = [(time.time(), 30.0)]
        self.monitor.method_timings = {
            "test_method": {
                "count": 1,
                "total_time": 0.1,
                "min_time": 0.1,
                "max_time": 0.1
            }
        }
        
        # Get summary
        summary = self.monitor.get_performance_summary()
        
        # Check summary
        self.assertIn("memory", summary)
        self.assertIn("cpu", summary)
        self.assertIn("method_timings", summary)
        self.assertEqual(summary["memory"]["current"], 50.0)
        self.assertEqual(summary["cpu"]["current"], 30.0)
        self.assertIn("test_method", summary["method_timings"])

class TestAsyncSearchExecutor(unittest.TestCase):
    """Test cases for AsyncSearchExecutor."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.executor = AsyncSearchExecutor(
            max_concurrent_searches=5,
            max_concurrent_extractions=10,
            timeout=5,
            use_thread_pool=True,
            max_workers=2
        )
        self.executor.start()
    
    def tearDown(self):
        """Tear down test fixtures."""
        if hasattr(self, 'executor'):
            self.executor.stop()
    
    def test_execute_search_async(self):
        """Test executing a search asynchronously."""
        # Define a test search function
        def test_search(query, **kwargs):
            time.sleep(0.1)
            return {
                "success": True,
                "query": query,
                "results": [{"title": f"Result for {query}"}]
            }
        
        # Execute search
        async def run_test():
            result = await self.executor.execute_search_async(test_search, "test query")
            return result
        
        # Run test
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(run_test())
        loop.close()
        
        # Check result
        self.assertTrue(result["success"])
        self.assertEqual(result["query"], "test query")
        self.assertEqual(len(result["results"]), 1)
        self.assertEqual(result["results"][0]["title"], "Result for test query")
        self.assertIn("execution_time", result)
    
    def test_execute_batch_search_async(self):
        """Test executing multiple searches asynchronously."""
        # Define a test search function
        def test_search(query, **kwargs):
            time.sleep(0.1)
            return {
                "success": True,
                "query": query,
                "results": [{"title": f"Result for {query}"}]
            }
        
        # Execute batch search
        async def run_test():
            results = await self.executor.execute_batch_search_async(
                test_search,
                ["query1", "query2", "query3"]
            )
            return results
        
        # Run test
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        results = loop.run_until_complete(run_test())
        loop.close()
        
        # Check results
        self.assertEqual(len(results), 3)
        self.assertTrue(all(result["success"] for result in results))
        self.assertEqual(results[0]["query"], "query1")
        self.assertEqual(results[1]["query"], "query2")
        self.assertEqual(results[2]["query"], "query3")

class TestResourceLimiter(unittest.TestCase):
    """Test cases for ResourceLimiter."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.limiter = ResourceLimiter(
            memory_limit=90.0,
            cpu_limit=90.0,
            max_concurrent_requests=5,
            check_interval=1.0,
            adaptive=True,
            gc_threshold=95.0,
            emergency_actions=True
        )
    
    def tearDown(self):
        """Tear down test fixtures."""
        if hasattr(self, 'limiter'):
            self.limiter.stop()
    
    def test_start_stop(self):
        """Test starting and stopping the limiter."""
        self.limiter.start()
        self.assertTrue(self.limiter.monitoring_thread.is_alive())
        
        self.limiter.stop()
        time.sleep(0.1)  # Give thread time to stop
        self.assertFalse(self.limiter.monitoring_thread.is_alive())
    
    def test_limit_resources(self):
        """Test limiting resources for a function."""
        # Define a test function
        @self.limiter.limit_resources
        def test_function():
            return "result"
        
        # Call the function
        result = test_function()
        
        # Check result
        self.assertEqual(result, "result")
        
        # Check stats
        stats = self.limiter.get_resource_stats()
        self.assertEqual(stats["total_requests"], 1)
        self.assertEqual(stats["active_requests"], 0)
        self.assertEqual(stats["rejected_requests"], 0)
    
    def test_resource_error(self):
        """Test resource error when limits are exceeded."""
        # Mock current_memory_usage to exceed limit
        self.limiter.current_memory_usage = 95.0
        
        # Define a test function
        @self.limiter.limit_resources
        def test_function():
            return "result"
        
        # Call the function and expect ResourceError
        with self.assertRaises(ResourceError):
            test_function()
        
        # Check stats
        stats = self.limiter.get_resource_stats()
        self.assertEqual(stats["rejected_requests"], 1)

class TestAdaptiveParameters(unittest.TestCase):
    """Test cases for AdaptiveParameter and AdaptiveParameterManager."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary directory for config file
        self.temp_dir = tempfile.TemporaryDirectory()
        self.config_file = os.path.join(self.temp_dir.name, "adaptive_params.json")
        
        # Create AdaptiveParameterManager
        self.manager = AdaptiveParameterManager(
            config_file=self.config_file,
            auto_save=True,
            auto_adjust_interval=1.0
        )
        
        # Add some parameters
        self.manager.add_parameter(
            name="max_concurrent_searches",
            initial_value=5,
            min_value=1,
            max_value=10,
            step_size=1,
            optimization_goal="maximize",
            is_integer=True
        )
        
        self.manager.add_parameter(
            name="timeout",
            initial_value=30.0,
            min_value=5.0,
            max_value=60.0,
            step_size=5.0,
            optimization_goal="minimize",
            is_integer=False
        )
    
    def tearDown(self):
        """Tear down test fixtures."""
        # Clean up temporary directory
        self.temp_dir.cleanup()
    
    def test_get_value(self):
        """Test getting parameter value."""
        # Check initial values
        self.assertEqual(self.manager.get_value("max_concurrent_searches"), 5)
        self.assertEqual(self.manager.get_value("timeout"), 30.0)
        
        # Check non-existent parameter
        self.assertIsNone(self.manager.get_value("non_existent"))
    
    def test_update_performance(self):
        """Test updating performance."""
        # Update performance
        self.manager.update_performance("max_concurrent_searches", 0.8)
        self.manager.update_performance("max_concurrent_searches", 0.9)
        
        # Check history
        param = self.manager.get_parameter("max_concurrent_searches")
        self.assertEqual(len(param.history), 2)
        self.assertEqual(param.history[0][0], 5)  # value
        self.assertEqual(param.history[0][1], 0.8)  # performance
        self.assertEqual(param.history[1][0], 5)  # value
        self.assertEqual(param.history[1][1], 0.9)  # performance
    
    def test_adjust(self):
        """Test adjusting parameter value."""
        # Add performance data
        param = self.manager.get_parameter("max_concurrent_searches")
        param.history = [
            (5, 0.8, time.time() - 120),  # 2 minutes ago
            (6, 0.9, time.time() - 60),   # 1 minute ago
        ]
        param.last_adjustment_time = time.time() - 120  # 2 minutes ago
        
        # Adjust parameter
        adjusted = param.adjust()
        
        # Check adjustment
        self.assertTrue(adjusted)
        self.assertEqual(param.value, 6)  # Increased to match best value
    
    def test_save_load_config(self):
        """Test saving and loading configuration."""
        # Save config
        self.manager.save_config()
        
        # Create a new manager and load config
        new_manager = AdaptiveParameterManager(
            config_file=self.config_file,
            auto_save=False
        )
        new_manager.load_config()
        
        # Check loaded parameters
        self.assertEqual(new_manager.get_value("max_concurrent_searches"), 5)
        self.assertEqual(new_manager.get_value("timeout"), 30.0)

if __name__ == "__main__":
    unittest.main()
