"""
Integration tests for the RL framework integrations.

These tests verify that all framework integrations work together
correctly and can be managed through the FrameworkFactory.
"""

import os
import unittest
from unittest.mock import patch, MagicMock

from deep_research_core.rl_tuning.frameworks import (
    FrameworkFactory,
    VerlIntegration,
    TinyZeroIntegration,
    OpenR1Integration,
    TRLX_AVAILABLE
)

# Check if frameworks are available
try:
    import verl
    VERL_AVAILABLE = True
except ImportError:
    VERL_AVAILABLE = False

try:
    import tinyzero
    TINYZERO_AVAILABLE = True
except ImportError:
    TINYZERO_AVAILABLE = False

try:
    import openr1
    OPENR1_AVAILABLE = True
except ImportError:
    OPENR1_AVAILABLE = False


class TestFrameworksIntegration(unittest.TestCase):
    """Integration tests for the RL framework integrations."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create a factory instance
        self.factory = FrameworkFactory()
        
        # Mock any external framework dependencies
        self.patches = []
        
        # Patch Verl if not available
        if not VERL_AVAILABLE:
            p = patch('deep_research_core.rl_tuning.frameworks.verl_integration.PPOAgent')
            self.patches.append(p)
            p.start()
        
        # Patch TinyZero if not available
        if not TINYZERO_AVAILABLE:
            p1 = patch('deep_research_core.rl_tuning.frameworks.tinyzero_integration.PPOTrainer')
            self.patches.append(p1)
            p1.start()
            
            p2 = patch('deep_research_core.rl_tuning.frameworks.tinyzero_integration.DPOTrainer')
            self.patches.append(p2)
            p2.start()
        
        # Patch OpenR1 if not available
        if not OPENR1_AVAILABLE:
            p1 = patch('deep_research_core.rl_tuning.frameworks.openr1_integration.GRPO')
            self.patches.append(p1)
            p1.start()
            
            p2 = patch('deep_research_core.rl_tuning.frameworks.openr1_integration.PPO')
            self.patches.append(p2)
            p2.start()
            
            p3 = patch('deep_research_core.rl_tuning.frameworks.openr1_integration.SFT')
            self.patches.append(p3)
            p3.start()
        
        # Patch Trlx if not available
        if not TRLX_AVAILABLE:
            p = patch('deep_research_core.rl_tuning.frameworks.trlx_integration.trlx')
            self.patches.append(p)
            p.start()
    
    def tearDown(self):
        """Tear down test fixtures."""
        # Stop all patches
        for p in self.patches:
            p.stop()
    
    def test_create_multiple_frameworks(self):
        """Test creating multiple frameworks and managing them with the factory."""
        # Create Verl integration
        verl_framework = self.factory.create_framework(
            framework_type="verl",
            framework_id="verl_ppo",
            device="cpu",
            agent_type="ppo"
        )
        
        # Create TinyZero integration
        tinyzero_framework = self.factory.create_framework(
            framework_type="tinyzero",
            framework_id="tinyzero_dpo",
            device="cpu",
            method="dpo"
        )
        
        # Create OpenR1 integration
        openr1_framework = self.factory.create_framework(
            framework_type="openr1",
            framework_id="openr1_grpo",
            device="cpu",
            method="grpo"
        )
        
        # Check if all frameworks were created
        frameworks = self.factory.list_frameworks()
        self.assertEqual(len(frameworks), 3)
        self.assertIn("verl_ppo", frameworks)
        self.assertIn("tinyzero_dpo", frameworks)
        self.assertIn("openr1_grpo", frameworks)
        
        # Check if we can retrieve the frameworks
        retrieved_verl = self.factory.get_framework("verl_ppo")
        self.assertIsInstance(retrieved_verl, VerlIntegration)
        
        retrieved_tinyzero = self.factory.get_framework("tinyzero_dpo")
        self.assertIsInstance(retrieved_tinyzero, TinyZeroIntegration)
        
        retrieved_openr1 = self.factory.get_framework("openr1_grpo")
        self.assertIsInstance(retrieved_openr1, OpenR1Integration)
        
        # Close individual framework
        self.factory.close_framework("verl_ppo")
        self.assertEqual(len(self.factory.list_frameworks()), 2)
        
        # Close all frameworks
        self.factory.close_all()
        self.assertEqual(len(self.factory.list_frameworks()), 0)
    
    @unittest.skipIf(not VERL_AVAILABLE, "Verl is not installed")
    def test_verl_initialization(self):
        """Test initialization of Verl framework."""
        # Create Verl integration
        verl_framework = self.factory.create_framework(
            framework_type="verl",
            device="cpu",
            agent_type="ppo"
        )
        
        # Mock environment and spaces
        mock_env = MagicMock()
        mock_obs_space = MagicMock()
        mock_action_space = MagicMock()
        
        # Initialize the framework
        self.factory.initialize_framework(
            framework_id="verl",
            environment=mock_env,
            observation_space=mock_obs_space,
            action_space=mock_action_space,
            agent_config={"learning_rate": 0.001}
        )
        
        # Check if the framework is initialized
        self.assertTrue(verl_framework.is_initialized)
    
    @unittest.skipIf(not TINYZERO_AVAILABLE, "TinyZero is not installed")
    def test_tinyzero_initialization(self):
        """Test initialization of TinyZero framework."""
        # Create TinyZero integration
        tinyzero_framework = self.factory.create_framework(
            framework_type="tinyzero",
            device="cpu",
            method="ppo"
        )
        
        # Mock environment and model
        mock_env = MagicMock()
        mock_model = MagicMock()
        
        # Initialize the framework
        self.factory.initialize_framework(
            framework_id="tinyzero",
            environment=mock_env,
            model=mock_model,
            trainer_config={"learning_rate": 0.001}
        )
        
        # Check if the framework is initialized
        self.assertTrue(tinyzero_framework.is_initialized)
    
    @unittest.skipIf(not OPENR1_AVAILABLE, "OpenR1 is not installed")
    def test_openr1_initialization(self):
        """Test initialization of OpenR1 framework."""
        # Create OpenR1 integration
        openr1_framework = self.factory.create_framework(
            framework_type="openr1",
            device="cpu",
            method="sft"
        )
        
        # Mock model and tokenizer
        mock_model = MagicMock()
        mock_tokenizer = MagicMock()
        
        # Initialize the framework
        self.factory.initialize_framework(
            framework_id="openr1",
            model=mock_model,
            tokenizer=mock_tokenizer,
            trainer_config={"lr": 0.001}
        )
        
        # Check if the framework is initialized
        self.assertTrue(openr1_framework.is_initialized)
    
    @unittest.skipIf(not TRLX_AVAILABLE, "Trlx is not installed")
    def test_trlx_initialization(self):
        """Test initialization of Trlx framework."""
        # Create Trlx integration
        trlx_framework = self.factory.create_framework(
            framework_type="trlx",
            device="cpu",
            method="ppo"
        )
        
        # Create a mock config
        mock_config = {
            "model": {
                "model_path": "gpt2",
            },
            "train": {
                "seq_length": 512,
                "epochs": 1,
                "total_steps": 1000,
            }
        }
        
        # Initialize the framework
        self.factory.initialize_framework(
            framework_id="trlx",
            config=mock_config
        )
        
        # Check if the framework is initialized
        self.assertTrue(trlx_framework.is_initialized)
    
    def test_mock_training_workflow(self):
        """Test a complete training workflow with mocked frameworks."""
        # Create all framework types
        with patch('deep_research_core.rl_tuning.frameworks.verl_integration.VerlIntegration.train') as mock_train, \
             patch('deep_research_core.rl_tuning.frameworks.verl_integration.VerlIntegration.evaluate') as mock_evaluate, \
             patch('deep_research_core.rl_tuning.frameworks.verl_integration.VerlIntegration.initialize') as mock_initialize:
            
            # Mock initialization and training
            mock_initialize.return_value = None
            mock_train.return_value = {"loss": 0.5, "reward": 10.0}
            mock_evaluate.return_value = {"accuracy": 0.8, "reward": 15.0}
            
            # Create framework
            self.factory.create_framework(
                framework_type="verl",
                framework_id="test_workflow",
                device="cpu"
            )
            
            # Initialize framework
            self.factory.initialize_framework(
                framework_id="test_workflow",
                environment=MagicMock(),
                observation_space=MagicMock(),
                action_space=MagicMock()
            )
            
            # Train the model
            train_result = self.factory.train(
                framework_id="test_workflow",
                train_data=MagicMock(),
                val_data=MagicMock()
            )
            
            # Evaluate the model
            eval_result = self.factory.evaluate(
                framework_id="test_workflow",
                eval_data=MagicMock()
            )
            
            # Check results
            self.assertEqual(train_result["loss"], 0.5)
            self.assertEqual(train_result["reward"], 10.0)
            self.assertEqual(eval_result["accuracy"], 0.8)
            self.assertEqual(eval_result["reward"], 15.0)
            
            # Clean up
            self.factory.close_all()


if __name__ == "__main__":
    unittest.main() 