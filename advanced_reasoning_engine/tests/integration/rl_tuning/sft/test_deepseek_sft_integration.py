"""
Integration tests for the DeepSeekSFT class.

These tests require valid DeepSeek API credentials to run.
Set the DEEPSEEK_API_KEY environment variable before running.

Note: These tests will use actual API credits if run.
"""

import os
import unittest
import json
from typing import List, Dict, Any

import pytest

from deep_research_core.rl_tuning.sft import DeepSeekSFT


def get_test_data() -> List[Dict[str, Any]]:
    """Get sample data for testing."""
    return [
        {
            "input": "Explain the concept of machine learning in simple terms.",
            "output": "Machine learning is a type of artificial intelligence where computers learn from examples and experiences, similar to how humans learn. Instead of being explicitly programmed to perform a task, the computer uses patterns in data to figure out what to do. For example, it can learn to recognize cats in photos after seeing many examples of cats, even without someone telling it exactly what features make up a cat."
        },
        {
            "input": "What is the capital of France?",
            "output": "The capital of France is Paris. It's known as the 'City of Light' and is famous for landmarks like the Eiffel Tower, the Louvre Museum, and Notre-Dame Cathedral."
        }
    ]


@pytest.mark.skipif(not os.environ.get("DEEPSEEK_API_KEY"), 
                    reason="DEEPSEEK_API_KEY environment variable not set")
class TestDeepSeekSFTIntegration(unittest.TestCase):
    """Integration tests for the DeepSeekSFT class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.api_key = os.environ.get("DEEPSEEK_API_KEY")
        self.test_data = get_test_data()
        
        # Initialize the DeepSeekSFT instance
        self.sft = DeepSeekSFT(
            model_name="deepseek-chat",
            output_dir="./test_integration_output",
            api_key=self.api_key
        )
    
    def test_prepare_data(self):
        """Test data preparation."""
        train_dataset, _ = self.sft.prepare_data(
            train_data=self.test_data
        )
        
        self.assertEqual(len(train_dataset), 2)
        for i, example in enumerate(self.test_data):
            self.assertEqual(train_dataset.data[i]["input"], example["input"])
            self.assertEqual(train_dataset.data[i]["output"], example["output"])
    
    def test_generate_text(self):
        """Test text generation."""
        response = self.sft.generate_text(
            prompt="What is artificial intelligence?",
            max_length=50,
            temperature=0.7
        )
        
        # Check that we got a non-empty response
        self.assertTrue(len(response) > 0)
        
        # Print the response for manual inspection
        print(f"\nGenerated response: {response}")


if __name__ == "__main__":
    unittest.main() 