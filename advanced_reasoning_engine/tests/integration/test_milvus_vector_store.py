"""
Integration tests for MilvusVectorStore.

These tests require a running Milvus server.
They will be skipped if <PERSON>l<PERSON>s is not available.
"""

import os
import unittest
import numpy as np
from unittest import skipIf

try:
    from pymilvus import connections
    MILVUS_AVAILABLE = True
except ImportError:
    MILVUS_AVAILABLE = False

from src.deep_research_core.retrieval.vector_store.milvus_vector_store import MilvusVectorStore

# Check if Milvus server is running
def is_milvus_running():
    """Check if Milvus server is running."""
    if not MILVUS_AVAILABLE:
        return False
    
    try:
        connections.connect(
            alias="test_connection",
            host="localhost",
            port="19530"
        )
        connections.disconnect("test_connection")
        return True
    except Exception:
        return False

MILVUS_RUNNING = is_milvus_running()

@skipIf(not MILVUS_RUNNING, "Milvus server not available")
class TestMilvusVectorStoreIntegration(unittest.TestCase):
    """
    Integration tests for MilvusVectorStore.
    
    These tests require a running Milvus server.
    """
    
    def setUp(self):
        """Set up test fixtures."""
        # Create a unique collection name for this test run
        self.collection_name = f"test_collection_{os.getpid()}"
        
        # Initialize the vector store
        self.vector_store = MilvusVectorStore(
            collection_name=self.collection_name,
            connection_args={"host": "localhost", "port": "19530"},
            embedding_dim=128
        )
        
        # Clear any existing data
        self.vector_store.clear()
        
        # Create sample documents and embeddings
        self.documents = [
            {
                "content": "This is a sample document about artificial intelligence.",
                "source": "example.txt",
                "author": "John Doe"
            },
            {
                "content": "Machine learning is a subset of artificial intelligence.",
                "source": "example.txt",
                "author": "Jane Smith"
            },
            {
                "content": "Natural language processing is used for text analysis.",
                "source": "example.txt",
                "author": "Bob Johnson"
            },
            {
                "content": "Trí tuệ nhân tạo đang phát triển nhanh chóng.",  # Vietnamese: Artificial intelligence is developing rapidly
                "source": "vietnamese.txt",
                "author": "Nguyen Van A"
            },
            {
                "content": "Học máy là một phần của trí tuệ nhân tạo.",  # Vietnamese: Machine learning is a part of artificial intelligence
                "source": "vietnamese.txt",
                "author": "Tran Thi B"
            }
        ]
        
        # Create random embeddings for demonstration
        np.random.seed(42)  # For reproducibility
        self.embeddings = [np.random.rand(128) for _ in range(len(self.documents))]
        
        # Normalize embeddings for cosine similarity
        self.embeddings = [embedding / np.linalg.norm(embedding) for embedding in self.embeddings]
        
        # Add documents to the vector store
        self.ids = [f"doc{i+1}" for i in range(len(self.documents))]
        self.vector_store.add(self.ids, self.embeddings, self.documents)
    
    def tearDown(self):
        """Tear down test fixtures."""
        # Clear the collection
        self.vector_store.clear()
        
        # Close the vector store
        self.vector_store.close()
    
    def test_count(self):
        """Test counting documents."""
        count = self.vector_store.count()
        self.assertEqual(count, len(self.documents))
    
    def test_search(self):
        """Test searching for documents."""
        # Search for documents similar to the first document
        results = self.vector_store.search(self.embeddings[0], top_k=3)
        
        # Check that we got the expected number of results
        self.assertEqual(len(results), 3)
        
        # Check that the first result is the first document (exact match)
        self.assertEqual(results[0]["id"], self.ids[0])
        
        # Check that all results have the expected fields
        for result in results:
            self.assertIn("id", result)
            self.assertIn("content", result)
            self.assertIn("source", result)
            self.assertIn("score", result)
            self.assertIn("author", result)
    
    def test_hybrid_search(self):
        """Test hybrid search."""
        # Perform hybrid search
        results = self.vector_store.hybrid_search(
            query_embedding=self.embeddings[0],
            query_text="artificial intelligence",
            top_k=3,
            vector_weight=0.7,
            text_weight=0.3
        )
        
        # Check that we got the expected number of results
        self.assertEqual(len(results), 3)
        
        # Check that all results have the expected fields
        for result in results:
            self.assertIn("id", result)
            self.assertIn("content", result)
            self.assertIn("source", result)
            self.assertIn("score", result)
            self.assertIn("vector_score", result)
            self.assertIn("text_score", result)
            self.assertIn("author", result)
    
    def test_vietnamese_search(self):
        """Test searching for Vietnamese documents."""
        # Search for documents similar to the Vietnamese document
        results = self.vector_store.search(self.embeddings[3], top_k=2)
        
        # Check that we got the expected number of results
        self.assertEqual(len(results), 2)
        
        # Check that the first result is the Vietnamese document (exact match)
        self.assertEqual(results[0]["id"], self.ids[3])
        
        # Check that the results contain Vietnamese content
        vietnamese_results = [r for r in results if "trí tuệ nhân tạo" in r["content"].lower()]
        self.assertGreater(len(vietnamese_results), 0)
    
    def test_get_document(self):
        """Test getting a document by ID."""
        # Get a document by ID
        document = self.vector_store.get_document(self.ids[0])
        
        # Check that we got the expected document
        self.assertEqual(document["id"], self.ids[0])
        self.assertEqual(document["content"], self.documents[0]["content"])
        self.assertEqual(document["source"], self.documents[0]["source"])
        self.assertEqual(document["author"], self.documents[0]["author"])
    
    def test_get_documents(self):
        """Test getting multiple documents by ID."""
        # Get multiple documents by ID
        documents = self.vector_store.get_documents([self.ids[0], self.ids[2]])
        
        # Check that we got the expected number of documents
        self.assertEqual(len(documents), 2)
        
        # Check that we got the expected documents
        self.assertEqual(documents[0]["id"], self.ids[0])
        self.assertEqual(documents[1]["id"], self.ids[2])
    
    def test_delete(self):
        """Test deleting documents."""
        # Delete a document
        self.vector_store.delete([self.ids[0]])
        
        # Check that the document was deleted
        count = self.vector_store.count()
        self.assertEqual(count, len(self.documents) - 1)
        
        # Check that the document is no longer retrievable
        document = self.vector_store.get_document(self.ids[0])
        self.assertIsNone(document)
    
    def test_advanced_search(self):
        """Test advanced search with filtering."""
        # Perform advanced search with filtering
        results = self.vector_store.advanced_search(
            query_embedding=self.embeddings[0],
            query_text="artificial intelligence",
            top_k=3,
            hybrid_search=True,
            filter_by={
                "source": "example.txt"
            }
        )
        
        # Check that we got results
        self.assertGreater(len(results), 0)
        
        # Check that all results have the expected source
        for result in results:
            self.assertEqual(result["source"], "example.txt")
    
    def test_partitioning(self):
        """Test partitioning by source."""
        # Create a new vector store with partitioning
        partition_vector_store = MilvusVectorStore(
            collection_name=f"{self.collection_name}_partitioned",
            connection_args={"host": "localhost", "port": "19530"},
            embedding_dim=128,
            partition_key="source",
            auto_create_partitions=True
        )
        
        try:
            # Add documents
            partition_vector_store.add(self.ids, self.embeddings, self.documents)
            
            # Get partitions
            partitions = partition_vector_store.get_partitions()
            
            # Check that we have the expected partitions
            self.assertIn("example.txt", partitions)
            self.assertIn("vietnamese.txt", partitions)
            
            # Search in a specific partition
            results = partition_vector_store.search(
                query_embedding=self.embeddings[0],
                top_k=3,
                partition_names=["example.txt"]
            )
            
            # Check that all results have the expected source
            for result in results:
                self.assertEqual(result["source"], "example.txt")
        
        finally:
            # Clean up
            partition_vector_store.clear()
            partition_vector_store.close()
    
    def test_optimize(self):
        """Test optimizing the vector store."""
        # Optimize the vector store
        result = self.vector_store.optimize()
        
        # Check that optimization was successful
        self.assertTrue(result)
    
    def test_auto_tune(self):
        """Test auto-tuning the vector store."""
        # Auto-tune the vector store
        result = self.vector_store.optimize(auto_tune=True)
        
        # Check that auto-tuning was successful
        self.assertTrue(result)

if __name__ == '__main__':
    unittest.main()
