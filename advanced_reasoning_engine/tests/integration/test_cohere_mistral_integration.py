"""
Integration tests for Cohere and Mistral providers.

This module contains tests to verify that the Cohere and Mistral providers
are correctly integrated with the system.
"""

import unittest
import os
from unittest.mock import patch, MagicMock

from src.deep_research_core.models.api.cohere import cohere_provider
from src.deep_research_core.models.api.mistral import mistral_provider


class TestCohereIntegration(unittest.TestCase):
    """
    Integration tests for the Cohere provider.
    """

    def setUp(self):
        """
        Set up test environment.
        """
        # Skip tests if API key is not available
        if not os.environ.get("COHERE_API_KEY"):
            self.skipTest("COHERE_API_KEY environment variable not set")

    def test_provider_initialization(self):
        """
        Test that the provider is correctly initialized.
        """
        self.assertIsNotNone(cohere_provider)
        self.assertIsNotNone(cohere_provider.provider)
        self.assertIsNotNone(cohere_provider.api_key)

    @patch('src.deep_research_core.providers.cohere.CohereProvider.generate')
    def test_generate_text(self, mock_generate):
        """
        Test text generation with the Cohere provider.
        """
        # Mock the generate method
        mock_response = {
            "choices": [
                {
                    "text": "Generated text from Cohere",
                    "finish_reason": "stop"
                }
            ],
            "usage": {
                "prompt_tokens": 10,
                "completion_tokens": 20,
                "total_tokens": 30
            },
            "model": "command",
            "provider": "cohere"
        }
        mock_generate.return_value = mock_response

        # Call generate
        result = cohere_provider.generate(
            prompt="Test prompt",
            model="command",
            max_tokens=100,
            temperature=0.7
        )

        # Verify result
        self.assertEqual(result["text"], "Generated text from Cohere")
        self.assertEqual(result["model"], "command")
        self.assertEqual(result["provider"], "cohere")
        self.assertIn("usage", result)

    @patch('src.deep_research_core.providers.cohere.CohereProvider.embed')
    def test_get_embedding(self, mock_embed):
        """
        Test embedding generation with the Cohere provider.
        """
        # Mock the embed method
        mock_response = {
            "embeddings": [[0.1, 0.2, 0.3]],
            "model": "embed-english-v3.0",
            "provider": "cohere"
        }
        mock_embed.return_value = mock_response

        # Call get_embedding
        result = cohere_provider.get_embedding(
            text="Test text",
            model="embed-english-v3.0"
        )

        # Verify result
        self.assertEqual(result, [0.1, 0.2, 0.3])


class TestMistralIntegration(unittest.TestCase):
    """
    Integration tests for the Mistral provider.
    """

    def setUp(self):
        """
        Set up test environment.
        """
        # Skip tests if API key is not available
        if not os.environ.get("MISTRAL_API_KEY"):
            self.skipTest("MISTRAL_API_KEY environment variable not set")

    def test_provider_initialization(self):
        """
        Test that the provider is correctly initialized.
        """
        self.assertIsNotNone(mistral_provider)
        self.assertIsNotNone(mistral_provider.provider)
        self.assertIsNotNone(mistral_provider.api_key)

    @patch('src.deep_research_core.providers.mistral.MistralProvider.generate')
    def test_generate_text(self, mock_generate):
        """
        Test text generation with the Mistral provider.
        """
        # Mock the generate method
        mock_response = {
            "choices": [
                {
                    "text": "Generated text from Mistral",
                    "finish_reason": "stop"
                }
            ],
            "usage": {
                "prompt_tokens": 10,
                "completion_tokens": 20,
                "total_tokens": 30
            },
            "model": "mistral-medium",
            "provider": "mistral"
        }
        mock_generate.return_value = mock_response

        # Call generate
        result = mistral_provider.generate(
            prompt="Test prompt",
            model="mistral-medium",
            max_tokens=100,
            temperature=0.7
        )

        # Verify result
        self.assertEqual(result["text"], "Generated text from Mistral")
        self.assertEqual(result["model"], "mistral-medium")
        self.assertEqual(result["provider"], "mistral")
        self.assertIn("usage", result)

    @patch('src.deep_research_core.providers.mistral.MistralProvider.embed')
    def test_get_embedding(self, mock_embed):
        """
        Test embedding generation with the Mistral provider.
        """
        # Mock the embed method
        mock_response = {
            "embeddings": [[0.4, 0.5, 0.6]],
            "model": "mistral-embed",
            "provider": "mistral"
        }
        mock_embed.return_value = mock_response

        # Call get_embedding
        result = mistral_provider.get_embedding(
            text="Test text",
            model="mistral-embed"
        )

        # Verify result
        self.assertEqual(result, [0.4, 0.5, 0.6])


if __name__ == "__main__":
    unittest.main()
