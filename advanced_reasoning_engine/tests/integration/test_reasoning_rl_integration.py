"""
Integration tests for reasoning modules and RL tuning modules.

This test suite tests the integration between reasoning modules (CoT, ToT, RAG)
and RL tuning modules (PPO, SFT, GRPO).
"""

import os
import sys
import unittest
from typing import Dict, Any, List, Optional

# Add the src directory to the path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

from src.deep_research_core.utils.test_utils import setup_test_environment, restore_environment, get_test_model
from src.deep_research_core.reasoning.cot import ChainOfThought
from src.deep_research_core.reasoning.tot import TreeOfThought
from src.deep_research_core.rl_tuning.providers.ppo_providers import get_ppo_provider
from src.deep_research_core.rl_tuning.providers.sft_providers import get_sft_provider
from src.deep_research_core.rl_tuning.grpo.base import FormatRewardFunction


class TestReasoningRLIntegration(unittest.TestCase):
    """Test integration between reasoning modules and RL tuning modules."""
    
    def setUp(self):
        """Set up the test environment."""
        self.original_env = setup_test_environment()
        
        # Define a simple reward function for testing
        def simple_reward_fn(prompt, response):
            return 0.5
            
        self.reward_fn = simple_reward_fn
        
        # Initialize models
        self.model_name = get_test_model("openrouter", "cheap")
        
        # Initialize reasoning modules
        self.cot = ChainOfThought(model=self.model_name, provider="openrouter")
        self.tot = TreeOfThought(model=self.model_name, provider="openrouter")
        
        # Initialize RL modules
        self.ppo = get_ppo_provider(
            model_name=self.model_name,
            provider_type="openrouter",
            reward_fn=self.reward_fn
        )
        
        self.sft = get_sft_provider(
            model_name=self.model_name,
            provider_type="openrouter"
        )
    
    def tearDown(self):
        """Restore the original environment."""
        restore_environment(self.original_env)
    
    def test_cot_with_ppo_model(self):
        """Test ChainOfThought with a PPO-tuned model."""
        # Create a custom PPO model that wraps the original model
        class PPOWrappedModel:
            def __init__(self, ppo):
                self.ppo = ppo
            
            def generate(self, prompt, **kwargs):
                return self.ppo.generate(prompt)
        
        # Create a CoT instance with the PPO-wrapped model
        cot_with_ppo = ChainOfThought(
            model=self.model_name,
            provider="openrouter",
            custom_model=PPOWrappedModel(self.ppo)
        )
        
        # Test reasoning
        problem = "What is the capital of France?"
        result = cot_with_ppo.solve(problem)
        
        self.assertIsNotNone(result)
        self.assertIsInstance(result, str)
        self.assertTrue(len(result) > 0)
    
    def test_tot_with_sft_model(self):
        """Test TreeOfThought with an SFT-tuned model."""
        # Create a custom SFT model that wraps the original model
        class SFTWrappedModel:
            def __init__(self, sft):
                self.sft = sft
            
            def generate(self, prompt, **kwargs):
                return self.sft.generate(prompt)
        
        # Create a ToT instance with the SFT-wrapped model
        tot_with_sft = TreeOfThought(
            model=self.model_name,
            provider="openrouter",
            custom_model=SFTWrappedModel(self.sft)
        )
        
        # Test reasoning
        problem = "What is the capital of France?"
        result = tot_with_sft.solve(problem)
        
        self.assertIsNotNone(result)
        self.assertIsInstance(result, str)
        self.assertTrue(len(result) > 0)
    
    def test_reward_function_for_reasoning_quality(self):
        """Test a reward function that evaluates reasoning quality."""
        # Create a reward function that rewards step-by-step reasoning
        def reasoning_quality_reward(prompt, response):
            # Check for reasoning indicators
            indicators = ["first", "second", "third", "because", "therefore", "thus"]
            score = 0.0
            
            # Count the number of reasoning indicators
            for indicator in indicators:
                if indicator.lower() in response.lower():
                    score += 0.1
            
            # Cap the score at 1.0
            return min(score, 1.0)
        
        # Initialize PPO with the reasoning quality reward function
        ppo_with_reasoning_reward = get_ppo_provider(
            model_name=self.model_name,
            provider_type="openrouter",
            reward_fn=reasoning_quality_reward
        )
        
        # Test generation with a prompt that encourages reasoning
        prompt = "Explain step by step why the sky appears blue."
        response = ppo_with_reasoning_reward.generate(prompt)
        
        self.assertIsNotNone(response)
        self.assertIsInstance(response, str)
        
        # Check if the response contains reasoning indicators
        indicators = ["first", "second", "because", "therefore"]
        has_indicator = any(indicator.lower() in response.lower() for indicator in indicators)
        
        self.assertTrue(has_indicator, f"Response should contain reasoning indicators: {response}")


if __name__ == "__main__":
    unittest.main()
