#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
<PERSON><PERSON><PERSON> thử tích hợp cho hệ thống cache tìm kiếm.

Module này chứa các kiểm thử tích hợp để kiểm tra sự tương tác giữa các thành phần
của hệ thống cache tìm kiếm, bao gồm:
- AdaptiveSearchCache
- SearchCacheManager
- CachedWebSearchAgent
- Decorator cached_search
"""

import os
import sys
import time
import json
import unittest
import tempfile
import threading
from unittest.mock import patch, MagicMock, call

# Thêm thư mục gốc vào sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.deep_research_core.optimization.caching.adaptive_search_cache import AdaptiveSearchCache
from src.deep_research_core.agents.search_cache_integration import (
    <PERSON><PERSON>ache<PERSON>anager,
    get_cache_manager,
    cached_search,
    searxng_cache_key,
    crawlee_cache_key,
    google_cache_key,
    unified_cache_key
)
from src.deep_research_core.agents.web_search_agent_cache import (
    CachedWebSearchAgent,
    patch_web_search_agent
)

class MockWebSearchAgent:
    """Mock WebSearchAgent cho kiểm thử."""
    
    def __init__(self):
        """Khởi tạo mock agent."""
        self.search_called = 0
        self.searxng_called = 0
        self.crawlee_called = 0
    
    def search(self, query, **kwargs):
        """Mock phương thức search."""
        self.search_called += 1
        return {
            "query": query,
            "results": [{"title": f"Result {i} for {query}"} for i in range(5)],
            "count": 5,
            "language": kwargs.get("language", "en"),
            "timestamp": time.time()
        }
    
    def search_with_searxng(self, query, **kwargs):
        """Mock phương thức search_with_searxng."""
        self.searxng_called += 1
        return {
            "query": query,
            "results": [{"title": f"SearXNG Result {i} for {query}"} for i in range(5)],
            "count": 5,
            "language": kwargs.get("language", "en"),
            "timestamp": time.time()
        }
    
    def search_with_crawlee(self, query, **kwargs):
        """Mock phương thức search_with_crawlee."""
        self.crawlee_called += 1
        return {
            "query": query,
            "results": [{"title": f"Crawlee Result {i} for {query}"} for i in range(5)],
            "count": 5,
            "language": kwargs.get("language", "en"),
            "timestamp": time.time()
        }


class TestDecoratorIntegration(unittest.TestCase):
    """Kiểm thử tích hợp decorator cached_search."""
    
    def setUp(self):
        """Thiết lập môi trường kiểm thử."""
        # Tạo thư mục tạm cho cache
        self.temp_dir = tempfile.mkdtemp()
        
        # Tạo cache manager
        self.cache_manager = SearchCacheManager(
            cache_dir=self.temp_dir,
            default_ttl=60,
            enable_analytics=True,
            enable_semantic_search=True
        )
        
        # Ghi đè cache manager toàn cục
        from src.deep_research_core.agents.search_cache_integration import _cache_manager
        self._original_cache_manager = _cache_manager
        from src.deep_research_core.agents.search_cache_integration import _cache_manager as cm
        cm = self.cache_manager
    
    def tearDown(self):
        """Dọn dẹp sau kiểm thử."""
        # Khôi phục cache manager toàn cục
        from src.deep_research_core.agents.search_cache_integration import _cache_manager as cm
        cm = self._original_cache_manager
        
        # Xóa thư mục tạm
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_decorator_basic(self):
        """Kiểm thử cơ bản decorator cached_search."""
        # Định nghĩa hàm với decorator
        call_count = 0
        
        @cached_search(cache_name="test_cache", ttl=60)
        def test_search(query, language="en"):
            nonlocal call_count
            call_count += 1
            return {
                "query": query,
                "results": [{"title": f"Result {i} for {query}"} for i in range(5)],
                "count": 5,
                "language": language,
                "timestamp": time.time()
            }
        
        # Gọi hàm lần đầu (cache miss)
        result1 = test_search("test query", language="en")
        
        # Kiểm tra hàm được gọi
        self.assertEqual(call_count, 1)
        
        # Gọi hàm lần thứ hai (cache hit)
        result2 = test_search("test query", language="en")
        
        # Kiểm tra hàm không được gọi lại
        self.assertEqual(call_count, 1)
        
        # Kiểm tra kết quả giống nhau
        self.assertEqual(result1["query"], result2["query"])
        self.assertEqual(len(result1["results"]), len(result2["results"]))
    
    def test_decorator_with_key_func(self):
        """Kiểm thử decorator cached_search với hàm tạo khóa."""
        # Định nghĩa hàm tạo khóa
        def custom_key_func(query, **kwargs):
            return f"custom:{query}:{kwargs.get('language', 'en')}"
        
        # Định nghĩa hàm với decorator
        call_count = 0
        
        @cached_search(cache_name="test_cache", ttl=60, key_func=custom_key_func)
        def test_search(query, language="en"):
            nonlocal call_count
            call_count += 1
            return {
                "query": query,
                "results": [{"title": f"Result {i} for {query}"} for i in range(5)],
                "count": 5,
                "language": language,
                "timestamp": time.time()
            }
        
        # Gọi hàm lần đầu (cache miss)
        result1 = test_search("test query", language="en")
        
        # Kiểm tra hàm được gọi
        self.assertEqual(call_count, 1)
        
        # Gọi hàm lần thứ hai (cache hit)
        result2 = test_search("test query", language="en")
        
        # Kiểm tra hàm không được gọi lại
        self.assertEqual(call_count, 1)
        
        # Kiểm tra kết quả giống nhau
        self.assertEqual(result1["query"], result2["query"])
        self.assertEqual(len(result1["results"]), len(result2["results"]))
    
    def test_decorator_with_searxng_key_func(self):
        """Kiểm thử decorator cached_search với searxng_cache_key."""
        # Định nghĩa hàm với decorator
        call_count = 0
        
        @cached_search(cache_name="searxng", ttl=60, key_func=searxng_cache_key)
        def search_with_searxng(query, num_results=10, language="en"):
            nonlocal call_count
            call_count += 1
            return {
                "query": query,
                "results": [{"title": f"Result {i} for {query}"} for i in range(num_results)],
                "count": num_results,
                "language": language,
                "timestamp": time.time()
            }
        
        # Gọi hàm lần đầu (cache miss)
        result1 = search_with_searxng("test query", num_results=5, language="en")
        
        # Kiểm tra hàm được gọi
        self.assertEqual(call_count, 1)
        
        # Gọi hàm lần thứ hai (cache hit)
        result2 = search_with_searxng("test query", num_results=5, language="en")
        
        # Kiểm tra hàm không được gọi lại
        self.assertEqual(call_count, 1)
        
        # Kiểm tra kết quả giống nhau
        self.assertEqual(result1["query"], result2["query"])
        self.assertEqual(len(result1["results"]), len(result2["results"]))


class TestCacheManagerIntegration(unittest.TestCase):
    """Kiểm thử tích hợp SearchCacheManager."""
    
    def setUp(self):
        """Thiết lập môi trường kiểm thử."""
        # Tạo thư mục tạm cho cache
        self.temp_dir = tempfile.mkdtemp()
        
        # Tạo cache manager
        self.cache_manager = SearchCacheManager(
            cache_dir=self.temp_dir,
            default_ttl=60,
            enable_analytics=True,
            enable_semantic_search=True
        )
    
    def tearDown(self):
        """Dọn dẹp sau kiểm thử."""
        # Xóa thư mục tạm
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_get_cache_creates_directories(self):
        """Kiểm thử get_cache tạo thư mục."""
        # Lấy cache
        cache = self.cache_manager.get_cache("test_cache")
        
        # Kiểm tra thư mục được tạo
        cache_dir = os.path.join(self.temp_dir, "test_cache")
        self.assertTrue(os.path.exists(cache_dir))
        self.assertTrue(os.path.isdir(cache_dir))
    
    def test_get_cache_returns_same_instance(self):
        """Kiểm thử get_cache trả về cùng một instance."""
        # Lấy cache lần đầu
        cache1 = self.cache_manager.get_cache("test_cache")
        
        # Lấy cache lần thứ hai
        cache2 = self.cache_manager.get_cache("test_cache")
        
        # Kiểm tra cùng một instance
        self.assertIs(cache1, cache2)
    
    def test_get_cache_with_different_params(self):
        """Kiểm thử get_cache với các tham số khác nhau."""
        # Lấy cache với các tham số khác nhau
        cache1 = self.cache_manager.get_cache("test_cache1", ttl=60)
        cache2 = self.cache_manager.get_cache("test_cache2", ttl=120)
        
        # Kiểm tra các tham số
        self.assertEqual(cache1.default_ttl, 60)
        self.assertEqual(cache2.default_ttl, 120)
    
    def test_clear_all(self):
        """Kiểm thử clear_all."""
        # Lấy các cache
        cache1 = self.cache_manager.get_cache("test_cache1")
        cache2 = self.cache_manager.get_cache("test_cache2")
        
        # Đặt giá trị
        cache1.set("key1", "value1")
        cache2.set("key2", "value2")
        
        # Xóa tất cả
        self.cache_manager.clear_all()
        
        # Kiểm tra các cache trống
        self.assertEqual(len(cache1.memory_cache), 0)
        self.assertEqual(len(cache2.memory_cache), 0)
    
    def test_clear_cache(self):
        """Kiểm thử clear_cache."""
        # Lấy các cache
        cache1 = self.cache_manager.get_cache("test_cache1")
        cache2 = self.cache_manager.get_cache("test_cache2")
        
        # Đặt giá trị
        cache1.set("key1", "value1")
        cache2.set("key2", "value2")
        
        # Xóa cache cụ thể
        self.cache_manager.clear_cache("test_cache1")
        
        # Kiểm tra cache1 trống và cache2 không trống
        self.assertEqual(len(cache1.memory_cache), 0)
        self.assertEqual(len(cache2.memory_cache), 1)
    
    def test_get_stats(self):
        """Kiểm thử get_stats."""
        # Lấy các cache
        cache1 = self.cache_manager.get_cache("test_cache1")
        cache2 = self.cache_manager.get_cache("test_cache2")
        
        # Đặt và lấy giá trị
        cache1.set("key1", "value1")
        cache1.get("key1")
        cache1.get("key3", default="default")  # Miss
        
        cache2.set("key2", "value2")
        cache2.get("key2")
        
        # Lấy thống kê
        stats = self.cache_manager.get_stats()
        
        # Kiểm tra thống kê
        self.assertEqual(stats["total_caches"], 2)
        self.assertIn("test_cache1", stats["caches"])
        self.assertIn("test_cache2", stats["caches"])
        self.assertEqual(stats["caches"]["test_cache1"]["memory_hits"], 1)
        self.assertEqual(stats["caches"]["test_cache1"]["misses"], 1)
        self.assertEqual(stats["caches"]["test_cache2"]["memory_hits"], 1)
        self.assertEqual(stats["caches"]["test_cache2"]["misses"], 0)
        self.assertEqual(stats["total_hits"], 2)
        self.assertEqual(stats["total_misses"], 1)
        self.assertEqual(stats["hit_ratio"], 2/3)


class TestFullIntegration(unittest.TestCase):
    """Kiểm thử tích hợp đầy đủ."""
    
    def setUp(self):
        """Thiết lập môi trường kiểm thử."""
        # Tạo thư mục tạm cho cache
        self.temp_dir = tempfile.mkdtemp()
        
        # Tạo cache manager
        self.cache_manager = SearchCacheManager(
            cache_dir=self.temp_dir,
            default_ttl=60,
            enable_analytics=True,
            enable_semantic_search=True
        )
        
        # Ghi đè cache manager toàn cục
        from src.deep_research_core.agents.search_cache_integration import _cache_manager
        self._original_cache_manager = _cache_manager
        from src.deep_research_core.agents.search_cache_integration import _cache_manager as cm
        cm = self.cache_manager
        
        # Tạo mock agent
        self.mock_agent = MockWebSearchAgent()
        
        # Patch agent
        self.cached_agent = patch_web_search_agent(
            self.mock_agent,
            cache_manager=self.cache_manager,
            enable_cache=True,
            default_ttl=60,
            enable_semantic_search=True
        )
    
    def tearDown(self):
        """Dọn dẹp sau kiểm thử."""
        # Khôi phục cache manager toàn cục
        from src.deep_research_core.agents.search_cache_integration import _cache_manager as cm
        cm = self._original_cache_manager
        
        # Xóa thư mục tạm
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_full_integration(self):
        """Kiểm thử tích hợp đầy đủ."""
        # Định nghĩa hàm với decorator
        call_count = 0
        
        @cached_search(cache_name="test_cache", ttl=60)
        def test_search(query, language="en"):
            nonlocal call_count
            call_count += 1
            return {
                "query": query,
                "results": [{"title": f"Result {i} for {query}"} for i in range(5)],
                "count": 5,
                "language": language,
                "timestamp": time.time()
            }
        
        # Gọi các phương thức tìm kiếm
        self.mock_agent.search("test query", language="en")
        self.mock_agent.search_with_searxng("test query", language="en")
        test_search("test query", language="en")
        
        # Kiểm tra các phương thức được gọi
        self.assertEqual(self.mock_agent.search_called, 1)
        self.assertEqual(self.mock_agent.searxng_called, 1)
        self.assertEqual(call_count, 1)
        
        # Gọi lại các phương thức (cache hit)
        self.mock_agent.search("test query", language="en")
        self.mock_agent.search_with_searxng("test query", language="en")
        test_search("test query", language="en")
        
        # Kiểm tra các phương thức không được gọi lại
        self.assertEqual(self.mock_agent.search_called, 1)
        self.assertEqual(self.mock_agent.searxng_called, 1)
        self.assertEqual(call_count, 1)
        
        # Lấy thống kê
        stats = self.cache_manager.get_stats()
        
        # Kiểm tra thống kê
        self.assertGreaterEqual(stats["total_caches"], 3)
        self.assertIn("unified", stats["caches"])
        self.assertIn("searxng", stats["caches"])
        self.assertIn("test_cache", stats["caches"])
        self.assertEqual(stats["caches"]["unified"]["memory_hits"], 1)
        self.assertEqual(stats["caches"]["searxng"]["memory_hits"], 1)
        self.assertEqual(stats["caches"]["test_cache"]["memory_hits"], 1)


if __name__ == "__main__":
    unittest.main()
