"""
Integration tests for provider integration with reasoning components.

This module tests the integration of providers with reasoning components.
"""

import unittest
from unittest.mock import patch, MagicMock
import os

from src.deep_research_core.providers.cohere import CohereProvider
from src.deep_research_core.providers.mistral import MistralProvider
from src.deep_research_core.reasoning.cot import ChainOfThought


class TestProviderIntegration(unittest.TestCase):
    """
    Test cases for provider integration with reasoning components.
    """

    def setUp(self):
        """
        Set up test environment.
        """
        # Mock environment variables
        self.env_patcher = patch.dict(os.environ, {
            "COHERE_API_KEY": "test-cohere-key",
            "MISTRAL_API_KEY": "test-mistral-key"
        })
        self.env_patcher.start()

    def tearDown(self):
        """
        Clean up after tests.
        """
        self.env_patcher.stop()

    @patch("cohere.Client")
    def test_cohere_with_cot(self, mock_client_class):
        """
        Test Cohere provider with Chain of Thought.
        """
        # Mock Cohere client
        mock_client = MagicMock()
        mock_client_class.return_value = mock_client

        # Mock generate method
        mock_response = MagicMock()
        mock_response.generations = [MagicMock(text="Step 1: Think about the problem\nStep 2: Solve it\nFinal answer: 42")]
        mock_response.meta.billed_units.input_tokens = 10
        mock_response.meta.billed_units.output_tokens = 20
        mock_client.generate.return_value = mock_response

        # Create provider
        provider = CohereProvider(model="command")

        # Create CoT with provider
        cot = ChainOfThought(
            model=provider,
            language="en",
            num_steps=3,
            verbose=True
        )

        # Test reasoning
        result = cot.reason("What is the meaning of life?")

        # Verify result
        self.assertIsNotNone(result)
        self.assertEqual(result.query, "What is the meaning of life?")
        self.assertIn("Step 1", result.answer)
        self.assertIn("Final answer: 42", result.answer)
        self.assertEqual(result.metadata["method"], "chain_of_thought")
        self.assertEqual(result.metadata["language"], "en")
        self.assertEqual(result.metadata["final_answer"], "42")

    @patch("mistralai.client.MistralClient")
    def test_mistral_with_cot(self, mock_client_class):
        """
        Test Mistral provider with Chain of Thought.
        """
        # Mock Mistral client
        mock_client = MagicMock()
        mock_client_class.return_value = mock_client

        # Mock chat method
        mock_choice = MagicMock()
        mock_choice.message.content = "Step 1: Think about the problem\nStep 2: Solve it\nFinal answer: 42"
        mock_choice.finish_reason = "stop"

        mock_usage = MagicMock()
        mock_usage.prompt_tokens = 10
        mock_usage.completion_tokens = 20
        mock_usage.total_tokens = 30

        mock_response = MagicMock()
        mock_response.choices = [mock_choice]
        mock_response.usage = mock_usage
        mock_client.chat.return_value = mock_response

        # Create provider
        provider = MistralProvider(model="mistral-medium")

        # Create CoT with provider
        cot = ChainOfThought(
            model=provider,
            language="en",
            num_steps=3,
            verbose=True
        )

        # Test reasoning
        result = cot.reason("What is the meaning of life?")

        # Verify result
        self.assertIsNotNone(result)
        self.assertEqual(result.query, "What is the meaning of life?")
        self.assertIn("Step 1", result.answer)
        self.assertIn("Final answer: 42", result.answer)
        self.assertEqual(result.metadata["method"], "chain_of_thought")
        self.assertEqual(result.metadata["language"], "en")
        self.assertEqual(result.metadata["final_answer"], "42")


if __name__ == "__main__":
    unittest.main()
