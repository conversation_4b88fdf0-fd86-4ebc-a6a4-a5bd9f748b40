"""
Integration tests for vector store RAG implementations.

These tests verify that FAISSRAG, PineconeRAG, and WeaviateRAG work correctly
and can be used interchangeably with the same interface.
"""

import os
import unittest
import tempfile
import shutil
from unittest.mock import patch

from src.deep_research_core.reasoning import BaseRAG
from src.deep_research_core.reasoning.faiss_rag import FAISSR<PERSON>
from src.deep_research_core.reasoning.pinecone_rag import <PERSON>coneRA<PERSON>
from src.deep_research_core.reasoning.weaviate_rag import <PERSON><PERSON>te<PERSON><PERSON>
from src.deep_research_core.config.monitoring_config import initialize_monitoring, shutdown_monitoring

# Sample documents for testing
SAMPLE_DOCUMENTS = [
    {
        "content": "Python is a high-level, interpreted programming language known for its readability and versatility.",
        "source": "Programming Languages",
        "title": "Python Overview",
        "date": "2023-01-10"
    },
    {
        "content": "JavaScript is a scripting language that enables interactive web pages and is an essential part of web applications.",
        "source": "Programming Languages",
        "title": "JavaScript Overview",
        "date": "2023-02-20"
    },
    {
        "content": "Machine learning is a subset of artificial intelligence that enables systems to learn and improve from experience.",
        "source": "AI Technologies",
        "title": "Machine Learning Basics",
        "date": "2023-03-01"
    },
    {
        "content": "Deep learning is a subset of machine learning that uses neural networks with many layers.",
        "source": "AI Technologies",
        "title": "Deep Learning Fundamentals",
        "date": "2023-03-15"
    },
    {
        "content": "Natural Language Processing (NLP) is a field of AI that focuses on the interaction between computers and human language.",
        "source": "AI Technologies",
        "title": "NLP Overview",
        "date": "2023-04-05"
    }
]

# Test queries
TEST_QUERIES = [
    "What is Python used for?",
    "Explain the relationship between machine learning and deep learning",
    "How is JavaScript used in web development?",
    "What is NLP in artificial intelligence?"
]


class TestVectorStoresIntegration(unittest.TestCase):
    """Integration tests for vector store RAG implementations."""
    
    @classmethod
    def setUpClass(cls):
        """Set up the test class."""
        # Initialize monitoring
        initialize_monitoring()
        
        # Create a temporary directory for FAISS index
        cls.temp_dir = tempfile.mkdtemp()
        cls.faiss_index_path = os.path.join(cls.temp_dir, "faiss_index")
        
        # Check if we have the necessary API keys
        cls.has_pinecone = os.environ.get("PINECONE_API_KEY") is not None
        cls.has_weaviate = os.environ.get("WEAVIATE_API_KEY") is not None
    
    @classmethod
    def tearDownClass(cls):
        """Clean up after the test class."""
        # Shutdown monitoring
        shutdown_monitoring()
        
        # Remove the temporary directory
        shutil.rmtree(cls.temp_dir)
    
    def setUp(self):
        """Set up the test case."""
        # Initialize FAISSRAG
        self.faiss_rag = FAISSRAG(
            provider="openai",
            model="gpt-4o",
            temperature=0.7,
            max_tokens=2000,
            embedding_model="text-embedding-ada-002",
            index_path=self.faiss_index_path,
            embedding_dim=1536,
            index_type="Flat",
            metric_type="ip",
            use_gpu=False,
            top_k=5
        )
        
        # Initialize PineconeRAG if we have the API key
        if self.has_pinecone:
            self.pinecone_rag = PineconeRAG(
                provider="openai",
                model="gpt-4o",
                temperature=0.7,
                max_tokens=2000,
                embedding_model="text-embedding-ada-002",
                api_key=os.environ.get("PINECONE_API_KEY"),
                environment=os.environ.get("PINECONE_ENVIRONMENT", "us-west1-gcp"),
                index_name="test-index",
                namespace="test-namespace",
                dimension=1536,
                metric="cosine",
                pod_type="p1",
                create_index=True,
                top_k=5
            )
        
        # Initialize WeaviateRAG if we have the API key
        if self.has_weaviate:
            self.weaviate_rag = WeaviateRAG(
                provider="openai",
                model="gpt-4o",
                temperature=0.7,
                max_tokens=2000,
                embedding_model="text-embedding-ada-002",
                url=os.environ.get("WEAVIATE_URL", "http://localhost:8080"),
                api_key=os.environ.get("WEAVIATE_API_KEY"),
                class_name="TestDocument",
                batch_size=100,
                create_class=True,
                vector_index_type="hnsw",
                vector_index_config={"efConstruction": 128, "maxConnections": 64},
                top_k=5
            )
    
    def tearDown(self):
        """Clean up after the test."""
        # Clear FAISS index
        self.faiss_rag.clear()
        
        # Clear Pinecone index if we have it
        if self.has_pinecone:
            self.pinecone_rag.clear()
        
        # Clear Weaviate class if we have it
        if self.has_weaviate:
            self.weaviate_rag.clear()
    
    @unittest.skipIf(not os.environ.get("RUN_FAISS_TESTS"), "FAISS tests disabled")
    def test_faiss_rag_workflow(self):
        """Test the complete workflow with FAISSRAG."""
        # Add documents
        doc_ids = self.faiss_rag.add_documents(SAMPLE_DOCUMENTS)
        self.assertEqual(len(doc_ids), len(SAMPLE_DOCUMENTS))
        
        # Count documents
        count = self.faiss_rag.count()
        self.assertEqual(count, len(SAMPLE_DOCUMENTS))
        
        # Search for documents
        for query in TEST_QUERIES:
            results = self.faiss_rag.search(query)
            self.assertGreater(len(results), 0)
            self.assertIn("content", results[0])
            self.assertIn("source", results[0])
            self.assertIn("score", results[0])
        
        # Process queries
        for query in TEST_QUERIES:
            result = self.faiss_rag.process(query)
            self.assertEqual(result["query"], query)
            self.assertIn("answer", result)
            self.assertGreater(len(result["documents"]), 0)
        
        # Clear documents
        self.faiss_rag.clear()
        count = self.faiss_rag.count()
        self.assertEqual(count, 0)
    
    @unittest.skipIf(not os.environ.get("RUN_PINECONE_TESTS") or not os.environ.get("PINECONE_API_KEY"), "Pinecone tests disabled or API key not set")
    def test_pinecone_rag_workflow(self):
        """Test the complete workflow with PineconeRAG."""
        # Add documents
        doc_ids = self.pinecone_rag.add_documents(SAMPLE_DOCUMENTS)
        self.assertEqual(len(doc_ids), len(SAMPLE_DOCUMENTS))
        
        # Count documents
        count = self.pinecone_rag.count()
        self.assertEqual(count, len(SAMPLE_DOCUMENTS))
        
        # Search for documents
        for query in TEST_QUERIES:
            results = self.pinecone_rag.search(query)
            self.assertGreater(len(results), 0)
            self.assertIn("content", results[0])
            self.assertIn("source", results[0])
            self.assertIn("score", results[0])
        
        # Process queries
        for query in TEST_QUERIES:
            result = self.pinecone_rag.process(query)
            self.assertEqual(result["query"], query)
            self.assertIn("answer", result)
            self.assertGreater(len(result["documents"]), 0)
        
        # Clear documents
        self.pinecone_rag.clear()
        count = self.pinecone_rag.count()
        self.assertEqual(count, 0)
    
    @unittest.skipIf(not os.environ.get("RUN_WEAVIATE_TESTS") or not os.environ.get("WEAVIATE_API_KEY"), "Weaviate tests disabled or API key not set")
    def test_weaviate_rag_workflow(self):
        """Test the complete workflow with WeaviateRAG."""
        # Add documents
        doc_ids = self.weaviate_rag.add_documents(SAMPLE_DOCUMENTS)
        self.assertEqual(len(doc_ids), len(SAMPLE_DOCUMENTS))
        
        # Count documents
        count = self.weaviate_rag.count()
        self.assertEqual(count, len(SAMPLE_DOCUMENTS))
        
        # Search for documents
        for query in TEST_QUERIES:
            results = self.weaviate_rag.search(query)
            self.assertGreater(len(results), 0)
            self.assertIn("content", results[0])
            self.assertIn("source", results[0])
            self.assertIn("score", results[0])
        
        # Process queries
        for query in TEST_QUERIES:
            result = self.weaviate_rag.process(query)
            self.assertEqual(result["query"], query)
            self.assertIn("answer", result)
            self.assertGreater(len(result["documents"]), 0)
        
        # Clear documents
        self.weaviate_rag.clear()
        count = self.weaviate_rag.count()
        self.assertEqual(count, 0)
    
    @unittest.skipIf(not os.environ.get("RUN_FAISS_TESTS"), "FAISS tests disabled")
    def test_rag_implementations_compatibility(self):
        """Test that different RAG implementations are compatible."""
        # Add documents to FAISSRAG
        self.faiss_rag.add_documents(SAMPLE_DOCUMENTS)
        
        # Test that FAISSRAG implements the BaseRAG interface
        self.assertIsInstance(self.faiss_rag, BaseRAG)
        
        # Test common methods
        self.assertTrue(hasattr(self.faiss_rag, "add_documents"))
        self.assertTrue(hasattr(self.faiss_rag, "search"))
        self.assertTrue(hasattr(self.faiss_rag, "process"))
        self.assertTrue(hasattr(self.faiss_rag, "count"))
        self.assertTrue(hasattr(self.faiss_rag, "clear"))
        
        # Test that the methods work as expected
        count = self.faiss_rag.count()
        self.assertEqual(count, len(SAMPLE_DOCUMENTS))
        
        results = self.faiss_rag.search(TEST_QUERIES[0])
        self.assertGreater(len(results), 0)
        
        result = self.faiss_rag.process(TEST_QUERIES[0])
        self.assertEqual(result["query"], TEST_QUERIES[0])
        self.assertIn("answer", result)
        
        self.faiss_rag.clear()
        count = self.faiss_rag.count()
        self.assertEqual(count, 0)


if __name__ == "__main__":
    unittest.main()
