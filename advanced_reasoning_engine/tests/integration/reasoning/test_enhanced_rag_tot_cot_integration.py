"""
Integration tests for EnhancedRAGTOTCOTReasoner.

This module contains integration tests for the EnhancedRAGTOTCOTReasoner class,
which combines RAG, TOT, and COT with advanced optimization.
"""

import os
import unittest
import tempfile
import json
from pathlib import Path
import time

from src.deep_research_core.reasoning.enhanced_rag_tot_cot import EnhancedRAGTOTCOTReasoner
from src.deep_research_core.rag.sqlite_vector import SQLiteVectorRAG

# Set API key for testing
os.environ["OPENROUTER_API_KEY"] = "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"

class TestEnhancedRAGTOTCOTReasonerIntegration(unittest.TestCase):
    """Integration tests for EnhancedRAGTOTCOTReasoner."""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures that are used for all tests."""
        # Create a temporary directory for test files
        cls.temp_dir = tempfile.TemporaryDirectory()
        cls.db_path = os.path.join(cls.temp_dir.name, "test_rag.db")
        
        # Sample documents for testing
        cls.documents = [
            {
                "content": "Trí tuệ nhân tạo (AI) là khả năng của một hệ thống máy tính để thực hiện các nhiệm vụ thường đòi hỏi trí thông minh của con người. AI bao gồm học máy, xử lý ngôn ngữ tự nhiên, thị giác máy tính và nhiều lĩnh vực khác.",
                "metadata": {"source": "AI Overview", "topic": "Artificial Intelligence"}
            },
            {
                "content": "Học máy (Machine Learning) là một nhánh của trí tuệ nhân tạo tập trung vào việc phát triển các thuật toán cho phép máy tính học từ dữ liệu. Các phương pháp học máy phổ biến bao gồm học có giám sát, học không giám sát và học tăng cường.",
                "metadata": {"source": "Machine Learning Basics", "topic": "Machine Learning"}
            },
            {
                "content": "Học sâu (Deep Learning) là một kỹ thuật học máy sử dụng mạng nơ-ron nhân tạo với nhiều lớp ẩn. Học sâu đã đạt được những tiến bộ đáng kể trong các lĩnh vực như nhận dạng hình ảnh, xử lý ngôn ngữ tự nhiên và trò chơi.",
                "metadata": {"source": "Deep Learning Introduction", "topic": "Deep Learning"}
            },
            {
                "content": "Xử lý ngôn ngữ tự nhiên (NLP) là lĩnh vực nghiên cứu tập trung vào tương tác giữa máy tính và ngôn ngữ tự nhiên của con người. NLP bao gồm các nhiệm vụ như phân tích cú pháp, dịch máy, tóm tắt văn bản và trả lời câu hỏi.",
                "metadata": {"source": "NLP Guide", "topic": "Natural Language Processing"}
            },
            {
                "content": "Thị giác máy tính (Computer Vision) là lĩnh vực AI tập trung vào việc giúp máy tính hiểu và diễn giải thông tin từ hình ảnh và video. Các ứng dụng bao gồm nhận dạng đối tượng, phát hiện khuôn mặt và xe tự lái.",
                "metadata": {"source": "Computer Vision Overview", "topic": "Computer Vision"}
            }
        ]
        
        # Initialize SQLiteVectorRAG
        cls.rag = SQLiteVectorRAG(
            db_path=cls.db_path,
            embedding_model="all-MiniLM-L6-v2",
            provider="openrouter",
            model="moonshotai/moonlight-16b-a3b-instruct:free"
        )
        
        # Add documents
        cls.rag.add_documents(cls.documents)
        
        # Initialize EnhancedRAGTOTCOTReasoner
        cls.reasoner = EnhancedRAGTOTCOTReasoner(
            rag_system=cls.rag,
            provider="openrouter",
            model="moonshotai/moonlight-16b-a3b-instruct:free",
            temperature=0.7,
            max_tokens=2000,
            language="vi",
            max_branches=2,
            max_depth=2,
            adaptive=True,
            use_advanced_optimization=True,
            verbose=True,
            use_weight_optimizer=True,
            use_query_classifier=True,
            use_performance_analyzer=True,
            use_vietnamese_optimization=True,
            vietnamese_embedding_model="phobert",
            save_analysis_results=True,
            analysis_results_path=os.path.join(cls.temp_dir.name, "analysis_results.json")
        )

    @classmethod
    def tearDownClass(cls):
        """Clean up after all tests."""
        # Close RAG
        cls.rag.close()
        
        # Remove temporary directory
        cls.temp_dir.cleanup()

    def test_factual_query(self):
        """Test processing a factual query."""
        # Skip this test in CI environment
        if os.environ.get("CI") == "true":
            self.skipTest("Skipping integration test in CI environment")
        
        # Process a factual query
        query = "Trí tuệ nhân tạo là gì?"
        result = self.reasoner.reason(query)
        
        # Check the result
        self.assertEqual(result["query"], query)
        self.assertIn("answer", result)
        self.assertIn("reasoning", result)
        self.assertIn("strategy", result)
        self.assertIn("performance_analysis", result)
        self.assertIn("weights_used", result)
        self.assertIn("query_type", result)
        
        # Check that the query was classified correctly
        self.assertEqual(result["query_type"], "factual")
        
        # Check that RAG was used
        self.assertTrue(result["rag_context_used"])
        
        # Check that the analysis results file was created
        self.assertTrue(os.path.exists(os.path.join(self.temp_dir.name, "analysis_results.json")))

    def test_analytical_query(self):
        """Test processing an analytical query."""
        # Skip this test in CI environment
        if os.environ.get("CI") == "true":
            self.skipTest("Skipping integration test in CI environment")
        
        # Process an analytical query
        query = "So sánh học máy và học sâu"
        result = self.reasoner.reason(query)
        
        # Check the result
        self.assertEqual(result["query"], query)
        self.assertIn("answer", result)
        self.assertIn("reasoning", result)
        self.assertIn("strategy", result)
        self.assertIn("performance_analysis", result)
        self.assertIn("weights_used", result)
        self.assertIn("query_type", result)
        
        # Check that the query was classified correctly
        self.assertEqual(result["query_type"], "analytical")
        
        # Check that all techniques were used
        self.assertTrue(result["strategy"]["use_rag"])
        self.assertTrue(result["strategy"]["use_tot"])
        self.assertTrue(result["strategy"]["use_cot"])

    def test_creative_query(self):
        """Test processing a creative query."""
        # Skip this test in CI environment
        if os.environ.get("CI") == "true":
            self.skipTest("Skipping integration test in CI environment")
        
        # Process a creative query
        query = "Hãy viết một câu chuyện ngắn về trí tuệ nhân tạo trong tương lai"
        result = self.reasoner.reason(query)
        
        # Check the result
        self.assertEqual(result["query"], query)
        self.assertIn("answer", result)
        self.assertIn("reasoning", result)
        self.assertIn("strategy", result)
        self.assertIn("performance_analysis", result)
        self.assertIn("weights_used", result)
        self.assertIn("query_type", result)
        
        # Check that the query was classified correctly
        self.assertEqual(result["query_type"], "creative")
        
        # Check that TOT and COT were used
        self.assertTrue(result["strategy"]["use_tot"])
        self.assertTrue(result["strategy"]["use_cot"])

    def test_provide_feedback(self):
        """Test providing feedback to improve weights."""
        # Skip this test in CI environment
        if os.environ.get("CI") == "true":
            self.skipTest("Skipping integration test in CI environment")
        
        # Process a query
        query = "Xử lý ngôn ngữ tự nhiên là gì?"
        result = self.reasoner.reason(query)
        
        # Get the initial weights
        initial_weights = result["weights_used"]
        
        # Provide feedback
        feedback = {"rag": 0.8, "tot": 0.5, "cot": 0.6}
        updated_weights = self.reasoner.provide_feedback(query, feedback)
        
        # Check that the weights were updated
        self.assertNotEqual(updated_weights, initial_weights)
        
        # Process the same query again
        result2 = self.reasoner.reason(query)
        
        # Check that the new weights were used
        self.assertNotEqual(result2["weights_used"], initial_weights)

    def test_compare_methods(self):
        """Test comparing different reasoning methods."""
        # Skip this test in CI environment
        if os.environ.get("CI") == "true":
            self.skipTest("Skipping integration test in CI environment")
        
        # Compare methods
        query = "Thị giác máy tính là gì và ứng dụng của nó?"
        comparison = self.reasoner.compare_methods_with_analysis(query)
        
        # Check the comparison result
        self.assertEqual(comparison["query"], query)
        self.assertIn("methods", comparison)
        self.assertIn("best_method", comparison)
        
        # Check that all methods were compared
        self.assertIn("cot_only", comparison["methods"])
        self.assertIn("rag_cot", comparison["methods"])
        self.assertIn("tot_cot", comparison["methods"])
        self.assertIn("rag_tot_cot", comparison["methods"])
        
        # Check that each method has analysis
        for method, result in comparison["methods"].items():
            self.assertIn("answer", result)
            self.assertIn("latency", result)
            self.assertIn("analysis", result)

    def test_vietnamese_specific_query(self):
        """Test processing a Vietnamese-specific query."""
        # Skip this test in CI environment
        if os.environ.get("CI") == "true":
            self.skipTest("Skipping integration test in CI environment")
        
        # Process a Vietnamese-specific query
        query = "Giải thích ý nghĩa của câu tục ngữ 'Học thầy không tày học bạn'"
        result = self.reasoner.reason(query)
        
        # Check the result
        self.assertEqual(result["query"], query)
        self.assertIn("answer", result)
        self.assertIn("reasoning", result)
        self.assertIn("strategy", result)
        self.assertIn("performance_analysis", result)
        self.assertIn("weights_used", result)
        self.assertIn("query_type", result)
        
        # Check that the query was classified correctly
        self.assertEqual(result["query_type"], "vietnamese")
        
        # Check that Vietnamese optimization was used
        self.assertTrue(result["strategy"]["use_rag"])
        self.assertTrue(result["strategy"]["use_tot"])
        self.assertTrue(result["strategy"]["use_cot"])

    def test_custom_weights(self):
        """Test using custom weights."""
        # Skip this test in CI environment
        if os.environ.get("CI") == "true":
            self.skipTest("Skipping integration test in CI environment")
        
        # Process a query with custom weights
        query = "Học máy là gì?"
        custom_weights = {"rag": 0.6, "tot": 0.2, "cot": 0.2}
        result = self.reasoner.reason(query, weights=custom_weights)
        
        # Check the result
        self.assertEqual(result["query"], query)
        self.assertIn("answer", result)
        self.assertIn("reasoning", result)
        self.assertIn("strategy", result)
        self.assertIn("performance_analysis", result)
        self.assertIn("weights_used", result)
        
        # Check that the custom weights were used
        self.assertEqual(result["weights_used"], custom_weights)

if __name__ == '__main__':
    unittest.main()
