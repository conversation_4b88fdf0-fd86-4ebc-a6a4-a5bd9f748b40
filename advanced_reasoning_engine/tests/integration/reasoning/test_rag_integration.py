"""
Integration tests for RAG implementations.

These tests verify that different RAG implementations work correctly together
and with other components of the system.
"""

import os
import unittest
import tempfile
from typing import List, Dict, Any

from src.deep_research_core.reasoning import BaseRAG, SQLiteVectorRAG
from src.deep_research_core.config.monitoring_config import initialize_monitoring, shutdown_monitoring


# Sample documents for testing
SAMPLE_DOCUMENTS = [
    {
        "content": "Python is a high-level, interpreted programming language known for its readability and versatility.",
        "source": "Programming Languages Overview",
        "title": "Python Programming Language",
        "date": "2023-01-15"
    },
    {
        "content": "JavaScript is a scripting language that enables interactive web pages and is an essential part of web applications.",
        "source": "Web Development Fundamentals",
        "title": "JavaScript Basics",
        "date": "2023-02-20"
    },
    {
        "content": "Machine learning is a subset of artificial intelligence that enables systems to learn and improve from experience.",
        "source": "AI Technologies",
        "title": "Introduction to Machine Learning",
        "date": "2023-03-10"
    },
    {
        "content": "Deep learning is a subset of machine learning that uses neural networks with many layers.",
        "source": "AI Technologies",
        "title": "Deep Learning Fundamentals",
        "date": "2023-03-15"
    },
    {
        "content": "Natural Language Processing (NLP) is a field of AI that focuses on the interaction between computers and human language.",
        "source": "AI Technologies",
        "title": "NLP Overview",
        "date": "2023-04-05"
    }
]

# Test queries
TEST_QUERIES = [
    "What is Python used for?",
    "Explain the relationship between machine learning and deep learning",
    "How is JavaScript used in web development?",
    "What is NLP in artificial intelligence?"
]


class TestRAGIntegration(unittest.TestCase):
    """Integration tests for RAG implementations."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Initialize monitoring
        initialize_monitoring()
        
        # Create a temporary database file
        self.temp_db_fd, self.temp_db_path = tempfile.mkstemp(suffix=".db")
        
        # Initialize SQLiteVectorRAG with the temporary database
        self.sqlite_rag = SQLiteVectorRAG(
            db_path=self.temp_db_path,
            provider="openai",
            model="gpt-4o",
            temperature=0.7,
            max_tokens=2000,
            embedding_model="all-MiniLM-L6-v2",
            top_k=5
        )
        
        # Try to initialize MilvusRAG if available
        try:
            from src.deep_research_core.reasoning import MilvusRAG
            self.milvus_rag = MilvusRAG(
                provider="openai",
                model="gpt-4o",
                temperature=0.7,
                max_tokens=2000,
                embedding_model="text-embedding-ada-002",
                collection_name="test_collection",
                connection_args={"host": "localhost", "port": "19530"},
                embedding_dim=1536,
                top_k=5
            )
            self.has_milvus = True
        except (ImportError, Exception):
            self.has_milvus = False
    
    def tearDown(self):
        """Tear down test fixtures."""
        # Close the SQLite connection
        self.sqlite_rag.close()
        
        # Close the Milvus connection if available
        if self.has_milvus:
            self.milvus_rag.close()
        
        # Remove the temporary database file
        os.close(self.temp_db_fd)
        os.unlink(self.temp_db_path)
        
        # Shutdown monitoring
        shutdown_monitoring()
    
    def test_sqlite_rag_workflow(self):
        """Test the complete workflow with SQLiteVectorRAG."""
        # Add documents
        doc_ids = self.sqlite_rag.add_documents(SAMPLE_DOCUMENTS)
        self.assertEqual(len(doc_ids), len(SAMPLE_DOCUMENTS))
        
        # Count documents
        count = self.sqlite_rag.count()
        self.assertEqual(count, len(SAMPLE_DOCUMENTS))
        
        # Search for documents
        for query in TEST_QUERIES:
            results = self.sqlite_rag.search(query)
            self.assertGreater(len(results), 0)
            self.assertIn("content", results[0])
            self.assertIn("source", results[0])
            self.assertIn("score", results[0])
        
        # Process queries
        for query in TEST_QUERIES:
            result = self.sqlite_rag.process(query)
            self.assertEqual(result["query"], query)
            self.assertIn("answer", result)
            self.assertGreater(len(result["documents"]), 0)
        
        # Clear documents
        self.sqlite_rag.clear()
        count = self.sqlite_rag.count()
        self.assertEqual(count, 0)
    
    @unittest.skipIf(not os.environ.get("RUN_MILVUS_TESTS"), "Milvus tests disabled")
    def test_milvus_rag_workflow(self):
        """Test the complete workflow with MilvusRAG."""
        if not self.has_milvus:
            self.skipTest("Milvus not available")
        
        # Add documents
        doc_ids = self.milvus_rag.add_documents(SAMPLE_DOCUMENTS)
        self.assertEqual(len(doc_ids), len(SAMPLE_DOCUMENTS))
        
        # Count documents
        count = self.milvus_rag.count()
        self.assertEqual(count, len(SAMPLE_DOCUMENTS))
        
        # Search for documents
        for query in TEST_QUERIES:
            results = self.milvus_rag.search(query)
            self.assertGreater(len(results), 0)
            self.assertIn("content", results[0])
            self.assertIn("source", results[0])
            self.assertIn("score", results[0])
        
        # Process queries
        for query in TEST_QUERIES:
            result = self.milvus_rag.process(query)
            self.assertEqual(result["query"], query)
            self.assertIn("answer", result)
            self.assertGreater(len(result["documents"]), 0)
        
        # Clear documents
        self.milvus_rag.clear()
        count = self.milvus_rag.count()
        self.assertEqual(count, 0)
    
    @unittest.skipIf(not os.environ.get("RUN_MILVUS_TESTS"), "Milvus tests disabled")
    def test_rag_implementations_compatibility(self):
        """Test that different RAG implementations are compatible."""
        if not self.has_milvus:
            self.skipTest("Milvus not available")
        
        # Add documents to both implementations
        sqlite_doc_ids = self.sqlite_rag.add_documents(SAMPLE_DOCUMENTS)
        milvus_doc_ids = self.milvus_rag.add_documents(SAMPLE_DOCUMENTS)
        
        # Verify counts
        sqlite_count = self.sqlite_rag.count()
        milvus_count = self.milvus_rag.count()
        self.assertEqual(sqlite_count, milvus_count)
        
        # Compare search results
        for query in TEST_QUERIES:
            sqlite_results = self.sqlite_rag.search(query)
            milvus_results = self.milvus_rag.search(query)
            
            # Both should return results
            self.assertGreater(len(sqlite_results), 0)
            self.assertGreater(len(milvus_results), 0)
            
            # Both should have the same fields
            self.assertEqual(set(sqlite_results[0].keys()) & {"content", "source", "score"},
                             {"content", "source", "score"})
            self.assertEqual(set(milvus_results[0].keys()) & {"content", "source", "score"},
                             {"content", "source", "score"})
        
        # Compare process results
        for query in TEST_QUERIES:
            sqlite_result = self.sqlite_rag.process(query)
            milvus_result = self.milvus_rag.process(query)
            
            # Both should have the same fields
            self.assertEqual(set(sqlite_result.keys()) & {"query", "answer", "documents"},
                             {"query", "answer", "documents"})
            self.assertEqual(set(milvus_result.keys()) & {"query", "answer", "documents"},
                             {"query", "answer", "documents"})
        
        # Clear both implementations
        self.sqlite_rag.clear()
        self.milvus_rag.clear()
        
        # Verify counts
        sqlite_count = self.sqlite_rag.count()
        milvus_count = self.milvus_rag.count()
        self.assertEqual(sqlite_count, 0)
        self.assertEqual(milvus_count, 0)


if __name__ == "__main__":
    unittest.main()
