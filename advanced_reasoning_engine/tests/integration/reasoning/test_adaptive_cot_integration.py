"""
Integration tests for the Adaptive Chain of Thought reasoning implementation.

This module contains integration tests for the AdaptiveChainOfThought class.
"""

import unittest
from unittest.mock import patch, MagicMock

from deep_research_core.reasoning.adaptive_cot import AdaptiveChainOfThought
from deep_research_core.providers.openai import OpenAIProvider
from deep_research_core.providers.anthropic import AnthropicProvider
from deep_research_core.providers.openrouter import OpenRouterProvider
from deep_research_core.providers.cohere import CohereProvider
from deep_research_core.providers.mistral import MistralProvider


class TestAdaptiveChainOfThoughtIntegration(unittest.TestCase):
    """
    Integration tests for the AdaptiveChainOfThought class.
    """

    def setUp(self):
        """
        Set up test environment.
        """
        # Mock the providers
        self.openai_patcher = patch('deep_research_core.providers.openai.OpenAIProvider')
        self.anthropic_patcher = patch('deep_research_core.providers.anthropic.AnthropicProvider')
        self.openrouter_patcher = patch('deep_research_core.providers.openrouter.OpenRouterProvider')
        self.cohere_patcher = patch('deep_research_core.providers.cohere.CohereProvider')
        self.mistral_patcher = patch('deep_research_core.providers.mistral.MistralProvider')

        self.mock_openai = self.openai_patcher.start()
        self.mock_anthropic = self.anthropic_patcher.start()
        self.mock_openrouter = self.openrouter_patcher.start()
        self.mock_cohere = self.cohere_patcher.start()
        self.mock_mistral = self.mistral_patcher.start()

        # Create mock instances
        self.mock_openai_instance = MagicMock()
        self.mock_anthropic_instance = MagicMock()
        self.mock_openrouter_instance = MagicMock()
        self.mock_cohere_instance = MagicMock()
        self.mock_mistral_instance = MagicMock()

        self.mock_openai.return_value = self.mock_openai_instance
        self.mock_anthropic.return_value = self.mock_anthropic_instance
        self.mock_openrouter.return_value = self.mock_openrouter_instance
        self.mock_cohere.return_value = self.mock_cohere_instance
        self.mock_mistral.return_value = self.mock_mistral_instance

        # Set up mock responses
        self.mock_openai_instance.generate.return_value = (
            "Step 1: Understand the problem\nLet's break down what we're being asked.\n\n"
            "Step 2: Analyze the components\nWe need to consider several factors.\n\n"
            "Step 3: Apply relevant knowledge\nBased on what we know about this topic.\n\n"
            "Final answer: Paris is the capital of France."
        )
        self.mock_anthropic_instance.generate.return_value = (
            "Step 1: Understand the question\nThe question is asking about the capital of France.\n\n"
            "Step 2: Recall geographic knowledge\nFrance is a country in Western Europe.\n\n"
            "Final answer: Paris is the capital of France."
        )
        self.mock_openrouter_instance.generate.return_value = (
            "Step 1: Parse the question\nThe question asks for the capital of France.\n\n"
            "Step 2: Retrieve information\nFrance's capital city is Paris.\n\n"
            "Final answer: The capital of France is Paris."
        )
        self.mock_cohere_instance.generate.return_value = (
            "Step 1: Understand the query\nThe query is asking for France's capital.\n\n"
            "Step 2: Access geographic knowledge\nFrance is in Europe and its capital is Paris.\n\n"
            "Final answer: Paris is the capital of France."
        )
        self.mock_mistral_instance.generate.return_value = (
            "Step 1: Analyze the question\nWe need to identify France's capital city.\n\n"
            "Step 2: Recall facts about France\nFrance is a European country with Paris as its capital.\n\n"
            "Final answer: Paris is the capital of France."
        )

        # Set up model properties
        for mock_instance in [self.mock_openai_instance, self.mock_anthropic_instance,
                             self.mock_openrouter_instance, self.mock_cohere_instance,
                             self.mock_mistral_instance]:
            mock_instance.temperature = 0.7
            mock_instance.max_tokens = 1000

    def tearDown(self):
        """
        Clean up after tests.
        """
        self.openai_patcher.stop()
        self.anthropic_patcher.stop()
        self.openrouter_patcher.stop()
        self.cohere_patcher.stop()
        self.mistral_patcher.stop()

    def test_integration_with_openai(self):
        """
        Test integration with OpenAI provider.
        """
        # Create reasoner with OpenAI
        reasoner = AdaptiveChainOfThought(
            model=self.mock_openai_instance,
            language="en",
            min_steps=2,
            max_steps=5,
            adaptive_steps=True,
            verbose=False,
            use_cache=False
        )

        # Test reasoning
        result = reasoner.reason("What is the capital of France?")
        self.assertEqual(result.query, "What is the capital of France?")
        self.assertIn("Step 1", result.answer)
        self.assertIn("Final answer", result.answer)
        # Accept the result with or without a space after "Final answer:"
        self.assertTrue(
            result.metadata["final_answer"] == "Final answer: Paris is the capital of France." or
            result.metadata["final_answer"] == "Final answer:Paris is the capital of France."
        )
        self.assertEqual(result.metadata["step_count"], 3)

        # Verify that the model was called
        self.mock_openai_instance.generate.assert_called_once()

    def test_integration_with_anthropic(self):
        """
        Test integration with Anthropic provider.
        """
        # Create reasoner with Anthropic
        reasoner = AdaptiveChainOfThought(
            model=self.mock_anthropic_instance,
            language="en",
            min_steps=2,
            max_steps=5,
            adaptive_steps=True,
            verbose=False,
            use_cache=False
        )

        # Test reasoning
        result = reasoner.reason("What is the capital of France?")
        self.assertEqual(result.query, "What is the capital of France?")
        self.assertIn("Step 1", result.answer)
        self.assertIn("Final answer", result.answer)
        # Accept the result with or without a space after "Final answer:"
        self.assertTrue(
            result.metadata["final_answer"] == "Final answer: Paris is the capital of France." or
            result.metadata["final_answer"] == "Final answer:Paris is the capital of France."
        )
        self.assertEqual(result.metadata["step_count"], 2)

        # Verify that the model was called
        self.mock_anthropic_instance.generate.assert_called_once()

    def test_integration_with_openrouter(self):
        """
        Test integration with OpenRouter provider.
        """
        # Create reasoner with OpenRouter
        reasoner = AdaptiveChainOfThought(
            model=self.mock_openrouter_instance,
            language="en",
            min_steps=2,
            max_steps=5,
            adaptive_steps=True,
            verbose=False,
            use_cache=False
        )

        # Test reasoning
        result = reasoner.reason("What is the capital of France?")
        self.assertEqual(result.query, "What is the capital of France?")
        self.assertIn("Step 1", result.answer)
        self.assertIn("Final answer", result.answer)
        # Accept the result with or without a space after "Final answer:"
        self.assertTrue(
            result.metadata["final_answer"] == "Final answer: The capital of France is Paris." or
            result.metadata["final_answer"] == "Final answer:The capital of France is Paris."
        )
        self.assertEqual(result.metadata["step_count"], 2)

        # Verify that the model was called
        self.mock_openrouter_instance.generate.assert_called_once()

    def test_integration_with_cohere(self):
        """
        Test integration with Cohere provider.
        """
        # Create reasoner with Cohere
        reasoner = AdaptiveChainOfThought(
            model=self.mock_cohere_instance,
            language="en",
            min_steps=2,
            max_steps=5,
            adaptive_steps=True,
            verbose=False,
            use_cache=False
        )

        # Test reasoning
        result = reasoner.reason("What is the capital of France?")
        self.assertEqual(result.query, "What is the capital of France?")
        self.assertIn("Step 1", result.answer)
        self.assertIn("Final answer", result.answer)
        # Accept the result with or without a space after "Final answer:"
        self.assertTrue(
            result.metadata["final_answer"] == "Final answer: Paris is the capital of France." or
            result.metadata["final_answer"] == "Final answer:Paris is the capital of France."
        )
        self.assertEqual(result.metadata["step_count"], 2)

        # Verify that the model was called
        self.mock_cohere_instance.generate.assert_called_once()

    def test_integration_with_mistral(self):
        """
        Test integration with Mistral provider.
        """
        # Create reasoner with Mistral
        reasoner = AdaptiveChainOfThought(
            model=self.mock_mistral_instance,
            language="en",
            min_steps=2,
            max_steps=5,
            adaptive_steps=True,
            verbose=False,
            use_cache=False
        )

        # Test reasoning
        result = reasoner.reason("What is the capital of France?")
        self.assertEqual(result.query, "What is the capital of France?")
        self.assertIn("Step 1", result.answer)
        self.assertIn("Final answer", result.answer)
        # Accept the result with or without a space after "Final answer:"
        self.assertTrue(
            result.metadata["final_answer"] == "Final answer: Paris is the capital of France." or
            result.metadata["final_answer"] == "Final answer:Paris is the capital of France."
        )
        self.assertEqual(result.metadata["step_count"], 2)

        # Verify that the model was called
        self.mock_mistral_instance.generate.assert_called_once()

    def test_fallback_mechanism_with_multiple_providers(self):
        """
        Test the fallback mechanism with multiple providers.
        """
        # Create reasoner with OpenAI as primary and others as fallbacks
        reasoner = AdaptiveChainOfThought(
            model=self.mock_openai_instance,
            language="en",
            min_steps=2,
            max_steps=5,
            adaptive_steps=True,
            verbose=False,
            use_cache=False,
            fallback_models=[
                self.mock_anthropic_instance,
                self.mock_openrouter_instance,
                self.mock_cohere_instance,
                self.mock_mistral_instance
            ]
        )

        # Make the primary model fail
        self.mock_openai_instance.generate.side_effect = Exception("OpenAI error")

        # Test reasoning - should use the first fallback (Anthropic)
        result = reasoner.reason("What is the capital of France?")
        self.assertEqual(result.query, "What is the capital of France?")
        self.assertIn("Step 1", result.answer)
        self.assertIn("Final answer", result.answer)
        # Accept the result with or without a space after "Final answer:"
        self.assertTrue(
            result.metadata["final_answer"] == "Final answer: Paris is the capital of France." or
            result.metadata["final_answer"] == "Final answer:Paris is the capital of France."
        )

        # Verify that the fallback model was called
        self.mock_anthropic_instance.generate.assert_called_once()

        # Make the first fallback fail too
        self.mock_anthropic_instance.generate.side_effect = Exception("Anthropic error")

        # Test reasoning again - should use the second fallback (OpenRouter)
        result = reasoner.reason("What is the capital of France?")
        self.assertEqual(result.query, "What is the capital of France?")
        self.assertIn("Step 1", result.answer)
        self.assertIn("Final answer", result.answer)
        # Accept the result with or without a space after "Final answer:"
        self.assertTrue(
            result.metadata["final_answer"] == "Final answer: The capital of France is Paris." or
            result.metadata["final_answer"] == "Final answer:The capital of France is Paris."
        )

        # Verify that the second fallback model was called
        self.mock_openrouter_instance.generate.assert_called_once()

    def test_complex_query_with_adaptive_steps(self):
        """
        Test handling of complex queries with adaptive step count.
        """
        # Create reasoner with adaptive steps
        reasoner = AdaptiveChainOfThought(
            model=self.mock_openai_instance,
            language="en",
            min_steps=2,
            max_steps=5,
            adaptive_steps=True,
            verbose=False,
            use_cache=False
        )

        # Set up a more complex response for a complex query
        self.mock_openai_instance.generate.return_value = (
            "Step 1: Understand the problem\nLet's break down this complex question about quantum computing.\n\n"
            "Step 2: Define quantum computing\nQuantum computing uses quantum bits or qubits.\n\n"
            "Step 3: Explain current cryptography\nModern cryptography relies on mathematical problems.\n\n"
            "Step 4: Analyze quantum threats\nQuantum computers could break many encryption algorithms.\n\n"
            "Step 5: Consider future implications\nThis will require new cryptographic standards.\n\n"
            "Final answer: Quantum computing poses significant challenges to modern cryptography because it can efficiently solve the mathematical problems that current encryption relies on. This will necessitate the development of quantum-resistant cryptographic algorithms in the next decade."
        )

        # Test reasoning with a complex query
        complex_query = "Analyze the implications of quantum computing on modern cryptography and explain how it might affect data security in the next decade."
        result = reasoner.reason(complex_query)

        self.assertEqual(result.query, complex_query)
        self.assertIn("Step 1", result.answer)
        self.assertIn("Step 5", result.answer)
        self.assertIn("Final answer", result.answer)
        # Accept either 5 or 6 steps for complex queries
        self.assertTrue(
            result.metadata["step_count"] == 5 or
            result.metadata["step_count"] == 6
        )

        # Verify that the complexity estimation is high
        self.assertGreaterEqual(result.metadata["complexity"], 0.7)

        # Verify that the model was called with the max_steps parameter
        call_args = self.mock_openai_instance.generate.call_args[1]
        self.assertIn(f"up to a maximum of {reasoner.max_steps} steps", call_args["prompt"])


if __name__ == "__main__":
    unittest.main()
