"""
Unit tests for NLP analyzers including transformer sentiment analyzers.
"""

import unittest
from unittest.mock import patch, MagicMock
import sys
import os
import json
import tempfile
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.deep_research_core.agents.nlp_analyzers.transformer_sentiment_analyzer import (
    TransformerSentimentAnalyzer,
    VietnameseSentimentEnhanced,
    get_sentiment_analyzer,
    TRANSFORMERS_AVAILABLE
)

class TestTransformerSentimentAnalyzer(unittest.TestCase):
    """Test cases for TransformerSentimentAnalyzer."""
    
    @unittest.skipIf(not TRANSFORMERS_AVAILABLE, "Transformers library not available")
    @patch('src.deep_research_core.agents.nlp_analyzers.transformer_sentiment_analyzer.pipeline')
    @patch('src.deep_research_core.agents.nlp_analyzers.transformer_sentiment_analyzer.AutoModelForSequenceClassification')
    @patch('src.deep_research_core.agents.nlp_analyzers.transformer_sentiment_analyzer.AutoTokenizer')
    def test_initialize_model(self, mock_tokenizer, mock_model, mock_pipeline):
        """Test model initialization."""
        # Set up mocks
        mock_model_instance = MagicMock()
        mock_tokenizer_instance = MagicMock()
        
        mock_model.from_pretrained.return_value = mock_model_instance
        mock_tokenizer.from_pretrained.return_value = mock_tokenizer_instance
        
        # Create analyzer
        analyzer = TransformerSentimentAnalyzer(
            model_name="test-model",
            language="en",
            device="cpu"
        )
        
        # Check that the model was initialized
        self.assertTrue(analyzer.is_ready)
        mock_model.from_pretrained.assert_called_once_with(
            "test-model",
            cache_dir=None
        )
        mock_tokenizer.from_pretrained.assert_called_once_with(
            "test-model",
            cache_dir=None
        )
        mock_pipeline.assert_called_once()
    
    @unittest.skipIf(not TRANSFORMERS_AVAILABLE, "Transformers library not available")
    def test_detect_language(self):
        """Test language detection."""
        analyzer = TransformerSentimentAnalyzer()
        
        # Test English
        self.assertEqual(analyzer._detect_language("This is English text"), "en")
        
        # Test Vietnamese
        self.assertEqual(analyzer._detect_language("Đây là văn bản tiếng Việt"), "vi")
        
        # Test mixed but with Vietnamese characters
        self.assertEqual(analyzer._detect_language("This text has some tiếng Việt"), "vi")
    
    @unittest.skipIf(not TRANSFORMERS_AVAILABLE, "Transformers library not available")
    @patch('src.deep_research_core.agents.nlp_analyzers.transformer_sentiment_analyzer.pipeline')
    def test_analyze_sentiment(self, mock_pipeline):
        """Test sentiment analysis."""
        # Set up mock pipeline
        pipeline_instance = MagicMock()
        pipeline_instance.return_value = [{"label": "POSITIVE", "score": 0.95}]
        mock_pipeline.return_value = pipeline_instance
        
        # Create analyzer
        analyzer = TransformerSentimentAnalyzer(language="en")
        
        # Mock the _initialize_model method
        analyzer._initialize_model = MagicMock(return_value=True)
        analyzer.is_ready = True
        analyzer.model_name = "test-model"
        
        # Test sentiment analysis
        result = analyzer.analyze_sentiment("This is a great product!")
        
        # Check result
        self.assertEqual(result["sentiment"], "positive")
        self.assertEqual(result["score"], 0.95)
        self.assertEqual(result["method"], "transformer")
        self.assertEqual(result["language"], "en")
    
    @unittest.skipIf(not TRANSFORMERS_AVAILABLE, "Transformers library not available")
    @patch('src.deep_research_core.agents.nlp_analyzers.transformer_sentiment_analyzer.pipeline')
    def test_normalize_sentiment_score(self, mock_pipeline):
        """Test sentiment score normalization."""
        # Create analyzer
        analyzer = TransformerSentimentAnalyzer()
        
        # Test English positive
        sentiment, score = analyzer._normalize_sentiment_score(
            {"label": "POSITIVE", "score": 0.9},
            "en"
        )
        self.assertEqual(sentiment, "positive")
        self.assertEqual(score, 0.9)
        
        # Test English negative
        sentiment, score = analyzer._normalize_sentiment_score(
            {"label": "NEGATIVE", "score": 0.8},
            "en"
        )
        self.assertEqual(sentiment, "negative")
        self.assertEqual(score, -0.8)
        
        # Test Vietnamese positive
        sentiment, score = analyzer._normalize_sentiment_score(
            {"label": "TÍCH CỰC", "score": 0.7},
            "vi"
        )
        self.assertEqual(sentiment, "positive")
        self.assertEqual(score, 0.7)
        
        # Test Vietnamese negative
        sentiment, score = analyzer._normalize_sentiment_score(
            {"label": "TIÊU CỰC", "score": 0.6},
            "vi"
        )
        self.assertEqual(sentiment, "negative")
        self.assertEqual(score, -0.6)
    
    @unittest.skipIf(not TRANSFORMERS_AVAILABLE, "Transformers library not available")
    @patch('src.deep_research_core.agents.nlp_analyzers.transformer_sentiment_analyzer.pipeline')
    def test_analyze_sentiment_batch(self, mock_pipeline):
        """Test batch sentiment analysis."""
        # Set up mock pipeline
        pipeline_instance = MagicMock()
        mock_pipeline.return_value = pipeline_instance
        
        # Create analyzer
        analyzer = TransformerSentimentAnalyzer(
            language="en",
            batch_size=2
        )
        
        # Mock analyze_sentiment to return predictable results
        analyzer.analyze_sentiment = MagicMock(side_effect=[
            {"sentiment": "positive", "score": 0.9},
            {"sentiment": "negative", "score": -0.8},
            {"sentiment": "neutral", "score": 0.1}
        ])
        
        # Set is_ready flag
        analyzer.is_ready = True
        
        # Test batch analysis
        texts = ["This is great!", "This is terrible!", "This is okay."]
        results = analyzer.analyze_sentiment_batch(texts)
        
        # Check results
        self.assertEqual(len(results), 3)
        self.assertEqual(results[0]["sentiment"], "positive")
        self.assertEqual(results[1]["sentiment"], "negative")
        self.assertEqual(results[2]["sentiment"], "neutral")
        
        # Check that analyze_sentiment was called for each text
        self.assertEqual(analyzer.analyze_sentiment.call_count, 3)


class TestVietnameseSentimentEnhanced(unittest.TestCase):
    """Test cases for VietnameseSentimentEnhanced."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create analyzer with transformer disabled to test rule-based methods
        self.analyzer = VietnameseSentimentEnhanced(use_transformers=False)
        
        # Sample Vietnamese texts
        self.positive_text = "Sản phẩm này rất tốt và tuyệt vời. Tôi rất hài lòng."
        self.negative_text = "Dịch vụ tệ quá. Tôi thất vọng và không hài lòng."
        self.neutral_text = "Hôm nay là thứ hai. Thời tiết bình thường."
    
    def test_rule_based_analysis(self):
        """Test rule-based sentiment analysis."""
        # Test positive text
        result = self.analyzer._rule_based_analysis(self.positive_text)
        self.assertEqual(result["sentiment"], "positive")
        self.assertGreater(result["score"], 0)
        self.assertEqual(result["method"], "rule-based")
        
        # Test negative text
        result = self.analyzer._rule_based_analysis(self.negative_text)
        self.assertEqual(result["sentiment"], "negative")
        self.assertLess(result["score"], 0)
        
        # Test neutral text
        result = self.analyzer._rule_based_analysis(self.neutral_text)
        self.assertEqual(result["sentiment"], "neutral")
        self.assertEqual(result["score"], 0.0)
    
    def test_negation_handling(self):
        """Test handling of negations in Vietnamese."""
        # Positive statement with negation
        text = "Sản phẩm này không tệ."  # "This product is not bad"
        result = self.analyzer._rule_based_analysis(text)
        self.assertEqual(result["sentiment"], "positive")
        
        # Negative statement with negation
        text = "Sản phẩm này không tốt."  # "This product is not good"
        result = self.analyzer._rule_based_analysis(text)
        self.assertEqual(result["sentiment"], "negative")
    
    def test_intensifier_handling(self):
        """Test handling of intensifiers in Vietnamese."""
        # Positive with intensifier
        text = "Sản phẩm này rất tốt."  # "This product is very good"
        result = self.analyzer._rule_based_analysis(text)
        self.assertEqual(result["sentiment"], "positive")
        
        # Compare with non-intensified positive
        text2 = "Sản phẩm này tốt."  # "This product is good"
        result2 = self.analyzer._rule_based_analysis(text2)
        
        # The intensified version should have a higher score
        self.assertGreater(result["score"], result2["score"])
        
        # Negative with intensifier
        text = "Sản phẩm này cực kỳ tệ."  # "This product is extremely bad"
        result = self.analyzer._rule_based_analysis(text)
        self.assertEqual(result["sentiment"], "negative")
        self.assertLess(result["score"], -0.5)  # Strong negative
    
    @patch('src.deep_research_core.agents.nlp_analyzers.transformer_sentiment_analyzer.TransformerSentimentAnalyzer')
    def test_analyze_sentiment_combined(self, mock_transformer):
        """Test combined sentiment analysis with multiple methods."""
        # Create mock transformer analyzer
        mock_transformer_instance = MagicMock()
        mock_transformer_instance.is_available.return_value = True
        mock_transformer_instance.analyze_sentiment.return_value = {
            "sentiment": "positive",
            "score": 0.85,
            "method": "transformer"
        }
        mock_transformer.return_value = mock_transformer_instance
        
        # Create analyzer with mock transformer
        analyzer = VietnameseSentimentEnhanced(use_transformers=True)
        analyzer.transformer_analyzer = mock_transformer_instance
        
        # Mock underthesea availability (normally False in tests)
        analyzer.underthesea_available = True
        analyzer._underthesea_analysis = MagicMock(return_value={
            "sentiment": "positive",
            "score": 0.7,
            "method": "underthesea"
        })
        
        # Override rule-based to return predictable results
        analyzer._rule_based_analysis = MagicMock(return_value={
            "sentiment": "positive",
            "score": 0.6,
            "method": "rule-based"
        })
        
        # Test combined analysis
        result = analyzer.analyze_sentiment("Sản phẩm này tốt.")
        
        # Check that all methods were called
        mock_transformer_instance.analyze_sentiment.assert_called_once()
        analyzer._underthesea_analysis.assert_called_once()
        analyzer._rule_based_analysis.assert_called_once()
        
        # Check that it used the method with highest confidence (transformer)
        self.assertEqual(result["method"], "transformer")
        self.assertEqual(result["score"], 0.85)
        self.assertEqual(result["language"], "vi")
        
        # Check that details contains all methods
        self.assertIn("transformer", result["details"])
        self.assertIn("underthesea", result["details"])
        self.assertIn("rule_based", result["details"])


class TestSentimentAnalyzerFactory(unittest.TestCase):
    """Test cases for sentiment analyzer factory function."""
    
    def test_get_sentiment_analyzer(self):
        """Test get_sentiment_analyzer factory function."""
        # Test with Vietnamese
        analyzer = get_sentiment_analyzer(language="vi", use_transformers=False)
        self.assertIsInstance(analyzer, VietnameseSentimentEnhanced)
        
        # Test with English
        analyzer = get_sentiment_analyzer(language="en", use_transformers=False)
        self.assertIsInstance(analyzer, TransformerSentimentAnalyzer)
        self.assertEqual(analyzer.language, "en")
        
        # Test with auto (should return TransformerSentimentAnalyzer)
        analyzer = get_sentiment_analyzer(language="auto", use_transformers=False)
        self.assertIsInstance(analyzer, TransformerSentimentAnalyzer)
        self.assertEqual(analyzer.language, "auto")


if __name__ == '__main__':
    unittest.main() 