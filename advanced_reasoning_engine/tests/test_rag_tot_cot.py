"""
Test script for RAGToTCoT.

This script tests the RAGToTCoT class which combines RAG, ToT, and CoT.
"""

import os
import sys
import unittest
from unittest.mock import MagicMock, patch
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.reasoning.rag_tot_cot import RAGTOTCOTReasoner
from src.deep_research_core.rag.base import BaseRAG

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestRAGToTCoT(unittest.TestCase):
    """Test case for RAGToTCoT."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create mock RAG system
        self.mock_rag = MagicMock(spec=BaseRAG)
        self.mock_rag.query.return_value = [
            {
                "content": "This is a test document about artificial intelligence.",
                "source": "Test Source"
            }
        ]
        
        # Create mock API provider
        self.mock_api_provider = MagicMock()
        self.mock_api_provider.generate.return_value = "AI is a field of computer science."
        
        # Create mock ToT
        self.mock_tot = MagicMock()
        self.mock_tot.reason.return_value = {
            "query": "What is AI?",
            "reasoning": "AI stands for Artificial Intelligence.",
            "answer": "AI is a field of computer science.",
            "model": "gpt-4o",
            "provider": "openai",
            "latency": 1.0
        }
        
        # Create mock CoT
        self.mock_cot = MagicMock()
        self.mock_cot.reason.return_value = {
            "reasoning": "Artificial Intelligence (AI) is a branch of computer science.",
            "answer": "AI is a field of computer science focused on creating intelligent machines.",
            "steps": 3,
            "latency": 0.5
        }
        
        # Create patches
        self.patches = [
            patch('src.deep_research_core.reasoning.rag_tot_cot.TreeOfThought', return_value=self.mock_tot),
            patch('src.deep_research_core.reasoning.rag_tot_cot.ChainOfThought', return_value=self.mock_cot),
            patch('src.deep_research_core.reasoning.rag_tot_cot.openai_provider', self.mock_api_provider),
            patch('src.deep_research_core.reasoning.rag_tot_cot.anthropic_provider', self.mock_api_provider),
            patch('src.deep_research_core.reasoning.rag_tot_cot.openrouter_provider', self.mock_api_provider)
        ]
        
        # Start patches
        for p in self.patches:
            p.start()
        
        # Create RAGToTCoT instance
        self.rag_tot_cot = RAGTOTCOTReasoner(
            rag_system=self.mock_rag,
            provider="openai",
            model="gpt-4o",
            temperature=0.7,
            max_tokens=2000,
            adaptive=True,
            use_advanced_optimization=True,
            verbose=True
        )
    
    def tearDown(self):
        """Tear down test fixtures."""
        # Stop patches
        for p in self.patches:
            p.stop()
    
    def test_reason_with_all_methods(self):
        """Test reasoning with all methods enabled."""
        # Call reason with all methods enabled
        result = self.rag_tot_cot.reason(
            query="What is artificial intelligence?",
            use_rag=True,
            use_tot=True,
            use_cot=True
        )
        
        # Check that RAG was used
        self.mock_rag.query.assert_called_once()
        
        # Check that ToT was used
        self.mock_tot.reason.assert_called_once()
        
        # Check that CoT was used to enhance ToT result
        self.mock_cot.reason.assert_called_once()
        
        # Check result
        self.assertEqual(result["methods_used"], "RAG-TOT-COT")
        self.assertTrue(result["rag_context_used"])
        self.assertIn("enhanced_reasoning", result)
        self.assertIn("enhanced_answer", result)
    
    def test_reason_without_rag(self):
        """Test reasoning without RAG."""
        # Call reason without RAG
        result = self.rag_tot_cot.reason(
            query="What is artificial intelligence?",
            use_rag=False,
            use_tot=True,
            use_cot=True
        )
        
        # Check that RAG was not used
        self.mock_rag.query.assert_not_called()
        
        # Check that ToT was used
        self.mock_tot.reason.assert_called_once()
        
        # Check result
        self.assertEqual(result["methods_used"], "TOT-COT")
        self.assertFalse(result["rag_context_used"])
    
    def test_reason_without_tot(self):
        """Test reasoning without ToT."""
        # Call reason without ToT
        result = self.rag_tot_cot.reason(
            query="What is artificial intelligence?",
            use_rag=True,
            use_tot=False,
            use_cot=True
        )
        
        # Check that RAG was used
        self.mock_rag.query.assert_called_once()
        
        # Check that ToT was not used
        self.mock_tot.reason.assert_not_called()
        
        # Check that CoT was used directly
        self.mock_cot.reason.assert_called_once()
        
        # Check result
        self.assertEqual(result["methods_used"], "RAG-COT")
    
    def test_reason_without_cot(self):
        """Test reasoning without CoT."""
        # Call reason without CoT
        result = self.rag_tot_cot.reason(
            query="What is artificial intelligence?",
            use_rag=True,
            use_tot=True,
            use_cot=False
        )
        
        # Check that RAG was used
        self.mock_rag.query.assert_called_once()
        
        # Check that ToT was used
        self.mock_tot.reason.assert_called_once()
        
        # Check that CoT was not used to enhance ToT result
        self.assertEqual(self.mock_cot.reason.call_count, 0)
        
        # Check result
        self.assertEqual(result["methods_used"], "RAG-TOT")
        self.assertNotIn("enhanced_reasoning", result)
    
    def test_compare_methods(self):
        """Test comparing different reasoning methods."""
        # Call compare_methods
        comparison = self.rag_tot_cot.compare_methods(
            query="What is artificial intelligence?"
        )
        
        # Check that all methods were tested
        self.assertIn("cot_only", comparison["methods"])
        self.assertIn("rag_cot", comparison["methods"])
        self.assertIn("tot_cot", comparison["methods"])
        self.assertIn("rag_tot_cot", comparison["methods"])
        
        # Check that each method has the expected fields
        for method in comparison["methods"].values():
            self.assertIn("answer_length", method)
            self.assertIn("latency", method)
            self.assertIn("answer", method)

if __name__ == '__main__':
    unittest.main()
