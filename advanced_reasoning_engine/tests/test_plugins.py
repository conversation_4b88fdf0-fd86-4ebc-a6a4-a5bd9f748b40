"""
Unit test cho các plugin.

Module n<PERSON><PERSON> chứ<PERSON> các test case cho các plugin.
"""

import unittest
import os
import sys
import tempfile
from unittest.mock import MagicMock, patch

# Th<PERSON><PERSON> thư mục cha vào sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.deep_research_core.plugins.base_plugin import BasePlugin
from src.deep_research_core.plugins.query_optimization_plugin import QueryOptimizationPlugin
from src.deep_research_core.plugins.result_ranking_plugin import ResultRankingPlugin
from src.deep_research_core.plugins.performance_optimization_plugin import PerformanceOptimizationPlugin
from src.deep_research_core.plugins.content_filter_plugin import ContentFilterPlugin
from src.deep_research_core.plugins.multilingual_plugin import MultilingualPlugin


class TestBasePlugin(unittest.TestCase):
    """Test case cho BasePlugin."""

    def setUp(self):
        """<PERSON><PERSON><PERSON><PERSON> lập trước mỗi test case."""
        self.plugin = BasePlugin({"enabled": True, "test_config": "test_value"})

    def test_init(self):
        """Test khởi tạo plugin."""
        self.assertTrue(self.plugin.is_enabled())
        self.assertEqual(self.plugin.get_config()["test_config"], "test_value")

    def test_enable_disable(self):
        """Test kích hoạt và vô hiệu hóa plugin."""
        self.plugin.disable()
        self.assertFalse(self.plugin.is_enabled())
        self.plugin.enable()
        self.assertTrue(self.plugin.is_enabled())

    def test_get_config(self):
        """Test lấy cấu hình plugin."""
        config = self.plugin.get_config()
        self.assertEqual(config["enabled"], True)
        self.assertEqual(config["test_config"], "test_value")

    def test_update_config(self):
        """Test cập nhật cấu hình plugin."""
        self.plugin.update_config({"test_config": "new_value", "new_config": "value"})
        config = self.plugin.get_config()
        self.assertEqual(config["test_config"], "new_value")
        self.assertEqual(config["new_config"], "value")

    def test_get_name(self):
        """Test lấy tên plugin."""
        self.assertEqual(self.plugin.get_name(), "BasePlugin")

    def test_get_description(self):
        """Test lấy mô tả plugin."""
        self.assertIsNotNone(self.plugin.get_description())

    def test_get_version(self):
        """Test lấy phiên bản plugin."""
        self.assertEqual(self.plugin.get_version(), "1.0.0")

    def test_get_author(self):
        """Test lấy tác giả plugin."""
        self.assertEqual(self.plugin.get_author(), "Unknown")

    def test_get_dependencies(self):
        """Test lấy danh sách phụ thuộc plugin."""
        self.assertEqual(self.plugin.get_dependencies(), [])

    def test_get_hooks(self):
        """Test lấy danh sách hook plugin."""
        self.assertEqual(self.plugin.get_hooks(), [])

    def test_initialize(self):
        """Test khởi tạo plugin."""
        self.assertTrue(self.plugin.initialize())

    def test_cleanup(self):
        """Test dọn dẹp plugin."""
        self.plugin.cleanup()  # Không có gì để kiểm tra, chỉ đảm bảo không có lỗi


class TestQueryOptimizationPlugin(unittest.TestCase):
    """Test case cho QueryOptimizationPlugin."""

    def setUp(self):
        """Thiết lập trước mỗi test case."""
        self.plugin = QueryOptimizationPlugin()

    def test_init(self):
        """Test khởi tạo plugin."""
        self.assertTrue(self.plugin.is_enabled())
        self.assertEqual(self.plugin.get_name(), "QueryOptimizationPlugin")

    def test_pre_search(self):
        """Test hook pre_search."""
        result = self.plugin.pre_search("test query")
        self.assertEqual(result["query"], "test query")
        self.assertEqual(result["original_query"], "test query")
        self.assertFalse(result["optimization_applied"])


class TestResultRankingPlugin(unittest.TestCase):
    """Test case cho ResultRankingPlugin."""

    def setUp(self):
        """Thiết lập trước mỗi test case."""
        self.plugin = ResultRankingPlugin()

    def test_init(self):
        """Test khởi tạo plugin."""
        self.assertTrue(self.plugin.is_enabled())
        self.assertEqual(self.plugin.get_name(), "ResultRankingPlugin")

    def test_post_search(self):
        """Test hook post_search."""
        results = [
            {"url": "http://example.com", "title": "Example", "snippet": "Example snippet"},
            {"url": "http://example.org", "title": "Example Org", "snippet": "Example org snippet"}
        ]
        result = self.plugin.post_search(results, query="test query")
        self.assertEqual(len(result["results"]), 2)
        self.assertEqual(result["original_results"], results)
        self.assertTrue(result["ranking_applied"])


class TestPerformanceOptimizationPlugin(unittest.TestCase):
    """Test case cho PerformanceOptimizationPlugin."""

    def setUp(self):
        """Thiết lập trước mỗi test case."""
        self.plugin = PerformanceOptimizationPlugin()

    def test_init(self):
        """Test khởi tạo plugin."""
        self.assertTrue(self.plugin.is_enabled())
        self.assertEqual(self.plugin.get_name(), "PerformanceOptimizationPlugin")

    def test_pre_search(self):
        """Test hook pre_search."""
        result = self.plugin.pre_search()
        self.assertIsInstance(result, dict)

    def test_post_search(self):
        """Test hook post_search."""
        results = [
            {"url": "http://example.com", "title": "Example", "snippet": "Example snippet"},
            {"url": "http://example.org", "title": "Example Org", "snippet": "Example org snippet"}
        ]
        result = self.plugin.post_search(results, search_time=0.5, success=True)
        self.assertEqual(result["results"], results)

    def test_get_performance_metrics(self):
        """Test lấy chỉ số hiệu suất."""
        metrics = self.plugin.get_performance_metrics()
        self.assertIsInstance(metrics, dict)
        self.assertIn("search_count", metrics)
        self.assertIn("total_search_time", metrics)
        self.assertIn("avg_search_time", metrics)

    def test_get_search_params(self):
        """Test lấy tham số tìm kiếm."""
        params = self.plugin.get_search_params()
        self.assertIsInstance(params, dict)
        self.assertIn("timeout", params)
        self.assertIn("concurrency", params)
        self.assertIn("batch_size", params)

    def test_reset_performance_metrics(self):
        """Test reset chỉ số hiệu suất."""
        self.plugin.reset_performance_metrics()
        metrics = self.plugin.get_performance_metrics()
        self.assertEqual(metrics["search_count"], 0)
        self.assertEqual(metrics["total_search_time"], 0.0)


class TestContentFilterPlugin(unittest.TestCase):
    """Test case cho ContentFilterPlugin."""

    def setUp(self):
        """Thiết lập trước mỗi test case."""
        self.plugin = ContentFilterPlugin()

    def test_init(self):
        """Test khởi tạo plugin."""
        self.assertTrue(self.plugin.is_enabled())
        self.assertEqual(self.plugin.get_name(), "ContentFilterPlugin")

    def test_post_search(self):
        """Test hook post_search."""
        results = [
            {"url": "http://example.com", "title": "Example", "snippet": "Example snippet"},
            {"url": "http://example.org", "title": "Example Org", "snippet": "Example org snippet"}
        ]
        result = self.plugin.post_search(results)
        self.assertEqual(len(result["results"]), 2)
        self.assertEqual(result["original_results"], results)
        self.assertEqual(result["filtered_count"], 0)

    def test_filter_adult_content(self):
        """Test lọc nội dung người lớn."""
        results = [
            {"url": "http://example.com", "title": "Example", "snippet": "Example snippet"},
            {"url": "http://example.org", "title": "Adult Content", "snippet": "This is adult content with xxx"}
        ]
        result = self.plugin.post_search(results)
        self.assertEqual(len(result["results"]), 1)
        self.assertEqual(result["filtered_count"], 1)


class TestMultilingualPlugin(unittest.TestCase):
    """Test case cho MultilingualPlugin."""

    def setUp(self):
        """Thiết lập trước mỗi test case."""
        self.plugin = MultilingualPlugin()

    def test_init(self):
        """Test khởi tạo plugin."""
        self.assertTrue(self.plugin.is_enabled())
        self.assertEqual(self.plugin.get_name(), "MultilingualPlugin")

    def test_pre_search(self):
        """Test hook pre_search."""
        result = self.plugin.pre_search("test query")
        self.assertEqual(result["query"], "test query")
        self.assertEqual(result["original_query"], "test query")
        self.assertEqual(result["language"], "vi")

    def test_post_search(self):
        """Test hook post_search."""
        results = [
            {"url": "http://example.com", "title": "Example", "snippet": "Example snippet"},
            {"url": "http://example.org", "title": "Example Org", "snippet": "Example org snippet"}
        ]
        result = self.plugin.post_search(results)
        self.assertEqual(result["results"], results)
        self.assertEqual(result["original_results"], results)
        self.assertEqual(result["language"], "vi")


if __name__ == "__main__":
    unittest.main()
