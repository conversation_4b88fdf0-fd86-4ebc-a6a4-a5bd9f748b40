"""
Test module for MultilingualCrawler.
"""

import os
import sys
import unittest
import asyncio
from unittest.mock import patch, MagicMock

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))

from deep_research_core.agents.multilingual_crawler import MultilingualCrawler
from deep_research_core.utils.multilingual_utils import is_non_latin_language, normalize_text_for_language

class TestMultilingualCrawler(unittest.TestCase):
    """Test cases for MultilingualCrawler."""

    def setUp(self):
        """Set up test fixtures."""
        self.crawler = MultilingualCrawler(
            max_depth=1,
            max_pages=2,
            timeout=10,
            respect_robots=True,
            config={
                "language": "auto",
                "normalize_text": True,
                "extract_metadata": True,
                "extract_links": True,
                "use_playwright": False,  # Use requests for testing
                "use_requests_fallback": True,
            }
        )

    @patch('deep_research_core.agents.multilingual_crawler.requests.get')
    def test_crawl_with_requests(self, mock_get):
        """Test crawling with requests."""
        # Mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = """
        <html>
            <head>
                <title>Test Page</title>
                <meta name="description" content="Test description">
            </head>
            <body>
                <h1>Test Heading</h1>
                <p>This is a test paragraph.</p>
                <a href="https://example.com/page1">Link 1</a>
                <a href="https://example.com/page2">Link 2</a>
            </body>
        </html>
        """
        mock_get.return_value = mock_response

        # Run the test
        result = asyncio.run(self.crawler.crawl("https://example.com"))

        # Assertions
        self.assertTrue(result["success"])
        self.assertEqual(result["url"], "https://example.com")
        self.assertEqual(len(result["pages"]), 1)
        self.assertIn("Test Heading", result["content"])
        self.assertIn("test paragraph", result["content"])

    @patch('deep_research_core.utils.multilingual_utils.detect_language')
    @patch('deep_research_core.agents.multilingual_crawler.requests.get')
    def test_crawl_chinese_content(self, mock_get, mock_detect_language):
        """Test crawling Chinese content."""
        # Mock language detection
        mock_detect_language.return_value = "zh"

        # Mock response with Chinese content
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = """
        <html>
            <head>
                <title>测试页面</title>
                <meta name="description" content="测试描述">
            </head>
            <body>
                <h1>测试标题</h1>
                <p>这是一个测试段落。</p>
                <a href="https://example.com/page1">链接 1</a>
                <a href="https://example.com/page2">链接 2</a>
            </body>
        </html>
        """
        mock_get.return_value = mock_response

        # Run the test
        result = asyncio.run(self.crawler.crawl("https://example.com"))

        # Assertions
        self.assertTrue(result["success"])
        self.assertEqual(result["language"], "zh")
        self.assertIn("测试标题", result["content"])
        self.assertIn("测试段落", result["content"])

    @patch('deep_research_core.utils.multilingual_utils.detect_language')
    @patch('deep_research_core.agents.multilingual_crawler.requests.get')
    def test_crawl_japanese_content(self, mock_get, mock_detect_language):
        """Test crawling Japanese content."""
        # Mock language detection
        mock_detect_language.return_value = "ja"

        # Mock response with Japanese content
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = """
        <html>
            <head>
                <title>テストページ</title>
                <meta name="description" content="テスト説明">
            </head>
            <body>
                <h1>テスト見出し</h1>
                <p>これはテスト段落です。</p>
                <a href="https://example.com/page1">リンク 1</a>
                <a href="https://example.com/page2">リンク 2</a>
            </body>
        </html>
        """
        mock_get.return_value = mock_response

        # Run the test
        result = asyncio.run(self.crawler.crawl("https://example.com"))

        # Assertions
        self.assertTrue(result["success"])
        self.assertEqual(result["language"], "ja")
        self.assertIn("テスト見出し", result["content"])
        self.assertIn("テスト段落", result["content"])

    @patch('deep_research_core.utils.multilingual_utils.detect_language')
    @patch('deep_research_core.agents.multilingual_crawler.requests.get')
    def test_crawl_arabic_content(self, mock_get, mock_detect_language):
        """Test crawling Arabic content."""
        # Mock language detection
        mock_detect_language.return_value = "ar"

        # Mock response with Arabic content
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = """
        <html>
            <head>
                <title>صفحة اختبار</title>
                <meta name="description" content="وصف الاختبار">
            </head>
            <body>
                <h1>عنوان الاختبار</h1>
                <p>هذه فقرة اختبار.</p>
                <a href="https://example.com/page1">رابط 1</a>
                <a href="https://example.com/page2">رابط 2</a>
            </body>
        </html>
        """
        mock_get.return_value = mock_response

        # Run the test
        result = asyncio.run(self.crawler.crawl("https://example.com"))

        # Assertions
        self.assertTrue(result["success"])
        self.assertEqual(result["language"], "ar")
        self.assertIn("عنوان الاختبار", result["content"])
        self.assertIn("فقرة اختبار", result["content"])

    def test_is_non_latin_language(self):
        """Test is_non_latin_language function."""
        self.assertTrue(is_non_latin_language("zh"))
        self.assertTrue(is_non_latin_language("ja"))
        self.assertTrue(is_non_latin_language("ar"))
        self.assertTrue(is_non_latin_language("ru"))
        self.assertFalse(is_non_latin_language("en"))
        self.assertFalse(is_non_latin_language("fr"))
        self.assertFalse(is_non_latin_language("es"))

    def test_normalize_text_for_language(self):
        """Test normalize_text_for_language function."""
        # Test Chinese normalization
        chinese_text = "这 是 一个 测试 文本"
        normalized_chinese = normalize_text_for_language(chinese_text, "zh")
        self.assertEqual(normalized_chinese, "这是一个测试 文本")

        # Test Japanese normalization
        japanese_text = "これ は テスト 文章 です"
        normalized_japanese = normalize_text_for_language(japanese_text, "ja")
        self.assertEqual(normalized_japanese, "これはテスト 文章 です")

        # Test Arabic normalization (RTL markers)
        arabic_text = "هذا نص اختبار"
        normalized_arabic = normalize_text_for_language(arabic_text, "ar")
        self.assertTrue(normalized_arabic.startswith("\u202B"))
        self.assertTrue(normalized_arabic.endswith("\u202C"))

if __name__ == '__main__':
    unittest.main()
