"""
Tests for RAG prompt templates and RAGPromptRouter.
"""

import unittest
from unittest.mock import MagicMock, patch

from deep_research_core.reasoning.rag_prompt_templates import (
    RAGPromptRouter,
    get_rag_prompt_template,
    RAG_EN_PROMPT_TEMPLATES,
    RAG_VI_PROMPT_TEMPLATES,
    DOMAIN_RAG_PROMPT_TEMPLATES,
    VI_DOMAIN_RAG_PROMPT_TEMPLATES
)


class TestRAGPromptTemplates(unittest.TestCase):
    """Test cases for RAG prompt templates."""

    def test_get_rag_prompt_template(self):
        """Test getting RAG prompt templates."""
        # Test English factual template
        template = get_rag_prompt_template(question_type="factual", language="en")
        self.assertEqual(template, RAG_EN_PROMPT_TEMPLATES["factual"])
        
        # Test Vietnamese factual template
        template = get_rag_prompt_template(question_type="factual", language="vi")
        self.assertEqual(template, RAG_VI_PROMPT_TEMPLATES["factual"])
        
        # Test English domain-specific template
        template = get_rag_prompt_template(question_type="factual", language="en", domain="medical")
        self.assertEqual(template, DOMAIN_RAG_PROMPT_TEMPLATES["medical"])
        
        # Test Vietnamese domain-specific template
        template = get_rag_prompt_template(question_type="factual", language="vi", domain="medical")
        self.assertEqual(template, VI_DOMAIN_RAG_PROMPT_TEMPLATES["medical"])
        
        # Test fallback to factual for unknown question type
        template = get_rag_prompt_template(question_type="unknown", language="en")
        self.assertEqual(template, RAG_EN_PROMPT_TEMPLATES["factual"])


class TestRAGPromptRouter(unittest.TestCase):
    """Test cases for RAGPromptRouter."""

    def setUp(self):
        """Set up test environment."""
        self.router = RAGPromptRouter()

    def test_analyze_query_english(self):
        """Test analyzing English queries."""
        # Test factual query
        query = "What is the capital of France?"
        analysis = self.router.analyze_query(query)
        self.assertEqual(analysis["question_type"], "factual")
        self.assertEqual(analysis["language"], "en")
        self.assertIsNone(analysis["domain"])
        
        # Test analytical query
        query = "Analyze the impact of climate change on global agriculture."
        analysis = self.router.analyze_query(query)
        self.assertEqual(analysis["question_type"], "analytical")
        self.assertEqual(analysis["language"], "en")
        
        # Test procedural query
        query = "How do I reset my password on a Windows computer?"
        analysis = self.router.analyze_query(query)
        self.assertEqual(analysis["question_type"], "procedural")
        self.assertEqual(analysis["language"], "en")
        self.assertEqual(analysis["domain"], "technical")

    def test_analyze_query_vietnamese(self):
        """Test analyzing Vietnamese queries."""
        # Test factual query
        query = "Thủ đô của Pháp là gì?"
        analysis = self.router.analyze_query(query)
        self.assertEqual(analysis["question_type"], "factual")
        self.assertEqual(analysis["language"], "vi")
        self.assertIsNone(analysis["domain"])
        
        # Test analytical query
        query = "Phân tích tác động của biến đổi khí hậu đối với nông nghiệp toàn cầu."
        analysis = self.router.analyze_query(query)
        self.assertEqual(analysis["question_type"], "analytical")
        self.assertEqual(analysis["language"], "vi")

    def test_detect_domain(self):
        """Test domain detection."""
        # Test medical domain
        query = "What are the symptoms of diabetes?"
        domain = self.router._detect_domain(query)
        self.assertEqual(domain, "medical")
        
        # Test legal domain
        query = "Explain the concept of intellectual property rights."
        domain = self.router._detect_domain(query)
        self.assertEqual(domain, "legal")
        
        # Test technical domain
        query = "How does a blockchain work?"
        domain = self.router._detect_domain(query)
        self.assertEqual(domain, "technical")
        
        # Test educational domain
        query = "What are effective teaching methods for elementary school students?"
        domain = self.router._detect_domain(query)
        self.assertEqual(domain, "educational")
        
        # Test Vietnamese domains
        query = "Các triệu chứng của bệnh tiểu đường là gì?"
        domain = self.router._detect_domain(query)
        self.assertEqual(domain, "medical")

    def test_get_prompt(self):
        """Test getting prompts for queries."""
        # Test factual query
        query = "What is the capital of France?"
        prompt = self.router.get_prompt(query)
        self.assertIn("system", prompt)
        self.assertIn("user", prompt)
        
        # Test Vietnamese query
        query = "Thủ đô của Pháp là gì?"
        prompt = self.router.get_prompt(query)
        self.assertIn("system", prompt)
        self.assertIn("user", prompt)
        
        # Test domain-specific query
        query = "What are the symptoms of diabetes?"
        prompt = self.router.get_prompt(query)
        self.assertIn("system", prompt)
        self.assertIn("user", prompt)

    def test_format_prompt(self):
        """Test formatting prompts with context."""
        # Test formatting prompt
        query = "What is the capital of France?"
        context = "Document 1:\nParis is the capital of France."
        formatted = self.router.format_prompt(query, context)
        self.assertIn("system_prompt", formatted)
        self.assertIn("user_prompt", formatted)
        self.assertIn(query, formatted["user_prompt"])
        self.assertIn(context, formatted["user_prompt"])


if __name__ == "__main__":
    unittest.main()
