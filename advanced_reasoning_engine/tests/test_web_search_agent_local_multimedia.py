#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test tìm kiếm đa phương tiện cho WebSearchAgentLocal.

Module này kiểm tra khả năng tìm kiếm và trích xuất nội dung từ các định dạng đa phương tiện
của WebSearchAgentLocal, bao gồm hình <PERSON>nh, video, và âm thanh.
"""

import os
import sys
import time
import json
import unittest
import logging
from unittest.mock import patch, MagicMock
from typing import Dict, Any, List, Optional

# Thêm thư mục gốc vào sys.path để import các module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Import các module c<PERSON><PERSON> thiết
try:
    from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal
    from src.deep_research_core.agents.multimedia_search import MultimediaSearch
except ImportError:
    try:
        # Thử import từ thư mục hiện tại
        from web_search_agent_local import WebSearchAgentLocal
        logger.warning("Importing from current directory. Some features may not be available.")
        
        # Định nghĩa các class giả nếu không import được
        class MockClass:
            def __init__(self, *args, **kwargs):
                pass
                
        MultimediaSearch = MockClass
    except ImportError:
        logger.error("Không thể import WebSearchAgentLocal. Vui lòng kiểm tra đường dẫn.")
        sys.exit(1)

# Hàm tiện ích
def print_section(title):
    """In tiêu đề phần."""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, "="))
    print("=" * 80)

def save_results_to_json(results, filename):
    """Lưu kết quả vào file JSON."""
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print(f"Đã lưu kết quả vào {filename}")

class TestWebSearchAgentLocalMultimedia(unittest.TestCase):
    """Test tìm kiếm đa phương tiện cho WebSearchAgentLocal."""
    
    @classmethod
    def setUpClass(cls):
        """Thiết lập trước khi chạy tất cả các test."""
        print_section("Thiết lập test tìm kiếm đa phương tiện WebSearchAgentLocal")
        
        # Tạo thư mục lưu kết quả nếu chưa tồn tại
        os.makedirs("test_results", exist_ok=True)
        
        # Khởi tạo WebSearchAgentLocal
        cls.agent = WebSearchAgentLocal(
            verbose=True,
            enable_multimedia_search=True
        )
        
        # Danh sách truy vấn đa phương tiện
        cls.image_queries = [
            "Hình ảnh Hà Nội",
            "Python logo",
            "Eiffel Tower images",
            "Cat pictures",
            "Vietnam landscape photos"
        ]
        
        cls.video_queries = [
            "Python programming tutorial video",
            "Vietnam travel guide video",
            "Machine learning tutorial video",
            "Cooking recipe video",
            "Nature documentary"
        ]
        
        cls.audio_queries = [
            "Classical music",
            "Vietnamese folk songs",
            "Nature sounds",
            "Podcast about technology",
            "Piano music"
        ]
    
    def setUp(self):
        """Thiết lập trước mỗi test."""
        # Đợi một chút giữa các test để tránh rate limit
        time.sleep(1)
    
    def test_01_multimedia_search_initialization(self):
        """Test khởi tạo MultimediaSearch."""
        print_section("Test khởi tạo MultimediaSearch")
        
        # Khởi tạo MultimediaSearch
        try:
            multimedia_search = MultimediaSearch()
            print(f"Đã khởi tạo MultimediaSearch thành công")
            
            # Kiểm tra các thuộc tính
            supported_types = multimedia_search.get_supported_types()
            print(f"Các loại đa phương tiện được hỗ trợ: {supported_types}")
            
            # Kiểm tra kết quả
            self.assertIsInstance(supported_types, list, "Danh sách loại đa phương tiện được hỗ trợ không phải là list")
            self.assertGreater(len(supported_types), 0, "Danh sách loại đa phương tiện được hỗ trợ trống")
            
            # Lưu kết quả
            result = {
                "supported_types": supported_types
            }
            save_results_to_json(result, "test_results/multimedia_search_initialization.json")
        except Exception as e:
            print(f"Lỗi khi khởi tạo MultimediaSearch: {str(e)}")
            self.skipTest(f"Lỗi khi khởi tạo MultimediaSearch: {str(e)}")
    
    def test_02_search_images(self):
        """Test tìm kiếm hình ảnh."""
        print_section("Test tìm kiếm hình ảnh")
        
        # Chọn một truy vấn hình ảnh
        query = self.image_queries[0]
        print(f"Truy vấn hình ảnh: {query}")
        
        # Thực hiện tìm kiếm hình ảnh
        if hasattr(self.agent, 'search_images'):
            result = self.agent.search_images(query, num_results=5)
            
            # Kiểm tra kết quả
            self.assertTrue(result.get('success', False), "Tìm kiếm hình ảnh không thành công")
            self.assertGreater(len(result.get('results', [])), 0, "Không có kết quả tìm kiếm hình ảnh")
            
            # In kết quả
            print(f"Tìm thấy {len(result.get('results', []))} kết quả hình ảnh")
            for i, item in enumerate(result.get('results', [])[:3]):  # Chỉ in 3 kết quả đầu tiên
                print(f"\nKết quả {i+1}:")
                print(f"  Tiêu đề: {item.get('title', 'N/A')}")
                print(f"  URL: {item.get('url', 'N/A')}")
                print(f"  Thumbnail: {item.get('thumbnail_url', 'N/A')}")
            
            # Lưu kết quả
            save_results_to_json(result, "test_results/search_images.json")
        else:
            print("Phương thức search_images không khả dụng")
            self.skipTest("Phương thức search_images không khả dụng")
    
    def test_03_search_videos(self):
        """Test tìm kiếm video."""
        print_section("Test tìm kiếm video")
        
        # Chọn một truy vấn video
        query = self.video_queries[0]
        print(f"Truy vấn video: {query}")
        
        # Thực hiện tìm kiếm video
        if hasattr(self.agent, 'search_videos'):
            result = self.agent.search_videos(query, num_results=5)
            
            # Kiểm tra kết quả
            self.assertTrue(result.get('success', False), "Tìm kiếm video không thành công")
            self.assertGreater(len(result.get('results', [])), 0, "Không có kết quả tìm kiếm video")
            
            # In kết quả
            print(f"Tìm thấy {len(result.get('results', []))} kết quả video")
            for i, item in enumerate(result.get('results', [])[:3]):  # Chỉ in 3 kết quả đầu tiên
                print(f"\nKết quả {i+1}:")
                print(f"  Tiêu đề: {item.get('title', 'N/A')}")
                print(f"  URL: {item.get('url', 'N/A')}")
                print(f"  Thumbnail: {item.get('thumbnail_url', 'N/A')}")
                print(f"  Thời lượng: {item.get('duration', 'N/A')}")
            
            # Lưu kết quả
            save_results_to_json(result, "test_results/search_videos.json")
        else:
            print("Phương thức search_videos không khả dụng")
            self.skipTest("Phương thức search_videos không khả dụng")
    
    def test_04_search_audio(self):
        """Test tìm kiếm âm thanh."""
        print_section("Test tìm kiếm âm thanh")
        
        # Chọn một truy vấn âm thanh
        query = self.audio_queries[0]
        print(f"Truy vấn âm thanh: {query}")
        
        # Thực hiện tìm kiếm âm thanh
        if hasattr(self.agent, 'search_audio'):
            result = self.agent.search_audio(query, num_results=5)
            
            # Kiểm tra kết quả
            self.assertTrue(result.get('success', False), "Tìm kiếm âm thanh không thành công")
            self.assertGreater(len(result.get('results', [])), 0, "Không có kết quả tìm kiếm âm thanh")
            
            # In kết quả
            print(f"Tìm thấy {len(result.get('results', []))} kết quả âm thanh")
            for i, item in enumerate(result.get('results', [])[:3]):  # Chỉ in 3 kết quả đầu tiên
                print(f"\nKết quả {i+1}:")
                print(f"  Tiêu đề: {item.get('title', 'N/A')}")
                print(f"  URL: {item.get('url', 'N/A')}")
                print(f"  Thời lượng: {item.get('duration', 'N/A')}")
            
            # Lưu kết quả
            save_results_to_json(result, "test_results/search_audio.json")
        else:
            print("Phương thức search_audio không khả dụng")
            self.skipTest("Phương thức search_audio không khả dụng")
    
    def test_05_extract_image_info(self):
        """Test trích xuất thông tin hình ảnh."""
        print_section("Test trích xuất thông tin hình ảnh")
        
        # Thực hiện tìm kiếm hình ảnh để lấy URL
        if not hasattr(self.agent, 'search_images'):
            self.skipTest("Phương thức search_images không khả dụng")
        
        # Chọn một truy vấn hình ảnh
        query = self.image_queries[0]
        print(f"Truy vấn hình ảnh: {query}")
        
        # Thực hiện tìm kiếm hình ảnh
        search_result = self.agent.search_images(query, num_results=1)
        
        # Kiểm tra kết quả tìm kiếm
        if not search_result.get('success', False) or not search_result.get('results', []):
            self.skipTest("Không thể tìm kiếm hình ảnh để lấy URL")
        
        # Lấy URL hình ảnh đầu tiên
        image_url = search_result['results'][0].get('url', '')
        if not image_url:
            self.skipTest("Không thể lấy URL hình ảnh")
        
        print(f"URL hình ảnh: {image_url}")
        
        # Trích xuất thông tin hình ảnh
        if hasattr(self.agent, 'extract_image_info'):
            result = self.agent.extract_image_info(image_url)
            
            # Kiểm tra kết quả
            self.assertTrue(result.get('success', False), f"Trích xuất thông tin hình ảnh từ {image_url} không thành công")
            
            # In kết quả
            print(f"Kích thước: {result.get('width', 'N/A')} x {result.get('height', 'N/A')}")
            print(f"Định dạng: {result.get('format', 'N/A')}")
            print(f"Dung lượng: {result.get('size', 'N/A')} bytes")
            
            # Lưu kết quả
            save_results_to_json(result, "test_results/extract_image_info.json")
        else:
            print("Phương thức extract_image_info không khả dụng")
            self.skipTest("Phương thức extract_image_info không khả dụng")
    
    def test_06_extract_video_info(self):
        """Test trích xuất thông tin video."""
        print_section("Test trích xuất thông tin video")
        
        # Thực hiện tìm kiếm video để lấy URL
        if not hasattr(self.agent, 'search_videos'):
            self.skipTest("Phương thức search_videos không khả dụng")
        
        # Chọn một truy vấn video
        query = self.video_queries[0]
        print(f"Truy vấn video: {query}")
        
        # Thực hiện tìm kiếm video
        search_result = self.agent.search_videos(query, num_results=1)
        
        # Kiểm tra kết quả tìm kiếm
        if not search_result.get('success', False) or not search_result.get('results', []):
            self.skipTest("Không thể tìm kiếm video để lấy URL")
        
        # Lấy URL video đầu tiên
        video_url = search_result['results'][0].get('url', '')
        if not video_url:
            self.skipTest("Không thể lấy URL video")
        
        print(f"URL video: {video_url}")
        
        # Trích xuất thông tin video
        if hasattr(self.agent, 'extract_video_info'):
            result = self.agent.extract_video_info(video_url)
            
            # Kiểm tra kết quả
            self.assertTrue(result.get('success', False), f"Trích xuất thông tin video từ {video_url} không thành công")
            
            # In kết quả
            print(f"Tiêu đề: {result.get('title', 'N/A')}")
            print(f"Thời lượng: {result.get('duration', 'N/A')}")
            print(f"Độ phân giải: {result.get('width', 'N/A')} x {result.get('height', 'N/A')}")
            
            # Lưu kết quả
            save_results_to_json(result, "test_results/extract_video_info.json")
        else:
            print("Phương thức extract_video_info không khả dụng")
            self.skipTest("Phương thức extract_video_info không khả dụng")
    
    def test_07_analyze_multimedia_content(self):
        """Test phân tích nội dung đa phương tiện."""
        print_section("Test phân tích nội dung đa phương tiện")
        
        # Thực hiện tìm kiếm hình ảnh để lấy URL
        if not hasattr(self.agent, 'search_images'):
            self.skipTest("Phương thức search_images không khả dụng")
        
        # Chọn một truy vấn hình ảnh
        query = "Python logo"
        print(f"Truy vấn hình ảnh: {query}")
        
        # Thực hiện tìm kiếm hình ảnh
        search_result = self.agent.search_images(query, num_results=1)
        
        # Kiểm tra kết quả tìm kiếm
        if not search_result.get('success', False) or not search_result.get('results', []):
            self.skipTest("Không thể tìm kiếm hình ảnh để lấy URL")
        
        # Lấy URL hình ảnh đầu tiên
        image_url = search_result['results'][0].get('url', '')
        if not image_url:
            self.skipTest("Không thể lấy URL hình ảnh")
        
        print(f"URL hình ảnh: {image_url}")
        
        # Phân tích nội dung đa phương tiện
        if hasattr(self.agent, 'analyze_multimedia_content'):
            result = self.agent.analyze_multimedia_content(image_url)
            
            # Kiểm tra kết quả
            self.assertTrue(result.get('success', False), f"Phân tích nội dung đa phương tiện từ {image_url} không thành công")
            
            # In kết quả
            print(f"Loại nội dung: {result.get('content_type', 'N/A')}")
            print(f"Mô tả: {result.get('description', 'N/A')}")
            print(f"Tags: {result.get('tags', [])}")
            
            # Lưu kết quả
            save_results_to_json(result, "test_results/analyze_multimedia_content.json")
        else:
            print("Phương thức analyze_multimedia_content không khả dụng")
            self.skipTest("Phương thức analyze_multimedia_content không khả dụng")
    
    @classmethod
    def tearDownClass(cls):
        """Dọn dẹp sau khi chạy tất cả các test."""
        print_section("Dọn dẹp sau khi chạy tất cả các test")
        
        # Dọn dẹp tài nguyên
        pass

if __name__ == "__main__":
    unittest.main()
