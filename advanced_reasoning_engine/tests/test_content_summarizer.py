"""
Test module for content_summarizer.py
"""

import unittest
import sys
import os
from unittest.mock import patch, MagicMock

# Add the src directory to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from src.deep_research_core.utils.content_summarizer import (
    BaseSummarizer,
    FrequencySummarizer,
    TextRankSummarizer,
    HybridSummarizer
)


class TestBaseSummarizer(unittest.TestCase):
    """Test cases for BaseSummarizer"""

    def test_split_sentences(self):
        """Test _split_sentences method"""
        summarizer = BaseSummarizer()

        # Test with simple text
        text = "This is the first sentence. This is the second sentence. This is the third sentence."
        sentences = summarizer._split_sentences(text)

        self.assertEqual(3, len(sentences))
        # Kiểm tra nội dung câu mà không quan tâm đến dấu chấm cuối
        self.assertTrue(sentences[0].startswith("This is the first sentence"))
        self.assertTrue(sentences[1].startswith("This is the second sentence"))
        self.assertTrue(sentences[2].startswith("This is the third sentence"))

        # Test with empty text
        self.assertEqual([], summarizer._split_sentences(""))

        # Test with text containing other punctuation
        text = "Hello! How are you? I'm fine."
        sentences = summarizer._split_sentences(text)

        self.assertEqual(3, len(sentences))
        self.assertTrue(sentences[0].startswith("Hello"))
        self.assertTrue(sentences[1].startswith("How are you"))
        self.assertTrue(sentences[2].startswith("I'm fine"))

    def test_tokenize(self):
        """Test _tokenize method"""
        summarizer = BaseSummarizer()

        # Test with simple text
        text = "This is a simple text with some words."
        tokens = summarizer._tokenize(text)

        # Kiểm tra các từ cần thiết có trong kết quả
        self.assertIn('this', tokens)
        self.assertIn('is', tokens)
        self.assertIn('a', tokens)
        self.assertIn('simple', tokens)
        self.assertIn('text', tokens)
        self.assertIn('with', tokens)
        self.assertIn('some', tokens)
        self.assertIn('words', tokens)

        # Test with empty text
        self.assertEqual([], summarizer._tokenize(""))

        # Test with text containing numbers and special characters
        text = "This text has 123 numbers and special characters like @#$."
        tokens = summarizer._tokenize(text)

        self.assertIn('this', tokens)
        self.assertIn('text', tokens)
        self.assertIn('has', tokens)
        self.assertIn('123', tokens)
        self.assertIn('numbers', tokens)
        self.assertIn('and', tokens)
        self.assertIn('special', tokens)
        self.assertIn('characters', tokens)
        self.assertIn('like', tokens)

    def test_remove_stopwords(self):
        """Test _remove_stopwords method"""
        summarizer = BaseSummarizer()

        # Test with simple text
        words = ['this', 'is', 'a', 'simple', 'text', 'with', 'some', 'words']
        filtered_words = summarizer._remove_stopwords(words)

        # Check that stopwords are removed
        self.assertNotIn('this', filtered_words)
        self.assertNotIn('is', filtered_words)
        self.assertNotIn('a', filtered_words)
        self.assertNotIn('with', filtered_words)
        self.assertNotIn('some', filtered_words)

        # Check that content words are kept
        self.assertIn('simple', filtered_words)
        self.assertIn('text', filtered_words)
        self.assertIn('words', filtered_words)

        # Test with empty list
        self.assertEqual([], summarizer._remove_stopwords([]))


class TestFrequencySummarizer(unittest.TestCase):
    """Test cases for FrequencySummarizer"""

    def test_summarize(self):
        """Test summarize method"""
        summarizer = FrequencySummarizer()

        # Create a long text with multiple sentences
        long_text = """
        This is the first sentence of the text. This is the second sentence with some important keywords.
        This is the third sentence that doesn't contain many important words.
        This is the fourth sentence with more important keywords and information.
        This is the fifth sentence that repeats some keywords from earlier.
        This is the sixth sentence with unique information not mentioned before.
        This is the seventh sentence that doesn't add much value.
        This is the eighth sentence with critical information about the topic.
        This is the ninth sentence that summarizes the main points.
        This is the tenth and final sentence that concludes the text.
        """

        # Summarize the text
        summary = summarizer.summarize(long_text, max_sentences=3)

        # Check that the summary is shorter than the original text
        self.assertLess(len(summary), len(long_text))

        # Check that the summary contains important sentences
        self.assertIn("first sentence", summary)  # First sentences are usually important

        # Test with text shorter than max_sentences
        short_text = "This is a short text with only one sentence."
        short_summary = summarizer.summarize(short_text, max_sentences=3)

        # Check that the summary is the same as the original text
        self.assertEqual(short_text, short_summary)


class TestTextRankSummarizer(unittest.TestCase):
    """Test cases for TextRankSummarizer"""

    @patch('src.deep_research_core.utils.content_summarizer.NETWORKX_AVAILABLE', True)
    @patch('src.deep_research_core.utils.content_summarizer.nx')
    def test_summarize_with_networkx(self, mock_nx):
        """Test summarize method with NetworkX available"""
        # Mock nx.from_numpy_array
        mock_graph = MagicMock()
        mock_nx.from_numpy_array.return_value = mock_graph

        # Mock nx.pagerank
        mock_nx.pagerank.return_value = {0: 0.3, 1: 0.2, 2: 0.5}

        summarizer = TextRankSummarizer()

        # Create a text with multiple sentences
        text = "This is the first sentence. This is the second sentence. This is the third sentence."

        # Summarize the text
        summary = summarizer.summarize(text, max_sentences=2)

        # Check that nx.from_numpy_array was called
        mock_nx.from_numpy_array.assert_called_once()

        # Check that nx.pagerank was called
        mock_nx.pagerank.assert_called_once_with(mock_graph)

        # Check that the summary contains the most important sentences
        self.assertIn("third sentence", summary)  # Highest score
        self.assertIn("first sentence", summary)  # Second highest score
        self.assertNotIn("second sentence", summary)  # Lowest score

    @patch('src.deep_research_core.utils.content_summarizer.NETWORKX_AVAILABLE', False)
    def test_summarize_without_networkx(self):
        """Test summarize method with NetworkX not available"""
        summarizer = TextRankSummarizer()

        # Create a text with multiple sentences
        text = "This is the first sentence. This is the second sentence. This is the third sentence."

        # Summarize the text
        summary = summarizer.summarize(text, max_sentences=2)

        # Check that the summary contains some sentences
        self.assertGreater(len(summary), 0)

        # Check that the summary is a subset of the original text
        self.assertTrue(summary in text)

    def test_sentence_similarity(self):
        """Test _sentence_similarity method"""
        summarizer = TextRankSummarizer()

        # Test with identical sentences
        sent1 = "This is a test sentence."
        sent2 = "This is a test sentence."
        similarity = summarizer._sentence_similarity(sent1, sent2)

        # Kiểm tra gần bằng 1.0 thay vì chính xác 1.0 để tránh lỗi do làm tròn số
        self.assertAlmostEqual(1.0, similarity, places=5)

        # Test with completely different sentences
        sent1 = "This is a test sentence."
        sent2 = "Python is a programming language."
        similarity = summarizer._sentence_similarity(sent1, sent2)

        self.assertLess(similarity, 0.5)

        # Test with partially similar sentences
        sent1 = "This is a test sentence."
        sent2 = "This is a different sentence."
        similarity = summarizer._sentence_similarity(sent1, sent2)

        self.assertGreater(similarity, 0.5)

        # Test with empty sentences
        self.assertEqual(0.0, summarizer._sentence_similarity("", ""))
        self.assertEqual(0.0, summarizer._sentence_similarity("This is a test", ""))
        self.assertEqual(0.0, summarizer._sentence_similarity("", "This is a test"))


class TestHybridSummarizer(unittest.TestCase):
    """Test cases for HybridSummarizer"""

    def test_summarize_frequency(self):
        """Test summarize method with frequency method"""
        summarizer = HybridSummarizer()

        # Create a text with multiple sentences
        text = "This is the first sentence. This is the second sentence. This is the third sentence."

        # Summarize the text with frequency method
        summary = summarizer.summarize(text, max_sentences=2, method='frequency')

        # Check that the summary contains some sentences
        self.assertGreater(len(summary), 0)

        # Check that the summary is a subset of the original text
        self.assertTrue(summary in text)

    @patch('src.deep_research_core.utils.content_summarizer.NETWORKX_AVAILABLE', True)
    def test_summarize_textrank(self):
        """Test summarize method with textrank method"""
        summarizer = HybridSummarizer()

        # Create a text with multiple sentences
        text = "This is the first sentence. This is the second sentence. This is the third sentence."

        # Summarize the text with textrank method
        summary = summarizer.summarize(text, max_sentences=2, method='textrank')

        # Check that the summary contains some sentences
        self.assertGreater(len(summary), 0)

        # Check that the summary is a subset of the original text
        self.assertTrue(summary in text)

    def test_summarize_hybrid(self):
        """Test summarize method with hybrid method"""
        summarizer = HybridSummarizer()

        # Create a long text with multiple sentences
        long_text = """
        This is the first sentence of the text. This is the second sentence with some important keywords.
        This is the third sentence that doesn't contain many important words.
        This is the fourth sentence with more important keywords and information.
        This is the fifth sentence that repeats some keywords from earlier.
        This is the sixth sentence with unique information not mentioned before.
        This is the seventh sentence that doesn't add much value.
        This is the eighth sentence with critical information about the topic.
        This is the ninth sentence that summarizes the main points.
        This is the tenth and final sentence that concludes the text.
        """

        # Summarize the text with hybrid method
        summary = summarizer.summarize(long_text, max_sentences=3, method='hybrid')

        # Check that the summary is shorter than the original text
        self.assertLess(len(summary), len(long_text))

        # Check that the summary contains important sentences
        self.assertIn("first sentence", summary)  # First sentences are usually important

        # Test with text shorter than max_sentences
        short_text = "This is a short text with only one sentence."
        short_summary = summarizer.summarize(short_text, max_sentences=3, method='hybrid')

        # Check that the summary is the same as the original text
        self.assertEqual(short_text, short_summary)


if __name__ == "__main__":
    unittest.main()
