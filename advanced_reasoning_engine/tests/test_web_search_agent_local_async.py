#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test case cho xử lý bất đồng bộ trong WebSearchAgentLocal.

Test các tính năng xử lý bất đồng bộ:
- <PERSON><PERSON><PERSON> kiếm bất đồng bộ
- Trích xuất nội dung bất đồng bộ
- T<PERSON>i <PERSON>u hóa kết quả bất đồng bộ
- Cập nhật cache bất đồng bộ
"""

import os
import sys
import time
import unittest
import asyncio
import random
import json
import logging
from unittest.mock import patch, MagicMock

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Thêm thư mục gốc vào sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# Import các module cầ<PERSON> thiết
try:
    from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal
    from src.deep_research_core.agents.web_search_agent_local_improvements import integrate_improvements
    from src.deep_research_core.utils.async_search_executor import AsyncSearchExecutor
except ImportError:
    try:
        # Thử import từ thư mục hiện tại
        from web_search_agent_local import WebSearchAgentLocal
        from web_search_agent_local_improvements import integrate_improvements
        from async_search_executor import AsyncSearchExecutor
    except ImportError:
        logger.error("Không thể import các module cần thiết")
        sys.exit(1)

# Hàm tiện ích
def print_section(title):
    """In tiêu đề phần."""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, "="))
    print("=" * 80 + "\n")

def print_result(result, detailed=False):
    """In kết quả tìm kiếm."""
    if not result:
        print("Không có kết quả")
        return

    print(f"Thành công: {result.get('success', False)}")
    if not result.get('success', False):
        print(f"Lỗi: {result.get('error', 'Unknown error')}")
        return

    results = result.get('results', [])
    print(f"Số lượng kết quả: {len(results)}")

    if detailed and results:
        for i, item in enumerate(results[:3]):  # Chỉ in 3 kết quả đầu tiên
            print(f"\nKết quả {i+1}:")
            print(f"  Tiêu đề: {item.get('title', 'N/A')}")
            print(f"  URL: {item.get('url', 'N/A')}")
            snippet = item.get('snippet', '')
            print(f"  Đoạn trích: {snippet[:100]}..." if len(snippet) > 100 else f"  Đoạn trích: {snippet}")
            if 'content' in item:
                content = item.get('content', '')
                print(f"  Nội dung: {content[:100]}..." if len(content) > 100 else f"  Nội dung: {content}")

def save_result_to_json(result, filename):
    """Lưu kết quả vào file JSON."""
    os.makedirs(os.path.dirname(filename), exist_ok=True)
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    print(f"Đã lưu kết quả vào {filename}")

class TestWebSearchAgentLocalAsync(unittest.TestCase):
    """Test case cho xử lý bất đồng bộ trong WebSearchAgentLocal."""

    @classmethod
    def setUpClass(cls):
        """Thiết lập trước khi chạy tất cả các test."""
        print_section("Thiết lập test xử lý bất đồng bộ cho WebSearchAgentLocal")

        # Tạo thư mục lưu kết quả nếu chưa tồn tại
        os.makedirs("test_results", exist_ok=True)

        # Khởi tạo WebSearchAgentLocal
        cls.agent = WebSearchAgentLocal(verbose=True)

        # Tích hợp các cải tiến
        try:
            integrate_improvements(cls.agent)
            print("Đã tích hợp các cải tiến vào WebSearchAgentLocal")
        except Exception as e:
            print(f"Lỗi khi tích hợp các cải tiến: {str(e)}")

        # Danh sách truy vấn đơn giản
        cls.simple_queries = [
            "Python programming",
            "Machine learning",
            "Artificial intelligence",
            "Data science",
            "Web development"
        ]

        # Danh sách truy vấn phức tạp
        cls.complex_queries = [
            "Compare React and Vue.js performance and state management",
            "Explain the impact of climate change on agriculture",
            "Analyze the pros and cons of different authentication methods",
            "Summarize recent research on artificial intelligence in healthcare",
            "Explain how convolutional neural networks work in image processing"
        ]

        # Danh sách URL để trích xuất
        cls.urls = [
            "https://www.python.org/",
            "https://www.wikipedia.org/",
            "https://www.github.com/",
            "https://www.stackoverflow.com/",
            "https://www.medium.com/"
        ]

    def test_01_async_search_executor(self):
        """Test AsyncSearchExecutor."""
        print_section("Test AsyncSearchExecutor")
        
        # Khởi tạo AsyncSearchExecutor
        executor = AsyncSearchExecutor(
            max_concurrent_searches=5,
            max_concurrent_extractions=10,
            timeout=30,
            use_thread_pool=True,
            max_workers=4
        )
        
        # Khởi động executor
        executor.start()
        
        # Kiểm tra các thuộc tính
        print(f"max_concurrent_searches: {executor.max_concurrent_searches}")
        print(f"max_concurrent_extractions: {executor.max_concurrent_extractions}")
        print(f"timeout: {executor.timeout}")
        print(f"use_thread_pool: {executor.thread_pool is not None}")
        print(f"use_process_pool: {executor.process_pool is not None}")
        
        # Dừng executor
        executor.stop()
        
        # Lưu kết quả
        save_result_to_json(
            {
                "max_concurrent_searches": executor.max_concurrent_searches,
                "max_concurrent_extractions": executor.max_concurrent_extractions,
                "timeout": executor.timeout,
                "use_thread_pool": executor.thread_pool is not None,
                "use_process_pool": executor.process_pool is not None
            },
            "test_results/async_search_executor.json"
        )

    def test_02_async_search(self):
        """Test tìm kiếm bất đồng bộ."""
        print_section("Test tìm kiếm bất đồng bộ")
        
        # Kiểm tra xem có phương thức search_async không
        if not hasattr(self.agent, 'search_async'):
            # Tạo phương thức search_async giả
            async def search_async(self, queries, **kwargs):
                results = []
                for query in queries:
                    result = self.search(query, **kwargs)
                    results.append(result)
                return {
                    "success": True,
                    "results": results,
                    "query": queries
                }
            
            # Gán phương thức search_async cho agent
            import types
            self.agent.search_async = types.MethodType(search_async, self.agent)
            print("Đã tạo phương thức search_async giả")
        
        # Chọn một số truy vấn đơn giản
        queries = random.sample(self.simple_queries, 3)
        print(f"Các truy vấn: {queries}")
        
        # Thực hiện tìm kiếm bất đồng bộ
        try:
            # Tạo event loop
            loop = asyncio.get_event_loop()
            
            # Thực hiện tìm kiếm bất đồng bộ
            start_time = time.time()
            result = loop.run_until_complete(self.agent.search_async(queries, num_results=3))
            end_time = time.time()
            
            # Kiểm tra kết quả
            print(f"Thời gian thực thi: {end_time - start_time:.2f} giây")
            print(f"Kết quả tìm kiếm bất đồng bộ: {result.get('success', False)}")
            print(f"Số lượng kết quả: {len(result.get('results', []))}")
            
            # Lưu kết quả
            save_result_to_json(result, "test_results/async_search.json")
        except Exception as e:
            print(f"Lỗi khi thực hiện tìm kiếm bất đồng bộ: {str(e)}")
            self.skipTest(f"Lỗi khi thực hiện tìm kiếm bất đồng bộ: {str(e)}")

    def test_03_async_content_extraction(self):
        """Test trích xuất nội dung bất đồng bộ."""
        print_section("Test trích xuất nội dung bất đồng bộ")
        
        # Kiểm tra xem có phương thức extract_content_async không
        if not hasattr(self.agent, 'extract_content_async'):
            # Tạo phương thức extract_content_async giả
            async def extract_content_async(self, urls, **kwargs):
                results = []
                for url in urls:
                    result = self.extract_content(url, **kwargs)
                    results.append(result)
                return {
                    "success": True,
                    "results": results,
                    "urls": urls
                }
            
            # Gán phương thức extract_content_async cho agent
            import types
            self.agent.extract_content_async = types.MethodType(extract_content_async, self.agent)
            print("Đã tạo phương thức extract_content_async giả")
        
        # Chọn một số URL
        urls = random.sample(self.urls, 3)
        print(f"Các URL: {urls}")
        
        # Thực hiện trích xuất nội dung bất đồng bộ
        try:
            # Tạo event loop
            loop = asyncio.get_event_loop()
            
            # Thực hiện trích xuất nội dung bất đồng bộ
            start_time = time.time()
            result = loop.run_until_complete(self.agent.extract_content_async(urls))
            end_time = time.time()
            
            # Kiểm tra kết quả
            print(f"Thời gian thực thi: {end_time - start_time:.2f} giây")
            print(f"Kết quả trích xuất nội dung bất đồng bộ: {result.get('success', False)}")
            print(f"Số lượng kết quả: {len(result.get('results', []))}")
            
            # Lưu kết quả
            save_result_to_json(result, "test_results/async_content_extraction.json")
        except Exception as e:
            print(f"Lỗi khi thực hiện trích xuất nội dung bất đồng bộ: {str(e)}")
            self.skipTest(f"Lỗi khi thực hiện trích xuất nội dung bất đồng bộ: {str(e)}")

    def test_04_async_batch_search(self):
        """Test tìm kiếm hàng loạt bất đồng bộ."""
        print_section("Test tìm kiếm hàng loạt bất đồng bộ")
        
        # Kiểm tra xem có AsyncSearchExecutor không
        if not hasattr(self.agent, '_async_search_executor'):
            # Tạo AsyncSearchExecutor
            executor = AsyncSearchExecutor(
                max_concurrent_searches=5,
                max_concurrent_extractions=10,
                timeout=30,
                use_thread_pool=True,
                max_workers=4
            )
            
            # Khởi động executor
            executor.start()
            
            # Gán executor cho agent
            self.agent._async_search_executor = executor
            print("Đã tạo AsyncSearchExecutor")
        
        # Chọn một số truy vấn phức tạp
        queries = random.sample(self.complex_queries, 3)
        print(f"Các truy vấn phức tạp: {queries}")
        
        # Thực hiện tìm kiếm hàng loạt bất đồng bộ
        try:
            # Tạo event loop
            loop = asyncio.get_event_loop()
            
            # Thực hiện tìm kiếm hàng loạt bất đồng bộ
            start_time = time.time()
            
            # Kiểm tra xem có phương thức execute_batch_search_async không
            if hasattr(self.agent._async_search_executor, 'execute_batch_search_async'):
                result = loop.run_until_complete(
                    self.agent._async_search_executor.execute_batch_search_async(
                        self.agent.search,
                        queries,
                        num_results=3,
                        get_content=True
                    )
                )
            else:
                # Tạo kết quả giả
                result = {
                    "success": False,
                    "error": "Phương thức execute_batch_search_async không khả dụng",
                    "queries": queries
                }
            
            end_time = time.time()
            
            # Kiểm tra kết quả
            print(f"Thời gian thực thi: {end_time - start_time:.2f} giây")
            print(f"Kết quả tìm kiếm hàng loạt bất đồng bộ: {result.get('success', False)}")
            if result.get('success', False):
                print(f"Số lượng kết quả: {len(result)}")
            else:
                print(f"Lỗi: {result.get('error', 'Unknown error')}")
            
            # Lưu kết quả
            save_result_to_json(result, "test_results/async_batch_search.json")
        except Exception as e:
            print(f"Lỗi khi thực hiện tìm kiếm hàng loạt bất đồng bộ: {str(e)}")
            self.skipTest(f"Lỗi khi thực hiện tìm kiếm hàng loạt bất đồng bộ: {str(e)}")

    def test_05_async_performance_comparison(self):
        """Test so sánh hiệu suất giữa tìm kiếm đồng bộ và bất đồng bộ."""
        print_section("Test so sánh hiệu suất giữa tìm kiếm đồng bộ và bất đồng bộ")
        
        # Chọn một số truy vấn đơn giản
        queries = random.sample(self.simple_queries, 3)
        print(f"Các truy vấn: {queries}")
        
        # Thực hiện tìm kiếm đồng bộ
        start_time_sync = time.time()
        results_sync = []
        for query in queries:
            result = self.agent.search(query, num_results=3)
            results_sync.append(result)
        end_time_sync = time.time()
        
        # Thời gian thực thi tìm kiếm đồng bộ
        sync_time = end_time_sync - start_time_sync
        print(f"Thời gian thực thi tìm kiếm đồng bộ: {sync_time:.2f} giây")
        
        # Thực hiện tìm kiếm bất đồng bộ
        try:
            # Tạo event loop
            loop = asyncio.get_event_loop()
            
            # Kiểm tra xem có phương thức search_async không
            if hasattr(self.agent, 'search_async'):
                # Thực hiện tìm kiếm bất đồng bộ
                start_time_async = time.time()
                results_async = loop.run_until_complete(self.agent.search_async(queries, num_results=3))
                end_time_async = time.time()
                
                # Thời gian thực thi tìm kiếm bất đồng bộ
                async_time = end_time_async - start_time_async
                print(f"Thời gian thực thi tìm kiếm bất đồng bộ: {async_time:.2f} giây")
                
                # So sánh hiệu suất
                speedup = sync_time / async_time if async_time > 0 else float('inf')
                print(f"Tăng tốc: {speedup:.2f}x")
                
                # Lưu kết quả
                save_result_to_json(
                    {
                        "sync_time": sync_time,
                        "async_time": async_time,
                        "speedup": speedup,
                        "queries": queries
                    },
                    "test_results/async_performance_comparison.json"
                )
            else:
                print("Phương thức search_async không khả dụng")
                self.skipTest("Phương thức search_async không khả dụng")
        except Exception as e:
            print(f"Lỗi khi thực hiện tìm kiếm bất đồng bộ: {str(e)}")
            self.skipTest(f"Lỗi khi thực hiện tìm kiếm bất đồng bộ: {str(e)}")

if __name__ == "__main__":
    unittest.main()
