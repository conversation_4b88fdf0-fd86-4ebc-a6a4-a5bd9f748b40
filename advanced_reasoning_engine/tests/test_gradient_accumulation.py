"""
Tests for the gradient accumulation implementation.
"""

import unittest
import torch
import torch.nn as nn
import torch.optim as optim
from src.deep_research_core.optimization.memory.gradient_accumulation import (
    GradientAccumulator,
    AccumulationTrainer,
    estimate_memory_savings
)

class SimpleModel(nn.Module):
    """A simple model for testing gradient accumulation."""

    def __init__(self, input_size=10, hidden_size=20, output_size=1):
        super().__init__()
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.relu = nn.ReLU()
        self.fc2 = nn.Linear(hidden_size, output_size)

    def forward(self, x):
        x = self.fc1(x)
        x = self.relu(x)
        x = self.fc2(x)
        return x

class TestGradientAccumulation(unittest.TestCase):
    """Test cases for the GradientAccumulator class."""

    def setUp(self):
        """Set up test fixtures."""
        self.model = SimpleModel()
        self.optimizer = optim.SGD(self.model.parameters(), lr=0.01)
        self.accumulator = GradientAccumulator(
            model=self.model,
            optimizer=self.optimizer,
            accumulation_steps=4
        )

        # Create test data
        self.input_tensor = torch.randn(10, 10)
        self.target = torch.randn(10, 1)

    def test_gradient_accumulator_init(self):
        """Test initialization of GradientAccumulator."""
        # Test with default parameters
        accumulator = GradientAccumulator(
            model=self.model,
            optimizer=self.optimizer
        )
        self.assertEqual(accumulator.accumulation_steps, 4)
        self.assertIsNone(accumulator.clip_grad_norm)
        self.assertIsNone(accumulator.clip_grad_value)
        self.assertIsNone(accumulator.scaler)
        self.assertEqual(accumulator.current_step, 0)

        # Test with custom parameters
        accumulator = GradientAccumulator(
            model=self.model,
            optimizer=self.optimizer,
            accumulation_steps=8,
            clip_grad_norm=1.0,
            clip_grad_value=0.5
        )
        self.assertEqual(accumulator.accumulation_steps, 8)
        self.assertEqual(accumulator.clip_grad_norm, 1.0)
        self.assertEqual(accumulator.clip_grad_value, 0.5)

        # Test with invalid parameters
        with self.assertRaises(ValueError):
            GradientAccumulator(
                model=self.model,
                optimizer=self.optimizer,
                accumulation_steps=0
            )

        with self.assertRaises(ValueError):
            GradientAccumulator(
                model=self.model,
                optimizer=self.optimizer,
                clip_grad_norm=-1.0
            )

        with self.assertRaises(ValueError):
            GradientAccumulator(
                model=self.model,
                optimizer=self.optimizer,
                clip_grad_value=-0.5
            )

    def test_zero_grad(self):
        """Test zero_grad method."""
        # Set some gradients
        for param in self.model.parameters():
            param.grad = torch.ones_like(param)

        # Zero gradients at the beginning of accumulation cycle
        self.accumulator.zero_grad()

        # Check that gradients are zeroed
        for param in self.model.parameters():
            self.assertIsNone(param.grad)

        # Set some gradients again
        for param in self.model.parameters():
            param.grad = torch.ones_like(param)

        # Increment step to middle of accumulation cycle
        self.accumulator.current_step = 1

        # Zero gradients in the middle of accumulation cycle (should not zero)
        self.accumulator.zero_grad()

        # Check that gradients are not zeroed
        for param in self.model.parameters():
            self.assertIsNotNone(param.grad)

    def test_backward(self):
        """Test backward method."""
        # Create loss
        output = self.model(self.input_tensor)
        loss = nn.MSELoss()(output, self.target)

        # Zero gradients
        self.optimizer.zero_grad()

        # Backward pass with accumulation
        self.accumulator.backward(loss)

        # Check that gradients are computed
        for param in self.model.parameters():
            self.assertIsNotNone(param.grad)

    def test_step(self):
        """Test step method."""
        # Create loss
        output = self.model(self.input_tensor)
        loss = nn.MSELoss()(output, self.target)

        # Zero gradients
        self.optimizer.zero_grad()

        # Backward pass
        self.accumulator.backward(loss)

        # Step (should not update parameters yet)
        updated = self.accumulator.step()
        self.assertFalse(updated)
        self.assertEqual(self.accumulator.current_step, 1)

        # Repeat for remaining steps in accumulation cycle
        for _ in range(2):
            output = self.model(self.input_tensor)
            loss = nn.MSELoss()(output, self.target)
            self.accumulator.backward(loss)
            updated = self.accumulator.step()
            self.assertFalse(updated)

        # Final step should update parameters
        output = self.model(self.input_tensor)
        loss = nn.MSELoss()(output, self.target)
        self.accumulator.backward(loss)
        updated = self.accumulator.step()
        self.assertTrue(updated)
        self.assertEqual(self.accumulator.current_step, 4)

    def test_state_dict(self):
        """Test state_dict and load_state_dict methods."""
        # Set some state
        self.accumulator.current_step = 2
        self.accumulator.accumulation_steps = 8
        self.accumulator.clip_grad_norm = 1.0
        self.accumulator.clip_grad_value = 0.5

        # Get state dict
        state_dict = self.accumulator.state_dict()

        # Check state dict contents
        self.assertEqual(state_dict["current_step"], 2)
        self.assertEqual(state_dict["accumulation_steps"], 8)
        self.assertEqual(state_dict["clip_grad_norm"], 1.0)
        self.assertEqual(state_dict["clip_grad_value"], 0.5)

        # Create new accumulator
        new_accumulator = GradientAccumulator(
            model=self.model,
            optimizer=self.optimizer
        )

        # Load state dict
        new_accumulator.load_state_dict(state_dict)

        # Check that state was loaded correctly
        self.assertEqual(new_accumulator.current_step, 2)
        self.assertEqual(new_accumulator.accumulation_steps, 8)
        self.assertEqual(new_accumulator.clip_grad_norm, 1.0)
        self.assertEqual(new_accumulator.clip_grad_value, 0.5)

class TestAccumulationTrainer(unittest.TestCase):
    """Test cases for the AccumulationTrainer class."""

    def setUp(self):
        """Set up test fixtures."""
        self.model = SimpleModel()
        self.optimizer = optim.SGD(self.model.parameters(), lr=0.01)

        # Define loss function
        def loss_fn(outputs, batch):
            inputs, targets = batch
            return nn.MSELoss()(outputs, targets)

        self.loss_fn = loss_fn

        # Create trainer
        self.trainer = AccumulationTrainer(
            model=self.model,
            optimizer=self.optimizer,
            loss_fn=self.loss_fn,
            accumulation_steps=4
        )

        # Create test data
        self.input_tensor = torch.randn(10, 10)
        self.target = torch.randn(10, 1)
        self.batch = (self.input_tensor, self.target)

    def test_trainer_init(self):
        """Test initialization of AccumulationTrainer."""
        # Test with default parameters
        trainer = AccumulationTrainer(
            model=self.model,
            optimizer=self.optimizer,
            loss_fn=self.loss_fn
        )
        self.assertEqual(trainer.accumulator.accumulation_steps, 4)
        self.assertFalse(trainer.use_amp)

        # Test with custom parameters
        trainer = AccumulationTrainer(
            model=self.model,
            optimizer=self.optimizer,
            loss_fn=self.loss_fn,
            accumulation_steps=8,
            clip_grad_norm=1.0,
            use_amp=True
        )
        self.assertEqual(trainer.accumulator.accumulation_steps, 8)
        self.assertEqual(trainer.accumulator.clip_grad_norm, 1.0)
        self.assertTrue(trainer.use_amp)

    def test_train_step(self):
        """Test train_step method."""
        # Perform a training step
        loss = self.trainer.train_step(self.batch)

        # Check that loss is a float
        self.assertIsInstance(loss, float)

        # Check that metrics are updated
        self.assertEqual(self.trainer.train_steps, 1)
        self.assertGreater(self.trainer.train_loss, 0)

    def test_checkpoint(self):
        """Test save_checkpoint and load_checkpoint methods."""
        import tempfile
        import os

        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False) as temp:
            checkpoint_path = temp.name

        try:
            # Perform some training steps
            for _ in range(5):
                self.trainer.train_step(self.batch)

            # Save checkpoint
            self.trainer.save_checkpoint(checkpoint_path)

            # Create a new trainer
            new_trainer = AccumulationTrainer(
                model=SimpleModel(),
                optimizer=optim.SGD(self.model.parameters(), lr=0.01),
                loss_fn=self.loss_fn
            )

            # Load checkpoint
            new_trainer.load_checkpoint(checkpoint_path)

            # Check that state was loaded correctly
            self.assertEqual(new_trainer.train_steps, self.trainer.train_steps)
            self.assertEqual(new_trainer.train_loss, self.trainer.train_loss)
        finally:
            # Clean up
            os.unlink(checkpoint_path)

class TestMemorySavingsEstimation(unittest.TestCase):
    """Test cases for the memory savings estimation function."""

    def setUp(self):
        """Set up test fixtures."""
        self.model = SimpleModel()

    def test_estimate_memory_savings(self):
        """Test estimate_memory_savings function."""
        # Test with input shape
        savings = estimate_memory_savings(
            model=self.model,
            batch_size=32,
            accumulation_steps=4,
            input_shape=(10,)
        )

        # Check that the dictionary contains the expected keys
        expected_keys = [
            "total_params",
            "params_memory_mb",
            "original_batch_size",
            "effective_batch_size",
            "memory_without_accumulation_mb",
            "memory_with_accumulation_mb",
            "absolute_savings_mb",
            "percentage_savings"
        ]
        for key in expected_keys:
            self.assertIn(key, savings)

        # Check that the values are reasonable
        self.assertGreater(savings["total_params"], 0)
        self.assertGreater(savings["params_memory_mb"], 0)
        self.assertEqual(savings["original_batch_size"], 32)
        self.assertEqual(savings["effective_batch_size"], 128)
        self.assertGreater(savings["memory_without_accumulation_mb"], 0)
        self.assertGreater(savings["memory_with_accumulation_mb"], 0)
        self.assertGreater(savings["absolute_savings_mb"], 0)
        self.assertGreater(savings["percentage_savings"], 0)

        # Check that memory with accumulation is less than without
        self.assertLess(
            savings["memory_with_accumulation_mb"],
            savings["memory_without_accumulation_mb"]
        )

        # Test without input shape
        savings_no_input = estimate_memory_savings(
            model=self.model,
            batch_size=32,
            accumulation_steps=4
        )

        # Check that the dictionary contains the expected keys
        for key in expected_keys:
            self.assertIn(key, savings_no_input)

if __name__ == '__main__':
    unittest.main()
