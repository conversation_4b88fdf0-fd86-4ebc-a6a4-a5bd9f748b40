"""
Unit tests for the Parallel Inference module.
"""

import unittest
import time
import threading
from typing import Dict, Any, List
from unittest.mock import MagicMock, patch

from src.deep_research_core.optimization.parallel.inference import ParallelInference, BatchProcessor
from src.deep_research_core.optimization.parallel.thread_management import AdaptiveThreadPool


class MockModel:
    """Mock model class for testing."""
    
    def __init__(self, delay: float = 0.01, fail_ratio: float = 0.0):
        """
        Initialize mock model.
        
        Args:
            delay: Simulated processing delay in seconds
            fail_ratio: Ratio of requests that should fail (0.0-1.0)
        """
        self.delay = delay
        self.fail_ratio = fail_ratio
        self.call_count = 0
        self.last_inputs = None
        self.batch_call_count = 0
    
    def inference(self, input_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Mock inference method.
        
        Args:
            input_data: Input data
            **kwargs: Additional arguments
            
        Returns:
            Output data
        """
        self.call_count += 1
        self.last_inputs = input_data
        
        # Simulate processing delay
        time.sleep(self.delay)
        
        # Simulate random failures
        import random
        if random.random() < self.fail_ratio:
            raise RuntimeError("Simulated inference failure")
            
        return {
            "input": input_data,
            "output": f"Processed {input_data}",
            "model_name": kwargs.get("model_name", "default"),
            "processing_time": self.delay
        }
    
    def batch_inference(self, inputs: List[Dict[str, Any]], **kwargs) -> List[Dict[str, Any]]:
        """
        Mock batch inference method.
        
        Args:
            inputs: List of input data
            **kwargs: Additional arguments
            
        Returns:
            List of output data
        """
        self.batch_call_count += 1
        
        # Simulate processing delay (slightly more efficient than individual calls)
        batch_delay = self.delay * len(inputs) * 0.8
        time.sleep(batch_delay)
        
        results = []
        for i, input_data in enumerate(inputs):
            # Simulate random failures
            import random
            if random.random() < self.fail_ratio:
                results.append(None)
                continue
                
            results.append({
                "input": input_data,
                "output": f"Batch processed {input_data}",
                "model_name": kwargs.get("model_name", "default"),
                "batch_idx": i,
                "processing_time": batch_delay / len(inputs)
            })
            
        return results


class TestParallelInference(unittest.TestCase):
    """Test cases for ParallelInference class."""
    
    def setUp(self):
        """Set up test environment."""
        self.model = MockModel(delay=0.01)
        
    def test_basic_inference(self):
        """Test basic inference functionality."""
        parallel_inference = ParallelInference(
            model=self.model,
            max_workers=2,
            batch_size=1,
            adaptive=False
        )
        
        inputs = [
            {"text": "Sample input 1"},
            {"text": "Sample input 2"},
            {"text": "Sample input 3"},
        ]
        
        results = parallel_inference.infer(inputs)
        
        # Check that we got all results
        self.assertEqual(len(results), len(inputs))
        self.assertIsNotNone(results[0])
        self.assertIsNotNone(results[1])
        self.assertIsNotNone(results[2])
        
        # Check that individual inference was called for each input
        self.assertEqual(self.model.call_count, 3)
        self.assertEqual(self.model.batch_call_count, 0)
        
        # Cleanup
        parallel_inference.shutdown()
        
    def test_batch_inference(self):
        """Test batch inference functionality."""
        # Create a model with batch inference capability
        batch_model = MockModel(delay=0.01)
        
        parallel_inference = ParallelInference(
            model=batch_model,
            max_workers=2,
            batch_size=2,  # Process in batches of 2
            adaptive=False
        )
        
        inputs = [
            {"text": "Sample input 1"},
            {"text": "Sample input 2"},
            {"text": "Sample input 3"},
            {"text": "Sample input 4"},
            {"text": "Sample input 5"},
        ]
        
        results = parallel_inference.infer(inputs)
        
        # Check that we got all results
        self.assertEqual(len(results), len(inputs))
        for result in results:
            self.assertIsNotNone(result)
            
        # There should be 3 batch calls (2+2+1)
        self.assertEqual(batch_model.batch_call_count, 3)
        
        # Cleanup
        parallel_inference.shutdown()
        
    def test_error_handling(self):
        """Test error handling in parallel inference."""
        # Create a model with 50% failure rate
        failing_model = MockModel(delay=0.01, fail_ratio=0.5)
        
        parallel_inference = ParallelInference(
            model=failing_model,
            max_workers=2,
            batch_size=1,
            adaptive=False,
            timeout=1.0  # Set short timeout
        )
        
        inputs = [
            {"text": "Sample input 1"},
            {"text": "Sample input 2"},
            {"text": "Sample input 3"},
            {"text": "Sample input 4"},
            {"text": "Sample input 5"},
        ]
        
        # Execute inference
        results = parallel_inference.infer(inputs)
        
        # Some results may be None due to failures
        self.assertEqual(len(results), len(inputs))
        
        # Get stats to check failures
        stats = parallel_inference.get_stats()
        self.assertEqual(stats["total_requests"], 5)
        self.assertGreaterEqual(stats["failed_requests"], 0)
        
        # Cleanup
        parallel_inference.shutdown()
        
    def test_adaptive_thread_pool(self):
        """Test adaptive thread pool behavior."""
        # Mock resource monitor to simulate CPU load
        with patch('src.deep_research_core.optimization.parallel.thread_management.ResourceMonitor') as mock_monitor:
            # Set up the mock
            mock_instance = mock_monitor.return_value
            mock_instance.get_current_usage.return_value = {
                "cpu_usage": 50.0,
                "memory_usage": 50.0,
                "process_cpu": 25.0,
                "process_memory": 30.0,
                "gpu_usage": None
            }
            
            parallel_inference = ParallelInference(
                model=self.model,
                max_workers=4,
                batch_size=1,
                adaptive=True
            )
            
            # Execute some inference to trigger resource monitoring
            inputs = [{"text": f"Sample input {i}"} for i in range(10)]
            parallel_inference.infer(inputs)
            
            # Verify that resource monitor was started
            mock_instance.start.assert_called_once()
            
            # Cleanup
            parallel_inference.shutdown()
            mock_instance.stop.assert_called_once()
            
    def test_callback(self):
        """Test callback functionality."""
        results_from_callback = []
        
        def capture_result(result):
            results_from_callback.append(result)
            
        parallel_inference = ParallelInference(
            model=self.model,
            max_workers=2,
            batch_size=1,
            adaptive=False
        )
        
        inputs = [
            {"text": "Sample input 1"},
            {"text": "Sample input 2"},
            {"text": "Sample input 3"},
        ]
        
        results = parallel_inference.infer(inputs, callback=capture_result)
        
        # Check that callback was called for each result
        self.assertEqual(len(results_from_callback), 3)
        
        # Cleanup
        parallel_inference.shutdown()


class TestBatchProcessor(unittest.TestCase):
    """Test cases for BatchProcessor class."""
    
    def setUp(self):
        """Set up test environment."""
        self.model = MockModel(delay=0.01)
        self.parallel_inference = ParallelInference(
            model=self.model,
            max_workers=2,
            batch_size=2,
            adaptive=False
        )
        
    def tearDown(self):
        """Clean up after tests."""
        self.parallel_inference.shutdown()
        
    def test_basic_processing(self):
        """Test basic batch processing functionality."""
        batch_processor = BatchProcessor(
            parallel_inference=self.parallel_inference,
            max_chunk_size=3
        )
        
        # Create a larger dataset
        data = [{"text": f"Sample input {i}"} for i in range(10)]
        
        # Process the data
        results = batch_processor.process(
            data=data,
            show_progress=False
        )
        
        # Check results
        self.assertEqual(len(results), len(data))
        self.assertIsNotNone(results[0])
        
        # Check stats
        stats = batch_processor.get_stats()
        self.assertEqual(stats["total_processed"], 10)
        self.assertEqual(stats["chunks_processed"], 4)  # Math.ceil(10/3) = 4
        
    def test_resume_processing(self):
        """Test resuming batch processing from an index."""
        batch_processor = BatchProcessor(
            parallel_inference=self.parallel_inference,
            max_chunk_size=3
        )
        
        # Create a dataset
        data = [{"text": f"Sample input {i}"} for i in range(10)]
        
        # Process from index 5
        results = batch_processor.process(
            data=data,
            resume_from=5,
            show_progress=False
        )
        
        # Check results
        self.assertEqual(len(results), len(data))
        self.assertIsNone(results[0])  # First 5 items should be None
        self.assertIsNone(results[4])
        self.assertIsNotNone(results[5])  # Items from index 5 should be processed
        
        # Check stats
        stats = batch_processor.get_stats()
        self.assertEqual(stats["total_processed"], 5)  # Only processed 5 items
        self.assertEqual(stats["chunks_processed"], 2)  # Math.ceil(5/3) = 2
        
    def test_callback(self):
        """Test callback functionality in batch processor."""
        chunk_results = []
        
        def capture_chunk(chunk_result):
            chunk_results.append(chunk_result)
            
        batch_processor = BatchProcessor(
            parallel_inference=self.parallel_inference,
            max_chunk_size=3
        )
        
        # Create a dataset
        data = [{"text": f"Sample input {i}"} for i in range(10)]
        
        # Process with callback
        batch_processor.process(
            data=data,
            show_progress=False,
            callback=capture_chunk
        )
        
        # Check that callback was called for each chunk
        self.assertEqual(len(chunk_results), 4)  # Math.ceil(10/3) = 4


if __name__ == '__main__':
    unittest.main() 