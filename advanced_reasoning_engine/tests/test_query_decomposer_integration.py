#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test cho module tích hợp QueryDecomposer.
"""

import unittest
import sys
import os
import json
from unittest.mock import patch, MagicMock

# Thêm thư mục gốc vào sys.path để import các module
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

try:
    from src.deep_research_core.agents.query_decomposer_integration import (
        integrate_query_decomposer,
        decompose_and_search,
        _parallel_search,
        _sequential_search,
        _merge_search_results
    )
except ImportError:
    # Thử import từ thư mục hiện tại
    from deepresearch.src.deep_research_core.agents.query_decomposer_integration import (
        integrate_query_decomposer,
        decompose_and_search,
        _parallel_search,
        _sequential_search,
        _merge_search_results
    )


class TestQueryDecomposerIntegration(unittest.TestCase):
    """Test cho module tích hợp QueryDecomposer."""

    def setUp(self):
        """Thiết lập cho các test."""
        # Tạo mock cho WebSearchAgentLocal
        self.mock_agent = MagicMock()
        self.mock_agent.verbose = True
        self.mock_agent.search = MagicMock(return_value={
            "success": True,
            "query": "test query",
            "results": [
                {"title": "Result 1", "url": "http://example.com/1", "score": 0.9},
                {"title": "Result 2", "url": "http://example.com/2", "score": 0.8},
            ],
            "engine": "test",
            "search_method": "local",
            "execution_time": 1.0,
            "timestamp": **********
        })

    @patch('src.deep_research_core.reasoning.query_decomposer.QueryDecomposer')
    def test_integrate_query_decomposer(self, mock_query_decomposer):
        """Test tích hợp QueryDecomposer vào WebSearchAgentLocal."""
        # Thiết lập mock
        mock_query_decomposer.return_value = MagicMock()
        
        # Gọi hàm tích hợp
        integrate_query_decomposer(self.mock_agent, {
            "provider": "openrouter",
            "model": "test-model",
            "temperature": 0.3,
            "max_tokens": 1000,
            "language": "auto",
            "use_cache": True,
            "cache_size": 100,
            "use_mock_for_testing": True,
        })
        
        # Kiểm tra kết quả
        self.assertTrue(hasattr(self.mock_agent, "query_decomposer"))
        self.assertTrue(self.mock_agent.use_query_decomposer)
        
        # Kiểm tra QueryDecomposer được khởi tạo với các tham số đúng
        mock_query_decomposer.assert_called_once_with(
            provider="openrouter",
            model="test-model",
            temperature=0.3,
            max_tokens=1000,
            language="auto",
            use_cache=True,
            cache_size=100,
            use_mock_for_testing=True,
        )

    def test_merge_search_results(self):
        """Test gộp kết quả tìm kiếm từ nhiều câu hỏi con."""
        # Tạo dữ liệu test
        all_results = [
            {
                "success": True,
                "query": "sub query 1",
                "results": [
                    {"title": "Result 1", "url": "http://example.com/1", "score": 0.9},
                    {"title": "Result 2", "url": "http://example.com/2", "score": 0.8},
                ],
            },
            {
                "success": True,
                "query": "sub query 2",
                "results": [
                    {"title": "Result 3", "url": "http://example.com/3", "score": 0.7},
                    {"title": "Result 1", "url": "http://example.com/1", "score": 0.6},  # Trùng lặp
                ],
            },
        ]
        
        # Gọi hàm gộp kết quả
        merged_results = _merge_search_results(all_results, "original query", 3)
        
        # Kiểm tra kết quả
        self.assertTrue(merged_results["success"])
        self.assertEqual(merged_results["query"], "original query")
        self.assertEqual(len(merged_results["results"]), 3)  # Giới hạn 3 kết quả
        self.assertEqual(merged_results["engine"], "query_decomposer")
        
        # Kiểm tra kết quả trùng lặp đã được loại bỏ
        urls = [result["url"] for result in merged_results["results"]]
        self.assertEqual(len(urls), len(set(urls)))  # Không có URL trùng lặp

    @patch('src.deep_research_core.reasoning.query_decomposer.QueryDecomposer')
    def test_decompose_and_search(self, mock_query_decomposer):
        """Test phân rã câu hỏi và tìm kiếm."""
        # Thiết lập mock
        mock_decomposer = MagicMock()
        mock_decomposer.decompose.return_value = [
            {"query": "sub query 1", "focus": "aspect 1", "complexity": "medium"},
            {"query": "sub query 2", "focus": "aspect 2", "complexity": "low"},
        ]
        self.mock_agent.query_decomposer = mock_decomposer
        self.mock_agent.use_query_decomposer = True
        
        # Gọi hàm phân rã và tìm kiếm
        result = decompose_and_search(
            self.mock_agent,
            query="complex query",
            num_results=5,
            language="auto",
            max_sub_queries=3,
            min_sub_queries=1,
            parallel_search=False,
            merge_results=True
        )
        
        # Kiểm tra kết quả
        self.assertTrue(result["success"])
        self.assertEqual(result["query"], "complex query")
        self.assertEqual(result["engine"], "query_decomposer")
        
        # Kiểm tra mock_decomposer.decompose được gọi
        mock_decomposer.decompose.assert_called_once_with(
            "complex query", max_sub_queries=3, min_sub_queries=1
        )
        
        # Kiểm tra self.mock_agent.search được gọi cho mỗi câu hỏi con
        self.assertEqual(self.mock_agent.search.call_count, 2)


if __name__ == '__main__':
    unittest.main()
