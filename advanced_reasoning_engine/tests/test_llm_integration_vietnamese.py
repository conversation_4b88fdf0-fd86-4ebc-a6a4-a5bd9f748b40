"""
Tests for LLM integration with Vietnamese support.
"""

import unittest
from unittest.mock import MagicMock, patch
import json
import time

from deep_research_core.rl_tuning.environment.llm_integration import LLMIntegration


class TestLLMIntegrationVietnamese(unittest.TestCase):
    """Test cases for LLM integration with Vietnamese support."""

    def setUp(self):
        """Set up test environment."""
        # Create mock model
        self.mock_model = MagicMock()
        self.mock_model.generate.return_value = "This is a test response"
        
        # Initialize LLM integration
        self.llm_integration = LLMIntegration(
            model_instance=self.mock_model,
            model_id="test-model",
            temperature=0.7,
            max_tokens=1000
        )
        
        # Mock Vietnamese utilities
        self.patch_adapt = patch('deep_research_core.utils.vietnamese_utils.adapt_prompt_for_vietnamese')
        self.mock_adapt = self.patch_adapt.start()
        self.mock_adapt.side_effect = lambda text: f"[Adapted] {text}"

    def tearDown(self):
        """Clean up after tests."""
        self.patch_adapt.stop()

    def test_generate_vietnamese_response(self):
        """Test generating Vietnamese responses."""
        # Test with default parameters
        response = self.llm_integration.generate_vietnamese_response("Test prompt")
        
        # Check that the model was called with adapted prompt
        self.mock_model.generate.assert_called()
        args, kwargs = self.mock_model.generate.call_args
        
        # Check that the prompt was adapted for Vietnamese
        self.assertIn("[Adapted]", kwargs.get("prompt", ""))
        self.assertIn("Hãy trả lời bằng tiếng Việt", kwargs.get("prompt", ""))
        
        # Check that the system prompt was set for Vietnamese
        self.assertIn("tiếng Việt", kwargs.get("system_prompt", ""))
        
        # Check that usage stats were updated
        self.assertIn("vietnamese_generations", self.llm_integration.usage_stats)
        self.assertGreater(self.llm_integration.usage_stats["vietnamese_generations"], 0)

    def test_translate_to_vietnamese(self):
        """Test translation to Vietnamese."""
        # Test with default parameters
        translation = self.llm_integration.translate_to_vietnamese("Test text")
        
        # Check that the model was called with translation prompt
        self.mock_model.generate.assert_called()
        args, kwargs = self.mock_model.generate.call_args
        
        # Check that the prompt includes the text to translate
        self.assertIn("Test text", kwargs.get("prompt", ""))
        self.assertIn("Translate", kwargs.get("prompt", ""))
        
        # Check that the system prompt was set for translation
        self.assertIn("translator", kwargs.get("system_prompt", ""))
        
        # Check that usage stats were updated
        self.assertIn("translations", self.llm_integration.usage_stats)
        self.assertGreater(self.llm_integration.usage_stats["translations"], 0)

    def test_analyze_vietnamese_text(self):
        """Test analyzing Vietnamese text."""
        # Mock JSON response
        json_response = json.dumps({
            "language_quality": "good",
            "themes": ["test"],
            "style_and_tone": "formal",
            "dialect_indicators": ["northern"],
            "cultural_elements": ["traditional"],
            "summary": "This is a test"
        })
        self.mock_model.generate.return_value = f"```json\n{json_response}\n```"
        
        # Test general analysis
        analysis = self.llm_integration.analyze_vietnamese_text("Test Vietnamese text")
        
        # Check that the model was called with analysis prompt
        self.mock_model.generate.assert_called()
        args, kwargs = self.mock_model.generate.call_args
        
        # Check that the prompt includes the text to analyze
        self.assertIn("Test Vietnamese text", kwargs.get("prompt", ""))
        self.assertIn("Analyze", kwargs.get("prompt", ""))
        
        # Check that the system prompt was set for analysis
        self.assertIn("expert", kwargs.get("system_prompt", ""))
        
        # Check that usage stats were updated
        self.assertIn("text_analyses", self.llm_integration.usage_stats)
        self.assertGreater(self.llm_integration.usage_stats["text_analyses"], 0)
        
        # Check that the response was parsed correctly
        self.assertIsInstance(analysis, dict)
        self.assertIn("language_quality", analysis)
        
        # Test sentiment analysis
        self.mock_model.generate.return_value = json_response  # Direct JSON without markdown
        analysis = self.llm_integration.analyze_vietnamese_text("Test Vietnamese text", analysis_type="sentiment")
        
        # Check that the prompt includes sentiment analysis instructions
        args, kwargs = self.mock_model.generate.call_args
        self.assertIn("sentiment", kwargs.get("prompt", ""))
        
        # Test with invalid JSON response
        self.mock_model.generate.return_value = "This is not valid JSON"
        analysis = self.llm_integration.analyze_vietnamese_text("Test Vietnamese text")
        
        # Check that error handling works
        self.assertIn("raw_analysis", analysis)
        self.assertIn("parsing_error", analysis)

    def test_error_handling(self):
        """Test error handling in Vietnamese methods."""
        # Test with model error
        self.mock_model.generate.side_effect = Exception("Test error")
        
        # Test Vietnamese response generation
        response = self.llm_integration.generate_vietnamese_response("Test prompt")
        self.assertIn("Lỗi", response)
        
        # Test translation
        translation = self.llm_integration.translate_to_vietnamese("Test text")
        self.assertIn("Lỗi dịch", translation)
        
        # Test analysis
        analysis = self.llm_integration.analyze_vietnamese_text("Test text")
        self.assertIn("error", analysis)
        
        # Check that error count was updated
        self.assertGreater(self.llm_integration.usage_stats["errors"], 0)

    def test_usage_stats(self):
        """Test usage statistics for Vietnamese methods."""
        # Reset mock
        self.mock_model.generate.side_effect = None
        self.mock_model.generate.return_value = "Test response"
        
        # Generate some activity
        self.llm_integration.generate_vietnamese_response("Test prompt 1")
        time.sleep(0.01)  # Ensure measurable latency
        self.llm_integration.translate_to_vietnamese("Test text 1")
        time.sleep(0.01)  # Ensure measurable latency
        self.llm_integration.analyze_vietnamese_text("Test text 2")
        
        # Get usage stats
        stats = self.llm_integration.get_usage_stats()
        
        # Check that all stats are present
        self.assertIn("total_calls", stats)
        self.assertIn("vietnamese_generations", stats)
        self.assertIn("translations", stats)
        self.assertIn("text_analyses", stats)
        self.assertIn("latency", stats)
        self.assertIn("avg_latency", stats)
        
        # Check that counts are correct
        self.assertEqual(stats["vietnamese_generations"], 1)
        self.assertEqual(stats["translations"], 1)
        self.assertEqual(stats["text_analyses"], 1)
        self.assertEqual(stats["total_calls"], 3)
        
        # Check that latency was recorded
        self.assertEqual(len(stats["latency"]), 3)
        self.assertGreater(stats["avg_latency"], 0)


if __name__ == "__main__":
    unittest.main()
