#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test cho CaptchaHandler integration.
"""

import unittest
import os
import sys
import time
from unittest.mock import MagicMock, patch

# Thêm thư mục gốc vào sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.agents.captcha_handler_integration import (
    integrate_captcha_handler,
    detect_captcha,
    handle_captcha,
    is_domain_known_captcha,
    clear_captcha_cache,
    handle_captcha_handler_error,
    <PERSON><PERSON><PERSON><PERSON><PERSON>Error,
    CaptchaHandlerNotAvailableError,
    Capt<PERSON><PERSON>andlerConfigError,
    CaptchaHandlerDetectionError,
    CaptchaHandlerSolvingError
)

class MockAgent:
    """Mock agent for testing."""

    def __init__(self):
        """Initialize mock agent."""
        self._captcha_handler = None
        self.has_captcha_handler = False
        self.captcha_domains = set()
        self.last_captcha_detection = {}

class TestCaptchaHandlerIntegration(unittest.TestCase):
    """Test cho CaptchaHandler integration."""

    def setUp(self):
        """Thiết lập trước mỗi test."""
        self.agent = MockAgent()

        # Sample HTML with CAPTCHA
        self.captcha_html = """
        <html>
            <head><title>Security Check</title></head>
            <body>
                <h1>Please complete the CAPTCHA</h1>
                <div class="g-recaptcha" data-sitekey="6LdQUOkUAAAAAJ6NhQr6c1O4I_T6JqM3TbRjJ3mB"></div>
                <p>Prove you are human to continue</p>
            </body>
        </html>
        """

        # Sample HTML without CAPTCHA
        self.normal_html = """
        <html>
            <head><title>Normal Page</title></head>
            <body>
                <h1>Welcome to our website</h1>
                <p>This is a normal page without CAPTCHA</p>
            </body>
        </html>
        """

        # Sample HTML with Vietnamese CAPTCHA
        self.vietnamese_captcha_html = """
        <html>
            <head><title>Kiểm tra bảo mật</title></head>
            <body>
                <h1>Vui lòng nhập mã xác nhận</h1>
                <div class="captcha">
                    <img src="/captcha.php" alt="Mã xác nhận" />
                    <input type="text" name="captcha" placeholder="Nhập mã xác nhận" />
                </div>
                <p>Xác nhận bạn không phải là robot</p>
            </body>
        </html>
        """

    @patch('src.deep_research_core.agents.captcha_handler_integration.CaptchaHandler')
    def test_integrate_captcha_handler(self, mock_captcha_handler):
        """Test tích hợp CaptchaHandler."""
        # Thiết lập mock
        mock_captcha_handler.return_value = MagicMock()

        # Gọi hàm tích hợp
        integrate_captcha_handler(self.agent)

        # Kiểm tra kết quả
        self.assertTrue(self.agent.has_captcha_handler)
        self.assertIsNotNone(self.agent._captcha_handler)
        self.assertTrue(hasattr(self.agent, 'captcha_domains'))
        self.assertTrue(hasattr(self.agent, 'last_captcha_detection'))

    @patch('src.deep_research_core.agents.captcha_handler_integration.CaptchaHandler')
    def test_detect_captcha(self, mock_captcha_handler):
        """Test phát hiện CAPTCHA."""
        # Thiết lập mock
        mock_handler = MagicMock()
        mock_handler.detect_captcha.return_value = (True, "recaptcha", {"site_key": "test"})
        mock_captcha_handler.return_value = mock_handler

        # Tích hợp CaptchaHandler
        integrate_captcha_handler(self.agent)

        # Gọi hàm phát hiện CAPTCHA
        has_captcha, captcha_type, captcha_data = detect_captcha(self.agent, self.captcha_html)

        # Kiểm tra kết quả
        self.assertTrue(has_captcha)
        self.assertEqual(captcha_type, "recaptcha")
        self.assertEqual(captcha_data, {"site_key": "test"})

    @patch('src.deep_research_core.agents.captcha_handler_integration.CaptchaHandler')
    def test_handle_captcha(self, mock_captcha_handler):
        """Test xử lý CAPTCHA."""
        # Thiết lập mock
        mock_handler = MagicMock()
        mock_handler.detect_captcha.return_value = (True, "recaptcha", {"site_key": "test"})
        mock_handler.solve_captcha.return_value = "SOLVED"
        mock_handler.handle_captcha.return_value = {
            "success": True,
            "handled": True,
            "message": "CAPTCHA đã được xử lý",
            "url": "https://example.com"
        }
        mock_captcha_handler.return_value = mock_handler

        # Tích hợp CaptchaHandler
        integrate_captcha_handler(self.agent)

        # Gán phương thức handle_captcha trực tiếp
        self.agent._captcha_handler.handle_captcha = mock_handler.handle_captcha

        # Gọi hàm xử lý CAPTCHA
        result = handle_captcha(self.agent, "https://example.com", self.captcha_html)

        # Kiểm tra kết quả
        self.assertEqual(result["url"], "https://example.com")
        # Kiểm tra các thuộc tính khác nếu có
        if "success" in result:
            self.assertTrue(result["success"])
        if "handled" in result:
            self.assertTrue(result["handled"])

    @patch('src.deep_research_core.agents.captcha_handler_integration.CaptchaHandler')
    def test_is_domain_known_captcha(self, mock_captcha_handler):
        """Test kiểm tra domain có CAPTCHA đã biết."""
        # Thiết lập mock
        mock_handler = MagicMock()
        mock_handler.is_domain_known_captcha.return_value = True
        mock_captcha_handler.return_value = mock_handler

        # Tích hợp CaptchaHandler
        integrate_captcha_handler(self.agent)

        # Thêm domain vào danh sách domain có CAPTCHA
        self.agent.captcha_domains.add("example.com")

        # Gọi hàm kiểm tra domain có CAPTCHA đã biết
        is_known = is_domain_known_captcha(self.agent, "https://example.com/page")

        # Kiểm tra kết quả
        self.assertTrue(is_known)

    @patch('src.deep_research_core.agents.captcha_handler_integration.CaptchaHandler')
    def test_clear_captcha_cache(self, mock_captcha_handler):
        """Test xóa cache CAPTCHA."""
        # Thiết lập mock
        mock_handler = MagicMock()
        mock_captcha_handler.return_value = mock_handler

        # Tích hợp CaptchaHandler
        integrate_captcha_handler(self.agent)

        # Thêm domain vào danh sách domain có CAPTCHA
        self.agent.captcha_domains.add("example.com")

        # Thêm thông tin phát hiện CAPTCHA
        self.agent.last_captcha_detection = {
            "https://example.com": time.time()
        }

        # Gọi hàm xóa cache CAPTCHA
        clear_captcha_cache(self.agent)

        # Kiểm tra kết quả
        self.assertEqual(len(self.agent.captcha_domains), 0)
        self.assertEqual(len(self.agent.last_captcha_detection), 0)

    def test_handle_captcha_handler_error(self):
        """Test xử lý lỗi CaptchaHandler."""
        # Tạo lỗi
        error = CaptchaHandlerError("Lỗi test", url="https://example.com")

        # Gọi hàm xử lý lỗi
        result = handle_captcha_handler_error(error, "https://example.com", "test_context")

        # Kiểm tra kết quả
        self.assertFalse(result["success"])
        self.assertEqual(result["error"], "Lỗi test")
        self.assertEqual(result["error_type"], "CaptchaHandlerError")
        self.assertEqual(result["error_category"], "captcha_handler_error")

    def test_handle_captcha_handler_error_with_different_error_types(self):
        """Test xử lý lỗi CaptchaHandler với các loại lỗi khác nhau."""
        # Tạo các loại lỗi
        errors = [
            (CaptchaHandlerNotAvailableError("CaptchaHandler không khả dụng"), "captcha_handler_error"),
            (CaptchaHandlerConfigError("Lỗi cấu hình"), "captcha_handler_error"),
            (CaptchaHandlerDetectionError("Lỗi phát hiện CAPTCHA"), "captcha_handler_error"),
            (CaptchaHandlerSolvingError("Lỗi giải CAPTCHA"), "captcha_handler_error"),
            (ConnectionError("Lỗi kết nối"), "connection_error"),
            (TimeoutError("Lỗi timeout"), "timeout_error"),
            (Exception("Lỗi không xác định"), "unknown_error")
        ]

        # Kiểm tra từng loại lỗi
        for error, expected_category in errors:
            result = handle_captcha_handler_error(error, "https://example.com", "test_context")
            self.assertEqual(result["error_category"], expected_category)

    def test_captcha_handler_integration_with_web_search_agent(self):
        """Test tích hợp CaptchaHandler với WebSearchAgentLocal."""
        # Bỏ qua test này vì cần WebSearchAgentLocal thực tế
        self.skipTest("Cần WebSearchAgentLocal thực tế để test")

    def test_captcha_handler_integration_with_adaptive_crawler(self):
        """Test tích hợp CaptchaHandler với AdaptiveCrawler."""
        # Bỏ qua test này vì cần AdaptiveCrawler thực tế
        self.skipTest("Cần AdaptiveCrawler thực tế để test")

if __name__ == "__main__":
    unittest.main()
