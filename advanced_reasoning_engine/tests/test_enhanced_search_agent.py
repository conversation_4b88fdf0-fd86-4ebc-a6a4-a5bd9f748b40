"""
Test for enhanced search agent.
"""

import unittest
import sys
import os
import json
from unittest.mock import patch, MagicMock

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from deepresearch.src.deep_research_core.agents.web_search_agent_local import (
    WebSearchAgentLocal,
)
from deepresearch.src.deep_research_core.utils.language_detector import LanguageDetector
from deepresearch.src.deep_research_core.utils.feedback_collector import (
    FeedbackCollector,
)


class TestEnhancedSearchAgent(unittest.TestCase):
    """Test the enhanced search agent."""

    def setUp(self):
        """Set up the test."""
        # Create a test agent
        self.agent = WebSearchAgentLocal(
            search_method="auto", cache_ttl=60, rate_limit=100, verbose=True
        )

    def test_initialization(self):
        """Test initialization."""
        # Check that the agent was initialized correctly
        self.assertEqual(self.agent.search_method, "auto")
        self.assertTrue(self.agent.use_language_detector)
        self.assertTrue(self.agent.collect_feedback)

    @patch(
        "src.deep_research_core.utils.language_detector.language_detector.detect_language"
    )
    def test_language_detection(self, mock_detect_language):
        """Test language detection."""
        # Mock the language detector
        mock_detect_language.return_value = {
            "language": "vi",
            "confidence": 0.95,
            "languages": {"vi": 0.95, "en": 0.05},
            "method": "combined",
        }

        # Call search with auto language detection
        with patch.object(
            self.agent, "_search_searxng", return_value={"success": True, "results": []}
        ):
            results = self.agent.search(
                query="Thủ đô của Việt Nam là gì?", language="auto"
            )

            # Check that language detection was called
            mock_detect_language.assert_called_once()

    @patch(
        "src.deep_research_core.utils.feedback_collector.feedback_collector.add_feedback"
    )
    def test_feedback_collection(self, mock_add_feedback):
        """Test feedback collection."""
        # Mock the feedback collector
        mock_add_feedback.return_value = True

        # Add feedback
        result = self.agent.add_feedback(
            query="test query", url="https://example.com", rating=5
        )

        # Check that feedback was added
        self.assertTrue(result)
        mock_add_feedback.assert_called_once_with(
            query="test query",
            engine="auto",
            url="https://example.com",
            rating=5,
            user_id=None,
            session_id=None,
            metadata=None,
        )

    @patch(
        "src.deep_research_core.utils.captcha_handler.captcha_handler.detect_captcha"
    )
    @patch("src.deep_research_core.utils.captcha_handler.captcha_handler.solve_captcha")
    def test_captcha_handling(self, mock_solve_captcha, mock_detect_captcha):
        """Test CAPTCHA handling."""
        # Set up the agent to handle CAPTCHAs
        self.agent.handle_captcha = True

        # Mock the CAPTCHA detector and solver
        mock_detect_captcha.return_value = {
            "detected": True,
            "confidence": 0.9,
            "type": "recaptcha",
            "pattern_matches": ["recaptcha"],
            "html_indicators": ["recaptcha"],
        }

        mock_solve_captcha.return_value = {
            "success": True,
            "type": "recaptcha",
            "solution": "03AGdBq24PBgUOhR7F_KRZ...",
            "solution_time": 15.5,
        }

        # Handle a CAPTCHA
        result = self.agent.handle_captcha(
            html="<html><body>recaptcha</body></html>", url="https://example.com"
        )

        # Check that CAPTCHA was handled
        self.assertTrue(result["success"])
        self.assertTrue(result["handled"])
        mock_detect_captcha.assert_called_once()
        mock_solve_captcha.assert_called_once()

    def test_robots_txt_compliance(self):
        """Test robots.txt compliance."""
        # Test with respect_robots=True
        with patch(
            "src.deep_research_core.utils.robots_parser.robots_parser.can_fetch",
            return_value=False,
        ):
            result = self.agent.extract_content(
                url="https://example.com", respect_robots=True
            )

            # Check that the URL was not fetched
            self.assertFalse(result["success"])
            self.assertEqual(result["error"], "URL is disallowed by robots.txt")

        # Test with respect_robots=False
        with patch(
            "src.deep_research_core.utils.robots_parser.robots_parser.can_fetch",
            return_value=False,
        ):
            with patch.object(
                self.agent,
                "_extract_content_sync",
                return_value={"success": True, "text": "test"},
            ):
                result = self.agent.extract_content(
                    url="https://example.com", respect_robots=False
                )

                # Check that the URL was fetched
                self.assertTrue(result["success"])


if __name__ == "__main__":
    unittest.main()
