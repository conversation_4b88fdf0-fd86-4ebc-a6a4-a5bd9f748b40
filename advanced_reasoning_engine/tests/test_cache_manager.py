"""
Unit tests for CacheManager.
"""

import os
import sys
import unittest
import time
import tempfile
import shutil
from unittest.mock import patch, MagicMock

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# Import CacheManager
try:
    from deepresearch.src.deep_research_core.utils.cache_manager import (
        MemoryCache,
        DiskCache,
        CacheManager
    )
    cache_manager_available = True
except ImportError:
    cache_manager_available = False


@unittest.skipIf(not cache_manager_available, "CacheManager not available")
class TestMemoryCache(unittest.TestCase):
    """Test cases for MemoryCache."""

    def setUp(self):
        """Set up test fixtures."""
        self.memory_cache = MemoryCache(max_size=3)

    def test_init(self):
        """Test initialization."""
        self.assertEqual(self.memory_cache.max_size, 3)
        self.assertEqual(len(self.memory_cache.cache), 0)

    def test_set_get(self):
        """Test set and get methods."""
        # Set a value
        self.memory_cache.set("key1", "value1")
        
        # Get the value
        value = self.memory_cache.get("key1")
        self.assertEqual(value, "value1")
        
        # Get a non-existent value
        value = self.memory_cache.get("non-existent")
        self.assertIsNone(value)

    def test_lru_eviction(self):
        """Test LRU eviction."""
        # Fill the cache
        self.memory_cache.set("key1", "value1")
        self.memory_cache.set("key2", "value2")
        self.memory_cache.set("key3", "value3")
        
        # Add a new item, which should evict the oldest item (key1)
        self.memory_cache.set("key4", "value4")
        
        # Check that key1 was evicted
        self.assertIsNone(self.memory_cache.get("key1"))
        
        # Check that other keys are still there
        self.assertEqual(self.memory_cache.get("key2"), "value2")
        self.assertEqual(self.memory_cache.get("key3"), "value3")
        self.assertEqual(self.memory_cache.get("key4"), "value4")

    def test_lru_update(self):
        """Test LRU update on access."""
        # Fill the cache
        self.memory_cache.set("key1", "value1")
        self.memory_cache.set("key2", "value2")
        self.memory_cache.set("key3", "value3")
        
        # Access key1, making it the most recently used
        self.memory_cache.get("key1")
        
        # Add a new item, which should evict the oldest item (now key2)
        self.memory_cache.set("key4", "value4")
        
        # Check that key2 was evicted
        self.assertIsNone(self.memory_cache.get("key2"))
        
        # Check that other keys are still there
        self.assertEqual(self.memory_cache.get("key1"), "value1")
        self.assertEqual(self.memory_cache.get("key3"), "value3")
        self.assertEqual(self.memory_cache.get("key4"), "value4")

    def test_delete(self):
        """Test delete method."""
        # Set a value
        self.memory_cache.set("key1", "value1")
        
        # Delete the value
        result = self.memory_cache.delete("key1")
        self.assertTrue(result)
        
        # Check that the value was deleted
        self.assertIsNone(self.memory_cache.get("key1"))
        
        # Try to delete a non-existent value
        result = self.memory_cache.delete("non-existent")
        self.assertFalse(result)

    def test_clear(self):
        """Test clear method."""
        # Set some values
        self.memory_cache.set("key1", "value1")
        self.memory_cache.set("key2", "value2")
        
        # Clear the cache
        self.memory_cache.clear()
        
        # Check that the cache is empty
        self.assertEqual(len(self.memory_cache.cache), 0)
        self.assertIsNone(self.memory_cache.get("key1"))
        self.assertIsNone(self.memory_cache.get("key2"))

    def test_get_size(self):
        """Test get_size method."""
        # Empty cache
        self.assertEqual(self.memory_cache.get_size(), 0)
        
        # Add some items
        self.memory_cache.set("key1", "value1")
        self.memory_cache.set("key2", "value2")
        
        # Check size
        self.assertEqual(self.memory_cache.get_size(), 2)

    def test_get_keys(self):
        """Test get_keys method."""
        # Empty cache
        self.assertEqual(self.memory_cache.get_keys(), [])
        
        # Add some items
        self.memory_cache.set("key1", "value1")
        self.memory_cache.set("key2", "value2")
        
        # Check keys
        keys = self.memory_cache.get_keys()
        self.assertEqual(len(keys), 2)
        self.assertIn("key1", keys)
        self.assertIn("key2", keys)


@unittest.skipIf(not cache_manager_available, "CacheManager not available")
class TestDiskCache(unittest.TestCase):
    """Test cases for DiskCache."""

    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.disk_cache = DiskCache(
            cache_dir=self.temp_dir,
            max_size=3,
            ttl=1,  # 1 second for testing expiration
            enable_compression=True
        )

    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir)

    def test_init(self):
        """Test initialization."""
        self.assertEqual(self.disk_cache.cache_dir, self.temp_dir)
        self.assertEqual(self.disk_cache.max_size, 3)
        self.assertEqual(self.disk_cache.ttl, 1)
        self.assertTrue(self.disk_cache.enable_compression)

    def test_set_get(self):
        """Test set and get methods."""
        # Set a value
        self.disk_cache.set("key1", "value1")
        
        # Get the value
        value = self.disk_cache.get("key1")
        self.assertEqual(value, "value1")
        
        # Get a non-existent value
        value = self.disk_cache.get("non-existent")
        self.assertIsNone(value)

    def test_expiration(self):
        """Test expiration."""
        # Set a value
        self.disk_cache.set("key1", "value1")
        
        # Wait for expiration
        time.sleep(1.5)
        
        # Get the value (should be None)
        value = self.disk_cache.get("key1")
        self.assertIsNone(value)

    def test_custom_ttl(self):
        """Test custom TTL."""
        # Set a value with custom TTL
        self.disk_cache.set("key1", "value1", ttl=3)
        
        # Wait for 1.5 seconds (should not expire)
        time.sleep(1.5)
        
        # Get the value (should still be there)
        value = self.disk_cache.get("key1")
        self.assertEqual(value, "value1")

    def test_metadata(self):
        """Test metadata."""
        # Set a value with metadata
        metadata = {"type": "test", "version": 1}
        self.disk_cache.set("key1", "value1", metadata=metadata)
        
        # Check that metadata was saved
        self.assertIn("key1", self.disk_cache.metadata["entries"])
        self.assertEqual(self.disk_cache.metadata["entries"]["key1"]["metadata"], metadata)

    def test_delete(self):
        """Test delete method."""
        # Set a value
        self.disk_cache.set("key1", "value1")
        
        # Delete the value
        result = self.disk_cache.delete("key1")
        self.assertTrue(result)
        
        # Check that the value was deleted
        self.assertIsNone(self.disk_cache.get("key1"))
        
        # Try to delete a non-existent value
        result = self.disk_cache.delete("non-existent")
        self.assertFalse(result)

    def test_clear(self):
        """Test clear method."""
        # Set some values
        self.disk_cache.set("key1", "value1")
        self.disk_cache.set("key2", "value2")
        
        # Clear the cache
        self.disk_cache.clear()
        
        # Check that the cache is empty
        self.assertEqual(len(self.disk_cache.metadata["entries"]), 0)
        self.assertIsNone(self.disk_cache.get("key1"))
        self.assertIsNone(self.disk_cache.get("key2"))

    def test_get_stats(self):
        """Test get_stats method."""
        # Get initial stats
        stats = self.disk_cache.get_stats()
        self.assertEqual(stats["entries"], 0)
        self.assertEqual(stats["max_size"], 3)
        self.assertEqual(stats["ttl"], 1)
        
        # Add some items and get hits
        self.disk_cache.set("key1", "value1")
        self.disk_cache.get("key1")
        self.disk_cache.get("non-existent")
        
        # Get updated stats
        stats = self.disk_cache.get_stats()
        self.assertEqual(stats["entries"], 1)
        self.assertEqual(stats["stats"]["hits"], 1)
        self.assertEqual(stats["stats"]["misses"], 1)
        self.assertEqual(stats["stats"]["inserts"], 1)


@unittest.skipIf(not cache_manager_available, "CacheManager not available")
class TestCacheManager(unittest.TestCase):
    """Test cases for CacheManager."""

    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.cache_manager = CacheManager(
            memory_cache_size=3,
            disk_cache_dir=self.temp_dir,
            disk_cache_size=5,
            ttl=1,  # 1 second for testing expiration
            enable_compression=True,
            semantic_cache=False,
            enable_prefetching=False,
            verbose=False
        )

    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir)

    def test_init(self):
        """Test initialization."""
        self.assertEqual(self.cache_manager.ttl, 1)
        self.assertTrue(self.cache_manager.enable_compression)
        self.assertFalse(self.cache_manager.semantic_cache)
        self.assertFalse(self.cache_manager.enable_prefetching)
        self.assertFalse(self.cache_manager.verbose)

    def test_set_get(self):
        """Test set and get methods."""
        # Set a value
        self.cache_manager.set("key1", "value1")
        
        # Get the value
        value = self.cache_manager.get("key1")
        self.assertEqual(value, "value1")
        
        # Get a non-existent value
        value = self.cache_manager.get("non-existent")
        self.assertIsNone(value)

    def test_memory_cache_hit(self):
        """Test memory cache hit."""
        # Set a value
        self.cache_manager.set("key1", "value1")
        
        # Get the value (should hit memory cache)
        value = self.cache_manager.get("key1")
        self.assertEqual(value, "value1")
        
        # Check stats
        self.assertEqual(self.cache_manager.stats["memory_hits"], 1)
        self.assertEqual(self.cache_manager.stats["disk_hits"], 0)
        self.assertEqual(self.cache_manager.stats["misses"], 0)

    def test_disk_cache_hit(self):
        """Test disk cache hit."""
        # Set a value
        self.cache_manager.set("key1", "value1")
        
        # Clear memory cache
        self.cache_manager.memory_cache.clear()
        
        # Get the value (should hit disk cache)
        value = self.cache_manager.get("key1")
        self.assertEqual(value, "value1")
        
        # Check stats
        self.assertEqual(self.cache_manager.stats["memory_hits"], 0)
        self.assertEqual(self.cache_manager.stats["disk_hits"], 1)
        self.assertEqual(self.cache_manager.stats["misses"], 0)

    def test_delete(self):
        """Test delete method."""
        # Set a value
        self.cache_manager.set("key1", "value1")
        
        # Delete the value
        result = self.cache_manager.delete("key1")
        self.assertTrue(result)
        
        # Check that the value was deleted
        self.assertIsNone(self.cache_manager.get("key1"))
        
        # Try to delete a non-existent value
        result = self.cache_manager.delete("non-existent")
        self.assertFalse(result)

    def test_clear(self):
        """Test clear method."""
        # Set some values
        self.cache_manager.set("key1", "value1")
        self.cache_manager.set("key2", "value2")
        
        # Clear the cache
        self.cache_manager.clear()
        
        # Check that the cache is empty
        self.assertIsNone(self.cache_manager.get("key1"))
        self.assertIsNone(self.cache_manager.get("key2"))

    def test_get_stats(self):
        """Test get_stats method."""
        # Get initial stats
        stats = self.cache_manager.get_stats()
        self.assertEqual(stats["memory_cache"]["size"], 0)
        self.assertEqual(stats["memory_cache"]["hits"], 0)
        self.assertEqual(stats["disk_cache"]["hits"], 0)
        self.assertEqual(stats["overall"]["misses"], 0)
        
        # Add some items and get hits
        self.cache_manager.set("key1", "value1")
        self.cache_manager.get("key1")
        self.cache_manager.get("non-existent")
        
        # Get updated stats
        stats = self.cache_manager.get_stats()
        self.assertEqual(stats["memory_cache"]["size"], 1)
        self.assertEqual(stats["memory_cache"]["hits"], 1)
        self.assertEqual(stats["overall"]["misses"], 1)


if __name__ == "__main__":
    unittest.main()
