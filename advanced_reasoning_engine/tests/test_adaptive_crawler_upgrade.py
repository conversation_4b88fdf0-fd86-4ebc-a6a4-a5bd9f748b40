#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test các module mới của AdaptiveCrawler.

Module này kiểm tra các module mới củ<PERSON>ptive<PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON>,
<PERSON><PERSON><PERSON><PERSON>, Site<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, InfiniteSc<PERSON><PERSON><PERSON><PERSON>,
JavaScriptHandler, v<PERSON> FormHandler.
"""

import os
import sys
import unittest
import tempfile
import shutil
from unittest.mock import patch, MagicMock

# Thêm đường dẫn src vào sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from src.deep_research_core.crawlers.media_handler import MediaHandler
from src.deep_research_core.crawlers.file_handler import <PERSON>Handler
from src.deep_research_core.crawlers.site_structure_handler import SiteStructureHandler
from src.deep_research_core.crawlers.pagination_handler import Pagination<PERSON>andler
from src.deep_research_core.crawlers.infinite_scroll_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from src.deep_research_core.crawlers.javascript_handler import JavaScript<PERSON>andler
from src.deep_research_core.crawlers.form_handler import FormHandler


class TestMediaHandler(unittest.TestCase):
    """
    Kiểm tra MediaHandler.
    """

    def setUp(self):
        """
        Thiết lập trước mỗi test.
        """
        self.temp_dir = tempfile.mkdtemp()
        self.media_handler = MediaHandler(download_path=self.temp_dir)

    def tearDown(self):
        """
        Dọn dẹp sau mỗi test.
        """
        shutil.rmtree(self.temp_dir)

    def test_extract_images(self):
        """
        Kiểm tra phương thức extract_images.
        """
        html = """
        <html>
        <head>
            <base href="https://example.com/">
        </head>
        <body>
            <img src="image1.jpg" alt="Image 1" title="Title 1" width="100" height="100">
            <img src="https://example.com/image2.jpg" alt="Image 2" title="Title 2">
            <img src="data:image/png;base64,..." alt="Data URL">
            <div style="background-image: url('background.jpg')"></div>
            <style>
                .header { background-image: url('header.jpg'); }
            </style>
            <svg width="100" height="100">
                <circle cx="50" cy="50" r="40" stroke="black" stroke-width="3" fill="red" />
            </svg>
        </body>
        </html>
        """
        url = "https://example.com/"
        images = self.media_handler.extract_images(html, url)
        
        # Kiểm tra số lượng hình ảnh
        self.assertGreaterEqual(len(images), 2)
        
        # Kiểm tra thông tin hình ảnh
        self.assertEqual(images[0]["url"], "https://example.com/image1.jpg")
        self.assertEqual(images[0]["alt"], "Image 1")
        self.assertEqual(images[0]["title"], "Title 1")
        self.assertEqual(images[0]["width"], "100")
        self.assertEqual(images[0]["height"], "100")
        self.assertEqual(images[0]["source_url"], "https://example.com/")
        
        # Kiểm tra thống kê
        self.assertGreaterEqual(self.media_handler.stats["total_images_extracted"], 2)

    @patch("requests.Session.get")
    def test_download_media(self, mock_get):
        """
        Kiểm tra phương thức download_media.
        """
        # Tạo mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.headers = {"Content-Type": "image/jpeg", "Content-Length": "1024"}
        mock_response.iter_content.return_value = [b"test data"]
        mock_get.return_value.__enter__.return_value = mock_response
        
        # Tải xuống media
        result = self.media_handler.download_media("https://example.com/image.jpg")
        
        # Kiểm tra kết quả
        self.assertTrue(result["success"])
        self.assertEqual(result["url"], "https://example.com/image.jpg")
        self.assertTrue(os.path.exists(result["file_path"]))
        self.assertGreater(result["file_size"], 0)
        self.assertEqual(result["content_type"], "image/jpeg")
        
        # Kiểm tra thống kê
        self.assertEqual(self.media_handler.stats["total_media_downloaded"], 1)
        self.assertGreater(self.media_handler.stats["total_download_size"], 0)


class TestFileHandler(unittest.TestCase):
    """
    Kiểm tra FileHandler.
    """

    def setUp(self):
        """
        Thiết lập trước mỗi test.
        """
        self.temp_dir = tempfile.mkdtemp()
        self.file_handler = FileHandler(download_path=self.temp_dir)

    def tearDown(self):
        """
        Dọn dẹp sau mỗi test.
        """
        shutil.rmtree(self.temp_dir)

    def test_extract_files(self):
        """
        Kiểm tra phương thức extract_files.
        """
        html = """
        <html>
        <head>
            <base href="https://example.com/">
        </head>
        <body>
            <a href="document.pdf">PDF Document</a>
            <a href="https://example.com/document.docx" title="Word Document">Word Document</a>
            <a href="spreadsheet.xlsx">Excel Spreadsheet</a>
            <a href="presentation.pptx">PowerPoint Presentation</a>
            <a href="archive.zip">ZIP Archive</a>
            <a href="text.txt">Text File</a>
            <a href="image.jpg">Image</a>
            <a href="javascript:void(0)">JavaScript Link</a>
            <a href="#">Anchor Link</a>
        </body>
        </html>
        """
        url = "https://example.com/"
        files = self.file_handler.extract_files(html, url)
        
        # Kiểm tra số lượng file
        self.assertEqual(len(files), 6)
        
        # Kiểm tra thông tin file
        self.assertEqual(files[0]["url"], "https://example.com/document.pdf")
        self.assertEqual(files[0]["filename"], "document.pdf")
        self.assertEqual(files[0]["text"], "PDF Document")
        self.assertEqual(files[0]["extension"], ".pdf")
        self.assertEqual(files[0]["type"], "pdf")
        self.assertEqual(files[0]["source_url"], "https://example.com/")
        
        # Kiểm tra thống kê
        self.assertEqual(self.file_handler.stats["total_files_extracted"], 6)
        self.assertEqual(self.file_handler.stats["file_types"]["pdf"], 1)

    @patch("requests.Session.get")
    def test_download_file(self, mock_get):
        """
        Kiểm tra phương thức download_file.
        """
        # Tạo mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.headers = {"Content-Type": "application/pdf", "Content-Length": "1024"}
        mock_response.iter_content.return_value = [b"test data"]
        mock_get.return_value.__enter__.return_value = mock_response
        
        # Tải xuống file
        result = self.file_handler.download_file("https://example.com/document.pdf", file_type="pdf")
        
        # Kiểm tra kết quả
        self.assertTrue(result["success"])
        self.assertEqual(result["url"], "https://example.com/document.pdf")
        self.assertTrue(os.path.exists(result["file_path"]))
        self.assertGreater(result["file_size"], 0)
        self.assertEqual(result["content_type"], "application/pdf")
        
        # Kiểm tra thống kê
        self.assertEqual(self.file_handler.stats["total_files_downloaded"], 1)
        self.assertGreater(self.file_handler.stats["total_download_size"], 0)


class TestSiteStructureHandler(unittest.TestCase):
    """
    Kiểm tra SiteStructureHandler.
    """

    def setUp(self):
        """
        Thiết lập trước mỗi test.
        """
        self.site_structure_handler = SiteStructureHandler()

    @patch("requests.Session.get")
    def test_parse_robots_txt(self, mock_get):
        """
        Kiểm tra phương thức parse_robots_txt.
        """
        # Tạo mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = """
        User-agent: *
        Disallow: /admin/
        Disallow: /private/
        Allow: /private/public/
        Sitemap: https://example.com/sitemap.xml
        Crawl-delay: 5
        Host: example.com
        """
        mock_get.return_value = mock_response
        
        # Phân tích robots.txt
        result = self.site_structure_handler.parse_robots_txt("https://example.com/")
        
        # Kiểm tra kết quả
        self.assertTrue(result["success"])
        self.assertEqual(result["url"], "https://example.com/")
        self.assertEqual(len(result["disallowed_paths"]), 2)
        self.assertEqual(result["disallowed_paths"][0], "/admin/")
        self.assertEqual(result["disallowed_paths"][1], "/private/")
        self.assertEqual(len(result["allowed_paths"]), 1)
        self.assertEqual(result["allowed_paths"][0], "/private/public/")
        self.assertEqual(len(result["sitemaps"]), 1)
        self.assertEqual(result["sitemaps"][0], "https://example.com/sitemap.xml")
        self.assertEqual(result["crawl_delay"], 5)
        self.assertEqual(result["host"], "example.com")
        
        # Kiểm tra thống kê
        self.assertEqual(self.site_structure_handler.stats["robots_parsed"], 1)

    @patch("requests.Session.get")
    def test_parse_sitemap(self, mock_get):
        """
        Kiểm tra phương thức parse_sitemap.
        """
        # Tạo mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = """
        <?xml version="1.0" encoding="UTF-8"?>
        <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
            <url>
                <loc>https://example.com/</loc>
                <lastmod>2023-01-01</lastmod>
                <changefreq>daily</changefreq>
                <priority>1.0</priority>
            </url>
            <url>
                <loc>https://example.com/about</loc>
                <lastmod>2023-01-02</lastmod>
                <changefreq>weekly</changefreq>
                <priority>0.8</priority>
            </url>
        </urlset>
        """
        mock_get.return_value = mock_response
        
        # Phân tích sitemap
        result = self.site_structure_handler.parse_sitemap("https://example.com/sitemap.xml")
        
        # Kiểm tra kết quả
        self.assertTrue(result["success"])
        self.assertEqual(result["url"], "https://example.com/sitemap.xml")
        self.assertEqual(len(result["urls"]), 2)
        self.assertEqual(result["urls"][0]["url"], "https://example.com/")
        self.assertEqual(result["urls"][0]["lastmod"], "2023-01-01")
        self.assertEqual(result["urls"][0]["changefreq"], "daily")
        self.assertEqual(result["urls"][0]["priority"], 1.0)
        self.assertEqual(result["urls"][1]["url"], "https://example.com/about")
        self.assertEqual(result["urls"][1]["lastmod"], "2023-01-02")
        self.assertEqual(result["urls"][1]["changefreq"], "weekly")
        self.assertEqual(result["urls"][1]["priority"], 0.8)
        
        # Kiểm tra thống kê
        self.assertEqual(self.site_structure_handler.stats["sitemaps_parsed"], 1)
        self.assertEqual(self.site_structure_handler.stats["urls_found"], 2)


class TestPaginationHandler(unittest.TestCase):
    """
    Kiểm tra PaginationHandler.
    """

    def setUp(self):
        """
        Thiết lập trước mỗi test.
        """
        self.pagination_handler = PaginationHandler()

    def test_detect_pagination(self):
        """
        Kiểm tra phương thức detect_pagination.
        """
        html = """
        <html>
        <body>
            <div class="pagination">
                <a href="?page=1" class="active">1</a>
                <a href="?page=2">2</a>
                <a href="?page=3">3</a>
                <a href="?page=2" class="next">Next</a>
            </div>
        </body>
        </html>
        """
        url = "https://example.com/"
        pagination_info = self.pagination_handler.detect_pagination(html, url)
        
        # Kiểm tra kết quả
        self.assertTrue(pagination_info["has_pagination"])
        self.assertEqual(pagination_info["pagination_type"], "standard")
        self.assertEqual(pagination_info["next_page"], "https://example.com/?page=2")
        self.assertEqual(pagination_info["current_page"], 1)
        self.assertGreaterEqual(pagination_info["total_pages"], 1)
        
        # Kiểm tra thống kê
        self.assertEqual(self.pagination_handler.stats["pagination_detected"], 1)
        self.assertEqual(self.pagination_handler.stats["next_pages_found"], 1)


class TestFormHandler(unittest.TestCase):
    """
    Kiểm tra FormHandler.
    """

    def setUp(self):
        """
        Thiết lập trước mỗi test.
        """
        self.form_handler = FormHandler()

    def test_detect_forms(self):
        """
        Kiểm tra phương thức detect_forms.
        """
        html = """
        <html>
        <body>
            <form id="login-form" action="/login" method="post">
                <input type="text" name="username" placeholder="Username" required>
                <input type="password" name="password" placeholder="Password" required>
                <input type="submit" value="Login">
            </form>
            <form id="search-form" action="/search" method="get">
                <input type="text" name="q" placeholder="Search">
                <input type="submit" value="Search">
            </form>
        </body>
        </html>
        """
        url = "https://example.com/"
        forms = self.form_handler.detect_forms(html, url)
        
        # Kiểm tra kết quả
        self.assertEqual(len(forms), 2)
        self.assertEqual(forms[0]["id"], "login-form")
        self.assertEqual(forms[0]["action"], "https://example.com/login")
        self.assertEqual(forms[0]["method"], "post")
        self.assertEqual(len(forms[0]["inputs"]), 3)
        self.assertEqual(forms[0]["inputs"][0]["type"], "text")
        self.assertEqual(forms[0]["inputs"][0]["name"], "username")
        self.assertEqual(forms[0]["inputs"][0]["placeholder"], "Username")
        self.assertTrue(forms[0]["inputs"][0]["required"])
        self.assertEqual(forms[1]["id"], "search-form")
        self.assertEqual(forms[1]["action"], "https://example.com/search")
        self.assertEqual(forms[1]["method"], "get")
        
        # Kiểm tra thống kê
        self.assertEqual(self.form_handler.stats["forms_detected"], 2)

    def test_fill_form(self):
        """
        Kiểm tra phương thức fill_form.
        """
        form = {
            "id": "login-form",
            "action": "https://example.com/login",
            "method": "post",
            "inputs": [
                {
                    "type": "text",
                    "name": "username",
                    "id": "username",
                    "value": "",
                    "placeholder": "Username",
                    "required": True,
                    "tag": "input",
                },
                {
                    "type": "password",
                    "name": "password",
                    "id": "password",
                    "value": "",
                    "placeholder": "Password",
                    "required": True,
                    "tag": "input",
                },
                {
                    "type": "submit",
                    "name": "submit",
                    "id": "submit",
                    "value": "Login",
                    "tag": "input",
                },
            ],
            "source_url": "https://example.com/",
        }
        
        # Điền form
        filled_form = self.form_handler.fill_form(form)
        
        # Kiểm tra kết quả
        self.assertEqual(filled_form["id"], "login-form")
        self.assertEqual(filled_form["action"], "https://example.com/login")
        self.assertEqual(filled_form["method"], "post")
        self.assertEqual(len(filled_form["inputs"]), 3)
        self.assertEqual(filled_form["inputs"][0]["name"], "username")
        self.assertNotEqual(filled_form["inputs"][0]["value"], "")
        self.assertEqual(filled_form["inputs"][1]["name"], "password")
        self.assertNotEqual(filled_form["inputs"][1]["value"], "")
        self.assertEqual(filled_form["inputs"][2]["name"], "submit")
        self.assertEqual(filled_form["inputs"][2]["value"], "Login")
        
        # Kiểm tra thống kê
        self.assertEqual(self.form_handler.stats["forms_filled"], 1)
        self.assertEqual(self.form_handler.stats["inputs_filled"], 2)


if __name__ == "__main__":
    unittest.main()
