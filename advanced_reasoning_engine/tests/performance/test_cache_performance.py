#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
<PERSON><PERSON>m thử hiệu suất cho hệ thống cache.

Module này chứa các kiểm thử hiệu suất để đánh giá hiệu suất của hệ thống cache,
bao gồm:
- <PERSON><PERSON><PERSON> suất AdaptiveSearchCache
- <PERSON><PERSON><PERSON> suất CachedWebSearchAgent
- So sánh với các phương pháp cache khác
"""

import os
import sys
import time
import json
import random
import string
import argparse
import tempfile
import statistics
from typing import Dict, Any, List, Tuple, Callable

# Thêm thư mục gốc vào sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.deep_research_core.optimization.caching.adaptive_search_cache import AdaptiveSearchCache
from src.deep_research_core.agents.search_cache_integration import (
    Search<PERSON>ache<PERSON>anager,
    get_cache_manager,
    cached_search
)
from src.deep_research_core.agents.web_search_agent_cache import (
    CachedWebSearchAgent,
    patch_web_search_agent
)

# Thiết lập logging
import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MockWebSearchAgent:
    """Mock WebSearchAgent cho kiểm thử hiệu suất."""
    
    def __init__(self, delay: float = 0.1):
        """
        Khởi tạo mock agent.
        
        Args:
            delay: Độ trễ giả lập (giây)
        """
        self.delay = delay
        self.search_called = 0
    
    def search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Mock phương thức search.
        
        Args:
            query: Truy vấn tìm kiếm
            **kwargs: Tham số khác
            
        Returns:
            Dict[str, Any]: Kết quả tìm kiếm
        """
        self.search_called += 1
        
        # Giả lập độ trễ
        time.sleep(self.delay)
        
        # Lấy tham số
        num_results = kwargs.get("num_results", 10)
        language = kwargs.get("language", "en")
        
        return {
            "query": query,
            "results": [
                {
                    "title": f"Result {i} for {query}",
                    "url": f"https://example.com/result/{i}",
                    "snippet": f"This is a snippet for result {i} of query '{query}'."
                }
                for i in range(num_results)
            ],
            "count": num_results,
            "language": language,
            "timestamp": time.time()
        }


def generate_random_query(length: int = 5) -> str:
    """
    Tạo truy vấn ngẫu nhiên.
    
    Args:
        length: Số từ trong truy vấn
        
    Returns:
        str: Truy vấn ngẫu nhiên
    """
    words = []
    for _ in range(length):
        word_length = random.randint(3, 10)
        word = ''.join(random.choice(string.ascii_lowercase) for _ in range(word_length))
        words.append(word)
    
    return ' '.join(words)


def generate_similar_query(query: str, similarity: float = 0.7) -> str:
    """
    Tạo truy vấn tương tự.
    
    Args:
        query: Truy vấn gốc
        similarity: Độ tương tự (0.0 - 1.0)
        
    Returns:
        str: Truy vấn tương tự
    """
    words = query.split()
    num_words = len(words)
    
    # Số từ cần giữ nguyên
    num_keep = int(num_words * similarity)
    
    # Giữ nguyên một số từ
    keep_indices = random.sample(range(num_words), num_keep)
    new_words = []
    
    for i in range(num_words):
        if i in keep_indices:
            new_words.append(words[i])
        else:
            # Thay thế từ
            word_length = random.randint(3, 10)
            new_word = ''.join(random.choice(string.ascii_lowercase) for _ in range(word_length))
            new_words.append(new_word)
    
    return ' '.join(new_words)


def benchmark_adaptive_search_cache(
    num_queries: int = 1000,
    num_unique_queries: int = 100,
    similarity_ratio: float = 0.2,
    semantic_search: bool = True
) -> Dict[str, Any]:
    """
    Benchmark AdaptiveSearchCache.
    
    Args:
        num_queries: Tổng số truy vấn
        num_unique_queries: Số truy vấn duy nhất
        similarity_ratio: Tỷ lệ truy vấn tương tự
        semantic_search: Có sử dụng tìm kiếm ngữ nghĩa không
        
    Returns:
        Dict[str, Any]: Kết quả benchmark
    """
    logger.info(f"Benchmark AdaptiveSearchCache với {num_queries} truy vấn ({num_unique_queries} truy vấn duy nhất)")
    
    # Tạo thư mục tạm cho cache
    temp_dir = tempfile.mkdtemp()
    
    try:
        # Tạo cache
        cache = AdaptiveSearchCache(
            name="benchmark_cache",
            cache_dir=temp_dir,
            max_memory_items=num_unique_queries * 2,
            default_ttl=3600,
            enable_semantic_search=semantic_search
        )
        
        # Tạo danh sách truy vấn duy nhất
        unique_queries = [generate_random_query() for _ in range(num_unique_queries)]
        
        # Tạo danh sách tất cả truy vấn
        all_queries = []
        for _ in range(num_queries):
            if random.random() < similarity_ratio and all_queries:
                # Tạo truy vấn tương tự
                original_query = random.choice(all_queries)
                query = generate_similar_query(original_query)
            else:
                # Sử dụng truy vấn duy nhất
                query = random.choice(unique_queries)
            
            all_queries.append(query)
        
        # Thực hiện benchmark
        start_time = time.time()
        
        # Thống kê
        stats = {
            "exact_hits": 0,
            "semantic_hits": 0,
            "misses": 0,
            "set_time": 0,
            "get_time": 0,
            "exact_get_time": [],
            "semantic_get_time": [],
            "miss_get_time": []
        }
        
        # Thực hiện các truy vấn
        for query in all_queries:
            # Thử lấy từ cache
            get_start_time = time.time()
            result = cache.get(
                key=query,
                use_semantic=semantic_search,
                language="en"
            )
            get_time = time.time() - get_start_time
            stats["get_time"] += get_time
            
            if result is not None:
                if query in unique_queries:
                    # Exact hit
                    stats["exact_hits"] += 1
                    stats["exact_get_time"].append(get_time)
                else:
                    # Semantic hit
                    stats["semantic_hits"] += 1
                    stats["semantic_get_time"].append(get_time)
            else:
                # Miss
                stats["misses"] += 1
                stats["miss_get_time"].append(get_time)
                
                # Đặt vào cache
                set_start_time = time.time()
                cache.set(
                    key=query,
                    value={
                        "query": query,
                        "results": [{"title": f"Result {i} for {query}"} for i in range(10)],
                        "count": 10,
                        "language": "en",
                        "timestamp": time.time()
                    },
                    language="en"
                )
                stats["set_time"] += time.time() - set_start_time
        
        # Tính toán thống kê
        total_time = time.time() - start_time
        total_hits = stats["exact_hits"] + stats["semantic_hits"]
        hit_ratio = total_hits / num_queries if num_queries > 0 else 0
        
        # Tính toán thời gian trung bình
        avg_get_time = stats["get_time"] / num_queries if num_queries > 0 else 0
        avg_set_time = stats["set_time"] / stats["misses"] if stats["misses"] > 0 else 0
        
        avg_exact_get_time = statistics.mean(stats["exact_get_time"]) if stats["exact_get_time"] else 0
        avg_semantic_get_time = statistics.mean(stats["semantic_get_time"]) if stats["semantic_get_time"] else 0
        avg_miss_get_time = statistics.mean(stats["miss_get_time"]) if stats["miss_get_time"] else 0
        
        # Kết quả
        result = {
            "total_queries": num_queries,
            "unique_queries": num_unique_queries,
            "similarity_ratio": similarity_ratio,
            "semantic_search": semantic_search,
            "total_time": total_time,
            "exact_hits": stats["exact_hits"],
            "semantic_hits": stats["semantic_hits"],
            "total_hits": total_hits,
            "misses": stats["misses"],
            "hit_ratio": hit_ratio,
            "avg_get_time": avg_get_time,
            "avg_set_time": avg_set_time,
            "avg_exact_get_time": avg_exact_get_time,
            "avg_semantic_get_time": avg_semantic_get_time,
            "avg_miss_get_time": avg_miss_get_time,
            "throughput": num_queries / total_time if total_time > 0 else 0
        }
        
        logger.info(f"Benchmark hoàn thành trong {total_time:.2f} giây")
        logger.info(f"Hit ratio: {hit_ratio:.2%} ({stats['exact_hits']} exact hits, {stats['semantic_hits']} semantic hits, {stats['misses']} misses)")
        logger.info(f"Thời gian trung bình: get={avg_get_time:.6f}s, set={avg_set_time:.6f}s")
        logger.info(f"Throughput: {result['throughput']:.2f} truy vấn/giây")
        
        return result
    
    finally:
        # Xóa thư mục tạm
        import shutil
        shutil.rmtree(temp_dir)


def benchmark_cached_web_search_agent(
    num_queries: int = 100,
    num_unique_queries: int = 20,
    similarity_ratio: float = 0.2,
    semantic_search: bool = True,
    delay: float = 0.1
) -> Dict[str, Any]:
    """
    Benchmark CachedWebSearchAgent.
    
    Args:
        num_queries: Tổng số truy vấn
        num_unique_queries: Số truy vấn duy nhất
        similarity_ratio: Tỷ lệ truy vấn tương tự
        semantic_search: Có sử dụng tìm kiếm ngữ nghĩa không
        delay: Độ trễ giả lập (giây)
        
    Returns:
        Dict[str, Any]: Kết quả benchmark
    """
    logger.info(f"Benchmark CachedWebSearchAgent với {num_queries} truy vấn ({num_unique_queries} truy vấn duy nhất)")
    
    # Tạo thư mục tạm cho cache
    temp_dir = tempfile.mkdtemp()
    
    try:
        # Tạo cache manager
        cache_manager = SearchCacheManager(
            cache_dir=temp_dir,
            default_ttl=3600,
            enable_analytics=True,
            enable_semantic_search=semantic_search
        )
        
        # Tạo mock agent
        mock_agent = MockWebSearchAgent(delay=delay)
        
        # Patch agent
        cached_agent = patch_web_search_agent(
            mock_agent,
            cache_manager=cache_manager,
            enable_cache=True,
            default_ttl=3600,
            enable_semantic_search=semantic_search
        )
        
        # Tạo danh sách truy vấn duy nhất
        unique_queries = [generate_random_query() for _ in range(num_unique_queries)]
        
        # Tạo danh sách tất cả truy vấn
        all_queries = []
        for _ in range(num_queries):
            if random.random() < similarity_ratio and all_queries:
                # Tạo truy vấn tương tự
                original_query = random.choice(all_queries)
                query = generate_similar_query(original_query)
            else:
                # Sử dụng truy vấn duy nhất
                query = random.choice(unique_queries)
            
            all_queries.append(query)
        
        # Thực hiện benchmark
        start_time = time.time()
        
        # Thực hiện các truy vấn
        for query in all_queries:
            mock_agent.search(query, language="en")
        
        # Tính toán thống kê
        total_time = time.time() - start_time
        
        # Lấy thống kê cache
        cache_stats = cached_agent.get_stats()
        
        # Kết quả
        result = {
            "total_queries": num_queries,
            "unique_queries": num_unique_queries,
            "similarity_ratio": similarity_ratio,
            "semantic_search": semantic_search,
            "delay": delay,
            "total_time": total_time,
            "search_called": mock_agent.search_called,
            "cache_stats": cache_stats,
            "throughput": num_queries / total_time if total_time > 0 else 0,
            "estimated_time_without_cache": num_queries * delay,
            "time_saved": (num_queries * delay) - total_time
        }
        
        logger.info(f"Benchmark hoàn thành trong {total_time:.2f} giây")
        logger.info(f"Phương thức search được gọi {mock_agent.search_called} lần")
        logger.info(f"Throughput: {result['throughput']:.2f} truy vấn/giây")
        logger.info(f"Thời gian ước tính không có cache: {result['estimated_time_without_cache']:.2f} giây")
        logger.info(f"Thời gian tiết kiệm: {result['time_saved']:.2f} giây")
        
        return result
    
    finally:
        # Xóa thư mục tạm
        import shutil
        shutil.rmtree(temp_dir)


def main():
    """Hàm chính."""
    parser = argparse.ArgumentParser(description="Benchmark hệ thống cache")
    parser.add_argument("--benchmark", choices=["adaptive", "agent", "all"], default="all", help="Loại benchmark")
    parser.add_argument("--queries", type=int, default=1000, help="Tổng số truy vấn")
    parser.add_argument("--unique", type=int, default=100, help="Số truy vấn duy nhất")
    parser.add_argument("--similarity", type=float, default=0.2, help="Tỷ lệ truy vấn tương tự")
    parser.add_argument("--semantic", action="store_true", help="Bật tìm kiếm ngữ nghĩa")
    parser.add_argument("--delay", type=float, default=0.1, help="Độ trễ giả lập (giây)")
    parser.add_argument("--output", type=str, help="File đầu ra (JSON)")
    args = parser.parse_args()
    
    results = {}
    
    if args.benchmark == "adaptive" or args.benchmark == "all":
        results["adaptive"] = benchmark_adaptive_search_cache(
            num_queries=args.queries,
            num_unique_queries=args.unique,
            similarity_ratio=args.similarity,
            semantic_search=args.semantic
        )
    
    if args.benchmark == "agent" or args.benchmark == "all":
        results["agent"] = benchmark_cached_web_search_agent(
            num_queries=args.queries // 10,  # Giảm số truy vấn vì có độ trễ
            num_unique_queries=args.unique // 10,
            similarity_ratio=args.similarity,
            semantic_search=args.semantic,
            delay=args.delay
        )
    
    # Lưu kết quả
    if args.output:
        with open(args.output, "w", encoding="utf-8") as f:
            json.dump(results, f, indent=2)
        logger.info(f"Đã lưu kết quả vào {args.output}")


if __name__ == "__main__":
    main()
