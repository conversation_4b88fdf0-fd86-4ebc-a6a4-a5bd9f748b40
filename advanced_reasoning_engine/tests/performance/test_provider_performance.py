"""
Performance tests for providers.

This module contains performance tests for the provider implementations.
"""

import unittest
import time
from unittest.mock import patch, MagicMock
import os

from src.deep_research_core.providers.cohere import CohereProvider
from src.deep_research_core.providers.mistral import MistralProvider


class TestProviderPerformance(unittest.TestCase):
    """
    Performance tests for providers.
    """

    def setUp(self):
        """
        Set up test environment.
        """
        # Mock environment variables
        self.env_patcher = patch.dict(os.environ, {
            "COHERE_API_KEY": "test-cohere-key",
            "MISTRAL_API_KEY": "test-mistral-key"
        })
        self.env_patcher.start()

    def tearDown(self):
        """
        Clean up after tests.
        """
        self.env_patcher.stop()

    @patch("cohere.Client")
    def test_cohere_provider_performance(self, mock_client_class):
        """
        Test Cohere provider performance.
        """
        # Mock Cohere client
        mock_client = MagicMock()
        mock_client_class.return_value = mock_client

        # Mock generate method with delay
        def delayed_generate(*args, **kwargs):
            time.sleep(0.01)  # Simulate API delay
            mock_response = MagicMock()
            mock_response.generations = [MagicMock(text="Generated text")]
            mock_response.meta.billed_units.input_tokens = 10
            mock_response.meta.billed_units.output_tokens = 20
            return mock_response

        mock_client.generate.side_effect = delayed_generate

        # Create provider
        provider = CohereProvider(model="command")

        # Measure performance
        start_time = time.time()
        for _ in range(10):
            provider.generate(prompt="Test prompt")
        end_time = time.time()

        # Calculate average time
        avg_time = (end_time - start_time) / 10
        print(f"Cohere provider average response time: {avg_time:.4f} seconds")

        # Verify performance is acceptable
        self.assertLess(avg_time, 0.1)  # Should be very fast with mocked responses

    @patch("mistralai.client.MistralClient")
    def test_mistral_provider_performance(self, mock_client_class):
        """
        Test Mistral provider performance.
        """
        # Mock Mistral client
        mock_client = MagicMock()
        mock_client_class.return_value = mock_client

        # Mock chat method with delay
        def delayed_chat(*args, **kwargs):
            time.sleep(0.01)  # Simulate API delay
            mock_choice = MagicMock()
            mock_choice.message.content = "Generated text"
            mock_choice.finish_reason = "stop"

            mock_usage = MagicMock()
            mock_usage.prompt_tokens = 10
            mock_usage.completion_tokens = 20
            mock_usage.total_tokens = 30

            mock_response = MagicMock()
            mock_response.choices = [mock_choice]
            mock_response.usage = mock_usage
            return mock_response

        mock_client.chat.side_effect = delayed_chat

        # Create provider
        provider = MistralProvider(model="mistral-medium")

        # Measure performance
        start_time = time.time()
        for _ in range(10):
            provider.generate(prompt="Test prompt")
        end_time = time.time()

        # Calculate average time
        avg_time = (end_time - start_time) / 10
        print(f"Mistral provider average response time: {avg_time:.4f} seconds")

        # Verify performance is acceptable
        self.assertLess(avg_time, 0.1)  # Should be very fast with mocked responses

    @patch("cohere.Client")
    def test_cohere_provider_error_recovery(self, mock_client_class):
        """
        Test Cohere provider error recovery performance.
        """
        # Mock Cohere client
        mock_client = MagicMock()
        mock_client_class.return_value = mock_client

        # Mock generate method to fail first time, succeed second time
        call_count = 0

        def fail_then_succeed(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise Exception("API error")
            else:
                mock_response = MagicMock()
                mock_response.generations = [MagicMock(text="Generated text")]
                mock_response.meta.billed_units.input_tokens = 10
                mock_response.meta.billed_units.output_tokens = 20
                return mock_response

        mock_client.generate.side_effect = fail_then_succeed

        # Create provider with retry
        provider = CohereProvider(model="command")

        # Measure performance with error recovery
        with patch.object(provider, 'max_retries', 3):
            with patch.object(provider, 'retry_delay', 0.01):
                start_time = time.time()
                result = provider.generate(prompt="Test prompt")
                end_time = time.time()

        # Calculate time
        recovery_time = end_time - start_time
        print(f"Cohere provider error recovery time: {recovery_time:.4f} seconds")

        # Verify result and performance
        self.assertIsNotNone(result)
        self.assertEqual(result["choices"][0]["text"], "Generated text")
        self.assertLess(recovery_time, 0.1)  # Should be fast with mocked responses

    @patch("mistralai.client.MistralClient")
    def test_mistral_provider_error_recovery(self, mock_client_class):
        """
        Test Mistral provider error recovery performance.
        """
        # Mock Mistral client
        mock_client = MagicMock()
        mock_client_class.return_value = mock_client

        # Mock chat method to fail first time, succeed second time
        call_count = 0

        def fail_then_succeed(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise Exception("API error")
            else:
                mock_choice = MagicMock()
                mock_choice.message.content = "Generated text"
                mock_choice.finish_reason = "stop"

                mock_usage = MagicMock()
                mock_usage.prompt_tokens = 10
                mock_usage.completion_tokens = 20
                mock_usage.total_tokens = 30

                mock_response = MagicMock()
                mock_response.choices = [mock_choice]
                mock_response.usage = mock_usage
                return mock_response

        mock_client.chat.side_effect = fail_then_succeed

        # Create provider with retry
        provider = MistralProvider(model="mistral-medium")

        # Measure performance with error recovery
        with patch.object(provider, 'max_retries', 3):
            with patch.object(provider, 'retry_delay', 0.01):
                start_time = time.time()
                result = provider.generate(prompt="Test prompt")
                end_time = time.time()

        # Calculate time
        recovery_time = end_time - start_time
        print(f"Mistral provider error recovery time: {recovery_time:.4f} seconds")

        # Verify result and performance
        self.assertIsNotNone(result)
        self.assertEqual(result["choices"][0]["text"], "Generated text")
        self.assertLess(recovery_time, 0.1)  # Should be fast with mocked responses


if __name__ == "__main__":
    unittest.main()
