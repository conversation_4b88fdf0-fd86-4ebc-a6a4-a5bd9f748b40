"""
Performance tests for Deep Research Core.

This module contains performance tests for various components of the Deep Research Core.
These tests are designed to measure the performance of key operations and identify
potential bottlenecks.
"""

import pytest
import time
import os
import sys
import numpy as np
from typing import List, Dict, Any, Optional

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

from src.deep_research_core.reasoning.cot import ChainOfThought
from src.deep_research_core.reasoning.tot import TreeOfThought
from src.deep_research_core.reasoning.rag import SQLiteVectorRAG
from src.deep_research_core.reasoning.cotrag import CoTRAG
from src.deep_research_core.reasoning.totrag import ToTRAG
from src.deep_research_core.reasoning.rag_tot_cot import RAGTOTCOTReasoner
from src.deep_research_core.reasoning.react import ReActReasoner
from src.deep_research_core.utils.performance_metrics import measure_latency

# Skip these tests in CI environment to avoid API calls
pytestmark = pytest.mark.skipif(
    os.environ.get('CI') == 'true',
    reason="Skip performance tests in CI environment"
)

# Test data
SIMPLE_QUERY = "Thủ đô của Việt Nam là gì?"
COMPLEX_QUERY = "Phân tích tác động của biến đổi khí hậu đối với nông nghiệp Việt Nam trong 10 năm tới."
ANALYTICAL_QUERY = "So sánh ưu nhược điểm của các mô hình kinh tế thị trường và kinh tế kế hoạch hóa."
CREATIVE_QUERY = "Viết một câu chuyện ngắn về tương lai của trí tuệ nhân tạo trong 100 năm tới."

# Mock API provider for testing
class MockProvider:
    """Mock API provider for testing."""
    
    def __init__(self, delay: float = 0.1):
        """Initialize the mock provider.
        
        Args:
            delay: Delay in seconds to simulate API latency
        """
        self.delay = delay
        self.temperature = 0.7
        self.max_tokens = 1000
    
    def generate(self, prompt: str, **kwargs) -> str:
        """Generate a response for the given prompt.
        
        Args:
            prompt: The prompt to generate a response for
            **kwargs: Additional arguments
            
        Returns:
            A mock response
        """
        time.sleep(self.delay)  # Simulate API latency
        
        # Return different responses based on the prompt
        if "Thủ đô" in prompt or "thủ đô" in prompt:
            return "Hà Nội là thủ đô của Việt Nam."
        elif "biến đổi khí hậu" in prompt:
            return "Biến đổi khí hậu sẽ tác động đến nông nghiệp Việt Nam theo nhiều cách..."
        elif "mô hình kinh tế" in prompt:
            return "Mô hình kinh tế thị trường có ưu điểm là..."
        elif "trí tuệ nhân tạo" in prompt:
            return "Trong tương lai, trí tuệ nhân tạo sẽ phát triển..."
        else:
            return "Đây là câu trả lời mẫu cho prompt: " + prompt[:50] + "..."

@pytest.fixture
def mock_provider():
    """Fixture for mock API provider."""
    return MockProvider()

@pytest.fixture
def cot_reasoner(mock_provider):
    """Fixture for ChainOfThought reasoner."""
    return ChainOfThought(
        provider=mock_provider,
        model="test-model",
        temperature=0.7,
        max_tokens=1000
    )

@pytest.fixture
def tot_reasoner(mock_provider):
    """Fixture for TreeOfThought reasoner."""
    return TreeOfThought(
        provider=mock_provider,
        model="test-model",
        temperature=0.7,
        max_tokens=1000,
        max_branches=3,
        max_depth=2
    )

@pytest.fixture
def rag_reasoner():
    """Fixture for SQLiteVectorRAG reasoner."""
    return SQLiteVectorRAG(
        db_path=":memory:",
        embedding_model="test-model",
        provider="mock",
        model="test-model",
        temperature=0.7,
        max_tokens=1000
    )

@pytest.fixture
def cotrag_reasoner(mock_provider):
    """Fixture for CoTRAG reasoner."""
    rag = SQLiteVectorRAG(
        db_path=":memory:",
        embedding_model="test-model",
        provider="mock",
        model="test-model",
        temperature=0.7,
        max_tokens=1000
    )
    
    return CoTRAG(
        rag=rag,
        provider=mock_provider,
        model="test-model",
        temperature=0.7,
        max_tokens=1000
    )

@pytest.fixture
def totrag_reasoner(mock_provider):
    """Fixture for ToTRAG reasoner."""
    rag = SQLiteVectorRAG(
        db_path=":memory:",
        embedding_model="test-model",
        provider="mock",
        model="test-model",
        temperature=0.7,
        max_tokens=1000
    )
    
    return ToTRAG(
        rag=rag,
        provider=mock_provider,
        model="test-model",
        temperature=0.7,
        max_tokens=1000,
        max_branches=3,
        max_depth=2
    )

@pytest.fixture
def ragtotcot_reasoner(mock_provider):
    """Fixture for RAGTOTCOTReasoner."""
    return RAGTOTCOTReasoner(
        provider=mock_provider,
        model="test-model",
        temperature=0.7,
        max_tokens=1000,
        vector_store_type="sqlite",
        vector_store_path=":memory:",
        embedding_model="test-model"
    )

@pytest.fixture
def react_reasoner(mock_provider):
    """Fixture for ReActReasoner."""
    return ReActReasoner(
        provider=mock_provider,
        model="test-model",
        temperature=0.7,
        max_tokens=1000,
        max_iterations=5
    )

@pytest.mark.performance
def test_cot_performance(cot_reasoner, benchmark):
    """Test ChainOfThought performance."""
    benchmark(cot_reasoner.reason, query=SIMPLE_QUERY)

@pytest.mark.performance
def test_cot_complex_performance(cot_reasoner, benchmark):
    """Test ChainOfThought performance with complex query."""
    benchmark(cot_reasoner.reason, query=COMPLEX_QUERY)

@pytest.mark.performance
def test_tot_performance(tot_reasoner, benchmark):
    """Test TreeOfThought performance."""
    benchmark(tot_reasoner.reason, query=SIMPLE_QUERY)

@pytest.mark.performance
def test_tot_complex_performance(tot_reasoner, benchmark):
    """Test TreeOfThought performance with complex query."""
    benchmark(tot_reasoner.reason, query=COMPLEX_QUERY)

@pytest.mark.performance
def test_rag_performance(rag_reasoner, benchmark):
    """Test SQLiteVectorRAG performance."""
    # Add some documents to the RAG
    rag_reasoner.add_texts(["Hà Nội là thủ đô của Việt Nam."])
    benchmark(rag_reasoner.query, query=SIMPLE_QUERY)

@pytest.mark.performance
def test_cotrag_performance(cotrag_reasoner, benchmark):
    """Test CoTRAG performance."""
    benchmark(cotrag_reasoner.reason, query=SIMPLE_QUERY)

@pytest.mark.performance
def test_totrag_performance(totrag_reasoner, benchmark):
    """Test ToTRAG performance."""
    benchmark(totrag_reasoner.reason, query=SIMPLE_QUERY)

@pytest.mark.performance
def test_ragtotcot_performance(ragtotcot_reasoner, benchmark):
    """Test RAGTOTCOTReasoner performance."""
    benchmark(ragtotcot_reasoner.reason, query=SIMPLE_QUERY)

@pytest.mark.performance
def test_react_performance(react_reasoner, benchmark):
    """Test ReActReasoner performance."""
    benchmark(react_reasoner.reason, query=SIMPLE_QUERY)

@pytest.mark.performance
def test_reasoning_comparison(cot_reasoner, tot_reasoner, cotrag_reasoner, totrag_reasoner, ragtotcot_reasoner, react_reasoner):
    """Compare performance of different reasoning methods."""
    queries = [SIMPLE_QUERY, COMPLEX_QUERY, ANALYTICAL_QUERY, CREATIVE_QUERY]
    reasoners = {
        "CoT": cot_reasoner,
        "ToT": tot_reasoner,
        "CoTRAG": cotrag_reasoner,
        "ToTRAG": totrag_reasoner,
        "RAGTOTCoT": ragtotcot_reasoner,
        "ReAct": react_reasoner
    }
    
    results = {}
    
    for name, reasoner in reasoners.items():
        query_times = {}
        
        for query in queries:
            start_time = time.time()
            reasoner.reason(query=query)
            end_time = time.time()
            
            query_times[query] = end_time - start_time
        
        results[name] = query_times
    
    # Print results
    print("\nPerformance Comparison:")
    print("-" * 80)
    print(f"{'Reasoner':<10} | {'Simple':<10} | {'Complex':<10} | {'Analytical':<10} | {'Creative':<10} | {'Average':<10}")
    print("-" * 80)
    
    for name, query_times in results.items():
        avg_time = sum(query_times.values()) / len(query_times)
        print(f"{name:<10} | {query_times[SIMPLE_QUERY]:<10.2f} | {query_times[COMPLEX_QUERY]:<10.2f} | "
              f"{query_times[ANALYTICAL_QUERY]:<10.2f} | {query_times[CREATIVE_QUERY]:<10.2f} | {avg_time:<10.2f}")
    
    # Assert that all reasoners completed successfully
    assert len(results) == len(reasoners)

@pytest.mark.performance
def test_vietnamese_text_processing_performance():
    """Test performance of Vietnamese text processing."""
    from src.deep_research_core.multilingual.vietnamese_text_processor import VietnameseTextProcessor
    
    processor = VietnameseTextProcessor()
    
    # Test text normalization
    text = "Hà Nội là thủ đô của Việt Nam. Đây là một thành phố có lịch sử lâu đời."
    
    start_time = time.time()
    for _ in range(100):
        processor.normalize_text(text)
    end_time = time.time()
    
    normalization_time = (end_time - start_time) / 100
    print(f"\nVietnamese text normalization: {normalization_time:.6f} seconds per operation")
    
    # Test compound word detection
    start_time = time.time()
    for _ in range(100):
        processor.detect_compound_words(text)
    end_time = time.time()
    
    compound_detection_time = (end_time - start_time) / 100
    print(f"Vietnamese compound word detection: {compound_detection_time:.6f} seconds per operation")
    
    # Assert that the operations completed in a reasonable time
    assert normalization_time < 0.1, "Text normalization is too slow"
    assert compound_detection_time < 0.1, "Compound word detection is too slow"

if __name__ == "__main__":
    # Run the tests
    pytest.main(["-v", __file__])
