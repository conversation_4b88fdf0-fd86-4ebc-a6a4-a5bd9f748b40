import os
import sys
import unittest
from unittest.mock import MagicMock, patch
import tempfile
import shutil
import json

# Thêm đường dẫn thư mục gốc vào sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.deep_research_core.agents.adaptive_crawler import AdaptiveCrawler


class TestAJAXHandling(unittest.TestCase):
    """
    Test case cho tính năng xử lý AJAX trong AdaptiveCrawler
    """

    def setUp(self):
        """
        Thiết lập môi trường test
        """
        # Tạo thư mục tạm để lưu file
        self.temp_dir = tempfile.mkdtemp()

        # Tạo AdaptiveCrawler với các tham số AJAX
        self.crawler = AdaptiveCrawler(
            download_path=self.temp_dir,
            enable_javascript=True,
            wait_for_selector=".content",
            wait_for_timeout=2000,
            handle_ajax=True,
            ajax_wait_time=3000,
            ajax_request_patterns=["/api/", ".json", "ajax", "xhr"]
        )

    def tearDown(self):
        """
        Dọn dẹp sau khi test
        """
        # Xóa thư mục tạm
        shutil.rmtree(self.temp_dir)

    @patch('playwright.sync_api.sync_playwright')
    def test_handle_ajax(self, mock_playwright):
        """
        Test phương thức _handle_ajax
        """
        # Tạo mock cho Playwright
        mock_context = MagicMock()
        mock_page = MagicMock()
        mock_browser = MagicMock()

        # Tạo mock cho request và response
        mock_request = MagicMock()
        mock_request.url = "https://example.com/api/data.json"
        mock_request.resource_type = "xhr"

        mock_response = MagicMock()
        mock_response.request = mock_request
        mock_response.status = 200

        # Thiết lập mock cho page
        mock_page.on = MagicMock()

        # Thiết lập mock cho browser và context
        mock_browser.new_context.return_value = mock_context
        mock_context.new_page.return_value = mock_page

        # Thiết lập mock cho playwright
        mock_playwright_instance = MagicMock()
        mock_playwright_instance.chromium.launch.return_value = mock_browser
        mock_playwright.return_value.__enter__.return_value = mock_playwright_instance

        # Gọi phương thức _handle_ajax
        self.crawler._handle_ajax(mock_page, "https://example.com")

        # Kiểm tra xem các phương thức đã được gọi đúng cách
        mock_page.wait_for_timeout.assert_any_call(1000)  # Đợi JavaScript thực thi

        # Kiểm tra xem phương thức on đã được gọi để theo dõi request và response
        mock_page.on.assert_any_call("request", unittest.mock.ANY)
        mock_page.on.assert_any_call("response", unittest.mock.ANY)

        # Kiểm tra xem phương thức _trigger_ajax_events đã được gọi
        mock_page.evaluate.assert_called()

        # Kiểm tra xem phương thức wait_for_timeout đã được gọi với ajax_wait_time
        mock_page.wait_for_timeout.assert_any_call(3000)

    @patch('playwright.sync_api.sync_playwright')
    def test_track_ajax_request(self, mock_playwright):
        """
        Test phương thức _track_ajax_request
        """
        # Tạo mock cho request
        mock_request = MagicMock()
        mock_request.url = "https://example.com/api/data.json"
        mock_request.resource_type = "xhr"

        # Tạo danh sách ajax_requests
        ajax_requests = []

        # Gọi phương thức _track_ajax_request
        self.crawler._track_ajax_request(mock_request, ajax_requests)

        # Kiểm tra xem request đã được thêm vào danh sách
        self.assertEqual(len(ajax_requests), 1)
        self.assertEqual(ajax_requests[0], mock_request)

        # Kiểm tra với resource_type khác
        mock_request2 = MagicMock()
        mock_request2.url = "https://example.com/image.jpg"
        mock_request2.resource_type = "image"

        # Gọi phương thức _track_ajax_request
        self.crawler._track_ajax_request(mock_request2, ajax_requests)

        # Kiểm tra xem request không được thêm vào danh sách
        self.assertEqual(len(ajax_requests), 1)

        # Kiểm tra với URL pattern
        mock_request3 = MagicMock()
        mock_request3.url = "https://example.com/data.json"
        mock_request3.resource_type = "document"

        # Gọi phương thức _track_ajax_request
        self.crawler._track_ajax_request(mock_request3, ajax_requests)

        # Kiểm tra xem request đã được thêm vào danh sách
        self.assertEqual(len(ajax_requests), 2)
        self.assertEqual(ajax_requests[1], mock_request3)

    @patch('playwright.sync_api.sync_playwright')
    def test_crawl_with_ajax(self, mock_playwright):
        """
        Test tính năng crawl với AJAX
        """
        # Tạo mock cho Playwright
        mock_context = MagicMock()
        mock_page = MagicMock()
        mock_browser = MagicMock()

        # Thiết lập mock cho page
        mock_page.title.return_value = "Test AJAX Page"
        mock_page.content.return_value = "<html><body><div class='content'>Test content with AJAX</div></body></html>"

        # Thiết lập mock cho browser và context
        mock_browser.new_context.return_value = mock_context
        mock_context.new_page.return_value = mock_page

        # Thiết lập mock cho playwright
        mock_playwright_instance = MagicMock()
        mock_playwright_instance.chromium.launch.return_value = mock_browser
        mock_playwright.return_value.__enter__.return_value = mock_playwright_instance

        # Gọi phương thức crawl
        result = self.crawler.crawl(
            urls=["https://example.com/ajax-page"]
        )

        # Kiểm tra kết quả
        self.assertTrue(result["success"])
        self.assertEqual(len(result["results"]), 1)
        self.assertEqual(result["results"][0]["title"], "Test AJAX Page")

        # Kiểm tra xem các tham số AJAX đã được sử dụng
        mock_page.wait_for_timeout.assert_called()

        # Lưu kết quả ra file để kiểm tra
        result_path = os.path.join(self.temp_dir, "ajax_crawl_result.txt")
        with open(result_path, "w", encoding="utf-8") as f:
            # Chuyển đổi kết quả thành chuỗi để tránh lỗi với MagicMock
            f.write(f"Success: {result['success']}\n")
            f.write(f"Count: {result['count']}\n")
            f.write(f"Results count: {len(result['results'])}\n")
            if result['results']:
                f.write(f"First result title: {result['results'][0]['title']}\n")

        print(f"Đã lưu kết quả crawl vào: {result_path}")


if __name__ == '__main__':
    print("Bắt đầu chạy test AJAX handling...")
    unittest.main(verbosity=2)
