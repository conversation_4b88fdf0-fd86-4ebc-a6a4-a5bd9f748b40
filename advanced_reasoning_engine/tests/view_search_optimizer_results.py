#!/usr/bin/env python3
"""
Hi<PERSON>n thị kết quả đánh giá SearchResultOptimizer từ file JSON.
"""

import json
import os
import sys
from typing import Dict, Any, List
import matplotlib.pyplot as plt
import numpy as np
from tabulate import tabulate

def load_results(file_path: str) -> Dict[str, Any]:
    """
    Đọc kết quả đánh giá từ file JSON.
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Lỗi khi đọc file: {e}")
        sys.exit(1)

def print_summary(data: Dict[str, Any]) -> None:
    """
    In tóm tắt kết quả đánh giá.
    """
    summary = data.get("summary", {})
    config = data.get("optimizer_config", {})
    
    print("\n" + "=" * 80)
    print(f"ĐÁNH GIÁ SEARCH RESULT OPTIMIZER (Ngày: {data.get('test_date', 'N/A')})")
    print("=" * 80)
    
    print("\nCấu hình:")
    print(f"- Ngưỡng độ liên quan: {config.get('relevance_threshold', 'N/A')}")
    print(f"- Ngưỡng chất lượng: {config.get('quality_threshold', 'N/A')}")
    print(f"- Độ dài nội dung tối đa: {config.get('max_content_length', 'N/A')} ký tự")
    print(f"- Độ dài nội dung tối thiểu: {config.get('min_content_length', 'N/A')} ký tự")
    print(f"- Số kết quả tối đa: {config.get('max_results', 'N/A')}")
    print(f"- Số kết quả tối thiểu: {config.get('min_results', 'N/A')}")
    print(f"- Độ dài lý tưởng: {config.get('ideal_total_length', 'N/A')} ký tự")
    
    print("\nTổng quan:")
    print(f"- Tổng số truy vấn: {summary.get('total_queries', 'N/A')}")
    print(f"- Điểm trung bình: {summary.get('average_score', 'N/A')}/10")
    print(f"- Độ chính xác phát hiện lĩnh vực: {summary.get('domain_detection_accuracy', 'N/A')}%")
    
    print("\nPhân loại kết quả:")
    quality_dist = summary.get("quality_distribution", {})
    for quality, count in quality_dist.items():
        if count > 0:
            percentage = (count / summary.get('total_queries', 1)) * 100
            print(f"- {quality}: {count} truy vấn ({percentage:.1f}%)")

def print_test_cases(data: Dict[str, Any]) -> None:
    """
    In chi tiết các trường hợp kiểm tra.
    """
    test_cases = data.get("test_cases", [])
    
    print("\n" + "=" * 80)
    print("CHI TIẾT CÁC TRƯỜNG HỢP KIỂM TRA")
    print("=" * 80)
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. Truy vấn: '{case.get('query', 'N/A')}'")
        
        # Thông tin lĩnh vực
        domain = case.get("domain", {})
        domain_status = "✓" if domain.get("is_correct") else "✗"
        print(f"   Lĩnh vực: {domain.get('expected', 'N/A')} (Phát hiện: {domain.get('detected', 'N/A')}) {domain_status}")
        
        # Thông tin kết quả
        results = case.get("results", {})
        print(f"   Số kết quả: {results.get('original_count', 'N/A')} → {results.get('optimized_count', 'N/A')}")
        print(f"   Độ dài nội dung: {results.get('content_length', 'N/A')} ký tự")
        
        # Chất lượng
        quality = case.get("quality_metrics", {})
        print(f"   Điểm: {quality.get('score', 'N/A')}/10 ({quality.get('assessment', 'N/A')})")
        
        # Chi tiết chất lượng
        count = quality.get("count", {})
        content_length = quality.get("content_length", {})
        diversity = quality.get("diversity", {})
        relevance = quality.get("relevance", {})
        
        print(f"   - Số lượng: {count.get('value', 'N/A')} ({count.get('quality', 'N/A')})")
        print(f"   - Độ dài: {content_length.get('value', 'N/A')} ký tự ({content_length.get('quality', 'N/A')})")
        print(f"   - Đa dạng: {diversity.get('value', 'N/A')} nguồn ({diversity.get('quality', 'N/A')})")
        print(f"   - Độ liên quan: {relevance.get('value', 'N/A'):.2f} ({relevance.get('quality', 'N/A')})")
        
        # Ghi chú
        if "notes" in case:
            print(f"   Ghi chú: {case.get('notes', '')}")

def print_recommendations(data: Dict[str, Any]) -> None:
    """
    In các đề xuất cải tiến.
    """
    recommendations = data.get("recommendations", [])
    
    print("\n" + "=" * 80)
    print("ĐỀ XUẤT CẢI TIẾN")
    print("=" * 80)
    
    for i, rec in enumerate(recommendations, 1):
        print(f"\n{i}. {rec.get('category', 'N/A')}: {rec.get('description', '')}")
        for j, suggestion in enumerate(rec.get("suggestions", []), 1):
            print(f"   {j}. {suggestion}")

def create_charts(data: Dict[str, Any], output_dir: str) -> None:
    """
    Tạo biểu đồ từ dữ liệu đánh giá.
    """
    try:
        # Đảm bảo thư mục đầu ra tồn tại
        os.makedirs(output_dir, exist_ok=True)
        
        # Dữ liệu cho biểu đồ
        test_cases = data.get("test_cases", [])
        queries = [case.get("query", "")[:20] + "..." if len(case.get("query", "")) > 20 else case.get("query", "") for case in test_cases]
        scores = [case.get("quality_metrics", {}).get("score", 0) for case in test_cases]
        relevance = [case.get("quality_metrics", {}).get("relevance", {}).get("value", 0) for case in test_cases]
        
        # Biểu đồ điểm số
        plt.figure(figsize=(12, 6))
        plt.bar(queries, scores, color='skyblue')
        plt.axhline(y=data.get("summary", {}).get("average_score", 0), color='r', linestyle='-', label=f'Điểm trung bình: {data.get("summary", {}).get("average_score", 0):.2f}')
        plt.xlabel('Truy vấn')
        plt.ylabel('Điểm (0-10)')
        plt.title('Điểm chất lượng theo truy vấn')
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()
        plt.legend()
        plt.savefig(os.path.join(output_dir, 'scores_chart.png'))
        
        # Biểu đồ độ liên quan
        plt.figure(figsize=(12, 6))
        plt.bar(queries, relevance, color='lightgreen')
        plt.axhline(y=data.get("optimizer_config", {}).get("relevance_threshold", 0), color='r', linestyle='-', label=f'Ngưỡng: {data.get("optimizer_config", {}).get("relevance_threshold", 0):.2f}')
        plt.xlabel('Truy vấn')
        plt.ylabel('Độ liên quan (0-1)')
        plt.title('Độ liên quan theo truy vấn')
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()
        plt.legend()
        plt.savefig(os.path.join(output_dir, 'relevance_chart.png'))
        
        # Biểu đồ phát hiện lĩnh vực
        domain_correct = sum(1 for case in test_cases if case.get("domain", {}).get("is_correct", False))
        domain_incorrect = len(test_cases) - domain_correct
        
        plt.figure(figsize=(8, 8))
        plt.pie([domain_correct, domain_incorrect], 
                labels=['Chính xác', 'Không chính xác'], 
                autopct='%1.1f%%',
                colors=['lightgreen', 'lightcoral'])
        plt.title('Độ chính xác phát hiện lĩnh vực')
        plt.savefig(os.path.join(output_dir, 'domain_accuracy_chart.png'))
        
        print(f"\nĐã tạo biểu đồ trong thư mục: {output_dir}")
    except Exception as e:
        print(f"Lỗi khi tạo biểu đồ: {e}")

def main():
    """
    Hàm chính.
    """
    # Kiểm tra đường dẫn file
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        file_path = "tests/search_optimizer_evaluation_results.json"
    
    # Kiểm tra file tồn tại
    if not os.path.exists(file_path):
        print(f"Lỗi: File '{file_path}' không tồn tại.")
        sys.exit(1)
    
    # Đọc dữ liệu
    data = load_results(file_path)
    
    # In kết quả
    print_summary(data)
    print_test_cases(data)
    print_recommendations(data)
    
    # Tạo biểu đồ nếu có thư viện matplotlib
    try:
        import matplotlib
        create_charts(data, "tests/charts")
    except ImportError:
        print("\nLưu ý: Không thể tạo biểu đồ do thiếu thư viện matplotlib.")
        print("Cài đặt matplotlib bằng lệnh: pip install matplotlib")

if __name__ == "__main__":
    main()
