"""
Test the rate limiter implementation.
"""

import time
import unittest
from unittest.mock import patch, MagicMock

import sys
import os

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from src.deep_research_core.utils.rate_limiter import RateLimiter


class TestRateLimiter(unittest.TestCase):
    """Test the RateLimiter class."""

    def setUp(self):
        """Set up the test case."""
        self.rate_limiter = RateLimiter(
            global_limit=5,
            global_window=1,
            engine_limits={
                "test_engine": (3, 1),  # 3 requests per second
                "default": (5, 1),  # 5 requests per second
            },
        )

    def test_global_limit(self):
        """Test global rate limiting."""
        # First 5 requests should pass
        for i in range(5):
            self.assertTrue(
                self.rate_limiter.check_limit(wait_if_limited=False),
                f"Request {i+1} should pass",
            )

        # 6th request should fail
        self.assertFalse(
            self.rate_limiter.check_limit(wait_if_limited=False),
            "Request 6 should fail due to global limit",
        )

        # Wait for the window to pass
        time.sleep(1.1)

        # Next request should pass
        self.assertTrue(
            self.rate_limiter.check_limit(wait_if_limited=False),
            "Request after waiting should pass",
        )

    def test_engine_specific_limit(self):
        """Test engine-specific rate limiting."""
        # First 3 requests to test_engine should pass
        for i in range(3):
            self.assertTrue(
                self.rate_limiter.check_limit(
                    engine="test_engine", wait_if_limited=False
                ),
                f"Request {i+1} to test_engine should pass",
            )

        # 4th request to test_engine should fail
        self.assertFalse(
            self.rate_limiter.check_limit(engine="test_engine", wait_if_limited=False),
            "Request 4 to test_engine should fail",
        )

        # But request to another engine should pass
        self.assertTrue(
            self.rate_limiter.check_limit(
                engine="another_engine", wait_if_limited=False
            ),
            "Request to another_engine should pass",
        )

    @patch("time.sleep")
    def test_waiting(self, mock_sleep):
        """Test waiting behavior."""
        # First 3 requests to test_engine should pass
        for i in range(3):
            self.assertTrue(
                self.rate_limiter.check_limit(
                    engine="test_engine", wait_if_limited=True
                ),
                f"Request {i+1} to test_engine should pass",
            )

        # 4th request should wait and then pass
        self.assertTrue(
            self.rate_limiter.check_limit(engine="test_engine", wait_if_limited=True),
            "Request 4 to test_engine should wait and then pass",
        )

        # Verify that sleep was called
        mock_sleep.assert_called_once()

    def test_stats(self):
        """Test statistics tracking."""
        # Make some requests
        for i in range(3):
            self.rate_limiter.check_limit(engine="test_engine")

        # Get stats
        stats = self.rate_limiter.get_stats()

        # Verify stats
        self.assertEqual(stats["total_requests"], 3)
        self.assertEqual(stats["engine_counts"]["test_engine"], 3)

    def test_thread_safety(self):
        """Test thread safety with a mock lock."""
        # Create a mock lock
        mock_lock = MagicMock()

        # Replace the real lock with the mock
        self.rate_limiter.lock = mock_lock

        # Call check_limit
        self.rate_limiter.check_limit()

        # Verify that the lock was acquired and released
        mock_lock.acquire.assert_called()
        mock_lock.release.assert_called()


if __name__ == "__main__":
    unittest.main()
