#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Chạy test case và ghi đầu ra vào file.

Script này chạy các test case cho WebSearchAgentLocal và ghi đầu ra vào file để đọc sau.
"""

import os
import sys
import time
import json
import unittest
import logging
import argparse
from datetime import datetime
from io import StringIO
import contextlib

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Thêm thư mục gốc vào sys.path để import các module
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def print_section(title, output_file=None):
    """In tiêu đề phần."""
    section_text = "\n" + "=" * 80 + "\n"
    section_text += f" {title} ".center(80, "=") + "\n"
    section_text += "=" * 80 + "\n"
    
    print(section_text)
    if output_file:
        output_file.write(section_text)

@contextlib.contextmanager
def capture_output():
    """Capture stdout and stderr."""
    old_stdout = sys.stdout
    old_stderr = sys.stderr
    new_stdout = StringIO()
    new_stderr = StringIO()
    sys.stdout = new_stdout
    sys.stderr = new_stderr
    try:
        yield new_stdout, new_stderr
    finally:
        sys.stdout = old_stdout
        sys.stderr = old_stderr

def run_test_module(module_name, output_file, verbosity=2):
    """Chạy một module test cụ thể và ghi đầu ra vào file."""
    print_section(f"Chạy test module: {module_name}", output_file)
    
    try:
        # Import module test
        __import__(module_name)
        module = sys.modules[module_name]
        
        # Tạo test suite
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromModule(module)
        
        # Chạy test và capture output
        with capture_output() as (stdout, stderr):
            runner = unittest.TextTestRunner(verbosity=verbosity)
            result = runner.run(suite)
        
        # Ghi output vào file
        output_file.write(stdout.getvalue())
        output_file.write(stderr.getvalue())
        
        # Trả về kết quả
        return {
            "module": module_name,
            "total": result.testsRun,
            "errors": len(result.errors),
            "failures": len(result.failures),
            "skipped": len(result.skipped),
            "success": result.wasSuccessful()
        }
    except Exception as e:
        error_message = f"Lỗi khi chạy module test {module_name}: {str(e)}"
        logger.error(error_message)
        output_file.write(f"\n{error_message}\n")
        return {
            "module": module_name,
            "total": 0,
            "errors": 1,
            "failures": 0,
            "skipped": 0,
            "success": False,
            "error_message": str(e)
        }

def run_all_tests(output_filename, test_modules=None, verbosity=2):
    """Chạy tất cả các test module và ghi đầu ra vào file."""
    # Tạo thư mục lưu kết quả nếu chưa tồn tại
    os.makedirs("test_results", exist_ok=True)
    
    # Mở file output
    with open(output_filename, "w", encoding="utf-8") as output_file:
        print_section("Chạy test case cho WebSearchAgentLocal", output_file)
        output_file.write(f"Thời gian bắt đầu: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # Nếu không có danh sách module, tìm tất cả các module test
        if test_modules is None:
            test_modules = []
            for file in os.listdir("tests"):
                if file.startswith("test_") and file.endswith(".py"):
                    module_name = f"tests.{file[:-3]}"
                    test_modules.append(module_name)
        
        # Chạy từng module test
        results = []
        for module_name in test_modules:
            result = run_test_module(module_name, output_file, verbosity)
            results.append(result)
            
            # Đợi một chút giữa các module test
            time.sleep(1)
        
        # Tạo báo cáo tổng hợp
        total_tests = sum(result["total"] for result in results)
        total_errors = sum(result["errors"] for result in results)
        total_failures = sum(result["failures"] for result in results)
        total_skipped = sum(result["skipped"] for result in results)
        total_success = sum(1 for result in results if result["success"])
        
        summary = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "total_modules": len(results),
            "total_tests": total_tests,
            "total_errors": total_errors,
            "total_failures": total_failures,
            "total_skipped": total_skipped,
            "total_success": total_success,
            "success_rate": f"{total_success / len(results) * 100:.2f}%" if results else "N/A",
            "modules": results
        }
        
        # Lưu báo cáo tổng hợp
        with open("test_results/summary.json", "w", encoding="utf-8") as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        # Ghi báo cáo tổng hợp vào file output
        print_section("Báo cáo tổng hợp", output_file)
        output_file.write(f"Thời gian kết thúc: {summary['timestamp']}\n")
        output_file.write(f"Tổng số module: {summary['total_modules']}\n")
        output_file.write(f"Tổng số test: {summary['total_tests']}\n")
        output_file.write(f"Tổng số lỗi: {summary['total_errors']}\n")
        output_file.write(f"Tổng số thất bại: {summary['total_failures']}\n")
        output_file.write(f"Tổng số bỏ qua: {summary['total_skipped']}\n")
        output_file.write(f"Tổng số thành công: {summary['total_success']}\n")
        output_file.write(f"Tỷ lệ thành công: {summary['success_rate']}\n\n")
        
        output_file.write("Kết quả chi tiết:\n")
        for result in results:
            status = "✅ Thành công" if result["success"] else "❌ Thất bại"
            output_file.write(f"{result['module']}: {status} ({result['total']} tests, {result['errors']} errors, {result['failures']} failures, {result['skipped']} skipped)\n")
    
    print(f"Đã ghi kết quả test vào file {output_filename}")
    return summary

def main():
    """Hàm chính."""
    # Phân tích tham số dòng lệnh
    parser = argparse.ArgumentParser(description="Chạy test case cho WebSearchAgentLocal và ghi đầu ra vào file")
    parser.add_argument("--modules", nargs="+", help="Danh sách các module test cần chạy")
    parser.add_argument("--verbosity", type=int, default=2, help="Mức độ chi tiết của output (1-3)")
    parser.add_argument("--output", type=str, default="test_output.txt", help="Tên file output")
    
    args = parser.parse_args()
    
    # Chạy tất cả các test
    run_all_tests(args.output, args.modules, args.verbosity)

if __name__ == "__main__":
    main()
