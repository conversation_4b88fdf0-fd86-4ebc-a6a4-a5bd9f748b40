#!/bin/bash

# Create necessary directories
mkdir -p src/deep_research_core/{agents,models/{api,local},reasoning,retrieval,evaluation,utils,config}
mkdir -p src/scripts
mkdir -p examples
mkdir -p tests/{unit,integration}
mkdir -p data/{sample_documents,evaluation_data}
mkdir -p docs/{api,user_guide,developer_guide}
mkdir -p notebooks

# Create __init__.py files
touch src/deep_research_core/__init__.py
touch src/deep_research_core/{agents,models,models/api,models/local,reasoning,retrieval,evaluation,utils,config}/__init__.py

# Move agent files
if [ -d "agents" ]; then
    cp agents/*.py src/deep_research_core/agents/
fi

# Move model files
if [ -d "models" ]; then
    cp models/api/*.py src/deep_research_core/models/api/
    # Create local model directory if needed
    mkdir -p src/deep_research_core/models/local
    touch src/deep_research_core/models/local/__init__.py
fi

# Move reasoning files
if [ -d "reasoning" ]; then
    cp reasoning/*.py src/deep_research_core/reasoning/
fi

# Move retrieval files
if [ -d "retrieval" ]; then
    cp retrieval/*.py src/deep_research_core/retrieval/
fi

# Move evaluation files
if [ -d "evaluation" ]; then
    cp evaluation/*.py src/deep_research_core/evaluation/
fi

# Move utils files
if [ -d "utils" ]; then
    cp utils/*.py src/deep_research_core/utils/
fi

# Move config files
if [ -d "config" ]; then
    cp config/*.py src/deep_research_core/config/
fi

# Move main scripts
cp main.py src/scripts/
cp run_enhanced_cotrag.py src/scripts/
cp run_evaluation.py src/scripts/

# Move test files to examples
cp test_*.py examples/
cp simple_*.py examples/

# Create a basic README in each directory
echo "# Deep Research Core - Agents" > src/deep_research_core/agents/README.md
echo "# Deep Research Core - Models" > src/deep_research_core/models/README.md
echo "# Deep Research Core - API Models" > src/deep_research_core/models/api/README.md
echo "# Deep Research Core - Local Models" > src/deep_research_core/models/local/README.md
echo "# Deep Research Core - Reasoning" > src/deep_research_core/reasoning/README.md
echo "# Deep Research Core - Retrieval" > src/deep_research_core/retrieval/README.md
echo "# Deep Research Core - Evaluation" > src/deep_research_core/evaluation/README.md
echo "# Deep Research Core - Utils" > src/deep_research_core/utils/README.md
echo "# Deep Research Core - Config" > src/deep_research_core/config/README.md
echo "# Deep Research Core - Scripts" > src/scripts/README.md
echo "# Deep Research Core - Examples" > examples/README.md
echo "# Deep Research Core - Tests" > tests/README.md
echo "# Deep Research Core - Unit Tests" > tests/unit/README.md
echo "# Deep Research Core - Integration Tests" > tests/integration/README.md
echo "# Deep Research Core - Data" > data/README.md
echo "# Deep Research Core - Documentation" > docs/README.md
echo "# Deep Research Core - Notebooks" > notebooks/README.md

echo "Reorganization complete!"
