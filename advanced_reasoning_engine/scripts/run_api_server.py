#!/usr/bin/env python3
"""
Run API server for Deep Research Core.
"""

import os
import argparse
import uvicorn
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

def main():
    """Run API server."""
    parser = argparse.ArgumentParser(description="Run Deep Research Core API server")
    parser.add_argument("--host", default=os.getenv("API_HOST", "0.0.0.0"), help="Host to bind to")
    parser.add_argument("--port", type=int, default=int(os.getenv("API_PORT", "8000")), help="Port to bind to")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload")
    parser.add_argument("--workers", type=int, default=1, help="Number of worker processes")
    parser.add_argument("--log-level", default=os.getenv("LOG_LEVEL", "info"), help="Log level")
    args = parser.parse_args()

    # Run server
    uvicorn.run(
        "src.deep_research_core.api.main:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        workers=args.workers,
        log_level=args.log_level.lower()
    )

if __name__ == "__main__":
    main()
