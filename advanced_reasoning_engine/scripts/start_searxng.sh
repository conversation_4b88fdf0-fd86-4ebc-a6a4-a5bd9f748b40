#!/bin/bash

# Kiểm tra xem Docker đã đư<PERSON><PERSON> cài đặt chưa
if ! command -v docker &> /dev/null; then
    echo "Docker chưa được cài đặt. Vui lòng cài đặt Docker trước."
    exit 1
fi

# Kiểm tra xem Docker Compose đã được cài đặt chưa
if ! command -v docker-compose &> /dev/null; then
    echo "Docker Compose chưa được cài đặt. Vui lòng cài đặt Docker Compose trước."
    exit 1
fi

# Đường dẫn đến file docker-compose.searxng.yml
COMPOSE_FILE="docker-compose.searxng.yml"

# Kiểm tra xem file docker-compose.searxng.yml có tồn tại không
if [ ! -f "$COMPOSE_FILE" ]; then
    echo "Không tìm thấy file $COMPOSE_FILE"
    exit 1
fi

# Kiểm tra xem thư mục searxng có tồn tại không
if [ ! -d "searxng" ]; then
    echo "<PERSON><PERSON><PERSON> <PERSON><PERSON> mục searxng..."
    mkdir -p searxng
fi

# Kiểm tra xem file cấu hình cải tiến có tồn tại không
if [ -f "searxng/settings_improved.yml" ]; then
    echo "Sử dụng file cấu hình cải tiến..."
    cp searxng/settings_improved.yml searxng/settings.yml
else
    echo "Không tìm thấy file cấu hình cải tiến. Sử dụng cấu hình mặc định."
fi

# Kiểm tra xem SearXNG đã chạy chưa
if docker ps | grep -q "searxng"; then
    echo "SearXNG đã đang chạy."
    echo "Bạn có muốn khởi động lại SearXNG không? (y/n)"
    read -r restart
    if [ "$restart" = "y" ]; then
        echo "Đang khởi động lại SearXNG..."
        docker-compose -f "$COMPOSE_FILE" down
        docker-compose -f "$COMPOSE_FILE" up -d
        echo "SearXNG đã được khởi động lại."
    else
        echo "Giữ nguyên SearXNG đang chạy."
    fi
else
    echo "Đang khởi động SearXNG..."
    docker-compose -f "$COMPOSE_FILE" up -d
    echo "SearXNG đã được khởi động."
    echo "Bạn có thể truy cập SearXNG tại http://localhost:8080"
fi

# Kiểm tra trạng thái của SearXNG
echo "Trạng thái của SearXNG:"
docker-compose -f "$COMPOSE_FILE" ps

# Kiểm tra kết nối với SearXNG
echo "Kiểm tra kết nối với SearXNG..."
sleep 5  # Đợi SearXNG khởi động hoàn tất
if command -v python3 &> /dev/null; then
    if [ -f "test_searxng_connection.py" ]; then
        python3 test_searxng_connection.py
    else
        echo "Không tìm thấy file test_searxng_connection.py"
    fi
else
    echo "Python3 chưa được cài đặt. Không thể kiểm tra kết nối."
fi
