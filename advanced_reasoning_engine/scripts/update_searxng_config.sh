#!/bin/bash

# Script để cập nhật cấu hình SearXNG và khởi động lại

# Kiểm tra xem Docker đã được cài đặt chưa
if ! command -v docker &> /dev/null; then
    echo "Docker chưa được cài đặt. Vui lòng cài đặt Docker trước."
    exit 1
fi

# Kiểm tra xem Docker Compose đã được cài đặt chưa
if ! command -v docker-compose &> /dev/null; then
    echo "Docker Compose chưa được cài đặt. Vui lòng cài đặt Docker Compose trước."
    exit 1
fi

# Đường dẫn đến file docker-compose.searxng.yml
COMPOSE_FILE="docker-compose.searxng.yml"

# Kiểm tra xem file docker-compose.searxng.yml có tồn tại không
if [ ! -f "$COMPOSE_FILE" ]; then
    echo "Không tìm thấy file $COMPOSE_FILE"
    exit 1
fi

# Kiểm tra xem thư mục searxng có tồn tại không
if [ ! -d "searxng" ]; then
    echo "T<PERSON><PERSON> thư mục searxng..."
    mkdir -p searxng
fi

# Kiểm tra xem file cấu hình cải tiến có tồn tại không
if [ -f "searxng/settings_improved.yml" ]; then
    echo "Sử dụng file cấu hình cải tiến..."
    cp searxng/settings_improved.yml searxng/settings.yml
else
    echo "Không tìm thấy file cấu hình cải tiến. Vui lòng tạo file searxng/settings_improved.yml trước."
    exit 1
fi

# Dừng SearXNG nếu đang chạy
if docker ps | grep -q "searxng"; then
    echo "Đang dừng SearXNG..."
    docker-compose -f "$COMPOSE_FILE" down
fi

# Khởi động lại SearXNG với cấu hình mới
echo "Đang khởi động lại SearXNG với cấu hình mới..."
docker-compose -f "$COMPOSE_FILE" up -d

# Đợi SearXNG khởi động
echo "Đợi SearXNG khởi động..."
sleep 10

# Kiểm tra trạng thái của SearXNG
echo "Trạng thái của SearXNG:"
docker-compose -f "$COMPOSE_FILE" ps

# Kiểm tra kết nối với SearXNG
echo "Kiểm tra kết nối với SearXNG..."
if command -v python3 &> /dev/null; then
    if [ -f "test_searxng_connection.py" ]; then
        python3 test_searxng_connection.py
    else
        echo "Không tìm thấy file test_searxng_connection.py"
    fi
else
    echo "Python3 chưa được cài đặt. Không thể kiểm tra kết nối."
fi

echo "Hoàn tất cập nhật cấu hình SearXNG."
