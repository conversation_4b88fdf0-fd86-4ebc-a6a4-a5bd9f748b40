#!/bin/bash

# Install required Python packages
echo "Installing required Python packages..."
pip install pymilvus python-dotenv openai

# Create directories for Docker volumes
echo "Creating directories for Docker volumes..."
mkdir -p volumes/etcd
mkdir -p volumes/minio
mkdir -p volumes/milvus

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "Docker is not installed. Please install Docker and Docker Compose before running this script."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "Docker Compose is not installed. Please install Docker Compose before running this script."
    exit 1
fi

# Start Milvus using Docker Compose
echo "Starting Milvus using Docker Compose..."
docker-compose up -d

# Wait for <PERSON><PERSON><PERSON><PERSON> to start
echo "Waiting for Mi<PERSON><PERSON><PERSON> to start (this may take a minute)..."
sleep 30

# Check if Mil<PERSON><PERSON> is running
if docker ps | grep -q "milvus-standalone"; then
    echo "Milvus is now running!"
    echo "You can access the Milvus server at localhost:19530"
else
    echo "Failed to start Milvus. Please check the Docker logs for more information."
    echo "docker logs milvus-standalone"
    exit 1
fi

echo "Setup completed successfully!"
