#!/usr/bin/env python3
"""
Run tests for Deep Research Core.
"""

import os
import sys
import unittest
import argparse
import logging
from pathlib import Path

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def run_improvements_tests():
    """Run tests for improvements."""
    logger.info("Chạy các bài kiểm thử cho cải tiến...")

    # Danh sách các bài kiểm thử cần chạy
    test_names = [
        "TestQuestionComplexityEvaluator.test_simple_questions",
        "TestQuestionComplexityEvaluator.test_medium_questions",
        "TestQuestionComplexityEvaluator.test_complex_questions",
        "TestQuestionComplexityEvaluator.test_structure_analysis",
        "TestAdaptiveCrawler.test_filter_urls",
        "TestAdaptiveCrawler.test_crawl_with_timeout",
        "TestAdaptiveCrawler.test_crawl_with_different_complexity"
    ]

    # Tạo test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()

    # Chạy các bài kiểm thử cụ thể
    for test_name in test_names:
        try:
            tests = loader.loadTestsFromName(f"tests.test_improvements.{test_name}")
            suite.addTests(tests)
        except Exception as e:
            logger.error(f"Lỗi khi tải bài kiểm thử {test_name}: {str(e)}")

    # Chạy các bài kiểm thử
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # Tổng hợp kết quả
    logger.info("=" * 50)
    logger.info("KẾT QUẢ KIỂM THỬ CẢI TIẾN")
    logger.info("=" * 50)
    logger.info(f"Tổng số bài kiểm thử: {result.testsRun}")
    logger.info(f"Số bài kiểm thử thất bại: {len(result.failures)}")
    logger.info(f"Số bài kiểm thử lỗi: {len(result.errors)}")
    logger.info(f"Số bài kiểm thử bỏ qua: {len(result.skipped) if hasattr(result, 'skipped') else 0}")
    logger.info(f"Kết quả: {'THÀNH CÔNG' if result.wasSuccessful() else 'THẤT BẠI'}")
    logger.info("=" * 50)

    return 0 if result.wasSuccessful() else 1

def main():
    """Run tests."""
    parser = argparse.ArgumentParser(description="Run tests for Deep Research Core")
    parser.add_argument("--unit", action="store_true", help="Run unit tests only")
    parser.add_argument("--integration", action="store_true", help="Run integration tests only")
    parser.add_argument("--module", help="Run tests for a specific module (e.g., reasoning, retrieval)")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--improvements", action="store_true", help="Run tests for improvements")
    args = parser.parse_args()

    # Run improvements tests
    if args.improvements:
        return run_improvements_tests()

    # Determine test directory
    test_dir = "tests"
    if args.unit and not args.integration:
        test_dir = os.path.join("tests", "unit")
    elif args.integration and not args.unit:
        test_dir = os.path.join("tests", "integration")

    # Determine module directory
    if args.module:
        if args.unit:
            test_dir = os.path.join("tests", "unit", args.module)
        elif args.integration:
            test_dir = os.path.join("tests", "integration", args.module)
        else:
            unit_dir = os.path.join("tests", "unit", args.module)
            integration_dir = os.path.join("tests", "integration", args.module)

            # Check if both directories exist
            if os.path.exists(unit_dir) and os.path.exists(integration_dir):
                test_dir = [unit_dir, integration_dir]
            elif os.path.exists(unit_dir):
                test_dir = unit_dir
            elif os.path.exists(integration_dir):
                test_dir = integration_dir
            else:
                print(f"Module '{args.module}' not found in tests")
                return 1

    # Discover and run tests
    loader = unittest.TestLoader()

    if isinstance(test_dir, list):
        suite = unittest.TestSuite()
        for directory in test_dir:
            suite.addTests(loader.discover(directory))
    else:
        suite = loader.discover(test_dir)

    verbosity = 2 if args.verbose else 1
    runner = unittest.TextTestRunner(verbosity=verbosity)
    result = runner.run(suite)

    return 0 if result.wasSuccessful() else 1

if __name__ == "__main__":
    sys.exit(main())
