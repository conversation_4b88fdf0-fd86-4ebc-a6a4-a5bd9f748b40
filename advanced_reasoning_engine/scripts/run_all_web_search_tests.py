#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script để chạy tất cả các test cho WebSearchAgentLocal.
"""

import os
import sys
import time
import argparse
import subprocess
import logging

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def print_section(title):
    """In tiêu đề phần."""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, "="))
    print("=" * 80)

def run_test(test_file, args=None):
    """Chạy một file test."""
    if args is None:
        args = []

    cmd = [sys.executable, test_file] + args
    print_section(f"Chạy test: {os.path.basename(test_file)}")
    print(f"Lệnh: {' '.join(cmd)}")

    start_time = time.time()
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

    # In output theo thời gian thực
    for line in iter(process.stdout.readline, ''):
        print(line, end='')

    process.stdout.close()
    return_code = process.wait()
    end_time = time.time()

    # In stderr nếu có lỗi
    if return_code != 0:
        print("\nLỗi:")
        for line in iter(process.stderr.readline, ''):
            print(line, end='')

    print(f"\nHoàn thành trong {end_time - start_time:.2f} giây với mã trả về: {return_code}")
    return return_code == 0

def main():
    """Hàm chính."""
    parser = argparse.ArgumentParser(description="Chạy tất cả các test cho WebSearchAgentLocal")
    parser.add_argument("--comprehensive", action="store_true", help="Chạy test toàn diện")
    parser.add_argument("--advanced", action="store_true", help="Chạy test tính năng nâng cao")
    parser.add_argument("--error-handling", action="store_true", help="Chạy test xử lý lỗi")
    parser.add_argument("--performance", action="store_true", help="Chạy test hiệu suất")
    parser.add_argument("--large-scale", action="store_true", help="Chạy test với số lượng lớn truy vấn")
    parser.add_argument("--all", action="store_true", help="Chạy tất cả các test (không bao gồm large-scale)")
    parser.add_argument("--num-queries", type=int, default=10, help="Số lượng truy vấn cho test hiệu suất (mặc định: 10)")
    parser.add_argument("--num-workers", type=int, default=1, help="Số lượng worker cho test hiệu suất (mặc định: 1)")
    parser.add_argument("--delay", type=float, default=0.5, help="Độ trễ giữa các truy vấn (giây, mặc định: 0.5)")
    parser.add_argument("--output", type=str, help="File để lưu kết quả test large-scale (JSON)")

    args = parser.parse_args()

    # Nếu không có tham số, chạy tất cả các test
    if not (args.comprehensive or args.advanced or args.error_handling or args.performance or args.large_scale):
        args.all = True

    # Danh sách các test file
    test_files = []

    # Thêm các test file tương ứng
    if args.comprehensive or args.all:
        test_files.append(("test_web_search_agent_local_comprehensive.py", []))

    if args.advanced or args.all:
        test_files.append(("test_web_search_agent_local_advanced.py", []))

    if args.error_handling or args.all:
        test_files.append(("test_web_search_agent_local_error_handling.py", []))

    if args.performance or args.all:
        test_files.append(("test_web_search_agent_local_performance.py", [
            f"--num-queries={args.num_queries}",
            f"--num-workers={args.num_workers}",
            f"--delay={args.delay}"
        ]))

    if args.large_scale:
        test_files.append(("test_web_search_agent_local_large_scale.py", [
            f"--num-queries={args.num_queries}",
            f"--num-workers={args.num_workers}",
            f"--delay={args.delay}",
            f"--output={args.output}" if args.output else ""
        ]))

    # Chạy các test
    print_section("Chạy tất cả các test cho WebSearchAgentLocal")

    results = {}
    for test_file, test_args in test_files:
        test_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), test_file)
        if os.path.exists(test_path):
            success = run_test(test_path, test_args)
            results[test_file] = success
        else:
            print(f"Không tìm thấy file test: {test_path}")
            results[test_file] = False

    # In tổng kết
    print_section("Tổng kết")

    all_success = True
    for test_file, success in results.items():
        status = "✅ Thành công" if success else "❌ Thất bại"
        print(f"{test_file}: {status}")
        all_success = all_success and success

    if all_success:
        print("\n✅ Tất cả các test đều thành công!")
    else:
        print("\n❌ Có một số test thất bại!")
        sys.exit(1)

if __name__ == "__main__":
    main()
