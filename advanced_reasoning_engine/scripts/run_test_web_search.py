import sys
import os
import json
import logging
from pathlib import Path

sys.path.append('/home/<USER>/Desktop/automation-tool/deep_research_core')

from src.deep_research_core.agents.web_search_agent import WebSearchAgent

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

# Create test directory if it doesn't exist
test_dir = Path("test_results")
test_dir.mkdir(exist_ok=True)

def run_test(agent, query, test_name, force_method=None):
    """Run a test with the given query and save results."""
    logger.info(f"Running test: {test_name}")
    try:
        result = agent.search(query, force_method=force_method)
        success = result.get("success", False)
        logger.info(f"{test_name}: {'Success' if success else 'Failed'}")
        return success
    except Exception as e:
        logger.error(f"Error in {test_name}: {str(e)}")
        return False

def main():
    """Run all web search tests."""
    # Initialize agent
    agent = WebSearchAgent(verbose=True)
    
    # Test cases
    test_cases = {
        "simple_query": "Python programming language",
        "recent_info_query": "Latest news about artificial intelligence",
        "medium_query": "Cách làm bánh mì Việt Nam truyền thống",
        "academic_query": "Machine learning applications in healthcare 2023",
        "complex_query": "So sánh chi tiết giữa React, Angular và Vue.js cho phát triển ứng dụng web năm 2023",
        "vietnamese_query": "Cách chữa bệnh cảm cúm bằng thuốc nam",
        "location_query": "Hồ Gươm Hà Nội",
        "book_query": "Python programming books"
    }
    
    # Run tests
    results = {}
    for test_name, query in test_cases.items():
        force_method = "crawlee" if test_name in ["medium_query", "complex_query", "vietnamese_query"] else None
        results[test_name] = run_test(agent, query, test_name, force_method)
    
    # Test extract_content
    try:
        logger.info("Testing extract_content...")
        url = "https://en.wikipedia.org/wiki/Python_(programming_language)"
        content = agent.extract_content(url)
        
        # Save content to file
        with open(test_dir / "extract_content_result.json", "w", encoding="utf-8") as f:
            json.dump({
                "url": url,
                "title": content.get("title", ""),
                "language": content.get("language", ""),
                "content": content.get("content", "")[:10000]  # Truncate for readability
            }, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Tiêu đề: {content.get('title', '')}")
        logger.info(f"Ngôn ngữ: {content.get('language', '')}")
        logger.info(f"Độ dài nội dung: {len(content.get('content', ''))}")
        logger.info(f"Kết quả đã được lưu vào {test_dir}/extract_content_result.json")
        
        results["extract_content"] = True
    except Exception as e:
        logger.error(f"Error in extract_content: {str(e)}")
        results["extract_content"] = False
    
    # Print summary
    logger.info("=== BÁO CÁO TỔNG HỢP ===")
    for test_name, success in results.items():
        logger.info(f"{test_name}: {'Thành công' if success else 'Thất bại'}")

if __name__ == "__main__":
    main()
