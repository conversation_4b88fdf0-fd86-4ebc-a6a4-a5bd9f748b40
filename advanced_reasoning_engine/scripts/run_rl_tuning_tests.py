#!/usr/bin/env python3
"""
Run tests for the RL tuning module.

This script specifically runs the test modules for the RL tuning components.

Usage:
    python run_rl_tuning_tests.py [options]

Options:
    --verbose       Show verbose output
    --coverage      Generate coverage report
"""

import sys
import os
import argparse
import unittest

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Run RL tuning tests for Deep Research Core')
    parser.add_argument('--verbose', action='store_true', help='Show verbose output')
    parser.add_argument('--coverage', action='store_true', help='Generate coverage report')
    return parser.parse_args()

def run_tests():
    """Discover and run all RL tuning tests."""
    args = parse_arguments()
    
    # Determine verbosity level
    verbosity = 2 if args.verbose else 1
    
    if args.coverage:
        try:
            import coverage
            cov = coverage.Coverage(source=['src/deep_research_core/rl_tuning'])
            cov.start()
        except ImportError:
            print("Warning: coverage package not installed. Running without coverage.")
            args.coverage = False
    
    # Set up test suite
    test_loader = unittest.TestLoader()
    test_suite = test_loader.discover(
        start_dir='tests/unit/rl_tuning',
        pattern='test_*.py'
    )
    
    # Run the tests
    print("Running RL tuning tests...")
    result = unittest.TextTestRunner(verbosity=verbosity).run(test_suite)
    
    # Generate coverage report if requested
    if args.coverage:
        cov.stop()
        cov.save()
        print("\nCoverage Summary:")
        cov.report()
        
        # Generate HTML report
        html_dir = os.path.join(os.path.dirname(__file__), 'htmlcov_rl_tuning')
        cov.html_report(directory=html_dir)
        print(f"HTML coverage report saved to {html_dir}")
    
    # Print summary
    print(f"\nRan {result.testsRun} tests")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    # Exit with appropriate code
    if result.failures or result.errors:
        sys.exit(1)
    else:
        print("All tests passed!")
        sys.exit(0)

if __name__ == "__main__":
    run_tests() 