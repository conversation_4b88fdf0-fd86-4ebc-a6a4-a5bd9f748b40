#!/usr/bin/env python3
"""
Run all tests for the deep_research_core project.

This script discovers and runs all tests in the project,
including unit tests, integration tests, and comprehensive tests.
"""

import os
import sys
import unittest
import argparse
import logging
from typing import List, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

# Set API keys for testing
os.environ["OPENROUTER_API_KEY"] = "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"


def discover_tests(test_dir: str) -> unittest.TestSuite:
    """
    Discover tests in the specified directory.

    Args:
        test_dir: Directory to search for tests

    Returns:
        TestSuite containing all discovered tests
    """
    logger.info(f"Discovering tests in {test_dir}")
    return unittest.defaultTestLoader.discover(test_dir, pattern="test_*.py")


def run_tests(test_suite: unittest.TestSuite) -> unittest.TestResult:
    """
    Run the specified test suite.

    Args:
        test_suite: TestSuite to run

    Returns:
        TestResult containing the results of the tests
    """
    runner = unittest.TextTestRunner(verbosity=2)
    return runner.run(test_suite)


def run_specific_tests(test_names: List[str]) -> unittest.TestResult:
    """
    Run specific tests by name.

    Args:
        test_names: List of test names to run

    Returns:
        TestResult containing the results of the tests
    """
    suite = unittest.TestSuite()

    for test_name in test_names:
        try:
            # Try to load the test by name
            test = unittest.defaultTestLoader.loadTestsFromName(test_name)
            suite.addTest(test)
            logger.info(f"Added test: {test_name}")
        except (ImportError, AttributeError) as e:
            logger.error(f"Could not load test {test_name}: {str(e)}")

    return run_tests(suite)


def run_all_tests() -> unittest.TestResult:
    """
    Run all tests in the project.

    Returns:
        TestResult containing the results of all tests
    """
    # Create a test suite containing all tests
    suite = unittest.TestSuite()

    # Add tests from different directories
    test_dirs = [
        "tests/unit",
        "tests/integration",
        "tests/unit/rl_tuning",
        "tests/unit/reasoning",
        "tests"  # Thêm thư mục tests để tìm các test case mới
    ]

    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            suite.addTest(discover_tests(test_dir))

    # Add individual test files in the root directory
    root_tests = [
        "test_all_modules.py",
        "test_rl_modules.py",
        "test_web_search_agent_local.py",
        "test_web_search_agent_local_comprehensive.py",
        "test_web_search_agent_local_comprehensive_all.py",
        "test_web_search_agent_local_file_extraction.py",
        "test_web_search_agent_local_multimedia.py",
        "test_web_search_agent_local_improvements.py"
    ]

    for test_file in root_tests:
        if os.path.exists(test_file):
            module_name = os.path.splitext(test_file)[0]
            try:
                # Try to load the test module
                test = unittest.defaultTestLoader.loadTestsFromName(module_name)
                suite.addTest(test)
                logger.info(f"Added test: {module_name}")
            except (ImportError, AttributeError) as e:
                logger.error(f"Could not load test {module_name}: {str(e)}")

    return run_tests(suite)


def main():
    """Run the test suite based on command line arguments."""
    parser = argparse.ArgumentParser(description="Run tests for deep_research_core")
    parser.add_argument("--test", "-t", nargs="+", help="Specific tests to run")
    parser.add_argument("--dir", "-d", help="Directory to search for tests")
    args = parser.parse_args()

    if args.test:
        # Run specific tests
        logger.info(f"Running specific tests: {args.test}")
        result = run_specific_tests(args.test)
    elif args.dir:
        # Run tests in a specific directory
        logger.info(f"Running tests in directory: {args.dir}")
        suite = discover_tests(args.dir)
        result = run_tests(suite)
    else:
        # Run all tests
        logger.info("Running all tests")
        result = run_all_tests()

    # Print summary
    logger.info(f"Tests run: {result.testsRun}")
    logger.info(f"Errors: {len(result.errors)}")
    logger.info(f"Failures: {len(result.failures)}")

    # Exit with non-zero status if there were failures or errors
    if result.errors or result.failures:
        sys.exit(1)

    sys.exit(0)


if __name__ == "__main__":
    main()
