#!/usr/bin/env python3
"""
Run evaluation for Deep Research Core.
"""

import os
import argparse
import json
import time
from pathlib import Path
from typing import Dict, Any, List, Optional
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

def main():
    """Run evaluation."""
    parser = argparse.ArgumentParser(description="Run evaluation for Deep Research Core")
    parser.add_argument("--config", default="evaluation_config.json", help="Evaluation configuration file")
    parser.add_argument("--output", default="evaluation_results.json", help="Output file for evaluation results")
    parser.add_argument("--rag-type", default=os.getenv("RAG_TYPE", "sqlite"), help="RAG type to evaluate")
    parser.add_argument("--provider", default=os.getenv("PROVIDER", "openrouter"), help="Provider to use")
    parser.add_argument("--model", default=os.getenv("MODEL", "moonshotai/moonlight-16b-a3b-instruct:free"), help="Model to use")
    args = parser.parse_args()

    # Load evaluation configuration
    config_path = Path(args.config)
    if not config_path.exists():
        print(f"Configuration file {args.config} not found")
        return

    with open(config_path, "r", encoding="utf-8") as f:
        config = json.load(f)

    # Initialize RAG
    rag_type = args.rag_type.lower()
    if rag_type == "sqlite":
        from src.deep_research_core.reasoning import SQLiteVectorRAG
        rag = SQLiteVectorRAG(
            db_path=os.getenv("DB_PATH", "data/deep_research.db"),
            provider=args.provider,
            model=args.model,
            temperature=float(os.getenv("TEMPERATURE", "0.7")),
            max_tokens=int(os.getenv("MAX_TOKENS", "2000")),
            embedding_model=os.getenv("EMBEDDING_MODEL", "all-MiniLM-L6-v2"),
            top_k=int(os.getenv("TOP_K", "5"))
        )
    elif rag_type == "milvus":
        from src.deep_research_core.reasoning import MilvusRAG
        rag = MilvusRAG(
            provider=args.provider,
            model=args.model,
            temperature=float(os.getenv("TEMPERATURE", "0.7")),
            max_tokens=int(os.getenv("MAX_TOKENS", "2000")),
            embedding_model=os.getenv("EMBEDDING_MODEL", "all-MiniLM-L6-v2"),
            collection_name=os.getenv("MILVUS_COLLECTION", "deep_research"),
            connection_args={
                "host": os.getenv("MILVUS_HOST", "localhost"),
                "port": os.getenv("MILVUS_PORT", "19530")
            },
            embedding_dim=int(os.getenv("EMBEDDING_DIM", "1536")),
            top_k=int(os.getenv("TOP_K", "5"))
        )
    elif rag_type == "faiss":
        from src.deep_research_core.reasoning import FAISSRAG
        rag = FAISSRAG(
            provider=args.provider,
            model=args.model,
            temperature=float(os.getenv("TEMPERATURE", "0.7")),
            max_tokens=int(os.getenv("MAX_TOKENS", "2000")),
            embedding_model=os.getenv("EMBEDDING_MODEL", "all-MiniLM-L6-v2"),
            index_path=os.getenv("FAISS_INDEX_PATH", "data/faiss_index"),
            top_k=int(os.getenv("TOP_K", "5"))
        )
    else:
        print(f"Unsupported RAG type: {rag_type}")
        return

    # Load documents
    documents = config.get("documents", [])
    if documents:
        print(f"Adding {len(documents)} documents to RAG")
        rag.add_documents(documents)

    # Run evaluation
    results = []
    queries = config.get("queries", [])
    print(f"Running evaluation on {len(queries)} queries")

    for i, query in enumerate(queries):
        print(f"Processing query {i+1}/{len(queries)}: {query}")
        start_time = time.time()
        result = rag.process(query)
        end_time = time.time()

        results.append({
            "query": query,
            "answer": result["answer"],
            "documents": result["documents"],
            "processing_time": end_time - start_time
        })

    # Save results
    output_path = Path(args.output)
    output_path.parent.mkdir(parents=True, exist_ok=True)

    with open(output_path, "w", encoding="utf-8") as f:
        json.dump({
            "config": config,
            "results": results,
            "rag_type": rag_type,
            "provider": args.provider,
            "model": args.model,
            "timestamp": time.time()
        }, f, indent=2)

    print(f"Evaluation results saved to {args.output}")

if __name__ == "__main__":
    main()
