"""
Setup script for Advanced Reasoning Engine
"""

from setuptools import setup, find_packages
import os

# Read README file
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def read_requirements(filename):
    with open(os.path.join("requirements", filename), "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="advanced-reasoning-engine",
    version="1.0.0",
    author="Deep Research Core Team",
    author_email="<EMAIL>",
    description="Một hệ thống reasoning tiên tiến với khả năng tích hợp nhiều phương pháp suy luận và hỗ trợ tiếng Việt",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/deepresearch/advanced-reasoning-engine",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements("requirements.txt"),
    extras_require={
        "dev": read_requirements("requirements-dev.txt"),
        "full": read_requirements("requirements-full.txt"),
    },
    entry_points={
        "console_scripts": [
            "advanced-reasoning=deep_research_core.cli:main",
        ],
    },
    include_package_data=True,
    package_data={
        "deep_research_core": [
            "config/*.yml",
            "config/*.json",
            "templates/*.html",
            "static/*",
        ],
    },
    zip_safe=False,
    keywords="ai, reasoning, rag, vietnamese, nlp, machine-learning, deep-learning",
    project_urls={
        "Bug Reports": "https://github.com/deepresearch/advanced-reasoning-engine/issues",
        "Source": "https://github.com/deepresearch/advanced-reasoning-engine",
        "Documentation": "https://advanced-reasoning-engine.readthedocs.io/",
    },
)
