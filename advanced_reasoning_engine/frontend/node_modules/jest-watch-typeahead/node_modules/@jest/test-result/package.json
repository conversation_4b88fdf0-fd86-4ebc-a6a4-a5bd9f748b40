{"name": "@jest/test-result", "version": "28.1.3", "repository": {"type": "git", "url": "https://github.com/facebook/jest.git", "directory": "packages/jest-test-result"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/console": "^28.1.3", "@jest/types": "^28.1.3", "@types/istanbul-lib-coverage": "^2.0.0", "collect-v8-coverage": "^1.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "2cce069800dab3fc8ca7c469b32d2e2b2f7e2bb1"}