/**
 * Route optimization utilities for Deep Research Core UI
 */

/**
 * Tracks which routes have been visited
 */
const visitedRoutes = new Set<string>();

/**
 * Routes that have been preloaded
 */
const preloadedRoutes = new Set<string>();

/**
 * Map of route paths to their dynamic import functions
 */
type RouteModuleMap = Record<string, () => Promise<any>>;

/**
 * Register a route as visited
 * @param route - Route path
 */
export const markRouteVisited = (route: string): void => {
  visitedRoutes.add(route);
};

/**
 * Check if a route has been visited
 * @param route - Route path
 * @returns Whether the route has been visited
 */
export const hasRouteBeenVisited = (route: string): boolean => {
  return visitedRoutes.has(route);
};

/**
 * Preload a route module
 * @param route - Route path
 * @param routeModules - Map of route paths to dynamic import functions
 * @returns Promise that resolves when the route module is loaded
 */
export const preloadRoute = async (
  route: string,
  routeModules: RouteModuleMap
): Promise<void> => {
  if (preloadedRoutes.has(route) || !routeModules[route]) {
    return;
  }
  
  try {
    await routeModules[route]();
    preloadedRoutes.add(route);
    console.log(`[RouteOptimization] Preloaded route: ${route}`);
  } catch (error) {
    console.error(`[RouteOptimization] Failed to preload route: ${route}`, error);
  }
};

/**
 * Preload multiple routes in order of priority
 * @param routes - Array of route paths
 * @param routeModules - Map of route paths to dynamic import functions
 * @returns Promise that resolves when all routes are preloaded
 */
export const preloadRoutes = async (
  routes: string[],
  routeModules: RouteModuleMap
): Promise<void> => {
  // Preload routes sequentially to avoid overwhelming the browser
  for (const route of routes) {
    await preloadRoute(route, routeModules);
  }
};

/**
 * Preload routes based on the current route
 * @param currentRoute - Current route path
 * @param routeModules - Map of route paths to dynamic import functions
 * @param routeGraph - Graph of route connections (which routes are likely to be visited from the current route)
 * @returns Promise that resolves when related routes are preloaded
 */
export const preloadRelatedRoutes = async (
  currentRoute: string,
  routeModules: RouteModuleMap,
  routeGraph: Record<string, string[]>
): Promise<void> => {
  const relatedRoutes = routeGraph[currentRoute] || [];
  
  if (relatedRoutes.length === 0) {
    return;
  }
  
  console.log(`[RouteOptimization] Preloading related routes for: ${currentRoute}`);
  await preloadRoutes(relatedRoutes, routeModules);
};

/**
 * Helper to create a map of route paths to their dynamic import functions
 * @param routes - Map of route paths to dynamic import functions
 * @returns RouteModuleMap
 */
export const createRouteModuleMap = (
  routes: Record<string, () => Promise<any>>
): RouteModuleMap => {
  return { ...routes };
};

/**
 * Define a graph of related routes to preload
 * @param graph - Graph of route connections
 * @returns Record of route paths to arrays of related route paths
 */
export const defineRouteGraph = (
  graph: Record<string, string[]>
): Record<string, string[]> => {
  return { ...graph };
};

/**
 * Check if the user has a fast connection
 * @returns Whether the user has a fast connection
 */
export const hasGoodConnection = (): boolean => {
  if (typeof navigator === 'undefined' || !('connection' in navigator)) {
    return true; // Default to true if the Connection API is not available
  }
  
  // @ts-ignore - navigator.connection is not in the TypeScript types
  const connection = navigator.connection;
  
  if (!connection) {
    return true;
  }

  // Consider the connection 'good' if it's not slow 2G/3G or if saveData is not enabled
  return !(
    // @ts-ignore - connection.effectiveType is not in the TypeScript types
    (connection.effectiveType === 'slow-2g' || 
    // @ts-ignore - connection.effectiveType is not in the TypeScript types
    connection.effectiveType === '2g' ||
    // @ts-ignore - connection.saveData is not in the TypeScript types
    connection.saveData === true)
  );
};

/**
 * Decide whether to preload routes based on connection quality and battery status
 * @returns Promise that resolves to whether preloading should be performed
 */
export const shouldPreload = async (): Promise<boolean> => {
  // Check connection quality
  const goodConnection = hasGoodConnection();
  
  // Check battery status if available
  let batteryNotLow = true;
  if (typeof navigator !== 'undefined' && 'getBattery' in navigator) {
    try {
      // @ts-ignore - navigator.getBattery is not in the TypeScript types
      const battery = await navigator.getBattery();
      batteryNotLow = !battery.charging && battery.level <= 0.2 ? false : true;
    } catch (e) {
      // Ignore errors with the Battery API
    }
  }
  
  return goodConnection && batteryNotLow;
};

export default {
  markRouteVisited,
  hasRouteBeenVisited,
  preloadRoute,
  preloadRoutes,
  preloadRelatedRoutes,
  createRouteModuleMap,
  defineRouteGraph,
  hasGoodConnection,
  shouldPreload
}; 