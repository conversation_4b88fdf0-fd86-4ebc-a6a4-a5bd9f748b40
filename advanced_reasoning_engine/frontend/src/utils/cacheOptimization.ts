/**
 * Data caching utilities for Deep Research Core UI
 */

/**
 * Cache entry with expiration
 */
interface CacheEntry<T> {
  value: T;
  expiry: number | null; // Timestamp in milliseconds, null means no expiration
}

/**
 * Options for cache operations
 */
interface CacheOptions {
  ttl?: number; // Time to live in milliseconds
  prefix?: string; // Key prefix for namespacing
  storage?: 'localStorage' | 'sessionStorage' | 'memory'; // Storage type
}

/**
 * Default cache options
 */
const defaultCacheOptions: CacheOptions = {
  ttl: 3600000, // 1 hour
  prefix: 'deep_research_',
  storage: 'localStorage'
};

/**
 * In-memory cache store
 */
const memoryCache = new Map<string, CacheEntry<any>>();

/**
 * Check if storage is available in the browser
 * @param type - Storage type to check
 * @returns Whether the storage is available
 */
export const isStorageAvailable = (type: 'localStorage' | 'sessionStorage'): boolean => {
  try {
    const storage = window[type];
    const testKey = `__test_${Date.now()}`;
    storage.setItem(testKey, testKey);
    storage.removeItem(testKey);
    return true;
  } catch (e) {
    return false;
  }
};

/**
 * Get the appropriate storage object based on options
 * @param options - Cache options
 * @returns Storage object or null if storage is not available
 */
const getStorage = (options: CacheOptions = defaultCacheOptions): Storage | null => {
  const storageType = options.storage || defaultCacheOptions.storage;
  
  if (typeof window === 'undefined') {
    return null; // Server-side rendering
  }
  
  if (storageType === 'memory') {
    return null; // Will use memory cache
  }
  
  return isStorageAvailable(storageType) ? window[storageType] : null;
};

/**
 * Generate a cache key with prefix
 * @param key - Base key
 * @param options - Cache options
 * @returns Prefixed key
 */
const getCacheKey = (key: string, options: CacheOptions = defaultCacheOptions): string => {
  const prefix = options.prefix || defaultCacheOptions.prefix;
  return `${prefix}${key}`;
};

/**
 * Set a value in cache
 * @param key - Cache key
 * @param value - Value to cache
 * @param options - Cache options
 */
export const setCacheValue = <T>(
  key: string,
  value: T,
  options: CacheOptions = defaultCacheOptions
): void => {
  const cacheKey = getCacheKey(key, options);
  const ttl = options.ttl ?? defaultCacheOptions.ttl;
  const expiry = ttl ? Date.now() + ttl : null;
  
  const cacheEntry: CacheEntry<T> = { value, expiry };
  
  if (options.storage === 'memory' || !getStorage(options)) {
    // Use in-memory cache
    memoryCache.set(cacheKey, cacheEntry);
    return;
  }
  
  // Use browser storage
  try {
    const storage = getStorage(options);
    if (storage) {
      storage.setItem(cacheKey, JSON.stringify(cacheEntry));
    }
  } catch (error) {
    console.error('[CacheOptimization] Error setting cache value:', error);
    // Fall back to memory cache
    memoryCache.set(cacheKey, cacheEntry);
  }
};

/**
 * Get a value from cache
 * @param key - Cache key
 * @param options - Cache options
 * @returns Cached value or null if not found or expired
 */
export const getCacheValue = <T>(
  key: string,
  options: CacheOptions = defaultCacheOptions
): T | null => {
  const cacheKey = getCacheKey(key, options);
  
  // First check memory cache
  if (memoryCache.has(cacheKey)) {
    const entry = memoryCache.get(cacheKey) as CacheEntry<T>;
    
    // Check if expired
    if (entry.expiry && entry.expiry < Date.now()) {
      memoryCache.delete(cacheKey);
      return null;
    }
    
    return entry.value;
  }
  
  // Then check browser storage
  if (options.storage !== 'memory') {
    const storage = getStorage(options);
    if (storage) {
      const data = storage.getItem(cacheKey);
      
      if (data) {
        try {
          const entry = JSON.parse(data) as CacheEntry<T>;
          
          // Check if expired
          if (entry.expiry && entry.expiry < Date.now()) {
            storage.removeItem(cacheKey);
            return null;
          }
          
          return entry.value;
        } catch (error) {
          console.error('[CacheOptimization] Error parsing cache value:', error);
          return null;
        }
      }
    }
  }
  
  return null;
};

/**
 * Remove a value from cache
 * @param key - Cache key
 * @param options - Cache options
 */
export const removeCacheValue = (
  key: string,
  options: CacheOptions = defaultCacheOptions
): void => {
  const cacheKey = getCacheKey(key, options);
  
  // Remove from memory cache
  memoryCache.delete(cacheKey);
  
  // Remove from browser storage
  if (options.storage !== 'memory') {
    const storage = getStorage(options);
    if (storage) {
      storage.removeItem(cacheKey);
    }
  }
};

/**
 * Clear all cached values with a specific prefix
 * @param prefix - Key prefix (defaults to options.prefix)
 * @param options - Cache options
 */
export const clearCacheByPrefix = (
  prefix?: string,
  options: CacheOptions = defaultCacheOptions
): void => {
  const keyPrefix = prefix || options.prefix || defaultCacheOptions.prefix;
  
  // Clear from memory cache
  for (const key of memoryCache.keys()) {
    if (key.startsWith(keyPrefix)) {
      memoryCache.delete(key);
    }
  }
  
  // Clear from browser storage
  if (options.storage !== 'memory') {
    const storage = getStorage(options);
    if (storage) {
      for (let i = 0; i < storage.length; i++) {
        const key = storage.key(i);
        if (key && key.startsWith(keyPrefix)) {
          storage.removeItem(key);
        }
      }
    }
  }
};

/**
 * Clear all expired cache entries
 * @param options - Cache options
 * @returns Number of cleared entries
 */
export const clearExpiredCache = (options: CacheOptions = defaultCacheOptions): number => {
  let cleared = 0;
  
  // Clear from memory cache
  const now = Date.now();
  for (const [key, entry] of memoryCache.entries()) {
    if (entry.expiry && entry.expiry < now) {
      memoryCache.delete(key);
      cleared++;
    }
  }
  
  // Clear from browser storage
  if (options.storage !== 'memory') {
    const storage = getStorage(options);
    if (storage) {
      const keysToRemove: string[] = [];
      
      for (let i = 0; i < storage.length; i++) {
        const key = storage.key(i);
        if (key && key.startsWith(options.prefix || defaultCacheOptions.prefix!)) {
          try {
            const data = storage.getItem(key);
            if (data) {
              const entry = JSON.parse(data) as CacheEntry<any>;
              if (entry.expiry && entry.expiry < now) {
                keysToRemove.push(key);
              }
            }
          } catch (e) {
            // Ignore parsing errors
          }
        }
      }
      
      // Remove expired items
      keysToRemove.forEach(key => {
        storage.removeItem(key);
        cleared++;
      });
    }
  }
  
  return cleared;
};

/**
 * Cache a function result
 * @param fn - Function to cache
 * @param keyFn - Function to generate cache key from arguments
 * @param options - Cache options
 * @returns Cached function
 */
export function cacheFunction<T, Args extends any[]>(
  fn: (...args: Args) => T,
  keyFn: (...args: Args) => string,
  options: CacheOptions = defaultCacheOptions
): (...args: Args) => T {
  return (...args: Args): T => {
    const cacheKey = keyFn(...args);
    const cachedValue = getCacheValue<T>(cacheKey, options);
    
    if (cachedValue !== null) {
      return cachedValue;
    }
    
    const result = fn(...args);
    setCacheValue(cacheKey, result, options);
    return result;
  };
}

/**
 * Cache an async function result
 * @param fn - Async function to cache
 * @param keyFn - Function to generate cache key from arguments
 * @param options - Cache options
 * @returns Cached async function
 */
export function cacheAsyncFunction<T, Args extends any[]>(
  fn: (...args: Args) => Promise<T>,
  keyFn: (...args: Args) => string,
  options: CacheOptions = defaultCacheOptions
): (...args: Args) => Promise<T> {
  return async (...args: Args): Promise<T> => {
    const cacheKey = keyFn(...args);
    const cachedValue = getCacheValue<T>(cacheKey, options);
    
    if (cachedValue !== null) {
      return cachedValue;
    }
    
    const result = await fn(...args);
    setCacheValue(cacheKey, result, options);
    return result;
  };
}

export default {
  setCacheValue,
  getCacheValue,
  removeCacheValue,
  clearCacheByPrefix,
  clearExpiredCache,
  cacheFunction,
  cacheAsyncFunction,
  isStorageAvailable
}; 