/**
 * ResponsiveUtils.ts
 * Utilities to help with responsive design in the application
 */

import { useMediaQuery, useTheme } from '@mui/material';

/**
 * Custom hook that returns different values based on the current breakpoint
 * @param {T} defaultValue - The default value to return
 * @param {Partial<Record<'xs' | 'sm' | 'md' | 'lg' | 'xl', T>>} options - Breakpoint-specific values
 * @returns {T} - The appropriate value for the current breakpoint
 */
export function useResponsiveValue<T>(
  defaultValue: T,
  options: Partial<Record<'xs' | 'sm' | 'md' | 'lg' | 'xl', T>>
): T {
  const theme = useTheme();
  const isXs = useMediaQuery(theme.breakpoints.only('xs'));
  const isSm = useMediaQuery(theme.breakpoints.only('sm'));
  const isMd = useMediaQuery(theme.breakpoints.only('md'));
  const isLg = useMediaQuery(theme.breakpoints.only('lg'));
  const isXl = useMediaQuery(theme.breakpoints.only('xl'));

  if (isXs && options.xs !== undefined) return options.xs;
  if (isSm && options.sm !== undefined) return options.sm;
  if (isMd && options.md !== undefined) return options.md;
  if (isLg && options.lg !== undefined) return options.lg;
  if (isXl && options.xl !== undefined) return options.xl;

  return defaultValue;
}

/**
 * Custom hook that returns true if the current viewport is mobile (xs or sm)
 * @returns {boolean} - True if the current viewport is mobile
 */
export function useIsMobile(): boolean {
  const theme = useTheme();
  return useMediaQuery(theme.breakpoints.down('md'));
}

/**
 * Custom hook that returns true if the current viewport is tablet (md)
 * @returns {boolean} - True if the current viewport is tablet
 */
export function useIsTablet(): boolean {
  const theme = useTheme();
  return useMediaQuery(theme.breakpoints.only('md'));
}

/**
 * Custom hook that returns true if the current viewport is desktop (lg or xl)
 * @returns {boolean} - True if the current viewport is desktop
 */
export function useIsDesktop(): boolean {
  const theme = useTheme();
  return useMediaQuery(theme.breakpoints.up('lg'));
}

/**
 * Get the appropriate spacing value based on the current breakpoint
 * @param {Object} theme - The MUI theme object
 * @param {Object} options - The spacing options for each breakpoint
 * @returns {number} - The appropriate spacing value
 */
export const getResponsiveSpacing = (
  theme: any,
  options: { xs?: number; sm?: number; md?: number; lg?: number; xl?: number; default: number }
): number => {
  const { breakpoints } = theme;
  const { xs, sm, md, lg, xl, default: defaultValue } = options;

  if (window.innerWidth < breakpoints.values.sm && xs !== undefined) return xs;
  if (window.innerWidth < breakpoints.values.md && sm !== undefined) return sm;
  if (window.innerWidth < breakpoints.values.lg && md !== undefined) return md;
  if (window.innerWidth < breakpoints.values.xl && lg !== undefined) return lg;
  if (window.innerWidth >= breakpoints.values.xl && xl !== undefined) return xl;

  return defaultValue;
};

export default {
  useResponsiveValue,
  useIsMobile,
  useIsTablet,
  useIsDesktop,
  getResponsiveSpacing
}; 