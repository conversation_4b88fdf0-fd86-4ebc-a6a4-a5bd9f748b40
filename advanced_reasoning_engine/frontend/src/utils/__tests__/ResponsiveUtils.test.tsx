import { renderHook } from '@testing-library/react-hooks';
import { useResponsiveValue, useIsMobile, useIsTablet, useIsDesktop } from '../ResponsiveUtils';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import * as React from 'react';
import { useMediaQuery } from '@mui/material';

// Mock useMediaQuery from MUI
jest.mock('@mui/material', () => ({
  ...jest.requireActual('@mui/material'),
  useMediaQuery: jest.fn(),
}));

describe('ResponsiveUtils', () => {
  // Setup wrapper for our hooks
  const wrapper = ({ children }: { children: React.ReactNode }) => {
    const theme = createTheme();
    return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
  };

  beforeEach(() => {
    // Reset mocks before each test
    (useMediaQuery as jest.Mock).mockReset();
  });

  describe('useResponsiveValue', () => {
    test('returns default value when no breakpoint matches', () => {
      (useMediaQuery as jest.Mock).mockReturnValue(false);
      
      const { result } = renderHook(
        () => useResponsiveValue('default', { xs: 'xs', sm: 'sm', md: 'md', lg: 'lg', xl: 'xl' }),
        { wrapper }
      );
      
      expect(result.current).toBe('default');
    });

    test('returns xs value when xs breakpoint matches', () => {
      // Setup mock to return true only for xs breakpoint
      (useMediaQuery as jest.Mock).mockImplementation((query) => {
        return query.toString().includes('xs');
      });
      
      const { result } = renderHook(
        () => useResponsiveValue('default', { xs: 'xs', sm: 'sm', md: 'md', lg: 'lg', xl: 'xl' }),
        { wrapper }
      );
      
      expect(result.current).toBe('xs');
    });

    test('returns md value when md breakpoint matches', () => {
      // Setup mock to return true only for md breakpoint
      (useMediaQuery as jest.Mock).mockImplementation((query) => {
        return query.toString().includes('md');
      });
      
      const { result } = renderHook(
        () => useResponsiveValue('default', { xs: 'xs', sm: 'sm', md: 'md', lg: 'lg', xl: 'xl' }),
        { wrapper }
      );
      
      expect(result.current).toBe('md');
    });
  });

  describe('useIsMobile', () => {
    test('returns true when breakpoint is below md', () => {
      (useMediaQuery as jest.Mock).mockReturnValue(true);
      
      const { result } = renderHook(() => useIsMobile(), { wrapper });
      
      expect(result.current).toBe(true);
    });

    test('returns false when breakpoint is md or above', () => {
      (useMediaQuery as jest.Mock).mockReturnValue(false);
      
      const { result } = renderHook(() => useIsMobile(), { wrapper });
      
      expect(result.current).toBe(false);
    });
  });

  describe('useIsTablet', () => {
    test('returns true when breakpoint is exactly md', () => {
      (useMediaQuery as jest.Mock).mockReturnValue(true);
      
      const { result } = renderHook(() => useIsTablet(), { wrapper });
      
      expect(result.current).toBe(true);
    });

    test('returns false when breakpoint is not md', () => {
      (useMediaQuery as jest.Mock).mockReturnValue(false);
      
      const { result } = renderHook(() => useIsTablet(), { wrapper });
      
      expect(result.current).toBe(false);
    });
  });

  describe('useIsDesktop', () => {
    test('returns true when breakpoint is lg or above', () => {
      (useMediaQuery as jest.Mock).mockReturnValue(true);
      
      const { result } = renderHook(() => useIsDesktop(), { wrapper });
      
      expect(result.current).toBe(true);
    });

    test('returns false when breakpoint is below lg', () => {
      (useMediaQuery as jest.Mock).mockReturnValue(false);
      
      const { result } = renderHook(() => useIsDesktop(), { wrapper });
      
      expect(result.current).toBe(false);
    });
  });

  describe('getResponsiveSpacing', () => {
    beforeAll(() => {
      // Mock window.innerWidth
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 0, // Default value, will be modified in tests
      });
    });

    test('returns xs spacing when screen width is xs', () => {
      // Set innerWidth to xs breakpoint
      window.innerWidth = 300; // xs breakpoint
      
      const theme = {
        breakpoints: {
          values: {
            xs: 0,
            sm: 600,
            md: 960,
            lg: 1280,
            xl: 1920,
          },
        },
      };
      
      const result = getResponsiveSpacing(theme, {
        xs: 1,
        sm: 2,
        md: 3,
        lg: 4,
        xl: 5,
        default: 0,
      });
      
      expect(result).toBe(1);
    });

    test('returns default spacing when no matching breakpoint option exists', () => {
      // Set innerWidth to sm breakpoint, but without sm option
      window.innerWidth = 700; // sm breakpoint
      
      const theme = {
        breakpoints: {
          values: {
            xs: 0,
            sm: 600,
            md: 960,
            lg: 1280,
            xl: 1920,
          },
        },
      };
      
      const result = getResponsiveSpacing(theme, {
        xs: 1,
        // No sm option
        md: 3,
        lg: 4,
        xl: 5,
        default: 0,
      });
      
      expect(result).toBe(0);
    });
  });
}); 