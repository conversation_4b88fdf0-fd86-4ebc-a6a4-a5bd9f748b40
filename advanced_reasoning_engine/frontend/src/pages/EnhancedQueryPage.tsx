import React from 'react';
import { Container, Box, Typography } from '@mui/material';
import EnhancedQueryInterface from '../components/reasoning/EnhancedQueryInterface';

const EnhancedQueryPage: React.FC = () => {
  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Enhanced Query Interface
        </Typography>
        <Typography variant="body1" color="text.secondary" gutterBottom>
          Our improved interface for complex reasoning with templates, history, and more
        </Typography>
      </Box>
      
      <EnhancedQueryInterface />
    </Container>
  );
};

export default EnhancedQueryPage; 