import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Tabs,
  Tab,
  CircularProgress,
  Di<PERSON>r,
  <PERSON>,
  Alert
} from '@mui/material';
import CompareArrowsIcon from '@mui/icons-material/CompareArrows';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`model-tabpanel-${index}`}
      aria-labelledby={`model-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

interface ModelConfig {
  id: string;
  model: string;
  reasoningMethod: string;
}

interface ComparisonResult {
  modelName: string;
  reasoningMethod: string;
  response: string;
  responseTime: number;
  tokensUsed: number;
}

const reasoningMethods = [
  { value: 'tot', label: 'Tree of Thought' },
  { value: 'react', label: 'ReAct' },
  { value: 'cot', label: 'Chain of Thought' },
  { value: 'standard', label: 'Standard' }
];

const models = [
  { value: 'gpt4', label: 'OpenAI GPT-4' },
  { value: 'claude3', label: 'Anthropic Claude 3' },
  { value: 'gemini', label: 'Google Gemini' },
  { value: 'llama3', label: 'Meta Llama 3' },
  { value: 'deepseek', label: 'DeepSeek Coder' }
];

const ComparisonPage: React.FC = () => {
  const [query, setQuery] = useState('');
  const [activeTab, setActiveTab] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [comparisonResults, setComparisonResults] = useState<ComparisonResult[]>([]);
  
  const [modelConfigs, setModelConfigs] = useState<ModelConfig[]>([
    { id: '1', model: 'gpt4', reasoningMethod: 'tot' },
    { id: '2', model: 'claude3', reasoningMethod: 'tot' }
  ]);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleModelChange = (id: string, value: string) => {
    setModelConfigs(prev => 
      prev.map(config => 
        config.id === id ? { ...config, model: value } : config
      )
    );
  };

  const handleReasoningMethodChange = (id: string, value: string) => {
    setModelConfigs(prev => 
      prev.map(config => 
        config.id === id ? { ...config, reasoningMethod: value } : config
      )
    );
  };

  const addModelConfig = () => {
    const newId = (modelConfigs.length + 1).toString();
    setModelConfigs([...modelConfigs, { 
      id: newId, 
      model: 'gpt4', 
      reasoningMethod: 'tot' 
    }]);
  };

  const removeModelConfig = (id: string) => {
    if (modelConfigs.length <= 2) {
      return; // Keep at least 2 models for comparison
    }
    setModelConfigs(modelConfigs.filter(config => config.id !== id));
  };

  const runComparison = () => {
    if (!query.trim()) return;
    
    setIsLoading(true);
    setComparisonResults([]);
    
    // Simulate API calls to different models
    setTimeout(() => {
      const results: ComparisonResult[] = modelConfigs.map(config => {
        const modelName = models.find(m => m.value === config.model)?.label || config.model;
        const reasoningMethodName = reasoningMethods.find(r => r.value === config.reasoningMethod)?.label || config.reasoningMethod;
        
        return {
          modelName,
          reasoningMethod: reasoningMethodName,
          response: `This is a simulated response from ${modelName} using ${reasoningMethodName} reasoning. In a real implementation, this would be the actual response from the model with the specified reasoning method.`,
          responseTime: Math.floor(Math.random() * 4000) + 1000, // Random time between 1-5 seconds
          tokensUsed: Math.floor(Math.random() * 500) + 100 // Random tokens between 100-600
        };
      });
      
      setComparisonResults(results);
      setIsLoading(false);
    }, 3000);
  };

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Model Comparison
      </Typography>
      
      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <Box sx={{ mb: 3 }}>
          <TextField
            fullWidth
            multiline
            rows={3}
            label="Enter your query for comparison"
            variant="outlined"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="e.g., Compare the economic effects of AI in developed vs developing countries"
          />
        </Box>
        
        <Typography variant="h6" gutterBottom>
          Models to Compare
        </Typography>
        
        {modelConfigs.map((config, index) => (
          <Grid container spacing={2} key={config.id} sx={{ mb: 2 }}>
            <Grid item xs={12} sm={5}>
              <FormControl fullWidth>
                <InputLabel>Model {index + 1}</InputLabel>
                <Select
                  value={config.model}
                  label={`Model ${index + 1}`}
                  onChange={(e) => handleModelChange(config.id, e.target.value)}
                >
                  {models.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={5}>
              <FormControl fullWidth>
                <InputLabel>Reasoning Method</InputLabel>
                <Select
                  value={config.reasoningMethod}
                  label="Reasoning Method"
                  onChange={(e) => handleReasoningMethodChange(config.id, e.target.value)}
                >
                  {reasoningMethods.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={2} sx={{ display: 'flex', alignItems: 'center' }}>
              {modelConfigs.length > 2 && (
                <Button 
                  variant="outlined" 
                  color="error" 
                  size="small"
                  onClick={() => removeModelConfig(config.id)}
                >
                  Remove
                </Button>
              )}
            </Grid>
          </Grid>
        ))}
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
          <Button 
            variant="outlined" 
            onClick={addModelConfig}
            disabled={modelConfigs.length >= 5}
          >
            Add Model
          </Button>
          <Button 
            variant="contained" 
            color="primary" 
            endIcon={<CompareArrowsIcon />}
            onClick={runComparison}
            disabled={isLoading || !query.trim()}
          >
            {isLoading ? <CircularProgress size={24} /> : 'Run Comparison'}
          </Button>
        </Box>
      </Paper>
      
      {comparisonResults.length > 0 && (
        <Paper elevation={2} sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Comparison Results
          </Typography>
          
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={activeTab} onChange={handleTabChange} aria-label="model tabs">
              {comparisonResults.map((result, index) => (
                <Tab 
                  key={index} 
                  label={`${result.modelName} (${result.reasoningMethod})`}
                  id={`model-tab-${index}`}
                  aria-controls={`model-tabpanel-${index}`}
                />
              ))}
            </Tabs>
          </Box>
          
          {comparisonResults.map((result, index) => (
            <TabPanel key={index} value={activeTab} index={index}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                <Box>
                  <Chip 
                    label={result.modelName} 
                    color="primary" 
                    sx={{ mr: 1 }}
                  />
                  <Chip 
                    label={result.reasoningMethod} 
                    variant="outlined" 
                  />
                </Box>
                <Box>
                  <Typography variant="body2" color="text.secondary" component="span" sx={{ mr: 2 }}>
                    Response time: {(result.responseTime / 1000).toFixed(2)}s
                  </Typography>
                  <Typography variant="body2" color="text.secondary" component="span">
                    Tokens: {result.tokensUsed}
                  </Typography>
                </Box>
              </Box>
              
              <Divider sx={{ my: 2 }} />
              
              <Typography variant="body1">
                {result.response}
              </Typography>
            </TabPanel>
          ))}
          
          {comparisonResults.length > 1 && (
            <Box sx={{ mt: 4 }}>
              <Alert severity="info" icon={<CheckCircleOutlineIcon />}>
                <Typography variant="subtitle1">
                  Comparison Summary
                </Typography>
                <Typography variant="body2">
                  The fastest response was from {comparisonResults.reduce((prev, current) => 
                    prev.responseTime < current.responseTime ? prev : current).modelName} ({
                    (comparisonResults.reduce((prev, current) => 
                      prev.responseTime < current.responseTime ? prev : current).responseTime / 1000).toFixed(2)}s).
                </Typography>
                <Typography variant="body2">
                  The most token-efficient was {comparisonResults.reduce((prev, current) => 
                    prev.tokensUsed < current.tokensUsed ? prev : current).modelName} ({
                    comparisonResults.reduce((prev, current) => 
                      prev.tokensUsed < current.tokensUsed ? prev : current).tokensUsed} tokens).
                </Typography>
              </Alert>
            </Box>
          )}
        </Paper>
      )}
    </Box>
  );
};

export default ComparisonPage; 