import React, { useState } from 'react';
import {
  Box,
  Grid,
  Typography,
  Paper,
  Button,
  useTheme,
  useMediaQuery,
  Divider,
  Tabs,
  Tab,
  TextField
} from '@mui/material';
import { ContentCard } from '../components/common';
import { useIsMobile, useIsTablet, useIsDesktop } from '../utils/ResponsiveUtils';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`responsive-tabpanel-${index}`}
      aria-labelledby={`responsive-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const ResponsiveTestPage: React.FC = () => {
  const theme = useTheme();
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();
  const isDesktop = useIsDesktop();
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Box sx={{ p: { xs: 1, sm: 2, md: 3 } }}>
      <Typography 
        variant={isMobile ? "h5" : "h4"} 
        component="h1" 
        gutterBottom
        sx={{ mb: 3 }}
      >
        Responsive Design Testing
      </Typography>

      <Grid container spacing={isMobile ? 2 : 3}>
        {/* Current Breakpoint Information */}
        <Grid item xs={12}>
          <ContentCard title="Current Breakpoint" subtitle="Details about the current viewport">
            <Box sx={{ p: 2 }}>
              <Typography variant="body1" gutterBottom>
                Current theme breakpoint: 
                <Box component="span" sx={{ fontWeight: 'bold', ml: 1 }}>
                  {isMobile ? 'Mobile (xs, sm)' : isTablet ? 'Tablet (md)' : 'Desktop (lg, xl)'}
                </Box>
              </Typography>
              
              <Box sx={{ 
                display: 'flex', 
                justifyContent: 'space-between',
                flexDirection: { xs: 'column', sm: 'row' },
                gap: 2,
                mt: 2
              }}>
                <Paper 
                  elevation={3} 
                  sx={{ 
                    p: 2, 
                    flex: 1,
                    backgroundColor: isMobile ? theme.palette.primary.light : undefined,
                    color: isMobile ? theme.palette.primary.contrastText : undefined
                  }}
                >
                  <Typography variant="subtitle1" gutterBottom>Mobile View</Typography>
                  <Typography variant="body2">
                    {isMobile ? 'Currently active' : 'Not active'}
                  </Typography>
                </Paper>
                
                <Paper 
                  elevation={3} 
                  sx={{ 
                    p: 2, 
                    flex: 1,
                    backgroundColor: isTablet ? theme.palette.secondary.light : undefined,
                    color: isTablet ? theme.palette.secondary.contrastText : undefined
                  }}
                >
                  <Typography variant="subtitle1" gutterBottom>Tablet View</Typography>
                  <Typography variant="body2">
                    {isTablet ? 'Currently active' : 'Not active'}
                  </Typography>
                </Paper>
                
                <Paper 
                  elevation={3} 
                  sx={{ 
                    p: 2, 
                    flex: 1,
                    backgroundColor: isDesktop ? theme.palette.success.light : undefined,
                    color: isDesktop ? theme.palette.success.contrastText : undefined
                  }}
                >
                  <Typography variant="subtitle1" gutterBottom>Desktop View</Typography>
                  <Typography variant="body2">
                    {isDesktop ? 'Currently active' : 'Not active'}
                  </Typography>
                </Paper>
              </Box>
            </Box>
          </ContentCard>
        </Grid>

        {/* Responsive Layout Demo */}
        <Grid item xs={12} md={6}>
          <ContentCard 
            title="Responsive Layout" 
            subtitle="Different layouts based on screen size"
          >
            <Box sx={{ p: 2 }}>
              <Box sx={{ 
                display: 'flex', 
                flexDirection: { xs: 'column', md: 'row' },
                gap: 2
              }}>
                <Box 
                  sx={{ 
                    bgcolor: 'primary.main', 
                    color: 'primary.contrastText',
                    p: 2,
                    flex: 1,
                    borderRadius: 1,
                    textAlign: 'center'
                  }}
                >
                  <Typography>Box 1</Typography>
                </Box>
                
                <Box 
                  sx={{ 
                    bgcolor: 'secondary.main', 
                    color: 'secondary.contrastText',
                    p: 2,
                    flex: 1,
                    borderRadius: 1,
                    textAlign: 'center'
                  }}
                >
                  <Typography>Box 2</Typography>
                </Box>
                
                <Box 
                  sx={{ 
                    bgcolor: 'error.main', 
                    color: 'error.contrastText',
                    p: 2,
                    flex: 1,
                    borderRadius: 1,
                    textAlign: 'center',
                    display: { xs: 'none', sm: 'block' }
                  }}
                >
                  <Typography>Box 3 (hidden on xs)</Typography>
                </Box>
              </Box>
              
              <Typography variant="body2" sx={{ mt: 2 }}>
                {isMobile 
                  ? 'On mobile, these boxes stack vertically and the third box is hidden on extra-small screens.'
                  : 'On larger screens, these boxes align horizontally.'}
              </Typography>
            </Box>
          </ContentCard>
        </Grid>

        {/* Responsive Typography Demo */}
        <Grid item xs={12} md={6}>
          <ContentCard 
            title="Responsive Typography" 
            subtitle="Text sizes adapt to screen sizes"
          >
            <Box sx={{ p: 2 }}>
              <Typography 
                variant="h4" 
                sx={{ 
                  fontSize: { xs: '1.5rem', sm: '2rem', md: '2.5rem' },
                  mb: 2
                }}
              >
                Adaptive Title
              </Typography>
              
              <Typography 
                variant="body1" 
                sx={{ 
                  fontSize: { xs: '0.875rem', sm: '1rem', md: '1rem' },
                  mb: 1
                }}
              >
                This paragraph uses responsive font sizes that change based on the viewport width.
                On mobile, the text is smaller to fit better on small screens.
              </Typography>
              
              <Typography 
                variant="subtitle1" 
                sx={{ 
                  fontSize: { xs: '1rem', sm: '1.1rem', md: '1.2rem' },
                  fontWeight: 'bold',
                  mt: 2
                }}
              >
                Responsive subtitle
              </Typography>
            </Box>
          </ContentCard>
        </Grid>

        {/* Responsive Form Demo */}
        <Grid item xs={12}>
          <ContentCard 
            title="Responsive Form" 
            subtitle="Form layout adapts to screen size"
          >
            <Box sx={{ p: 2 }}>
              <Box 
                component="form" 
                sx={{
                  display: 'flex',
                  flexDirection: { xs: 'column', md: 'row' },
                  gap: 2,
                  alignItems: { md: 'flex-start' }
                }}
              >
                <Box sx={{ flex: 1 }}>
                  <TextField
                    label="First Name"
                    variant="outlined"
                    fullWidth
                    margin="normal"
                  />
                  <TextField
                    label="Last Name"
                    variant="outlined"
                    fullWidth
                    margin="normal"
                  />
                </Box>
                
                <Box sx={{ flex: 1 }}>
                  <TextField
                    label="Email Address"
                    variant="outlined"
                    fullWidth
                    margin="normal"
                  />
                  <TextField
                    label="Phone Number"
                    variant="outlined"
                    fullWidth
                    margin="normal"
                  />
                </Box>
                
                <Box sx={{ 
                  display: 'flex', 
                  flexDirection: 'column', 
                  justifyContent: 'flex-end',
                  alignItems: { xs: 'stretch', md: 'flex-end' },
                  alignSelf: { md: 'flex-end' },
                  mt: { xs: 2, md: 0 }
                }}>
                  <Button 
                    variant="contained" 
                    color="primary" 
                    size={isMobile ? "medium" : "large"}
                    fullWidth={isMobile}
                  >
                    Submit
                  </Button>
                </Box>
              </Box>
            </Box>
          </ContentCard>
        </Grid>

        {/* Responsive Tabs Demo */}
        <Grid item xs={12}>
          <ContentCard 
            title="Responsive Tabs" 
            subtitle="Tab layout changes on mobile devices"
          >
            <Box>
              <Tabs 
                value={tabValue} 
                onChange={handleTabChange} 
                variant={isMobile ? "fullWidth" : "standard"}
                centered={!isMobile}
                sx={{
                  borderBottom: 1,
                  borderColor: 'divider'
                }}
              >
                <Tab label="First Tab" />
                <Tab label="Second Tab" />
                <Tab label="Third Tab" />
                {!isMobile && <Tab label="Desktop Only" />}
              </Tabs>
              
              <TabPanel value={tabValue} index={0}>
                <Typography>
                  This is the content for the first tab. Notice how the tabs display changes
                  between mobile and desktop layouts.
                </Typography>
              </TabPanel>
              
              <TabPanel value={tabValue} index={1}>
                <Typography>
                  This is the content for the second tab. On mobile, tabs take up the full width,
                  while on desktop they're centered.
                </Typography>
              </TabPanel>
              
              <TabPanel value={tabValue} index={2}>
                <Typography>
                  This is the content for the third tab. You can navigate between tabs to 
                  see different content.
                </Typography>
              </TabPanel>
              
              {!isMobile && (
                <TabPanel value={tabValue} index={3}>
                  <Typography>
                    This tab is only visible on desktop devices. On mobile, this tab is hidden
                    to simplify the interface for smaller screens.
                  </Typography>
                </TabPanel>
              )}
            </Box>
          </ContentCard>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ResponsiveTestPage; 