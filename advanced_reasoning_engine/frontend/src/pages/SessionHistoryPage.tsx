import React, { useState } from 'react';
import { Container, Box, Typography, Grid, Paper } from '@mui/material';
import { SessionHistoryManager, DebugSessionInfo, DebugSession } from '../components/debugging';

const SessionHistoryPage: React.FC = () => {
  const [selectedSession, setSelectedSession] = useState<DebugSession | null>(null);

  const handleSessionSelect = (session: DebugSession) => {
    setSelectedSession(session);
  };

  return (
    <Container maxWidth="xl" sx={{ my: 3 }}>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          Debug Session History
        </Typography>
        <Typography variant="body1" color="text.secondary">
          View and manage past debugging sessions
        </Typography>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <SessionHistoryManager 
            onSessionSelect={handleSessionSelect}
            height="calc(100vh - 200px)"
          />
        </Grid>
        <Grid item xs={12} md={6}>
          {selectedSession ? (
            <DebugSessionInfo 
              session={selectedSession} 
              showRawData={true}
            />
          ) : (
            <Paper 
              elevation={2} 
              sx={{ 
                p: 3, 
                height: 'calc(100vh - 200px)', 
                display: 'flex', 
                justifyContent: 'center', 
                alignItems: 'center' 
              }}
            >
              <Typography color="text.secondary">
                Select a session to view details
              </Typography>
            </Paper>
          )}
        </Grid>
      </Grid>
    </Container>
  );
};

export default SessionHistoryPage; 