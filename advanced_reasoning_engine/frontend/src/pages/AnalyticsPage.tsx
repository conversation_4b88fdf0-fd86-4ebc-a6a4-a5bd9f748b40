import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Card,
  CardContent,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Divider
} from '@mui/material';
import {
  Equalizer as EqualizerIcon,
  Timeline as TimelineIcon,
  DonutLarge as DonutLargeIcon,
  BarChart as BarChartIcon,
  Assessment as AssessmentIcon
} from '@mui/icons-material';
import { ContentCard, LoadingIndicator } from '../components/common';

// Mock data for demonstration
const performanceData = {
  models: [
    { name: 'GPT-O1', accuracy: 0.85, latency: 580, cost: 0.009 },
    { name: 'Deepseek-R1', accuracy: 0.79, latency: 480, cost: 0.007 },
    { name: 'Claude 3', accuracy: 0.82, latency: 520, cost: 0.008 },
    { name: 'Gemini Pro', accuracy: 0.80, latency: 450, cost: 0.007 },
    { name: 'Llama 3', accuracy: 0.78, latency: 380, cost: 0.001 }
  ],
  reasoning: [
    { name: 'Tree of Thought', accuracy: 0.88, latency: 850, costMultiplier: 2.4 },
    { name: 'Chain of Thought', accuracy: 0.75, latency: 450, costMultiplier: 1.2 },
    { name: 'ReAct', accuracy: 0.83, latency: 680, costMultiplier: 1.8 },
    { name: 'Graph of Thought', accuracy: 0.85, latency: 780, costMultiplier: 2.1 },
    { name: 'Standard', accuracy: 0.68, latency: 320, costMultiplier: 1.0 }
  ],
  timeSeries: [
    { date: '2023-01', queries: 1243, totalLatency: 450_000, avgAccuracy: 0.72 },
    { date: '2023-02', queries: 1560, totalLatency: 590_000, avgAccuracy: 0.74 },
    { date: '2023-03', queries: 1890, totalLatency: 680_000, avgAccuracy: 0.75 },
    { date: '2023-04', queries: 2150, totalLatency: 720_000, avgAccuracy: 0.76 },
    { date: '2023-05', queries: 2580, totalLatency: 820_000, avgAccuracy: 0.78 },
    { date: '2023-06', queries: 3020, totalLatency: 950_000, avgAccuracy: 0.79 }
  ]
};

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const AnalyticsPage: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate data loading
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1500);
    
    return () => clearTimeout(timer);
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Analytics Dashboard
      </Typography>
      
      {loading ? (
        <LoadingIndicator message="Loading analytics data..." size="large" />
      ) : (
        <Box>
          {/* Performance Metrics Summary Cards */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Total Queries
                  </Typography>
                  <Typography variant="h4">12,443</Typography>
                  <Typography variant="body2" color="success.main" sx={{ mt: 1 }}>
                    +18.2% from last month
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Average Accuracy
                  </Typography>
                  <Typography variant="h4">80.2%</Typography>
                  <Typography variant="body2" color="success.main" sx={{ mt: 1 }}>
                    +2.5% from last month
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Average Latency
                  </Typography>
                  <Typography variant="h4">521ms</Typography>
                  <Typography variant="body2" color="error.main" sx={{ mt: 1 }}>
                    +12ms from last month
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Estimated Cost
                  </Typography>
                  <Typography variant="h4">$158.42</Typography>
                  <Typography variant="body2" color="error.main" sx={{ mt: 1 }}>
                    +21.3% from last month
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
          
          {/* Tabbed Content */}
          <Box sx={{ width: '100%' }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs 
                value={tabValue} 
                onChange={handleTabChange}
                variant="scrollable"
                scrollButtons="auto"
              >
                <Tab icon={<EqualizerIcon />} label="Model Performance" />
                <Tab icon={<TimelineIcon />} label="Reasoning Methods" />
                <Tab icon={<DonutLargeIcon />} label="Query Types" />
                <Tab icon={<BarChartIcon />} label="Usage Trends" />
                <Tab icon={<AssessmentIcon />} label="Detailed Reports" />
              </Tabs>
            </Box>
            
            {/* Model Performance Tab */}
            <TabPanel value={tabValue} index={0}>
              <ContentCard
                title="Model Performance Comparison"
                subtitle="Accuracy, latency, and cost comparison across different models"
                elevation={2}
              >
                <TableContainer component={Paper} elevation={0}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell><strong>Model</strong></TableCell>
                        <TableCell align="right"><strong>Accuracy</strong></TableCell>
                        <TableCell align="right"><strong>Avg. Latency (ms)</strong></TableCell>
                        <TableCell align="right"><strong>Cost ($/1K tokens)</strong></TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {performanceData.models.map((model) => (
                        <TableRow key={model.name}>
                          <TableCell component="th" scope="row">
                            {model.name}
                          </TableCell>
                          <TableCell align="right">{(model.accuracy * 100).toFixed(1)}%</TableCell>
                          <TableCell align="right">{model.latency}</TableCell>
                          <TableCell align="right">${model.cost.toFixed(3)}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </ContentCard>
              
              <Box mt={3}>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <ContentCard
                      title="Accuracy Comparison"
                      subtitle="Model accuracy on benchmark tasks"
                      elevation={2}
                    >
                      <Box
                        sx={{
                          height: 300,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          backgroundColor: 'background.default',
                          borderRadius: 1
                        }}
                      >
                        <Typography color="text.secondary">
                          Accuracy chart would be displayed here
                        </Typography>
                      </Box>
                    </ContentCard>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <ContentCard
                      title="Latency Comparison"
                      subtitle="Average response time by model"
                      elevation={2}
                    >
                      <Box
                        sx={{
                          height: 300,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          backgroundColor: 'background.default',
                          borderRadius: 1
                        }}
                      >
                        <Typography color="text.secondary">
                          Latency chart would be displayed here
                        </Typography>
                      </Box>
                    </ContentCard>
                  </Grid>
                </Grid>
              </Box>
            </TabPanel>
            
            {/* Reasoning Methods Tab */}
            <TabPanel value={tabValue} index={1}>
              <ContentCard
                title="Reasoning Methods Comparison"
                subtitle="Performance metrics for different reasoning strategies"
                elevation={2}
              >
                <TableContainer component={Paper} elevation={0}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell><strong>Method</strong></TableCell>
                        <TableCell align="right"><strong>Accuracy</strong></TableCell>
                        <TableCell align="right"><strong>Avg. Latency (ms)</strong></TableCell>
                        <TableCell align="right"><strong>Cost Multiplier</strong></TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {performanceData.reasoning.map((method) => (
                        <TableRow key={method.name}>
                          <TableCell component="th" scope="row">
                            {method.name}
                          </TableCell>
                          <TableCell align="right">{(method.accuracy * 100).toFixed(1)}%</TableCell>
                          <TableCell align="right">{method.latency}</TableCell>
                          <TableCell align="right">x{method.costMultiplier.toFixed(1)}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </ContentCard>
              
              <Box mt={3}>
                <ContentCard
                  title="Reasoning Performance by Task Type"
                  subtitle="Comparative effectiveness of reasoning methods across different tasks"
                  elevation={2}
                >
                  <Box
                    sx={{
                      height: 400,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: 'background.default',
                      borderRadius: 1
                    }}
                  >
                    <Typography color="text.secondary">
                      Reasoning effectiveness chart would be displayed here
                    </Typography>
                  </Box>
                </ContentCard>
              </Box>
            </TabPanel>
            
            {/* Query Types Tab */}
            <TabPanel value={tabValue} index={2}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <ContentCard
                    title="Query Distribution by Type"
                    subtitle="Breakdown of queries by category"
                    elevation={2}
                  >
                    <Box
                      sx={{
                        height: 350,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: 'background.default',
                        borderRadius: 1
                      }}
                    >
                      <Typography color="text.secondary">
                        Query distribution pie chart would be displayed here
                      </Typography>
                    </Box>
                  </ContentCard>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <ContentCard
                    title="Performance by Query Type"
                    subtitle="Accuracy and latency breakdown by query category"
                    elevation={2}
                  >
                    <Box
                      sx={{
                        height: 350,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: 'background.default',
                        borderRadius: 1
                      }}
                    >
                      <Typography color="text.secondary">
                        Performance by query type chart would be displayed here
                      </Typography>
                    </Box>
                  </ContentCard>
                </Grid>
              </Grid>
              
              <Box mt={3}>
                <ContentCard
                  title="Top Query Categories"
                  subtitle="Most frequent query categories and their performance metrics"
                  elevation={2}
                >
                  <TableContainer component={Paper} elevation={0}>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell><strong>Category</strong></TableCell>
                          <TableCell align="right"><strong>Query Count</strong></TableCell>
                          <TableCell align="right"><strong>Avg. Accuracy</strong></TableCell>
                          <TableCell align="right"><strong>Avg. Latency (ms)</strong></TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        <TableRow>
                          <TableCell>Research Questions</TableCell>
                          <TableCell align="right">2,345</TableCell>
                          <TableCell align="right">83.2%</TableCell>
                          <TableCell align="right">620</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>Fact-Based Questions</TableCell>
                          <TableCell align="right">1,890</TableCell>
                          <TableCell align="right">92.5%</TableCell>
                          <TableCell align="right">380</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>Programming Tasks</TableCell>
                          <TableCell align="right">1,420</TableCell>
                          <TableCell align="right">78.7%</TableCell>
                          <TableCell align="right">580</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>Explanation Requests</TableCell>
                          <TableCell align="right">1,320</TableCell>
                          <TableCell align="right">86.3%</TableCell>
                          <TableCell align="right">450</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>Creative Tasks</TableCell>
                          <TableCell align="right">980</TableCell>
                          <TableCell align="right">75.1%</TableCell>
                          <TableCell align="right">520</TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                </ContentCard>
              </Box>
            </TabPanel>
            
            {/* Usage Trends Tab */}
            <TabPanel value={tabValue} index={3}>
              <ContentCard
                title="Usage Trends Over Time"
                subtitle="Monthly query volume and performance metrics"
                elevation={2}
              >
                <Box
                  sx={{
                    height: 400,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: 'background.default',
                    borderRadius: 1
                  }}
                >
                  <Typography color="text.secondary">
                    Usage trends chart would be displayed here
                  </Typography>
                </Box>
              </ContentCard>
              
              <Box mt={3}>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <ContentCard
                      title="Monthly Query Volume"
                      subtitle="Number of queries processed per month"
                      elevation={2}
                    >
                      <TableContainer component={Paper} elevation={0}>
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell><strong>Month</strong></TableCell>
                              <TableCell align="right"><strong>Queries</strong></TableCell>
                              <TableCell align="right"><strong>Change</strong></TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {performanceData.timeSeries.map((item, index) => {
                              const prevItem = index > 0 ? performanceData.timeSeries[index - 1] : null;
                              const change = prevItem 
                                ? ((item.queries - prevItem.queries) / prevItem.queries * 100) 
                                : 0;
                              
                              return (
                                <TableRow key={item.date}>
                                  <TableCell>{item.date}</TableCell>
                                  <TableCell align="right">{item.queries.toLocaleString()}</TableCell>
                                  <TableCell 
                                    align="right"
                                    sx={{ 
                                      color: change > 0 ? 'success.main' : 
                                             change < 0 ? 'error.main' : 'text.secondary'
                                    }}
                                  >
                                    {change !== 0 ? `${change > 0 ? '+' : ''}${change.toFixed(1)}%` : '-'}
                                  </TableCell>
                                </TableRow>
                              );
                            })}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </ContentCard>
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <ContentCard
                      title="Performance Trends"
                      subtitle="Accuracy and latency trends over time"
                      elevation={2}
                    >
                      <TableContainer component={Paper} elevation={0}>
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell><strong>Month</strong></TableCell>
                              <TableCell align="right"><strong>Avg. Accuracy</strong></TableCell>
                              <TableCell align="right"><strong>Avg. Latency (ms)</strong></TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {performanceData.timeSeries.map((item) => (
                              <TableRow key={item.date}>
                                <TableCell>{item.date}</TableCell>
                                <TableCell align="right">{(item.avgAccuracy * 100).toFixed(1)}%</TableCell>
                                <TableCell align="right">{(item.totalLatency / item.queries).toFixed(0)}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </ContentCard>
                  </Grid>
                </Grid>
              </Box>
            </TabPanel>
            
            {/* Detailed Reports Tab */}
            <TabPanel value={tabValue} index={4}>
              <ContentCard
                title="Detailed Performance Reports"
                subtitle="Comprehensive analytics reports for in-depth analysis"
                elevation={2}
              >
                <Box p={2}>
                  <Typography variant="body1" paragraph>
                    The following reports are available for download or detailed viewing:
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6} md={4}>
                      <Paper 
                        variant="outlined" 
                        sx={{ 
                          p: 2, 
                          textAlign: 'center', 
                          cursor: 'pointer',
                          '&:hover': {
                            backgroundColor: 'background.default',
                          }
                        }}
                      >
                        <AssessmentIcon color="primary" sx={{ fontSize: 48, mb: 1 }} />
                        <Typography variant="subtitle1" gutterBottom>
                          Monthly Performance Report
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Comprehensive monthly analytics
                        </Typography>
                      </Paper>
                    </Grid>
                    
                    <Grid item xs={12} sm={6} md={4}>
                      <Paper 
                        variant="outlined" 
                        sx={{ 
                          p: 2, 
                          textAlign: 'center', 
                          cursor: 'pointer',
                          '&:hover': {
                            backgroundColor: 'background.default',
                          }
                        }}
                      >
                        <EqualizerIcon color="primary" sx={{ fontSize: 48, mb: 1 }} />
                        <Typography variant="subtitle1" gutterBottom>
                          Model Comparison Report
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Detailed model benchmarks
                        </Typography>
                      </Paper>
                    </Grid>
                    
                    <Grid item xs={12} sm={6} md={4}>
                      <Paper 
                        variant="outlined" 
                        sx={{ 
                          p: 2, 
                          textAlign: 'center', 
                          cursor: 'pointer',
                          '&:hover': {
                            backgroundColor: 'background.default',
                          }
                        }}
                      >
                        <TimelineIcon color="primary" sx={{ fontSize: 48, mb: 1 }} />
                        <Typography variant="subtitle1" gutterBottom>
                          Usage Trends Report
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Long-term usage patterns
                        </Typography>
                      </Paper>
                    </Grid>
                    
                    <Grid item xs={12} sm={6} md={4}>
                      <Paper 
                        variant="outlined" 
                        sx={{ 
                          p: 2, 
                          textAlign: 'center', 
                          cursor: 'pointer',
                          '&:hover': {
                            backgroundColor: 'background.default',
                          }
                        }}
                      >
                        <DonutLargeIcon color="primary" sx={{ fontSize: 48, mb: 1 }} />
                        <Typography variant="subtitle1" gutterBottom>
                          Query Analysis Report
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Detailed query type analysis
                        </Typography>
                      </Paper>
                    </Grid>
                    
                    <Grid item xs={12} sm={6} md={4}>
                      <Paper 
                        variant="outlined" 
                        sx={{ 
                          p: 2, 
                          textAlign: 'center', 
                          cursor: 'pointer',
                          '&:hover': {
                            backgroundColor: 'background.default',
                          }
                        }}
                      >
                        <BarChartIcon color="primary" sx={{ fontSize: 48, mb: 1 }} />
                        <Typography variant="subtitle1" gutterBottom>
                          Cost Analysis Report
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Detailed cost breakdown
                        </Typography>
                      </Paper>
                    </Grid>
                  </Grid>
                </Box>
              </ContentCard>
            </TabPanel>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default AnalyticsPage; 