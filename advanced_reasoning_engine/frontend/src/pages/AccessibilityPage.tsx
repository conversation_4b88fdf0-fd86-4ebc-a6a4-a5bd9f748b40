import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Divider,
  Slider,
  FormControlLabel,
  Switch,
  Grid,
  useTheme,
  Card,
  CardContent,
  Button
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import {
  TextFields,
  FormatLineSpacing,
  Contrast,
  HighlightAlt,
  Animation,
  VolumeUp,
  Keyboard,
  Restore
} from '@mui/icons-material';

const AccessibilityPage: React.FC = () => {
  const theme = useTheme();
  const { t } = useTranslation();
  
  // Font size settings
  const [fontSize, setFontSize] = useState<number>(100);
  const [lineSpacing, setLineSpacing] = useState<number>(1.5);
  
  // Contrast settings
  const [highContrast, setHighContrast] = useState<boolean>(false);
  const [focusHighlight, setFocusHighlight] = useState<boolean>(false);
  
  // Motion settings
  const [reduceMotion, setReduceMotion] = useState<boolean>(false);
  const [reduceAnimations, setReduceAnimations] = useState<boolean>(false);
  
  // Sound settings
  const [screenReader, setScreenReader] = useState<boolean>(false);
  const [soundFeedback, setSoundFeedback] = useState<boolean>(false);
  
  // Keyboard settings
  const [keyboardNavigation, setKeyboardNavigation] = useState<boolean>(false);
  const [stickyKeys, setStickyKeys] = useState<boolean>(false);
  
  // Reset all settings
  const handleResetSettings = () => {
    setFontSize(100);
    setLineSpacing(1.5);
    setHighContrast(false);
    setFocusHighlight(false);
    setReduceMotion(false);
    setReduceAnimations(false);
    setScreenReader(false);
    setSoundFeedback(false);
    setKeyboardNavigation(false);
    setStickyKeys(false);
    
    // Reset document styles
    document.documentElement.style.fontSize = '100%';
    document.body.style.lineHeight = '1.5';
    document.body.classList.remove('high-contrast', 'focus-highlight', 'reduce-motion', 'reduce-animations');
  };
  
  // Apply settings to document
  const applySettings = () => {
    // Apply font size
    document.documentElement.style.fontSize = `${fontSize}%`;
    
    // Apply line spacing
    document.body.style.lineHeight = `${lineSpacing}`;
    
    // Apply high contrast
    if (highContrast) {
      document.body.classList.add('high-contrast');
    } else {
      document.body.classList.remove('high-contrast');
    }
    
    // Apply focus highlight
    if (focusHighlight) {
      document.body.classList.add('focus-highlight');
    } else {
      document.body.classList.remove('focus-highlight');
    }
    
    // Apply reduce motion
    if (reduceMotion) {
      document.body.classList.add('reduce-motion');
    } else {
      document.body.classList.remove('reduce-motion');
    }
    
    // Apply reduce animations
    if (reduceAnimations) {
      document.body.classList.add('reduce-animations');
    } else {
      document.body.classList.remove('reduce-animations');
    }
  };
  
  // Apply settings when they change
  React.useEffect(() => {
    applySettings();
  }, [fontSize, lineSpacing, highContrast, focusHighlight, reduceMotion, reduceAnimations]);
  
  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto' }}>
      <Typography variant="h4" component="h1" gutterBottom>
        {t('accessibility.title')}
      </Typography>
      
      <Typography variant="body1" paragraph>
        {t('accessibility.description')}
      </Typography>
      
      <Paper sx={{ p: 3, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h5" component="h2">
            {t('accessibility.settings')}
          </Typography>
          
          <Button 
            startIcon={<Restore />}
            variant="outlined"
            onClick={handleResetSettings}
          >
            {t('accessibility.resetSettings')}
          </Button>
        </Box>
        
        <Divider sx={{ mb: 3 }} />
        
        <Grid container spacing={4}>
          {/* Text Settings */}
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <TextFields sx={{ mr: 1, color: theme.palette.primary.main }} />
                  <Typography variant="h6" component="h3">
                    {t('accessibility.textSettings')}
                  </Typography>
                </Box>
                
                <Typography variant="body2" sx={{ mb: 3 }}>
                  {t('accessibility.textSettingsDesc')}
                </Typography>
                
                <Box sx={{ mb: 3 }}>
                  <Typography gutterBottom>
                    {t('accessibility.fontSize')} ({fontSize}%)
                  </Typography>
                  <Slider
                    value={fontSize}
                    onChange={(_, newValue) => setFontSize(newValue as number)}
                    aria-label={t('accessibility.fontSize')}
                    valueLabelDisplay="auto"
                    min={80}
                    max={200}
                    marks={[
                      { value: 80, label: '80%' },
                      { value: 100, label: '100%' },
                      { value: 150, label: '150%' },
                      { value: 200, label: '200%' },
                    ]}
                  />
                </Box>
                
                <Box>
                  <Typography gutterBottom>
                    {t('accessibility.lineSpacing')} ({lineSpacing})
                  </Typography>
                  <Slider
                    value={lineSpacing}
                    onChange={(_, newValue) => setLineSpacing(newValue as number)}
                    aria-label={t('accessibility.lineSpacing')}
                    valueLabelDisplay="auto"
                    min={1}
                    max={3}
                    step={0.1}
                    marks={[
                      { value: 1, label: '1' },
                      { value: 1.5, label: '1.5' },
                      { value: 2, label: '2' },
                      { value: 3, label: '3' },
                    ]}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          {/* Visual Settings */}
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Contrast sx={{ mr: 1, color: theme.palette.primary.main }} />
                  <Typography variant="h6" component="h3">
                    {t('accessibility.visualSettings')}
                  </Typography>
                </Box>
                
                <Typography variant="body2" sx={{ mb: 3 }}>
                  {t('accessibility.visualSettingsDesc')}
                </Typography>
                
                <FormControlLabel
                  control={
                    <Switch 
                      checked={highContrast}
                      onChange={(e) => setHighContrast(e.target.checked)}
                    />
                  }
                  label={t('accessibility.highContrast')}
                  sx={{ display: 'block', mb: 2 }}
                />
                
                <FormControlLabel
                  control={
                    <Switch 
                      checked={focusHighlight}
                      onChange={(e) => setFocusHighlight(e.target.checked)}
                    />
                  }
                  label={t('accessibility.focusHighlight')}
                  sx={{ display: 'block', mb: 2 }}
                />
                
                <FormControlLabel
                  control={
                    <Switch 
                      checked={reduceMotion}
                      onChange={(e) => setReduceMotion(e.target.checked)}
                    />
                  }
                  label={t('accessibility.reduceMotion')}
                  sx={{ display: 'block', mb: 2 }}
                />
                
                <FormControlLabel
                  control={
                    <Switch 
                      checked={reduceAnimations}
                      onChange={(e) => setReduceAnimations(e.target.checked)}
                    />
                  }
                  label={t('accessibility.reduceAnimations')}
                  sx={{ display: 'block' }}
                />
              </CardContent>
            </Card>
          </Grid>
          
          {/* Audio Settings */}
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <VolumeUp sx={{ mr: 1, color: theme.palette.primary.main }} />
                  <Typography variant="h6" component="h3">
                    {t('accessibility.audioSettings')}
                  </Typography>
                </Box>
                
                <Typography variant="body2" sx={{ mb: 3 }}>
                  {t('accessibility.audioSettingsDesc')}
                </Typography>
                
                <FormControlLabel
                  control={
                    <Switch 
                      checked={screenReader}
                      onChange={(e) => setScreenReader(e.target.checked)}
                    />
                  }
                  label={t('accessibility.screenReader')}
                  sx={{ display: 'block', mb: 2 }}
                />
                
                <FormControlLabel
                  control={
                    <Switch 
                      checked={soundFeedback}
                      onChange={(e) => setSoundFeedback(e.target.checked)}
                    />
                  }
                  label={t('accessibility.soundFeedback')}
                  sx={{ display: 'block' }}
                />
              </CardContent>
            </Card>
          </Grid>
          
          {/* Keyboard Settings */}
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Keyboard sx={{ mr: 1, color: theme.palette.primary.main }} />
                  <Typography variant="h6" component="h3">
                    {t('accessibility.keyboardSettings')}
                  </Typography>
                </Box>
                
                <Typography variant="body2" sx={{ mb: 3 }}>
                  {t('accessibility.keyboardSettingsDesc')}
                </Typography>
                
                <FormControlLabel
                  control={
                    <Switch 
                      checked={keyboardNavigation}
                      onChange={(e) => setKeyboardNavigation(e.target.checked)}
                    />
                  }
                  label={t('accessibility.keyboardNavigation')}
                  sx={{ display: 'block', mb: 2 }}
                />
                
                <FormControlLabel
                  control={
                    <Switch 
                      checked={stickyKeys}
                      onChange={(e) => setStickyKeys(e.target.checked)}
                    />
                  }
                  label={t('accessibility.stickyKeys')}
                  sx={{ display: 'block' }}
                />
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

export default AccessibilityPage; 