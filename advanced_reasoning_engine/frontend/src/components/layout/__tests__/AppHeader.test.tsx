import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { MemoryRouter } from 'react-router-dom';
import AppHeader from '../AppHeader';

// Mock useMediaQuery hook
jest.mock('@mui/material/useMediaQuery', () => ({
  __esModule: true,
  default: jest.fn().mockReturnValue(false) // Default to desktop view
}));

describe('AppHeader Component', () => {
  const renderWithTheme = (ui: React.ReactElement) => {
    const theme = createTheme();
    return render(
      <ThemeProvider theme={theme}>
        <MemoryRouter>
          {ui}
        </MemoryRouter>
      </ThemeProvider>
    );
  };

  test('renders with correct title', () => {
    renderWithTheme(<AppHeader title="Test Title" />);
    expect(screen.getByText('Test Title')).toBeInTheDocument();
  });

  test('renders app logo', () => {
    renderWithTheme(<AppHeader title="Test Title" />);
    const logo = screen.getByAltText(/logo/i);
    expect(logo).toBeInTheDocument();
    expect(logo).toHaveAttribute('src');
  });

  test('contains navigation menu', () => {
    renderWithTheme(<AppHeader title="Test Title" />);
    expect(screen.getByRole('navigation')).toBeInTheDocument();
  });

  test('displays user menu', () => {
    renderWithTheme(<AppHeader title="Test Title" />);
    const userButton = screen.getByRole('button', { name: /account/i });
    expect(userButton).toBeInTheDocument();
    
    // Click to open user menu
    fireEvent.click(userButton);
    
    // Check menu items appear
    expect(screen.getByText(/profile/i)).toBeInTheDocument();
    expect(screen.getByText(/settings/i)).toBeInTheDocument();
    expect(screen.getByText(/logout/i)).toBeInTheDocument();
  });

  test('toggles dark mode', () => {
    // Mock theme toggle function
    const toggleDarkMode = jest.fn();
    
    renderWithTheme(
      <AppHeader 
        title="Test Title" 
        darkMode={false}
        toggleDarkMode={toggleDarkMode}
      />
    );
    
    // Find and click dark mode toggle
    const themeToggle = screen.getByRole('button', { name: /toggle dark mode/i });
    fireEvent.click(themeToggle);
    
    // Verify toggle function was called
    expect(toggleDarkMode).toHaveBeenCalledTimes(1);
  });

  test('shows correct theme icon based on current mode', () => {
    // Test light mode
    const { rerender } = renderWithTheme(
      <AppHeader title="Test Title" darkMode={false} />
    );
    
    expect(screen.getByTestId('LightModeIcon')).toBeInTheDocument();
    
    // Test dark mode
    rerender(
      <ThemeProvider theme={createTheme()}>
        <MemoryRouter>
          <AppHeader title="Test Title" darkMode={true} />
        </MemoryRouter>
      </ThemeProvider>
    );
    
    expect(screen.getByTestId('DarkModeIcon')).toBeInTheDocument();
  });
}); 