import React from 'react';
import { Box, Container, Typography, <PERSON>, Divider, useTheme } from '@mui/material';

const Footer: React.FC = () => {
  const theme = useTheme();
  const currentYear = new Date().getFullYear();

  return (
    <Box
      component="footer"
      sx={{
        py: 3,
        px: 2,
        mt: 'auto',
        backgroundColor: theme.palette.mode === 'light' 
          ? theme.palette.grey[100] 
          : theme.palette.grey[900],
      }}
    >
      <Divider />
      <Container maxWidth="lg" sx={{ pt: 3 }}>
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: 'space-between',
            alignItems: { xs: 'center', sm: 'flex-start' },
          }}
        >
          <Box sx={{ mb: { xs: 2, sm: 0 } }}>
            <Typography variant="h6" color="text.primary" gutterBottom>
              Deep Research Core
            </Typography>
            <Typography variant="body2" color="text.secondary">
              A comprehensive deep research system with advanced retrieval and reasoning capabilities.
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              © {currentYear} Quan Nguyen. All rights reserved.
            </Typography>
          </Box>

          <Box
            sx={{
              display: 'flex',
              flexDirection: { xs: 'column', sm: 'row' },
              gap: { xs: 2, sm: 4 },
              textAlign: { xs: 'center', sm: 'left' },
            }}
          >
            <Box>
              <Typography variant="subtitle1" color="text.primary" gutterBottom>
                Resources
              </Typography>
              <Box component="ul" sx={{ p: 0, m: 0, listStyle: 'none' }}>
                <Box component="li" sx={{ mb: 0.5 }}>
                  <Link href="/documentation" color="inherit" underline="hover">
                    Documentation
                  </Link>
                </Box>
                <Box component="li" sx={{ mb: 0.5 }}>
                  <Link href="/examples" color="inherit" underline="hover">
                    Examples
                  </Link>
                </Box>
                <Box component="li" sx={{ mb: 0.5 }}>
                  <Link href="/api-reference" color="inherit" underline="hover">
                    API Reference
                  </Link>
                </Box>
              </Box>
            </Box>

            <Box>
              <Typography variant="subtitle1" color="text.primary" gutterBottom>
                Legal
              </Typography>
              <Box component="ul" sx={{ p: 0, m: 0, listStyle: 'none' }}>
                <Box component="li" sx={{ mb: 0.5 }}>
                  <Link href="/privacy" color="inherit" underline="hover">
                    Privacy Policy
                  </Link>
                </Box>
                <Box component="li" sx={{ mb: 0.5 }}>
                  <Link href="/terms" color="inherit" underline="hover">
                    Terms of Service
                  </Link>
                </Box>
                <Box component="li" sx={{ mb: 0.5 }}>
                  <Link href="/license" color="inherit" underline="hover">
                    License
                  </Link>
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer; 