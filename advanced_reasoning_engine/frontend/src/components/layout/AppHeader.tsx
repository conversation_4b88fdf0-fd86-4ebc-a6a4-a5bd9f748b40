import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  IconButton,
  Box,
  useMediaQuery,
  useTheme as useMuiTheme,
  Button,
  Menu,
  MenuItem,
  Avatar,
  Badge,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import {
  Menu as MenuIcon,
  Notifications as NotificationsIcon,
  AccountCircle as AccountIcon,
  Close as CloseIcon,
  Settings as SettingsIcon,
  Logout as LogoutIcon,
  Accessibility as AccessibilityIcon
} from '@mui/icons-material';
import { useThemeMode } from '../../theme/ThemeContext';
import { Link } from 'react-router-dom';
import LanguageSwitcher from '../common/LanguageSwitcher';
import { ThemeToggle, AccessibilityMenu } from '../common';

interface AppHeaderProps {
  title: string;
  onMenuToggle?: () => void;
}

const AppHeader: React.FC<AppHeaderProps> = ({
  title,
  onMenuToggle
}) => {
  const muiTheme = useMuiTheme();
  const { mode, toggleTheme, isDark } = useThemeMode();
  const isMobile = useMediaQuery(muiTheme.breakpoints.down('md'));
  const isMobileSmall = useMediaQuery(muiTheme.breakpoints.down('sm'));
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [mobileMenuOpen, setMobileMenuOpen] = useState<boolean>(false);
  
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  
  const handleMenuClose = () => {
    setAnchorEl(null);
  };
  
  const handleMobileMenuToggle = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  // Mobile menu content
  const mobileMenuContent = (
    <Box sx={{ width: 250 }} role="presentation">
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between',
        p: 2,
        borderBottom: `1px solid ${muiTheme.palette.divider}`
      }}>
        <Typography variant="h6" component="div">
          Menu
        </Typography>
        <IconButton onClick={handleMobileMenuToggle}>
          <CloseIcon />
        </IconButton>
      </Box>
      
      <List>
        <ListItem button component={Link} to="/profile">
          <ListItemIcon>
            <AccountIcon />
          </ListItemIcon>
          <ListItemText primary="Profile" />
        </ListItem>
        <ListItem button component={Link} to="/settings">
          <ListItemIcon>
            <SettingsIcon />
          </ListItemIcon>
          <ListItemText primary="Settings" />
        </ListItem>
        <ListItem button onClick={toggleTheme}>
          <ListItemIcon>
            <ThemeToggle onToggle={toggleTheme} isDarkMode={isDark} />
          </ListItemIcon>
          <ListItemText primary={isDark ? "Light Mode" : "Dark Mode"} />
        </ListItem>
        <ListItem button component={Link} to="/accessibility">
          <ListItemIcon>
            <AccessibilityIcon />
          </ListItemIcon>
          <ListItemText primary="Accessibility" />
        </ListItem>
      </List>
      
      <Divider />
      
      <List>
        <ListItem button component={Link} to="/logout">
          <ListItemIcon>
            <LogoutIcon />
          </ListItemIcon>
          <ListItemText primary="Logout" />
        </ListItem>
      </List>
    </Box>
  );

  return (
    <AppBar 
      position="sticky" 
      elevation={1} 
      color="primary"
      sx={{
        zIndex: muiTheme.zIndex.drawer + 1
      }}
    >
      <Toolbar sx={{ px: isMobileSmall ? 1 : 2 }}>
        {isMobile && (
          <IconButton
            color="inherit"
            aria-label="open drawer"
            onClick={onMenuToggle}
            edge="start"
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>
        )}
        
        <Typography 
          variant={isMobileSmall ? "subtitle1" : "h6"} 
          component="div" 
          noWrap 
          sx={{ 
            flexGrow: 1,
            fontSize: isMobileSmall ? '1rem' : 'inherit'
          }}
        >
          {title}
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {/* Language Switcher */}
          <LanguageSwitcher variant={isMobileSmall ? "icon" : "text"} />
          
          {/* Theme Toggle - Hidden on small mobile */}
          {!isMobileSmall && (
            <Box sx={{ ml: 1 }}>
              <ThemeToggle onToggle={toggleTheme} isDarkMode={isDark} onDarkBackground />
            </Box>
          )}
          
          {/* Accessibility Menu - Hidden on small mobile */}
          {!isMobileSmall && (
            <Box sx={{ ml: 1 }}>
              <AccessibilityMenu onDarkBackground />
            </Box>
          )}
          
          {/* Notifications */}
          <IconButton color="inherit" sx={{ ml: isMobileSmall ? 0.5 : 1 }}>
            <Badge badgeContent={4} color="error">
              <NotificationsIcon fontSize={isMobileSmall ? "small" : "medium"} />
            </Badge>
          </IconButton>
          
          {/* User Menu */}
          {isMobileSmall ? (
            // Mobile version - Menu button that opens drawer
            <IconButton 
              color="inherit" 
              onClick={handleMobileMenuToggle}
              sx={{ ml: 0.5 }}
            >
              <AccountIcon fontSize="small" />
            </IconButton>
          ) : (
            // Desktop version - Avatar with dropdown
            <Box sx={{ ml: 2 }}>
              <Button
                color="inherit"
                onClick={handleMenuOpen}
                startIcon={
                  <Avatar sx={{ width: 32, height: 32, backgroundColor: 'secondary.main' }}>
                    <AccountIcon />
                  </Avatar>
                }
              >
                {!isMobile && "User"}
              </Button>
              <Menu
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleMenuClose}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'right',
                }}
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'right',
                }}
              >
                <MenuItem onClick={handleMenuClose}>Profile</MenuItem>
                <MenuItem onClick={handleMenuClose}>Settings</MenuItem>
                <MenuItem onClick={handleMenuClose}>Logout</MenuItem>
              </Menu>
            </Box>
          )}
        </Box>
      </Toolbar>
      
      {/* Mobile Menu Drawer */}
      <Drawer
        anchor="right"
        open={mobileMenuOpen}
        onClose={handleMobileMenuToggle}
      >
        {mobileMenuContent}
      </Drawer>
    </AppBar>
  );
};

export default AppHeader; 