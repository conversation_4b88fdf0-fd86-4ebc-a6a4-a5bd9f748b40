import React, { ReactNode, useState } from 'react';
import { Box, Container, CssBaseline, useMediaQuery, useTheme } from '@mui/material';
import AppHeader from './AppHeader';
import Sidebar from './Sidebar';
import Footer from './Footer';

interface MainLayoutProps {
  children: ReactNode;
  title?: string;
  showSidebar?: boolean;
}

const MainLayout: React.FC<MainLayoutProps> = ({ 
  children, 
  title = 'Deep Research Core', 
  showSidebar = true 
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mobileOpen, setMobileOpen] = useState(false);
  
  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };
  
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      <CssBaseline />
      
      {/* Header */}
      <AppHeader 
        title={title} 
        onMenuToggle={handleDrawerToggle}
      />
      
      {/* Main Content */}
      <Box sx={{ 
        display: 'flex', 
        flex: 1,
        flexDirection: { xs: 'column', md: 'row' }
      }}>
        {/* Sidebar */}
        {showSidebar && <Sidebar open={isMobile ? mobileOpen : true} onClose={handleDrawerToggle} />}
        
        {/* Page Content */}
        <Box component="main" sx={{ 
          flexGrow: 1, 
          p: 3,
          width: { md: `calc(100% - ${showSidebar ? '240px' : '0px'})` }
        }}>
          <Container maxWidth="xl" sx={{ mt: 2 }}>
            {children}
          </Container>
        </Box>
      </Box>
      
      {/* Footer */}
      <Footer />
    </Box>
  );
};

export default MainLayout; 