import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  TextField, 
  Button, 
  Select, 
  MenuItem, 
  FormControl, 
  InputLabel, 
  Paper, 
  Grid, 
  CircularProgress, 
  Divider,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Menu,
  FormControlLabel,
  Slider,
  Switch
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import SettingsIcon from '@mui/icons-material/Settings';
import BarChartIcon from '@mui/icons-material/BarChart';
import { apiService } from '../../api/apiService';

// Define types
interface QueryOptions {
  temperature: number;
  maxTokens: number;
  responseFormat: string;
  language: string;
}

interface QueryResult {
  answer: string;
  reasoningSteps?: Array<{
    id: string;
    content: string;
    type: string;
    confidence?: number;
  }>;
  metadata?: Record<string, any>;
}

const defaultOptions: QueryOptions = {
  temperature: 0.7,
  maxTokens: 1000,
  responseFormat: 'text',
  language: 'en',
};

const QueryInterface: React.FC = () => {
  // State variables
  const [query, setQuery] = useState('');
  const [model, setModel] = useState('GPT-O1');
  const [reasoningMethod, setReasoningMethod] = useState('ToT-RAG');
  const [options, setOptions] = useState<QueryOptions>(defaultOptions);
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<QueryResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showVisualization, setShowVisualization] = useState(false);
  
  // Options menu state
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [providers, setProviders] = useState<string[]>([]);
  
  // Fetch available providers on component mount
  React.useEffect(() => {
    const fetchProviders = async () => {
      try {
        const data = await apiService.getProviders();
        setProviders(data.providers);
        // Set default model to first provider if available
        if (data.providers.length > 0) {
          setModel(data.providers[0]);
        }
      } catch (error) {
        console.error('Failed to fetch providers:', error);
        setError('Failed to load available AI models');
      }
    };
    
    fetchProviders();
  }, []);

  // Handle query submission
  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setError(null);
    setIsLoading(true);
    setResult(null);
    
    try {
      const data = await apiService.processQuery(query, model, reasoningMethod, options);
      setResult(data);
      setShowVisualization(false);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle options menu open/close
  const handleOpenOptions = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleCloseOptions = () => {
    setAnchorEl(null);
  };

  // Handle option changes
  const handleOptionChange = (key: keyof QueryOptions, value: any) => {
    setOptions((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // Toggle visualization view
  const toggleVisualization = () => {
    setShowVisualization(!showVisualization);
  };

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ width: '100%' }}>
      <Grid container spacing={2}>
        {/* Model and Reasoning Method Selection */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
            <InputLabel>Model</InputLabel>
            <Select
              value={model}
              onChange={(e) => setModel(e.target.value)}
              label="Model"
            >
              {providers.map((provider) => (
                <MenuItem key={provider} value={provider}>
                  {provider}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
            <InputLabel>Reasoning Method</InputLabel>
            <Select
              value={reasoningMethod}
              onChange={(e) => setReasoningMethod(e.target.value)}
              label="Reasoning Method"
            >
              <MenuItem value="ToT-RAG">Tree of Thought (ToT-RAG)</MenuItem>
              <MenuItem value="RAG-TOT-COT">RAG-TOT-COT</MenuItem>
              <MenuItem value="ReAct">ReAct</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        
        {/* Query Input */}
        <Grid item xs={12}>
          <TextField
            fullWidth
            multiline
            rows={4}
            variant="outlined"
            label="Enter your query"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            sx={{ mb: 2 }}
          />
        </Grid>
        
        {/* Options and Submit */}
        <Grid item xs={6}>
          <Button 
            variant="outlined" 
            startIcon={<SettingsIcon />}
            onClick={handleOpenOptions}
          >
            Options
          </Button>
          
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleCloseOptions}
            sx={{ padding: 2, width: 320 }}
          >
            <Box sx={{ p: 2, width: 300 }}>
              <Typography variant="subtitle1" gutterBottom>
                Query Options
              </Typography>
              
              <FormControl fullWidth sx={{ mt: 2 }}>
                <Typography gutterBottom>Temperature: {options.temperature}</Typography>
                <Slider
                  value={options.temperature}
                  onChange={(_, value) => handleOptionChange('temperature', value)}
                  min={0}
                  max={1}
                  step={0.1}
                  valueLabelDisplay="auto"
                />
              </FormControl>
              
              <FormControl fullWidth sx={{ mt: 2 }}>
                <Typography gutterBottom>Max Tokens: {options.maxTokens}</Typography>
                <Slider
                  value={options.maxTokens}
                  onChange={(_, value) => handleOptionChange('maxTokens', value)}
                  min={100}
                  max={4000}
                  step={100}
                  valueLabelDisplay="auto"
                />
              </FormControl>
              
              <FormControl fullWidth sx={{ mt: 2 }}>
                <InputLabel>Response Format</InputLabel>
                <Select
                  value={options.responseFormat}
                  onChange={(e) => handleOptionChange('responseFormat', e.target.value)}
                  label="Response Format"
                  size="small"
                >
                  <MenuItem value="text">Text</MenuItem>
                  <MenuItem value="json">JSON</MenuItem>
                  <MenuItem value="structured">Structured</MenuItem>
                </Select>
              </FormControl>
              
              <FormControl fullWidth sx={{ mt: 2 }}>
                <InputLabel>Language</InputLabel>
                <Select
                  value={options.language}
                  onChange={(e) => handleOptionChange('language', e.target.value)}
                  label="Language"
                  size="small"
                >
                  <MenuItem value="en">English</MenuItem>
                  <MenuItem value="vi">Vietnamese</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </Menu>
        </Grid>
        
        <Grid item xs={6} sx={{ textAlign: 'right' }}>
          <Button 
            type="submit" 
            variant="contained" 
            color="primary"
            disabled={isLoading || !query.trim()}
          >
            {isLoading ? <CircularProgress size={24} color="inherit" /> : 'Submit'}
          </Button>
        </Grid>
        
        {/* Error message */}
        {error && (
          <Grid item xs={12}>
            <Paper sx={{ p: 2, bgcolor: 'error.light', color: 'error.contrastText' }}>
              <Typography>{error}</Typography>
            </Paper>
          </Grid>
        )}
        
        {/* Results Section */}
        {result && (
          <Grid item xs={12}>
            <Paper elevation={2} sx={{ p: 2, mt: 2 }}>
              <Typography variant="h6" gutterBottom>
                Results
              </Typography>
              <Typography paragraph sx={{ whiteSpace: 'pre-wrap' }}>
                {result.answer}
              </Typography>
            </Paper>
          </Grid>
        )}
        
        {/* Reasoning Process */}
        {result && result.reasoningSteps && (
          <Grid item xs={12}>
            <Paper elevation={2} sx={{ p: 2, mt: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6" gutterBottom>
                  Reasoning Process
                </Typography>
                <IconButton onClick={toggleVisualization}>
                  <BarChartIcon />
                </IconButton>
              </Box>
              
              {!showVisualization ? (
                <Box>
                  {result.reasoningSteps.map((step) => (
                    <Accordion key={step.id}>
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Typography>
                          {step.type} {step.confidence ? `(${(step.confidence * 100).toFixed(0)}%)` : ''}
                        </Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Typography sx={{ whiteSpace: 'pre-wrap' }}>
                          {step.content}
                        </Typography>
                      </AccordionDetails>
                    </Accordion>
                  ))}
                </Box>
              ) : (
                <Box sx={{ height: 400, p: 2 }}>
                  <Typography variant="body2" color="text.secondary" align="center">
                    Visualization would be rendered here using D3.js
                    <br />
                    (Placeholder for tree visualization component)
                  </Typography>
                </Box>
              )}
            </Paper>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default QueryInterface; 