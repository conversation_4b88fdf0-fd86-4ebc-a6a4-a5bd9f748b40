import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Grid,
  IconButton,
  Paper,
  Tab,
  Tabs,
  TextField,
  Typography,
  useTheme,
} from '@mui/material';
import {
  Close as CloseIcon,
  Delete as DeleteIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { Document, Collection } from '../../types';

interface DocumentDetailsProps {
  document: Document;
  collections: Collection[];
  open: boolean;
  onClose: () => void;
  onDelete: (id: string) => Promise<void>;
  onReprocess: (id: string) => Promise<void>;
  onUpdateTags: (id: string, tags: string[]) => Promise<void>;
  onMoveToCollection: (id: string, collectionId: string) => Promise<void>;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index, ...other }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`document-tabpanel-${index}`}
      aria-labelledby={`document-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const DocumentDetails: React.FC<DocumentDetailsProps> = ({
  document,
  collections,
  open,
  onClose,
  onDelete,
  onReprocess,
  onUpdateTags,
  onMoveToCollection,
}) => {
  const theme = useTheme();
  const [tabValue, setTabValue] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isReprocessing, setIsReprocessing] = useState(false);
  const [newTag, setNewTag] = useState('');
  const [tags, setTags] = useState<string[]>(document.tags || []);
  
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  const handleDelete = async () => {
    if (window.confirm(`Are you sure you want to delete "${document.name}"?`)) {
      setIsDeleting(true);
      try {
        await onDelete(document.id);
        onClose();
      } catch (error) {
        console.error('Error deleting document:', error);
      } finally {
        setIsDeleting(false);
      }
    }
  };
  
  const handleReprocess = async () => {
    setIsReprocessing(true);
    try {
      await onReprocess(document.id);
    } catch (error) {
      console.error('Error reprocessing document:', error);
    } finally {
      setIsReprocessing(false);
    }
  };
  
  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      const updatedTags = [...tags, newTag.trim()];
      setTags(updatedTags);
      setNewTag('');
      
      // Send update to server
      onUpdateTags(document.id, updatedTags).catch(error => {
        console.error('Error updating tags:', error);
      });
    }
  };
  
  const handleRemoveTag = (tagToRemove: string) => {
    const updatedTags = tags.filter(tag => tag !== tagToRemove);
    setTags(updatedTags);
    
    // Send update to server
    onUpdateTags(document.id, updatedTags).catch(error => {
      console.error('Error updating tags:', error);
    });
  };
  
  const handleTagInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };
  
  const handleMoveToCollection = (collectionId: string) => {
    onMoveToCollection(document.id, collectionId).catch(error => {
      console.error('Error moving document:', error);
    });
  };
  
  // Format file size for display
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
  };
  
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { height: '80vh' }
      }}
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6" component="div">
            {document.name}
          </Typography>
          <IconButton
            edge="end"
            color="inherit"
            onClick={onClose}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <Divider />
      
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="document tabs">
          <Tab label="Preview" />
          <Tab label="Metadata" />
          <Tab label="Tags" />
          <Tab label="Organization" />
        </Tabs>
      </Box>
      
      <DialogContent sx={{ p: 0 }}>
        <TabPanel value={tabValue} index={0}>
          <Box display="flex" flexDirection="column" height="100%">
            {/* Document Preview */}
            <Box sx={{ mb: 2, flex: 1, overflow: 'auto' }}>
              <Paper 
                variant="outlined" 
                sx={{ 
                  height: '100%', 
                  display: 'flex', 
                  flexDirection: 'column',
                  p: 2,
                  backgroundColor: 'rgba(0, 0, 0, 0.02)'
                }}
              >
                <Typography variant="subtitle2" gutterBottom>
                  Document Preview
                </Typography>
                <Box 
                  sx={{ 
                    flex: 1, 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'center' 
                  }}
                >
                  <Typography variant="body2" color="textSecondary">
                    {document.type.toUpperCase()} Preview Not Available
                  </Typography>
                </Box>
              </Paper>
            </Box>
            
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
              <Button 
                variant="outlined" 
                startIcon={<DownloadIcon />}
              >
                Download
              </Button>
              <Button 
                variant="outlined" 
                startIcon={<RefreshIcon />}
                onClick={handleReprocess}
                disabled={isReprocessing}
              >
                Re-process
              </Button>
              <Button 
                variant="outlined" 
                color="error"
                startIcon={<DeleteIcon />}
                onClick={handleDelete}
                disabled={isDeleting}
              >
                Delete
              </Button>
            </Box>
          </Box>
        </TabPanel>
        
        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>
                Basic Information
              </Typography>
              <Card variant="outlined">
                <CardContent>
                  <Grid container spacing={2}>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="textSecondary">
                        Name
                      </Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography variant="body2">
                        {document.name}
                      </Typography>
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="textSecondary">
                        Type
                      </Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography variant="body2">
                        {document.type.toUpperCase()}
                      </Typography>
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="textSecondary">
                        Size
                      </Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography variant="body2">
                        {formatFileSize(document.size)}
                      </Typography>
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="textSecondary">
                        Upload Date
                      </Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography variant="body2">
                        {new Date(document.upload_date).toLocaleString()}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>
                Indexing Information
              </Typography>
              <Card variant="outlined">
                <CardContent>
                  <Grid container spacing={2}>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="textSecondary">
                        Status
                      </Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography variant="body2">
                        {document.status 
                          ? document.status.charAt(0).toUpperCase() + document.status.slice(1)
                          : 'Unknown'}
                      </Typography>
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="textSecondary">
                        Chunks
                      </Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography variant="body2">
                        {document.chunks || 'N/A'}
                      </Typography>
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="textSecondary">
                        Vector Store
                      </Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography variant="body2">
                        {document.vector_store || 'FAISS'}
                      </Typography>
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="textSecondary">
                        Last Used
                      </Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography variant="body2">
                        {document.last_used 
                          ? new Date(document.last_used).toLocaleString()
                          : 'Never used'}
                      </Typography>
                    </Grid>
                    {document.last_query && (
                      <>
                        <Grid item xs={4}>
                          <Typography variant="body2" color="textSecondary">
                            Last Query
                          </Typography>
                        </Grid>
                        <Grid item xs={8}>
                          <Typography variant="body2">
                            "{document.last_query}"
                          </Typography>
                        </Grid>
                      </>
                    )}
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>
        
        <TabPanel value={tabValue} index={2}>
          <Box>
            <Typography variant="subtitle2" gutterBottom>
              Manage Tags
            </Typography>
            
            <Box sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
              <TextField
                label="Add Tag"
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyPress={handleTagInputKeyPress}
                size="small"
                sx={{ mr: 1 }}
              />
              <Button
                variant="contained"
                onClick={handleAddTag}
                disabled={!newTag.trim()}
              >
                Add
              </Button>
            </Box>
            
            <Typography variant="body2" gutterBottom>
              Current Tags:
            </Typography>
            
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
              {tags.length === 0 ? (
                <Typography variant="body2" color="textSecondary">
                  No tags assigned to this document
                </Typography>
              ) : (
                tags.map((tag) => (
                  <Chip
                    key={tag}
                    label={tag}
                    onDelete={() => handleRemoveTag(tag)}
                    color="primary"
                    variant="outlined"
                  />
                ))
              )}
            </Box>
          </Box>
        </TabPanel>
        
        <TabPanel value={tabValue} index={3}>
          <Box>
            <Typography variant="subtitle2" gutterBottom>
              Collection Assignment
            </Typography>
            
            <Typography variant="body2" sx={{ mb: 2 }}>
              Current Collection: 
              <span style={{ fontWeight: 'bold', marginLeft: '8px' }}>
                {collections.find(c => c.id === document.collection)?.name || 'Uncategorized'}
              </span>
            </Typography>
            
            <Typography variant="body2" gutterBottom>
              Move to Collection:
            </Typography>
            
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
              {collections.map((collection) => (
                <Chip
                  key={collection.id}
                  label={collection.name}
                  onClick={() => handleMoveToCollection(collection.id)}
                  color={collection.id === document.collection ? 'primary' : 'default'}
                  variant={collection.id === document.collection ? 'filled' : 'outlined'}
                  clickable={collection.id !== document.collection}
                />
              ))}
            </Box>
          </Box>
        </TabPanel>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};

export default DocumentDetails; 