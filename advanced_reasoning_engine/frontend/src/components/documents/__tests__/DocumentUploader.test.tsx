import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import DocumentUploader from '../DocumentUploader';

// Create a wrapper component that provides theme context
const Wrapper = ({ children }: { children: React.ReactNode }) => {
  const theme = createTheme();
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

// Mock data
const mockCollections = [
  { id: '1', name: 'Research Papers', documentCount: 5 },
  { id: '2', name: 'Financial Documents', documentCount: 3 },
];

// Mock functions
const mockOnUploadComplete = jest.fn();

describe('DocumentUploader Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders upload button correctly', () => {
    render(
      <Wrapper>
        <DocumentUploader collections={mockCollections} onUploadComplete={mockOnUploadComplete} />
      </Wrapper>
    );
    
    const uploadButton = screen.getByText(/upload documents/i);
    expect(uploadButton).toBeInTheDocument();
  });

  test('opens dialog when upload button is clicked', () => {
    render(
      <Wrapper>
        <DocumentUploader collections={mockCollections} onUploadComplete={mockOnUploadComplete} />
      </Wrapper>
    );
    
    // Dialog should not be visible initially
    expect(screen.queryByText(/upload new documents/i)).not.toBeInTheDocument();
    
    // Click upload button
    const uploadButton = screen.getByText(/upload documents/i);
    fireEvent.click(uploadButton);
    
    // Dialog should be visible
    expect(screen.getByText(/upload new documents/i)).toBeInTheDocument();
  });

  test('closes dialog when cancel button is clicked', () => {
    render(
      <Wrapper>
        <DocumentUploader collections={mockCollections} onUploadComplete={mockOnUploadComplete} />
      </Wrapper>
    );
    
    // Open dialog
    const uploadButton = screen.getByText(/upload documents/i);
    fireEvent.click(uploadButton);
    
    // Dialog should be visible
    expect(screen.getByText(/upload new documents/i)).toBeInTheDocument();
    
    // Click cancel button
    const cancelButton = screen.getByText(/cancel/i);
    fireEvent.click(cancelButton);
    
    // Dialog should not be visible
    waitFor(() => {
      expect(screen.queryByText(/upload new documents/i)).not.toBeInTheDocument();
    });
  });

  test('shows collection selector in the dialog', () => {
    render(
      <Wrapper>
        <DocumentUploader collections={mockCollections} onUploadComplete={mockOnUploadComplete} />
      </Wrapper>
    );
    
    // Open dialog
    const uploadButton = screen.getByText(/upload documents/i);
    fireEvent.click(uploadButton);
    
    // Check if collection selector is present
    expect(screen.getByLabelText(/collection/i)).toBeInTheDocument();
  });

  test('shows file input in the dialog', () => {
    render(
      <Wrapper>
        <DocumentUploader collections={mockCollections} onUploadComplete={mockOnUploadComplete} />
      </Wrapper>
    );
    
    // Open dialog
    const uploadButton = screen.getByText(/upload documents/i);
    fireEvent.click(uploadButton);
    
    // Check if file input is present
    const fileInput = screen.getByLabelText(/drag and drop files here/i, { selector: 'input' });
    expect(fileInput).toBeInTheDocument();
    expect(fileInput).toHaveAttribute('type', 'file');
    expect(fileInput).toHaveAttribute('multiple');
  });

  test('displays selected files when files are added', () => {
    render(
      <Wrapper>
        <DocumentUploader collections={mockCollections} onUploadComplete={mockOnUploadComplete} />
      </Wrapper>
    );
    
    // Open dialog
    const uploadButton = screen.getByText(/upload documents/i);
    fireEvent.click(uploadButton);
    
    // Mock file input change
    const fileInput = screen.getByLabelText(/drag and drop files here/i, { selector: 'input' });
    const file1 = new File(['test content'], 'test-file-1.pdf', { type: 'application/pdf' });
    const file2 = new File(['test content'], 'test-file-2.docx', { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });
    
    Object.defineProperty(fileInput, 'files', {
      value: [file1, file2],
    });
    
    fireEvent.change(fileInput);
    
    // Check if selected files are displayed
    waitFor(() => {
      expect(screen.getByText('test-file-1.pdf')).toBeInTheDocument();
      expect(screen.getByText('test-file-2.docx')).toBeInTheDocument();
    });
  });
}); 