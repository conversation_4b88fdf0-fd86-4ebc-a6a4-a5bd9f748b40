import React, { useState, useRef } from 'react';
import {
  Box,
  Button,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  Grid,
  InputLabel,
  LinearProgress,
  MenuItem,
  Select,
  Typography,
  SelectChangeEvent,
} from '@mui/material';
import { CloudUpload as CloudUploadIcon } from '@mui/icons-material';
import { useDocumentUpload } from '../../hooks/useApi';
import { Collection } from '../../types';
import { useTranslation } from 'react-i18next';
import { useToast } from '../../context/ToastContext';

interface DocumentUploaderProps {
  collections: Collection[];
  onUploadComplete: () => void;
}

const DocumentUploader: React.FC<DocumentUploaderProps> = ({ 
  collections,
  onUploadComplete 
}) => {
  const [open, setOpen] = useState(false);
  const [selectedCollection, setSelectedCollection] = useState<string>('');
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { t } = useTranslation();
  const { showToast } = useToast();
  
  const { mutateAsync: uploadDocuments } = useDocumentUpload();
  
  const handleClickOpen = () => {
    setOpen(true);
  };
  
  const handleClose = () => {
    if (!uploading) {
      setOpen(false);
      setSelectedFiles([]);
      setSelectedCollection('');
      setUploadProgress(0);
    }
  };
  
  const handleFileSelect = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };
  
  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      setSelectedFiles(Array.from(event.target.files));
    }
  };
  
  const handleCollectionChange = (event: SelectChangeEvent<string>) => {
    setSelectedCollection(event.target.value);
  };
  
  const handleUpload = async () => {
    if (selectedFiles.length === 0) return;
    
    setUploading(true);
    
    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          const newProgress = prev + 5;
          return newProgress >= 90 ? 90 : newProgress;
        });
      }, 300);
      
      // Upload the files
      await uploadDocuments(selectedFiles, selectedCollection || undefined);
      
      // Complete the progress
      clearInterval(progressInterval);
      setUploadProgress(100);
      
      // Show success notification
      showToast(t('documents.uploadSuccess'), 'success');
      
      // Notify parent about completion
      onUploadComplete();
      
      // Close dialog after a brief delay to show completion
      setTimeout(() => {
        handleClose();
      }, 1000);
    } catch (error) {
      console.error('Error uploading documents:', error);
      showToast(t('documents.uploadError'), 'error');
    } finally {
      setUploading(false);
    }
  };
  
  // Format file size for display
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
  };
  
  return (
    <>
      <Button 
        variant="contained" 
        startIcon={<CloudUploadIcon />}
        onClick={handleClickOpen}
      >
        {t('documents.uploadDocuments')}
      </Button>
      
      <Dialog 
        open={open} 
        onClose={handleClose}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>{t('documents.uploadDialogTitle')}</DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ mb: 3 }}>
            {t('documents.uploadDialogSubtitle')}
          </DialogContentText>
          
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Box 
                sx={{ 
                  border: '2px dashed #ccc', 
                  borderRadius: 1, 
                  p: 3, 
                  textAlign: 'center',
                  cursor: 'pointer',
                  '&:hover': {
                    borderColor: 'primary.main',
                    backgroundColor: 'rgba(0, 0, 0, 0.01)'
                  }
                }}
                onClick={handleFileSelect}
              >
                <input
                  type="file"
                  multiple
                  hidden
                  ref={fileInputRef}
                  onChange={handleFileInputChange}
                  accept=".pdf,.docx,.txt,.md,.csv"
                />
                
                {selectedFiles.length === 0 ? (
                  <>
                    <CloudUploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 1 }} />
                    <Typography variant="body1" gutterBottom>
                      {t('documents.dragAndDropFiles')}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      (PDF, DOCX, TXT, MD, CSV)
                    </Typography>
                  </>
                ) : (
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      {selectedFiles.length} {selectedFiles.length === 1 
                        ? t('documents.file') 
                        : t('documents.files')} {t('documents.selected')}
                    </Typography>
                    
                    <Box sx={{ mt: 2, maxHeight: 200, overflow: 'auto', textAlign: 'left' }}>
                      {selectedFiles.map((file, index) => (
                        <Box 
                          key={index} 
                          sx={{ 
                            display: 'flex', 
                            justifyContent: 'space-between', 
                            p: 1, 
                            borderBottom: '1px solid #eee'
                          }}
                        >
                          <Typography variant="body2" noWrap sx={{ maxWidth: '70%' }}>
                            {file.name}
                          </Typography>
                          <Typography variant="body2" color="textSecondary">
                            {formatFileSize(file.size)}
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  </Box>
                )}
              </Box>
            </Grid>
            
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel id="collection-select-label">{t('documents.collection')}</InputLabel>
                <Select
                  labelId="collection-select-label"
                  id="collection-select"
                  value={selectedCollection}
                  label={t('documents.collection')}
                  onChange={handleCollectionChange}
                  disabled={uploading}
                >
                  <MenuItem value="">
                    <em>{t('documents.uncategorized')}</em>
                  </MenuItem>
                  {collections.map((collection) => (
                    <MenuItem key={collection.id} value={collection.id}>
                      {collection.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            {uploading && (
              <Grid item xs={12}>
                <Box sx={{ width: '100%' }}>
                  <LinearProgress variant="determinate" value={uploadProgress} />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                    <Typography variant="body2" color="textSecondary">
                      {t('documents.processingDocument')}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      {uploadProgress}%
                    </Typography>
                  </Box>
                </Box>
              </Grid>
            )}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} disabled={uploading}>
            {t('common.cancel')}
          </Button>
          <Button 
            onClick={handleUpload} 
            disabled={selectedFiles.length === 0 || uploading}
            variant="contained"
          >
            {t('common.upload')}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default DocumentUploader; 