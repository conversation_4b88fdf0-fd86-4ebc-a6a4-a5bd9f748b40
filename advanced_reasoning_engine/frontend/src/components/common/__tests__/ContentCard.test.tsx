import React from 'react';
import { render, screen } from '@testing-library/react';
import ContentCard from '../ContentCard';
import { ThemeProvider, createTheme } from '@mui/material/styles';

// Create a wrapper component that provides the necessary context
const Wrapper = ({ children }: { children: React.ReactNode }) => {
  const theme = createTheme();
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('ContentCard', () => {
  test('renders title and subtitle', () => {
    render(
      <Wrapper>
        <ContentCard title="Test Title" subtitle="Test Subtitle">
          <div>Content</div>
        </ContentCard>
      </Wrapper>
    );
    
    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.getByText('Test Subtitle')).toBeInTheDocument();
  });

  test('renders loading state', () => {
    render(
      <Wrapper>
        <ContentCard title="Test Title" loading={true}>
          <div>Content</div>
        </ContentCard>
      </Wrapper>
    );
    
    // In loading state, the content is not rendered and skeletons are shown instead
    expect(screen.queryByText('Content')).not.toBeInTheDocument();
  });

  test('renders with custom elevation', () => {
    const { container } = render(
      <Wrapper>
        <ContentCard title="Test Title" elevation={5}>
          <div>Content</div>
        </ContentCard>
      </Wrapper>
    );
    
    // Check that the elevation attribute is applied (indirect test through class)
    const card = container.querySelector('.MuiPaper-elevation5');
    expect(card).toBeInTheDocument();
  });

  test('renders with full height', () => {
    const { container } = render(
      <Wrapper>
        <ContentCard title="Test Title" fullHeight={true}>
          <div>Content</div>
        </ContentCard>
      </Wrapper>
    );
    
    // Verify the card has height: 100% style
    const card = container.querySelector('.MuiCard-root');
    expect(card).toHaveStyle('height: 100%');
  });

  test('renders with actions', () => {
    render(
      <Wrapper>
        <ContentCard 
          title="Test Title" 
          actions={<button>Action Button</button>}
        >
          <div>Content</div>
        </ContentCard>
      </Wrapper>
    );
    
    expect(screen.getByText('Action Button')).toBeInTheDocument();
  });

  test('renders with custom header action', () => {
    render(
      <Wrapper>
        <ContentCard 
          title="Test Title" 
          headerAction={<button>Custom Header Action</button>}
        >
          <div>Content</div>
        </ContentCard>
      </Wrapper>
    );
    
    expect(screen.getByText('Custom Header Action')).toBeInTheDocument();
  });

  test('applies no padding when noPadding is true', () => {
    const { container } = render(
      <Wrapper>
        <ContentCard title="Test Title" noPadding={true}>
          <div>Content</div>
        </ContentCard>
      </Wrapper>
    );
    
    // Find CardContent component and check it has padding: 0
    const cardContent = container.querySelector('.MuiCardContent-root');
    expect(cardContent).toHaveStyle('padding: 0');
  });
}); 