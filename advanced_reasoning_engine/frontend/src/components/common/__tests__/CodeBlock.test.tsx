import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CodeBlock from '../CodeBlock';

// Create a wrapper component that provides theme context
const Wrapper = ({ children }: { children: React.ReactNode }) => {
  const theme = createTheme();
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: jest.fn(),
  },
});

describe('CodeBlock Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders code block with default props', () => {
    const code = 'const hello = "world";';
    render(
      <Wrapper>
        <CodeBlock code={code} />
      </Wrapper>
    );
    
    expect(screen.getByText(code)).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument(); // Line number
  });

  test('renders code block with title and language', () => {
    render(
      <Wrapper>
        <CodeBlock 
          code="function test() {}" 
          title="Example Code" 
          language="javascript" 
        />
      </Wrapper>
    );
    
    expect(screen.getByText('Example Code')).toBeInTheDocument();
    expect(screen.getByText('javascript')).toBeInTheDocument();
  });

  test('copies code to clipboard when copy button is clicked', async () => {
    const code = 'const data = { key: "value" };';
    render(
      <Wrapper>
        <CodeBlock code={code} title="JSON Example" />
      </Wrapper>
    );
    
    const copyButton = screen.getByLabelText('copy code');
    fireEvent.click(copyButton);
    
    expect(navigator.clipboard.writeText).toHaveBeenCalledWith(code);
    
    // Success message should appear
    await waitFor(() => {
      expect(screen.getByText('Code copied to clipboard')).toBeInTheDocument();
    });
  });

  test('renders multiline code with correct line numbers', () => {
    const multilineCode = `line 1
line 2
line 3`;
    
    render(
      <Wrapper>
        <CodeBlock code={multilineCode} />
      </Wrapper>
    );
    
    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument();
    expect(screen.getByText(multilineCode)).toBeInTheDocument();
  });

  test('handles empty code gracefully', () => {
    render(
      <Wrapper>
        <CodeBlock code="" title="Empty Code" />
      </Wrapper>
    );
    
    expect(screen.getByText('Empty Code')).toBeInTheDocument();
  });

  test('does not show line numbers when disabled', () => {
    render(
      <Wrapper>
        <CodeBlock 
          code="console.log('test');" 
          showLineNumbers={false} 
        />
      </Wrapper>
    );
    
    // Line number '1' should not be present
    expect(screen.queryByText('1')).not.toBeInTheDocument();
  });

  test('applies max height when specified', () => {
    render(
      <Wrapper>
        <CodeBlock 
          code="const test = true;" 
          maxHeight={200} 
          data-testid="code-block"
        />
      </Wrapper>
    );
    
    // Find the scrollable container
    const codeContainer = screen.getByTestId('code-block');
    expect(codeContainer).toHaveStyle('max-height: 200px');
  });

  test('applies custom font size', () => {
    render(
      <Wrapper>
        <CodeBlock 
          code="const test = true;" 
          fontSize="12px" 
          data-testid="code-block"
        />
      </Wrapper>
    );
    
    // Find elements with the font size
    const codeContainer = screen.getByTestId('code-block');
    expect(codeContainer).toHaveStyle('font-size: 12px');
  });
}); 