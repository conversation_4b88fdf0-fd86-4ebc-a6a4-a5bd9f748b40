import React from 'react';
import { render, screen, fireEvent, act, waitFor } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import SearchInput from '../SearchInput';

// Create a wrapper component that provides the necessary context
const Wrapper = ({ children }: { children: React.ReactNode }) => {
  const theme = createTheme();
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('SearchInput Component', () => {
  test('renders properly with default props', () => {
    render(
      <Wrapper>
        <SearchInput />
      </Wrapper>
    );
    
    const inputElement = screen.getByPlaceholderText('Search...');
    expect(inputElement).toBeInTheDocument();
    expect(screen.getByLabelText(/search/i)).toBeInTheDocument(); // Search icon
  });

  test('handles input changes', () => {
    const handleChange = jest.fn();
    render(
      <Wrapper>
        <SearchInput onChange={handleChange} />
      </Wrapper>
    );
    
    const inputElement = screen.getByPlaceholderText('Search...');
    fireEvent.change(inputElement, { target: { value: 'test query' } });
    
    expect(handleChange).toHaveBeenCalledWith('test query');
    expect(inputElement).toHaveValue('test query');
  });

  test('calls onSearch when Enter key is pressed', () => {
    const handleSearch = jest.fn();
    render(
      <Wrapper>
        <SearchInput onSearch={handleSearch} />
      </Wrapper>
    );
    
    const inputElement = screen.getByPlaceholderText('Search...');
    fireEvent.change(inputElement, { target: { value: 'test query' } });
    fireEvent.keyDown(inputElement, { key: 'Enter', code: 'Enter' });
    
    expect(handleSearch).toHaveBeenCalledWith('test query');
  });

  test('debounces search when typing', async () => {
    jest.useFakeTimers();
    const handleSearch = jest.fn();
    
    render(
      <Wrapper>
        <SearchInput onSearch={handleSearch} debounceMs={300} />
      </Wrapper>
    );
    
    const inputElement = screen.getByPlaceholderText('Search...');
    fireEvent.change(inputElement, { target: { value: 'test' } });
    
    // Search should not be called immediately
    expect(handleSearch).not.toHaveBeenCalled();
    
    // Fast-forward time
    act(() => {
      jest.advanceTimersByTime(300);
    });
    
    // Now the search should have been called
    expect(handleSearch).toHaveBeenCalledWith('test');
    
    jest.useRealTimers();
  });

  test('shows clear button when text is entered', () => {
    render(
      <Wrapper>
        <SearchInput value="test query" />
      </Wrapper>
    );
    
    expect(screen.getByLabelText('clear search')).toBeInTheDocument();
  });

  test('clears text when clear button is clicked', () => {
    const handleChange = jest.fn();
    render(
      <Wrapper>
        <SearchInput value="test query" onChange={handleChange} />
      </Wrapper>
    );
    
    const clearButton = screen.getByLabelText('clear search');
    fireEvent.click(clearButton);
    
    expect(handleChange).toHaveBeenCalledWith('');
  });

  test('displays loading indicator when loading prop is true', () => {
    render(
      <Wrapper>
        <SearchInput loading={true} />
      </Wrapper>
    );
    
    // Check for CircularProgress component
    const loadingIndicator = document.querySelector('.MuiCircularProgress-root');
    expect(loadingIndicator).toBeInTheDocument();
  });

  test('applies variant and size props correctly', () => {
    render(
      <Wrapper>
        <SearchInput variant="filled" size="small" />
      </Wrapper>
    );
    
    const inputElement = screen.getByPlaceholderText('Search...');
    const textFieldRoot = inputElement.closest('.MuiTextField-root');
    
    expect(textFieldRoot).toHaveClass('MuiTextField-root');
    // Check for filled variant
    expect(textFieldRoot?.querySelector('.MuiFilledInput-root')).toBeInTheDocument();
    // Check for small size
    expect(textFieldRoot?.querySelector('.MuiInputBase-sizeSmall')).toBeInTheDocument();
  });

  test('handles both controlled and uncontrolled modes', () => {
    const { rerender } = render(
      <Wrapper>
        <SearchInput />
      </Wrapper>
    );
    
    // Test uncontrolled mode
    const inputElement = screen.getByPlaceholderText('Search...');
    fireEvent.change(inputElement, { target: { value: 'uncontrolled' } });
    expect(inputElement).toHaveValue('uncontrolled');
    
    // Test controlled mode
    rerender(
      <Wrapper>
        <SearchInput value="controlled" />
      </Wrapper>
    );
    
    expect(inputElement).toHaveValue('controlled');
  });
}); 