import React from 'react';
import { 
  Box, 
  CircularProgress, 
  LinearProgress, 
  Typography, 
  Paper,
  Fade
} from '@mui/material';

export interface LoadingIndicatorProps {
  loading?: boolean;
  size?: 'small' | 'medium' | 'large';
  variant?: 'circular' | 'linear' | 'overlay' | 'fullpage';
  message?: string;
  value?: number; // For determinate progress (0-100)
  color?: 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
  delay?: number; // Delay before showing in ms
}

const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  loading = true,
  size = 'medium',
  variant = 'circular',
  message,
  value,
  color = 'primary',
  delay = 300
}) => {
  const [showLoading, setShowLoading] = React.useState(delay === 0);
  
  React.useEffect(() => {
    if (!loading) {
      setShowLoading(false);
      return;
    }
    
    const timer = setTimeout(() => {
      setShowLoading(true);
    }, delay);
    
    return () => {
      clearTimeout(timer);
    };
  }, [loading, delay]);
  
  if (!loading) return null;
  if (!showLoading) return null;
  
  // Determine size in pixels
  const getSize = () => {
    switch (size) {
      case 'small': return 24;
      case 'large': return 60;
      default: return 40;
    }
  };
  
  // Circular spinner
  if (variant === 'circular') {
    return (
      <Fade in={showLoading} timeout={300}>
        <Box 
          sx={{ 
            display: 'flex', 
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            py: 2
          }}
        >
          <CircularProgress 
            size={getSize()} 
            color={color} 
            variant={value !== undefined ? 'determinate' : 'indeterminate'} 
            value={value}
          />
          {message && (
            <Typography 
              variant="body2" 
              color="text.secondary" 
              sx={{ mt: 2 }}
            >
              {message}
            </Typography>
          )}
        </Box>
      </Fade>
    );
  }
  
  // Linear progress bar
  if (variant === 'linear') {
    return (
      <Fade in={showLoading} timeout={300}>
        <Box sx={{ width: '100%' }}>
          <LinearProgress 
            color={color} 
            variant={value !== undefined ? 'determinate' : 'indeterminate'} 
            value={value}
          />
          {message && (
            <Typography 
              variant="caption" 
              color="text.secondary" 
              sx={{ mt: 1, display: 'block' }}
            >
              {message}
            </Typography>
          )}
        </Box>
      </Fade>
    );
  }
  
  // Full-page overlay
  if (variant === 'overlay') {
    return (
      <Fade in={showLoading} timeout={300}>
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.4)',
            zIndex: 1300,
          }}
        >
          <Paper 
            elevation={3} 
            sx={{ 
              p: 3, 
              display: 'flex', 
              flexDirection: 'column',
              alignItems: 'center',
              backdropFilter: 'blur(4px)',
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              borderRadius: 2
            }}
          >
            <CircularProgress 
              size={getSize()} 
              color={color} 
              variant={value !== undefined ? 'determinate' : 'indeterminate'} 
              value={value}
            />
            {message && (
              <Typography 
                variant="body2" 
                color="text.secondary" 
                sx={{ mt: 2 }}
              >
                {message}
              </Typography>
            )}
          </Paper>
        </Box>
      </Fade>
    );
  }
  
  // Full-page centered
  if (variant === 'fullpage') {
    return (
      <Fade in={showLoading} timeout={300}>
        <Box
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1400,
            backgroundColor: (theme) => 
              theme.palette.mode === 'light' 
                ? 'rgba(255, 255, 255, 0.9)' 
                : 'rgba(0, 0, 0, 0.9)',
          }}
        >
          <CircularProgress 
            size={getSize() * 1.5} 
            color={color} 
            variant={value !== undefined ? 'determinate' : 'indeterminate'} 
            value={value}
          />
          {message && (
            <Typography 
              variant="h6" 
              color="text.primary" 
              sx={{ mt: 3 }}
            >
              {message}
            </Typography>
          )}
        </Box>
      </Fade>
    );
  }
  
  return null;
};

export default LoadingIndicator; 