import React, { useState, FormEvent } from 'react';
import { Box, Paper, Typography, Grid, styled } from '@mui/material';
import FormInput from './FormInput';

export interface FormField {
  /**
   * Unique ID for the field
   */
  id: string;
  
  /**
   * Label text for the field
   */
  label: string;
  
  /**
   * Type of input field
   */
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'date' | 'time' | 'select' | 'textarea' | 'checkbox' | 'radio';
  
  /**
   * Whether the field is required
   */
  required?: boolean;
  
  /**
   * Placeholder text
   */
  placeholder?: string;
  
  /**
   * Helper text to display below the field
   */
  helperText?: string;
  
  /**
   * Validation function for the field
   */
  validate?: (value: any) => string;
  
  /**
   * Options for select fields
   */
  options?: { label: string; value: string }[];
}

export interface FormProps {
  /**
   * Array of form field definitions
   */
  fields: FormField[];
  
  /**
   * Initial values for fields
   */
  initialValues?: Record<string, any>;
  
  /**
   * Function to handle form submission
   */
  onSubmit: (values: Record<string, any>) => void;
  
  /**
   * Form title
   */
  title?: string;
  
  /**
   * Form description
   */
  description?: string;
  
  /**
   * Submit button text
   */
  submitText?: string;
  
  /**
   * Whether to disable the submit button when the form is invalid
   */
  disableInvalidSubmit?: boolean;
  
  /**
   * Whether to show validation errors immediately (without waiting for blur)
   */
  showValidationImmediately?: boolean;
  
  /**
   * Form layout: vertical or horizontal
   */
  layout?: 'vertical' | 'horizontal';
  
  /**
   * Loading state for the form
   */
  loading?: boolean;
  
  /**
   * Additional className for styling
   */
  className?: string;
  
  /**
   * ID for the form element
   */
  id?: string;
  
  /**
   * Form validation function
   */
  validate?: (values: Record<string, any>) => Record<string, string>;
}

const FormContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  boxShadow: theme.shadows[1],
}));

const StyledForm = styled('form')(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(2),
}));

const Form: React.FC<FormProps> = ({
  fields,
  initialValues = {},
  onSubmit,
  title,
  description,
  submitText = 'Submit',
  disableInvalidSubmit = false,
  showValidationImmediately = false,
  layout = 'vertical',
  loading = false,
  className,
  id,
  validate,
}) => {
  const [values, setValues] = useState<Record<string, any>>(initialValues);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  const handleChange = (name: string, value: any) => {
    setValues((prev) => ({ ...prev, [name]: value }));
    
    // Clear error when field is changed
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    
    // Mark field as touched
    setTouched({ ...touched, [name]: true });
    
    // Validate on blur
    const error = validateField(name, value);
    setErrors({
      ...errors,
      [name]: error,
    });
  };

  const validateField = (id: string, value: any): string => {
    const field = fields.find((f) => f.id === id);
    
    if (!field) return '';
    
    // Required field validation
    if (field.required && (value === undefined || value === null || value === '')) {
      return `${field.label} is required`;
    }
    
    // Field-specific validation
    if (field.validate) {
      return field.validate(value) || '';
    }
    
    return '';
  };

  const validateForm = (): boolean => {
    let isValid = true;
    const newErrors: Record<string, string> = {};

    fields.forEach((field) => {
      const error = validateField(field.id, values[field.id]);
      newErrors[field.id] = error;
      if (error) {
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    // Mark all fields as touched
    const allTouched = fields.reduce((acc, field) => {
      acc[field.id] = true;
      return acc;
    }, {} as Record<string, boolean>);
    setTouched(allTouched);

    // Validate form
    const isValid = validateForm();
    
    // If custom validation function is provided, use it
    if (validate) {
      const validationErrors = validate(values);
      if (Object.keys(validationErrors).length > 0) {
        setErrors(prev => ({ ...prev, ...validationErrors }));
        return;
      }
    }
    
    // If form is valid or we're not disabling invalid submit
    if (isValid || !disableInvalidSubmit) {
      onSubmit(values);
    }
  };

  // Clone children and pass form context props
  const childrenWithProps = React.Children.map(fields, (field) => (
    <Grid item xs={12} key={field.id} md={layout === 'horizontal' ? 6 : 12}>
      <FormInput
        id={field.id}
        name={field.id}
        label={field.label}
        type={field.type || 'text'}
        value={values[field.id]}
        onChange={(e) => handleChange(field.id, e.target.value)}
        onBlur={handleBlur}
        error={touched[field.id] ? !!errors[field.id] : false}
        helperText={touched[field.id] ? errors[field.id] || field.helperText : field.helperText}
        required={field.required}
        placeholder={field.placeholder}
        options={field.options}
        disabled={loading}
        fullWidth
      />
    </Grid>
  ));

  return (
    <FormContainer className={className}>
      {title && <Typography variant="h5" gutterBottom>{title}</Typography>}
      {description && <Typography variant="body2" color="textSecondary" paragraph>{description}</Typography>}
      
      <StyledForm onSubmit={handleSubmit} id={id}>
        <Grid container spacing={2} direction={layout === 'vertical' ? 'column' : 'row'}>
          {childrenWithProps}
          
          <Grid item xs={12}>
            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
              <button type="submit" disabled={loading}>
                {submitText}
              </button>
            </Box>
          </Grid>
        </Grid>
      </StyledForm>
    </FormContainer>
  );
};

export default Form; 