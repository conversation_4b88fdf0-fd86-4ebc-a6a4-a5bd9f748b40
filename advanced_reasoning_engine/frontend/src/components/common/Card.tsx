import React from 'react';
import { Card as <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Typo<PERSON>, styled } from '@mui/material';
import type { CardProps as MuiCardProps } from '@mui/material';

export interface CustomCardProps {
  /**
   * Card title
   */
  title?: React.ReactNode;
  
  /**
   * Card subtitle
   */
  subtitle?: React.ReactNode;
  
  /**
   * Card content
   */
  children: React.ReactNode;
  
  /**
   * Whether the card should be elevated with a shadow
   * @default false
   */
  elevated?: boolean;
  
  /**
   * Card header action component
   */
  action?: React.ReactNode;
  
  /**
   * Whether to add extra padding to the card
   * @default false
   */
  padded?: boolean;
  
  /**
   * Custom padding to apply to the card content
   */
  contentPadding?: string | number;
}

export type CardProps = CustomCardProps & Omit<MuiCardProps, 'title' | 'children'>;

const StyledCard = styled(MuiCard, {
  shouldForwardProp: (prop) => 
    !['elevated', 'padded', 'contentPadding', 'title', 'subtitle', 'action'].includes(prop as string),
})<{ elevated?: boolean; padded?: boolean; contentPadding?: string | number }>(
  ({ theme, elevated, padded, contentPadding }) => ({
    borderRadius: '8px',
    boxShadow: elevated 
      ? '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
      : '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    overflow: 'hidden',
    transition: 'box-shadow 0.3s ease-in-out',
    '& .MuiCardContent-root': {
      padding: contentPadding || (padded ? '24px' : '16px'),
    },
    '&:hover': {
      boxShadow: elevated 
        ? '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
        : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    },
  })
);

/**
 * Card component that extends Material-UI Card with custom styling and props.
 */
const Card: React.FC<CardProps> = ({
  title,
  subtitle,
  children,
  elevated = false,
  action,
  padded = false,
  contentPadding,
  ...props
}) => {
  return (
    <StyledCard 
      elevated={elevated} 
      padded={padded}
      contentPadding={contentPadding}
      {...props}
    >
      {(title || subtitle) && (
        <CardHeader
          title={
            typeof title === 'string' ? (
              <Typography variant="h6" component="h2">
                {title}
              </Typography>
            ) : (
              title
            )
          }
          subheader={
            typeof subtitle === 'string' ? (
              <Typography variant="body2" color="text.secondary">
                {subtitle}
              </Typography>
            ) : (
              subtitle
            )
          }
          action={action}
        />
      )}
      <CardContent>{children}</CardContent>
    </StyledCard>
  );
};

export default Card; 