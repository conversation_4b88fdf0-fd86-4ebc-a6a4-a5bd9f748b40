import React, { ReactNode } from 'react';
import { 
  Card, 
  CardContent, 
  Typography, 
  CardActions,
  CardHeader,
  CardMedia,
  useTheme,
  useMediaQuery
} from '@mui/material';
import { 
  useResponsivePadding, 
  useResponsiveFontSizes 
} from '../../utils/ResponsiveLayout';

interface ResponsiveCardProps {
  title: string;
  subtitle?: string;
  content: ReactNode;
  image?: string;
  imageHeight?: number | {xs?: number, sm?: number, md?: number, lg?: number, xl?: number};
  actions?: ReactNode;
  elevation?: number;
  variant?: 'outlined' | 'elevation';
  className?: string;
  fullHeight?: boolean;
  noPadding?: boolean;
}

/**
 * A responsive card component that adapts to different screen sizes
 */
const ResponsiveCard = ({
  title,
  subtitle,
  content,
  image,
  imageHeight = {xs: 140, sm: 180, md: 200, lg: 220, xl: 240},
  actions,
  elevation = 1,
  variant = 'elevation',
  className,
  fullHeight = false,
  noPadding = false
}: ResponsiveCardProps) => {
  const theme = useTheme();
  const isXs = useMediaQuery(theme.breakpoints.only('xs'));
  const isSm = useMediaQuery(theme.breakpoints.only('sm'));
  const isMd = useMediaQuery(theme.breakpoints.only('md'));
  
  // Get responsive styles from our utility hooks
  const paddings = useResponsivePadding();
  const fontSizes = useResponsiveFontSizes();
  
  // Determine the image height based on screen size
  const getImageHeight = (): number => {
    if (typeof imageHeight === 'number') {
      return imageHeight;
    }
    
    if (isXs && imageHeight.xs !== undefined) return imageHeight.xs;
    if (isSm && imageHeight.sm !== undefined) return imageHeight.sm;
    if (isMd && imageHeight.md !== undefined) return imageHeight.md;
    
    // Default fallbacks
    return isXs ? 140 : isSm ? 180 : isMd ? 200 : 220;
  };
  
  return (
    <Card 
      elevation={elevation} 
      variant={variant}
      className={className}
      sx={{
        height: fullHeight ? '100%' : 'auto',
        display: 'flex',
        flexDirection: 'column',
        transition: 'all 0.3s ease',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: theme.shadows[elevation + 2]
        }
      }}
    >
      {image && (
        <CardMedia
          component="img"
          height={getImageHeight()}
          image={image}
          alt={title}
        />
      )}
      
      <CardHeader
        title={
          <Typography 
            variant={isXs ? "h6" : "h5"} 
            component="div"
            sx={{ 
              fontSize: fontSizes.h2.fontSize,
              fontWeight: 600,
              lineHeight: 1.2
            }}
          >
            {title}
          </Typography>
        }
        subheader={
          subtitle ? (
            <Typography 
              variant="subtitle1" 
              color="text.secondary"
              sx={{
                fontSize: fontSizes.body2.fontSize,
                mt: 0.5
              }}
            >
              {subtitle}
            </Typography>
          ) : null
        }
        sx={{
          padding: paddings.card.padding,
          pb: 0
        }}
      />
      
      <CardContent 
        sx={{
          padding: noPadding ? 0 : paddings.card.padding,
          pt: subtitle ? 1 : 0,
          flexGrow: 1,
          '&:last-child': {
            paddingBottom: noPadding ? 0 : isXs ? 1 : 2
          }
        }}
      >
        {content}
      </CardContent>
      
      {actions && (
        <CardActions 
          sx={{
            padding: paddings.card.padding,
            pt: 0
          }}
        >
          {actions}
        </CardActions>
      )}
    </Card>
  );
};

export default ResponsiveCard; 