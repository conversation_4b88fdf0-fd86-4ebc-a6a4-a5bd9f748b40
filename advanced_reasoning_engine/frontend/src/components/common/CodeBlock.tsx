import React, { useState } from 'react';
import { 
  Box, 
  Paper, 
  Typography, 
  IconButton, 
  Tooltip, 
  useTheme,
  Snackbar,
  Alert
} from '@mui/material';
import { 
  ContentCopy as CopyIcon,
  Check as CheckIcon,
  Code as CodeIcon
} from '@mui/icons-material';

interface CodeBlockProps {
  code: string;
  language?: string;
  title?: string;
  maxHeight?: string | number;
  showLineNumbers?: boolean;
  fontSize?: string | number;
}

const CodeBlock: React.FC<CodeBlockProps> = ({
  code,
  language = 'text',
  title,
  maxHeight = 500,
  showLineNumbers = true,
  fontSize = '0.9rem'
}) => {
  const theme = useTheme();
  const [copied, setCopied] = useState(false);
  
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };
  
  const lines = code.split('\n');
  
  return (
    <Paper
      elevation={1}
      sx={{
        backgroundColor: theme.palette.mode === 'dark' 
          ? 'rgba(0, 0, 0, 0.3)' 
          : 'rgba(0, 0, 0, 0.02)',
        borderRadius: 1,
        overflow: 'hidden',
        position: 'relative',
        mb: 2
      }}
    >
      {/* Header */}
      {(title || language) && (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: theme.palette.mode === 'dark' 
              ? 'rgba(255, 255, 255, 0.05)' 
              : 'rgba(0, 0, 0, 0.05)',
            px: 2,
            py: 1,
            borderBottom: `1px solid ${theme.palette.divider}`
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <CodeIcon 
              fontSize="small" 
              sx={{ mr: 1, color: theme.palette.text.secondary }} 
            />
            {title && (
              <Typography variant="subtitle2" sx={{ mr: 1 }}>
                {title}
              </Typography>
            )}
            {language && (
              <Typography variant="caption" color="text.secondary">
                {language}
              </Typography>
            )}
          </Box>
          <Tooltip title="Copy code">
            <IconButton 
              size="small" 
              onClick={handleCopy}
              aria-label="copy code"
            >
              {copied ? <CheckIcon fontSize="small" /> : <CopyIcon fontSize="small" />}
            </IconButton>
          </Tooltip>
        </Box>
      )}
      
      {/* Code Content */}
      <Box
        sx={{
          display: 'flex',
          overflowX: 'auto',
          maxHeight,
          fontFamily: 'monospace',
          fontSize,
        }}
      >
        {showLineNumbers && (
          <Box
            sx={{
              minWidth: 'fit-content',
              textAlign: 'right',
              px: 2,
              py: 1.5,
              color: theme.palette.text.secondary,
              backgroundColor: theme.palette.mode === 'dark' 
                ? 'rgba(255, 255, 255, 0.03)' 
                : 'rgba(0, 0, 0, 0.03)',
              userSelect: 'none',
              borderRight: `1px solid ${theme.palette.divider}`
            }}
          >
            {lines.map((_, i) => (
              <div key={`line-${i}`}>{i + 1}</div>
            ))}
          </Box>
        )}
        <Box
          component="pre"
          sx={{
            m: 0,
            p: 1.5,
            overflow: 'auto',
            width: '100%'
          }}
        >
          <Box
            component="code"
            sx={{
              fontSize: 'inherit',
              fontFamily: 'inherit'
            }}
          >
            {code}
          </Box>
        </Box>
      </Box>
      
      <Snackbar
        open={copied}
        autoHideDuration={2000}
        onClose={() => setCopied(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert severity="success" variant="filled" sx={{ width: '100%' }}>
          Code copied to clipboard
        </Alert>
      </Snackbar>
    </Paper>
  );
};

export default CodeBlock; 