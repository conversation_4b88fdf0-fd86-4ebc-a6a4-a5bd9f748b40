import React, { useState, useEffect } from 'react';
import {
  TextField,
  InputAdornment,
  IconButton,
  CircularProgress
} from '@mui/material';
import {
  Search as SearchIcon,
  Clear as ClearIcon
} from '@mui/icons-material';

export interface SearchInputProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  onSearch?: (value: string) => void;
  loading?: boolean;
  fullWidth?: boolean;
  autoFocus?: boolean;
  debounceMs?: number;
  variant?: 'outlined' | 'filled' | 'standard';
  size?: 'small' | 'medium';
}

const SearchInput: React.FC<SearchInputProps> = ({
  placeholder = 'Search...',
  value: externalValue,
  onChange,
  onSearch,
  loading = false,
  fullWidth = true,
  autoFocus = false,
  debounceMs = 300,
  variant = 'outlined',
  size = 'medium'
}) => {
  // For uncontrolled usage
  const [internalValue, setInternalValue] = useState('');
  
  // Use either external or internal value
  const value = externalValue !== undefined ? externalValue : internalValue;
  
  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    
    if (externalValue === undefined) {
      setInternalValue(newValue);
    }
    
    if (onChange) {
      onChange(newValue);
    }
  };
  
  // Handle clear button click
  const handleClear = () => {
    if (externalValue === undefined) {
      setInternalValue('');
    }
    
    if (onChange) {
      onChange('');
    }
    
    if (onSearch) {
      onSearch('');
    }
  };
  
  // Handle enter key press
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && onSearch) {
      onSearch(value);
    }
  };
  
  // Set up debounced search
  useEffect(() => {
    if (!onSearch || !debounceMs) return;
    
    const handler = setTimeout(() => {
      onSearch(value);
    }, debounceMs);
    
    return () => {
      clearTimeout(handler);
    };
  }, [value, onSearch, debounceMs]);
  
  return (
    <TextField
      fullWidth={fullWidth}
      placeholder={placeholder}
      variant={variant}
      size={size}
      value={value}
      onChange={handleChange}
      onKeyDown={handleKeyDown}
      autoFocus={autoFocus}
      InputProps={{
        startAdornment: (
          <InputAdornment position="start">
            <SearchIcon color="action" />
          </InputAdornment>
        ),
        endAdornment: (
          <InputAdornment position="end">
            {loading ? (
              <CircularProgress size={20} color="inherit" />
            ) : value ? (
              <IconButton
                aria-label="clear search"
                onClick={handleClear}
                edge="end"
                size="small"
              >
                <ClearIcon fontSize="small" />
              </IconButton>
            ) : null}
          </InputAdornment>
        )
      }}
    />
  );
};

export default SearchInput; 