import React from 'react';
import { Button as MuiButton, ButtonProps as MuiButtonProps, styled } from '@mui/material';
import { alpha } from '@mui/material/styles';

type MuiVariant = 'text' | 'contained' | 'outlined';
type CustomVariant = 'primary' | 'secondary' | 'tertiary' | 'danger' | 'success' | 'warning' | 'info' | 'ghost';

export interface ButtonProps extends Omit<MuiButtonProps, 'variant'> {
  /**
   * Button variant
   * @default 'primary'
   */
  variant?: CustomVariant;
  
  /**
   * Button size
   * @default 'medium'
   */
  size?: 'small' | 'medium' | 'large';
  
  /**
   * Whether the button should be elevated with a shadow
   * @default false
   */
  elevated?: boolean;
  
  /**
   * Whether the button should take full width of its container
   * @default false
   */
  fullWidth?: boolean;
  
  /**
   * Whether the button has rounded corners
   * @default false
   */
  rounded?: boolean;
}

const StyledMuiButton = styled(MuiButton, {
  shouldForwardProp: (prop) => 
    !['elevated', 'rounded'].includes(prop as string),
})<{
  elevated?: boolean;
  rounded?: boolean;
  size?: 'small' | 'medium' | 'large';
  customVariant?: CustomVariant;
}>(({ theme, elevated, rounded, size, customVariant }) => {
  // Define custom variant styles
  const variants: Record<string, any> = {
    primary: {
      backgroundColor: theme.palette.primary.main,
      color: theme.palette.primary.contrastText,
      '&:hover': {
        backgroundColor: theme.palette.primary.dark,
      },
    },
    secondary: {
      backgroundColor: theme.palette.secondary.main,
      color: theme.palette.secondary.contrastText,
      '&:hover': {
        backgroundColor: theme.palette.secondary.dark,
      },
    },
    tertiary: {
      backgroundColor: alpha(theme.palette.primary.main, 0.1),
      color: theme.palette.primary.main,
      '&:hover': {
        backgroundColor: alpha(theme.palette.primary.main, 0.2),
      },
    },
    danger: {
      backgroundColor: theme.palette.error.main,
      color: theme.palette.error.contrastText,
      '&:hover': {
        backgroundColor: theme.palette.error.dark,
      },
    },
    success: {
      backgroundColor: theme.palette.success.main,
      color: theme.palette.success.contrastText,
      '&:hover': {
        backgroundColor: theme.palette.success.dark,
      },
    },
    warning: {
      backgroundColor: theme.palette.warning.main,
      color: theme.palette.warning.contrastText,
      '&:hover': {
        backgroundColor: theme.palette.warning.dark,
      },
    },
    info: {
      backgroundColor: theme.palette.info.main,
      color: theme.palette.info.contrastText,
      '&:hover': {
        backgroundColor: theme.palette.info.dark,
      },
    },
    ghost: {
      backgroundColor: 'transparent',
      color: theme.palette.text.primary,
      '&:hover': {
        backgroundColor: alpha(theme.palette.text.primary, 0.05),
      },
    },
  };

  // Define size styles
  const sizes: Record<string, any> = {
    small: {
      padding: '6px 12px',
      fontSize: '0.75rem',
    },
    medium: {
      padding: '8px 16px',
      fontSize: '0.875rem',
    },
    large: {
      padding: '10px 22px',
      fontSize: '1rem',
    },
  };

  return {
    textTransform: 'none',
    fontWeight: 600,
    borderRadius: rounded ? '50px' : '4px',
    boxShadow: elevated ? theme.shadows[2] : 'none',
    transition: 'all 0.2s ease-in-out',
    letterSpacing: '0.025em',
    ...(customVariant && variants[customVariant] ? variants[customVariant] : {}),
    ...(size && sizes[size] ? sizes[size] : {}),
    '&:active': {
      transform: 'translateY(1px)',
    },
    '&.Mui-disabled': {
      backgroundColor: theme.palette.action.disabledBackground,
      color: theme.palette.action.disabled,
    },
  };
});

/**
 * Custom Button component that extends Material-UI Button with custom variants
 * and styling options.
 */
export const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  elevated = false,
  rounded = false,
  size = 'medium',
  ...props
}) => {
  // Map our custom variants to MUI variants
  const getMuiVariant = (customVariant: CustomVariant): MuiVariant => {
    if (customVariant === 'ghost') return 'text';
    if (customVariant === 'tertiary') return 'outlined';
    return 'contained';
  };

  return (
    <StyledMuiButton
      {...props}
      variant={getMuiVariant(variant)}
      elevated={elevated}
      rounded={rounded}
      size={size}
      customVariant={variant}
    >
      {children}
    </StyledMuiButton>
  );
};

export default Button; 