import React, { ReactNode } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>ontent,
  CardActions,
  Typography,
  Box,
  IconButton,
  Divider,
  Skeleton,
  useTheme,
  useMediaQuery,
  SxProps,
  Theme
} from '@mui/material';
import { MoreVert as MoreIcon } from '@mui/icons-material';

export interface ContentCardProps {
  title: string;
  subtitle?: string;
  children: ReactNode;
  actions?: ReactNode;
  headerAction?: ReactNode;
  elevation?: number;
  loading?: boolean;
  noPadding?: boolean;
  fullHeight?: boolean;
  className?: string;
  sx?: SxProps<Theme>;
}

const ContentCard: React.FC<ContentCardProps> = ({
  title,
  subtitle,
  children,
  actions,
  headerAction,
  elevation = 1,
  loading = false,
  noPadding = false,
  fullHeight = false,
  className,
  sx = {}
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  if (loading) {
    return (
      <Card 
        elevation={elevation} 
        className={className}
        sx={{ 
          height: fullHeight ? '100%' : 'auto',
          display: 'flex',
          flexDirection: 'column',
          mx: isMobile ? -1 : 0, // Negative margin on mobile to extend to edge
          borderRadius: isMobile ? { xs: 0, sm: '4px' } : '4px', // No border radius on mobile
          ...sx
        }}
      >
        <CardHeader
          title={<Skeleton animation="wave" height={24} width="80%" />}
          subheader={subtitle && <Skeleton animation="wave" height={20} width="60%" />}
          sx={{
            p: isMobile ? 2 : 3 // Less padding on mobile
          }}
        />
        <CardContent sx={{ 
          flexGrow: 1, 
          p: noPadding ? 0 : (isMobile ? 2 : 3) // Less padding on mobile
        }}>
          <Box sx={{ p: noPadding ? 0 : 2 }}>
            <Skeleton animation="wave" height={150} />
            <Skeleton animation="wave" height={20} width="90%" />
            <Skeleton animation="wave" height={20} width="70%" />
          </Box>
        </CardContent>
        {actions && (
          <>
            <Divider />
            <CardActions sx={{ p: isMobile ? 1.5 : 2 }}>
              <Skeleton animation="wave" height={36} width={80} />
            </CardActions>
          </>
        )}
      </Card>
    );
  }
  
  return (
    <Card 
      elevation={elevation} 
      className={className}
      sx={{ 
        height: fullHeight ? '100%' : 'auto',
        display: 'flex',
        flexDirection: 'column',
        mx: isMobile ? -1 : 0, // Negative margin on mobile to extend to edge
        borderRadius: isMobile ? { xs: 0, sm: '4px' } : '4px', // No border radius on mobile
        ...sx
      }}
    >
      <CardHeader
        title={
          <Typography 
            variant={isMobile ? "subtitle1" : "h6"} 
            component="h2"
            sx={{
              fontSize: isMobile ? '1rem' : '1.25rem',
              lineHeight: isMobile ? 1.4 : 1.6
            }}
          >
            {title}
          </Typography>
        }
        subheader={subtitle && (
          <Typography 
            variant="body2" 
            color="text.secondary"
            sx={{
              fontSize: isMobile ? '0.75rem' : '0.875rem',
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }}
          >
            {subtitle}
          </Typography>
        )}
        action={
          headerAction || (
            <IconButton aria-label="settings" size={isMobile ? "small" : "medium"}>
              <MoreIcon fontSize={isMobile ? "small" : "medium"} />
            </IconButton>
          )
        }
        sx={{
          p: isMobile ? 2 : 3, // Less padding on mobile
          borderBottom: `1px solid ${theme.palette.divider}`,
          backgroundColor: theme.palette.mode === 'light' 
            ? 'rgba(0, 0, 0, 0.02)' 
            : 'rgba(255, 255, 255, 0.05)'
        }}
      />
      <CardContent sx={{ 
        flexGrow: 1, 
        p: noPadding ? 0 : (isMobile ? 2 : 3), // Less padding on mobile
        '&:last-child': { pb: noPadding ? 0 : (isMobile ? 2 : 3) }
      }}>
        {children}
      </CardContent>
      {actions && (
        <>
          <Divider />
          <CardActions sx={{ p: isMobile ? 1.5 : 2 }}>
            {actions}
          </CardActions>
        </>
      )}
    </Card>
  );
};

export default ContentCard; 