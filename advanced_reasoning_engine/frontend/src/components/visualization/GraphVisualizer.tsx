import React, { useRef, useEffect, useState } from 'react';
import { 
  Box, 
  Typography, 
  IconButton, 
  Paper, 
  Slider, 
  FormControlLabel, 
  Switch, 
  Tooltip,
  Select,
  MenuItem,
  InputLabel,
  FormControl
} from '@mui/material';
import {
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  Refresh as RefreshIcon,
  Save as SaveIcon,
  LegendToggle as LegendToggleIcon
} from '@mui/icons-material';
import { ContentCard } from '../common';

// Define types for graph data
export interface GraphNode {
  id: string;
  text: string;
  score?: number;
  metadata?: Record<string, any>;
  type?: 'thought' | 'action' | 'observation' | 'result' | 'decision';
}

export interface GraphEdge {
  source: string;
  target: string;
  label?: string;
  weight?: number;
  type?: 'reasoning' | 'action' | 'observation' | 'decision';
}

export interface GraphData {
  nodes: GraphNode[];
  edges: GraphEdge[];
}

export interface GraphVisualizerProps {
  data: GraphData | null;
  title?: string;
  width?: number | string;
  height?: number | string;
  showControls?: boolean;
  onNodeClick?: (node: GraphNode) => void;
  onEdgeClick?: (edge: GraphEdge) => void;
  loading?: boolean;
  layoutType?: 'force' | 'hierarchical' | 'radial';
}

const GraphVisualizer: React.FC<GraphVisualizerProps> = ({
  data,
  title = 'Graph of Thoughts Visualization',
  width = '100%',
  height = 600,
  showControls = true,
  onNodeClick,
  onEdgeClick,
  loading = false,
  layoutType = 'force'
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [zoom, setZoom] = useState<number>(1);
  const [showLabels, setShowLabels] = useState<boolean>(true);
  const [showLegend, setShowLegend] = useState<boolean>(true);
  
  // These state variables are for future implementation of node/edge hover details
  // Will be used when D3 integration is complete
  const [hoveredNode, setHoveredNode] = useState<GraphNode | null>(null);
  
  const [selectedLayout, setSelectedLayout] = useState<string>(layoutType);
  const [nodeFilter, setNodeFilter] = useState<string>('all');
  
  // Mock rendering of a graph (in a real implementation, use D3.js or a similar library)
  useEffect(() => {
    if (!data || !svgRef.current) return;
    
    // In a real implementation, this would use D3.js to render the graph
    // For now, we'll just add a placeholder
    const svg = svgRef.current;
    
    // Clear previous content
    while (svg.firstChild) {
      svg.removeChild(svg.firstChild);
    }
    
    // Create a text element to show we would render the graph here
    const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    text.setAttribute('x', '50%');
    text.setAttribute('y', '50%');
    text.setAttribute('text-anchor', 'middle');
    text.setAttribute('dominant-baseline', 'middle');
    text.setAttribute('fill', '#666');
    text.textContent = 'Graph visualization would be rendered here using D3.js';
    
    svg.appendChild(text);
    
    // Draw a sample graph to illustrate the concept
    const createCircle = (cx: number, cy: number, r: number, fill: string) => {
      const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
      circle.setAttribute('cx', cx.toString());
      circle.setAttribute('cy', cy.toString());
      circle.setAttribute('r', r.toString());
      circle.setAttribute('fill', fill);
      circle.setAttribute('stroke', '#fff');
      circle.setAttribute('stroke-width', '2');
      return circle;
    };
    
    const createLine = (x1: number, y1: number, x2: number, y2: number, stroke: string) => {
      const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
      line.setAttribute('x1', x1.toString());
      line.setAttribute('y1', y1.toString());
      line.setAttribute('x2', x2.toString());
      line.setAttribute('y2', y2.toString());
      line.setAttribute('stroke', stroke);
      line.setAttribute('stroke-width', '2');
      return line;
    };
    
    // Sample nodes
    svg.appendChild(createCircle(300, 200, 20, '#4A55BD')); // Thought
    svg.appendChild(createCircle(500, 200, 20, '#22C55E')); // Thought
    svg.appendChild(createCircle(400, 350, 20, '#FF4D4F')); // Decision
    svg.appendChild(createCircle(200, 350, 20, '#FAAD14')); // Action
    svg.appendChild(createCircle(600, 350, 20, '#13C2C2')); // Observation
    
    // Sample edges
    svg.appendChild(createLine(300, 200, 500, 200, '#999')); // Between thoughts
    svg.appendChild(createLine(300, 200, 200, 350, '#999')); // Thought to action
    svg.appendChild(createLine(300, 200, 400, 350, '#999')); // Thought to decision
    svg.appendChild(createLine(500, 200, 400, 350, '#999')); // Thought to decision
    svg.appendChild(createLine(500, 200, 600, 350, '#999')); // Thought to observation
    
    // Add labels if enabled
    if (showLabels) {
      const createText = (x: number, y: number, content: string) => {
        const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        text.setAttribute('x', x.toString());
        text.setAttribute('y', y.toString());
        text.setAttribute('text-anchor', 'middle');
        text.setAttribute('font-size', '12px');
        text.setAttribute('fill', '#333');
        text.textContent = content;
        return text;
      };
      
      svg.appendChild(createText(300, 170, 'Initial Thought'));
      svg.appendChild(createText(500, 170, 'Refined Thought'));
      svg.appendChild(createText(400, 380, 'Decision'));
      svg.appendChild(createText(200, 380, 'Action'));
      svg.appendChild(createText(600, 380, 'Observation'));
    }
    
    // Add legend if enabled
    if (showLegend) {
      const legend = document.createElementNS('http://www.w3.org/2000/svg', 'g');
      
      // Background
      const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
      rect.setAttribute('x', '20');
      rect.setAttribute('y', '20');
      rect.setAttribute('width', '150');
      rect.setAttribute('height', '150');
      rect.setAttribute('fill', 'white');
      rect.setAttribute('stroke', '#ddd');
      rect.setAttribute('rx', '5');
      legend.appendChild(rect);
      
      // Title
      const title = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      title.setAttribute('x', '95');
      title.setAttribute('y', '40');
      title.setAttribute('text-anchor', 'middle');
      title.setAttribute('font-weight', 'bold');
      title.textContent = 'Legend';
      legend.appendChild(title);
      
      // Legend items
      const items = [
        { color: '#4A55BD', label: 'Thought' },
        { color: '#22C55E', label: 'Refined Thought' },
        { color: '#FF4D4F', label: 'Decision' },
        { color: '#FAAD14', label: 'Action' },
        { color: '#13C2C2', label: 'Observation' }
      ];
      
      items.forEach((item, index) => {
        const y = 60 + index * 20;
        
        // Circle
        const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        circle.setAttribute('cx', '40');
        circle.setAttribute('cy', y.toString());
        circle.setAttribute('r', '6');
        circle.setAttribute('fill', item.color);
        legend.appendChild(circle);
        
        // Label
        const label = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        label.setAttribute('x', '55');
        label.setAttribute('y', (y + 5).toString());
        label.setAttribute('font-size', '12px');
        label.textContent = item.label;
        legend.appendChild(label);
      });
      
      svg.appendChild(legend);
    }
    
  }, [data, zoom, showLabels, showLegend, selectedLayout, nodeFilter]);
  
  const handleZoomIn = () => {
    setZoom(prevZoom => Math.min(prevZoom + 0.2, 3));
  };
  
  const handleZoomOut = () => {
    setZoom(prevZoom => Math.max(prevZoom - 0.2, 0.5));
  };
  
  const handleZoomChange = (event: Event, newValue: number | number[]) => {
    setZoom(newValue as number);
  };
  
  const handleResetView = () => {
    setZoom(1);
  };
  
  const handleToggleLabels = () => {
    setShowLabels(!showLabels);
  };
  
  const handleToggleLegend = () => {
    setShowLegend(!showLegend);
  };
  
  const handleLayoutChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setSelectedLayout(event.target.value as string);
  };
  
  const handleNodeFilterChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setNodeFilter(event.target.value as string);
  };
  
  const handleSaveImage = () => {
    if (!svgRef.current) return;
    
    const svg = svgRef.current;
    const serializer = new XMLSerializer();
    const svgString = serializer.serializeToString(svg);
    const svgBlob = new Blob([svgString], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(svgBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = 'graph_visualization.svg';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };
  
  // Node details panel that shows when a node is hovered or clicked
  const renderNodeDetails = () => {
    if (!hoveredNode) return null;
    
    return (
      <Paper
        elevation={3}
        sx={{
          position: 'absolute',
          bottom: 16,
          right: 16,
          padding: 2,
          maxWidth: 300,
          zIndex: 10
        }}
      >
        <Typography variant="subtitle1" gutterBottom>
          Node Details
        </Typography>
        <Typography variant="body2" gutterBottom>
          <strong>ID:</strong> {hoveredNode.id}
        </Typography>
        <Typography variant="body2" gutterBottom>
          <strong>Text:</strong> {hoveredNode.text}
        </Typography>
        {hoveredNode.score !== undefined && (
          <Typography variant="body2" gutterBottom>
            <strong>Score:</strong> {hoveredNode.score.toFixed(2)}
          </Typography>
        )}
        {hoveredNode.type && (
          <Typography variant="body2" gutterBottom>
            <strong>Type:</strong> {hoveredNode.type}
          </Typography>
        )}
      </Paper>
    );
  };
  
  return (
    <ContentCard
      title={title}
      subtitle="Interactive visualization of the reasoning graph"
      elevation={2}
      loading={loading}
      fullHeight
      headerAction={
        showControls && (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Tooltip title="Zoom In">
              <IconButton size="small" onClick={handleZoomIn}>
                <ZoomInIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Zoom Out">
              <IconButton size="small" onClick={handleZoomOut}>
                <ZoomOutIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Reset View">
              <IconButton size="small" onClick={handleResetView}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Toggle Labels">
              <FormControlLabel
                control={
                  <Switch
                    checked={showLabels}
                    onChange={handleToggleLabels}
                    size="small"
                  />
                }
                label="Labels"
                sx={{ ml: 1, mr: 1 }}
              />
            </Tooltip>
            <Tooltip title="Toggle Legend">
              <IconButton size="small" onClick={handleToggleLegend}>
                <LegendToggleIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Save Image">
              <IconButton size="small" onClick={handleSaveImage}>
                <SaveIcon />
              </IconButton>
            </Tooltip>
          </Box>
        )
      }
    >
      <Box sx={{ position: 'relative', height: 'calc(100% - 20px)' }}>
        {showControls && (
          <Box
            sx={{
              position: 'absolute',
              top: 16,
              left: 16,
              zIndex: 5,
              background: 'rgba(255, 255, 255, 0.9)',
              p: 1,
              borderRadius: 1,
              boxShadow: 1
            }}
          >
            <Box sx={{ mb: 1 }}>
              <FormControl size="small" sx={{ minWidth: 120, mr: 1 }}>
                <InputLabel id="layout-select-label">Layout</InputLabel>
                <Select
                  labelId="layout-select-label"
                  value={selectedLayout}
                  label="Layout"
                  onChange={handleLayoutChange as any}
                  size="small"
                >
                  <MenuItem value="force">Force Directed</MenuItem>
                  <MenuItem value="hierarchical">Hierarchical</MenuItem>
                  <MenuItem value="radial">Radial</MenuItem>
                </Select>
              </FormControl>
              
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel id="filter-select-label">Filter</InputLabel>
                <Select
                  labelId="filter-select-label"
                  value={nodeFilter}
                  label="Filter"
                  onChange={handleNodeFilterChange as any}
                  size="small"
                >
                  <MenuItem value="all">All Nodes</MenuItem>
                  <MenuItem value="thought">Thoughts</MenuItem>
                  <MenuItem value="action">Actions</MenuItem>
                  <MenuItem value="observation">Observations</MenuItem>
                  <MenuItem value="decision">Decisions</MenuItem>
                  <MenuItem value="result">Results</MenuItem>
                </Select>
              </FormControl>
            </Box>
            
            <Typography variant="caption" display="block" gutterBottom>
              Zoom: {zoom.toFixed(1)}x
            </Typography>
            <Slider
              size="small"
              value={zoom}
              min={0.5}
              max={3}
              step={0.1}
              onChange={handleZoomChange}
              sx={{ width: 240 }}
            />
          </Box>
        )}
        
        <Box
          ref={containerRef}
          sx={{
            width,
            height,
            overflow: 'hidden'
          }}
        >
          <svg
            ref={svgRef}
            width="100%"
            height="100%"
            style={{
              transform: `scale(${zoom})`,
              transformOrigin: 'center',
              transition: 'transform 0.3s ease-in-out'
            }}
          />
        </Box>
        
        {renderNodeDetails()}
      </Box>
    </ContentCard>
  );
};

export default GraphVisualizer; 