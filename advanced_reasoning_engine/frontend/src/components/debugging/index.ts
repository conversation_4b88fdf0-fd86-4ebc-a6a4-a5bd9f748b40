// Type definitions for the debugging components
export interface DebugStep {
  id: string;
  timestamp: string;
  type: 'model_call' | 'reasoning' | 'retrieval' | 'action' | 'error' | 'other';
  name: string;
  content: string;
  duration?: number;
  error?: string;
  modelInput?: string;
  modelOutput?: string;
  metadata?: Record<string, any>;
}

export interface DebugSession {
  id: string;
  startTime: string;
  endTime: string;
  query: string;
  steps: DebugStep[];
  metadata?: Record<string, any>;
}

// Component exports
export { default as DebugConsole } from './DebugConsole';
export { default as DebugStepViewer } from './DebugStepViewer';
export { default as DebugSessionInfo } from './DebugSessionInfo';
export { default as SessionHistoryManager } from './SessionHistoryManager'; 