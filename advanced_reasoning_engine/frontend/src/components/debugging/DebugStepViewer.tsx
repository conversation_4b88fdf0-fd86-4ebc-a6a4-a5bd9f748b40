import React from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Divider, 
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  CircularProgress,
  Alert
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import CodeIcon from '@mui/icons-material/Code';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import SyntaxHighlighter from 'react-syntax-highlighter';
import { docco } from 'react-syntax-highlighter/dist/esm/styles/hljs';
import { formatDuration, formatTimestamp } from '../../utils/formatters';

// Types for the debug step
export interface DebugStep {
  id: string;
  name: string;
  type: 'reasoning' | 'retrieval' | 'generation' | 'processing' | 'error';
  startTime: number;
  endTime?: number;
  description?: string;
  modelInput?: string | object;
  modelOutput?: string | object;
  error?: string;
  metadata?: Record<string, any>;
  parentId?: string;
  children?: DebugStep[];
}

interface DebugStepViewerProps {
  step: DebugStep | null;
  isLoading?: boolean;
}

// Helper function to determine color based on step type
const getStepTypeColor = (type: DebugStep['type']): string => {
  switch (type) {
    case 'reasoning':
      return '#4285F4'; // Blue
    case 'retrieval':
      return '#0F9D58'; // Green
    case 'generation':
      return '#9C27B0'; // Purple
    case 'processing':
      return '#FF9800'; // Orange
    case 'error':
      return '#DB4437'; // Red
    default:
      return '#757575'; // Grey
  }
};

// Helper function to format JSON for display
const formatJson = (data: any): string => {
  try {
    if (typeof data === 'string') {
      // Try to parse string as JSON
      const parsed = JSON.parse(data);
      return JSON.stringify(parsed, null, 2);
    }
    return JSON.stringify(data, null, 2);
  } catch (e) {
    // If it's not valid JSON, return as is
    return typeof data === 'string' ? data : JSON.stringify(data, null, 2);
  }
};

const DebugStepViewer: React.FC<DebugStepViewerProps> = ({ step, isLoading = false }) => {
  if (isLoading) {
    return (
      <Paper elevation={2} sx={{ p: 3, mt: 2, display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
        <CircularProgress />
      </Paper>
    );
  }

  if (!step) {
    return (
      <Paper elevation={2} sx={{ p: 3, mt: 2, display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
        <Typography variant="body1" color="text.secondary">
          Select a debug step to view details
        </Typography>
      </Paper>
    );
  }

  const duration = step.endTime ? step.endTime - step.startTime : undefined;
  const stepColor = getStepTypeColor(step.type);

  return (
    <Paper elevation={2} sx={{ p: 3, mt: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5" component="h2">
          {step.name}
        </Typography>
        <Chip 
          label={step.type.toUpperCase()} 
          sx={{ 
            backgroundColor: stepColor,
            color: 'white',
            fontWeight: 'bold'
          }} 
        />
      </Box>

      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <AccessTimeIcon sx={{ mr: 1, color: 'text.secondary' }} />
        <Typography variant="body2" color="text.secondary">
          Started: {formatTimestamp(step.startTime)}
          {duration && ` • Duration: ${formatDuration(duration)}`}
        </Typography>
      </Box>

      {step.description && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="body1">{step.description}</Typography>
        </Box>
      )}

      {step.error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-wrap', fontFamily: 'monospace' }}>
            {step.error}
          </Typography>
        </Alert>
      )}

      <Divider sx={{ my: 2 }} />

      {/* Model Input */}
      {step.modelInput && (
        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <CodeIcon sx={{ mr: 1, color: 'text.secondary' }} />
            <Typography>Model Input</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ maxHeight: '400px', overflow: 'auto', bgcolor: '#f5f5f5', borderRadius: 1 }}>
              <SyntaxHighlighter language="json" style={docco}>
                {formatJson(step.modelInput)}
              </SyntaxHighlighter>
            </Box>
          </AccordionDetails>
        </Accordion>
      )}

      {/* Model Output */}
      {step.modelOutput && (
        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <CodeIcon sx={{ mr: 1, color: 'text.secondary' }} />
            <Typography>Model Output</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ maxHeight: '400px', overflow: 'auto', bgcolor: '#f5f5f5', borderRadius: 1 }}>
              <SyntaxHighlighter language="json" style={docco}>
                {formatJson(step.modelOutput)}
              </SyntaxHighlighter>
            </Box>
          </AccordionDetails>
        </Accordion>
      )}

      {/* Metadata */}
      {step.metadata && Object.keys(step.metadata).length > 0 && (
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <InfoOutlinedIcon sx={{ mr: 1, color: 'text.secondary' }} />
            <Typography>Metadata</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ maxHeight: '400px', overflow: 'auto', bgcolor: '#f5f5f5', borderRadius: 1 }}>
              <SyntaxHighlighter language="json" style={docco}>
                {formatJson(step.metadata)}
              </SyntaxHighlighter>
            </Box>
          </AccordionDetails>
        </Accordion>
      )}

      {/* Child Steps */}
      {step.children && step.children.length > 0 && (
        <Box sx={{ mt: 3 }}>
          <Typography variant="h6" sx={{ mb: 1 }}>Child Steps</Typography>
          <Box sx={{ pl: 2, borderLeft: `2px solid ${stepColor}` }}>
            {step.children.map((childStep) => (
              <Box 
                key={childStep.id}
                sx={{ 
                  p: 1.5, 
                  mb: 1, 
                  borderRadius: 1,
                  cursor: 'pointer',
                  '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.04)' },
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}
              >
                <Box>
                  <Typography variant="body1">{childStep.name}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {childStep.type} • {formatDuration(childStep.endTime ? childStep.endTime - childStep.startTime : 0)}
                  </Typography>
                </Box>
                <Chip 
                  size="small" 
                  label={childStep.type} 
                  sx={{ bgcolor: getStepTypeColor(childStep.type), color: 'white' }} 
                />
              </Box>
            ))}
          </Box>
        </Box>
      )}
    </Paper>
  );
};

export default DebugStepViewer; 