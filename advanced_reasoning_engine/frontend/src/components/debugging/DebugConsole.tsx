import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  Divider,
  TextField,
  Button,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Tooltip,
  Switch,
  FormControlLabel,
  Tab,
  Tabs,
} from '@mui/material';
import {
  Code as CodeIcon,
  Search as SearchIcon,
  FileCopy as FileCopyIcon, 
  ExpandMore as ExpandMoreIcon,
  DeleteOutline as DeleteIcon,
  Save as SaveIcon,
  Info as InfoIcon,
  BugReport as BugReportIcon,
  Timeline as TimelineIcon,
  Assignment as AssignmentIcon
} from '@mui/icons-material';
import { ContentCard } from '../common';
import SyntaxHighlighter from 'react-syntax-highlighter';
import { docco } from 'react-syntax-highlighter/dist/esm/styles/hljs';

// Types for debugging data
export interface DebugStep {
  id: string;
  timestamp: string;
  type: 'model_call' | 'retrieval' | 'reasoning' | 'action' | 'error' | 'info';
  name: string;
  content: string;
  metadata?: Record<string, any>;
  modelInput?: string;
  modelOutput?: string;
  duration?: number;
  error?: string;
}

export interface DebugSession {
  id: string;
  startTime: string;
  endTime?: string;
  query: string;
  steps: DebugStep[];
  metadata?: Record<string, any>;
}

export interface DebugConsoleProps {
  session: DebugSession | null;
  onStepClick?: (step: DebugStep) => void;
  onSearch?: (query: string) => void;
  onClear?: () => void;
  onSave?: (session: DebugSession) => void;
  width?: string | number;
  height?: string | number;
}

const DebugConsole: React.FC<DebugConsoleProps> = ({
  session,
  onStepClick,
  onSearch,
  onClear,
  onSave,
  width = '100%',
  height = '600px'
}) => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [stepFilter, setStepFilter] = useState<string>('all');
  const [expandedSteps, setExpandedSteps] = useState<Set<string>>(new Set());
  const [showRawData, setShowRawData] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<number>(0);
  const [showTimestamps, setShowTimestamps] = useState<boolean>(true);
  const consoleEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to the bottom when new steps are added
  useEffect(() => {
    if (session && consoleEndRef.current) {
      consoleEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [session?.steps?.length]);

  // Filter steps based on search term and filter type
  const getFilteredSteps = () => {
    if (!session) return [];
    
    return session.steps.filter(step => {
      // Filter by type
      if (stepFilter !== 'all' && step.type !== stepFilter) {
        return false;
      }
      
      // Filter by search term
      if (searchTerm) {
        const normalizedSearch = searchTerm.toLowerCase();
        return (
          step.name.toLowerCase().includes(normalizedSearch) ||
          step.content.toLowerCase().includes(normalizedSearch) ||
          (step.modelInput && step.modelInput.toLowerCase().includes(normalizedSearch)) ||
          (step.modelOutput && step.modelOutput.toLowerCase().includes(normalizedSearch))
        );
      }
      
      return true;
    });
  };

  // Handle step expansion toggle
  const toggleStepExpansion = (stepId: string) => {
    setExpandedSteps(prev => {
      const newSet = new Set(prev);
      if (newSet.has(stepId)) {
        newSet.delete(stepId);
      } else {
        newSet.add(stepId);
      }
      return newSet;
    });
  };

  // Handle search submission
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch && onSearch(searchTerm);
  };

  // Handle save session
  const handleSaveSession = () => {
    if (session && onSave) {
      onSave(session);
    }
  };

  // Format timestamp to be more readable
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false });
  };

  // Calculate duration in ms or seconds
  const formatDuration = (duration?: number) => {
    if (!duration) return '';
    return duration < 1000 ? `${duration.toFixed(2)}ms` : `${(duration / 1000).toFixed(2)}s`;
  };

  // Get icon for step type
  const getStepIcon = (type: string) => {
    switch (type) {
      case 'model_call':
        return <CodeIcon fontSize="small" />;
      case 'retrieval':
        return <SearchIcon fontSize="small" />;
      case 'reasoning':
        return <TimelineIcon fontSize="small" />;
      case 'action':
        return <AssignmentIcon fontSize="small" />;
      case 'error':
        return <BugReportIcon fontSize="small" color="error" />;
      case 'info':
      default:
        return <InfoIcon fontSize="small" />;
    }
  };

  // Get color for step type
  const getStepColor = (type: string) => {
    switch (type) {
      case 'model_call':
        return '#4A55BD'; // Blue
      case 'retrieval':
        return '#22C55E'; // Green
      case 'reasoning':
        return '#FAAD14'; // Yellow/Orange
      case 'action':
        return '#13C2C2'; // Cyan
      case 'error':
        return '#FF4D4F'; // Red
      case 'info':
      default:
        return '#8C8C8C'; // Grey
    }
  };

  // Copy content to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  return (
    <ContentCard
      title="Debug Console"
      subtitle="Interactive debugging tools for reasoning processes"
      headerAction={
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="Save debug session">
            <IconButton
              size="small"
              onClick={handleSaveSession}
              disabled={!session}
            >
              <SaveIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title="Clear console">
            <IconButton
              size="small"
              onClick={onClear}
              disabled={!session || session.steps.length === 0}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      }
    >
      <Box sx={{ width, height: 'auto' }}>
        {/* Toolbar */}
        <Box sx={{ mb: 2, display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2, alignItems: 'center' }}>
          <form onSubmit={handleSearchSubmit} style={{ flex: 1, display: 'flex', gap: 8 }}>
            <TextField
              size="small"
              placeholder="Search debug logs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />,
              }}
              fullWidth
            />
            <Button
              variant="outlined"
              size="small"
              type="submit"
            >
              Search
            </Button>
          </form>
          
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel id="step-filter-label">Filter</InputLabel>
            <Select
              labelId="step-filter-label"
              id="step-filter"
              value={stepFilter}
              label="Filter"
              onChange={(e) => setStepFilter(e.target.value)}
            >
              <MenuItem value="all">All Steps</MenuItem>
              <MenuItem value="model_call">Model Calls</MenuItem>
              <MenuItem value="retrieval">Retrievals</MenuItem>
              <MenuItem value="reasoning">Reasoning</MenuItem>
              <MenuItem value="action">Actions</MenuItem>
              <MenuItem value="error">Errors</MenuItem>
              <MenuItem value="info">Info</MenuItem>
            </Select>
          </FormControl>
          
          <FormControlLabel
            control={
              <Switch
                checked={showRawData}
                onChange={(e) => setShowRawData(e.target.checked)}
                size="small"
              />
            }
            label="Show Raw Data"
            sx={{ mr: 0 }}
          />
          
          <FormControlLabel
            control={
              <Switch
                checked={showTimestamps}
                onChange={(e) => setShowTimestamps(e.target.checked)}
                size="small"
              />
            }
            label="Timestamps"
            sx={{ mr: 0 }}
          />
        </Box>
        
        {/* Console Tabs */}
        <Tabs
          value={activeTab}
          onChange={(_, newValue) => setActiveTab(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}
        >
          <Tab label="Console" id="tab-0" />
          <Tab label="Session Info" id="tab-1" disabled={!session} />
          <Tab label="Timeline" id="tab-2" disabled={!session} />
        </Tabs>
        
        {/* Tab Panels */}
        <Box role="tabpanel" hidden={activeTab !== 0} sx={{ height: 'calc(100% - 100px)', overflow: 'auto' }}>
          {/* Main Debug Console */}
          {!session ? (
            <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'background.default' }}>
              <Typography color="text.secondary">No active debugging session</Typography>
            </Paper>
          ) : session.steps.length === 0 ? (
            <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'background.default' }}>
              <Typography color="text.secondary">No steps in current session</Typography>
            </Paper>
          ) : (
            <Box sx={{ maxHeight: 'calc(100vh - 300px)', overflow: 'auto', pr: 1 }}>
              {getFilteredSteps().map((step, index) => (
                <Accordion
                  key={step.id}
                  expanded={expandedSteps.has(step.id)}
                  onChange={() => toggleStepExpansion(step.id)}
                  sx={{
                    mb: 1,
                    borderLeft: `4px solid ${getStepColor(step.type)}`,
                    '&:before': { display: 'none' },
                  }}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    sx={{ alignItems: 'center' }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1, gap: 1 }}>
                      {getStepIcon(step.type)}
                      <Typography variant="subtitle2">
                        {step.name}
                      </Typography>
                      {showTimestamps && (
                        <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                          {formatTimestamp(step.timestamp)}
                        </Typography>
                      )}
                      {step.duration !== undefined && (
                        <Chip
                          label={formatDuration(step.duration)}
                          size="small"
                          sx={{
                            backgroundColor: step.duration > 1000 ? '#FAAD14' : '#E6F7FF',
                            fontSize: '0.7rem',
                            height: 20,
                            ml: 'auto',
                          }}
                        />
                      )}
                      {step.error && (
                        <Chip
                          label="Error"
                          size="small"
                          color="error"
                          sx={{ fontSize: '0.7rem', height: 20, ml: 1 }}
                        />
                      )}
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Box>
                      {/* Step Content */}
                      <Typography variant="body2" gutterBottom>
                        {step.content}
                      </Typography>
                      
                      {/* Error Message */}
                      {step.error && (
                        <Paper sx={{ p: 1, mt: 1, bgcolor: '#FFF1F0', borderLeft: '4px solid #FF4D4F' }}>
                          <Typography variant="body2" color="error">
                            {step.error}
                          </Typography>
                        </Paper>
                      )}
                      
                      {/* Model Input/Output Tabs */}
                      {(step.modelInput || step.modelOutput) && (
                        <Box sx={{ mt: 2 }}>
                          <Tabs
                            value={0}
                            sx={{ minHeight: 32, mb: 1 }}
                            TabIndicatorProps={{ style: { height: 2 } }}
                          >
                            {step.modelInput && <Tab label="Input" sx={{ minHeight: 32, py: 0 }} />}
                            {step.modelOutput && <Tab label="Output" sx={{ minHeight: 32, py: 0 }} />}
                          </Tabs>
                          
                          {step.modelInput && (
                            <Paper variant="outlined" sx={{ p: 1, mb: 2, position: 'relative' }}>
                              {showRawData ? (
                                <SyntaxHighlighter
                                  language="json"
                                  style={docco}
                                  customStyle={{ fontSize: '0.85rem', padding: '0.5rem', margin: 0 }}
                                >
                                  {typeof step.modelInput === 'string' ? step.modelInput : JSON.stringify(step.modelInput, null, 2)}
                                </SyntaxHighlighter>
                              ) : (
                                <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap', fontFamily: '"Roboto Mono", monospace', fontSize: '0.85rem' }}>
                                  {typeof step.modelInput === 'string' ? step.modelInput : JSON.stringify(step.modelInput, null, 2)}
                                </Typography>
                              )}
                              <Tooltip title="Copy to clipboard">
                                <IconButton
                                  size="small"
                                  sx={{ position: 'absolute', top: 4, right: 4 }}
                                  onClick={() => copyToClipboard(typeof step.modelInput === 'string' ? step.modelInput! : JSON.stringify(step.modelInput, null, 2))}
                                >
                                  <FileCopyIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            </Paper>
                          )}
                          
                          {step.modelOutput && (
                            <Paper variant="outlined" sx={{ p: 1, position: 'relative' }}>
                              {showRawData ? (
                                <SyntaxHighlighter
                                  language="json"
                                  style={docco}
                                  customStyle={{ fontSize: '0.85rem', padding: '0.5rem', margin: 0 }}
                                >
                                  {typeof step.modelOutput === 'string' ? step.modelOutput : JSON.stringify(step.modelOutput, null, 2)}
                                </SyntaxHighlighter>
                              ) : (
                                <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap', fontFamily: '"Roboto Mono", monospace', fontSize: '0.85rem' }}>
                                  {typeof step.modelOutput === 'string' ? step.modelOutput : JSON.stringify(step.modelOutput, null, 2)}
                                </Typography>
                              )}
                              <Tooltip title="Copy to clipboard">
                                <IconButton
                                  size="small"
                                  sx={{ position: 'absolute', top: 4, right: 4 }}
                                  onClick={() => copyToClipboard(typeof step.modelOutput === 'string' ? step.modelOutput! : JSON.stringify(step.modelOutput, null, 2))}
                                >
                                  <FileCopyIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            </Paper>
                          )}
                        </Box>
                      )}
                      
                      {/* Metadata */}
                      {step.metadata && Object.keys(step.metadata).length > 0 && (
                        <Box sx={{ mt: 2 }}>
                          <Typography variant="caption" color="text.secondary">
                            Metadata:
                          </Typography>
                          <Paper variant="outlined" sx={{ mt: 0.5, p: 1 }}>
                            {showRawData ? (
                              <SyntaxHighlighter
                                language="json"
                                style={docco}
                                customStyle={{ fontSize: '0.85rem', padding: '0.5rem', margin: 0 }}
                              >
                                {JSON.stringify(step.metadata, null, 2)}
                              </SyntaxHighlighter>
                            ) : (
                              <Box sx={{ pl: 1 }}>
                                {Object.entries(step.metadata).map(([key, value]) => (
                                  <Typography key={key} variant="body2" sx={{ fontSize: '0.85rem', mb: 0.5 }}>
                                    <b>{key}:</b> {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                                  </Typography>
                                ))}
                              </Box>
                            )}
                          </Paper>
                        </Box>
                      )}
                    </Box>
                  </AccordionDetails>
                </Accordion>
              ))}
              <div ref={consoleEndRef} />
            </Box>
          )}
        </Box>
        
        <Box role="tabpanel" hidden={activeTab !== 1} sx={{ height: 'calc(100% - 100px)', overflow: 'auto' }}>
          {/* Session Info Panel */}
          {session && (
            <Paper variant="outlined" sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom>Session Details</Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2">Session ID</Typography>
                <Typography variant="body2">{session.id}</Typography>
              </Box>
              
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2">Query</Typography>
                <Typography variant="body2">{session.query}</Typography>
              </Box>
              
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2">Started</Typography>
                <Typography variant="body2">{new Date(session.startTime).toLocaleString()}</Typography>
              </Box>
              
              {session.endTime && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2">Ended</Typography>
                  <Typography variant="body2">{new Date(session.endTime).toLocaleString()}</Typography>
                </Box>
              )}
              
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2">Duration</Typography>
                <Typography variant="body2">
                  {session.endTime 
                    ? formatDuration(new Date(session.endTime).getTime() - new Date(session.startTime).getTime())
                    : 'In progress...'}
                </Typography>
              </Box>
              
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2">Steps</Typography>
                <Typography variant="body2">{session.steps.length} total steps</Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                  <Chip 
                    size="small" 
                    label={`${session.steps.filter(s => s.type === 'model_call').length} Model Calls`}
                    sx={{ bgcolor: '#E6F7FF' }}
                  />
                  <Chip 
                    size="small" 
                    label={`${session.steps.filter(s => s.type === 'retrieval').length} Retrievals`}
                    sx={{ bgcolor: '#F6FFED' }}
                  />
                  <Chip 
                    size="small" 
                    label={`${session.steps.filter(s => s.type === 'reasoning').length} Reasoning`}
                    sx={{ bgcolor: '#FFFBE6' }}
                  />
                  <Chip 
                    size="small" 
                    label={`${session.steps.filter(s => s.type === 'action').length} Actions`}
                    sx={{ bgcolor: '#E6FFFB' }}
                  />
                  <Chip 
                    size="small" 
                    label={`${session.steps.filter(s => s.type === 'error').length} Errors`}
                    sx={{ bgcolor: '#FFF1F0' }}
                  />
                </Box>
              </Box>
              
              {session.metadata && Object.keys(session.metadata).length > 0 && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>Session Metadata</Typography>
                  <Paper variant="outlined" sx={{ p: 1 }}>
                    {showRawData ? (
                      <SyntaxHighlighter
                        language="json"
                        style={docco}
                        customStyle={{ fontSize: '0.85rem', padding: '0.5rem', margin: 0 }}
                      >
                        {JSON.stringify(session.metadata, null, 2)}
                      </SyntaxHighlighter>
                    ) : (
                      <Box sx={{ pl: 1 }}>
                        {Object.entries(session.metadata).map(([key, value]) => (
                          <Typography key={key} variant="body2" sx={{ fontSize: '0.85rem', mb: 0.5 }}>
                            <b>{key}:</b> {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                          </Typography>
                        ))}
                      </Box>
                    )}
                  </Paper>
                </Box>
              )}
            </Paper>
          )}
        </Box>
        
        <Box role="tabpanel" hidden={activeTab !== 2} sx={{ height: 'calc(100% - 100px)', overflow: 'auto' }}>
          {/* Timeline Panel - would be implemented with a timeline visualization */}
          {session && (
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography>Timeline visualization would be implemented here</Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                This would show a time-based visualization of the debug steps
              </Typography>
            </Paper>
          )}
        </Box>
      </Box>
    </ContentCard>
  );
};

export default DebugConsole; 