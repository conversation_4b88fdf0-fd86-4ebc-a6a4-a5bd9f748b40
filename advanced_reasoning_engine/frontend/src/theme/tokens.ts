/**
 * Design System Tokens for Deep Research Core
 * 
 * This file defines the foundational design tokens used throughout the application.
 * Any changes to this file will affect the entire application design.
 */

// COLORS
export const colors = {
  // Primary colors
  primary: {
    main: '#2563EB',
    light: '#60A5FA',
    dark: '#1E40AF',
    contrastText: '#FFFFFF',
  },
  
  // Secondary colors
  secondary: {
    main: '#7C3AED',
    light: '#A78BFA',
    dark: '#5B21B6',
    contrastText: '#FFFFFF',
  },
  
  // Status colors
  success: {
    main: '#10B981',
    light: '#6EE7B7',
    dark: '#065F46',
    contrastText: '#FFFFFF',
  },
  error: {
    main: '#EF4444',
    light: '#FCA5A5',
    dark: '#B91C1C',
    contrastText: '#FFFFFF',
  },
  warning: {
    main: '#F59E0B',
    light: '#FCD34D',
    dark: '#B45309',
    contrastText: '#000000',
  },
  info: {
    main: '#3B82F6',
    light: '#93C5FD',
    dark: '#1E40AF',
    contrastText: '#FFFFFF',
  },
  
  // Neutral colors
  neutral: {
    50: '#F9FAFB',
    100: '#F3F4F6',
    200: '#E5E7EB',
    300: '#D1D5DB',
    400: '#9CA3AF',
    500: '#6B7280',
    600: '#4B5563',
    700: '#374151',
    800: '#1F2937',
    900: '#111827',
  },

  // Background colors
  background: {
    default: '#FFFFFF',
    paper: '#F9FAFB',
    dark: '#111827',
  },
  
  // Text colors
  text: {
    primary: '#111827',
    secondary: '#4B5563',
    disabled: '#9CA3AF',
    hint: '#6B7280',
    white: '#FFFFFF',
  },
  
  // Action colors
  action: {
    active: 'rgba(0, 0, 0, 0.54)',
    hover: 'rgba(0, 0, 0, 0.04)',
    selected: 'rgba(0, 0, 0, 0.08)',
    disabled: 'rgba(0, 0, 0, 0.26)',
    disabledBackground: 'rgba(0, 0, 0, 0.12)',
  },
  
  // Special purpose colors
  reasoning: {
    react: '#2563EB',
    tot: '#7C3AED',
    cot: '#10B981',
    rag: '#F59E0B',
  },
};

// TYPOGRAPHY
export const typography = {
  fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
  
  // Font weights
  fontWeightLight: 300,
  fontWeightRegular: 400,
  fontWeightMedium: 500,
  fontWeightSemiBold: 600,
  fontWeightBold: 700,
  
  // Headings
  h1: {
    fontWeight: 700,
    fontSize: '2.5rem', // 40px
    lineHeight: 1.2,
    letterSpacing: '-0.01562em',
  },
  h2: {
    fontWeight: 700,
    fontSize: '2rem', // 32px
    lineHeight: 1.2,
    letterSpacing: '-0.00833em',
  },
  h3: {
    fontWeight: 600,
    fontSize: '1.75rem', // 28px
    lineHeight: 1.3,
    letterSpacing: '0',
  },
  h4: {
    fontWeight: 600,
    fontSize: '1.5rem', // 24px
    lineHeight: 1.3,
    letterSpacing: '0.00735em',
  },
  h5: {
    fontWeight: 600,
    fontSize: '1.25rem', // 20px
    lineHeight: 1.4,
    letterSpacing: '0',
  },
  h6: {
    fontWeight: 600,
    fontSize: '1.125rem', // 18px
    lineHeight: 1.4,
    letterSpacing: '0.0075em',
  },
  
  // Body text
  body1: {
    fontWeight: 400,
    fontSize: '1rem', // 16px
    lineHeight: 1.5,
    letterSpacing: '0.00938em',
  },
  body2: {
    fontWeight: 400,
    fontSize: '0.875rem', // 14px
    lineHeight: 1.5,
    letterSpacing: '0.01071em',
  },
  
  // Other text styles
  subtitle1: {
    fontWeight: 500,
    fontSize: '1rem', // 16px
    lineHeight: 1.5,
    letterSpacing: '0.00938em',
  },
  subtitle2: {
    fontWeight: 500,
    fontSize: '0.875rem', // 14px
    lineHeight: 1.5,
    letterSpacing: '0.00714em',
  },
  caption: {
    fontWeight: 400,
    fontSize: '0.75rem', // 12px
    lineHeight: 1.5,
    letterSpacing: '0.03333em',
  },
  button: {
    fontWeight: 600,
    fontSize: '0.875rem', // 14px
    lineHeight: 1.75,
    letterSpacing: '0.02857em',
    textTransform: 'none' as const,
  },
  overline: {
    fontWeight: 500,
    fontSize: '0.75rem', // 12px
    lineHeight: 1.5,
    letterSpacing: '0.08333em',
    textTransform: 'uppercase' as const,
  },
};

// SPACING
export const spacing = {
  unit: 8, // Base spacing unit in pixels
  
  // Named spacing values
  tiny: '4px',
  xxsmall: '8px',
  xsmall: '12px',
  small: '16px',
  medium: '24px',
  large: '32px',
  xlarge: '48px',
  xxlarge: '64px',
  huge: '96px',
};

// BREAKPOINTS
export const breakpoints = {
  xs: 0,
  sm: 600,
  md: 960,
  lg: 1280,
  xl: 1920,
};

// SHAPE
export const shape = {
  borderRadius: {
    none: '0',
    small: '4px',
    medium: '8px',
    large: '12px',
    xl: '16px',
    xxl: '24px',
    full: '9999px',
  },
  border: {
    width: {
      thin: '1px',
      medium: '2px',
      thick: '4px',
    },
    style: {
      solid: 'solid',
      dashed: 'dashed',
      dotted: 'dotted',
    },
  },
};

// SHADOWS
export const shadows = {
  none: 'none',
  xs: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  sm: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  xxl: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
};

// TRANSITIONS
export const transitions = {
  easing: {
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
  },
  duration: {
    shortest: 150,
    shorter: 200,
    short: 250,
    standard: 300,
    complex: 375,
    enteringScreen: 225,
    leavingScreen: 195,
  },
};

// Z-INDEX
export const zIndex = {
  mobileStepper: 1000,
  fab: 1050,
  speedDial: 1050,
  appBar: 1100,
  drawer: 1200,
  modal: 1300,
  snackbar: 1400,
  tooltip: 1500,
};

// Export all tokens together
export const tokens = {
  colors,
  typography,
  spacing,
  breakpoints,
  shape,
  shadows,
  transitions,
  zIndex,
};

export default tokens; 