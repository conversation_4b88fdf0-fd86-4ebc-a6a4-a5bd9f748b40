# Theme System

The theme system in Deep Research Core provides consistent styling across the application with support for light and dark modes. It's built on top of Material-UI's theming capabilities.

## Key Features

- **Light and Dark Mode**: The application supports both light and dark modes, which can be toggled by the user.
- **System Preference Detection**: By default, the theme system detects the user's system preference for light or dark mode.
- **User Preference Persistence**: User theme preferences are persisted in localStorage.
- **Consistent Design Tokens**: Design tokens for colors, typography, spacing, etc. are defined in a centralized location.

## Components

### ThemeContext

The `ThemeContext.tsx` provides theme-related state and functionality throughout the application:

- `mode`: Current theme mode ('light' or 'dark')
- `toggleTheme`: Function to toggle between light and dark mode
- `isDark`: Boolean indicating if dark mode is active

### Design Tokens

Design tokens are defined in `tokens.ts` and include:

- **Colors**: Primary, secondary, status colors, neutral palette
- **Typography**: Font families, weights, sizes, etc.
- **Spacing**: Standardized spacing values
- **Breakpoints**: Screen size breakpoints for responsive design
- **Shadows**: Elevation values
- **Transitions**: Animation timing and easing functions

### Theme Configuration

The actual Material-UI theme is configured in `theme.ts`, which creates light and dark themes using the design tokens.

## Usage

### Using the Theme Provider

Wrap your application with the ThemeProvider:

```tsx
import ThemeProvider from './theme/ThemeContext';

const App = () => (
  <ThemeProvider>
    <YourApp />
  </ThemeProvider>
);
```

### Accessing Theme Variables

Within components, access the current theme:

```tsx
import { useTheme } from '@mui/material';

const MyComponent = () => {
  const theme = useTheme();
  
  return (
    <div style={{ color: theme.palette.primary.main }}>
      Themed content
    </div>
  );
};
```

### Toggling the Theme

Use the theme toggle functionality:

```tsx
import { useThemeMode } from './theme/ThemeContext';

const ThemeSwitcher = () => {
  const { toggleTheme, isDark } = useThemeMode();
  
  return (
    <button onClick={toggleTheme}>
      Switch to {isDark ? 'Light' : 'Dark'} Mode
    </button>
  );
};
```

### ThemeToggle Component

Use the pre-built ThemeToggle component:

```tsx
import { ThemeToggle } from './components/common';
import { useThemeMode } from './theme/ThemeContext';

const MyComponent = () => {
  const { toggleTheme, isDark } = useThemeMode();
  
  return (
    <ThemeToggle 
      onToggle={toggleTheme}
      isDarkMode={isDark}
    />
  );
};
```

## Design System Integration

The theme system is integrated with the overall design system, providing consistent styling for:

- Components
- Typography
- Spacing
- Colors
- Responsive layouts

## Customization

The theme can be customized by modifying:

- `tokens.ts`: Update design tokens
- `theme.ts`: Change theme configuration
- `ThemeContext.tsx`: Alter theme behavior

## Accessibility

The theme system ensures appropriate contrast ratios for both light and dark modes to maintain accessibility standards. 