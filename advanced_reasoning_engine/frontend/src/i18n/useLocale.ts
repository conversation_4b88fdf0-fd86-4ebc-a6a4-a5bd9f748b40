import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage, SupportedLanguage } from './LanguageContext';

/**
 * Custom hook for locale detection and formatting based on the current language
 */
export function useLocale() {
  const { i18n } = useTranslation();
  const { currentLanguage } = useLanguage();
  const [locale, setLocale] = useState<string>(currentLanguage);
  
  // Update locale when language changes
  useEffect(() => {
    setLocale(currentLanguage);
  }, [currentLanguage]);
  
  /**
   * Format a date according to the current locale
   */
  const formatDate = (date: Date | string | number, options?: Intl.DateTimeFormatOptions): string => {
    const dateObj = date instanceof Date ? date : new Date(date);
    
    // Default options
    const defaultOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    };
    
    // Create formatter with the current locale
    const formatter = new Intl.DateTimeFormat(
      getLocaleCode(currentLanguage),
      options || defaultOptions
    );
    
    return formatter.format(dateObj);
  };
  
  /**
   * Format a number according to the current locale
   */
  const formatNumber = (num: number, options?: Intl.NumberFormatOptions): string => {
    // Default options
    const defaultOptions: Intl.NumberFormatOptions = {
      maximumFractionDigits: 2
    };
    
    // Create formatter with the current locale
    const formatter = new Intl.NumberFormat(
      getLocaleCode(currentLanguage),
      options || defaultOptions
    );
    
    return formatter.format(num);
  };
  
  /**
   * Get the locale code for a language
   */
  const getLocaleCode = (language: SupportedLanguage): string => {
    // Map of language codes to locale codes
    const localeMap: Record<SupportedLanguage, string> = {
      en: 'en-US',
      vi: 'vi-VN'
    };
    
    return localeMap[language] || 'en-US';
  };
  
  /**
   * Get the current text direction
   */
  const getDirection = (): 'ltr' | 'rtl' => {
    // Vietnamese and English are both LTR languages
    // (for future support of RTL languages)
    return 'ltr';
  };
  
  return {
    locale,
    formatDate,
    formatNumber,
    getDirection,
    getLocaleCode
  };
}

export default useLocale; 