version: '3.8'

services:
  searxng:
    image: searxng/searxng:latest
    container_name: searxng
    ports:
      - "8080:8080"
    environment:
      - BASE_URL=http://localhost:8080/
      - INSTANCE_NAME=DeepResearchSearXNG
      - AUTOCOMPLETE=google
      - DEFAULT_LANG=vi
      - ENABLE_METRICS=true
      - REDIS_URL=redis://redis:6379/0
      - SEARX_DEBUG=true
      - SEARX_SETTINGS_PATH=/etc/searxng/settings.yml
      - SEARX_ENGINES_TIMEOUT=10.0
      - SEARX_ENGINES_RETRIES=3
      - SEARX_ENGINES_BACKOFF_FACTOR=0.5
      - SEARX_ENGINES_BATCH_SIZE=5
    volumes:
      - searxng_data:/etc/searxng
      - ./searxng:/etc/searxng/custom
    networks:
      - searxng-network
    restart: unless-stopped
    depends_on:
      - redis
    healthcheck:
      test: ["<PERSON><PERSON>", "wget", "-q", "--spider", "http://localhost:8080/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  redis:
    image: "redis:alpine"
    container_name: redis
    command: redis-server --save 60 1 --loglevel warning
    volumes:
      - redis_data:/data
    networks:
      - searxng-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  searxng-network:
    driver: bridge

volumes:
  searxng_data:
  redis_data:
