version: '3.8'

services:
  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - LOG_LEVEL=INFO
      - ENABLE_MONITORING=true
      - PROMETHEUS_PORT=9090
    volumes:
      - ./data:/app/data
    depends_on:
      - milvus-standalone
      - prometheus
      - grafana
      - weaviate
    networks:
      - deep-research-network

  # Reuse existing Milvus services from the original docker-compose.yml
  etcd:
    extends:
      file: docker-compose.yml
      service: etcd
    networks:
      - deep-research-network

  minio:
    extends:
      file: docker-compose.yml
      service: minio
    networks:
      - deep-research-network

  milvus-standalone:
    extends:
      file: docker-compose.yml
      service: standalone
    networks:
      - deep-research-network

  weaviate:
    image: semitechnologies/weaviate:1.19.6
    ports:
      - "8080:8080"
    environment:
      - QUERY_DEFAULTS_LIMIT=20
      - AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED=true
      - PERSISTENCE_DATA_PATH=/var/lib/weaviate
      - DEFAULT_VECTORIZER_MODULE=text2vec-openai
      - ENABLE_MODULES=text2vec-openai
      - OPENAI_APIKEY=${OPENAI_API_KEY}
    volumes:
      - weaviate_data:/var/lib/weaviate
    networks:
      - deep-research-network

  prometheus:
    image: prom/prometheus:v2.43.0
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - deep-research-network

  grafana:
    image: grafana/grafana:9.5.1
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./dashboards:/var/lib/grafana/dashboards
      - ./config/grafana/provisioning:/etc/grafana/provisioning
    networks:
      - deep-research-network
    depends_on:
      - prometheus

networks:
  deep-research-network:
    driver: bridge
  milvus:
    external: true

volumes:
  weaviate_data:
  prometheus_data:
  grafana_data:
