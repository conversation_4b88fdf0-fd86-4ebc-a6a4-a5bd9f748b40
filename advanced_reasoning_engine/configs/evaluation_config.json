{"name": "Deep Research Core Evaluation", "description": "Evaluation configuration for Deep Research Core", "documents": [{"content": "Quantization là kỹ thuật giảm độ chính xác của các trọng số trong mô hình AI để giảm kích thước và tăng tốc độ suy luận. INT8 và INT4 là các mức quantization phổ biến, giảm độ chính xác từ 32-bit (FP32) xuống 8-bit hoặc 4-bit.", "source": "<PERSON><PERSON>i liệu về tối ưu hóa mô hình AI", "title": "<PERSON><PERSON> thuật Quantization", "date": "2023-05-15"}, {"content": "LoRA (Low-Rank Adaptation) là kỹ thuật fine-tuning hiệu quả cho các mô hình ngôn ngữ lớn. Thay vì cập nhật toàn bộ trọng số của mô hình, LoRA chỉ thêm một số ma trận nhỏ có thể huấn luyện được vào các layer của mô hình, giúp giảm đáng kể yêu cầu về bộ nhớ và tính toán.", "source": "<PERSON><PERSON><PERSON><PERSON> c<PERSON><PERSON> về fine-tuning LLM", "title": "<PERSON><PERSON>", "date": "2023-06-20"}, {"content": "PEFT (Parameter-Efficient Fine-Tuning) is a group of methods for fine-tuning large language models by updating only a small subset of parameters. LoRA, Prefix Tuning, and Prompt Tuning are all PEFT techniques. These methods significantly reduce computational and memory requirements compared to full fine-tuning.", "source": "Guide to PEFT", "title": "Overview of PEFT", "date": "2023-07-10"}, {"content": "Chain of Thought (CoT) is a reasoning technique that allows large language models to solve complex problems by breaking them down into intermediate steps. CoT is typically prompted by adding phrases like 'Let's think step by step' to the prompt.", "source": "Research on reasoning techniques", "title": "Chain of Thought", "date": "2023-08-05"}, {"content": "Retrieval-Augmented Generation (RAG) is a technique that combines the ability to retrieve information from an external database with the text generation capabilities of large language models. RAG helps improve the accuracy and up-to-date information in model responses.", "source": "Guide to RAG", "title": "Retrieval-Augmented Generation", "date": "2023-09-15"}, {"content": "Đa ngôn ngữ (Multilingual) là khả năng của mô hình ngôn ngữ để hiểu và xử lý nhiều ngôn ngữ khác nhau. Các mô hình đa ngôn ngữ được huấn luyện trên dữ liệu từ nhiều ngôn ngữ, cho phép chúng hoạt động hiệu quả với nhiều ngôn ngữ mà không cần huấn luyện riêng cho từng ngôn ngữ.", "source": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>u về mô hình đa ngôn ngữ", "title": "<PERSON><PERSON> hình đa ngôn ngữ", "date": "2023-10-20"}, {"content": "Hybrid search kết hợp tìm kiếm vector (vector search) và tìm kiếm từ khóa (keyword search) để cải thiện kết quả tìm kiếm. Phương pháp này tận dụng ưu điểm của cả hai kỹ thuật: tìm kiếm vector hiểu được ngữ nghĩa và ngữ cảnh, trong khi tìm kiếm từ khóa tốt với các thuật ngữ chính xác.", "source": "Hướng dẫn về tìm kiếm hybrid", "title": "<PERSON><PERSON><PERSON> k<PERSON>", "date": "2023-11-10"}], "queries": ["<PERSON><PERSON><PERSON><PERSON> thích về các kỹ thuật tối ưu hóa mô hình AI như quantization, LoRA, và PEFT.", "Tìm kiếm hybrid là gì và nó hoạt động như thế nào?", "<PERSON><PERSON><PERSON><PERSON> thích về mô hình đa ngôn ngữ và ứng dụng của chúng.", "So sánh Chain of Thought và Tree of Thought.", "Retrieval-Augmented Generation (RAG) là gì và nó cải thiện độ chính xác như thế nào?"], "metrics": ["relevance", "accuracy", "completeness", "processing_time"]}