# Advanced Reasoning Engine

Một hệ thống reasoning tiên tiến với khả năng tích hợp nhiều phương pháp suy luận và hỗ trợ tiếng Việt.

## 🚀 Tính năng chính

### Phương pháp Reasoning
- **Chain of Thought (CoT)** - <PERSON><PERSON> luận theo chuỗi tư duy
- **Tree of Thoughts (ToT)** - <PERSON><PERSON> luận theo cây tư duy  
- **ReAct** - Reasoning + Action
- **RAG** - Retrieval-Augmented Generation
- **Graph of Thoughts (GoT)** - <PERSON><PERSON> luận theo đồ thị tư duy

### Tích hợp AI Models
- **Local Models**: Llama, Mistral, Gemma, QwQ-32B, DeepSeek-R1
- **Cloud APIs**: OpenAI, Anthropic, Google, Cohere, OpenRouter
- **Vietnamese Models**: Hỗ trợ tối ưu cho tiếng Việt

### Web Search & Crawling
- **Adaptive Crawler**: <PERSON><PERSON> thập dữ liệu thông minh
- **Multi-engine Search**: <PERSON>r<PERSON><PERSON>, DuckDuckGo, Google
- **Vietnamese Search**: <PERSON><PERSON><PERSON> hợ<PERSON>, BaoMoi, WikiTiengViet
- **File Processing**: PDF, DOCX, XLSX, PPTX, Audio, Video

### RL Tuning & Optimization
- **PPO, DPO, GRPO**: Các phương pháp fine-tuning
- **Reward Models**: Đánh giá chất lượng output
- **Vietnamese RL**: Tối ưu cho ngôn ngữ Việt

## 📁 Cấu trúc thư mục

```
advanced_reasoning_engine/
├── src/deep_research_core/     # Source code chính
│   ├── reasoning/              # Các module reasoning
│   ├── rag/                   # RAG implementations
│   ├── models/                # Model integrations
│   ├── agents/                # Web search agents
│   ├── utils/                 # Utility functions
│   └── config/                # Configuration
├── tests/                     # Test cases
│   ├── unit/                  # Unit tests
│   ├── integration/           # Integration tests
│   └── performance/           # Performance tests
├── docs/                      # Documentation
│   ├── api/                   # API docs
│   ├── guides/                # User guides
│   └── examples/              # Examples
├── scripts/                   # Utility scripts
├── configs/                   # Config templates
└── requirements/              # Dependencies
```

## 🛠️ Cài đặt

### Requirements
```bash
# Cài đặt dependencies cơ bản
pip install -r requirements/requirements.txt

# Cài đặt dependencies đầy đủ
pip install -r requirements/requirements-full.txt

# Cài đặt dependencies development
pip install -r requirements/requirements-dev.txt
```

### Docker Setup
```bash
# Khởi động SearXNG
docker-compose -f configs/docker-compose.searxng.yml up -d

# Khởi động Milvus (optional)
./scripts/setup_milvus.sh
```

## 🚀 Sử dụng nhanh

### Basic Reasoning
```python
from deep_research_core.reasoning import CoTReasoner, ToTReasoner, RAGReasoner

# Chain of Thought
cot = CoTReasoner()
result = cot.reason("Giải thích về AI là gì?")

# Tree of Thoughts  
tot = ToTReasoner()
result = tot.reason("So sánh các phương pháp machine learning")

# RAG
rag = RAGReasoner()
result = rag.reason("Tìm hiểu về luật pháp Việt Nam")
```

### Web Search Agent
```python
from deep_research_core.agents import WebSearchAgentLocal

agent = WebSearchAgentLocal()
results = agent.search("nghiên cứu về AI tại Việt Nam")
```

### Vietnamese Support
```python
from deep_research_core.multilingual import VietnameseEmbeddings
from deep_research_core.reasoning import CoTRAGVietnamese

# Vietnamese embeddings
embeddings = VietnameseEmbeddings()

# Vietnamese reasoning
reasoner = CoTRAGVietnamese()
result = reasoner.reason("Phân tích tình hình kinh tế Việt Nam")
```

## 📚 Documentation

- [API Reference](docs/api_reference.md)
- [User Guide](docs/user_guide.md)
- [Vietnamese Support](docs/vietnamese_support.md)
- [Testing Guide](docs/testing_guide.md)

## 🧪 Testing

```bash
# Chạy tất cả tests
python scripts/run_tests.py

# Chạy unit tests
pytest tests/unit/

# Chạy integration tests  
pytest tests/integration/

# Chạy performance tests
pytest tests/performance/
```

## 🤝 Contributing

1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push to branch
5. Tạo Pull Request

## 📄 License

MIT License - xem file [LICENSE](LICENSE) để biết thêm chi tiết.

## 🔗 Links

- [Documentation](docs/)
- [Examples](docs/examples/)
- [API Reference](docs/api/)
