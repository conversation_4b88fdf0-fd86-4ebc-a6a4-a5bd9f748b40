FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first to leverage Docker cache
COPY requirements.txt .
COPY setup.py .
COPY README.md .

# Install Python dependencies
RUN pip install --no-cache-dir -e .
RUN pip install --no-cache-dir -e ".[monitoring]"
RUN pip install --no-cache-dir -e ".[faiss]"
RUN pip install --no-cache-dir -e ".[milvus]"
RUN pip install --no-cache-dir -e ".[pinecone]"
RUN pip install --no-cache-dir -e ".[weaviate]"

# Copy source code
COPY src/ ./src/
COPY examples/ ./examples/
COPY tests/ ./tests/
COPY docs/ ./docs/

# Create data directory
RUN mkdir -p /app/data

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV DB_PATH=/app/data/deep_research.db
ENV FAISS_INDEX_PATH=/app/data/faiss_index
ENV RAG_TYPE=sqlite

# Expose ports
EXPOSE 8000

# Set entrypoint
ENTRYPOINT ["python", "-m", "src.deep_research_core.api.main"]
