# CoTRAG Enhanced Documentation

This document provides detailed information about the enhanced CoTRAG implementation, including its features, usage examples, and API reference.

## Table of Contents

1. [Introduction](#introduction)
2. [Features](#features)
3. [Architecture](#architecture)
4. [Usage Examples](#usage-examples)
5. [API Reference](#api-reference)
6. [Advanced Configuration](#advanced-configuration)
7. [Performance Optimization](#performance-optimization)
8. [Vietnamese Language Support](#vietnamese-language-support)
9. [Adaptive Learning](#adaptive-learning)
10. [Advanced Strategies](#advanced-strategies)

## Introduction

CoTRAG (Chain of Thought + Retrieval Augmented Generation) is a hybrid approach that combines the reasoning capabilities of Chain of Thought (CoT) with the knowledge retrieval capabilities of Retrieval Augmented Generation (RAG). This enhanced implementation adds several advanced features to improve performance, adaptability, and language support.

## Features

The enhanced CoTRAG implementation includes the following features:

- **Dynamic Weight Adjustment**: Automatically adjusts the balance between CoT and RAG based on query type, complexity, and document relevance.
- **Vietnamese Language Support**: Specialized support for Vietnamese language with dedicated embedding models.
- **Adaptive Learning**: Learns from user feedback to continuously improve performance.
- **Advanced Strategies**: Implements specialized strategies for handling edge cases, such as irrelevant documents.
- **Error Analysis**: Provides detailed error analysis to identify the source of issues.
- **Fallback Mechanisms**: Implements robust fallback mechanisms to handle failures gracefully.

## Architecture

The enhanced CoTRAG implementation consists of the following components:

- **CoTRAG**: The base implementation that combines CoT and RAG.
- **CoTRAGVietnamese**: Extends CoTRAG with Vietnamese language support.
- **CoTRAGAdaptiveLearning**: Extends CoTRAG with adaptive learning capabilities.
- **CoTRAGAdvancedStrategies**: Extends CoTRAG with advanced strategies for handling edge cases.
- **CoTRAGEnhanced**: Integrates all the above components into a comprehensive solution.

## Usage Examples

### Basic Usage

```python
from deep_research_core.reasoning.cotrag_enhanced import CoTRAGEnhanced
from deep_research_core.rag.sqlite_vector_rag import SQLiteVectorRAG

# Initialize vector store
vector_store = SQLiteVectorRAG(
    db_path="path/to/vector_store.db",
    embedding_model="all-MiniLM-L6-v2"
)

# Initialize CoTRAGEnhanced
cotrag = CoTRAGEnhanced(
    provider="openrouter",
    model="anthropic/claude-3-opus",
    vector_store=vector_store,
    use_adaptive_learning=True,
    use_advanced_strategies=True
)

# Process a query
result = cotrag.process("What is artificial intelligence?")

# Print the answer
print(result["answer"])
```

### Vietnamese Language Support

```python
from deep_research_core.reasoning.cotrag_enhanced import CoTRAGEnhanced
from deep_research_core.rag.sqlite_vector_rag import SQLiteVectorRAG

# Initialize vector store
vector_store = SQLiteVectorRAG(
    db_path="path/to/vector_store.db",
    embedding_model="all-MiniLM-L6-v2"
)

# Initialize CoTRAGEnhanced with Vietnamese support
cotrag = CoTRAGEnhanced(
    provider="openrouter",
    model="anthropic/claude-3-opus",
    vector_store=vector_store,
    language="vi",
    vietnamese_embedding_model="phobert"
)

# Process a Vietnamese query
result = cotrag.process("Trí tuệ nhân tạo là gì?")

# Print the answer
print(result["answer"])
```

### Adaptive Learning

```python
from deep_research_core.reasoning.cotrag_enhanced import CoTRAGEnhanced
from deep_research_core.rag.sqlite_vector_rag import SQLiteVectorRAG

# Initialize vector store
vector_store = SQLiteVectorRAG(
    db_path="path/to/vector_store.db",
    embedding_model="all-MiniLM-L6-v2"
)

# Initialize CoTRAGEnhanced with adaptive learning
cotrag = CoTRAGEnhanced(
    provider="openrouter",
    model="anthropic/claude-3-opus",
    vector_store=vector_store,
    use_adaptive_learning=True,
    learning_rate=0.05,
    feedback_history_path="feedback_history.json"
)

# Process a query
result = cotrag.process("What is artificial intelligence?")

# Add feedback
cotrag.add_feedback(
    query="What is artificial intelligence?",
    result=result,
    feedback_score=0.9,  # 0.0 (poor) to 1.0 (excellent)
    feedback_type="user",
    feedback_notes="Good explanation of AI concepts"
)

# Adjust parameters based on feedback
cotrag.adjust_parameters()

# Get parameter statistics
stats = cotrag.get_parameter_stats()
print(stats)
```

### Advanced Strategies

```python
from deep_research_core.reasoning.cotrag_enhanced import CoTRAGEnhanced
from deep_research_core.rag.sqlite_vector_rag import SQLiteVectorRAG

# Initialize vector store
vector_store = SQLiteVectorRAG(
    db_path="path/to/vector_store.db",
    embedding_model="all-MiniLM-L6-v2"
)

# Initialize CoTRAGEnhanced with advanced strategies
cotrag = CoTRAGEnhanced(
    provider="openrouter",
    model="anthropic/claude-3-opus",
    vector_store=vector_store,
    use_advanced_strategies=True,
    use_query_expansion=True,
    use_iterative_retrieval=True,
    use_fallback_strategies=True
)

# Process a query
result = cotrag.process("What is artificial intelligence?")

# Check if advanced strategies were used
if "advanced_strategies" in result:
    print("Advanced strategies used:", result["advanced_strategies"])
```

## API Reference

### CoTRAGEnhanced

```python
class CoTRAGEnhanced:
    def __init__(
        self,
        provider: str = "openai",
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        vector_store=None,
        language: str = "en",
        vietnamese_embedding_model: str = "phobert",
        use_adaptive_learning: bool = True,
        use_advanced_strategies: bool = True,
        learning_rate: float = 0.01,
        feedback_history_path: Optional[str] = None,
        use_query_expansion: bool = True,
        use_iterative_retrieval: bool = True,
        use_fallback_strategies: bool = True,
        adaptive: bool = True,
        use_cache: bool = True,
        evaluate_results: bool = False,
        use_dynamic_weighting: bool = True,
        min_cot_weight: float = 0.3,
        max_cot_weight: float = 0.8,
        default_cot_weight: float = 0.5,
        weighting_strategy: str = "auto",
        handle_irrelevant_docs: bool = True,
        relevance_threshold: float = 0.3,
        analyze_errors: bool = False,
        verbose: bool = False,
        device: Optional[str] = None,
        cache_dir: Optional[str] = None
    )
    
    def process(
        self,
        query: str,
        top_k: int = 5,
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        callback: Optional[Callable[[str], None]] = None,
        force_refresh: bool = False,
        expected_answer: Optional[str] = None,
        auto_feedback: bool = False
    ) -> Dict[str, Any]
    
    def add_feedback(
        self,
        query: str,
        result: Dict[str, Any],
        feedback_score: float,
        feedback_type: str = "user",
        feedback_notes: Optional[str] = None
    ) -> None
    
    def adjust_parameters(self) -> Dict[str, Any]
    
    def get_parameter_stats(self) -> Dict[str, Any]
    
    def analyze_feedback_trends(self) -> Dict[str, Any]
    
    def analyze_query(self, query: str) -> Dict[str, Any]
    
    def retrieve(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]
```

## Advanced Configuration

### Dynamic Weight Adjustment

The dynamic weight adjustment feature automatically balances the contribution of CoT and RAG based on various factors:

- **Query Type**: Factual queries rely more on RAG, while reasoning queries rely more on CoT.
- **Query Complexity**: Complex queries rely more on CoT, while simple queries rely more on RAG.
- **Document Relevance**: When retrieved documents are highly relevant, RAG is weighted more heavily.

You can configure this feature using the following parameters:

- `use_dynamic_weighting`: Whether to use dynamic weighting (default: True).
- `min_cot_weight`: Minimum weight for CoT (0.0 to 1.0, default: 0.3).
- `max_cot_weight`: Maximum weight for CoT (0.0 to 1.0, default: 0.8).
- `default_cot_weight`: Default weight for CoT (0.0 to 1.0, default: 0.5).
- `weighting_strategy`: Weight adjustment strategy ("auto", "query_type", "query_complexity", "document_relevance").

### Handling Irrelevant Documents

The system can detect when RAG returns irrelevant documents and adjust its approach accordingly:

- `handle_irrelevant_docs`: Whether to handle cases when RAG returns irrelevant documents (default: True).
- `relevance_threshold`: Threshold for document relevance (0.0 to 1.0, default: 0.3).

### Error Analysis

The error analysis feature helps identify the source of issues in the results:

- `analyze_errors`: Whether to analyze errors in results (default: False).

## Performance Optimization

### Caching

The system can cache results to improve performance for repeated queries:

- `use_cache`: Whether to use caching for repeated queries (default: True).

### Adaptive Parameter Adjustment

The system can automatically adjust parameters based on feedback:

- `adaptive`: Whether to use adaptive parameter adjustment (default: True).
- `learning_rate`: Rate at which parameters are adjusted based on feedback (default: 0.01).

## Vietnamese Language Support

The Vietnamese language support feature provides specialized support for Vietnamese language:

- `language`: Language to use ("en", "vi", default: "en").
- `vietnamese_embedding_model`: Name of the Vietnamese embedding model to use ("phobert", "viebert", "xlm-roberta-vi", "multilingual-e5", default: "phobert").
- `device`: Device to use for inference ("cpu", "cuda", "mps", default: None).
- `cache_dir`: Directory to cache models (default: None).

## Adaptive Learning

The adaptive learning feature allows the system to learn from user feedback:

- `use_adaptive_learning`: Whether to use adaptive learning (default: True).
- `learning_rate`: Rate at which parameters are adjusted based on feedback (default: 0.01).
- `feedback_history_path`: Path to store feedback history (default: "cotrag_feedback_history.json").
- `max_history_size`: Maximum number of feedback entries to store (default: 1000).
- `auto_adjust_interval`: Number of feedback entries before auto-adjustment (default: 50).
- `min_samples_for_adjustment`: Minimum number of samples required for adjustment (default: 10).

## Advanced Strategies

The advanced strategies feature implements specialized strategies for handling edge cases:

- `use_advanced_strategies`: Whether to use advanced strategies (default: True).
- `use_query_expansion`: Whether to use query expansion for retrieval (default: True).
- `use_iterative_retrieval`: Whether to use iterative retrieval (default: True).
- `use_fallback_strategies`: Whether to use fallback strategies (default: True).
- `max_retrieval_iterations`: Maximum number of retrieval iterations (default: 2).
- `query_expansion_model`: Model to use for query expansion (default: None, uses the same model as the main model).
- `fallback_providers`: List of fallback providers to try (default: ["openai", "anthropic", "openrouter"]).
