# Vietnamese Reward Functions Guide

## Introduction

Deep Research Core provides specialized reward functions for optimizing language models for Vietnamese language using Generalized Reward-based Policy Optimization (GRPO). These reward functions are designed to evaluate and improve the quality of Vietnamese text generation, taking into account the unique characteristics of the Vietnamese language such as diacritics, compound words, dialects, and grammar.

This guide provides information on how to use the various Vietnamese reward functions available in the Deep Research Core library.

## Available Vietnamese Reward Functions

The Deep Research Core library supports the following Vietnamese reward functions:

1. **VietnameseDiacriticRewardFunction**: Evaluates the consistency of diacritic usage in Vietnamese text
2. **VietnameseCompoundWordRewardFunction**: Evaluates the correct usage of compound words in Vietnamese text
3. **VietnameseDialectConsistencyRewardFunction**: Evaluates the consistency of dialect usage in Vietnamese text
4. **VietnameseGrammarRewardFunction**: Evaluates the grammatical correctness of Vietnamese text
5. **VietnameseEvaluatorRewardFunction**: Uses the VietnameseRLEvaluator to compute comprehensive rewards
6. **VietnameseCombinedRewardFunction**: Combines multiple Vietnamese-specific reward functions

## Using Vietnamese Reward Functions

### Basic Usage

To use Vietnamese reward functions with GRPO, you need to initialize them and pass them to the GRPO provider:

```python
from deep_research_core.rl_tuning.providers import get_grpo_provider
from deep_research_core.rl_tuning.grpo import (
    VietnameseDiacriticRewardFunction,
    VietnameseCompoundWordRewardFunction,
    VietnameseDialectConsistencyRewardFunction,
    VietnameseGrammarRewardFunction
)

# Define Vietnamese reward functions
reward_functions = {
    "diacritic": VietnameseDiacriticRewardFunction(),
    "compound_word": VietnameseCompoundWordRewardFunction(),
    "dialect": VietnameseDialectConsistencyRewardFunction(dialect="northern"),
    "grammar": VietnameseGrammarRewardFunction()
}

# Initialize GRPO provider
grpo = get_grpo_provider(
    model_name="command",
    provider_type="cohere",
    api_key="your_api_key",
    output_dir="./grpo_output",
    reward_functions=reward_functions
)
```

### Using the Vietnamese Evaluator Reward Function

The `VietnameseEvaluatorRewardFunction` uses the `VietnameseRLEvaluator` to compute comprehensive rewards for Vietnamese text:

```python
from deep_research_core.rl_tuning.grpo import VietnameseEvaluatorRewardFunction

# Define Vietnamese evaluator reward function
reward_functions = {
    "evaluator": VietnameseEvaluatorRewardFunction(
        embedding_model="phobert",  # Vietnamese embedding model
        metrics=["response_quality", "consistency", "performance"],
        weights={
            "response_quality": 0.3,
            "consistency": 0.2,
            "performance": 0.5
        }
    )
}
```

### Using the Combined Reward Function

The `VietnameseCombinedRewardFunction` combines multiple Vietnamese-specific reward functions:

```python
from deep_research_core.rl_tuning.grpo import VietnameseCombinedRewardFunction

# Define Vietnamese combined reward function
reward_functions = {
    "vietnamese": VietnameseCombinedRewardFunction(
        reward_functions={
            "diacritic": VietnameseDiacriticRewardFunction(),
            "compound_word": VietnameseCompoundWordRewardFunction(),
            "dialect": VietnameseDialectConsistencyRewardFunction(),
            "grammar": VietnameseGrammarRewardFunction()
        },
        weights={
            "diacritic": 0.3,
            "compound_word": 0.2,
            "dialect": 0.2,
            "grammar": 0.3
        }
    )
}
```

## Integrating with RL-Tuning Adapters

You can also use Vietnamese reward functions with RL-tuning adapters:

```python
from deep_research_core.rl_tuning.providers import get_grpo_provider
from deep_research_core.rl_tuning.grpo import VietnameseCombinedRewardFunction
from deep_research_core.rl_tuning.adapters import VerlGRPOAdapter

# Define Vietnamese reward functions
reward_functions = {
    "vietnamese": VietnameseCombinedRewardFunction()
}

# Initialize GRPO provider
grpo = get_grpo_provider(
    model_name="command",
    provider_type="cohere",
    api_key="your_api_key",
    output_dir="./grpo_output",
    reward_functions=reward_functions
)

# Initialize Verl adapter
adapter = VerlGRPOAdapter(
    grpo_instance=grpo,
    agent_type="ppo",
    seed=42
)

# Train with Verl
adapter.train(
    train_dataset=train_dataset,
    eval_dataset=eval_dataset,
    num_epochs=3,
    batch_size=4,
    learning_rate=5e-5
)
```

## Detailed Function Descriptions

### VietnameseDiacriticRewardFunction

This reward function evaluates the consistency of diacritic usage in Vietnamese text. It checks whether diacritics are used consistently throughout the text.

```python
VietnameseDiacriticRewardFunction(
    min_reward=0.0,
    max_reward=1.0,
    vietnamese_chars=None  # Optional set of Vietnamese characters with diacritics
)
```

### VietnameseCompoundWordRewardFunction

This reward function evaluates the correct usage of compound words in Vietnamese text. It checks whether common Vietnamese compound words are used correctly.

```python
VietnameseCompoundWordRewardFunction(
    min_reward=0.0,
    max_reward=1.0,
    compound_words=None  # Optional list of common Vietnamese compound words
)
```

### VietnameseDialectConsistencyRewardFunction

This reward function evaluates the consistency of dialect usage in Vietnamese text. It checks whether a specific Vietnamese dialect (northern, central, or southern) is used consistently.

```python
VietnameseDialectConsistencyRewardFunction(
    min_reward=0.0,
    max_reward=1.0,
    dialect="northern",  # Target dialect ('northern', 'central', 'southern')
    dialect_markers=None  # Optional dictionary mapping dialects to their marker words
)
```

### VietnameseGrammarRewardFunction

This reward function evaluates the grammatical correctness of Vietnamese text. It checks for proper sentence structure, punctuation, and capitalization.

```python
VietnameseGrammarRewardFunction(
    min_reward=0.0,
    max_reward=1.0
)
```

### VietnameseEvaluatorRewardFunction

This reward function uses the `VietnameseRLEvaluator` to compute comprehensive rewards for Vietnamese text. It can evaluate various aspects of Vietnamese text quality.

```python
VietnameseEvaluatorRewardFunction(
    embedding_model=None,  # Name of the embedding model to use
    metrics=None,  # List of metrics to evaluate
    weights=None,  # Dictionary mapping metrics to their weights
    min_reward=0.0,
    max_reward=1.0
)
```

### VietnameseCombinedRewardFunction

This reward function combines multiple Vietnamese-specific reward functions. It allows you to use multiple reward functions together with custom weights.

```python
VietnameseCombinedRewardFunction(
    reward_functions=None,  # Dictionary mapping names to reward functions
    weights=None,  # Dictionary mapping names to weights
    min_reward=0.0,
    max_reward=1.0
)
```

## Example: Training with Vietnamese Reward Functions

Here's a complete example of training a model with Vietnamese reward functions:

```python
import os
from deep_research_core.rl_tuning.providers import get_grpo_provider
from deep_research_core.rl_tuning.grpo import VietnameseCombinedRewardFunction

# Define Vietnamese reward functions
reward_functions = {
    "vietnamese": VietnameseCombinedRewardFunction()
}

# Initialize GRPO provider
grpo = get_grpo_provider(
    model_name="command",
    provider_type="cohere",
    api_key=os.environ.get("COHERE_API_KEY"),
    output_dir="./vietnamese_grpo_output",
    reward_functions=reward_functions
)

# Prepare Vietnamese data
train_data = [
    {
        "prompt": "Giới thiệu về Hà Nội.",
        "reference": "Hà Nội là thủ đô của Việt Nam, một thành phố với lịch sử hơn 1000 năm."
    },
    {
        "prompt": "Mô tả về ẩm thực Việt Nam.",
        "reference": "Ẩm thực Việt Nam nổi tiếng với sự cân bằng giữa các nguyên liệu tươi."
    }
]

# Prepare datasets
train_dataset, _ = grpo.prepare_data(train_data)

# Train the model
grpo.train(
    train_dataset=train_dataset,
    num_epochs=3,
    batch_size=2,
    learning_rate=5e-5
)

# Generate text
response = grpo.generate_text(
    prompt="Mô tả về văn hóa cà phê ở Việt Nam.",
    max_length=200,
    temperature=0.7
)
```

## Conclusion

The Vietnamese reward functions provided by Deep Research Core allow you to optimize language models specifically for Vietnamese language. By using these reward functions, you can improve the quality of Vietnamese text generation, taking into account the unique characteristics of the Vietnamese language.

For more information, refer to the API documentation and example scripts in the Deep Research Core library.
