# RL-Tuning Framework Adapters Guide

## Introduction

The Deep Research Core library provides adapters for integrating GRPO (Generalized Reward-based Policy Optimization) with various reinforcement learning frameworks. These adapters allow you to leverage the capabilities of specialized RL frameworks while using the GRPO providers for model access and reward computation.

This guide provides information on how to use the various RL framework adapters available in the Deep Research Core library.

## Available Adapters

The Deep Research Core library supports the following RL framework adapters:

1. **VerlGRPOAdapter**: For integrating with the Verl framework
2. **TinyZeroGRPOAdapter**: For integrating with the TinyZero framework
3. **OpenR1GRPOAdapter**: For integrating with the OpenR1 framework
4. **TrlxGRPOAdapter**: For integrating with the Trlx framework

## Common Workflow

All adapters follow a similar workflow:

1. **Initialize a GRPO Provider**: Create a GRPO provider instance for the model you want to use
2. **Initialize the Adapter**: Create an adapter instance with the GRPO provider
3. **Initialize the Agent**: Initialize the RL agent with the desired parameters
4. **Prepare Data**: Prepare training and evaluation data
5. **Train the Model**: Train the model using the adapter
6. **Generate Text**: Use the trained model to generate text
7. **Evaluate the Model**: Evaluate the model's performance
8. **Save and Load the Model**: Save and load the trained model

## VerlGRPOAdapter

The VerlGRPOAdapter integrates GRPO with the Verl framework, which provides implementations of PPO, DPO, and SFT algorithms.

### Example Usage

```python
from deep_research_core.rl_tuning.providers import get_grpo_provider
from deep_research_core.rl_tuning.adapters import VerlGRPOAdapter

# Initialize GRPO provider
grpo = get_grpo_provider(
    model_name="command",
    provider_type="cohere",
    api_key="your_api_key",
    output_dir="./grpo_output"
)

# Prepare data
train_dataset, eval_dataset = grpo.prepare_data(train_data, eval_data)

# Initialize Verl adapter
adapter = VerlGRPOAdapter(
    grpo_instance=grpo,
    agent_type="ppo",
    seed=42
)

# Initialize agent
adapter.initialize_agent(
    model_name="command",
    learning_rate=5e-5
)

# Train with Verl
metrics = adapter.train(
    train_dataset=train_dataset,
    eval_dataset=eval_dataset,
    num_epochs=3,
    batch_size=4,
    learning_rate=5e-5
)

# Generate text with the trained model
response = adapter.generate_text(
    prompt="Explain how neural networks learn.",
    max_length=200,
    temperature=0.7
)

# Evaluate the model
eval_metrics = adapter.evaluate(eval_dataset)

# Save the model
save_path = adapter.save_model()

# Load the model
adapter.load_model(save_path)
```

## TinyZeroGRPOAdapter

The TinyZeroGRPOAdapter integrates GRPO with the TinyZero framework, which provides lightweight implementations of PPO, DPO, and SFT algorithms.

### Example Usage

```python
from deep_research_core.rl_tuning.providers import get_grpo_provider
from deep_research_core.rl_tuning.adapters import TinyZeroGRPOAdapter

# Initialize GRPO provider
grpo = get_grpo_provider(
    model_name="mistral-small",
    provider_type="mistral",
    api_key="your_api_key",
    output_dir="./grpo_output"
)

# Prepare data
train_dataset, eval_dataset = grpo.prepare_data(train_data, eval_data)

# Initialize TinyZero adapter
adapter = TinyZeroGRPOAdapter(
    grpo_instance=grpo,
    algorithm="ppo",
    seed=42
)

# Initialize agent
adapter.initialize_agent(
    model_name="mistral-small",
    learning_rate=5e-5
)

# Train with TinyZero
metrics = adapter.train(
    train_dataset=train_dataset,
    eval_dataset=eval_dataset,
    num_epochs=3,
    batch_size=4,
    learning_rate=5e-5
)

# Generate text with the trained model
response = adapter.generate_text(
    prompt="Explain how neural networks learn.",
    max_length=200,
    temperature=0.7
)

# Evaluate the model
eval_metrics = adapter.evaluate(eval_dataset)

# Save the model
save_path = adapter.save_model()

# Load the model
adapter.load_model(save_path)
```

## OpenR1GRPOAdapter

The OpenR1GRPOAdapter integrates GRPO with the OpenR1 framework, which provides implementations of PPO, DPO, and SFT algorithms with a focus on open-source models.

### Example Usage

```python
from deep_research_core.rl_tuning.providers import get_grpo_provider
from deep_research_core.rl_tuning.adapters import OpenR1GRPOAdapter

# Initialize GRPO provider
grpo = get_grpo_provider(
    model_name="gemini-pro",
    provider_type="gemini",
    api_key="your_api_key",
    output_dir="./grpo_output"
)

# Prepare data
train_dataset, eval_dataset = grpo.prepare_data(train_data, eval_data)

# Initialize OpenR1 adapter
adapter = OpenR1GRPOAdapter(
    grpo_instance=grpo,
    algorithm="ppo",
    seed=42
)

# Initialize agent
adapter.initialize_agent(
    model_name="gemini-pro",
    learning_rate=5e-5
)

# Train with OpenR1
metrics = adapter.train(
    train_dataset=train_dataset,
    eval_dataset=eval_dataset,
    num_epochs=3,
    batch_size=4,
    learning_rate=5e-5
)

# Generate text with the trained model
response = adapter.generate_text(
    prompt="Explain how neural networks learn.",
    max_length=200,
    temperature=0.7
)

# Evaluate the model
eval_metrics = adapter.evaluate(eval_dataset)

# Save the model
save_path = adapter.save_model()

# Load the model
adapter.load_model(save_path)
```

## TrlxGRPOAdapter

The TrlxGRPOAdapter integrates GRPO with the Trlx framework, which provides implementations of PPO and SFT algorithms with a focus on transformer models.

### Example Usage

```python
from deep_research_core.rl_tuning.providers import get_grpo_provider
from deep_research_core.rl_tuning.adapters import TrlxGRPOAdapter

# Initialize GRPO provider
grpo = get_grpo_provider(
    model_name="command",
    provider_type="cohere",
    api_key="your_api_key",
    output_dir="./grpo_output"
)

# Prepare data
train_dataset, eval_dataset = grpo.prepare_data(train_data, eval_data)

# Initialize Trlx adapter
adapter = TrlxGRPOAdapter(
    grpo_instance=grpo,
    algorithm="ppo",
    seed=42
)

# Create Trlx configuration
adapter.create_config(
    model_name="command",
    num_epochs=3,
    batch_size=4,
    learning_rate=5e-5,
    seq_length=512,
    total_steps=1000
)

# Train with Trlx
metrics = adapter.train(
    train_dataset=train_dataset,
    eval_dataset=eval_dataset
)

# Generate text with the trained model
response = adapter.generate_text(
    prompt="Explain how neural networks learn.",
    max_length=200,
    temperature=0.7
)

# Save the model
save_path = adapter.save_model()

# Load the model
adapter.load_model(save_path)
```

## Advanced Usage

### Custom Reward Functions

You can use custom reward functions with the adapters by defining them in the GRPO provider:

```python
from deep_research_core.rl_tuning.grpo import RewardFunction

class CustomRewardFunction(RewardFunction):
    def __init__(self, param1, param2):
        self.param1 = param1
        self.param2 = param2
    
    def __call__(self, response, reference=None, prompt=None):
        # Implement your custom reward logic here
        reward = ...
        return reward

# Define reward functions
reward_functions = {
    "custom": CustomRewardFunction(param1=1.0, param2=2.0)
}

# Initialize GRPO provider with custom reward functions
grpo = get_grpo_provider(
    model_name="command",
    provider_type="cohere",
    api_key="your_api_key",
    output_dir="./grpo_output",
    reward_functions=reward_functions
)
```

### Adapter-Specific Parameters

Each adapter supports additional parameters specific to the underlying framework:

#### VerlGRPOAdapter

```python
adapter = VerlGRPOAdapter(
    grpo_instance=grpo,
    agent_type="ppo",
    seed=42,
    device="cuda",
    config={
        "ppo_epochs": 4,
        "clip_range": 0.2,
        "value_loss_coef": 0.1,
        "entropy_coef": 0.01
    }
)
```

#### TinyZeroGRPOAdapter

```python
adapter = TinyZeroGRPOAdapter(
    grpo_instance=grpo,
    algorithm="ppo",
    seed=42,
    device="cuda",
    config={
        "ppo_epochs": 4,
        "clip_range": 0.2,
        "value_loss_coef": 0.1,
        "entropy_coef": 0.01
    }
)
```

#### OpenR1GRPOAdapter

```python
adapter = OpenR1GRPOAdapter(
    grpo_instance=grpo,
    algorithm="ppo",
    seed=42,
    device="cuda",
    config={
        "ppo_epochs": 4,
        "clip_range": 0.2,
        "value_loss_coef": 0.1,
        "entropy_coef": 0.01
    }
)
```

#### TrlxGRPOAdapter

```python
adapter = TrlxGRPOAdapter(
    grpo_instance=grpo,
    algorithm="ppo",
    seed=42,
    device="cuda"
)

adapter.create_config(
    model_name="command",
    num_epochs=3,
    batch_size=4,
    learning_rate=5e-5,
    seq_length=512,
    total_steps=1000,
    ppo_epochs=4,
    init_kl_coef=0.05,
    target=6.0,
    horizon=10000,
    gamma=1.0,
    lam=0.95,
    cliprange=0.2,
    cliprange_value=0.2,
    vf_coef=0.1
)
```

## Conclusion

The RL-Tuning Framework Adapters provide a flexible way to integrate GRPO with various reinforcement learning frameworks. By following the examples in this guide, you can use these adapters to train models from different providers using specialized RL algorithms.

For more information, refer to the API documentation and example scripts in the Deep Research Core library.
