# GRPO Providers Guide

## Introduction

Generalized Reward-based Policy Optimization (GRPO) is a flexible framework for optimizing language models using reward functions. This guide provides information on how to use the various GRPO providers available in the Deep Research Core library.

## Available Providers

The Deep Research Core library supports the following GRPO providers:

1. **HuggingFaceGRPO**: For optimizing local Hugging Face models
2. **OpenRouterGRPO**: For optimizing models through OpenRouter API
3. **DeepSeekGRPO**: For optimizing DeepSeek models
4. **QwQGRPO**: For optimizing QwQ models
5. **CohereGRPO**: For optimizing Cohere models
6. **MistralGRPO**: For optimizing Mistral AI models
7. **GeminiGRPO**: For optimizing Google Gemini models

## Provider Selection

You can select a provider in two ways:

1. **Explicit Provider Selection**: Specify the provider type when calling `get_grpo_provider`
2. **Automatic Provider Selection**: Let the system infer the provider from the model name

### Explicit Provider Selection

```python
from deep_research_core.rl_tuning.providers import get_grpo_provider

# Initialize a Cohere GRPO provider
cohere_grpo = get_grpo_provider(
    model_name="command",
    provider_type="cohere",
    api_key="your_api_key",
    output_dir="./grpo_output"
)

# Initialize a Mistral GRPO provider
mistral_grpo = get_grpo_provider(
    model_name="mistral-medium",
    provider_type="mistral",
    api_key="your_api_key",
    output_dir="./grpo_output"
)

# Initialize a Gemini GRPO provider
gemini_grpo = get_grpo_provider(
    model_name="gemini-pro",
    provider_type="gemini",
    api_key="your_api_key",
    output_dir="./grpo_output"
)
```

### Automatic Provider Selection

```python
from deep_research_core.rl_tuning.providers import get_grpo_provider

# The provider will be inferred from the model name
cohere_grpo = get_grpo_provider(
    model_name="command",
    api_key="your_api_key",
    output_dir="./grpo_output"
)

# The provider will be inferred from the model name
mistral_grpo = get_grpo_provider(
    model_name="mistral-medium",
    api_key="your_api_key",
    output_dir="./grpo_output"
)

# The provider will be inferred from the model name
gemini_grpo = get_grpo_provider(
    model_name="gemini-pro",
    api_key="your_api_key",
    output_dir="./grpo_output"
)
```

## API Keys

Each provider requires an API key. You can provide the API key in two ways:

1. **Environment Variables**: Set the appropriate environment variable for each provider
2. **Direct Parameter**: Pass the API key directly to the `get_grpo_provider` function

### Environment Variables

- **Cohere**: `COHERE_API_KEY`
- **Mistral**: `MISTRAL_API_KEY`
- **Gemini**: `GEMINI_API_KEY`
- **OpenRouter**: `OPENROUTER_API_KEY`
- **DeepSeek**: `DEEPSEEK_API_KEY`
- **QwQ**: `QWQ_API_KEY`

### Direct Parameter

```python
cohere_grpo = get_grpo_provider(
    model_name="command",
    provider_type="cohere",
    api_key="your_cohere_api_key",
    output_dir="./grpo_output"
)
```

## Basic Usage

All GRPO providers follow the same basic workflow:

1. **Initialize the Provider**: Create a GRPO provider instance
2. **Prepare Data**: Prepare training and evaluation data
3. **Optimize Policy**: Run the optimization process
4. **Generate Text**: Use the optimized model to generate text
5. **Save Model**: Save the optimization results

### Example with Cohere

```python
import os
from deep_research_core.rl_tuning.providers import get_grpo_provider
from deep_research_core.rl_tuning.grpo import (
    BLEURewardFunction, ROUGERewardFunction, EmbeddingRewardFunction
)
from sentence_transformers import SentenceTransformer

# Initialize embedding model for reward function
embedding_model = SentenceTransformer('all-MiniLM-L6-v2')

# Define reward functions
reward_functions = {
    "bleu": BLEURewardFunction(),
    "rouge": ROUGERewardFunction(),
    "embedding": EmbeddingRewardFunction(embedding_fn=lambda x: embedding_model.encode(x))
}

# Initialize GRPO provider
cohere_grpo = get_grpo_provider(
    model_name="command",
    provider_type="cohere",
    api_key=os.environ.get("COHERE_API_KEY"),
    output_dir="./grpo_cohere_output",
    reward_functions=reward_functions
)

# Prepare data
train_data = [
    {
        "prompt": "Explain reinforcement learning.",
        "reference": "Reinforcement learning is a type of machine learning where an agent learns to make decisions by taking actions in an environment to maximize rewards."
    },
    {
        "prompt": "What is natural language processing?",
        "reference": "Natural language processing (NLP) is a field of AI that enables computers to understand, interpret, and generate human language."
    }
]

train_dataset, _ = cohere_grpo.prepare_data(train_data)

# Optimize policy
results = cohere_grpo.optimize_policy(
    train_dataset=train_dataset,
    num_epochs=3,
    batch_size=2,
    learning_rate=5e-5
)

# Generate text with the optimized model
prompt = "Explain how neural networks learn."
response = cohere_grpo.generate_text(prompt, max_length=200)
print(response)

# Save the model
save_path = cohere_grpo.save_model()
print(f"Saved optimization results to: {save_path}")
```

### Example with Mistral

```python
import os
from deep_research_core.rl_tuning.providers import get_grpo_provider
from deep_research_core.rl_tuning.grpo import (
    BLEURewardFunction, ROUGERewardFunction, EmbeddingRewardFunction
)
from sentence_transformers import SentenceTransformer

# Initialize embedding model for reward function
embedding_model = SentenceTransformer('all-MiniLM-L6-v2')

# Define reward functions
reward_functions = {
    "bleu": BLEURewardFunction(),
    "rouge": ROUGERewardFunction(),
    "embedding": EmbeddingRewardFunction(embedding_fn=lambda x: embedding_model.encode(x))
}

# Initialize GRPO provider
mistral_grpo = get_grpo_provider(
    model_name="mistral-medium",
    provider_type="mistral",
    api_key=os.environ.get("MISTRAL_API_KEY"),
    output_dir="./grpo_mistral_output",
    reward_functions=reward_functions
)

# Prepare data
train_data = [
    {
        "prompt": "Explain reinforcement learning.",
        "reference": "Reinforcement learning is a type of machine learning where an agent learns to make decisions by taking actions in an environment to maximize rewards."
    },
    {
        "prompt": "What is natural language processing?",
        "reference": "Natural language processing (NLP) is a field of AI that enables computers to understand, interpret, and generate human language."
    }
]

train_dataset, _ = mistral_grpo.prepare_data(train_data)

# Optimize policy
results = mistral_grpo.optimize_policy(
    train_dataset=train_dataset,
    num_epochs=3,
    batch_size=2,
    learning_rate=5e-5
)

# Generate text with the optimized model
prompt = "Explain how neural networks learn."
response = mistral_grpo.generate_text(prompt, max_length=200)
print(response)

# Save the model
save_path = mistral_grpo.save_model()
print(f"Saved optimization results to: {save_path}")
```

### Example with Gemini

```python
import os
from deep_research_core.rl_tuning.providers import get_grpo_provider
from deep_research_core.rl_tuning.grpo import (
    BLEURewardFunction, ROUGERewardFunction, EmbeddingRewardFunction
)
from sentence_transformers import SentenceTransformer

# Initialize embedding model for reward function
embedding_model = SentenceTransformer('all-MiniLM-L6-v2')

# Define reward functions
reward_functions = {
    "bleu": BLEURewardFunction(),
    "rouge": ROUGERewardFunction(),
    "embedding": EmbeddingRewardFunction(embedding_fn=lambda x: embedding_model.encode(x))
}

# Initialize GRPO provider
gemini_grpo = get_grpo_provider(
    model_name="gemini-pro",
    provider_type="gemini",
    api_key=os.environ.get("GEMINI_API_KEY"),
    output_dir="./grpo_gemini_output",
    reward_functions=reward_functions
)

# Prepare data
train_data = [
    {
        "prompt": "Explain reinforcement learning.",
        "reference": "Reinforcement learning is a type of machine learning where an agent learns to make decisions by taking actions in an environment to maximize rewards."
    },
    {
        "prompt": "What is natural language processing?",
        "reference": "Natural language processing (NLP) is a field of AI that enables computers to understand, interpret, and generate human language."
    }
]

train_dataset, _ = gemini_grpo.prepare_data(train_data)

# Optimize policy
results = gemini_grpo.optimize_policy(
    train_dataset=train_dataset,
    num_epochs=3,
    batch_size=2,
    learning_rate=5e-5
)

# Generate text with the optimized model
prompt = "Explain how neural networks learn."
response = gemini_grpo.generate_text(prompt, max_length=200)
print(response)

# Save the model
save_path = gemini_grpo.save_model()
print(f"Saved optimization results to: {save_path}")
```

## Reward Functions

GRPO providers use reward functions to evaluate the quality of generated responses. The Deep Research Core library provides several built-in reward functions:

- **BLEURewardFunction**: Evaluates using BLEU score
- **ROUGERewardFunction**: Evaluates using ROUGE score
- **EmbeddingRewardFunction**: Evaluates using embedding similarity
- **LengthRewardFunction**: Evaluates based on response length
- **KeywordRewardFunction**: Evaluates based on keyword presence
- **FormatRewardFunction**: Evaluates based on format adherence
- **OutcomeRewardFunction**: Evaluates based on task outcome
- **DiversityRewardFunction**: Evaluates based on response diversity
- **EvaluatorModelRewardFunction**: Evaluates using another model
- **MultiCriteriaEvaluatorRewardFunction**: Evaluates using multiple criteria
- **AdaptiveEvaluatorRewardFunction**: Adapts evaluation based on context
- **AdaptiveRewardFunction**: Adapts reward function based on context
- **BanditRewardFunction**: Uses bandit algorithms for reward

You can combine multiple reward functions to create a comprehensive evaluation:

```python
reward_functions = {
    "bleu": BLEURewardFunction(),
    "rouge": ROUGERewardFunction(),
    "embedding": EmbeddingRewardFunction(embedding_fn=lambda x: embedding_model.encode(x)),
    "length": LengthRewardFunction(target_length=100),
    "keyword": KeywordRewardFunction(keywords=["important", "relevant", "accurate"])
}
```

## Advanced Usage

### Custom Reward Functions

You can create custom reward functions by subclassing `RewardFunction`:

```python
from deep_research_core.rl_tuning.grpo import RewardFunction

class CustomRewardFunction(RewardFunction):
    def __init__(self, param1, param2):
        self.param1 = param1
        self.param2 = param2
    
    def __call__(self, response, reference=None, prompt=None):
        # Implement your custom reward logic here
        reward = ...
        return reward
```

### Saving and Loading Models

GRPO providers allow you to save and load optimization results:

```python
# Save optimization results
save_path = grpo.save_model("./my_model.json")

# Load optimization results
grpo.load_model("./my_model.json")
```

### Batch Processing

You can process multiple examples in batches:

```python
# Prepare data
train_dataset, _ = grpo.prepare_data(train_data)

# Optimize policy with batch processing
results = grpo.optimize_policy(
    train_dataset=train_dataset,
    num_epochs=3,
    batch_size=4  # Process 4 examples at a time
)
```

## Conclusion

GRPO providers offer a flexible framework for optimizing language models using reward functions. By following the examples in this guide, you can use various providers to optimize models from different sources and generate high-quality text.

For more information, refer to the API documentation and example scripts in the Deep Research Core library.
