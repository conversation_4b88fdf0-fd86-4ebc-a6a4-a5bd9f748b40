# Error Recovery System

The Error Recovery System is a robust framework designed to handle errors in various reasoning techniques (CoT, ToT, RAG, etc.) and improve the reliability of AI systems.

## Overview

The Error Recovery System provides:

- Automatic error detection and classification
- Multiple recovery strategies for different error types
- Learning from past errors to improve future recovery
- Analytics and visualization for error patterns
- Distributed error recovery across multiple nodes
- ML-based error classification

## Components

### Core Components

- **ReasoningErrorRecovery**: The main class that coordinates error recovery
- **ErrorDetector**: Detects and classifies errors
- **ErrorRecoveryManager**: Manages recovery strategies
- **ErrorLearningManager**: Learns from past errors

### Advanced Components

- **ErrorAnalytics**: Tracks and analyzes error patterns
- **ErrorVisualization**: Visualizes error patterns
- **ErrorStrategyOptimizerManager**: Optimizes recovery strategies
- **ErrorMonitor**: Monitors errors and sends alerts
- **MLErrorClassifier**: Uses machine learning to classify errors
- **DistributedErrorRecovery**: Coordinates error recovery across multiple nodes

## Recovery Strategies

The system includes multiple recovery strategies:

- **RetryStrategy**: Simply retries the failed operation
- **AlternativeToolStrategy**: Tries alternative tools
- **InputReformulationStrategy**: Reformulates the input
- **FallbackResultStrategy**: Returns a fallback result
- **ContextAwareReformulationStrategy**: Reformulates based on context
- **PromptEngineeringStrategy**: Adjusts prompts
- **ModelFallbackStrategy**: Falls back to alternative models
- **ToolParameterAdjustmentStrategy**: Adjusts tool parameters
- **AdaptiveRecoveryStrategy**: Adapts based on past errors

## Integration with Reasoning Techniques

### Tree of Thought (ToT)

The Error Recovery System is integrated with the Tree of Thought reasoning technique:

```python
from src.deep_research_core.reasoning.tot import TreeOfThought

# Create a Tree of Thought reasoner with error recovery
tot = TreeOfThought(
    provider="openai",
    model="gpt-4o",
    language="en",
    max_branches=3,
    max_depth=3,
    adaptive=True,
    verbose=True,
    use_error_recovery=True,  # Enable error recovery
    max_retries=3,
    retry_delay=0.5,
    cache_path="./cache",
    use_advanced_strategies=True
)

# Use the reasoner
result = tot.reason(query="What is the capital of France?")
```

### Retrieval-Augmented Generation (RAG)

The Error Recovery System is also integrated with RAG systems:

```python
from src.deep_research_core.rag.sqlite_vector_rag import SQLiteVectorRAG
from src.deep_research_core.rag.error_recovery_rag import ErrorRecoveryRAG

# Create a RAG system
rag_system = SQLiteVectorRAG(
    db_path="vector_rag.sqlite",
    table_name="documents",
    embedding_model="multilingual-e5-large",
    embedding_dim=768,
    create_if_not_exists=True
)

# Wrap the RAG system with error recovery
error_recovery_rag = ErrorRecoveryRAG(
    rag_system=rag_system,
    use_error_recovery=True,
    max_retries=3,
    retry_delay=0.5,
    cache_path="./cache",
    use_advanced_strategies=True
)

# Use the RAG system
result = error_recovery_rag.query(query="What is the capital of France?")
```

## Examples

See the examples directory for complete examples:

- `examples/tot_error_recovery.py`: Example of using error recovery with Tree of Thought
- `examples/rag_error_recovery.py`: Example of using error recovery with RAG

## Configuration Options

The Error Recovery System can be configured with various options:

- `use_error_recovery`: Whether to use error recovery (default: True)
- `max_retries`: Maximum number of retries for failed actions (default: 3)
- `retry_delay`: Delay between retries in seconds (default: 0.5)
- `cache_path`: Path to cache directory (default: None)
- `use_advanced_strategies`: Whether to use advanced recovery strategies (default: True)
- `use_error_analytics`: Whether to use error analytics (default: True)
- `use_error_visualization`: Whether to use error visualization (default: False)
- `use_error_strategy_optimizer`: Whether to use error strategy optimizer (default: True)
- `use_error_monitoring`: Whether to use error monitoring (default: True)
- `use_ml_error_classifier`: Whether to use ML-based error classifier (default: True)
- `use_distributed_error_recovery`: Whether to use distributed error recovery (default: False)
- `cluster_nodes`: List of cluster nodes for distributed error recovery (default: None)

## Custom Error Recovery

You can create custom error recovery strategies by extending the `ErrorRecoveryStrategy` class:

```python
from src.deep_research_core.utils.error_recovery import ErrorRecoveryStrategy

class MyCustomStrategy(ErrorRecoveryStrategy):
    def __init__(self, name="my_custom_strategy", **kwargs):
        super().__init__(name=name, **kwargs)
    
    def can_handle(self, error, context):
        # Determine if this strategy can handle the error
        return True
    
    def recover(self, error, context):
        # Implement recovery logic
        return {
            "success": True,
            "strategy": self.name,
            "result": "Custom recovery result"
        }
```

## Error Analytics

The Error Recovery System includes error analytics to track and analyze error patterns:

```python
from src.deep_research_core.utils.error_analytics import SQLiteErrorAnalytics

# Create an error analytics instance
analytics = SQLiteErrorAnalytics(db_path="error_analytics.db")

# Get error statistics
stats = analytics.get_error_statistics()
print(f"Total errors: {stats['total_errors']}")
print(f"Recovery rate: {stats['recovery_rate']:.2%}")
print(f"Most common error: {stats['most_common_error']}")
```

## Error Visualization

The Error Recovery System can visualize error patterns:

```python
from src.deep_research_core.utils.error_visualization import ErrorVisualization

# Create an error visualization instance
visualization = ErrorVisualization(analytics=analytics, output_dir="./visualizations")

# Generate visualizations
visualization.generate_error_trend_chart()
visualization.generate_recovery_rate_chart()
visualization.generate_error_category_chart()
```

## Distributed Error Recovery

The Error Recovery System supports distributed error recovery across multiple nodes:

```python
from src.deep_research_core.utils.distributed_error_recovery import DistributedErrorRecovery

# Create a distributed error recovery instance
distributed = DistributedErrorRecovery(
    recovery_manager=recovery_manager,
    learning_manager=learning_manager,
    storage_path="distributed_errors.json",
    sync_interval=60,
    node_id=None,  # Will generate a random UUID
    cluster_nodes=["node1", "node2", "node3"]
)

# Sync with other nodes
distributed.sync()
```

## ML-Based Error Classification

The Error Recovery System includes ML-based error classification:

```python
from src.deep_research_core.utils.ml_error_classifier import MLErrorClassifier

# Create an ML error classifier
classifier = MLErrorClassifier(
    model_path="ml_error_model.pkl",
    vectorizer_path="ml_error_vectorizer.pkl",
    min_samples=50,
    auto_train=True
)

# Classify an error
classification = classifier.classify(error, context)
print(f"Error category: {classification['category']}")
print(f"Confidence: {classification['confidence']:.2%}")
```

## Error Monitoring

The Error Recovery System includes error monitoring:

```python
from src.deep_research_core.utils.error_monitoring import ErrorMonitor, LoggingAlertHandler, EmailAlertHandler

# Create an error monitor
monitor = ErrorMonitor(
    alert_threshold=5,
    alert_window=60,
    alert_cooldown=300,
    alert_handlers=[
        LoggingAlertHandler(),
        EmailAlertHandler(
            smtp_server="smtp.example.com",
            smtp_port=587,
            smtp_username="user",
            smtp_password="password",
            from_email="<EMAIL>",
            to_emails=["<EMAIL>"]
        )
    ]
)

# Track an error
monitor.track_error(error, context, detection_result, recovery_result)
```

## Error Strategy Optimizer

The Error Recovery System includes an error strategy optimizer:

```python
from src.deep_research_core.utils.error_strategy_optimizer import ErrorStrategyOptimizerManager

# Create an error strategy optimizer
optimizer = ErrorStrategyOptimizerManager(
    recovery_manager=recovery_manager,
    learning_manager=learning_manager,
    storage_path="error_optimizer.json"
)

# Update performance for a strategy
optimizer.update_performance("retry_strategy", 1.0)

# Optimize strategies
optimized_values = optimizer.optimize_strategies()
print(f"Optimized values: {optimized_values}")
```

## Conclusion

The Error Recovery System provides a robust framework for handling errors in AI systems, making them more reliable and resilient. By integrating this system with your reasoning techniques, you can improve the user experience and reduce the impact of errors.
