# Deep Research Core Documentation

Welcome to the Deep Research Core documentation!

## Overview

Deep Research Core is a comprehensive deep research system with advanced retrieval and reasoning capabilities.

## Features

- **Advanced Retrieval Techniques**
  - Hybrid Search (BM25 + Embedding Search)
  - Dense Passage Retrieval (DPR)
  - Multilingual Retrieval

- **Chain of Thought Reasoning**
  - Adaptive reasoning based on question type
  - Natural transitions between reasoning steps
  - Support for multiple languages

- **Retrieval-Augmented Generation (RAG)**
  - Integration with personal knowledge bases
  - Source citation and attribution
  - Fact-checking against retrieved documents

- **Combined CoTRAG**
  - Chain of Thought + Retrieval-Augmented Generation
  - Enhanced reasoning with retrieved information
  - Improved accuracy and reliability

- **Multiple Language Models**
  - OpenAI (GPT-4, GPT-3.5)
  - Anthropic (Claude)
  - OpenRouter (access to various models)
  - Local models (with quantization)

- **Multilingual Support**
  - English
  - Vietnamese
  - Extensible to other languages

## Installation

See the [Installation Guide](user_guide/installation.md) for detailed instructions.

## Usage

See the [Usage Guide](user_guide/usage.md) for detailed instructions.

## API Reference

See the [API Reference](api/index.md) for detailed API documentation.

## Developer Guide

See the [Developer Guide](developer_guide/index.md) for information on contributing to the project.

## License

Deep Research Core is licensed under the MIT License. See the [LICENSE](https://github.com/yourusername/deep-research-core/blob/main/LICENSE) file for details.
