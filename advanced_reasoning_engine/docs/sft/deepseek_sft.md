# DeepSeek Supervised Fine-Tuning (SFT)

This document provides information on how to fine-tune DeepSeek models using the DeepSeekSFT implementation in the Deep Research Core.

## Overview

The `DeepSeekSFT` class provides functionality to fine-tune DeepSeek large language models using supervised learning. It interfaces with DeepSeek's API to perform the fine-tuning process.

## Prerequisites

- DeepSeek API key (set as `DEEPSEEK_API_KEY` environment variable or passed to the constructor)
- Training data in the correct format (see below)
- Python environment with required dependencies installed

## Installation

The DeepSeekSFT class is part of the Deep Research Core package. Make sure you have the package installed.

## Training Data Format

Training data should be prepared as a list of dictionaries, each with "input" and "output" keys:

```json
[
  {
    "input": "What is machine learning?",
    "output": "Machine learning is a branch of artificial intelligence that focuses on building systems that can learn from data, identify patterns, and make decisions with minimal human intervention."
  },
  {
    "input": "How do neural networks work?",
    "output": "Neural networks are computational models inspired by the human brain. They consist of layers of interconnected nodes (neurons) that process information. Each connection has a weight that adjusts as learning proceeds, allowing the network to learn complex patterns in data."
  }
]
```

You can customize the input and output keys when calling the `prepare_data` method.

## Usage Example

Here's a simple example of how to use the `DeepSeekSFT` class:

```python
from deep_research_core.rl_tuning.sft import DeepSeekSFT

# Initialize the fine-tuning class
sft = DeepSeekSFT(
    model_name="deepseek-chat",
    output_dir="./deepseek_sft_output",
    api_key="your_deepseek_api_key"  # Optional if set as environment variable
)

# Load training data (example)
train_data = [
    {
        "input": "What is deep learning?",
        "output": "Deep learning is a subset of machine learning that uses neural networks with many layers to learn from large amounts of data."
    },
    {
        "input": "Explain reinforcement learning",
        "output": "Reinforcement learning is a type of machine learning where an agent learns to make decisions by taking actions in an environment to maximize some notion of cumulative reward."
    }
]

# Prepare the dataset
train_dataset, _ = sft.prepare_data(
    train_data=train_data,
    system_message="You are a helpful AI assistant."
)

# Start fine-tuning
result = sft.train_model(
    train_dataset=train_dataset,
    num_epochs=3,
    wait_for_completion=True,
    suffix="assistant-v1"
)

# Print the result
print(f"Fine-tuning result: {result}")

# Generate text with the fine-tuned model
response = sft.generate_text(
    prompt="How do transformers work in NLP?",
    max_length=200,
    temperature=0.7
)

print(f"Generated response: {response}")
```

## Command-line Example

You can also use the included example script:

```bash
python examples/deepseek_sft_example.py --model deepseek-chat --epochs 3 --wait
```

Or with your own data:

```bash
python examples/deepseek_sft_example.py --model deepseek-chat --data_path your_data.json --wait
```

## API Reference

### Constructor

```python
DeepSeekSFT(
    model_name: str,
    output_dir: str = "./sft_output",
    data_path: Optional[str] = None,
    api_key: Optional[str] = None,
    api_base: Optional[str] = None,
    **kwargs
)
```

### Key Methods

#### prepare_data
```python
prepare_data(
    train_data: Union[str, List[Dict[str, Any]]],
    eval_data: Optional[Union[str, List[Dict[str, Any]]]] = None,
    input_key: str = "input",
    output_key: str = "output",
    **kwargs
) -> Tuple[SFTDataset, Optional[SFTDataset]]
```

#### train_model
```python
train_model(
    train_dataset: SFTDataset,
    eval_dataset: Optional[SFTDataset] = None,
    num_epochs: int = 3,
    batch_size: int = 4,
    learning_rate: float = 5e-5,
    **kwargs
) -> Dict[str, Any]
```

#### generate_text
```python
generate_text(
    prompt: str,
    max_length: int = 100,
    temperature: float = 0.7,
    top_p: float = 0.9,
    **kwargs
) -> str
```

## Additional Information

### Fine-tuning Parameters

DeepSeek's API allows for the following fine-tuning hyperparameters:

- `n_epochs`: Number of training epochs
- `batch_size`: Batch size for training
- `learning_rate_multiplier`: Multiplier for the learning rate

### Waiting for Completion

By default, the `train_model` method returns immediately after starting the fine-tuning job. To wait for completion, pass `wait_for_completion=True` to the method.

### Saving and Loading Models

After fine-tuning, you can save the model information to a file using the `save_model` method:

```python
model_path = sft.save_model("path/to/save/model_info.json")
```

And load it later:

```python
sft.load_model("path/to/saved/model_info.json")
```

Note that this only saves/loads the reference to the fine-tuned model, not the model weights themselves which are stored on DeepSeek's servers.

### Error Handling

The implementation includes error handling for common issues like:
- Missing API keys
- Failed file uploads
- Failed fine-tuning jobs

Always check the returned result dictionary for any error messages. 