# Metrics và Dashboard cho WebSearchAgent

## Tổng quan

Module metrics và dashboard cung cấp khả năng theo dõi, phân tích và hiển thị hiệu suất của WebSearchAgent. Hệ thống thu thập các thông số chi tiết về thời gian xử lý, tỷ lệ thành công, sử dụng cache và nhiều metrics khác, đồng thời cung cấp giao diện trực quan để phân tích dữ liệu này.

## Tính năng chính

### WebSearchMetricsCollector

1. **Thu thập dữ liệu toàn diện**
   - Metrics cho từng truy vấn riêng lẻ
   - Metrics cho các batch tìm kiếm đồng thời
   - Thông tin tài nguyên hệ thống (CPU, RAM, disk)

2. **Lưu trữ linh hoạt**
   - Lưu trữ trong bộ nhớ với giới hạn có thể cấu hình
   - Lưu trữ trên đĩa dưới dạng JSONL có thể phân tích
   - Tự động phân chia dữ liệu theo ngày

3. **Phân tích hiệu suất**
   - Thống kê về thời gian xử lý (trung bình, min, max, percentiles)
   - Tỷ lệ thành công và cache hit
   - Phân tích theo công cụ tìm kiếm
   - Theo dõi tài nguyên hệ thống theo thời gian

### Dashboard trực quan

1. **Giao diện Streamlit**
   - Dễ sử dụng, tương tác và đáp ứng
   - Tự động làm mới dữ liệu

2. **Biểu đồ và bảng hiệu suất**
   - Biểu đồ thời gian xử lý theo thời gian
   - Biểu đồ tỷ lệ thành công
   - So sánh hiệu suất giữa các công cụ tìm kiếm
   - Phân tích batch processing

3. **Theo dõi tài nguyên**
   - CPU & Memory usage qua thời gian
   - Gauge charts hiển thị tình trạng hiện tại
   - Cảnh báo khi tài nguyên gần đạt ngưỡng

## Cách sử dụng

### Bật thu thập metrics

Để bật thu thập metrics, bạn cần cấu hình WebSearchAgent:

```python
from deep_research_core.agents.web_search_agent_base import WebSearchAgentBase
from deep_research_core.utils.web_search_metrics import metrics_collector

# Cách 1: Sử dụng instance toàn cục
agent = WebSearchAgentBase(config={
    "metrics": {
        "enabled": True,
        "save_to_disk": True
    }
})

# Cách 2: Tạo collector riêng
metrics_collector_custom = WebSearchMetricsCollector(
    metrics_dir="./custom_metrics",
    max_memory_records=2000,
    track_system_resources=True
)

agent = WebSearchAgentBase(config={
    "metrics": {
        "enabled": True,
        "use_global": False
    }
})
agent.metrics_collector = metrics_collector_custom
```

### Truy vấn metrics

```python
# Lấy tóm tắt metrics cho 24 giờ qua
metrics_summary = agent.get_metrics_summary(time_range_hours=24)

# Tạo báo cáo hiệu suất
report = metrics_collector.generate_performance_report(
    output_file="performance_report.json",
    time_range=timedelta(days=7)
)

# Tạo biểu đồ
metrics_collector.plot_performance_graphs(
    output_dir="./performance_graphs"
)
```

### Chạy Dashboard

```python
from deep_research_core.utils.search_performance_dashboard import run_dashboard

# Chạy dashboard với thư mục metrics mặc định
run_dashboard()

# Hoặc chỉ định thư mục và cổng
run_dashboard(metrics_dir="./custom_metrics", port=8502)
```

Ngoài ra, bạn có thể chạy từ command line:

```bash
python -m deep_research_core.utils.search_performance_dashboard --metrics_dir=./metrics --port=8501
```

Hoặc sử dụng ví dụ có sẵn:

```bash
python examples/metrics_dashboard_example.py --generate --dashboard
```

## Cấu trúc dữ liệu metrics

### QueryMetrics
- `query`: Chuỗi truy vấn
- `start_time`/`end_time`: Thời điểm bắt đầu và kết thúc
- `duration`: Thời gian xử lý (giây)
- `success`: Trạng thái thành công (boolean)
- `result_count`: Số lượng kết quả
- `engine`: Công cụ tìm kiếm
- `cache_hit`: Có sử dụng cache hay không
- `error`: Thông tin lỗi (nếu có)

### BatchMetrics
- `batch_id`: ID của batch
- `query_count`: Số lượng truy vấn
- `concurrent_workers`: Số lượng workers đồng thời
- `duration`: Tổng thời gian xử lý
- `success_count`/`error_count`: Số lượng thành công/thất bại
- `query_metrics`: Danh sách các QueryMetrics

### SystemMetrics
- `cpu_percent`: Phần trăm sử dụng CPU
- `memory_percent`/`memory_used`/`memory_available`: Thông tin bộ nhớ
- `disk_percent`/`disk_used`/`disk_free`: Thông tin đĩa
- `net_bytes_sent`/`net_bytes_recv`: Thông tin mạng

## Tùy chỉnh Dashboard

Bạn có thể tùy chỉnh dashboard theo nhu cầu riêng bằng cách mở rộng lớp WebSearchDashboard:

```python
from deep_research_core.utils.search_performance_dashboard import WebSearchDashboard

class CustomDashboard(WebSearchDashboard):
    def _render_streamlit_dashboard(self):
        # Triển khai dashboard tùy chỉnh
        pass

# Sử dụng
dashboard = CustomDashboard(metrics_dir="./metrics")
dashboard.run(port=8501)
```

## Phụ thuộc

- **Cốt lõi**: Python 3.8+, psutil
- **Dashboard**: streamlit, pandas, plotly
- **Biểu đồ**: matplotlib

## Khắc phục sự cố

1. **Không thu thập được metrics**
   - Kiểm tra xem `metrics.enabled` đã được đặt thành `True` chưa
   - Đảm bảo metrics_collector đã được khởi tạo đúng cách

2. **Dashboard không hiển thị dữ liệu**
   - Kiểm tra thư mục metrics có tồn tại không
   - Kiểm tra quyền đọc/ghi trên thư mục
   - Đảm bảo đã cài đặt streamlit và các phụ thuộc khác

3. **Thu thập dữ liệu hệ thống thất bại**
   - Đảm bảo `psutil` đã được cài đặt
   - Kiểm tra quyền truy cập thông tin hệ thống 