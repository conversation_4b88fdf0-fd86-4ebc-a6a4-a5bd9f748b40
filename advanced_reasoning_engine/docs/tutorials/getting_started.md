# Getting Started with Deep Research Core

This tutorial guides you through the basic setup and first steps with Deep Research Core, helping you understand core concepts and get up and running quickly.

## Prerequisites

Before starting, ensure you have:
- Python 3.8 or higher installed
- pip package manager
- At least 16GB RAM (8GB minimum, but 16GB+ recommended for better performance)
- GPU with 8GB+ VRAM (for local models)

## Installation

Install Deep Research Core from PyPI:

```bash
pip install deep-research-core
```

Or install from source for the latest development version:

```bash
git clone https://github.com/organization/deep-research-core.git
cd deep-research-core
pip install -e .
```

## Basic Configuration

Create a configuration file called `config.yaml` in your project directory:

```yaml
models:
  default: "openai/gpt-3.5-turbo"
  local:
    enabled: false
    path: "meta-llama/Llama-2-7b-chat-hf"
  
reasoning:
  default_format: "cot"  # Options: cot, react, tot, rag
  
retrieval:
  vector_store: "chroma"
  embedding_model: "sentence-transformers/all-MiniLM-L6-v2"
  
api_keys:
  openai: ${OPENAI_API_KEY}
  anthropic: ${ANTHROPIC_API_KEY}
```

Set up your API keys as environment variables:

```bash
export OPENAI_API_KEY="your-openai-key"
export ANTHROPIC_API_KEY="your-anthropic-key"
```

## Your First Reasoning Task

Let's create a simple script that uses Chain of Thought (CoT) reasoning:

```python
from deep_research_core.reasoning import CoTReasoner

# Initialize the reasoner
reasoner = CoTReasoner()

# Define a question that requires step-by-step thinking
question = "If a shirt costs $25 and is discounted by 20%, and then there's an additional 10% off the discounted price, what is the final price of the shirt?"

# Generate a response with step-by-step reasoning
response = reasoner.generate(question)

# Print the response
print(response.answer)
print("\nReasoning steps:")
print(response.reasoning)
```

## Using Retrieval-Augmented Generation (RAG)

Here's how to use RAG with your own documents:

```python
from deep_research_core.rag import RAGReasoner
from deep_research_core.utils import DocumentLoader

# Load documents
loader = DocumentLoader()
documents = loader.load("path/to/your/documents")

# Initialize RAG reasoner
rag = RAGReasoner()

# Add documents to the vector store
rag.add_documents(documents)

# Ask a question that requires knowledge from your documents
question = "What are the key findings in the Q3 financial report?"
response = rag.generate(question)

# Print the response with source citations
print(response.answer)
print("\nSources:")
for source in response.sources:
    print(f"- {source.document_name}: {source.page}")
```

## Exploring Advanced Features

### Tree of Thoughts (ToT) Reasoning

```python
from deep_research_core.reasoning import ToTReasoner

# Initialize with more exploration
reasoner = ToTReasoner(
    num_thoughts=4,  # Generate 4 thoughts at each step 
    max_steps=3,     # Maximum 3 steps of reasoning
    beam_width=2     # Keep top 2 reasoning paths
)

question = "What's the best approach to optimize a machine learning model that's overfitting?"
response = reasoner.generate(question)

print(response.answer)
print("\nExplored paths:")
for path in response.paths:
    print(f"Path {path.id} (score: {path.score}):")
    for thought in path.thoughts:
        print(f"  - {thought.content}")
```

## Learning More

Explore more advanced features in our examples directory:
- Try different reasoning formats (ReAct, ToT, CoT, RAG)
- Experiment with local models
- Fine-tune models with LoRA/QLoRA
- Combine reasoning methods
- Use Vietnamese language optimization

## Next Steps

- Read the [full documentation](../index.md)
- Check out the [API reference](../api_reference.md)
- Explore [example scripts](../examples/README.md)
- Join our community on [Discord](#) or [GitHub Discussions](#) 