# Deep Research Core Tutorials

Welcome to the Deep Research Core tutorials section. These tutorials provide comprehensive guides to help you get started with and make the most of the Deep Research Core framework.

## Available Tutorials

### [Getting Started](getting_started.md)
A beginner-friendly introduction to Deep Research Core, covering basic installation, configuration, and simple examples of reasoning tasks. Perfect for new users who want to quickly understand the core concepts and get a working setup.

### [Advanced Usage Techniques](advanced_usage.md)
Explore complex integrations and optimizations, including multi-model reasoning, advanced RAG techniques, Graph of Thoughts implementation, trajectory scaling, Vietnamese language support, and performance monitoring. Ideal for users who have mastered the basics and want to leverage the full power of the framework.

## Upcoming Tutorials

- **Fine-tuning Models**: A comprehensive guide to fine-tuning models with techniques like LoRA, QLoRA, and SFT
- **Building Custom Reasoning Flows**: Learn how to create custom reasoning formats and combine existing ones
- **Working with Vector Databases**: In-depth exploration of different vector database options and configurations
- **Benchmark and Evaluation**: Guide to measuring and improving model performance
- **Multi-Agent Systems**: Tutorial on implementing collaborative multi-agent systems

## External Resources

- [API Reference](../api_reference.md): Detailed documentation of all classes and methods
- [Example Scripts](../examples/README.md): Complete working examples for various tasks
- [Troubleshooting Guide](../troubleshooting.md): Solutions to common issues and debugging tips

## Feedback

If you have suggestions for new tutorials or improvements to existing ones, please open an issue on our GitHub repository. 