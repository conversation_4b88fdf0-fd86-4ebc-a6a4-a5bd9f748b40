# Advanced Usage Techniques

This tutorial covers advanced usage patterns for the Deep Research Core framework, focusing on complex integrations and optimizations.

## Multi-Model Reasoning

Deep Research Core allows you to utilize multiple models in a single reasoning pipeline:

```python
from deep_research_core.reasoning import MultiModelReasoner
from deep_research_core.models import ModelRegistry

# Register multiple models
registry = ModelRegistry()
registry.register("generator", "anthropic/claude-3-opus")
registry.register("evaluator", "openai/gpt-4")
registry.register("refiner", "local/mistral-7b")

# Create a multi-model reasoner
reasoner = MultiModelReasoner(registry)

# Configure the reasoning flow
reasoner.configure_flow([
    {"step": "generate", "model": "generator"},
    {"step": "evaluate", "model": "evaluator"},
    {"step": "refine", "model": "refiner"}
])

# Execute the reasoning process
query = "Propose three novel approaches to carbon capture technology"
result = reasoner.execute(query)

print(result.final_output)
print("\nContributions by model:")
for step in result.steps:
    print(f"- {step.model_name}: {step.role}")
```

## Advanced RAG Techniques

### HyDE (Hypothetical Document Embeddings)

HyDE improves retrieval by generating hypothetical documents:

```python
from deep_research_core.retrieval import HyDERetriever
from deep_research_core.models import LanguageModel

# Create a HyDE retriever
model = LanguageModel("openai/gpt-3.5-turbo")
retriever = HyDERetriever(
    language_model=model,
    vector_store="./vector_store",
    temperature=0.2,
    hyde_prompt_template="Generate a detailed passage that would contain the answer to: {query}"
)

# Index your documents
retriever.index_documents("./documents")

# Retrieve with HyDE
query = "What are the environmental impacts of quantum computing?"
results = retriever.retrieve(query, top_k=5)

for i, doc in enumerate(results):
    print(f"Document {i+1}: {doc.metadata['title']} (Score: {doc.score})")
    print(f"Content: {doc.content[:200]}...\n")
```

### Multi-Query RAG with Source Attribution

```python
from deep_research_core.rag import MultiQueryRAG
from deep_research_core.retrieval.attribution import SourceAttributor

# Initialize the multi-query RAG system
rag = MultiQueryRAG(
    base_model="openai/gpt-4",
    decomposer_model="anthropic/claude-3-haiku",
    max_queries=5,
    attribution=True
)

# Add the source attributor
attributor = SourceAttributor(
    granularity="paragraph",
    highlighting=True,
    confidence_threshold=0.7
)
rag.set_attributor(attributor)

# Process a complex query
query = "Compare the economic and environmental trade-offs between nuclear and renewable energy sources over the next 50 years"
response = rag.generate(query)

print(response.answer)
print("\nDecomposed into queries:")
for q in response.sub_queries:
    print(f"- {q}")
    
print("\nSources by confidence:")
for source in response.sources:
    print(f"- {source.document} (Confidence: {source.confidence})")
    print(f"  Evidence: {source.evidence[:100]}...")
```

## Graph of Thoughts Implementation

```python
from deep_research_core.reasoning import GraphOfThoughts
from deep_research_core.reasoning.evaluators import ComplexityEvaluator

# Create a graph of thoughts reasoner
got = GraphOfThoughts(
    model="anthropic/claude-3-sonnet",
    max_nodes=15,
    exploration_factor=0.3
)

# Add custom evaluator for node selection
evaluator = ComplexityEvaluator()
got.add_evaluator(evaluator)

# Define custom node expansion strategy
got.set_expansion_strategy("adaptive")

# Execute reasoning on a complex problem
problem = """
Design a system to detect and mitigate bias in machine learning models 
that process loan applications, considering ethical, legal, and technical aspects.
"""

solution = got.reason(problem)

print(solution.final_answer)
print("\nGraph Statistics:")
print(f"- Total nodes explored: {solution.stats.nodes_explored}")
print(f"- Max depth reached: {solution.stats.max_depth}")
print(f"- Backtracking instances: {solution.stats.backtrack_count}")

# Visualize the reasoning graph (requires matplotlib)
got.visualize_graph(solution.graph, "reasoning_graph.png")
```

## Test-time Scaling of Trajectories

```python
from deep_research_core.rl_tuning.trajectories import TrajectoryScaler
from deep_research_core.rl_tuning.environment import AgentEnvironment

# Create an environment
env = AgentEnvironment("problem_solving")

# Generate multiple solution trajectories
trajectories = []
for i in range(10):
    trajectory = env.generate_trajectory(
        model="openai/gpt-4",
        problem_id=f"problem_{i}",
        max_steps=20
    )
    trajectories.append(trajectory)

# Scale trajectories at test time
scaler = TrajectoryScaler(
    diversity_weight=0.6,
    performance_weight=0.4,
    adaptive=True
)

# Select optimal trajectories for the given resources
selected = scaler.select_trajectories(
    trajectories=trajectories,
    resource_constraint=5,  # We can only use 5 trajectories
    task_difficulty=0.8     # Task is quite difficult
)

print(f"Selected {len(selected)} trajectories:")
for i, traj in enumerate(selected):
    print(f"Trajectory {i+1}: {traj.id}")
    print(f"- Performance score: {traj.metrics.performance}")
    print(f"- Diversity score: {traj.metrics.diversity}")
    print(f"- Length: {traj.length} steps")
```

## Advanced Vietnamese Language Support

```python
from deep_research_core.language.vietnamese import VietnameseProcessor
from deep_research_core.reasoning import CoTReasoner

# Initialize the Vietnamese processor
processor = VietnameseProcessor(
    compound_handling=True,
    dialect_awareness=True,
    specialized_embeddings=True
)

# Create a Vietnamese-optimized reasoner
reasoner = CoTReasoner(
    model="openai/gpt-4",
    language_processor=processor
)

# Process a Vietnamese query
query = "Phân tích tác động của biến đổi khí hậu đối với đồng bằng sông Cửu Long trong 30 năm tới"
response = reasoner.generate(query)

print(response.answer)
```

## Reinforcement Learning with Direct Preference Optimization

```python
from deep_research_core.rl_tuning.dpo import DPOTrainer
from deep_research_core.data import PreferenceDataset

# Load preference dataset
dataset = PreferenceDataset.from_file("preferences.jsonl")

# Initialize DPO trainer
trainer = DPOTrainer(
    model="meta-llama/Llama-2-7b-hf",
    beta=0.1,
    learning_rate=5e-5,
    max_length=512
)

# Train with DPO
trainer.train(
    dataset=dataset,
    num_epochs=3,
    batch_size=4,
    gradient_accumulation_steps=8
)

# Evaluate the model
metrics = trainer.evaluate(dataset.test_split())
print(f"Preference accuracy: {metrics['preference_accuracy']:.4f}")
print(f"KL divergence from reference: {metrics['kl_divergence']:.4f}")

# Save the model
trainer.save_model("./dpo_tuned_model")
```

## Performance Monitoring and Optimization

```python
from deep_research_core.monitoring import PerformanceMonitor
from deep_research_core.optimization import ModelOptimizer

# Set up performance monitoring
monitor = PerformanceMonitor(
    log_directory="./logs",
    metrics=["latency", "memory", "token_count", "cost"]
)

# Track an operation
with monitor.track("complex_reasoning"):
    # Your reasoning code here
    result = reasoner.generate(complex_query)

# Analyze performance data
report = monitor.generate_report()
print(f"Average latency: {report.metrics.latency.mean:.2f}ms")
print(f"Peak memory usage: {report.metrics.memory.peak / 1024:.2f}MB")
print(f"Total tokens: {report.metrics.token_count.total}")
print(f"Estimated cost: ${report.metrics.cost.total:.4f}")

# Optimize a model for performance
optimizer = ModelOptimizer()
optimized_model = optimizer.optimize(
    model_path="./my_model",
    target="speed",  # Options: speed, memory, balanced
    quantization="int8",
    pruning=0.3,  # 30% weight pruning
    distillation=False
)

# Benchmark the optimized model
benchmark = optimizer.benchmark(
    model=optimized_model,
    test_queries=["query1", "query2", "query3"],
    repetitions=5
)

print(f"Optimization results:")
print(f"- Speed improvement: {benchmark.speedup:.2f}x")
print(f"- Memory reduction: {benchmark.memory_reduction:.2f}%")
print(f"- Quality loss: {benchmark.quality_impact:.2f}%")
```

## Next Steps

For even more advanced usages:
- Explore combining multiple reasoning formats in a single pipeline
- Implement custom evaluation metrics for reasoning quality
- Develop domain-specific reasoning strategies
- Build multi-agent collaborative systems
- Integrate with external tools and APIs 