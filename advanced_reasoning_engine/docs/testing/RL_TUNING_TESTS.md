# Guide for Writing and Running RL Tuning Tests

This document provides detailed instructions for writing and running tests for the RL tuning components in the Deep Research Core project.

## Table of Contents
1. [Getting Started](#getting-started)
2. [Running Tests](#running-tests)
3. [Test Structure](#test-structure)
4. [Writing Test Cases](#writing-test-cases)
5. [Advanced Testing Techniques](#advanced-testing-techniques)
6. [Testing Reward Functions](#testing-reward-functions)
7. [Test Fixtures](#test-fixtures)
8. [Best Practices](#best-practices)
9. [Adding Tests for New Modules](#adding-tests-for-new-modules)

## Getting Started

The Deep Research Core project uses `pytest` as its testing framework. The tests for RL tuning components are located in the `tests/unit/rl_tuning` directory.

Required packages for testing:
- pytest
- pytest-mock
- pytest-cov
- unittest.mock (standard library)

## Running Tests

### Run all RL tuning tests:

```bash
python run_rl_tuning_tests.py
```

### Run tests for a specific module:

```bash
pytest tests/unit/rl_tuning/ppo/
pytest tests/unit/rl_tuning/dpo/
pytest tests/unit/rl_tuning/prm/
```

### Run a specific test file:

```bash
pytest tests/unit/rl_tuning/ppo/test_anthropic_ppo.py
```

### Run with coverage:

```bash
pytest tests/unit/rl_tuning/ --cov=deep_research_core.rl_tuning
pytest tests/unit/rl_tuning/ --cov=deep_research_core.rl_tuning --cov-report=html
```

## Test Structure

Each RL tuning module has its own test directory under `tests/unit/rl_tuning/`:

```
tests/
  unit/
    rl_tuning/
      __init__.py
      ppo/
        __init__.py
        test_base.py
        test_anthropic_ppo.py
        test_openai_ppo.py
      dpo/
        __init__.py
        test_base.py
      prm/
        __init__.py
        test_base.py
        test_reward_model.py
      sft/
        __init__.py
        test_sft_base.py
```

## Writing Test Cases

Here's a template for writing test cases:

```python
import unittest
from unittest.mock import patch, MagicMock
import torch
import numpy as np

from deep_research_core.rl_tuning.module_name import ClassToTest

class TestClassToTest(unittest.TestCase):
    """Tests for the ClassToTest."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Initialize objects needed for testing
        self.instance = ClassToTest(param1="value1", param2="value2")
    
    def tearDown(self):
        """Tear down test fixtures."""
        # Clean up after tests if necessary
        pass
    
    def test_method_name(self):
        """Test description."""
        # Arrange
        input_data = "test input"
        expected_output = "expected result"
        
        # Act
        result = self.instance.method_name(input_data)
        
        # Assert
        self.assertEqual(result, expected_output)
    
    def test_edge_case(self):
        """Test edge case description."""
        # Test edge cases
        pass
```

## Advanced Testing Techniques

### Using unittest.mock for External API Testing

For components that interact with external APIs (like OpenAI, Anthropic, etc.), use mocking to isolate your tests:

```python
@patch('anthropic.Anthropic')
def test_anthropic_api_call(self, mock_anthropic):
    # Setup mock
    mock_client = MagicMock()
    mock_anthropic.return_value = mock_client
    
    mock_response = MagicMock()
    mock_response.content = [MagicMock(text="Mocked response")]
    mock_client.messages.create.return_value = mock_response
    
    # Test code that calls the API
    result = self.ppo.generate("Test prompt")
    
    # Assert API was called correctly
    mock_client.messages.create.assert_called_with(
        model="claude-model-name",
        max_tokens=1024,
        messages=[{"role": "user", "content": "Test prompt"}],
        temperature=1.0,
        top_p=1.0,
        stream=False
    )
    
    # Assert result is as expected
    self.assertEqual(result, "Mocked response")
```

### Testing Asynchronous Code

For async components, use pytest's async support:

```python
import pytest

@pytest.mark.asyncio
async def test_async_function():
    # Test async function
    result = await async_function()
    assert result == expected_result
```

### Parameterized Testing

For testing multiple input combinations:

```python
@pytest.mark.parametrize("input_value,expected_output", [
    ("input1", "output1"),
    ("input2", "output2"),
    ("input3", "output3"),
])
def test_parameterized_function(self, input_value, expected_output):
    result = function_to_test(input_value)
    assert result == expected_output
```

### Testing Exceptions

Testing for expected exceptions:

```python
def test_exception_raised(self):
    with self.assertRaises(ValueError):
        self.instance.method_that_raises_exception("invalid input")
```

## Testing Reward Functions

Reward functions are critical components in RL tuning. Here's a comprehensive approach to testing them:

```python
def test_reward_function(self):
    # Test basic functionality
    reward_fn = RewardFunction(param1=value1)
    
    # Test with valid inputs
    reward = reward_fn("prompt", "good response")
    self.assertGreaterEqual(reward, 0.0)
    self.assertLessEqual(reward, 1.0)
    
    # Test with empty inputs
    reward_empty = reward_fn("", "")
    self.assertEqual(reward_empty, 0.0)  # Or expected value
    
    # Test with different languages
    reward_vietnamese = reward_fn("câu hỏi", "câu trả lời tốt")
    self.assertGreaterEqual(reward_vietnamese, 0.0)
    
    # Test edge cases
    reward_long = reward_fn("prompt", "x" * 10000)  # Very long response
    self.assertGreaterEqual(reward_long, 0.0)
    
    # Test various formats
    reward_with_code = reward_fn("prompt", "response with ```code block```")
    self.assertGreaterEqual(reward_with_code, 0.0)
```

## Test Fixtures

For complex setup that's repeated across tests, use fixtures:

```python
# In conftest.py
import pytest

@pytest.fixture
def mock_api_client():
    with patch('api_module.Client') as mock_client:
        client_instance = MagicMock()
        mock_client.return_value = client_instance
        yield client_instance

@pytest.fixture
def sample_experiences():
    # Create and return sample experiences
    experiences = []
    for i in range(5):
        experience = create_test_experience(i)
        experiences.append(experience)
    return experiences

# In your test file
def test_with_fixtures(mock_api_client, sample_experiences):
    # Test using the fixtures
    result = process_experiences(mock_api_client, sample_experiences)
    assert result is not None
```

## Best Practices

1. **Ensure test isolation**: Each test should be independent, not relying on side effects from other tests.

2. **Use descriptive names**: Test names should describe what's being tested and the expected outcome.

3. **Aim for high coverage**: Try to cover all code paths, including edge cases and error conditions.

4. **Keep tests fast**: Tests should execute quickly. Use mocks for external services.

5. **Test one thing per test**: Each test should verify a single aspect of functionality.

6. **Use appropriate assertions**: Use the most specific assertion for each test case.

7. **Clean up after tests**: If tests create resources, ensure they're cleaned up in tearDown or fixtures.

8. **Use realistic test data**: Test with data that resembles what will be used in production.

9. **Test failure cases**: Don't just test the happy path; also test error handling.

10. **Keep test code clean**: Apply the same quality standards to test code as production code.

## Real-world Example: Testing a PPO Implementation

Here's a comprehensive example of testing an API-based PPO implementation:

```python
import unittest
from unittest.mock import patch, MagicMock
import torch
import os
import json

from deep_research_core.rl_tuning.ppo.anthropic_ppo import AnthropicPPO
from deep_research_core.rl_tuning.ppo.dataset import PPOExperience

class TestAnthropicPPO(unittest.TestCase):
    def setUp(self):
        # Mock environment variables and API client
        self.env_patcher = patch.dict(os.environ, {"ANTHROPIC_API_KEY": "test_key"})
        self.env_patcher.start()
        
        self.client_patcher = patch('anthropic.Anthropic')
        self.mock_client_class = self.client_patcher.start()
        self.mock_client = MagicMock()
        self.mock_client_class.return_value = self.mock_client
        
        # Set up mock response
        self.mock_response = MagicMock()
        self.mock_response.content = [MagicMock(text="Test response")]
        self.mock_client.messages.create.return_value = self.mock_response
        
        # Create reward function
        def reward_fn(prompt, response):
            if "good" in response.lower():
                return 0.9
            return 0.5
        
        # Create PPO instance
        self.ppo = AnthropicPPO(
            model_name="claude-test",
            reward_fn=reward_fn
        )
    
    def tearDown(self):
        self.client_patcher.stop()
        self.env_patcher.stop()
    
    def test_generate_experiences(self):
        # Set up test data
        prompts = ["Prompt 1", "Prompt 2"]
        
        # Set different responses for each prompt
        self.mock_client.messages.create.side_effect = [
            MagicMock(content=[MagicMock(text="Good response")]),
            MagicMock(content=[MagicMock(text="Average response")])
        ]
        
        # Call method
        experiences = self.ppo.generate_experiences(prompts)
        
        # Verify results
        self.assertEqual(len(experiences), 2)
        self.assertEqual(experiences[0].metadata["response"], "Good response")
        self.assertEqual(experiences[0].metadata["raw_reward"], 0.9)
        self.assertEqual(experiences[1].metadata["response"], "Average response")
        self.assertEqual(experiences[1].metadata["raw_reward"], 0.5)
        
        # Verify API was called correctly
        self.assertEqual(self.mock_client.messages.create.call_count, 2)
        calls = self.mock_client.messages.create.call_args_list
        self.assertEqual(calls[0][1]["messages"][0]["content"], "Prompt 1")
        self.assertEqual(calls[1][1]["messages"][0]["content"], "Prompt 2")
    
    # Add more test methods here
```

## Adding Tests for New Modules

When adding a new RL tuning module, follow these steps:

1. Create a new directory under `tests/unit/rl_tuning/` if it doesn't exist
2. Add an `__init__.py` file in the new directory
3. Create test files for each class or module component
4. Follow the test structure and patterns from existing tests
5. Update `REWARDS_TEST_STATUS.md` with the new tests
6. Add the new tests to `TASKS.md` and mark them as completed
7. Run the tests to ensure they pass

For example, if adding a new module called `custom_rl`:

```bash
mkdir -p tests/unit/rl_tuning/custom_rl
touch tests/unit/rl_tuning/custom_rl/__init__.py
# Create test files
touch tests/unit/rl_tuning/custom_rl/test_base.py
touch tests/unit/rl_tuning/custom_rl/test_component1.py
``` 