# Hướng dẫn sử dụng Vietnamese RL Evaluator

## Giới thiệu

`VietnameseRLEvaluator` là một công cụ đánh giá chuyên biệt cho các mô hình RL-tuning với tiếng Việt. Công cụ này cung cấp các metrics đánh giá chất lượng đặc thù cho tiếng Việt, bao gồm đánh giá chất lượng phản hồi, t<PERSON><PERSON> nh<PERSON>t quán, và hiệu suất của mô hình.

## Cài đặt

Để sử dụng `VietnameseRLEvaluator`, bạn cần cài đặt các thư viện phụ thuộc sau:

```bash
pip install underthesea pyvi torch transformers sentence-transformers
```

Nếu bạn muốn sử dụng VnCoreNLP, bạn cần tải và cài đặt nó:

```bash
# Tải VnCoreNLP
mkdir -p vncorenlp/models/wordsegmenter
wget https://raw.githubusercontent.com/vncorenlp/VnCoreNLP/master/VnCoreNLP-1.1.1.jar -O vncorenlp/VnCoreNLP-1.1.1.jar
wget https://raw.githubusercontent.com/vncorenlp/VnCoreNLP/master/models/wordsegmenter/vi-vocab -O vncorenlp/models/wordsegmenter/vi-vocab
wget https://raw.githubusercontent.com/vncorenlp/VnCoreNLP/master/models/wordsegmenter/wordsegmenter.rdr -O vncorenlp/models/wordsegmenter/wordsegmenter.rdr

# Cài đặt thư viện Python
pip install vncorenlp
```

## Khởi tạo

```python
from deep_research_core.evaluation.vietnamese_rl_evaluator import VietnameseRLEvaluator

# Khởi tạo với mô hình embedding mặc định
evaluator = VietnameseRLEvaluator()

# Khởi tạo với mô hình embedding cụ thể
evaluator = VietnameseRLEvaluator(
    embedding_model="phobert",  # Có thể là: phobert, viebert, xlm-roberta-vi, multilingual-e5, bkai-foundation-vi, envibert, ...
    vncorenlp_annotator_path="/path/to/vncorenlp",  # Tùy chọn
    verbose=True  # Bật chế độ verbose
)
```

## Các mô hình embedding hỗ trợ

`VietnameseRLEvaluator` hỗ trợ nhiều mô hình embedding tiếng Việt khác nhau:

### Mô hình cơ bản
- `phobert`: PhoBERT - Mô hình BERT tiếng Việt từ VinAI
- `viebert`: VieBERT - Mô hình BERT tiếng Việt từ FPTAI
- `xlm-roberta-vi`: XLM-RoBERTa với hỗ trợ tiếng Việt
- `multilingual-e5`: Multilingual-E5 - Mô hình embedding đa ngôn ngữ

### Mô hình mới
- `bkai-foundation-vi`: BKAI-Foundation-Vi - Mô hình bi-encoder tiếng Việt
- `envibert`: EnViBERT - Mô hình BERT song ngữ Anh-Việt
- `bartpho`: BARTpho - Mô hình BART tiếng Việt
- `velectra`: VELECTRA - Mô hình ELECTRA tiếng Việt
- `vibert4news`: ViBERT4News - Mô hình BERT tiếng Việt cho tin tức

### Mô hình chuyên biệt
- `vi-mrc`: Vi-MRC - Mô hình đọc hiểu tiếng Việt
- `vi-qa`: Vi-QA - Mô hình hỏi đáp tiếng Việt
- `vietnamese-roberta`: Vietnamese-RoBERTa - Mô hình RoBERTa tiếng Việt

## Các metrics đánh giá

`VietnameseRLEvaluator` cung cấp nhiều metrics đánh giá khác nhau:

### Metrics cơ bản
- `diacritic_consistency`: Đánh giá tính nhất quán của dấu thanh
- `dialect_consistency`: Đánh giá tính nhất quán của phương ngữ
- `reasoning_quality`: Đánh giá chất lượng lập luận
- `lexical_diversity`: Đánh giá tính đa dạng từ vựng
- `grammatical_correctness`: Đánh giá tính chính xác ngữ pháp

### Metrics đặc thù cho RL
- `rl_response_quality`: Đánh giá chất lượng phản hồi RL
- `rl_performance`: Đánh giá hiệu suất tổng thể của mô hình RL
- `rl_task_completion`: Đánh giá mức độ hoàn thành nhiệm vụ
- `rl_consistency`: Đánh giá tính nhất quán giữa các phản hồi

### Metrics đặc thù cho tiếng Việt
- `vietnamese_compound_word_usage`: Đánh giá sử dụng từ ghép tiếng Việt
- `vietnamese_idiom_usage`: Đánh giá sử dụng thành ngữ, tục ngữ tiếng Việt
- `vietnamese_cultural_context`: Đánh giá ngữ cảnh văn hóa tiếng Việt
- `rl_vietnamese_alignment`: Đánh giá mức độ phù hợp với tiếng Việt

### Điểm tổng hợp
- `overall_score`: Điểm tổng hợp từ tất cả các metrics trên

## Ví dụ sử dụng

### Đánh giá một phản hồi đơn lẻ

```python
from deep_research_core.evaluation.vietnamese_rl_evaluator import VietnameseRLEvaluator

# Khởi tạo evaluator
evaluator = VietnameseRLEvaluator(embedding_model="phobert")

# Phản hồi cần đánh giá
response = """
Học tăng cường (Reinforcement Learning) là một phương pháp học máy trong đó một tác tử (agent) 
học cách đưa ra quyết định bằng cách tương tác với môi trường. Tác tử thực hiện hành động và 
nhận phần thưởng hoặc hình phạt, từ đó học cách tối ưu hóa chiến lược để đạt được phần thưởng 
cao nhất trong dài hạn.
"""

# Đánh giá phản hồi
metrics = evaluator.evaluate_all_metrics(response)

# In kết quả
for metric, value in metrics.items():
    print(f"{metric}: {value:.4f}")
```

### Đánh giá với tham chiếu và mô tả nhiệm vụ

```python
# Mô tả nhiệm vụ
task_description = """
Giải thích về học tăng cường (Reinforcement Learning) và ứng dụng của nó trong việc tinh chỉnh 
mô hình ngôn ngữ lớn. Hãy đề cập đến các thuật toán phổ biến như PPO và ứng dụng tại Việt Nam.
"""

# Từ khóa mong đợi
expected_keywords = [
    "học tăng cường", "reinforcement learning", "tác tử", "agent", "môi trường", 
    "phần thưởng", "chính sách", "policy", "PPO", "RLHF", "mô hình ngôn ngữ", 
    "tinh chỉnh", "Việt Nam", "ứng dụng"
]

# Phản hồi tham chiếu
reference_response = """
Học tăng cường (Reinforcement Learning) là một nhánh của học máy, trong đó một tác tử học 
cách hành động trong một môi trường để tối đa hóa phần thưởng tích lũy.
"""

# Các phản hồi trước đó
previous_responses = [
    """
    Học tăng cường (Reinforcement Learning) là một nhánh của học máy, trong đó một tác tử học 
    cách hành động trong một môi trường để tối đa hóa phần thưởng tích lũy.
    """,
    
    """
    Trong học tăng cường, tác tử tương tác với môi trường thông qua một quá trình thử và sai, 
    và học từ kết quả của các hành động của mình.
    """
]

# Đánh giá phản hồi
metrics = evaluator.evaluate_all_metrics(
    response=response,
    reference_response=reference_response,
    task_description=task_description,
    expected_keywords=expected_keywords,
    previous_responses=previous_responses
)

# In kết quả
for metric, value in metrics.items():
    print(f"{metric}: {value:.4f}")
```

### Đánh giá các metrics đặc thù cho tiếng Việt

```python
# Đánh giá các metrics đặc thù cho tiếng Việt
vietnamese_metrics = evaluator.evaluate_vietnamese_specific_metrics(response)

# In kết quả
for metric, value in vietnamese_metrics.items():
    print(f"{metric}: {value:.4f}")

# Đánh giá mức độ phù hợp với tiếng Việt
alignment_score = evaluator.evaluate_rl_vietnamese_alignment(response)
print(f"RL Vietnamese Alignment: {alignment_score:.4f}")
```

## Tích hợp với các mô hình embedding tiếng Việt

`VietnameseRLEvaluator` có thể được tích hợp với các mô hình embedding tiếng Việt khác nhau để cải thiện chất lượng đánh giá. Dưới đây là ví dụ về cách so sánh hiệu suất của các mô hình embedding khác nhau:

```python
# Khởi tạo các evaluator với các mô hình embedding khác nhau
evaluators = {
    "PhoBERT": VietnameseRLEvaluator(embedding_model="phobert"),
    "VieBERT": VietnameseRLEvaluator(embedding_model="viebert"),
    "XLM-RoBERTa": VietnameseRLEvaluator(embedding_model="xlm-roberta-vi"),
    "Multilingual-E5": VietnameseRLEvaluator(embedding_model="multilingual-e5")
}

# Đánh giá phản hồi với mỗi evaluator
for name, evaluator in evaluators.items():
    print(f"\n=== {name} ===")
    metrics = evaluator.evaluate_all_metrics(response)
    
    # In kết quả
    for metric, value in metrics.items():
        print(f"{metric}: {value:.4f}")
```

## Tùy chỉnh trọng số

Bạn có thể tùy chỉnh trọng số của các metrics trong phương thức `evaluate_all_metrics` để phản ánh tầm quan trọng tương đối của từng metric trong ứng dụng cụ thể của bạn:

```python
# Tùy chỉnh trọng số
custom_weights = {
    "diacritic_consistency": 0.10,
    "dialect_consistency": 0.05,
    "reasoning_quality": 0.15,
    "lexical_diversity": 0.05,
    "grammatical_correctness": 0.10,
    "rl_response_quality": 0.20,
    "rl_performance": 0.15,
    "rl_task_completion": 0.10,
    "rl_consistency": 0.05,
    "vietnamese_compound_word_usage": 0.05,
    "vietnamese_idiom_usage": 0.05,
    "vietnamese_cultural_context": 0.05,
    "rl_vietnamese_alignment": 0.10
}

# Tính điểm tổng hợp với trọng số tùy chỉnh
available_metrics = {k: v for k, v in metrics.items() if k in custom_weights}
total_weight = sum(custom_weights[k] for k in available_metrics)
overall_score = sum(metrics[k] * custom_weights[k] / total_weight for k in available_metrics)

print(f"Custom Overall Score: {overall_score:.4f}")
```

## Kết luận

`VietnameseRLEvaluator` là một công cụ mạnh mẽ để đánh giá chất lượng của các mô hình RL-tuning với tiếng Việt. Với nhiều metrics đánh giá khác nhau, công cụ này giúp bạn hiểu rõ hơn về hiệu suất của mô hình và cải thiện chất lượng phản hồi tiếng Việt.

Để biết thêm thông tin, vui lòng tham khảo mã nguồn và các ví dụ trong thư mục `examples/`.
