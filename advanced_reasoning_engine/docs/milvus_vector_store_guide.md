# Milvus Vector Store Guide

This guide provides information on using the Milvus Vector Store implementation in Deep Research Core.

## Overview

Milvus is a high-performance, open-source vector database built for similarity search and AI applications. The `MilvusVectorStore` implementation in Deep Research Core provides a way to store and retrieve vectors using Milvus, which is optimized for high-performance vector similarity search.

## Features

The MilvusVectorStore implementation includes several advanced features:

1. **Hybrid Search**: Combines vector similarity with text matching for more accurate results.
2. **Advanced Filtering**: Supports complex filtering based on metadata and content.
3. **Partitioning**: Improves performance for large collections by partitioning data.
4. **Distributed Deployment**: Supports connecting to standalone servers, clusters, or cloud deployments.
5. **Automatic Index Optimization**: Automatically tunes index parameters based on collection size.

## Prerequisites

To use the Milvus Vector Store, you need:

1. A running Milvus server (either standalone or cluster)
2. The `pymilvus` Python package installed

### Installing Milvus

You can install Milvus using Docker:

```bash
# Pull and start Milvus standalone
docker run -d --name milvus_standalone -p 19530:19530 -p 9091:9091 milvusdb/milvus:latest standalone
```

For more installation options, see the [Milvus documentation](https://milvus.io/docs/install_standalone-docker.md).

### Installing pymilvus

```bash
pip install pymilvus
```

## Basic Usage

### Initializing MilvusVectorStore

```python
from src.deep_research_core.retrieval.vector_store.milvus_vector_store import MilvusVectorStore

# Initialize the vector store
vector_store = MilvusVectorStore(
    collection_name="my_collection",
    connection_args={"host": "localhost", "port": "19530"},
    embedding_dim=1536,
    index_params={
        "metric_type": "COSINE",
        "index_type": "HNSW",
        "params": {"M": 16, "efConstruction": 200}
    }
)
```

### Adding Documents

```python
# Create documents
documents = [
    {
        "id": "doc1",
        "content": "This is a sample document about artificial intelligence.",
        "source": "example.txt",
        "author": "John Doe"
    },
    {
        "id": "doc2",
        "content": "Machine learning is a subset of artificial intelligence.",
        "source": "example.txt",
        "author": "Jane Smith"
    }
]

# Create embeddings (in a real application, these would be generated by an embedding model)
import numpy as np
embeddings = [np.random.rand(1536) for _ in range(len(documents))]

# Add documents to the vector store
ids = [doc["id"] for doc in documents]
vector_store.add(ids, embeddings, documents)
```

### Searching for Similar Documents

#### Vector Search

```python
# Create a query embedding (in a real application, this would be generated by an embedding model)
query_embedding = np.random.rand(1536)

# Search for similar documents
results = vector_store.search(query_embedding, top_k=3)

# Display results
for result in results:
    print(f"ID: {result['id']}")
    print(f"Content: {result['content']}")
    print(f"Score: {result['score']}")
    print()
```

#### Hybrid Search

Hybrid search combines vector similarity with text matching for more accurate results:

```python
# Perform hybrid search
hybrid_results = vector_store.hybrid_search(
    query_embedding=query_embedding,
    query_text="artificial intelligence",
    top_k=3,
    vector_weight=0.7,
    text_weight=0.3
)

# Display results
for result in hybrid_results:
    print(f"ID: {result['id']}")
    print(f"Content: {result['content']}")
    print(f"Score: {result['score']}")
    print(f"Vector Score: {result['vector_score']}")
    print(f"Text Score: {result['text_score']}")
    print()
```

### Other Operations

```python
# Get a document by ID
doc = vector_store.get_document("doc1")

# Get multiple documents by ID
docs = vector_store.get_documents(["doc1", "doc2"])

# Get an embedding by document ID
embedding = vector_store.get_embedding("doc1")

# Delete documents
vector_store.delete(["doc1"])

# Count documents
count = vector_store.count()

# Clear all documents
vector_store.clear()

# Create or recreate the index
vector_store.create_index()

# Optimize the vector store
vector_store.optimize()

# Optimize with automatic index parameter tuning
vector_store.optimize(auto_tune=True)

# Close the vector store connection
vector_store.close()
```

#### Automatic Index Optimization

The MilvusVectorStore includes an automatic index optimization feature that adjusts index parameters based on the collection size:

```python
# Optimize with automatic index parameter tuning
vector_store.optimize(auto_tune=True)
```

This will:
1. Analyze the current collection size
2. Select the optimal index type and parameters based on the size
3. Recreate the index with the optimized parameters

The auto-tuning logic uses these guidelines:
- Small collections (<10K documents): HNSW with high precision
- Medium collections (10K-100K documents): HNSW with balanced precision/performance
- Large collections (100K-1M documents): IVF_FLAT for better scalability
- Very large collections (>1M documents): IVF_SQ8 for memory efficiency

## Using MilvusRAG

The `MilvusRAG` class provides a higher-level interface for Retrieval-Augmented Generation using Milvus as the vector store.

```python
from src.deep_research_core.reasoning.milvus_rag import MilvusRAG

# Initialize MilvusRAG
rag = MilvusRAG(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    embedding_model="text-embedding-ada-002",
    collection_name="my_rag_collection",
    connection_args={"host": "localhost", "port": "19530"},
    embedding_dim=1536,
    top_k=3
)

# Add documents
documents = [
    {
        "content": "The Python programming language was created by Guido van Rossum and first released in 1991.",
        "source": "python_info.txt",
        "title": "Python Programming Language"
    },
    {
        "content": "TensorFlow is an open-source software library for machine learning and artificial intelligence.",
        "source": "tensorflow_info.txt",
        "title": "TensorFlow"
    }
]

rag.add_documents(documents)

# Process a query
result = rag.process("When was Python first released?")

# Display result
print(f"Answer: {result['answer']}")
```

## Advanced Features

### Partitioning

Partitioning improves performance for large collections by dividing data into smaller segments. MilvusVectorStore supports automatic partitioning based on document fields:

```python
# Initialize with partitioning by source field
vector_store = MilvusVectorStore(
    collection_name="my_collection",
    embedding_dim=1536,
    partition_key="source",  # Partition by document source
    auto_create_partitions=True  # Automatically create partitions when adding documents
)

# Add documents with different sources (will be added to different partitions)
vector_store.add(ids, embeddings, documents)

# Search in specific partitions
results = vector_store.search(
    query_embedding=query_embedding,
    top_k=5,
    partition_names=["partition1", "partition2"]
)

# Get list of partitions
partitions = vector_store.get_partitions()
```

### Distributed Deployment

MilvusVectorStore supports connecting to standalone servers, clusters, or cloud deployments:

```python
# Connect to standalone server (default)
vector_store = MilvusVectorStore(
    collection_name="my_collection",
    cluster_mode="standalone",
    connection_args={
        "host": "localhost",
        "port": "19530"
    }
)

# Connect to Milvus cluster
vector_store = MilvusVectorStore(
    collection_name="my_collection",
    cluster_mode="cluster",
    connection_args={
        "hosts": ["node1:19530", "node2:19530", "node3:19530"],
        "user": "username",  # Optional
        "password": "password"  # Optional
    }
)

# Connect to Milvus cloud
vector_store = MilvusVectorStore(
    collection_name="my_collection",
    cluster_mode="cloud",
    connection_args={
        "uri": "https://your-cluster-url.milvus.io",
        "token": "your-api-token",
        "db_name": "default"
    }
)

# Get connection information
connection_info = vector_store.get_connection_info()
```

### Advanced Filtering

MilvusVectorStore supports complex filtering based on metadata and content:

```python
# Search with filtering
results = vector_store.search(
    query_embedding=query_embedding,
    top_k=5,
    filter_by={
        "content": {"contains": "artificial intelligence"},
        "source": ["document1.txt", "document2.txt"],
        "metadata": {
            "category": "technology",
            "date": {"gt": "2023-01-01"}
        }
    }
)

# Advanced search with filtering and sorting
results = vector_store.advanced_search(
    query_embedding=query_embedding,
    query_text="artificial intelligence",
    top_k=5,
    hybrid_search=True,
    filter_by={
        "content": {"contains": "intelligence"},
        "metadata": {
            "page": {"gt": 1}
        }
    },
    sort_by=[
        {"field": "score", "order": "desc"},
        {"field": "metadata.date", "order": "desc"}
    ]
)
```

### Hybrid Search

Hybrid search combines vector similarity with text matching for more accurate results:

```python
# Perform hybrid search
hybrid_results = vector_store.hybrid_search(
    query_embedding=query_embedding,
    query_text="artificial intelligence",
    top_k=5,
    vector_weight=0.7,  # Weight for vector similarity (0.0 to 1.0)
    text_weight=0.3     # Weight for text matching (0.0 to 1.0)
)

# Display results with separate scores
for result in hybrid_results:
    print(f"Content: {result['content']}")
    print(f"Combined Score: {result['score']}")
    print(f"Vector Score: {result['vector_score']}")
    print(f"Text Score: {result['text_score']}")
```

### Document Processing Integration

MilvusVectorStore integrates with the document processing pipeline:

```python
from src.deep_research_core.document_processing.document_processor import DocumentProcessor
from src.deep_research_core.document_processing.text_splitter import RecursiveCharacterTextSplitter
from src.deep_research_core.document_processing.document_loader import TextLoader
from src.deep_research_core.embeddings.openai_embeddings import OpenAIEmbeddings

# Set up document processing pipeline
loader = TextLoader("path/to/document.txt")
text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
embedding_model = OpenAIEmbeddings(model="text-embedding-3-small")

# Create document processor
document_processor = DocumentProcessor(
    loader=loader,
    text_splitter=text_splitter,
    embedding_model=embedding_model
)

# Process documents
documents = document_processor.load()
chunks = document_processor.split(documents)
texts = [doc.page_content for doc in chunks]
embeddings = document_processor.embed(texts)

# Prepare documents for vector store
ids = [f"doc_{i}" for i in range(len(chunks))]
docs = []
for i, doc in enumerate(chunks):
    document = {
        "content": doc.page_content,
        "source": doc.metadata.get("source", ""),
        "metadata": doc.metadata
    }
    docs.append(document)

# Add to vector store
vector_store = MilvusVectorStore(embedding_dim=1536)
vector_store.add(ids, embeddings, docs)
```

## Advanced Configuration

### Index Types

Milvus supports several index types for different use cases:

- **HNSW**: Hierarchical Navigable Small World, good for high-recall scenarios
- **IVF_FLAT**: Inverted File with Flat Vectors, good for large-scale datasets
- **IVF_SQ8**: IVF with Scalar Quantization, good for reducing memory usage
- **IVF_PQ**: IVF with Product Quantization, good for very large-scale datasets

Example:

```python
# HNSW index
hnsw_params = {
    "metric_type": "COSINE",
    "index_type": "HNSW",
    "params": {"M": 16, "efConstruction": 200}
}

# IVF_FLAT index
ivf_flat_params = {
    "metric_type": "COSINE",
    "index_type": "IVF_FLAT",
    "params": {"nlist": 1024}
}

# Initialize with specific index params
vector_store = MilvusVectorStore(
    collection_name="my_collection",
    connection_args={"host": "localhost", "port": "19530"},
    embedding_dim=1536,
    index_params=hnsw_params
)
```

### Metric Types

Milvus supports different distance metrics for similarity search:

- **COSINE**: Cosine similarity (1 - cosine distance)
- **L2**: Euclidean distance
- **IP**: Inner product (dot product)

Example:

```python
# Cosine similarity
cosine_params = {
    "metric_type": "COSINE",
    "index_type": "HNSW",
    "params": {"M": 16, "efConstruction": 200}
}

# Euclidean distance
l2_params = {
    "metric_type": "L2",
    "index_type": "HNSW",
    "params": {"M": 16, "efConstruction": 200}
}

# Inner product
ip_params = {
    "metric_type": "IP",
    "index_type": "HNSW",
    "params": {"M": 16, "efConstruction": 200}
}
```

### Consistency Levels

Milvus supports different consistency levels:

- **Strong**: Ensures data is immediately available for search after insertion
- **Bounded**: Ensures data is available for search after a bounded time
- **Eventually**: Data will eventually be available for search

Example:

```python
# Strong consistency
vector_store = MilvusVectorStore(
    collection_name="my_collection",
    connection_args={"host": "localhost", "port": "19530"},
    embedding_dim=1536,
    consistency_level="Strong"
)

# Bounded consistency
vector_store = MilvusVectorStore(
    collection_name="my_collection",
    connection_args={"host": "localhost", "port": "19530"},
    embedding_dim=1536,
    consistency_level="Bounded"
)
```

## Performance Considerations

- **Index Type**: Choose the appropriate index type based on your dataset size and performance requirements
- **Batch Size**: Add documents in batches for better performance
- **Consistency Level**: Use "Eventually" for better write performance if immediate search is not required
- **Search Parameters**: Adjust search parameters for better recall or performance

## Troubleshooting

### Common Issues

1. **Connection Errors**:
   - Ensure Milvus server is running
   - Check connection parameters (host, port)
   - Verify network connectivity

2. **Search Performance Issues**:
   - Adjust index parameters
   - Increase search parameters (e.g., ef for HNSW)
   - Consider using a different index type

3. **Memory Issues**:
   - Use quantization (IVF_SQ8, IVF_PQ) for large datasets
   - Adjust batch sizes for adding documents

### Logging

The MilvusVectorStore uses structured logging to help with debugging:

```python
import logging
logging.basicConfig(level=logging.INFO)
```

## Examples

See the example scripts in the `examples` directory:

- `milvus_vector_store_example.py`: Basic usage of MilvusVectorStore
- `milvus_rag_example.py`: Using MilvusRAG for retrieval-augmented generation
- `milvus_document_processing_example.py`: Integration with document processing pipeline
- `vector_store_benchmark.py`: Performance benchmarking of vector store implementations

## References

- [Milvus Documentation](https://milvus.io/docs)
- [PyMilvus API Reference](https://milvus.io/api-reference/pymilvus/v2.2.8/About.md)
- [Vector Database Comparison](https://milvus.io/docs/comparison.md)
