# Tài liệu Tính Năng Tìm Kiếm Nâng Cao

## 1. Tổng Quan

Tính năng tìm kiếm nâng cao bao gồm hai module chính:

1. **EnhancedQueryAnalyzer**: Phân tích truy vấn nâng cao để lựa chọn phương thức tìm kiếm tối ưu
2. **ResultAnalyzer**: Phân tích độ tin cậy (factuality) và tình cảm (sentiment) của kết quả tìm kiếm

Hai module này được tích hợp vào `WebSearchAgentAdvanced` để cung cấp trải nghiệm tìm kiếm toàn diện và đáng tin cậy hơn.

## 2. EnhancedQueryAnalyzer

EnhancedQueryAnalyzer mở rộng QueryAnalyzer cơ bản với các tính năng phân tích truy vấn nâng cao:

### 2.1. <PERSON><PERSON><PERSON>

- **<PERSON><PERSON> tích ngữ cảnh**: Hi<PERSON><PERSON> ngữ cảnh sâu hơn của truy vấn tìm kiếm
- **Phân loại truy vấn theo lĩnh vực**: Phát hiện lĩnh vực cụ thể của truy vấn (tài chính, kỹ thuật, y tế, giáo dục, v.v.)
- **Hệ thống chấm điểm đa yếu tố**: Cân nhắc nhiều yếu tố để lựa chọn phương thức tìm kiếm
- **Phát hiện ngôn ngữ nâng cao**: Đặc biệt tối ưu cho tiếng Việt
- **Phân tích ý định sâu**: Hiểu nhu cầu thông tin cụ thể của người dùng

### 2.2. Cơ Chế Phân Tích Truy Vấn

EnhancedQueryAnalyzer sử dụng phương pháp phân tích đa chiều:

1. **Phân tích độ phức tạp**: Đánh giá độ phức tạp của truy vấn dựa trên:
   - Độ dài truy vấn
   - Số lượng từ độc đáo
   - Phép toán nâng cao (AND, OR, NOT)
   - Tính cụ thể của lĩnh vực
   - Độ phức tạp của câu hỏi

2. **Phát hiện lĩnh vực**: Phân loại truy vấn theo lĩnh vực bằng cách sử dụng:
   - Mẫu regex lĩnh vực cụ thể
   - Từ khóa đặc trưng cho lĩnh vực
   - Phân tích cấu trúc truy vấn

3. **Phân tích nhu cầu thông tin thực tế**: Đánh giá mức độ cần thông tin thực tế và đáng tin cậy:
   - Chỉ báo sự thật cao ("nghiên cứu", "thống kê", "dữ liệu")
   - Chỉ báo sự thật trung bình ("bài viết", "tin tức", "theo như")
   - Chỉ báo sự thật thấp ("ý kiến", "tin rằng", "quan điểm")

4. **Phân loại truy vấn nâng cao**: Phân loại chi tiết hơn:
   - Thực tế: "là gì", "ai là", "định nghĩa của"
   - Thủ tục: "cách", "các bước để", "hướng dẫn"
   - So sánh: "so với", "sự khác biệt giữa", "tốt hơn"
   - Khám phá: "ví dụ về", "các loại", "danh sách"
   - Xử lý sự cố: "sửa", "khắc phục", "lỗi"
   - Tìm kiếm ý kiến: "tốt nhất", "đánh giá", "nên"

5. **Tính toán điểm đa yếu tố**: Tính toán điểm số cho mỗi phương thức tìm kiếm dựa trên:
   - Điểm độ phức tạp
   - Loại truy vấn
   - Ý định tìm kiếm
   - Nhu cầu thông tin thực tế
   - Tính cụ thể của truy vấn

### 2.3. Quyết Định Phương Thức Tìm Kiếm

Dựa vào phân tích truy vấn, EnhancedQueryAnalyzer quyết định phương thức tìm kiếm tối ưu:

- **API Search**: Phù hợp cho truy vấn đơn giản, định hướng điều hướng/giao dịch, hoặc cần thông tin mới nhất
- **Crawlee Search**: Phù hợp cho truy vấn phức tạp, yêu cầu thông tin chi tiết, hoặc cần thông tin có độ tin cậy cao

### 2.4. Đề Xuất Công Cụ Tìm Kiếm

EnhancedQueryAnalyzer cũng đề xuất công cụ tìm kiếm phù hợp nhất dựa trên:

- Lĩnh vực của truy vấn (học thuật, sách, bản đồ, kỹ thuật, y tế, v.v.)
- Nhu cầu thông tin thực tế
- Ngôn ngữ truy vấn

Ví dụ:
- Truy vấn học thuật → Google Scholar, Semantic Scholar, arXiv
- Truy vấn y tế → PubMed, Google Scholar
- Truy vấn tiếng Việt → DuckDuckGo (tối ưu cho tiếng Việt)

## 3. ResultAnalyzer

ResultAnalyzer phân tích kết quả tìm kiếm để đánh giá độ tin cậy và tình cảm:

### 3.1. Tính Năng Chính

- **Đánh giá độ tin cậy**: Đánh giá mức độ đáng tin cậy của từng kết quả tìm kiếm
- **Phân tích tình cảm**: Phát hiện tình cảm (tích cực, trung lập, tiêu cực) trong nội dung
- **Đánh giá chất lượng nội dung**: Đánh giá chất lượng tổng thể của nội dung
- **Phát hiện xu hướng**: Phát hiện xu hướng trong nội dung

### 3.2. Cơ Chế Phân Tích Độ Tin Cậy

ResultAnalyzer sử dụng phương pháp phân tích đa chiều để đánh giá độ tin cậy:

1. **Đánh giá tin cậy tên miền**: Đánh giá độ tin cậy của tên miền dựa trên:
   - Cơ sở dữ liệu tin cậy tên miền (edu, gov, org, v.v.)
   - Mẫu tên miền đáng tin cậy cao (research, science, academic)
   - Mẫu tên miền đáng tin cậy thấp (blog, opinion, forum)

2. **Phân tích nội dung thực tế**: Phân tích nội dung để tìm các chỉ báo thực tế:
   - Chỉ báo tin cậy cao ("nghiên cứu chỉ ra", "theo nghiên cứu", "bằng chứng")
   - Chỉ báo tin cậy thấp ("gây sốc", "không thể tin được", "bí mật")
   - Tài liệu tham khảo và trích dẫn
   - Ngày tháng và thống kê
   - Từ định lượng và không chắc chắn
   - Mẫu clickbait

3. **Kết hợp điểm số**: Kết hợp điểm tin cậy tên miền và nội dung:
   - 60% từ độ tin cậy tên miền
   - 40% từ phân tích nội dung

### 3.3. Cơ Chế Phân Tích Tình Cảm

ResultAnalyzer sử dụng nhiều phương pháp để phân tích tình cảm:

1. **VADER Sentiment Analysis**: Sử dụng VADER cho phân tích tình cảm nếu có
2. **TextBlob**: Sử dụng TextBlob như một phương pháp thay thế
3. **Phương pháp dựa trên từ**: Sử dụng từ điển tình cảm cho tiếng Anh và tiếng Việt

Kết quả tình cảm được phân loại thành:
- Tích cực (điểm > 0.1)
- Trung lập (-0.1 ≤ điểm ≤ 0.1)
- Tiêu cực (điểm < -0.1)

### 3.4. Đánh Giá Chất Lượng Nội Dung

ResultAnalyzer đánh giá chất lượng nội dung dựa trên nhiều yếu tố:

1. **Khả năng đọc**: Đánh giá cấu trúc và khả năng đọc của nội dung
2. **Tính thông tin**: Đánh giá mức độ thông tin của nội dung
3. **Trình bày**: Đánh giá cách trình bày nội dung (bảng, hình, biểu đồ)
4. **Độ chính xác**: Đánh giá độ chính xác của thông tin

Các chỉ báo bổ sung:
- Độ dài nội dung
- Cấu trúc HTML (tiêu đề, đoạn văn, danh sách, bảng)

### 3.5. Phát Hiện Xu Hướng

ResultAnalyzer phát hiện xu hướng trong nội dung qua việc tìm kiếm:

1. **Xu hướng chính trị**: Nhận biết ngôn ngữ thiên về cánh tả hoặc cánh hữu
2. **Xu hướng ngôn ngữ**: Phát hiện từ ngữ thiên vị hoặc cực đoan
3. **Xu hướng cảm xúc**: Phát hiện ngôn ngữ mang tính cảm xúc hoặc kịch tính

## 4. WebSearchAgentAdvanced

WebSearchAgentAdvanced tích hợp cả EnhancedQueryAnalyzer và ResultAnalyzer để cung cấp trải nghiệm tìm kiếm nâng cao:

### 4.1. Luồng Tìm Kiếm Nâng Cao

1. **Phân tích truy vấn**: Sử dụng EnhancedQueryAnalyzer để phân tích truy vấn và quyết định phương thức tìm kiếm tối ưu
2. **Thực hiện tìm kiếm**: Sử dụng phương thức tìm kiếm được đề xuất để thực hiện tìm kiếm
3. **Phân tích kết quả**: Sử dụng ResultAnalyzer để phân tích độ tin cậy và tình cảm của kết quả
4. **Trả về kết quả phân tích**: Trả về kết quả tìm kiếm cùng với phân tích

### 4.2. Tính Năng Lọc Kết Quả

WebSearchAgentAdvanced cung cấp các phương thức để lọc kết quả:

1. **Lọc theo độ tin cậy**: Chỉ trả về kết quả có độ tin cậy cao
   ```python
   credible_results = agent.get_credible_results(search_results, min_factuality_score=0.7)
   ```

2. **Lọc theo tình cảm**: Chỉ trả về kết quả có tình cảm cụ thể
   ```python
   positive_results = agent.get_sentiment_filtered_results(search_results, sentiment="positive")
   ```

3. **Tìm kiếm với lọc độ tin cậy**: Thực hiện tìm kiếm và chỉ trả về kết quả đáng tin cậy
   ```python
   results = agent.search_with_factuality_filter(query, min_factuality_score=0.7)
   ```

### 4.3. Phân Tích Nội Dung Đơn Lẻ

WebSearchAgentAdvanced cũng cung cấp phương thức để phân tích nội dung từ một URL cụ thể:

```python
content_analysis = agent.extract_content_with_analysis(url)
```

Kết quả bao gồm:
- Nội dung được trích xuất
- Phân tích độ tin cậy
- Phân tích tình cảm
- Các chỉ số chất lượng

## 5. Ví Dụ Sử Dụng

Xem tệp `examples/enhanced_search_example.py` để biết ví dụ đầy đủ về cách sử dụng các tính năng tìm kiếm nâng cao.

### 5.1. Khởi Tạo WebSearchAgentAdvanced

```python
from src.deep_research_core.agents.web_search_agent_advanced import WebSearchAgentAdvanced

agent = WebSearchAgentAdvanced(
    search_method="auto",
    use_enhanced_query_analysis=True,
    use_result_analysis=True
)
```

### 5.2. Phân Tích Truy Vấn

```python
query = "Nghiên cứu mới nhất về trí tuệ nhân tạo"
analysis = agent._analyze_query(query)
print(f"Language: {analysis['language']}")
print(f"Recommended method: {analysis['recommended_search_method']}")
print(f"Recommended engines: {analysis['recommended_engines']}")
```

### 5.3. Tìm Kiếm Với Phân Tích

```python
results = agent.search(
    query="artificial intelligence ethics",
    num_results=5,
    get_content=True,
    analyze_results=True
)

# Lấy chỉ số phân tích
factuality_score = results["analysis"]["overall_factuality_score"]
sentiment_score = results["analysis"]["overall_sentiment_score"]
```

### 5.4. Lọc Kết Quả Theo Độ Tin Cậy

```python
credible_results = agent.search_with_factuality_filter(
    query="climate change research",
    min_factuality_score=0.7
)
```

### 5.5. Lọc Kết Quả Theo Tình Cảm

```python
positive_results = agent.get_sentiment_filtered_results(
    search_results=results,
    sentiment="positive"
)
```

## 6. Lợi Ích Chính

### 6.1. Chất Lượng Tìm Kiếm Được Cải Thiện

- Lựa chọn phương thức tìm kiếm tối ưu cho mỗi truy vấn
- Ưu tiên nguồn đáng tin cậy
- Lọc ra kết quả kém chất lượng hoặc không đáng tin cậy

### 6.2. Hỗ Trợ Tiếng Việt Nâng Cao

- Phát hiện ngôn ngữ tiếng Việt tốt hơn
- Phân tích tình cảm cho nội dung tiếng Việt
- Tối ưu hóa tìm kiếm cho truy vấn tiếng Việt

### 6.3. Công Cụ Đánh Giá Thông Tin

- Đánh giá độ tin cậy của nguồn và nội dung
- Phát hiện nội dung thiên vị hoặc cảm xúc
- Đánh giá chất lượng tổng thể của thông tin

### 6.4. Tích Hợp Linh Hoạt

- Tích hợp dễ dàng với các ứng dụng hiện có
- Cấu hình linh hoạt cho nhu cầu cụ thể
- Khả năng mở rộng với các phương pháp phân tích mới

## 7. Phát Triển Tương Lai

### 7.1. Cải Thiện Độ Chính Xác

- Mở rộng cơ sở dữ liệu tin cậy tên miền
- Cải thiện phương pháp phân tích nội dung
- Tích hợp các mô hình ML cho phân loại

### 7.2. Tính Năng Mới

- Phát hiện thông tin sai lệch tích cực
- Tích hợp với mô hình ngôn ngữ lớn cho phân tích sâu hơn
- Công cụ trực quan hóa kết quả phân tích

### 7.3. Hiệu Suất Tối Ưu

- Cải thiện hiệu suất phân tích cho nội dung lớn
- Cơ chế cache thông minh hơn
- Xử lý song song cho phân tích nhanh hơn

### 7.4. Đa Ngôn Ngữ Mở Rộng

- Mở rộng hỗ trợ cho nhiều ngôn ngữ
- Phân tích ngữ cảnh đặc thù cho từng ngôn ngữ
- Cơ sở dữ liệu tin cậy đa ngôn ngữ 