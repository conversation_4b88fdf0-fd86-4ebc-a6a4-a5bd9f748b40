# Mô hình Embedding Tiếng Việt

Tài liệu này mô tả các mô hình embedding tiếng Việt có sẵn trong Deep Research Core và cách sử dụng chúng.

## Giới thiệu

Deep Research Core cung cấp các mô hình embedding tiếng Việt chuyên biệt để hỗ trợ xử lý ngôn ngữ tự nhiên tiếng Việt. Các mô hình này được tối ưu hóa cho tiếng Việt và cung cấp biểu diễn vector chất lượng cao cho văn bản tiếng Việt.

## Các mô hình có sẵn

### PhoBERT

PhoBERT là mô hình BERT được huấn luyện trên dữ liệu tiếng Việt lớn, cung cấp biểu diễn ngữ nghĩa phong phú cho văn bản tiếng Việt.

- **phobert-base**: <PERSON><PERSON> hình PhoBERT cơ bản (768 chiều)
- **phobert-large**: <PERSON><PERSON> hình PhoBERT lớn (1024 chiều)

### VieBERT

VieBERT là mô hình BERT được huấn luyện đặc biệt cho tiếng Việt, cung cấp biểu diễn ngữ nghĩa phong phú cho văn bản tiếng Việt.

- **viebert-base**: Mô hình VieBERT cơ bản (768 chiều)

### XLM-RoBERTa-Vi

XLM-RoBERTa là mô hình đa ngôn ngữ có hỗ trợ tốt cho tiếng Việt, cho phép biểu diễn văn bản tiếng Việt trong không gian ngữ nghĩa đa ngôn ngữ.

- **xlm-roberta-vi-base**: Mô hình XLM-RoBERTa cơ bản (768 chiều)
- **xlm-roberta-vi-large**: Mô hình XLM-RoBERTa lớn (1024 chiều)

### Multilingual-E5

Multilingual-E5 là mô hình embedding đa ngôn ngữ được tối ưu hóa cho tìm kiếm ngữ nghĩa, hỗ trợ hơn 100 ngôn ngữ bao gồm tiếng Việt.

- **vietnamese-multilingual-e5-base**: Mô hình Multilingual-E5 cơ bản (768 chiều)
- **vietnamese-multilingual-e5-large**: Mô hình Multilingual-E5 lớn (1024 chiều)

## Cài đặt

Để sử dụng các mô hình embedding tiếng Việt, bạn cần cài đặt các thư viện cần thiết:

```bash
pip install -r requirements.txt
```

## Sử dụng cơ bản

### Sử dụng trực tiếp

```python
from src.deep_research_core.models.embeddings import (
    VietnamesePhoBERT,
    VietnameseVieBERT,
    VietnameseXLMRoBERTa,
    VietnameseMultilingualE5
)

# Khởi tạo mô hình PhoBERT
phobert = VietnamesePhoBERT(
    model_name="vinai/phobert-base",
    device="cuda",  # hoặc "cpu", "mps"
    use_preprocessing=True  # sử dụng tiền xử lý tiếng Việt
)

# Lấy embedding cho văn bản
text = "Trí tuệ nhân tạo đang phát triển nhanh chóng ở Việt Nam."
embedding = phobert.encode(text)

# Lấy embedding cho nhiều văn bản
texts = [
    "Việt Nam đang ứng dụng trí tuệ nhân tạo vào nhiều lĩnh vực.",
    "Thị trường chứng khoán Việt Nam có nhiều biến động.",
    "Công nghệ AI đang được đầu tư mạnh mẽ."
]
embeddings = phobert.encode(texts)
```

### Sử dụng hàm get_embedding_model

```python
from src.deep_research_core.models.embeddings import get_embedding_model

# Khởi tạo mô hình PhoBERT
phobert = get_embedding_model("phobert-base")

# Khởi tạo mô hình VieBERT
viebert = get_embedding_model("viebert-base")

# Khởi tạo mô hình XLM-RoBERTa
xlm_roberta = get_embedding_model("xlm-roberta-vi-base")

# Khởi tạo mô hình Multilingual-E5
multilingual_e5 = get_embedding_model("vietnamese-multilingual-e5-base")
```

## Tích hợp với VietnameseTextProcessor

Các mô hình embedding tiếng Việt có thể được tích hợp với `VietnameseTextProcessor` để tiền xử lý văn bản tiếng Việt trước khi tạo embedding:

```python
from src.deep_research_core.models.embeddings import VietnamesePhoBERT
from src.deep_research_core.utils.vietnamese_text_processor import VietnameseTextProcessor

# Khởi tạo Vietnamese text processor
text_processor = VietnameseTextProcessor()

# Tiền xử lý văn bản
text = "Trí tuệ nhân tạo đang phát triển nhanh chóng ở Việt Nam."
processed_text = text_processor.word_segmentation(text)

# Khởi tạo mô hình PhoBERT với tiền xử lý tự động
phobert = VietnamesePhoBERT(
    model_name="vinai/phobert-base",
    use_preprocessing=True  # tự động sử dụng VietnameseTextProcessor
)

# Lấy embedding
embedding = phobert.encode(text)  # tiền xử lý được thực hiện tự động
```

## Tích hợp với CoTRAG

Các mô hình embedding tiếng Việt có thể được tích hợp với CoTRAG thông qua `CoTRAGVietnameseEmbeddingAdapter`:

```python
from src.deep_research_core.reasoning.cot_rag import CoTRAG
from src.deep_research_core.reasoning.cotrag_vietnamese_embedding_adapter import CoTRAGVietnameseEmbeddingAdapter

# Khởi tạo CoTRAG
cotrag = CoTRAG(
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free",
    temperature=0.7,
    max_tokens=2000
)

# Tạo adapter cho mô hình embedding tiếng Việt
vietnamese_adapter = CoTRAGVietnameseEmbeddingAdapter(
    cotrag_instance=cotrag,
    embedding_model="phobert-base",
    device="cuda",
    use_hybrid_search=True,
    use_vietnamese_preprocessing=True
)

# Sử dụng adapter
query = "Trí tuệ nhân tạo ở Việt Nam hiện nay như thế nào?"
result = vietnamese_adapter.process(query)
```

## Lựa chọn mô hình phù hợp

Dưới đây là một số hướng dẫn để lựa chọn mô hình embedding tiếng Việt phù hợp:

- **PhoBERT**: Lựa chọn tốt cho hầu hết các ứng dụng tiếng Việt, đặc biệt là phân tích văn bản tổng quát.
- **VieBERT**: Phù hợp cho các ứng dụng yêu cầu hiểu biết sâu về ngữ pháp tiếng Việt.
- **XLM-RoBERTa-Vi**: Tốt cho các ứng dụng đa ngôn ngữ cần hỗ trợ tiếng Việt.
- **Multilingual-E5**: Phù hợp cho tìm kiếm ngữ nghĩa đa ngôn ngữ.

## Ví dụ

Xem file `examples/vietnamese_embeddings_example.py` để biết thêm chi tiết về cách sử dụng các mô hình embedding tiếng Việt.

```bash
python examples/vietnamese_embeddings_example.py
```
