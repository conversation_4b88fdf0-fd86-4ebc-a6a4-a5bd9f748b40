# RAG Implementations Guide

This guide provides information about the different Retrieval-Augmented Generation (RAG) implementations available in the Deep Research Core, how to choose between them, and how to use them effectively.

## Available RAG Implementations

The Deep Research Core provides the following RAG implementations:

1. **BaseRAG**: An abstract base class that defines the common interface for all RAG implementations.
2. **MilvusRAG**: A RAG implementation using Milvus as the vector store.
3. **SQLiteVectorRAG**: A RAG implementation using SQLite with vector search capabilities.

All implementations share a common interface defined by the `BaseRAG` abstract class, making it easy to switch between them or add new implementations.

## Choosing the Right Implementation

### MilvusRAG

**Best for:**
- Production environments
- Large datasets (100,000+ documents)
- High-performance requirements
- Distributed deployments
- Multi-user environments

**Advantages:**
- High-performance vector search
- Scalable to millions of vectors
- Support for ANN (Approximate Nearest Neighbors) algorithms
- Distributed architecture
- Optimized for large-scale deployments

**Disadvantages:**
- Requires running a Milvus server
- More complex setup
- Higher resource requirements
- External dependencies

### SQLiteVectorRAG

**Best for:**
- Development and testing
- Small to medium datasets (< 100,000 documents)
- Simple deployments
- Offline environments
- Resource-constrained environments

**Advantages:**
- Simple setup (single file)
- No external dependencies
- Works offline
- Low resource requirements
- Integrated hybrid search (vector + keyword)
- Built-in evaluation capabilities

**Disadvantages:**
- Limited scalability
- Lower performance with large datasets
- No distributed capabilities
- Less optimized vector search

## Common Interface

All RAG implementations share the following common interface:

```python
# Initialize
rag = RagImplementation(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    embedding_model="text-embedding-ada-002",
    top_k=5
)

# Add documents
rag.add_documents(documents)

# Search for relevant documents
results = rag.search(query, top_k=5)

# Process a query using RAG
response = rag.process(query)

# Clear all documents
rag.clear()

# Get document count
count = rag.count()

# Close connection
rag.close()
```

## Implementation-Specific Features

### MilvusRAG

```python
# Initialize with Milvus-specific parameters
milvus_rag = MilvusRAG(
    provider="openai",
    model="gpt-4o",
    collection_name="rag_documents",
    connection_args={"host": "localhost", "port": "19530"},
    embedding_dim=1536
)

# Search with filter expression
results = milvus_rag.search(query, filter_expr="source == 'Wikipedia'")

# Process with filter expression
response = milvus_rag.process(query, filter_expr="date >= '2023-01-01'")
```

### SQLiteVectorRAG

```python
# Initialize with SQLite-specific parameters
sqlite_rag = SQLiteVectorRAG(
    provider="openai",
    model="gpt-4o",
    db_path="rag_documents.db",
    chunk_size=1000,
    chunk_overlap=200
)

# Search with hybrid weight
results = sqlite_rag.search(query, hybrid_weight=0.7)

# Process with custom prompts and evaluation
response = sqlite_rag.process(
    query,
    hybrid_weight=0.7,
    custom_system_prompt="You are a helpful assistant...",
    custom_user_prompt="Answer the question: {query}\nContext: {context}",
    save_evaluation=True
)

# Evaluate query performance
sqlite_rag.evaluate_query(query_id, feedback="Good answer", relevance_score=0.9)

# Get evaluation statistics
stats = sqlite_rag.get_evaluation_stats()
```

## Monitoring and Performance

All RAG implementations include built-in monitoring capabilities:

- **Structured Logging**: Detailed logs with contextual information
- **Performance Metrics**: Latency, throughput, and memory usage tracking
- **Distributed Tracing**: Trace request flow through the system
- **Alerting**: Detect and notify about performance issues

These monitoring features help you understand how your RAG implementation is performing and identify potential issues.

## Switching Between Implementations

Thanks to the common interface, switching between implementations is straightforward:

```python
# Using MilvusRAG
from deep_research_core.reasoning import MilvusRAG

rag = MilvusRAG(
    provider="openai",
    model="gpt-4o"
)

# Using SQLiteVectorRAG
from deep_research_core.reasoning import SQLiteVectorRAG

rag = SQLiteVectorRAG(
    provider="openai",
    model="gpt-4o"
)

# The rest of your code remains the same
documents = [...]
rag.add_documents(documents)
response = rag.process("What is RAG?")
```

## Best Practices

1. **Choose the right implementation for your use case**:
   - Use MilvusRAG for production with large datasets
   - Use SQLiteVectorRAG for development, testing, or small deployments

2. **Optimize document chunking**:
   - Use appropriate chunk sizes (500-1000 tokens)
   - Use appropriate overlap (10-20% of chunk size)
   - Include metadata for better filtering

3. **Monitor performance**:
   - Track latency, throughput, and memory usage
   - Set up alerts for performance issues
   - Use distributed tracing to identify bottlenecks

4. **Implement error handling**:
   - Handle API errors gracefully
   - Implement retries for transient errors
   - Log detailed error information

5. **Evaluate and improve**:
   - Use the built-in evaluation capabilities
   - Collect user feedback
   - Continuously improve your RAG implementation

## Example: Comparing Implementations

See the `examples/rag_comparison_example.py` file for a complete example of how to use and compare different RAG implementations.

## Conclusion

The Deep Research Core provides multiple RAG implementations to suit different needs. By understanding the strengths and weaknesses of each implementation, you can choose the right one for your specific use case and get the best performance and results.

For more information, see the API documentation for each implementation:
- [BaseRAG API Reference](../api/base_rag.md)
- [MilvusRAG API Reference](../api/milvus_rag.md)
- [SQLiteVectorRAG API Reference](../api/sqlite_vector_rag.md)
