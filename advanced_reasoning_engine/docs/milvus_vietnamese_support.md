# Milvus Vector Store Vietnamese Language Support

This guide provides information on using the Milvus Vector Store implementation in Deep Research Core with Vietnamese language support.

## Overview

The MilvusVectorStore implementation in Deep Research Core provides excellent support for multilingual applications, including Vietnamese language. This document explains how to optimize the vector store for Vietnamese language documents and queries.

## Multilingual Embeddings

For Vietnamese language support, it's important to use multilingual embedding models that have been trained on Vietnamese text. Here are some recommended models:

1. **Multilingual Models from Sentence Transformers**:
   - `paraphrase-multilingual-MiniLM-L12-v2`: Good balance of performance and size
   - `paraphrase-multilingual-mpnet-base-v2`: Higher quality but larger model
   - `distiluse-base-multilingual-cased-v2`: Good for semantic similarity

2. **Vietnamese-specific Models**:
   - `PhoBERT`: Pre-trained BERT model for Vietnamese
   - `VieBERT`: Vietnamese BERT model
   - `XLM-RoBERTa-Vi`: XLM-RoBERTa fine-tuned for Vietnamese
   - `Multilingual-E5`: Multilingual embedding model with good Vietnamese support

## Example Usage

Here's an example of using MilvusVectorStore with Vietnamese language documents:

```python
from src.deep_research_core.retrieval.vector_store.milvus_vector_store import MilvusVectorStore
from sentence_transformers import SentenceTransformer

# Initialize multilingual embedding model
model = SentenceTransformer("paraphrase-multilingual-MiniLM-L12-v2")

# Vietnamese documents
documents = [
    {
        "content": "Trí tuệ nhân tạo đang phát triển nhanh chóng tại Việt Nam.",
        "source": "vietnamese_ai.txt",
        "title": "Trí tuệ nhân tạo tại Việt Nam"
    },
    {
        "content": "Các công ty công nghệ Việt Nam đang đầu tư vào nghiên cứu học máy.",
        "source": "vietnamese_tech.txt",
        "title": "Công nghệ tại Việt Nam"
    }
]

# Generate embeddings
texts = [doc["content"] for doc in documents]
embeddings = model.encode(texts, convert_to_numpy=True)

# Initialize MilvusVectorStore
vector_store = MilvusVectorStore(
    collection_name="vietnamese_collection",
    connection_args={"host": "localhost", "port": "19530"},
    embedding_dim=384  # Dimension for multilingual models
)

# Add documents
ids = ["vi_doc1", "vi_doc2"]
vector_store.add(ids, embeddings, documents)

# Search in Vietnamese
query_text = "công nghệ trí tuệ nhân tạo"
query_embedding = model.encode(query_text, convert_to_numpy=True)
results = vector_store.search(query_embedding, top_k=2)

# Display results
for result in results:
    print(f"Title: {result['title']}")
    print(f"Content: {result['content']}")
    print(f"Score: {result['score']:.4f}")
    print()
```

## Vietnamese Text Processing

When working with Vietnamese text, consider these optimizations:

1. **Word Segmentation**: Vietnamese words can be compound words without spaces. Consider using a Vietnamese word segmentation tool like `underthesea` or `pyvi` before embedding.

2. **Stopword Removal**: Remove common Vietnamese stopwords to improve embedding quality.

3. **Tone Mark Normalization**: Normalize Vietnamese tone marks for better matching.

Example of Vietnamese text preprocessing:

```python
try:
    from underthesea import word_tokenize
    
    def preprocess_vietnamese(text):
        # Word segmentation
        segmented = word_tokenize(text, format="text")
        return segmented
except ImportError:
    def preprocess_vietnamese(text):
        return text  # Fallback if underthesea is not available
```

## Hybrid Search for Vietnamese

Hybrid search combines vector similarity with text matching, which can be particularly effective for Vietnamese:

```python
# Perform hybrid search
hybrid_results = vector_store.hybrid_search(
    query_embedding=query_embedding,
    query_text="công nghệ trí tuệ nhân tạo",
    top_k=3,
    vector_weight=0.7,  # Weight for vector similarity
    text_weight=0.3     # Weight for text matching
)
```

## Partitioning by Language

If your application handles multiple languages, consider partitioning the vector store by language:

```python
# Initialize with partitioning by language
vector_store = MilvusVectorStore(
    collection_name="multilingual_collection",
    embedding_dim=384,
    partition_key="language",  # Partition by language
    auto_create_partitions=True
)

# Add documents with language metadata
documents = [
    {
        "content": "Artificial intelligence is developing rapidly.",
        "source": "english_doc.txt",
        "language": "en"
    },
    {
        "content": "Trí tuệ nhân tạo đang phát triển nhanh chóng.",
        "source": "vietnamese_doc.txt",
        "language": "vi"
    }
]

# Add documents
vector_store.add(ids, embeddings, documents)

# Search only in Vietnamese documents
vi_results = vector_store.search(
    query_embedding=query_embedding,
    top_k=5,
    partition_names=["vi"]
)
```

## Performance Considerations for Vietnamese

1. **Embedding Model Selection**: Choose a multilingual model with good Vietnamese support. Larger models generally provide better quality but are slower.

2. **Index Type**: For Vietnamese text, HNSW index type often provides the best balance of performance and accuracy.

3. **Metric Type**: Cosine similarity (`COSINE`) is generally recommended for multilingual embeddings.

4. **Hybrid Search Weights**: Experiment with different weights for vector and text components. For Vietnamese, a higher weight for vector similarity (0.7-0.8) often works well.

## Example: Vietnamese RAG Application

Here's an example of using MilvusVectorStore in a Retrieval-Augmented Generation (RAG) application for Vietnamese:

```python
from src.deep_research_core.reasoning.milvus_rag import MilvusRAG

# Initialize MilvusRAG with Vietnamese support
rag = MilvusRAG(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    embedding_model="paraphrase-multilingual-MiniLM-L12-v2",
    collection_name="vietnamese_rag",
    connection_args={"host": "localhost", "port": "19530"},
    embedding_dim=384,
    top_k=3
)

# Add Vietnamese documents
documents = [
    {
        "content": "Trí tuệ nhân tạo (AI) là một nhánh của khoa học máy tính liên quan đến việc phát triển các hệ thống máy tính có khả năng thực hiện các nhiệm vụ thường đòi hỏi trí thông minh của con người.",
        "source": "vi_ai_intro.txt",
        "title": "Giới thiệu về Trí tuệ nhân tạo"
    },
    {
        "content": "Học máy là một phần của trí tuệ nhân tạo, tập trung vào việc phát triển các thuật toán cho phép máy tính học từ dữ liệu và đưa ra dự đoán hoặc quyết định mà không cần được lập trình rõ ràng.",
        "source": "vi_machine_learning.txt",
        "title": "Học máy"
    }
]

rag.add_documents(documents)

# Process a Vietnamese query
result = rag.process("Trí tuệ nhân tạo là gì và nó liên quan đến học máy như thế nào?")

print(f"Answer: {result['answer']}")
```

## Conclusion

MilvusVectorStore provides robust support for Vietnamese language applications when used with appropriate multilingual embedding models. By following the guidelines in this document, you can optimize your vector search for Vietnamese text and build high-quality multilingual applications.

For more detailed examples, see the `milvus_vietnamese_example.py` file in the examples directory.
