# Custom Extensions Guide

This document provides guidance for extending the Deep Research Core system with custom components and functionality.

## Overview

The Deep Research Core system is designed to be highly extensible. You can extend various components including:

1. Reasoning methods
2. Models and providers
3. Retrieval methods
4. Tools and agents
5. Visualization components
6. UI components

This guide will walk you through the process of creating custom extensions for each of these areas.

## Table of Contents

- [Extension Points](#extension-points)
- [Creating Custom Reasoning Methods](#creating-custom-reasoning-methods)
- [Adding New Models and Providers](#adding-new-models-and-providers)
- [Implementing Custom Retrieval Methods](#implementing-custom-retrieval-methods)
- [Building Custom Tools and Agents](#building-custom-tools-and-agents)
- [Creating Visualization Extensions](#creating-visualization-extensions)
- [Extending the UI](#extending-the-ui)
- [Testing Extensions](#testing-extensions)
- [Publishing Extensions](#publishing-extensions)

## Extension Points

The Deep Research Core provides several extension points:

| Component Type | Base Class/Interface | Directory | Purpose |
|----------------|---------------------|-----------|---------|
| Reasoning Methods | `BaseReasoner` | `src/deep_research_core/reasoning/` | Add new reasoning techniques |
| Model Providers | `BaseAPIProvider` | `src/deep_research_core/models/api/` | Integrate new AI model providers |
| Retrieval Methods | `BaseRetriever` | `src/deep_research_core/retrieval/` | Implement new retrieval techniques |
| Tools | `BaseTool` | `src/deep_research_core/tools/` | Create new tools for agents |
| Agents | `BaseAgent` | `src/deep_research_core/agents/` | Implement specialized agents |
| Visualizations | `BaseVisualizer` | `src/deep_research_core/visualization/` | Create custom visualizations |
| UI Components | React Components | `frontend/src/components/` | Add new UI features |

## Creating Custom Reasoning Methods

To create a custom reasoning method, you need to extend the `BaseReasoner` class:

1. Create a new file in `src/deep_research_core/reasoning/your_reasoner.py`
2. Implement the required methods

```python
from typing import Dict, Any, List, Optional
from .base import BaseReasoner, register_reasoner

@register_reasoner
class YourCustomReasoner(BaseReasoner):
    """
    Your custom reasoning method.
    
    Add a detailed description of your reasoning approach.
    """
    
    name = "your_custom_reasoner"
    description = "Description of your custom reasoner"
    
    def __init__(self, **kwargs):
        """Initialize your custom reasoner."""
        super().__init__(**kwargs)
        # Add your initialization code here
    
    def generate_reasoning(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Generate reasoning based on the query.
        
        Args:
            query: The user query to reason about
            **kwargs: Additional arguments
            
        Returns:
            Dictionary containing the reasoning results
        """
        # Implement your reasoning logic here
        pass
```

## Adding New Models and Providers

To add a new model provider:

1. Create a new file in `src/deep_research_core/models/api/your_provider.py`
2. Implement the required methods by extending `BaseAPIProvider`

```python
from typing import Dict, Any, List, Optional
import os
import requests
from .base import BaseAPIProvider

class YourProviderAPI(BaseAPIProvider):
    """
    API client for YourProvider.
    
    This class handles communication with the YourProvider API.
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        api_base: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize the YourProvider API client.
        
        Args:
            api_key: API key for YourProvider. If None, will try to get from environment.
            api_base: Base URL for the API. If None, will use the default.
            **kwargs: Additional implementation-specific arguments
        """
        api_key = api_key or os.environ.get("YOUR_PROVIDER_API_KEY")
        api_base = api_base or "https://api.yourprovider.com/v1"
        
        super().__init__(api_key=api_key, api_base=api_base, **kwargs)
        
        # Add available models
        self.available_models = {
            "your-model-name": {
                "id": "your-model-name",
                "name": "Your Model Name",
                "context_length": 8192,
                "supports_functions": True
            }
        }
    
    def generate(
        self,
        prompt: str,
        model: str,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate text using YourProvider's API.
        
        Args:
            prompt: The prompt to generate text from
            model: The model to use
            max_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature
            **kwargs: Additional arguments
            
        Returns:
            Dictionary containing the generated text and metadata
        """
        # Implement your API call logic here
        pass
```

3. Register your provider in `src/deep_research_core/models/api/__init__.py`

## Implementing Custom Retrieval Methods

To create a custom retrieval method:

1. Create a new file in `src/deep_research_core/retrieval/your_retriever.py`
2. Implement the required methods by extending `BaseRetriever`

```python
from typing import Dict, Any, List, Optional
from .base import BaseRetriever, register_retriever

@register_retriever
class YourCustomRetriever(BaseRetriever):
    """
    Your custom retrieval method.
    
    Add a detailed description of your retrieval approach.
    """
    
    name = "your_custom_retriever"
    description = "Description of your custom retriever"
    
    def __init__(self, **kwargs):
        """Initialize your custom retriever."""
        super().__init__(**kwargs)
        # Add your initialization code here
    
    def retrieve(self, query: str, top_k: int = 3, **kwargs) -> List[Dict[str, Any]]:
        """
        Retrieve documents based on the query.
        
        Args:
            query: The query to retrieve documents for
            top_k: Number of documents to retrieve
            **kwargs: Additional arguments
            
        Returns:
            List of retrieved documents
        """
        # Implement your retrieval logic here
        pass
```

## Building Custom Tools and Agents

To create a custom tool:

1. Create a new file in `src/deep_research_core/tools/your_tool.py`
2. Implement the required methods by extending `BaseTool`

```python
from typing import Dict, Any, List, Optional
from .base import BaseTool, register_tool

@register_tool
class YourCustomTool(BaseTool):
    """
    Your custom tool.
    
    Add a detailed description of your tool's functionality.
    """
    
    name = "your_custom_tool"
    description = "Description of your custom tool"
    
    def __init__(self, **kwargs):
        """Initialize your custom tool."""
        super().__init__(**kwargs)
        # Add your initialization code here
    
    def run(self, **kwargs) -> Dict[str, Any]:
        """
        Run the tool.
        
        Args:
            **kwargs: Tool-specific arguments
            
        Returns:
            Dictionary containing the tool's output
        """
        # Implement your tool logic here
        pass
```

To create a custom agent:

1. Create a new file in `src/deep_research_core/agents/your_agent.py`
2. Implement the required methods by extending `BaseAgent`

## Creating Visualization Extensions

To create a custom visualization:

1. Create a new file in `src/deep_research_core/visualization/your_visualizer.py`
2. Implement the required methods by extending `BaseVisualizer`

```python
from typing import Dict, Any, List, Optional
import matplotlib.pyplot as plt
import os
from .base import BaseVisualizer, register_visualizer

@register_visualizer
class YourCustomVisualizer(BaseVisualizer):
    """
    Your custom visualization.
    
    Add a detailed description of your visualization approach.
    """
    
    name = "your_custom_visualizer"
    description = "Description of your custom visualizer"
    
    def __init__(self, output_dir: str = "visualizations", **kwargs):
        """Initialize your custom visualizer."""
        super().__init__(output_dir=output_dir, **kwargs)
        # Add your initialization code here
    
    def visualize(self, data: Dict[str, Any], **kwargs) -> str:
        """
        Create a visualization from the provided data.
        
        Args:
            data: The data to visualize
            **kwargs: Additional arguments
            
        Returns:
            Path to the generated visualization file
        """
        # Implement your visualization logic here
        pass
```

## Extending the UI

To create a custom UI component:

1. Create a new file in `frontend/src/components/your_component/YourComponent.tsx`
2. Implement your React component

```typescript
import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button
} from '@mui/material';

interface YourComponentProps {
  // Define your component props
  title: string;
  data: any;
  onAction: (data: any) => void;
}

const YourComponent: React.FC<YourComponentProps> = ({
  title,
  data,
  onAction
}) => {
  // Component state
  const [state, setState] = useState<any>(null);
  
  // Effects
  useEffect(() => {
    // Initialize component
  }, []);
  
  // Event handlers
  const handleClick = () => {
    onAction(data);
  };
  
  return (
    <Paper elevation={2} sx={{ p: 2 }}>
      <Typography variant="h6">{title}</Typography>
      
      {/* Component content */}
      <Box sx={{ mt: 2 }}>
        {/* Render your component UI */}
      </Box>
      
      <Button 
        variant="contained" 
        onClick={handleClick}
        sx={{ mt: 2 }}
      >
        Perform Action
      </Button>
    </Paper>
  );
};

export default YourComponent;
```

3. Export your component in an index file
4. Use your component in a page or another component

## Testing Extensions

It's important to test your extensions thoroughly:

1. Create unit tests in the `tests/unit/` directory corresponding to your extension's type
2. Create integration tests in the `tests/integration/` directory to test interaction with other components
3. Run tests using pytest: `pytest tests/unit/your_tests.py`

Example unit test for a custom reasoner:

```python
import pytest
from deep_research_core.reasoning.your_reasoner import YourCustomReasoner

def test_your_reasoner_initialization():
    reasoner = YourCustomReasoner()
    assert reasoner.name == "your_custom_reasoner"

def test_your_reasoner_generate():
    reasoner = YourCustomReasoner()
    result = reasoner.generate_reasoning("Test query")
    
    # Assert expected behavior
    assert "answer" in result
    assert isinstance(result["answer"], str)
```

## Publishing Extensions

To share your extensions with others:

1. Create a separate Python package for your extension
2. Include installation instructions
3. Document dependencies and requirements
4. Provide examples of usage
5. Consider submitting your extension to the Deep Research Core extension registry (coming soon)

## Best Practices

1. Follow the existing code style and patterns
2. Add comprehensive documentation to your extension
3. Include unit tests covering all functionality
4. Handle errors gracefully
5. Make your extension configurable
6. Support both English and Vietnamese languages if possible
7. Implement proper logging
8. Optimize performance where possible

## Example Extensions

For examples of custom extensions, see the `examples/extensions/` directory:

- `examples/extensions/reasoning/collaborative_reasoner.py`: A custom reasoner that combines multiple reasoning methods
- `examples/extensions/retrieval/image_retriever.py`: A retriever for image content
- `examples/extensions/tools/web_search_tool.py`: A tool for performing web searches
- `examples/extensions/visualization/network_visualizer.py`: A visualizer for displaying network relationships

## Getting Help

If you need assistance with creating custom extensions:

1. Check the detailed API documentation
2. Review the example extensions
3. Read the developer guide
4. Join the Deep Research Core community forum
5. Submit an issue on GitHub 