# EnhancedRAGTOTCOTReasoner

EnhancedRAGTOTCOTReasoner là phiên bản nâng cao của RAGTOTCOTReasoner, kết hợp Retrieval-Augmented Generation (RAG), Tree of Thought (TOT), và Chain of Thought (COT) với các tính năng tối ưu hóa nâng cao.

## Tính năng chính

1. **Tối ưu hóa nâng cao cho việc kết hợp cả ba kỹ thuật**
   - Điều chỉnh trọng số tự động giữa RAG, TOT và COT dựa trên loại truy vấn
   - Phân tích hiệu suất chi tiết về đóng góp của từng kỹ thuật
   - Lựa chọn chiến lược tự động dựa trên loại truy vấn

2. **Hỗ trợ tiếng Việt tối ưu**
   - Tích hợp với các mô hình embedding tiếng Việt chuyên biệt (PhoBERT, VieBERT, XLM-RoBERTa-Vi, v.v.)
   - <PERSON><PERSON><PERSON> thiện prompt cho tiếng Việt
   - Phân loại truy vấn tiếng Việt đặc biệt

3. **Phân tích hiệu suất và đề xuất cải tiến**
   - Phân tích đóng góp của từng kỹ thuật
   - Đề xuất trọng số tối ưu
   - Lưu kết quả phân tích để cải thiện trong tương lai

4. **Học từ phản hồi**
   - Cập nhật trọng số dựa trên phản hồi
   - Ghi nhớ hiệu suất cho các loại truy vấn khác nhau
   - Cải thiện liên tục theo thời gian

## Cài đặt

```python
from src.deep_research_core.reasoning.enhanced_rag_tot_cot import EnhancedRAGTOTCOTReasoner
from src.deep_research_core.rag.sqlite_vector import SQLiteVectorRAG

# Khởi tạo SQLiteVectorRAG
rag = SQLiteVectorRAG(
    db_path="example_rag.db",
    embedding_model="all-MiniLM-L6-v2",
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free"
)

# Thêm tài liệu
rag.add_documents([
    {
        "content": "Nội dung tài liệu 1",
        "metadata": {"source": "Nguồn 1", "topic": "Chủ đề 1"}
    },
    {
        "content": "Nội dung tài liệu 2",
        "metadata": {"source": "Nguồn 2", "topic": "Chủ đề 2"}
    }
])

# Khởi tạo EnhancedRAGTOTCOTReasoner
reasoner = EnhancedRAGTOTCOTReasoner(
    rag_system=rag,
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free",
    language="vi",
    max_branches=3,
    max_depth=2,
    use_weight_optimizer=True,
    use_query_classifier=True,
    use_performance_analyzer=True,
    use_vietnamese_optimization=True,
    vietnamese_embedding_model="phobert"
)
```

## Sử dụng cơ bản

### Xử lý truy vấn

```python
# Xử lý truy vấn với tự động xác định chiến lược
result = reasoner.reason("Trí tuệ nhân tạo là gì?")

# Xử lý truy vấn với chiến lược tùy chỉnh
result = reasoner.reason(
    query="Trí tuệ nhân tạo là gì?",
    use_rag=True,
    use_tot=False,
    use_cot=True,
    weights={"rag": 0.7, "tot": 0.0, "cot": 0.3}
)

# In kết quả
print(f"Câu trả lời: {result['answer']}")
print(f"Loại truy vấn: {result['query_type']}")
print(f"Chiến lược: {result['strategy']}")
print(f"Trọng số: {result['weights_used']}")
```

### So sánh các phương pháp

```python
# So sánh các phương pháp khác nhau
comparison = reasoner.compare_methods_with_analysis("Học máy là gì?")

# In kết quả so sánh
print(f"Phương pháp tốt nhất: {comparison['best_method']}")
for method, result in comparison["methods"].items():
    print(f"\n{method.upper()}:")
    print(f"- Độ dài câu trả lời: {result['answer_length']} ký tự")
    print(f"- Thời gian xử lý: {result['latency']:.2f} giây")
```

### Cung cấp phản hồi

```python
# Cung cấp phản hồi để cải thiện trọng số
feedback = {"rag": 0.8, "tot": 0.5, "cot": 0.6}
updated_weights = reasoner.provide_feedback("Học máy là gì?", feedback)

# In trọng số đã cập nhật
print(f"Trọng số đã cập nhật: {updated_weights}")
```

## Tham số

### Khởi tạo

| Tham số | Mô tả | Mặc định |
|---------|-------|----------|
| `rag_system` | Hệ thống RAG để truy xuất thông tin | (bắt buộc) |
| `provider` | Nhà cung cấp API ("openai", "anthropic", "openrouter", v.v.) | "openai" |
| `model` | Mô hình để sử dụng | None |
| `temperature` | Nhiệt độ lấy mẫu | 0.7 |
| `max_tokens` | Số token tối đa để tạo | 2000 |
| `language` | Ngôn ngữ để sử dụng (vi hoặc en) | "vi" |
| `max_branches` | Số nhánh tối đa trong TOT | 3 |
| `max_depth` | Độ sâu tối đa trong TOT | 3 |
| `adaptive` | Có điều chỉnh tham số dựa trên độ phức tạp hay không | True |
| `use_advanced_optimization` | Có sử dụng tối ưu hóa nâng cao hay không | True |
| `token_budget` | Ngân sách token tối đa | 100000 |
| `parallel_exploration` | Có khám phá song song hay không | False |
| `max_workers` | Số lượng worker tối đa cho khám phá song song | 3 |
| `early_pruning` | Có cắt tỉa sớm hay không | True |
| `verbose` | Có in thông tin chi tiết hay không | False |
| `use_weight_optimizer` | Có sử dụng bộ tối ưu hóa trọng số hay không | True |
| `use_query_classifier` | Có sử dụng bộ phân loại truy vấn hay không | True |
| `use_performance_analyzer` | Có sử dụng công cụ phân tích hiệu suất hay không | True |
| `use_vietnamese_optimization` | Có sử dụng tối ưu hóa tiếng Việt hay không | True |
| `vietnamese_embedding_model` | Mô hình embedding tiếng Việt để sử dụng | "phobert" |
| `min_weight` | Trọng số tối thiểu cho mỗi kỹ thuật | 0.1 |
| `max_weight` | Trọng số tối đa cho mỗi kỹ thuật | 0.7 |
| `learning_rate` | Tốc độ học cho bộ tối ưu hóa trọng số | 0.05 |
| `use_historical_data` | Có sử dụng dữ liệu lịch sử hay không | True |
| `max_history_size` | Kích thước tối đa của lịch sử | 100 |
| `save_analysis_results` | Có lưu kết quả phân tích hay không | True |
| `analysis_results_path` | Đường dẫn để lưu kết quả phân tích | "ragtotcot_analysis_results.json" |

### Phương thức reason()

| Tham số | Mô tả | Mặc định |
|---------|-------|----------|
| `query` | Truy vấn để suy luận | (bắt buộc) |
| `use_rag` | Có sử dụng RAG hay không (None để tự động xác định) | None |
| `use_tot` | Có sử dụng TOT hay không (None để tự động xác định) | None |
| `use_cot` | Có sử dụng COT hay không (None để tự động xác định) | None |
| `num_rag_results` | Số lượng kết quả RAG để truy xuất | 5 |
| `callback` | Hàm callback tùy chọn cho streaming | None |
| `weights` | Trọng số tùy chỉnh cho các kỹ thuật (None để sử dụng tự động) | None |

## Kết quả trả về

Phương thức `reason()` trả về một từ điển (dict) chứa các thông tin sau:

```python
{
    "query": "Truy vấn gốc",
    "answer": "Câu trả lời cuối cùng",
    "reasoning": "Quá trình suy luận",
    "enhanced_reasoning": "Quá trình suy luận đã được nâng cao",
    "enhanced_answer": "Câu trả lời đã được nâng cao",
    "model": "Mô hình đã sử dụng",
    "provider": "Nhà cung cấp API",
    "total_latency": 1.234,  # Thời gian xử lý tổng cộng
    "rag_context_used": True,  # Có sử dụng RAG hay không
    "rag_context_length": 1000,  # Độ dài ngữ cảnh RAG
    "methods_used": "RAG-TOT-COT",  # Các phương pháp đã sử dụng
    "strategy": {  # Chiến lược đã sử dụng
        "use_rag": True,
        "use_tot": True,
        "use_cot": True,
        "weights": {"rag": 0.4, "tot": 0.3, "cot": 0.3},
        "query_type": "factual"
    },
    "performance_analysis": {  # Phân tích hiệu suất
        "query": "Truy vấn gốc",
        "query_type": "factual",
        "weights_used": {"rag": 0.4, "tot": 0.3, "cot": 0.3},
        "technique_contributions": {"rag": 0.5, "tot": 0.3, "cot": 0.2},
        "recommended_weights": {"rag": 0.45, "tot": 0.35, "cot": 0.2},
        "visualization": "visualization_data"
    },
    "weights_used": {"rag": 0.4, "tot": 0.3, "cot": 0.3},
    "query_type": "factual",
    "enhanced_latency": 1.5  # Thời gian xử lý bao gồm phân tích
}
```

## Các loại truy vấn

EnhancedRAGTOTCOTReasoner phân loại truy vấn thành các loại sau:

1. **factual**: Truy vấn thực tế, cần thông tin chính xác
   - Ví dụ: "Thủ đô của Việt Nam là gì?"
   - Chiến lược: RAG (70%), COT (30%)

2. **analytical**: Truy vấn phân tích, cần suy luận sâu
   - Ví dụ: "So sánh ưu nhược điểm của các nguồn năng lượng tái tạo"
   - Chiến lược: RAG (30%), TOT (40%), COT (30%)

3. **creative**: Truy vấn sáng tạo, cần tư duy đa chiều
   - Ví dụ: "Hãy viết một câu chuyện ngắn về tương lai của Việt Nam năm 2050"
   - Chiến lược: TOT (60%), COT (40%)

4. **procedural**: Truy vấn quy trình, cần các bước tuần tự
   - Ví dụ: "Cách nấu phở bò truyền thống"
   - Chiến lược: RAG (40%), COT (60%)

5. **opinion**: Truy vấn ý kiến, cần đánh giá chủ quan
   - Ví dụ: "Bạn nghĩ gì về việc sử dụng năng lượng hạt nhân ở Việt Nam?"
   - Chiến lược: RAG (20%), TOT (30%), COT (50%)

6. **complex**: Truy vấn phức tạp, cần kết hợp nhiều kỹ thuật
   - Ví dụ: "Phân tích tác động của biến đổi khí hậu đến nông nghiệp Việt Nam và đề xuất giải pháp thích ứng"
   - Chiến lược: RAG (33%), TOT (33%), COT (34%)

7. **vietnamese**: Truy vấn tiếng Việt đặc biệt
   - Ví dụ: "Phân tích ý nghĩa của câu tục ngữ 'Một cây làm chẳng nên non, ba cây chụm lại nên hòn núi cao'"
   - Chiến lược: RAG (40%), TOT (30%), COT (30%)

## Hỗ trợ tiếng Việt

EnhancedRAGTOTCOTReasoner hỗ trợ tiếng Việt thông qua:

1. **Mô hình embedding tiếng Việt**:
   - PhoBERT (mặc định): `vietnamese_embedding_model="phobert"`
   - VieBERT: `vietnamese_embedding_model="viebert"`
   - XLM-RoBERTa-Vi: `vietnamese_embedding_model="xlm-roberta-vi"`
   - Multilingual-E5: `vietnamese_embedding_model="multilingual-e5"`
   - Vietnamese-SBERT: `vietnamese_embedding_model="vietnamese-sbert"`

2. **Prompt tiếng Việt tối ưu**:
   - Prompt cho TOT được điều chỉnh cho tiếng Việt
   - Prompt cho COT được điều chỉnh cho tiếng Việt

3. **Phân loại truy vấn tiếng Việt**:
   - Phát hiện tự động truy vấn tiếng Việt
   - Phân loại truy vấn tiếng Việt đặc biệt

## Ví dụ mã nguồn đầy đủ

```python
import os
from src.deep_research_core.reasoning.enhanced_rag_tot_cot import EnhancedRAGTOTCOTReasoner
from src.deep_research_core.rag.sqlite_vector import SQLiteVectorRAG

# Set API key
os.environ["OPENROUTER_API_KEY"] = "your-api-key"

# Initialize SQLiteVectorRAG
rag = SQLiteVectorRAG(
    db_path="example_rag.db",
    embedding_model="all-MiniLM-L6-v2",
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free"
)

# Add documents
documents = [
    {
        "content": "Trí tuệ nhân tạo (AI) là khả năng của một hệ thống máy tính để thực hiện các nhiệm vụ thường đòi hỏi trí thông minh của con người.",
        "metadata": {"source": "AI Overview", "topic": "Artificial Intelligence"}
    },
    {
        "content": "Học máy (Machine Learning) là một nhánh của trí tuệ nhân tạo tập trung vào việc phát triển các thuật toán cho phép máy tính học từ dữ liệu.",
        "metadata": {"source": "Machine Learning Basics", "topic": "Machine Learning"}
    }
]
rag.add_documents(documents)

# Initialize EnhancedRAGTOTCOTReasoner
reasoner = EnhancedRAGTOTCOTReasoner(
    rag_system=rag,
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free",
    language="vi",
    max_branches=2,
    max_depth=2,
    use_weight_optimizer=True,
    use_query_classifier=True,
    use_performance_analyzer=True,
    use_vietnamese_optimization=True,
    vietnamese_embedding_model="phobert"
)

# Process a query
result = reasoner.reason("Trí tuệ nhân tạo là gì?")

# Print the result
print(f"Câu trả lời: {result['answer']}")
print(f"Loại truy vấn: {result['query_type']}")
print(f"Chiến lược: {result['strategy']}")
print(f"Trọng số: {result['weights_used']}")

# Compare methods
comparison = reasoner.compare_methods_with_analysis("Học máy là gì?")
print(f"Phương pháp tốt nhất: {comparison['best_method']}")

# Provide feedback
feedback = {"rag": 0.8, "tot": 0.5, "cot": 0.6}
updated_weights = reasoner.provide_feedback("Học máy là gì?", feedback)
print(f"Trọng số đã cập nhật: {updated_weights}")

# Clean up
rag.close()
```
