# Hướng dẫn kiểm thử hồi quy cho Deep Research Core

Tài liệu này cung cấp hướng dẫn chi tiết về cách thực hiện kiểm thử hồi quy tự động cho dự án Deep Research Core.

## Tổng quan

Kiểm thử hồi quy là quá trình kiểm tra xem các tính năng đã tồn tại có bị ảnh hưởng bởi các thay đổi mới hay không. Điều này đặc biệt quan trọng trong dự án phức tạp như Deep Research Core, nơi các thay đổi ở một phần của hệ thống có thể ảnh hưởng đến các phần khác.

Hệ thống kiểm thử hồi quy của Deep Research Core bao gồm:

1. **Kiểm thử hồi quy cơ bản**: Kiểm tra các tính năng cơ bản của hệ thống
2. **<PERSON>ểm thử hồi quy tiếng Việt**: <PERSON>ểm tra các tính năng tiếng Việt
3. **<PERSON>ể<PERSON> thử hồi quy nâng cao**: Kiểm tra các tính năng nâng cao
4. **So sánh kết quả**: So sánh kết quả với các lần chạy trước đó
5. **Báo cáo hồi quy**: Tạo báo cáo chi tiết về kết quả kiểm thử hồi quy

## Chạy kiểm thử hồi quy

### Sử dụng script kiểm thử hồi quy tự động

Dự án cung cấp script `scripts/run_regression_tests.sh` để chạy kiểm thử hồi quy một cách dễ dàng:

```bash
# Chạy kiểm thử hồi quy cơ bản
./scripts/run_regression_tests.sh

# Chạy kiểm thử hồi quy với báo cáo chi tiết
./scripts/run_regression_tests.sh --verbose --report

# Chạy kiểm thử hồi quy và so sánh với kết quả trước đó
./scripts/run_regression_tests.sh --compare

# Chạy kiểm thử hồi quy, lưu kết quả và gửi thông báo khi thất bại
./scripts/run_regression_tests.sh --save --notify

# Chạy kiểm thử hồi quy song song
./scripts/run_regression_tests.sh --parallel
```

### Sử dụng pytest trực tiếp

```bash
# Chạy tất cả các kiểm thử hồi quy
pytest tests/regression/

# Chạy kiểm thử hồi quy cơ bản
pytest tests/regression/test_core_functionality.py

# Chạy kiểm thử hồi quy tiếng Việt
pytest tests/regression/test_vietnamese_functionality.py

# Chạy kiểm thử hồi quy nâng cao
pytest tests/regression/test_advanced_functionality.py

# Chạy kiểm thử hồi quy với báo cáo chi tiết
pytest tests/regression/ -v --regression-report

# Chạy kiểm thử hồi quy song song
pytest tests/regression/ -n auto
```

## Cấu trúc kiểm thử hồi quy

```
tests/regression/
├── __init__.py                       # Package initialization
├── conftest.py                       # Configuration for regression tests
├── test_core_functionality.py        # Core functionality regression tests
├── test_vietnamese_functionality.py  # Vietnamese functionality regression tests
└── test_advanced_functionality.py    # Advanced functionality regression tests
```

## Tích hợp với CI/CD

Kiểm thử hồi quy được tích hợp vào CI/CD pipeline của dự án. Mỗi khi có push hoặc pull request vào nhánh main hoặc develop, GitHub Actions sẽ tự động chạy các kiểm thử hồi quy.

Để xem kết quả kiểm thử hồi quy, truy cập tab "Actions" trên GitHub repository và tải xuống artifact "regression-test-results".

## Viết kiểm thử hồi quy mới

### Kiểm thử hồi quy cơ bản

```python
import unittest
from src.deep_research_core.reasoning.cot import ChainOfThought

class TestCoreRegressionSuite(unittest.TestCase):
    """Regression test suite for core functionality."""
    
    def setUp(self):
        """Set up the test environment."""
        # Create a mock provider
        self.mock_provider = MockProvider()
        
        # Create an instance of ChainOfThought
        self.cot = ChainOfThought(
            provider=self.mock_provider,
            model="test-model",
            temperature=0.7,
            max_tokens=1000
        )
    
    def test_cot_regression(self):
        """Test Chain of Thought regression."""
        # Test with a simple query
        result = self.cot.reason(query="Test query")
        
        # Verify the result structure
        self.assertIsInstance(result, dict)
        self.assertIn("answer", result)
        self.assertIn("reasoning", result)
        self.assertIn("model", result)
        self.assertIn("provider", result)
        self.assertIn("latency", result)
```

### Sử dụng fixtures

```python
import unittest
import pytest

class TestRegressionWithFixtures(unittest.TestCase):
    """Regression test suite using fixtures."""
    
    def test_regression_with_fixtures(self, regression_data, save_regression_results, load_regression_results, compare_regression_results):
        """Test regression with fixtures."""
        # Get test data
        simple_query = regression_data["simple_query"]
        
        # Run the test
        result = self.cot.reason(query=simple_query)
        
        # Save the results
        save_regression_results("cot_simple_query", result)
        
        # Load previous results
        previous_results = load_regression_results("cot_simple_query")
        
        # Compare results
        if previous_results:
            comparison = compare_regression_results(previous_results, result)
            print(f"Comparison summary: {comparison['summary']}")
```

## Phân tích kết quả kiểm thử hồi quy

Kết quả kiểm thử hồi quy được lưu trong thư mục `test_results/regression/`. Mỗi lần chạy kiểm thử hồi quy sẽ tạo ra một file JSON chứa kết quả chi tiết.

Để phân tích kết quả kiểm thử hồi quy, bạn có thể sử dụng script `scripts/analyze_regression_results.py`:

```bash
python scripts/analyze_regression_results.py
```

Script này sẽ phân tích kết quả kiểm thử hồi quy và tạo ra một báo cáo chi tiết về các thay đổi giữa các lần chạy.

## Các mẹo và thủ thuật

1. **Sử dụng mock**: Sử dụng mock để tránh gọi API thật trong kiểm thử hồi quy
2. **Lưu kết quả**: Luôn lưu kết quả kiểm thử hồi quy để so sánh với các lần chạy sau
3. **So sánh kết quả**: So sánh kết quả với các lần chạy trước đó để phát hiện các thay đổi
4. **Tự động hóa**: Tự động hóa quá trình kiểm thử hồi quy bằng cách tích hợp vào CI/CD pipeline
5. **Thông báo**: Thiết lập thông báo khi kiểm thử hồi quy thất bại

## Tài liệu tham khảo

- [Pytest Documentation](https://docs.pytest.org/)
- [Regression Testing Best Practices](https://www.softwaretestinghelp.com/regression-testing-tools-and-methods/)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)
