# Hướng dẫn kiểm thử cho Deep Research Core

Tài liệu này cung cấp hướng dẫn chi tiết về cách thực hiện kiểm thử tự động cho dự án Deep Research Core.

## Tổng quan

Hệ thống kiểm thử của Deep Research Core bao gồm:

1. **Kiểm thử đơn vị (Unit Tests)**: <PERSON><PERSON><PERSON> tra các thành phần riêng lẻ
2. **Kiể<PERSON> thử tích hợp (Integration Tests)**: Kiểm tra tương tác giữa các thành phần
3. **Kiểm thử hiệu suất (Performance Tests)**: <PERSON><PERSON><PERSON> giá hiệu suất của hệ thống
4. **Kiểm thử benchmark**: So sánh hiệu suất giữa các phiên bản
5. **B<PERSON>o cáo độ phủ kiểm thử**: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> mức độ bao phủ của các kiểm thử

## Cài đặt môi trường kiểm thử

```bash
# Cài đặt các gói phụ thuộc cho kiểm thử
pip install pytest pytest-cov pytest-xdist pytest-benchmark flake8 mypy pylint

# Cài đặt dự án ở chế độ phát triển
pip install -e ".[dev,test]"
```

## Chạy kiểm thử

### Sử dụng script kiểm thử tự động

Dự án cung cấp script `tests/run_all_tests.py` để chạy kiểm thử một cách dễ dàng:

```bash
# Chạy tất cả các kiểm thử
python tests/run_all_tests.py

# Chạy chỉ kiểm thử đơn vị
python tests/run_all_tests.py --unit

# Chạy chỉ kiểm thử tích hợp
python tests/run_all_tests.py --integration

# Chạy kiểm thử với báo cáo độ phủ
python tests/run_all_tests.py --coverage --html

# Chạy kiểm thử hiệu suất
python tests/run_all_tests.py --performance

# Chạy kiểm thử benchmark
python tests/run_all_tests.py --benchmark

# Chạy kiểm thử song song
python tests/run_all_tests.py --parallel
```

### Sử dụng script bash

Dự án cũng cung cấp script bash `scripts/run_tests.sh` để chạy kiểm thử:

```bash
# Chạy tất cả các kiểm thử
./scripts/run_tests.sh

# Chạy chỉ kiểm thử đơn vị
./scripts/run_tests.sh --unit

# Chạy chỉ kiểm thử tích hợp
./scripts/run_tests.sh --integration

# Chạy kiểm thử với báo cáo độ phủ
./scripts/run_tests.sh --coverage --html

# Chạy kiểm thử hiệu suất
./scripts/run_tests.sh --performance

# Chạy kiểm thử benchmark
./scripts/run_tests.sh --benchmark

# Chạy kiểm thử song song
./scripts/run_tests.sh --parallel
```

### Sử dụng pytest trực tiếp

```bash
# Chạy tất cả các kiểm thử
pytest

# Chạy chỉ kiểm thử đơn vị
pytest tests/unit/

# Chạy chỉ kiểm thử tích hợp
pytest tests/integration/

# Chạy kiểm thử với báo cáo độ phủ
pytest --cov=src/deep_research_core --cov-report=html tests/

# Chạy kiểm thử hiệu suất
pytest tests/ -k "performance"

# Chạy kiểm thử benchmark
pytest --benchmark-only --benchmark-json=./benchmark.json

# Chạy kiểm thử song song
pytest -n auto
```

## CI/CD Pipeline

Dự án sử dụng GitHub Actions để tự động hóa quy trình CI/CD. Pipeline được cấu hình trong file `.github/workflows/ci.yml` và bao gồm các bước sau:

1. **Kiểm tra cú pháp**: Sử dụng flake8 để kiểm tra cú pháp Python
2. **Kiểm tra kiểu dữ liệu**: Sử dụng mypy để kiểm tra kiểu dữ liệu
3. **Chạy kiểm thử đơn vị**: Chạy các kiểm thử đơn vị
4. **Chạy kiểm thử tích hợp**: Chạy các kiểm thử tích hợp
5. **Chạy kiểm thử toàn diện**: Chạy tất cả các kiểm thử
6. **Tạo báo cáo độ phủ**: Tạo báo cáo độ phủ kiểm thử
7. **Chạy kiểm thử hiệu suất**: Chạy các kiểm thử hiệu suất
8. **Đóng gói và phát hành**: Đóng gói và phát hành dự án (chỉ khi push vào nhánh main)

## Cấu trúc thư mục kiểm thử

```
tests/
├── unit/                  # Kiểm thử đơn vị
│   ├── reasoning/         # Kiểm thử các kỹ thuật suy luận
│   │   ├── cot/           # Kiểm thử Chain of Thought
│   │   ├── tot/           # Kiểm thử Tree of Thought
│   │   ├── react/         # Kiểm thử ReAct
│   │   └── combined/      # Kiểm thử kết hợp các kỹ thuật
│   ├── retrieval/         # Kiểm thử các kỹ thuật truy xuất
│   ├── multilingual/      # Kiểm thử hỗ trợ đa ngôn ngữ
│   └── utils/             # Kiểm thử các tiện ích
├── integration/           # Kiểm thử tích hợp
│   └── reasoning/         # Kiểm thử tích hợp các kỹ thuật suy luận
├── performance/           # Kiểm thử hiệu suất
├── evaluation/            # Kiểm thử đánh giá
└── run_all_tests.py       # Script chạy tất cả các kiểm thử
```

## Viết kiểm thử mới

### Kiểm thử đơn vị

```python
import unittest
from src.deep_research_core.reasoning.cot import ChainOfThought

class TestChainOfThought(unittest.TestCase):
    def setUp(self):
        # Thiết lập môi trường kiểm thử
        self.cot = ChainOfThought(
            provider="mock",
            model="test-model",
            temperature=0.7,
            max_tokens=1000
        )
    
    def test_reason(self):
        # Kiểm tra phương thức reason
        result = self.cot.reason(query="Test query")
        self.assertIsNotNone(result)
        self.assertIn("answer", result)
```

### Kiểm thử tích hợp

```python
import unittest
from src.deep_research_core.reasoning.cot import ChainOfThought
from src.deep_research_core.reasoning.rag import SQLiteVectorRAG
from src.deep_research_core.reasoning.cotrag import CoTRAG

class TestCoTRAGIntegration(unittest.TestCase):
    def setUp(self):
        # Thiết lập môi trường kiểm thử
        self.rag = SQLiteVectorRAG(
            db_path=":memory:",
            embedding_model="test-model",
            provider="mock",
            model="test-model"
        )
        
        self.cot = ChainOfThought(
            provider="mock",
            model="test-model"
        )
        
        self.cotrag = CoTRAG(
            rag=self.rag,
            provider="mock",
            model="test-model"
        )
    
    def test_cotrag_integration(self):
        # Kiểm tra tích hợp CoTRAG
        self.rag.add_texts(["Test document"])
        result = self.cotrag.reason(query="Test query")
        self.assertIsNotNone(result)
        self.assertIn("answer", result)
```

### Kiểm thử hiệu suất

```python
import pytest
import time
from src.deep_research_core.reasoning.cot import ChainOfThought

@pytest.mark.performance
def test_cot_performance(benchmark):
    # Thiết lập môi trường kiểm thử
    cot = ChainOfThought(
        provider="mock",
        model="test-model"
    )
    
    # Đo hiệu suất của phương thức reason
    benchmark(cot.reason, query="Test query")
```

## Báo cáo độ phủ kiểm thử

Báo cáo độ phủ kiểm thử được tạo bằng cách sử dụng pytest-cov:

```bash
pytest --cov=src/deep_research_core --cov-report=html tests/
```

Báo cáo HTML sẽ được tạo trong thư mục `htmlcov/`. Mở file `htmlcov/index.html` để xem báo cáo.

## Kiểm thử tự động với GitHub Actions

Dự án sử dụng GitHub Actions để tự động hóa quy trình kiểm thử. Mỗi khi có push hoặc pull request vào nhánh main hoặc develop, GitHub Actions sẽ tự động chạy các kiểm thử.

Để xem kết quả kiểm thử, truy cập tab "Actions" trên GitHub repository.

## Các mẹo và thủ thuật

1. **Sử dụng mock**: Sử dụng mock để tránh gọi API thật trong kiểm thử
2. **Sử dụng fixture**: Sử dụng fixture để tái sử dụng mã thiết lập môi trường kiểm thử
3. **Sử dụng parametrize**: Sử dụng parametrize để chạy kiểm thử với nhiều bộ dữ liệu khác nhau
4. **Sử dụng marker**: Sử dụng marker để phân loại kiểm thử (ví dụ: unit, integration, performance)
5. **Sử dụng skip**: Sử dụng skip để bỏ qua kiểm thử trong một số trường hợp (ví dụ: kiểm thử hiệu suất trong CI)

## Tài liệu tham khảo

- [Pytest Documentation](https://docs.pytest.org/)
- [Pytest-cov Documentation](https://pytest-cov.readthedocs.io/)
- [Pytest-xdist Documentation](https://pytest-xdist.readthedocs.io/)
- [Pytest-benchmark Documentation](https://pytest-benchmark.readthedocs.io/)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)
