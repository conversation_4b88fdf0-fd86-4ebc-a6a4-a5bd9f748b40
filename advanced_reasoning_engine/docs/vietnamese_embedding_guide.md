# Hướng dẫn sử dụng mô hình Embedding tiếng Việt

Tài liệu này cung cấp hướng dẫn chi tiết về cách sử dụng các mô hình embedding tiếng Việt trong Deep Research Core.

## Giới thiệu

Deep Research Core hỗ trợ nhiều mô hình embedding tiếng Việt chuyên biệt, gi<PERSON><PERSON> nâng cao hiệu suất xử lý ngôn ngữ tự nhiên cho tiếng Việt. Các mô hình này được tích hợp vào hệ thống CoTRAG (Chain of Thought + Retrieval-Augmented Generation) để cải thiện khả năng truy xuất thông tin và suy luận cho các truy vấn tiếng Việt.

## Các mô hình Embedding tiếng Việt được hỗ trợ

Deep Research Core hỗ trợ các mô hình embedding tiếng Việt sau:

### Mô hình cơ bản
- **PhoBERT**: <PERSON>ô hình BERT được huấn luyện trên dữ liệu tiếng Việt lớn, cung cấp biểu diễn ngữ nghĩa phong phú cho văn bản tiếng Việt.
  - `phobert`: PhoBERT-base
  - `phobert-large`: PhoBERT-large

- **VieBERT**: Mô hình BERT được huấn luyện đặc biệt cho tiếng Việt, cung cấp biểu diễn ngữ nghĩa phong phú cho văn bản tiếng Việt.
  - `viebert`: VieBERT-base-cased

- **XLM-RoBERTa-Vi**: Mô hình đa ngôn ngữ có hỗ trợ tốt cho tiếng Việt, cho phép biểu diễn văn bản tiếng Việt trong không gian ngữ nghĩa đa ngôn ngữ.
  - `xlm-roberta-vi`: XLM-RoBERTa-base

- **Multilingual-E5**: Mô hình embedding đa ngôn ngữ được tối ưu hóa cho tìm kiếm ngữ nghĩa, hỗ trợ hơn 100 ngôn ngữ bao gồm tiếng Việt.
  - `multilingual-e5`: Multilingual-E5-base
  - `multilingual-e5-large`: Multilingual-E5-large

- **Vietnamese-SBERT**: Phiên bản của Sentence-BERT được tinh chỉnh cho tiếng Việt, cung cấp embedding câu chất lượng cao cho tìm kiếm ngữ nghĩa.
  - `vietnamese-sbert`: Vietnamese-SBERT

### Mô hình mới
- **BKAI-Foundation-Vi**: Mô hình bi-encoder tiếng Việt được phát triển bởi BKAI, tối ưu hóa cho nhiệm vụ tìm kiếm ngữ nghĩa.
  - `bkai-foundation-vi`: BKAI-Foundation-Vi

- **EnViBERT**: Mô hình BERT song ngữ Anh-Việt, hữu ích cho các ứng dụng đa ngôn ngữ.
  - `envibert`: EnViBERT

- **BARTpho**: Mô hình sequence-to-sequence tiếng Việt dựa trên kiến trúc BART.
  - `bartpho`: BARTpho-syllable

- **VElectra**: Mô hình ELECTRA được huấn luyện trên dữ liệu tiếng Việt.
  - `velectra`: VElectra-base-discriminator-cased

- **ViBERT4News**: Mô hình BERT được huấn luyện trên dữ liệu tin tức tiếng Việt.
  - `vibert4news`: ViBERT4News-base-cased

- **Vi-MRC**: Mô hình được tối ưu hóa cho nhiệm vụ Machine Reading Comprehension (MRC) tiếng Việt.
  - `vi-mrc`: Vi-MRC-base

- **Vi-QA**: Mô hình được tối ưu hóa cho nhiệm vụ Question Answering (QA) tiếng Việt.
  - `vi-qa`: Vi-QA-base

### Mô hình mới bổ sung
- **ViT5**: Mô hình T5 được huấn luyện cho tiếng Việt, phù hợp cho các nhiệm vụ sequence-to-sequence.
  - `vit5`: ViT5-base

- **VinALLM**: Mô hình ngôn ngữ lớn được phát triển bởi VinAI cho tiếng Việt.
  - `vinallm`: VinALLM-7B-Chat

- **Vietnamese-LLaMA**: Phiên bản LLaMA được tinh chỉnh cho tiếng Việt.
  - `vietnamese-llama`: Vietnamese-LLaMA2

- **Vietnamese-GPT**: Mô hình GPT-Neo được huấn luyện cho tiếng Việt.
  - `vietnamese-gpt`: GPT-Neo-Vi-Small

- **Vietnamese-RoBERTa**: Phiên bản RoBERTa được tinh chỉnh cho phân tích cảm xúc tiếng Việt.
  - `vietnamese-roberta`: PhoBERT-base-Vietnamese-Sentiment

## Cài đặt

Để sử dụng các mô hình embedding tiếng Việt, bạn cần cài đặt các thư viện cần thiết:

```bash
pip install -r requirements.txt
```

## Sử dụng cơ bản

### Sử dụng VietnameseEmbeddings

```python
from src.deep_research_core.multilingual.vietnamese_embeddings import VietnameseEmbeddings, VietnameseEmbeddingFactory

# Khởi tạo mô hình embedding
embedding_model = VietnameseEmbeddingFactory.get_embedding_model(
    model_name="phobert",  # Lựa chọn: "phobert", "viebert", "xlm-roberta-vi", ...
    device="cuda",         # Lựa chọn: "cpu", "cuda", "mps"
    cache_dir=None         # Thư mục cache (tùy chọn)
)

# Tạo embedding cho văn bản tiếng Việt
text = "Xin chào, đây là một ví dụ về embedding tiếng Việt."
embedding = embedding_model.get_embedding(text)

# Tạo embedding cho nhiều văn bản
texts = [
    "Xin chào, đây là ví dụ thứ nhất.",
    "Đây là ví dụ thứ hai."
]
embeddings = embedding_model.get_embeddings(texts)

# Tính độ tương đồng giữa hai văn bản
text1 = "Hà Nội là thủ đô của Việt Nam."
text2 = "Thủ đô của Việt Nam là Hà Nội."
similarity = embedding_model.calculate_similarity(text1, text2, method="cosine")
print(f"Độ tương đồng: {similarity}")

# Tìm văn bản tương đồng nhất
query = "Thủ đô Việt Nam"
candidates = [
    "Hà Nội là thủ đô của Việt Nam.",
    "Thành phố Hồ Chí Minh là thành phố lớn nhất Việt Nam.",
    "Việt Nam có 63 tỉnh thành."
]
results = embedding_model.find_most_similar(query, candidates, top_k=2)
for idx, score in results:
    print(f"- {candidates[idx]} (Điểm: {score:.4f})")
```

### Tích hợp với CoTRAG

```python
from src.deep_research_core.reasoning.cot_rag import CoTRAG
from src.deep_research_core.reasoning.cotrag_vietnamese_embedding_adapter import CoTRAGVietnameseEmbeddingAdapter

# Khởi tạo CoTRAG
cotrag = CoTRAG(
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free",
    temperature=0.7,
    max_tokens=2000
)

# Tạo adapter cho mô hình embedding tiếng Việt
vietnamese_adapter = CoTRAGVietnameseEmbeddingAdapter(
    cotrag_instance=cotrag,
    embedding_model="phobert",
    device="cuda",
    use_hybrid_search=True,
    use_vietnamese_preprocessing=True
)

# Xử lý truy vấn tiếng Việt
query = "Việt Nam có bao nhiêu tỉnh thành?"
result = vietnamese_adapter.process(query)

# Hiển thị kết quả
print(f"Câu hỏi: {result['query']}")
print(f"Câu trả lời: {result['answer']}")
print(f"Mô hình embedding: {result['vietnamese']['embedding_model']}")
print(f"Thời gian xử lý: {result['vietnamese']['processing_time']:.4f}s")
```

## Đánh giá hiệu suất

### Sử dụng VietnameseBenchmark

```python
from src.deep_research_core.evaluation.vietnamese_benchmark import VietnameseBenchmark

# Khởi tạo benchmark
benchmark = VietnameseBenchmark()

# Đánh giá một mô hình embedding
results = benchmark.evaluate_embedding_model(
    model_name="phobert",
    domain="general",
    task="retrieval",
    device="cuda",
    top_k=5
)

print(f"Precision: {results['precision']:.4f}")
print(f"Recall: {results['recall']:.4f}")
print(f"F1 Score: {results['f1_score']:.4f}")
print(f"NDCG: {results['ndcg']:.4f}")
print(f"Latency: {results['latency']:.4f}s")

# So sánh nhiều mô hình embedding
model_names = ["phobert", "viebert", "xlm-roberta-vi", "multilingual-e5"]
comparison_results = benchmark.compare_embedding_models(
    model_names=model_names,
    domain="general",
    task="retrieval",
    device="cuda",
    top_k=5
)

# Hiển thị kết quả so sánh
for model_name, metrics in comparison_results.items():
    print(f"\n{model_name}:")
    print(f"  Precision: {metrics['precision']:.4f}")
    print(f"  Recall: {metrics['recall']:.4f}")
    print(f"  F1 Score: {metrics['f1_score']:.4f}")
    print(f"  NDCG: {metrics['ndcg']:.4f}")
    print(f"  Latency: {metrics['latency']:.4f}s")
```

### Sử dụng script đánh giá

```bash
python scripts/evaluate_vietnamese_embeddings.py --models phobert viebert multilingual-e5 --domain general --task retrieval --device cuda --top-k 5
```

## Tạo bộ dữ liệu benchmark mới

```python
from src.deep_research_core.evaluation.vietnamese_benchmark import VietnameseBenchmark

# Khởi tạo benchmark
benchmark = VietnameseBenchmark()

# Tạo bộ dữ liệu benchmark mới
samples = [
    {
        "id": "news001",
        "query": "Ai là Tổng thống Hoa Kỳ hiện tại?",
        "documents": [
            "Joe Biden là Tổng thống thứ 46 của Hoa Kỳ, nhậm chức vào ngày 20 tháng 1 năm 2021.",
            "Kamala Harris là Phó Tổng thống Hoa Kỳ, phục vụ cùng với Tổng thống Joe Biden.",
            "Donald Trump là Tổng thống thứ 45 của Hoa Kỳ, phục vụ từ năm 2017 đến năm 2021."
        ],
        "relevant_documents": [0, 1],
        "expected_answer": "Joe Biden là Tổng thống Hoa Kỳ hiện tại."
    },
    {
        "id": "news002",
        "query": "Thủ đô của Pháp là gì?",
        "documents": [
            "Paris là thủ đô của Pháp.",
            "Paris là thành phố lớn nhất của Pháp và là một trong những trung tâm văn hóa, nghệ thuật và thời trang quan trọng nhất thế giới.",
            "London là thủ đô của Vương quốc Anh."
        ],
        "relevant_documents": [0, 1],
        "expected_answer": "Thủ đô của Pháp là Paris."
    }
]

benchmark.create_benchmark_dataset(
    domain="news",
    task="qa",
    samples=samples,
    name="Vietnamese News QA Benchmark",
    description="Bộ dữ liệu benchmark cho nhiệm vụ hỏi đáp tin tức tiếng Việt",
    version="1.0"
)
```

## Các phương pháp tìm kiếm

### Tìm kiếm ngữ nghĩa (Semantic Search)

Tìm kiếm ngữ nghĩa sử dụng embedding để tìm các tài liệu có ý nghĩa tương tự với truy vấn, ngay cả khi chúng không chia sẻ các từ chính xác.

```python
# Tìm kiếm ngữ nghĩa với CoTRAGVietnameseEmbeddingAdapter
adapter = CoTRAGVietnameseEmbeddingAdapter(
    cotrag_instance=cotrag,
    embedding_model="phobert",
    use_hybrid_search=False  # Chỉ sử dụng tìm kiếm ngữ nghĩa
)

result = adapter.process(query)
```

### Tìm kiếm hybrid (Hybrid Search)

Tìm kiếm hybrid kết hợp tìm kiếm ngữ nghĩa và tìm kiếm từ vựng để cải thiện kết quả truy xuất.

```python
# Tìm kiếm hybrid với CoTRAGVietnameseEmbeddingAdapter
adapter = CoTRAGVietnameseEmbeddingAdapter(
    cotrag_instance=cotrag,
    embedding_model="phobert",
    use_hybrid_search=True  # Sử dụng tìm kiếm hybrid
)

result = adapter.process(query)
```

## Lựa chọn mô hình phù hợp

Dưới đây là một số hướng dẫn để lựa chọn mô hình embedding tiếng Việt phù hợp:

### Mô hình cơ bản
- **PhoBERT**: Lựa chọn tốt cho hầu hết các ứng dụng tiếng Việt, đặc biệt là phân tích văn bản tổng quát.
- **VieBERT**: Phù hợp cho các ứng dụng yêu cầu hiểu biết sâu về ngữ pháp tiếng Việt.
- **XLM-RoBERTa-Vi**: Tốt cho các ứng dụng đa ngôn ngữ cần hỗ trợ tiếng Việt.
- **Multilingual-E5**: Phù hợp cho tìm kiếm ngữ nghĩa đa ngôn ngữ.
- **Vietnamese-SBERT**: Tối ưu cho tìm kiếm ngữ nghĩa và so sánh câu tiếng Việt.

### Mô hình chuyên biệt
- **BKAI-Foundation-Vi**: Phù hợp cho các ứng dụng tìm kiếm ngữ nghĩa tiếng Việt hiện đại.
- **EnViBERT**: Tốt cho các ứng dụng song ngữ Anh-Việt.
- **BARTpho**: Phù hợp cho các nhiệm vụ sequence-to-sequence như tóm tắt và dịch thuật.
- **VElectra**: Tốt cho các nhiệm vụ phân loại văn bản tiếng Việt.
- **ViBERT4News**: Tối ưu cho phân tích tin tức tiếng Việt.
- **Vi-MRC**: Chuyên biệt cho nhiệm vụ Machine Reading Comprehension tiếng Việt.
- **Vi-QA**: Chuyên biệt cho nhiệm vụ Question Answering tiếng Việt.

### Mô hình mới bổ sung
- **ViT5**: Phù hợp cho các nhiệm vụ sequence-to-sequence như tóm tắt, dịch thuật và sinh văn bản tiếng Việt.
- **VinALLM**: Mô hình ngôn ngữ lớn cho tiếng Việt, phù hợp cho các ứng dụng đòi hỏi hiểu biết sâu về ngữ cảnh.
- **Vietnamese-LLaMA**: Phù hợp cho các ứng dụng sinh văn bản và đối thoại tiếng Việt.
- **Vietnamese-GPT**: Tốt cho các ứng dụng sinh văn bản tiếng Việt với yêu cầu tài nguyên thấp hơn.
- **Vietnamese-RoBERTa**: Chuyên biệt cho phân tích cảm xúc và phân loại văn bản tiếng Việt.

## Kết luận

Các mô hình embedding tiếng Việt trong Deep Research Core cung cấp nền tảng mạnh mẽ cho việc xử lý ngôn ngữ tự nhiên tiếng Việt. Bằng cách tích hợp các mô hình này với CoTRAG, bạn có thể xây dựng các ứng dụng AI tiên tiến với hiệu suất cao cho tiếng Việt.

Với các mô hình mới bổ sung như ViT5, VinALLM, Vietnamese-LLaMA, Vietnamese-GPT và Vietnamese-RoBERTa, Deep Research Core mở rộng khả năng xử lý tiếng Việt sang nhiều loại nhiệm vụ khác nhau, từ sinh văn bản, dịch thuật, đến phân tích cảm xúc và đối thoại. Hãy chọn mô hình phù hợp với nhu cầu cụ thể của ứng dụng của bạn để đạt hiệu suất tốt nhất.
