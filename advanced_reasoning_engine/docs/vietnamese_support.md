# Hỗ trợ Tiếng Việt

## Hướng dẫn sử dụng Deep Research Core với Tiếng Việt

Tài liệu này cung cấp hướng dẫn toàn diện về cách sử dụng Deep Research Core với tiếng V<PERSON>, bao gồm các mô hình embedding ch<PERSON><PERSON><PERSON> bi<PERSON>, xử lý ngôn ngữ đặc thù, và hỗ trợ cho RL-tuning và thu thập dữ liệu quỹ đạo.

## Mục lục

- [Giới thiệu](#giới-thiệu)
- [Cá<PERSON> mô hình Embedding cho Tiếng Việt](#các-mô-hình-embedding-cho-tiếng-việt)
- [SQLiteVectorRAGVietnamese](#sqlitevectorragvietnamese)
- [Hỗ trợ Tiếng Việt trong RL-Tuning](#hỗ-trợ-tiếng-việt-trong-rl-tuning)
- [Thu thập dữ liệu quỹ đạo với Tiếng Việt](#thu-thập-dữ-liệu-quỹ-đạo-với-tiếng-việt)
- [Ví dụ sử dụng](#ví-dụ-sử-dụng)
- [Đánh giá hiệu suất](#đánh-giá-hiệu-suất)
- [Các vấn đề thường gặp](#các-vấn-đề-thường-gặp)

---

## Giới thiệu

Deep Research Core cung cấp hỗ trợ đặc biệt cho tiếng Việt thông qua các mô hình embedding chuyên biệt và xử lý ngôn ngữ đặc thù. Điều này cho phép hệ thống RAG hoạt động hiệu quả với nội dung tiếng Việt, cung cấp kết quả tìm kiếm chính xác hơn và phản hồi phù hợp hơn với ngữ cảnh văn hóa và ngôn ngữ.

## Các mô hình Embedding cho Tiếng Việt

Deep Research Core hỗ trợ nhiều mô hình embedding chuyên biệt cho tiếng Việt:

### PhoBERT

[PhoBERT](https://github.com/VinAIResearch/PhoBERT) là mô hình ngôn ngữ tiền huấn luyện cho tiếng Việt, được phát triển bởi VinAI Research. Đây là mô hình BERT được huấn luyện trên tập dữ liệu tiếng Việt lớn và cung cấp hiệu suất tốt cho nhiều tác vụ xử lý ngôn ngữ tự nhiên tiếng Việt.

### VieBERT

VieBERT là một mô hình BERT khác được huấn luyện đặc biệt cho tiếng Việt, cung cấp biểu diễn ngữ nghĩa phong phú cho văn bản tiếng Việt.

### XLM-RoBERTa-Vi

XLM-RoBERTa là mô hình đa ngôn ngữ có hỗ trợ tốt cho tiếng Việt, cho phép biểu diễn văn bản tiếng Việt trong không gian ngữ nghĩa đa ngôn ngữ.

### Multilingual-E5

Multilingual-E5 là mô hình embedding đa ngôn ngữ được tối ưu hóa cho tìm kiếm ngữ nghĩa, hỗ trợ hơn 100 ngôn ngữ bao gồm tiếng Việt.

### Vietnamese-SBERT

Vietnamese-SBERT là phiên bản của Sentence-BERT được tinh chỉnh cho tiếng Việt, cung cấp embedding câu chất lượng cao cho tìm kiếm ngữ nghĩa.

## SQLiteVectorRAGVietnamese

Lớp `SQLiteVectorRAGVietnamese` mở rộng `SQLiteVectorRAG` với hỗ trợ đặc biệt cho tiếng Việt, sử dụng các mô hình embedding chuyên biệt và xử lý ngôn ngữ đặc thù.

## Hỗ trợ Tiếng Việt trong RL-Tuning

Deep Research Core cung cấp hỗ trợ tiếng Việt trong các mô-đun RL-Tuning, cho phép huấn luyện và tinh chỉnh các mô hình ngôn ngữ lớn với dữ liệu tiếng Việt.

### Mô hình RL-Tuning với Tiếng Việt

Lớp `RLModelParadigm` hỗ trợ cấu hình đặc biệt cho tiếng Việt:

```python
# Khởi tạo mô hình với hỗ trợ tiếng Việt
model_paradigm = RLModelParadigm(
    model_id="your-model-id",
    config={
        "use_vietnamese": True,
        "add_vietnamese_tokens": True,
        "revision": "vietnamese",
        # Các tham số cấu hình tiêu chuẩn
        "learning_rate": 1e-5,
        "batch_size": 4,
        "num_epochs": 3,
        "device": "cuda",
        "output_dir": "output/model"
    }
)
```

### Tích hợp LLM với Tiếng Việt

Lớp `LLMIntegration` cung cấp các phương thức đặc biệt cho tiếng Việt:

```python
# Tạo phản hồi tiếng Việt
response = llm_integration.generate_vietnamese_response(
    prompt="Thủ đô của Việt Nam là gì?",
    system_prompt="Bạn là trợ lý AI hữu ích nói tiếng Việt.",
    temperature=0.7
)

# Dịch sang tiếng Việt
translation = llm_integration.translate_to_vietnamese(
    text="Hello, how are you?",
    temperature=0.3
)

# Phân tích văn bản tiếng Việt
analysis = llm_integration.analyze_vietnamese_text(
    text="Xin chào, bạn khỏe không?",
    analysis_type="general",
    temperature=0.3
)
```

### Hỗ trợ Môi trường Agent cho Tiếng Việt

Lớp `VietnameseSupport` cung cấp hỗ trợ tiếng Việt cho môi trường agent:

```python
# Khởi tạo hỗ trợ tiếng Việt
vietnamese_support = VietnameseSupport(
    fallback_to_english=False,
    translation_model=None
)

# Xử lý hành động từ tiếng Việt sang tiếng Anh
processed_action = vietnamese_support.process_action(
    action={"action_type": "think", "content": "Tôi đang suy nghĩ"},
    source_lang="vi"
)

# Tăng cường phản hồi tiếng Việt
enhanced_response = vietnamese_support.enhance_vietnamese_response(
    response="Đây là câu trả lời của tôi."
)
```

## Thu thập dữ liệu quỹ đạo với Tiếng Việt

Deep Research Core hỗ trợ thu thập dữ liệu quỹ đạo (trajectories) với tiếng Việt, cho phép huấn luyện các mô hình RL với dữ liệu tiếng Việt.

### Khởi tạo TrajectoryCollector với hỗ trợ Tiếng Việt

```python
from deep_research_core.rl_tuning.trajectories.trajectory_collector import TrajectoryCollector

# Khởi tạo bộ thu thập quỹ đạo với hỗ trợ tiếng Việt
collector = TrajectoryCollector(
    storage_path="data/trajectories",
    models={"model_name": model_instance},
    environments={"env_name": env_instance},
    tasks=tasks,
    vietnamese_support=True,
    vietnamese_config={
        "collect_bilingual": True,
        "translate_tasks": True,
        "vietnamese_ratio": 0.3,
        "dialect": "northern",
        "specialized_models": ["vietnamese-model-1", "vietnamese-model-2"]
    }
)
```

### Thu thập quỹ đạo tiếng Việt

```python
# Thu thập quỹ đạo tiếng Việt
num_collected = collector.collect_vietnamese_trajectories(
    model_names=["model_name"],
    env_names=["env_name"],
    tasks=tasks,
    max_per_task=5
)

print(f"Đã thu thập {num_collected} quỹ đạo tiếng Việt")
```

### Thu thập quỹ đạo song ngữ

```python
# Thu thập quỹ đạo song ngữ (Anh-Việt)
num_collected = collector.collect_bilingual_trajectories(
    model_names=["model_name"],
    env_names=["env_name"],
    tasks=tasks,
    max_per_task=5
)

print(f"Đã thu thập {num_collected} quỹ đạo song ngữ")
```

### Thống kê quỹ đạo

```python
# Lấy thống kê bao gồm các chỉ số tiếng Việt
stats = collector.get_statistics()

# Hiển thị thống kê tiếng Việt
if "language_stats" in stats:
    vi_stats = stats["language_stats"]
    print(f"Tổng số quỹ đạo tiếng Việt: {vi_stats['vietnamese_trajectories']}")
    print(f"Tổng số quỹ đạo song ngữ: {vi_stats['bilingual_trajectories']}")
    print(f"Tỷ lệ thành công tiếng Việt: {vi_stats['vietnamese_success_rate']:.2f}")
    print(f"Phần thưởng trung bình tiếng Việt: {vi_stats['vietnamese_avg_reward']:.2f}")
```

### Tùy chỉnh hệ thống nhắc (System Prompt) cho Tiếng Việt

Phương thức `_create_system_prompt` trong `TrajectoryCollector` tự động tạo hệ thống nhắc tiếng Việt khi cần thiết:

```python
# Ví dụ về hệ thống nhắc tiếng Việt được tạo tự động
system_prompt = """Bạn là một tác nhân thông minh giải quyết nhiệm vụ trong môi trường tương tác.

NHIỆM VỤ: Tìm kiếm thông tin về lịch sử Việt Nam

Mục tiêu của bạn là giải quyết nhiệm vụ này bằng cách thực hiện các hành động trong môi trường. Mỗi hành động phải được định dạng như một đối tượng JSON theo không gian hành động sau:

{
  "action_type": "string",
  "content": "string"
}

HƯỚNG DẪN:
1. Phân tích kỹ quan sát hiện tại
2. Suy nghĩ về hành động tốt nhất để thực hiện
3. Trả lời với một hành động HỢP LỆ tuân theo cấu trúc trên
4. Phản hồi của bạn CHỈ nên chứa đối tượng hành động JSON, không có văn bản nào khác

Hãy nhớ rằng, mục tiêu của bạn là giải quyết nhiệm vụ thành công và tối đa hóa phần thưởng của bạn."""
```

## Ví dụ sử dụng
from deep_research_core.reasoning.sqlite_vector_rag_vietnamese import SQLiteVectorRAGVietnamese

# Khởi tạo RAG với hỗ trợ tiếng Việt
rag = SQLiteVectorRAGVietnamese(
    db_path="tai_lieu_tieng_viet.db",
    embedding_model="phobert",  # Lựa chọn: "phobert", "viebert", "xlm-roberta-vi", "multilingual-e5"
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free",
    api_key="your-api-key"
)
```

### Tham số

- `db_path`: Đường dẫn đến cơ sở dữ liệu SQLite
- `embedding_model`: Tên của mô hình embedding tiếng Việt
- `provider`: Nhà cung cấp mô hình (openai, anthropic, openrouter)
- `model`: Tên mô hình
- `temperature`: Nhiệt độ cho sinh văn bản
- `max_tokens`: Số lượng token tối đa để sinh
- `top_k`: Số lượng tài liệu để truy xuất
- `chunk_size`: Kích thước của các đoạn tài liệu
- `chunk_overlap`: Độ chồng lấp giữa các đoạn tài liệu
- `api_key`: Khóa API cho nhà cung cấp
- `device`: Thiết bị để sử dụng cho suy luận ("cpu", "cuda", "mps")
- `cache_dir`: Thư mục để lưu trữ cache mô hình

### Phương thức

#### add_documents

Thêm tài liệu tiếng Việt vào cơ sở dữ liệu.

```python
# Thêm tài liệu tiếng Việt
tai_lieu_tieng_viet = [
    {
        "content": "Trí tuệ nhân tạo (AI) là khả năng của một hệ thống máy tính để thực hiện các nhiệm vụ thường đòi hỏi trí thông minh của con người.",
        "source": "Định nghĩa AI",
        "title": "Trí tuệ nhân tạo là gì?",
        "date": "2023-01-01"
    }
]

rag.add_documents(tai_lieu_tieng_viet)
```

#### search

Tìm kiếm tài liệu tiếng Việt tương tự với truy vấn.

```python
# Tìm kiếm tài liệu tiếng Việt
ket_qua = rag.search("Trí tuệ nhân tạo là gì?", top_k=3)

for i, ket_qua_item in enumerate(ket_qua):
    print(f"Kết quả {i+1} (điểm: {ket_qua_item['score']:.4f}):")
    print(f"  Tiêu đề: {ket_qua_item['title']}")
    print(f"  Nội dung: {ket_qua_item['content'][:100]}...")
```

#### process

Xử lý truy vấn tiếng Việt và tạo phản hồi.

```python
# Xử lý truy vấn tiếng Việt
ket_qua = rag.process("Trí tuệ nhân tạo là gì và các ứng dụng của nó?")

print(f"Câu trả lời: {ket_qua['answer']}")
print(f"Đã truy xuất {len(ket_qua['documents'])} tài liệu")
print(f"Độ trễ: {ket_qua['latency']:.4f}s")
```

## Ví dụ sử dụng

### Ví dụ 1: Tìm kiếm tài liệu tiếng Việt

```python
from deep_research_core.reasoning.sqlite_vector_rag_vietnamese import SQLiteVectorRAGVietnamese

# Khởi tạo RAG với hỗ trợ tiếng Việt
rag = SQLiteVectorRAGVietnamese(
    db_path="tai_lieu_tieng_viet.db",
    embedding_model="phobert"
)

# Thêm tài liệu tiếng Việt
tai_lieu = [
    {
        "content": "Trí tuệ nhân tạo (AI) là khả năng của một hệ thống máy tính để thực hiện các nhiệm vụ thường đòi hỏi trí thông minh của con người.",
        "source": "Định nghĩa AI",
        "title": "Trí tuệ nhân tạo là gì?",
        "date": "2023-01-01"
    },
    {
        "content": "Học máy là một nhánh của trí tuệ nhân tạo tập trung vào việc phát triển các thuật toán cho phép máy tính học từ dữ liệu.",
        "source": "Định nghĩa Học máy",
        "title": "Học máy là gì?",
        "date": "2023-01-02"
    }
]

rag.add_documents(tai_lieu)

# Tìm kiếm tài liệu
ket_qua = rag.search("Trí tuệ nhân tạo là gì?")

for i, ket_qua_item in enumerate(ket_qua):
    print(f"Kết quả {i+1} (điểm: {ket_qua_item['score']:.4f}):")
    print(f"  Tiêu đề: {ket_qua_item['title']}")
    print(f"  Nội dung: {ket_qua_item['content']}")
```

### Ví dụ 2: So sánh các mô hình embedding tiếng Việt

```python
from deep_research_core.multilingual.vietnamese_embeddings import VietnameseEmbeddingFactory

# Khởi tạo các mô hình embedding
mo_hinh = {
    "phobert": VietnameseEmbeddingFactory.get_embedding_model("phobert"),
    "viebert": VietnameseEmbeddingFactory.get_embedding_model("viebert"),
    "xlm-roberta-vi": VietnameseEmbeddingFactory.get_embedding_model("xlm-roberta-vi"),
    "multilingual-e5": VietnameseEmbeddingFactory.get_embedding_model("multilingual-e5")
}

# Chuẩn bị dữ liệu kiểm tra
truy_van = "Trí tuệ nhân tạo là gì?"
van_ban = [
    "Trí tuệ nhân tạo (AI) là khả năng của một hệ thống máy tính để thực hiện các nhiệm vụ thường đòi hỏi trí thông minh của con người.",
    "Học máy là một nhánh của trí tuệ nhân tạo tập trung vào việc phát triển các thuật toán cho phép máy tính học từ dữ liệu."
]

# So sánh độ tương đồng
for ten, mo_hinh_item in mo_hinh.items():
    print(f"\nMô hình {ten}:")
    for i, van_ban_item in enumerate(van_ban):
        do_tuong_dong = mo_hinh_item.calculate_similarity(truy_van, van_ban_item)
        print(f"  Văn bản {i+1}: {do_tuong_dong:.4f}")
```

## Đánh giá hiệu suất

### So sánh các mô hình embedding tiếng Việt

Bảng dưới đây so sánh hiệu suất của các mô hình embedding tiếng Việt khác nhau:

| Mô hình | Kích thước | Thời gian embedding | Độ chính xác tìm kiếm |
|---------|------------|---------------------|------------------------|
| PhoBERT | 768        | Nhanh               | Cao                    |
| VieBERT | 768        | Trung bình          | Cao                    |
| XLM-RoBERTa-Vi | 768  | Trung bình          | Trung bình             |
| Multilingual-E5 | 768 | Chậm                | Cao                    |
| Vietnamese-SBERT | 384 | Nhanh               | Trung bình             |

### Đánh giá tìm kiếm

Kết quả đánh giá tìm kiếm trên bộ dữ liệu tiếng Việt:

| Mô hình | Precision@5 | Recall@5 | F1@5 |
|---------|-------------|----------|------|
| PhoBERT | 0.92        | 0.85     | 0.88 |
| VieBERT | 0.89        | 0.82     | 0.85 |
| XLM-RoBERTa-Vi | 0.87  | 0.80     | 0.83 |
| Multilingual-E5 | 0.90 | 0.83     | 0.86 |
| Vietnamese-SBERT | 0.85 | 0.78     | 0.81 |

## Các vấn đề thường gặp

### Cài đặt mô hình tiếng Việt

Nếu bạn gặp vấn đề khi cài đặt các mô hình tiếng Việt, hãy thử cài đặt các phụ thuộc bổ sung:

```bash
pip install vncorenlp underthesea transformers
```

Bạn cũng có thể cần tải xuống mô hình VnCoreNLP:

```python
from vncorenlp import VnCoreNLP
VnCoreNLP.download_model()
```

### Xử lý lỗi tokenize tiếng Việt

Nếu bạn gặp lỗi khi tokenize văn bản tiếng Việt, hãy đảm bảo rằng bạn đã cài đặt đúng tokenizer:

```python
from transformers import AutoTokenizer

# Đối với PhoBERT
tokenizer = AutoTokenizer.from_pretrained("vinai/phobert-base", use_fast=False)

# Đối với VieBERT
tokenizer = AutoTokenizer.from_pretrained("FPTAI/viebert-base-cased")
```

### Tối ưu hóa hiệu suất

- Sử dụng PhoBERT cho hiệu suất tốt nhất với văn bản tiếng Việt
- Sử dụng batch processing để tăng tốc độ xử lý
- Sử dụng cache để lưu trữ embedding và kết quả tìm kiếm
- Sử dụng GPU nếu có thể để tăng tốc độ tính toán
