# QueryAnalyzer

QueryAnalyzer là một thành phần mới của WebSearchAgent, gi<PERSON><PERSON> phân tích truy vấn tìm kiếm để xác định phương thức tìm kiếm tốt nhất và công cụ tìm kiếm phù hợp nhất.

## Tính năng chính

1. **<PERSON>ân tích độ phức tạp của truy vấn**: <PERSON><PERSON><PERSON> gi<PERSON> độ phức tạp của truy vấn dựa trên độ dài, từ kh<PERSON>a phức tạp, và các yếu tố khác.
2. **Phát hiện ngôn ngữ**: Tự động phát hiện ngôn ngữ của truy vấn, hỗ trợ đặc biệt cho tiếng Việt.
3. **Phân loại loại truy vấn**: <PERSON><PERSON> loại truy vấn thành các loại nh<PERSON> h<PERSON> thu<PERSON>, k<PERSON> thu<PERSON>, y tế, <PERSON>h<PERSON><PERSON> lý, hoặc chung.
4. **Ph<PERSON>t hiện khoảng thời gian**: <PERSON>ác định khoảng thời gian liên quan đến truy vấn (gần đây, ngày hôm qua, tuần trước, v.v.).
5. **Phát hiện khu vực**: Xác định khu vực địa lý liên quan đến truy vấn (Mỹ, Anh, EU, Châu Á, Việt Nam).
6. **Xác định phương thức tìm kiếm tốt nhất**: Đề xuất phương thức tìm kiếm tốt nhất (API, crawler, academic_api) dựa trên phân tích.
7. **Đề xuất công cụ tìm kiếm cụ thể**: Đề xuất công cụ tìm kiếm cụ thể (Google, Bing, DuckDuckGo, Google Scholar, Semantic Scholar, arXiv, PubMed) dựa trên loại truy vấn.

## Cách sử dụng

```python
from deep_research_core.agents.query_analyzer import QueryAnalyzer

# Khởi tạo QueryAnalyzer
analyzer = QueryAnalyzer()

# Phân tích truy vấn
result = analyzer.analyze_query("nghiên cứu khoa học về trí tuệ nhân tạo năm 2023")

# Kết quả phân tích
print(result)
```

Kết quả phân tích sẽ bao gồm:
- `query`: Truy vấn gốc
- `cleaned_query`: Truy vấn đã được làm sạch
- `complexity`: Độ phức tạp của truy vấn (low, medium, high)
- `complexity_score`: Điểm độ phức tạp (0.0 - 1.0)
- `language`: Mã ngôn ngữ (vi, en, v.v.)
- `query_type`: Loại truy vấn (academic, technical, medical, legal, general)
- `time_range`: Khoảng thời gian (recent, past_day, past_week, past_month, past_year, specific_year)
- `region`: Khu vực (us, uk, eu, asia, vn)
- `recommended_search_method`: Phương thức tìm kiếm được đề xuất (api, crawler, academic_api)

## Tích hợp với WebSearchAgent

QueryAnalyzer đã được tích hợp vào WebSearchAgent để cải thiện việc lựa chọn phương thức tìm kiếm và công cụ tìm kiếm:

```python
from deep_research_core.agents.web_search_agent import WebSearchAgent

# Khởi tạo WebSearchAgent
agent = WebSearchAgent(search_method="auto")

# Tìm kiếm với truy vấn
results = agent.search("nghiên cứu khoa học về trí tuệ nhân tạo năm 2023")
```

Khi `search_method` được đặt thành "auto", WebSearchAgent sẽ sử dụng QueryAnalyzer để tự động chọn phương thức tìm kiếm tốt nhất và công cụ tìm kiếm phù hợp nhất.

## Tùy chỉnh

Bạn có thể tùy chỉnh QueryAnalyzer bằng cách cung cấp cấu hình tùy chỉnh:

```python
config = {
    "academic_keywords": ["research", "paper", "study", "nghiên cứu", "bài báo"],
    "technical_keywords": ["code", "programming", "software", "mã nguồn", "lập trình"],
    "medical_keywords": ["medical", "health", "disease", "y học", "sức khỏe"],
    "legal_keywords": ["legal", "law", "regulation", "pháp luật", "luật"],
    "complexity_indicators": {
        "high": ["compare", "difference between", "so sánh", "khác biệt"],
        "medium": ["what is", "how to", "là gì", "cách"],
        "low": ["weather", "news", "thời tiết", "tin tức"]
    },
    "time_keywords": {
        "recent": ["recent", "latest", "mới đây", "mới nhất"],
        "past_day": ["yesterday", "hôm qua"],
        # ...
    },
    "region_keywords": {
        "us": ["usa", "united states", "hoa kỳ", "mỹ"],
        "uk": ["uk", "united kingdom", "anh", "vương quốc anh"],
        # ...
    }
}

analyzer = QueryAnalyzer(config=config)
```

## Lợi ích

- **Cải thiện chất lượng kết quả tìm kiếm**: Bằng cách chọn phương thức tìm kiếm và công cụ tìm kiếm phù hợp nhất, QueryAnalyzer giúp cải thiện chất lượng kết quả tìm kiếm.
- **Tối ưu hóa hiệu suất**: Bằng cách chọn phương thức tìm kiếm phù hợp, QueryAnalyzer giúp tối ưu hóa hiệu suất tìm kiếm.
- **Hỗ trợ đa ngôn ngữ**: QueryAnalyzer hỗ trợ đặc biệt cho tiếng Việt, giúp cải thiện chất lượng kết quả tìm kiếm cho người dùng Việt Nam.
- **Tùy chỉnh linh hoạt**: QueryAnalyzer có thể được tùy chỉnh để phù hợp với nhu cầu cụ thể của ứng dụng.
