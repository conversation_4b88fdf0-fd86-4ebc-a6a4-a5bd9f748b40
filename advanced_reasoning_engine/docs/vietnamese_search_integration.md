# Tích hợp tìm kiếm tiếng Việt cho WebSearchAgent

## Tổng quan

Đã tích hợp các công cụ tìm kiếm tiếng Việt vào WebSearchAgent để cải thiện kết quả tìm kiếm cho truy vấn tiếng Việt. C<PERSON><PERSON> công cụ tìm kiếm tiếng Việt được thêm vào bao gồm:

1. **Cốc Cốc**: Công cụ tìm kiếm phổ biến tại Việt Nam
2. **Wiki tiếng Việt**: Tìm kiếm trong Wikipedia tiếng Việt
3. **Báo Mới**: Tìm kiếm tin tức tiếng Việt

## Cải tiến

1. **Phát hiện ngôn ngữ tiếng Việt**: Sử dụng thư viện langdetect và kiểm tra các ký tự đặc biệt của tiếng Việt
2. **Ưu tiên công cụ tìm kiếm**: Ưu tiên các công cụ tìm kiếm tiếng Việt cho truy vấn tiếng Việt
3. **Cơ chế fallback**: Cải thiện cơ chế fallback khi các công cụ tìm kiếm thất bại
4. **Điều chỉnh tham số Crawlee**: Tăng max_pages và max_depth cho truy vấn tiếng Việt

## Thách thức

1. **Giới hạn tốc độ**: Các dịch vụ tìm kiếm thường giới hạn số lượng truy vấn, dẫn đến lỗi 429 Too Many Requests
2. **Chặn truy cập**: Một số dịch vụ chặn truy cập từ các IP không phải từ Việt Nam, dẫn đến lỗi 403 Forbidden
3. **Thay đổi cấu trúc**: Cấu trúc HTML của các trang web có thể thay đổi, làm cho việc trích xuất dữ liệu trở nên khó khăn

## Kết quả test

1. **Truy vấn tiếng Anh**: Hoạt động tốt với DuckDuckGo và Crawlee
2. **Truy vấn tiếng Việt**: Đã nhận diện đúng và ưu tiên các công cụ tìm kiếm tiếng Việt, nhưng các dịch vụ tìm kiếm đều gặp vấn đề:
   - Cốc Cốc, Wiki tiếng Việt, Báo Mới: Không trả về kết quả
   - DuckDuckGo: Không trả về kết quả cho truy vấn tiếng Việt
   - SearX: Gặp lỗi 403 Forbidden hoặc 429 Too Many Requests

## Cải tiến trong tương lai

1. **Proxy Việt Nam**: Sử dụng proxy từ Việt Nam để tránh bị chặn
2. **Caching**: Cải thiện cơ chế caching để giảm số lượng truy vấn
3. **Thêm công cụ tìm kiếm**: Thêm các công cụ tìm kiếm tiếng Việt khác như Bing Vietnam, Google Vietnam
4. **Cải thiện trích xuất dữ liệu**: Cải thiện việc trích xuất dữ liệu từ các trang web tiếng Việt
5. **Tích hợp dịch**: Tích hợp dịch tự động để dịch truy vấn tiếng Việt sang tiếng Anh và ngược lại
