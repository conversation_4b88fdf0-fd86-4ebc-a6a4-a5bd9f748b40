# Use Cases cơ bản (Single Module)

Các use case c<PERSON> bản sử dụng chủ yếu một module chính của Deep Research Core để giải quyết các vấn đề đơn giản. Đ<PERSON>y là điểm khởi đầu tốt để làm quen với framework.

## 1. <PERSON><PERSON> thống RAG cơ bản

### Mô tả
Sử dụng Retrieval-Augmented Generation (RAG) để tạo hệ thống hỏi đáp dựa trên tài liệu, giúp truy xuất thông tin liên quan và tạo câu trả lời chính xác.

### Các module chính
- **Reasoning Module**: RAG

### Sơ đồ hoạt động
```
Truy vấn người dùng
    ↓
Xử lý truy vấn
    ↓
Truy xuất tài liệu liên quan
    ↓
Tạo câu trả lời dựa trên tài liệu
    ↓
Trả về kết quả cho người dùng
```

### Ví dụ thực tế
- **<PERSON><PERSON> thống hỏi đáp tài liệu nội bộ**: Truy xuất thông tin từ tài liệu công ty
- **Trợ lý hỗ trợ khách hàng**: Trả lời câu hỏi dựa trên tài liệu sản phẩm
- **Hệ thống tra cứu pháp luật**: Truy xuất thông tin từ văn bản pháp luật

### Mã nguồn tham khảo
```python
from deep_research_core.reasoning import RAG
from deep_research_core.vector_store import SQLiteVectorStore

# Khởi tạo RAG
rag = RAG(
    vector_store=SQLiteVectorStore("documents.db"),
    embedding_model="all-MiniLM-L6-v2",
    provider="openai",
    model="gpt-4"
)

# Thêm tài liệu
rag.add_documents("path/to/documents")

# Truy vấn
result = rag.generate_reasoning("Tóm tắt các chính sách mới về thuế trong tài liệu")
print(result)
```

### Ưu điểm
- Dễ triển khai và sử dụng
- Cải thiện độ chính xác bằng cách sử dụng thông tin từ tài liệu
- Giảm hallucination
- Có thể cập nhật kiến thức bằng cách thêm tài liệu mới

## 2. Hệ thống ToT cơ bản

### Mô tả
Sử dụng Tree of Thought (ToT) để tạo hệ thống suy luận có khả năng khám phá nhiều đường dẫn suy luận khác nhau, đánh giá từng đường dẫn, và chọn đường dẫn tốt nhất.

### Các module chính
- **Reasoning Module**: ToT

### Sơ đồ hoạt động
```
Truy vấn người dùng
    ↓
Tạo nhiều đường dẫn suy luận
    ↓
Đánh giá từng đường dẫn
    ↓
Chọn đường dẫn tốt nhất
    ↓
Trả về kết quả cho người dùng
```

### Ví dụ thực tế
- **Hệ thống giải quyết vấn đề phức tạp**: Khám phá nhiều giải pháp cho một vấn đề
- **Trợ lý lập kế hoạch**: Đánh giá nhiều phương án kế hoạch
- **Hệ thống phân tích chiến lược**: Phân tích nhiều chiến lược kinh doanh

### Mã nguồn tham khảo
```python
from deep_research_core.reasoning import TreeOfThought

# Khởi tạo ToT
tot = TreeOfThought(
    provider="openai",
    model="gpt-4",
    max_branches=3,
    max_depth=3
)

# Truy vấn
result = tot.generate_reasoning("Đề xuất 3 chiến lược marketing khác nhau cho sản phẩm mới")
print(result)
```

### Ưu điểm
- Khám phá nhiều hướng giải quyết vấn đề
- Tránh bị mắc kẹt ở các đường dẫn suy luận không hiệu quả
- Cải thiện độ chính xác cho các vấn đề phức tạp
- Cung cấp nhiều giải pháp thay vì chỉ một

## 3. Hệ thống CoT cơ bản

### Mô tả
Sử dụng Chain of Thought (CoT) để tạo hệ thống suy luận từng bước, giúp mô hình giải quyết vấn đề phức tạp bằng cách chia nhỏ thành các bước trung gian.

### Các module chính
- **Reasoning Module**: CoT

### Sơ đồ hoạt động
```
Truy vấn người dùng
    ↓
Chia vấn đề thành các bước
    ↓
Suy luận từng bước
    ↓
Tổng hợp kết quả
    ↓
Trả về kết quả cho người dùng
```

### Ví dụ thực tế
- **Hệ thống giải toán**: Giải quyết bài toán từng bước
- **Trợ lý giáo dục**: Giải thích khái niệm phức tạp từng bước
- **Hệ thống phân tích logic**: Phân tích lập luận từng bước

### Mã nguồn tham khảo
```python
from deep_research_core.reasoning import ChainOfThought

# Khởi tạo CoT
cot = ChainOfThought(
    provider="openai",
    model="gpt-4"
)

# Truy vấn
result = cot.generate_reasoning("Tính tổng của 17 và 28, sau đó nhân với 3")
print(result)
```

### Ưu điểm
- Dễ hiểu và theo dõi quá trình suy luận
- Hiệu quả cho các vấn đề cần suy luận tuần tự
- Cải thiện độ chính xác cho các vấn đề toán học và logic
- Giúp người dùng hiểu quá trình suy luận

## 4. Hệ thống ReAct cơ bản

### Mô tả
Sử dụng Reasoning and Acting (ReAct) để tạo hệ thống có khả năng kết hợp suy luận với hành động, cho phép mô hình tương tác với môi trường bên ngoài.

### Các module chính
- **Reasoning Module**: ReAct
- **Tools Module**: Các công cụ cơ bản

### Sơ đồ hoạt động
```
Truy vấn người dùng
    ↓
Suy luận về hành động cần thực hiện
    ↓
Thực hiện hành động (sử dụng công cụ)
    ↓
Quan sát kết quả
    ↓
Suy luận tiếp theo
    ↓
Trả về kết quả cho người dùng
```

### Ví dụ thực tế
- **Trợ lý ảo tương tác**: Thực hiện các tác vụ theo yêu cầu
- **Hệ thống tự động hóa**: Tự động thực hiện các quy trình
- **Trợ lý nghiên cứu**: Tìm kiếm và tổng hợp thông tin

### Mã nguồn tham khảo
```python
from deep_research_core.reasoning import ReAct
from deep_research_core.tools import SearchTool, CalculatorTool

# Khởi tạo ReAct
react = ReAct(
    provider="openai",
    model="gpt-4",
    tools=[SearchTool(), CalculatorTool()]
)

# Truy vấn
result = react.generate_reasoning("Tìm kiếm GDP của Việt Nam năm 2022 và tính tăng trưởng so với 2021")
print(result)
```

### Ưu điểm
- Kết hợp suy luận với hành động thực tế
- Có thể sử dụng công cụ bên ngoài
- Thích hợp cho các tác vụ tương tác
- Mở rộng khả năng của mô hình ngôn ngữ
