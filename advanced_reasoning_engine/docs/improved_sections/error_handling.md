# Hệ thống xử lý lỗi (Error Handling System)

Deep Research Core cung cấp một hệ thống xử lý lỗi toàn diện, thống nhất các cơ chế xử lý lỗi từ nhiều module khác nhau. Dưới đây là hệ thống phân cấp rõ ràng của các thành phần xử lý lỗi:

## C<PERSON>u trúc hệ thống xử lý lỗi

```
Error Handling System
├── Core Error Handling
│   ├── ErrorDetection: Phát hiện lỗi
│   ├── ErrorClassification: Phân loại lỗi
│   ├── ErrorRecovery: <PERSON>ụ<PERSON> hồi từ lỗi
│   └── ErrorLogging: Ghi nhật ký lỗi
├── Domain-Specific Error Handling
│   ├── ReasoningErrorHandler: Xử lý lỗi trong suy luận
│   ├── RAGErrorHandler: Xử lý lỗi trong RAG
│   ├── AgentErrorHandler: Xử lý lỗi trong hệ thống agent
│   └── WebErrorHandler: Xử lý lỗi trong web module
└── Error Monitoring & Analytics
    ├── ErrorDashboard: Bảng điều khiển lỗi
    ├── ErrorAnalytics: <PERSON>ân tích xu hướng lỗi
    └── ErrorAlerts: Cảnh báo lỗi
```

## Core Error Handling

### ErrorDetection
**Mô tả**: Phát hiện lỗi trong quá trình thực thi, bao gồm lỗi hệ thống, lỗi mô hình, và lỗi đầu vào.

**Ưu điểm**:
- Phát hiện lỗi sớm
- Hỗ trợ nhiều loại lỗi
- Tích hợp với các module khác

**Trường hợp sử dụng**:
- Phát hiện lỗi trong quá trình suy luận
- Phát hiện lỗi trong quá trình truy xuất thông tin
- Phát hiện lỗi trong quá trình tương tác với API

**Ví dụ**:
```python
from deep_research_core.error_handling import ErrorDetector

error_detector = ErrorDetector(
    detection_level="high",  # hoặc "medium", "low"
    detection_types=["system", "model", "input", "output"],
    early_detection=True
)

# Kiểm tra lỗi đầu vào
input_error = error_detector.check_input("Đầu vào cần kiểm tra")

# Kiểm tra lỗi đầu ra
output_error = error_detector.check_output("Đầu ra cần kiểm tra")

# Kiểm tra lỗi trong quá trình thực thi
def process_with_error_detection(func, *args, **kwargs):
    try:
        result = func(*args, **kwargs)
        if error_detector.check_output(result):
            return result
        else:
            return "Đầu ra không hợp lệ"
    except Exception as e:
        return error_detector.handle_exception(e)
```

### ErrorClassification
**Mô tả**: Phân loại lỗi thành các loại khác nhau để xử lý phù hợp.

**Ưu điểm**:
- Phân loại lỗi chính xác
- Hỗ trợ xử lý lỗi có mục tiêu
- Cải thiện khả năng phục hồi

**Trường hợp sử dụng**:
- Phân loại lỗi để xử lý phù hợp
- Ưu tiên xử lý các lỗi nghiêm trọng
- Phân tích nguyên nhân gốc rễ

**Ví dụ**:
```python
from deep_research_core.error_handling import ErrorClassifier

error_classifier = ErrorClassifier(
    classification_model="rule_based",  # hoặc "ml_based"
    custom_rules={"timeout": "Quá thời gian chờ", "api_error": "Lỗi API"}
)

# Phân loại lỗi
error = Exception("API request failed with status code 429")
error_class = error_classifier.classify(error)
# Output: "api_error"

# Phân loại lỗi với ngữ cảnh
error_with_context = error_classifier.classify_with_context(
    error=error,
    context={"module": "RAG", "operation": "retrieve", "attempt": 3}
)
# Output: {"class": "api_error", "subclass": "rate_limit", "severity": "high"}
```

### ErrorRecovery
**Mô tả**: Phục hồi từ lỗi bằng cách thực hiện các chiến lược phục hồi phù hợp.

**Ưu điểm**:
- Tự động phục hồi từ lỗi
- Giảm thời gian ngừng hoạt động
- Cải thiện trải nghiệm người dùng

**Trường hợp sử dụng**:
- Phục hồi từ lỗi tạm thời
- Thử lại các hoạt động thất bại
- Chuyển đổi dự phòng khi cần thiết

**Ví dụ**:
```python
from deep_research_core.error_handling import ErrorRecovery

error_recovery = ErrorRecovery(
    recovery_strategies={
        "timeout": "retry",
        "api_error": "fallback",
        "model_error": "alternative_model",
        "input_error": "fix_input"
    },
    max_retries=3,
    backoff_factor=2
)

# Phục hồi từ lỗi
def execute_with_recovery(func, *args, **kwargs):
    try:
        return func(*args, **kwargs)
    except Exception as e:
        error_class = error_classifier.classify(e)
        return error_recovery.recover(error_class, func, *args, **kwargs)

# Sử dụng với RAG
try:
    result = rag.generate_reasoning("Truy vấn người dùng")
except Exception as e:
    result = error_recovery.recover_rag(e, rag, "Truy vấn người dùng")
```

### ErrorLogging
**Mô tả**: Ghi nhật ký lỗi để phân tích và cải thiện hệ thống.

**Ưu điểm**:
- Ghi lại thông tin chi tiết về lỗi
- Hỗ trợ phân tích nguyên nhân
- Theo dõi xu hướng lỗi

**Trường hợp sử dụng**:
- Ghi nhật ký lỗi để phân tích sau
- Theo dõi tần suất lỗi
- Cải thiện hệ thống dựa trên phân tích lỗi

**Ví dụ**:
```python
from deep_research_core.error_handling import ErrorLogger

error_logger = ErrorLogger(
    log_level="info",  # hoặc "debug", "warning", "error", "critical"
    log_format="json",  # hoặc "text"
    log_destination="file",  # hoặc "console", "database", "cloud"
    log_file="errors.log"
)

# Ghi nhật ký lỗi
try:
    result = complex_operation()
except Exception as e:
    error_logger.log(
        error=e,
        context={"module": "reasoning", "operation": "generate", "user_id": "123"},
        severity="high"
    )

# Truy vấn nhật ký lỗi
recent_errors = error_logger.query(
    time_range={"start": "2023-12-01", "end": "2023-12-15"},
    filters={"module": "reasoning", "severity": "high"}
)
```

## Domain-Specific Error Handling

### ReasoningErrorHandler
**Mô tả**: Xử lý lỗi đặc thù trong quá trình suy luận, bao gồm lỗi logic, lỗi suy luận, và lỗi mô hình.

**Ưu điểm**:
- Xử lý lỗi đặc thù trong suy luận
- Cải thiện chất lượng suy luận
- Phát hiện và sửa lỗi logic

**Trường hợp sử dụng**:
- Xử lý lỗi trong Chain of Thought
- Xử lý lỗi trong Tree of Thought
- Xử lý lỗi trong ReAct

**Ví dụ**:
```python
from deep_research_core.error_handling import ReasoningErrorHandler

reasoning_error_handler = ReasoningErrorHandler(
    reasoning_types=["cot", "tot", "react"],
    detection_strategies=["contradiction", "circular", "incomplete"],
    recovery_strategies=["reformulate", "branch", "simplify"]
)

# Sử dụng với Chain of Thought
try:
    result = cot.generate_reasoning("Giải bài toán phức tạp")
except Exception as e:
    result = reasoning_error_handler.handle_cot_error(e, cot, "Giải bài toán phức tạp")

# Sử dụng với Tree of Thought
try:
    result = tot.generate_reasoning("Phân tích vấn đề đa chiều")
except Exception as e:
    result = reasoning_error_handler.handle_tot_error(e, tot, "Phân tích vấn đề đa chiều")
```

### RAGErrorHandler
**Mô tả**: Xử lý lỗi đặc thù trong quá trình truy xuất thông tin và tạo văn bản dựa trên thông tin truy xuất.

**Ưu điểm**:
- Xử lý lỗi đặc thù trong RAG
- Cải thiện chất lượng truy xuất
- Giảm hallucination

**Trường hợp sử dụng**:
- Xử lý lỗi truy xuất thông tin
- Xử lý lỗi tạo văn bản
- Xử lý lỗi nguồn thông tin

**Ví dụ**:
```python
from deep_research_core.error_handling import RAGErrorHandler

rag_error_handler = RAGErrorHandler(
    retrieval_strategies=["expand_query", "alternative_embedding", "reduce_filter"],
    generation_strategies=["constrain_output", "verify_facts", "cite_sources"],
    hallucination_detection=True
)

# Sử dụng với RAG
try:
    result = rag.generate_reasoning("Truy vấn người dùng")
except Exception as e:
    result = rag_error_handler.handle_error(e, rag, "Truy vấn người dùng")

# Kiểm tra hallucination
generated_text = rag.generate_reasoning("Truy vấn người dùng")
if rag_error_handler.detect_hallucination(generated_text, rag.retrieved_documents):
    corrected_text = rag_error_handler.correct_hallucination(generated_text, rag.retrieved_documents)
```

### AgentErrorHandler
**Mô tả**: Xử lý lỗi đặc thù trong hệ thống agent, bao gồm lỗi giao tiếp, lỗi phối hợp, và lỗi thực thi nhiệm vụ.

**Ưu điểm**:
- Xử lý lỗi đặc thù trong hệ thống agent
- Cải thiện phối hợp giữa các agent
- Đảm bảo hoàn thành nhiệm vụ

**Trường hợp sử dụng**:
- Xử lý lỗi giao tiếp giữa các agent
- Xử lý lỗi phân công nhiệm vụ
- Xử lý lỗi đồng thuận

**Ví dụ**:
```python
from deep_research_core.error_handling import AgentErrorHandler

agent_error_handler = AgentErrorHandler(
    agent_types=["researcher", "synthesizer", "orchestrator"],
    communication_strategies=["retry", "simplify", "reroute"],
    task_strategies=["decompose", "reassign", "simplify"]
)

# Sử dụng với Agent System
try:
    result = agent_system.execute_task("Nhiệm vụ phức tạp")
except Exception as e:
    result = agent_error_handler.handle_error(e, agent_system, "Nhiệm vụ phức tạp")

# Xử lý lỗi giao tiếp
try:
    agent_a.send_message(agent_b, "Thông điệp phức tạp")
except Exception as e:
    agent_error_handler.handle_communication_error(e, agent_a, agent_b, "Thông điệp phức tạp")
```

### WebErrorHandler
**Mô tả**: Xử lý lỗi đặc thù trong web module, bao gồm lỗi API, lỗi giao diện người dùng, và lỗi xác thực.

**Ưu điểm**:
- Xử lý lỗi đặc thù trong web module
- Cải thiện trải nghiệm người dùng
- Đảm bảo tính khả dụng của ứng dụng

**Trường hợp sử dụng**:
- Xử lý lỗi API
- Xử lý lỗi giao diện người dùng
- Xử lý lỗi xác thực và phân quyền

**Ví dụ**:
```python
from deep_research_core.error_handling import WebErrorHandler

web_error_handler = WebErrorHandler(
    api_strategies={"timeout": "retry", "rate_limit": "backoff", "server_error": "fallback"},
    ui_strategies={"render_error": "simplified_view", "data_error": "placeholder"},
    auth_strategies={"token_expired": "refresh", "invalid_credentials": "reauth"}
)

# Sử dụng với API
try:
    response = api_client.request("GET", "/data")
except Exception as e:
    response = web_error_handler.handle_api_error(e, api_client, "GET", "/data")

# Xử lý lỗi giao diện người dùng
try:
    ui_component.render(data)
except Exception as e:
    web_error_handler.handle_ui_error(e, ui_component, data)
```

## Error Monitoring & Analytics

### ErrorDashboard
**Mô tả**: Bảng điều khiển trực quan hóa thông tin về lỗi, giúp theo dõi và phân tích lỗi.

**Ưu điểm**:
- Trực quan hóa thông tin lỗi
- Theo dõi xu hướng lỗi theo thời gian
- Hỗ trợ ra quyết định

**Trường hợp sử dụng**:
- Theo dõi tình trạng hệ thống
- Phân tích xu hướng lỗi
- Đánh giá hiệu quả xử lý lỗi

**Ví dụ**:
```python
from deep_research_core.error_handling import ErrorDashboard

error_dashboard = ErrorDashboard(
    data_source="error_logs.db",
    update_interval=60,  # giây
    visualization_types=["time_series", "pie_chart", "heatmap"]
)

# Khởi tạo dashboard
error_dashboard.initialize()

# Cập nhật dashboard
error_dashboard.update()

# Xuất báo cáo
report = error_dashboard.generate_report(
    time_range={"start": "2023-12-01", "end": "2023-12-15"},
    group_by="module",
    metrics=["count", "severity", "recovery_rate"]
)
```

### ErrorAnalytics
**Mô tả**: Phân tích xu hướng lỗi và nguyên nhân gốc rễ để cải thiện hệ thống.

**Ưu điểm**:
- Phân tích sâu về lỗi
- Xác định nguyên nhân gốc rễ
- Đề xuất cải tiến

**Trường hợp sử dụng**:
- Phân tích nguyên nhân lỗi
- Dự đoán lỗi tiềm ẩn
- Tối ưu hóa chiến lược xử lý lỗi

**Ví dụ**:
```python
from deep_research_core.error_handling import ErrorAnalytics

error_analytics = ErrorAnalytics(
    data_source="error_logs.db",
    analysis_methods=["frequency", "correlation", "root_cause", "trend"],
    ml_enabled=True
)

# Phân tích xu hướng lỗi
trend_analysis = error_analytics.analyze_trends(
    time_range={"start": "2023-11-01", "end": "2023-12-15"},
    group_by=["module", "error_type"],
    interval="day"
)

# Phân tích nguyên nhân gốc rễ
root_cause = error_analytics.analyze_root_cause(
    error_type="timeout",
    context_variables=["module", "operation", "load", "time_of_day"]
)

# Đề xuất cải tiến
recommendations = error_analytics.generate_recommendations(
    target_modules=["reasoning", "rag"],
    priority="high"
)
```

### ErrorAlerts
**Mô tả**: Cảnh báo về lỗi nghiêm trọng hoặc xu hướng lỗi bất thường.

**Ưu điểm**:
- Phát hiện sớm vấn đề
- Thông báo kịp thời
- Giảm thời gian phản ứng

**Trường hợp sử dụng**:
- Cảnh báo lỗi nghiêm trọng
- Thông báo xu hướng lỗi bất thường
- Theo dõi ngưỡng lỗi

**Ví dụ**:
```python
from deep_research_core.error_handling import ErrorAlerts

error_alerts = ErrorAlerts(
    alert_channels=["email", "slack", "dashboard"],
    alert_thresholds={
        "critical": 1,  # cảnh báo ngay lập tức
        "high": 5,  # cảnh báo sau 5 lỗi
        "medium": 10,  # cảnh báo sau 10 lỗi
        "low": 50  # cảnh báo sau 50 lỗi
    },
    alert_cooldown=300  # giây
)

# Cấu hình người nhận
error_alerts.configure_recipients(
    email=["<EMAIL>", "<EMAIL>"],
    slack="#error-alerts"
)

# Kích hoạt cảnh báo
error_alerts.trigger(
    error_type="api_timeout",
    severity="high",
    context={"module": "rag", "operation": "retrieve", "affected_users": 25}
)

# Theo dõi lỗi và tự động cảnh báo
error_alerts.monitor(error_logger)
```

## Giao diện thống nhất cho xử lý lỗi

Deep Research Core cung cấp giao diện thống nhất `ErrorHandler` để dễ dàng tích hợp các thành phần xử lý lỗi:

```python
from deep_research_core.error_handling import ErrorHandler

# Khởi tạo Error Handler với cấu hình đầy đủ
error_handler = ErrorHandler(
    # Cấu hình cơ bản
    domain="reasoning",  # hoặc "rag", "agent", "web"
    log_level="info",
    
    # Cấu hình phát hiện và phân loại
    detection_level="high",
    classification_model="rule_based",
    
    # Cấu hình phục hồi
    enable_recovery=True,
    recovery_strategies={
        "timeout": "retry",
        "api_error": "fallback",
        "model_error": "alternative_model"
    },
    max_retries=3,
    
    # Cấu hình ghi nhật ký
    enable_logging=True,
    log_destination="file",
    log_file="errors.log",
    
    # Cấu hình giám sát
    enable_monitoring=True,
    alert_channels=["email", "slack"],
    alert_thresholds={"critical": 1, "high": 5}
)

# Sử dụng trong code
try:
    result = perform_complex_operation()
except Exception as e:
    handled_result = error_handler.handle_error(
        error=e,
        context={"operation": "complex_operation", "user_id": "123"}
    )

# Xử lý lỗi theo domain
try:
    result = rag.generate_reasoning("Truy vấn người dùng")
except Exception as e:
    handled_result = error_handler.handle_rag_error(e, rag, "Truy vấn người dùng")

try:
    result = tot.generate_reasoning("Phân tích vấn đề")
except Exception as e:
    handled_result = error_handler.handle_reasoning_error(e, tot, "Phân tích vấn đề")

try:
    result = agent_system.execute_task("Nhiệm vụ phức tạp")
except Exception as e:
    handled_result = error_handler.handle_agent_error(e, agent_system, "Nhiệm vụ phức tạp")
```

## Ví dụ sử dụng ErrorHandler trong các module khác

### Sử dụng với RAG
```python
from deep_research_core.reasoning import RAG
from deep_research_core.error_handling import ErrorHandler

# Khởi tạo Error Handler cho RAG
rag_error_handler = ErrorHandler(
    domain="rag",
    enable_recovery=True,
    recovery_strategies={
        "retrieval_error": "expand_query",
        "embedding_error": "alternative_model",
        "generation_error": "constrain_output"
    }
)

# Khởi tạo RAG với error handler
rag = RAG(
    vector_store=SQLiteVectorStore("documents.db"),
    embedding_model="all-MiniLM-L6-v2",
    provider="openai",
    model="gpt-4",
    error_handler=rag_error_handler
)

# Sử dụng RAG với xử lý lỗi tích hợp
try:
    result = rag.generate_reasoning("Truy vấn người dùng")
except Exception as e:
    # Error handler sẽ tự động được gọi trong RAG
    pass
```

### Sử dụng với ToT
```python
from deep_research_core.reasoning import TreeOfThought
from deep_research_core.error_handling import ErrorHandler

# Khởi tạo Error Handler cho ToT
tot_error_handler = ErrorHandler(
    domain="reasoning",
    enable_recovery=True,
    recovery_strategies={
        "branch_error": "alternative_branch",
        "evaluation_error": "simplified_evaluation",
        "selection_error": "random_selection"
    }
)

# Khởi tạo ToT với error handler
tot = TreeOfThought(
    provider="openai",
    model="gpt-4",
    max_branches=3,
    max_depth=3,
    error_handler=tot_error_handler
)

# Sử dụng ToT với xử lý lỗi tích hợp
result = tot.generate_reasoning("Phân tích vấn đề phức tạp")
```

### Sử dụng với Multi-Agent
```python
from deep_research_core.agent_system import AgentSystem
from deep_research_core.error_handling import ErrorHandler

# Khởi tạo Error Handler cho Agent System
agent_error_handler = ErrorHandler(
    domain="agent",
    enable_recovery=True,
    recovery_strategies={
        "communication_error": "retry",
        "task_error": "decompose",
        "consensus_error": "simplified_voting"
    }
)

# Khởi tạo Agent System với error handler
agent_system = AgentSystem(
    provider="openai",
    model="gpt-4",
    error_handler=agent_error_handler
)

# Thêm các agent
agent_system.add_agent("researcher", ResearcherAgent())
agent_system.add_agent("synthesizer", SynthesizerAgent())

# Sử dụng Agent System với xử lý lỗi tích hợp
result = agent_system.execute_task("Nhiệm vụ phức tạp")
```

## Cây quyết định cho việc lựa chọn xử lý lỗi

Để chọn cơ chế xử lý lỗi phù hợp, hãy trả lời các câu hỏi sau:

1. **Bạn cần xử lý lỗi trong module nào?**
   - **Reasoning Module** → Sử dụng `ReasoningErrorHandler`
   - **RAG Module** → Sử dụng `RAGErrorHandler`
   - **Agent System** → Sử dụng `AgentErrorHandler`
   - **Web Module** → Sử dụng `WebErrorHandler`
   - **Nhiều module** → Sử dụng `ErrorHandler` với domain phù hợp

2. **Bạn cần khả năng phục hồi từ lỗi?**
   - **Có** → Bật `enable_recovery=True` và cấu hình `recovery_strategies`
   - **Không** → Bật `enable_recovery=False`

3. **Bạn cần ghi nhật ký lỗi?**
   - **Có** → Bật `enable_logging=True` và cấu hình `log_destination`
   - **Không** → Bật `enable_logging=False`

4. **Bạn cần giám sát và cảnh báo lỗi?**
   - **Có** → Bật `enable_monitoring=True` và cấu hình `alert_channels`
   - **Không** → Bật `enable_monitoring=False`

5. **Bạn cần phân tích lỗi?**
   - **Có** → Sử dụng thêm `ErrorAnalytics`
   - **Không** → Không cần thêm
