# Hỗ trợ tiếng Việt (Vietnamese Support)

Deep Research Core cung cấp hỗ trợ toàn diện cho tiếng Việt, bao gồm các thành phần chuyên biệt để xử lý đặc thù của ngôn ngữ Việt. Dưới đây là hệ thống phân cấp rõ ràng của các thành phần hỗ trợ tiếng Việt:

## Cấu trúc hệ thống hỗ trợ tiếng Việt

```
Vietnamese Support
├── Embeddings
│   ├── PhoBERT: Embedding chuyên biệt cho tiếng Việt
│   ├── VieBERT: Embedding tiếng Việt dựa trên BERT
│   ├── XLM-RoBERTa-Vi: Embedding đa ngôn ngữ với hỗ trợ tiếng Việt
│   └── Multilingual-E5: Embedding đa ngôn ngữ hiệu suất cao
├── Text Processing
│   ├── CompoundProcessor: Xử lý từ ghép tiếng Việt
│   ├── DiacriticProcessor: Xử lý dấu tiếng Việt
│   └── DialectProcessor: Xử lý phương ngữ tiếng Việt
├── Optimization
│   ├── PromptOptimizer: Tối ưu hóa prompt tiếng Việt
│   └── ModelQuantizer: Lượng tử hóa mô hình tiếng Việt
└── Evaluation
    ├── RLEvaluator: Đánh giá mô hình RL-tuning tiếng Việt
    └── BenchmarkDatasets: Bộ dữ liệu đánh giá tiếng Việt
```

## Embeddings tiếng Việt

### PhoBERT
**Mô tả**: Mô hình embedding chuyên biệt cho tiếng Việt, được huấn luyện trên kho dữ liệu tiếng Việt lớn.

**Ưu điểm**:
- Hiểu sâu về ngữ nghĩa tiếng Việt
- Xử lý tốt các đặc thù của tiếng Việt
- Hiệu suất cao cho các tác vụ NLP tiếng Việt

**Trường hợp sử dụng**:
- Phân tích văn bản tiếng Việt
- Tìm kiếm ngữ nghĩa tiếng Việt
- Phân loại văn bản tiếng Việt

**Ví dụ**:
```python
from deep_research_core.multilingual import VietnameseEmbeddings

phobert = VietnameseEmbeddings(
    model="phobert",
    version="base",  # hoặc "large"
    max_length=256
)

# Tạo embedding cho văn bản tiếng Việt
embeddings = phobert.encode("Tiếng Việt là một ngôn ngữ có nhiều đặc điểm thú vị.")
```

### VieBERT
**Mô tả**: Mô hình embedding tiếng Việt dựa trên kiến trúc BERT, được tinh chỉnh cho các tác vụ NLP tiếng Việt.

**Ưu điểm**:
- Tương thích với hệ sinh thái BERT
- Hiệu suất tốt cho các tác vụ phân loại
- Dễ tinh chỉnh cho các tác vụ cụ thể

**Trường hợp sử dụng**:
- Phân loại văn bản tiếng Việt
- Trích xuất thông tin từ văn bản tiếng Việt
- Phân tích cảm xúc tiếng Việt

**Ví dụ**:
```python
from deep_research_core.multilingual import VietnameseEmbeddings

viebert = VietnameseEmbeddings(
    model="viebert",
    version="base",
    max_length=512
)

# Tạo embedding cho văn bản tiếng Việt
embeddings = viebert.encode("Phân tích cảm xúc trong bài đánh giá sản phẩm này.")
```

### XLM-RoBERTa-Vi
**Mô tả**: Phiên bản của XLM-RoBERTa được tinh chỉnh cho tiếng Việt, hỗ trợ cả tiếng Việt và các ngôn ngữ khác.

**Ưu điểm**:
- Hỗ trợ đa ngôn ngữ
- Hiệu suất tốt cho các tác vụ đa ngôn ngữ
- Chuyển giao kiến thức giữa các ngôn ngữ

**Trường hợp sử dụng**:
- Xử lý văn bản đa ngôn ngữ có tiếng Việt
- Dịch thuật liên quan đến tiếng Việt
- Ứng dụng cần hỗ trợ nhiều ngôn ngữ

**Ví dụ**:
```python
from deep_research_core.multilingual import VietnameseEmbeddings

xlm_roberta_vi = VietnameseEmbeddings(
    model="xlm-roberta-vi",
    version="base",
    max_length=512
)

# Tạo embedding cho văn bản tiếng Việt và tiếng Anh
vi_embeddings = xlm_roberta_vi.encode("Tiếng Việt là một ngôn ngữ tuyệt vời.")
en_embeddings = xlm_roberta_vi.encode("Vietnamese is a wonderful language.")
```

### Multilingual-E5
**Mô tả**: Mô hình embedding đa ngôn ngữ hiệu suất cao, hỗ trợ tiếng Việt và hơn 100 ngôn ngữ khác.

**Ưu điểm**:
- Hỗ trợ rất nhiều ngôn ngữ
- Hiệu suất cao cho tìm kiếm ngữ nghĩa
- Tốt cho so sánh văn bản đa ngôn ngữ

**Trường hợp sử dụng**:
- Tìm kiếm ngữ nghĩa đa ngôn ngữ
- Hệ thống RAG đa ngôn ngữ
- Ứng dụng toàn cầu cần hỗ trợ tiếng Việt

**Ví dụ**:
```python
from deep_research_core.multilingual import VietnameseEmbeddings

multilingual_e5 = VietnameseEmbeddings(
    model="multilingual-e5",
    version="large",
    max_length=512
)

# Tạo embedding cho văn bản nhiều ngôn ngữ
embeddings = multilingual_e5.encode_batch([
    "Tiếng Việt là một ngôn ngữ tuyệt vời.",
    "Vietnamese is a wonderful language.",
    "El vietnamita es un idioma maravilloso."
])
```

## Xử lý văn bản tiếng Việt

### CompoundProcessor
**Mô tả**: Xử lý từ ghép tiếng Việt, giúp phân tích và hiểu đúng các từ ghép phức tạp.

**Ưu điểm**:
- Xử lý chính xác từ ghép tiếng Việt
- Cải thiện hiểu ngữ nghĩa
- Hỗ trợ phân tích cú pháp

**Trường hợp sử dụng**:
- Phân tích văn bản tiếng Việt
- Xử lý ngôn ngữ tự nhiên tiếng Việt
- Tìm kiếm và trích xuất thông tin

**Ví dụ**:
```python
from deep_research_core.multilingual import CompoundProcessor

compound_processor = CompoundProcessor(
    dictionary_path="vietnamese_compound_dict.json",
    enable_custom_rules=True
)

# Xử lý từ ghép
processed_text = compound_processor.process("Bộ Giáo dục và Đào tạo đã ban hành quy định mới.")
```

### DiacriticProcessor
**Mô tả**: Xử lý dấu tiếng Việt, bao gồm thêm dấu, chuẩn hóa dấu, và xử lý văn bản thiếu dấu.

**Ưu điểm**:
- Thêm dấu cho văn bản tiếng Việt thiếu dấu
- Chuẩn hóa dấu không nhất quán
- Cải thiện chất lượng văn bản đầu vào

**Trường hợp sử dụng**:
- Xử lý văn bản người dùng nhập thiếu dấu
- Chuẩn hóa văn bản từ nhiều nguồn
- Cải thiện chất lượng đầu vào cho mô hình

**Ví dụ**:
```python
from deep_research_core.multilingual import DiacriticProcessor

diacritic_processor = DiacriticProcessor(
    mode="auto",  # hoặc "add", "normalize", "remove"
    model="transformer"  # hoặc "statistical", "rule_based"
)

# Thêm dấu cho văn bản thiếu dấu
with_diacritics = diacritic_processor.add_diacritics("Tieng Viet khong dau rat kho hieu.")

# Chuẩn hóa dấu
normalized = diacritic_processor.normalize("Tiếng Việt với dấu không nhất quán")
```

### DialectProcessor
**Mô tả**: Xử lý phương ngữ tiếng Việt, chuyển đổi giữa các phương ngữ và chuẩn hóa về tiếng Việt chuẩn.

**Ưu điểm**:
- Xử lý các biến thể phương ngữ
- Chuẩn hóa về tiếng Việt chuẩn
- Cải thiện hiểu văn bản từ nhiều vùng miền

**Trường hợp sử dụng**:
- Xử lý văn bản từ nhiều vùng miền
- Ứng dụng phục vụ người dùng toàn quốc
- Phân tích dữ liệu mạng xã hội

**Ví dụ**:
```python
from deep_research_core.multilingual import DialectProcessor

dialect_processor = DialectProcessor(
    target_dialect="standard",  # hoặc "northern", "central", "southern"
    preserve_meaning=True
)

# Chuyển đổi phương ngữ miền Nam sang tiếng Việt chuẩn
standardized = dialect_processor.process("Tui đi chợ mua đồ nè.")
```

## Tối ưu hóa cho tiếng Việt

### PromptOptimizer
**Mô tả**: Tối ưu hóa prompt tiếng Việt để cải thiện chất lượng đầu ra của mô hình ngôn ngữ.

**Ưu điểm**:
- Cải thiện chất lượng đầu ra tiếng Việt
- Tối ưu hóa cho đặc thù ngôn ngữ Việt
- Giảm hallucination trong tiếng Việt

**Trường hợp sử dụng**:
- Tạo nội dung tiếng Việt chất lượng cao
- Ứng dụng chatbot tiếng Việt
- Hệ thống hỏi đáp tiếng Việt

**Ví dụ**:
```python
from deep_research_core.multilingual import VietnamesePromptOptimizer

prompt_optimizer = VietnamesePromptOptimizer(
    optimization_target="quality",  # hoặc "fluency", "accuracy", "formality"
    cultural_context=True
)

# Tối ưu hóa prompt
optimized_prompt = prompt_optimizer.optimize("Viết một bài giới thiệu về văn hóa Việt Nam.")
```

### ModelQuantizer
**Mô tả**: Lượng tử hóa mô hình tiếng Việt để giảm kích thước và tăng tốc độ suy luận.

**Ưu điểm**:
- Giảm kích thước mô hình
- Tăng tốc độ suy luận
- Tối ưu hóa cho thiết bị hạn chế

**Trường hợp sử dụng**:
- Triển khai mô hình tiếng Việt trên thiết bị di động
- Ứng dụng edge computing
- Giảm chi phí triển khai

**Ví dụ**:
```python
from deep_research_core.multilingual import VietnameseModelQuantizer

model_quantizer = VietnameseModelQuantizer(
    quantization_bits=8,  # hoặc 4, 2
    quantization_type="dynamic",  # hoặc "static"
    optimize_for="accuracy"  # hoặc "size", "speed"
)

# Lượng tử hóa mô hình tiếng Việt
quantized_model = model_quantizer.quantize("vietnamese-llm")
```

## Đánh giá mô hình tiếng Việt

### RLEvaluator
**Mô tả**: Đánh giá mô hình RL-tuning tiếng Việt với các metric chuyên biệt cho tiếng Việt.

**Ưu điểm**:
- Đánh giá chuyên sâu cho tiếng Việt
- Metric phù hợp với đặc thù ngôn ngữ
- Phát hiện lỗi đặc trưng tiếng Việt

**Trường hợp sử dụng**:
- Đánh giá mô hình RL-tuning tiếng Việt
- So sánh hiệu suất các mô hình tiếng Việt
- Cải thiện chất lượng mô hình

**Ví dụ**:
```python
from deep_research_core.multilingual import VietnameseRLEvaluator

rl_evaluator = VietnameseRLEvaluator(
    metrics=["fluency", "grammar", "coherence", "cultural_relevance"],
    reference_corpus="vietnamese_reference_corpus.json"
)

# Đánh giá mô hình
evaluation_results = rl_evaluator.evaluate(
    model="vietnamese-rl-model",
    test_dataset="vietnamese_test_dataset.json"
)
```

### BenchmarkDatasets
**Mô tả**: Bộ dữ liệu đánh giá tiếng Việt cho nhiều tác vụ khác nhau.

**Ưu điểm**:
- Đánh giá toàn diện nhiều khía cạnh
- Dữ liệu đa dạng từ nhiều lĩnh vực
- Chuẩn so sánh nhất quán

**Trường hợp sử dụng**:
- Đánh giá mô hình tiếng Việt
- So sánh hiệu suất các mô hình
- Nghiên cứu cải tiến mô hình

**Ví dụ**:
```python
from deep_research_core.multilingual import VietnameseBenchmark

benchmark = VietnameseBenchmark(
    benchmark_name="vib-1",  # Vietnamese Instruction Benchmark
    tasks=["summarization", "qa", "classification", "generation"]
)

# Đánh giá mô hình trên benchmark
benchmark_results = benchmark.evaluate_model("vietnamese-llm")
```

## Giao diện thống nhất cho xử lý tiếng Việt

Deep Research Core cung cấp giao diện thống nhất `VietnameseProcessor` để dễ dàng tích hợp các thành phần xử lý tiếng Việt:

```python
from deep_research_core.multilingual import VietnameseProcessor

# Khởi tạo Vietnamese Processor với cấu hình đầy đủ
vietnamese_processor = VietnameseProcessor(
    # Cấu hình embedding
    embedding_model="phobert",  # hoặc "viebert", "xlm-roberta-vi", "multilingual-e5"
    embedding_version="base",  # hoặc "large"
    
    # Cấu hình xử lý văn bản
    use_compound_processing=True,
    use_diacritic_processing=True,
    diacritic_mode="auto",
    use_dialect_processing=True,
    target_dialect="standard",
    
    # Cấu hình tối ưu hóa
    use_prompt_optimization=True,
    optimization_target="quality",
    
    # Cấu hình đánh giá
    evaluation_metrics=["fluency", "grammar", "coherence"]
)

# Xử lý văn bản tiếng Việt
processed_text = vietnamese_processor.process_text("Tieng Viet khong dau can xu ly.")

# Tạo embedding
embeddings = vietnamese_processor.create_embeddings([
    "Câu tiếng Việt thứ nhất",
    "Câu tiếng Việt thứ hai"
])

# Tối ưu hóa prompt
optimized_prompt = vietnamese_processor.optimize_prompt("Viết một bài thơ về Hà Nội.")

# Đánh giá văn bản
evaluation = vietnamese_processor.evaluate_text("Văn bản tiếng Việt cần đánh giá.")
```

## Ví dụ sử dụng VietnameseProcessor trong các module khác

### Sử dụng với RAG
```python
from deep_research_core.reasoning import RAG
from deep_research_core.vector_store import SQLiteVectorStore
from deep_research_core.multilingual import VietnameseProcessor

# Khởi tạo Vietnamese Processor
vietnamese_processor = VietnameseProcessor(
    embedding_model="phobert",
    use_diacritic_processing=True
)

# Khởi tạo RAG với hỗ trợ tiếng Việt
vietnamese_rag = RAG(
    vector_store=SQLiteVectorStore("vietnamese_documents.db"),
    embedding_model=vietnamese_processor.get_embedding_model(),
    provider="openai",
    model="gpt-4",
    text_processor=vietnamese_processor
)

# Thêm tài liệu tiếng Việt
vietnamese_rag.add_documents("path/to/vietnamese_documents")

# Truy vấn bằng tiếng Việt
result = vietnamese_rag.generate_reasoning("Tóm tắt các chính sách mới về thuế")
```

### Sử dụng với ToT
```python
from deep_research_core.reasoning import TreeOfThought
from deep_research_core.multilingual import VietnameseProcessor

# Khởi tạo Vietnamese Processor
vietnamese_processor = VietnameseProcessor(
    use_prompt_optimization=True,
    optimization_target="reasoning"
)

# Khởi tạo ToT với hỗ trợ tiếng Việt
vietnamese_tot = TreeOfThought(
    provider="openai",
    model="gpt-4",
    max_branches=3,
    max_depth=3,
    text_processor=vietnamese_processor
)

# Sử dụng ToT với tiếng Việt
result = vietnamese_tot.generate_reasoning("Phân tích các giải pháp cho vấn đề ô nhiễm môi trường tại Việt Nam")
```

### Sử dụng với Multi-Agent
```python
from deep_research_core.agent_system import AgentSystem
from deep_research_core.agents import ResearcherAgent, SynthesizerAgent
from deep_research_core.multilingual import VietnameseProcessor

# Khởi tạo Vietnamese Processor
vietnamese_processor = VietnameseProcessor(
    embedding_model="phobert",
    use_compound_processing=True,
    use_prompt_optimization=True
)

# Khởi tạo Agent System với hỗ trợ tiếng Việt
agent_system = AgentSystem(
    provider="openai",
    model="gpt-4",
    text_processor=vietnamese_processor
)

# Thêm các agent
agent_system.add_agent("researcher", ResearcherAgent())
agent_system.add_agent("synthesizer", SynthesizerAgent())

# Cấu hình multi-agent framework
agent_system.configure_framework(
    role_specialization=True,
    task_decomposition=True,
    shared_memory=True,
    consensus_mechanism="bayesian"
)

# Thực thi nhiệm vụ bằng tiếng Việt
result = agent_system.execute_task("Phân tích tác động của biến đổi khí hậu đến nông nghiệp Việt Nam")
```

## Cây quyết định cho việc lựa chọn thành phần hỗ trợ tiếng Việt

Để chọn thành phần hỗ trợ tiếng Việt phù hợp, hãy trả lời các câu hỏi sau:

1. **Bạn cần xử lý văn bản tiếng Việt ở mức độ nào?**
   - **Cơ bản** → Sử dụng `VietnameseProcessor` với cấu hình mặc định
   - **Nâng cao** → Tiếp tục với câu hỏi 2

2. **Bạn cần embedding cho tiếng Việt?**
   - **Chỉ tiếng Việt** → Sử dụng `PhoBERT` hoặc `VieBERT`
   - **Đa ngôn ngữ có tiếng Việt** → Sử dụng `XLM-RoBERTa-Vi` hoặc `Multilingual-E5`

3. **Bạn cần xử lý vấn đề gì với văn bản tiếng Việt?**
   - **Từ ghép phức tạp** → Thêm `CompoundProcessor`
   - **Văn bản thiếu dấu** → Thêm `DiacriticProcessor`
   - **Phương ngữ địa phương** → Thêm `DialectProcessor`
   - **Tất cả vấn đề trên** → Sử dụng `VietnameseProcessor` đầy đủ

4. **Bạn cần tối ưu hóa cho thiết bị hạn chế?**
   - **Có** → Thêm `VietnameseModelQuantizer`
   - **Không** → Không cần thêm

5. **Bạn cần đánh giá mô hình tiếng Việt?**
   - **Đánh giá toàn diện** → Sử dụng `VietnameseBenchmark`
   - **Đánh giá mô hình RL** → Sử dụng `VietnameseRLEvaluator`
   - **Không cần đánh giá** → Không cần thêm
