graph TD
    %% Main categories
    A[Đ<PERSON>nh dạng suy luận] --> B[1. <PERSON><PERSON><PERSON> dạng suy luận cơ bản]
    A --> C[2. <PERSON><PERSON><PERSON> dạng suy luận nâng cao]
    A --> D[3. <PERSON><PERSON><PERSON> dạng suy luận kết hợp]
    
    %% Basic reasoning formats
    B --> B1[1.1 Chain of Thought]
    B --> B2[1.2 Tree of Thought]
    B --> B3[1.3 ReAct]
    B --> B4[1.4 RAG]
    
    %% Advanced reasoning formats
    C --> C1[2.1 Graph of Thoughts]
    C --> C2[2.2 Multi-Stage Reasoning]
    C --> C3[2.3 Adaptive Chain of Thought]
    
    %% Combined reasoning formats
    D --> D1[3.1 CoT-RAG]
    D --> D2[3.2 ToT-RAG]
    D --> D3[3.3 Multi-query ToT-RAG]
    D --> D4[3.4 Enhanced Source Attribution RAG]
    
    %% Relationships and extensions
    B1 -.-> C3
    B2 -.-> C1
    B1 -.-> D1
    B2 -.-> D2
    B4 -.-> D1
    B4 -.-> D2
    D2 -.-> D3
    B4 -.-> D4
    
    %% Styling
    classDef basic fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef advanced fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px
    classDef combined fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef main fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px
    
    class A main
    class B,B1,B2,B3,B4 basic
    class C,C1,C2,C3 advanced
    class D,D1,D2,D3,D4 combined
