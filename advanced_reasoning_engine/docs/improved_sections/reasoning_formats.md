# Định dạng suy luận (Reasoning Formats)

Deep Research Core cung cấp nhiều định dạng suy luận khác <PERSON>hau, mỗi định dạng có ưu điểm và trường hợp sử dụng riêng. Dưới đây là hệ thống phân cấp rõ ràng của các định dạng suy luận:

## Định dạng suy luận cơ bản

### Chain of Thought (CoT)
**Mô tả**: Suy luận từng bước tuần tự, giúp mô hình giải quyết vấn đề phức tạp bằng cách chia nhỏ thành các bước trung gian.

**Ưu điểm**:
- D<PERSON> hiểu và theo dõi quá trình suy luận
- Hiệu qu<PERSON> cho các vấn đề cần suy luận tuần tự
- Cải thiện độ chính xác cho các vấn đề toán học và logic

**Tr<PERSON><PERSON><PERSON> hợp sử dụng**:
- <PERSON><PERSON><PERSON><PERSON> quyết bài toán toán học
- <PERSON><PERSON> luận logic tuần tự
- Giải thích khái niệm phức tạp

**Ví dụ**:
```python
from deep_research_core.reasoning import ChainOfThought

cot = ChainOfThought(
    provider="openai",
    model="gpt-4",
    verbose=True
)

result = cot.generate_reasoning("Tính tổng của 17 và 28, sau đó nhân với 3")
# Output sẽ hiển thị từng bước suy luận:
# Bước 1: Tính tổng của 17 và 28. 17 + 28 = 45
# Bước 2: Nhân 45 với 3. 45 * 3 = 135
# Kết quả: 135
```

### Tree of Thought (ToT)
**Mô tả**: Khám phá nhiều đường dẫn suy luận khác nhau, đánh giá từng đường dẫn, và chọn đường dẫn tốt nhất.

**Ưu điểm**:
- Khám phá nhiều hướng giải quyết vấn đề
- Tránh bị mắc kẹt ở các đường dẫn suy luận không hiệu quả
- Cải thiện độ chính xác cho các vấn đề phức tạp

**Trường hợp sử dụng**:
- Giải quyết vấn đề có nhiều hướng tiếp cận
- Các bài toán tìm kiếm và quy hoạch
- Phân tích đa chiều

**Ví dụ**:
```python
from deep_research_core.reasoning import TreeOfThought

tot = TreeOfThought(
    provider="openai",
    model="gpt-4",
    max_branches=3,
    max_depth=3,
    verbose=True
)

result = tot.generate_reasoning("Đề xuất 3 chiến lược marketing khác nhau cho sản phẩm mới")
# Output sẽ hiển thị cây suy luận với nhiều nhánh khác nhau
```

### ReAct (Reasoning and Acting)
**Mô tả**: Kết hợp suy luận với hành động, cho phép mô hình tương tác với môi trường bên ngoài.

**Ưu điểm**:
- Kết hợp suy luận với hành động thực tế
- Có thể sử dụng công cụ bên ngoài
- Thích hợp cho các tác vụ tương tác

**Trường hợp sử dụng**:
- Trợ lý ảo tương tác
- Tự động hóa quy trình
- Tìm kiếm và truy xuất thông tin

**Ví dụ**:
```python
from deep_research_core.reasoning import ReAct
from deep_research_core.tools import SearchTool, CalculatorTool

react = ReAct(
    provider="openai",
    model="gpt-4",
    tools=[SearchTool(), CalculatorTool()],
    verbose=True
)

result = react.generate_reasoning("Tìm kiếm GDP của Việt Nam năm 2022 và tính tăng trưởng so với 2021")
# Output sẽ hiển thị quá trình suy luận và sử dụng công cụ
```

### RAG (Retrieval-Augmented Generation)
**Mô tả**: Tăng cường sinh văn bản bằng cách truy xuất thông tin liên quan từ cơ sở dữ liệu.

**Ưu điểm**:
- Cải thiện độ chính xác bằng cách sử dụng thông tin bên ngoài
- Giảm hallucination
- Cung cấp thông tin cập nhật

**Trường hợp sử dụng**:
- Hỏi đáp dựa trên tài liệu
- Tổng hợp thông tin từ nhiều nguồn
- Cập nhật thông tin cho mô hình

**Ví dụ**:
```python
from deep_research_core.reasoning import RAG
from deep_research_core.vector_store import SQLiteVectorStore

rag = RAG(
    vector_store=SQLiteVectorStore("documents.db"),
    embedding_model="all-MiniLM-L6-v2",
    provider="openai",
    model="gpt-4",
    verbose=True
)

# Thêm tài liệu
rag.add_documents("path/to/documents")

result = rag.generate_reasoning("Tóm tắt các chính sách mới về thuế trong tài liệu")
# Output sẽ hiển thị thông tin được truy xuất và câu trả lời
```

## Định dạng suy luận kết hợp

### CoT-RAG
**Mô tả**: Kết hợp Chain of Thought với RAG, cho phép suy luận từng bước dựa trên thông tin được truy xuất.

**Ưu điểm**:
- Suy luận rõ ràng, từng bước dựa trên tài liệu
- Dễ theo dõi và kiểm tra
- Phù hợp cho các vấn đề cần giải thích chi tiết

**Trường hợp sử dụng**:
- Giải thích khái niệm phức tạp dựa trên tài liệu
- Phân tích tuần tự các dữ liệu
- Hướng dẫn từng bước dựa trên tài liệu

**Ví dụ**:
```python
from deep_research_core.reasoning import CoTRAG

cot_rag = CoTRAG(
    vector_store=SQLiteVectorStore("documents.db"),
    embedding_model="all-MiniLM-L6-v2",
    provider="openai",
    model="gpt-4",
    verbose=True
)

result = cot_rag.generate_reasoning("Giải thích quá trình quang hợp dựa trên tài liệu khoa học")
# Output sẽ hiển thị từng bước suy luận dựa trên tài liệu
```

### ToT-RAG
**Mô tả**: Kết hợp Tree of Thought với RAG, cho phép khám phá nhiều đường dẫn suy luận dựa trên thông tin được truy xuất.

**Ưu điểm**:
- Khám phá nhiều hướng tiếp cận dựa trên tài liệu
- Đánh giá và so sánh các hướng tiếp cận khác nhau
- Phù hợp cho các vấn đề phức tạp, đa chiều

**Trường hợp sử dụng**:
- Phân tích các giải pháp cho vấn đề phức tạp
- So sánh nhiều phương pháp khác nhau
- Đánh giá đa chiều dựa trên tài liệu

**Ví dụ**:
```python
from deep_research_core.reasoning import ToTRAG

tot_rag = ToTRAG(
    vector_store=SQLiteVectorStore("documents.db"),
    embedding_model="all-MiniLM-L6-v2",
    provider="openai",
    model="gpt-4",
    max_branches=3,
    max_depth=3,
    verbose=True
)

result = tot_rag.generate_reasoning("Phân tích các giải pháp cho biến đổi khí hậu dựa trên nghiên cứu gần đây")
# Output sẽ hiển thị cây suy luận với nhiều nhánh dựa trên tài liệu
```

### Multi-query ToT-RAG
**Mô tả**: Mở rộng ToT-RAG bằng cách phân tách truy vấn thành nhiều truy vấn con, giúp truy xuất thông tin toàn diện hơn.

**Ưu điểm**:
- Truy xuất thông tin toàn diện hơn
- Phù hợp cho các câu hỏi phức tạp, đa khía cạnh
- Cải thiện độ bao phủ của thông tin

**Trường hợp sử dụng**:
- Nghiên cứu toàn diện về một chủ đề
- Phân tích đa khía cạnh
- Câu hỏi phức tạp cần nhiều loại thông tin

**Ví dụ**:
```python
from deep_research_core.reasoning import MultiQueryToTRAG

mq_tot_rag = MultiQueryToTRAG(
    vector_store=SQLiteVectorStore("documents.db"),
    embedding_model="all-MiniLM-L6-v2",
    provider="openai",
    model="gpt-4",
    max_branches=3,
    max_depth=3,
    max_queries=5,
    verbose=True
)

result = mq_tot_rag.generate_reasoning("Phân tích tác động của AI đến kinh tế, xã hội, và đạo đức")
# Output sẽ hiển thị các truy vấn con và cây suy luận cho từng khía cạnh
```

### Enhanced Source Attribution RAG
**Mô tả**: Mở rộng RAG với khả năng trích dẫn nguồn chính xác, giúp tăng độ tin cậy của thông tin.

**Ưu điểm**:
- Trích dẫn nguồn chính xác
- Tăng độ tin cậy của thông tin
- Dễ dàng kiểm tra và xác minh

**Trường hợp sử dụng**:
- Báo cáo nghiên cứu cần trích dẫn
- Phân tích pháp lý cần dẫn chiếu
- Tổng hợp thông tin từ nhiều nguồn

**Ví dụ**:
```python
from deep_research_core.reasoning import EnhancedSourceAttributionRAG

esa_rag = EnhancedSourceAttributionRAG(
    vector_store=SQLiteVectorStore("documents.db"),
    embedding_model="all-MiniLM-L6-v2",
    provider="openai",
    model="gpt-4",
    verbose=True
)

result = esa_rag.generate_reasoning("Tổng hợp các nghiên cứu gần đây về vaccine COVID-19")
# Output sẽ hiển thị thông tin với trích dẫn nguồn chính xác
```

## Cây quyết định cho việc lựa chọn định dạng suy luận

Để chọn định dạng suy luận phù hợp, hãy trả lời các câu hỏi sau:

1. **Bạn cần truy xuất thông tin từ tài liệu?**
   - **Có** → Tiếp tục với câu hỏi 2
   - **Không** → Tiếp tục với câu hỏi 5

2. **Bạn cần khám phá nhiều hướng suy luận?**
   - **Có** → Tiếp tục với câu hỏi 3
   - **Không** → Tiếp tục với câu hỏi 4

3. **Bạn cần phân tách truy vấn thành nhiều phần?**
   - **Có** → Sử dụng **Multi-query ToT-RAG**
   - **Không** → Sử dụng **ToT-RAG**

4. **Bạn cần trích dẫn nguồn chính xác?**
   - **Có** → Sử dụng **Enhanced Source Attribution RAG**
   - **Không** → Sử dụng **CoT-RAG**

5. **Bạn cần thực hiện hành động?**
   - **Có** → Sử dụng **ReAct**
   - **Không** → Tiếp tục với câu hỏi 6

6. **Bạn cần khám phá nhiều hướng suy luận?**
   - **Có** → Sử dụng **ToT**
   - **Không** → Sử dụng **CoT**

## So sánh các định dạng suy luận

| Định dạng suy luận | Độ phức tạp | Thời gian xử lý | Độ chính xác | Trường hợp sử dụng |
|--------------------|-------------|----------------|--------------|-------------------|
| CoT | Thấp | Nhanh | Trung bình | Suy luận tuần tự, giải thích từng bước |
| ToT | Cao | Chậm | Cao | Vấn đề phức tạp, nhiều hướng tiếp cận |
| ReAct | Trung bình | Trung bình | Cao | Tương tác với công cụ bên ngoài |
| RAG | Trung bình | Trung bình | Cao | Truy xuất thông tin từ tài liệu |
| CoT-RAG | Trung bình | Trung bình | Cao | Giải thích từng bước dựa trên tài liệu |
| ToT-RAG | Cao | Chậm | Rất cao | Phân tích đa chiều dựa trên tài liệu |
| Multi-query ToT-RAG | Rất cao | Rất chậm | Rất cao | Phân tích toàn diện, đa khía cạnh |
| Enhanced Source Attribution RAG | Cao | Trung bình | Cao | Báo cáo cần trích dẫn chính xác |

## Ví dụ so sánh

### CoT-RAG vs ToT-RAG

**Câu hỏi**: "Giải thích quá trình quang hợp"

**CoT-RAG** phù hợp hơn vì:
- Quá trình quang hợp là một quy trình tuần tự, rõ ràng
- Cần giải thích từng bước một cách rõ ràng
- Không cần khám phá nhiều hướng tiếp cận khác nhau

**Câu hỏi**: "Phân tích các giải pháp cho biến đổi khí hậu"

**ToT-RAG** phù hợp hơn vì:
- Có nhiều giải pháp khác nhau cần được khám phá
- Cần đánh giá ưu nhược điểm của từng giải pháp
- Cần so sánh các giải pháp để đưa ra khuyến nghị

### Multi-query ToT-RAG vs Enhanced Source Attribution RAG

**Câu hỏi**: "Phân tích tác động của AI đến kinh tế, xã hội, và đạo đức"

**Multi-query ToT-RAG** phù hợp hơn vì:
- Câu hỏi có nhiều khía cạnh (kinh tế, xã hội, đạo đức)
- Cần phân tách thành nhiều truy vấn con
- Cần khám phá nhiều hướng tiếp cận cho mỗi khía cạnh

**Câu hỏi**: "Tổng hợp các nghiên cứu gần đây về vaccine COVID-19"

**Enhanced Source Attribution RAG** phù hợp hơn vì:
- Cần trích dẫn chính xác từ các nghiên cứu
- Độ tin cậy của thông tin rất quan trọng
- Cần dễ dàng kiểm tra và xác minh nguồn
