graph TD
    A[Bắt đầu] --> B{Cần thông tin từ<br>tài liệu bên ngoài?}
    B -->|Có| C{Cần trích dẫn<br>nguồn chính xác?}
    B -->|Không| J{Cần tương tác với<br>công cụ bên ngoài?}
    
    C -->|Có| D[Enhanced Source<br>Attribution RAG]
    C -->|Không| E{Câu hỏi có nhiều<br>khía cạnh?}
    
    E -->|Có| F{Cần phân tích toàn diện<br>với nhiều truy vấn con?}
    E -->|Không| G{Cần giải thích<br>từng bước?}
    
    F -->|Có| H[Multi-query ToT-RAG]
    F -->|Không| I[ToT-RAG]
    
    G -->|Có| K[CoT-RAG]
    G -->|Không| L{Cần đơn giản hóa<br>quá trình?}
    
    L -->|Có| M[RAG]
    L -->|Không| K
    
    J -->|Có| N[ReAct]
    J -->|Không| O{Vấn đề có nhiều<br>hướng tiếp cận?}
    
    O -->|Có| P{Vấn đề có cấu trúc phức tạp<br>với nhiều phụ thuộc?}
    O -->|Không| Q{Vấn đề có độ phức tạp<br>thay đổi?}
    
    P -->|Có| R[Graph of Thoughts]
    P -->|Không| S[Tree of Thought]
    
    Q -->|Có| T[Adaptive Chain<br>of Thought]
    Q -->|Không| U[Chain of Thought]
    
    %% Styling
    classDef default fill:#f9f9f9,stroke:#333,stroke-width:1px
    classDef question fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
    classDef answer fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px
    classDef start fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px
    
    class A start
    class B,C,E,F,G,J,L,O,P,Q question
    class D,H,I,K,M,N,R,S,T,U answer
