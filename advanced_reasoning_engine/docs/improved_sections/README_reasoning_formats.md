# <PERSON><PERSON> thống phân cấp định dạng suy luận - Task 13.2.1

T<PERSON><PERSON> mục này chứa các tài liệu liên quan đến Task 13.2.1: "Tạ<PERSON> hệ thống phân cấp rõ ràng cho các định dạng suy luận".

## <PERSON><PERSON><PERSON> đích

Mục đích của task này là tạo một hệ thống phân cấp rõ ràng cho các định dạng suy luận trong Deep Research Core, giúp người dùng hiểu rõ sự khác biệt giữa các định dạng và lựa chọn định dạng phù hợp cho nhu cầu của mình.

## Các tài liệu

1. **[reasoning_formats_summary.md](reasoning_formats_summary.md)** - Tổng quan về hệ thống phân cấp định dạng suy luận và hướng dẫn sử dụng các tài liệu khác.

2. **[reasoning_formats_hierarchy.md](reasoning_formats_hierarchy.md)** - <PERSON><PERSON> tả chi tiết về cấu trúc phân cấp của các định dạng suy luận, từ cơ bản đến nâng cao và kết hợp.

3. **[reasoning_formats_hierarchy.mermaid](reasoning_formats_hierarchy.mermaid)** - Biểu đồ Mermaid thể hiện cấu trúc phân cấp của các định dạng suy luận.

4. **[reasoning_format_decision_tree.mermaid](reasoning_format_decision_tree.mermaid)** - Biểu đồ cây quyết định giúp người dùng lựa chọn định dạng suy luận phù hợp.

5. **[reasoning_formats_comparison.md](reasoning_formats_comparison.md)** - Bảng so sánh chi tiết giữa các định dạng suy luận, bao gồm ưu điểm, nhược điểm và trường hợp sử dụng.

6. **[reasoning_formats_differences.md](reasoning_formats_differences.md)** - Giải thích sự khác biệt giữa các định dạng suy luận tương tự như ToT-RAG vs CoT-RAG, ToT-RAG vs Multi-query ToT-RAG, và Multi-query ToT-RAG vs Enhanced Source Attribution RAG.

## Cách sử dụng

1. Bắt đầu với **[reasoning_formats_summary.md](reasoning_formats_summary.md)** để có cái nhìn tổng quan về hệ thống phân cấp và các tài liệu có sẵn.

2. Sử dụng **[reasoning_format_decision_tree.mermaid](reasoning_format_decision_tree.mermaid)** để xác định định dạng suy luận phù hợp nhất cho nhu cầu cụ thể.

3. Tham khảo **[reasoning_formats_comparison.md](reasoning_formats_comparison.md)** để hiểu chi tiết về ưu điểm và nhược điểm của mỗi định dạng.

4. Đọc **[reasoning_formats_differences.md](reasoning_formats_differences.md)** nếu bạn đang cân nhắc giữa các định dạng có vẻ giống nhau.

## Hiển thị biểu đồ Mermaid

Các file `.mermaid` có thể được hiển thị bằng cách:

1. Sử dụng plugin Mermaid trong các trình soạn thảo như VS Code
2. Sử dụng công cụ trực tuyến như [Mermaid Live Editor](https://mermaid.live/)
3. Nhúng vào tài liệu Markdown với cú pháp:

```markdown
```mermaid
(nội dung file .mermaid)
```
```

## Cập nhật

Các tài liệu này nên được cập nhật khi có thêm định dạng suy luận mới hoặc khi các định dạng hiện tại được cải tiến.
