# Kết hợp module phổ biến

Deep Research Core cho phép kết hợp các module khác nhau để tạo ra các hệ thống AI mạnh mẽ. Thay vì liệt kê tất cả các kết hợp có thể (có thể lên đến hàng trăm), tài liệu này tập trung vào các kết hợp phổ biến và hữu ích nhất.

## Kết hợp phổ biến giữa các module chính

Dưới đây là các kết hợp phổ biến nhất giữa các module chính, cùng với chức năng và ứng dụng của chúng:

| Module 1 | Module 2 | Chức năng kết hợp | Ứng dụng | Ví dụ mã nguồn |
|----------|----------|-------------------|----------|----------------|
| **Reasoning** | **RAG** | Suy luận dựa trên thông tin truy xuất | Hỏi đáp dựa trên tài liệu | `reasoning/rag_reasoning_example.py` |
| **Reasoning** | **Multi-Agent** | Suy luận đa chiều với nhiều agent | Giải quyết vấn đề phức tạp | `reasoning/multi_agent_reasoning_example.py` |
| **Reasoning** | **Tools** | Suy luận kết hợp với hành động | Trợ lý thông minh | `reasoning/react_tools_example.py` |
| **Multi-Agent** | **RAG** | Nhiều agent truy xuất và phân tích thông tin | Phân tích tài liệu đa chiều | `multi_agent/rag_agents_example.py` |
| **RAG** | **Tools** | RAG với khả năng sử dụng công cụ | RAG chủ động | `rag/tool_enhanced_rag_example.py` |
| **RL-Tuning** | **Reasoning** | Suy luận tự cải thiện | Mô hình thích ứng | `rl_tuning/reasoning_rl_example.py` |
| **Multilingual** | **RAG** | RAG đa ngôn ngữ | RAG tiếng Việt | `multilingual/vietnamese_rag_example.py` |
| **Optimization** | **Reasoning** | Suy luận tối ưu hóa | Suy luận hiệu quả | `optimization/optimized_reasoning_example.py` |
| **Web** | **Reasoning** | Suy luận với giao diện web | Nền tảng hỏi đáp web | `web/reasoning_web_example.py` |
| **Security** | **RAG** | RAG bảo mật | RAG với kiểm soát truy cập | `security/secure_rag_example.py` |

## Template cho các kết hợp phổ biến

### 1. RAG + Reasoning (CoT/ToT)

Kết hợp RAG với Chain of Thought hoặc Tree of Thought để tạo hệ thống hỏi đáp nâng cao.

```python
from deep_research_core.reasoning import RAG, ChainOfThought, TreeOfThought
from deep_research_core.vector_store import SQLiteVectorStore

# Khởi tạo RAG
rag = RAG(
    vector_store=SQLiteVectorStore("documents.db"),
    embedding_model="all-MiniLM-L6-v2",
    provider="openai",
    model="gpt-4"
)

# Thêm tài liệu
rag.add_documents("path/to/documents")

# Kết hợp với CoT
def rag_cot(query):
    # Truy xuất tài liệu
    documents = rag.retrieve_documents(query, top_k=3)
    context = "\n\n".join([doc.content for doc in documents])
    
    # Sử dụng CoT với context
    cot = ChainOfThought(
        provider="openai",
        model="gpt-4"
    )
    
    # Tạo prompt với context
    prompt = f"Dựa trên thông tin sau:\n\n{context}\n\nHãy trả lời câu hỏi: {query}"
    
    # Suy luận với CoT
    result = cot.generate_reasoning(prompt)
    return result

# Kết hợp với ToT
def rag_tot(query):
    # Truy xuất tài liệu
    documents = rag.retrieve_documents(query, top_k=5)
    context = "\n\n".join([doc.content for doc in documents])
    
    # Sử dụng ToT với context
    tot = TreeOfThought(
        provider="openai",
        model="gpt-4",
        max_branches=3,
        max_depth=3
    )
    
    # Tạo prompt với context
    prompt = f"Dựa trên thông tin sau:\n\n{context}\n\nHãy trả lời câu hỏi: {query}"
    
    # Suy luận với ToT
    result = tot.generate_reasoning(prompt)
    return result
```

### 2. Multi-Agent System

Tạo hệ thống multi-agent với các agent chuyên biệt và cơ chế đồng thuận.

```python
from deep_research_core.agent_system import AgentSystem
from deep_research_core.agents import ResearcherAgent, SynthesizerAgent, OrchestratorAgent
from deep_research_core.tools import SearchTool, DocumentTool

# Khởi tạo hệ thống agent
agent_system = AgentSystem(
    provider="openai",
    model="gpt-4"
)

# Thêm các agent
agent_system.add_agent("researcher", ResearcherAgent(
    tools=[SearchTool(), DocumentTool()]
))
agent_system.add_agent("synthesizer", SynthesizerAgent())
agent_system.add_agent("orchestrator", OrchestratorAgent())

# Cấu hình multi-agent framework
agent_system.configure_framework(
    role_specialization=True,
    task_decomposition=True,
    shared_memory=True,
    consensus_mechanism="bayesian"  # hoặc "expert-weighted", "voting"
)

# Thực thi nhiệm vụ
result = agent_system.execute_task("Phân tích tác động của AI đến thị trường lao động")
```

### 3. ReAct + Tools

Kết hợp ReAct với các công cụ để tạo hệ thống có khả năng suy luận và thực hiện hành động.

```python
from deep_research_core.reasoning import ReAct
from deep_research_core.tools import SearchTool, CalculatorTool, DatabaseTool, APITool

# Khởi tạo các công cụ
search_tool = SearchTool()
calculator_tool = CalculatorTool()
database_tool = DatabaseTool(connection_string="sqlite:///database.db")
api_tool = APITool(base_url="https://api.example.com")

# Khởi tạo ReAct với các công cụ
react = ReAct(
    provider="openai",
    model="gpt-4",
    tools=[search_tool, calculator_tool, database_tool, api_tool]
)

# Sử dụng ReAct
result = react.generate_reasoning("Tìm kiếm GDP của Việt Nam năm 2022, tính tăng trưởng so với 2021, và lưu kết quả vào cơ sở dữ liệu")
```

### 4. RL-Tuning

Tối ưu hóa mô hình bằng học tăng cường.

```python
from deep_research_core.rl_tuning import RLTuner, AgentEnvironment, TrajectoryCollector
from deep_research_core.reasoning import ReAct

# Khởi tạo môi trường và thu thập quỹ đạo
environment = AgentEnvironment(
    task_type="conversation",
    reward_type="human_feedback"
)

trajectory_collector = TrajectoryCollector(
    environment=environment,
    num_trajectories=1000
)

# Thu thập quỹ đạo
trajectories = trajectory_collector.collect()

# Khởi tạo RL Tuner
rl_tuner = RLTuner(
    base_model="gpt-3.5-turbo",
    method="ppo",  # hoặc "dpo", "sft", "grpo"
    learning_rate=1e-5,
    batch_size=8
)

# Huấn luyện mô hình
tuned_model = rl_tuner.train(
    trajectories=trajectories,
    num_epochs=3
)

# Sử dụng mô hình đã huấn luyện
react = ReAct(
    provider="custom",
    model=tuned_model
)

result = react.generate_reasoning("Gợi ý một số hoạt động cuối tuần ở Hà Nội")
```

### 5. Vietnamese Support

Tạo hệ thống hỗ trợ tiếng Việt.

```python
from deep_research_core.multilingual import VietnameseProcessor, VietnameseEmbeddings
from deep_research_core.reasoning import RAG
from deep_research_core.vector_store import SQLiteVectorStore

# Khởi tạo Vietnamese Processor
vietnamese_processor = VietnameseProcessor(
    use_compound_processing=True,
    use_diacritic_processing=True,
    use_dialect_processing=True
)

# Khởi tạo Vietnamese Embeddings
vietnamese_embeddings = VietnameseEmbeddings(
    model="phobert"  # hoặc "viebert", "xlm-roberta-vi", "multilingual-e5"
)

# Khởi tạo RAG với hỗ trợ tiếng Việt
vietnamese_rag = RAG(
    vector_store=SQLiteVectorStore("vietnamese_documents.db"),
    embedding_model=vietnamese_embeddings,
    provider="openai",
    model="gpt-4",
    text_processor=vietnamese_processor
)

# Thêm tài liệu tiếng Việt
vietnamese_rag.add_documents("path/to/vietnamese_documents")

# Truy vấn bằng tiếng Việt
result = vietnamese_rag.generate_reasoning("Phân tích tác động của chính sách mới về thuế đối với doanh nghiệp vừa và nhỏ tại Việt Nam")
```

## Kết hợp phức tạp (3+ module)

Dưới đây là một số kết hợp phức tạp phổ biến, sử dụng 3 module trở lên:

### 1. RAG + ToT + Multi-Agent

Kết hợp RAG, Tree of Thought và Multi-Agent để tạo hệ thống phân tích tài liệu đa chiều.

```python
from deep_research_core.reasoning import RAG, TreeOfThought
from deep_research_core.agent_system import AgentSystem
from deep_research_core.agents import ResearcherAgent, SynthesizerAgent
from deep_research_core.vector_store import SQLiteVectorStore
from deep_research_core.multi_agent import SharedMemory

# Khởi tạo RAG
rag = RAG(
    vector_store=SQLiteVectorStore("documents.db"),
    embedding_model="all-MiniLM-L6-v2",
    provider="openai",
    model="gpt-4"
)

# Khởi tạo ToT
tot = TreeOfThought(
    provider="openai",
    model="gpt-4",
    max_branches=3,
    max_depth=3
)

# Khởi tạo Shared Memory
shared_memory = SharedMemory(storage_type="in_memory", versioning=True)

# Khởi tạo Agent System
agent_system = AgentSystem(
    provider="openai",
    model="gpt-4"
)

# Thêm các agent
agent_system.add_agent("researcher", ResearcherAgent())
agent_system.add_agent("synthesizer", SynthesizerAgent())

# Cấu hình agent system
agent_system.configure_framework(
    shared_memory=shared_memory,
    consensus_mechanism="bayesian"
)

# Thêm tài liệu
rag.add_documents("path/to/documents")

# Hàm phân tích tài liệu đa chiều
def analyze_documents(query):
    # Truy xuất tài liệu
    documents = rag.retrieve_documents(query, top_k=5)
    
    # Lưu tài liệu vào shared memory
    for i, doc in enumerate(documents):
        shared_memory.set(f"document_{i}", doc.content)
    
    # Phân tách nhiệm vụ
    tasks = [
        {"agent": "researcher", "action": "analyze", "params": {"document_ids": [f"document_{i}" for i in range(len(documents))], "aspect": "factual"}},
        {"agent": "researcher", "action": "analyze", "params": {"document_ids": [f"document_{i}" for i in range(len(documents))], "aspect": "implications"}},
        {"agent": "synthesizer", "action": "synthesize", "params": {"memory_keys": ["analysis_factual", "analysis_implications"]}}
    ]
    
    # Thực thi các nhiệm vụ
    for task in tasks:
        result = agent_system.execute_task_for_agent(
            agent_id=task["agent"],
            action=task["action"],
            params=task["params"]
        )
        if task["action"] == "analyze":
            shared_memory.set(f"analysis_{task['params']['aspect']}", result)
    
    # Sử dụng ToT để khám phá nhiều hướng phân tích
    synthesis = shared_memory.get("synthesis")
    tot_result = tot.generate_reasoning(f"Dựa trên phân tích sau:\n\n{synthesis}\n\nHãy khám phá các hướng tiếp cận khác nhau để giải quyết vấn đề: {query}")
    
    return tot_result
```

### 2. RAG + Multilingual + Optimization

Kết hợp RAG, Multilingual và Optimization để tạo hệ thống RAG tiếng Việt hiệu quả.

```python
from deep_research_core.reasoning import RAG
from deep_research_core.multilingual import VietnameseProcessor, VietnameseEmbeddings
from deep_research_core.optimization import ModelQuantizer, CachingStrategy
from deep_research_core.vector_store import SQLiteVectorStore

# Khởi tạo Vietnamese Processor
vietnamese_processor = VietnameseProcessor(
    use_compound_processing=True,
    use_diacritic_processing=True,
    use_dialect_processing=True
)

# Khởi tạo Vietnamese Embeddings
vietnamese_embeddings = VietnameseEmbeddings(
    model="phobert"
)

# Khởi tạo Model Quantizer
model_quantizer = ModelQuantizer(
    bits=8,
    method="dynamic"
)

# Khởi tạo Caching Strategy
caching_strategy = CachingStrategy(
    cache_type="redis",
    ttl=3600
)

# Khởi tạo RAG với hỗ trợ tiếng Việt và tối ưu hóa
optimized_vietnamese_rag = RAG(
    vector_store=SQLiteVectorStore("vietnamese_documents.db"),
    embedding_model=vietnamese_embeddings,
    provider="openai",
    model="gpt-4",
    text_processor=vietnamese_processor,
    model_quantizer=model_quantizer,
    caching_strategy=caching_strategy
)

# Thêm tài liệu tiếng Việt
optimized_vietnamese_rag.add_documents("path/to/vietnamese_documents")

# Truy vấn bằng tiếng Việt
result = optimized_vietnamese_rag.generate_reasoning("Phân tích tác động của chính sách mới về thuế đối với doanh nghiệp vừa và nhỏ tại Việt Nam")
```

### 3. ReAct + Tools + RL-Tuning

Kết hợp ReAct, Tools và RL-Tuning để tạo hệ thống trợ lý thông minh tự cải thiện.

```python
from deep_research_core.reasoning import ReAct
from deep_research_core.tools import SearchTool, CalculatorTool, DatabaseTool
from deep_research_core.rl_tuning import RLTuner, AgentEnvironment, TrajectoryCollector

# Khởi tạo các công cụ
tools = [SearchTool(), CalculatorTool(), DatabaseTool()]

# Khởi tạo ReAct với các công cụ
react = ReAct(
    provider="openai",
    model="gpt-4",
    tools=tools
)

# Khởi tạo môi trường và thu thập quỹ đạo
environment = AgentEnvironment(
    task_type="tool_use",
    reward_type="task_completion"
)

trajectory_collector = TrajectoryCollector(
    environment=environment,
    agent=react,
    num_trajectories=1000
)

# Thu thập quỹ đạo
trajectories = trajectory_collector.collect()

# Khởi tạo RL Tuner
rl_tuner = RLTuner(
    base_model="gpt-4",
    method="ppo",
    learning_rate=1e-5,
    batch_size=8
)

# Huấn luyện mô hình
tuned_model = rl_tuner.train(
    trajectories=trajectories,
    num_epochs=3
)

# Sử dụng mô hình đã huấn luyện
improved_react = ReAct(
    provider="custom",
    model=tuned_model,
    tools=tools
)

result = improved_react.generate_reasoning("Tìm kiếm thông tin về GDP Việt Nam, tính tăng trưởng, và lưu vào cơ sở dữ liệu")
```

## Giao diện thống nhất cho kết hợp module

Deep Research Core cung cấp giao diện `ModuleCombiner` để dễ dàng kết hợp các module:

```python
from deep_research_core.core import ModuleCombiner
from deep_research_core.reasoning import RAG, TreeOfThought
from deep_research_core.multi_agent import TaskDecomposer, SharedMemory
from deep_research_core.tools import SearchTool
from deep_research_core.vector_store import SQLiteVectorStore

# Khởi tạo ModuleCombiner
combiner = ModuleCombiner(
    provider="openai",
    model="gpt-4"
)

# Thêm các module
combiner.add_module("rag", RAG(
    vector_store=SQLiteVectorStore("documents.db"),
    embedding_model="all-MiniLM-L6-v2"
))

combiner.add_module("tot", TreeOfThought(
    max_branches=3,
    max_depth=3
))

combiner.add_module("task_decomposer", TaskDecomposer())

combiner.add_module("shared_memory", SharedMemory(
    storage_type="in_memory",
    versioning=True
))

combiner.add_module("search_tool", SearchTool())

# Cấu hình luồng xử lý
combiner.configure_flow([
    {"module": "task_decomposer", "method": "decompose", "input": "query", "output": "subtasks"},
    {"module": "rag", "method": "retrieve_documents", "input": "query", "output": "documents"},
    {"module": "shared_memory", "method": "set", "input": {"key": "documents", "value": "documents"}},
    {"module": "tot", "method": "generate_reasoning", "input": {"query": "query", "context": "documents"}, "output": "reasoning"},
    {"module": "search_tool", "method": "search", "input": "reasoning", "output": "search_results"},
    {"module": "shared_memory", "method": "set", "input": {"key": "search_results", "value": "search_results"}},
    {"module": "tot", "method": "generate_reasoning", "input": {"query": "query", "context": ["documents", "search_results"]}, "output": "final_result"}
])

# Sử dụng combiner
result = combiner.process("Phân tích tác động của AI đến thị trường lao động")
```

## So sánh hiệu suất các kết hợp module

| Kết hợp module | Độ chính xác | Thời gian xử lý | Tài nguyên | Trường hợp sử dụng |
|----------------|--------------|----------------|------------|-------------------|
| RAG | 80-85% | Nhanh | Thấp | Hỏi đáp dựa trên tài liệu đơn giản |
| RAG + CoT | 85-90% | Trung bình | Trung bình | Hỏi đáp cần giải thích từng bước |
| RAG + ToT | 88-93% | Chậm | Cao | Hỏi đáp cần khám phá nhiều hướng |
| ReAct + Tools | 85-90% | Trung bình | Trung bình | Trợ lý thông minh với khả năng hành động |
| Multi-Agent | 82-87% | Chậm | Cao | Giải quyết vấn đề phức tạp đa lĩnh vực |
| RL-Tuning | 84-89% | Rất chậm (huấn luyện) | Rất cao | Mô hình tự cải thiện theo thời gian |
| Vietnamese Support | 80-85% | Trung bình | Trung bình | Ứng dụng tiếng Việt |
| RAG + ToT + Multi-Agent | 90-95% | Rất chậm | Rất cao | Phân tích tài liệu đa chiều phức tạp |

## Lưu ý khi kết hợp module

1. **Độ phức tạp**: Kết hợp nhiều module sẽ làm tăng độ phức tạp của hệ thống. Chỉ kết hợp các module cần thiết.

2. **Tài nguyên**: Một số kết hợp (như ToT + Multi-Agent) đòi hỏi tài nguyên lớn. Đảm bảo hệ thống của bạn có đủ tài nguyên.

3. **Thời gian xử lý**: Kết hợp phức tạp sẽ làm tăng thời gian xử lý. Cân nhắc sử dụng ParallelInference và CachingStrategies.

4. **Tương thích**: Không phải tất cả các module đều tương thích với nhau. Tham khảo tài liệu để biết các kết hợp được hỗ trợ.

5. **Tùy chỉnh**: Các template chỉ là điểm khởi đầu. Tùy chỉnh các tham số để phù hợp với nhu cầu cụ thể của bạn.
