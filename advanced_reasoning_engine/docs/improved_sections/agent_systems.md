# <PERSON><PERSON> thống Agent (Agent Systems)

Deep Research Core cung cấp một hệ thống agent to<PERSON><PERSON> di<PERSON>n, kết hợp các loại agent chuyên biệt với framework multi-agent để giải quyết các vấn đề phức tạp. D<PERSON><PERSON><PERSON> đây là cấu trúc thống nhất của hệ thống agent:

## <PERSON><PERSON><PERSON> trú<PERSON> hệ thống Agent

```
Agent System
├── Agent Types (Loại Agent)
│   ├── Researcher: <PERSON><PERSON> thập thông tin
│   ├── Synthesizer: Tổng hợp thông tin
│   └── Orchestrator: Điều phối quy trình
└── Multi-Agent Framework
    ├── Role Specialization: Phân công vai trò
    ├── Task Decomposition: Phân tách nhiệm vụ
    ├── Shared Memory: Chia sẻ thông tin
    └── Consensus Mechanisms: Đạt được đồng thuận
        ├── Simple Voting: Bỏ phiếu đơn giản
        ├── Weighted Voting: Bỏ phiếu có trọng số
        ├── Bayesian Consensus: <PERSON><PERSON><PERSON> thuận dựa trên Bayes
        └── Expert-weighted: <PERSON><PERSON><PERSON> thuận dựa trên chuyên gia
```

## Loại Agent (Agent Types)

### Researcher Agent
**M<PERSON> tả**: Agent chuyên biệt về thu thập và phân tích thông tin từ nhiều nguồn.

**Khả năng**:
- Tìm kiếm thông tin từ nhiều nguồn
- Đánh giá độ tin cậy của thông tin
- Trích xuất thông tin quan trọng
- Tổ chức thông tin theo chủ đề

**Trường hợp sử dụng**:
- Nghiên cứu thị trường
- Thu thập dữ liệu khoa học
- Phân tích tài liệu

**Ví dụ**:
```python
from deep_research_core.agents import ResearcherAgent
from deep_research_core.tools import SearchTool, DocumentTool

researcher = ResearcherAgent(
    provider="openai",
    model="gpt-4",
    tools=[SearchTool(), DocumentTool()],
    verbose=True
)

results = researcher.research("Phân tích thị trường smartphone Việt Nam 2023")
# Output sẽ là thông tin được thu thập và phân tích
```

### Synthesizer Agent
**Mô tả**: Agent chuyên biệt về tổng hợp thông tin từ nhiều nguồn thành một báo cáo hoặc tài liệu mạch lạc.

**Khả năng**:
- Tổng hợp thông tin từ nhiều nguồn
- Phát hiện và giải quyết mâu thuẫn
- Tạo báo cáo có cấu trúc
- Trích dẫn nguồn chính xác

**Trường hợp sử dụng**:
- Tạo báo cáo tổng hợp
- Viết tài liệu tổng quan
- Tổng kết kết quả nghiên cứu

**Ví dụ**:
```python
from deep_research_core.agents import SynthesizerAgent

synthesizer = SynthesizerAgent(
    provider="openai",
    model="gpt-4",
    verbose=True
)

report = synthesizer.synthesize(
    research_results=results,
    format="markdown",
    max_length=2000
)
# Output sẽ là báo cáo tổng hợp
```

### Orchestrator Agent
**Mô tả**: Agent chuyên biệt về điều phối quy trình làm việc giữa các agent khác.

**Khả năng**:
- Phân tách nhiệm vụ phức tạp
- Phân công nhiệm vụ cho các agent
- Theo dõi tiến độ
- Tổng hợp kết quả cuối cùng

**Trường hợp sử dụng**:
- Quản lý dự án
- Điều phối quy trình nghiên cứu
- Quản lý quy trình làm việc phức tạp

**Ví dụ**:
```python
from deep_research_core.agents import OrchestratorAgent, ResearcherAgent, SynthesizerAgent

orchestrator = OrchestratorAgent(
    provider="openai",
    model="gpt-4",
    verbose=True
)

# Thêm các agent khác
orchestrator.add_agent("researcher", ResearcherAgent())
orchestrator.add_agent("synthesizer", SynthesizerAgent())

# Thực thi quy trình
result = orchestrator.execute_workflow(
    task="Tạo báo cáo về thị trường smartphone Việt Nam 2023",
    workflow=[
        {"agent": "researcher", "action": "research", "params": {"topic": "thị trường smartphone Việt Nam 2023"}},
        {"agent": "synthesizer", "action": "synthesize", "params": {"format": "markdown", "max_length": 2000}}
    ]
)
# Output sẽ là kết quả cuối cùng của quy trình
```

## Multi-Agent Framework

### Role Specialization (Phân công vai trò)
**Mô tả**: Cơ chế phân công vai trò cho các agent dựa trên khả năng và chuyên môn.

**Ưu điểm**:
- Tận dụng điểm mạnh của từng agent
- Tăng hiệu quả giải quyết vấn đề
- Giảm xung đột giữa các agent

**Trường hợp sử dụng**:
- Hệ thống multi-agent phức tạp
- Giải quyết vấn đề đa lĩnh vực
- Quy trình làm việc đòi hỏi nhiều kỹ năng

**Ví dụ**:
```python
from deep_research_core.multi_agent import RoleSpecialization

role_specialization = RoleSpecialization()

# Định nghĩa vai trò
role_specialization.define_role(
    name="data_analyst",
    skills=["data_processing", "statistical_analysis", "visualization"],
    priority=0.8
)

role_specialization.define_role(
    name="domain_expert",
    skills=["domain_knowledge", "interpretation", "recommendation"],
    priority=0.9
)

# Gán vai trò cho agent
role_specialization.assign_role(agent_id="agent1", role="data_analyst")
role_specialization.assign_role(agent_id="agent2", role="domain_expert")
```

### Task Decomposition (Phân tách nhiệm vụ)
**Mô tả**: Cơ chế phân tách nhiệm vụ phức tạp thành các nhiệm vụ nhỏ hơn, dễ quản lý.

**Ưu điểm**:
- Giảm độ phức tạp của vấn đề
- Dễ dàng phân công cho các agent
- Tăng khả năng giải quyết vấn đề phức tạp

**Trường hợp sử dụng**:
- Vấn đề phức tạp, đa chiều
- Quy trình làm việc phức tạp
- Dự án lớn cần chia nhỏ

**Ví dụ**:
```python
from deep_research_core.multi_agent import TaskDecomposer

task_decomposer = TaskDecomposer(
    provider="openai",
    model="gpt-4",
    verbose=True
)

subtasks = task_decomposer.decompose(
    task="Phân tích tác động của AI đến thị trường lao động Việt Nam",
    max_subtasks=5
)
# Output sẽ là danh sách các nhiệm vụ con
```

### Shared Memory (Chia sẻ thông tin)
**Mô tả**: Cơ chế chia sẻ thông tin giữa các agent, cho phép chúng truy cập và cập nhật thông tin chung.

**Ưu điểm**:
- Chia sẻ thông tin hiệu quả giữa các agent
- Duy trì tính nhất quán của thông tin
- Hỗ trợ phiên bản hóa và lịch sử thay đổi

**Trường hợp sử dụng**:
- Hệ thống multi-agent cần chia sẻ thông tin
- Quy trình làm việc tuần tự giữa các agent
- Dự án đòi hỏi tính nhất quán cao

**Ví dụ**:
```python
from deep_research_core.multi_agent import SharedMemory

shared_memory = SharedMemory(
    storage_type="in_memory",  # hoặc "redis", "sqlite"
    versioning=True
)

# Lưu trữ thông tin
shared_memory.set("market_data", {"date": "2023-12-01", "data": [...]})

# Truy xuất thông tin
market_data = shared_memory.get("market_data")

# Truy vấn nâng cao
query_result = shared_memory.query(
    "Tìm tất cả dữ liệu thị trường từ tháng 10/2023 đến tháng 12/2023"
)
```

### Consensus Mechanisms (Cơ chế đồng thuận)

#### Simple Voting (Bỏ phiếu đơn giản)
**Mô tả**: Mỗi agent có một phiếu bầu, kết quả được chọn dựa trên đa số.

**Ưu điểm**:
- Đơn giản, dễ triển khai
- Công bằng khi các agent có khả năng tương đương
- Dễ hiểu và giải thích

**Trường hợp sử dụng**:
- Quyết định đơn giản
- Các agent có khả năng tương đương
- Cần quyết định nhanh chóng

#### Weighted Voting (Bỏ phiếu có trọng số)
**Mô tả**: Mỗi agent có trọng số khác nhau, kết quả được chọn dựa trên tổng trọng số.

**Ưu điểm**:
- Phản ánh mức độ chuyên môn khác nhau
- Linh hoạt trong việc điều chỉnh trọng số
- Cải thiện chất lượng quyết định

**Trường hợp sử dụng**:
- Các agent có mức độ chuyên môn khác nhau
- Quyết định phức tạp
- Cần tính đến độ tin cậy của từng agent

#### Bayesian Consensus (Đồng thuận dựa trên Bayes)
**Mô tả**: Kết hợp ý kiến từ nhiều agent dựa trên xác suất Bayes, tính đến độ tin cậy của từng agent.

**Ưu điểm**:
- Xử lý tốt thông tin không chắc chắn
- Tính đến độ tin cậy của từng nguồn thông tin
- Cập nhật niềm tin dựa trên bằng chứng mới

**Trường hợp sử dụng**:
- Kết hợp thông tin từ nhiều nguồn với độ tin cậy khác nhau
- Quyết định trong môi trường không chắc chắn
- Cần cập nhật niềm tin theo thời gian

**Ví dụ**:
```python
from deep_research_core.multi_agent import BayesianConsensus

bayesian_consensus = BayesianConsensus()

# Thêm ý kiến từ các agent
bayesian_consensus.add_opinion(
    agent_id="agent1",
    opinion="Thị trường sẽ tăng trưởng 5%",
    confidence=0.7
)

bayesian_consensus.add_opinion(
    agent_id="agent2",
    opinion="Thị trường sẽ tăng trưởng 8%",
    confidence=0.6
)

bayesian_consensus.add_opinion(
    agent_id="agent3",
    opinion="Thị trường sẽ tăng trưởng 3%",
    confidence=0.8
)

# Đạt được đồng thuận
consensus = bayesian_consensus.reach_consensus()
# Output sẽ là ý kiến đồng thuận với độ tin cậy
```

#### Expert-weighted Consensus (Đồng thuận dựa trên chuyên gia)
**Mô tả**: Kết hợp ý kiến từ nhiều agent dựa trên mức độ chuyên môn của họ trong từng lĩnh vực cụ thể.

**Ưu điểm**:
- Tính đến chuyên môn cụ thể trong từng lĩnh vực
- Linh hoạt trong việc đánh giá chuyên môn
- Cải thiện chất lượng quyết định trong các lĩnh vực chuyên biệt

**Trường hợp sử dụng**:
- Quyết định đa lĩnh vực
- Các agent có chuyên môn khác nhau trong từng lĩnh vực
- Cần tận dụng điểm mạnh của từng agent

**Ví dụ**:
```python
from deep_research_core.multi_agent import ExpertWeightedConsensus

expert_consensus = ExpertWeightedConsensus()

# Định nghĩa chuyên môn của các agent
expert_consensus.define_expertise(
    agent_id="agent1",
    expertise={
        "technology": 0.9,
        "finance": 0.5,
        "marketing": 0.3
    }
)

expert_consensus.define_expertise(
    agent_id="agent2",
    expertise={
        "technology": 0.4,
        "finance": 0.9,
        "marketing": 0.6
    }
)

# Thêm ý kiến từ các agent
expert_consensus.add_opinion(
    agent_id="agent1",
    opinion="Nên đầu tư vào AI",
    domain="technology"
)

expert_consensus.add_opinion(
    agent_id="agent2",
    opinion="Nên đầu tư vào Blockchain",
    domain="technology"
)

# Đạt được đồng thuận trong lĩnh vực cụ thể
consensus = expert_consensus.reach_consensus(domain="technology")
# Output sẽ là ý kiến đồng thuận trong lĩnh vực technology
```

## Phân biệt giữa các cơ chế đồng thuận

### Bayesian Consensus vs Expert-weighted Consensus

**Bayesian Consensus** phù hợp khi:
- Cần kết hợp nhiều nguồn thông tin với độ tin cậy khác nhau
- Thông tin có tính không chắc chắn cao
- Cần cập nhật niềm tin dựa trên bằng chứng mới
- Các agent có độ tin cậy khác nhau nhưng không phân biệt theo lĩnh vực

**Expert-weighted Consensus** phù hợp khi:
- Các agent có mức độ chuyên môn khác nhau trong từng lĩnh vực cụ thể
- Quyết định liên quan đến nhiều lĩnh vực khác nhau
- Cần tận dụng điểm mạnh của từng agent trong lĩnh vực họ giỏi nhất
- Cần phân biệt rõ ràng chuyên môn theo từng lĩnh vực

**Ví dụ so sánh**:

Trong một dự án đầu tư, nếu bạn cần đánh giá triển vọng của một công ty công nghệ:

- **Bayesian Consensus** sẽ kết hợp ý kiến từ các chuyên gia dựa trên độ tin cậy chung của họ, không phân biệt lĩnh vực.
- **Expert-weighted Consensus** sẽ đánh giá cao ý kiến của chuyên gia công nghệ về sản phẩm, chuyên gia tài chính về tài chính, và chuyên gia marketing về chiến lược thị trường.

## Giao diện thống nhất cho hệ thống Agent

Deep Research Core cung cấp giao diện thống nhất `AgentSystem` để dễ dàng tạo và quản lý hệ thống agent:

```python
from deep_research_core.agent_system import AgentSystem
from deep_research_core.agents import ResearcherAgent, SynthesizerAgent, OrchestratorAgent

# Khởi tạo hệ thống agent
agent_system = AgentSystem(
    provider="openai",
    model="gpt-4"
)

# Thêm các agent
agent_system.add_agent("researcher", ResearcherAgent())
agent_system.add_agent("synthesizer", SynthesizerAgent())
agent_system.add_agent("orchestrator", OrchestratorAgent())

# Cấu hình multi-agent framework
agent_system.configure_framework(
    role_specialization=True,
    task_decomposition=True,
    shared_memory=True,
    consensus_mechanism="bayesian"  # hoặc "expert-weighted", "voting", "weighted_voting"
)

# Thực thi nhiệm vụ
result = agent_system.execute_task("Phân tích tác động của AI đến giáo dục")

# Truy xuất kết quả chi tiết
detailed_results = agent_system.get_detailed_results()
agent_interactions = agent_system.get_agent_interactions()
consensus_process = agent_system.get_consensus_process()
```

## Cây quyết định cho việc lựa chọn cấu hình Agent System

Để chọn cấu hình Agent System phù hợp, hãy trả lời các câu hỏi sau:

1. **Vấn đề của bạn có phức tạp và đa chiều không?**
   - **Có** → Sử dụng nhiều agent chuyên biệt với TaskDecomposition
   - **Không** → Có thể sử dụng một agent đơn lẻ hoặc cấu hình đơn giản

2. **Bạn cần xử lý nhiều loại thông tin khác nhau không?**
   - **Có** → Sử dụng ResearcherAgent với các công cụ đa dạng
   - **Không** → Có thể bỏ qua ResearcherAgent

3. **Bạn cần tổng hợp thông tin từ nhiều nguồn không?**
   - **Có** → Thêm SynthesizerAgent vào hệ thống
   - **Không** → Có thể bỏ qua SynthesizerAgent

4. **Quy trình làm việc của bạn phức tạp không?**
   - **Có** → Thêm OrchestratorAgent để quản lý quy trình
   - **Không** → Có thể bỏ qua OrchestratorAgent

5. **Các agent của bạn cần chia sẻ thông tin không?**
   - **Có** → Bật SharedMemory
   - **Không** → Tắt SharedMemory

6. **Các agent của bạn có chuyên môn khác nhau trong từng lĩnh vực không?**
   - **Có** → Sử dụng Expert-weighted Consensus
   - **Không** → Tiếp tục với câu hỏi 7

7. **Bạn cần xử lý thông tin không chắc chắn với độ tin cậy khác nhau không?**
   - **Có** → Sử dụng Bayesian Consensus
   - **Không** → Sử dụng Simple Voting hoặc Weighted Voting

## So sánh các cấu hình Agent System

| Cấu hình | Độ phức tạp | Thời gian xử lý | Độ chính xác | Trường hợp sử dụng |
|----------|-------------|----------------|--------------|-------------------|
| Single Agent | Thấp | Nhanh | Trung bình | Nhiệm vụ đơn giản, một lĩnh vực |
| Researcher + Synthesizer | Trung bình | Trung bình | Cao | Thu thập và tổng hợp thông tin |
| Full Agent System với Simple Voting | Cao | Chậm | Cao | Nhiệm vụ phức tạp, các agent tương đương |
| Full Agent System với Bayesian Consensus | Rất cao | Rất chậm | Rất cao | Nhiệm vụ phức tạp, thông tin không chắc chắn |
| Full Agent System với Expert-weighted Consensus | Rất cao | Rất chậm | Rất cao | Nhiệm vụ đa lĩnh vực, chuyên môn khác nhau |
