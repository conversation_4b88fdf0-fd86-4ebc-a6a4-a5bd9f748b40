# <PERSON><PERSON> thuật tối ưu hóa (Optimization Techniques)

Deep Research Core cung cấp nhiều kỹ thuật tối ưu hóa khác nhau để cải thiện hiệu suất và hiệu quả của các mô hình ngôn ngữ lớn. Dưới đây là hệ thống phân cấp rõ ràng của các kỹ thuật tối ưu hóa:

## Tối ưu hóa bộ nhớ (Memory Optimization)

### KV Cache Management
**Mô tả**: C<PERSON><PERSON> kỹ thuật quản lý bộ nhớ đệm key-value (KV cache) để tối ưu hóa việc sử dụng bộ nhớ trong quá trình suy luận với mô hình ngôn ngữ lớn.

#### KV Cache Pruning
**Mô tả**: Loại bỏ các token ít quan trọng khỏi bộ nhớ đệm KV để giảm sử dụng bộ nhớ.

**Ưu điểm**:
- Gi<PERSON>m đáng kể sử dụng bộ nhớ
- Cho phép xử lý sequence dài hơn
- Ít ảnh hưởng đến chất lượng đầu ra

**Trường hợp sử dụng**:
- Xử lý văn bản rất dài
- Môi trường có giới hạn bộ nhớ
- Cần tăng độ dài ngữ cảnh

**Ví dụ**:
```python
from deep_research_core.optimization import KVCachePruning

pruning = KVCachePruning(
    pruning_method="attention_based",  # hoặc "token_importance", "sliding_window"
    pruning_ratio=0.3,  # loại bỏ 30% token ít quan trọng nhất
    min_attention_threshold=0.01
)

# Sử dụng với mô hình
model_config = {
    "provider": "openai",
    "model": "gpt-4",
    "kv_cache_pruning": pruning
}
```

#### Adaptive KV Cache
**Mô tả**: Điều chỉnh kích thước bộ nhớ đệm KV theo ngữ cảnh, phân bổ nhiều bộ nhớ cho các phần quan trọng của văn bản.

**Ưu điểm**:
- Phân bổ bộ nhớ thông minh
- Tối ưu hóa theo ngữ cảnh cụ thể
- Cân bằng giữa hiệu suất và sử dụng bộ nhớ

**Trường hợp sử dụng**:
- Văn bản có cấu trúc không đồng nhất
- Cần tập trung vào các phần quan trọng
- Môi trường có bộ nhớ hạn chế nhưng cần độ chính xác cao

**Ví dụ**:
```python
from deep_research_core.optimization import AdaptiveKVCache

adaptive_cache = AdaptiveKVCache(
    adaptation_method="importance_based",  # hoặc "attention_based", "semantic_based"
    min_cache_size=0.2,  # tối thiểu 20% kích thước đầy đủ
    max_cache_size=1.0,  # tối đa 100% kích thước đầy đủ
    importance_threshold=0.05
)

# Sử dụng với mô hình
model_config = {
    "provider": "openai",
    "model": "gpt-4",
    "kv_cache": adaptive_cache
}
```

#### Chunked Attention
**Mô tả**: Xử lý attention theo từng phần (chunk) thay vì toàn bộ sequence, giúp giảm sử dụng bộ nhớ và tăng hiệu suất.

**Ưu điểm**:
- Giảm đáng kể sử dụng bộ nhớ
- Cho phép xử lý sequence rất dài
- Tăng tốc độ tính toán

**Trường hợp sử dụng**:
- Xử lý văn bản cực dài
- Môi trường có giới hạn bộ nhớ nghiêm trọng
- Cần tăng tốc độ suy luận

**Ví dụ**:
```python
from deep_research_core.optimization import ChunkedAttention

chunked_attn = ChunkedAttention(
    chunk_size=1024,  # kích thước mỗi chunk
    overlap=128,  # số token chồng lấp giữa các chunk
    attention_method="local_global"  # hoặc "sliding_window", "hierarchical"
)

# Sử dụng với mô hình
model_config = {
    "provider": "openai",
    "model": "gpt-4",
    "attention_mechanism": chunked_attn
}
```

### Gradient Optimization
**Mô tả**: Các kỹ thuật tối ưu hóa gradient để cải thiện quá trình huấn luyện mô hình.

#### Gradient Checkpointing
**Mô tả**: Lưu trữ trạng thái trung gian có chọn lọc và tính toán lại khi cần thiết, giúp giảm sử dụng bộ nhớ trong quá trình huấn luyện.

**Ưu điểm**:
- Giảm đáng kể sử dụng bộ nhớ trong huấn luyện
- Cho phép huấn luyện mô hình lớn hơn
- Đánh đổi nhỏ về tốc độ

**Trường hợp sử dụng**:
- Huấn luyện mô hình lớn
- Môi trường có giới hạn bộ nhớ GPU
- Cần tăng kích thước batch

**Ví dụ**:
```python
from deep_research_core.optimization import GradientCheckpointing

checkpointing = GradientCheckpointing(
    checkpoint_ratio=0.5,  # lưu trữ 50% trạng thái trung gian
    checkpoint_strategy="uniform"  # hoặc "layer_based", "memory_based"
)

# Sử dụng trong huấn luyện
training_config = {
    "model": "gpt-3.5-turbo",
    "gradient_checkpointing": checkpointing,
    "batch_size": 32,
    "learning_rate": 1e-5
}
```

#### Gradient Accumulation
**Mô tả**: Tích lũy gradient qua nhiều batch nhỏ trước khi cập nhật tham số, cho phép mô phỏng batch size lớn hơn.

**Ưu điểm**:
- Cho phép sử dụng batch size lớn hơn
- Cải thiện ổn định trong huấn luyện
- Tận dụng hiệu quả bộ nhớ hạn chế

**Trường hợp sử dụng**:
- Môi trường có giới hạn bộ nhớ GPU
- Cần batch size lớn cho ổn định
- Huấn luyện mô hình lớn

**Ví dụ**:
```python
from deep_research_core.optimization import GradientAccumulation

grad_accum = GradientAccumulation(
    accumulation_steps=8,  # tích lũy gradient qua 8 batch nhỏ
    sync_frequency=1  # đồng bộ hóa sau mỗi bước cập nhật
)

# Sử dụng trong huấn luyện
training_config = {
    "model": "gpt-3.5-turbo",
    "gradient_accumulation": grad_accum,
    "micro_batch_size": 4,  # batch size thực tế = 4 * 8 = 32
    "learning_rate": 1e-5
}
```

#### Distributed Gradient
**Mô tả**: Phân tán tính toán gradient qua nhiều thiết bị, cho phép huấn luyện mô hình lớn hơn và nhanh hơn.

**Ưu điểm**:
- Tăng tốc độ huấn luyện
- Cho phép huấn luyện mô hình rất lớn
- Mở rộng quy mô dễ dàng

**Trường hợp sử dụng**:
- Huấn luyện mô hình rất lớn
- Môi trường có nhiều GPU/TPU
- Cần giảm thời gian huấn luyện

**Ví dụ**:
```python
from deep_research_core.optimization import DistributedGradient

dist_grad = DistributedGradient(
    distribution_strategy="data_parallel",  # hoặc "model_parallel", "pipeline_parallel"
    num_devices=8,  # số lượng thiết bị
    communication_backend="nccl"  # hoặc "gloo", "mpi"
)

# Sử dụng trong huấn luyện
training_config = {
    "model": "gpt-3.5-turbo",
    "distributed_gradient": dist_grad,
    "batch_size": 64,
    "learning_rate": 1e-5
}
```

## Parameter-Efficient Fine-Tuning (PEFT)
**Mô tả**: Các phương pháp tinh chỉnh mô hình hiệu quả về tham số, cho phép điều chỉnh mô hình lớn với ít tài nguyên.

### Adapter-based Methods
**Mô tả**: Thêm các lớp nhỏ (adapter) vào mô hình đã huấn luyện trước và chỉ cập nhật các lớp này.

#### Standard Adapter
**Mô tả**: Thêm các lớp adapter nhỏ vào mô hình và chỉ huấn luyện các lớp này.

**Ưu điểm**:
- Số lượng tham số huấn luyện thấp (1-5% so với mô hình gốc)
- Dễ chuyển đổi giữa các tác vụ
- Bảo toàn kiến thức của mô hình gốc

**Trường hợp sử dụng**:
- Tinh chỉnh mô hình lớn với tài nguyên hạn chế
- Cần nhiều phiên bản tinh chỉnh cho các tác vụ khác nhau
- Cần khả năng chuyển đổi nhanh giữa các tác vụ

**Ví dụ**:
```python
from deep_research_core.optimization import StandardAdapter

adapter = StandardAdapter(
    base_model="gpt-3.5-turbo",
    bottleneck_size=64,  # kích thước bottleneck của adapter
    adapter_location="all_layers",  # hoặc "last_layers", "selected_layers"
    init_scale=0.01
)

# Huấn luyện adapter
adapter.train(
    train_data=train_dataset,
    learning_rate=1e-3,
    num_epochs=3
)

# Sử dụng mô hình với adapter
result = adapter.generate("Dự đoán giá Bitcoin trong năm tới")
```

#### IA3 (Infused Adapter by Inhibiting and Amplifying Inner Activations)
**Mô tả**: Phương pháp adapter nhẹ hơn, chỉ thêm các tham số vào để ức chế hoặc khuếch đại các kích hoạt bên trong.

**Ưu điểm**:
- Rất ít tham số (0.1-1% so với mô hình gốc)
- Hiệu quả cao cho một số tác vụ cụ thể
- Yêu cầu bộ nhớ rất thấp

**Trường hợp sử dụng**:
- Môi trường có giới hạn tài nguyên nghiêm trọng
- Cần tinh chỉnh nhanh
- Các tác vụ đơn giản hoặc chuyên biệt

**Ví dụ**:
```python
from deep_research_core.optimization import IA3Adapter

ia3 = IA3Adapter(
    base_model="gpt-3.5-turbo",
    target_modules=["attention", "mlp"],  # các module cần áp dụng IA3
    init_scale=0.01
)

# Huấn luyện IA3
ia3.train(
    train_data=train_dataset,
    learning_rate=1e-3,
    num_epochs=3
)

# Sử dụng mô hình với IA3
result = ia3.generate("Tóm tắt bài báo này")
```

### Low-Rank Adaptation
**Mô tả**: Sử dụng ma trận hạng thấp (low-rank) để điều chỉnh trọng số của mô hình, giảm đáng kể số lượng tham số cần huấn luyện.

#### LoRA (Low-Rank Adaptation)
**Mô tả**: Thêm các cặp ma trận hạng thấp vào mô hình để điều chỉnh trọng số mà không thay đổi kích thước mô hình gốc.

**Ưu điểm**:
- Số lượng tham số huấn luyện thấp (0.5-5% so với mô hình gốc)
- Không tăng độ trễ suy luận
- Chất lượng cao, gần với fine-tuning đầy đủ

**Trường hợp sử dụng**:
- Tinh chỉnh mô hình lớn với tài nguyên hạn chế
- Cần chất lượng cao gần với fine-tuning đầy đủ
- Cần nhiều phiên bản tinh chỉnh cho các tác vụ khác nhau

**Ví dụ**:
```python
from deep_research_core.optimization import LoRA

lora = LoRA(
    base_model="gpt-3.5-turbo",
    rank=8,  # hạng của ma trận thích ứng
    alpha=16,  # hệ số tỷ lệ
    target_modules=["q_proj", "v_proj"],  # các module cần áp dụng LoRA
    dropout=0.05
)

# Huấn luyện LoRA
lora.train(
    train_data=train_dataset,
    learning_rate=1e-4,
    num_epochs=3
)

# Sử dụng mô hình với LoRA
result = lora.generate("Viết một bài thơ về mùa thu")
```

#### QLoRA (Quantized Low-Rank Adaptation)
**Mô tả**: Kết hợp lượng tử hóa (quantization) với LoRA để giảm thêm yêu cầu bộ nhớ.

**Ưu điểm**:
- Giảm đáng kể yêu cầu bộ nhớ (75-90% so với fine-tuning đầy đủ)
- Cho phép tinh chỉnh mô hình rất lớn trên GPU tiêu chuẩn
- Chất lượng gần với LoRA thông thường

**Trường hợp sử dụng**:
- Tinh chỉnh mô hình rất lớn với GPU tiêu chuẩn
- Môi trường có giới hạn bộ nhớ nghiêm trọng
- Cần chất lượng cao với tài nguyên hạn chế

**Ví dụ**:
```python
from deep_research_core.optimization import QLoRA

qlora = QLoRA(
    base_model="gpt-3.5-turbo",
    rank=8,  # hạng của ma trận thích ứng
    alpha=16,  # hệ số tỷ lệ
    target_modules=["q_proj", "v_proj"],  # các module cần áp dụng QLoRA
    quantization_bits=4,  # số bit lượng tử hóa
    quantization_type="nf4"  # hoặc "int4", "fp4"
)

# Huấn luyện QLoRA
qlora.train(
    train_data=train_dataset,
    learning_rate=1e-4,
    num_epochs=3
)

# Sử dụng mô hình với QLoRA
result = qlora.generate("Giải thích khái niệm học máy cho người mới bắt đầu")
```

### Prompt-based Methods
**Mô tả**: Các phương pháp điều chỉnh mô hình bằng cách tối ưu hóa prompt liên tục (continuous prompt).

#### Prefix Tuning
**Mô tả**: Thêm các prefix có thể học được vào mỗi lớp của mô hình, giữ nguyên tham số mô hình gốc.

**Ưu điểm**:
- Số lượng tham số huấn luyện thấp (0.1-1% so với mô hình gốc)
- Hiệu quả cho các tác vụ sinh văn bản
- Dễ chuyển đổi giữa các tác vụ

**Trường hợp sử dụng**:
- Tinh chỉnh mô hình lớn với tài nguyên hạn chế
- Các tác vụ sinh văn bản (NLG)
- Điều chỉnh phong cách hoặc định dạng đầu ra

**Ví dụ**:
```python
from deep_research_core.optimization import PrefixTuning

prefix_tuning = PrefixTuning(
    base_model="gpt-3.5-turbo",
    prefix_length=20,  # độ dài prefix
    num_virtual_tokens=20,  # số lượng token ảo
    prefix_projection=True  # sử dụng projection cho prefix
)

# Huấn luyện Prefix Tuning
prefix_tuning.train(
    train_data=train_dataset,
    learning_rate=1e-3,
    num_epochs=3
)

# Sử dụng mô hình với Prefix Tuning
result = prefix_tuning.generate("Viết một email chuyên nghiệp cho khách hàng")
```

#### Prompt Tuning
**Mô tả**: Tối ưu hóa prompt liên tục (soft prompt) được thêm vào đầu vào, giữ nguyên tham số mô hình gốc.

**Ưu điểm**:
- Rất ít tham số (0.01-0.1% so với mô hình gốc)
- Dễ triển khai và sử dụng
- Hiệu quả cho các tác vụ đơn giản

**Trường hợp sử dụng**:
- Điều chỉnh nhẹ cho các tác vụ đơn giản
- Môi trường có giới hạn tài nguyên nghiêm trọng
- Cần triển khai nhanh và đơn giản

**Ví dụ**:
```python
from deep_research_core.optimization import PromptTuning

prompt_tuning = PromptTuning(
    base_model="gpt-3.5-turbo",
    num_virtual_tokens=10,  # số lượng token ảo
    init_from_vocab=True  # khởi tạo từ từ vựng của mô hình
)

# Huấn luyện Prompt Tuning
prompt_tuning.train(
    train_data=train_dataset,
    learning_rate=1e-2,
    num_epochs=5
)

# Sử dụng mô hình với Prompt Tuning
result = prompt_tuning.generate("Phân loại văn bản này")
```

## Inference Optimization
**Mô tả**: Các kỹ thuật tối ưu hóa quá trình suy luận để tăng tốc độ và giảm tài nguyên.

### Model Quantization
**Mô tả**: Giảm độ chính xác của tham số mô hình (từ float32 xuống int8, int4, v.v.) để giảm kích thước mô hình và tăng tốc độ suy luận.

**Ưu điểm**:
- Giảm đáng kể kích thước mô hình (75-90%)
- Tăng tốc độ suy luận
- Giảm yêu cầu bộ nhớ

**Trường hợp sử dụng**:
- Triển khai mô hình trên thiết bị có tài nguyên hạn chế
- Cần tăng tốc độ suy luận
- Có thể chấp nhận giảm nhẹ độ chính xác

**Ví dụ**:
```python
from deep_research_core.optimization import ModelQuantizer

quantizer = ModelQuantizer(
    quantization_bits=8,  # số bit lượng tử hóa
    quantization_type="dynamic",  # hoặc "static", "weight_only"
    quantization_scheme="symmetric"  # hoặc "asymmetric"
)

# Lượng tử hóa mô hình
quantized_model = quantizer.quantize(
    model="gpt-3.5-turbo",
    calibration_data=calibration_dataset
)

# Sử dụng mô hình đã lượng tử hóa
result = quantized_model.generate("Dự đoán xu hướng thị trường trong quý tới")
```

### ParallelInference
**Mô tả**: Thực hiện suy luận song song trên nhiều thiết bị hoặc nhiều phiên bản mô hình.

**Ưu điểm**:
- Tăng đáng kể thông lượng
- Giảm độ trễ trung bình
- Tận dụng hiệu quả nhiều thiết bị

**Trường hợp sử dụng**:
- Xử lý nhiều yêu cầu đồng thời
- Môi trường có nhiều GPU/TPU
- Cần giảm thời gian phản hồi

**Ví dụ**:
```python
from deep_research_core.optimization import ParallelInference

parallel_inference = ParallelInference(
    model="gpt-3.5-turbo",
    num_instances=4,  # số lượng phiên bản mô hình
    batch_size=8,  # kích thước batch cho mỗi phiên bản
    device_map="auto"  # hoặc danh sách thiết bị cụ thể
)

# Xử lý nhiều yêu cầu
results = parallel_inference.generate_batch([
    "Tóm tắt bài báo này",
    "Viết một email chuyên nghiệp",
    "Dịch đoạn văn này sang tiếng Anh",
    "Giải thích khái niệm học máy"
])
```

### CachingStrategies
**Mô tả**: Lưu trữ kết quả suy luận để tái sử dụng, giảm tính toán trùng lặp.

**Ưu điểm**:
- Giảm đáng kể thời gian phản hồi cho các yêu cầu tương tự
- Giảm tải cho mô hình
- Tiết kiệm tài nguyên tính toán

**Trường hợp sử dụng**:
- Xử lý các yêu cầu lặp lại hoặc tương tự
- Ứng dụng có mẫu truy cập có thể dự đoán
- Cần giảm chi phí API

**Ví dụ**:
```python
from deep_research_core.optimization import CachingStrategy

caching = CachingStrategy(
    cache_type="redis",  # hoặc "in_memory", "disk"
    ttl=3600,  # thời gian sống của cache (giây)
    max_size=1000,  # số lượng mục tối đa trong cache
    similarity_threshold=0.95  # ngưỡng tương đồng để sử dụng cache
)

# Sử dụng với mô hình
model_config = {
    "provider": "openai",
    "model": "gpt-4",
    "caching_strategy": caching
}
```

## Bảng so sánh các kỹ thuật PEFT

| Kỹ thuật | Số tham số | Hiệu suất | Tài nguyên | Phù hợp cho |
|----------|------------|-----------|------------|-------------|
| LoRA     | Thấp (0.5-5%) | Cao (90-95% so với full fine-tuning) | Trung bình | Mô hình lớn với yêu cầu chất lượng cao |
| QLoRA    | Thấp (0.5-5%) | Cao (85-95% so với full fine-tuning) | Thấp | Phần cứng hạn chế, mô hình lớn |
| Adapter  | Trung bình (1-5%) | Trung bình (80-90% so với full fine-tuning) | Thấp | Nhiều tác vụ khác nhau, dễ chuyển đổi |
| IA3      | Rất thấp (0.1-1%) | Trung bình (75-85% so với full fine-tuning) | Rất thấp | Phần cứng rất hạn chế, tác vụ đơn giản |
| Prefix Tuning | Thấp (0.1-1%) | Trung bình (80-90% so với full fine-tuning) | Trung bình | Tác vụ NLG, điều chỉnh phong cách |
| Prompt Tuning | Rất thấp (0.01-0.1%) | Thấp (70-80% so với full fine-tuning) | Rất thấp | Điều chỉnh nhẹ, tác vụ đơn giản |

### Ví dụ cụ thể cho từng kỹ thuật PEFT

#### LoRA
**Ví dụ**: Tinh chỉnh mô hình GPT-3.5 để tạo nội dung marketing chuyên nghiệp. LoRA cho phép điều chỉnh mô hình với chất lượng cao mà chỉ cần huấn luyện khoảng 1% tham số.

```python
# Tinh chỉnh mô hình với LoRA
lora_model = LoRA(
    base_model="gpt-3.5-turbo",
    rank=8,
    alpha=16,
    target_modules=["q_proj", "v_proj", "k_proj", "o_proj"]
).train(marketing_dataset)

# Kết quả: Nội dung marketing chất lượng cao, phong cách nhất quán
```

#### QLoRA
**Ví dụ**: Tinh chỉnh mô hình Llama 2 70B trên laptop với GPU 8GB VRAM. QLoRA cho phép tinh chỉnh mô hình cực lớn trên phần cứng tiêu chuẩn.

```python
# Tinh chỉnh mô hình với QLoRA
qlora_model = QLoRA(
    base_model="llama-2-70b",
    rank=8,
    alpha=16,
    quantization_bits=4
).train(instruction_dataset)

# Kết quả: Mô hình 70B được tinh chỉnh trên GPU 8GB
```

#### Adapter
**Ví dụ**: Tinh chỉnh mô hình cho nhiều lĩnh vực khác nhau (y tế, luật, kỹ thuật) và chuyển đổi nhanh giữa các lĩnh vực.

```python
# Tinh chỉnh nhiều adapter
medical_adapter = StandardAdapter(base_model="gpt-3.5-turbo").train(medical_dataset)
legal_adapter = StandardAdapter(base_model="gpt-3.5-turbo").train(legal_dataset)
technical_adapter = StandardAdapter(base_model="gpt-3.5-turbo").train(technical_dataset)

# Chuyển đổi nhanh giữa các lĩnh vực
model.set_active_adapter("medical")  # Kích hoạt adapter y tế
```

#### IA3
**Ví dụ**: Tinh chỉnh mô hình trên thiết bị di động với bộ nhớ rất hạn chế cho tác vụ phân loại văn bản đơn giản.

```python
# Tinh chỉnh với IA3 cho thiết bị hạn chế
ia3_model = IA3Adapter(
    base_model="distilbert",
    target_modules=["attention", "mlp"]
).train(classification_dataset)

# Kết quả: Mô hình nhẹ có thể chạy trên thiết bị di động
```

#### Prefix Tuning
**Ví dụ**: Điều chỉnh phong cách viết của mô hình để tạo nội dung theo nhiều phong cách khác nhau (trang trọng, thân mật, hài hước).

```python
# Tinh chỉnh prefix cho các phong cách khác nhau
formal_prefix = PrefixTuning(base_model="gpt-3.5-turbo").train(formal_dataset)
casual_prefix = PrefixTuning(base_model="gpt-3.5-turbo").train(casual_dataset)
humorous_prefix = PrefixTuning(base_model="gpt-3.5-turbo").train(humorous_dataset)

# Kết quả: Cùng một nội dung nhưng phong cách khác nhau
```

#### Prompt Tuning
**Ví dụ**: Điều chỉnh nhẹ mô hình cho tác vụ phân loại cảm xúc đơn giản với rất ít dữ liệu huấn luyện.

```python
# Tinh chỉnh prompt cho phân loại cảm xúc
sentiment_prompt = PromptTuning(
    base_model="gpt-3.5-turbo",
    num_virtual_tokens=10
).train(sentiment_dataset)

# Kết quả: Mô hình có thể phân loại cảm xúc với độ chính xác khá
```

## Cây quyết định cho việc lựa chọn kỹ thuật tối ưu hóa

Để chọn kỹ thuật tối ưu hóa phù hợp, hãy trả lời các câu hỏi sau:

1. **Bạn cần tối ưu hóa gì?**
   - **Bộ nhớ** → Tiếp tục với câu hỏi 2
   - **Tham số mô hình** → Tiếp tục với câu hỏi 5
   - **Tốc độ suy luận** → Tiếp tục với câu hỏi 8

2. **Bạn đang gặp vấn đề với sequence dài?**
   - **Có** → Tiếp tục với câu hỏi 3
   - **Không** → Tiếp tục với câu hỏi 4

3. **Bạn cần giải pháp nào cho sequence dài?**
   - **Giảm bộ nhớ đệm KV** → Sử dụng **KV Cache Pruning**
   - **Phân bổ bộ nhớ thông minh** → Sử dụng **Adaptive KV Cache**
   - **Xử lý sequence cực dài** → Sử dụng **Chunked Attention**

4. **Bạn đang gặp vấn đề với kích thước batch?**
   - **Có** → Sử dụng **Gradient Accumulation** hoặc **Gradient Checkpointing**
   - **Không** → Sử dụng **Model Quantization**

5. **Bạn có giới hạn tài nguyên tính toán?**
   - **Có** → Tiếp tục với câu hỏi 6
   - **Không** → Sử dụng **Full Fine-tuning**

6. **Bạn cần độ chính xác cao nhất?**
   - **Có** → Tiếp tục với câu hỏi 7
   - **Không** → Bạn có giới hạn bộ nhớ nghiêm trọng?
     - **Có** → Sử dụng **IA3**
     - **Không** → Sử dụng **Standard Adapter**

7. **Bạn có giới hạn bộ nhớ nghiêm trọng?**
   - **Có** → Sử dụng **QLoRA**
   - **Không** → Sử dụng **LoRA**

8. **Bạn muốn tối ưu hóa gì cho suy luận?**
   - **Giảm kích thước mô hình** → Sử dụng **Model Quantization**
   - **Tăng thông lượng** → Sử dụng **ParallelInference**
   - **Giảm độ trễ cho yêu cầu tương tự** → Sử dụng **CachingStrategies**
