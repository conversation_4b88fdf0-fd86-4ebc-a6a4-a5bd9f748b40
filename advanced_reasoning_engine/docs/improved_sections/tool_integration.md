# Tích hợp công cụ (Tool Integration)

Deep Research Core cung cấp một hệ thống tích hợp công cụ toàn diện, phân biệt rõ ràng giữa việc đăng ký/quản lý công cụ (Tool Registry) và việc sử dụng công cụ (Tool Usage). Dưới đây là hệ thống phân cấp rõ ràng của các thành phần tích hợp công cụ:

## Cấu trúc hệ thống tích hợp công cụ

```
Tool Integration
├── Tool Registry (Đăng ký công cụ)
│   ├── Search: Công cụ tìm kiếm
│   ├── Calculator: Công cụ tính toán
│   ├── Database: Công cụ truy cập cơ sở dữ liệu
│   ├── File: Công cụ xử lý file
│   ├── Web: Công cụ truy cập web
│   └── API: Công cụ gọi API
└── Tool Usage (Sử dụng công cụ)
    ├── ToolSelection: Lựa chọn công cụ phù hợp
    ├── ToolExecution: Thực thi công cụ
    └── ResultInterpretation: Giải thích kết quả
```

## Tool Registry (Đăng ký công cụ)

Tool Registry là hệ thống quản lý và đăng ký các công cụ có thể được sử dụng bởi các module khác trong Deep Research Core. Nó cung cấp một giao diện thống nhất để đăng ký, quản lý, và truy cập các công cụ.

### Search Tool
**Mô tả**: Công cụ tìm kiếm thông tin từ internet hoặc cơ sở dữ liệu.

**Khả năng**:
- Tìm kiếm thông tin từ internet
- Tìm kiếm thông tin từ cơ sở dữ liệu
- Lọc và xếp hạng kết quả

**Tham số**:
- `query`: Truy vấn tìm kiếm
- `source`: Nguồn tìm kiếm (web, database, etc.)
- `max_results`: Số lượng kết quả tối đa
- `filters`: Bộ lọc kết quả

**Ví dụ**:
```python
from deep_research_core.tools import SearchTool

search_tool = SearchTool(
    default_source="web",  # hoặc "database", "custom"
    api_key="your-search-api-key",
    max_results=10
)

# Tìm kiếm thông tin
search_results = search_tool.search(
    query="Tác động của AI đến thị trường lao động",
    source="web",
    max_results=5,
    filters={"time": "past_year", "language": "vietnamese"}
)
```

### Calculator Tool
**Mô tả**: Công cụ thực hiện các phép tính toán số học và đại số.

**Khả năng**:
- Thực hiện phép tính số học
- Giải phương trình
- Tính toán thống kê
- Vẽ đồ thị hàm số

**Tham số**:
- `expression`: Biểu thức cần tính toán
- `mode`: Chế độ tính toán (arithmetic, equation, statistics, etc.)
- `precision`: Độ chính xác của kết quả

**Ví dụ**:
```python
from deep_research_core.tools import CalculatorTool

calculator_tool = CalculatorTool(
    default_mode="arithmetic",
    precision=10,
    enable_symbolic=True
)

# Tính toán biểu thức
result = calculator_tool.calculate("(5 + 3) * 2 / 4")
# Output: 4.0

# Giải phương trình
solution = calculator_tool.solve_equation("2x + 3 = 7")
# Output: x = 2.0

# Tính toán thống kê
stats = calculator_tool.calculate_statistics([1, 2, 3, 4, 5])
# Output: {"mean": 3.0, "median": 3.0, "std": 1.58...}
```

### Database Tool
**Mô tả**: Công cụ truy cập và thao tác với cơ sở dữ liệu.

**Khả năng**:
- Truy vấn dữ liệu
- Thêm/sửa/xóa dữ liệu
- Thực hiện các phép phân tích

**Tham số**:
- `connection_string`: Chuỗi kết nối đến cơ sở dữ liệu
- `query`: Truy vấn SQL hoặc NoSQL
- `params`: Tham số cho truy vấn
- `operation`: Loại thao tác (select, insert, update, delete)

**Ví dụ**:
```python
from deep_research_core.tools import DatabaseTool

database_tool = DatabaseTool(
    connection_string="sqlite:///database.db",
    default_table="users",
    max_results=100
)

# Truy vấn dữ liệu
results = database_tool.query(
    query="SELECT * FROM users WHERE age > :age",
    params={"age": 18},
    max_results=10
)

# Thêm dữ liệu
database_tool.insert(
    table="users",
    data={"name": "John Doe", "age": 30, "email": "<EMAIL>"}
)

# Cập nhật dữ liệu
database_tool.update(
    table="users",
    where={"id": 1},
    data={"age": 31}
)
```

### File Tool
**Mô tả**: Công cụ đọc, ghi, và xử lý file.

**Khả năng**:
- Đọc file
- Ghi file
- Xử lý file (nén, giải nén, chuyển đổi định dạng)

**Tham số**:
- `file_path`: Đường dẫn đến file
- `mode`: Chế độ mở file (read, write, append)
- `encoding`: Mã hóa file
- `format`: Định dạng file (txt, csv, json, etc.)

**Ví dụ**:
```python
from deep_research_core.tools import FileTool

file_tool = FileTool(
    default_encoding="utf-8",
    default_format="auto",
    base_directory="./data"
)

# Đọc file
content = file_tool.read(
    file_path="document.txt",
    encoding="utf-8"
)

# Ghi file
file_tool.write(
    file_path="output.txt",
    content="Nội dung cần ghi",
    mode="w"  # hoặc "a" để append
)

# Đọc file CSV
data = file_tool.read_csv(
    file_path="data.csv",
    delimiter=",",
    has_header=True
)

# Ghi file JSON
file_tool.write_json(
    file_path="data.json",
    data={"name": "John", "age": 30}
)
```

### Web Tool
**Mô tả**: Công cụ truy cập và tương tác với web.

**Khả năng**:
- Truy cập trang web
- Trích xuất thông tin từ trang web
- Tải file từ web

**Tham số**:
- `url`: URL của trang web
- `method`: Phương thức HTTP (GET, POST, etc.)
- `headers`: Headers của request
- `data`: Dữ liệu gửi kèm request
- `selector`: Bộ chọn CSS hoặc XPath để trích xuất thông tin

**Ví dụ**:
```python
from deep_research_core.tools import WebTool

web_tool = WebTool(
    default_headers={"User-Agent": "Deep Research Core"},
    timeout=30,
    max_retries=3
)

# Truy cập trang web
response = web_tool.get(
    url="https://example.com",
    headers={"Accept-Language": "vi-VN"}
)

# Trích xuất thông tin
title = web_tool.extract(
    url="https://example.com",
    selector="h1.title",
    attribute="text"
)

# Tải file
web_tool.download(
    url="https://example.com/file.pdf",
    output_path="./downloads/file.pdf"
)

# Gửi form
web_tool.post(
    url="https://example.com/form",
    data={"name": "John", "email": "<EMAIL>"}
)
```

### API Tool
**Mô tả**: Công cụ gọi API bên ngoài.

**Khả năng**:
- Gọi API REST
- Gọi API GraphQL
- Xử lý authentication và authorization

**Tham số**:
- `url`: URL của API
- `method`: Phương thức HTTP (GET, POST, etc.)
- `headers`: Headers của request
- `params`: Query parameters
- `data`: Dữ liệu gửi kèm request
- `auth`: Thông tin xác thực

**Ví dụ**:
```python
from deep_research_core.tools import APITool

api_tool = APITool(
    base_url="https://api.example.com",
    default_headers={"Content-Type": "application/json"},
    auth_type="bearer",  # hoặc "basic", "api_key"
    auth_token="your-api-token"
)

# Gọi API GET
response = api_tool.get(
    endpoint="/users",
    params={"limit": 10, "offset": 0}
)

# Gọi API POST
response = api_tool.post(
    endpoint="/users",
    data={"name": "John", "email": "<EMAIL>"}
)

# Gọi API GraphQL
response = api_tool.graphql(
    query="""
    query {
        user(id: "123") {
            name
            email
        }
    }
    """,
    variables={"id": "123"}
)
```

## Tool Usage (Sử dụng công cụ)

Tool Usage là hệ thống sử dụng các công cụ đã đăng ký trong Tool Registry. Nó cung cấp các thành phần để lựa chọn công cụ phù hợp, thực thi công cụ, và giải thích kết quả.

### ToolSelection
**Mô tả**: Lựa chọn công cụ phù hợp dựa trên yêu cầu và ngữ cảnh.

**Khả năng**:
- Phân tích yêu cầu để xác định công cụ cần thiết
- Xếp hạng các công cụ phù hợp
- Lựa chọn công cụ tối ưu

**Ví dụ**:
```python
from deep_research_core.tool_usage import ToolSelector
from deep_research_core.tools import ToolRegistry

# Đăng ký các công cụ
tool_registry = ToolRegistry()
tool_registry.register("search", SearchTool())
tool_registry.register("calculator", CalculatorTool())
tool_registry.register("database", DatabaseTool())

# Khởi tạo Tool Selector
tool_selector = ToolSelector(
    tool_registry=tool_registry,
    selection_method="ml",  # hoặc "rule_based", "hybrid"
    provider="openai",
    model="gpt-4"
)

# Lựa chọn công cụ
selected_tool = tool_selector.select(
    query="Tính tổng của 17 và 28, sau đó nhân với 3",
    context={"previous_tools": ["search"]}
)
# Output: {"tool": "calculator", "confidence": 0.95}
```

### ToolExecution
**Mô tả**: Thực thi công cụ đã chọn với các tham số phù hợp.

**Khả năng**:
- Chuẩn bị tham số cho công cụ
- Thực thi công cụ
- Xử lý lỗi và retry

**Ví dụ**:
```python
from deep_research_core.tool_usage import ToolExecutor
from deep_research_core.tools import ToolRegistry

# Đăng ký các công cụ
tool_registry = ToolRegistry()
tool_registry.register("search", SearchTool())
tool_registry.register("calculator", CalculatorTool())

# Khởi tạo Tool Executor
tool_executor = ToolExecutor(
    tool_registry=tool_registry,
    max_retries=3,
    timeout=30
)

# Thực thi công cụ
result = tool_executor.execute(
    tool_name="calculator",
    operation="calculate",
    params={"expression": "(17 + 28) * 3"}
)
# Output: 135.0
```

### ResultInterpretation
**Mô tả**: Giải thích kết quả từ công cụ và tích hợp vào quá trình suy luận.

**Khả năng**:
- Phân tích kết quả từ công cụ
- Trích xuất thông tin quan trọng
- Tích hợp kết quả vào quá trình suy luận

**Ví dụ**:
```python
from deep_research_core.tool_usage import ResultInterpreter

# Khởi tạo Result Interpreter
result_interpreter = ResultInterpreter(
    provider="openai",
    model="gpt-4",
    interpretation_style="detailed"  # hoặc "concise", "technical"
)

# Giải thích kết quả
interpretation = result_interpreter.interpret(
    tool_name="search",
    result=search_results,
    query="Tác động của AI đến thị trường lao động",
    context={"purpose": "research"}
)
```

## Sự khác biệt giữa Tool Registry và ReAct Reasoning

Mặc dù cả Tool Registry và ReAct Reasoning đều liên quan đến việc sử dụng công cụ, nhưng chúng có những khác biệt quan trọng:

### Tool Registry
- **Mục đích**: Đăng ký, quản lý, và cung cấp giao diện thống nhất cho các công cụ
- **Trách nhiệm**: Quản lý vòng đời của công cụ, xác thực tham số, xử lý lỗi cấp thấp
- **Tương tác**: Tương tác trực tiếp với các API và dịch vụ bên ngoài
- **Độc lập**: Có thể được sử dụng độc lập bởi bất kỳ module nào cần công cụ

### ReAct Reasoning
- **Mục đích**: Kết hợp suy luận với hành động (sử dụng công cụ)
- **Trách nhiệm**: Quyết định khi nào sử dụng công cụ, lựa chọn công cụ phù hợp, giải thích kết quả
- **Tương tác**: Tương tác với Tool Registry để sử dụng công cụ
- **Phụ thuộc**: Phụ thuộc vào Tool Registry để truy cập công cụ

### So sánh

| Khía cạnh | Tool Registry | ReAct Reasoning |
|-----------|--------------|-----------------|
| Mục đích | Quản lý công cụ | Suy luận và hành động |
| Trách nhiệm | Cung cấp giao diện cho công cụ | Quyết định khi nào và cách sử dụng công cụ |
| Tương tác | Với API và dịch vụ bên ngoài | Với Tool Registry |
| Độc lập | Có thể sử dụng độc lập | Phụ thuộc vào Tool Registry |
| Cấp độ | Cấp thấp (low-level) | Cấp cao (high-level) |
| Người dùng | Các module khác trong hệ thống | Người dùng cuối |

## Giao diện thống nhất cho tích hợp công cụ

Deep Research Core cung cấp giao diện thống nhất `ToolRegistry` để dễ dàng đăng ký, quản lý, và sử dụng các công cụ:

```python
from deep_research_core.tools import ToolRegistry, SearchTool, CalculatorTool, DatabaseTool, FileTool, WebTool, APITool

# Khởi tạo Tool Registry
tool_registry = ToolRegistry()

# Đăng ký các công cụ
tool_registry.register("search", SearchTool(
    default_source="web",
    api_key="your-search-api-key"
))

tool_registry.register("calculator", CalculatorTool(
    precision=10,
    enable_symbolic=True
))

tool_registry.register("database", DatabaseTool(
    connection_string="sqlite:///database.db"
))

tool_registry.register("file", FileTool(
    base_directory="./data"
))

tool_registry.register("web", WebTool(
    timeout=30,
    max_retries=3
))

tool_registry.register("api", APITool(
    base_url="https://api.example.com",
    auth_token="your-api-token"
))

# Lấy công cụ từ registry
search_tool = tool_registry.get("search")
calculator_tool = tool_registry.get("calculator")

# Liệt kê tất cả công cụ đã đăng ký
all_tools = tool_registry.list_tools()

# Kiểm tra xem công cụ có tồn tại không
has_search = tool_registry.has_tool("search")  # True

# Gỡ bỏ công cụ
tool_registry.unregister("api")

# Sử dụng công cụ thông qua registry
result = tool_registry.execute(
    tool_name="calculator",
    operation="calculate",
    params={"expression": "5 + 3 * 2"}
)
# Output: 11.0
```

## Ví dụ sử dụng ToolRegistry với ReAct

```python
from deep_research_core.tools import ToolRegistry, SearchTool, CalculatorTool
from deep_research_core.reasoning import ReAct

# Khởi tạo Tool Registry
tool_registry = ToolRegistry()

# Đăng ký các công cụ
tool_registry.register("search", SearchTool())
tool_registry.register("calculator", CalculatorTool())

# Khởi tạo ReAct với Tool Registry
react = ReAct(
    provider="openai",
    model="gpt-4",
    tool_registry=tool_registry
)

# Sử dụng ReAct với các công cụ
result = react.generate_reasoning(
    "Tìm kiếm GDP của Việt Nam năm 2022, sau đó tính tăng trưởng so với năm 2021 khi GDP là 362.6 tỷ USD"
)
```

## Ví dụ sử dụng ToolRegistry với các module khác

### Sử dụng với RAG
```python
from deep_research_core.tools import ToolRegistry, SearchTool, WebTool
from deep_research_core.reasoning import RAG
from deep_research_core.vector_store import SQLiteVectorStore

# Khởi tạo Tool Registry
tool_registry = ToolRegistry()
tool_registry.register("search", SearchTool())
tool_registry.register("web", WebTool())

# Khởi tạo RAG với Tool Registry
rag = RAG(
    vector_store=SQLiteVectorStore("documents.db"),
    embedding_model="all-MiniLM-L6-v2",
    provider="openai",
    model="gpt-4",
    tool_registry=tool_registry
)

# Sử dụng RAG với khả năng truy cập công cụ
result = rag.generate_reasoning_with_tools(
    "Tìm kiếm thông tin mới nhất về biến đổi khí hậu và tóm tắt dựa trên tài liệu hiện có"
)
```

### Sử dụng với Agent System
```python
from deep_research_core.tools import ToolRegistry, SearchTool, DatabaseTool, APITool
from deep_research_core.agent_system import AgentSystem
from deep_research_core.agents import ResearcherAgent, SynthesizerAgent

# Khởi tạo Tool Registry
tool_registry = ToolRegistry()
tool_registry.register("search", SearchTool())
tool_registry.register("database", DatabaseTool())
tool_registry.register("api", APITool())

# Khởi tạo Agent System với Tool Registry
agent_system = AgentSystem(
    provider="openai",
    model="gpt-4",
    tool_registry=tool_registry
)

# Thêm các agent
agent_system.add_agent("researcher", ResearcherAgent())
agent_system.add_agent("synthesizer", SynthesizerAgent())

# Cấu hình multi-agent framework
agent_system.configure_framework(
    role_specialization=True,
    task_decomposition=True,
    shared_memory=True,
    consensus_mechanism="bayesian"
)

# Thực thi nhiệm vụ với khả năng truy cập công cụ
result = agent_system.execute_task(
    "Nghiên cứu thị trường smartphone Việt Nam, thu thập dữ liệu bán hàng, và tổng hợp báo cáo"
)
```

## Cây quyết định cho việc lựa chọn công cụ

Để chọn công cụ phù hợp, hãy trả lời các câu hỏi sau:

1. **Bạn cần thực hiện loại tác vụ nào?**
   - **Tìm kiếm thông tin** → Sử dụng `SearchTool`
   - **Tính toán** → Sử dụng `CalculatorTool`
   - **Truy cập cơ sở dữ liệu** → Sử dụng `DatabaseTool`
   - **Xử lý file** → Sử dụng `FileTool`
   - **Truy cập web** → Sử dụng `WebTool`
   - **Gọi API** → Sử dụng `APITool`

2. **Bạn cần sử dụng công cụ trong module nào?**
   - **ReAct** → Sử dụng `ReAct` với `tool_registry`
   - **RAG** → Sử dụng `RAG` với `tool_registry`
   - **Agent System** → Sử dụng `AgentSystem` với `tool_registry`
   - **Module tùy chỉnh** → Sử dụng `ToolRegistry` trực tiếp

3. **Bạn cần tự động lựa chọn công cụ?**
   - **Có** → Sử dụng `ToolSelector` với `selection_method="ml"`
   - **Không** → Chỉ định công cụ cụ thể

4. **Bạn cần giải thích kết quả từ công cụ?**
   - **Có** → Sử dụng `ResultInterpreter`
   - **Không** → Sử dụng kết quả trực tiếp
