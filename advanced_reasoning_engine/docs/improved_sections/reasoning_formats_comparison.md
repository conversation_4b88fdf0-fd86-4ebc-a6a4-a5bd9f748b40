# So sánh các định dạng suy luận

Bảng dưới đây cung cấp so sánh chi tiết giữa các định dạng suy luận khác nhau trong Deep Research Core:

## Định dạng suy luận c<PERSON> bản

| Tính năng | Chain of Thought (CoT) | Tree of Thought (ToT) | ReAct | RAG |
|-----------|------------------------|------------------------|-------|-----|
| **Cách tiếp cận** | Suy luận tuần tự từng bước | Khám phá nhiều đường dẫn suy luận | Kết hợp suy luận và hành động | Truy xuất thông tin từ tài liệu |
| **<PERSON><PERSON> phức tạp** | Thấp | Cao | Trung bình | Trung bình |
| **Thời gian xử lý** | Nhanh | Chậm | Trung bình | Trung bình |
| **<PERSON><PERSON> chính xác** | Trung bình | Cao | Cao | Cao |
| **Khả năng mở rộng** | Tốt | Rất tốt | Tốt | Tốt |
| **Tương tác bên ngoài** | Không | Không | Có | Có (tài liệu) |
| **Khả năng giải thích** | Rất tốt | Tốt | Tốt | Trung bình |
| **Trường hợp sử dụng tốt nhất** | Bài toán toán học, suy luận logic | Vấn đề phức tạp, nhiều hướng tiếp cận | Tương tác với công cụ bên ngoài | Truy xuất thông tin từ tài liệu |
| **Trường hợp sử dụng kém nhất** | Vấn đề cần nhiều hướng tiếp cận | Vấn đề đơn giản, cần xử lý nhanh | Vấn đề không cần tương tác bên ngoài | Vấn đề không cần thông tin bên ngoài |
| **Hỗ trợ tiếng Việt** | Có | Có | Có | Có |

## Định dạng suy luận nâng cao

| Tính năng | Graph of Thoughts (GoT) | Multi-Stage Reasoning | Adaptive Chain of Thought |
|-----------|--------------------------|------------------------|----------------------------|
| **Cách tiếp cận** | Đồ thị các suy nghĩ với chu trình | Chia suy luận thành nhiều giai đoạn | Tự động điều chỉnh số bước suy luận |
| **Độ phức tạp** | Rất cao | Cao | Trung bình |
| **Thời gian xử lý** | Rất chậm | Chậm | Trung bình |
| **Độ chính xác** | Rất cao | Cao | Cao |
| **Khả năng mở rộng** | Rất tốt | Tốt | Tốt |
| **Tương tác bên ngoài** | Không | Có thể | Không |
| **Khả năng giải thích** | Tốt | Tốt | Rất tốt |
| **Trường hợp sử dụng tốt nhất** | Vấn đề phức tạp với nhiều phụ thuộc | Vấn đề đa lĩnh vực, nhiều giai đoạn | Vấn đề với độ phức tạp khác nhau |
| **Trường hợp sử dụng kém nhất** | Vấn đề đơn giản, cần xử lý nhanh | Vấn đề đơn giản, một lĩnh vực | Vấn đề cần nhiều hướng tiếp cận |
| **Hỗ trợ tiếng Việt** | Có | Có | Có |

## Định dạng suy luận kết hợp

| Tính năng | CoT-RAG | ToT-RAG | Multi-query ToT-RAG | Enhanced Source Attribution RAG |
|-----------|---------|---------|---------------------|--------------------------------|
| **Cách tiếp cận** | Suy luận từng bước với tài liệu | Khám phá nhiều đường dẫn với tài liệu | Phân tách truy vấn và khám phá với tài liệu | RAG với trích dẫn nguồn chính xác |
| **Độ phức tạp** | Trung bình | Cao | Rất cao | Cao |
| **Thời gian xử lý** | Trung bình | Chậm | Rất chậm | Trung bình |
| **Độ chính xác** | Cao | Rất cao | Rất cao | Cao |
| **Khả năng mở rộng** | Tốt | Tốt | Rất tốt | Tốt |
| **Tương tác bên ngoài** | Có (tài liệu) | Có (tài liệu) | Có (tài liệu) | Có (tài liệu) |
| **Khả năng giải thích** | Rất tốt | Tốt | Tốt | Rất tốt |
| **Trường hợp sử dụng tốt nhất** | Giải thích từng bước dựa trên tài liệu | Phân tích đa chiều dựa trên tài liệu | Phân tích toàn diện, đa khía cạnh | Báo cáo cần trích dẫn chính xác |
| **Trường hợp sử dụng kém nhất** | Vấn đề cần nhiều hướng tiếp cận | Vấn đề đơn giản, cần xử lý nhanh | Vấn đề đơn giản, một khía cạnh | Vấn đề không cần trích dẫn nguồn |
| **Hỗ trợ tiếng Việt** | Có | Có | Có | Có |

## Ví dụ mã nguồn cho từng định dạng suy luận

### Chain of Thought (CoT)

```python
from deep_research_core.reasoning import ChainOfThought

# Khởi tạo CoT
cot = ChainOfThought(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    language="vi"
)

# Xử lý truy vấn
result = cot.reason("Tính tổng của 17 và 28, sau đó nhân với 3")
print(result["answer"])  # Output: 135
```

### Tree of Thought (ToT)

```python
from deep_research_core.reasoning import TreeOfThought

# Khởi tạo ToT
tot = TreeOfThought(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    max_branches=3,
    max_depth=3,
    language="vi"
)

# Xử lý truy vấn
result = tot.reason("Giải bài toán: Có bao nhiêu cách để sắp xếp 4 người vào 4 ghế?")
print(result["answer"])  # Output: 24 cách (4!)
```

### ReAct

```python
from deep_research_core.reasoning import ReActReasoner
from deep_research_core.tools import WebSearchTool, CalculatorTool

# Khởi tạo ReAct với các công cụ
react = ReActReasoner(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    language="vi",
    tools=[WebSearchTool(), CalculatorTool()]
)

# Xử lý truy vấn
result = react.reason("Tìm kiếm giá cổ phiếu của Apple và tính tăng trưởng so với năm trước")
print(result["answer"])
```

### RAG

```python
from deep_research_core.reasoning import RAGReasoner
from deep_research_core.rag import SQLiteVectorRAG

# Khởi tạo RAG
rag = RAGReasoner(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    language="vi",
    vector_store=SQLiteVectorRAG(db_path="documents.db")
)

# Thêm tài liệu
rag.add_documents([
    {"content": "Nội dung tài liệu 1", "metadata": {"source": "Nguồn 1"}},
    {"content": "Nội dung tài liệu 2", "metadata": {"source": "Nguồn 2"}}
])

# Xử lý truy vấn
result = rag.process("Tóm tắt nội dung của các tài liệu")
print(result["answer"])
```

### CoT-RAG

```python
from deep_research_core.reasoning.integrated import CoTRAGIntegrator
from deep_research_core.reasoning import ChainOfThought, RAGReasoner

# Khởi tạo CoT và RAG
cot = ChainOfThought(provider="openai", model="gpt-4o")
rag = RAGReasoner(provider="openai", model="gpt-4o")

# Khởi tạo CoT-RAG
cot_rag = CoTRAGIntegrator(
    model=model,
    cot_reasoner=cot,
    rag_reasoner=rag,
    max_documents_per_step=3,
    language="vi"
)

# Xử lý truy vấn
result = cot_rag.reason("Giải thích quá trình quang hợp dựa trên tài liệu")
print(result["answer"])
```

### ToT-RAG

```python
from deep_research_core.reasoning import ToTRAG

# Khởi tạo ToT-RAG
tot_rag = ToTRAG(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    max_branches=3,
    max_depth=3,
    language="vi",
    adaptive=True
)

# Xử lý truy vấn
result = tot_rag.process(
    query="Phân tích các giải pháp cho biến đổi khí hậu",
    top_k=5
)
print(result["answer"])
```

### Multi-query ToT-RAG

```python
from deep_research_core.reasoning.combined import MultiQueryToTRAG
from deep_research_core.rag import SQLiteVectorRAG

# Khởi tạo RAG
rag = SQLiteVectorRAG(db_path="documents.db")

# Khởi tạo Multi-query ToT-RAG
mq_tot_rag = MultiQueryToTRAG(
    rag_instance=rag,
    provider="openai",
    model="gpt-4o",
    language="vi",
    mq_max_depth=2,
    tot_max_branches=3,
    tot_max_depth=2
)

# Xử lý truy vấn
result = mq_tot_rag.process(
    query="Phân tích tác động của AI đến kinh tế, xã hội, và đạo đức",
    top_k=5
)
print(result["answer"])
```

### Enhanced Source Attribution RAG

```python
from deep_research_core.reasoning import SourceAttributionRAG
from deep_research_core.rag import SQLiteVectorRAG

# Khởi tạo RAG
rag = SQLiteVectorRAG(db_path="documents.db")

# Khởi tạo Enhanced Source Attribution RAG
sa_rag = SourceAttributionRAG(
    rag_instance=rag,
    citation_style="apa",
    track_token_level=True,
    language="vi",
    auto_register_sources=True,
    auto_detect_usage=True
)

# Xử lý truy vấn
result = sa_rag.process(
    query="Tổng hợp các nghiên cứu gần đây về vaccine COVID-19",
    include_citations=True,
    include_bibliography=True
)
print(result["response_with_citations"])
print("\nTài liệu tham khảo:")
for entry in result["bibliography"]:
    print(f"- {entry}")
```
