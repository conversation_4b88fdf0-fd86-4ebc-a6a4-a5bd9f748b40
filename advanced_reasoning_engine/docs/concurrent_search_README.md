# Xử lý bất đồng bộ/đồng thời trong WebSearchAgent

## Tổng quan

Module này cung cấp khả năng thực hiện các tìm kiếm web đồng thời, gi<PERSON><PERSON> cải thiện hiệu suất khi cần thực hiện nhiều truy vấn tìm kiếm. Với việc hỗ trợ cả `ThreadPoolExecutor` và `asyncio`, WebSearchAgent có thể đạt được hiệu suất cao trong khi vẫn duy trì khả năng quản lý tài nguyên và ngăn chặn quá tải.

## Tính năng chính

1. **Xử lý bất đồng bộ với asyncio**
   - Thực hiện tìm kiếm bất đồng bộ với `async`/`await`
   - Hỗ trợ nhiều truy vấn đồng thời trong cùng một event loop
   - Tận dụng non-blocking I/O để tối ưu hóa hiệu suất

2. **Xử lý đồng thời với threading**
   - Sử dụng `ThreadPoolExecutor` cho đa luồng
   - Phù hợp với các ứng dụng không sử dụng asyncio
   - Hoạt động tốt với các phiên bản Python cũ hơn

3. **Xử lý theo lô (Batch Processing)**
   - Chia nhỏ danh sách truy vấn lớn thành các lô
   - Giới hạn số lượng yêu cầu đồng thời
   - Tránh quá tải và tuân thủ giới hạn tỷ lệ của API

4. **Hệ thống theo dõi và khôi phục lỗi**
   - Xử lý ngoại lệ riêng biệt cho từng truy vấn
   - Theo dõi và báo cáo lỗi chi tiết
   - Đảm bảo kết quả hợp lệ cho tất cả các truy vấn

## Cách sử dụng

### Cấu hình

Để sử dụng chức năng xử lý bất đồng bộ/đồng thời, bạn có thể cung cấp các tùy chọn cấu hình trong từ điển `config`:

```python
config = {
    "concurrent": {
        "max_workers": 5,           # Số lượng worker tối đa
        "use_asyncio": True,         # Sử dụng asyncio thay vì threading
        "batch_size": 3              # Kích thước lô (0 để tắt xử lý theo lô)
    },
    # Cấu hình khác...
}

agent = WebSearchAgentBase(config=config)
```

### Tìm kiếm bất đồng bộ với asyncio

```python
import asyncio

async def search_example():
    # Khởi tạo agent
    agent = WebSearchAgentBase()
    
    # Thực hiện tìm kiếm đơn bất đồng bộ
    result = await agent.async_search(
        query="Trí tuệ nhân tạo Việt Nam",
        language="vi"
    )
    print(result)
    
    # Thực hiện nhiều tìm kiếm bất đồng bộ
    queries = [
        "Machine learning algorithms",
        "Neural networks",
        "Deep learning applications"
    ]
    
    results = await agent.search_many_async(queries=queries)
    
    for i, result in enumerate(results):
        print(f"Kết quả cho '{queries[i]}': {len(result.get('results', []))} kết quả")

# Chạy hàm bất đồng bộ
asyncio.run(search_example())
```

### Tìm kiếm đồng thời với threading

```python
from src.deep_research_core.agents.web_search_agent_base import WebSearchAgentBase

# Khởi tạo agent
agent = WebSearchAgentBase(config={"concurrent": {"use_asyncio": False}})

# Thực hiện nhiều tìm kiếm đồng thời
queries = [
    "Machine learning algorithms",
    "Neural networks",
    "Deep learning applications",
    "Artificial intelligence"
]

results = agent.search_many(queries=queries)

for i, result in enumerate(results):
    print(f"Kết quả cho '{queries[i]}': {len(result.get('results', []))} kết quả")
```

### Xử lý theo lô

```python
from src.deep_research_core.agents.web_search_agent_base import WebSearchAgentBase

# Khởi tạo agent với cấu hình batch
agent = WebSearchAgentBase(config={"concurrent": {"batch_size": 2}})

# Danh sách truy vấn lớn
queries = [
    "Machine learning algorithms",
    "Neural networks",
    "Deep learning applications", 
    "Computer vision",
    "Natural language processing",
    "Reinforcement learning"
]

# Thực hiện tìm kiếm theo lô (xử lý 2 truy vấn mỗi lần)
results = agent.search_many(queries=queries)
```

## Lợi ích hiệu suất

Với việc sử dụng xử lý bất đồng bộ/đồng thời, bạn có thể đạt được cải thiện hiệu suất đáng kể:

1. **Thời gian xử lý nhanh hơn**: Thực hiện nhiều truy vấn cùng lúc, giảm thời gian chờ.
2. **Mở rộng quy mô tốt hơn**: Xử lý hiệu quả hàng nghìn truy vấn với quản lý tài nguyên tốt.
3. **Tối ưu hóa I/O**: Giảm thiểu thời gian chờ mạng bằng cách thực hiện nhiều yêu cầu cùng lúc.
4. **Kiểm soát tài nguyên**: Quản lý số lượng yêu cầu đồng thời thông qua cấu hình `max_workers` và `batch_size`.

## Các thực tiễn tốt nhất

1. **Điều chỉnh `max_workers`**:
   - Cho máy cục bộ: 4-8 workers thường là tối ưu
   - Cho máy chủ: Có thể tăng lên 16-32 workers tùy thuộc vào phần cứng

2. **Điều chỉnh `batch_size`**:
   - Đặt giá trị phù hợp dựa trên giới hạn tỷ lệ của API tìm kiếm
   - Giá trị hợp lý: 3-5 truy vấn mỗi lô

3. **Chọn giữa asyncio và threading**:
   - Sử dụng asyncio cho ứng dụng web và các ứng dụng I/O-bound
   - Sử dụng threading cho mã hiện có không hỗ trợ asyncio

4. **Xử lý ngoại lệ**:
   - Luôn kiểm tra trường `success` trong kết quả
   - Kiểm tra lỗi riêng lẻ trong các kết quả batched 