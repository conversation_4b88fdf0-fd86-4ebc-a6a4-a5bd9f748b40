# Model Evaluation and Selection

This document provides information about the model evaluation and selection utilities in Deep Research Core.

## Overview

Deep Research Core includes two main components for model evaluation and selection:

1. **ModelSelector**: Selects the most appropriate model for a given task based on performance metrics and requirements.
2. **ReasoningModelEvaluator**: Evaluates the performance of reasoning models on various tasks and benchmarks.

## ModelSelector

The `ModelSelector` class provides utilities to benchmark models, store performance metrics, and automatically select the best model for various tasks.

### Key Features

- Automatic model selection based on task type and requirements
- Performance tracking and caching for efficient selection
- Task type detection from prompt text
- Constraint-based filtering (e.g., minimum accuracy, maximum latency)

### Usage Example

```python
from deep_research_core.models import ModelSelector, GPTO1Model, DeepseekR1Model

# Initialize models
gpt_model = GPTO1Model(api_key="your-api-key", model_name="gpt-4o")
deepseek_model = DeepseekR1Model(api_key="your-api-key")

# Initialize model selector
model_selector = ModelSelector(
    available_models={
        "gpt-o1": gpt_model,
        "deepseek-r1": deepseek_model
    },
    cache_results=True,
    auto_benchmark=True
)

# Select model for a prompt
prompt = "Solve the following math problem: What is 123 + 456?"
model_id, model = model_selector.select_for_prompt(prompt)

# Use the selected model
response = model.generate(prompt)
print(f"Selected model: {model_id}")
print(f"Response: {response.get('text')}")
```

## ReasoningModelEvaluator

The `ReasoningModelEvaluator` class provides utilities to evaluate and compare the performance of reasoning models on various benchmarks.

### Key Features

- Evaluation on custom benchmark datasets
- Multiple metrics (accuracy, latency, token usage, custom metrics)
- Model comparison and ranking
- Strengths and weaknesses analysis
- Export to CSV for further analysis

### Evaluation Metrics

- **Accuracy**: Measures how often the model's response matches the expected output
- **Latency**: Measures the response time of the model
- **Token Usage**: Tracks prompt tokens, completion tokens, and total tokens used
- **Custom Metrics**: Support for user-defined evaluation metrics

### Usage Example

```python
from deep_research_core.models import ReasoningModelEvaluator, GPTO1Model

# Initialize models
gpt_model = GPTO1Model(api_key="your-api-key", model_name="gpt-4o")

# Initialize evaluator
evaluator = ReasoningModelEvaluator(
    models={"gpt-o1": gpt_model},
    benchmark_data_path="./benchmarks",
    results_dir="./evaluation_results"
)

# Evaluate model on benchmark
results = evaluator.evaluate_model(
    model_id="gpt-o1",
    benchmark_name="math_benchmark",
    metrics=["accuracy", "latency", "token_usage"]
)

# Print results
print(f"Accuracy: {results['metrics']['accuracy']['mean']:.2%}")
print(f"Latency: {results['metrics']['latency']['mean']:.2f}s")

# Export results to CSV
evaluator.export_results_to_csv(
    results={"gpt-o1": results},
    output_path="./evaluation_results.csv"
)

# Compare multiple models
comparison = evaluator.compare_models(
    benchmark_name="math_benchmark",
    model_ids=["gpt-o1", "deepseek-r1"],
    metrics=["accuracy", "latency"]
)

# Print rankings
print(f"Accuracy ranking: {comparison['rankings']['accuracy']}")
print(f"Latency ranking: {comparison['rankings']['latency']}")
```

## Creating Benchmark Datasets

A benchmark dataset is a JSON file containing a list of examples, where each example has:

- `prompt`: The input text to send to the model
- `expected`: The expected output (for accuracy evaluation)
- `system_message` (optional): System message to set context

Example benchmark file:

```json
[
  {
    "prompt": "What is 5 + 3?",
    "expected": "8",
    "system_message": "You are a helpful assistant that provides accurate arithmetic calculations."
  },
  {
    "prompt": "If a train travels at 60 km/h, how far will it travel in 2.5 hours?",
    "expected": "150 km",
    "system_message": "You are a helpful assistant that provides accurate calculations."
  }
]
```

## Best Practices

1. **Use deterministic settings** for evaluation (temperature=0) to get consistent results
2. **Create specific benchmark datasets** for different task types
3. **Use custom metrics** for task-specific evaluation criteria
4. **Regular re-evaluation** to account for model updates and improvements
5. **Export and analyze results** to identify patterns and improvement areas 