# Xử lý truy vấn so sánh với Crawlee

## Vấn đề hiện tại

Khi sử dụng các công cụ tìm kiếm web như SearX và <PERSON>, chúng ta gặp phải nhiều vấn đề:

1. **Bị chặn và giới hạn tốc độ**: <PERSON><PERSON><PERSON> hết các instance SearX trả về lỗi 429 (Too Many Requests) hoặc 403 (Forbidden)
2. **Timeout và lỗi kết nối**: Nhiều instance bị timeout hoặc không thể kết nối
3. **Không hỗ trợ tốt tiếng Việt**: Đặc biệt là với các truy vấn so sánh phức tạp

Khi người dùng đưa ra truy vấn so sánh (ví dụ: "So sánh React và Angular cho phát triển web"), chúng ta cần một cách tiếp cận khác hiệu quả hơn.

## Giải pháp đề xuất: Sử dụng Crawlee

Crawlee là một thư viện web scraping mạnh mẽ có thể giúp chúng ta vượt qua các hạn chế của SearX và Qwant. Dưới đây là cách chúng ta có thể sử dụng Crawlee để xử lý truy vấn so sánh:

### 1. Phát hiện truy vấn so sánh

Chúng ta đã triển khai cơ chế phát hiện truy vấn so sánh trong `web_search_comparison.py`:

```python
def detect_comparison_query(query: str) -> Dict[str, Any]:
    """
    Phát hiện nếu truy vấn yêu cầu so sánh nhiều nguồn.
    """
    # Kiểm tra các từ khóa so sánh tiếng Anh và tiếng Việt
    comparison_terms = [
        "compare", "comparison", "versus", "vs", "difference", "similarities",
        "so sánh", "đối chiếu", "khác nhau", "giống nhau", "ưu điểm", "nhược điểm"
    ]
    
    # Trích xuất các thực thể được so sánh
    entities = extract_comparison_entities(query)
    
    # Xác định nếu truy vấn yêu cầu so sánh
    requires_comparison = len(matches) > 0 and len(entities) >= 2
    
    return {
        "requires_comparison": requires_comparison,
        "comparison_terms_found": matches,
        "entities": entities,
        "entity_count": len(entities)
    }
```

### 2. Sử dụng Crawlee cho từng thực thể

Thay vì sử dụng SearX hoặc Qwant, chúng ta có thể sử dụng Crawlee để tìm kiếm thông tin cho từng thực thể:

```python
def search_with_crawlee_comparison(query: str, entities: List[str], num_results: int = 5) -> Dict[str, Any]:
    """
    Sử dụng Crawlee để tìm kiếm thông tin cho truy vấn so sánh.
    """
    entity_results = {}
    
    for entity in entities:
        # Tạo truy vấn cho thực thể cụ thể
        entity_query = f"{entity} {query}"
        
        # Tạo danh sách URL khởi đầu
        start_urls = [
            f"https://www.google.com/search?q={urllib.parse.quote_plus(entity_query)}",
            f"https://duckduckgo.com/?q={urllib.parse.quote_plus(entity_query)}",
            f"https://www.bing.com/search?q={urllib.parse.quote_plus(entity_query)}"
        ]
        
        # Thực hiện crawl với Crawlee
        results = crawl_with_playwright(
            start_urls=start_urls,
            max_depth=2,
            max_pages=num_results * 3,
            max_results=num_results,
            timeout=60
        )
        
        # Lưu kết quả
        entity_results[entity] = results
    
    # Kết hợp kết quả
    combined_results = {
        "success": True,
        "search_method": "crawlee_comparison",
        "query": query,
        "entities": entities,
        "entity_results": entity_results,
        "results": []
    }
    
    # Thêm kết quả từ mỗi thực thể vào kết quả kết hợp
    for entity, results in entity_results.items():
        for result in results.get("results", []):
            result["entity"] = entity
            combined_results["results"].append(result)
    
    return combined_results
```

### 3. Triển khai crawl_with_playwright

Chúng ta cần triển khai hàm `crawl_with_playwright` để sử dụng Playwright thông qua Crawlee:

```javascript
// crawlee_comparison.js
const { PlaywrightCrawler } = require('crawlee');
const { writeFileSync } = require('fs');

// Hàm trích xuất thông tin từ trang web
const extractPageInfo = (page) => {
    return page.evaluate(() => {
        // Trích xuất tiêu đề, nội dung, v.v.
        const title = document.title;
        const content = document.body.innerText;
        
        // Trích xuất các đoạn văn bản có liên quan
        const paragraphs = Array.from(document.querySelectorAll('p, h1, h2, h3, h4, h5, h6'))
            .map(el => el.innerText)
            .filter(text => text.length > 50);
        
        return {
            title,
            content: paragraphs.join('\n\n').substring(0, 5000),
            url: window.location.href
        };
    });
};

// Hàm chính để crawl
async function crawlWithPlaywright(startUrls, maxDepth, maxPages, maxResults, timeout) {
    const results = [];
    const visitedUrls = new Set();
    
    const crawler = new PlaywrightCrawler({
        maxRequestsPerCrawl: maxPages,
        timeout: timeout * 1000,
        
        // Hàm xử lý mỗi trang
        async requestHandler({ page, request, enqueueLinks }) {
            // Kiểm tra độ sâu
            const depth = request.userData.depth || 0;
            if (depth > maxDepth) return;
            
            // Trích xuất thông tin
            const pageInfo = await extractPageInfo(page);
            
            // Thêm vào kết quả nếu chưa đủ
            if (results.length < maxResults && !visitedUrls.has(pageInfo.url)) {
                results.push(pageInfo);
                visitedUrls.add(pageInfo.url);
            }
            
            // Thêm các liên kết vào hàng đợi
            if (depth < maxDepth) {
                await enqueueLinks({
                    userData: { depth: depth + 1 }
                });
            }
        }
    });
    
    // Bắt đầu crawl
    await crawler.run(startUrls);
    
    // Lưu kết quả vào file tạm thời
    writeFileSync('crawlee_results.json', JSON.stringify(results));
    
    return {
        success: true,
        results: results
    };
}

// Xử lý tham số dòng lệnh
const args = process.argv.slice(2);
const startUrls = JSON.parse(args[0]);
const maxDepth = parseInt(args[1]);
const maxPages = parseInt(args[2]);
const maxResults = parseInt(args[3]);
const timeout = parseInt(args[4]);

// Chạy crawler
crawlWithPlaywright(startUrls, maxDepth, maxPages, maxResults, timeout)
    .catch(error => {
        console.error(error);
        process.exit(1);
    });
```

### 4. Tích hợp với Python

Chúng ta cần một hàm Python để gọi script Crawlee:

```python
def crawl_with_playwright(start_urls: List[str], max_depth: int = 2, max_pages: int = 20, max_results: int = 10, timeout: int = 60) -> Dict[str, Any]:
    """
    Sử dụng Playwright thông qua Crawlee để crawl các trang web.
    
    Args:
        start_urls: Danh sách URL khởi đầu
        max_depth: Độ sâu tối đa để crawl
        max_pages: Số trang tối đa để crawl
        max_results: Số kết quả tối đa để trả về
        timeout: Thời gian chờ tối đa (giây)
        
    Returns:
        Dictionary chứa kết quả crawl
    """
    # Đường dẫn đến script Crawlee
    script_path = os.path.join(os.path.dirname(__file__), 'crawlee_comparison.js')
    
    # Kiểm tra xem script có tồn tại không
    if not os.path.exists(script_path):
        raise FileNotFoundError(f"Crawlee script not found at {script_path}")
    
    # Đường dẫn đến file kết quả tạm thời
    results_path = os.path.join(os.path.dirname(__file__), 'crawlee_results.json')
    
    # Xóa file kết quả cũ nếu có
    if os.path.exists(results_path):
        os.remove(results_path)
    
    # Chuyển đổi tham số thành chuỗi JSON
    start_urls_json = json.dumps(start_urls)
    
    # Chạy script Crawlee
    try:
        subprocess.run(
            ['node', script_path, start_urls_json, str(max_depth), str(max_pages), str(max_results), str(timeout)],
            check=True,
            timeout=timeout * 2  # Thời gian chờ gấp đôi timeout của crawler
        )
        
        # Đọc kết quả từ file
        with open(results_path, 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        # Xóa file kết quả
        os.remove(results_path)
        
        return {
            "success": True,
            "results": results
        }
    except subprocess.SubprocessError as e:
        return {
            "success": False,
            "error": str(e),
            "results": []
        }
```

## Ưu điểm của giải pháp

1. **Vượt qua các biện pháp chống bot**: Playwright có thể giả lập trình duyệt thật, giúp vượt qua các biện pháp chống bot
2. **Hỗ trợ JavaScript**: Có thể truy cập các trang web sử dụng JavaScript động
3. **Trích xuất nội dung phong phú**: Có thể trích xuất nhiều thông tin hơn từ các trang web
4. **Không bị giới hạn tốc độ**: Không phụ thuộc vào các API bên thứ ba có giới hạn tốc độ
5. **Hỗ trợ tốt hơn cho tiếng Việt**: Có thể truy cập trực tiếp các trang web tiếng Việt

## Nhược điểm và giải pháp

1. **Tốc độ chậm hơn**: Crawling với Playwright chậm hơn so với gọi API trực tiếp
   - Giải pháp: Sử dụng cache để lưu kết quả và giới hạn số trang crawl

2. **Yêu cầu cài đặt Playwright**: Cần cài đặt Playwright và các trình duyệt liên quan
   - Giải pháp: Thêm hướng dẫn cài đặt và kiểm tra tự động

3. **Sử dụng nhiều tài nguyên**: Crawling với Playwright sử dụng nhiều CPU và RAM
   - Giải pháp: Giới hạn số lượng trình duyệt đồng thời và thời gian chạy

## Kết luận

Sử dụng Crawlee với Playwright là một giải pháp mạnh mẽ để xử lý truy vấn so sánh, đặc biệt là khi các công cụ tìm kiếm như SearX và Qwant gặp vấn đề. Giải pháp này có thể vượt qua các biện pháp chống bot, hỗ trợ JavaScript, và trích xuất nội dung phong phú từ các trang web.

Mặc dù có một số nhược điểm như tốc độ chậm hơn và yêu cầu cài đặt thêm, nhưng những ưu điểm của giải pháp này làm cho nó trở thành một lựa chọn tốt cho việc xử lý truy vấn so sánh, đặc biệt là trong tiếng Việt.
