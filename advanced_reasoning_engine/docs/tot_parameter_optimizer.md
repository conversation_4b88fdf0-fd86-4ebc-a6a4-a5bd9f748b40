# Tự động điều chỉnh tham số cho Tree of Thought (ToT)

Tài liệu này mô tả tính năng tự động điều chỉnh tham số cho Tree of Thought (ToT) dựa trên độ phức tạp của truy vấn.

## Tổng quan

Tree of Thought (ToT) là một kỹ thuật suy luận mạnh mẽ, cho phép khám phá nhiều đường dẫn suy luận khác nhau và chọn đường dẫn tốt nhất. Hiệu suất của ToT phụ thuộc nhiều vào các tham số như độ sâu tối đa (`max_depth`), số nhánh tối đa (`max_branches`), và nhiệt độ (`temperature`).

Tính năng tự động điều chỉnh tham số sử dụng lớp `TOTParameterOptimizer` để phân tích độ phức tạp của truy vấn và điều chỉnh các tham số ToT một cách tự động, giúp tối ưu hóa hiệu suất và chất lượng kết quả.

## Cách hoạt động

### 1. Phân tích độ phức tạp của truy vấn

`TOTParameterOptimizer` phân tích độ phức tạp của truy vấn dựa trên nhiều yếu tố:

- **Độ dài truy vấn**: Truy vấn dài thường phức tạp hơn
- **Cấu trúc ngữ pháp**: Số lượng dấu phẩy, dấu chấm phẩy, từ nối logic
- **Câu hỏi lồng nhau**: Phát hiện các câu hỏi nhiều phần
- **Lĩnh vực chuyên ngành**: Phát hiện các lĩnh vực kỹ thuật, khoa học, triết học, toán học
- **Loại truy vấn**: Phân tích, sáng tạo, thực tế, giải quyết vấn đề, nhiều bước

### 2. Điều chỉnh tham số dựa trên độ phức tạp

Dựa trên kết quả phân tích, `TOTParameterOptimizer` điều chỉnh các tham số:

- **Độ sâu tối đa (max_depth)**: Tăng cho truy vấn phức tạp, giảm cho truy vấn đơn giản
- **Số nhánh tối đa (max_branches)**: Tăng cho truy vấn sáng tạo hoặc có nhiều giải pháp
- **Nhiệt độ (temperature)**: Tăng cho truy vấn sáng tạo, giảm cho truy vấn phân tích hoặc thực tế

### 3. Điều chỉnh tham số nâng cao

Ngoài các tham số cơ bản, `TOTParameterOptimizer` còn điều chỉnh các tham số nâng cao:

- **Cắt tỉa sớm (early_pruning)**: Bật cho truy vấn phức tạp để tối ưu hóa hiệu suất
- **Đánh giá đa tiêu chí (multi_criteria_evaluation)**: Bật cho truy vấn phân tích
- **Chia sẻ thông tin (information_sharing)**: Bật cho truy vấn phức tạp
- **Khám phá song song (parallel_exploration)**: Bật cho truy vấn rất phức tạp

## Cách sử dụng

### Sử dụng cơ bản

```python
from deep_research_core.reasoning import TreeOfThought

# Khởi tạo TreeOfThought với tính năng tự động điều chỉnh tham số
tot = TreeOfThought(
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free",
    max_branches=3,  # Tham số ban đầu
    max_depth=2,     # Tham số ban đầu
    adaptive=True,   # Bật tính năng tự động điều chỉnh
    verbose=True     # In thông tin chi tiết
)

# Truy vấn đơn giản
simple_query = "Thủ đô của Việt Nam là gì?"
simple_result = tot.reason(query=simple_query)

# Truy vấn phức tạp
complex_query = "Phân tích và so sánh các giải pháp khác nhau để giải quyết vấn đề biến đổi khí hậu."
complex_result = tot.reason(query=complex_query)
```

### Sử dụng nâng cao

```python
from deep_research_core.reasoning import TreeOfThought

# Khởi tạo TreeOfThought với tính năng tự động điều chỉnh tham số và tối ưu hóa nâng cao
tot = TreeOfThought(
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free",
    max_branches=3,
    max_depth=2,
    adaptive=True,
    use_advanced_optimization=True,  # Bật tối ưu hóa nâng cao
    token_budget=100000,             # Ngân sách token
    parallel_exploration=True,       # Bật khám phá song song
    max_workers=3,                   # Số lượng worker tối đa
    early_pruning=True,              # Bật cắt tỉa sớm
    verbose=True
)

# Truy vấn phức tạp
complex_query = "Phân tích và so sánh các giải pháp khác nhau để giải quyết vấn đề biến đổi khí hậu."
result = tot.reason(query=complex_query)

# Xem tham số đã được điều chỉnh
print(f"Tham số đã điều chỉnh: {result['parameters']}")
```

### Sử dụng trực tiếp TOTParameterOptimizer

```python
from deep_research_core.reasoning.tot.parameter_optimizer import TOTParameterOptimizer

# Khởi tạo TOTParameterOptimizer
optimizer = TOTParameterOptimizer(
    base_max_depth=3,
    base_max_branches=3,
    base_temperature=0.7,
    adaptive=True,
    verbose=True
)

# Phân tích độ phức tạp của truy vấn
query = "Phân tích tác động của trí tuệ nhân tạo đến thị trường lao động trong tương lai."
complexity_analysis = optimizer.analyze_query_complexity(query)
print(f"Độ phức tạp tổng thể: {complexity_analysis['overall_complexity']:.2f}")
print(f"Loại truy vấn: {complexity_analysis['query_types']}")

# Điều chỉnh tham số dựa trên độ phức tạp
adjusted_params = optimizer.adjust_parameters_for_complexity(query)
print(f"Tham số đã điều chỉnh: {adjusted_params}")

# Tối ưu hóa tất cả tham số
optimized_params = optimizer.optimize_parameters_for_query(query)
print(f"Tham số đã tối ưu hóa: {optimized_params}")
```

## Các loại truy vấn và điều chỉnh tham số

| Loại truy vấn | Điều chỉnh độ sâu | Điều chỉnh số nhánh | Điều chỉnh nhiệt độ |
|---------------|-------------------|---------------------|---------------------|
| Phân tích     | +0.3              | +0.2                | -0.1                |
| Sáng tạo      | +0.1              | +0.3                | +0.2                |
| Thực tế       | -0.2              | -0.2                | -0.2                |
| Giải quyết vấn đề | +0.3          | +0.2                | 0.0                 |
| Nhiều bước    | +0.4              | +0.1                | -0.1                |

## Hỗ trợ tiếng Việt

`TOTParameterOptimizer` hỗ trợ đầy đủ tiếng Việt, với khả năng phát hiện các chỉ báo độ phức tạp và loại truy vấn trong tiếng Việt:

- **Phân tích**: "phân tích", "so sánh", "đánh giá", "ưu nhược điểm", v.v.
- **Sáng tạo**: "sáng tạo", "thiết kế", "tưởng tượng", "phát minh", v.v.
- **Thực tế**: "là gì", "định nghĩa", "giải thích", "mô tả", v.v.
- **Giải quyết vấn đề**: "giải quyết", "giải pháp", "cải thiện", "tối ưu hóa", v.v.
- **Nhiều bước**: "các bước để", "quy trình", "phương pháp", "hướng dẫn", v.v.

## Ví dụ

Xem file `tests/unit/reasoning/tot/test_tot_parameter_adjustment.py` để biết thêm chi tiết về cách sử dụng tính năng tự động điều chỉnh tham số.

```bash
python tests/unit/reasoning/tot/test_tot_parameter_adjustment.py
```
