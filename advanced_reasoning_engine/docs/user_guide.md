# User Guide

## Deep Research Core User Guide

This document provides a comprehensive guide to using the Deep Research Core, including all the enhancements implemented in the latest version.

## Table of Contents

- [Introduction](#introduction)
- [Installation](#installation)
- [Basic Usage](#basic-usage)
- [Enhanced RAG Features](#enhanced-rag-features)
  - [Approximate Nearest Neighbor Search](#approximate-nearest-neighbor-search)
  - [R-tree Indexing](#r-tree-indexing)
  - [Adaptive Weighting](#adaptive-weighting)
  - [Semantic Chunking](#semantic-chunking)
  - [Hierarchical Documents](#hierarchical-documents)
  - [Vietnamese Language Support](#vietnamese-language-support)
- [Domain-Specific Evaluation](#domain-specific-evaluation)
- [Examples](#examples)
- [Troubleshooting](#troubleshooting)

---

## Introduction

Deep Research Core is a powerful library for building Retrieval-Augmented Generation (RAG) systems. It provides a range of features for efficient vector search, document processing, and language model integration.

The core of the library is the `SQLiteVectorRAG` class, which provides a lightweight yet powerful RAG implementation using SQLite for document storage and vector search. This base implementation has been extended with several enhancements to improve performance, flexibility, and multilingual support.

## Installation

To install Deep Research Core, you can use pip:

```bash
pip install deep-research-core
```

Or install from source:

```bash
git clone https://github.com/bazi88/deepresearch.git
cd deepresearch
pip install -e .
```

### Dependencies

Deep Research Core requires the following dependencies:

- Python 3.8+
- SQLite 3.35.0+
- sentence-transformers
- numpy
- torch
- faiss-cpu (or faiss-gpu)
- nltk
- transformers

For Vietnamese language support, additional dependencies are required:

- vncorenlp
- underthesea

## Basic Usage

Here's a simple example of how to use the basic `SQLiteVectorRAG`:

```python
from deep_research_core.reasoning.sqlite_vector_rag import SQLiteVectorRAG

# Initialize RAG
rag = SQLiteVectorRAG(
    db_path="my_documents.db",
    embedding_model="all-MiniLM-L6-v2",
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free",
    api_key="your-api-key"
)

# Add documents
documents = [
    {
        "content": "Artificial Intelligence (AI) is the simulation of human intelligence processes by machines.",
        "source": "AI Definition",
        "title": "What is AI?",
        "date": "2023-01-01"
    },
    {
        "content": "Machine Learning is a subset of AI that enables systems to learn and improve from experience.",
        "source": "ML Definition",
        "title": "What is Machine Learning?",
        "date": "2023-01-02"
    }
]

rag.add_documents(documents)

# Process a query
result = rag.process("What is the difference between AI and Machine Learning?")

print(result["answer"])
```

## Enhanced RAG Features

Deep Research Core provides several enhanced RAG implementations that extend the base `SQLiteVectorRAG` with additional features.

### Approximate Nearest Neighbor Search

The `SQLiteVectorRAGWithANN` class uses FAISS for efficient approximate nearest neighbor search, which is much faster than brute-force cosine similarity calculation for large document collections.

```python
from deep_research_core.reasoning.sqlite_vector_rag_ann import SQLiteVectorRAGWithANN

# Initialize RAG with ANN
rag = SQLiteVectorRAGWithANN(
    db_path="my_documents.db",
    embedding_model="all-MiniLM-L6-v2",
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free",
    api_key="your-api-key",
    ann_index_type="IVFFlat",  # Options: "Flat", "IVFFlat", "IVFPQ", "HNSW"
    ann_metric_type="cosine",
    ann_nlist=100
)

# Use the same API as SQLiteVectorRAG
rag.add_documents(documents)
result = rag.process("What is the difference between AI and Machine Learning?")
```

#### Index Types

- `Flat`: Exact search (no approximation), slowest but most accurate
- `IVFFlat`: Inverted file with flat quantizer, good balance of speed and accuracy
- `IVFPQ`: Inverted file with product quantization, fastest but less accurate
- `HNSW`: Hierarchical Navigable Small World, very fast with good accuracy

### R-tree Indexing

The `SQLiteVectorRAGWithRTree` class uses SQLite's R-tree extension for efficient spatial indexing of vector embeddings.

```python
from deep_research_core.reasoning.sqlite_vector_rag_rtree import SQLiteVectorRAGWithRTree

# Initialize RAG with R-tree
rag = SQLiteVectorRAGWithRTree(
    db_path="my_documents.db",
    embedding_model="all-MiniLM-L6-v2",
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free",
    api_key="your-api-key",
    pca_dimensions=20  # Number of dimensions for R-tree (lower is faster)
)

# Use the same API as SQLiteVectorRAG
rag.add_documents(documents)
result = rag.process("What is the difference between AI and Machine Learning?")
```

### Adaptive Weighting

The `SQLiteVectorRAGWithAdaptiveWeighting` class automatically determines the optimal weight between vector search and keyword search based on query characteristics.

```python
from deep_research_core.reasoning.sqlite_vector_rag_adaptive import SQLiteVectorRAGWithAdaptiveWeighting

# Initialize RAG with adaptive weighting
rag = SQLiteVectorRAGWithAdaptiveWeighting(
    db_path="my_documents.db",
    embedding_model="all-MiniLM-L6-v2",
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free",
    api_key="your-api-key",
    weighting_strategy="auto",  # Options: "auto", "query_length", "query_specificity", "query_type"
    min_weight=0.3,
    max_weight=0.9,
    default_weight=0.7
)

# Search with query analysis
results, query_analysis = rag.search("What is the difference between AI and Machine Learning?", analyze_query=True)

print(f"Query analysis: {query_analysis}")
print(f"Adaptive weight: {query_analysis['final_weight']}")
```

#### Weighting Strategies

- `query_length`: Longer queries favor keyword search, shorter queries favor vector search
- `query_specificity`: More specific queries favor keyword search, general queries favor vector search
- `query_type`: Factual queries favor keyword search, conceptual queries favor vector search
- `auto`: Combines all strategies

### Semantic Chunking

The `SQLiteVectorRAGWithSemanticChunking` class splits documents based on semantic structure rather than fixed character counts.

```python
from deep_research_core.reasoning.sqlite_vector_rag_semantic import SQLiteVectorRAGWithSemanticChunking

# Initialize RAG with semantic chunking
rag = SQLiteVectorRAGWithSemanticChunking(
    db_path="my_documents.db",
    embedding_model="all-MiniLM-L6-v2",
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free",
    api_key="your-api-key",
    chunking_strategy="hybrid",  # Options: "paragraph", "section", "hybrid", "sentence"
    respect_sections=True,
    respect_sentences=True
)

# Analyze document structure
document = {
    "content": "# Introduction\n\nThis is the introduction.\n\n## Section 1\n\nThis is section 1.\n\n## Section 2\n\nThis is section 2.",
    "source": "Test Document",
    "title": "Test Document",
    "date": "2023-01-01"
}

analysis = rag.analyze_document(document["content"])
print(f"Document analysis: {analysis}")

# Add documents with semantic chunking
rag.add_documents([document])
```

#### Chunking Strategies

- `paragraph`: Chunk by paragraphs
- `section`: Chunk by sections (headings)
- `sentence`: Chunk by sentences
- `hybrid`: Combine section, paragraph, and sentence chunking

### Hierarchical Documents

The `SQLiteVectorRAGWithHierarchicalDocuments` class represents documents at multiple levels of granularity (document, section, paragraph, sentence).

```python
from deep_research_core.reasoning.sqlite_vector_rag_hierarchical import SQLiteVectorRAGWithHierarchicalDocuments

# Initialize RAG with hierarchical documents
rag = SQLiteVectorRAGWithHierarchicalDocuments(
    db_path="my_documents.db",
    embedding_model="all-MiniLM-L6-v2",
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free",
    api_key="your-api-key",
    hierarchical_levels=["document", "section", "paragraph"]
)

# Add documents with hierarchical representation
rag.add_documents([document])

# Search at specific levels
section_results = rag.search("What is section 1?", chunk_types=["section"])
paragraph_results = rag.search("What is section 1?", chunk_types=["paragraph"])
multi_level_results = rag.search("What is section 1?", chunk_types=["section", "paragraph"])

# Get related chunks
if section_results:
    section_id = section_results[0]["id"]
    parent_chunks = rag.get_related_chunks(section_id, relation_type="parent")
    children_chunks = rag.get_related_chunks(section_id, relation_type="children")
```

### Vietnamese Language Support

The `SQLiteVectorRAGVietnamese` class provides specialized Vietnamese embedding models and language-specific processing.

```python
from deep_research_core.reasoning.sqlite_vector_rag_vietnamese import SQLiteVectorRAGVietnamese

# Initialize RAG with Vietnamese support
rag = SQLiteVectorRAGVietnamese(
    db_path="my_documents.db",
    embedding_model="phobert",  # Options: "phobert", "viebert", "xlm-roberta-vi", "multilingual-e5"
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free",
    api_key="your-api-key"
)

# Add Vietnamese documents
vietnamese_documents = [
    {
        "content": "Trí tuệ nhân tạo (AI) là khả năng của một hệ thống máy tính để thực hiện các nhiệm vụ thường đòi hỏi trí thông minh của con người.",
        "source": "Định nghĩa AI",
        "title": "Trí tuệ nhân tạo là gì?",
        "date": "2023-01-01"
    }
]

rag.add_documents(vietnamese_documents)

# Process a Vietnamese query
result = rag.process("Trí tuệ nhân tạo là gì?")
```

## Domain-Specific Evaluation

Deep Research Core provides domain-specific evaluation metrics for different domains such as medical, legal, technical, and educational.

```python
from deep_research_core.evaluation.domain_metrics import get_domain_metrics
from deep_research_core.evaluation.domain_evaluator import DomainEvaluator

# Get domain-specific metrics
medical_metrics = get_domain_metrics("medical")

# Evaluate a response
response = {
    "query": "What are the treatment options for type 2 diabetes?",
    "answer": "Treatment options for type 2 diabetes include lifestyle modifications and medications..."
}

metrics = medical_metrics.calculate_domain_metrics(response)
print(f"Domain metrics: {metrics}")

# Use domain evaluator
evaluator = DomainEvaluator(
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free",
    language="en",
    domain="medical",
    expected_terms={
        "treatment": ["metformin", "insulin", "lifestyle modification", "diet", "exercise"]
    }
)

# Evaluate CoTRAG with domain-specific metrics
results = evaluator.evaluate_cotrag_with_domain(question_type="treatment")
```

## Examples

Deep Research Core includes several examples that demonstrate how to use the different features:

- `examples/test_sqlite_vector_rag_ann.py`: Demonstrates ANN search with FAISS
- `examples/test_sqlite_vector_rag_rtree.py`: Demonstrates R-tree indexing
- `examples/test_sqlite_vector_rag_adaptive.py`: Demonstrates adaptive weighting
- `examples/test_sqlite_vector_rag_semantic.py`: Demonstrates semantic chunking
- `examples/test_sqlite_vector_rag_hierarchical.py`: Demonstrates hierarchical documents
- `examples/test_vietnamese_embeddings.py`: Demonstrates Vietnamese language support
- `examples/test_domain_evaluation.py`: Demonstrates domain-specific evaluation

## Troubleshooting

### Common Issues

#### FAISS Installation

If you encounter issues with FAISS installation, try installing the CPU version:

```bash
pip install faiss-cpu
```

Or the GPU version if you have a compatible GPU:

```bash
pip install faiss-gpu
```

#### SQLite R-tree Extension

The R-tree extension requires SQLite 3.35.0 or higher. To check your SQLite version:

```python
import sqlite3
print(sqlite3.sqlite_version)
```

#### Vietnamese Models

Vietnamese models require additional dependencies:

```bash
pip install vncorenlp underthesea
```

You may also need to download the VnCoreNLP model:

```python
from vncorenlp import VnCoreNLP
VnCoreNLP.download_model()
```

### Performance Optimization

- For large document collections, use `SQLiteVectorRAGWithANN` with the `HNSW` index type
- For faster document addition, use batch processing with larger batch sizes
- For better search quality, use `SQLiteVectorRAGWithAdaptiveWeighting` with the `auto` strategy
- For better document chunking, use `SQLiteVectorRAGWithSemanticChunking` with the `hybrid` strategy
