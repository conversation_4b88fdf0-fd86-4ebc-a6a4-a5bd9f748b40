# Source Attribution & Citation Guide

This guide explains how to use the Source Attribution & Citation module in the Deep Research Core library.

## Overview

The Source Attribution & Citation module provides functionality to track and cite information sources used in the reasoning process, enabling transparent attribution and citation of information sources in AI-generated content.

Key features:
- Multiple citation styles (APA, MLA, Chicago, IEEE)
- Token-level tracking of information usage
- Source credibility evaluation
- Automatic citation generation
- Bibliography generation
- Vietnamese language support
- Integration with RAG systems

## Basic Usage

### Initializing Source Attribution

```python
from deep_research_core.reasoning.source_attribution import SourceAttribution

# Initialize SourceAttribution
source_attribution = SourceAttribution(
    citation_style="apa",  # Options: "apa", "mla", "chicago", "ieee"
    track_token_level=True,  # Enable token-level tracking
    language="en",  # Options: "en", "vi"
    auto_detect_sources=True,  # Enable automatic source detection
    citation_format="inline"  # Options: "inline", "footnote", "endnote", "bibliography"
)
```

### Registering Sources

```python
# Register a single source
source_attribution.register_source(
    "source1",
    {
        "title": "Example Document",
        "content": "This is the content of the document.",
        "author": "<PERSON>",
        "publication_date": "2023",
        "url": "https://example.com/doc",
        "publisher": "Example Publisher"
    }
)

# Register multiple sources
sources = [
    {
        "id": "source2",
        "title": "Another Document",
        "content": "This is another document.",
        "author": "Jane Smith"
    },
    {
        "id": "source3",
        "title": "Third Document",
        "content": "This is a third document.",
        "author": "Bob Johnson"
    }
]
source_ids = source_attribution.register_sources_batch(sources)
```

### Tracking Information Usage

```python
# Track information usage manually
source_attribution.track_information_usage(
    content="This is the content of the document.",
    source_id="source1",
    span=(0, 35)  # Optional: specify the span of text
)

# Detect source usage automatically
text = "This is the content of the document. This is another document."
detected_sources = source_attribution.detect_source_usage(text)
```

### Generating Citations

```python
# Generate a citation for a source
citation = source_attribution.generate_citation("source1")
print(f"Citation: {citation}")

# Add citations to text
text_with_citations = source_attribution.add_citations_to_text(
    "This is the content of the document. This is another document."
)
print(f"Text with citations: {text_with_citations}")

# Get bibliography
bibliography = source_attribution.get_bibliography()
print("Bibliography:")
for entry in bibliography:
    print(f"- {entry}")

# Get citation metrics
metrics = source_attribution.get_citation_metrics()
print(f"Citation metrics: {metrics}")
```

## Integration with RAG

The Source Attribution module can be integrated with RAG systems to automatically track and cite sources used in RAG-generated content.

```python
from deep_research_core.reasoning.source_attribution_rag import SourceAttributionRAG
from deep_research_core.reasoning.sqlite_vector_rag import SQLiteVectorRAG

# Initialize RAG
rag = SQLiteVectorRAG(
    db_path="documents.db",
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free"
)

# Add documents to RAG
rag.add_documents(documents)

# Initialize SourceAttributionRAG
source_attribution_rag = SourceAttributionRAG(
    rag_instance=rag,
    citation_style="apa",
    track_token_level=True,
    language="en",
    auto_register_sources=True,
    auto_detect_usage=True,
    citation_format="inline"
)

# Process a query with source attribution
result = source_attribution_rag.process(
    query="What is artificial intelligence?",
    include_citations=True,
    include_bibliography=True
)

# Print results
print(f"Original response: {result['response']}")
print(f"Response with citations: {result['response_with_citations']}")
print("Bibliography:")
for entry in result["bibliography"]:
    print(f"- {entry}")
```

## Advanced Features

### Finding Sources by Content

```python
# Find a source by its content
source_id = source_attribution.find_source_by_content(
    "This is the content of the document."
)
print(f"Found source: {source_id}")
```

### Getting Source Content

```python
# Get the content of a source
content = source_attribution.get_source_content("source1")
print(f"Source content: {content}")
```

### Vietnamese Language Support

```python
# Initialize SourceAttribution with Vietnamese language
source_attribution_vi = SourceAttribution(
    citation_style="apa",
    track_token_level=True,
    language="vi"
)

# Register a Vietnamese source
source_attribution_vi.register_source(
    "source_vi",
    {
        "title": "Tài liệu Tiếng Việt",
        "content": "Đây là nội dung tiếng Việt.",
        "author": "Nguyễn Văn A",
        "publication_date": "2023",
        "url": "https://example.vn/doc"
    }
)

# Generate citation
citation = source_attribution_vi.generate_citation("source_vi")
print(f"Citation: {citation}")
```

## Citation Styles

The Source Attribution module supports the following citation styles:

- APA (American Psychological Association)
- MLA (Modern Language Association)
- Chicago
- IEEE (Institute of Electrical and Electronics Engineers)

## Citation Formats

The Source Attribution module supports the following citation formats:

- Inline: Citations are added directly in the text
- Footnote: Citations are added as footnotes at the end of the text
- Endnote: Citations are added as endnotes at the end of the document
- Bibliography: Citations are added as a bibliography at the end of the document

## Source Credibility Evaluation

The Source Attribution module evaluates the credibility of sources based on various factors:

- Author information
- Publication date (more recent sources get higher scores)
- URL domain (.edu, .gov, .org domains get higher scores)
- Publisher information
- Content length (longer content gets higher scores)

## Conclusion

The Source Attribution & Citation module provides a comprehensive solution for tracking and citing information sources in AI-generated content. It can be used standalone or integrated with RAG systems to provide transparent attribution and citation of information sources.
