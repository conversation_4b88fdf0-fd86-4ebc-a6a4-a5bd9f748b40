# Usage Guide

This guide provides instructions for using Deep Research Core.

## Basic Usage

### Command Line Interface

Deep Research Core provides several command-line tools for different use cases.

#### Basic Query Processing

```bash
deep-research --query "Your query here" --provider openai --model gpt-4
```

#### Using Enhanced CoTRAG

```bash
enhanced-cotrag --query "Your query here" --provider openrouter --api-key "your-api-key" --retriever multilingual
```

#### Running Evaluation

```bash
evaluate-cotrag --provider openrouter --api-key "your-api-key" --language en --method both
```

### Command-line Arguments

- `--query`, `-q`: Query to process
- `--provider`, `-p`: Model provider (openai, anthropic, openrouter)
- `--model`, `-m`: Model name
- `--api-key`, `-k`: API key
- `--retriever`, `-r`: Retriever type (hybrid, dpr, multilingual, simple)
- `--language`, `-l`: Language (en, vi)
- `--question-type`, `-t`: Question type (factual, analytical, procedural, etc.)

## Examples

### English Query

```bash
enhanced-cotrag --query "Explain the techniques for optimizing AI models like quantization, LoRA, and PEFT." --provider openrouter --api-key "your-api-key"
```

### Vietnamese Query

```bash
enhanced-cotrag --query "Giải thích về các kỹ thuật tối ưu hóa mô hình AI như quantization, LoRA, và PEFT." --provider openrouter --api-key "your-api-key"
```

## Python API

You can also use Deep Research Core as a Python library:

```python
from deep_research_core.reasoning.enhanced_cotrag import EnhancedCoTRAG
from deep_research_core.models.api.openrouter import openrouter_provider

# Set API key
openrouter_provider.api_key = "your-api-key"

# Create sample documents
documents = [
    {
        "content": "Quantization is a technique to reduce the precision of weights in AI models to reduce size and increase inference speed.",
        "metadata": {
            "source": "AI Model Optimization Guide",
            "title": "Quantization Techniques",
            "date": "2023-05-15"
        }
    },
    # Add more documents...
]

# Create Enhanced CoTRAG
cotrag = EnhancedCoTRAG(
    provider="openrouter",
    model="anthropic/claude-3-opus",
    retriever_type="multilingual",
    documents=documents,
    language="en"
)

# Process a query
result = cotrag.process(
    query="Explain quantization in AI models",
    question_type="factual"
)

# Print the result
print(result["answer"])
```

## Advanced Usage

### Custom Prompt Templates

You can create custom prompt templates for different question types:

```python
from deep_research_core.reasoning.prompt_templates import get_prompt_template

# Get a prompt template for a specific question type and language
template = get_prompt_template(question_type="analytical", language="en")

# Use the template
system_prompt = template["system"]
user_prompt = template["user"].format(query="Your query", context="Your context")
```

### Custom Retrievers

You can create custom retrievers by extending the base retriever classes:

```python
from deep_research_core.retrieval.advanced_retrieval import HybridRetriever

# Create a custom retriever
class CustomRetriever(HybridRetriever):
    def __init__(self, documents=None, embedding_dim=1536):
        super().__init__(documents, embedding_dim)
        # Add custom initialization
    
    def search(self, query, query_embedding=None, top_k=5):
        # Implement custom search logic
        return super().search(query, query_embedding, top_k)
```

### Multilingual Support

You can use the multilingual utilities to detect language and create appropriate prompts:

```python
from deep_research_core.utils.multilingual_utils import detect_language, create_multilingual_prompt

# Detect language
query = "Giải thích về quantization trong mô hình AI"
language = detect_language(query)  # Returns "vi"

# Create multilingual prompt
system_prompt, user_prompt = create_multilingual_prompt(
    query=query,
    context="Your context",
    language=language
)
```

## Best Practices

- **API Keys**: Store API keys securely in environment variables or a `.env` file.
- **Document Preparation**: Prepare documents with clear metadata for better retrieval.
- **Question Types**: Specify the question type when possible for better results.
- **Language Detection**: Let the system detect the language automatically for multilingual support.
- **Evaluation**: Regularly evaluate the system's performance using the evaluation tools.
