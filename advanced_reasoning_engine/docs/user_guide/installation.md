# Installation Guide

This guide provides instructions for installing Deep Research Core.

## Prerequisites

- Python 3.8 or higher
- pip (Python package installer)
- Git (optional, for cloning the repository)

## Installation Methods

### Method 1: Install from source (recommended)

1. Clone the repository:
```bash
git clone https://github.com/yourusername/deep-research-core.git
cd deep-research-core
```

2. Install in development mode:
```bash
# Basic installation
pip install -e .

# Or install with additional dependencies for development
pip install -e ".[dev,docs]"

# For GPU support
pip install -e ".[gpu]"
```

### Method 2: Install using requirements.txt

1. Clone the repository:
```bash
git clone https://github.com/yourusername/deep-research-core.git
cd deep-research-core
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

## Setting up API Keys

Create a `.env` file in the root directory of the project with your API keys:

```bash
# Create a .env file with your API keys
echo "OPENAI_API_KEY=your_openai_key" > .env
echo "ANTHROPIC_API_KEY=your_anthropic_key" >> .env
echo "OPENROUTER_API_KEY=your_openrouter_key" >> .env
```

Alternatively, you can set environment variables:

```bash
export OPENAI_API_KEY=your_openai_key
export ANTHROPIC_API_KEY=your_anthropic_key
export OPENROUTER_API_KEY=your_openrouter_key
```

## Verifying Installation

To verify that Deep Research Core is installed correctly, run:

```bash
python -c "import deep_research_core; print(deep_research_core.__version__)"
```

## Troubleshooting

### Common Issues

#### Missing Dependencies

If you encounter errors about missing dependencies, try installing them manually:

```bash
pip install <dependency_name>
```

#### API Key Issues

If you encounter errors related to API keys, make sure they are set correctly in your `.env` file or as environment variables.

#### GPU Support

If you want to use GPU acceleration, make sure you have the appropriate CUDA drivers installed and install the GPU version of dependencies:

```bash
pip install -e ".[gpu]"
```

### Getting Help

If you encounter any issues not covered here, please [create an issue](https://github.com/yourusername/deep-research-core/issues) on GitHub.
