# Deep Research Core - User Guide

## Introduction

Welcome to the Deep Research Core user guide. This documentation will help you understand and use the various capabilities of our AI research and development system. The Deep Research Core combines local models and cloud providers (OpenRouter, DeepSeek, OpenAI, Claude, Google) with a focus on reasoning abilities, Vietnamese language support, and UI integration.

## Contents

- [Installation](installation.md): Guide to install and set up Deep Research Core
- [Usage](usage.md): Basic usage instructions and examples

## Core Features

### Reasoning Capabilities

Deep Research Core implements several advanced reasoning techniques:

1. **Chain of Thought (CoT)**: Step-by-step reasoning approach
2. **Tree of Thoughts (ToT)**: Exploration of multiple reasoning paths
3. **Retrieval-Augmented Generation (RAG)**: Knowledge-enhanced generation
4. **Graph of Thoughts (GoT)**: Non-linear reasoning paths
5. **Multi-stage Reasoning**: Combining multiple reasoning techniques

For detailed documentation on reasoning capabilities, see [../reasoning_features.md](../reasoning_features.md).

### Retrieval-Augmented Generation

Our RAG implementation includes:

1. **Multi-source Retrieval**: Retrieve information from multiple sources
2. **Source Attribution**: Properly attribute information to sources
3. **HyDE (Hypothetical Document Embeddings)**: Enhanced retrieval through hypothetical documents
4. **Advanced Reranking**: Optimize retrieval results with contextual reranking

For detailed documentation on RAG capabilities, see [../rag_implementations_guide.md](../rag_implementations_guide.md).

### Vietnamese Language Support

Deep Research Core provides extensive support for Vietnamese language processing:

1. **Vietnamese Embeddings**: Specialized embeddings for Vietnamese text
2. **Compound Word Processing**: Handling Vietnamese compound words
3. **Domain-specific Language Models**: Models fine-tuned for Vietnamese domains
4. **Multilingual Capabilities**: Seamless switching between Vietnamese and other languages

For detailed documentation on Vietnamese language support, see [../vietnamese_support.md](../vietnamese_support.md).

### User Interface

The system includes a comprehensive user interface with:

1. **Document Management**: Upload, organize, and manage documents
2. **Interactive Querying**: Ask questions and receive well-reasoned answers
3. **Visualization Tools**: Visualize reasoning processes and knowledge graphs
4. **Analytics Dashboard**: Monitor system performance and usage

## Getting Started

If you're new to Deep Research Core, we recommend following these steps:

1. Begin with the [Installation Guide](installation.md)
2. Follow the [Usage Guide](usage.md) to understand basic operations
3. Explore specific features based on your interests

## Advanced Topics

For more advanced topics, explore our specialized guides:

- [Model Fine-tuning](../sft_guide.md): Learn how to fine-tune models
- [Monitoring and Evaluation](../monitoring_guide.md): Monitor system performance
- [Integration with External Systems](../api_reference.md): Integrate with other systems

## Need Help?

If you encounter any issues or have questions that aren't addressed in this guide, please:

1. Check the [Troubleshooting](../troubleshooting.md) section
2. Review our [API Reference](../api_reference.md)
3. Contact our support team at [<EMAIL>](mailto:<EMAIL>) 