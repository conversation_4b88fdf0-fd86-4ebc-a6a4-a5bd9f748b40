# Chain of Thought RAG (CoT-RAG) Guide

This guide provides information about the Chain of Thought RAG (CoT-RAG) implementation in the Deep Research Core, how to use it, and best practices.

## What is Chain of Thought RAG?

Chain of Thought RAG (CoT-RAG) combines two powerful techniques:

1. **Chain of Thought (CoT)**: A prompting technique that encourages the model to break down complex problems into steps and reason through them before providing a final answer.

2. **Retrieval-Augmented Generation (RAG)**: A technique that retrieves relevant documents from a corpus and uses them to generate more accurate and informed responses.

By combining these techniques, CoT-RAG first retrieves relevant documents, then uses Chain of Thought reasoning to analyze the information and generate a step-by-step response. This approach improves both accuracy and explainability.

## Available Implementations

The Deep Research Core provides two CoT-RAG implementations:

1. **CoTRAG**: The base implementation that combines Chain of Thought with any RAG implementation.
2. **EnhancedCoTRAG**: An extended implementation with additional features like multilingual support, hybrid retrieval, and reranking.

## Using CoT-RAG

### Basic Usage

```python
from deep_research_core.reasoning import CoTRAG, SQLiteVectorRAG

# Initialize a RAG instance
rag = SQLiteVectorRAG(
    db_path="documents.db",
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000
)

# Add documents
rag.add_documents(documents)

# Initialize CoT-RAG
cot_rag = CoTRAG(
    rag_instance=rag,
    language="en",
    verbose=True
)

# Process a query
result = cot_rag.process(
    query="What are the benefits of combining Chain of Thought with RAG?",
    callback=lambda content: print(content, end="", flush=True)
)

# Print the result
print(f"\nLatency: {result['latency']:.2f} seconds")
```

### Using EnhancedCoTRAG

```python
from deep_research_core.reasoning import EnhancedCoTRAG, SQLiteVectorRAG

# Initialize a RAG instance
rag = SQLiteVectorRAG(
    db_path="documents.db",
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000
)

# Add documents
rag.add_documents(documents)

# Initialize EnhancedCoTRAG
enhanced_cot_rag = EnhancedCoTRAG(
    rag_instance=rag,
    language="en",
    use_hybrid_search=True,
    use_reranking=True,
    use_query_expansion=True,
    verbose=True
)

# Process a query
result = enhanced_cot_rag.process(
    query="What are the benefits of combining Chain of Thought with RAG?",
    callback=lambda content: print(content, end="", flush=True)
)

# Print the result
print(f"\nLatency: {result['latency']:.2f} seconds")
```

## Advanced Features

### Multilingual Support

Both CoTRAG and EnhancedCoTRAG support multiple languages. You can specify the language when initializing the instance:

```python
cot_rag = CoTRAG(
    rag_instance=rag,
    language="vi",  # Vietnamese
    verbose=True
)
```

EnhancedCoTRAG can also automatically detect the language of the query and adjust accordingly:

```python
# Process a Vietnamese query
result = enhanced_cot_rag.process(
    query="Giải thích về kỹ thuật Chain of Thought và RAG",
    callback=lambda content: print(content, end="", flush=True)
)
```

### Hybrid Search

EnhancedCoTRAG supports hybrid search, which combines vector search with keyword search for better retrieval:

```python
result = enhanced_cot_rag.process(
    query="What are the benefits of combining Chain of Thought with RAG?",
    hybrid_weight=0.7  # 70% vector search, 30% keyword search
)
```

### Query Expansion

EnhancedCoTRAG can automatically expand queries to improve retrieval:

```python
enhanced_cot_rag = EnhancedCoTRAG(
    rag_instance=rag,
    use_query_expansion=True
)
```

### Reranking

EnhancedCoTRAG can rerank search results to improve relevance:

```python
enhanced_cot_rag = EnhancedCoTRAG(
    rag_instance=rag,
    use_reranking=True
)
```

## Best Practices

1. **Choose the right RAG implementation**: Different RAG implementations have different strengths. Choose the one that best fits your use case.

2. **Use hybrid search**: Hybrid search often provides better results than pure vector search or keyword search.

3. **Enable reranking for better relevance**: Reranking can significantly improve the relevance of search results.

4. **Use query expansion for complex queries**: Query expansion can help retrieve more relevant documents for complex queries.

5. **Adjust the language for multilingual use cases**: Set the language parameter to match your use case, or let EnhancedCoTRAG detect it automatically.

6. **Use streaming for better user experience**: The callback parameter allows you to stream the response as it's being generated.

## Use Cases

CoT-RAG is particularly useful for:

1. **Complex reasoning tasks**: When the task requires multi-step reasoning.

2. **Explainable AI**: When you need to explain how the model arrived at its answer.

3. **Fact-based responses**: When you need to ground the model's responses in factual information.

4. **Educational applications**: When you want to show the reasoning process for educational purposes.

5. **Research synthesis**: When you need to analyze and synthesize information from multiple sources.

## Conclusion

Chain of Thought RAG (CoT-RAG) combines the strengths of Chain of Thought reasoning and Retrieval-Augmented Generation to provide more accurate, explainable, and grounded responses. The Deep Research Core provides flexible implementations that can be adapted to various use cases.
