# Hướng dẫn sử dụng Tree of Thought (ToT)

## Giới thiệu

Tree of Thought (ToT) là một phương pháp suy luận nâng cao, mở rộng từ Chain of Thought (CoT). Trong khi CoT chỉ khám phá một đường dẫn suy luận tuyến tính, ToT khám phá nhiều nhánh suy luận song song, đánh giá từng nhánh và chọn nhánh tốt nhất.

Phương pháp ToT đặc biệt hữu ích cho:
- Các vấn đề phức tạp đòi hỏi khám phá nhiều hướng tiếp cận
- <PERSON><PERSON><PERSON> câu hỏi có nhiều cách giải quyết khác nhau
- <PERSON><PERSON>c tình huống cần cân nhắc nhiều khía cạnh trước khi đưa ra kết luận

## Các triển khai có sẵn

Deep Research Core cung cấp hai triển khai ToT:

1. **TreeOfThought**: Triển khai cơ bản của ToT, kh<PERSON>m phá nhiều nhánh suy luận và chọn nhánh tốt nhất.
2. **ToTRAG**: Kết hợp ToT với Retrieval-Augmented Generation (RAG), sử dụng thông tin truy xuất để cải thiện quá trình suy luận.

## Sử dụng ToT

### Sử dụng cơ bản

```python
from deep_research_core.reasoning import TreeOfThought

# Khởi tạo TreeOfThought
tot = TreeOfThought(
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free",
    temperature=0.7,
    max_tokens=2000,
    max_branches=5,  # Số nhánh tối đa để khám phá
    max_depth=3,     # Độ sâu tối đa của cây suy luận
    verbose=True     # In thông tin chi tiết
)

# Thực hiện suy luận
result = tot.reason(
    query="Làm thế nào để giải quyết vấn đề biến đổi khí hậu?",
    callback=lambda content: print(content, end="", flush=True)
)

# In kết quả
print(f"\nCâu trả lời cuối cùng: {result['answer']}")
print(f"Số đường dẫn đã khám phá: {result['explored_paths']}")
print(f"Thời gian xử lý: {result['latency']:.2f} giây")
```

### Sử dụng ToTRAG

```python
from deep_research_core.reasoning import ToTRAG, SQLiteVectorRAG

# Khởi tạo SQLiteVectorRAG
rag = SQLiteVectorRAG(
    db_path="documents.db",
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free",
    temperature=0.7,
    max_tokens=2000
)

# Thêm tài liệu
documents = [
    {
        "content": "Biến đổi khí hậu là sự thay đổi của khí hậu Trái Đất do hoạt động của con người...",
        "metadata": {"source": "Wikipedia", "title": "Biến đổi khí hậu"}
    },
    # Thêm các tài liệu khác
]
rag.add_documents(documents)

# Khởi tạo ToTRAG
tot_rag = ToTRAG(
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free",
    temperature=0.7,
    max_tokens=2000,
    vector_store=rag,
    max_branches=5,
    max_depth=3,
    verbose=True
)

# Xử lý truy vấn
result = tot_rag.process(
    query="Làm thế nào để giải quyết vấn đề biến đổi khí hậu?",
    top_k=5,  # Số lượng tài liệu truy xuất
    callback=lambda content: print(content, end="", flush=True)
)

# In kết quả
print(f"\nCâu trả lời cuối cùng: {result['answer']}")
print(f"Số tài liệu truy xuất: {len(result['documents'])}")
print(f"Số đường dẫn đã khám phá: {result['explored_paths']}")
print(f"Thời gian xử lý: {result['latency']:.2f} giây")
```

## Cách hoạt động

### TreeOfThought

1. **Khởi tạo**: Tạo ra nhiều nhánh suy luận ban đầu cho vấn đề.
2. **Đánh giá**: Đánh giá từng nhánh và gán điểm cho chúng.
3. **Khám phá**: Mở rộng các nhánh triển vọng nhất.
4. **Lặp lại**: Tiếp tục đánh giá và mở rộng cho đến khi đạt đến độ sâu tối đa.
5. **Chọn lựa**: Chọn nhánh tốt nhất và mở rộng nó đến kết luận cuối cùng.

### ToTRAG

1. **Truy xuất**: Truy xuất các tài liệu liên quan đến truy vấn.
2. **Định dạng**: Định dạng các tài liệu thành ngữ cảnh.
3. **Suy luận ToT**: Thực hiện suy luận ToT với ngữ cảnh truy xuất.
4. **Kết quả**: Trả về kết quả suy luận cùng với các tài liệu đã truy xuất.

## Tùy chỉnh

### Tùy chỉnh prompt

Bạn có thể tùy chỉnh prompt hệ thống và prompt người dùng:

```python
result = tot.reason(
    query="Làm thế nào để giải quyết vấn đề biến đổi khí hậu?",
    custom_system_prompt="Bạn là một chuyên gia về môi trường và biến đổi khí hậu.",
    custom_user_prompt="Hãy phân tích vấn đề sau: {query}"
)
```

### Điều chỉnh tham số

Bạn có thể điều chỉnh các tham số để tối ưu hóa hiệu suất:

- `max_branches`: Số nhánh tối đa để khám phá ở mỗi bước.
- `max_depth`: Độ sâu tối đa của cây suy luận.
- `temperature`: Nhiệt độ lấy mẫu, giá trị cao hơn tạo ra kết quả đa dạng hơn.

## Ưu điểm của ToT

- **Khám phá toàn diện**: Khám phá nhiều hướng tiếp cận khác nhau.
- **Đánh giá chất lượng**: Đánh giá và so sánh các đường dẫn suy luận khác nhau.
- **Kết quả tốt hơn**: Thường cho kết quả tốt hơn so với CoT đơn giản.
- **Minh bạch**: Cung cấp cái nhìn sâu sắc về quá trình suy luận.

## Hạn chế

- **Tốn kém hơn**: Yêu cầu nhiều lệnh gọi API hơn so với CoT.
- **Thời gian xử lý lâu hơn**: Mất nhiều thời gian hơn để khám phá nhiều nhánh.
- **Phức tạp hơn**: Triển khai và tùy chỉnh phức tạp hơn.

## Kết luận

Tree of Thought (ToT) là một công cụ mạnh mẽ cho các vấn đề phức tạp đòi hỏi khám phá nhiều hướng tiếp cận. Bằng cách kết hợp ToT với RAG, bạn có thể tận dụng cả sức mạnh của suy luận đa nhánh và thông tin truy xuất để có được kết quả tốt nhất.
