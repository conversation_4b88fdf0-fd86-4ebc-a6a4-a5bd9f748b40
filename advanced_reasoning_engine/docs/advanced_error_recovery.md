# Hệ thống khôi phục lỗi nâng cao cho ReAct

Tài liệu này mô tả hệ thống khôi phục lỗi nâng cao cho ReAct, bao gồm phát hiện lỗi thông minh, họ<PERSON> lỗi, và các chiến lược khôi phục nâng cao.

## Tổng quan

Hệ thống khôi phục lỗi nâng cao cho ReAct bao gồm các thành phần sau:

1. **Phát hiện lỗi thông minh**: <PERSON><PERSON> loại lỗi, đánh giá mức độ nghiêm trọng, x<PERSON><PERSON> định khả năng khôi phục, và trích xuất thông tin liên quan.
2. **Học lỗi**: <PERSON> dõ<PERSON> mẫu lỗi và hiệu quả của chiến lượ<PERSON> khô<PERSON> phụ<PERSON>, cho ph<PERSON><PERSON> hệ thống học từ các lỗi trong quá khứ.
3. **<PERSON><PERSON><PERSON> lược khôi phục nâng cao**: <PERSON><PERSON><PERSON> chiến lược khôi phục mới như định dạng lại đầu vào dựa trên ngữ cảnh, điều chỉnh tham số công cụ, và chuyển đổi mô hình.
4. **Phân tích lỗi**: Phân tích xu hướng lỗi, phân phối lỗi, và hiệu quả của chiến lược khôi phục.
5. **Trực quan hóa lỗi**: Tạo báo cáo và biểu đồ để hiển thị thông tin về lỗi và khôi phục.
6. **Tối ưu hóa chiến lược**: Tự động điều chỉnh tham số của chiến lược khôi phục dựa trên hiệu suất.
7. **Giám sát lỗi thời gian thực**: Theo dõi lỗi theo thời gian thực và cảnh báo khi phát hiện các mẫu lỗi bất thường.
8. **Phân loại lỗi dựa trên học máy**: Sử dụng học máy để phân loại lỗi chính xác hơn và cải thiện theo thời gian.
9. **Khôi phục lỗi phân tán**: Chia sẻ thông tin về lỗi và chiến lược khôi phục giữa nhiều phiên bản ReAct.

## Cách sử dụng

### Bật khôi phục lỗi trong ReAct

Để bật khôi phục lỗi trong ReAct, hãy đặt tham số `use_error_recovery=True` khi khởi tạo:

```python
from src.deep_research_core.reasoning.react import ReAct

react = ReAct(
    provider="openai",
    model="gpt-4",
    tools=[...],
    use_error_recovery=True,
    max_retries=3,
    retry_delay=0.5
)
```

### Cung cấp công cụ thay thế

Để cho phép chiến lược công cụ thay thế, hãy cung cấp từ điển `tool_alternatives`:

```python
tool_alternatives = {
    "api_request": ["web_search"],
    "web_search": ["api_request"]
}

react = ReAct(
    provider="openai",
    model="gpt-4",
    tools=[...],
    use_error_recovery=True,
    tool_alternatives=tool_alternatives
)
```

### Truy cập phân tích lỗi

Sau khi xử lý các truy vấn, bạn có thể truy cập phân tích lỗi:

```python
# Lấy phân phối lỗi
distribution = react.error_analytics.get_error_distribution()

# Lấy lỗi hàng đầu
top_errors = react.error_analytics.get_top_errors(limit=5)

# Lấy hiệu quả khôi phục
effectiveness = react.error_analytics.get_recovery_effectiveness()
```

### Tạo báo cáo lỗi

Bạn có thể tạo báo cáo lỗi bằng cách sử dụng trực quan hóa lỗi:

```python
# Tạo báo cáo tóm tắt lỗi
report = react.error_visualization.generate_error_summary_report(
    format="markdown",
    output_file="error_report.md"
)

# Tạo biểu đồ xu hướng lỗi
chart_data = react.error_visualization.generate_error_trend_chart(
    time_period="day",
    chart_type="line",
    output_file="error_trends.json"
)
```

### Tối ưu hóa chiến lược khôi phục

Bạn có thể tối ưu hóa các chiến lược khôi phục:

```python
# Tối ưu hóa tất cả các chiến lược
optimized_values = react.error_strategy_optimizer.optimize_strategies()

# Lấy tham số tốt nhất cho một chiến lược
best_params = react.error_strategy_optimizer.get_best_parameters("retry")
```

## Các thành phần chính

### ErrorDetector

`ErrorDetector` phân tích lỗi và cung cấp thông tin chi tiết về lỗi:

```python
from src.deep_research_core.utils.intelligent_error_detection import ErrorDetector

detector = ErrorDetector()
detection_result = detector.detect(error, context)

print(detection_result["category"])  # Loại lỗi
print(detection_result["severity"])  # Mức độ nghiêm trọng
print(detection_result["recoverable"])  # Có thể khôi phục không
print(detection_result["relevant_info"])  # Thông tin liên quan
```

### ErrorLearningManager

`ErrorLearningManager` theo dõi lỗi và hiệu quả của chiến lược khôi phục:

```python
from src.deep_research_core.utils.error_learning import ErrorLearningManager

learning_manager = ErrorLearningManager(storage_path="error_learning.json")
learning_manager.track_error(error, context, recovery_result)

best_strategy = learning_manager.get_best_strategy(error, context)
```

### Chiến lược khôi phục nâng cao

Các chiến lược khôi phục nâng cao bao gồm:

- `ContextAwareReformulationStrategy`: Định dạng lại đầu vào dựa trên ngữ cảnh lỗi
- `PromptEngineeringStrategy`: Sử dụng mô hình để tạo ra các cách tiếp cận thay thế
- `ModelFallbackStrategy`: Chuyển sang các mô hình khác khi xảy ra lỗi
- `ToolParameterAdjustmentStrategy`: Điều chỉnh tham số công cụ dựa trên mẫu lỗi
- `AdaptiveRecoveryStrategy`: Chọn chiến lược tốt nhất dựa trên học lỗi

### ErrorAnalytics

`ErrorAnalytics` cung cấp phân tích về lỗi và khôi phục:

```python
from src.deep_research_core.utils.error_analytics import ErrorAnalytics

analytics = ErrorAnalytics(storage_path="error_analytics.json")
analytics.track_error(error, context, detection_result, recovery_result)

distribution = analytics.get_error_distribution()
trends = analytics.get_error_trends(time_period="day")
effectiveness = analytics.get_recovery_effectiveness()
top_errors = analytics.get_top_errors(limit=10)
```

### ErrorVisualization

`ErrorVisualization` tạo báo cáo và biểu đồ về lỗi:

```python
from src.deep_research_core.utils.error_visualization import ErrorVisualization

visualization = ErrorVisualization(analytics=analytics, output_dir="reports")
report = visualization.generate_error_summary_report(format="html", output_file="report.html")
chart = visualization.generate_error_trend_chart(time_period="day", chart_type="line", output_file="trends.json")
```

### ErrorStrategyOptimizerManager

`ErrorStrategyOptimizerManager` tối ưu hóa các chiến lược khôi phục:

```python
from src.deep_research_core.utils.error_strategy_optimizer import ErrorStrategyOptimizerManager

optimizer = ErrorStrategyOptimizerManager(
    recovery_manager=recovery_manager,
    learning_manager=learning_manager,
    storage_path="optimizer.json"
)

optimizer.update_performance("retry", 0.8)
optimized_values = optimizer.optimize_strategies()
```

### ErrorMonitor

`ErrorMonitor` theo dõi lỗi theo thời gian thực và cảnh báo khi phát hiện các mẫu lỗi bất thường:

```python
from src.deep_research_core.utils.error_monitoring import ErrorMonitor, LoggingAlertHandler

# Tạo trình xử lý cảnh báo
alert_handlers = [
    LoggingAlertHandler(),  # Ghi nhật ký cảnh báo
    # WebhookAlertHandler(webhook_url="https://example.com/webhook"),  # Gửi cảnh báo qua webhook
    # EmailAlertHandler(smtp_server="smtp.example.com", ...)  # Gửi cảnh báo qua email
]

# Tạo monitor
monitor = ErrorMonitor(
    alert_threshold=5,  # Cảnh báo sau 5 lỗi
    alert_window=60,    # Trong 60 giây
    alert_cooldown=300, # Chờ 300 giây trước khi cảnh báo lại
    alert_handlers=alert_handlers
)

# Theo dõi lỗi
monitor.track_error(error, context, detection_result, recovery_result)

# Lấy trạng thái monitor
status = monitor.get_status()
active_alerts = monitor.get_active_alerts()
error_rate = monitor.get_error_rate(60)  # Tỷ lệ lỗi trong 60 giây qua
```

### MLErrorClassifier

`MLErrorClassifier` sử dụng học máy để phân loại lỗi chính xác hơn:

```python
from src.deep_research_core.utils.ml_error_classifier import MLErrorClassifier, EnsembleErrorClassifier

# Tạo bộ phân loại ML
ml_classifier = MLErrorClassifier(
    model_path="ml_error_model.pkl",
    vectorizer_path="ml_error_vectorizer.pkl",
    min_samples=50,
    auto_train=True
)

# Phân loại lỗi
classification = ml_classifier.classify(error, context)

# Thêm lỗi đã gắn nhãn vào dữ liệu huấn luyện
ml_classifier.add_labeled_error(error, context, "invalid_arguments")

# Tạo bộ phân loại tổng hợp
ensemble = EnsembleErrorClassifier(
    classifiers=[rule_based_classifier, ml_classifier.classify],
    weights=[0.3, 0.7]  # Đặt trọng số cao hơn cho bộ phân loại ML
)
```

### DistributedErrorRecovery

`DistributedErrorRecovery` chia sẻ thông tin về lỗi và chiến lược khôi phục giữa nhiều phiên bản ReAct:

```python
from src.deep_research_core.utils.distributed_error_recovery import DistributedErrorRecovery, DistributedErrorServer

# Tạo máy chủ phân tán (chạy trên một máy chủ trung tâm)
server = DistributedErrorServer(
    host="localhost",
    port=8765,
    storage_path="distributed_errors.json"
)
server.start()

# Tạo khôi phục lỗi phân tán (chạy trên mỗi phiên bản ReAct)
distributed_recovery = DistributedErrorRecovery(
    recovery_manager=recovery_manager,
    learning_manager=learning_manager,
    storage_path="local_distributed_errors.json",
    sync_interval=60,
    node_id="node-1",
    cluster_nodes=["central-server:8765"]
)

# Theo dõi lỗi
distributed_recovery.track_error(error, context, recovery_result)

# Theo dõi hiệu quả chiến lược
distributed_recovery.track_strategy_effectiveness("retry", 0.8)

# Lấy chiến lược tốt nhất dựa trên kiến thức phân tán
best_strategy = distributed_recovery.get_best_strategy(error, context)

# Đồng bộ hóa với các nút khác
distributed_recovery.sync()
```

## Ví dụ

Dự án cung cấp nhiều ví dụ về cách sử dụng hệ thống khôi phục lỗi nâng cao:

- `examples/advanced_error_recovery.py`: Ví dụ cơ bản về hệ thống khôi phục lỗi nâng cao
- `examples/error_monitoring.py`: Ví dụ về giám sát lỗi thời gian thực và cảnh báo
- `examples/ml_error_classification.py`: Ví dụ về phân loại lỗi dựa trên học máy
- `examples/distributed_error_recovery.py`: Ví dụ về khôi phục lỗi phân tán giữa nhiều phiên bản ReAct

## Tùy chỉnh

### Tạo chiến lược khôi phục tùy chỉnh

Bạn có thể tạo chiến lược khôi phục tùy chỉnh bằng cách kế thừa lớp `ErrorRecoveryStrategy`:

```python
from src.deep_research_core.utils.error_recovery import ErrorRecoveryStrategy

class MyCustomStrategy(ErrorRecoveryStrategy):
    def __init__(self, **kwargs):
        super().__init__(name="my_custom_strategy", **kwargs)

    def can_handle(self, error, context):
        # Kiểm tra xem chiến lược có thể xử lý lỗi không
        return isinstance(error, MyCustomError)

    def recover(self, error, context):
        # Thực hiện khôi phục
        return {
            "success": True,
            "strategy": self.name,
            "message": "Recovered using my custom strategy"
        }
```

### Tùy chỉnh phát hiện lỗi

Bạn có thể tùy chỉnh phát hiện lỗi bằng cách kế thừa lớp `ErrorDetector`:

```python
from src.deep_research_core.utils.intelligent_error_detection import ErrorDetector

class MyCustomDetector(ErrorDetector):
    def detect(self, error, context):
        # Thực hiện phát hiện lỗi tùy chỉnh
        return {
            "category": "my_custom_category",
            "severity": "medium",
            "relevant_info": {},
            "recoverable": True
        }
```

## Hiệu suất và tối ưu hóa

### Lưu trữ dữ liệu

Để cải thiện hiệu suất với tập dữ liệu lớn, hãy sử dụng `SQLiteErrorAnalytics` thay vì `ErrorAnalytics`:

```python
from src.deep_research_core.utils.error_analytics import SQLiteErrorAnalytics

analytics = SQLiteErrorAnalytics(db_path="error_analytics.db")
```

### Tối ưu hóa Bayesian

Để tối ưu hóa chiến lược khôi phục bằng phương pháp Bayesian, hãy sử dụng `BayesianOptimizer`:

```python
from src.deep_research_core.utils.error_strategy_optimizer import BayesianOptimizer

optimizer = BayesianOptimizer(
    strategy=strategy,
    parameters=parameters,
    n_iterations=20
)
```

## Kết luận

Hệ thống khôi phục lỗi nâng cao cho ReAct cung cấp một cách mạnh mẽ để xử lý lỗi trong các ứng dụng dựa trên ReAct. Bằng cách kết hợp phát hiện lỗi thông minh, học lỗi, và các chiến lược khôi phục nâng cao, hệ thống có thể tự động khôi phục từ nhiều loại lỗi khác nhau và cải thiện theo thời gian.
