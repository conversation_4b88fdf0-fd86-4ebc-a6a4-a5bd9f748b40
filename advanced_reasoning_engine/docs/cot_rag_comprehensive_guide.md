# Comprehensive Chain of Thought RAG Guide

This guide provides detailed information on using the Chain of Thought (CoT) Retrieval-Augmented Generation (RAG) implementations in Deep Research Core to improve the quality, accuracy, and explainability of generated responses.

## Table of Contents

1. [Introduction](#1-introduction)
2. [CoT-RAG Implementations](#2-cot-rag-implementations)
3. [Basic Usage](#3-basic-usage)
4. [Advanced Features](#4-advanced-features)
5. [Performance Optimization](#5-performance-optimization)
6. [Evaluation and Metrics](#6-evaluation-and-metrics)
7. [Multilingual Support](#7-multilingual-support)
8. [Monitoring and Observability](#8-monitoring-and-observability)
9. [Best Practices](#9-best-practices)
10. [Examples](#10-examples)

## 1. Introduction

Chain of Thought (CoT) is a prompting technique that encourages large language models (LLMs) to generate a series of intermediate reasoning steps before producing a final answer. When combined with Retrieval-Augmented Generation (RAG), CoT can significantly improve the quality, accuracy, and explainability of generated responses.

### Benefits of CoT-RAG

- **Improved Reasoning**: Helps the model reason through complex problems step by step
- **Better Accuracy**: Reduces hallucinations and improves factual accuracy
- **Enhanced Explainability**: Makes the model's reasoning process transparent
- **Reduced Errors**: Catches logical errors in the reasoning process
- **Improved Context Utilization**: Makes better use of retrieved documents

### How CoT-RAG Works

1. **Retrieval**: Relevant documents are retrieved from a vector store
2. **Chain of Thought Prompting**: The model is prompted to think step by step
3. **Reasoning**: The model generates intermediate reasoning steps
4. **Answer Generation**: The model produces a final answer based on its reasoning

## 2. CoT-RAG Implementations

Deep Research Core provides two main CoT-RAG implementations:

### CoTRAG

The base implementation that combines Chain of Thought with any RAG implementation.

```python
from deep_research_core.reasoning import CoTRAG, SQLiteVectorRAG

# Initialize a RAG instance
rag = SQLiteVectorRAG(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    embedding_model="all-MiniLM-L6-v2",
    db_path="my_documents.db"
)

# Add documents
rag.add_documents(documents)

# Initialize CoTRAG
cot_rag = CoTRAG(
    rag_instance=rag,
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    verbose=True
)

# Process a query
result = cot_rag.process("What is the difference between RAG and traditional search?")
print(result["answer"])
```

### EnhancedCoTRAG

An extended implementation with additional features like multilingual support, hybrid retrieval, and reranking.

```python
from deep_research_core.reasoning import EnhancedCoTRAG, SQLiteVectorRAG

# Initialize a RAG instance
rag = SQLiteVectorRAG(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    embedding_model="all-MiniLM-L6-v2",
    db_path="my_documents.db"
)

# Add documents
rag.add_documents(documents)

# Initialize EnhancedCoTRAG
enhanced_cot_rag = EnhancedCoTRAG(
    rag_instance=rag,
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    language="en",
    use_hybrid_search=True,
    use_reranking=True,
    use_query_expansion=True,
    verbose=True
)

# Process a query
result = enhanced_cot_rag.process("What is the difference between RAG and traditional search?")
print(result["answer"])
```

## 3. Basic Usage

### Initializing CoT-RAG

```python
from deep_research_core.reasoning import CoTRAG, SQLiteVectorRAG

# Initialize a RAG instance
rag = SQLiteVectorRAG(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    embedding_model="all-MiniLM-L6-v2",
    db_path="my_documents.db"
)

# Add documents
documents = [
    {
        "content": "RAG (Retrieval-Augmented Generation) combines retrieval systems with generative models. It retrieves relevant documents from a knowledge base and uses them to generate more accurate and factual responses.",
        "source": "RAG Paper",
        "title": "Retrieval-Augmented Generation for Knowledge-Intensive NLP Tasks"
    },
    {
        "content": "Traditional search systems like Google retrieve documents based on relevance to a query but don't generate new content. They simply return the most relevant existing documents.",
        "source": "Search Systems Overview",
        "title": "Introduction to Information Retrieval"
    }
]
rag.add_documents(documents)

# Initialize CoTRAG
cot_rag = CoTRAG(
    rag_instance=rag,
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    verbose=True
)
```

### Processing Queries

```python
# Process a query
result = cot_rag.process("What is the difference between RAG and traditional search?")

# Print the answer
print(result["answer"])

# Print the reasoning steps
print(result["cot_prompt"])

# Print the retrieved documents
for i, doc in enumerate(result["documents"]):
    print(f"Document {i+1}: {doc['title']} (Score: {doc['score']})")
    print(doc["content"])
    print()

# Print performance metrics
print(f"Latency: {result['latency']:.2f} seconds")
```

### Streaming Responses

```python
# Define a callback function for streaming
def callback(content):
    print(content, end="", flush=True)

# Process a query with streaming
result = cot_rag.process(
    "What is the difference between RAG and traditional search?",
    callback=callback
)
```

### Using Different Providers

```python
# Initialize CoTRAG with Anthropic
cot_rag_anthropic = CoTRAG(
    rag_instance=rag,
    provider="anthropic",
    model="claude-3-opus-20240229",
    temperature=0.7,
    max_tokens=2000,
    verbose=True
)

# Process a query
result = cot_rag_anthropic.process("What is the difference between RAG and traditional search?")
```

## 4. Advanced Features

### Hybrid Search

EnhancedCoTRAG supports hybrid search, which combines vector search with keyword search for better results.

```python
# Initialize EnhancedCoTRAG with hybrid search
enhanced_cot_rag = EnhancedCoTRAG(
    rag_instance=rag,
    provider="openai",
    model="gpt-4o",
    use_hybrid_search=True,
    hybrid_weight=0.5,  # 0.0 = keyword only, 1.0 = vector only, 0.5 = equal weight
    verbose=True
)

# Process a query
result = enhanced_cot_rag.process("What is the difference between RAG and traditional search?")
```

### Query Expansion

Query expansion generates multiple variations of the original query to improve retrieval.

```python
# Initialize EnhancedCoTRAG with query expansion
enhanced_cot_rag = EnhancedCoTRAG(
    rag_instance=rag,
    provider="openai",
    model="gpt-4o",
    use_query_expansion=True,
    verbose=True
)

# Process a query
result = enhanced_cot_rag.process("What is the difference between RAG and traditional search?")

# Print the expanded query
print(f"Original query: {result['query']}")
print(f"Expanded query: {result['expanded_query']}")
```

### Document Reranking

Document reranking uses the LLM to rerank retrieved documents based on their relevance to the query.

```python
# Initialize EnhancedCoTRAG with document reranking
enhanced_cot_rag = EnhancedCoTRAG(
    rag_instance=rag,
    provider="openai",
    model="gpt-4o",
    use_reranking=True,
    verbose=True
)

# Process a query
result = enhanced_cot_rag.process("What is the difference between RAG and traditional search?")

# Print the reranked documents
print("Reranked documents:")
for i, doc in enumerate(result["documents"]):
    print(f"Document {i+1}: {doc['title']} (Score: {doc['score']})")
```

### Custom Prompts

You can customize the system and user prompts used for CoT-RAG.

```python
# Define custom prompts
custom_system_prompt = """You are a helpful assistant that thinks step by step to answer questions accurately.
You have access to retrieved documents that may contain relevant information.
Always cite your sources and be transparent about your reasoning process."""

custom_user_prompt = """Question: {query}

Retrieved Documents:
{documents}

Let's think about this step by step to find the answer:
1. What information do we have from the retrieved documents?
2. What is the question asking for?
3. How can we use the information to answer the question?
4. Are there any gaps in our knowledge that we need to acknowledge?

Answer:"""

# Process a query with custom prompts
result = cot_rag.process(
    "What is the difference between RAG and traditional search?",
    custom_system_prompt=custom_system_prompt,
    custom_user_prompt=custom_user_prompt
)
```

### Filtering Documents

You can filter retrieved documents based on metadata.

```python
# Process a query with a filter
result = cot_rag.process(
    "What is the difference between RAG and traditional search?",
    filter_expr="source == 'RAG Paper'"
)
```

## 5. Performance Optimization

### Model Selection

Different models have different performance characteristics for CoT-RAG:

```python
# High-performance model (best quality, slower)
cot_rag_high = CoTRAG(
    rag_instance=rag,
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000
)

# Balanced model (good quality, faster)
cot_rag_balanced = CoTRAG(
    rag_instance=rag,
    provider="anthropic",
    model="claude-3-sonnet-20240229",
    temperature=0.7,
    max_tokens=2000
)

# Fast model (lower quality, fastest)
cot_rag_fast = CoTRAG(
    rag_instance=rag,
    provider="openai",
    model="gpt-3.5-turbo",
    temperature=0.7,
    max_tokens=2000
)
```

### Optimizing Retrieval

```python
# Use a more efficient vector store for large datasets
from deep_research_core.reasoning import MilvusRAG

milvus_rag = MilvusRAG(
    provider="openai",
    model="gpt-4o",
    collection_name="my_collection",
    connection_args={"host": "localhost", "port": "19530"}
)

# Initialize CoTRAG with the efficient vector store
cot_rag = CoTRAG(
    rag_instance=milvus_rag,
    provider="openai",
    model="gpt-4o"
)
```

### Caching

```python
from deep_research_core.utils.caching import enable_caching

# Enable caching for CoTRAG
enable_caching(cache_dir="cache", ttl=3600)  # Cache for 1 hour

# Initialize CoTRAG with caching
cot_rag = CoTRAG(
    rag_instance=rag,
    provider="openai",
    model="gpt-4o",
    use_cache=True
)
```

### Parallel Processing

```python
import concurrent.futures

# Process multiple queries in parallel
queries = [
    "What is RAG?",
    "How does traditional search work?",
    "What are the benefits of CoT-RAG?",
    "How does query expansion improve retrieval?"
]

def process_query(query):
    return cot_rag.process(query)

with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
    results = list(executor.map(process_query, queries))

for i, result in enumerate(results):
    print(f"Query {i+1}: {queries[i]}")
    print(f"Answer: {result['answer']}")
    print()
```

## 6. Evaluation and Metrics

### Evaluating CoT-RAG Performance

```python
from deep_research_core.evaluation import CoTRAGEvaluator

# Initialize the evaluator
evaluator = CoTRAGEvaluator(
    provider="openai",
    model="gpt-4o"
)

# Define evaluation queries and ground truth
evaluation_data = [
    {
        "query": "What is the difference between RAG and traditional search?",
        "ground_truth": "RAG generates new content based on retrieved documents, while traditional search only retrieves existing documents."
    },
    {
        "query": "How does CoT improve RAG?",
        "ground_truth": "CoT improves RAG by making the reasoning process explicit, which leads to more accurate and explainable answers."
    }
]

# Evaluate CoT-RAG
evaluation_results = evaluator.evaluate(cot_rag, evaluation_data)

# Print evaluation results
print(f"Average accuracy: {evaluation_results['accuracy']:.2f}")
print(f"Average relevance: {evaluation_results['relevance']:.2f}")
print(f"Average reasoning quality: {evaluation_results['reasoning_quality']:.2f}")
```

### Advanced Metrics

```python
from deep_research_core.evaluation import (
    calculate_rouge, calculate_bleu, calculate_bertscore,
    calculate_faithfulness, calculate_answer_relevance
)

# Calculate ROUGE score
rouge_score = calculate_rouge(
    prediction=result["answer"],
    reference="RAG generates new content based on retrieved documents, while traditional search only retrieves existing documents."
)
print(f"ROUGE-L: {rouge_score['rouge-l']:.4f}")

# Calculate BLEU score
bleu_score = calculate_bleu(
    prediction=result["answer"],
    reference="RAG generates new content based on retrieved documents, while traditional search only retrieves existing documents."
)
print(f"BLEU: {bleu_score:.4f}")

# Calculate BERTScore
bert_score = calculate_bertscore(
    prediction=result["answer"],
    reference="RAG generates new content based on retrieved documents, while traditional search only retrieves existing documents."
)
print(f"BERTScore: {bert_score:.4f}")

# Calculate faithfulness (how well the answer is grounded in the retrieved documents)
faithfulness_score = calculate_faithfulness(
    answer=result["answer"],
    documents=result["documents"]
)
print(f"Faithfulness: {faithfulness_score:.4f}")

# Calculate answer relevance (how relevant the answer is to the query)
relevance_score = calculate_answer_relevance(
    query=result["query"],
    answer=result["answer"]
)
print(f"Relevance: {relevance_score:.4f}")
```

## 7. Multilingual Support

EnhancedCoTRAG supports multiple languages through its `language` parameter.

```python
# Initialize EnhancedCoTRAG with Vietnamese language support
enhanced_cot_rag_vi = EnhancedCoTRAG(
    rag_instance=rag,
    provider="openai",
    model="gpt-4o",
    language="vi",
    verbose=True
)

# Process a Vietnamese query
result = enhanced_cot_rag_vi.process("RAG và tìm kiếm truyền thống khác nhau như thế nào?")
print(result["answer"])
```

### Supported Languages

- English (`en`)
- Vietnamese (`vi`)
- Chinese (`zh`)
- Spanish (`es`)
- French (`fr`)
- German (`de`)
- Japanese (`ja`)
- Korean (`ko`)
- Russian (`ru`)
- Arabic (`ar`)
- Portuguese (`pt`)
- Italian (`it`)
- Dutch (`nl`)
- Swedish (`sv`)
- Thai (`th`)
- Indonesian (`id`)

### Custom Language Support

```python
# Define custom prompts for a new language
custom_prompts = {
    "system_prompt": "Bạn là một trợ lý hữu ích...",
    "user_prompt_template": "Câu hỏi: {query}\n\nTài liệu đã truy xuất:\n{documents}\n\nHãy suy nghĩ từng bước một để tìm câu trả lời:"
}

# Initialize EnhancedCoTRAG with custom language support
enhanced_cot_rag_custom = EnhancedCoTRAG(
    rag_instance=rag,
    provider="openai",
    model="gpt-4o",
    language="custom",
    custom_prompts=custom_prompts,
    verbose=True
)
```

## 8. Monitoring and Observability

### Structured Logging

```python
from deep_research_core.utils.structured_logging import get_logger

# Create a logger
logger = get_logger("cot_rag_example")

# Process a query and log the result
result = cot_rag.process("What is the difference between RAG and traditional search?")
logger.info(
    "Processed query",
    extra={
        "query": result["query"],
        "latency": result["latency"],
        "num_documents": len(result["documents"]),
        "model": result["model"],
        "provider": result["provider"]
    }
)
```

### Performance Metrics

```python
from deep_research_core.utils.performance_metrics import (
    measure_latency, start_metrics_collection, get_metrics_snapshot
)

# Start collecting metrics
start_metrics_collection()

# Define a function with latency measurement
@measure_latency("cot_rag_process")
def process_with_metrics(query):
    return cot_rag.process(query)

# Process a query
result = process_with_metrics("What is the difference between RAG and traditional search?")

# Get metrics
metrics = get_metrics_snapshot()
print(f"CoT-RAG process latency (p95): {metrics['latency']['cot_rag_process']['p95_ms']} ms")
```

### Distributed Tracing

```python
from deep_research_core.utils.distributed_tracing import configure_tracing, trace_function

# Configure tracing
configure_tracing(service_name="cot-rag-example")

# Define a function with tracing
@trace_function(name="process_query")
def process_with_tracing(query):
    return cot_rag.process(query)

# Process a query
result = process_with_tracing("What is the difference between RAG and traditional search?")
```

### Alerting

```python
from deep_research_core.utils.alerting import (
    add_threshold_alert, AlertSeverity, start_alerting
)

# Add an alert for high latency
add_threshold_alert(
    name="high_cot_rag_latency",
    description="CoT-RAG processing is taking too long",
    metric_path="latency.cot_rag_process.p95_ms",
    threshold=5000,  # 5000ms = 5s
    comparison=">",
    severity=AlertSeverity.WARNING
)

# Start alerting
start_alerting()
```

## 9. Best Practices

### Prompt Engineering

1. **Be Explicit**: Explicitly instruct the model to think step by step
2. **Structure the Reasoning**: Provide a clear structure for the reasoning process
3. **Ask for Citations**: Encourage the model to cite the retrieved documents
4. **Break Down Complex Questions**: Split complex questions into simpler sub-questions
5. **Encourage Self-Critique**: Ask the model to review and critique its own reasoning

### Document Preparation

1. **Chunk Appropriately**: Use appropriate chunk sizes for your documents
2. **Maintain Context**: Ensure chunks contain enough context to be meaningful
3. **Include Metadata**: Add relevant metadata to documents for filtering
4. **Remove Duplicates**: Deduplicate documents to avoid redundancy
5. **Preprocess Text**: Clean and normalize text before embedding

### Model Selection

1. **Consider Capabilities**: Choose models with strong reasoning capabilities
2. **Balance Performance and Cost**: Consider the trade-off between quality and cost
3. **Test Multiple Models**: Evaluate different models for your specific use case
4. **Use the Right Temperature**: Lower temperature for factual questions, higher for creative ones
5. **Set Appropriate Token Limits**: Ensure max_tokens is sufficient for the reasoning process

### Error Handling

1. **Handle Timeouts**: Implement timeout handling for long-running queries
2. **Retry on Failure**: Retry failed API calls with exponential backoff
3. **Fallback Mechanisms**: Implement fallbacks for when the primary model fails
4. **Validate Outputs**: Check that outputs meet expected format and quality
5. **Log Errors**: Log detailed error information for debugging

## 10. Examples

### Basic CoT-RAG Example

```python
from deep_research_core.reasoning import CoTRAG, SQLiteVectorRAG

# Initialize a RAG instance
rag = SQLiteVectorRAG(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    embedding_model="all-MiniLM-L6-v2",
    db_path="my_documents.db"
)

# Add documents
documents = [
    {
        "content": "RAG (Retrieval-Augmented Generation) combines retrieval systems with generative models. It retrieves relevant documents from a knowledge base and uses them to generate more accurate and factual responses.",
        "source": "RAG Paper",
        "title": "Retrieval-Augmented Generation for Knowledge-Intensive NLP Tasks"
    },
    {
        "content": "Traditional search systems like Google retrieve documents based on relevance to a query but don't generate new content. They simply return the most relevant existing documents.",
        "source": "Search Systems Overview",
        "title": "Introduction to Information Retrieval"
    }
]
rag.add_documents(documents)

# Initialize CoTRAG
cot_rag = CoTRAG(
    rag_instance=rag,
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    verbose=True
)

# Process a query
result = cot_rag.process("What is the difference between RAG and traditional search?")

# Print the answer
print(result["answer"])
```

### Enhanced CoT-RAG Example

```python
from deep_research_core.reasoning import EnhancedCoTRAG, SQLiteVectorRAG

# Initialize a RAG instance
rag = SQLiteVectorRAG(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    embedding_model="all-MiniLM-L6-v2",
    db_path="my_documents.db"
)

# Add documents
documents = [
    {
        "content": "RAG (Retrieval-Augmented Generation) combines retrieval systems with generative models. It retrieves relevant documents from a knowledge base and uses them to generate more accurate and factual responses.",
        "source": "RAG Paper",
        "title": "Retrieval-Augmented Generation for Knowledge-Intensive NLP Tasks"
    },
    {
        "content": "Traditional search systems like Google retrieve documents based on relevance to a query but don't generate new content. They simply return the most relevant existing documents.",
        "source": "Search Systems Overview",
        "title": "Introduction to Information Retrieval"
    }
]
rag.add_documents(documents)

# Initialize EnhancedCoTRAG
enhanced_cot_rag = EnhancedCoTRAG(
    rag_instance=rag,
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    language="en",
    use_hybrid_search=True,
    use_reranking=True,
    use_query_expansion=True,
    verbose=True
)

# Process a query
result = enhanced_cot_rag.process("What is the difference between RAG and traditional search?")

# Print the answer
print(result["answer"])

# Print enhanced features
print("\nEnhanced Features:")
print(f"Hybrid Search: {result['enhanced_features']['hybrid_search']}")
print(f"Reranking: {result['enhanced_features']['reranking']}")
print(f"Query Expansion: {result['enhanced_features']['query_expansion']}")
if "expanded_query" in result:
    print(f"Expanded Query: {result['expanded_query']}")
```

### Multilingual CoT-RAG Example

```python
from deep_research_core.reasoning import EnhancedCoTRAG, SQLiteVectorRAG

# Initialize a RAG instance
rag = SQLiteVectorRAG(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    embedding_model="all-MiniLM-L6-v2",
    db_path="my_documents.db"
)

# Add documents (in multiple languages)
documents = [
    {
        "content": "RAG (Retrieval-Augmented Generation) combines retrieval systems with generative models. It retrieves relevant documents from a knowledge base and uses them to generate more accurate and factual responses.",
        "source": "RAG Paper",
        "title": "Retrieval-Augmented Generation for Knowledge-Intensive NLP Tasks"
    },
    {
        "content": "RAG (Truy xuất-Tăng cường Tạo sinh) kết hợp hệ thống truy xuất với mô hình tạo sinh. Nó truy xuất tài liệu liên quan từ cơ sở kiến thức và sử dụng chúng để tạo ra phản hồi chính xác và thực tế hơn.",
        "source": "RAG Paper Vietnamese",
        "title": "Tạo sinh Tăng cường Truy xuất cho Các Tác vụ NLP Đòi hỏi Kiến thức"
    }
]
rag.add_documents(documents)

# Initialize EnhancedCoTRAG with Vietnamese language support
enhanced_cot_rag_vi = EnhancedCoTRAG(
    rag_instance=rag,
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    language="vi",
    use_hybrid_search=True,
    verbose=True
)

# Process a Vietnamese query
result = enhanced_cot_rag_vi.process("RAG là gì và nó khác với tìm kiếm truyền thống như thế nào?")

# Print the answer
print(result["answer"])
```

## Conclusion

Chain of Thought RAG combines the power of retrieval-augmented generation with explicit reasoning, resulting in more accurate, explainable, and reliable responses. By using the CoT-RAG implementations in Deep Research Core, you can significantly improve the quality of your AI-generated content while maintaining transparency in the reasoning process.

The two implementations, CoTRAG and EnhancedCoTRAG, provide a range of features to suit different use cases, from basic CoT-RAG to advanced features like multilingual support, hybrid search, query expansion, and document reranking.

By following the best practices and examples in this guide, you can effectively leverage CoT-RAG to build more powerful and reliable AI applications.
