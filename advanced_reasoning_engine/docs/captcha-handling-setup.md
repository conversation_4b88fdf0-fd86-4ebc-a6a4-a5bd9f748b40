# Hướng dẫn cài đặt xử lý CAPTCHA

Tài liệu này hướng dẫn cách cài đặt và cấu hình các thành phần cần thiết để sử dụng tính năng xử lý CAPTCHA trong WebSearchAgent.

## 1. Cài đặt Dependencies

### 1.1. Dependencies cơ bản

Tính năng xử lý CAPTCHA yêu cầu các thư viện Python sau:

```bash
pip install requests beautifulsoup4
```

### 1.2. Cài đặt Anti-CAPTCHA (tùy chọn)

Để sử dụng dịch vụ Anti-CAPTCHA (dịch vụ giải CAPTCHA tự động), cài đặt thư viện chính thức:

```bash
pip install anticaptchaofficial
```

Bạn cũng cần [đăng ký tài khoản Anti-CAPTCHA](https://anti-captcha.com/) và mua các đơn vị giải CAPTCHA.

### 1.3. Cài đặt Selenium (tù<PERSON> chọn)

Để sử dụng Selenium cho việc giải CAPTCHA tự động thông qua trình duyệt:

```bash
pip install selenium
```

Bạn cũng cần cài đặt WebDriver tương ứng với trình duyệt của mình:

- **Chrome**: 
  1. Tải [ChromeDriver](https://sites.google.com/a/chromium.org/chromedriver/downloads)
  2. Giải nén và đặt vào thư mục trong `PATH` (ví dụ: `/usr/local/bin`)

- **Firefox**:
  1. Tải [GeckoDriver](https://github.com/mozilla/geckodriver/releases)
  2. Giải nén và đặt vào thư mục trong `PATH`

## 2. Cấu hình

### 2.1. Cấu hình Anti-CAPTCHA

1. Đăng ký tài khoản trên [anti-captcha.com](https://anti-captcha.com/)
2. Nạp tiền vào tài khoản
3. Lấy API key từ trang cài đặt tài khoản
4. Thêm API key vào biến môi trường hoặc cung cấp trực tiếp khi khởi tạo WebSearchAgent:

```bash
# Thêm vào file .env hoặc .bashrc
export ANTICAPTCHA_KEY="your_anticaptcha_api_key"
```

### 2.2. Cấu hình Proxy (tùy chọn)

Nếu bạn muốn sử dụng tính năng luân chuyển proxy, tạo một file text chứa danh sách các proxy, mỗi proxy trên một dòng:

```
http://proxy1.example.com:8080
http://username:<EMAIL>:8080
https://proxy3.example.com:443
```

## 3. Kiểm tra cài đặt

Để kiểm tra xem các tính năng CAPTCHA đã được cài đặt đúng cách, chạy script kiểm tra:

```python
from deep_research_core.utils.captcha_handler import CaptchaHandler

# Kiểm tra Anti-CAPTCHA
try:
    import anticaptchaofficial
    print("Anti-CAPTCHA đã được cài đặt thành công")
except ImportError:
    print("Anti-CAPTCHA chưa được cài đặt")

# Kiểm tra Selenium
try:
    from selenium import webdriver
    print("Selenium đã được cài đặt thành công")
    
    # Thử khởi tạo driver
    try:
        options = webdriver.chrome.options.Options()
        options.add_argument("--headless")
        driver = webdriver.Chrome(options=options)
        print("ChromeDriver đã được cài đặt đúng cách")
        driver.quit()
    except Exception as e:
        print(f"ChromeDriver chưa được cài đặt đúng cách: {str(e)}")
except ImportError:
    print("Selenium chưa được cài đặt")
```

## 4. Ví dụ sử dụng

Bạn có thể chạy ví dụ mẫu có sẵn để kiểm tra chức năng xử lý CAPTCHA:

```bash
# Chạy với API key từ biến môi trường
python examples/web_search_captcha_example.py

# Chạy với API key được cung cấp trực tiếp
python examples/web_search_captcha_example.py --api-key your_api_key

# Chạy với Selenium
python examples/web_search_captcha_example.py --use-selenium

# Chạy với proxy
python examples/web_search_captcha_example.py --proxy-file proxies.txt

# Chạy với truy vấn cụ thể
python examples/web_search_captcha_example.py --query "Truy vấn tìm kiếm của bạn"
```

## 5. Khắc phục sự cố

### 5.1. Lỗi Anti-CAPTCHA

- **Lỗi API key không hợp lệ**: Kiểm tra lại API key và chắc chắn rằng tài khoản của bạn đã được nạp tiền.
- **Lỗi không đủ tiền**: Nạp thêm tiền vào tài khoản Anti-CAPTCHA.

### 5.2. Lỗi Selenium

- **WebDriver không tìm thấy**: Đảm bảo WebDriver đã được cài đặt và nằm trong PATH.
- **Lỗi trình duyệt không tương thích**: Cập nhật WebDriver phù hợp với phiên bản trình duyệt.

### 5.3. Lỗi Proxy

- **Proxy không hoạt động**: Kiểm tra xem proxy có còn hoạt động hay không.
- **Lỗi xác thực proxy**: Kiểm tra lại thông tin đăng nhập proxy nếu có.

## 6. Tài liệu tham khảo

- [Anti-CAPTCHA API Documentation](https://anti-captcha.com/apidoc)
- [Selenium Documentation](https://selenium-python.readthedocs.io/)
- [WebSearchAgent Documentation](./agent-web-search.md) 