# Advanced Querying for SharedMemory

Tài liệu này mô tả các tính năng truy vấn nâng cao cho `SharedMemory` trong hệ thống Multi-Agent.

## Tổng quan

`SharedMemory` cung cấp một kho lưu trữ thông tin chung mà tất cả các agent có thể truy cập và cập nhật trong quá trình hợp tác. Với tính năng truy vấn nâng cao, các agent có thể tìm kiếm thông tin một cách hiệu quả hơn bằng cách sử dụng ngôn ngữ truy vấn đơn giản nhưng mạnh mẽ.

## Ngôn ngữ truy vấn

Ngôn ngữ truy vấn hỗ trợ các tính năng sau:

### Truy vấn cơ bản

- `key:value` - Tìm kiếm theo key chính xác
- `key:prefix:value` - Tìm kiếm theo prefix của key
- `content:value` - T<PERSON>m kiếm theo nội dung chính xác
- `content~value` - Tìm kiếm theo nội dung chứa value
- `metadata.field:value` - Tìm kiếm theo trường metadata

### Toán tử so sánh

- `:` - Bằng (mặc định)
- `~` - Chứa (substring)
- `>` - Lớn hơn
- `>=` - Lớn hơn hoặc bằng
- `<` - Nhỏ hơn
- `<=` - Nhỏ hơn hoặc bằng

### Toán tử logic

- `AND` - Kết hợp các điều kiện (mặc định khi có nhiều điều kiện)
- `OR` - Một trong các điều kiện thỏa mãn
- `NOT` - Phủ định điều kiện

### Nhóm điều kiện

- `(...)` - Nhóm các điều kiện để kiểm soát thứ tự ưu tiên

## Ví dụ truy vấn

### Truy vấn cơ bản

```python
# Tìm kiếm theo key chính xác
memory.query("key:solution_123")

# Tìm kiếm theo prefix của key
memory.query("key:prefix:solution")

# Tìm kiếm theo nội dung
memory.query("content~important information")

# Tìm kiếm theo metadata
memory.query("metadata.priority:high")
```

### Truy vấn phức tạp

```python
# Kết hợp nhiều điều kiện với AND
memory.query("metadata.category:task AND metadata.priority:high")

# Kết hợp nhiều điều kiện với OR
memory.query("created_by:agent1 OR created_by:agent2")

# Sử dụng NOT
memory.query("metadata.category:task AND NOT metadata.priority:low")

# Sử dụng nhóm điều kiện
memory.query("(metadata.category:task OR metadata.category:project) AND metadata.priority:high")
```

### Truy vấn thời gian

```python
# Tìm kiếm các mục được tạo sau một thời điểm
memory.query("created_at>2023-01-01")

# Tìm kiếm các mục được cập nhật trong vòng 1 giờ
memory.query("last_updated<1h")
```

## Tìm kiếm theo độ tương đồng

Ngoài ngôn ngữ truy vấn, `SharedMemory` còn hỗ trợ tìm kiếm theo độ tương đồng ngữ nghĩa:

```python
# Định nghĩa hàm embedder
def get_embedding(text):
    # Implement embedding logic here
    # For example, using a pre-trained model
    return [0.1, 0.2, 0.3, ...]  # Vector embedding

# Tìm kiếm theo độ tương đồng
results = memory.search_by_similarity(
    text="How to implement authentication?",
    top_k=5,
    threshold=0.7,
    embedder=get_embedding
)
```

## Thống kê bộ nhớ

`SharedMemory` cung cấp phương thức để lấy thống kê về dữ liệu đã lưu trữ:

```python
stats = memory.get_statistics()
print(f"Total items: {stats['total_items']}")
print(f"Items by agent: {stats['items_by_agent']}")
print(f"Access counts: {stats['access_counts']}")
```

## Ví dụ hoàn chỉnh

```python
from deep_research_core.multi_agent.core import SharedMemory

# Khởi tạo SharedMemory
memory = SharedMemory()

# Lưu trữ dữ liệu
memory.store(
    key="task_123",
    value="Implement authentication system",
    metadata={"priority": "high", "category": "development"},
    agent_id="manager"
)

memory.store(
    key="solution_123",
    value={"approach": "Use OAuth2", "libraries": ["authlib", "flask-login"]},
    metadata={"task_id": "123", "status": "proposed"},
    agent_id="developer"
)

# Truy vấn đơn giản
results = memory.query("metadata.priority:high")

# Truy vấn phức tạp
results = memory.query("metadata.category:development AND created_by:manager")

# Tìm kiếm theo độ tương đồng
def get_embedding(text):
    # Implement embedding logic
    return [0.1, 0.2, 0.3]  # Simplified example

results = memory.search_by_similarity(
    text="authentication implementation",
    embedder=get_embedding
)

# Lấy thống kê
stats = memory.get_statistics()
```

## Hiệu suất và tối ưu hóa

Khi số lượng mục trong bộ nhớ tăng lên, hiệu suất truy vấn có thể bị ảnh hưởng. Một số gợi ý để tối ưu hóa:

1. **Sử dụng truy vấn cụ thể**: Truy vấn càng cụ thể càng tốt để giảm số lượng kết quả cần xử lý.
2. **Giới hạn kết quả**: Sử dụng tham số `limit` để giới hạn số lượng kết quả trả về.
3. **Cân nhắc lưu trữ ngoài**: Đối với dữ liệu lớn, cân nhắc sử dụng các loại lưu trữ khác như cơ sở dữ liệu.

## Tích hợp với các thành phần khác

`SharedMemory` với khả năng truy vấn nâng cao có thể được tích hợp với các thành phần khác trong hệ thống:

- **Agent**: Các agent có thể sử dụng truy vấn nâng cao để tìm kiếm thông tin liên quan đến nhiệm vụ của họ.
- **TaskDecomposer**: Có thể sử dụng để tìm kiếm các subtask đã hoàn thành hoặc đang thực hiện.
- **ConsensusMechanism**: Có thể sử dụng để tìm kiếm các đề xuất trước đó để tham khảo.

## Kết luận

Tính năng truy vấn nâng cao của `SharedMemory` cung cấp một cách mạnh mẽ để các agent tìm kiếm và truy xuất thông tin trong hệ thống multi-agent. Bằng cách sử dụng ngôn ngữ truy vấn đơn giản nhưng linh hoạt, các agent có thể tìm kiếm thông tin một cách hiệu quả, cải thiện khả năng hợp tác và giải quyết vấn đề.
