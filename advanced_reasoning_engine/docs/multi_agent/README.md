# Multi-Agent Collaboration Framework

This document provides guidelines and implementation details for the Multi-Agent Collaboration framework in Deep Research Core.

## Overview

The Multi-Agent Collaboration framework enables multiple AI agents to work together to solve complex tasks. By combining specialized capabilities and using effective communication protocols, the system can tackle problems that would be difficult for a single agent.

## Architecture

The Multi-Agent Collaboration system consists of the following components:

1. **Agent Communication Protocol**: Enables structured communication between agents
2. **Role Specialization System**: Allows agents to assume specific roles with specialized capabilities
3. **Consensus Mechanism**: Helps agents reach agreement when opinions differ
4. **Task Decomposition Engine**: Breaks complex tasks into subtasks that can be delegated
5. **Shared Memory System**: Provides a unified knowledge base accessible by all agents

## Implementation Details

### 1. Agent Communication Protocol

The communication protocol defines how agents exchange information, using a standardized message format:

```python
class AgentMessage:
    def __init__(
        self,
        sender_id: str,
        receiver_id: str,
        message_type: str,
        content: Dict[str, Any],
        timestamp: Optional[float] = None,
        message_id: Optional[str] = None,
        in_reply_to: Optional[str] = None
    ):
        self.sender_id = sender_id
        self.receiver_id = receiver_id
        self.message_type = message_type  # e.g., "request", "response", "inform", "query"
        self.content = content
        self.timestamp = timestamp or time.time()
        self.message_id = message_id or str(uuid.uuid4())
        self.in_reply_to = in_reply_to  # ID of the message this is replying to

    def to_dict(self) -> Dict[str, Any]:
        return {
            "sender_id": self.sender_id,
            "receiver_id": self.receiver_id,
            "message_type": self.message_type,
            "content": self.content,
            "timestamp": self.timestamp,
            "message_id": self.message_id,
            "in_reply_to": self.in_reply_to
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "AgentMessage":
        return cls(
            sender_id=data["sender_id"],
            receiver_id=data["receiver_id"],
            message_type=data["message_type"],
            content=data["content"],
            timestamp=data["timestamp"],
            message_id=data["message_id"],
            in_reply_to=data.get("in_reply_to")
        )
```

### 2. Role Specialization System

The role system defines specialized capabilities for agents:

```python
class AgentRole:
    def __init__(
        self,
        name: str,
        description: str,
        capabilities: List[str],
        knowledge_areas: List[str],
        system_prompt: str
    ):
        self.name = name
        self.description = description
        self.capabilities = capabilities
        self.knowledge_areas = knowledge_areas
        self.system_prompt = system_prompt

    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "description": self.description,
            "capabilities": self.capabilities,
            "knowledge_areas": self.knowledge_areas,
            "system_prompt": self.system_prompt
        }
```

Implement predefined roles such as:
- Research Specialist
- Data Analyst
- Planning Coordinator
- Critical Evaluator
- Creative Thinker

### 3. Consensus Mechanism

The consensus mechanism helps agents reach agreement:

```python
class ConsensusMechanism:
    def __init__(
        self,
        strategy: str = "voting",  # "voting", "weighted_voting", "discussion", "bayesian", "expert_weighted"
        confidence_threshold: float = 0.7,
        max_iterations: int = 3
    ):
        self.strategy = strategy
        self.confidence_threshold = confidence_threshold
        self.max_iterations = max_iterations

        # Initialize Bayesian and Expert-weighted consensus mechanisms if needed
        self._bayesian_consensus = None
        self._expert_weighted_consensus = None

        if strategy in ["bayesian", "expert_weighted"]:
            try:
                from .bayesian_consensus import BayesianConsensus, ExpertWeightedConsensus
                self._bayesian_consensus = BayesianConsensus()
                self._expert_weighted_consensus = ExpertWeightedConsensus()
            except ImportError:
                logger.warning("Bayesian consensus module not available. Falling back to weighted voting.")
                self.strategy = "weighted_voting"

    def reach_consensus(
        self,
        proposals: List[Dict[str, Any]],
        agents: List["Agent"],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Reach consensus among agents based on the chosen strategy.

        Args:
            proposals: List of proposed solutions/answers
            agents: List of agents participating in consensus
            context: Additional context about the task

        Returns:
            The consensus solution/answer
        """
        if self.strategy == "voting":
            return self._simple_voting(proposals)
        elif self.strategy == "weighted_voting":
            return self._weighted_voting(proposals, agents)
        elif self.strategy == "discussion":
            return self._discussion_based(proposals, agents, context)
        elif self.strategy == "bayesian":
            # Use Bayesian consensus if available
            if self._bayesian_consensus:
                return self._bayesian_consensus.combine_beliefs(proposals, agents, context)
            else:
                logger.warning("Bayesian consensus not available. Falling back to weighted voting.")
                return self._weighted_voting(proposals, agents)
        elif self.strategy == "expert_weighted":
            # Use Expert-weighted consensus if available
            if self._expert_weighted_consensus:
                return self._expert_weighted_consensus.reach_consensus(proposals, agents, context)
            else:
                logger.warning("Expert-weighted consensus not available. Falling back to weighted voting.")
                return self._weighted_voting(proposals, agents)
        else:
            raise ValueError(f"Unknown consensus strategy: {self.strategy}")
```

The consensus mechanism now supports five different strategies:

1. **Simple Voting**: Basic majority voting where each proposal gets one vote.

2. **Weighted Voting**: Proposals are weighted based on agent expertise and confidence.

3. **Discussion-based**: Agents engage in multiple rounds of discussion to reach consensus.

4. **Bayesian Consensus**: Uses Bayesian inference to combine beliefs, accounting for prior probabilities, agent expertise, and confidence levels.

5. **Expert-weighted Consensus**: Weights agent opinions based on their domain-specific expertise relevant to the current task.

### Bayesian Consensus

The Bayesian consensus mechanism combines agent beliefs using Bayesian inference principles:

```python
class BayesianConsensus:
    def __init__(
        self,
        prior_strength: float = 1.0,
        expertise_weight: float = 2.0,
        confidence_scale: float = 1.0,
        min_evidence_weight: float = 0.5
    ):
        self.prior_strength = prior_strength
        self.expertise_weight = expertise_weight
        self.confidence_scale = confidence_scale
        self.min_evidence_weight = min_evidence_weight

    def combine_beliefs(
        self,
        proposals: List[Dict[str, Any]],
        agents: List[Any],
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Combine beliefs from multiple agents using Bayesian inference.

        Args:
            proposals: List of proposed solutions/answers from agents
            agents: List of agents participating in consensus
            context: Additional context about the task

        Returns:
            The consensus solution/answer with metadata
        """
        # Implementation details...
```

### Expert-weighted Consensus

The Expert-weighted consensus mechanism weights agent opinions based on their domain-specific expertise:

```python
class ExpertWeightedConsensus:
    def __init__(
        self,
        expertise_factor: float = 2.0,
        confidence_weight: float = 1.0,
        domain_specificity: float = 1.5,
        min_weight: float = 0.2
    ):
        self.expertise_factor = expertise_factor
        self.confidence_weight = confidence_weight
        self.domain_specificity = domain_specificity
        self.min_weight = min_weight

    def reach_consensus(
        self,
        proposals: List[Dict[str, Any]],
        agents: List[Any],
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Reach consensus by weighting agent proposals based on expertise.

        Args:
            proposals: List of proposed solutions/answers from agents
            agents: List of agents participating in consensus
            context: Additional context about the task

        Returns:
            The consensus solution/answer with metadata
        """
        # Implementation details...
```

### 4. Task Decomposition Engine

The task decomposition engine breaks complex tasks into manageable subtasks:

```python
class TaskDecomposer:
    def __init__(
        self,
        language_model,
        decomposition_strategy: str = "hierarchical",
        max_depth: int = 3
    ):
        self.language_model = language_model
        self.decomposition_strategy = decomposition_strategy
        self.max_depth = max_depth

    def decompose_task(
        self,
        task_description: str,
        available_roles: List[str] = None
    ) -> Dict[str, Any]:
        """
        Decompose a complex task into subtasks.

        Args:
            task_description: Description of the main task
            available_roles: List of agent roles available for assignment

        Returns:
            Dictionary containing subtasks and their dependencies
        """
        # Implementation details for task decomposition
        pass
```

### 5. Shared Memory System

The shared memory system provides a unified knowledge base:

```python
class SharedMemory:
    def __init__(self, storage_type: str = "in_memory"):
        self.storage_type = storage_type
        self.memory = {}
        self.access_log = []

    def store(
        self,
        key: str,
        value: Any,
        metadata: Dict[str, Any] = None,
        agent_id: str = None
    ) -> None:
        """Store information in shared memory."""
        self.memory[key] = {
            "value": value,
            "metadata": metadata or {},
            "created_by": agent_id,
            "created_at": time.time(),
            "last_updated": time.time()
        }
        self._log_access("store", key, agent_id)

    def retrieve(
        self,
        key: str,
        agent_id: str = None
    ) -> Optional[Any]:
        """Retrieve information from shared memory."""
        if key in self.memory:
            self._log_access("retrieve", key, agent_id)
            return self.memory[key]["value"]
        return None

    def update(
        self,
        key: str,
        value: Any,
        metadata: Dict[str, Any] = None,
        agent_id: str = None
    ) -> bool:
        """Update existing information in shared memory."""
        if key in self.memory:
            self.memory[key]["value"] = value
            if metadata:
                self.memory[key]["metadata"].update(metadata)
            self.memory[key]["last_updated"] = time.time()
            self.memory[key]["last_updated_by"] = agent_id
            self._log_access("update", key, agent_id)
            return True
        return False

    def _log_access(self, operation: str, key: str, agent_id: str = None) -> None:
        """Log memory access for analysis."""
        self.access_log.append({
            "operation": operation,
            "key": key,
            "agent_id": agent_id,
            "timestamp": time.time()
        })
```

## Multi-Agent System Class

The core `MultiAgentSystem` class orchestrates the collaboration:

```python
class MultiAgentSystem:
    def __init__(
        self,
        agents: List["Agent"],
        communication_protocol=None,
        consensus_mechanism=None,
        task_decomposer=None,
        shared_memory=None,
        language_model=None
    ):
        self.agents = {agent.id: agent for agent in agents}
        self.communication_protocol = communication_protocol or AgentCommunicationProtocol()
        self.consensus_mechanism = consensus_mechanism or ConsensusMechanism()
        self.task_decomposer = task_decomposer or TaskDecomposer(language_model)
        self.shared_memory = shared_memory or SharedMemory()
        self.language_model = language_model
        self.conversation_history = []

    def solve_task(
        self,
        task_description: str,
        max_iterations: int = 10,
        timeout: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        Solve a complex task using multi-agent collaboration.

        Args:
            task_description: Description of the task to solve
            max_iterations: Maximum number of collaboration iterations
            timeout: Maximum time in seconds to spend on the task

        Returns:
            Solution to the task
        """
        # Step 1: Decompose the task
        subtasks = self.task_decomposer.decompose_task(
            task_description,
            available_roles=[agent.role.name for agent in self.agents.values()]
        )

        # Step 2: Assign subtasks to agents
        assignments = self._assign_subtasks(subtasks)

        # Step 3: Execute subtasks in the correct order
        results = self._execute_subtasks(assignments, max_iterations, timeout)

        # Step 4: Synthesize the final solution
        final_solution = self._synthesize_solution(results, task_description)

        return final_solution
```

## Agent Implementation

Define the base Agent class for consistent capabilities:

```python
class Agent:
    def __init__(
        self,
        id: str,
        role: AgentRole,
        language_model,
        tools: List[BaseTool] = None,
        system_prompt: Optional[str] = None
    ):
        self.id = id
        self.role = role
        self.language_model = language_model
        self.tools = tools or []
        self.system_prompt = system_prompt or role.system_prompt
        self.memory = {}  # Agent's local memory

    def process_message(
        self,
        message: AgentMessage,
        shared_memory: SharedMemory = None
    ) -> AgentMessage:
        """
        Process an incoming message and generate a response.

        Args:
            message: The incoming message to process
            shared_memory: Optional shared memory to access

        Returns:
            Agent's response message
        """
        # Implementation for processing messages
        pass

    def solve_subtask(
        self,
        subtask: Dict[str, Any],
        shared_memory: SharedMemory = None
    ) -> Dict[str, Any]:
        """
        Solve an assigned subtask.

        Args:
            subtask: The subtask to solve
            shared_memory: Shared memory to access and update

        Returns:
            Solution to the subtask
        """
        # Implementation for solving subtasks
        pass
```

## Integration with Reasoning Methods

The Multi-Agent system should integrate with the existing reasoning methods:

```python
class ReasoningAgent(Agent):
    def __init__(
        self,
        id: str,
        role: AgentRole,
        language_model,
        reasoning_method: str = "tot",  # "tot", "cot", "react"
        reasoner = None,
        **kwargs
    ):
        super().__init__(id, role, language_model, **kwargs)

        # Initialize the appropriate reasoner
        if reasoner:
            self.reasoner = reasoner
        elif reasoning_method == "tot":
            self.reasoner = ToTReasoner(language_model=language_model)
        elif reasoning_method == "cot":
            self.reasoner = CoTReasoner(language_model=language_model)
        elif reasoning_method == "react":
            self.reasoner = ReActReasoner(language_model=language_model)
        else:
            raise ValueError(f"Unknown reasoning method: {reasoning_method}")

    def solve_subtask(
        self,
        subtask: Dict[str, Any],
        shared_memory: SharedMemory = None
    ) -> Dict[str, Any]:
        """
        Solve a subtask using the agent's reasoning method.

        Args:
            subtask: The subtask to solve
            shared_memory: Shared memory to access and update

        Returns:
            Solution to the subtask
        """
        # Adapt the subtask description for the reasoner
        query = self._format_reasoning_query(subtask, shared_memory)

        # Use the reasoner to solve the task
        reasoning_result = self.reasoner.generate_reasoning(query)

        # Extract and format the solution
        solution = self._extract_solution(reasoning_result, subtask)

        # Update shared memory with relevant information
        if shared_memory:
            self._update_shared_memory(shared_memory, subtask, solution)

        return solution
```

## Example Usage

Here's an example of how to use the Multi-Agent Collaboration framework:

```python
from deep_research_core.multi_agent.core import (
    MultiAgentSystem, Agent, AgentRole, SharedMemory,
    ConsensusMechanism, TaskDecomposer
)
from deep_research_core.models import GPT4Model

# Initialize the language model
language_model = GPT4Model()

# Create agent roles
researcher_role = AgentRole(
    name="Researcher",
    description="Specializes in gathering and analyzing information",
    capabilities=["information_search", "fact_checking", "source_analysis"],
    knowledge_areas=["research_methods", "information_evaluation"],
    system_prompt="You are a research specialist focused on gathering accurate information..."
)

analyst_role = AgentRole(
    name="Analyst",
    description="Specializes in data analysis and pattern recognition",
    capabilities=["data_analysis", "statistical_methods", "visualization"],
    knowledge_areas=["statistics", "data_science", "pattern_recognition"],
    system_prompt="You are a data analyst specialized in finding patterns..."
)

planner_role = AgentRole(
    name="Planner",
    description="Specializes in planning and coordination",
    capabilities=["task_planning", "resource_allocation", "progress_tracking"],
    knowledge_areas=["project_management", "decision_theory"],
    system_prompt="You are a planning coordinator responsible for organizing tasks..."
)

# Create agents
agents = [
    Agent(id="researcher-1", role=researcher_role, language_model=language_model),
    Agent(id="analyst-1", role=analyst_role, language_model=language_model),
    Agent(id="planner-1", role=planner_role, language_model=language_model)
]

# Initialize components
shared_memory = SharedMemory()
consensus_mechanism = ConsensusMechanism(strategy="discussion")
task_decomposer = TaskDecomposer(language_model=language_model)

# Create multi-agent system
mas = MultiAgentSystem(
    agents=agents,
    shared_memory=shared_memory,
    consensus_mechanism=consensus_mechanism,
    task_decomposer=task_decomposer,
    language_model=language_model
)

# Solve a complex task
result = mas.solve_task(
    task_description="Analyze the impact of climate change on agricultural productivity in Southeast Asia, and propose adaptation strategies."
)

print(f"Final solution: {result['solution']}")
```

## Testing Multi-Agent Collaboration

Create comprehensive tests to validate the multi-agent system:

```python
import pytest
from deep_research_core.multi_agent.core import (
    MultiAgentSystem, Agent, AgentRole, SharedMemory,
    ConsensusMechanism, TaskDecomposer, AgentMessage
)

def test_agent_communication():
    # Test that agents can exchange messages correctly
    pass

def test_role_specialization():
    # Test that agents with different roles respond appropriately
    pass

def test_consensus_mechanism():
    # Test that agents can reach consensus when given conflicting information
    pass

def test_task_decomposition():
    # Test that complex tasks are properly broken down
    pass

def test_shared_memory():
    # Test that agents can effectively share and update information
    pass

def test_end_to_end_collaboration():
    # Test the full collaboration process on sample tasks
    pass
```

## Next Steps

To implement the Multi-Agent Collaboration framework, follow these steps:

1. Implement the core classes defined in this document
2. Create specialized agent roles with appropriate system prompts
3. Develop the task decomposition engine for different types of tasks
4. Build the consensus mechanism with multiple strategies
5. Implement the shared memory system with persistence
6. Create test cases and examples
7. Document usage patterns and best practices

By following this implementation plan, we can create a robust framework for multi-agent collaboration that enhances the capabilities of Deep Research Core.