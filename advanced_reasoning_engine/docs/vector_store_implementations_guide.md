# Vector Store Implementations Guide

Deep Research Core provides multiple vector store implementations to suit different use cases and requirements. This guide explains the available implementations, their features, and how to choose the right one for your needs.

## Available Vector Store Implementations

Deep Research Core supports the following vector store implementations:

1. **MilvusRAG**: Uses Milvus, a highly scalable vector database designed for production use with large datasets.
2. **SQLiteVectorRAG**: Uses SQLite with vector search capabilities, ideal for development, testing, or small deployments.
3. **FAISSRAG**: Uses FAISS (Facebook AI Similarity Search) for efficient vector search, with optional GPU acceleration.
4. **PineconeRAG**: Uses Pinecone, a managed vector database service with high scalability and availability.
5. **WeaviateRAG**: Uses Weaviate, a vector search engine with semantic search capabilities.

All implementations inherit from the `BaseRAG` abstract class and provide a consistent interface for adding documents, searching, and processing queries.

## Common Interface

All vector store implementations share the following common interface:

```python
# Initialize a RAG instance
rag = VectorStoreRAG(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    embedding_model="text-embedding-ada-002",
    # Vector store-specific parameters...
)

# Add documents
doc_ids = rag.add_documents(documents)

# Search for documents
results = rag.search("What is RAG?")

# Process a query
response = rag.process("What is RAG?")

# Count documents
count = rag.count()

# Clear all documents
rag.clear()

# Close the connection
rag.close()
```

## MilvusRAG

MilvusRAG uses [Milvus](https://milvus.io/), an open-source vector database designed for production use with large datasets. It provides high performance, scalability, and reliability.

### Features

- High scalability for large datasets (millions to billions of vectors)
- Support for multiple index types (FLAT, IVF_FLAT, HNSW, etc.)
- Distributed architecture for horizontal scaling
- Advanced filtering capabilities
- High availability with replica sets

### Usage

```python
from deep_research_core.reasoning import MilvusRAG

# Initialize MilvusRAG
rag = MilvusRAG(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    embedding_model="text-embedding-ada-002",
    collection_name="my_collection",
    connection_args={"host": "localhost", "port": "19530"},
    embedding_dim=1536,
    index_type="HNSW",
    metric_type="IP",
    index_params={"M": 16, "efConstruction": 200},
    create_collection=True,
    top_k=5
)

# Add documents
documents = [
    {
        "content": "This is a test document.",
        "source": "Test Source",
        "title": "Test Document"
    },
    {
        "content": "This is another test document.",
        "source": "Test Source 2",
        "title": "Test Document 2"
    }
]
doc_ids = rag.add_documents(documents)

# Process a query
response = rag.process("What is RAG?")
print(response["answer"])
```

## SQLiteVectorRAG

SQLiteVectorRAG uses SQLite with vector search capabilities, ideal for development, testing, or small deployments. It provides a simple yet powerful solution for vector search without external dependencies.

### Features

- Simple setup with no external services required
- Suitable for small to medium-sized datasets
- Hybrid search combining vector and keyword search
- Automatic document updates with hash-based deduplication
- Result evaluation and performance tracking

### Usage

```python
from deep_research_core.reasoning import SQLiteVectorRAG

# Initialize SQLiteVectorRAG
rag = SQLiteVectorRAG(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    embedding_model="all-MiniLM-L6-v2",
    db_path="my_documents.db",
    create_tables=True,
    top_k=5
)

# Add documents
documents = [
    {
        "content": "This is a test document.",
        "source": "Test Source",
        "title": "Test Document"
    },
    {
        "content": "This is another test document.",
        "source": "Test Source 2",
        "title": "Test Document 2"
    }
]
doc_ids = rag.add_documents(documents)

# Process a query
response = rag.process("What is RAG?")
print(response["answer"])
```

## FAISSRAG

FAISSRAG uses [FAISS](https://github.com/facebookresearch/faiss) (Facebook AI Similarity Search) for efficient vector search, with optional GPU acceleration. It provides high performance for large datasets and complex search operations.

### Features

- High performance for large datasets
- Multiple index types for different performance/accuracy trade-offs
- GPU acceleration for faster search
- In-memory and disk-based storage options
- Approximate nearest neighbor search for faster retrieval

### Usage

```python
from deep_research_core.reasoning import FAISSRAG

# Initialize FAISSRAG
rag = FAISSRAG(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    embedding_model="text-embedding-ada-002",
    index_path="faiss_index",
    embedding_dim=1536,
    index_type="Flat",  # Options: Flat, IVF, HNSW, etc.
    metric_type="ip",   # Options: ip (inner product), l2 (Euclidean)
    use_gpu=False,      # Set to True to use GPU acceleration
    gpu_id=0,           # GPU ID to use
    top_k=5
)

# Add documents
documents = [
    {
        "content": "This is a test document.",
        "source": "Test Source",
        "title": "Test Document"
    },
    {
        "content": "This is another test document.",
        "source": "Test Source 2",
        "title": "Test Document 2"
    }
]
doc_ids = rag.add_documents(documents)

# Process a query
response = rag.process("What is RAG?")
print(response["answer"])
```

## PineconeRAG

PineconeRAG uses [Pinecone](https://www.pinecone.io/), a managed vector database service with high scalability and availability. It provides a serverless solution for vector search without the need to manage infrastructure.

### Features

- Managed service with high availability
- Serverless architecture with automatic scaling
- Support for multiple index types
- Advanced filtering capabilities
- Global distribution for low-latency access

### Usage

```python
from deep_research_core.reasoning import PineconeRAG

# Initialize PineconeRAG
rag = PineconeRAG(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    embedding_model="text-embedding-ada-002",
    api_key="your-pinecone-api-key",
    environment="your-pinecone-environment",
    index_name="your-pinecone-index",
    namespace="your-namespace",
    dimension=1536,
    metric="cosine",
    pod_type="p1",
    create_index=False,
    top_k=5
)

# Add documents
documents = [
    {
        "content": "This is a test document.",
        "source": "Test Source",
        "title": "Test Document"
    },
    {
        "content": "This is another test document.",
        "source": "Test Source 2",
        "title": "Test Document 2"
    }
]
doc_ids = rag.add_documents(documents)

# Process a query
response = rag.process("What is RAG?")
print(response["answer"])
```

## WeaviateRAG

WeaviateRAG uses [Weaviate](https://weaviate.io/), a vector search engine with semantic search capabilities. It provides a powerful solution for semantic search and knowledge graph operations.

### Features

- Semantic search with GraphQL interface
- Knowledge graph capabilities
- Automatic schema inference
- Support for multiple vector indices
- Real-time search and updates

### Usage

```python
from deep_research_core.reasoning import WeaviateRAG

# Initialize WeaviateRAG
rag = WeaviateRAG(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    embedding_model="text-embedding-ada-002",
    url="http://localhost:8080",
    api_key=None,  # Optional API key
    class_name="Document",
    batch_size=100,
    create_class=True,
    vector_index_type="hnsw",
    vector_index_config={"efConstruction": 128, "maxConnections": 64},
    top_k=5
)

# Add documents
documents = [
    {
        "content": "This is a test document.",
        "source": "Test Source",
        "title": "Test Document"
    },
    {
        "content": "This is another test document.",
        "source": "Test Source 2",
        "title": "Test Document 2"
    }
]
doc_ids = rag.add_documents(documents)

# Process a query
response = rag.process("What is RAG?")
print(response["answer"])
```

## Choosing the Right Vector Store

When choosing a vector store implementation, consider the following factors:

1. **Dataset Size**:
   - Small to medium datasets (< 100K documents): SQLiteVectorRAG or FAISSRAG
   - Large datasets (100K - 1M documents): FAISSRAG or MilvusRAG
   - Very large datasets (> 1M documents): MilvusRAG, PineconeRAG, or WeaviateRAG

2. **Deployment Environment**:
   - Development/Testing: SQLiteVectorRAG
   - Production with self-hosted infrastructure: MilvusRAG, FAISSRAG, or WeaviateRAG
   - Production with managed services: PineconeRAG

3. **Performance Requirements**:
   - Low latency: FAISSRAG with GPU acceleration
   - High throughput: MilvusRAG or PineconeRAG
   - Balance of latency and throughput: WeaviateRAG

4. **Feature Requirements**:
   - Simple vector search: SQLiteVectorRAG or FAISSRAG
   - Advanced filtering: MilvusRAG, PineconeRAG, or WeaviateRAG
   - Semantic search: WeaviateRAG
   - Knowledge graph capabilities: WeaviateRAG

## Performance Comparison

The following table provides a rough performance comparison of the different vector store implementations:

| Implementation   | Small Dataset (10K) | Medium Dataset (100K) | Large Dataset (1M+) | Setup Complexity | Maintenance Complexity |
|------------------|---------------------|----------------------|---------------------|------------------|------------------------|
| SQLiteVectorRAG  | Fast                | Moderate             | Slow                | Very Low         | Very Low               |
| FAISSRAG         | Fast                | Fast                 | Moderate            | Low              | Low                    |
| MilvusRAG        | Moderate            | Fast                 | Fast                | Moderate         | Moderate               |
| PineconeRAG      | Moderate            | Fast                 | Fast                | Low              | Very Low               |
| WeaviateRAG      | Moderate            | Fast                 | Fast                | Moderate         | Moderate               |

## Monitoring and Observability

All vector store implementations include comprehensive monitoring and observability features:

- Structured logging for all operations
- Performance metrics for latency, throughput, and memory usage
- Distributed tracing for end-to-end visibility
- Integration with external monitoring tools like Prometheus and Grafana

For more information, see the [Monitoring Guide](monitoring_guide.md).

## Advanced Features

### Hybrid Search

All vector store implementations support hybrid search, which combines vector search with keyword search for better results:

```python
# Process a query with hybrid search
response = rag.process("What is RAG?", hybrid_weight=0.5)
```

The `hybrid_weight` parameter controls the balance between vector search (1.0) and keyword search (0.0). A value of 0.5 gives equal weight to both.

### Filtering

Most vector store implementations support filtering based on metadata:

```python
# Process a query with a filter
response = rag.process("What is RAG?", filter_expr="source == 'Test Source'")
```

The filter expression format depends on the vector store implementation:

- SQLiteVectorRAG: SQL WHERE clause
- MilvusRAG: Milvus expression
- FAISSRAG: Custom filter function
- PineconeRAG: Pinecone filter expression
- WeaviateRAG: GraphQL filter

### Custom Prompts

All vector store implementations support custom prompts for the LLM:

```python
# Process a query with custom prompts
response = rag.process(
    "What is RAG?",
    custom_system_prompt="You are a helpful assistant...",
    custom_user_prompt="Question: {query}\nRetrieved Documents:\n{documents}"
)
```

## Conclusion

Deep Research Core provides a variety of vector store implementations to suit different use cases and requirements. By choosing the right implementation for your needs, you can optimize performance, scalability, and features for your RAG application.

For more information on specific implementations, see the following guides:

- [MilvusRAG Guide](milvus_rag_guide.md)
- [SQLiteVectorRAG Guide](sqlite_vector_rag_guide.md)
- [FAISSRAG Guide](faiss_rag_guide.md)
- [PineconeRAG Guide](pinecone_rag_guide.md)
- [WeaviateRAG Guide](weaviate_rag_guide.md)
