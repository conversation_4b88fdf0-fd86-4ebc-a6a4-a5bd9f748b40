# Hướng dẫn sử dụng SearXNG Local

Tài liệu này hướng dẫn cách thiết lập và sử dụng SearXNG local trong dự án Deep Research Core.

## Giới thiệu

SearXNG là một công cụ tìm kiếm meta, tổng hợp kết quả từ nhiều nguồn tìm kiếm khác nhau. Việc sử dụng SearXNG local giúp:

- Tăng độ tin cậy của kết quả tìm kiếm
- Giảm phụ thuộc vào các dịch vụ tìm kiếm bên ngoài
- Tránh bị giới hạn tốc độ (rate limiting)
- Cải thiện tính riêng tư
- Tùy chỉnh các nguồn tìm kiếm theo nhu cầu

## Cài đặt

### Yêu cầu

- <PERSON>er và Docker Compose
- Python 3.8+

### Thiết lập SearXNG local

1. Chạy script `start_searxng.sh` để khởi động SearXNG local:

```bash
# Cấp quyền thực thi cho script
chmod +x start_searxng.sh

# Chạy script
./start_searxng.sh
```

2. Kiểm tra kết nối với SearXNG local:

```bash
# Cấp quyền thực thi cho script
chmod +x test_searxng_connection.py

# Chạy script
./test_searxng_connection.py
```

3. Truy cập giao diện web của SearXNG tại: http://localhost:8080

## Cấu hình

### Cấu hình Docker

File `docker-compose.searxng.yml` chứa cấu hình Docker cho SearXNG local:

```yaml
version: '3.8'

services:
  searxng:
    image: searxng/searxng:latest
    container_name: searxng
    ports:
      - "8080:8080"
    environment:
      - BASE_URL=http://localhost:8080/
      - INSTANCE_NAME=DeepResearchSearXNG
      - AUTOCOMPLETE=google
      - DEFAULT_LANG=vi
      - ENABLE_METRICS=true
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - searxng_data:/etc/searxng
    networks:
      - searxng-network
    restart: unless-stopped
    depends_on:
      - redis
    
  redis:
    image: "redis:alpine"
    container_name: redis
    command: redis-server --save 60 1 --loglevel warning
    volumes:
      - redis_data:/data
    networks:
      - searxng-network
    restart: unless-stopped

networks:
  searxng-network:
    driver: bridge

volumes:
  searxng_data:
  redis_data:
```

### Cấu hình môi trường

Biến môi trường `SEARXNG_URL` được sử dụng để xác định URL của SearXNG local:

- Khi chạy trong Docker: `http://searxng:8080`
- Khi chạy trên máy local: `http://localhost:8080`

## Sử dụng trong code

### Sử dụng WebSearchAgent với SearXNG local

```python
from deep_research_core.agents.web_search_agent import WebSearchAgent

# Tạo WebSearchAgent với SearXNG local
agent = WebSearchAgent(
    search_method="api",  # Sử dụng phương thức API
    api_search_config={
        "engine": "searx",  # Sử dụng SearXNG
        "num_results": 10,
        "language": "auto"  # Tự động phát hiện ngôn ngữ
    },
    verbose=False,
    enable_cache=True,
    cache_ttl=3600,  # 1 giờ
    timeout=15,  # Tăng timeout để tránh lỗi kết nối
    verify_ssl=True
)

# Thực hiện tìm kiếm
results = agent.search("Tình hình kinh tế Việt Nam năm 2024", num_results=10)
```

### Sử dụng trực tiếp SearXNG API

```python
from deep_research_core.agents.searxng_search import search_with_fallback

# Tìm kiếm với SearXNG local
results = search_with_fallback(
    query="Tình hình kinh tế Việt Nam năm 2024",
    num_results=10,
    language="vi",
    max_retries=3,
    prioritize_local=True  # Ưu tiên sử dụng SearXNG local
)
```

## Kiểm tra

Chạy script `test_searxng_local.py` để kiểm tra WebSearchAgent với SearXNG local:

```bash
# Chạy với truy vấn mặc định
python examples/test_searxng_local.py

# Chạy với truy vấn tùy chỉnh
python examples/test_searxng_local.py --query "Tình hình kinh tế Việt Nam năm 2024"

# Hiển thị thông tin chi tiết
python examples/test_searxng_local.py --verbose
```

## Xử lý sự cố

### SearXNG không khởi động

Kiểm tra logs của Docker:

```bash
docker logs searxng
```

### Không thể kết nối với SearXNG

Kiểm tra xem SearXNG có đang chạy không:

```bash
docker ps | grep searxng
```

### Không nhận được kết quả tìm kiếm

Kiểm tra xem SearXNG có thể truy cập được các nguồn tìm kiếm không:

```bash
# Truy cập giao diện web của SearXNG
http://localhost:8080

# Thử tìm kiếm trực tiếp trên giao diện web
```

## Tùy chỉnh

### Thêm nguồn tìm kiếm

Truy cập giao diện web của SearXNG tại http://localhost:8080 và vào phần Preferences để tùy chỉnh các nguồn tìm kiếm.

### Thay đổi ngôn ngữ mặc định

Sửa biến môi trường `DEFAULT_LANG` trong file `docker-compose.searxng.yml`:

```yaml
environment:
  - DEFAULT_LANG=vi  # Tiếng Việt
  # hoặc
  - DEFAULT_LANG=en  # Tiếng Anh
```

## Tài liệu tham khảo

- [SearXNG Documentation](https://docs.searxng.org/)
- [SearXNG GitHub Repository](https://github.com/searxng/searxng)
- [SearXNG Docker Image](https://hub.docker.com/r/searxng/searxng)
