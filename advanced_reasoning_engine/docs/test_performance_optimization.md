# Hướng dẫn tối ưu hóa hiệu suất kiểm thử

Tài liệu này cung cấp hướng dẫn chi tiết về cách tối ưu hóa hiệu suất kiểm thử cho dự án Deep Research Core.

## Tổng quan

Tối ưu hóa hiệu suất kiểm thử là một phần quan trọng của quy trình phát triển phần mềm. Nó giúp giảm thời gian chạy kiểm thử, tăng hiệu quả của quy trình CI/CD, và cải thiện trải nghiệm phát triển.

Dự án Deep Research Core cung cấp nhiều công cụ và cấu hình để tối ưu hóa hiệu suất kiểm thử:

1. **Kiểm thử song song**: Chạy nhiều kiểm thử cùng một lúc để tận dụng tối đa tài nguyên hệ thống
2. **Bộ nhớ cache**: Sử dụng bộ nhớ cache để tránh chạy lại các kiểm thử không thay đổi
3. **Timeout**: Thiết lập thời gian chờ tối đa cho các kiểm thử để tránh bị treo
4. **Phân tích hiệu suất**: Phân tích thời gian chạy của các kiểm thử để xác định các điểm nghẽn

## Kiểm thử song song

Kiểm thử song song cho phép chạy nhiều kiểm thử cùng một lúc, giúp giảm đáng kể thời gian chạy kiểm thử.

### Cấu hình

Cấu hình kiểm thử song song được định nghĩa trong file `pytest.ini`:

```ini
# Parallel test execution configuration
xdist_concurrency = auto
xdist_worker_setup_timeout = 30.0
xdist_worker_teardown_timeout = 10.0
xdist_group_by_fixture = true
xdist_group_by_module = true
```

### Sử dụng

```bash
# Chạy kiểm thử song song với số lượng worker tự động
./scripts/run_tests.sh --parallel

# Chạy kiểm thử song song với số lượng worker cụ thể
./scripts/run_tests.sh --parallel --workers=4
```

### Tối ưu hóa

- **Nhóm theo module**: Các kiểm thử trong cùng một module sẽ được chạy trên cùng một worker, giúp tránh xung đột
- **Nhóm theo fixture**: Các kiểm thử sử dụng cùng một fixture sẽ được chạy trên cùng một worker, giúp tận dụng bộ nhớ cache
- **Cân bằng tải**: Sử dụng `--dist=loadscope` để cân bằng tải giữa các worker

## Bộ nhớ cache

Bộ nhớ cache cho phép tránh chạy lại các kiểm thử không thay đổi, giúp giảm thời gian chạy kiểm thử.

### Cấu hình

Cấu hình bộ nhớ cache được định nghĩa trong file `.pytest_cache/config`:

```ini
[pytest.ini]
cache_dir = .pytest_cache
cache_size = 10000
last_failed_paths = true
last_failed = true
last_failed_no_failures_behavior = none

[cache]
directory = .pytest_cache
```

### Sử dụng

```bash
# Sử dụng bộ nhớ cache
./scripts/run_tests.sh --cache

# Chỉ chạy lại các kiểm thử thất bại
./scripts/run_tests.sh --cache --lf
```

### Tối ưu hóa

- **Kích thước cache**: Tăng kích thước cache để lưu trữ nhiều kết quả kiểm thử hơn
- **Chỉ chạy lại các kiểm thử thất bại**: Sử dụng `--lf` để chỉ chạy lại các kiểm thử thất bại
- **Không xóa cache**: Sử dụng `--cache-clear=false` để không xóa cache trước khi chạy kiểm thử

## Timeout

Timeout cho phép thiết lập thời gian chờ tối đa cho các kiểm thử, giúp tránh bị treo.

### Cấu hình

Cấu hình timeout được định nghĩa trong file `pytest.ini`:

```ini
# Timeout configuration
timeout = 300
timeout_method = thread
timeout_func_only = false
```

### Sử dụng

```bash
# Sử dụng timeout mặc định (300 giây)
./scripts/run_tests.sh

# Sử dụng timeout tùy chỉnh
./scripts/run_tests.sh --timeout=600
```

### Tối ưu hóa

- **Timeout cho từng kiểm thử**: Sử dụng `@pytest.mark.timeout(60)` để thiết lập timeout cho từng kiểm thử
- **Timeout cho từng hàm**: Sử dụng `timeout_func_only = true` để chỉ áp dụng timeout cho từng hàm kiểm thử
- **Phương thức timeout**: Sử dụng `timeout_method = thread` để sử dụng thread cho timeout, hoặc `timeout_method = signal` để sử dụng signal

## Phân tích hiệu suất

Phân tích hiệu suất cho phép xác định các điểm nghẽn trong quy trình kiểm thử.

### Sử dụng

```bash
# Hiển thị 10 kiểm thử chậm nhất
./scripts/run_tests.sh --optimize

# Hiển thị tất cả các kiểm thử chậm hơn 1 giây
./scripts/run_tests.sh --optimize --durations-min=1.0
```

### Tối ưu hóa

- **Hiển thị kiểm thử chậm**: Sử dụng `--durations=10` để hiển thị 10 kiểm thử chậm nhất
- **Ngưỡng thời gian**: Sử dụng `--durations-min=1.0` để chỉ hiển thị các kiểm thử chậm hơn 1 giây
- **Định dạng traceback**: Sử dụng `--tb=native` để hiển thị traceback ngắn gọn hơn
- **Không hiển thị header**: Sử dụng `--no-header` để không hiển thị header

## Tối ưu hóa tổng thể

Để tối ưu hóa tổng thể hiệu suất kiểm thử, bạn có thể sử dụng tùy chọn `--optimize`:

```bash
# Tối ưu hóa tổng thể
./scripts/run_tests.sh --optimize --parallel --cache
```

Tùy chọn này sẽ bật tất cả các tối ưu hóa hiệu suất:

- Kiểm thử song song
- Bộ nhớ cache
- Timeout
- Phân tích hiệu suất
- Cân bằng tải
- Định dạng traceback ngắn gọn
- Không hiển thị header

## Tối ưu hóa cho CI/CD

Để tối ưu hóa hiệu suất kiểm thử trong môi trường CI/CD, bạn có thể sử dụng cấu hình sau trong file `.github/workflows/ci.yml`:

```yaml
- name: Run tests
  run: |
    ./scripts/run_tests.sh --optimize --parallel --cache --timeout=600
```

## Tối ưu hóa cho phát triển cục bộ

Để tối ưu hóa hiệu suất kiểm thử trong môi trường phát triển cục bộ, bạn có thể sử dụng cấu hình sau:

```bash
# Chỉ chạy lại các kiểm thử thất bại
./scripts/run_tests.sh --cache --lf

# Chạy kiểm thử song song với tối ưu hóa
./scripts/run_tests.sh --optimize --parallel --cache
```

## Các mẹo và thủ thuật

1. **Sử dụng fixture scope**: Sử dụng `@pytest.fixture(scope="module")` hoặc `@pytest.fixture(scope="session")` để tái sử dụng fixture giữa các kiểm thử
2. **Tránh fixture phụ thuộc**: Tránh sử dụng fixture phụ thuộc vào nhau, vì điều này có thể làm chậm quá trình khởi tạo
3. **Sử dụng mock**: Sử dụng mock để tránh gọi API thật trong kiểm thử
4. **Tối ưu hóa setup và teardown**: Tối ưu hóa quá trình setup và teardown để giảm thời gian chạy kiểm thử
5. **Sử dụng kiểm thử tham số hóa**: Sử dụng `@pytest.mark.parametrize` để chạy cùng một kiểm thử với nhiều bộ dữ liệu khác nhau

## Tài liệu tham khảo

- [Pytest Documentation](https://docs.pytest.org/)
- [Pytest-xdist Documentation](https://pytest-xdist.readthedocs.io/)
- [Pytest-timeout Documentation](https://pytest-timeout.readthedocs.io/)
- [Pytest-cache Documentation](https://docs.pytest.org/en/latest/how-to/cache.html)
