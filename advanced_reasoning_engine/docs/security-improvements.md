# Web Search Agent Security & Performance Improvements

This document outlines the recent security, performance, error handling, and quality improvements added to the WebSearchAgent component.

## 1. Security Enhancements 🔒

### HTML Sanitization
- Added HTML sanitization to protect against XSS and other injection attacks
- Created configurable list of allowed HTML tags (`allowed_html_tags`)
- Implemented `_sanitize_html()` method to clean potentially dangerous content
- Applied sanitization to snippets, descriptions, and content in search results

### Enhanced HTML Security with lxml
- Upgraded HTML sanitization to use lxml's Cleaner for enterprise-grade security
- Configured strict security settings to prevent XSS vulnerabilities
- Added intelligent tag and attribute filtering
- Implemented automatic removal of dangerous elements (scripts, iframes, objects, embeds)
- Added fallback to BeautifulSoup if lxml is not available

### URL Validation
- Added validation for all URLs in search results
- Implemented regex pattern matching to verify URL format
- Invalid URLs are marked with '#' and flagged with `url_valid=False`
- Prevents potential malicious URL injection

### Implementation Details
```python
def _sanitize_html(self, raw_html: str) -> str:
    """Clean potentially dangerous HTML content using lxml's Cleaner"""
    if not self.sanitize_html:
        return raw_html
        
    try:
        from lxml.html.clean import Cleaner
        
        # Create a cleaner with strict security settings
        cleaner = Cleaner(
            safe_attrs_only=True,
            safe_attrs=frozenset(['href', 'src', 'alt']),
            remove_unknown_tags=False,
            style=True,           # Remove style tags
            javascript=True,      # Remove JavaScript
            scripts=True,         # Remove script tags
            meta=True,            # Remove meta tags
            frames=True,          # Remove frames
            forms=True,           # Remove forms
            annoying_tags=True,   # Remove blink, marquee
            kill_tags=frozenset(['object', 'embed', 'iframe', 'script'])
        )
        
        return cleaner.clean_html(raw_html)
    except ImportError:
        # Fallback to BeautifulSoup if lxml cleaner is not available
        return BeautifulSoup(raw_html, 'lxml').get_text()
```

### BeautifulSoup Warning Suppression
- Added warning suppression for `MarkupResemblesLocatorWarning` when parsing HTML
- Prevents console clutter while maintaining security functionality

## 2. Performance Optimization ⚡

### LRU Cache Implementation
- Replaced simple dictionary cache with `TTLCache` from `cachetools`
- Automatically handles expiration without manual timestamp tracking
- Provides better performance for cache lookups and maintenance
- Configurable maximum size to prevent memory issues

### Enhanced Connection Pooling
- Optimized connection pooling with increased pool sizes:
  - `pool_connections=50` (default: 10)
  - `pool_maxsize=100` (default: 10)
- Improved retry mechanism with backoff logic
- Configured persistent HTTP connections for faster subsequent requests
- Reduced connection overhead for multiple requests

### Engine-Specific Rate Limiting
- Implemented engine-specific rate limits for different search providers
- Customizable request limits and time windows for each engine
- Prevents rate limit errors and API bans
- Separate tracking of request counts per engine

### Implementation Details
```python
# Engine-specific rate limits (requests, window in seconds)
self.engine_limits = {
    'duckduckgo': (100, 60),
    'duckduckgo_api': (50, 60),
    'google_scholar': (10, 300),
    'searx': (60, 60),
    'qwant': (60, 60),
    'default': (self.rate_limit, 60)
}

def _check_rate_limit(self) -> bool:
    """Check rate limit with engine-specific values."""
    current_time = time.time()
    
    # Get engine-specific limits
    limit, window = self.engine_limits.get(self.search_engine, self.engine_limits["default"])

    # Reset request count after each time window
    if current_time - self.engine_reset_times.get(self.search_engine, 0) >= window:
        self.engine_request_counts[self.search_engine] = 0
        self.engine_reset_times[self.search_engine] = current_time

    # Check rate limit
    if self.engine_request_counts.get(self.search_engine, 0) >= limit:
        return False

    # Increment request count
    self.engine_request_counts[self.search_engine] += 1
    return True
```

## 3. Error Handling Improvement 🛠️

### Robust Retry Logic
- Added configurable retry mechanism for network errors
- Implemented exponential backoff between retry attempts
- Added detailed logging of error details for debugging

### Expanded Fallback Search Mechanism
- Enhanced `_fallback_search()` with more alternative engines
- Added "searx" and "qwant" to fallback chain
- Implemented proxy rotation during fallback attempts
- Preserves original engine information in results for tracking

### Implementation Details
```python
def _fallback_search(self, query: str, num_results: int, search_type: str) -> Dict[str, Any]:
    """Fallback to alternative search engine when primary fails"""
    logger.warning(f"Primary search failed for query: {query}. Trying fallback.")
    
    # Store original engine and try fallback
    original_engine = self.search_engine
    fallback_engines = ["searx", "qwant", "duckduckgo_api", "duckduckgo"]
    
    # Remove the original engine from fallbacks
    if original_engine in fallback_engines:
        fallback_engines.remove(original_engine)
        
    for engine in fallback_engines:
        # Rotate proxy if enabled
        if self.proxy_rotation:
            proxy = self._rotate_proxy()
            if proxy and self.verbose:
                logger.info(f"Using proxy {proxy} for fallback engine {engine}")
                
        self.search_engine = engine
        try:
            results = self.search(query, num_results, search_type)
            if results["success"]:
                results["fallback_used"] = True
                results["original_engine"] = original_engine
                results["fallback_engine"] = engine
                return results
        except Exception as e:
            logger.error(f"Fallback {engine} failed: {str(e)}")
```

### Proxy Rotation
- Added proxy rotation capability for resilience against IP-based rate limiting
- Implemented automatic proxy switching on failures
- Configurable proxy list with current proxy tracking

## 4. Configuration Validation ✅

### Input Parameter Validation
- Added validation for critical configuration parameters
- Implemented checks for search method and rate limit values
- Added meaningful error messages for invalid configurations
- Expanded list of supported search engines

## 5. Result Quality Control 📊

### Enhanced Quality Scoring System
- Improved `_calculate_quality_score()` with more sophisticated metrics
- Added domain authority scoring based on top-level domains and known sites
- Implemented date parsing and freshness scoring
- Enhanced URL quality checks

### Progressive Result Filtering
- Added three-stage filtering pipeline for search results:
  1. `_filter_results()`: Removes invalid URLs and low-quality content
  2. `_sort_by_quality()`: Sorts results by calculated quality score
  3. `_remove_duplicates()`: Eliminates duplicate content based on normalized URLs

### Implementation Details
```python
def _calculate_quality_score(self, result: Dict) -> float:
    """Calculate result quality score based on multiple factors"""
    score = 0.0
    
    # Score based on snippet length (max 0.3)
    snippet_length = len(result.get('snippet', ''))
    score += min(0.3, snippet_length / 500)
    
    # Score based on title quality (max 0.2)
    # ...
    
    # Score based on domain authority (max 0.3)
    score += self._get_domain_authority_score(url)
    
    # Add freshness scoring (max 0.2)
    if 'date' in result and result['date']:
        parsed_date = self._parse_date(result['date'])
        if parsed_date:
            days_old = (datetime.now() - parsed_date).days
            freshness_score = max(0, 0.2 * (1 - days_old/365))
            score += freshness_score
            result['freshness_score'] = freshness_score
    
    return min(1.0, score)
```

## 6. Academic Search Improvements 📚

### Semantic Scholar Integration
- Added Semantic Scholar API as a fallback for academic searches
- Implemented direct API querying with proper error handling
- Enhanced academic result quality with abstracts and citation data
- Added year, author, and publication information to search results

### Implementation Details
```python
# Try Semantic Scholar API as a fallback
try:
    encoded_query = quote(query)
    sem_scholar_url = f"https://api.semanticscholar.org/graph/v1/paper/search?query={encoded_query}&limit={num_results}"

    # Make the request
    response = self.session.get(sem_scholar_url, headers=headers, timeout=15)
    
    if response.status_code == 200:
        data = response.json()
        papers = data.get('data', [])
        
        # Process the papers into results
        results = []
        for paper in papers:
            # Process paper details...
            results.append({
                "title": title,
                "url": url,
                "snippet": snippet,
                "authors": authors_str,
                "year": year,
                "source": "semantic_scholar"
                # Additional fields...
            })
```

## Best Practices for Usage

### Security
- Always enable HTML sanitization when displaying results to users
- Consider custom `allowed_html_tags` lists for different security contexts
- Regularly update the sanitization logic as new threats emerge
- Filter results with `url_valid=False` before displaying to users

### Performance
- Configure cache size and TTL based on your application's memory constraints
- Adjust engine-specific rate limits based on provider documentation
- Consider using proxy rotation for high-volume applications
- Monitor rate limit errors and adjust limits accordingly

### Error Handling
- Monitor error logs for recurring issues with specific search engines
- Adjust retry and fallback strategies based on application needs
- Configure proxy rotation when working with strict rate-limited APIs
- Consider implementing custom fallback chains for specific use cases

### Quality Control
- Adjust quality scoring weightings based on your specific use case
- Customize domain authority lists for your application domain
- Implement additional content filtering for sensitive applications
- Consider caching filtered results to improve performance 