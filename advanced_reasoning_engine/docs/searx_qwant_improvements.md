# Cải tiến cho SearX và Qwant trong WebSearchAgent

## Vấn đề hiện tại

Qua quá trình test, chúng tôi đã phát hiện một số vấn đề với SearX và Qwant trong WebSearchAgent:

1. **SearX**:
   - <PERSON><PERSON><PERSON> hết các instance SearX đều trả về lỗi 429 (Too Many Requests) hoặc 403 (Forbidden)
   - Một số instance bị timeout khi kết nối
   - Các instance thường xuyên thay đổi hoặc ngừng hoạt động

2. **Qwant**:
   - <PERSON>h<PERSON><PERSON> trả về kết quả cho truy vấn tiếng Việt
   - Hoạt động không ổn định với một số truy vấn

## Cải tiến đã thực hiện

### 1. <PERSON><PERSON>i tiến cho SearX

1. **Thêm nhiều instance SearX hơn**:
   - Thêm 5 instance mới: search.mdosch.de, search.ononoki.org, searx.tuxcloud.net, search.rhscz.eu, search.neet.works
   - Sắp xếp lại thứ tự ưu tiên các instance dựa trên độ ổn định

2. **Cải thiện cơ chế xoay vòng instance**:
   - Xáo trộn ngẫu nhiên danh sách instance để tránh tập trung vào một instance
   - Thêm delay giữa các request để tránh bị chặn

3. **Cải thiện headers HTTP**:
   - Thêm nhiều headers giống trình duyệt thật hơn
   - Thêm Accept-Language, Referer, và các headers khác để giảm khả năng bị phát hiện là bot

4. **Tăng timeout và thêm proxy**:
   - Tăng timeout từ 10 giây lên 20 giây
   - Thêm cơ chế sử dụng proxy (cần cấu hình thêm)

5. **Cải thiện xử lý lỗi**:
   - Thêm delay sau khi gặp lỗi để tránh bị chặn
   - Ghi log chi tiết hơn về lỗi

### 2. Cải tiến cho Qwant

1. **Cải thiện headers HTTP**:
   - Thêm nhiều headers giống trình duyệt thật hơn
   - Thêm Accept-Language, Referer, và các headers khác

2. **Thêm cookies**:
   - Thêm cookies để giả lập người dùng đã đăng nhập
   - Thêm qwant-session, qwant-locale, qwant-consent

3. **Cải thiện cơ chế retry**:
   - Thêm cơ chế retry khi gặp lỗi
   - Tăng timeout từ 10 giây lên 20 giây

4. **Cải thiện tham số tìm kiếm**:
   - Thêm tham số locale để hỗ trợ tốt hơn cho ngôn ngữ khác nhau
   - Yêu cầu nhiều kết quả hơn để đảm bảo đủ số lượng sau khi lọc

## Kết quả test

1. **Truy vấn tiếng Anh**:
   - Hoạt động tốt với DuckDuckGo
   - SearX và Qwant vẫn gặp một số vấn đề nhưng đã cải thiện

2. **Truy vấn tiếng Việt**:
   - Vẫn gặp nhiều vấn đề với cả SearX và Qwant
   - Hầu hết các instance SearX trả về lỗi 429 hoặc 403
   - Qwant không trả về kết quả cho truy vấn tiếng Việt

## Đề xuất cải tiến trong tương lai

1. **Sử dụng proxy**:
   - Sử dụng proxy từ các quốc gia khác nhau để tránh bị chặn
   - Xoay vòng proxy để tránh bị phát hiện

2. **Cải thiện caching**:
   - Lưu cache kết quả tìm kiếm để giảm số lượng request
   - Sử dụng cache thông minh với thời gian hết hạn phù hợp

3. **Thêm các instance SearX mới**:
   - Thường xuyên cập nhật danh sách instance SearX
   - Tự động kiểm tra và đánh giá độ ổn định của các instance

4. **Cải thiện xử lý ngôn ngữ**:
   - Tự động dịch truy vấn tiếng Việt sang tiếng Anh khi cần thiết
   - Sử dụng các tham số ngôn ngữ phù hợp cho từng công cụ tìm kiếm

5. **Cải thiện cơ chế fallback**:
   - Thêm các công cụ tìm kiếm dự phòng
   - Cải thiện logic chuyển đổi giữa các công cụ tìm kiếm
