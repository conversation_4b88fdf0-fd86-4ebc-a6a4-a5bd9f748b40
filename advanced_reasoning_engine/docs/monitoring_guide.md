# Hướng dẫn Monitoring và Alerting cho Deep Research Core

Tài liệu này hướng dẫn cách sử dụng các tính năng monitoring và alerting mới được thêm vào Deep Research Core để theo dõi hi<PERSON> su<PERSON>, ph<PERSON><PERSON> hiện vấn đề và cải thiện khả năng gỡ lỗi của hệ thống.

## Mụ<PERSON> lục

1. [Structured Logging](#1-structured-logging)
2. [Performance Metrics](#2-performance-metrics)
3. [Distributed Tracing](#3-distributed-tracing)
4. [Alerting System](#4-alerting-system)
5. [Tích hợp với các hệ thống bên ngoài](#5-tích-hợp-với-các-hệ-thống-bên-ngoài)
6. [V<PERSON> dụ thực tế](#6-ví-dụ-thực-tế)

## 1. Structured Logging

Structured Logging giúp tạo ra các log có cấ<PERSON> tr<PERSON>, dễ dàng tìm kiếm và phân tích.

### Cách sử dụng

```python
from deep_research_core.utils import get_logger

# Tạo logger
logger = get_logger(
    "my_module",
    level="INFO",
    json_format=True,
    log_file="logs/my_module.log"
)

# Log thông tin cơ bản
logger.info("Processing query", query="What is RAG?")

# Log với context
logger.with_context(user_id="123", session_id="abc").info("User action")

# Log lỗi với exception
try:
    # Mã có thể gây lỗi
    result = 1 / 0
except Exception as e:
    logger.exception("Error occurred", operation="division")

# Đo thời gian thực hiện
with logger.timed(message_prefix="Database query"):
    # Mã cần đo thời gian
    time.sleep(1)
```

### Lợi ích

- **Cấu trúc nhất quán**: Tất cả log đều có cùng định dạng, dễ dàng phân tích
- **Metadata phong phú**: Thêm thông tin context vào log
- **Hỗ trợ JSON**: Dễ dàng tích hợp với các công cụ phân tích log như ELK Stack
- **Đo thời gian**: Tích hợp sẵn tính năng đo thời gian thực hiện

## 2. Performance Metrics

Performance Metrics giúp theo dõi hiệu suất của hệ thống qua các chỉ số như latency, throughput và memory usage.

### Cách sử dụng

```python
from deep_research_core.utils import (
    measure_latency, measure_block_latency,
    start_metrics_collection, stop_metrics_collection,
    get_metrics_snapshot
)

# Bắt đầu thu thập metrics
start_metrics_collection()

# Đo latency của hàm
@measure_latency("search_documents")
def search_documents(query):
    # Mã tìm kiếm tài liệu
    time.sleep(0.5)
    return ["doc1", "doc2"]

# Đo latency của một đoạn mã
def process_query(query):
    with measure_block_latency("vector_search"):
        # Mã tìm kiếm vector
        time.sleep(0.3)
    
    with measure_block_latency("generate_response"):
        # Mã tạo phản hồi
        time.sleep(0.7)

# Lấy snapshot của tất cả metrics
metrics = get_metrics_snapshot()
print(json.dumps(metrics, indent=2))

# Dừng thu thập metrics khi không cần nữa
stop_metrics_collection()
```

### Các loại metrics

1. **Latency Metrics**: Đo thời gian thực hiện các hoạt động
   - min_ms, max_ms, mean_ms, median_ms, p95_ms, p99_ms

2. **Throughput Metrics**: Đo số lượng hoạt động trên đơn vị thời gian
   - count, throughput_per_second

3. **Memory Metrics**: Đo lượng bộ nhớ sử dụng
   - process_rss_mb, process_vms_mb, system_used_percent, gpu_used_mb

## 3. Distributed Tracing

Distributed Tracing giúp theo dõi luồng xử lý qua các thành phần khác nhau của hệ thống.

### Cách sử dụng

```python
from deep_research_core.utils import (
    configure_tracing, trace_function, span,
    start_trace, start_span, end_span
)

# Cấu hình tracing
configure_tracing(service_name="deep-research-core")

# Tạo trace cho một request
with start_trace("process_query"):
    # Tạo span cho các bước xử lý
    with span("parse_query"):
        # Mã phân tích truy vấn
        pass
    
    with span("search_documents"):
        # Mã tìm kiếm tài liệu
        pass
    
    with span("generate_response"):
        # Mã tạo phản hồi
        pass

# Sử dụng decorator để trace hàm
@trace_function(name="index_document")
def index_document(doc):
    # Mã lập chỉ mục tài liệu
    pass

# Tạo span thủ công
def complex_operation():
    span_obj = start_span("complex_operation")
    try:
        # Mã phức tạp
        pass
    finally:
        end_span(span_obj)
```

### Lợi ích

- **Theo dõi luồng xử lý**: Hiểu rõ luồng xử lý qua các thành phần
- **Phát hiện bottleneck**: Xác định các bước xử lý chậm
- **Phân tích phụ thuộc**: Hiểu rõ mối quan hệ giữa các thành phần
- **Tích hợp OpenTelemetry**: Tương thích với các công cụ tracing phổ biến

## 4. Alerting System

Alerting System giúp phát hiện và thông báo về các vấn đề hiệu suất.

### Cách sử dụng

```python
from deep_research_core.utils import (
    AlertSeverity, add_threshold_alert,
    add_percentage_change_alert, start_alerting, stop_alerting,
    add_email_notifier, add_slack_notifier
)

# Thêm notifier
add_email_notifier(
    smtp_server="smtp.gmail.com",
    smtp_port=587,
    username="<EMAIL>",
    password="your-password",
    sender="<EMAIL>",
    recipients=["<EMAIL>"]
)

add_slack_notifier(
    webhook_url="https://hooks.slack.com/services/XXX/YYY/ZZZ",
    channel="#alerts"
)

# Thêm alert dựa trên ngưỡng
add_threshold_alert(
    name="high_search_latency",
    description="Search latency is too high",
    metric_path="latency.search_documents.p95_ms",
    threshold=1000,  # 1000ms = 1s
    comparison=">",
    severity=AlertSeverity.WARNING
)

# Thêm alert dựa trên thay đổi phần trăm
add_percentage_change_alert(
    name="throughput_drop",
    description="Search throughput has dropped significantly",
    metric_path="throughput.search_documents.throughput_per_second",
    percentage_change=50,
    direction="decrease",
    severity=AlertSeverity.ERROR
)

# Bắt đầu hệ thống alert
start_alerting()

# Dừng hệ thống alert khi không cần nữa
stop_alerting()
```

### Các loại alert

1. **Threshold Alert**: Kích hoạt khi một metric vượt quá ngưỡng
2. **Percentage Change Alert**: Kích hoạt khi một metric thay đổi quá nhiều
3. **Composite Alert**: Kết hợp nhiều alert với các toán tử logic

### Các kênh thông báo

1. **Console Notifier**: Ghi log ra console
2. **Email Notifier**: Gửi email thông báo
3. **Slack Notifier**: Gửi thông báo đến Slack

## 5. Tích hợp với các hệ thống bên ngoài

### Tích hợp với ELK Stack

```python
# Cấu hình structured logging để ghi log dạng JSON
logger = get_logger(
    "my_module",
    level="INFO",
    json_format=True,
    log_file="/var/log/deep_research_core/app.log"
)

# Cấu hình Filebeat để thu thập log và gửi đến Elasticsearch
# Xem filebeat.yml trong thư mục docs/examples
```

### Tích hợp với Prometheus

```python
# Sử dụng Prometheus Python Client để export metrics
from prometheus_client import start_http_server, Gauge, Counter

# Tạo custom metric trong performance_metrics.py
def export_metrics_to_prometheus(metrics):
    # Chuyển đổi metrics từ Deep Research Core sang Prometheus format
    # Xem ví dụ trong docs/examples/prometheus_exporter.py
```

### Tích hợp với Jaeger/Zipkin

```python
# Cấu hình OpenTelemetry để gửi trace đến Jaeger
configure_tracing(
    service_name="deep-research-core",
    use_opentelemetry=True,
    otlp_endpoint="http://jaeger:4317"
)
```

## 6. Ví dụ thực tế

Xem file `examples/monitoring_example.py` để thấy cách sử dụng tất cả các tính năng monitoring và alerting trong một ứng dụng thực tế.

### Chạy ví dụ

```bash
# Cài đặt các dependencies
pip install -e ".[monitoring]"

# Chạy ví dụ
python examples/monitoring_example.py --queries 20 --interval 0.5
```

### Kết quả

Sau khi chạy ví dụ, bạn sẽ thấy:

1. **Structured logs** trong file `logs/monitoring_example.log`
2. **Performance metrics** được in ra console sau mỗi lần chạy
3. **Trace spans** được ghi lại cho mỗi hoạt động
4. **Alerts** được kích hoạt khi các điều kiện được đáp ứng

## Kết luận

Các tính năng monitoring và alerting mới trong Deep Research Core cung cấp một bộ công cụ mạnh mẽ để theo dõi, phân tích và cải thiện hiệu suất của hệ thống. Bằng cách kết hợp structured logging, performance metrics, distributed tracing và alerting, bạn có thể:

1. **Hiểu rõ hơn** về cách hệ thống hoạt động
2. **Phát hiện sớm** các vấn đề hiệu suất
3. **Phân tích chi tiết** các bottleneck
4. **Nhận thông báo** khi có vấn đề xảy ra
5. **Tích hợp dễ dàng** với các công cụ monitoring phổ biến

Hãy tích hợp các tính năng này vào ứng dụng của bạn để có một hệ thống mạnh mẽ và đáng tin cậy hơn.
