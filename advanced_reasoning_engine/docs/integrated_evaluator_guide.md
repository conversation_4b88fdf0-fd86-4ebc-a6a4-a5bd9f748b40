# Hướng dẫn sử dụng Integrated Evaluator

## Giới thiệu

`IntegratedEvaluator` là một công cụ tích hợp các công cụ đánh giá khác nhau trong hệ thống để cung cấp một giao diện thống nhất cho việc đánh giá chất lượng phản hồi. Công cụ này tự động chọn công cụ đánh giá phù hợp dựa trên ngôn ngữ và loại nhiệm vụ.

## Cài đặt

Để sử dụng `IntegratedEvaluator`, bạn cần cài đặt các thư viện phụ thuộc sau:

```bash
pip install underthesea pyvi torch transformers sentence-transformers rouge numpy
```

Nếu bạn muốn sử dụng các tính năng đặc thù cho tiếng Việt, bạn cần cài đặt thêm:

```bash
pip install langdetect googletrans==4.0.0-rc1
```

## Khởi tạo

```python
from deep_research_core.evaluation.integrated_evaluator import IntegratedEvaluator

# Khởi tạo với tất cả các công cụ đánh giá
evaluator = IntegratedEvaluator(
    use_vietnamese_evaluator=True,
    use_rl_evaluator=True,
    use_reasoning_evaluator=True,
    use_factuality_evaluator=True,
    vietnamese_embedding_model="phobert",
    verbose=True
)

# Khởi tạo với một số công cụ đánh giá
evaluator = IntegratedEvaluator(
    use_vietnamese_evaluator=True,
    use_rl_evaluator=True,
    use_reasoning_evaluator=False,
    use_factuality_evaluator=False,
    vietnamese_embedding_model="phobert",
    verbose=False
)
```

## Các công cụ đánh giá hỗ trợ

`IntegratedEvaluator` hỗ trợ các công cụ đánh giá sau:

### Vietnamese RL Evaluator
- Đánh giá chất lượng phản hồi tiếng Việt từ các mô hình RL-tuning
- Hỗ trợ các metrics đặc thù cho tiếng Việt
- Tích hợp với các mô hình embedding tiếng Việt

### RL Evaluator
- Đánh giá chất lượng phản hồi từ các mô hình RL-tuning
- Hỗ trợ các metrics đánh giá chất lượng phản hồi, hiệu suất, và tính nhất quán

### Reasoning Evaluator
- Đánh giá chất lượng lập luận
- Hỗ trợ các metrics đánh giá tính mạch lạc, tính liên quan, độ sâu, và tính logic

### Factuality Evaluator
- Đánh giá tính chính xác của thông tin
- Hỗ trợ các metrics đánh giá tính chính xác, tính đầy đủ, tính nhất quán, và tính liên quan

## Các phương thức chính

### Đánh giá một phản hồi

```python
metrics = evaluator.evaluate(
    response="Phản hồi cần đánh giá",
    reference_response="Phản hồi tham chiếu (nếu có)",
    task_description="Mô tả nhiệm vụ (nếu có)",
    expected_keywords=["từ khóa 1", "từ khóa 2"],
    previous_responses=["Phản hồi trước đó 1", "Phản hồi trước đó 2"],
    task_type="rl",  # Có thể là: "rl", "reasoning", "factuality"
    language="vi",   # Có thể là: "vi", "en", "auto"
    context="Ngữ cảnh của phản hồi (nếu có)",
    facts=["Sự kiện 1", "Sự kiện 2"]  # Cho factuality_evaluator
)
```

### Đánh giá nhiều phản hồi

```python
batch_results = evaluator.evaluate_batch(
    responses=["Phản hồi 1", "Phản hồi 2"],
    reference_responses=["Tham chiếu 1", "Tham chiếu 2"],
    task_descriptions=["Nhiệm vụ 1", "Nhiệm vụ 2"],
    expected_keywords_list=[["từ khóa 1", "từ khóa 2"], ["từ khóa 3", "từ khóa 4"]],
    previous_responses_list=[["Phản hồi trước đó 1"], ["Phản hồi trước đó 2"]],
    task_types=["rl", "factuality"],
    languages=["vi", "en"],
    contexts=["Ngữ cảnh 1", "Ngữ cảnh 2"],
    facts_list=[["Sự kiện 1", "Sự kiện 2"], ["Sự kiện 3", "Sự kiện 4"]]
)
```

### Lấy danh sách các công cụ đánh giá có sẵn

```python
available_evaluators = evaluator.get_available_evaluators()
print(available_evaluators)  # Ví dụ: ['vietnamese', 'rl', 'reasoning', 'factuality']
```

### Lấy công cụ đánh giá theo tên

```python
vietnamese_evaluator = evaluator.get_evaluator("vietnamese")
rl_evaluator = evaluator.get_evaluator("rl")
```

## Các metrics đánh giá

### Vietnamese RL Evaluator

#### Metrics cơ bản
- `diacritic_consistency`: Đánh giá tính nhất quán của dấu thanh
- `dialect_consistency`: Đánh giá tính nhất quán của phương ngữ
- `reasoning_quality`: Đánh giá chất lượng lập luận
- `lexical_diversity`: Đánh giá tính đa dạng từ vựng
- `grammatical_correctness`: Đánh giá tính chính xác ngữ pháp

#### Metrics đặc thù cho RL
- `rl_response_quality`: Đánh giá chất lượng phản hồi RL
- `rl_performance`: Đánh giá hiệu suất tổng thể của mô hình RL
- `rl_task_completion`: Đánh giá mức độ hoàn thành nhiệm vụ
- `rl_consistency`: Đánh giá tính nhất quán giữa các phản hồi

#### Metrics đặc thù cho tiếng Việt
- `vietnamese_compound_word_usage`: Đánh giá sử dụng từ ghép tiếng Việt
- `vietnamese_idiom_usage`: Đánh giá sử dụng thành ngữ, tục ngữ tiếng Việt
- `vietnamese_cultural_context`: Đánh giá ngữ cảnh văn hóa tiếng Việt
- `vietnamese_tone_consistency`: Đánh giá tính nhất quán về giọng điệu
- `vietnamese_regional_term_usage`: Đánh giá sử dụng từ ngữ vùng miền
- `vietnamese_formal_language`: Đánh giá mức độ trang trọng của ngôn ngữ
- `vietnamese_technical_term_usage`: Đánh giá sử dụng thuật ngữ chuyên ngành
- `rl_vietnamese_alignment`: Đánh giá mức độ phù hợp với tiếng Việt

#### Điểm nhóm
- `basic_metrics_score`: Điểm tổng hợp từ các metrics cơ bản
- `rl_metrics_score`: Điểm tổng hợp từ các metrics đặc thù cho RL
- `vietnamese_specific_score`: Điểm tổng hợp từ các metrics đặc thù cho tiếng Việt

#### Điểm tổng hợp
- `overall_score`: Điểm tổng hợp từ tất cả các metrics

### RL Evaluator

- `rl_response_quality`: Đánh giá chất lượng phản hồi RL
- `rl_performance`: Đánh giá hiệu suất tổng thể của mô hình RL
- `rl_task_completion`: Đánh giá mức độ hoàn thành nhiệm vụ
- `rl_consistency`: Đánh giá tính nhất quán giữa các phản hồi
- `overall_score`: Điểm tổng hợp từ tất cả các metrics

### Reasoning Evaluator

- `reasoning_coherence`: Đánh giá tính mạch lạc của lập luận
- `reasoning_relevance`: Đánh giá tính liên quan của lập luận
- `reasoning_depth`: Đánh giá độ sâu của lập luận
- `reasoning_logic`: Đánh giá tính logic của lập luận
- `reasoning_creativity`: Đánh giá tính sáng tạo của lập luận
- `overall_score`: Điểm tổng hợp từ tất cả các metrics

### Factuality Evaluator

- `factual_accuracy`: Đánh giá tính chính xác của thông tin
- `factual_completeness`: Đánh giá tính đầy đủ của thông tin
- `factual_consistency`: Đánh giá tính nhất quán của thông tin
- `factual_relevance`: Đánh giá tính liên quan của thông tin
- `overall_score`: Điểm tổng hợp từ tất cả các metrics

## Ví dụ sử dụng

### Đánh giá phản hồi tiếng Việt

```python
from deep_research_core.evaluation.integrated_evaluator import IntegratedEvaluator

# Khởi tạo evaluator
evaluator = IntegratedEvaluator(
    use_vietnamese_evaluator=True,
    vietnamese_embedding_model="phobert"
)

# Phản hồi cần đánh giá
response = """
Học tăng cường (Reinforcement Learning) là một phương pháp học máy trong đó một tác tử (agent) 
học cách đưa ra quyết định bằng cách tương tác với môi trường. Tác tử thực hiện hành động và 
nhận phần thưởng hoặc hình phạt, từ đó học cách tối ưu hóa chiến lược để đạt được phần thưởng 
cao nhất trong dài hạn.
"""

# Mô tả nhiệm vụ
task_description = """
Giải thích về học tăng cường (Reinforcement Learning) và ứng dụng của nó trong việc tinh chỉnh 
mô hình ngôn ngữ lớn. Hãy đề cập đến các thuật toán phổ biến như PPO và ứng dụng tại Việt Nam.
"""

# Từ khóa mong đợi
expected_keywords = [
    "học tăng cường", "reinforcement learning", "tác tử", "agent", "môi trường", 
    "phần thưởng", "chính sách", "policy", "PPO", "RLHF", "mô hình ngôn ngữ", 
    "tinh chỉnh", "Việt Nam", "ứng dụng"
]

# Đánh giá phản hồi
metrics = evaluator.evaluate(
    response=response,
    task_description=task_description,
    expected_keywords=expected_keywords,
    language="vi",
    task_type="rl"
)

# In kết quả
for metric, value in metrics.items():
    print(f"{metric}: {value}")
```

### Đánh giá phản hồi tiếng Anh về tính chính xác

```python
# Phản hồi cần đánh giá
response = """
The capital of France is Paris. It is located on the Seine River and has a population of about 
2.2 million people within the city limits. Paris is known for landmarks such as the Eiffel Tower, 
the Louvre Museum, and Notre-Dame Cathedral.
"""

# Các sự kiện để đánh giá tính chính xác
facts = [
    "Paris is the capital of France",
    "Paris is located on the Seine River",
    "The Eiffel Tower is in Paris",
    "The Louvre Museum is in Paris",
    "Notre-Dame Cathedral is in Paris"
]

# Đánh giá phản hồi
metrics = evaluator.evaluate(
    response=response,
    facts=facts,
    language="en",
    task_type="factuality"
)

# In kết quả
for metric, value in metrics.items():
    print(f"{metric}: {value}")
```

### Đánh giá nhiều phản hồi

```python
# Danh sách các phản hồi cần đánh giá
responses = [
    "Phản hồi tiếng Việt",
    "English response"
]

# Đánh giá các phản hồi
batch_results = evaluator.evaluate_batch(
    responses=responses,
    languages=["vi", "en"],
    task_types=["rl", "factuality"]
)

# In kết quả
for i, result in enumerate(batch_results):
    print(f"\nResult {i+1}:")
    for metric, value in result.items():
        print(f"  {metric}: {value}")
```

## Lưu ý

- Nếu không chỉ định `language`, `IntegratedEvaluator` sẽ tự động phát hiện ngôn ngữ của phản hồi.
- Nếu không chỉ định `task_type`, `IntegratedEvaluator` sẽ tự động chọn loại nhiệm vụ dựa trên các tham số được cung cấp.
- Để đánh giá tính chính xác, bạn cần cung cấp danh sách các sự kiện thông qua tham số `facts`.
- Để đánh giá tính nhất quán, bạn cần cung cấp danh sách các phản hồi trước đó thông qua tham số `previous_responses`.
- Để đánh giá mức độ hoàn thành nhiệm vụ, bạn cần cung cấp mô tả nhiệm vụ thông qua tham số `task_description` và danh sách các từ khóa mong đợi thông qua tham số `expected_keywords`.

## Kết luận

`IntegratedEvaluator` là một công cụ mạnh mẽ để đánh giá chất lượng phản hồi từ các mô hình ngôn ngữ lớn. Với khả năng tích hợp nhiều công cụ đánh giá khác nhau, `IntegratedEvaluator` giúp bạn dễ dàng đánh giá phản hồi dựa trên ngôn ngữ và loại nhiệm vụ.
