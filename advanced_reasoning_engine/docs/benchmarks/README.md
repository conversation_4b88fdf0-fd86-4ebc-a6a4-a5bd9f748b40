# Benchmark Results and Comparisons

This document provides detailed information about the benchmarks used in Deep Research Core, how to run evaluations, and how to interpret the results.

## Available Benchmarks

Deep Research Core includes the following benchmark categories:

### Reasoning Benchmarks

| Benchmark Name | Description | Number of Tasks | Primary Metrics |
|----------------|-------------|-----------------|-----------------|
| `reasoning_basic` | Basic reasoning tasks including arithmetic, logic, and common-sense reasoning | 200 | accuracy, latency |
| `tot_benchmark` | Tasks specifically designed to evaluate Tree of Thoughts reasoning | 150 | accuracy, reasoning_steps, latency |
| `cot_benchmark` | Tasks for evaluating Chain of Thought reasoning | 120 | accuracy, reasoning_quality, latency |
| `react_benchmark` | Tasks for evaluating ReAct reasoning with tool use | 100 | task_completion, tool_usage, latency |
| `hyde_benchmark` | Tasks for evaluating HyDE retrieval quality | 200 | retrieval_precision, retrieval_recall, query_efficiency |
| `vietnamese_reasoning` | Vietnamese language reasoning tasks | 180 | accuracy, fluency, latency |

### Agent Benchmarks

| Benchmark Name | Description | Number of Tasks | Primary Metrics |
|----------------|-------------|-----------------|-----------------|
| `agent_environment` | Tasks for testing agent-environment interaction | 80 | task_completion, efficiency, adaptability |
| `tool_usage` | Tasks for evaluating effective tool usage | 120 | tool_selection, tool_usage_success, efficiency |
| `trajectory_benchmark` | Tasks for evaluating trajectory scaling and reasoning | 100 | success_rate, reasoning_quality, adaptability |
| `multi_agent` | Tasks for evaluating multi-agent collaboration | 50 | task_completion, communication_efficiency, role_specialization |

## Running Benchmark Evaluations

The Deep Research Core provides a comprehensive `ReasoningModelEvaluator` class for running benchmark evaluations. Here's how to use it:

```python
from deep_research_core.evaluation.reasoning_model_evaluator import ReasoningModelEvaluator
from deep_research_core.models import GPT4Model, DeepSeekModel, LocalLlama3Model

# Initialize evaluator with models
evaluator = ReasoningModelEvaluator(
    models={
        "gpt-4": GPT4Model(),
        "deepseek": DeepSeekModel(),
        "llama3-local": LocalLlama3Model()
    },
    benchmark_data_path="./benchmarks",
    results_dir="./evaluation_results",
    verbose=True
)

# Evaluate a single model on a benchmark
results = evaluator.evaluate_model(
    model_id="gpt-4",
    benchmark_name="reasoning_basic",
    metrics=["accuracy", "latency", "token_usage"]
)

# Print key metrics
print(f"Accuracy: {results['metrics']['accuracy']['mean']:.2f}")
print(f"Average latency: {results['metrics']['latency']['mean']:.2f}s")

# Compare multiple models
comparison = evaluator.compare_models(
    benchmark_name="reasoning_basic",
    model_ids=["gpt-4", "deepseek", "llama3-local"],
    metrics=["accuracy", "latency"]
)

# Export results to CSV
evaluator.export_results_to_csv(
    results={
        "gpt-4": results,
        # Add other model results here
    },
    output_path="./benchmark_results.csv"
)
```

### Parallel Evaluation

For faster evaluation, you can enable parallel processing:

```python
evaluator = ReasoningModelEvaluator(
    # ... other parameters ...
    parallel=True,
    max_workers=4
)
```

## Benchmark Results

Below are the latest benchmark results for various models across key tasks:

### Reasoning Basic Benchmark

| Model | Accuracy | Latency (s) | Token Efficiency |
|-------|----------|-------------|------------------|
| GPT-4 | 0.92 | 2.34 | 0.87 |
| DeepSeek | 0.89 | 1.76 | 0.81 |
| Claude 3 | 0.91 | 2.12 | 0.85 |
| Llama 3 | 0.87 | 1.45 | 0.79 |
| Gemini | 0.88 | 1.98 | 0.83 |

### Vietnamese Reasoning Benchmark

| Model | Accuracy | Fluency | Latency (s) |
|-------|----------|---------|-------------|
| GPT-4 | 0.85 | 0.88 | 2.45 |
| DeepSeek | 0.79 | 0.82 | 1.89 |
| Claude 3 | 0.83 | 0.89 | 2.21 |
| Llama 3 | 0.76 | 0.80 | 1.56 |
| Gemini | 0.81 | 0.86 | 2.05 |

### Tree of Thoughts Benchmark

| Model | Accuracy | Reasoning Steps | Latency (s) |
|-------|----------|-----------------|-------------|
| GPT-4 | 0.89 | 6.8 | 5.23 |
| DeepSeek | 0.84 | 5.9 | 4.12 |
| Claude 3 | 0.88 | 7.2 | 5.47 |
| Llama 3 | 0.81 | 5.5 | 3.76 |
| Gemini | 0.83 | 6.3 | 4.89 |

## Interpreting Results

### Key Metrics

- **Accuracy**: Percentage of correct answers or successful task completions
- **Latency**: Average time in seconds to complete a task
- **Token Efficiency**: Ratio of helpful output tokens to total tokens generated
- **Reasoning Quality**: Score from 0-1 assessing the quality of reasoning steps
- **Task Completion**: Success rate for completing multi-step tasks
- **Tool Usage**: Effectiveness score for using the right tools at the right time

### Visualizing Results

The evaluator includes methods for generating visualizations:

```python
# Create comparison chart
evaluator.visualize_comparison(
    comparison_id="reasoning_basic_comparison",
    metrics=["accuracy", "latency"],
    output_path="./comparison_chart.png",
    chart_type="bar"
)

# Generate comprehensive HTML report
report_path = evaluator.generate_report(
    result_id="gpt4_reasoning_basic",
    output_format="html",
    output_path="./evaluation_report.html"
)
```

## Creating Custom Benchmarks

You can create custom benchmarks by following these steps:

1. Define benchmark tasks in a JSON file:

```json
{
  "name": "my_custom_benchmark",
  "description": "Custom benchmark for specific reasoning tasks",
  "metrics": ["accuracy", "reasoning_quality", "latency"],
  "tasks": [
    {
      "id": "task_001",
      "prompt": "What is the next number in the sequence: 2, 4, 8, 16, ...?",
      "expected": "32",
      "difficulty": "easy",
      "category": "pattern_recognition",
      "system_message": "You are a helpful assistant that solves mathematical sequences."
    },
    // Add more tasks here
  ],
  "metadata": {
    "version": "1.0",
    "created_by": "Your Name",
    "date_created": "2024-01-15"
  }
}
```

2. Register the benchmark with the evaluator:

```python
from deep_research_core.evaluation.reasoning_model_evaluator import ReasoningBenchmark

# Load from file
benchmark = ReasoningBenchmark.from_file("path/to/my_benchmark.json")

# Or create programmatically
benchmark = ReasoningBenchmark(
    name="my_custom_benchmark",
    description="Custom benchmark for specific reasoning tasks",
    tasks=[
        {
            "id": "task_001",
            "prompt": "What is the next number in the sequence: 2, 4, 8, 16, ...?",
            "expected": "32",
            "difficulty": "easy",
            "category": "pattern_recognition"
        },
        # Add more tasks
    ],
    metrics=["accuracy", "reasoning_quality", "latency"]
)

# Register with evaluator
evaluator.register_benchmark("my_benchmark", benchmark)
```

3. Run evaluations with your custom benchmark:

```python
results = evaluator.evaluate_model(
    model_id="my_model",
    benchmark_name="my_benchmark",
    metrics=["accuracy", "reasoning_quality", "latency"]
)
```

## Best Practices

1. **Use consistent evaluation settings**: Keep temperature, max_tokens, and other generation parameters consistent across evaluations.

2. **Run multiple trials**: For more reliable results, run each benchmark multiple times and average the results.

3. **Filter tasks when needed**: Use the `tasks_filter` parameter to evaluate on specific subsets of tasks:

```python
results = evaluator.evaluate_model(
    model_id="my_model",
    benchmark_name="reasoning_basic",
    tasks_filter={"difficulty": "hard", "category": "logic"},
    filter_mode="and"  # Both criteria must match
)
```

4. **Save results for historical comparison**: Store benchmark results to track performance over time.

5. **Update benchmarks regularly**: As models improve, update benchmarks to include more challenging tasks.

## Roadmap for Future Benchmarks

The following benchmark categories are planned for future releases:

1. **Multi-agent collaboration benchmarks**: Complex tasks requiring coordination between multiple agents
2. **Interactive learning benchmarks**: Tasks that measure how well models adapt from feedback
3. **Advanced Vietnamese language understanding**: Tasks focusing on Vietnamese cultural context
4. **Domain-specific reasoning**: Tasks for legal, medical, and scientific domains

## Contributing Benchmarks

We welcome community contributions to our benchmark collection. To contribute:

1. Create benchmark tasks following the format described above
2. Test the benchmark with at least two different models
3. Document the benchmark purpose, structure, and evaluation criteria
4. Submit a pull request with your benchmark files

For more information, contact the project maintainers or open an issue on GitHub. 