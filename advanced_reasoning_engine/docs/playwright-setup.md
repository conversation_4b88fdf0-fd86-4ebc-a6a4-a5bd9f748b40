# Cài đặt Playwright cho WebSearchAgent

Tài liệu này hướng dẫn cách cài đặt Playwright, một công cụ cần thiết để WebSearchAgent có thể thực hiện tìm kiếm phức tạp với phương thức Crawlee.

## Tại sao cần Playwright?

WebSearchAgent sử dụng hai phương thức tìm kiếm chính:
1. **API Search**: Sử dụng các API tìm kiếm như DuckDuckGo, SearX, Qwant để tìm kiếm nhanh chóng
2. **Crawlee Search**: Sử dụng Crawlee để tìm kiếm sâu hơn, phù hợp với các truy vấn phức tạp

Phương thức Crawlee yêu cầu Playwright để điều khiển trình duyệt web và thu thập thông tin. Nếu không cài đặt Playwright, WebSearchAgent sẽ tự động chuyển sang phương thức API Search, nhưng điều này có thể làm giảm chất lượng kết quả cho các truy vấn phức tạp.

## Cài đặt tự động

WebSearchAgent đã được cập nhật để tự động kiểm tra và cài đặt Playwright khi cần thiết. Tuy nhiên, quá trình cài đặt tự động có thể gặp vấn đề trong một số môi trường. Nếu bạn gặp lỗi liên quan đến Playwright, hãy thử cài đặt thủ công theo hướng dẫn bên dưới.

## Cài đặt thủ công

### 1. Cài đặt Playwright cho Python

```bash
pip install playwright
```

### 2. Cài đặt các trình duyệt cần thiết

```bash
python -m playwright install chromium
```

Lệnh này sẽ cài đặt trình duyệt Chromium, đủ để WebSearchAgent hoạt động. Nếu bạn muốn cài đặt tất cả các trình duyệt, hãy sử dụng:

```bash
python -m playwright install
```

### 3. Cài đặt Playwright cho Node.js (cần thiết cho Crawlee)

```bash
npm install playwright
npx playwright install chromium
```

## Kiểm tra cài đặt

Để kiểm tra xem Playwright đã được cài đặt đúng cách chưa, bạn có thể chạy đoạn mã Python sau:

```python
from playwright.sync_api import sync_playwright

with sync_playwright() as p:
    browser = p.chromium.launch()
    page = browser.new_page()
    page.goto('https://example.com')
    print(page.title())
    browser.close()
```

Nếu mã trên chạy mà không có lỗi và hiển thị tiêu đề trang web, Playwright đã được cài đặt thành công.

## Xử lý sự cố

### Lỗi "Executable doesn't exist at /path/to/browser"

Lỗi này xảy ra khi Playwright không thể tìm thấy trình duyệt đã cài đặt. Hãy thử cài đặt lại trình duyệt:

```bash
python -m playwright install chromium
```

### Lỗi "No usable sandbox"

Trên một số hệ thống Linux, bạn có thể gặp lỗi liên quan đến sandbox. Hãy thử chạy với tùy chọn `--no-sandbox`:

```python
browser = p.chromium.launch(args=['--no-sandbox'])
```

### Lỗi "Protocol error"

Lỗi này thường xảy ra khi có vấn đề với kết nối giữa Python và trình duyệt. Hãy thử khởi động lại máy tính và cài đặt lại Playwright.

## Tài liệu tham khảo

- [Tài liệu chính thức của Playwright](https://playwright.dev/python/docs/intro)
- [Tài liệu của Crawlee](https://crawlee.dev/)
