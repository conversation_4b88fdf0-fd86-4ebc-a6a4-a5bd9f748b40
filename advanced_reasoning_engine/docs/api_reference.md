# API Reference

## Deep Research Core API Reference

This document provides a comprehensive reference for the Deep Research Core API, including all endpoints, request and response models, and implementation details.

## Table of Contents

- [API Endpoints](#api-endpoints)
  - [General Endpoints](#general-endpoints)
  - [AI Generation Endpoints](#ai-generation-endpoints)
  - [Configuration Endpoints](#configuration-endpoints)
  - [Visualization Endpoints](#visualization-endpoints)
- [Request and Response Models](#request-and-response-models)
- [Implementation Components](#implementation-components)
  - [SQLiteVectorRAG](#sqlitevectorrag)
  - [Enhanced RAG Implementations](#enhanced-rag-implementations)
  - [Retrieval Components](#retrieval-components)
  - [Evaluation Components](#evaluation-components)

---

## API Endpoints

### General Endpoints

#### Root Endpoint

```
GET /
```

Returns basic information about the API.

**Response:**
```json
{
  "message": "Deep Research Core API",
  "version": "0.1.0",
  "documentation": "/docs"
}
```

#### Health Check

```
GET /api/health
```

Checks the health status of the API.

**Response:**
```json
{
  "status": "OK",
  "timestamp": "2023-07-25T15:30:45.123456",
  "version": "0.1.0"
}
```

### AI Generation Endpoints

#### Text Generation

```
POST /api/generate
```

Generates text based on a prompt using a specified AI model and provider.

**Request Body:**
```json
{
  "prompt": "Explain the concept of quantization in AI models.",
  "model": "moonshotai/moonlight-16b-a3b-instruct:free",
  "provider": "openrouter",
  "temperature": 0.7,
  "max_tokens": 1000,
  "system_prompt": "You are a helpful AI assistant.",
  "stream": false
}
```

**Response:**
```json
{
  "text": "Quantization is a technique used in AI to reduce the precision of numerical values...",
  "model": "moonshotai/moonlight-16b-a3b-instruct:free",
  "provider": "openrouter",
  "generation_time": 1.25
}
```

#### Retrieval-Augmented Generation (RAG)

```
POST /api/rag
```

Processes a query using Retrieval-Augmented Generation.

**Request Body:**
```json
{
  "query": "What is quantization in AI models?",
  "documents": [
    {
      "content": "Quantization is a technique to reduce the precision of weights in AI models to reduce size and increase inference speed.",
      "source": "AI Model Optimization Guide",
      "title": "Quantization Techniques"
    }
  ],
  "model": "moonshotai/moonlight-16b-a3b-instruct:free",
  "provider": "openrouter",
  "temperature": 0.7,
  "max_tokens": 1000,
  "top_k": 3,
  "system_prompt": "You are a helpful AI assistant."
}
```

**Response:**
```json
{
  "query": "What is quantization in AI models?",
  "answer": "Quantization is a technique used to reduce the precision of weights in AI models...",
  "documents": [
    {
      "content": "Quantization is a technique to reduce the precision of weights in AI models to reduce size and increase inference speed.",
      "source": "AI Model Optimization Guide",
      "title": "Quantization Techniques",
      "date": "2023-05-15",
      "id": 1
    }
  ],
  "model": "moonshotai/moonlight-16b-a3b-instruct:free",
  "provider": "openrouter",
  "generation_time": 1.5
}
```

#### Chain of Thought (CoT)

```
POST /api/cot
```

Processes a prompt using Chain of Thought reasoning.

**Request Body:**
```json
{
  "prompt": "Explain how to solve this math problem: If a train travels at 60 mph for 3 hours, how far does it go?",
  "model": "moonshotai/moonlight-16b-a3b-instruct:free",
  "provider": "openrouter",
  "temperature": 0.7,
  "max_tokens": 1000,
  "system_prompt": "You are a helpful AI assistant."
}
```

**Response:**
```json
{
  "prompt": "Explain how to solve this math problem: If a train travels at 60 mph for 3 hours, how far does it go?",
  "answer": "To solve this problem, I need to find the distance traveled by the train.\n\nGiven:\n- Speed of the train = 60 mph\n- Time of travel = 3 hours\n\nUsing the formula: Distance = Speed × Time\nDistance = 60 mph × 3 hours\nDistance = 180 miles\n\nTherefore, the train travels 180 miles.",
  "model": "moonshotai/moonlight-16b-a3b-instruct:free",
  "provider": "openrouter",
  "generation_time": 1.3
}
```

#### Chain of Thought with RAG (CoT-RAG)

```
POST /api/cot-rag
```

Processes a query using Chain of Thought with Retrieval-Augmented Generation.

**Request Body:**
```json
{
  "query": "What are the benefits of quantization in AI models?",
  "documents": [
    {
      "content": "Quantization is a technique to reduce the precision of weights in AI models to reduce size and increase inference speed.",
      "source": "AI Model Optimization Guide",
      "title": "Quantization Techniques"
    }
  ],
  "model": "moonshotai/moonlight-16b-a3b-instruct:free",
  "provider": "openrouter",
  "temperature": 0.7,
  "max_tokens": 1000,
  "top_k": 3,
  "system_prompt": "You are a helpful AI assistant.",
  "language": "en"
}
```

**Response:**
```json
{
  "query": "What are the benefits of quantization in AI models?",
  "answer": "Based on the provided documents, I can identify several benefits of quantization in AI models:\n\n1. Reduced model size: Quantization reduces the precision of weights, which directly translates to smaller model file sizes.\n\n2. Increased inference speed: Lower precision calculations can be performed faster on most hardware.\n\n3. Lower memory requirements: Quantized models require less RAM during execution.\n\n4. Improved deployment options: Smaller models can be deployed on edge devices with limited resources.\n\nThese benefits make quantization an essential technique for optimizing AI models for production environments.",
  "documents": [
    {
      "content": "Quantization is a technique to reduce the precision of weights in AI models to reduce size and increase inference speed.",
      "source": "AI Model Optimization Guide",
      "title": "Quantization Techniques",
      "date": null,
      "id": null
    }
  ],
  "model": "moonshotai/moonlight-16b-a3b-instruct:free",
  "provider": "openrouter",
  "generation_time": 1.8,
  "language": "en"
}
```

#### Unified AI Endpoint

```
POST /api/unified
```

Provides a unified interface for all AI processing modes.

**Request Body:**
```json
{
  "query": "What are the benefits of quantization in AI models?",
  "mode": "cotrag",
  "documents": [
    {
      "content": "Quantization is a technique to reduce the precision of weights in AI models to reduce size and increase inference speed.",
      "source": "AI Model Optimization Guide",
      "title": "Quantization Techniques"
    }
  ],
  "model": "moonshotai/moonlight-16b-a3b-instruct:free",
  "provider": "openrouter",
  "temperature": 0.7,
  "max_tokens": 1000,
  "top_k": 3,
  "system_prompt": "You are a helpful AI assistant.",
  "language": "en"
}
```

**Response:**
```json
{
  "query": "What are the benefits of quantization in AI models?",
  "answer": "Based on the provided documents, I can identify several benefits of quantization in AI models...",
  "mode": "cotrag",
  "documents": [
    {
      "content": "Quantization is a technique to reduce the precision of weights in AI models to reduce size and increase inference speed.",
      "source": "AI Model Optimization Guide",
      "title": "Quantization Techniques"
    }
  ],
  "model": "moonshotai/moonlight-16b-a3b-instruct:free",
  "provider": "openrouter",
  "generation_time": 1.8,
  "language": "en"
}
```

### Configuration Endpoints

#### Get Providers

```
GET /api/providers
```

Returns a list of available AI providers.

**Response:**
```json
{
  "providers": ["openrouter", "anthropic", "openai"],
  "default": "openrouter"
}
```

#### Set API Key

```
POST /api/set-api-key
```

Sets the API key for a specific provider.

**Request Parameters:**
- `provider`: The provider to set the API key for
- `api_key`: The API key

**Response:**
```json
{
  "status": "success",
  "message": "API key set for provider: openai"
}
```

### Visualization Endpoints

#### Generate Visualization

```
POST /visualization/generate
```

Generates visualizations for ToT-RAG results.

**Request Body:**
```json
{
  "result": {
    "query": "What are the benefits of quantization in AI models?",
    "best_paths": [["path1", "step1"], ["path1", "step2"]],
    "retrieved_documents": [
      {
        "content": "Quantization is a technique to reduce the precision of weights in AI models to reduce size and increase inference speed.",
        "source": "AI Model Optimization Guide",
        "title": "Quantization Techniques"
      }
    ],
    "metadata": {
      "model": "gpt-4o",
      "provider": "openai"
    }
  },
  "filename_prefix": "quantization_benefits",
  "max_label_length": 50,
  "max_docs": 10
}
```

**Response:**
```json
{
  "html_url": "/static/visualizations/quantization_benefits_dashboard.html",
  "image_urls": [
    "/static/visualizations/quantization_benefits_tree.png",
    "/static/visualizations/quantization_benefits_graph.png"
  ],
  "message": "Visualizations generated successfully"
}
```

#### Get Tree Data

```
GET /visualization/tree-data?result_id=12345
```

Returns tree visualization data for a specific ToT-RAG result.

**Query Parameters:**
- `result_id`: ID of the ToT-RAG result to visualize

**Response:**
```json
{
  "name": "Query Root",
  "children": [
    {
      "name": "Path 1",
      "children": [
        {"name": "Step 1"},
        {"name": "Step 2"}
      ]
    },
    {
      "name": "Path 2",
      "children": [
        {"name": "Step 1"},
        {"name": "Step 2"}
      ]
    }
  ]
}
```

## Request and Response Models

### Document Model

```python
class Document(BaseModel):
    content: str
    source: str
    title: Optional[str] = None
    date: Optional[str] = None
    id: Optional[int] = None
```

### Generate Request Model

```python
class GenerateRequest(BaseModel):
    prompt: str
    model: str = "moonshotai/moonlight-16b-a3b-instruct:free"
    provider: str = "openrouter"
    temperature: float = 0.7
    max_tokens: int = 1000
    system_prompt: Optional[str] = None
    stream: bool = False
```

### RAG Request Model

```python
class RAGRequest(BaseModel):
    query: str
    documents: List[Document]
    model: str = "moonshotai/moonlight-16b-a3b-instruct:free"
    provider: str = "openrouter"
    temperature: float = 0.7
    max_tokens: int = 1000
    top_k: int = 3
    system_prompt: Optional[str] = None
```

### CoT Request Model

```python
class CoTRequest(BaseModel):
    prompt: str
    model: str = "moonshotai/moonlight-16b-a3b-instruct:free"
    provider: str = "openrouter"
    temperature: float = 0.7
    max_tokens: int = 1000
    system_prompt: Optional[str] = None
```

### CoT-RAG Request Model

```python
class CoTRAGRequest(BaseModel):
    query: str
    documents: List[Document]
    model: str = "moonshotai/moonlight-16b-a3b-instruct:free"
    provider: str = "openrouter"
    temperature: float = 0.7
    max_tokens: int = 1000
    top_k: int = 3
    system_prompt: Optional[str] = None
    language: str = "en"
```

### Unified AI Request Model

```python
class UnifiedAIRequest(BaseModel):
    query: str
    mode: str = "cotrag"  # One of: 'generate', 'rag', 'cot', or 'cotrag'
    documents: Optional[List[Document]] = None
    model: str = "moonshotai/moonlight-16b-a3b-instruct:free"
    provider: str = "openrouter"
    temperature: float = 0.7
    max_tokens: int = 1000
    top_k: int = 3
    system_prompt: Optional[str] = None
    language: str = "en"
```

## Implementation Components

## SQLiteVectorRAG

The base class for SQLite-based Vector Retrieval-Augmented Generation.

### Constructor

```python
SQLiteVectorRAG(
    db_path: str,
    embedding_model: str = "all-MiniLM-L6-v2",
    provider: str = "openrouter",
    model: Optional[str] = None,
    temperature: float = 0.7,
    max_tokens: int = 1000,
    top_k: int = 5,
    chunk_size: int = 1000,
    chunk_overlap: int = 200,
    api_key: Optional[str] = None
)
```

#### Parameters:

- `db_path`: Path to the SQLite database
- `embedding_model`: Name of the sentence-transformers model to use
- `provider`: Model provider (openai, anthropic, openrouter)
- `model`: Model name
- `temperature`: Temperature for text generation
- `max_tokens`: Maximum number of tokens to generate
- `top_k`: Number of documents to retrieve
- `chunk_size`: Size of document chunks
- `chunk_overlap`: Overlap between document chunks
- `api_key`: API key for the provider

### Methods

#### add_documents

Add documents to the database.

```python
add_documents(
    documents: List[Dict[str, Any]],
    update_existing: bool = True,
    auto_chunk: bool = True
) -> List[int]
```

#### Parameters:

- `documents`: List of document dictionaries with 'content', 'source', etc.
- `update_existing`: Whether to update existing documents with the same hash
- `auto_chunk`: Whether to automatically chunk long documents

#### Returns:

- List of IDs of the inserted or updated documents

#### search

Search for documents similar to the query.

```python
search(
    query: str,
    top_k: Optional[int] = None,
    hybrid_weight: float = 0.7,
    **kwargs
) -> List[Dict[str, Any]]
```

#### Parameters:

- `query`: The query string
- `top_k`: Number of results to return (defaults to self.top_k)
- `hybrid_weight`: Weight for vector search vs keyword search (1.0 = vector only, 0.0 = keyword only)
- `**kwargs`: Additional arguments

#### Returns:

- List of dictionaries containing the retrieved documents and their similarity scores

#### process

Process a query and generate a response.

```python
process(
    query: str,
    top_k: Optional[int] = None,
    hybrid_weight: float = 0.7,
    custom_system_prompt: Optional[str] = None,
    custom_user_prompt: Optional[str] = None,
    callback: Optional[Callable[[str], None]] = None,
    save_evaluation: bool = False,
    **kwargs
) -> Dict[str, Any]
```

#### Parameters:

- `query`: The query to process
- `top_k`: Number of results to return (defaults to self.top_k)
- `hybrid_weight`: Weight for vector search vs keyword search (1.0 = vector only, 0.0 = keyword only)
- `custom_system_prompt`: Custom system prompt to use
- `custom_user_prompt`: Custom user prompt template to use
- `callback`: Optional callback function for streaming
- `save_evaluation`: Whether to save evaluation data
- `**kwargs`: Additional arguments

#### Returns:

- Dictionary containing the answer, retrieved documents, and other information

#### clear

Clear the database.

```python
clear() -> None
```

#### close

Close the RAG instance.

```python
close() -> None
```

## Enhanced RAG Implementations

### SQLiteVectorRAGWithANN

Extends SQLiteVectorRAG with Approximate Nearest Neighbor search capabilities using FAISS.

### Constructor

```python
SQLiteVectorRAGWithANN(
    db_path: str,
    embedding_model: str = "all-MiniLM-L6-v2",
    provider: str = "openrouter",
    model: Optional[str] = None,
    temperature: float = 0.7,
    max_tokens: int = 1000,
    top_k: int = 5,
    chunk_size: int = 1000,
    chunk_overlap: int = 200,
    api_key: Optional[str] = None,
    ann_index_type: str = "IVFFlat",
    ann_metric_type: str = "cosine",
    ann_nlist: int = 100,
    ann_cache_dir: Optional[str] = None,
    use_ann: bool = True
)
```

#### Additional Parameters:

- `ann_index_type`: Type of FAISS index to use ("Flat", "IVFFlat", "IVFPQ", "HNSW")
- `ann_metric_type`: Distance metric to use ("cosine", "l2", "ip")
- `ann_nlist`: Number of clusters for IVF-based indexes
- `ann_cache_dir`: Directory to cache the FAISS index
- `use_ann`: Whether to use ANN search (if False, falls back to brute-force)

### Methods

Inherits all methods from SQLiteVectorRAG with optimized vector search using FAISS.

### SQLiteVectorRAGWithRTree

Extends SQLiteVectorRAG with R-tree indexing for efficient spatial search of vector embeddings.

### Constructor

```python
SQLiteVectorRAGWithRTree(
    db_path: str,
    embedding_model: str = "all-MiniLM-L6-v2",
    provider: str = "openrouter",
    model: Optional[str] = None,
    temperature: float = 0.7,
    max_tokens: int = 1000,
    top_k: int = 5,
    chunk_size: int = 1000,
    chunk_overlap: int = 200,
    api_key: Optional[str] = None,
    pca_dimensions: int = 20,
    use_rtree: bool = True
)
```

#### Additional Parameters:

- `pca_dimensions`: Number of dimensions to use for PCA reduction (R-tree works best with fewer dimensions)
- `use_rtree`: Whether to use R-tree indexing (if False, falls back to brute-force)

### Methods

Inherits all methods from SQLiteVectorRAG with optimized vector search using SQLite's R-tree extension.

### SQLiteVectorRAGWithAdaptiveWeighting

Extends SQLiteVectorRAG with adaptive weighting for hybrid search.

### Constructor

```python
SQLiteVectorRAGWithAdaptiveWeighting(
    db_path: str,
    embedding_model: str = "all-MiniLM-L6-v2",
    provider: str = "openrouter",
    model: Optional[str] = None,
    temperature: float = 0.7,
    max_tokens: int = 1000,
    top_k: int = 5,
    chunk_size: int = 1000,
    chunk_overlap: int = 200,
    api_key: Optional[str] = None,
    weighting_strategy: str = "auto",
    min_weight: float = 0.3,
    max_weight: float = 0.9,
    default_weight: float = 0.7,
    use_adaptive_weighting: bool = True
)
```

#### Additional Parameters:

- `weighting_strategy`: Strategy for adaptive weighting ("auto", "query_length", "query_specificity", "query_type")
- `min_weight`: Minimum weight for vector search
- `max_weight`: Maximum weight for vector search
- `default_weight`: Default weight for vector search
- `use_adaptive_weighting`: Whether to use adaptive weighting (if False, uses default_weight)

### Methods

#### search

Search for documents similar to the query using adaptive hybrid search.

```python
search(
    query: str,
    top_k: Optional[int] = None,
    hybrid_weight: Optional[float] = None,
    analyze_query: bool = False,
    **kwargs
) -> Union[List[Dict[str, Any]], Tuple[List[Dict[str, Any]], Dict[str, Any]]]
```

#### Additional Parameters:

- `hybrid_weight`: Weight for vector search vs keyword search (if None, determined adaptively)
- `analyze_query`: Whether to return query analysis along with results

#### Returns:

- If analyze_query is False: List of dictionaries containing the retrieved documents and their similarity scores
- If analyze_query is True: Tuple of (results, query_analysis)

### SQLiteVectorRAGWithSemanticChunking

Extends SQLiteVectorRAG with semantic chunking capabilities.

### Constructor

```python
SQLiteVectorRAGWithSemanticChunking(
    db_path: str,
    embedding_model: str = "all-MiniLM-L6-v2",
    provider: str = "openrouter",
    model: Optional[str] = None,
    temperature: float = 0.7,
    max_tokens: int = 1000,
    top_k: int = 5,
    chunk_size: int = 1000,
    chunk_overlap: int = 200,
    api_key: Optional[str] = None,
    chunking_strategy: str = "hybrid",
    respect_sections: bool = True,
    respect_sentences: bool = True,
    use_semantic_chunking: bool = True
)
```

#### Additional Parameters:

- `chunking_strategy`: Chunking strategy ("paragraph", "section", "hybrid", "sentence")
- `respect_sections`: Whether to respect section boundaries
- `respect_sentences`: Whether to respect sentence boundaries
- `use_semantic_chunking`: Whether to use semantic chunking (if False, falls back to character-based chunking)

### Methods

#### analyze_document

Analyze a document and return information about its structure.

```python
analyze_document(content: str) -> Dict[str, Any]
```

#### Parameters:

- `content`: Document content

#### Returns:

- Dictionary with document analysis

### SQLiteVectorRAGWithHierarchicalDocuments

Extends SQLiteVectorRAG with hierarchical document representation.

### Constructor

```python
SQLiteVectorRAGWithHierarchicalDocuments(
    db_path: str,
    embedding_model: str = "all-MiniLM-L6-v2",
    provider: str = "openrouter",
    model: Optional[str] = None,
    temperature: float = 0.7,
    max_tokens: int = 1000,
    top_k: int = 5,
    chunk_size: int = 1000,
    chunk_overlap: int = 200,
    api_key: Optional[str] = None,
    hierarchical_levels: List[str] = ["document", "section", "paragraph"],
    use_hierarchical_documents: bool = True
)
```

#### Additional Parameters:

- `hierarchical_levels`: Levels of granularity to include ("document", "section", "paragraph", "sentence")
- `use_hierarchical_documents`: Whether to use hierarchical documents (if False, falls back to character-based chunking)

### Methods

#### search

Search for documents similar to the query with support for hierarchical filtering.

```python
search(
    query: str,
    top_k: Optional[int] = None,
    hybrid_weight: float = 0.7,
    chunk_types: Optional[List[str]] = None,
    **kwargs
) -> List[Dict[str, Any]]
```

#### Additional Parameters:

- `chunk_types`: Types of chunks to include in search ("document", "section", "paragraph", "sentence")

#### get_related_chunks

Get related chunks for a given chunk.

```python
get_related_chunks(
    chunk_id: int,
    relation_type: str = "children"
) -> List[Dict[str, Any]]
```

#### Parameters:

- `chunk_id`: ID of the chunk to get related chunks for
- `relation_type`: Type of relation ("children", "parent", "siblings")

#### Returns:

- List of related chunks

#### format_context_hierarchical

Format retrieved documents into context with hierarchical information.

```python
format_context_hierarchical(
    documents: List[Dict[str, Any]],
    include_related: bool = True,
    relation_type: str = "parent"
) -> str
```

#### Parameters:

- `documents`: List of retrieved documents
- `include_related`: Whether to include related chunks
- `relation_type`: Type of relation for related chunks ("parent", "children", "siblings")

#### Returns:

- Formatted context string

### SQLiteVectorRAGVietnamese

Extends SQLiteVectorRAG with specialized Vietnamese support.

### Constructor

```python
SQLiteVectorRAGVietnamese(
    db_path: str,
    embedding_model: str = "phobert",
    provider: str = "openrouter",
    model: Optional[str] = None,
    temperature: float = 0.7,
    max_tokens: int = 1000,
    top_k: int = 5,
    chunk_size: int = 1000,
    chunk_overlap: int = 200,
    api_key: Optional[str] = None,
    device: Optional[str] = None,
    cache_dir: Optional[str] = None
)
```

#### Additional Parameters:

- `embedding_model`: Name of the Vietnamese embedding model to use
- `device`: Device to use for inference ("cpu", "cuda", "mps")
- `cache_dir`: Directory to cache models

### Methods

Inherits all methods from SQLiteVectorRAG with specialized Vietnamese embedding models and language-specific processing.

#### get_system_prompt

Get the system prompt for Vietnamese RAG.

```python
get_system_prompt() -> str
```

#### Returns:

- System prompt string in Vietnamese

#### get_user_prompt_template

Get the user prompt template for Vietnamese RAG.

```python
get_user_prompt_template() -> str
```

#### Returns:

- User prompt template string in Vietnamese

## Retrieval Components

### FAISSVectorSearch

FAISS-based Approximate Nearest Neighbor (ANN) search implementation.

### Constructor

```python
FAISSVectorSearch(
    dimension: int,
    index_type: str = "IVFFlat",
    metric_type: str = "cosine",
    nlist: int = 100,
    cache_dir: Optional[str] = None
)
```

#### Parameters:

- `dimension`: Dimension of the embedding vectors
- `index_type`: Type of FAISS index to use ("Flat", "IVFFlat", "IVFPQ", "HNSW")
- `metric_type`: Distance metric to use ("cosine", "l2", "ip")
- `nlist`: Number of clusters for IVF-based indexes
- `cache_dir`: Directory to cache the FAISS index

### Methods

#### add_documents

Add documents to the index.

```python
add_documents(doc_ids: List[int], embeddings: np.ndarray) -> None
```

#### Parameters:

- `doc_ids`: List of document IDs
- `embeddings`: Document embeddings as a numpy array

#### search

Search for similar documents.

```python
search(query_embedding: np.ndarray, top_k: int = 5) -> List[Tuple[int, float]]
```

#### Parameters:

- `query_embedding`: Query embedding
- `top_k`: Number of results to return

#### Returns:

- List of tuples (doc_id, similarity_score)

#### save

Save the index to disk.

```python
save(filepath: Optional[str] = None) -> None
```

#### Parameters:

- `filepath`: Path to save the index to (if None, use cache_dir)

#### load

Load the index from disk.

```python
load(filepath: Optional[str] = None) -> bool
```

#### Parameters:

- `filepath`: Path to load the index from (if None, use cache_dir)

#### Returns:

- True if loaded successfully, False otherwise

#### clear

Clear the index.

```python
clear() -> None
```

### AdaptiveWeighting

Adaptive weighting for hybrid search.

### Constructor

```python
AdaptiveWeighting(
    strategy: str = "auto",
    min_weight: float = 0.3,
    max_weight: float = 0.9,
    default_weight: float = 0.7
)
```

#### Parameters:

- `strategy`: Weighting strategy ("auto", "query_length", "query_specificity", "query_type")
- `min_weight`: Minimum weight for vector search
- `max_weight`: Maximum weight for vector search
- `default_weight`: Default weight for vector search

### Methods

#### get_weight

Get the optimal weight for a query.

```python
get_weight(query: str) -> float
```

#### Parameters:

- `query`: The query string

#### Returns:

- Weight for vector search (between min_weight and max_weight)

#### analyze_query

Analyze a query and return detailed information about its characteristics.

```python
analyze_query(query: str) -> Dict[str, Any]
```

#### Parameters:

- `query`: The query string

#### Returns:

- Dictionary with query analysis

### SemanticChunker

Semantic chunker for document processing.

### Constructor

```python
SemanticChunker(
    strategy: str = "paragraph",
    max_chunk_size: int = 1000,
    min_chunk_size: int = 100,
    overlap_size: int = 50,
    respect_sections: bool = True,
    respect_sentences: bool = True
)
```

#### Parameters:

- `strategy`: Chunking strategy ("paragraph", "section", "hybrid", "sentence")
- `max_chunk_size`: Maximum size of a chunk in characters
- `min_chunk_size`: Minimum size of a chunk in characters
- `overlap_size`: Size of overlap between chunks in characters
- `respect_sections`: Whether to respect section boundaries
- `respect_sentences`: Whether to respect sentence boundaries

### Methods

#### chunk_document

Chunk a document based on semantic structure.

```python
chunk_document(document: Dict[str, Any]) -> List[Dict[str, Any]]
```

#### Parameters:

- `document`: Document dictionary with 'content', 'source', etc.

#### Returns:

- List of document chunks

#### analyze_document

Analyze a document and return information about its structure.

```python
analyze_document(content: str) -> Dict[str, Any]
```

#### Parameters:

- `content`: Document content

#### Returns:

- Dictionary with document analysis

### HierarchicalDocument

Hierarchical document representation.

### Constructor

```python
HierarchicalDocument(
    content: str,
    source: str,
    title: Optional[str] = None,
    date: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None,
    doc_id: Optional[str] = None
)
```

#### Parameters:

- `content`: Document content
- `source`: Document source
- `title`: Document title
- `date`: Document date
- `metadata`: Additional metadata
- `doc_id`: Document ID (if None, a UUID will be generated)

### Methods

#### get_document_dict

Get the document as a dictionary.

```python
get_document_dict() -> Dict[str, Any]
```

#### Returns:

- Dictionary representation of the document

#### get_section_dicts

Get the sections as a list of dictionaries.

```python
get_section_dicts() -> List[Dict[str, Any]]
```

#### Returns:

- List of section dictionaries

#### get_paragraph_dicts

Get the paragraphs as a list of dictionaries.

```python
get_paragraph_dicts() -> List[Dict[str, Any]]
```

#### Returns:

- List of paragraph dictionaries

#### get_sentence_dicts

Get the sentences as a list of dictionaries.

```python
get_sentence_dicts() -> List[Dict[str, Any]]
```

#### Returns:

- List of sentence dictionaries

#### get_all_chunks

Get all chunks at all levels of granularity.

```python
get_all_chunks(include_document: bool = True) -> List[Dict[str, Any]]
```

#### Parameters:

- `include_document`: Whether to include the document-level chunk

#### Returns:

- List of all chunks

#### get_chunks_by_level

Get chunks at a specific level of granularity.

```python
get_chunks_by_level(level: str) -> List[Dict[str, Any]]
```

#### Parameters:

- `level`: Level of granularity ("document", "section", "paragraph", "sentence")

#### Returns:

- List of chunks at the specified level

#### get_document_structure

Get the document structure as a nested dictionary.

```python
get_document_structure() -> Dict[str, Any]
```

#### Returns:

- Nested dictionary representing the document structure

### VietnameseEmbeddings

Vietnamese-specific embeddings for Deep Research Core.

### Constructor

```python
VietnameseEmbeddings(
    model_name: str = "phobert",
    device: Optional[str] = None,
    cache_dir: Optional[str] = None
)
```

#### Parameters:

- `model_name`: Name of the embedding model to use
- `device`: Device to use for inference ("cpu", "cuda", "mps")
- `cache_dir`: Directory to cache models

### Methods

#### get_embeddings

Get embeddings for Vietnamese texts.

```python
get_embeddings(texts: List[str]) -> np.ndarray
```

#### Parameters:

- `texts`: List of Vietnamese texts

#### Returns:

- Numpy array of embeddings

#### get_embedding

Get embedding for a single Vietnamese text.

```python
get_embedding(text: str) -> np.ndarray
```

#### Parameters:

- `text`: Vietnamese text

#### Returns:

- Numpy array of embedding

#### get_embedding_dimension

Get the dimension of the embeddings.

```python
get_embedding_dimension() -> int
```

#### Returns:

- Dimension of the embeddings

#### calculate_similarity

Calculate similarity between two Vietnamese texts.

```python
calculate_similarity(
    text1: str,
    text2: str,
    method: str = "cosine"
) -> float
```

#### Parameters:

- `text1`: First Vietnamese text
- `text2`: Second Vietnamese text
- `method`: Similarity method ("cosine", "dot", "euclidean")

#### Returns:

- Similarity score

#### find_most_similar

Find the most similar Vietnamese texts to a query.

```python
find_most_similar(
    query: str,
    candidates: List[str],
    top_k: int = 5,
    method: str = "cosine"
) -> List[Tuple[int, float]]
```

#### Parameters:

- `query`: Query Vietnamese text
- `candidates`: List of candidate Vietnamese texts
- `top_k`: Number of results to return
- `method`: Similarity method ("cosine", "dot", "euclidean")

#### Returns:

- List of tuples (index, similarity_score)

## Evaluation Components

### DomainMetrics

Domain-specific evaluation metrics.

### Constructor

```python
DomainMetrics(domain: str = "general")
```

#### Parameters:

- `domain`: Domain to evaluate ("general", "medical", "legal", "technical", "educational")

### Methods

#### calculate_domain_relevance

Calculate domain relevance score.

```python
calculate_domain_relevance(response: Dict[str, Any]) -> float
```

#### Parameters:

- `response`: Response from the model

#### Returns:

- Domain relevance score (0.0 to 1.0)

#### calculate_citation_quality

Calculate citation quality score.

```python
calculate_citation_quality(response: Dict[str, Any]) -> float
```

#### Parameters:

- `response`: Response from the model

#### Returns:

- Citation quality score (0.0 to 1.0)

#### calculate_technical_accuracy

Calculate technical accuracy score.

```python
calculate_technical_accuracy(response: Dict[str, Any], expected_terms: List[str]) -> float
```

#### Parameters:

- `response`: Response from the model
- `expected_terms`: List of expected technical terms

#### Returns:

- Technical accuracy score (0.0 to 1.0)

#### calculate_explanation_depth

Calculate explanation depth score.

```python
calculate_explanation_depth(response: Dict[str, Any]) -> float
```

#### Parameters:

- `response`: Response from the model

#### Returns:

- Explanation depth score (0.0 to 1.0)

#### calculate_readability

Calculate readability score using Flesch Reading Ease.

```python
calculate_readability(response: Dict[str, Any]) -> float
```

#### Parameters:

- `response`: Response from the model

#### Returns:

- Readability score (0.0 to 1.0)

#### calculate_domain_metrics

Calculate all domain-specific metrics.

```python
calculate_domain_metrics(
    response: Dict[str, Any],
    expected_terms: Optional[List[str]] = None
) -> Dict[str, float]
```

#### Parameters:

- `response`: Response from the model
- `expected_terms`: List of expected technical terms

#### Returns:

- Dictionary containing all domain-specific metrics

### DomainEvaluator

Domain-specific evaluator for Deep Research Core.

### Constructor

```python
DomainEvaluator(
    provider: str,
    model: str,
    language: str = "en",
    documents: Optional[List[Dict[str, Any]]] = None,
    domain: str = "general",
    expected_terms: Optional[Dict[str, List[str]]] = None,
    cache_dir: Optional[str] = None
)
```

#### Parameters:

- `provider`: Model provider (openai, anthropic, openrouter)
- `model`: Model name
- `language`: Language for evaluation
- `documents`: List of documents to use for evaluation
- `domain`: Domain to evaluate ("general", "medical", "legal", "technical", "educational")
- `expected_terms`: Dictionary mapping query types to lists of expected technical terms
- `cache_dir`: Directory to cache evaluation results

### Methods

#### evaluate_domain_response

Evaluate a response using domain-specific metrics.

```python
evaluate_domain_response(
    response: Dict[str, Any],
    query_type: Optional[str] = None
) -> Dict[str, float]
```

#### Parameters:

- `response`: Response from the model
- `query_type`: Type of query (for expected terms)

#### Returns:

- Dictionary containing domain-specific evaluation metrics

#### evaluate_cotrag_with_domain

Evaluate CoTRAG on the evaluation dataset with domain-specific metrics.

```python
evaluate_cotrag_with_domain(
    question_type: Optional[str] = None,
    num_queries: int = -1,
    parallel: bool = False,
    max_workers: int = 4
) -> Dict[str, Any]
```

#### Parameters:

- `question_type`: Type of question to evaluate (if None, evaluate all)
- `num_queries`: Number of queries to evaluate (-1 for all)
- `parallel`: Whether to evaluate in parallel
- `max_workers`: Maximum number of workers for parallel evaluation

#### Returns:

- Dictionary containing evaluation results

#### evaluate_enhanced_cotrag_with_domain

Evaluate Enhanced CoTRAG on the evaluation dataset with domain-specific metrics.

```python
evaluate_enhanced_cotrag_with_domain(
    question_type: Optional[str] = None,
    num_queries: int = -1,
    parallel: bool = False,
    max_workers: int = 4
) -> Dict[str, Any]
```

#### Parameters:

- `question_type`: Type of question to evaluate (if None, evaluate all)
- `num_queries`: Number of queries to evaluate (-1 for all)
- `parallel`: Whether to evaluate in parallel
- `max_workers`: Maximum number of workers for parallel evaluation

#### Returns:

- Dictionary containing evaluation results

#### compare_methods_with_domain

Compare CoTRAG and Enhanced CoTRAG with domain-specific metrics.

```python
compare_methods_with_domain(
    question_type: Optional[str] = None,
    num_queries: int = -1
) -> Dict[str, Any]
```

#### Parameters:

- `question_type`: Type of question to evaluate (if None, evaluate all)
- `num_queries`: Number of queries to evaluate (-1 for all)

#### Returns:

- Dictionary containing comparison results
