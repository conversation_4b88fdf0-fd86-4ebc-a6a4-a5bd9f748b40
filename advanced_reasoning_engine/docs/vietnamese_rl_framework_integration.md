# Tích hợp tiếng Việt với các Framework RL-Tuning

Tài liệu này mô tả cách tích hợp hỗ trợ tiếng Việt với các framework RL-Tuning trong Deep Research Core, bao gồ<PERSON>, TinyZero, OpenR1, và Trlx.

## Tổng quan

Deep Research Core cung cấp lớp `VietnameseRLAdapter` để tích hợp hỗ trợ tiếng Việt với các framework RL-Tuning. Lớp này cung cấp các phương thức để xử lý dữ liệu tiếng Việt, tạo prompt tiế<PERSON>, và tăng cường phản hồi tiếng Việt.

## Cài đặt

Để sử dụng `VietnameseRLAdapter`, bạn cần cài đặt các thư viện sau:

```bash
pip install transformers
pip install langdetect
pip install googletrans==4.0.0-rc1
```

Nếu bạn muốn sử dụng các mô hình embedding ti<PERSON><PERSON>, bạn cần cài đặt thêm:

```bash
pip install sentencepiece
pip install torch
```

## Sử dụng với FrameworkFactory

Cách đơn giản nhất để sử dụng `VietnameseRLAdapter` là thông qua lớp `FrameworkFactory`:

```python
from deep_research_core.rl_tuning.frameworks import FrameworkFactory

# Tạo framework factory
factory = FrameworkFactory()

# Tạo framework
framework = factory.create_framework(
    framework_type="verl",
    framework_id="verl_ppo",
    agent_type="ppo",
    device="cuda",
    log_level="INFO"
)

# Tạo Vietnamese adapter
adapter = factory.create_vietnamese_adapter(
    framework_id="verl_ppo",
    config={
        "enable_vietnamese": True,
        "translate_prompts": True,
        "adapt_prompts": True,
        "vietnamese_ratio": 0.5,
        "bilingual_training": True,
        "dialect": "northern",
        "embedding_model": "phobert"
    },
    device="cuda",
    verbose=True
)

# Khởi tạo framework
factory.initialize_framework(
    framework_id="verl_ppo",
    model_name="gpt2",
    tokenizer_name="gpt2"
)

# Xử lý dữ liệu cho tiếng Việt
processed_data = adapter.process_batch(data)

# Huấn luyện framework
factory.train(
    framework_id="verl_ppo",
    train_data=processed_data,
    num_epochs=3,
    batch_size=8
)
```

Hoặc bạn có thể sử dụng phương thức `adapt_framework_for_vietnamese` để tự động tích hợp hỗ trợ tiếng Việt:

```python
# Tạo framework
framework = factory.create_framework(
    framework_type="tinyzero",
    framework_id="tinyzero_ppo",
    algorithm_type="ppo",
    device="cuda",
    log_level="INFO"
)

# Tích hợp hỗ trợ tiếng Việt
factory.adapt_framework_for_vietnamese(
    framework_id="tinyzero_ppo",
    config={
        "enable_vietnamese": True,
        "translate_prompts": True,
        "adapt_prompts": True,
        "vietnamese_ratio": 0.5,
        "bilingual_training": False,
        "dialect": "northern",
        "embedding_model": "phobert"
    },
    device="cuda",
    verbose=True
)

# Huấn luyện framework
factory.train(
    framework_id="tinyzero_ppo",
    train_data=data,
    num_epochs=3,
    batch_size=8
)
```

## Sử dụng trực tiếp VietnameseRLAdapter

Bạn cũng có thể sử dụng `VietnameseRLAdapter` trực tiếp:

```python
from deep_research_core.rl_tuning.frameworks import VietnameseRLAdapter

# Tạo adapter
adapter = VietnameseRLAdapter(
    framework_type="openr1",
    framework_instance=openr1_framework,
    config={
        "enable_vietnamese": True,
        "translate_prompts": True,
        "adapt_prompts": True,
        "vietnamese_ratio": 0.5,
        "bilingual_training": False,
        "dialect": "northern",
        "embedding_model": "phobert"
    },
    device="cuda",
    verbose=True
)

# Xử lý dữ liệu cho tiếng Việt
processed_data = adapter.process_batch(data)

# Tạo batch song ngữ
bilingual_data = adapter.create_bilingual_batch(processed_data)

# Tạo prompt tiếng Việt
vietnamese_prompt = adapter.create_vietnamese_prompt(
    prompt="Explain the process of photosynthesis in plants.",
    algorithm="ppo"
)

# Xử lý phản hồi tiếng Việt
enhanced_response = adapter.process_vietnamese_response(
    response="Quá trình quang hợp là quá trình thực vật sử dụng năng lượng ánh sáng để chuyển đổi carbon dioxide và nước thành glucose và oxygen."
)
```

## Cấu hình

`VietnameseRLAdapter` hỗ trợ các tùy chọn cấu hình sau:

- `enable_vietnamese`: Bật/tắt hỗ trợ tiếng Việt (mặc định: True)
- `translate_prompts`: Dịch prompt sang tiếng Việt (mặc định: True)
- `adapt_prompts`: Điều chỉnh prompt cho tiếng Việt (mặc định: True)
- `vietnamese_ratio`: Tỷ lệ dữ liệu tiếng Việt trong tập dữ liệu (mặc định: 0.3)
- `bilingual_training`: Bật/tắt huấn luyện song ngữ (mặc định: False)
- `dialect`: Phương ngữ tiếng Việt (mặc định: "northern")
- `embedding_model`: Mô hình embedding tiếng Việt (mặc định: "phobert")
- `fallback_to_english`: Sử dụng tiếng Anh nếu không có hỗ trợ tiếng Việt (mặc định: True)

## Mô hình Embedding Tiếng Việt

`VietnameseRLAdapter` hỗ trợ các mô hình embedding tiếng Việt sau:

- `phobert`: PhoBERT base (vinai/phobert-base)
- `phobert-large`: PhoBERT large (vinai/phobert-large)
- `viebert`: VieBERT (FPTAI/viebert-base-cased)
- `xlm-roberta-vi`: XLM-RoBERTa (xlm-roberta-base)
- `multilingual-e5`: Multilingual E5 (intfloat/multilingual-e5-base)
- `multilingual-e5-large`: Multilingual E5 Large (intfloat/multilingual-e5-large)
- `vietnamese-sbert`: Vietnamese SBERT (keepitreal/vietnamese-sbert)
- `bkai-foundation-vi`: BKAI Foundation Vietnamese (bkai-foundation-models/vietnamese-bi-encoder)
- `envibert`: EnViBERT (nguyenvulebinh/envibert)
- `bartpho`: BARTpho (vinai/bartpho-syllable)
- `velectra`: VElectra (vinai/velectra-base-discriminator-cased)

## Ví dụ

Xem ví dụ đầy đủ trong tệp `examples/vietnamese_rl_framework_integration.py`.

## Lưu ý

- Không phải tất cả các framework đều hỗ trợ tất cả các tính năng của `VietnameseRLAdapter`.
- Một số framework có thể yêu cầu cấu hình đặc biệt để hoạt động với tiếng Việt.
- Hiệu suất của các mô hình embedding tiếng Việt có thể khác nhau tùy thuộc vào tác vụ.
- Việc dịch tự động có thể không hoàn hảo, đặc biệt là với các thuật ngữ chuyên ngành.
