# Tính năng nâng cao cho các kỹ thuật suy luận

Tài liệu này mô tả các tính năng nâng cao đã được thêm vào các kỹ thuật suy luận trong hệ thống Deep Research Core.

## Tính năng chung

Các tính năng sau đây đã được thêm vào cả CoTRAG và ToTRAG:

### 1. Điều chỉnh tham số thích ứng (Adaptive Parameter Adjustment)

H<PERSON> thống có thể tự động điều chỉnh các tham số dựa trên độ phức tạp của truy vấn:

- **Nhiệt độ (Temperature)**: Giảm nhiệt độ cho các truy vấn phức tạp để tăng tính nhất quán
- **Số token tối đa (Max To<PERSON>)**: Tăng số token cho các truy vấn phức tạp để có đủ không gian cho suy luận
- **<PERSON><PERSON> nhánh tối đa (Max Branches)**: <PERSON><PERSON><PERSON> số nhánh cho các truy vấn phức tạp (chỉ áp dụng cho ToT)
- **Độ sâu tối đa (Max Depth)**: Tăng độ sâu cho các truy vấn phức tạp (chỉ áp dụng cho ToT)

### 2. Bộ nhớ đệm có chọn lọc (Selective Caching)

Hệ thống lưu trữ kết quả của các truy vấn trước đó để tránh tính toán lại:

- **Bộ nhớ đệm dựa trên truy vấn**: Lưu trữ kết quả dựa trên truy vấn đầu vào
- **Kích thước bộ nhớ đệm có giới hạn**: Giới hạn số lượng kết quả được lưu trữ
- **Tùy chọn làm mới (Force Refresh)**: Cho phép bỏ qua bộ nhớ đệm khi cần

### 3. Đánh giá kết quả (Result Evaluation)

Hệ thống có thể tự động đánh giá chất lượng của suy luận:

- **Đánh giá độ rõ ràng của các bước**: Đánh giá mức độ rõ ràng của từng bước suy luận
- **Đánh giá tính logic**: Đánh giá tính logic và nhất quán của suy luận
- **Đánh giá việc sử dụng bằng chứng**: Đánh giá mức độ sử dụng thông tin từ ngữ cảnh
- **Đánh giá độ mạnh của kết luận**: Đánh giá mức độ thuyết phục của kết luận
- **Đánh giá chất lượng tổng thể**: Đánh giá chất lượng tổng thể của suy luận

### 4. Đo lường hiệu suất (Performance Metrics)

Hệ thống theo dõi các chỉ số hiệu suất:

- **Độ trễ (Latency)**: Thời gian xử lý truy vấn
- **Số bước suy luận**: Số bước được sử dụng trong quá trình suy luận
- **Số nhánh được khám phá**: Số nhánh được khám phá (chỉ áp dụng cho ToT)
- **Độ sâu tối đa đạt được**: Độ sâu tối đa đạt được trong cây suy luận (chỉ áp dụng cho ToT)

## Tính năng riêng cho CoTRAG

### 1. Cải thiện định dạng ngữ cảnh

- **Định dạng nguồn rõ ràng**: Hiển thị rõ ràng nguồn của mỗi tài liệu
- **Định dạng metadata**: Hiển thị metadata của tài liệu một cách có cấu trúc

### 2. Xử lý streaming cải tiến

- **Hỗ trợ nhiều nhà cung cấp**: Xử lý streaming từ nhiều nhà cung cấp khác nhau (OpenAI, Anthropic, OpenRouter)
- **Callback linh hoạt**: Cho phép callback tùy chỉnh để xử lý từng phần của phản hồi

## Tính năng riêng cho ToTRAG

### 1. Điều chỉnh tham số ToT thích ứng

- **Điều chỉnh số nhánh**: Tự động điều chỉnh số nhánh dựa trên độ phức tạp của truy vấn
- **Điều chỉnh độ sâu**: Tự động điều chỉnh độ sâu dựa trên độ phức tạp của truy vấn

### 2. Đánh giá đường dẫn suy luận tốt nhất

- **Đánh giá đường dẫn tốt nhất**: Đánh giá chất lượng của đường dẫn suy luận tốt nhất
- **Trích xuất các bước suy luận**: Trích xuất các bước suy luận từ đường dẫn tốt nhất

## Tích hợp RAG, ToT và CoT (RAGToTCoT)

Lớp `RAGTOTCOTReasoner` kết hợp cả ba kỹ thuật để tạo ra một hệ thống suy luận mạnh mẽ:

### 1. Quy trình suy luận

1. **Truy xuất thông tin (RAG)**: Truy xuất thông tin liên quan từ cơ sở dữ liệu
2. **Khám phá nhiều hướng suy luận (ToT)**: Sử dụng ToT để khám phá nhiều hướng suy luận
3. **Cải thiện suy luận (CoT)**: Sử dụng CoT để cải thiện suy luận từ ToT

### 2. Tính năng so sánh phương pháp

- **So sánh các phương pháp khác nhau**: So sánh hiệu suất của các phương pháp khác nhau (CoT, RAG+CoT, ToT+CoT, RAG+ToT+CoT)
- **Phân tích hiệu suất**: Phân tích độ dài câu trả lời, thời gian xử lý và chất lượng của mỗi phương pháp

### 3. Tùy chọn linh hoạt

- **Bật/tắt RAG**: Cho phép bật/tắt việc sử dụng RAG
- **Bật/tắt ToT**: Cho phép bật/tắt việc sử dụng ToT
- **Bật/tắt CoT**: Cho phép bật/tắt việc sử dụng CoT
- **Điều chỉnh số lượng kết quả RAG**: Cho phép điều chỉnh số lượng kết quả RAG được truy xuất

## Cách sử dụng

### CoTRAG với tính năng nâng cao

```python
from src.deep_research_core.reasoning.cot_rag import CoTRAG

# Khởi tạo CoTRAG với tính năng nâng cao
cot_rag = CoTRAG(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    adaptive=True,  # Bật điều chỉnh tham số thích ứng
    use_cache=True,  # Bật bộ nhớ đệm
    evaluate_results=True  # Bật đánh giá kết quả
)

# Xử lý truy vấn
result = cot_rag.process(
    query="Giải thích cách hoạt động của mạng neural",
    top_k=5,  # Số lượng tài liệu để truy xuất
    force_refresh=False  # Sử dụng bộ nhớ đệm nếu có
)

# Truy cập kết quả
answer = result["answer"]
reasoning = result["reasoning"]
documents = result["documents"]
latency = result["latency"]

# Truy cập đánh giá (nếu được bật)
if "evaluation" in result:
    overall_quality = result["evaluation"]["metrics"]["overall_quality"]
    step_clarity = result["evaluation"]["metrics"]["step_clarity"]
    logical_flow = result["evaluation"]["metrics"]["logical_flow"]
```

### ToTRAG với tính năng nâng cao

```python
from src.deep_research_core.reasoning.tot_rag import ToTRAG

# Khởi tạo ToTRAG với tính năng nâng cao
tot_rag = ToTRAG(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    max_branches=3,
    max_depth=3,
    adaptive=True,  # Bật điều chỉnh tham số thích ứng
    use_cache=True,  # Bật bộ nhớ đệm
    evaluate_results=True  # Bật đánh giá kết quả
)

# Xử lý truy vấn
result = tot_rag.process(
    query="Giải thích cách hoạt động của mạng neural",
    top_k=5,  # Số lượng tài liệu để truy xuất
    force_refresh=False  # Sử dụng bộ nhớ đệm nếu có
)

# Truy cập kết quả
answer = result["answer"]
best_path = result["best_path"]
documents = result["documents"]
latency = result["latency"]

# Truy cập đánh giá (nếu được bật)
if "evaluation" in result:
    overall_quality = result["evaluation"]["metrics"]["overall_quality"]
```

### RAGToTCoT

```python
from src.deep_research_core.reasoning.rag_tot_cot import RAGTOTCOTReasoner
from src.deep_research_core.rag.sqlite_vector_rag import SQLiteVectorRAG

# Khởi tạo RAG
rag_system = SQLiteVectorRAG(
    db_path="path/to/vector_db.sqlite",
    embedding_model="multilingual-e5-large"
)

# Khởi tạo RAGToTCoT
rag_tot_cot = RAGTOTCOTReasoner(
    rag_system=rag_system,
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    max_branches=3,
    max_depth=3,
    adaptive=True,
    use_advanced_optimization=True,
    verbose=True
)

# Suy luận với tất cả các phương pháp
result = rag_tot_cot.reason(
    query="Giải thích cách hoạt động của mạng neural",
    use_rag=True,
    use_tot=True,
    use_cot=True,
    num_rag_results=5
)

# So sánh các phương pháp khác nhau
comparison = rag_tot_cot.compare_methods(
    query="Giải thích cách hoạt động của mạng neural"
)

# Phân tích kết quả so sánh
for method, data in comparison["methods"].items():
    print(f"Method: {method}")
    print(f"Answer length: {data['answer_length']}")
    print(f"Latency: {data['latency']:.2f} seconds")
    print(f"Answer: {data['answer'][:100]}...")
    print()
```

## Kết luận

Các tính năng nâng cao này giúp cải thiện hiệu suất và độ chính xác của các kỹ thuật suy luận trong hệ thống Deep Research Core. Việc kết hợp RAG, ToT và CoT tạo ra một hệ thống suy luận mạnh mẽ có thể xử lý các truy vấn phức tạp với độ chính xác cao.
