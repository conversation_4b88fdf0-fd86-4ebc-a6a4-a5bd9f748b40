# Vietnamese Compound Word Processor

## Overview

The Vietnamese Compound Word Processor is a specialized module designed for handling Vietnamese compound words, which are essential for accurate natural language processing in Vietnamese. This processor provides functionality for detecting, segmenting, and analyzing compound words in Vietnamese text.

## Features

- **Compound Word Detection**: Identify compound words in Vietnamese text with context
- **Text Segmentation**: Segment text while preserving compound words
- **Compound Analysis**: Analyze distribution of compound word types in text
- **Compound Validation**: Check if a word is a valid Vietnamese compound
- **Compound Classification**: Classify compounds into different types
- **Core Meaning Extraction**: Extract the semantic core from compounds

## Compound Word Types

Vietnamese compounds are classified into three main types:

1. **Coordinate Compounds** (từ ghép đẳng lập): Words where components have equal semantic weight
   - Examples: bàn ghế (table-chair), quần áo (clothes), cha mẹ (parents)

2. **Subordinate Compounds** (từ ghép chính phụ): Words where one component modifies the other
   - Examples: xe máy (motorbike), nhà trường (school), thức ăn (food)

3. **Reduplicated Compounds** (từ láy): Words formed through reduplication
   - Examples: h<PERSON><PERSON> (studying), đ<PERSON><PERSON>ng sá (roads), đỏ đỏ (reddish)

## Usage Examples

### Detecting Compound Words

```python
from deep_research_core.multilingual.vietnamese_compound_processor import VietnameseCompoundProcessor

processor = VietnameseCompoundProcessor.get_instance()
text = "Xe máy của tôi đang đỗ ở nhà cửa của bạn."
compounds = processor.detect_compound_words(text)

# Result:
# [
#   ("xe máy", 0, 6, "subordinate", 0.95),
#   ("nhà cửa", 17, 24, "coordinate", 0.92)
# ]
```

### Segmenting Text with Compounds

```python
text = "Xe máy của tôi đang đỗ ở nhà cửa của bạn."
segments = processor.segment_compound_words(text)

# Result:
# ["xe máy", "của", "tôi", "đang", "đỗ", "ở", "nhà cửa", "của", "bạn", "."]
```

### Analyzing Compound Distribution

```python
text = "Xe máy và bàn ghế là vật dụng quan trọng trong nhà cửa."
analysis = processor.analyze_compound_distribution(text)

# Result:
# {
#   "total_compounds": 3,
#   "type_distribution": {
#     "subordinate": 1,
#     "coordinate": 2,
#     "reduplicated": 0
#   },
#   "compound_examples": {
#     "subordinate": ["xe máy"],
#     "coordinate": ["bàn ghế", "nhà cửa"],
#     "reduplicated": []
#   },
#   "density": 0.3  # 30% of words are part of compounds
# }
```

## Integration with Other Modules

The Vietnamese Compound Word Processor is designed to work alongside other Vietnamese language processing modules:

- **Vietnamese Diacritic Processor**: For normalizing text with proper diacritics
- **Vietnamese Word Segmenter**: For basic word segmentation tasks
- **Vietnamese Named Entity Recognition**: For identifying named entities within compound words

## Technical Implementation

- **Singleton Pattern**: Ensures efficient resource usage
- **Dictionary-based Recognition**: Uses pre-defined dictionaries for common compounds
- **Pattern Recognition**: Uses patterns to identify reduplicated compounds
- **Statistical Analysis**: Analyzes compound word usage patterns

## Performance Considerations

- The processor maintains a dictionary of common compounds for quick lookup
- Compound word detection is performed with context-aware algorithms
- Segmentation preserves the integrity of compound words
- Classification of compounds is based on linguistic rules 