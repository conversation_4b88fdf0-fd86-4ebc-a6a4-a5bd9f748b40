# WebSearchAgent

WebSearchAgent là một module thông minh cho phép tìm kiếm thông tin trên web với khả năng tự động lựa chọn phương thức tìm kiếm phù hợp nhất dựa trên phân tích truy vấn.

## Tính năng chính

- **Phân tích truy vấn thông minh**: Phân tích độ phức tạp, lo<PERSON><PERSON> truy vấn, và các yếu tố khác để xác định phương thức tìm kiếm tối ưu.
- **Lựa chọn phương thức tự động**: Tự động chọn giữa tìm kiếm API (DuckDuckGo/Searx) và tìm kiếm dựa trên crawler (Crawlee).
- **Hỗ trợ đa ngôn ngữ**: Tự động phát hiện ngôn ngữ và tối ưu hóa tìm kiếm cho nhiều ngôn ngữ, đặc biệt là tiếng Việt.
- **Tìm kiếm theo thời gian**: Tìm kiếm thông tin trong các khoảng thời gian cụ thể (ngày, tuần, tháng, năm).
- **Tìm kiếm theo khu vực**: Tìm kiếm thông tin theo khu vực địa lý cụ thể.
- **Trích xuất nội dung**: Trích xuất và phân tích nội dung từ các trang web.
- **Bộ nhớ đệm thông minh**: Lưu trữ kết quả tìm kiếm với khóa đa tham số để tăng tốc độ và giảm số lượng request.
- **Giới hạn tốc độ (rate limiting)**: Đảm bảo không vượt quá giới hạn API và tránh bị chặn.
- **Đánh giá chất lượng kết quả**: Tự động đánh giá chất lượng kết quả tìm kiếm.

## Khi nào sử dụng API (DuckDuckGo/Searx)

- Truy vấn đơn giản, ngắn gọn
- Tìm kiếm thông tin phổ biến, dễ tìm
- Cần kết quả nhanh
- Không yêu cầu thông tin chi tiết
- Cần thông tin mới nhất

Ví dụ:
- "Thủ đô của Pháp"
- "iPhone 15 giá bao nhiêu"
- "Cách nấu phở"

## Khi nào sử dụng Crawlee

- Truy vấn phức tạp, dài
- Tìm kiếm thông tin chuyên sâu, kỹ thuật
- Cần thông tin chi tiết
- Cần trích xuất nội dung từ nhiều trang
- Cần phân tích sâu về một chủ đề

Ví dụ:
- "Cách triển khai thuật toán transformer trong PyTorch với attention mechanism"
- "So sánh chi tiết giữa React và Vue.js về performance và state management"
- "Phân tích tác động của chính sách tiền tệ mới đối với thị trường bất động sản Việt Nam 2024"

## Cách sử dụng cơ bản

```python
from deep_research_core.agents.web_search_agent import WebSearchAgent

# Khởi tạo WebSearchAgent
agent = WebSearchAgent(
    search_method="auto",  # "api", "crawlee", hoặc "auto"
    api_search_config={
        "engine": "duckduckgo",  # hoặc "searx"
        "searx_url": "https://searx.be"  # Chỉ cần cho Searx
    },
    crawlee_search_config={
        "max_depth": 2,
        "max_pages_per_url": 3
    },
    verbose=True
)

# Tìm kiếm với phương thức tự động
results = agent.search("Cách triển khai mạng neural trong Python", num_results=5)

# In kết quả
print(f"Phương thức tìm kiếm: {results['search_method']}")
print(f"Số kết quả: {len(results['results'])}")
for i, result in enumerate(results['results']):
    print(f"\nKết quả {i+1}:")
    print(f"Tiêu đề: {result['title']}")
    print(f"URL: {result['url']}")
    print(f"Đoạn trích: {result['snippet']}")
```

## Tìm kiếm đa ngôn ngữ

WebSearchAgent có khả năng tự động phát hiện ngôn ngữ của truy vấn và tối ưu hóa tìm kiếm cho ngôn ngữ đó. Bạn cũng có thể chỉ định ngôn ngữ cụ thể:

```python
# Tìm kiếm với ngôn ngữ tự động phát hiện
results = agent.search("Cách triển khai mạng neural trong Python")

# Tìm kiếm với ngôn ngữ cụ thể
results_en = agent.search("How to implement neural networks", language="en")
results_vi = agent.search("Cách triển khai mạng neural", language="vi")
results_fr = agent.search("Comment implémenter les réseaux de neurones", language="fr")
```

## Tìm kiếm theo thời gian

Bạn có thể tìm kiếm thông tin trong các khoảng thời gian cụ thể:

```python
# Tìm kiếm thông tin trong ngày
results_day = agent.search("Tin tức mới nhất", time_range="day")

# Tìm kiếm thông tin trong tuần
results_week = agent.search("Tin tức mới nhất", time_range="week")

# Tìm kiếm thông tin trong tháng
results_month = agent.search("Tin tức mới nhất", time_range="month")

# Tìm kiếm thông tin trong năm
results_year = agent.search("Tin tức mới nhất", time_range="year")
```

## Tìm kiếm theo khu vực

Bạn có thể tìm kiếm thông tin theo khu vực địa lý cụ thể:

```python
# Tìm kiếm thông tin ở Việt Nam
results_vn = agent.search("Tin tức mới nhất", region="vn")

# Tìm kiếm thông tin ở Mỹ
results_us = agent.search("Latest news", region="us")

# Tìm kiếm thông tin ở Pháp
results_fr = agent.search("Actualités", region="fr")
```

## Trích xuất nội dung

WebSearchAgent có thể trích xuất và phân tích nội dung từ các trang web:

```python
# Trích xuất nội dung từ URL
content = agent.extract_content("https://example.com/article")

# In thông tin
print(f"Tiêu đề: {content['title']}")
print(f"Ngôn ngữ: {content['language']}")
print(f"Nội dung: {content['content'][:500]}...")

# Trích xuất với HTML
content_with_html = agent.extract_content("https://example.com/article", include_html=True)

# Trích xuất liên kết
if content.get("links"):
    print(f"Số liên kết: {len(content['links'])}")
    for i, link in enumerate(content['links'][:5]):
        print(f"  {i+1}. {link['text']}: {link['url']}")
```

## Thuật toán lựa chọn phương thức tìm kiếm

WebSearchAgent sử dụng thuật toán phân tích truy vấn để quyết định phương thức tìm kiếm tối ưu:

1. **Phân tích truy vấn**:
   - Độ dài truy vấn
   - Kiểm tra xem có phải là câu hỏi không
   - Kiểm tra các chỉ báo độ phức tạp
   - Kiểm tra các thuật ngữ kỹ thuật
   - Kiểm tra yêu cầu thông tin chi tiết
   - Kiểm tra yêu cầu thông tin mới nhất
   - Phát hiện ngôn ngữ tự động

2. **Quyết định phương thức**:
   - Nếu truy vấn đơn giản (điểm phức tạp < 0.4) và không yêu cầu thông tin chi tiết → API
   - Nếu truy vấn thuộc loại navigational hoặc transactional → Crawlee
   - Nếu truy vấn yêu cầu thông tin chi tiết hoặc phức tạp (điểm phức tạp > 0.7) → Crawlee
   - Nếu truy vấn yêu cầu thông tin mới nhất → API
   - Mặc định → API

3. **Đánh giá chất lượng kết quả**:
   - Số lượng kết quả
   - Độ liên quan của kết quả với truy vấn
   - Tính điểm chất lượng tổng thể

## Hỗ trợ nhiều công cụ tìm kiếm

WebSearchAgent hỗ trợ nhiều công cụ tìm kiếm khác nhau:

### Công cụ tìm kiếm web thông thường

1. **DuckDuckGo**: Tìm kiếm miễn phí, không yêu cầu API key
2. **Searx**: Tìm kiếm miễn phí, hỗ trợ nhiều instance
3. **Google**: Sử dụng Google Custom Search API (yêu cầu API key và CSE ID)
4. **Bing**: Sử dụng Bing Search API (yêu cầu API key)
5. **Serper**: Sử dụng Serper.dev API (yêu cầu API key)
6. **SerpAPI**: Sử dụng SerpAPI (yêu cầu API key)
7. **Qwant**: Tìm kiếm miễn phí, tập trung vào quyền riêng tư, không yêu cầu API key
8. **Brave Search**: Sử dụng Brave Search API (có phiên bản miễn phí và trả phí)
9. **Yandex**: Sử dụng Yandex Search API (yêu cầu API key)

### Công cụ tìm kiếm học thuật

1. **Google Scholar**: Tìm kiếm học thuật, hỗ trợ lọc theo năm và ngôn ngữ
2. **Semantic Scholar**: Tìm kiếm học thuật với API chính thức, hỗ trợ lọc theo năm và trường
3. **arXiv**: Tìm kiếm bài báo khoa học trên arXiv, hỗ trợ lọc theo danh mục và sắp xếp
4. **PubMed**: Tìm kiếm bài báo y khoa, hỗ trợ lọc theo khoảng thời gian

### Cấu hình Google Search

```python
agent = WebSearchAgent(
    api_search_config={
        "engine": "google",
        "google_api_key": "YOUR_GOOGLE_API_KEY",
        "google_cse_id": "YOUR_GOOGLE_CSE_ID",
        "language": "vi",
        "safe_search": True
    }
)
```

### Cấu hình Qwant

```python
agent = WebSearchAgent(
    api_search_config={
        "engine": "qwant",
        "language": "vi"
    }
)
```

### Cấu hình Brave Search

```python
agent = WebSearchAgent(
    api_search_config={
        "engine": "brave",
        "brave_api_key": "YOUR_BRAVE_API_KEY",  # Tùy chọn cho phiên bản trả phí
        "language": "vi",
        "safe_search": True
    }
)
```

### Cấu hình Yandex

```python
agent = WebSearchAgent(
    api_search_config={
        "engine": "yandex",
        "yandex_api_key": "YOUR_YANDEX_API_KEY",
        "language": "vi",
        "region": "RU"  # Mã khu vực
    }
)
```

### Cấu hình Google Scholar

```python
agent = WebSearchAgent(
    api_search_config={
        "engine": "google_scholar",
        "language": "vi",
        "start_year": 2020,
        "end_year": 2024
    }
)
```

### Cấu hình Semantic Scholar

```python
agent = WebSearchAgent(
    api_search_config={
        "engine": "semantic_scholar",
        "semantic_scholar_api_key": "YOUR_SEMANTIC_SCHOLAR_API_KEY",
        "semantic_scholar_fields": ["title", "abstract", "url", "year", "authors", "venue", "citationCount"],
        "year": 2023
    }
)
```

### Cấu hình arXiv

```python
agent = WebSearchAgent(
    api_search_config={
        "engine": "arxiv",
        "arxiv_categories": ["cs.AI", "cs.CL", "cs.LG"],
        "sort_by": "relevance",
        "sort_order": "descending"
    }
)
```

### Cấu hình PubMed

```python
agent = WebSearchAgent(
    api_search_config={
        "engine": "pubmed",
        "pubmed_api_key": "YOUR_PUBMED_API_KEY",
        "date_range": {
            "start": "2022/01/01",
            "end": "2024/12/31"
        }
    }
)
```

### Cấu hình Bing Search

```python
agent = WebSearchAgent(
    api_search_config={
        "engine": "bing",
        "bing_api_key": "YOUR_BING_API_KEY",
        "language": "vi",
        "region": "VN",
        "safe_search": True
    }
)
```

### Cấu hình Serper

```python
agent = WebSearchAgent(
    api_search_config={
        "engine": "serper",
        "serper_api_key": "YOUR_SERPER_API_KEY",
        "language": "vi",
        "region": "vn"
    }
)
```

### Cấu hình SerpAPI

```python
agent = WebSearchAgent(
    api_search_config={
        "engine": "serpapi",
        "serpapi_api_key": "YOUR_SERPAPI_API_KEY",
        "language": "vi",
        "region": "vn",
        "safe_search": True
    }
)
```

## Hỗ trợ nhiều định dạng tài liệu

WebSearchAgent có thể trích xuất nội dung từ nhiều định dạng tài liệu khác nhau:

1. **HTML**: Trích xuất nội dung, tiêu đề, liên kết, và hình ảnh từ trang web
2. **PDF**: Trích xuất văn bản từ tệp PDF
3. **DOCX**: Trích xuất văn bản và bảng từ tệp DOCX
4. **JSON**: Phân tích và trích xuất dữ liệu từ tệp JSON
5. **Text**: Trích xuất nội dung từ tệp văn bản thuần túy

## Cấu hình nâng cao

```python
agent = WebSearchAgent(
    search_method="auto",
    api_search_config={
        "engine": "duckduckgo",  # hoặc "searx", "google", "bing", "serper", "serpapi"
        "searx_url": "https://searx.be",  # Có thể là một URL hoặc danh sách các URL
        "language": "auto",  # hoặc "en", "vi", "fr", v.v.
        "time_range": "",  # "", "day", "week", "month", "year"
        "region": "",  # "", "vn", "us", "fr", v.v.
        "safe_search": True,
        "engines": "google,bing,duckduckgo",  # Chỉ cho Searx
        "additional_params": {},  # Tham số bổ sung

        # API keys cho các công cụ tìm kiếm khác
        "google_api_key": "",
        "google_cse_id": "",
        "bing_api_key": "",
        "serper_api_key": "",
        "serpapi_api_key": ""
    },
    crawlee_search_config={
        "max_depth": 2,
        "max_pages_per_url": 3,
        "max_urls": 5,
        "timeout": 15,
        "follow_links": True,
        "respect_robots_txt": True
    },
    content_extractor_config={
        "extract_links": True,
        "extract_images": False,
        "max_content_length": 10000,
        "timeout": 15
    },
    cache_ttl=3600,  # 1 giờ
    rate_limit=10,   # 10 request/phút
    verbose=True
)
```

## Ví dụ sử dụng từ dòng lệnh

WebSearchAgent có thể được sử dụng từ dòng lệnh thông qua script ví dụ:

```bash
# Tìm kiếm cơ bản
python examples/web_search_agent_example.py --query "Cách triển khai mạng neural trong Python"

# Tìm kiếm với phương thức cụ thể
python examples/web_search_agent_example.py --query "Cách triển khai mạng neural" --method crawlee

# Tìm kiếm với ngôn ngữ cụ thể
python examples/web_search_agent_example.py --query "How to implement neural networks" --language en

# Tìm kiếm với khoảng thời gian
python examples/web_search_agent_example.py --query "Tin tức mới nhất" --time-range day

# Tìm kiếm với khu vực
python examples/web_search_agent_example.py --query "Tin tức mới nhất" --region vn

# Trích xuất nội dung từ URL
python examples/web_search_agent_example.py --extract "https://example.com/article"
```
