# Supervised Fine-Tuning (SFT) Guide

This guide provides information on how to use the Supervised Fine-Tuning (SFT) module in Deep Research Core.

## Overview

Supervised Fine-Tuning (SFT) is a technique used to adapt pre-trained language models to specific tasks or domains using human-annotated data. The SFT module in Deep Research Core provides a flexible framework for fine-tuning language models with different providers.

## Supported Providers

The SFT module supports the following providers:

1. **Hugging Face**: Fine-tune models from the Hugging Face model hub.
2. **OpenAI**: Fine-tune OpenAI models through their API.
3. **OpenRouter**: Prepare data for fine-tuning with OpenRouter models.

## Basic Usage

### Hugging Face SFT

```python
from deep_research_core.rl_tuning.sft import HuggingFaceSFT

# Initialize SFT trainer
trainer = HuggingFaceSFT(
    model_name="gpt2",  # or any other model from Hugging Face
    output_dir="output/sft",
    data_path="data/training_data.jsonl",
    batch_size=4,
    learning_rate=5e-5,
    num_epochs=3,
    max_length=1024,
    use_lora=True,  # Use LoRA for parameter-efficient fine-tuning
    lora_r=8,
    lora_alpha=16,
    lora_dropout=0.05
)

# Run SFT
metrics = trainer.run()
print(metrics)
```

### OpenAI SFT

```python
from deep_research_core.rl_tuning.sft import OpenAISFT

# Initialize SFT trainer
trainer = OpenAISFT(
    model_name="gpt-3.5-turbo",  # or any other OpenAI model
    output_dir="output/sft",
    data_path="data/training_data.jsonl",
    num_epochs=3,
    suffix_name="my-custom-model"  # Optional suffix for the fine-tuned model
)

# Run SFT
metrics = trainer.run()
print(metrics)
```

### OpenRouter SFT

```python
from deep_research_core.rl_tuning.sft import OpenRouterSFT

# Initialize SFT trainer
trainer = OpenRouterSFT(
    model_name="moonshotai/moonlight-16b-a3b-instruct:free",  # or any other OpenRouter model
    output_dir="output/sft",
    data_path="data/training_data.jsonl",
    export_format="jsonl"  # Format to export data in (jsonl, csv, or parquet)
)

# Run SFT
metrics = trainer.run()
print(metrics)
```

## Data Format

The SFT module expects data in JSONL format, with each line containing a JSON object with at least two fields:

```json
{"prompt": "What is 1 + 1?", "completion": "The answer is 2."}
```

The field names can be customized using the `input_field` and `output_field` parameters.

## Advanced Usage

### Parameter-Efficient Fine-Tuning with LoRA

For Hugging Face models, you can use LoRA (Low-Rank Adaptation) for parameter-efficient fine-tuning:

```python
trainer = HuggingFaceSFT(
    model_name="gpt2",
    output_dir="output/sft",
    data_path="data/training_data.jsonl",
    use_lora=True,
    lora_r=8,  # Rank of the LoRA update matrices
    lora_alpha=16,  # LoRA scaling factor
    lora_dropout=0.05,  # LoRA dropout rate
    lora_target_modules=["q_proj", "v_proj"]  # Modules to apply LoRA to
)
```

### Quantization

For Hugging Face models, you can use quantization to reduce memory usage:

```python
trainer = HuggingFaceSFT(
    model_name="gpt2",
    output_dir="output/sft",
    data_path="data/training_data.jsonl",
    load_in_8bit=True,  # Load model in 8-bit precision
    # or
    load_in_4bit=True  # Load model in 4-bit precision
)
```

### Custom Preprocessing

You can provide a custom preprocessing function to transform the data before training:

```python
def preprocess_data(sample):
    # Add a prefix to the prompt
    sample["prompt"] = "Answer the following question: " + sample["prompt"]
    return sample

trainer = HuggingFaceSFT(
    model_name="gpt2",
    output_dir="output/sft",
    data_path="data/training_data.jsonl",
    preprocessing_fn=preprocess_data
)
```

## Creating Custom SFT Implementations

You can create custom SFT implementations by subclassing `BaseSFT` and implementing the required methods:

```python
from deep_research_core.rl_tuning.sft import BaseSFT

class CustomSFT(BaseSFT):
    def _load_model_and_tokenizer(self):
        # Load model and tokenizer
        ...
        return model, tokenizer
    
    def train_model(self, train_dataloader, eval_dataloader=None):
        # Train model
        ...
        return metrics
    
    def evaluate_model(self, eval_dataloader):
        # Evaluate model
        ...
        return metrics
```

## Best Practices

1. **Data Quality**: Ensure your training data is high-quality and representative of the task you want the model to perform.
2. **Validation Split**: Use a validation split to monitor the model's performance during training.
3. **Learning Rate**: Start with a small learning rate (e.g., 5e-5) and adjust as needed.
4. **Batch Size**: Use the largest batch size that fits in your GPU memory.
5. **Number of Epochs**: Start with a small number of epochs (e.g., 3) and increase if needed.
6. **LoRA**: Use LoRA for parameter-efficient fine-tuning when possible.
7. **Quantization**: Use quantization to reduce memory usage when working with large models.

## Troubleshooting

### Out of Memory Errors

If you encounter out of memory errors, try the following:

1. Reduce the batch size
2. Use gradient accumulation
3. Use LoRA for parameter-efficient fine-tuning
4. Use quantization (8-bit or 4-bit)
5. Reduce the maximum sequence length

### Slow Training

If training is slow, try the following:

1. Use a smaller model
2. Use a smaller dataset
3. Use a smaller maximum sequence length
4. Use mixed precision training (fp16)
5. Use a GPU with more memory

### Poor Performance

If the fine-tuned model performs poorly, try the following:

1. Improve the quality of your training data
2. Increase the number of training examples
3. Increase the number of training epochs
4. Adjust the learning rate
5. Try a different base model

## Further Reading

- [Hugging Face Fine-Tuning Guide](https://huggingface.co/docs/transformers/training)
- [OpenAI Fine-Tuning Guide](https://platform.openai.com/docs/guides/fine-tuning)
- [LoRA Paper](https://arxiv.org/abs/2106.09685)
- [QLoRA Paper](https://arxiv.org/abs/2305.14314)
