# Từ điển từ ghép tiếng Việt theo lĩnh vực chuyên ngành

## Tổng quan

Mô-đun này cung cấp từ điển từ ghép tiếng Việt phân loại theo lĩnh vực chuyên ngành, cho phép phát hiện và phân tích từ ghép trong các văn bản chuyên ngành. Tính năng này mở rộng khả năng của `VietnameseCompoundProcessor` bằng cách bổ sung các từ ghép chuyên ngành từ nhiều lĩnh vực khác nhau.

## Các lĩnh vực chuyên ngành

Hệ thống hiện hỗ trợ 8 lĩnh vực chuyên ngành sau:

1. **Y tế / Medical**: Thu<PERSON><PERSON> ngữ y học, g<PERSON><PERSON><PERSON> phẫu, b<PERSON><PERSON> lý, d<PERSON><PERSON><PERSON>h<PERSON>, v.v.
2. **<PERSON><PERSON><PERSON> lu<PERSON> / Legal**: Thu<PERSON><PERSON> ngữ ph<PERSON> lý, thủ tụ<PERSON> tố tụng, <PERSON><PERSON><PERSON>, quyền và nghĩa vụ, v.v.
3. **Công nghệ / Technology**: Thuật ngữ CNTT, AI, học máy, phần mềm, phần cứng, v.v.
4. **Giáo dục / Education**: Thuật ngữ về dạy và học, trường học, chương trình đào tạo, v.v.
5. **Kinh tế / Economics**: Thuật ngữ tài chính, ngân hàng, kinh doanh, đầu tư, v.v.
6. **Nông nghiệp / Agriculture**: Thuật ngữ về trồng trọt, chăn nuôi, thủy sản, lâm nghiệp, v.v.
7. **Môi trường / Environment**: Thuật ngữ về sinh thái, biến đổi khí hậu, ô nhiễm, bảo tồn, v.v.
8. **Khoa học / Science**: Thuật ngữ khoa học cơ bản như vật lý, hóa học, sinh học, v.v.

## Cách sử dụng

### 1. Truy cập từ điển từ ghép chuyên ngành

```python
from deep_research_core.multilingual.vietnamese_domain_compounds import VietnameseDomainCompounds

# Lấy tất cả từ ghép chuyên ngành
all_compounds = VietnameseDomainCompounds.get_all_domain_compounds()

# Lấy từ ghép của một lĩnh vực cụ thể
medical_compounds = VietnameseDomainCompounds.get_domain_compounds("medical")
tech_compounds = VietnameseDomainCompounds.get_domain_compounds("technology")

# Xem danh sách các lĩnh vực có sẵn
available_domains = VietnameseDomainCompounds.get_available_domains()
```

### 2. Sử dụng với VietnameseCompoundProcessor

```python
from deep_research_core.multilingual.vietnamese_compound_processor import VietnameseCompoundProcessor

# Khởi tạo processor
processor = VietnameseCompoundProcessor.get_instance()

# Văn bản mẫu
text = "Trí tuệ nhân tạo đang được ứng dụng trong chẩn đoán bệnh tim mạch tại các bệnh viện lớn."

# Phát hiện tất cả các từ ghép
compounds = processor.detect_compound_words(text)
for compound, start, end, compound_type in compounds:
    print(f"'{compound}' - {compound_type}")

# Lọc từ ghép theo lĩnh vực y tế
medical_compounds = processor.filter_by_domain(text, "medical")
for compound, start, end, compound_type in medical_compounds:
    print(f"Y tế: '{compound}' - {compound_type}")

# Lọc từ ghép theo lĩnh vực công nghệ
tech_compounds = processor.filter_by_domain(text, "technology")
for compound, start, end, compound_type in tech_compounds:
    print(f"Công nghệ: '{compound}' - {compound_type}")

# Phân loại từ ghép theo lĩnh vực
domain_compounds = processor.detect_domain_specific_compounds(text)
for domain, compounds_list in domain_compounds.items():
    if compounds_list:
        print(f"Lĩnh vực {domain}:")
        for compound, _, _, _ in compounds_list:
            print(f"- {compound}")
```

### 3. Phân tích phân bổ lĩnh vực trong văn bản

```python
from deep_research_core.multilingual.vietnamese_compound_processor import VietnameseCompoundProcessor

processor = VietnameseCompoundProcessor.get_instance()

# Văn bản cần phân tích
document = """
Nghiên cứu mới về mối liên hệ giữa biến đổi khí hậu và sức khỏe cộng đồng
Các nhà khoa học tại trường đại học Y đã công bố nghiên cứu mới về tác động 
của biến đổi khí hậu đến sức khỏe cộng đồng. Theo báo cáo, ô nhiễm không khí 
và nước biển dâng đang ảnh hưởng trực tiếp đến bệnh viện và hệ thống y tế.
"""

# Phát hiện và phân loại từ ghép theo lĩnh vực
domain_compounds = processor.detect_domain_specific_compounds(document)

# Tính phân bổ lĩnh vực
domain_distribution = {}
for domain, compounds_list in domain_compounds.items():
    if compounds_list:
        domain_distribution[domain] = len(compounds_list)

# Sắp xếp và hiển thị kết quả
sorted_domains = sorted(domain_distribution.items(), key=lambda x: x[1], reverse=True)
total = sum(domain_distribution.values())

print("Phân bổ lĩnh vực trong văn bản:")
for domain, count in sorted_domains:
    print(f"{domain}: {count} từ ghép ({count/total*100:.1f}%)")
```

## Mở rộng từ điển từ ghép

Bạn có thể mở rộng từ điển từ ghép bằng cách thêm các từ vào lớp `VietnameseDomainCompounds`. Mỗi từ ghép được gán một loại (type): "coordinate" (từ ghép đẳng lập), "subordinate" (từ ghép chính phụ), hoặc "reduplicated" (từ láy).

```python
from deep_research_core.multilingual.vietnamese_domain_compounds import VietnameseDomainCompounds

# Mở rộng từ điển từ ghép y tế
extended_medical = VietnameseDomainCompounds.MEDICAL_COMPOUNDS.copy()
extended_medical.update({
    "chẩn đoán hình ảnh": "subordinate",
    "cộng hưởng từ": "subordinate",
    "siêu âm doppler": "subordinate",
    "nội soi": "subordinate"
})

# Sử dụng từ điển mở rộng
# (Trong triển khai thực tế, bạn nên cập nhật file vietnamese_domain_compounds.py)
```

## Lợi ích của từ điển từ ghép chuyên ngành

1. **Phân tích chuyên ngành**: Xác định chủ đề chính của văn bản dựa trên tần suất xuất hiện của từ ghép theo lĩnh vực
2. **Cải thiện RAG**: Tăng cường truy xuất thông tin liên quan đến lĩnh vực cụ thể
3. **Phân loại văn bản**: Hỗ trợ việc phân loại văn bản theo lĩnh vực dựa trên các từ ghép đặc trưng
4. **Tổng hợp văn bản**: Cải thiện việc tổng hợp văn bản bằng cách nhận diện và giữ nguyên các từ ghép chuyên ngành
5. **Dịch máy**: Nâng cao chất lượng dịch máy bằng cách bảo toàn các thuật ngữ chuyên ngành

## Thông số kỹ thuật

- **Số lượng lĩnh vực**: 8 lĩnh vực
- **Tổng số từ ghép**: > 350 từ ghép chuyên ngành
- **Độ chính xác phân loại**: Dựa trên phân loại thủ công
- **Khả năng mở rộng**: Dễ dàng thêm từ ghép và lĩnh vực mới

## Yêu cầu hệ thống

- Python 3.6+
- Phụ thuộc vào `VietnameseDiacriticProcessor` cho việc chuẩn hóa dấu
- Hoạt động tốt nhất với bộ từ điển từ ghép cơ bản đã có sẵn

## Kế hoạch phát triển tương lai

1. Mở rộng thêm từ ghép cho các lĩnh vực hiện có
2. Bổ sung các lĩnh vực mới như:
   - Thể thao (Sports)
   - Nghệ thuật (Arts)
   - Du lịch (Tourism)
   - Giao thông (Transportation)
3. Tích hợp với các mô hình nhúng từ (embedding) đặc thù cho tiếng Việt
4. Cung cấp API để cộng đồng đóng góp từ ghép chuyên ngành
5. Phát triển công cụ tự động trích xuất từ ghép từ kho ngữ liệu chuyên ngành 