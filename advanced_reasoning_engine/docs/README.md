# Deep Research Core - Documentation

## Overview

This directory contains comprehensive documentation for the Deep Research Core project, which is a research and development system for AI models combining local models and cloud providers, with a focus on reasoning capabilities, Vietnamese language support, and UI integration.

## Documentation Structure

- [**User Guide**](user_guide/index.md): Getting started and fundamental usage
- [**Tutorials**](tutorials/index.md): Comprehensive step-by-step guides
- [**API Reference**](api_reference.md): Detailed class and function documentation
- [**Examples**](examples/README.md): Practical code examples for various use cases
- [**Guides**](guides/): Technical guides for specific features
- [**Implementation Notes**](implementation_notes/): Detailed explanations of implementations
- **sft/**: Documentation for supervised fine-tuning
- **testing/**: Testing guidelines and procedures

## Main Documentation Files

- **[api_reference.md](api_reference.md)**: Comprehensive API reference
- **[user_guide.md](user_guide.md)**: Main user guide
- **[testing_guide.md](testing_guide.md)**: Testing framework documentation
- **[tot_guide.md](tot_guide.md)**: Tree of Thoughts reasoning guide
- **[cot_rag_guide.md](cot_rag_guide.md)**: Chain of Thought with RAG integration guide
- **[vietnamese_support.md](vietnamese_support.md)**: Vietnamese language support guide

## Reasoning Modules

- **[tot_rag_guide.md](tot_rag_guide.md)**: Tree of Thoughts with RAG integration
- **[enhanced_cot_guide.md](enhanced_cot_guide.md)**: Enhanced Chain of Thought
- **[cotrag_enhanced.md](cotrag_enhanced.md)**: Enhanced CoT-RAG integration
- **[reasoning_features.md](reasoning_features.md)**: Overview of reasoning features

## RAG Implementation

- **[rag_implementations_guide.md](rag_implementations_guide.md)**: RAG implementations guide
- **[advanced_rag_guide.md](advanced_rag_guide.md)**: Advanced RAG features
- **[vector_store_implementations_guide.md](vector_store_implementations_guide.md)**: Vector store implementations guide
- **[source_attribution_guide.md](source_attribution_guide.md)**: Guide for source attribution in RAG

## Vietnamese Language Support

- **[vietnamese_embeddings.md](vietnamese_embeddings.md)**: Vietnamese embeddings guide
- **[vietnamese_embedding_guide.md](vietnamese_embedding_guide.md)**: Detailed Vietnamese embedding guide
- **[vietnamese_domain_compounds.md](vietnamese_domain_compounds.md)**: Domain-specific compound handling
- **[vietnamese_compound_processor.md](vietnamese_compound_processor.md)**: Vietnamese compound processing

## Model Fine-tuning

- **[sft_guide.md](sft_guide.md)**: Supervised fine-tuning guide
- **[dpo_guide.md](dpo_guide.md)**: Direct Preference Optimization guide
- **[gemini_dpo_implementation.md](gemini_dpo_implementation.md)**: Gemini DPO implementation details

## Monitoring and Evaluation

- **[monitoring_guide.md](monitoring_guide.md)**: Monitoring framework guide
- **[monitoring_guide_comprehensive.md](monitoring_guide_comprehensive.md)**: Comprehensive monitoring guide
- **[model_evaluation.md](model_evaluation.md)**: Model evaluation framework

## Getting Started

For new users, we recommend starting with the [user_guide.md](user_guide.md) file, which provides an introduction to the system and its capabilities. Then, explore the specific guides based on your interests:

1. For reasoning capabilities: [reasoning_features.md](reasoning_features.md)
2. For RAG integration: [rag_implementations_guide.md](rag_implementations_guide.md)
3. For Vietnamese support: [vietnamese_support.md](vietnamese_support.md)
4. For model fine-tuning: [sft_guide.md](sft_guide.md) and [dpo_guide.md](dpo_guide.md)

## Contributing to Documentation

If you would like to contribute to the documentation, please follow these guidelines:

1. Use Markdown format for all documentation files
2. Include a clear title and description at the beginning of each file
3. Use code examples with proper syntax highlighting
4. Reference related documentation files where appropriate
5. Add new documentation to the appropriate directory based on content type
