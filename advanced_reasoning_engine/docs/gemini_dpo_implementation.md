# Gemini DPO Implementation Documentation

## Overview

This document provides information about the implementation of Direct Preference Optimization (DPO) for Google's Gemini API models in the Deep Research Core project. The implementation allows for training language models using human preference data.

## Introduction to DPO

Direct Preference Optimization (DPO) is a method for fine-tuning language models using human preference data without explicitly constructing a reward model. Proposed in the paper ["Direct Preference Optimization: Your Language Model is Secretly a Reward Model"](https://arxiv.org/abs/2305.18290), DPO offers a simpler alternative to traditional RLHF (Reinforcement Learning from Human Feedback).

The key idea behind DPO is to directly maximize the likelihood of preferred responses relative to non-preferred ones, as determined by human feedback. This approach bypasses the need for a separate reward model and RL optimization, making fine-tuning more efficient.

## Implementation Details

The `GeminiDPO` class is implemented in `src/deep_research_core/rl_tuning/dpo/gemini_dpo.py` and follows the pattern established by the `BaseDPO` abstract class. It provides functionality for:

1. Initializing Gemini API models (reference and policy models)
2. Computing model outputs
3. Training models using preference data
4. Generating responses with the policy model
5. Saving and loading configurations

## Class Structure

```python
class GeminiDPO(BaseDPO):
    def __init__(
        self,
        reference_model_name: str = "gemini-1.5-pro",
        policy_model_name: Optional[str] = None,
        api_key: Optional[str] = None,
        api_base: Optional[str] = None,
        beta: float = 0.1,
        max_length: int = 1024,
        batch_size: int = 4,
        learning_rate: float = 5e-5,
        num_epochs: int = 3,
        log_interval: int = 10,
        save_interval: int = 100,
        output_dir: str = "./dpo_output",
        device: str = "cuda" if torch.cuda.is_available() else "cpu",
        model_kwargs: Optional[Dict[str, Any]] = None,
        optimizer_kwargs: Optional[Dict[str, Any]] = None,
        verbose: bool = False,
    )
```

## Key Methods

- `_init_models()`: Initializes the reference and policy models using Google's Generative AI client
- `_compute_model_outputs()`: Computes outputs for prompt-response pairs using a specified model
- `_compute_reference_outputs()`: Computes outputs from the reference model
- `_compute_policy_outputs()`: Computes outputs from the policy model
- `_prepare_fine_tuning_file()`: Prepares data for fine-tuning in the format expected by Gemini API
- `train()`: Simulates training the policy model using preference data (actual fine-tuning depends on Gemini API capabilities)
- `generate()`: Generates responses using the policy model
- `_save_model()`: Saves the model configuration
- `_load_model()`: Loads a model configuration from a file

## Implementation Notes

As of the time of implementation, Google's Gemini API does not support direct fine-tuning through their API in the same way as OpenAI or Anthropic. The `GeminiDPO` implementation provides:

1. A complete implementation that adheres to the `BaseDPO` interface
2. Full support for inference using Gemini models
3. Simulation of the training process for compatibility with the DPO interface
4. A foundation that can be easily updated when Google adds more fine-tuning capabilities

## Usage Examples

### Basic Initialization

```python
from deep_research_core.rl_tuning.dpo import GeminiDPO

# Initialize GeminiDPO with API key
dpo = GeminiDPO(
    reference_model_name="gemini-1.5-pro",
    policy_model_name="gemini-1.5-pro",
    api_key="your_api_key_here",  # Or set GOOGLE_API_KEY environment variable
    beta=0.1,
    max_length=1024,
    batch_size=4,
    verbose=True
)
```

### Generating Responses

```python
# Generate a response
prompt = "Explain quantum computing to a high school student."
response = dpo.generate(prompt, temperature=0.7, max_tokens=300)
print(response)
```

### Training with Preference Data

```python
from deep_research_core.rl_tuning.dpo import PreferenceData

# Create preference data
preference_data = PreferenceData(
    prompts=["What is AI?", "Explain machine learning."],
    chosen_responses=["AI is...", "Machine learning is..."],
    rejected_responses=["AI is when...", "ML is..."]
)

# Define callback function
def training_callback(epoch, metrics):
    print(f"Epoch {epoch+1}: Loss={metrics['train_loss']:.4f}, Accuracy={metrics['train_accuracy']:.4f}")

# Train model
metrics = dpo.train(
    preference_data=preference_data,
    callback=training_callback
)
```

### Saving and Loading

```python
# Save model configuration
path = dpo.save()

# Load model configuration
dpo.load(path)
```

## Complete Example

For a complete working example, see `examples/gemini_dpo_example.py` which demonstrates:
- Initialization
- Response generation
- Training with preference data
- Saving and loading model configurations

## Requirements

- Python 3.8+
- `google-generativeai` package
- Google API key (set as environment variable `GOOGLE_API_KEY` or passed directly)
- PyTorch

## Error Handling

The implementation includes robust error handling:
- Validation of API key
- Handling of API errors during model initialization and generation
- Graceful fallbacks in case of errors during model output computation
- Structured logging of errors

## Future Improvements

As Google's Gemini API evolves, the following improvements could be made:
1. Direct support for fine-tuning when Google adds this capability
2. Improved log probability estimation using more accurate methods
3. Support for more advanced features specific to Gemini models
4. Performance optimizations for large datasets

## References

1. "Direct Preference Optimization: Your Language Model is Secretly a Reward Model" (https://arxiv.org/abs/2305.18290)
2. Google Generative AI SDK (https://github.com/google/generative-ai-python)
3. Deep Research Core DPO module documentation 