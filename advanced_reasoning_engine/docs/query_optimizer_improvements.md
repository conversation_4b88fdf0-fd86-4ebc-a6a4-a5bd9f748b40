# Cải thiện QueryOptimizer

## Tổng quan

Tài liệu này mô tả các cải tiến đã thực hiện cho module `QueryOptimizer` trong hệ thống Deep Research Core, tập trung vào việc cải thiện hỗ trợ tiếng Việt và khả năng học từ kết quả tìm kiếm.

## Các cải tiến chính

### 1. C<PERSON><PERSON> thiện trích xuất từ khóa cho tiếng Việt

- **Tích hợp underthesea**: Thêm hỗ trợ cho thư viện `underthesea` để tokenize tiếng Việt chính xác hơn.
- **C<PERSON> chế fallback**: N<PERSON>u không có `underthesea`, sử dụng phương pháp thay thế dựa trên danh sách từ ghép phổ biến.
- **<PERSON><PERSON> lý từ ghép**: G<PERSON><PERSON> nguyên các từ ghép phổ biến trong tiếng Việt như "trí tuệ nhân tạo", "học máy", v.v.
- **Gộp từ liên quan**: Thêm logic để gộp các từ thường đi cùng nhau trong tiếng Việt thành cụm từ có ý nghĩa.
- **Hỗ trợ từ viết tắt**: Thêm danh sách các từ viết tắt đặc biệt (AI, ML, DL, v.v.) để không bị loại bỏ khi lọc từ ngắn.

### 2. Khả năng học từ kết quả tìm kiếm

- **Cache biến thể truy vấn**: Lưu trữ các biến thể truy vấn đã tạo để tái sử dụng.
- **Ghi nhớ biến thể thành công**: Thêm cơ chế để ghi nhận các biến thể truy vấn đã thành công.
- **Ưu tiên biến thể thành công**: Khi tối ưu hóa truy vấn, ưu tiên sử dụng các biến thể đã thành công trước đây.
- **Lưu trữ bền vững**: Thêm khả năng lưu cache vào file và tải lại khi khởi động.

### 3. Tích hợp với WebSearchAgentLocal

- **Ghi nhận kết quả tìm kiếm**: Thêm code để ghi nhận biến thể truy vấn thành công trong `WebSearchAgentLocal`.
- **Cải thiện luồng tìm kiếm**: Khi một biến thể truy vấn tìm kiếm thành công, ghi nhận lại để sử dụng trong tương lai.

## Chi tiết kỹ thuật

### Cấu trúc QueryOptimizer mới

```python
def __init__(
    self,
    language: str = "auto",
    embedding_model: Optional[str] = None,
    use_knowledge_graph: bool = True,
    use_rules: bool = True,
    max_variants: int = 5,
    cache_file: Optional[str] = None,
    enable_learning: bool = True
):
    # ...
```

### Phương thức trích xuất từ khóa cải tiến

```python
def _extract_keywords(self, query: str, language: str) -> List[str]:
    # ...
    # Xử lý đặc biệt cho tiếng Việt
    if language == "vi":
        # Thử sử dụng underthesea nếu có thể
        try:
            from underthesea import word_tokenize
            # Tokenize tiếng Việt
            words = word_tokenize(query_clean)
        except ImportError:
            # Fallback: Xử lý đơn giản cho tiếng Việt
            # ...
    # ...
```

### Phương thức ghi nhận biến thể thành công

```python
def record_successful_variant(self, original_query: str, successful_variant: str) -> None:
    """
    Ghi nhận biến thể truy vấn thành công.
    """
    if not self.enable_learning:
        return
        
    query_key = original_query.lower().strip()
    
    # Khởi tạo danh sách nếu chưa có
    if query_key not in self.successful_variants:
        self.successful_variants[query_key] = []
    
    # Thêm biến thể thành công nếu chưa có
    if successful_variant not in self.successful_variants[query_key]:
        self.successful_variants[query_key].append(successful_variant)
        logger.info(f"Recorded successful variant for query '{original_query}': '{successful_variant}'")
        
        # Lưu cache vào file nếu có
        self._save_cache()
```

## Hướng dẫn sử dụng

### Khởi tạo QueryOptimizer với khả năng học

```python
optimizer = QueryOptimizer(
    language="auto",
    cache_file="query_optimizer_cache.json",
    enable_learning=True
)
```

### Tối ưu hóa truy vấn

```python
result = optimizer.optimize("Những xu hướng AI mới nhất trong năm 2025?")
```

### Ghi nhận biến thể thành công

```python
optimizer.record_successful_variant(
    original_query="Những xu hướng AI mới nhất trong năm 2025?",
    successful_variant="xu hướng trí tuệ nhân tạo mới nhất 2025"
)
```

## Kết quả thử nghiệm

Thử nghiệm với các truy vấn tiếng Việt cho thấy:

1. Cải thiện đáng kể trong việc trích xuất từ khóa từ truy vấn tiếng Việt
2. Khả năng học từ kết quả tìm kiếm hoạt động tốt
3. Cache biến thể truy vấn giúp tăng tốc độ xử lý

## Công việc tiếp theo

1. Cải thiện thêm việc phát hiện ngôn ngữ
2. Thêm hỗ trợ cho các ngôn ngữ khác
3. Tối ưu hóa thuật toán gộp từ liên quan
4. Tích hợp với các mô hình embedding tiếng Việt tốt hơn
5. Thêm cơ chế đánh giá chất lượng biến thể truy vấn
