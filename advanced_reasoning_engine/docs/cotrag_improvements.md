# CoTRAG Improvements Documentation

## Overview

This document describes the improvements made to the CoTRAG (Chain of Thought + Retrieval Augmented Generation) system in the Deep Research Core. These improvements address several key areas:

1. **Handling irrelevant documents** - Improved strategies for cases when RAG returns low-relevance documents
2. **Adaptive learning** - Learning from user feedback to optimize weights between CoT and RAG
3. **Advanced strategies** - Query expansion, iterative retrieval, and fallback mechanisms
4. **Error analysis** - Detailed analysis of errors and suggestions for improvement
5. **Vietnamese language support** - Enhanced support for Vietnamese language queries

## CoTRAG Adaptive Learning

The `CoTRAGAdaptiveLearning` class extends CoTRAG to add adaptive learning capabilities, allowing the system to learn from user feedback and automatically adjust weights between CoT and RAG based on performance.

### Key Features

- **Feedback Collection**: Collects and stores user feedback on query results
- **Weight Adjustment**: Automatically adjusts weights between CoT and RAG based on feedback
- **Similar Query Learning**: Applies learning from similar past queries to new queries
- **Persistent Storage**: Saves feedback history to disk for long-term learning

### Usage Example

```python
from src.deep_research_core.reasoning.cotrag_adaptive_learning import CoTRAGAdaptiveLearning

# Initialize with adaptive learning
cotrag = CoTRAGAdaptiveLearning(
    provider="openai",
    model="gpt-4o",
    feedback_history_path="./feedback_history.json",
    learning_rate=0.05
)

# Process a query
result = cotrag.process("AI là gì?")

# Add feedback
cotrag.add_feedback(
    query="AI là gì?",
    result=result,
    feedback_score=0.9,  # 0.0 (poor) to 1.0 (excellent)
    feedback_type="user",
    feedback_notes="Câu trả lời rất chi tiết và chính xác"
)
```

### How It Works

1. **Feedback Collection**: When users provide feedback on responses, the system stores this feedback along with the query, weights used, and other metadata.

2. **Immediate Learning**: When feedback is received, the system immediately adjusts weights based on the feedback score:
   - High scores (>0.5) reinforce the current weights
   - Low scores (<0.5) adjust weights away from current values

3. **Similar Query Learning**: When processing a new query, the system finds similar queries in the feedback history and adjusts weights based on what worked well for those queries.

4. **Weight Optimization**: The system maintains optimal weights for different query types and complexity levels, continuously refining these based on feedback.

## CoTRAG Advanced Strategies

The `CoTRAGAdvancedStrategies` class extends CoTRAG to add advanced strategies for handling special cases, particularly when RAG returns irrelevant documents or when errors occur.

### Key Features

- **Query Expansion**: Expands queries into multiple variations to improve retrieval
- **Iterative Retrieval**: Refines queries and retrieves documents iteratively to improve relevance
- **Irrelevant Document Handling**: Detects and handles cases when RAG returns irrelevant documents
- **Fallback Strategies**: Provides multiple fallback mechanisms when errors occur

### Usage Example

```python
from src.deep_research_core.reasoning.cotrag_advanced_strategies import CoTRAGAdvancedStrategies

# Initialize with advanced strategies
cotrag = CoTRAGAdvancedStrategies(
    provider="openai",
    model="gpt-4o",
    handle_irrelevant_docs=True,
    relevance_threshold=0.5,
    use_query_expansion=True,
    use_iterative_retrieval=True,
    max_retrieval_iterations=3,
    use_fallback_strategies=True
)

# Process a query
result = cotrag.process("AI là gì?")

# Check if documents were relevant
if result.get("low_relevance_documents", False):
    print("Retrieved documents had low relevance")

# Check which advanced strategies were used
print(f"Advanced strategies used: {result['advanced_strategies']}")
```

### How It Works

1. **Query Expansion**: The system expands the original query into multiple variations to capture different aspects and phrasings, improving retrieval coverage.

2. **Iterative Retrieval**:
   - If initial documents have low relevance scores, the system refines the query based on the initial results
   - It then retrieves new documents with the refined query
   - This process repeats until relevant documents are found or the maximum iterations are reached

3. **Irrelevant Document Handling**:
   - Documents with relevance scores below the threshold are flagged as irrelevant
   - When irrelevant documents are detected, the system adjusts weights to rely more on CoT
   - A special system prompt is used that instructs the model to rely more on its knowledge

4. **Fallback Strategies**:
   - If an error occurs during processing, the system tries multiple fallback approaches:
     1. Pure CoT (no RAG)
     2. Different providers (e.g., switching from OpenAI to Anthropic)
     3. Simplified processing with reduced complexity

## CoTRAG Error Analysis

The enhanced `CoTRAGErrorAnalyzer` provides detailed analysis of errors in CoTRAG responses, helping to identify and address issues.

### Key Features

- **Document Analysis**: Analyzes the relevance and quality of retrieved documents
- **Reasoning Analysis**: Evaluates the quality and correctness of the reasoning steps
- **Answer Analysis**: Assesses the accuracy and completeness of the final answer
- **Error Classification**: Classifies errors into different categories
- **Improvement Suggestions**: Provides specific suggestions for improving responses
- **Vietnamese Language Support**: Special handling for Vietnamese language queries

### Usage Example

```python
from src.deep_research_core.reasoning.cotrag_error_analyzer import CoTRAGErrorAnalyzer

# Initialize the error analyzer
analyzer = CoTRAGErrorAnalyzer(
    provider="openai",
    model="gpt-4o",
    error_history_path="./error_history.json",
    learn_from_errors=True,
    detailed_analysis=True,
    vietnamese_support=True
)

# Analyze a response
analysis = analyzer.analyze(
    query="AI là gì?",
    documents=[...],  # Retrieved documents
    reasoning="...",  # Reasoning steps
    answer="...",     # Final answer
    expected_answer="..."  # Optional expected answer
)

# Print analysis results
print(f"Error source: {analysis['error_source']}")
print(f"Suggestions: {analysis['suggestions']}")
print(f"Detailed analysis: {analysis['detailed_analysis']}")
```

### How It Works

1. **Document Analysis**: The analyzer evaluates the relevance, coverage, and quality of the retrieved documents relative to the query.

2. **Reasoning Analysis**: It assesses the reasoning steps for logical coherence, factual accuracy, and completeness.

3. **Answer Analysis**: It evaluates the final answer for accuracy, completeness, and alignment with both the reasoning and the query.

4. **Error Classification**: Errors are classified into categories such as:
   - Retrieval errors (irrelevant documents)
   - Reasoning errors (logical flaws)
   - Integration errors (failure to combine CoT and RAG effectively)
   - Language-specific errors (e.g., Vietnamese diacritics handling)

5. **Improvement Suggestions**: Based on the analysis, specific suggestions are provided for improving the response, such as:
   - Adjusting weights between CoT and RAG
   - Refining the query
   - Improving document retrieval
   - Enhancing reasoning steps

6. **Learning from Errors**: The analyzer can track error patterns over time and learn to prevent similar errors in the future.

## Vietnamese Language Support

All improvements include enhanced support for Vietnamese language queries, addressing specific challenges in Vietnamese NLP.

### Key Features

- **Diacritics Handling**: Proper handling of Vietnamese diacritics in queries and responses
- **Language Detection**: Automatic detection of Vietnamese language queries
- **Vietnamese-Specific Analysis**: Special analysis for Vietnamese language responses
- **Integration with Vietnamese Embeddings**: Support for Vietnamese-specific embedding models

### Vietnamese-Specific Considerations

1. **Query Understanding**: Enhanced understanding of Vietnamese queries, including handling of diacritics, regional variations, and colloquialisms.

2. **Document Relevance**: Special handling for assessing the relevance of Vietnamese documents, considering language-specific features.

3. **Response Generation**: Ensuring natural, fluent Vietnamese responses with proper grammar, diacritics, and style.

4. **Error Analysis**: Vietnamese-specific error analysis, including issues related to diacritics, word segmentation, and regional variations.

## Testing and Evaluation

Comprehensive test suites have been developed for all improvements:

- **Unit Tests**: Testing individual components and features
- **Integration Tests**: Testing how components work together
- **Edge Case Tests**: Testing handling of special cases and error conditions
- **Vietnamese Language Tests**: Specific tests for Vietnamese language support

## Future Improvements

Potential areas for future improvement include:

1. **Reinforcement Learning**: Implementing more sophisticated reinforcement learning techniques for weight optimization

2. **Multi-Modal Support**: Extending CoTRAG to handle multi-modal queries (text, images, audio)

3. **Domain-Specific Optimization**: Creating specialized versions for domains like medical, legal, or technical content

4. **Performance Optimization**: Improving speed and efficiency, especially for complex queries

5. **Enhanced Vietnamese Support**: Integrating with more Vietnamese-specific models and resources
