# Comprehensive Monitoring and Observability Guide

This guide provides detailed information on using the monitoring and observability features in Deep Research Core to track performance, detect issues, and improve the reliability of your system.

## Table of Contents

1. [Introduction](#1-introduction)
2. [Structured Logging](#2-structured-logging)
3. [Performance Metrics](#3-performance-metrics)
4. [Distributed Tracing](#4-distributed-tracing)
5. [Alerting System](#5-alerting-system)
6. [External Monitoring Integration](#6-external-monitoring-integration)
7. [Best Practices](#7-best-practices)
8. [Troubleshooting](#8-troubleshooting)
9. [Examples](#9-examples)

## 1. Introduction

Monitoring and observability are critical aspects of any production system. Deep Research Core provides a comprehensive suite of tools to help you monitor your system's performance, detect issues, and gain insights into its behavior.

The monitoring and observability features in Deep Research Core include:

- **Structured Logging**: Consistent, machine-readable logs with rich context
- **Performance Metrics**: Detailed metrics for latency, throughput, and resource usage
- **Distributed Tracing**: End-to-end visibility into request processing
- **Alerting System**: Proactive notifications for performance issues
- **External Integration**: Seamless integration with popular monitoring tools

These features are designed to work together to provide a complete picture of your system's health and performance.

## 2. Structured Logging

Structured logging provides a consistent, machine-readable format for logs, making them easier to search, filter, and analyze.

### Basic Usage

```python
from deep_research_core.utils.structured_logging import get_logger

# Create a logger
logger = get_logger(
    name="my_module",
    level="INFO",
    json_format=True,
    include_stack_info=False,
    log_file="logs/my_module.log",
    log_to_console=True
)

# Log basic information
logger.info("Processing query", extra={"query": "What is RAG?"})

# Log with context
logger.set_context({"user_id": "123", "session_id": "abc"})
logger.info("User action")

# Log errors with exception
try:
    # Code that might raise an exception
    result = 1 / 0
except Exception as e:
    logger.exception("Error occurred", extra={"operation": "division"})
```

### Advanced Features

#### Log Rotation

```python
logger = get_logger(
    name="my_module",
    level="INFO",
    log_file="logs/my_module.log",
    rotation_type="size",  # "size" or "time"
    max_bytes=10485760,    # 10 MB
    backup_count=5,        # Keep 5 backup files
    when="D"               # Rotate daily (for time-based rotation)
)
```

#### Filtering

```python
logger = get_logger(
    name="my_module",
    level="INFO",
    filters=["sqlalchemy", "urllib3"]  # Filter out logs from these modules
)
```

#### Custom Formatters

```python
from deep_research_core.utils.structured_logging import JSONFormatter

# Create a custom formatter
formatter = JSONFormatter(
    include_stack_info=True,
    additional_fields={"app_name": "my_app", "environment": "production"}
)

# Use the formatter with a logger
logger = get_logger(
    name="my_module",
    level="INFO",
    formatter=formatter
)
```

### Benefits

- **Consistent Structure**: All logs have the same format, making them easier to analyze
- **Rich Metadata**: Add context and additional information to logs
- **JSON Support**: Easy integration with log analysis tools like ELK Stack
- **Correlation**: Trace requests across different components
- **Performance**: Minimal overhead for logging operations

## 3. Performance Metrics

Performance metrics provide quantitative measurements of your system's behavior, helping you identify bottlenecks, track improvements, and set baselines.

### Basic Usage

```python
from deep_research_core.utils.performance_metrics import (
    measure_latency, measure_block_latency,
    start_metrics_collection, stop_metrics_collection,
    get_metrics_snapshot, get_metrics
)

# Start collecting metrics
start_metrics_collection()

# Measure function latency
@measure_latency("search_documents")
def search_documents(query):
    # Document search code
    time.sleep(0.5)
    return ["doc1", "doc2"]

# Measure code block latency
def process_query(query):
    with measure_block_latency("vector_search"):
        # Vector search code
        time.sleep(0.3)
    
    with measure_block_latency("generate_response"):
        # Response generation code
        time.sleep(0.7)

# Get a snapshot of all metrics
metrics = get_metrics_snapshot()
print(json.dumps(metrics, indent=2))

# Get all metrics in a flattened format
all_metrics = get_metrics()
print(json.dumps(all_metrics, indent=2))

# Stop collecting metrics when no longer needed
stop_metrics_collection()
```

### Metric Types

#### Latency Metrics

Latency metrics measure the time it takes to perform operations.

```python
# Latency metrics include:
# - count: Number of measurements
# - min: Minimum latency
# - max: Maximum latency
# - avg: Average latency
# - p50_ms: 50th percentile (median) in milliseconds
# - p95_ms: 95th percentile in milliseconds
# - p99_ms: 99th percentile in milliseconds

# Example latency metrics
{
    "latency.search_documents": {
        "count": 100,
        "min": 0.1,
        "max": 1.5,
        "avg": 0.5,
        "p50_ms": 450,
        "p95_ms": 950,
        "p99_ms": 1200
    }
}
```

#### Throughput Metrics

Throughput metrics measure the rate at which operations are performed.

```python
# Throughput metrics include:
# - count: Total number of operations
# - rate_1m: Operations per minute
# - rate_5m: Operations per 5 minutes
# - rate_15m: Operations per 15 minutes

# Example throughput metrics
{
    "throughput.search_documents": {
        "count": 1000,
        "rate_1m": 60,
        "rate_5m": 300,
        "rate_15m": 900
    }
}
```

#### Memory Metrics

Memory metrics measure resource usage.

```python
# Memory metrics include:
# - process_rss_mb: Resident set size in MB
# - process_vms_mb: Virtual memory size in MB
# - system_used_percent: System memory usage percentage
# - gpu_used_mb: GPU memory usage in MB (if available)

# Example memory metrics
{
    "memory.process_rss_mb": {
        "latest": 256,
        "min": 128,
        "max": 512,
        "avg": 256
    },
    "memory.system_used_percent": {
        "latest": 75,
        "min": 50,
        "max": 90,
        "avg": 70
    }
}
```

### Custom Metrics

You can define custom metrics for application-specific measurements.

```python
from deep_research_core.utils.performance_metrics import set_custom_metric

# Set a custom metric
set_custom_metric("document_count", 1000)
set_custom_metric("embedding_dimension", 1536)
set_custom_metric("model_version", "v2.0")

# Custom metrics are included in the metrics snapshot
metrics = get_metrics_snapshot()
print(metrics["custom"]["document_count"])  # 1000
```

### Metrics Configuration

You can configure metrics collection through the `monitoring_config.py` file:

```python
# In config/monitoring_config.py
METRICS_CONFIG = {
    "enabled": True,
    "report_interval": 60,  # Report metrics every 60 seconds
    "memory_measure_interval": 10,  # Measure memory every 10 seconds
    "latency_thresholds": {
        "search_documents": 1000,  # 1000ms = 1s
        "process_query": 5000,  # 5000ms = 5s
    },
    "throughput_thresholds": {
        "search_documents": 10,  # At least 10 searches per minute
        "process_query": 1  # At least 1 query per minute
    },
    "memory_thresholds": {
        "process_rss_mb": 1024,  # 1GB
        "system_used_percent": 90  # 90% system memory usage
    }
}
```

## 4. Distributed Tracing

Distributed tracing provides end-to-end visibility into request processing across different components of your system.

### Basic Usage

```python
from deep_research_core.utils.distributed_tracing import (
    configure_tracing, trace_function, span
)

# Configure tracing
configure_tracing(service_name="deep-research-core")

# Create a trace for a request
@trace_function(name="process_query")
def process_query(query):
    # Parse the query
    with span("parse_query"):
        parsed_query = parse_query(query)
    
    # Search for documents
    with span("search_documents"):
        documents = search_documents(parsed_query)
    
    # Generate a response
    with span("generate_response"):
        response = generate_response(parsed_query, documents)
    
    return response

# Trace a function with attributes
@trace_function(name="index_document", attributes={"priority": "high"})
def index_document(doc):
    # Document indexing code
    pass
```

### Advanced Features

#### Manual Span Management

```python
from deep_research_core.utils.distributed_tracing import start_trace, end_trace

# Create a trace manually
def complex_operation():
    trace = start_trace("complex_operation")
    try:
        # Complex code
        pass
    finally:
        end_trace(trace)
```

#### OpenTelemetry Integration

```python
# Configure tracing with OpenTelemetry
configure_tracing(
    service_name="deep-research-core",
    use_opentelemetry=True,
    otlp_endpoint="http://jaeger:4317"
)
```

#### Baggage and Context Propagation

```python
from deep_research_core.utils.distributed_tracing import set_baggage, get_baggage

# Set baggage items
set_baggage("user_id", "123")
set_baggage("request_id", "abc")

# Get baggage items
user_id = get_baggage("user_id")
```

### Trace Structure

A trace consists of spans, which represent operations or units of work. Each span has:

- **Name**: The name of the operation
- **Start Time**: When the operation started
- **End Time**: When the operation completed
- **Attributes**: Key-value pairs with additional information
- **Events**: Time-stamped events within the span
- **Links**: References to other spans
- **Status**: Success, error, or unset

### Benefits

- **End-to-End Visibility**: Track requests across different components
- **Performance Analysis**: Identify bottlenecks and slow operations
- **Dependency Analysis**: Understand relationships between components
- **Error Correlation**: Connect errors across different services
- **OpenTelemetry Compatibility**: Integration with popular tracing tools

## 5. Alerting System

The alerting system helps you detect and notify about performance issues before they impact users.

### Basic Usage

```python
from deep_research_core.utils.alerting import (
    AlertSeverity, add_threshold_alert,
    add_percentage_change_alert, start_alerting, stop_alerting,
    add_email_notifier, add_slack_notifier
)

# Add notifiers
add_email_notifier(
    recipients=["<EMAIL>"],
    smtp_server="smtp.gmail.com",
    smtp_port=587,
    smtp_username="<EMAIL>",
    smtp_password="your-password",
    from_address="<EMAIL>"
)

add_slack_notifier(
    webhook_url="https://hooks.slack.com/services/XXX/YYY/ZZZ",
    channel="#alerts",
    username="Alert Bot"
)

# Add threshold-based alert
add_threshold_alert(
    name="high_search_latency",
    description="Search latency is too high",
    metric_path="latency.search_documents.p95_ms",
    threshold=1000,  # 1000ms = 1s
    comparison=">",
    severity=AlertSeverity.WARNING
)

# Add percentage change alert
add_percentage_change_alert(
    name="throughput_drop",
    description="Search throughput has dropped significantly",
    metric_path="throughput.search_documents.rate_1m",
    percentage=50,
    window_minutes=5,
    severity=AlertSeverity.ERROR
)

# Start the alerting system
start_alerting()

# Stop the alerting system when no longer needed
stop_alerting()
```

### Alert Types

#### Threshold Alert

Triggers when a metric exceeds a threshold.

```python
add_threshold_alert(
    name="high_memory_usage",
    description="Memory usage is too high",
    metric_path="memory.process_rss_mb.latest",
    threshold=1024,  # 1GB
    comparison=">",
    severity=AlertSeverity.WARNING
)
```

#### Percentage Change Alert

Triggers when a metric changes by a certain percentage.

```python
add_percentage_change_alert(
    name="latency_spike",
    description="Latency has increased significantly",
    metric_path="latency.process_query.p95_ms",
    percentage=30,
    window_minutes=10,
    severity=AlertSeverity.ERROR
)
```

#### Composite Alert

Combines multiple alerts with logical operators.

```python
from deep_research_core.utils.alerting import CompositeAlert, AlertOperator

# Create individual alerts
high_latency = ThresholdAlert(
    name="high_latency",
    description="High latency",
    metric_path="latency.process_query.p95_ms",
    threshold=5000,
    comparison=">",
    severity=AlertSeverity.WARNING
)

low_throughput = ThresholdAlert(
    name="low_throughput",
    description="Low throughput",
    metric_path="throughput.process_query.rate_1m",
    threshold=1,
    comparison="<",
    severity=AlertSeverity.WARNING
)

# Create a composite alert
add_composite_alert(
    name="performance_degradation",
    description="Performance degradation detected",
    alerts=[high_latency, low_throughput],
    operator=AlertOperator.AND,
    severity=AlertSeverity.ERROR
)
```

### Notification Channels

#### Email Notifier

Sends alert notifications via email.

```python
add_email_notifier(
    recipients=["<EMAIL>", "<EMAIL>"],
    smtp_server="smtp.gmail.com",
    smtp_port=587,
    smtp_username="<EMAIL>",
    smtp_password="your-password",
    from_address="<EMAIL>",
    use_ssl=False,
    use_tls=True
)
```

#### Slack Notifier

Sends alert notifications to a Slack channel.

```python
add_slack_notifier(
    webhook_url="https://hooks.slack.com/services/XXX/YYY/ZZZ",
    channel="#alerts",
    username="Alert Bot",
    icon_emoji=":warning:"
)
```

#### Custom Notifier

Create custom notifiers for other notification channels.

```python
from deep_research_core.utils.alerting import Notifier

class CustomNotifier(Notifier):
    def __init__(self, api_key):
        self.api_key = api_key
    
    def notify(self, alert_name, alert_description, message, severity):
        # Custom notification logic
        pass

# Add the custom notifier
custom_notifier = CustomNotifier(api_key="your-api-key")
add_notifier(custom_notifier)
```

### Alert Configuration

You can configure the alerting system through the `monitoring_config.py` file:

```python
# In config/monitoring_config.py
ALERTING_CONFIG = {
    "enabled": True,
    "check_interval": 60,  # Check alerts every 60 seconds
    "alerts": [
        {
            "name": "high_search_latency",
            "description": "Search latency is too high",
            "metric_path": "latency.search_documents.p95_ms",
            "threshold": 1000,  # 1000ms = 1s
            "comparison": ">",
            "severity": "warning"
        },
        {
            "name": "high_memory_usage",
            "description": "Memory usage is too high",
            "metric_path": "memory.process_rss_mb.latest",
            "threshold": 1024,  # 1GB
            "comparison": ">",
            "severity": "error"
        }
    ],
    "notifiers": [
        {
            "type": "email",
            "recipients": ["<EMAIL>"],
            "smtp_server": "smtp.gmail.com",
            "smtp_port": 587,
            "smtp_username": "<EMAIL>",
            "smtp_password": "your-password",
            "from_address": "<EMAIL>"
        },
        {
            "type": "slack",
            "webhook_url": "https://hooks.slack.com/services/XXX/YYY/ZZZ",
            "channel": "#alerts",
            "username": "Alert Bot"
        }
    ]
}
```

## 6. External Monitoring Integration

Deep Research Core integrates with popular external monitoring tools to provide a comprehensive monitoring solution.

### Prometheus Integration

```python
from deep_research_core.utils.external_monitoring import get_prometheus_exporter

# Initialize Prometheus exporter
exporter = get_prometheus_exporter(port=8000)
exporter.start()

# Record metrics
exporter.record_request("search", "success")
exporter.record_document_operation("add", "SQLiteVectorRAG", 10)
exporter.record_error("search", "timeout")
exporter.record_document_count("SQLiteVectorRAG", 1000)
exporter.record_latency("search", 0.1)
exporter.record_query("SQLiteVectorRAG", "search", 0.1)

# Stop exporter when done
exporter.stop()
```

### Grafana Dashboards

```python
from deep_research_core.utils.external_monitoring import (
    generate_default_dashboard,
    generate_rag_comparison_dashboard,
    generate_all_dashboards
)

# Generate default dashboard
generate_default_dashboard("dashboards/default_dashboard.json")

# Generate RAG comparison dashboard
generate_rag_comparison_dashboard("dashboards/rag_comparison_dashboard.json")

# Generate all dashboards
generate_all_dashboards("dashboards")
```

### ELK Stack Integration

```python
# Configure structured logging to write JSON logs
logger = get_logger(
    name="my_module",
    level="INFO",
    json_format=True,
    log_file="/var/log/deep_research_core/app.log"
)

# Configure Filebeat to collect logs and send to Elasticsearch
# See filebeat.yml in the docs/examples directory
```

### OpenTelemetry Integration

```python
# Configure tracing with OpenTelemetry
configure_tracing(
    service_name="deep-research-core",
    use_opentelemetry=True,
    otlp_endpoint="http://jaeger:4317"
)
```

## 7. Best Practices

### Logging Best Practices

1. **Use Structured Logging**: Always use structured logging for machine-readable logs
2. **Include Context**: Add relevant context to logs (user ID, request ID, etc.)
3. **Log Levels**: Use appropriate log levels (DEBUG, INFO, WARNING, ERROR, CRITICAL)
4. **Sensitive Data**: Avoid logging sensitive data (passwords, API keys, etc.)
5. **Performance**: Be mindful of logging performance impact in high-throughput systems

### Metrics Best Practices

1. **Measure What Matters**: Focus on metrics that provide actionable insights
2. **Consistent Naming**: Use consistent naming conventions for metrics
3. **Granularity**: Choose appropriate granularity for metrics collection
4. **Aggregation**: Consider how metrics will be aggregated and analyzed
5. **Baselines**: Establish performance baselines for comparison

### Tracing Best Practices

1. **Span Naming**: Use clear, descriptive names for spans
2. **Attributes**: Add relevant attributes to spans for context
3. **Error Handling**: Mark spans as errors when exceptions occur
4. **Sampling**: Use appropriate sampling rates for high-volume systems
5. **Context Propagation**: Ensure trace context is propagated across components

### Alerting Best Practices

1. **Alert Fatigue**: Avoid too many alerts that could lead to alert fatigue
2. **Actionable Alerts**: Ensure alerts are actionable and provide clear next steps
3. **Severity Levels**: Use appropriate severity levels for different types of issues
4. **Thresholds**: Set realistic thresholds based on historical data
5. **Notification Channels**: Use appropriate notification channels for different severity levels

## 8. Troubleshooting

### Common Issues

#### High Log Volume

If you're generating too many logs:

1. Increase the log level (e.g., from DEBUG to INFO)
2. Use more selective logging (log only important events)
3. Implement log sampling for high-volume events

#### Metric Collection Performance Impact

If metric collection is impacting performance:

1. Reduce the metrics collection interval
2. Use sampling for high-frequency metrics
3. Disable metrics collection for non-critical components

#### Tracing Overhead

If tracing is causing performance issues:

1. Reduce the sampling rate
2. Use more selective tracing (trace only important operations)
3. Optimize span creation and attribute setting

#### False Alerts

If you're getting too many false alerts:

1. Adjust alert thresholds based on historical data
2. Implement more sophisticated alerting logic
3. Add debouncing or hysteresis to alerts

### Debugging Tools

#### Log Analysis

```python
# Enable debug logging for troubleshooting
logger = get_logger(
    name="my_module",
    level="DEBUG",
    include_stack_info=True
)
```

#### Metrics Inspection

```python
# Get detailed metrics for debugging
metrics = get_metrics_snapshot()
print(json.dumps(metrics, indent=2))
```

#### Trace Inspection

```python
# Enable debug mode for tracing
configure_tracing(
    service_name="deep-research-core",
    debug=True
)
```

## 9. Examples

### Complete Monitoring Example

See the file `examples/monitoring_example.py` for a complete example of using all monitoring and alerting features in a real application.

```python
# Run the example
python examples/monitoring_example.py --queries 20 --interval 0.5
```

### Vector Store Monitoring Example

```python
from deep_research_core.reasoning import SQLiteVectorRAG
from deep_research_core.utils.structured_logging import get_logger
from deep_research_core.utils.performance_metrics import start_metrics_collection
from deep_research_core.utils.distributed_tracing import configure_tracing
from deep_research_core.utils.alerting import (
    add_threshold_alert, AlertSeverity, start_alerting
)

# Initialize logging, metrics, tracing, and alerting
logger = get_logger("vector_store_example")
start_metrics_collection()
configure_tracing(service_name="vector-store-example")
add_threshold_alert(
    name="high_search_latency",
    description="Search latency is too high",
    metric_path="latency.sqlite_vector_rag_search.p95_ms",
    threshold=1000,
    comparison=">",
    severity=AlertSeverity.WARNING
)
start_alerting()

# Initialize SQLiteVectorRAG
rag = SQLiteVectorRAG(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    embedding_model="all-MiniLM-L6-v2",
    db_path="my_documents.db"
)

# Add documents
documents = [
    {
        "content": "This is a test document.",
        "source": "Test Source",
        "title": "Test Document"
    },
    {
        "content": "This is another test document.",
        "source": "Test Source 2",
        "title": "Test Document 2"
    }
]
doc_ids = rag.add_documents(documents)
logger.info("Added documents", extra={"count": len(doc_ids)})

# Process a query
response = rag.process("What is RAG?")
logger.info("Processed query", extra={"query": "What is RAG?"})
print(response["answer"])
```

### CoT-RAG Monitoring Example

```python
from deep_research_core.reasoning import EnhancedCoTRAG, SQLiteVectorRAG
from deep_research_core.utils.structured_logging import get_logger
from deep_research_core.utils.performance_metrics import start_metrics_collection
from deep_research_core.utils.distributed_tracing import configure_tracing

# Initialize logging, metrics, and tracing
logger = get_logger("cot_rag_example")
start_metrics_collection()
configure_tracing(service_name="cot-rag-example")

# Initialize SQLiteVectorRAG
rag = SQLiteVectorRAG(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    embedding_model="all-MiniLM-L6-v2",
    db_path="my_documents.db"
)

# Add documents
documents = [
    {
        "content": "This is a test document.",
        "source": "Test Source",
        "title": "Test Document"
    },
    {
        "content": "This is another test document.",
        "source": "Test Source 2",
        "title": "Test Document 2"
    }
]
rag.add_documents(documents)

# Initialize EnhancedCoTRAG
cot_rag = EnhancedCoTRAG(
    rag_instance=rag,
    language="en",
    use_hybrid_search=True,
    use_reranking=True,
    use_query_expansion=True,
    verbose=True
)

# Process a query
result = cot_rag.process(
    query="What is the difference between RAG and traditional search?",
    callback=lambda content: print(content, end="", flush=True)
)

# Log metrics
logger.info("CoT-RAG processing complete", extra={
    "latency": result["latency"],
    "enhanced_features": result["enhanced_features"]
})
```

## Conclusion

The monitoring and observability features in Deep Research Core provide a powerful toolkit for tracking, analyzing, and improving the performance of your system. By combining structured logging, performance metrics, distributed tracing, and alerting, you can:

1. **Gain deeper insights** into how your system operates
2. **Detect issues early** before they impact users
3. **Analyze bottlenecks** in detail
4. **Receive notifications** when problems occur
5. **Integrate easily** with popular monitoring tools

Incorporate these features into your application to build a more robust and reliable system.
