# Hướng dẫn sử dụng Enhanced Chain of Thought

## Giới thiệu

Enhanced Chain of Thought (EnhancedCoT) là một phiên bản nâng cao của Chain of Thought (CoT), cung cấp các tính năng bổ sung như:

1. **Tự động điều chỉnh số bước suy luận** dựa trên độ phức tạp của câu hỏi
2. **Đánh giá chất lượng suy luận** để đảm bảo kết quả chính xác và rõ ràng
3. **Tối ưu hóa hiệu suất** thông qua caching và xử lý song song
4. **Hỗ trợ đa ngôn ngữ** với tối ưu hóa đặc biệt cho tiếng Việt

EnhancedCoT đặc biệt hữu ích cho các tình huống yêu cầu suy luận phức tạp, gi<PERSON><PERSON> thích từng bướ<PERSON>, và độ chính xác cao.

## Cài đặt

EnhancedCoT được tích hợp sẵn trong Deep Research Core. Không cần cài đặt thêm.

## Sử dụng cơ bản

```python
from deep_research_core.reasoning import EnhancedChainOfThought

# Khởi tạo EnhancedChainOfThought
cot = EnhancedChainOfThought(
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free",
    temperature=0.7,
    max_tokens=2000,
    language="en",
    verbose=True
)

# Thực hiện suy luận
result = cot.reason(
    query="Explain why the sky appears blue during the day.",
    callback=lambda content: print(content, end="", flush=True)
)

# In kết quả
print(f"\nSố bước suy luận: {result['step_count']}")
print(f"Câu trả lời cuối cùng: {result['answer']}")
```

## Tính năng nâng cao

### 1. Tự động điều chỉnh số bước suy luận

EnhancedCoT có thể tự động điều chỉnh số bước suy luận dựa trên độ phức tạp của câu hỏi:

```python
cot = EnhancedChainOfThought(
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free",
    min_steps=2,  # Số bước tối thiểu
    max_steps=8,  # Số bước tối đa
    adaptive_steps=True,  # Bật tính năng tự động điều chỉnh
    verbose=True
)

# Câu hỏi đơn giản - sẽ sử dụng ít bước hơn
simple_result = cot.reason("What causes rainbows to form?")

# Câu hỏi phức tạp - sẽ sử dụng nhiều bước hơn
complex_result = cot.reason(
    "Analyze the potential long-term environmental and economic impacts of transitioning from fossil fuels to renewable energy sources, considering both developed and developing nations."
)

print(f"Số bước cho câu hỏi đơn giản: {simple_result['step_count']}")
print(f"Số bước cho câu hỏi phức tạp: {complex_result['step_count']}")
```

### 2. Đánh giá chất lượng suy luận

EnhancedCoT có thể đánh giá chất lượng của quá trình suy luận:

```python
cot = EnhancedChainOfThought(
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free",
    evaluate_quality=True,  # Bật tính năng đánh giá chất lượng
    verbose=True
)

result = cot.reason("Explain the concept of quantum entanglement.")

# In các chỉ số chất lượng
print("\nChỉ số chất lượng suy luận:")
for metric, value in result['quality_metrics'].items():
    print(f"  {metric}: {value:.2f}")
```

Các chỉ số chất lượng bao gồm:
- `step_clarity`: Độ rõ ràng của từng bước suy luận
- `logical_flow`: Luồng logic giữa các bước
- `evidence_usage`: Mức độ sử dụng bằng chứng và dữ liệu
- `conclusion_strength`: Độ mạnh của kết luận
- `overall_quality`: Chất lượng tổng thể

### 3. Tối ưu hóa hiệu suất

EnhancedCoT hỗ trợ caching và xử lý song song để cải thiện hiệu suất:

```python
cot = EnhancedChainOfThought(
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free",
    use_cache=True,  # Bật tính năng caching
    cache_ttl=3600,  # Thời gian cache (giây)
    parallel_evaluation=True,  # Bật tính năng xử lý song song
    max_workers=3,  # Số lượng worker tối đa
    verbose=True
)

# Xử lý hàng loạt
queries = [
    "What is photosynthesis?",
    "How do airplanes fly?",
    "Explain the water cycle.",
    "How does a refrigerator work?"
]

results = cot.batch_reason(queries=queries)
```

### 4. Hỗ trợ tiếng Việt

EnhancedCoT có hỗ trợ đặc biệt cho tiếng Việt:

```python
cot = EnhancedChainOfThought(
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free",
    language="vi",  # Sử dụng tiếng Việt
    verbose=True
)

result = cot.reason(
    query="Giải thích tại sao biến đổi khí hậu là một vấn đề toàn cầu và các giải pháp có thể áp dụng ở Việt Nam."
)

print(f"\nSố bước suy luận: {result['step_count']}")
print(f"Câu trả lời cuối cùng: {result['answer']}")
```

## Tham số cấu hình

| Tham số | Mô tả | Giá trị mặc định |
|---------|-------|-----------------|
| `provider` | Nhà cung cấp API ("openai", "anthropic", "openrouter") | "openai" |
| `model` | Mô hình sử dụng | Tùy thuộc vào provider |
| `temperature` | Nhiệt độ sampling (0.0 - 1.0) | 0.7 |
| `max_tokens` | Số token tối đa | 2000 |
| `language` | Ngôn ngữ sử dụng ("en", "vi") | "en" |
| `min_steps` | Số bước suy luận tối thiểu | 2 |
| `max_steps` | Số bước suy luận tối đa | 8 |
| `adaptive_steps` | Tự động điều chỉnh số bước | True |
| `use_cache` | Sử dụng caching | True |
| `cache_ttl` | Thời gian cache (giây) | 3600 |
| `evaluate_quality` | Đánh giá chất lượng suy luận | True |
| `parallel_evaluation` | Xử lý song song | False |
| `max_workers` | Số lượng worker tối đa | 3 |
| `verbose` | In thông tin chi tiết | False |

## Đánh giá chất lượng suy luận

Để đánh giá chất lượng suy luận một cách chi tiết hơn, bạn có thể sử dụng `CoTEvaluator`:

```python
from deep_research_core.evaluation.cot_evaluator import CoTEvaluator

# Khởi tạo evaluator
evaluator = CoTEvaluator(
    provider="openrouter",
    model="moonshotai/moonlight-16b-a3b-instruct:free",
    use_model_evaluation=True,
    verbose=True
)

# Đánh giá quá trình suy luận
evaluation = evaluator.evaluate(result['reasoning'])

print("\nKết quả đánh giá:")
print(f"Số bước suy luận: {evaluation['step_count']}")
for metric, value in evaluation['metrics'].items():
    print(f"  {metric}: {value:.2f}")
```

## So sánh hai quá trình suy luận

Bạn cũng có thể so sánh chất lượng của hai quá trình suy luận khác nhau:

```python
# Thực hiện hai quá trình suy luận khác nhau
result_a = cot.reason("Explain quantum computing.")
result_b = cot.reason("Explain quantum computing.", max_steps=8)  # Với nhiều bước hơn

# So sánh chất lượng
comparison = evaluator.compare(result_a['reasoning'], result_b['reasoning'])

print("\nKết quả so sánh:")
print(f"Quá trình suy luận tốt hơn: {comparison['better']}")
print(f"Mức độ cải thiện: {comparison['improvement']:.2f} điểm")
```

## Kết luận

EnhancedChainOfThought cung cấp một cách tiếp cận mạnh mẽ và linh hoạt cho các tác vụ suy luận phức tạp. Bằng cách tự động điều chỉnh số bước suy luận, đánh giá chất lượng, và tối ưu hóa hiệu suất, EnhancedCoT giúp cải thiện cả độ chính xác và hiệu quả của quá trình suy luận.
