# Hướng dẫn sử dụng Direct Preference Optimization (DPO)

## Giới thiệu

Direct Preference Optimization (DPO) là một kỹ thuật tinh chỉnh mô hình ngôn ngữ dựa trên phản hồi và sở thích của người dùng. Thay vì sử dụng phản hồi gián tiếp thông qua mô hình phần thưởng như trong RLHF truyền thống, DPO tối ưu hóa mô hình trực tiếp từ dữ liệu sở thích cặp.

Triển khai DPO trong Deep Research Core hỗ trợ nhiều nhà cung cấp mô hình khác nhau, bao gồm OpenAI, Anthropic và OpenRouter. Module này cung cấp cả hai phương pháp: sử dụng phần thưởng ngầm và sử dụng phần thưởng rõ ràng.

## Cài đặt

Không cần cài đặt thêm, DPO đã được tích hợp sẵn trong Deep Research Core. Đảm bảo bạn đã cài đặt tất cả các gói phụ thuộc bằng cách chạy:

```bash
pip install -r requirements.txt
```

## Sử dụng cơ bản

### 1. Tạo dữ liệu sở thích

Đầu tiên, bạn cần chuẩn bị dữ liệu sở thích dưới dạng cặp (câu trả lời được chọn và câu trả lời bị từ chối):

```python
from deep_research_core.rl_tuning.dpo import PreferenceData

# Chuẩn bị dữ liệu sở thích
preference_data = PreferenceData(
    queries=["Làm thế nào để giải quyết vấn đề biến đổi khí hậu?", 
             "Giải thích về trí tuệ nhân tạo"],
    chosen_responses=["Để giải quyết vấn đề biến đổi khí hậu, chúng ta cần...", 
                      "Trí tuệ nhân tạo là..."],
    rejected_responses=["Biến đổi khí hậu không phải là vấn đề lớn...", 
                        "Trí tuệ nhân tạo chỉ là..."]
)

# Hoặc tải dữ liệu từ file
preference_data = PreferenceData.from_json("preference_data.json")
```

### 2. Khởi tạo và sử dụng DPO

```python
from deep_research_core.rl_tuning.dpo import AnthropicDPO, OpenAIDPO, OpenRouterDPO

# Sử dụng với Anthropic
dpo = AnthropicDPO(
    reference_model_name="claude-3-haiku", 
    policy_model_name="claude-3-sonnet",
    beta=0.1,
    max_length=1024,
    batch_size=4,
    learning_rate=1e-5,
    num_epochs=3
)

# Huấn luyện mô hình
train_metrics = dpo.train(preference_data)

# Sinh nội dung với mô hình đã tinh chỉnh
response = dpo.generate("Các giải pháp cho vấn đề ô nhiễm không khí là gì?")
print(response)
```

## Tùy chỉnh tham số

### Các tham số chính

- `reference_model_name`: Tên mô hình tham chiếu (thường là mô hình gốc)
- `policy_model_name`: Tên mô hình chính sách (mô hình sẽ được tinh chỉnh)
- `beta`: Hệ số cân bằng giữa đáp ứng sở thích và sự tương đồng với mô hình tham chiếu (0.1-0.5)
- `max_length`: Độ dài token tối đa cho mỗi mẫu
- `batch_size`: Kích thước batch trong quá trình huấn luyện
- `learning_rate`: Tốc độ học
- `num_epochs`: Số epoch huấn luyện
- `output_dir`: Thư mục lưu mô hình tinh chỉnh
- `device`: Thiết bị tính toán ("cpu" hoặc "cuda")

### Ví dụ với tùy chỉnh nâng cao

```python
from deep_research_core.rl_tuning.dpo import OpenRouterDPO

dpo = OpenRouterDPO(
    reference_model_name="anthropic/claude-3-opus",
    policy_model_name="anthropic/claude-3-sonnet",
    beta=0.2,
    max_length=2048,
    batch_size=8,
    learning_rate=5e-6,
    num_epochs=5,
    log_interval=10,
    save_interval=1,
    output_dir="./dpo_models",
    device="cuda",
    model_kwargs={
        "temperature": 0.7,
        "top_p": 0.9
    },
    optimizer_kwargs={
        "weight_decay": 0.01,
        "eps": 1e-8
    },
    verbose=True
)

# Huấn luyện với phần thưởng rõ ràng
train_metrics = dpo.train_with_explicit_rewards(
    preference_data,
    reward_fn=lambda x: compute_reward(x)  # Hàm tính phần thưởng tùy chỉnh
)
```

## Ví dụ

### Ví dụ 1: Tinh chỉnh mô hình cho trợ lý AI

```python
from deep_research_core.rl_tuning.dpo import OpenAIDPO, PreferenceData

# Tạo dữ liệu sở thích cho trợ lý
assistant_data = PreferenceData.from_json("assistant_preferences.json")

# Khởi tạo DPO
dpo = OpenAIDPO(
    reference_model_name="gpt-4o",
    policy_model_name="gpt-3.5-turbo",
    beta=0.1,
    max_length=1024,
    batch_size=16,
    learning_rate=1e-5,
    num_epochs=3
)

# Huấn luyện mô hình
metrics = dpo.train(assistant_data)

# Lưu mô hình đã tinh chỉnh
dpo.save_model("assistant_dpo_model")

# Sinh câu trả lời
response = dpo.generate("Hỗ trợ tôi lập kế hoạch cho chuyến du lịch đến Đà Nẵng")
print(response)
```

### Ví dụ 2: Sử dụng DPO với phần thưởng rõ ràng

```python
from deep_research_core.rl_tuning.dpo import AnthropicDPO
from deep_research_core.evaluation.metrics import compute_factuality_score

# Định nghĩa hàm phần thưởng
def factuality_reward(response, query):
    return compute_factuality_score(response, query)

# Dữ liệu sở thích
expert_data = PreferenceData.from_json("expert_preferences.json")

# Khởi tạo DPO
dpo = AnthropicDPO(
    reference_model_name="claude-3-haiku",
    policy_model_name="claude-3-sonnet",
    beta=0.2,
    max_length=2048
)

# Huấn luyện với phần thưởng rõ ràng
metrics = dpo.train_with_explicit_rewards(
    expert_data,
    reward_fn=factuality_reward
)
```

## FAQ

### DPO khác với RLHF như thế nào?

DPO và RLHF đều nhằm mục đích tinh chỉnh mô hình ngôn ngữ dựa trên phản hồi của con người, nhưng cách tiếp cận khác nhau:
- RLHF sử dụng một mô hình phần thưởng riêng biệt để đánh giá câu trả lời, sau đó sử dụng RL (như PPO) để tối ưu hóa mô hình chính sách.
- DPO loại bỏ mô hình phần thưởng trung gian và tối ưu hóa trực tiếp từ dữ liệu sở thích cặp, đơn giản hóa quá trình và thường hiệu quả hơn.

### Tôi cần bao nhiêu dữ liệu để DPO hiệu quả?

Hiệu quả của DPO phụ thuộc vào chất lượng dữ liệu hơn là số lượng. Thông thường, khoảng 1,000-10,000 cặp sở thích chất lượng cao đã đủ để thu được kết quả tốt. Tuy nhiên, nếu tác vụ phức tạp hoặc chuyên biệt, có thể cần nhiều dữ liệu hơn.

### Làm thế nào để chọn giá trị beta phù hợp?

Tham số beta cân bằng giữa việc tối ưu hóa sở thích và sự tương đồng với mô hình tham chiếu:
- Beta nhỏ (0.1-0.2): Mô hình tinh chỉnh sẽ giữ gần với mô hình tham chiếu, ít rủi ro divergence
- Beta lớn (0.3-0.5): Mô hình sẽ tối ưu hóa mạnh mẽ hơn theo sở thích, nhưng có thể dẫn đến các hành vi không mong muốn

Nên bắt đầu với beta=0.1 và điều chỉnh dần dựa trên kết quả.

### Làm cách nào để đánh giá mô hình đã tinh chỉnh?

Nên sử dụng kết hợp các phương pháp đánh giá:
1. Đánh giá tự động: Tính độ chính xác, factuality, độ đa dạng
2. Đánh giá A/B: So sánh trực tiếp với mô hình gốc
3. Đánh giá người dùng: Thu thập phản hồi từ người dùng thực

Mô-đun `deep_research_core.evaluation` cung cấp nhiều công cụ đánh giá. 