# Hướng dẫn sử dụng ToTRAG (Tree of Thought + Retrieval-Augmented Generation)

Tài liệu này cung cấp hướng dẫn chi tiết về cách sử dụng ToTRAG, một kỹ thuật kết hợp Tree of Thought (ToT) và Retrieval-Augmented Generation (RAG) để nâng cao khả năng suy luận và truy xuất thông tin.

## Giới thiệu

ToTRAG kết hợp sức mạnh của hai kỹ thuật:
- **Tree of Thought (ToT)**: Cho phép mô hình khám phá nhiều đường dẫn suy luận khác nhau và chọn đường dẫn tốt nhất.
- **Retrieval-Augmented Generation (RAG)**: Nâng cao chất lượng phản hồi bằng cách truy xuất thông tin liên quan từ các nguồn bên ngoài.

Kết hợp này giú<PERSON> mô hình có thể suy luận phức tạp dựa trên thông tin truy xuất, đồng thời xử lý thông tin mâu thuẫn và tối ưu hóa hiệu suất.

## Cài đặt

Để sử dụng ToTRAG, bạn cần cài đặt các thư viện cần thiết:

```bash
pip install -r requirements.txt
```

## Sử dụng cơ bản

### Khởi tạo ToTRAG

```python
from deep_research_core.reasoning.tot_rag import ToTRAG

# Khởi tạo ToTRAG với cấu hình cơ bản
totrag = ToTRAG(
    provider="openai",           # Nhà cung cấp API: "openai", "anthropic", "openrouter"
    model="gpt-4o",              # Mô hình ngôn ngữ
    temperature=0.7,             # Nhiệt độ sampling
    max_tokens=2000,             # Số token tối đa cho mỗi lần gọi API
    max_branches=3,              # Số nhánh tối đa cho mỗi bước suy luận
    max_depth=3,                 # Độ sâu tối đa của cây suy luận
    language="vi",               # Ngôn ngữ: "en", "vi", ...
    verbose=False,               # Hiển thị thông tin chi tiết
    adaptive=True,               # Tự động điều chỉnh tham số dựa trên độ phức tạp
    use_cache=True,              # Sử dụng cache cho các truy vấn lặp lại
    evaluate_results=True        # Đánh giá chất lượng suy luận
)
```

### Xử lý truy vấn

```python
# Xử lý truy vấn đơn giản
result = totrag.process(
    query="Tại sao bầu trời có màu xanh?",
    top_k=5                      # Số lượng tài liệu truy xuất
)

# In kết quả
print(f"Câu hỏi: {result['query']}")
print(f"Câu trả lời: {result['answer']}")
print(f"Thời gian xử lý: {result['latency']:.2f} giây")
```

### Truy xuất tài liệu

```python
# Truy xuất tài liệu liên quan
documents = totrag.retrieve(
    query="Tại sao bầu trời có màu xanh?",
    top_k=5                      # Số lượng tài liệu truy xuất
)

# In tài liệu
for i, doc in enumerate(documents):
    print(f"Tài liệu {i+1}: {doc['content'][:100]}...")
    print(f"Điểm số: {doc['score']:.4f}")
    print()
```

### Định dạng ngữ cảnh

```python
# Định dạng ngữ cảnh từ tài liệu
context = totrag.format_context(documents)
print(f"Ngữ cảnh: {context[:200]}...")
```

## Tính năng nâng cao

### Tối ưu hóa nâng cao

ToTRAG hỗ trợ nhiều tính năng tối ưu hóa nâng cao để cải thiện hiệu suất và chất lượng:

```python
# Khởi tạo ToTRAG với tối ưu hóa nâng cao
totrag = ToTRAG(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    max_branches=3,
    max_depth=3,
    language="vi",
    verbose=False,
    adaptive=True,
    use_cache=True,
    evaluate_results=True,
    use_advanced_optimization=True,     # Sử dụng tối ưu hóa nâng cao
    token_budget=100000,                # Ngân sách token tối đa
    parallel_exploration=True,          # Khám phá song song
    max_workers=3,                      # Số lượng worker tối đa cho khám phá song song
    conflict_resolution_strategy="weighted_voting"  # Chiến lược giải quyết mâu thuẫn
)
```

### Chiến lược giải quyết mâu thuẫn

ToTRAG hỗ trợ nhiều chiến lược giải quyết mâu thuẫn trong tài liệu truy xuất:

- **weighted_voting**: Ưu tiên tài liệu có điểm số cao hơn.
- **recency_bias**: Ưu tiên tài liệu mới hơn.
- **source_reliability**: Ưu tiên tài liệu từ nguồn đáng tin cậy hơn.
- **consensus**: Tìm sự đồng thuận giữa các tài liệu.

```python
# Sử dụng chiến lược giải quyết mâu thuẫn khác nhau
totrag = ToTRAG(
    # ... các tham số khác ...
    conflict_resolution_strategy="consensus"
)
```

### Trực quan hóa

ToTRAG cung cấp công cụ trực quan hóa để hiểu rõ hơn về quá trình suy luận:

```python
from deep_research_core.visualization.tot_rag_visualizer import ToTRAGVisualizer

# Khởi tạo visualizer
visualizer = ToTRAGVisualizer(output_dir="visualizations")

# Trực quan hóa kết quả
output_paths = visualizer.create_comprehensive_visualization(
    result=result,
    filename_prefix="my_query",
    show=True                    # Hiển thị trực quan hóa
)

print(f"Đã lưu trực quan hóa vào: {output_paths}")
```

## Ví dụ chi tiết

### Ví dụ 1: Suy luận phức tạp

```python
from deep_research_core.reasoning.tot_rag import ToTRAG
from deep_research_core.visualization.tot_rag_visualizer import ToTRAGVisualizer

# Khởi tạo ToTRAG
totrag = ToTRAG(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    max_branches=5,
    max_depth=5,
    language="vi",
    adaptive=True,
    use_advanced_optimization=True,
    conflict_resolution_strategy="weighted_voting"
)

# Xử lý truy vấn phức tạp
result = totrag.process(
    query="Phân tích tác động của biến đổi khí hậu đến nông nghiệp Việt Nam và đề xuất giải pháp thích ứng.",
    top_k=10
)

# In kết quả
print(f"Câu hỏi: {result['query']}")
print(f"Câu trả lời: {result['answer']}")
print(f"Thời gian xử lý: {result['latency']:.2f} giây")

# Trực quan hóa kết quả
visualizer = ToTRAGVisualizer()
visualizer.create_comprehensive_visualization(result, show=True)
```

### Ví dụ 2: Xử lý thông tin mâu thuẫn

```python
from deep_research_core.reasoning.tot_rag import ToTRAG

# Khởi tạo ToTRAG với chiến lược giải quyết mâu thuẫn
totrag = ToTRAG(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    conflict_resolution_strategy="source_reliability"
)

# Xử lý truy vấn có thông tin mâu thuẫn
result = totrag.process(
    query="Liệu cà phê có tốt cho sức khỏe không?",
    top_k=10
)

# In thông tin về giải quyết mâu thuẫn
print(f"Chiến lược giải quyết mâu thuẫn: {result['conflict_resolution']['strategy']}")
print(f"Phát hiện mâu thuẫn: {result['conflict_resolution']['conflicts_detected']}")
print(f"Câu trả lời: {result['answer']}")
```

### Ví dụ 3: Khám phá song song

```python
from deep_research_core.reasoning.tot_rag import ToTRAG

# Khởi tạo ToTRAG với khám phá song song
totrag = ToTRAG(
    provider="openai",
    model="gpt-4o",
    temperature=0.7,
    max_tokens=2000,
    max_branches=5,
    max_depth=5,
    use_advanced_optimization=True,
    parallel_exploration=True,
    max_workers=5
)

# Xử lý truy vấn phức tạp
result = totrag.process(
    query="Phân tích ưu nhược điểm của các nguồn năng lượng tái tạo và đề xuất chiến lược phát triển cho Việt Nam.",
    top_k=10
)

# In thông tin về khám phá song song
print(f"Số đường dẫn đã khám phá: {result['explored_paths']}")
print(f"Câu trả lời: {result['answer']}")
```

## Tùy chỉnh nâng cao

### Tùy chỉnh prompt

```python
# Tùy chỉnh system prompt và user prompt
result = totrag.process(
    query="Tại sao bầu trời có màu xanh?",
    custom_system_prompt="Bạn là một chuyên gia vật lý, hãy giải thích các hiện tượng tự nhiên một cách khoa học và dễ hiểu.",
    custom_user_prompt="Hãy giải thích hiện tượng sau đây: {query}\n\nNgữ cảnh: {context}"
)
```

### Sử dụng callback cho streaming

```python
# Sử dụng callback để streaming kết quả
def print_callback(content):
    print(f"Streaming: {content}")

result = totrag.process(
    query="Tại sao bầu trời có màu xanh?",
    callback=print_callback
)
```

### Đánh giá kết quả

```python
# Đánh giá kết quả với câu trả lời mong đợi
result = totrag.process(
    query="Tại sao bầu trời có màu xanh?",
    expected_answer="Bầu trời có màu xanh do hiện tượng tán xạ Rayleigh."
)

# In kết quả đánh giá
if "evaluation" in result:
    print(f"Chất lượng suy luận: {result['evaluation']['metrics']['overall_quality']}/10")
    print(f"Độ chính xác: {result['evaluation']['metrics'].get('accuracy', 'N/A')}")
    print(f"Độ đầy đủ: {result['evaluation']['metrics'].get('completeness', 'N/A')}")
```

## Xử lý lỗi

```python
try:
    result = totrag.process(query="Câu hỏi phức tạp")
except Exception as e:
    print(f"Lỗi khi xử lý truy vấn: {str(e)}")
    
    # Thử lại với tham số đơn giản hơn
    result = totrag.process(
        query="Câu hỏi phức tạp",
        max_branches=2,
        max_depth=2,
        temperature=0.5
    )
```

## Tối ưu hóa hiệu suất

### Quản lý ngân sách token

```python
# Kiểm tra ngân sách token còn lại
remaining_budget = totrag.token_budget_manager.get_remaining_budget()
print(f"Ngân sách token còn lại: {remaining_budget}")

# Đặt lại ngân sách token
totrag.token_budget_manager.reset()
```

### Sử dụng cache có chọn lọc

```python
# Xem thống kê cache
cache_stats = totrag.cache.get_stats()
print(f"Kích thước cache: {cache_stats['size']}/{cache_stats['max_size']}")
print(f"Số lần truy cập trung bình: {cache_stats['avg_hit_count']:.2f}")

# Xóa cache
totrag.cache.clear()
```

## Kết luận

ToTRAG là một công cụ mạnh mẽ kết hợp sức mạnh của Tree of Thought và Retrieval-Augmented Generation để nâng cao khả năng suy luận và truy xuất thông tin. Với các tính năng tối ưu hóa nâng cao, xử lý thông tin mâu thuẫn và trực quan hóa, ToTRAG cung cấp một giải pháp toàn diện cho các nhiệm vụ suy luận phức tạp.

## Tài liệu tham khảo

- [Tree of Thought: Deliberate Problem Solving with Large Language Models](https://arxiv.org/abs/2305.10601)
- [Retrieval-Augmented Generation for Knowledge-Intensive NLP Tasks](https://arxiv.org/abs/2005.11401)
- [Chain-of-Thought Prompting Elicits Reasoning in Large Language Models](https://arxiv.org/abs/2201.11903)
