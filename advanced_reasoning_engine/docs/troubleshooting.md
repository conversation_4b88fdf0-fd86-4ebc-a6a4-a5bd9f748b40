# Deep Research Core - Troubleshooting Guide

## Introduction

This troubleshooting guide addresses common issues you might encounter when working with Deep Research Core. If you experience problems, please consult this guide before contacting support. Most issues can be resolved by following these recommendations.

## Table of Contents

1. [Installation Issues](#installation-issues)
2. [Connection and API Issues](#connection-and-api-issues)
3. [Model Loading and Inference Problems](#model-loading-and-inference-problems)
4. [RAG (Retrieval-Augmented Generation) Issues](#rag-issues)
5. [Reasoning Module Issues](#reasoning-module-issues)
6. [Vietnamese Language Support Issues](#vietnamese-language-support-issues)
7. [Performance and Optimization](#performance-and-optimization)
8. [User Interface Issues](#user-interface-issues)
9. [Common Error Messages](#common-error-messages)
10. [Contacting Support](#contacting-support)

## Installation Issues

### Package Installation Fails

**Problem**: Error when installing the package with pip.

**Solution**:
1. Ensure you're using Python 3.8 or higher: `python --version`
2. Try upgrading pip: `pip install --upgrade pip`
3. Install with verbose output to identify issues: `pip install -v deep-research-core`
4. Check for dependency conflicts: `pip check`

### Environment Setup Problems

**Problem**: ImportError or ModuleNotFoundError after installation.

**Solution**:
1. Verify your virtual environment is activated
2. Reinstall with all dependencies: `pip install -e ".[all]"`
3. Check for path issues: `python -c "import sys; print(sys.path)"`

### CUDA Installation Issues

**Problem**: CUDA errors when trying to use GPU.

**Solution**:
1. Verify CUDA installation: `nvidia-smi`
2. Ensure CUDA version matches PyTorch requirements
3. Set environment variable: `export CUDA_VISIBLE_DEVICES=0`
4. Fall back to CPU if needed: `export USE_CPU=1`

## Connection and API Issues

### API Connection Failures

**Problem**: Cannot connect to external APIs (OpenAI, DeepSeek, etc.).

**Solution**:
1. Check your API keys in the `.env` file
2. Verify network connectivity: `ping api.openai.com`
3. Check for proxy settings that might block API calls
4. Increase timeout settings in `config.yaml`

### Rate Limit Errors

**Problem**: Receiving rate limit errors from APIs.

**Solution**:
1. Implement exponential backoff (already included in most API clients)
2. Reduce parallel requests
3. Consider upgrading your API plan
4. Update `retry_limit` in your configuration

### Authentication Errors

**Problem**: "Authentication error" or "Invalid API key" messages.

**Solution**:
1. Regenerate your API keys
2. Check for whitespace in your API key string
3. Verify the correct environment variable names are used
4. Check API key permissions and roles

## Model Loading and Inference Problems

### Out of Memory Errors

**Problem**: CUDA out of memory or system RAM exhaustion.

**Solution**:
1. Reduce model size or batch size
2. Use model quantization: `use_4bit=True` in config
3. Set `device_map="auto"` for automatic memory management
4. Close other applications consuming GPU memory

### Slow Inference

**Problem**: Models take too long to generate responses.

**Solution**:
1. Check model parameters (temperature, max_tokens)
2. Verify you're not running too many concurrent processes
3. Consider using a smaller model
4. Set appropriate batch size for your hardware

### Model Loading Failures

**Problem**: Models fail to load with various errors.

**Solution**:
1. Check disk space for downloaded models
2. Verify model cache location has write permissions
3. Try different model loading strategies: `from_pretrained` options
4. Clear Hugging Face cache: `rm -rf ~/.cache/huggingface`

## RAG Issues

### Document Processing Failures

**Problem**: Documents fail to process or index.

**Solution**:
1. Check file format compatibility (PDF, TXT, DOCX supported)
2. Verify file permissions
3. Check for file corruption: `file your_document.pdf`
4. Try preprocessing documents: `prepare_documents` utility

### Poor Retrieval Quality

**Problem**: RAG retrieves irrelevant documents.

**Solution**:
1. Adjust chunk size in `config.yaml` (try 256, 512, or 1024)
2. Increase the number of retrieved chunks: `k=5` or higher
3. Try different embedding models
4. Use the HyDE technique: `use_hyde=True`

### Vector Store Issues

**Problem**: Vector store errors or poor performance.

**Solution**:
1. Check database connection settings
2. Try a different vector database (FAISS, Chroma, etc.)
3. Rebuild your vector indices: `rebuild_indices=True`
4. Increase similarity threshold: `similarity_threshold=0.75`

### Missing Sources in Citations

**Problem**: Source citations are missing or incomplete.

**Solution**:
1. Enable source attribution: `source_attribution=True`
2. Increase citation confidence threshold
3. Verify document metadata is preserved during indexing
4. Check for document duplicates that might confuse attribution

## Reasoning Module Issues

### Tree of Thoughts (ToT) Problems

**Problem**: ToT reasoning produces poor or incomplete results.

**Solution**:
1. Increase exploration breadth: `k=3` or higher
2. Adjust evaluation parameters
3. Check prompt templates for clarity
4. Increase max tokens for complex reasoning

### Chain of Thought (CoT) Issues

**Problem**: CoT reasoning quality issues.

**Solution**:
1. Verify your prompt includes proper reasoning examples
2. Increase number of examples in few-shot prompts
3. Try different temperatures (0.3-0.7 usually works best)
4. Enable self-consistency with multiple paths

### Graph of Thoughts (GoT) Problems

**Problem**: GoT reasoning fails or produces cyclic paths.

**Solution**:
1. Check max_nodes and max_edges parameters
2. Verify node evaluation function
3. Adjust cycle detection parameters
4. Increase timeout for complex graphs

## Vietnamese Language Support Issues

### Vietnamese Text Processing Issues

**Problem**: Vietnamese text not properly processed.

**Solution**:
1. Ensure proper UTF-8 encoding throughout the pipeline
2. Use the Vietnamese tokenization module: `use_vn_tokenizer=True`
3. Check compound word handling settings
4. Verify model supports Vietnamese characters

### Vietnamese Embedding Issues

**Problem**: Poor embedding quality for Vietnamese text.

**Solution**:
1. Switch to Vietnamese-specific embeddings: `vn_embeddings=True`
2. Check preprocessing for Vietnamese text
3. Verify normalization settings
4. Consider domain-specific embeddings for specialized vocabulary

### Translation Issues

**Problem**: Poor translation quality between Vietnamese and other languages.

**Solution**:
1. Use specialized translation models instead of general ones
2. Enable domain adaptation for translation
3. Adjust beam search parameters
4. Consider pre/post-processing for specialized terms

## Performance and Optimization

### Memory Usage Problems

**Problem**: High memory consumption during operation.

**Solution**:
1. Enable memory efficient attention: `use_flash_attention=True`
2. Implement gradient checkpointing
3. Use lower precision (fp16 or int8)
4. Implement proper garbage collection for large objects

### Slow Processing

**Problem**: Overall system performance is slow.

**Solution**:
1. Profile bottlenecks: `profiler.enable()`
2. Cache intermediate results
3. Implement parallel processing where possible
4. Use streaming responses for long outputs

### Batch Processing Failures

**Problem**: Batch processing jobs fail or timeout.

**Solution**:
1. Reduce batch size
2. Implement checkpointing
3. Add failure recovery mechanisms
4. Increase timeouts for large batches

## User Interface Issues

### UI Rendering Problems

**Problem**: UI components not rendering correctly.

**Solution**:
1. Clear browser cache and reload
2. Check browser console for JavaScript errors
3. Verify compatibility with your browser version
4. Check for CSS conflicts

### File Upload Issues

**Problem**: Cannot upload documents or uploads fail.

**Solution**:
1. Check file size limits (default is 50MB)
2. Verify supported file formats
3. Check network stability during uploads
4. Try splitting large files into smaller ones

### Visualization Problems

**Problem**: Reasoning visualizations not displaying properly.

**Solution**:
1. Check browser compatibility (modern browsers required)
2. Reduce complexity of visualized structures
3. Increase timeout for rendering complex visualizations
4. Check for JavaScript console errors

## Common Error Messages

### "Model not found"

**Solution**:
1. Check model path or identifier
2. Verify model exists in Hugging Face or local cache
3. Check for typos in model name
4. Use full model path instead of shorthand

### "Token limit exceeded"

**Solution**:
1. Reduce input length
2. Split inputs into smaller chunks
3. Use a model with larger context window
4. Implement chunking and summarization for long content

### "Resource exhausted"

**Solution**:
1. Check system resources (CPU, RAM, GPU)
2. Reduce concurrent operations
3. Implement request queuing
4. Scale infrastructure if needed

### "Invalid configuration"

**Solution**:
1. Check configuration file syntax
2. Verify all required parameters are present
3. Check for deprecated parameters
4. Reset to default configuration and recustomize

## Contacting Support

If you've tried the solutions above and still encounter issues, please contact our support team with:

1. A detailed description of the problem
2. Steps to reproduce the issue
3. Error messages and logs
4. System information (OS, Python version, dependencies)
5. Configuration files (with sensitive information removed)

**Support Email**: <EMAIL>

**GitHub Issues**: https://github.com/deepresearchcore/deep-research-core/issues 