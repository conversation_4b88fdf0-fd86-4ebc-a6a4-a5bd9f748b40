# Advanced RAG Guide

This guide provides advanced information about the Retrieval-Augmented Generation (RAG) implementations in Deep Research Core, including performance optimization, monitoring, and advanced use cases.

## Table of Contents

1. [Performance Optimization](#performance-optimization)
2. [Monitoring and Observability](#monitoring-and-observability)
3. [Advanced Use Cases](#advanced-use-cases)
4. [Troubleshooting](#troubleshooting)
5. [Best Practices](#best-practices)

## Performance Optimization

### Optimized RAG Implementations

Deep Research Core provides optimized versions of the standard RAG implementations:

- **OptimizedSQLiteVectorRAG**: An optimized version of SQLiteVectorRAG with performance improvements
- **OptimizedMilvusRAG**: An optimized version of MilvusRAG with performance improvements

These optimized implementations include:

- **Caching**: In-memory caching of search results
- **Batch processing**: Processing documents in batches for better performance
- **Database optimizations**: Optimized database settings and indexes
- **Vector search optimizations**: Optimized vector search algorithms

### Using Optimized Implementations

```python
from deep_research_core.reasoning.optimizations import OptimizedSQLiteVectorRAG, OptimizedMilvusRAG

# Initialize OptimizedSQLiteVectorRAG
sqlite_rag = OptimizedSQLiteVectorRAG(
    db_path="optimized_rag.db",
    provider="openai",
    model="gpt-4o",
    use_cache=True,
    cache_ttl=3600,  # 1 hour
    batch_size=100
)

# Initialize OptimizedMilvusRAG
milvus_rag = OptimizedMilvusRAG(
    provider="openai",
    model="gpt-4o",
    collection_name="optimized_collection",
    connection_args={"host": "localhost", "port": "19530"},
    use_cache=True,
    cache_ttl=3600,  # 1 hour
    batch_size=100,
    index_type="HNSW",
    metric_type="IP",
    use_partitions=True,
    partition_key="source"
)
```

### Performance Comparison

The optimized implementations can provide significant performance improvements:

| Implementation | Document Addition | Search | Processing |
|----------------|-------------------|--------|------------|
| SQLiteVectorRAG | 1x | 1x | 1x |
| OptimizedSQLiteVectorRAG | 1.5-2x | 2-3x | 1.5-2x |
| MilvusRAG | 1x | 1x | 1x |
| OptimizedMilvusRAG | 1.5-3x | 3-5x | 1.5-2x |
| FAISSRAG | 1-2x | 5-10x | 1.5-2x |

The actual performance improvement depends on the specific use case, dataset size, and hardware.

### Optimizing for Different Use Cases

#### Small Datasets (< 10,000 documents)

For small datasets, SQLiteVectorRAG or OptimizedSQLiteVectorRAG is usually sufficient:

```python
from deep_research_core.reasoning.optimizations import OptimizedSQLiteVectorRAG

rag = OptimizedSQLiteVectorRAG(
    db_path="small_dataset.db",
    provider="openai",
    model="gpt-4o",
    use_cache=True,
    cache_ttl=3600
)
```

#### Medium Datasets (10,000 - 100,000 documents)

For medium datasets, FAISSRAG provides a good balance of performance and simplicity:

```python
from deep_research_core.reasoning import FAISSRAG

rag = FAISSRAG(
    provider="openai",
    model="gpt-4o",
    index_path="medium_dataset",
    index_type="Flat",
    use_gpu=False
)
```

#### Large Datasets (> 100,000 documents)

For large datasets, OptimizedMilvusRAG with partitioning is recommended:

```python
from deep_research_core.reasoning.optimizations import OptimizedMilvusRAG

rag = OptimizedMilvusRAG(
    provider="openai",
    model="gpt-4o",
    collection_name="large_dataset",
    connection_args={"host": "localhost", "port": "19530"},
    use_cache=True,
    index_type="HNSW",
    use_partitions=True,
    partition_key="source"
)
```

## Monitoring and Observability

Deep Research Core provides comprehensive monitoring and observability features:

### Structured Logging

All RAG implementations include structured logging:

```python
from deep_research_core.utils.structured_logging import get_logger

logger = get_logger(__name__)
logger.info("This is a structured log message", extra={"key": "value"})
```

### Performance Metrics

Performance metrics are collected automatically:

```python
from deep_research_core.utils.performance_metrics import get_metrics

metrics = get_metrics()
print(f"Latency: {metrics['latency']}")
print(f"Throughput: {metrics['throughput']}")
print(f"Memory: {metrics['memory']}")
```

### Distributed Tracing

Distributed tracing is available for tracking request flow:

```python
from deep_research_core.utils.distributed_tracing import trace_function, span

@trace_function(name="my_function")
def my_function():
    # Function code here
    with span("my_operation"):
        # Operation code here
        pass
```

### External Monitoring Integration

Deep Research Core integrates with external monitoring tools:

#### Prometheus Integration

```python
from deep_research_core.utils.external_monitoring import get_prometheus_exporter

# Initialize Prometheus exporter
exporter = get_prometheus_exporter(port=8000)
exporter.start()

# Record metrics
exporter.record_request("search", "success")
exporter.record_document_operation("add", "SQLiteVectorRAG", 10)
exporter.record_latency("search", 0.1)
```

#### Grafana Dashboards

```python
from deep_research_core.utils.external_monitoring import generate_all_dashboards

# Generate Grafana dashboards
generate_all_dashboards("dashboards")
```

## Advanced Use Cases

### Hybrid Search

Combine vector search with keyword search for better results:

```python
from deep_research_core.reasoning import SQLiteVectorRAG

rag = SQLiteVectorRAG(
    db_path="hybrid_search.db",
    provider="openai",
    model="gpt-4o"
)

# Add documents
rag.add_documents(documents)

# Perform hybrid search
results = rag.search(
    query="Python programming language",
    hybrid_weight=0.7  # 70% vector search, 30% keyword search
)
```

### Filtering

Filter search results based on metadata:

```python
from deep_research_core.reasoning import MilvusRAG

rag = MilvusRAG(
    provider="openai",
    model="gpt-4o",
    collection_name="filtered_search"
)

# Add documents
rag.add_documents(documents)

# Search with filter
results = rag.search(
    query="Machine learning",
    filter_expr="source == 'AI Technologies' && date >= '2023-01-01'"
)
```

### Custom Prompts

Use custom prompts for specific use cases:

```python
from deep_research_core.reasoning import SQLiteVectorRAG

rag = SQLiteVectorRAG(
    db_path="custom_prompts.db",
    provider="openai",
    model="gpt-4o"
)

# Add documents
rag.add_documents(documents)

# Process with custom prompts
result = rag.process(
    query="Explain machine learning",
    custom_system_prompt="You are an AI expert explaining complex concepts in simple terms.",
    custom_user_prompt="Question: {query}\n\nContext:\n{context}\n\nExplain this concept as if you were teaching a 10-year-old."
)
```

### Multi-Modal RAG

Combine text and image data:

```python
from deep_research_core.reasoning import SQLiteVectorRAG

rag = SQLiteVectorRAG(
    db_path="multimodal_rag.db",
    provider="openai",
    model="gpt-4o"
)

# Add documents with image URLs
documents = [
    {
        "content": "This is an image of a cat.",
        "source": "Images",
        "image_url": "https://example.com/cat.jpg"
    },
    {
        "content": "This is an image of a dog.",
        "source": "Images",
        "image_url": "https://example.com/dog.jpg"
    }
]

rag.add_documents(documents)

# Process with image references
result = rag.process(
    query="Describe the cat in the image",
    custom_user_prompt="Question: {query}\n\nContext:\n{context}\n\nImage: {image_url}\n\nPlease describe the image based on the context."
)
```

## Troubleshooting

### Common Issues and Solutions

#### High Latency

If you're experiencing high latency:

1. **Check document size**: Large documents can slow down retrieval
2. **Optimize chunk size**: Try different chunk sizes (500-1000 tokens)
3. **Use optimized implementations**: Switch to OptimizedSQLiteVectorRAG or OptimizedMilvusRAG
4. **Enable caching**: Set `use_cache=True` in optimized implementations
5. **Monitor memory usage**: High memory usage can cause swapping

#### Out of Memory Errors

If you're experiencing out of memory errors:

1. **Reduce batch size**: Set a smaller batch size for document addition
2. **Use streaming**: Enable streaming for large responses
3. **Switch to a more memory-efficient implementation**: FAISSRAG with IVF index is memory-efficient

#### Poor Retrieval Quality

If you're experiencing poor retrieval quality:

1. **Adjust hybrid weight**: Try different hybrid weights (0.5-0.8)
2. **Increase top_k**: Retrieve more documents (10-20)
3. **Improve chunking**: Use smaller chunks with more overlap
4. **Use a better embedding model**: Try a more powerful embedding model

## Best Practices

### Document Processing

1. **Chunk documents appropriately**: 500-1000 tokens per chunk
2. **Include metadata**: Add source, title, date, and other relevant metadata
3. **Process in batches**: Add documents in batches of 100-500
4. **Deduplicate content**: Remove duplicate or near-duplicate content

### Query Processing

1. **Use hybrid search**: Combine vector and keyword search
2. **Apply filters**: Use metadata filters to narrow down results
3. **Adjust top_k**: Retrieve enough documents for context (5-20)
4. **Use custom prompts**: Tailor prompts for specific use cases

### Monitoring and Maintenance

1. **Monitor performance**: Track latency, throughput, and memory usage
2. **Set up alerts**: Configure alerts for performance issues
3. **Regularly optimize**: Rebuild indexes and optimize databases
4. **Back up data**: Regularly back up vector stores

### Scaling

1. **Start small**: Begin with SQLiteVectorRAG for prototyping
2. **Scale gradually**: Move to FAISSRAG for medium datasets
3. **Use distributed solutions**: Use MilvusRAG for large-scale deployments
4. **Shard data**: Partition data by source or date for better performance
