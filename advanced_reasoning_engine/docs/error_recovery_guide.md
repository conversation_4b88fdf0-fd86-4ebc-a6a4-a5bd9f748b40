# Error Recovery System Guide

This guide provides detailed information on how to use the error recovery system in your applications.

## Overview

The error recovery system is designed to make your AI applications more robust by automatically detecting and recovering from errors. It includes:

- Automatic error detection and classification
- Multiple recovery strategies for different error types
- Learning from past errors to improve future recovery
- Analytics and visualization for error patterns
- Distributed error recovery across multiple nodes
- ML-based error classification

## Getting Started

### Basic Usage

To use the error recovery system, you need to create a `ReasoningErrorRecovery` instance and integrate it with your code:

```python
from src.deep_research_core.utils.reasoning_error_recovery import ReasoningErrorRecovery

# Create a recovery system
recovery = ReasoningErrorRecovery(
    use_error_recovery=True,
    max_retries=3,
    retry_delay=0.5,
    cache_path="./cache/error_recovery.json",
    use_advanced_strategies=True
)

# Use the recovery system
try:
    result = some_function_that_might_fail()
except Exception as e:
    # Create error context
    context = {
        "method": "my_method",
        "tool_name": "my_tool",
        "tool_args": {"arg1": "value1", "arg2": "value2"}
    }
    
    # Handle error with error recovery system
    recovery_result = recovery.handle_error(e, context)
    
    if recovery_result.get("success", False):
        # If recovery was successful, use the recovered result
        result = recovery_result.get("result")
        print(f"Recovered using strategy: {recovery_result.get('strategy')}")
    else:
        # If recovery failed, handle the error
        print(f"Recovery failed: {recovery_result.get('message')}")
```

### Integration with Tree of Thought (ToT)

The error recovery system is already integrated with the Tree of Thought (ToT) reasoning technique:

```python
from src.deep_research_core.reasoning.tot import TreeOfThought

# Create a Tree of Thought reasoner with error recovery
tot = TreeOfThought(
    provider="openai",
    model="gpt-4o",
    language="en",
    max_branches=3,
    max_depth=3,
    adaptive=True,
    verbose=True,
    use_error_recovery=True,  # Enable error recovery
    max_retries=3,
    retry_delay=0.5,
    cache_path="./cache",
    use_advanced_strategies=True
)

# Use the reasoner
result = tot.reason(query="What is the capital of France?")
```

### Integration with Retrieval-Augmented Generation (RAG)

The error recovery system is also integrated with RAG systems:

```python
from src.deep_research_core.rag.sqlite_vector_rag import SQLiteVectorRAG
from src.deep_research_core.rag.error_recovery_rag import ErrorRecoveryRAG

# Create a RAG system
rag_system = SQLiteVectorRAG(
    db_path="vector_rag.sqlite",
    table_name="documents",
    embedding_model="multilingual-e5-large",
    embedding_dim=768,
    create_if_not_exists=True
)

# Wrap the RAG system with error recovery
error_recovery_rag = ErrorRecoveryRAG(
    rag_system=rag_system,
    use_error_recovery=True,
    max_retries=3,
    retry_delay=0.5,
    cache_path="./cache",
    use_advanced_strategies=True
)

# Use the RAG system
result = error_recovery_rag.query(query="What is the capital of France?")
```

## Configuration Options

The error recovery system can be configured with various options:

### Basic Options

- `use_error_recovery`: Whether to use error recovery (default: True)
- `max_retries`: Maximum number of retries for failed actions (default: 3)
- `retry_delay`: Delay between retries in seconds (default: 0.5)
- `cache_path`: Path to cache directory (default: None)
- `use_advanced_strategies`: Whether to use advanced recovery strategies (default: True)

### Advanced Options

- `use_error_analytics`: Whether to use error analytics (default: True)
- `use_error_visualization`: Whether to use error visualization (default: False)
- `use_error_strategy_optimizer`: Whether to use error strategy optimizer (default: True)
- `use_error_monitoring`: Whether to use error monitoring (default: True)
- `use_ml_error_classifier`: Whether to use ML-based error classifier (default: True)
- `use_distributed_error_recovery`: Whether to use distributed error recovery (default: False)
- `cluster_nodes`: List of cluster nodes for distributed error recovery (default: None)

## Recovery Strategies

The error recovery system includes multiple recovery strategies:

### Basic Strategies

- **RetryStrategy**: Simply retries the failed operation
- **AlternativeToolStrategy**: Tries alternative tools
- **InputReformulationStrategy**: Reformulates the input
- **FallbackResultStrategy**: Returns a fallback result

### Advanced Strategies

- **ContextAwareReformulationStrategy**: Reformulates based on context
- **PromptEngineeringStrategy**: Adjusts prompts
- **ModelFallbackStrategy**: Falls back to alternative models
- **ToolParameterAdjustmentStrategy**: Adjusts tool parameters
- **AdaptiveRecoveryStrategy**: Adapts based on past errors

## Error Analytics

The error recovery system includes error analytics to track and analyze error patterns:

```python
from src.deep_research_core.utils.error_analytics import SQLiteErrorAnalytics

# Create an error analytics instance
analytics = SQLiteErrorAnalytics(db_path="error_analytics.db")

# Get error statistics
stats = analytics.get_error_statistics()
print(f"Total errors: {stats['total_errors']}")
print(f"Recovery rate: {stats['recovery_rate']:.2%}")
print(f"Most common error: {stats['most_common_error']}")
```

## Error Visualization

The error recovery system can visualize error patterns:

```python
from src.deep_research_core.utils.error_visualization import ErrorVisualization

# Create an error visualization instance
visualization = ErrorVisualization(analytics=analytics, output_dir="./visualizations")

# Generate visualizations
visualization.generate_error_trend_chart()
visualization.generate_recovery_rate_chart()
visualization.generate_error_category_chart()
```

## Distributed Error Recovery

The error recovery system supports distributed error recovery across multiple nodes:

```python
from src.deep_research_core.utils.distributed_error_recovery import DistributedErrorRecovery

# Create a distributed error recovery instance
distributed = DistributedErrorRecovery(
    recovery_manager=recovery_manager,
    learning_manager=learning_manager,
    storage_path="distributed_errors.json",
    sync_interval=60,
    node_id=None,  # Will generate a random UUID
    cluster_nodes=["node1", "node2", "node3"]
)

# Sync with other nodes
distributed.sync()
```

## ML-Based Error Classification

The error recovery system includes ML-based error classification:

```python
from src.deep_research_core.utils.ml_error_classifier import MLErrorClassifier

# Create an ML error classifier
classifier = MLErrorClassifier(
    model_path="ml_error_model.pkl",
    vectorizer_path="ml_error_vectorizer.pkl",
    min_samples=50,
    auto_train=True
)

# Classify an error
classification = classifier.classify(error, context)
print(f"Error category: {classification['category']}")
print(f"Confidence: {classification['confidence']:.2%}")
```

## Best Practices

### Error Context

When handling errors, provide as much context as possible to help the recovery system:

```python
context = {
    "method": "my_method",  # The method that failed
    "tool_name": "my_tool",  # The tool that failed
    "tool_args": {  # The arguments passed to the tool
        "arg1": "value1",
        "arg2": "value2"
    },
    "retry_count": 0,  # The number of retries so far
    "max_retries": 3,  # The maximum number of retries
    "tool_alternatives": {  # Alternative tools that can be used
        "my_tool": ["alternative_tool1", "alternative_tool2"]
    },
    "tool_registry": {  # Registry of available tools
        "my_tool": my_tool_function,
        "alternative_tool1": alternative_tool1_function,
        "alternative_tool2": alternative_tool2_function
    }
}
```

### Error Recovery in Production

For production systems, consider:

1. **Monitoring**: Enable error monitoring to track error patterns
2. **Analytics**: Use error analytics to identify common errors
3. **Optimization**: Use error strategy optimizer to improve recovery strategies
4. **Distributed Recovery**: Use distributed error recovery for multi-node systems
5. **ML Classification**: Use ML-based error classification for more accurate error detection

### Performance Considerations

- **Caching**: Enable caching to improve performance
- **Selective Recovery**: Only use error recovery for critical operations
- **Timeout Management**: Set appropriate timeouts for recovery operations
- **Resource Management**: Monitor resource usage during recovery

## Troubleshooting

### Common Issues

1. **Recovery Not Working**: Check that `use_error_recovery` is set to `True`
2. **Strategies Not Applied**: Check that the appropriate strategies are enabled
3. **Slow Recovery**: Adjust `max_retries` and `retry_delay` to balance recovery time
4. **High Resource Usage**: Adjust `max_retries` and `retry_delay` to reduce resource usage

### Logging

The error recovery system logs information about errors and recovery attempts:

```python
import logging
logging.basicConfig(level=logging.INFO)
```

### Debugging

For debugging, enable verbose output:

```python
recovery = ReasoningErrorRecovery(
    use_error_recovery=True,
    max_retries=3,
    retry_delay=0.5,
    cache_path="./cache/error_recovery.json",
    use_advanced_strategies=True,
    verbose=True  # Enable verbose output
)
```

## Examples

See the examples directory for complete examples:

- `examples/tot_error_recovery.py`: Example of using error recovery with Tree of Thought
- `examples/rag_error_recovery.py`: Example of using error recovery with RAG

## Conclusion

The error recovery system provides a robust framework for handling errors in AI systems, making them more reliable and resilient. By integrating this system with your reasoning techniques, you can improve the user experience and reduce the impact of errors.
