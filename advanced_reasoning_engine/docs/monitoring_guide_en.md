# Monitoring and Observability Guide

This guide provides detailed information about the monitoring and observability features in Deep Research Core, including structured logging, performance metrics, distributed tracing, and alerting.

## Table of Contents

1. [Overview](#overview)
2. [Structured Logging](#structured-logging)
3. [Performance Metrics](#performance-metrics)
4. [Distributed Tracing](#distributed-tracing)
5. [Alerting](#alerting)
6. [External Monitoring Integration](#external-monitoring-integration)
7. [Configuration](#configuration)
8. [Best Practices](#best-practices)

## Overview

Deep Research Core includes comprehensive monitoring and observability features:

- **Structured Logging**: JSON-formatted logs with contextual information
- **Performance Metrics**: Latency, throughput, and memory usage tracking
- **Distributed Tracing**: Trace request flow through the system
- **Alerting**: Detect and notify about performance issues
- **External Monitoring Integration**: Integration with Prometheus and Grafana

These features help you understand how your system is performing and identify potential issues.

## Structured Logging

### Basic Usage

```python
from deep_research_core.utils.structured_logging import get_logger

# Create a logger
logger = get_logger(__name__)

# Log messages
logger.info("This is an info message")
logger.warning("This is a warning message")
logger.error("This is an error message")

# Log with context
logger.info("User logged in", extra={"user_id": 123, "ip_address": "***********"})
```

### Configuration

```python
from deep_research_core.utils.structured_logging import get_logger

# Create a logger with custom configuration
logger = get_logger(
    name="my_logger",
    level="DEBUG",
    json_format=True,
    log_file="logs/my_app.log",
    include_stack_info=True
)
```

### Log Levels

- **DEBUG**: Detailed information for debugging
- **INFO**: General information about system operation
- **WARNING**: Potential issues that don't prevent normal operation
- **ERROR**: Errors that prevent normal operation
- **CRITICAL**: Critical errors that require immediate attention

## Performance Metrics

### Types of Metrics

Deep Research Core collects the following metrics:

- **Latency**: Time taken to complete operations
- **Throughput**: Number of operations per second
- **Memory Usage**: Process and system memory usage

### Measuring Latency

```python
from deep_research_core.utils.performance_metrics import measure_latency, measure_block_latency

# Measure function latency with decorator
@measure_latency("my_function")
def my_function():
    # Function code here
    pass

# Measure block latency with context manager
def process_data():
    # Some code here
    with measure_block_latency("data_processing"):
        # Code to measure
        pass
    # More code here
```

### Getting Metrics

```python
from deep_research_core.utils.performance_metrics import get_metrics

# Get all metrics
metrics = get_metrics()

# Access specific metrics
latency = metrics["latency"]
throughput = metrics["throughput"]
memory = metrics["memory"]

# Print p95 latency for a specific operation
print(f"P95 latency for search: {latency['search']['p95_ms']} ms")

# Print throughput for a specific operation
print(f"Throughput for search: {throughput['search']['throughput_per_second']} ops/s")

# Print memory usage
print(f"Process memory: {memory['latest']['process_rss_mb']} MB")
print(f"System memory usage: {memory['latest']['system_used_percent']}%")
```

## Distributed Tracing

### Basic Usage

```python
from deep_research_core.utils.distributed_tracing import trace_function, span

# Trace a function with decorator
@trace_function(name="my_function")
def my_function():
    # Function code here
    pass

# Create spans with context manager
def process_data():
    # Some code here
    with span("data_processing"):
        # Code to trace
        pass
    # More code here
```

### Configuration

```python
from deep_research_core.utils.distributed_tracing import configure_tracing

# Configure tracing
configure_tracing(
    service_name="my_service",
    use_opentelemetry=True,
    otlp_endpoint="http://localhost:4317"
)
```

## Alerting

### Types of Alerts

Deep Research Core supports the following types of alerts:

- **Threshold Alerts**: Alert when a metric exceeds a threshold
- **Percentage Change Alerts**: Alert when a metric changes by a percentage
- **Absence Alerts**: Alert when a metric is absent

### Setting Up Alerts

```python
from deep_research_core.utils.alerting import (
    add_threshold_alert,
    add_percentage_change_alert,
    AlertSeverity,
    start_alerting
)

# Add a threshold alert
add_threshold_alert(
    name="high_search_latency",
    description="Search latency is too high",
    metric_path="latency.search.p95_ms",
    threshold=1000,  # 1000ms = 1s
    comparison=">",
    severity=AlertSeverity.WARNING
)

# Add a percentage change alert
add_percentage_change_alert(
    name="throughput_drop",
    description="Search throughput has dropped significantly",
    metric_path="throughput.search.throughput_per_second",
    percentage_change=50,
    direction="decrease",
    severity=AlertSeverity.ERROR
)

# Start alerting
start_alerting()
```

### Notification Channels

```python
from deep_research_core.utils.alerting import add_email_notifier, add_slack_notifier

# Add email notifier
add_email_notifier(
    smtp_server="smtp.gmail.com",
    smtp_port=587,
    username="<EMAIL>",
    password="your_password",
    sender="<EMAIL>",
    recipients=["<EMAIL>", "<EMAIL>"],
    use_tls=True
)

# Add Slack notifier
add_slack_notifier(
    webhook_url="https://hooks.slack.com/services/XXX/YYY/ZZZ",
    channel="#alerts"
)
```

## External Monitoring Integration

### Prometheus Integration

```python
from deep_research_core.utils.external_monitoring import get_prometheus_exporter

# Initialize Prometheus exporter
exporter = get_prometheus_exporter(port=8000)
exporter.start()

# Record metrics
exporter.record_request("search", "success")
exporter.record_document_operation("add", "SQLiteVectorRAG", 10)
exporter.record_error("search", "timeout")
exporter.record_document_count("SQLiteVectorRAG", 1000)
exporter.record_latency("search", 0.1)
exporter.record_query("SQLiteVectorRAG", "search", 0.1)

# Stop exporter when done
exporter.stop()
```

### Grafana Dashboards

```python
from deep_research_core.utils.external_monitoring import (
    generate_default_dashboard,
    generate_rag_comparison_dashboard,
    generate_all_dashboards
)

# Generate default dashboard
generate_default_dashboard("dashboards/default_dashboard.json")

# Generate RAG comparison dashboard
generate_rag_comparison_dashboard("dashboards/rag_comparison_dashboard.json")

# Generate all dashboards
generate_all_dashboards("dashboards")
```

## Configuration

### Monitoring Configuration

Deep Research Core uses a centralized configuration for monitoring:

```python
from deep_research_core.config.monitoring_config import (
    LOG_CONFIG,
    METRICS_CONFIG,
    TRACING_CONFIG,
    ALERTING_CONFIG
)

# Log configuration
print(LOG_CONFIG)

# Metrics configuration
print(METRICS_CONFIG)

# Tracing configuration
print(TRACING_CONFIG)

# Alerting configuration
print(ALERTING_CONFIG)
```

### Initialization and Shutdown

```python
from deep_research_core.config.monitoring_config import initialize_monitoring, shutdown_monitoring

# Initialize monitoring
initialize_monitoring()

# Your application code here

# Shutdown monitoring
shutdown_monitoring()
```

## Best Practices

### Logging Best Practices

1. **Use structured logging**: Include context in logs
2. **Use appropriate log levels**: DEBUG for development, INFO for production
3. **Include relevant context**: Add user IDs, request IDs, etc.
4. **Avoid sensitive information**: Don't log passwords, API keys, etc.
5. **Use JSON format**: Makes logs easier to parse and analyze

### Metrics Best Practices

1. **Measure critical operations**: Focus on operations that impact user experience
2. **Use consistent naming**: Use a consistent naming scheme for metrics
3. **Set appropriate thresholds**: Base thresholds on historical data
4. **Monitor trends**: Look for changes over time, not just absolute values
5. **Aggregate metrics**: Use percentiles (p50, p95, p99) for latency

### Tracing Best Practices

1. **Trace important operations**: Focus on operations that span multiple components
2. **Add context to spans**: Include relevant information in span attributes
3. **Use meaningful span names**: Make span names descriptive
4. **Keep spans focused**: Create separate spans for distinct operations
5. **Propagate trace context**: Ensure trace context is propagated across services

### Alerting Best Practices

1. **Avoid alert fatigue**: Only alert on actionable issues
2. **Set appropriate thresholds**: Base thresholds on historical data
3. **Include context in alerts**: Add relevant information to alert messages
4. **Define severity levels**: Use different severity levels for different issues
5. **Have an escalation plan**: Define what to do when alerts fire
