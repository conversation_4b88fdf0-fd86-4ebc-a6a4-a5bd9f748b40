# Cải tiến hệ thống cache cho Deep Research Core

Tài liệu này mô tả các cải tiến đã được thực hiện cho hệ thống cache của Deep Research Core, đặc biệt là cho các phương thức tìm kiếm web.

## 1. Tổng quan

Hệ thống cache mới `AdaptiveSearchCache` cung cấp nhiều tính năng thông minh để cải thiện hiệu suất tìm kiếm web, bao gồm:

- Tự động điều chỉnh TTL dựa trên tần suất truy cập
- Tìm kiếm ngữ nghĩa để tìm các truy vấn tương tự
- Hỗ trợ đa ngôn ngữ với trọng số khác nhau
- Phân tích mẫu truy cập để tối ưu hóa cache
- Tiền tải thông minh dựa trên dự đoán
- <PERSON><PERSON><PERSON> trữ phân tầng trên bộ nhớ và đĩa
- <PERSON>h<PERSON><PERSON> kê chi tiết về hiệu suất cache

## 2. Kiến trúc mới

### 2.1. Các thành phần chính

- **CacheEntry**: Đại diện cho một mục trong cache, với thông tin về độ ưu tiên, số lần truy cập, và thời gian hết hạn.
- **AdaptiveSearchCache**: Lớp cache chính với các tính năng thông minh.
- **SearchCacheManager**: Quản lý nhiều cache cho các phương thức tìm kiếm khác nhau.
- **CachedWebSearchAgent**: Tích hợp cache với WebSearchAgent.

### 2.2. Luồng dữ liệu

1. Truy vấn tìm kiếm được gửi đến WebSearchAgent
2. CachedWebSearchAgent chặn truy vấn và kiểm tra trong cache
3. Nếu có trong cache, trả về kết quả từ cache
4. Nếu không có trong cache, gọi phương thức tìm kiếm gốc và lưu kết quả vào cache
5. Kết quả được trả về cho người dùng

## 3. Tính năng mới

### 3.1. Tự động điều chỉnh TTL

Cache tự động điều chỉnh thời gian sống (TTL) dựa trên tần suất truy cập:

- Các mục được truy cập thường xuyên có TTL dài hơn
- Các mục ít được truy cập có TTL ngắn hơn
- TTL được giới hạn trong khoảng min_ttl và max_ttl

### 3.2. Tìm kiếm ngữ nghĩa

Cache có khả năng tìm kiếm các truy vấn tương tự:

- Sử dụng TF-IDF để tính toán độ tương đồng giữa các truy vấn
- Áp dụng ngưỡng tương đồng để xác định các truy vấn tương tự
- Hỗ trợ nhiều ngôn ngữ với trọng số khác nhau

### 3.3. Lưu trữ phân tầng

Cache sử dụng lưu trữ phân tầng để cân bằng giữa tốc độ và dung lượng:

- Bộ nhớ cache: Lưu trữ các mục được truy cập gần đây
- Đĩa cache: Lưu trữ tất cả các mục
- Tự động dọn dẹp các mục hết hạn

### 3.4. Phân tích mẫu truy cập

Cache phân tích mẫu truy cập để tối ưu hóa:

- Theo dõi tần suất truy cập của các mục
- Theo dõi mẫu truy cập theo thời gian
- Theo dõi thống kê ngôn ngữ

### 3.5. Tiền tải thông minh

Cache có khả năng tiền tải dựa trên dự đoán:

- Dự đoán các truy vấn có khả năng được sử dụng
- Tiền tải các kết quả vào cache
- Tối ưu hóa việc sử dụng tài nguyên

## 4. Tích hợp với WebSearchAgent

### 4.1. CachedWebSearchAgent

CachedWebSearchAgent là một lớp bao bọc WebSearchAgent với khả năng cache:

- Tự động chặn các phương thức tìm kiếm
- Kiểm tra cache trước khi gọi phương thức gốc
- Lưu kết quả vào cache sau khi tìm kiếm
- Cung cấp thống kê và quản lý cache

### 4.2. Decorator cached_search

Decorator cached_search giúp dễ dàng tích hợp cache với các hàm tìm kiếm:

- Tự động kiểm tra cache trước khi gọi hàm
- Tự động lưu kết quả vào cache
- Hỗ trợ tìm kiếm ngữ nghĩa
- Hỗ trợ nhiều ngôn ngữ

## 5. Hiệu suất

### 5.1. Cải thiện tốc độ

Các thử nghiệm cho thấy cải thiện đáng kể về tốc độ:

- Tìm kiếm lần đầu: Tương đương với không có cache
- Tìm kiếm lần thứ hai (cache hit): Nhanh hơn 10-100 lần
- Tìm kiếm ngữ nghĩa (semantic hit): Nhanh hơn 5-50 lần

### 5.2. Sử dụng bộ nhớ

Cache sử dụng bộ nhớ hiệu quả:

- Giới hạn số lượng mục trong bộ nhớ
- Tự động xóa các mục ít được sử dụng
- Lưu trữ phân tầng để cân bằng giữa tốc độ và dung lượng

### 5.3. Độ chính xác

Cache duy trì độ chính xác cao:

- TTL để đảm bảo dữ liệu không quá cũ
- Tìm kiếm ngữ nghĩa với ngưỡng tương đồng có thể điều chỉnh
- Trọng số ngôn ngữ để cải thiện kết quả đa ngôn ngữ

## 6. Cách sử dụng

### 6.1. Sử dụng CachedWebSearchAgent

```python
from deep_research_core.agents.web_search_agent import WebSearchAgent
from deep_research_core.agents.web_search_agent_cache import patch_web_search_agent

# Tạo WebSearchAgent
agent = WebSearchAgent()

# Patch với cache
cached_agent = patch_web_search_agent(agent)

# Tìm kiếm (sẽ sử dụng cache)
result = agent.search("machine learning python")
```

### 6.2. Sử dụng decorator cached_search

```python
from deep_research_core.agents.search_cache_integration import cached_search

@cached_search(cache_name="searxng", ttl=86400)
def search_with_searxng(query, num_results=10, language="en"):
    # Thực hiện tìm kiếm với SearXNG
    # ...
    return results
```

### 6.3. Sử dụng AdaptiveSearchCache trực tiếp

```python
from deep_research_core.optimization.caching.adaptive_search_cache import AdaptiveSearchCache

# Tạo cache
cache = AdaptiveSearchCache(
    name="my_cache",
    default_ttl=3600,  # 1 giờ
    enable_semantic_search=True
)

# Lưu giá trị vào cache
cache.set(
    key="machine learning algorithms",
    value={"title": "ML Algorithms", "content": "..."},
    language="en"
)

# Lấy giá trị từ cache
result = cache.get(key="machine learning algorithms")
```

## 7. Ví dụ

Xem các ví dụ đầy đủ trong thư mục `examples/`:

- `adaptive_search_cache_example.py`: Ví dụ về AdaptiveSearchCache
- `cached_web_search_agent_example.py`: Ví dụ về CachedWebSearchAgent

## 8. Tài liệu API

Xem tài liệu API đầy đủ trong thư mục `docs/`:

- `adaptive-search-cache.md`: Tài liệu về AdaptiveSearchCache

## 9. Kết luận

Hệ thống cache mới cung cấp nhiều cải tiến đáng kể cho Deep Research Core:

- Cải thiện hiệu suất tìm kiếm
- Giảm tải cho các dịch vụ bên ngoài
- Cải thiện trải nghiệm người dùng
- Hỗ trợ tìm kiếm ngữ nghĩa và đa ngôn ngữ

Các cải tiến này giúp Deep Research Core trở thành một công cụ mạnh mẽ hơn cho việc tìm kiếm và nghiên cứu.
