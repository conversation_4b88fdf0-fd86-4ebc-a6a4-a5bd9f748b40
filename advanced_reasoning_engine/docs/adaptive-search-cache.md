# Adaptive Search Cache

Tài liệu này mô tả cách sử dụng và tích hợp `AdaptiveSearchCache` - một hệ thống cache thông minh cho các phương thức tìm kiếm trong Deep Research Core.

## 1. Tổng quan

`AdaptiveSearchCache` là một hệ thống cache thông minh được thiết kế đặc biệt cho các phương thức tìm kiếm web, với khả năng tự động điều chỉnh dựa trên mẫu truy cập và nội dung.

### 1.1. T<PERSON>h năng chính

- **Tự động điều chỉnh TTL**: Tự động điều chỉnh thời gian sống (TTL) dựa trên tần suất truy cập
- **Tìm kiếm ngữ nghĩa**: Tìm kiếm các truy vấn tương tự trong cache
- **Hỗ trợ đa ngôn ngữ**: Hỗ trợ nhiều ngôn ngữ với trọng số khác nhau
- **<PERSON>ân tích mẫu truy cập**: Phân tích và tối ưu hóa dựa trên mẫu truy cập
- **Tiền tải thông minh**: Tự động tiền tải cache dựa trên dự đoán
- **Lưu trữ phân tầng**: Lưu trữ trên bộ nhớ và đĩa với các chiến lược xóa thông minh
- **Thống kê chi tiết**: Cung cấp thống kê chi tiết về hiệu suất cache

### 1.2. Kiến trúc

`AdaptiveSearchCache` bao gồm các thành phần chính sau:

- **CacheEntry**: Đại diện cho một mục trong cache
- **AdaptiveSearchCache**: Lớp cache chính với các tính năng thông minh
- **SearchCacheManager**: Quản lý nhiều cache cho các phương thức tìm kiếm khác nhau
- **Decorator cached_search**: Decorator để dễ dàng tích hợp cache với các hàm tìm kiếm

## 2. Cài đặt và thiết lập

### 2.1. Yêu cầu

- Python 3.8+
- NumPy
- scikit-learn
- Các thư viện khác trong `requirements.txt`

### 2.2. Cài đặt

```bash
pip install -r requirements.txt
```

### 2.3. Thiết lập cơ bản

```python
from deep_research_core.optimization.caching.adaptive_search_cache import AdaptiveSearchCache

# Tạo cache
cache = AdaptiveSearchCache(
    name="my_cache",
    cache_dir="/path/to/cache",
    default_ttl=3600,  # 1 giờ
    enable_semantic_search=True
)
```

## 3. Sử dụng cơ bản

### 3.1. Lưu và lấy giá trị

```python
# Lưu giá trị vào cache
cache.set(
    key="machine learning algorithms",
    value={"title": "ML Algorithms", "content": "Random Forest, SVM, Neural Networks"},
    ttl=3600,  # 1 giờ
    language="en"
)

# Lấy giá trị từ cache
result = cache.get(key="machine learning algorithms")
```

### 3.2. Tìm kiếm ngữ nghĩa

```python
# Lấy giá trị với tìm kiếm ngữ nghĩa
result = cache.get(
    key="ML algorithms",
    use_semantic=True,
    language="en"
)
```

### 3.3. Xóa cache

```python
# Xóa tất cả các mục trong cache
cache.clear()
```

### 3.4. Lấy thống kê

```python
# Lấy thống kê cache
stats = cache.get_stats()
print(stats)
```

## 4. Sử dụng SearchCacheManager

`SearchCacheManager` giúp quản lý nhiều cache cho các phương thức tìm kiếm khác nhau.

### 4.1. Khởi tạo

```python
from deep_research_core.agents.search_cache_integration import SearchCacheManager

# Tạo cache manager
cache_manager = SearchCacheManager(
    cache_dir="/path/to/cache",
    default_ttl=3600,
    enable_semantic_search=True,
    language_weights={"en": 1.0, "vi": 1.2}
)
```

### 4.2. Lấy cache theo tên

```python
# Lấy cache cho SearXNG
searxng_cache = cache_manager.get_cache("searxng", ttl=86400)  # 24 giờ

# Lấy cache cho Crawlee
crawlee_cache = cache_manager.get_cache("crawlee", ttl=172800)  # 48 giờ
```

### 4.3. Lấy thống kê tổng hợp

```python
# Lấy thống kê tổng hợp
stats = cache_manager.get_stats()
print(stats)
```

### 4.4. Xóa cache

```python
# Xóa cache cụ thể
cache_manager.clear_cache("searxng")

# Xóa tất cả các cache
cache_manager.clear_all()
```

## 5. Sử dụng decorator cached_search

`cached_search` là một decorator giúp dễ dàng tích hợp cache với các hàm tìm kiếm.

### 5.1. Sử dụng cơ bản

```python
from deep_research_core.agents.search_cache_integration import cached_search

@cached_search(cache_name="searxng", ttl=86400)
def search_with_searxng(query, num_results=10, language="en"):
    # Thực hiện tìm kiếm với SearXNG
    # ...
    return results
```

### 5.2. Sử dụng với hàm tạo khóa tùy chỉnh

```python
from deep_research_core.agents.search_cache_integration import cached_search, searxng_cache_key

@cached_search(cache_name="searxng", ttl=86400, key_func=searxng_cache_key)
def search_with_searxng(query, num_results=10, language="en"):
    # Thực hiện tìm kiếm với SearXNG
    # ...
    return results
```

## 6. Tích hợp với các phương thức tìm kiếm

### 6.1. Tích hợp với SearXNG

```python
from deep_research_core.agents.search_cache_integration import cached_search, searxng_cache_key
from deep_research_core.agents.searxng_search import search_with_searxng

@cached_search(cache_name="searxng", ttl=86400, key_func=searxng_cache_key)
def cached_searxng_search(query, num_results=10, language="en"):
    return search_with_searxng(query=query, num_results=num_results, language=language)
```

### 6.2. Tích hợp với Crawlee

```python
from deep_research_core.agents.search_cache_integration import cached_search
from deep_research_core.agents.advanced_crawlee import search_with_advanced_crawlee

@cached_search(cache_name="crawlee", ttl=172800)
def cached_crawlee_search(query, num_results=10, language="en"):
    return search_with_advanced_crawlee(
        query=query,
        num_results=num_results,
        language=language,
        max_depth=2,
        detailed_scraping=True
    )
```

### 6.3. Tích hợp với WebSearchAgent

```python
from deep_research_core.agents.search_cache_integration import get_cache_manager
from deep_research_core.agents.web_search_agent import WebSearchAgent

# Tạo WebSearchAgent với cache
agent = WebSearchAgent(use_cache=True)

# Hoặc thiết lập cache manager tùy chỉnh
cache_manager = get_cache_manager()
agent.set_cache_manager(cache_manager)
```

## 7. Cấu hình nâng cao

### 7.1. Tùy chỉnh TTL

```python
cache = AdaptiveSearchCache(
    default_ttl=3600,  # 1 giờ
    min_ttl=300,       # 5 phút
    max_ttl=604800     # 1 tuần
)
```

### 7.2. Tùy chỉnh trọng số ngôn ngữ

```python
cache = AdaptiveSearchCache(
    language_weights={
        "en": 1.0,
        "vi": 1.2,
        "fr": 0.8,
        "de": 0.8,
        "es": 0.9,
        "zh": 0.7
    }
)
```

### 7.3. Tùy chỉnh ngưỡng tương đồng

```python
cache = AdaptiveSearchCache(
    similarity_threshold=0.8  # Ngưỡng tương đồng (0.0 - 1.0)
)
```

### 7.4. Tùy chỉnh kích thước cache

```python
cache = AdaptiveSearchCache(
    max_memory_items=1000,  # Số lượng mục tối đa trong bộ nhớ
    max_disk_items=10000    # Số lượng mục tối đa trên đĩa
)
```

## 8. Ví dụ

Xem các ví dụ đầy đủ trong thư mục `examples/`:

```bash
# Chạy ví dụ về AdaptiveSearchCache
python examples/adaptive_search_cache_example.py

# Chạy ví dụ cụ thể
python examples/adaptive_search_cache_example.py --example 1
```

## 9. Hiệu suất và tối ưu hóa

### 9.1. Hiệu suất

`AdaptiveSearchCache` được thiết kế để cân bằng giữa tốc độ và độ chính xác:

- **Tìm kiếm chính xác**: O(1) - Thời gian truy cập hằng số
- **Tìm kiếm ngữ nghĩa**: O(n) - Phụ thuộc vào số lượng truy vấn trong cache
- **Lưu trữ**: O(1) - Thời gian lưu trữ hằng số

### 9.2. Tối ưu hóa

- **Sử dụng cache phân tầng**: Lưu trữ trên bộ nhớ và đĩa
- **Tự động điều chỉnh TTL**: Tăng TTL cho các mục được truy cập thường xuyên
- **Tự động dọn dẹp**: Dọn dẹp các mục hết hạn định kỳ
- **Tự động tiền tải**: Tiền tải cache dựa trên dự đoán

## 10. Xử lý lỗi và debug

### 10.1. Ghi log

`AdaptiveSearchCache` sử dụng module `logging` để ghi log:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 10.2. Xử lý lỗi phổ biến

- **Lỗi đọc/ghi file**: Kiểm tra quyền truy cập thư mục cache
- **Lỗi bộ nhớ**: Giảm `max_memory_items` hoặc tăng tần suất dọn dẹp
- **Lỗi tìm kiếm ngữ nghĩa**: Kiểm tra `similarity_threshold` và `language_weights`
