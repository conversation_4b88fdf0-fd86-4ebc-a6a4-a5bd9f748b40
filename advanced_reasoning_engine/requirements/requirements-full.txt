# Core dependencies
requests>=2.28.0
beautifulsoup4>=4.11.0
lxml>=4.9.0
numpy>=1.23.0
pandas>=1.4.0
tqdm>=4.64.0
python-dotenv>=0.20.0
pyyaml>=6.0
jsonschema>=4.6.0
urllib3>=1.26.9
chardet>=5.0.0
certifi>=2022.5.18
idna>=3.3
pytz>=2022.1
python-dateutil>=2.8.2
six>=1.16.0
langdetect>=1.0.9
scipy>=1.7.0
scikit-learn>=1.0.0
psutil>=5.9.0

# Web scraping and crawling
playwright>=1.22.0
selenium>=4.2.0
webdriver-manager>=3.7.0
html2text>=2020.1.16
# readability-lxml có thể được import dưới tên 'readability'
readability-lxml>=0.8.1
trafilatura>=1.2.2
newspaper3k>=0.2.8
feedparser>=6.0.10
aiohttp>=3.8.1
httpx>=0.23.0
tldextract>=3.1.0

# NLP and text processing
nltk>=3.7
spacy>=3.3.0
transformers>=4.19.0
# sentence-transformers import dưới tên 'sentence_transformers'
sentence-transformers>=2.2.0
fasttext>=0.9.2
polyglot>=16.7.4
textblob>=0.17.1
gensim>=4.2.0
vaderSentiment>=3.3.2
# huggingface-hub import dưới tên 'huggingface_hub'
huggingface-hub>=0.0.12
torch>=1.10.0

# Vietnamese NLP
underthesea>=1.3.5
pyvi>=0.1.1
vncorenlp>=1.0.3

# File processing
PyPDF2>=2.0.0
python-docx>=0.8.11
python-pptx>=0.6.21
ebooklib>=0.17.1
markdown>=3.3.7
pypandoc>=1.7.5
pymupdf>=1.19.6  # fitz
pytesseract>=0.3.9
Pillow>=9.1.1  # PIL

# CAPTCHA handling
# captcha-solver>=0.1.5  # Thư viện này không tồn tại trên PyPI
python-anticaptcha>=1.0.0  # Thư viện chính thức thay thế cho anticaptcha, import dưới tên 'python_anticaptcha'
twocaptcha>=0.0.1  # Thư viện chính thức thay thế cho 2captcha-python

# Caching and storage
diskcache>=5.4.0
pymongo>=4.1.1
redis>=4.3.4
sqlalchemy>=1.4.37
aiofiles>=0.8.0
aiosqlite>=0.17.0
aioredis>=2.0.1

# Async and concurrency
asyncio>=3.4.3
uvloop>=0.16.0; sys_platform != 'win32'
nest_asyncio>=1.5.5
concurrent-log-handler>=0.9.20

# API and web frameworks
fastapi>=0.78.0
uvicorn>=0.17.6
jinja2>=3.1.2
markupsafe>=2.1.1
# python-multipart import dưới tên 'python_multipart'
python-multipart>=0.0.5
# email-validator import dưới tên 'email_validator'
email-validator>=1.2.1
pydantic>=1.9.1

# Monitoring and telemetry
# Cài đặt opentelemetry-api và opentelemetry-sdk trước
opentelemetry-api>=1.15.0,<1.34.0
opentelemetry-sdk>=1.15.0,<1.34.0
prometheus-client>=0.16.0
opentelemetry-exporter-otlp>=1.15.0,<1.34.0
# Các gói instrumentation chỉ có phiên bản beta
opentelemetry-instrumentation-fastapi>=0.38b0
opentelemetry-instrumentation-requests>=0.38b0
# Cài đặt opentelemetry-util-http nếu cần
opentelemetry-util-http>=0.38b0
opentelemetry-instrumentation>=0.38b0
opentelemetry-semantic-conventions>=0.38b0

# Vector stores
faiss-cpu>=1.7.0
# faiss-gpu>=1.7.0  # Uncomment if GPU is available
pymilvus>=2.2.0
pinecone-client>=2.2.0
weaviate-client>=3.15.0
chromadb>=0.4.0

# Testing
pytest>=7.1.2
pytest-asyncio>=0.18.3
pytest-cov>=3.0.0
pytest-mock>=3.7.0
responses>=0.21.0
pytest-benchmark>=3.4.1
pytest-timeout>=2.1.0

# Documentation
sphinx>=5.0.1
sphinx-rtd-theme>=1.0.0
sphinx-autodoc-typehints>=1.18.3
nbsphinx>=0.8.9
jupyter>=1.0.0
ipython>=8.4.0

# Development tools
black>=23.0.0
isort>=5.12.0
mypy>=1.0.0
pylint>=2.17.0
flake8>=4.0.1
autopep8>=1.6.0
yapf>=0.32.0
pre-commit>=2.19.0

# Utilities
loguru>=0.6.0
typer>=0.4.1
rich>=12.4.4
click>=8.1.3
tabulate>=0.8.10
colorama>=0.4.5
progress>=1.6
alive-progress>=2.4.1
