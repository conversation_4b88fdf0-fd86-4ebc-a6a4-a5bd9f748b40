# 🏗️ KẾ HOẠCH TỔ CHỨC LẠI DỰ ÁN

## 🚨 TÌNH TRẠNG HIỆN TẠI
- **2 cấu trúc duplicate**: `src/deep_research_core/` (MAIN) và `deepresearch/` (LEGACY)
- **50+ file documentation** rải rác ở root level
- **100+ test files** không có tổ chức, duplicate functionality
- **Cache/temp files** nằm khắp nơi
- **Duplicate code** và chức năng overlap

## 🔍 **PHÂN TÍCH CODEBASE:**

### **`src/deep_research_core/` (KEEP - MAIN CODEBASE)**
- ✅ **Cấu trúc chính thức** với modular design
- ✅ **Component-based architecture**
- ✅ **WebSearchAgentLocalMerged** (4122 lines) - Production ready
- ✅ **AdaptiveCrawlerConsolidatedMerged** - Full featured
- ✅ **Credibility evaluation** modules
- ✅ **Vietnamese search integration**

### **`deepresearch/` (ARCHIVE - LEGACY CODEBASE)**
- ⚠️ **Experimental features** cần extract
- ⚠️ **Duplicate agents** với tên tương tự
- ⚠️ **Test files** rải rác
- ⚠️ **Outdated structure**

## 🎯 CẤU TRÚC MỚI ĐỀ XUẤT

```
deep_research_core_1/
├── 📁 src/                          # CORE SOURCE CODE
│   └── deep_research_core/
│       ├── agents/                  # Web search agents
│       ├── utils/                   # Utilities & helpers
│       ├── api/                     # API endpoints
│       ├── credibility/             # Credibility evaluation
│       ├── integrations/            # External integrations
│       └── __init__.py
│
├── 📁 tests/                        # ALL TESTS CONSOLIDATED
│   ├── unit/                        # Unit tests
│   ├── integration/                 # Integration tests
│   ├── performance/                 # Performance tests
│   └── fixtures/                    # Test data
│
├── 📁 docs/                         # ALL DOCUMENTATION
│   ├── api/                         # API documentation
│   ├── guides/                      # User guides
│   ├── architecture/                # Architecture docs
│   └── examples/                    # Code examples
│
├── 📁 config/                       # CONFIGURATION FILES
│   ├── development.yml
│   ├── production.yml
│   └── docker/
│
├── 📁 scripts/                      # UTILITY SCRIPTS
│   ├── setup/
│   ├── deployment/
│   └── maintenance/
│
├── 📁 data/                         # DATA FILES
│   ├── cache/                       # Cache files
│   ├── models/                      # ML models
│   └── samples/                     # Sample data
│
├── 📁 output/                       # OUTPUT FILES
│   ├── test_results/
│   ├── logs/
│   └── downloads/
│
├── 📄 README.md                     # MAIN README
├── 📄 requirements.txt              # Dependencies
├── 📄 pyproject.toml               # Project config
└── 📄 .gitignore                   # Git ignore
```

## 🔄 MIGRATION PLAN

### PHASE 1: BACKUP & ANALYSIS (30 phút)
1. **Create backup directory**: `mkdir -p backup_$(date +%Y%m%d)`
2. **Analyze duplicate files**: Compare `src/` vs `deepresearch/`
3. **Identify critical files**: List production-ready modules
4. **Document current imports**: Map all import dependencies

### PHASE 2: CORE CONSOLIDATION (2 giờ)
1. **Extract useful features from `deepresearch/`**:
   - Experimental search methods
   - Advanced test cases
   - Configuration files
2. **Merge into `src/deep_research_core/`**:
   - Keep existing structure
   - Add missing functionality
   - Resolve conflicts
3. **Update import paths**: Fix all relative imports
4. **Test core functionality**: Ensure no breaking changes

### PHASE 3: DOCUMENTATION CLEANUP (1 giờ)
1. **Consolidate README files**:
   - Main README.md (project overview)
   - docs/README.md (detailed documentation)
   - Archive 50+ duplicate READMEs
2. **Organize by category**:
   - docs/api/ (API documentation)
   - docs/guides/ (User guides)
   - docs/architecture/ (Technical docs)
3. **Update links**: Fix all documentation references

### PHASE 4: TEST REORGANIZATION (1.5 giờ)
1. **Create test structure**:
   ```
   tests/
   ├── unit/           # Unit tests
   ├── integration/    # Integration tests
   ├── performance/    # Performance tests
   └── fixtures/       # Test data
   ```
2. **Move and categorize tests**:
   - Group by functionality
   - Remove duplicates
   - Update import paths
3. **Create test runners**: Unified test execution

### PHASE 5: CLEANUP & OPTIMIZATION (1 giờ)
1. **Remove unnecessary files**:
   - Cache directories
   - Temp files
   - Duplicate configurations
2. **Update configuration**:
   - .gitignore
   - requirements.txt
   - pyproject.toml
3. **Final testing**: Run full test suite

## 📊 EXPECTED BENEFITS
- **Giảm 70% số files** ở root level
- **Loại bỏ duplicate code**
- **Cải thiện maintainability**
- **Dễ dàng navigate**
- **Chuẩn hóa project structure**

## ⚠️ RISKS & MITIGATION
- **Risk**: Breaking imports
- **Mitigation**: Systematic import path updates

- **Risk**: Lost functionality
- **Mitigation**: Comprehensive testing after migration

## 🚀 NEXT STEPS
1. **Confirm restructure plan**
2. **Start with Phase 1 backup**
3. **Execute phases incrementally**
4. **Test after each phase**
