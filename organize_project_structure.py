#!/usr/bin/env python3
"""
<PERSON>ript to organize project structure by moving files to appropriate locations
"""

import os
import shutil
import json
from pathlib import Path

def organize_project_structure():
    """Organize files into proper directory structure"""
    
    base_dir = Path("/home/<USER>/Desktop/automation-tool/deep_research_core_1")
    
    # Define target directories
    directories = {
        'docs': base_dir / 'docs',
        'scripts': base_dir / 'scripts',
        'reports': base_dir / 'reports',
        'tools': base_dir / 'tools',
        'archive': base_dir / 'archive',
        'temp': base_dir / 'temp'
    }
    
    # Create directories if they don't exist
    for dir_path in directories.values():
        dir_path.mkdir(exist_ok=True)
    
    # Files to move to docs/
    docs_files = [
        'API_REFERENCE_GUIDE.md',
        'DEVELOPMENT_WORKFLOW_GUIDE.md', 
        'PROJECT_RESTRUCTURE_PLAN.md',
        'PROJECT_STRUCTURE_GUIDE.md',
        'QUICK_START.md'
    ]
    
    # Files to move to reports/
    report_files = [
        'FINAL_PROJECT_CHECK_REPORT.json',
        'FIX_AND_OPTIMIZE_REPORT.json',
        'FUNCTIONALITY_TEST_REPORT.json',
        'POST_RESTRUCTURE_VALIDATION_REPORT.json',
        'RESTRUCTURE_FINAL_SUMMARY.json',
        'SIMPLE_VALIDATION_REPORT.json',
        'cleanup_report.json',
        'project_analysis_report.json'
    ]
    
    # Files to move to scripts/
    script_files = [
        'cleanup_project_structure.py',
        'final_cleanup.py',
        'final_project_check.py',
        'fix_and_optimize.py',
        'post_restructure_validation.py',
        'simple_validation.py',
        'test_main_functionality.py'
    ]
    
    # Directories to move to archive/
    archive_dirs = [
        'config_consolidated',
        'docs_consolidated', 
        'scripts_consolidated',
        'tests_consolidated',
        'backup',
        'temp_test_dir',
        'test_results'
    ]
    
    # Directories to move to temp/
    temp_dirs = [
        'test_downloads',
        'thuvienphapluat_docs',
        'results'
    ]
    
    moved_files = []
    errors = []
    
    try:
        # Move docs files
        print("Moving documentation files...")
        for file in docs_files:
            src = base_dir / file
            if src.exists():
                dst = directories['docs'] / file
                shutil.move(str(src), str(dst))
                moved_files.append(f"{file} -> docs/")
                print(f"Moved {file} to docs/")
        
        # Move report files  
        print("Moving report files...")
        for file in report_files:
            src = base_dir / file
            if src.exists():
                dst = directories['reports'] / file
                shutil.move(str(src), str(dst))
                moved_files.append(f"{file} -> reports/")
                print(f"Moved {file} to reports/")
        
        # Move script files
        print("Moving script files...")
        for file in script_files:
            src = base_dir / file
            if src.exists():
                dst = directories['scripts'] / file
                shutil.move(str(src), str(dst))
                moved_files.append(f"{file} -> scripts/")
                print(f"Moved {file} to scripts/")
        
        # Move archive directories
        print("Moving directories to archive...")
        for dir_name in archive_dirs:
            src = base_dir / dir_name
            if src.exists():
                dst = directories['archive'] / dir_name
                if dst.exists():
                    shutil.rmtree(dst)
                shutil.move(str(src), str(dst))
                moved_files.append(f"{dir_name}/ -> archive/")
                print(f"Moved {dir_name}/ to archive/")
        
        # Move temp directories
        print("Moving directories to temp...")
        for dir_name in temp_dirs:
            src = base_dir / dir_name
            if src.exists():
                dst = directories['temp'] / dir_name
                if dst.exists():
                    shutil.rmtree(dst)
                shutil.move(str(src), str(dst))
                moved_files.append(f"{dir_name}/ -> temp/")
                print(f"Moved {dir_name}/ to temp/")
        
        # Move HTML files to tools
        html_files = ['docs_viewer.html']
        for file in html_files:
            src = base_dir / file
            if src.exists():
                dst = directories['tools'] / file
                shutil.move(str(src), str(dst))
                moved_files.append(f"{file} -> tools/")
                print(f"Moved {file} to tools/")
        
    except Exception as e:
        errors.append(str(e))
        print(f"Error: {e}")
    
    return moved_files, errors

def create_main_documentation():
    """Create a single main documentation file"""
    
    base_dir = Path("/home/<USER>/Desktop/automation-tool/deep_research_core_1")
    
    main_doc_content = """# 📚 DEEP RESEARCH CORE - COMPREHENSIVE DOCUMENTATION

## 🎯 PROJECT OVERVIEW

Deep Research Core là một hệ thống tìm kiếm và crawl website thông minh, được thiết kế đặc biệt cho tiếng Việt với khả năng đánh giá độ tin cậy thông tin.

## 🚀 QUICK START

### Installation
```bash
pip install -r requirements.txt
```

### Basic Usage
```python
from src.deep_research_core.agents.web_search_agent_local_merged import WebSearchAgentLocalMerged

# Khởi tạo agent
agent = WebSearchAgentLocalMerged()

# Tìm kiếm cơ bản
results = agent.search("python programming")

# Tìm kiếm nâng cao
results = agent.deep_research("machine learning algorithms")
```

## 📁 PROJECT STRUCTURE

```
deep_research_core_1/
├── src/deep_research_core/          # Core source code
├── docs/                           # Documentation files
├── scripts/                        # Utility scripts
├── reports/                        # Analysis reports
├── tools/                          # Development tools
├── archive/                        # Archived/legacy files
├── temp/                          # Temporary files
├── config/                        # Configuration files
├── data/                          # Data files
├── examples/                      # Example scripts
├── tests/                         # Test files
└── README.md                      # This file
```

## 🔧 CORE COMPONENTS

### 1. Web Search Agent
- **File**: `src/deep_research_core/agents/web_search_agent_local_merged.py`
- **Purpose**: Main search interface with SearXNG integration
- **Features**: Vietnamese search, credibility evaluation, deep research

### 2. Adaptive Crawler
- **File**: `src/deep_research_core/agents/adaptive_crawler_consolidated_merged.py`
- **Purpose**: Advanced website crawling with JavaScript support
- **Features**: Site structure analysis, file downloads, SPA support

### 3. Credibility Evaluator
- **File**: `src/deep_research_core/utils/credibility_evaluator.py`
- **Purpose**: Evaluate information credibility
- **Features**: Source analysis, content assessment, bias detection

### 4. Vietnamese Utils
- **File**: `src/deep_research_core/utils/vietnamese_utils.py`
- **Purpose**: Vietnamese language processing
- **Features**: Text normalization, diacritic handling, search optimization

## 📋 API REFERENCE

### Search Methods
```python
# Basic search
results = agent.search("query")

# Advanced search with filters
results = agent.search("query", language="vi", safe_search=True)

# Deep research with crawling
results = agent.deep_research("complex question")

# Credibility-aware search
results = agent.search_with_credibility("news topic")
```

### Crawler Methods
```python
# Website crawling
results = crawler.crawl_website("https://example.com")

# Download files during crawl
results = crawler.crawl_with_downloads("https://site.com")

# SPA crawling
results = crawler.crawl_spa("https://spa-site.com")
```

## 🔍 ADVANCED FEATURES

### Vietnamese Language Support
- Automatic diacritic normalization
- Vietnamese-specific search optimization
- Language detection and processing

### Credibility Assessment
- Source reputation analysis
- Content quality evaluation
- Bias and misinformation detection

### Smart Crawling
- JavaScript-rendered content
- Adaptive depth control
- File type detection and download

## 🛠️ DEVELOPMENT

### Running Tests
```bash
python -m pytest tests/
```

### Code Quality
```bash
# Linting
flake8 src/

# Type checking
mypy src/
```

### Documentation
- **API Reference**: `docs/API_REFERENCE_GUIDE.md`
- **Development Guide**: `docs/DEVELOPMENT_WORKFLOW_GUIDE.md`
- **Project Structure**: `docs/PROJECT_STRUCTURE_GUIDE.md`

## 📊 CONFIGURATION

### Search Configuration
```json
{
    "searxng_url": "http://localhost:8080",
    "max_results": 20,
    "timeout": 30,
    "language": "vi",
    "safe_search": true
}
```

### Crawler Configuration
```json
{
    "max_depth": 3,
    "max_pages": 100,
    "enable_javascript": true,
    "download_files": true,
    "respect_robots_txt": true
}
```

## 🤝 CONTRIBUTING

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 LICENSE

This project is licensed under the MIT License.

## 🙏 ACKNOWLEDGMENTS

- SearXNG for search capabilities
- BeautifulSoup for HTML parsing
- Selenium for JavaScript rendering
- Various Vietnamese NLP libraries

---

For detailed API documentation, see `docs/API_REFERENCE_GUIDE.md`
For development workflow, see `docs/DEVELOPMENT_WORKFLOW_GUIDE.md`
"""

    # Write main documentation
    with open(base_dir / "README.md", "w", encoding="utf-8") as f:
        f.write(main_doc_content)
    
    print("Created comprehensive README.md")
    return True

def main():
    """Main function"""
    print("🧹 Organizing Project Structure...")
    print("=" * 50)
    
    moved_files, errors = organize_project_structure()
    
    print("\n📝 Creating Main Documentation...")
    create_main_documentation()
    
    print("\n✅ ORGANIZATION COMPLETE")
    print("=" * 50)
    print(f"Files moved: {len(moved_files)}")
    print(f"Errors: {len(errors)}")
    
    if moved_files:
        print("\n📁 Moved Files:")
        for file in moved_files:
            print(f"  ✓ {file}")
    
    if errors:
        print("\n❌ Errors:")
        for error in errors:
            print(f"  ✗ {error}")
    
    print("\n🎯 Final Project Structure:")
    print("""
    deep_research_core_1/
    ├── README.md                    # Main documentation
    ├── src/                        # Core source code
    ├── docs/                       # All documentation
    ├── scripts/                    # Utility scripts  
    ├── reports/                    # Analysis reports
    ├── tools/                      # Development tools
    ├── archive/                    # Archived files
    ├── temp/                       # Temporary files
    ├── config/                     # Configuration
    ├── data/                       # Data files
    ├── examples/                   # Examples
    └── tests/                      # Test files
    """)

if __name__ == "__main__":
    main()
