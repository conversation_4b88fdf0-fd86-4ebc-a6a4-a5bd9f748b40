#!/usr/bin/env python3
"""
Script to clean up archive directory by removing redundant and obsolete files
"""

import os
import shutil
import json
from pathlib import Path
from datetime import datetime

def analyze_archive_content():
    """Analyze what's in the archive directory"""
    
    archive_dir = Path("/home/<USER>/Desktop/automation-tool/deep_research_core_1/archive")
    
    analysis = {
        "total_size": 0,
        "directories": {},
        "files_to_keep": [],
        "files_to_remove": [],
        "directories_to_remove": []
    }
    
    # Analyze each subdirectory
    for item in archive_dir.iterdir():
        if item.is_dir():
            dir_size = sum(f.stat().st_size for f in item.rglob('*') if f.is_file())
            file_count = len(list(item.rglob('*')))
            
            analysis["directories"][item.name] = {
                "size_mb": round(dir_size / (1024*1024), 2),
                "file_count": file_count,
                "recommendation": ""
            }
    
    # Recommendations based on analysis
    recommendations = {
        "deepresearch_legacy": "REMOVE - This is a complete legacy copy with duplicate code",
        "backup": "KEEP - Contains backup of current agents",
        "config_consolidated": "MERGE - Move useful configs to main config/",
        "docs_consolidated": "REMOVE - Already moved to docs/",
        "scripts_consolidated": "REMOVE - Already moved to scripts/",
        "tests_consolidated": "REMOVE - Already moved to tests/",
        "temp_test_dir": "REMOVE - Temporary test files",
        "test_results": "SELECTIVE - Keep recent results, remove old ones"
    }
    
    for dir_name, rec in recommendations.items():
        if dir_name in analysis["directories"]:
            analysis["directories"][dir_name]["recommendation"] = rec
    
    return analysis

def clean_archive():
    """Clean up the archive directory"""
    
    archive_dir = Path("/home/<USER>/Desktop/automation-tool/deep_research_core_1/archive")
    
    cleaned_items = []
    kept_items = []
    errors = []
    
    try:
        print("🧹 Starting archive cleanup...")
        
        # 1. Remove deepresearch_legacy (complete duplicate)
        legacy_dir = archive_dir / "deepresearch_legacy"
        if legacy_dir.exists():
            print(f"Removing legacy directory: {legacy_dir}")
            shutil.rmtree(legacy_dir)
            cleaned_items.append("deepresearch_legacy/ - Complete legacy code duplicate")
        
        # 2. Remove consolidated directories (already moved)
        consolidated_dirs = ["docs_consolidated", "scripts_consolidated", "tests_consolidated"]
        for dir_name in consolidated_dirs:
            dir_path = archive_dir / dir_name
            if dir_path.exists():
                print(f"Removing consolidated directory: {dir_path}")
                shutil.rmtree(dir_path)
                cleaned_items.append(f"{dir_name}/ - Already moved to main directories")
        
        # 3. Remove temp_test_dir
        temp_dir = archive_dir / "temp_test_dir"
        if temp_dir.exists():
            print(f"Removing temp directory: {temp_dir}")
            shutil.rmtree(temp_dir)
            cleaned_items.append("temp_test_dir/ - Temporary test files")
        
        # 4. Clean test_results - keep only recent and important ones
        test_results_dir = archive_dir / "test_results"
        if test_results_dir.exists():
            print("Cleaning test_results directory...")
            
            # Keep recent test results (last 30 days) and final tests
            current_time = datetime.now()
            files_to_keep = []
            files_to_remove = []
            
            for file in test_results_dir.rglob("*.json"):
                file_name = file.name.lower()
                
                # Keep final/important test results
                if any(keyword in file_name for keyword in [
                    "final", "comprehensive", "production", "functionality"
                ]):
                    files_to_keep.append(file.name)
                    continue
                
                # Remove old dated test results
                try:
                    file_stat = file.stat()
                    file_age_days = (current_time.timestamp() - file_stat.st_mtime) / (24 * 3600)
                    
                    if file_age_days > 30:  # Older than 30 days
                        file.unlink()
                        files_to_remove.append(file.name)
                    else:
                        files_to_keep.append(file.name)
                        
                except Exception as e:
                    errors.append(f"Error processing {file}: {e}")
            
            # Remove downloads subdirectory if exists
            downloads_dir = test_results_dir / "downloads"
            if downloads_dir.exists():
                shutil.rmtree(downloads_dir)
                cleaned_items.append("test_results/downloads/ - Old download files")
            
            cleaned_items.append(f"test_results/ - Removed {len(files_to_remove)} old test files")
        
        # 5. Handle config_consolidated - move useful configs
        config_consolidated = archive_dir / "config_consolidated"
        main_config = Path("/home/<USER>/Desktop/automation-tool/deep_research_core_1/config")
        
        if config_consolidated.exists():
            print("Processing config_consolidated...")
            
            # Check if main requirements.txt exists
            main_requirements = Path("/home/<USER>/Desktop/automation-tool/deep_research_core_1/requirements.txt")
            
            if not main_requirements.exists():
                # Copy main requirements.txt to root
                config_req = config_consolidated / "requirements.txt"
                if config_req.exists():
                    shutil.copy2(config_req, main_requirements)
                    kept_items.append("requirements.txt - Moved to project root")
            
            # Remove config_consolidated after copying
            shutil.rmtree(config_consolidated)
            cleaned_items.append("config_consolidated/ - Configs processed and moved")
        
        # 6. Keep backup directory but clean it if needed
        backup_dir = archive_dir / "backup"
        if backup_dir.exists():
            kept_items.append("backup/ - Kept as code backup")
        
        # 7. Keep archive info file
        archive_info = archive_dir / "deepresearch_archive_info.json"
        if archive_info.exists():
            kept_items.append("deepresearch_archive_info.json - Archive information")
        
        print("✅ Archive cleanup completed!")
        
    except Exception as e:
        errors.append(f"General error: {e}")
    
    return cleaned_items, kept_items, errors

def create_cleanup_report(cleaned_items, kept_items, errors):
    """Create a cleanup report"""
    
    report = {
        "cleanup_date": datetime.now().isoformat(),
        "cleaned_items": cleaned_items,
        "kept_items": kept_items,
        "errors": errors,
        "summary": {
            "items_cleaned": len(cleaned_items),
            "items_kept": len(kept_items),
            "errors_count": len(errors)
        }
    }
    
    report_file = Path("/home/<USER>/Desktop/automation-tool/deep_research_core_1/reports/archive_cleanup_report.json")
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    return report_file

def main():
    """Main cleanup function"""
    
    print("🔍 ARCHIVE CLEANUP STARTING")
    print("=" * 50)
    
    # Analyze before cleanup
    print("Analyzing archive content...")
    analysis = analyze_archive_content()
    
    print("\n📊 Archive Analysis:")
    for dir_name, info in analysis["directories"].items():
        print(f"  📁 {dir_name}: {info['file_count']} files, {info['size_mb']} MB")
        print(f"     Recommendation: {info['recommendation']}")
    
    # Perform cleanup
    print("\n🧹 Performing cleanup...")
    cleaned_items, kept_items, errors = clean_archive()
    
    # Create report
    report_file = create_cleanup_report(cleaned_items, kept_items, errors)
    
    print("\n✅ CLEANUP COMPLETED")
    print("=" * 50)
    print(f"Items cleaned: {len(cleaned_items)}")
    print(f"Items kept: {len(kept_items)}")
    print(f"Errors: {len(errors)}")
    
    if cleaned_items:
        print("\n🗑️ Cleaned Items:")
        for item in cleaned_items:
            print(f"  ✓ {item}")
    
    if kept_items:
        print("\n📦 Kept Items:")
        for item in kept_items:
            print(f"  ✓ {item}")
    
    if errors:
        print("\n❌ Errors:")
        for error in errors:
            print(f"  ✗ {error}")
    
    print(f"\n📋 Detailed report saved to: {report_file}")
    
    # Show final archive structure
    archive_dir = Path("/home/<USER>/Desktop/automation-tool/deep_research_core_1/archive")
    print(f"\n📁 Final archive structure:")
    
    if archive_dir.exists():
        for item in sorted(archive_dir.iterdir()):
            if item.is_dir():
                file_count = len(list(item.rglob('*')))
                print(f"  📁 {item.name}/ ({file_count} files)")
            else:
                print(f"  📄 {item.name}")
    else:
        print("  (Archive directory is empty)")

if __name__ == "__main__":
    main()
