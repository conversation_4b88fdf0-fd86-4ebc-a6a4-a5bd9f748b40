# <PERSON><PERSON> hoạch tổ chức lại thư mục src/

## Vấn đề hiện tại
Th<PERSON> mục `src/` có quá nhiều files không đúng vị trí:
- 80+ test files trong src/ (nên ở tests/)
- 20+ temp files trong src/ (nên ở temp/)
- 15+ run/script files trong src/ (nên ở scripts/)
- 10+ setup/check files trong src/ (nên ở scripts/setup/)
- Duplicate files giữa src/ và src/agents/, src/models/, src/utils/
- Output data trong src/ (nên ở data/)

## Phân loại files cần di chuyển

### 1. TEST FILES (80+ files) → tests/
```
test_*.py files:
- test_100_requests.py
- test_academic_search.py
- test_adaptive_crawler_integration.py
- test_advanced_crawlee.py
- test_all_integrations.py
- test_all_modules.py
- test_book_search_direct.py
- test_cache_mechanism.py
- test_captcha_solver.py
- test_check_ddg_params.py
- test_check_ddgs.py
- test_crawl_methods.py
- test_crawl_standalone.py
- test_crawlee.py
- test_crawlee_playwright_output.py
- test_crawlee_playwright_search.py
- test_crossref_openalex_fallback.py
- test_decide_method.py
- test_decide_method_advanced.py
- test_deep_crawl.py
- test_deep_crawl_basic.py
- test_deep_crawl_final.py
- test_deep_crawl_improved.py
- test_deep_crawl_improved_simple.py
- test_deep_crawl_minimal.py
- test_deep_crawler_enhanced.py
- test_deep_crawler_with_file_download.py
- test_direct_crawlee.py
- test_enhanced_agent.py
- test_error_recovery.py
- test_extended_document_extractor.py
- test_file_download.py
- test_file_download_direct.py
- test_file_extraction.py
- test_file_processor.py
- test_imports.py
- test_improved_web_search_agent.py
- test_improved_web_search_agent_fix.py
- test_local_agent.py
- test_merged_agent.py
- test_model_loader.py
- test_model_loader_simple.py
- test_module.py
- test_modules.py
- test_modules_simple.py
- test_multilingual_search.py
- test_openalex_crossref.py
- test_openalex_crossref_fallback.py
- test_optimizer.py
- test_outcome_based.py
- test_performance_nlp.py
- test_prm_reward_model.py
- test_query_analyzer.py
- test_query_analyzer_direct.py
- test_query_decomposer.py
- test_query_optimizer.py
- test_query_optimizer_integration.py
- test_query_optimizer_standalone.py
- test_reasoning_basic.py
- test_reasoning_components.py
- test_reasoning_comprehensive.py
- test_reasoning_edge_cases.py
- test_reasoning_modules.py
- test_reasoning_only.py
- test_rl_modules.py
- test_search.py
- test_search_flow.py
- test_search_local_limit.py
- test_search_methods.py
- test_search_optimizer.py
- test_search_simple.py
- test_searxng_agent.py
- test_searxng_api.py
- test_searxng_connection.py
- test_searxng_crawlee.py
- test_security_enhancements.py
- test_simple.py
- test_simple_deep_crawler.py
- test_simple_module.py
- test_source_attribution.py
- test_specialized_search.py
- test_specific_query.py
- test_standalone.py
- test_standalone_improved.py
- test_tinyzero_framework.py
- test_torch_cuda.py
- test_torch_transformers.py
- test_tot_basic.py
- test_tot_empty_query.py
- test_tot_simple.py
- test_validator.py
- test_verl_framework.py
- test_web_imports.py
- test_web_search.py
- test_web_search_agent_local.py
- test_web_search_agent_local_advanced.py
- test_web_search_agent_local_advanced_detailed.py
- test_web_search_agent_local_comprehensive.py
- test_web_search_agent_local_custom.py
- test_web_search_agent_local_error_handling.py
- test_web_search_agent_local_improvements.py
- test_web_search_agent_local_large_scale.py
- test_web_search_agent_local_performance.py
- test_web_search_agent_mock.py
- test_websearch_evaluation.py
- test_websearch_fixed.py
- comprehensive_test_web_search_agent_local.py
- hello_test.py
- simple_test.py
- simple_test_fixed.py
- simple_test_improved.py
- simple_query_optimizer_test.py
- simple_search_test.py
```

### 2. TEMP FILES (20+ files) → temp/
```
temp_*.py files:
- temp_agent_reward_model.py
- temp_local_model_optimizer.py
- temp_openr1_integration.py
- temp_outcome_based.py
- temp_tinyzero_integration.py
- temp_trlx_integration.py
- temp_verl_integration.py
```

### 3. SCRIPT FILES (15+ files) → scripts/
```
run_*.py files:
- run_all_tests.py
- run_all_web_search_tests.py
- run_api_server.py
- run_evaluation.py
- run_rl_tuning_tests.py
- run_test_web_search.py
- run_test_with_output.py
- run_tests.py

setup/check files:
- setup.py
- check_directory.py
- check_libraries.py
- check_torch.py
- check_torch_installation.py
- create_directory.py
- install_libraries.py
- fix_api_providers.py
- fix_transformers.py
- improve_modules.py
- move_tests.py
- update_tasks.py
- suppress_warnings.py

simple files:
- simple_web_search.py
- hello.py
- main.py
```

### 4. DATA FILES → data/
```
output/ directory
```

### 5. DUPLICATE FILES (cần xóa)
```
Files có duplicate trong src/ và src/agents/:
- adaptive_crawler.py (keep in agents/)
- adaptive_crawler_integration.py (keep in agents/)
- web_search_agent_enhanced.py (keep in agents/)
- web_search_agent_local.py (keep in agents/)
- web_search_agent_local_fix.py (keep in agents/)

Files có duplicate trong src/ và src/utils/:
- answer_quality_evaluator.py (keep in utils/)
- captcha_handler.py (keep in utils/)
- captcha_solver_basic.py (keep in utils/)
- query_decomposer.py (keep in utils/)
- question_complexity_evaluator.py (keep in utils/)

Files có duplicate trong src/ và src/models/:
- anthropic_reward_model.py (keep in models/)
- openr1_integration.py (keep in models/)
- vietnamese_search_integration.py (keep in models/)
- tinyzero_integration.py (keep in models/)
- trlx_integration.py (keep in models/)
```

### 6. CORE SOURCE FILES (giữ lại trong src/)
```
Core functionality files:
- academic_search.py
- deep_crawl_improvements.py
- deep_research_integration.py
- exceptions.py
- tinyzero_example.py
- verl_example.py
- verl_framework.py

Core directories:
- deep_research_core/ (complete module structure)
- agents/ (web search agents)
- models/ (model integrations)
- utils/ (utility functions)
- rag/ (RAG implementations)
- reasoning/ (reasoning modules)
- config/ (configuration)
```

## Kế hoạch thực hiện
1. Di chuyển test files → tests/
2. Di chuyển temp files → temp/
3. Di chuyển script files → scripts/
4. Di chuyển data files → data/
5. Xóa duplicate files
6. Kiểm tra cấu trúc cuối cùng
